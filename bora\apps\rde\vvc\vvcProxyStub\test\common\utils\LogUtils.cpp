/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * LogUtils.cpp --
 *
 *      Utilities to interface with the logging system
 */

#include "stdafx.h"
#include "LogUtils.h"

namespace LogUtils {
HMODULE g_hModule = NULL;
std::string g_logFileName = "";
bool g_logFirst = true;
int g_logLevel = 0;
#if defined(WIN32) || defined(WIN64)
DWORD g_prevTID = 0;
#else
/*
 * In Win32, GetCurrentThreadId() returns a DWORD HANDLE which
 * is an opaque datatype.
 * In Linux, pthread_t is also an opaque data type which can be
 * safely typecasted to a DWORD (unsigned int)
 * However, in MAC OS X, pthread_t is not directly an opaque datatype.
 * It is a pointer to _opaque_pthread_t struct
 * And hence assigning return value of pthread_self() to a DWORD
 * causes typecast warning.
 * Therefore, for non-Win32 systems, use pthread_t itself.
 */
pthread_t g_prevTID = 0;
#endif
bool g_prevNewLine = true;
bool g_isServer = true;

#if defined(WIN32) || defined(WIN64)

std::string g_moduleNameLong = "";
std::string g_moduleNameShort = "";
std::string g_programNameLong = "";
std::string g_programNameShort = "";

#endif

}; // namespace LogUtils


#if !defined(WIN32) && !defined(WIN64)

#   define VMW_RDPVC_BRIDGE_LOG_ENV_NAME "VMW_RDPVC_BRIDGE_LOG_ENABLED"

#   define MB(x) ((x) * 1024 * 1024)
#   define LOG_FILE_MAX_SIZE MB(10)


/*
 *----------------------------------------------------------------------
 *
 * LogUtilsGetLogDir --
 *
 *    Returns the directory in which the LogFile will be stored.
 *    This functions mimics the way Linux View Client finds/creates
 *    logging directory.
 *
 * Results:
 *    Returns true upon successful retrieval of logging directory and
 *    copy of it to ppData parameter.
 *    ppData returns the string containing logging directory.
 *    pnBytes returns the number of bytes of ppData.
 *
 * Side effects:
 *    None
 *
 *----------------------------------------------------------------------
 */

bool
LogUtilsGetLogDir(LPSTR *ppData,  // OUT
                  DWORD *pnBytes) // IN/OUT
{
   if (ppData == NULL || pnBytes == NULL) {
      fprintf(stderr, "%s Failed - Null parameters", LOG_FUNC_NAME);
      return false;
   }

   const char *user;
   const char *tmpDir;

   char *logDir = (char *)::malloc((int)(*pnBytes));
   if (!logDir) {
      fprintf(stderr, "Error in allocating Memory!\n");
      return false;
   }

   /*
    * The pointer returned by getenv() is within the environment list
    * of the calling process.
    * The caller should not modify or store it. So we make a copy of it
    * and use it.
    * Reference: getenv manpage.
    */

   user = getenv("USER");
   if (!user) {
      fprintf(stderr, "'USER' Environment variable not set -"
                      "Creating default log file directory.");
      user = "default";
   }

#   ifndef __APPLE__

   /*
    * For Linux - rdpvcbridge logs will be present at /tmp/omnissa-$USER/
    */

   if (!(tmpDir = getenv("TMPDIR"))) {
      if (!(tmpDir = getenv("TMP"))) {
         if (!(tmpDir = getenv("TEMP"))) {
            tmpDir = "/tmp";
         }
      }
   }

   size_t logDirLen = strlen(tmpDir) + 8 + strlen(user) + 1;
   snprintf(logDir, logDirLen, "%s/omnissa-%s", tmpDir, user);

#   elif __APPLE__

   /*
    * For MAC - rdpvcbridge logs will be present at
    * $HOME/Library/Logs/PRODUCT_VDM_CLIENT_NAME/
    * If $HOME is undefined, place the logs at directory $TMPDIR
    */

   const char *vmLogPath = HZN_LOG_PATH_MAC;

   if (!(tmpDir = getenv("HOME"))) {
      vmLogPath = "";
      if (!(tmpDir = getenv("TMPDIR"))) {
         tmpDir = "/tmp";
      }
   }

   size_t logDirLen = strlen(tmpDir) + strlen(vmLogPath) + strlen(PRODUCT_VDM_CLIENT_NAME) + 1;
   snprintf(logDir, logDirLen, "%s%s%s", tmpDir, vmLogPath, PRODUCT_VDM_CLIENT_NAME);

#   endif

   size_t nBytes = (strlen(logDir) + 1) * sizeof *logDir;

   if (nBytes > ((size_t)*pnBytes)) {
      fprintf(stderr, "Overflow in copying username!");
      return false;
   }

   ::memcpy(*ppData, logDir, nBytes);
   *pnBytes = (DWORD)nBytes;

   free(logDir);
   return true;
}


/*
 *----------------------------------------------------------------------
 *
 * LogUtilsGetLocalTimeBridge --
 *
 *    Fills the given tm struct with local time info. Returns the
 *    milliseconds past the current second as well.
 *
 * Results:
 *    Milliseconds.
 *
 * Side effects:
 *    None
 *
 *----------------------------------------------------------------------
 */

int
LogUtilsGetLocalTimeBridge(struct tm *local) // OUT
{
   time_t seconds;
   struct tm *tmp;
   struct timeval tv;

   time(&seconds);
   tmp = localtime(&seconds);

   *local = *tmp;

   gettimeofday(&tv, NULL);
   return tv.tv_usec / 1000;
}


/*
 *----------------------------------------------------------------------
 *
 * LogUtilsShouldAppend --
 *
 *   Log should not be increased beyond a maximum size. It is 10MB here.
 *   So this method returns whether the log message should be appended
 *   to existing logfile or not.
 *
 * Results:
 *    Returns true or false for if append should be done or not.
 *
 * Side effects:
 *    LogUtils::g_logFileName will be changed when this function gets
 *    called and LogUtils::g_logFirst is true.
 *
 *----------------------------------------------------------------------
 */

bool
LogUtilsShouldAppend()
{
   bool append = true;

   if (LogUtils::g_logFirst) {

      DWORD nBytes = 1024;
      char *logDir = (char *)::malloc((int)nBytes);

      if (!LogUtilsGetLogDir(&logDir, &nBytes)) {
         fprintf(stderr, "LogUtilsGetLogDir Failed!");
         if (logDir) {
            free(logDir);
            logDir = NULL;
         }
         return false;
      }

      /*
       * Check if the directory /tmp/omnissa-$USER exists or not.
       */
      if (!FileUtils::IsFolder(logDir)) {
         /*
          * Create the directory.
          * Let everyone be able to read and write to it.
          * Save the process usmask before calling mkdir & restore it.
          */

         mode_t procMask = umask(0);
         if (mkdir(logDir, S_IRWXU | S_IRWXG | S_IRWXO)) {
            fprintf(stderr, "Error in creating Log Directory - %s \n", strerror(errno));
         }
         umask(procMask);
      }

      LogUtils::g_logFileName = std::string(logDir) + "/" + LogUtils::g_logFileName;

      /*
       * Append to the logfile if the size has not reached the limit.
       */
      __int64 fileSz = LOG_FILE_MAX_SIZE + 1;
      FileUtils::FileSize(LogUtils::g_logFileName, &fileSz);
      append = fileSz <= LOG_FILE_MAX_SIZE;
      free(logDir);
   }

   return append;
}

#elif defined(WIN32) || defined(WIN64)


/*
 *----------------------------------------------------------------------
 *
 * LogUtilsGetLocalTimeBridge --
 *
 *    Fills the given tm struct with local time info. Returns the
 *    milliseconds past the current second as well.
 *
 * Results:
 *    Milliseconds.
 *
 * Side effects:
 *    None
 *
 *----------------------------------------------------------------------
 */

int
LogUtilsGetLocalTimeBridge(struct tm *local) // OUT
{
   struct __timeb64 tb64;
   _ftime64_s(&tb64);
   _localtime64_s(local, &tb64.time);
   return tb64.millitm;
}


/*
 *----------------------------------------------------------------------
 *
 * LogUtilsShouldAppend --
 *
 *    I can't just wipe out the log file because each module loaded, DLL
 *    application, etc., would wipe the log the first time they logged
 *    a message.  So keep the log around, just don't let it get too big.
 *
 * Results:
 *    Returns true or false for if append should be done or not.
 *
 * Side effects:
 *   LogUtils::g_logFileName will be changed when this function gets
 *   called and LogUtils::g_logFirst is true.
 *
 *----------------------------------------------------------------------
 */

bool
LogUtilsShouldAppend()
{
   bool append = true;
   if (LogUtils::g_logFirst) {

      char path[MAX_PATH];
      if (::GetModuleFileNameA(LogUtils::g_hModule, path, ARRAYSIZE(path)) == 0) {
         return false;
      }
      LogUtils::g_moduleNameLong = path;
      LogUtils::g_moduleNameShort = FileUtils::FileName(LogUtils::g_moduleNameLong);


      if (::GetModuleFileNameA(NULL, path, ARRAYSIZE(path)) == 0) {
         return false;
      }
      LogUtils::g_programNameLong = path;
      LogUtils::g_programNameShort = FileUtils::FileName(LogUtils::g_programNameLong);


      std::string logFolder;
      int csidl = LogUtils::g_isServer ? CSIDL_COMMON_APPDATA : CSIDL_LOCAL_APPDATA;
      if (::SHGetSpecialFolderPath(NULL, path, csidl, FALSE)) {
         logFolder = FileUtils::Join(path, HORIZON_LOG_DIR_PATH_A "\\logs");

      } else {
         logFolder = FileUtils::FolderName(LogUtils::g_moduleNameLong);
      }

      if (LogUtils::g_logFileName.empty()) {

         if (LogUtils::g_isServer) {
            /*
             * I've noticed that on Win7 and Vista that some programs
             * can't write to the same log file as another program so
             * I will keep the log files separated.  I could have used
             * the PID instead of the program name, but then there
             * would be a large number of log files accumulating because
             * nothing cleans up after them.  This way I have a limited
             * number of log files that get truncated if they get too big
             * and as a bonus, it's easy to find the log file that I'm
             * interested in.
             */
            _snprintf_s(path, ARRAYSIZE(path), _TRUNCATE, LOG_SERVER_FILENAME "_%s",
                        FileUtils::BaseName(LogUtils::g_programNameShort).c_str());

            LogUtils::g_logFileName = path;
            // LogUtils::g_logFileName = LOG_SERVER_FILENAME;
         } else {
            LogUtils::g_logFileName = LOG_CLIENT_FILENAME;
         }
      }

      LogUtils::g_logFileName = FileUtils::Join(logFolder, LogUtils::g_logFileName);

      const char *regKey = VIEW_REG_KEY "\\Log\\MaxDebugLogSizeMB";
      std::string regValue = StringUtils::RegistryValue(regKey);
      __int64 fileSzMax = ::_atoi64(regValue.c_str());

#   define MB(x) ((x) * 1024 * 1024)
      fileSzMax = fileSzMax < 1 ? MB(10) : fileSzMax > 100 ? MB(100) : MB(fileSzMax);

      __int64 fileSz = fileSzMax + 1;
      FileUtils::FileSize(LogUtils::g_logFileName, &fileSz);
      append = fileSz <= fileSzMax;
   }

   return append;
}

#endif


/*
 *----------------------------------------------------------------------
 *
 * Class AutoCS()
 *
 *    Use a critical section to prevent threads from overlapping
 *    their I/O.  The critical section is entered when the object
 *    is created and left when the object is destructed.
 *
 *    Linux does not have a notion of Critical Section.
 *    AutoCS class for linux uses VMMutex to ensure serializability.
 *
 *----------------------------------------------------------------------
 */

#if defined(WIN32) || defined(WIN64)

class AutoCS {
public:
   AutoCS()
   {
      if (!s_initCS) {
         InitializeCriticalSection(&s_cs);
         s_initCS = true;
      }

      EnterCriticalSection(&s_cs);
   }

   ~AutoCS() { LeaveCriticalSection(&s_cs); }

private:
   static CRITICAL_SECTION s_cs;
   static bool s_initCS;
};

CRITICAL_SECTION AutoCS::s_cs;
bool AutoCS::s_initCS = false;

#else

class AutoCS {
public:
   AutoCS() { sLock.Acquire(); }

   ~AutoCS() { sLock.Release(); }

private:
   static VMMutex sLock;
};

VMMutex AutoCS::sLock(false);

static VMMutex vLogMessageLock(false);
#endif


#if defined(WIN32) || defined(WIN64)

/*
 *----------------------------------------------------------------------
 *
 * LogInit --
 *
 *    Logs a message to a debug file using printf() style variable
 *    argument list.
 *
 * Results:
 *    None.
 *
 * Side effects:
 *    None.
 *
 *----------------------------------------------------------------------
 */

void
LogUtils::LogInit(bool isServer,        // IN
                  const char *filename, // IN
                  HMODULE hModule)      // IN
{
   AutoCS lock;
   g_isServer = isServer;
   g_logFirst = true;
   g_prevNewLine = true;
   g_hModule = hModule;
   g_prevTID = ::GetCurrentThreadId();
   // g_logFileName = filename;

   if (isServer) {
      g_logFileName = LOG_SERVER_FILENAME;
   } else {
      g_logFileName = LOG_CLIENT_FILENAME;
   }

   g_logLevel = 1;
}

#else

/*
 *----------------------------------------------------------------------
 *
 * LogInit --
 *
 *    Checks value of Environment Variable VMW_RDPVC_BRIDGE_LOG_ENABLED.
 *    If VMW_RDPVC_BRIDGE_LOG_ENABLED is 0 - means logging will be disabled
 *    otherwise enabled.
 *    In Win32, the hModule function parameter points to dll that enables
 *    the logging. For non-Win32 platforms, the hModule parameter is ignored.
 *
 * Results:
 *    The g_logLevel will reflect the value of
 *    VMW_RDPVC_BRIDGE_LOG_ENABLED
 *
 * Side effects:
 *    None.
 *
 *----------------------------------------------------------------------
 */

void
LogUtils::LogInit(bool isServer,        // IN
                  const char *filename, // IN/OPT
                  HMODULE hModule)      // IN/UNUSED
{
   AutoCS lock;
   g_isServer = isServer;
   g_logFirst = true;
   g_prevTID = GetCurrentThreadId();
   g_hModule = NULL;

   if (!filename) {
      if (g_isServer) {
         g_logFileName = LOG_SERVER_FILENAME;
      } else {
         g_logFileName = LOG_CLIENT_FILENAME;
      }
   } else {
      g_logFileName = filename;
   }

   fprintf(stderr, "Using LogFile %s\n", (char *)g_logFileName.c_str());

   g_logLevel = 1;
}
#endif


/*
 *----------------------------------------------------------------------
 *
 * LogEnabled --
 *
 *    Checks if Logging is enabled or not.
 *
 * Results:
 *    None.
 *
 * Side effects:
 *    None.
 *
 *----------------------------------------------------------------------
 */

bool
LogUtils::LogEnabled()
{
   return g_logLevel > 0;
}


/*
 *----------------------------------------------------------------------
 *
 * LogLastWindowsError --
 *
 *    Logs an error message using GetLastError().
 *
 *    // XXX : For non-Win32, the function does not actually log LastError()
 *             Calls the vLogError directly.
 *
 * Results:
 *    None.
 *
 * Side effects:
 *    None.
 *
 *----------------------------------------------------------------------
 */

void
LogUtils::LogLastWindowsError(const char *funcName, // IN
                              const char *fmt,      // IN
                              ...)                  // IN
{
   va_list args;
   va_start(args, fmt);
#if defined(WIN32) || defined(WIN64)
   vLogWindowsError(funcName, ::GetLastError(), fmt, args);
#else
   vLogWindowsError(funcName, 0, fmt, args);
#endif
   va_end(args);
}


/*
 *----------------------------------------------------------------------
 *
 * Method LogWindowsError
 *
 *    Logs an error message given a Windows error code.
 *
 *    // XXX : For non-Win32, WinErr is not valid. Directly log via vLogError
 *
 * Results:
 *    None.
 *
 * Side effects:
 *    None.
 *
 *----------------------------------------------------------------------
 */

void
LogUtils::LogWindowsError(const char *funcName, // IN
                          LONG winErr,          // IN
                          const char *fmt,      // IN
                          ...)                  // IN
{
   va_list args;
   va_start(args, fmt);
   vLogWindowsError(funcName, winErr, fmt, args);
   va_end(args);
}

#if defined(WIN32) || defined(WIN64)
void
LogUtils::vLogWindowsError(const char *funcName, // IN
                           LONG winErr,          // IN
                           const char *fmt,      // IN
                           va_list args)         // IN
{
   LPVOID lpMsgBuf;

   ::FormatMessage(FORMAT_MESSAGE_ALLOCATE_BUFFER | FORMAT_MESSAGE_FROM_SYSTEM, NULL, winErr,
                   MAKELANGID(LANG_NEUTRAL, SUBLANG_DEFAULT), (LPTSTR)&lpMsgBuf, 0, NULL);

   vLogError(funcName, false, fmt, args);
   LogErrorNoNewLine(funcName, "; (%d)%s", winErr, lpMsgBuf);
   ::LocalFree(lpMsgBuf);

   /*
    * There's a newLine at the end of the string
    * return from FormatMessage()
    */
   g_prevNewLine = TRUE;
}
#else
/*
 * // XXX : For non-Win32, the behavior is just to call vLogError().
 * There is no direct equivalent of Win32's GetLastError() and
 * errno might have been overwritten by the time actual logging is done.
 */
void
LogUtils::vLogWindowsError(const char *funcName, // IN
                           LONG winErr,          // IN/UNUSED
                           const char *fmt,      // IN
                           va_list args)         // IN
{
   vLogError(funcName, true, fmt, args);
}
#endif


/*
 *----------------------------------------------------------------------
 *
 * LogError --
 *
 *    Logs an error message using GetLastError().
 *
 * Results:
 *    None.
 *
 * Side effects:
 *    Prints a newline after the message.
 *
 *----------------------------------------------------------------------
 */

void
LogUtils::LogError(const char *funcName, // IN
                   const char *fmt,      // IN
                   ...)                  // IN
{
   va_list args;
   va_start(args, fmt);
   vLogError(funcName, true, fmt, args);
   va_end(args);
}

void
LogUtils::vLogError(const char *funcName, // IN
                    bool putNewLine,      // IN
                    const char *fmt,      // IN
                    va_list args)         // IN
{
   AutoCS lock;

#if _DEBUG
   if (g_prevNewLine) {
      ::fprintf(stderr, "%s", funcName);
   }

   ::vfprintf(stderr, fmt, args);

   if (putNewLine) {
      putc('\n', stderr);
   }
#endif

   vLogDebug(funcName, putNewLine, fmt, args);
}


/*
 *----------------------------------------------------------------------
 *
 * LogErrorNoNewLine --
 *
 *    Logs an message.
 *
 * Results:
 *    None.
 *
 * Side effects:
 *    Doesn't print a newline after the message.
 *
 *----------------------------------------------------------------------
 */

void
LogUtils::LogErrorNoNewLine(const char *funcName, // IN
                            const char *fmt,      // IN
                            ...)                  // IN
{
   va_list args;
   va_start(args, fmt);
   vLogError(funcName, false, fmt, args);
   va_end(args);
}


/*
 *----------------------------------------------------------------------
 *
 * LogMessage --
 *
 *    Logs an message.
 *
 * Results:
 *    None.
 *
 * Side effects:
 *    Prints a newline after the message.
 *
 *----------------------------------------------------------------------
 */

void
LogUtils::LogMessage(const char *funcName, // IN
                     const char *fmt,      // IN
                     ...)                  // IN
{
   va_list args;
   va_start(args, fmt);
   vLogMessage(funcName, true, fmt, args);
   va_end(args);
}

void
LogUtils::vLogMessage(const char *funcName, // IN
                      bool putNewLine,      // IN
                      const char *fmt,      // IN
                      va_list args)         // IN
{
#if defined(WIN32) || defined(WIN64)
   AutoCS lock;
#else
   vLogMessageLock.Acquire();
#endif

   if (g_prevNewLine) {
      ::fprintf(stdout, "%s", funcName);
   }

   ::vfprintf(stdout, fmt, args);

   if (putNewLine) {
      putc('\n', stdout);
   }

   vLogDebug(funcName, putNewLine, fmt, args);

#if !defined(WIN32) && !defined(WIN64)
   vLogMessageLock.Release();
#endif
}


/*
 *----------------------------------------------------------------------
 *
 * LogMessageNoNewLine --
 *
 *    Logs a message.
 *
 * Results:
 *    None.
 *
 * Side effects:
 *    Doesn't print a newline after the message.
 *
 *----------------------------------------------------------------------
 */

void
LogUtils::LogMessageNoNewLine(const char *funcName, // IN
                              const char *fmt,      // IN
                              ...)                  // IN
{
   va_list args;
   va_start(args, fmt);
   vLogMessage(funcName, false, fmt, args);
   va_end(args);
}


/*
 *----------------------------------------------------------------------
 *
 * LogDebug --
 *
 *    Logs a message to a debug file using printf() style variable
 *    argument list.
 *
 * Results:
 *    None.
 *
 * Side effects:
 *    None.
 *
 *----------------------------------------------------------------------
 */

void
LogUtils::LogDebug(const char *funcName, // IN
                   const char *fmt,      // IN
                   ...)                  // IN
{
   va_list args;
   va_start(args, fmt);
   vLogDebug(funcName, true, fmt, args);
   va_end(args);
}


/*
 *----------------------------------------------------------------------
 *
 * LogDebugNoNewLine --
 *
 *    Logs a message to a debug file without writing the newline char
 *    using printf() style variable argument list.
 *
 * Results:
 *    None.
 *
 * Side effects:
 *    None.
 *
 *----------------------------------------------------------------------
 */

void
LogUtils::LogDebugNoNewLine(const char *funcName, // IN
                            const char *fmt,      // IN
                            ...)                  // IN
{
   va_list args;
   va_start(args, fmt);
   vLogDebug(funcName, false, fmt, args);
   va_end(args);
}


/*
 *----------------------------------------------------------------------
 *
 * vLogDebug --
 *
 *    Logs a message to a debug file using va_list arguments.
 *
 * Results:
 *    None.
 *
 * Side effects:
 *    None.
 *
 *----------------------------------------------------------------------
 */

void
LogUtils::vLogDebug(const char *funcName, // IN
                    bool putNewLine,      // IN
                    const char *fmt,      // IN
                    va_list args)         // IN
{
#ifdef LOCAL_LOG_ENABLED
   if (LogEnabled()) {

      AutoCS lock;

      bool append = LogUtilsShouldAppend();

      int milliseconds = 0;
      struct tm tm64;

      milliseconds = LogUtilsGetLocalTimeBridge(&tm64);

      /*
       * If the file doesn't exist then create it with all access
       * so that it can be updated by other users.  If you leave it
       * to fopen_s() to create the file it will be created with
       * less than all access privledges and other users won't be
       * able to append to it.
       */
      if (!append || !FileUtils::Exists(g_logFileName)) {
         FileUtils::CreateFileWithAllAccess(g_logFileName);
      }

      /*
       * Try to open the file
       */
      FILE *fp = NULL;
#   if defined(WIN32) || defined(WIN64)

      if (::fopen_s(&fp, g_logFileName.c_str(), "a") != 0) {
         return;
      }

#   else

      if (!(fp = fopen(g_logFileName.c_str(), "a"))) {
         fprintf(stderr, "Error opening Logfile\n");
         return;
      }

#   endif

      /*
       * If it's the first time through, then put in a separator
       */
      if (g_logFirst && append) {
         const char *sep = "-----------------------------------------------------------------------"
                           "----------------------------------";
         ::fprintf(fp, "\n%s\n%s\n%s\n\n", sep, sep, sep);
      }


      /*
       * Prefix the message with some useful information.  I use
       * a high-resolution timer to track time so that I can log
       * milliseconds as well.  This helps track where time is
       * taken while searching for performance bottlenecks.
       */

#   if defined(WIN32) || defined(WIN64)
      DWORD tid = GetCurrentThreadId();
#   else
      pthread_t tid = GetCurrentThreadId();
#   endif

      DWORD pid = GetCurrentProcessId();

#   if defined(WIN32) || defined(WIN64)

#      define LOG_PREFIX                                                                           \
         ::fprintf(fp, "%04d/%02d/%02d %2d:%02d:%02d.%03d <%4d> [%4d] %20s  %s",                   \
                   tm64.tm_year + 1900, tm64.tm_mon + 1, tm64.tm_mday, tm64.tm_hour, tm64.tm_min,  \
                   tm64.tm_sec, milliseconds, tid, pid, g_programNameShort.c_str(), funcName)
#   elif __linux__

      /*
       * For Linux, just displaying output of pthread_self() is not sufficient.
       * Debugging tools like gdb use the notion of LWP to identify threads
       * of a process. So display LWP as well in LogLine.
       */

#      define LOG_PREFIX                                                                           \
         ::fprintf(fp, "%04d/%02d/%02d %2d:%02d:%02d.%03d <%4d> <%ld> [%4d] %s: ",                 \
                   tm64.tm_year + 1900, tm64.tm_mon + 1, tm64.tm_mday, tm64.tm_hour, tm64.tm_min,  \
                   tm64.tm_sec, milliseconds, (int)tid, syscall(SYS_gettid), pid, funcName)

#   elif __APPLE__

      /*
       * MAC does not define SYS_gettid via syscall() function.
       * The gettid manpage of MAC OS X says that unless the process is in a thread
       * group, gettid() returns the same value as getpid().
       */

#      define LOG_PREFIX                                                                           \
         ::fprintf(fp, "%04d/%02d/%02d %2d:%02d:%02d.%03d <%p> [%4d] %s: ", tm64.tm_year + 1900,   \
                   tm64.tm_mon + 1, tm64.tm_mday, tm64.tm_hour, tm64.tm_min, tm64.tm_sec,          \
                   milliseconds, tid, pid, funcName)

#   endif

      /*
       * Log a started/loaded message the first time through
       */
      if (g_logFirst) {
         LOG_PREFIX;
#   if defined(WIN32) || defined(WIN64)
         ::fprintf(fp, "%s %s\n", g_moduleNameLong.c_str(),
                   g_hModule == NULL ? "started" : "loaded");
#   else
         ::fprintf(fp, "Started\n");
#   endif
         g_prevTID = tid;
      }

      /*
       * Write the requested log message
       */
      if (g_prevNewLine) {

         /*
          * Put a separator in the logs when there's a thread switch
          */

#   if defined(WIN32) || defined(WIN64)
         if (g_prevTID != tid) {
            g_prevTID = tid;
            ::fprintf(fp, "-------------------------------------\n");
         }
#   else
         if (pthread_equal(g_prevTID, tid)) {
            g_prevTID = tid;
            ::fprintf(fp, "-------------------------------------\n");
         }
#   endif
         LOG_PREFIX;
      }
      ::vfprintf(fp, fmt, args);

      if (putNewLine) {
         ::putc('\n', fp);
      }

      ::fclose(fp);
   }
#endif

   g_prevNewLine = putNewLine;
   g_logFirst = false;
}


/*
 *----------------------------------------------------------------------
 *
 * LogData --
 *
 *    Logs binary data using a standard format.
 *
 * Results:
 *    None.
 *
 * Side effects:
 *    None.
 *
 *----------------------------------------------------------------------
 */

void
LogUtils::LogData(const char *funcName, // IN
                  const char *prefix,   // IN
                  void *pData,          // IN
                  int dataLen,          // IN
                  int colMax)           // IN
{
#ifdef LOCAL_LOG_ENABLED

   if (!LogUtils::LogEnabled()) {
      return;
   }

   static const int COL_MAX = 64;
   if (colMax > COL_MAX) {
      colMax = COL_MAX;
   }

   int rowMax = (dataLen + colMax - 1) / colMax;

   unsigned char *logData = (unsigned char *)pData;

   for (int row = 0; row < rowMax; ++row) {

      int dataRemaining = dataLen - (row * colMax);
      int rowLen = min(dataRemaining, colMax);

      char buffer[COL_MAX * 16];
      char *buf = buffer;
      int bufLen = ARRAYSIZE(buffer);
      int col, n;

      for (col = 0; col < rowLen; ++col) {
         n = _snprintf_s(buf, bufLen, _TRUNCATE, "%02x ", logData[col]);
         buf += n;
         bufLen -= n;
      }

      if (col < colMax) {
         n = (colMax - col) * 3;
         n = _snprintf_s(buf, bufLen, _TRUNCATE, "%*s", n, "");
         buf += n;
         bufLen -= n;
      }

      for (col = 0; col < rowLen; ++col) {

         unsigned char c = *logData++;

         if (c < 32 || c > 127) {
            c = '.';
         }

         n = _snprintf_s(buf, bufLen, _TRUNCATE, " %c", c);
         buf += n;
         bufLen -= n;
      }

      if (col < colMax) {
         n = (colMax - col) * 2;
         n = _snprintf_s(buf, bufLen, _TRUNCATE, "%*s", n, "");
         buf += n;
         bufLen -= n;
      }

      LogUtils::LogDebug(funcName, "%s: %4d:  %s", prefix, row * colMax, buffer);
   }
#endif
}


/*
 *----------------------------------------------------------------------
 *
 * Class BridgeTrace --
 *
 *    Logs an "Enter" message when the object is created and an "Exit"
 *    message when the object is deleted.  This makes it easy to track
 *    the enter/exit of a function or scope.
 *
 *----------------------------------------------------------------------
 */

#ifdef LOCAL_LOG_ENABLED
BridgeTrace::BridgeTrace(const char *funcName) // IN
{
   m_funcName = funcName;
   LogUtils::LogDebug(m_funcName, "Enter");
}

BridgeTrace::BridgeTrace(const char *funcName,   // IN
                         const std::string &msg) // IN
{
   m_funcName = funcName;
   LogUtils::LogDebug(funcName, "Enter - %s", msg.c_str());
}

BridgeTrace::~BridgeTrace()
{
   if (m_exitMsg.size() == 0) {
      LogUtils::LogDebug(m_funcName, "Exit");
   } else {
      LogUtils::LogDebug(m_funcName, "Exit  - %s", m_exitMsg.c_str());
   }
}
#endif
