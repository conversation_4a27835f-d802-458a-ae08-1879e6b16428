/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * vncRegEncTest.cpp --
 *
 *    A GTest fixture class for testing vncEncodeRegion.
 *
 */

#include "vncRegEncTest.h"

// Instantiate static member object
std::vector<ImageInfo> VncRegEncTest::images;


/*
 *----------------------------------------------------------------------------
 *
 * VncRegEnc::CreateDeviceWrapper --
 *
 *      Call into CreateDevice implementation.
 *
 *----------------------------------------------------------------------------
 */

Bool
VncRegEnc::CreateDeviceWrapper(uint16_t vendorId,      // IN/UNUSED
                               void *pAdapterLuid,     // IN/UNUSED
                               void **ppDevice,        // OUT
                               void **ppDeviceContext, // OUT/UNUSED
                               void *pCriticalSection) // OUT/UNUSED
{
   auto pDevice = CreateDevice();
   *ppDevice = pDevice;
   return TRUE;
}


/*
 *----------------------------------------------------------------------------
 *
 * VncRegEnc::DestroyDeviceWrapper --
 *
 *      Call into DestroyDevice implementation.
 *
 *----------------------------------------------------------------------------
 */

void
VncRegEnc::DestroyDeviceWrapper(void **ppDevice,        // IN/OUT
                                void **ppDeviceContext, // IN/UNUSED
                                void *pCriticalSection) // IN/UNUSED
{
   if (*ppDevice) {
      auto pDevice = reinterpret_cast<OSDevicePtr>(*ppDevice);
      DestroyDevice(pDevice);
      *ppDevice = nullptr;
   }
}


/*
 * The EXPECT_() macros wrongly flag a C6326 warning("potential comparison of
 * a constant with another constant") with MSVC cl /analyze.
 */
#if defined _MSC_VER
#   pragma warning(disable : 6326)
#endif

using DeviceUniquePtr = std::unique_ptr<OSDevicePtr>;
using TextureUniquePtr = std::unique_ptr<OSTexturePtr>;


/*
 *----------------------------------------------------------------------------
 *
 * VncRegEnc::WaitFrameEncodeComplete --
 * VncRegEncTest::TestFrameEncode --
 *
 *      Common functions to test and complete encode.
 *
 *----------------------------------------------------------------------------
 */


void
VncRegEncTest::WaitFrameEncodeComplete(VncRegEnc &regEnc,          // IN
                                       VNCRegEncFrameState &state) // IN
{
   VNCError error;
   uint32 encodeCount = 0;
   uint32 encodeCountCap = MAX_ENCODE_BUFFER_RETRY;

   /*
    * We keep encoding until the encoder reaches idle stage to ensure
    * encode work (boosting) is fully completed.
    */
   while (true) {
      error = regEnc.Encode(state);
      EXPECT_TRUE(error == VNC_SUCCESS || error == VNC_ERROR_RETRY_ENCODE);

      regEnc.ResetBuffer();
      EXPECT_EQ(regEnc.BufferedRectCount(), 0);

      encodeCount++;
      double boostTime;
      if (regEnc.GetEncodeState(&boostTime) == REGENC_IDLE || encodeCount > encodeCountCap) {
         regEnc.ResetBuffer();
         EXPECT_EQ(regEnc.BufferedRectCount(), 0);
         EXPECT_LE(encodeCount, encodeCountCap);
         break;
      }

      if (boostTime > 0.0) {
         double sleepTime = (boostTime - VNCUtil_SystemTime()) * 1000000;
         if (sleepTime > 0.0) {
            Util_Usleep((long)sleepTime);
         }
         encodeCountCap = MAX_ENCODE_BOOST_TIME_RETRY;
      }
   }
}

void
VncRegEncTest::TestFrameEncode(VncRegEnc &regEnc,          // IN/OUT
                               VNCError statusOnEmptyDiff, // IN
                               PixelProviderType pp)       // IN
{
   VNCRegEncFrameState state = {};
   state.byteBudget = 1000000 / 30;
   state.bandwidth = 2048.0;

   auto image = GetImage(0);
   VncBitmaskPtr dirtyMask(image.width, image.height, 16);
   state.dirtyMask = *dirtyMask;

   VncFbPtr fbPtr(image);
   std::unique_ptr<OSDevicePtr> device;
   std::unique_ptr<OSTexturePtr> texture;

   if (pp == PixelProviderType::SW) {
      state.fbPtr = *fbPtr;
      state.fbStride = fbPtr.Pitch();
   } else {
      device = std::make_unique<OSDevicePtr>(VncRegEnc::CreateDevice());
      texture = std::make_unique<OSTexturePtr>(VncRegEnc::CreateTexture(*device, fbPtr));
      VncRegEnc::UpdateFrameTexture(state, *texture);
   }

   /*
    * Encode should succeed, and yield 1+ rects. Incoming dirty mask is
    * empty, but internal dirty mask created with all bits set.
    */
   EXPECT_EQ(regEnc.Encode(state), VNC_SUCCESS);
   EXPECT_GE(regEnc.BufferedRectCount(), 1);

   regEnc.ResetBuffer();
   EXPECT_EQ(regEnc.BufferedRectCount(), 0);

   WaitFrameEncodeComplete(regEnc, state);

   /*
    * Encode should succeed, but yield 0 rect because dirty mask is empty.
    */
   EXPECT_EQ(regEnc.Encode(state), statusOnEmptyDiff);
   EXPECT_EQ(regEnc.BufferedRectCount(), 0);

   regEnc.ResetBuffer();
   EXPECT_EQ(regEnc.BufferedRectCount(), 0);

   /*
    * Encode should succeed, and yield 1+ rects. Incoming dirty mask is
    * empty, but internal dirty mask created with all bits set. We reset
    * the dirty mask after first encode as encoder has internal mask.
    */
   dirtyMask.Set();
   EXPECT_EQ(regEnc.Encode(state), VNC_SUCCESS);
   EXPECT_GE(regEnc.BufferedRectCount(), 1);

   dirtyMask.Clear();
   regEnc.ResetBuffer();
   EXPECT_EQ(regEnc.BufferedRectCount(), 0);

   WaitFrameEncodeComplete(regEnc, state);

   /*
    * Lower byte budget so that any rate control updates can apply.
    */
   state.byteBudget = state.byteBudget / 8;
   state.bandwidth = 2048.0 / 8;
   regEnc.ResetBuffer();
   dirtyMask.Set();

   /*
    * Encode should succeed, and yield 1+ rects. Incoming dirty mask is
    * set.
    */
   EXPECT_EQ(regEnc.Encode(state), VNC_SUCCESS);
   EXPECT_GE(regEnc.BufferedRectCount(), 1);
}


#if CEDAR_PLATFORM_WINDOWS
void
VncIntelRegEncTest::TestFrameEncodeIntel(VncRegEnc &regEnc,    // IN/OUT
                                         PixelProviderType pp) // IN
{
   VNCRegEncFrameState state = {};
   state.byteBudget = 1000000 / 30;
   state.supportCaptureEncodeUnifiedD3DDevice = TRUE;

   auto image = GetImage(0);
   VncBitmaskPtr dirtyMask(image.width, image.height, 16);
   state.dirtyMask = *dirtyMask;

   VncFbPtr fbPtr(image);
   std::unique_ptr<OSDevicePtr> device;
   std::unique_ptr<OSTexturePtr> texture;

   if (pp == PixelProviderType::SW) {
      state.fbPtr = *fbPtr;
   } else {
      device = std::make_unique<OSDevicePtr>(VncRegEnc::CreateDevice());
      texture = std::make_unique<OSTexturePtr>(VncRegEnc::CreateTexture(*device, fbPtr));
      VncRegEnc::UpdateFrameTexture(state, *texture);
   }

   // Encode should succeed, and yield 1+ rects. Incoming dirty mask is
   // empty, but internal dirty mask created with all bits set.
   EXPECT_EQ(regEnc.Encode(state), VNC_SUCCESS);
   EXPECT_GE(regEnc.BufferedRectCount(), 1);
}


void
VncAMDRegEncTest::TestFrameEncodeAMD(VncRegEnc &regEnc,    // IN/OUT
                                     PixelProviderType pp) // IN
{
   VNCRegEncFrameState state = {};
   state.byteBudget = 1000000 / 30;
   state.supportCaptureEncodeUnifiedD3DDevice = TRUE;

   auto image = GetImage(0);
   VncBitmaskPtr dirtyMask(image.width, image.height, 16);
   state.dirtyMask = *dirtyMask;

   VncFbPtr fbPtr(image);
   OSDevicePtr device;
   ID3D11Texture2D *pD3d11Texture;
   CD3D11_TEXTURE2D_DESC desc(DXGI_FORMAT_B8G8R8A8_UNORM, fbPtr.Width(), fbPtr.Height(), 1, 1);
   D3D11_SUBRESOURCE_DATA data{};
   data.pSysMem = fbPtr.Data();
   data.SysMemPitch = fbPtr.Pitch();


   if (pp == PixelProviderType::SW) {
      state.fbPtr = *fbPtr;
   } else {
      device = new MockD3D11Device();
      device->CreateTexture2D(&desc, &data, &pD3d11Texture);
      VncRegEnc::UpdateFrameTexture(state, pD3d11Texture);
   }

   // Encode should succeed, and yield 1+ rects. Incoming dirty mask is
   // empty, but internal dirty mask created with all bits set.
   EXPECT_EQ(regEnc.Encode(state), VNC_SUCCESS);
   EXPECT_GE(regEnc.BufferedRectCount(), 1);
}
#endif // CEDAR_PLATFORM_WINDOWS

/*
 * Region encoder startup requirements:
 * 1) VNCRegEncConfig::clientInitialized == TRUE
 */

TEST_F(VncRegEncTest, TestCreateRegEncStartup)
{
   // Cases with insufficient configuration to choose BlastCodec
   EXPECT_STRNE(VncRegEnc(_config).Name(), "startup");

   BitVector_Set(_config.caps, VNCTightPNGRectCap);

   // startup should now be chosen
   EXPECT_STREQ(VncRegEnc(_config).Name(), "startup");

   // Setting clientInitialized should cause startup not to be chosen again.
   _config.clientInitialized = TRUE;
   EXPECT_STRNE(VncRegEnc(_config).Name(), "startup");
}

TEST_F(VncRegEncTest, TestEncodeRegEncStartup)
{
   BitVector_Set(_config.caps, VNCTightPNGRectCap);
   VncRegEnc regEnc(_config);

   EXPECT_STREQ(regEnc.Name(), "startup");

   VNCRegEncFrameState state = {};

   // Encode should succeed, and yield 1+ rects.
   // This region encoder doesn't use a dirty mask.
   EXPECT_EQ(regEnc.Encode(state), VNC_SUCCESS);
   EXPECT_GT(regEnc.BufferedRectCount(), 0);
}


/*
 * Region encoder static requirements:
 * - None
 */

TEST_F(VncRegEncTest, TestCreateRegEncStatic)
{
   // static should never fail to be created.
   EXPECT_STREQ(VncRegEnc(_config).Name(), "static");
}

TEST_F(VncRegEncTest, TestEncodeRegEncStatic)
{
   auto image = GetImage(0);
   Rect_SetXYWH(&_config.region.rect, 0, 0, image.width, image.height);

   VncRegEnc regEnc(_config);

   EXPECT_STREQ(regEnc.Name(), "static");

   VncBitmaskPtr dirtyMask(image.width, image.height, 16);
   VNCRegEncFrameState state = {};
   state.dirtyMask = *dirtyMask;

   VncFbPtr fbPtr(image);
   state.fbPtr = *fbPtr;

   // Encode should succeed, and yield 1+ rects. Incoming dirty mask is
   // empty, but internal dirty mask created with all bits set.
   EXPECT_EQ(regEnc.Encode(state), VNC_SUCCESS);
   EXPECT_GT(regEnc.BufferedRectCount(), 0);
}


/*
 * Region encoder adaptive requirements:
 * 1) VNCRegEncConfig::dynamicConfig::allowAdaptive == TRUE
 * 2) VNCRegEncConfig::region::rect width and height >= 16
 * 3) a) VNCRegEncConfig::clientSupportsTightJpeg == TRUE
 *    AND i) VNCRegEncConfig::caps contains VNCTightPNGRectCap
 *    AND ii) VNCRegEncConfig::clientInitialized == TRUE
 *    OR i) VNCRegEncConfig::caps contains VNCJpegRectCap
 * OR b) VNCRegEncConfig::clientSupportsTightJpeg == FALSE
 */

TEST_F(VncRegEncTest, TestCreateRegEncAdaptive)
{
   // Cases with insufficient configuration to choose adaptive
   EXPECT_STRNE(VncRegEnc(_config).Name(), "adaptive");

   _config.dynamicConfig.allowAdaptive = TRUE;
   EXPECT_STRNE(VncRegEnc(_config).Name(), "adaptive");

   _config.clientSupportsTightJpeg = TRUE;
   EXPECT_STRNE(VncRegEnc(_config).Name(), "adaptive");

   // adaptive should now be chosen
   BitVector_Set(_config.caps, VNCJpegRectCap);
   EXPECT_STREQ(VncRegEnc(_config).Name(), "adaptive");

   BitVector_Set(_config.caps, VNCTightPNGRectCap);
   // startup will be used here as clientInitialized==FALSE.
   EXPECT_STRNE(VncRegEnc(_config).Name(), "adaptive");
   // back to adaptive!
   _config.clientInitialized = TRUE;
   EXPECT_STREQ(VncRegEnc(_config).Name(), "adaptive");

   _config.clientSupportsTightJpeg = FALSE;
   EXPECT_STREQ(VncRegEnc(_config).Name(), "adaptive");

   // Using a width or height < 16 should cause adaptive not to be chosen again.
   _config.region.rect.bottom = 15;
   EXPECT_STRNE(VncRegEnc(_config).Name(), "adaptive");
}

TEST_F(VncRegEncTest, TestEncodeRegEncAdaptive)
{
   auto image = GetImage(0);
   Rect_SetXYWH(&_config.region.rect, 0, 0, image.width, image.height);
   _config.dynamicConfig.allowAdaptive = TRUE;
   _config.clientSupportsTightJpeg = TRUE;
   _config.dynamicConfig.allowJPEG = TRUE;
   _config.dynamicConfig.allowPNG = TRUE;
   _config.dynamicConfig.buildToLossless = TRUE;
   BitVector_Set(_config.caps, VNCJpegRectCap);
   BitVector_Set(_config.caps, VNCTightPNGRectCap);
   _config.clientInitialized = TRUE;
   VncRegEnc regEnc(_config);

   EXPECT_STREQ(regEnc.Name(), "adaptive");
   TestFrameEncode(regEnc, VNC_SUCCESS, PixelProviderType::SW);
}

/*
 * Region encoder BlastCodec requirements:
 * 1) VNCRegEncConfig::dynamicConfig::allowBlastCodec == TRUE
 * 2) VNCRegEncConfig::region::rect width and height >= 16
 * 3) VNCRegEncConfig::caps contains VNCRegionRectCap
 * 4) VNCRegEncConfig::clientDecoderCaps != 0
 * 5) VNCRegEncConfig::dynamicConfig::maxBlastCodecProtocolVersion >= clientDecoderCaps MSB
 * 6) VNCRegEncConfig::dynamicConfig::alwaysLossless == FALSE
 * 7) VNCRegEncConfig::dynamicConfig::buildToLossless == FALSE
 */

TEST_F(VncRegEncTest, TestCreateRegEncBlast)
{
   // Cases with insufficient configuration to choose BlastCodec
   EXPECT_STRNE(VncRegEnc(_config).Name(), "BlastCodec");

   _config.dynamicConfig.allowBlastCodec = TRUE;
   EXPECT_STRNE(VncRegEnc(_config).Name(), "BlastCodec");

   _config.clientDecoderCaps = VNCVIEW_CLIENTDECODERCAPS_BLAST_ANY;
   EXPECT_STRNE(VncRegEnc(_config).Name(), "BlastCodec");

   _config.dynamicConfig.maxBlastCodecProtocolVersion = VNC_BLAST_PROTOCOL_LATEST;
   EXPECT_STRNE(VncRegEnc(_config).Name(), "BlastCodec");

   BitVector_Set(_config.caps, VNCBlastCodecRectCap);

   // BlastCodec should now be chosen
   EXPECT_STREQ(VncRegEnc(_config).Name(), "BlastCodec");

   _config.dynamicConfig.alwaysLossless = TRUE;

   // Forcing lossless should cause BlastCodec not to be chosen again.
   EXPECT_STRNE(VncRegEnc(_config).Name(), "BlastCodec");
}

TEST_F(VncRegEncTest, TestEncodeRegEncBlastCodec)
{
   auto image = GetImage(0);
   Rect_SetXYWH(&_config.region.rect, 0, 0, image.width, image.height);
   _config.dynamicConfig.allowBlastCodec = TRUE;
   _config.dynamicConfig.enableStats = TRUE;
   _config.dynamicConfig.vncRegEncLogLevel = 1;
   _config.clientDecoderCaps = VNCVIEW_CLIENTDECODERCAPS_BLAST_ANY;
   _config.blastCodecProtocolVersion = VNC_BLAST_PROTOCOL_LATEST;
   _config.blastCodecCaps = VNCVIEW_BLASTCAP_STABLE;
   _config.dynamicConfig.maxBlastCodecProtocolVersion = VNC_BLAST_PROTOCOL_LATEST;
   _config.dynamicConfig.enableBlastCodecSharedCache = TRUE;
   _config.dynamicConfig.enableBlastCodecMultiSlice = TRUE;
   _config.dynamicConfig.enableBlastCodecDynamicSlices = TRUE;
   _config.dynamicConfig.allowImageCache = TRUE;
   BitVector_Set(_config.caps, VNCBlastCodecRectCap);
   BitVector_Set(_config.caps, VNCUpdateCacheCap);

   {
      VncRegEnc regEnc(_config);
      EXPECT_STREQ(regEnc.Name(), "BlastCodec");
      TestFrameEncode(regEnc, VNC_SUCCESS, PixelProviderType::SW);
   }

   _config.dynamicConfig.blastCacheX2Enable = FALSE;
   {
      BitVector_Set(_config.caps, VNCUpdateCacheCap);
      VncRegEnc regEnc(_config);
      EXPECT_STREQ(regEnc.Name(), "BlastCodec");
      TestFrameEncode(regEnc, VNC_SUCCESS, PixelProviderType::SW);
   }
}

/*
 * Region encoder h264 4:2:0 requirements:
 * 1) VNCRegEncConfig::dynamicConfig::allowH264 == TRUE
 * 2) VNCRegEncConfig::dynamicConfig::allowSoftwareH264 == TRUE
 * 3) VNCRegEncConfig::region::rect width and height > 0
 * 4) VNCRegEncConfig::caps contains VNCH264RectCap
 * OR VNCRegEncConfig::caps contains VNCH264MP4RectCap
 * 5) VNCRegEncConfig::dynamicConfig::alwaysLossless == FALSE
 * 6) VNCRegEncConfig::dynamicConfig::buildToLossless == FALSE
 *
 * Additional requirements for h264 4:4:4:
 * 7) VNCRegEncConfig::dynamicConfig::allowH264YUV444 == TRUE
 * 8) VNCRegEncConfig::clientSupportsH264YUV444 == TRUE
 */

TEST_F(VncRegEncTest, TestCreateRegEncH264)
{
   // Cases with insufficient configuration to choose h264
   EXPECT_STRNE(VncRegEnc(_config).Name(), "h264 4:2:0");

   _config.dynamicConfig.allowH264 = TRUE;
   EXPECT_STRNE(VncRegEnc(_config).Name(), "h264 4:2:0");

   _config.dynamicConfig.allowSoftwareH264 = TRUE;
   EXPECT_STRNE(VncRegEnc(_config).Name(), "h264 4:2:0");

   BitVector_Set(_config.caps, VNCH264RectCap);

   // h264 should now be chosen
   EXPECT_STREQ(VncRegEnc(_config).Name(), "h264 4:2:0");

   _config.dynamicConfig.allowH264YUV444 = TRUE;
   _config.clientSupportsH264YUV444 = TRUE;
   EXPECT_STREQ(VncRegEnc(_config).Name(), "h264 4:4:4");

   BitVector_Clear(_config.caps, VNCH264RectCap);
   BitVector_Set(_config.caps, VNCH264MP4RectCap);
   EXPECT_STRNE(VncRegEnc(_config).Name(), "h264 4:4:4");

   _config.dynamicConfig.allowMP4 = TRUE;
   EXPECT_STREQ(VncRegEnc(_config).Name(), "h264 4:4:4");

   _config.dynamicConfig.alwaysLossless = TRUE;

   // Forcing lossless should cause h264 not to be chosen again.
   EXPECT_STRNE(VncRegEnc(_config).Name(), "h264 4:4:4");
}

TEST_F(VncRegEncTest, TestEncodeRegEncH264)
{
   auto image = GetImage(0);
   Rect_SetXYWH(&_config.region.rect, 0, 0, image.width, image.height);
   _config.dynamicConfig.allowH264 = TRUE;
   _config.dynamicConfig.allowSoftwareH264 = TRUE;
   BitVector_Set(_config.caps, VNCH264RectCap);

   VNCRegEncFrameState state = {};
   state.byteBudget = 10000 / 30;

   _config.dynamicConfig.qpmaxH264SW = 36;
   _config.dynamicConfig.qpminH264 = 8;
   {
      VncRegEnc regEnc(_config);
      EXPECT_STREQ(regEnc.Name(), "h264 4:2:0");
      TestFrameEncode(regEnc, VNC_SUCCESS, PixelProviderType::SW);
   }

   _config.dynamicConfig.allowH264YUV444 = TRUE;
   _config.clientSupportsH264YUV444 = TRUE;

   {
      VncRegEnc regEnc(_config);
      EXPECT_STREQ(regEnc.Name(), "h264 4:4:4");
      TestFrameEncode(regEnc, VNC_SUCCESS, PixelProviderType::SW);
   }
}

/*
 * Windows-only for now, as consumers of gmock via dynamic linking on Linux hit
 * a double-free crash on exit.
 * See https://github.com/google/googletest/issues/930, which is supposedly
 * fixed in our googletest tree...
 */

#if CEDAR_PLATFORM_WINDOWS

/*
 * Region encoder NVIDIA NvEnc H264 4:2:0 requirements:
 * 1) VNCRegEncConfig::dynamicConfig::allowH264 == TRUE
 * 2) VNCRegEncConfig::dynamicConfig::allowNvidiaH264 == TRUE
 * 3) VNCRegEncConfig::region::rect width and height > 0
 * 4) VNCRegEncConfig::caps contains VNCH264RectCap
 * OR VNCRegEncConfig::caps contains VNCH264MP4RectCap
 * 5) VNCRegEncConfig::dynamicConfig::alwaysLossless == FALSE
 * 6) VNCRegEncConfig::dynamicConfig::buildToLossless == FALSE
 *
 * Additional requirements for NVIDIA NvEnc H264 4:4:4:
 * 7) VNCRegEncConfig::dynamicConfig::allowH264YUV444 == TRUE
 * 8) VNCRegEncConfig::clientSupportsH264YUV444 == TRUE
 */

TEST_F(VncNvidiaRegEncTest, TestCreateRegEncNvidia)
{
   // Cases with insufficient configuration to choose NvEnc H264
   EXPECT_STRNE(VncRegEnc(_config).Name(), "NVIDIA NvEnc H264 4:2:0");

   _config.dynamicConfig.allowH264 = TRUE;
   EXPECT_STRNE(VncRegEnc(_config).Name(), "NVIDIA NvEnc H264 4:2:0");

   _config.dynamicConfig.allowNvidiaH264 = TRUE;
   EXPECT_STRNE(VncRegEnc(_config).Name(), "NVIDIA NvEnc H264 4:2:0");

   BitVector_Set(_config.caps, VNCH264RectCap);

   // NvEnc H264 should now be chosen
   EXPECT_STREQ(VncRegEnc(_config).Name(), "NVIDIA NvEnc H264 4:2:0");

   _config.dynamicConfig.allowH264YUV444 = TRUE;
   _config.clientSupportsH264YUV444 = TRUE;
   EXPECT_STREQ(VncRegEnc(_config).Name(), "NVIDIA NvEnc H264 4:4:4");

   BitVector_Clear(_config.caps, VNCH264RectCap);
   BitVector_Set(_config.caps, VNCH264MP4RectCap);
   EXPECT_STRNE(VncRegEnc(_config).Name(), "NVIDIA NvEnc H264 4:4:4");

   _config.dynamicConfig.allowMP4 = TRUE;
   EXPECT_STREQ(VncRegEnc(_config).Name(), "NVIDIA NvEnc H264 4:4:4");

   _config.dynamicConfig.allowHEVC = TRUE;
   _config.clientSupportsHEVC = TRUE;
   _config.dynamicConfig.allowNvidiaHEVC = TRUE;
   _config.clientSupportsHEVCYUV444 = TRUE;
   _config.dynamicConfig.allowHEVCYUV444 = TRUE;
   EXPECT_STREQ(VncRegEnc(_config).Name(), "NVIDIA NvEnc HEVC 4:4:4");

   _config.dynamicConfig.allowH264YUV444 = FALSE;
   _config.dynamicConfig.allowHEVCYUV444 = FALSE;
   EXPECT_STREQ(VncRegEnc(_config).Name(), "NVIDIA NvEnc HEVC 4:2:0");

   _config.dynamicConfig.allowAV1 = TRUE;
   _config.clientSupportsAV1 = TRUE;
   _config.dynamicConfig.allowNvidiaAV1 = TRUE;
   EXPECT_STREQ(VncRegEnc(_config).Name(), "NVIDIA NvEnc AV1 4:2:0");

   _config.dynamicConfig.alwaysLossless = TRUE;

   // Forcing lossless should cause NvEnc H264 not to be chosen again.
   EXPECT_STRNE(VncRegEnc(_config).Name(), "NVIDIA NvEnc AV1 4:2:0");
}

TEST_F(VncNvidiaRegEncTest, TestEncodeRegEncNvidia)
{
   auto image = GetImage(0);
   Rect_SetXYWH(&_config.region.rect, 0, 0, image.width, image.height);

   // Turn these on to maximize code coverage
   _config.dynamicConfig.vncEncodeLogLevel = 9;
   _config.dynamicConfig.vncRegEncLogLevel = 9;
   _config.dynamicConfig.enableNvidiaBwSaving = TRUE;

   _config.dynamicConfig.allowH264 = TRUE;
   _config.dynamicConfig.allowNvidiaH264 = TRUE;
   BitVector_Set(_config.caps, VNCH264RectCap);

   _config.dynamicConfig.qpmaxH264 = 36;
   _config.dynamicConfig.qpminH264 = 8;
   _config.dynamicConfig.enableNvidiaH264CustomRC = TRUE;
   _config.staticConfig.allowCaptureEncodeUnifiedD3DDevice = TRUE;
   {
      VncRegEnc regEnc(_config);
      EXPECT_STREQ(regEnc.Name(), "NVIDIA NvEnc H264 4:2:0");
      TestFrameEncode(regEnc, VNC_ERROR_RETRY_ENCODE, PixelProviderType::HW);
   }

   _config.dynamicConfig.allowHEVC = TRUE;
   _config.clientSupportsHEVC = TRUE;
   _config.dynamicConfig.allowNvidiaHEVC = TRUE;
   {
      VncRegEnc regEnc(_config);
      EXPECT_STREQ(regEnc.Name(), "NVIDIA NvEnc HEVC 4:2:0");
      TestFrameEncode(regEnc, VNC_ERROR_RETRY_ENCODE, PixelProviderType::HW);
   }

   _config.dynamicConfig.allowAV1 = TRUE;
   _config.clientSupportsAV1 = TRUE;
   _config.dynamicConfig.allowNvidiaAV1 = TRUE;
   {
      VncRegEnc regEnc(_config);
      EXPECT_STREQ(regEnc.Name(), "NVIDIA NvEnc AV1 4:2:0");
      TestFrameEncode(regEnc, VNC_ERROR_RETRY_ENCODE, PixelProviderType::HW);
   }

   _config.clientSupportsHEVCYUV444 = TRUE;
   _config.dynamicConfig.allowHEVCYUV444 = TRUE;
   {
      VncRegEnc regEnc(_config);
      EXPECT_STREQ(regEnc.Name(), "NVIDIA NvEnc HEVC 4:4:4");
      TestFrameEncode(regEnc, VNC_ERROR_RETRY_ENCODE, PixelProviderType::HW);
   }

   _config.dynamicConfig.allowHEVC = FALSE;
   _config.clientSupportsH264YUV444 = TRUE;
   _config.dynamicConfig.allowH264YUV444 = TRUE;
   {
      VncRegEnc regEnc(_config);
      EXPECT_STREQ(regEnc.Name(), "NVIDIA NvEnc H264 4:4:4");
      TestFrameEncode(regEnc, VNC_ERROR_RETRY_ENCODE, PixelProviderType::HW);
   }
}

/*
 * Intel H264 4:2:0 requirements:
 * 1) VNCRegEncConfig::dynamicConfig::allowH264 == TRUE
 * 2) VNCRegEncConfig::dynamicConfig::allowIntelH264 == TRUE
 * 3) VNCRegEncConfig::region::rect width and height > 0
 * 4) VNCRegEncConfig::caps contains VNCH264RectCap
 * 5) VNCRegEncConfig::dynamicConfig::alwaysLossless == FALSE
 * 6) VNCRegEncConfig::dynamicConfig::buildToLossless == FALSE
 *
 * Intel H264 4:2:0 will not be set if:
 * 1) VNCRegEncConfig::caps contains VNCH264MP4RectCap
 *
 * Intel H264 4:4:4 will never be chosen even if:
 * 1) VNCRegEncConfig::dynamicConfig::allowH264YUV444 == TRUE
 * 2) VNCRegEncConfig::clientSupportsH264YUV444 == TRUE
 *
 * Intel HEVC 4:2:0 requirements:
 * 1) VNCRegEncConfig::dynamicConfig::allowHEVC == TRUE
 * 2) VNCRegEncConfig::dynamicConfig::allowIntelHEVC == TRUE
 * 3) VNCRegEncConfig::region::rect width and height > 0
 * 4) VNCRegEncConfig::caps contains VNCH264RectCap
 * 5) VNCRegEncConfig::dynamicConfig::alwaysLossless == FALSE
 * 6) VNCRegEncConfig::dynamicConfig::buildToLossless == FALSE
 */

TEST_F(VncIntelRegEncTest, TestCreateRegEncIntelSW)
{
   // Cases with insufficient configuration to choose Intel H264 SW
   EXPECT_STRNE(VncRegEnc(_config).Name(), "Intel H264 SW 4:2:0");

   _config.dynamicConfig.allowH264 = TRUE;
   EXPECT_STRNE(VncRegEnc(_config).Name(), "Intel H264 SW 4:2:0");

   _config.dynamicConfig.allowIntelH264 = TRUE;
   EXPECT_STRNE(VncRegEnc(_config).Name(), "Intel H264 SW 4:2:0");

   BitVector_Set(_config.caps, VNCH264RectCap);
   // Intel H264 SW 4:2:0 should now be chosen
   EXPECT_STREQ(VncRegEnc(_config).Name(), "Intel H264 SW 4:2:0");

   // Intel H264 SW 4:4:4 will never be set, as it is not supported agent-side
   _config.dynamicConfig.allowH264YUV444 = TRUE;
   _config.clientSupportsH264YUV444 = TRUE;
   EXPECT_STRNE(VncRegEnc(_config).Name(), "Intel H264 SW 4:4:4");

   BitVector_Clear(_config.caps, VNCH264RectCap);
   BitVector_Set(_config.caps, VNCH264MP4RectCap);
   EXPECT_STRNE(VncRegEnc(_config).Name(), "Intel H264 SW 4:4:4");

   _config.dynamicConfig.allowMP4 = TRUE;
   EXPECT_STRNE(VncRegEnc(_config).Name(), "Intel H264 SW 4:4:4");

   BitVector_Set(_config.caps, VNCH264RectCap);
   BitVector_Clear(_config.caps, VNCH264MP4RectCap);

   _config.dynamicConfig.allowHEVC = TRUE;
   EXPECT_STRNE(VncRegEnc(_config).Name(), "Intel HEVC SW 4:2:0");
   _config.clientSupportsHEVC = TRUE;
   EXPECT_STRNE(VncRegEnc(_config).Name(), "Intel HEVC SW 4:2:0");
   _config.dynamicConfig.allowIntelHEVC = TRUE;
   EXPECT_STREQ(VncRegEnc(_config).Name(), "Intel HEVC SW 4:2:0");

   _config.clientSupportsHEVCYUV444 = TRUE;
   _config.dynamicConfig.allowHEVCYUV444 = TRUE;
   EXPECT_STREQ(VncRegEnc(_config).Name(), "Intel HEVC SW 4:4:4");

   _config.dynamicConfig.allowAV1 = TRUE;
   _config.clientSupportsAV1 = TRUE;
   _config.dynamicConfig.allowIntelAV1 = TRUE;
   // HEVC 4:4:4 will be preferred over AV1 4:2:0
   EXPECT_STRNE(VncRegEnc(_config).Name(), "Intel AV1 SW 4:2:0");
   EXPECT_STREQ(VncRegEnc(_config).Name(), "Intel HEVC SW 4:4:4");

   _config.clientSupportsHEVCYUV444 = FALSE;
   _config.dynamicConfig.allowHEVCYUV444 = TRUE;
   // HEVC 4:4:4 is disabled. Now AV1 4:2:0 will be selected
   EXPECT_STREQ(VncRegEnc(_config).Name(), "Intel AV1 SW 4:2:0");

   _config.dynamicConfig.alwaysLossless = TRUE;
   // Forcing lossless should cause Intel Encoder not to be chosen again.
   EXPECT_STRNE(VncRegEnc(_config).Name(), "Intel AV1 SW 4:2:0");
   EXPECT_STRNE(VncRegEnc(_config).Name(), "Intel HEVC SW 4:2:0");
   EXPECT_STRNE(VncRegEnc(_config).Name(), "Intel H264 SW 4:2:0");
}

TEST_F(VncIntelRegEncTest, TestCreateRegEncIntelHW)
{
   // Set HW Capture path
   _config.dynamicConfig.nrHWEncoders = 6;

   // Cases with insufficient configuration to choose Intel H264 HW
   EXPECT_STRNE(VncRegEnc(_config).Name(), "Intel H264 HW 4:2:0");

   _config.dynamicConfig.allowH264 = TRUE;
   EXPECT_STRNE(VncRegEnc(_config).Name(), "Intel H264 HW 4:2:0");

   _config.dynamicConfig.allowIntelH264 = TRUE;
   EXPECT_STRNE(VncRegEnc(_config).Name(), "Intel H264 HW 4:2:0");

   BitVector_Set(_config.caps, VNCH264RectCap);
   // Intel H264 SW 4:2:0 should now be chosen
   EXPECT_STREQ(VncRegEnc(_config).Name(), "Intel H264 HW 4:2:0");

   // Intel H264 SW 4:4:4 will never be set, as it is not supported agent-side
   _config.dynamicConfig.allowH264YUV444 = TRUE;
   _config.clientSupportsH264YUV444 = TRUE;
   EXPECT_STRNE(VncRegEnc(_config).Name(), "Intel H264 HW 4:4:4");

   BitVector_Clear(_config.caps, VNCH264RectCap);
   BitVector_Set(_config.caps, VNCH264MP4RectCap);
   EXPECT_STRNE(VncRegEnc(_config).Name(), "Intel H264 HW 4:4:4");

   _config.dynamicConfig.allowMP4 = TRUE;
   EXPECT_STRNE(VncRegEnc(_config).Name(), "Intel H264 HW 4:4:4");

   BitVector_Set(_config.caps, VNCH264RectCap);
   BitVector_Clear(_config.caps, VNCH264MP4RectCap);

   _config.dynamicConfig.allowHEVC = TRUE;
   EXPECT_STRNE(VncRegEnc(_config).Name(), "Intel HEVC HW 4:2:0");
   _config.clientSupportsHEVC = TRUE;
   EXPECT_STRNE(VncRegEnc(_config).Name(), "Intel HEVC HW 4:2:0");
   _config.dynamicConfig.allowIntelHEVC = TRUE;
   EXPECT_STREQ(VncRegEnc(_config).Name(), "Intel HEVC HW 4:2:0");

   _config.clientSupportsHEVCYUV444 = TRUE;
   _config.dynamicConfig.allowHEVCYUV444 = TRUE;
   EXPECT_STREQ(VncRegEnc(_config).Name(), "Intel HEVC HW 4:4:4");

   _config.dynamicConfig.allowAV1 = TRUE;
   _config.clientSupportsAV1 = TRUE;
   _config.dynamicConfig.allowIntelAV1 = TRUE;
   // HEVC 4:4:4 will be preferred over AV1 4:2:0
   EXPECT_STRNE(VncRegEnc(_config).Name(), "Intel AV1 HW 4:2:0");
   EXPECT_STREQ(VncRegEnc(_config).Name(), "Intel HEVC HW 4:4:4");

   _config.clientSupportsHEVCYUV444 = FALSE;
   _config.dynamicConfig.allowHEVCYUV444 = TRUE;
   // HEVC 4:4:4 is disabled. Now AV1 4:2:0 will be selected
   EXPECT_STREQ(VncRegEnc(_config).Name(), "Intel AV1 HW 4:2:0");

   _config.dynamicConfig.alwaysLossless = TRUE;
   // Forcing lossless should cause Intel Encoder not to be chosen again.
   EXPECT_STRNE(VncRegEnc(_config).Name(), "Intel AV1 HW 4:2:0");
   EXPECT_STRNE(VncRegEnc(_config).Name(), "Intel HEVC HW 4:2:0");
   EXPECT_STRNE(VncRegEnc(_config).Name(), "Intel H264 HW 4:2:0");
}

TEST_F(VncIntelRegEncTest, TestEncodeRegEncIntelAligned)
{
   auto image = GetImage(0);
   Rect_SetXYWH(&_config.region.rect, 0, 0, image.width, image.height);

   // Turn these on to maximize code coverage
   _config.dynamicConfig.vncEncodeLogLevel = 9;
   _config.dynamicConfig.vncRegEncLogLevel = 9;

   _config.dynamicConfig.allowH264 = TRUE;
   _config.dynamicConfig.allowIntelH264 = TRUE;

   BitVector_Set(_config.caps, VNCH264RectCap);

   _config.staticConfig.allowCaptureEncodeUnifiedD3DDevice = TRUE;

   {
      VncRegEnc regEnc(_config);
      EXPECT_STREQ(regEnc.Name(), "Intel H264 SW 4:2:0");
      TestFrameEncodeIntel(regEnc, PixelProviderType::SW);
   }

   _config.dynamicConfig.allowHEVC = TRUE;
   _config.clientSupportsHEVC = TRUE;
   _config.dynamicConfig.allowIntelHEVC = TRUE;
   {
      VncRegEnc regEnc(_config);
      EXPECT_STREQ(regEnc.Name(), "Intel HEVC SW 4:2:0");
      TestFrameEncodeIntel(regEnc, PixelProviderType::SW);
   }

   _config.dynamicConfig.allowAV1 = TRUE;
   _config.clientSupportsAV1 = TRUE;
   _config.dynamicConfig.allowIntelAV1 = TRUE;
   {
      VncRegEnc regEnc(_config);
      EXPECT_STREQ(regEnc.Name(), "Intel AV1 SW 4:2:0");
      TestFrameEncodeIntel(regEnc, PixelProviderType::SW);
   }

   _config.clientSupportsHEVCYUV444 = TRUE;
   _config.dynamicConfig.allowHEVCYUV444 = TRUE;
   {
      VncRegEnc regEnc(_config);
      EXPECT_STREQ(regEnc.Name(), "Intel HEVC SW 4:4:4");
      TestFrameEncodeIntel(regEnc, PixelProviderType::SW);
   }

   // Set HW Capture path
   _config.dynamicConfig.nrHWEncoders = 6;

   _config.dynamicConfig.allowH264 = TRUE;
   _config.dynamicConfig.allowIntelH264 = TRUE;
   _config.dynamicConfig.allowHEVC = FALSE;
   _config.clientSupportsHEVC = FALSE;
   _config.dynamicConfig.allowIntelHEVC = FALSE;
   _config.dynamicConfig.allowAV1 = FALSE;
   _config.clientSupportsAV1 = FALSE;
   _config.dynamicConfig.allowIntelAV1 = FALSE;
   _config.clientSupportsHEVCYUV444 = FALSE;
   _config.dynamicConfig.allowHEVCYUV444 = FALSE;

   BitVector_Set(_config.caps, VNCH264RectCap);

   _config.staticConfig.allowCaptureEncodeUnifiedD3DDevice = TRUE;

   {
      VncRegEnc regEnc(_config);
      EXPECT_STREQ(regEnc.Name(), "Intel H264 HW 4:2:0");
      TestFrameEncodeIntel(regEnc, PixelProviderType::HW);
   }

   _config.dynamicConfig.allowHEVC = TRUE;
   _config.clientSupportsHEVC = TRUE;
   _config.dynamicConfig.allowIntelHEVC = TRUE;
   {
      VncRegEnc regEnc(_config);
      EXPECT_STREQ(regEnc.Name(), "Intel HEVC HW 4:2:0");
      TestFrameEncodeIntel(regEnc, PixelProviderType::HW);
   }

   _config.dynamicConfig.allowAV1 = TRUE;
   _config.clientSupportsAV1 = TRUE;
   _config.dynamicConfig.allowIntelAV1 = TRUE;
   {
      VncRegEnc regEnc(_config);
      EXPECT_STREQ(regEnc.Name(), "Intel AV1 HW 4:2:0");
      TestFrameEncodeIntel(regEnc, PixelProviderType::HW);
   }

   _config.clientSupportsHEVCYUV444 = TRUE;
   _config.dynamicConfig.allowHEVCYUV444 = TRUE;
   {
      VncRegEnc regEnc(_config);
      EXPECT_STREQ(regEnc.Name(), "Intel HEVC HW 4:4:4");
      TestFrameEncodeIntel(regEnc, PixelProviderType::HW);
   }
}

TEST_F(VncIntelRegEncTest, TestEncodeRegEncIntelMisaligned)
{
   // Turn these on to maximize code coverage
   _config.dynamicConfig.vncEncodeLogLevel = 9;
   _config.dynamicConfig.vncRegEncLogLevel = 9;

   auto image = GetImage(0);
   Rect_SetXYWH(&_config.region.rect, 0, 0, image.width, image.height);

   // TODO: Add SW Capture misaligned path once we mock the surface interfaces

   // Set HW Capture path
   _config.dynamicConfig.nrHWEncoders = 6;

   _config.dynamicConfig.allowH264 = TRUE;
   _config.dynamicConfig.allowIntelH264 = TRUE;
   _config.dynamicConfig.allowHEVC = FALSE;
   _config.clientSupportsHEVC = FALSE;
   _config.dynamicConfig.allowIntelHEVC = FALSE;
   _config.dynamicConfig.allowAV1 = FALSE;
   _config.clientSupportsAV1 = FALSE;
   _config.dynamicConfig.allowIntelAV1 = FALSE;
   _config.clientSupportsHEVCYUV444 = FALSE;
   _config.dynamicConfig.allowHEVCYUV444 = FALSE;

   BitVector_Set(_config.caps, VNCH264RectCap);

   _config.staticConfig.allowCaptureEncodeUnifiedD3DDevice = TRUE;

   {
      VncRegEnc regEnc(_config);
      EXPECT_STREQ(regEnc.Name(), "Intel H264 HW 4:2:0");
      TestFrameEncodeIntel(regEnc, PixelProviderType::HW);
   }

   _config.dynamicConfig.allowHEVC = TRUE;
   _config.clientSupportsHEVC = TRUE;
   _config.dynamicConfig.allowIntelHEVC = TRUE;
   {
      VncRegEnc regEnc(_config);
      EXPECT_STREQ(regEnc.Name(), "Intel HEVC HW 4:2:0");
      TestFrameEncodeIntel(regEnc, PixelProviderType::HW);
   }

   _config.dynamicConfig.allowAV1 = TRUE;
   _config.clientSupportsAV1 = TRUE;
   _config.dynamicConfig.allowIntelAV1 = TRUE;
   {
      VncRegEnc regEnc(_config);
      EXPECT_STREQ(regEnc.Name(), "Intel AV1 HW 4:2:0");
      TestFrameEncodeIntel(regEnc, PixelProviderType::HW);
   }

   _config.clientSupportsHEVCYUV444 = TRUE;
   _config.dynamicConfig.allowHEVCYUV444 = TRUE;
   {
      VncRegEnc regEnc(_config);
      EXPECT_STREQ(regEnc.Name(), "Intel HEVC HW 4:4:4");
      TestFrameEncodeIntel(regEnc, PixelProviderType::HW);
   }
}

/*
 * Region encoder AMD H264 HW 4:2:0 requirements:
 * 1) VNCRegEncConfig::dynamicConfig::allowH264 == TRUE
 * 2) VNCRegEncConfig::dynamicConfig::allowAMDH264 == TRUE
 * 3) VNCRegEncConfig::region::rect width and height > 0
 * 4) VNCRegEncConfig::caps contains VNCH264RectCap
 * OR VNCRegEncConfig::caps contains VNCH264MP4RectCap
 * 5) VNCRegEncConfig::dynamicConfig::alwaysLossless == FALSE
 * 6) VNCRegEncConfig::dynamicConfig::buildToLossless == FALSE
 *
 * Requirements for AMD HEVC HW 4:2:0
 * 7) VNCRegEncConfig::dynamicConfig::allowHEVCH264 == TRUE
 *
 * Requirements for AMD AV1 HW 4:2:0
 * 8) VNCRegEncConfig::dynamicConfig::allowHEVCH264 == TRUE
 *
 * AMD HW encoders curretly do not support 4:4:4 for mat
 *
 */

TEST_F(VncAMDRegEncTest, TestCreateRegEncAMDSW)
{
   // Cases with insufficient configuration to choose AMD H264 HW
   EXPECT_STRNE(VncRegEnc(_config).Name(), "AMD H264 SW 4:2:0");

   _config.dynamicConfig.allowH264 = TRUE;
   EXPECT_STRNE(VncRegEnc(_config).Name(), "AMD H264 SW 4:2:0");

   _config.dynamicConfig.allowAMDH264 = TRUE;
   EXPECT_STRNE(VncRegEnc(_config).Name(), "AMD H264 SW 4:2:0");

   BitVector_Set(_config.caps, VNCH264RectCap);

   // AMD H264 HW should now be chosen
   EXPECT_STREQ(VncRegEnc(_config).Name(), "AMD H264 SW 4:2:0");

   // AMD H264 SW 4:4:4 will never be set, as it is not supported agent-side
   _config.dynamicConfig.allowH264YUV444 = TRUE;
   _config.clientSupportsH264YUV444 = TRUE;
   EXPECT_STRNE(VncRegEnc(_config).Name(), "AMD H264 SW 4:4:4");

   BitVector_Clear(_config.caps, VNCH264RectCap);
   BitVector_Set(_config.caps, VNCH264MP4RectCap);
   EXPECT_STRNE(VncRegEnc(_config).Name(), "AMD H264 SW 4:4:4");

   _config.dynamicConfig.allowMP4 = TRUE;
   EXPECT_STRNE(VncRegEnc(_config).Name(), "AMD H264 SW 4:4:4");

   BitVector_Set(_config.caps, VNCH264RectCap);
   BitVector_Clear(_config.caps, VNCH264MP4RectCap);

   _config.dynamicConfig.allowHEVC = TRUE;
   EXPECT_STRNE(VncRegEnc(_config).Name(), "AMD HEVC SW 4:2:0");
   _config.clientSupportsHEVC = TRUE;
   EXPECT_STRNE(VncRegEnc(_config).Name(), "AMD HEVC SW 4:2:0");
   _config.dynamicConfig.allowAMDHEVC = TRUE;
   EXPECT_STREQ(VncRegEnc(_config).Name(), "AMD HEVC SW 4:2:0");

   // AMD HEVC SW 4:4:4 will never be set, as it is not supported agent-side
   _config.clientSupportsHEVCYUV444 = TRUE;
   _config.dynamicConfig.allowHEVCYUV444 = TRUE;
   EXPECT_STRNE(VncRegEnc(_config).Name(), "AMD HEVC SW 4:4:4");

   _config.dynamicConfig.allowAV1 = TRUE;
   _config.clientSupportsAV1 = TRUE;
   _config.dynamicConfig.allowAMDAV1 = TRUE;
   EXPECT_STREQ(VncRegEnc(_config).Name(), "AMD AV1 SW 4:2:0");

   _config.dynamicConfig.alwaysLossless = TRUE;

   // Forcing lossless should cause AMD AV1 not to be chosen again.
   EXPECT_STRNE(VncRegEnc(_config).Name(), "AMD AV1 SW 4:2:0");
   EXPECT_STRNE(VncRegEnc(_config).Name(), "AMD HEVC SW 4:2:0");
   EXPECT_STRNE(VncRegEnc(_config).Name(), "AMD H264 SW 4:2:0");
}

TEST_F(VncAMDRegEncTest, TestCreateRegEncAMDHW)
{
   // Set HW Capture path
   _config.dynamicConfig.nrHWEncoders = 6;

   // Cases with insufficient configuration to choose AMD H264 HW
   EXPECT_STRNE(VncRegEnc(_config).Name(), "AMD H264 HW 4:2:0");

   _config.dynamicConfig.allowH264 = TRUE;
   EXPECT_STRNE(VncRegEnc(_config).Name(), "AMD H264 HW 4:2:0");

   _config.dynamicConfig.allowAMDH264 = TRUE;
   EXPECT_STRNE(VncRegEnc(_config).Name(), "AMD H264 HW 4:2:0");

   BitVector_Set(_config.caps, VNCH264RectCap);
   // AMD H264 HW 4:2:0 should now be chosen
   EXPECT_STREQ(VncRegEnc(_config).Name(), "AMD H264 HW 4:2:0");

   // AMD H264 HW 4:4:4 will never be set, as it is not supported agent-side
   _config.dynamicConfig.allowH264YUV444 = TRUE;
   _config.clientSupportsH264YUV444 = TRUE;
   EXPECT_STRNE(VncRegEnc(_config).Name(), "AMD H264 HW 4:4:4");

   BitVector_Clear(_config.caps, VNCH264RectCap);
   BitVector_Set(_config.caps, VNCH264MP4RectCap);
   EXPECT_STRNE(VncRegEnc(_config).Name(), "AMD H264 HW 4:4:4");

   _config.dynamicConfig.allowMP4 = TRUE;
   EXPECT_STRNE(VncRegEnc(_config).Name(), "AMD H264 HW 4:4:4");

   BitVector_Set(_config.caps, VNCH264RectCap);
   BitVector_Clear(_config.caps, VNCH264MP4RectCap);

   _config.dynamicConfig.allowHEVC = TRUE;
   EXPECT_STRNE(VncRegEnc(_config).Name(), "AMD HEVC HW 4:2:0");
   _config.clientSupportsHEVC = TRUE;
   EXPECT_STRNE(VncRegEnc(_config).Name(), "AMD HEVC HW 4:2:0");
   _config.dynamicConfig.allowAMDHEVC = TRUE;
   EXPECT_STREQ(VncRegEnc(_config).Name(), "AMD HEVC HW 4:2:0");

   _config.clientSupportsHEVCYUV444 = TRUE;
   _config.dynamicConfig.allowHEVCYUV444 = TRUE;
   EXPECT_STRNE(VncRegEnc(_config).Name(), "AMD HEVC HW 4:4:4");

   _config.dynamicConfig.allowAV1 = TRUE;
   _config.clientSupportsAV1 = TRUE;
   _config.dynamicConfig.allowAMDAV1 = TRUE;
   EXPECT_STREQ(VncRegEnc(_config).Name(), "AMD AV1 HW 4:2:0");

   _config.dynamicConfig.alwaysLossless = TRUE;
   // Forcing lossless should cause AMD Encoder not to be chosen again.
   EXPECT_STRNE(VncRegEnc(_config).Name(), "AMD AV1 HW 4:2:0");
   EXPECT_STRNE(VncRegEnc(_config).Name(), "AMD HEVC HW 4:2:0");
   EXPECT_STRNE(VncRegEnc(_config).Name(), "AMD H264 HW 4:2:0");
}


TEST_F(VncAMDRegEncTest, TestEncodeRegEncAMD)
{
   auto image = GetImage(0);
   Rect_SetXYWH(&_config.region.rect, 0, 0, image.width, image.height);

   // Turn these on to maximize code coverage
   _config.dynamicConfig.vncEncodeLogLevel = 9;
   _config.dynamicConfig.vncRegEncLogLevel = 9;

   _config.dynamicConfig.allowH264 = TRUE;
   _config.dynamicConfig.allowAMDH264 = TRUE;

   BitVector_Set(_config.caps, VNCH264RectCap);

   _config.staticConfig.allowCaptureEncodeUnifiedD3DDevice = TRUE;

   {
      VncRegEnc regEnc(_config);
      EXPECT_STREQ(regEnc.Name(), "AMD H264 SW 4:2:0");
      TestFrameEncodeAMD(regEnc, PixelProviderType::SW);
   }

   _config.dynamicConfig.allowHEVC = TRUE;
   _config.clientSupportsHEVC = TRUE;
   _config.dynamicConfig.allowAMDHEVC = TRUE;
   {
      VncRegEnc regEnc(_config);
      EXPECT_STREQ(regEnc.Name(), "AMD HEVC SW 4:2:0");
      TestFrameEncodeAMD(regEnc, PixelProviderType::SW);
   }

   _config.dynamicConfig.allowAV1 = TRUE;
   _config.clientSupportsAV1 = TRUE;
   _config.dynamicConfig.allowAMDAV1 = TRUE;
   {
      VncRegEnc regEnc(_config);
      EXPECT_STREQ(regEnc.Name(), "AMD AV1 SW 4:2:0");
      TestFrameEncodeAMD(regEnc, PixelProviderType::SW);
   }


   // Set HW Capture path
   _config.dynamicConfig.nrHWEncoders = 6;

   _config.dynamicConfig.allowH264 = TRUE;
   _config.dynamicConfig.allowAMDH264 = TRUE;
   _config.dynamicConfig.allowHEVC = FALSE;
   _config.clientSupportsHEVC = FALSE;
   _config.dynamicConfig.allowAMDHEVC = FALSE;
   _config.dynamicConfig.allowAV1 = FALSE;
   _config.clientSupportsAV1 = FALSE;
   _config.dynamicConfig.allowAMDAV1 = FALSE;
   _config.clientSupportsHEVCYUV444 = FALSE;
   _config.dynamicConfig.allowHEVCYUV444 = FALSE;

   BitVector_Set(_config.caps, VNCH264RectCap);

   _config.staticConfig.allowCaptureEncodeUnifiedD3DDevice = TRUE;

   {
      VncRegEnc regEnc(_config);
      EXPECT_STREQ(regEnc.Name(), "AMD H264 HW 4:2:0");
      TestFrameEncodeAMD(regEnc, PixelProviderType::HW);
   }

   _config.dynamicConfig.allowHEVC = TRUE;
   _config.clientSupportsHEVC = TRUE;
   _config.dynamicConfig.allowAMDHEVC = TRUE;
   {
      VncRegEnc regEnc(_config);
      EXPECT_STREQ(regEnc.Name(), "AMD HEVC HW 4:2:0");
      TestFrameEncodeAMD(regEnc, PixelProviderType::HW);
   }

   _config.dynamicConfig.allowAV1 = TRUE;
   _config.clientSupportsAV1 = TRUE;
   _config.dynamicConfig.allowAMDAV1 = TRUE;
   {
      VncRegEnc regEnc(_config);
      EXPECT_STREQ(regEnc.Name(), "AMD AV1 HW 4:2:0");
      TestFrameEncodeAMD(regEnc, PixelProviderType::HW);
   }
}


#endif // CEDAR_PLATFORM_WINDOWS