/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * autoTestBase.cpp --
 *
 *      Base class of auto tests.
 */

#include <string>

#include "autoTestBase.h"

#ifdef __APPLE__
#   include "macos/ciCFLoop.h"
#elif defined(__ANDROID__)
#   include "android/ciMainLoop.h"
#elif defined __linux__
#   include "linux/ciGtkLoop.h"
#elif defined _WIN32
#   include "win32/ciDefaultLoop.h"
#endif

#include "log.h"
#include "preference.h"
#include "vthread.h"

#define LOG_TAG "AndroidCT-autoTestBase"
#include "androidLog.h"

VmwHorizonClientProtocol AutoTestBase::mClientProtocol = VmwHorizonClientProtocol_Default;
SessionManagementInterface *AutoTestBase::mSessionManager = nullptr;

static int g_exitCode = 0;


/*
 *-----------------------------------------------------------------------------
 *
 * AutoTestBase::SetUp --
 *
 *      Sets up the test fixture.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

void
AutoTestBase::SetUp()
{
   TestBase::SetUp();
   Log("%s Case Start.\n", GetCurrentTimeStamp().c_str());
}


/*
 *-----------------------------------------------------------------------------
 *
 * AutoTestBase::TearDown --
 *
 *      Tears down the test fixture.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

void
AutoTestBase::TearDown()
{
   TestBase::TearDown();
   Log("%s Case End.\n", GetCurrentTimeStamp().c_str());
}


/*
 *-----------------------------------------------------------------------------
 *
 * AutoTestBase::SetUpTestCase --
 * AutoTestBase::TearDownTestCase --
 *
 *      Called before connecting to the Agent.
 *      You can implement it in your test case class to get the pointers of
 *      exported functions and register required callbacks here.
 *
 *      Give a warning if the Test case doesn't not implement it.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

void
AutoTestBase::SetUpTestCase()
{
   Warning("[***Warning***] %s: SetUpTestCase is NOT implemented.\n", __FUNCTION__);
}

void
AutoTestBase::TearDownTestCase()
{
   Warning("[***Warning***] %s: TearDownTestCase is NOT implemented.\n", __FUNCTION__);
}


/*
 *-----------------------------------------------------------------------------
 *
 * AutoTestBase::SetSessionManager --
 *
 *      Set session manager to test case which will provide the connection method.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

void
AutoTestBase::SetSessionManager(SessionManagementInterface *sessionMgr) // IN
{
   ASSERT(mSessionManager == nullptr);
   mSessionManager = sessionMgr;
}


/*
 *-----------------------------------------------------------------------------
 *
 * RunAllTestsFunc --
 *
 *      Function entry point for running the all tests.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      Sets global variable g_exitCode to the result of RUN_ALL_TESTS().
 *
 *-----------------------------------------------------------------------------
 */

void
RunAllTestsFunc(void *data) // IN
{
   g_exitCode = RUN_ALL_TESTS();

#ifdef __APPLE__
   bool *needStopLoop = reinterpret_cast<bool *>(data);
   if (*needStopLoop) {
      /*
       * Stop the CFRunLoop which runs in main thread so that the test can
       * exit. It must be put after cases finish. Because the stop of CFRunLoop
       * will finish the RunSessionCases function in autoTestBase and the
       * GlobalTearDown will be triggered by framework immediately.
       */
      StopMainCFLoop();
   }
#endif
}


/*
 *-----------------------------------------------------------------------------
 *
 * RunAllTests --
 *
 *      Run all the tests per the flags. Start default loop or CFRunLoop with
 *      the flags requirement.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

int
AutoTestBase::RunAllTests(uint32 flags) // IN
{
   bool needsDefaultLoop = flags & RXTEST_CLIENT_NEED_WIN32_DEFAULT_LOOP;
   bool needsCFLoop = flags & RXTEST_CLIENT_NEED_MAC_CFLOOP;

   if (needsDefaultLoop) {
      /*
       * Windows use the default poll loop. The default loop runs in child
       * thread and the test cases runs in main thread.
       */
      StartPollLoop();
      RunAllTestsFunc(&needsCFLoop);
   } else if (needsCFLoop) {
#ifdef __APPLE__
      /*
       * The default thread stack size of Mac OS is 512K, set it to 2M so that
       * the operations in thread will be safer. And this function is the
       * Mac/Linux only function defined in vthread.h
       */
      VThread_SetDefaultStackSize(2 * 1024 * 1024);
#endif
      /*
       * CFLoop is Mac OS specific mechanism. The test cases will be run in the
       * child thread and the CFRunLoop is a infinite loop running in main
       * thread which will hold the following codes. So when the test case
       * finished, the CFRunLoopStop should be dispatched to main thread to
       * stop this infinite loop.
       */
      VThread_CreateThread(RunAllTestsFunc, &needsCFLoop, "AutoTestRunAllTests", NULL);
      // Make sure to stop the CFRunLoop when the test cases finished.
      StartPollLoop();
   } else {
      RunAllTestsFunc(&needsCFLoop);
   }
   return g_exitCode;
}


/*
 *-----------------------------------------------------------------------------
 *
 * RunSessionCases --
 *
 *      Extern API entry for run session cases.
 *
 * Results:
 *      Alwags return 0.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

extern "C" RX_CI_PLUGIN_API int
RunSessionCases(SessionManagementInterface *sessionMgr, // IN
                FrameworkInterface *framework,          // IN
                int argc, char **argv)                  // IN
{
   TestBase::SetFramework(framework);
   AutoTestBase::SetSessionManager(sessionMgr);

   RxTestClientTargetInfo targetInfo;
   /*
    * This flag should be initialized to 0 by default.
    * The RunAllTests may check this flag to determine how to run
    * all tests.
    */
   targetInfo.flags = 0;
   TestCasesTargetInfo(targetInfo);

   bool needsInitLog = !(targetInfo.flags & RXTEST_CLIENT_LOG_INITIALIZED);

   /*
    * If component is compiled with test case to one lib for testing, such as
    * crtbora, then the log will be initialized insdie component product
    * code and no needs to initialize log here.
    */
   if (needsInitLog) {
      // log init
      Preference_Init();
      LogOutput *output =
         Log_InitWithCustom(Log_CfgInterface(), &AutoTestBase::LogCustomMsgFuncImpl, HZN_LOG_INFO);
      ConsoleLog("Logging test case\n");
   }

   ::testing::InitGoogleTest(&argc, argv);
   int exitCode = AutoTestBase::RunAllTests(targetInfo.flags);

   if (needsInitLog) {
      // log exit
      Log_Exit();
      Preference_Exit();
   }

   return exitCode;
}
