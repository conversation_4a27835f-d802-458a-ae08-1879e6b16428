inputs:
  workflowId:
    description: 'Workflow filename'
    required: true
  jobs:
    description: '<PERSON><PERSON> list of jobs to check the status of'
    required: true
  buildtype:
    description: 'Build type'
    default: 'beta'
  slackWebhookUrl:
    description: 'Pass secrets.CART_SLACK_WEBHOOK_URL for this parameter'
    required: true
  dashboardUrl:
    description: 'Pass vars.CART_BUILD_DASHBOARD for this parameter'
    required: false
  slackBranches:
    description: 'Pass vars.DAILY_BUILD_BRANCHES for this parameter'
    required: true

runs:
  using: composite
  steps:
    - name: Normalize action path
      run: |
        echo "ACTION_PATH_NORMALIZED=$(echo "$GITHUB_ACTION_PATH" | sed 's|\\|/|g')" >> $GITHUB_ENV
      shell: bash

    # set job status based on dependent jobs so we can make this job a
    # required check
    - name: Check overall workflow status
      run: python ${{ env.ACTION_PATH_NORMALIZED }}/check-jobs.py
      env:
        JOBS: ${{ inputs.jobs }}
      shell: bash

    # send a message to #cart-build-alert slack channel on failure
    - name: Send Slack notification
      if: ${{ failure() && contains(inputs.slackBranches, github.ref_name) }}
      uses: euc-eng/slack-notification@v1
      with:
        status: 'failure'
        token: ${{ github.token }}
        notify_when: 'failure'
        workflow_id: ${{ inputs.workflowId }}
        build_type: ${{ inputs.buildtype }}
        notification_title: "Github workflow {workflow} {build_type} failed"
        footer: ${{ inputs.dashboardUrl != '' &&
                    format('<{0}/?target={1}&branch={2}|{1} build trending> for the past 7 days',
                           inputs.dashboardUrl, github.workflow, github.ref_name)
                    || '' }}
        detect_failure: true
      env:
        SLACK_WEBHOOK_URL: ${{ inputs.slackWebhookUrl }}
      continue-on-error: true

    - name: Trigger re-run failed jobs for tests on PR
      if: ${{ failure() && github.event_name == 'pull_request' }}
      uses: euc-eng/run-workflows@v1
      with:
        workflowNames: rerun-failed-jobs
        inputs: >
          runId=${{ github.run_id }}
          "ignoreJobFailures=build-*;*-overall-status"
