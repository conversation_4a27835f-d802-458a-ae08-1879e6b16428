/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */
#include "SystemTimeZoneExTest.h"
#include "rxUTLog.h"
#include "utMock.h"
#include "utilSystemTimeZone.h"
#include <gmock/gmock.h>
using namespace testing;

// Overload function pointer for pGetDTZI and mock the function
DWORD WINAPI
MockGetDTZI(PDYNAMIC_TIME_ZONE_INFORMATION *pdTimeZone)
{
   return ERROR_SUCCESS;
}

// Overload function pointer for pSetDTZI and mock the function
BOOL WINAPI
MockSetDTZI(PDYNAMIC_TIME_ZONE_INFORMATION *pdTimeZone)
{
   return true;
}

void
SystemTimeZoneExTest::SetUpTestCase()
{
   UTConsoleLog("%s", __FUNCTION__);
}


/*
 *-----------------------------------------------------------------------------
 *
 * SystemTimeZoneExTest::TearDownTestCase --
 *
 *      TearDown for the entire test suite.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

void
SystemTimeZoneExTest::TearDownTestCase()
{
   UTConsoleLog("%s", __FUNCTION__);
}


/*
 *-----------------------------------------------------------------------------
 *
 * SystemTimeZoneExTest::SetUp --
 *
 *      SetUp for the each test case.
 *      Create an object of type SystemTimeZone for testing.
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

void
SystemTimeZoneExTest::SetUp()
{
   UTConsoleLog("%s", __FUNCTION__);
}


/*
 *-----------------------------------------------------------------------------
 *
 * SystemTimeZoneExTest::TearDown --
 *
 *      TearDown for the each test case.
 *      Delete the object m_systemTimeZone.
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

void
SystemTimeZoneExTest::TearDown()
{
   UTConsoleLog("%s", __FUNCTION__);
}


/*
 *-----------------------------------------------------------------------------
 *
 * Test_SetTime_QueryRegFail
 *
 * Search the time zone in registry key
 *    RegOpenKeyEx Passed and RegQueryValueEx failed
 *
 * Results :
 *  SetTimeFromStandardName should return false
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(SystemTimeZoneExTest, Test_SetTime_QueryRegFail)
{
   // Search the timezone key in registry
   wstr sysTz = L"Software\\Microsoft\\Windows NT\\CurrentVersion\\Time Zones\\";
   wstr tzKey = L"PST Time Zone";
   wstr findKey = sysTz + tzKey;
   // Search the timezonekey been sent by client, got error.
   HKEY expectedRegKey = reinterpret_cast<HKEY>(0x0000000000000118);
   VMOCK(RegOpenKeyEx)
      .ExpectCall(_, StrEq(findKey.c_str()), _, _, _)
      .Times(1)
      .WillOnce(DoAll(Invoke([&expectedRegKey](HKEY, LPCWSTR, DWORD, REGSAM, HKEY *phk) {
                         *phk = expectedRegKey;
                      }),
                      Return(ERROR_SUCCESS)));

   VMOCK(RegQueryValueEx)
      .ExpectCall(_, _, _, _, _, _)
      .Times(1)
      .WillOnce(Return(ERROR_ACCESS_DENIED));

   VMOCK(RegCloseKey).ExpectCall(expectedRegKey).WillOnce(Return(ERROR_SUCCESS));

   SystemTimeZone systemTimeZone;
   EXPECT_FALSE(systemTimeZone.SetTimeFromStandardName(tzKey, false));
}


/*
 *-----------------------------------------------------------------------------
 *
 * Test_GetModuleFail_SetTzApiPass
 *
 *    Search the time zone in registry key
 *       RegOpenKeyEx Passed and RegQueryValueEx passed
 *
 *    GetModuleHandle returned NULL
 *
 *    SetTimeZoneInfomation API Pass
 *
 * Results :
 *    SetTimeFromStandardName should return true.
 *-----------------------------------------------------------------------------
 */

TEST_F(SystemTimeZoneExTest, Test_GetModuleFail_SetTzApiPass)
{
   SystemTimeZone systemTimeZone;
   wstr sysTz = L"Software\\Microsoft\\Windows NT\\CurrentVersion\\Time Zones\\";
   wstr tzKey = L"PST Time Zone";
   LPCWSTR expectedDltKey = L"DayLight PST Time Zone";
   wstr dltSubKey = L"Dlt";
   wstr tziSubKey = L"TZI";
   wstr findKey = sysTz + tzKey;
   SystemTimeZone::TZREG tzinfo = {0};
   int tzInfoSize = sizeof(tzinfo);
   HKEY expectedRegKey = reinterpret_cast<HKEY>(0x0000000000000118);

   VMOCK(RegOpenKeyEx)
      .ExpectCall(_, StrEq(findKey.c_str()), _, _, _)
      .Times(1)
      .WillOnce(DoAll(Invoke([&expectedRegKey](HKEY, LPCWSTR, DWORD, REGSAM, HKEY *phk) {
                         *phk = expectedRegKey;
                      }),
                      Return(ERROR_SUCCESS)));

   // Expect both the reg call to pass and return ERROR_SUCCESS
   VMOCK(RegQueryValueEx)
      .ExpectCall(_, StrEq(tziSubKey.c_str()), _, _, _, _)
      .WillOnce( // Get the TZInfo
         DoAll(
            Invoke([&tzInfoSize](HKEY, LPCWSTR, LPDWORD, LPDWORD lpType, LPBYTE, LPDWORD lpcbData) {
               *lpType = REG_BINARY;
               *lpcbData = tzInfoSize;
            }),
            Return(ERROR_SUCCESS)));
   VMOCK(RegQueryValueEx)
      .ExpectCall(_, StrEq(dltSubKey.c_str()), _, _, _, _)
      .WillOnce(DoAll( // Get the DLT key
         Invoke([&expectedDltKey](HKEY, LPCWSTR, LPDWORD, LPDWORD lpType, LPBYTE dltKey,
                                  LPDWORD pDataSize) {
            *lpType = REG_SZ;
            wcsncpy(reinterpret_cast<wchar_t *>(dltKey), expectedDltKey,
                    *pDataSize / sizeof(wchar_t));
            *pDataSize = static_cast<DWORD>(wcslen(expectedDltKey) * sizeof(wchar_t));
         }),
         Return(ERROR_SUCCESS)));

   VMOCK(RegCloseKey).ExpectCall(expectedRegKey).Times(1);

   // Mock get/setdynamictimezoneinfo functions
   HMODULE mockModule = reinterpret_cast<HMODULE>(NULL);
   const wstring kernel32 = L"kernel32.dll";
   const string getDynamicTz = "GetDynamicTimeZoneInformation";
   const string setDynamicTz = "SetDynamicTimeZoneInformation";

   VMOCK(GetModuleHandle).ExpectCall(StrEq(kernel32)).WillOnce(Return(mockModule));

   VMOCK(GetTimeZoneInformation).ExpectCall(_).Times(1).WillOnce(Return(TIME_ZONE_ID_STANDARD));

   VMOCK(SetTimeZoneInformation).ExpectCall(_).WillOnce(Return(true));

   EXPECT_TRUE(systemTimeZone.SetTimeFromStandardName(tzKey, false));
}


/*
 *-----------------------------------------------------------------------------
 *
 * Test_GetProcPass_SetDyTzApiPass
 *
 *    Search the time zone in registry key
 *       RegOpenKeyEx Passed and RegQueryValueEx passed
 *
 *    Get the function pointers for
 *       SetDynamicTimeZoneInfomation & GetDynamicTimeZoneInfomation fail
 *
 *    SetTimeZoneInfomation API Pass
 *
 * Results :
 *    SetTimeFromStandardName should return true.
 *-----------------------------------------------------------------------------
 */

TEST_F(SystemTimeZoneExTest, Test_GetProcFail_SetTzApiPass)
{
   SystemTimeZone systemTimeZone;
   wstr sysTz = L"Software\\Microsoft\\Windows NT\\CurrentVersion\\Time Zones\\";
   wstr tzKey = L"PST Time Zone";
   LPCWSTR expectedDltKey = L"DayLight PST Time Zone";
   wstr dltSubKey = L"Dlt";
   wstr tziSubKey = L"TZI";
   wstr findKey = sysTz + tzKey;
   SystemTimeZone::TZREG tzinfo = {0};
   int tzInfoSize = sizeof(tzinfo);

   HKEY expectedRegKey = reinterpret_cast<HKEY>(0x0000000000000118);
   VMOCK(RegOpenKeyEx)
      .ExpectCall(_, StrEq(findKey.c_str()), _, _, _)
      .Times(1)
      .WillOnce(DoAll(Invoke([&expectedRegKey](HKEY, LPCWSTR, DWORD, REGSAM, HKEY *phk) {
                         *phk = expectedRegKey;
                      }),
                      Return(ERROR_SUCCESS)));

   // Expect both the reg call to pass and return ERROR_SUCCESS
   VMOCK(RegQueryValueEx)
      .ExpectCall(_, StrEq(tziSubKey.c_str()), _, _, _, _)
      .WillOnce( // Get the TZInfo
         DoAll(
            Invoke([&tzInfoSize](HKEY, LPCWSTR, LPDWORD, LPDWORD lpType, LPBYTE, LPDWORD lpcbData) {
               *lpType = REG_BINARY;
               *lpcbData = tzInfoSize;
            }),
            Return(ERROR_SUCCESS)));

   VMOCK(RegQueryValueEx)
      .ExpectCall(_, StrEq(dltSubKey.c_str()), _, _, _, _)
      .WillOnce(DoAll( // Get the DLT key
         Invoke([&expectedDltKey](HKEY, LPCWSTR, LPDWORD, LPDWORD lpType, LPBYTE dltKey,
                                  LPDWORD pDataSize) {
            *lpType = REG_SZ;
            wcsncpy(reinterpret_cast<wchar_t *>(dltKey), expectedDltKey,
                    *pDataSize / sizeof(wchar_t));
            *pDataSize = static_cast<DWORD>(wcslen(expectedDltKey) * sizeof(wchar_t));
         }),
         Return(ERROR_SUCCESS)));

   VMOCK(RegCloseKey).ExpectCall(expectedRegKey).Times(1);

   // Mock get/setdynamictimezoneinfo functions
   HMODULE mockModule = reinterpret_cast<HMODULE>(0x12345678);
   const wstring kernel32 = L"kernel32.dll";
   const string getDynamicTz = "GetDynamicTimeZoneInformation";
   const string setDynamicTz = "SetDynamicTimeZoneInformation";

   VMOCK(GetModuleHandle).ExpectCall(StrEq(kernel32)).WillOnce(Return(mockModule));

   // GetProcAddress failed and returned nullptr
   VMOCK_V(getProcAddrMock, GetProcAddress)
      .ExpectCall(_, _)
      .WillRepeatedly([&](HMODULE hModule, LPCSTR lpProcName) {
         if (hModule == mockModule && lpProcName == getDynamicTz) {
            return ((FARPROC) nullptr);
         } else if (hModule == mockModule && lpProcName == setDynamicTz) {
            return ((FARPROC) nullptr);
         }
         return getProcAddrMock.CallRealFunc(hModule, lpProcName);
      });

   VMOCK(GetTimeZoneInformation).ExpectCall(_).Times(1).WillOnce(Return(TIME_ZONE_ID_STANDARD));

   VMOCK(SetTimeZoneInformation).ExpectCall(_).WillOnce(Return(true));

   EXPECT_TRUE(systemTimeZone.SetTimeFromStandardName(tzKey, false));
}


/*
 *-----------------------------------------------------------------------------
 *
 * Test_GetProcPass_SetDyTzApiPass
 *
 *     Search the time zone in registry key
 *       RegOpenKeyEx Passed and RegQueryValueEx passed
 *
 *     Get the function pointers for
 *       SetDynamicTimeZoneInfomation & GetDynamicTimeZoneInfomation
 *
 *     SetDynamicTimeZoneInfomation Passed
 *
 * Result :
 *
 *    SetTimeFromStandardName should return true.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(SystemTimeZoneExTest, Test_GetProcPass_SetDyTzApiPass)
{
   SystemTimeZone systemTimeZone;
   wstr sysTz = L"Software\\Microsoft\\Windows NT\\CurrentVersion\\Time Zones\\";
   wstr tzKey = L"PST Time Zone";
   LPCWSTR expectedDltKey = L"DayLight PST Time Zone";
   wstr dltSubKey = L"Dlt";
   wstr tziSubKey = L"TZI";
   wstr findKey = sysTz + tzKey;
   SystemTimeZone::TZREG tzinfo = {0};
   int tzInfoSize = sizeof(tzinfo);

   HKEY expectedRegKey = reinterpret_cast<HKEY>(0x0000000000000118);
   VMOCK(RegOpenKeyEx)
      .ExpectCall(_, StrEq(findKey.c_str()), _, _, _)
      .Times(1)
      .WillOnce(DoAll(Invoke([&expectedRegKey](HKEY, LPCWSTR, DWORD, REGSAM, HKEY *phk) {
                         *phk = expectedRegKey;
                      }),
                      Return(ERROR_SUCCESS)));

   // Expect both the reg call to pass and return ERROR_SUCCESS
   VMOCK(RegQueryValueEx)
      .ExpectCall(_, StrEq(tziSubKey.c_str()), _, _, _, _)
      .WillOnce( // Get the TZInfo
         DoAll(
            Invoke([&tzInfoSize](HKEY, LPCWSTR, LPDWORD, LPDWORD lpType, LPBYTE, LPDWORD lpcbData) {
               *lpType = REG_BINARY;
               *lpcbData = tzInfoSize;
            }),
            Return(ERROR_SUCCESS)));

   VMOCK(RegQueryValueEx)
      .ExpectCall(_, StrEq(dltSubKey.c_str()), _, _, _, _)
      .WillOnce(DoAll( // Get the DLT key
         Invoke([&expectedDltKey](HKEY, LPCWSTR, LPDWORD, LPDWORD lpType, LPBYTE dltKey,
                                  LPDWORD pDataSize) {
            *lpType = REG_SZ;
            wcsncpy(reinterpret_cast<wchar_t *>(dltKey), expectedDltKey,
                    *pDataSize / sizeof(wchar_t));
            *pDataSize = static_cast<DWORD>(wcslen(expectedDltKey) * sizeof(wchar_t));
         }),
         Return(ERROR_SUCCESS)));

   VMOCK(RegCloseKey).ExpectCall(expectedRegKey).Times(1);

   // Mock get/setdynamictimezoneinfo functions
   HMODULE mockModule = reinterpret_cast<HMODULE>(0x12345678);
   const wstring kernel32 = L"kernel32.dll";
   const string getDynamicTz = "GetDynamicTimeZoneInformation";
   const string setDynamicTz = "SetDynamicTimeZoneInformation";

   VMOCK(GetModuleHandle).ExpectCall(StrEq(kernel32)).WillOnce(Return(mockModule));

   VMOCK_V(getProcAddrMock, GetProcAddress)
      .ExpectCall(_, _)
      .WillRepeatedly([&](HMODULE hModule, LPCSTR lpProcName) {
         if (hModule == mockModule && lpProcName == getDynamicTz) {
            return ((FARPROC)(DWORD(*)(PDYNAMIC_TIME_ZONE_INFORMATION)) & MockGetDTZI);
         } else if (hModule == mockModule && lpProcName == setDynamicTz) {
            return ((FARPROC)(DWORD(*)(PDYNAMIC_TIME_ZONE_INFORMATION)) & MockSetDTZI);
         }
         return getProcAddrMock.CallRealFunc(hModule, lpProcName);
      });

   VMOCK(&MockSetDTZI).ExpectCall(_).Times(1).WillOnce(Return(true));

   // If SetDynamicTimeZoneInformation passes
   // below 2 apis should not get called.
   VMOCK(GetTimeZoneInformation).ExpectCall(_).Times(0);

   VMOCK(SetTimeZoneInformation).ExpectCall(_).Times(0);

   EXPECT_TRUE(systemTimeZone.SetTimeFromStandardName(tzKey, false));
}


/*
 *-----------------------------------------------------------------------------
 *
 * Test_GetProcPass_SetDyTzApiFail_SetTzApiPass
 *
 *     Search the time zone in registry key
 *       RegOpenKeyEx Passed and RegQueryValueEx passed
 *
 *     Get the function pointers for
 *       SetDynamicTimeZoneInfomation & GetDynamicTimeZoneInfomation
 *
 *     SetDynamicTimeZoneInfomation API Failed.
 *
 *     SetTimeZoneInformation Api passed.
 *
 * Results :
 *
 *     SetTimeFromStandardName should return true.
 *-----------------------------------------------------------------------------
 */

TEST_F(SystemTimeZoneExTest, Test_GetProcPass_SetDyTzApiFail_SetTzApiPass)
{
   SystemTimeZone systemTimeZone;
   wstr sysTz = L"Software\\Microsoft\\Windows NT\\CurrentVersion\\Time Zones\\";
   wstr tzKey = L"PST Time Zone";
   LPCWSTR expectedDltKey = L"DayLight PST Time Zone";
   wstr dltSubKey = L"Dlt";
   wstr tziSubKey = L"TZI";
   wstr findKey = sysTz + tzKey;
   SystemTimeZone::TZREG tzinfo = {0};
   int tzInfoSize = sizeof(tzinfo);
   HKEY expectedRegKey = reinterpret_cast<HKEY>(0x0000000000000118);

   VMOCK(RegOpenKeyEx)
      .ExpectCall(_, StrEq(findKey.c_str()), _, _, _)
      .Times(1)
      .WillOnce(DoAll(Invoke([&expectedRegKey](HKEY, LPCWSTR, DWORD, REGSAM, HKEY *phk) {
                         *phk = expectedRegKey;
                      }),
                      Return(ERROR_SUCCESS)));

   // Expect both the reg call to pass and return ERROR_SUCCESS
   VMOCK(RegQueryValueEx)
      .ExpectCall(_, StrEq(tziSubKey.c_str()), _, _, _, _)
      .WillOnce( // Get the TZInfo
         DoAll(
            Invoke([&tzInfoSize](HKEY, LPCWSTR, LPDWORD, LPDWORD lpType, LPBYTE, LPDWORD lpcbData) {
               *lpType = REG_BINARY;
               *lpcbData = tzInfoSize;
            }),
            Return(ERROR_SUCCESS)));

   VMOCK(RegQueryValueEx)
      .ExpectCall(_, StrEq(dltSubKey.c_str()), _, _, _, _)
      .WillOnce(DoAll( // Get the DLT key
         Invoke([&expectedDltKey](HKEY, LPCWSTR, LPDWORD, LPDWORD lpType, LPBYTE dltKey,
                                  LPDWORD pDataSize) {
            *lpType = REG_SZ;
            wcsncpy(reinterpret_cast<wchar_t *>(dltKey), expectedDltKey,
                    *pDataSize / sizeof(wchar_t));
            *pDataSize = static_cast<DWORD>(wcslen(expectedDltKey) * sizeof(wchar_t));
         }),
         Return(ERROR_SUCCESS)));

   VMOCK(RegCloseKey).ExpectCall(expectedRegKey).Times(1);

   // Mock get/setdynamictimezoneinfo functions
   HMODULE mockModule = reinterpret_cast<HMODULE>(0x12345678);
   const wstring kernel32 = L"kernel32.dll";
   const string getDynamicTz = "GetDynamicTimeZoneInformation";
   const string setDynamicTz = "SetDynamicTimeZoneInformation";

   VMOCK(GetModuleHandle).ExpectCall(StrEq(kernel32)).WillOnce(Return(mockModule));

   VMOCK_V(getProcAddrMock, GetProcAddress)
      .ExpectCall(_, _)
      .WillRepeatedly([&](HMODULE hModule, LPCSTR lpProcName) {
         if (hModule == mockModule && lpProcName == getDynamicTz) {
            return ((FARPROC)(DWORD(*)(PDYNAMIC_TIME_ZONE_INFORMATION)) & MockGetDTZI);
         } else if (hModule == mockModule && lpProcName == setDynamicTz) {
            return ((FARPROC)(DWORD(*)(PDYNAMIC_TIME_ZONE_INFORMATION)) & MockSetDTZI);
         }
         return getProcAddrMock.CallRealFunc(hModule, lpProcName);
      });

   VMOCK(&MockSetDTZI).ExpectCall(_).Times(1).WillOnce(Return(false));

   // If SetDynamicTimeZoneInformation fails
   // below 2 apis should get called.
   VMOCK(GetTimeZoneInformation).ExpectCall(_).Times(1).WillOnce(Return(TIME_ZONE_ID_STANDARD));

   VMOCK(SetTimeZoneInformation).ExpectCall(_).Times(1).WillOnce(Return(true));

   EXPECT_TRUE(systemTimeZone.SetTimeFromStandardName(tzKey, false));
}


/*
 *-----------------------------------------------------------------------------
 *
 * Test_GetProcPass_SetDyTzApiFail_SetTzApiFail
 *
 *     Search the time zone in registry key
 *       RegOpenKeyEx Passed and RegQueryValueEx passed
 *
 *     Get the function pointers for
 *       SetDynamicTimeZoneInfomation & GetDynamicTimeZoneInfomation
 *
 *     SetDynamicTimeZoneInfomation API Failed.
 *
 *     SetTimeZoneInformation Api Failed.
 *
 * Results :
 *
 *     SetTimeFromStandardName should return false.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(SystemTimeZoneExTest, Test_GetProcPass_SetDyTzApiFail_SetTzApiFail)
{
   SystemTimeZone systemTimeZone;
   wstr sysTz = L"Software\\Microsoft\\Windows NT\\CurrentVersion\\Time Zones\\";
   wstr tzKey = L"PST Time Zone";
   LPCWSTR expectedDltKey = L"DayLight PST Time Zone";
   wstr dltSubKey = L"Dlt";
   wstr tziSubKey = L"TZI";
   wstr findKey = sysTz + tzKey;
   SystemTimeZone::TZREG tzinfo = {0};
   int tzInfoSize = sizeof(tzinfo);

   HKEY expectedRegKey = reinterpret_cast<HKEY>(0x0000000000000118);
   VMOCK(RegOpenKeyEx)
      .ExpectCall(_, StrEq(findKey.c_str()), _, _, _)
      .Times(1)
      .WillOnce(DoAll(Invoke([&expectedRegKey](HKEY, LPCWSTR, DWORD, REGSAM, HKEY *phk) {
                         *phk = expectedRegKey;
                      }),
                      Return(ERROR_SUCCESS)));

   // Expect both the reg call to pass and return ERROR_SUCCESS
   VMOCK(RegQueryValueEx)
      .ExpectCall(_, StrEq(tziSubKey.c_str()), _, _, _, _)
      .WillOnce( // Get the TZInfo
         DoAll(
            Invoke([&tzInfoSize](HKEY, LPCWSTR, LPDWORD, LPDWORD lpType, LPBYTE, LPDWORD lpcbData) {
               *lpType = REG_BINARY;
               *lpcbData = tzInfoSize;
            }),
            Return(ERROR_SUCCESS)));

   VMOCK(RegQueryValueEx)
      .ExpectCall(_, StrEq(dltSubKey.c_str()), _, _, _, _)
      .WillOnce(DoAll( // Get the DLT key
         Invoke([&expectedDltKey](HKEY, LPCWSTR, LPDWORD, LPDWORD lpType, LPBYTE dltKey,
                                  LPDWORD pDataSize) {
            *lpType = REG_SZ;
            wcsncpy(reinterpret_cast<wchar_t *>(dltKey), expectedDltKey,
                    *pDataSize / sizeof(wchar_t));
            *pDataSize = static_cast<DWORD>(wcslen(expectedDltKey) * sizeof(wchar_t));
         }),
         Return(ERROR_SUCCESS)));

   VMOCK(RegCloseKey).ExpectCall(expectedRegKey).Times(1);

   // Mock get/setdynamictimezoneinfo functions
   HMODULE mockModule = reinterpret_cast<HMODULE>(0x12345678);
   const wstring kernel32 = L"kernel32.dll";
   const string getDynamicTz = "GetDynamicTimeZoneInformation";
   const string setDynamicTz = "SetDynamicTimeZoneInformation";

   VMOCK(GetModuleHandle).ExpectCall(StrEq(kernel32)).WillOnce(Return(mockModule));

   VMOCK_V(getProcAddrMock, GetProcAddress)
      .ExpectCall(_, _)
      .WillRepeatedly([&](HMODULE hModule, LPCSTR lpProcName) {
         if (hModule == mockModule && lpProcName == getDynamicTz) {
            return ((FARPROC)(DWORD(*)(PDYNAMIC_TIME_ZONE_INFORMATION)) & MockGetDTZI);
         } else if (hModule == mockModule && lpProcName == setDynamicTz) {
            return ((FARPROC)(DWORD(*)(PDYNAMIC_TIME_ZONE_INFORMATION)) & MockSetDTZI);
         }
         return getProcAddrMock.CallRealFunc(hModule, lpProcName);
      });

   VMOCK(&MockSetDTZI).ExpectCall(_).Times(1).WillOnce(Return(false));

   // If SetDynamicTimeZoneInformation failed
   // below 2 apis should get called.
   VMOCK(GetTimeZoneInformation).ExpectCall(_).Times(1).WillOnce(Return(TIME_ZONE_ID_STANDARD));

   VMOCK(SetTimeZoneInformation).ExpectCall(_).Times(1).WillOnce(Return(false));

   EXPECT_FALSE(systemTimeZone.SetTimeFromStandardName(tzKey, false));
}


/*
 *-----------------------------------------------------------------------------
 *
 * Test_GetValueFromStdMatch_TzApiFailed
 *
 *     Search the time zone in registry key
 *       RegOpenKeyEx Passed and RegQueryValueEx passed
 *
 *     GetModuleHandle Failed
 *
 *     Call GetTimeZoneInfomation failed
 *
 * Results:
 *
 *     SetTimeFromStandardName should return false
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(SystemTimeZoneExTest, Test_GetModuleHandleFail_GetTzApiFail)
{
   wstr sysTz = L"Software\\Microsoft\\Windows NT\\CurrentVersion\\Time Zones\\";
   wstr tzKey = L"PST Time Zone";
   LPCWSTR expectedDltKey = L"DayLight PST Time Zone";
   wstr dltSubKey = L"Dlt";
   wstr tziSubKey = L"TZI";
   wstr findKey = sysTz + tzKey;
   SystemTimeZone::TZREG tzinfo = {0};
   int tzInfoSize = sizeof(tzinfo);

   HKEY expectedRegKey = reinterpret_cast<HKEY>(0x0000000000000118);
   VMOCK(RegOpenKeyEx)
      .ExpectCall(_, StrEq(findKey.c_str()), _, _, _)
      .WillOnce(DoAll(Invoke([&expectedRegKey](HKEY, LPCWSTR, DWORD, REGSAM, HKEY *phk) {
                         *phk = expectedRegKey;
                      }),
                      Return(ERROR_SUCCESS)));

   // Expect both the reg call to pass and return ERROR_SUCCESS
   VMOCK(RegQueryValueEx)
      .ExpectCall(_, StrEq(tziSubKey.c_str()), _, _, _, _)
      .WillOnce(DoAll(
         Invoke([&tzInfoSize](HKEY, LPCWSTR, LPDWORD, LPDWORD lpType, LPBYTE, LPDWORD lpcbData) {
            *lpType = REG_BINARY;
            *lpcbData = tzInfoSize;
         }),
         Return(ERROR_SUCCESS)));

   VMOCK(RegQueryValueEx)
      .ExpectCall(_, StrEq(dltSubKey.c_str()), _, _, _, _)
      .WillOnce(DoAll(Invoke([&expectedDltKey](HKEY, LPCWSTR, LPDWORD, LPDWORD lpType,
                                               LPBYTE dltKey, LPDWORD pDataSize) {
                         *lpType = REG_SZ;
                         wcsncpy(reinterpret_cast<wchar_t *>(dltKey), expectedDltKey,
                                 *pDataSize / sizeof(wchar_t));
                         *pDataSize = static_cast<DWORD>(wcslen(expectedDltKey) * sizeof(wchar_t));
                      }),
                      Return(ERROR_SUCCESS)));

   VMOCK(RegCloseKey).ExpectCall(expectedRegKey).Times(1);

   // Mock get/setdynamictimezoneinfo functions
   HMODULE mockModule = reinterpret_cast<HMODULE>(NULL);
   const wstring kernel32 = L"kernel32.dll";

   VMOCK(GetModuleHandle).ExpectCall(StrEq(kernel32)).WillOnce(Return(mockModule));

   VMOCK(GetTimeZoneInformation).ExpectCall(_).Times(1).WillOnce(Return(TIME_ZONE_ID_INVALID));

   // If GetTimeZoneInformation fails
   // SetTimeZoneInformation should not get called
   VMOCK(SetTimeZoneInformation).Times(0);

   SystemTimeZone systemTimeZone;
   EXPECT_FALSE(systemTimeZone.SetTimeFromStandardName(tzKey, false));
}


/*
 *-----------------------------------------------------------------------------
 *
 * Test_GetValueFromStdMatch_DyTzApiPassed
 *
 *     Search the time zone in registry key
 *       RegOpenKeyEx Passed and RegQueryValueEx failed
 *
 *     Search the std match in registry now
 *       Say we found a match with in 0th index
 *
 *     Call SetDynamicTimeZoneInfomation API passed
 *
 * Results :
 *
 *     SetTimeZoneInfomation should return true.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(SystemTimeZoneExTest, Test_GetValueFromStdMatch_DyTzApiPassed)
{
   wstr sysTz = L"Software\\Microsoft\\Windows NT\\CurrentVersion\\Time Zones\\";
   wstr tzKey = L"PST Time Zone";
   LPCWSTR expectedDltKey = L"DayLight PST Time Zone";
   wstr dltSubKey = L"Dlt";
   wstr tziSubKey = L"TZI";
   wstr tzStdSubKey = L"Std";
   wstr findKey = sysTz + tzKey;
   SystemTimeZone::TZREG tzinfo = {0};
   int tzInfoSize = sizeof(tzinfo);

   /* If timezone send by Client is not found in registry
    * enumerate every subkey inside the main key
    * Say in the first position itself we found a match
    * in that case return the match
    * Note  : match is case insensitive.
    */
   wstr findTimeZone = tzKey;
   wstr foundStdTimeZone = L"pst time zone"; // Match ignore case

   DWORD maxKeyLen = 128;
   DWORD noOfKeys = 10;

   HKEY hIterKey = reinterpret_cast<HKEY>(0x0000000000000119);
   HKEY hStdSubKey = reinterpret_cast<HKEY>(0x000000000000120);

   // Open the root key for timezone
   VMOCK(RegOpenKeyEx)
      .ExpectCall(HKEY_LOCAL_MACHINE, _, 0, KEY_READ, _)
      /* RegOpenKeyEx Inside FindTimeZoneInRegistry failed */
      .WillOnce(Return(ERROR_ACCESS_DENIED))
      .WillOnce(
         DoAll(Invoke([&hIterKey](HKEY, LPCWSTR, DWORD, REGSAM, HKEY *phk) { *phk = hIterKey; }),
               Return(ERROR_SUCCESS)))
      .WillOnce(DoAll(
         Invoke([&hStdSubKey](HKEY, LPCWSTR, DWORD, REGSAM, HKEY *phk) { *phk = hStdSubKey; }),
         Return(ERROR_SUCCESS)));

   // Query number of keys under timezone and max length for that key
   VMOCK(RegQueryInfoKey)
      .ExpectCall(hIterKey, NULL, NULL, NULL, _, _, NULL, NULL, NULL, NULL, NULL, NULL)
      .WillOnce(
         DoAll(Invoke([maxKeyLen, noOfKeys](HKEY, LPWSTR, LPDWORD, LPDWORD, LPDWORD lpcSubKeys,
                                            LPDWORD lpcbMaxSubKeyLen, LPDWORD, LPDWORD, LPDWORD,
                                            LPDWORD, LPDWORD, PFILETIME) {
                  *lpcSubKeys = noOfKeys;
                  *lpcbMaxSubKeyLen = maxKeyLen;
               }),
               Return(ERROR_SUCCESS)));

   // Return 0th subKeyName once Enum starts
   VMOCK(RegEnumKeyEx)
      .ExpectCall(hIterKey, _, _, _ /* output subkeyName */, NULL, NULL, NULL, NULL)
      .WillOnce(
         DoAll(Invoke([findTimeZone](HKEY, DWORD, LPWSTR lpName, LPDWORD, LPDWORD, LPWSTR, LPDWORD,
                                     PFILETIME) { wcscpy(lpName, findTimeZone.c_str()); }),
               Return(ERROR_SUCCESS)));

   // For std key match
   VMOCK(RegQueryValueEx)
      .ExpectCall(_, StrEq(tzStdSubKey.c_str()), _, _, _, _)
      .WillOnce(DoAll(Invoke([&foundStdTimeZone](HKEY, LPCWSTR, LPDWORD, LPDWORD lpType,
                                                 LPBYTE dltKey, LPDWORD pDataSize) {
                         *lpType = REG_SZ;
                         wcsncpy(reinterpret_cast<wchar_t *>(dltKey), foundStdTimeZone,
                                 *pDataSize / sizeof(wchar_t));
                         *pDataSize =
                            static_cast<DWORD>(wcslen(foundStdTimeZone) * sizeof(wchar_t));
                      }),
                      Return(ERROR_SUCCESS)));

   // For TzInfo
   VMOCK(RegQueryValueEx)
      .ExpectCall(_, StrEq(tziSubKey.c_str()), _, _, _, _)
      .WillOnce(DoAll(
         Invoke([&tzInfoSize](HKEY, LPCWSTR, LPDWORD, LPDWORD lpType, LPBYTE, LPDWORD lpcbData) {
            *lpType = REG_BINARY;
            *lpcbData = tzInfoSize;
         }),
         Return(ERROR_SUCCESS)));

   // For DltKey
   VMOCK(RegQueryValueEx)
      .ExpectCall(_, StrEq(dltSubKey.c_str()), _, _, _, _)
      .WillOnce(DoAll(Invoke([&expectedDltKey](HKEY, LPCWSTR, LPDWORD, LPDWORD lpType,
                                               LPBYTE dltKey, LPDWORD pDataSize) {
                         *lpType = REG_SZ;
                         wcsncpy(reinterpret_cast<wchar_t *>(dltKey), expectedDltKey,
                                 *pDataSize / sizeof(wchar_t));
                         *pDataSize = static_cast<DWORD>(wcslen(expectedDltKey) * sizeof(wchar_t));
                      }),
                      Return(ERROR_SUCCESS)));

   VMOCK(RegCloseKey).ExpectCall(hStdSubKey).WillOnce(Return(ERROR_SUCCESS));
   VMOCK(RegCloseKey).ExpectCall(hIterKey).WillOnce(Return(ERROR_SUCCESS));

   // Mock get/setdynamictimezoneinfo functions
   HMODULE mockModule = reinterpret_cast<HMODULE>(0x12345678);
   const wstring kernel32 = L"kernel32.dll";
   const string getDynamicTz = "GetDynamicTimeZoneInformation";
   const string setDynamicTz = "SetDynamicTimeZoneInformation";

   VMOCK(GetModuleHandle).ExpectCall(StrEq(kernel32)).WillOnce(Return(mockModule));

   VMOCK_V(getProcAddrMock, GetProcAddress)
      .ExpectCall(_, _)
      .WillRepeatedly([&](HMODULE hModule, LPCSTR lpProcName) {
         if (hModule == mockModule && lpProcName == getDynamicTz) {
            return ((FARPROC)(DWORD(*)(PDYNAMIC_TIME_ZONE_INFORMATION)) & MockGetDTZI);
         } else if (hModule == mockModule && lpProcName == setDynamicTz) {
            return ((FARPROC)MockSetDTZI);
         }
         return getProcAddrMock.CallRealFunc(hModule, lpProcName);
      });

   VMOCK(&MockSetDTZI).ExpectCall(_).WillOnce(Return(true));

   VMOCK(GetTimeZoneInformation).Times(0);

   VMOCK(SetTimeZoneInformation).Times(0);

   SystemTimeZone systemTimeZone;
   EXPECT_TRUE(systemTimeZone.SetTimeFromStandardName(tzKey, false));
}


/*
 *-----------------------------------------------------------------------------
 *
 * Test_GetValueFromStdMatch_TzApiFailed
 *
 *     Search the time zone in registry key
 *       RegOpenKeyEx Passed and RegQueryValueEx failed
 *
 *     Search the std match in registry now
 *       Say we found a match with in 0th index
 *
 *     Call SetDynamicTimeZoneInfomation API
 *        it failed.
 *
 *     Call SetTimeZoneInfomation API
 *        it passed.
 *
 * Results :
 *
 *     SetTimeZoneInfomation should return true
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(SystemTimeZoneExTest, Test_GetValueFromStdMatch_TzApiFailed)
{
   wstr sysTz = L"Software\\Microsoft\\Windows NT\\CurrentVersion\\Time Zones\\";
   wstr tzKey = L"PST Time Zone";
   LPCWSTR expectedDltKey = L"DayLight PST Time Zone";
   wstr dltSubKey = L"Dlt";
   wstr tziSubKey = L"TZI";
   wstr tzStdSubKey = L"Std";
   wstr findKey = sysTz + tzKey;
   SystemTimeZone::TZREG tzinfo = {0};
   int tzInfoSize = sizeof(tzinfo);

   /* If timezone send by Client is not found in registry
    * enumerate every subkey inside the main key
    * Say in the first position itself we found a match
    * in that case return the match
    * Note  : match is case insensitive.
    */
   wstr findTimeZone = tzKey;
   wstr foundStdTimeZone = L"pst time zone"; // Match ignore case

   DWORD maxKeyLen = 128;
   DWORD noOfKeys = 10;

   HKEY hIterKey = reinterpret_cast<HKEY>(0x0000000000000119);
   HKEY hStdSubKey = reinterpret_cast<HKEY>(0x000000000000120);

   // Open the root key for timezone
   VMOCK(RegOpenKeyEx)
      .ExpectCall(HKEY_LOCAL_MACHINE, _, 0, KEY_READ, _)
      /* RegOpenKeyEx Inside FindTimeZoneInRegistry failed */
      .WillOnce(Return(ERROR_ACCESS_DENIED))
      .WillOnce(
         DoAll(Invoke([&hIterKey](HKEY, LPCWSTR, DWORD, REGSAM, HKEY *phk) { *phk = hIterKey; }),
               Return(ERROR_SUCCESS)))
      .WillOnce(DoAll(
         Invoke([&hStdSubKey](HKEY, LPCWSTR, DWORD, REGSAM, HKEY *phk) { *phk = hStdSubKey; }),
         Return(ERROR_SUCCESS)));

   // Query number of keys under timezone and max length for that key
   VMOCK(RegQueryInfoKey)
      .ExpectCall(hIterKey, NULL, NULL, NULL, _, _, NULL, NULL, NULL, NULL, NULL, NULL)
      .WillOnce(
         DoAll(Invoke([maxKeyLen, noOfKeys](HKEY, LPWSTR, LPDWORD, LPDWORD, LPDWORD lpcSubKeys,
                                            LPDWORD lpcbMaxSubKeyLen, LPDWORD, LPDWORD, LPDWORD,
                                            LPDWORD, LPDWORD, PFILETIME) {
                  *lpcSubKeys = noOfKeys;
                  *lpcbMaxSubKeyLen = maxKeyLen;
               }),
               Return(ERROR_SUCCESS)));

   // Return 0th subKeyName once Enum starts
   VMOCK(RegEnumKeyEx)
      .ExpectCall(hIterKey, _, _, _ /* output subkeyName */, NULL, NULL, NULL, NULL)
      .WillOnce(
         DoAll(Invoke([findTimeZone](HKEY, DWORD, LPWSTR lpName, LPDWORD, LPDWORD, LPWSTR, LPDWORD,
                                     PFILETIME) { wcscpy(lpName, findTimeZone.c_str()); }),
               Return(ERROR_SUCCESS)));

   // For std key match
   VMOCK(RegQueryValueEx)
      .ExpectCall(_, StrEq(tzStdSubKey.c_str()), _, _, _, _)
      .WillOnce(DoAll(Invoke([&foundStdTimeZone](HKEY, LPCWSTR, LPDWORD, LPDWORD lpType,
                                                 LPBYTE dltKey, LPDWORD pDataSize) {
                         *lpType = REG_SZ;
                         wcsncpy(reinterpret_cast<wchar_t *>(dltKey), foundStdTimeZone,
                                 *pDataSize / sizeof(wchar_t));
                         *pDataSize =
                            static_cast<DWORD>(wcslen(foundStdTimeZone) * sizeof(wchar_t));
                      }),
                      Return(ERROR_SUCCESS)));

   // For TzInfo
   VMOCK(RegQueryValueEx)
      .ExpectCall(_, StrEq(tziSubKey.c_str()), _, _, _, _)
      .WillOnce(DoAll(
         Invoke([&tzInfoSize](HKEY, LPCWSTR, LPDWORD, LPDWORD lpType, LPBYTE, LPDWORD lpcbData) {
            *lpType = REG_BINARY;
            *lpcbData = tzInfoSize;
         }),
         Return(ERROR_SUCCESS)));

   // For DltKey
   VMOCK(RegQueryValueEx)
      .ExpectCall(_, StrEq(dltSubKey.c_str()), _, _, _, _)
      .WillOnce(DoAll(Invoke([&expectedDltKey](HKEY, LPCWSTR, LPDWORD, LPDWORD lpType,
                                               LPBYTE dltKey, LPDWORD pDataSize) {
                         *lpType = REG_SZ;
                         wcsncpy(reinterpret_cast<wchar_t *>(dltKey), expectedDltKey,
                                 *pDataSize / sizeof(wchar_t));
                         *pDataSize = static_cast<DWORD>(wcslen(expectedDltKey) * sizeof(wchar_t));
                      }),
                      Return(ERROR_SUCCESS)));

   VMOCK(RegCloseKey).ExpectCall(hStdSubKey).WillOnce(Return(ERROR_SUCCESS));
   VMOCK(RegCloseKey).ExpectCall(hIterKey).WillOnce(Return(ERROR_SUCCESS));

   // Mock get/setdynamictimezoneinfo functions
   HMODULE mockModule = reinterpret_cast<HMODULE>(0x12345678);
   const wstring kernel32 = L"kernel32.dll";
   const string getDynamicTz = "GetDynamicTimeZoneInformation";
   const string setDynamicTz = "SetDynamicTimeZoneInformation";

   VMOCK(GetModuleHandle).ExpectCall(StrEq(kernel32)).WillOnce(Return(mockModule));

   VMOCK_V(getProcAddrMock, GetProcAddress)
      .ExpectCall(_, _)
      .WillRepeatedly([&](HMODULE hModule, LPCSTR lpProcName) {
         if (hModule == mockModule && lpProcName == getDynamicTz) {
            return ((FARPROC)(DWORD(*)(PDYNAMIC_TIME_ZONE_INFORMATION)) & MockGetDTZI);
         } else if (hModule == mockModule && lpProcName == setDynamicTz) {
            return ((FARPROC) nullptr);
         }
         return getProcAddrMock.CallRealFunc(hModule, lpProcName);
      });

   VMOCK(GetTimeZoneInformation).ExpectCall(_).Times(1).WillOnce(Return(TIME_ZONE_ID_STANDARD));

   VMOCK(SetTimeZoneInformation).ExpectCall(_).Times(1).WillOnce(Return(true));

   SystemTimeZone systemTimeZone;
   EXPECT_TRUE(systemTimeZone.SetTimeFromStandardName(tzKey, false));
}


/*
 *-----------------------------------------------------------------------------
 *
 * Test_GetValueFromStdMatch_TzApiFailed
 *
 *     Search the time zone in registry key
 *       RegOpenKeyEx Passed and RegQueryValueEx failed
 *
 *     Search the std match in registry now
 *       Unable to find a match
 *
 * Results:
 *
 *    SetTimeFromStandardName should return false
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(SystemTimeZoneExTest, Test_GetValueFromStdMatchNotFound)
{
   wstr sysTz = L"Software\\Microsoft\\Windows NT\\CurrentVersion\\Time Zones\\";
   wstr tzKey = L"PST Time Zone";
   LPCWSTR expectedDltKey = L"DayLight PST Time Zone";
   wstr dltSubKey = L"Dlt";
   wstr tziSubKey = L"TZI";
   wstr tzStdSubKey = L"Std";
   wstr findKey = sysTz + tzKey;
   SystemTimeZone::TZREG tzinfo = {0};
   int tzInfoSize = sizeof(tzinfo);
   wstr findTimeZone = tzKey;
   wstr foundStdTimeZone = L"ET time zone";

   DWORD maxKeyLen = 128;
   DWORD noOfKeys = 10;

   HKEY hIterKey = reinterpret_cast<HKEY>(0x0000000000000119);
   HKEY hStdSubKey = reinterpret_cast<HKEY>(0x000000000000120);

   // Open the root key for timezone
   VMOCK(RegOpenKeyEx)
      .ExpectCall(HKEY_LOCAL_MACHINE, _, 0, KEY_READ, _)
      /* RegOpenKeyEx Inside FindTimeZoneInRegistry failed */
      .WillOnce(Return(ERROR_ACCESS_DENIED))
      .WillOnce(
         DoAll(Invoke([&hIterKey](HKEY, LPCWSTR, DWORD, REGSAM, HKEY *phk) { *phk = hIterKey; }),
               Return(ERROR_SUCCESS)))
      .WillRepeatedly(DoAll(
         Invoke([&hStdSubKey](HKEY, LPCWSTR, DWORD, REGSAM, HKEY *phk) { *phk = hStdSubKey; }),
         Return(ERROR_SUCCESS)));

   // Query number of keys under timezone and max length for that key
   VMOCK(RegQueryInfoKey)
      .ExpectCall(hIterKey, NULL, NULL, NULL, _, _, NULL, NULL, NULL, NULL, NULL, NULL)
      .WillOnce(
         DoAll(Invoke([maxKeyLen, noOfKeys](HKEY, LPWSTR, LPDWORD, LPDWORD, LPDWORD lpcSubKeys,
                                            LPDWORD lpcbMaxSubKeyLen, LPDWORD, LPDWORD, LPDWORD,
                                            LPDWORD, LPDWORD, PFILETIME) {
                  *lpcSubKeys = noOfKeys;
                  *lpcbMaxSubKeyLen = maxKeyLen;
               }),
               Return(ERROR_SUCCESS)));

   // Return 0th subKeyName once Enum starts
   VMOCK(RegEnumKeyEx)
      .ExpectCall(hIterKey, _, _, _ /* output subkeyName */, NULL, NULL, NULL, NULL)
      .Times(noOfKeys)
      .WillRepeatedly(
         DoAll(Invoke([findTimeZone](HKEY, DWORD, LPWSTR lpName, LPDWORD, LPDWORD, LPWSTR, LPDWORD,
                                     PFILETIME) { wcscpy(lpName, findTimeZone.c_str()); }),
               Return(ERROR_SUCCESS)));

   /* Read std key */
   VMOCK(RegQueryValueEx)
      .ExpectCall(_, StrEq(tzStdSubKey.c_str()), _, _, _, _)
      .WillRepeatedly(DoAll(Invoke([&foundStdTimeZone](HKEY, LPCWSTR, LPDWORD, LPDWORD lpType,
                                                       LPBYTE dltKey, LPDWORD pDataSize) {
                               *lpType = REG_SZ;
                               wcsncpy(reinterpret_cast<wchar_t *>(dltKey), foundStdTimeZone,
                                       *pDataSize / sizeof(wchar_t));
                               *pDataSize =
                                  static_cast<DWORD>(wcslen(foundStdTimeZone) * sizeof(wchar_t));
                            }),
                            Return(ERROR_SUCCESS)));

   VMOCK_V(RegCloseKeyMock, RegCloseKey)
      .ExpectCall(_)
      .WillRepeatedly([&hStdSubKey, &hIterKey, &RegCloseKeyMock](HKEY hKey) {
         if (hKey == hStdSubKey) {
            return ERROR_SUCCESS;
         } else if (hKey == hIterKey) {
            return ERROR_SUCCESS;
         }
         return RegCloseKeyMock.CallRealFunc(hKey);
      });

   // As no match found in registry below apis will never get called.
   VMOCK(GetModuleHandle).Times(0);

   VMOCK(GetTimeZoneInformation).Times(0);

   VMOCK(SetTimeZoneInformation).Times(0);

   // SetTimeFromStandardName will fail as no match found in registry
   SystemTimeZone systemTimeZone;
   EXPECT_FALSE(systemTimeZone.SetTimeFromStandardName(tzKey, false));
}


/*
 *-----------------------------------------------------------------------------
 *
 * Test_InvalidOffsetFormat
 *
 *     Invalid offset paassed to SetTimeFromOffset
 *     it will return false.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(SystemTimeZoneExTest, Test_InvalidOffsetFormat)
{
   // As offset is invalid below apis will never get called.
   VMOCK(GetModuleHandle).Times(0);

   VMOCK(GetTimeZoneInformation).Times(0);

   VMOCK(SetTimeZoneInformation).Times(0);

   SystemTimeZone systemTimeZone;
   EXPECT_FALSE(systemTimeZone.SetTimeFromOffset(L""));
   EXPECT_FALSE(systemTimeZone.SetTimeFromOffset(L"    "));
   EXPECT_FALSE(systemTimeZone.SetTimeFromOffset(L"00"));
   EXPECT_FALSE(systemTimeZone.SetTimeFromOffset(L"00:00"));
   EXPECT_FALSE(systemTimeZone.SetTimeFromOffset(L"00:10:10"));
   EXPECT_FALSE(systemTimeZone.SetTimeFromOffset(L"ab:cd"));
   EXPECT_FALSE(systemTimeZone.SetTimeFromOffset(L"abcd"));
   EXPECT_FALSE(systemTimeZone.SetTimeFromOffset(L"-----"));
   EXPECT_FALSE(systemTimeZone.SetTimeFromOffset(L"::"));
   EXPECT_FALSE(systemTimeZone.SetTimeFromOffset(L"-+"));
   EXPECT_FALSE(systemTimeZone.SetTimeFromOffset(L":"));
}


/*
 *-----------------------------------------------------------------------------
 *
 * Test_ValidOffsetFormat
 *
 *     Valid offset paassed to SetTimeFromOffset, and system offset is same.
 *     There is no need to update the time.
 *     it will return false.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(SystemTimeZoneExTest, Test_ValidOffsetFormat_UpdateNotNeeded)
{
   // Valid offset passed , but as time is same, update not needed.
   LONG tzBias = -90;
   VMOCK(GetTimeZoneInformation)
      .ExpectCall(_)
      .Times(1)
      .WillOnce(DoAll(Invoke([tzBias](TIME_ZONE_INFORMATION *info) { info->Bias = tzBias; }),
                      Return(TIME_ZONE_ID_STANDARD)));

   SystemTimeZone systemTimeZone;
   EXPECT_FALSE(systemTimeZone.SetTimeFromOffset(L"1:30"));
}


/*
 *-----------------------------------------------------------------------------
 *
 * Test_ValidOffsetFormat
 *
 *     Valid offset paassed to SetTimeFromOffset, and system offset is same.
 *     There is no need to update the time.
 *     it will return false.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(SystemTimeZoneExTest, Test_ValidOffsetFormat_UpdateNeeded)
{
   // Valid offset passed , but as time is same, update not needed.
   LONG tzBias = -60;
   VMOCK(GetTimeZoneInformation)
      .ExpectCall(_)
      .Times(1)
      .WillOnce(DoAll(Invoke([tzBias](TIME_ZONE_INFORMATION *info) { info->Bias = tzBias; }),
                      Return(TIME_ZONE_ID_STANDARD)));

   // Mock get/setdynamictimezoneinfo functions
   HMODULE mockModule = reinterpret_cast<HMODULE>(0x12345678);
   const wstring kernel32 = L"kernel32.dll";
   const string getDynamicTz = "GetDynamicTimeZoneInformation";
   const string setDynamicTz = "SetDynamicTimeZoneInformation";

   VMOCK(GetModuleHandle).ExpectCall(StrEq(kernel32)).WillOnce(Return(mockModule));

   VMOCK_V(getProcAddrMock, GetProcAddress)
      .ExpectCall(_, _)
      .WillRepeatedly([&](HMODULE hModule, LPCSTR lpProcName) {
         if (hModule == mockModule && lpProcName == getDynamicTz) {
            return ((FARPROC)(DWORD(*)(PDYNAMIC_TIME_ZONE_INFORMATION)) & MockGetDTZI);
         } else if (hModule == mockModule && lpProcName == setDynamicTz) {
            return ((FARPROC)(DWORD(*)(PDYNAMIC_TIME_ZONE_INFORMATION)) & MockSetDTZI);
         }
         return getProcAddrMock.CallRealFunc(hModule, lpProcName);
      });

   SystemTimeZone systemTimeZone;
   EXPECT_TRUE(systemTimeZone.SetTimeFromOffset(L"1:30"));
}
