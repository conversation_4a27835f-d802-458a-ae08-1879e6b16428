/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

#include <d3d11.h>

class MockD3D11DeviceContext;

class MockD3D11Device : public ID3D11Device {
public:
   MockD3D11Device();

   ULONG STDMETHODCALLTYPE AddRef() override;
   ULONG STDMETHODCALLTYPE Release() override;
   HRESULT STDMETHODCALLTYPE QueryInterface(REFIID riid, void **ppvObject) override;

   // ID3D11Device (all methods stubbed)
   HRESULT STDMETHODCALLTYPE CreateBuffer(const D3D11_BUFFER_DESC *, const D3D11_SUBRESOURCE_DATA *,
                                          ID3D11Buffer **) override
   {
      return E_NOTIMPL;
   }
   HRESULT STDMETHODCALLTYPE CreateTexture1D(const D3D11_TEXTURE1D_DESC *,
                                             const D3D11_SUBRESOURCE_DATA *,
                                             ID3D11Texture1D **) override
   {
      return E_NOTIMPL;
   }
   HRESULT STDMETHODCALLTYPE CreateTexture2D(const D3D11_TEXTURE2D_DESC *,
                                             const D3D11_SUBRESOURCE_DATA *,
                                             ID3D11Texture2D **) override;
   HRESULT STDMETHODCALLTYPE CreateTexture3D(const D3D11_TEXTURE3D_DESC *,
                                             const D3D11_SUBRESOURCE_DATA *,
                                             ID3D11Texture3D **) override
   {
      return E_NOTIMPL;
   }
   HRESULT STDMETHODCALLTYPE CreateShaderResourceView(ID3D11Resource *,
                                                      const D3D11_SHADER_RESOURCE_VIEW_DESC *,
                                                      ID3D11ShaderResourceView **) override
   {
      return E_NOTIMPL;
   }
   HRESULT STDMETHODCALLTYPE CreateUnorderedAccessView(ID3D11Resource *,
                                                       const D3D11_UNORDERED_ACCESS_VIEW_DESC *,
                                                       ID3D11UnorderedAccessView **) override
   {
      return E_NOTIMPL;
   }
   HRESULT STDMETHODCALLTYPE CreateRenderTargetView(ID3D11Resource *,
                                                    const D3D11_RENDER_TARGET_VIEW_DESC *,
                                                    ID3D11RenderTargetView **) override
   {
      return E_NOTIMPL;
   }
   HRESULT STDMETHODCALLTYPE CreateDepthStencilView(ID3D11Resource *,
                                                    const D3D11_DEPTH_STENCIL_VIEW_DESC *,
                                                    ID3D11DepthStencilView **) override
   {
      return E_NOTIMPL;
   }
   HRESULT STDMETHODCALLTYPE CreateInputLayout(const D3D11_INPUT_ELEMENT_DESC *, UINT, const void *,
                                               SIZE_T, ID3D11InputLayout **) override
   {
      return E_NOTIMPL;
   }
   HRESULT STDMETHODCALLTYPE CreateVertexShader(const void *, SIZE_T, ID3D11ClassLinkage *,
                                                ID3D11VertexShader **) override
   {
      return E_NOTIMPL;
   }
   HRESULT STDMETHODCALLTYPE CreateGeometryShader(const void *, SIZE_T, ID3D11ClassLinkage *,
                                                  ID3D11GeometryShader **) override
   {
      return E_NOTIMPL;
   }
   HRESULT STDMETHODCALLTYPE CreateGeometryShaderWithStreamOutput(
      const void *, SIZE_T, const D3D11_SO_DECLARATION_ENTRY *, UINT, const UINT *, UINT, UINT,
      ID3D11ClassLinkage *, ID3D11GeometryShader **) override
   {
      return E_NOTIMPL;
   }
   HRESULT STDMETHODCALLTYPE CreatePixelShader(const void *, SIZE_T, ID3D11ClassLinkage *,
                                               ID3D11PixelShader **) override
   {
      return E_NOTIMPL;
   }
   HRESULT STDMETHODCALLTYPE CreateHullShader(const void *, SIZE_T, ID3D11ClassLinkage *,
                                              ID3D11HullShader **) override
   {
      return E_NOTIMPL;
   }
   HRESULT STDMETHODCALLTYPE CreateDomainShader(const void *, SIZE_T, ID3D11ClassLinkage *,
                                                ID3D11DomainShader **) override
   {
      return E_NOTIMPL;
   }
   HRESULT STDMETHODCALLTYPE CreateComputeShader(const void *, SIZE_T, ID3D11ClassLinkage *,
                                                 ID3D11ComputeShader **) override
   {
      return E_NOTIMPL;
   }
   HRESULT STDMETHODCALLTYPE CreateClassLinkage(ID3D11ClassLinkage **) override
   {
      return E_NOTIMPL;
   }
   HRESULT STDMETHODCALLTYPE CreateBlendState(const D3D11_BLEND_DESC *,
                                              ID3D11BlendState **) override
   {
      return E_NOTIMPL;
   }
   HRESULT STDMETHODCALLTYPE CreateDepthStencilState(const D3D11_DEPTH_STENCIL_DESC *,
                                                     ID3D11DepthStencilState **) override
   {
      return E_NOTIMPL;
   }
   HRESULT STDMETHODCALLTYPE CreateRasterizerState(const D3D11_RASTERIZER_DESC *,
                                                   ID3D11RasterizerState **) override
   {
      return E_NOTIMPL;
   }
   HRESULT STDMETHODCALLTYPE CreateSamplerState(const D3D11_SAMPLER_DESC *,
                                                ID3D11SamplerState **) override
   {
      return E_NOTIMPL;
   }
   HRESULT STDMETHODCALLTYPE CreateQuery(const D3D11_QUERY_DESC *, ID3D11Query **) override
   {
      return E_NOTIMPL;
   }
   HRESULT STDMETHODCALLTYPE CreatePredicate(const D3D11_QUERY_DESC *, ID3D11Predicate **) override
   {
      return E_NOTIMPL;
   }
   HRESULT STDMETHODCALLTYPE CreateCounter(const D3D11_COUNTER_DESC *, ID3D11Counter **) override
   {
      return E_NOTIMPL;
   }
   HRESULT STDMETHODCALLTYPE CreateDeferredContext(UINT, ID3D11DeviceContext **) override
   {
      return E_NOTIMPL;
   }
   HRESULT STDMETHODCALLTYPE OpenSharedResource(HANDLE, REFIID, void **) override
   {
      return E_NOTIMPL;
   }
   HRESULT STDMETHODCALLTYPE CheckFormatSupport(DXGI_FORMAT, UINT *) override { return E_NOTIMPL; }
   HRESULT STDMETHODCALLTYPE CheckMultisampleQualityLevels(DXGI_FORMAT, UINT, UINT *) override
   {
      return E_NOTIMPL;
   }
   void STDMETHODCALLTYPE CheckCounterInfo(D3D11_COUNTER_INFO *) override {}
   HRESULT STDMETHODCALLTYPE CheckCounter(const D3D11_COUNTER_DESC *, D3D11_COUNTER_TYPE *, UINT *,
                                          LPSTR, UINT *, LPSTR, UINT *, LPSTR, UINT *) override
   {
      return E_NOTIMPL;
   }
   HRESULT STDMETHODCALLTYPE CheckFeatureSupport(D3D11_FEATURE, void *, UINT) override
   {
      return E_NOTIMPL;
   }
   HRESULT STDMETHODCALLTYPE GetPrivateData(REFGUID, UINT *, void *) override { return E_NOTIMPL; }
   HRESULT STDMETHODCALLTYPE SetPrivateData(REFGUID, UINT, const void *) override
   {
      return E_NOTIMPL;
   }
   HRESULT STDMETHODCALLTYPE SetPrivateDataInterface(REFGUID, const IUnknown *) override
   {
      return E_NOTIMPL;
   }
   D3D_FEATURE_LEVEL STDMETHODCALLTYPE GetFeatureLevel() override { return D3D_FEATURE_LEVEL_11_0; }
   UINT STDMETHODCALLTYPE GetCreationFlags() override { return 0; }
   HRESULT STDMETHODCALLTYPE GetDeviceRemovedReason() override { return S_OK; }
   void STDMETHODCALLTYPE GetImmediateContext(ID3D11DeviceContext **ppContext) override;
   HRESULT STDMETHODCALLTYPE SetExceptionMode(UINT) override { return E_NOTIMPL; }
   UINT STDMETHODCALLTYPE GetExceptionMode() override { return 0; }

private:
   ULONG m_refCount = 1;
};
