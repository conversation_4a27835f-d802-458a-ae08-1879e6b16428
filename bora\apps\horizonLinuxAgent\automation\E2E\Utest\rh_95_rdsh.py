# -*- coding: cp1252 -*-
# Copyright (c) Omnissa, LLC. All rights reserved.
# This product is protected by copyright and intellectual property laws in the
# United States and other countries as well as by international treaties.
# -- Omnissa Restricted
import unittest
from lib.client import Client
from testCaseBase import TestCaseBase, broker, agent, catch_exception, CheckCDR_Remote
import lib.kits
import lib.vadc
import lib.client
import lib.auto
import pytest
import configparser
import lib.Tool as tool
import lib.bdweb as bdweb
# import lib.agent

vadc = lib.vadc.vadc



cached_values = lib.kits.cached_values
# branch = lib.auto.raceTrackData['branch']
# buildType = lib.auto.raceTrackData['buildType']
remoteclients = lib.client.remoteclients
users = [f'lvdi-p{x:03}' for x in range(1, 51)]

class Workflow(TestCaseBase):
    """
    LinuxVDI VADC workflow for RHEL9.5
    """
    @pytest.fixture(autouse=True, scope='session')
    def get_args(self, request):
        self.buildnum = int(request.config.getoption("--buildnum"))
        lib.auto.FeatureDescription = request.config.getoption("--featurename")
        self.updateChrome = request.config.getoption("--updatechromedriver")
        lib.auto.sendEmail = request.config.getoption("--sendemail")
        print("Printing buildnum %s" % self.buildnum)
        if self.buildnum == -1:
            print('Getting Latest Build')
            self.buildnum = bdweb.getLatestBuildNumber()
            print('Build Number: %s' % self.buildnum)
        config = configparser.ConfigParser()
        config.optionxform = str
        config.read(lib.auto.configFile)
        config['buildinfo']['buildnum'] = self.buildnum
        with open(lib.auto.configFile, 'w') as configfile:
            config.write(configfile)
        lib.auto.buildInfo['buildnum'] = self.buildnum
        lib.auto.buildNum = self.buildnum
        if self.updateChrome == "true":
            tool.updateChromeDriver()


    def setUp(self):
        """
        -----------------------------------------------------------
        @Mandatory:
        baseVM: base VM name
        ssFresh: snapshot which is ready to install ob, need create
                  before the test running
        CustomSpecName: vCenter customization specification name, should be a Linux one
        installArg: installer script paramters

        @Optional:
        pool: will create Full Clone Pool with the name
        VcID: will use the vCenter of auto.ini if None
        VmFolder: will use same vmFolder as baseVM if None
        HostOrCluster: will use same HostOrCluster as base VM if None
        ResourcePool: will use same ResourcePool as base VM if None
        datastore: will use same datastore as baseVM if None
        NamingPattern: IC pool VM naming pattern
        MaximumCount: IC pool VM quantity

        """
        self.poolInfo = lib.auto.GetConfigData()

        ###########################################################
        self.poolInfo['VmFolder'] = None
        self.poolInfo['HostOrCluster'] = None
        self.poolInfo['ResourcePool'] = None
        self.poolInfo['datastore'] = None
        self.poolInfo['MaximumCount'] = "1"
        self.poolInfo['multisession'] = True
        self.Description = 'Test Sample on how a test script should look like'


        #-----------------------------------------------------------
          # Define the feature name, test case name, description, tcmsid for each case
        self.Feature = 'RHEL 9.5 VADC Multi-Session over Win10 client'
        if self._testMethodName == 'test_01':
            self.Description = "VADC Pool with RHEL 9.5"
            self.Name = self.Description
            self.TCMSID = '1'
        if self._testMethodName == 'test_02':
            self.Description = 'Multi-Session H264 Enabled'
            self.Name = self.Description
            self.TCMSID = '2'
        if self._testMethodName == 'test_03':
            self.Description = 'Multi-Apps(Terminal) H264 Enabled'
            self.Name = self.Description
            self.TCMSID = '3'

        # reset per workflow variables for agent/broker/client
        broker.brokerDomain = self.poolInfo['domain']
        agent.user          = self.poolInfo['user']
        agent.password      = self.poolInfo['password']
        
        # reset per workflow variables for agent/broker/client
        remoteclients.clear()
        for k, v in lib.auto.rmtData.items():
            c = Client()
            c.Update(ipAddr=v)
            c.node = k
            remoteclients.append(c)
        for idx in remoteclients:
            print(f"remote client {idx}")

        TestCaseBase.setUp(self)

    def tearDown(self):
        """
        This function will be called after each case running
        """
        for idx in remoteclients:
            if not hasattr(idx, 'user'):
                continue
            vadc.Disconnect_Logoff(idx.user, action='logoff')
        TestCaseBase.tearDown(self)

    @catch_exception
    def test_01(self):
        self.New_VADCPool(self.poolInfo, ssoDesktopType=self.poolInfo['SSODesktopType'])

    @catch_exception
    def test_02(self):
        self.MultiSession(users=users)

    @catch_exception
    def test_03(self):
        self.MultiApps(users=users)

if __name__ == '__main__':
    unittest.main()
