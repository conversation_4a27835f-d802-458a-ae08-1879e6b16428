# Copyright (c) Omnissa, LLC. All rights reserved.
# This product is protected by copyright and intellectual property laws in the
# United States and other countries as well as by international treaties.
# -- Omnissa Restricted

"""rgbyuvTest: RGB<->YUV fidelity testing tool

rgb2yuvTest is a test application that tests our color conversion code.
Currently it is limited to libyuv routines.
It uses a PNG file as input, converts to a number of YUV formats (different
chroma subsampling, YUV range, and color matrices are covered), and then
back to RGB for comparison to the source.

Maintained by the Blast display team:
   1. <EMAIL>
   2. Reviewboard groups: appblast
"""

import vmware

Import("env_opts")

target = "rgbyuvTest"
env = vmware.Host().DefaultEnvironment()

env.Append(
    CPPDEFINES={
        "USERLEVEL": None,
        "VMX86_DESKTOP": None,
    },
    CPPPATH=[
        "#bora/public",
        "#bora/lib/public",
        vmware.HeaderDirectory("vnc"),
    ],
)

vmware.LoadTool(
    env,
    [
        "libpng",
        "libz",
        "libyuv",
    ],
)

if vmware.Host().IsLinux():
    env.Append(
        LIBS=[
            "dl",
        ],
        STATICLIBS=[
            "yuv",
        ],
    )

if vmware.Host().IsMac():
    env.Append(
        FRAMEWORKS=[
            "CoreFoundation",
            "DiskArbitration",
            "IOKit",
            "Security",
        ],
        LIBS=["z"],
    )

if vmware.Host().IsWindows():
    env.LoadTool(
        [
            "msvcrt",
        ]
    )

    env.Append(
        LINKFLAGS=[
            '-base:"0x69500000"',
            "-subsystem:console",
        ],
        LIBS=[
            "ws2_32.lib",
            "uuid.lib",
            "oldnames.lib",
            "setupapi.lib",
            "netapi32.lib",
            "delayimp.lib",
            "kernel32.lib",
            "user32.lib",
            "gdi32.lib",
            "advapi32.lib",
            "ole32.lib",
            "oleaut32.lib",
            "shell32.lib",
            "msimg32.lib",
            "wbemuuid.lib",
            "winmm.lib",
            "glu32.lib",
            "Wtsapi32.lib",
            "msvcrt.lib",
            "bcrypt.lib",
            "crypt32.lib",
            "yuv.lib",
        ],
    )

vncLibs = [
    "config",
    "coreDump",
    "dict",
    "err",
    "file",
    "image",
    "lock",
    "log",
    "misc",
    "panic",
    "panicDefault",
    "poll",
    "productState",
    "raster",
    "rectangle",
    "region",
    "sig",
    "string",
    "thread",
    "unicode",
    "user",
    "uuid",
]

if vmware.Host().IsMac():
    vncLibs += [
        "location",
    ]

if vmware.Host().IsWindows():
    vncLibs += ["wmi"]

exe = vmware.Executable(target, env=env)

exe.addStaticLibs("vmlibs", vncLibs)

exe.addSubdirs(
    [
        "apps/rde/blast/rgbyuvTest",
    ]
)

node = exe.createProgramNode(env)
vmware.RegisterNode(node, target)

# Stage redistributables like libpng when building rgbyuvTest.
for n in env.get("REDIST") or []:
    vmware.RegisterNode([File(n)], target)

vmware.Alias(target + "-build", node)
