/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */
#pragma once

class gutils {
public:
   static std::string GetTestNameA()
   {
      auto testInstance = UnitTest::GetInstance();
      std::string testCaseName(testInstance->current_test_case()->name());
      std::string testName(testInstance->current_test_info()->name());
      return testCaseName + "." + testName;
   }

   static std::wstring GetTestName() { return CORE::mstr::to_wstr(GetTestNameA().c_str()).c_str(); }
};