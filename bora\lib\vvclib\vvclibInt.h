/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */


/*
 * vvclibInt.h
 *
 * View Virtual Channel internal definition
 *
 */


#ifndef _VVCLIBINT_H
#define _VVCLIBINT_H

#ifdef __cplusplus
extern "C" {
#endif

#include "horizon.h"
#include "dbllnklst.h"
#include "file.h"
#include "userlock.h"
#include "sequenceNumber.h"
#include "hashMap.h"
#include "str.h"
#include "mutexRankLib.h"
#include "util.h"
#include "hostinfo.h"

#include "vvc.h"
#include "vvcBuf.h"
#include "vvcChunk.h"
#include "vvclib.h"
#include "vvcPerf.h"
#include "vvcStatus.h"
#include "rbtree.h"
#include "pluginPrivateFunctions.h"
#include "vvcChannelRecvPriQ.h"
#include "vvcRecvMessage.h"
#include "vvcRecv.h"
#include "vvcDataTransportSwitch.h"
#include "vvcLog.h"

/*
 * This header file is also included by LinuxVDI component test only for In-Proc
 * plugins testing purpose:
 * /bora/apps/horizonLinuxAgent/componentTest/c/vvcLoader/common/mockedVvcLib.h
 */

#if defined(__ANDROID__)
#   include <pthread.h>
#endif

// Config settings and default
#define VVC_DISABLE_BANDWIDTH_CFG_KEY "disableBandwidthDetection"
#define VVC_DISABLE_BANDWIDTH_DEFAULT 0

#define VVC_BANDWIDTH_MAX_INFLIGHT_CFG_KEY "bandwidthMaxInFlight"
#define VVC_BANDWIDTH_MAX_INFLIGHT_DEFAULT VVC_INFINITE_INFLIGHT

#define VVC_LATENCY_TIMEOUT_PERIOD_MS_CFG_KEY "LatencyTimeoutPeriodMS"
#define VVC_LATENCY_TIMEOUT_PERIOD_MS_DEFAULT 10000

#define VVC_DISPATCH_SEND_INTERVAL_CFG_KEY "dispatchSendIntervalUS"
#define VVC_DISPATCH_SEND_INTERVAL_DEFAULT 15000

#define VVC_DISPATCH_SEND_INTERVAL_SLOP_CFG_KEY "dispatchSendIntervalSlopUS"
#define VVC_DISPATCH_SEND_INTERVAL_SLOP_DEFAULT 100

#define VVC_ENABLE_VVC_CLOSE_SEQ_CFG_KEY "VVCCloseSeq"
#define VVC_ENABLE_VVC_CLOSE_SEQ_DEFAULT 1

#define VVC_ENABLE_VVC_RECONNECT_TOKEN_CFG_KEY "VVCReconnectTokenEnable"
#define VVC_ENABLE_VVC_RECONNECT_TOKEN_DEFAULT 0

#define VVC_DISABLE_PLUGIN_RECONNECT_TOKEN_CFG_KEY "PluginReconnectTokenDisable"
#define VVC_DISABLE_PLUGIN_RECONNECT_TOKEN_DEFAULT 1

#define VVC_ENABLE_VVC_HEARTBEATS_CFG_KEY "VVCHeartbeats"
#define VVC_ENABLE_VVC_HEARTBEATS_DEFAULT 1

#define VVC_ENABLE_RECEIVE_WINDOW_CFG_KEY "receiveWindow"
#define VVC_ENABLE_RECEIVE_WINDOW_DEFAULT 0

#define VVC_ENABLE_CHANNEL_RESYNC_CFG_KEY "channelResync"
#define VVC_ENABLE_CHANNEL_RESYNC_DEFAULT 0

#define VVC_ENABLE_RAW_CHANNELS_CFG_KEY "RawChannelsEnabled" /* Managed by GPO */
#define VVC_ENABLE_RAW_CHANNELS_DEFAULT 1

#define VVC_RAW_CHANNEL_TYPE_BEAT "beat"
#define VVC_RAW_CHANNEL_TYPE_TCP "tcp"
#define VVC_RAW_CHANNEL_TYPE_VVC "vvc"

#define VVC_RAW_CHANNEL_TYPE_CFG_KEY "rawChannelType"
#define VVC_RAW_CHANNEL_TYPE_DEFAULT VVC_RAW_CHANNEL_TYPE_VVC

#define VVC_RAW_CHANNEL_TYPE_PORT 0
#define VVC_RAW_CHANNEL_TYPE_PROTO 1

#define VVC_CHANNEL_BW_MIN_UPDATE_INTERVAL_CFG_KEY "channelBwMinUpdateIntervalMS"
#define VVC_CHANNEL_BW_MIN_UPDATE_INTERVAL_MIN 0       /* cache disabled */
#define VVC_CHANNEL_BW_MIN_UPDATE_INTERVAL_MAX 2000    /* ms */
#define VVC_CHANNEL_BW_MIN_UPDATE_INTERVAL_DEFAULT 200 /* ms */
#define VVC_CHANNEL_IS_BW_CACHE_ENABLED(session) ((session)->channelBwMinUpdateIntervalMS > .1)

/*
 * Send rate limiter bucket size is set to number of bytes that can be
 * transmitted in 1sec at the rate of maxBandwidth set for the
 * vvcSession. Restrict to min BW of 56kbps and max BW of MAX_UINT32 Bps.
 */
#define VVC_SEND_BUCKET_DISPATCH_PERIOD_MIN_BYTES 512 /* bytes */
#define VVC_SEND_BUCKET_SIZE_MIN 7000                 /* bytes */
#define VVC_SEND_BUCKET_SIZE_MAX (MAX_UINT32)         /* bytes */
#define VVC_MAX_BANDWIDTH_BURST_MSEC_DEFAULT 1000     /* msec */
#define KBPS(x) ((x) * 8 / 1000.0)

/*
 * numDataTransports is the Vvc-level killswitch for BENIT.
 * Set the default value to 2 i.e. BENIT is enabled at VVC level.
 *
 * However there are BlastSocket-level and AppBlast/MKS level kill-switches.
 * These kill switches decide whether to ask VVCLIB_OpenSession() to enable
 * BENIT or not.
 * The BlastSocket level kill-switches can have platform specific values.
 * (This pattern is followed for channelResync i.e. NC as well).
 */
#define VVC_NUM_DATA_TRANSPORTS_CFG_KEY "numDataTransports"
#define VVC_NUM_DATA_TRANSPORTS_DEFAULT 2

#define VVC_MPT_VERSION_CFG_KEY "mptVersion"

// Magic numbers for handle types
#define VVC_INSTANCE_MAGIC 0xc7700c77
#define VVC_LISTENER_MAGIC 0xc7711c77
#define VVC_CHANNEL_MAGIC 0xc7722c77
#define VVC_SESSION_MAGIC 0xc7733c77
#define VVC_SEND_COMPLETION_CONTEXT_MAGIC 0xc7744c77
#define VVC_OPEN_CHANNEL_MAGIC 0xc7755c77
#define VVC_MSG_MAGIC 0xc7766c77
#define VVC_RECV_COMPLETION_CONTEXT_MAGIC 0xc7777c77
#define VVC_SCC_BATCHER_MAGIC 0xc7788c77
#define VVC_MSGCHANNEL_MAGIC 0xc7799c77

#define VVC_PROXY_NODE_MAGIC 0xff0178ec
#define VVC_PROXY_LISTENNODE_MAGIC 0xff1178ec
#define VVC_PROXY_CHANNELNODE_MAGIC 0xff2178ec
#define VVC_PROXY_HUB_MAGIC 0xff0278ec
#define VVC_PROXY_LISTENHUB_MAGIC 0xff1278ec
#define VVC_PROXY_CHANNELHUB_MAGIC 0xff2278ec
#define VVC_PROXY_MSGCHANNEL_MAGIC 0xff3278ec


// API latest version defintions (for internal use only)
#define VVC_API_MAJOR_VER VVC_MAJOR_VER_2
#define VVC_API_MINOR_VER VVC_MINOR_VER_1
#define VVC_API_INTF VvcIntfV21


// Protocol version and capabilities
#define VVC_PROT_MAJOR_VER 4
#define VVC_PROT_MINOR_VER 0


// Lowest supported version
#define VVC_PROT_BASE_MAJOR_VER 1
#define VVC_PROT_BASE_MINOR_VER 0


// Control channel ID and name
#define VVC_CONTROL_CHANNEL_ID 0
#define VVC_CONTROL_CHANNEL_NAME "vvcctrl"

// Zombie listener name
#define VVC_ZOMBIE_LISTENER_NAME "vvc-zombie"

// Channel property config values
#define VVC_CHANNEL_PROPERTIES_CFG_KEY "channelProperties"
#define VVC_CHANNEL_PROP_PRIORITY_CFG_KEY "priority"
#define VVC_CHANNEL_PROP_DEBUG_TRACE_FLAGS_CFG_KEY "debugTraceFlags"


// Allow zombie sessions
#define VVC_ZOMBIE_SESSION_CFG_KEY "allowZombieSessions"
#define VVC_ZOMBIE_SESSION_DEFAULT TRUE

#define VVC_MAX_CHANNELS_V20 255
#define VVC_MAX_CHANNELS_V30 (MAX_UINT32 - 2)

// Periodic interval when session cleanup tasks run
#define VVC_SESSION_CLEANUP_TASKS_INTERVAL_SEC (30)


// Debug tracing flags
#define VVC_TRACE_RTT_HISTORY 0x001
#define VVC_TRACE_DISPATCH_SEND_PERIOD_HISTORY 0x002
#define VVC_TRACE_DISPATCH_SEND_NEW_PERIOD_HISTORY 0x004
#define VVC_TRACE_DISPATCH_SEND_POLL_HISTORY 0x008
#define VVC_TRACE_SEND_COMPLETION_HISTORY 0x010
#define VVC_TRACE_ACK_EVENT_HISTORY 0x020
#define VVC_TRACE_MSG_DISPATCH_HISTORY 0x040
/*
 * Combined send/recv history for session transport and channels.  Channel
 * tracing must also be enabled using channel trace flags
 */
#define VVC_TRACE_SEND_RECV_HISTORY 0x080
// Do not write send/recv history entries for the session transport
#define VVC_TRACE_SEND_RECV_SUPRESS_TP_HISTORY 0x100

/*
 * VVC_TRACE_XBE_GET_NW_STATS_HISTORY:
 * Set this bit if you want to trace calls done by VVC to
 * transportBe.getNetworkStats (provided transportBe implements getNetworkStats)
 * This trace bit is applicable session-wide.
 *
 * The VvcXBeGetNwStatsQueryReason enum is used to indicate the reason
 * for VVC to invoke transport.getNetworkStats.
 *
 * An additional CHAN_NW_STATS_HISTORY bit below will trace every call
 * done to VVCLIB_GetInfo(VvcChannel) to report allowedBwConsumption for
 * some specific channel.
 *
 */

#define VVC_TRACE_XBE_GET_NW_STATS_HISTORY 0x200

/*
 * Consumers of VVC can query via VVCLIB_GetInfo(VvcChannel) about
 * how much percentage of current bandwidth a particular channel can
 * use for itself (provided transportBe implements getNetworkStats).
 *
 * See comments section in this file next to struct VvcDispatchimulator
 * for more details.
 *
 * The VVC_TRACE_CHAN_NW_STATS_HISTORY bit will decide
 * whether to log all such queries to a trace file or not.
 *
 * The below bit must be set session wide & also for channel whose
 * GetInfo queries you are interested in.
 * Example:
 * If you want to trace queries done on channel "blast-mks", then
 * you will need 2 Reg Keys set as below:
 *
 * vvc\\debugTraceFlags                               REG_DWORD 0x200
 * vvc\\channelProperties\\blast-mks\\debugTraceFlags REG_DWORD 0x200
 */

#define VVC_TRACE_CHAN_NW_STATS_HISTORY 0x400


// Channel recv buf initial size
#define VVC_CHANNEL_RECV_BUF_INITIAL_SIZE_DEFAULT 262144
#define VVC_CHANNEL_RECV_BUF_INITIAL_SIZE_KEY "ChannelRecvBufInitialSize"
#define RECV_BUF_MOD_GROW_BY 4096

// Specifies how long we should wait for the event thread to exit
#define VVC_EVENT_THREAD_WAIT_TIMEOUT_MS 5000

// Flag to check if the instance belongs to worker process
#define VVC_INSTANCE_ROLE_WORKER(instance)                                                         \
   (0 == (instance->flags & VVC_INSTANCE_ROLE_CLIENT)) &&                                          \
      (0 == (instance->flags & VVC_INSTANCE_LOCATION_OUTOFPROC))

// Flag to check if the instance belongs to out-of-proc process
#define VVC_INSTANCE_ROLE_OUTOFPROC(instance)                                                      \
   0 != (instance->flags & VVC_INSTANCE_LOCATION_OUTOFPROC)

/*
 *-----------------------------------------------------------------------------
 *
 * LERP --
 *
 *      Linearly interpolates between b and c, using a as the factor.
 *
 *-----------------------------------------------------------------------------
 */
#define LERP(a, b, c) ((a) * (b) + (1.0 - (a)) * (c))


/*
 *-----------------------------------------------------------------------------
 *
 * CAP --
 *
 *      Cap deviation of i from a using w as a factor.  For example,
 *      CAP(100, .1, x) can vary between 90 and 110, providing x is a
 *      finite number.
 *
 *-----------------------------------------------------------------------------
 */
#define CAP(w, a, i) HZN_CLAMP((i), (1.0 - (w)) * (a), (1.0 + (w)) * (a))

// Blast VVC session name
#define BLAST_VVC_SESSION_NAME "Blast-Session"

// VVC Session Id
struct _VvcSessionIdentifier {
   int32 sessionId;
};

// Handle types
typedef enum {
   VvcHandleTypeInstance,
   VvcHandleTypeListener,
   VvcHandleTypeChannel,
   VvcHandleTypeSession,
   VvcHandleTypeHubData,
   VvcHandleTypeListenHub,
   VvcHandleTypeChannelHub,
   VvcHandleTypeNodeData,
   VvcHandleTypeListenNode,
   VvcHandleTypeChannelNode,
   VvcHandleTypeRawChannel,
   VvcHandleTypeProxyMsgChannel,

   VvcHandleTypeMaximum

} VvcHandleType;


#define VVC_HANDLE_TYPE_MASK 0x000F

// Used to get VvcHandleType from VvcXXXHandle
#define VVC_GET_HANDLE_TYPE(handle) (uint8)((uintptr_t)(handle) & VVC_HANDLE_TYPE_MASK)

// Use NULL as invalid token handle value
#define INVALID_TOKEN_HANDLE_VALUE NULL

#define VVC_HANDLE_TYPE_RAW_CHANNEL(handle) (VVC_GET_HANDLE_TYPE(handle) == VvcHandleTypeRawChannel)

// Diagnostic reference tags
typedef enum {
   VvcTagDiag,
   VvcTagCreation,
   VvcTagGetHandleFromToken,
   VvcTagExternal,
   VvcTagSnapshot,
   VvcTagFuncOpenChanTimeoutCb,
   VvcTagFuncActivateListener,
   VvcTagFuncSessionTransportSendComplete,
   VvcTagFuncScheduleHeadMsgFromChannel,
   VvcTagFuncCleanupMessageTree,
   VvcTagFindSessionFromId,
   VvcTagFindZombieSessionFromId,
   VvcTagFindListenerFromNameAndId,
   VvcTagFindChannelFromSession,
   VvcTagChildOpenChan,
   VvcTagChildMsg,
   VvcTagChildListener,
   VvcTagChildChannel,
   VvcTagChildSession,
   VvcTagEvent,
   VvcTagEventOnError,
   VvcTagEventData,
   VvcTagDispatchEvents,
   VvcTagDispatchEventsPoll,
   VvcTagPurgeEvents,
   VvcTagSendCompletionContext,
   VvcTagOpenChanTimeout,
   VvcTagAsyncRead,
   VvcTagAsyncReRead,
   VvcTagPerfPoll,
   VvcTagFuncCloseChannelInt,
   VvcTagDispatchSendQueuesPeriodicPoll,
   VvcTagRttPoll,
   VvcTagSendMsg,
   VvcTagDeferredDispatchSendQueues,
   VvcTagDispatchSendPoll,
   VvcTagDispatchEventPoll,
   VvcTagDestroy,
   VvcTagMainInstance,
   VvcTagBandwidthStatsLoggingPoll,
   VvcTagSendQueue,
   VvcTagSendTree,
   VvcTagMptMsg,
   VvcTagProbePoll,
   VvcTagFuncSessionTransportCancelIo,
   VvcTagCancelRecvBuffer,
   VvcTagAsockBackend,
   VvcTagCtrlKeepAlive,
   VvcTagAsockBeErrorHandler,
   VvcTagAsockBeRecvComplete,
   VvcTagSessionOnCloseEvent,
   VvcTagCloseSessionChannelCleanup,
   VvcTagPurgeMptSendQueue,
   VvcTagPurgeSendQueue,
   VvcTagPurgeChannelSendQueues,
   VvcTagAsockBeGeneric,
   VvcTagAsockBeSnapshot,
   VvcTagAsockBeKeepaliveTimerCb,
   VvcTagAsockBeSessionParent,
   VvcTagAsockBeAsockApi,
   VvcTagAsockBeInAsockCb,
   VvcTagFuncSend,
   VvcTagReleaseMsg,
   VvcTagTransportSwitchPollCb,
   VvcTagDeferredAcksPollCb,
   VvcTagGetNwStats,
   VvcTagSessionCleanupTasks,
   VvcTagSessionCloseNCDeclinedChannels,
   VvcTagSendMPTAcks,
   VvcTagSessionCloseSeq,
   VvcTagRawGeneric,
   VvcTagRawConnect,
   VvcTagMax

} VvcTag;


// Diagnostic invocation triggers for dispatch send
typedef enum {
   VvcDispatchSendTriggerPollCb,
   VvcDispatchSendTriggerPollThread,
   VvcDispatchSendTriggerPollDeferred,
   VvcDispatchSendTriggerOnCtrl,
   VvcDispatchSendTriggerTransportSendComplete,
   VvcDispatchSendTriggerTransportRecvComplete,
   VvcDispatchSendTriggerChannelSend,
   VvcDispatchSendTriggerKeepAliveIntervalTimeout,
   VvcDispatchSendTriggerSessionCloseSend,
   VvcDispatchSendTriggerSessionCloseFlush,

} VvcDispatchSendTrigger;


// Diagnostic invovation trigger for queueing channel on close event
typedef enum {
   VvcQueueOnChannelCloseTriggerOpClose,
   VvcQueueOnChannelCloseTriggerOpCloseAck,
   VvcQueueOnChannelCloseTriggerSessionClose,
   VvcQueueOnChannelCloseTriggerPendingOpen,
   VvcQueueOnChannelCloseTriggerDestroySendComplCtx,
   VvcQueueOnChannelCloseTriggerPurgeSendTree,
   VvcQueueOnChannelCloseTriggerAbort,
   VvcQueueOnChannelCloseTriggerError
} VvcQueueOnChannelCloseTrigger;


// Command that send queue poll will perform on waking
typedef enum {
   // Restart the time slice
   VvcDispatchSendPollRestart = 0,

   // Stop poll thread
   VvcDispatchSendPollStop = 1,

   // Nothing to send
   VvcDispatchSendPollNoWork = 2,

   // Dispatch send queue
   VvcDispatchSendPollDispatch = 3

} VvcDispatchSendPollCmd;

#define IS_DEFER_SEND_INSTANCE(instance) (instance->flags & VVC_INSTANCE_DEFER_SEND_DISPATCH)

#define IS_DEFER_SEND_THREAD_INSTANCE(instance)                                                    \
   (instance->flags & VVC_INSTANCE_DEFER_SEND_DISPATCH_THREAD)

// Command that event queue poll will perform on waking
typedef enum {
   // Stop poll thread
   VvcDispatchEventPollStop = 0,

   // Dispatch event queue
   VvcDispatchEventPollDispatch = 1

} VvcDispatchEventPollCmd;


// IS/SET/RESET_DEFER_EVENT_INSTANCE
#define IS_DEFER_EVENT_INSTANCE(instance)                                                          \
   IS_DEFER_EVENT_PLUGIN(instance, VVC_PLUGIN_ID_CORE_PROTOCOL)

#define SET_DEFER_EVENT_INSTANCE(instance)                                                         \
   SET_DEFER_EVENT_PLUGIN(instance, VVC_PLUGIN_ID_CORE_PROTOCOL)

#define RESET_DEFER_EVENT_INSTANCE(instance)                                                       \
   RESET_DEFER_EVENT_PLUGIN(instance, VVC_PLUGIN_ID_CORE_PROTOCOL)


// IS/SET/RESET_DEFER_EVENT_PLUGIN
#define IS_DEFER_EVENT_PLUGIN(instance, pluginId)                                                  \
   (pluginId == VVC_PLUGIN_ID_CORE_PROTOCOL ? instance->flags & VVC_INSTANCE_DEFER_EVENT_DISPATCH  \
                                            : instance->plugins[pluginId].useDeferEvent)

#define SET_DEFER_EVENT_PLUGIN(instance, pluginId)                                                 \
   do {                                                                                            \
      instance->plugins[pluginId].useDeferEvent = TRUE;                                            \
      if (pluginId == VVC_PLUGIN_ID_CORE_PROTOCOL) {                                               \
         instance->flags |= VVC_INSTANCE_DEFER_EVENT_DISPATCH;                                     \
      }                                                                                            \
   } while (FALSE)

#define RESET_DEFER_EVENT_PLUGIN(instance, pluginId)                                               \
   do {                                                                                            \
      instance->plugins[pluginId].useDeferEvent = FALSE;                                           \
      if (pluginId == VVC_PLUGIN_ID_CORE_PROTOCOL) {                                               \
         instance->flags &= ~VVC_INSTANCE_DEFER_EVENT_DISPATCH;                                    \
      }                                                                                            \
   } while (FALSE)


// IS/SET/RESET_DEFER_EVENT_THREAD_INSTANCE
#define IS_DEFER_EVENT_THREAD_INSTANCE(instance)                                                   \
   IS_DEFER_EVENT_THREAD_PLUGIN(instance, VVC_PLUGIN_ID_CORE_PROTOCOL)

#define SET_DEFER_EVENT_THREAD_INSTANCE(instance)                                                  \
   SET_DEFER_EVENT_THREAD_PLUGIN(instance, VVC_PLUGIN_ID_CORE_PROTOCOL)

#define RESET_DEFER_EVENT_THREAD_INSTANCE(instance)                                                \
   RESET_DEFER_EVENT_THREAD_PLUGIN(instance, VVC_PLUGIN_ID_CORE_PROTOCOL)


// IS/SET/RESET_DEFER_EVENT_THREAD_PLUGIN
#define IS_DEFER_EVENT_THREAD_PLUGIN(instance, pluginId)                                           \
   (pluginId == VVC_PLUGIN_ID_CORE_PROTOCOL                                                        \
       ? instance->flags & VVC_INSTANCE_DEFER_EVENT_DISPATCH_THREAD                                \
       : instance->plugins[pluginId].useDeferEventThread)

#define SET_DEFER_EVENT_THREAD_PLUGIN(instance, pluginId)                                          \
   do {                                                                                            \
      instance->plugins[pluginId].useDeferEventThread = TRUE;                                      \
      if (pluginId == VVC_PLUGIN_ID_CORE_PROTOCOL) {                                               \
         instance->flags |= VVC_INSTANCE_DEFER_EVENT_DISPATCH_THREAD;                              \
      }                                                                                            \
   } while (FALSE)

#define RESET_DEFER_EVENT_THREAD_PLUGIN(instance, pluginId)                                        \
   do {                                                                                            \
      instance->plugins[pluginId].useDeferEventThread = FALSE;                                     \
      if (pluginId == VVC_PLUGIN_ID_CORE_PROTOCOL) {                                               \
         instance->flags &= ~VVC_INSTANCE_DEFER_EVENT_DISPATCH_THREAD;                             \
      }                                                                                            \
   } while (FALSE)


/**---------------------------------------------------------
 *  Common field for main VVC handle types
 *----------------------------------------------------------*/

typedef struct _VvcCommon {
   uint32 magic;
   Atomic_uint32 refCount;
   // Token given out to users of VVC as the token handle
   VvcCommonHandle tokenHandle;
   void *clientData;
   Atomic_uint32 *tags;

} VvcCommon;

#define VVCCOMMON_TAGS_SIZE sizeof(Atomic_uint32[VvcTagMax])

#define VVCCOMMON_TAG_VALUE(common, idx) (common->tags ? common->tags[idx].value : 0)


// Forward decl
typedef struct _VvcInstance VvcInstance;
typedef struct _VvcSession VvcSession;
typedef struct _VvcListener VvcListener;
typedef struct _VvcChannel VvcChannel;
typedef struct _VvcMsgChannel VvcMsgChannel;
typedef struct _VvcRecvCompletionContext VvcRecvCompletionContext;
typedef struct _VvcBandwidthDetection VvcBandwidthDetection;
typedef struct _VvcSessionIdentifier VvcSessionIdentifier;
typedef struct _VvcSCCBatcher VvcSCCBatcher;


// VVC events
#define VvcEvListenerOnConnect 0x001
#define VvcEvListenerOnPeerOpen 0x002
#define VvcEvListenerOnClose 0x004
#define VvcEvChannelOnOpen 0x008
#define VvcEvChannelOnClose 0x010
#define VvcEvChannelOnSendComplete 0x040
#define VvcEvChannelOnDelivered 0x080
#define VvcEvChannelOnRecv 0x100
#define VvcEvSessionOnError 0x200
#define VvcEvListenerOnDisconnect 0x400
#define VvcEvSessionOnClose 0x800
#define VvcEvChannelOnPause 0x1000
#define VvcEvChannelOnResume 0x2000


#define VVC_ENABLE_PERF_COUNTERS_DEFAULT FALSE
#define VVC_ENABLE_PERF_COUNTERS_KEY "EnablePerfCounters"

#define VVC_ONRECV_EXTENSION_BUFFER (1U << 0)
#define VVC_ONRECV_RAW_BUFFER (1U << 1)

#define MILLI_SEC_IN_SEC 1000
#define MICRO_SEC_IN_SEC 1000000
#define MICRO_SEC_IN_MILLI_SEC 1000

#define NS_TO_US(ns) ((ns) / 1000)
#define NS_TO_MS(ns) ((ns) / 1000000)


/*
 * ----------------------------------------------------
 *  VvcHandleToTokenMapping structure
 *
 *    This struct will be the "data" portion in the
 *    corresponding map for the specified handle for
 *    instance, listener, session and channel. The "key"
 *    is the opaque tokenHandle that users of VVC will see.
 *
 *    This struct doesn't need its own refCount because
 *    it is tied to VVC object's refCount.
 *
 *    The id & name are not used in HashMap Get/Put
 *    - they are only helpful in debugging
 *----------------------------------------------------
 */

typedef struct _VvcHandleToTokenMapping {
   VvcCommonHandle handle; // raw struct pointer
   int32 id;               // sessionId or channelId
   char *name;             // for debugging only
} VvcHandleToTokenMapping;

typedef struct _VvcHandleToTokenMap {

   VvcHandleType handleType;
   HashMap **handleToTokenMap;
   Atomic_Ptr *handleToTokenMapLock;
   const char *handleToTokenMapName;
   const uint32 initMapSize;
   uint32 maxHandleCount; // Keep tracking maximum handles saved in the map

} VvcHandleToTokenMap;

// VVC iterate token map callback
typedef void (*VvcTokenMapIterCb)(void *key, void *data, void *clientData);

// Iterate VVC object to token map (gXXXToTokenMap)
void VvcIterateTokenMap(const VvcHandleToTokenMap *mapEntry, VvcTokenMapIterCb callback,
                        Bool clear);

// VVC event callback
typedef void (*VvcEventCb)(uint32 seqNo, uint32 event, VvcCommon *object1, VvcCommon *object2,
                           void *recvBuf, size_t recvLen, void *clientData);

// Event entry
typedef struct _VvcEventEntry {
   DblLnkLst_Links link;

   uint32 seqNo;
   uint32 event;
   VvcCommon *object1;
   VvcCommon *object2;
   void *recvBuf;
   size_t recvLen;
   void *eventData;
   VvcEventCb eventCb;

} VvcEventEntry;


// Plugin information
typedef struct _VvcPluginInfo {
   DblLnkLst_Links eventQueue;
   VvcDispatchEventPollCmd dispatchEventPollCmd;

   VThreadID dispatchEventPollTid;
   Bool useDeferEvent;
   Bool useDeferEventThread;

   Atomic_uint32 dispatching;
   Atomic_uint32 dispatchingDone;

   MXUserExclLock *dispatchEventWakePollMutex;
   MXUserCondVar *dispatchEventWakePollCond;

} VvcPluginInfo;


/*
 * VVC instance
 *
 *    A host process can contain multiple VVC instances.  A host process
 *    must create a 'main' instance.  A 'main' instance is the default
 *    instance that the public Channel API calls are routed to.
 *
 */
struct _VvcInstance {
   VvcCommon common;

   // Instance sync lock
   MXUserExclLock *instanceLock;

   // Channel listener list
   DblLnkLst_Links listenerList;

   // Session list
   DblLnkLst_Links sessionList;

   // Inactive but still referenced sessions list
   DblLnkLst_Links zombieSessionList;

   // Event Queue
   uint32 queueSeqNo;

   // Per plugin information
   VvcPluginInfo plugins[VVC_PLUGIN_MAX];

   uint32 flags;
   char *name;

   VvcInstanceBackend instanceBe;

   VvcInstanceEvents events;

   Bool enablePerfCounters;

   void *perfCountersDbContext;

#ifdef _WIN32
   int32 dispatchEventThreadCount;
   MXUserExclLock *dispatchEventThreadCountLock;
   HANDLE dispatchEventThreadDone;
#endif
};


// Get of the global main instance
VvcInstance *VvcGetMainInstance();


// Channel listener instance definition
struct _VvcListener {
   VvcCommon common;

   // Back pointer to parent instance
   DblLnkLst_Links instanceLink;
   VvcInstance *instance;

   VvcListenerState state;

   /*
    * Name of virtual channel (can contain a wildcard, e.g. mmr*) to allow
    * for multiple instances of the same channel
    */
   char *name;
   int32 sessionId;
   VvcPluginId pluginId;

   VvcListenerEvents events;

   // Save the function address in API module for security check
   uint64 apiFuncAddr;

   Bool connectEvCbQueued;
};


typedef struct _FeatureCounterSet FeatureCounterSet;

// Feature counterSet
struct _FeatureCounterSet {
   // Back pointer to session
   DblLnkLst_Links sessionLink;
   char *featureName;
   PerfDbHandle perfDbHandle;
   VvcPerfCounterSet counterSet;
};


typedef enum {
   VVC_RAW_CHAN_BEAT,
   VVC_RAW_CHAN_TCP,
   VVC_RAW_CHAN_VVC,
   VVC_RAW_CHAN_ERROR
} VvcRawChanType;


#define VVC_MAX_MSG_CHANNELS_PER_SESSION 64

#define VVC_MAX_NUM_ASOCK_BACKENDS 2

// maximum number of RX features for perf counters
#define VVCPERF_MAX_FEATURES 24
// rate perf counters (like bandwidth) sample count for rolling average
#define VVCPERF_RATE_SAMPLE_CNT_BITS 2
#define VVCPERF_RATE_SAMPLE_COUNT (1 << VVCPERF_RATE_SAMPLE_CNT_BITS)
#define VVCPERF_RATE_COUNT_MASK MASK(VVCPERF_RATE_SAMPLE_CNT_BITS)

// Session instance definition
struct _VvcSession {
   VvcCommon common;

   // Instance sync lock
   MXUserExclLock *sessLock;

   // Back pointer to parent instance
   DblLnkLst_Links instanceLink;
   VvcInstance *instance;

   // Channel list
   DblLnkLst_Links channelList;

   // Cached open channel requests
   DblLnkLst_Links openChanList;

   // Transport backend
   VvcTransptBackend transportBe;

   // Track a transport's receive state - Each Asockbackend has one of
   // these. However there will always be one for the entire session to support
   // non-asock backends (there can only be one of those)
   VvcRecvState *transportRecvState;

   /*
    * A VvcSession can hold two VvcAsockBackends.
    */
   VvcAsockBackend *asockBackends[VVC_MAX_NUM_ASOCK_BACKENDS];
   int32 numAsockBackends;
   int32 activeAsockBackendIndex; // index into asockBackends[] array
   Bool isDataSockNotificationNeeded;
   /*
    * Flag that determines whether a receive on asockBe should be treated
    * as if it is from a new connection.
    */
   Bool asockXBeConnectNeeded;

   /*
    * What transport the _other_ side (the peer) is currently using in its own
    * activeAsockBackendIndex, as far as we can tell from the last
    * appropriate traffic received on this side.
    * Starts at ...None value. Never changed from that value if, by the time
    * the session has started, (numAsockBackends > 0) is false.
    */
   VvcSessionActiveTransportType lastKnownPeerActiveTransportType;

   // Has control keepalive started?
   Bool ctrlKeepaliveStarted;

   VvcSessionCloseReason closeReason;

   VvcDataTransportSwitch *dataTransportSwitch;
   // Signal that we are stopping the periodic transport switch poll function
   Bool stoppingDataTransportSwitch;

   Bool localEnableQoSPolicy;
   VvcQoSPolicyParams *qosPolicy;

   VvcSessionEvents events;

   // Session Flags is currently UNUSED
   uint32 flags;

   /*
    * Channel ID control.  Clients assign odd channel IDs and servers
    * assign even channel IDs.
    */
   uint32 channelIdCtrl;

   // Capabilities provided by vvc for this session
   uint32 connectionCaps;

   VvcSessionState state;

   // Set when session made it to estalibhed
   Bool establishedLatch;

   // Only allow a single thread to dispatch messages
   Bool sendQueued;
   Atomic_uint32 sending;
   Atomic_uint32 sendingDone;

   // Keep track of oustanding async read
   Bool asyncRecvOutstanding;

   /*
    * Keep track of forced cancel IO operations.
    * VVC_SESSION_TP_CANCEL_SENDS, VVC_SESSION_TP_CANCEL_RECV bits
    * are set when user calls VVCLIB_SessionTransportCancelIo
    */
   uint32 forcedCancelIoOps;

   uint16 peerVerMajor;
   uint16 peerVerMinor;

   uint16 negotiatedVerMajor;
   uint16 negotiatedVerMinor;

   int32 sessionId;

   // TODO: recv buffer pool

   VvcRecvCompletionContext *recvCompletionContext;
   /*
    * VVCLIB_SessionTransportCancelIo will hold the outstanding context in
    * cancelledRecvCompletionContext and free if the transport completes
    * the cancelled recv, otherwise it will be placed into the delayed
    * gCancelledRecvCompletionContextList and freed after we 'hope' the
    * transport has finished with it
    */
   VvcRecvCompletionContext *cancelledRecvCompletionContext;

   // Control channel structures
   VvcListener *ctrlListener;
   VvcChannel *ctrlChannel;

   /*
    * Zombied channels are detached from its real listener and attach to this
    * one
    */
   VvcListener *zombieListener;

   // Sent useful bytes - the sum of sentBytes on all transports
   uint64 sentBytes;
   uint64 rawSentBytes;
   // Snapshot of bytes sent on multi-asock transport (ie. non-raw connection)
   uint64 savedSentBytes;

   /*
    * Sent bytes for BWE purpose only. A transport should only increment this
    * counter if wants to use VVC's original bandwidth estimator.
    */
   uint64 bweSentBytes;

   /*
    * Received useful bytes - the cumulative sum of received bytes across all
    * transports.
    */
   uint64 recvedBytes;

   // Acknowledged useful bytes - requested by VVC's BWE and honored by the
   // BWE peer.
   uint64 bweAckedBytes;


   /*
    * Received useful bytes that we (the receiver) haven't acked yet --
    *
    * The receiver acks bytes in a message before the whole message is
    * received. Therefore, during network recovery, we would've "over-acked"
    * these partial bytes, because the partially received message will be
    * retransmitted when the new socket comes up. To prevent the over-acking,
    * these bytes must be subtracted from bytes received in the retransmitted
    * message.
    */
   int64 unackedRecvBytes;

   // Sent VVC messages
   uint64 sentMessages;

   // Received VVC messages
   uint64 recvedMessages;

   // Total time active (Used to calculate sendThroughput).
   uint64 activeTimeMs;

   // Send Throughput
   double sendThroughput;

   // Peer send Throughput
   double peerSendThroughput;

   // Sent bytes on TCP
   uint64 tcpSentBytes;

   // Received bytes on TCP
   uint64 tcpRecvedBytes;

   // Sent bytes on UDP
   uint64 udpSentBytes;

   // Received bytes on UDP
   uint64 udpRecvedBytes;

   uint64 sentBytesInDb;
   uint64 recvedBytesInDb;
   uint64 sentMessagesInDb;
   uint64 recvedMessagesInDb;
   uint64 tcpSentBytesInDb;
   uint64 tcpRecvedBytesInDb;
   uint64 udpSentBytesInDb;
   uint64 udpRecvedBytesInDb;

   /*
    * Priority queue (as sorted tree) of next message chunks to send from each
    * channel. This map stores distinct VvcMsg* + virtual time pairs, the latter
    * value a unique integer acting as the key that also equals VvcMsg::tag.
    *
    * This is the central data structure of the send scheduling
    * algorithm, which fairly multiplexes message chunks from multiple competing
    * channels into the same transport pipe (backend). The scheduler algorithm
    * main step is in VvcDoDispatchSendQueues().
    *
    * Control channels' messages do not participate in this structure, as
    * they're always sent ASAP. Below discusses non-control channels only.
    *
    * The following describes the "steady state" properties of sendTree.
    * It remains in that state except inside the main while() loop in
    * VvcScheduleChannelMessages() (which is in VvcDoDispatchSendQueues()).
    *
    * (It is recommended to read the doc header for VvcChannel::sendQueue
    * before reading on; sendTree and channels' sendQueues are strongly
    * related.)
    *
    * Generally there is 1 VvcMsg* per channel. In particular, the
    * VvcMsg* from channel C in sendTree is the head VvcMsg* of C->sendQueue,
    * unless latter is empty. Take some channel C's head message M0 in sendTree.
    * Then, M0->remaining != 0; and the next chunk to send from channel C
    * is M0->nextChunkLen long and starts at the offset of M0->buf[] determined
    * by M0->remaining. (Recall that, for a given channel C, all bytes must be
    * sent and received in the same order as they were enqueued by user
    * via VVCLIB_Send(); but it's up to scheduler to determine from which
    * channel to send the next bytes and how many, at a given time.)
    *
    * That answers which chunk of which message to send for a given C; but
    * which channel C is the one from which to next send a message chunk?
    * Answer: The one with the lowest v-time value. Since this is currently
    * implemented as a min-tree by v-time, it's the first element of sendTree
    * then. [#]
    *
    * Finally, what _is_ v-time? In the black-boxy sense, it's simply an integer
    * that determines the relative order in which channels' next messages
    * should be multiplexed into the pipe. When dequeuing from sendTree,
    * only the relative order of v-times in it matters; the scale does not.
    * However, when inserting a message into sendTree, a v-time must be
    * determined for it; here scale matters, as the message's channel's
    * priority and other factors determine how high or low its v-time should be.
    * Algorithm details for computing v-time are in the sendTree insert routine,
    * VvcScheduleHeadMsgFromChannel().
    *
    * [#] Conceptually, sendTree is a priority queue rather than a tree
    * of parents and children nodes. (TODO: Consider renaming it sendPriQ.)
    * A bin-heap is typically a space- and time-efficient structure for a
    * a priority queue. See VvcScheduleHeadMsgFromChannel() for why it's
    * currently implemented as a sorted tree.
    */
   RbtInt32 *sendTree;

   /*
    * Changes from FALSE to TRUE when the transport backend (pipe) has
    * permanently failed, meaning the system is currently in Recovery.
    *
    * When Network Continuity is enabled, a new pipe spins up during this
    * Recovery phase. If it comes up, Recovery ends successfully, and
    * asockXBeDown becomes FALSE again.
    *
    * When Network Continuity is disabled (Network Recovery 1.0), Recovery
    * ends when a brand new session is brought up with a new transport
    * backend and with asockXBeDown set to FALSE. asockXBeDown will never
    * be set back to FALSE on that original session.
    *
    * See doc header for VvcChannel::mptSendQ for details about how
    * Recovery works.
    */
   Bool asockXBeDown;

   /*
    * Capture the number of session reconnects.
    */
   uint32 reconnectCount;

   /*
    * Current virtual time used by queuing scheduler. During "steady state",
    * this equals the msg->tag of the message containing the last chunk to have
    * been sent out via transport pipe (backend). See sendTree doc header.
    */
   int32 virtualTime;

   /*
    * List of outstanding transport async sends (VvcSendCompletionContext) that
    * have not been completed.  This supports VVCLIB_SessionTransportCancelIo,
    * which is a workaround for backend transports that do not always complete
    * IO requests.  Access must be done under the gCancelLock and session lock.
    * This list is only used when the
    * VVC_TRANSPORT_BE_ENABLE_FORCE_CANCEL_SAFE_IO is set.
    */
   DblLnkLst_Links transportSendList;

   // Collect perf counters
   Bool enablePerfCounters;
   VvcPerfCounterSet counterSet;
   // Channel counter set list
   DblLnkLst_Links featureCounterSetList;
   MXUserExclLock *counterListLock;
   PerfDbHandle perfDbHandle;

   // Message ID control
   Atomic_uint32 msgIdCtrl;

   // Size of packets used by weighted fair queuing algorithm
   uint32 pseudoPacketLength;
   uint32 pseudoPacketLengthHighBw;

   /*
    * If dispatch period is full but message remaining is
    * < maxRemainingMsgLength, send it anyway
    */
   uint32 maxRemainingMsgLength;

   uint8 disableBandwidthDetection;

   uint64 rttQueueTimeUSLocal;
   uint64 rttQueueTimeUSRemote;

   // Used to track bandwidth estimation
   VvcBandwidthDetection *bwDetection;

   // OS interrupt timer resolution
   uint32 defaultTimerRes;
   uint32 minTimerRes;
   uint32 systemMinTimerRes;
   uint32 origTimerRes;
   uint32 fixedTimerRes;
   uint32 currentTimerRes;

   // Send dispatch poll thread
#ifdef _WIN32
   HANDLE dispatchSendWakePoll;
#else
   MXUserExclLock *dispatchSendWakePollMutex;
   MXUserCondVar *dispatchSendWakePollCond;
#endif
   VThreadID dispatchSendPollTid;
   VvcDispatchSendPollCmd dispatchSendPollCmd;

   // Poll interval used to dispatch outgoing chunks in micro-seconds
   double dispatchSendIntervalUS;
   double dispatchSendIntervalSlopUS;

   // Send dispatch period control variables
   uint64 dispatchSendPeriodStart;
   double dispatchSendPeriodDuration;
   double dispatchSendPeriodRate;
   uint32 dispatchSendPeriodMaxBytes;
   uint32 dispatchSendPeriodSentBytes;
   uint32 dispatchSendPeriodCtrlSentBytes;

   // Send rate limiter
   double sendBucket;
   double sendBucketMax;
   double sendBucketLastFillTime;
   double sendBucketLastDrainTime;
   uint32 sendBucketDrainBytesMin;

   /*
    * Send dispatch timer is running.  This can either be a timeout for the
    * send thread if VVC_INSTANCE_DEFER_SEND_DISPATCH_THREAD is set, or a
    * dispatch poll has been registered.
    */
   Atomic_uint32 dispatchSendTimerRunning;

   // Registered for poll-lib message dispatch callback
   Bool dispatchPollRegistered;

   // Max in-flight bytes - only used when bandwidth detection is disabled
   uint64 maxInFlight;

   // How long the clock frequency remains for a channel after sending data
   uint64 latencyTimeoutPeriodMS;

   // Min interval used to update allowed BW consumption percentage per channel
   double channelBwMinUpdateIntervalMS;

   // Debug tracing
   uint32 traceFlags;
   FILE *traceRttHistory;
   FILE *traceDispatchSendPeriodHistory;
   FILE *traceDispatchSendNewPeriodHistory;
   FILE *traceDispatchSendPollHistory;
   uint64 traceChunkSendBatch;
   FILE *traceSendCompletionHistory;
   FILE *traceAckEventHistory;
   FILE *traceMsgDispatchHistory;
   FILE *traceSendRecvHistory;
   FILE *traceXBeGetNwStatsHistory;
   FILE *traceChanNwStatsHistory;
   uint32 traceDumpLen;

   size_t channelRecvBufInitialSize;

   /*
    * VvcEventHistory also maintains a maximumRate & minimumRate.
    * Their value would be similar as below variables but they come
    * into picture when VVC's Original TCP BwDetection is enabled.
    */
   double minBandwidth; /* bytesPerSecond */
   double maxBandwidth; /* bytesPerSecond */

   double maxBandwidthBurstMsec;

   /*
    * Locally-enabled capabilities, and the actual capabilities to be
    * used in this session after negotiation during the INIT/INIT-ACK
    * exchange with the peer.
    */
   Bool localEnableVVCCloseSeq;
   Bool localEnableVVCReconnectToken;
   Bool localDisablePluginReconnectToken;
   Bool localAllowVVCHeartbeats;
   Bool localAllowReceiveWindow;
   Bool localAllowChannelResync;
   Bool localAllowConcurrentTransports;
   Bool localEnableVVCPauseResume;
   uint8 localMptVersion;
   Bool localEnableVVCBatching;
   Bool localAllowRawChannels;

   Bool negotiatedEnableVVCCloseSeq;
   Bool negotiatedEnableVVCReconnectToken;
   Bool negotiatedDisablePluginReconnectToken;
   Bool negotiatedDoVVCHeartbeats;
   Bool negotiatedDoReceiveWindow;
   Bool negotiatedDoChannelResync;
   Bool negotiatedDoConcurrentTransports;
   Bool negotiatedEnableVVCPauseResume;
   uint8 negotiatedMptVersion;
   Bool negotiatedRawChannelSupport;

   // VVC Session Identifier (Opaque Id given to VVC Consumers)
   VvcSessionIdentifier sessionIdentifier;

   /* Message Channel related details */
   VvcMsgChannelGroupId currMsgChannelGroupId;
   uint32 currNumMsgChannels;
   DblLnkLst_Links vvcMsgChannelList;

   /*
    * Flags used during VVC close sequence. Only valid for non-proxy sessions.
    */

   // TRUE when sendCompl callback received after sending local close msg.
   Bool isLocalSessionClosed;

   // TRUE when received peer close msg
   Bool isPeerSessionClosed;

   // TRUE when final MPTAck on CtrlChan is received
   Bool isLocalVvcCloseSeqComplete;

   // TRUE only when received peer close and local close seq is complete
   Bool isVvcCloseSeqComplete;

   // Enable deferred (MPT or VVC) ACKs?
   Bool enableDeferredAcks;

   // Max elapsed time during which no MPT ACK is sent
   int32 mptAckQuietPeriod;
   // Maximum gap between rcvNxt and highAckSeq
   int32 mptAckSeqGap;
   // Max number of bytes that haven't been (MPT) acked
   int64 mptAckUnackedBytes;

   // number of VVC ACKs sent
   uint64 vvcAckSentNr;

   // number of standalone MPT ACKs sent
   uint64 mptAckSentNr;

   // Process Id for out-of-proc
   uint32 nodeProcessId;

   VvcSessionCloseParams closeParams;

   // server side counter for raw channels
   uint32 numRawChannels;
   VvcRawChanType rawChannelType;
   // unique nonce used for TCP raw channels
   uint32 nonce;
   // serial number of raw channels
   unsigned char rawChanSN;
   Bool blastSocketThreadEnabled;
   Bool tcpBweNegotiated; // TCP BWE negotiated on control asock

   // cached stats of raw channel with maximum observable bandwidth
   double cachedBW; // bytes per second
   double cachedBWTime;
   uint32 cachedRTT; // ms
   uint32 cachedRawChannelId;
};


typedef struct _VvcMPTMessage VvcMPTMessage;
typedef struct _VvcMessageSendData VvcMessageSendData;


// Channel instance definition
struct _VvcChannel {
   VvcCommon common;

   // Back pointer to parent session
   DblLnkLst_Links sessionLink;
   VvcSession *session;

   // Reference to channel listener
   VvcListener *listener;

   uint32 channelId;

   // The channel name if listener has a wildcard
   char *name;
   // The channel's feature name
   char *featureName;

   // Indicates this session is in the process of being shutdown
   VvcChannelState state;

   VvcCloseChannelReason closeReason;

   uint32 priority;
   uint32 latency;
   uint64 latencyTimeoutMS;
   uint32 trafficType;
   uint32 priorityVersion;
   uint32 timeout;
   uint32 flags;

   VvcChannelEvents events;

   /*
    * First in first out (FIFO) message send queue for this channel.
    * Its essential job is is to store messages in the VVC layer in original
    * send order until entirely sent through pipe (transport backend).
    *
    * The "steady state" (meaning, outside of the scheduler running --
    * VvcDoDispatchSendQueues()) properties of this queue are as follows:
    *
    * This stores every VvcMsg* whose buffer has been given to VVCLIB_Send()
    * that still has some data that needs to be passed to the network pipe
    * (sent). They are ordered from least to most recently passed to
    * VVCLIB_Send().
    *
    * Assuming the queue isn't empty, its first element M0 represents the
    * message N bytes long, where M of those N bytes have already been sent off;
    * then, M0->remaining = N - M; the next chunk to be sent for this channel
    * (perhaps in next VvcDoDispatchSendQueues()) starts at (M0->buf + M)
    * and is nextChunkLen bytes long. Note that M0->nextChunkLen may well not
    * take us up through all N bytes. Once that chunk is sent off,
    * M0->remaining is adjusted accordingly, and M0->nextChunkLen is re-decided
    * for the next round.
    *
    * Once (M0->remaining == 0) is reached somewhere inside the scheduler
    * (VvcDoDispatchSendQueues()), M0 has all been sent out and thus no longer
    * belongs in this queue and is popped.
    *
    * Thus, all messages except/beyond head message M0 are completely unsent;
    * whereas the head message M0 is partially unsent.
    *
    * Relationship with channel->session->sendTree (for non-control channel):
    * For each channel C, sendTree (common to all the
    * channels of a session) stores the VvcMsg* M0 such that the next bytes to
    * send are M0->nextChunkLen bytes from within M0->buf[], according to
    * M0->remaining. M0 is the same VvcMsg* at the head of C->sendQueue.
    * Therefore, once M0 is entirely sent off (M0->remaining reaches 0),
    * it is removed from ->sendTree *and* from C->sendQueue; and then
    * the new head message M0 in C->sendQueue is added into ->sendTree
    * (unless no more messages queued). C->sendQueue exists
    * as _the_ source and ordering of messages to feed to C->session->sendTree,
    * for that particular channel C.
    *
    * Behavior under Network Continuity: Suppose the transport pipe enters
    * the Recovery phase (asockXBeDown becomes true). During this phase,
    * no messages are queued in this queue, because its purpose is to feed
    * message chunks to the pipe, but the pipe is down during recovery.
    * Once recovery does (successfully) end (asockXBeDown is again FALSE),
    * a new pipe (transport backend) is ready to go. sendQueue is then
    * loaded up with messages to send ASAP (see mptSendQ) -- in much the same
    * way as if they had been VVCLIB_Send()ed during normal operation.
    * Then the messages are sent off in normal fashion (through sendTree, etc.).
    *
    * Note that sendQueue itself holds no information related to Network
    * Continuity or related Recovery (and, again, is not touched when in
    * Recovery). Instead, it is manipulated (using MPT code and
    * data structures) when Network Continuity becomes relevant (essentially
    * merely once Recovery ends).
    *
    * Buffer management: See similar note on mptSendQ.
    *
    * [*] Footnote: There appears to be a corner case, wherein mptSendQ-related
    * sequence # wrap-around concerns cause only messages up to but
    * excluding ->schedNxt[Msg] message in ->mptSendQ to actually be
    * in this sendQueue. So the "every" wording is accurate assuming this
    * wrap-around situation does not occur. See the loop that enqueues onto
    * sendQueue from mptSendQ (currently: VvcQueueChannelMessagesToSendTree()).
    */
   DblLnkLst_Links sendQueue;

   /*
    * Multi-protocol transport (MPT) send queue: Tracks message information
    * needed for enabling Network Continuity feature.
    * Network Continuity's main data structures are mptSendQ and
    * pointers into its nodes -- sndUnaMsg, sndNxtMsg --
    * and the associated sequence numbers -- sndUna, sndNxt.
    *
    * Basic idea of MPT is that, given channel C, each message M _fully_
    * received by receiver is replied to with an MPT ACK sent back to sender.
    * (Each message is assigned a single incremental sequence number.)
    * Network Continuity means that, once Recovery begins
    * (transport 1 breaks down; replacement transport 2 begins spinning up)
    * and successfully ends (transport 2 is ready to use), we have recorded
    * which messages have not yet been MPT-ACKed and should be retransmitted.
    * Given that basic idea, the details are:
    *
    * mptSendQ stores VvcMPTMessage* elements (each VvcMPTMessage storing, among
    * other things, the pointer VvcMsg* to the original message created by
    * the original VVCLIB_Send()). Namely, there is
    * a VvcMPTMessage* for EVERY message from VVCLIB_Send(), except those
    * that have been acknowledged as fully received by the other side.
    *
    * snd{Nxt|Una}[Msg] are key sequence numbers [and corresponding
    * pointers into mptSendQ]. They're documented individually below.
    *
    * So what's the point of tracking mptSendQ and snd{Nxt|Una}[Msg]
    * specifically? Suppose Recovery begins. Any VVCLIB_Send()ed messages
    * during this time will be queued on mptSendQ as usual. Now suppose
    * Recovery ends successfully: the new transport pipe is ready to use.
    * Every message starting with sndUna'th one and later is a lost cause,
    * as at best it arrived through the old, broken transport and can never
    * be ack'ed now. Therefore, sndNxt[Msg] is set ("rewound") to equal
    * sndUna[Msg], and all messages in mptSendQ are re-queued onto an empty
    * sendQueue. That re-queueing is as if all the messages starting with sndUna
    * were just VVCLIB_Send()ed. Now, the usual sending/scheduling algorithm is
    * resumed as normal; this has the effect of retransmitting everything from
    * sndUna on via the new transport.
    *
    * Buffer management: The contract with the user is that once message M
    * from some VVCLIB_Send(buf, bufLen) call has been sent via the network
    * (which is supposed to be a reliable operation), the buf/bufLen buffer
    * is to be freed by VVC. We do indeed do this now: buf[] is stored in VvcMsg
    * and once sent off through transport backend it is freed; and
    * VvcMsg::buf = NULL is set. However, given that actually we might need
    * to resend (retransmit; see above) that message in the event of
    * NC/Recovery, a copy of buf[] is stored in that VvcMsg's corresponding
    * VvcMPTMessage (which also has the VvcMsg* pointer). Thus, VvcMsg::buf[]
    * is restored from the VvcMPTMessage copy if/when necessary. (Once
    * the message is acked via MPT ACK, the VvcMPTMessage is destroyed too;
    * so that "backup" buffer will be destroyed; as there's no longer any
    * future need to retransmit it.) This explains why
    * VvcMPTMessage has a buf/bufLen, even though its VvcMsg does also. In fact
    * that's (at least partly) the reason VvcMPTMessage exists at all (as
    * opposed to just VvcMsg). TODO: There is probably some more elegant way
    * to do this, avoiding the buffer copy and the two buf[Len] member pairs;
    * and possibly even avoiding need for VvcMPTMessage at all.
    * Speculation: it is currently done this way in order to keep the existing
    * pre-NC logic/structures unchanged while adding NC logic.
    */
   DblLnkLst_Links mptSendQ;

   /*
    * Sequence # to encode in the next message chunk sent (over transport
    * backend/pipe) for this channel. Incremented whenever a message's
    * ->remaining reaches 0. See mptSendQ doc header for context/algorithm info.
    *
    * In particular, sndUna == sndNxt if and only if all messages that could
    * possibly be acknowledged by other side have indeed
    * been acked. (Recall sndNxt points just past the last message to have been
    * fully sent out; and sndUna points just past the last message to have been
    * fully received and so acked by other side.)
    */
   SeqNum_16 sndNxt;
   /*
    * The VvcMPTMessage* in mptSendQ corresponding to sequence # sndNxt.
    * TODO: It appears this is only used in ASSERT()s. Consider eliminating
    * it for performance. (sndNxt, however, is ultra-critical!)
    */
   VvcMPTMessage *sndNxtMsg;

   /*
    * Sequence # of the message just past the last message such that the other
    * side has fully received it and successfully acknowledged that fact to
    * this side. See mptSendQ doc header for details/context/algorithm info.
    */
   SeqNum_16 sndUna;
   /*
    * The VvcMPTMessage* in mptSendQ corresponding to sequence # sndUna.
    * This should be the head message in mptSendQ.
    */
   VvcMPTMessage *sndUnaMsg;

   /*
    * This equals the first VvcMPTMessage* in mptSendQ such that it has not
    * been enqueued onto sendQueue; NULL if all have been enqueued (which
    * is probably typical). See footnote in sendQueue's doc header.
    */
   VvcMPTMessage *schedNxtMsg;
   // MPT sequence # corresponding to schedNxtMsg.
   SeqNum_16 schedNxt;

   /*
    * Maximal sndNxt value seen since last purge of mptSendQ (which, as of
    * this writing, really should only occur when closing/aborting channel).
    * So essentially this is sndNxt that doesn't reset across NC Recovery
    * phases.
    */
   SeqNum_16 highSeq;

   /*
    * The most recent timestamp when we sent an MPT ACK (either standalone or
    * piggy-backed)
    */
   uint64 lastMptAckSendTs;

   // The number of bytes in all currently unacked MPT messages
   int64 mptUnackedBytes;

   // Next expected receive sequence # (i.e., first seq.# we've not received).
   SeqNum_16 rcvNxt;

   /*
    * The highest ackSeq (for received messages) that we have sent back to the
    * sender. If this number equals rcvNxt, then we're all caught up in
    * acknowledging incoming messages.
    */
   SeqNum_16 highAckSeq;

   /*
    * Priority queue of all messages received so far such that each is
    * strictly past (having sequence # strictly greater than) rcvNxt,
    * the latter indicating the first "gap" in the received data, as ordered
    * by sequence #; highest-priority message is defined as that with lowest
    * sequence #. Each message is stored as a VVC receive buffer (buffer
    * with a VvcRecvBufHdr at the start).
    *
    * This handles messages that arrive out of order. Reasons why this might
    * occur despite a reliable transport underneath are outside the scope of
    * this comment. Essentially, each new message either has sequence # = rcvNxt
    * and can be passed on to consumer; or is added to this queue. If the
    * former occurs, it may fill the gap right up to the lowest-sequence-#
    * contiguous element(s) in this queue, in which case those messages can
    * also all be immediately passed on to consumer ("reassembled") and popped
    * from the queue.
    */
   VvcPriorityQBySeqOfRecvMsgs *rcvMsgReassemblyQ;

   /*
    * Is the MPT SendQ being or having been purged (by VvcPurgeMPTSendQueue)?
    * This is to ensure we don't (by accident) do any linked list operation on
    * the MPT send queue.
    */
   Bool mptSendQueuePurged;

   /*
    * Number of transport sends that have not been completed (protected by
    * session lock)
    */
   uint32 outstandingSends;

   /*
    * The latest cached value for Allowed Bandwidth Consumption percentage for
    * this channel.
    *
    * This is computed when dispatch simulator runs are triggered by the
    * VvcInfoChannelInfo queries every channelBwMinUpdateIntervalMS or so.
    * chanBwCacheNextUpdateTimeUS is the time for next update.
    */
   double cachedAllowedBwConsumptionPercentage;
   uint64 chanBwCacheNextUpdateTimeUS;
   Bool chanBwCacheUpdateInProgress;

   /*
    * Indicates an onClose has been queued for this channel (protected by
    * session lock)
    */
   Bool channelOnCloseQueued;

   /*
    * Indicates a pending onClose has to be queued for this channel after
    * all outstanding sends are done.
    */
   Bool pendingQueueOnCloseAfterSends;

   VvcPerfCounterSet counterSet;
   PerfDbHandle perfDbHandle;

   /*
    * A list of recv'ed messages waiting to be retrieved for a
    * VVC_CHANNEL_USE_RECV_BUFFER channel
    */
   DblLnkLst_Links pendingRecvMsgs;
   /*
    * Number of bytes in the first pendingRecvMsgs message that has already
    * been delivered
    */
   size_t pendingDeliveredLen;

   /*
    * Extension provided recv buffer for a VVC_CHANNEL_USE_RECV_BUFFER
    * channel
    */
   uint8 *recvBuffer;
   size_t recvBufferLen;
   size_t recvBufferRecvMin;
   size_t recvBufferRecvLen;

   size_t recvBufferInitialSize;

   /*
    * This message send data pointer is only to be used by the dispatch code.
    * It stores the current active message chunk being sent and related
    * metadata.
    */
   VvcMessageSendData *messageData;

   /*
    * Number of dispatched recv messages that have not been subsequently
    * completed with a call to VVC_RecvComplete
    */
   Atomic_uint32 outstandingRecvs;

   uint32 traceFlags;

   /*
    * Number of bytes queued by a channel at any given time.
    * - Incremented when a new VvcMsg is added to this channel's sendQueue.
    * - Decremented when a VvcChunk belonging to any VvcMsg on
    *   this channel's sendQueue is handed over to transportBe.send.
    */
   uint64 currentQueuedBytes;
   /*
    * Sum of all bytes queued by a channel throughout its lifetime.
    * Reset only when the channel is to be closed/destroyed.
    */
   uint64 totalQueuedBytes;

   // Received bytes
   uint64 recvedBytes;

   // Sent VVC messages
   uint64 sentMessages;

   // Received VVC messages
   uint64 recvedMessages;

   // Total time active (Used to calculate sendThroughput).
   uint64 activeTimeMs;

   // Send Throughput
   double sendThroughput;

   // Peer send Throughput
   double peerSendThroughput;

   uint64 totalQueuedBytesInDb;
   uint64 recvedBytesInDb;
   uint64 sentMessagesInDb;
   uint64 recvedMessagesInDb;

   /*
    * When a VvcChunk belonging to any VvcMsg on this channel's
    * sendQueue is to be given to transportBe.send, record length
    * of that chunk.
    * The currentBeSendSizeAvg is the running average of such
    * sent chunk size values.
    */
   uint64 currentBeSendSizeAvg;

   // queueing time gets reported as a perf metric
   uint32 perfSendQueueTimeUS;

   struct vvcRawChanContext *rawCtx;
};


typedef struct _VvcOpenChan {
   VvcCommon common;

   DblLnkLst_Links sessionLink;
   VvcSession *session;

   VvcListener *listener;

   uint32 channelId;
   uint32 priority;
   uint16 timeout;
   uint32 flags;

   char *name;

   // initialData from op request
   uint32 initialDataLen;
   void *initialData;
   // initialData for ack response
   uint32 ackInitialDataLen;
   void *ackInitialData;

   uint8 rawChannelType;
   union {
      struct {
         VvcChannel *channel; // save ptr for delayed accept on client side
         uint16 port;
      } beat;
      struct {
         uint32 nonce;
         uint8 serialNo;
      } tcp;
      struct {
         // combined fields from beat and tcp
         VvcChannel *channel;
         uint16 port;
         uint32 nonce;
         uint8 serialNo;
      } vvc;
   } raw;
} VvcOpenChan;


typedef struct _VvcOpenChanAck {
   uint32 channelId;
   VvcStatus status;

   uint32 initialDataLen;
   void *initialData;

} VvcOpenChanAck;


typedef enum { VVC_MSG_PERF_TAGGED = 1 << 0 } VvcMsgFlags;


// If a VvcMsg should be pinned to a specific AsockBe
typedef enum {
   VvcMsgPinToDefault, // pin VvcMsg to whichever is Active AsockBe
   VvcMsgPinToTCP,     // pin VvcMsg to TCP AsockBe
   VvcMsgPinToBEAT     // pin VvcMsg to BEAT AsockBe

} VvcMsgPinToTransportType;


// Message entry
typedef struct _VvcMsg {
   VvcCommon common;

   DblLnkLst_Links channelLink;
   VvcChannel *channel;

   // Extension allocated buffer
   uint8 *buf;
   size_t bufLen;

   // track how long a message has been in the queue
   uint64 createTimeUS;

   /*
    * Indicates a control message that has been queued on a channel queue.  This
    * allows control message to be sent inline with other channel messages
    */
   Bool inlineCtrlMsg;
   // Reference to control channel
   VvcChannel *ctrlChannel;

   /*
    * Is this an internal MPT message? These are MPT ack, or retransmitted
    * channel messages. Normal send completion callback shouldn't be invoked on
    * MPT internal messages.
    */
   Bool isMptInternal;
   /*
    * TODO We can do better when indicating whether a message is rexmit or not,
    * should refactor the retransmission code to simply the logic, and
    * potentially get rid of isRexmitMsg.
    */
   Bool isRexmitMsg;

   // The mptMsg which tracks this VvcMsg
   VvcMPTMessage *mptMsg;

   uint32 msgId;
   uint32 flags;

   // Sending state variables
   size_t nextChunkLen;
   size_t remaining;
   int32 tag;

   // AsockID upon which all chunks of this VvcMsg should be sent
   int sendAsockID;

   // TRUE if the VvcMsg is a Ctrl Msg used by the VVC BW Estimator
   Bool isVvcBweCtrlMsg;

   // Track if a VvcMsg needs to be pinned to a specific Transport Type
   VvcMsgPinToTransportType pinToTransport;

   // Completion status
   VvcStatus status;

} VvcMsg;


// MPT Message
struct _VvcMPTMessage {
   DblLnkLst_Links sendQLink;
   uint32 channelId;
   uint8 *buf;
   size_t bufLen;
   Bool inlineCtrlMsg;
   Bool isVvcBweCtrlMsg;
   VvcMsg *vvcMsg;
};


// Send completion context
typedef struct _VvcSendCompletionContext {
   VvcCommon common;

   DblLnkLst_Links sessionLink;
   DblLnkLst_Links cancelledLink;
   DblLnkLst_Links batchingLink;

   VvcSCCBatcher *sccBatcher;

   uint64 queuedTime;
   uint64 queuedDuration;

   VvcMsg *msg;

   // partial chunk buffer and length
   void *chunkBuf;
   size_t chunkLen;

   uint32 flags;

   VvcStatus status;

   uint32 cancelGeneration;
   Bool sendCompletedWhileCancelPending;

} VvcSendCompletionContext;


// Recv completion context
struct _VvcRecvCompletionContext {
   uint32 magic;

   DblLnkLst_Links link;
   VvcRecvBufHdr *recvBuf;
   VvcSession *session;
};


// VvcEvChannelOnSendComplete event data
typedef struct _VvcSendCompleteEventData {
   uint8 *buf;
   size_t bufLen;

   VvcStatus status;

   uint32 msgId;
   void *clientData;
} VvcSendCompleteEventData;


// Used for batching VvcSendCompletionContexts
struct _VvcSCCBatcher {
   uint32 magic;

   VvcSession *session;

   MXUserExclLock *sccListLock;
   DblLnkLst_Links sccList;
   uint32 numSCCs;

   int sendAsockID;

   uint8 *buf;
   size_t bufLen;
   int usefulSendBytes;

   Bool incrementSendBytes;
};


// Forward declarations
typedef struct _VvcDispatchSimulator VvcDispatchSimulator;
typedef struct _VvcDispatchSimulatorNode VvcDispatchSimulatorNode;
typedef struct _VvcDispatchSimulatorNodeData VvcDispatchSimulatorNodeData;


/*
 * 1. When TransportBe is UdpV2, Vvc needs to expose allowedBwConsumption for
 *    a channel, taking into account the avg size of data being sent by all
 *    open channels.
 *
 * 2. On these per-channel running averages, we run a simplified version of
 *    Vvc's tag based scheduler loop & calculate the allowed portion of Bw
 *    for a channel based on the output.
 *
 * 3. VvcDispatchSimulator: parent struct which abstracts the high level
 *                          details required for this scheduler loop simulation.
 *
 * 4. VvcDispatchSimulatorNode: One node per currently opened channel. Stores
 *                              the currentBeSendSizeAvg as size of "data" the
 *                              channel would have put on wire.
 *
 * 5. VvcDispatchSimulatorNodeData: Output of running scheduler loop. Allowed
 *                                     Bw portion is calculated using these.
 *
 * 6. See comments in func VvcGetAllowedBwConsumptionForChannel() for more details.
 */

// VvcDispatchSimulator
struct _VvcDispatchSimulator {
   uint32 pseudoPacketLength;
   uint32 pseudoPacketLengthHighBw;

   // A sorted tree that is used in simulating the scheduling algorithm on
   // per channel's currentBeSendSizeAvg
   RbtInt32 *sendTree;

   // Virtual time used by queuing scheduler while mimicking the scheduling
   // algorithm
   int32 virtualTime;

   double xBeBwRateBytesPerSecond;
   double dispatchSendPeriodDuration;
   uint64 bytesToSend;

   /*
    * A TransportBackend object with only getNetworkStats callback ptr
    * This is required since VvcDispatchSimulator should not directly
    * access any of VvcSession's members since we don't hold Session Lock
    * in any of the VvcDispatchSimulator_* functions
    */
   VvcTransptBackend transportBe;

   // A list of VvcDispatchSimulatorNode objects - one per opened channel
   // Each node will only hold the currentSendSizeAvg for the channel
   DblLnkLst_Links nodeList;
};


// VvcDispatchSimulatorNode
struct _VvcDispatchSimulatorNode {
   Bool isGetInfoChannel;

   size_t bufLen;

   size_t nextChunkLen;
   size_t remaining;
   int32 tag;

   char *chanName;
   uint32 chanPriority;

   uint64 currentBeSendSizeAvg;

   VvcDispatchSimulatorNodeData *nodeData;

   DblLnkLst_Links link;
};


// VvcDispatchSimulatorNodeData
struct _VvcDispatchSimulatorNodeData {
   size_t chunkLen;
   VvcDispatchSimulatorNode *node;
   DblLnkLst_Links link;
};


// Enum VvcXBeGetNwStatsQueryReason
// Add an entry below if xBe.getNetworkStats is invoked for a new reason
typedef enum {
   VvcQueryReasonDefault,
   VvcQueryReasonGetSessionInfo,
   VvcQueryReasonGetChannelInfo,
   VvcQueryReasonQueueMessage,
   VvcQueryReasonDoDispatchSendQueues

} VvcXBeGetNwStatsQueryReason;

typedef enum {
   VvcMsgChannelConnecting,
   VvcMsgChannelConnected,
   VvcMsgChannelClosed

} VvcMsgChannelState;

struct _VvcMsgChannel {
   VvcCommon common;

   DblLnkLst_Links link;
   VvcSessionIdentifier sessionId;
   VvcMsgChannelIdentity identity;
   VvcMsgChannelEvents events;
   VvcMsgChannelGroupId msgChannelId;
   VvcMsgChannelState state;
   uint32 pluginPId;
};

typedef struct _VvcMsgChannelMetadata {
   DblLnkLst_Links link;
   VvcCommonHandle nodeHandle;
   VvcMsgChannel *msgChannel;
} VvcMsgChannelMetadata;

#pragma pack(push, 1)
typedef struct {
   VvcSessionIdentifier sessionId;
   VvcMsgChannelIdentity identity;
   int32 pluginPId;
   uint64 reqId;

} VvcMsgChannelOpenChannelReq;
#pragma pack(pop)

#pragma pack(push, 1)
typedef struct {
   Bool accepted;
   VvcSessionIdentifier sessionId;
   VvcMsgChannelGroupId msgChannelId;
   uint64 reqId;

} VvcMsgChannelOpenChannelResp;
#pragma pack(pop)

#pragma pack(push, 1)
typedef struct {
   VvcSessionIdentifier sessionId;
   VvcMsgChannelGroupId msgChannelId;

} VvcMsgChannelCloseChannel;
#pragma pack(pop)

#pragma pack(push, 1)
typedef struct {
   VvcSessionIdentifier sessionId;
   VvcMsgChannelGroupId srcMsgChannelId;
   VvcMsgChannelGroupId destGroupId;
   VvcMsgChannelInfo srcMsgChannelInfo;
   uint32 msgLen;
   // Msg to send

} VvcMsgChannelSend;
#pragma pack(pop)

#pragma pack(push, 1)
typedef struct {
   VvcSessionIdentifier sessionId;
   VvcMsgChannelGroupId srcMsgChannelId;
   VvcMsgChannelGroupId destGroupId;
   VvcMsgChannelGroupId destMsgChannelId;
   VvcMsgChannelInfo srcMsgChannelInfo;
   uint32 msgLen;
   // Msg to send

} VvcMsgChannelRecv;
#pragma pack(pop)


// In the very bad network scenarios, report a Floor percentage
// value instead of reporting zero.
#define ALLOWED_BWCONSUMPTION_PERCENT_FLOOR 10


/*
 * Send completion context flags
 */

// Indicates partial chunk of a user message
#define VVC_SEND_COMPLETION_CONTEXT_PART 0x01
// Indicates the last chunk of a user message
#define VVC_SEND_COMPLETION_CONTEXT_LAST 0x02
// The sent data is a chunk header
#define VVC_SEND_COMPLETION_CONTEXT_CHUNK_HDR 0x04


/*
 * Session snapshot list.  Used to temporarily store a list
 * of sessions that require notifying when a listener is activated
 */
typedef struct VvcSessionSnapshot {
   DblLnkLst_Links link;
   VvcSession *session;

} VvcSessionSnapshot;


/*
 * VVC locks should rank no higher than VVC_RANK_LEAF.
 */
#define VVC_RANK_LEAF RANK_libLockBase

#define VVC_RAW_CHANNEL_LOCK VVC_RANK_LEAF - 5
#define VVC_PERFCOUNTER_RANK VVC_RANK_LEAF - 4
#define VVC_CANCEL_RANK VVC_RANK_LEAF - 3
#define VVC_GLOBAL_RANK VVC_RANK_LEAF - 2
#define VVC_SESSION_RANK VVC_RANK_LEAF - 1
#define VVC_INSTANCE_RANK VVC_RANK_LEAF

/*
 * Set map lock to RANK_UNRANKED to avoid breaking up sLock at multiple places in
   vvcProxyServer and vvcProxyForwarder. The map lock is strictly used for map
   operation only and does not use any other locks within its scope.
 */
#define VVC_GLOBAL_HANDLETOTOKENMAPLOCK_RANK RANK_UNRANKED

// TODO: There's no reason for all these not to be functions.
#define LOCK_INSTANCE(instance) MXUser_AcquireExclLock((instance)->instanceLock)
#define UNLOCK_INSTANCE(instance) MXUser_ReleaseExclLock((instance)->instanceLock)
#define ISLOCKED_INSTANCE(instance) MXUser_IsCurThreadHoldingExclLock((instance)->instanceLock)

#define LOCK_SESSION(sess) MXUser_AcquireExclLock((sess)->sessLock)
#define UNLOCK_SESSION(sess) MXUser_ReleaseExclLock((sess)->sessLock)
#define ISLOCKED_SESSION(sess) MXUser_IsCurThreadHoldingExclLock((sess)->sessLock)

#define LOCK_LISTENER(listener) LOCK_INSTANCE((listener)->instance)
#define UNLOCK_LISTENER(listener) UNLOCK_INSTANCE((listener)->instance)
#define ISLOCKED_LISTENER(listener) ISLOCKED_INSTANCE((listener)->instance)

#define LOCK_CHANNEL(chan) LOCK_SESSION((chan)->session)
#define UNLOCK_CHANNEL(chan) UNLOCK_SESSION((chan)->session)
#define ISLOCKED_CHANNEL(chan) ISLOCKED_SESSION((chan)->session)


/* ----------------------------------------------------------
 *  Instance reference counting macro definitions
 * ----------------------------------------------------------
 */

// ==========================================================

#define VVC_ADD_REF_INSTANCE(instance, tag) VvcAddRefInstance((instance), (tag), __FUNCTION__)

#define VVC_RELEASE_INSTANCE(instance, tag) VvcReleaseInstance((instance), (tag), __FUNCTION__)

// ==========================================================

#define VVC_GET_INSTANCE(handle, instance)                                                         \
   VvcGetHandleFromTokenInt((handle), VvcTagGetHandleFromToken, __FUNCTION__,                      \
                            (VvcCommonHandle *)(&(instance)))

#define VVC_YIELD_INSTANCE(instance) VVC_RELEASE_INSTANCE((instance), VvcTagGetHandleFromToken)

// ==========================================================

#define VVC_GET_INSTANCE_TAG(handle, instance, tag)                                                \
   VvcGetHandleFromTokenInt((handle), (tag), __FUNCTION__, (VvcCommonHandle *)(&(instance)))

#define VVC_YIELD_INSTANCE_TAG(instance, tag) VVC_RELEASE_INSTANCE((instance), (tag))

// ==========================================================


/* ----------------------------------------------------------
 *  Listener reference counting macro definitions
 * ----------------------------------------------------------
 */

// ==========================================================

#define VVC_ADD_REF_LISTENER(listener, tag) VvcAddRefListener((listener), (tag), __FUNCTION__)

#define VVC_RELEASE_LISTENER(listener, tag) VvcReleaseListener((listener), (tag), __FUNCTION__)

// ==========================================================

#define VVC_GET_LISTENER(handle, listener)                                                         \
   VvcGetHandleFromTokenInt((handle), VvcTagGetHandleFromToken, __FUNCTION__,                      \
                            (VvcCommonHandle *)(&(listener)))

#define VVC_YIELD_LISTENER(listener) VVC_RELEASE_LISTENER((listener), VvcTagGetHandleFromToken)

// ==========================================================

#define VVC_GET_LISTENER_TAG(handle, listener, tag)                                                \
   VvcGetHandleFromTokenInt((handle), (tag), __FUNCTION__, (VvcCommonHandle *)(&(listener)))

#define VVC_YIELD_LISTENER_TAG(listener, tag) VVC_RELEASE_LISTENER((listener), (tag))

// ==========================================================


/* ----------------------------------------------------------
 *  Session reference counting macro definitions
 * ----------------------------------------------------------
 */

// ==========================================================

#define VVC_ADD_REF_SESSION(session, tag) VvcAddRefSession((session), (tag), __FUNCTION__)

#define VVC_RELEASE_SESSION(session, tag) VvcReleaseSession((session), (tag), __FUNCTION__)

// ==========================================================

#define VVC_GET_SESSION(handle, session)                                                           \
   VvcGetHandleFromTokenInt((handle), VvcTagGetHandleFromToken, __FUNCTION__,                      \
                            (VvcCommonHandle *)(&(session)))

#define VVC_YIELD_SESSION(session) VVC_RELEASE_SESSION((session), VvcTagGetHandleFromToken)

// ==========================================================

#define VVC_GET_SESSION_TAG(handle, session, tag)                                                  \
   VvcGetHandleFromTokenInt((handle), (tag), __FUNCTION__, (VvcCommonHandle *)(&(session)))

#define VVC_YIELD_SESSION_TAG(session, tag) VVC_RELEASE_SESSION((session), (tag))

// ==========================================================


/* ----------------------------------------------------------
 *  Channel reference counting macro definitions
 * ----------------------------------------------------------
 */

// ==========================================================

#define VVC_ADD_REF_CHANNEL(channel, tag) VvcAddRefChannel((channel), (tag), __FUNCTION__)

#define VVC_RELEASE_CHANNEL(channel, tag) VvcReleaseChannel((channel), (tag), __FUNCTION__)

// ==========================================================

#define VVC_GET_CHANNEL(handle, channel)                                                           \
   VvcGetHandleFromTokenInt((handle), VvcTagGetHandleFromToken, __FUNCTION__,                      \
                            (VvcCommonHandle *)(&(channel)))

#define VVC_YIELD_CHANNEL(channel) VVC_RELEASE_CHANNEL((channel), VvcTagGetHandleFromToken)

// ==========================================================

#define VVC_GET_CHANNEL_TAG(handle, channel, tag)                                                  \
   VvcGetHandleFromTokenInt((handle), (tag), __FUNCTION__, (VvcCommonHandle *)(&(channel)))

#define VVC_YIELD_CHANNEL_TAG(channel, tag) VVC_RELEASE_CHANNEL((channel), (tag))

// ==========================================================


/* ----------------------------------------------------------
 *  hubData reference counting macro definitions
 * ----------------------------------------------------------
 */

// ==========================================================

#define VVC_ADD_REF_HUB(hub) AddRefHub((hub), __FUNCTION__)

#define VVC_RELEASE_HUB(hub) ReleaseHub((hub), __FUNCTION__)

// ==========================================================

#define VVC_GET_HUB(handle, hub)                                                                   \
   VvcGetHandleFromTokenInt((handle), VvcTagGetHandleFromToken, __FUNCTION__,                      \
                            (VvcCommonHandle *)(&(hub)))

#define VVC_YIELD_HUB(hub) VVC_RELEASE_HUB((hub))

// ==========================================================


/* ----------------------------------------------------------
 *  listenHubData reference counting macro definitions
 * ----------------------------------------------------------
 */

// ==========================================================

#define VVC_ADD_REF_LISTENHUB(listenHub) AddRefListenHub(listenHub, __FUNCTION__)

#define VVC_RELEASE_LISTENHUB(listenHub) ReleaseListenHub(listenHub, __FUNCTION__)

// ==========================================================

#define VVC_GET_LISTENHUB(handle, listenHub)                                                       \
   VvcGetHandleFromTokenInt((handle), VvcTagGetHandleFromToken, __FUNCTION__,                      \
                            (VvcCommonHandle *)(&(listenHub)))

#define VVC_YIELD_LISTENHUB(listenHub) VVC_RELEASE_LISTENHUB((listenHub))

// ==========================================================


/* ----------------------------------------------------------
 *  channelFwd reference counting macro definitions
 * ----------------------------------------------------------
 */

// ==========================================================

#define VVC_ADD_REF_CHANNELHUB(channel) AddRefChannelHub(channel, __FUNCTION__)

#define VVC_RELEASE_CHANNELHUB(channel) ReleaseChannelHub(channel, __FUNCTION__)

// ==========================================================

#define VVC_GET_CHANNELHUB(handle, channel)                                                        \
   VvcGetHandleFromTokenInt((handle), VvcTagGetHandleFromToken, __FUNCTION__,                      \
                            (VvcCommonHandle *)(&(channel)))

#define VVC_YIELD_CHANNELHUB(channel) VVC_RELEASE_CHANNELHUB((channel))

// ==========================================================


/* ----------------------------------------------------------
 *  nodeData reference counting macro definitions
 * ----------------------------------------------------------
 */

// ==========================================================

#define VVC_ADD_REF_NODE(nodeData) AddRefNode(nodeData, __FUNCTION__)

#define VVC_RELEASE_NODE(nodeData) ReleaseNode(nodeData, __FUNCTION__)

// ==========================================================

#define VVC_GET_NODE(handle, nodeData)                                                             \
   VvcGetHandleFromTokenInt((handle), VvcTagGetHandleFromToken, __FUNCTION__,                      \
                            (VvcCommonHandle *)(&(nodeData)))

#define VVC_YIELD_NODE(nodeData) VVC_RELEASE_NODE((nodeData))

// ==========================================================


/* ----------------------------------------------------------
 *  listenData reference counting macro definitions
 * ----------------------------------------------------------
 */

// ==========================================================

#define VVC_ADD_REF_LISTENNODE(listener) AddRefListenNode(listener, __FUNCTION__)

#define VVC_RELEASE_LISTENNODE(listener) ReleaseListenNode(listener, NULL, __FUNCTION__)

#define VVC_RELEASE_LISTENNODE_SAFE(listener, freed)                                               \
   ReleaseListenNode(listener, freed, __FUNCTION__)

// ==========================================================

#define VVC_GET_LISTENNODE(handle, listener)                                                       \
   VvcGetHandleFromTokenInt((handle), VvcTagGetHandleFromToken, __FUNCTION__,                      \
                            (VvcCommonHandle *)(&(listener)))

#define VVC_YIELD_LISTENNODE(listener) VVC_RELEASE_LISTENNODE((listener))

// ==========================================================


/* ----------------------------------------------------------
 *  channelData reference counting macro definitions
 * ----------------------------------------------------------
 */

// ==========================================================

#define VVC_ADD_REF_CHANNELNODE(channelData) AddRefChannelNode(channelData, __FUNCTION__)

#define VVC_RELEASE_CHANNELNODE(channelData) ReleaseChannelNode(channelData, __FUNCTION__)

// ==========================================================

#define VVC_GET_CHANNELNODE(handle, channelData)                                                   \
   VvcGetHandleFromTokenInt((handle), VvcTagGetHandleFromToken, __FUNCTION__,                      \
                            (VvcCommonHandle *)(&(channelData)))

#define VVC_YIELD_CHANNELNODE(channelData) VVC_RELEASE_CHANNELNODE((channelData))

// ==========================================================


/* ----------------------------------------------------------
 *  ProxyMsgChannel reference counting macro definitions
 * ----------------------------------------------------------
 */

// ==========================================================


#define VVC_ADD_REF_PROXY_MSGCHANNEL(msgChannel) AddRefProxyMsgChannel(msgChannel, __FUNCTION__)

#define VVC_RELEASE_PROXY_MSGCHANNEL(msgChannel) ReleaseProxyMsgChannel(msgChannel, __FUNCTION__)

// ==========================================================

#define VVC_GET_PROXY_MSGCHANNEL(handle, msgChannel)                                               \
   VvcGetHandleFromTokenInt((handle), VvcTagGetHandleFromToken, __FUNCTION__,                      \
                            (VvcCommonHandle *)(&(msgChannel)))

#define VVC_YIELD_PROXY_MSGCHANNEL(msgChannel) VVC_RELEASE_PROXY_MSGCHANNEL((msgChannel))

// ==========================================================


/* ----------------------------------------------------------
 *  VVC token handle mapping function definitions
 * ----------------------------------------------------------
 */

// ==========================================================

Bool VvcAddTokenToHandleMapping(VvcHandleType handleType, VvcCommonHandle objectHandle, int32 id,
                                char *name);

void VvcRemoveTokenFromHandleMapping(VvcCommonHandle tokenHandle, const char *callingFunctionName);

Bool VvcGetHandleFromTokenInt(VvcCommonHandle tokenHandle, VvcTag tag,
                              const char *callingFunctionName, VvcCommonHandle *objectHandle);

// ==========================================================

/* ----------------------------------------------------------
 *  vvcProxyServer and vvcProxyForwarder forward definitions
 * ----------------------------------------------------------
 */

/* ---------
 *  HubData
 * ---------
 */
struct _hubData;

extern void AddRefHub(struct _hubData *hub, const char *callingFunctionName);

extern void ReleaseHub(struct _hubData *hub, const char *callingFunctionName);


/* -----------
 *  ListenHub
 * -----------
 */
struct _listenHubData;

extern void AddRefListenHub(struct _listenHubData *listenHub, const char *callingFunctionName);

extern void ReleaseListenHub(struct _listenHubData *listenHub, const char *callingFunctionName);


/* ------------
 *  channelFwd
 * ------------
 */
struct _channelFwd;

extern void AddRefChannelHub(struct _channelFwd *channel, const char *callingFunctionName);

extern void ReleaseChannelHub(struct _channelFwd *channel, const char *callingFunctionName);


/* ----------
 *  nodeData
 * ----------
 */
struct _nodeData;

extern void AddRefNode(struct _nodeData *node, const char *callingFunctionName);

extern void ReleaseNode(struct _nodeData *node, const char *callingFunctionName);


/* ------------
 *  listenData
 * ------------
 */
struct _listenData;

extern void AddRefListenNode(struct _listenData *listener, const char *callingFunctionName);

extern void ReleaseListenNode(struct _listenData *listener, Bool *freed,
                              const char *callingFunctionName);


/* -------------
 *  channelData
 * -------------
 */
struct _channelData;

extern void AddRefChannelNode(struct _channelData *channel, const char *callingFunctionName);

extern void ReleaseChannelNode(struct _channelData *channel, const char *callingFunctionName);

/* ----------------
 *  ProxyMsgChannel
 * ----------------
 */
extern void AddRefProxyMsgChannel(struct _VvcMsgChannel *msgChannel,
                                  const char *callingFunctionName);

extern void ReleaseProxyMsgChannel(struct _VvcMsgChannel *msgChannel,
                                   const char *callingFunctionName);

// ==========================================================


#define VVC_ADD_REF_MSG(msg, tag) VvcAddRefMsg((msg), (tag), __FUNCTION__)
#define VVC_RELEASE_MSG(msg, tag) VvcReleaseMsg((msg), (tag), __FUNCTION__)

#define VVC_ADD_REF_OPEN_CHAN(openChan, tag) VvcAddRefOpenChan((openChan), (tag), __FUNCTION__)
#define VVC_RELEASE_OPEN_CHAN(openChan, tag) VvcReleaseOpenChan((openChan), (tag), __FUNCTION__)

#define VVC_ADD_REF_SEND_COMPLETION_CONTEXT(sendComplCtxt, tag)                                    \
   VvcAddRefSendCompletionContext((sendComplCtxt), (tag), __FUNCTION__)
#define VVC_RELEASE_SEND_COMPLETION_CONTEXT(sendComplCtxt, cancelSafe, tag, destroyedBatcher)      \
   VvcReleaseSendCompletionContext((sendComplCtxt), (cancelSafe), (tag), (destroyedBatcher),       \
                                   __FUNCTION__)

#define VVC_RELEASE_SCCS_IN_SCC_BATCHER(sccBatcher, cancelSafe, tag)                               \
   VvcReleaseSCCsInSCCBatcher((sccBatcher), (cancelSafe), (tag), __FUNCTION__)

VvcListener *VvcCreateListener(VvcInstance *instance, VvcPluginId pluginId, int32 sessionId,
                               char *name, VvcListenerEvents *events, void *clientData);

VvcChannel *VvcCreateChannel(VvcListener *listener, uint32 channelId, char *name, uint32 priority,
                             uint32 latency, uint32 priorityVersion, uint32 trafficType,
                             uint32 timeout, uint32 flags, uint32 traceFlags, VvcSession *session,
                             VvcChannelEvents *events, void *clientData);

VvcOpenChan *VvcCreateOpenChan(VvcSession *session, VvcListener *listener, uint32 channelId,
                               uint32 priority, uint16 timeout, uint32 flags, char *name,
                               size_t nameLen, void *initialData, size_t initialDataLen);

VvcOpenChanAck *VvcCreateOpenChanAck(uint32 channelId, VvcStatus status, void *initialData,
                                     size_t initialDataLen);

VvcMsg *VvcCreateMsg(VvcChannel *channel, uint8 *buf, size_t bufLen, Bool ctrlMsg,
                     Bool isMptInternal, Bool isVvcBweCtrlMsg,
                     VvcMsgPinToTransportType pinToTransport, void *clientData);

VvcMPTMessage *VvcCreateMptMsg(VvcChannel *channel, uint8 *buf, size_t bufLen, Bool ctrlMsg,
                               Bool isVvcBweCtrlMsg, VvcMsg *vvcMsg);

void VvcDestroyMptMsg(VvcMPTMessage *mptMsg);

void VvcQueueEvent(VvcInstance *instance, VvcPluginId pluginId, uint32 event, VvcCommon *object1,
                   VvcCommon *object2, void *recvBuf, size_t recvLen, void *eventData,
                   VvcEventCb eventCb);

void VvcListenerOnConnectEvCb(uint32 seqNo, uint32 event, VvcCommon *object1, VvcCommon *object2,
                              void *recvBuf, size_t recvLen, void *eventData);

void VvcListenerOnPeerOpenEvCb(uint32 seqNo, uint32 event, VvcCommon *object1, VvcCommon *object2,
                               void *recvBuf, size_t recvLen, void *eventData);

void VvcChannelOnOpenEvCb(uint32 seqNo, uint32 event, VvcCommon *object1, VvcCommon *object2,
                          void *recvBuf, size_t recvLen, void *eventData);

void VvcListenerOnDisconnectEvCb(uint32 seqNo, uint32 event, VvcCommon *object1, VvcCommon *object2,
                                 void *recvBuf, size_t recvLen, void *eventData);

void VvcDispatchEvents(VvcInstance *instance);

void VvcAddRefInstance(VvcInstance *instance, VvcTag tag, const char *callingFunctionName);

void VvcReleaseInstance(VvcInstance *instance, VvcTag tag, const char *callingFunctionName);

void VvcAddRefListener(VvcListener *listener, VvcTag tag, const char *callingFunctionName);

void VvcReleaseListener(VvcListener *listener, VvcTag tag, const char *callingFunctionName);

void VvcAddRefSession(VvcSession *session, VvcTag tag, const char *callingFunctionName);

void VvcReleaseSession(VvcSession *session, VvcTag tag, const char *callingFunctionName);

void VvcAddRefChannel(VvcChannel *channel, VvcTag tag, const char *callingFunctionName);

void VvcReleaseChannel(VvcChannel *channel, VvcTag tag, const char *callingFunctionName);

void VvcAddRefMsg(VvcMsg *msg, VvcTag tag, const char *callingFunctionName);

Bool VvcReleaseMsg(VvcMsg *msg, VvcTag tag, const char *callingFunctionName);

void VvcAddRefOpenChan(VvcOpenChan *openChan, VvcTag tag, const char *callingFunctionName);

void VvcReleaseOpenChan(VvcOpenChan *openChan, VvcTag tag, const char *callingFunctionName);

void VvcAddRefSendCompletionContext(VvcSendCompletionContext *sendComplCtxt, VvcTag tag,
                                    const char *callingFunctionName);

Bool VvcReleaseSendCompletionContext(VvcSendCompletionContext *sendCompletionCtxt, Bool cancelSafe,
                                     VvcTag tag, Bool *destroyedBatcher,
                                     const char *callingFunctionName);

Bool VvcReleaseSCCsInSCCBatcher(VvcSCCBatcher *sccBatcher, Bool cancelSafe, VvcTag tag,
                                const char *callingFunctionName);

void VvcChannelOnSendCompleteEvCb(uint32 seqNo, uint32 event, VvcCommon *object1,
                                  VvcCommon *object2, void *recvBuf, size_t recvLen,
                                  void *eventData);

Bool VvcQueueChannelOnClose(VvcChannel *channel, VvcQueueOnChannelCloseTrigger trigger);

VvcSCCBatcher *VvcCreateSCCBatcher(VvcSession *session, size_t bufLen);

VvcSendCompletionContext *VvcCreateSendCompletionContext(VvcMsg *msg, uint8 *chunkBuf,
                                                         size_t chunkLen, uint32 flags,
                                                         uint64 nowUS);

void VvcIncrementChannelSends(VvcChannel *channel);

Bool VvcDecrementChannelSends(VvcChannel *channel, VvcQueueOnChannelCloseTrigger trigger);

VvcStatus VvcSendBuf(VvcSession *session, VvcSCCBatcher *sccBatcher, Bool *eventQueued);

void VvcOpenChanTimeoutCb(void *clientData);

Bool VvcSessionErrorHandler(VvcSession *session, VvcStatus status);

Bool VvcPeerSessionClosed(VvcSession *session, uint32 closeReason);

Bool VvcSessionQueueOnClose(VvcSession *session);

Bool VvcSessionIsUp(VvcSession *session);

Bool VvcMatchListenerName(const char *listenerName, const char *channelName);


#if defined(__APPLE__) || defined(__linux__)
#   define VvcSetTimerResoluton(session, nowNS)
#else
void VvcSetTimerResoluton(VvcSession *session, uint64 nowNS);
#endif

uint32 VvcGetTimerResoluton(VvcSession *session, uint64 nowNS);

void VvcClearTimerResoluton(VvcSession *session);

void VvcUnregisterMsgDispatchPoll(VvcSession *session);

VvcStatus VvcRegisterMsgDispatchPoll(VvcSession *session);

void VvcQueueChannelOnRecvEvent(VvcChannel *channel, VvcRecvMessage *recvMsg);

Bool VvcMatchListenerSessionId(VvcSession *session, VvcListener *listener);

Bool VvcInitDispatchEventPoll(VvcInstance *instance, VvcPluginId pluginId);

void VvcStopDispatchEventPoll(VvcInstance *instance);

void VvcCleanupDispatchEventPoll(VvcInstance *instance);

void VvcUninitDispatchEventPoll(VvcInstance *instance);

void VvcDispatchEventPollWake(VvcInstance *instance, VvcDispatchEventPollCmd cmd);

void VvcIncrementChannelCurrentQueuedBytes(VvcChannel *channel, size_t numBytes);

void VvcDecrementChannelCurrentQueuedBytes(VvcChannel *channel, size_t numBytes);

void VvcUpdateChannelCurrentBeSendSizeAvg(VvcChannel *channel, size_t numBytes);

VvcStatus VvcGetAuxiliaryFlowInfoFromTransportBe(VvcSession *session,
                                                 VvcAuxiliaryFlowInfo *auxiliaryFlowInfo,
                                                 SSLVerifyParam *sslParams);

VvcStatus VvcGetSetupMsgFromTransportBe(VvcSession *session, char **setupMsg, int *len);

VvcStatus VvcVerifySetupMsgFromTransportBe(AsyncSocket *asock, VvcSession *session);

void *VvcGetSslContextFromTransportBe(VvcSession *session);

VvcStatus VvcGetNetworkStatsFromTransportBe(VvcSession *session, VvcNetworkStats *networkStats);

VvcStatus VvcStartSession(VvcSession *session);

Bool VvcIsTransportSwitchingEnabled(const VvcSession *session);

int32 VvcGetPlaformIdFromVvcSessionId(VvcInstance *instance, VvcSessionId sessionId);

VvcStatus VvcSessionStartAsyncRead(VvcSession *session,
                                   VvcRecvCompletionContext *recvCompletionContext);

VvcStatus VvcAddAsockBackend(VvcSession *session, const VvcAsockBackend *asockBackend);

VvcStatus VvcRemoveAllAsockBackends(VvcSession *session, Bool isSocketError,
                                    int32 *numAsockBeRemoved);

VvcDispatchSimulator *VvcDispatchSimulator_Init(VvcChannel *channel);

void VvcDispatchSimulator_Uninit(VvcDispatchSimulator *simulator);

VvcStatus VvcDispatchSimulator_GetAllowedBwConsumption(const char *chanName,
                                                       VvcDispatchSimulator *simulator,
                                                       double *allowedBwConsumption,
                                                       double *allowedBwConsumptionPercentage);

VvcStatus VvcDispatchSimulator_ScheduleMessages(VvcDispatchSimulator *simulator,
                                                DblLnkLst_Links *allowedBwConsumptionMessages);

void VvcDispatchSimulator_ScheduleHeadMsgFromChannel(VvcDispatchSimulator *simulator,
                                                     VvcDispatchSimulatorNode *node);

VvcStatus VvcGetAllowedBwConsumption(const char *chanName, VvcDispatchSimulator *simulator,
                                     DblLnkLst_Links *allowedBwConsumptionMessages,
                                     double *allowedBwConsumption,
                                     double *allowedBwConsumptionPercentage);

void VvcOnAsockBackendConnected(VvcSession *session);

void VvcOnAsockBackendDisconnected(VvcSession *session);

void VvcMultiAsockBackendCloseOnSessionError(VvcSession *session);

VvcAsockBackend *VvcGetActiveAsockBackend(VvcSession *session);

int32 VvcSetActiveAsockBackend(VvcSession *session, AsyncSocket *asock);

Bool VvcHasActiveAsockBackend(VvcSession *session);

Bool VvcIsDataSockNotificationNeeded(VvcSession *session);

void VvcNewAsockBackendConnected(VvcSession *session);

void VvcSendChannelMPTDupAckNow(VvcChannel *channel, VvcMsgPinToTransportType pinToTransport);


VvcChannel *VvcGetChannelById(VvcSession *session, uint32 channelId);
HashMap *VvcGetChannelIdMap(VvcSession *session);

VvcStatus VvcRecvParseMessage(VvcSession *session, VvcRecvState *xportRecvState, uint8 *buf,
                              size_t bufLen, size_t *usefulBytes, size_t *leftover);

void VvcTransportReceiveAndAck(VvcSession *session, VvcRecvState *xportRecvState, uint8 *recvBuf,
                               size_t recvBufLen, size_t recvBufSize);

int VvcQueueVvcAck(VvcSession *session);
void VvcOnMPTAck(VvcChannel *channel, uint32 messagesAcked);

SeqNum_16 VvcAdvanceRcvNxtOnAsockBe(VvcChannel *channel, int asockID);
void VvcUpdateRcvNxtOnAsockBe(VvcChannel *channel, int asockID, SeqNum_16 msgSeq);
void VvcAdvanceSndNxtOnAsockBe(VvcChannel *channel, int asockID);
void VvcUpdateSndNxtOnAsockBe(VvcChannel *channel, int asockID, SeqNum_16 msgSeq);
Bool VvcMsgShouldIncludeMptHeader(VvcMsg *msg);
Bool VvcShouldSendMptAck(VvcChannel *channel, Bool piggybacking);

void VvcSetSessionCloseReason(VvcSession *session, VvcSessionCloseReason closeReason);

Bool VvcIsPeerRejected(AsyncSocket *asock);

/*
 * Internal APIs for VVC to create and destroy
 * Performance Counters database.
 */
VvclibPerfError VvcPerfCountersCreateDb(void);
VvclibPerfError VvcPerfCountersDestroyDb(void *perfCountersDbContext);
// VVC Perf counters internal API for VVC use
double PerfCountersSystemTime();

VvclibPerfError VvcPerfCountersIncrementValueUint64(VvcInstance *mainInstance, PerfDbHandle handle,
                                                    PerfCounterType ctrType, uint64 value);

VvclibPerfError VvcPerfCountersSetRateValueUint64(VvcInstance *mainInstance, PerfDbHandle handle,
                                                  PerfCounterType ctrType, uint64 value, double ts);

VvclibPerfError VvcPerfCountersSetValueUint32(VvcInstance *mainInstance, PerfDbHandle handle,
                                              PerfCounterType ctrType, uint32 value);
VvcSession *VvcFindSessionFromId(VvcInstance *instance, int32 sessionId);

int VvcGetSendMsgAsockID(void *completionContext);


VvcStatus VvcCreateMsgChannel(VvcMsgChannelOpenChannelReq *req, VvcMsgChannel **msgChannel);

void VvcDestroyMsgChannel(VvcMsgChannel *msgChannel);

VvcStatus VvcAddMsgChannelToSession(VvcSession *session, VvcMsgChannel *msgChannel,
                                    VvcCommonHandle nodeHandle, VvcMsgChannelGroupId *msgChannelId);
VvcStatus VvcDeleteMsgChannelFromSession(VvcSession *session, VvcMsgChannelGroupId msgChannelId);
VvcStatus VvcSendMsgForMsgChannels(VvcSession *session, VvcMsgChannelSend *req);
void SendMsgChannelMsgToNode(void *node, VvcMsgChannel *msgChannel, VvcMsgChannelRecv *req);

Bool VvcIsControlSendMsg(const VvcMsg *msg);

void VvcMptAckSent(VvcChannel *channel, SeqNum_16 ackSeq);

Bool VvcShouldCountVvcAck(VvcSession *session, int asockID);

Bool VvcSendSessionCloseMsg(VvcSession *session);

Bool VvcIsAnyDataQueued(VvcSession *session);

void VvcEnableBandwidthEstimation(VvcSession *session);

void VvcInitSendRateLimiter(VvcSession *session);

void VvcDisableBandwidthEstimation(VvcSession *session);

void VvcQueuePauseResumeEvents(VvcSession *session, uint32 event);

Bool VvcIsErrorNotificationNeeded(VvcSession *session, Bool *errorNotificationNeeded);

void VvcSetNegotiatedMptVersion(VvcSession *session, uint8 peerMptVersion);

VvcStatus VvcRegisterDeferredAcksPollCb(VvcSession *session);
void VvcUnregisterDeferredAcksPollCb(VvcSession *session);
void VvcDeferredAcksPollCb(void *clientData);

VvcStatus VvcRegisterSessionCleanupTasksPollCb(VvcSession *session);
void VvcUnregisterSessionCleanupTasksPollCb(VvcSession *session);
void VvcSessionCleanupTasksPollCb(void *clientData);

void VvcSessionCleanupChannelRecvMessages(VvcSession *session);

VvcStatus VvcCloseChannelInt(VvcChannel *channel, VvcCloseChannelReason reason);

VvcChannel *VvcCreateChannel(VvcListener *listener, uint32 channelId, char *name, uint32 priority,
                             uint32 latency, uint32 priorityVersion, uint32 trafficType,
                             uint32 timeout, uint32 flags, uint32 traceFlags, VvcSession *session,
                             VvcChannelEvents *events, void *clientData);
void VvcDestroyChannel(VvcChannel *channel);

VvcStatus VvcGetInfoAuxiliaryFlowInfo(VvcInstance *instance, VvcInfoType infoType, uint32 flags,
                                      const void *param, size_t paramLen, void *info,
                                      size_t *infoLen, SSLVerifyParam *sslParams);

void VvcChannelOnRecvEvCb(uint32 seqNo, uint32 event, VvcCommon *object1, VvcCommon *object2,
                          void *recvBuf, size_t recvLen, void *eventData);

void VvcChannelCheckAndDispatchRecv(VvcChannel *channel);

void VvcSessionTransportSendComplete(VvcSendCompletionContext *sendCompletionContext,
                                     VvcStatus status, uint8 *buf, size_t len,
                                     Bool *destroyedBatcher);

VvcChannel *VvcFindChannelFromSession(VvcSession *session, char *name, int id);

void VvcNwStatsBWConstraints(VvcSession *session, VvcNetworkStats *networkStats);

#ifdef __cplusplus
}
#endif

#endif // _VVCLIBINT_H
