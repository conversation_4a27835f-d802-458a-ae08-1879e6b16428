/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * mainWin32Test.cpp --
 *
 * The main entry of rdeServerTest.
 *
 */

#include "shared/win32/stdafx.h"
#include "shared/win32/rdeSvc.h"
#include "shared/rdeSvc_defines.h"
#include "shared/appSvcMsg.h"
#include "appSvc/appSvc.h"

#include "lib/util/thunk.h"
#include "cliparseWin32.h"
#include "mainChannel.h"
#include "utilFunctions.h"

#include "vmware/tools/utils.h" // For VMTools_NewHandleSource.

#include "getoptwin32.h"
#include "vm_product.h"
#include "productState.h"
#include "log.h"
#include "str.h"
#include "file.h"
#include "util.h"
#include "unicode.h"
#include "coreDump.h"
#include "winregistry.h"

#include "vthread.h"
#include <gtest/gtest.h>
#include <thread>
#include "fakeWSNM.h"
#include "gcd.h"


static gboolean g_shouldTerminate = false;
static int g_exitCode = 0;
static DWORD mainThreadId = 0;
static gboolean MessagePump(gpointer userData);

extern GMainLoop *mainLoop = nullptr;

/*
 *-----------------------------------------------------------------------------
 *
 * MessagePump --
 *
 *      GLib callback triggered when one or more messages are available in the
 *      main UI thread's message queue. This function reads and dispatches them.
 *
 * Results:
 *      TRUE so GLib doesn't remove the callback.
 *
 * Side effects:
 *      Quits the main loop if WM_QUIT was posted and g_shouldTerminate is set.
 *
 *-----------------------------------------------------------------------------
 */

gboolean
MessagePump(gpointer userData) // IN: GMainLoop* (main loop object)
{
   MSG msg = {0};
   while (PeekMessage(&msg, NULL, 0, 0, PM_REMOVE)) {
      if (msg.message == WM_QUIT && g_shouldTerminate) {
         GMainLoop *mainLoop = static_cast<GMainLoop *>(userData);
         printf("%s: Received WM_QUIT, so exiting main loop.\n", __FUNCTION__);
         g_main_loop_quit(mainLoop);
         break;
      }
      TranslateMessage(&msg);
      DispatchMessage(&msg);
      msg = {0};
   }
   return TRUE;
}


/*
 *----------------------------------------------------------------------
 *
 * RdeServerTestThread --
 *
 *    Thread function for executing test cases.
 *
 * Results:
 *    None
 *
 * Side effects:
 *    Sets global variable g_exitCode to the result of RUN_ALL_TESTS().
 *
 *----------------------------------------------------------------------
 */

void
RdeServerTestThread(int argc,
                    wchar_t *wargv[]) // IN:
{
   ::testing::InitGoogleTest(&argc, wargv);
   HRESULT hr = S_FALSE;
   printf("%s, start to initialize Com Interface.\n", __FUNCTION__);
   hr = CoInitialize(0);
   printf("%s, end initialize Com Interface.\n", __FUNCTION__);
   if (FAILED(hr)) {
      printf("%s, failed to initialize Com Interface %x.\n", __FUNCTION__, hr);
      goto exit;
   }
   printf("%s, start to run all tests.\n", __FUNCTION__);
   g_exitCode = RUN_ALL_TESTS();
   printf("%s, end run all tests.\n", __FUNCTION__);
   CoUninitialize();
exit:
   g_shouldTerminate = true;
   ::PostThreadMessage(mainThreadId, WM_QUIT, NULL, NULL);
}

/*
 *----------------------------------------------------------------------
 *
 * IsInMainThread --
 *
 *    Whether current thread is main thread.
 *
 * Results:
 *    true or false.
 *
 * Side effects:
 *    None
 *
 *----------------------------------------------------------------------
 */

bool
IsInMainThread()
{
   return mainThreadId == GetCurrentThreadId();
}


/*
 *----------------------------------------------------------------------
 *
 * wmain --
 *
 *    Main function for the rdeserver test.
 *
 * Results:
 *    Always 0.
 *
 * Side effects:
 *    None
 *
 *----------------------------------------------------------------------
 */

int
wmain(int argc,         // IN:
      wchar_t *wargv[]) // IN:
{
   // Initialize the core dump handler first.
   CoreDump_SetUnhandledExceptionFilter();
   gcd_init();

   FakeWSNM *wsnm = new FakeWSNM();
   wsnm->Init();

   GMainContext *ctx = NULL;
   GSource *messagePumpSource = NULL;

   if (!ParamCmdLine(argc, wargv)) {
      ;
      return 0;
   }

   if (!gOptions.shortcut.empty()) {
      /*
       * This call happens only when a user tries to launch an application using
       * the Unity Touch menu.
       */
      return AppSvc::RunShellExecute(gOptions.shortcut, gOptions.verb, gOptions.flags);
   }
   printf("%s, Initialize.\n", __FUNCTION__);
   Initialize();
   printf("%s, CoInitializeEx.\n", __FUNCTION__);
   CoInitializeEx(NULL, COINIT_APARTMENTTHREADED | COINIT_DISABLE_OLE1DDE);
   printf("%s, CoInitializeEx end.\n", __FUNCTION__);
   // Initialize glib main loop.
   ctx = g_main_context_default();
   mainLoop = g_main_loop_new(ctx, FALSE);
   if (mainLoop == nullptr) {
      printf("%s: Failed to allocate main loop.\n", __FUNCTION__);
      return 0;
   }

   mainThreadId = ::GetCurrentThreadId();
   // Add a callback for when window messages are available.
   messagePumpSource = VMTools_NewHandleSource(reinterpret_cast<HANDLE>(G_WIN32_MSG_HANDLE));
   g_source_set_callback(messagePumpSource, MessagePump, mainLoop, NULL);
   g_source_attach(messagePumpSource, ctx);
   printf("%s, VThread_Init\n", __FUNCTION__);
   // create the test worker thread
   VThread_Init("main");
   std::thread testWorker(RdeServerTestThread, argc, wargv);
   printf("%s, g_main_loop_run\n", __FUNCTION__);
   g_main_loop_run(mainLoop);

   testWorker.join();
   wsnm->Exit();
   delete wsnm;
   CoUninitialize();
   printf("%s: Shutting down RdeServer.\n", __FUNCTION__);
   g_source_destroy(messagePumpSource);
   g_main_loop_unref(mainLoop);
   g_main_context_unref(ctx);
   mainLoop = nullptr;
   UnInitialize();
   return g_exitCode;
}