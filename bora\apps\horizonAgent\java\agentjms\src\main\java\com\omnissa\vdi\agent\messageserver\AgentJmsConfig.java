/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */
package com.omnissa.vdi.agent.messageserver;

import java.security.KeyPair;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.TimeUnit;

import javax.jms.JMSException;
import javax.jms.Session;
import javax.jms.TopicConnection;
import javax.jms.TopicSession;

import com.omnissa.vdi.commonutils.JCA;
import org.apache.commons.lang3.StringUtils;

import com.omnissa.vdi.commonutils.SslUtils;
import com.omnissa.vdi.commonutils.Thumbprint;
import com.omnissa.vdi.logger.Logger;
import com.omnissa.vdi.messagesecurity.Identity;
import com.omnissa.vdi.messagesecurity.JMSMessageSecurity;
import com.omnissa.vdi.messagesecurity.MessageSecurityException;
import com.omnissa.vdi.messagesecurity.MessageSecurityHandler;
import com.omnissa.vdi.messagesecurity.MessageSecurityHandler.MSSecMode;
import com.omnissa.vdi.messagesecurity.WrappedMessageSecurityHandler;
import com.omnissa.vdi.messagesecurity.swiftmq.BrokerUpdateUtility;
import com.omnissa.vdi.messagesecurity.swiftmq.DaasSwiftMQCertificateManager;
import com.omnissa.vdi.messagesecurity.swiftmq.FileBasedCertificateManager;
import com.omnissa.vdi.messagesecurity.swiftmq.SwiftMQCertificateManager;
import com.omnissa.vdi.ssl.CertificateManager;

/**
 * Abstract class which contains the methods which provide the details needed
 * for setting up the JMS connection for communication with the broker(s), and
 * the functionality to handle secure pairing.
 *
 * Note: getUseSSOKeyForEncryption returns true by default. override the method
 * and return false if SSO key should not be used for encrypting sensitive data.
 *
 */
public abstract class AgentJmsConfig {
    final private static Logger log = Logger.getLogger(AgentJmsConfig.class);

    public final static int DEFAULT_SSL_CERTIFICATE_KEY_CHECK_INTERVAL = 300;

    public final static int DEFAULT_SSL_CERTIFICATE_KEY_SIZE = 2048;

    public final static int DEFAULT_SSL_CERTIFICATE_VALIDITY_PERIOD = 180;

    protected int certificateKeyCheckInterval = DEFAULT_SSL_CERTIFICATE_KEY_CHECK_INTERVAL;

    protected int certificateValidityPeriod = DEFAULT_SSL_CERTIFICATE_VALIDITY_PERIOD;

    protected TimeUnit certificateValidityPeriodUnits = TimeUnit.DAYS;

    public abstract void init();

    public void reinit() {
    }

    /**
     * Configure the agent's message security handler based on the configuration
     * we have stored.
     *
     * @param config The security handler
     * @throws MessageSecurityException Unable to configure the handler
     */
    public void configureMessageSecurity(AgentMessageSecurityHandler config)
            throws MessageSecurityException {
        PrivateKey key = getPrivateKey();
        config.configure(getMsMode(), getIdentity(), key, getBrokerPublicKey(),
                null, this);
    }

    /**
     * Returns the maximum number of seconds to wait between sending session
     * reports. A negative value implies that they should be disabled.
     *
     * @return Number of seconds to wait
     */
    public abstract int getAsyncSessionSeconds();

    /**
     * Returns the public key used by the broker
     *
     * @return The public key of the broker
     */
    public abstract PublicKey getBrokerPublicKey();

    /**
     * Returns the thumbprint of the SSL Certificate which broker's will use
     *
     * @return The thumbprint of the broker
     */
    public abstract String getBrokerSSLCertificateThumbprint();

    /**
     * Returns the set of broker FQHNs which the agent can connect to. This can
     * be in an arbitrary order, callers should shuffle the result if needing to
     * perform basic load balancing.
     *
     * @return The set of brokers
     */
    public abstract Collection<String> getBrokers();

    /**
     * Writes the set of broker FQHNs to the Broker registry value under the
     * Agent\Configuration registry location.
     */
    public abstract void setBrokers(String brokersParam);

    /**
     * Generates a new keypair once for use by the agent and saves it in memory.
     *
     * @return JMSMessageSecurity.makeKeyPair()
     *
     */
    protected KeyPair getGeneratedKeyPair() {
        try {
            return JMSMessageSecurity.makeKeyPair();
        } catch (MessageSecurityException e) {
            /*
             * wraps NoSuchAlgorithmException, so not really expected to occur
             * with fixed algorithm defined - we can't proceed without a valid
             * keypair
             */
            log.fatal("Unable to generate required keypair: {}",
                    e.getMessage());
            throw new RuntimeException(e);
        }
    }

    /**
     * Generates a new encryption keypair once for use by the agent and saves it
     * in memory.
     *
     * @return JMSMessageSecurity.makeEncryptedKeyPair()
     *
     */
    protected KeyPair generateEncryptionKeyPair() {
        try {
            return JMSMessageSecurity.makeEncryptionKeyPair();
        } catch (MessageSecurityException e) {
            /*
             * wraps NoSuchAlgorithmException, so not really expected to occur
             * with fixed algorithm defined - we can't proceed without a valid
             * keypair
             */
            log.fatal("Unable to generate required encryption keypair: {}",
                    e.getMessage());
            throw new RuntimeException(e);
        }
    }

    /**
     * Agent identity string used for message security, based on the cn, so will
     * be in the form agent/&lt;guid&gt;
     *
     * @return The agent identity
     */
    public abstract Identity getIdentity();


    /**
     * Infrastructure zone used for filtering based on site locations. Unused in
     * VMware broker code (everything defaults to null/site1)
     *
     * @return The infrastructure zone
     */
    public abstract String getInfrastructureZone();

    /**
     * The private key as provided pre-pairing (typically supplied via
     * machine.id) used by message security.
     *
     * @return The private key
     */
    protected abstract PrivateKey getLegacyPrivateKey();

    /**
     * The message security mode.
     *
     * @return The message security mode
     */
    public abstract MSSecMode getMsMode();

    public abstract void setMsMode(MSSecMode mode);


    /**
     * Returns the value of managedCertificateAdvanceRollOver present in the registry
     *
     * @return the value of managedCertificateAdvanceRollOver
     */
    public abstract int getManagedCertificateAdvanceRollOver();

    /**
     * Get the private key used by this agent for message security.
     *
     * @return The private key of this agent
     */
    public abstract PrivateKey getPrivateKey();

    /**
     * Get the public key used by this agent for message security.
     *
     * @return The public key of this agent
     */
    public abstract PublicKey getPublicKey();

    /**
     * Returns the thumbprint of the agent SSL Certificate
     *
     * @return The agent thumbprint
     */
    public abstract String getAgentSSLCertificateThumbprint();

    /**
     * Set the thumbprint for the agent's certificate
     *
     * @param thumbprint The agent thumbprint
     */
    public abstract void setAgentSSLCertificateThumbprint(String thumbprint);

    /**
     * DN in the form cn=&lt;guid&gt;,ou=servers,dc=vdi,dc=vmware,dc=int. Used
     * for message filtering over JMS.
     *
     * @return The DN
     */
    public abstract String getServerDn();

    /**
     * DNS name of the server
     *
     * @return The DNS name of the server
     */
    public abstract String getServerDnsName();

    /**
     * DN in the form cn=&lt;name&gt;,ou=server groups,dc=vdi,dc=vmware,dc=int.
     * Used for message filtering over JMS.
     *
     * @return The server pool dn
     */
    public abstract String getServerPoolDn();

    /**
     * The MQ SSL Certificate key check interval (seconds)
     *
     * @return The key check interval
     */
    public int getSSLCertificateKeyCheckInterval() {
        return certificateKeyCheckInterval;
    }

    /**
     * The MQ SSL Certificate key size
     *
     * @return The certificate key size
     */
    public int getSSLCertificateKeysize() {
        return DEFAULT_SSL_CERTIFICATE_KEY_SIZE;
    }

    /**
     * Agent identity string used in DaaSMode for message security.  This is
     * the value that the DaaS agent wants to run with, not the current
     * config of running jms connection. Will be in the form agent/&lt;guid&gt;
     *
     * @return The agent Identity
     */
    public Identity getNonCachedIdentity(){
        return null;
    }

    /**
     * The MQ SSL Certificate validity period
     *
     * @return The certificate validity period
     */
    public int getSSLCertificateValidityPeriod() {
        return certificateValidityPeriod;
    }

    /**
     * The MQ SSL Certificate validity period units (days/minutes)
     *
     * @return The ssl validity period units
     */
    public TimeUnit getSSLCertificateValidityPeriodUnits() {
        return certificateValidityPeriodUnits;
    }

    /**
     * Returns whether this is a VC managed VM or not.
     *
     * @return True if VC managed
     */
    public abstract boolean isManaged();

    /**
     * Whether this agent has been securely paired.
     *
     * @return True if securely paired
     */
    public abstract boolean isPaired();

    /**
     * Handles the secure pairing over JMS, using the CHANGEKEY message. It will
     * be signed using the agent's identity and legacy private key.
     *
     * If the pairing operation is not needed, will log at debug level and
     * return.
     *
     * @param mainObject
     *            Main object reference
     * @param connection
     *            TopicConnection to a router for communication to the brokers
     * @param sslCertificateThumbprints
     *            {@literal List<Thumbprints>}
     * @param jmsThumbprintExchangeRequired
     *            do we need to exchange JMS SSL thumbprints
     * @param activeBroker
     *            the broker that we are actually contacting
     * @throws MessageSecurityException
     *             If the agent message security handler can't be created
     * @throws JMSException
     *             On JMS issue
     * @throws InterruptedException
     *             If the thread has been interrupted while waiting for pairing
     *             to complete
     */
    public void pairOverJms(final Main mainObject, TopicConnection connection,
            List<Thumbprint> sslCertificateThumbprints,
            boolean jmsThumbprintExchangeRequired, String activeBroker)
            throws MessageSecurityException, JMSException, InterruptedException {

        if (isPaired() && !jmsThumbprintExchangeRequired) {
            log.debug("Skipping pair operation: already paired");
            return;
        }

        if (isDaaSAgent()) {
            if (isDaaSLegacyPairingMode()) {
                log.debug(
                        "Skipping pair operation: DaaS agent is using legacy pairing mechanism.");
                return;
            } else {
                log.debug("Daas mode detected, proceeding with pairing");
            }
        }

        log.info("Attempting to securely pair agent for JMS communication");

        PrivateKey signer = null;
        if (isPaired()) {
            /*
             * if thumbprints need to be exchanged then we've already performed
             * a pairing operation so just reuse the paired signing key
             */
            log.debug("Using paired signing key");
            signer = getPrivateKey();
            if (signer == null) {
                /*
                 * This shouldn't happen, but if it does it implies config is
                 * broken. Set flag to enable restart to re-read config.
                 */
                if (mainObject != null) {
                    mainObject.setInvalidConfig(true);
                }

                throw new MessageSecurityException("Paired key does not exist");
            }
        } else {
            /**
             * Create a temporary key store using the old private key
             */
            log.trace("Configuring temporary message security key store");
            signer = getLegacyPrivateKey();
            if (signer == null) {
                log.debug("No legacy key exists for signing, use new one");
                signer = getPrivateKey();
            }
        }

        AgentMessageSecurityHandler handler = new AgentMessageSecurityHandler(
                false);
        handler.configure(getMsMode(), getIdentity(), signer,
                getBrokerPublicKey(), null, this);

        TopicSession session = connection.createTopicSession(false,
                Session.AUTO_ACKNOWLEDGE);
        WrappedMessageSecurityHandler wrappedHandler = new WrappedMessageSecurityHandler(
                handler);

        /*
         * create and configure broker updater to send changekey
         * Note: in the Daas mode, unlike View, we expect only the "ACTIVEBROKER" to respond
         * to the CHANGEKEY request
         */
        BrokerUpdateUtility updater = new BrokerUpdateUtility(session,
                "desktopcontrol", "CHANGEKEY", "CHANGEKEYREPLY",
                wrappedHandler, isDaaSAgent() ? 1 : getBrokers().size(),
                getChangeKeyResponseTimeout(), getChangeKeyMessageTtl());

        updater.addParameterToSend("SERVERDN", getServerDn());

        final PublicKey publicKey = getPublicKey();
        if (publicKey != null) {
            final String newKey = JMSMessageSecurity.keyToString(publicKey);
            updater.addParameterToSend("PUBLICKEY", newKey);
        }

        KeyPair encryptionKey = getEncryptionKey();
        if (encryptionKey != null) {
            try {
                final String encryptionKeyString = JMSMessageSecurity
                        .keyToString(encryptionKey.getPublic());
                updater.addParameterToSend("ENCPUBLICKEY", encryptionKeyString);
            } catch (Exception e) {
                log.debug("Unable to convert encryption key to string");
                // Allow fall through, will fall back to SSO
            }
        }

        // create a string containing the thumbprints
        StringBuilder tps = new StringBuilder();
        for (Thumbprint tp : sslCertificateThumbprints) {
            if (tps.length() > 1) {
                tps.append(';');
            }
            tps.append(tp);

        }
        updater.addParameterToSend("SSLTHUMBPRINTS", tps.toString());

        updater.addParameterToSend("ACTIVEBROKER", activeBroker);

        /*
         * If RPs (RePair signing) keypair is present then send it. This
         * keypair will be present only on a golden image and the public part
         * needs to be shared with the broker here. When a clone created from
         * this image gets powered on after Sysprep specialize, then in the
         * function confirmMachineIdentity the presence of this keypair would
         * be used as an indicator that the machine can be a candidate for
         * identity change and after the change of identity this keypair will
         * be deleted.
         */
        KeyPair rePairingKeyPair = getRePairingKey();
        if (rePairingKeyPair != null) {
            log.debug("Found RPs key pair, will send it with CHANGEKEY");
            final String rePairingPublicKey = JMSMessageSecurity
                    .keyToString(rePairingKeyPair.getPublic());
            updater.addParameterToSend("RPSPUBLICKEY",
                                       rePairingPublicKey);
        }

        updater.addParameterToReceive("BROKERTHUMBPRINTS");

        updater.addParameterToReceive("USESSOENCRYPT");

        updater.updateOverJms(new BrokerUpdateUtility.LoopChecker() {

            @Override
            public boolean isRunning() {
                return !mainObject.areWeStopping();
            }
        });

        if (updater.okResponseReceived()) {
            String thumbprint = updater
                    .getReceivedParameter("BROKERTHUMBPRINTS");
            String useSSOKeyForEncryption = updater
                    .getReceivedParameter("USESSOENCRYPT");
            updateAgentRegistryOnThumbprints(thumbprint);
            setUseSSOKeyForEncryption(
                    StringUtils.isNoneEmpty(useSSOKeyForEncryption)
                            ? useSSOKeyForEncryption
                            : "true");

            log.info("Securely paired agent for JMS communication, mode: {}",
                    getMsMode());
            pairOverJmsComplete();
        }
    }

    boolean updateAgentRegistryOnThumbprints(String thumbprints) {
        log.trace("Agent current mode: {} thumbprints: {}", getMsMode(),
                thumbprints);
        setBrokerSSLCertificateThumbprint(thumbprints);
        if (!StringUtils.isBlank(thumbprints)) {
            if (!MSSecMode.ENHANCED.equals(getMsMode())) {
                log.debug("Setting agent mode to ENHANCED");
                setMsMode(MSSecMode.ENHANCED);
                return true;
            }
        } else if (JCA.isFips() && MSSecMode.ENHANCED.equals(getMsMode())) {
            log.debug("Setting agent mode to ON");
            setMsMode(MSSecMode.ON);
            return true;
        }
        return false;
    }

    /**
     * Called by pairOverJms when at least one broker has accepted our CHANGEKEY
     * message.
     */
    protected abstract void pairOverJmsComplete();

    /**
     * Set the thumbprint for the Certificate which broker's will present
     *
     * @param thumbprint The thumbprint of the broker
     */
    public abstract void setBrokerSSLCertificateThumbprint(String thumbprint);

    public void setUseSSOKeyForEncryption(String useSSOKeyForEncryption) {};

    public boolean getUseSSOKeyForEncryption() {
        return true;
    }

    public void setSSLCertificateKeyCheckInterval(int interval) {
        this.certificateKeyCheckInterval = interval;
    }

    public void setSSLCertificateValidityPeriod(int period) {
        this.certificateValidityPeriod = period;
    }

    /**
     * set the units to use for SSL Certificate validity period
     *
     * the default is DAYS, this method can be used to override this for testing
     *
     * @param units The units to use for validity
     */
    public void setSSLCertificateValidityPeriodUnits(TimeUnit units) {
        certificateValidityPeriodUnits = units;
    }

    public boolean isDaaSAgent() {
        return false;
    }

    public boolean isDaaSLegacyPairingMode() {
        return false;
    }

    public boolean isManagedByDaasAgent() {
        return false;
    }

    public void setManagedByDaasAgent(boolean managedByDaasAgent) {
    }

    /**
     * Wrapper for #JMSMessageSecurity.stringToPrivateKey, returns null on error
     * or not defined.
     *
     * @param key The key to decode
     * @return null on error
     */
    protected PrivateKey stringToPrivateKey(String key) {
        if (key != null && key.trim().length() > 0) {
            try {
                return JMSMessageSecurity.stringToPrivateKey(key);
            } catch (MessageSecurityException e) {
                log.warn("Invalid private key - " + e.getMessage());
                if (log.isDebugEnabled()) {
                    log.debug("Invalid private key - " + e.getMessage(), e);
                }
            }
        }

        return null;
    }

    /**
     * Wrapper for #JMSMessageSecurity.stringToPublicKey, returns null on error
     * or not defined.
     *
     * @param key The key to decode
     * @return null on error
     */
    protected PublicKey stringToPublicKey(String key) {
        if (!StringUtils.isBlank(key)) {
            try {
                return JMSMessageSecurity.stringToPublicKey(key);
            } catch (MessageSecurityException e) {
                log.warn("Invalid public key - {}", e.getMessage());
            }
        }

        return null;
    }

    /**
     * Wrapper for #JMSMessageSecurity.stringToEncryptionPublicKey, returns null
     * on error or not defined.
     *
     * @param key
     *            The key to decode
     * @return null on error
     */
    protected PublicKey stringToEncryptionPublicKey(String key) {
        if (!StringUtils.isBlank(key)) {
            try {
                return JMSMessageSecurity.stringToEncryptionPublicKey(key);
            } catch (MessageSecurityException e) {
                log.warn("Invalid encryption public key - {}", e.getMessage());
            }
        }

        return null;
    }

    public CertificateManager getCertificateManager() {
        CertificateManager cm = null;
        if (Main.isSimulated()) {
            cm = FileBasedCertificateManager.getInstance();
        } else if (isDaaSAgent()) {
            cm = new DaasSwiftMQCertificateManager();
        } else {
            cm = new SwiftMQCertificateManager(
                    SslUtils.getPreferredThumbprintAlgorithm(),
                    SwiftMQCertificateManager.DEFAULT_CERTIFICATE_AGENT_FRIENDLY_NAME,
                    SwiftMQCertificateManager.DEFAULT_PENDING_CERTIFICATE_AGENT_FRIENDLY_NAME,
                    SslUtils.getCertificateSignatureAlgorithm());
        }
        return cm;
    }

    /**
     * Get the ttl for the CMS message
     *
     * @return the ttl for the CMS message
     */
    public abstract long getCmsMessageTtl();

    /**
     * Get the ttl for the CHANGEKEY message
     *
     * @return time to live in milliseconds
     */
    public abstract long getChangeKeyMessageTtl();

    /**
     * Get the timeout for the CHANGEKEY response message
     *
     * @return response time in milliseconds
     */
    public abstract long getChangeKeyResponseTimeout();

    /**
     * Get the key used by this agent for encryption.
     *
     * @return The encryption key of this agent
     */
    public abstract KeyPair getEncryptionKey();

    public boolean invalidateIdentity() {
        return true;
    }

    public boolean requireNewIdentity() {
        return false;
    }

    public void setRequireNewIdentity(boolean invalidate) {
    }

    public void setNewIdentity(String identity, String serverDn,
            String serverPoolDn, String publicKey, String privateKey) {
    }

    public void setGoldenImageKey(KeyPair goldenImageKeyPair) {
    }

    public KeyPair getGoldenImageKey() {
        return null;
    }

    // PoC only
    public boolean triggerGoldenImage() {
        return false;
    }

    // PoC only
    public void setTriggerGoldenImage(boolean triggerGoldenImage) {
    }

    public abstract boolean shouldCreateRePairingKeyPair();

    public abstract KeyPair createRePairingKeyPair();

    public abstract KeyPair getRePairingKey();

    public abstract void setRePairingKey(KeyPair rePairingKeyPair);

    public abstract KeyPair getGoldenImageKeypair();

    public abstract void setIcaParameter(String name, String value);
}
