/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/** \file KeyVault.h
 *
 *  Services KeyVaultWorker using KeyVaultImplementation
 */

#pragma once

#ifdef WIN32


#   include "KeyVaultCommon.h"
#   include "KeyVaultCng.h"


class KeyVault {
public:
   friend class KeyCollection;
   friend class KeyCollections;
   friend class KeyVaultCng;

   KeyVaultCng m_cng; // this is kept from the capi time
   corecritsec *m_globalSync;

   KeyVault(corecritsec *pGlobalSync);
   virtual ~KeyVault();

   bool addContext(DWORD keyLength, KeyName &keyname, PropertyBag &response);
   bool addKey(bool keyPair, DWORD keyLength, KeyName &keyname, KeyName &encipherName, bool persist,
               PropertyBag &response, bool checkSize = true);
   bool addMaster(KeyName &keyname, PropertyBag &response);
   void clear(PropertyBag &response);
   bool decipher(KeyName &keyname, PropertyBag &params, PropertyBag &response, WorkItem *workitem);
   void deleteRootKey(PropertyBag &params, PropertyBag &response);
   bool deriveKey(KeyName &keyname, KeyName &master, KeyName &context, PropertyBag &response);
   bool encipher(KeyName &keyname, PropertyBag &params, PropertyBag &response, WorkItem *workItem);
   bool generateSignature(KeyName &keyname, SignDataInfo &sigData, PropertyBag &response);
   const tstr &getContainerName() const;
   bool getData(KeyName &keyname, bool decipher, PropertyBag &response, WorkItem *workitem);
   bool getKeyRaw(KeyName &keyname, PropertyBag &response, WorkItem *workitem);
   bool getKeyRawInt(KeyName &keyname, PropertyBag &response, MsgBinary &bin, TempData &tmp,
                     LPCTSTR type, LPCTSTR subtype = SUBTYPE_SYMMETRIC, DWORD *pkeyLength = NULL);
   bool getPublic(KeyName &keyname, SignDataInfo &sigData, PropertyBag &response,
                  WorkItem *workitem);
   bool id(KeyName &keyname, PropertyBag &params, PropertyBag &response, KeyVault *pKeyVault);
   bool info(KeyName &keyname, PropertyBag &response);
   bool init(bool useCAPI);
   bool keyExport(KeyName &keyname, tstr keytype, KeyName &keynameEncipher,
                  bool exportAllOperationalKeyGens, PropertyBag &transform, PropertyBag &response,
                  WorkItem *workItem);
   bool keyImport(tstr keytype, PropertyBag &params, PropertyBag &response, WorkItem *workItem);
   bool latest(KeyName &keyname, PropertyBag &response);
   bool list(KeyName &name, PropertyBag &params, PropertyBag &response);
   bool putData(KeyName &keyname, KeyName &encipherName, PropertyBag &transform,
                PropertyBag &response, WorkItem *workItem);
   bool putKeyRaw(KeyName &keyname, KeyName &keynameEncipher, bool persist, PropertyBag &response,
                  WorkItem *workItem);
   bool putPublic(KeyName &keyname, bool fTrusted, SignDataInfo &sigData, PropertyBag &response,
                  WorkItem *workItem);
   bool rekey();
   bool remove(tstr &name, PropertyBag &response, bool fCheckDependents,
               DWORD retainLatestKeys = 0);
   bool signHash(KeyName &keyname, SignDataInfo &sigData, PropertyBag &response);
   bool signPKCS10(KeyName &keyname, SignDataInfo &sigData, PropertyBag &response);
   bool verifySignature(KeyName &keyname, SignDataInfo &sigData, PropertyBag &response);
   KeyVaultImplementation *pint;

private:
   bool _createOrVerifySignature(MsgBinary &keyBlob, SignDataInfo &sigData, TempData &tmpData,
                                 PropertyBag &response, bool fCreate = false);
};


#endif
