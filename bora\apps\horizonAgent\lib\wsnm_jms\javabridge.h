/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */


/* */

/* javabridge.h */

/* */


/*
 * NOTE
 * the JavaBridge class has one static instance that is not intended
 * to be deconstructed (see comment in wsnm_jms.cpp, we do an ExitProcess
 * when we want to stop)
 */


/*
 * Message properties (headers) are case sensitive. We use mixed-case for
 * response headers and upper-case for notification headers. XML tags are
 * not case sensitive but by convention we use upper case for these.
 */

#include "ws_ldap_partition.h"
// Message construction params in PropertyBag
#define PROP_BODY TEXT("BODY")
#define PROP_MSGPROPS TEXT("PROPERTIES")
#define PROP_ASYNC TEXT("ASYNC")
#define PROP_TOPIC TEXT("TOPIC")

// Message log level params in PropertyBag
#define LOG_MSG_TRACE_LEVEL TEXT("LOGMSGTRACELEVEL")

// Notification message headers and XML tags
#define MY_DN "SERVERDN"
#define MY_POOLDN "SERVERPOOLDN"
#define MY_DNSNAME "SERVERDNSNAME"
#define MY_IP "DYNAMICIP"
#define MY_IP6 "DYNAMICIP6"
#define MY_IPADDR "DYNAMICIPADDRESS"
#define MY_IP6ADDR "DYNAMICIP6ADDRESS"
#define MY_MACADDRIPV4 "MACADDRESSIPV4"
#define MY_PREFIXLENIPV4 "PREFIXLENGTHIPV4"

// not case sensitive but watch the whitespace
#define CI_DN "Server DN"
#define CI_POOLDN "Server Pool DN"
#define CI_DNSNAME "Server DNS Name"
#define CI_INFRAZONE "Infrastructure Zone"
#define CI_MSSECMODE "MS Mode"
#define CI_AGENTID "Agent Identity"
#define CI_AGENTKEY "Agent Private Key"
#define CI_BROKERKEY "Broker Public Key"
#define CI_MANAGED "Managed"
#define CI_ASYNCSESS "AsyncSessionSeconds"
#define CI_SYSPREP "Use Sysprep"
#define CI_VMWAREID "VMware ID"

#define SERVER_DN_PREFIX "cn="

enum JmsState {
   JMS_STATE_UNKNOWN,
   JMS_STATE_INITIALIZING,
   JMS_STATE_WAITING_CONFIG,
   JMS_STATE_CUSTOMIZING,
   JMS_STATE_WAITING_JAVA
};

static tstr
JmsStateString(JmsState state)
{
#define CASE_ENUM_TOSTRING(state)                                                                  \
   case state:                                                                                     \
      return _T(#state)

   switch (state) {
      CASE_ENUM_TOSTRING(JMS_STATE_UNKNOWN);
      CASE_ENUM_TOSTRING(JMS_STATE_INITIALIZING);
      CASE_ENUM_TOSTRING(JMS_STATE_WAITING_CONFIG);
      CASE_ENUM_TOSTRING(JMS_STATE_CUSTOMIZING);
      CASE_ENUM_TOSTRING(JMS_STATE_WAITING_JAVA);
   default:
      return tstr::printf(_T("JMS_STATE_%d"), state);
   }
}


#define TICKET_TAG L"KRB5:"
typedef struct _tagUNICODE_STRING {
   UINT16 Length;
   UINT16 MaximumLength;
   WCHAR *Buffer;
} UNICODE_STRING, *PUNICODESTRING;
static DWORD(WINAPI *l_pRtlRunDecodeUnicodeString)(BYTE HashByte, UNICODE_STRING *pString) = NULL;


/* These are dynamically loaded, allowing us to specify a location for the Java
 * VM: */
typedef _JNI_IMPORT_OR_EXPORT_ jint(JNICALL PJNI_GetDefaultJavaVMInitArgs)(void *args);
typedef _JNI_IMPORT_OR_EXPORT_ jint(JNICALL PJNI_CreateJavaVM)(JavaVM **pvm, void **penv,
                                                               void *args);


/* Standard class & method/signatures */
#define DEF_CONSTRUCTORNAME "<init>"
#define DEF_CONSTRUCTORSIGNATURE "()V"

/* Used Java classes */
#define JMS_CLASS_COMPMSG "com/omnissa/vdi/agent/messageserver/ComponentMessage"
#define JMS_CLASS_COMPRESP "com/omnissa/vdi/agent/messageserver/ComponentResponse"
#define JMS_CLASS_MAIN "com/omnissa/vdi/agent/messageserver/Main"
#define JMS_CLASS_TOPICMSGMAN "com/omnissa/vdi/agent/messageserver/TopicMessageManager"
#define JMS_CLASS_TOPICMSGRESP "com/omnissa/vdi/agent/messageserver/TopicMessageResponder"
#define JMS_CLASS_TOPICPUBMAN "com/omnissa/vdi/agent/messageserver/TopicPublishingManager"
#define JMS_CLASS_JMSMETRICS "com/omnissa/vdi/agent/messageserver/JmsMetrics"
#define JMS_CLASS_CUSTSUPPORT "com/omnissa/vdi/agent/messageserver/CustomizationSupport"

/* the orchestrator final async response callback */
void msgResponse(void *Context, wstr &messageId, MessageHandler::respType respType,
                 PropertyBag &responseData, MsgBinary *bin);

void msgResponseAsync(PropertyBag *Context, wstr &messageId, MessageHandler::respType respType,
                      PropertyBag &responseData);

BOOL(__cdecl *g_BridgePluginParams)(PropertyBag *params) = NULL;
BOOL(__cdecl *g_BridgeWsnmJmsParams)(PropertyBag *params, PropertyBag *response) = NULL;


/*-------------------------
 *  myconfig
 *  ---------------------------*/
class myconfig : public sysconfig {
public:
   myconfig(LPCWSTR location, mwstr &items, bool watch) : sysconfig(location, items, watch) {}


   /*
    *-----------------------------------------------------------------------------
    *
    * get --
    *
    *      get
    *
    * Results:
    *    None
    *
    * Side effects:
    *    None
    *
    *-----------------------------------------------------------------------------
    */

   wstr get(const wstr &name)
   {
      coresync sync(m_sync);
      wstr data = sysconfig::get(name);

      if (name.comparei(TEXT(CI_DN)) == 0) {
         if (data.size()) {
            data = ldapUtils::NormaliseDn(data);
         }
      } else if (name.comparei(TEXT(CI_DNSNAME)) == 0) {
         if (!data.size()) {
            data = l_addressCache->GetDomainNameCached();
         }
      } else if (name.comparei(TEXT(CI_POOLDN)) == 0) {
         if (data.size()) {
            data = ldapUtils::NormaliseDn(data);
         }
      }

      return data;
   }
};

/*-------------------------
 *  the JavaBridge
 *  ---------------------------*/

class JavaBridge {
private:
   corecritsec m_stateSync;
   JmsState m_jmsState;

public:
   bool m_stop;
   HANDLE m_stopEvent;
   bool m_started;

   // Configuration
   myconfig *m_configBase;
   sysconfig *m_agentConfig;

   /* the barrier to allow one write thread at a time access to the javaVM */
   HANDLE m_barrier;

   wstr m_netbiosName;

   /* the java vm */
   HMODULE m_hJavaVM;
   JavaVM *m_javaVm;

   /* The messaging system object - this is retained as a global reference */
   /* for the duration of the lifetime of the process. */

   /*
    * Hans 2010-04-01
    *
    * The wsnm_jms has always done an ExitProcess to stop.
    * The JavaBridge has always been a static object and the deconstructor
    * will always have failed if called to destruct the mainObject after
    * the ExitProcess call.
    *
    * If this static deconstructor is called or not seems to be Windows
    * version dependent. We have not seen any problem in the area until
    * now when suddenly Windows XP 64 bit displays an ugly message box saying
    * that wsnm_jms has hit an unexpected exception. And it does it 100%
    * reproducible on my XP 64 bit system. No other of my 32 or 64 bit
    * Window systems here does show the error.
    *
    * The msgbox started when the framework was changed to write a crashdump
    * on an exception. Thats too late in the show to do that and now
    * suddenly the system selects to display this message box that it did
    * not display for the exception whithout exception handling.
    *
    * This ugly msgbox if fixed by changing the mainObject to a pointer and
    * drop the delete.
    */

   jobject mainObject;

   JavaBridge()
   {
      // mainObject will be set before m_started is true
      mainObject = NULL;
      m_jmsState = JmsState::JMS_STATE_INITIALIZING;
   }

   ~JavaBridge()
   {
      // Here we by purpose do NOT delete the mainObject
   }

   /*-------------------------
    *  help methods
    *  ---------------------------*/

   void setState(JmsState state)
   {
      {
         coresync sync(m_stateSync);
         m_jmsState = state;
      }
      sysmsg(Debug, L"State: %s", JmsStateString(state));
   }

   JmsState getState()
   {
      coresync sync(m_stateSync);
      return m_jmsState;
   }

   /*
    *-----------------------------------------------------------------------------
    *
    * waitOnStop --
    *
    * Results:
    *    true if the bridge is stopping
    *
    * Side effects:
    *    None
    *
    *-----------------------------------------------------------------------------
    */

   bool waitOnStop(DWORD timeout)
   {
      if (!m_stop) {
         DWORD res = WaitForSingleObject(m_stopEvent, timeout);
         if (res != WAIT_OBJECT_0 && res != WAIT_TIMEOUT) {
            sysmsg(Warn, L"Unexpected result while waiting for shutdown: %u", res);
         }
      }
      return m_stop;
   }


   bool barrierEnter()
   {
      if (!m_barrier) {
         return false;
      }

      if (WaitForSingleObject(m_barrier, 60000) == WAIT_TIMEOUT) {
         sysmsg(Error, L"Timeout waiting for JavaBridge write access");
         return false;
      }

      if (!m_javaVm) {
         SetEvent(m_barrier);
         return false;
      }

      return true;
   }


   /*
    *-----------------------------------------------------------------------------
    *
    * barrierLeave --
    *
    *      barrierLeave
    *
    * Results:
    *    None
    *
    * Side effects:
    *    None
    *
    *-----------------------------------------------------------------------------
    */

   void barrierLeave() { SetEvent(m_barrier); }


   /*
    *-----------------------------------------------------------------------------
    *
    * DynamicIpAddress --
    *
    *      DynamicIpAddress
    *
    * Results:
    *    None
    *
    * Side effects:
    *    None
    *
    *-----------------------------------------------------------------------------
    */

   wstr DynamicIpAddress(ADDRESS_FAMILY family = AF_UNSPEC)
   {
      return l_addressCache->GetIpAddressCached(family);
   }


   /*
    *-----------------------------------------------------------------------------
    *
    * DynamicIpAddresses --
    *
    *      DynamicIpAddresses
    *
    * Results:
    *    None
    *
    * Side effects:
    *    None
    *
    *-----------------------------------------------------------------------------
    */

   bool DynamicIpAddresses(wstr &addr4, wstr &addr6)
   {
      return l_addressCache->GetIpAddressesCached(addr4, addr6);
   }


   /*
    *-----------------------------------------------------------------------------
    *
    * GetMacAddr --
    *
    *      GetMacAddr
    *
    * Results:
    *    Returns the mac address of the network interface according to the
    *    ADDRESS_FAMILY
    *
    * Side effects:
    *    None
    *
    *-----------------------------------------------------------------------------
    */

   wstr GetMacAddr(ADDRESS_FAMILY family) { return l_addressCache->GetMacAddrCached(family); }


   /*
    *-----------------------------------------------------------------------------
    *
    * GetPrefixLength --
    *
    *      GetPrefixLength
    *
    * Results:
    *    Gets the prefix length of the
    *    IPv4 or IPv6 address that we are using to connect to depending on the
    *    ADDRESS_FAMILY passed to it.
    *
    * Side effects:
    *    None
    *
    *-----------------------------------------------------------------------------
    */

   UINT8
   GetPrefixLength(ADDRESS_FAMILY family) { return l_addressCache->GetPrefixLengthCached(family); }


   /*
    *-----------------------------------------------------------------------------
    *
    * ServerDnsName --
    *
    *      ServerDnsName
    *
    * Results:
    *    None
    *
    * Side effects:
    *    None
    *
    *-----------------------------------------------------------------------------
    */

   tstr ServerDnsName() { return l_addressCache->GetDomainNameCached(); }


   /*-------------------------
    *  gotMessageResponse
    *  ---------------------------*/

   void gotMessageResponse(wstr &jmsMessageId, wstr &XMLResponse, DWORD startTick)
   {
      if (!m_started) {
         sysmsg(Debug, L"%s before m_started set", _T(__FUNCTION__));
         return;
      }

      if (!barrierEnter()) {
         sysmsg(Debug, L"Could not enter barrier for %s", _T(__FUNCTION__));
         return;
      }

      /* Attach thread, locate responder class. May already be attached,
       * depending on which thread we're called on, so only attach if not
       * already attached */
      JNIEnv *jniEnv = NULL;
      bool alreadyAttached = attached(&jniEnv);
      if (!alreadyAttached) {
         if (!attach_vm(&jniEnv)) {
            barrierLeave();
            return;
         }
      }

      try {
         jclass messagingMainClass;
         jmethodID methodId;

         CHECKJNI(messagingMainClass = jniEnv->FindClass(JMS_CLASS_MAIN));

         /* Get the instance of the TopicMessageManager */

         CHECKJNI(methodId = jniEnv->GetMethodID(messagingMainClass, "getTopicMessageResponder",
                                                 "()L" JMS_CLASS_TOPICMSGRESP ";"));

         JavaObject topicMessageResponder(jniEnv, jniEnv->CallObjectMethod(mainObject, methodId));

         CheckJavaException(jniEnv, L"Retrieving topicMessageResponder");

         /* Construct message, and "put" into the responder */

         jclass topicMessageResponderClass;
         jclass componentResponseClass;

         jmethodID putMessageMethodId;
         jmethodID setReplyToJMSMsgIdId;
         jmethodID setMessageTextId;
         jmethodID constructorId;

         CHECKJNI(topicMessageResponderClass = jniEnv->FindClass(JMS_CLASS_TOPICMSGRESP));
         CHECKJNI(componentResponseClass = jniEnv->FindClass(JMS_CLASS_COMPRESP));

         CHECKJNI(putMessageMethodId = jniEnv->GetMethodID(topicMessageResponderClass, "putMessage",
                                                           "(L" JMS_CLASS_COMPRESP ";)V"));
         CHECKJNI(setReplyToJMSMsgIdId = jniEnv->GetMethodID(
                     componentResponseClass, "setReplyToJMSMsgId", "(Ljava/lang/String;)V"));
         CHECKJNI(setMessageTextId = jniEnv->GetMethodID(componentResponseClass, "setMessageText",
                                                         "(Ljava/lang/String;)V"));
         CHECKJNI(constructorId = jniEnv->GetMethodID(componentResponseClass, DEF_CONSTRUCTORNAME,
                                                      DEF_CONSTRUCTORSIGNATURE));

         JavaObject componentResponseObject(
            jniEnv, jniEnv->NewObject(componentResponseClass, constructorId));
         CheckJavaException(jniEnv, L"Creating a component response object");

         JavaString msgIdString(jniEnv, jmsMessageId);
         JavaString msgTextString(jniEnv, XMLResponse);

         jniEnv->CallVoidMethod(componentResponseObject, setReplyToJMSMsgIdId,
                                (jstring)msgIdString);
         CheckJavaException(jniEnv, L"Setting JMS message id");

         jniEnv->CallVoidMethod(componentResponseObject, setMessageTextId, (jstring)msgTextString);
         CheckJavaException(jniEnv, L"Setting JMS message text");

         /* Now shove this into the responder: */


         /* already logged by TopicMessageManager, only display the ticks */
         /*       sysmsg(Trace,L"java bridge: JMS Message Response (ticks=%u)
          * '%s'",GetTickCount() - startTick,XMLResponse); */
         sysmsg(Trace, L"java bridge: JMS Message Response (ticks=%u)", GetTickCount() - startTick);

         jniEnv->CallVoidMethod(topicMessageResponder, putMessageMethodId,
                                (jobject)componentResponseObject);
         CheckJavaException(jniEnv, L"Inserting message into the responder");
      } catch (...) {
         sysmsg(Error, L"Failed to respond to the command message.");
      }

      // Only detach if we had to attach.
      if (!alreadyAttached) {
         detach_vm(&jniEnv);
      }

      barrierLeave();
   }


   /*
    *-----------------------------------------------------------------------------
    *
    * discardMessage --
    *
    *      Notify JMS bridge we have ignored a message response (and so it won't
    *      be receiving a response to a message it sent us). The JMS bridge
    *      maintains a message map that needs to be updated to reflect this.
    *
    * Results:
    *    None
    *
    * Side effects:
    *    None
    *
    *-----------------------------------------------------------------------------
    */

   void discardMessage(wstr &jmsMessageId, DWORD startTick)
   {
      if (!m_started) {
         sysmsg(Debug, L"%s before m_started set", _T(__FUNCTION__));
         return;
      }

      if (!barrierEnter()) {
         sysmsg(Debug, L"Could not enter barrier for %s", _T(__FUNCTION__));
         return;
      }

      /* Attach thread, locate responder class: */
      JNIEnv *jniEnv = NULL;
      if (!attach_vm(&jniEnv)) {
         barrierLeave();
         return;
      }

      try {
         jclass messagingMainClass;
         jmethodID methodId;

         CHECKJNI(messagingMainClass = jniEnv->FindClass(JMS_CLASS_MAIN));

         /* Get the instance of the TopicMessageResponder */
         CHECKJNI(methodId = jniEnv->GetMethodID(messagingMainClass, "getTopicMessageResponder",
                                                 "()L" JMS_CLASS_TOPICMSGRESP ";"));

         JavaObject topicMessageResponder(jniEnv, jniEnv->CallObjectMethod(mainObject, methodId));

         CheckJavaException(jniEnv, L"Retrieving topicMessageResponder");

         /* Notify the message responder of the discard */
         jclass topicMessageResponderClass;
         jmethodID discardMessageMethodId;

         CHECKJNI(topicMessageResponderClass = jniEnv->FindClass(JMS_CLASS_TOPICMSGRESP));

         CHECKJNI(discardMessageMethodId = jniEnv->GetMethodID(
                     topicMessageResponderClass, "discardMessage", "(Ljava/lang/String;)V"));

         JavaString msgIdString(jniEnv, jmsMessageId);

         sysmsg(Trace, L"java bridge: JMS Discard Message Response (ticks=%u)",
                GetTickCount() - startTick);

         jniEnv->CallVoidMethod(topicMessageResponder, discardMessageMethodId,
                                (jstring)msgIdString);
         CheckJavaException(jniEnv, L"Notifying responder of message discard");
      } catch (...) {
         sysmsg(Error, L"Failed to notify responder of discarded message.");
      }

      detach_vm(&jniEnv);
      barrierLeave();
   }


   /*-------------------------
    *  PublishJMSMessage
    *      Wrapper for common uses
    *  ---------------------------*/

   void PublishJMSMessage(wstr &message, PropertyBag &properties, bool async = false)
   {
      properties.set(L"POOLMESSAGETYPE", L"AGENTNOTIFICATION");

      PropertyBag bag;
      bag.addBag(PROP_MSGPROPS, properties);
      bag.addBool(PROP_ASYNC, async);
      bag.add(PROP_BODY, message);
      PublishJMSMessage(bag);
   }


   /*-------------------------
    *  PublishJMSMessage
    *  ---------------------------*/

   bool PublishJMSMessage(PropertyBag &bag)
   {
      PropertyBag properties;
      properties &= bag.getBag(PROP_MSGPROPS);
      wstr topicName = bag.get(PROP_TOPIC, L"");
      wstr messageBody = bag.get(PROP_BODY, L"");
      bool async = bag.getBool(PROP_ASYNC, false);
      bool queued = false;
      bool logMsgInTraceLevel = bag.getBool(LOG_MSG_TRACE_LEVEL, false);

      /*
       * TODO mpryor: need to double check this block is redunant in all paths,
       *      as msgResponseCommon looks to be called earlier in all cases
       */
      wstr addr4, addr6;
      if (DynamicIpAddresses(addr4, addr6)) {
         if (addr4.size()) {
            properties.set(TEXT(MY_IP), addr4.p());
         }
         if (addr6.size()) {
            properties.set(TEXT(MY_IP6), addr6.p());
         }
      }

      if (!m_started || !barrierEnter()) {
         return queued;
      }

      JNIEnv *jniEnv = NULL;
      if (!attach_vm(&jniEnv)) {
         barrierLeave();
         return queued;
      }

      try {
         jclass messagingMainClass;
         jmethodID methodId;

         CHECKJNI(messagingMainClass = jniEnv->FindClass(JMS_CLASS_MAIN));

         CHECKJNI(methodId = jniEnv->GetMethodID(messagingMainClass, "getTopicPublishingManager",
                                                 "()L" JMS_CLASS_TOPICPUBMAN ";"));

         JavaObject topicPublishingManager(jniEnv, jniEnv->CallObjectMethod(mainObject, methodId));

         CheckJavaException(jniEnv, L"Retrieving topicPublishingManager");

         /* Construct message, and "put" into the manager */

         jclass topicPublishingManagerClass;
         jclass componentResponseClass;

         jmethodID putMessageMethodId;
         jmethodID setMessageTextId;
         jmethodID setReplyToJMSMsgIdId;
         jmethodID setMessageTextInTraceModeId;
         jmethodID setStringPropertyId;
         jmethodID constructorId;
         jmethodID setAsyncId;

         CHECKJNI(topicPublishingManagerClass = jniEnv->FindClass(JMS_CLASS_TOPICPUBMAN));
         CHECKJNI(componentResponseClass = jniEnv->FindClass(JMS_CLASS_COMPRESP));

         CHECKJNI(putMessageMethodId = jniEnv->GetMethodID(
                     topicPublishingManagerClass, "putMessage", "(L" JMS_CLASS_COMPRESP ";)V"));
         CHECKJNI(setMessageTextId = jniEnv->GetMethodID(componentResponseClass, "setMessageText",
                                                         "(Ljava/lang/String;)V"));
         CHECKJNI(setMessageTextInTraceModeId = jniEnv->GetMethodID(
                     componentResponseClass, "setMessageTextInTraceMode", "(Ljava/lang/String;)V"));
         CHECKJNI(setReplyToJMSMsgIdId = jniEnv->GetMethodID(
                     componentResponseClass, "setReplyToJMSMsgId", "(Ljava/lang/String;)V"));
         CHECKJNI(setStringPropertyId =
                     jniEnv->GetMethodID(componentResponseClass, "setStringProperty",
                                         "(Ljava/lang/String;Ljava/lang/String;)V"));
         CHECKJNI(setAsyncId = jniEnv->GetMethodID(componentResponseClass, "setAsync", "()V"));
         CHECKJNI(constructorId = jniEnv->GetMethodID(componentResponseClass, DEF_CONSTRUCTORNAME,
                                                      DEF_CONSTRUCTORSIGNATURE));

         JavaObject componentResponseObject(
            jniEnv, jniEnv->NewObject(componentResponseClass, constructorId));

         CheckJavaException(jniEnv, L"Creating a component response object");

         JavaString msgTextString(jniEnv, messageBody);

         if (logMsgInTraceLevel) {
            jniEnv->CallVoidMethod(componentResponseObject, setMessageTextInTraceModeId,
                                   (jstring)msgTextString);
         } else {
            jniEnv->CallVoidMethod(componentResponseObject, setMessageTextId,
                                   (jstring)msgTextString);
         }
         CheckJavaException(jniEnv, L"Setting JMS message text");

         if (topicName.size()) {
            JavaString msgIdString(jniEnv, topicName);
            jniEnv->CallVoidMethod(componentResponseObject, setReplyToJMSMsgIdId,
                                   (jstring)msgIdString);
            CheckJavaException(jniEnv, L"Setting JMS message replyTo");
         }

         if (async) {
            jniEnv->CallVoidMethod(componentResponseObject, setAsyncId);
            CheckJavaException(jniEnv, L"Setting JMS message as async");
         }

         /* Iterate properties: */

         for (size_t i = 0, n = properties.size(); i < n; i++) {
            if (!properties.isBag(i)) {
               JavaString propertyName(jniEnv, properties.getName(i));

               /* To be safe... */
               if (properties.getName(i).comparei(L"PASSWORD") == 0) {
                  continue;
               }

               JavaString propertyValue(jniEnv, properties.get(i));

               jniEnv->CallVoidMethod(componentResponseObject, setStringPropertyId,
                                      (jstring)propertyName, (jstring)propertyValue);

               CheckJavaException(jniEnv, L"Setting property value");
            }
         }


         /* Insert an event ID: */
         wstr eventId;
         eventId.newGuid();
         JavaString eventIdValue(jniEnv, eventId);
         JavaString eventIdName(jniEnv, L"EVENTID");
         jniEnv->CallVoidMethod(componentResponseObject, setStringPropertyId, (jstring)eventIdName,
                                (jstring)eventIdValue);

         CheckJavaException(jniEnv, L"Setting property value");

         /* Now shove this into the publishing manager: */

         jniEnv->CallVoidMethod(topicPublishingManager, putMessageMethodId,
                                (jobject)componentResponseObject);

         CheckJavaException(jniEnv, L"Inserting message into the publisher");
         queued = true;
      } catch (...) {
         sysmsg(Error, L"Failed to publish the message.");
      }

      detach_vm(&jniEnv);
      barrierLeave();
      return queued;
   }


   /*
    *-----------------------------------------------------------------------------
    *
    * Customization --
    *
    *      Send customization message to broker.
    *
    * Results:
    *    True if settings were reconfigured without the need for a restart.
    *
    * Side effects:
    *    None
    *
    *-----------------------------------------------------------------------------
    */

   bool Customization(wstr &customizationState)
   {
      jboolean result = JNI_FALSE;

      if (!barrierEnter()) {
         sysmsg(Debug, L"Could not enter barrier for %s", _T(__FUNCTION__));
         return false;
      }

      JNIEnv *jniEnv = NULL;
      if (!attach_vm(&jniEnv)) {
         return false;
      }

      try {
         jclass messagingCustSupportClass;
         jmethodID methodId;
         jobject custObject;

         CHECKJNI(messagingCustSupportClass = jniEnv->FindClass(JMS_CLASS_CUSTSUPPORT));
         /* Retrieve the object instance for this messaging instance */
         CHECKJNI(methodId =
                     jniEnv->GetStaticMethodID(messagingCustSupportClass, "GetSingletonInstance",
                                               "()L" JMS_CLASS_CUSTSUPPORT ";"));
         CHECKJNI(custObject = jniEnv->CallStaticObjectMethod(messagingCustSupportClass, methodId));
         CheckJavaException(jniEnv, L"Retrieving Main class from messaging system");

         /* Send customization message */
         jmethodID methodIdSendCustom;
         CHECKJNI(methodIdSendCustom = jniEnv->GetStaticMethodID(
                     messagingCustSupportClass, "sendCustomizationMsg", "(Ljava/lang/String;)Z"));
         JavaString custState(jniEnv, customizationState);

         result = jniEnv->CallStaticBooleanMethod(messagingCustSupportClass, methodIdSendCustom,
                                                  (jstring)custState);

         CheckJavaException(jniEnv, L"Sending Customization Message");
         custState.Release();
      } catch (coreException &e) {
         sysmsg(m_stop ? Trace : Error, L"%s - error %d", e.m_Description, e.m_Error);
      } catch (...) {
         sysmsg(m_stop ? Trace : Error, L"Reader exception whilst invoking the JVM.");
      }

      detach_vm(&jniEnv);
      barrierLeave();
      return (result == JNI_TRUE);
   }


   /*
    *-----------------------------------------------------------------------------
    *
    * Reconfigure --
    *
    *      Reconfigure the JMS settings in the Java code.
    *
    * Results:
    *    True if settings were reconfigured without the need for a restart.
    *
    * Side effects:
    *    None
    *
    *-----------------------------------------------------------------------------
    */

   bool Reconfigure()
   {

      jboolean result = JNI_FALSE;

      if (!m_started) {
         sysmsg(Debug, L"%s before m_started set", _T(__FUNCTION__));
         return false;
      }

      if (!barrierEnter()) {
         sysmsg(Debug, L"Could not enter barrier for %s", _T(__FUNCTION__));
         return false;
      }

      JNIEnv *jniEnv = NULL;
      if (!attach_vm(&jniEnv)) {
         return false;
      }

      try {
         jclass messagingMainClass;
         jmethodID methodId;
         CHECKJNI(messagingMainClass = jniEnv->FindClass(JMS_CLASS_MAIN));

         /* Reconfigure the messaging system */
         CHECKJNI(methodId = jniEnv->GetMethodID(messagingMainClass, "Reconfigure",
                                                 "(Ljava/lang/String;Ljava/lang/String;"
                                                 "Ljava/lang/String;Ljava/lang/String;"
                                                 "Ljava/lang/String;Ljava/lang/String;"
                                                 "Ljava/lang/String;Ljava/lang/String;"
                                                 "Ljava/lang/String;Ljava/lang/String;)Z"));

         /* Reload the config - TODO: do this process wide */
         m_configBase->reload();

         wstr oldStyleDn = L"";

         JavaString infrastructureZone(jniEnv, m_configBase->get(TEXT(CI_INFRAZONE)));
         JavaString serverPoolDn(jniEnv, m_configBase->get(TEXT(CI_POOLDN)));
         JavaString serverDn(jniEnv, m_configBase->get(TEXT(CI_DN)));
         JavaString compatibleDn(jniEnv, oldStyleDn);
         JavaString msMode(jniEnv, m_configBase->get(TEXT(CI_MSSECMODE)));
         JavaString agentIdentity(jniEnv, m_configBase->get(TEXT(CI_AGENTID)));
         JavaString agentPrivateKey(jniEnv, m_configBase->get(TEXT(CI_AGENTKEY)));
         JavaString brokerPublicKey(jniEnv, m_configBase->get(TEXT(CI_BROKERKEY)));
         JavaString managed(jniEnv, m_configBase->get(TEXT(CI_MANAGED)));
         JavaString asyncSessionSeconds(jniEnv, m_configBase->get(TEXT(CI_ASYNCSESS)));

         result = jniEnv->CallBooleanMethod(
            mainObject, methodId, (jstring)serverDn, (jstring)compatibleDn, (jstring)serverPoolDn,
            (jstring)infrastructureZone, (jstring)msMode, (jstring)agentIdentity,
            (jstring)agentPrivateKey, (jstring)brokerPublicKey, (jstring)managed,
            (jstring)asyncSessionSeconds);

         CheckJavaException(jniEnv, L"Reconfiguring the messaging system");

         infrastructureZone.Release();
         serverPoolDn.Release();
         serverDn.Release();

      } catch (coreException &e) {
         sysmsg(m_stop ? Trace : Error, L"%s - error %d", e.m_Description, e.m_Error);
      } catch (...) {
         sysmsg(m_stop ? Trace : Error, L"Reader exception whilst invoking the JVM.");
      }

      detach_vm(&jniEnv);
      barrierLeave();
      return (result == JNI_TRUE);
   }


   /*
    *-----------------------------------------------------------------------------
    *
    * getJmsMetrics --
    *
    *    Look at local and Java state to provide a single overall JMS state and
    *    provide basic queue metrics.
    *
    * Results:
    *    None
    *
    * Side effects:
    *    None
    *
    *-----------------------------------------------------------------------------
    */

   void getJmsMetrics(wstr &state, int &outgoingQueueSize, int &incomingQueueSize)
   {

      JmsState currentState = getState();

      state = JmsStateString(currentState);
      outgoingQueueSize = 0;
      incomingQueueSize = 0;

      // If Java VM isn't up, return local state
      if (currentState < JMS_STATE_WAITING_JAVA) {
         return;
      }

      // Try to get metrics from Java VM
      JNIEnv *jniEnv = NULL;
      if (!attach_vm(&jniEnv)) {
         sysmsg(Debug, L"Unable to attach, Java VM not ready yet");
         return;
      }

      try {
         jclass messagingMainClass;
         jmethodID methodId;

         CHECKJNI(messagingMainClass = jniEnv->FindClass(JMS_CLASS_MAIN));

         // Get the metrics
         CHECKJNI(methodId = jniEnv->GetMethodID(messagingMainClass, "getJmsMetrics",
                                                 "()L" JMS_CLASS_JMSMETRICS ";"));

         JavaObject jmsMetricsObject(jniEnv, jniEnv->CallObjectMethod(mainObject, methodId));

         CheckJavaException(jniEnv, L"Retrieving JmsMetrics");

         // Read metrics from JmsMetrics object
         jclass jmsMetricsClass;
         jmethodID getStateAsStringId;
         jmethodID getOutgoingQueueSizeId;
         jmethodID getIncomingQueueSizeId;

         CHECKJNI(jmsMetricsClass = jniEnv->FindClass(JMS_CLASS_JMSMETRICS));

         CHECKJNI(getStateAsStringId = jniEnv->GetMethodID(jmsMetricsClass, "getStateAsString",
                                                           "()Ljava/lang/String;"));
         CHECKJNI(getOutgoingQueueSizeId =
                     jniEnv->GetMethodID(jmsMetricsClass, "getOutgoingQueueSize", "()I"));
         CHECKJNI(getIncomingQueueSizeId =
                     jniEnv->GetMethodID(jmsMetricsClass, "getIncomingQueueSize", "()I"));

         JavaString stateStr(jniEnv,
                             jniEnv->CallObjectMethod(jmsMetricsObject, getStateAsStringId));
         CheckJavaException(jniEnv, L"Getting state");
         state = stateStr.getWString();

         outgoingQueueSize = jniEnv->CallIntMethod(jmsMetricsObject, getOutgoingQueueSizeId);
         CheckJavaException(jniEnv, L"Getting outgoing queue size");

         incomingQueueSize = jniEnv->CallIntMethod(jmsMetricsObject, getIncomingQueueSizeId);
         CheckJavaException(jniEnv, L"Getting incoming queue size");

         sysmsg(Trace,
                L"java bridge: state=%s, outgoing queue size=%d, incoming "
                L"queue size=%d",
                state, outgoingQueueSize, incomingQueueSize);
      } catch (...) {
         sysmsg(Error, L"Failed to get JMS metrics.");
      }

      detach_vm(&jniEnv);
   }

   /*
    *-----------------------------------------------------------------------------
    *
    * handleSensitiveData --
    *
    * Some fields may be encrypted using ASKS and need re-encrypting to use
    * our shared master secret (to replace hardcoded SSO key). Ideally we would
    * do that when we first get the message from SwiftMQ in Java land in
    * TopicMessageManager.onMessage() but that would require us to parse the
    * message body string as XML, find the fields needing re-encryption,
    * re-encrypt them, and return the XML back to a string. This would be
    * expensive, so we do re-encryption here instead where we have already
    * converted the XML message string into a PropertyBag.
    * Typical fields requiring re-encryption are PASSWORD, SMARTCARDPIN
    * or WHFBSESSIONPIN, and only one of those is typically set.
    *
    * Results:
    *    None
    *
    * Side effects:
    *    Encrypted fields are re-encrypted using SMSS key.
    *
    *-----------------------------------------------------------------------------
    */

   void handleSensitiveData(JNIEnv *jniEnv, jclass &topicMessageManagerClass,
                            jclass &componentMessageClass, JavaObject &topicMessageManager,
                            JavaObject &componentMessage, PropertyBag &bag)
   {
      jmethodID getMessageDataEncryptionKeyId;

      CHECKJNI(getMessageDataEncryptionKeyId = jniEnv->GetMethodID(
                  componentMessageClass, "getMessageDataEncryptionKey", "()Ljava/lang/String;"));

      /* If key is present in the JMS message header we need to re-encrypt one
       * or more fields. */
      JavaString wrappedDataKey(
         jniEnv, jniEnv->CallObjectMethod(componentMessage, getMessageDataEncryptionKeyId));
      wstr wrappedDataKeyText = wrappedDataKey.getWString();

      if (wrappedDataKeyText.size()) {
         sysmsg(Trace, L"Sensitive data key present");

         /* The key is wrapped by an encryption key managed by Java, so Java
          * needs to unwrap it for us.
          * TODO: Move this to native once security manager supports it
          */
         jmethodID unwrapMessageDataEncryptionKeyId;
         CHECKJNI(unwrapMessageDataEncryptionKeyId =
                     jniEnv->GetMethodID(topicMessageManagerClass, "unwrapMessageDataEncryptionKey",
                                         "(Ljava/lang/String;)Ljava/lang/String;"));

         JavaString unwrappedDataKey(
            jniEnv, jniEnv->CallObjectMethod(topicMessageManager, unwrapMessageDataEncryptionKeyId,
                                             (jstring)wrappedDataKey));
         wstr unwrappedDataKeyText = unwrappedDataKey.getWString();

         if (unwrappedDataKeyText.size()) {
            /* We can use native Security Manager from now on. Prepare a
             * propertybag containing the deciphered key. Security manager will
             * need this to be able to re-encrypt our data */
            PropertyBag params;
            mstr decipheredKey = SecurityManager().Decrypt(unwrappedDataKey.getAString());
            CORE::MsgBinary keyBin(decipheredKey.p_upd(), decipheredKey.s(), false, false);
            params.setBinary(TEXT("SessionKeyForDecrypt"), keyBin.pBinary, keyBin.sBinary);

            /* Look for any field value with the ASKS marker and re-encrypt */
            for (size_t i = 0, n = bag.size(); i < n; i++) {
               if (bag.isBag(i)) {
                  continue;
               }

               wstr name = bag.getName(i);
               wstr value = bag.get(i);

               if (value.size() && (value.find(L"{#-ASKS=") == 0)) {
                  sysmsg(Trace, L"%s field needs re-encrypting", name);

                  try {
                     // Re-encrypt using our SMSS key
                     mstr reencryptedString =
                        SecurityManager().EncryptEx(value._mstr(), "NodeMS", "", 0, &params);

                     // Replace original value with re-encrypted value
                     bag.set(name, wstr::to_wstr(reencryptedString));

                  } catch (SecurityException se) {
                     sysmsg(Error, TEXT("SecurityManager call FAILED: %s"), se.m_Description);
                  }
               }
            }
         } else {
            sysmsg(Error, L"Error extracting encrypted data key");
         }
      }
   }


   /*-------------------------
    *  ReaderThread
    *  ---------------------------*/

   void ReaderThread(HANDLE sync)
   {
      JNIEnv *jniEnv = NULL;
      if (!attach_vm(&jniEnv)) {
         return;
      }

      try {
         jclass messagingMainClass;
         jmethodID methodId;
         CHECKJNI(messagingMainClass = jniEnv->FindClass(JMS_CLASS_MAIN));

         /* Retrieve the object instance for this messaging instance */

         CHECKJNI(methodId = jniEnv->GetStaticMethodID(messagingMainClass, "GetSingletonInstance",
                                                       "()L" JMS_CLASS_MAIN ";"));

         jobject tempMainObject;

         CHECKJNI(tempMainObject = jniEnv->CallStaticObjectMethod(messagingMainClass, methodId));

         CHECKJNI(mainObject = jniEnv->NewGlobalRef(tempMainObject));

         CheckJavaException(jniEnv, L"Retrieving Main class from messaging system");

         /* Start the messaging system */
         CHECKJNI(methodId = jniEnv->GetMethodID(messagingMainClass, "Start",
                                                 "(Ljava/lang/String;Ljava/lang/String;"
                                                 "Ljava/lang/String;Ljava/lang/String;"
                                                 "Ljava/lang/String;Ljava/lang/String;"
                                                 "Ljava/lang/String;Ljava/lang/String;"
                                                 "Ljava/lang/String;Ljava/lang/String;)V"));

         wstr oldStyleDn = L"";

         JavaString infrastructureZone(jniEnv, m_configBase->get(TEXT(CI_INFRAZONE)));
         JavaString serverPoolDn(jniEnv, m_configBase->get(TEXT(CI_POOLDN)));
         JavaString serverDn(jniEnv, m_configBase->get(TEXT(CI_DN)));
         JavaString compatibleDn(jniEnv, oldStyleDn);
         JavaString msMode(jniEnv, m_configBase->get(TEXT(CI_MSSECMODE)));
         JavaString agentIdentity(jniEnv, m_configBase->get(TEXT(CI_AGENTID)));
         JavaString agentPrivateKey(jniEnv, m_configBase->get(TEXT(CI_AGENTKEY)));
         JavaString brokerPublicKey(jniEnv, m_configBase->get(TEXT(CI_BROKERKEY)));
         JavaString managed(jniEnv, m_configBase->get(TEXT(CI_MANAGED)));
         JavaString asyncSessionSeconds(jniEnv, m_configBase->get(TEXT(CI_ASYNCSESS)));

         jniEnv->CallVoidMethod(mainObject, methodId, (jstring)serverDn, (jstring)compatibleDn,
                                (jstring)serverPoolDn, (jstring)infrastructureZone, (jstring)msMode,
                                (jstring)agentIdentity, (jstring)agentPrivateKey,
                                (jstring)brokerPublicKey, (jstring)managed,
                                (jstring)asyncSessionSeconds);

         CheckJavaException(jniEnv, L"Starting the messaging system");

         infrastructureZone.Release();
         serverDn.Release();
         serverPoolDn.Release();

         /* Get the instance of the TopicMessageManager */

         CHECKJNI(methodId = jniEnv->GetMethodID(messagingMainClass, "getTopicMessageManager",
                                                 "()L" JMS_CLASS_TOPICMSGMAN ";"));

         JavaObject topicMessageManager(jniEnv, jniEnv->CallObjectMethod(mainObject, methodId));
         CheckJavaException(jniEnv, L"Retrieving topicMessageManager from messaging system");


         /* Wait on messages */

         jclass topicMessageManagerClass;
         jclass componentMessageClass;

         CHECKJNI(topicMessageManagerClass = jniEnv->FindClass(JMS_CLASS_TOPICMSGMAN));
         CHECKJNI(componentMessageClass = jniEnv->FindClass(JMS_CLASS_COMPMSG));

         jmethodID retrieveMessageMethodId;
         jmethodID getMessageTextId;
         jmethodID getJMSMessageIdId;

         CHECKJNI(retrieveMessageMethodId = jniEnv->GetMethodID(
                     topicMessageManagerClass, "retrieveMessage", "()L" JMS_CLASS_COMPMSG ";"));
         CHECKJNI(getMessageTextId = jniEnv->GetMethodID(componentMessageClass, "getMessageText",
                                                         "()Ljava/lang/String;"));
         CHECKJNI(getJMSMessageIdId = jniEnv->GetMethodID(componentMessageClass, "getJMSMessageId",
                                                          "()Ljava/lang/String;"));


         /* reader init is done */
         m_started = true;
         SetEvent(sync);

         while (!m_stop) {
            JavaObject componentMessage(
               jniEnv, jniEnv->CallObjectMethod(topicMessageManager, retrieveMessageMethodId));
            jthrowable javaException = jniEnv->ExceptionOccurred();

            if (javaException) {
               throw coreException(0, L"The Java Bridge reader encountered a java exception whilst "
                                      L"waiting for message");
            }


            /* Retrieve text from component */

            JavaString textMessage(jniEnv,
                                   jniEnv->CallObjectMethod(componentMessage, getMessageTextId));
            CheckJavaException(jniEnv, L"Retrieving incoming message text");

            JavaString msgId(jniEnv, jniEnv->CallObjectMethod(componentMessage, getJMSMessageIdId));
            CheckJavaException(jniEnv, L"Retrieving incoming message ID");


            /* Get contents of message and place in the command handler queue: */

            wstr messageText = textMessage.getWString();
            wstr messageId = msgId.getWString();
            int ndcDepth = -1;

            try {
               sysmsg(Trace, L"java bridge reader: JMS Message Received '%s'", messageText);

               PropertyBag *context = new PropertyBag;
               context->set(L"JMSMessageId", messageId);
               context->setInt(L"StartTick", GetTickCount());

               PropertyBag bag;
               wstr command;

               if (!fromXmlToBag(context, messageText, bag, command)) {
                  if (bag.get(L"ID", L"").size()) {
                     msgResponseAsync(context, wstr(), MessageHandler::MsgError, bag);
                  } else {
                     context->Release();
                  }
               } else {
                  wstr ndcContext = context->get(L"ndcContext", L"");

                  if (ndcContext.size()) {
                     ndcDepth = NDC::getDepth();
                     NDC::inherit(ndcContext.split(NDC_SPLITTER), false);
                  }
                  context->setBag(L"original", bag);

                  if (bag.contains(ENCRYPT_MARK)) {
                     PropertyBag decrypt;

                     if (handleEncrypt(bag, decrypt, context)) {
                        MessageHandler::respType resptype = decrypt.get(L"ERROR", L"").size()
                                                               ? MessageHandler::MsgError
                                                               : MessageHandler::MsgOk;
                        msgResponseAsync(context, wstr(), resptype, decrypt);
                     } else {
                        context->setBool(ENCRYPT_MARK, true);
                        Orchestrator::System()->SendAsyncMsg(QUEUE_DESKTOPMANAGER, command, decrypt,
                                                             msgResponse, 0, context, l_channel);
                     }
                  } else {
                     handleSensitiveData(jniEnv, topicMessageManagerClass, componentMessageClass,
                                         topicMessageManager, componentMessage, bag);

                     Orchestrator::System()->SendAsyncMsg(QUEUE_DESKTOPMANAGER, command, bag,
                                                          msgResponse, 0, context, l_channel);
                  }
               }
            } catch (...) {
               sysmsg(Error,
                      L"java bridge reader got exception whilst sending message to orchestrator");
            }

            if (ndcDepth != -1) {
               NDC::setMaxDepth(ndcDepth);
            }
         }
      } catch (coreException &e) {
         sysmsg(m_stop ? Trace : Error, L"%s - error %d", e.m_Description, e.m_Error);
      } catch (...) {
         sysmsg(m_stop ? Trace : Error, L"Reader exception whilst invoking the JVM.");
      }

      detach_vm(&jniEnv);
   }


   /*-------------------------------
    * storeNetBiosNameForShutdown
    *
    * Retrieve and store the machine name
    * to be sent with the AGENT_SHUTDOWN
    * event. We may not have access to
    * the WinAuth service on shutting
    * down.
    * -------------------------------*/
   void storeNetBiosNameForShutdown()
   {

      PropertyBag resp;
      Orchestrator::System()->SendMsg(L"WinAuth", L"getUserComputerNames", PropertyBag(), resp, 0,
                                      0, l_channel);
      if (resp.get(L"ComputerNameNetBIOS", L"").size()) {
         m_netbiosName = resp.get(L"ComputerNameNetBIOS");
      }
   }


   /*-------------------------
    *  Pre_Startup
    *
    *  We have a bug in Oracle's java net.dll that may cause deadlock.
    *  Here we pre initialize the javabridge without any threads created
    *  so that we can load the needed dll's without thread contention.
    *  NOTE: no sysmsg if not error
    *
    *---------------------------*/

   bool Pre_Startup()
   {
      wstr jvmPath = wstr::readRegistry(utils::regPath() + L"\\Node Manager\\JVM\\JVM Path", L"");

      /* Bug 178663 - VDM Connection Server should use redistriubted JRE */
      if (jvmPath.size() == 0) {
         wstr jreDir = wstr::path(wstr::readRegistry(utils::regPath() + L"\\JavaHome", L""));

         if (jreDir.size() != 0) {
            wstr jvm = jreDir;
            jvm.appendPath(wstr::path(
               wstr::readRegistry(utils::regPath() + L"\\jvmDll", L"bin\\server\\jvm.dll")));

            if (_waccess(jvm, 0) == 0) {
               jvmPath = jvm;
            }
         }
      }

      if (jvmPath.size() == 0) {
         /* Attempt to locate JVM using Javasoft registry keys: */
         wstr javaSoftKeyName = L"HKLM\\SOFTWARE\\JavaSoft\\Java Runtime Environment\\";
         wstr currentVersion = wstr::readRegistry(javaSoftKeyName + L"CurrentVersion", L"");

         if (currentVersion.size() == 0) {
            sysmsg(
               Error,
               L"Unable to load the Java VM - no JVM specified, and no default JVM configured.");
            return false;
         }

         wstr javaVmKeyName = javaSoftKeyName + currentVersion + L"\\";
         jvmPath = wstr::readRegistry(javaVmKeyName + L"RuntimeLib", L"");

         if (jvmPath.size() == 0) {
            sysmsg(Error, L"Unable to load the Java VM - no JVM specified, and no default JVM "
                          L"Runtime configured.");
            return false;
         }
      }

      // The bin directory in the JRE has dependencies for the JVM
      tstr jreBinDir = tstr::dropLastPath(tstr::dropLastPath(jvmPath));
      if (!SetDllDirectoryW(jreBinDir.c_str())) {
         sysmsg(Error, L"Unable to set the Java VM bin directory, error %s", wstr::formatError());
         return false;
      }

      // Load the JVM
      UINT OldErrorMode = SetErrorMode(SEM_NOOPENFILEERRORBOX | SEM_FAILCRITICALERRORS);
      m_hJavaVM = LoadLibraryW(jvmPath.c_str());
      if (!m_hJavaVM) {
         sysmsg(Error, L"Unable to load the Java VM, error %s", wstr::formatError());
         return false;
      }

      // Reset the library search path
      SetDllDirectoryW(NULL);

      tstr netdll = jreBinDir;
      netdll.appendPath(TEXT("net.dll"));
      HMODULE hNetDll = LoadLibraryEx(netdll, 0, LOAD_WITH_ALTERED_SEARCH_PATH);
      if (!hNetDll) {
         sysmsg(Error, TEXT("Load of net.dll FAILED, error: %s"), tstr::formatError());
         return false;
      }

      SetErrorMode(OldErrorMode);

      return true;
   }


   /*-------------------------
    *  Startup
    *  ---------------------------*/

   bool Startup()
   {
      m_stop = false;
      m_started = false;
      m_stopEvent = CreateEvent(0, TRUE, FALSE, 0);
      m_barrier = CreateEvent(0, FALSE, TRUE, 0);

      if (!m_barrier) {
         sysmsg(Error, L"Cannot CreateEvent, error %s", wstr::formatError());
         sendHzMonError(L"Unable to create critical barrier event");
         return false;
      }

      UINT OldErrorMode = SetErrorMode(SEM_NOOPENFILEERRORBOX | SEM_FAILCRITICALERRORS);
      HMODULE hJavaBridge = LoadLibrary(TEXT("ws_java_bridgeDLL.dll"));
      SetErrorMode(OldErrorMode);

      if (hJavaBridge) {
         *(void **)&g_BridgePluginParams = GetProcAddress(hJavaBridge, "PluginParams");
         *(void **)&g_BridgeWsnmJmsParams = GetProcAddress(hJavaBridge, "WsnmJmsParams");
      }

      if (!g_BridgeWsnmJmsParams || !g_BridgePluginParams) {
         sysmsg(Error, L"Unable to load Java bridge dll");
         sendHzMonError(L"Unable to load Java bridge dll");
         return false;
      }

      m_configBase = new myconfig(utils::regPath() + L"\\Node Manager",
                                  mwstr(TEXT(CI_INFRAZONE) TEXT("=site1"),
                                        /* default set in config get */
                                        TEXT(CI_DNSNAME),
                                        /* default set in config get */
                                        TEXT(CI_DN), TEXT(CI_POOLDN), TEXT(CI_MSSECMODE),
                                        TEXT(CI_AGENTID), TEXT(CI_AGENTKEY), TEXT(CI_BROKERKEY),
                                        TEXT(CI_MANAGED), TEXT(CI_VMWAREID), TEXT(CI_ASYNCSESS),
                                        TEXT(CI_SYSPREP) TEXT("=0"), NULL),
                                  true);

      bool enableJavaView =
         wstr::readRegistry(utils::regPath() + L"\\Node Manager\\JVM\\EnableJavaView", L"false")
            .toBool();

      m_agentConfig = new sysconfig(utils::regPath() + L"\\Agent\\Configuration",
                                    mwstr(L"Broker=", L"CustomizationRescheduledFixAttempted=false",
                                          L"CustomizationRescheduledStallTimeout=300", NULL),
                                    true);

      m_javaVm = NULL;

      /* Initialise the VM using ws_java_bridge... */
      PropertyBag jvmParams, jvmResponse;
      jvmParams.loadFromRegistry(utils::regPath() + L"\\Node Manager\\JVM");
      jvmParams.setInt64(L"ExitHandler", (__int64)&javaExitHandler);
      jvmParams.setInt64(L"NMChannel", (__int64)l_channel);
      if (g_BridgeWsnmJmsParams(&jvmParams, &jvmResponse)) {
         m_javaVm = (JavaVM *)jvmResponse.getInt64(L"JavaVM", 0);
      }

      if (!m_javaVm) {
         sysmsg(Fatal, L"Unable to create the Java VM");
         sendHzMonError(L"Unable to create the Java VM");
         return false;
      }

      sysmsg(Trace, L"Java Virtual Machine initialised.");

      /*
       * Add remote queue for javaview. We need to explicitly do this as the
       * default channel used by the dll is not the one to wsnm.
       */
      if (enableJavaView) {
         PropertyBag javaViewBag;
         wstr javaViewService = wstr::printf(L"JavaView-%d", GetCurrentProcessId());
         javaViewBag.set(L"Name", javaViewService);
         javaViewBag.set(L"Description", L"Support Java runtime diagnostics");

         if (Orchestrator::System()->SendMsg(ORCHESTRATOR_SYSTEM_QUEUE, L"AddWorker", javaViewBag,
                                             PropertyBag(), 0, 0,
                                             l_channel) != MessageHandler::MsgOk) {
            sysmsg(Warn, L"Failed to add javaview worker");
         }
      }

      /* Enable additional java services */

      /* start a test service, NOTE because this prog does not use the autoNM option
         we need to manually add the service to the node manager */
      /*
            PropertyBag service;
            service.set(TEXT("serviceName"),TEXT("javaSample"));
            service.set(TEXT("serviceClass"),TEXT("com/omnissa/vdi/samples/samplejavaservice/MyService"));
            params.clear();
            params.setBag(TEXT("service"), service);
            g_BridgePluginParams(&params);
            params.clear();
            params.set(TEXT("Name"),TEXT("javaSample"));
            MessageFrameWork::System()->PostMsg(TEXT("system"),TEXT("AddWorker"),params,l_channel);
      */

      /* Start the EventLogger service */
      PropertyBag eventService, eventParams;
      eventService.set(TEXT("serviceName"), TEXT("EventLoggerService"));
      eventService.set(TEXT("serviceClass"),
                       TEXT("com/omnissa/vdi/agent/messageserver/events/EventLoggerService"));
      eventParams.setBag(TEXT("service"), eventService);
      if (g_BridgePluginParams(&eventParams)) {
         eventParams.clear();
         eventParams.set(TEXT("Name"), TEXT("EventLoggerService"));
         MessageFrameWork::System()->PostMsg(TEXT("system"), TEXT("AddWorker"), eventParams,
                                             l_channel);
      }

      /* Start the TimingProfiler service */
      PropertyBag timingService, timingParams;
      timingService.set(TEXT("serviceName"), TEXT("TimingProfilerService"));
      timingService.set(
         TEXT("serviceClass"),
         TEXT("com/omnissa/vdi/agent/messageserver/timingprofiler/TimingProfilerService"));
      timingParams.setBag(TEXT("service"), timingService);
      if (g_BridgePluginParams(&timingParams)) {
         timingParams.clear();
         timingParams.set(TEXT("Name"), TEXT("TimingProfilerService"));
         MessageFrameWork::System()->PostMsg(TEXT("system"), TEXT("AddWorker"), timingParams,
                                             l_channel);
      }

      return true;
   }


   /*
    *-----------------------------------------------------------------------------
    *
    * PauseJms --
    *
    *      PauseJms
    *
    * Results:
    *    None
    *
    * Side effects:
    *    None
    *
    *-----------------------------------------------------------------------------
    */

   bool PauseJms()
   {
      if (!m_started) {
         sysmsg(Debug, L"%s before m_started set", _T(__FUNCTION__));
         return false;
      }

      JNIEnv *jniEnv = NULL;
      if (!attach_vm(&jniEnv)) {
         return false;
      }

      try {
         jclass messagingMainClass;
         jmethodID methodId;

         CHECKJNI(messagingMainClass = jniEnv->FindClass(JMS_CLASS_MAIN));

         CHECKJNI(methodId = jniEnv->GetMethodID(messagingMainClass, "Pause", "()V"));

         jniEnv->CallVoidMethod(mainObject, methodId);
         CheckJavaException(jniEnv, L"Pausing JMS");
      } catch (...) {
      }
      detach_vm(&jniEnv);

      return true;
   }


   /*
    *-----------------------------------------------------------------------------
    *
    * ResumeJms --
    *
    *      ResumeJms
    *
    * Results:
    *    None
    *
    * Side effects:
    *    None
    *
    *-----------------------------------------------------------------------------
    */

   bool ResumeJms()
   {

      if (!m_started) {
         sysmsg(Debug, L"%s before m_started set", _T(__FUNCTION__));
         return false;
      }

      JNIEnv *jniEnv = NULL;
      if (!attach_vm(&jniEnv)) {
         return false;
      }

      try {
         jclass messagingMainClass;
         jmethodID methodId;

         CHECKJNI(messagingMainClass = jniEnv->FindClass(JMS_CLASS_MAIN));

         CHECKJNI(methodId = jniEnv->GetMethodID(messagingMainClass, "Resume", "()V"));

         jniEnv->CallVoidMethod(mainObject, methodId);
         CheckJavaException(jniEnv, L"Resuming JMS");
      } catch (...) {
      }
      detach_vm(&jniEnv);

      return true;
   }

   /*-------------------------
    *  Shutdown
    *  ---------------------------*/

   bool Shutdown(PropertyBag &bag, PropertyBag &response)
   {
      bool result = false;

      wstr RestartWithParam = bag.get(L"RestartWithParam", L"");
      bool systemShutdown = bag.getBool(L"SystemShutdown", false);
      // Do not shut down wsnm_jmsbridge
      bool keepJmsbridge = bag.getBool(L"KeepJmsbridge", false);

      if (m_started && !m_stop) {
         if (RestartWithParam.comparei(L"Resume") != 0) {

            PropertyBag msgBag, eventBag, attrBag, eventResp;

            if (systemShutdown) {
               msgBag.addBool(L"SYSTEMSHUTDOWN", true);
            }
            PublishJMSMessage(wstr(L"SHUTDOWN"), msgBag);

            eventBag.addInt(L"type", AGENT_SHUTDOWN);
            attrBag.add(PROP_MACHINE_ID, ldapUtils::nameFromDn(m_configBase->get(TEXT(CI_DN))));
            attrBag.add(PROP_POOL_ID, ldapUtils::nameFromDn(m_configBase->get(TEXT(CI_POOLDN))));
            attrBag.add(PROP_MACHINE_NAME, m_netbiosName);
            eventBag.addBag(L"attributes", attrBag);

            if (Orchestrator::System()->SendMsg(L"EventLoggerService", L"SendEvent", eventBag,
                                                eventResp) != MessageHandler::MsgOk) {
               sysmsg(Warn, L"Could not send shutdown event: %s", eventResp.getErrorText());
            } else {
               sysmsg(Trace, L"Send shutdown event ok:\n%s", eventBag.flattenForDisplay());
            }
            sendHzMonInfo(AGENT_SHUTDOWN, eventBag);
         }
      }

      if (l_channel) {
         if (RestartWithParam.size()) {
            /* Need to inform the loader that this is a planned restart */

            PropertyBag bag;
            bag.add(L"Param", RestartWithParam);
            bag.add(TEXT(MY_DN), m_configBase->get(TEXT(CI_DN)));
            Orchestrator::System()->SendMsg(L"JMSBridgeLoader", L"RestartWithParam", bag,
                                            PropertyBag(), 0, 0, l_channel);
         } else if (!keepJmsbridge) {
            /* Need to get rid of the loader that will reload us on failure */

            PropertyBag bag;
            bag.set(L"FileName", L"wsnm_jmsbridge");
            Orchestrator::System()->SendMsg(ORCHESTRATOR_SYSTEM_QUEUE, L"UnloadPlugin", bag,
                                            PropertyBag(), 0, 0, l_channel);
         }
      }

      if (!m_started) {
         sysmsg(Debug, L"%s before m_started set", _T(__FUNCTION__));
         sysmsg(Trace, L"JavaBridge::Shutdown complete");
         /* Bug 368650 - ExitProcess is currently called straight after
          * returning from here. Sleep a bit for logging etc. to finish
          */
         Sleep(500);
         return true;
      }

      if (!barrierEnter()) {
         return false;
      }

      if (m_stop) {
         return true;
      }

      m_stop = true;
      SetEvent(m_stopEvent);

      JNIEnv *jniEnv = NULL;
      if (!attach_vm(&jniEnv)) {
         barrierLeave();
         return false;
      }

      try {
         jclass messagingMainClass;
         jmethodID methodId;

         CHECKJNI(messagingMainClass = jniEnv->FindClass(JMS_CLASS_MAIN));

         /* Bug 368650 - Audit log SHUTDOWN is not captured in the broker logs
          * shutdown is published just before coming here, give it time to get
          * out
          */
         Sleep(500);

         CHECKJNI(methodId = jniEnv->GetMethodID(messagingMainClass, "Stop", "()V"));

         jniEnv->CallVoidMethod(mainObject, methodId);
         CheckJavaException(jniEnv, L"Stopping JMS");
         sysmsg(Trace, L"Sent stop to Java");

         result = true;
      } catch (...) {
         sysmsg(Debug, L"Failed to invoke shutdown operation via Java Bridge.");
      }

      detach_vm(&jniEnv);
      barrierLeave();
      sysmsg(Trace, L"JavaBridge::Shutdown complete");
      /* Bug 368650 - Audit log SHUTDOWN is not captured in the broker logs
       * we will be killed immediatly after this method call, give jms time
       * to close
       */
      Sleep(500);
      return result;
   }

   bool JavaBridge::PublishTopicMessage(PropertyBag &bag);

   /*-------------------------
    *  PublishAsyncNotification
    *  ---------------------------*/
   void PublishAsyncNotification(PropertyBag &notification);

   /*-------------------------
    *  SendNotificationToBroker
    *  ---------------------------*/
   void SendNotificationToBroker(wstr hint, PropertyBag &notification);

   bool attach_vm(JNIEnv **jniEnv);

   void detach_vm(JNIEnv **jniEnv);

   bool attached(JNIEnv **jniEnv);
};

JavaBridge theBridge;


/*------------------------------------------------
 *  the orchestrator final async response callback
 *  --------------------------------------------------*/

void
msgResponseCommon(PropertyBag &responseData)
{
   /* In all command responses, we include the LDAP DN of the */

   /* server responding and the DNS name of the server responding. */

   /* This is done so that the */

   /* correlation of the responses can be done per reponse, rather than */

   /* per answering entity, and this allows the session directors to be */

   /* put into the mix. */


   /* HC 20061103 I use setFirst just to get the xml message to look like the
    * SystemManager ones */


   /* TTE:5017 - If we have a dynamic address, we should return it here */

   /* rather than the hostname, otherwise other parts of the system will fail */

   /* Update to above - DYNAMICIPADDRESS returns a value if a DHCP value was
    * found, */

   /* and the broker works out the right thing to do. */


   /* AW - We may need to override the Server DN if we've had a config message
    * that changes the DN. */
   wstr serverDn = responseData.get(TEXT(MY_DN), theBridge.m_configBase->get(TEXT(CI_DN)));

   int prefixLengthIPv4 = theBridge.GetPrefixLength(AF_INET);
   if (prefixLengthIPv4) {
      responseData.setFirst(wstr(TEXT(MY_PREFIXLENIPV4)), wstr(prefixLengthIPv4, 10));
   }

   wstr macAddrIPv4 = theBridge.GetMacAddr(AF_INET);
   if (macAddrIPv4.size()) {
      responseData.setFirst(wstr(TEXT(MY_MACADDRIPV4)), macAddrIPv4);
   }

   wstr addr4, addr6;
   if (theBridge.DynamicIpAddresses(addr4, addr6)) {
      if (addr4.size()) {
         responseData.setFirst(wstr(TEXT(MY_IPADDR)), addr4);
      }
      if (addr6.size()) {
         responseData.setFirst(wstr(TEXT(MY_IP6ADDR)), addr6);
      }
   }
   responseData.setFirst(wstr(TEXT(MY_DNSNAME)), theBridge.m_configBase->get(TEXT(CI_DNSNAME)));
   responseData.setFirst(wstr(TEXT(MY_POOLDN)), theBridge.m_configBase->get(TEXT(CI_POOLDN)));
   responseData.setFirst(wstr(TEXT(MY_DN)), serverDn);
}


/*
 *-----------------------------------------------------------------------------
 *
 * msgResponse --
 *
 *      msgResponse
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

void
msgResponse(void *Context, wstr &messageId, MessageHandler::respType respType,
            PropertyBag &responseData, MsgBinary *bin)
{
   PropertyBag *context = (PropertyBag *)Context;

   try {
      if ((-1 == responseData.getError()) &&
          (_tcsicmp(responseData.getErrorText(), TEXT("IGNORE")) == 0)) {
         /* Response message can be ignored, but we still need to notify the
          * Java bridge to allow it to maintain its message map.
          */
         theBridge.discardMessage(context->get(L"JMSMessageId", L""),
                                  context->get(L"StartTick", L"0").toUInt());

         context->Release();
         return;
      }

      msgResponseCommon(responseData);

      wstr xml;
      fromBagToXml(context, xml, responseData, respType);

      if (context->getBool(ENCRYPT_MARK, false)) {
         if (respType != MessageHandler::MsgOk) {
            sysmsg(Debug, L"Encrypted JMS message failed, error=%s", responseData.formatError());
         }

         wstr encrypted = encryptResponse(xml, context);
         responseData.attach(context->getBag(L"original"));
         msgResponseCommon(responseData);
         responseData.set(ENCRYPT_MARK, encrypted);

         xml.clear();
         fromBagToXml(context, xml, responseData, MessageHandler::MsgOk);
      }

      theBridge.gotMessageResponse(context->get(L"JMSMessageId", L""), xml,
                                   context->get(L"StartTick", L"0").toUInt());
   } catch (...) {
      sysmsg(Error, L"java bridge got exception whilst handling orchestrator message response");
   }
   context->Release();
}


class asyncRespData {
public:
   PropertyBag *Context;
   wstr messageId;
   MessageHandler::respType respType;
   PropertyBag responseData;

   asyncRespData(PropertyBag *_Context, wstr &_messageId, MessageHandler::respType _respType,
                 PropertyBag &_responseData)
   {
      Context = _Context;
      messageId = _messageId;
      respType = _respType;
      responseData.attach(_responseData);
   }
};

/*
 *-----------------------------------------------------------------------------
 *
 * msgResponseAsyncThread --
 *
 *      msgResponseAsyncThread
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

void
msgResponseAsyncThread(void *args)
{
   asyncRespData *data = (asyncRespData *)args;

   msgResponse(data->Context, data->messageId, data->respType, data->responseData, 0);
   delete data;
}


/*
 *-----------------------------------------------------------------------------
 *
 * msgResponseAsync --
 *
 *      msgResponseAsync
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

void
msgResponseAsync(PropertyBag *Context, wstr &messageId, MessageHandler::respType respType,
                 PropertyBag &responseData)
{
   asyncRespData *data = new asyncRespData(Context, messageId, respType, responseData);

   if (!corerun::run("msgResponseAsyncThread", msgResponseAsyncThread, data)) {
      Context->Release();
      delete data;
   }
}


/*----------------------------
 * PublishTopicMessage
 *---------------------------*/
bool
JavaBridge::PublishTopicMessage(PropertyBag &bag)
{
   // Fill in the standard property fields, without impacting body contents
   PropertyBag properties;
   properties &= bag.getBag(PROP_MSGPROPS);
   msgResponseCommon(properties);

   // Render body to text if applicable
   if (bag.isBag(PROP_BODY)) {
      wstr xml;
      fromBagToXml(&bag, xml, bag.getBag(PROP_BODY), MessageHandler::MsgOk);
      bag.set(PROP_BODY, xml);
   }
   return PublishJMSMessage(bag);
}


/*-------------------------
 *  PublishAsyncNotification
 *  ---------------------------*/
void
JavaBridge::PublishAsyncNotification(PropertyBag &notification)
{
   // Fill in the standard fields
   msgResponseCommon(notification);

   // Render to text
   wstr xml;
   PropertyBag context;
   fromBagToXml(&context, xml, notification, MessageHandler::MsgOk);

   // Send as async message
   PublishJMSMessage(xml, context, true);
}

/*-------------------------
 *  SendNotificationToBroker
 *  ---------------------------*/
void
JavaBridge::SendNotificationToBroker(wstr hint, PropertyBag &notification)
{
   // Fill in the standard fields
   msgResponseCommon(notification);

   // Render to text
   wstr xml;
   PropertyBag context;
   fromBagToXml(&context, xml, notification, MessageHandler::MsgOk);

   // Set message hint as a message property so that we can identify the message in the broker
   context.set(L"MESSAGE_NAME", hint);

   PublishJMSMessage(xml, context);
}

/* This is used to track each thread that has to attach to the Java VM,
 * preventing from detaching before we have finished with it. See bug 2711749
 * for further details */
thread_local int attachCount = 0;

bool
JavaBridge::attach_vm(JNIEnv **jniEnv)
{
   if (!m_javaVm) {
      return false;
   }

   try {
      jint jRetCode = m_javaVm->AttachCurrentThread((void **)jniEnv, NULL);

      if ((jRetCode != 0) || (!*jniEnv)) {
         sysmsg(Error, L"Failed to attach the current thread to the JVM - error %d.", jRetCode);
         return false;
      }

      SetupThreadContextClassLoader(*jniEnv);
      // sysmsg(Trace, L"Attached current thread %u to the JVM.", GetCurrentThreadId());
   } catch (...) {
      sysmsg(Error, L"Exception in attach the current thread to the JVM");
      return false;
   }

   attachCount++;
   if (attachCount > 1) {
      /* This shouldn't happen, is a programming error if it does. Each thread
       * should only attach once and therefore only detach once. If we are
       * seeing more than one attach there is a possibility we will see more
       * than one detach and that may allow the thread to access the VM in a
       * detached state after the first detach and risk a crash if the VM shuts
       * down. See bug 2711749 for further details of this edge case.
       */
      sysmsg(Warn, L"attach_vm: Possible nested attach/detach mismatch.");
   }

   return true;
}


void
JavaBridge::detach_vm(JNIEnv **jniEnv)
{
   attachCount--;
   if (attachCount > 0) {
      /* An attach/detach mismatch has been detected. Nested attaches are a
       * no-op, but we can only detach the thread once - once it is detached, it
       * is detached. Here we prevent nested detaches by only detaching when
       * count goes to 0 */
      sysmsg(Warn, L"detach_vm: nested attach/detach mismatch, not detaching.");
      return;
   }

   if (!m_javaVm) {
      return;
   }

   if ((jniEnv == NULL) || (*jniEnv == NULL)) {
      sysmsg(Warn, L"Thread to detach from the JVM is not currently attached");
      return;
   }

   try {
      ClearJavaException(*jniEnv);
      *jniEnv = NULL;
      m_javaVm->DetachCurrentThread();
   } catch (...) {
      sysmsg(Error, L"Exception in detach the current thread from the JVM");
   }
}

bool
JavaBridge::attached(JNIEnv **jniEnv)
{
   try {
      /* If current thread is already attached to VM this call will return the
       * corresponding JNI env pointer to use */
      jint res = m_javaVm->GetEnv((void **)jniEnv, JNI_VERSION_1_4);

      if (res != JNI_OK) {
         return false;
      }
   } catch (...) {
      sysmsg(Error, L"Exception in getting JVM env");
      return false;
   }

   if (!*jniEnv) {
      sysmsg(Debug, L"javaVm->GetEnv was successful but returned NULL. ThreadId: %u",
             GetCurrentThreadId());
      return false;
   }

   return true;
}
