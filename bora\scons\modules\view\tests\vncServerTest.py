# ************************************************************************
# Copyright (c) Omnissa, LLC. All rights reserved.
# This product is protected by copyright and intellectual property laws in the
# United States and other countries as well as by international treaties.
# -- Omnissa Restricted
# ************************************************************************

#
# vncServerTest.py
#
#   The unit test of vncServer used in the Horizon/View agents.
#   Mailing lists: <EMAIL>, <EMAIL>

import vmware
import vtools.common

Import("env_opts")

vncTestName = "vncServerTest"
env = vmware.DefaultEnvironment()

env.LoadTool("gtest", linkDynamic=True)

hlslNodeNames = ["vnc-win32-hlsl"]

# rxUnitTestLib.py should be added before real test component scons
rxUnitTestLibNodeName = "rxUnitTestLib"
rxUnitTestLibNode = vmware.LookupNode(rxUnitTestLibNodeName, host=env.Host().Name())

rxTestLibDir = rxUnitTestLibNode[0].dir.abspath
rxTestLibName = rxUnitTestLibNodeName

env.Append(
    CPPDEFINES={
        "USERLEVEL": None,
        "VMX86_DESKTOP": None,
    }
)

vncServerSubdirs = [
    "apps/rde/vncServer",
    "apps/rde/vncServer/unittest",
]

env.Prepend(
    CPPPATH=[
        "#bora/lib/public",
        "#bora/public",
    ]
)

env["LINUX_AGENT_UNIT_TEST"] = True

if vmware.Host().IsLinux():
    vncServerSubdirs += [
        "apps/rde/vvc/vvcProxyStub/common",
        "apps/rde/vvc/vvcProxyStub/common/utils/posix",
        "apps/rde/vvc/vvcProxyStub/hub",
        # Do not move this to vmlibs, it requires VVCPROXYHUB_EXPORTS
        "lib/vncConnectionManager",
        # Do not move this to vmlibs, it requires VVCHUB_SUPPORT
        "lib/blastSockets",
    ]

    vmware.LoadTool(
        env,
        tool=[
            "cvt",
            "ffmpeg",
            "keyutils",
            "libssl",
            "xorg",
            "libx264",
            "libyuv",
            "libz",
        ],
    )

    env.Append(
        CCFLAGS=[
            "-Wno-unused-value",
            "-DVVCPROXYHUB_EXPORTS",
            "-DLINUX_VIEW_AGENT",
            "-DUNITTEST",
            "-DVVCHUB_SUPPORT",
            "-fno-inline",
        ],
        CPPPATH=[
            "#bora/apps/horizonrxtest/unitTest/lib",
            "#bora/apps/rde/vncServer",
            "#bora/apps/rde/vvc/vvcProxyStub/hub",
            "#bora/apps/rde/vvc/vvcProxyStub/common",
            "#bora/apps/rde/vvc/vvcProxyStub/common/utils/include",
            "#bora/apps/lib/public",
            "#bora/lib/blastSockets",
        ],
        STATICLIBS=[
            "jpeg",
            "ogg",
            "opus",
            "speexdsp",
            "yuv",
            rxTestLibName,
        ],
        LIBS=[
            "dl",
            "m",
            "pthread",
            "stdc++",
            "unitTestUtils",
            "X11",
            "Xcursor",
            "Xdamage",
            "Xext",
            "Xfixes",
            "Xi",
            "Xinerama",
            "xkbfile",
            "Xrandr",
            "Xtst",
        ],
        LIBPATH=[vmware.LibraryDirectory("unitTestUtils"), rxTestLibDir],
        STATICLIBPATH={rxTestLibName: rxTestLibDir},
    )

    if vmware.BuildType() in ["beta", "obj", "opt"]:
        # stack unwinding code in Util_Backtrace still depends on these
        env.Append(CCFLAGS=["-fno-omit-frame-pointer"])
        env.Append(LINKFLAGS=["-Wl,--export-dynamic"])

if vmware.Host().IsWindows():
    nvapiLib = "nvapi64.lib" if vmware.Host().Is64Bit() else "nvapi.lib"
    env.LoadTool(["intel_sdk"])
    env.LoadTool("amd_rapidfire_sdk", dllName="RapidFire")
    env.Append(
        LIBS=[
            nvapiLib,
            "d3d9.lib",
            "dxva2.lib",
            "legacy_stdio_definitions.lib",
            "legacy_stdio_wide_specifiers.lib",
            "yuv.lib",
            "cryptui.lib",
            "ncrypt.lib",
        ]
    )

    env.LoadTool(
        [
            "msvcrt",
            "atlmfc",
            "fmtlib",
            "libjpeg",
            "libpng",
            "libz",
            "libssl",
            "libxdr",
            "libyuv",
            "libx264",
        ]
    )
    env.LoadTool("mfw")

    env.Append(
        LINKFLAGS=[
            '-base:"0x69500000"',
            "-entry:wWinMainCRTStartup",
            "-delayload:ddraw.dll",
            "-delayload:d3d11.dll",
            "-delayload:dxgi.dll",
            "-subsystem:console",
        ]
    )

    env.Append(
        CPPDEFINES={
            "_UNICODE": None,
            "UNICODE": None,
            "WINVER": "0x0A00",  # Windows 10 and up
        }
    )

    env.Append(
        LIBS=[
            "advapi32.lib",
            "bcrypt.lib",
            "crypt32.lib",
            "d3d11.lib",
            "ddraw.lib",
            "delayimp.lib",
            "dxgi.lib",
            "dxguid.lib",
            "gdi32.lib",
            "glu32.lib",
            "kernel32.lib",
            "msimg32.lib",
            "netapi32.lib",
            "ntdll.lib",
            "oldnames.lib",
            "ole32.lib",
            "oleaut32.lib",
            "psapi.lib",
            "qwave.lib",
            "Rpcrt4.lib",
            "Secur32.lib",
            "Shlwapi.lib",
            "setupapi.lib",
            "shell32.lib",
            "user32.lib",
            "uuid.lib",
            "version.lib",
            "Wtsapi32.lib",
            "wbemuuid.lib",
            "winmm.lib",
            "ws2_32.lib",
        ]
    )

    env.Append(
        CPPPATH=[
            "#bora/lib/vncConnectionManager/win32",
            "#bora/lib/vncConnectionManager/win32/capture",
            "#bora/lib/vncConnectionManager/win32/topology",
            "#bora-vmsoft/svga/hznvidd",
            "#bora-vmsoft/svga/vdisplay",
            "#bora-vmsoft/svga/wddm/include",
        ]
    )

vmware.LoadTool(
    env,
    [
        "libjpeg",
        "libogg",
        "libopus",
        "libpng",
        "libspeexdsp",
        "libz",
        "protobuf-c-3",
        "vm-product",
    ],
)

if vmware.Host().IsWindows():
    env.Append(LIBS=["iphlpapi.lib"])

e = vmware.Executable(vncTestName, env=env)
vncLibs = [
    "asyncsocket",
    "blastControl",
    "blastCodec",
    "cityhash",
    "config",
    "coreDump",
    "crypto",
    "d3des",
    "dict",
    "err",
    "file",
    "hashMap",
    "image",
    "keyboard",
    "lock",
    "log",
    "mempool",
    "misc",
    "panic",
    "poll",
    "pollDefault",
    "productState",
    "raster",
    "rbtree",
    "rectangle",
    "region",
    "sig",
    "slab",
    "sound",
    "soundlib",
    "ssl",
    "string",
    "thread",
    "udpfec",
    "udpProxy",
    "unicode",
    "user",
    "uuid",
    "version",
    "vnc",
    "vvclib",
]

if vmware.Host().IsWindows():
    vncLibs += [
        "blastSockets",
        "config",
        "log",
        "kbdlayoutid",
        "rdsutils",
        "sslRemap",
        "uuid",
        "vncConnectionManager",
        "vnc/win32",
        "win32auth",
        "win32cfgmgr",
        "win32tsf",
        "wmi",
    ]
    e.addGlobalStaticLibs(
        [
            "cedarBase",
            "cedarConfig",
            "cedarLog",
            "cedarTask",
            "ipclib-static-md",
            "objectMap-static-md",
            "smlib-static-md",
            "vvc-view-hub-unicode-md",
            "vvc-view-hub2-unicode-md",
        ]
    )

e.addStaticLibs("vmlibs", vncLibs)
e.addGlobalStaticLibs(
    [
        "vncNvEncSDK8",
        "vncNvEncSDK12",
        "vncReplay",
        "vncReplayHardware",
    ]
)
e.addSubdirs(vncServerSubdirs)

node = e.createProgramNode()

if vmware.Host().IsLinux():
    Depends(node, vmware.LookupNode("unitTestUtils"))
    Depends(node, rxUnitTestLibNode)

if vmware.Host().IsWindows():
    for hlslNodeName in hlslNodeNames:
        hlslNode = vmware.LookupNode(hlslNodeName, host="win32")
        env.Depends(node, dependency=hlslNode)

vmware.RegisterNode(node, vncTestName)

# libstdc++ is not a normal, explicit shared lib of vncServer.
# but we must stage it on linux. On windows the C++ runtime can be
# installed manually as a pre-requisite.
if "LIBSTDCXX_REDIST" in env:
    vmware.RegisterNode([env["LIBSTDCXX_REDIST"]], vncTestName)

vmware.RegisterEnv("%s-env" % vncTestName, env)
vmware.Alias("%s-build" % vncTestName, node)
