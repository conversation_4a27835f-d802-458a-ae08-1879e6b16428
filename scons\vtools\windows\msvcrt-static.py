# Copyright (c) Omnissa, LLC. All rights reserved.
# This product is protected by copyright and intellectual property laws in the
# United States and other countries as well as by international treaties.
# -- Omnissa Restricted

import vmware
import atexit

numConanNonRedistWarnings = 0
numConanRedistWarnings = 0
cachedHosttypeForWarnings = None

# A simple vtool that helps you add the right flags for each msvc runtime


def PrintWarningForConanNonRedist():
    global numConanNonRedistWarnings

    log = vmware.GetLogger("vtools")
    log.warn(
        "WARNING: Non redistributable static libraries used in %d modules "
        "during compilation of hosttype %s. The resulting binaries cannot "
        "be legally redistributed to external parties."
        % (numConanNonRedistWarnings, cachedHosttypeForWarnings)
    )


def PrintWarningForConanRedist():
    global numConanRedistWarnings

    log = vmware.GetLogger("vtools")
    log.warn(
        "%d modules linked to static binaries during compilation of "
        "hosttype %s. This is discouraged because zero day "
        "fixes directly from Microsoft will not be available."
        % (numConanRedistWarnings, cachedHosttypeForWarnings)
    )


def generate(env):
    global numConanNonRedistWarnings
    global numConanRedistWarnings
    global cachedHosttypeForWarnings

    cachedHosttypeForWarnings = vmware.Host().Name()
    log = vmware.GetLogger("vtools")

    if vmware.BuildType() == "obj":
        env.Append(
            CCFLAGS=[
                "-MTd",
            ],
            CPPDEFINES={
                "_MT": None,
            },
            LIBS=[
                "libcmtd.lib",
                "libcpmtd.lib",
            ],
        )
        if env["MSVC_VERSION_TUPLE"] >= (14, 0):
            env["LIBS"] += ["libucrtd.lib", "libvcruntimed.lib"]
        # Print warning when linking against non redistributable component
        if (
            env["MSVC_NON_REDISTS_ROOT"] is not None
            or env["WINDOWS_NON_REDISTS_ROOT"] is not None
        ):
            if numConanNonRedistWarnings == 0:
                atexit.register(PrintWarningForConanNonRedist)
            numConanNonRedistWarnings += 1
    else:
        env.Append(
            CCFLAGS=[
                "-MT",
            ],
            CPPDEFINES={
                "_MT": None,
            },
            LIBS=[
                "libcmt.lib",
                "libcpmt.lib",
            ],
        )
        if env["MSVC_VERSION_TUPLE"] >= (14, 0):
            env["LIBS"] += ["libucrt.lib", "libvcruntime.lib"]
        # Print notice that static linking is not encouraged
        if (
            env["MSVC_REDISTS_ROOT"] is not None
            or env["WINDOWS_REDISTS_ROOT"] is not None
        ):
            if numConanRedistWarnings == 0:
                atexit.register(PrintWarningForConanRedist)
            numConanRedistWarnings += 1

    for sanitizer in vmware.UseSanitizers():
        archDir = "x64" if env.Host().Is64Bit() else "x86"
        libStr = "x86_64" if env.Host().Is64Bit() else "i386"
        debugStr = "_dbg" if vmware.BuildType() == "obj" else ""

        sanitizerEnv = {
            "address": dict(
                SANITIZER_REDISTS=[
                    "$MSVCROOT/bin/Hostx64/%s/llvm-symbolizer.exe" % (archDir),
                ],
            ),
        }

        if sanitizer in sanitizerEnv:
            env.Append(**sanitizerEnv[sanitizer])
        else:
            log.info(
                "Unknown sanitizer %s. Only the following are supported: %s"
                % (sanitizer, list(sanitizerEnv.keys()))
            )
