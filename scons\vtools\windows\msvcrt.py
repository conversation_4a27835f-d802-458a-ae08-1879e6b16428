# Copyright (c) Omnissa, LLC. All rights reserved.
# This product is protected by copyright and intellectual property laws in the
# United States and other countries as well as by international treaties.
# -- Omnissa Restricted

"""Add msvc runtime flags"""


import vmware


def AddManifestFlags(env):
    if env["MSVC_VERSION_TUPLE"] >= (12, 0):
        # We no longer need to take extra steps to deal with manifests starting
        # with MSVC12.
        return

    if env["USING_SDK70_OR_LATER"]:
        # Need this or executables will be created with dependencies on both
        # RTM and SP1 VCRT runtimes.
        if vmware.Host().Is64Bit():
            force = "-include:_forceCRTManifestCUR"
        else:
            force = "-include:__forceCRTManifestCUR"

        env.Append(
            LINKFLAGS=[force],
            SHLIBFLAGS=[force],
            # Define this or executables will be created with a dependency on
            # the RTM VCRT runtime instead of the SP1 runtime.
            CPPDEFINES={"_BIND_TO_CURRENT_VCLIBS_VERSION": "1"},
        )


def generate(env):
    log = vmware.GetLogger("vtools")

    if vmware.BuildType() == "obj":
        env.Append(
            CCFLAGS=[
                "-MDd",
            ],
            CPPDEFINES={
                "_DLL": None,
                "_MT": None,
            },
            LIBS=[
                "msvcrtd.lib",
                "msvcprtd.lib",
            ],
        )
        if env["MSVC_VERSION_TUPLE"] >= (14, 0):
            env["LIBS"] += ["ucrtd.lib", "vcruntimed.lib"]
    else:
        env.Append(
            CCFLAGS=[
                "-MD",
            ],
            CPPDEFINES={
                "_DLL": None,
                "_MT": None,
            },
            LIBS=[
                "msvcrt.lib",
                "msvcprt.lib",
            ],
        )
        if env["MSVC_VERSION_TUPLE"] >= (14, 0):
            env["LIBS"] += ["ucrt.lib", "vcruntime.lib"]

    for sanitizer in vmware.UseSanitizers():
        msvcRoot = env["MSVCROOT"]
        archDir = "x64" if env.Host().Is64Bit() else "x86"
        libStr = "x86_64" if env.Host().Is64Bit() else "i386"

        sanitizerEnv = {
            "address": dict(
                SANITIZER_REDISTS=[
                    # As per https://devblogs.microsoft.com/cppblog/msvc-address-sanitizer-one-dll-for-all-runtime-configurations/
                    # 17.7 and later use the same DLL regardless of /MD or /MDd.
                    f"{msvcRoot}/bin/Hostx64/{archDir}/clang_rt.asan_dynamic-{libStr}.dll",
                    f"{msvcRoot}/bin/Hostx64/{archDir}/llvm-symbolizer.exe",
                ],
            ),
        }

        if sanitizer in sanitizerEnv:
            env.Append(**sanitizerEnv[sanitizer])
        else:
            log.info(
                "Unknown sanitizer %s. Only the following are supported: %s"
                % (sanitizer, list(sanitizerEnv.keys()))
            )

    AddManifestFlags(env)
