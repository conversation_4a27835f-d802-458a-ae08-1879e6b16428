/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/** \file KeyVault.cpp
 *
 *  The KeyVault implementation
 */

/*
 *--------------------------------------------------------------------------
 *
 * Key Vault implementation:
 *
 *    The KeyVault service is built in to the framework core.
 *    It is restricted to local process usage.
 *
 *    We maintain our own persisted store in registry. When we
 *    need to use a key it is taken out of the registry and imported.
 *    This is a slow process using signature compare and
 *    deciphering of the registry key blob.
 *
 *    To get this design to perform we keep a map of PropertyBag's per
 *    key name. These key name PropertyBag's has a subbag for each generation.
 *    We build this map at startup from registry, the generation subbag's
 *    are created empty. When a key is imported, we load
 *    the generation bag with metadata and store the handles in there.
 *    This way we get performance when repeatedly using a key for encrypting.
 *
 *--------------------------------------------------------------------------
 */

#include <stdafx.h>

#ifdef WIN32

#   include "KeyVault.h"
#   include "KeyVaultWorker.h"
#   include "KeyCollection.h"
#   include "KeyVaultImplementation.h"
#   include "KeyVaultShare.h"
#   include <WorkItemInt.h>
#   include "PKCS10Util.h"
#   include <cedar/windows/resource.h>
#   include <cedar/unique_any.h>


#   define CHECK_KEY_TYPE(keydata, type)                                                           \
      if (keydata->get(KEY_TYPE, TEXT("")).compare(type) != 0) {                                   \
         response.setError(TEXT("key exists but is of wrong type"));                               \
         return false;                                                                             \
      }

#   define CHECK_KEY_SUBTYPE(keydata, subtype)                                                     \
      if (keydata->get(KEY_SUBTYPE, TEXT("")).compare(subtype) != 0) {                             \
         response.setError(TEXT("key exists but is of wrong subtype"));                            \
         return false;                                                                             \
      }

#   define VALIDATE_SESSION_KEY_LENGTH(x)                                                          \
      if ((x != 128) && (x != 192) && (x != 256)) {                                                \
         response.setError(TEXT("bad session key length"));                                        \
         return false;                                                                             \
      }

#   define VALIDATE_KEYPAIR_LENGTH(x)                                                              \
      if ((x != 1024) && (x != 2048) && (x != 3072)) {                                             \
         response.setError(TEXT("bad keypair length"));                                            \
         return false;                                                                             \
      }

/*
 * Defines for the Id command.
 * As fix salt we us a 20 byte random number generated by CNG GetRandom
 * and base64 encoded in the define here.
 */
#   define ID_KEYSALT "j/mQrxV+8YhyvoCu4va2HC23IcE="
#   define ID_ITERATIONS 2048


/*
 *--------------------------------------------------------------------------
 *
 * KeyVault
 *
 *    The Key Vault Public Class
 *
 *--------------------------------------------------------------------------
 */

/*
 *--------------------------------------------------------------------------
 *
 * constructor
 *
 *--------------------------------------------------------------------------
 */
KeyVault::KeyVault(corecritsec *pGlobalSync)
{
   pint = new KeyVaultImplementation;
   TRY_MEM_RETV(pint, );
   pint->m_keyVault = this;
   m_globalSync = pGlobalSync;
   m_globalSync->AddRef();
}


/*
 *--------------------------------------------------------------------------
 *
 * destructor
 *
 *--------------------------------------------------------------------------
 */
KeyVault::~KeyVault()
{
   m_globalSync->Release();
   delete pint;
}


/*
 *--------------------------------------------------------------------------
 *
 * addContext
 *
 *   add a new operational context
 *
 * Results:
 *   true if ok
 *
 *--------------------------------------------------------------------------
 */
bool
KeyVault::addContext(DWORD keyLength, KeyName &keyname, PropertyBag &response)
{
   VALIDATE_SESSION_KEY_LENGTH(keyLength);

   /*
    * generate the context
    */

   TempData tmp;
   WORD wordCount = (WORD)(keyLength / 16);
   MsgBinary binTmp;
   TRY_MEM(binTmp.resize(wordCount * 2));
   WORD *pdw = (WORD *)binTmp.p();
   TRY_NTERR(BCryptGenRandom(NULL, (PUCHAR)pdw, wordCount * 2, BCRYPT_USE_SYSTEM_PREFERRED_RNG));

   /*
    * save the context
    */

   coresync sync(m_globalSync);

   CHECK_OK(pint->checkGeneration(keyname, tmp, true, response));
   tmp.pbag->setBinary(KEY_DATA, binTmp.p(), wordCount * 2);
   tmp.pbag->set(KEY_NAME, keyname.name);
   tmp.pbag->setInt(KEY_GEN, keyname.gen);
   tmp.pbag->set(KEY_TYPE, TYPE_CONTEXT);
   tmp.pbag->setInt(KEY_LENGTH, keyLength);
   tmp.pbag->setBool(KEY_PERSIST, true);

   TRY_OK(pint->addPersist(keyname, tmp), TEXT("addPersist"));
   pint->addFinalize(keyname, tmp);
   response.setInt(RESP_GEN, keyname.gen);
   return true;
}


/*
 *--------------------------------------------------------------------------
 *
 * addKey
 *
 *   add a new operational session key
 *
 * Results:
 *   true if ok
 *
 *--------------------------------------------------------------------------
 */
bool
KeyVault::addKey(bool keyPair, DWORD keyLength, KeyName &keyname, KeyName &encipherName,
                 bool persist, PropertyBag &response, bool checkSize)
{
   if (checkSize) {
      if (keyPair) {
         VALIDATE_KEYPAIR_LENGTH(keyLength);
      } else {
         VALIDATE_SESSION_KEY_LENGTH(keyLength);
      }
   }

   /*
    * generate the key
    */

   TempData tmp;
   if (keyPair) {
      TRY_NTERR(BCryptGenerateKeyPair(pint->m_hAlgAsym, &tmp.hkey, keyLength, 0));
      TRY_NTERR(BCryptFinalizeKeyPair(tmp.hkey, 0));

   } else {
      DWORD sSecret = (keyLength == 128) ? 16 : ((keyLength == 256) ? 32 : 24);
      MsgBinary secret(malloc(sSecret), sSecret);
      TRY_NTERR(
         BCryptGenRandom(0, (PUCHAR)secret.pBinary, sSecret, BCRYPT_USE_SYSTEM_PREFERRED_RNG));
      TRY_NTERR(
         BCryptGenerateSymmetricKey(pint->m_hAlgSym, &tmp.hkey, NULL, 0, secret.p(), sSecret, 0));
   }

   /*
    * save the key
    */

   coresync sync(m_globalSync);

   CHECK_OK(pint->checkGeneration(keyname, tmp, true, response));
   TempData tmpEncipher;
   if (persist) {
      CHECK_OK(pint->checkGeneration(encipherName, tmpEncipher, false, response));
      CHECK_KEY_TYPE(tmpEncipher.pbag, TYPE_MASTER);
   }
   tmp.pbag->set(KEY_NAME, keyname.name);
   tmp.pbag->setInt(KEY_GEN, keyname.gen);
   tmp.pbag->set(KEY_TYPE, TYPE_OPERATIONAL);
   tmp.pbag->set(KEY_SUBTYPE, keyPair ? SUBTYPE_ASYMMETRIC : SUBTYPE_SYMMETRIC);
   tmp.pbag->setInt(KEY_LENGTH, keyLength);
   tmp.pbag->setBool(KEY_PERSIST, persist);

   if (persist) {
      tmp.pbag->set(KEY_ENCIPHER_NAME, encipherName.name);
      tmp.pbag->setInt(KEY_ENCIPHER_GEN, encipherName.gen);
      TRY_OK(pint->addPersist(keyname, tmp), TEXT("addPersist"));
   }

   pint->addFinalize(keyname, tmp);
   response.setInt(RESP_GEN, keyname.gen);
   return true;
}


/*
 *--------------------------------------------------------------------------
 *
 * addMaster
 *
 *   add a new master key
 *
 * Results:
 *   true if ok
 *
 *--------------------------------------------------------------------------
 */
bool
KeyVault::addMaster(KeyName &keyname, PropertyBag &response)
{
   /*
    * generate the key
    */

   TempData tmp;
   DWORD sSecret = 256;
   MsgBinary secret(malloc(sSecret), sSecret);
   TRY_NTERR(BCryptGenRandom(0, (PUCHAR)secret.pBinary, sSecret, BCRYPT_USE_SYSTEM_PREFERRED_RNG));
   TRY_NTERR(
      BCryptGenerateSymmetricKey(pint->m_hAlgSym, &tmp.hkey, NULL, 0, secret.p(), sSecret, 0));

   /*
    * save the key
    */

   coresync sync(m_globalSync);

   CHECK_OK(pint->checkGeneration(keyname, tmp, true, response));
   tmp.pbag->set(KEY_NAME, keyname.name);
   tmp.pbag->setInt(KEY_GEN, keyname.gen);
   tmp.pbag->set(KEY_TYPE, TYPE_MASTER);
   tmp.pbag->set(KEY_SUBTYPE, SUBTYPE_SYMMETRIC);
   tmp.pbag->setInt(KEY_LENGTH, 256);
   tmp.pbag->setBool(KEY_PERSIST, true);

   TRY_OK(pint->addPersist(keyname, tmp), TEXT("addPersist"));
   pint->addFinalize(keyname, tmp);
   response.setInt(RESP_GEN, keyname.gen);
   return true;
}


/*
 *--------------------------------------------------------------------------
 *
 * clear
 *
 *   remove all keys
 *
 * Results:
 *   none
 *
 *--------------------------------------------------------------------------
 */
void
KeyVault::clear(PropertyBag &response)
{
   coresync sync(m_globalSync);

   DWORD countDeleted = 0;
   KEYVAULTMAP_ITER it;
   while ((it = pint->m_keys.begin()) != pint->m_keys.end()) {
      tstr name = it->first;
      PropertyBag resp;
      remove(name, resp, false);
      countDeleted += resp.getInt(RESP_COUNT, 0);
   }

   SYSMSG_FUNC(Debug, TEXT("KeyVault removed all keys, count=%u"), countDeleted);
   response.setInt(RESP_COUNT, countDeleted);
}


/*
 *--------------------------------------------------------------------------
 *
 * decipher
 *
 *   decrypt a blob passed as workitem binary data.
 *
 * Results:
 *   true if ok
 *
 *--------------------------------------------------------------------------
 */
bool
KeyVault::decipher(KeyName &keyname, PropertyBag &params, PropertyBag &response, WorkItem *workItem)
{
   MsgBinary bin;
   workItem->GetBinaryRequestData(bin, true);
   if (!bin.sBinary) {
      return true;
   }

   TempData tmp;
   if (keyname.isSuppliedKey()) {
      CHECK_OK(pint->setSuppliedKey(keyname, tmp, params, response));
   } else {
      CHECK_OK(pint->checkGeneration(keyname, tmp, false, response));
   }

   bool keyPair = isKeypair(tmp.pbag);
   BCRYPT_KEY_HANDLE hkey =
      tmp.hDupkey.get() ? tmp.hDupkey.get() : (BCRYPT_KEY_HANDLE)tmp.pbag->getInt64(KEY_HKEY, 0);

   PropertyBag transform = params.getBag(KEYPAR_TRANSFORM);
   TRY_OK(pint->decrypt(hkey, keyPair, bin, transform), TEXT("decrypt"));

   workItem->SetBinaryResponseData(bin, true);
   return true;
}


/*
 *--------------------------------------------------------------------------
 *
 * deleteRootKey
 *
 *   Deletes the root keys and calls clear to delete all existing keys.
 *
 * Results:
 *   true if ok
 *
 *--------------------------------------------------------------------------
 */
void
KeyVault::deleteRootKey(PropertyBag &params, PropertyBag &response)
{
   if (!params.contains(TEXT("KeyName"))) {
      clear(response);
   }

   MsgBinary bin;
   tstr lsaname = params.get(TEXT("KeyName"), LSAROOTNAME(pint->m_container));
   pint->putToCache(lsaname, bin);
}


/*
 *--------------------------------------------------------------------------
 *
 * deriveKey
 *
 *   Derive an operational key from master and context
 *
 * Results:
 *   true if ok
 *
 *--------------------------------------------------------------------------
 */
bool
KeyVault::deriveKey(KeyName &keyname, KeyName &keynameMaster, KeyName &keynameContext,
                    PropertyBag &response)
{
   TempData tmpMaster;
   CHECK_OK(pint->checkGeneration(keynameMaster, tmpMaster, false, response));
   CHECK_KEY_TYPE(tmpMaster.pbag, TYPE_MASTER);

   TempData tmpContext;
   CHECK_OK(pint->checkGeneration(keynameContext, tmpContext, false, response));
   CHECK_KEY_TYPE(tmpContext.pbag, TYPE_CONTEXT);

   BCRYPT_KEY_HANDLE hkey = tmpMaster.hDupkey.get()
                               ? tmpMaster.hDupkey.get()
                               : (BCRYPT_KEY_HANDLE)tmpMaster.pbag->getInt64(KEY_HKEY, 0);

   void *ctx;
   size_t size;
   tmpContext.pbag->getBinary(KEY_DATA, &ctx, &size);
   DWORD keyLength = tmpContext.pbag->getInt(KEY_LENGTH, 0);

   TempData temp;
   HashInfo *phash = pint->m_kvHash;
   TRY_NTERR(KV_CreateHash(phash, temp));
   MsgBinary bin;
   TRY_NTERR(KV_ExportKey(hkey, BCRYPT_KEY_DATA_BLOB, bin));
   BCRYPT_KEY_DATA_BLOB_HEADER *p = (BCRYPT_KEY_DATA_BLOB_HEADER *)bin.p();

   // We need to reverse the key data to be compatible with CAPI deriveKey
   MsgBinary binkey(p + 1, p->cbKeyData, false, false);
   binkey.reverse();

   TRY_NTERR(BCryptHashData(temp.hhash, binkey.p(), p->cbKeyData, 0));
   TRY_NTERR(BCryptHashData(temp.hhash, (PUCHAR)ctx, (DWORD)size, 0));
   tstr name = tstr::printf(TEXT("%s#%u"), keyname.name, keyname.gen);
   TRY_NTERR(BCryptHashData(temp.hhash, (PUCHAR)name.p(), (DWORD)name.s(), 0));
   TRY_NTERR(BCryptFinishHash(temp.hhash, (BYTE *)temp.phash, phash->hashSize, 0));
   p->cbKeyData = keyLength / 8;
   DWORD s = __min(p->cbKeyData, phash->hashSize);
   memcpy(p + 1, temp.phash, s);
   s += sizeof(BCRYPT_KEY_DATA_BLOB_HEADER);

   TempData tmp;
   TRY_NTERR(BCryptImportKey(pint->m_hAlgSym, NULL, BCRYPT_KEY_DATA_BLOB, &tmp.hkey, NULL, 0,
                             (PUCHAR)p, s, 0));

   /*
    * save the key
    */

   coresync sync(m_globalSync);

   CHECK_OK(pint->checkGeneration(keyname, tmp, true, response));

   tmp.pbag->set(KEY_NAME, keyname.name);
   tmp.pbag->setInt(KEY_GEN, keyname.gen);
   tmp.pbag->set(KEY_TYPE, TYPE_OPERATIONAL);
   tmp.pbag->set(KEY_SUBTYPE, SUBTYPE_SYMMETRIC);
   tmp.pbag->setInt(KEY_LENGTH, keyLength);
   tmp.pbag->setBool(KEY_PERSIST, false);
   tmp.pbag->set(TEXT("DerivedFromKey"),
                 tstr::printf(TEXT("%s#%u"), keynameMaster.name, keynameMaster.gen));
   tmp.pbag->set(TEXT("DerivedFromContext"),
                 tstr::printf(TEXT("%s#%u"), keynameContext.name, keynameContext.gen));

   pint->addFinalize(keyname, tmp);
   response.setInt(RESP_GEN, keyname.gen);
   return true;
}


/*
 *--------------------------------------------------------------------------
 *
 * encipher
 *
 *   Encrypt a blob passed as workitem binary data.
 *
 * Results:
 *   true if ok
 *
 *--------------------------------------------------------------------------
 */
bool
KeyVault::encipher(KeyName &keyname, PropertyBag &params, PropertyBag &response, WorkItem *workItem)
{
   MsgBinary bin;
   workItem->GetBinaryRequestData(bin, true);
   TRY_OK(bin.sBinary && bin.pBinary, TEXT("no binary data to encipher"));

   TempData tmp;
   if (keyname.isSuppliedKey()) {
      CHECK_OK(pint->setSuppliedKey(keyname, tmp, params, response));
   } else {
      CHECK_OK(pint->checkGeneration(keyname, tmp, false, response));
   }

   bool keyPair = isKeypair(tmp.pbag);
   BCRYPT_KEY_HANDLE hkey =
      tmp.hDupkey.get() ? tmp.hDupkey.get() : (BCRYPT_KEY_HANDLE)tmp.pbag->getInt64(KEY_HKEY, 0);

   DWORD extra = params.getInt(TEXT("extraBytes"), 0);
   if (extra) {
      bin.sBinary -= extra;
   }
   PropertyBag transform = params.getBag(KEYPAR_TRANSFORM);
   TRY_OK(pint->encrypt(hkey, keyPair, bin, transform, extra), TEXT("encrypt"));

   response.setBag(KEYPAR_TRANSFORM, transform, true);
   response.setInt(KEYPAR_GEN, keyname.gen);
   workItem->SetBinaryResponseData(bin, true);
   return true;
}


/*
 *--------------------------------------------------------------------------
 *
 * getContainerName
 *
 *   Exposes the container name.
 *
 * Results:
 *   a const reference to the container name.
 *
 *--------------------------------------------------------------------------
 */
const tstr &
KeyVault::getContainerName() const
{
   return pint->m_container;
}


/*
 *--------------------------------------------------------------------------
 *
 * getData
 *
 *   Get an application data entry with the specified name and generation
 *
 * Results:
 *   true if ok
 *
 *--------------------------------------------------------------------------
 */
bool
KeyVault::getData(KeyName &keyname, bool decipher, PropertyBag &response, WorkItem *workItem)
{
   TempData tmp;
   CHECK_OK(pint->checkGeneration(keyname, tmp, false, response));
   CHECK_KEY_TYPE(tmp.pbag, TYPE_APPDATA);

   MsgBinary bin;
   DWORD extra = decipher ? 0 : 256; // optimize realloc
   BYTE *p;
   size_t s;
   tmp.pbag->getBinary(KEY_DATA, (void **)&p, &s);
   bin.pBinary = malloc(s + extra);
   TRY_MEM(bin.pBinary);
   bin.sBinary = s;
   memcpy(bin.pBinary, p, s);

   if (!decipher) {

      KeyName keynameEncipher;
      keynameEncipher.name = tmp.pbag->get(KEY_ENCIPHER_NAME, TEXT(""));
      keynameEncipher.gen = tmp.pbag->getInt(KEY_ENCIPHER_GEN, 0);
      TempData tmpEncipher;
      CHECK_OK(pint->checkGeneration(keynameEncipher, tmpEncipher, false, response));

      BCRYPT_KEY_HANDLE hkey =
         tmp.hDupkey.get() ? tmp.hDupkey.get() : (BCRYPT_KEY_HANDLE)tmp.pbag->getInt64(KEY_HKEY, 0);

      PropertyBag transform = tmp.pbag->getBag(KEY_TRANSFORM);
      TRY_OK(pint->encrypt(hkey, isKeypair(tmpEncipher.pbag), bin, transform, extra),
             TEXT("encrypt"));
   }

   workItem->SetBinaryResponseData(bin, true);
   return true;
}


/*
 *--------------------------------------------------------------------------
 *
 * getKeyRaw
 *
 *   Get an operational key (not a keypair) and return
 *   the deciphered raw key data.
 *
 * Results:
 *   true if ok
 *
 *--------------------------------------------------------------------------
 */
bool
KeyVault::getKeyRaw(KeyName &keyname, PropertyBag &response, WorkItem *workItem)
{
   MsgBinary bin;
   TempData tmp;
   CHECK_OK(getKeyRawInt(keyname, response, bin, tmp, TYPE_OPERATIONAL));
   workItem->SetBinaryResponseData(bin, true);
   return true;
}


/*
 *--------------------------------------------------------------------------
 *
 * getKeyRawInt
 *
 *   Get a key of the specific type/subtype and get the raw key data
 *
 * Results:
 *   true if ok
 *
 *--------------------------------------------------------------------------
 */

bool
KeyVault::getKeyRawInt(KeyName &keyname, PropertyBag &response, MsgBinary &bin, TempData &tmp,
                       LPCTSTR type, LPCTSTR subtype, DWORD *pkeyLength)
{
   CHECK_OK(pint->checkGeneration(keyname, tmp, false, response));
   CHECK_KEY_TYPE(tmp.pbag, type);
   CHECK_KEY_SUBTYPE(tmp.pbag, subtype);
   LPCWSTR blobType = isKeypair(tmp.pbag) ? LEGACY_RSAPUBLIC_BLOB : BCRYPT_KEY_DATA_BLOB;
   BCRYPT_KEY_HANDLE hkey = (BCRYPT_KEY_HANDLE)tmp.pbag->getInt64(KEY_HKEY, 0);
   TRY_NTERR(KV_ExportKey(hkey, blobType, bin));
   if (pkeyLength) {
      *pkeyLength = tmp.pbag->getInt(KEY_LENGTH, 0);
   }
   return true;
}


/*
 *--------------------------------------------------------------------------
 *
 * getPublic
 *
 *   Get the public half of an operational keypair, returning the deciphered
 *   raw key data and optionally a signature as evidence of the private key.
 *
 * Results:
 *   true if ok
 *
 *--------------------------------------------------------------------------
 */
bool
KeyVault::getPublic(KeyName &keyname, SignDataInfo &sigData, PropertyBag &response,
                    WorkItem *workItem)
{
   MsgBinary bin;
   TempData tmp;
   CHECK_OK(getKeyRawInt(keyname, response, bin, tmp, TYPE_OPERATIONAL, SUBTYPE_ASYMMETRIC));
   if (sigData.dataLen) {
      // we have material for a signature - generate one
      TRY_OK(_createOrVerifySignature(bin, sigData, tmp, response, true), TEXT("getPublic"));
   }
   workItem->SetBinaryResponseData(bin, true);
   return true;
}


/*
 *--------------------------------------------------------------------------
 *
 * id
 *
 *   Returns a repeatable identity derived from the value of a specified
 *   kvBlob and the given salt.
 *
 * Results:
 *   true if ok
 *
 *--------------------------------------------------------------------------
 */
bool
KeyVault::id(KeyName &keyname, PropertyBag &params, PropertyBag &response, KeyVault *pKeyVault)
{
   // check params

   TempData tmp;
   CHECK_OK(pint->checkGeneration(keyname, tmp, false, response));
   void *pSalt;
   size_t sSalt;
   params.getBinary(KEYPAR_SALT, &pSalt, &sSalt);
   if (sSalt < 12) {
      response.setError(TEXT("id salt parameter is missing or too small"));
      return false;
   }

   // get key data

   tstr keytype = tmp.pbag->get(KEY_TYPE, TEXT(""));
   tstr keysubtype = tmp.pbag->get(KEY_SUBTYPE, TEXT(""));
   bool synthetic = tmp.pbag->getBool(KEY_SYNTHETIC, false);
   DWORD keyLength = tmp.pbag->getInt(KEY_LENGTH, 0);
   HashInfo *phash;

   // for hash/encrypt we use a mstr of the keyname#gen without namespace
   // (namespace should only be used to find the key),
   mstr name = KeyName(keyname.name, keyname.gen).fullName()._mstr();

   // we calculate id differently for the key types

   MsgBinary bin;
   if ((keytype == TYPE_MASTER) || ((keytype == TYPE_OPERATIONAL) && (!synthetic))) {
      /*
       * For backward compatibility we use CAPI for the first step for keys
       * first get key handle and hash alg
       */
      BCRYPT_KEY_HANDLE hkey = (BCRYPT_KEY_HANDLE)tmp.pbag->getInt64(KEY_HKEY, 0);
      bool hmac = false;
      ALG_ID hashAlg = CALG_SHA_512;
      if (keysubtype.compare(SUBTYPE_ASYMMETRIC) != 0) {
         hmac = true;
         if (keyLength < 192) {
            hashAlg = CALG_SHA_256;
         } else if (keyLength < 256) {
            hashAlg = CALG_SHA_384;
         }
      } else if (keyLength == 3072) {
         hashAlg = CALG_SHA_384;
      } else if (keyLength < 3072) {
         hashAlg = CALG_SHA_256;
      }
      MsgBinary in1((void *)name.p(), name.s(), false, false);
      MsgBinary in2((void *)pSalt, sSalt, false, false);

      coresync sync(m_globalSync);
      CHECK_OK(KV_CAPI_BuildHashForId(hmac, hkey, keyLength, hashAlg, in1, in2, bin));

   } else if (keytype == TYPE_CONTEXT) {

      // get hash alg
      DWORD hashIndex =
         ((keyLength < 192) ? HASH_256_HMAC : ((keyLength < 256) ? HASH_384_HMAC : HASH_512_HMAC));
      CHECK_OK(pint->getHashInfo(hashIndex, &phash));

      // for a context we encrypt the name+salt using encipherWithDerivedKey
      // with hard-wired parameters

      class dummy : public WorkItem {
      public:
      };
      WorkItem *worker = new dummy;
      size_t sbin = name.size() + sSalt;
      MsgBinary binIn(malloc(sbin), sbin);
      TRY_MEM(binIn.pBinary);
      memcpy(binIn.pBinary, name.p(), name.size());
      memcpy(binIn.p() + name.size(), pSalt, sSalt);
      worker->get_pint()->m_Message = new Message;

#   ifdef __LINUX__
      worker->get_pint()->m_Message->SetBinDataAndDropFromSrc(
         binIn, // contains bin data ref
         false, // should make its own copy
         true,  // should take ownership of the memory ref, if not copying
         true); // should drop ownership of memory ref from source
#   else
      worker->get_pint()->m_Message->bin = new MsgBinary(binIn.pBinary, binIn.sBinary, true);
#   endif

      PropertyBag bag, transform;
      bag.set(TEXT("hashAlgorithm"), wstr(phash->pad.pszAlgId)._tstr());
      bag.setInt64(TEXT("iterations"), ID_ITERATIONS);
      mstr fixSalt = mstr(ID_KEYSALT).base64Decode();
      bag.setBinary(TEXT("saltBytes"), fixSalt.p_upd(), fixSalt.s());
      transform.setBinary(TEXT("KP_IV"), pSalt, sSalt);
      bag.setBag(TEXT("transform"), transform);
      bag.setBool(TEXT("id"), true);
      bool ok = pKeyVault->m_cng.encipherWithDerivedKey(keyname, bag, response, worker, pKeyVault);
      response.remove(KEYPAR_GEN);
      response.remove(TEXT("salt"));
      MsgBinary *presp = worker->get_pint()->m_Binary;
      if ((!ok) || (!presp) || (!presp->sBinary)) {
         SYSMSG_FUNC(Error, TEXT("KeyVault id encipherWithDerivedKey FAILED"));
         worker->Release();
         return false;
      }
      bin.set(presp->pBinary, presp->sBinary);
      presp->drop();
      worker->Release();

   } else {
      response.setError(TEXT("bad keytype for id cmd"));
      return false;
   }

   // hash the result in bin using SHA1 and BASE64 to get id string
   CHECK_OK(pint->getHashInfo(HASH_1, &phash));
   TempData tmp2;
   TRY_NTERR(KV_CreateHash(phash, tmp2));
   TRY_NTERR(BCryptHashData(tmp2.hhash, bin.p(), bin.s(), 0));
   TRY_NTERR(BCryptFinishHash(tmp2.hhash, (BYTE *)tmp2.phash, phash->hashSize, 0));
   mstr str((LPCSTR)tmp2.phash, phash->hashSize);
   tstr id = str.base64Encode()._tstr();
   response.set(TEXT("id"), id);
   return true;
}


/*
 *--------------------------------------------------------------------------
 *
 * info
 *
 *   get the metadata for a key
 *
 * Results:
 *   true if ok
 *
 *--------------------------------------------------------------------------
 */
bool
KeyVault::info(KeyName &keyname, PropertyBag &response)
{
   TempData tmp;
   CHECK_OK(pint->checkGeneration(keyname, tmp, false, response));
   for (size_t i = 0, n = tmp.pbag->size(); i < n; ++i) {
      tstr name = tmp.pbag->getName(i);
      if ((name != KEY_HKEY) && (name != KEY_DATA) && (name != KEY_PUBLISHER_KEYDATA)) {

         if (tmp.pbag->isBag(i)) {
            PropertyBag bag = tmp.pbag->getBag(i);
            response.setBag(name, bag);
         } else {
            response.set(name, tmp.pbag->get(i));
         }
      }
   }
   return true;
}


/*
 *--------------------------------------------------------------------------
 *
 * rekey
 *
 *   build a new tree and switch when done
 *
 * Results:
 *   true if rekey was successfully performed
 *
 *--------------------------------------------------------------------------
 */
bool
KeyVault::rekey()
{
   /*
    * reset g_rekeyWaitEvent before setting g_isRekeying to avoid
    * wait timing windows (create error checked at wait usage)
    */
   if (!g_rekeyWaitEvent) {
      g_rekeyWaitEvent = CreateEvent(0, TRUE, FALSE, 0);
   } else {
      ResetEvent(g_rekeyWaitEvent);
   }
   g_isRekeying = true;

   bool ok = pint->rekey();

   g_isRekeying = false;
   if (g_rekeyWaitEvent) {
      SetEvent(g_rekeyWaitEvent);
   }
   return ok;
}


/*
 *--------------------------------------------------------------------------
 *
 * init
 *
 *   Initiate the KeyVaultImplementation data
 *
 * Results:
 *   true if ok
 *
 *--------------------------------------------------------------------------
 */
bool
KeyVault::init(bool useCAPI)
{
   return pint && pint->init(useCAPI);
}


/*
 *--------------------------------------------------------------------------
 *
 * keyExport
 *
 *   Create a multiBlob containing kvBlobs
 *
 * Results:
 *   true if ok
 *
 *--------------------------------------------------------------------------
 */

bool
KeyVault::keyExport(KeyName &keyname, tstr keytype, KeyName &keynameEncipher,
                    bool exportAllOperationalKeyGens, PropertyBag &transform, PropertyBag &response,
                    WorkItem *workItem)
{
   TempData tmpKey;
   CHECK_OK(pint->checkGeneration(keyname, tmpKey, false, response));

   TempData tmpEncipher(transform);
   CHECK_OK(pint->checkGeneration(keynameEncipher, tmpEncipher, false, response));

   /*
    * create the multiblob header
    */

   MsgBinary mblob;
   PropertyBag mblobBag;
   mblobBag.setInt(KEY_ENCIPHER_NAMESPACE, keynameEncipher.nameSpace);
   mblobBag.set(KEY_ENCIPHER_NAME, keynameEncipher.name);
   mblobBag.setInt(KEY_ENCIPHER_GEN, keynameEncipher.gen);
   mblobBag.setBag(KEY_TRANSFORM, transform);

   TempData tmpEphemeral;
   BCRYPT_KEY_HANDLE hkey = 0;
   bool keyPair = tmpEncipher.pbag->get(KEY_SUBTYPE, TEXT("")).comparei(SUBTYPE_ASYMMETRIC) == 0;
   if (keyPair) {
      /*
       * For a asymmetric key we need to create an ephemeral symmetric key to
       * encrypt the multiblob. The ephemeral key is exported and encrypted
       * by the public half of the keypair and added to the non-encrypted
       * part of the multiBlob.
       *
       * It was decided that backward compatibility with CAPI for new created
       * ephemeral keys is not needed since the current use cases does not
       * require it. We do support forward compatibility for ephemeral keys.
       */
      DWORD asymKeyLength = tmpEncipher.pbag->getInt(KEY_LENGTH, 0);
      DWORD sSecret = (asymKeyLength < 2048) ? 16 : ((asymKeyLength < 3072) ? 24 : 32);
      MsgBinary secret(malloc(sSecret), sSecret);
      TRY_MEM(secret.pBinary);
      TRY_NTERR(
         BCryptGenRandom(0, (PUCHAR)secret.pBinary, sSecret, BCRYPT_USE_SYSTEM_PREFERRED_RNG));
      TRY_NTERR(BCryptGenerateSymmetricKey(pint->m_hAlgSym, &hkey, NULL, 0, (PUCHAR)secret.pBinary,
                                           sSecret, 0));
      tmpEphemeral.hkey = hkey; // to destroy the key at method exit
      tmpEphemeral.pbag = new PropertyBag;
      tmpEphemeral.pbag->set(KEY_NAME, TEXT("EPHEMERAL-KEY"));
      TRACE_KEY_USAGE("HCRYPT", 1, KeyName(tmpEphemeral.pbag), "keyExport", hkey);

      MsgBinary bin;
      TRY_NTERR(KV_ExportKey(hkey, BCRYPT_KEY_DATA_BLOB, bin));
      BCRYPT_KEY_HANDLE keyEncipher = (BCRYPT_KEY_HANDLE)tmpEncipher.pbag->getInt64(KEY_HKEY, 0);
      PropertyBag bag;
      TRY_OK(pint->encrypt(keyEncipher, true, bin, bag), TEXT("encrypt"));
      mblobBag.setBinary(TEXT("EPHEMERAL-KEY"), bin.pBinary, bin.sBinary);
      mblobBag.setBool(TEXT("EPHEMERAL-NEWFORMAT"), true);
   } else {
      hkey = (BCRYPT_KEY_HANDLE)tmpEncipher.pbag->getInt64(KEY_HKEY, 0);
   }

   TRY_OK(pint->mblobHeaderSet(mblob, mblobBag), TEXT("mblobHeaderSet"));

   /*
    * Loop over generations
    */

   PropertyBag namebag;
   {
      coresync sync(m_globalSync);

      KEYVAULTMAP_ITER pos = pint->m_keys.find(&keyname.name);
      if (pos == pint->m_keys.end()) {
         response.setError(TEXT("The key has been removed"));
         return false;
      }

      // here we copy the namebag not to hold the lock while looping
      namebag = *pos->second;
   }

   TempData tmp;
   DWORD count = 0;
   for (size_t i = 0, n = namebag.size(); i < n; ++i) {
      if (namebag.isBag(i)) {

         KeyName keynameGen = keyname;
         keynameGen.gen = namebag.getName(i).toUInt();
         /*
          * For operational keys we only want the specified generation unless
          * exportAllOperationalKeyGens is set to true
          */
         if ((keytype == TYPE_OPERATIONAL) && (keynameGen.gen != keyname.gen) &&
             !exportAllOperationalKeyGens) {
            continue;
         }

         /*
          * Verify that the name#gen is loaded and is a wanted type
          */

         CHECK_OK(pint->checkGeneration(keynameGen, tmp, false, response));
         if (tmp.pbag->get(KEY_TYPE, TEXT("")) != keytype) {
            response.setError(TEXT("the key is of wrong type"));
            return false;
         }

         /*
          * Create the blob for export
          */

         tmp.hkey = (BCRYPT_KEY_HANDLE)tmp.pbag->getInt64(KEY_HKEY, 0);
         bool ok = pint->createKeyBlob(tmp, NULL, false);
         tmp.hkey = NULL;
         TRY_OK(ok, TEXT("createKeyBlob"));

         /*
          * Realloc the multiblob and copy the created blob
          */

         DWORD oldSize = (DWORD)mblob.sBinary;
         DWORD newSize = oldSize + tmp.sdata + sizeof(DWORD);
         BYTE *p = (BYTE *)realloc(mblob.pBinary, newSize);
         TRY_MEM(p);
         mblob.pBinary = p;
         mblob.sBinary = newSize;
         *((DWORD *)(p + oldSize)) = tmp.sdata;
         memcpy(p + oldSize + sizeof(DWORD), tmp.pdata, tmp.sdata);

         count++;
      }
   }


   multiBlobHeader *phead = (multiBlobHeader *)mblob.pBinary;
   phead->nblobs = count;

   TRY_OK(pint->encrypt(hkey, false, mblob, transform, 0, phead->totSize), TEXT("encrypt"));

   workItem->SetBinaryResponseData(mblob, true);
   response.setInt(RESP_COUNT, count);
   return true;
}


/*
 *--------------------------------------------------------------------------
 *
 * keyImport
 *
 *   Import a multiBlob containing kvBlobs
 *
 *   Note that we may have imported some blobs already when an error is
 *   detected, so the response "Imported" may be non-zero also when the
 *   return value is false.
 *
 * Results:
 *   true if ok
 *
 *--------------------------------------------------------------------------
 */
bool
KeyVault::keyImport(tstr keytype, PropertyBag &params, PropertyBag &response, WorkItem *workItem)
{
   MsgBinary bin;
   workItem->GetBinaryRequestData(bin);
   PropertyBag mblobBag;
   BYTE *pdata;
   DWORD sdata;
   TRY_OK(pint->mblobHeaderGet(bin, mblobBag, &pdata, &sdata, response), TEXT("mblobHeaderGet"));
   multiBlobHeader *phead = (multiBlobHeader *)bin.pBinary;

   /*
    * If params allowOverwrite is set and the key exists then all the
    * generations for the key is deleted upfront and the imported
    * generations are added.
    * Otherwise skip is set by loadBlob if a name#gen already exists
    */

   bool allowOverwrite = params.getBool(TEXT("allowOverwrite"), false);
   bool skip = false;  // set by loadblob if not allowOverwrite
   tstr tmpname;       // used internally by loadblob, init empty
   DWORD imported = 0; // count of generations

   for (DWORD i = 0; i < phead->nblobs; i++) {
      TRY_OK(sdata >= sizeof(DWORD), TEXT("KeyVault blob size field invalid"));
      DWORD sblob = *((DWORD *)pdata);
      pdata += sizeof(DWORD);
      sdata -= sizeof(DWORD);
      TRY_OK(sblob <= sdata, TEXT("KeyVault blob size invalid"));

      KeyName keyname;
      TempData tmp;
      tmp.sdata = sdata;
      tmp.pdata = pdata;

      bool ok = pint->loadBlob(keyname, tmp, NULL, &keytype, allowOverwrite ? NULL : &skip,
                               allowOverwrite ? &tmpname : NULL);
      tmp.pdata = NULL;
      TRY_OK(ok, TEXT("loadBlob"));

      if (!skip) {
         /*
          * save the key
          */
         coresync sync(m_globalSync);

         tmp.hkey = (BCRYPT_KEY_HANDLE)tmp.pbag->getInt64(KEY_HKEY, 0);
         TRY_OK(pint->addPersist(keyname, tmp), TEXT("addPersist"));
         tmp.hkey = 0;
         pint->addFinalize(keyname, tmp);
         response.setInt(RESP_COUNT, ++imported);
      }
      pdata += sblob;
      sdata -= sblob;
   }

   return true;
}


/*
 *--------------------------------------------------------------------------
 *
 * latest
 *
 *   Returns the highest generation number in the vault for the named kvBlob
 *
 * Results:
 *   true if ok
 *
 *--------------------------------------------------------------------------
 */
bool
KeyVault::latest(KeyName &keyname, PropertyBag &response)
{
   TempData tmp;
   CHECK_OK(pint->checkGeneration(keyname, tmp, false, response));
   response.setInt(RESP_GEN, keyname.gen);
   return true;
}


/*
 *--------------------------------------------------------------------------
 *
 * list
 *
 *   List existing keys for 'name' if specified else all.
 *   Key names are stored as [namespace##]name#gen in a comma separated
 *   string "names".
 *
 * Results:
 *   true if ok
 *
 *--------------------------------------------------------------------------
 */
bool
KeyVault::list(KeyName &keyname, PropertyBag &params, PropertyBag &response)
{
   DWORD count = 0, countCAPI = 0, countShared = 0;
   tstr names, namesCAPI, namesShared;
   bool ex = (!keyname.name.size()) ? params.getBool(TEXT("ex"), false) : false;

   if (keyname.nameSpace == namespace_none) {

      coresync sync(m_globalSync);

      KEYVAULTMAP_ITER it;
      if (keyname.name.size()) {
         it = pint->m_keys.find(&keyname.name);
      } else {
         it = pint->m_keys.begin();
      }

      for (; it != pint->m_keys.end(); ++it) {
         PropertyBag *namebag = it->second;

         for (size_t iname = 0, nname = namebag->size(); iname < nname; ++iname) {
            if (namebag->isBag(iname)) {
               tstr thename = it->first;

               thename << TEXT("#") << namebag->getName(iname);
               if ((ex) && (namebag->contains(KEY_IN_CAPI))) {
                  if (namesCAPI.size()) {
                     namesCAPI << TEXT(", ");
                  }
                  namesCAPI << thename;
                  countCAPI++;
               } else {
                  if (names.size()) {
                     names << TEXT(", ");
                  }
                  names << thename;
                  count++;
               }
            }
         }

         if (keyname.name.size()) {
            break;
         }
      }
   }

   if (g_Shared) {
      if ((!keyname.name.size()) || (keyname.nameSpace == namespace_shared)) {
         if (ex) {
            countShared += g_Shared->list(keyname, namesShared);
         } else {
            count += g_Shared->list(keyname, names);
         }
      }
   }

   if (ex) {
      if (names.size()) {
         response.set(TEXT("namesCNG"), names);
      }
      response.setInt(TEXT("countCNG"), count);
      if (namesShared.size()) {
         response.set(TEXT("namesShared"), namesShared);
      }
      response.setInt(TEXT("countShared"), countShared);
      if (namesCAPI.size()) {
         response.set(TEXT("namesInCapi"), namesCAPI);
      }
      response.setInt(TEXT("countInCapi"), countCAPI);
   } else {
      response.set(RESP_LIST, names);
      response.setInt(RESP_COUNT, count);
   }
   return true;
}


/*
 *--------------------------------------------------------------------------
 *
 * putData
 *
 *   add a data entry
 *
 * Results:
 *   true if ok
 *
 *--------------------------------------------------------------------------
 */
bool
KeyVault::putData(KeyName &keyname, KeyName &encipherName, PropertyBag &transform,
                  PropertyBag &response, WorkItem *workItem)
{
   MsgBinary bin;
   workItem->GetBinaryRequestData(bin, false);
   if (!bin.sBinary) {
      response.setError(TEXT("no data provided"));
      return false;
   }

   /*
    * save the data
    */

   coresync sync(m_globalSync);

   TempData tmp;
   CHECK_OK(pint->checkGeneration(keyname, tmp, true, response));
   TempData tmpEncipher;
   CHECK_OK(pint->checkGeneration(encipherName, tmpEncipher, false, response));
   CHECK_KEY_TYPE(tmpEncipher.pbag, TYPE_OPERATIONAL);

   tmp.pbag->set(KEY_NAME, keyname.name);
   tmp.pbag->setInt(KEY_GEN, keyname.gen);
   tmp.pbag->set(KEY_TYPE, TYPE_APPDATA);
   tmp.pbag->setBool(KEY_PERSIST, true);
   if (transform.size()) {
      tmp.pbag->setBag(KEY_TRANSFORM, transform);
   }
   tmp.pbag->setBinary(KEY_DATA, bin.pBinary, bin.sBinary);
   tmp.pbag->setInt(KEY_APPDATASIZE, (int)bin.sBinary);
   tmp.pbag->set(KEY_ENCIPHER_NAME, encipherName.name);
   tmp.pbag->setInt(KEY_ENCIPHER_GEN, encipherName.gen);

   TRY_OK(pint->addPersist(keyname, tmp), TEXT("addPersist"));
   tmp.pbag->setBinary(KEY_DATA, bin.pBinary, bin.sBinary);

   pint->addFinalize(keyname, tmp);
   response.setInt(RESP_GEN, keyname.gen);
   return true;
}


/*
 *--------------------------------------------------------------------------
 *
 * putKeyRaw
 *
 *   add an operational key (not a keypair) from supplied key material
 *   if persisting, encrypt with the named master key
 *
 * Results:
 *   true if ok
 *
 *--------------------------------------------------------------------------
 */
bool
KeyVault::putKeyRaw(KeyName &keyname, KeyName &encipherName, bool persist, PropertyBag &response,
                    WorkItem *workItem)
{
   MsgBinary bin;
   workItem->GetBinaryRequestData(bin, false);
   if (!bin.sBinary) {
      response.setError(TEXT("no data provided"));
      return false;
   }
   if (bin.sBinary < sizeof(BCRYPT_KEY_DATA_BLOB_HEADER)) {
      response.setError(TEXT("bad data size"));
      return false;
   }

   /*
    * Import the key into CNG
    * To support co-existence with capi KeyVault implementations
    * we need to make sure the data header is of type
    * BCRYPT_KEY_DATA_BLOB_HEADER
    */
   TempData tmp;
   BCRYPT_KEY_DATA_BLOB_HEADER *pblob = (BCRYPT_KEY_DATA_BLOB_HEADER *)bin.pBinary;
   pblob->dwMagic = BCRYPT_KEY_DATA_BLOB_MAGIC;
   pblob->dwVersion = BCRYPT_KEY_DATA_BLOB_VERSION1;
   TRY_NTERR(BCryptImportKey(pint->m_hAlgSym, NULL, BCRYPT_KEY_DATA_BLOB, &tmp.hkey, NULL, 0,
                             bin.p(), bin.s(), 0));
   TRACE_KEY_USAGE("HCRYPT", 1, keyname, "putKeyRaw", tmp.hkey);

   DWORD keyLength;
   DWORD size = sizeof(keyLength);
   TRY_NTERR(
      BCryptGetProperty(tmp.hkey, BCRYPT_KEY_LENGTH, (PUCHAR)&keyLength, sizeof(DWORD), &size, 0));

   /*
    * save the key
    */

   coresync sync(m_globalSync);

   CHECK_OK(pint->checkGeneration(keyname, tmp, true, response));
   TempData tmpEncipher;
   CHECK_OK(pint->checkGeneration(encipherName, tmpEncipher, false, response));
   CHECK_KEY_TYPE(tmpEncipher.pbag, TYPE_MASTER);

   tmp.pbag->set(KEY_NAME, keyname.name);
   tmp.pbag->setInt(KEY_GEN, keyname.gen);
   tmp.pbag->set(KEY_TYPE, TYPE_OPERATIONAL);
   tmp.pbag->set(KEY_SUBTYPE, SUBTYPE_SYMMETRIC);
   tmp.pbag->setInt(KEY_LENGTH, keyLength);
   tmp.pbag->setBool(KEY_PERSIST, persist);
   tmp.pbag->setInt64(KEY_HKEY, (__int64)tmp.hkey);

   if (persist) {
      tmp.pbag->set(KEY_ENCIPHER_NAME, encipherName.name);
      tmp.pbag->setInt(KEY_ENCIPHER_GEN, encipherName.gen);
      TRY_OK(pint->addPersist(keyname, tmp), TEXT("addPersist"));
   }

   pint->addFinalize(keyname, tmp);
   response.setInt(RESP_GEN, keyname.gen);
   return true;
}


/*
 *--------------------------------------------------------------------------
 *
 * putPublic
 *
 *   add a synthetic operational keypair based on a raw public key.
 *
 *   a signature is normally required; this will be verified using the
 *   public key. Set fTrusted if the key has been verified a different way.
 *
 * Results:
 *   true if ok
 *
 *--------------------------------------------------------------------------
 */
bool
KeyVault::putPublic(KeyName &keyname, bool fTrusted, SignDataInfo &sigData, PropertyBag &response,
                    WorkItem *workItem)
{
   MsgBinary bin;
   workItem->GetBinaryRequestData(bin, false);
   if (!bin.sBinary) {
      response.setError(TEXT("putPublic: no key data provided"));
      return false;
   }

   TempData tmp;
   CHECK_OK(pint->checkGeneration(keyname, tmp, true, response));
   tmp.pbag->set(KEY_NAME, keyname.name);
   tmp.pbag->setInt(KEY_GEN, keyname.gen);
   tmp.pbag->set(KEY_TYPE, TYPE_OPERATIONAL);
   tmp.pbag->set(KEY_SUBTYPE, SUBTYPE_ASYMMETRIC);
   tmp.pbag->setBool(KEY_PERSIST, false);
   tmp.pbag->setBool(KEY_SYNTHETIC, true);

   /*
    * Microsoft primitive provider is documented not to support
    * import of the LEGACY_RSAPUBLIC_BLOB format (it does export).
    * So here we need to manipulate buffers.
    */

   CAPI_PUBLIC_BLOB *pcapi = (CAPI_PUBLIC_BLOB *)bin.p();
   MsgBinary conv;
   DWORD sizeNeeded = sizeof(CNG_PUBLIC_BLOB) + sizeof(DWORD) + (pcapi->bitLen / 8);
   TRY_MEM(conv.resize(sizeNeeded));
   CNG_PUBLIC_BLOB *pcng = (CNG_PUBLIC_BLOB *)conv.p();
   pcng->Magic = BCRYPT_RSAPUBLIC_MAGIC;
   pcng->BitLength = pcapi->bitLen;
   pcng->cbPublicExp = 0;
   BYTE *pf = ((BYTE *)&pcapi->pubExp) + 3;
   BYTE *pt = (BYTE *)(pcng + 1);
   bool first = false;
   for (DWORD i = 0; i < sizeof(DWORD); i++, pf--) {
      if (*pf || first) {
         first = true;
         *pt++ = *pf;
         pcng->cbPublicExp++;
      }
   }
   pcng->cbModulus = pcng->BitLength / 8;
   pcng->cbPrime1 = 0;
   pcng->cbPrime2 = 0;
   memcpy(pt, pcapi + 1, pcng->cbModulus);
   MsgBinary binTmp(pt, pcng->cbModulus, false, false);
   binTmp.reverse();

   /*
    * Now we should be able to import the key as a BCRYPT_RSAPUBLIC_BLOB
    */

   DWORD size = sizeof(CNG_PUBLIC_BLOB) + pcng->cbPublicExp + pcng->cbModulus;
   TRY_NTERR(BCryptImportKeyPair(pint->m_hAlgAsym, 0, BCRYPT_RSAPUBLIC_BLOB, &tmp.hkey, conv.p(),
                                 size, 0));

   TRACE_KEY_USAGE("HCRYPT", 1, keyname, "putPublic", tmp.hkey);
   tmp.pbag->setInt64(KEY_HKEY, (__int64)tmp.hkey);

   DWORD keyLength;
   size = sizeof(keyLength);
   TRY_NTERR(BCryptGetProperty(tmp.hkey, BCRYPT_KEY_STRENGTH, (PUCHAR)&keyLength, size, &size, 0));
   tmp.pbag->setInt(KEY_LENGTH, keyLength);

   /*
    * Verify the provided signature
    */
   if (!fTrusted) {
      TRY_OK(_createOrVerifySignature(bin, sigData, tmp, response), TEXT("putPublic"));
   }

   /*
    * Save the key
    */

   tmp.hkey = 0;
   pint->addFinalize(keyname, tmp);
   response.setInt(RESP_GEN, keyname.gen);
   return true;
}


/*
 *--------------------------------------------------------------------------
 *
 * remove
 *
 *   remove all generations for a named key
 *
 * Results:
 *   true if ok
 *
 *--------------------------------------------------------------------------
 */
bool
KeyVault::remove(tstr &name, PropertyBag &response, bool fCheckDependents, DWORD retainLatestKeys)
{
   coresync sync(m_globalSync);

   KEYVAULTMAP_ITER it = pint->m_keys.find(&name);
   if (it == pint->m_keys.end()) {
      response.setError(TEXT("key name does not exist"));
      return false;
   }

   KeyName thename;
   thename.name = name;
   tstr *namename = it->first;
   PropertyBag *namebag = it->second;
   bool inCapi = namebag->getBool(KEY_IN_CAPI, false);
   vtstr toDeleteFromKeyMap;

   /*
    * First loop over all generations to check for dependents,
    * skip capi keys not imported
    */

   size_t iname;
   size_t nname = namebag->size();
   if (!inCapi && fCheckDependents) {
      for (iname = 0; iname < nname; ++iname) {
         if (namebag->isBag(iname)) {
            thename.gen = namebag->getName(iname).toUInt();
            TempData tmp;
            tmp.pbag = namebag->getBagPtr(iname);
            bool checkDep = false;
            if (pint->verifyLoaded(thename, namebag, tmp)) {
               tstr keyType = tmp.pbag->get(KEY_TYPE, TEXT(""));
               if ((keyType.comparei(TYPE_MASTER) == 0) ||
                   (keyType.comparei(TYPE_OPERATIONAL) == 0)) {
                  checkDep = true;
               }
            }
            if (checkDep) {
               tstr lastDep;
               for (it = pint->m_keys.begin(); it != pint->m_keys.end(); ++it) {
                  PropertyBag *bag = it->second;
                  KeyName keyname;
                  keyname.name = it->first;
                  for (size_t i = 0, n = bag->size(); i < n; ++i) {

                     if (bag->isBag(i)) {
                        keyname.gen = bag->getName(i).toUInt();
                        TempData tmp2;
                        tmp2.pbag = bag->getBagPtr(i);
                        if (pint->verifyLoaded(keyname, namebag, tmp2)) {
                           tstr encipherName = tmp2.pbag->get(KEY_ENCIPHER_NAME, TEXT(""));
                           if ((encipherName.size()) && (name.comparei(encipherName) == 0)) {
                              tstr err = TEXT("has dependent (");
                              err << keyname.name << TEXT("#") << keyname.gen << TEXT(")");
                              response.setError(err);
                              return false;
                           }
                        }
                     }
                  }
               }
            }
         }
      }
   }

   DWORD totalKeys = 0;
   for (iname = 0; iname < nname; ++iname) {
      if (namebag->isBag(iname)) {
         totalKeys++;
      }
   }
   DWORD keepFromGen = 0;
   DWORD keysToKeep = 0;
   if (!inCapi && totalKeys && retainLatestKeys) {
      keysToKeep = min(totalKeys, retainLatestKeys);
      PropertyBag genBag;
      latest(thename, genBag);
      DWORD highGen = genBag.getInt(RESP_GEN);
      keepFromGen = 1 + (highGen - keysToKeep);
   }

   /*
    * Now loop over all generations to delete them.
    * For capi keys not imported we still count generations and report
    * them as deleted (we logically delete them from capi).
    */

   DWORD countDeleted = 0;
   for (iname = 0; iname < nname; ++iname) {
      if (namebag->isBag(iname)) {
         DWORD gen = namebag->getName(iname).toUInt();
         if (keepFromGen && gen >= keepFromGen) {
            SYSMSG_FUNC(Debug, TEXT("... skipping %u"), gen);
            continue;
         }
         TempData tmp;
         tmp.pbag = namebag->getBagPtr(iname);
         if (fCheckDependents) {
            thename.gen = gen;
            SYSMSG_FUNC(Debug, TEXT("KeyVault remove key, name=%s#%u, type=%s %s"), thename.name,
                        thename.gen, tmp.pbag->get(KEY_TYPE, TEXT("")),
                        tmp.pbag->get(KEY_SUBTYPE, TEXT("")));
         }
         tmp.hkey = (BCRYPT_KEY_HANDLE)tmp.pbag->getInt64(KEY_HKEY, 0);
         toDeleteFromKeyMap.push_back(namebag->getName(iname));
         countDeleted++;
      }
   }

   SYSMSG_FUNC(Debug, TEXT("KeyVault remove key %s"), name);
   if (!inCapi) {
      if (keepFromGen) {
         vtstr toDelete;
         cedar::windows::unique_regkey uniqueNamedKey;
         LONG openErr = RegOpenKey(pint->m_hRegKey, name, uniqueNamedKey.put());
         if (openErr == ERROR_SUCCESS) {
            static const DWORD MAX_NAME_SIZE = 1024;
            SYSMSG_FUNC(Debug, TEXT("... retaining %d"), keysToKeep);
            TCHAR buf[MAX_NAME_SIZE];
            DWORD sbuf = MAX_NAME_SIZE * sizeof(TCHAR);
            DWORD regType, index = 0;
            while (RegEnumValue(uniqueNamedKey.get(), index++, buf, &sbuf, 0, &regType, 0, 0) ==
                   ERROR_SUCCESS) {
               if (regType == REG_BINARY) {
                  tstr genstr(buf);
                  if (genstr.isNum() && genstr.toUInt() < keepFromGen) {
                     toDelete.push_back(genstr);
                  }
               }
               sbuf = MAX_NAME_SIZE * sizeof(TCHAR);
            }

            vtstrit delname;
            DWORD count = 0;
            for (delname = toDelete.begin(); delname != toDelete.end(); delname++) {
               LONG delValueError = RegDeleteValue(uniqueNamedKey.get(), *delname);
               if (delValueError == ERROR_SUCCESS) {
                  count++;
               } else {
                  SYSMSG_FUNC(Error, TEXT("KeyVault registry delete value error = %s"),
                              tstr::formatError(delValueError));
               }
            }
            if (count) {
               SYSMSG_FUNC(Debug, TEXT("Removed %d generations"), count);
            }
         }
      } else {
         LONG err = RegDeleteKey(pint->m_hRegKey, name);
         if ((err != ERROR_SUCCESS) && (err != 2) /*2=Key does not exist*/) {
            SYSMSG_FUNC(Error, TEXT("KeyVault registry delete error = %s"), tstr::formatError(err));
         }
      }
   }
   if (!inCapi && keepFromGen) {
      // Delete entries from keys map (m_keys)
      vtstrit bagname;
      for (bagname = toDeleteFromKeyMap.begin(); bagname != toDeleteFromKeyMap.end(); bagname++) {
         namebag->remove(bagname->c_str(), true);
      }
   } else {
      pint->m_keys.erase(&name);
      delete namename;
      namebag->Release();

      /*
       * if the key is coming from capi we need to mark the registry not
       * to pick the key up again when we restart wsnm, two cases here:
       * - not imported from capi, the inCapi flag is set
       * - imported from capi and now deleted in cng, check the capi keys
       */
      if (inCapi || pint->m_keysCapi.getBool(name, false)) {
         pint->removeLogicalFromCAPI(name);
      }
   }
   response.setInt(RESP_COUNT, countDeleted);
   return true;
}


/*
 *--------------------------------------------------------------------------
 *
 * signHash
 *
 * Results:
 *   true if ok
 *
 *--------------------------------------------------------------------------
 */
bool
KeyVault::signHash(KeyName &keyname, SignDataInfo &sigData, PropertyBag &response)
{
   HashInfo *phash;
   CHECK_OK(pint->getHashInfo(sigData.algName, &phash));
   TempData tmp;
   CHECK_OK(pint->checkGeneration(keyname, tmp, false, response));

   MsgBinary in(sigData.dataBuffer, sigData.dataLen, false, false), out;
   TRY_NTERR(KV_SignHash(phash, tmp, out, &in));

   // NOTE: the reverseBytes flag switched meaning when converting to CNG
   if (!sigData.reverseBytes) {
      out.reverse();
   }
   response.setBinary(KEYPAR_BINDATA_SIGNED, out.p(), out.s());
   return true;
}


/*
 *--------------------------------------------------------------------------
 *
 * signPKCS10
 *
 *   signs a pkcs10 packed blob
 *
 * Results:
 *   true if ok
 *
 *--------------------------------------------------------------------------
 */
bool
KeyVault::signPKCS10(KeyName &keyname, SignDataInfo &sigData, PropertyBag &response)
{
   HashInfo *phash;
   CHECK_OK(pint->getHashInfo(sigData.algName, &phash));
   TempData tmp;
   CHECK_OK(pint->checkGeneration(keyname, tmp, false, response));
   BCRYPT_KEY_HANDLE hkey = (BCRYPT_KEY_HANDLE)tmp.pbag->getInt64(KEY_HKEY, 0);

   CERT_BLOB pkcs10Req = {(DWORD)sigData.dataLen, (PBYTE)sigData.dataBuffer};
   Pkcs10Data pkcs10Data;

   if (!PKCS10Util::SignPKCS10(hkey, sigData.algName._mstr(), &pkcs10Req, pkcs10Data, response)) {
      SYSMSG_FUNC(Error, TEXT("SignPKCS10 failed, %s"), response.getErrorText());
      return false;
   }

   if (sigData.reverseBytes) {
      pkcs10Data.Reverse();
   }

   response.setBinary(KEYPAR_BINDATA_SIGNED, pkcs10Data.GetMemRef(), pkcs10Data.GetSize());
   return true;
}


/*
 *--------------------------------------------------------------------------
 *
 * generateSignature
 *
 * Results:
 *   true if ok
 *
 *--------------------------------------------------------------------------
 */
bool
KeyVault::generateSignature(KeyName &keyname, SignDataInfo &sigData, PropertyBag &response)
{
   TempData tmp;
   CHECK_OK(pint->checkGeneration(keyname, tmp, false, response));

   MsgBinary dummy;
   return _createOrVerifySignature(dummy, sigData, tmp, response, true);
}


/*
 *--------------------------------------------------------------------------
 *
 * verifySignature
 *
 * Results:
 *   true if ok
 *
 *--------------------------------------------------------------------------
 */
bool
KeyVault::verifySignature(KeyName &keyname, SignDataInfo &sigData, PropertyBag &response)
{
   TempData tmp;
   CHECK_OK(pint->checkGeneration(keyname, tmp, false, response));

   MsgBinary dummy;
   return _createOrVerifySignature(dummy, sigData, tmp, response);
}


/*
 *--------------------------------------------------------------------------
 *
 * _createOrVerifySignature
 *
 *   If fCreate is true, generate a signature by encrypting the supplied data
 *   with the private half of the provided keypair.
 *
 *   Verify the supplied or generated signature by decrypting it with the public
 *   half of the provided keypair. If the resulting data matches what was passed
 *   in, then the public and private halves are self-consistent, and the public
 *   half is shown to be associated with the signature.
 *
 * Results:
 *   true if the signature is successfully generated and/or verified
 *
 *--------------------------------------------------------------------------
 */
bool
KeyVault::_createOrVerifySignature(MsgBinary &keyBlob, SignDataInfo &sigData, TempData &tmpData,
                                   PropertyBag &response, bool fCreate)
{
   HashInfo *phash;
   CHECK_OK(pint->getHashInfo(sigData.algName, &phash));
   TRY_NTERR(KV_CreateHash(phash, tmpData));

   // hash the naming data first...
   TRY_NTERR(BCryptHashData(tmpData.hhash, (PUCHAR)sigData.dataBuffer, (DWORD)sigData.dataLen, 0));

   // ...and then the public key blob (if set)
   if (keyBlob.p()) {
      TRY_NTERR(BCryptHashData(tmpData.hhash, keyBlob.p(), keyBlob.s(), 0));
   }

   // and finish the hash
   TRY_NTERR(BCryptFinishHash(tmpData.hhash, (BYTE *)tmpData.phash, phash->hashSize, 0));

   MsgBinary out;
   if (fCreate) {
      // generate a signature
      TRY_NTERR(KV_SignHash(phash, tmpData, out));
      sigData.sigBuffer = out.p();
      sigData.sigLen = out.s();
   }

   // verify the signature to ensure the two keypair halves match
   BCRYPT_KEY_HANDLE hkey = (BCRYPT_KEY_HANDLE)tmpData.pbag->getInt64(KEY_HKEY);
   DWORD flags = isKeypair(tmpData.pbag) ? BCRYPT_PAD_PKCS1 : 0;
   TRY_NTERR(BCryptVerifySignature(hkey, &phash->pad, tmpData.phash, phash->hashSize,
                                   (PUCHAR)sigData.sigBuffer, (DWORD)sigData.sigLen, flags));
   if (fCreate) {
      response.setBinary(KEYPAR_BINDATA_SIGNED, out.p(), out.s());
   }
   return true;
}


#endif // WIN32
