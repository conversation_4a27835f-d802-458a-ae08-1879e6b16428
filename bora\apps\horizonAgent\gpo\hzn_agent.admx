﻿<?xml version="1.0" encoding="utf-8"?>
<policyDefinitions xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" revision="1.0" schemaVersion="1.0" xmlns="http://www.microsoft.com/GroupPolicy/PolicyDefinitions">

   <policyNamespaces>
      <target prefix="Hzn_agent" namespace="Horizon.Policies.Hzn_agent" />
         <using prefix="Hzn_agent_base" namespace="Horizon.Policies.Hzn_agent_base" />
   </policyNamespaces>
   <supersededAdm fileName="hzn_agent.adm" />
   <resources minRequiredRevision="1.0" fallbackCulture="en-us" />

   <supportedOn>
      <definitions>
         <definition name="SUPPORTED_Windows10_1607_VM" displayName="$(string.SUPPORTED_Windows10_1607_VM)"/>
      </definitions>
   </supportedOn>

   <categories>

      <category name="Agent_Configuration" displayName="$(string.Agent_Configuration)" key="Software\Policies\Omnissa\Horizon\Agent\Configuration">
         <parentCategory ref="Hzn_agent_base:Hzn_Agent_Config" />
      </category>

      <category name="Collaboration" displayName="$(string.Collaboration)" key="Software\Policies\Omnissa\Horizon\Agent\Configuration">
         <parentCategory ref="Hzn_agent_base:Hzn_Agent_Config" />
      </category>

      <category name="Agent_Security" displayName="$(string.Agent_Security)" key="Software\Policies\Omnissa\Horizon\Security">
         <parentCategory ref="Hzn_agent_base:Hzn_Agent_Config" />
      </category>

      <category name="Unity_Touch_Hosted_Apps" displayName="$(string.Unity_Touch_Hosted_Apps)" key="Software\Policies\Omnissa\Horizon\Unity">
         <parentCategory ref="Hzn_agent_base:Hzn_Agent_Config" />
      </category>

      <category name="Smartcard_Redirection" displayName="$(string.Smartcard_Redirection)" key="Software\Policies\Omnissa\Horizon\Agent\SmartcardRedirection">
         <parentCategory ref="Hzn_agent_base:Hzn_Agent_Config" />
      </category>

      <category name="Local_Reader_Access" displayName="$(string.Local_Reader_Access)" key="Software\Policies\Omnissa\Horizon\Agent\SmartcardRedirection">
         <parentCategory ref="Smartcard_Redirection" />
      </category>

      <category name="True_SSO_Configuration" displayName="$(string.True_SSO_Configuration)" key="Software\Policies\Omnissa\Horizon\Agent\CertSSO">
         <parentCategory ref="Hzn_agent_base:Hzn_Agent_Config" />
      </category>

      <category name="Whfb_Certificate_Redirection" displayName="$(string.Whfb_Certificate_Redirection)" key="Software\Policies\Omnissa\Horizon\Whfb">
         <parentCategory ref="Hzn_agent_base:Hzn_Agent_Config" />
      </category>

      <category name="Unity_Touch_Hosted_Apps_2" displayName="$(string.Unity_Touch_Hosted_Apps)" key="Software\Policies\Omnissa\Horizon\Unity">
         <parentCategory ref="Agent_Security" />
      </category>

      <category name="View_USB_Configuration" displayName="$(string.View_USB_Configuration)" key="Software\Policies\Omnissa\Horizon\Agent\USB">
         <parentCategory ref="Hzn_agent_base:Hzn_Agent_Config" />
      </category>

      <category name="Client_Downloadable_only_settings" displayName="$(string.Client_Downloadable_only_settings)" key="Software\Policies\Omnissa\Horizon\Agent\USB">
         <parentCategory ref="View_USB_Configuration" />
      </category>

      <category name="Horizon_HTML5_FEATURES" displayName="$(string.Horizon_HTML5_FEATURES)" key="Software\Policies\Omnissa\Horizon\HTML5SERVER">
         <parentCategory ref="Hzn_agent_base:Hzn_Agent_Config" />
      </category>

      <category name="Horizon_HTML5MMR" displayName="$(string.Horizon_HTML5MMR)" key="Software\Policies\Omnissa\Horizon\Html5mmr">
         <parentCategory ref="Horizon_HTML5_FEATURES" />
      </category>

      <category name="Horizon_GEO_REDIR" displayName="$(string.Horizon_GEO_REDIR)" key="Software\Policies\Omnissa\Horizon\GEOREDIR">
         <parentCategory ref="Horizon_HTML5_FEATURES" />
      </category>

      <category name="Horizon_BROWSER_REDIR" displayName="$(string.Horizon_BROWSER_REDIR)" key="Software\Policies\Omnissa\Horizon\BrowserRedir">
         <parentCategory ref="Horizon_HTML5_FEATURES" />
      </category>

      <category name="Horizon_WebRTC_REDIR_FEATURES" displayName="$(string.Horizon_WebRTC_REDIR_FEATURES)" key="Software\Policies\Omnissa\Horizon\WebRTCRedir">
         <parentCategory ref="Horizon_HTML5_FEATURES" />
      </category>

      <category name="Horizon_WebRTC_SDK_WEBAPP" displayName="$(string.Horizon_WebRTC_SDK_WEBAPP)" key="Software\Policies\Omnissa\Horizon\WebRTCRedirSDKWebApp">
         <parentCategory ref="Horizon_HTML5_FEATURES" />
      </category>

      <category name="Horizon_AppTap_Config" displayName="$(string.Horizon_AppTap_Config)" key="Software\Policies\Omnissa\Horizon\AppTap\Config">
         <parentCategory ref="Hzn_agent_base:Hzn_Agent_Config" />
      </category>

      <category name="Horizon_WaterMark" displayName="$(string.Horizon_WaterMark)" key="Software\Policies\Omnissa\Horizon\Agent\Watermark">
         <parentCategory ref="Hzn_agent_base:Hzn_Agent_Config" />
      </category>

      <category name="CustomBackgroundImages" displayName="$(string.CustomBackgroundImages)" key="Software\Policies\Omnissa\Horizon\WebRTCRedir\MSTeamsCustomImagesBackground">
         <parentCategory ref="Horizon_WebRTC_REDIR_FEATURES" />
      </category>

   </categories>

   <policies>
      <policy
         name="Recursive_Domain_Enumeration" class="Machine" displayName="$(string.Recursive_Domain_Enumeration)" explainText="$(string.Recursive_Domain_Enumeration_Desc)"
         key="Software\Policies\Omnissa\Horizon" valueName="RecursiveDomainEnum">
            <parentCategory ref="Hzn_agent_base:Hzn_Agent_Config" />
            <supportedOn ref="SupportedOn" />
               <enabledValue>
                  <string>true</string>
               </enabledValue>
               <disabledValue>
                  <string>false</string>
               </disabledValue>
      </policy>

      <policy
         name="Force_MMR_to_use_overlay" class="Machine" displayName="$(string.Force_MMR_to_use_overlay)" explainText="$(string.Force_MMR_to_use_overlay_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\Configuration" valueName="ForceMMRToUseSoftwareOverlay">
            <parentCategory ref="Agent_Configuration" />
            <supportedOn ref="SupportedOn" />
               <enabledValue>
                  <string>true</string>
               </enabledValue>
               <disabledValue>
                  <string>false</string>
               </disabledValue>
      </policy>

      <policy
         name="Enable_multi_media_acceleration" class="Machine" displayName="$(string.Enable_multi_media_acceleration)" explainText="$(string.Enable_multi_media_acceleration_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\Configuration" valueName="EnableMMR">
            <parentCategory ref="Agent_Configuration" />
            <supportedOn ref="SupportedOn" />
               <enabledValue>
                  <string>true</string>
               </enabledValue>
               <disabledValue>
                  <string>false</string>
               </disabledValue>
      </policy>

      <policy
         name="AllowDirectRDP" class="Machine" displayName="$(string.AllowDirectRDP)" explainText="$(string.AllowDirectRDP_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\Configuration" valueName="AllowDirectRDP">
            <parentCategory ref="Agent_Configuration" />
            <supportedOn ref="SupportedOn" />
               <enabledValue>
                  <string>true</string>
               </enabledValue>
               <disabledValue>
                  <string>false</string>
               </disabledValue>
      </policy>

      <policy
         name="AllowSingleSignon" class="Machine" displayName="$(string.AllowSingleSignon)" explainText="$(string.AllowSingleSignon_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\Configuration" valueName="AllowSingleSignon">
            <parentCategory ref="Agent_Configuration" />
            <supportedOn ref="SupportedOn" />
               <enabledValue>
                  <string>true</string>
               </enabledValue>
               <disabledValue>
                  <string>false</string>
               </disabledValue>
      </policy>

      <policy
         name="AutoPopulateLogonUI" class="Machine" displayName="$(string.AutoPopulateLogonUI)" explainText="$(string.AutoPopulateLogonUI_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\Configuration" valueName="AutoPopulateLogonUI">
            <parentCategory ref="Agent_Configuration" />
            <supportedOn ref="SupportedOn" />
               <enabledValue>
                  <string>true</string>
               </enabledValue>
               <disabledValue>
                  <string>false</string>
               </disabledValue>
      </policy>

      <policy
         name="ConnectionTicketTimeout" class="Machine" displayName="$(string.ConnectionTicketTimeout)" explainText="$(string.ConnectionTicketTimeout_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\Configuration" presentation="$(presentation.ConnectionTicketTimeout)">
            <parentCategory ref="Agent_Configuration" />
            <supportedOn ref="SupportedOn" />
               <elements>
                  <decimal id="ConnectionTicketTimeout_DB" key="Software\Policies\Omnissa\Horizon\Agent\Configuration" valueName="ConnectionTicketTimeout" required="true" maxValue="900" minValue="30" />
               </elements>
      </policy>

      <policy
         name="CredentialFilterExceptions" class="Machine" displayName="$(string.CredentialFilterExceptions)" explainText="$(string.CredentialFilterExceptions_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\Configuration" presentation="$(presentation.CredentialFilterExceptions)">
            <parentCategory ref="Agent_Configuration" />
            <supportedOn ref="SupportedOn" />
               <elements>
                  <text id="CredentialFilterExceptions_TB" key="Software\Policies\Omnissa\Horizon\Agent\Configuration" valueName="CredentialFilterExceptions" />
               </elements>
      </policy>

      <policy
         name="RDPVcBridgeUnsupportedClients" class="Machine" displayName="$(string.RDPVcBridgeUnsupportedClients)" explainText="$(string.RDPVcBridgeUnsupportedClients_Desc)"
         key="Software\Policies\Omnissa\Horizon\RdpVcBridge" presentation="$(presentation.RDPVcBridgeUnsupportedClients)">
            <parentCategory ref="Agent_Configuration" />
            <supportedOn ref="SupportedOn" />
               <elements>
                  <text id="RDPVcBridgeUnsupportedClients_TB" key="Software\Policies\Omnissa\Horizon\RdpVcBridge" valueName="unsupported_clients" />
               </elements>
      </policy>

      <policy
         name="Disable_Time_Zone_sync" class="Machine" displayName="$(string.Disable_Time_Zone_sync)" explainText="$(string.Disable_Time_Zone_sync_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\Configuration" valueName="DisableTimeZoneSynchronization">
            <parentCategory ref="Agent_Configuration" />
            <supportedOn ref="SupportedOn" />
               <enabledValue>
                  <string>true</string>
               </enabledValue>
               <disabledValue>
                  <string>false</string>
               </disabledValue>
      </policy>

      <policy
         name="Keep_Time_Zone_sync_disconnect" class="Machine" displayName="$(string.Keep_Time_Zone_sync_disconnect)" explainText="$(string.Keep_Time_Zone_sync_disconnect_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\Configuration" valueName="KeepTimeZoneSyncDisconnect">
            <parentCategory ref="Agent_Configuration" />
            <supportedOn ref="SupportedOn" />
               <enabledValue>
                  <string>true</string>
               </enabledValue>
               <disabledValue>
                  <string>false</string>
               </disabledValue>
      </policy>

      <policy
         name="Keep_Time_Zone_sync_logoff" class="Machine" displayName="$(string.Keep_Time_Zone_sync_logoff)" explainText="$(string.Keep_Time_Zone_sync_logoff_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\Configuration" valueName="KeepTimeZoneSyncLogoff">
            <parentCategory ref="Agent_Configuration" />
            <supportedOn ref="SupportedOn" />
               <enabledValue>
                  <string>true</string>
               </enabledValue>
               <disabledValue>
                  <string>false</string>
               </disabledValue>
      </policy>

      <policy
         name="Toggle_Display_Settings_Control" class="Machine" displayName="$(string.Toggle_Display_Settings_Control)" explainText="$(string.Toggle_Display_Settings_Control_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\Configuration" valueName="ToggleDisplaySettingsControl">
            <parentCategory ref="Agent_Configuration" />
            <supportedOn ref="SupportedOn" />
               <enabledValue>
                  <string>true</string>
               </enabledValue>
               <disabledValue>
                  <string>false</string>
               </disabledValue>
      </policy>

      <policy
         name="DpiSync" class="Machine" displayName="$(string.DpiSync)" explainText="$(string.DpiSync_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\Configuration" valueName="DpiSync">
            <parentCategory ref="Agent_Configuration" />
            <supportedOn ref="SupportedOn" />
               <enabledValue>
                  <decimal value="1" />
               </enabledValue>
               <disabledValue>
                  <decimal value="0" />
               </disabledValue>
      </policy>

      <policy
         name="DpiSyncPerMonitor" class="Machine" displayName="$(string.DpiSyncPerMonitor)" explainText="$(string.DpiSyncPerMonitor_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\Configuration" valueName="DpiSyncPerMonitor">
         <parentCategory ref="Agent_Configuration" />
         <supportedOn ref="SupportedOn" />
         <enabledValue>
            <decimal value="1" />
         </enabledValue>
         <disabledValue>
            <decimal value="0" />
         </disabledValue>
      </policy>

      <policy
         name="DisplayScaling" class="Machine" displayName="$(string.DisplayScaling)" explainText="$(string.DisplayScaling_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\Configuration" valueName="DisplayScaling">
         <parentCategory ref="Agent_Configuration" />
         <supportedOn ref="SupportedOn" />
         <enabledValue>
            <decimal value="1" />
         </enabledValue>
         <disabledValue>
            <decimal value="0" />
         </disabledValue>
      </policy>

      <policy
         name="UnAuthenticatedAccessEnabled" class="Machine" displayName="$(string.UnAuthenticatedAccessEnabled)" explainText="$(string.UnAuthenticatedAccessEnabled_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\Configuration" valueName="UnAuthenticatedAccessEnabled">
            <parentCategory ref="Agent_Configuration" />
            <supportedOn ref="SupportedOn" />
               <enabledValue>
                  <decimal value="1" />
               </enabledValue>
               <disabledValue>
                  <decimal value="0" />
               </disabledValue>
      </policy>

      <policy
         name="RdsAadAuthEnabled" class="Machine" displayName="$(string.RdsAadAuthEnabled)" explainText="$(string.RdsAadAuthEnabled_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\Configuration" valueName="RdsAadAuthEnabled">
         <parentCategory ref="Agent_Configuration" />
         <supportedOn ref="SupportedOn" />
         <enabledValue>
            <string>true</string>
         </enabledValue>
         <disabledValue>
            <string>false</string>
         </disabledValue>
      </policy>

      <policy
         name="CommandsToRunOnConnect" class="Machine" displayName="$(string.CommandsToRunOnConnect)" explainText="$(string.CommandsToRunOnConnect_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\Configuration\CommandsToRunOnConnect" presentation="$(presentation.CommandsToRunOnConnect)">
            <parentCategory ref="Agent_Configuration" />
            <supportedOn ref="SupportedOn" />
               <elements>
                  <list id="CommandsToRunOnConnect_list" key="Software\Policies\Omnissa\Horizon\Agent\Configuration\CommandsToRunOnConnect" valuePrefix="Command" />
               </elements>
      </policy>

      <policy
         name="CommandsToRunOnReconnect" class="Machine" displayName="$(string.CommandsToRunOnReconnect)" explainText="$(string.CommandsToRunOnReconnect_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\Configuration\CommandsToRunOnReconnect" presentation="$(presentation.CommandsToRunOnReconnect)">
            <parentCategory ref="Agent_Configuration" />
            <supportedOn ref="SupportedOn" />
               <elements>
                  <list id="CommandsToRunOnReconnect_list" key="Software\Policies\Omnissa\Horizon\Agent\Configuration\CommandsToRunOnReconnect" valuePrefix="Command" />
               </elements>
      </policy>

      <policy
         name="CommandsToRunOnDisconnect" class="Machine" displayName="$(string.CommandsToRunOnDisconnect)" explainText="$(string.CommandsToRunOnDisconnect_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\Configuration\CommandsToRunOnDisconnect" presentation="$(presentation.CommandsToRunOnDisconnect)">
            <parentCategory ref="Agent_Configuration" />
            <supportedOn ref="SupportedOn" />
               <elements>
                  <list id="CommandsToRunOnDisconnect_list" key="Software\Policies\Omnissa\Horizon\Agent\Configuration\CommandsToRunOnDisconnect" valuePrefix="Command" />
               </elements>
      </policy>

      <policy
         name="ShowDiskActivityIcon" class="Machine" displayName="$(string.ShowDiskActivityIcon)" explainText="$(string.ShowDiskActivityIcon_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\Configuration" valueName="ShowDiskActivityIcon">
            <parentCategory ref="Agent_Configuration" />
            <supportedOn ref="SupportedOn" />
               <enabledValue>
                  <string>true</string>
               </enabledValue>
               <disabledValue>
                  <string>false</string>
               </disabledValue>
      </policy>

      <policy
         name="SSO_retry_timeout" class="Machine" displayName="$(string.SSO_retry_timeout)" explainText="$(string.SSO_retry_timeout_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\Configuration" presentation="$(presentation.SSO_retry_timeout)">
            <parentCategory ref="Agent_Configuration" />
            <supportedOn ref="SupportedOn" />
               <elements>
                  <decimal id="SSO_retry_timeout_DB" key="Software\Policies\Omnissa\Horizon\Agent\Configuration" valueName="SSORetryTimeout" maxValue="30000" minValue="0" />
               </elements>
      </policy>

      <policy
         name="Win10PhysicalAgentAudioOption" class="Machine" displayName="$(string.Win10PhysicalAgentAudioOption)" explainText="$(string.Win10PhysicalAgentAudioOption_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\Configuration" presentation="$(presentation.Win10PhysicalAgentAudioOption)">
            <parentCategory ref="Agent_Configuration" />
            <supportedOn ref="SupportedOn" />
               <elements>
                  <enum id="Win10PhysicalAgentAudioOption_DDL" valueName="AudioOption" key="Software\Policies\Omnissa\Horizon\Agent\Configuration">
                     <item displayName="$(string.UseClientAudioDevice)">
                        <value>
                           <decimal value="0"/>
                        </value>
                     </item>
                     <item displayName="$(string.UsePhysicalMachineAudioDevice)">
                        <value>
                           <decimal value="1"/>
                        </value>
                     </item>
                  </enum>
               </elements>
      </policy>

      <policy
         name="WaitForLogoff" class="Machine" displayName="$(string.WaitForLogoff)" explainText="$(string.WaitForLogoff_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\Configuration" presentation="$(presentation.WaitForLogoff)">
            <parentCategory ref="Agent_Configuration" />
            <supportedOn ref="SupportedOn" />
               <elements>
                  <decimal id="WaitForLogoff_DB" key="Software\Policies\Omnissa\Horizon\Agent\Configuration" valueName="WaitForLogoff" maxValue="60" minValue="0" />
               </elements>
      </policy>

      <policy
         name="Accept_SSL_encr_framework_channel" class="Machine" displayName="$(string.Accept_SSL_encr_framework_channel)" explainText="$(string.Accept_SSL_encr_framework_channel_Desc)"
         key="Software\Policies\Omnissa\Horizon\Security" presentation="$(presentation.Accept_SSL_encr_framework_channel)">
            <parentCategory ref="Agent_Security" />
            <supportedOn ref="SupportedOn" />
               <elements>
                  <enum id="Accept_SSL_encr_framework_channel_DDL" valueName="AcceptTicketSSLAuth" key="Software\Policies\Omnissa\Horizon\Security" required="true">
                     <item displayName="$(string.Enable)">
                        <value>
                           <string>1</string>
                        </value>
                     </item>
                     <item displayName="$(string.Disable)">
                        <value>
                           <string>0</string>
                        </value>
                     </item>
                     <item displayName="$(string.Enforce)">
                        <value>
                           <string>3</string>
                        </value>
                     </item>
                  </enum>
               </elements>
      </policy>

      <policy
         name="VDI_idle_time_till_disconnect" class="Machine" displayName="$(string.VDI_idle_time_till_disconnect)" explainText="$(string.VDI_idle_time_till_disconnect_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\Configuration" valueName="VDIIdleTimeout" presentation="$(presentation.VDI_idle_time_till_disconnect)">
            <parentCategory ref="Agent_Configuration" />
            <supportedOn ref="SupportedOn" />
              <disabledList>
                <item key="Software\Policies\Omnissa\Horizon\Agent\Configuration" valueName="VDIIdleTimeout">
                  <value>
                    <string>0</string>
                  </value>
                </item>
              </disabledList>
              <elements>
                <enum id="VDI_idle_time_till_disconnect_minutes" valueName="VDIIdleTimeout">
                  <item displayName="$(string.TIME_NEVER)">
                    <value>
                      <decimal value="0" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_1MIN)">
                    <value>
                      <decimal value="1" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_5MIN)">
                    <value>
                      <decimal value="5" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_10MIN)">
                    <value>
                      <decimal value="10" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_15MIN)">
                    <value>
                      <decimal value="15" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_30MIN)">
                    <value>
                      <decimal value="30" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_1HR)">
                    <value>
                      <decimal value="60" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_2HR)">
                    <value>
                      <decimal value="120" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_3HR)">
                    <value>
                      <decimal value="180" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_6HR)">
                    <value>
                      <decimal value="360" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_8HR)">
                    <value>
                      <decimal value="480" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_10HR)">
                    <value>
                      <decimal value="600" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_12HR)">
                    <value>
                      <decimal value="720" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_18HR)">
                    <value>
                      <decimal value="1080" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_1D)">
                    <value>
                      <decimal value="1440" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_2D)">
                    <value>
                      <decimal value="2880" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_3D)">
                    <value>
                      <decimal value="4320" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_4D)">
                    <value>
                      <decimal value="5760" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_5D)">
                    <value>
                      <decimal value="7200" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_1W)">
                    <value>
                      <decimal value="10080" />
                    </value>
                  </item>
                </enum>
              </elements>
      </policy>

      <policy
         name="VDI_disconnect_time_till_logoff" class="Machine" displayName="$(string.VDI_disconnect_time_till_logoff)" explainText="$(string.VDI_disconnect_time_till_logoff_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\Configuration" valueName="VDIDisconnectTimeout" presentation="$(presentation.VDI_disconnect_time_till_logoff)">
            <parentCategory ref="Agent_Configuration" />
            <supportedOn ref="SupportedOn" />
              <disabledList>
                <item key="Software\Policies\Omnissa\Horizon\Agent\Configuration" valueName="VDIDisconnectTimeout">
                  <value>
                    <string>0</string>
                  </value>
                </item>
              </disabledList>
              <elements>
                <enum id="VDI_disconnect_time_till_logoff_minutes" valueName="VDIDisconnectTimeout">
                <item displayName="$(string.TIME_NEVER)">
                    <value>
                      <decimal value="0" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_IMMEDIATELY)">
                    <value>
                        <decimal value="4294967295" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_1MIN)">
                    <value>
                      <decimal value="1" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_5MIN)">
                    <value>
                      <decimal value="5" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_10MIN)">
                    <value>
                      <decimal value="10" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_15MIN)">
                    <value>
                      <decimal value="15" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_30MIN)">
                    <value>
                      <decimal value="30" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_1HR)">
                    <value>
                      <decimal value="60" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_2HR)">
                    <value>
                      <decimal value="120" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_3HR)">
                    <value>
                      <decimal value="180" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_6HR)">
                    <value>
                      <decimal value="360" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_8HR)">
                    <value>
                      <decimal value="480" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_10HR)">
                    <value>
                      <decimal value="600" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_12HR)">
                    <value>
                      <decimal value="720" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_18HR)">
                    <value>
                      <decimal value="1080" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_1D)">
                    <value>
                      <decimal value="1440" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_2D)">
                    <value>
                      <decimal value="2880" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_3D)">
                    <value>
                      <decimal value="4320" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_4D)">
                    <value>
                      <decimal value="5760" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_5D)">
                    <value>
                      <decimal value="7200" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_1W)">
                    <value>
                      <decimal value="10080" />
                    </value>
                  </item>
                </enum>
              </elements>
      </policy>

      <policy
         name="RDS_idle_time_till_disconnect" class="Machine" displayName="$(string.RDS_idle_time_till_disconnect)" explainText="$(string.RDS_idle_time_till_disconnect_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\Configuration" valueName="RDSIdleTimeout" presentation="$(presentation.RDS_idle_time_till_disconnect)">
            <parentCategory ref="Agent_Configuration" />
            <supportedOn ref="SupportedOn" />
              <disabledList>
                <item key="Software\Policies\Omnissa\Horizon\Agent\Configuration" valueName="RDSIdleTimeout">
                  <value>
                    <string>0</string>
                  </value>
                </item>
              </disabledList>
              <elements>
                <enum id="RDS_idle_time_till_disconnect_milliseconds" valueName="RDSIdleTimeout">
                  <item displayName="$(string.TIME_NEVER)">
                    <value>
                      <decimal value="0" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_1MIN)">
                    <value>
                      <decimal value="60000" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_5MIN)">
                    <value>
                      <decimal value="300000" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_10MIN)">
                    <value>
                      <decimal value="600000" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_15MIN)">
                    <value>
                      <decimal value="900000" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_30MIN)">
                    <value>
                      <decimal value="1800000" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_1HR)">
                    <value>
                      <decimal value="3600000" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_2HR)">
                    <value>
                      <decimal value="7200000" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_3HR)">
                    <value>
                      <decimal value="10800000" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_6HR)">
                    <value>
                      <decimal value="21600000" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_8HR)">
                    <value>
                      <decimal value="28800000" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_10HR)">
                    <value>
                      <decimal value="36000000" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_12HR)">
                    <value>
                      <decimal value="43200000" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_18HR)">
                    <value>
                      <decimal value="64800000" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_1D)">
                    <value>
                      <decimal value="86400000" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_2D)">
                    <value>
                      <decimal value="172800000" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_3D)">
                    <value>
                      <decimal value="259200000" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_4D)">
                    <value>
                      <decimal value="345600000" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_5D)">
                    <value>
                      <decimal value="432000000" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_1W)">
                    <value>
                      <decimal value="604800000" />
                    </value>
                  </item>
                </enum>
              </elements>
      </policy>

      <policy
         name="RDS_disconnect_time_till_logoff" class="Machine" displayName="$(string.RDS_disconnect_time_till_logoff)" explainText="$(string.RDS_disconnect_time_till_logoff_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\Configuration" valueName="RDSDisconnectTimeout" presentation="$(presentation.RDS_disconnect_time_till_logoff)">
            <parentCategory ref="Agent_Configuration" />
            <supportedOn ref="SupportedOn" />
              <disabledList>
                <item key="Software\Policies\Omnissa\Horizon\Agent\Configuration" valueName="RDSDisconnectTimeout">
                  <value>
                    <string>0</string>
                  </value>
                </item>
              </disabledList>
              <elements>
                <enum id="RDS_disconnect_time_till_logoff_milliseconds" valueName="RDSDisconnectTimeout">
                  <item displayName="$(string.TIME_NEVER)">
                    <value>
                      <decimal value="0" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_1MIN)">
                    <value>
                      <decimal value="60000" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_5MIN)">
                    <value>
                      <decimal value="300000" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_10MIN)">
                    <value>
                      <decimal value="600000" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_15MIN)">
                    <value>
                      <decimal value="900000" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_30MIN)">
                    <value>
                      <decimal value="1800000" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_1HR)">
                    <value>
                      <decimal value="3600000" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_2HR)">
                    <value>
                      <decimal value="7200000" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_3HR)">
                    <value>
                      <decimal value="10800000" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_6HR)">
                    <value>
                      <decimal value="21600000" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_8HR)">
                    <value>
                      <decimal value="28800000" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_10HR)">
                    <value>
                      <decimal value="36000000" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_12HR)">
                    <value>
                      <decimal value="43200000" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_18HR)">
                    <value>
                      <decimal value="64800000" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_1D)">
                    <value>
                      <decimal value="86400000" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_2D)">
                    <value>
                      <decimal value="172800000" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_3D)">
                    <value>
                      <decimal value="259200000" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_4D)">
                    <value>
                      <decimal value="345600000" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_5D)">
                    <value>
                      <decimal value="432000000" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_1W)">
                    <value>
                      <decimal value="604800000" />
                    </value>
                  </item>
                </enum>
              </elements>
      </policy>

      <policy
         name="RDS_active_time_till_disconnect" class="Machine" displayName="$(string.RDS_active_time_till_disconnect)" explainText="$(string.RDS_active_time_till_disconnect_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\Configuration" valueName="RDSConnectTimeout" presentation="$(presentation.RDS_active_time_till_disconnect)">
            <parentCategory ref="Agent_Configuration" />
            <supportedOn ref="SupportedOn" />
              <disabledList>
                <item key="Software\Policies\Omnissa\Horizon\Agent\Configuration" valueName="RDSConnectTimeout">
                  <value>
                    <string>0</string>
                  </value>
                </item>
              </disabledList>
              <elements>
                <enum id="RDS_active_time_till_disconnect_milliseconds" valueName="RDSConnectTimeout">
                  <item displayName="$(string.TIME_NEVER)">
                    <value>
                      <decimal value="0" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_1MIN)">
                    <value>
                      <decimal value="60000" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_5MIN)">
                    <value>
                      <decimal value="300000" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_10MIN)">
                    <value>
                      <decimal value="600000" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_15MIN)">
                    <value>
                      <decimal value="900000" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_30MIN)">
                    <value>
                      <decimal value="1800000" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_1HR)">
                    <value>
                      <decimal value="3600000" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_2HR)">
                    <value>
                      <decimal value="7200000" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_3HR)">
                    <value>
                      <decimal value="10800000" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_6HR)">
                    <value>
                      <decimal value="21600000" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_8HR)">
                    <value>
                      <decimal value="28800000" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_10HR)">
                    <value>
                      <decimal value="36000000" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_12HR)">
                    <value>
                      <decimal value="43200000" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_18HR)">
                    <value>
                      <decimal value="64800000" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_1D)">
                    <value>
                      <decimal value="86400000" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_2D)">
                    <value>
                      <decimal value="172800000" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_3D)">
                    <value>
                      <decimal value="259200000" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_4D)">
                    <value>
                      <decimal value="345600000" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_5D)">
                    <value>
                      <decimal value="432000000" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_1W)">
                    <value>
                      <decimal value="604800000" />
                    </value>
                  </item>
                </enum>
              </elements>
      </policy>

      <policy
         name="RDS_end_session_time_limit" class="Machine" displayName="$(string.RDS_end_session_time_limit)" explainText="$(string.RDS_end_session_time_limit_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\Configuration" valueName="RDSEndSession">
            <parentCategory ref="Agent_Configuration" />
            <supportedOn ref="SupportedOn" />
               <enabledValue>
                  <decimal value="1" />
               </enabledValue>
               <disabledValue>
                  <decimal value="0" />
               </disabledValue>
      </policy>

      <policy
         name="RDS_threshold_connecting_session" class="Machine" displayName="$(string.RDS_threshold_connecting_session)" explainText="$(string.RDS_threshold_connecting_session_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\Configuration\Session Threshold Settings" presentation="$(presentation.RDS_threshold_connecting_session)">
            <parentCategory ref="Agent_Configuration" />
            <supportedOn ref="SupportedOn" />
               <elements>
                  <decimal id="RDS_threshold_connecting_session_DB" key="Software\Policies\Omnissa\Horizon\Agent\Configuration\Session Threshold Settings" valueName="Connecting Session Threshold" minValue="0" maxValue="150" />
               </elements>
      </policy>

      <policy
         name="RDS_threshold_load_index" class="Machine" displayName="$(string.RDS_threshold_load_index)" explainText="$(string.RDS_threshold_load_index_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\Configuration\Session Threshold Settings" presentation="$(presentation.RDS_threshold_load_index)">
            <parentCategory ref="Agent_Configuration" />
            <supportedOn ref="SupportedOn" />
               <elements>
                  <decimal id="RDS_threshold_load_index_DB" key="Software\Policies\Omnissa\Horizon\Agent\Configuration\Session Threshold Settings" valueName="Load Index Threshold" minValue="0" maxValue="100" />
               </elements>
      </policy>

      <policy
         name="Prewarm_disconnect_time_till_logoff" class="Machine" displayName="$(string.Prewarm_disconnect_time_till_logoff)" explainText="$(string.Prewarm_disconnect_time_till_logoff_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\Configuration" valueName="PrewarmDisconnectTimeout" presentation="$(presentation.Prewarm_disconnect_time_till_logoff)">
            <parentCategory ref="Agent_Configuration" />
            <supportedOn ref="SupportedOn" />
              <disabledList>
                <item key="Software\Policies\Omnissa\Horizon\Agent\Configuration" valueName="PrewarmDisconnectTimeout">
                  <value>
                    <string>30</string>
                  </value>
                </item>
              </disabledList>
              <elements>
                <enum id="Prewarm_disconnect_time_till_logoff_minutes" valueName="PrewarmDisconnectTimeout">
                  <item displayName="$(string.TIME_NEVER)">
                    <value>
                      <decimal value="0" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_1MIN)">
                    <value>
                      <decimal value="1" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_5MIN)">
                    <value>
                      <decimal value="5" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_10MIN)">
                    <value>
                      <decimal value="10" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_15MIN)">
                    <value>
                      <decimal value="15" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_30MIN)">
                    <value>
                      <decimal value="30" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_1HR)">
                    <value>
                      <decimal value="60" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_2HR)">
                    <value>
                      <decimal value="120" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_3HR)">
                    <value>
                      <decimal value="180" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_6HR)">
                    <value>
                      <decimal value="360" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_8HR)">
                    <value>
                      <decimal value="480" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_10HR)">
                    <value>
                      <decimal value="600" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_12HR)">
                    <value>
                      <decimal value="720" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_18HR)">
                    <value>
                      <decimal value="1080" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_1D)">
                    <value>
                      <decimal value="1440" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_2D)">
                    <value>
                      <decimal value="2880" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_3D)">
                    <value>
                      <decimal value="4320" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_4D)">
                    <value>
                      <decimal value="5760" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_5D)">
                    <value>
                      <decimal value="7200" />
                    </value>
                  </item>
                  <item displayName="$(string.TIME_1W)">
                    <value>
                      <decimal value="10080" />
                    </value>
                  </item>
                </enum>
              </elements>
      </policy>

      <policy
         name="Allow_smartcard_local_access" class="Machine" displayName="$(string.Allow_smartcard_local_access)" explainText="$(string.Allow_smartcard_local_access_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\SmartcardRedirection" valueName="AllowLocalAccess">
            <parentCategory ref="Local_Reader_Access" />
            <supportedOn ref="SupportedOn" />
               <enabledValue>
                  <string>true</string>
               </enabledValue>
               <disabledValue>
                  <string>false</string>
               </disabledValue>
      </policy>

      <policy
         name="Local_Reader_Name" class="Machine" displayName="$(string.Local_Reader_Name)" explainText="$(string.Local_Reader_Name_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\SmartcardRedirection" presentation="$(presentation.Local_Reader_Name)">
            <parentCategory ref="Local_Reader_Access" />
            <supportedOn ref="SupportedOn" />
               <elements>
                  <text id="Local_Reader_Name_TB" key="Software\Policies\Omnissa\Horizon\Agent\SmartcardRedirection" valueName="LocalReaderName" />
               </elements>
      </policy>

      <policy
         name="Require_an_inserted_smart_card" class="Machine" displayName="$(string.Require_an_inserted_smart_card)" explainText="$(string.Require_an_inserted_smart_card_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\SmartcardRedirection" valueName="EnableLocalAccessAtCardInsert">
            <parentCategory ref="Local_Reader_Access" />
            <supportedOn ref="SupportedOn" />
               <enabledValue>
                  <string>true</string>
               </enabledValue>
               <disabledValue>
                  <string>false</string>
               </disabledValue>
      </policy>

      <policy
         name="Disable_true_SSO" class="Machine" displayName="$(string.Disable_true_SSO)" explainText="$(string.Disable_true_SSO_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\CertSSO" valueName="DisableCertSSO">
            <parentCategory ref="True_SSO_Configuration" />
            <supportedOn ref="SupportedOn" />
               <enabledValue>
                  <string>true</string>
               </enabledValue>
               <disabledValue>
                  <string>false</string>
               </disabledValue>
      </policy>

      <policy
         name="Cert_wait_timeout" class="Machine" displayName="$(string.Cert_wait_timeout)" explainText="$(string.Cert_wait_timeout_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\CertSSO" presentation="$(presentation.Cert_wait_timeout)">
            <parentCategory ref="True_SSO_Configuration" />
            <supportedOn ref="SupportedOn" />
               <elements>
                  <decimal id="Cert_wait_timeout_DB" key="Software\Policies\Omnissa\Horizon\Agent\CertSSO" valueName="CertSSOCertificateTimeoutSec" maxValue="120" minValue="10" />
               </elements>
      </policy>

      <policy
         name="Min_key_size" class="Machine" displayName="$(string.Min_key_size)" explainText="$(string.Min_key_size_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\CertSSO" presentation="$(presentation.Min_key_size)">
            <parentCategory ref="True_SSO_Configuration" />
            <supportedOn ref="SupportedOn" />
               <elements>
                  <decimal id="Min_key_size_DB" key="Software\Policies\Omnissa\Horizon\Agent\CertSSO" valueName="MinKeySize" maxValue="8192" minValue="1024" />
               </elements>
      </policy>

      <policy
         name="All_key_sizes" class="Machine" displayName="$(string.All_key_sizes)" explainText="$(string.All_key_sizes_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\CertSSO" presentation="$(presentation.All_key_sizes)">
            <parentCategory ref="True_SSO_Configuration" />
            <supportedOn ref="SupportedOn" />
               <elements>
                  <text id="All_key_sizes_TB" key="Software\Policies\Omnissa\Horizon\Agent\CertSSO" valueName="KeySizes" />
               </elements>
      </policy>

      <policy
         name="Keys_to_precreate" class="Machine" displayName="$(string.Keys_to_precreate)" explainText="$(string.Keys_to_precreate_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\CertSSO" presentation="$(presentation.Keys_to_precreate)">
            <parentCategory ref="True_SSO_Configuration" />
            <supportedOn ref="SupportedOn" />
               <elements>
                  <decimal id="Keys_to_precreate_DB" key="Software\Policies\Omnissa\Horizon\Agent\CertSSO" valueName="KeyCount" maxValue="100" minValue="1" />
               </elements>
      </policy>

      <policy
         name="Cert_min_validity" class="Machine" displayName="$(string.Cert_min_validity)" explainText="$(string.Cert_min_validity_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\CertSSO" presentation="$(presentation.Cert_min_validity)">
            <parentCategory ref="True_SSO_Configuration" />
            <supportedOn ref="SupportedOn" />
               <elements>
                  <decimal id="Cert_min_validity_DB" key="Software\Policies\Omnissa\Horizon\Agent\CertSSO" valueName="ValidityPeriodAtReconnect" minValue="5" />
               </elements>
      </policy>

      <policy
         name="Whfb_Certificate_Allowed_Applications" class="Machine" displayName="$(string.Whfb_Certificate_Allowed_Applications)" explainText="$(string.Whfb_Certificate_Allowed_Applications_Desc)"
         key="Software\Policies\Omnissa\Horizon\Whfb" presentation="$(presentation.WhfbCertificateAllowedApplicationsList)">
            <parentCategory ref="Whfb_Certificate_Redirection" />
            <supportedOn ref="SupportedOn" />
               <elements>
                  <multiText id="Whfb_Certificate_Allowed_Applications_list_TB" key="Software\Policies\Omnissa\Horizon\Whfb" valueName="CertInterceptIncludeList" />
               </elements>
      </policy>

      <policy
         name="Enable_Unity_Touch" class="Machine" displayName="$(string.Enable_Unity_Touch)" explainText="$(string.Enable_Unity_Touch_Desc)"
         key="Software\Policies\Omnissa\Horizon\Unity" valueName="EnableUnityTouch"
         presentation="$(presentation.Enable_Unity_Touch)">
            <parentCategory ref="Unity_Touch_Hosted_Apps" />
            <supportedOn ref="SupportedOn" />
               <enabledValue>
                  <string>true</string>
               </enabledValue>
               <disabledValue>
                  <string>false</string>
               </disabledValue>
            <elements>
               <boolean id="Enabled_UWP_For_UnityTouch_CB"
                        key="Software\Policies\Omnissa\Horizon\Unity"
                        valueName="EnableUWPForUnityTouch">
                  <trueValue>
                     <string>true</string>
                  </trueValue>
                  <falseValue>
                     <string>false</string>
                  </falseValue>
               </boolean>
            </elements>
      </policy>

      <policy
         name="Unity_Filter" class="Machine" displayName="$(string.Unity_Filter)" explainText="$(string.Unity_Filter_Rules_Desc)"
         presentation="$(presentation.UnityFilterRules_Filter)" key="Software\Policies\Omnissa\Horizon\Unity\Filters">
         <parentCategory ref="Unity_Touch_Hosted_Apps" />
         <supportedOn ref="SupportedOn" />
         <elements>
            <list id="UnityFilterRules_FilterList" />
         </elements>
      </policy>

      <policy
         name="Enable_system_tray_redir" class="Machine" displayName="$(string.Enable_system_tray_redir)" explainText="$(string.Enable_system_tray_redir_Desc)"
         key="Software\Policies\Omnissa\Horizon\Unity" valueName="EnableSystemTrayRedirection">
            <parentCategory ref="Unity_Touch_Hosted_Apps" />
            <supportedOn ref="SupportedOn" />
               <enabledValue>
                  <string>true</string>
               </enabledValue>
               <disabledValue>
                  <string>false</string>
               </disabledValue>
      </policy>

      <policy
         name="Enable_user_prof_customization" class="Machine" displayName="$(string.Enable_user_prof_customization)" explainText="$(string.Enable_user_prof_customization_Desc)"
         key="Software\Policies\Omnissa\Horizon\Unity" valueName="CustomizeUserProfile">
            <parentCategory ref="Unity_Touch_Hosted_Apps" />
            <supportedOn ref="SupportedOn" />
               <enabledValue>
                  <string>true</string>
               </enabledValue>
               <disabledValue>
                  <string>false</string>
               </disabledValue>
      </policy>

      <policy
         name="AllowTinyOrOffscreenWindows" class="Machine" displayName="$(string.AllowTinyOrOffscreenWindows)" explainText="$(string.AllowTinyOrOffscreenWindows_Desc)"
         key="Software\Policies\Omnissa\Horizon\Unity" valueName="AllowTinyOrOffscreenWindows">
            <parentCategory ref="Unity_Touch_Hosted_Apps" />
            <supportedOn ref="SupportedOn" />
               <enabledValue>
                  <string>true</string>
               </enabledValue>
               <disabledValue>
                  <string>false</string>
               </disabledValue>
      </policy>

      <policy
         name="MinimalHookingModeEnabled" class="Machine" displayName="$(string.MinimalHookingModeEnabled)" explainText="$(string.MinimalHookingModeEnabled_Desc)"
         key="Software\Policies\Omnissa\Horizon\Unity" valueName="MinimalHookingModeEnabled">
            <parentCategory ref="Unity_Touch_Hosted_Apps" />
            <supportedOn ref="SupportedOn" />
               <enabledValue>
                  <string>true</string>
               </enabledValue>
               <disabledValue>
                  <string>false</string>
               </disabledValue>
      </policy>

      <policy
         name="LaunchAppWhenArgsAreDifferent" class="Machine" displayName="$(string.LaunchAppWhenArgsAreDifferent)" explainText="$(string.LaunchAppWhenArgsAreDifferent_Desc)"
         key="Software\Policies\Omnissa\Horizon\Unity" valueName="LaunchAppWhenArgsAreDifferent">
            <parentCategory ref="Unity_Touch_Hosted_Apps" />
            <supportedOn ref="SupportedOn" />
               <enabledValue>
                  <string>true</string>
               </enabledValue>
               <disabledValue>
                  <string>false</string>
               </disabledValue>
      </policy>

      <policy
         name="Exclude_Vid_Pid" class="Machine" displayName="$(string.Exclude_Vid_Pid)" explainText="$(string.Exclude_Vid_Pid_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\USB" presentation="$(presentation.Exclude_Vid_Pid)">
            <parentCategory ref="View_USB_Configuration" />
            <supportedOn ref="SupportedOn" />
               <elements>
                  <text id="Exclude_Vid_Pid_TB" key="Software\Policies\Omnissa\Horizon\Agent\USB" valueName="ExcludeVidPid" />
               </elements>
      </policy>

      <policy
         name="Include_Vid_Pid" class="Machine" displayName="$(string.Include_Vid_Pid)" explainText="$(string.Include_Vid_Pid_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\USB" presentation="$(presentation.Include_Vid_Pid)">
            <parentCategory ref="View_USB_Configuration" />
            <supportedOn ref="SupportedOn" />
               <elements>
                  <text id="Include_Vid_Pid_TB" key="Software\Policies\Omnissa\Horizon\Agent\USB" valueName="IncludeVidPid" />
               </elements>
      </policy>

      <policy
         name="Exclude_Vid_Pid_Rel" class="Machine" displayName="$(string.Exclude_Vid_Pid_Rel)" explainText="$(string.Exclude_Vid_Pid_Rel_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\USB" presentation="$(presentation.Exclude_Vid_Pid_Rel)">
            <parentCategory ref="View_USB_Configuration" />
            <supportedOn ref="SupportedOn" />
               <elements>
                  <text id="Exclude_Vid_Pid_Rel_TB" key="Software\Policies\Omnissa\Horizon\Agent\USB" valueName="ExcludeVidPidRel" />
               </elements>
      </policy>

      <policy
         name="Include_Vid_Pid_Rel" class="Machine" displayName="$(string.Include_Vid_Pid_Rel)" explainText="$(string.Include_Vid_Pid_Rel_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\USB" presentation="$(presentation.Include_Vid_Pid_Rel)">
            <parentCategory ref="View_USB_Configuration" />
            <supportedOn ref="SupportedOn" />
               <elements>
                  <text id="Include_Vid_Pid_Rel_TB" key="Software\Policies\Omnissa\Horizon\Agent\USB" valueName="IncludeVidPidRel" />
               </elements>
      </policy>

      <policy
         name="Exclude_device_family" class="Machine" displayName="$(string.Exclude_device_family)" explainText="$(string.Exclude_device_family_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\USB" presentation="$(presentation.Exclude_device_family)">
            <parentCategory ref="View_USB_Configuration" />
            <supportedOn ref="SupportedOn" />
               <elements>
                  <text id="Exclude_device_family_TB" key="Software\Policies\Omnissa\Horizon\Agent\USB" valueName="ExcludeFamily" />
               </elements>
      </policy>

      <policy
         name="Include_device_family" class="Machine" displayName="$(string.Include_device_family)" explainText="$(string.Include_device_family_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\USB" presentation="$(presentation.Include_device_family)">
            <parentCategory ref="View_USB_Configuration" />
            <supportedOn ref="SupportedOn" />
               <elements>
                  <text id="Include_device_family_TB" key="Software\Policies\Omnissa\Horizon\Agent\USB" valueName="IncludeFamily" />
               </elements>
      </policy>

      <policy
         name="Exclude_all" class="Machine" displayName="$(string.Exclude_all)" explainText="$(string.Exclude_all_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\USB" valueName="ExcludeAllDevices">
            <parentCategory ref="View_USB_Configuration" />
            <supportedOn ref="SupportedOn" />
               <enabledValue>
                  <string>true</string>
               </enabledValue>
               <disabledValue>
                  <string>false</string>
               </disabledValue>
      </policy>

      <policy
         name="HidOpt_Include_Vid_Pid" class="Machine" displayName="$(string.HidOpt_Include_Vid_Pid)" explainText="$(string.HidOpt_Include_Vid_Pid_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\USB" presentation="$(presentation.HidOpt_Include_Vid_Pid)">
            <parentCategory ref="View_USB_Configuration" />
            <supportedOn ref="SupportedOn" />
               <elements>
                  <text id="HidOpt_Include_Vid_Pid_TB" key="Software\Policies\Omnissa\Horizon\Agent\USB" valueName="HidOptIncludeVidPid" />
               </elements>
      </policy>

      <policy
         name="Exclude_Auto_Vid_Pid" class="Machine" displayName="$(string.Exclude_Auto_Vid_Pid)" explainText="$(string.Exclude_Auto_Vid_Pid_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\USB" presentation="$(presentation.Exclude_Auto_Vid_Pid)">
            <parentCategory ref="Client_Downloadable_only_settings" />
            <supportedOn ref="SupportedOn" />
               <elements>
                  <text id="Exclude_Auto_Vid_Pid_TB" key="Software\Policies\Omnissa\Horizon\Agent\USB" valueName="ExAutoRedirectVidPid" />
               </elements>
      </policy>

      <policy
         name="Exclude_auto_device_family" class="Machine" displayName="$(string.Exclude_auto_device_family)" explainText="$(string.Exclude_auto_device_family_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\USB" presentation="$(presentation.Exclude_auto_device_family)">
            <parentCategory ref="Client_Downloadable_only_settings" />
            <supportedOn ref="SupportedOn" />
               <elements>
                  <text id="Exclude_auto_device_family_TB" key="Software\Policies\Omnissa\Horizon\Agent\USB" valueName="ExAutoRedirectFamily" />
               </elements>
      </policy>

      <policy
         name="Exclude_Vid_Pid_from_Split" class="Machine" displayName="$(string.Exclude_Vid_Pid_from_Split)" explainText="$(string.Exclude_Vid_Pid_from_Split_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\USB" presentation="$(presentation.Exclude_Vid_Pid_from_Split)">
            <parentCategory ref="Client_Downloadable_only_settings" />
            <supportedOn ref="SupportedOn" />
               <elements>
                  <text id="Exclude_Vid_Pid_from_Split_TB" key="Software\Policies\Omnissa\Horizon\Agent\USB" valueName="SplitExcludeVidPid" />
               </elements>
      </policy>

      <policy
         name="Split_Vid_Pid_Device" class="Machine" displayName="$(string.Split_Vid_Pid_Device)" explainText="$(string.Split_Vid_Pid_Device_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\USB" presentation="$(presentation.Split_Vid_Pid_Device)">
            <parentCategory ref="Client_Downloadable_only_settings" />
            <supportedOn ref="SupportedOn" />
               <elements>
                  <text id="Split_Vid_Pid_Device_TB" key="Software\Policies\Omnissa\Horizon\Agent\USB" valueName="SplitVidPid" />
               </elements>
      </policy>

      <policy
         name="Allow_other_input_devices" class="Machine" displayName="$(string.Allow_other_input_devices)" explainText="$(string.Allow_other_input_devices_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\USB" presentation="$(presentation.Allow_other_input_devices)">
            <parentCategory ref="Client_Downloadable_only_settings" />
            <supportedOn ref="SupportedOn" />
               <elements>
                  <enum id="Allow_other_input_devices_DDL" valueName="AllowHID" key="Software\Policies\Omnissa\Horizon\Agent\USB" required="true">
                     <item displayName="$(string.Allow_Default)">
                        <value>
                           <string>d:true</string>
                        </value>
                     </item>
                     <item displayName="$(string.Allow_Override)">
                        <value>
                           <string>o:true</string>
                        </value>
                     </item>
                     <item displayName="$(string.Disable_Default)">
                        <value>
                           <string>d:false</string>
                        </value>
                     </item>
                     <item displayName="$(string.Disable_Override)">
                        <value>
                           <string>o:false</string>
                        </value>
                     </item>
                  </enum>
               </elements>
      </policy>

      <policy
         name="Allow_HID_Bootable" class="Machine" displayName="$(string.Allow_HID_Bootable)" explainText="$(string.Allow_HID_Bootable_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\USB" presentation="$(presentation.Allow_HID_Bootable)">
            <parentCategory ref="Client_Downloadable_only_settings" />
            <supportedOn ref="SupportedOn" />
               <elements>
                  <enum id="Allow_HID_Bootable_DDL" valueName="AllowHIDBootable" key="Software\Policies\Omnissa\Horizon\Agent\USB" required="true">
                     <item displayName="$(string.Allow_Default)">
                        <value>
                           <string>d:true</string>
                        </value>
                     </item>
                     <item displayName="$(string.Allow_Override)">
                        <value>
                           <string>o:true</string>
                        </value>
                     </item>
                     <item displayName="$(string.Disable_Default)">
                        <value>
                           <string>d:false</string>
                        </value>
                     </item>
                     <item displayName="$(string.Disable_Override)">
                        <value>
                           <string>o:false</string>
                        </value>
                     </item>
                  </enum>
               </elements>
      </policy>

      <policy
         name="Allow_Audio_Input_devices" class="Machine" displayName="$(string.Allow_Audio_Input_devices)" explainText="$(string.Allow_Audio_Input_devices_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\USB" presentation="$(presentation.Allow_Audio_Input_devices)">
            <parentCategory ref="Client_Downloadable_only_settings" />
            <supportedOn ref="SupportedOn" />
               <elements>
                  <enum id="Allow_Audio_Input_devices_DDL" valueName="AllowAudioIn" key="Software\Policies\Omnissa\Horizon\Agent\USB" required="true">
                     <item displayName="$(string.Allow_Default)">
                        <value>
                           <string>d:true</string>
                        </value>
                     </item>
                     <item displayName="$(string.Allow_Override)">
                        <value>
                           <string>o:true</string>
                        </value>
                     </item>
                     <item displayName="$(string.Disable_Default)">
                        <value>
                           <string>d:false</string>
                        </value>
                     </item>
                     <item displayName="$(string.Disable_Override)">
                        <value>
                           <string>o:false</string>
                        </value>
                     </item>
                  </enum>
               </elements>
      </policy>

      <policy
         name="Allow_Audio_Output_devices" class="Machine" displayName="$(string.Allow_Audio_Output_devices)" explainText="$(string.Allow_Audio_Output_devices_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\USB" presentation="$(presentation.Allow_Audio_Output_devices)">
            <parentCategory ref="Client_Downloadable_only_settings" />
            <supportedOn ref="SupportedOn" />
               <elements>
                  <enum id="Allow_Audio_Output_devices_DDL" valueName="AllowAudioOut" key="Software\Policies\Omnissa\Horizon\Agent\USB" required="true">
                     <item displayName="$(string.Allow_Default)">
                        <value>
                           <string>d:true</string>
                        </value>
                     </item>
                     <item displayName="$(string.Allow_Override)">
                        <value>
                           <string>o:true</string>
                        </value>
                     </item>
                     <item displayName="$(string.Disable_Default)">
                        <value>
                           <string>d:false</string>
                        </value>
                     </item>
                     <item displayName="$(string.Disable_Override)">
                        <value>
                           <string>o:false</string>
                        </value>
                     </item>
                  </enum>
               </elements>
      </policy>

      <policy
         name="Allow_keyboard_mouse" class="Machine" displayName="$(string.Allow_keyboard_mouse)" explainText="$(string.Allow_keyboard_mouse_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\USB" presentation="$(presentation.Allow_keyboard_mouse)">
            <parentCategory ref="Client_Downloadable_only_settings" />
            <supportedOn ref="SupportedOn" />
               <elements>
                  <enum id="Allow_keyboard_mouse_DDL" valueName="AllowKeyboardMouse" key="Software\Policies\Omnissa\Horizon\Agent\USB" required="true">
                     <item displayName="$(string.Allow_Default)">
                        <value>
                           <string>d:true</string>
                        </value>
                     </item>
                     <item displayName="$(string.Allow_Override)">
                        <value>
                           <string>o:true</string>
                        </value>
                     </item>
                     <item displayName="$(string.Disable_Default)">
                        <value>
                           <string>d:false</string>
                        </value>
                     </item>
                     <item displayName="$(string.Disable_Override)">
                        <value>
                           <string>o:false</string>
                        </value>
                     </item>
                  </enum>
               </elements>
      </policy>

      <policy
         name="Allow_Video_Devices" class="Machine" displayName="$(string.Allow_Video_Devices)" explainText="$(string.Allow_Video_Devices_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\USB" presentation="$(presentation.Allow_Video_Devices)">
            <parentCategory ref="Client_Downloadable_only_settings" />
            <supportedOn ref="SupportedOn" />
               <elements>
                  <enum id="Allow_Video_Devices_DDL" valueName="AllowVideo" key="Software\Policies\Omnissa\Horizon\Agent\USB" required="true">
                     <item displayName="$(string.Allow_Default)">
                        <value>
                           <string>d:true</string>
                        </value>
                     </item>
                     <item displayName="$(string.Allow_Override)">
                        <value>
                           <string>o:true</string>
                        </value>
                     </item>
                     <item displayName="$(string.Disable_Default)">
                        <value>
                           <string>d:false</string>
                        </value>
                     </item>
                     <item displayName="$(string.Disable_Override)">
                        <value>
                           <string>o:false</string>
                        </value>
                     </item>
                  </enum>
               </elements>
      </policy>

      <policy
         name="Allow_Smart_Cards" class="Machine" displayName="$(string.Allow_Smart_Cards)" explainText="$(string.Allow_Smart_Cards_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\USB" presentation="$(presentation.Allow_Smart_Cards)">
            <parentCategory ref="Client_Downloadable_only_settings" />
            <supportedOn ref="SupportedOn" />
               <elements>
                  <enum id="Allow_Smart_Cards_DDL" valueName="AllowSmartcard" key="Software\Policies\Omnissa\Horizon\Agent\USB" required="true">
                     <item displayName="$(string.Allow_Default)">
                        <value>
                           <string>d:true</string>
                        </value>
                     </item>
                     <item displayName="$(string.Allow_Override)">
                        <value>
                           <string>o:true</string>
                        </value>
                     </item>
                     <item displayName="$(string.Disable_Default)">
                        <value>
                           <string>d:false</string>
                        </value>
                     </item>
                     <item displayName="$(string.Disable_Override)">
                        <value>
                           <string>o:false</string>
                        </value>
                     </item>
                  </enum>
               </elements>
      </policy>

      <policy
         name="Allow_Auto_Device_Splitting" class="Machine" displayName="$(string.Allow_Auto_Device_Splitting)" explainText="$(string.Allow_Auto_Device_Splitting_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\USB" presentation="$(presentation.Allow_Auto_Device_Splitting)">
            <parentCategory ref="Client_Downloadable_only_settings" />
            <supportedOn ref="SupportedOn" />
               <elements>
                  <enum id="Allow_Auto_Device_Splitting_DDL" valueName="AllowAutoDeviceSplitting" key="Software\Policies\Omnissa\Horizon\Agent\USB" required="true">
                     <item displayName="$(string.Allow_Default)">
                        <value>
                           <string>d:true</string>
                        </value>
                     </item>
                     <item displayName="$(string.Allow_Override)">
                        <value>
                           <string>o:true</string>
                        </value>
                     </item>
                     <item displayName="$(string.Disable_Default)">
                        <value>
                           <string>d:false</string>
                        </value>
                     </item>
                     <item displayName="$(string.Disable_Override)">
                        <value>
                           <string>o:false</string>
                        </value>
                     </item>
                  </enum>
               </elements>
      </policy>

      <policy
         name="Enable_HTML5_FEATURES" class="Machine" displayName="$(string.Enable_HTML5_FEATURES)" explainText="$(string.Enable_HTML5_FEATURES_Desc)"
         key="Software\Policies\Omnissa\Horizon\HTML5SERVER" valueName="enabled">
         <parentCategory ref="Horizon_HTML5_FEATURES" />
         <supportedOn ref="SupportedOn" />
         <enabledValue>
            <decimal value="1" />
         </enabledValue>
         <disabledValue>
            <decimal value="0" />
         </disabledValue>
      </policy>

      <policy
         name="HTML5FEATURES_Disable_AudoDetect_Intranet" class="Machine" displayName="$(string.HTML5FEATURES_Disable_AudoDetect_Intranet)" explainText="$(string.HTML5FEATURES_Disable_AudoDetect_Intranet_Desc)"
         key="Software\Policies\Omnissa\Horizon\HTML5SERVER" valueName="disable_autodetect_intranet">
         <parentCategory ref="Horizon_HTML5_FEATURES" />
         <supportedOn ref="SupportedOn" />
         <enabledValue>
            <decimal value="1" />
         </enabledValue>
         <disabledValue>
            <decimal value="0" />
         </disabledValue>
      </policy>

     <policy
        name="Enable_HTML5_MMR" class="Machine" displayName="$(string.Enable_HTML5_MMR)" explainText="$(string.Enable_HTML5_MMR_Desc)"
        key="Software\Policies\Omnissa\Horizon\Html5mmr" valueName="enabled">
       <parentCategory ref="Horizon_HTML5MMR" />
       <supportedOn ref="SupportedOn" />
       <enabledValue>
         <decimal value="1" />
       </enabledValue>
       <disabledValue>
         <decimal value="0" />
       </disabledValue>
     </policy>

      <policy
         name="HTML5MMR_Enable_Chrome" class="Machine" displayName="$(string.HTML5MMR_Enable_Chrome)" explainText="$(string.HTML5MMR_Enable_Chrome_Desc)"
         key="Software\Policies\Omnissa\Horizon\Html5mmr" valueName="chrome_enabled">
         <parentCategory ref="Horizon_HTML5MMR" />
         <supportedOn ref="SupportedOn" />
         <enabledValue>
            <decimal value="1" />
         </enabledValue>
         <disabledValue>
            <decimal value="0" />
         </disabledValue>
      </policy>

      <policy
         name="HTML5MMR_Enable_Edge" class="Machine" displayName="$(string.HTML5MMR_Enable_Edge)" explainText="$(string.HTML5MMR_Enable_Edge_Desc)"
         key="Software\Policies\Omnissa\Horizon\Html5mmr" valueName="edge_enabled">
         <parentCategory ref="Horizon_HTML5MMR" />
         <supportedOn ref="SupportedOn" />
         <enabledValue>
            <decimal value="1" />
         </enabledValue>
         <disabledValue>
            <decimal value="0" />
         </disabledValue>
      </policy>

      <policy
         name="HTML5MMR_Enable_Edge_Chromium" class="Machine" displayName="$(string.HTML5MMR_Enable_Edge_Chromium)" explainText="$(string.HTML5MMR_Enable_Edge_Chromium_Desc)"
         key="Software\Policies\Omnissa\Horizon\Html5mmr" valueName="edge_chromium_enabled">
         <parentCategory ref="Horizon_HTML5MMR" />
         <supportedOn ref="SupportedOn" />
         <enabledValue>
            <decimal value="1" />
         </enabledValue>
         <disabledValue>
            <decimal value="0" />
         </disabledValue>
      </policy>
      <policy
         name="HTML5MMR_Auto_Adjust_Visual_Effect" class="Machine" displayName="$(string.HTML5MMR_Auto_Adjust_Visual_Effect)" explainText="$(string.HTML5MMR_Auto_Adjust_Visual_Effect_Desc)"
         key="Software\Policies\Omnissa\Horizon\Html5mmr" valueName="auto_adjust_visualeffect">
         <parentCategory ref="Horizon_HTML5_FEATURES" />
         <supportedOn ref="SupportedOn" />
         <enabledValue>
            <decimal value="1" />
         </enabledValue>
         <disabledValue>
            <decimal value="0" />
         </disabledValue>
      </policy>

      <policy
         name="HTML5MMRUrlList" class="Machine" displayName="$(string.HTML5MMRUrlList)" explainText="$(string.HTML5MMRUrlList_Desc)"
         key="Software\Policies\Omnissa\Horizon\Html5mmr\UrlWhiteList" presentation="$(presentation.HTML5MMRUrlList)">
         <parentCategory ref="Horizon_HTML5MMR" />
         <supportedOn ref="SupportedOn" />
         <elements>
            <list id="HTML5MMRUrlList_list" key="Software\Policies\Omnissa\Horizon\Html5mmr\UrlWhiteList" explicitValue="true" />
         </elements>
      </policy>

      <policy
         name="HTML5MMRUrlBlockList" class="Machine" displayName="$(string.HTML5MMRUrlBlockList)" explainText="$(string.HTML5MMRUrlBlockList_Desc)"
         key="Software\Policies\Omnissa\Horizon\Html5mmr\UrlBlockList" presentation="$(presentation.HTML5MMRUrlBlockList)">
         <parentCategory ref="Horizon_HTML5MMR" />
         <supportedOn ref="SupportedOn" />
         <elements>
            <list id="HTML5MMRUrlBlockList_list" key="Software\Policies\Omnissa\Horizon\Html5mmr\UrlBlockList" explicitValue="true" />
         </elements>
      </policy>

      <policy
         name="Enable_GEO_REDIR" class="Machine" displayName="$(string.Enable_GEO_REDIR)" explainText="$(string.Enable_GEO_REDIR_Desc)"
         key="Software\Policies\Omnissa\Horizon\GEOREDIR" valueName="enabled">
         <parentCategory ref="Horizon_GEO_REDIR" />
         <supportedOn ref="SupportedOn" />
         <enabledValue>
            <decimal value="1" />
         </enabledValue>
         <disabledValue>
         <decimal value="0" />
         </disabledValue>
      </policy>

      <policy
         name="Enable_GEO_REDIR_For_Chrome" class="Machine" displayName="$(string.Enable_GEO_REDIR_For_Chrome)" explainText="$(string.Enable_GEO_REDIR_For_Chrome_Desc)"
         key="Software\Policies\Omnissa\Horizon\GEOREDIR" valueName="chrome_enabled">
         <parentCategory ref="Horizon_GEO_REDIR" />
         <supportedOn ref="SupportedOn" />
         <enabledValue>
            <decimal value="1" />
         </enabledValue>
         <disabledValue>
         <decimal value="0" />
         </disabledValue>
      </policy>

      <policy
         name="Enable_GEO_REDIR_For_Edge_Chromium" class="Machine" displayName="$(string.Enable_GEO_REDIR_For_Edge_Chromium)" explainText="$(string.Enable_GEO_REDIR_For_Edge_Chromium_Desc)"
         key="Software\Policies\Omnissa\Horizon\GEOREDIR" valueName="edge_chromium_enabled">
         <parentCategory ref="Horizon_GEO_REDIR" />
         <supportedOn ref="SupportedOn" />
         <enabledValue>
            <decimal value="1" />
         </enabledValue>
         <disabledValue>
         <decimal value="0" />
         </disabledValue>
      </policy>

      <policy
         name="GeoRedirUrlList" class="Machine" displayName="$(string.GeoRedirUrlList)" explainText="$(string.GeoRedirUrlList_Desc)"
         key="Software\Policies\Omnissa\Horizon\GEOREDIR\UrlWhiteList" presentation="$(presentation.GeoRedirUrlList)">
         <parentCategory ref="Horizon_GEO_REDIR" />
         <supportedOn ref="SupportedOn" />
         <elements>
            <list id="GeoRedirUrlList_list" key="Software\Policies\Omnissa\Horizon\GEOREDIR\UrlWhiteList" explicitValue="true" />
         </elements>
      </policy>

      <policy
         name="GeoRedirDistanceDelta" class="Machine" displayName="$(string.GeoRedirDistanceDelta)" explainText="$(string.GeoRedirDistanceDelta_Desc)"
         key="Software\Policies\Omnissa\Horizon\GEOREDIR" presentation="$(presentation.GeoRedirDistanceDelta)">
         <parentCategory ref="Horizon_GEO_REDIR" />
         <supportedOn ref="SupportedOn" />
         <elements>
            <text id="GeoRedirDistanceDelta_value" key="Software\Policies\Omnissa\Horizon\GEOREDIR" valueName="geoDistanceDelta" />
         </elements>
      </policy>

      <policy
         name="Enable_BROWSER_REDIR" class="Machine" displayName="$(string.Enable_BROWSER_REDIR)" explainText="$(string.Enable_BROWSER_REDIR_Desc)"
         key="Software\Policies\Omnissa\Horizon\BrowserRedir" valueName="enabled">
         <parentCategory ref="Horizon_BROWSER_REDIR" />
         <supportedOn ref="SupportedOn" />
         <enabledValue>
            <decimal value="1" />
         </enabledValue>
         <disabledValue>
         <decimal value="0" />
         </disabledValue>
      </policy>

      <policy
         name="Enable_ClientMediaPerm_Popup" class="Machine" displayName="$(string.Enable_ClientMediaPerm_Popup)"
         explainText="$(string.Enable_ClientMediaPerm_Popup_Desc)" valueName="enableClientMediaPermPopup"
         key="Software\Policies\Omnissa\Horizon\BrowserRedir">
         <parentCategory ref="Horizon_BROWSER_REDIR" />
         <supportedOn ref="SupportedOn" />
         <enabledValue>
            <decimal value="1" />
         </enabledValue>
         <disabledValue>
            <decimal value="0" />
         </disabledValue>
      </policy>

      <policy
         name="Enable_BROWSER_REDIR_For_Chrome" class="Machine" displayName="$(string.Enable_BROWSER_REDIR_For_Chrome)" explainText="$(string.Enable_BROWSER_REDIR_For_Chrome_Desc)"
         key="Software\Policies\Omnissa\Horizon\BrowserRedir" valueName="chrome_enabled">
         <parentCategory ref="Horizon_BROWSER_REDIR" />
         <supportedOn ref="SupportedOn" />
         <enabledValue>
            <decimal value="1" />
         </enabledValue>
         <disabledValue>
            <decimal value="0" />
         </disabledValue>
      </policy>

      <policy
         name="Enable_BROWSER_REDIR_For_Edge_Chromium" class="Machine" displayName="$(string.Enable_BROWSER_REDIR_For_Edge_Chromium)" explainText="$(string.Enable_BROWSER_REDIR_For_Edge_Chromium_Desc)"
         key="Software\Policies\Omnissa\Horizon\BrowserRedir" valueName="edge_chromium_enabled">
         <parentCategory ref="Horizon_BROWSER_REDIR" />
         <supportedOn ref="SupportedOn" />
         <enabledValue>
            <decimal value="1" />
         </enabledValue>
         <disabledValue>
            <decimal value="0" />
         </disabledValue>
      </policy>

      <policy
         name="Enable_Teams_Redir" class="Machine" displayName="$(string.Enable_Teams_Redir)" explainText="$(string.Enable_Teams_Redir_Desc)"
         key="Software\Policies\Omnissa\Horizon\WebRTCRedir" valueName="teamsEnabled" presentation="$(presentation.Force_Enable_Teams_Redir)">
         <parentCategory ref="Horizon_WebRTC_REDIR_FEATURES" />
         <supportedOn ref="SupportedOn" />
         <enabledValue>
            <decimal value="1" />
         </enabledValue>
         <disabledValue>
            <decimal value="0" />
         </disabledValue>
         <elements>
            <boolean id="Force_Enable_Teams_Redir_Value"
                     key="Software\Policies\Omnissa\Horizon\WebRTCRedir"
                     valueName="forceTeamsEnabled">
               <trueValue>
                  <decimal value="1" />
               </trueValue>
               <falseValue>
                  <delete />
               </falseValue>
            </boolean>
         </elements>
      </policy>

      <policy
         name="Enable_Electron_App_Redir" class="Machine" displayName="$(string.Enable_Electron_App_Redir)" explainText="$(string.Enable_Electron_App_Redir_Desc)"
         key="Software\Policies\Omnissa\Horizon\WebRTCRedir" valueName="electronAppEnabled" presentation="$(presentation.Force_Enable_Electron_App_Redir)">
         <parentCategory ref="Horizon_WebRTC_REDIR_FEATURES" />
         <supportedOn ref="SupportedOn" />
         <enabledValue>
            <decimal value="1" />
         </enabledValue>
         <disabledValue>
            <decimal value="0" />
         </disabledValue>
         <elements>
            <boolean id="Force_Enable_Electron_App_Redir_Value"
                     key="Software\Policies\Omnissa\Horizon\WebRTCRedir"
                     valueName="forceElectronAppEnabled">
               <trueValue>
                  <decimal value="1" />
               </trueValue>
               <falseValue>
                  <decimal value="0" />
               </falseValue>
            </boolean>
         </elements>
      </policy>

      <policy
         name="WebRTC_SDK_Enable_Web_App_Redir" class="Machine" displayName="$(string.WebRTC_SDK_Enable_Web_App_Redir)" explainText="$(string.WebRTC_SDK_Enable_Web_App_Redir_Desc)"
         key="Software\Policies\Omnissa\Horizon\WebRTCRedirSDKWebApp" valueName="enabled">
         <parentCategory ref="Horizon_WebRTC_SDK_WEBAPP" />
         <supportedOn ref="SupportedOn" />
         <enabledValue>
            <decimal value="1" />
         </enabledValue>
         <disabledValue>
            <decimal value="0" />
         </disabledValue>
      </policy>

      <policy
         name="WebRTC_SDK_Enable_Web_App_Redir_Chrome" class="Machine" displayName="$(string.WebRTC_SDK_Enable_Web_App_Redir_Chrome)" explainText="$(string.WebRTC_SDK_Enable_Web_App_Redir_Chrome_Desc)"
         key="Software\Policies\Omnissa\Horizon\WebRTCRedirSDKWebApp" valueName="chrome_enabled">
         <parentCategory ref="Horizon_WebRTC_SDK_WEBAPP" />
         <supportedOn ref="SupportedOn" />
         <enabledValue>
            <decimal value="1" />
         </enabledValue>
         <disabledValue>
            <decimal value="0" />
         </disabledValue>
      </policy>

      <policy
         name="WebRTC_SDK_Enable_Web_App_Redir_Edge" class="Machine" displayName="$(string.WebRTC_SDK_Enable_Web_App_Redir_Edge)" explainText="$(string.WebRTC_SDK_Enable_Web_App_Redir_Edge_Desc)"
         key="Software\Policies\Omnissa\Horizon\WebRTCRedirSDKWebApp" valueName="edge_chromium_enabled">
         <parentCategory ref="Horizon_WebRTC_SDK_WEBAPP" />
         <supportedOn ref="SupportedOn" />
         <enabledValue>
            <decimal value="1" />
         </enabledValue>
         <disabledValue>
            <decimal value="0" />
         </disabledValue>
      </policy>

      <policy
         name="WebRTC_SDK_Web_App_Redir_Allow_List" class="Machine" displayName="$(string.WebRTC_SDK_Web_App_Redir_Allow_List)" explainText="$(string.WebRTC_SDK_Web_App_Redir_Allow_List_Desc)"
         key="Software\Policies\Omnissa\Horizon\WebRTCRedirSDKWebApp\UrlAllowList" presentation="$(presentation.WebRTC_SDK_Web_App_Redir_Allow_List)">
         <parentCategory ref="Horizon_WebRTC_SDK_WEBAPP" />
         <supportedOn ref="SupportedOn" />
         <elements>
            <list id="WebRTC_SDK_Web_App_Redir_Allow_List_list" key="Software\Policies\Omnissa\Horizon\WebRTCRedirSDKWebApp\UrlAllowList" explicitValue="true" />
         </elements>
      </policy>

      <policy
         name="Enable_AEC_Teams_Redir" class="Machine" displayName="$(string.Enable_AEC_Teams_Redir)" explainText="$(string.Enable_AEC_Teams_Redir_Desc)"
         key="Software\Policies\Omnissa\Horizon\WebRTCRedir" valueName="enableAEC" presentation="$(presentation.Webrtc_Recommended_AEC)">
         <parentCategory ref="Horizon_WebRTC_REDIR_FEATURES" />
         <supportedOn ref="SupportedOn" />
         <enabledValue>
            <decimal value="1" />
         </enabledValue>
         <disabledValue>
            <decimal value="0" />
         </disabledValue>
         <elements>
            <boolean id="Webrtc_Recommended_AEC_CB"
                     key="Software\Policies\Omnissa\Horizon\WebRTCRedir"
                     valueName="AecOption">
               <trueValue>
                  <decimal value="1" />
               </trueValue>
               <falseValue>
                  <decimal value="2" />
               </falseValue>
            </boolean>
         </elements>
      </policy>

      <policy
         name="Enable_Datachannel_Teams_Redir" class="Machine" displayName="$(string.Enable_Datachannel_Teams_Redir)" explainText="$(string.Enable_Datachannel_Teams_Redir_Desc)"
         key="Software\Policies\Omnissa\Horizon\WebRTCRedir" valueName="enableDatachannel">
         <parentCategory ref="Horizon_WebRTC_REDIR_FEATURES" />
         <supportedOn ref="SupportedOn" />
         <enabledValue>
            <decimal value="1" />
         </enabledValue>
         <disabledValue>
            <decimal value="0" />
         </disabledValue>
      </policy>

      <policy
         name="Video_Cpu_Overuse_Threshold" class="Machine" displayName="$(string.Video_Cpu_Overuse_Threshold)" explainText="$(string.Video_Cpu_Overuse_Threshold_Desc)"
         key="Software\Policies\Omnissa\Horizon\WebRTCRedir" valueName="videoCpuOveruseThreshold" presentation="$(presentation.Video_Cpu_Overuse_Threshold)">
         <parentCategory ref="Horizon_WebRTC_REDIR_FEATURES" />
         <supportedOn ref="SupportedOn" />
         <elements>
             <decimal id="Video_Cpu_Overuse_Threshold_data" key="Software\Policies\Omnissa\Horizon\WebRTCRedir" valueName="videoCpuOveruseThreshold" maxValue="100" minValue="0" />
         </elements>
      </policy>

      <policy
         name="Enable_Sharing_Client_Screen_InAppSession" class="Machine" displayName="$(string.Enable_Sharing_Client_Screen_InAppSession)" explainText="$(string.Enable_Sharing_Client_Screen_InAppSession_Desc)"
         key="Software\Policies\Omnissa\Horizon\WebRTCRedir" valueName="allowSharingClientScreenInAppSession">
         <parentCategory ref="Horizon_WebRTC_REDIR_FEATURES" />
         <supportedOn ref="SupportedOn" />
         <enabledValue>
            <decimal value="1" />
         </enabledValue>
         <disabledValue>
            <decimal value="0" />
         </disabledValue>
      </policy>

      <policy
         name="Enable_Sharing_Client_E911_data" class="Machine" displayName="$(string.Enable_E911)" explainText="$(string.Enable_E911_Desc)"
         key="Software\Policies\Omnissa\Horizon\WebRTCRedir" valueName="supportE911">
         <parentCategory ref="Horizon_WebRTC_REDIR_FEATURES" />
         <supportedOn ref="SupportedOn" />
         <enabledValue>
            <decimal value="1" />
         </enabledValue>
         <disabledValue>
            <decimal value="0" />
         </disabledValue>
      </policy>

      <policy
         name="Enable_Background_Effects" class="Machine" displayName="$(string.Enable_Background_Effects)" explainText="$(string.Enable_Background_Effects_Desc)"
         key="Software\Policies\Omnissa\Horizon\WebRTCRedir" valueName="enableBackgroundEffects">
         <parentCategory ref="Horizon_WebRTC_REDIR_FEATURES" />
         <supportedOn ref="SupportedOn" />
         <enabledValue>
            <decimal value="1" />
         </enabledValue>
         <disabledValue>
            <decimal value="0" />
         </disabledValue>
      </policy>

      <policy
         name="Enable_Client_HID" class="Machine" displayName="$(string.Enable_HID)" explainText="$(string.Enable_HID_Desc)"
         key="Software\Policies\Omnissa\Horizon\WebRTCRedir" valueName="enableHid">
         <parentCategory ref="Horizon_WebRTC_REDIR_FEATURES" />
         <supportedOn ref="SupportedOn" />
         <enabledValue>
            <decimal value="1" />
         </enabledValue>
         <disabledValue>
            <decimal value="0" />
         </disabledValue>
      </policy>

      <policy
         name="Enable_Webrtc_Appshare" class="Machine" displayName="$(string.Enable_Webrtc_Appshare)" explainText="$(string.Enable_Webrtc_Appshare_Desc)"
         key="Software\Policies\Omnissa\Horizon\WebRTCRedir" valueName="enableAppshare">
         <parentCategory ref="Horizon_WebRTC_REDIR_FEATURES" />
         <supportedOn ref="SupportedOn" />
         <enabledValue>
            <decimal value="1" />
         </enabledValue>
         <disabledValue>
            <decimal value="0" />
         </disabledValue>
      </policy>

      <policy
         name="Enable_Webrtc_Appshare_Givecontrol" class="Machine" displayName="$(string.Enable_Webrtc_Appshare_Givecontrol)" explainText="$(string.Enable_Webrtc_Appshare_Givecontrol_Desc)"
         key="Software\Policies\Omnissa\Horizon\WebRTCRedir" valueName="enableAppshareGivecontrol">
         <parentCategory ref="Horizon_WebRTC_REDIR_FEATURES" />
         <supportedOn ref="SupportedOn" />
         <enabledValue>
            <decimal value="1" />
         </enabledValue>
         <disabledValue>
            <decimal value="0" />
         </disabledValue>
      </policy>

      <policy
         name="Disable_Mirrored_Video" class="Machine" displayName="$(string.Disable_Mirrored_Video)" explainText="$(string.Disable_Mirrored_Video_Desc)"
         key="Software\Policies\Omnissa\Horizon\WebRTCRedir" valueName="disableMirroredVideo">
         <parentCategory ref="Horizon_WebRTC_REDIR_FEATURES" />
         <supportedOn ref="SupportedOn" />
         <enabledValue>
            <decimal value="1" />
         </enabledValue>
         <disabledValue>
            <decimal value="0" />
         </disabledValue>
      </policy>

      <policy
         name="ForceEnableCustomBackgroundImages" class="Machine" displayName="$(string.ForceEnableCustomBackgroundImages)" explainText="$(string.ForceEnableCustomBackgroundImagesDesc)"
         key="Software\Policies\Omnissa\Horizon\WebRTCRedir\MSTeamsCustomImagesBackground" valueName="enable">
         <parentCategory ref="CustomBackgroundImages" />
         <supportedOn ref="SupportedOn" />
         <enabledValue>
            <decimal value="1" />
         </enabledValue>
         <disabledValue>
            <decimal value="0" />
         </disabledValue>
      </policy>

      <policy
         name="CustomBackgroundImagesFolderPath" class="Machine" displayName="$(string.CustomBackgroundImagesFolderPath)" explainText="$(string.CustomBackgroundImagesFolderPathDesc)"
         key="Software\Policies\Omnissa\Horizon\WebRTCRedir\MSTeamsCustomImagesBackground" presentation="$(presentation.CustomBackgroundImagesFolderPath)">
         <parentCategory ref="CustomBackgroundImages" />
         <supportedOn ref="SupportedOn" />
         <elements>
            <text id="CustomBackgroundImagesFolder_Path" key="Software\Policies\Omnissa\Horizon\WebRTCRedir\MSTeamsCustomImagesBackground" valueName="imageFolder" />
         </elements>
      </policy>

      <policy
         name="CustomBackgroundDefaultImageName" class="Machine" displayName="$(string.CustomBackgroundDefaultImageName)" explainText="$(string.CustomBackgroundDefaultImageNameDesc)"
         key="Software\Policies\Omnissa\Horizon\WebRTCRedir\MSTeamsCustomImagesBackground" presentation="$(presentation.CustomBackgroundDefaultImageName)">
         <parentCategory ref="CustomBackgroundImages" />
         <supportedOn ref="SupportedOn" />
         <elements>
            <text id="CustomBackgroundDefaultImageName_Value" key="Software\Policies\Omnissa\Horizon\WebRTCRedir\MSTeamsCustomImagesBackground" valueName="defaultImageName" />
         </elements>
      </policy>

      <policy
         name="Enable_Webrtc_ProxyProbeUrl" class="Machine" displayName="$(string.Enable_Webrtc_ProxyProbeUrl)" explainText="$(string.Enable_Webrtc_ProxyProbeUrl_Desc)"
         key="Software\Policies\Omnissa\Horizon\WebRTCRedir" presentation="$(presentation.WebrtcProxyProbeUrl)">
         <parentCategory ref="Horizon_WebRTC_REDIR_FEATURES" />
         <supportedOn ref="SupportedOn" />
         <elements>
            <text id="WebrtcProxyProbeUrl_value" key="Software\Policies\Omnissa\Horizon\WebRTCRedir" valueName="proxyProbeUrl" />
         </elements>
      </policy>

      <policy
         name="BrowserRedirUrlList" class="Machine" displayName="$(string.BrowserRedirUrlList)" explainText="$(string.BrowserRedirUrlList_Desc)"
         key="Software\Policies\Omnissa\Horizon\BrowserRedir\UrlWhiteList" presentation="$(presentation.BrowserRedirUrlList)">
         <parentCategory ref="Horizon_BROWSER_REDIR" />
         <supportedOn ref="SupportedOn" />
         <elements>
            <list id="BrowserRedirUrlList_list" key="Software\Policies\Omnissa\Horizon\BrowserRedir\UrlWhiteList" explicitValue="true" />
         </elements>
      </policy>

      <policy
         name="BrowserRedirNavUrlList" class="Machine" displayName="$(string.BrowserRedirNavUrlList)" explainText="$(string.BrowserRedirNavUrlList_Desc)"
         key="Software\Policies\Omnissa\Horizon\BrowserRedir\NavUrlWhiteList" presentation="$(presentation.BrowserRedirNavUrlList)">
         <parentCategory ref="Horizon_BROWSER_REDIR" />
         <supportedOn ref="SupportedOn" />
         <elements>
            <list id="BrowserRedirNavUrlList_list" key="Software\Policies\Omnissa\Horizon\BrowserRedir\NavUrlWhiteList" explicitValue="true" />
         </elements>
      </policy>

      <policy
         name="EnhBrowserRedirUrlList" class="Machine" displayName="$(string.EnhBrowserRedirUrlList)" explainText="$(string.EnhBrowserRedirUrlList_Desc)"
         key="Software\Policies\Omnissa\Horizon\BrowserRedir\EnhUrlAllowList" presentation="$(presentation.EnhBrowserRedirUrlList)">
         <parentCategory ref="Horizon_BROWSER_REDIR" />
         <supportedOn ref="SupportedOn" />
         <elements>
            <list id="EnhBrowserRedirUrlList_list" key="Software\Policies\Omnissa\Horizon\BrowserRedir\EnhUrlAllowList" explicitValue="true" />
         </elements>
      </policy>

      <policy
         name="BrowserRedirFallbackWhitelistErr" class="Machine" displayName="$(string.BrowserRedirFallbackWhitelistErr)" explainText="$(string.BrowserRedirFallbackWhitelistErr_Desc)"
         key="Software\Policies\Omnissa\Horizon\BrowserRedir" valueName="autoFallbackOnWhitelistError">
         <parentCategory ref="Horizon_BROWSER_REDIR" />
         <supportedOn ref="SupportedOn" />
         <enabledValue>
            <decimal value="1" />
         </enabledValue>
         <disabledValue>
            <decimal value="0" />
         </disabledValue>
      </policy>

      <policy
         name="BrowserRedirFetchFromServer" class="Machine" displayName="$(string.BrowserRedirFetchFromServer)" explainText="$(string.BrowserRedirFetchFromServer_Desc)"
         key="Software\Policies\Omnissa\Horizon\BrowserRedir" valueName="fetchFromServer">
         <parentCategory ref="Horizon_BROWSER_REDIR" />
         <supportedOn ref="SupportedOn" />
         <enabledValue>
            <decimal value="1" />
         </enabledValue>
         <disabledValue>
            <decimal value="0" />
         </disabledValue>
      </policy>

      <policy
         name="BrowserRedirShowErrPage" class="Machine" displayName="$(string.BrowserRedirShowErrPage)" explainText="$(string.BrowserRedirShowErrPage_Desc)"
         key="Software\Policies\Omnissa\Horizon\BrowserRedir" valueName="showErrorPage">
         <parentCategory ref="Horizon_BROWSER_REDIR" />
         <supportedOn ref="SupportedOn" />
         <enabledValue>
            <decimal value="1" />
         </enabledValue>
         <disabledValue>
            <decimal value="0" />
         </disabledValue>
      </policy>

      <policy
         name="Disable_Time_Zone_sync_2" class="User" displayName="$(string.Disable_Time_Zone_sync)" explainText="$(string.Disable_Time_Zone_sync_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\Configuration" valueName="DisableTimeZoneSynchronization">
            <parentCategory ref="Agent_Configuration" />
            <supportedOn ref="SupportedOn" />
               <enabledValue>
                  <string>true</string>
               </enabledValue>
               <disabledValue>
                  <string>false</string>
               </disabledValue>
      </policy>

      <policy
         name="DpiSync_2" class="User" displayName="$(string.DpiSync)" explainText="$(string.DpiSync_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\Configuration" valueName="DpiSync">
            <parentCategory ref="Agent_Configuration" />
            <supportedOn ref="SupportedOn" />
               <enabledValue>
                  <decimal value="1" />
               </enabledValue>
               <disabledValue>
                  <decimal value="0" />
               </disabledValue>
      </policy>

      <policy
         name="DpiSyncPerMonitor_2" class="User" displayName="$(string.DpiSyncPerMonitor)" explainText="$(string.DpiSyncPerMonitor_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\Configuration" valueName="DpiSyncPerMonitor">
         <parentCategory ref="Agent_Configuration" />
         <supportedOn ref="SupportedOn" />
         <enabledValue>
            <decimal value="1" />
         </enabledValue>
         <disabledValue>
            <decimal value="0" />
         </disabledValue>
      </policy>

      <policy
         name="DisplayScaling_2" class="User" displayName="$(string.DisplayScaling)" explainText="$(string.DisplayScaling_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\Configuration" valueName="DisplayScaling">
         <parentCategory ref="Agent_Configuration" />
         <supportedOn ref="SupportedOn" />
         <enabledValue>
            <decimal value="1" />
         </enabledValue>
         <disabledValue>
            <decimal value="0" />
         </disabledValue>
      </policy>

      <policy
         name="DisallowCollaboration" class="Machine" displayName="$(string.DisallowCollaboration)" explainText="$(string.DisallowCollaboration_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\Configuration" valueName="AllowCollaboration">
         <parentCategory ref="Collaboration" />
         <supportedOn ref="SupportedOn" />
         <enabledValue>
            <string>false</string>
         </enabledValue>
         <disabledValue>
            <string>true</string>
         </disabledValue>
      </policy>

      <policy
         name="AllowCollaborationInviteByIM" class="Machine" displayName="$(string.AllowCollaborationInviteByIM)" explainText="$(string.AllowCollaborationInviteByIM_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\Configuration" valueName="AllowCollaborationInviteByIM">
         <parentCategory ref="Collaboration" />
         <supportedOn ref="SupportedOn" />
         <enabledValue>
            <string>true</string>
         </enabledValue>
         <disabledValue>
            <string>false</string>
         </disabledValue>
      </policy>

      <policy
         name="AllowCollaborationInviteByEmail" class="Machine" displayName="$(string.AllowCollaborationInviteByEmail)" explainText="$(string.AllowCollaborationInviteByEmail_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\Configuration" valueName="AllowCollaborationInviteByEmail">
         <parentCategory ref="Collaboration" />
         <supportedOn ref="SupportedOn" />
         <enabledValue>
            <string>true</string>
         </enabledValue>
         <disabledValue>
            <string>false</string>
         </disabledValue>
      </policy>

      <policy
         name="AllowCollaborationControlPassing" class="Machine" displayName="$(string.AllowCollaborationControlPassing)" explainText="$(string.AllowCollaborationControlPassing_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\Configuration" valueName="AllowCollaborationControlPassing">
         <parentCategory ref="Collaboration" />
         <supportedOn ref="SupportedOn" />
         <enabledValue>
            <string>true</string>
         </enabledValue>
         <disabledValue>
            <string>false</string>
         </disabledValue>
      </policy>

      <policy
         name="MaxCollaboratorCount" class="Machine" displayName="$(string.MaxCollaboratorCount)" explainText="$(string.MaxCollaboratorCount_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\Configuration" presentation="$(presentation.MaxCollaboratorCount)">
         <parentCategory ref="Collaboration" />
         <supportedOn ref="SupportedOn" />
         <elements>
            <decimal id="MaxCollaboratorCount_DB" key="Software\Policies\Omnissa\Horizon\Agent\Configuration" valueName="MaxCollaboratorCount" maxValue="20" minValue="1" storeAsText="true" />
         </elements>
      </policy>

      <policy
         name="CollaborationEmailInviteDelimiter" class="Machine" displayName="$(string.CollaborationEmailInviteDelimiter)" explainText="$(string.CollaborationEmailInviteDelimiter_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\Configuration" presentation="$(presentation.CollaborationEmailInviteDelimiter)">
         <parentCategory ref="Collaboration" />
         <supportedOn ref="SupportedOn" />
         <elements>
            <text id="CollaborationEmailInviteDelimiter_TB" key="Software\Policies\Omnissa\Horizon\Agent\Configuration" valueName="CollaborationEmailInviteDelimiter" />
         </elements>
      </policy>

      <policy
         name="CollaborationClipboardIncludeOutlookURL" class="Machine" displayName="$(string.CollaborationClipboardIncludeOutlookURL)" explainText="$(string.CollaborationClipboardIncludeOutlookURL_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\Configuration" valueName="CollaborationClipboardIncludeOutlookURL">
         <parentCategory ref="Collaboration" />
         <supportedOn ref="SupportedOn" />
         <enabledValue>
            <string>true</string>
         </enabledValue>
         <disabledValue>
            <string>false</string>
         </disabledValue>
      </policy>

      <policy
         name="CollaborationServerURLs" class="Machine" displayName="$(string.CollaborationServerURLs)" explainText="$(string.CollaborationServerURLs_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\Configuration\CollaborationServerURLs" presentation="$(presentation.CollaborationServerURLs)">
         <parentCategory ref="Collaboration" />
         <supportedOn ref="SupportedOn" />
         <elements>
            <list id="CollaborationServerURLs_list" key="Software\Policies\Omnissa\Horizon\Agent\Configuration\CollaborationServerURLs" explicitValue="true" />
         </elements>
      </policy>

      <policy
         name="Accept_SSL_encr_framework_channel_2" class="User" displayName="$(string.Accept_SSL_encr_framework_channel)" explainText="$(string.Accept_SSL_encr_framework_channel_Desc)"
         key="Software\Policies\Omnissa\Horizon\Security" presentation="$(presentation.Accept_SSL_encr_framework_channel_2)">
            <parentCategory ref="Agent_Security" />
            <supportedOn ref="SupportedOn" />
               <elements>
                  <enum id="Accept_SSL_encr_framework_channel_2_DDL" valueName="AcceptTicketSSLAuth" key="Software\Policies\Omnissa\Horizon\Security" required="true">
                     <item displayName="$(string.Enable)">
                        <value>
                           <string>1</string>
                        </value>
                     </item>
                     <item displayName="$(string.Disable)">
                        <value>
                           <string>0</string>
                        </value>
                     </item>
                     <item displayName="$(string.Enforce)">
                        <value>
                           <string>3</string>
                        </value>
                     </item>
                  </enum>
               </elements>
      </policy>

      <policy
         name="Enable_user_prof_customization_2" class="User" displayName="$(string.Enable_user_prof_customization)" explainText="$(string.Enable_user_prof_customization_Desc)"
         key="Software\Policies\Omnissa\Horizon\Unity" valueName="CustomizeUserProfile">
            <parentCategory ref="Unity_Touch_Hosted_Apps_2" />
            <supportedOn ref="SupportedOn" />
               <enabledValue>
                  <string>true</string>
               </enabledValue>
               <disabledValue>
                  <string>false</string>
               </disabledValue>
      </policy>

      <policy
         name="LaunchAppWhenArgsAreDifferent_2" class="User" displayName="$(string.LaunchAppWhenArgsAreDifferent)" explainText="$(string.LaunchAppWhenArgsAreDifferent_Desc)"
         key="Software\Policies\Omnissa\Horizon\Unity" valueName="LaunchAppWhenArgsAreDifferent">
            <parentCategory ref="Unity_Touch_Hosted_Apps_2" />
            <supportedOn ref="SupportedOn" />
            <enabledValue>
               <string>true</string>
            </enabledValue>
            <disabledValue>
               <string>false</string>
            </disabledValue>
      </policy>

      <policy
         name="ProcessIgnoreList" class="Machine" displayName="$(string.ProcessIgnoreList)" explainText="$(string.ProcessIgnoreList_Desc)"
         key="Software\Policies\Omnissa\Horizon\AppTap\Config" presentation="$(presentation.ProcessIgnoreList)">
            <parentCategory ref="Horizon_AppTap_Config" />
            <supportedOn ref="SupportedOn" />
               <elements>
                  <multiText id="ProcessIgnoreList_TB" key="Software\Policies\Omnissa\Horizon\AppTap\Config" valueName="ProcessIgnoreList" />
               </elements>
      </policy>

     <policy
        name="EnableUWPOnRDSH" class="Machine" displayName="$(string.EnableUWPOnRDSH)" explainText="$(string.EnableUWPOnRDSH_Desc)"
        key="Software\Policies\Omnissa\Horizon\Unity" valueName="EnableUWPOnRDSH">
           <parentCategory ref="Unity_Touch_Hosted_Apps" />
           <supportedOn ref="SupportedOn" />
              <enabledValue>
                 <string>true</string>
              </enabledValue>
              <disabledValue>
                 <string>false</string>
              </disabledValue>
     </policy>

     <policy
        name="HandleLegalNoticeInWindow" class="Machine" displayName="$(string.HandleLegalNoticeInWindow)" explainText="$(string.HandleLegalNoticeInWindow_Desc)"
        presentation="$(presentation.AppSignInWindow)" key="Software\Policies\Omnissa\Horizon\Unity" valueName="HandleLegalNoticeInWindow">
           <parentCategory ref="Unity_Touch_Hosted_Apps" />
           <supportedOn ref="SupportedOn" />
              <enabledValue>
                 <string>true</string>
              </enabledValue>
              <disabledValue>
                 <string>false</string>
              </disabledValue>
        <elements>
           <decimal id="app_sign_in_window_width" valueName="SignInWindowWidth" minValue="640" maxValue="16384" />
           <decimal id="app_sign_in_window_height" valueName="SignInWindowHeight" minValue="360" maxValue="16384" />
        </elements>
     </policy>

     <policy
        name="EnableBatStatRedir" class="Machine" displayName="$(string.EnableBatStatRedir)" explainText="$(string.EnableBatStatRedir_Desc)"
        key="Software\Policies\Omnissa\Horizon\Agent\Configuration" valueName="EnableBatStatRedir">
           <parentCategory ref="Agent_Configuration" />
           <supportedOn ref="SupportedOn" />
              <enabledValue>
                 <string>true</string>
              </enabledValue>
              <disabledValue>
                 <string>false</string>
              </disabledValue>
     </policy>

     <policy
        name="EnableDisplayNetworkState" class="Machine" displayName="$(string.EnableDisplayNetworkState)" explainText="$(string.EnableDisplayNetworkStateExplain)"
        presentation="$(presentation.EnableDisplayNetworkState)" key="Software\Policies\Omnissa\Horizon\Agent\Configuration" valueName="EnableDisplayNetworkState">
           <parentCategory ref="Agent_Configuration" />
           <supportedOn ref="SupportedOn" />
              <enabledValue>
                 <string>true</string>
              </enabledValue>
              <disabledValue>
                 <string>false</string>
              </disabledValue>
              <elements>
                 <decimal id="NetworkWarningInterval_CB" key="Software\Policies\Omnissa\Horizon\Agent\Configuration" valueName="NetworkStateWarningUIInterval" maxValue="60" minValue="1" />
              </elements>
     </policy>

     <policy
        name="DesktopWatermarkConfiguration" class="User" displayName="$(string.Horizon_Watermark_Config)" explainText="$(string.Desktop_Watermark_Configuration_Desc)"
        presentation="$(presentation.watermark_configuration)" key="Software\Policies\Omnissa\Horizon\Agent\Watermark" valueName ="Enabled">
           <parentCategory ref="Horizon_WaterMark" />
           <supportedOn ref="SupportedOn" />
           <elements>
               <multiText id="Text" valueName="Text" />
               <enum id="ImageFit" valueName="ImageFit">
                  <item displayName="$(string.Tile)">
                     <value>
                        <string>Tile</string>
                     </value>
                  </item>
                  <item displayName="$(string.Center)">
                     <value>
                        <string>Center</string>
                     </value>
                  </item>
                  <item displayName="$(string.Multiple)">
                     <value>
                        <string>Multiple</string>
                     </value>
                  </item>
               </enum>
               <decimal id="Rotation" valueName="Rotation" minValue="0" maxValue="360" />
               <decimal id="Opacity" valueName="Opacity" minValue="0" maxValue="255" />
               <decimal id="Margin" valueName="Margin" minValue="0" maxValue="1024" />
               <text id="TextColor" valueName="TextColor" />
               <decimal id="FontSize" valueName="FontSize" minValue="0" maxValue="100" />
               <decimal id="RefreshInterval" valueName="RefreshInterval" minValue="0" maxValue="86400" />
           </elements>
     </policy>
     <policy
        name="BlockScreenCapture" class="Both" displayName="$(string.BlockScreenCapture)" explainText="$(string.BlockScreenCapture_Desc)"
        key="Software\Policies\Omnissa\Horizon\Agent\Configuration" valueName="Screen-capture Blocking"
        presentation="$(presentation.AllowScreenRecording)">
           <parentCategory ref="Agent_Configuration" />
           <supportedOn ref="SupportedOn" />
              <enabledValue>
                 <decimal value="1" />
              </enabledValue>
              <disabledValue>
                 <decimal value="0" />
              </disabledValue>
              <elements>
                 <boolean id="AllowScreenRecording_CB"
                          key="Software\Policies\Omnissa\Horizon\Agent\Configuration"
                          valueName="Allow Screen-recording">
                 <trueValue>
                    <decimal value="1" />
                 </trueValue>
                 <falseValue>
                    <decimal value="0" />
                 </falseValue>
            </boolean>
         </elements>
     </policy>
     <policy
        name="BlockThumbnailRepresentationWhenMinimized" class="Both" displayName="$(string.BlockThumbnailRepresentationWhenMinimized)" explainText="$(string.BlockThumbnailRepresentationWhenMinimized_Desc)"
        key="Software\Policies\Omnissa\Horizon\Agent\Configuration" valueName="Thumbnail Representation Blocking">
           <parentCategory ref="Agent_Configuration" />
           <supportedOn ref="SupportedOn" />
              <enabledValue>
                 <decimal value="1" />
              </enabledValue>
              <disabledValue>
                 <decimal value="0" />
              </disabledValue>
     </policy>
     <policy
        name="ScreenCaptureForMediaOffloaded" class="Both" displayName="$(string.ScreenCaptureForMediaOffloaded)" explainText="$(string.ScreenCaptureForMediaOffloaded_Desc)"
        key="Software\Policies\Omnissa\Horizon\Agent\Configuration" valueName="Screen-capture for Media Offloaded Solution">
           <parentCategory ref="Agent_Configuration" />
           <supportedOn ref="SupportedOn" />
              <enabledValue>
                 <decimal value="1" />
              </enabledValue>
              <disabledValue>
                 <decimal value="0" />
              </disabledValue>
     </policy>
     <policy
        name="AntiKeyLogger" class="Both" displayName="$(string.AntiKeyLogger)" explainText="$(string.AntiKeyLogger_Desc)"
        key="Software\Policies\Omnissa\Horizon\Agent\Configuration" valueName="Key Logger Blocking"
        presentation="$(presentation.AllowConnectionFromWindowsARMClientWithoutAntiKeyLogger)">
           <parentCategory ref="Agent_Configuration" />
           <supportedOn ref="SupportedOn" />
              <enabledValue>
                 <decimal value="1" />
              </enabledValue>
              <disabledValue>
                 <decimal value="0" />
              </disabledValue>
              <elements>
                 <boolean id="AllowConnectionFromWindowsARMClientWithoutAntiKeyLogger_CB"
                          key="Software\Policies\Omnissa\Horizon\Agent\Configuration"
                          valueName="Allow Connections from Horizon Client for Windows without Antikeylogger service if the device is ARM-based">
                    <trueValue>
                       <decimal value="1" />
                    </trueValue>
                    <falseValue>
                       <decimal value="0" />
                    </falseValue>
                 </boolean>
              </elements>
     </policy>
     <policy
        name="BlockSendInput" class="Both" displayName="$(string.BlockSendInput)" explainText="$(string.BlockSendInput_Desc)"
        key="Software\Policies\Omnissa\Horizon\Agent\Configuration" valueName="BlockSendInput">
           <parentCategory ref="Agent_Configuration" />
           <supportedOn ref="SupportedOn" />
              <enabledValue>
                 <decimal value="1" />
              </enabledValue>
              <disabledValue>
                 <decimal value="0" />
              </disabledValue>
     </policy>
     <policy
        name="AllowFIDO2AuthenticatorAccess" class="Both" displayName="$(string.AllowFIDO2AuthenticatorAccess)" explainText="$(string.AllowFIDO2AuthenticatorAccess_Desc)"
        key="Software\Policies\Omnissa\Horizon\Agent\Configuration" valueName="AllowFIDO2AuthenticatorAccess">
           <parentCategory ref="Agent_Configuration" />
           <supportedOn ref="SupportedOn" />
              <enabledValue>
                 <string>true</string>
              </enabledValue>
              <disabledValue>
                 <string>false</string>
              </disabledValue>
     </policy>
     <policy
        name="FIDO2AllowList" class="Machine" displayName="$(string.FIDO2AllowList)" explainText="$(string.FIDO2AllowList_Desc)"
        key="Software\Policies\Omnissa\Horizon\Agent\Configuration" presentation="$(presentation.FIDO2AllowList)">
           <parentCategory ref="Agent_Configuration" />
           <supportedOn ref="SupportedOn" />
              <elements>
                 <text id="FIDO2AllowList_TB" key="Software\Policies\Omnissa\Horizon\Agent\Configuration" valueName="FIDO2AllowList" />
              </elements>
     </policy>
     <policy
         name="WaitForHybridJoin" class="Machine" displayName="$(string.WaitForHybridJoin)" explainText="$(string.WaitForHybridJoin_Desc)"
         key="Software\Policies\Omnissa\Horizon\Agent\Configuration" valueName="WaitForHybridJoin">
            <parentCategory ref="Agent_Configuration" />
            <supportedOn ref="SupportedOn" />
               <enabledValue>
                  <string>1</string>
               </enabledValue>
               <disabledValue>
                  <string>0</string>
               </disabledValue>
      </policy>
      <policy
         name="IpPrefix" class="Machine" displayName="$(string.IpPrefix)" explainText="$(string.IpPrefixDesc)"
         key="Software\Policies\Omnissa\Horizon" presentation="$(presentation.IpPrefix)">
            <parentCategory ref="Hzn_agent_base:Hzn_Agent_Config" />
            <supportedOn ref="SupportedOn" />
              <elements >
                 <text id="IpPrefixTextBox" key="Software\Policies\Omnissa\Horizon" valueName="IpPrefix" />
              </elements>
      </policy>
   </policies>
</policyDefinitions>
