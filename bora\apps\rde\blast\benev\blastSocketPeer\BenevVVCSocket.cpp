/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

#include <string>
#include <sstream>
#include <unordered_map>

#include "vm_assert.h"
#include "userlock.h"
#include "util.h"

#include "vvclib.h"

#include "BenevVVCSocket.h"
#include "BenevUtil.h"


using namespace std;


const char *BenevVVCSocket::sClassName = "BenevVVCSocket";


// wildcardListenerMap: key is name of channel/listener without wildcard.
unordered_map<string, VvcListenerHandle> wildcardListenerMap;
int BenevVVCSocket::sNextChannelNumber = 0;


/*
 *----------------------------------------------------------------------
 *
 * BenevTCPSocket::connectTo --
 *
 *      Connect to the socket peer by doing openning the VVC channel.
 *
 * Results:
 *      A new BenevVVCSocket pointer.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------
 */

BenevVVCSocket *
BenevVVCSocket::connectTo(const string &wildcardListenerName, const string &featureName,
                          int32 sessionId, onConnect *connectCb, const void *connectCbData,
                          onError *errorCb, const void *errorCbData, uint32 channelPriority,
                          uint32 channelFlags)
{
   BenevLog("wildcardListenerName %s, sessionId %d\n", wildcardListenerName.c_str(), sessionId);

   ASSERT(sessionId != VVC_INVALID_SESSIONID);

   VERIFY(wildcardListenerName.length() > 1);
   VERIFY(*wildcardListenerName.rbegin() == '*');

   // XXX Only support wildcard listener name for now
   // XXX TODO these operations need locking
   string name = wildcardListenerName;
   name.erase(name.end() - 1);
   VERIFY(!name.empty() && *name.rbegin() != '*');
   if (wildcardListenerMap.find(name) == wildcardListenerMap.end()) {
      wildcardListenerMap[name] = createVVCListenerForConnect(wildcardListenerName, sessionId);
      VERIFY(wildcardListenerMap[name]);
   } else {
      ASSERT(wildcardListenerMap[name] != NULL);
   }

   ++sNextChannelNumber;

   stringstream chNameSS;
   chNameSS << name << sNextChannelNumber;

   /* XXX Only support the RECV_BUFFER option */
   channelFlags |= VVC_CHANNEL_EXTENSION_RECV_BUFFER;

   BenevVVCSocket *socket = new BenevVVCSocket(wildcardListenerMap[name], sessionId, chNameSS.str(),
                                               featureName, connectCb, connectCbData, errorCb,
                                               errorCbData, channelPriority, channelFlags);

   // Open channel right away
   VvcChannelEvents channelEvents = {
      channelOnOpenCb,         // onOpen
      channelOnCloseCb,        // onClose
      channelOnSendCompleteCb, // onSendComplete
      NULL,                    // onDelivered
      channelOnRecvCb,         // onRecv
      NULL,                    // onPause
      NULL                     // onResume
   };

   uint32 flags = channelFlags | VVC_CHANNEL_CONNECT_SESSION_ID;
   VvcStatus status =
      VVCLIB_OpenChannel(socket->mListener, const_cast<char *>(socket->mChannelName.c_str()),
                         (void *)(intptr_t)sessionId, &channelEvents, channelPriority,
                         -1, // timeout
                         flags,
                         NULL,   // initialData
                         0,      // initialDataLen
                         socket, // cbData
                         &socket->mChannelId);
   VERIFY(VVC_SUCCESS(status));

   return socket;
}


/*
 *----------------------------------------------------------------------
 *
 * BenevTCPSocket::acceptPeerOpenedChannel --
 *
 *      Accept a peer-opened channel.
 *
 * Results:
 *      A new BenevVVCSocket pointer.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------
 */

BenevVVCSocket *
BenevVVCSocket::acceptPeerOpenedChannel(VvcListenerHandle listener, int32 sessionId,
                                        const char *channelName, const char *featureName,
                                        void *connCookie, onConnect *connectCb, void *cbData,
                                        onError *errorCb, const void *errorCbData,
                                        uint32 channelPriority, uint32 channelFlags)
{
   BenevLog("channelName %s, sessionId %d, channelPriority %08x"
            "channelFlags %08x\n",
            channelName, sessionId, channelPriority, channelFlags);

   ASSERT(channelName);

   /* XXX Only support the RECV_BUFFER option */
   channelFlags |= VVC_CHANNEL_EXTENSION_RECV_BUFFER;

   VvcChannelEvents channelEvents = {
      channelOnOpenCb,         // onOpen
      channelOnCloseCb,        // onClose
      channelOnSendCompleteCb, // onSendComplete
      NULL,                    // onDelivered
      channelOnRecvCb,         // onRecv
      NULL,                    // onPause
      NULL                     // onResume
   };

   BenevVVCSocket *socket =
      new BenevVVCSocket(listener, sessionId, channelName, featureName, connectCb, cbData, errorCb,
                         errorCbData, channelPriority, channelFlags);
   /* consider the socket already connecte */
   socket->mState = SocketConnected;

   VvcStatus status = VVCLIB_AcceptChannel(connCookie, channelFlags, &channelEvents,
                                           NULL,           // initialData
                                           0,              // initialDataLen
                                           (void *)socket, // clientData
                                           &socket->mChannelId);

   VERIFY(VVC_SUCCESS(status));

   return socket;
}


/*
 *----------------------------------------------------------------------
 *
 * BenevTCPSocket::channelOnOpenCb --
 *
 *      The onOpen callback of the VVC listener events.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------
 */

void
BenevVVCSocket::channelOnOpenCb(VvcChannelHandle channel, VvcStatus status, uint8 *initialData,
                                size_t initialDataLen, void *clientData)
{
   BenevDebug("status %d channel %p\n", status, channel);

   VERIFY(VVC_SUCCESS(status) || VVC_SUCCESS_RAW_CHAN_OPEN(status));

   BenevVVCSocket *socket = reinterpret_cast<BenevVVCSocket *>(clientData);

   if (VVC_SUCCESS_RAW_CHAN_OPEN(status)) {
      socket->mRawChannel = true;
   }

   socket->channelOpened(channel, initialData, initialDataLen);
}


/*
 *----------------------------------------------------------------------
 *
 * BenevTCPSocket::channelOnCloseCb --
 *
 *      The onClose callback of the VVC listener events.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------
 */

void
BenevVVCSocket::channelOnCloseCb(VvcChannelHandle channel, VvcCloseChannelReason reason,
                                 void *clientData)
{
   BenevDebug("channel %p, reason %d\n", channel, reason);

   BenevVVCSocket *socket = reinterpret_cast<BenevVVCSocket *>(clientData);
   socket->channelClosed(channel, reason);
}


/*
 *----------------------------------------------------------------------
 *
 * BenevTCPSocket::channelOnRecvCb --
 *
 *      The onRecv callback of the VVC listener events.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------
 */

void
BenevVVCSocket::channelOnRecvCb(VvcChannelHandle channelHandle, uint32 flags, uint8 *buf,
                                size_t len, void *clientData)
{
   BenevTrace("channel %p, buf %p, len %" FMTSZ "d\n", channelHandle, buf, len);

   BenevVVCSocket *socket = reinterpret_cast<BenevVVCSocket *>(clientData);
   ASSERT(socket->mChannel == channelHandle);

   /* VVC code seems to indicate that "flags" is always zero */
   ASSERT(flags == 0);
   socket->dataReceived(buf, len);
}


/*
 *----------------------------------------------------------------------
 *
 * BenevTCPSocket::channelOnSendCompleteCb --
 *
 *      The onSendComplete callback of the VVC listener events.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------
 */

void
BenevVVCSocket::channelOnSendCompleteCb(VvcChannelHandle channelHandle, VvcStatus status,
                                        uint8 *buf, size_t len, void *clientData,
                                        void *msgClientData, uint32 msgId)
{
   if (!VVC_SUCCESS(status)) {
      BenevDebug("status %d (unsuccessful), buf %p, len %" FMTSZ "d, msgId %u\n", status, buf, len,
                 msgId);
   } else {
      BenevTrace("status %d, buf %p, len %" FMTSZ "d, msgId %u\n", status, buf, len, msgId);
   }

   BenevVVCSocket *socket = reinterpret_cast<BenevVVCSocket *>(clientData);
   ASSERT(socket->mChannel == channelHandle);

   SendCtx *sendCtx = reinterpret_cast<SendCtx *>(msgClientData);

   socket->dataSent(buf, len, sendCtx);
}


/*
 *----------------------------------------------------------------------
 *
 * BenevTCPSocket::BenevTCPSocket --
 *
 *      The constructor.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------
 */

BenevVVCSocket::BenevVVCSocket(VvcListenerHandle listener, int32 sessionId,
                               const string &channelName, string featureName, onConnect *connectCb,
                               const void *connectCbData, onError *errorCb, const void *errorCbData,
                               uint32 channelPriority, uint32 channelFlags) :
   BenevSocket(connectCb, connectCbData, errorCb, errorCbData),
   mListener(listener),
   mSessionId(sessionId),
   mChannelName(channelName),
   mFeatureName(std::move(featureName)),
   mChannelFlags(channelFlags),
   mChannelPriority(channelPriority),
   mChannelId(0),
   mChannel(NULL),
   mLock(NULL),
   mRecvCb(NULL),
   mRecvCbData(NULL),
   mRecvBuf(NULL),
   mRecvBufLen(0),
   mRawChannel(false)
{
   mLock = MXUser_CreateExclLock("BenevVVCSocket", RANK_UNRANKED);
}


/*
 *----------------------------------------------------------------------
 *
 * BenevTCPSocket::BenevTCPSocket --
 *
 *      The destructor.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------
 */

BenevVVCSocket::~BenevVVCSocket()
{
   MXUser_DestroyExclLock(mLock);
}


/*
 *----------------------------------------------------------------------
 *
 * BenevTCPSocket::recvData --
 *
 *      Public function to receive data from the socket.
 *
 * Results:
 *      true if successful, false otherwise.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------
 */

bool
BenevVVCSocket::recvData(void *buf, int bufLen, onRecv *recvCb, const void *cbData)
{
   BenevTrace("buf %p, bufLen %d\n", buf, bufLen);

   MXUser_AcquireExclLock(mLock);

   if (mState != SocketConnected || mChannel == NULL) {
      MXUser_ReleaseExclLock(mLock);
      return false;
   }

   ASSERT(buf && bufLen);

   /* Not supporting multiple receives */
   VERIFY(mRecvBuf == NULL && mRecvCb == NULL);

   mRecvBuf = static_cast<uint8 *>(buf);
   mRecvBufLen = bufLen;
   mRecvCb = recvCb;
   mRecvCbData = cbData;

   MXUser_ReleaseExclLock(mLock);

   VVCLIB_RecvBuffer(mChannel, mRecvBuf, bufLen, 1);

   return true;
}


/*
 *----------------------------------------------------------------------
 *
 * BenevTCPSocket::sendData --
 *
 *      Public function to send data out of the socket.
 *
 * Results:
 *      true if successful, false otherwise.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------
 */

bool
BenevVVCSocket::sendData(const void *buf, int bufLen, onSent *sentCb, const void *cbData)
{
   int result = true;

   BenevTrace("buf %p bufLen %d\n", buf, bufLen);

   MXUser_AcquireExclLock(mLock);

   if (mState != SocketConnected || mChannel == NULL) {
      MXUser_ReleaseExclLock(mLock);
      return false;
   }

   MXUser_ReleaseExclLock(mLock);

   ASSERT(buf && bufLen);

   SendCtx *sendCtx = new SendCtx(buf, bufLen, sentCb, cbData);

   VVCLIB_Send(mChannel,
               0, // flags
               0, // reserved
               const_cast<uint8 *>(static_cast<const uint8 *>(buf)), bufLen,
               sendCtx, // msgClientData
               NULL);   // msgId

   return result;
}


/*
 *----------------------------------------------------------------------
 *
 * BenevTCPSocket::closeSocket --
 *
 *      Close the socket.
 *
 * Results:
 *      true if successful, false otherwise.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------
 */

void
BenevVVCSocket::closeSocket()
{
   BenevLog("socket %d: closing\n", getId());

   MXUser_AcquireExclLock(mLock);

   if (mState == SocketClosing || mState == SocketClosed) {
      MXUser_ReleaseExclLock(mLock);
      return;
   }
   mState = SocketClosing;

   // reset all the callbacks
   mConnectCb = NULL;
   mConnectCbData = NULL;
   mRecvCb = NULL;
   mRecvCbData = NULL;
   mRecvBuf = NULL;
   mRecvBufLen = 0;

   VvcChannelHandle channel = mChannel;

   MXUser_ReleaseExclLock(mLock);

   if (channel != NULL) {
      BenevLog("Closing channel and waiting for close callback\n");

      VVCLIB_CloseChannel(channel, VvcCloseChannelNormal);

      // XXX FIXME Busy wait for close callback - this should be async.
      MXUser_AcquireExclLock(mLock);
      while (mChannel != NULL) {
         MXUser_ReleaseExclLock(mLock);
         Util_Usleep(50 * 1000);
         MXUser_AcquireExclLock(mLock);
      }
      MXUser_ReleaseExclLock(mLock);
   } else {
      BenevLog("Channel already closed.\n");
   }

   mState = SocketClosed;

   if (mListener != NULL) {
      /*
       * Can't close listener here because the listener may be shared. Will need
       * to wrap it in a ref-counted object to manage its lifetime.
       */
      // XXX Leak the listener.
      // VvcStatus status = VVCLIB_CloseListener(mListener);
      // VERIFY(VVC_SUCCESS(status));
      // mListener = NULL;
   }

   BenevLog("socket %d: closed\n", getId());
}


/*
 *----------------------------------------------------------------------
 *
 * BenevTCPSocket::getRawChannelStatus --
 *
 *      Check if raw channel was created or regular channel was created.
 *
 * Results:
 *      true if raw channel was created, false otherwise.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------
 */

bool
BenevVVCSocket::getRawChannelStatus()
{
   return mRawChannel;
}


/*
 *----------------------------------------------------------------------
 *
 * BenevTCPSocket::channelOpened --
 *
 *      The VVC channel is opened (in response to a previous OpenChan
 *      request). Fire the socket onConnect callback.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------
 */

void
BenevVVCSocket::channelOpened(VvcChannelHandle channel, uint8 *initialData, size_t initialDataLen)
{

   MXUser_AcquireExclLock(mLock);
   mState = SocketConnected;
   mChannel = channel;
   MXUser_ReleaseExclLock(mLock);

   VVCLIB_SetFeatureName(mListener, mChannel, mFeatureName.c_str());

   if (initialDataLen != 0 && initialData != NULL) {
      /* TODO handle initial data */
      VERIFY(FALSE);
   }

   if (mConnectCb) {
      mConnectCb(this, const_cast<void *>(mConnectCbData));
   }
}


/*
 *----------------------------------------------------------------------
 *
 * BenevTCPSocket::dataReceived --
 *
 *      Received some data, now send it out of the other socket.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------
 */

void
BenevVVCSocket::dataReceived(uint8 *buf, size_t len)
{
   BenevTrace("buf %p len %" FMTSZ "d\n", buf, len);

   MXUser_AcquireExclLock(mLock);

   ASSERT(buf == mRecvBuf);
   ASSERT(mRecvCb);

   onRecv *cb = mRecvCb;
   void *cbData = const_cast<void *>(mRecvCbData);

   mRecvBuf = NULL;
   mRecvBufLen = 0;
   mRecvCb = NULL;
   mRecvCbData = NULL;

   MXUser_ReleaseExclLock(mLock);

   cb(buf, len, this, cbData);
}


void
BenevVVCSocket::dataSent(uint8 *buf, size_t len, SendCtx *sendCtx)
{
   BenevTrace("buf %p len %" FMTSZ "d\n", buf, len);

   ASSERT(buf == sendCtx->mSendBuf);
   ASSERT(len <= sendCtx->mSendBufLen);

   onSent *cb = sendCtx->mSentCb;
   void *cbData = const_cast<void *>(sendCtx->mSentCbData);
   delete sendCtx;

   if (cb) {
      cb(buf, len, this, cbData);
   }
}


/*
 *----------------------------------------------------------------------
 *
 * BenevTCPSocket::channelClosed
 *
 *      Channel is closed either due to us closing the socket, or network/socket
 *      error, or peer close.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------
 */

void
BenevVVCSocket::channelClosed(VvcChannelHandle channel, VvcCloseChannelReason reason)
{
   ASSERT(mChannel == channel);

   bool notify = false;

   MXUser_AcquireExclLock(mLock);
   mChannel = NULL;
   if (mState != SocketClosing) {
      notify = true;
   }
   MXUser_ReleaseExclLock(mLock);

   if (notify) {
      if (mErrorCb) {
         mErrorCb(-4, this, const_cast<void *>(mErrorCbData));
      }
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * BenevVVCSocket::createVVCListenerForConnect --
 *
 *      Create a VVC listener for OpenChannel
 *
 *      Confusingly, VVC requires a (VVC) "listener" even for a client (that
 *      does OpenChannel) before it can connect. The vvcListener here is needed
 *      to do a OpenChannel to the upstream Benev Peer. Caller should already
 *      have established the VVC Session before calling this.
 *
 * Results:
 *      The VvcListenerHandle.
 *
 * Side effects:
 *      None.
 *-----------------------------------------------------------------------------
 */

VvcListenerHandle
BenevVVCSocket::createVVCListenerForConnect(const string &channelName, int32 sessionId)
{
   VvcListenerHandle listener = NULL;

   /*
    * Although not strictly necessary, channelName should be a wildcard channel
    * (ending with a single '*') in order for the same listener to be used to
    * connect multiple VVCSockets.
    */
   string listenerName(channelName);
   ASSERT(listenerName.length() > 1);
   ASSERT(*listenerName.rbegin() == '*');

   VvcListenerEvents vvcListenerEvents = {
      vvcSessionConnected,  // onConnect
      vvcPeerOpenedChannel, // onPeerOpen
      vvcListenerClosed,    // onClose
      NULL                  // onDisconnect
   };

   VvcStatus status = VVCLIB_CreateListener(VVC_PLUGIN_ID_CORE_PROTOCOL, sessionId,
                                            const_cast<char *>(listenerName.c_str()),
                                            &vvcListenerEvents, NULL, &listener);
   VERIFY(VVC_SUCCESS(status));

   status = VVCLIB_ActivateListener(listener);
   VERIFY(VVC_SUCCESS(status));

   return listener;
}


/*
 *----------------------------------------------------------------------
 *
 * BenevTCPSocket::vvcSessionConnected
 *
 *      The VVC session is established. We don't really care about this.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------
 */

void
BenevVVCSocket::vvcSessionConnected(char *name, VvcListenerHandle listenerHandle,
                                    void *connectionCookie, uint32 connectionCaps, int32 sessionId,
                                    void *clientData)
{
   BenevLog("name: %s, connectionCaps: %u, sessionId %d\n", name, connectionCaps, sessionId);
}


/*
 *----------------------------------------------------------------------
 *
 * BenevTCPSocket::vvcSessionConnected
 *
 *      The VVC peer opened a new channel. See comments inside - we don't handle
 *      this "weird" situation.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------
 */

void
BenevVVCSocket::vvcPeerOpenedChannel(char *name, VvcListenerHandle listenerHandle,
                                     void *connectionCookie, uint32 connectionCaps, int32 sessionId,
                                     uint8 *initialData, size_t initialDataLen, void *clientData)
{
   BenevLog("name: %s, connectionCaps: %u, sessionId %d\n", name, connectionCaps, sessionId);

   /*
    * We're the client to the upstream BENEV peer, don't know what to do if
    * upstream decides to connect to us.
    */
   VERIFY(FALSE);
}


/*
 *----------------------------------------------------------------------
 *
 * BenevTCPSocket::vvcListenerClosed --
 *
 *      The VVC listener is closed.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------
 */

void
BenevVVCSocket::vvcListenerClosed(VvcListenerHandle listenerHandle, void *clientData)
{
   BenevLog("listener %p\n", listenerHandle);
}
