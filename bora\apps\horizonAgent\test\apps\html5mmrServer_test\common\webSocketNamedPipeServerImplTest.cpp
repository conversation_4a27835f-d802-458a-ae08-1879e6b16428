/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

#include "webSocketNamedPipeServerImplTest.h"
// Gets executed once at the start of the test suite
void
WebSocketNamedPipeServerImplTest::SetUpTestCase()
{
   UTConsoleLog("%s", __FUNCTION__);
}

// Gets executed once at the end of the test suite
void
WebSocketNamedPipeServerImplTest::TearDownTestCase()
{
   UTConsoleLog("%s", __FUNCTION__);
}

// Gets executed before each unit test
void
WebSocketNamedPipeServerImplTest::SetUp()
{
   // Mock apis in current class and base class
   VMOCK(GetCurrentProcessId).ExpectCall().WillOnce(Return(mockProcessId));
   VMOCK(ProcessIdToSessionId).ExpectCall(_, _).WillOnce(Invoke([this](DWORD, LPDWORD pSessionId) {
      *pSessionId = mockSessionId;
      return TRUE;
   }));

   VMOCK(IsTeamsHelperSupported).Times(AtMost(1)).WillRepeatedly(Return(True));
   mockNamedPipeServer = std::make_shared<MockWebSocketNamedPipeServerImpl>(mockPipeName, false);
   mockNamedPipeServer->mServerName = mockServerName;
   UTConsoleLog("%s", __FUNCTION__);
}

void
WebSocketNamedPipeServerImplTest::TearDown()
{
   VMOCK(&WebSocketServerImpl::StopServer).WillOnce(Return(True));
   mockNamedPipeServer.reset();
   UTConsoleLog("%s", __FUNCTION__);
}

TEST_F(WebSocketNamedPipeServerImplTest, Test_IsNamedPipe)
{
   // Test case for IsNamedPipe
   EXPECT_TRUE(mockNamedPipeServer->IsNamedPipe());
}

TEST_F(WebSocketNamedPipeServerImplTest, Test_StartServer_ThreadFailure)
{
   mockNamedPipeServer->mImplicitPoll = True;
   VMOCK(&VMThread::IsRunning).WillOnce(Return(False));
   VMOCK(&VMThread::Start).ExpectCall(_, _, _, _).WillOnce(Invoke([](void *, void *, int, bool) {
      return false;
   }));
   VMOCK(AsyncSocket_CreateNamedPipe).Times(0);

   // Call the method under test
   EXPECT_FALSE(mockNamedPipeServer->StartServer());
}

TEST_F(WebSocketNamedPipeServerImplTest, Test_StartServer_ThreadRunning_SocketFailure)
{
   mockNamedPipeServer->mImplicitPoll = true;
   VMOCK(&VMThread::IsRunning).WillOnce(Return(True));
   VMOCK(&VMThread::Start).Times(0);

   VMOCK(AsyncSocket_CreateNamedPipe)
      .ExpectCall(StrEq(mockServerName.c_str()), _, mockNamedPipeServer.get(), _, _, 2,
                  ASOCK_NAMEDPIPE_ALLOW_DEFAULT, NULL, _)
      .WillOnce(Return(reinterpret_cast<AsyncSocket *>(nullptr)));

   // Call the method under test
   EXPECT_FALSE(mockNamedPipeServer->StartServer());
}

TEST_F(WebSocketNamedPipeServerImplTest, Test_StartServer_Success)
{
   mockNamedPipeServer->mImplicitPoll = true;
   VMOCK(&VMThread::IsRunning).WillOnce(Return(False));
   VMOCK(&VMThread::Start).ExpectCall(_, _, _, _).WillOnce(Invoke([](void *, void *, int, bool) {
      return True;
   }));

   VMOCK(AsyncSocket_CreateNamedPipe)
      .ExpectCall(StrEq(mockServerName.c_str()), _, mockNamedPipeServer.get(), _, _, 2,
                  ASOCK_NAMEDPIPE_ALLOW_DEFAULT, NULL, _)
      .WillOnce(Return(reinterpret_cast<AsyncSocket *>(0x1234)));

   // Call the method under test
   EXPECT_TRUE(mockNamedPipeServer->StartServer());
}

// ****** Unit tests for all Extern functions  *******
TEST_F(WebSocketNamedPipeServerImplTest, Test_IsKnownCode)
{
   // Test known opcodes
   EXPECT_TRUE(IsKnownCode(FRAME_OPCODE_CONTINUATION));
   EXPECT_TRUE(IsKnownCode(FRAME_OPCODE_TEXT));
   EXPECT_TRUE(IsKnownCode(FRAME_OPCODE_BINARY));
   EXPECT_TRUE(IsKnownCode(FRAME_OPCODE_CLOSE));
   EXPECT_TRUE(IsKnownCode(FRAME_OPCODE_PING));
   EXPECT_TRUE(IsKnownCode(FRAME_OPCODE_PONG));

   // Test invalid opcodes
   EXPECT_FALSE(IsKnownCode(0xFF)); // Random invalid value
   EXPECT_FALSE(IsKnownCode(0x7F)); // Another random invalid value
}

TEST_F(WebSocketNamedPipeServerImplTest, Test_HashWebHttpAcceptString_Base64EncodeFailure)
{
   // Test Base64_Encode failure scenario
   std::string testKey = "testKey";

   VMOCK(SHA1Init).ExpectCall(_);
   VMOCK(SHA1Update).ExpectCall(_, _, _);
   VMOCK(SHA1Final).ExpectCall(_, _);
   VMOCK(Base64_EncodedLength).ExpectCall(_, _).WillOnce(Return(10));
   VMOCK(Base64_Encode).WillOnce(Return(false));

   // Base64_Encode failure should result in empty string
   EXPECT_TRUE(HashWebHttpAcceptString(testKey).empty());
}

TEST_F(WebSocketNamedPipeServerImplTest, Test_HashWebHttpAcceptString_SuccessPath)
{
   // Test successful path with mocked internals
   std::string testKey = "dGhlIHNhbXBsZSBub25jZQ==";
   std::string expectedHash = "s3pPLMBiTxaQ9kYGzzhZRbK+xOo=";

   VMOCK(SHA1Init).ExpectCall(_);
   VMOCK(SHA1Update).ExpectCall(_, _, _);
   VMOCK(SHA1Final).ExpectCall(_, _).WillOnce(
      Invoke([](unsigned char *digest, SHA1_CTX *) { memset(digest, 0x12, SHA1_HASH_LEN); }));

   VMOCK(Base64_EncodedLength).ExpectCall(_, _).WillOnce(Return(40));
   VMOCK(Base64_Encode)
      .WillOnce(Invoke([&expectedHash](const void *, size_t, char *dst, size_t, size_t *encLen) {
         strcpy(dst, expectedHash.c_str());
         *encLen = expectedHash.length();
         return true;
      }));

   EXPECT_EQ(HashWebHttpAcceptString(testKey), expectedHash);
}

TEST_F(WebSocketNamedPipeServerImplTest, Test_CreateEncodeContext)
{
   auto randomSeed = reinterpret_cast<rqContext *>(0x5678);
   void *voidPtr = static_cast<void *>(randomSeed);
   VMOCK(Random_QuickSeed).ExpectCall(_).WillOnce(Return(randomSeed));

   // Verify the return value is what we expect from our mock
   EXPECT_TRUE(CreateEncodeContext() == voidPtr);
}

TEST_F(WebSocketNamedPipeServerImplTest, Test_EncodeText_Success)
{
   std::string testText = "Hello WebSocket";
   uint8 *outBuf = nullptr;
   uint32 outSize = 0;
   void *mockContext = reinterpret_cast<void *>(0x1234);

   // Mock WebSocketConnImpl::EncodeToBuffer to simulate success
   VMOCK(&WebSocketConnImpl::EncodeToBuffer)
      .ExpectCall(FRAME_OPCODE_TEXT,
                  static_cast<const uint8 *>(reinterpret_cast<const void *>(testText.c_str())),
                  testText.size(), TRUE, _, _, mockContext)
      .WillOnce(
         Invoke([&](uint8, const uint8 *, size_t, Bool, uint8 **buf, uint32 *totalSize, void *) {
            *buf = new uint8[10]; // Allocate some buffer
            *totalSize = 10;
            return TRUE;
         }));

   // Call the function under test
   EXPECT_TRUE(EncodeText(testText, &outBuf, &outSize, mockContext));

   // Verify output parameters were set correctly
   EXPECT_NE(outBuf, nullptr);
   EXPECT_EQ(outSize, 10);

   // Clean up the allocated buffer
   delete[] outBuf;
}

TEST_F(WebSocketNamedPipeServerImplTest, Test_EncodeText_Failure)
{
   std::string testText = "Hello WebSocket";
   uint8 *outBuf = nullptr;
   uint32 outSize = 0;
   void *mockContext = reinterpret_cast<void *>(0x1234);

   // Mock WebSocketConnImpl::EncodeToBuffer to simulate failure
   VMOCK(&WebSocketConnImpl::EncodeToBuffer)
      .ExpectCall(FRAME_OPCODE_TEXT,
                  static_cast<const uint8 *>(reinterpret_cast<const void *>(testText.c_str())),
                  testText.size(), TRUE, _, _, mockContext)
      .WillOnce(Return(FALSE));

   // Call the function under test
   EXPECT_FALSE(EncodeText(testText, &outBuf, &outSize, mockContext));

   // Output parameters should remain unchanged
   EXPECT_EQ(outBuf, nullptr);
   EXPECT_EQ(outSize, 0);
}
TEST_F(WebSocketNamedPipeServerImplTest, Test_EncodeBinary_Success)
{
   uint8 testData[] = {0x01, 0x02, 0x03, 0x04, 0x05};
   uint32 testDataSize = sizeof(testData);
   uint8 *outBuf = nullptr;
   uint32 outSize = 0;
   void *mockContext = reinterpret_cast<void *>(0x1234);

   // Mock WebSocketConnImpl::EncodeToBuffer to simulate success
   VMOCK(&WebSocketConnImpl::EncodeToBuffer)
      .ExpectCall(FRAME_OPCODE_BINARY, testData, testDataSize, TRUE, _, _, mockContext)
      .WillOnce(
         Invoke([&](uint8, const uint8 *, size_t, Bool, uint8 **buf, uint32 *totalSize, void *) {
            *buf = new uint8[15]; // Allocate some buffer
            *totalSize = 15;
            return TRUE;
         }));

   // Call the function under test
   EXPECT_TRUE(EncodeBinary(testData, testDataSize, &outBuf, &outSize, mockContext));

   // Verify output parameters were set correctly
   EXPECT_NE(outBuf, nullptr);
   EXPECT_EQ(outSize, 15);

   // Clean up the allocated buffer
   delete[] outBuf;
}

TEST_F(WebSocketNamedPipeServerImplTest, Test_EncodeBinary_Failure)
{
   uint8 testData[] = {0x01, 0x02, 0x03, 0x04, 0x05};
   uint32 testDataSize = sizeof(testData);
   uint8 *outBuf = nullptr;
   uint32 outSize = 0;
   void *mockContext = reinterpret_cast<void *>(0x1234);

   // Mock WebSocketConnImpl::EncodeToBuffer to simulate failure
   VMOCK(&WebSocketConnImpl::EncodeToBuffer)
      .ExpectCall(FRAME_OPCODE_BINARY, testData, testDataSize, TRUE, _, _, mockContext)
      .WillOnce(Return(FALSE));

   // Call the function under test
   EXPECT_FALSE(EncodeBinary(testData, testDataSize, &outBuf, &outSize, mockContext));

   // Output parameters should remain unchanged
   EXPECT_EQ(outBuf, nullptr);
   EXPECT_EQ(outSize, 0);
}

TEST_F(WebSocketNamedPipeServerImplTest, Test_EncodeFragmentBinary_Begin)
{
   uint8 testData[] = {0x01, 0x02, 0x03, 0x04, 0x05};
   uint32 testDataSize = sizeof(testData);
   uint8 *outBuf = nullptr;
   uint32 outSize = 0;
   void *mockContext = reinterpret_cast<void *>(0x1234);

   // Mock WebSocketConnImpl::EncodeToBuffer to simulate success for
   // FRAGMENT_BEGIN
   VMOCK(&WebSocketConnImpl::EncodeToBuffer)
      .ExpectCall(FRAME_OPCODE_BINARY, testData, testDataSize, FALSE, _, _, mockContext)
      .WillOnce(
         Invoke([&](uint8, const uint8 *, size_t, Bool, uint8 **buf, uint32 *totalSize, void *) {
            *buf = new uint8[15];
            *totalSize = 15;
            return TRUE;
         }));

   // Call the function under test
   EXPECT_TRUE(
      EncodeFragmentBinary(FRAGMENT_BEGIN, testData, testDataSize, &outBuf, &outSize, mockContext));

   // Verify output parameters
   EXPECT_NE(outBuf, nullptr);
   EXPECT_EQ(outSize, 15);

   delete[] outBuf;
}

TEST_F(WebSocketNamedPipeServerImplTest, Test_EncodeFragmentBinary_Cont)
{
   uint8 testData[] = {0x06, 0x07, 0x08};
   uint32 testDataSize = sizeof(testData);
   uint8 *outBuf = nullptr;
   uint32 outSize = 0;
   void *mockContext = reinterpret_cast<void *>(0x1234);

   // Mock for continuation fragment
   VMOCK(&WebSocketConnImpl::EncodeToBuffer)
      .ExpectCall(FRAME_OPCODE_CONTINUATION, testData, testDataSize, FALSE, _, _, mockContext)
      .WillOnce(
         Invoke([&](uint8, const uint8 *, size_t, Bool, uint8 **buf, uint32 *totalSize, void *) {
            *buf = new uint8[10];
            *totalSize = 10;
            return TRUE;
         }));

   EXPECT_TRUE(
      EncodeFragmentBinary(FRAGMENT_CONT, testData, testDataSize, &outBuf, &outSize, mockContext));

   EXPECT_NE(outBuf, nullptr);
   EXPECT_EQ(outSize, 10);

   delete[] outBuf;
}

TEST_F(WebSocketNamedPipeServerImplTest, Test_EncodeFragmentBinary_End)
{
   uint8 testData[] = {0x09, 0x0A};
   uint32 testDataSize = sizeof(testData);
   uint8 *outBuf = nullptr;
   uint32 outSize = 0;
   void *mockContext = reinterpret_cast<void *>(0x1234);

   // Mock for final fragment
   VMOCK(&WebSocketConnImpl::EncodeToBuffer)
      .ExpectCall(FRAME_OPCODE_CONTINUATION, testData, testDataSize, TRUE, _, _, mockContext)
      .WillOnce(
         Invoke([&](uint8, const uint8 *, size_t, Bool, uint8 **buf, uint32 *totalSize, void *) {
            *buf = new uint8[8];
            *totalSize = 8;
            return TRUE;
         }));

   EXPECT_TRUE(
      EncodeFragmentBinary(FRAGMENT_END, testData, testDataSize, &outBuf, &outSize, mockContext));

   EXPECT_NE(outBuf, nullptr);
   EXPECT_EQ(outSize, 8);

   delete[] outBuf;
}

TEST_F(WebSocketNamedPipeServerImplTest, Test_EncodeFragmentBinary_Failure)
{
   uint8 testData[] = {0x01, 0x02, 0x03};
   uint32 testDataSize = sizeof(testData);
   uint8 *outBuf = nullptr;
   uint32 outSize = 0;
   void *mockContext = reinterpret_cast<void *>(0x1234);

   // Mock failure case
   VMOCK(&WebSocketConnImpl::EncodeToBuffer)
      .ExpectCall(FRAME_OPCODE_BINARY, testData, testDataSize, FALSE, _, _, mockContext)
      .WillOnce(Return(FALSE));

   EXPECT_FALSE(
      EncodeFragmentBinary(FRAGMENT_BEGIN, testData, testDataSize, &outBuf, &outSize, mockContext));

   EXPECT_EQ(outBuf, nullptr);
   EXPECT_EQ(outSize, 0);
}

TEST_F(WebSocketNamedPipeServerImplTest, Test_EncodeFragmentBinary_NullData)
{
   uint32 testDataSize = 10;
   uint8 *outBuf = nullptr;
   uint32 outSize = 0;
   void *mockContext = reinterpret_cast<void *>(0x1234);

   // Mock for null data pointer
   VMOCK(&WebSocketConnImpl::EncodeToBuffer)
      .ExpectCall(FRAME_OPCODE_BINARY, nullptr, testDataSize, FALSE, _, _, mockContext)
      .WillOnce(Return(FALSE));

   EXPECT_FALSE(
      EncodeFragmentBinary(FRAGMENT_BEGIN, nullptr, testDataSize, &outBuf, &outSize, mockContext));

   EXPECT_EQ(outBuf, nullptr);
   EXPECT_EQ(outSize, 0);
}

TEST_F(WebSocketNamedPipeServerImplTest, Test_EncodeFragmentBinary_ZeroSize)
{
   uint8 testData[] = {0x01, 0x02, 0x03};
   uint8 *outBuf = nullptr;
   uint32 outSize = 0;
   void *mockContext = reinterpret_cast<void *>(0x1234);

   // Mock for zero data size
   VMOCK(&WebSocketConnImpl::EncodeToBuffer)
      .ExpectCall(FRAME_OPCODE_BINARY, testData, 0, FALSE, _, _, mockContext)
      .WillOnce(Return(TRUE));

   EXPECT_TRUE(EncodeFragmentBinary(FRAGMENT_BEGIN, testData, 0, &outBuf, &outSize, mockContext));
}

TEST_F(WebSocketNamedPipeServerImplTest, Test_EncodeFragmentBinary_ContFragmentEdgeCases)
{
   uint8 testData[] = {0x41, 0x42, 0x43};
   uint32 testDataSize = sizeof(testData);
   uint8 *outBuf = nullptr;
   uint32 outSize = 0;
   void *mockContext = reinterpret_cast<void *>(0x1234);

   VMOCK(&WebSocketConnImpl::EncodeToBuffer)
      .ExpectCall(FRAME_OPCODE_CONTINUATION, testData, testDataSize, FALSE, _, _, mockContext)
      .WillOnce(
         Invoke([&](uint8, const uint8 *, size_t, Bool, uint8 **buf, uint32 *totalSize, void *) {
            // Simulate a small frame
            *buf = new uint8[5];
            *totalSize = 5;
            return TRUE;
         }));

   EXPECT_TRUE(
      EncodeFragmentBinary(FRAGMENT_CONT, testData, testDataSize, &outBuf, &outSize, mockContext));

   EXPECT_NE(outBuf, nullptr);
   EXPECT_EQ(outSize, 5);

   delete[] outBuf;
}

TEST_F(WebSocketNamedPipeServerImplTest, Test_EncodeFragmentBinary_EndWithLargePayload)
{
   // Create a relatively large payload to test the end fragment handling
   const uint32 testDataSize = 1024;
   uint8 *testData = new uint8[testDataSize];
   for (uint32 i = 0; i < testDataSize; i++) {
      testData[i] = static_cast<uint8>(i % 256);
   }

   uint8 *outBuf = nullptr;
   uint32 outSize = 0;
   void *mockContext = reinterpret_cast<void *>(0x1234);

   VMOCK(&WebSocketConnImpl::EncodeToBuffer)
      .ExpectCall(FRAME_OPCODE_CONTINUATION, testData, testDataSize, TRUE, _, _, mockContext)
      .WillOnce(
         Invoke([&](uint8, const uint8 *, size_t, Bool, uint8 **buf, uint32 *totalSize, void *) {
            // Simulate a frame with header overhead + payload
            *buf = new uint8[testDataSize + 14];
            *totalSize = testDataSize + 14; // 14 is typical WebSocket frame overhead
            return TRUE;
         }));

   EXPECT_TRUE(
      EncodeFragmentBinary(FRAGMENT_END, testData, testDataSize, &outBuf, &outSize, mockContext));

   EXPECT_NE(outBuf, nullptr);
   EXPECT_EQ(outSize, testDataSize + 14);

   delete[] testData;
   delete[] outBuf;
}

TEST_F(WebSocketNamedPipeServerImplTest, Test_SetNextParitialBufferSize_NotPayload)
{
   Frame frame;
   frame.buffer.resize(10);
   frame.payloadLen = 10;
   frame.partialHeaderRemaining = 0;

   const uint32 testFlag = FF_MASKKEY;
   const uint32 testSize = 8;

   // Call the function under test
   SetNextParitialBufferSize(&frame, testFlag, testSize);

   // Verify the flag was set correctly
   EXPECT_EQ(frame.flag, testFlag);

   // Verify the partial header was resized properly
   EXPECT_EQ(frame.partialHeaderRemaining, testSize);
   EXPECT_EQ(frame.partialHeader.size(), testSize);
}
