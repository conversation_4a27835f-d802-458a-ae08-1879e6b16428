/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */


/* ----------------------------------------------
 * hzaprep.cpp
 * ---------------------------------------------- */

#include "stdafx.h"
#include "commonhelper.h"
#include "baseprep.h"
#include "goldenimageregprep.h"
#include "cloneprep.h"
#include "svireg.h"

extern bool initTemplateHelperService();
extern bool initCloneHelperService();

/*
 *-----------------------------------------------------------------------------
 *
 * RunSysprepMode --
 *
 *    Implementation for hzaprep default mode
 *
 * Results:
 *    void
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */
void
RunSysprepMode()
{
   CommonHelper::mode = hzaprepMode::sysprep_mode;

   ClonePrep clonePrep;
   if (!clonePrep.init()) {
      return;
   }

   if (!clonePrep.populateSysprepData()) {
      SYSMSG_FUNC(Error, L"Failed to populate Sysyprep data.");
      return;
   }

   /*
    * Set the "Use SVI" flag to 1.
    * After the Sysprep specialization is done it is required that this
    * flag is set to 1 so that wsnm_jms waits for the customization to
    * finish.
    */
   if (!SviReg::setUseSVIFlag(true)) {
      SYSMSG_FUNC(Error, L"Failed to set \"Use SVI\" to 1.");
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * RunServiceMode --
 *
 *    Implementation for various service calls to hzaprep
 *
 * Results:
 *    Relevant hzapreperror.
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

hzaprepError
RunServiceMode()
{
   CommonHelper::mode = hzaprepMode::service_mode;
   // Check if the machine is a golden image by verifing a presence of PHs keypair in the
   // key valut
   /*BasePrep chkKey;
   if (!chkKey.keyExists(KEYPAR_PH_SIGN)) {
      SYSMSG_FUNC(Error, L"Machine is not a golden image, as PHs keypair not found");
      if (CommonHelper::getInstance().isMFWInitialized()) {
         CommonHelper::getInstance().shutdownMFW();
      }
      return hzaprepError::HZAPREP_NOT_GOLDEN_IMAGE;
   }*/

   // We need an event to make this thread wait till shutdown/restart
   g_hShutdown = CreateEvent(NULL, TRUE, FALSE, NULL);
   if (!g_hShutdown) {
      SYSMSG_FUNC(Error, L"Failed to create shutdown event, %d", GetLastError());
      return hzaprepError::HZAPREP_FAILED;
   }

   // start Template Helper Service
   if (!initTemplateHelperService()) {
      SYSMSG_FUNC(Error, L"Failed to start mfw TemplateHelper service");
      return hzaprepError::HZAPREP_MFW_SERVICE_FAILED;
   }

   // start Clone Helper Service
   if (!initCloneHelperService()) {
      SYSMSG_FUNC(Error, L"Failed to start mfw CloneHelper service");
      return hzaprepError::HZAPREP_MFW_SERVICE_FAILED;
   }

   /* Now we just hang around and wait for the shutdown signal or reboot */
   SYSMSG_FUNC(Info, L"Services started");
   WaitForSingleObject(g_hShutdown, INFINITE);
   // ResetEvent(g_hShutdown);
   g_bShutdown = true;
   return HZAPREP_SUCCESS;
}


/*
 *-----------------------------------------------------------------------------
 *
 * RunRegisterMode --
 *
 *    Implementation for golden image registration
 *
 * Results:
 *    Relevant hzapreperror.
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */
hzaprepError
RunRegisterMode(wstr brokerFQDN)
{
   // Register the machine as golden image.
   GoldenImageRegPrep goldenImageRegPrep;
   if (!goldenImageRegPrep.registerGoldenImage(brokerFQDN)) {
      SYSMSG_FUNC(Error, L"Golden image registration failed.");
      return hzaprepError::HZAPREP_REGISTRATION_FAILED;
   }

   /*
    * Set the "Use SVI" flag to 0.
    * This flag is checked by wsnm_jms.exe and if it is not set and the
    * IC Agent is installed then wsnm_jms.exe waits for customization to
    * finish. On a golden image machine, the IC Agent will be present but
    * there shouldn't be any customization at that point, now when the
    * agent service starts up for the first time this value needs to be
    * present and set to 0 so that wsnm_jms.exe doesn't wait for
    * customization, this will make sure that pairing happens and
    * eventually the installation succeeds in the golden image case.
    *
    * When the clone VM is powered on for the first time, this tool on
    * successfully getting the domain join details from the broker, will
    * set this flag to 1, so that wsnm_jms.exe can then wait for
    * customization to happen before pairing.
    */
   if (!SviReg::setUseSVIFlag(false)) {
      SYSMSG_FUNC(Error, L"Failed to set \"Use SVI\" to 0.");
   }

   return hzaprepError::HZAPREP_SUCCESS;
}

/*
 *-----------------------------------------------------------------------------
 *
 * wmain --
 *
 *    The program entry point implementation.
 *
 * Results:
 *    ERROR_SUCCESS on success otherwise -1.
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

int
wmain(int argc, wchar_t *argv[])
{
   int retVal = HZAPREP_SUCCESS;
   corerunnable::setThreadName("Main Thread");

   auto InitMFW = [&](bool serverMode) -> bool {
      /*
       * For non-server,
       * Start MessageFramework such that KeyVault read and write operations
       * can be done and can communicate with the broker over 32111 port.
       */
      if (!CommonHelper::getInstance().initMFW(serverMode)) {
         SYSMSG_FUNC(Error, L"Failed to initialize MessageFramework");
         retVal = hzaprepError::HZAPREP_MFW_FAILED;
         return false;
      }
      SYSMSG_FUNC(Trace, L"MessageFramework initialized.");
      return true;
   };

   if (argc == 1) {
      /*
       * This is the case when this tool is launched by Windows (in place of
       * Windeploy.exe) when the cloned VM is started up for the first time.
       * Since this is being launched in place of Windeploy.exe, so at the end
       * of each control path here, WinDeploy.exe is executed so that Sysprep
       * can be completed. Also, in each of these cases it is always tried that
       * the return value is the exit code of the Windeploy.exe process.
       */
      SYSMSG_FUNC(Debug, L"Helper tool starting without any argument.");
      if (InitMFW(false)) {
         RunSysprepMode();
      }
      retVal = ClonePrep::runWinDeploy();
   } else if (wcsstr(argv[1], L"service")) {
      /*
       * This is the case when the tool is launched in service mode
       * for preparing an Internal Template or a clone starts first time.
       */
      SYSMSG_FUNC(Debug, L"Helper tool starting in service mode.");
      if (!InitMFW(true)) {
         return hzaprepError::HZAPREP_MFW_FAILED;
      }
      retVal = RunServiceMode();
   } else if (wcsstr(argv[1], L"register")) {
      /*
       * This is the case when this tool is launched by the Horizon Agent
       * installer.
       */
      SYSMSG_FUNC(Debug, L"Helper tool starting for Golden Image registration.");
      if (argc < 3) {
         SYSMSG_FUNC(Error, L"Missing broker FQDN in register mode, will exit");
         return hzaprepError::HZAPREP_ARGUMENT_MISSIMG;
      }
      CommonHelper::mode = hzaprepMode::register_mode;
      if (!InitMFW(false)) {
         return hzaprepError::HZAPREP_MFW_FAILED;
      }

      retVal = RunRegisterMode(argv[2]);
   } else {
      SYSMSG_FUNC(Error, L"Unexpected parameter %s provided, helper tool will exit.", argv[1]);
      return hzaprepError::HZAPREP_INVALID_ARGUMENT;
   }

   CommonHelper::getInstance().shutdownMFW();
   return retVal;
}
