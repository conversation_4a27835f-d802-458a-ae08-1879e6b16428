/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * guestOpsMKSControl.hh --
 *
 *     viewControl Implementation of the GuestOps interface.
 */

#ifndef CUI_GHI_GUESTOPSMKSCONTROL_HH
#define CUI_GHI_GUESTOPSMKSCONTROL_HH

#include "dynxdr.h"

#include "cui/core/destroyNotifier.hh"
#include "cui/core/slotutils.hh"
#include "cui/ghi/guestOps.hh"

#include "mksCtrlxx/mksCtrl.hh"


#ifdef _WIN32
// Disable the inherits via dominance for MSVC
#   pragma warning(push)
#   pragma warning(disable : 4250)
#endif // _WIN32

struct GHITrayIcon;
struct GHITrayIconV1;


namespace cui {


class LIB_EXPORT GuestOpsMKSControl : public virtual GuestOps, public DestroyNotifier {
public:
   GuestOpsMKSControl();
   virtual ~GuestOpsMKSControl();

   bool GetCanUseShellLocationScheme(const utf::string &uriScheme) const; // OVERRIDE

   void SetResolution(int width, int height, AbortSlot onAbort = AbortSlot(),
                      DoneSlot onDone = DoneSlot()); // OVERRIDE
   void SetDisplayTopology(const std::vector<Rect> &monitors, AbortSlot onAbort = AbortSlot(),
                           DoneSlot onDone = DoneSlot()); // OVERRIDE
   void UnminimizeUnityWindow(uint32 windowID, AbortSlot onAbort = AbortSlot(),
                              DoneSlot onDone = DoneSlot()); // OVERRIDE
   void GetUnityWindowPath(uint32 windowID, GetUnityWindowPathDoneSlot onDone,
                           AbortSlot onAbort = AbortSlot()) const; // OVERRIDE
   void GetGuestExecInfo(const utf::string &path, GetGuestExecInfoDoneSlot onDone,
                         AbortSlot onAbort = AbortSlot()) const; // OVERRIDE
   void GetGuestExecHandlers(const utf::string &path, GetGuestExecHandlersDoneSlot onDone,
                             AbortSlot onAbort = AbortSlot()) const; // OVERRIDE
   void GetExecInfoHash(const utf::string &path, GetExecInfoHashDoneSlot onDone,
                        AbortSlot onAbort = AbortSlot()) const; // OVERRIDE
   void GetGuestWindowIcon(UnityWindowId windowID, UnityIconType iconType, UnityIconSize iconSize,
                           GetGuestWindowIconDoneSlot onDone,
                           AbortSlot onAbort = AbortSlot()) const; // OVERRIDE
   void OpenGuestFile(const utf::string &guestPath, DoneSlot onDone = DoneSlot(),
                      AbortSlot onAbort = AbortSlot()); // OVERRIDE
   void SetGuestFileHandler(const GuestApp::FileHandler &handler, DoneSlot onDone = DoneSlot(),
                            AbortSlot onAbort = AbortSlot()) const; // OVERRIDE
   void UnsetGuestFileHandler(const GuestApp::FileHandler &handler, DoneSlot onDone = DoneSlot(),
                              AbortSlot onAbort = AbortSlot()) const; // OVERRIDE
   void SetGuestURLHandler(const GuestApp::URLHandler &handler, DoneSlot onDone = DoneSlot(),
                           AbortSlot onAbort = AbortSlot()) const; // OVERRIDE
   void UnsetGuestURLHandler(const GuestApp::URLHandler &handler, DoneSlot onDone = DoneSlot(),
                             AbortSlot onAbort = AbortSlot()) const; // OVERRIDE
   void SetOutlookTempFolder(const utf::string &path, AbortSlot onAbort = AbortSlot(),
                             DoneSlot onDone = DoneSlot()) const; // OVERRIDE
   void RestoreOutlookTempFolder(AbortSlot onAbort = AbortSlot(),
                                 DoneSlot onDone = DoneSlot()) const; // OVERRIDE
   virtual void SetFocusedWindow(const uint32 windowId, AbortSlot onAbort = AbortSlot(),
                                 DoneSlot onDone = DoneSlot()) const; // OVERRIDE
   void StartNotificationAreaUpdates(AbortSlot onAbort = AbortSlot(),
                                     DoneSlot onDone = DoneSlot()); // OVERRIDE
   void StopNotificationAreaUpdates(AbortSlot onAbort = AbortSlot(),
                                    DoneSlot onDone = DoneSlot()); // OVERRIDE
   void SendNotificationAreaEvent(const utf::string &iconID, notificationArea::EventType event,
                                  int x, int y, AbortSlot onAbort = AbortSlot(),
                                  DoneSlot onDone = DoneSlot());                 // OVERRIDE
   PNGData GetNotificationAreaItemIcon(const utf::string &id) const;             // OVERRIDE
   utf::string GetNotificationAreaItemTooltip(const utf::string &id) const;      // OVERRIDE
   utf::string GetNotificationAreaItemBlacklistKey(const utf::string &id) const; // OVERRIDE
   void OrderUnityWindowsToTop(const std::list<uint32> &windows, AbortSlot onAbort = AbortSlot(),
                               DoneSlot onDone = DoneSlot()); // OVERRIDE
   void MinimizeUnityWindow(UnityWindowId windowID, AbortSlot onAbort = AbortSlot(),
                            DoneSlot onDone = DoneSlot()); // OVERRIDE
   void MaximizeUnityWindow(UnityWindowId windowID, AbortSlot onAbort = AbortSlot(),
                            DoneSlot onDone = DoneSlot()); // OVERRIDE
   void UnmaximizeUnityWindow(UnityWindowId windowID, AbortSlot onAbort = AbortSlot(),
                              DoneSlot onDone = DoneSlot()); // OVERRIDE
   void StickUnityWindow(UnityWindowId windowID, AbortSlot onAbort = AbortSlot(),
                         DoneSlot onDone = DoneSlot()); // OVERRIDE
   void UnstickUnityWindow(UnityWindowId windowID, AbortSlot onAbort = AbortSlot(),
                           DoneSlot onDone = DoneSlot()); // OVERRIDE
   void CloseUnityWindowRequest(uint32 windowID, AbortSlot onAbort = AbortSlot(),
                                DoneSlot onDone = DoneSlot()); // OVERRIDE
   void MoveResizeUnityWindow(UnityWindowId windowID, int x, int y, int width, int height,
                              AbortSlot onAbort = AbortSlot(),
                              MoveResizeDoneSlot = MoveResizeDoneSlot()); // OVERRIDE
   virtual void SetUnityDesktopWorkAreas(const std::vector<Rect> &workAreas,
                                         AbortSlot onAbort = AbortSlot(),
                                         DoneSlot onDone = DoneSlot()); // OVERRIDE
   void ToggleStartUI(AbortSlot onAbort = AbortSlot(),
                      DoneSlot onDone = DoneSlot()); // OVERRIDE
   void SetDisplayScaling(int percent, bool resetResolution,
                          const std::vector<DisplayScaling> &displays,
                          AbortSlot onAbort = AbortSlot(), DoneSlot onDone = DoneSlot());
   void SetDarkMode(bool isDarkMode, AbortSlot onAbort = AbortSlot(), DoneSlot onDone = DoneSlot());
   void SetUnityOptions(uint32 featureMask, AbortSlot onAbort = AbortSlot(),
                        DoneSlot onDone = DoneSlot()); // OVERRIDE
   void SetUnityDesktopConfig(const DesktopList &desktops, uint32 activeDesktopID,
                              AbortSlot onAbort = AbortSlot(),
                              DoneSlot onDone = DoneSlot()); // OVERRIDE
   void SetUnityActiveDesktop(uint32 desktopID, AbortSlot onAbort = AbortSlot(),
                              DoneSlot onDone = DoneSlot()); // OVERRIDE
   void SetUnityWindowDesktop(UnityWindowId windowID, UnityDesktopId desktopID,
                              AbortSlot onAbort = AbortSlot(),
                              DoneSlot onDone = DoneSlot()); // OVERRIDE
   void RequestUnityGuestWindowContents(const std::list<UnityWindowId> &windowIDs,
                                        AbortSlot onAbort = AbortSlot(),
                                        DoneSlot onDone = DoneSlot()); // OVERRIDE
   void UpdateUnityState(bool isOn);                                   // OVERRIDE
   void UnityConfirmOperation(const UnityOperation &operation, bool allow,
                              AbortSlot onAbort = AbortSlot(),
                              DoneSlot onDone = DoneSlot()); // OVERRIDE
   void ShowUnityWindow(UnityWindowId windowId, AbortSlot onAbort = AbortSlot(),
                        DoneSlot onDone = DoneSlot()); // OVERRIDE
   void HideUnityWindow(UnityWindowId windowId, AbortSlot onAbort = AbortSlot(),
                        DoneSlot onDone = DoneSlot()); // OVERRIDE
   void EnterUnity(AbortSlot onAbort = AbortSlot(),
                   DoneSlot onDone = DoneSlot()); // OVERRIDE
   void ExitUnity(AbortSlot onAbort = AbortSlot(),
                  DoneSlot onDone = DoneSlot()); // OVERRIDE
   void RequestFullUnityUpdate(AbortSlot onAbort = AbortSlot(),
                               DoneSlot onDone = DoneSlot()); // OVERRIDE
   void UnitySendMouseWheel(int32 deltaX, int32 deltaY, int32 deltaZ, uint32 modifierFlags,
                            AbortSlot onAbort = AbortSlot(),
                            DoneSlot onDone = DoneSlot()); // OVERRIDE

protected:
   void SetMKSControlClient(mksctrl::MKSControlClientBase *mksControlClient);
   mksctrl::MKSControlClientBase *GetMKSControlClient() const { return mMKSControlClient.get(); }

   virtual void SendGHIRequest(GHIChannelType channel, const char *msgName, const uint8 *msgData,
                               uint32 msgDataLen, cui::AbortSlot onAbort,
                               mksctrl::GHIResponseSlot onDone) const;
   void SendUnityWindowRPC(const char *msgName, UnityWindowId windowID, AbortSlot onAbort,
                           mksctrl::GHIResponseSlot onDone) const;
   template<typename XdrMsgType>
   void SendGHIXdrRequest(GHIChannelType channel, const char *msgName, XdrMsgType &xdrMsg,
                          bool_t (*xdrMsgFunc)(XDR *, XdrMsgType *), bool needFreeXdrMsg,
                          cui::AbortSlot onAbort, mksctrl::GHIResponseSlot onDone) const;

   static void OnGetGuestExecInfoDone(const uint8 *msg, uint32 msgLen,
                                      GetGuestExecInfoDoneSlot onDone, AbortSlot onAbort);

   sigc::signal<void, GHIGuestToHostMessageType, const uint8 *, uint32> ghiUpdateNotified;
   virtual void OnGuestTrayIconUpdateCB(const uint8 *msg, uint32 msgLen);

private:
   struct GuestWindowIconData {
      UnityWindowId windowID;
      UnityIconType iconType;
      UnityIconSize iconSize;
      PNGData pngData;
      GetGuestWindowIconDoneSlot doneSlot;
      AbortSlot abortSlot;
   };

   struct GuestTrayIcon {
      PNGData pngData;
      utf::string tooltip;
      utf::string blacklistKey;
   };
   typedef std::map<utf::string, GuestTrayIcon> GuestTrayIconMap;

   struct UnityWindowContents {
      UnityWindowContents();

      UnityWindowId windowID; // window ID
      uint32 nextChunk;       // sequence number of next chunk
      uint32 imageWidth;      // width of the image
      uint32 imageHeight;     // height of the image
      uint32 imageLength;     // total length of the image data, in bytes
      PNGData buffer;         // image data
   };

private:
   void OnGHIUpdateNotified(GHIGuestToHostMessageType type, const uint8 *msg, uint32 msgLen);
   void OnUnityUpdateCB(const uint8 *msg, uint32 msgLen);
   void OnUnityGuestRequestOperationCB(const uint8 *msg, uint32 msgLen);
   void OnGuestWindowContentStartCB(const uint8 *msg, uint32 msgLen);
   void OnGuestWindowContentChunkCB(const uint8 *msg, uint32 msgLen);
   void OnGuestWindowContentEndCB(const uint8 *msg, uint32 msgLen);
   void OnGuestHostShellActionCB(const uint8 *msg, uint32 msgLen);
   void OnGuestLaunchMenuChangeCB(const uint8 *msg, uint32 msgLen);
   void OnGuestWindowOverlayIconUpdateCB(const uint8 *msg, uint32 msgLen);

   static int ToolsEventForNotificationAreaEvent(notificationArea::EventType event);
   void GetGuestWindowIconChunk(GuestWindowIconData *iconData) const;
   void OnGetGuestWindowIconChunkDone(const uint8 *msg, uint32 msgLen,
                                      GuestWindowIconData *iconData) const;
   static void OnGetGuestWindowIconChunkAbort(bool cancelled, const cui::Error &e,
                                              GuestWindowIconData *iconData);

   static void OnGetUnityWindowPathResponse(const uint8 *msg, uint32 msgLen,
                                            GetUnityWindowPathDoneSlot onDone, AbortSlot onAbort);

   static void OnMoveResizeUnityWindowDone(const uint8 *msg, uint32 msgLen, AbortSlot onAbort,
                                           MoveResizeDoneSlot onDone);

   static void OnGetExecInfoHashDone(const uint8 *msg, uint32 msgLen,
                                     GetExecInfoHashDoneSlot onDone, AbortSlot onAbort);

   bool AddTrayIcon(const utf::string &iconID, const GHITrayIcon &ghiTrayIcon);
   bool UpdateTrayIcon(const utf::string &iconID, const GHITrayIcon &ghiTrayIcon);
   bool DeleteTrayIcon(const utf::string &iconID);
   void ClearAllTrayIcons();
   static void UpdateTrayIconItems(GuestTrayIcon &trayIcon, const GHITrayIconV1 *trayIconV1);

   static GuestOps::UnityOperationType ConvertUnityOperationType(UnityOperations requestOpType);
   static UnityOperations ConvertUnityOperationType(GuestOps::UnityOperationType requestOpType);

   static void OnGetGuestExecHandlersDone(const uint8 *msg, uint32 msgLen,
                                          GetGuestExecHandlersDoneSlot onDone, AbortSlot onAbort);

private:
   cui::WeakPtr<mksctrl::MKSControlClientBase> mMKSControlClient;
   sigc::connection mToolsUserNotifiedConnection;
   sigc::connection mToolsMainNotifiedConnection;
   bool mUnityIsOn;
   bool mSawUnityUpdateWhileNotInUnity;
   GuestTrayIconMap mGuestTrayIconItems;
   UnityWindowContents mWindowContents;

#ifdef CRTBORATEST
   friend class GHIGuestOpsMKSControlUnitTest_TestOnGuestWindowContentChunkCB_Test;
#endif
};


/*
 *-----------------------------------------------------------------------------
 *
 * cui::GuestOpsMKSControl::SendGHIXdrRequest --
 *
 *      xxx: deprecated. Please use the XdrEncoder class in cui/utils/autoXdr.hh
 *      instead.
 *
 *      Send to the guest a GHI/Unity message carrying a XDR structure.
 *
 *      onDone/onAbort will be called when the guest's response to the request
 *      is received, or the request is failed to be sent, or the request times out.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

template<typename XdrMsgType>
void
GuestOpsMKSControl::SendGHIXdrRequest(GHIChannelType channel,                    // IN
                                      const char *msgName,                       // IN
                                      XdrMsgType &xdrMsg,                        // IN
                                      bool_t (*xdrMsgFunc)(XDR *, XdrMsgType *), // IN
                                      bool needFreeXdrMsg,                       // IN
                                      cui::AbortSlot onAbort,                    // IN
                                      mksctrl::GHIResponseSlot onDone)           // IN
   const
{
   bool sent = false;
   XDR xdrs;
   if (DynXdr_Create(&xdrs) != NULL) {
      if (xdrMsgFunc(&xdrs, &xdrMsg)) {
         SendGHIRequest(channel, msgName, reinterpret_cast<uint8 *>(DynXdr_Get(&xdrs)),
                        xdr_getpos(&xdrs), onAbort, onDone);
         sent = true;
      } else {
         Warning("GuestOpsMKSControl::SendGHIXdrRequest: %s: encoding message failed.\n", msgName);
      }
      DynXdr_Destroy(&xdrs, TRUE);
   } else {
      Warning("GuestOpsMKSControl::SendGHIXdrRequest: %s: creating DynXdr failed.\n", msgName);
   }

   if (needFreeXdrMsg) {
      xdr_free(reinterpret_cast<xdrproc_t>(xdrMsgFunc), reinterpret_cast<char *>(&xdrMsg));
   }

   if (!sent) {
      cui::Abort(onAbort);
   }
}


} // namespace cui


#endif // CUI_GHI_GUESTOPSMKSCONTROL_HH
