name: hclin-ut

on:
  workflow_call:
    inputs:
      buildtype:
        type: string
        description: 'Build type'
        required: True
      useGcov:
        type: boolean
        description: 'Flag for if the build enable gcov'
        required: True
  workflow_dispatch:
    inputs:
      productWorkflow:
        type: choice
        description: 'Product'
        required: True
        default: 'hclin'
        options:
          - 'hclin'
          - 'hclin_codecov'
      runNumber:
        type: string
        description: 'Run number of hclin or hclin_codecov'
        required: true
      runAttempt:
        type: string
        description: 'Run attempt'
        required: true
        default: '1'
      buildtype:
        type: choice
        description: 'Build type'
        required: True
        default: 'beta'
        options:
          - 'beta'
          - 'obj'
          - 'release'
      useGcov:
        type: boolean
        description: 'Flag for if the build enable gcov'
        required: True
        default: false

env:
  PRODUCTWORKFLOW: ${{ github.event_name == 'workflow_dispatch' && inputs.productWorkflow || github.workflow }}
  RUNNUMBER: ${{ github.event_name == 'workflow_dispatch' && inputs.runNumber || github.run_number }}
  RUNATTEMPT: ${{ github.event_name == 'workflow_dispatch' && inputs.runAttempt || github.run_attempt }}
  PRODUCTWORKFLOWJOB: build-hclin

jobs:
  ar-crtbora:
    # This test is disabled due to repeated failures. It can be re-enabled when
    # https://omnissa.atlassian.net/browse/VCART-6710 is resolved and the test is able to run
    # repeatedly as per https://omnissa.atlassian.net/wiki/spaces/HorizonArchitecture/pages/719226201
    # if: ${{inputs.buildtype == 'obj'}}
    if: false
    runs-on: [cart-bj, ut, self-hosted, Linux]
    timeout-minutes: 20
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Run Case
        uses: ./.github/actions/testframework/runcase
        with:
          productWorkflow: $PRODUCTWORKFLOW
          runNumber: $RUNNUMBER
          runAttempt: $RUNATTEMPT
          productWorkflowJob: $PRODUCTWORKFLOWJOB
          artifactoryBaseUrl: ${{ vars.ARTIFACTORY_GITHUB_DELIVERABLES_URL }}
          artifactoryApiToken: ${{ secrets.ARTIFACTORY_GITHUB_DELIVERABLES_API_TOKEN }}
          reportDbUri: ${{ secrets.VDUB_REPORT_DB_W }}
          reportDbName: ${{ secrets.VDUB_REPORT_DB_NAME }}
          reportDbCollection: ${{ secrets.VDUB_REPORT_COL_NAME }}
          testFilePaths: tests/linux64/crtboraApiTest.zip
          deployCommand: chmod 777 crtboraApiTest;
          testCommand: export LD_LIBRARY_PATH=./; ./crtboraApiTest --gtest_catch_exceptions=0 --gtest_output=xml:unitTest.xml --gtest_filter=*UnitTest*
          buildtype: ${{ inputs.buildtype}}
          useGcov: ${{ inputs.useGcov }}

  ar-rmks:
    runs-on: [cart-bj, ut, self-hosted, Linux]
    timeout-minutes: 20
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Run Case
        uses: ./.github/actions/testframework/runcase
        with:
          productWorkflow: $PRODUCTWORKFLOW
          runNumber: $RUNNUMBER
          runAttempt: $RUNATTEMPT
          productWorkflowJob: $PRODUCTWORKFLOWJOB
          artifactoryBaseUrl: ${{ vars.ARTIFACTORY_GITHUB_DELIVERABLES_URL }}
          artifactoryApiToken: ${{ secrets.ARTIFACTORY_GITHUB_DELIVERABLES_API_TOKEN }}
          reportDbUri: ${{ secrets.VDUB_REPORT_DB_W }}
          reportDbName: ${{ secrets.VDUB_REPORT_DB_NAME }}
          reportDbCollection: ${{ secrets.VDUB_REPORT_COL_NAME }}
          testFilePaths: tests/linux64/viewClientApiTest.zip
          deployCommand: chmod 777 viewClientApiTest;
          testCommand: export LD_LIBRARY_PATH=./; ./viewClientApiTest --gtest_catch_exceptions=0 --gtest_output=xml:unitTest.xml --gtest_filter=*UnitTest*
          buildtype: ${{ inputs.buildtype}}
          useGcov: ${{ inputs.useGcov }}

  client-clientsdk:
    runs-on: [cart-bj, ut, self-hosted, Linux]
    timeout-minutes: 20
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Run Case
        uses: ./.github/actions/testframework/runcase
        with:
          productWorkflow: $PRODUCTWORKFLOW
          runNumber: $RUNNUMBER
          runAttempt: $RUNATTEMPT
          productWorkflowJob: $PRODUCTWORKFLOWJOB
          artifactoryBaseUrl: ${{ vars.ARTIFACTORY_GITHUB_DELIVERABLES_URL }}
          artifactoryApiToken: ${{ secrets.ARTIFACTORY_GITHUB_DELIVERABLES_API_TOKEN }}
          reportDbUri: ${{ secrets.VDUB_REPORT_DB_W }}
          reportDbName: ${{ secrets.VDUB_REPORT_DB_NAME }}
          reportDbCollection: ${{ secrets.VDUB_REPORT_COL_NAME }}
          testFilePaths: tests/unittest-Omnissa-Horizon-Client-Linux-Intel-*.zip
          deployCommand: chmod 777 horizonrxut;
          testCommand: export LD_LIBRARY_PATH=./; ./horizonrxut libclientSdkUnitTest.so --report=unitTest.xml
          buildtype: ${{ inputs.buildtype}}
          useGcov: ${{ inputs.useGcov }}

  client-libcdk:
    runs-on: [cart-bj, ut, self-hosted, Linux]
    timeout-minutes: 20
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Run Case
        uses: ./.github/actions/testframework/runcase
        with:
          productWorkflow: $PRODUCTWORKFLOW
          runNumber: $RUNNUMBER
          runAttempt: $RUNATTEMPT
          productWorkflowJob: $PRODUCTWORKFLOWJOB
          artifactoryBaseUrl: ${{ vars.ARTIFACTORY_GITHUB_DELIVERABLES_URL }}
          artifactoryApiToken: ${{ secrets.ARTIFACTORY_GITHUB_DELIVERABLES_API_TOKEN }}
          reportDbUri: ${{ secrets.VDUB_REPORT_DB_W }}
          reportDbName: ${{ secrets.VDUB_REPORT_DB_NAME }}
          reportDbCollection: ${{ secrets.VDUB_REPORT_COL_NAME }}
          testFilePaths: tests/unittest-Omnissa-Horizon-Client-Linux-Intel-*.zip
          deployCommand: chmod 777 horizonrxut;
          testCommand: export LD_LIBRARY_PATH=./; ./horizonrxut libcdkUnitTest.so --report=unitTest.xml
          buildtype: ${{ inputs.buildtype}}
          useGcov: ${{ inputs.useGcov }}

  rx-clipboard:
    runs-on: [cart-bj, ut, self-hosted, Linux]
    timeout-minutes: 20
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Run Case
        uses: ./.github/actions/testframework/runcase
        with:
          productWorkflow: $PRODUCTWORKFLOW
          runNumber: $RUNNUMBER
          runAttempt: $RUNATTEMPT
          productWorkflowJob: $PRODUCTWORKFLOWJOB
          artifactoryBaseUrl: ${{ vars.ARTIFACTORY_GITHUB_DELIVERABLES_URL }}
          artifactoryApiToken: ${{ secrets.ARTIFACTORY_GITHUB_DELIVERABLES_API_TOKEN }}
          reportDbUri: ${{ secrets.VDUB_REPORT_DB_W }}
          reportDbName: ${{ secrets.VDUB_REPORT_DB_NAME }}
          reportDbCollection: ${{ secrets.VDUB_REPORT_COL_NAME }}
          testFilePaths: tests/horizonrxtest/linux64/horizonrxut.zip,tests/linux64/clipboardUnitTest.zip
          deployCommand: chmod 777 horizonrxut;
          testCommand: export LD_LIBRARY_PATH=./; ./horizonrxut --modules=libclipboardUnitTest.so --type=run_tests --report=unitTest.xml
          buildtype: ${{ inputs.buildtype}}
          useGcov: ${{ inputs.useGcov }}

  rx-fcp:
    runs-on: [cart-bj, ut, self-hosted, Linux]
    timeout-minutes: 20
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Run Case
        uses: ./.github/actions/testframework/runcase
        with:
          productWorkflow: $PRODUCTWORKFLOW
          runNumber: $RUNNUMBER
          runAttempt: $RUNATTEMPT
          productWorkflowJob: $PRODUCTWORKFLOWJOB
          artifactoryBaseUrl: ${{ vars.ARTIFACTORY_GITHUB_DELIVERABLES_URL }}
          artifactoryApiToken: ${{ secrets.ARTIFACTORY_GITHUB_DELIVERABLES_API_TOKEN }}
          reportDbUri: ${{ secrets.VDUB_REPORT_DB_W }}
          reportDbName: ${{ secrets.VDUB_REPORT_DB_NAME }}
          reportDbCollection: ${{ secrets.VDUB_REPORT_COL_NAME }}
          testFilePaths: tests/horizonrxtest/linux64/horizonrxut.zip,tests/linux64/fcpUnitTest.zip
          deployCommand: chmod 777 horizonrxut;
          testCommand: export LD_LIBRARY_PATH=./; ./horizonrxut --modules=libfcpUnitTest.so --type=run_tests --report=unitTest.xml
          buildtype: ${{ inputs.buildtype}}
          useGcov: ${{ inputs.useGcov }}

  rx-sdk:
    runs-on: [cart-bj, ut, self-hosted, Linux]
    timeout-minutes: 20
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Run Case
        uses: ./.github/actions/testframework/runcase
        with:
          productWorkflow: $PRODUCTWORKFLOW
          runNumber: $RUNNUMBER
          runAttempt: $RUNATTEMPT
          productWorkflowJob: $PRODUCTWORKFLOWJOB
          artifactoryBaseUrl: ${{ vars.ARTIFACTORY_GITHUB_DELIVERABLES_URL }}
          artifactoryApiToken: ${{ secrets.ARTIFACTORY_GITHUB_DELIVERABLES_API_TOKEN }}
          reportDbUri: ${{ secrets.VDUB_REPORT_DB_W }}
          reportDbName: ${{ secrets.VDUB_REPORT_DB_NAME }}
          reportDbCollection: ${{ secrets.VDUB_REPORT_COL_NAME }}
          testFilePaths: tests/horizonrxtest/linux64/horizonrxut.zip,tests/unittest-Omnissa-Horizon-Client-Linux-Intel-*.zip
          deployCommand: chmod 777 horizonrxut;
          testCommand: export LD_LIBRARY_PATH=./; ./horizonrxut --modules=libclientSdkUnitTest.so --type=run_tests --report=unitTest.xml
          buildtype: ${{ inputs.buildtype}}
          useGcov: ${{ inputs.useGcov }}

  rx-tsdr:
    runs-on: [cart-bj, ut, self-hosted, Linux]
    timeout-minutes: 20
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Run Case
        uses: ./.github/actions/testframework/runcase
        with:
          productWorkflow: $PRODUCTWORKFLOW
          runNumber: $RUNNUMBER
          runAttempt: $RUNATTEMPT
          productWorkflowJob: $PRODUCTWORKFLOWJOB
          artifactoryBaseUrl: ${{ vars.ARTIFACTORY_GITHUB_DELIVERABLES_URL }}
          artifactoryApiToken: ${{ secrets.ARTIFACTORY_GITHUB_DELIVERABLES_API_TOKEN }}
          reportDbUri: ${{ secrets.VDUB_REPORT_DB_W }}
          reportDbName: ${{ secrets.VDUB_REPORT_DB_NAME }}
          reportDbCollection: ${{ secrets.VDUB_REPORT_COL_NAME }}
          testFilePaths: tests/horizonrxtest/linux64/horizonrxut.zip,tests/linux64/tsdrClientUnitTest.zip
          deployCommand: chmod 777 horizonrxut;
          testCommand: export LD_LIBRARY_PATH=./; ./horizonrxut --modules=libtsdrClientUnitTest.so --type=run_tests --report=unitTest.xml
          buildtype: ${{ inputs.buildtype}}
          useGcov: ${{ inputs.useGcov }}

  rx-url:
    runs-on: [cart-bj, ut, self-hosted, Linux]
    timeout-minutes: 20
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Run Case
        uses: ./.github/actions/testframework/runcase
        with:
          productWorkflow: $PRODUCTWORKFLOW
          runNumber: $RUNNUMBER
          runAttempt: $RUNATTEMPT
          productWorkflowJob: $PRODUCTWORKFLOWJOB
          artifactoryBaseUrl: ${{ vars.ARTIFACTORY_GITHUB_DELIVERABLES_URL }}
          artifactoryApiToken: ${{ secrets.ARTIFACTORY_GITHUB_DELIVERABLES_API_TOKEN }}
          reportDbUri: ${{ secrets.VDUB_REPORT_DB_W }}
          reportDbName: ${{ secrets.VDUB_REPORT_DB_NAME }}
          reportDbCollection: ${{ secrets.VDUB_REPORT_COL_NAME }}
          testFilePaths: tests/horizonrxtest/linux64/horizonrxut.zip,tests/linux64/urlUnitTest.zip
          deployCommand: chmod 777 horizonrxut;
          testCommand: export LD_LIBRARY_PATH=./; ./horizonrxut --modules=liburlUnitTest.so --type=run_tests --report=unitTest.xml
          buildtype: ${{ inputs.buildtype}}
          useGcov: ${{ inputs.useGcov }}

  vd-fido2:
    runs-on: [cart-bj, ut, self-hosted, Linux]
    timeout-minutes: 20
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Run Case
        uses: ./.github/actions/testframework/runcase
        with:
          productWorkflow: $PRODUCTWORKFLOW
          runNumber: $RUNNUMBER
          runAttempt: $RUNATTEMPT
          productWorkflowJob: $PRODUCTWORKFLOWJOB
          artifactoryBaseUrl: ${{ vars.ARTIFACTORY_GITHUB_DELIVERABLES_URL }}
          artifactoryApiToken: ${{ secrets.ARTIFACTORY_GITHUB_DELIVERABLES_API_TOKEN }}
          reportDbUri: ${{ secrets.VDUB_REPORT_DB_W }}
          reportDbName: ${{ secrets.VDUB_REPORT_DB_NAME }}
          reportDbCollection: ${{ secrets.VDUB_REPORT_COL_NAME }}
          testFilePaths: tests/horizonrxtest/linux64/horizonrxut.zip,tests/linux64/fido2ClientUnitTest.zip
          deployCommand: chmod 777 horizonrxut;
          testCommand: export LD_LIBRARY_PATH=./; ./horizonrxut libfido2ClientUnitTest.so --report=unitTest.xml
          buildtype: ${{ inputs.buildtype}}
          useGcov: ${{ inputs.useGcov }}

  vd-rtav:
    runs-on: [cart-bj, ut, self-hosted, Linux]
    timeout-minutes: 20
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Run Case
        uses: ./.github/actions/testframework/runcase
        with:
          productWorkflow: $PRODUCTWORKFLOW
          runNumber: $RUNNUMBER
          runAttempt: $RUNATTEMPT
          productWorkflowJob: $PRODUCTWORKFLOWJOB
          artifactoryBaseUrl: ${{ vars.ARTIFACTORY_GITHUB_DELIVERABLES_URL }}
          artifactoryApiToken: ${{ secrets.ARTIFACTORY_GITHUB_DELIVERABLES_API_TOKEN }}
          reportDbUri: ${{ secrets.VDUB_REPORT_DB_W }}
          reportDbName: ${{ secrets.VDUB_REPORT_DB_NAME }}
          reportDbCollection: ${{ secrets.VDUB_REPORT_COL_NAME }}
          testFilePaths: tests/horizonrxtest/linux64/horizonrxut.zip,tests/linux64/rtavUnitTest.zip
          deployCommand: chmod 777 horizonrxut;
          testCommand: export LD_LIBRARY_PATH=./; ./horizonrxut librtavUnitTest.so --report=unitTest.xml
          buildtype: ${{ inputs.buildtype}}
          useGcov: ${{ inputs.useGcov }}

  vd-usb:
    runs-on: [cart-bj, ut, self-hosted, Linux]
    timeout-minutes: 20
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Run Case
        uses: ./.github/actions/testframework/runcase
        with:
          productWorkflow: $PRODUCTWORKFLOW
          runNumber: $RUNNUMBER
          runAttempt: $RUNATTEMPT
          productWorkflowJob: $PRODUCTWORKFLOWJOB
          artifactoryBaseUrl: ${{ vars.ARTIFACTORY_GITHUB_DELIVERABLES_URL }}
          artifactoryApiToken: ${{ secrets.ARTIFACTORY_GITHUB_DELIVERABLES_API_TOKEN }}
          reportDbUri: ${{ secrets.VDUB_REPORT_DB_W }}
          reportDbName: ${{ secrets.VDUB_REPORT_DB_NAME }}
          reportDbCollection: ${{ secrets.VDUB_REPORT_COL_NAME }}
          testFilePaths: tests/horizonrxtest/linux64/horizonrxut.zip,tests/linux64/usbUnitTestClient.zip
          deployCommand: chmod 777 horizonrxut;
          testCommand: "export LD_LIBRARY_PATH=./;
            ./horizonrxut libusbDevConfigUnitTest.so,\
            libusbDevFilterUnitTest.so,\
            libusbMmfwUnitTest.so,\
            libusbRedirectionClientUnitTest.so,\
            libusbStringStoreUnitTest.so,\
            libusbUrbTrxUnitTest.so,\
            libusbUsbdUnitTest.so,\
            libusbViewUsbLibUnitTest.so --report=unitTest.xml"
          buildtype: ${{ inputs.buildtype}}
          useGcov: ${{ inputs.useGcov }}

  vd-vip:
    runs-on: [cart-bj, ut, self-hosted, Linux]
    timeout-minutes: 20
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Run Case
        uses: ./.github/actions/testframework/runcase
        with:
          productWorkflow: $PRODUCTWORKFLOW
          runNumber: $RUNNUMBER
          runAttempt: $RUNATTEMPT
          productWorkflowJob: $PRODUCTWORKFLOWJOB
          artifactoryBaseUrl: ${{ vars.ARTIFACTORY_GITHUB_DELIVERABLES_URL }}
          artifactoryApiToken: ${{ secrets.ARTIFACTORY_GITHUB_DELIVERABLES_API_TOKEN }}
          reportDbUri: ${{ secrets.VDUB_REPORT_DB_W }}
          reportDbName: ${{ secrets.VDUB_REPORT_DB_NAME }}
          reportDbCollection: ${{ secrets.VDUB_REPORT_COL_NAME }}
          testFilePaths: tests/horizonrxtest/linux64/horizonrxut.zip,tests/linux64/printredirut.zip
          deployCommand: chmod 777 horizonrxut;
          testCommand: export LD_LIBRARY_PATH=./; ./horizonrxut libprintredirut.so --report=unitTest.xml
          buildtype: ${{ inputs.buildtype}}
          useGcov: ${{ inputs.useGcov }}