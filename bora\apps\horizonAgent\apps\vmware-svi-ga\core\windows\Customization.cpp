/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

#include "stdafx.h"

#include <core/util/NotifyViewAgent.h>
#include <core/windows/Customization.h>
#include <core/windows/Network.h>
#include <core/windows/PersistentDisks.h>

using namespace svmga::core::windows;
using namespace svmga::core::util;

Customization::Customization(
   AgentIntegration *pAgentIntegration, CustomizationPersistentInfoReg *pCPI,
   CustomizationFlags *pCustomizationFlags, CustomizationState *pCustomizationState,
   CustomizationType *pCustomizationType, DomainJoin *pDomainJoin, GroupPolicy *pGroupPolicy,
   GuestInfo *pGuestInfo, License *pLicense, MachineType *pMachineType, Network *pNetwork,
   PersistentDisks *pPersistentDisks, Registry *pRegistry, Script *pScript, Support *pSupport,
   System *pSystem, NotifyViewAgent *pNva, windev::RenewIp *pRenewIp)
{
   //
   // Override pointers
   //
   pAgentIntegration ? _AgentIntegration.reset(pAgentIntegration)
                     : _AgentIntegration.reset(new AgentIntegration());

   pCPI ? _cpi = pCPI : _cpi = CustomizationPersistentInfo::GetInstance();

   pCustomizationFlags ? _CustFlags = pCustomizationFlags
                       : _CustFlags = CustomizationFlags::GetInstance();

   pCustomizationState ? _CustState = pCustomizationState
                       : _CustState = CustomizationState::GetInstance();

   pCustomizationType ? _CustType = pCustomizationType
                      : _CustType = CustomizationType::GetInstance();

   pDomainJoin ? _DomainJoin.reset(pDomainJoin) : _DomainJoin.reset(new DomainJoin());

   pGroupPolicy ? _GroupPolicy.reset(pGroupPolicy) : _GroupPolicy.reset(new GroupPolicy());

   pGuestInfo ? _GuestInfo.reset(pGuestInfo) : _GuestInfo.reset(new GuestInfo());

   pLicense ? _License.reset(pLicense) : _License.reset(new License());

   pMachineType ? _MachineType = pMachineType : _MachineType = MachineType::GetInstance();

   pNetwork ? _Network = pNetwork : _Network = Network::GetInstance();

   pPersistentDisks ? _PersistentDisks.reset(pPersistentDisks)
                    : _PersistentDisks.reset(new PersistentDisks());
   pRegistry ? _Registry = pRegistry : _Registry = Registry::GetInstance();
   pScript ? _Script.reset(pScript) : _Script.reset(new Script());
   pSupport ? _Support = pSupport : _Support = Support::GetInstance();
   pSystem ? _System = pSystem : _System = System::GetInstance();

   pRenewIp ? _RenewIp.reset(pRenewIp) : _RenewIp.reset(new windev::RenewIp());
}

bool
Customization::CustomizeVM()
{
   bool bCustomized = false;

   switch (_MachineType->GetMachineType()) {
   case VmType::InternalTemplate:
      SYSMSG_FUNC(Debug, _T("** Internal Template **"));

      bCustomized = CustomizeIT();
      if (!bCustomized) {
         SYSMSG_FUNC(Debug, _T("Internal Template Customization Failed"));
      }

      break;

   case VmType::Replica:
      SYSMSG_FUNC(Debug, _T("** Replica **"));

      bCustomized = CustomizeReplica();
      if (!bCustomized) {
         SYSMSG_FUNC(Debug, _T("Replica Customization Failed"));
      }

      break;

   case VmType::Parent:

      SYSMSG_FUNC(Debug, _T("** Parent **"));

      bCustomized = CustomizeParent();
      if (!bCustomized) {
         SYSMSG_FUNC(Debug, _T("Parent Customization Failed"));
      }

      break;

   case VmType::Clone:

      SYSMSG_FUNC(Debug, _T("** Clone **"));

      bCustomized = CustomizeClone();
      if (!bCustomized) {
         if (!_cpi->IsCloneCustomizationCompleted()) {
            SYSMSG_FUNC(Debug, _T("Clone Customization Failed"));
         }
      }

      break;

   default:
      SYSMSG_FUNC(Debug, _T("Nothing Needs To Be Done."));
   };

   return bCustomized;
}

bool
Customization::CustomizeMaster()
{
   //
   // Make sure nga's BootExecute value is set
   // It can get cleared by Windows Updates
   //
   return VerifyNGAConfig();
}

bool
Customization::CustomizeIT()
{
   return true;
}

bool
Customization::CustomizeParent()
{
   return true;
}

bool
Customization::CustomizeReplica()
{
   return true;
}

bool
Customization::CustomizeClone()
{
   return true;
}

bool
Customization::CheckCustomizationFlagsForReboot()
{
   int flags = 0;
   bool bSkip = false;

   //
   // Customization flags tell us if we need to do anything
   // special during customization, e.g. shutdown or reboot
   // the clone
   //
   flags = _CustFlags->GetCustomizationFlagsValue();
   SYSMSG_FUNC(Debug, _T("Current Customization Flags: (0x%X), %ws"), flags,
               _CustFlags->GetCustomizationFlagsString().c_str());

   if (_CustFlags->IsFlagOn(CUSTOMIZATION_FLAG_SHUTDOWN_REQUESTED) &&
       !_CustFlags->IsFlagOn(CUSTOMIZATION_FLAG_SHUTDOWN_COMPLETED)) {
      bSkip = true;
      flags = _CustFlags->SetCustomizationFlags(flags | CUSTOMIZATION_FLAG_SHUTDOWN_COMPLETED);
      SYSMSG_FUNC(Debug, _T("Set shutdown completed flag"));
   }

   if (_CustFlags->IsFlagOn(CUSTOMIZATION_FLAG_REBOOT_REQUESTED) &&
       !_CustFlags->IsFlagOn(CUSTOMIZATION_FLAG_REBOOT_COMPLETED)) {
      bSkip = true;
      flags = _CustFlags->SetCustomizationFlags(CUSTOMIZATION_FLAG_REBOOT_COMPLETED);
      SYSMSG_FUNC(Debug, _T("Set reboot completed flag"));
   }

   //
   // Return true if a reboot or shutdown has happened because
   // we've already performed customization
   //
   return bSkip;
}

bool
Customization::CompleteCustomization()
{
   if (OkToCompleteCustomization()) {
      //
      // If we make it to here, it's time to complete customization
      //
      SYSMSG_FUNC(Debug, _T("Completing Clone Customization..."));

      if (!_Script->RunPostCustomizationScript(_CustType->IsFastRefresh())) {
         _CustState->SetCustomizationState(CUSTOMIZATION_STATE_ERROR,
                                           SVM_PS_FAILED_TO_EXECUTE_POSTSYNC_SCRIPT,
                                           NGVC_CATEGORY_SCRIPTS, E_FAIL);

         return false;
      }

      //
      // Set post-customization agent integration values
      //
      if (!_cpi->IsUniversalPrep()) {
         // Only for legacy vShpere based VMs
         _AgentIntegration->ParseAgentIntegrationValues(SVM_INTEGRATION_FLAGS_PARSE_POST_VALUES);
      }

      if (_CustType->IsFastRefresh()) {
         _CustFlags->SetCustomizationFlags(CUSTOMIZATION_FLAG_FAST_REFRESH_COMPLETED);
      }

      //
      // Set customization status to success
      // License activation does not affect this
      //
      _CustState->SetCustomizationState(CUSTOMIZATION_STATE_SUCCESS, SVM_PS_SUCCESS,
                                        NGVC_CATEGORY_CUSTOMIZATION,
                                        NGVC_INFO_CUSTOMIZATION_SUCCEEDED, ERROR_SUCCESS);

      //
      // Mark Customization Complete so we know not try to complete it
      // again.
      //
      _cpi->MarkCloneCustomizationCompleted();

      if (_CustFlags->GetCustomizationFlagsValue() == CUSTOMIZATION_FLAG_NONE) {
         SYSMSG_FUNC(Debug, _T("Total Clone Customization Time: %dms"),
                     _Support->GetTotalCloneICCustomizationTime(false));
      } else if (_CustFlags->IsFlagOn(CUSTOMIZATION_FLAG_SHUTDOWN_COMPLETED) ||
                 _CustFlags->IsFlagOn(CUSTOMIZATION_FLAG_REBOOT_COMPLETED)) {
         SYSMSG_FUNC(Debug,
                     _T("Total Clone Customization Time Post-Shutdown or ")
                     _T("Reboot: %dms"),
                     _Support->GetTotalCloneICCustomizationTime(true));
      }

      //
      // Start Post Customization Thread
      //
      CreatePostCustomizationThread();
   } else {
      //
      // If we are here, a shutdown and/or reboot has not been
      // processed
      //
      return ProcessShutdownOrReboot();
   }

   return true;
}

bool
Customization::OkToCompleteCustomization()
{
   bool bDone = false;

   //
   // Check if we need to complete customization or process a
   // shutdown/reboot first
   //
   if (_CustType->IsClonePrep()) {
      if (_cpi->IsCloneDomainJoined() && _cpi->IsMacAddressReset() &&
          (_cpi->IsCloneMachinePasswordChanged() || !_cpi->IsCloneMachinePwdChangeEnabled()) &&
          !_cpi->IsCloneCustomizationCompleted()) {
         SYSMSG_FUNC(Debug, _T("Cloneprep Ready To Complete Customization"));
         bDone = true;
      }
   } else if (_CustType->IsSysPrep()) {
      if (_cpi->IsCloneDomainJoined() && !_cpi->IsCloneCustomizationCompleted()) {
         SYSMSG_FUNC(Debug, _T("Sysprep Ready To Complete Customization"));
         bDone = true;
      }
   } else if (_CustType->IsUniversalPrep()) {
      if (_cpi->IsCloneDomainJoined() && !_cpi->IsCloneCustomizationCompleted()) {
         SYSMSG_FUNC(Debug, _T("Universalprep Ready To Complete Customization"));
         bDone = true;
      }
   } else {
      SYSMSG_FUNC(Error, _T("Unknown Customization Type: %d"), _CustType->GetCustomizationType());
   }

   //
   // If customization isn't finished, cannot complete customization
   //
   if (bDone == false) {
      return false;
   }

   //
   // If no flags set we or all required shutdown/reboots are completed
   // we can complete customization
   //
   if (!_CustFlags->IsFlagOn(CUSTOMIZATION_FLAG_REBOOT_REQUIRED) &&
       !_CustFlags->IsFlagOn(CUSTOMIZATION_FLAG_SHUTDOWN_REQUIRED)) {
      //
      // Neither required, customization is done, so we can complete
      //
      SYSMSG_FUNC(Debug, _T("No flags set, completing customization"));
      return true;
   } else if (_CustFlags->IsFlagOn(CUSTOMIZATION_FLAG_REBOOT_REQUIRED) &&
              _CustFlags->IsFlagOn(CUSTOMIZATION_FLAG_SHUTDOWN_REQUIRED)) {
      if (_CustFlags->IsFlagOn(CUSTOMIZATION_FLAG_REBOOT_COMPLETED) &&
          _CustFlags->IsFlagOn(CUSTOMIZATION_FLAG_SHUTDOWN_COMPLETED)) {
         //
         // Both shutdown and reboot requird and completed
         //
         SYSMSG_FUNC(Debug, _T("Shutdown and reboot were required and have ")
                            _T("completed, completing customization"));
         return true;
      }
   } else if (_CustFlags->IsFlagOn(CUSTOMIZATION_FLAG_SHUTDOWN_REQUIRED)) {
      if (_CustFlags->IsFlagOn(CUSTOMIZATION_FLAG_SHUTDOWN_COMPLETED)) {
         //
         // Only shutdown required and completed
         //
         SYSMSG_FUNC(Debug, _T("Only a shutdown was required and has ")
                            _T("completed, completing customization"));
         return true;
      }
   } else if (_CustFlags->IsFlagOn(CUSTOMIZATION_FLAG_REBOOT_REQUIRED)) {
      if (_CustFlags->IsFlagOn(CUSTOMIZATION_FLAG_REBOOT_COMPLETED)) {
         //
         // Only reboot required and completed
         //
         SYSMSG_FUNC(Debug, _T("Only a reboot was required and has completed, ")
                            _T("completing customization"));
         return true;
      }
   }

   return false;
}

bool
Customization::ProcessShutdownOrReboot()
{
   int flags = 0;

   //
   // Process shutdown and/or reboot
   //
   if (_CustFlags->IsFlagOn(CUSTOMIZATION_FLAG_SHUTDOWN_REQUIRED) &&
       !_CustFlags->IsFlagOn(CUSTOMIZATION_FLAG_SHUTDOWN_COMPLETED)) {
      //
      // Shutdown is required, but not completed yet, so do the
      // shutdown now
      //
      flags = _CustFlags->SetCustomizationFlags(CUSTOMIZATION_FLAG_SHUTDOWN_REQUESTED);
      SYSMSG_FUNC(Debug, _T("Set shutdown requested flag"));

      SYSMSG_FUNC(Debug, _T("Total Clone Customization Time Pre-Shutdown: %dms"),
                  _Support->GetTotalCloneICCustomizationTime(false));

      SYSMSG_FUNC(Debug, _T("Initiating required agent shutdown"));
      _System->Shutdown();
   } else if (_CustFlags->IsFlagOn(CUSTOMIZATION_FLAG_REBOOT_REQUIRED) &&
              !_CustFlags->IsFlagOn(CUSTOMIZATION_FLAG_REBOOT_COMPLETED)) {
      //
      // Reboot is required, but not completed yet, so do the reboot
      // now
      //
      flags = _CustFlags->SetCustomizationFlags(CUSTOMIZATION_FLAG_REBOOT_REQUESTED);
      SYSMSG_FUNC(Debug, _T("Set reboot requested flag"));

      SYSMSG_FUNC(Debug, _T("Total Clone Customization Time Pre-Reboot: %dms"),
                  _Support->GetTotalCloneICCustomizationTime(false));

      SYSMSG_FUNC(Debug, _T("Initiating required agent reboot"));
      _System->Reboot();
   }

   return true;
}

bool
Customization::OkToCustomizeClone()
{
   std::string status;

   //
   // Make sure NGA customization succeeded and we have not already customized
   //
   if (!_CustState->GetCustomizationState(status)) {
      SYSMSG_FUNC(Debug, _T("Clone: %S"),
                  _CustState->GetDescription(SVM_PS_NO_CUSTOMIZATION_STATE).c_str());
      _CustState->SetCustomizationState(CUSTOMIZATION_STATE_ERROR, SVM_PS_NO_CUSTOMIZATION_STATE,
                                        NGVC_CATEGORY_CUSTOMIZATION_STATE, ERROR_SUCCESS);
      return false;
   } else {

      if (status.compare(CUSTOMIZATION_STATE_RUNNING) != 0) {
         if (status.compare(CUSTOMIZATION_STATE_SUCCESS) == 0 &&
             _cpi->IsCloneCustomizationCompleted()) {
            SYSMSG_FUNC(Debug, _T("Clone: Customization Has Already Completed Successfully"));
            return false;
         }

         if (status.compare(CUSTOMIZATION_STATE_SPECIALIZE) == 0 ||
             status.compare(CUSTOMIZATION_STATE_OOBE) == 0 ||
             status.compare(CUSTOMIZATION_STATE_SNAPSHOTTING) == 0 ||
             status.compare(CUSTOMIZATION_STATE_POST_SNAPSHOTTING) == 0) {
            SYSMSG_FUNC(Debug, _T("Clone: Sysprep Customization In Progress"));
         } else {
            //
            // Customization failed in the nga, cannot continue
            //
            SYSMSG_FUNC(Debug, _T("Clone: Customization failed in nga, cannot continue"));

            SYSMSG_FUNC(Debug, _T("Total Clone Customization Time: %dms"),
                        _Support->GetTotalCloneICCustomizationTime(true));

            //
            // Tell the View Agent we failed too
            //
            _cpi->SetCustomizationResult(SVM_PS_UNKNOWN_FAILURE, CustomizationFailed);

            return false;
         }
      }
   }

   return true;
}

bool
Customization::VerifyNGAConfig()
{
   SYSMSG_FUNC(Debug, _T("VerifyNGAConfig Started"));

   HRESULT hr = S_OK;
   int i = 0;
   std::vector<std::wstring> strings;

   SYSMSG_FUNC(Debug, _T("smKey Opened"));

   hr = _Registry->GetValue(SVM_BOOTEXECUTE_VALUE_NAME, strings, RegType::SessionMgr);
   if (FAILED(hr)) {
      SYSMSG_FUNC(Error, L"Failed Reading BootExecute: 0x%X", hr);
      return false;
   }

   for (i = 0; i < strings.size(); i++) {
      if (strings[i].length() > 1) {
         if (wcsicmp(strings[i].c_str(), SVM_BOOTEXECUTE_VALUE) == 0) {
            SYSMSG_FUNC(Debug, _T("VerifyNGAConfig Succeeded"));
            return true;
         }
      }
   }

   //
   // If we get to here, the BootExecute value needs updated.
   //
   bool ret = false;
   ret = UpdateBootExecute(strings);
   if (ret == false) {
      SYSMSG_FUNC(Debug, _T("Failed Updating %ws"), SVM_BOOTEXECUTE_VALUE_NAME);
      SYSMSG_FUNC(Debug, _T("VerifyNGAConfig Failed"));
   } else {
      SYSMSG_FUNC(Debug, _T("Updated %ws"), SVM_BOOTEXECUTE_VALUE_NAME);
      SYSMSG_FUNC(Debug, _T("VerifyNGAConfig Succeeded"));
   }

   return ret;
}

bool
Customization::UpdateBootExecute(const std::vector<std::wstring> strings)
{
   //
   // If we get to here, the bootexecute value needs updated.
   //
   HRESULT hr = S_OK;
   SIZE_T i = 0;
   size_t total_len = 0;
   DWORD err = ERROR_SUCCESS;
   TCHAR *szMultiSz = NULL;
   TCHAR *pPtr = NULL;
   TCHAR *szTemp = NULL;
   std::vector<std::wstring> stringsW;

   //
   // Compute length and convert strings to stringsW
   //
   total_len = 0;
   for (i = 0; i < strings.size(); i++) {
      szTemp = (TCHAR *)HeapAlloc(GetProcessHeap(), HEAP_ZERO_MEMORY,
                                  (strings[i].length() + 1) * sizeof(TCHAR));
      if (szTemp == NULL) {
         SYSMSG_FUNC(Debug, _T("HeapAlloc Failed: 0x%X"), HRESULT_FROM_WIN32(GetLastError()));
         return false;
      }

      stringsW.push_back(strings[i]);
      total_len += stringsW[i].length() + 1;

      if (szTemp != NULL) {
         HeapFree(GetProcessHeap(), 0, szTemp);
         szTemp = NULL;
      }
   }
   total_len += wcslen(SVM_BOOTEXECUTE_VALUE) + 1;

   //
   // Generate value
   //
   szMultiSz = (TCHAR *)HeapAlloc(GetProcessHeap(), HEAP_ZERO_MEMORY, total_len * sizeof(TCHAR));
   if (szMultiSz == NULL) {
      SYSMSG_FUNC(Debug, _T("HeapAlloc Failed: 0x%X"), HRESULT_FROM_WIN32(GetLastError()));
      return false;
   }

   pPtr = szMultiSz;
   for (i = 0; i < strings.size(); i++) {
      if (stringsW[i].length() > 1) {
         wcscpy(pPtr, stringsW[i].c_str());
         pPtr += stringsW[i].length() + 1;
      }
   }
   wcscpy(pPtr, SVM_BOOTEXECUTE_VALUE);

   //
   // Set BootExecute value
   //
   hr = _Registry->SetValue(SVM_BOOTEXECUTE_VALUE_NAME, (BYTE *)szMultiSz,
                            total_len * sizeof(TCHAR), RegType::SessionMgr);
   if (FAILED(hr)) {
      SYSMSG_FUNC(Debug, _T("Failed Updating %ws, 0x%X"), SVM_BOOTEXECUTE_VALUE_NAME, err);
      return false;
   }

   if (szMultiSz != NULL) {
      HeapFree(GetProcessHeap(), 0, szMultiSz);
      szMultiSz = NULL;
   }

   return true;
}

bool
Customization::RunPreShutdownRoutine()
{
   // Backup machine password to guest info for sysprep domain join fast refresh
   _CustFlags->GetCustomizationFlagsValue();

   if (_CustFlags->IsFlagOn(CUSTOMIZATION_FLAG_SYSPREP_DOMAIN_JOIN) &&
       _CustFlags->IsFlagOn(CUSTOMIZATION_FLAG_RESYNC_CLEANUP)) {

      SYSMSG_FUNC(Debug, _T("Backup machine password for sysprep domain join"));
      if (!_DomainJoin->BackupMachinePassword()) {
         _GuestInfo->Log("Failed to backup machine password for HIC sysprep domain join");
      } else {
         _GuestInfo->Log("Successfull backup of machine password for HIC "
                         "sysprep domain join");
      }
   }

   // Skip running pre-shutdown script during sysprep/unversalprep customization
   if ((_CustType->IsSysPrep() || _CustType->IsUniversalPrep()) &&
       !_cpi->IsCloneCustomizationCompleted()) {
      SYSMSG_FUNC(Debug, _T("Skipping pre-shutdown script execution ")
                         _T("until clone customization is completed."));
      return true;
   }

   return _Script->RunPreShutdownScript();
}

bool
Customization::IsPreShutdownScriptConfigured()
{
   return _Script->IsPreShutdownScriptConfigured();
}

void
Customization::CreatePostCustomizationThread()
{
   boost::thread *thr = new boost::thread(PostCustomizationWorker, this);
   _thread = boost::shared_ptr<boost::thread>(thr);
}

void
Customization::PostCustomizationWorker(void *p)
{
   Customization *pCust = (Customization *)p;

   //
   // Call gpupdate if enabled
   //
   pCust->_GroupPolicy->UpdateGroupPolicy();

   //
   // Log UPN
   //
   pCust->_DomainJoin->LogUPN();

   //
   // Activate License
   //
   pCust->_License->Activate();
}

bool
Customization::PreparePersistentDisks()
{
   //
   // Prepare The Persistent Disks
   //
   if (_CustFlags->IsFlagOn(CUSTOMIZATION_FLAG_PERSISTENT_DISKS_ENABLED)) {
      return _PersistentDisks->InitializeDisks();
   }

   SYSMSG_FUNC(Debug, L"Persistent Disks Disabled");

   return true;
}

#if ENABLE_BASE_APP_MERGE
bool
Cloneprep::WaitForAVBaseMerge()
{
   HRESULT hr = S_OK;
   bool bResult = false;
   DWORD dwBaseMergeStatus = AV_BASE_MERGE_NONE;

   reg::ExistingRegKey avKey(HKEY_LOCAL_MACHINE, AGENT_INTEGRATION_AV_KEYPATH,
                             KEY_NOTIFY | KEY_READ | KEY_WOW64_64KEY, hr);
   if (FAILED(hr)) {
      SYSMSG_FUNC(Error, L"Failed Opening Key: %ws, 0x%X", AGENT_INTEGRATION_AV_KEYPATH, hr);
      return false;
   }

   avKey.ReadDwordValueEx(AV_BASE_MERGE_VALUE_NAME, AV_BASE_MERGE_NONE, dwBaseMergeStatus);
   SYSMSG_FUNC(Debug, _T("%ws: %d"), AV_BASE_MERGE_VALUE_NAME, dwBaseMergeStatus);

   const std::wstring SvmAVBaseMergeMaxWaitTimeValueName = _T("AVBaseMergeMaxWaitTime");
   DWORD dwDuration = DEFAULT_AV_BASE_MERGE_WAIT_TIME;

   _Registry->GetGaServiceRegVal(SvmAVBaseMergeMaxWaitTimeValueName,
                                 DEFAULT_AV_BASE_MERGE_WAIT_TIME, dwDuration);

   if (dwBaseMergeStatus == AV_BASE_MERGE_RUNNING) {
      SYSMSG_FUNC(Debug, _T("About to wait for AV Base Merge. Max Wait Time: %dms"), dwDuration);

      hr = avKey.WaitForDwordValue(AV_BASE_MERGE_VALUE_NAME, AV_BASE_MERGE_COMPLETE, dwDuration,
                                   bResult);
      if (SUCCEEDED(hr) && bResult) {
         SYSMSG_FUNC(Debug, _T("AV Base Merge Finished"));
      } else {
         SYSMSG_FUNC(Debug, _T("Wait for AV Base Merge Failed"));
         return false;
      }
   } else {
      SYSMSG_FUNC(Debug, _T("AV Base Merge Skipped"));
   }

   return true;
}
#endif