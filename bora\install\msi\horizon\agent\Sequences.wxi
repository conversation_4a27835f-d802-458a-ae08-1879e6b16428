<?xml version="1.0" encoding="utf-8"?>

<!--
*******************************************************************************
   Copyright (c) Omnissa, LLC. All rights reserved.
   This product is protected by copyright and intellectual property laws in the
   United States and other countries as well as by international treaties.
   - Omnissa Restricted

   Sequences.wxi  (product: Horizon Agent)

      WiX include for all custom action sequences in this product.
*******************************************************************************
-->

<Include>

   <!-- Keep these actions ordered by sequence low -> high -->

   <AdminUISequence>
      <Show Dialog="SetupInitialization" Sequence="50" />
      <Show Dialog="AdminWelcome" Sequence="1010" />
      <Show Dialog="SetupProgress" Sequence="1020" />

      <Show Dialog="SetupCompleteError" OnExit="error" />
      <Show Dialog="SetupCompleteSuccess" OnExit="success" />
      <Show Dialog="SetupInterrupted" OnExit="cancel" />
   </AdminUISequence>


   <InstallUISequence>
      <Show Dialog="SetupInitialization" Sequence="1" />
      <Custom Action="VM_ClearVMREBOOT" Sequence="5">VMREBOOT And RESUME</Custom>
      <Custom Action="VM_SetVdmInstallerChecks" Sequence="6">%VDM_INSTALLER_CHECKS And Not VDM_INSTALLER_CHECKS</Custom>
      <Custom Action="VM_SetVdmSkipBrokerRegistration" Sequence="8">%VDM_SKIP_BROKER_REGISTRATION And Not VDM_SKIP_BROKER_REGISTRATION</Custom>
      <Custom Action="VM_SetALLUSERS" Sequence="9" />
      <Custom Action="HZ_Check_AgentRegistryMigrated" Before="AppSearch">Not Installed</Custom>
      <Custom Action="HZ_CopyRegTree_Migration_Imm" After="HZ_Check_AgentRegistryMigrated">Not Installed AND AGENT_REGISTRY_MIGRATION_DONE="0"</Custom>
      <Custom Action="VM_ReadInstallerSettingsFile" Before="AppSearch">SETTINGS_FILE</Custom>
      <Custom Action="VM_ErrSettingsFileInvalid" After="VM_ReadInstallerSettingsFile">SETTINGS_FILE And SettingsFileErrorLine</Custom>
      <AppSearch Sequence="304" />
      <Custom Action="VM_SetVdmForceDesktopAgent" After="AppSearch">VDM_FORCE_DESKTOP_AGENT</Custom>
      <Custom Action="VM_CheckWindowsVersion" After="AppSearch" />
      <Custom Action="VM_CheckSupportedHznviddVersion" After="VM_CheckWindowsVersion" />
      <Custom Action="VM_CheckWindowsWVD" After="AppSearch" />
      <Custom Action="VM_IsTeraHostCardPresent" After="AppSearch" />
      <Custom Action="VM_CheckVDMLogsDirOverride" After="AppSearch">REG_VDM_LOGS_OVERRIDE</Custom>
      <Custom Action="VM_SetVersionNT_Win10" After="VM_SetVdmForceDesktopAgent">WINDOWS_MAJOR_VERSION ~= "#10"</Custom>
      <Custom Action="VM_SetVersionNT64_Win10" After="VM_SetVersionNT_Win10">WINDOWS_MAJOR_VERSION ~= "#10"</Custom>
      <Custom Action="VM_CheckWindowsUpdateAndRestartPending" After="AppSearch"/>
      <Custom Action="VM_AppSearch" After="AppSearch" />
      <Custom Action="VM_SetToolsDir32" After="AppSearch">Not TOOLSDIR</Custom>
      <Custom Action="VM_CheckRunningInVm" After="AppSearch" />
      <Custom Action="VM_SetIsAzureManagedDeployment" After="VM_CheckRunningInVm">IsAzure=1 And HORIZON_CLOUD_DEPLOYMENT=1</Custom>
      <Custom Action="VM_SetVM_TYPE_REG" After="VM_SetIsAzureManagedDeployment">Not VM</Custom>
      <Custom Action="VM_SetPcoipFeatureDesc" After="VM_SetVM_TYPE_REG">Not VM</Custom>
      <Custom Action="VM_CheckForClientSession" After="VM_SetPcoipFeatureDesc">VDM_INSTALLER_CHECKS&lt;&gt;0</Custom>
      <Custom Action="VM_ErrClientRunning" After="VM_CheckForClientSession">CLIENTRUNNING</Custom>
      <Custom Action="VM_SetForceDesktopAgent" After="VM_ErrClientRunning">Not (MsiNTProductType=1 Or TerminalServer or VDM_INSTALLER_CHECKS=0) And Not VdmForceDesktopAgent=1</Custom>
      <Custom Action="VM_SetVCManaged" After="VM_SetForceDesktopAgent" />
      <LaunchConditions Sequence="415" />
      <Custom Action="VM_SetUrlRedirectionWarnText" Sequence="418" />
      <Show Dialog="MessageBox" Sequence="419" >(URL_FILTERING_ENABLED=1 Or REG_URL_FILTERING_ENABLED=1) And REG_URL_FILTERING_ENABLED_SIDE="Client"</Show>
      <Custom Action="VM_SetUNCRedirectionWarnText" Sequence="420" />
      <Show Dialog="UNCMessageBox" Sequence="421" >(ENABLE_UNC_REDIRECTION=1 Or REG_ENABLE_UNC_REDIRECTION=1) And REG_ENABLE_UNC_REDIRECTION_SIDE="Client"</Show>
      <Custom Action="VM_PopulateUpgradeTable" Before="FindRelatedProducts" />
      <FindRelatedProducts Sequence="430" />
      <Custom Action="VM_GetInstalledProductVersion_NEWPRODUCTFOUND_SD" After="FindRelatedProducts">NEWPRODUCTFOUND</Custom>
      <Custom Action="VM_GetInstalledProductVersion_SAMEVERSIONDETECTED_SD" After="VM_GetInstalledProductVersion_NEWPRODUCTFOUND_SD">SAMEVERSIONDETECTED</Custom>
      <Custom Action="VM_GetInstalledProductVersion" After="VM_GetInstalledProductVersion_SAMEVERSIONDETECTED_SD">NEWPRODUCTFOUND Or SAMEVERSIONDETECTED</Custom>
      <Custom Action="VM_ErrDowngradeDetectedUI" After="VM_GetInstalledProductVersion">NEWPRODUCTFOUND Or (SAMEVERSIONDETECTED And REG_BUILD_NUMBER &gt; BuildNumber And BuildNumber &gt; 0)</Custom>
      <Custom Action="VM_MustRebootCheck" After="VM_ErrDowngradeDetectedUI" />
      <Custom Action="VM_ErrWindowsUpdateAndRestartPending" After="VM_MustRebootCheck">VDM_WINDOWS_UPDATE_AND_RESTART_PENDING=1 And Not VDM_SKIP_WINDOWS_UPDATE_CHECK=1 And Not IGNORE_PENDING_REBOOTS=1</Custom>
      <Custom Action="VM_CheckRebootForVMwareComponents" After="VM_ErrWindowsUpdateAndRestartPending" />
      <Custom Action="VM_MustReboot" After="VM_CheckRebootForVMwareComponents">(VMREBOOT And Not VM_COMPONENT_REBOOT_REQUESTED) And Not Installed And Not UPGRADINGPRODUCTCODE And Not IGNORE_PENDING_REBOOTS=1</Custom>
      <Custom Action="VM_SetWIX_ACCOUNT_ADMINISTRATORS_BLAST" Sequence="440" />
      <Custom Action="VM_SetOLDPRODUCTFOUND" After="VM_SetWIX_ACCOUNT_ADMINISTRATORS_BLAST">(OLDPRODUCT_ALPHA Or OLDPRODUCT_BETA Or OLDPRODUCT_BETA2 Or OLDPRODUCT_RC) And Not OLDPRODUCTFOUND</Custom>
      <Custom Action="VM_GetInstalledFeatureState_SVI_SD" After="VM_SetOLDPRODUCTFOUND">OLDPRODUCTFOUND</Custom>
      <Custom Action="VM_GetInstalledFeatureState_SVI" After="VM_GetInstalledFeatureState_SVI_SD">OLDPRODUCTFOUND</Custom>
      <Custom Action="VM_ErrSVIFeatureInstalled" After="VM_GetInstalledFeatureState_SVI">OLDPRODUCTFOUND And InstallState=3</Custom>
      <!-- VM_ErrManualUninstallRequired needs to be after VM_SetOLDPRODUCTFOUND -->
      <Custom Action="VM_ErrManualUninstallRequired" Sequence="449">OLDPRODUCTFOUND And Not Installed And (FEATURE_SCANNER_INSTALLED Or FEATURE_SERIAL_INSTALLED)</Custom>
      <Custom Action="VM_SetWIX_ACCOUNT_USERS_BLAST" Sequence="450" />
      <Custom Action="VM_GetInstalledFeatureState_ThinPrint_SD" Sequence="451">OLDPRODUCTFOUND And Not Installed And Not Preselected</Custom>
      <Custom Action="VM_GetInstalledFeatureState_ThinPrint" After="VM_GetInstalledFeatureState_ThinPrint_SD">OLDPRODUCTFOUND And Not Installed And Not Preselected</Custom>
      <Custom Action="VM_SetEnablePrintRedir" After="VM_GetInstalledFeatureState_ThinPrint">InstallState=3</Custom>
      <Custom Action="VM_ErrWSWCInstalled" Sequence="482">$(var.WSWCUpgradeActionProperty)</Custom>
      <Custom Action="VM_ErrServerInstalled" Sequence="491">$(var.ServerUpgradeActionProperty)</Custom>
      <Custom Action="VM_ErrUnsupportedOldVersion" Sequence="495">AGENT_20BETA1 And Not Installed</Custom>
      <CCPSearch Sequence="500">CCP_TEST</CCPSearch>
      <RMCCPSearch Sequence="600">Not CCP_SUCCESS And CCP_TEST</RMCCPSearch>
      <ValidateProductID Sequence="700" />
      <Custom Action="VM_SetServerOsValid" Sequence="751">MsiNTProductType=3 And VersionNT64&gt;603</Custom>
      <CostInitialize Sequence="800" />
      <FileCost Sequence="900" />
      <IsolateComponents Sequence="950" />
      <Custom Action="VM_SetToolsDir" Sequence="955" />
      <Custom Action="setUserProfileNT" Sequence="960" />
      <Custom Action="setAllUsersProfile2K" Sequence="980" />
      <ResolveSource Sequence="990">Not Installed</ResolveSource>
      <CostFinalize Sequence="1000" />
      <MigrateFeatureStates Sequence="1200" />
      <Custom Action="VM_EnablePrintRedir_SD" After="MigrateFeatureStates">VMEnablePrintRedir=1</Custom>
      <Custom Action="VM_EnablePrintRedir" After="VM_EnablePrintRedir_SD">VMEnablePrintRedir=1</Custom>
      <Custom Action="VM_CheckSpoolerServiceRunning" After="VM_EnablePrintRedir" />
      <Show Dialog="InstallWelcome" Sequence="1210">Not Installed</Show>
      <Show Dialog="SetupResume" Sequence="1220">Installed And (RESUME Or Preselected)</Show>
      <Show Dialog="MaintenanceWelcome" Sequence="1230">Installed And Not RESUME And Not Preselected</Show>
      <Show Dialog="SetupProgress" Sequence="1240">Not ExitInstallEarly</Show>
      <ExecuteAction Sequence="1300">Not ExitInstallEarly</ExecuteAction>
      <Custom Action="VM_CheckReboot" Sequence="1301" />
      <Custom Action="VM_SetREBOOTPROMPT" After="VM_CheckReboot">(AUTORESTART_SELECTED_UNINST=1 Or AUTORESTART_SELECTED_INST=1)</Custom>
      <ScheduleReboot After="VM_SetREBOOTPROMPT">InstallOSRolesAndFeaturesSuccessful</ScheduleReboot>

      <Show Dialog="SetupCompleteError" OnExit="error" />
      <Show Dialog="SetupCompleteSuccess" OnExit="success" >Not (AUTORESTART_SELECTED_UNINST=1 Or AUTORESTART_SELECTED_INST=1)</Show>
      <Show Dialog="SetupInterrupted" OnExit="cancel" />
   </InstallUISequence>


   <InstallExecuteSequence>
      <Custom Action="VM_ClearVMREBOOT" Sequence="5">VMREBOOT And RESUME</Custom>
      <Custom Action="VM_SetVdmInstallerChecks" Sequence="6">%VDM_INSTALLER_CHECKS And Not VDM_INSTALLER_CHECKS</Custom>
      <Custom Action="VM_SetALLUSERS" Sequence="8" />
      <Custom Action="VM_SetIntelUnsDesc" Sequence="11" />
      <Custom Action="HZ_Check_AgentRegistryMigrated" Before="AppSearch">Not Installed</Custom>
      <Custom Action="HZ_CopyRegTree_Migration_Imm" After="HZ_Check_AgentRegistryMigrated">Not Installed AND AGENT_REGISTRY_MIGRATION_DONE="0"</Custom>
      <Custom Action="VM_ReadInstallerSettingsFile" Before="AppSearch">SETTINGS_FILE</Custom>
      <Custom Action="VM_ErrSettingsFileInvalid" After="VM_ReadInstallerSettingsFile">SETTINGS_FILE And SettingsFileErrorLine</Custom>
      <AppSearch Sequence="300" />
      <Custom Action="SetVMWARE_AGENT_INSTALLPATH_CERT" After="AppSearch">VMWARE_AGENT_INSTALLPATH</Custom>
      <Custom Action="VM_SetVdmForceDesktopAgent" After="AppSearch">VDM_FORCE_DESKTOP_AGENT</Custom>
      <Custom Action="VM_SetForceDesktopAgent" After="VM_SetVdmForceDesktopAgent">Not (MsiNTProductType=1 Or TerminalServer or VDM_INSTALLER_CHECKS=0) And Not VdmForceDesktopAgent=1</Custom>
      <Custom Action="VM_CheckWindowsVersion" After="AppSearch" />
      <Custom Action="VM_CheckSupportedHznviddVersion" After="VM_CheckWindowsVersion" />
      <Custom Action="VM_CheckWindowsWVD" After="AppSearch" />
      <Custom Action="VM_IsTeraHostCardPresent" After="AppSearch" />
      <Custom Action="VM_SetIsAgentDCT" After="AppSearch" />
      <Custom Action="VM_SetVersionNT_Win10" After="AppSearch">WINDOWS_MAJOR_VERSION ~= "#10"</Custom>
      <Custom Action="VM_SetVersionNT64_Win10" After="VM_SetVersionNT_Win10">WINDOWS_MAJOR_VERSION ~= "#10"</Custom>
      <Custom Action="VM_AppSearch" After="AppSearch" />
      <Custom Action="VM_SetVDM_LOOPBACK_IP" After="AppSearch">Not Installed And UILevel&lt;5</Custom>
      <Custom Action="VM_CheckSpoolerServiceRunning" After="AppSearch" />
      <Custom Action="VM_ErrVDM_LOOPBACK_IP" After="VM_SetVDM_LOOPBACK_IP">Not Installed And UILevel&lt;5 And Not VDM_LOOPBACK_IP</Custom>
      <Custom Action="VM_SetToolsDir32" After="VM_ErrVDM_LOOPBACK_IP">Not TOOLSDIR</Custom>
      <Custom Action="VM_SetProperty_REINSTALL" After="AppSearch">Installed AND Not REMOVE="ALL" AND VDM_REINSTALL And UILevel&lt;5</Custom>
      <Custom Action="VM_SetProperty_REINSTALLMODE" After="AppSearch">Installed AND Not REMOVE="ALL" AND VDM_REINSTALLMODE And UILevel&lt;5</Custom>
      <Custom Action="VM_CheckRunningInVm" After="VM_SetToolsDir32" />
      <Custom Action="VM_SetIsAzureManagedDeployment" After="VM_CheckRunningInVm">IsAzure=1 And HORIZON_CLOUD_DEPLOYMENT=1</Custom>
      <Custom Action="VM_SetVM_TYPE_REG" After="VM_SetIsAzureManagedDeployment">Not VM</Custom>
      <Custom Action="VM_CheckForClientSession" After="VM_SetVM_TYPE_REG">VDM_INSTALLER_CHECKS&lt;&gt;0</Custom>
      <Custom Action="VM_CheckWindowsUpdateProgress" After="VM_CheckForClientSession" />
      <Custom Action="VM_CheckWindowsUpdateAndRestartPending" After="VM_CheckWindowsUpdateProgress"/>
      <Custom Action="VM_ErrClientRunning" After="VM_CheckWindowsUpdateAndRestartPending">CLIENTRUNNING</Custom>
      <Custom Action="VM_SetVCManaged" After="VM_ErrClientRunning" />
      <LaunchConditions Sequence="413" />
      <Custom Action="VM_PopulateCustomActionDetails" Before="VM_PopulateUpgradeTable" />
      <Custom Action="VM_PopulateUpgradeTable" Before="FindRelatedProducts" />
      <FindRelatedProducts Sequence="420" />
      <Custom Action="VM_SetOldVPProductCode_VP" After="FindRelatedProducts">VPFOUND</Custom>
      <Custom Action="VM_SetOldVPProductCode_VPA" After="FindRelatedProducts">OLDPRODUCTFOUND And Not VPFOUND</Custom>
      <Custom Action="VM_SetVDM_SERVER_NAME" After="FindRelatedProducts">OLDPRODUCTFOUND And Not VDM_SERVER_NAME And AGENT_CONFIGURATION_BROKER_REG</Custom>
      <Custom Action="VM_ErrDowngradeDetectedSilent" After="FindRelatedProducts">UILevel&lt;3 And NEWPRODUCTFOUND Or (SAMEVERSIONDETECTED And REG_BUILD_NUMBER &gt; BuildNumber And BuildNumber &gt; 0)</Custom>
      <Custom Action="VM_MustRebootCheck" After="VM_ErrDowngradeDetectedSilent" />
      <Custom Action="VM_ErrWindowsUpdateAndRestartPending" After="VM_MustRebootCheck">VDM_WINDOWS_UPDATE_AND_RESTART_PENDING=1 And Not VDM_SKIP_WINDOWS_UPDATE_CHECK=1 And Not IGNORE_PENDING_REBOOTS=1</Custom>
      <Custom Action="VM_CheckRebootForVMwareComponents" After="VM_ErrWindowsUpdateAndRestartPending" />
      <Custom Action="VM_MustReboot" After="VM_CheckRebootForVMwareComponents">(VMREBOOT And Not VM_COMPONENT_REBOOT_REQUESTED) And Not Installed And Not UPGRADINGPRODUCTCODE And Not IGNORE_PENDING_REBOOTS=1</Custom>
      <Custom Action="VM_SetOLDPRODUCTFOUND" After="VM_MustReboot">(OLDPRODUCT_ALPHA Or OLDPRODUCT_BETA Or OLDPRODUCT_BETA2 Or OLDPRODUCT_RC) And Not OLDPRODUCTFOUND</Custom>
      <Custom Action="VM_ErrWindowsUpdateInProgress" After="VM_SetOLDPRODUCTFOUND">VDM_WINDOWS_UPDATE_IN_PROGRESS=1 And Not Installed And Not VDM_SKIP_WINDOWS_UPDATE_CHECK=1</Custom>
      <Custom Action="VM_GetInstalledFeatureState_SVI_SD" After="VM_SetOLDPRODUCTFOUND">OLDPRODUCTFOUND</Custom>
      <Custom Action="VM_GetInstalledFeatureState_SVI" After="VM_GetInstalledFeatureState_SVI_SD">OLDPRODUCTFOUND</Custom>
      <Custom Action="VM_ErrSVIFeatureInstalled" After="VM_GetInstalledFeatureState_SVI">OLDPRODUCTFOUND And InstallState=3</Custom>
      <Custom Action="VM_ErrManualUninstallRequired" After="VM_SetOLDPRODUCTFOUND">OLDPRODUCTFOUND And Not Installed And (FEATURE_SCANNER_INSTALLED Or FEATURE_SERIAL_INSTALLED)</Custom>
      <Custom Action="VM_SetWIX_ACCOUNT_ADMINISTRATORS_BLAST" Sequence="440" />
      <Custom Action="VM_SetWIX_ACCOUNT_USERS_BLAST" Sequence="450" />
      <Custom Action="VM_GetInstalledFeatureState_ThinPrint_SD" Sequence="451">OLDPRODUCTFOUND And Not Installed And Not Preselected</Custom>
      <Custom Action="VM_GetInstalledFeatureState_ThinPrint" After="VM_GetInstalledFeatureState_ThinPrint_SD">OLDPRODUCTFOUND And Not Installed And Not Preselected</Custom>
      <Custom Action="VM_SetEnablePrintRedir" After="VM_GetInstalledFeatureState_ThinPrint">InstallState=3</Custom>
      <Custom Action="VM_ErrWSWCInstalled" Sequence="465">$(var.WSWCUpgradeActionProperty)</Custom>
      <Custom Action="VM_ValidateINSTALLDIR" Sequence="470">INSTALLDIR</Custom>
      <Custom Action="VM_ErrServerInstalled" Sequence="482">$(var.ServerUpgradeActionProperty)</Custom>
      <CCPSearch Sequence="500">CCP_TEST</CCPSearch>
      <Custom Action="VM_ErrUnsupportedOldVersion" Sequence="587">AGENT_20BETA1 And Not Installed</Custom>
      <RMCCPSearch Sequence="600">Not CCP_SUCCESS And CCP_TEST</RMCCPSearch>
      <ValidateProductID Sequence="700" />
      <Custom Action="VM_SetServerOsValid" Sequence="751">MsiNTProductType=3 And VersionNT64&gt;603</Custom>
      <CostInitialize Sequence="800" />
      <FileCost Sequence="900" />
      <IsolateComponents Sequence="950" />
      <Custom Action="VM_SetToolsDir" Sequence="975" />
      <CostFinalize Sequence="1000" />
      <SetODBCFolders Sequence="1100" />
      <MigrateFeatureStates Sequence="1200" />

      <!--
         Default feature states are set by this point
      -->

      <Custom Action="VM_EnablePrintRedir_SD" After="MigrateFeatureStates">VMEnablePrintRedir=1</Custom>
      <Custom Action="VM_EnablePrintRedir" After="VM_EnablePrintRedir_SD">VMEnablePrintRedir=1</Custom>

      <!-- These are helper actions to simplify conditions later in the sequence -->
      <Custom Action="VM_SetNGVC_INSTALLING" After="VM_EnablePrintRedir">&amp;NGVC=3 Or (!NGVC=3 And REINSTALL)</Custom>

      <!-- Determine if we need to treat this machine as 'managed' or not -->
      <Custom Action="VM_SetVDM_VC_MANAGED_AGENT" After="VM_SetNGVC_INSTALLING">
         VDM_VC_MANAGED_AGENT=1 And TerminalServer And Not NGVC_INSTALLING
      </Custom>

      <!-- Determine if the install requires broker connection to be made up front -->
      <Custom Action="VM_SetVDM_ConnectionServerRequired" After="VM_SetVDM_VC_MANAGED_AGENT">
         Not (VDM_SKIP_BROKER_REGISTRATION=1 Or IsAzureManagedDeployment=1) And (Not VDM_VC_MANAGED_AGENT=1 Or (VDM_VC_MANAGED_AGENT=1 And TerminalServer And Not NGVC_INSTALLING))
      </Custom>

      <!-- The INSTALLDIR value is now established, populate the ARPINSTALLLOCATION property with the install path -->
      <Custom Action="SetARPINSTALLLOCATION" After="VM_SetVDM_ConnectionServerRequired">Not Installed</Custom>

      <!-- Turn off features that are selected for installation, but not supported in an IPv6 deployment -->
      <Custom Action="VM_DeselectAgentFeaturesForIPv6" After="SetARPINSTALLLOCATION">Not VDM_IP_PROTOCOL_USAGE="IPv4" AND Not Installed</Custom>

      <!-- Mark that a reboot is required if the keyboard driver is being installed -->
      <Custom Action="VM_SetVMREBOOT" After="VM_DeselectAgentFeaturesForIPv6">$_vmkbd.sys=3 And VMREBOOT&lt;&gt;1</Custom>

      <Custom Action="VM_CacheMod_UninstallRegistry.EDD77A6D_F039_46D3_8228_62AC9CE974A8" Before="InstallValidate">OLDVPPRODUCTCODE</Custom>
      <Custom Action="VM_CacheMod_UninstallRTOLogonDriver.EDD77A6D_F039_46D3_8228_62AC9CE974A8" Before="InstallValidate">OLDVPPRODUCTCODE</Custom>
      <Custom Action="VM_CacheMod_UninstallRTOLogonService.EDD77A6D_F039_46D3_8228_62AC9CE974A8" Before="InstallValidate">OLDVPPRODUCTCODE</Custom>
      <Custom Action="VM_CacheMod_UninstallWinlogonComp.EDD77A6D_F039_46D3_8228_62AC9CE974A8" Before="InstallValidate">OLDVPPRODUCTCODE</Custom>
      <Custom Action="VM_CacheMod_VM_RemoveNetworkContinuityRegValue" Before="InstallValidate">OLDPRODUCTFOUND</Custom>
      <Custom Action="VM_CacheMod_VM_RemoveFeatureStatesFromRegistry" Before="InstallValidate">HORIZON_7_AGENT_INSTALLED</Custom>

      <!--
         At this point all feature states are determined
      -->
      <InstallValidate Sequence="1400" />
      <Custom Action="SetProperty_INSTALLING" After="InstallValidate">Not Installed</Custom>
      <Custom Action="SetProperty_UNINSTALLING" After="InstallValidate">&amp;Core=2</Custom>
      <Custom Action="VM_ValidateRequestedFeatureChanges" After="InstallValidate">
         Installed And Not UNINSTALLING And UILevel&lt;5 And (ADDLOCAL Or REMOVE) And Not REINSTALL
      </Custom>
      <Custom Action="VM_ErrCloningFeaturesCanNotChange" After="VM_ValidateRequestedFeatureChanges">
         Installed And Not UNINSTALLING And UILevel&lt;5 And
         (
            (!NGVC=3 And &amp;NGVC=2) Or
            (Not !NGVC=3 And &amp;NGVC=3)
         )
      </Custom>
      <Custom Action="VM_AdminAccessRequired_ModifyRepair" After="VM_ErrCloningFeaturesCanNotChange">Installed And RestrictedUserControl=1 And Not UNINSTALLING And Not PATCH</Custom>
      <Custom Action="VM_AdminAccessRequired_Patch" After="VM_ErrCloningFeaturesCanNotChange">Installed And RestrictedUserControl=1 And Not UNINSTALLING And PATCH</Custom>

      <!--
         At this point installation/uninstallation begins
      -->
      <InstallInitialize Sequence="1501" />
      <Custom Action="VM_DeleteOldLogs" After="InstallInitialize" />
      <Custom Action="VM_CreateVDMLogsDir_SD" After="VM_DeleteOldLogs">Not Installed</Custom>
      <Custom Action="VM_CreateVDMLogsDir" After="VM_CreateVDMLogsDir_SD">Not Installed</Custom>
      <Custom Action="HZ_CopyRegTree_Migration_Def_SD" After="RemoveExistingProducts">Not Installed AND AGENT_REGISTRY_MIGRATION_DONE="0"</Custom>
      <Custom Action="HZ_CopyRegTree_Migration_Def" After="HZ_CopyRegTree_Migration_Def_SD">Not Installed AND AGENT_REGISTRY_MIGRATION_DONE="0"</Custom>
      <Custom Action="VM_WriteFailedCustomActionDetailsToRegistry_RB_SD" After="VM_CreateVDMLogsDir"></Custom>
      <Custom Action="VM_WriteFailedCustomActionDetailsToRegistry_RB" After="VM_WriteFailedCustomActionDetailsToRegistry_RB_SD"></Custom>
      <RemoveExistingProducts After="InstallInitialize" />
      <Custom Action="VM_SelfRegInit" Sequence="1525" />
      <AllocateRegistrySpace Sequence="1550">Not Installed</AllocateRegistrySpace>
      <ProcessComponents Sequence="1600" />
      <UnpublishComponents Sequence="1700" />
      <MsiUnpublishAssemblies Sequence="1750" />
      <UnpublishFeatures Sequence="1800" />
      <Custom Action="VM_SetBlast_UDP_PORT_RANGE_VDI" Before="VM_SetBlast_UDP_PORT_RANGE_RDSH"> Not TerminalServer And Not BLAST_UDP_PORT_RANGE</Custom>
      <Custom Action="VM_SetBlast_UDP_PORT_RANGE_RDSH" Before="VM_BlastUDPReleasePorts_SideChannel_RB_SD"> TerminalServer And Not BLAST_UDP_PORT_RANGE</Custom>
      <Custom Action="VM_BlastUDPReleasePorts_SideChannel_RB_SD" Before="VM_BlastUDPReleasePorts_SideChannel_SD">&amp;BlastUDP=2 Or (!BlastUDP=3 And REINSTALL)</Custom>
      <Custom Action="VM_BlastUDPReleasePorts_SideChannel_SD" Before="VM_BlastUDPReleasePorts_SideChannel_RB">&amp;BlastUDP=2 Or (!BlastUDP=3 And REINSTALL)</Custom>
      <Custom Action="VM_BlastUDPReleasePorts_SideChannel_RB" Before="VM_BlastUDPReleasePorts_SideChannel">&amp;BlastUDP=2 Or (!BlastUDP=3 And REINSTALL)</Custom>
      <Custom Action="VM_BlastUDPReleasePorts_SideChannel" Before="VM_BlastUDPReleasePorts_RB_SD">&amp;BlastUDP=2 Or (!BlastUDP=3 And REINSTALL)</Custom>
      <Custom Action="VM_BlastUDPReleasePorts_RB_SD" Before="VM_BlastUDPReleasePorts_SD">&amp;BlastUDP=2 Or (!BlastUDP=3 And REINSTALL)</Custom>
      <Custom Action="VM_BlastUDPReleasePorts_SD" Before="VM_BlastUDPReleasePorts_RB">&amp;BlastUDP=2 Or (!BlastUDP=3 And REINSTALL)</Custom>
      <Custom Action="VM_BlastUDPReleasePorts_RB" Before="VM_BlastUDPReleasePorts">&amp;BlastUDP=2 Or (!BlastUDP=3 And REINSTALL)</Custom>
      <Custom Action="VM_BlastUDPReleasePorts" Before="StopServices">&amp;BlastUDP=2 Or (!BlastUDP=3 And REINSTALL)</Custom>
      <StopServices Sequence="1900" />
      <DeleteServices Sequence="2000" />
      <Custom Action="VM_EnableStandbyMode_RB_SD" After="DeleteServices">&amp;Core=2 Or (!Core=3 And REINSTALL)</Custom>
      <Custom Action="VM_EnableStandbyMode_SD" After="VM_EnableStandbyMode_RB_SD">&amp;Core=2 Or (!Core=3 And REINSTALL)</Custom>
      <Custom Action="VM_EnableStandbyMode_RB" After="VM_EnableStandbyMode_SD">VM_EnableStandbyMode_RB</Custom>
      <Custom Action="VM_EnableStandbyMode" After="VM_EnableStandbyMode_RB">VM_EnableStandbyMode</Custom>
      <Custom Action="VM_ClosePCoIPFirewall_TCP_RB_SD" After="VM_EnableStandbyMode">$_64_pcoip_server_win32.exe=2 Or (?_64_pcoip_server_win32.exe=3 And REINSTALL)</Custom>
      <Custom Action="VM_ClosePCoIPFirewall_UDP_RB_SD" After="VM_ClosePCoIPFirewall_TCP_RB_SD">$_64_pcoip_server_win32.exe=2 Or (?_64_pcoip_server_win32.exe=3 And REINSTALL)</Custom>
      <Custom Action="VM_ClosePCoIPFirewall_SD" After="VM_ClosePCoIPFirewall_UDP_RB_SD">$_64_pcoip_server_win32.exe=2 Or (?_64_pcoip_server_win32.exe=3 And REINSTALL)</Custom>
      <Custom Action="VM_ClosePCoIPFirewall_TCP_RB" After="VM_ClosePCoIPFirewall_SD">$_64_pcoip_server_win32.exe=2 Or (?_64_pcoip_server_win32.exe=3 And REINSTALL)</Custom>
      <Custom Action="VM_ClosePCoIPFirewall_UDP_RB" After="VM_ClosePCoIPFirewall_TCP_RB">$_64_pcoip_server_win32.exe=2 Or (?_64_pcoip_server_win32.exe=3 And REINSTALL)</Custom>
      <Custom Action="VM_ClosePCoIPFirewall" After="VM_ClosePCoIPFirewall_UDP_RB">$_64_pcoip_server_win32.exe=2 Or (?_64_pcoip_server_win32.exe=3 And REINSTALL)</Custom>
      <Custom Action="VM_CloseFrameworkChannel_RB_SetData" After="VM_ClosePCoIPFirewall">&amp;Core=2 Or (!Core=3 And REINSTALL)</Custom>
      <Custom Action="VM_CloseFrameworkChannel_SetData" After="VM_CloseFrameworkChannel_RB_SetData">&amp;Core=2 Or (!Core=3 And REINSTALL)</Custom>
      <Custom Action="VM_CloseFrameworkChannel_RB" After="VM_CloseFrameworkChannel_SetData">&amp;Core=2 Or (!Core=3 And REINSTALL)</Custom>
      <Custom Action="VM_CloseFrameworkChannel" After="VM_CloseFrameworkChannel_RB">&amp;Core=2 Or (!Core=3 And REINSTALL)</Custom>
      <Custom Action="VM_RemoveRegInterceptEnabled32Wow_RB_SD" After="VM_CloseFrameworkChannel">$_32_hznsci.dll=2 Or (?_32_hznsci.dll=3 And REINSTALL)</Custom>
      <Custom Action="VM_RemoveRegInterceptEnabled32Wow_SD" After="VM_RemoveRegInterceptEnabled32Wow_RB_SD">$_32_hznsci.dll=2 Or (?_32_hznsci.dll=3 And REINSTALL)</Custom>
      <Custom Action="VM_RemoveRegInterceptEnabled32Wow_RB" After="VM_RemoveRegInterceptEnabled32Wow_SD">VM_RemoveRegInterceptEnabled32Wow_RB</Custom>
      <Custom Action="VM_RemoveRegInterceptEnabled32Wow" After="VM_RemoveRegInterceptEnabled32Wow_RB">VM_RemoveRegInterceptEnabled32Wow</Custom>
      <Custom Action="VM_RemoveRegInterceptEnabled_RB_SD" After="VM_RemoveRegInterceptEnabled32Wow">$_hznsci.dll=2 Or (?_hznsci.dll=3 And REINSTALL)</Custom>
      <Custom Action="VM_RemoveRegInterceptEnabled_SD" After="VM_RemoveRegInterceptEnabled_RB_SD">$_hznsci.dll=2 Or (?_hznsci.dll=3 And REINSTALL)</Custom>
      <Custom Action="VM_RemoveRegInterceptEnabled_RB" After="VM_RemoveRegInterceptEnabled_SD">VM_RemoveRegInterceptEnabled_RB</Custom>
      <Custom Action="VM_RemoveRegInterceptEnabled" After="VM_RemoveRegInterceptEnabled_RB">VM_RemoveRegInterceptEnabled</Custom>
      <UnregisterComPlus Sequence="2150" />
      <SelfUnregModules Sequence="2200" />
      <Custom Action="VM_UnRegisterPcoipPerf64_RB_SD" After="SelfUnregModules">$_64_pcoip_perf_provider64.dll=2</Custom>
      <Custom Action="VM_UnRegisterPcoipPerf64_RB" After="VM_UnRegisterPcoipPerf64_RB_SD">$_64_pcoip_perf_provider64.dll=2</Custom>
      <Custom Action="VM_UnRegisterPcoipPerf" After="VM_UnRegisterPcoipPerf64_RB">$_64_pcoip_perf_provider64.dll=2</Custom>
      <Custom Action="VM_RemoveRegInterceptWhiteList32Wow_RB_SD" After="VM_UnRegisterPcoipPerf">$_32_hznsci.dll=2 Or (?_32_hznsci.dll=3 And REINSTALL)</Custom>
      <Custom Action="VM_RemoveRegInterceptWhiteList32Wow_SD" Sequence="2225">$_32_hznsci.dll=2 Or (?_32_hznsci.dll=3 And REINSTALL)</Custom>
      <Custom Action="VM_RemoveRegInterceptWhiteList32Wow_RB" Sequence="2226">VM_RemoveRegInterceptWhiteList32Wow_RB</Custom>
      <Custom Action="VM_RemoveRegInterceptWhiteList32Wow" Sequence="2227">VM_RemoveRegInterceptWhiteList32Wow</Custom>
      <Custom Action="VM_RemoveRegInterceptWhiteList_RB_SD" Sequence="2228">$_hznsci.dll=2 Or (?_hznsci.dll=3 And REINSTALL)</Custom>
      <Custom Action="VM_RemoveRegInterceptWhiteList_SD" Sequence="2229">$_hznsci.dll=2 Or (?_hznsci.dll=3 And REINSTALL)</Custom>
      <Custom Action="VM_RemoveRegInterceptWhiteList_RB" Sequence="2230">VM_RemoveRegInterceptWhiteList_RB</Custom>
      <Custom Action="VM_RemoveRegInterceptWhiteList" Sequence="2231">VM_RemoveRegInterceptWhiteList</Custom>
      <Custom Action="VM_SelfUnreg_RB_SD" Sequence="2250" />
      <Custom Action="VM_SelfUnreg_SD" Sequence="2275" />
      <Custom Action="VM_SelfUnreg_RB" Sequence="2287" />
      <Custom Action="VM_SelfUnreg" Sequence="2293" />
      <UnregisterTypeLibraries Sequence="2300" />
      <RemoveODBC Sequence="2400" />
      <UnregisterFonts Sequence="2500" />
      <RemoveRegistryValues Sequence="2600" />
      <Custom Action="VM_RemoveRegistryKeys_SD" After="RemoveRegistryValues" >&amp;Core=2</Custom>
      <Custom Action="VM_RemoveRegistryKeys" After="VM_RemoveRegistryKeys_SD">VM_RemoveRegistryKeys</Custom>
      <Custom Action="VM_InstallWsnmWinlogonNotificationHandlerUninstall" After="VM_RemoveRegistryKeys">&amp;Core=2</Custom>
      <UnregisterClassInfo Sequence="2700" />
      <UnregisterExtensionInfo Sequence="2800" />
      <UnregisterProgIdInfo Sequence="2900" />
      <UnregisterMIMEInfo Sequence="3000" />
      <RemoveIniValues Sequence="3100" />
      <RemoveShortcuts Sequence="3200" />
      <RemoveEnvironmentStrings Sequence="3300" />
      <RemoveDuplicateFiles Sequence="3400" />
      <Custom Action="VM_DeleteLogs_SD" Sequence="3452">REMOVE="ALL" And (Not UPGRADINGPRODUCTCODE) And (Not VDM_INSTALLER_CHECKS=0)</Custom>
      <Custom Action="VM_DeleteLogs" Sequence="3453">VM_DeleteLogs</Custom>
      <RemoveFiles Sequence="3500" />
      <RemoveFolders Sequence="3600" />
      <CreateFolders Sequence="3700" />
      <MoveFiles Sequence="3800" />
      <Custom Action="VMCreateUnityFiltersDir_SD" Sequence="3850">Not Installed</Custom>
      <Custom Action="VMCreateUnityFiltersDir" Sequence="3851">VMCreateUnityFiltersDir</Custom>
      <InstallFiles Sequence="4000" />
      <PatchFiles Sequence="4090" />
      <DuplicateFiles Sequence="4210" />
      <BindImage Sequence="4300" />
      <CreateShortcuts Sequence="4500" />
      <Custom Action="VM_SetPerfhostStartupType_RB_SD" Sequence="4500">SVC_STARTTYPE_INITIAL_PERFHOST&lt;&gt;2 And $_64_pcoip_perf_provider64.dll=3</Custom>
      <Custom Action="VM_SetPerfhostStartupType_SD" Sequence="4501">SVC_STARTTYPE_INITIAL_PERFHOST&lt;&gt;2 And $_64_pcoip_perf_provider64.dll=3</Custom>
      <Custom Action="VM_SetPerfhostStartupType_RB" Sequence="4502">VM_SetPerfhostStartupType_RB</Custom>
      <Custom Action="VM_SetPerfhostStartupType" Sequence="4503">VM_SetPerfhostStartupType</Custom>
      <RegisterClassInfo Sequence="4600" />
      <RegisterExtensionInfo Sequence="4700" />
      <RegisterProgIdInfo Sequence="4800" />
      <RegisterMIMEInfo Sequence="4900" />
      <WriteRegistryValues Sequence="5000" />
      <Custom Action="VM_SetPcoipUdpSizeThreshold" After="WriteRegistryValues">&amp;Core=3</Custom>
      <Custom Action="VM_InstallWsnmWinlogonNotificationHandler" After="VM_SetPcoipUdpSizeThreshold">&amp;Core=3</Custom>
      <Custom Action="VM_VMLMHandlersUninstall" After="VM_InstallWsnmWinlogonNotificationHandler">&amp;Core=3</Custom>
      <Custom Action="VM_VMUninstallHznusmWinlogonNotificationHandler" After="VM_VMLMHandlersUninstall">&amp;Core=3</Custom>
      <Custom Action="VM_DisableStandbyMode_RB_SD" After="VM_VMUninstallHznusmWinlogonNotificationHandler">&amp;Core=3 Or (!Core=3 And REINSTALL)</Custom>
      <Custom Action="VM_DisableStandbyMode_SD" After="VM_DisableStandbyMode_RB_SD">&amp;Core=3 Or (!Core=3 And REINSTALL)</Custom>
      <Custom Action="VM_DisableStandbyMode_RB" After="VM_DisableStandbyMode_SD">VM_DisableStandbyMode_RB</Custom>
      <Custom Action="VM_DisableStandbyMode" After="VM_DisableStandbyMode_RB">VM_DisableStandbyMode</Custom>
      <Custom Action="VM_SetSanPolicy_RB_SD" After="VM_DisableStandbyMode">NGVC_INSTALLING And Not VDS_SAN_POLICY=1</Custom>
      <Custom Action="VM_SetSanPolicy_RB" After="VM_SetSanPolicy_RB_SD">VM_SetSanPolicy_RB</Custom>
      <Custom Action="VM_SetSanPolicy_SD" After="VM_SetSanPolicy_RB">NGVC_INSTALLING And Not VDS_SAN_POLICY=1</Custom>
      <Custom Action="VM_SetSanPolicy" After="VM_SetSanPolicy_SD">VM_SetSanPolicy</Custom>
      <WriteIniValues Sequence="5100" />
      <WriteEnvironmentStrings Sequence="5200" />
      <RegisterFonts Sequence="5300" />
      <InstallODBC Sequence="5400" />
      <RegisterTypeLibraries Sequence="5500" />
      <SelfRegModules Sequence="5600" />
      <RegisterComPlus Sequence="5700" />
      <Custom Action="VM_RegisterPcoipPerf_RB" After="RegisterComPlus">$_64_pcoip_perf_provider64.dll=3 Or (?_64_pcoip_perf_provider64.dll=3 And REINSTALL)</Custom>
      <Custom Action="VM_RegisterPcoipPerf64_SD" After="VM_RegisterPcoipPerf_RB">$_64_pcoip_perf_provider64.dll=3 Or (?_64_pcoip_perf_provider64.dll=3 And REINSTALL)</Custom>
      <Custom Action="VM_RegisterPcoipPerf64" After="VM_RegisterPcoipPerf64_SD">$_64_pcoip_perf_provider64.dll=3 Or (?_64_pcoip_perf_provider64.dll=3 And REINSTALL)</Custom>
      <Custom Action="HZ_MigrateCertStore" After="VM_RegisterPcoipPerf64">Not Installed AND HORIZON_VMWAREBRANDED_AGENT_INSTALLED</Custom>
      <Custom Action="VM_OpenFrameworkChannel_RB_SetData" Sequence="5770">&amp;Core=3 Or (!Core=3 And REINSTALL)</Custom>
      <Custom Action="VM_OpenFrameworkChannel_SetData" Sequence="5771">&amp;Core=3 Or (!Core=3 And REINSTALL)</Custom>
      <Custom Action="VM_OpenFrameworkChannel_RB" Sequence="5772">&amp;Core=3 Or (!Core=3 And REINSTALL)</Custom>
      <Custom Action="VM_OpenFrameworkChannel" Sequence="5773">&amp;Core=3 Or (!Core=3 And REINSTALL)</Custom>
      <Custom Action="VM_OpenPCoIPFirewall_RB_SD" Sequence="5775">$_64_pcoip_server_win32.exe=3 Or (?_64_pcoip_server_win32.exe=3 And REINSTALL)</Custom>
      <Custom Action="VM_OpenPCoIPFirewall_TCP_SD" Sequence="5776">$_64_pcoip_server_win32.exe=3 Or (?_64_pcoip_server_win32.exe=3 And REINSTALL)</Custom>
      <Custom Action="VM_OpenPCoIPFirewall_UDP_SD" Sequence="5777">$_64_pcoip_server_win32.exe=3 Or (?_64_pcoip_server_win32.exe=3 And REINSTALL)</Custom>
      <Custom Action="VM_OpenPCoIPFirewall_RB" Sequence="5778">$_64_pcoip_server_win32.exe=3 Or (?_64_pcoip_server_win32.exe=3 And REINSTALL)</Custom>
      <Custom Action="VM_OpenPCoIPFirewall_TCP" Sequence="5779">$_64_pcoip_server_win32.exe=3 Or (?_64_pcoip_server_win32.exe=3 And REINSTALL)</Custom>
      <Custom Action="VM_OpenPCoIPFirewall_UDP" Sequence="5780">$_64_pcoip_server_win32.exe=3 Or (?_64_pcoip_server_win32.exe=3 And REINSTALL)</Custom>
      <Custom Action="VM_RunEVDConfiguration_SD" After="WriteRegistryValues">$_evd_configurationapi.dll=3</Custom>
      <Custom Action="VM_RunEVDConfiguration" After="VM_RunEVDConfiguration_SD">VM_RunEVDConfiguration</Custom>
      <Custom Action="VM_BlastUDPReservePorts_RB_SD" Before="VM_BlastUDPReservePorts_SD">&amp;BlastUDP=3 Or (!BlastUDP=3 And REINSTALL)</Custom>
      <Custom Action="VM_BlastUDPReservePorts_SD" Before="VM_BlastUDPReservePorts_RB">&amp;BlastUDP=3 Or (!BlastUDP=3 And REINSTALL)</Custom>
      <Custom Action="VM_BlastUDPReservePorts_RB" Before="VM_BlastUDPReservePorts">&amp;BlastUDP=3 Or (!BlastUDP=3 And REINSTALL)</Custom>
      <Custom Action="VM_BlastUDPReservePorts" Before="VM_BlastUDPReservePorts_SideChannel_RB_SD">&amp;BlastUDP=3 Or (!BlastUDP=3 And REINSTALL)</Custom>
      <Custom Action="VM_BlastUDPReservePorts_SideChannel_RB_SD" Before="VM_BlastUDPReservePorts_SideChannel_SD">&amp;BlastUDP=3 Or (!BlastUDP=3 And REINSTALL)</Custom>
      <Custom Action="VM_BlastUDPReservePorts_SideChannel_SD" Before="VM_BlastUDPReservePorts_SideChannel_RB">&amp;BlastUDP=3 Or (!BlastUDP=3 And REINSTALL)</Custom>
      <Custom Action="VM_BlastUDPReservePorts_SideChannel_RB" Before="VM_BlastUDPReservePorts_SideChannel">&amp;BlastUDP=3 Or (!BlastUDP=3 And REINSTALL)</Custom>
      <Custom Action="VM_BlastUDPReservePorts_SideChannel" Before="InstallServices">&amp;BlastUDP=3 Or (!BlastUDP=3 And REINSTALL)</Custom>
      <InstallServices Sequence="5800" />
      <Custom Action="VM_LaunchHzaprep_SD" Before="VM_LaunchHzaprep">GOLDEN_IMAGE_INSTALL=1 and Not Installed</Custom>
      <Custom Action="VM_LaunchHzaprep" Before="StartServices">VM_LaunchHzaprep</Custom>
      <Custom Action="VM_WriteRegistry_Hzaprep_RB_SD" After="VM_LaunchHzaprep">GOLDEN_IMAGE_INSTALL=1 and Not Installed</Custom>
      <Custom Action="VM_WriteRegistry_Hzaprep_SD" After="VM_WriteRegistry_Hzaprep_RB_SD">GOLDEN_IMAGE_INSTALL=1 and Not Installed</Custom>
      <Custom Action="VM_WriteRegistry_Hzaprep_RB" After="VM_WriteRegistry_Hzaprep_SD">VM_WriteRegistry_Hzaprep_RB</Custom>
      <Custom Action="VM_WriteRegistry_Hzaprep" After="VM_WriteRegistry_Hzaprep_RB">VM_WriteRegistry_Hzaprep</Custom>
      <StartServices Sequence="5900" />
      <RegisterUser Sequence="6000" />
      <RegisterProduct Sequence="6100" />
      <Custom Action="VM_UpdateARPDisplayVersion_SD"  After="RegisterProduct">Not UNINSTALLING</Custom>
      <Custom Action="VM_UpdateARPDisplayVersion"  After="VM_UpdateARPDisplayVersion_SD">Not UNINSTALLING</Custom>
      <PublishComponents Sequence="6200" />
      <MsiPublishAssemblies Sequence="6250" />
      <PublishFeatures Sequence="6300" />
      <PublishProduct Sequence="6400" />
      <Custom Action="VM_SelfReg_RB_SD" Sequence="6500" />
      <Custom Action="VM_SelfReg_SD" Sequence="6501" />
      <Custom Action="VM_SelfReg_RB" Sequence="6502" />
      <Custom Action="VM_SelfReg" Sequence="6503" />
      <Custom Action="VM_AddRegInterceptEnabled32Wow_RB_SD" Sequence="6624">$_32_hznsci.dll=3</Custom>
      <Custom Action="VM_AddRegInterceptEnabled32Wow_SD" Sequence="6625">$_32_hznsci.dll=3</Custom>
      <Custom Action="VM_AddRegInterceptEnabled32Wow_RB" Sequence="6626">VM_AddRegInterceptEnabled32Wow_RB</Custom>
      <Custom Action="VM_AddRegInterceptEnabled32Wow" Sequence="6627">VM_AddRegInterceptEnabled32Wow</Custom>
      <Custom Action="VM_AddRegInterceptEnabled_RB_SD" Sequence="6628">$_hznsci.dll=3</Custom>
      <Custom Action="VM_AddRegInterceptEnabled_SD" Sequence="6629">$_hznsci.dll=3</Custom>
      <Custom Action="VM_AddRegInterceptEnabled_RB" Sequence="6630">VM_AddRegInterceptEnabled_RB</Custom>
      <Custom Action="VM_AddRegInterceptEnabled" Sequence="6632">VM_AddRegInterceptEnabled</Custom>
      <Custom Action="VM_AddRegInterceptWhiteList32Wow_RB_SD" Sequence="6644">$_32_hznsci.dll=3</Custom>
      <Custom Action="VM_AddRegInterceptWhiteList32Wow_SD" Sequence="6645">$_32_hznsci.dll=3</Custom>
      <Custom Action="VM_AddRegInterceptWhiteList32Wow_RB" Sequence="6646">VM_AddRegInterceptWhiteList32Wow_RB</Custom>
      <Custom Action="VM_AddRegInterceptWhiteList32Wow" Sequence="6647">VM_AddRegInterceptWhiteList32Wow</Custom>
      <Custom Action="VM_AddRegInterceptWhiteList_RB_SD" Sequence="6648">$_hznsci.dll=3</Custom>
      <Custom Action="VM_AddRegInterceptWhiteList_SD" Sequence="6649">$_hznsci.dll=3</Custom>
      <Custom Action="VM_AddRegInterceptWhiteList_RB" Sequence="6650">VM_AddRegInterceptWhiteList_RB</Custom>
      <Custom Action="VM_AddRegInterceptWhiteList" Sequence="6652">VM_AddRegInterceptWhiteList</Custom>
      <Custom Action="VM_Remove_vmwvaudio_SD" Before="InstallFinalize">&amp;VmwVaudio=2 And Not UPGRADINGPRODUCTCODE</Custom>
      <Custom Action="VM_Remove_vmwvaudio" After="VM_Remove_vmwvaudio_SD">&amp;VmwVaudio=2 And Not UPGRADINGPRODUCTCODE</Custom>
      <Custom Action="VM_Remove_hznvaudioin_SD" Before="InstallFinalize">&amp;RTAV=2 And Not UPGRADINGPRODUCTCODE</Custom>
      <Custom Action="VM_Remove_hznvaudioin" After="VM_Remove_hznvaudioin_SD">&amp;RTAV=2 And Not UPGRADINGPRODUCTCODE</Custom>
      <Custom Action="VM_Remove_hznvwebcam_SD" Before="InstallFinalize">&amp;RTAV=2 And Not UPGRADINGPRODUCTCODE</Custom>
      <Custom Action="VM_Remove_hznvwebcam" After="VM_Remove_hznvwebcam_SD">&amp;RTAV=2 And Not UPGRADINGPRODUCTCODE</Custom>
      <Custom Action="VM_DebugRollback_SD" Before="VM_DebugSimulateError" />
      <Custom Action="VM_DebugSimulateError" Before="InstallFinalize">Not Installed</Custom>
      <Custom Action="VM_WaitForPairing_SD" Before="VM_WaitForPairing">GOLDEN_IMAGE_INSTALL=1 and Not Installed</Custom>
      <Custom Action="VM_WaitForPairing" Before="VM_DisableWSNMService">VM_WaitForPairing</Custom>
      <Custom Action="VM_DisableWSNMService" Before="VM_RemoveRegistry_Hzaprep_SD">GOLDEN_IMAGE_INSTALL=1 and Not Installed</Custom>
      <Custom Action="VM_RemoveRegistry_Hzaprep_SD" Before="VM_RemoveRegistry_Hzaprep">GOLDEN_IMAGE_INSTALL=1 and Not Installed</Custom>
      <Custom Action="VM_RemoveRegistry_Hzaprep" Before="VM_RemoveCachedInstallerFiles_SD">VM_RemoveRegistry_Hzaprep</Custom>
      <!-- VM_RemoveCachedInstallerFiles must be the last action before InstallFinalize since it can't be rolled back -->
      <Custom Action="VM_RemoveCachedInstallerFiles_SD" Before="VM_RemoveCachedInstallerFiles">&amp;Core=2</Custom>
      <Custom Action="VM_RemoveCachedInstallerFiles" Before="InstallFinalize">VM_RemoveCachedInstallerFiles</Custom>
      <InstallFinalize Sequence="6750" />
      <Custom Action="VM_CheckReboot" Sequence="6800" />
      <ScheduleReboot Sequence="6900">VMREBOOT</ScheduleReboot>
      <Custom Action="VM_LaunchURLRedirectionHelper" After="ScheduleReboot">$_horizon_url_protocol_launch_helper.exe=3</Custom>
   </InstallExecuteSequence>

</Include>
