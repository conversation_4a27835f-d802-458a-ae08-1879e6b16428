# Copyright (c) Omnissa, LLC. All rights reserved.
# This product is protected by copyright and intellectual property laws in the
# United States and other countries as well as by international treaties.
# -- Omnissa Restricted

import time, os, json
import platform, subprocess
import lib.auto, lib.raceTrack
import lib.findPic as pic
from PIL import Image
import lib.resultListener
import lib.bdweb as bdweb
from lib.testrail import APIClient
from datetime import datetime
import sys
from lib.testIds import getTestCaseList

resultListener = lib.resultListener.resultListener
auto = lib.auto
raceTrack = lib.raceTrack
cached_values = {}
cached_values['test_result']= 'PASSED'
shared_values = {}

# This is for send result email
def publishOthers():
    branch = cached_values.get('branch', 'main')
    agentBuild = cached_values.get('agentBuildNum', 'unknown')
    print(f"publish info:\n\tbranch: {branch}, build: {agentBuild}")

    return testrailUpload(cached_values)


def sendresult(tr_run,vadc=False,To=lib.auto.toolData['email']):
    helloMsg = """\
                    <html>
                    Hello Team,<br><br>
                    </html>
                    """
    bodyTemplate = """\
                    Title
                    <table style="border: 1px solid #000000; border-collapse: collapse;" border="1">
                    <tr>
                    <th>Horizon Server Build</th><th>Linux Agent Build</th><th>Linux Agent Distribution</th><th>Horizon Client Build</th><th>Horizon Client OS</th><th>Testrail Link</th>
                    </tr>
                    <tr>
                    <td>brokerBuild</td><td>agentBuild</td><td>agentDistro</td><td>clientBuild</td><td>clientos</td><td>testrailLink</td>
                    </tr>
                    </table>
                    <br>
                    testSummay
                    <br>
                    """
    thanksMsg = """\
                    Thanks,
                    <br>
                    Horizon Linux VDI Team
                    """
    if vadc:
        bodyTemplate = """\
                        Title
                        <table style="border: 1px solid #000000; border-collapse: collapse;" border="1">
                        <tr>
                        <th>Linux Agent Build</th><th>Linux Agent Distribution</th><th>Horizon Client Build</th><th>Horizon Client OS</th><th>Testrail Link</th>
                        </tr>
                        <tr>
                        <td>agentBuild</td><td>agentDistro</td><td>clientBuild</td><td>clientos</td><td>testrailLink</td>
                        </tr>
                        </table>
                        <br>
                        testSummay
                        <br>
                        """

    brokerBuild = cached_values.get('brokerBuildNum', 'unknown')
    if auto.github:
        agentBuildHref = bdweb.githubBuildwithHref(buildid=auto.buildNum,
                                                   branch=auto.branch,
                                                   buildtype=auto.buildtype)
    else:
        bd = bdweb.BuildWeb(auto.product,
                            auto.branch,
                            auto.buildtype,
                            sb = auto.isSandbox,
                            buildNum=auto.buildNum)
        agentBuildHref = bd.getBuildwithHref(buildNum=auto.buildNum)
    
    clientBuild = cached_values.get('clientBuildNum', 'unknown')
    clientos = cached_values.get('hostos', 'unknown')
    agentDist = cached_values.get('agentDist', 'unknown')
    title = f'LinuxVDI BAT over client {clientos} ' \
              + f'for agent {agentDist}'
    Title = title + f' {cached_values["test_result"]}'
    subject = f'Linux VDI BAT Result : {cached_values.get("Feature", title)} {cached_values["test_result"]}'
    body = bodyTemplate
    body = body.replace('Title', Title)
    if not vadc:
        body = body.replace('brokerBuild', brokerBuild)
    body = body.replace('agentBuild', agentBuildHref)
    body = body.replace('agentDistro', agentDist)
    body = body.replace('clientBuild', clientBuild)
    body = body.replace('clientos', clientos)
    body = body.replace("testrailLink", tr_run.GetTestrailLink() )
    body = body.replace('testSummay', resultListener.GetRowsInHtml())

    # write the results in file.
    with open("emailreport.html", "w") as file:
        file.write(body)

    #commenting the below code as the result will be sent to jenkins server. 
    #jenkins server will consolidate all the platform results and report it in single email
     
    #sender_name = "Linux Agent Team"
    #sender = '<EMAIL>'
    #body = helloMsg + body + thanksMsg
    #sendemail(sender_name=sender_name,From=sender,to=To, subject=subject, body=body)

    time.sleep(2)


def sendemail(sender_name="",From='<EMAIL>', to='<EMAIL>', subject='Email Subject', body='Email body'):
    '''
    @body: Email body.
    @status: Send email to [status] recipients in unattendedbat.ini.
       passed
       failed
    @subject: Email subject.

    '''
    import smtplib
    from email.mime.text import MIMEText
    from email.utils import formataddr
    mail = MIMEText(body, 'html')
    mail['Subject'] = subject
    mail['From'] = formataddr((sender_name,From))
    mail['To'] = to
    smtp = smtplib.SMTP('smtp.oci.omnissa.com',587)
    smtp.ehlo()
    smtp.starttls()
    smtp.login('smtp-euc','VMware1!')
    
    print("\nSending email to %s" % (mail['To']))
    smtp.sendmail(mail['From'], mail['To'].split(','), mail.as_string())
    smtp.close()

def getAgentBuildNum(branch=auto.branch, buildtype=auto.buildtype):
    if auto.buildNum:
        return auto.buildNum
    if auto.github:
        agent_build = bdweb.githubLatestID(branch=branch, buildtype=buildtype,
                product=auto.product)
    else:
        bd = bdweb.BuildWeb(auto.product,
                            auto.branch,
                            auto.buildtype,
                            sb = auto.isSandbox,
                            buildNum=None)
        agent_build = bd.getBuildNum()
    return str(agent_build)

def getJson(cached_values):
    jsondata = {}
    if not 'agentBuildNum' in cached_values:
        updateCacheBuild()
    jsondata["Product"] = cached_values['product']
    jsondata["Branch"] = cached_values['branch']
    jsondata["BuildType"] = cached_values['buildtype']
    jsondata["BuildNum"] = cached_values['agentBuildNum']
    jsondata["TestType"] = 'BAT'
    jsondata["case_ids"] = cached_values["case_ids"]
    jsondata["results"] = cached_values["results"]
    return json.dumps(jsondata)
        
def updateCacheResult(tclist, feature, passonly=True):
    
    results = {}
    tmpids = getTestCaseList()
    print("TestCase IDs: "+str(tmpids))
    for tid in tmpids:
        if int(tid) < 10000:
            continue
        results['case_id'] = tid
        results['status_id'] = 1
        results['comment'] = feature
        results['custom_build_number'] = cached_values['agentBuildNum']
        if kits.raceTrack.testedCasesResults[feature] != 'Pass':
            if passonly:
                continue
            results['status_id'] = 5
        cached_values['results'].append(results.copy())
        cached_values['case_ids'].append(tid)

def testrailUpload(cached_values):
    # make sure custom_build_number is not null
    if not len(cached_values['results']):
        print('no case to report')
        return
    for idx in cached_values['results']:
        if idx['custom_build_number'] is None:
            idx['custom_build_number'] = cached_values['agentBuildNum']
    agentDist = cached_values.get('agentDist', 'unknown')
    print(f'cached values for testrail:\n{cached_values}')
    tr_run = TestRailRun(
       buildNumber = cached_values['agentBuildNum'],
       milestoneId = 3, # 3: "CART BATs - Main"
       productName = f'Linux Agent '+str(agentDist),
       includeAll  = False,
       caseId_List = cached_values['case_ids'],
       description = cached_values['Feature'],
       user="<EMAIL>",
       password="Ba7B8Chcmc9ZerF1kdef-EzKyP4lQ/pOAHaKOkwAK"
    )
    jsd = json.loads(getJson(cached_values))
    print('json results')
    print(f'{jsd}')
    tr_run_id = tr_run.CreateNewTestRun()
    trResults = tr_run.AddResultToTestRun(
       run_id=tr_run_id,
       result_data=jsd)
    print('test rail results')
    print(f'{trResults}')
    return tr_run

def updateCacheBuild(branch=auto.branch, buildtype=auto.buildtype):
    if 'agentBuildNum' in cached_values and 'branch' in cached_values:
        return
    if auto.github:
        agent_build, branch, buildtype = bdweb.githubLatestID(branch=branch, buildtype=buildtype)
    else:
        bd = bdweb.BuildWeb(auto.product,
                            auto.branch,
                            auto.buildtype,
                            sb = auto.isSandbox,
                            buildNum=auto.buildNum)
        agent_build = bd.getBuildNum()
    if auto.buildNum:
        cached_values['agentBuildNum'] = f'{auto.buildNum}'
    else:
        cached_values['agentBuildNum'] = f'{agent_build}'
    cached_values['branch'] = branch
    cached_values['buildtype'] = buildtype
    cached_values['product'] = auto.product

class TestRailRun():
    def __init__(self, buildNumber=None, milestoneId=3, productName="HorizonLinuxAgnet",
                 includeAll=False, caseId_List=[], description="",
                 user="", password=""):
        self.buildNumber = buildNumber
        self.milestone_id = milestoneId
        self.product_name = productName
        self.TestRun_Description = description
        self.TestRun_IncludeAll = includeAll
        self.TestRun_CaseIDs = caseId_List
        self.Username = user
        self.Password = password
        self.tr_run_id = ''

    def CreateNewTestRun(self):
        today = '{}/{}/{}'.format(datetime.now().month, datetime.now().day, datetime.now().year)
        TestRun_Name = "%s %s %s" % (self.product_name, self.buildNumber, today)
        payload = {
           "milestone_id": self.milestone_id,
           "suite_id": 4,
           "name": TestRun_Name,
           "description": self.TestRun_Description,
           "include_all": self.TestRun_IncludeAll,
           "case_ids": self.TestRun_CaseIDs
        }
        client = APIClient(r'https://omnissa.testrail.io/')
        client.user = self.Username
        client.password = self.Password
        # Below request adds a new test run per BAT test
        # --> tr_result = client.send_post('add_run/4', payload)
        # Adding each test run to a test plan
        # https://omnissa.testrail.io/index.php?/plans/view/47999
        # 47999 --> Test Plan ID for Linux VDI BAT Results in testrail
        tr_result = client.send_post('add_plan_entry/47999', payload)
        self.tr_run_id = tr_result['runs'][0]['id']
        return self.tr_run_id

    def UpdateTestRun(self, run_id, description=None, case_ids=None):
        payload = {}
        if description:
           payload["description"] = description
        if case_ids:
           payload["case_ids"] = case_ids
        client = APIClient(r'https://omnissa.testrail.io/')
        client.user = self.Username
        client.password = self.Password
        tr_result = client.send_post('update_run/%s' %run_id, payload)
        run_id = tr_result['id']
        return run_id

    def AddResultToTestRun(self, run_id, result_data):
        client = APIClient(r'https://omnissa.testrail.io/')
        client.user = self.Username
        client.password = self.Password
        return client.send_post(
           "add_results_for_cases/%s" % run_id,
           result_data
        )
    def GetTestrailLink(self):
        return f"https://omnissa.testrail.io/index.php?/runs/view/{self.tr_run_id}"

class Kits:
    def __init__(self):
        self.raceTrack = self.InitRaceTrack()
        self.IsTestSetStarted = False
        self.needExit = False
        # cache basic info
        if 'linux' in sys.platform:
            outs = subprocess.check_output("grep PRETTY_NAME= /etc/os-release | awk -F= '{print $2}'", shell=True).decode().strip()
            self.hostos = outs.replace('"', '')
        else:
            self.hostos = platform.system() + ' ' + platform.release()
        global cached_values
        cached_values['hostos'] = self.hostos

    def InitRaceTrack(self):
        buildData = auto.buildInfo
        #BAT_AUTO_BLR
        buildData['product'] = auto.buildInfo['product']
        buildData['branch'] = auto.buildInfo['branch']
        buildData['buildtype'] = auto.buildInfo['buildType']
        # buildData['buildnum'] = auto.GetLatestBuildNum()
        #raceTrackData['description'] = f"{auto.raceTrackData['testtype']} over {auto.buildData['branch']} branch"
        return raceTrack.RaceTrackWrapper(autodata = buildData)

    def Verify(self, actual, expectation, comment, screenshot=False, clientHost='localhost', delete=True):
        self.raceTrack.TestCaseVerification(comment, actual, expectation)
        if screenshot:
            lpic=[]
            if 'localhost' in clientHost:
                lpic = pic.GrabScreenLocal()
            else:
                lpic = pic.GrabScreenRemote((clientHost, None))
            # let's only upload the overal screen
            if len(lpic) > 1:
                idx = lpic[0]
                if os.path.getsize(idx) >> 20 > 1:
                    img = Image.open(idx)
                    w, h = img.size
                    r = 480 / float(h)
                    new_w = int(w * r)
                    img2 = img.resize((new_w, 480), Image.LANCZOS)
                    fn, fe = os.path.splitext(idx)
                    fname = f"{fn}__{new_w}x480{fe}"
                    print(fname)
                    img2.save(fname)
                    self.Screenshot('local desktop', fname)
                else:
                    self.Screenshot('local desktop', idx)
            if delete:
                for idx in lpic:
                    os.remove(idx)
        self.needExit = True

    def Comment(self, comment):
        self.raceTrack.TestCaseComment(comment)

    def Warning(self, comment):
        pass #self.raceTrack.TestCaseWarning(comment)

    def Screenshot(self, description, screenShot):
        self.raceTrack.TestCaseScreenshot(Description=description, Screenshot=screenShot)

    def Log(self, description, log, delete=False):
        pass #self.raceTrack.TestCaseLog(Description=description, Log=log)
        #if delete:
        #    os.remove(log)

    def Wait(self, waitTime, comment=''):
        """
        Wait and print start/end time
        @Input:
          -waitTime, wait time in seconds
        """
        currentTime = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print('%s: start waiting %d seconds: %s' %(currentTime, waitTime, comment))
        time.sleep(waitTime)
        currentTime = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print('%s: End Wait' %(currentTime))
# Initiate an instance, which will be available in whole running
kits = Kits()


if __name__ == '__main__':
    print('kits is lib file, cannot be executed directly')
    exit()
