# Copyright (c) Omnissa, LLC. All rights reserved.
# This product is protected by copyright and intellectual property laws in the
# United States and other countries as well as by international treaties.
# -- Omnissa Restricted

"""vncBlitTest: VNC region encoder blit detector testing tool

vncBlitTest is a test application that exercises the vncBlitDetect
module.  It uses two PNG files as inputs representing encoded video
frames.  It searches the frames for a large region that can be copied
from the first frame to the second using a blit operation, and reports
the blit it finds and how it's found.

Maintained by the Blast display team:
   1. <EMAIL>
   2. Reviewboard groups: appblast
"""

import vmware

Import("env_opts")

target = "vncBlitTest"
env = vmware.Host().DefaultEnvironment()

env.Append(
    CPPDEFINES={
        "USERLEVEL": None,
        "VMX86_DESKTOP": None,
    },
    CPPPATH=[
        "#bora/public",
        "#bora/lib/public",
        "#bora/lib/vnc",
        "#bora/lib/blastCodec",
        "#bora/lib/vncConnectionManager",
        "#bora/lib/vncConnectionManager/encode",
        vmware.HeaderDirectory("vnc"),
    ],
)

vmware.LoadTool(
    env,
    [
        "libpng",
        "libz",
        "protobuf-c-3",
        "vm-product",
    ],
)

if vmware.Host().IsLinux():
    env.Append(
        LIBS=[
            "dl",
        ]
    )

if vmware.Host().IsMac():
    env.Append(
        FRAMEWORKS=[
            "CoreFoundation",
            "DiskArbitration",
            "IOKit",
            "Security",
        ],
        LIBS=["z"],
    )

if vmware.Host().IsWindows():
    env.LoadTool(
        [
            "msvcrt",
        ]
    )

    env.Append(
        LINKFLAGS=[
            '-base:"0x69500000"',
            "-subsystem:console",
        ],
        LIBS=[
            "ws2_32.lib",
            "uuid.lib",
            "oldnames.lib",
            "setupapi.lib",
            "netapi32.lib",
            "delayimp.lib",
            "kernel32.lib",
            "user32.lib",
            "gdi32.lib",
            "advapi32.lib",
            "ole32.lib",
            "oleaut32.lib",
            "shell32.lib",
            "msimg32.lib",
            "wbemuuid.lib",
            "winmm.lib",
            "glu32.lib",
            "Wtsapi32.lib",
            "msvcrt.lib",
            "bcrypt.lib",
            "crypt32.lib",
        ],
    )

vncLibs = [
    "cityhash",
    "config",
    "coreDump",
    "crypto",
    "d3des",
    "dict",
    "err",
    "file",
    "image",
    "lock",
    "log",
    "misc",
    "panic",
    "panicDefault",
    "poll",
    "productState",
    "raster",
    "rectangle",
    "region",
    "sig",
    "string",
    "thread",
    "unicode",
    "user",
    "uuid",
    "vnc",
    "vncConnectionManager",
]

if vmware.Host().IsMac():
    vncLibs += [
        "location",
    ]

if vmware.Host().IsWindows():
    vncLibs += ["wmi"]

exe = vmware.Executable(target, env=env)

exe.addStaticLibs("vmlibs", vncLibs)

exe.addSubdirs(
    [
        "apps/rde/blast/vncBlitTest",
    ]
)

node = exe.createProgramNode(env)
vmware.RegisterNode(node, target)

# Stage redistributables like libpng when building vncBlitTest.
for n in env.get("REDIST") or []:
    vmware.RegisterNode([File(n)], target)

vmware.Alias(target + "-build", node)
