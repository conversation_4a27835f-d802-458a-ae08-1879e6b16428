# Copyright (c) Omnissa, LLC. All rights reserved.
# This product is protected by copyright and intellectual property laws in the
# United States and other countries as well as by international treaties.
# -- Omnissa Restricted

"""
SCons initialization.
"""

import inspect
import atexit
import itertools
import logging
import os
import re
import shutil
import sys

import SCons
import SCons.Warnings
import SCons.Script
import SCons.Node

import vmware
from vmware import VMwareOptions
from vmware import logwrapper
from vmware.noderegistry import UpdateRegisteredNodes, LoadRegisteredNodes
from vtools.common import SetIncludeCacheOptions
from vmware.forker import ForkerManager

import SCons.Variables

# If dependency enforcement is enabled, depcop is set via
# import depcop<platform> as depcop, where platform is Windows, Linux, etc.
depcop = None


def SetupOptions():
    """Function for setting up scons options processing

    Returns a env object containing all the options
    """

    if not "SCONS_SKIP_LOCAL_SC" in os.environ.keys():
        # Candidates for Local.sc
        # XXX: We should work our way up from cwd to find a Local.sc rather
        # than hard-coding paths here.
        localscpaths = [
            "#Local.sc",
            "#bora/Local.sc",
        ]

        # scons chokes if you give Options constructor a None object
        localsc = localscpaths[0]
        for path in localscpaths:
            localscabspath = File(path).abspath
            if os.path.exists(localscabspath):
                localsc = localscabspath
                break

        # Run the Local.sc file ourselves (if it exists), so that we can
        # learn which values it defines. This means that we end up executing the
        # Local.sc file twice, which is bad if it has side effects, but there's
        # currently no other way to get at this information.
        localscvals = {}
        if os.path.exists(localsc):
            vmware.ExecFile(localsc, localscvals)
    else:
        print("Skipping Local.sc, as per SCONS_SKIP_LOCAL_SC environment option")
        localsc = "/dev/null"

    opts = VMwareOptions(os.path.normpath(localsc), ARGUMENTS)

    # Core options
    opts.AddVariables(
        BoolVariable("VERBOSE", "Verbose output level", 0),
        BoolVariable(
            "DUMPNODESONLY",
            "If true, stop execution after the nodes list is dumped "
            "into registered-nodes.txt",
            0,
        ),
        ("CACHEDIR", "Directory for build cache", os.path.normpath("bora/scache")),
        (
            "BUILD_NUMBER",
            "Build number",
            None,
        ),  # default: '0'; see _buildnum definition in config.py
        (
            "PRODUCT_BUILD_NUMBER",
            "Product build number",
            None,
        ),  # default: '0'; see _prodbuildnum definition in config.py
        ("BUILDROOT", "Root of build output.", None),
        (
            "LOGGING",
            "Parameters for logging. "
            "Comma separated list of loggername=level pairs\n\n\n"
            + vmware.GetLoggersHelpString(),
            "",
        ),
        BoolVariable("COLORED_LOGGING", "Enable colored logging", False),
        BoolVariable("LOGGING_SHOW_MODULE", "Show logger", True),
        BoolVariable("LOGGING_SHOW_LEVEL", "Show loglevel", True),
        BoolVariable("LOGGING_SHOW_DATE", "Show date in logstring", False),
        BoolVariable("LOGGING_SHOW_TIME", "Show time in logstring", True),
        BoolVariable("LOGGING_SHOW_MILLIS", "Show milliseconds in logstring", True),
        BoolVariable(
            "LOGGING_SHOW_FILE", "Show file and line number in logstring", False
        ),
        BoolVariable(
            "LOGGING_DISABLE_SCONSIGN_PROGRESS",
            "Disable sconsign progress output",
            False,
        ),
        (
            "DEFAULT_LOGLEVEL",
            "The default log level for the root logger.\n"
            "The smaller the value, the more verbose the output.",
            str(logwrapper.DEFAULT_LOG_LEVEL),
        ),
        BoolVariable("ENABLE_BUILD_STATS", "Enable simple build statistics", False),
        (
            "BUILDTREES",
            "A list of foreign buildtree paths. Comma separated pairs of "
            "<build host os name>=<path to build tree> e.g. Linux=/foo/bar/build\n"
            "<build host os name> should be one of the values "
            "returned by vmware.BuildHost()\n"
            "<path to build tree> should be the value passed "
            "to BUILDROOT for the build on the remote host",
            "",
        ),
        (
            "NUM_CPU",
            "The number of local CPUs to use.  "
            "Defaults to number of local processors",
            0,
        ),
        # REMOTE_CACHE_FETCH is not a BoolVariable because we need to tell the
        # difference between True, False, and not specified. If specified, the
        # config handler will still enforce that it is a bool value when it uses
        # vmware.LocalOpts.GetBool().
        (
            "REMOTE_CACHE_FETCH",
            "Fetch build output from the corporate remote cache server. Defaults "
            "to automatically enabling if cache server connection server is fast. "
            "See http://url/teu2",
            None,
        ),
        BoolVariable(
            "DEPCOP",
            "Enforce dependency correctness for all cacheable actions. "
            "Defaults to False. See http://url/teu2",
            False,
        ),
        BoolVariable(
            "LOW_BANDWIDTH",
            "Set this to True if you have a slow connection to EUC "
            "Artifactory. Defaults to False.",
            False,
        ),
        BoolVariable(
            "VFS",
            "Set the build root directory as a virtual filesystem root. "
            "All remote cache hits are created as virtual filesystem "
            "placeholders until needed. See http://url/teu2. "
            "This setting is only used if remote caching is enabled.",
            False,
        ),
        BoolVariable(
            "COMPILE_DB",
            "Enable compilation database integration in SCons. When "
            "this parameter is set to True, SCons will register a "
            "compiledb alias that generates a JSON compilation "
            "database file that can be integrated into clang tools.",
            False,
        ),
    )

    # too early to know the list of products, so can't be an EnumVariable.
    opts.AddVariables(("PRODUCT", "Products to build", None))

    # JDK configuration
    # TODO Corretto is disabled until it is approved for use at a later date
    opts.AddVariables(
        EnumVariable(
            "JDK",
            "Set the JDK provider (default = bellsoft)",
            "bellsoft",
            # ["corretto", "bellsoft"],
            ["bellsoft"],
        )
    ),

    opts.AddVariables(
        EnumVariable(
            "BUILDTYPE",
            "Build type (formerly OBJDIR)",
            "obj",
            ["obj", "opt", "beta", "release"],
        ),
        BoolVariable(
            "FULL_SCANNING",
            "If true, tells scons to not use cached "
            "implicit dependencies\n"
            "such as automatically detected C header file dependencies.",
            False,
        ),
        (
            "RELEASE_PACKAGES_DIR",
            "Destination for release packages to be copied to.",
            None,
        ),
        ("BUILDLOG_DIR", "Build logs directory.", None),
        BoolVariable("BUILD_ERROR_SUMMARY", "Enable logging of error summary", True),
        (
            "SCACHE_DIR",
            "Directory to store the cached compiler output from SCons.",
            None,
        ),
        (
            "BRANCH_NAME",
            "The branch this build is occurring on. Usually set by official builds.",
            None,
        ),
        ("WINDOWS_SYSTEM", "The Windows %SYSTEM% variable.", None),
    )

    # The Roots
    opts.AddVariables(
        ("COMPONENTS_ROOT", "Location of downloaded components", None),
    )

    # Gobuild component options
    opts.AddVariables(
        ("GOBUILD_COMPONENTS", "Comma separated list of component=path/to/root", None),
        BoolVariable(
            "GOBUILD_AUTO_COMPONENTS", "Use gobuild components automatically", True
        ),
        BoolVariable(
            "GOBUILD_USE_SHARED_CACHE",
            "Consume shared, cached, components (set by default on DBC)",
            False,
        ),
        BoolVariable(
            "GOBUILD_DISABLE_SHARED_CACHE",
            "Disable consuming shared, cached, components "
            "(overrides GOBUILD_USE_SHARED_CACHE)",
            False,
        ),
        BoolVariable("FORCE_GOBUILD_DEPS", "Force a rerun of gobuild-deps", False),
        ("GOBUILD_HOSTTYPE", "gobuild hosttype. E.g. 'macosx-mavericks'", None),
    )

    # Conan component options
    opts.AddVariables(
        BoolVariable(
            "CONAN_BUILD_MISSING", "Allow conan to build missing dependencies.", False
        ),
    )

    # clang configuration
    opts.AddVariables(
        ("USE_CLANG", "Use clang as a compiler instead of gcc", None),
        ("USE_CLANG_SANITIZER", "Use a clang sanitizer", None),
        ("USE_CLANG_STATIC_ANALYZER", "Use the clang source code analyzer", None),
        ("USE_CLANG_COVERAGE", "Use the clang code coverage", None),
        ("USE_CLANG_TIDY", "Use specified clang-tidy plugin", None),
        ("USE_CLANG_XRAY", "Use specified clang xray plugin", None),
        ("CLANG_PLUGINS", "Use the given clang plugins", None),
    )

    opts.AddVariables(
        (
            "USE_SANITIZERS",
            "Comma separated list of compiler sanitizers to use. Currently supported sanitizers: `address`",
            None,
        ),
        BoolVariable(
            "USE_MSVC_STATIC_ANALYZER",
            "Enable MSVC code analysis which adds a msvc-analyze scons "
            "target that generates a .sarif file of analysis output per "
            "build environment. DISPLAY_COMMAND_OUTPUT=1 can be used to "
            "show the static analyzer output during compilation.",
            False,
        ),
    )

    graph_options = ["summary", "nodes", "edges", "sort"]

    def opt_graph(option, opt, value, parser, graph_options=graph_options):
        gp = vmware.utils.GraphPrinter()
        for o in value.split(","):
            if o == "summary":
                gp.summary = True
            elif o == "nodes":
                gp.nodes = True
            elif o == "edges":
                gp.edges = True
            else:
                raise OptionValueError(
                    "Warning:  %s is not a valid " "--graph option" % o
                )
        parser.values.tree_printers.append(gp)

    opt_graph_help = "Print a dependency graph in various formats: %s." % ", ".join(
        graph_options
    )

    from SCons.Script.Main import OptionsParser

    OptionsParser.add_option(
        "--graph",
        nargs=1,
        type="string",
        dest="tree_printers",
        default=[],
        action="callback",
        callback=opt_graph,
        help=opt_graph_help,
        metavar="OPTIONS",
    )

    tree_options = ["summary", "nodes", "edges", "sort"]

    def opt_file(option, opt, value, parser, graph_options=tree_options):
        filep = vmware.utils.FilePrinter()
        filep.search_str = value
        filep.search_str_lst = value.split(",")
        parser.values.tree_printers.append(filep)

    opt_file_help = "Print the node needed for this file"

    from SCons.Script.Main import OptionsParser

    OptionsParser.add_option(
        "--find",
        nargs=1,
        type="string",
        dest="tree_printers",
        default=[],
        action="callback",
        callback=opt_file,
        help=opt_file_help,
        metavar="OPTIONS",
    )

    if not vmware.IsMinimal():
        # Load Tree specific options. For each tree, look for a
        # tree/scons/treeoptions.py. execfile() that file and pull out the
        # LoadTreeOptions() function, and call it with the opts object.
        for tree in vmware.SourceTreesList():
            treePath = Dir(vmware.PoundPrefixRelativePath(tree)).abspath
            treeoptionspath = os.path.join(treePath, "scons", "treeoptions.py")
            # make a copy of our globals to pass to the subscript.
            g = globals().copy()
            if os.path.exists(treeoptionspath):
                vmware.ExecFile(treeoptionspath, g)

                try:
                    # Call the LoadTreeOptions() function in the tree's
                    # treeoptions.sc'
                    g["LoadTreeOptions"](opts)
                except KeyError:
                    raise vmware.ConfigError(
                        "Tree options file %s does not "
                        "define a LoadTreeOptions function" % treeoptionspath
                    )

    env_opts = Environment(options=opts, tools="")
    vmware.SetLocalOptions(opts.UndeclaredOptions())

    # Setup the help text that gets printed when -h is used
    def GenerateOptionHelpHeader():
        text = """
             ====================
             Command-line options
             ====================
             """
        return inspect.cleandoc(text)

    def GenerateTargetHelpText():
        text = """
             ===============
             Special targets
             ===============

             aliases
                 Enumerates all aliases (top-level targets) registered by
                 the build system for the given PRODUCT.

             clobber
                 Clobbers your BUILDROOT directory (default: bora/build).

             <product>-deps
                 Primes the Gobuild component cache, downloading all
                 components required by a product build.

             prune
                 Clobbers BUILDROOT/build.  Intended for official/sandbox
                 builds to prune intermediate output from the build tree
                 before committing the tree to long-term storage.
             """
        return inspect.cleandoc(text)

    help_text = [
        GenerateOptionHelpHeader(),
        opts.GenerateHelpText(env_opts, True),
        GenerateTargetHelpText(),
    ]
    help_text = "\n".join(help_text)
    Help(help_text)

    return env_opts


def DeleteTempDirectory():
    """
    At Scons exit time, when PRESERVE_TEMP_DIRECTORY=True is not
    provided, delete the temporary directory.
    """
    if not vmware.LocalOpts.GetBool("PRESERVE_TEMP_DIRECTORY", False):
        log = vmware.GetLogger("main")

        tmpdir = vmware.DirAbsPath(vmware.TempDirectory())
        log.info("Deleting temporary directory tree %s" % tmpdir)
        vmware.utils.DeleteDirectory(tmpdir, warn=False)


def ConfigureTemporaryDirectory(log):
    """
    Creates a temporary directory where Scons can write temporary files.

    The directory is keyed to each Scons process, and its contents are removed
    at the end of the build.
    """
    tmpdir = vmware.TempDirectory()
    log.info("Creating temporary directory: %s" % tmpdir)
    Execute(vmware.Mkdir(tmpdir))

    atexit.register(DeleteTempDirectory)


def CreateDirectory(path):
    if not os.path.exists(path):
        os.makedirs(path)


def ConfigureMinimal():
    """
    Minimal initialization logic, usually only used by consumers of the
    vmware-scons gobuild component.
    """
    env_opts = SetupOptions()

    # For vmware.Verbose() and other config related things.
    vmware.ImportConfigModule(
        "core", File("#scons/lib/vmware/config.py").abspath, env_opts
    )
    ConfigureSCons()

    env = DefaultEnvironment()
    env.substCache = {"___self___": env}

    # Set environment for build related stuff (not cross-compile
    # environments).
    buildHost = vmware.HostObject(vmware.HostFromBuildHost())
    # Scons Minimal can't make decisions about itself
    # buildHost.Decider(vmware.VMwareDecider)
    vmware.SetBuildHostEnvironment(buildHost)

    host = vmware.HostObject(env_opts["HOST"])

    # Provide Host() accessor to provide a similar interface to
    # VMwareEnvironment.
    env.Host = lambda: host

    # Set accessor for base environment.
    vmware.SetCurrentHostBaseEnv(env)

    # Done in main().
    vmware.SetBuildType(env_opts["BUILDTYPE"])

    # Also from main() - If BUILDROOT doesn't exist, try to make it
    buildRootPath = vmware.BuildRoot()
    if not os.path.exists(vmware.DirAbsPath(buildRootPath)):
        Execute(vmware.Mkdir(buildRootPath))

    # Set up the logging system.
    vmware.SetLoggingOptions(env_opts["LOGGING"], env_opts["DEFAULT_LOGLEVEL"])
    # Now we can log.
    log = vmware.GetLogger("main")

    ConfigureTemporaryDirectory(log)

    # Required so that some functions are created.
    SetIncludeCacheOptions(False, False)

    # XXX: This doesn't get initialized to anything substitution fails
    # so give it a default.
    env["RPATH"] = []

    # Taken from envs/base.py.
    # Clear out the parent environment
    if vmware.Host().IsLinux():
        env["PATH"] = ""

    if vmware.BuildHostIsLinux():
        env.Replace(SHELL="/build/toolchain/lin32/bash-3.2/bin/bash")

    # Record values that show up on the command line
    env.Replace(VMWARE_HOST=vmware.Host().Name(), VMWARE_BUILDTYPE=vmware.BuildType())


def ConfigureSCons():
    # Not sure why, but this line is required for subsequent calls into the
    # SCons module.
    global SCons
    """Configures scons runtime variables to our liking"""

    # Disable SCons' swallowing exceptions when variable are substituted
    # (expanded) so we get errors if someone tries to use an undefined
    # variable, or has a syntax error in a ${} code expansion, or the like.
    AllowSubstExceptions(None)

    # Make scons warnings fatal
    SCons.Warnings.warningAsException(True)

    # Replace the default scons directory scanner with our own, which ignores
    # certain files that we don't care about.
    SCons.Defaults.DirScanner = vmware.VMwareDirScanner()
    SCons.Script.DirScanner = vmware.VMwareDirScanner()

    # Disables checking for source code repositories
    Dir("#").set_src_builder(None)

    default_env = DefaultEnvironment()
    default_env._build_signature = 1

    try:
        import SCons.Sig
    except ImportError:
        # The signature refactoring code eliminated this module,
        # so this isn't a problem.
        pass
    else:
        default_env._calc_module = SCons.Sig.default_module

    import SCons.Errors

    # Setup our 'decider' function which uses timestamps to make up-to-date
    # decision most of the time, but md5's on certain marked files.
    # Note that VMwareDecider is also for content-based decisions, allowing
    # us to control how deciders work for us.
    default_env.Decider(vmware.VMwareDecider)

    # Original implemntation of SCons node decider is a function call chain to
    # call the decider method of environment finally, so patching it to call
    # VMwareDecider directly for better performance.
    import SCons.Node.FS

    if default_env._get_major_minor_revision(SCons.__version__) >= (2, 5, 0):
        # Newer scons have a _decider_map (function table), so we replace entries
        from SCons.Node import _decider_map, decide_source, decide_target

        for k, f in _decider_map.items():
            if f in [decide_source, decide_target]:
                _decider_map[k] = vmware.VMwareDecider
    else:
        # For our traditional 2.0.x, and also works for 2.3.x
        SCons.Node.FS.File.decide_source = vmware.VMwareDecider
        SCons.Node.FS.File.decide_target = vmware.VMwareDecider
        SCons.Node.FS.File.changed_since_last_build = vmware.VMwareDecider

    def disabled_method(*args, **kw):
        msg = """
Methods SourceSignatures() and TargetSignatures() are disabled.  If intent to be
decided by content, please mark the nodes by vmware.AddHashedNodes(nodelist).
"""
        raise vmware.ScriptError(msg)

    # disable below methods so that only VMwareDecider will be used as Decider
    # method
    SCons.Environment.Base.SourceSignatures = disabled_method
    SCons.Environment.Base.TargetSignatures = disabled_method

    def wrapAlwaysBuild(origAlwaysBuild):
        def wrappedAlwaysBuild(*args, **kwargs):
            nodeList = origAlwaysBuild(*args, **kwargs)
            vmware.environment.AddAlwaysBuiltNodes(nodeList)
            return nodeList

        return wrappedAlwaysBuild

    SCons.Environment.Base.AlwaysBuild = wrapAlwaysBuild(
        SCons.Environment.Base.AlwaysBuild
    )

    # replace FileNodeInfo.update with our own version that knows how to
    # check our hashed node set to see whether we need load a csig.
    #
    # This prevents us from hashing a file every time it gets built, even if
    # we don't use it. It saves time by avoiding the hash, and saves memory
    # by not having to store the result.
    def custom_update(self, node):
        if node in vmware.HashedNodes():
            fields = ["timestamp", "csig", "size"]
        else:
            fields = ["timestamp", "size"]

        for f in fields:
            try:
                delattr(self, f)
            except AttributeError:
                pass

            try:
                func = getattr(node, "get_" + f)
            except AttributeError:
                pass
            else:
                setattr(self, f, func())

    SCons.Node.FS.FileNodeInfo.update = custom_update.__get__(
        None, SCons.Node.FS.FileNodeInfo
    )

    def fs_custom_lookup(self, p, directory, fsclass, create=1):
        """
        Wraps the built-in SCons Node _lookup function to support additional
        namespaces.

        @see: vmware.LookupNamespacePath for details.

        :param self: Supplies the filesystem object to examine.
        :param p: Supplies the name string, proxy object, or node to lookup.
        :param directory: Optionally supplies the parent directory object for
                          relative lookups.
        :param fsclass: Supplies the expected SCons.Node.Base type, either
                        File, Dir, or Entry.
        :param create: Supplies true if a Node should be created if one doesn't
                       already exist.
        :return: Receives the located or created Node.
        """
        if isinstance(p, SCons.Node.FS.Base):
            # Do what SCons does - make sure it's the right class and return.
            # 25% of calls take this path for visor-pxe builds.
            p.must_be_same(fsclass)
            return p

        # Note: str(p) will convert Subst's proxy objects into strings
        p = str(p)
        if p and p[0] == "^":
            n, p = vmware.LookupNamespacePath(p, fsclass, create)
            if n is not None:
                vmware.VerifyLookupAllowed(n)
                return n

        n = self._old_fs_lookup(p, directory, fsclass, create)
        vmware.VerifyLookupAllowed(n)
        return n

    SCons.Node.FS.FS._old_fs_lookup = SCons.Node.FS.FS._lookup
    SCons.Node.FS.FS._lookup = fs_custom_lookup.__get__(None, SCons.Node.FS.FS)

    def node_custom_for_signature(self):
        """
        Wraps the normal SCons File/Dir/Entry signature name getter to
        support additional namespaces and return paths instead of mere
        filenames.

        :param self: Supplies a File/Dir/Entry Node.
        :return: Receives the text to use in signature calculations.
        """
        try:
            return self._memo["_sname"]
        except KeyError:
            pass

        nodePathNorm = os.path.normcase(self.get_abspath())
        sname = vmware.AbsPathToSignatureName(nodePathNorm, isNormalized=True)

        self._memo["_sname"] = sname
        return sname

    SCons.Node.FS.Base.for_signature = node_custom_for_signature.__get__(
        None, SCons.Node.FS.Base
    )

    def action_custom_get_implicit_deps_arg(self, arg, env, entry_count):
        """
        Wraps the built-in SCons command action argument-to-node logic to
        support signature names for custom namespaces. We also now return
        source-tree relative signature names with # prefixes, so we convert
        those too.
        """
        if arg.startswith(("#", "^")):
            path = vmware.SignatureNameToAbsPath(arg)
            if path and os.path.isfile(path):
                if arg.startswith("^build/root/") and entry_count > 0:
                    # To maintain existing behavior, we only support built
                    # implicit command dependencies for the program name.
                    # We will relax this is in subsequent commits.
                    return None

                # Note: we could pass arg, but its faster to pass path.
                node = env.fs.File(path)
                return [node]
            else:
                # We don't want to call the old logic: if will treat
                # ^ and # as actual filename characters.
                return None

        return self._old_get_implicit_deps_arg(arg, env, entry_count)

    SCons.Action.CommandAction._old_get_implicit_deps_arg = (
        SCons.Action.CommandAction._get_implicit_deps_arg
    )
    SCons.Action.CommandAction._get_implicit_deps_arg = (
        action_custom_get_implicit_deps_arg.__get__(None, SCons.Action.CommandAction)
    )

    def node_custom_get_implicit_deps(self, env, scanner, path, kw={}):
        """
        Wraps the built-in SCons implicit dependency calculation. This allows us
        scan headers even through cayman and toolchain and then later consolidate
        them by replacing a list of headers with a build-specific key, which
        saves space in the node graph.
        """
        unfiltered_deps = self._old_get_implicit_deps(env, scanner, path, kw)

        # On Linux we have problems when we consolidate dependencies for the
        # ProgramScanner. Specifically ESX builds fail when linking libvmlibs.so.
        # However, on Windows we really do need to consolidate dependencies for
        # all scanners or else dependency enforcement throws exceptions for pdbs
        # in cayman_msvc_redists.
        #
        # Eventually I hope to remove this exception for ProgramScanner on Linux
        # after fixing the linking issue.
        if vmware.BuildHostIsWindows() or (
            scanner and scanner.name != "ProgramScanner"
        ):
            return vmware.FilterGobuildAndToolchainNodes(self, env, unfiltered_deps)
        else:
            return unfiltered_deps

    SCons.Node.Node._old_get_implicit_deps = SCons.Node.Node.get_implicit_deps
    SCons.Node.Node.get_implicit_deps = node_custom_get_implicit_deps.__get__(
        None, SCons.Node.Node
    )


_buildNumNodes = None
_buildNumNodesInitialized = False
_filesUsingBuildNumber = set()


def VersionOverrideShouldRetrieveFromCache(self):
    """
    This function overrides SCons.Node.FS.File.should_retrieve_from_cache to
    return False for any nodes that, directly or indirectly, have exposure
    to the build number files.

    Unfortunately the results can't always be memoized because this function
    could be called early during SCons module registration and then called
    again later by the job scheduler. In the meantime, the node may have been
    scanned so its dependencies would have been changed and our cached result
    would not be applicable. The only safe memoization we can do is that if
    we detect build number exposure once, we can remember that for all
    subsequent calls to this function. That is safe because dependencies should
    not disappear during one run of SCons.
    """
    global _buildNumNodes, _buildNumNodesInitialized

    if self in _filesUsingBuildNumber:
        # Override to not being cacheable.
        return False

    if not _buildNumNodesInitialized:
        try:
            _buildNumNodes = vmware.LookupNode(
                "build-number", host="unknown", buildtype="unknown", missingOk=True
            )
        except vmware.ScriptError:
            pass

        _buildNumNodesInitialized = True

    # Note: we still need to update _filesUsingBuildNumber even if this returns
    # False.
    originalValue = self._old_should_retrieve_from_cache()

    if not _buildNumNodes:
        # This build may not use buildnum.py.
        return originalValue

    sources = getattr(self, "sources", None)
    depends = getattr(self, "depends", None)
    implicit = getattr(self, "implicit", None)
    if sources is None or depends is None or implicit is None:
        # This node may not have been scanned yet. We will likely be called again
        # later after the node has been scanned and it is ready to build.
        return originalValue

    # Note: for performance reasons, we don't see if the build number nodes are
    # marked with env.Ignore(d).
    for d in itertools.chain(sources, depends, implicit):
        if d in _buildNumNodes or d in _filesUsingBuildNumber:
            # Remember that all targets in the executor have this dependency.
            # This is important because SCons stops calling
            # should_retrieve_from_cache after the first target returns False.
            for t in self.get_executor().get_all_targets():
                _filesUsingBuildNumber.add(t)

            # Override to not being cacheable.
            return False

    return originalValue


_actionBaseCustomGetContentsReplacements = None
_actionBaseCustomGetContentsDrives = None


def _InitializeActionBaseCustomGetContentsGlobals():
    """
    Initializes the globals used by _ActionBaseCustomGetContents.
    This logic is separated into its own function because it is nontrivial.
    """
    global _actionBaseCustomGetContentsReplacements
    global _actionBaseCustomGetContentsDrives

    # Order is important so that SRCROOT is evaluated last, just in case
    # any of the other folders (particularly BUILDROOT and GOBUILD_ROOT)
    # are subdirectories of SRCROOT.
    # Note 1: vmware.DirAbsPath() doesn't handle network-mounted toolchain
    # (e.g. "T:") correctly, so we use os.path.abspath instead.
    # Note 2: all entries in "replacements" end with a trailing slash to
    # bad substitutions for network-mounted toolchain; for example, if
    # TCROOT=T: we don't want to change "-OUT:a.dll" -> "-OU%%TCROOT%%a.dll".
    isWindows = vmware.BuildHostIsWindows()
    depcopEnabled = vmware.DependencyEnforcementEnabled()
    srcRoot = os.path.join(vmware.DirAbsPath("#"), "")
    buildRoot = os.path.join(vmware.DirAbsPath(vmware.BuildRoot()), "")
    gobuildRoot = os.path.join(vmware.DirAbsPath(vmware.ComponentsRoot()), "")
    conanRoot = os.path.join(vmware.DirAbsPath(vmware.ConanRoot()), "")
    releasePkgsRoot = os.path.join(vmware.DirAbsPath(vmware.ReleasePackagesDir()), "")
    replacements = []

    # Any gobuild components that are listed as filtering exceptions also need
    # have their action string escaped differently. Without this, we will still
    # have action strings that include something like:
    #   /I"%%GOBUILD_ROOT%%\crtbora\ob-12341234\win64_vc140\include
    # and thus a component bump would still cause cache invalidation.
    gobuildComponents = vmware.GetGobuildDependencies()
    for alias in sorted(vmware.LocalOpts.GetSet("GOBUILD_FILTER_EXCEPTIONS", [])):
        # gobuild module normalizes names by replacing dashes with underscores
        # (e.g. vmware.GetGobuildDependencies() result has underscores), so we
        # match that here.
        alias = alias.replace("-", "_")

        if alias not in gobuildComponents:
            # Developer build where this platform doesn't consume this component.
            continue

        path = vmware.GetGobuildComponent(alias)
        if not path:
            # Gobuild-bootstrapped build where this platform doesn't consume this
            # component.
            continue

        # Two notes about the following append() call:
        #   1. four % characters are needed on each side because the format
        #      operation will convert these to two.
        #   2. We don't append a trailing slash here to avoid issues when
        #      escaping paths to the exact component path. For example, the cds
        #      vtool puts cdsng's gobuild path in the include list with no
        #      subdirectory and without a trailing slash and we want to handle
        #      that case.
        replacements.append(
            ("%%%%GOBUILD_%s_ROOT%%%%" % alias.upper(), vmware.DirAbsPath(path))
        )

    # %%GOBUILD_ROOT%% must be after any per-gobuild-component replacements.
    replacements.append(("%%GOBUILD_ROOT%%" + os.path.sep, gobuildRoot))

    # This can be right before or after %%GOBUILD_ROOT%%.
    replacements.append(("%%CONAN_ROOT%%" + os.path.sep, conanRoot))

    # This must be before %%BUILDROOT%% to handle the case where
    # ReleasePackagesDir is under the build root, which can happen on local
    # builds.
    if vmware.ReleasePackagesDirNamespaceEnabled():
        replacements.append(("%%RELEASEPKGSROOT%%" + os.path.sep, releasePkgsRoot))

    # This must be after any gobuild component replacements in case gobuild root
    # is a subdirectory of build root. If %%BUILDROOT%% was first in the list,
    # gobuild components would mistakenly be escaped using %%BUILDROOT%% instead
    # of %%GOBUILD_ROOT%%.
    replacements += [
        ("%%BUILDROOT%%" + os.path.sep, buildRoot),
    ]

    for variable, path in list(replacements):
        # On Windows, account for how some vtools (e.g. libiconv in
        # providing /DICONVPATH) escape backslashes by putting two in.
        if isWindows:
            replacements.append(
                (variable.replace("\\", "\\\\"), path.replace("\\", "\\\\"))
            )

        # On all platforms, support replacement for relative paths from the
        # source root.
        if vmware.IsFromDir(os.path.normpath(path), os.path.normpath(srcRoot)):
            relativePath = path[len(srcRoot) :]
            if relativePath:
                replacements.append((variable, relativePath))

    # As mentioned above, SRCROOT needs to be last.
    replacements.append(("%%SRCROOT%%" + os.path.sep, srcRoot))
    if isWindows:
        replacements.append(("%%SRCROOT%%\\\\", srcRoot.replace("\\", "\\\\")))

    # The replacements object needs to have bytes-like objects instead of
    # strings because SCons passes in an object generated using:
    # b''.join([etc]). Python 2 is forgiving but Python 3 requires
    # that the types match in replace().
    _actionBaseCustomGetContentsReplacements = [
        (r[0].encode(), r[1].encode()) for r in replacements
    ]

    # Remember the relevant drive paths for path validation in
    # _ActionBaseCustomGetContents.
    if isWindows and depcopEnabled:
        _actionBaseCustomGetContentsDrives = set()
        for var, path in _actionBaseCustomGetContentsReplacements:
            if os.path.isabs(path):
                drive = os.path.splitdrive(path)[0]
                _actionBaseCustomGetContentsDrives.add(drive.upper())
                _actionBaseCustomGetContentsDrives.add(drive.lower())


def _ActionBaseCustomGetContents(self, target, source, env):
    """
    This override of ActionBase.get_contents() is designed to normalize
    absolute paths in actions so that they will be the same across
    build environments.
    """
    contents = self._old_get_contents(target, source, env)

    # No need to override contents of actions with uncacheable targets.
    for t in target:
        if not t.should_retrieve_from_cache():
            return contents

    if _actionBaseCustomGetContentsReplacements is None:
        _InitializeActionBaseCustomGetContentsGlobals()

    drives = set()
    for var, path in _actionBaseCustomGetContentsReplacements:
        contents = contents.replace(path, var)

    # This is a bit draconian, but we need builds to fail if we have
    # dependency enforcement enabled and the build is using an action
    # string that won't support caching because it has an absolute path
    # in it.
    if vmware.BuildHostIsWindows() and vmware.DependencyEnforcementEnabled():
        for drive in _actionBaseCustomGetContentsDrives:
            if contents.find(drive + b"/") != -1 or contents.find(drive + b"\\") != -1:
                raise Exception(
                    "Action command string contains an absolute "
                    "path, which is illegal. Was it normalized "
                    "properly? Action string is:\n\t%s" % contents
                )

        for var, path in _actionBaseCustomGetContentsReplacements:
            if contents.find(path.replace(b"\\", b"/")) != -1:
                raise Exception(
                    "Action command string contains path %s "
                    "but does not use the proper slashes, "
                    "which will break caching. Ensure that "
                    "this path is normalized properly. Action "
                    "string is:\n\t%s" % (var, contents)
                )

    return contents


def ConfigureOptionalSConsOverrides():
    """
    This function performs any optional SCons overrides. These overrides could
    not be done in ConfigureSCons() because vmware.LocalOpts had not yet been
    initialized.
    """
    global depcop

    depcopEnabled = vmware.DependencyEnforcementEnabled()
    depcopTrackAll = depcopEnabled and vmware.LocalOpts.GetBool(
        "DEPCOP_TRACK_ALL", False
    )
    cacheFetchEnabled = vmware.RemoteCacheFetchEnabled()
    cachePushEnabled = vmware.RemoteCachePushEnabled()
    cacheEnabled = cacheFetchEnabled or cachePushEnabled

    # We must better understand our dependencies if either of dependency
    # enforcement or remote caching is enabled. The former is important because
    # we need our dependencies correct when dependencies are enforced. The
    # latter is important because we want consistent cache signatures regardless
    # of whether dependency enforcement is enabled.
    if depcopEnabled or cacheEnabled:
        if cachePushEnabled and not depcopEnabled:
            raise Exception(
                "REMOTE_CACHE_PUSH=True requires that dependency "
                "enforcement is also enabled. Provide DEPCOP=True "
                "to resolve this issue."
            )

        scannerlog = vmware.GetLogger("scanner")
        scannerinfo = scannerlog.isEnabledFor(logging.INFO)

        def command_action_custom_get_implicit_deps(
            self, target, source, env, executor=None
        ):
            """
            Overrides the SCons CommandAction.get_implicit_deps function to
            filter gobuild and toolchain nodes.
            """
            res = self._old_get_implicit_deps(target, source, env, executor)
            return vmware.FilterGobuildAndToolchainNodes(self, env, res)

        # Replace CommandAction.get_implicit_deps with our version that filters
        # gobuild and toolchain nodes.
        SCons.Action.CommandAction._old_get_implicit_deps = (
            SCons.Action.CommandAction.get_implicit_deps
        )
        SCons.Action.CommandAction.get_implicit_deps = (
            command_action_custom_get_implicit_deps.__get__(
                None, SCons.Action.CommandAction
            )
        )

        # If build number information is overridden, we want to avoid going to
        # the cache unnecessarily for nodes that have exposure to the build
        # number files. The only exception is if SCons was provided
        # DEPCOP=True and DEPCOP_TRACK_ALL=True, which is given when
        # developers want to track all actions as part of initial build
        # validation. This helps us avoid cases where sandbox builds would
        # succeed because the file wouldn't be validated but tinderbox would
        # fail because build number info isn't overridden there.
        if vmware.IsBuildNumberInfoOverridden() and not depcopTrackAll:
            SCons.Node.FS.File._old_should_retrieve_from_cache = (
                SCons.Node.FS.File.should_retrieve_from_cache
            )
            SCons.Node.FS.File.should_retrieve_from_cache = (
                VersionOverrideShouldRetrieveFromCache.__get__(None, SCons.Node.FS.File)
            )

        # Replace ActionBase.get_contents with the one that normalizes paths.
        SCons.Action.ActionBase._old_get_contents = SCons.Action.ActionBase.get_contents
        SCons.Action.ActionBase.get_contents = _ActionBaseCustomGetContents.__get__(
            None, SCons.Action.ActionBase
        )

        if depcopEnabled:
            import vmware.depcopWindows as depcop

            SCons.Action.CommandAction._old_process = SCons.Action.CommandAction.process
            SCons.Action.CommandAction.process = depcop.WrapActionProcess.__get__(
                None, SCons.Action.CommandAction
            )

            SCons.Executor.Executor._old_call = SCons.Executor.Executor.__call__
            SCons.Executor.Executor.__call__ = depcop.WrapExecutorCall.__get__(
                None, SCons.Executor.Executor
            )


# SCons.Node.Annotate is set to this function by certain targets,
# so that the default is to NOT cache derived files -- we then
# selectively set nocache to 0 (i.e., use cache) for derived
# files we do want to cache (e.g., C/C++ objects)
def AnnotateSetDefaultNoCache(node):
    node.nocache = 1  # For now, we want to default to nocache


# Where it all starts
def main():
    ConfigureSCons()

    # Set up the logging system
    vmware.InitLogging()

    # Now we can log with default log levels
    log = vmware.GetLogger("main")

    # Given the plethora of wrapper scripts available on any given system,
    # emit to the log exactly which Python interpreter is running this build.
    log.info(
        "Running SCons %s using Python %d.%d.%d from %s",
        vmware.SConsVersion(),
        sys.version_info.major,
        sys.version_info.minor,
        sys.version_info.micro,
        sys.executable,
    )

    # The ESXi build, in particular the part that creates the tardisk
    # images, really assumes that files/directories are created with
    # this umask.  It would be more robust to make sure permissions
    # were explicitly set on all files we stage into the tar disk
    # images, see PR#650996, but this should at least fix the most
    # egregious issues where builds are done in environments with other
    # umasks.
    os.umask(0o022)

    # A hook for testing. If SCONS_TEST_SCRIPT is defined, then the file pointed
    # to by the variable will be executed in place of the normal scons scripts.
    testScript = os.environ.get("SCONS_TEST_SCRIPT", None)
    if testScript:
        log.info("Running test script: %s" % (testScript))
        SConscript(testScript)
        return

    # Register our exit hook
    vmware.RegisterExitFunc()

    vmware.LoadSourceTrees()

    env_opts = SetupOptions()

    # Now we can set log levels based on command line or Local.sc options
    vmware.SetLoggingOptions(
        env_opts["LOGGING"],
        env_opts["DEFAULT_LOGLEVEL"],
        env_opts["LOGGING_SHOW_MODULE"],
        env_opts["LOGGING_SHOW_LEVEL"],
        env_opts["LOGGING_SHOW_DATE"],
        env_opts["LOGGING_SHOW_TIME"],
        env_opts["LOGGING_SHOW_MILLIS"],
        env_opts["LOGGING_SHOW_FILE"],
    )

    # If user specified -h, then just exit, because all the information printed
    # for help is loaded already.
    if "-h" in sys.argv or "--help" in sys.argv:
        vmware.AddExitFunc(vmware.utils.ExtraHelp)
        return

    ########################################################
    # Initialize subsystems.
    vmware.phase.SetPhase("init")

    if "PRODUCT" in env_opts:
        pname = env_opts["PRODUCT"]

        # for some strange reason we allow products to be
        # specified as <product-name>-<host>.   Handle this case,
        # by effectively disallowing a '-' to be product names.
        if "-" in pname:
            oldProduct = pname
            pname = pname.split("-")[0]
            log.warning(
                'Specifying "-" not allowed in product names.  Using '
                "PRODUCT=%s instead of PRODUCT=%s.",
                pname,
                oldProduct,
            )

        vmware.SetRequestedProduct(env_opts["PRODUCT"])
        vmware.LoadProductDefinitions()
        vmware.PickProductDefinition(env_opts, pname)
        vmware.PickSourceTree(env_opts["PRODUCT"])

        # Append the tree scons/lib directory to the import path
        treeLibDir = Dir(
            vmware.PoundPrefixRelativePath(
                os.path.join(vmware.SourceTree(), "scons", "lib")
            )
        ).abspath
        if os.path.exists(treeLibDir):
            sys.path.append(treeLibDir)
    else:
        log.error("Must specify a value for PRODUCT")
        log.error("Possible values are: %s", ", ".join(vmware.GetPossibleProducts()))
        Exit(1)

    # Load the core config variable accessor functions
    configModulePath = File("#scons/lib/vmware/config.py").abspath
    vmware.ImportConfigModule("core", configModulePath, env_opts)

    # There are two cases where we only support caching of compilation actions:
    #    1. Linux platform.
    #    2. Windows platform with squishcoco code coverage enabled.
    # To support these cases, we install a hook that disables caching by
    # default. During a build, specific builders will then opt into caching by
    # enabling it on their newly-created nodes.
    if (vmware.RemoteCacheFetchEnabled() or vmware.DependencyEnforcementEnabled()) and (
        vmware.BuildHostIsLinux()
        or (
            vmware.BuildHostIsWindows()
            and vmware.LocalOpts.GetBool("ENABLE_CODE_COV", False)
        )
    ):
        # We could call the original annotate function, but that
        # would slow SCons down a tiny bit on large graphs. If
        # this is absolutely necessary, here is the work:
        # 1. Save original annotate value in a global, and
        # 2. call original in callback.
        if SCons.Node.Annotate != SCons.Node.do_nothing_node:
            m = "SCons.Node.Annotate set to non-default:"
            m += " Chaining of this hook is not supported, aborting"
            raise vmware.ScriptError(m)

        SCons.Node.Annotate = AnnotateSetDefaultNoCache

    # Now that vmware.LocalOpts has been initialized, load any optional SCons
    # overrides.
    ConfigureOptionalSConsOverrides()

    # With vmware.config available, fire the product's InitPostConfig hook.
    vmware.GetProductDefinition().InitPostConfig()

    tty = hasattr(sys.stdout, "isatty") and sys.stdout.isatty()
    iscons = vmware.LocalOpts.GetBool("ISCONS", False)

    # Respect COLORED_LOGGING parameter if it's tty or iscons and it's any OS
    # other than Windows before 10.
    if (tty or iscons) and vmware.IsColoredLoggingSupportedByPlatform():
        coloredLogging = vmware.ColoredLogging()
        vmware.LoggingSetColor(coloredLogging)

        # Bug 2692345: iscons with Python 3 loses \r characters, which causes
        # every print of sconsign logging detail to print a new line. Turn off
        # until we can figure out the root cause.
        # SCons.Script.SetOption("logging_sconsign_detail", True)

    # Add the standard set of exit functions
    if not SCons.Script.GetOption("interactive"):
        # Build stats and error summaries are not meaningful when exiting
        # interactive sessions.
        vmware.AddExitFunc(vmware.utils.FailedCommandSummary)
        vmware.AddExitFunc(vmware.utils.ReportBuildStats)

    # Debugging function for broken null builds.
    vmware.AddExitFunc(vmware.utils.ReportBuildViolations)

    # Debugging function for sporadic Windows failures.
    if vmware.BuildHostIsWindows():
        vmware.AddExitFunc(vmware.utils.DumpWindowsEventLogsOnFailure)

    # With python 3.11 forkers don't work so they can be disabled instead because Python uses vfork() instead of fork(), which gives us better subprocess       launch performance.
    # if vmware.LocalOpts.GetBool("USE_FORKERS", vmware.BuildHostIsLinux()):
    # +1 for the main SCons thread which will run SCons Function actions
    #   ForkerManager().StartForkers(vmware.NumProcessors() + 1)
    #   vmware.AddExitFunc(ForkerManager().Cleanup)

    if vmware.LocalOpts.GetBool("DEBUG_HEAP_USAGE", False):
        vmware.AddExitFunc(vmware.utils.DebugHeapUsage)

    if vmware.LocalOpts.GetBool("TRACK_SCRIPT_HEAP_USAGE", False):
        vmware.utils.EnableScriptHeapTracking()

    if vmware.LocalOpts.GetBool("REPORT_INCLUDES_STATS", False):
        vmware.AddExitFunc(vmware.utils.ReportIncludeStats)

    buildRootPath = vmware.BuildRoot()

    # Remember which build types were requested on the command line
    vmware.SetRequestedBuildType(env_opts["BUILDTYPE"])

    product = vmware.GetRequestedProduct()
    buildtype = vmware.RequestedBuildType()

    # The logic for the next 3 lists can be a bit confusing. Here are summaries:
    #
    # Products in `products_wanting_release_pkg_dirs_caching` want caching
    # enabled in the same way as the previous list, but do NOT always want
    # vmware.ReleasePackagesDir() set to something.
    #
    # Products in `validated_with_default_release_pkg_dirs` must be in one of
    # the first two lists and have had at least one successful sandbox build
    # with remote caching enabled. Products in this list get to avoid a small
    # verification tax during lookup.
    #
    # If you want to include a new product in these lists, follow these steps:
    #    1. Ensure that remote caching is supported and enabled for this product
    #       (see RemoteCachingSupported).
    #    2. Add that product to the first list
    #       `products_wanting_release_pkg_dirs_caching`.
    #    3. Perform at least one GitHub build with remote caching enabled.
    #       That sandbox build must contain a pending change performing step #2.
    #    4. If that GitHub build is successful, add the product to
    #       the `validated_with_default_release_pkg_dirs` list.
    #    5. Send out a code review that contains steps 2 and 4.
    products_wanting_release_pkg_dirs_caching = [
        "appblastlibs",
        "hccrt",
        "horizonagent",
        "horizoncommon",
    ]
    validated_with_default_release_pkg_dirs = [
        "appblastlibs",
        "hccrt",
        "horizonagent",
        "horizoncommon",
    ]

    wants_rpd_caching = product in products_wanting_release_pkg_dirs_caching
    validated_rpd_usage = product in validated_with_default_release_pkg_dirs

    if wants_rpd_caching:
        defaultPackagesDir = os.path.join(buildRootPath, product, buildtype, "publish")
        defaultPackagesDir = vmware.PoundPrefixRelativePath(defaultPackagesDir)
        releasePackagesDir = vmware.ReleasePackagesDir()

        if vmware.RemoteCacheFetchEnabled() or vmware.RemoteCachePushEnabled():

            # Enable the namespace so we can do cache this folder.
            vmware.SetReleasePackagesDirNamespaceEnabled()

            if (
                releasePackagesDir != defaultPackagesDir
                and product not in validated_with_default_release_pkg_dirs
            ):

                # Anything looking under here is using an ambiguous file location
                # when caching is enabled (we must "relativize" cached paths).
                vmware.AddNoLookupPath(
                    defaultPackagesDir,
                    "Use the function "
                    "vmware.ReleasePackagesDir() to get the "
                    "publish folder, as its location can be "
                    "overridden",
                )
    elif validated_rpd_usage:
        raise vmware.ScriptError(
            "Product %s is unexpectedly in the "
            "`validated_with_default_release_pkg_dirs` list but not in the other "
            "two. This is an invalid configuration. See documentation of this "
            "list in scons/init.py for more details." % product
        )

    # Special 'prune' target deletes certain directories out of the buildroot
    # which shouldn't be preserved in official builds.
    if "prune" in SCons.Script.COMMAND_LINE_TARGETS:
        Alias("prune", [])
        vmware.utils.DeleteDirectory(os.path.join(buildRootPath, "build"))

        # package/COMPONENTS directory needs to be pruned as we use a
        # non-standard GOBUILD_AUTO_COMPONENTS=1 option in esxall target.
        # XXX: This can be removed when esxall does not rely on it.
        vmware.utils.DeleteDirectory(
            os.path.join(buildRootPath, "package", "COMPONENTS")
        )

        # build/$PRODUCT/$BUILDTYPE/tardiskStage is used as a staging directory
        # for intermediate files generated by createTardisk method.
        # XXX: This can be removed when the entire staging directory,
        # build/$PRODUCT/$BUILDTYPE, is pruned.
        vmware.utils.DeleteDirectory(
            os.path.join(
                buildRootPath,
                vmware.GetRequestedProduct(),
                vmware.RequestedBuildType(),
                "tardiskStage",
            )
        )

        if len(COMMAND_LINE_TARGETS) == 1:
            # Exit if prune is the only target
            Exit(0)

    # The very special 'clobber' target wipes out any old BUILDROOT
    if "clobber" in SCons.Script.COMMAND_LINE_TARGETS:
        # 'clobber' is a fake target that:
        # 1) if defined, wipes everything before we run anything
        # 2) actually builds nothing
        # Unfortunately, removing it from COMMAND_LINE_TARGETS and BUILD_TARGETS
        # doesn't work. -- kevinc
        Alias("clobber", [])

        vmware.utils.DeleteDirectory(buildRootPath, deleteToplevelSymlink=False)
        if os.path.isdir(Dir(vmware.ReleasePackagesDir()).abspath):
            log.warning("Deleting RELEASE_PACKAGES_DIR=%r", vmware.ReleasePackagesDir())
            vmware.utils.DeleteDirectory(vmware.ReleasePackagesDir(), warn=False)

        if len(COMMAND_LINE_TARGETS) == 1:
            # If 'clobber' is the only thing on the command line, then we don't
            # need to do anything else.
            Exit(0)

    # If BUILDROOT doesn't exist, try to make it
    if not os.path.exists(vmware.DirAbsPath(buildRootPath)):
        log.debug("Creating BUILDROOT directory: %s", buildRootPath)
        Execute(vmware.Mkdir(buildRootPath))

    # If build logs directory doesn't exist, create it.
    if not os.path.exists(vmware.DirAbsPath(vmware.BuildLogDir())):
        log.debug("Creating build logs directory: %s", vmware.BuildLogDir())
        Execute(vmware.Mkdir(vmware.BuildLogDir()))

    # Create the component directory
    if not os.path.isdir(vmware.DirAbsPath(vmware.ComponentsRoot())):
        Execute(vmware.Mkdir(vmware.ComponentsRoot()))

    ConfigureTemporaryDirectory(log)

    # create the build info & lock file directories
    CreateDirectory(vmware.DirAbsPath(vmware.BuildInfoDir()))
    CreateDirectory(vmware.DirAbsPath(vmware.LockDir()))

    if "SCACHE_DIR" in env_opts:
        CacheDir(env_opts["SCACHE_DIR"])
    SConsignFile(File(os.path.join(vmware.BuildRoot(), ".sconsign")).abspath)

    SetIncludeCacheOptions(
        True, vmware.LocalOpts.GetBool("VERIFY_INCLUDE_CACHE", False)
    )

    # Now that the source tree is picked, load the config variable module
    # from that tree from a well known location, tree/scons/treeconfig.py.
    sourceTreePath = Dir(vmware.PoundPrefixRelativePath(vmware.SourceTree())).abspath
    configModulePath = os.path.join(sourceTreePath, "scons", "treeconfig.py")
    if os.path.exists(configModulePath):
        log.debug("Importing config module %s" % (configModulePath))
        vmware.ImportConfigModule(vmware.SourceTree(), configModulePath, env_opts)
    else:
        # For now, it's ok if we don't find anything here.
        log.warning("No config module found at: %s" % (configModulePath))

    # Initialize the scons environment for our build host
    # Must come after init. of buildroots, but before reading in of foreign build
    # trees.
    vmware.InitBuildHostEnvironment()

    # Create gobuild-component alias
    Alias("gobuild-component")

    # Load in registered nodes from foreign trees, but only if we're actually
    # building
    if not vmware.DumpNodesOnly():
        buildRootRealPath = os.path.realpath(vmware.DirAbsPath(buildRootPath))
        for aPathSpec in env_opts["BUILDTREES"].split(","):
            if len(aPathSpec) == 0:
                continue
            try:
                (buildhost, buildtreepath) = aPathSpec.split("=")
                buildtreepath = vmware.PoundPrefixRelativePath(buildtreepath)
            except ValueError:
                log.error(
                    "BUILDTREES must be a comma separated list of <build host>=<path> pairs.\n"
                    "     For example: BUILDTREES=Linux=/foo/bar,Win32=/foo/baz"
                )
                Exit(1)

            buildTreeRealPath = os.path.realpath(vmware.DirAbsPath(buildtreepath))

            if buildRootRealPath == buildTreeRealPath:
                log.error(
                    "BUILDROOT and BUILDTREES (%s) point to the same path: \n     %s\n"
                    "   Different build hosts should not share the same BUILDROOT"
                    % (buildhost, buildTreeRealPath)
                )
                Exit(1)

            vmware.LoadRegisteredNodes(buildtreepath, buildhost)

    # All non-deprecated uses of env_opts stops here. Wrap the object in a
    # proxy object so that we can trace accesses to it on demand.
    if vmware.GetLogger("deprecated").isEnabledFor(logging.INFO):
        env_opts = vmware.DeprecationProxy(env_opts, "env_opts")

    Export("env_opts")

    # We use the subprocess module to launch build commands. On Windows, this
    # calls cmd.exe with the build comand. We need to pass it the /d argument
    # which prevents cmd.exe from reading any startup scripts specified by
    # special registry keys.
    # XXX: This breaks builds that use "rpcgen.exe"
    # see https://reviewboard.eng.vmware.com/r/267008/
    # if vmware.BuildHostIsWindows():
    #   log.debug("Setting custom COMSPEC")
    #   os.environ['COMSPEC'] = 'cmd.exe /d'

    if vmware.DependencyEnforcementEnabled():
        error = depcop.Initialize()
        if error:
            log.error(error)
            Exit(1)

    vmware.phase.SetPhase("build-init")
    script = os.path.join(
        vmware.PoundPrefixRelativePath(vmware.SourceTree()), "scons/build.py"
    )
    log.info("loading %s" % script)
    SConscript(script)

    # Run the default packaging script
    SConscript("#scons/package.py")

    defaultTarget = vmware.GetProductDefinition().default
    if defaultTarget:
        log.debug("Setting default target to %s" % defaultTarget)
        SCons.Script.Default(defaultTarget)

    # Execute special local file if it exists, so that user can add custom
    # aliases. It happens here because all aliases need to be defined before
    # custom ones that refer to them can be set up correctly.
    localAliasesPaths = ["#LocalAliases.sc", "#bora/LocalAliases.sc"]
    for p in localAliasesPaths:
        localAliasesNodes = vmware.GlobSourceDir(p)
        if localAliasesNodes:
            log.info("Executing local aliases script at %s", p)
            SConscript(p)
            break

    # Dump all registered globals to a file at the buildroot
    nodedumpfile = vmware.FileAbsPath(
        os.path.join(vmware.BuildInfoDir(), "registered-nodes.txt")
    )
    UpdateRegisteredNodes(nodedumpfile)

    # In dump nodes only mode, we simply exit before we build anything. This is
    # because we never read in nodes from remote buildtrees, and thus our
    # dependency graph has been populated with place holder nodes that were
    # returned by LookupNode when a foreign node was requested. These can't
    # possibly build, so we exit instead. You shouldn't be using dump nodes
    # only mode if you actually want to build something.
    if vmware.DumpNodesOnly():
        Exit(0)

    if vmware.LocalOpts.GetString("COVERITY_SCAN", "") != "":
        if not vmware.GetProductDefinition().ALLOW_COVERITY_SCAN:
            raise vmware.ScriptError(
                "Product '%s' is not in the allowed "
                "products list for Coverity scans. See "
                "http://wiki.eng.vmware.com/SCons/Coverity" % vmware.Product()
            )
        vmware.AddExitFunc(vmware.utils.CoverityPostScanAnalysis)

    # If the GENERATE_SOURCE_LIST option is set, fetch the sources used by
    # each of the command line targets.
    if vmware.LocalOpts.GetBool("GENERATE_SOURCE_LIST", False):
        for t in SCons.Script.COMMAND_LINE_TARGETS:
            vmware.utils.GenerateSourceList(Alias(t, [])[0])

    if vmware.LocalOpts.GetBool("SKIP_BUILD_PHASE", False):
        log.warn("Exiting before build phase due to SKIP_BUILD_PHASE option")
        Exit(0)

    if vmware.LocalOpts.GetBool("REPORT_INSECURE_BINARIES", False):
        vmware.AddExitFunc(vmware.Executable.reportInsecureBinaries)
        Exit(0)

    if vmware.LocalOpts.GetBool("TRACK_SCRIPT_HEAP_USAGE", False):
        vmware.utils.DumpScriptHeapTrackingInfo()

    # Create metadata file, if needed
    vmware.ComponentMetadataFile()

    # Set up remote caching if it was requested.
    cacheFetchEnabled = vmware.RemoteCacheFetchEnabled()
    cachePushEnabled = vmware.RemoteCachePushEnabled()
    if cacheFetchEnabled or cachePushEnabled:
        # Show a useful error message if a person requested remote caching but it
        # isn't supported for their specific configuration.
        if not vmware.RemoteCachingSupported():
            raise Exception(
                "Remote caching is not currently supported for this "
                "product (%s) or platform. If you are testing remote "
                "caching, provide REMOTE_CACHE_RESTRICTIONS=False "
                "to disable this check. Otherwise, refer to "
                "http://url/5qb2 for instructions on how to add "
                "support for remote caching." % vmware.Product()
            )

        # Set the cache URL to our default server if one has not been provided.
        if not SCons.Script.GetOption("remote_cache_url"):
            SCons.Script.SetOption("remote_cache_url", vmware.RemoteCacheUrlDefault())

        if cacheFetchEnabled:
            SCons.Script.SetOption("remote_cache_fetch_enabled", True)

            if vmware.LocalOpts.GetBool("VFS", False):
                # The bootstrapper provides this in the environment.
                root = os.environ.get("CAYMAN_BUILDFASTERD_ROOT", "")
                if not root:
                    raise Exception(
                        "VFS=True requires that cayman_buildfasterd "
                        "be consumed by scons_bootstrap"
                    )

                # cayman_buildfasterd being present implies that we may want VFS
                # integration. Hook it up now just in case.
                if root and vmware.BuildHostIsWindows():
                    buildRootNorm = os.path.normcase(
                        vmware.DirAbsPath(vmware.BuildRoot())
                    )
                    # If the VFSManager is in the build dir, it can't stop
                    # virtualizing and delete the build dir. Catch that case now.
                    if vmware.IsFromDir(os.path.normcase(root), buildRootNorm):
                        log.info(
                            "Remote caching VFS integration is not available "
                            "for your machine because your gobuild component "
                            "cache is a subdirectory of your build root. Set "
                            "GOBUILD_LOCALCACHE_DIR in your environment to "
                            "override it to an external directory. To suppress "
                            "this message, put VFS=False in your Local.sc."
                        )
                    else:
                        SCons.Script.SetOption(
                            "remote_cache_vfs_build_root", buildRootPath
                        )

                        # Windows builds only ever use 64-bit Python, so we can
                        # hardcode win64_vc140.
                        SCons.Script.SetOption(
                            "remote_cache_vfs_plugin_path",
                            os.path.join(root, "win64_vc140", "VFSPlugin.dll"),
                        )

        if cachePushEnabled:
            SCons.Script.SetOption("remote_cache_push_enabled", True)

    # Special 'aliases' target dumps a sorted list of all registered aliases.
    # Should be easier to digest than the output of LOGGING=aliases=info.
    if "aliases" in SCons.Script.COMMAND_LINE_TARGETS:
        log.info("Registered aliases:")
        for k in sorted(SCons.Node.Alias.default_ans.keys()):
            log.info("   %s" % k)

        if len(COMMAND_LINE_TARGETS) == 1:
            # Exit if aliases is the only target
            Exit(0)

        # Give SCons a dummy target to consume the 'aliases' parameter named
        # on the command line.
        Alias("aliases", [])

    # Alright ... now we're building!
    vmware.phase.SetPhase("run")

    # See http://www.scons.org/doc/2.0.1/HTML/scons-man.html#lbAO and
    # look for "WARNING: Python only keeps one current directory".
    vmware.utils.RestrictChdir()


# Where it all starts
if vmware.IsMinimal():
    ConfigureMinimal()
else:
    main()
