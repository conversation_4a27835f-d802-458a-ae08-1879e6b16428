# Copyright (c) Omnissa, LLC. All rights reserved.
# This product is protected by copyright and intellectual property laws in the
# United States and other countries as well as by international treaties.
# -- Omnissa Restricted
import os, re
import datetime, time
import ipaddress
import lib.kits, lib.psRun, lib.auto, lib.client
import requests, json, urllib3

kits = lib.kits.kits
auto = lib.auto
cached_values = lib.kits.cached_values

class Broker():
    def __init__(self, brokerIP):
        self.brokerIP     = brokerIP
        self.brokerPort   = None
        self.cache        = {}
        self.brokerDomain = auto.horizonData['domain']
        self.brokerUser = auto.horizonData['user']
        self.brokerPassword = auto.horizonData['password']

        self.vcAddr       = auto.vcenterData['address']
        self.token        = ""
        self.cookie       = ""

        # Disable certificate warnning
        urllib3.disable_warnings()

    def Run(self, cmd, cacheable=False, timeout=120, WriteComment=False):
        """
        Run View PowerCLI on broker with psRun
        @Input:
          - cmd: command to run
          - cacheable: if the result should be cached. Once it is cached,
                       next time, cache will be returnned directly
        """
        print('\n\n')
        psRun = lib.psRun.psRunClient('%s:%s' % (self.brokerIP,self.brokerPort))
        if ('ViewPS_firstRunDone' in self.cache) == False:
            self.cache['ViewPS_firstRunDone'] = True
            psRun.ClearLog()
        if (auto.toolData['loadBrokerPSModule'].lower() == 'true' and \
            ('ViewPowerCLI_Loaded' in self.cache) == False):
            print('Load View PowerCLI module in psRun server')
            psRun.Run("C:\\automation\\installHorizon\\uninstall-snapin.ps1")
            psRun.Run("C:\\automation\\installHorizon\\add-snapin.ps1")
            self.cache['ViewPowerCLI_Loaded'] = True
        if WriteComment:
            kits.Comment('Broker Run: cmd "%s"' %cmd)
        else:
            print(f'Broker cmd:{cmd}')
        if cmd in self.cache:
            return self.cache[cmd]
        else:
            ret = psRun.Run(cmd, timeout)
            if cacheable:
                self.cache[cmd] = ret
            return ret


    def HV_Run(self, cmd, cacheable=False, timeout=120, errorStop=True, hvConnect=True):
        """
        Run cmd on broker with psRun
        @Input:
          - cmd: command to run
          - cacheable: if the result should be cached. Once it is cached,
                       next time, cache will be returnned directly
        """
        psRun = lib.psRun.psRunClient('%s:%s' % (self.brokerIP, self.brokerPort))
        print('\n')
        if not hvConnect:
            return psRun.Run(cmd, timeout)
        if errorStop:
            cmd += " -ErrorAction:Stop"

        def ReConnect():
            # kits.Comment('Broker Run: Refresh the Horizon Connection')
            loadHvCmd = "Import-Module Omnissa.VimAutomation.HorizonView;"
            disconnHvCmd = "Disconnect-HVServer -Confirm:$false -Force *;"
            connHvCmd = "$HVServer = Connect-HVServer -Server localhost -Domain %s -Force " % self.brokerDomain

            NewPasswd = lib.auto.horizonData.get('brokerPassword', None)
            if NewPasswd:
                connHvCmd += f"-User administrator -Password {NewPasswd}"
            else:
                connHvCmd += "-User administrator -Password ca`$hc0w"
            try:
                psRun.Run(loadHvCmd)
                psRun.Run(disconnHvCmd)
            except:
                print('exception when load and disconnect Hv')
            try:
                psRun.Run(connHvCmd)
            except:
                print('exception when build a Hv connection')

        if (auto.toolData['loadBrokerPSModule'].lower() == 'true' and \
            ('HV_Loaded' in self.cache) == False):
            print('load VMware PowerCLI Hv module and build a connection in psRun server')
            ReConnect()
            self.cache['HV_Loaded'] = True
        if cmd in self.cache:
            # kits.Comment('Broker Run: cache found for cmd "%s"' %cmd)
            return self.cache[cmd]
        else:
            # kits.Comment('Broker Run: cache not found for cmd "%s"' %cmd)
            while True:
                ret = psRun.Run(cmd, timeout)
                if 'authenticated' in ret:
                    # kits.Comment('Broker Run: authentication expired, re-connect...')
                    ReConnect()
                    continue
                if cacheable:
                    self.cache[cmd] = ret
                return ret


    def VI_Run(self, cmd, cacheable = False, timeout = 120):
        """
        Run cmd on broker with psRun
        @Input:
          - cmd: command to run
          - cacheable: if the result should be cached. Once it is cached,
                       next time, cache will be returnned directly
        """
        print('\n\n')
        psRun = lib.psRun.psRunClient('%s:%s' % (self.brokerIP, self.brokerPort))
        if (auto.toolData['loadBrokerPSModule'].lower() == 'true' and \
            ('VI_Loaded' in self.cache) == False):
            print('load VMware PowerCLI Hv module and build a connection in psRun server')
            loadViCmd = "Import-Module VMware.VimAutomation.Core;"
            ignoreCert = "Set-PowerCLIConfiguration -InvalidCertificateAction Ignore -Confirm:$false;"
            disconnViCmd = "Disconnect-VIServer -Server * -Force -Confirm:$false;"
            connViCmd = "$VIServer = Connect-VIServer -Server %s" % self.vcAddr
            connViCmd += " -user %s" %auto.vcenterData['admin']
            connViCmd += " -password %s" %auto.vcenterData['password']
            try:
                psRun.Run(loadViCmd)
                psRun.Run(ignoreCert)
                psRun.Run(disconnViCmd)
            except:
                print('exception when load or disconnect VI')
            try:
                psRun.Run(connViCmd)
            except:
                print('exception when build a VI connection')
            self.cache['VI_Loaded'] = True
        if cmd in self.cache:
            kits.Comment('Broker Run: cache found for cmd "%s"' %cmd)
            return self.cache[cmd]
        else:
            kits.Comment('Broker Run: cache not found for cmd "%s"' %cmd)
            ret = psRun.Run(cmd, timeout)
            if cacheable:
                self.cache[cmd] = ret
            return ret


    def UploadScript(self, localPath, remotePath):
        psRun = lib.psRun.psRunClient('%s:%s' % (self.brokerIP,self.brokerPort))
        psRun.UploadScript(localPath, remotePath)


    def HV_NewPool(self,  *args, **kwargs):
        """
        @Example:
        broker.HV_NewPool("-InstantClone",
                          PoolName = "hv-ic",
                          UserAssignment = "FLOATING",
                          ParentVM = "Tiddy11_1604",
                          SnapshotVM = "realready",
                          VmFolder ="/LinuxVDI/vm",
                          HostOrCluster = "cluster028",
                          ResourcePool = "cluster028",
                          Datastores = "local-s028",
                          NamingMethod = "PATTERN",
                          NamingPattern = '"hv-ic-{n}"',
                          MaximumCount = "1",
                          NetBiosName = "LXD")
        """
        cmd = 'New-HVPool'
        for i in args:
            cmd = '%s %s' % (cmd, i)
        for i in list(kwargs.keys()):
            cmd = '%s -%s "%s" ' % (cmd, i, kwargs[i])
        self.HV_Run(cmd, timeout = 300)
    
    def HV_NewFarm(self,  *args, **kwargs):
        """
        @Example:
        broker.HV_NewFarm("-Manual",
                          PoolName = "ub-2404-ag-rdsh",
                          DisplayName = "ub-2404-ag-rdsh",
                          UserAssignment = "FLOATING",
                          DisplayProtocol = 'BLAST'
                          EnableCollaboration = True
        """
        cmd = 'New-HVFarm'
        for i in args:
            cmd = '%s %s' % (cmd, i)
        for i in list(kwargs.keys()):
            if(i == "EnableCollaboration"):
                val = str(kwargs[i]).lower()
                cmd = '%s -%s $%s'%(cmd,i,val)
            else:
                cmd = '%s -%s "%s" ' % (cmd, i, kwargs[i])
        self.HV_Run(cmd, timeout = 300)

    def HV_GetPool(self, pool):
        """
        Get the pool settings with HV module

        With this function, you can get the pool setting keys name, value,
        and structure, for example:

        DesktopSettings.LogoffSettings.PowerPolicy: TAKE_NO_POWER_ACTION
        DesktopSettings.DisplayProtocolSettings.DefaultDisplayProtocol: PCOIP
        """
        cmd = 'Get-HVPool %s' %pool
        return self.HV_Run(cmd)["cmd"]


    def HV_SetPool(self, setting, pool=auto.horizonData['pool'], viaSpec=True):
        """
        Set the pool settings with HV module
        @setting: pool setting key and value in json format, and multiple keys
        are supported.

        Please note that, the 1st letter is lower-case in key, such as:
        "{'desktopSettings.displayProtocolSettings.defaultDisplayProtocol': 'PCOIP'}"
        "{'desktopSettings.displayProtocolSettings.enableHTMLAccess': true}"
        "{'desktopSettings.logoffSettings.automaticLogoffPolicy' : 'AFTER', 'desktopSettings.logoffSettings.automaticLogoffMinutes' : 60}"

        @Examples:
        HV_SetPool("{'desktopSettings.displayProtocolSettings.defaultDisplayProtocol': 'BLAST'}" , 'my-win10')
        """
        if viaSpec:
            cmd = 'Set-Content C:\\automation\\psRun\\temp\\pool-spec.json "%s" ' %setting
            self.HV_Run(cmd)
            cmd = 'Set-HVPool -PoolName %s -Spec C:\\automation\\psRun\\temp\\pool-spec.json' %pool
            self.HV_Run(cmd)
        else:
            cmd = 'Set-HVPool -PoolName {} {}'.format(pool, setting)
            self.HV_Run(cmd)


    def HV_GetPoolType(self, pool = auto.horizonData['pool']):
        """
        Get the pool type, RDS, AUTOMATED, MANUAL, APP, APP-VDI
        """
        cmd = '''
        $pool = "%s";
        $pool_type = $null;
        $ViewAPI = $global:DefaultHVServers[0].ExtensionData;
        $query_service = New-Object "Vmware.Hv.QueryServiceService";
        $query = New-Object "Vmware.Hv.QueryDefinition";
        $query.queryEntityType = 'ApplicationInfo';
        $apps = $query_service.QueryService_Query($ViewAPI,$query);
        Foreach($app in $apps.Results)
        {
           if($app.Data.Name -eq $pool)
           {
              if ($app.ExecutionData.Farm -ne $null)
              {
                 $pool_type = "APP";
              }
              else
              {
              $pool_type = "APP-VDI";
              }
           }
        }
        if ($pool_type -eq $null)
        {
            $query.queryEntityType = 'DesktopSummaryView';
            $desktops = $query_service.QueryService_Query($ViewAPI,$query);
            Foreach($desktop in $desktops.Results)
            {
               if($desktop.DesktopSummaryData.Name -eq $pool)
               {
                    $pool_type = $desktop.DesktopSummaryData.Type;
               }
            }
        }
        $pool_type;
        ''' %pool
        return self.HV_Run("Get-HVPool -PoolName '%s' | ForEach-object {$_.Type}" % pool, errorStop=False)["cmd"]


    def HV_RemovePool(self,  *args, **kwargs):
        """
        @Examples:
        HV_RemovePool("-DeleteFromDisk", PoolName = "hv-ic")
        """
        cmd = 'Remove-HVPool'
        for i in args:
            cmd = '%s %s' % (cmd, i)
        for i in list(kwargs.keys()):
            cmd = '%s -%s %s ' % (cmd, i, kwargs[i])
        self.HV_Run(cmd)

        if 'PoolName' in kwargs:
            pool = kwargs['PoolName']
        else:
            pool = auto.horizonData['pool']
        #this is needed because there are instances
        #where the pool does not get deleted in 30 seconds
        #in this case the broker deleted the vm itself.
        for t in range(1, 12):
            ret = self.HV_GetPool(pool=pool)
            if ret == {}:
                # If null is received on Get-HVPool
                #then it means pool is deleted
                print('Pool does not exist/Deleted')
                kits.Wait(10)
                return
            else:
                print('Wait Until Pool Deleted:%d' %t)
                kits.Wait(30)

    def HV_RemoveFarm(self,  *args, **kwargs):
        """
        @Examples:
        HV_RemoveFarm("-DeleteFromDisk", FarmName = "hv-ic")
        """
        cmd = 'Remove-HVFarm'
        for i in args:
            cmd = '%s %s' % (cmd, i)
        for i in list(kwargs.keys()):
            cmd = '%s -%s %s ' % (cmd, i, kwargs[i])
        self.HV_Run(cmd)

        if 'FarmName' in kwargs:
            farm = kwargs['FarmName']
        else:
            farm = auto.horizonData['farm']
        for t in range(1, 12):
            ret = self.HV_GetFarm(farm=farm)
            if ret == {}:
                # try to be safe
                print("Farm does not exist/deleted")
                kits.Wait(10)
                return
            else:
                print('Wait Until Farm is Deleted:%d' %t)
                kits.Wait(30)

    def HV_GetFarm(self, farm):
        """
        Get the farm settings with HV module
        """
        cmd = 'Get-HVFarm -FarmName %s' %farm
        return self.HV_Run(cmd)["cmd"]

    def HV_RemoveRDSHost(self, hostname):
        cmd = """Invoke-Command -ScriptBlock {
        $rdsId = Get-HVQueryResult RDSServerInfo | Where-Object {$_.Base.Name -eq "%s"} | ForEach-Object {$_.id}
        $global:DefaultHVServers[0].ExtensionData.RDSServer.RDSServer_Delete($rdsId)
        }"""%hostname
        return self.HV_Run(cmd, errorStop=False)
        


    def HV_NewEntitlement(self,
                          user = auto.horizonData['user'],
                          domain = auto.horizonData['domain'],
                          pool = auto.horizonData['pool']):
        cmd = "New-HVEntitlement -User '%s\\%s'" %(domain, user)
        cmd += " -ResourceName '%s' -Confirm:$false" %pool
        self.HV_Run(cmd)


    def HV_GetDesktopState(self, pool = auto.horizonData['pool']):
        
        #the below command is throwing exceptions on Connection broker sometimes
        # for those exceptions just try the same command few more times with 1 sec break
        cmd = '''((Get-HVQueryResult MachineNamesView | Where-Object {$_.NamesData.DesktopName -eq "%s"}).Base.BasicState)'''%pool

        for i in range(3):
            ret = self.HV_Run(cmd, errorStop=False)
            if isinstance(ret, dict):
                return ret['cmd']
            print("Exception in Broker expected dict but returned "+str(type(ret))+",Trying again")
            time.sleep(1)


    def HV_GetDesktopSummary(self, pool = auto.horizonData['pool']):
        cmd = "$pExists = '%s' -in (Get-HVMachineSummary | ForEach-object { $_.NamesData.DesktopName } );" % pool
        cmd += "if ($pExists) { Get-HVMachineSummary -PoolName '%s' -ErrorAction Stop }" % pool
        times = 6
        while times > 0:
            ret = self.HV_Run(cmd, errorStop=False)
            print(f'desktop summary:\n{ret}')
            print(f'ret type:{type(ret)}\n')
            if isinstance(ret, dict):
                return ret
            print('Failed to get desktop summary: %s' %ret)
            time.sleep(10)
            times -= 1
            continue


    def HV_WaitRDSDesktopState(self, farm = auto.horizonData['farm'],
                            expectation = 'CONNECTED', times = 240):
        state = ''
        while times > 0:
            state = self.HV_GetRDSDesktopState(farm)
            if state == expectation:
                return True
            elif 'ERROR' in state:
                # don't return immediately when hit error
                kits.Warning('Wait State, actual: %s, expectd: %s' %(state, expectation))
            else:
                if times % 5 == 0:
                    kits.Comment('Wait State, actual: %s, expectd: %s' %(state, expectation))
            times = times -1
        return False

    def HV_WaitDesktopState(self, pool = auto.horizonData['pool'],
                            expectation = 'CONNECTED', times = 240):
        state = ''
        while times > 0:
            state = self.HV_GetDesktopState(pool)
            if state == expectation:
                return True
            elif 'ERROR' in state:
                # don't return immediately when hit error
                kits.Warning('Wait State, actual: %s, expectd: %s' %(state, expectation))
            else:
                if times % 5 == 0:
                    kits.Comment('Wait State, actual: %s, expectd: %s' %(state, expectation))
            print("Waiting for Agent to be Available on Broker")
            time.sleep(10)
            times = times -1
        return False

    def HV_GetRDSDesktopState(self, farm = auto.horizonData['farm']):
        farm_exists = self.HV_Run("'%s' -in (Get-HVQueryResult RDSServerSummaryView | ForEach-Object {$_.SummaryData.FarmName})" % farm, errorStop=False)["cmd"]
        if farm_exists:
            return self.HV_Run("(Get-HVQueryResult RDSServerSummaryView | Where-Object {$_.SummaryData.FarmName -eq '%s'} | ForEach-Object {$_.RuntimeData.status})" % farm, errorStop=False)["cmd"]
        return None

    def HV_GetCS(self, times=10):
        """
        Get the Connection Server information with HV module

        With this function, you can get the pool setting keys name, value,
        and structure, for example:
        General.ExternalURL" :  "https://txsrv4.crt.fvt:443"
        General.BypassTunnel : false
        General.BypassPCoIPGateway : false
        General.BypassAppBlastGateway : false

        Authentication.SmartCardSupport":  "OPTIONAL"
        Authentication.LogoffWhenRemoveSmartCard":  false
        """
        while times > 0:
            #cmd = "$brokerID = $Global:DefaultHVServers.ExtensionData.ConnectionServer.ConnectionServer_List().id ; "
            #cmd += "$Global:DefaultHVServers.ExtensionData.ConnectionServer.ConnectionServer_Get($brokerID)"
            cmd = "$Global:DefaultHVServers.ExtensionData.ConnectionServer.ConnectionServer_List().id | ForEach-Object {$Global:DefaultHVServers.ExtensionData.ConnectionServer.ConnectionServer_Get($_)}"
            ret = self.HV_Run(cmd, errorStop=False)["cmd"]
            if isinstance(ret, dict):
                return ret
            print('Failed to get broker info: %s' %ret)
            kits.Wait(10, 'wait for getting broker info')
            times -= 1
            continue


    def HV_SetCS(self, key, value):
        """
        Set the Connection Server settings with HV module

        @key: Connection Server setting key
        @value: Connection Server setting  value

        Please note that, the 1st letter is lower-case in key, such as:
        'general.externalPCoIPURL'
        'general.bypassPCoIPGateway'

        @Example:
        HV_SetCS('general.externalPCoIPURL', '************:41722')
        HV_SetCS('general.bypassPCoIPGateway', '$true')

        """
        cmd = "Invoke-Command -ScriptBlock {($update = new-object -TypeName Omnissa.Horizon.MapEntry),"
        cmd += '($update.key = "{}"),'.format(key)
        if value.lower() in ('$false', '$true'):
            cmd += '($update.value = {} ),'.format(value)
        else:
            cmd += '($update.value = {} ),'.format(value) 
        cmd += "($Global:DefaultHVServers.ExtensionData.ConnectionServer.ConnectionServer_List().id | ForEach-Object {$Global:DefaultHVServers.ExtensionData.ConnectionServer.ConnectionServer_Update($_, $update)} )}"
        
        self.HV_Run(cmd, errorStop=False)
        


    def HV_GetGlobal(self):
        """
        Get global settings with HV module
        With this function, you can get the global setting keys name, value,
        and structure, for example:

        GeneralData.ClientMaxSessionTimePolicy: NEVER
        GeneralData.PreLoginMessage: null
        """
        cmd = 'Get-HVGlobalSettings'
        self.HV_Run(cmd)


    def HV_SetGlobal(self, setting):
        """
        Set global settings with HV module

        @setting: global setting key and value in json format, and multiple keys
        are supported.

        Please note that, the 1st letter is lower-case in key, such as:
        "{'generalData.clientMaxSessionTimePolicy': 'NEVER'}"
        "{'generalData.clientIdleSessionTimeoutPolicy': 'NEVER'}"
        "{'generalData.clientIdleSessionTimeoutPolicy': 'TIMEOUT_AFTER', 'generalData.clientIdleSessionTimeoutMinutes':  15}"
        "{'generalData.clientMaxSessionTimePolicy': 'TIMEOUT_AFTER' , 'generalData.clientMaxSessionTimeMinutes': 15}"

        @Examples:
        HV_SetGlobal("{'generalData.clientMaxSessionTimePolicy': 'TIMEOUT_AFTER' , 'generalData.clientMaxSessionTimeMinutes': 15}")
        HV_SetGlobal("{'generalData.clientIdleSessionTimeoutPolicy': 'TIMEOUT_AFTER', 'generalData.clientIdleSessionTimeoutMinutes':  15}")
        """
        cmd = 'Set-Content C:\\automation\\psRun\\temp\\global-spec.json "%s"' %setting
        self.HV_Run(cmd)
        cmd = 'Set-HVGlobalSettings -Spec C:\\automation\\psRun\\temp\\global-spec.json -Confirm:$false'
        self.HV_Run(cmd)


    def HV_SetBSG(self, enable=False):
        if enable:
            self.HV_SetCS('general.bypassAppBlastGateway', '$true')
        else:
            self.HV_SetCS('general.bypassAppBlastGateway', '$false')


    def HV_EnableHtml(self, pool):
        if self.HV_GetPoolType(pool) == 'RDS':
            farmName = broker.GetRdshFarmName(pool)
            setting = "{'data.displayProtocolSettings.enableHTMLAccess': true}"
            broker.HV_SetFarm(setting=setting, farmName = farmName)
        else:
            setting = ' -enableHTMLAccess $true '
            self.HV_SetPool(pool=pool, setting=setting, viaSpec=False)


    def HV_UpdateLDAPEntryAttribute(self, attributeName, value, pool=auto.horizonData['pool']):
        entryPath = "CN=%s,OU=Server Groups,DC=vdi,DC=horizon,DC=internal" % pool
        cmd = "$obj = [adsi]'LDAP://localhost:389/%s';" %entryPath
        cmd += "$obj.put('%s',%s);" %(attributeName, value)
        cmd += "$obj.setInfo();"
        kits.Comment('Broker LDAP cmd: "%s"' % cmd)
        self.HV_Run(cmd, hvConnect=False)


    def HV_EnableCollab(self, enable=True, pool=auto.horizonData['pool']):
        attributeName = "pae-SessionCollaborationEnabled"
        value = ('0', '1')[enable]
        self.HV_UpdateLDAPEntryAttribute(attributeName, value, pool=pool)


    def HV_GetBuildInfo(self, pool = auto.horizonData['pool']):
        brokerBuild = 'Unknown'
        agentBuild = 'Unknown'
        cs= self.HV_GetCS()
        csGeneral = cs.get('General', None)
        if csGeneral:
            brokerBuild = csGeneral.get('Version', 'UnKnown')
        am = self.HV_GetAgentInfoOnBroker(pool=pool)
        if isinstance(am, dict):
            agentBuild = am.get('AgentBuildNumber', 'UnKnown')
        return (brokerBuild, agentBuild)


    def HV_GetSmartCardSupport(self):
        cs = self.HV_GetCS()
        csAuthentication = cs.get('Authentication', None)
        if csAuthentication:
            scSupport = csAuthentication.get('SmartCardSupport', 'UnKnown')
        return scSupport


    def HV_ForceKillBrokerService(self):
        """
        Kill all processes of broker, including wsnm.exe, ws_TunnelService.exe, ws_ConnectionServer.exe,
        ws_TomcatService.exe, ws_MessageBusService.exe, SecurityGateway.exe(PCOIP Security Gateway)
        :return:
        """
        processes = 'wsnm, ws_*, SecurityGateway'
        cmd = 'stop-process -force -processname %s' %processes
        self.HV_Run(cmd)


    def HV_RestartCSService(self, brokerservice, MaxSeconds=360):
        cmd = 'Restart-service -Name %s -Force' %brokerservice
        self.HV_Run(cmd)

        waited= 0
        status = False
        while (status == False and MaxSeconds >0):
            status = self.HV_CheckHPPTS()
            if status:
                kits.Comment('Connection Server service is up.')
                if 'HV_Loaded' in self.cache:
                    del self.cache['HV_Loaded']
                return
            elif status == False and waited >= 180:
                kits.Comment('Fail to restart Connection Server service and kill all processes.')
                self.HV_ForceKillBrokerService()
            kits.Wait(30)
            MaxSeconds -= 30
            waited += 30


    def HV_CheckHPPTS(self, port=443):
        sPort = port
        brokerURL = 'https://%s:%s' %(lib.auto.horizonData['broker'], sPort) # disable proxy is required for FQDN
        import urllib.request as request
        req = request.Request(brokerURL)
        try:
            request.urlopen(req)
        except request.HTTPError as responseError:
            print('HTTP Error code: %s' %responseError)
            e = responseError
        except request.URLError as responseError:
            print('URL Error: %s' %responseError)
            e = responseError
        if 'certificate verify failed' in str(e):
            status = True
        else:
            status = False
        return status


    def HV_GetAgentInfoOnBroker(self, pool=auto.horizonData['pool']):
        '''
        Example output of VDI:
            {
                "Name":  "Tiddy_Win1809",
                "DnsName":  "tx-1809.lxd.vdi",
                "User":  null,
                "AccessGroup":  {
                                    "Id":  "AccessGroup/YjQxY2JkZTAtZjNhOS00ZTc2LWExNzMtZGU2ZDhlNDdhMzc5/Um9vdA"
                                },
                "Desktop":  {
                                "Id":  "Desktop/YjQxY2JkZTAtZjNhOS00ZTc2LWExNzMtZGU2ZDhlNDdhMzc5/dHgtd2luMTA"
                            },
                "Session":  {
                                "Id":  "Session/YjQxY2JkZTAtZjNhOS00ZTc2LWExNzMtZGU2ZDhlNDdhMzc5/YjljNzhiZmYtMzU5NC00MmZkLThjZDItZGFhMzAyN2U2ZmUy/TFhEXHQwMDEoY249cy0xLTUtMjEtMjI3MTQ1NTM0MS0yOTI1MDE2MDEzLTIwODA2NjU4MjMtMzE0Mixjbj1mb3JlaWduc2VjdXJpdHlwcmluY2lwYWxzLGRjPXZkaSxkYz12bXdhcmUsZGM9aW50KS8zQGNuPWRkOWU2OWExLWY1YjEtNDRiNy05OTI3LTI4NGQxZDI4MDIxMyxvdT1zZXJ2ZXJzLGRjPXZkaSxkYz12bXdhcmUsZGM9aW50LmNuPXR4LXdpbjEwLG91PXNlcnZlciBncm91cHMsZGM9dmRpLGRjPXZtd2FyZSxkYz1pbnQ6QkxBU1Q6MDpERVNLVE9Q"
                            },
                "BasicState":  "CONNECTED",
                "Type":  "MANAGED_VIRTUAL_MACHINE",
                "OperatingSystem":  "Windows 10",
                "AgentVersion":  "7.9.0",
                "AgentBuildNumber":  "12763625",
                "RemoteExperienceAgentVersion":  "",
                "RemoteExperienceAgentBuildNumber":  "12763625"
            }

        Example output of RDSH or App
            {
                "DnsName":  "tx-2016.lxd.vdi",
                "OperatingSystem":  "Windows Server 2016",
                "AgentVersion":  "7.8.0",
                "AgentBuildNumber":  "12436681",
                "RemoteExperienceAgentVersion":  "",
                "RemoteExperienceAgentBuildNumber":  "12436681"
            }

        '''
        poolType = None
        cmd = None
        while not poolType and not cmd:
            poolType = self.HV_GetPoolType(pool)
            poolName = pool
            if poolType == "APP-VDI":
                poolName = self.HV_Get_VDI_of_App(pool)
            cmd_VDI = '''((Get-HVQueryResult MachineNamesView | Where-Object {$_.NamesData.DesktopName -eq "%s"} ).Base)
            ''' %poolName
            cmd_RDS = '''
            Invoke-Command -ScriptBlock { $pool = Get-HVPool %s;
            (Get-HVQueryResult RDSServerSummaryView | Where-Object {$_.Base.Desktop.Id -eq $pool.Id.Id}).AgentData
             }
            ''' %poolName
            cmd_APP = '''
            $pool = "%s";
            $ViewAPI = $global:DefaultHVServers[0].ExtensionData;
            $query_service = New-Object "Vmware.Hv.QueryServiceService";
            $query = New-Object "Vmware.Hv.QueryDefinition";
            $query.queryEntityType = 'ApplicationInfo';
            $apps = $query_service.QueryService_Query($ViewAPI,$query);
            $appfarmID = ($apps.Results|Where-Object{$_.Data.Name -eq $pool}).ExecutionData.Farm.ID;
            $query.queryEntityType = 'RDSServerSummaryView';
            $rdss = $query_service.QueryService_Query($ViewAPI,$query);
            ($rdss.Results|Where-Object{$_.Base.Farm.ID -eq $appfarmID}).AgentData;
            ''' %poolName

            print(f'poolType: {poolType}')
            if poolType in ('MANUAL', 'AUTOMATED', 'APP-VDI'):
                cmd = cmd_VDI
            if poolType == 'RDS':
                cmd = cmd_RDS
            if poolType == 'APP':
                cmd = cmd_APP
            key = '%s_agent_info' %pool
            if not cmd:
                print(f'cmd : {cmd}')
                print('Failed to get poolType')
                time.sleep(2)
                continue
            if not key in self.cache:
               self.cache[key] = self.HV_Run(cmd, errorStop=False)
            return self.cache[key]


    def HV_GetAgentFQDN(self, pool=auto.horizonData['pool']):
        agentInfo = self.HV_GetAgentInfoOnBroker(pool=pool)
        return agentInfo['cmd']['DnsName']


    def HV_GetVdiVMName(self, pool):
        # return self.HV_Run("Get-HVPool -PoolName '%s' | ForEach-object {$_.Base.DisplayName}" % pool, errorStop=False)["cmd"]
        poolType = self.HV_GetPoolType(pool)
        if poolType in ('MANUAL', 'AUTOMATED', "APP-VDI"):
           agentInfo = self.HV_GetAgentInfoOnBroker(pool=pool)
           return agentInfo["cmd"]['Name']
        else:
           return False


    def HV_GetAgentOS(self):
        agentInfo = self.HV_GetAgentInfoOnBroker()
        return agentInfo['OperatingSystem']


    def Get_DomainFullName(self):
        key = 'domainFullName'
        if key in self.cache:
            return self.cache[key]
        else:
            cmd = '(Get-WmiObject Win32_ComputerSystem).Domain'
            self.cache[key] = self.Run(cmd, WriteComment = False)
            return self.cache[key]


    def HV_QuerySession(self):
        """
        :return all the info per broker
        """
        cmd = '''
        Invoke-Command -ScriptBlock {
        $sessions = Get-HVQueryResult SessionLocalSummaryView | ForEach-Object {$_}
        $sessionList = @();
        Foreach($session in $sessions)
        {
            $sessionObj =  New-Object System.Object;
            $sessionObj | Add-Member -type NoteProperty -name Username -Value $session.NamesData.UserName;
            $sessionObj | Add-Member -type NoteProperty -name FarmName -Value $session.NamesData.FarmName;
            $sessionObj | Add-Member -type NoteProperty -name DesktopName -Value $session.NamesData.DesktopName;
            $sessionObj | Add-Member -type NoteProperty -name RdsServerName -Value $session.NamesData.MachineOrRDSServerName;
            $sessionObj | Add-Member -type NoteProperty -name DNS -Value $session.NamesData.MachineOrRDSServerDNS;
            $sessionObj | Add-Member -type NoteProperty -name ClientAddress -Value $session.NamesData.ClientAddress;
            $sessionObj | Add-Member -type NoteProperty -name DesktopSource -Value $session.NamesData.DesktopSource;
            $sessionObj | Add-Member -type NoteProperty -name SessionType -Value $session.SessionData.SessionType;
            $sessionObj | Add-Member -type NoteProperty -name protocol -Value $session.SessionData.SessionProtocol;
            $sessionObj | Add-Member -type NoteProperty -name state -Value $session.SessionData.SessionState;
            $sessionObj | Add-Member -type NoteProperty -name startTime -Value $session.SessionData.StartTime;
            $sessionObj | Add-Member -type NoteProperty -name DisconnectTime -Value $session.SessionData.DisconnectTime;
            $sessionList += $sessionObj;
        }
        $sessionList
        }
        '''
        print('HV_QuerySession on %s' % self.brokerIP)
        return self.HV_Run(cmd, errorStop=False)["cmd"]

    def HV_QueryRdsServer(self):
        """
        :return all the rds server info
        """
        cmd = '''
        $ViewAPI = $global:DefaultHVServers[0].ExtensionData;
        $query_service = New-Object "Vmware.Hv.QueryServiceService";
        $query = New-Object "Vmware.Hv.QueryDefinition";
        $query.queryEntityType = 'RDSServerInfo';
        $rds = $query_service.QueryService_Query($ViewAPI,$query);
        $rdsList = @();
        Foreach($rds in $rds.Results)
        {
            $rdsObj =  New-Object System.Object;
            $rdsObj | Add-Member -type NoteProperty -name Name -Value $rds.Base.Name;
            $rdsObj | Add-Member -type NoteProperty -name DnsName -Value $rds.AgentData.DnsName;
            $rdsObj | Add-Member -type NoteProperty -name FarmID -Value $rds.Base.Farm.Id;
            $rdsObj | Add-Member -type NoteProperty -name Desktop -Value $rds.Base.Desktop;
            $rdsObj | Add-Member -type NoteProperty -name Description -Value $rds.Base.Description;
            $rdsObj | Add-Member -type NoteProperty -name AgentVersion -Value $rds.AgentData.AgentVersion;
            $rdsObj | Add-Member -type NoteProperty -name AgentBuildNumer -Value $rds.AgentData.AgentBuildNumber;
            $rdsObj | Add-Member -type NoteProperty -name Status -Value $rds.RuntimeData.Status;
            $rdsList += $rdsObj;
        }
        $rdsList
        '''
        kits.Comment('HV_QueryRdsServer on %s' % self.brokerIP)
        return self.HV_Run(cmd, errorStop=False)


    def VI_ConvertTemplateToVm(self, vmName):
        cmd = 'Set-Template %s -ToVM' %vmName
        return self.VI_Run(cmd)


    def VI_ConvertVmToTemplate(self, vmName):
        cmd = 'Set-VM %s -ToTemplate -Confirm:$false' %vmName
        return self.VI_Run(cmd)


    def VI_GetVmFolderPath(self, vmName):
        localScript = os.path.join(os.getcwd(), 'ps', 'get-vm-FolderPath.ps1')
        remoteScript = 'C:\\Automation\\psRun\\temp\\get-vm-FolderPath.ps1'
        self.UploadScript(localScript, remoteScript)
        cmd = '%s %s' %(remoteScript, vmName)
        return self.VI_Run(cmd)


    def VI_GetVmClusterPath(self, vmName):

        localScript = os.path.join(os.getcwd(), 'ps', 'get-vm-ClusterPath.ps1')
        remoteScript = 'C:\\Automation\\psRun\\temp\\get-vm-ClusterPath.ps1'
        self.UploadScript(localScript, remoteScript)
        cmd = '%s %s' %(remoteScript, vmName)
        return self.VI_Run(cmd)


    def VI_GetVmResourcePoolPath(self, vmName):

        localScript = os.path.join(os.getcwd(), 'ps', 'get-vm-ResourcePoolPath.ps1')
        remoteScript = 'C:\\Automation\\psRun\\temp\\get-vm-ResourcePoolPath.ps1'
        self.UploadScript(localScript, remoteScript)
        cmd = '%s %s' %(remoteScript, vmName)
        return self.VI_Run(cmd)


    def VI_GetVmDatastore(self, vmName):
        cmd = 'Get-VM %s | Get-Datastore | Select Name, Type' %vmName
        ret = self.VI_Run(cmd)
        if type(ret) == type([]):
            for datastore in ret:
                if datastore['Type'] == 'VMFS':
                    return datastore['Name']
        elif type(ret) == type({}):
            return ret['Name']


    def VI_NewSnapshot(self, vmName, snapshot):
        cmd = 'New-Snapshot %s -Name %s -Confirm:$false' %(vmName, snapshot)
        return self.VI_Run(cmd)


    def VI_ReverToSnapshot(self, vmName, snapshot):
        cmd = 'Set-VM %s -Snapshot %s -Confirm:$false | Format-List' %(vmName, snapshot)
        return self.VI_Run(cmd)


    def VI_RemoveSnapshot(self, vmName, snapshot):
        cmd = 'Get-Snapshot %s -Name %s' %(vmName, snapshot)
        cmd += ' | Remove-Snapshot -Confirm:$false'
        return self.VI_Run(cmd)


    def VI_PowerOnVM(self, vmName, times=10):
        cmd = 'Start-VM %s -Confirm:$false | Select Name, PowerState' %vmName
        self.VI_Run(cmd)
        kits.Wait(5)
        for t in range(1, times +1):
            print('VI_WaitUntillPoweredOn: %d' %t)
            status = self.VI_GetVMPowerState(vmName)
            if status == 'PoweredOn':
                return True
            else:
                self.VI_Run(cmd)
                kits.Wait(5)
        return False


    def VI_PowerOffVM(self, vmName):
        cmd = 'Stop-VM -VM %s -Confirm:$false | Select Name, PowerState' %vmName
        print([self.VI_Run(cmd)])


    def VI_ShutdownVM(self, vmName):
        cmd = 'Get-VM %s | Shutdown-VMGuest -Confirm:$false' %vmName
        cmd += ' | Select VmName, State'
        return self.VI_Run(cmd)


    def VI_RebootVM(self, vmName):
        cmd = 'Get-VM %s | Restart-VMGuest -Confirm:$false | Format-List' %vmName
        return self.VI_Run(cmd)


    def VI_GetVMPowerState(self, vmName):
        """
        @Output: PoweredOn, PoweredOff...
        """
        #cmd = 'Get-VM "%s" | Select-Object PowerState | Format-Table -HideTableHeaders' %vmName      #'$vm = Get-VM %s; [string]$vm.PowerState;' %vmName
        cmd = 'Get-VM "%s" | ForEach-Object {$_.PowerState.ToString()}' % vmName
        return self.VI_Run(cmd)["cmd"]


    def VI_GetVMToolsRunningStatus(self, vmName):
        """
        @Output: guestToolsRunning, guestToolsNotRunning, ...
        """
        #cmd = '$vm = Get-VM %s; $vm.ExtensionData.Guest.ToolsRunningStatus;' %vmName
        cmd = 'Get-VM "%s" | ForEach-Object {$_.ExtensionData.Guest.ToolsRunningStatus.ToString()};' %vmName
        return self.VI_Run(cmd)["cmd"]


    def VI_WaitUntillToolsRunning(self, vmName, times = 24):
        for t in range(1, times +1):
            print('VI_WaitUntillToolsRunning: %d' %t)
            status = self.VI_GetVMToolsRunningStatus(vmName)
            if status == 'guestToolsRunning':
                return True
            else:
                kits.Wait(20)
        return False


    def VI_WaitUntillPoweredOff(self, vmName, times = 24):
        for t in range(1, times +1):
            print('VI_WaitUntillPoweredOff: %d' %t)
            status = self.VI_GetVMPowerState(vmName)
            if status == 'PoweredOff':
                return
            else:
                kits.Wait(20)
        self.VI_PowerOffVM(vmName)

    def VI_PoolingGuestIP(self, pool, times=60):
        ip = ''
        while times > 0:
            vmName = self.GetAgentVMName(pool)
            if not vmName:
                kits.Wait(10)
                continue
            if vmName == 'None':
                kits.Wait(10)
                continue
            ok, newIp = self.VI_GetGuestIP(vmName, cache=False)
            if ok:
                if not ip:
                    ip = newIp
                    kits.Comment(f"IP of {vmName} : {ip}")
                else:
                    if ip != newIp:
                        kits.Comment(f"IP of {vmName} changed to: {newIp}")
                        break
            else:
                kits.Comment(f"Failed to Get IP for {vmName}")
            kits.Wait(10)
            times -= 1

    def GetGlobalIPv6Address(self, ips):
        if isinstance(ips, str):
            ips = ips.split()
        ipv6t = list(filter(lambda x: ':' in x, ips))
        print(f'ipv6 address: {ipv6t}')
        ipv6s = map(ipaddress.IPv6Address, ipv6t)
        ipv6g = list(filter(lambda x: x.is_global, ipv6s))
        print(f'global ipv6 address: {ipv6g}')
        return(ipv6g)[0]

    def VI_GetGuestIP(self, vmName, times=24, cache=True):

        if 'agentIP' in cached_values:
            return (True, cached_values['agentIP'])

        cmd = '(Get-VM %s).Guest.IPAddress | ? {$_ -match \"\d{1,3}\.\d{1,3}\.\"} | ForEach-Object {$_.ToString()}' %vmName
        # cmd = 'Get-VM "%s" | ForEach-Object {$_.Guest.IPAddress} | ? {$_ -match "\d{1,3}\.\d{1,3}\."} | ForEach-Object {$_.ToString()}' % vmName
        if lib.auto.agentData['ipv6'] == 'true':
            cmd = '(Get-VM %s).Guest.IPAddress' %vmName
        for _ in range(1, times +1):
            ret = self.VI_Run(cmd)
            if ret == 'Done':
                print('Failed to get guest IP, wait 10 seconds to retry')
                kits.Wait(10)
                continue
            else:
                kits.Comment(f'Agent IP Address:\n {ret}')
                if lib.auto.agentData['ipv6'] == 'true':
                    topip = str(self.GetGlobalIPv6Address(ret))
                else:
                    topip = ret["cmd"]
                if cache:
                    cached_values['agentIP'] = topip
                return (True, topip)
        else:
            return(False, '127.0.0.1')


    def VI_GetVMStat(self, vmName, lastMinutes = 1, stat = 'cpu.usage.average', intervalSecs = 5):
        """
        Retrieves the VM statistical information
        :param lastMinutes: last N minutes' statistical
        :param stat: cpu.usage.average, mem.usage.average, ...
        :return:
        """
        cmd = '''
        $endTime = Get-Date;
        $startTime = $endTime.AddMinutes(-%d);
        Get-VM %s | Get-Stat -Stat %s -Start $startTime -Finish $endTime -IntervalSecs %d | select MetricId, Timestamp, Value| ConvertTo-Csv | Out-String;
        ''' %(lastMinutes, vmName, stat, intervalSecs)
        kits.Comment('Get VM statistical information')
        return self.VI_Run(cmd, WriteComment = False)


    def VI_GetVMnameViaFQDN(self, fqdn):
        cmd = '''Invoke-Command -ScriptBlock {
            $vm = Get-View -ViewType VirtualMachine -Filter @{"Guest.HostName" = "%s"};
            if ($vm -is [array]) { $vm[0].Name;} else { $vm.Name;}
        }''' %fqdn
        key = 'vmName_of_%s' %fqdn
        if not key in self.cache:
            self.cache[key] = self.VI_Run(cmd)["cmd"]
        return self.cache[key]


    def VI_GetVMGuestInfo(self, vmName):
        cmd = '''
        $vm = Get-VM -Name %s;
        $vm.ExtensionData.guest;
        ''' %vmName
        key = '%s_guestInfo' %vmName
        if not key in self.cache:
            self.cache[key] = self.VI_Run(cmd)
        return self.cache[key]


    def AddFullClonePool(self, **kwargs):
        pass
        """
        @Example:

        """
        cmd = 'Add-AutomaticPool '
        for k in list(kwargs.keys()):
            if k == 'customizationSpecName' and kwargs[k] == None:
                continue
            else:
                cmd = '%s -%s %s ' % (cmd, k, kwargs[k])
        self.Run(cmd, timeout = 300)


    def GetVcID(self):
        """
        Get the vCenter ID in View PowerCLI
        """
        cmd = '(Get-ViewVC -serverName %s).vc_id' %auto.vcenterData['address']
        return self.Run(cmd)


    def GetDesktopVM(self, pool = auto.horizonData['pool']):
        """
        Get the Desktop pool VM information
        @Output, dictionary of the VM information,
        {
            "vm":  "300",
            "ps_object_type":  "vc_vm",
            "id":  "VirtualMachine-vm-12698",
            "vc_id":  "ac71ff26-eb3f-4e8e-972e-1c6848ce6770",
            "Name":  "Tiddy_RHEL69",
            "UnescapedName":  "Tiddy_RHEL69",
            "Path":  "/LinuxVDI/vm/Tiddy_RHEL69",
            "GuestFullName":  "Red Hat Enterprise Linux 6 (64-bit)",
            "GuestID":  "rhel6_64Guest",
            "HostName":  "tx-rhel69",
            "IPAddress":  "************",
            "machine_id":  "b9ce558b-97a2-4f61-bc4b-e6a71e5d4066",
            "user_sid":  "",
            "user_displayname":  "",
            "isInPool":  "true",
            "pool_id":  "tx-rhel69",
            "isLinkedClone":  "false",
            "composerTask":  "",
            "localState":  "",
            "netLabelAutoAssigns":  "disabled"
        }
        """
        cmd = 'Get-DesktopVM -Pool_id %s' %pool
        ret = self.Run(cmd, cacheable = True)
        print('GetDesktopVM: ')
        print(ret)
        return ret


    def GetDesktopVmName(self, pool = auto.horizonData['pool']):
        ret = self.GetDesktopVM(pool)
        return ret['Name']


    def GetAgentVMStat(self, lastMinutes = 1, stat = 'cpu.usage.average', intervalSecs = 5):
        '''

        :param lastMinutes: last N minutes' statistical
        :param stat: cpu.usage.average, mem.usage.average, ...
        :param intervalSecs: intervalSecs for command get-Stat
        :return:
        '''
        agentVMname = self.GetAgentVMName()
        ret =  self.VI_GetVMStat(vmName = agentVMname, lastMinutes = lastMinutes, stat = stat, intervalSecs = intervalSecs)
        result = ""
        for line in ret.split('\r\n'):
            if '#TYPE' in line:
                pass
            else:
                result = result + line + '\n'
        currentTime = datetime.datetime.now().strftime("%Y-%m-%d-%H-%M-%S")
        fileName = 'log\\stat_%s_%s.txt' %(stat, currentTime)
        with open(fileName, 'w') as openedfile:
            openedfile.write(result)
        kits.Comment("%s saved" %fileName)
        kits.TestCaseLog(fileName, "GetAgentVMStat")
        return result


    def GetDesktopOSType(self, pool):
        agentInfo = self.HV_GetAgentInfoOnBroker(pool=pool)
        if isinstance(agentInfo, dict):
            print(f"agent info: \n{agentInfo}")
            if 'Windows' not in agentInfo["cmd"]["OperatingSystem"]:
                return "linux"
            else:
                return "windows"
        else:
            return "unknown"


    def GetAgentVMName(self, pool):
        agentOSType = self.GetDesktopOSType(pool)
        print(f"{pool} os type : {agentOSType}")
        # FQDN for FC/IC Linux pool failed have suffix appendded sometimes
        ret = self.HV_GetVdiVMName(pool)
        if ret:
            return ret
        else:
            # RDS Desktop/App
            print("Get VM Name via FQDN")
            fqdn = self.HV_GetAgentFQDN(pool)
            print(f"FQDN: {fqdn}")
            ret = self.VI_GetVMnameViaFQDN(fqdn)
            if not "Done" in ret:
                return ret
            else:
                fqdn = fqdn.split(".")[0]
                print(f"FQDN: {fqdn}")
                return self.VI_GetVMnameViaFQDN(fqdn)
        print(f"Agent VM Name: {ret}")
        return ret


    def Env_ClearLogDump(self):
        cmd = '''
        Remove-Item -Path $Env:ProgramData\VMware\logs\*.*;
        Remove-Item -Path $Env:ProgramData\VMware\VDM\logs\*.*;
        Remove-Item -Path "$Env:ProgramData\VMware\VDM\logs\PCoIP Secure Gateway\*";
        Remove-Item -Path "$Env:ProgramData\VMware\VDM\logs\Blast Secure Gateway\*";
        '''
        kits.Comment('Clean Horizon logs on Broker')
        self.Run(cmd, WriteComment = False)


    def Env_QueryHorizonVersion(self):
        cmd = '''
        (Get-ItemProperty -Path Registry::"HKEY_LOCAL_MACHINE\SOFTWARE\VMware, Inc.\VMware VDM" -Name ProductVersion).ProductVersion;
        '''
        kits.Comment('Query Horizon Broker version')
        return self.Run(cmd, WriteComment = False)


    def Env_EnableTraceDebug(self):
        """
        Enable Horizon agent's Trace and Debug
        """
        cmd = '''
        $newItem = New-Item -Force -Path Registry::"HKEY_LOCAL_MACHINE\Software\Policies\VMware, Inc.\VMware VDM\Log";
        Set-ItemProperty -Name "MaxDebuglogs" -Value 50 -Path Registry::"HKEY_LOCAL_MACHINE\Software\Policies\VMware, Inc.\VMware VDM\Log";
        Set-ItemProperty -Name "MaxDebuglogSizeMB" -Value 100 -Path Registry::"HKEY_LOCAL_MACHINE\Software\Policies\VMware, Inc.\VMware VDM\Log";
        Set-ItemProperty -Name "DebugEnabled" -Value "TRUE" -Path Registry::"HKEY_LOCAL_MACHINE\SOFTWARE\VMware, Inc.\VMware VDM";
        Set-ItemProperty -Name "TraceEnabled" -Value "TRUE" -Path Registry::"HKEY_LOCAL_MACHINE\SOFTWARE\VMware, Inc.\VMware VDM" ;
        '''
        kits.Comment('Set Log level on broker')
        self.Run(cmd, WriteComment = False)


    def GetSIDbyUser(self, username):
        cmd = '(Get-ADUser -Identity %s).SID.Value' %username
        return self.Run(cmd)


    def GetRdshFarmName(self, pool=auto.horizonData['pool']):
        cmd = '(Get-HVFarmSummary | Where-Object {$_.Id.Id -eq (Get-HVPool -PoolName %s).RdsDesktopData.Farm.Id}).Data.Name;'%pool
        return self.Run(cmd, cacheable = True)["cmd"]


    def HV_SetFarm(self, setting, farmName):
        """
        Set the farm settings with HV module
        @setting: pool setting key and value in json format, and multiple keys
        are supported.

        Please note that, the 1st letter is lower-case in key, such as:
        "{'data.displayProtocolSettings.enableHTMLAccess': true}"

        @Examples:
        HV_SetPool("{'data.displayProtocolSettings.enableHTMLAccess': true}" , 'myfarm')
        """
        cmd = """Invoke-Command -ScriptBlock {{ Set-Content C:\\automation\\psRun\\temp\\farm-spec.json "{0}"; 
        Set-HVFarm -FarmName {1} -Spec "C:\\automation\\psRun\\temp\\farm-spec.json"}}""".format(setting,farmName)
        return self.Run(cmd)["cmd"]


    def HV_WaitState(self, pool, expectation='CONNECTED', users = [], times = 6, stopError=False):
        """
        :param expectation:  expected state
        :param users:  user list, such as ['User001', 'User002"],
                       [] for all users in main thread, and current user in sub-thread
        :param times:  retry times
        :return: True or False
        """
        fqdn = self.HV_GetAgentFQDN(pool=pool)
        waitTimes = times
        users = [x.lower() for x in users]
        expectedStates = {}
        for user in users:
            expectedStates[user] = expectation
        while times > 0:
            kits.Wait(20, 'for next Agent session state query')
            sessions = self.HV_QuerySession()
            print(sessions)
            actualStates = {}
            # multiple sessions found
            if type(sessions) == type([]):
                for session in sessions:
                    username = session["Username"].split('\\')[1]
                    state = session["state"]
                    dns = session['DNS']
                    if '.' in dns:
                        dns = dns.split('.')[0]
                    if username in users and dns in fqdn:
                        actualStates[username] = state
            # single session found
            if type(sessions) == type({}):
                if times >= 1 and sessions == {}:
                    return True
                session = sessions
                username = session["Username"].split('\\')[1]
                state = session["state"]
                dns = session['DNS']
                if '.' in dns:
                    dns = dns.split('.')[0]
                if username in users and dns in fqdn:
                    actualStates[username] = state
            # for the no session users, mark as AVAILABLE
            for user in users:
                if not user in actualStates:
                    actualStates[user] = 'AVAILABLE'
            kits.Comment(f'Expected State: {str(expectedStates)}')
            kits.Comment(f'Actual State: {str(actualStates)}')

            if actualStates == expectedStates:
                kits.Verify(True, True, 'Wait State of %s' %expectation)
                return True
            else:
                kits.Comment('actualStates does not match expectedStates "%s"' %expectation)
                for user, state in actualStates.items():
                    if state.lower() != expectation.lower():
                        comment = f'WaitState fails on {user}: actual={state} expectation={expectation} times={waitTimes-times}'
                        kits.Comment(comment)
            times = times -1
        if stopError:
            kits.Verify(False, True, 'Wait State')
        else:
            kits.Warning('Wait State timeout without expected session state')
        return False


    def Disconnect_Logoff(self, pool=auto.horizonData['pool'],
                          users=None, interval=0, action='disconnect'):
        fqdn = self.HV_GetAgentFQDN(pool=pool)
        cmd = ''
        cmdBody = """
          Invoke-Command -ScriptBlock {
          $sessions = Get-HVQueryResult SessionLocalSummaryView | Where-Object {$_.NamesData.MachineOrRDSServerDNS.Contains("%s")};
          $sessionsToHandle = @();
          Foreach ($session in $sessions){ $sessionsToHandle += $session.Id;};
        """% fqdn

        cmdPostfix_disconnect = """
          if($sessionsToHandle.Length -eq 1){$Global:DefaultHVServers.ExtensionData.Session.Session_Disconnect($sessionsToHandle[0])};
          if($sessionsToHandle.Length -gt 1){$Global:DefaultHVServers.ExtensionData.Session.Session_DisconnectSessions($sessionsToHandle)};
          "Sessions found for disconnect: ";
          Foreach ($session in $sessions){ $session.NamesData; };}
        """
        cmdPostfix_logoff = '''
        if($sessionsToHandle.Length -gt 0){$Global:DefaultHVServers.ExtensionData.Session.Session_LogoffSessionsForced($sessionsToHandle)};
        "Sessions found for logoff: ";
        Foreach ($session in $sessions){ $session.NamesData; };}
        '''
        if action.lower() == 'disconnect':
            cmd = cmdBody + cmdPostfix_disconnect
        if action.lower() == 'logoff':
            cmd = cmdBody + cmdPostfix_logoff
        kits.Comment(f"{action.lower()} session from broker")
        ret = self.HV_Run(cmd, errorStop=False)["cmd"]
        # print(ret)


    def GetCurrentTS(self):
        cmd = '(Get-Date -UFormat "%Y-%m-%d %H:%M:%S").ToString();'
        return self.Run(cmd, WriteComment = False)


    def MultiSession_Setting(self, pool = auto.horizonData['pool'], mode = 3, max_sessions = 60):
        """
        Set Multi-Session mode on app pool
        0: Disabled
        1: Enabled (Default Off)
        2: Enabled (Default On)
        3: Enabled (Enforced)

        For Example:
                Enable: broker.MultiSession_Setting(mode =3, max_sessions=60)
                Disable: broker.MultiSession_Setting(mode=0)
        """
        if self.HV_GetPoolType(pool) != 'APP':
            print('Multi-Session mode is applicable for APP only. skip')
            return
        if mode == 0:
            cmd = '''
            $obj = [adsi]'LDAP://localhost:389/CN=%s,OU=Applications,DC=vdi,DC=horizon,DC=internal';
            $obj.put("pae-MultiSessionMode", 0);
            $obj.SetInfo();
            ''' %pool
        else:
            cmd = '''
            $obj = [adsi]'LDAP://localhost:389/CN=%s,OU=Applications,DC=vdi,DC=horizon,DC=internal';
            $obj.put("pae-MultiSessionMode", %d);
            $obj.SetInfo();
            $obj.put("pae-MaxMultiSessions", %d);
            $obj.SetInfo();
            ''' %(pool, mode, max_sessions)
        self.Run(cmd)


    def MultiSession_WaitState(self, expectation = 'CONNECTED', user = 'TestUser', times = 6, stopError=False):
        '''
        This function for App Multi-Session mode only
        '''
        fullDomain = self.Get_DomainFullName()
        username = '%s\\%s' % (fullDomain, user)
        fqdn = self.HV_GetAgentFQDN()
        cmd = '''
        $DNSName = "%s";
        $UserName = "%s";
        $ViewAPI = $global:DefaultHVServers[0].ExtensionData;
        $query_service = New-Object "Vmware.Hv.QueryServiceService";
        $query = New-Object "Vmware.Hv.QueryDefinition";
        $query.queryEntityType = 'SessionLocalSummaryView';
        $sessions = $query_service.QueryService_Query($ViewAPI,$query);
        $sessions = $sessions.Results | Where-Object {$_.NamesData.MachineOrRDSServerDNS -eq $DNSName} | Where-Object {$_.NamesData.UserName -eq $UserName};
        $sessions.Length;
        ''' % (fqdn, username)
        if expectation == 'CONNECTED':
            expectedSessions = len(auto.clientInfo.clientName)
        else:
            expectedSessions = 0
        actualSessons = 0
        while times > 0:
            kits.Wait(20, 'for next MultiSession state query')
            actualSessons = self.Run(cmd, WriteComment=False)
            if actualSessons == expectedSessions:
                kits.Verify(actualSessons, expectedSessions, 'MultiSession_WaitState')
                return True
            times = times - 1
        if stopError:
            kits.Verify(actualSessons, expectedSessions, 'MultiSession_WaitState')
        else:
            kits.Warning('MultiSession_WaitState timeout without expected session state: %s, %s' %(actualSessons, expectedSessions))
        return False


    def MultiSession_Disconnect_Logoff(self, pool = auto.horizonData['pool'],
                                       user = 'TestUser',
                                       action = 'disconnect'):
        '''
        This function for App Multi-Session mode only
        '''
        fullDomain = self.Get_DomainFullName()
        username = '%s\\%s' % (fullDomain, user)
        fqdn = self.HV_GetAgentFQDN()
        cmd = ''
        cmdBody = '''
        $DNSName = "%s";
        $UserName = "%s";
        $ViewAPI = $global:DefaultHVServers[0].ExtensionData;
        $query_service = New-Object "Vmware.Hv.QueryServiceService";
        $query = New-Object "Vmware.Hv.QueryDefinition";
        $query.queryEntityType = 'SessionLocalSummaryView';
        $sessions = $query_service.QueryService_Query($ViewAPI,$query);
        $sessions = $sessions.Results | Where-Object {$_.NamesData.MachineOrRDSServerDNS -eq $DNSName} | Where-Object {$_.NamesData.UserName -eq $UserName};
        $sessionsToHandle = @();
        Foreach ($session in $sessions){ $sessionsToHandle += $session.Id;};
        "Sessions found for logoff or disconnect: ";
        $sessionsToHandle.Length;
        ''' % (fqdn, username)
        cmdPostfix_disconnect = '''
        if($sessionsToHandle.Length -eq 1){$Global:DefaultHVServers.ExtensionData.Session.Session_Disconnect($sessionsToHandle[0])};
        if($sessionsToHandle.Length -gt 1){$Global:DefaultHVServers.ExtensionData.Session.Session_DisconnectSessions($sessionsToHandle)};
        '''
        cmdPostfix_logoff = '''
        if($sessionsToHandle.Length -eq 1){$Global:DefaultHVServers.ExtensionData.Session.Session_Logoff($sessionsToHandle[0])};
        if($sessionsToHandle.Length -gt 1){$Global:DefaultHVServers.ExtensionData.Session.Session_LogoffSessionsForced($sessionsToHandle)};
        '''
        if action.lower() == 'disconnect':
            cmd = cmdBody + cmdPostfix_disconnect
        if action.lower() == 'logoff':
            cmd = cmdBody + cmdPostfix_logoff
        self.HV_Run(cmd, errorStop=False)

    def Execute_Rest(self, action):
        """Execute broker rest api, currently used by helpdesk testcases
        """
        url = "https://{}/view-vlsi/rest/v1/".format(self.brokerIP)
        try:
            if action.lower() == "prepare":
                # Get login token and cookie
                resp = requests.post(url + "login",
                       headers={'Content-Type':'application/json'},
                       json={'name': self.brokerUser, 'passwd': self.brokerPassword,
                              'domain':self.brokerDomain}, verify=False)

                if resp.status_code != 200:
                    return False, "Login failure"

                self.token = resp.headers['CSRFToken']
                self.cookie = resp.headers['Set-Cookie'][:43]

                if self.token and self.cookie:
                    return True, "Prepare success"
            else:
                resp = requests.get(url + "queryservice/SessionLocalSummaryView",
                       headers={'Content-Type':'application/json', 'CSRFToken':self.token, 'Cookie':self.cookie},
                       verify=False)

                if resp.status_code != 200:
                    return False, "SessionLocalSummaryView failure"

                session_id = resp.json()['results'][0]['id']
                suburl = ""
                if action.lower() == "blast":
                    suburl = "performance/getDisplayProtocolPerformanceData"
                elif action.lower() == "client":
                    suburl = "viewclient/get"
                elif action.lower() == "process":
                    suburl = "performance/getProcessPerformanceData"
                elif action.lower() == "history":
                    suburl = "performance/getHistoricalPerformanceData"

                if not suburl:
                    return False, "Wrong action " + suburl

                resp = requests.post(url + suburl,
                       headers={'Content-Type':'application/json', 'CSRFToken':self.token, 'Cookie':self.cookie},
                       json={'id':session_id},
                       verify=False)

                if resp.status_code != 200:
                    return False, suburl + " failure"

                return True, resp.json()
        except Exception as e:
            return False, "Exception %s" % str(e)

        return False, "Failure"

# Initialize a Broker object:
broker = Broker(auto.horizonData['broker'])

