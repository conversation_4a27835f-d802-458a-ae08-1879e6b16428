<?xml version="1.0" encoding="utf-8"?>

   <!--
   **********************************************************************
      Copyright (c) Omnissa, LLC. All rights reserved.
      This product is protected by copyright and intellectual property laws in the
      United States and other countries as well as by international treaties.
      - Omnissa Restricted


      UI.wxi  (product: Horizon Agent)

         WiX include for all UI dialogs/elements included in this product.

   **********************************************************************
   -->

<Include>

   <Binary Id="completeSetupIco" SourceFile="$(var.SRCROOT)\install\msi\horizon\agent\resources\CompleteSetupIco.ibd" />
   <Binary Id="destIcon.ibd" SourceFile="$(var.SRCROOT)\install\msi\horizon\agent\resources\DestIcon.ibd" />
   <Binary Id="dialogBanner" SourceFile="$(var.WixUIBannerBmp)" />
   <Binary Id="dialogBitmap" SourceFile="$(var.WixUIDialogBmp)" />
   <Binary Id="dontInstall.ico" SourceFile="$(var.SRCROOT)\install\msi\horizon\agent\resources\dontInstall.ico" />
   <Binary Id="install.ico" SourceFile="$(var.SRCROOT)\install\msi\horizon\agent\resources\install.ico" />
   <Binary Id="installFirstUse.ico" SourceFile="$(var.SRCROOT)\install\msi\horizon\agent\resources\installFirstUse.ico" />
   <Binary Id="installPartial.ico" SourceFile="$(var.SRCROOT)\install\msi\horizon\agent\resources\installPartial.ico" />
   <Binary Id="installStateMenu.ico" SourceFile="$(var.SRCROOT)\install\msi\horizon\agent\resources\installStateMenu.ico" />
   <Binary Id="new.ibd" SourceFile="$(var.SRCROOT)\install\msi\horizon\agent\resources\new.ibd" />
   <Binary Id="networkInstall.ico" SourceFile="$(var.SRCROOT)\install\msi\horizon\agent\resources\networkInstall.ico" />
   <Binary Id="CustomSetupIco.ibd" SourceFile="$(var.SRCROOT)\install\msi\horizon\agent\resources\CustomSetupIco.ibd" />
   <Binary Id="reinstIco.ibd" SourceFile="$(var.SRCROOT)\install\msi\horizon\agent\resources\reinstIco.ibd" />
   <Binary Id="removeIco.ibd" SourceFile="$(var.SRCROOT)\install\msi\horizon\agent\resources\removeIco.ibd" />
   <Binary Id="setupIcon.ibd" SourceFile="$(var.SRCROOT)\install\msi\horizon\agent\resources\setupIcon.ibd" />
   <Binary Id="up.ibd" SourceFile="$(var.SRCROOT)\install\msi\horizon\agent\resources\up.ibd" />
   <Binary Id="warningIcon.ibd" SourceFile="$(var.SRCROOT)\install\msi\horizon\agent\resources\WarningIcon.ibd" />

   <UI>
      <TextStyle Id="Arial8" FaceName="Arial" Size="8" />
      <TextStyle Id="Arial9" FaceName="Arial" Size="9" />
      <TextStyle Id="ArialBlue10" FaceName="Arial" Size="10" Blue="255" />
      <TextStyle Id="ArialBlueStrike10" FaceName="Arial" Size="10" Blue="255" Strike="yes" />
      <TextStyle Id="CourierNew8" FaceName="Courier New" Size="8" />
      <TextStyle Id="CourierNew9" FaceName="Courier New" Size="9" />
      <TextStyle Id="MSGothic9" FaceName="MS Gothic" Size="9" />
      <TextStyle Id="MSSGreySerif8" FaceName="MS Sans Serif" Size="8" Red="128" Green="128" Blue="128" />
      <TextStyle Id="MSSWhiteSerif8" FaceName="Tahoma" Size="8" Red="255" Green="255" Blue="255" />
      <TextStyle Id="MSSansBold8" FaceName="Tahoma" Size="8" Bold="yes" />
      <TextStyle Id="MSSansSerif8" FaceName="MS Sans Serif" Size="8" />
      <TextStyle Id="MSSansSerif9" FaceName="MS Sans Serif" Size="9" />
      <TextStyle Id="Tahoma8" FaceName="Tahoma" Size="8" />
      <TextStyle Id="Tahoma9" FaceName="Tahoma" Size="9" />
      <TextStyle Id="Tahoma10" FaceName="Tahoma" Size="10" />
      <TextStyle Id="TahomaBold8" FaceName="Tahoma" Size="8" Bold="yes" />
      <TextStyle Id="TahomaBold10" FaceName="Tahoma" Size="10" Bold="yes" />
      <TextStyle Id="Times8" FaceName="Times New Roman" Size="8" />
      <TextStyle Id="Times9" FaceName="Times New Roman" Size="9" />
      <TextStyle Id="TimesItalic12" FaceName="Times New Roman" Size="12" Italic="yes" />
      <TextStyle Id="TimesItalicBlue10" FaceName="Times New Roman" Size="10" Blue="255" Italic="yes" />
      <TextStyle Id="TimesRed16" FaceName="Times New Roman" Size="16" Red="255" />

      <Property Id="DefaultUIFont" Value="Tahoma8" />


      <!-- UI Dialogs -->
      <?include $(var.WixUIDir)\AdminChangeFolder.wxi ?>
      <?include $(var.WixUIDir)\AdminNetworkLocation.wxi ?>
      <?include $(var.WixUIDir)\AdminWelcome.wxi ?>
      <?include $(var.WixUIDir)\CancelSetup.wxi ?>
      <?include $(var.WixUIDir)\ConnectionServer.wxi ?>
      <?include $(var.WixUIDir)\CustomSetup.wxi ?>
      <?include $(var.WixUIDir)\CustomSetupTips.wxi ?>
      <?include $(var.WixUIDir)\DesktopConfig.wxi ?>
      <?include $(var.WixUIDir)\InstallRolesConfirm.wxi ?>
      <?include $(var.WixUIDir)\DiskSpaceRequirements.wxi ?>
      <?include $(var.WixUIDir)\FilesInUse.wxi ?>
      <?include $(var.WixUIDir)\GoldenImage.wxi ?>
      <?include $(var.WixUIDir)\InstallChangeFolder.wxi ?>
      <?include $(var.WixUIDir)\InstallWelcome.wxi ?>
      <?include $(var.WixUIDir)\IpProtocolConfig.wxi ?>
      <?include $(var.WixUIDir)\MaintenanceType.wxi ?>
      <?include $(var.WixUIDir)\MaintenanceWelcome.wxi ?>
      <?include $(var.WixUIDir)\MessageBox.wxi ?>
      <?include $(var.WixUIDir)\MsiRMFilesInUse.wxi ?>
      <?include $(var.WixUIDir)\OutOfSpace.wxi ?>
      <?include $(var.WixUIDir)\RdpConfig.wxi ?>
      <?include $(var.WixUIDir)\ReadyToInstall.wxi ?>
      <?include $(var.WixUIDir)\ReadyToRemove.wxi ?>
      <?include $(var.WixUIDir)\SetupCompleteError.wxi ?>
      <?include $(var.WixUIDir)\SetupCompleteSuccess.wxi ?>
      <?include $(var.WixUIDir)\SetupError.wxi ?>
      <?include $(var.WixUIDir)\SetupInitialization.wxi ?>
      <?include $(var.WixUIDir)\SetupInterrupted.wxi ?>
      <?include $(var.WixUIDir)\SetupProgress.wxi ?>
      <?include $(var.WixUIDir)\SetupResume.wxi ?>
      <?include $(var.WixUIDir)\UNCMessageBox.wxi ?>


      <Property Id="ErrorDialog" Value="SetupError" />


      <!-- These entries represent the ControlEvent table -->
      <Publish Dialog="CancelSetup" Control="No" Event="EndDialog" Value="Return" Order="0">1</Publish>
      <Publish Dialog="CancelSetup" Control="Yes" Event="EndDialog" Value="Exit" Order="2">1</Publish>


      <Publish Dialog="FilesInUse" Control="Exit" Event="EndDialog" Value="Exit" Order="1">1</Publish>
      <Publish Dialog="FilesInUse" Control="Ignore" Event="EndDialog" Value="Ignore" Order="1">1</Publish>
      <Publish Dialog="FilesInUse" Control="Retry" Event="EndDialog" Value="Retry" Order="1">1</Publish>


      <Publish Dialog="MsiRMFilesInUse" Control="Cancel" Event="EndDialog" Value="Exit" Order="1">1</Publish>
      <Publish Dialog="MsiRMFilesInUse" Control="OK" Event="EndDialog" Value="Return" Order="1">1</Publish>
      <Publish Dialog="MsiRMFilesInUse" Control="OK" Event="RMShutdownAndRestart" Value="0" Order="2">RestartManagerOption="CloseRestart"</Publish>


      <Publish Dialog="MessageBox" Control="OK" Event="[MessageBoxOK]" Value="1" Order="1">1</Publish>
      <Publish Dialog="MessageBox" Control="OK" Event="EndDialog" Value="Return" Order="2">1</Publish>

      <Publish Dialog="UNCMessageBox" Control="OK" Event="[MessageBoxOK]" Value="1" Order="1">1</Publish>
      <Publish Dialog="UNCMessageBox" Control="OK" Event="EndDialog" Value="Return" Order="2">1</Publish>

      <Publish Dialog="AdminWelcome" Control="Cancel" Event="SpawnDialog" Value="CancelSetup" Order="0">1</Publish>
      <Publish Dialog="AdminWelcome" Control="Next" Event="NewDialog" Value="AdminNetworkLocation" Order="1">1</Publish>


      <Publish Dialog="AdminNetworkLocation" Control="Cancel" Event="SpawnDialog" Value="CancelSetup" Order="0">1</Publish>
      <Publish Dialog="AdminNetworkLocation" Control="Back" Event="NewDialog" Value="AdminWelcome" Order="1">1</Publish>
      <Publish Dialog="AdminNetworkLocation" Control="Browse" Event="SpawnDialog" Value="AdminChangeFolder" Order="0">1</Publish>
      <Publish Dialog="AdminNetworkLocation" Control="InstallNow" Event="SetTargetPath" Value="TARGETDIR" Order="1">1</Publish>
      <Publish Dialog="AdminNetworkLocation" Control="InstallNow" Event="NewDialog" Value="OutOfSpace" Order="2">OutOfNoRbDiskSpace=1</Publish>
      <Publish Dialog="AdminNetworkLocation" Control="InstallNow" Event="EndDialog" Value="Return" Order="3">OutOfNoRbDiskSpace &lt;&gt; 1</Publish>


      <Publish Dialog="AdminChangeFolder" Control="Cancel" Event="Reset" Value="0" Order="1">1</Publish>
      <Publish Dialog="AdminChangeFolder" Control="Cancel" Event="EndDialog" Value="Return" Order="2">1</Publish>
      <Publish Dialog="AdminChangeFolder" Control="NewFolder" Event="DirectoryListNew" Value="0" Order="1">1</Publish>
      <Publish Dialog="AdminChangeFolder" Control="OK" Event="SetTargetPath" Value="TARGETDIR" Order="1">1</Publish>
      <Publish Dialog="AdminChangeFolder" Control="OK" Event="EndDialog" Value="Return" Order="2">1</Publish>
      <Publish Dialog="AdminChangeFolder" Control="Up" Event="DirectoryListUp" Value="0" Order="1">1</Publish>


      <Publish Dialog="SetupInitialization" Control="Cancel" Event="SpawnDialog" Value="CancelSetup" Order="0">1</Publish>


      <Publish Dialog="InstallWelcome" Control="Cancel" Event="SpawnDialog" Value="CancelSetup" Order="1">1</Publish>
      <Publish Dialog="InstallWelcome" Control="Next" Event="[DesktopConfig_Back]" Value="InstallWelcome" Order="1">1</Publish>
      <Publish Dialog="InstallWelcome" Control="Next" Event="[IpProtocolConfig_Back]" Value="InstallWelcome" Order="2">1</Publish>
      <Publish Dialog="InstallWelcome" Control="Next" Event="NewDialog" Value="DesktopConfig" Order="3">Not (MsiNTProductType=1 Or TerminalServer Or VdmForceDesktopAgent&lt;&gt;1 Or VDM_INSTALLER_CHECKS=0)</Publish>
      <Publish Dialog="InstallWelcome" Control="Next" Event="NewDialog" Value="IpProtocolConfig" Order="4">(MsiNTProductType=1 Or TerminalServer Or VdmForceDesktopAgent&lt;&gt;1 Or VDM_INSTALLER_CHECKS=0)</Publish>      
      <Publish Dialog="InstallWelcome" Control="Next" Event="[ERROR_NUMBER]" Value="{}" Order="5">1</Publish>
      <Publish Dialog="InstallWelcome" Control="Next" Event="[ERROR_NUMBER]" Value="28100" Order="6">VDM_INSTALLER_CHECKS=0</Publish>
      <Publish Dialog="InstallWelcome" Control="Next" Event="DoAction" Value="VM_MessageBox" Order="7">ERROR_NUMBER="28100"</Publish>


      <Publish Dialog="MaintenanceWelcome" Control="Cancel" Event="SpawnDialog" Value="CancelSetup" Order="0">1</Publish>
      <Publish Dialog="MaintenanceWelcome" Control="Next" Event="NewDialog" Value="MaintenanceType" Order="1">1</Publish>


      <Publish Dialog="MaintenanceType" Control="Back" Event="NewDialog" Value="MaintenanceWelcome" Order="1">1</Publish>
      <Publish Dialog="MaintenanceType" Control="Cancel" Event="SpawnDialog" Value="CancelSetup" Order="1">1</Publish>
      <Publish Dialog="MaintenanceType" Control="Next" Event="[CustomSetup_Back]" Value="MaintenanceType" Order="1">1</Publish>
      <Publish Dialog="MaintenanceType" Control="Next" Event="[ConnectionServer_Back]" Value="MaintenanceType" Order="2">1</Publish>
      <Publish Dialog="MaintenanceType" Control="Next" Event="[ReadyToInstall_Back]" Value="MaintenanceType" Order="3">1</Publish>
      <Publish Dialog="MaintenanceType" Control="Next" Event="[RdpConfig_Back]" Value="MaintenanceType" Order="4">1</Publish>
      <!-- Modify events -->
      <Publish Dialog="MaintenanceType" Control="Next" Event="[ProgressType0]" Value="Modify" Order="5">_IsMaintenance="Modify"</Publish>
      <Publish Dialog="MaintenanceType" Control="Next" Event="[ProgressType1]" Value="Modifying" Order="6">_IsMaintenance="Modify"</Publish>
      <Publish Dialog="MaintenanceType" Control="Next" Event="[ProgressType2]" Value="modifies" Order="7">_IsMaintenance="Modify"</Publish>
      <Publish Dialog="MaintenanceType" Control="Next" Event="[ProgressType3]" Value="modifies" Order="8">_IsMaintenance="Modify"</Publish>
      <!-- Repair events -->
      <Publish Dialog="MaintenanceType" Control="Next" Event="[ProgressType0]" Value="Repair" Order="9">_IsMaintenance="Reinstall"</Publish>
      <Publish Dialog="MaintenanceType" Control="Next" Event="[ProgressType1]" Value="Repairing" Order="10">_IsMaintenance="Reinstall"</Publish>
      <Publish Dialog="MaintenanceType" Control="Next" Event="[ProgressType2]" Value="repairs" Order="11">_IsMaintenance="Reinstall"</Publish>
      <Publish Dialog="MaintenanceType" Control="Next" Event="[ProgressType3]" Value="repairs" Order="12">_IsMaintenance="Reinstall"</Publish>
      <Publish Dialog="MaintenanceType" Control="Next" Event="[ComposerInstalling]" Value="{}" Order="13">1</Publish>
      <Publish Dialog="MaintenanceType" Control="Next" Event="[ComposerInstalling]" Value="1" Order="14">&amp;NGVC=3 Or !NGVC=3</Publish>
      <Publish Dialog="MaintenanceType" Control="Next" Event="[ProgressType0]" Value="Remove" Order="15">_IsMaintenance="Remove"</Publish>
      <!-- Shared events -->
      <Publish Dialog="MaintenanceType" Control="Next" Event="DoAction" Value="VM_MustReboot" Order="16">(VMREBOOT And Not VM_COMPONENT_REBOOT_REQUESTED) And Not _IsMaintenance="Remove"</Publish>
      <Publish Dialog="MaintenanceType" Control="Next" Event="DoAction" Value="VM_AdminAccessRequired_ModifyRepair" Order="17">RestrictedUserControl=1 And Not _IsMaintenance="Remove" And Not PATCH</Publish>
      <Publish Dialog="MaintenanceType" Control="Next" Event="DoAction" Value="VM_AdminAccessRequired_Patch" Order="18">RestrictedUserControl=1 And Not _IsMaintenance="Remove" And PATCH</Publish>
      <!-- Next Dialog -->
      <Publish Dialog="MaintenanceType" Control="Next" Event="NewDialog" Value="CustomSetup" Order="19">_IsMaintenance="Modify"</Publish>
      <Publish Dialog="MaintenanceType" Control="Next" Event="NewDialog" Value="ConnectionServer" Order="20">_IsMaintenance="Reinstall" And Not RDP_STATUS="#1" And (Not VDM_VC_MANAGED_AGENT=1 Or (VDM_VC_MANAGED_AGENT=1 And TerminalServer And Not ComposerInstalling)) And Not (VDM_SKIP_BROKER_REGISTRATION=1 Or IsAzureManagedDeployment=1)</Publish>
      <Publish Dialog="MaintenanceType" Control="Next" Event="NewDialog" Value="RdpConfig" Order="21">_IsMaintenance="Reinstall" And RDP_STATUS="#1"</Publish>
      <Publish Dialog="MaintenanceType" Control="Next" Event="NewDialog" Value="ReadyToRemove" Order="22">_IsMaintenance="Remove"</Publish>
      <Publish Dialog="MaintenanceType" Control="Next" Event="NewDialog" Value="ReadyToInstall" Order="23">_IsMaintenance="Reinstall" And Not RDP_STATUS="#1" And ((VDM_VC_MANAGED_AGENT=1 And TerminalServer And ComposerInstalling) Or (VDM_VC_MANAGED_AGENT=1 And Not TerminalServer) Or VDM_SKIP_BROKER_REGISTRATION=1 Or IsAzureManagedDeployment=1) And Not CLIENTRUNNING</Publish>
      <Publish Dialog="MaintenanceType" Control="Next" Event="DoAction" Value="VM_NoRepairAllowed" Order="24">_IsMaintenance="Reinstall" And CLIENTRUNNING</Publish>

      <Publish Dialog="DesktopConfig" Control="Back" Event="NewDialog" Value="[DesktopConfig_Back]" Order="1">1</Publish>
      <Publish Dialog="DesktopConfig" Control="Cancel" Event="SpawnDialog" Value="CancelSetup" Order="1">1</Publish>
      <Publish Dialog="DesktopConfig" Control="Next" Event="SpawnDialog" Value="InstallRolesConfirm" Order="1">VDM_INSTALL_MODE="RDS Mode"</Publish>
      <Publish Dialog="DesktopConfig" Control="Next" Event="EndDialog" Value="Return" Order="2">VDM_INSTALLING_ROLES And InstallOSRolesAndFeaturesSuccessful</Publish>
      <Publish Dialog="DesktopConfig" Control="Next" Event="EndDialog" Value="Exit" Order="3">VDM_INSTALLING_ROLES AND Not InstallOSRolesAndFeaturesSuccessful=1</Publish>
      <Publish Dialog="DesktopConfig" Control="Next" Event="[IpProtocolConfig_Back]" Value="DesktopConfig" Order="4">Not VDM_INSTALL_MODE="RDS Mode"</Publish>
      <Publish Dialog="DesktopConfig" Control="Next" Event="NewDialog" Value="IpProtocolConfig" Order="5">Not VDM_INSTALL_MODE="RDS Mode"</Publish>


      <Publish Dialog="InstallRolesConfirm" Control="Cancel" Event="EndDialog" Value="Return" Order="1">1</Publish>
      <Publish Dialog="InstallRolesConfirm" Control="OK" Event="[VDM_INSTALLING_ROLES]" Value="1" Order="1">1</Publish>
      <Publish Dialog="InstallRolesConfirm" Control="OK" Event="DoAction" Value="VM_InstallOSRolesAndFeatures" Order="2">1</Publish>
      <Publish Dialog="InstallRolesConfirm" Control="OK" Event="EndDialog" Value="Return" Order="3">1</Publish>


      <Publish Dialog="IpProtocolConfig" Control="Back" Event="NewDialog" Value="[IpProtocolConfig_Back]" Order="1">1</Publish>
      <Publish Dialog="IpProtocolConfig" Control="Cancel" Event="SpawnDialog" Value="CancelSetup" Order="1">1</Publish>
      <Publish Dialog="IpProtocolConfig" Control="Next" Event="DoAction" Value="VM_SetVDM_LOOPBACK_IP" Order="1">1</Publish>
      <Publish Dialog="IpProtocolConfig" Control="Next" Event="[MessageBoxText]" Value="!(loc.MsgVdmLoopbackIp)" Order="2">Not VDM_LOOPBACK_IP</Publish>
      <Publish Dialog="IpProtocolConfig" Control="Next" Event="SpawnDialog" Value="MessageBox" Order="3">Not VDM_LOOPBACK_IP</Publish>
      <Publish Dialog="IpProtocolConfig" Control="Next" Event="[CustomSetup_Back]" Value="IpProtocolConfig" Order="4">VDM_LOOPBACK_IP And Not VM="Nutanix"</Publish>
      <Publish Dialog="IpProtocolConfig" Control="Next" Event="[GoldenImage_Back]" Value="IpProtocolConfig" Order="5">VDM_LOOPBACK_IP And VM="Nutanix"</Publish>
      <Publish Dialog="IpProtocolConfig" Control="Next" Event="NewDialog" Value="CustomSetup" Order="6">VDM_LOOPBACK_IP and Not VM="Nutanix"</Publish>
      <Publish Dialog="IpProtocolConfig" Control="Next" Event="NewDialog" Value="GoldenImage" Order="7">VDM_LOOPBACK_IP And VM="Nutanix"</Publish>
      <Publish Dialog="IpProtocolConfig" Control="Next" Event="DoAction" Value="VM_DeselectAgentFeaturesForIPv6" Order="8">Not VDM_IP_PROTOCOL_USAGE="IPv4" AND NOT Installed</Publish>


      <Publish Dialog="GoldenImage" Control="Back" Event="NewDialog" Value="[GoldenImage_Back]" Order="1">1</Publish>
      <Publish Dialog="GoldenImage" Control="Cancel" Event="SpawnDialog" Value="CancelSetup" Order="1">1</Publish>
      <Publish Dialog="GoldenImage" Control="Next" Event="[CustomSetup_Back]" Value="GoldenImage" Order="1">1</Publish>
      <Publish Dialog="GoldenImage" Control="Next" Event="AddLocal" Value="NGVC" Order="2">GOLDEN_IMAGE_INSTALL=1</Publish>
      <Publish Dialog="GoldenImage" Control="Next" Event="Remove" Value="NGVC" Order="3">Not GOLDEN_IMAGE_INSTALL=1</Publish>
      <Publish Dialog="GoldenImage" Control="Next" Event="NewDialog" Value="CustomSetup" Order="4">1</Publish>


      <Publish Dialog="CustomSetup" Control="Back" Event="NewDialog" Value="[CustomSetup_Back]" Order="1">1</Publish>
      <Publish Dialog="CustomSetup" Control="Cancel" Event="SpawnDialog" Value="CancelSetup" Order="1">1</Publish>
      <Publish Dialog="CustomSetup" Control="ChangeFolder" Event="SelectionBrowse" Value="InstallChangeFolder" Order="0">1</Publish>
      <Publish Dialog="CustomSetup" Control="Details" Event="SelectionBrowse" Value="DiskSpaceRequirements" Order="1">1</Publish>
      <Publish Dialog="CustomSetup" Control="Help" Event="SpawnDialog" Value="CustomSetupTips" Order="1">1</Publish>
      <!-- Set condition-less properties -->
      <Publish Dialog="CustomSetup" Control="Next" Event="[RdpConfig_Back]" Value="CustomSetup" Order="1">1</Publish>
      <Publish Dialog="CustomSetup" Control="Next" Event="[ConnectionServer_Back]" Value="CustomSetup" Order="2">1</Publish>
      <Publish Dialog="CustomSetup" Control="Next" Event="[ReadyToInstall_Back]" Value="CustomSetup" Order="3">1</Publish>
      <Publish Dialog="CustomSetup" Control="Next" Event="[CustomSetupOkToContinue]" Value="{}" Order="4">1</Publish>
      <Publish Dialog="CustomSetup" Control="Next" Event="[ComposerFeatureInstalling]" Value="{}" Order="5">1</Publish>
      <Publish Dialog="CustomSetup" Control="Next" Event="[BrokerConnectionRequired]" Value="{}" Order="6">1</Publish>
      <Publish Dialog="CustomSetup" Control="Next" Event="[ERROR_NUMBER]" Value="{}" Order="7">1</Publish>
      <!-- These features can't be changed -->
      <Publish Dialog="CustomSetup" Control="Next" Event="[ErrorNGVCChanged]" Value="{}" Order="10">1</Publish>
      <Publish Dialog="CustomSetup" Control="Next" Event="[ErrorNGVCChanged]" Value="1" Order="11">
         Installed And
         (
            (!NGVC=3 And &amp;NGVC=2) Or
            (Not !NGVC=3 And &amp;NGVC=3)
         )
      </Publish>

      <!-- Check error conditions -->
         <!-- Blocking errors -->
         <Property Id="CustomSetup_BlockingErrors" Value="28111,28113" />
      <Publish Dialog="CustomSetup" Control="Next" Event="[ERROR_NUMBER]" Value="28113" Order="19">Not ERROR_NUMBER And ErrorNGVCChanged</Publish>
      <!-- During a modify install, don't allow the user to progress unless they've actually changed a feature state -->
      <Publish Dialog="CustomSetup" Control="Next" Event="DoAction" Value="VM_ValidateRequestedFeatureChanges" Order="22">Not ERROR_NUMBER And Installed</Publish>
      <Publish Dialog="CustomSetup" Control="Next" Event="[ERROR_NUMBER]" Value="28111" Order="29">Not ERROR_NUMBER And Installed And Not FeatureStatesChanged</Publish>
         <!-- Non-blocking errors -->
      <Publish Dialog="CustomSetup" Control="Next" Event="[ERROR_NUMBER]" Value="28090" Order="30">Not ERROR_NUMBER And ((RDP_MAX_INSTANCE_COUNT And RDP_MAX_INSTANCE_COUNT&lt;&gt;"#-1") Or (RDS_MAX_INSTANCE_COUNT And RDS_MAX_INSTANCE_COUNT&lt;&gt;"#-1"))</Publish>
      <Publish Dialog="CustomSetup" Control="Next" Event="[ERROR_NUMBER]" Value="28096" Order="31">Not ERROR_NUMBER And &amp;USB=3</Publish>
      <Publish Dialog="CustomSetup" Control="Next" Event="[ERROR_NUMBER]" Value="28116" Order="33">Not ERROR_NUMBER And PrintSpoolerServiceRunning=0 And &amp;PrintRedir=3</Publish>
      <!-- Display error dialogs -->
      <Publish Dialog="CustomSetup" Control="Next" Event="DoAction" Value="VM_MessageBox" Order="40">ERROR_NUMBER</Publish>
      <Publish Dialog="CustomSetup" Control="Next" Event="NewDialog" Value="OutOfSpace" Order="41">OutOfNoRbDiskSpace=1</Publish>
      <Publish Dialog="CustomSetup" Control="Next" Event="DoAction" Value="VM_ValidateINSTALLDIR" Order="42">Not Installed</Publish>
      <Publish Dialog="CustomSetup" Control="Next" Event="[CustomSetupOkToContinue]" Value="1" Order="43">Not OutOfNoRbDiskSpace=1 And (Installed Or BadINSTALLDIR=0) And (Not ERROR_NUMBER Or (ERROR_NUMBER And Not CustomSetup_BlockingErrors&gt;&lt;ERROR_NUMBER))</Publish>
      <Publish Dialog="CustomSetup" Control="Next" Event="[ComposerFeatureInstalling]" Value="1" Order="44">&amp;NGVC=3</Publish>
      <Publish Dialog="CustomSetup" Control="Next" Event="[BrokerConnectionRequired]" Value="1" Order="45">
         (Not VDM_VC_MANAGED_AGENT=1 Or (VDM_VC_MANAGED_AGENT=1 And TerminalServer And Not ComposerFeatureInstalling)) And Not Installed And Not VDM_SKIP_BROKER_REGISTRATION=1 And Not IsAzureManagedDeployment=1
      </Publish>
      <!-- Move to the next dialog -->
      <Publish Dialog="CustomSetup" Control="Next" Event="NewDialog" Value="RdpConfig" Order="50">CustomSetupOkToContinue And Not RDPEnabled</Publish>
      <Publish Dialog="CustomSetup" Control="Next" Event="NewDialog" Value="ConnectionServer" Order="51">CustomSetupOkToContinue And RDPEnabled And BrokerConnectionRequired</Publish>
      <Publish Dialog="CustomSetup" Control="Next" Event="NewDialog" Value="ReadyToInstall" Order="52">CustomSetupOkToContinue And RDPEnabled And Not BrokerConnectionRequired</Publish>


      <Publish Dialog="CustomSetupTips" Control="OK" Event="EndDialog" Value="Return" Order="1">1</Publish>


      <Publish Dialog="InstallChangeFolder" Control="Cancel" Event="Reset" Value="0" Order="1">1</Publish>
      <Publish Dialog="InstallChangeFolder" Control="Cancel" Event="EndDialog" Value="Return" Order="2">1</Publish>
      <Publish Dialog="InstallChangeFolder" Control="NewFolder" Event="DirectoryListNew" Value="0" Order="1">1</Publish>
      <Publish Dialog="InstallChangeFolder" Control="OK" Event="DoAction" Value="VM_ValidateINSTALLDIR" Order="1">1</Publish>
      <Publish Dialog="InstallChangeFolder" Control="OK" Event="SetTargetPath" Value="[_BrowseProperty]" Order="2">BadINSTALLDIR=0</Publish>
      <Publish Dialog="InstallChangeFolder" Control="OK" Event="EndDialog" Value="Return" Order="3">BadINSTALLDIR=0</Publish>
      <Publish Dialog="InstallChangeFolder" Control="Up" Event="DirectoryListUp" Value="0" Order="1">1</Publish>


      <Publish Dialog="DiskSpaceRequirements" Control="OK" Event="EndDialog" Value="Return" Order="1">1</Publish>


      <Publish Dialog="OutOfSpace" Control="OK" Event="NewDialog" Value="AdminNetworkLocation" Order="1">ACTION="ADMIN"</Publish>
      <Publish Dialog="OutOfSpace" Control="OK" Event="NewDialog" Value="CustomSetup" Order="2">ACTION &lt;&gt; "ADMIN"</Publish>


      <Publish Dialog="RdpConfig" Control="Back" Event="NewDialog" Value="[RdpConfig_Back]" Order="1">1</Publish>
      <Publish Dialog="RdpConfig" Control="Cancel" Event="SpawnDialog" Value="CancelSetup" Order="1">1</Publish>
      <Publish Dialog="RdpConfig" Control="Next" Event="AddLocal" Value="RDP" Order="1">RDP_CHOICE=1</Publish>
      <Publish Dialog="RdpConfig" Control="Next" Event="Remove" Value="RDP" Order="2">RDP_CHOICE=0</Publish>
      <Publish Dialog="RdpConfig" Control="Next" Event="[ComposerInstalling]" Value="{}" Order="3">1</Publish>
      <Publish Dialog="RdpConfig" Control="Next" Event="[ComposerInstalling]" Value="1" Order="4">&amp;NGVC=3</Publish>
      <Publish Dialog="RdpConfig" Control="Next" Event="NewDialog" Value="ConnectionServer" Order="5">(Not VDM_VC_MANAGED_AGENT=1 Or (VDM_VC_MANAGED_AGENT=1 And TerminalServer And Not ComposerInstalling)) And Not (VDM_SKIP_BROKER_REGISTRATION=1 Or IsAzureManagedDeployment=1)</Publish>
      <Publish Dialog="RdpConfig" Control="Next" Event="NewDialog" Value="ReadyToInstall" Order="6">(VDM_VC_MANAGED_AGENT=1 And TerminalServer And ComposerInstalling) Or (VDM_VC_MANAGED_AGENT=1 And Not TerminalServer) Or VDM_SKIP_BROKER_REGISTRATION=1 Or IsAzureManagedDeployment=1</Publish>
      <Publish Dialog="RdpConfig" Control="Next" Event="[ConnectionServer_Back]" Value="RdpConfig" Order="7">1</Publish>
      <Publish Dialog="RdpConfig" Control="Next" Event="[ReadyToInstall_Back]" Value="RdpConfig" Order="8">1</Publish>


      <Publish Dialog="ConnectionServer" Control="Back" Event="NewDialog" Value="[ConnectionServer_Back]" Order="1">1</Publish>
      <Publish Dialog="ConnectionServer" Control="Cancel" Event="SpawnDialog" Value="CancelSetup" Order="1">1</Publish>
      <Publish Dialog="ConnectionServer" Control="Next" Event="[ReadyToInstall_Back]" Value="ConnectionServer" Order="1">1</Publish>
      <Publish Dialog="ConnectionServer" Control="Next" Event="DoAction" Value="VM_ValidateConnectionServer" Order="2">VDM_SERVER_NAME</Publish>
      <Publish Dialog="ConnectionServer" Control="Next" Event="NewDialog" Value="ReadyToInstall" Order="3">BadConnectionServer=0 Or VDM_INSTALLER_CHECKS=0</Publish>


      <Publish Dialog="ReadyToInstall" Control="Back" Event="NewDialog" Value="[ReadyToInstall_Back]" Order="1">1</Publish>
      <Publish Dialog="ReadyToInstall" Control="Cancel" Event="SpawnDialog" Value="CancelSetup" Order="0">1</Publish>
      <Publish Dialog="ReadyToInstall" Control="InstallNow" Event="EndDialog" Value="Return" Order="1">OutOfNoRbDiskSpace &lt;&gt; 1</Publish>
      <Publish Dialog="ReadyToInstall" Control="InstallNow" Event="NewDialog" Value="OutOfSpace" Order="2">OutOfNoRbDiskSpace=1</Publish>
      <Publish Dialog="ReadyToInstall" Control="InstallNow" Event="DoAction" Value="VM_CheckWindowsUpdateProgress" Order="3">1</Publish>
      <Publish Dialog="ReadyToInstall" Control="InstallNow" Event="[MessageBoxText]" Value="!(loc.MsgWindowsUpdateInProgress)" Order="4">VDM_WINDOWS_UPDATE_IN_PROGRESS=1 And Not VDM_SKIP_WINDOWS_UPDATE_CHECK=1</Publish>
      <Publish Dialog="ReadyToInstall" Control="InstallNow" Event="SpawnDialog" Value="MessageBox" Order="5">VDM_WINDOWS_UPDATE_IN_PROGRESS=1 And Not VDM_SKIP_WINDOWS_UPDATE_CHECK=1</Publish>
      <Publish Dialog="ReadyToInstall" Control="InstallNow" Event="[ProgressType1]" Value="Installing" Order="6">1</Publish>
      <Publish Dialog="ReadyToInstall" Control="InstallNow" Event="[ProgressType2]" Value="installed" Order="7">1</Publish>
      <Publish Dialog="ReadyToInstall" Control="InstallNow" Event="[ProgressType3]" Value="installs" Order="8">1</Publish>
      <Publish Dialog="ReadyToInstall" Control="InstallNow" Event="ReinstallMode" Value="[ReinstallModeText]" Order="9">_IsMaintenance="Reinstall"</Publish>
      <Publish Dialog="ReadyToInstall" Control="InstallNow" Event="Reinstall" Value="ALL" Order="10">_IsMaintenance="Reinstall"</Publish>


      <Publish Dialog="ReadyToRemove" Control="Back" Event="NewDialog" Value="MaintenanceType" Order="1">1</Publish>
      <Publish Dialog="ReadyToRemove" Control="Cancel" Event="SpawnDialog" Value="CancelSetup" Order="1">1</Publish>
      <Publish Dialog="ReadyToRemove" Control="RemoveNow" Event="Remove" Value="ALL" Order="1">1</Publish>
      <Publish Dialog="ReadyToRemove" Control="RemoveNow" Event="EndDialog" Value="Return" Order="2">OutOfNoRbDiskSpace &lt;&gt; 1</Publish>
      <Publish Dialog="ReadyToRemove" Control="RemoveNow" Event="NewDialog" Value="OutOfSpace" Order="2">OutOfNoRbDiskSpace=1</Publish>
      <Publish Dialog="ReadyToRemove" Control="RemoveNow" Event="[ProgressType1]" Value="Uninstalling" Order="3">1</Publish>
      <Publish Dialog="ReadyToRemove" Control="RemoveNow" Event="[ProgressType2]" Value="uninstalled" Order="4">1</Publish>
      <Publish Dialog="ReadyToRemove" Control="RemoveNow" Event="[ProgressType3]" Value="uninstalls" Order="5">1</Publish>


      <Publish Dialog="SetupProgress" Control="Cancel" Event="SpawnDialog" Value="CancelSetup" Order="0">1</Publish>


      <Publish Dialog="SetupCompleteError" Control="Back" Event="[Suspend]" Value="{}" Order="1">1</Publish>
      <Publish Dialog="SetupCompleteError" Control="Back" Event="EndDialog" Value="Return" Order="2">1</Publish>
      <Publish Dialog="SetupCompleteError" Control="Cancel" Event="[Suspend]" Value="1" Order="1">1</Publish>
      <Publish Dialog="SetupCompleteError" Control="Cancel" Event="EndDialog" Value="Return" Order="2">1</Publish>
      <Publish Dialog="SetupCompleteError" Control="Finish" Event="EndDialog" Value="Exit" Order="2">1</Publish>


      <Publish Dialog="SetupCompleteSuccess" Control="OK" Event="EndDialog" Value="Exit" Order="1">1</Publish>


      <Publish Dialog="SetupError" Control="Y" Event="EndDialog" Value="ErrorYes" Order="0">1</Publish>
      <Publish Dialog="SetupError" Control="N" Event="EndDialog" Value="ErrorNo" Order="0">1</Publish>
      <Publish Dialog="SetupError" Control="A" Event="EndDialog" Value="ErrorAbort" Order="1">1</Publish>
      <Publish Dialog="SetupError" Control="C" Event="EndDialog" Value="ErrorCancel" Order="0">1</Publish>
      <Publish Dialog="SetupError" Control="I" Event="EndDialog" Value="ErrorIgnore" Order="0">1</Publish>
      <Publish Dialog="SetupError" Control="O" Event="EndDialog" Value="ErrorOk" Order="1">1</Publish>
      <Publish Dialog="SetupError" Control="R" Event="EndDialog" Value="ErrorRetry" Order="0">1</Publish>


      <Publish Dialog="SetupInterrupted" Control="Back" Event="[Suspend]" Value="{}" Order="1">1</Publish>
      <Publish Dialog="SetupInterrupted" Control="Back" Event="EndDialog" Value="Exit" Order="2">1</Publish>
      <Publish Dialog="SetupInterrupted" Control="Cancel" Event="[Suspend]" Value="1" Order="1">1</Publish>
      <Publish Dialog="SetupInterrupted" Control="Cancel" Event="EndDialog" Value="Exit" Order="2">1</Publish>
      <Publish Dialog="SetupInterrupted" Control="Finish" Event="EndDialog" Value="Exit" Order="2">1</Publish>


      <Publish Dialog="SetupResume" Control="Cancel" Event="SpawnDialog" Value="CancelSetup" Order="0">1</Publish>
      <Publish Dialog="SetupResume" Control="Next" Event="EndDialog" Value="Return" Order="1">OutOfNoRbDiskSpace &lt;&gt; 1</Publish>
      <Publish Dialog="SetupResume" Control="Next" Event="NewDialog" Value="OutOfSpace" Order="2">OutOfNoRbDiskSpace=1</Publish>

      <!-- UIText Strings -->
      <UIText Id="AbsentPath"></UIText>
      <UIText Id="GB">!(loc.IDS_UITEXT_GB)</UIText>
      <UIText Id="KB">!(loc.IDS_UITEXT_KB)</UIText>
      <UIText Id="MB">!(loc.IDS_UITEXT_MB)</UIText>
      <UIText Id="MenuAbsent">!(loc.IDS_UITEXT_FeatureNotAvailable)</UIText>
      <UIText Id="MenuAdvertise">!(loc.IDS_UITEXT_FeatureInstalledWhenRequired2)</UIText>
      <UIText Id="MenuAllCD">!(loc.IDS_UITEXT_FeatureInstalledCD)</UIText>
      <UIText Id="MenuAllLocal">!(loc.IDS_UITEXT_FeatureInstalledLocal)</UIText>
      <UIText Id="MenuAllNetwork">!(loc.IDS_UITEXT_FeatureInstalledNetwork)</UIText>
      <UIText Id="MenuCD">!(loc.IDS_UITEXT_FeatureInstalledCD2)</UIText>
      <UIText Id="MenuLocal">!(loc.IDS_UITEXT_FeatureInstalledLocal2)</UIText>
      <UIText Id="MenuNetwork">!(loc.IDS_UITEXT_FeatureInstalledNetwork2)</UIText>
      <UIText Id="NewFolder">!(loc.IDS_UITEXT_Folder)</UIText>
      <UIText Id="SelAbsentAbsent">!(loc.IDS_UITEXT_GB)</UIText>
      <UIText Id="SelAbsentAdvertise">!(loc.IDS_UITEXT_FeatureInstalledWhenRequired)</UIText>
      <UIText Id="SelAbsentCD">!(loc.IDS_UITEXT_FeatureOnCD)</UIText>
      <UIText Id="SelAbsentLocal">!(loc.IDS_UITEXT_FeatureLocal)</UIText>
      <UIText Id="SelAbsentNetwork">!(loc.IDS_UITEXT_FeatureNetwork)</UIText>
      <UIText Id="SelAdvertiseAbsent">!(loc.IDS_UITEXT_FeatureUnavailable)</UIText>
      <UIText Id="SelAdvertiseAdvertise">!(loc.IDS_UITEXT_FeatureInstalledRequired)</UIText>
      <UIText Id="SelAdvertiseCD">!(loc.IDS_UITEXT_FeatureOnCD2)</UIText>
      <UIText Id="SelAdvertiseLocal">!(loc.IDS_UITEXT_FeatureLocal2)</UIText>
      <UIText Id="SelAdvertiseNetwork">!(loc.IDS_UITEXT_FeatureNetwork2)</UIText>
      <UIText Id="SelCDAbsent">!(loc.IDS_UITEXT_FeatureWillBeUninstalled)</UIText>
      <UIText Id="SelCDAdvertise">!(loc.IDS_UITEXT_FeatureWasCD)</UIText>
      <UIText Id="SelCDCD">!(loc.IDS_UITEXT_FeatureRunFromCD)</UIText>
      <UIText Id="SelCDLocal">!(loc.IDS_UITEXT_FeatureWasCDLocal)</UIText>
      <UIText Id="SelChildCostNeg">!(loc.IDS_UITEXT_FeatureFreeSpace)</UIText>
      <UIText Id="SelChildCostPos">!(loc.IDS_UITEXT_FeatureRequiredSpace)</UIText>
      <UIText Id="SelCostPending">!(loc.IDS_UITEXT_CompilingFeaturesCost)</UIText>
      <UIText Id="SelLocalAbsent">!(loc.IDS_UITEXT_FeatureCompletelyRemoved)</UIText>
      <UIText Id="SelLocalAdvertise">!(loc.IDS_UITEXT_FeatureRemovedUnlessRequired)</UIText>
      <UIText Id="SelLocalCD">!(loc.IDS_UITEXT_FeatureRemovedCD)</UIText>
      <UIText Id="SelLocalLocal">!(loc.IDS_UITEXT_FeatureRemainLocal)</UIText>
      <UIText Id="SelLocalNetwork">!(loc.IDS_UITEXT_FeatureRemoveNetwork)</UIText>
      <UIText Id="SelNetworkAbsent">!(loc.IDS_UITEXT_FeatureUninstallNoNetwork)</UIText>
      <UIText Id="SelNetworkAdvertise">!(loc.IDS_UITEXT_FeatureWasOnNetworkInstalled)</UIText>
      <UIText Id="SelNetworkLocal">!(loc.IDS_UITEXT_FeatureWasOnNetworkLocal)</UIText>
      <UIText Id="SelNetworkNetwork">!(loc.IDS_UITEXT_FeatureContinueNetwork)</UIText>
      <UIText Id="SelParentCostNegNeg">!(loc.IDS_UITEXT_FeatureSpaceFree)</UIText>
      <UIText Id="SelParentCostNegPos">!(loc.IDS_UITEXT_FeatureSpaceFree2)</UIText>
      <UIText Id="SelParentCostPosNeg">!(loc.IDS_UITEXT_FeatureSpaceFree3)</UIText>
      <UIText Id="SelParentCostPosPos">!(loc.IDS_UITEXT_FeatureSpaceFree4)</UIText>
      <UIText Id="TimeRemaining">!(loc.IDS_UITEXT_TimeRemaining)</UIText>
      <UIText Id="VolumeCostAvailable">!(loc.IDS_UITEXT_Available)</UIText>
      <UIText Id="VolumeCostDifference">!(loc.IDS_UITEXT_Differences)</UIText>
      <UIText Id="VolumeCostRequired">!(loc.IDS_UITEXT_Required)</UIText>
      <UIText Id="VolumeCostSize">!(loc.IDS_UITEXT_DiskSize)</UIText>
      <UIText Id="VolumeCostVolume">!(loc.IDS_UITEXT_Volume)</UIText>
      <UIText Id="bytes">!(loc.IDS_UITEXT_Bytes)</UIText>

   </UI>
</Include>
