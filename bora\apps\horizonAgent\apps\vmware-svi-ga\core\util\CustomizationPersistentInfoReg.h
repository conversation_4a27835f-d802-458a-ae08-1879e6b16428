/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/**
 * @file CustomizationPersistentInfoReg.h --
 * Defines the guest customization related information persisted in registry.
 */

#pragma once

#include "InstantClonesErrors.h"

#include <common/windows/singleton/Singleton.h>

namespace svmga {
namespace core {
namespace util {

namespace reg = svmga::common::windows::registry;

/*!
 * Provides all guest customiation related persistent information stored
 * in registry. All the information will be persisted under registry key:
 * [HKLM]\<HORIZON_REG_ROOT>\Instant Clone Agent\ga for guest agent and
 * [HKLM]\<HORIZON_REG_ROOT>\Instant Clone Agent\nga for native agent.
 */
class CustomizationPersistentInfoReg : public CustomizationPersistentInfo {

   friend class CustomizationPersistentInfo;

public:
   CustomizationPersistentInfoReg();

   virtual void FlushKeys();

   virtual bool IsUniversalPrep();

   //
   // Horizon Agent Funcitons
   //
   virtual bool IsPoolIdSet();
   virtual std::wstring GetOperationType();
   virtual bool IsMachinePwdSet();
   virtual void ClearMachinePwd();

   //
   // Fork Functions
   //
   virtual bool IsVmForked();
   virtual void MarkVmForked();
   virtual void ClearVmForked();

   //
   // Sysprep Functions
   //
   virtual bool IsSysprepCompleted();
   virtual bool IsFastRefreshCompleted();
   virtual bool IsSysprepGeneralizeCompleted();
   virtual bool IsSysprepSpecializeCompleted();
   virtual bool IsSysprepOobeCompleted();
   virtual void MarkSysprepCompleted();
   virtual void MarkFastRefreshCompleted();
   virtual void MarkSysprepGeneralizeCompleted();
   virtual void MarkSysprepSpecializeCompleted();
   virtual void MarkSysprepOobeCompleted();

   //
   // Domain Join Functions
   //
   virtual bool IsTemplateDomainJoined();
   virtual bool IsCloneDomainJoined();
   virtual bool IsCloneDomainJoinedWithNetJoinDomain();
   virtual void MarkTemplateDomainJoined();
   virtual void MarkCloneDomainJoined();
   virtual void MarkCloneDomainJoinedWithNetJoinDomain();
   virtual void SetDomainJoinRebootCount(int iCount);
   virtual int GetDomainJoinRebootCount();

   virtual bool IsPreVerifyTrustRebootEnabled();
   virtual void IncrementPreVerifyTrustReboots();
   virtual int GetPreVerifyTrustReboots();
   virtual int GetMaxPreVerifyTrustReboots();

   virtual bool IsVerifyTrustFailedRebootEnabled();
   virtual void IncrementVerifyTrustFailedReboots();
   virtual int GetVerifyTrustFailedReboots();
   virtual int GetMaxVerifyTrustFailedReboots();

   virtual bool IsNetJoinDomainEnabled();
   virtual void IncrementNetJoinDomainAttempts();
   virtual int GetNetJoinDomainAttempts();
   virtual int GetMaxNetJoinDomainAttempts();

   virtual bool SkipCloneprepDomainJoin();

   virtual DWORD GetPostNetlogonStartDelay();

   //
   // Machine Password Functions
   //
   virtual bool IsTemplateMachinePasswordChanged();
   virtual bool IsCloneMachinePasswordChanged();
   virtual void MarkTemplateMachinePasswordChanged();
   virtual void MarkCloneMachinePasswordChanged();
   virtual bool IsTemplateMachinePwdChangeEnabled();
   virtual bool IsCloneMachinePwdChangeEnabled();
   virtual bool IsSecureMachinePwdInfoDisabled();
   virtual bool IsMachinePwdInfoSecured();
   virtual bool MarkMachinePwdInfoSecured();
   virtual bool ClearSecuredMachinePwdInfo();

   //
   // Rename Functions
   //
   virtual bool IsTemplateRenamed();
   virtual bool IsParentRenamed();
   virtual bool IsCloneRenamed();
   virtual void MarkTemplateRenamed();
   virtual void MarkParentRenamed();
   virtual void MarkCloneRenamed();

   //
   // Reboot Functions
   //
   virtual bool IsCloneRebooted();
   virtual bool IsTemplateRebooted();
   virtual bool IsReplicaRebooted();

   virtual void MarkCloneRebooted();
   virtual void MarkTemplateRebooted();
   virtual void MarkReplicaRebooted();

   //
   // Shutdown Functions
   //
   virtual bool IsTemplateShutdownNeeded();
   virtual void MarkTemplateShutdownNeeded();
   virtual void MarkTemplateShutdownDone();
   virtual bool IsCloneShutdownNeeded();
   virtual void MarkCloneShutdownNeeded();
   virtual void MarkCloneShutdownDone();

   //
   // Mac Address Functions
   //
   virtual bool IsMacAddressReset();
   virtual void MarkMacAddressReset();
   virtual std::wstring GetGoldenImageMacAddress();
   virtual void MarkGoldenImageMacAddress(const std::wstring &value);

   //
   // IP Functions
   //
   virtual int GetIpReleaseOnShutdownState();
   virtual bool IsIpRenewed();
   virtual bool MarkIpRenewed();
   virtual bool ClearIpRenewed();

   //
   // IPv6 Functions
   //
   virtual bool IsIPv6SupportEnabled();

   //
   // DHCP Fix Functions
   //
   virtual bool DisableDhcpService();

   //
   // GPUpdate Functions
   //
   virtual bool IsGPUpdateEnabledOnClone();
   virtual bool IsGPUpdateEnabledOnIT();

   //
   // Script Functions
   //
   virtual bool IsPostCustScriptDisabled();
   virtual bool AreScriptsSecured();
   virtual void MarkScriptsSecured();
   virtual bool IsPostCustScriptSecured();
   virtual bool IsPreShutdownScriptSecured();
   virtual void MarkPostCustScriptSecured();
   virtual void MarkPreShutdownScriptSecured();
   virtual bool IsPostCustScriptConfigured();
   virtual bool IsPreShutdownScriptConfigured();
   virtual void MarkPostCustScriptConfigured();
   virtual void MarkPreShutdownScriptConfigured();
   virtual bool IsLogScriptOutputEnabled();

   //
   // License Functions
   //
   virtual bool IsLicenseActivationEnabled(bool bSysprep);
   virtual bool IsLicenseActivated();
   virtual void MarkLicenseActivated();
   virtual bool IsLicenseRearmEnabled();
   virtual bool IsLicenseRearmCompleted();
   virtual void MarkLicenseRearmCompleted();

   //
   // Internal Template Customatization Functions
   //
   virtual bool IsTemplateCustomizationNeeded();
   virtual void MarkTemplateCustomizationNeeded();
   virtual void MarkTemplateCustomizationDone();
   virtual bool IsTemplateCustomizationDone();
   virtual bool IsITDomainJoinFinished();
   virtual void SetITDomainJoinFinished();
   virtual bool IsITCustomizationStartTimeSet();
   virtual void MarkITCustomizationStartTimeSet();

   //
   // Clone Customization Functions
   //
   virtual bool IsCloneCustomizationCompleted();
   virtual void MarkCloneCustomizationCompleted();

   //
   // Clone Customization Functions
   //
   virtual bool IsReplicaCustomizationCompleted();
   virtual void MarkReplicaCustomizationCompleted();

   //
   // Pre/Post Intregration Values
   //
   virtual bool PreIntegrationValuesParsed();
   virtual void MarkPreIntegrationValuesParsed();
   virtual bool PostIntegrationValuesParsed();
   virtual void MarkPostIntegrationValuesParsed();

   //
   // Profile Redirection
   //
   virtual bool AreProfilesRedirected();
   virtual void MarkProfilesRedirected();

   //
   // Persistent Disks Ready
   //
   virtual bool ArePersistentDisksReady();
   virtual void MarkPersistentDisksReady();

   //
   // NL Flags
   //
   virtual bool IsNLFlagEnabled();

   //
   // Service Notify Values
   //
   virtual bool IsServiceNotifyEnabled();
   virtual DWORD ServiceNotifyGetMaxRestarts(DWORD dwDefault);
   virtual DWORD ServiceNotifyGetStartDelay(DWORD dwDefault);
   virtual DWORD ServiceGetMaxRetries(DWORD dwDefault);
   virtual DWORD ServiceGetRetryDelay(DWORD dwDefault);
   virtual DWORD GetServiceShutdownDelay(DWORD dwDefault);
   virtual void SetCustomizationResult(SvmPolicyState psState, NotifyVdmStatusValue vsValue,
                                       bool force = false);

   //
   // All guestInfo for vmx backdoor we move to regkey
   //
   virtual std::wstring GetGuestInfoValue(const std::wstring &entry);
   virtual void SetGuestInfoValue(const std::wstring &entry, const std::wstring &value);

private:
   /*
    * Validate and create if needed all parent registry keys.
    */
   bool ValidateParentKeys();

private:
   // Constant to allow this 32-bit agent access 64 bit registry keys.
   static const unsigned long WOW64_ACCESS_64KEY;

   // Constants below are related to parent keys for all the registry values.
   static const std::wstring SoftwareRegistryKeyName;
   static const std::wstring CompanyRegistryKeyName;
   static const std::wstring SuiteRegistryKeyName;
   static const std::wstring ProductRegistryKeyName;
   static const std::wstring ProductRegistryKeyPath;
   static const std::wstring GuestAgentRegistryKeyName;
   static const std::wstring NativeAgentRegistryKeyName;
   static const std::wstring GuestAgentRegistryKeyPath;
   static const std::wstring NativeAgentRegistryKeyPath;

   // Indicate this is a master vm/golden image (not yet copied/forked)
   static const std::wstring GoldenImageValueName;

   // Horizon Agent Config values
   static const std::wstring PoolIdValueName;
   static const std::wstring OperationTypeValueName;
   static const std::wstring MachinePwdValueName;

   //
   // Constants below define all the registry values used to
   // persist customization info.
   //
   static const std::wstring VmForkedValueName;
   static const std::wstring TemplateCustomizationValueName;

   //
   // Sysprep Values
   //
   static const std::wstring SysprepCompletedValueName;
   static const std::wstring SysprepGeneralizeCompletedValueName;
   static const std::wstring SysprepSpecializeCompletedValueName;
   static const std::wstring SysprepOobeCompletedValueName;
   static const std::wstring FastRefreshCompletedValueName;

   //
   // Domain Join Values
   //
   static const std::wstring TemplateDomainJoinedValueName;
   static const std::wstring ParentDomainJoinedValueName;
   static const std::wstring CloneDomainJoinedValueName;
   static const std::wstring CloneDomainJoinedWithNetJoinDomainValueName;
   static const std::wstring DomainJoinRebootCountValueName;

   static const std::wstring PreVerifyTrustRebootsValueName;
   static const std::wstring MaxPreVerifyTrustRebootsValueName;

   static const std::wstring VerifyTrustFailedRebootsValueName;
   static const std::wstring MaxVerifyTrustFailedRebootsValueName;

   static const std::wstring NetJoinDomainAttemptsValueName;
   static const std::wstring MaxNetJoinDomainAttemptsValueName;

   static const std::wstring SkipCloneprepDomainJoinValueName;

   static const std::wstring PostNetlogonStartDelayValueName;

   //
   // Machine Password Values
   //
   static const std::wstring TemplateMachinePasswordChangedValueName;
   static const std::wstring CloneMachinePasswordChangedValueName;
   static const std::wstring TemplateMachinePwdChangeEnabledValueName;
   static const std::wstring CloneMachinePwdChangeEnabledValueName;
   static const std::wstring MachinePwdInfoSecuredValueName;
   static const std::wstring SecureMachinePwdInfoDisabledValueName;

   //
   // Rebooted Values
   //
   static const std::wstring CloneRebootedValueName;
   static const std::wstring TemplateRebootedValueName;
   static const std::wstring ReplicaRebootedValueName;

   //
   // Shutdown Values
   //
   static const std::wstring TemplateShutdownValueName;
   static const std::wstring CloneShutdownValueName;

   //
   // Renamed Values
   //
   static const std::wstring TemplateRenamedValueName;
   static const std::wstring ParentRenamedValueName;
   static const std::wstring CloneRenamedValueName;

   //
   // Mac Address Values
   //
   static const std::wstring MacAddressResetValueName;
   static const std::wstring GoldenImageMacAddressValueName;

   //
   // IP Values
   //
   static const std::wstring IpReleaseOnShutdownValueName;
   static const std::wstring IpRenewedValueName;

   //
   // IPv6 Values
   //
   static const std::wstring IPv6SupportEnabledValueName;

   //
   // DHCP Fix Value
   //
   static const std::wstring DisableDhcpServiceValueName;

   //
   // GPUpdate Values
   //
   static const std::wstring GPUpdateEnabledOnCloneValueName;
   static const std::wstring GPUpdateEnabledOnITValueName;

   //
   // Script Values
   //
   static const std::wstring PostCustScriptDisabledValueName;
   static const std::wstring ScriptsSecuredValueName;
   static const std::wstring PostCustScriptSecuredValueName;
   static const std::wstring PreShutdownScriptSecuredValueName;
   static const std::wstring PostCustScriptConfiguredValueName;
   static const std::wstring PreShutdownScriptConfiguredValueName;
   static const std::wstring ScriptLogOutputValueName;

   //
   // License Activation Values
   //
   static const std::wstring LicenseActivationEnabledValueName;
   static const std::wstring LicenseActivationEnabledForSysprepValueName;
   static const std::wstring LicenseActivatedValueName;
   static const std::wstring LicenseRearmEnabledValueName;
   static const std::wstring LicenseRearmCompletedValueName;

   //
   // Clone Customization Values
   //
   static const std::wstring CloneCustomizationCompletedValueName;

   //
   // IT Customization Values
   //
   static const std::wstring TemplateDomainJoinFinishedValueName;
   static const std::wstring ITCustomizationStartTimeSetValueName;

   //
   // Replica Customization Values
   //
   static const std::wstring ReplicaCustomizationCompletedValueName;

   //
   // Pre/Post Integration Values
   //
   static const std::wstring PreIntegrationValuesParsedValueName;
   static const std::wstring PostIntegrationValuesParsedValueName;

   //
   // Profile Redirection
   //
   static const std::wstring ProfilesRedirectedValueName;

   //
   // Persistent Disk Ready
   //
   static const std::wstring PersistentDisksReadyValueName;

   //
   // NL Flags
   //
   static const std::wstring NLFlagsDisabledValueName;

   //
   // Service Notify Values
   //
   static const std::wstring ServiceNotifyEnabledValueName;
   static const std::wstring ServiceNotifyMaxRestartsValueName;
   static const std::wstring ServiceNotifyStartDelayValueName;

   static const std::wstring ServiceMaxRetriesValueName;
   static const std::wstring ServiceRetryDelayValueName;

   static const std::wstring ServiceShutdownDelayValueName;

   Registry *_Registry;
};

} // namespace util
} // namespace core
} // namespace svmga
