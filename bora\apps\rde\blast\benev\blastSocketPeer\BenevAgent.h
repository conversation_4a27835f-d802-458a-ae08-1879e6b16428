/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * BenevAgent.h
 */

#include "BenevPeer.h"
#include "BenevProxy.h"

class BenevAgent : public BenevPeer {
public:
   BenevAgent(bool useController) : BenevPeer("server", useController)
   {
      mController.SetWritable(useController);
   }

   virtual void Shutdown();
   virtual void StartConnection(const StartConnectionParams &startParams);
   virtual void StopBlastSocketsAndVvc(VDPConnectionResult closeReason);
   virtual void CloseConnection(const string &reqId);
   virtual void ExitConnection();
   virtual void SetupVAuth(const string &vAuth);
   virtual void CreateSSLManager(const string &certFilePathName = "", bool checkRevocation = false,
                                 bool enableCrlCache = false);

   void vvcOnChannelOpen(VvcChannelHandle channel, VvcStatus status, uint8 *initialData,
                         size_t initialDataLen);

   void vvcOnListenerConnectCb(char *name, VvcListenerHandle listenerHandle, void *connectionCookie,
                               uint32 connectionCaps, int32 sessionId);

   void vvcChannelOnRecv(VvcChannelHandle channel, uint8 *buf, size_t len);

   void vvcOnChannelClose(VvcChannelHandle channel, VvcCloseChannelReason reason);

   Bool blastSocketConnectionAcceptCB(const char *vauth, int32 vvcSessionID);

   void blastSocketConnectionSocketError(int32 vvcSessionID);

   void setTransportSwitchPolicy(const char *vAuth);
   void setVvcDeferredAcksParams(const char *vAuth);

   bool IsRawChannelEnabled() const { return mEnableRawChannel; }

private:
#ifdef _WIN32
   bool AddConnectionToSessionMonitor();
#endif

   static void BlastSocketConnectionOnClose(const char *vauth, int32 sessionId, Bool isError,
                                            VDPConnectionResult closeReason, void *clientData);

   BlastSocketContext *mAgentBlastSocketContext = NULL;
   bool mIsSocketClosed = false;
   bool mCloseRequested = false;
   bool mEnableRawChannel = false;
   // Requesting raw channels does not guarantee raw channels
   bool mRawChannelStatus = false;
};
