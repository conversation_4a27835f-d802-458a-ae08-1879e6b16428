# Copyright (c) Omnissa, LLC. All rights reserved.
# This product is protected by copyright and intellectual property laws in the
# United States and other countries as well as by international treaties.
# -- Omnissa Restricted

"""vncRegEncPerf: VNC region encoder performance testing tool

vncRegEncPerf is a test application that exercises the VNC region
encoders via the VNCRegionEncoder interface.

vncRegEncPerf sends every frame from an vncServer replay to the region
encoder.  Unlike vncPerf, vncRegEncPerf does not depend on frame
timings or use VNCEncode's frame scheduler.  Therefore its output
is intended to be deterministic and useful for testing and profiling.

Maintained by the Blast display team:
   1. <EMAIL>
   2. Reviewboard groups: appblast
"""

import vmware

target = "vncRegEncPerf"
env = vmware.DefaultEnvironment()

env.LoadTool("nvidia_sdk")

vmware.LoadTool(
    env,
    [
        "libssl",
        "ffmpeg",
        "libjpeg",
        "libogg",
        "libopus",
        "libpng",
        "libspeexdsp",
        "libx264",
        "libyuv",
        "libz",
        "protobuf-c-3",
        "vm-product",
    ],
)

env.Append(
    CPPDEFINES={
        "USERLEVEL": None,
        # We must disable debug msg because we are compiling lib/vnc as part of
        # this build rather than using vmlibs, which leads to a conflict in the
        # protobuf generated headers.
        "VNC_DEBUG_MSG_DISABLED": None,
    },
    CPPPATH=[
        "#bora/apps/cedar/include",
        "#bora/apps/rde/blast/vncRegEncPerf/override",
        "#bora/apps/rde/blast/vncReplay/include",
        "#bora/apps/rde/vncServer",
        "#bora/lib/public",
        "#bora/lib/vnc",
        "#bora/lib/blastCodec",
        "#bora/lib/vncConnectionManager",
        "#bora/lib/vncConnectionManager/encode",
        "#bora/public",
        vmware.HeaderDirectory("vnc"),
    ],
)

if vmware.Host().IsWindows():
    env.LoadTool("amd_rapidfire_sdk", dllName="RapidFire")
    vmware.LoadTool(
        env,
        [
            "atlmfc",
            "intel_sdk",
            "msvcrt",
        ],
    )

    env.Append(
        CPPDEFINES={"_UNICODE": None, "UNICODE": None},
        CPPPATH=[
            "#bora-vmsoft/svga/hznvidd",
            "#bora-vmsoft/svga/vdisplay",
            "#bora-vmsoft/svga/wddm/include",
            "#bora/lib/vncConnectionManager/win32",
            "#bora/lib/vncConnectionManager/win32/capture",
            "#bora/lib/vncConnectionManager/win32/topology",
        ],
        LINKFLAGS=[
            "-delayload:d3d11.dll",
            "-delayload:dxgi.dll",
            "-subsystem:console",
        ],
        LIBS=[
            "advapi32.lib",
            "Bcrypt.lib",
            "Cfgmgr32.lib",
            "crypt32.lib",
            "d3dcompiler.lib",
            "d3d11.lib",
            "d3d9.lib",
            "delayimp.lib",
            "dxgi.lib",
            "dxguid.lib",
            "dxva2.lib",
            "dwmapi.lib",
            "gdi32.lib",
            "iphlpapi.lib",
            "kernel32.lib",
            "legacy_stdio_definitions.lib",
            "legacy_stdio_wide_specifiers.lib",
            "magnification.lib",
            "nvapi64.lib" if vmware.Host().Is64Bit() else "nvapi.lib",
            "oldnames.lib",
            "ole32.lib",
            "oleaut32.lib",
            "psapi.lib",
            "qwave.lib",
            "setupapi.lib",
            "shell32.lib",
            "user32.lib",
            "uuid.lib",
            "wbemuuid.lib",
            "winmm.lib",
            "ws2_32.lib",
            "wtsapi32.lib",
            "yuv.lib",
        ],
    )

if vmware.Host().IsLinux():
    vmware.LoadTool(
        env,
        [
            "cvt",
            "xorg",
        ],
    )

    env.Append(
        STATICLIBS=[
            "jpeg",
            "ogg",
            "opus",
            "speexdsp",
            "yuv",
        ],
        LIBS=[
            "dl",
            "X11",
            "Xcursor",
            "Xdamage",
            "Xext",
            "Xfixes",
            "Xi",
            "Xinerama",
            "xkbfile",
            "Xrandr",
            "Xtst",
            "z",
        ],
    )

vncRegEncPerfSubdirs = [
    "apps/rde/blast/vncRegEncPerf",
    # These must be built as part of vncRegEncPerf and not from vmlibs, because
    # we override VNCUtil_SystemTime from the vncUtil.h header in order to
    # inject virtual time stamps into the encoder.
    "lib/vnc",
    "lib/blastCodec",
    "lib/vncConnectionManager",
]

e = vmware.Executable(target, env=env)
vncLibs = [
    "asyncsocket",
    "blastControl",
    "cityhash",
    "config",
    "coreDump",
    "crypto",
    "d3des",
    "dict",
    "err",
    "file",
    "hashMap",
    "image",
    "keyboard",
    "lfqueue",
    "lock",
    "log",
    "mempool",
    "misc",
    "panic",
    "panicDefault",
    "poll",
    "pollDefault",
    "productState",
    "raster",
    "rbtree",
    "rectangle",
    "region",
    "sig",
    "slab",
    "sound",
    "soundlib",
    "ssl",
    "string",
    "thread",
    "udpfec",
    "unicode",
    "user",
    "uuid",
    "vvclib",
    "version",
]

if vmware.Host().IsWindows():
    vncLibs += [
        "win32cfgmgr",
        "kbdlayoutid",
        "rdsutils",
        "sslRemap",
        "uuid",
        "win32auth",
        "win32tsf",
        "wmi",
    ]
    vncRegEncPerfSubdirs += [
        "lib/vnc/win32",
    ]

if vmware.Host().IsLinux():
    vncLibs += [
        "win32tsf",
    ]

e.addStaticLibs("vmlibs", vncLibs)
e.addSubdirs(vncRegEncPerfSubdirs)
e.addGlobalStaticLibs(
    [
        "vncNvEncSDK8",
        "vncNvEncSDK12",
        "vncReplay",
        "vncReplayHardware",
    ]
)

node = e.createProgramNode()
vmware.RegisterNode(node, target)

# Stage redistributables like libx264 when building vncRegEncPerf.
for n in env.get("REDIST") or []:
    vmware.RegisterNode([File(n)], target)

# libstdc++ is not a normal, explicit shared lib of vncRegEncPerf.
# but we must stage it on linux. On windows the C++ runtime can be
# installed manually as a pre-requisite.
if vmware.Host().IsLinux():
    vmware.RegisterNode([env["LIBSTDCXX_REDIST"]], target)

vmware.RegisterEnv("%s-env" % target, env)
vmware.Alias("%s-build" % target, node)
