/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

#include <algorithm>
#include <string>
#include <sstream>

#include "userlock.h"
#include "vm_assert.h"
#include "util.h"
#include "hostinfo.h"

#include "BenevProxyConnection.h"
#include "BenevTCPSocket.h"
#include "BenevVVCSocket.h"

using namespace std;


const char *BenevProxyConnection::sClassName = "BenevProxyConnection";

static void checkBytesPerSecondCb(void *clientData);


/*
 *----------------------------------------------------------------------
 *
 * BenevProxyConnection::BenevProxyConnection --
 *
 *      Construct a BenevProxyConnection object. 'downSock' is the newly
 *      accepted downstream socket. 'upSockType' and 'upSockParams' indicate the
 *      upstream socket's type (VVC or TCP) and connect params.
 *
 * Results:
 *      The BenevProxyConnection object.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------
 */

BenevProxyConnection::BenevProxyConnection(uint32 connId, BenevSocket *downSock,
                                           SocketType upSockType, const SocketParam &upSockParams) :
   mConnId(connId),
   mLock(NULL),
   mState(Connecting),
   mDataInTransit(false),
   mUpSockType(upSockType),
   mUpSockParams(upSockParams),
   mDownSock(downSock),
   mDownSockBufLen(sizeof(mDownSockBuf)),
   mDownSockReceivedBytes(0),
   mDownSockSentBytes(0),
   mDownBytesSentThisSecond(0),
   mDownSockBytesSentPerSecond({}),
   mUpSock(NULL),
   mUpSockReceivedBytes(0),
   mUpSockSentBytes(0),
   mUpBytesSentThisSecond(0),
   mUpSockBytesSentPerSecond({}),
   mUpSendStartTime(0),
   mUpLastSentTime(0),
   mUpAvgBw(0.0)
{
   mLock = MXUser_CreateExclLock("BenevProxyConnectionLock", RANK_UNRANKED);

   HorizonStatus status =
      Poll_Callback(POLL_CS_MAIN, POLL_FLAG_PERIODIC, checkBytesPerSecondCb, this, POLL_REALTIME,
                    1000 * 1000, // Fires every second
                    NULL);
   if (status != HORIZON_STATUS_SUCCESS) {
      BenevLog("Failed to start bandwidth tracking poll callback\n");
   }

   // Set errorCb for downSock
   downSock->setErrorCb(socketOnErrorCb, this);
}


/*
 *----------------------------------------------------------------------
 *
 * BenevProxyConnection::~BenevProxyConnection --
 *
 *      Destructor of the proxy conn object.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------
 */

BenevProxyConnection::~BenevProxyConnection()
{
   MXUser_DestroyExclLock(mLock);
}


/*
 *----------------------------------------------------------------------
 *
 * BenevProxyConnection::start --
 *
 *      Connect to upstream to start the proxy conn.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------
 */

void
BenevProxyConnection::start()
{
   BenevLog("starting...\n");

   VERIFY(this->connectToUpstream());
}


/*
 *----------------------------------------------------------------------
 *
 * BenevProxyConnection::stop --
 *
 *      Stop the proxy conn by shuting down both sockets. Taking care not to
 *      lose in-transit data, if any.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------
 */

std::map<std::string, std::string>
BenevProxyConnection::stop()
{
   std::map<std::string, std::string> stopSummary = {};
   BenevLog("Conn %d: stopping...\n", getConnId());

   MXUser_AcquireExclLock(mLock);

   stopSummary.insert(make_pair("avgBw", std::to_string(mUpAvgBw)));
   if (mUpSockType == VVCSocket) {
      stopSummary.insert({"rawChannel", mRawChannelStatus ? "true" : "false"});
   }

   if (mState == Closing || mState == Closed) {
      MXUser_ReleaseExclLock(mLock);
      return stopSummary;
   }

   mState = Closing;

   bool closeSocketsNow = !mDataInTransit;

   MXUser_ReleaseExclLock(mLock);

   if (closeSocketsNow) {
      closeSockets();
   } else {
      // delay closeSockets until data is sent
      BenevLog("Conn %d: delay closeSockets until in-transit data is sent\n", getConnId());
   }

   return stopSummary;
}


/*
 *----------------------------------------------------------------------
 *
 * BenevProxyConnection::closeSockets --
 *
 *      Close both up and down stream sockets.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------
 */

void
BenevProxyConnection::closeSockets()
{
   BenevLog("Conn %d\n", getConnId());

   MXUser_AcquireExclLock(mLock);

   ASSERT(mState == Closing);
   ASSERT(!mDataInTransit);

   BenevSocket *upSock = mUpSock;
   mUpSock = NULL;
   BenevSocket *dnSock = mDownSock;
   mDownSock = NULL;

   bool status = Poll_CallbackRemove(POLL_CS_MAIN, POLL_FLAG_PERIODIC, checkBytesPerSecondCb, this,
                                     POLL_REALTIME);
   if (status != true) {
      BenevLog("Failed to remove bandwidth poll callback");
   }

   if (mDownBytesSentThisSecond > 0) {
      mDownSockBytesSentPerSecond.push_back(mDownBytesSentThisSecond);
      mDownBytesSentThisSecond = 0;
   }
   if (mUpBytesSentThisSecond > 0) {
      mUpSockBytesSentPerSecond.push_back(mUpBytesSentThisSecond);
      mUpBytesSentThisSecond = 0;
   }

   // Stringify the arrays
   std::string downBWPS, upBWPS;
   std::for_each(mDownSockBytesSentPerSecond.begin(), mDownSockBytesSentPerSecond.end(),
                 [&downBWPS](const int &i) { downBWPS.append(std::to_string(i) + ','); });
   std::for_each(mUpSockBytesSentPerSecond.begin(), mUpSockBytesSentPerSecond.end(),
                 [&upBWPS](const int &i) { upBWPS.append(std::to_string(i) + ','); });

   // Calculate avg up send bandwidth so long as something was actually sent
   if (mUpLastSentTime != 0) {
      uint64 delta = mUpLastSentTime - mUpSendStartTime;
      double deltaSec = delta / 1000000.0;
      mUpAvgBw = mUpSockSentBytes / deltaSec;
   }

   BenevLog("Conn %d: downRecvd %" FMT64 "u, downSent %" FMT64 "u, "
            "upRecvd %" FMT64 "u, upSent %" FMT64 "u, upAvgBw %lf\n",
            getConnId(), mDownSockReceivedBytes, mDownSockSentBytes, mUpSockReceivedBytes,
            mUpSockSentBytes, mUpAvgBw);

   BenevLog("DownSent bytes per second: %s\n", downBWPS.c_str());
   BenevLog("UpSent bytes per second: %s\n", upBWPS.c_str());

   mState = Closed;

   MXUser_ReleaseExclLock(mLock);

   if (upSock) {
      upSock->closeSocket();
      delete upSock;
   }

   ASSERT(dnSock);
   dnSock->closeSocket();
   delete dnSock;
}


/*
 *----------------------------------------------------------------------
 *
 * BenevProxyConnection::checkBytesPerSecond --
 *
 *      Track how many bytes were sent upstream and downstream in one
 *      second.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------
 */

static void
checkBytesPerSecondCb(void *clientData)
{
   BenevProxyConnection *bpc = (BenevProxyConnection *)clientData;
   bpc->checkBytesPerSecond();
}

void
BenevProxyConnection::checkBytesPerSecond()
{
   std::lock_guard<std::mutex> guard(mLockGuard);
   mUpSockBytesSentPerSecond.push_back(mUpBytesSentThisSecond);
   mUpBytesSentThisSecond = 0;

   mDownSockBytesSentPerSecond.push_back(mDownBytesSentThisSecond);
   mDownBytesSentThisSecond = 0;
}


/*
 *----------------------------------------------------------------------
 *
 * BenevProxyConnection::connectToUpstream --
 *
 *      Connect to upstream destination by calling the right connectTo function
 *      for the socket type.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------
 */

bool
BenevProxyConnection::connectToUpstream()
{
   BenevLog("ConnId %d\n", getConnId());

   BenevSocket *socket = nullptr;

   if (mUpSockType == TCPSocket) {
      socket = BenevTCPSocket::connectTo(mUpSockParams.tcp.host, mUpSockParams.tcp.port,
                                         upstreamOnConnectCb, // connectCb
                                         this,                // connectCbData
                                         NULL,                // errorCb
                                         this);               // errorCbData
   } else if (mUpSockType == VVCSocket) {
      socket = BenevVVCSocket::connectTo(
         mUpSockParams.vvc.channelName, mUpSockParams.vvc.featureName, mUpSockParams.vvc.sessionId,
         upstreamOnConnectCb, this, // connectCb/connectCbData
         socketOnErrorCb, this,     // errorCb/errorCbData
         mUpSockParams.vvc.channelPriority, mUpSockParams.vvc.channelFlags);
   } else {
      ASSERT(FALSE);
   }

   MXUser_AcquireExclLock(mLock);
   mUpSock = socket;
   MXUser_ReleaseExclLock(mLock);

   return (socket != nullptr);
}


/*
 *----------------------------------------------------------------------
 *
 * BenevProxyConnection::upstreamOnConnectCb --
 *
 *      The OnConnect callback for the upstream socket.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------
 */

void
BenevProxyConnection::upstreamOnConnectCb(BenevSocket *socket, void *cbData)
{
   BenevLog("==>\n");

   BenevProxyConnection *connPtr = reinterpret_cast<BenevProxyConnection *>(cbData);

   MXUser_AcquireExclLock(connPtr->mLock);
   if (!connPtr->mUpSock) {
      connPtr->mUpSock = socket;
   }

   if (connPtr->mUpSockType == VVCSocket) {
      BenevVVCSocket *upSock = (BenevVVCSocket *)connPtr->mUpSock;
      connPtr->mRawChannelStatus = upSock->getRawChannelStatus();

      BenevLog("Raw Channel status updated: %s\n", connPtr->mRawChannelStatus ? "true" : "false");
   }
   MXUser_ReleaseExclLock(connPtr->mLock);

   connPtr->upstreamConnected();
}


/*
 *----------------------------------------------------------------------
 *
 * BenevProxyConnection::upstreamConnected --
 *
 *      Upstream socket is connected. Start receiving on both sockets.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------
 */

void
BenevProxyConnection::upstreamConnected()
{
   BenevLog("conn %d\n", this->getConnId());

   MXUser_AcquireExclLock(mLock);
   mState = Connected;
   MXUser_ReleaseExclLock(mLock);

   // Start receiving on both socket
   mDownSock->recvData(mDownSockBuf, sizeof(mDownSockBuf), socketOnRecvCb, this);
   mUpSock->recvData(mUpSockBuf, sizeof(mUpSockBuf), socketOnRecvCb, this);
}


/*
 *----------------------------------------------------------------------
 *
 * BenevProxyConnection::socketOnRecvCb --
 *
 *      The receive callback for the sockets.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------
 */

void
BenevProxyConnection::socketOnRecvCb(void *buf, int bufLen, BenevSocket *socket, void *cbData)
{
   BenevTrace("buf %p, bufLen %d, socket %d\n", buf, bufLen, socket->getId());

   BenevProxyConnection *connPtr = reinterpret_cast<BenevProxyConnection *>(cbData);

   connPtr->dataReceived(socket, buf, bufLen);
}


/*
 *----------------------------------------------------------------------
 *
 * BenevProxyConnection::socketOnErrorCb --
 *
 *      The error callback for the sockets.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------
 */

void
BenevProxyConnection::socketOnErrorCb(int err, BenevSocket *socket, void *cbData)
{
   BenevProxyConnection *connPtr = reinterpret_cast<BenevProxyConnection *>(cbData);

   BenevLog("%s socket %d got error %d\n", socket == connPtr->mUpSock ? "Upstream" : "Downstream",
            socket->getId(), err);

   connPtr->stop();
}


/*
 *----------------------------------------------------------------------
 *
 * BenevProxyConnection::dataReceived --
 *
 *      Received some data from the (upstream|downstream) socket. Send the data
 *      out of the other socket.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------
 */

void
BenevProxyConnection::dataReceived(BenevSocket *srcSock, void *buf, int bufLen)
{
   BenevSocket *dstSock = NULL;
   string dataDirection;

   // decide where to send out the data
   if (srcSock == mDownSock) {
      ASSERT(buf == mDownSockBuf);
      dstSock = mUpSock;
      dataDirection = "upstream";
      mDownSockReceivedBytes += bufLen;
      if (mUpSendStartTime == 0) {
         // Start tracking transfer time as we're about to do the first send
         mUpSendStartTime = Hostinfo_SystemTimerUS();
      }
   } else if (srcSock == mUpSock) {
      ASSERT(buf == mUpSockBuf);
      dstSock = mDownSock;
      dataDirection = "downstream";
      mUpSockReceivedBytes += bufLen;
   } else {
      ASSERT(FALSE);
   }

   BenevTrace("forwarding %d bytes %s from socket %d to socket %d\n", bufLen, dataDirection.c_str(),
              srcSock->getId(), dstSock->getId());

   // send out the data
   MXUser_AcquireExclLock(mLock);
   mDataInTransit = true;
   MXUser_ReleaseExclLock(mLock);
   dstSock->sendData(buf, bufLen, socketOnSentCb, this);
}


/*
 *----------------------------------------------------------------------
 *
 * BenevProxyConnection::dataOnSentCb --
 *
 *      The sent callback to the sockets.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------
 */

void
BenevProxyConnection::socketOnSentCb(void *buf, int bufLen, BenevSocket *socket, void *cbData)
{
   BenevTrace("socket %d, buf %p, bufLen %d\n", socket->getId(), buf, bufLen);

   BenevProxyConnection *connPtr = reinterpret_cast<BenevProxyConnection *>(cbData);

   connPtr->dataSent(socket, buf, bufLen);
}


/*
 *----------------------------------------------------------------------
 *
 * BenevProxyConnection::dataSent --
 *
 *      Data is confirmed sent on a socket. Resume receive on the other socket.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------
 */

void
BenevProxyConnection::dataSent(BenevSocket *dstSock, void *buf, int bufLen)
{
   BenevSocket *srcSock = NULL;
   uint8 *recvBuf = NULL;
   int recvBufLen = 0;
   bool connClosing;

   BenevTrace("socket %d, buf %p, bufLen %d\n", dstSock->getId(), buf, bufLen);

   MXUser_AcquireExclLock(mLock);
   mDataInTransit = false;

   /*
    * If user decided to close the proxy conn while data is in transit, then
    * the state should be in Closing, go ahead and close the sockets now.
    */
   connClosing = (mState == Closing);
   MXUser_ReleaseExclLock(mLock);

   if (connClosing) {
      closeSockets();
      return;
   }

   std::lock_guard<std::mutex> lock(mLockGuard);
   // which socket is the (sent) data source?
   if (dstSock == mDownSock) {
      srcSock = mUpSock;
      recvBuf = mUpSockBuf;
      recvBufLen = sizeof(mUpSockBuf);
      mDownSockSentBytes += bufLen;
      mDownBytesSentThisSecond += bufLen;
   } else if (dstSock == mUpSock) {
      srcSock = mDownSock;
      recvBuf = mDownSockBuf;
      recvBufLen = sizeof(mDownSockBuf);
      mUpSockSentBytes += bufLen;
      mUpBytesSentThisSecond += bufLen;
      mUpLastSentTime = Hostinfo_SystemTimerUS();
   } else {
      ASSERT(FALSE);
   }

   // Now that the data is sent, start a new round of receive-and-send cycle
   srcSock->recvData(recvBuf, recvBufLen, socketOnRecvCb, this);
}
