﻿<?xml version="1.0" encoding="utf-8"?>
<policyDefinitionResources xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" revision="1.0" schemaVersion="1.0" xmlns="http://www.microsoft.com/GroupPolicy/PolicyDefinitions">

   <displayName>Omnissa Horizon Agent</displayName>
   <description>Konfigurationseinstellungen für Horizon Agent</description>
   <resources>
      <stringTable>
         <string id="SUPPORTED_Windows10_1607_VM">Mindestens virtuelle Maschine mit Windows 10/Windows Server 2016 VDI Version 1607</string>

         <string id="Agent_Configuration">Agent-Konfiguration</string>

         <string id="Collaboration">Zusammenarbeit</string>

         <string id="Agent_Security">Agent-Sicherheit</string>

         <string id="Unity_Touch_Hosted_Apps">Unity Touch und gehostete Anwendungen</string>

         <string id="Unity_Filter">Liste mit Unity-Filterregeln</string>

         <string id="Unity_Filter_Rules_Desc">Diese Richtlinie bestimmt die Filterregeln für das Fenster bei einem Remotezugriff auf gehostete Anwendungen. Filterregeln werden von Horizon Agent verwendet, um benutzerdefinierte Anwendungen zu unterstützen. Dieses GPO sollte zum Einsatz kommen, wenn Sie ein Problem mit der Anzeige von Fenstern haben, z. B. wenn ein Fenster einen schwarzen Hintergrund hat oder die Größe eines Dropdown-Fensters nicht ordnungsgemäß angepasst wird.

Der erste Schritt beim Festlegen einer Regel besteht darin, die Merkmale des Fensters bzw. der Fenster zu bestimmen, für das bzw. die die Regel gelten soll. Es gibt viele mögliche Merkmale, die identifiziert werden können:

1. Fensterklassenname, identifiziert in einer benutzerdefinierten Regel als classname=XYZ
2. Produktunternehmen, identifiziert als company=XYZ
3. Produktname, identifiziert als product=XYZ
4. Hauptversion des Produkts, identifiziert als major=XYZ
5. Nebenversion des Produkts, identifiziert als minor=XYZ
6. Produkt-Build-Nummer, identifiziert als build=XYZ
7. Produktrevisionsnummer, identifiziert als revision=XYZ

Es ist üblich, nur „Fensterklassenname“ als bevorzugtes Merkmal zu verwenden (z. B. classname=CustomClassName). Allerdings werden die anderen Merkmale für den Fall bereitgestellt, dass Sie Regeln auf ein bestimmtes Produkt beschränken müssen. Sie finden diese Eigenschaften im Fenster „Dateieigenschaften“ einer ausführbaren Datei. Sie müssen exakt identisch sein, einschließlich Groß-/Kleinschreibung und Sonderzeichen. Wenn mehrere Merkmale angegeben werden, müssen alle übereinstimmen, damit die Regel auf das Fenster angewendet werden kann.

Sobald Sie die Merkmale identifiziert haben, müssen Sie in einem nächsten Schritt eine Aktion auswählen. Die Aktion muss entweder action=block oder action=map sein. action=block weist den Horizon Agent an, das Fenster nicht dem Client remote bereitzustellen. Diese Aktion wird verwendet, wenn ein Fenster auf dem Client angezeigt wird, das zu groß ist oder die normale Fokussierung von Fenstern beeinträchtigt. action=map weist den Horizon Agent an, das Fenster als einen bestimmten, fest kodierten Typ zu behandeln.

Wenn Sie action=map festlegen, müssen Sie auch den Typ angeben, dem das Fenster zugeordnet werden soll. Sie können dies tun, indem Sie type=XYZ mit angeben. Hier eine Liste aller verfügbaren Werte für die Typauswahl: normal, panel, dialog, tooltip, splash, toolbar, dock, desktop, widget, combobox, startscreen, sidepanel, taskbar, metrofullscreen, metrodocked.

Hier zwei Beispiele für Regeln, die Sie festlegen können, um eine fehlerhafte Anwendung zu reparieren:

1. Sie können ein Fenster herausfiltern, das nicht remote bereitgestellt werden soll.
   - Um alle Fenster mit dem Klassennamen „MyClassName“ zu blockieren, verwenden Sie die Regel „classname=MyClassName;action=block“.
   - Um alle Fenster des Produkts „MyProduct“ zu blockieren, verwenden Sie die Regel „product=MyProduct;action=block“.
2. Sie können ein Fenster dem richtigen Typ zuordnen. Dies ist normalerweise nur erforderlich, wenn Sie vom Omnissa Support dazu aufgefordert werden, da nur schwer festgestellt werden kann, ob ein Fenster einem falschen Typ zugeordnet ist.
   - Um eine benutzerdefinierte Klasse dem Typ „combobox“ zuzuordnen, verwenden Sie die Regel „classname=MyClassName;action=map;type=combobox“.

Hinweis: Dieses GPO hat niedrigere Priorität als die unter „%ProgramData%\Omnissa\RdeServer\Unity Filters“ installierten Filterregeln.</string>

         <string id="Smartcard_Redirection">Smartcard-Umleitung</string>

         <string id="Local_Reader_Access">Zugriff auf lokale Lesegeräte</string>

         <string id="True_SSO_Configuration">True SSO-Konfiguration</string>

         <string id="Whfb_Certificate_Redirection">WHFB-Zertifikatsumleitung</string>

         <string id="Whfb_Certificate_Allowed_Applications">Liste der zulässigen ausführbaren Dateien</string>

         <string id="Whfb_Certificate_Allowed_Applications_Desc">Liste der ausführbaren Dateien, die ein umgeleitetes WHFB-Zertifikat verwenden dürfen</string>

         <string id="View_USB_Configuration">Horizon USB-Konfiguration</string>

         <string id="Client_Downloadable_only_settings">Einstellungen für nur Download zum Client</string>

         <string id="Recursive_Domain_Enumeration">Rekursive Enumeration vertrauenswürdiger Domänen</string>

         <string id="Recursive_Domain_Enumeration_Desc">Legt fest, ob alle Domänen aufgelistet werden, die von der Serverdomäne als vertrauenswürdig eingestuft werden. Um eine vollständige Vertrauenskette zu erzielen, werden rekursiv auch die vertrauten Domänen aller vertrauten Domänen aufgelistet – so lange, bis alle vertrauenswürdigen Domänen ermittelt wurden. Diese Informationen werden an den Horizon Connection Server weitergeleitet, um sicherzustellen, dass für die Clientanmeldung alle vertrauenswürdigen Domänen verfügbar sind.

Diese Eigenschaft ist standardmäßig aktiviert. Ist diese Eigenschaft deaktiviert, werden nur Domänen mit einem direkten Vertrauensverhältnis aufgelistet; eine Verbindung mit Remotedomänencontrollern findet nicht statt.

Hinweis: In Umgebungen mit komplexen Domänenbeziehungen – z.B. in Umgebungen mit mehreren Gesamtstrukturen, bei denen Vertrauensstellungen zwischen den Domänen der Gesamtstrukturen eingerichtet wurden – kann dieser Vorgang mehrere Minuten in Anspruch nehmen.</string>

         <string id="Force_MMR_to_use_overlay">Verwendung des Software-Overlay für MMR erzwingen</string>

         <string id="Force_MMR_to_use_overlay_Desc">MMR versucht standardmäßig das Hardware-Overlay für eine verbesserte Leistung der Videowiedergabe zu verwenden. Bei der Arbeit mit mehreren Anzeigegeräten ist das Hardware-Overlay aber nur auf einem Anzeigegerät verfügbar – entweder auf dem primären Anzeigegerät oder auf dem Anzeigegerät, auf dem WMP gestartet wurde. Wenn WMP auf ein anderes Anzeigegerät gezogen wird, wird statt des Videos ein schwarzes Rechteck angezeigt. Mit dieser Option können Sie sicherstellen, dass MMR ein Software-Overlay für alle Anzeigegeräte verwendet.</string>

         <string id="Enable_multi_media_acceleration">Multimedia-Beschleunigung aktivieren</string>

         <string id="Enable_multi_media_acceleration_Desc">Legt fest, ob die Multimedia-Umleitung (Multimedia Redirection, MMR) auf dem Agenten aktiviert ist. MMR ist ein Microsoft DirectShow-Filter, der Multimediadaten von bestimmten Codecs auf dem Remote-System direkt über einen TCP-Socket an den Client weiterleitet. Die Daten werden direkt auf dem Client decodiert, auf dem sie wiedergegeben werden. Administratoren können MMR deaktivieren, wenn der Client nicht über ausreichend Ressourcen verfügt, um eine lokale Multimedia-Decodierung durchzuführen.

Hinweis: MMR arbeitet nicht ordnungsgemäß, wenn die Horizon Client-Hardware zur Videoanzeige keine Overlay-Unterstützung aufweist. Die MMR-Richtlinie gilt nicht für Offline-Desktop-Sitzungen.</string>

         <string id="AllowDirectRDP">Direktes RDP zulassen</string>

         <string id="AllowDirectRDP_Desc">Legt fest, ob Nicht-Horizon Clients mithilfe RDP eine direkte Verbindung mit Horizon-Desktops herstellen können. Wenn Sie diese Eigenschaft deaktivieren, sind für den Agenten nur Horizon-verwaltete Verbindungen über Horizon Client oder Horizon Portal zulässig.

Diese Eigenschaft ist standardmäßig aktiviert.</string>

         <string id="AllowSingleSignon">Single Sign-On zulassen</string>

         <string id="AllowSingleSignon_Desc">Legt fest, ob zur Verbindungsherstellung mit Horizon-Desktops die einmalige Anmeldung (Single Sign-On, SSO) verwendet wird. Wenn Sie diese Eigenschaft aktivieren, müssen Benutzer nur dann ihre Anmeldedaten eingeben, wenn sie eine Verbindung mit Horizon Client oder Horizon Portal herstellen. Wenn Sie diese Eigenschaft deaktivieren, müssen sich die Benutzer beim Herstellen einer Remoteverbindung erneut authentifizieren.

Für diese Eigenschaft muss die Komponente „Secure Authentication“ von Horizon Agent auf dem Desktop installiert sein. Sie ist standardmäßig aktiviert.</string>

         <string id="AutoPopulateLogonUI">Anmeldeoberfläche automatisch auffüllen</string>

         <string id="AutoPopulateLogonUI_Desc">Legt fest, ob das Feld „Benutzername“ in der Anmeldeoberfläche automatisch ausgefüllt wird oder nicht. Diese Eigenschaft ist standardmäßig aktiviert und nur im RDS-Fall anwendbar, wenn Single Sign-On deaktiviert ist oder nicht funktioniert.</string>

         <string id="ConnectionTicketTimeout">Verbindungsticket, Zeitüberschreitung</string>

         <string id="ConnectionTicketTimeout_Desc">Legt den Zeitraum in Sekunden fest, in dem das Horizon-Verbindungs-Ticket gültig ist. Das Verbindungs-Ticket wird von Horizon Clients bei der Herstellung einer Verbindung mit Horizon Agent sowie für Überprüfungen und für die Single Sign-On-Anmeldung verwendet.

Aus Sicherheitsgründen sind solche Tickets nur im angegebenen Zeitraum gültig. Wenn diese Eigenschaft nicht explizit festgelegt ist, gilt ein Standardwert von 900 Sekunden.</string>

         <string id="CredentialFilterExceptions">Ausnahmen für Anmeldedatenfilter</string>

         <string id="CredentialFilterExceptions_Desc">Eine Liste von durch Semikolon getrennten Namen ausführbarer Dateien, die den CredentialFilter des Agenten nicht laden sollen. Die Dateinamen müssen ohne Pfad und ohne Suffix angegeben werden.</string>

         <string id="RDPVcBridgeUnsupportedClients">Nicht unterstützte RDPVcBridge-Clients</string>

         <string id="RDPVcBridgeUnsupportedClients_Desc">Eine kommagetrennte Liste von Horizon Client-Typen, die RDPVcBridge nicht unterstützen.</string>

         <string id="Disable_Time_Zone_sync">Zeitzonensynchronisierung deaktivieren</string>

         <string id="Disable_Time_Zone_sync_Desc">Legt fest, ob die Zeitzone des Horizon-Desktops mit der des verbundenen Clients synchronisiert wird. Ist diese Eigenschaft aktiviert, wird sie nur angewendet, wenn die Eigenschaft „Zeitzonenweiterleitung deaktivieren“ der Horizon Client-Konfigurationsrichtlinie nicht deaktiviert ist.

Diese Eigenschaft ist standardmäßig deaktiviert.</string>

         <string id="Keep_Time_Zone_sync_disconnect">Zeitzonensynchronisierung bei getrennter Verbindung beibehalten (VDI)</string>

         <string id="Keep_Time_Zone_sync_disconnect_Desc">Wenn „Zeitzonensynchronisierung“ aktiviert ist und diese Eigenschaft aktiviert ist, bleibt die Zeitzone des Remote-Desktops mit der Zeitzone des Clients synchron, von dem die Verbindung zuletzt getrennt wurde.

Wenn diese Eigenschaft deaktiviert ist, wird die Zeitzone des Remote-Desktops beim Trennen der Endbenutzersitzung wiederhergestellt.

Diese Einstellung wird für RDSH-Hosts nicht angewendet, wenn die Remotedesktopdienste-Rolle aktiviert ist.

Diese Eigenschaft ist standardmäßig deaktiviert.</string>

         <string id="Keep_Time_Zone_sync_logoff">Zeitzonensynchronisierung bei Abmeldung beibehalten (VDI)</string>

         <string id="Keep_Time_Zone_sync_logoff_Desc">Wenn „Zeitzonensynchronisierung“ aktiviert ist und diese Eigenschaft aktiviert ist, bleibt die Zeitzone des Remote-Desktops mit der Zeitzone des Clients synchron, der zuletzt abgemeldet wurde.

Wenn diese Eigenschaft deaktiviert ist, wird die Zeitzone des Remote-Desktops beim Abmelden der Endbenutzersitzung wiederhergestellt.

Diese Einstellung wird für RDSH-Hosts nicht angewendet, wenn die Remotedesktopdienste-Rolle aktiviert ist.

Diese Eigenschaft ist standardmäßig deaktiviert.</string>

          <string id="Enable_ClientMediaPerm_Popup">Registerkarten-, Bildschirm- und Anwendungsauswahl für die Bildschirmfreigabe mit Browserumleitung aktivieren</string>

          <string id="Enable_ClientMediaPerm_Popup_Desc">Wenn diese Option aktiviert ist, wird bei der Bildschirmfreigabe mit Browserumleitung eine Auswahlmöglichkeit für die Browser-Registerkarte, den Bildschirm oder die Anwendung angezeigt. Diese Eigenschaft ist standardmäßig aktiviert.</string>

		  <string id="Toggle_Display_Settings_Control">Steuerelement für Anzeigeeinstellungen umschalten</string>

         <string id="Toggle_Display_Settings_Control_Desc">Legt fest, ob die Seite „Einstellungen“ in der Systemsteuerungsoption „Anzeige“ deaktiviert ist, wenn ein Horizon Client verbunden ist.

Diese Eigenschaft wird nur für Sitzungen mit dem PCoIP-Protokoll angewendet. Diese Eigenschaft ist standardmäßig aktiviert.</string>

         <string id="DpiSync">DPI-Synchronisierung</string>

         <string id="DpiSync_Desc">Passt die systemweite DPI-Einstellung für die Remotesitzung an. Wenn diese Eigenschaft aktiviert oder nicht konfiguriert ist, wird die systemweite DPI-Einstellung der Remotesitzung dem Wert der entsprechenden DPI-Einstellung des Clientbetriebssystems angepasst. Wenn diese Eigenschaft deaktiviert ist, wird die systemweite DPI-Einstellung für die Remotesitzung nicht geändert.</string>

         <string id="DpiSyncPerMonitor">DPI-Synchronisierung pro Monitor</string>

         <string id="DpiSyncPerMonitor_Desc">Passt die DPI-Einstellung bei mehreren Monitoren während einer Remotesitzung an. Wenn diese Option aktiviert oder nicht konfiguriert ist, ändert sich die DPI-Einstellung in allen Monitoren, sodass sie während einer Remotesitzung mit der DPI-Einstellung des Client-Betriebssystems übereinstimmt. Wenn die DPI-Einstellung angepasst ist, wird die angepasste DPI-Einstellung abgeglichen. Wenn diese Option deaktiviert ist, müssen die Benutzer die Verbindung trennen und eine Verbindung zu einer neuen Remotesitzung herstellen, damit die DPI-Änderungen für alle Monitore wirksam werden.</string>

         <string id="DisplayScaling">Anzeigeskalierung</string>

         <string id="DisplayScaling_Desc">Legen Sie fest, ob die Anzeigeskalierungsfunktion auf der Agent-Seite zulässig ist. Wenn diese Option aktiviert oder nicht konfiguriert ist, wird die Anzeigeskalierung auf der Agent-Seite zugelassen, und der endgültige Ein- oder Aus-Zustand der Anzeigeskalierungsfunktion hängt von der clientseitigen Konfiguration ab. Wenn diese Option deaktiviert ist, ist die Anzeigeskalierungsfunktion unabhängig von der clientseitigen Konfiguration deaktiviert. Diese Konfiguration wird nur wirksam, wenn die DPI-Synchronisierung pro Monitor deaktiviert ist.</string>

         <string id="DisallowCollaboration">Zusammenarbeit deaktivieren</string>

         <string id="DisallowCollaboration_Desc">Mit dieser Einstellung wird festgelegt, ob die Zusammenarbeit auf der Horizon Agent-VM zugelassen wird. Wenn diese Einstellung aktiviert ist, wird die Zusammenarbeitsfunktion vollständig deaktiviert. Wenn diese Einstellung deaktiviert oder nicht konfiguriert ist, wird die Funktion auf Pool-Ebene gesteuert. Horizon Agent-Computer müssen neu gestartet werden, damit diese Einstellung übernommen wird.</string>

         <string id="AllowCollaborationInviteByIM">Einladung von Kollaboratoren per Sofortnachricht zulassen</string>

         <string id="AllowCollaborationInviteByIM_Desc">Mit dieser Einstellung wird festgelegt, ob Benutzer Einladungen zur Zusammenarbeit über einen installierten Sofortnachrichtendienst senden dürfen. Wenn diese Einstellung deaktiviert ist, dürfen Benutzer keine Einladungen über eine Sofortnachricht versenden, auch wenn ein Sofortnachrichtendienst installiert ist. Diese Einstellung ist standardmäßig aktiviert.</string>

         <string id="AllowCollaborationInviteByEmail">Einladung von Kollaboratoren per E-Mail zulassen</string>

         <string id="AllowCollaborationInviteByEmail_Desc">Mit dieser Einstellung wird festgelegt, ob Benutzer Einladungen zur Zusammenarbeit über eine installierte E-Mail-Anwendung senden dürfen. Wenn diese Einstellung deaktiviert ist, dürfen Benutzer keine Einladungen per E-Mail versenden, auch wenn eine E-Mail-Anwendung installiert ist. Diese Einstellung ist standardmäßig aktiviert.</string>

         <string id="AllowCollaborationControlPassing">Übergabe der Kontrolle an Kollaboratoren zulassen</string>

         <string id="AllowCollaborationControlPassing_Desc">Diese Einstellung konfiguriert, ob Benutzer die Eingabesteuerung während der Zusammenarbeit an andere Kollaboratoren übergeben dürfen. Diese Einstellung ist standardmäßig aktiviert.</string>

         <string id="MaxCollaboratorCount">Maximale Anzahl an eingeladenen Kollaboratoren</string>

         <string id="MaxCollaboratorCount_Desc">Mit dieser Einstellung wird die maximale Anzahl an Kollaboratoren festgelegt, die ein Benutzer zur Teilnahme an seiner Sitzung einladen kann. Standardmäßig ist ein Höchstwert von 5 festgelegt.</string>

         <string id="CollaborationEmailInviteDelimiter">Trennzeichen für mehrere E-Mail-Adressen in „Senden an“-Links</string>

         <string id="CollaborationEmailInviteDelimiter_Desc">Mit dieser Einstellung werden die Trennzeichen für mehrere E-Mail-Adressen in mailto:-Links konfiguriert. Wenn diese Richtlinie nicht konfiguriert wird, ist der Standardwert zum Trennen von E-Mail-Adressen „;“ (Semikolon ohne Leerzeichen). So wird die Kompatibilität mit den gängigen E-Mail-Clients gewährleistet.

Wenn bei Ihrem Standard-E-Mail-Client Probleme mit diesem Trennzeichen auftreten, können Sie es mit anderen Kombinationen versuchen, beispielsweise „, “ (Komma plus ein Leerzeichen) oder „; “ (Semikolon plus ein Leerzeichen). Dieser Wert wird vor der Platzierung im mailto:-Link URI-verschlüsselt, also verwenden Sie für diesen Eintrag keinen URI-verschlüsselten Wert.</string>

         <string id="CollaborationClipboardIncludeOutlookURL">Outlook-formatierte URL in den Zwischenablagetext einschließen</string>

         <string id="CollaborationClipboardIncludeOutlookURL_Desc">Wenn diese Einstellung aktiviert ist, wird eine für Outlook formatierte Einladungs-URL in den Einladungstext in der Zwischenablage aufgenommen. Aktivieren Sie diese Einstellung, wenn Sie erwarten, dass Endbenutzer den Einladungstext aus der Zwischenablage in eine E-Mail einfügen. Diese Einstellung ist standardmäßig deaktiviert.</string>

         <string id="CollaborationServerURLs">In Einladungsnachrichten enthaltene Server-URLs</string>

         <string id="CollaborationServerURLs_Desc">Mit dieser Einstellung können Sie die Standard-URL in Einladungen zur Zusammenarbeit ändern. Es wird empfohlen, diesen Wert festzulegen, wenn für Ihre Bereitstellung mehr als eine Server-URL verwendet wird (z. B. verschiedene interne und externe URLs bzw. URLs pro Pod).

Wenn Sie die Werte festlegen, sollte die erste Spalte die URL mit einem optionalen Port beinhalten (z. B. „horizon-ca.corp.int“ oder „horizon-ca.corp.int:2323“). Die zweite Spalte sollte eine kurze Beschreibung der URL beinhalten (z. B. „Kalifornien-Pod“ oder „Unternehmensnetzwerk“). Die Beschreibung wird nur verwendet, wenn sich in der Liste mehrere Server befinden.</string>

         <string id="UnAuthenticatedAccessEnabled">Aktivieren des nicht authentifizierten Zugriffs</string>

         <string id="UnAuthenticatedAccessEnabled_Desc">Diese Einstellung aktiviert die Funktion für den nicht authentifizierten Zugriff. Damit diese Änderung wirksam wird, muss das System neu gestartet werden. Der nicht authentifizierte Zugriff ist standardmäßig aktiviert.</string>

         <string id="RdsAadAuthEnabled">Azure Active Directory-Single Sign-On aktivieren</string>

         <string id="RdsAadAuthEnabled_Desc">Diese Einstellung aktiviert die Azure Active Directory-Single Sign-On-Funktion. Damit diese Änderung wirksam wird, muss das System neu gestartet werden. Diese Funktion ist standardmäßig aktiviert.  Diese Funktion hängt davon ab, ob dem System Azure Active Directory hinzugefügt wird.</string>

         <string id="CommandsToRunOnConnect">Befehle zum Ausführen bei Verbindung</string>

         <string id="CommandsToRunOnConnect_Desc">Liste mit Befehlen, die bei der ersten Verbindungsherstellung einer Sitzung ausgeführt werden.</string>

         <string id="CommandsToRunOnReconnect">Befehle zum Ausführen bei erneuter Verbindung</string>

         <string id="CommandsToRunOnReconnect_Desc">Liste mit Befehlen, die bei der erneuten Herstellung einer Verbindung mit einer Sitzung nach einer vorangegangenen Trennung ausgeführt werden.</string>

         <string id="CommandsToRunOnDisconnect">Befehle zum Ausführen bei Verbindungstrennung</string>

         <string id="CommandsToRunOnDisconnect_Desc">Liste mit Befehlen, die bei der Trennung einer Sitzung ausgeführt werden.</string>

         <string id="ShowDiskActivityIcon">Symbol „Festplattenaktivität anzeigen“</string>

         <string id="ShowDiskActivityIcon_Desc">Legt fest, ob ein Symbol für die Festplattenaktivität in der Taskleiste angezeigt wird. Verwendet den „System Trace NT Kernel Logger“ (NT-Kernelprotokollierung der Systemablaufverfolgung), der nur von einem Prozess verwendet werden kann. Deaktivieren Sie diese Eigenschaft, wenn die Protokollierung für andere Zwecke verwendet werden soll. Standardmäßig ist die Eigenschaft aktiviert.</string>

         <string id="SSO_retry_timeout">Zeitraum für die Wiederholung der Single-Sign-On-Anmeldung</string>

         <string id="SSO_retry_timeout_Desc">Legt den Zeitraum in Millisekunden fest, nach dem erneut versucht wird, eine Single-Sign-On-Anmeldung durchzuführen. Mit dem Wert 0 wird keine erneute Single-Sign-On-Anmeldung durchgeführt. Der Standardwert beträgt 5000 Millisekunden.</string>

         <string id="Win10PhysicalAgentAudioOption">Audiooption für physischen Remote-Desktop-Computer bei einer Einzelsitzung unter Windows 10</string>

         <string id="Win10PhysicalAgentAudioOption_Desc">Gibt an, welche Audiogeräte für den physischen Remote-Desktop-Computer bei einer Horizon-Sitzung unter Windows 10 verwendet werden sollen. Standardmäßig werden Audiogeräte verwendet, die mit einem Horizon Client-Endpunkt verbunden sind.</string>

         <string id="WaitForLogoff">Zeitüberschreitung beim Warten auf Abmeldung</string>

         <string id="WaitForLogoff_Desc">Gibt die Zeit in Sekunden an, die gewartet werden soll, bis die vorherige Sitzung des Benutzers beendet ist, bevor ein neuer Anmeldeversuch unternommen wird. Setzen Sie den Wert auf 0, um das Warten zu deaktivieren und den Vorgang sofort abzubrechen. Der Standardwert beträgt 10 Sekunden.</string>

         <string id="UseClientAudioDevice">Mit einem Horizon Client-Endpunkt verbundene Audiogeräte verwenden</string>

         <string id="UsePhysicalMachineAudioDevice">Mit einem physischen Remote-Desktop eines Horizon-Endpunkts unter Windows 10 verbundene Audiogeräte verwenden</string>

         <string id="VDI_idle_time_till_disconnect">Leerlaufzeit bis zum Trennen (VDI)</string>

         <string id="VDI_idle_time_till_disconnect_Desc">Gibt den Zeitraum an, nach dem eine VDI-Desktop-Sitzung aufgrund von Benutzerinaktivität getrennt wird.
Wenn diese Einstellung entweder deaktiviert oder nicht konfiguriert ist, werden die VDI-Desktop-Sitzungen nie getrennt. Die Auswahl von „Nie“ hat dieselbe Wirkung.
Hinweis: Wenn der Desktop-Pool oder die Computer für die automatische Abmeldung nach einer Trennung konfiguriert ist, werden diese Einstellungen berücksichtigt.</string>

         <string id="Accept_SSL_encr_framework_channel">SSL-verschlüsselten Framework-Kanal akzeptieren</string>

         <string id="Accept_SSL_encr_framework_channel_Desc">Akzeptiert den SSL-verschlüsselten Framework-Kanal

Aktivieren: Aktiviert SSL und ermöglicht älteren Clients eine Verbindung ohne SSL
Deaktivieren: Deaktiviert SSL
Erzwingen: Aktiviert SSL und verhindert die Verbindung älterer Clients</string>

         <string id="Enable">Aktivieren</string>

         <string id="Disable">Deaktivieren</string>

         <string id="Enforce">Erzwingen</string>

         <string id="Allow_smartcard_local_access">Anwendungszugriff auf lokale Smartcard-Lesegeräte zulassen</string>

         <string id="Allow_smartcard_local_access_Desc">Wenn Sie diese Einstellung aktivieren, können Anwendungen auf alle „lokalen“ Smartcard-Lesegeräte zugreifen, auch wenn die Funktion der Smartcard-Umleitung installiert ist.

Diese Einstellung wird für nicht RDP- oder RDSH-Hosts angewendet, wenn die Remotedesktopdienste-Rolle aktiviert ist.

Ist diese Einstellung aktiviert, wird der Desktop auf lokale Lesegeräte überwacht. Werden solche Geräte ermittelt, wird die Smartcard-Umleitung ausgeschaltet und damit der Zugriff auf lokale Lesegeräte ermöglicht. Die Umleitung bleibt solange ausgeschaltet, bis der Benutzer wieder eine Verbindung mit der Sitzung herstellt.

HINWEIS: Wenn der lokale Zugriff aktiviert ist, können Anwendungen nicht mehr auf Remotelesegeräte auf dem Client zugreifen.

Diese Einstellung ist standardmäßig deaktiviert.</string>

         <string id="Local_Reader_Name">Name des lokalen Lesegeräts</string>

         <string id="Local_Reader_Name_Desc">Legt den Namen eines lokalen Lesegeräts fest, das überwacht werden soll, um den lokalen Zugriff zu aktivieren. Standardmäßig muss im Lesegerät eine Smartcard eingesteckt sein, damit der lokale Zugriff aktiviert werden kann. Diese Anforderung können Sie mithilfe der Einstellung „Eingesteckte Smartcard anfordern“ deaktivieren.


Standardmäßig ist die Funktion für alle Lesegeräte aktiviert.</string>

         <string id="Require_an_inserted_smart_card">Eingesteckte Smartcard anfordern</string>

         <string id="Require_an_inserted_smart_card_Desc">Wenn diese Einstellung aktiviert ist, wird der lokale Zugriff auf Lesegeräte nur aktiviert, wenn in den Lesegeräten eine Smartcard eingesteckt ist. Wenn diese Einstellung deaktiviert ist, wird der lokale Zugriff aktiviert, wenn ein lokales Lesegerät ermittelt wird.

Diese Einstellung ist standardmäßig aktiviert.</string>

         <string id="Disable_true_SSO">True SSO deaktivieren</string>

         <string id="Disable_true_SSO_Desc">Legt fest, ob „True SSO“ auf dem Agenten deaktiviert wird.</string>

         <string id="Cert_wait_timeout">Wartezeit für Zertifikate</string>

         <string id="Cert_wait_timeout_Desc">Legt die Zeitüberschreitungsdauer für Zertifikate in Millisekunden fest. Innerhalb dieser Zeitspanne müssen die Zertifikate auf dem Agenten eintreffen.</string>

         <string id="Min_key_size">Mindestschlüsselgröße</string>

         <string id="Min_key_size_Desc">Es werden nur Schlüssel der Mindestgröße verwendet.</string>

         <string id="All_key_sizes">Alle Schlüsselgrößen</string>

         <string id="All_key_sizes_Desc">Es können alle Schlüsselgrößen verwendet werden. Es lassen sich maximal fünf Größen angeben. Beispiel: 1024,2048,3072,4096</string>

         <string id="Keys_to_precreate">Anzahl der vorab erstellten Schlüssel</string>

         <string id="Keys_to_precreate_Desc">Anzahl der Schlüssel, die in einer RDSH-Umgebung vorab erstellt werden sollen.</string>

         <string id="Cert_min_validity">Für ein Zertifikat erforderliche Mindestgültigkeitsdauer</string>

         <string id="Cert_min_validity_Desc">Mindestgültigkeitsdauer (in Minuten), die für ein Zertifikat für die Wiederverwendung zur erneuten Herstellung einer Verbindung mit dem Benutzer erforderlich ist.</string>

         <string id="Enable_Unity_Touch">Unity Touch aktivieren</string>

         <string id="Enable_Unity_Touch_Desc">Diese Richtlinie legt fest, ob die Unity Touch-Funktionalität auf Horizon Agent aktiviert ist. Standardmäßig ist Unity Touch aktiviert.

Wenn Unity Touch unter Windows 10 aktiviert ist, gibt die untergeordnete Richtlinie an, ob die UWP-App-Unterstützung (Univeral Windows Platform) für Unity Touch auf dem Horizon Agent aktiviert ist. Die Standardeinstellung für die UWP-Unterstützung auf Unity Touch ist „Aktiviert“. Wenn die Unity Touch-Richtlinie nicht konfiguriert ist, ist die UWP-Unterstützung für Unity Touch auf Windows 10 aktiviert.</string>

         <string id="Enable_system_tray_redir">Taskleistenumleitung für gehostete Anwendungen aktivieren</string>

         <string id="Enable_system_tray_redir_Desc">Diese Richtlinie legt fest, ob die Taskleistenumleitung beim Remotezugriff auf gehostete Anwendungen aktiviert ist. Standardmäßig ist die Taskleistenumleitung aktiviert.</string>

         <string id="Enable_user_prof_customization">Anpassung von Benutzerprofilen für gehostete Anwendungen aktivieren</string>

         <string id="Enable_user_prof_customization_Desc">Diese Richtlinie legt fest, ob die Anpassung von Benutzerprofilen beim Remotezugriff auf gehostete Anwendungen ausgeführt wird. Damit werden ein Benutzerprofil generiert, das Windows-Design angepasst und die registrierten Startanwendungen ausgeführt. Standardmäßig ist diese Richtlinie deaktiviert.</string>

         <string id="AllowTinyOrOffscreenWindows">Aktualisierungen für leere oder nicht sichtbare Fenster senden</string>

         <string id="AllowTinyOrOffscreenWindows_Desc">Diese Richtlinie legt fest, ob Horizon Client Aktualisierungen für leere oder nicht sichtbare Fenster erhält. Wenn die Richtlinie deaktiviert ist, werden Informationen zu Fenstern, die kleiner als 2x2 Pixel oder komplett nicht sichtbar sind, nicht an Horizon Client gesendet. Die Richtlinie ist standardmäßig deaktiviert.</string>

         <string id="MinimalHookingModeEnabled">Verwendung von Windows-Hooks beschränken</string>

         <string id="MinimalHookingModeEnabled_Desc">Diese Richtlinie schaltet die meisten Hooks bei einem Remotezugriff auf gehostete Anwendungen oder bei der Verwendung von Unity Touch aus. Sie ist für Anwendungen mit Kompatibilitäts- und Leistungsproblemen vorgesehen, wenn Hooks auf Betriebssystemebene festgelegt sind. Diese Einstellung deaktiviert beispielsweise die Verwendung der meisten Windows Active Accessibility- und Windows In-Process-Hooks. Diese Richtlinie ist standardmäßig deaktiviert, d. h., standardmäßig werden alle bevorzugten Hooks verwendet.</string>

         <string id="LaunchAppWhenArgsAreDifferent">Nur bei unterschiedlichen Argumenten neue Instanzen gehosteter Anwendungen starten</string>

         <string id="LaunchAppWhenArgsAreDifferent_Desc">Diese Richtlinie steuert das Verhalten, wenn eine gehostete Anwendung gestartet wird, aber bereits eine vorhandene Instanz der Anwendung innerhalb einer Sitzung mit getrenntem Protokoll ausgeführt wird. Bei Deaktivierung wird die vorhandene Instanz der Anwendung aktiviert. Bei Aktivierung wird die vorhandene Instanz der Anwendung nur aktiviert, wenn die Befehlszeilenparameter übereinstimmen. Standardmäßig ist diese Richtlinie deaktiviert.</string>

         <string id="Exclude_Vid_Pid">Vid/Pid-Gerät ausschließen</string>

         <string id="Exclude_Vid_Pid_Desc">Schließt ein Gerät mit einer bestimmten Anbieter- und Produkt-ID von der Weiterleitung aus.

Syntax: {m|o}:&lt;vid-&lt;xxxx&gt;_pid-&lt;xxxx|*&gt;&gt;[;...]
merge-flag:
m = Clienteinstellung wird mit der Agenteneinstellung verknüpft
o = Agenteneinstellung hat Vorrang vor der Clienteinstellung

Beispiel: m:vid-0781_pid-554c;vid-0781_pid-9999</string>

         <string id="Exclude_Vid_Pid_Rel">Vid-/Pid-/Rel-Gerät ausschließen</string>

         <string id="Exclude_Vid_Pid_Rel_Desc">Schließt ein Gerät mit einer bestimmten Anbieter-ID, Produkt-ID und Versionsnummer von der Weiterleitung aus.

Syntax: {m|o}:&lt;vid-&lt;xxxx&gt;_pid-&lt;xxxx&gt;_rel-&lt;xxxx|*&gt;&gt;[;...]
merge-flag:
m = Client-Einstellung wird mit der Agent-Einstellung verknüpft
o = Agent-Einstellung hat Vorrang vor der Client-Einstellung

Beispiel: m:vid-0781_pid-554c_rel-0100;vid-0781_pid-9999_rel-0010</string>

         <string id="Include_Vid_Pid">Vid-/Pid-Gerät einschließen</string>

         <string id="Include_Vid_Pid_Desc">Legt für ein Gerät mit einer bestimmten Anbieter- und Produkt-ID fest, dass es weitergeleitet werden kann.

Syntax: {m|o}:&lt;vid-&lt;xxxx&gt;_pid-&lt;xxxx|*&gt;&gt;[;...]

merge-flag:
m = Clienteinstellung wird mit der Agenteneinstellung verknüpft
o = Agenteneinstellung hat Vorrang vor der Clienteinstellung

Beispiel: o:vid-0781_pid-554c;vid-0781_pid-9999</string>

         <string id="Include_Vid_Pid_Rel">Vid-/Pid-/Rel-Gerät einschließen</string>

         <string id="Include_Vid_Pid_Rel_Desc">Schließt ein Gerät mit einer bestimmten Anbieter-ID, Produkt-ID und Versionsnummer ein, das weitergeleitet werden kann.

Syntax: {m|o}:&lt;vid-&lt;xxxx&gt;_pid-&lt;xxxx&gt;_rel-&lt;xxxx|*&gt;&gt;[;...]

merge-flag:
m = Client-Einstellung wird mit der Agent-Einstellung verknüpft
o = Agent-Einstellung hat Vorrang vor der Client-Einstellung

Beispiel: o:vid-0781_pid-554c_rel-0100;vid-0781_pid-9999_rel-0010</string>

         <string id="Exclude_device_family">Gerätefamilie ausschließen</string>

         <string id="Exclude_device_family_Desc">Schließt die Geräte einer Gerätefamilie von der Weiterleitung aus.

Syntax: {m|o}:&lt;Familienname&gt;[;...]

merge-flag:
m = Clienteinstellung wird mit der Agenteneinstellung verknüpft
o = Agenteneinstellung hat Vorrang vor der Clienteinstellung

Beispiel: o:bluetooth;audio-in</string>

         <string id="Include_device_family">Gerätefamilie einschließen</string>

         <string id="Include_device_family_Desc">Legt für eine Gerätefamilie fest, dass deren Geräte weitergeleitet werden können.

Syntax: {m|o}:&lt;Familienname&gt;[;...]

merge-flag:
m = Clienteinstellung wird mit der Agenteneinstellung verknüpft
o = Agenteneinstellung hat Vorrang vor der Clienteinstellung

Beispiel: m:storage;audio-out</string>

         <string id="Exclude_all">Alle Geräte ausschließen</string>

         <string id="Exclude_all_Desc">Blockiert alle Geräte, solange sie nicht über eine Filterregel eingeschlossen werden.

Standard: Alle Geräte zulassen</string>

         <string id="HidOpt_Include_Vid_Pid">HID-Optimierung Vid/Pid-Gerät einbeziehen</string>

         <string id="HidOpt_Include_Vid_Pid_Desc">Legt für ein HID-Gerät mit einer bestimmten Anbieter- und Produkt-ID fest, dass es optimiert werden kann.

Syntax: &lt;vid-&lt;xxxx&gt;_pid-&lt;xxxx|*&gt;&gt;[;...]

Beispiel: vid-056a_pid-0302;vid-046d_pid-c628</string>

         <string id="Exclude_Auto_Vid_Pid">Verbindung für Vid/Pid-Gerät automatisch ausschließen</string>

         <string id="Exclude_Auto_Vid_Pid_Desc">Schließt ein Gerät mit einer bestimmten Anbieter- und Produkt-ID von der automatischen Weiterleitung aus.

Syntax: {m|o}:&lt;vid-&lt;xxxx&gt;_pid-&lt;xxxx|*&gt;&gt;[;...]
merge-flag:
m = Clienteinstellung wird mit der Agenteneinstellung verknüpft
o = Agenteneinstellung hat Vorrang vor der Clienteinstellung

Beispiel: m:vid-0781_pid-554c;vid-0781_pid-9999</string>

         <string id="Exclude_auto_device_family">Verbindungsgeräte-Familie automatisch ausschließen</string>

         <string id="Exclude_auto_device_family_Desc">Schließt eine Familie mit Geräten von der automatischen Weiterleitung aus.

Syntax: {m|o}:&lt;Familienname&gt;[;...]

merge-flag:
m = Clienteinstellung wird mit der Agenteneinstellung verknüpft
o = Agenteneinstellung hat Vorrang vor der Clienteinstellung

Beispiel: o:storage;hid</string>

         <string id="Exclude_Vid_Pid_from_Split">Vid/Pid-Gerät vom Splitten ausschließen</string>

         <string id="Exclude_Vid_Pid_from_Split_Desc">Schließt die Komponentengeräte eines mit seiner Anbieter- und Produkt-ID angegebenen Verbundgeräts von der Behandlung als separate Geräte für das Filtern aus.

Syntax: {m|o}:&lt;vid-&lt;xxxx&gt;_pid-&lt;xxxx|*&gt;&gt;[;...]

merge-flag:
m = Clienteinstellung wird mit der Agenteneinstellung verknüpft
o = Agenteneinstellung hat Vorrang vor der Clienteinstellung

Beispiel: o:vid-0781_pid-554c;vid-0781_pid-9999</string>

         <string id="Split_Vid_Pid_Device">Vid/Pid-Gerät splitten</string>

         <string id="Split_Vid_Pid_Device_Desc">Behandelt die Komponentengeräte eines mit seiner Anbieter- und Produkt-ID angegebenen Verbundgeräts als separate Geräte für das Filtern.

Syntax: {m|o}:&lt;vid-&lt;xxxx&gt;_pid-&lt;xxxx|*&gt;(exintf:xx[;exintf:xx])&gt;[;...]
merge-flag:
m = Clienteinstellung wird mit der Agenteneinstellung verknüpft
o = Agenteneinstellung hat Vorrang vor der Clienteinstellung

Beispiel: o:vid-0781_pid-554c(exintf:01;exintf:02);vid-0781_pid-9999(exintf:02)</string>

         <string id="Allow_other_input_devices">Andere Eingabegeräte zulassen</string>

         <string id="Allow_other_input_devices_Desc">Erlaubt die Weiterleitung anderer Eingabegeräte neben startfähigen Geräten, Tastatur- und Mausgeräten.

Standard: Weiterleitung zulassen</string>

         <string id="Allow_Default">Aktivieren – Standardeinstellung für Client</string>

         <string id="Allow_Override">Zulassen – Clienteinstellung außer Kraft setzen</string>

         <string id="Disable_Default">Deaktivieren – Standardeinstellung für Client</string>

         <string id="Disable_Override">Deaktivieren – Clienteinstellung außer Kraft setzen</string>

         <string id="Allow_HID_Bootable">Startfähige Eingabegeräte zulassen</string>

         <string id="Allow_HID_Bootable_Desc">Erlaubt die Weiterleitung von Eingabegeräten, die zur Startzeit verfügbar sind (auch als „Startfähige Eingabegeräte“ bezeichnet).

Standard: Weiterleitung zulassen</string>

         <string id="Allow_Audio_Input_devices">Audioeingabegeräte zulassen</string>

         <string id="Allow_Audio_Input_devices_Desc">Erlaubt die Weiterleitung von Audioeingabegeräten.

Standard: Weiterleitung zulassen</string>

         <string id="Allow_Audio_Output_devices">Audioausgabegeräte zulassen</string>

         <string id="Allow_Audio_Output_devices_Desc">Erlaubt die Weiterleitung von Audioausgabegeräten.

Standard: Weiterleitung blockieren</string>

         <string id="Allow_keyboard_mouse">Tastatur- und Mausgeräte zulassen</string>

         <string id="Allow_keyboard_mouse_Desc">Erlaubt die Weiterleitung von Tastatur- und Mausgeräten.

Standard: Weiterleitung blockieren</string>

         <string id="Allow_Video_Devices">Videogeräte zulassen</string>

         <string id="Allow_Video_Devices_Desc">Erlaubt die Weiterleitung von Videogeräten.

Standard: Weiterleitung zulassen</string>

         <string id="Allow_Smart_Cards">Smartcards zulassen</string>

         <string id="Allow_Smart_Cards_Desc">Erlaubt die Weiterleitung von Smartcard-Geräten.

Standard: Weiterleitung blockieren</string>

         <string id="Allow_Auto_Device_Splitting">Autom. Gerätesplitten zulassen</string>

         <string id="Allow_Auto_Device_Splitting_Desc">Schließt die Komponentengeräte von Verbundgeräten von der automatischen Behandlung als separate Geräte aus.</string>

         <string id="Proxy_default_ie_autodetect">Proxy-Einstellungen automatisch erkennen</string>

         <string id="Proxy_default_ie_autodetect_Desc">Standard-IE-Verbindungseinstellung. Mit dieser Einstellung wird die automatische Erkennung der Einstellungen unter „Internetoptionen/LAN-Einstellungen“ eingeschaltet.</string>

         <string id="Default_proxy_server">Standard-Proxy-Server</string>

         <string id="Default_proxy_server_Desc">Standard-IE-Verbindungseinstellung für Proxy-Server. Damit wird der Proxy-Server festgelegt, der unter „Internetoptionen/LAN-Einstellungen“ verwendet werden soll.</string>

         <string id="Update_Java_Proxy">Proxy-Server für Java-Applet festlegen</string>

         <string id="Update_Java_Proxy_Desc">Legt den Java-Proxy-Server für die direkte Herstellung einer Verbindung unter Umgehung der Browsereinstellung fest. Legt den Java-Proxy-Server für die Verwendung der Client IP-Transparenz zur Netzwerkumleitung für das Java-Applet fest. Legt den Standardwert zur Wiederherstellung der Original-Java-Proxy-Einstellungen fest.</string>

         <string id="Use_Client_IP">Client IP-Transparenz für Java-Proxy-Server verwenden</string>

         <string id="Use_Direct_Connect">Direkte Verbindung für Java-Proxy-Server verwenden</string>

         <string id="Use_Default">Standardwert für Java-Proxy-Server verwenden</string>

         <string id="Enable_white_list">Positivliste aktivieren</string>

         <string id="Enable_black_list">Schwarze Liste aktivieren</string>

         <string id="Horizon_HTML5_FEATURES">Horizon HTML5-Funktionen</string>

         <string id="Enable_HTML5_FEATURES">Horizon HTML5-Funktionen aktivieren</string>

         <string id="Enable_HTML5_FEATURES_Desc">Horizon HTML5-Funktionen aktivieren. Wenn diese Richtlinie auf „Aktiviert“ festgelegt ist, können Horizon HTML5 Multimedia-Umleitung, Geolocation-Umleitung, Browserumleitung oder Medienoptimierung für Microsoft Teams verwendet werden.  Wenn diese Richtlinie auf „Deaktiviert“ festgelegt ist, kann keine Horizon HTML5-Funktion verwendet werden. Die Einstellung wird nach der nächsten Anmeldung wirksam.</string>

         <string id="HTML5FEATURES_Disable_AudoDetect_Intranet">Automatische Intranet-Erkennung deaktivieren</string>

         <string id="HTML5FEATURES_Disable_AudoDetect_Intranet_Desc">Wenn die Richtlinie aktiviert ist, werden die Intranet-Einstellungen „Alle lokalen Sites (Intranet), die nicht in anderen Zonen aufgeführt sind, einbeziehen“ und „Alle Sites, die den Proxyserver umgehen, einbeziehen“ bei der nächsten Anmeldung deaktiviert. Wenn die Richtlinie deaktiviert ist, wird keine Änderung an der lokalen Intranet-Zone in IE vorgenommen.

Hinweis: Diese Richtlinie muss auf „Aktiviert“ festgelegt sein, wenn (1) der Edge-Browser für die Horizon HTML5 Multimedia-Umleitung aktiviert ist, oder (2) die Geolocation-Umleitung aktiviert ist.</string>

         <string id="Horizon_HTML5MMR">Horizon HTML5 Multimedia-Umleitung</string>

         <string id="Enable_HTML5_MMR">Horizon HTML5 Multimedia-Umleitung aktivieren</string>

         <string id="Enable_HTML5_MMR_Desc">Horizon HTML5 Multimedia-Umleitung aktivieren. Die Einstellung wird nach der nächsten Anmeldung wirksam.</string>

         <string id="HTML5MMRUrlList">URL-Liste für Horizon HTML5 Multimedia-Umleitung aktivieren.</string>

         <string id="HTML5MMRUrlBlockList">URL-Liste für Horizon HTML5 Multimedia-Umleitung ausschließen.</string>

         <string id="HTML5MMRUrlList_Desc">Legt die URL-Liste für die Aktivierung der Horizon HTML5 Multimedia-Umleitung fest. Geben Sie das URL-Muster in der Spalte „Wertname“ an, wie z. B. „https://www.youtube.com/*“. Die Spalte „Wert“ sollte leer sein und ist für die zukünftige Verwendung vorgesehen.</string>

         <string id="HTML5MMRUrlBlockList_Desc">Gibt die URL-Liste an, die von der Horizon HTML5 Multimedia-Umleitung ausgenommen werden soll. Geben Sie das URL-Muster in der Spalte „Wertname“ an, wie z. B. „https://www.youtube.com/*“. Die Spalte „Wert“ ist für die zukünftige Verwendung reserviert und sollte leer sein.</string>

         <string id="HTML5MMR_Enable_Chrome">Chrome-Browser für Horizon HTML5 Multimedia-Umleitung aktivieren</string>

         <string id="HTML5MMR_Enable_Chrome_Desc">Diese Richtlinie wird nur verwendet, wenn die Horizon HTML5 Multimedia-Umleitung aktiviert ist. Wenn sie nicht konfiguriert ist, lautet der Standardwert wie die Einstellung für „Horizon HTML5 Multimedia-Umleitung aktivieren“.</string>

         <string id="HTML5MMR_Enable_Edge">Vorgängerversion des Microsoft Edge-Browsers für Horizon HTML5 Multimedia-Umleitung aktivieren</string>

         <string id="HTML5MMR_Enable_Edge_Desc">Diese Richtlinie wird nur verwendet, wenn die Horizon HTML5 Multimedia-Umleitung aktiviert ist. Wenn sie nicht konfiguriert ist, lautet der Standardwert wie die Einstellung für „Horizon HTML5 Multimedia-Umleitung aktivieren“. </string>

         <string id="HTML5MMR_Enable_Edge_Chromium">Microsoft Edge (Chromium)-Browser für Horizon HTML5 Multimedia-Umleitung aktivieren</string>

         <string id="HTML5MMR_Enable_Edge_Chromium_Desc">Diese Richtlinie wird nur verwendet, wenn die Horizon HTML5 Multimedia-Umleitung aktiviert ist. Wenn sie nicht konfiguriert ist, lautet der Standardwert wie die Einstellung für „Horizon HTML5 Multimedia-Umleitung aktivieren“. </string>

         <string id="HTML5MMR_Auto_Adjust_Visual_Effect">Visuelle Effekte von Fenstern automatisch anpassen</string>

         <string id="HTML5MMR_Auto_Adjust_Visual_Effect_Desc">Diese Richtlinie wird verwendet, um die visuellen Effekte des Fensters automatisch für die Horizon HTML5 Multimedia-Umleitung anzupassen. Wenn die Richtlinie weder konfiguriert noch aktiviert ist, wird keine automatische Anpassung der visuellen Effekte von Fenstern durchgeführt.</string>

         <string id="Horizon_GEO_REDIR">Horizon Geolocation-Umleitung</string>

         <string id="Enable_GEO_REDIR">Horizon Geolocation-Umleitung aktivieren</string>

         <string id="Enable_GEO_REDIR_Desc">Aktivieren Sie die Horizon Geolocation-Umleitungsfunktion. Die Einstellung wird nach der nächsten Anmeldung wirksam.</string>

         <string id="Enable_GEO_REDIR_For_Chrome">Horizon Geolocation-Umleitung für Chrome Browser aktivieren</string>

         <string id="Enable_GEO_REDIR_For_Chrome_Desc">Horizon Geolocation-Umleitungsfunktion für Chrome Browser aktivieren. Die Einstellung wird nach der nächsten Anmeldung wirksam.</string>

         <string id="Enable_GEO_REDIR_For_Edge_Chromium">Horizon Geolocation-Umleitung für Microsoft Edge-Browser (Chromium) aktivieren</string>

         <string id="Enable_GEO_REDIR_For_Edge_Chromium_Desc">Horizon Geolocation-Umleitungsfunktion für Microsoft Edge-Browser (Chromium) aktivieren. Die Einstellung wird nach der nächsten Anmeldung wirksam.</string>

         <string id="GeoRedirUrlList">Aktivieren Sie die URL-Liste für die Horizon Geolocation-Umleitung.</string>

         <string id="GeoRedirUrlList_Desc">Gibt die URL-Liste zum Aktivieren der Geolocation-Umleitungsfunktion an. Geben Sie das URL-Muster in der Spalte „Wertname“ an, wie z. B. „https://www.youtube.com/*“. Die Spalte „Wert“ sollte leer sein und ist für die zukünftige Verwendung vorgesehen. Diese URL-Liste wird von der (1) Horizon Geolocation-Umleitungserweiterung für Chrome- und Microsoft Edge (Chromium)-Browser in allen RDSH- und VDI-Umgebungen sowie vom (2) Horizon Geolocation-Umleitungs-Plug-In für Internet Explorer in RDSH- und Windows 7 VDI-Umgebungen verwendet.</string>

         <string id="GeoRedirDistanceDelta">Mindestentfernung zum Melden von Standort-Updates festlegen</string>

         <string id="GeoRedirDistanceDelta_Desc">Bestimmt die Mindestentfernung zwischen einem Standort-Update auf dem Client und dem zuletzt an den Agent gemeldeten Update, für das der neue Standort an den Agent gemeldet werden muss. Standardmäßig wird die Mindestentfernung 75 Meter verwendet.</string>

         <string id="Horizon_BROWSER_REDIR">Horizon-Browser-Umleitung</string>

         <string id="Enable_BROWSER_REDIR">Horizon-Browserumleitung aktivieren</string>

         <string id="Enable_BROWSER_REDIR_Desc">Funktion „Horizon-Browserumleitung aktivieren“. Die Einstellung wird nach der nächsten Anmeldung wirksam. Beachten Sie, dass durch die Aktivierung der Horizon-Browserumleitung auch die erweiterte Horizon-Browserumleitung aktiviert wird.</string>

         <string id="Enable_BROWSER_REDIR_For_Chrome">„Horizon-Browserumleitung aktivieren“ für Chrome-Browser</string>

         <string id="Enable_BROWSER_REDIR_For_Chrome_Desc">Funktion „Horizon-Browserumleitung aktivieren“ für Chrome-Browser. Die Einstellung wird nach der nächsten Anmeldung wirksam.</string>

         <string id="Enable_BROWSER_REDIR_For_Edge_Chromium">Funktion „Horizon-Browserumleitung aktivieren“ für Microsoft Edge-Browser (Chromium) aktivieren</string>

         <string id="Enable_BROWSER_REDIR_For_Edge_Chromium_Desc">Funktion „Horizon-Browserumleitung aktivieren“ für Microsoft Edge-Browser (Chromium) aktivieren. Die Einstellung wird nach der nächsten Anmeldung wirksam.</string>

         <string id="BrowserRedirFallbackWhitelistErr">Automatisches Fallback nach einem Whitelist-Verstoß aktivieren</string>

         <string id="BrowserRedirFallbackWhitelistErr_Desc">Wenn Sie zu einer URL von einer Registerkarte navigieren, die mit der Browserumleitung umgeleitet wurde, indem Sie sie in die benutzerdefinierte Adressleiste oder in die Adressleiste des Browsers eingeben oder von der umgeleiteten Registerkarte aus browsen, und wenn die neue URL nicht in der Liste der URLs für die Browserumleitung oder die erweiterte Browserumleitung aufgeführt ist, wird die neue URL automatisch wieder auf den Agent geladen, wenn diese Einstellung aktiviert ist. Wenn sich die neue URL zu diesem Zeitpunkt auch in der Liste der erweiterten Browserumleitung befindet, wird sie über die erweiterte Browserumleitung umgeleitet. Beachten Sie, dass beim Versuch, zu einer URL zu navigieren, die nicht unter „URL-Liste für Horizon-Browserumleitung aktivieren“ oder unter „URL-Liste für erweiterte Horizon-Browserumleitung aktivieren“ festgelegt ist, unabhängig von dieser Einstellung sofort auf den Agent zum Abrufen und Rendern zurückgegriffen wird.</string>

         <string id="BrowserRedirFetchFromServer">Aktivieren des Agent-seitigen Abrufens für Browserumleitungsfunktion</string>

         <string id="BrowserRedirFetchFromServer_Desc">Aktivieren Sie das Abrufen von Websiteinhalten aus dem Agent statt aus dem Client, wenn Sie die Browserumleitungsfunktion verwenden. Diese Einstellung ist standardmäßig deaktiviert.</string>

         <string id="BrowserRedirShowErrPage">Vor dem automatischen Fallback eine Seite mit Fehlerinformationen anzeigen</string>

         <string id="BrowserRedirShowErrPage_Desc">Diese Einstellung wird nur verwendet, wenn die Option „Automatisches Fallback nach einem Whitelist-Verstoß aktivieren“ aktiviert ist und ein Whitelist-Verstoß vorliegt. Wenn diese Einstellung aktiviert ist, wird in diesem Fall eine Seite mit einem Countdown von 5 Sekunden angezeigt, nach dem die Registerkarte automatisch auf das Abrufen und Rendern der URL zurückgreift, die den Verstoß auf dem Agenten verursacht hat. Wenn diese Einstellung deaktiviert ist, greift die Registerkarte direkt auf das agentenseitige Rendern zurück, ohne dass dem Benutzer eine 5-sekündige Warnung angezeigt wird.</string>

         <string id="BrowserRedirUrlList">URL-Liste für Horizon-Browserumleitung aktivieren</string>

         <string id="BrowserRedirUrlList_Desc">Gibt alle URLs für die Browserumleitungsfunktion an. Diese URLs können entweder durch Eingabe in der Adressleiste von Chrome oder in der benutzerdefinierten Adressleiste aufgerufen werden. Diese URLs können auch aufgerufen werden, indem man ausgehend von einer anderen URL in der Liste oder von einer beliebigen agentenseitig gerenderten Seite zu ihnen navigiert. Geben Sie das URL-Muster in der Spalte „Wertname“ an, wie z. B. „https://www.youtube.com/*“. Die Spalte „Wert“ sollte leer sein und ist für die zukünftige Verwendung vorgesehen. Wenn eine URL mit einem Muster sowohl in der URL-Liste für die Browserumleitung als auch in der Liste für die erweiterte Browserumleitung übereinstimmt, hat die erweiterte Browserumleitung Vorrang.</string>

         <string id="EnhBrowserRedirUrlList">URL-Liste für erweiterte Horizon-Browserumleitung aktivieren</string>

         <string id="EnhBrowserRedirUrlList_Desc">Gibt alle URLs für die Funktion „Erweiterte Browserumleitung“ an. Diese URLs können entweder durch Eingabe in die Adressleiste von Chrome, durch Navigation zu ihnen ausgehend von einer anderen URL in der Liste oder von einer beliebigen agentenseitig gerenderten Seite aufgerufen werden. Geben Sie das URL-Muster in der Spalte „Wertname“ an, wie z. B. „https://www.youtube.com/*“. Die Spalte „Wert“ sollte leer sein und ist für die zukünftige Verwendung vorgesehen. Wenn eine URL mit einem Muster sowohl in der URL-Liste für die Browserumleitung als auch in der Liste für die erweiterte Browserumleitung übereinstimmt, hat die erweiterte Browserumleitung Vorrang.</string>

         <string id="BrowserRedirNavUrlList">Navigations-URL-Liste für Horizon-Browserumleitung aktivieren</string>

         <string id="BrowserRedirNavUrlList_Desc">Gibt die URLs an, zu denen ein Benutzer navigieren darf, entweder durch direkte Eingabe in die benutzerdefinierte Adressleiste oder durch Navigation von einer URL in der anderen Liste. Diese URLs können nicht aufgerufen werden, indem Sie sie direkt in die Adressleiste von Chrome eingeben oder von einer agentenseitig gerenderten Seite aus zu ihnen navigieren. Geben Sie das URL-Muster in der Spalte „Wertname“ an, wie z. B. „https://www.youtube.com/*“. Die Spalte „Wert“ sollte leer sein und ist für die zukünftige Verwendung vorgesehen.</string>

         <string id="Horizon_WebRTC_REDIR_FEATURES">Horizon WebRTC-Umleitungsfunktionen</string>

         <string id="Enable_Teams_Redir">Medienoptimierung für Microsoft Teams aktivieren</string>

         <string id="Enable_Teams_Redir_Desc">Diese Einstellung wird zum Aktivieren oder Deaktivieren der Microsoft Teams-Optimierung verwendet.

Wenn Horizon Agent installiert ist, wird ein teamsEnabled-Registrierungsschlüssel auf dem Agent erstellt, der die Microsoft Teams-Optimierung ermöglicht.  Standardmäßig können Benutzer die Microsoft Teams-Optimierung nutzen oder nicht nutzen, indem sie die Einstellung „Medienoptimierung für WebRTC-basierte Anwendungen“ in Horizon Client konfigurieren.

Wenn diese Richtlinie auf „Aktiviert“ festgelegt ist, ist die Microsoft Teams-Optimierung aktiviert. Wenn diese Option und die Option „Clientseitige WebRTC-Optimierung erzwingen“ aktiviert sind, wird die Teams-Medienoptimierung auf dem Endpunkt erzwungen, und alle Clienteinstellungen oder andere Verwaltungsrichtlinien (z. B. Benutzerrichtlinien auf Chrome-Ebene für Chrome-Client) werden ignoriert. Wenn diese Option aktiviert ist und die Option „Clientseitige WebRTC-Optimierung erzwingen“ deaktiviert ist, hat der Benutzer die Möglichkeit, die Microsoft Teams-Optimierung zu verwenden oder nicht zu verwenden, indem er die Einstellung „Medienoptimierung für WebRTC-basierte Anwendungen“ von Horizon Client konfiguriert.

Wenn für die Richtlinie „Deaktiviert“ festgelegt wurde, ist die Microsoft Teams-Optimierung deaktiviert und kann nicht verwendet werden. Die Horizon Client-Einstellung „Medienoptimierung für WebRTC-basierte Anwendungen“ hat keine Auswirkungen.

Standardmäßig ist diese Richtlinie auf „Nicht konfiguriert“ festgelegt. Wenn die Richtlinie jedoch geändert und dann wieder in „Nicht konfiguriert“ geändert wird, wird der teamsEnabled-Registrierungsschlüssel entfernt, und die Microsoft Teams-Optimierung wird nicht verwendet.

Die Einstellung wird nach der nächsten Anmeldung wirksam.</string>

         <string id="Enable_Electron_App_Redir">Medienoptimierung für allgemeine Electron Apps aktivieren</string>

         <string id="Enable_Electron_App_Redir_Desc">Diese Einstellung wird zum Aktivieren oder Deaktivieren der Electron-App-Optimierung verwendet.

Bei „Aktiviert“ oder „Nicht konfiguriert“ ist die Electron-App-Optimierung aktiviert. Wenn Sie außerdem erzwingen möchten, dass der Endbenutzer die Optimierung verwendet (sofern auf dem Endpunkt unterstützt), wählen Sie „Aktiviert“ und dann „Clientseitige WebRTC-Optimierung erzwingen“ aus. „Nicht konfiguriert“ berücksichtigt die Clienteinstellung, sofern verfügbar.
Details:
Wenn diese Option aktiviert ist und die Option „Clientseitige WebRTC-Optimierung erzwingen“ deaktiviert ist, hat der Benutzer die Möglichkeit, die Electron-App-Optimierung zu verwenden oder nicht zu verwenden, indem er die Einstellung „Medienoptimierung für WebRTC-basierte Anwendungen“ von Horizon Client konfiguriert. Wenn diese Option aktiviert ist, wird die Electron-App-Medienoptimierung für die Endpunkt- und Client-Einstellung erzwungen, oder eine andere Administratorrichtlinie (z. B. die Benutzerrichtlinie auf Chrome-Ebene für Chrome Client) wird ignoriert.
Die Standardeinstellung für die Electron-App-Optimierung ist „Nicht konfiguriert“, wodurch die Electron-App-Optimierung aktiviert wird und der Benutzer die Einstellung „Medienoptimierung für WebRTC-basierte Anwendungen“ konfigurieren kann.
Bei „Deaktiviert“ ist die Electron-App-Optimierung deaktiviert und kann nicht verwendet werden. Die Horizon Client-Einstellung „Medienoptimierung für WebRTC-basierte Anwendungen“ hat keine Auswirkungen.

Die Einstellung wird nach der nächsten Anmeldung wirksam.</string>

         <string id="Horizon_WebRTC_SDK_WEBAPP">Unterstützung der Horizon WebRTC-Umleitungs-SDK-Web-App</string>

         <string id="WebRTC_SDK_Enable_Web_App_Redir">Medienoptimierung für Web-Anwendungen aktivieren</string>

         <string id="WebRTC_SDK_Enable_Web_App_Redir_Desc">Diese Einstellung wird zum Aktivieren oder Deaktivieren der Web-App-Optimierung verwendet. Bei „Aktiviert“ ist die Web-App-Optimierung aktiviert.</string>

         <string id="WebRTC_SDK_Enable_Web_App_Redir_Chrome">Chrome-Browser für Unterstützung der Horizon WebRTC-Umleitungs-SDK-Web-App aktivieren</string>

         <string id="WebRTC_SDK_Enable_Web_App_Redir_Chrome_Desc">Diese Richtlinie wird nur genutzt, wenn die Unterstützung der Horizon WebRTC-Umleitungs-SDK-Web-App „Aktiviert“ ist. Wenn er nicht konfiguriert ist, ist der Wert identisch mit dem Enablement „Medienoptimierung für Web-Anwendungen aktivieren“.</string>

         <string id="WebRTC_SDK_Enable_Web_App_Redir_Edge">Chromium Edge-Browser für Unterstützung der Horizon WebRTC-Umleitungs-SDK-Web-App aktivieren</string>

         <string id="WebRTC_SDK_Enable_Web_App_Redir_Edge_Desc">Diese Richtlinie wird nur genutzt, wenn die Unterstützung der Horizon WebRTC-Umleitungs-SDK-Web-App „Aktiviert“ ist. Wenn er nicht konfiguriert ist, ist der Wert identisch mit dem Enablement „Medienoptimierung für Web-Anwendungen aktivieren“.</string>

         <string id="WebRTC_SDK_Web_App_Redir_Allow_List">URL-Liste für Unterstützung der Horizon WebRTC-Umleitungs-SDK-Web-App aktivieren</string>

         <string id="WebRTC_SDK_Web_App_Redir_Allow_List_Desc">Gibt alle URLs für die Unterstützung der Horizon WebRTC-Umleitungs-SDK-Web-App an. Diese URLs können durch Eingabe in der Adressleiste von Chrome aufgerufen werden. Diese URLs können auch aufgerufen werden, indem man ausgehend von einer anderen URL in der Liste oder von einer beliebigen agentenseitig gerenderten Seite zu ihnen navigiert. Geben Sie das URL-Muster in der Spalte „Wertname“ an, wie z. B. „https://www.youtube.com/*“. Die Spalte „Wert“ sollte leer sein und ist für die zukünftige Verwendung vorgesehen.</string>

         <string id="Enable_AEC_Teams_Redir">Akustische Software-Echounterdrückung zur Medienoptimierung für Microsoft Teams aktivieren</string>

         <string id="Enable_AEC_Teams_Redir_Desc">Mit dieser Einstellung können Sie die akustische Software-Echounterdrückung (Software Acoustic Echo Cancellation, AEC) zur Medienoptimierung für Microsoft Teams konfigurieren.

Wenn „Aktiviert“, ist AEC in der Software aktiviert. Aktivieren Sie „Empfohlenen AEC-Algorithmus verwenden“, um eine optimale Audioqualität und -leistung zu erzielen. Deaktivieren Sie „Empfohlenen AEC-Algorithmus verwenden“, um einen AEC-Algorithmus zu verwenden, der zwar weniger CPU verwendet, aber die Audioqualität beeinträchtigt. Diese Option ist nützlich für Low-End-Prozessoren mit geringer Gleitkommaleistung. Die Verwendung des empfohlenen AEC-Algorithmus wird dringend empfohlen und ist in den meisten Fällen bestens geeignet.

Wenn „Deaktiviert“, wird AEC in der Software deaktiviert und nicht mehr verwendet.

Wenn „Nicht konfiguriert“, wird AEC in der Software unter Verwendung des empfohlenen Algorithmus aktiviert. Bei Verwendung eines Windows-Clients wird Software-AEC verwendet, wenn Hardware-AEC nicht verfügbar ist. Wenn Hardware-AEC verfügbar ist (z. B. wenn das Headset über integrierte AEC verfügt), wird Software-AEC nicht verwendet. Bei Verwendung eines Nicht-Windows-Clients wird Software-AEC unabhängig davon verwendet, ob Hardware-AEC verfügbar ist.</string>

         <string id="Enable_Datachannel_Teams_Redir">Datenkanal für Medienoptimierung für Microsoft Teams aktivieren</string>
         <string id="Enable_Datachannel_Teams_Redir_Desc">Diese Einstellung wird verwendet, um den Datenkanal für die Medienoptimierung für Microsoft Teams zu aktivieren oder zu deaktivieren.

Wenn „Aktiviert“, kann der Datenkanal für die Medienoptimierung für Microsoft Teams verwendet werden, und Funktionen, die den Datenkanal benötigen, sind verfügbar (z. B. Liveuntertitel).

Wenn „Deaktiviert“, kann der Datenkanal nicht für die Medienoptimierung für Microsoft Teams verwendet werden, und Funktionen, die den Datenkanal benötigen, sind nicht verfügbar.

Wenn „Nicht konfiguriert“, ist der Datenkanal aktiviert.</string>

         <string id="Video_Cpu_Overuse_Threshold">Schwellenwert für CPU-Überauslastung konfigurieren</string>

         <string id="Video_Cpu_Overuse_Threshold_Desc"> Wenn die CPU-Auslastung über dem Schwellenwert liegt, wird die gesendete Videoauflösung reduziert, wodurch die CPU-Auslastung des Clients gesenkt wird. Der Standardschwellenwert beträgt 85. Um die Client-CPU während Videoanrufen zu reduzieren, legen Sie diese Richtlinie auf „Aktiviert“ mit einem Wert kleiner als 85 fest. Legen Sie diese Richtlinie auf „Deaktiviert“ oder „Nicht konfiguriert“ fest, um den Standardschwellenwert von 85 zu verwenden. Um keine CPU-Überauslastung zu erkennen, legen Sie diese Richtlinie auf „Aktiviert“ mit einem Wert von 0 fest. Diese Einstellung wird bei der nächsten Anmeldung übernommen.</string>

         <string id="Enable_Sharing_Client_Screen_InAppSession">Bei Verwendung der Microsoft Teams-Anwendung als veröffentlichte Anwendung die Freigabe des Client-Desktop-Bildschirms ermöglichen</string>

         <string id="Enable_Sharing_Client_Screen_InAppSession_Desc">Wenn Sie die Microsoft Teams-Anwendung als veröffentlichte Anwendung verwenden, gibt die Bildschirmfreigabefunktion den Client-Desktop-Bildschirm frei. Deaktivieren Sie diese Richtlinie, um die Bildschirmfreigabefunktion zu deaktivieren, wenn Sie Microsoft Teams als veröffentlichte Anwendung verwenden. Wenn die Richtlinie aktiviert oder nicht konfiguriert ist, kann der Client-Desktop-Bildschirm freigegeben werden.</string>

         <string id="Enable_E911">E911 für Microsoft Teams aktivieren</string>

         <string id="Enable_E911_Desc">Während Microsoft Teams im optimierten Modus ausgeführt wird, sendet der Client E911-Daten an Microsoft. Um die Freigabe von E911-Daten für Microsoft zu deaktivieren, wählen Sie „Deaktiviert“ aus. Bei Auswahl von „Aktiviert“ oder „Nicht konfiguriert“ werden die E911-Daten für Microsoft freigegeben.</string>

         <string id="Enable_HID">Aktivieren mithilfe der Schaltfläche „Client-HID-Geräte“ für Microsoft Teams</string>

         <string id="Enable_HID_Desc">Während Microsoft Teams im optimierten Modus ausgeführt wird, kann der Benutzer über die Schaltfläche „Client-HID-Geräte“ mit Microsoft Teams interagieren. Um die Unterstützung von Client-HID-Geräten zu deaktivieren, wählen Sie „Deaktiviert“ aus. Bei Auswahl von „Aktiviert“ oder „Nicht konfiguriert“ wird die Unterstützung von Client-HID-Geräten zugelassen.</string>

         <string id="Enable_Webrtc_Appshare">Individuelle Anwendungsfreigabe für Microsoft Teams aktivieren</string>

         <string id="Enable_Webrtc_Appshare_Desc">Während Microsoft Teams im optimierten Modus ausgeführt wird, ermöglicht diese Option dem Benutzer die Freigabe einzelner Anwendungen. Um die Anwendungsfreigabe zu deaktivieren, wählen Sie „Deaktiviert“ aus. Wenn „Aktiviert“ oder „Nicht konfiguriert“, wird die Anwendungsfreigabe zugelassen.</string>

         <string id="Enable_Webrtc_Appshare_Givecontrol">Freigabesteuerung für die gemeinsame Nutzung einzelner Anwendungen für Microsoft Teams aktivieren</string>

         <string id="Enable_Webrtc_Appshare_Givecontrol_Desc">Während Microsoft Teams im optimierten Modus ausgeführt wird, kann der Benutzer mit dieser Option die Steuerung einer gemeinsam genutzte Einzelanwendung übernehmen. Um die Steuerung während der gemeinsamen Nutzung einzelner Anwendungen zu deaktivieren, legen Sie diese Richtlinie auf „Deaktiviert“ fest. Bei Auswahl von „Aktiviert“ oder „Nicht konfiguriert“ ist die Steuerung während der Freigabe einzelner Anwendungen zulässig.</string>

         <string id="CustomBackgroundImages">Benutzerdefinierte Hintergrundbilder in Microsoft Teams</string>

         <string id="Enable_Background_Effects">Hintergrundeffekte für Microsoft Teams aktivieren</string>

         <string id="Enable_Background_Effects_Desc">Während Microsoft Teams im optimierten Modus ausgeführt wird, können Benutzer einen virtuellen Hintergrund für Anrufe und Meetings auswählen. Um die Unterstützung für Hintergrundeffekte zu deaktivieren, wählen Sie „Deaktiviert“ aus. Bei Auswahl von „Aktiviert“ oder „Nicht konfiguriert“ wird die Unterstützung von Hintergrundeffekten zugelassen.</string>

         <string id="ForceEnableCustomBackgroundImages">Aktivierung oder Deaktivierung benutzerdefinierter Hintergrundbilder für Microsoft Teams erzwingen</string>

         <string id="ForceEnableCustomBackgroundImagesDesc">Während Microsoft Teams im optimierten Modus ausgeführt wird, können Benutzer benutzerdefinierte Hintergrundbilder bei Anrufen und in Besprechungen anwenden. Wählen Sie „Deaktiviert“ aus, um die Unterstützung für benutzerdefinierte Hintergrundbilder zu deaktivieren. Wählen Sie „Aktiviert“ aus, um die ausschließliche Verwendung benutzerdefinierter Hintergrundbilder durch Benutzer zu erzwingen und zu verhindern, dass auf der Microsoft Teams-Benutzeroberfläche „Hintergrundeffekte“ bereitgestellte Bestandsbilder angewendet werden. Bei Auswahl von „Nicht konfiguriert“ können Benutzer nach eigenem Ermessen zwischen der Verwendung von benutzerdefinierten Hintergrundbildern und den von Microsoft Teams bereitgestellten Benutzeroberflächenbildern wechseln.</string>

         <string id="CustomBackgroundImagesFolderPath">Ordner für benutzerdefinierte Hintergrundbilder für Microsoft Teams eingeben</string>

         <string id="CustomBackgroundImagesFolderPathDesc">Während Microsoft Teams im optimierten Modus ausgeführt wird, können Benutzer benutzerdefinierte Hintergrundbilder anwenden, die in einem vom Administrator hochgeladenen Bilderordner ausgewählt wurden. Bei Auswahl von „Deaktiviert“ oder „Nicht konfiguriert“ sollten die Bilder in folgenden Ordner hochgeladen werden: C:\Program Files\Common Files\Omnissa\Remote Experience\x64\MSTeamsBackgroundImages. Wählen Sie zur Verwendung eines anderen Ordners die Option „Aktiviert“ aus und geben Sie den Pfad des Ordners im Textfeld des Ordners für benutzerdefinierte Hintergrundbilder an, wie z. B. „C:\Users\<USER>\CustomBackgroundImagesFolder“.</string>

         <string id="CustomBackgroundDefaultImageName">Benutzerdefiniertes Standardhintergrundbild auswählen, das bei einem Benutzerfehler angewendet werden soll</string>

         <string id="CustomBackgroundDefaultImageNameDesc">Geben Sie einen Namen für das benutzerdefinierte Standardbild an, das für den Fall angewendet werden soll, dass der Benutzer den Registrierungswert „imageName“ leer lässt oder einen ungültigen Namen für das benutzerdefinierte Bild eingibt, wenn die Funktion für benutzerdefinierte Hintergrundbilder aktiviert ist.</string>

         <string id="Disable_Mirrored_Video">Gespiegelte Kameravorschau in Microsoft Teams deaktivieren</string>

         <string id="Disable_Mirrored_Video_Desc">Standardmäßig wird das eigene Video für Microsoft Teams im optimierten Modus gespiegelt. Wenn Sie diese Option festlegen, wird das gespiegelte Video deaktiviert.</string>
         
         <string id="Enable_Webrtc_ProxyProbeUrl">Verwenden Sie die benutzerdefinierte Proxy-Prüf-URL, um den funktionierenden Proxy-Server zu ermitteln.</string>

         <string id="Enable_Webrtc_ProxyProbeUrl_Desc">Wenn mehrere Proxy-Server konfiguriert sind, geben Sie die benutzerdefinierte Proxy-Prüf-URL an, um den funktionierenden Proxy-Server zu testen und im Microsoft Teams-Anruf zu verwenden. Beispiel: https://teams.microsoft.com</string>

         <string id="Horizon_AppTap_Config">Horizon AppTap-Konfiguration</string>

         <string id="ProcessIgnoreList">Zu ignorierende Prozesse beim Erkennen einer leeren Anwendungssitzung</string>

         <string id="ProcessIgnoreList_Desc">Gibt die Liste der Prozesse an, die ignoriert werden sollen, wenn leere Anwendungssitzungen erkannt werden. Sie können entweder einen Prozessdateinamen oder einen vollständigen Pfad angeben. Diese Werte werden ohne Berücksichtigung der Groß-/Kleinschreibung ausgewertet. Umgebungsvariablen sind in Pfaden nicht zulässig. UNC-Netzwerkpfade sind zulässig (z. B. \\Omnissa\temp\app.exe).</string>

         <string id="VDI_disconnect_time_till_logoff">Zeitlimit für getrennte Sitzung (VDI)</string>

         <string id="VDI_disconnect_time_till_logoff_Desc">Gibt den Zeitraum an, nach dem sich eine getrennte VDI-Desktop-Sitzung automatisch abmeldet.
Wenn „Nie“ ausgewählt ist, werden keine getrennten VDI-Desktop-Sitzungen auf diesem Computer abgemeldet. Wenn „Sofort“ ausgewählt ist, werden Sitzungen, die getrennt werden, sofort abgemeldet.

Eine ähnliche Einstellung ist in Horizon Connection Server-Administrator vorhanden. Sie befindet sich in den Desktop-Pool-Einstellungen und heißt „Nach Trennung automatisch abmelden“. Wenn sowohl diese Einstellung als auch die Horizon Connection Server-Administrator-Einstellung konfiguriert sind, hat der hier ausgewählte Wert Vorrang.
Wenn Sie beispielsweise „Nie“ auswählen, wird verhindert, dass eine getrennte Sitzung (auf diesem Computer) abgemeldet wird, unabhängig davon, was über Horizon Connection Server-Administrator festgelegt ist.</string>

         <string id="RDS_idle_time_till_disconnect">RDS-Leerlaufzeit bis zum Trennen der Verbindung</string>

         <string id="RDS_idle_time_till_disconnect_Desc">Gibt den Zeitraum an, nach dem eine inaktive Remotedesktopdienste-Sitzung automatisch getrennt wird.
Wenn „Nie“ ausgewählt ist, werden keine Remotedesktopdienste-Sitzungen auf diesem Computer getrennt.</string>

         <string id="RDS_disconnect_time_till_logoff">RDS-Verbindungsunterbrechungszeit bis zur Abmeldung</string>

         <string id="RDS_disconnect_time_till_logoff_Desc">Gibt den Zeitraum an, nach dem eine getrennte Remotedesktopdienste-Sitzung automatisch abgemeldet wird.
Wenn „Nie“ ausgewählt ist, werden keine getrennten Remotedesktopdienste-Sitzungen auf diesem Computer abgemeldet.</string>

         <string id="RDS_active_time_till_disconnect">RDS-Verbindungszeit bis zum Trennen der Verbindung</string>

         <string id="RDS_active_time_till_disconnect_Desc">Gibt die maximale Zeit an, die eine Remotedesktopdienste-Sitzung aktiv sein kann, bevor sie automatisch getrennt wird.
Wenn „Nie“ ausgewählt ist, werden keine Remotedesktopdienste-Sitzungen auf diesem Computer getrennt.</string>

         <string id="RDS_end_session_time_limit">RDS-Sitzung beenden, wenn Zeitlimit erreicht ist</string>

         <string id="RDS_end_session_time_limit_Desc">Gibt an, ob eine Remotedesktopdienste-Sitzung, bei der die Zeit abgelaufen ist, beendet werden soll, anstatt die Verbindung zu trennen.
Wenn diese Option eingestellt ist, wird die Sitzung beendet (der Benutzer wird abgemeldet und die Sitzung wird vom Server gelöscht), nachdem die Zeitlimits für aktive oder inaktive Sitzungen erreicht wurden. Standardmäßig werden Remotedesktopdienste-Sitzungen getrennt, nachdem deren Zeitlimit erreicht wurde.</string>

         <string id="RDS_threshold_connecting_session">Schwellenwert für die Verbindungssitzung</string>

         <string id="RDS_threshold_connecting_session_Desc">Gibt die maximale Anzahl an Sitzungen an, die sich gleichzeitig bei der RDSH-Maschine anmelden können, wobei wiederaufgenommene Verbindungen ausgenommen werden.

Bei aktivierter Option wird der Sitzungsschwellenwert zunächst auf 20 festgelegt, sollte jedoch je nach Anwendungsfall geändert werden. Bei Auswahl von 0 wird der Schwellenwert für die Verbindungssitzung deaktiviert.

Diese Richtlinie ist standardmäßig deaktiviert. Wenn die Richtlinie nicht konfiguriert ist, wird der Schwellenwert für die Verbindungssitzung deaktiviert.</string>

         <string id="RDS_threshold_load_index">Schwellenwert für Lastindex</string>

         <string id="RDS_threshold_load_index_Desc">Gibt den Mindestladeindex an, ab dem die RDSH-Maschine mit der Ablehnung von Sitzungsanmeldungen beginnt, wobei wiederaufgenommene Verbindungen ausgenommen werden.

Bei aktivierter Option wird der Auslastungsschwellenwert zunächst auf 0 festgelegt, sollte jedoch je nach Anwendungsfall geändert werden. Bei Auswahl von 0 wird der Schwellenwert für den Ladeindex deaktiviert.

Diese Richtlinie ist standardmäßig deaktiviert. Wenn die Richtlinie nicht konfiguriert ist, wird der Schwellenwert für den Ladeindex deaktiviert.</string>

         <string id="Prewarm_disconnect_time_till_logoff">Zeitlimit für Vorwärmsitzung</string>

         <string id="Prewarm_disconnect_time_till_logoff_Desc">Gibt den Zeitraum an, nach dem sich eine Vorwärmsitzung automatisch abmeldet.</string>

         <string id="EnableUWPOnRDSH">Aktivieren der UWP-Unterstützung auf RDSH-Plattformen</string>

         <string id="EnableUWPOnRDSH_Desc">Mit dieser Richtlinie wird festgelegt, ob UWP-Anwendungen auf RDSH-Farmen mit einer Betriebssystemversion, die UWP-Anwendungen unterstützt, gescannt und gestartet werden können. Diese Richtlinie gilt nicht für Desktop-Betriebssystemplattformen wie VDI-App-Remoting. Wenn diese Option aktiviert ist, können UWP-Anwendungen als gehostete Apps von RDSH-Farmen verwendet werden. Sie müssen den wsnm-Dienst oder den RDSH-Server neu starten, damit das GPO wirksam wird. In der Omnissa-Dokumentation finden Sie Informationen zu den unterstützten Plattformen und darüber, ob diese Einstellung standardmäßig aktiviert oder deaktiviert ist.</string>

        <string id="HandleLegalNoticeInWindow">Rechtliche Hinweise im Fenster umleiten</string>

        <string id="HandleLegalNoticeInWindow_Desc">Wenn diese Richtlinie aktiviert ist, werden rechtliche Hinweise an den Horizon Client in einem Fenster mit der angegebenen Größe umgeleitet. Die Breite und Höhe in dieser Richtlinie werden in Pixel angegeben. Bei Monitoren mit hohen DPI-Werten werden die Größen auf der Grundlage der DPI-Werte multipliziert. Diese Funktionalität wird nur für RDSH-gehostete Anwendungen unterstützt.
Diese Richtlinie ist standardmäßig deaktiviert. Der RDSH-Server und der Horizon Client müssen neu gestartet werden, damit das GPO wirksam wird.</string>

        <string id="TIME_NEVER">Nie</string>

         <string id="TIME_1MIN">1 Minute</string>

         <string id="TIME_5MIN">5 Minuten</string>

         <string id="TIME_10MIN">10 Minuten</string>

         <string id="TIME_15MIN">15 Minuten</string>

         <string id="TIME_30MIN">30 Minuten</string>

         <string id="TIME_1HR">1 Stunde</string>

         <string id="TIME_2HR">2 Stunden</string>

         <string id="TIME_3HR">3 Stunden</string>

         <string id="TIME_6HR">6 Stunden</string>

         <string id="TIME_8HR">8 Stunden</string>

         <string id="TIME_10HR">10 Stunden</string>

         <string id="TIME_12HR">12 Stunden</string>

         <string id="TIME_18HR">18 Stunden</string>

         <string id="TIME_1D">1 Tag</string>

         <string id="TIME_2D">2 Tage</string>

         <string id="TIME_3D">3 Tage</string>

         <string id="TIME_4D">4 Tage</string>

         <string id="TIME_5D">5 Tage</string>

         <string id="TIME_1W">1 Woche</string>

         <string id="TIME_IMMEDIATELY">Sofort</string>

         <string id="EnableBatStatRedir">Aktivieren der Akkustandsumleitung</string>

         <string id="EnableDisplayNetworkState">Anzeige des Netzwerkzustands aktivieren</string>
         <string id="EnableDisplayNetworkStateExplain">Mit dieser Einstellung können Sie konfigurieren, ob Netzwerkzustandmeldungen auf der Horizon Client-Benutzeroberfläche angezeigt werden sollen. Wenn diese Einstellung aktiviert ist, erhält der Endbenutzer bei einer schlechten Netzwerkverbindung eine Benachrichtigung über den Netzwerkzustand. Wenn diese Einstellung deaktiviert ist, erhält der Endbenutzer bei einer schlechten Netzwerkverbindung keine Benachrichtigung über den Netzwerkzustand. Diese Eigenschaft ist standardmäßig aktiviert.</string>

         <string id="EnableBatStatRedir_Desc">Mit dieser Richtlinie wird festgelegt, ob der Akkustand umgeleitet wird. Ist diese Richtlinie nicht konfiguriert, ist die Akkustandsumleitung aktiviert.</string>
         <string id="Horizon_WaterMark">Wasserzeichen</string>
         <string id="Horizon_Watermark_Config">Konfiguration des Wasserzeichens</string>
         <string id="Desktop_Watermark_Configuration_Desc">Mit dieser Einstellung können Sie ein Wasserzeichen konfigurieren, das auf Ihrem virtuellen Desktop angezeigt wird. Im Bereich „Text“ können Sie festlegen, was im Wasserzeichen angezeigt werden soll. Optionen:

%ViewClient_IP_Address%
%ViewClient_Broker_UserName%
%ViewClient_Broker_DomainName%
%COMPUTERNAME%
%USERDOMAIN%
%USERNAME%
%ViewClient_ConnectTime%   – Datum in Monat/Tag/Jahr
%ViewClient_ConnectTicks%  – Zeit in Stunde:Minute:Sekunde

Beispiel für „Text“:
%USERDOMAIN%\%USERNAME%
%COMPUTERNAME% am %ViewClient_ConnectTime%
%ViewClient_IP_Address%

Die Zeichenbegrenzung für den „Text“ beträgt 256 Zeichen und ist nach der Erweiterung auf 1.024 Zeichen begrenzt.

„Image Layout“ (Bildlayout) gibt das Layout des Wasserzeichens an. Unterstützt werden „Tile“ (Kachel), „Multiple“ (Mehrere) und „Center“ (Zentriert). „Multiple“ (Mehrere) zeigt das Wasserzeichen in der Mitte und in den Ecken an. Diese Einstellung wird für App-Sitzungen ignoriert, und als Layout wird immer eine Kachel angezeigt.
Mit „Text Rotation“ (Textdrehung) können Sie einen Rotationswinkel für den Text des Wasserzeichens auswählen.
Mit „Opacity“ (Deckkraft) können Sie die Transparenz des Textes festlegen.
„Rand“ legt die Entfernung zwischen dem Wasserzeichen und dem Rand des virtuellen Desktopbildschirms fest. Diese Einstellung gilt nur für das Kachel-Layout.
„Textfarbe“ gibt die Farbe des Wasserzeichentexts unter Verwendung der durch Leerzeichen getrennten RGB-Farbwerte im Dezimalformat an, und die Textkontur wird in kontrastierender Farbe dargestellt. Standardmäßig wird der Text in Weiß und der Umriss in Schwarz dargestellt.
„Schriftgröße“ gibt die Größe des Wasserzeichentexts an. Wenn dieser Wert 0 ist, wird die Standardschriftgröße angewendet.
„Aktualisierungsintervall“ gibt das Intervall in Sekunden an, in dem das Wasserzeichen aktualisiert wird. Wenn 0 angegeben wird, wird die Aktualisierung des Wasserzeichens deaktiviert. Der Höchstwert beträgt 86400 Sekunden (24 Stunden).
</string>
         <string id="Tile">Kachel</string>
         <string id="Multiple">Mehrere</string>
         <string id="Center">Zentriert</string>
         <string id="TextColor">Textfarbe</string>
         <string id="FontSize">Schriftgröße</string>
         <string id="RefreshInterval">Aktualisierungsintervall</string>
         <string id="BlockScreenCapture">Bildschirmaufnahmen blockieren</string>
         <string id="BlockScreenCapture_Desc">Legt fest, ob der Endbenutzer von seinem Endpunkt aus Bildschirmfotos von seinem virtuellen Desktop oder seiner Remoteanwendung aufnehmen kann. Diese Einstellung kann nur auf dem Horizon Client 2106 und höher für Windows und für Mac erzwungen werden. Die Standardeinstellung ist deaktiviert, sodass der Endbenutzer Bildschirmfotos von seinem Gerät erstellen kann.

Aktivieren: Verhindert, dass Endbenutzer von ihren Windows- oder macOS-Geräten aus Bildschirmfotos des virtuellen Desktops oder virtueller Anwendungen erstellen.

Deaktivieren: Ermöglicht es Endbenutzern, Bildschirmfotos von ihrem Endpunkt aus zu erstellen.


Mit „Bildschirmaufzeichnung für Horizon Mac Client zulassen“ wird festgelegt, ob Endbenutzer Bildschirmaufzeichnungen ihres virtuellen Desktops oder ihrer Remoteanwendung von ihrem Endpunkt aus durchführen können, wenn das GPO „Bildschirmaufnahmen blockieren“ aktiviert ist. Diese Einstellung kann nur auf der Horizon Client 2309 für Mac und höher erzwungen werden. Standardmäßig ist diese Option nicht aktiviert. Endbenutzer können daher keine Bildschirmaufzeichnungen von ihren Geräten aus erstellen.

Aktiviert: Ermöglicht es Endbenutzern, Bildschirmaufzeichnungen des virtuellen Desktops oder virtueller Anwendungen von ihren macOS-Geräten aus zu erstellen.

Nicht aktiviert: Verhindert, dass Endbenutzer Bildschirmaufzeichnungen von ihren macOS-Geräten aus erstellen können.</string>
         <string id="BlockThumbnailRepresentationWhenMinimized">Darstellung der Miniaturansicht bei Minimierung blockieren</string>
         <string id="BlockThumbnailRepresentationWhenMinimized_Desc">Legt fest, ob der Inhalt des Remote-Desktops angezeigt wird, wenn der Mauszeiger über die Miniaturansicht des Remote-Desktops bewegt wird und das Fenster minimiert ist.
Bei aktivierter Option wird das Symbol der Horizon Client-Anwendung anstelle des Inhalts des Remote-Desktops für die Fensterminiaturansicht und die Live-Vorschau angezeigt, wenn das Fenster minimiert wird.
Wenn diese Option deaktiviert oder nicht konfiguriert ist, wird der Snapshot des letzten Remote-Desktops vor dem Minimieren als Fensterminiatur und Live-Vorschau angezeigt. Dieses GPO wird nur auf Windows-Endpoints wirksam.</string>
         <string id="ScreenCaptureForMediaOffloaded">Bildschirmaufnahme für Medienauslagerungslösung</string>
         <string id="ScreenCaptureForMediaOffloaded_Desc">Ermöglichen Sie es Endbenutzern, Bildschirmaufnahmen für den VDI Agent-Desktop zu erstellen, wenn die Mediensitzung an Endpunkte ausgelagert wird.</string>
         <string id="AntiKeyLogger">Keylogger-Blockierung</string>
         <string id="AntiKeyLogger_Desc">Legt fest, ob der Endpunkt die Kommunikation zwischen der Tastatur und dem Horizon Client verschlüsselt, um Keylogger-Malware auf dem Endpunkt zu verhindern. Die anfängliche Verbindung mit dem Horizon Server ist unabhängig von der GPO-Einstellung auf der virtuellen Maschine immer geschützt. Nach der anfänglichen Authentifizierung bestimmt diese Einstellung, ob alle Eingaben auf dem Endpunkt verschlüsselt sind. Diese Einstellung kann nur auf dem Horizon Client 2111 für Mac und Horizon Client 2203 für Windows oder höher erzwungen werden. Die Standardeinstellung ist deaktiviert.

Aktivieren: Verschlüsseln Sie alle Tastatureingaben zwischen der Tastatur und dem Horizon Client.

Deaktivieren: Tastaturbefehle werden am Endpunkt normal übermittelt.</string>
         <string id="BlockSendInput">Blockieren von synthetischen Tastatureingaben</string>
         <string id="BlockSendInput_Desc">Gibt an, ob der Endpoint Skripts blockiert, die Tastatureingaben vom Endpoint in einen virtuellen Desktop oder eine virtuelle Anwendung automatisieren. Die anfängliche Verbindung mit dem Horizon Server ist unabhängig von der GPO-Einstellung auf der virtuellen Maschine immer geschützt. Nach der ersten Authentifizierung wird anhand dieser Einstellung festgelegt, ob alle synthetischen Tastatureingaben auf dem Endpoint blockiert werden. Diese Einstellung kann nur auf dem Horizon Client 2312 für Windows oder höher erzwungen werden. Diese Richtlinie ist standardmäßig deaktiviert.

Wenn „Keylogger-Blockierung“ nicht aktiviert ist, ist diese Einstellung unwirksam.

Aktivieren: Blockieren Sie alle synthetischen Tastatureingaben vom Endpoint in virtuelle Desktops oder virtuelle Anwendungen.

Deaktivieren: Horizon Client leitet synthetische Tastatureingaben wie gewohnt weiter.</string>
         <string id="AllowFIDO2AuthenticatorAccess">FIDO2-Authentifikatorzugriff zulassen</string>
         <string id="AllowFIDO2AuthenticatorAccess_Desc">Legt fest, ob Anwendungen im Remote-Desktop auf FIDO2-Authentifikatoren des Endpoints zugreifen können. Wenn diese Option deaktiviert ist, dürfen Anwendungen im Remote-Desktop nicht auf die FIDO2-Authentifikatoren des Endpoints zugreifen. Wenn diese Option aktiviert oder nicht konfiguriert ist, dürfen Anwendungen im Remote-Desktop auf die FIDO2-Authentifikatoren des Endpoints zugreifen.</string>
         <string id="FIDO2AllowList">FIDO2-Positivliste</string>
         <string id="FIDO2AllowList_Desc">Eine Liste der Anwendungen, die auf FIDO2-Authentifikatoren des Endpoints zugreifen können.

Die Syntax lautet:
   appname1.exe;appname2.exe

Wenn diese Einstellung nicht konfiguriert oder deaktiviert ist, wird die Standardliste verwendet. Die Standardliste lautet:
   chrome.exe;firefox.exe;msedge.exe</string>

         <string id="WaitForHybridJoin">Warten auf Hybrid-Beitritt konfigurieren</string>

         <string id="WaitForHybridJoin_Desc">Dieses Gruppenrichtlinienobjekt (GPO) steuert das Verhalten des Agenten in Bezug auf den Microsoft Hybrid Entra ID-Beitrittsprozess. Es legt fest, ob der Agent auf den Abschluss des Hybrid-Beitrittsprozesses warten soll, bevor er Desktop- oder Anwendungsanforderungen bearbeiten kann.

Deaktiviert oder nicht konfiguriert: Wenn diese Einstellung deaktiviert oder nicht konfiguriert ist, wartet der Agent nicht auf den Abschluss des Hybrid-Beitrittsprozesses. Das bedeutet, dass der Agent sofort mit der Bearbeitung von Anfragen beginnen kann, möglicherweise bevor die Maschine vollständig in Entra ID integriert ist.

Aktiviert: Wenn diese Option aktiviert ist, wartet der Agent, bis die Maschine den Hybrid-Beitrittsvorgang mit Entra ID erfolgreich abgeschlossen hat. Erst wenn dieser Prozess abgeschlossen ist, markiert sich der Agent selbst als VERFÜGBAR und zeigt damit an, dass er bereit ist, Desktop- oder Anwendungsanforderungen zu bearbeiten.

Die Aktivierung dieser Funktion ist entscheidend, um sicherzustellen, dass der Agent vollständig in Entra ID integriert ist, bevor er mit der Bearbeitung von Anforderungen beginnt. Diese Integration ist notwendig für Funktionen wie Single Sign-On (SSO) in Azure/Office-Ressourcen und dafür, dass das Gerät in Entra ID zu Verwaltungszwecken erkannt wird. Beachten Sie jedoch, dass die Aktivierung dieser Funktion zu einer erheblichen Verzögerung bei der Verfügbarkeit von Maschinen führen kann, da der Agent auf den Abschluss des Hybrid-Beitrittsprozesses wartet.
         </string>

         <string id="IpPrefix">Konfigurieren des von Horizon Agent verwendeten Subnetzes</string>

         <string id="IpPrefixDesc">Wenn Sie Horizon Agent auf einer virtuellen Maschine mit mehr als einer Netzwerkkarte installieren, müssen Sie das von Horizon Agent verwendete Subnetz konfigurieren. Mit dem Subnetz wird festgelegt, welche Netzwerkadresse Horizon Agent dem Verbindungsserver oder der Verbindungsserver-Instanz für Clientprotokollverbindungen bereitstellt.

Die Syntax lautet:
   n.n.n.n/m

In diesem Beispiel steht n.n.n.n für das TCP/IP-Subnetz und m für die Anzahl der Bits in der Subnetzmaske.

Beispielwert:
   ***********/21

In diesem Beispiel werden nur IP-Adressen im Bereich *********** bis ************* für die Verwendung durch den Horizon Agent akzeptiert.
         </string>

      </stringTable>

      <presentationTable>
         <presentation id="MaxCollaboratorCount">
            <decimalTextBox refId="MaxCollaboratorCount_DB" defaultValue="5">Maximum</decimalTextBox>
         </presentation>

         <presentation id="CollaborationEmailInviteDelimiter">
            <textBox refId="CollaborationEmailInviteDelimiter_TB">
               <label>Trennzeichen zwischen E-Mail-Adressen</label>
            </textBox>
         </presentation>

         <presentation id="CollaborationServerURLs">
            <listBox refId="CollaborationServerURLs_list">Liste externer Server-URLs und Servernamen</listBox>
         </presentation>

         <presentation id="ConnectionTicketTimeout">
            <decimalTextBox refId="ConnectionTicketTimeout_DB" defaultValue="120">Verbindungsticket, Zeitüberschreitung</decimalTextBox>
         </presentation>

         <presentation id="CredentialFilterExceptions">
            <textBox refId="CredentialFilterExceptions_TB">
               <label>Ausnahmen für Anmeldedatenfilter</label>
            </textBox>
         </presentation>
         <presentation id="RDPVcBridgeUnsupportedClients">
            <textBox refId="RDPVcBridgeUnsupportedClients_TB">
               <label>Nicht unterstützte RDPVcBridge-Clients</label>
            </textBox>
         </presentation>

         <presentation id="CommandsToRunOnConnect">
            <listBox refId="CommandsToRunOnConnect_list">Befehle</listBox>
         </presentation>

         <presentation id="CommandsToRunOnReconnect">
            <listBox refId="CommandsToRunOnReconnect_list">Befehle</listBox>
         </presentation>

         <presentation id="CommandsToRunOnDisconnect">
            <listBox refId="CommandsToRunOnDisconnect_list">Befehle</listBox>
         </presentation>

         <presentation id="SSO_retry_timeout">
            <decimalTextBox refId="SSO_retry_timeout_DB" defaultValue="5000">Zeitraum für die Wiederholung der Single-Sign-On-Anmeldung</decimalTextBox>
         </presentation>

         <presentation id="RDS_threshold_connecting_session">
            <decimalTextBox refId="RDS_threshold_connecting_session_DB" defaultValue="20">Schwellenwert für die Verbindungssitzung</decimalTextBox>
         </presentation>

         <presentation id="RDS_threshold_load_index">
            <decimalTextBox refId="RDS_threshold_load_index_DB" defaultValue="0">Schwellenwert für Ladeindex</decimalTextBox>
         </presentation>

         <presentation id="Win10PhysicalAgentAudioOption">
            <dropdownList refId="Win10PhysicalAgentAudioOption_DDL" defaultItem="0">Audiooption für physischen Remote-Desktop-Computer bei einer Einzelsitzung unter Windows 10</dropdownList>
         </presentation>

         <presentation id="WaitForLogoff">
            <decimalTextBox refId="WaitForLogoff_DB" defaultValue="10">Zeitüberschreitung beim Warten auf Abmeldung</decimalTextBox>
         </presentation>

         <presentation id="Accept_SSL_encr_framework_channel">
            <dropdownList refId="Accept_SSL_encr_framework_channel_DDL" defaultItem="0">SSL-verschlüsselten Framework-Kanal akzeptieren</dropdownList>
         </presentation>

         <presentation id="Local_Reader_Name">
            <textBox refId="Local_Reader_Name_TB">
               <label>Name des lokalen Lesegeräts</label>
            </textBox>
         </presentation>

         <presentation id="Cert_wait_timeout">
            <decimalTextBox refId="Cert_wait_timeout_DB" defaultValue="40">Wartezeit für Zertifikate</decimalTextBox>
         </presentation>

         <presentation id="Min_key_size">
            <decimalTextBox refId="Min_key_size_DB" defaultValue="1024">Mindestschlüsselgröße</decimalTextBox>
         </presentation>

         <presentation id="All_key_sizes">
            <textBox refId="All_key_sizes_TB">
               <label>Alle Schlüsselgrößen</label>
                  <defaultValue>2048</defaultValue>
            </textBox>
         </presentation>

         <presentation id="Keys_to_precreate">
            <decimalTextBox refId="Keys_to_precreate_DB" defaultValue="5">Anzahl der vorab erstellten Schlüssel</decimalTextBox>
         </presentation>

         <presentation id="Cert_min_validity">
            <decimalTextBox refId="Cert_min_validity_DB" defaultValue="10">Für ein Zertifikat erforderliche Mindestgültigkeitsdauer</decimalTextBox>
         </presentation>

         <presentation id="WhfbCertificateAllowedApplicationsList">
            <multiTextBox refId="Whfb_Certificate_Allowed_Applications_list_TB">Liste der zulässigen ausführbaren Dateien</multiTextBox>
         </presentation>

         <presentation id="Exclude_Vid_Pid">
            <textBox refId="Exclude_Vid_Pid_TB">
               <label>Vid/Pid-Gerät ausschließen</label>
            </textBox>
         </presentation>

         <presentation id="Exclude_Vid_Pid_Rel">
            <textBox refId="Exclude_Vid_Pid_Rel_TB">
               <label>Vid-/Pid-/Rel-Gerät ausschließen</label>
            </textBox>
         </presentation>

         <presentation id="Include_Vid_Pid">
            <textBox refId="Include_Vid_Pid_TB">
               <label>Vid-/Pid-Gerät einschließen</label>
            </textBox>
         </presentation>

         <presentation id="Include_Vid_Pid_Rel">
            <textBox refId="Include_Vid_Pid_Rel_TB">
               <label>Vid-/Pid-/Rel-Gerät einschließen</label>
            </textBox>
         </presentation>

         <presentation id="Exclude_device_family">
            <textBox refId="Exclude_device_family_TB">
               <label>Gerätefamilie ausschließen</label>
            </textBox>
         </presentation>

         <presentation id="Include_device_family">
            <textBox refId="Include_device_family_TB">
               <label>Gerätefamilie einschließen</label>
            </textBox>
         </presentation>

         <presentation id="HidOpt_Include_Vid_Pid">
            <textBox refId="HidOpt_Include_Vid_Pid_TB">
               <label>HID-Optimierung Vid/Pid-Gerät einbeziehen</label>
            </textBox>
         </presentation>

         <presentation id="Exclude_Auto_Vid_Pid">
            <textBox refId="Exclude_Auto_Vid_Pid_TB">
               <label>Verbindung für Vid/Pid-Gerät automatisch ausschließen</label>
            </textBox>
         </presentation>

         <presentation id="Exclude_auto_device_family">
            <textBox refId="Exclude_auto_device_family_TB">
               <label>Verbindungsgeräte-Familie automatisch ausschließen</label>
            </textBox>
         </presentation>

         <presentation id="Exclude_Vid_Pid_from_Split">
            <textBox refId="Exclude_Vid_Pid_from_Split_TB">
               <label>Vid/Pid-Gerät vom Splitten ausschließen</label>
            </textBox>
         </presentation>

         <presentation id="Split_Vid_Pid_Device">
            <textBox refId="Split_Vid_Pid_Device_TB">
               <label>Vid/Pid-Gerät splitten</label>
            </textBox>
         </presentation>

         <presentation id="Allow_other_input_devices">
            <dropdownList refId="Allow_other_input_devices_DDL" defaultItem="0">Andere Eingabegeräte zulassen</dropdownList>
         </presentation>

         <presentation id="Allow_HID_Bootable">
            <dropdownList refId="Allow_HID_Bootable_DDL" defaultItem="0">Startfähige Eingabegeräte zulassen</dropdownList>
         </presentation>

         <presentation id="Allow_Audio_Input_devices">
            <dropdownList refId="Allow_Audio_Input_devices_DDL" defaultItem="0">Audioeingabegeräte zulassen</dropdownList>
         </presentation>

         <presentation id="Allow_Audio_Output_devices">
            <dropdownList refId="Allow_Audio_Output_devices_DDL" defaultItem="0">Audioausgabegeräte zulassen</dropdownList>
         </presentation>

         <presentation id="Allow_keyboard_mouse">
            <dropdownList refId="Allow_keyboard_mouse_DDL" defaultItem="0">Tastatur- und Mausgeräte zulassen</dropdownList>
         </presentation>

         <presentation id="Allow_Video_Devices">
            <dropdownList refId="Allow_Video_Devices_DDL" defaultItem="0">Videogeräte zulassen</dropdownList>
         </presentation>

         <presentation id="Allow_Smart_Cards">
            <dropdownList refId="Allow_Smart_Cards_DDL" defaultItem="0">Smartcards zulassen</dropdownList>
         </presentation>

         <presentation id="Allow_Auto_Device_Splitting">
            <dropdownList refId="Allow_Auto_Device_Splitting_DDL" defaultItem="0">Autom. Gerätesplitten zulassen</dropdownList>
         </presentation>

         <presentation id="Accept_SSL_encr_framework_channel_2">
            <dropdownList refId="Accept_SSL_encr_framework_channel_2_DDL" defaultItem="0">SSL-verschlüsselten Framework-Kanal akzeptieren</dropdownList>
         </presentation>

         <presentation id="Default_proxy_server">
            <textBox refId="Default_proxy_server_TB">
               <label>Standard-Proxy-Server</label>
            </textBox>
         </presentation>

         <presentation id="Update_Java_Proxy">
            <dropdownList refId="Update_Java_Proxy_DDL" defaultItem="2">Proxy-Server für Java-Applet festlegen</dropdownList>
         </presentation>

         <presentation id="HTML5MMRUrlList">
            <listBox refId="HTML5MMRUrlList_list">Die URL-Liste für die Aktivierung der Horizon HTML5 Multimedia-Umleitung.</listBox>
         </presentation>

         <presentation id="HTML5MMRUrlBlockList">
            <listBox refId="HTML5MMRUrlBlockList_list">Die URL-Liste zum Ausschluss der Horizon HTML5 Multimedia-Umleitung.</listBox>
         </presentation>

         <presentation id="GeoRedirUrlList">
            <listBox refId="GeoRedirUrlList_list">Die URL-Liste zum Aktivieren der Horizon Geolocation-Umleitungsfunktion.</listBox>
         </presentation>

         <presentation id="GeoRedirDistanceDelta">
            <textBox refId="GeoRedirDistanceDelta_value">
               <label>Die Mindestentfernung in Metern</label>
               <defaultValue>75</defaultValue>
            </textBox>
         </presentation>

         <presentation id="WebrtcProxyProbeUrl">
            <textBox refId="WebrtcProxyProbeUrl_value">
               <label>URL zum Überprüfen des Proxy-Servers auf Webrtc-Anrufe verwenden</label>
            </textBox>
         </presentation>


         <presentation id="BrowserRedirUrlList">
            <listBox refId="BrowserRedirUrlList_list">Die URL-Liste zum Aktivieren der Horizon-Browserumleitungsfunktion.</listBox>
         </presentation>

         <presentation id="EnhBrowserRedirUrlList">
            <listBox refId="EnhBrowserRedirUrlList_list">Die URL-Liste zum Aktivieren der Funktion „Erweiterte Horizon-Browserumleitung“.</listBox>
         </presentation>

         <presentation id="BrowserRedirNavUrlList">
            <listBox refId="BrowserRedirNavUrlList_list">Die URL-Liste zum Aktivieren der Navigation für die Horizon-Browserumleitungsfunktion.</listBox>
         </presentation>

         <presentation id="WebRTC_SDK_Web_App_Redir_Allow_List">
            <listBox refId="WebRTC_SDK_Web_App_Redir_Allow_List_list">Die URL-Liste zum Aktivieren der Unterstützung des Horizon WebRTC-SDK für Web-Anwendungen.</listBox>
         </presentation>

         <presentation id="SetForceNonOptimizedOptions">
            <checkBox refId="AUTO_DETECT_EXTERNAL_CONNECTIONS_CHKBOX">Externe Verbindungen automatisch erkennen</checkBox>
            <textBox refId="SetForceNonOptimizedEnvVar_TB">
               <label>Der Name der Umgebungsvariablen:</label>
            </textBox>
         </presentation>

         <presentation id="UnityFilterRules_Filter">
            <listBox refId="UnityFilterRules_FilterList">Unity-Filterregeln</listBox>
         </presentation>

         <presentation id="Enable_Unity_Touch">
            <checkBox refId="Enabled_UWP_For_UnityTouch_CB" defaultChecked="true">Aktivieren Sie die UWP-App-Unterstützung (Universal Windows Platform) für Unity Touch unter Windows 10.</checkBox>
         </presentation>

         <presentation id="ProcessIgnoreList">
            <multiTextBox refId="ProcessIgnoreList_TB">Zu ignorierende Prozesse beim Erkennen leerer Anwendungssitzungen</multiTextBox>
         </presentation>

         <presentation id="VDI_idle_time_till_disconnect">
            <dropdownList refId="VDI_idle_time_till_disconnect_minutes" noSort="true" defaultItem="0">Leerlaufzeitüberschreitung</dropdownList>
         </presentation>

         <presentation id="VDI_disconnect_time_till_logoff">
            <dropdownList refId="VDI_disconnect_time_till_logoff_minutes" noSort="true" defaultItem="0">Zeitüberschreitung zum Trennen</dropdownList>
         </presentation>

         <presentation id="RDS_idle_time_till_disconnect">
            <dropdownList refId="RDS_idle_time_till_disconnect_milliseconds" noSort="true" defaultItem="0">Zeitüberschreitung beim RDS-Leerlauf</dropdownList>
         </presentation>

         <presentation id="RDS_disconnect_time_till_logoff">
            <dropdownList refId="RDS_disconnect_time_till_logoff_milliseconds" noSort="true" defaultItem="0">Zeitüberschreitung beim Trennen der RDS-Verbindung</dropdownList>
         </presentation>

         <presentation id="RDS_active_time_till_disconnect">
            <dropdownList refId="RDS_active_time_till_disconnect_milliseconds" noSort="true" defaultItem="0">Zeitüberschreitung bei RDS-Verbindung</dropdownList>
         </presentation>

         <presentation id="Prewarm_disconnect_time_till_logoff">
            <dropdownList refId="Prewarm_disconnect_time_till_logoff_minutes" noSort="true" defaultItem="5">Zeitüberschreitung beim Vorwärmen</dropdownList>
         </presentation>

         <presentation id ="watermark_configuration">
            <multiTextBox refId="Text">Text</multiTextBox>
            <dropdownList refId="ImageFit" noSort="true" defaultItem="0">Bildlayout</dropdownList>
            <decimalTextBox refId="Rotation" defaultValue="45">Textdrehung</decimalTextBox>
            <decimalTextBox refId="Opacity" defaultValue="50">Deckkraft</decimalTextBox>
            <decimalTextBox refId="Margin" defaultValue="50">Rand</decimalTextBox>
            <textBox refId="TextColor">
               <label>Textfarbe</label>
               <defaultValue>255 255 255</defaultValue>
            </textBox>
            <decimalTextBox refId="FontSize" defaultValue="0">Schriftgröße</decimalTextBox>
            <decimalTextBox refId="RefreshInterval" defaultValue="0">Aktualisierungsintervall</decimalTextBox>
         </presentation>

        <presentation id="AppSignInWindow">
            <decimalTextBox refId="app_sign_in_window_width" defaultValue="800" spinStep="2">Breite des Fensters „Rechtlicher Hinweis“: </decimalTextBox>
            <decimalTextBox refId="app_sign_in_window_height" defaultValue="600" spinStep="2">Höhe des Fensters „Rechtlicher Hinweis“: </decimalTextBox>
        </presentation>
        <presentation id="Video_Cpu_Overuse_Threshold">
            <decimalTextBox refId="Video_Cpu_Overuse_Threshold_data" defaultValue="85">Schwellenwert für Video-CPU-Überauslastung</decimalTextBox>
         </presentation>
         <presentation id="Webrtc_Recommended_AEC">
            <checkBox refId="Webrtc_Recommended_AEC_CB" defaultChecked="true"> Empfohlenen AEC-Algorithmus verwenden </checkBox>
         </presentation>
         <presentation id="Force_Enable_Teams_Redir">
            <checkBox refId="Force_Enable_Teams_Redir_Value" defaultChecked="false"> Clientseitige WebRTC-Optimierung erzwingen </checkBox>
         </presentation>
         <presentation id="Force_Enable_Electron_App_Redir">
            <checkBox refId="Force_Enable_Electron_App_Redir_Value" defaultChecked="false"> Clientseitige WebRTC-Optimierung erzwingen </checkBox>
         </presentation>
         <presentation id="FIDO2AllowList">
            <textBox refId="FIDO2AllowList_TB">
               <label>FIDO2-Positivliste</label>
               <defaultValue>chrome.exe;firefox.exe;msedge.exe</defaultValue>
            </textBox>
         </presentation>
         <presentation id="AllowScreenRecording">
            <checkBox refId="AllowScreenRecording_CB" defaultChecked="false"> Bildschirmaufzeichnung für Horizon Mac Client zulassen </checkBox>
         </presentation>
         <presentation id="CustomBackgroundImagesFolderPath">
            <textBox refId="CustomBackgroundImagesFolder_Path">
               <label>Ordner für benutzerdefinierter Hintergrundbilder</label>
            </textBox>
         </presentation>
         <presentation id="CustomBackgroundDefaultImageName">
            <textBox refId="CustomBackgroundDefaultImageName_Value">
               <label>Name des Standardbilds</label>
            </textBox>
         </presentation>
         <presentation id="EnableDisplayNetworkState">
            <decimalTextBox refId="NetworkWarningInterval_CB" defaultValue="5">Das Zeitintervall für die Popup-Meldung zur Netzwerkwarnung, in Minuten. Maximal 60 Minuten, mindestens 1 Minute. Der Standardwert ist 5 Minuten.</decimalTextBox>
         </presentation>
         <presentation id="IpPrefix" >
            <textBox refId="IpPrefixTextBox" >
               <label >IP-Präfix</label>
            </textBox>
         </presentation>
      </presentationTable>
   </resources>
</policyDefinitionResources>
