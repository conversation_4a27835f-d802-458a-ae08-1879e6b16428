/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */


/* ----------------------------------------------
 * cloneprep.cpp
 * ---------------------------------------------- */

#include "stdafx.h"
#include "cloneprep.h"
#include <fstream>

using namespace std;

/*
 *----------------------------------------------------------------------------
 *
 * ClonePrep::retrieveDomainJoinCreds --
 *
 *      Retrieves the domain join details from the broker
 *
 * Returns:
 *      true if the domain join details are successfully retrieved otherwise false.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

bool
ClonePrep::retrieveDomainJoinCreds()
{
   // Discover MAC address
   wstr macAddr;
   macAddr = getMacAddr();
   if (macAddr.empty()) {
      SYSMSG_FUNC(Error, L"Couldn't retrieve MAC address.");
      return false;
   }

   // Generate a Nonce
   MsgBinary nonce;
   if (!generateNonce(VAL_NONCE_SIZE, nonce)) {
      SYSMSG_FUNC(Error, L"Nonce creation failed.");
      return false;
   }

   // Get the list of brokers
   vwstr brokerFQDNs = readRegServerFQDNs();
   if (brokerFQDNs.empty()) {
      SYSMSG_FUNC(Error, L"Couldn't retrieve the broker FQDNs.");
      return false;
   }

   // Get the pool ID:
   wstr poolId = wstr::readRegistry(utils::regPath() + REG_POOL_ID, L"");
   if (poolId.empty()) {
      SYSMSG_FUNC(Error, L"Couldn't retrieve the pool ID.");
      return false;
   }

   // Send message to the broker.
   PropertyBag params, response;
   params.set(PAR_HOST_MAC_ADDR, macAddr);
   params.set(PAR_POOL_ID, poolId);
   params.setBinary(PAR_NONCE, nonce.pBinary, nonce.sBinary);
   /*
    * TODO: Generate signature by signing the above mentioned parameters with
    * the PHs key. Related ticket for tracking this work:
    * https://omnissa.atlassian.net/browse/UBI-438
    * For now just sending a dummy value for signature field.
    */
   params.setBinary(PAR_SIGNATURE, nonce.pBinary, nonce.sBinary);

   for (auto fqdn : brokerFQDNs) {
      SYSMSG_FUNC(Debug, L"Trying broker with FQDN:%s", fqdn);
      if (!connectAndSend(fqdn, QUEUE_AGENT_PAIRING, HINT_PREPARE_CLONE, params, response)) {
         SYSMSG_FUNC(Error,
                     L"%s failed with broker:%s, Error: (%d) "
                     L"%s",
                     HINT_PREPARE_CLONE, fqdn, response.getError(), response.getErrorText());
      } else {
         SYSMSG_FUNC(Trace, L"Succeeded with broker with FQDN=%s", fqdn);
         break;
      }
   }

   if (!response.getErrorText().empty()) {
      SYSMSG_FUNC(Debug, L"Error text=%s", response.getErrorText());
      return false;
   }

   /*
    * TODO: Verify the signature.
    * Tracking ticket: https://omnissa.atlassian.net/browse/UBI-440
    * For now we just check for the presence of the signature.
    */
   void *data = NULL;
   size_t dataLen = 0;
   response.getBinary(PAR_RESULT_SIG, &data, &dataLen);

   if (!data || !dataLen) {
      SYSMSG_FUNC(Error, L"Signature data is missing in response from broker.");
      return false;
   }

   auto retrieveProperty = [&response](LPWSTR propName, wstr &valHolder) {
      if ((valHolder = response.get(propName, L"")).empty()) {
         SYSMSG_FUNC(Error, L"Received empty %s from the broker", propName);
         return false;
      }
      return true;
   };
   /*
    * Get details from the response bag.
    */
   if (!retrieveProperty(LPWSTR(L"domainUserName"), mUserName)) {
      return false;
   }
   if (!retrieveProperty(LPWSTR(L"domainFqdn"), mDomainName)) {
      return false;
   }
   if (!retrieveProperty(LPWSTR(L"computer-name"), mComputerName)) {
      return false;
   }
   if (!retrieveProperty(LPWSTR(L"joinDomain"), mJoinDomain)) {
      return false;
   }
   /*
    * TODO: Decrypt the domain credentials using KeyVault (GIe key)
    * Tracking ticket: https://omnissa.atlassian.net/browse/UBI-439
    */
   if (!retrieveProperty(LPWSTR(L"domainAdminPassword"), mPassword)) {
      return false;
   }

   SYSMSG_FUNC(Trace, L"username=%s, domainname=%s, joindomain=%s, computername=%s", mUserName,
               mDomainName, mJoinDomain, mComputerName);

   return true;
}


/*
 *----------------------------------------------------------------------------
 *
 * ClonePrep::readLinesFromFile --
 *
 *      Reads file and returns the lines in a vector.
 *
 * Returns:
 *      true if the file is read successfully otherwise false
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

bool
ClonePrep::readLinesFromFile(wstr filename, std::vector<wstr> &lines)
{
   if (filename.empty()) {
      SYSMSG_FUNC(Error, L"Filename is empty.");
      return false;
   }

   std::wifstream inputFile(filename);
   if (!inputFile.is_open()) {
      SYSMSG_FUNC(Error, L"Error opening file %s", filename);
      return false;
   }

   // Get the length of the file
   inputFile.seekg(0, inputFile.end);
   _int64 length = inputFile.tellg();
   inputFile.seekg(0, inputFile.beg);

   wstr buffer;
   // Set the buffer size
   buffer.setSize(length);

   /*
    * Read the whole file.
    * The actual number of characters read will be less than the total length because
    * the file contains \r\n at the end of each line, and a line once read into buffer
    * will not have \r at the end.
    */
   inputFile.read((wchar_t *)buffer.c_str(), length);
   __int64 charsRead = inputFile.gcount();
   if (!inputFile.good() && !inputFile.eof()) {
      // Log the bad and fail bits.
      SYSMSG_FUNC(Error,
                  L"Didn't reach end of file %s, actual number of chars read=%lld, expected "
                  L"number of chars=%lld, bad bit=%d, fail bit=%d",
                  filename.c_str(), charsRead, length, inputFile.bad(), inputFile.fail());
      return false;
   }

   SYSMSG_FUNC(Trace, L"Reached end of file %s, actual chars read = %lld, expected chars=%d",
               filename.c_str(), charsRead, length);

   // Read the lines from the buffer
   __int64 start = 0;
   while (start <= charsRead) {
      size_t end = buffer.find(L'\n', start);
      if (end == wstr::npos) {
         end = charsRead; // If no newline found, read till the end
      }
      wstr line = buffer.substr(start, end - start);
      lines.push_back(line);
      start = end + 1; // Move to the next line
   }

   for (const wstr &updatedLine : lines) {
      SYSMSG_FUNC(Debug, L"%s", updatedLine.c_str());
   }

   return true;
}


/*
 *----------------------------------------------------------------------------
 *
 * ClonePrep::populateUnAttendFileWithCreds --
 *
 *      Writes the domain join details to the unattend.xml file
 *
 * Returns:
 *      true if the domain join details are successfully written to the
        unattend.xml file otherwise false
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

bool
ClonePrep::populateUnAttendFileWithCreds()
{
   wstr filename;
   if (!expandEnvStr(UNATTEND_FILE_PATH, filename)) {
      SYSMSG_FUNC(Error, L"Failed to expand the filename %s", UNATTEND_FILE_PATH);
      return false;
   }

   vector<wstr> lines;
   wstr line;

   if (!readLinesFromFile(filename, lines)) {
      SYSMSG_FUNC(Error, L"Failed to read lines from file %s", filename.c_str());
      return false;
   }

   size_t index = 0;
   auto populateEntry = [&lines, &index](wstr entryName, wstr value) {
      wstr startTag, endTag;
      startTag = wstr(L"<") + entryName + L">";
      endTag = wstr(L"</") + entryName + L">";
      size_t offset = lines[index].find(startTag.c_str());
      if (offset != wstr::npos) {
         size_t startPos = offset + startTag.length();
         size_t endPos = lines[index].find(endTag.c_str(), startPos);
         if (endPos != wstr::npos) {
            lines[index].replace(startPos, endPos - startPos, value.c_str());
            if (entryName.compare(L"Password") != 0) { // Don't log password.
               SYSMSG_FUNC(Debug, L"Modified line: %s", lines[index].c_str());
            }
            return true;
         }
      }
      return false;
   };

   // Add values to the unattend.xml file.
   bool entryProcessed[UNATTEND_FIELD_COUNT] = {0};
   int count = 0;
   for (; count < UNATTEND_FIELD_COUNT && index < lines.size(); ++index) {
      if (!entryProcessed[USERNAME] && populateEntry(L"Username", mUserName)) {
         entryProcessed[USERNAME] = true;
         count++;
         continue;
      }
      if (!entryProcessed[JOINDOMAIN] && populateEntry(L"JoinDomain", mJoinDomain)) {
         entryProcessed[JOINDOMAIN] = true;
         count++;
         continue;
      }
      if (!entryProcessed[DOMAINNAME] && populateEntry(L"Domain", mDomainName)) {
         entryProcessed[DOMAINNAME] = true;
         count++;
         continue;
      }
      if (!entryProcessed[PASSWORD] && populateEntry(L"Password", mPassword)) {
         entryProcessed[PASSWORD] = true;
         count++;
         continue;
      }
      if (!entryProcessed[COMPUTERNAME] && populateEntry(L"ComputerName", mComputerName)) {
         entryProcessed[COMPUTERNAME] = true;
         count++;
         continue;
      }
   }

   // Write back to the file
   wofstream outputFile(filename, ios::trunc); // Open in write mode, overwriting existing content
   if (outputFile.is_open()) {
      for (const wstr &updatedLine : lines) {
         outputFile << updatedLine.c_str() << endl;
      }
   } else {
      SYSMSG_FUNC(Error, L"Error opening file for writing");
      return false;
   }

   SYSMSG_FUNC(Debug, L"%s file updated successfully.", filename.c_str());
   return true;
}


/*
 *----------------------------------------------------------------------------
 *
 * ClonePrep::runWinDeploy --
 *
 *      Executes the windeloy.exe and waits for it to finish execution
 *
 * Returns:
 *      returne the exit code of the windeloy.exe if it is successfully
 *      retrieved otherwise 1.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

int
ClonePrep::runWinDeploy()
{
   STARTUPINFOW sInfo = {0};
   sInfo.cb = sizeof(sInfo);
   PROCESS_INFORMATION pInfo = {0};
   DWORD exitCode = 0;

   /*
    * If we are not successful in launching windeploy.exe here for some reason
    * then the "CmdLine" registry value must be set to Windeploy.exe path so
    * that when Windows reboots then WinDeploy.exe will get launched to finish
    * Sysprep.
    */
   wstr windeployPath;
   if (!expandEnvStr(FULL_WINDEPLOY_PATH, windeployPath)) {
      // Set the"CmdLine" registry value to Windeploy.exe.
      wstr regStr(WINDEPLOY_PATH);
      if (!regStr.writeRegistry(L"HKLM\\SYSTEM\\Setup", L"CmdLine", REG_SZ)) {
         SYSMSG_FUNC(Error, L"Failed to write the HKLM\\SYSTEM\\Setup\\CmdLine value, error=%d",
                     GetLastError());
      }
      return 1;
   }

   if (!CreateProcessW(windeployPath, NULL, NULL, NULL, FALSE, 0, NULL, NULL, &sInfo, &pInfo)) {
      SYSMSG_FUNC(Error, L"Failed to launch windeploy.exe, error=%d, returning 1", GetLastError());
      wstr regStr(WINDEPLOY_PATH);
      if (!regStr.writeRegistry(L"HKLM\\SYSTEM\\Setup", L"CmdLine", REG_SZ)) {
         SYSMSG_FUNC(Error, L"Failed to write the HKLM\\SYSTEM\\Setup\\CmdLine value, error=%d",
                     GetLastError());
      }
      return 1;
   } else {
      SYSMSG_FUNC(Debug, L"After launching windeploy.exe.");
      WaitForSingleObject(pInfo.hProcess, INFINITE);
      if (!GetExitCodeProcess(pInfo.hProcess, &exitCode)) {
         SYSMSG_FUNC(
            Error,
            L"Error getting exit code for process with handle %x returning 1, last error = %d",
            pInfo.hProcess, GetLastError());
         return 1;
      } else {
         if (exitCode == STILL_ACTIVE) {
            SYSMSG_FUNC(Error, L"Exit code is STILL_ACTIVE for process with handle %x, returning 1",
                        pInfo.hProcess);
            return 1;
         } else {
            SYSMSG_FUNC(Debug, L"Exit code for process with handle %x is %d", pInfo.hProcess,
                        exitCode);
         }
      }
   }
   return exitCode;
}


/*
 *----------------------------------------------------------------------------
 *
 * ClonePrep::startDhcpService --
 *
 *      starts dhcp service
 *
 * Returns:
 *      returne true if it is able to start the dhcp service otherwise false
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

bool
ClonePrep::startDhcpService()
{
   bool ret = true;
   SC_HANDLE schSCManager = OpenSCManager(NULL, NULL, SC_MANAGER_ALL_ACCESS);
   if (schSCManager == NULL) {
      SYSMSG_FUNC(Error, L"OpenSCManager failed (%d)", GetLastError());
      return false;
   }

   SC_HANDLE schService = OpenService(schSCManager, L"Dhcp", SERVICE_START);
   if (schService == NULL) {
      SYSMSG_FUNC(Error, L"OpenService failed (%d)", GetLastError());
      CloseServiceHandle(schSCManager);
      return false;
   }

   if (!StartService(schService, 0, NULL)) {
      SYSMSG_FUNC(Error, L"Failed to start DHCP service. Error: (%d)", GetLastError());
      ret = false;
   }

   CloseServiceHandle(schService);
   CloseServiceHandle(schSCManager);
   return ret;
}


/*
 *----------------------------------------------------------------------------
 *
 * ClonePrep::init --
 *
 *      starts dhcp service and gets the IP address
 *
 * Returns:
 *      returns true if it is able to start the dhcp service and retrive the IP
 *      address otherwise false
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

bool
ClonePrep::init()
{
   /*
    * Start DHCP service first because when Windows launches this tool at
    * startup, the DHCP service is not running yet. The Dhcp service needs
    * to run so that IP address can be assigned to the machine and then
    * eventually the hzaprep can talk to broker and retrieve domain join
    * details.
    */
   if (!startDhcpService()) {
      SYSMSG_FUNC(Error, L"Failed to start the dhcp service");
      return false;
   }

   wstr ipAddr = L"";
   ipAddr = getIPAddr();
   if (ipAddr.empty()) {
      SYSMSG_FUNC(Error, L"No IP Address found");
      return false;
   }
   SYSMSG_FUNC(Trace, L"Found IP address %s", ipAddr);
   return true;
}


/*
 *----------------------------------------------------------------------------
 *
 * ClonePrep::populateSysprepData --
 *
 *      retrieves the Sysprep details from the broker and uses them for adding
 *      information in the unattend.xml file
 *
 * Returns:
 *      returns true if it is successful in retrieving the Sysprep details and
 *      populating the unattend file, otherwise returns false
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

bool
ClonePrep::populateSysprepData()
{
   if (!retrieveDomainJoinCreds()) {
      SYSMSG_FUNC(Error, L"Couldn't retrieve domain join information");
      return false;
   }
   if (!populateUnAttendFileWithCreds()) {
      SYSMSG_FUNC(Error, L"Couldn't populate the unattend file with domain join information");
      return false;
   }

   return true;
}


/*
 *----------------------------------------------------------------------------
 *
 * ClonePrep::expandEnvStr --
 *
 *      Util function that uses ExpandEnvironmentStringsW to expand environment
 *      string in an input string.
 *
 * Returns:
 *      returne true if expansion is successful otherwise false
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

bool
ClonePrep::expandEnvStr(const wchar_t *strToExpand, wstr &expandedStr)
{
   if (!strToExpand) {
      SYSMSG_FUNC(Error, L"Null pointer provided for input string.");
      return false;
   }
   DWORD reqdSize = ExpandEnvironmentStringsW(strToExpand, NULL, 0);
   if (reqdSize == 0) {
      SYSMSG_FUNC(Error, L"ExpandEnvironmentStringsW failed with error=%d, returning 1",
                  GetLastError());
      return false;
   }

   expandedStr.resize(reqdSize);
   if (!ExpandEnvironmentStringsW(strToExpand, (LPWSTR)expandedStr.c_str(), reqdSize)) {
      SYSMSG_FUNC(Error, L"ExpandEnvironmentStringsW failed with error=%d, returning 1",
                  GetLastError());
      return false;
   }

   return true;
}
