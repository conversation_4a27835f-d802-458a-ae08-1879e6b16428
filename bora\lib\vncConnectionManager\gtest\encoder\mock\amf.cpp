/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

// bora/apps/cedar/include
#include <cedar/unique_any.h>
#include "cedar/platform.h"
#include <map>
#include <set>
#include <string>


#if CEDAR_PLATFORM_WINDOWS

#   include <components/Component.h>
#   include <components/ComponentCaps.h>
#   include <core/Buffer.h>
#   include <core/Context.h>
#   include <core/Data.h>
#   include <core/Factory.h>
#   include <core/Interface.h>
#   include <core/Plane.h>
#   include <core/Platform.h>
#   include <core/Result.h>
#   include <core/Surface.h>
#   include <core/Variant.h>


const amf::AMFGuid IID_AMFContext = {0xa76a13f0, 0xd80e, 0x4fcc, 0xb5, 0x8, 0x65,
                                     0xd0,       0xb5,   0x2e,   0xd9, 0xee};
const amf::AMFGuid IID_AMFComponent = {0x8b51e5e4, 0x455d, 0x4034, 0xa7, 0x46, 0xde,
                                       0x1b,       0xed,   0xc3,   0xc4, 0x6};
const amf::AMFGuid IID_AMFCaps = {0x12345678, 0x1234, 0x1234, 0x12, 0x34, 0x56,
                                  0x78,       0x90,   0xAB,   0xCD, 0xEF};
const amf::AMFGuid IID_AMFIOCaps = {0x87654321, 0x4321, 0x4321, 0x43, 0x21, 0x65,
                                    0x87,       0xBA,   0xDC,   0xFE, 0x10};
const amf::AMFGuid IID_AMFSurface = {0x3075dbe3, 0x8718, 0x4cfa, 0x86, 0xfb, 0x21,
                                     0x14,       0xc0,   0xa5,   0xa4, 0x51};
const amf::AMFGuid IID_AMFPlane = {0xbede1aa6, 0xd8fa, 0x4625, 0x94, 0x65, 0x6c,
                                   0x82,       0xc4,   0x37,   0x71, 0x2e};
const amf::AMFGuid IID_AMFData = {0xa1159bf6, 0x9104, 0x4107, 0x8e, 0xaa, 0xc5,
                                  0x3d,       0x5d,   0xba,   0xc5, 0x11};
const amf::AMFGuid IID_AMFBuffer = {0xb04b7248, 0xb6f0, 0x4321, 0xb6, 0x91, 0xba,
                                    0xa4,       0x74,   0xf,    0x9f, 0xcb};

class MockAMFBuffer : public amf::AMFBuffer {
public:
   MockAMFBuffer() : m_refCount(1), m_size(0), m_nativeData(nullptr) {}

   virtual ~MockAMFBuffer()
   {
      if (m_nativeData) {
         delete[] static_cast<uint8_t *>(m_nativeData);
      }
   }

   // AMFInterface
   virtual amf_long AMF_STD_CALL Acquire() override { return ++m_refCount; }

   virtual amf_long AMF_STD_CALL Release() override
   {
      if (--m_refCount == 0) {
         delete this;
         return 0;
      }
      return m_refCount;
   }

   virtual AMF_RESULT AMF_STD_CALL QueryInterface(const amf::AMFGuid &interfaceID,
                                                  void **ppInterface) override
   {
      if (ppInterface == nullptr) {
         return AMF_INVALID_POINTER;
      }

      if (interfaceID == IID_AMFBuffer) {
         *ppInterface = this;
         Acquire();
         return AMF_OK;
      }

      *ppInterface = nullptr;
      return AMF_NO_INTERFACE;
   }

   // AMFPropertyStorage
   virtual AMF_RESULT AMF_STD_CALL SetProperty(const wchar_t *name,
                                               amf::AMFVariantStruct value) override
   {
      return AMF_OK;
   }

   virtual AMF_RESULT AMF_STD_CALL GetProperty(const wchar_t *name,
                                               amf::AMFVariantStruct *value) const override
   {
      return AMF_OK;
   }

   virtual amf_bool AMF_STD_CALL HasProperty(const wchar_t *name) const override { return true; }

   virtual amf_size AMF_STD_CALL GetPropertyCount() const override { return 1; }

   virtual AMF_RESULT AMF_STD_CALL GetPropertyAt(amf_size index, wchar_t *name, amf_size nameSize,
                                                 amf::AMFVariantStruct *value) const override
   {
      return AMF_OK;
   }

   virtual AMF_RESULT AMF_STD_CALL Clear() override { return AMF_OK; }

   virtual AMF_RESULT AMF_STD_CALL AddTo(amf::AMFPropertyStorage *dest, amf_bool overwrite,
                                         amf_bool deepCopy) const override
   {
      return AMF_OK;
   }

   virtual AMF_RESULT AMF_STD_CALL CopyTo(amf::AMFPropertyStorage *dest,
                                          amf_bool deepCopy) const override
   {
      return AMF_OK;
   }

   virtual void AMF_STD_CALL AddObserver(amf::AMFPropertyStorageObserver *observer) override {}

   virtual void AMF_STD_CALL RemoveObserver(amf::AMFPropertyStorageObserver *observer) override {}

   // AMFData
   virtual amf::AMF_MEMORY_TYPE AMF_STD_CALL GetMemoryType() override
   {
      return amf::AMF_MEMORY_HOST;
   }

   virtual AMF_RESULT AMF_STD_CALL Duplicate(amf::AMF_MEMORY_TYPE type,
                                             amf::AMFData **ppData) override
   {
      return AMF_OK;
   }

   virtual AMF_RESULT AMF_STD_CALL Convert(amf::AMF_MEMORY_TYPE type) override { return AMF_OK; }

   virtual AMF_RESULT AMF_STD_CALL Interop(amf::AMF_MEMORY_TYPE type) override { return AMF_OK; }

   virtual amf::AMF_DATA_TYPE AMF_STD_CALL GetDataType() override { return amf::AMF_DATA_BUFFER; }

   virtual amf_bool AMF_STD_CALL IsReusable() override { return true; }

   virtual void AMF_STD_CALL SetPts(amf_pts pts) override { m_pts = pts; }

   virtual amf_pts AMF_STD_CALL GetPts() override { return m_pts; }

   virtual void AMF_STD_CALL SetDuration(amf_pts duration) override { m_duration = duration; }

   virtual amf_pts AMF_STD_CALL GetDuration() override { return m_duration; }

   // AMFBuffer
   virtual AMF_RESULT AMF_STD_CALL SetSize(amf_size newSize) override
   {
      if (m_nativeData) {
         delete[] static_cast<uint8_t *>(m_nativeData);
      }
      m_nativeData = new uint8_t[newSize];
      m_size = newSize;
      return AMF_OK;
   }

   virtual amf_size AMF_STD_CALL GetSize() override { return m_size; }

   virtual void *AMF_STD_CALL GetNative() override { return m_nativeData; }

   virtual void AMF_STD_CALL AddObserver(amf::AMFBufferObserver *pObserver) override {}

   virtual void AMF_STD_CALL RemoveObserver(amf::AMFBufferObserver *pObserver) override {}

   // Setters for mocking behavior
   void SetNative(void *data, amf_size size)
   {
      if (m_nativeData) {
         delete[] static_cast<uint8_t *>(m_nativeData);
      }
      m_nativeData = new uint8_t[size];
      std::memcpy(m_nativeData, data, size);
      m_size = size;
   }

private:
   amf_long m_refCount;
   amf_size m_size;
   void *m_nativeData;
   amf_pts m_pts;
   amf_pts m_duration;
};


class MockAMFData : public amf::AMFData {
public:
   MockAMFData() :
      m_refCount(1),
      m_memoryType(amf::AMF_MEMORY_UNKNOWN),
      m_dataType(amf::AMF_DATA_BUFFER),
      m_pts(0),
      m_duration(0),
      m_isReusable(false)
   {}

   virtual ~MockAMFData() {}

   // AMFInterface
   virtual amf_long AMF_STD_CALL Acquire() override { return ++m_refCount; }

   virtual amf_long AMF_STD_CALL Release() override
   {
      if (--m_refCount == 0) {
         delete this;
         return 0;
      }
      return m_refCount;
   }

   virtual AMF_RESULT AMF_STD_CALL QueryInterface(const amf::AMFGuid &interfaceID,
                                                  void **ppInterface) override
   {
      if (ppInterface == nullptr) {
         return AMF_INVALID_POINTER;
      }

      if (interfaceID == IID_AMFData) {
         *ppInterface = this;
         Acquire();
         return AMF_OK;
      }

      if (interfaceID == IID_AMFBuffer) {
         *ppInterface = new MockAMFBuffer();
         Acquire();
         return AMF_OK;
      }

      *ppInterface = nullptr;
      return AMF_NO_INTERFACE;
   }

   // AMFPropertyStorage
   virtual AMF_RESULT AMF_STD_CALL SetProperty(const wchar_t *name,
                                               amf::AMFVariantStruct value) override
   {
      return AMF_OK;
   }

   virtual AMF_RESULT AMF_STD_CALL GetProperty(const wchar_t *name,
                                               amf::AMFVariantStruct *value) const override
   {
      return AMF_NOT_FOUND;
   }

   virtual amf_bool AMF_STD_CALL HasProperty(const wchar_t *name) const override { return true; }

   virtual amf_size AMF_STD_CALL GetPropertyCount() const override { return 1; }

   virtual AMF_RESULT AMF_STD_CALL GetPropertyAt(amf_size index, wchar_t *name, amf_size nameSize,
                                                 amf::AMFVariantStruct *value) const override
   {
      return AMF_OK;
   }

   virtual AMF_RESULT AMF_STD_CALL Clear() override { return AMF_OK; }

   virtual AMF_RESULT AMF_STD_CALL AddTo(amf::AMFPropertyStorage *dest, amf_bool overwrite,
                                         amf_bool deepCopy) const override
   {
      return AMF_OK;
   }

   virtual AMF_RESULT AMF_STD_CALL CopyTo(amf::AMFPropertyStorage *dest,
                                          amf_bool deepCopy) const override
   {
      return AMF_OK;
   }

   virtual void AMF_STD_CALL AddObserver(amf::AMFPropertyStorageObserver *observer) override {}

   virtual void AMF_STD_CALL RemoveObserver(amf::AMFPropertyStorageObserver *observer) override {}

   // AMFData
   virtual amf::AMF_MEMORY_TYPE AMF_STD_CALL GetMemoryType() override { return m_memoryType; }

   virtual AMF_RESULT AMF_STD_CALL Duplicate(amf::AMF_MEMORY_TYPE type,
                                             amf::AMFData **ppData) override
   {
      *ppData = nullptr;
      return AMF_OK;
   }

   virtual AMF_RESULT AMF_STD_CALL Convert(amf::AMF_MEMORY_TYPE type) override
   {
      m_memoryType = type;
      return AMF_OK;
   }

   virtual AMF_RESULT AMF_STD_CALL Interop(amf::AMF_MEMORY_TYPE type) override { return AMF_OK; }

   virtual amf::AMF_DATA_TYPE AMF_STD_CALL GetDataType() override { return m_dataType; }

   virtual amf_bool AMF_STD_CALL IsReusable() override { return m_isReusable; }

   virtual void AMF_STD_CALL SetPts(amf_pts pts) override { m_pts = pts; }

   virtual amf_pts AMF_STD_CALL GetPts() override { return m_pts; }

   virtual void AMF_STD_CALL SetDuration(amf_pts duration) override { m_duration = duration; }

   virtual amf_pts AMF_STD_CALL GetDuration() override { return m_duration; }

   // Setters for mocking behavior
   void SetMemoryType(amf::AMF_MEMORY_TYPE memoryType) { m_memoryType = memoryType; }

   void SetDataType(amf::AMF_DATA_TYPE dataType) { m_dataType = dataType; }

   void SetReusable(amf_bool isReusable) { m_isReusable = isReusable; }

private:
   amf_long m_refCount;
   amf::AMF_MEMORY_TYPE m_memoryType;
   amf::AMF_DATA_TYPE m_dataType;
   amf_pts m_pts;
   amf_pts m_duration;
   amf_bool m_isReusable;
};


class MockAMFPlane : public amf::AMFPlane {
public:
public:
   MockAMFPlane() :
      m_refCount(1),
      m_type(amf::AMF_PLANE_UNKNOWN),
      m_nativeData(nullptr),
      m_pixelSizeInBytes(0),
      m_offsetX(0),
      m_offsetY(0),
      m_width(0),
      m_height(0),
      m_hPitch(0),
      m_vPitch(0),
      m_isTiled(false)
   {}

   virtual ~MockAMFPlane()
   {
      if (m_nativeData) {
         delete[] static_cast<uint8_t *>(m_nativeData);
      }
   }

   // AMFInterface
   virtual amf_long AMF_STD_CALL Acquire() override { return ++m_refCount; }

   virtual amf_long AMF_STD_CALL Release() override
   {
      if (--m_refCount == 0) {
         delete this;
         return 0;
      }
      return m_refCount;
   }

   virtual AMF_RESULT AMF_STD_CALL QueryInterface(const amf::AMFGuid &interfaceID,
                                                  void **ppInterface) override
   {
      if (ppInterface == nullptr) {
         return AMF_INVALID_POINTER;
      }

      if (interfaceID == IID_AMFPlane) {
         *ppInterface = this;
         Acquire();
         return AMF_OK;
      }

      *ppInterface = nullptr;
      return AMF_NO_INTERFACE;
   }

   // AMFPlane
   virtual amf::AMF_PLANE_TYPE AMF_STD_CALL GetType() override { return m_type; }

   virtual void *AMF_STD_CALL GetNative() override { return m_nativeData; }

   virtual amf_int32 AMF_STD_CALL GetPixelSizeInBytes() override { return m_pixelSizeInBytes; }

   virtual amf_int32 AMF_STD_CALL GetOffsetX() override { return m_offsetX; }

   virtual amf_int32 AMF_STD_CALL GetOffsetY() override { return m_offsetY; }

   virtual amf_int32 AMF_STD_CALL GetWidth() override { return m_width; }

   virtual amf_int32 AMF_STD_CALL GetHeight() override { return m_height; }

   virtual amf_int32 AMF_STD_CALL GetHPitch() override { return m_hPitch; }

   virtual amf_int32 AMF_STD_CALL GetVPitch() override { return m_vPitch; }

   virtual bool AMF_STD_CALL IsTiled() override { return m_isTiled; }

private:
   amf_long m_refCount;
   amf::AMF_PLANE_TYPE m_type;
   void *m_nativeData;
   amf_int32 m_pixelSizeInBytes;
   amf_int32 m_offsetX;
   amf_int32 m_offsetY;
   amf_int32 m_width;
   amf_int32 m_height;
   amf_int32 m_hPitch;
   amf_int32 m_vPitch;
   bool m_isTiled;
};


// Mock class for AMFSurface
class MockSurface : public amf::AMFSurface {
public:
   MockSurface() : m_refCount(1) {}
   virtual ~MockSurface() {}

   // AMFInterface
   virtual amf_long AMF_STD_CALL Acquire() override { return ++m_refCount; }
   virtual amf_long AMF_STD_CALL Release() override
   {
      if (--m_refCount == 0) {
         delete this;
         return 0;
      }
      return m_refCount;
   }
   virtual AMF_RESULT AMF_STD_CALL QueryInterface(const amf::AMFGuid &interfaceID,
                                                  void **ppInterface) override
   {
      if (ppInterface == nullptr) {
         return AMF_INVALID_POINTER;
      }

      if (interfaceID == IID_AMFSurface) {
         *ppInterface = this;
         Acquire();
         return AMF_OK;
      }

      *ppInterface = nullptr;
      return AMF_NO_INTERFACE;
   }

   // AMFPropertyStorage
   virtual AMF_RESULT AMF_STD_CALL SetProperty(const wchar_t *name,
                                               amf::AMFVariantStruct value) override
   {
      return AMF_OK;
   }

   virtual AMF_RESULT AMF_STD_CALL GetProperty(const wchar_t *name,
                                               amf::AMFVariantStruct *value) const override
   {
      return AMF_NOT_FOUND;
   }

   virtual amf_bool AMF_STD_CALL HasProperty(const wchar_t *name) const override { return true; }

   virtual amf_size AMF_STD_CALL GetPropertyCount() const override { return 1; }

   virtual AMF_RESULT AMF_STD_CALL GetPropertyAt(amf_size index, wchar_t *name, amf_size nameSize,
                                                 amf::AMFVariantStruct *value) const override
   {
      return AMF_OK;
   }

   virtual AMF_RESULT AMF_STD_CALL Clear() override { return AMF_OK; }

   virtual AMF_RESULT AMF_STD_CALL AddTo(amf::AMFPropertyStorage *dest, amf_bool overwrite,
                                         amf_bool deepCopy) const override
   {
      return AMF_OK;
   }

   virtual AMF_RESULT AMF_STD_CALL CopyTo(amf::AMFPropertyStorage *dest,
                                          amf_bool deepCopy) const override
   {
      return AMF_OK;
   }

   virtual void AMF_STD_CALL AddObserver(amf::AMFPropertyStorageObserver *observer) override {}

   virtual void AMF_STD_CALL RemoveObserver(amf::AMFPropertyStorageObserver *observer) override {}


   // AMFData
   virtual amf::AMF_MEMORY_TYPE AMF_STD_CALL GetMemoryType() override
   {
      return amf::AMF_MEMORY_UNKNOWN;
   }

   virtual AMF_RESULT AMF_STD_CALL Duplicate(amf::AMF_MEMORY_TYPE type,
                                             amf::AMFData **ppData) override
   {
      *ppData = nullptr;
      return AMF_OK;
   }

   virtual AMF_RESULT AMF_STD_CALL Convert(amf::AMF_MEMORY_TYPE type) override { return AMF_OK; }

   virtual AMF_RESULT AMF_STD_CALL Interop(amf::AMF_MEMORY_TYPE type) override { return AMF_OK; }

   virtual amf::AMF_DATA_TYPE AMF_STD_CALL GetDataType() override { return amf::AMF_DATA_SURFACE; }

   virtual amf_bool AMF_STD_CALL IsReusable() override { return false; }

   virtual void AMF_STD_CALL SetPts(amf_pts pts) override { m_pts = pts; }

   virtual amf_pts AMF_STD_CALL GetPts() override { return m_pts; }

   virtual void AMF_STD_CALL SetDuration(amf_pts duration) override { m_duration = duration; }

   virtual amf_pts AMF_STD_CALL GetDuration() override { return m_duration; }

   // AMFSurface
   virtual amf::AMF_SURFACE_FORMAT AMF_STD_CALL GetFormat() override
   {
      return amf::AMF_SURFACE_UNKNOWN;
   }

   virtual amf_size AMF_STD_CALL GetPlanesCount() override { return 0; }

   virtual amf::AMFPlane *AMF_STD_CALL GetPlaneAt(amf_size index) override
   {
      return new MockAMFPlane();
   }

   virtual amf::AMFPlane *AMF_STD_CALL GetPlane(amf::AMF_PLANE_TYPE type) override
   {
      return nullptr;
   }

   virtual amf::AMF_FRAME_TYPE AMF_STD_CALL GetFrameType() override
   {
      return amf::AMF_FRAME_UNKNOWN;
   }

   virtual void AMF_STD_CALL SetFrameType(amf::AMF_FRAME_TYPE type) override {}

   virtual AMF_RESULT AMF_STD_CALL SetCrop(amf_int32 x, amf_int32 y, amf_int32 width,
                                           amf_int32 height) override
   {
      return AMF_OK;
   }

   virtual AMF_RESULT AMF_STD_CALL CopySurfaceRegion(amf::AMFSurface *pDestSurface, amf_int32 srcX,
                                                     amf_int32 srcY, amf_int32 srcWidth,
                                                     amf_int32 srcHeight, amf_int32 destX,
                                                     amf_int32 destY) override
   {
      return AMF_OK;
   }

   virtual void AMF_STD_CALL AddObserver(amf::AMFSurfaceObserver *observer) override {}

   virtual void AMF_STD_CALL RemoveObserver(amf::AMFSurfaceObserver *observer) override {}

private:
   amf_long m_refCount;
   amf_pts m_pts = 0;
   amf_pts m_duration = 0;
};

// Mock implementation of AMFIOCaps
class MockAMFIOCaps : public amf::AMFIOCaps {
public:
   MockAMFIOCaps() : m_refCount(1) {}
   virtual ~MockAMFIOCaps() {}

   // Implement AMFInterface methods
   virtual amf_long AMF_STD_CALL Acquire() override { return ++m_refCount; }

   virtual amf_long AMF_STD_CALL Release() override
   {
      if (--m_refCount == 0) {
         delete this;
         return 0;
      }
      return m_refCount;
   }

   virtual AMF_RESULT AMF_STD_CALL QueryInterface(const amf::AMFGuid &interfaceID,
                                                  void **ppInterface) override
   {
      if (ppInterface == nullptr) {
         return AMF_INVALID_POINTER;
      }

      if (interfaceID == IID_AMFIOCaps) {
         *ppInterface = this;
         Acquire();
         return AMF_OK;
      }

      *ppInterface = nullptr;
      return AMF_NO_INTERFACE;
   }

   // Implement AMFIOCaps methods
   virtual void AMF_STD_CALL GetWidthRange(amf_int32 *minWidth, amf_int32 *maxWidth) const override
   {
      if (minWidth)
         *minWidth = 64; // Mock minimum width
      if (maxWidth)
         *maxWidth = 4096; // Mock maximum width
   }

   virtual void AMF_STD_CALL GetHeightRange(amf_int32 *minHeight,
                                            amf_int32 *maxHeight) const override
   {
      if (minHeight)
         *minHeight = 64; // Mock minimum height
      if (maxHeight)
         *maxHeight = 4096; // Mock maximum height
   }

   virtual amf_int32 AMF_STD_CALL GetVertAlign() const override
   {
      return 1; // Mock vertical alignment
   }

   virtual amf_int32 AMF_STD_CALL GetNumOfFormats() const override
   {
      return 2; // Mock number of supported formats
   }

   virtual AMF_RESULT AMF_STD_CALL GetFormatAt(amf_int32 index, amf::AMF_SURFACE_FORMAT *format,
                                               amf_bool *native) const override
   {
      if (index < 0 || index >= GetNumOfFormats()) {
         return AMF_OUT_OF_RANGE;
      }

      if (format) {
         *format = (index == 0) ? amf::AMF_SURFACE_NV12 : amf::AMF_SURFACE_RGBA; // Mock formats
      }

      if (native) {
         *native = true; // Mock native support
      }

      return AMF_OK;
   }

   virtual amf_int32 AMF_STD_CALL GetNumOfMemoryTypes() const override
   {
      return 2; // Mock number of supported memory types
   }

   virtual AMF_RESULT AMF_STD_CALL GetMemoryTypeAt(amf_int32 index,
                                                   amf::AMF_MEMORY_TYPE *memoryType,
                                                   amf_bool *native) const override
   {
      if (index < 0 || index >= GetNumOfMemoryTypes()) {
         return AMF_OUT_OF_RANGE;
      }

      if (memoryType) {
         *memoryType =
            (index == 0) ? amf::AMF_MEMORY_DX11 : amf::AMF_MEMORY_VULKAN; // Mock memory types
      }

      if (native) {
         *native = true; // Mock native support
      }

      return AMF_OK;
   }

   virtual amf_bool AMF_STD_CALL IsInterlacedSupported() const override
   {
      return false; // Mock interlaced support
   }

private:
   amf_long m_refCount;
};

// Mock implementation of AMFCaps
class MockAMFCaps : public amf::AMFCaps {
public:
   MockAMFCaps() : m_refCount(1) {}
   virtual ~MockAMFCaps() {}

   // Implement AMFInterface methods
   virtual amf_long AMF_STD_CALL Acquire() override { return ++m_refCount; }

   virtual amf_long AMF_STD_CALL Release() override
   {
      if (--m_refCount == 0) {
         delete this;
         return 0;
      }
      return m_refCount;
   }

   virtual AMF_RESULT AMF_STD_CALL QueryInterface(const amf::AMFGuid &interfaceID,
                                                  void **ppInterface) override
   {
      if (ppInterface == nullptr) {
         return AMF_INVALID_POINTER;
      }

      if (interfaceID == IID_AMFCaps) {
         *ppInterface = this;
         Acquire();
         return AMF_OK;
      }

      *ppInterface = nullptr;
      return AMF_NO_INTERFACE;
   }

   virtual AMF_RESULT AMF_STD_CALL SetProperty(const wchar_t *name,
                                               amf::AMFVariantStruct value) override
   {
      return AMF_OK;
   }

   virtual AMF_RESULT AMF_STD_CALL GetProperty(const wchar_t *name,
                                               amf::AMFVariantStruct *value) const override
   {

      // Mock behavior: Return 1 for the "hwInstanceCap" property
      if (std::wstring(name) == L"NumOfHwInstances" ||
          std::wstring(name) == L"HevcNumOfHwInstances" ||
          std::wstring(name) == L"Av1CapNumOfHwInstances") {
         value->type = amf::AMF_VARIANT_INT64;
         value->int64Value = 1; // Set the value to 1
         return AMF_OK;
      }
      return AMF_NOT_FOUND;
   }

   virtual amf_bool AMF_STD_CALL HasProperty(const wchar_t *name) const override { return true; }

   virtual amf_size AMF_STD_CALL GetPropertyCount() const override { return 1; }

   virtual AMF_RESULT AMF_STD_CALL GetPropertyAt(amf_size index, wchar_t *name, amf_size nameSize,
                                                 amf::AMFVariantStruct *value) const override
   {
      return AMF_OK;
   }

   virtual AMF_RESULT AMF_STD_CALL Clear() override { return AMF_OK; }

   virtual AMF_RESULT AMF_STD_CALL AddTo(amf::AMFPropertyStorage *dest, amf_bool overwrite,
                                         amf_bool deepCopy) const override
   {
      return AMF_OK;
   }

   virtual AMF_RESULT AMF_STD_CALL CopyTo(amf::AMFPropertyStorage *dest,
                                          amf_bool deepCopy) const override
   {
      return AMF_OK;
   }

   virtual void AMF_STD_CALL AddObserver(amf::AMFPropertyStorageObserver *observer) override {}

   virtual void AMF_STD_CALL RemoveObserver(amf::AMFPropertyStorageObserver *observer) override {}


   // Implement AMFCaps methods
   virtual amf::AMF_ACCELERATION_TYPE AMF_STD_CALL GetAccelerationType() const override
   {
      return amf::AMF_ACCEL_HARDWARE; // Mocked to return hardware acceleration
   }

   virtual AMF_RESULT AMF_STD_CALL GetInputCaps(amf::AMFIOCaps **ppCaps) override
   {
      // Mock input caps
      *ppCaps = nullptr; // Replace with a valid mock if needed
      return AMF_OK;
   }

   virtual AMF_RESULT AMF_STD_CALL GetOutputCaps(amf::AMFIOCaps **ppCaps) override
   {
      if (ppCaps == nullptr) {
         return AMF_INVALID_POINTER;
      }

      // Create and return a valid mock AMFIOCaps instance
      *ppCaps = new MockAMFIOCaps();
      return AMF_OK;
   }

private:
   amf_long m_refCount;
};

class MockAMFContext : public amf::AMFContext {
public:
   MockAMFContext() : m_refCount(1) {}

   virtual ~MockAMFContext() {}

   virtual AMF_RESULT AMF_STD_CALL Terminate() override { return AMF_OK; }

   // Implement AMFInterface methods
   virtual amf_long AMF_STD_CALL Acquire() override { return ++m_refCount; }

   virtual amf_long AMF_STD_CALL Release() override
   {
      if (--m_refCount == 0) {
         delete this;
         return 0;
      }
      return m_refCount;
   }

   virtual AMF_RESULT AMF_STD_CALL QueryInterface(const amf::AMFGuid &interfaceID,
                                                  void **ppInterface) override
   {
      if (ppInterface == nullptr) {
         return AMF_INVALID_POINTER;
      }

      // Check if the requested interface matches this class
      if (interfaceID == IID_AMFContext) {
         *ppInterface = this;
         Acquire();
         return AMF_OK;
      }

      *ppInterface = nullptr;
      return AMF_NO_INTERFACE;
   }


   // Implement AMFPropertyStorage methods
   virtual AMF_RESULT AMF_STD_CALL SetProperty(const wchar_t *name,
                                               amf::AMFVariantStruct value) override
   {
      return AMF_OK;
   }

   virtual AMF_RESULT AMF_STD_CALL GetProperty(const wchar_t *name,
                                               amf::AMFVariantStruct *value) const override
   {
      return AMF_NOT_FOUND;
   }

   virtual amf_bool AMF_STD_CALL HasProperty(const wchar_t *name) const override { return true; }

   virtual amf_size AMF_STD_CALL GetPropertyCount() const override { return 1; }

   virtual AMF_RESULT AMF_STD_CALL GetPropertyAt(amf_size index, wchar_t *name, amf_size nameSize,
                                                 amf::AMFVariantStruct *value) const override
   {
      return AMF_OK;
   }

   virtual AMF_RESULT AMF_STD_CALL Clear() override { return AMF_OK; }

   virtual AMF_RESULT AMF_STD_CALL AddTo(amf::AMFPropertyStorage *dest, amf_bool overwrite,
                                         amf_bool deepCopy) const override
   {
      return AMF_OK;
   }

   virtual AMF_RESULT AMF_STD_CALL CopyTo(amf::AMFPropertyStorage *dest,
                                          amf_bool deepCopy) const override
   {
      return AMF_OK;
   }

   virtual void AMF_STD_CALL AddObserver(amf::AMFPropertyStorageObserver *observer) override {}

   virtual void AMF_STD_CALL RemoveObserver(amf::AMFPropertyStorageObserver *observer) override {}

   // DX9
   virtual AMF_RESULT AMF_STD_CALL InitDX9(void *pDX9Device) override { return AMF_OK; }
   virtual void *AMF_STD_CALL
   GetDX9Device(amf::AMF_DX_VERSION dxVersionRequired = amf::AMF_DX9) override
   {
      return reinterpret_cast<void *>(0x1234); // Mock device pointer
   }
   virtual AMF_RESULT AMF_STD_CALL LockDX9() override { return AMF_OK; }
   virtual AMF_RESULT AMF_STD_CALL UnlockDX9() override { return AMF_OK; }

   // DX11
   virtual AMF_RESULT AMF_STD_CALL InitDX11(void *pDevice, amf::AMF_DX_VERSION dxVersion) override
   {
      // Mock implementation for InitDX11
      return AMF_OK;
   }
   virtual void *AMF_STD_CALL
   GetDX11Device(amf::AMF_DX_VERSION dxVersionRequired = amf::AMF_DX11_0) override
   {
      return reinterpret_cast<void *>(0x5678); // Mock device pointer
   }
   virtual AMF_RESULT AMF_STD_CALL LockDX11() override { return AMF_OK; }
   virtual AMF_RESULT AMF_STD_CALL UnlockDX11() override { return AMF_OK; }

   // OpenCL
   virtual AMF_RESULT AMF_STD_CALL InitOpenCL(void *pCommandQueue = NULL) override
   {
      return AMF_OK;
   }
   virtual void *AMF_STD_CALL GetOpenCLContext() override
   {
      return reinterpret_cast<void *>(0x1111); // Mock OpenCL context
   }
   virtual void *AMF_STD_CALL GetOpenCLCommandQueue() override
   {
      return reinterpret_cast<void *>(0x2222); // Mock OpenCL command queue
   }
   virtual void *AMF_STD_CALL GetOpenCLDeviceID() override
   {
      return reinterpret_cast<void *>(0x3333); // Mock OpenCL device ID
   }
   virtual AMF_RESULT AMF_STD_CALL
   GetOpenCLComputeFactory(amf::AMFComputeFactory **ppFactory) override
   {
      *ppFactory = nullptr;
      return AMF_OK;
   }
   virtual AMF_RESULT AMF_STD_CALL InitOpenCLEx(amf::AMFComputeDevice *pDevice) override
   {
      return AMF_OK;
   }
   virtual AMF_RESULT AMF_STD_CALL LockOpenCL() override { return AMF_OK; }
   virtual AMF_RESULT AMF_STD_CALL UnlockOpenCL() override { return AMF_OK; }

   // OpenGL
   virtual AMF_RESULT AMF_STD_CALL InitOpenGL(amf_handle hOpenGLContext, amf_handle hWindow,
                                              amf_handle hDC) override
   {
      return AMF_OK;
   }
   virtual amf_handle AMF_STD_CALL GetOpenGLContext() override
   {
      return reinterpret_cast<amf_handle>(0x4444); // Mock OpenGL context
   }
   virtual amf_handle AMF_STD_CALL GetOpenGLDrawable() override
   {
      return reinterpret_cast<amf_handle>(0x5555); // Mock OpenGL drawable
   }
   virtual AMF_RESULT AMF_STD_CALL LockOpenGL() override { return AMF_OK; }
   virtual AMF_RESULT AMF_STD_CALL UnlockOpenGL() override { return AMF_OK; }

   // XV - Linux
   virtual AMF_RESULT AMF_STD_CALL InitXV(void *pXVDevice) override { return AMF_OK; }
   virtual void *AMF_STD_CALL GetXVDevice() override
   {
      return reinterpret_cast<void *>(0x6666); // Mock XV device
   }
   virtual AMF_RESULT AMF_STD_CALL LockXV() override { return AMF_OK; }
   virtual AMF_RESULT AMF_STD_CALL UnlockXV() override { return AMF_OK; }

   // Gralloc - Android
   virtual AMF_RESULT AMF_STD_CALL InitGralloc(void *pGrallocDevice) override { return AMF_OK; }
   virtual void *AMF_STD_CALL GetGrallocDevice() override
   {
      return reinterpret_cast<void *>(0x7777); // Mock Gralloc device
   }
   virtual AMF_RESULT AMF_STD_CALL LockGralloc() override { return AMF_OK; }
   virtual AMF_RESULT AMF_STD_CALL UnlockGralloc() override { return AMF_OK; }

   // Allocation
   virtual AMF_RESULT AMF_STD_CALL AllocBuffer(amf::AMF_MEMORY_TYPE type, amf_size size,
                                               amf::AMFBuffer **ppBuffer) override
   {
      *ppBuffer = nullptr;
      return AMF_OK;
   }
   virtual AMF_RESULT AMF_STD_CALL AllocSurface(amf::AMF_MEMORY_TYPE type,
                                                amf::AMF_SURFACE_FORMAT format, amf_int32 width,
                                                amf_int32 height,
                                                amf::AMFSurface **ppSurface) override
   {
      *ppSurface = new MockSurface();
      return AMF_OK;
   }
   virtual AMF_RESULT AMF_STD_CALL AllocAudioBuffer(amf::AMF_MEMORY_TYPE type,
                                                    amf::AMF_AUDIO_FORMAT format, amf_int32 samples,
                                                    amf_int32 sampleRate, amf_int32 channels,
                                                    amf::AMFAudioBuffer **ppAudioBuffer) override
   {
      *ppAudioBuffer = nullptr;
      return AMF_OK;
   }


   virtual AMF_RESULT AMF_STD_CALL
   CreateBufferFromHostNative(void *pHostBuffer, amf_size size, amf::AMFBuffer **ppBuffer,
                              amf::AMFBufferObserver *pObserver) override
   {
      *ppBuffer = nullptr;
      return AMF_OK;
   }
   virtual AMF_RESULT AMF_STD_CALL CreateSurfaceFromHostNative(
      amf::AMF_SURFACE_FORMAT format, amf_int32 width, amf_int32 height, amf_int32 hPitch,
      amf_int32 vPitch, void *pData, amf::AMFSurface **ppSurface,
      amf::AMFSurfaceObserver *pObserver) override
   {
      *ppSurface = nullptr;
      return AMF_OK;
   }
   virtual AMF_RESULT AMF_STD_CALL CreateSurfaceFromDX9Native(
      void *pDX9Surface, amf::AMFSurface **ppSurface, amf::AMFSurfaceObserver *pObserver) override
   {
      *ppSurface = nullptr;
      return AMF_OK;
   }

   virtual AMF_RESULT AMF_STD_CALL CreateSurfaceFromDX11Native(
      void *pDX11Surface, amf::AMFSurface **ppSurface, amf::AMFSurfaceObserver *pObserver) override
   {
      *ppSurface = new MockSurface();
      return AMF_OK;
   }
   virtual AMF_RESULT AMF_STD_CALL CreateSurfaceFromOpenGLNative(
      amf::AMF_SURFACE_FORMAT format, amf_handle hGLTextureID, amf::AMFSurface **ppSurface,
      amf::AMFSurfaceObserver *pObserver) override
   {
      *ppSurface = nullptr;
      return AMF_OK;
   }
   virtual AMF_RESULT AMF_STD_CALL
   CreateSurfaceFromGrallocNative(amf_handle hGrallocSurface, amf::AMFSurface **ppSurface,
                                  amf::AMFSurfaceObserver *pObserver) override
   {
      *ppSurface = nullptr;
      return AMF_OK;
   }
   virtual AMF_RESULT AMF_STD_CALL CreateSurfaceFromOpenCLNative(
      amf::AMF_SURFACE_FORMAT format, amf_int32 width, amf_int32 height, void **pClPlanes,
      amf::AMFSurface **ppSurface, amf::AMFSurfaceObserver *pObserver) override
   {
      *ppSurface = nullptr;
      return AMF_OK;
   }
   virtual AMF_RESULT AMF_STD_CALL CreateBufferFromOpenCLNative(void *pCLBuffer, amf_size size,
                                                                amf::AMFBuffer **ppBuffer) override
   {
      *ppBuffer = nullptr;
      return AMF_OK;
   }

   // Access to AMFCompute interface
   virtual AMF_RESULT AMF_STD_CALL GetCompute(amf::AMF_MEMORY_TYPE eMemType,
                                              amf::AMFCompute **ppCompute) override
   {
      *ppCompute = nullptr;
      return AMF_OK;
   }

private:
   amf_long m_refCount;
};


// Mock implementation of AMFComponent
class MockAMFComponent : public amf::AMFComponent {
public:
   MockAMFComponent() : m_refCount(1) {}
   virtual ~MockAMFComponent() {}

   // Implement AMFInterface methods
   virtual amf_long AMF_STD_CALL Acquire() override { return ++m_refCount; }

   virtual amf_long AMF_STD_CALL Release() override
   {
      if (--m_refCount == 0) {
         delete this;
         return 0;
      }
      return m_refCount;
   }

   virtual AMF_RESULT AMF_STD_CALL QueryInterface(const amf::AMFGuid &interfaceID,
                                                  void **ppInterface) override
   {
      if (ppInterface == nullptr) {
         return AMF_INVALID_POINTER;
      }

      // Check if the requested interface matches this mock component
      if (interfaceID == IID_AMFComponent) {
         *ppInterface = this;
         Acquire();
         return AMF_OK;
      }

      *ppInterface = nullptr;
      return AMF_NO_INTERFACE;
   }

   // Implement AMFPropertyStorage methods
   virtual AMF_RESULT AMF_STD_CALL SetProperty(const wchar_t *name,
                                               amf::AMFVariantStruct value) override
   {
      // m_properties[name] = value;
      return AMF_OK;
   }

   virtual AMF_RESULT AMF_STD_CALL GetProperty(const wchar_t *name,
                                               amf::AMFVariantStruct *value) const override
   {
      return AMF_OK;
   }

   virtual amf_bool AMF_STD_CALL HasProperty(const wchar_t *name) const override { return true; }

   virtual amf_size AMF_STD_CALL GetPropertyCount() const override { return 1; }

   virtual AMF_RESULT AMF_STD_CALL GetPropertyAt(amf_size index, wchar_t *name, amf_size nameSize,
                                                 amf::AMFVariantStruct *value) const override
   {
      return AMF_OK;
   }

   virtual AMF_RESULT AMF_STD_CALL Clear() override { return AMF_OK; }

   virtual AMF_RESULT AMF_STD_CALL AddTo(amf::AMFPropertyStorage *dest, amf_bool overwrite,
                                         amf_bool deepCopy) const override
   {
      return AMF_OK;
   }

   virtual AMF_RESULT AMF_STD_CALL CopyTo(amf::AMFPropertyStorage *dest,
                                          amf_bool deepCopy) const override
   {
      return AMF_OK;
   }

   virtual void AMF_STD_CALL AddObserver(amf::AMFPropertyStorageObserver *observer) override {}

   virtual void AMF_STD_CALL RemoveObserver(amf::AMFPropertyStorageObserver *observer) override {}

   // Implement AMFPropertyStorageEx methods
   virtual amf_size AMF_STD_CALL GetPropertiesInfoCount() const override { return 1; }

   virtual AMF_RESULT AMF_STD_CALL
   GetPropertyInfo(const wchar_t *name, const amf::AMFPropertyInfo **ppInfo) const override
   {
      return AMF_NOT_FOUND;
   }

   virtual AMF_RESULT AMF_STD_CALL
   GetPropertyInfo(amf_size index, const amf::AMFPropertyInfo **ppInfo) const override
   {
      return AMF_OK;
   }

   virtual AMF_RESULT AMF_STD_CALL
   ValidateProperty(const wchar_t *name, amf::AMFVariantStruct value,
                    amf::AMFVariantStruct *pOutValidatedValue) const override
   {
      return AMF_NOT_FOUND;
   }

   // Implement AMFComponent methods
   virtual AMF_RESULT AMF_STD_CALL Init(amf::AMF_SURFACE_FORMAT format, amf_int32 width,
                                        amf_int32 height) override
   {
      m_initialized = true;
      return AMF_OK;
   }

   virtual AMF_RESULT AMF_STD_CALL ReInit(amf_int32 width, amf_int32 height) override
   {
      return AMF_OK;
   }

   virtual AMF_RESULT AMF_STD_CALL Terminate() override
   {
      m_initialized = false;
      return AMF_OK;
   }

   virtual AMF_RESULT AMF_STD_CALL Drain() override { return AMF_OK; }

   virtual AMF_RESULT AMF_STD_CALL Flush() override { return AMF_OK; }

   virtual AMF_RESULT AMF_STD_CALL SubmitInput(amf::AMFData *pData) override { return AMF_OK; }

   virtual AMF_RESULT AMF_STD_CALL QueryOutput(amf::AMFData **ppData) override
   {
      *ppData = new MockAMFData();
      return AMF_OK;
   }

   virtual amf::AMFContext *AMF_STD_CALL GetContext() override { return nullptr; }

   virtual AMF_RESULT AMF_STD_CALL
   SetOutputDataAllocatorCB(amf::AMFDataAllocatorCB *callback) override
   {
      return AMF_OK;
   }

   virtual AMF_RESULT AMF_STD_CALL GetCaps(amf::AMFCaps **ppCaps) override
   {
      *ppCaps = new MockAMFCaps();
      return AMF_OK;
   }

   virtual AMF_RESULT AMF_STD_CALL
   Optimize(amf::AMFComponentOptimizationCallback *callback) override
   {
      return AMF_OK;
   }

private:
   amf_long m_refCount;
   bool m_initialized = false;
};


class MockAMFFactory : public amf::AMFFactory {
public:
   // Implement CreateContext
   virtual AMF_RESULT AMF_STD_CALL CreateContext(amf::AMFContext **ppContext) override
   {
      *ppContext = new MockAMFContext();
      return AMF_OK;
   }

   // Implement CreateComponent
   virtual AMF_RESULT AMF_STD_CALL CreateComponent(amf::AMFContext *pContext, const wchar_t *id,
                                                   amf::AMFComponent **ppComponent) override
   {
      *ppComponent = new MockAMFComponent();
      return AMF_OK;
   }

   virtual AMF_RESULT AMF_STD_CALL SetCacheFolder(const wchar_t *path) override { return AMF_OK; }

   virtual const wchar_t *AMF_STD_CALL GetCacheFolder() override
   {
      return L""; // Example: return an empty string
   }

   virtual AMF_RESULT AMF_STD_CALL GetDebug(amf::AMFDebug **ppDebug) override
   {
      *ppDebug = nullptr;
      return AMF_OK;
   }

   virtual AMF_RESULT AMF_STD_CALL GetTrace(amf::AMFTrace **ppTrace) override
   {
      *ppTrace = nullptr;
      return AMF_OK;
   }

   virtual AMF_RESULT AMF_STD_CALL GetPrograms(amf::AMFPrograms **ppPrograms) override
   {
      *ppPrograms = nullptr;
      return AMF_OK;
   }
};

// Mock implementation of AMFInit
AMF_RESULT AMF_CDECL_CALL
MockAMFInit(amf_uint64 version, amf::AMFFactory **ppFactory)
{
   *ppFactory = new MockAMFFactory(); // Return a mock factory instance
   return AMF_OK;
}

// Mock implementation of AMFQueryVersion
AMF_RESULT AMF_CDECL_CALL
MockAMFQueryVersion(amf_uint64 *pVersion)
{
   *pVersion = 0x01020000; // Example version: 1.2.0.0
   return AMF_OK;
}

#endif // CEDAR_PLATFORM_WINDOWS