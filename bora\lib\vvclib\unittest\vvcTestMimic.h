/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

#ifdef __cplusplus
extern "C" {
#endif

VvcSession *VvcMimicCreateSession(VvcInstanceHandle instanceHandle,
                                  VvcSessionHandle *sessionHandle);

void VvcMimicCloseSession(VvcSession *session);

void VvcMimicCreateListener(char *name, VvcListenerHandle *listener);

VvcChannel *VvcMimicCreateChannel(VvcListenerHandle listener, VvcSessionHandle sessionHandle,
                                  VvcSession *session, int flags);

void VvcMimicRemoveChannel(VvcChannel *channel);
void VvcMimicCloseListener(VvcListenerHandle listenerHandle);
Bool VvcTestIsRawChannel(VvcChannel *channel);
#ifdef __cplusplus
}
#endif
