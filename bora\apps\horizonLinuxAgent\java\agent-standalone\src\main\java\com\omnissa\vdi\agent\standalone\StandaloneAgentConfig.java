/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */
package com.omnissa.vdi.agent.standalone;

import java.io.BufferedInputStream;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.Integer;
import java.lang.NumberFormatException;
import java.net.UnknownHostException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.PosixFilePermissions;
import java.security.KeyPair;
import java.security.KeyStore;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Properties;
import java.util.UUID;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import java.util.regex.Matcher;

import com.omnissa.vdi.agent.standalone.uem.UEMMountManager;
import com.omnissa.vdi.agent.standalone.uem.UEMPolicy;
import com.omnissa.vdi.agent.standalone.utility.Utils;
import org.apache.commons.lang3.StringUtils;

import com.omnissa.vdi.agent.common.Machine;
import com.omnissa.vdi.agent.common.Protocol;
import com.omnissa.vdi.agent.messageserver.AgentJmsConfig;
import com.omnissa.vdi.commonutils.RenderList;
import com.omnissa.vdi.commonutils.SslUtils;
import com.omnissa.vdi.commonutils.SslUtils.sslRole;
import com.omnissa.vdi.commonutils.Version;
import com.omnissa.vdi.logger.Logger;
import com.omnissa.vdi.messagesecurity.Identity;
import com.omnissa.vdi.messagesecurity.JMSMessageSecurity;
import com.omnissa.vdi.messagesecurity.MessageSecurityHandler.MSSecMode;
import com.omnissa.vdi.messagesecurity.swiftmq.FileBasedCertificateManager;
import com.omnissa.vdi.messagesecurity.swiftmq.FileBasedSSLSocketFactory;

public class StandaloneAgentConfig extends AgentJmsConfig {
    private static Logger log = Logger.getLogger(StandaloneAgentConfig.class);

    /*
     * file-based config parameters
     */

    private static final long MAX_MACHINE_CONFIG_FILE_LENGTH = 50 * 1024; // 50KB

    private static final String BASE_DN = "dc=vdi,dc=horizon,dc=internal";

    private static final String PARAM_NAME = "NAME";

    private static final String PARAM_HOSTNAME = "HOSTNAME";

    private static final String PARAM_RUNONCE = "RUNONCE";

    private static final String PARAM_MANAGED = "MANAGED";

    private static final String PARAM_USBINST = "USBINST";

    private static final String PARAM_BLAST_KEYSTORE_PATH = "BLAST_KEYSTORE_PATH";

    private static final String PARAM_CERTIFICATE_PATH = "CERTIFICATE_PATH";

    private static final String PARAM_DOMAIN_NAME = "DOMAIN";

    private static final String PARAM_JMS_KEYSTORE_DIR = "JMS_KEYSTORE_DIR";

    private static final String PARAM_JMS_KEYSTORE_PASSWORD = "JMS_KEYSTORE_PASSWORD";

    private static final String PARAM_MACHINE_CONFIG_FILE = "MACHINE_CONFIG_FILE";

    private static final String PARAM_REMOTE_CONFIG_FILE = "REMOTE_CONFIG_FILE";

    private static final String PARAM_OS_VERSION_LDAP = "OS_VERSION_LDAP";

    private static final String PARAM_OS_VERSION_MAJOR = "OS_VERSION_MAJOR";

    private static final String PARAM_OS_VERSION_MINOR = "OS_VERSION_MINOR";

    private static final String PARAM_OS_SYSTEM_UUID = "OS_SYSTEM_UUID";

    private static final String PARAM_PLATFORM = "PLATFORM";

    private static final String PARAM_PROTOCOL = "PROTOCOL";

    private static final String PARAM_FIPS_ENABLE = "FIPS_ENABLE";

    private static final String PARAM_PROTOCOL_PORT = "PROTOCOL_PORT";

    private static final String PARAM_IPC_ENABLE = "IPC_ENABLE";

    private static final String PARAM_CDR_INSTALL = "CDRINST";

    private static final String PARAM_CLIPBOARD_INSTALL = "CLIPBOARDINST";

    private static final String PARAM_HTML5MMR_INSTALL = "HTML5MMRINST";

    private static final String PARAM_APP_INSTALL = "APPINST";

    private static final String PARAM_VADC_INSTALL = "VADCINST";

    private static final String PARAM_VGPU_ENABLE = "VGPU_ENABLE";

    private static final String PARAM_PROGRAM_DESKTOPDAEMON = "DESKTOP_DAEMON_PROGRAM";

    private static final String PARAM_PRINTREDIR_INSTALL = "PRINTERREDIRINST";

    private static final String PARAM_PROGRAM_BLASTSERVER = "BLAST_SERVER_PROGRAM";

    private static final String PARAM_COLLABORATION_UI_PATH = "COLLABORATION_UI_PATH";

    private static final String PARAM_SCRIPT_STARTRDESERVER = "START_RDE_SERVER_SCRIPT";

    private static final String PARAM_SCRIPT_RUNAPPSCANNER = "RUN_APPSCANNER_SCRIPT";

    private static final String PARAM_SCRIPT_LAUNCHAPP = "LAUNCH_APP_SCRIPT";

    private static final String PARAM_SCRIPT_CONTROL_EXTENSION = "CONTROL_EXTENSION_SCRIPT";

    private static final String PARAM_SCRIPT_CLOSEAPP  = "CLOSE_APP_SCRIPT";

    private static final String PARAM_SCRIPT_PERSISTENTDISK = "PERSISTENTDISK_SCRIPT";

    private static final String PARAM_SCRIPT_POSTLOGIN = "POSTLOGIN_SCRIPT";

    private static final String PARAM_SCRIPT_POSTLOGIN_USER = "POSTLOGIN_USER_SCRIPT";

    private static final String PARAM_SCRIPT_POST_CONNECTION = "POSTCONNECTION_SCRIPT";

    private static final String PARAM_SCRIPT_CONSOLE_MODE = "CONSOLEMODE_SCRIPT";

    private static final String PARAM_SCRIPT_RUN_AUTORANDR = "RUNAUTORANDR_SCRIPT";

    private static final String PARAM_SSO_INSTALL = "SSOINST";

    private static final String PARAM_TRUESSO_INSTALL = "TRUESSOINST";

    private static final String PARAM_MAX_SESSION_THREADS = "MAX_SESSION_THREADS";

    private static final String PARAM_SCRIPT_STARTXSERVER = "START_XSERVER_SCRIPT";

    private static final String PARAM_SCRIPT_STARTBLASTWORKER = "START_BLASTWORKER_SCRIPT";

    private static final String PARAM_SCRIPT_STARTCLIPBOARD = "START_CLIPBOARD_SCRIPT";

    private static final String PARAM_SCRIPT_STARTCDR = "START_CDR_SCRIPT";

    private static final String PARAM_SCRIPT_STARTHTML5MMR = "START_HTML5MMR_SCRIPT";

    private static final String PARAM_SCRIPT_STARTPRINTREDIR = "START_PRINTREDIR_SERVER_SCRIPT";


    private static final String PARAM_SCRIPT_STARTUSBREDIR = "START_USBREDIR_SCRIPT";

    private static final String PARAM_SCRIPT_STOPUSBREDIR = "STOP_USBREDIR_SCRIPT";

    private static final String PARAM_SCRIPT_SSO_CONFIG_DESKTOP_TYPE = "SSO_CONFIG_DESKTOP_TYPE_SCRIPT";

    private static final String PARAM_SCRIPT_CLEANLOG = "CLEANLOG_SCRIPT";

    private static final String PARAM_SCRIPT_MOUNTFOLDER = "MOUNT_FOLDER_SCRIPT";

    private static final String PARAM_SCRIPT_UMOUNTFOLDER = "UMOUNT_FOLDER_SCRIPT";

    private static final String PARAM_CUSTOM_CONFIG_FILE = "CUSTOM_CONFIG_FILE";

    private static final String PARAM_CUSTOM_CONFIG_FILE_BLAST = "CUSTOM_CONFIG_FILE_BLAST";

    private static final String PARAM_CUSTOMIZATION_STATE = "CUSTOMIZATION_STATE";

    private static final String PARAM_CONSOLE_MODE = "CONSOLE_MODE";

    private static final String PARAM_SINGLETON_MODE = "SINGLETON_MODE";

    private static final String PARAM_USE_IPV6 = "USE_IPV6";

    private static final String PARAM_XSERVER_OMNMEM_ENABLED = "XSERVER_OMNMEM_ENABLED";

    private static final String PARAM_HZNGREETER_ENABLE = "HZNGREETER_ENABLE";

    /*
     * optional file-based config parameters
     */

    private static final String PARAM_INITIAL_NOTIFICATION_DELAY = "INITIAL_NOTIFICATION_DELAY";

    /*
     * optional customer config parameters
     */
    private static final String CPARAM_USB_ENABLE = "USBEnable";

    private static final String CPARAM_SSO_ENABLE = "SSOEnable";

    private static final String CPARAM_SSO_USER_FORMAT = "SSOUserFormat";

    private static final String CPARAM_SSO_DESKTOP_TYPE = "SSODesktopType";

    private static final String CPARAM_START_BLAST_SERVER_TIMEOUT = "StartBlastServerTimeout";

    private static final String CPARAM_PENDING_SESSION_TIMEOUT = "PendingSessionTimeout";

    private static final String CPARAM_SSL_PROTOCOLS = "SSLProtocols";

    private static final String CPARAM_SSL_CIPHERS = "SSLCiphers";

    private static final String CPARAM_TLS_CIPHERS_SUITES = "TLSCipherSuites";

    private static final String CPARAM_SSL_CERT_NAME = "SSLCertName";

    private static final String CPARAM_SSL_KEY_NAME = "SSLKeyName";

    private static final String CPARAM_TSL_CURVES = "TLSCurves";

    private static final String CPARAM_SSL_CIPHER_SERVER_PREFERENCE = "SSLCipherServerPreference";

    private static final String CPARAM_SUBNET = "Subnet";

    private static final String CPARAM_SUBNET6 = "Subnet6";

    private static final String CPARAM_RESERVED_LOG_COUNT = "LogCnt";

    private static final String CPARAM_RUN_ONCE_SCRIPT = "RunOnceScript";

    private static final String CPARAM_RUN_ONCE_SCRIPT_TIMER = "RunOnceScriptTimeout";

    private static final String CPARAM_CDR_ENABLE = "CDREnable";

    private static final String CPARAM_BLOCK_SCREEN_CPATURE_ENABLE = "BlockScreenCaptureEnable";

    private static final String CPARAM_UEM_ENABLE = "UEMEnable";

    private static final String CPARAM_DEM_ENABLE = "DEMEnable";

    private static final String CPARAM_UEM_NETWORK_PATH = "UEMNetworkPath";

    private static final String CPARAM_DEM_NETWORK_PATH = "DEMNetworkPath";

    private static final String CPARAM_UEM_MOUNT_TIMEOUT = "UEMMountTimeout";

    private static final String CPARAM_DEM_MOUNT_TIMEOUT = "DEMMountTimeout";

    private static final String CPARAM_ENDPOINT_VPN_ENABLE = "EndpointVPNEnable";

    private static final String CPARAM_HELPDESK_ENABLE = "HelpDeskEnable";

    private static final String CPARAM_PRINTREDIR_ENABLE = "PrintRedirEnable";

    private static final String CPARAM_EMPTY_SESSION_DELAY = "EMPTY_SESSION_DELAY";

    private static final String CPARAM_MAX_SESSIONS_BUFFER = "MaxSessionsBuffer";

    private static final String CPARAM_START_STORM_THROTTLE = "StartStormThrottle";

    private static final String CPARAM_DPI_SYNC_ENABLE = "DPISyncEnable";

    private static final String CPARAM_KEYBOARD_LAYOUT_SYNC_ENABLE = "KeyboardLayoutSync";

    private static final String CPARAM_LOCALE_SYNC_ENABLE = "LocaleSync";

    private static final String CPARAM_NETBIOS_DOMAIN = "NetbiosDomain";

    private static final String PARAM_COLLABINST = "COLLABINST";

    private static final String CPARAM_COLLABORATION_ENABLE = "CollaborationEnable";

    private static final String CPARAM_SSO_TIMEOUT = "SsoTimeout";

    private static final String CPARAM_DELETE_FAILED_CLONED_VM = "DeleteFailedClonedVM";

    private static final String CPARAM_VADC_CONFIG_FILE = "VadcConfigFile";

    private static final String CPARAM_APP_ENABLE = "AppEnable";

    private static final String CPARAM_DOMAIN_CONTROLLER_FQDN = "DomainControllerFqdn";

    private static final String CPARAM_LB_CPU_WEIGHTAGE = "LBCpuWeightage";

    private static final String CPARAM_LB_MEMORY_WEIGHTAGE = "LBMemWeightage";

    private static final String CPARAM_LB_DISK_WEIGHTAGE = "LBDiskWeightage";

    private static final String CPARAM_LB_SESSION_COUNT_WEIGHTAGE = "LBSessionCountWeightage";

    private static final String CPARAM_LB_CUSTOM_SCRIPT = "LBCustomScript";

    private static final String CPARAM_LB_CUSTOM_SCRIPT_RESULT = "LBCustomScriptResult";

    private static final String CPARAM_LB_POOL_INTERVAL_SECONDS = "LBPoolInternal";

    private static final String CPARAM_LB_vGPU_MEMORY_USAGE_ENABLE = "LBNvidiaGPUMemoryUsageEnable";

    private String fqdn = "";

    /*
     * Application icon and xml parameters
     */
    private static final String PARAM_APP_ICON_DIR = "APP_ICON_DIR";

    private static final String PARAM_APP_XML_FILE = "APP_XML_FILE";

    /*
     * default values
     */
    private final static int DEFAULT_PORT = 5443;

    private final static Protocol DEFAULT_PROTOCOL = Protocol.BLAST;

    private final static int DEFAULT_SESSIONS_BUFFER = 1;

    private final static int DEFAULT_STARTSTORM_INTERVAL = 5;

    private final static int STARTSTORM_DELAY_MAX = 10;

    private final static double LB_DEFAULT_WEIGHTAGE = 5;

    private static final String DEFAULT_BLAST_PROTOCOLS = "TLSv1.3:TLSv1.2";

    private static final String DEFAULT_BLAST_SSL_CIPHERS = "ECDHE+AESGCM";

    private static final String FIPS_BLAST_SSL_CIPHERS = "ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-GCM-SHA256";

    // Only used by TLSv1.3
    private static final String DEFAULT_BLAST_TLS_CIPHERS = "TLS_AES_256_GCM_SHA384:TLS_AES_128_GCM_SHA256";

    private static final String FIPS_BLAST_TLS_CURVER = "prime256v1:secp384r1:secp521r1";

    /*
     * per agent config parameters
     */
    private String name;

    private String hostName;

    private boolean runOnce;

    private boolean managedVM;

    private boolean cdrEnabled = true;

    private boolean blockScreenCaptureEnabled = false;

    private boolean cdrInstalled = false;

    private boolean clipboardInstalled = false;

    private boolean html5mmrInstalled = false;

    private boolean appInstalled = false;

    private boolean vadcInstalled = false;

    private boolean printerRedirEnabled = true;

    private boolean printerRedirInstalled = true;

    private boolean ssoInstalled = true;

    private boolean trueSsoInstalled = false;

    private boolean helpDeskEnabled = true;

    private boolean usbInst = false;

    private boolean uemEnable = true;

    private String uemNetworkPath = null;

    private long uemMountTimeout = 60000;

    private double lbCpuWeightage = LB_DEFAULT_WEIGHTAGE;

    private double lbMemWeightage = LB_DEFAULT_WEIGHTAGE;

    private double lbDiskWeightage = LB_DEFAULT_WEIGHTAGE;

    private double lbSessionCountWeightage = LB_DEFAULT_WEIGHTAGE;

    private String lbCustomScript = null;

    private String lbCustomScriptResult = null;

    private boolean appEnable = true;

    /**
     * Load balance pool timer interval
     * Not publish at config file
     */
    private int lbPoolIntervalSeconds = 30;

    private boolean lbNvidiaGPUMemoryUsageEnable = true;

    private boolean endpointVPNEnable = false;

    private String blastKeystorePath;

    private String certificatePath;

    private String domainName;

    private String netbiosDomain;

    private String domainController;

    private String jmsKeystoreDirectory;

    private KeyStore.PasswordProtection jmsKeystorePassword;

    private boolean fipsEnable = true;

    private boolean agentShutDown = false;

    // indicated if agent is VDI mode or multi-session mode.
    private boolean singletonMode = true;

    // Indicate if use IPv6
    private boolean useIPv6 = false;

    // If use omnmem_drv as X server driver
    private boolean omnMemEnabled = true;

    private boolean hznGreeterEnabled = true;

    private boolean vgpuEnabled = false;

    /**
     * persistent machine data is written here in plain text so agent can
     * survive restarts
     *
     * NOTE: private key is also written in plain text to this file TODO improve
     * above
     */
    private String machineConfigFile;

    private String osVersionLdap;

    private String osVersionMajor;

    private String osVersionMinor;

    private String osSystemUUID;

    private String platform;

    private String appIconDir;

    private String appXmlFile;

    private Protocol protocol = DEFAULT_PROTOCOL;

    private int protocolPort = DEFAULT_PORT;

    private String customerConfigFile = null;

    private String customerConfigFileBlast = null;

    private String vadcConfigFile = "/etc/omnissa/vadc/viewagent-vadc.conf";

    /**
     * program file to run BlastServer on the VM
     */
    private String programBlastServer = null;

    /**
     * program file to run DesktopDeamon on the VM
     */
    private String programDesktopDaemon = null;

    /**
     * collaboration UI path
     */
    private String collabUIPath = null;

    /**
     * script file to run to create a user session on the VM
     */
    private String scriptStartXServer = null;

    /**
     * script file to run to create a user session on the VM
     */
    private String scriptStartBlastWorker = null;

    /**
     * script file to start clipboard.
     */
    private String scriptStartClipboard = null;

    /**
     * script file to start CDR.
     */
    private String scriptStartCDR = null;

    /**
     * script file to start html5mmr.
     */
    private String scriptStartHtml5mmr = null;

     /**
     * script file to run to start rdeSvc
     */
    private String scriptStartRDEServer = null;

    /**
     * script file to run appScanner
     */
    private String scriptRunAppScanner = null;

    /**
     * script file to run to launch app
     */
    private String scriptLaunchApp = null;

     /**
     * script file to control gnome-shell extension
     */
    private String scriptControlExtension = null;

    /**
     * script file to run to stop app
     */
    private String scriptStopApp = null;

    /**
     * script file to run to mount persistent disk
     */
    private String scriptPersistentDisk = null;

    /**
     * script file to run after user logined
     */
    private String scriptPostLogin = null;

    /**
     * script file to run after user logined in desktopController
     */
    private String scriptPostLoginUser = null;

    /**
     * script file to run after blast connection
     */
    private String scriptPostConnection = null;

    /**
     * script file to run after remote session is disconnected
     * and before the agent is notified
     */
    private String scriptConsoleMode = null;

    /**
     * script file to run autorandr
     */
    private String scriptRunAutorandr = null;

    /**
     * script file to run to start usbredir server
     */
    private String scriptStartUsbRedir = null;

    /**
     * script file to run to stop usbredir server
     */
    private String scriptStopUsbRedir = null;

    /**
     * script file to run to start print redirection server
     */
    private String scriptStartPrintRedir = null;

    /**
     * script file to configure desktop type when Single Sign On
     */
    private String scriptSsoConfigDesktopType = null;

    /**
     * script file to clean the log files
     */
    private String scriptCleanLog = null;

    /**
     * script file to mount the folder
     */
    private String scriptMountFolder = null;

    /**
     * script file to unmount the folder
     */
    private String scriptUMountFolder = null;

    /*
     * customer optional config parameters
     */
    private boolean enableUSB = true;

    private boolean enableSSO = true;

    private boolean sviState = false;

    private String ssoUserFormat = null;

    private String ssoDesktopType = null;

    private int startBlastServerTimeout = 20000;

    private int pendingSessionTimeout = 15;

    private boolean delFailedClonedVM = true;

    private int ssoTimeout = 60;

    private String sslProtocols = null;

    private String sslCiphers = DEFAULT_BLAST_SSL_CIPHERS;

    private String tlsCipherSuites = DEFAULT_BLAST_TLS_CIPHERS;

    private String sslCertName = "hznblast:cert";

    private String sslKeyName = "hznblast:key";

    private boolean keyringsEnabled = false;

    private String tlsCurves = "prime256v1:secp384r1:secp521r1";

    private boolean sslCipherServerPreference = true;

    private String subnet = null;

    private String subnet6 = null;

    private int logcnt = -1;

    private String runOnceScript = null;

    private String runOnceScriptTimeout = null;

    private boolean enableDpiSync = true;

    private boolean enableKeyboardLayoutSync = true;

    private boolean enableLocaleSync = false;

    private boolean ipcEnabled = true;

    private boolean collaborationInst = false;

    private boolean collaborationEnabled = true;

    /*
     * The max number of free sessions.
     */
    private int maxSessionsBuffer = DEFAULT_SESSIONS_BUFFER;

    /*
     * The throttle for start storm.
     */
    private int startStormThrottle = 5;

    /*
     * The max session threads.
     */
    private int maxSessionThreads = 1;

    /*
     * Delayed seconds before disconnect empty session immediately for tmporary
     * app window, the defulat value is 1 second.
     */
    private int emptySessionDelay = 1;

    /*
     * per agent optional config parameters
     */

    private int initialNotificationDelay = 5000;

    /**
     * determines if pairing is complete for the agent
     */
    private boolean isPaired;

    /**
     * determines if support console desktop.
     */
    private boolean enableConsoleMode = false;

    /**
     * paired private key
     */
    private PrivateKey agentPairedPrivateKey;

    /**
     * paired public key
     */
    private PublicKey agentPairedPublicKey;

    private String agentPairedPrivateKeyString;

    private String agentPairedPublicKeyString;

    private String brokerSSLCertificateThumbprint = "";

    private String agentSSLCertificateThumbprint = "";

    private Machine agentVM = null;

    private Lock loadConfLock = new ReentrantLock();

    private Map<Long, String> keyboardLayoutMap = null;

    private Map<String, String> keyboardLanguageMap = null;

    private static final String [] FIPS_SERVER_PROTOCOLS = { "TLSv1.2", "TLSv1.3" };

    private static final String [] FIPS_SERVER_CIPHERSUITES = {
            "TLS_AES_256_GCM_SHA384",
            "TLS_AES_128_GCM_SHA256",
            "TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384",
            "TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256"};

    private static final String[] FIPS_CIPHERSUITES = {
            "TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384",
            "TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256" };

    private static final String[] FIPS_SECURE_PROTOCOLS = { "TLSv1.2" };

    private static final String[] FIPS_THUMBPRINT_ALGORITHMS = { "SHA_512", "SHA_384" , "SHA_256", "SHA_1" };

    private static final String[] FIPS_CLIENT_SIGNATURE_SCHEMES = { "rsa_pss_rsae_sha512",
                                                                    "rsa_pss_rsae_sha256",
                                                                    "rsa_pss_rsae_sha384",
                                                                    "rsa_pss_pss_sha512",
                                                                    "rsa_pss_pss_sha256",
                                                                    "rsa_pss_pss_sha384",
                                                                    "rsa_pkcs1_sha512",
                                                                    "rsa_pkcs1_sha256",
                                                                    "rsa_pkcs1_sha384" };

    private static final String[] FIPS_NAMED_GROUPS = {"secp384r1", "secp256r1", "secp521r1"};

    private static final String FIPS_CERTIFICATE_SIGNATURE_ALGORITHM = "RSA_SHA384RSA";

    private static final String FIPS_PREFERRED_THUMBPRINT_ALGORITHM = "SHA_256";

    private static final String FIPS_Options = "keySize=2048";

    /**
     * UEM parameters.
     */
    public static final String UEM_MOUNT_POINT = "/var/omnissa/UEM/";

    private ConcurrentMap<Long, UEMBlastAdapter> uemAdapterCollection =
            new ConcurrentHashMap<>();

    private UEMMountManager mountManager = null;

    /**
     * Remote Settings from broker
     */
    private String remoteConfigFile = null;

    private AgentRemoteConfig remoteConfig = new AgentRemoteConfig();

    /**
     * Build Version/Build Num.
     */
    private static final String PRODUCT_TXT = "/usr/lib/omnissa/viewagent/Product.txt";

    private String agentBuildVersion = "";

    private String agentBuildNum = "";

    private KeyPair encryptionKeyPair = null;

    private boolean useSSOKey = true;

    public void configure(String configFile) throws Exception {
        loadKeyboardMapping();

        /*
         * process config
         */
        Properties parameters = new Properties();
        try {
            parameters.load(new FileInputStream(configFile));
        } catch (Exception e) {
            log.error("Failed to load configuration file: " + e.getMessage());
            if (log.isDebugEnabled()) {
                log.debug(
                        "Failed to load configuration file: " + e.getMessage(),
                        e);
            }
            throw new Exception("Failed to load configuration file: "
                    + e.getMessage(), e);
        }

        if (parameters.containsKey(PARAM_NAME)) {
            name = parameters.getProperty(PARAM_NAME);
        } else {
            throw new Exception("config is missing parameter: " + PARAM_NAME);
        }

        if (parameters.containsKey(PARAM_HOSTNAME)) {
           hostName = parameters.getProperty(PARAM_HOSTNAME);
        } else {
           throw new Exception("config is missing parameter: " + PARAM_HOSTNAME);
        }

        if (parameters.containsKey(PARAM_FIPS_ENABLE)) {
           fipsEnable = Boolean.parseBoolean(parameters.getProperty(PARAM_FIPS_ENABLE));
        } else {
           throw new Exception("config is missing parameter: " + PARAM_FIPS_ENABLE);
        }

        if (parameters.containsKey(PARAM_RUNONCE)) {
           runOnce = Boolean.parseBoolean(parameters.getProperty(PARAM_RUNONCE));
        } else {
           throw new Exception("config is missing parameter: " + PARAM_RUNONCE);
        }

        if (parameters.containsKey(PARAM_IPC_ENABLE)) {
           ipcEnabled = Boolean.parseBoolean(parameters.getProperty(PARAM_IPC_ENABLE));
        } else {
           throw new Exception("config is missing parameter: " + PARAM_IPC_ENABLE);
        }

        if (parameters.containsKey(PARAM_BLAST_KEYSTORE_PATH)) {
            blastKeystorePath = parameters.getProperty(PARAM_BLAST_KEYSTORE_PATH);
        } else {
            throw new Exception("config is missing parameter: "
                    + PARAM_BLAST_KEYSTORE_PATH);
        }

        if (parameters.containsKey(PARAM_CERTIFICATE_PATH)) {
            certificatePath = parameters.getProperty(PARAM_CERTIFICATE_PATH);
        } else {
            throw new Exception("config is missing parameter: "
                    + PARAM_CERTIFICATE_PATH);
        }

        if (parameters.containsKey(PARAM_MANAGED)) {
           managedVM = Boolean.parseBoolean(parameters.getProperty(PARAM_MANAGED));
        } else {
           throw new Exception("config is missing parameter: " + PARAM_MANAGED);
        }

        if (parameters.containsKey(PARAM_CDR_INSTALL)) {
           cdrInstalled = Boolean.parseBoolean(parameters.getProperty(PARAM_CDR_INSTALL));
        } else {
           throw new Exception("config is missing parameter: " + PARAM_CDR_INSTALL);
        }

        if (parameters.containsKey(PARAM_CLIPBOARD_INSTALL)) {
           clipboardInstalled = Boolean.parseBoolean(parameters.getProperty(PARAM_CLIPBOARD_INSTALL));
        } else {
           throw new Exception("config is missing parameter: " + PARAM_CLIPBOARD_INSTALL);
        }

        if (parameters.containsKey(PARAM_APP_INSTALL)) {
           appInstalled = Boolean.parseBoolean(parameters.getProperty(PARAM_APP_INSTALL));
        } else {
           throw new Exception("config is missing parameter: " + PARAM_APP_INSTALL);
        }

        if (parameters.containsKey(PARAM_PROGRAM_BLASTSERVER)) {
            programBlastServer = parameters
                    .getProperty(PARAM_PROGRAM_BLASTSERVER);
            log.info("Blast server program set to: " + programBlastServer);
        }

        if (parameters.containsKey(PARAM_PRINTREDIR_INSTALL)) {
           printerRedirInstalled = Boolean.parseBoolean(parameters.getProperty(PARAM_PRINTREDIR_INSTALL));
        } else {
           throw new Exception("config is missing parameter: " +
                               PARAM_PRINTREDIR_INSTALL);
        }

        if (parameters.containsKey(PARAM_VADC_INSTALL)) {
            vadcInstalled = Boolean.parseBoolean(parameters.getProperty(PARAM_VADC_INSTALL));
        } else {
            throw new Exception("config is missing parameter: " + PARAM_VADC_INSTALL);
        }

        if (parameters.containsKey(PARAM_PROGRAM_DESKTOPDAEMON)) {
            programDesktopDaemon = parameters
                    .getProperty(PARAM_PROGRAM_DESKTOPDAEMON);
            log.info("Desktop daemon program set to: " + programDesktopDaemon);
        } else {
           throw new Exception("config is missing parameter: " + PARAM_PROGRAM_DESKTOPDAEMON);
        }

        if (parameters.containsKey(PARAM_SCRIPT_STARTPRINTREDIR)) {
            scriptStartPrintRedir = parameters
                    .getProperty(PARAM_SCRIPT_STARTPRINTREDIR);
            log.info("Start Printer redirection script set to: " +
                     scriptStartPrintRedir);
        }

        if (parameters.containsKey(PARAM_SCRIPT_STARTRDESERVER)) {
            scriptStartRDEServer = parameters
                    .getProperty(PARAM_SCRIPT_STARTRDESERVER);
            log.info("Start rdeSvc script set to: " + scriptStartRDEServer);
        }

        if (parameters.containsKey(PARAM_SCRIPT_RUNAPPSCANNER)) {
            scriptRunAppScanner = parameters
                    .getProperty(PARAM_SCRIPT_RUNAPPSCANNER);
            log.info("Run appScanner script set to: " + scriptRunAppScanner);
        }

        if (parameters.containsKey(PARAM_APP_ICON_DIR)) {
            appIconDir = parameters
                    .getProperty(PARAM_APP_ICON_DIR);
            log.info("App icon dir set to: " + appIconDir);
        }

        if (parameters.containsKey(PARAM_APP_XML_FILE)) {
            appXmlFile = parameters
                    .getProperty(PARAM_APP_XML_FILE);
            log.info("App xml file set to: " + appXmlFile);
        }

        if (parameters.containsKey(PARAM_SCRIPT_LAUNCHAPP)) {
            scriptLaunchApp = parameters
                    .getProperty(PARAM_SCRIPT_LAUNCHAPP);
            log.info("Launch App script set to: " + scriptLaunchApp);
        }

        if (parameters.containsKey(PARAM_SCRIPT_CONTROL_EXTENSION)) {
            scriptControlExtension = parameters
                    .getProperty(PARAM_SCRIPT_CONTROL_EXTENSION);
            log.info("Control extension script set to: " + scriptControlExtension);
        }

        if (parameters.containsKey(PARAM_SCRIPT_CLOSEAPP)) {
            scriptStopApp = parameters
                    .getProperty(PARAM_SCRIPT_CLOSEAPP);
            log.info("Stop App script set to: " + scriptStopApp);
        }

        if (parameters.containsKey(PARAM_SCRIPT_PERSISTENTDISK)) {
           scriptPersistentDisk = parameters
                   .getProperty(PARAM_SCRIPT_PERSISTENTDISK);
           log.info("Persistent Disk script set to: " + scriptPersistentDisk);
        }

        if (parameters.containsKey(PARAM_SCRIPT_POSTLOGIN)) {
           scriptPostLogin = parameters
                   .getProperty(PARAM_SCRIPT_POSTLOGIN);
           log.info("Post Login script set to: " + scriptPostLogin);
        }

        if (parameters.containsKey(PARAM_SCRIPT_POSTLOGIN_USER)) {
            scriptPostLoginUser = parameters
                    .getProperty(PARAM_SCRIPT_POSTLOGIN_USER);
            log.info("Post Login User script set to: " + scriptPostLoginUser);
        }

        if (parameters.containsKey(PARAM_SCRIPT_POST_CONNECTION)) {
           scriptPostConnection = parameters
                   .getProperty(PARAM_SCRIPT_POST_CONNECTION);
           log.info("Post Connection script set to: " + scriptPostConnection);
        }

        if (parameters.containsKey(PARAM_SCRIPT_CONSOLE_MODE)) {
           scriptConsoleMode = parameters
                   .getProperty(PARAM_SCRIPT_CONSOLE_MODE);
           log.info("Console Mode script set to: " + scriptConsoleMode);
        }

        if (parameters.containsKey(PARAM_SCRIPT_RUN_AUTORANDR)) {
            scriptRunAutorandr = parameters
                    .getProperty(PARAM_SCRIPT_RUN_AUTORANDR);
            log.info("RunAutorandr script set to: " + scriptRunAutorandr);
        }

        if (parameters.containsKey(PARAM_SSO_INSTALL)) {

            ssoInstalled = Boolean.parseBoolean(parameters.getProperty(PARAM_SSO_INSTALL));
        } else {
           throw new Exception("config is missing parameter: " + PARAM_SSO_INSTALL);
        }

        if (parameters.containsKey(PARAM_TRUESSO_INSTALL)) {
           trueSsoInstalled = Boolean.parseBoolean(parameters.getProperty(PARAM_TRUESSO_INSTALL));
        } else {
           throw new Exception("config is missing parameter: " + PARAM_TRUESSO_INSTALL);
        }

        if (parameters.containsKey(PARAM_CUSTOMIZATION_STATE)) {
           sviState = Boolean.parseBoolean(parameters.getProperty(PARAM_CUSTOMIZATION_STATE));
        } else {
           throw new Exception("config is missing parameter: " + PARAM_CUSTOMIZATION_STATE);
        }

        if (parameters.containsKey(PARAM_CONSOLE_MODE)) {
            enableConsoleMode = Boolean.parseBoolean(parameters.getProperty(PARAM_CONSOLE_MODE));
        } else {
            throw new Exception("config is missing parameter: " + PARAM_CONSOLE_MODE);
        }

        if (parameters.containsKey(PARAM_SINGLETON_MODE)) {
            singletonMode = Boolean.parseBoolean(parameters.getProperty(PARAM_SINGLETON_MODE));
        } else {
            throw new Exception("config is missing parameter: " + PARAM_SINGLETON_MODE);
        }

        if (parameters.containsKey(PARAM_USE_IPV6)) {
            useIPv6 = Boolean.parseBoolean(parameters.getProperty(PARAM_USE_IPV6));
        } else {
            throw new Exception("config is missing parameter: " + PARAM_USE_IPV6);
        }

        vgpuEnabled = Boolean.parseBoolean(parameters.getProperty(PARAM_VGPU_ENABLE, "false"));
        if (vgpuEnabled) {
            /*
             * If vgpu is used, we set the default pre-launch session number to 1.
             * This is to avoid wasting too much gup memory due to pre-launch.
             */
            maxSessionsBuffer = 1;
        }

        omnMemEnabled = Boolean.parseBoolean(parameters.getProperty(PARAM_XSERVER_OMNMEM_ENABLED, "true"));

        hznGreeterEnabled = Boolean.parseBoolean(parameters.getProperty(PARAM_HZNGREETER_ENABLE, "true"));

        if (parameters.containsKey(PARAM_USBINST)) {
           usbInst = Boolean.parseBoolean(parameters.getProperty(PARAM_USBINST));
        } else {
           throw new Exception("config is missing parameter: " + PARAM_USBINST);
        }

        if (parameters.containsKey(PARAM_COLLABINST)) {
           collaborationInst = Boolean.parseBoolean(parameters.getProperty(PARAM_COLLABINST));
        } else {
           throw new Exception("config is missing parameter: " + PARAM_COLLABINST);
        }

        if (parameters.containsKey(PARAM_HTML5MMR_INSTALL)) {
            html5mmrInstalled = Boolean.parseBoolean(parameters.getProperty(PARAM_HTML5MMR_INSTALL));
        } else {
            throw new Exception("config is missing parameter: " + PARAM_HTML5MMR_INSTALL);
        }

        if (parameters.containsKey(PARAM_DOMAIN_NAME)) {
            domainName = parameters.getProperty(PARAM_DOMAIN_NAME);
        } else {
            throw new Exception("config is missing parameter: "
                    + PARAM_DOMAIN_NAME);
        }

        if (parameters.containsKey(PARAM_JMS_KEYSTORE_DIR)) {
            jmsKeystoreDirectory = parameters
                    .getProperty(PARAM_JMS_KEYSTORE_DIR);
        } else {
            throw new Exception("config is missing parameter: "
                    + PARAM_JMS_KEYSTORE_DIR);
        }

        if (parameters.containsKey(PARAM_JMS_KEYSTORE_PASSWORD)) {
            String s = parameters.getProperty(PARAM_JMS_KEYSTORE_PASSWORD);
            jmsKeystorePassword = new KeyStore.PasswordProtection(
                    s.toCharArray());
        } else {
            throw new Exception("config is missing parameter: "
                    + PARAM_JMS_KEYSTORE_PASSWORD);
        }

        if (parameters.containsKey(PARAM_MACHINE_CONFIG_FILE)) {
            synchronized(this) {
                machineConfigFile = parameters
                    .getProperty(PARAM_MACHINE_CONFIG_FILE);
            }
        } else {
            throw new Exception("config is missing parameter: "
                    + PARAM_MACHINE_CONFIG_FILE);
        }

        if (parameters.containsKey(PARAM_REMOTE_CONFIG_FILE)) {
            remoteConfigFile = parameters
                    .getProperty(PARAM_REMOTE_CONFIG_FILE);
        } else {
            throw new Exception("config is missing parameter: "
                    + PARAM_REMOTE_CONFIG_FILE);
        }

        if (parameters.containsKey(PARAM_OS_VERSION_LDAP)) {
            osVersionLdap = parameters.getProperty(PARAM_OS_VERSION_LDAP);
        } else {
            throw new Exception("config is missing parameter: "
                    + PARAM_OS_VERSION_LDAP);
        }

        if (parameters.containsKey(PARAM_OS_VERSION_MAJOR)) {
            osVersionMajor = parameters.getProperty(PARAM_OS_VERSION_MAJOR);
        } else {
            throw new Exception("config is missing parameter: "
                    + PARAM_OS_VERSION_MAJOR);
        }

        if (parameters.containsKey(PARAM_OS_VERSION_MINOR)) {
            osVersionMinor = parameters.getProperty(PARAM_OS_VERSION_MINOR);
        } else {
            throw new Exception("config is missing parameter: "
                    + PARAM_OS_VERSION_MINOR);
        }

        if (parameters.containsKey(PARAM_OS_SYSTEM_UUID)) {
            osSystemUUID = parameters.getProperty(PARAM_OS_SYSTEM_UUID);
        } else {
            log.debug("config is missing parameter: " + PARAM_OS_SYSTEM_UUID);
            osSystemUUID = UUID.randomUUID().toString();
        }

        if (parameters.containsKey(PARAM_PLATFORM)) {
            platform = parameters.getProperty(PARAM_PLATFORM);
        } else {
            throw new Exception("config is missing parameter: "
                    + PARAM_PLATFORM);
        }

        if (parameters.containsKey(PARAM_PROTOCOL)) {
            protocol = Protocol.fromString(parameters
                    .getProperty(PARAM_PROTOCOL));
        } else {
            throw new Exception("config is missing parameter: "
                    + PARAM_PROTOCOL);
        }

        if (parameters.containsKey(PARAM_PROTOCOL_PORT)) {
            protocolPort = Integer.parseInt(parameters
                    .getProperty(PARAM_PROTOCOL_PORT));
        } else {
            throw new Exception("config is missing parameter: "
                    + PARAM_PROTOCOL_PORT);
        }

        if (parameters.containsKey(PARAM_MAX_SESSION_THREADS)) {
            try {
                maxSessionThreads = Integer.parseInt(parameters
                    .getProperty(PARAM_MAX_SESSION_THREADS));
            } catch (Exception e) {
                throw new Exception("config for parameter: "
                    + PARAM_MAX_SESSION_THREADS + " is invalid");
            }
        }

        if (parameters.containsKey(PARAM_SCRIPT_STARTXSERVER)) {
            scriptStartXServer = parameters
                    .getProperty(PARAM_SCRIPT_STARTXSERVER);
            log.info("Start XServer script set to: " + scriptStartXServer);
        } else {
            throw new Exception("config is missing parameter: "
                    + PARAM_SCRIPT_STARTXSERVER);
        }

        if (parameters.containsKey(PARAM_SCRIPT_STARTBLASTWORKER)) {
           scriptStartBlastWorker = parameters
                   .getProperty(PARAM_SCRIPT_STARTBLASTWORKER);
           log.info("Start Session script set to: " + scriptStartBlastWorker);
        } else {
           throw new Exception("config is missing parameter: "
                   + PARAM_SCRIPT_STARTBLASTWORKER);
        }

        if (parameters.containsKey(PARAM_SCRIPT_SSO_CONFIG_DESKTOP_TYPE)) {
           scriptSsoConfigDesktopType = parameters.getProperty(PARAM_SCRIPT_SSO_CONFIG_DESKTOP_TYPE);
           log.info("SSO ConfigDesktopType script set to: " + scriptSsoConfigDesktopType);
        }  else {
           throw new Exception("config is missing parameter: "
                 + PARAM_SCRIPT_SSO_CONFIG_DESKTOP_TYPE);
        }

        if (parameters.containsKey(PARAM_SCRIPT_CLEANLOG)) {
           scriptCleanLog = parameters.getProperty(PARAM_SCRIPT_CLEANLOG);
           log.info("Clean log script set to: " + scriptCleanLog);
        }  else {
           throw new Exception("config is missing parameter: "
                 + PARAM_SCRIPT_CLEANLOG);
        }

        if (parameters.containsKey(PARAM_SCRIPT_MOUNTFOLDER)) {
            scriptMountFolder = parameters.getProperty(PARAM_SCRIPT_MOUNTFOLDER);
            log.info("Mount folder script set to: " + scriptMountFolder);
         }  else {
            throw new Exception("config is missing parameter: "
                  + PARAM_SCRIPT_MOUNTFOLDER);
        }

        if (parameters.containsKey(PARAM_SCRIPT_UMOUNTFOLDER)) {
            scriptUMountFolder = parameters.getProperty(PARAM_SCRIPT_UMOUNTFOLDER);
            log.info("UMount folder script set to: " + scriptUMountFolder);
         }  else {
            throw new Exception("config is missing parameter: "
                  + PARAM_SCRIPT_UMOUNTFOLDER);
        }

        if (parameters.containsKey(PARAM_CUSTOM_CONFIG_FILE)) {
           customerConfigFile = parameters.getProperty(PARAM_CUSTOM_CONFIG_FILE);
           log.info("Customer config file set to: " + customerConfigFile);
        } else {
           throw new Exception("config is missing parameter: "
                 + PARAM_CUSTOM_CONFIG_FILE);
        }

        if (parameters.containsKey(PARAM_CUSTOM_CONFIG_FILE_BLAST)) {
            customerConfigFileBlast = parameters
                    .getProperty(PARAM_CUSTOM_CONFIG_FILE_BLAST);
            log.info("Blast customer config file set to: " + customerConfigFileBlast);
        } else {
            throw new Exception("config is missing parameter: "
                    + PARAM_CUSTOM_CONFIG_FILE_BLAST);
        }

        /*
         * optional parameters
         */
        if (parameters.containsKey(PARAM_SCRIPT_STARTCLIPBOARD)) {
            scriptStartClipboard = parameters
                    .getProperty(PARAM_SCRIPT_STARTCLIPBOARD);
            log.info("Start Clipboard script set to: " + scriptStartClipboard);
        }

        if (parameters.containsKey(PARAM_SCRIPT_STARTCDR)) {
            scriptStartCDR = parameters
                    .getProperty(PARAM_SCRIPT_STARTCDR);
            log.info("Start CDR script set to: " + scriptStartCDR);
        }

        if (parameters.containsKey(PARAM_SCRIPT_STARTHTML5MMR)) {
            scriptStartHtml5mmr = parameters
                    .getProperty(PARAM_SCRIPT_STARTHTML5MMR);
            log.info("Start HTML5MMR script set to: " + scriptStartHtml5mmr);
        }

        if (parameters.containsKey(PARAM_COLLABORATION_UI_PATH)) {
            collabUIPath = parameters
                    .getProperty(PARAM_COLLABORATION_UI_PATH);
            log.info("Start collaboration UI path: " + collabUIPath);
        }

        if (parameters.containsKey(PARAM_SCRIPT_STARTUSBREDIR)) {
            scriptStartUsbRedir = parameters
                    .getProperty(PARAM_SCRIPT_STARTUSBREDIR);
            log.info("Start UsbRedir script set to: " + scriptStartUsbRedir);
        }

        if (parameters.containsKey(PARAM_SCRIPT_STOPUSBREDIR)) {
            scriptStopUsbRedir = parameters
                    .getProperty(PARAM_SCRIPT_STOPUSBREDIR);
            log.info("Stop UsbRedir script set to: " + scriptStopUsbRedir);
        }

        if (parameters.containsKey(PARAM_INITIAL_NOTIFICATION_DELAY)) {
            try {
                initialNotificationDelay = Integer.parseInt(parameters
                        .getProperty(PARAM_INITIAL_NOTIFICATION_DELAY));
            } catch (Exception e) {
                throw new Exception("config for parameter: "
                        + PARAM_INITIAL_NOTIFICATION_DELAY + " is invalid");
            }
        }

        // check current host name and restore to config file
        String currentHostName = "";
        try {

           Runtime run = Runtime.getRuntime();
           Process proc = run.exec("hostname");
           BufferedInputStream host = new BufferedInputStream(proc.getInputStream());
           byte[] contents = new byte[1024];
           int bytesRead = 0;
           while ((bytesRead = host.read(contents)) != -1) {
              currentHostName += new String(contents, 0, bytesRead - 1,
                                            StandardCharsets.UTF_8);
           }
           log.info("current host name is " + currentHostName);
        } catch (UnknownHostException e) {
           log.error("Failed to resolve current host name." + e.getMessage());
           return;
        } catch (IOException e) {
           log.error("Failed to get the hostname. " + e.getMessage());
           return;
        }
        try {
            if (!name.isEmpty() && (currentHostName != null) &&
                !currentHostName.isEmpty() &&
                !name.equalsIgnoreCase(currentHostName)) {
               if (log.isDebugEnabled()) {
                  log.debug("update current host name to " + currentHostName);
               }
               name = currentHostName;
               parameters.setProperty(PARAM_NAME, name);
               if (loadConfLock != null) {
                   loadConfLock.lock();
                   if (log.isDebugEnabled()) {
                       log.debug("Getting the load configure lock.");
                   }
               }
               if (!agentShutDown) {
                  FileOutputStream fos = new FileOutputStream(configFile);
                  parameters.store(fos, "update host name");
                  fos.close();
                  if (log.isDebugEnabled()) {
                     log.debug("Successfully updated the host name.");
                  }
               }
            }
        } catch (Exception e) {
            log.error("Failed to update the host name." + e.getMessage(), e);
        } finally {
            if (loadConfLock != null &&
                ((ReentrantLock)loadConfLock).isLocked()) {
                if (log.isDebugEnabled()) {
                    log.debug("unlocking the load configure lock.");
                }
                loadConfLock.unlock();
            }
        }

        // get the fqdn from command line "hostname -f"
        try {
           Runtime run = Runtime.getRuntime();
           Process proc = run.exec("hostname -f");
           BufferedInputStream host = new BufferedInputStream(proc.getInputStream());
           byte[] contents = new byte[1024];
           int bytesRead = 0;
           while ((bytesRead = host.read(contents)) != -1) {
              fqdn += new String(contents, 0, bytesRead,
                                 StandardCharsets.UTF_8);
           }
           log.debug("Current fqdn is " + fqdn);
        } catch (IOException e) {
           log.error("Failed to get the fqdn. " + e.getMessage());
        }
    }

    protected void loadKeyboardMapping() {
        log.info("Load Keyboard Mapping file..");
        InputStream layoutIn = StandaloneAgentConfig.class.getResourceAsStream("/KeyboardLayoutMapping.properties");
        InputStream languageIn = StandaloneAgentConfig.class.getResourceAsStream("/KeyboardLanguageMapping.properties");

        if (layoutIn != null) {
           try {
               Properties prop = new Properties();
               prop.load(layoutIn);
               keyboardLayoutMap = new HashMap<Long, String>();
               for (String key : prop.stringPropertyNames()) {
                  keyboardLayoutMap.put(Long.valueOf(key, 16),
                                        prop.getProperty(key));
               }

               layoutIn.close();
           } catch (Exception e) {
               log.error("Failed to load Keyboard Layout Mapping file. " +
                         Utils.printExceptionStack(e));
           }
       } else {
           log.error("Fail to load KeyboardLayoutMapping");
       }

       if (keyboardLayoutMap != null && keyboardLayoutMap.isEmpty()) {
           log.error("Keyboard Layout Mapping file is empty.");
       }

       if (languageIn != null) {
           try {
               Properties prop = new Properties();
               prop.load(languageIn);
               keyboardLanguageMap = new HashMap<String, String>((Map)prop);
               languageIn.close();
           } catch (IOException e) {
               log.error("Failed to load Keyboard Language Mapping file. " +
                         Utils.printExceptionStack(e));
           }
       } else {
           log.error("Fail to load KeyboardLanguageMapping.");
       }

       if (keyboardLanguageMap != null && keyboardLanguageMap.isEmpty()) {
           log.error("Keyboard Language Mapping file is empty.");
       }
    }

    public boolean update_machine_keypair( String privateKey, String publicKey) {
        PrivateKey pvk = stringToPrivateKey(privateKey);
        PublicKey pbk = stringToPublicKey(publicKey);

        if (pvk != null) {
            agentPairedPrivateKeyString = privateKey;
            agentPairedPublicKeyString = publicKey;
            agentPairedPrivateKey = pvk;
            agentPairedPublicKey = pbk;
            return true;
        } else {
            log.info("Skip update, no paired keys present");
        }
        return false;
    }

    public synchronized boolean load_machine_conf() {
        String gson = null;
        try {
            Path machineConfigPath = Paths.get(machineConfigFile);
            // manchine config file don't exist at first time startup in managed vm
            if (managedVM && !machineConfigPath.toFile().exists()) {
              log.info("file " + machineConfigPath.toString() + " does not exist");
              return true;
            }
            if (log.isDebugEnabled()) {
                log.debug("Reading machine configuration from "
                        + machineConfigFile);
            }

            gson = Utils.readTextFile(machineConfigPath,
                    MAX_MACHINE_CONFIG_FILE_LENGTH);

            if (gson.isEmpty()) {
               log.debug("machine config file is empty");
               return false;
            }
        } catch (Exception e) {
            log.error("Failed to read from machine configuration "
                    + e.getMessage());
            if (log.isDebugEnabled()) {
                log.debug(
                        "Failed to read from machine configuration "
                                + e.getMessage(), e);
            }
            return false;
        }

        log.debug("Found machine configuration");
        try {
            if (log.isDebugEnabled()) {
                // remove private key from log
                String gsonLog = gson;
                int begin = gsonLog.indexOf("agentPrivateKey");
                int end = gsonLog.indexOf("agentPublicKey");
                if (begin != -1 && end != -1) {
                    String sub = gson.substring(begin-1, end-1);
                    gsonLog = gsonLog.replace(sub, " ");
                }
                log.debug("machine configuration: " + gsonLog);
            }
            agentVM = Machine.readFromGson(gson);
            if (agentVM == null) {
                log.debug("Failed to initialize machine object from machine config file.");
                return false;
            }
        } catch (Throwable t) {
            log.error("Failed to parse machine configuration "
                      + t.getMessage());
            if (log.isDebugEnabled()) {
                log.debug("Failed to read from machine configuration "
                          + t.getMessage(), t);
            }
            return false;
        }

        PrivateKey pvk = stringToPrivateKey(agentVM
                    .getAgentPrivateKey());
        PublicKey pbk = stringToPublicKey(agentVM.getAgentPublicKey());

        if (pvk != null) {
            agentPairedPrivateKey = pvk;
                agentPairedPublicKey = pbk;
        } else {
            log.info("No paired keys present");
        }

        log.debug("Machine configuration loaded");
        return true;
    }

    protected boolean load_customer_conf() {
        try {
            if (Paths.get(customerConfigFile).toFile().isFile()) {
                Properties cparameters = new Properties();
                if (log.isDebugEnabled()) {
                    log.debug("Reading customer configuration from "
                            + customerConfigFile);
                }
                cparameters.load(new FileInputStream(customerConfigFile));

                if (cparameters.containsKey(CPARAM_MAX_SESSIONS_BUFFER)) {
                   String maxSessionsBufferSetting = cparameters.getProperty(CPARAM_MAX_SESSIONS_BUFFER);
                   try {
                       int maxSessionsBufferNumber = Integer.parseInt(maxSessionsBufferSetting);
                       if (maxSessionsBufferNumber > 0 && maxSessionsBufferNumber <= getMaxSessions()) {
                          maxSessionsBuffer = maxSessionsBufferNumber;
                          if (log.isDebugEnabled()) {
                             log.debug("Set max sessions buffer as " + maxSessionsBuffer);
                          }
                       } else {
                          log.debug("Set max sessions buffer as a non-positive value: "
                                    + maxSessionsBufferNumber + ", use the default value: "
                                    + maxSessionsBuffer);
                       }
                   } catch (NumberFormatException e) {
                       if (log.isDebugEnabled()) {
                          log.debug("Set max sessions buffer as invalid format: "
                                    + maxSessionsBufferSetting + ", using the default value: "
                                    + maxSessionsBuffer);
                       }
                   }
                }

                if (cparameters.containsKey(CPARAM_START_STORM_THROTTLE)) {
                   String startStormThrottleSetting = cparameters.getProperty(CPARAM_START_STORM_THROTTLE);
                   try {
                       int startStormThrottleNumber = Integer.parseInt(startStormThrottleSetting);
                       if (startStormThrottleNumber >= 0 && startStormThrottleNumber <= getMaxSessions()) {
                          startStormThrottle = startStormThrottleNumber;
                          if (log.isDebugEnabled()) {
                             log.debug("Set start storm throttle as " + startStormThrottle);
                          }
                       } else {
                          log.debug("Set start storm throttle out of range: "
                                    + startStormThrottleNumber + ", use the default value: "
                                    + startStormThrottle);
                       }
                   } catch (NumberFormatException e) {
                       if (log.isDebugEnabled()) {
                          log.debug("Set start storm throttle as invalid format: "
                                    + startStormThrottleSetting + ", using the default value: "
                                    + startStormThrottle);
                       }
                   }
                }

                if (cparameters.containsKey(CPARAM_USB_ENABLE)) {
                    String usbFlag = cparameters.getProperty(CPARAM_USB_ENABLE);
                    if (usbFlag.equalsIgnoreCase("false")) {
                        enableUSB = false;
                    }
                }

                if (cparameters.containsKey(CPARAM_SSO_ENABLE)) {
                    String ssoFlag = cparameters.getProperty(CPARAM_SSO_ENABLE);
                    if (ssoFlag.equalsIgnoreCase("false")) {
                        enableSSO = false;
                    }
                }

                if (cparameters.containsKey(CPARAM_CDR_ENABLE)) {
                    String cdrFlag = cparameters.getProperty(CPARAM_CDR_ENABLE);
                    if (cdrFlag.equalsIgnoreCase("false")) {
                        cdrEnabled = false;
                    }
                }

                if (cparameters.containsKey(CPARAM_BLOCK_SCREEN_CPATURE_ENABLE)) {
                    String blockScreenCaptureFlag = cparameters.getProperty(
                                                       CPARAM_BLOCK_SCREEN_CPATURE_ENABLE);
                    if (blockScreenCaptureFlag.equalsIgnoreCase("true")) {
                        blockScreenCaptureEnabled = true;
                    }
                }

                if (cparameters.containsKey(CPARAM_DELETE_FAILED_CLONED_VM)) {
                    String delFlag = cparameters.getProperty(CPARAM_DELETE_FAILED_CLONED_VM);
                    if (delFlag.equalsIgnoreCase("false")) {
                        delFailedClonedVM = false;
                    }
                }

                // UEM is renamed DEM in custom config file
                if (cparameters.containsKey(CPARAM_DEM_ENABLE)) {
                    uemEnable = Boolean.parseBoolean(cparameters
                            .getProperty(CPARAM_DEM_ENABLE));
                } else if (cparameters.containsKey(CPARAM_UEM_ENABLE)) {
                    uemEnable = Boolean.parseBoolean(cparameters
                            .getProperty(CPARAM_UEM_ENABLE));
                }


                if (cparameters.containsKey(CPARAM_DEM_NETWORK_PATH) ||
                        cparameters.containsKey(CPARAM_UEM_NETWORK_PATH)) {
                    if (cparameters.containsKey(CPARAM_DEM_NETWORK_PATH)) {
                        uemNetworkPath = cparameters
                                .getProperty(CPARAM_DEM_NETWORK_PATH);
                    } else {
                        uemNetworkPath = cparameters
                                .getProperty(CPARAM_UEM_NETWORK_PATH);
                    }
                    if (log.isDebugEnabled()) {
                        log.debug("DEM network path: "
                                + ((uemNetworkPath == null || uemNetworkPath
                                .isEmpty()) ? "empty" : uemNetworkPath));
                    }
                }

                if (cparameters.containsKey(CPARAM_DEM_MOUNT_TIMEOUT) ||
                        cparameters.containsKey(CPARAM_UEM_MOUNT_TIMEOUT) ) {
                    String mountTimeout;
                    if (cparameters.containsKey(CPARAM_DEM_MOUNT_TIMEOUT)) {
                        mountTimeout = cparameters.getProperty(CPARAM_DEM_MOUNT_TIMEOUT);
                    } else {
                        mountTimeout = cparameters.getProperty(CPARAM_UEM_MOUNT_TIMEOUT);
                    }
                    if (mountTimeout == null || mountTimeout.isEmpty()) {
                        if (log.isDebugEnabled()) {
                            log.debug("Set DEM mount timeout as empty, using the default value: "
                                    + uemMountTimeout);
                        }
                    } else {
                        try {
                            long timeoutSecs = Long.parseLong(mountTimeout);
                            if (timeoutSecs > 0) {
                                uemMountTimeout = timeoutSecs * 1000;
                                if (log.isDebugEnabled()) {
                                    log.debug("Set DEM mount timeout as "
                                            + uemMountTimeout);
                                }
                            } else {
                                if (log.isDebugEnabled()) {
                                    log.debug("Set DEM mount timeout as a non-positive value: "
                                            + mountTimeout
                                            + ", using the default value: "
                                            + uemMountTimeout);
                                }
                            }
                        } catch (NumberFormatException e) {
                            if (log.isDebugEnabled()) {
                                log.debug("Set DEM mount timeout as invalid format: "
                                        + mountTimeout
                                        + ", using the default value: "
                                        + uemMountTimeout);
                            }
                        }
                    }
                }

                if (cparameters.containsKey(CPARAM_LB_CUSTOM_SCRIPT)) {
                    lbCustomScript = cparameters
                            .getProperty(CPARAM_LB_CUSTOM_SCRIPT);
                    if (lbCustomScript != null && !lbCustomScript.isEmpty()) {
                        log.debug("Set load balance custom script to "
                                   + lbCustomScript);

                        if (cparameters.containsKey(CPARAM_LB_CUSTOM_SCRIPT_RESULT)) {
                            lbCustomScriptResult = cparameters
                                   .getProperty(CPARAM_LB_CUSTOM_SCRIPT_RESULT);
                            if (lbCustomScriptResult == null ||
                                lbCustomScriptResult.isEmpty()) {
                                log.error("Missing load balance custom script result");
                                lbCustomScript = null;
                            } else {
                                log.debug("Set load balance custom script result to "
                                          + lbCustomScriptResult);
                            }
                        }
                    }
                }

                if (cparameters.containsKey(CPARAM_LB_vGPU_MEMORY_USAGE_ENABLE)) {
                    String value = cparameters
                            .getProperty(CPARAM_LB_vGPU_MEMORY_USAGE_ENABLE);
                    if (value.equalsIgnoreCase("false")) {
                        lbNvidiaGPUMemoryUsageEnable = false;
                    }
                }

                if (cparameters.containsKey(CPARAM_APP_ENABLE)) {
                    String value = cparameters
                            .getProperty(CPARAM_APP_ENABLE);
                    if (value.equalsIgnoreCase("false")) {
                        disableApp();
                    }
                }

                if (cparameters.containsKey(CPARAM_LB_POOL_INTERVAL_SECONDS)) {
                    try {
                        lbPoolIntervalSeconds = Integer.parseInt(cparameters
                               .getProperty(CPARAM_LB_POOL_INTERVAL_SECONDS));
                        if (lbPoolIntervalSeconds <= 0) {
                            log.error("Invalid LB pool interval " +
                                      Integer.toString(lbPoolIntervalSeconds) +
                                      ", using the default value");
                            lbPoolIntervalSeconds = 30;
                        }
                    } catch (NumberFormatException e) {
                        if (log.isDebugEnabled()) {
                            log.debug("Invalid format for LB pool interval" +
                                      ", using the default value");
                        }
                    }
                }

                if (cparameters.containsKey(CPARAM_LB_CPU_WEIGHTAGE)) {
                    String weightage = cparameters
                            .getProperty(CPARAM_LB_CPU_WEIGHTAGE);
                    if (weightage != null && !weightage.isEmpty()) {
                        try {
                            lbCpuWeightage = Double.parseDouble(weightage);
                            if (lbCpuWeightage > 0) {
                                if (log.isDebugEnabled()) {
                                    log.debug("Set load balance cpu weightage to " +
                                              weightage);
                                }
                            } else {
                                if (log.isDebugEnabled()) {
                                    log.debug("Invalid non-positive cpu weightage value " +
                                              weightage + ", using the default value");
                                }
                                lbCpuWeightage = LB_DEFAULT_WEIGHTAGE;
                            }
                        } catch (NumberFormatException e) {
                            if (log.isDebugEnabled()) {
                                log.debug("Invalid format for cpu weightage " +
                                          weightage + ", using the default value");
                            }
                        }
                    }
                }

                if (cparameters.containsKey(CPARAM_LB_MEMORY_WEIGHTAGE)) {
                    String weightage = cparameters
                            .getProperty(CPARAM_LB_MEMORY_WEIGHTAGE);
                    if (weightage != null && !weightage.isEmpty()) {
                        try {
                            lbMemWeightage = Double.parseDouble(weightage);
                            if (lbMemWeightage > 0) {
                                if (log.isDebugEnabled()) {
                                    log.debug("Set load balance memory weightage to " +
                                              weightage);
                                }
                            } else {
                                if (log.isDebugEnabled()) {
                                    log.debug("Invalid non-positive memory weightage value " +
                                              weightage + ", using the default value");
                                }
                                lbMemWeightage = LB_DEFAULT_WEIGHTAGE;
                            }
                        } catch (NumberFormatException e) {
                            if (log.isDebugEnabled()) {
                                log.debug("Invalid format for memory weightage " +
                                          weightage + ", using the default value");
                            }
                        }
                    }
                }

                if (cparameters.containsKey(CPARAM_LB_DISK_WEIGHTAGE)) {
                    String weightage = cparameters
                            .getProperty(CPARAM_LB_DISK_WEIGHTAGE);
                    if (weightage != null && !weightage.isEmpty()) {
                        try {
                            lbDiskWeightage = Double.parseDouble(weightage);
                            if (lbDiskWeightage > 0) {
                                if (log.isDebugEnabled()) {
                                    log.debug("Set load balance disk weightage to "
                                            + weightage);
                                }
                            } else {
                                if (log.isDebugEnabled()) {
                                    log.debug("Invalid non-positive disk weightage value " +
                                              weightage + ", using the default value");
                                }
                                lbDiskWeightage = LB_DEFAULT_WEIGHTAGE;
                            }
                        } catch (NumberFormatException e) {
                            if (log.isDebugEnabled()) {
                                log.debug("Invalid format for disk weightage " +
                                          weightage + ", using the default value");
                            }
                        }
                    }
                }

                if (cparameters.containsKey(CPARAM_LB_SESSION_COUNT_WEIGHTAGE)) {
                    String weightage = cparameters
                            .getProperty(CPARAM_LB_SESSION_COUNT_WEIGHTAGE);
                    if (weightage != null && !weightage.isEmpty()) {
                        try {
                            lbSessionCountWeightage = Double.parseDouble(weightage);
                            if (lbSessionCountWeightage > 0) {
                                if (log.isDebugEnabled()) {
                                    log.debug("Set load balance session count weightage to "
                                            + weightage);
                                }
                            } else {
                                if (log.isDebugEnabled()) {
                                    log.debug("Invalid non-positive session count weightage value "
                                            + weightage
                                            + ", using the default value");
                                }
                                lbSessionCountWeightage = LB_DEFAULT_WEIGHTAGE;
                            }
                        } catch (NumberFormatException e) {
                            if (log.isDebugEnabled()) {
                                log.debug("Invalid format for session count weightage " +
                                          weightage + ", using the default value");
                            }
                        }
                    }
                }

                // Check total weightage is valid
                double totalWeightage = lbCpuWeightage + lbMemWeightage +
                                 lbDiskWeightage + lbSessionCountWeightage;
                if (totalWeightage > 100) {
                    log.error("The total weightage " +
                        Double.toString(totalWeightage) +
                        " is invalid, using default value for each of them");
                    lbCpuWeightage = lbMemWeightage = lbDiskWeightage =
                    lbSessionCountWeightage = LB_DEFAULT_WEIGHTAGE;
                }

                if (cparameters.containsKey(CPARAM_ENDPOINT_VPN_ENABLE)) {
                    endpointVPNEnable = Boolean.parseBoolean(cparameters
                            .getProperty(CPARAM_ENDPOINT_VPN_ENABLE));
                }

                if (cparameters.containsKey(CPARAM_HELPDESK_ENABLE)) {
                    String helpDeskFlag = cparameters
                            .getProperty(CPARAM_HELPDESK_ENABLE);
                    if (helpDeskFlag.equalsIgnoreCase("false")) {
                        helpDeskEnabled = false;
                    }
                }

                if (cparameters.containsKey(CPARAM_PRINTREDIR_ENABLE)) {
                    String printerFlag = cparameters
                            .getProperty(CPARAM_PRINTREDIR_ENABLE);
                    if (printerFlag.equalsIgnoreCase("false")) {
                        printerRedirEnabled = false;
                    }
                }

                if (cparameters.containsKey(CPARAM_SSO_USER_FORMAT)) {
                    ssoUserFormat = cparameters
                            .getProperty(CPARAM_SSO_USER_FORMAT);
                    if (log.isDebugEnabled()) {
                        log.debug("Set SSO user format: " + ssoUserFormat);
                    }
                }

                if (cparameters.containsKey(CPARAM_SSO_DESKTOP_TYPE)) {
                    ssoDesktopType = cparameters
                            .getProperty(CPARAM_SSO_DESKTOP_TYPE);
                    if (log.isDebugEnabled()) {
                        log.debug("Set SSO desktop type: " + ssoDesktopType);
                    }
                }

                if (cparameters.containsKey(CPARAM_START_BLAST_SERVER_TIMEOUT)) {
                    String startTimeout = cparameters
                            .getProperty(CPARAM_START_BLAST_SERVER_TIMEOUT);
                    if (startTimeout == null || startTimeout.isEmpty()) {
                        if (log.isDebugEnabled()) {
                            log.debug("Set start blast server timeout as empty, use the default value: "
                                    + startBlastServerTimeout);
                        }
                    } else {
                        try {
                            int timeoutSecs = Integer.parseInt(startTimeout);
                            if (timeoutSecs > 0) {
                                startBlastServerTimeout = timeoutSecs * 1000;
                                if (log.isDebugEnabled()) {
                                    log.debug("Set start blast server timeout as "
                                            + startBlastServerTimeout);
                                }
                            } else {
                                if (log.isDebugEnabled()) {
                                    log.debug("Set start blast server timeout as a non-positive value: "
                                            + startTimeout
                                            + ", use the default value: "
                                            + startBlastServerTimeout);
                                }
                            }
                        } catch (NumberFormatException e) {
                            if (log.isDebugEnabled()) {
                                log.debug("Set start blast server timeout as invalid format: "
                                        + startTimeout
                                        + ", use the default value: "
                                        + startBlastServerTimeout);
                            }
                        }
                    }
                }

                if (cparameters.containsKey(CPARAM_PENDING_SESSION_TIMEOUT)) {
                    String customPendingSessionTimeout = cparameters
                            .getProperty(CPARAM_PENDING_SESSION_TIMEOUT);
                    if (customPendingSessionTimeout == null
                            || customPendingSessionTimeout.isEmpty()) {
                        if (log.isDebugEnabled()) {
                            log.debug("Set pending session timeout as empty, use the default value: "
                                    + pendingSessionTimeout);
                        }
                    } else {
                        try {
                            int timeoutMins = Integer
                                    .parseInt(customPendingSessionTimeout);
                            if (timeoutMins > 0) {
                                pendingSessionTimeout = timeoutMins;
                                if (pendingSessionTimeout < 5) {
                                    pendingSessionTimeout = 5;
                                } else if (pendingSessionTimeout > 15) {
                                    pendingSessionTimeout = 15;
                                }
                                if (log.isDebugEnabled()) {
                                    log.debug("Set pending session timeout as "
                                            + pendingSessionTimeout);
                                }
                            } else {
                                if (log.isDebugEnabled()) {
                                    log.debug("Set pending session timeout as a non-positive value: "
                                            + customPendingSessionTimeout
                                            + ", use the default value: "
                                            + pendingSessionTimeout);
                                }
                            }
                        } catch (NumberFormatException e) {
                            if (log.isDebugEnabled()) {
                                log.debug("Set pending session timeout as invalid format: "
                                        + customPendingSessionTimeout
                                        + ", use the default value: "
                                        + pendingSessionTimeout);
                            }
                        }
                    }
                }

                String timeout = cparameters
                        .getProperty(CPARAM_SSO_TIMEOUT, Integer.toString(ssoTimeout));
                try {
                    int timeoutSec = Integer.parseInt(timeout);
                    if (timeoutSec > 0) {
                        if (log.isDebugEnabled()) {
                            log.debug("Set SSO timeout as "
                                    + timeoutSec);
                            ssoTimeout = timeoutSec;
                        }
                    } else {
                        if (log.isDebugEnabled()) {
                            log.debug("Set SSO timeout as a non-positive value: "
                                    + timeoutSec
                                    + ", use the default value: "
                                    + ssoTimeout);
                        }
                    }
                } catch (NumberFormatException e) {
                    if (log.isDebugEnabled()) {
                        log.debug("Set SSO timeout as invalid format: "
                                + timeout
                                + ", use the default value: "
                                + ssoTimeout);
                    }
                }

                if (cparameters.containsKey(CPARAM_SSL_PROTOCOLS)) {
                    String value = cparameters.getProperty(CPARAM_SSL_PROTOCOLS);
                    String[] protocols = parseSslProtocols(value);
                    if (protocols != null) {
                        sslProtocols = StringUtils.join(protocols, ":");
                        if (log.isDebugEnabled()) {
                            log.debug("Set SSL protocols: " + sslProtocols);
                        }
                    } else {
                        log.error("Invalid ssl protocols string: " + value);
                    }
                }

                if (cparameters.containsKey(CPARAM_SSL_CIPHERS)) {
                    sslCiphers = cparameters.getProperty(CPARAM_SSL_CIPHERS);
                    if (log.isDebugEnabled()) {
                        log.debug("Set SSL ciphers: " + sslCiphers);
                    }
                } else {
                    /*
                     * If we are in FIPS mode, only following default ciphers are permitted
                     * as cited in our Common Criteria submission
                     */
                    if (fipsEnable) {
                       sslCiphers = FIPS_BLAST_SSL_CIPHERS;
                       if (log.isDebugEnabled()) {
                           log.debug("Set SSL ciphers: " + sslCiphers);
                       }
                    }
                }

                if (cparameters.containsKey(CPARAM_TLS_CIPHERS_SUITES)) {
                    tlsCipherSuites = cparameters.getProperty(CPARAM_TLS_CIPHERS_SUITES);
                    if (log.isDebugEnabled()) {
                        log.debug("Set TLS cipher suites: " + tlsCipherSuites);
                    }
                }

                if (cparameters.containsKey(CPARAM_SSL_CERT_NAME)) {
                    sslCertName = cparameters.getProperty(CPARAM_SSL_CERT_NAME);
                    if (log.isDebugEnabled()) {
                        log.debug("Set SSL certificate name: " + sslCertName);
                    }
                }

                if (cparameters.containsKey(CPARAM_SSL_KEY_NAME)) {
                    sslKeyName = cparameters.getProperty(CPARAM_SSL_KEY_NAME);
                    if (log.isDebugEnabled()) {
                        log.debug("Set SSL private key name: " + sslKeyName);
                    }
                }

                if (cparameters.containsKey(CPARAM_TSL_CURVES)) {
                    tlsCurves = cparameters.getProperty(CPARAM_TSL_CURVES);
                    if (log.isDebugEnabled()) {
                        log.debug("Set TLS curves: " + tlsCurves);
                    }
                } else {
                    /*
                     * If we are in FIPS mode, only following default curves lists are permitted
                     * as cited in our Common Criteria submission
                     */
                    if (fipsEnable) {
                       tlsCurves = FIPS_BLAST_TLS_CURVER;
                       if (log.isDebugEnabled()) {
                           log.debug("Set TSL curves: " + tlsCurves);
                       }
                    }
                }

                if (cparameters
                        .containsKey(CPARAM_SSL_CIPHER_SERVER_PREFERENCE)) {
                    String sslFlag = cparameters
                            .getProperty(CPARAM_SSL_CIPHER_SERVER_PREFERENCE);
                    if (sslFlag.equalsIgnoreCase("false")) {
                        sslCipherServerPreference = false;
                    }
                }

                if (cparameters.containsKey(CPARAM_SUBNET)) {
                    subnet = cparameters.getProperty(CPARAM_SUBNET);
                    if (log.isDebugEnabled()) {
                        log.debug("Set subnet: " + subnet);
                    }
                }

                if (cparameters.containsKey(CPARAM_SUBNET6)) {
                    subnet6 = cparameters.getProperty(CPARAM_SUBNET6);
                    if (log.isDebugEnabled()) {
                        log.debug("Set subnet6: " + subnet6);
                    }
                }

                if (cparameters.containsKey(CPARAM_RESERVED_LOG_COUNT)) {
                    logcnt = Integer.parseInt(cparameters
                            .getProperty(CPARAM_RESERVED_LOG_COUNT));
                    if (log.isDebugEnabled()) {
                        log.debug("Set reserved log count: " + logcnt);
                    }
                }

                if (cparameters.containsKey(CPARAM_RUN_ONCE_SCRIPT)) {
                    runOnceScript = cparameters
                            .getProperty(CPARAM_RUN_ONCE_SCRIPT);
                    if (log.isDebugEnabled()) {
                        log.debug("Set run once script after hostname changed: "
                                + runOnceScript);
                    }
                }

                if (cparameters.containsKey(CPARAM_DPI_SYNC_ENABLE)) {
                    String flag = cparameters
                            .getProperty(CPARAM_DPI_SYNC_ENABLE);
                    if (flag.equalsIgnoreCase("false")) {
                        enableDpiSync = false;
                    }
                }

                if (cparameters.containsKey(CPARAM_KEYBOARD_LAYOUT_SYNC_ENABLE)) {
                    String flag = cparameters
                            .getProperty(CPARAM_KEYBOARD_LAYOUT_SYNC_ENABLE);
                    if (flag.equalsIgnoreCase("false")) {
                        enableKeyboardLayoutSync = false;
                    }
                }

                if (cparameters.containsKey(CPARAM_LOCALE_SYNC_ENABLE)) {
                    String flag = cparameters
                            .getProperty(CPARAM_LOCALE_SYNC_ENABLE);
                    if (flag.equalsIgnoreCase("true")) {
                        enableLocaleSync = true;
                    }
                }

                if (cparameters.containsKey(CPARAM_RUN_ONCE_SCRIPT_TIMER)) {
                    runOnceScriptTimeout = cparameters
                            .getProperty(CPARAM_RUN_ONCE_SCRIPT_TIMER);
                    if (log.isDebugEnabled()) {
                        log.debug("Set run once script after hostname timeout changed: "
                                + runOnceScriptTimeout);
                    }
                }

                if (cparameters.containsKey(CPARAM_NETBIOS_DOMAIN)) {
                    netbiosDomain = cparameters
                            .getProperty(CPARAM_NETBIOS_DOMAIN);
                    if (log.isDebugEnabled()) {
                        log.debug("Set netbios domain name: "
                                + netbiosDomain);
                    }
                }

                if (cparameters.containsKey(CPARAM_COLLABORATION_ENABLE)) {
                    String flag = cparameters
                            .getProperty(CPARAM_COLLABORATION_ENABLE);
                    if (flag.equalsIgnoreCase("false")) {
                        collaborationEnabled = false;
                    }
                    if (log.isDebugEnabled()) {
                        log.debug("Set collaborationEnabled: "
                                + collaborationEnabled);
                    }
                }

                if (cparameters.containsKey(CPARAM_EMPTY_SESSION_DELAY)) {
                    emptySessionDelay = Integer.parseInt(cparameters
                        .getProperty(CPARAM_EMPTY_SESSION_DELAY));
                    if (log.isDebugEnabled()) {
                        log.debug("Empty session delay set to: " + emptySessionDelay);
                    }
                }

                if (cparameters.containsKey(CPARAM_DOMAIN_CONTROLLER_FQDN)) {
                    domainController = cparameters.getProperty(CPARAM_DOMAIN_CONTROLLER_FQDN);
                    if (log.isDebugEnabled()) {
                        log.debug("Set domain controller FQDN: " + domainController);
                    }
                }

            }
        } catch (Exception e) {
            log.error("Failed to load customer configuration file: "
                    + e.getMessage());
            if (log.isDebugEnabled()) {
                log.debug(
                        "Failed to load customer configuration file: "
                                + e.getMessage(), e);
            }
            return false;
        }
        return true;
    }

    public boolean load() {
        if (!load_machine_conf()) {
           log.info("Failed to load the machine config during initialization, "
                 + "will attempt to load it again once the machine id is updated.");
        }

        remoteConfig.loadFromFile(remoteConfigFile);

        return load_customer_conf();
    }

    public int getAsyncInitialDelay() {
        return initialNotificationDelay;
    }


    @Override
    public void init() {
        // Not used
    }

    @Override
    public int getAsyncSessionSeconds() {
        return (agentVM == null) ? Machine.DEFAULT_ASYNC_SESSION_INTERVAL :
           agentVM.getAsyncSessionSeconds();
    }

    @Override
    public PublicKey getBrokerPublicKey() {
        try {
            return JMSMessageSecurity.stringToPublicKey(agentVM
                    .getBrokerPublicKey());
        } catch (Exception e) {
            throw new RuntimeException("Broker public key not valid");
        }
    }

    public String getBrokerPublicKeyAsString() {
        return (agentVM == null) ? "" : agentVM.getBrokerPublicKey();
    }

    @Override
    public String getBrokerSSLCertificateThumbprint() {
        return brokerSSLCertificateThumbprint;
    }

    @Override
    public Collection<String> getBrokers() {
        List<String> brokers = null;
        if (agentVM != null) {
           String b = agentVM.getBrokers();
           if (b != null) {
               brokers = new ArrayList<String>();
               String[] sa = StringUtils.split(b, ' ');
               for (String s : sa) {
                   brokers.add(s.trim());
               }
           }
        }
        return brokers;
    }

    @Override
    public void setBrokers(String brokersParam) {
        if (StringUtils.isEmpty(brokersParam)) {
            log.info("Broker list is empty.");
            return;
        }

        agentVM.setBrokers(brokersParam);
        updateMachineConfigfile(agentVM.toGson());
    }

    public String getBrokersAsString() {
        return (agentVM == null) ? "" : agentVM.getBrokers();
    }

    public String getBlastKeystorePath() {
        return blastKeystorePath;
    }

    public String getCertificatePath() {
        return certificatePath;
    }

    public String getDisconnectTimeout() {
        if (StringUtils.isNotEmpty(remoteConfig.getDisconnectTimeout())) {
            return remoteConfig.getDisconnectTimeout();
        }
        return (agentVM == null) ? "" : agentVM.getDisconnectTimeout();
    }

    public int getEmptySessionTimeout() {
        if (StringUtils.isNotEmpty(remoteConfig.getEmptySessionTimeout())) {
            return Integer.valueOf(remoteConfig.getEmptySessionTimeout());
        }
        return (agentVM == null || StringUtils.isEmpty(agentVM.getEmptySessionTimeout())) ? 0 :
                Integer.valueOf(agentVM.getEmptySessionTimeout());
    }

    public boolean getEmptySessionLogoff() {
        if (StringUtils.isNotEmpty(remoteConfig.getEmptySessionLogoff())) {
            return remoteConfig.getEmptySessionLogoff().equals("1");
        }
        return (agentVM == null || StringUtils.isEmpty(agentVM.getEmptySessionLogoff())) ? false :
                agentVM.getEmptySessionLogoff().equals("1");
    }

    public int getPreLaunchSessionTimeout() {
        if (StringUtils.isNotEmpty(remoteConfig.getPreLaunchSessionTimeout())) {
            return Integer.valueOf(remoteConfig.getPreLaunchSessionTimeout());
        }
        return (agentVM == null || StringUtils.isEmpty(agentVM.getPreLaunchSessionTimeout())) ? 0 :
                Integer.valueOf(agentVM.getPreLaunchSessionTimeout());
    }

    public boolean isIPCEnabled() {
        return ipcEnabled;
    }

    public boolean isCollaborationEnabled() {
        return collaborationEnabled;
    }

    /**
     * Get domain name
     *
     * @return domain string
     */
    public String getDomain() {
        if (!StringUtils.isEmpty(netbiosDomain)) {
            return netbiosDomain;
        }
        return domainName;
    }

    /**
     * Get domain fqdn name.
     *
     * @return domain fqdn string
     */
    public String getFqdnDomain() {
        return domainName;
    }

    public String getHostName() {
       return hostName;
    }

    /**
     * Get domain controller fqdn
     *
     * @return domain controller fqdn
     */
    public String getDomainController() {
       return domainController;
    }

    public boolean getRunonce() {
       return runOnce;
    }

    public boolean getFIPSEnable() {
       return fipsEnable;
    }

    /*
     * Return true when uemEnable is enabled and uem network path
     * is not empty, otherwise return false
     */
    public boolean getUEMEnable() {
        return uemEnable && !StringUtils.isEmpty(getUEMNetworkPath());
    }

    /*
     * Return demNetworkPath when it is configured at Titan,
     * otherwise return DEMNetworkPath defined in viewagent-custom.conf
     */
    public String getUEMNetworkPath() {
        return Optional.ofNullable(remoteConfig.getDemNetworkPath())
                .orElse(uemNetworkPath);
    }

    public long getUEMMountTimeout() {
        return uemMountTimeout;
    }

    public double getLbCpuWeightage() {
        return lbCpuWeightage;
    }

    public double getLbMemWeightage() {
        return lbMemWeightage;
    }

    public double getLbDiskWeightage() {
        return lbDiskWeightage;
    }

    public double getLbSessionCountWeightage() {
        return lbSessionCountWeightage;
    }

    public String getLbCustomScript() {
        return lbCustomScript;
    }

    public String getLbCustomScriptResult() {
        return lbCustomScriptResult;
    }

    public int getLbPoolInterval() {
        return lbPoolIntervalSeconds;
    }

    public boolean getLbNvidiaGPUMemoryUsageEnable() {
        return lbNvidiaGPUMemoryUsageEnable;
    }

    public boolean getAppEnable() {
        return appEnable;
    }

    public boolean getEndpointVPNEnable() {
        return endpointVPNEnable;
    }

    @Override
    public Identity getIdentity() {
        try {
            return new Identity(agentVM.getAgentIdentity());
        } catch (Exception e) {
            throw new RuntimeException("Agent identity not valid");
        }
    }

    @Override
    public String getInfrastructureZone() {
        // not used
        return null;
    }

    @Override
    protected PrivateKey getLegacyPrivateKey() {
        log.info("No legacy private key");
        return null;
    }

    public String getJmsKeystoreDirectory() {
        return jmsKeystoreDirectory;
    }

    public KeyStore.PasswordProtection getJmsKeystorePassword() {
        return jmsKeystorePassword;
    }

    @Override
    public MSSecMode getMsMode() {
        return agentVM.getMessageSecurityMode();
    }

    @Override
    public void setMsMode(MSSecMode mode) {
        agentVM.setMessageSecurityMode(mode.toString());
    }

    public String getName() {
        return name;
    }

    public boolean isSingletonMode() {
        return singletonMode;
    }

    public boolean useIPv6() {
        return useIPv6;
    }

    public boolean isOmnMemEnabled() {
        return omnMemEnabled;
    }

    public boolean isHznGreeterEnabled() {
        return hznGreeterEnabled;
    }

    public boolean isVgpuEnabled() {
        return vgpuEnabled;
    }

    public String getOsVersionMajor() {
        return osVersionMajor;
    }

    public String getOsVersionMinor() {
        return osVersionMinor;
    }

    public String getPlatform() {
        return platform;
    }

    @Override
    public PrivateKey getPrivateKey() {
        return agentPairedPrivateKey;
    }

    public String getPrivateKeyAsString() {
        if (!StringUtils.isEmpty(agentPairedPrivateKeyString)) {
            return agentPairedPrivateKeyString;
        }
        return (agentVM == null) ? "" : agentVM.getAgentPrivateKey();
    }

    public Protocol getProtocol() {
        return protocol;
    }

    public int getProtocolPort() {
        return protocolPort;
    }

    @Override
    public PublicKey getPublicKey() {
        return agentPairedPublicKey;
    }

    public String getAgentSSLCertificateThumbprint() {
        return agentSSLCertificateThumbprint;
    }

    public void setAgentSSLCertificateThumbprint(String thumbprint) {
        agentSSLCertificateThumbprint = thumbprint;
    }

    public String getProgramBlastServer() {
        return programBlastServer;
    }

    public String getProgramDesktopDaemon() {
        return programDesktopDaemon;
    }

    public String getCollabUIPath() {
        return collabUIPath;
    }

    public String getScriptStartRdeServer() {
        return scriptStartRDEServer;
    }

    public String getScriptRunAppScanner() {
        return scriptRunAppScanner;
    }

    public String getScriptControlExtension() {
        return scriptControlExtension;
    }

    public String getAppIconDir() {
        return appIconDir;
    }

    public String getAppXmlFile() {
        return appXmlFile;
    }

    public String getScriptLaunchApp() {
        return scriptLaunchApp;
    }

    public String getScriptPersistentDisk() {
        return scriptPersistentDisk;
    }

    public String getScriptPostLogin() {
        return scriptPostLogin;
    }

    public String getScriptPostLoginUser() {
        return scriptPostLoginUser;
    }

    public String getScriptPostConnection() {
        return scriptPostConnection;
    }

    public String getScriptConsoleMode() {
        return scriptConsoleMode;
    }

    public String getScriptRunAutorandr() {
        return scriptRunAutorandr;
    }

    public String getScriptStartXServer() {
        return scriptStartXServer;
    }

    public String getScriptStartBlastWorker() {
       return scriptStartBlastWorker;
    }

    public String getScriptStartClipboard() {
       return scriptStartClipboard;
    }

    public String getScriptStartCDR() { return scriptStartCDR; }

    public String getScriptStartHtml5mmr() { return scriptStartHtml5mmr; }

    public String getScriptStartUsbRedir() {
        return scriptStartUsbRedir;
    }

    public String getScriptStopUsbRedir() {
        return scriptStopUsbRedir;
    }

    public String getScriptStartPrintRedir() {
       return scriptStartPrintRedir;
    }

    public String getScriptSsoConfigDesktopType() {
        return scriptSsoConfigDesktopType;
    }

    public String getScriptCleanLog() {
        return scriptCleanLog;
    }

    public String getScriptMountFolder() {
        return scriptMountFolder;
    }

    public String getScriptUMountFolder() {
        return scriptUMountFolder;
    }

    public String getCustomerConfigFileBlast() {
        return customerConfigFileBlast;
    }

    @Override
    public String getServerDn() {
        return (agentVM == null) ? "" : agentVM.getDn().toLowerCase();
    }

    @Override
    public String getServerDnsName() {

        return !fqdn.isEmpty() ? fqdn : getName();
    }

    @Override
    public String getServerPoolDn() {
        if (agentVM == null) {
           return StringUtils.EMPTY;
        }
        if (null != agentVM.getPoolDn()) {
            return agentVM.getPoolDn().toLowerCase();
        } else {
            return StringUtils.EMPTY;
        }
    }

    public boolean getUseSVI() {
       boolean useSVI = false;
       if (agentVM != null) {
          useSVI = ("1".equals(agentVM.getUseSVI())) ? true : false;
       }
       return useSVI;
    }

    public boolean getUseSysprep() {
       boolean useSysprep = false;
       if (agentVM != null) {
          useSysprep = ("1".equals(agentVM.getUseSysprep())) ? true : false;
       }
       return useSysprep;
    }

    public String getSsoUserFormat() {
        return ssoUserFormat;
    }

    public String getSsoDesktopType() {
        return ssoDesktopType;
    }

    public int getStartBlastServerTimeout() {
        return startBlastServerTimeout;
    }

    public int getCustomPendingSessionTimeout() {
        return pendingSessionTimeout;
    }

    public int getCustomSsoTimeout() {
        return ssoTimeout;
    }

    public String getSslProtocols() {
        if (StringUtils.isNotEmpty(sslProtocols)) {
            return sslProtocols;
        } else {
            return fipsEnable ? FIPS_SECURE_PROTOCOLS[0] : DEFAULT_BLAST_PROTOCOLS;
        }
    }

    public String getSslCiphers() {
        return sslCiphers;
    }

    public String getTlsCipherSuites() {
        // Only support tlsCipherSuites for TLSv1.3
        String protocols = getSslProtocols();
        if (StringUtils.containsIgnoreCase(protocols, "TLSv1.3")) {
            return tlsCipherSuites;
        }
        return null;
    }

    public String getSslCertName() {
        return sslCertName;
    }

    public String getSslKeyName() {
        return sslKeyName;
    }

    public boolean isKeyringsEnabled() {
        return keyringsEnabled;
    }

    public void setKeyringsFlag(boolean enabled) {
        keyringsEnabled = enabled;
    }

    public String getTlsCurves() {
        return tlsCurves;
    }

    public String getSubnet() {
        return subnet;
    }

    public String getSubnet6() {
        return subnet6;
    }

    public int getLogCnt() {
        return logcnt;
    }

    public String getRunOnceScript() {
        return runOnceScript;
    }

    public Lock getLoadConfLock() {
        return loadConfLock;
    }

    public String getRunOnceScriptTimeout() {
        return runOnceScriptTimeout;
    }

    public String getBaseDn() {
        if (agentVM != null) {
            return agentVM.getBaseDn();
        }

        if (isVADCInstalled()) {
            return BASE_DN;
        } else {
            return "";
        }
    }

    public boolean isSslCipherServerPreference() {
        return sslCipherServerPreference;
    }

    public boolean isUsbInst() {
       return usbInst;
    }

    public boolean isCollabInst() {
       return collaborationInst;
    }

    @Override
    public boolean isManaged() {
        return managedVM;
    }

    public boolean isCDRLocalEnable() {
        return cdrEnabled;
    }

    public boolean isBlockScreenCaptureLocalEnable() {
        return blockScreenCaptureEnabled;
    }

    public boolean isHelpDeskEnabled() {
        // Helpdesk is not support at VADC env
        return !isVADCInstalled() && helpDeskEnabled;
    }

    public void setHelpDeskFlag(boolean enabled) {
        helpDeskEnabled = enabled;
    }

    public boolean isDpiSyncEnable() {
        return enableDpiSync;
    }

    public boolean isKeyboardLayoutSyncEnable() {
        return enableKeyboardLayoutSync;
    }

    public boolean isLocaleSyncEnabled() {
        return enableLocaleSync;
    }

    public boolean isCDRInstalled() {
        return cdrInstalled;
    }

    public boolean isClipboardInstalled() {
        return clipboardInstalled;
    }

    public boolean isHtml5mmrInstalled() { return html5mmrInstalled; }

    public boolean isAppInstalled() {
        return appInstalled;
    }

    public boolean supportRemoteApp() {
        return getAppEnable() && isAppInstalled();
    }

    public boolean isVADCInstalled() {
        return vadcInstalled;
    }

    public boolean isPrinterRedirInstalled() {
        return printerRedirInstalled;
    }

    public boolean isPrinterRedirEnabled(long desktopId) {
        return printerRedirEnabled;
    }

    public boolean isDelFailedClonedVM() {
        return delFailedClonedVM;
    }

    @Override
    public boolean isPaired() {
        return isPaired;
    }

    public void SetPaired(boolean isPaired) {
       this.isPaired = isPaired;
    }

    public boolean isUSBLocalEnable() {
        return enableUSB;
    }

    public boolean isSsoInstalled() {
        return ssoInstalled;
    }

    public boolean isTrueSsoInstalled() {
        return trueSsoInstalled;
    }

    public boolean isEnableSSO() {
       return enableSSO;
    }

    /**
     * Check whether support console desktop, only support console desktop
     * on physical machine.
     *
     * @return True if success, otherwise False
     */
    public boolean isConsoleMode() {
        return enableConsoleMode;
    }

    @Override
    protected void pairOverJmsComplete() {
        log.info("Agent pairing complete: " + agentVM.getName());
        isPaired = true;

        try {
            FileBasedCertificateManager.getInstance().writeToDirectory(
                    getJmsKeystoreDirectory());
        } catch (Exception e) {
            log.error("Failed to write to JMS Keystore directory");
            if (log.isDebugEnabled()) {
                log.debug("Failed to write to JMS Keystore directory", e);
            }
        }
    }

    public synchronized void updateMachineConfigfile(String context) {
        try {
            Utils.writeTextFile(Paths.get(machineConfigFile), context);
            Files.setPosixFilePermissions(Paths.get(machineConfigFile),
                    PosixFilePermissions.fromString("rw-------"));
        } catch (Exception e) {
            log.error("Failed to update machine config file, " +
                      Utils.printExceptionStack(e));
        }
    }

        /*
     * MQTT Reconfigure
     */
    public void reconfigureToRemote(String disconnnectTimeout, String emptySessionTimeout,
            String emptySessionLogoff, String preLaunchSessionTimeout,
            String maxSessions, String poolDisplayName, String esurl,
            String productUsageSharingEnabled, String demNetworkPath, String idleTimeout) {
        boolean needUpdate = false;

        if (disconnnectTimeout != null) {
            remoteConfig.setDisconnectTimeout(disconnnectTimeout);
            needUpdate = true;
        }
        if (emptySessionTimeout != null) {
            remoteConfig.setEmptySessionTimeout(emptySessionTimeout);
            needUpdate = true;
        }
        if (emptySessionLogoff != null) {
            remoteConfig.setEmptySessionLogoff(emptySessionLogoff);
            needUpdate = true;
        }
        if (preLaunchSessionTimeout != null) {
            remoteConfig.setPreLaunchSessionTimeout(preLaunchSessionTimeout);
            needUpdate = true;
        }
        if (maxSessions != null) {
            remoteConfig.setMaxSessions(maxSessions);
            needUpdate = true;
        }
        if (poolDisplayName != null) {
            remoteConfig.setPoolDisplayName(poolDisplayName);
            needUpdate = true;
        }
        if (esurl != null) {
            remoteConfig.setEsurl(esurl);
            needUpdate = true;
        }
        if (productUsageSharingEnabled != null) {
            remoteConfig.setProductUsageSharingEnabled(productUsageSharingEnabled);
            needUpdate = true;
        }
        if (idleTimeout != null) {
            remoteConfig.setIdleTimeout(idleTimeout);
            needUpdate = true;
        }
        if (demNetworkPath != null) {
            updateDemNetworkPath(demNetworkPath);
            remoteConfig.setDemNetworkPath(demNetworkPath);
            needUpdate = true;
        }

        if (needUpdate) {
            remoteConfig.updateToFile(remoteConfigFile);
        }
    }

    public boolean reconfigure(String asyncSessionSeconds, String brokerPublicKey,
            String messageSecurityMode, String newPoolDn, String brokers,
            String disconnnectTimeout, String emptySessionTimeout,
            String emptySessionLogoff, String preLaunchSessionTimeout,
            String maxSessions, String clientCipherSuites,
            String namedGrps, String thumbprintAlgos, String clientSigSchemes,
            String certSigAlgo, String preferredThumbprintAlgo,
            String clientProtocols, String noManagedCert) {
        boolean needUpdateMachine = false;
        boolean needUpdateRemote = false;

        /*
         * Update common settings which used by both managed and unmanged VM
         */
        if (clientCipherSuites != null) {
            remoteConfig.setClientCipherSuites(clientCipherSuites);
            needUpdateRemote = true;
        }

        if (namedGrps != null) {
            remoteConfig.setNamedGrps(namedGrps);
            needUpdateRemote = true;
        }

        if (thumbprintAlgos != null) {
            remoteConfig.setThumbprintAlgos(thumbprintAlgos);
            needUpdateRemote = true;
        }

        if (clientSigSchemes != null) {
            remoteConfig.setClientSigSchemes(clientSigSchemes);
            needUpdateRemote = true;
        }
        if (certSigAlgo != null) {
            remoteConfig.setCertSigAlgo(certSigAlgo);
            needUpdateRemote = true;
        }

        if (preferredThumbprintAlgo != null) {
            remoteConfig.setPreferredThumbprintAlgo(preferredThumbprintAlgo);
            needUpdateRemote = true;
        }

        if (clientProtocols != null) {
            remoteConfig.setClientProtocols(clientProtocols);
            needUpdateRemote = true;
        }
        if (noManagedCert != null) {
            remoteConfig.setNoManagedCert(noManagedCert);
            needUpdateRemote = true;
        }

        /*
         * Update unmanged VM settings
         */
        if (!StringUtils.isEmpty(asyncSessionSeconds)) {
            agentVM.setAsyncSessionSeconds(Integer
            .parseInt(asyncSessionSeconds));
            needUpdateMachine = true;
        }
        if (brokerPublicKey != null) {
            agentVM.setBrokerPublicKey(brokerPublicKey);
            needUpdateMachine = true;
        }
        if (messageSecurityMode != null) {
            agentVM.setMessageSecurityMode(messageSecurityMode);
            needUpdateMachine = true;
        }
        if (newPoolDn != null) {
            agentVM.setPoolDn(newPoolDn);
            needUpdateMachine = true;
        }
        if (brokers != null) {
            agentVM.setBrokers(brokers);
            needUpdateMachine = true;
        }
        if (disconnnectTimeout != null) {
            agentVM.setDisconnectTimeout(disconnnectTimeout);
            needUpdateMachine = true;
        }
        if (emptySessionTimeout != null) {
            agentVM.setEmptySessionTimeout(emptySessionTimeout);
            needUpdateMachine = true;
        }
        if (emptySessionLogoff != null) {
            agentVM.setEmptySessionLogoff(emptySessionLogoff);
            needUpdateMachine = true;
        }
        if (preLaunchSessionTimeout != null) {
            agentVM.setPreLaunchSessionTimeout(preLaunchSessionTimeout);
            needUpdateMachine = true;
        }
        if (maxSessions != null) {
            agentVM.setMaxSessions(maxSessions);
            needUpdateMachine = true;
        }

        if (needUpdateMachine) {
            updateMachineConfigfile(agentVM.toGson());
        }

        if (needUpdateRemote) {
            remoteConfig.updateToFile(remoteConfigFile);
        }
        return needUpdateMachine;
    }

    void syncDisconnectTimeoutToFile(String disconnnectTimeout) {
        agentVM.setDisconnectTimeout(disconnnectTimeout);
        updateMachineConfigfile(agentVM.toGson());
    }

    @Override
    public void setBrokerSSLCertificateThumbprint(String thumbprint) {
        brokerSSLCertificateThumbprint = thumbprint;
        FileBasedSSLSocketFactory.setThumbprint(thumbprint);
    }

    public synchronized String getMachineConfigFile() {
       return machineConfigFile;
    }

    public Map<Long, String> GetKeyboardLayoutMapping() {
        return keyboardLayoutMap;
    }

    public Map<String, String> GetKeyboardLanguageMapping() {
        return keyboardLanguageMap;
    }

    public void SetAgentShutDown(boolean shutDown) {
        agentShutDown = shutDown;
    }

    /* this svi state value is from struct SvmPolicyState in
       "\sim\ngvc-agent-main\vmsoft\apps\svm\public\ViewComposerGuestAgent.h"
     */
    public boolean getSviState() {
        return sviState;
    }

    /**
     * Get UEM policy and write to the non persistent config file.
     *
     * @param clientIP
     *            The client IP
     * @param clientLocation
     *            The client location
     * @param clientPlatform
     *            The client platform type
     *
     * @return true if successfully; otherwise false.
     */
    public synchronized boolean initUEMPolicy(String clientIP,
                                              String clientLocation,
                                              String clientPlatform,
                                              long desktopid) {
        // Load UEM policy
        UEMPolicy uem = new UEMPolicy(UEM_MOUNT_POINT, clientIP,
                                      clientLocation, clientPlatform);
        boolean result = uem.getDEMSettings();

        // If get UEM policy failed, we should return.
        if (!result) {
            log.info("Load DEM settings failed");
            // try to mount uem folder again
            if (mountUEMFolder()) {
                result = uem.getDEMSettings();
                if (!result)
                    return false;
            } else {
                return false;
            }
        }

        // Convert UEM policy to rde options
        UEMBlastAdapter uemBlastAdapter = new UEMBlastAdapter(uem);
        uemAdapterCollection.put(new Long(desktopid), uemBlastAdapter);
        return true;
    }

    /**
     * Check the USB server can be launched or not. In the scenario that the UEM
     * switch is on: (1) The USB server should be launched always; (2) The USB
     * server should enable/disable via the vdp message isUsbAvailable. In the
     * scenario that the UEM switch is off, return the local USB switch.
     *
     * @return true if USB server can be launched; otherwise false.
     */
    public boolean canLaunchUSBServer() {
        return getUEMEnable() || isUSBLocalEnable();
    }

    /**
     * Check the CDR enable/disable. If the customer set UEM CDR policy, return
     * the UEM policy; otherwise return the local CDR policy.
     *
     * @return true if CDR enabled; otherwise false.
     */
    public boolean isCDREnable(long desktopId) {
        if (getUEMEnable()) {
            UEMBlastAdapter uemBlastAdapter =
                          uemAdapterCollection.get(new Long(desktopId));
            if (uemBlastAdapter == null) {
                log.error("Exception uemBlastAdapter is null");
                return false;
            } else {
                Boolean cdrEnable = uemBlastAdapter.getCDREnable();
                if (cdrEnable != null) {
                    return cdrEnable.booleanValue();
                }
            }
        }
        return isCDRLocalEnable();
    }

    /**
     * Check the BlockScreenCapture enable/disable. If the customer set UEM
     * BlockScreenCapture policy, return the UEM policy; otherwise return
     * the local BlockScreenCapture policy.
     *
     * @return true if BlockScreenCapture enabled; otherwise false.
     */
    public boolean isBlockScreenCaptureEnable(long desktopId) {
        if (getUEMEnable()) {
            UEMBlastAdapter uemBlastAdapter =
                          uemAdapterCollection.get(new Long(desktopId));
            if (uemBlastAdapter == null) {
                log.error("Exception uemBlastAdapter is null");
                return false;
            } else {
                Boolean blockScreenCaptureEnable =
                            uemBlastAdapter.getBlockScreenCaptureEnable();
                if (blockScreenCaptureEnable != null) {
                    return blockScreenCaptureEnable.booleanValue();
                }
            }
        }
        return isBlockScreenCaptureLocalEnable();
    }

    /**
     * Check the clipboard enable/disable. If the customer set UEM clipboard
     * policy, return the UEM policy; otherwise return true as the clipboard is
     * enabled always.
     *
     * @return true if clipboard enabled; otherwise false.
     */
    public boolean isClipboardEnable(long desktopId) {
        if (getUEMEnable()) {
            UEMBlastAdapter uemBlastAdapter =
                  uemAdapterCollection.get(new Long(desktopId));
            if (uemBlastAdapter == null) {
                log.error("Exception uemBlastAdapter is null");
                return false;
            } else {
                Boolean clipboardEnable = uemBlastAdapter.getClipboardEnable();
                if (clipboardEnable != null) {
                    return clipboardEnable.booleanValue();
                }
            }
        }
        return true;
    }

    private UEMMountManager getUEMMountManager() {
        if (mountManager == null) {
            mountManager = new UEMMountManager(
                    getScriptMountFolder(), getScriptUMountFolder(),
                    getUEMNetworkPath(), StandaloneAgentConfig.UEM_MOUNT_POINT,
                    getUEMMountTimeout());
        }

        return mountManager;
    }

    public boolean mountUEMFolder() {
        unmountUEMFolder();
        if (getUEMMountManager().mount()) {
            log.info("Succeed to mount DEM network path");
            return true;
        }

        log.error("Failed to mount DEM network path");
        return false;
    }

    public void unmountUEMFolder() {
        if (getUEMMountManager().umount()) {
            log.info("Succeed to umount DEM network path");
            return;
        }

        log.error("Failed to umount DEM network path");
    }

    /**
     * Clean UEM policy and restore the local blast config file.
     *
     * @return
     */
    public synchronized void cleanUEMPolicy() {
        if (getUEMEnable()) {
            log.info("Cleaning the DEM policy and restoring the local blast config file");
            for(Long desktopid : uemAdapterCollection.keySet()) {
               uemAdapterCollection.remove(new Long(desktopid));
            }
            UEMNonPersistentConfig.cleanUEMConfigFolder();
        }
    }

    public UEMBlastAdapter getUemAdapter(long desktopid) {
        return uemAdapterCollection.get(new Long(desktopid));
    }

    public void removeUEMAdapter(long desktopid) {
        uemAdapterCollection.remove(new Long(desktopid));
    }

    /**
     * We only support 1 session in singleton mode.
     * And up to 50 sessions in multi-session mode.
     *
     * @return max sessions number
     */
    public int getMaxSessions() {
       // The max sessions is 1 always in singleton mode
       int maxSessions = 1;

       if (!isSingletonMode()) {
          maxSessions = agentVM.DEFAULT_MAX_SESSIONS;
          if (agentVM != null && !StringUtils.isEmpty(agentVM.getMaxSessions())) {
             try {
                 int value = Integer.valueOf(agentVM.getMaxSessions());
                 if (value > 0 || value < agentVM.DEFAULT_MAX_SESSIONS) {
                    maxSessions = value;
                 }
             } catch (NumberFormatException e) {
                 log.warn("Max session in connection-sever is invalid (" +
                          agentVM.getMaxSessions() + ")," + " use default value: " +
                          agentVM.DEFAULT_MAX_SESSIONS);
             }
          }
       }
       return maxSessions;
    }

    public int getMaxSessionsBuffer() {
       if (maxSessionsBuffer > getMaxSessions()) {
          maxSessionsBuffer = getMaxSessions();
       }
       return maxSessionsBuffer;
    }

    public int getStartStormThrottle() {
       return startStormThrottle;
    }

    public int getMaxSessionThreads() {
      return maxSessionThreads;
    }

    public int getEmptySessionDelay() {
      return emptySessionDelay;
    }

    @Override
    public long getCmsMessageTtl() {
        return TimeUnit.MINUTES.toMillis(5);
    }

    @Override
    public long getChangeKeyMessageTtl() {
        return TimeUnit.SECONDS.toMillis(10);
    }

    @Override
    public long getChangeKeyResponseTimeout() {
        return TimeUnit.SECONDS.toMillis(15);
    }

    public String getAgentBuildVersion(){
        if (!StringUtils.isEmpty(agentBuildVersion)){
            return agentBuildVersion;
        }
        readProductTxt();
        return (StringUtils.isEmpty(agentBuildVersion)) ? Version.getVersion() :
           agentBuildVersion;
    }

    public String getAgentBuildNum(){
        if (!StringUtils.isEmpty(agentBuildNum)){
            return agentBuildNum;
        }
        readProductTxt();
        return (StringUtils.isEmpty(agentBuildNum)) ? Version.getBuild() :
           agentBuildNum;

    }

    public String getVadcConfigFile() {
        return vadcConfigFile;
    }

    public String getSystemUUID() {
        return osSystemUUID;
    }

    private void readProductTxt() {
        String tempString = null;
        try {
            tempString = Files.lines(Paths.get(PRODUCT_TXT)).findFirst().get();
        } catch (IOException e) {
            log.error("Error reading PRODUCT_TXT "
                     + PRODUCT_TXT + " :" + e.getMessage());
        }
        if (!StringUtils.isEmpty(tempString)) {
                Pattern p = Pattern.compile(".*-(.*)-(\\d*)$");
                Matcher m = p.matcher(tempString);
                if (m.find()) {
                    agentBuildVersion = m.group(1).trim();
                    agentBuildNum = m.group(2).trim();
                }
        }
    }

    public static String getProductTxtFileName() {
        return PRODUCT_TXT;
    }

    /**
     * @see com.omnissa.vdi.agent.messageserver.AgentJmsConfig#getManagedCertificateAdvanceRollOver
     * Un-used.
     */
    @Override
    public int getManagedCertificateAdvanceRollOver() {
        return 0;
    }

    /**
     * @see com.omnissa.vdi.agent.messageserver.AgentJmsConfig#getEncryptionKey
     */
    @Override
    public KeyPair getEncryptionKey() {
        return encryptionKeyPair;
    }

    public void setEncryptionKey(KeyPair kp) {
        encryptionKeyPair = kp;
    }

    public Map<String, String> getRemoteConfig() {
        return remoteConfig.getRemoteConfig();
    }

    /*
     * Handle DEM mount point changes at Horizon v2
     */
    private void updateDemNetworkPath(String demConfigPath) {
        String networkPath = demConfigPath;
        if (StringUtils.contains(demConfigPath, "\\")) {
            /*
             * Windows format => Linux format
             * "\\\\Server\\Path" => "//Server/Path"
             */
            networkPath = StringUtils.replace(demConfigPath, "\\", "/");
        }

        if (!StringUtils.equals(networkPath, getUEMNetworkPath())) {
            log.info("Update dem network path {} to {}...",
                    getUEMNetworkPath(), networkPath);
            // Update new DEM network path
            getUEMMountManager().setDemNetworkPath(networkPath);

            if (networkPath == "") {
                // DemConfigPath is removed from this vm
                unmountUEMFolder();
            } else {
                // Mount new network path for DEM feature
                mountUEMFolder();
            }
        }
    }

    private String[] parseSslProtocols(String strProtocls) {
        List<String> list = new ArrayList<>();
        if (StringUtils.isNotEmpty(strProtocls)) {
            String[] array = strProtocls.split(":");

            if (array != null) {
                for (int i = 0; i < array.length; i++) {
                    if (array[i].equalsIgnoreCase("TLSv1.1") &&
                               !list.contains("TLSv1.1")) {
                        if (!fipsEnable) {
                            list.add("TLSv1.1");
                        } else {
                            log.error("Unsupport TLSv1.1 in FIPS mode");
                        }
                    } else if (array[i].equalsIgnoreCase("TLSv1.2") &&
                               !list.contains("TLSv1.2")) {
                        list.add("TLSv1.2");
                    } else if (array[i].equalsIgnoreCase("TLSv1.3") &&
                               !list.contains("TLSv1.3")) {
                        list.add("TLSv1.3");
                    } else {
                        log.error("Unsupport ssl protocol: " + array[i]);
                    }
                }
            }
        }

        return (list == null || list.isEmpty()) ? null : list.toArray(new String[list.size()]);
    }

    private void setClientCipherSuites() {
        String[] suites = RenderList.stringToArray(remoteConfig.getClientCipherSuites());
        if (suites == null) {
            if (fipsEnable) {
                suites = FIPS_CIPHERSUITES;
                String[] protocols = SslUtils.getSecureProtocols(sslRole.CLIENT);
                if (protocols != null) {
                    for (String protocol : protocols) {
                        if (protocol.equalsIgnoreCase("TLSv1.3")) {
                            suites = FIPS_SERVER_CIPHERSUITES;
                            break;
                        }
                    }
                }
                log.info("Resetting SSL client enabled cipherSuites with fips mode: "
                          + Arrays.toString(suites));
            }
            log.info("Resetting SSL client enabled cipherSuites");
        } else {
            log.info("Overriding SSL client enabled cipherSuites: "
                     + Arrays.toString(suites));
        }
        SslUtils.setCipherSuites(sslRole.CLIENT, suites);
    }

    private void setClientSslProtocols() {
        String[] protocols = RenderList.stringToArray(remoteConfig.getClientProtocols());
        if (protocols == null) {
            protocols = parseSslProtocols(this.sslProtocols);
        }

        if (protocols == null) {
            protocols = fipsEnable ? FIPS_SECURE_PROTOCOLS : null;
            log.info("Resetting SSL client enabled protocols" +
                     (fipsEnable ? " with fips mode" : ""));
        } else {
            log.info("Overriding SSL client enabled protocols: "
                     + Arrays.toString(protocols));
        }
        SslUtils.setSecureProtocols(sslRole.CLIENT, protocols);
        SslUtils.setPreferredProtocol(sslRole.CLIENT,
                      protocols != null ? protocols[0] : null);
    }

    private void setNamedGroups() {
        List<String> namedGroups = RenderList.stringToList(remoteConfig.getNamedGrps());
        if ((namedGroups == null || namedGroups.isEmpty()) && fipsEnable) {
            namedGroups = Arrays.asList(FIPS_NAMED_GROUPS);
        }
        SslUtils.setNamedGroups(namedGroups);
    }

    private void setSignatureSchemes() {
        List<String> clientSignatureSchemes = RenderList.stringToList(
                remoteConfig.getClientSigSchemes());
        if ((clientSignatureSchemes == null || clientSignatureSchemes.isEmpty()) && fipsEnable) {
            clientSignatureSchemes = Arrays.asList(FIPS_CLIENT_SIGNATURE_SCHEMES);
        }
        SslUtils.setSignatureSchemes(sslRole.CLIENT, clientSignatureSchemes);
    }

    private void setPreferredThumbprintAlgorithm() {
        String preferredThumbprintAlgorithm = remoteConfig.getPreferredThumbprintAlgo();
        if (StringUtils.isEmpty(preferredThumbprintAlgorithm)) {
            preferredThumbprintAlgorithm = fipsEnable ? FIPS_PREFERRED_THUMBPRINT_ALGORITHM : null;
        }
        SslUtils.setPreferredThumbprintAlgorithm(preferredThumbprintAlgorithm);
    }

    private void setThumbprintAlgorithms() {
        List<String> thumbprintAlgorithms = RenderList.stringToList(remoteConfig.getThumbprintAlgos());
        if ((thumbprintAlgorithms == null || thumbprintAlgorithms.isEmpty()) && fipsEnable) {
            thumbprintAlgorithms = Arrays.asList(FIPS_THUMBPRINT_ALGORITHMS);
        }
        SslUtils.setThumbprintAlgorithms(thumbprintAlgorithms);
    }

    private void setCertificateSignatureAlgorithm() {
        String certificateSignatureAlgorithm = remoteConfig.getCertSigAlgo();
        if (StringUtils.isEmpty(certificateSignatureAlgorithm)) {
            certificateSignatureAlgorithm = fipsEnable ? FIPS_CERTIFICATE_SIGNATURE_ALGORITHM : null;
        }
        SslUtils.setCertificateSignatureAlgorithm(certificateSignatureAlgorithm);
    }

    private void setNoManagedCertificate() {
        Boolean noManagedCertificate = null;
        String strNoManagedCert = remoteConfig.getNoManagedCert();
        if (!StringUtils.isEmpty(strNoManagedCert)) {
           if (strNoManagedCert.equals("1")) {
               noManagedCertificate = true;
           } else if (strNoManagedCert.equals("0")) {
               noManagedCertificate = false;
           } else {
               log.error("Unknown noManagedCertificate flag: " + strNoManagedCert);
           }
        }
        SslUtils.setNoManagedCertificate(noManagedCertificate);
    }

    public void configureSSL() {
        log.info("Agent SSL configured");

        // process client ssl protocols
        setClientSslProtocols();

        // process client ssl cipher suites
        setClientCipherSuites();

        // process named groups
        setNamedGroups();

        // process client signature schemes
        setSignatureSchemes();

        // process preferred thumbprint algorithm
        setPreferredThumbprintAlgorithm();

        // process thumbprint algorithms
        setThumbprintAlgorithms();

        // process certificate signature algorithm
        setCertificateSignatureAlgorithm();

        // process NoManagedCertificate flag
        setNoManagedCertificate();

        if (fipsEnable) {
            JMSMessageSecurity.setOptions(FIPS_Options.split(","));
            if (isVADCInstalled()) {
                SslUtils.setCipherSuites(sslRole.SERVER, FIPS_SERVER_CIPHERSUITES);
                SslUtils.setSecureProtocols(sslRole.SERVER, FIPS_SERVER_PROTOCOLS);
                SslUtils.setPreferredProtocol(sslRole.SERVER,
                        FIPS_SERVER_PROTOCOLS != null ? FIPS_SERVER_PROTOCOLS[0] : null);
            }
        }
    }

    public boolean resetRemoteConfig() {
        return remoteConfig.resetConfig(remoteConfigFile);
    }

    @Override
    public void setUseSSOKeyForEncryption(String useSSOKeyForEncryption) {
        log.debug("Setting use SSO keys: " + useSSOKeyForEncryption);
        useSSOKey = Boolean.valueOf(useSSOKeyForEncryption);
    }

    @Override
    public boolean getUseSSOKeyForEncryption() {
        return useSSOKey;
    }

    public void disableApp() {
        this.appEnable = false;
    }

    public static String getDefaultBaseDn() {
        return BASE_DN;
    }

    @Override
    public void setIcaParameter(String name, String value) {
        return;
    }

     @Override
    public KeyPair getGoldenImageKeypair() {
        return null;
    }

    @Override
    public void setRePairingKey(KeyPair rePairingKeyPair) {
        return;
    }

    @Override
    public boolean shouldCreateRePairingKeyPair() {
        return false;
    }

    @Override
    public KeyPair createRePairingKeyPair() {
        return null;
    }

    @Override
    public KeyPair getRePairingKey() {
        return null;
    }
}
