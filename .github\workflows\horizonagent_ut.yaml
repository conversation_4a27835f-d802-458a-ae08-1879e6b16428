name: horizonagent-ut

on:
  workflow_call:
    inputs:
      buildtype:
        type: string
        description: Build type
        required: True

  workflow_dispatch:
    inputs:
      productWorkflow:
        type: choice
        description: Product
        required: True
        default: horizonagent
        options:
          - horizonagent
      runNumber:
        type: string
        description: 'run number of horizonagent'
        required: true
      runAttempt:
        type: string
        description: 'run attempt'
        required: true
        default: '1'
      buildtype:
        type: choice
        description: Build type
        required: True
        default: beta
        options:
          - beta
          - obj
          - release

env:
  PRODUCTWORKFLOW: ${{ github.event_name == 'workflow_dispatch' && inputs.productWorkflow || github.workflow }}
  RUNNUMBER: ${{ github.event_name == 'workflow_dispatch' && inputs.runNumber || github.run_number }}
  RUNATTEMPT: ${{ github.event_name == 'workflow_dispatch' && inputs.runAttempt || github.run_attempt }}
  PRODUCTWORKFLOWJOB: build-horizonagent

jobs:
  ar-rde-server:
    # TODO: re-enable when test are fixed
    if: ${{inputs.buildtype == 'obj'}}
    runs-on: [cart-bj, ut, self-hosted, Windows]
    timeout-minutes: 20
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Run Case
        uses: ./.github/actions/testframework/runcase
        with:
          productWorkflow: $PRODUCTWORKFLOW
          runNumber: $RUNNUMBER
          runAttempt: $RUNATTEMPT
          productWorkflowJob: $PRODUCTWORKFLOWJOB
          artifactoryBaseUrl: ${{ vars.ARTIFACTORY_GITHUB_DELIVERABLES_URL }}
          artifactoryApiToken: ${{ secrets.ARTIFACTORY_GITHUB_DELIVERABLES_API_TOKEN }}
          reportDbUri: ${{ secrets.VDUB_REPORT_DB_W }}
          reportDbName: ${{ secrets.VDUB_REPORT_DB_NAME }}
          reportDbCollection: ${{ secrets.VDUB_REPORT_COL_NAME }}
          testFilePaths: tests/win64/rdeServerComponentTest.zip
          testCommand: ./Horizon-RdeServer-Test.exe --gtest_catch_exceptions=0 --gtest_output=xml:unitTest.xml --gtest_filter=*UnitTest*
          buildtype: ${{ inputs.buildtype }}
          pdbPath: Horizon-RdeServer-Test.pdb

  rx-clipboard:
    runs-on: [cart-bj, ut, self-hosted, Windows]
    timeout-minutes: 20
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Run Case
        uses: ./.github/actions/testframework/runcase
        with:
          productWorkflow: $PRODUCTWORKFLOW
          runNumber: $RUNNUMBER
          runAttempt: $RUNATTEMPT
          productWorkflowJob: $PRODUCTWORKFLOWJOB
          artifactoryBaseUrl: ${{ vars.ARTIFACTORY_GITHUB_DELIVERABLES_URL }}
          artifactoryApiToken: ${{ secrets.ARTIFACTORY_GITHUB_DELIVERABLES_API_TOKEN }}
          reportDbUri: ${{ secrets.VDUB_REPORT_DB_W }}
          reportDbName: ${{ secrets.VDUB_REPORT_DB_NAME }}
          reportDbCollection: ${{ secrets.VDUB_REPORT_COL_NAME }}
          testFilePaths: tests/horizonrxut.zip,tests/win64/clipboardServerUnitTest.zip
          testCommand: ./horizonrxut.exe clipboardServerUnitTest.dll --report=unitTest.xml
          buildtype: ${{ inputs.buildtype }}
          # TODO: this dll doesn't have a pdb file, which might cause issues in cc
          # report. If this job starts having issues with the cc report including
          # lines not in source code, this needs to be fixed
          #pdbPath: clipboardServerUnitTest.pdb

  rx-dnd:
    runs-on: [cart-bj, ut, self-hosted, Windows]
    timeout-minutes: 20
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Run Case
        uses: ./.github/actions/testframework/runcase
        with:
          productWorkflow: $PRODUCTWORKFLOW
          runNumber: $RUNNUMBER
          runAttempt: $RUNATTEMPT
          productWorkflowJob: $PRODUCTWORKFLOWJOB
          artifactoryBaseUrl: ${{ vars.ARTIFACTORY_GITHUB_DELIVERABLES_URL }}
          artifactoryApiToken: ${{ secrets.ARTIFACTORY_GITHUB_DELIVERABLES_API_TOKEN }}
          reportDbUri: ${{ secrets.VDUB_REPORT_DB_W }}
          reportDbName: ${{ secrets.VDUB_REPORT_DB_NAME }}
          reportDbCollection: ${{ secrets.VDUB_REPORT_COL_NAME }}
          testFilePaths: tests/win64/dndUT.zip
          testCommand: ./dndTests.exe --gtest_output=xml:unitTest.xml
          buildtype: ${{ inputs.buildtype }}
          pdbPath: dndTests.pdb

  rx-fcp:
    runs-on: [cart-bj, ut, self-hosted, Windows]
    timeout-minutes: 20
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Run Case
        uses: ./.github/actions/testframework/runcase
        with:
          productWorkflow: $PRODUCTWORKFLOW
          runNumber: $RUNNUMBER
          runAttempt: $RUNATTEMPT
          productWorkflowJob: $PRODUCTWORKFLOWJOB
          artifactoryBaseUrl: ${{ vars.ARTIFACTORY_GITHUB_DELIVERABLES_URL }}
          artifactoryApiToken: ${{ secrets.ARTIFACTORY_GITHUB_DELIVERABLES_API_TOKEN }}
          reportDbUri: ${{ secrets.VDUB_REPORT_DB_W }}
          reportDbName: ${{ secrets.VDUB_REPORT_DB_NAME }}
          reportDbCollection: ${{ secrets.VDUB_REPORT_COL_NAME }}
          testFilePaths: tests/horizonrxut.zip,tests/win64/fcpServerUnitTest.zip
          testCommand: ./horizonrxut.exe fcpServerUnitTest.dll --report=unitTest.xml
          buildtype: ${{ inputs.buildtype }}
          pdbPath: fcpServerUnitTest.pdb

  rx-screen-capture:
    runs-on: [cart-bj, ut, self-hosted, Windows]
    timeout-minutes: 20
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Run Case
        uses: ./.github/actions/testframework/runcase
        with:
          productWorkflow: $PRODUCTWORKFLOW
          runNumber: $RUNNUMBER
          runAttempt: $RUNATTEMPT
          productWorkflowJob: $PRODUCTWORKFLOWJOB
          artifactoryBaseUrl: ${{ vars.ARTIFACTORY_GITHUB_DELIVERABLES_URL }}
          artifactoryApiToken: ${{ secrets.ARTIFACTORY_GITHUB_DELIVERABLES_API_TOKEN }}
          reportDbUri: ${{ secrets.VDUB_REPORT_DB_W }}
          reportDbName: ${{ secrets.VDUB_REPORT_DB_NAME }}
          reportDbCollection: ${{ secrets.VDUB_REPORT_COL_NAME }}
          testFilePaths: tests/horizonrxut.zip,tests/win64/screenCaptureServerRdeUt.zip
          testCommand: ./horizonrxut.exe --modules=./screenCaptureServerUt.dll --type=run_tests --report=unitTest.xml
          buildtype: ${{ inputs.buildtype }}
          pdbPath: screenCaptureServerUt.pdb

  rx-tsdr:
    # TODO: re-enable when test are fixed
    if: false
    runs-on: [cart-bj, ut, self-hosted, Windows]
    timeout-minutes: 20
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Run Case
        uses: ./.github/actions/testframework/runcase
        with:
          productWorkflow: $PRODUCTWORKFLOW
          runNumber: $RUNNUMBER
          runAttempt: $RUNATTEMPT
          productWorkflowJob: $PRODUCTWORKFLOWJOB
          artifactoryBaseUrl: ${{ vars.ARTIFACTORY_GITHUB_DELIVERABLES_URL }}
          artifactoryApiToken: ${{ secrets.ARTIFACTORY_GITHUB_DELIVERABLES_API_TOKEN }}
          reportDbUri: ${{ secrets.VDUB_REPORT_DB_W }}
          reportDbName: ${{ secrets.VDUB_REPORT_DB_NAME }}
          reportDbCollection: ${{ secrets.VDUB_REPORT_COL_NAME }}
          testFilePaths: tests/horizonrxut.zip,tests/win64/tsdrAgentUnitTest.zip
          testCommand: ./horizonrxut.exe --modules=./tsdrServerUnitTest.dll --type=run_tests --report=unitTest.xml
          buildtype: ${{ inputs.buildtype }}
          pdbPath: tsdrServerUnitTest.pdb

  vd-audio:
    runs-on: [cart-bj, ut, self-hosted, Windows]
    timeout-minutes: 20
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Run Case
        uses: ./.github/actions/testframework/runcase
        with:
          productWorkflow: $PRODUCTWORKFLOW
          runNumber: $RUNNUMBER
          runAttempt: $RUNATTEMPT
          productWorkflowJob: $PRODUCTWORKFLOWJOB
          artifactoryBaseUrl: ${{ vars.ARTIFACTORY_GITHUB_DELIVERABLES_URL }}
          artifactoryApiToken: ${{ secrets.ARTIFACTORY_GITHUB_DELIVERABLES_API_TOKEN }}
          reportDbUri: ${{ secrets.VDUB_REPORT_DB_W }}
          reportDbName: ${{ secrets.VDUB_REPORT_DB_NAME }}
          reportDbCollection: ${{ secrets.VDUB_REPORT_COL_NAME }}
          testFilePaths: tests/horizonrxut.zip,tests/hznaudioendpointUnitTest-x64.zip
          testCommand: ./horizonrxut.exe --modules=./hznaudioendpointUnitTest.dll --type=run_tests --report=unitTest.xml
          #deployCommand: mkdir C:/ProgramData/Vmware/VDM/logs & REG ADD \"HKLM\\SYSTEM\\CurrentControlSet\\Control\\Terminal Server\\WinStations\\VMware-RDS\" /f /v AudioEndpointLogEnable /t REG_DWORD /d 0x1 & REG ADD \"HKLM\\SYSTEM\\CurrentControlSet\\Control\\Terminal Server\\WinStations\\VMware-RDS\" /f /v AudioEndpointLogLevel /t REG_SZ /d \"verbose\"
          buildtype: ${{ inputs.buildtype }}
          # this ut will if we enable cc. Need to enable it when it is fixed.
          enablecc: 'false'
          pdbPath: hznaudioendpointUnitTest.pdb

  vd-fido2:
    runs-on: [cart-bj, ut, self-hosted, Windows]
    timeout-minutes: 20
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Run Case
        uses: ./.github/actions/testframework/runcase
        with:
          productWorkflow: $PRODUCTWORKFLOW
          runNumber: $RUNNUMBER
          runAttempt: $RUNATTEMPT
          productWorkflowJob: $PRODUCTWORKFLOWJOB
          artifactoryBaseUrl: ${{ vars.ARTIFACTORY_GITHUB_DELIVERABLES_URL }}
          artifactoryApiToken: ${{ secrets.ARTIFACTORY_GITHUB_DELIVERABLES_API_TOKEN }}
          reportDbUri: ${{ secrets.VDUB_REPORT_DB_W }}
          reportDbName: ${{ secrets.VDUB_REPORT_DB_NAME }}
          reportDbCollection: ${{ secrets.VDUB_REPORT_COL_NAME }}
          testFilePaths: tests/horizonrxut.zip,tests/hzwebauthnUnitTest-x64.zip
          testCommand: ./horizonrxut.exe --modules=./hzwebauthnUnitTest.dll --type=run_tests --report=unitTest.xml
          buildtype: ${{ inputs.buildtype }}
          pdbPath: hzwebauthnUnitTest.pdb

  vd-usb:
    runs-on: [cart-bj, ut, self-hosted, Windows]
    timeout-minutes: 20
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Run Case
        uses: ./.github/actions/testframework/runcase
        with:
          productWorkflow: $PRODUCTWORKFLOW
          runNumber: $RUNNUMBER
          runAttempt: $RUNATTEMPT
          productWorkflowJob: $PRODUCTWORKFLOWJOB
          artifactoryBaseUrl: ${{ vars.ARTIFACTORY_GITHUB_DELIVERABLES_URL }}
          artifactoryApiToken: ${{ secrets.ARTIFACTORY_GITHUB_DELIVERABLES_API_TOKEN }}
          reportDbUri: ${{ secrets.VDUB_REPORT_DB_W }}
          reportDbName: ${{ secrets.VDUB_REPORT_DB_NAME }}
          reportDbCollection: ${{ secrets.VDUB_REPORT_COL_NAME }}
          testFilePaths: tests/horizonrxut.zip,tests/usbUnitTestServer.zip
          testCommand: ./horizonrxut.exe --modules=./usbWsUsbStorUnitTest.dll,./usbWsVhubCommonTest.dll,./usbWsVhubUnitTest.dll,./usbWsVdpVhubUnitTest.dll,./usbVhublibUnitTest.dll,./usbRedirectionServerUnitTest.dll --type=run_tests --report=unitTest.xml
          buildtype: ${{ inputs.buildtype }}
          # TODO: the cc setup script only accepts 1 pdb path right now. If this job
          # starts having issues with the cc report including lines not in source code,
          # this needs to be fixed
          pdbPath: usbWsUsbStorUnitTest.pdb

  winHttp:
    if: ${{ inputs.buildtype == 'obj' }}
    runs-on: [self-hosted, Windows, mosquitto]
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Run Case
        uses: ./.github/actions/testframework/runcase
        with:
          productWorkflow: $PRODUCTWORKFLOW
          runNumber: $RUNNUMBER
          runAttempt: $RUNATTEMPT
          productWorkflowJob: $PRODUCTWORKFLOWJOB
          artifactoryBaseUrl: ${{ vars.ARTIFACTORY_GITHUB_DELIVERABLES_URL }}
          artifactoryApiToken: ${{ secrets.ARTIFACTORY_GITHUB_DELIVERABLES_API_TOKEN }}
          reportDbUri: ${{ secrets.VDUB_REPORT_DB_W }}
          reportDbName: ${{ secrets.VDUB_REPORT_DB_NAME }}
          reportDbCollection: ${{ secrets.VDUB_REPORT_COL_NAME }}
          testFilePaths: tests/win64/CoreTest.zip
          testCommand: winHttpTests.exe --gtest_output=xml:winHttpTest.xml
          buildtype: ${{ inputs.buildtype }}
          modules: winHttpTests.exe
          pdbPath: winHttpTests.pdb

  wsnm_mqtt:
    if: ${{ inputs.buildtype == 'obj' }}
    runs-on: [self-hosted, Windows, mosquitto]
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Run Case
        uses: ./.github/actions/testframework/runcase
        with:
          productWorkflow: $PRODUCTWORKFLOW
          runNumber: $RUNNUMBER
          runAttempt: $RUNATTEMPT
          productWorkflowJob: $PRODUCTWORKFLOWJOB
          artifactoryBaseUrl: ${{ vars.ARTIFACTORY_GITHUB_DELIVERABLES_URL }}
          artifactoryApiToken: ${{ secrets.ARTIFACTORY_GITHUB_DELIVERABLES_API_TOKEN }}
          reportDbUri: ${{ secrets.VDUB_REPORT_DB_W }}
          reportDbName: ${{ secrets.VDUB_REPORT_DB_NAME }}
          reportDbCollection: ${{ secrets.VDUB_REPORT_COL_NAME }}
          testFilePaths: tests/win64/CoreTest.zip
          testCommand: wsnm_mqttTest.exe --gtest_output=xml:mqttTest.xml
          buildtype: ${{ inputs.buildtype }}
          modules: wsnm_mqttTest.exe
          pdbPath: wsnm_mqttTest.pdb

  UT-Agent-Core:
    if: ${{ inputs.buildtype == 'obj' }}
    runs-on:
      - self-hosted
      - windows
      - vdub-ut
    strategy:
      fail-fast: false
      matrix:
        testType: [wsnm_desktop, wsnm_common, wssm_desktop, ws_applaunchmgr, ws_updatemgr]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Run Case
        uses: ./.github/actions/testframework/runcase
        with:
          productWorkflow: $PRODUCTWORKFLOW
          runNumber: $RUNNUMBER
          runAttempt: $RUNATTEMPT
          productWorkflowJob: $PRODUCTWORKFLOWJOB
          artifactoryBaseUrl: ${{ vars.ARTIFACTORY_GITHUB_DELIVERABLES_URL }}
          artifactoryApiToken: ${{ secrets.ARTIFACTORY_GITHUB_DELIVERABLES_API_TOKEN }}
          reportDbUri: ${{ secrets.VDUB_REPORT_DB_W }}
          reportDbName: ${{ secrets.VDUB_REPORT_DB_NAME }}
          reportDbCollection: ${{ secrets.VDUB_REPORT_COL_NAME }}
          testFilePaths: tests/win64/CoreTest.zip,tests/horizonrxut.zip
          testCommand: ./${{ matrix.testType }}Test.exe --gtest_output=xml:unitTest.xml
          buildtype: ${{ inputs.buildtype }}
          pdbPath: ${{ matrix.testType }}Test.pdb

  UT-Agent-IC:
    runs-on:
      - self-hosted
      - windows
      - vdub-ut
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Run Case
        uses: ./.github/actions/testframework/runcase
        with:
          productWorkflow: $PRODUCTWORKFLOW
          runNumber: $RUNNUMBER
          runAttempt: $RUNATTEMPT
          productWorkflowJob: $PRODUCTWORKFLOWJOB
          artifactoryBaseUrl: ${{ vars.ARTIFACTORY_GITHUB_DELIVERABLES_URL }}
          artifactoryApiToken: ${{ secrets.ARTIFACTORY_GITHUB_DELIVERABLES_API_TOKEN }}
          reportDbUri: ${{ secrets.VDUB_REPORT_DB_W }}
          reportDbName: ${{ secrets.VDUB_REPORT_DB_NAME }}
          reportDbCollection: ${{ secrets.VDUB_REPORT_COL_NAME }}
          testFilePaths: tests/win64/omnissa-ic-ga-unittest.*,tests/win64/CoreTest.zip,tests/horizonrxut.zip
          deployCommand: cp "${{ github.workspace }}/tests/win64/omnissa-ic-ga-unittest."* "${{ github.workspace }}"
          testCommand: ./omnissa-ic-ga-unittest.exe /GTest --gtest_output=xml:unitTest.xml
          buildtype: ${{ inputs.buildtype }}
          pdbPath: omnissa-ic-ga-unittest.pdb

  UT-Agent-HzaPrep:
    if: ${{ inputs.buildtype == 'obj' }}
    runs-on:
      - self-hosted
      - windows
      - vdub-ut
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Run Case
        uses: ./.github/actions/testframework/runcase
        with:
          productWorkflow: $PRODUCTWORKFLOW
          runNumber: $RUNNUMBER
          runAttempt: $RUNATTEMPT
          productWorkflowJob: $PRODUCTWORKFLOWJOB
          artifactoryBaseUrl: ${{ vars.ARTIFACTORY_GITHUB_DELIVERABLES_URL }}
          artifactoryApiToken: ${{ secrets.ARTIFACTORY_GITHUB_DELIVERABLES_API_TOKEN }}
          reportDbUri: ${{ secrets.VDUB_REPORT_DB_W }}
          reportDbName: ${{ secrets.VDUB_REPORT_DB_NAME }}
          reportDbCollection: ${{ secrets.VDUB_REPORT_COL_NAME }}
          testFilePaths: tests/win64/hzaprepTest.zip
          testCommand: hzaprepTest.exe --gtest_output=xml:unitTest.xml
          buildtype: ${{ inputs.buildtype }}
          enablecc: 'true'
          pdbPath: hzaprepTest.pdb

  UT-Agent-Html5mmrServer:
    runs-on:
      - self-hosted
      - windows
      - vdub-ut
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Run Case
        uses: ./.github/actions/testframework/runcase
        with:
          productWorkflow: $PRODUCTWORKFLOW
          runNumber: $RUNNUMBER
          runAttempt: $RUNATTEMPT
          productWorkflowJob: $PRODUCTWORKFLOWJOB
          artifactoryBaseUrl: ${{ vars.ARTIFACTORY_GITHUB_DELIVERABLES_URL }}
          artifactoryApiToken: ${{ secrets.ARTIFACTORY_GITHUB_DELIVERABLES_API_TOKEN }}
          reportDbUri: ${{ secrets.VDUB_REPORT_DB_W }}
          reportDbName: ${{ secrets.VDUB_REPORT_DB_NAME }}
          reportDbCollection: ${{ secrets.VDUB_REPORT_COL_NAME }}
          testFilePaths: tests/win64/html5mmrServerTest.zip,tests/horizonrxut.zip
          testCommand: ./html5mmrServerTest.exe --gtest_output=xml:unitTest.xml
          buildtype: ${{ inputs.buildtype }}
          pdbPath: html5mmrServerTest.pdb
          modules: html5mmrServerTest.exe

  UT-Auth-Whfb:
    runs-on:
      - self-hosted
      - windows
      - vdub-ut
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Run Case
        uses: ./.github/actions/testframework/runcase
        with:
          productWorkflow: $PRODUCTWORKFLOW
          runNumber: $RUNNUMBER
          runAttempt: $RUNATTEMPT
          productWorkflowJob: $PRODUCTWORKFLOWJOB
          artifactoryBaseUrl: ${{ vars.ARTIFACTORY_GITHUB_DELIVERABLES_URL }}
          artifactoryApiToken: ${{ secrets.ARTIFACTORY_GITHUB_DELIVERABLES_API_TOKEN }}
          reportDbUri: ${{ secrets.VDUB_REPORT_DB_W }}
          reportDbName: ${{ secrets.VDUB_REPORT_DB_NAME }}
          reportDbCollection: ${{ secrets.VDUB_REPORT_COL_NAME }}
          testFilePaths: tests/win64/whfbUnitTest.zip
          testCommand: ./whfbUnitTest.exe --gtest_output=xml:unitTest.xml
          buildtype: ${{ inputs.buildtype }}
          pdbPath: whfbUnitTest.pdb

  UT-Updatetool:
    runs-on:
      - self-hosted
      - windows
      - vdub-ut
    strategy:
      fail-fast: false
      matrix:
        testType: [cdsClient]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Run Case
        uses: ./.github/actions/testframework/runcase
        with:
          productWorkflow: $PRODUCTWORKFLOW
          runNumber: $RUNNUMBER
          runAttempt: $RUNATTEMPT
          productWorkflowJob: $PRODUCTWORKFLOWJOB
          artifactoryBaseUrl: ${{ vars.ARTIFACTORY_GITHUB_DELIVERABLES_URL }}
          artifactoryApiToken: ${{ secrets.ARTIFACTORY_GITHUB_DELIVERABLES_API_TOKEN }}
          reportDbUri: ${{ secrets.VDUB_REPORT_DB_W }}
          reportDbName: ${{ secrets.VDUB_REPORT_DB_NAME }}
          reportDbCollection: ${{ secrets.VDUB_REPORT_COL_NAME }}
          testFilePaths: tests/win64/UpdateToolTest.zip
          testCommand: ./${{ matrix.testType }}Test.exe --gtest_output=xml:unitTest.xml
          buildtype: ${{ inputs.buildtype }}
          pdbPath: ${{ matrix.testType }}Test.pdb
