{"version": "0.5", "requires": ["zlib/1.3.1#070fdb998838959a825710509689296b%1733494270.942", "theora/1.1.1#80a4cffeef63baec0828b3e9012f0e6f%1715834616.288", "sse2neon/1.7.0#1ef6b4b578e8d52e9fd2f047f574eab5%1729949215.449", "speexdsp/1.2rc3#39d2a177a6d38971a8b0c5ad3189ba70%1712027434.492", "speex/1.2rc2#fe17dcfabdac9e92c508805b71d2ffa1%1712027433.216", "snappy/1.1.7#72841864bdd1c7f79ff4ba47980761e7%1713251799.36", "protobuf-c/1.5.0#b35075497328319d2db737fe4621cba0%1721620596.84", "protobuf/3.25.3#f2ffb086cd9e67ad6d91ca9114c8abfa%1748257020.598", "pixman/0.44.2#c75d2e6873c6826eefb7340f12a0b838%1745586475.706", "pcre2/10.42#0aa2ba6a3446486c65e60346d5a9fa55%1711692874.277", "opus/1.4#ff4e5a955840682b271bb7fe2c29ace6%1713283597.769", "openssl_fips_validated/3.0.9#2af1ca5cc524fa3775353719b750e440%1740295376.965", "openssl/3.0.16#09fdc1e695600e5f11185e9d5aee6aae%1740295373.964", "ogg/1.3.2#8bd0bd36c461da588578ae7715f0bc0f%1713283595.762", "ocmock/3.9.1#16d359b99e22084a30551dabd8e2ea08%1716347953.275", "nv-codec-headers/********#54305e64fb6b661e10d2f345a170369f%1711572184.0", "nlohmann_json/3.11.2#66de7b3253d039ae75ae40a0b2050bcd%1709206230.076", "lldpd/1.0.17#aafe83409bd4e0ffa1dd34c98d6299a5%1733859972.507", "libyuv/1882#2f016ac1d9f331bea900cf5a04aac7e5%1741363798.278", "libx264/164.20220124#c4bbd5df1c0dac55c7757e1ae21ad58d%1740649913.111", "libsigcpp/3.6.0#d3c5236342c25de8ca730fe167aa499d%1711602963.241", "libpng/1.6.48#4af49de4b86d319c4c64d0be65e71774%1747755034.838", "libjpeg-turbo/3.0.1#0c3ec26496747234ae68a3055636c006%1721007067.804", "libgettext/0.22#5ff9077ef9609e8eb31f9026256e82a0%1725475554.379", "libfido2/1.13.0#0d961b4216413802cf09186a50bbe98c%1730899790.534", "libcurl/8.10.0#fbfed1ad02dcc0432775363377a10303%1744204106.236", "libcbor/0.11.0#3059498263c5a08abe667e0464e6c588%1709048654.14", "libbacktrace/cci.20210118#f5d18dee9942ec36058b965afa867e7b%1715834615.941", "jsoncpp/1.9.5#925804cc02bddd48441050a0344eed30%1708430492.248", "implot/0.14#eb222e31487146f526f4a76387cbf304%1713874342.742", "imgui/1.88#4ea75ac5573f158b1f89628a13e02e98%1713776294.477", "icu/74.2#df0a1df80ef1e8db3db270f57414918b%1711025915.669", "gtest/1.17.0#b81d3ff53b1af0ef3d23373a0a923c98%1748940935.811", "glibmm/2.78.1#12cc7f69b7812866049cfc02f80a05ac%1748430560.143", "glib/2.84.1#2730f913e60bcdbb23eeece7123bdc38%1747979467.326", "fmt/10.2.1#00dc4de2f60a8abb3a0f0b08d30ca771%1720109961.309", "ffmpeg/7.0.1#fd3d0bc80ff7f7f9221177df0186875c%1730899784.511", "fastlz/0.1.0#610b82f86ce33210a6cc592b826c7363%1715033319.566", "expat/2.7.1#d358f611a0bc8eb21a0fb7ff0db61241%1748452935.812", "cunit/2.1-3#c594461bd1e131d54b2713e0064ae72e%1722874945.196", "cairo/1.18.0#364e5da5c40f58765b594cb7ad4bda9b%1748430558.937", "boost/1.86.0#f70e151226aa0dbd5b1c5b1fd886823d%1730708381.876", "abseil/20240116.2#f625ffef28cb41b3009c69dda17093c8%1748257014.465"], "build_requires": ["zlib/1.3.1#070fdb998838959a825710509689296b%1733494270.942", "xcode/16.2#fb8b2a898ee2fdcc24e619f96fc19e8b%1745424608.976", "xapian-core/1.4.19#a44dbb1d01de1eb9a9f95f2fd25427c5%1721007068.813", "util_linux/2.39.3#c69f594b4712cfa4e05995e37c2268c5%1740648977.648", "protobuf-c/1.5.0#b35075497328319d2db737fe4621cba0%1721620596.84", "protobuf/3.25.3#f2ffb086cd9e67ad6d91ca9114c8abfa%1748257020.598", "pkgconf/2.1.0#0177a5313f23035f337f78f6cfdcdcba%1719990004.814", "perl/5.38.0#e5f3f95ef101e56de5a37edfcdd472ee%1726298679.552", "ninja/1.11.1#f73fb14f87f6343107224de5c8734c0d%1704462561.271", "nasm/2.16.01#32bb0d9a9c2f9a786bfc501ab9589eb4%1745586475.269", "meson/1.4.1#723365dfd8e4ec2decc7ce18f83cac69%1747979472.412", "meson/1.3.1#ba55deb2839bcf58e99bee7eb5e7d496%1747979472.099", "make/4.4.1#572a6685bf5343b63f81a1e0c57c7b75%1745586474.861", "m4/1.4.19#265fb80b0415aef2ff3b0177569e7629%1711337655.59", "libtool/2.4.7#9bea50bfb7323892ff95c79fe29d47c7%1711678421.691", "libiconv/1.17#055c1d6f948ec3843b03b945360c6b3e%1718780206.714", "icu/74.2#df0a1df80ef1e8db3db270f57414918b%1711025915.669", "gnu-config/cci.20210814#7778c4be47471550efcfab6245e8fcc4%1720447612.743", "gettext/0.22.5#13aeb9d05f4fa4a7d916ed95164dc781%1744999949.049", "flex/2.6.4#9e15d97730c7c05d7673701644956217%1706781133.048", "doxygen/1.9.4#461b479e74052d4a5bb41dcf63b8cb5d%1708396102.071", "dotnet_sdk/8.0.100#d84db6eb3e578831b1ea595f48657c9d%1732161177.095", "cmake/3.27.1#ff5aa246eafce398974ebaf4c0a6ae95%1712665136.894", "bison/3.7.6#4b33a752621345789d5a7bd0b7e3969d%1745586472.911", "b2/5.2.1#559a98b1c2599d6d8c8e211e54c7cf9e%1730708381.119", "automake/1.16.5#61211678c2d683d8a58f506ddcf63285%1711647072.926", "autoconf/2.71#e65e58d3eb5c70a6cc7ea1f1a22c2c41%1711347438.585", "abseil/20240116.2#f625ffef28cb41b3009c69dda17093c8%1748257014.465", "7zip/24.09#753051c21fbb8c1eafe0747959080f1a%1734348912.1"], "python_requires": [], "config_requires": []}