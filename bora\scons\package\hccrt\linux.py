# Copyright (c) Omnissa, LLC. All rights reserved.
# This product is protected by copyright and intellectual property laws in the
# United States and other countries as well as by international treaties.
# -- Omnissa Restricted

"""hclinux: Omnissa Horizon Client for Linux

SCons package file for the Linux Horizon Client.

Maintained by the Horizon client team:
   1. <EMAIL>
   2. Reviewboard groups: vdm-client
"""

import os
import vmware
import re
import SCons

from fnmatch import fnmatch
from math import ceil
from SCons.Script import SConscript, File, Dir

useNewGcc = vmware.LocalOpts.GetInt("USE_GCC", 0) >= 12
gccTarget = "cayman_esx_toolchain_gcc%s" % ("12" if useNewGcc else "9")

# These next two lines are needed to avoid copying too many files to Artifactory.
# TODO: Remove this once fewer files are copied under stagePath.
vmware.pkg.stagePath = os.path.join(vmware.BuildRoot(), "hclin", "stage")
vmware.pkg.stagePP = vmware.PathPrefixer(vmware.pkg.stagePath)


def nonrecursiveDirFilter(root, dirs):
    del dirs[:]


def reviseDebInstalledSize(target, source, env):
    installedSize = 0
    for root, _, files in os.walk(source[0].abspath):
        for f in files:
            installedSize += os.path.getsize(os.path.join(root, f))

    installedSize = ceil(installedSize / 1024)

    debCtrlFile = source[0].File("DEBIAN/control").abspath
    with open(debCtrlFile, "r") as f:
        data = f.read()
    if data is not None:
        data = data.replace("@INSTALLEDSIZE@", str(installedSize))
        with open(debCtrlFile, "w") as f:
            f.write(data)


def addDepsToZipSources(env, names, zipSources):
    """
    Add all dependencies which are specied by names and their pdb to zip
    """
    deps = []
    for name in names:
        dep = env[name]
        if isinstance(dep, list):
            deps += dep
        else:
            deps.append(dep)

    for dep in deps:
        depAbsPath = File(dep).abspath
        zipSources.append(os.path.split(depAbsPath))
        pdb = os.path.splitext(depAbsPath)[0] + ".pdb"
        if os.path.exists(pdb):
            zipSources.append(os.path.split(pdb))


def stageViewClientApiTest():
    """stageViewClientApiTest
    Stage the binaries for view client Api Test.
    """

    nodes = []
    targetName = "viewClientApiTest"
    vmware.PathPrefixer(vmware.pkg.stagePath) / targetName

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is None:
        return nodes

    host = "linux64"
    zipSources = []
    stageEnv.LoadTool("zip")
    stageEnv.LoadTool("zip-builder")
    stageEnv.LoadTool("libssl")

    libToolChainDir = os.path.join(vmware.GetGobuildComponent(gccTarget))
    zipSources.append(
        (libToolChainDir + "/usr/x86_64-vmk-linux-gnu/lib64", "libstdc++.so.6")
    )

    libPcoipClientDir = os.path.join(vmware.GetGobuildComponent("pcoip-soft-clients"))
    libPcoipClientSuffix = "-gcc12" if useNewGcc else ""
    libPcoipClientSubDir = "linux-x86_64" + libPcoipClientSuffix
    zipSources.append(
        (
            os.path.join(libPcoipClientDir, libPcoipClientSubDir, "client"),
            "libpcoip_client.so",
        )
    )

    for ssl_lib in stageEnv["OPENSSL_REDIST"]:
        zipSources.append(os.path.split(ssl_lib))

    zipSources.append(
        (
            vmware.DirAbsPath("#bora/apps/crtbora/componentTest/appRemoting/scripts"),
            "viewClientApiTest.py",
        )
    )

    x64ApiTestEnv = vmware.LookupEnv("viewClientApiTest", host)
    for item in x64ApiTestEnv["REDIST"]:
        for rule in ossFileWhitelist:
            if fnmatch(os.path.basename(item), rule):
                zipSources.append((os.path.dirname(item), os.path.basename(item)))
    for item in x64ApiTestEnv["GTEST_REDIST"]:
        zipSources.append((os.path.dirname(str(item)), os.path.basename(str(item))))

    deps = vmware.pkg.LookupDeliverableNodes(targetName, host)

    for node in deps:
        zipSources.append((node.dir.abspath, node.name))

    zipFile = File(os.path.join(publishDir, "tests", host, targetName + ".zip"))
    nodes += stageEnv.Zip(zipFile, zipSources)
    stageEnv.AddZipDeps(nodes)
    return nodes


def stageDctClientComponentTest():
    stageEnv = vmware.pkg.stageEnv
    stageEnv.LoadTool(
        [
            "zip",
            "zip-builder",
        ]
    )
    # Stage the binaries for DCT Component Test
    nodes = []

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(publishDir, "tests")
        extraZipSources = []
        hosts = ["linux64"]
        for host in hosts:
            # Add the DCT test cases
            boraDCTTestDir = vmware.Dir("#bora/apps/horizonDCT/componentTest")
            testCasesDir = boraDCTTestDir.Dir("testCases/linux")
            extraZipSources.append((testCasesDir.abspath, "."))

            # Add the test deployment config files
            deployConfigsDir = boraDCTTestDir.Dir("deploymentConfigs")
            extraZipSources.append((deployConfigsDir.abspath, "dct_client_linux.ini"))

            # Add DCT scripts
            clientDCTDir = vmware.Dir("#horizonclient/view/openClient/dct")
            extraZipSources.append((clientDCTDir.Dir("common").abspath, "."))
            extraZipSources.append((clientDCTDir.Dir("linux").abspath, "."))

            deliverableNodes = vmware.pkg.LookupDeliverableNodes(
                "rxDCTComponentTestClient", host
            )
            for node in deliverableNodes:
                extraZipSources.append((node.dir.abspath, node.name))

            zipFilePath = os.path.join(publishDir, host, "rxDCTComponentTestClient.zip")
            zipNode = stageEnv.Zip(vmware.File(zipFilePath), extraZipSources)
            if zipNode is not None:
                nodes += zipNode
    return nodes


def ArchiveDirectory(dirName, dstDir, parentDir, stageEnv):
    """Create the archive for a directory using tar.
    The function will first enter the parent directory 'parentDir' of the
    target directory 'dirName'. And then under the 'dstDir' directory, create
    an archive with the same name but with the suffix '.tar.gz'.
    """
    archiveFile = dstDir.File("%s.tar.gz" % dirName)
    return stageEnv.Command(
        archiveFile,
        parentDir.Dir(dirName),
        vmware.ActionWithDisplay(
            "$CD $PARENTDIR && $TAR -chzvf ${TARGET.abspath} $DIR",
            "Archive directory to %s.tar.gz" % dirName,
        ),
        PARENTDIR=parentDir.abspath,
        DIR=dirName,
    )


def DirLinkCopy(files, sourceDir, targetDir, mirrorDir, env):
    """Copy 'files' from within sourcedir to within targetdir

    - files should be a list of File nodes
    - sourceDir, targetDir and mirrorDir should be Dir nodes
    - mirrorDir (optional) will have the same content as targetDir
    """
    targets = []
    archives = []
    sDir = sourceDir.abspath
    for fNode in files:
        file = fNode.abspath
        commonPath = os.path.commonprefix([sDir, file])
        if len(commonPath) != len(sDir):
            raise vmware.ScriptError(
                "DirLinkCopy: file %s not under directory %s" % (file, sDir)
            )
        tFile = targetDir.abspath + file[len(sDir) :]
        tNode = env.LinkCopy(target=tFile, source=fNode)
        SCons.Script.Precious(tNode)
        targets += tNode
        if mirrorDir is not None:
            aFile = mirrorDir.abspath + file[len(sDir) :]
            aNode = env.LinkCopy(target=aFile, source=tFile)
            env.Depends(aNode, tNode)
            SCons.Script.Precious(aNode)
            archives += aNode
    return targets, archives


def CopyFilesInList(fileList, cpInfo, needStrip, env):
    for item in fileList:
        src = item[0]
        dst = item[1]
        runPath = None
        if len(item) == 3:
            runPath = item[2]
        if isinstance(src, str):
            srcPath = src
            srcFile = stageEnv.File(src)
        else:
            srcPath = src.abspath
            srcFile = src
        copier = env.FileCopyStrip if needStrip(srcPath) else env.FileCopy
        for _, cpInfoNode in cpInfo.items():
            node = copier(cpInfoNode["dstDir"].File(dst), srcFile)
            if runPath is not None:
                env.AddPostAction(node, env.SetRunPathAction(runPath))
            cpInfoNode["cpNode"] += node


def GetGobuildComponentPath(component, env, missingOk=False, substitutions=None):
    root = vmware.GetGobuildComponent(component)
    if missingOk:
        return os.path.join(root, substitutions)
    else:
        return os.path.join(root, vmware.CaymanProductDirectory(root, env))


def GetLibPackageInfo(libPackagePathRules, env):
    libPackageInfo = set()
    for pathRule, runPath in libPackagePathRules:
        for libLinkPath in env["REDIST"]:
            libLinkName = os.path.basename(libLinkPath)
            if fnmatch(libLinkName, os.path.basename(pathRule)):
                libPackageInfo.add(
                    (
                        libLinkPath,
                        os.path.join(os.path.dirname(pathRule), libLinkName),
                        runPath,
                    )
                )
    return libPackageInfo


def GetFileForPattern(path, pattern):
    """Get the only file which matches the pattern under the path but without
    recursion. For none or more than one, raise exception.
    """
    _, _, files = next(os.walk(path))
    result = [f for f in files if (re.match(pattern, f) is not None)]
    if len(result) == 1:
        return result[0]
    else:
        raise Exception(
            "Can't get the file for pattern '%s' because "
            "result is %s under path %s" % (pattern, result, path)
        )


ossFileWhitelist = [
    "libatk-1.0.so.*",
    "libatkmm-*.so.1",
    "libcairomm-*.so.1",
    "libffi.so*",
    "libgdk*.so.0",
    "libgdkmm*.so.1",
    "libgio-*.so*",
    "libgiomm-*.so.1",
    "libglib-*.so*",
    "libglibmm-*.so.1",
    "libglibmm_generate_extra_defs-*.so.1",
    "libgobject-*.so*",
    "libgtk*.so.0",
    "libgtkmm*.so.1",
    "libharfbuzz.so*",
    "libpango-1.0.so.0",
    "libpangocairo-1.0.so.0",
    "libpangoft2-1.0.so.0",
    "libpangomm-*.so.1",
    "libpng16.so.16",
    "libz.so.1",
    "libsigc*.so.0",
]

utOssFilelist = [
    "libatkmm-*.so.1",
    "libcurl.so.*",
    "libgdk*.so.0",
    "libgdkmm*.so.1",
    "libgio-*.so*",
    "libgiomm-*.so.1",
    "libglib-*.so*",
    "libglibmm-*.so.1",
    "libglibmm_generate_extra_defs-*.so.1",
    "libgobject-*.so*",
    "libgtk*.so.0",
    "libgtkmm*.so.1",
    "libharfbuzz.so*",
    "libpango-1.0.so.0",
    "libpangocairo-1.0.so.0",
    "libpangoft2-1.0.so.0",
    "libpangomm-*.so.1",
    "libpng16.so.16",
]


def stageCrtboraTestBinaries():
    """stageCrtboraTestBinaries
    Stage the binaries for crtbora API Test.
    """
    ossFileWhitelist = [
        "libatkmm-*.so.1",
        "libcairomm-*.so.1",
        "libffi.so*",
        "libgdkmm*.so.1",
        "libgiomm-*.so.1",
        "libglibmm-*.so.1",
        "libglibmm_generate_extra_defs-*.so.1",
        "libgtkmm*.so.1",
        "libharfbuzz.so*",
        "libpangomm-*.so.1",
        "libpcre.so.1",
        "libpng16.so.16",
        "libz.so.1",
        "libsigc*.so.0",
    ]
    nodes = []
    publishDir = vmware.ReleasePackagesDir()
    if publishDir is None:
        return nodes

    targetName = "crtboraApiTest"
    host = "linux64"
    zipSources = []
    stageEnv.LoadTool("zip")
    stageEnv.LoadTool("zip-builder")
    stageEnv.LoadTool("libssl")

    for ssl_lib in stageEnv["OPENSSL_REDIST"]:
        zipSources.append(os.path.split(ssl_lib))

    libToolChainDir = os.path.join(vmware.GetGobuildComponent(gccTarget))
    zipSources.append(
        (libToolChainDir + "/usr/x86_64-vmk-linux-gnu/lib64", "libstdc++.so.6")
    )

    zipSources.append(
        (
            vmware.GetConanComponent(stageEnv, "libffi").env["LIBPATH"][0],
            "libffi.so",
        )
    )

    libzDir = os.path.join(vmware.GetConanComponent(stageEnv, "zlib").env["LIBPATH"][0])
    zipSources.append((libzDir, "libz.so.1"))

    x64ApiTestEnv = vmware.LookupEnv("crtboraApiTest", "linux64")
    for item in x64ApiTestEnv["REDIST"]:
        for rule in ossFileWhitelist:
            if fnmatch(os.path.basename(item), rule):
                zipSources.append((os.path.dirname(item), os.path.basename(item)))
    for item in x64ApiTestEnv["GTEST_REDIST"]:
        zipSources.append((os.path.dirname(str(item)), os.path.basename(str(item))))

    deps = vmware.pkg.LookupDeliverableNodes(targetName, host)
    deps += vmware.pkg.LookupDeliverableNodes("libcrtbora", host)
    deps += vmware.pkg.LookupDeliverableNodes("omnissabaselib", host)
    for node in deps:
        zipSources.append((node.dir.abspath, node.name))

    appRemotingDir = vmware.DirAbsPath("#bora/apps/crtbora/componentTest/appRemoting")
    zipSources.append((appRemotingDir + "/scripts", "crtboraApiTest.py"))
    zipSources.append((appRemotingDir, "data"))

    zipFile = File(os.path.join(publishDir, "tests", host, targetName + ".zip"))
    nodes += stageEnv.Zip(zipFile, zipSources)
    stageEnv.AddZipDeps(nodes)

    vmware.Alias("crtboraApiTest-stage", nodes)

    return nodes


def stageRdeClientTest():
    """stageRdeClientTest
    Stage the bundle for linux rdeClient Test.
    """
    ossFileWhitelist = [
        "libatkmm-*.so.1",
        "libcairomm-*.so.1",
        "libffi.so*",
        "libgdkmm*.so.1",
        "libgiomm-*.so.1",
        "libglibmm*.so.1",
        "libgtkmm*.so.1",
        "libharfbuzz.so*",
        "libpangomm-*.so.1",
        "libpng16.so.16",
        "libz.so.1",
        "libsigc*.so.0",
    ]
    nodes = []
    publishDir = vmware.ReleasePackagesDir()
    if publishDir is None:
        return nodes

    targetName = "rdeClientTest"
    host = "linux64"
    zipSources = []
    stageEnv.LoadTool(["zip", "zip-builder"])
    rdeClientTestEnv = vmware.LookupEnv(targetName, host)
    for item in rdeClientTestEnv["REDIST"]:
        for rule in ossFileWhitelist:
            if fnmatch(os.path.basename(item), rule):
                zipSources.append((os.path.dirname(item), os.path.basename(item)))
    for item in rdeClientTestEnv["GTEST_REDIST"]:
        zipSources.append((os.path.dirname(str(item)), os.path.basename(str(item))))

    deps = vmware.pkg.LookupDeliverableNodes(targetName, host)
    for node in deps:
        zipSources.append((node.dir.abspath, node.name))

    libstdcpp_dir = os.path.join(
        vmware.GetGobuildComponent(stageEnv.GetGCCComponentName()),
        "usr",
        "x86_64-vmk-linux-gnu",
        "lib64",
    )
    zipSources.append((libstdcpp_dir, "libstdc++.so.6"))

    appRemotingDir = vmware.DirAbsPath("#bora/apps/crtbora/componentTest/appRemoting")
    zipSources.append((appRemotingDir + "/scripts", "crtboraApiTest.py"))
    data = vmware.DirAbsPath("#bora/apps/rde/rdeSvc/tests/componentTest/rdeClientTest")
    zipSources.append((data, "data"))

    zipFile = File(os.path.join(publishDir, "tests", host, targetName + ".zip"))
    nodes += stageEnv.Zip(zipFile, zipSources)
    return nodes


def stageViewClient():
    """
    Stage the binaries for horizon-protocol.
    """
    stagePP = vmware.PathPrefixer(vmware.pkg.stagePath)
    viewClientPP = stagePP / "viewClient"
    nodes = []
    targetName = ["viewClient"]
    publishDir = vmware.ReleasePackagesDir()

    stageEnv.LoadTool(["libssl", "ffmpeg"])

    for host, arch in [("linux64", "x64"), ("armhf-linux", "armhf")]:
        zipSources = []
        rmksEnv = vmware.LookupEnv("viewClient", host)
        for item in rmksEnv["REDIST"]:
            for rule in ossFileWhitelist:
                if fnmatch(os.path.basename(item), rule):
                    nodes += stageEnv.LinkCopy(
                        File(viewClientPP / arch / os.path.basename(item)),
                        os.path.abspath(item),
                    )
                    zipSources.append(
                        (os.path.dirname(os.path.abspath(item)), os.path.basename(item))
                    )

        for ext in ["software", "vaapi", "vaapi2", "vaapi27", "vdpau"]:
            avdeps = [
                stageEnv[f"FFMPEG_{ext.upper()}_AVCODEC_REDIST"],
                stageEnv[f"FFMPEG_{ext.upper()}_AVUTIL_REDIST"],
                stageEnv[f"FFMPEG_{ext.upper()}_AVFORMAT_REDIST"],
            ]
            for dep in avdeps:
                nodes += stageEnv.LinkCopy(
                    File(viewClientPP / arch / ext / os.path.basename(dep)),
                    os.path.abspath(dep),
                )
                zipSources.append(
                    (
                        vmware.DirAbsPath(viewClientPP / arch),
                        os.path.join(ext, os.path.basename(dep)),
                    )
                )

        deps = rmksEnv["OPENSSL_FIPS_REDIST"] + [
            vmware.pkg.LookupDeliverableNodes("omnissabaselib", host)[0].abspath
        ]

        if host in ["linux64"]:
            deps.append(rmksEnv["LIBX264_REDIST"])

        for dep in deps:
            nodes += stageEnv.LinkCopy(
                File(viewClientPP / arch / os.path.basename(dep)), os.path.abspath(dep)
            )
            zipSources.append(
                (os.path.dirname(os.path.abspath(dep)), os.path.basename(dep))
            )

        gccdep = (rmksEnv.GetGCCRedist("libstdc++.so.6"),)
        for dep in gccdep:
            nodes += stageEnv.LinkCopy(
                File(viewClientPP / arch / "gcc" / os.path.basename(dep)),
                os.path.abspath(dep),
            )
            zipSources.append(
                (
                    vmware.DirAbsPath(viewClientPP / arch),
                    os.path.join("gcc", os.path.basename(dep)),
                )
            )

        for node in vmware.LookupNode("viewClient", host):
            nodes += stageEnv.LinkCopy(File(viewClientPP / arch / node.name), node)

        if publishDir is not None:
            deliverableNodesNames = ["viewClient"]

            zipFileName = "viewClient-%s-%s.zip" % (vmware.BuildNumber(), host)
            zipFilePath = File(os.path.join(publishDir, "viewClient", zipFileName))
            zipNode = vmware.pkg.CreateZipStageNode(
                stageEnv,
                targetName,
                host,
                deliverableNodesNames,
                zipSources,
                zipFilePath,
            )
            if zipNode is not None:
                nodes += zipNode
                stageEnv.AddZipDeps(zipNode)
                stageEnv.NoCacheIfBuildNumberInfoOverridden(zipNode)

    return nodes


def stageSDKCommon(
    sdkName,
    clientArch,
    includePaths,
    sampleCopyNodes,
    sdkLibSrc,
    sdkLibDst,
    sdkLibNode=None,
    includeFilefilter=None,
):
    """Create Archive node for Client SDK or Session SDK, includes:
    - Copy sample to SDK publish folder 'sample'
    - Copy required libraries and binaries to SDK publish folder 'lib'
    - Copy required headers to SDK publish folder 'include'
    """
    nodes = []
    clientSDKDir = stageDir.Dir(sdkName)
    sdkIncludeDst = clientSDKDir.Dir("include")
    clientSDKCopy = []

    for incPath in includePaths:
        sdkIncludeDir = stageEnv.Dir(incPath)
        if includeFilefilter:
            sdkIncludeSrc = vmware.EnumerateSourceDir(
                sdkIncludeDir, includeFilefilter, dirfilter=nonrecursiveDirFilter
            )
        else:
            sdkIncludeSrc = vmware.EnumerateSourceDir(
                sdkIncludeDir, dirfilter=nonrecursiveDirFilter
            )
        clientSDKCopy += vmware.DirCopy(
            sdkIncludeSrc, sdkIncludeDir, sdkIncludeDst, stageEnv
        )

    clientSDKCopy += sampleCopyNodes

    clientSDKLibSrc = stageEnv.File(sdkLibSrc)
    clientSDKLibDst = clientSDKDir.File(sdkLibDst)
    clientSDKNode = stageEnv.LinkCopy(clientSDKLibDst, clientSDKLibSrc)
    if sdkLibNode:
        stageEnv.Depends(clientSDKNode, vmware.LookupNode(sdkLibNode))
    clientSDKCopy += clientSDKNode

    if clientArch == "x64":
        clientSDKCopy += stageEnv.FileCopy(
            clientSDKDir.File("lib/libpcoip_client.so"),
            vmware.pkg.x64PcoipArchiveDir.File("usr/lib/libpcoip_client.so"),
        )

    if clientArch == "armhf":
        clientSDKCopy += stageEnv.FileCopy(
            clientSDKDir.File("lib/libpcoip_client.so"),
            vmware.pkg.armhfPcoipArchiveDir.File("usr/lib/libpcoip_client.so"),
        )
        clientSDKCopy += stageEnv.FileCopy(
            clientSDKDir.File("lib/libpcoip_client_neon.so"),
            vmware.pkg.armhfPcoipArchiveDir.File("usr/lib/libpcoip_client_neon.so"),
        )

    # ClientSDK archive shares most binaries with PCoIP archive.
    # We use symlinks here to avoid duplicate binaries in build root.
    for pcoipArchivePath in ["lib/pcoip", "lib/omnissa"]:
        linkPath = clientSDKDir.Dir(pcoipArchivePath)
        if not os.path.lexists(linkPath.abspath):
            if clientArch == "x64":
                targetPath = vmware.pkg.x64PcoipArchiveDir.Dir(
                    "usr/{}".format(pcoipArchivePath)
                )
            if clientArch == "armhf":
                targetPath = vmware.pkg.armhfPcoipArchiveDir.Dir(
                    "usr/{}".format(pcoipArchivePath)
                )
            symlinkNode = stageEnv.Command(
                linkPath,
                targetPath,
                vmware.ActionWithDisplay(
                    lambda target, source, env: os.symlink(
                        os.path.relpath(
                            source[0].abspath, os.path.dirname(target[0].abspath)
                        ),
                        target[0].abspath,
                    ),
                    "Symlinking ${SOURCE} to ${TARGET}",
                ),
            )
            # The "clientSDKNode" has a side effect that
            # it makes sure 'lib' folder exists.
            stageEnv.Depends(symlinkNode, clientSDKNode)
            stageEnv.NoCache(symlinkNode)
            clientSDKCopy += symlinkNode
    nodes += clientSDKCopy

    if vmware.ReleasePackagesDir():
        clientSDKArchive = ArchiveDirectory(sdkName, publishDir, stageDir, stageEnv)
        stageEnv.Depends(clientSDKArchive, clientSDKCopy)
        stageEnv.NoCacheIfBuildNumberInfoOverridden(clientSDKArchive)
        nodes += clientSDKArchive

    return nodes


def StageSymlink(linkDst, linkTarget, noCache=False, dependencyTarget=None):
    """
    Create symlink node
    """
    symlinkNode = stageEnv.Command(
        linkDst,
        linkTarget,
        vmware.ActionWithDisplay(
            lambda target, source, env: os.symlink(
                os.path.relpath(source[0].abspath, os.path.dirname(target[0].abspath)),
                target[0].abspath,
            ),
            "Symlinking ${TARGET} to ${SOURCE}",
        ),
    )
    if noCache:
        stageEnv.NoCache(symlinkNode)
    if dependencyTarget:
        stageEnv.Depends(dependencyTarget, symlinkNode)
    return symlinkNode


def PackageClientSdkLocalizedFiles(outputDir):
    """
    Create nodes to copy localized message files to client SDK publish
    folder 'l10n'.
    """
    copyNodes = []
    resBuildRoot = vmware.Host().ComponentBuildPath("hclinuxresource")
    gmoFiles = [
        (
            os.path.join(resBuildRoot, "gmo", "de.gmo"),
            "de/LC_MESSAGES/horizon-client-sdk.mo",
        ),
        (
            os.path.join(resBuildRoot, "gmo", "es.gmo"),
            "es/LC_MESSAGES/horizon-client-sdk.mo",
        ),
        (
            os.path.join(resBuildRoot, "gmo", "fr.gmo"),
            "fr/LC_MESSAGES/horizon-client-sdk.mo",
        ),
        (
            os.path.join(resBuildRoot, "gmo", "ja.gmo"),
            "ja/LC_MESSAGES/horizon-client-sdk.mo",
        ),
        (
            os.path.join(resBuildRoot, "gmo", "ko.gmo"),
            "ko/LC_MESSAGES/horizon-client-sdk.mo",
        ),
        (
            os.path.join(resBuildRoot, "gmo", "zh_CN.gmo"),
            "zh_CN/LC_MESSAGES/horizon-client-sdk.mo",
        ),
        (
            os.path.join(resBuildRoot, "gmo", "zh_TW.gmo"),
            "zh_TW/LC_MESSAGES/horizon-client-sdk.mo",
        ),
    ]

    for src, dst in gmoFiles:
        copySrc = stageEnv.File(src)
        copyDst = outputDir.File(dst)
        copyNode = stageEnv.LinkCopy(copyDst, copySrc)
        stageEnv.NoCache(copyNode)
        copyNodes += copyNode
    return copyNodes


def stageClientUICdkSdkUnitTest():
    """
    Stage all files for client team's unit tests.
    """

    stageEnv.LoadTool(
        [
            "zip-builder",
        ]
    )

    nodes = []

    if not vmware.ReleasePackagesDir():
        return nodes

    zipSources = []

    # 1. Deps
    for ssl_lib in stageEnv["OPENSSL_REDIST"]:
        zipSources.append(os.path.split(ssl_lib))

    zipSources.append(
        (
            vmware.GetConanComponent(stageEnv, "libffi").env["LIBPATH"][0],
            "libffi.so",
        )
    )

    libUdevCmp = vmware.GetConanComponent(stageEnv, "libudev")
    zipSources.append((os.path.join(libUdevCmp.env["LIBPATH"][0]), "libudev.so.1"))

    deps = vmware.pkg.LookupDeliverableNodes("libcrtbora", "linux64")
    deps += vmware.pkg.LookupDeliverableNodes("omnissabaselib", "linux64")
    deps += vmware.pkg.LookupDeliverableNodes("clientSdkCPrimitive", "linux64")
    for dep in deps:
        zipSources.append((dep.dir.abspath, dep.name))

    sdkUtX64Env = vmware.LookupEnv("clientSdkUnitTest-env", "linux64")
    for item in sdkUtX64Env["REDIST"]:
        for file in utOssFilelist:
            if fnmatch(os.path.basename(item), file):
                zipSources.append((os.path.dirname(item), os.path.basename(item)))

    # 2. LibCDK and SDK UTs
    cdkSdkNodes = vmware.pkg.LookupDeliverableNodes("libcdkUnitTest", "linux64")
    cdkSdkNodes += vmware.pkg.LookupDeliverableNodes("clientSdkUnitTest", "linux64")
    for cdkSdkNode in cdkSdkNodes:
        zipSources.append((cdkSdkNode.dir.abspath, cdkSdkNode.name))

    # 3. Horizon RX Test
    utNodes = vmware.pkg.LookupDeliverableNodes("horizonrxut", "linux64")
    for node in utNodes:
        zipSources.append((node.dir.abspath, node.name))
    # utBinaryPath = utNodes[0].abspath
    rxciNodes = vmware.pkg.LookupDeliverableNodes("rxci_socket_vchan", "linux64")
    # rxciBinaryPath = rxciNodes[0].abspath
    for node in rxciNodes:
        zipSources.append((node.dir.abspath, node.name))

    horizonRxTestEnv = vmware.LookupEnv("horizonrxut-env", "linux64")
    for item in horizonRxTestEnv["GTEST_REDIST"]:
        zipSources.append((os.path.dirname(str(item)), os.path.basename(str(item))))

    deploySrc = "#bora/apps/horizonrxtest/componentTest/deploy"
    zipSources.append((deploySrc, "horizonut_deploy.py"))
    zipSources.append((deploySrc, "horizonut_run_cases.py"))
    zipSources.append((deploySrc, "horizonut_lin.ini"))
    zipSources.append((deploySrc, "utility"))

    # 4. target
    zipFileName = "unittest-Omnissa-Horizon-Client-Linux-Intel-%s-%s-%s.zip" % (
        marketVersion,
        clientVersion,
        vmware.BuildNumber(),
    )
    zipFile = publishDir.File(os.path.join("tests", zipFileName))
    nodes += stageEnv.Zip(zipFile, zipSources)
    stageEnv.AddZipDeps(nodes)
    return nodes


def stageClientSDK(buildHost, clientArch):
    """
    Create Archive node for Client SDK.
    """

    nodes = []

    if not vmware.ReleasePackagesDir():
        return nodes

    SDK_ARCHIVE_NAME = "Omnissa-Horizon-Client-Linux-ClientSDK-%s-%s-%s.%s" % (
        marketVersion,
        clientVersion,
        vmware.BuildNumber(),
        clientArch,
    )
    clientSDKLibSrc = os.path.join(
        vmware.BuildRoot(),
        "build",
        "clientSdkC",
        vmware.BuildType(),
        buildHost,
        "libclientSdkC.so",
    )

    sampleCopyNodes = []
    srcDir = stageEnv.Dir(
        "#horizonclient/view/openClient/sdk/sample/linux/clientSdkGtk"
    )
    sampleCopyNodes += vmware.DirCopy(
        vmware.EnumerateSourceDir(srcDir),
        srcDir,
        stageDir.Dir(SDK_ARCHIVE_NAME).Dir("gtkSample"),
        stageEnv,
    )

    srcDir = stageEnv.Dir("#horizonclient/view/openClient/sdk/sample/linux/clientSdkQt")
    sampleCopyNodes += vmware.DirCopy(
        vmware.EnumerateSourceDir(srcDir),
        srcDir,
        stageDir.Dir(SDK_ARCHIVE_NAME).Dir("qtSample"),
        stageEnv,
    )

    clientSDKArchive = stageSDKCommon(
        SDK_ARCHIVE_NAME,
        clientArch,
        [
            "#horizonclient/view/openClient/sdk/semi-public/c/linux",
            "#horizonclient/view/openClient/sdk/semi-public/c",
            "#horizonclient/view/openClient/sdk/semi-public/",
        ],
        sampleCopyNodes,
        clientSDKLibSrc,
        "lib/libclientSdkC.so",
        "clientSdkC",
        includeFilefilter=(
            lambda root, f: f.endswith(".h")
            and not f.endswith("Int.h")
            and not f.endswith("Priv.h")
            and f != "pch.h"
        ),
    )

    # Link the USB redirection package to installers
    clientSDKDir = stageDir.Dir(SDK_ARCHIVE_NAME)
    usbInstallArchiveName = "Omnissa-Horizon-USB-{}-{}-{}.{}.tar.gz".format(
        marketVersion, clientVersion, vmware.BuildNumber(), clientArch
    )

    usbLinkTarget = vmHzCltDir.Dir(clientArch).File(usbInstallArchiveName)
    usbLinkPath = clientSDKDir.Dir("installers").File(usbInstallArchiveName)
    StageSymlink(usbLinkPath, usbLinkTarget, True, clientSDKArchive)

    if clientArch == "x64":
        # Link other RX feature packages to the installers
        rxFeatureNames = [
            "html5mmr",
            "scannerClient",
            "integratedPrinting",
            "serialportClient",
        ]
        for rxFeature in rxFeatureNames:
            tarFileName = "Omnissa-Horizon-{}-{}-{}-{}.x64.tar.gz".format(
                rxFeature, marketVersion, clientVersion, vmware.BuildNumber()
            )
            rxFeatureTarget = vmHzCltDir.Dir(clientArch).File(tarFileName)
            linkPath = clientSDKDir.Dir("installers").File(tarFileName)
            StageSymlink(linkPath, rxFeatureTarget, True, clientSDKArchive)

        # Copy horizon_hid.rules file
        clientSDKArchive += stageEnv.FileCopy(
            clientSDKDir.File("etc/udev/rules.d/horizon_hid.rules"),
            stageEnv.File("#horizonclient/install/vmis/linux/rules/horizon_hid.rules"),
        )

    # Copy the SDK docs to client SDK folder
    # Could not use the function DirCopy, because the file list is unknown.
    docCopyNode = stageEnv.Command(
        stageDir.Dir(SDK_ARCHIVE_NAME).Dir("doc").abspath,
        vmware.pkg.sdkCDocDir.abspath,
        vmware.ActionWithDisplay(
            "$MKDIR -p $TARGET && $CP -rf $SOURCE/* $TARGET/",
            "Copying client SDK API docs",
        ),
    )
    stageEnv.NoCache(docCopyNode)
    stageEnv.Depends(clientSDKArchive, docCopyNode)
    nodes += docCopyNode

    # Copy the release notes
    srcDir = stageEnv.Dir("#horizonclient/view/openClient/sdk/docs/linux")
    clientSDKArchive += vmware.DirCopy(
        vmware.EnumerateSourceDir(srcDir, dirfilter=nonrecursiveDirFilter),
        srcDir,
        stageDir.Dir(SDK_ARCHIVE_NAME),
        stageEnv,
    )

    # Copy localized message files
    gmoDir = clientSDKDir.Dir("l10n")
    gmoNodes = PackageClientSdkLocalizedFiles(gmoDir)
    stageEnv.Depends(clientSDKArchive, gmoNodes)

    nodes += clientSDKArchive

    return nodes


def stageRTAVClientComponentTest():
    """Stage RTAV CI test packages and binaries for client side

    stageEnv specifies a stage environment object.
    """
    nodes = []
    targetName = ["rtavTestNodeClient"]

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        testsSrc = "#bora/apps/rde/rtav/tests"
        deploySrc = testsSrc + "/testFramework/deploy/client"
        utilsSrc = testsSrc + "/testFramework/utils"
        testSuiteSrc = testsSrc + "/testFramework/testSuite"
        testCasesSrc = testsSrc + "/testCases"

        publishDir = os.path.join(publishDir, "tests")
        deliverableNodesNames = [
            "rtavCliLib",
            "rtavPlugin",
            "rtavSetting",
            "rtavTestNodeClient",
            "omnissabaselib",
        ]

        rtavPluginEnv = vmware.LookupEnv("rtavPlugin-env", "linux64")
        extraZipSources = []

        for file in rtavPluginEnv["REDIST"]:
            if os.path.basename(file).startswith(
                (
                    "libcrypto",
                    "libssl",
                    "libpng",
                )
            ):
                fileAbsPath = os.path.abspath(file)
                extraZipSources.append(
                    (os.path.dirname(fileAbsPath), os.path.basename(file))
                )

        x264Lib = rtavPluginEnv["LIBX264_REDIST"]
        extraZipSources.append((os.path.dirname(x264Lib), os.path.basename(x264Lib)))
        libstdcpp_dir = os.path.join(
            vmware.GetGobuildComponent(stageEnv.GetGCCComponentName()),
            "usr",
            "x86_64-vmk-linux-gnu",
            "lib64",
        )
        extraZipSources.append((libstdcpp_dir, "libstdc++.so.6"))

        avcodecLib = rtavPluginEnv["FFMPEG_SOFTWARE_AVCODEC_REDIST"]
        avutilLib = rtavPluginEnv["FFMPEG_SOFTWARE_AVUTIL_REDIST"]
        extraZipSources.append(
            (os.path.dirname(avcodecLib), os.path.basename(avcodecLib))
        )
        extraZipSources.append(
            (os.path.dirname(avutilLib), os.path.basename(avutilLib))
        )
        extraZipSources.append((Dir(deploySrc).abspath, "."))
        extraZipSources.append((Dir(utilsSrc).abspath, "."))
        extraZipSources.append((Dir(testSuiteSrc).abspath, "."))
        extraZipSources.append((Dir(testCasesSrc).abspath, "."))
        zipFilePath = os.path.join(publishDir, "linux64", "rtavClientComponentTest.zip")
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetName,
            "linux64",
            deliverableNodesNames,
            extraZipSources,
            zipFilePath,
        )
        if zipNode is not None:
            nodes += zipNode

    return nodes


def stageRTAVUnitTest():
    """stageRTAVUnitTest

    Stage the test library for RTAV Unit Test.

    """
    stageEnv.LoadTool("zip")
    stageEnv.LoadTool("zip-builder")
    zipNode = []
    publishDir = vmware.ReleasePackagesDir()

    if publishDir is not None:
        rtavPluginEnv = vmware.LookupEnv("rtavPlugin-env", "linux64")
        nodeNames = [
            "rtavUnitTest",
            "omnissabaselib",
        ]
        zipSources = []

        for file in rtavPluginEnv["REDIST"]:
            if os.path.basename(file).startswith(
                (
                    "libcrypto",
                    "libssl",
                    "libpng",
                )
            ):
                fileAbsPath = os.path.abspath(file)
                zipSources.append(
                    (os.path.dirname(fileAbsPath), os.path.basename(file))
                )

        libUdevCmp = vmware.GetConanComponent(stageEnv, "libudev")
        zipSources.append((os.path.join(libUdevCmp.env["LIBPATH"][0]), "libudev.so.1"))

        for nodeName in nodeNames:
            binaryNodes = vmware.pkg.LookupDeliverableNodes(nodeName, "linux64")
            for node in binaryNodes:
                zipSources.append((node.dir.abspath, node.name))

        zipFile = File(os.path.join(publishDir, "tests", "linux64", "rtavUnitTest.zip"))
        zipNode = stageEnv.Zip(zipFile, zipSources)
        stageEnv.AddZipDeps(zipNode)

    return zipNode


def stageUnitTest():
    """stageUnitTest
    Stage the binaries for MultiAudio unit test..
    """
    nodes = []
    targetName = "multiAudioTest"
    vmware.PathPrefixer(vmware.pkg.stagePath) / targetName

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is None:
        return nodes

    host = "linux64"
    zipSources = []
    stageEnv.LoadTool("zip")
    stageEnv.LoadTool("zip-builder")
    stageEnv.LoadTool("libssl")

    for ssl_lib in stageEnv["OPENSSL_REDIST"]:
        zipSources.append(os.path.split(ssl_lib))

    x64TestEnv = vmware.LookupEnv(targetName, "linux64")
    for item in x64TestEnv["REDIST"]:
        for rule in utOssFilelist:
            if fnmatch(os.path.basename(item), rule):
                zipSources.append((os.path.dirname(item), os.path.basename(item)))

    deps = vmware.pkg.LookupDeliverableNodes(targetName, host)
    deps += vmware.pkg.LookupDeliverableNodes("libcrtbora", host)
    deps += vmware.pkg.LookupDeliverableNodes("omnissabaselib", host)

    for node in deps:
        zipSources.append((node.dir.abspath, node.name))

    zipFile = File(
        os.path.join(
            publishDir,
            "tests",
            host,
            "ut" + targetName[0].upper() + targetName[1:] + ".zip",
        )
    )
    nodes += stageEnv.Zip(zipFile, zipSources)
    stageEnv.AddZipDeps(nodes)
    return nodes


def stageURLComponentTestClient():
    """URLComponentTest

    Stage the binaries for URL Redirection Component Test.

    """

    nodes = []
    targetName = ["urlComponentTest"]

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(vmware.DirAbsPath(publishDir), "tests")
        deliverableNodesNames = ["urlComponentTest", "urlFilterPlugin"]
        deliverableNodesNames.append("rxTestApp")

        extraZipSources = []
        prefix = "linux"
        extraZipSources.append(
            (
                stageEnv.Dir(
                    "#bora/apps/rde/urlRedirection/tests/" "componenttest/"
                ).abspath,
                "url_client_%s.ini" % prefix,
            )
        )

        zipFilePath = os.path.join(publishDir, "linux64", "urlComponentTestClient.zip")
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetName,
            "linux64",
            deliverableNodesNames,
            extraZipSources,
            zipFilePath,
        )
        if zipNode is not None:
            nodes += zipNode

    return nodes


def stageURLUnitTest():
    """URLUnitTest

    Stage the binaries for URL Redirection Unit Test.

    """

    nodes = []
    targetName = ["urlUnitTest"]

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(vmware.DirAbsPath(publishDir), "tests")
        extraZipSources = []
        zipFilePath = os.path.join(publishDir, "linux64", "urlUnitTest.zip")
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetName,
            "linux64",
            targetName,
            extraZipSources,
            zipFilePath,
            excludedFileFormats=[".map"],
        )
        if zipNode is not None:
            nodes += zipNode

    return nodes


usbTestDepFilelist = [
    "libxml*.so.*",
]


def stageClientUSBUnitTest():
    """stageClientUSBUnitTest

    Stage the unit test library for USB client components.

    """
    nodes = []
    targetName = "usbTest"
    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(publishDir, "tests")
        deploySrc = "#bora/apps/viewusb/framework/usb/test/unitTestSet"
        deploySrc = stageEnv.Dir(deploySrc).abspath
        zipSources = [
            (deploySrc, "usb_ut_deploy.py"),
            (deploySrc, "usb_ut_run_all_cases.py"),
        ]
        zipSources.append((deploySrc, "usb_ut_lin.ini"))

        testNodes = vmware.pkg.LookupDeliverableNodes(targetName, "linux64")
        testLibs = [
            "usbMmfwUnitTest",
            "usbViewUsbLibUnitTest",
            "usbStringStoreUnitTest",
            "usbUrbTrxUnitTest",
            "usbDevConfigUnitTest",
            "usbDevFilterUnitTest",
            "usbUsbdUnitTest",
            "usbRedirectionClientUnitTest",
        ]
        testLibs = ["lib%s" % name for name in testLibs]

        for node in testNodes:
            if node.name.startswith(tuple(testLibs)) and not node.name.endswith(
                (".lib", ".map")
            ):
                zipSources.append((node.dir.abspath, node.name))

        # other dependencies
        usbTestEnv = vmware.LookupEnv("usbComponentTest-env", "linux64")
        addDepsToZipSources(
            usbTestEnv, ["LIBICONV_REDIST", "OPENSSL_REDIST"], zipSources
        )

        for item in usbTestEnv["REDIST"]:
            for file in usbTestDepFilelist:
                if fnmatch(os.path.basename(item), file):
                    zipSources.append((os.path.dirname(item), os.path.basename(item)))

        zipFile = File(os.path.join(publishDir, "linux64", "usbUnitTestClient.zip"))
        zipNode = stageEnv.Zip(zipFile, zipSources)
        nodes += zipNode

    return nodes


def stageUSBClientComponentTest():
    """stageUSBClientComponentTest

    Stage the binaries for usb Component Test.
    """

    nodes = []
    targetName = "usbTest"
    vmware.PathPrefixer(vmware.pkg.stagePath) / targetName

    deploySrc = "#bora/apps/viewusb/framework/usb/test/componenttest/deploy"
    testDataSrc = "#bora/apps/viewusb/framework/usb/test/componenttest/plugin/testData"
    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(publishDir, "tests")
        deliverableNodesNames = [
            "horizon-usbd",
            "usbRedirectionClient",
            "vdpservice",
            "usbArbitrator-podbin",
            "usbArbLib-podbin",
            "usbEnum-podbin",
            "usbEnumArb-podbin",
            "usbDeviceProperties-podbin",
            "usbStringPod-podbin",
        ]
        zipSources = []
        testNodes = vmware.pkg.LookupDeliverableNodes("usbTest", "linux64")
        for node in testNodes:
            if node.name.startswith(
                (
                    "usbComponentTest",
                    "libusbComponentTest",
                    "usbEngineTest",
                    "usbUdeTest",
                    "usbMmfwClientTest",
                    "usbMmfwServerTest",
                )
            ):
                zipSources.append((node.dir.abspath, node.name))

        zipSources.append((stageEnv.Dir(deploySrc).abspath, "usb_run_cases.py"))
        zipSources.append((stageEnv.Dir(testDataSrc).abspath, "memdebug_config.json"))

        zipSources.append((stageEnv.Dir(deploySrc).abspath, "usb_client_lin.ini"))
        zipSources.append(
            (stageEnv.Dir(testDataSrc).abspath, "usbLinuxInstallerTest.py")
        )

        usbTestEnv = vmware.LookupEnv("usbComponentTest-env", "linux64")
        addDepsToZipSources(usbTestEnv, ["LIBICONV_REDIST"], zipSources)
        for item in usbTestEnv["REDIST"]:
            for file in usbTestDepFilelist:
                if fnmatch(os.path.basename(item), file):
                    zipSources.append((os.path.dirname(item), os.path.basename(item)))

        zipSources.append((stageEnv.Dir(testDataSrc).abspath, "deployUtils.py"))
        zipSources.append((stageEnv.Dir(testDataSrc).abspath, "deviceUtils.py"))
        zipSources.append((stageEnv.Dir(testDataSrc).abspath, "policyUtils.py"))
        zipSources.append((stageEnv.Dir(deploySrc).abspath, "prepare.sh"))

        # Use usbComponentTestClient.zip to adapt to the CI deploy script.
        zipFile = File(
            os.path.join(publishDir, "linux64", "usbComponentTestClient.zip")
        )
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv, [], "linux64", deliverableNodesNames, zipSources, zipFile
        )
        nodes += zipNode
    return nodes


componentNodes = {"x64": (), "armhf": ()}


def componentNodeParse():
    SConscript("#bora/scons/package/hccrt/linux/hclininst.py")
    for clientArch, buildHost in [("x64", "linux64"), ("armhf", "armhf-linux")]:
        vmware.pkg.buildHost = buildHost
        vmware.pkg.clientArch = clientArch
        vmware.pkg.env = vmware.LookupEnv("hclinux-env", host=buildHost)
        rpmProductName = "omnissa-horizon-client"
        rpmBuildRootPath = os.path.join(
            stageDir.abspath, clientArch, "%s-rpm" % rpmProductName
        )
        # 'stage' is from rpm's definition and can't be changed.
        rpmPackageDirPath = os.path.join(rpmBuildRootPath, rpmProductName, "stage")
        vmware.pkg.rpmPackageDir = stageEnv.Dir(rpmPackageDirPath)
        sconsFileList = [
            "#bora/scons/package/hccrt/linux/client.py",
            "#bora/scons/package/hccrt/linux/fido2.py",
            "#bora/scons/package/hccrt/linux/fileAssociation.py",
            "#bora/scons/package/hccrt/linux/hostedApps.py",
            "#bora/scons/package/hccrt/linux/integratedPrinting.py",
            "#bora/scons/package/hccrt/linux/mmr.py",
            "#bora/scons/package/hccrt/linux/pcoip.py",
            "#bora/scons/package/hccrt/linux/rtav.py",
            "#bora/scons/package/hccrt/linux/scannerClient.py",
            "#bora/scons/package/hccrt/linux/serialPortClient.py",
            "#bora/scons/package/hccrt/linux/smartCard.py",
            "#bora/scons/package/hccrt/linux/tsdr.py",
            "#bora/scons/package/hccrt/linux/urlRedirection.py",
            "#bora/scons/package/hccrt/linux/usb.py",
            "#bora/scons/package/hccrt/linux/html5Mmr.py",
        ]
        componentNodes[clientArch] = SConscript(sconsFileList)


def stageBundleInstaller():
    nodes = []
    vmisInstallerCopy = vmware.DirCopy(
        vmware.EnumerateSourceDir("#horizonclient/install/vmis"),
        vmware.Dir("#horizonclient/install/vmis"),
        vmware.Dir(vmisDir),
        stageEnv,
    )
    if not vmware.LocalOpts.GetBool("ENABLE_CODE_COV", False):
        archLst = ["x64", "armhf"]
    else:
        archLst = ["x64"]
    for clientArch in archLst:
        compNodes = []
        for sconsFileRet in componentNodes[clientArch]:
            compNodes += [sconsFileRet[1]]
        nodes += compNodes
        BUNDLE_NAME = "Omnissa-Horizon-Client-%s-%s-%s.%s.bundle" % (
            marketVersion,
            clientVersion,
            vmware.BuildNumber(),
            clientArch,
        )
        stageEnv.Depends(compNodes, vmisInstallerCopy)
        vmisInstallerComp = vmware.pkg.VmisCreateInstaller(
            "vmware",
            vmisVersion,
            marketVersion,
            clientArch,
            scriptPath,
            vmisDir,
            vmisSource,
        )
        nodes += [vmisInstallerComp]

        if not vmware.ReleasePackagesDir():
            continue

        publishDir = stageEnv.Dir(vmware.ReleasePackagesDir())
        bundle = vmware.pkg.VmisCreateBundle(
            publishDir.File(BUNDLE_NAME),
            "Omnissa Horizon Client",
            "omnissa-horizon-client",
            "Omnissa Horizon Client for Linux",
            clientArch,
            scriptPath,
            vmisDir,
            vmisSource,
            vmisInstallerComp,
            componentNodes[clientArch],
            vmisVersion,
            clientVersion,
        )
        nodes += [bundle]

    return nodes


def stageDebInstaller():
    nodes = []
    cpNodes = []

    for sconsFileRet in componentNodes["x64"]:
        cpNodes += sconsFileRet[4]
    nodes += cpNodes

    publishDir = vmware.ReleasePackagesDir()
    if not publishDir:
        return nodes

    archStageDir = stageDir.Dir("x64")
    DEB_PACKAGE_NAME = "Omnissa-Horizon-Client-%s-%s-%s.%s.deb" % (
        marketVersion,
        clientVersion,
        vmware.BuildNumber(),
        "x64",
    )
    debPackageFile = Dir(publishDir).File(DEB_PACKAGE_NAME)
    debCtrlFileSrc = stageEnv.Dir("#horizonclient/install/vmis/linux/scriptlets/deb")
    # For binary package, folder name must be
    # 'DEBIAN' which is upper case.
    debCtrlFileDst = archStageDir.Dir("{}/DEBIAN".format(vmware.pkg.debPackageDirName))
    ctrlCopy = vmware.DirCopy(
        vmware.EnumerateSourceDir(debCtrlFileSrc),
        debCtrlFileSrc,
        debCtrlFileDst,
        stageEnv,
    )
    nodes += ctrlCopy

    installedSizeRevise = vmware.DummyCommand(
        stageEnv,
        "deb-installed-size-revise",
        archStageDir.Dir(vmware.pkg.debPackageDirName),
        vmware.ActionWithDisplay(
            reviseDebInstalledSize,
            "Revising DEB Installed-Size",
        ),
    )
    stageEnv.Depends(installedSizeRevise, cpNodes)
    stageEnv.Depends(installedSizeRevise, ctrlCopy)
    nodes += installedSizeRevise

    debCtrlFile = debCtrlFileDst.File("control")
    versionRevise = '-i -e "s|@VERSION@|{}-{}-{}|g"'.format(
        marketVersion, clientVersion, vmware.BuildNumber()
    )
    archRevise = '-i -e "s|@ARCH@|{}|g"'.format("amd64")

    if not vmware.ReleasePackagesDir():
        return nodes

    stageEnv.LoadTool("tar-1.35")
    debPackage = stageEnv.Command(
        debPackageFile,
        archStageDir.Dir(vmware.pkg.debPackageDirName),
        # Note: because of the $CD calls, any binaries in the command string
        # that are from the gobuild compcache dir need to have absolute paths.
        vmware.ActionWithDisplay(
            "$CD $DEBPACKAGEDIR && "
            "$SEDBIN $VERSIONREVISE $DEBCTRLFILE && "
            "$SEDBIN $ARCHREVISE $DEBCTRLFILE && "
            "$FIND * -type f -not -path 'DEBIAN/*' | "
            "$XARGS $MD5SUM > DEBIAN/md5sums && "
            "$DPKGDEB -b --root-owner-group . $DEBPACKAGEFILE",
            "Generating DEB package: {}".format(debPackageFile.abspath),
        ),
        DEBPACKAGEDIR=archStageDir.Dir(vmware.pkg.debPackageDirName),
        DEBCTRLFILE=debCtrlFile.abspath,
        VERSIONREVISE=versionRevise,
        ARCHREVISE=archRevise,
        DEBPACKAGEFILE=debPackageFile.abspath,
        DEBPACKAGE=DEB_PACKAGE_NAME,
    )

    stageEnv.Depends(debPackage, installedSizeRevise)
    nodes += debPackage

    return nodes


def stageRpmInstaller():
    nodes = []
    cpNodes = []
    rpmProductName = "omnissa-horizon-client"
    fileAndDirAttr = "%attr(0555, root, root)"
    rpmBuildRootPath = os.path.join(stageDir.abspath, "x64", "%s-rpm" % rpmProductName)
    # 'stage' is from rpm's definition and can't be changed.
    rpmPackageDirPath = os.path.join(rpmBuildRootPath, rpmProductName, "stage")

    for sconsFileRet in componentNodes["x64"]:
        cpNodes += sconsFileRet[5]
    nodes += cpNodes

    rpmSpec = vmware.RPMSpec(
        rpmProductName, stageEnv, "x86_64", rootPath=rpmBuildRootPath, noautoreq=True
    )

    rpmSpec.SetDescription("Omnissa Horizon Client for Linux")

    mVersion = (
        marketVersion[: marketVersion.index(".")]
        if "." in marketVersion
        else marketVersion
    )
    rpmSpec.AddHeaders(
        {
            "Summary": "Omnissa Horizon Client for Linux",
            "Name": rpmProductName,
            "Version": "{}_{}".format(mVersion, clientVersion),
            "Release": vmware.BuildNumber(),
            "License": "commercial",
            "Vendor": "Omnissa, Inc",
            "Group": "Application/System",
            "Provides": "vmware-horizon-client",
            "Obsoletes": "vmware-horizon-client",
            "Conflicts": "vmware-horizon-client",
        }
    )

    scriptlets = {"post": "postin", "preun": "preun", "postun": "postun"}
    for role, file in scriptlets.items():
        script = stageEnv.File(
            "#horizonclient/install/vmis/linux/scriptlets/rpm/{}".format(file)
        )
        rpmSpec.SetScript(role, script.get_text_contents())

    """
    RPM tool packs files which are placed under %buildRoot
    and added into %file.
    """
    for cpNode in cpNodes:
        fRelativePath = cpNode.abspath.removeprefix(rpmPackageDirPath)
        rpmSpec.AddFileManual(vmware.pkg.rpmPackageDir, fRelativePath, fileAndDirAttr)

    clientDirs = {
        "/usr/lib/pcoip",
        "/usr/lib/pcoip/vchan_plugins",
        "/usr/lib/omnissa/gcc",
        "/usr/lib/omnissa/rdpvcbridge",
        "/usr/lib/omnissa/horizon",
        "/usr/lib/omnissa/horizon/bin",
        "/usr/lib/omnissa/horizon/client",
        "/usr/lib/omnissa/horizon/dct",
        "/usr/lib/omnissa/horizon/dct/configFiles",
        "/usr/lib/omnissa/horizon/env",
        "/usr/lib/omnissa/horizon/integratedPrinting",
        "/usr/lib/omnissa/horizon/software",
        "/usr/lib/omnissa/horizon/urlRedirection",
        "/usr/lib/omnissa/horizon/vaapi",
        "/usr/lib/omnissa/horizon/vaapi2",
        "/usr/lib/omnissa/horizon/vaapi2.7",
        "/usr/lib/omnissa/horizon/vdpau",
        "/usr/lib/omnissa/horizon/vdpService",
        "/usr/lib/omnissa/horizon/vdpService/webrtcRedir",
        "/usr/lib/omnissa/xkeymap",
        "/usr/share/doc/omnissa-horizon-client",
        "/usr/share/doc/omnissa-horizon-client/patches",
    }
    for dir in clientDirs:
        rpmSpec.AddDirectory(dir, fileAndDirAttr)

    rpmSpec.AddRequires("libXinerama >= 1.1.3")
    rpmSpec.AddRequires("libXrandr >= 1.5.2")
    rpmSpec.AddRequires("libXScrnSaver >= 1.0.0")
    rpmPackage = rpmSpec.CreatePackageNode()[0]
    stageEnv.Depends(rpmPackage, cpNodes)

    if not vmware.ReleasePackagesDir():
        return nodes

    RPM_PACKAGE_NAME = "Omnissa-Horizon-Client-%s-%s-%s.x64.rpm" % (
        marketVersion,
        clientVersion,
        vmware.BuildNumber(),
    )
    signedRpmPackage = publishDir.File(RPM_PACKAGE_NAME)

    signBinaries = vmware.SignBinaries()
    if not signBinaries:
        rpmPublish = stageEnv.LinkCopy(signedRpmPackage, rpmPackage)
        nodes += [rpmPublish]
    else:
        stageEnv.LoadTool("rpm")
        stageEnv.LoadTool("gnupg")

        SIGN_KEY_ID = vmware.LocalOpts.GetString("LIN_SIGNING_KEY_ID", None)
        SIGN_KEY_PATH = vmware.LocalOpts.GetString("LIN_SIGNING_KEY_PATH", None)
        PASSWD = os.environ.get("LIN_SIGNING_KEY_PASSWORD")

        rpmPublish = stageEnv.Command(
            signedRpmPackage,
            rpmPackage,
            vmware.ActionWithDisplay(
                "$CP ${SOURCE.abspath} ${TARGET.abspath} && "
                "$RPMSIGN $RPMBUILDPARAMS "
                '--define="_signature gpg" '
                '--define="__gpg $GPG" '
                '--define="_gpg_path $KEYPATH" '
                '--define="_gpg_name $KEYID" '
                '--define="_tmppath /var/tmp" '
                '--define="__gpg_sign_cmd %{__gpg} gpg '
                "--pinentry-mode=loopback "
                "--passphrase $PASSWORD "
                "--no-verbose --no-armor --no-secmem-warning "
                "--digest-algo=SHA256 -u %{_gpg_name} "
                '-sbo %{__signature_filename} %{__plaintext_filename}" '
                "--addsign ${TARGET.abspath}",
                "Signing rpm package",
            ),
            KEYPATH=SIGN_KEY_PATH,
            KEYID=SIGN_KEY_ID,
            PASSWORD=PASSWD,
        )
        nodes += [rpmPublish]

    return nodes


def stageUSBSDKArchive():
    nodes = []
    for clientArch, buildHost in [("x64", "linux64"), ("armhf", "armhf-linux")]:
        USB_SDK_ARCHIVE_NAME = "Omnissa-Horizon-USB-sdk-%s-%s-%s.%s" % (
            marketVersion,
            clientVersion,
            vmware.BuildNumber(),
            clientArch,
        )
        usbSDKArchiveDir = stageDir.Dir(USB_SDK_ARCHIVE_NAME).Dir(clientArch)

        nodes += stageEnv.FileCopy(
            usbSDKArchiveDir.Dir("include").File("viewusblib.h"),
            File("#bora/apps/viewusb/framework/usb/include/viewusblib.h"),
        )

        for name in ["mmfw", "viewusblib"]:
            for src in vmware.LookupNode(name, buildHost):
                nodes += stageEnv.LinkCopy(
                    usbSDKArchiveDir.Dir("lib").File(src.name), src
                )

        if vmware.ReleasePackagesDir():
            usbSDKArchive = ArchiveDirectory(
                USB_SDK_ARCHIVE_NAME, vmHzCltDir.Dir(clientArch), stageDir, stageEnv
            )
            stageEnv.NoCacheIfBuildNumberInfoOverridden(usbSDKArchive)
            nodes += usbSDKArchive
    return nodes


def stageSDKArchive():
    nodes = []
    for clientArch, buildHost in [("x64", "linux64"), ("armhf", "armhf-linux")]:
        LIB_CDK_ARCHIVE_NAME = "Omnissa-Horizon-Client-sdk-%s-%s-%s.%s" % (
            marketVersion,
            clientVersion,
            vmware.BuildNumber(),
            clientArch,
        )
        libCdkSrc = stageEnv.Dir("#horizonclient/view/openClient/lib/cdk")
        libCdkDir = stageDir.Dir(LIB_CDK_ARCHIVE_NAME)
        libCdkDst = libCdkDir.Dir("include")
        headerFiles = [
            file
            for file in vmware.EnumerateSourceDir(
                libCdkSrc, dirfilter=nonrecursiveDirFilter
            )
            if file.name.endswith(".h")
        ]
        headerFilesCopy = vmware.DirCopy(headerFiles, libCdkSrc, libCdkDst, stageEnv)
        libCdkSrc = stageEnv.File(
            os.path.join(buildRoot, "build", "libcdk", buildType, buildHost, "libcdk.a")
        )
        libCdkDst = libCdkDir.File("lib/libcdk.a")
        libCdkCopy = stageEnv.LinkCopy(libCdkDst, libCdkSrc)
        nodes += headerFilesCopy
        nodes += libCdkCopy

        if vmware.ReleasePackagesDir():
            libCdkArchive = ArchiveDirectory(
                LIB_CDK_ARCHIVE_NAME, publishDir, stageDir, stageEnv
            )
            stageEnv.NoCacheIfBuildNumberInfoOverridden(libCdkArchive)
            nodes += libCdkArchive

        clientSDKArchive = stageClientSDK(buildHost, clientArch)

        # "clientSDKArchive" depends on contents of "vmware.pkg.pcoipArchiveNode"
        # via 'lib/pcoip' and 'lib/vmware' symlinks.
        if clientArch == "x64":
            stageEnv.Depends(clientSDKArchive, vmware.pkg.x64PcoipArchiveNode)

        if clientArch == "armhf":
            stageEnv.Depends(clientSDKArchive, vmware.pkg.armhfPcoipArchiveNode)

        stageEnv.Depends(clientSDKArchive, vmware.pkg.sdkCDocNode)
        nodes += clientSDKArchive

    return nodes


def stagePOTFile():
    nodes = []

    if not vmware.ReleasePackagesDir():
        return nodes
    publishDir = stageEnv.Dir(vmware.ReleasePackagesDir())
    vmwareViewDir = stageEnv.Dir(
        os.path.join(
            vmware.BuildRoot(), "build", "linuxClient", vmware.BuildType(), "linux64"
        )
    )
    vmwareViewPot = vmwareViewDir.File("horizon-client.pot")
    vmwareView = vmwareViewDir.File("horizon-client")
    poFilesDir = vmware.Dir("#horizonclient/view/openClient/po")
    sedScript = '-i -e "s|%s/||g"' % poFilesDir.abspath
    potFile = stageEnv.Command(
        vmwareViewPot,
        poFilesDir,
        vmware.ActionWithDisplay(
            "$CD $POFILESDIR && XGETTEXT_ARGS=--keyword=NS_ "
            "INTLTOOLMERGE=$INTLTOOLMERGE XGETTEXT=$XGETTEXT "
            "srcdir=$POFILESDIR INTLTOOL_EXTRACT=$INTLTOOL_EXTRACT "
            "$PERL $INTLTOOLUPDATE --pot --gettext-package $VMWAREVIEW "
            "-o $VMWAREVIEWPOT && $SEDBIN $SEDSCRIPT $VMWAREVIEWPOT",
            "Generate horizon-client.pot",
        ),
        INTLTOOLMERGE=intlToolMerge,
        INTLTOOL_EXTRACT=intlToolExtract,
        POFILESDIR=poFilesDir.abspath,
        INTLTOOLUPDATE=intlToolUpdate,
        VMWAREVIEW=vmwareView.abspath,
        VMWAREVIEWPOT=vmwareViewPot.abspath,
        SEDSCRIPT=sedScript,
    )
    stageEnv.AddIntlToolDeps(potFile)

    potSrc = stageEnv.File(
        os.path.join(
            vmware.BuildRoot(),
            "build",
            "linuxClient",
            vmware.BuildType(),
            "linux64",
            "horizon-client.pot",
        )
    )
    potDst = publishDir.File("horizon-client.pot")
    potCopy = stageEnv.LinkCopy(potDst, potSrc)

    stageEnv.Depends(potCopy, potFile)

    nodes += potCopy

    return nodes


def stageFabulatechJsonFile():
    nodes = []
    if not vmware.ReleasePackagesDir():
        return nodes
    FabulatechInPublishDir = stageEnv.Dir(
        os.path.join(vmware.ReleasePackagesDir(), "fabulatech")
    )
    stageEnv.Execute(vmware.Mkdir(FabulatechInPublishDir))
    arch_folder = "lin64"
    for item in ["fabulatech_common", "fabulatech_scanner", "fabulatech_serialport"]:
        conanFabulatechDir = vmware.GetConanComponent(stageEnv, item).package_folder
        jsonFileSrc = stageEnv.File(
            os.path.join(
                conanFabulatechDir,
                "lin",
                arch_folder,
                "fabulatech_drop_components_version.json",
            )
        )
        jsonFileDst = FabulatechInPublishDir.File(f"{item}_version.json")
        jsonFileCopy = stageEnv.LinkCopy(jsonFileDst, jsonFileSrc)
        nodes += jsonFileCopy
    return nodes


def stageRmksStubArchive():
    nodes = []

    if not vmware.ReleasePackagesDir():
        return nodes

    RMKS_STUB_ARCHIVE_NAME = "Omnissa-Horizon-Remotemks-Stub-Linux-%s-%s-%s" % (
        marketVersion,
        clientVersion,
        vmware.BuildNumber(),
    )
    rmksStubIntDir = buildIntDir.Dir(RMKS_STUB_ARCHIVE_NAME)
    rmksStubCopy = []
    for bh, arch in [("linux64", "x64"), ("armhf-linux", "armhf")]:
        for n in vmware.LookupNode("viewClientStub", bh):
            rmksStubCopy += stageEnv.LinkCopy(rmksStubIntDir.Dir(arch).File(n.name), n)

    publishDir = stageEnv.Dir(vmware.ReleasePackagesDir())
    rmksStubArchive = ArchiveDirectory(
        rmksStubIntDir.name, publishDir, rmksStubIntDir.dir, stageEnv
    )
    stageEnv.NoCacheIfBuildNumberInfoOverridden(rmksStubArchive)

    nodes += rmksStubArchive

    return nodes


def stageVMHZCltArchiveAll():
    nodes = []

    if not vmware.ReleasePackagesDir():
        return nodes

    publishDir = stageEnv.Dir(vmware.ReleasePackagesDir())
    vmHzCltArchiveAll = ArchiveDirectory(
        VMWARE_HORIZON_CLIENT_ARCHIVE_NAME, publishDir, stageDir, stageEnv
    )
    stageEnv.NoCacheIfBuildNumberInfoOverridden(vmHzCltArchiveAll)

    nodes += vmHzCltArchiveAll

    return nodes


def stageOpenSourceArchive():
    nodes = []
    headerDirs = set()
    files = set()
    openSourceSrc = stageEnv.Dir("#horizonclient/view/openClient/")
    # Get header files directories and source files.
    for hosttype in ["armhf-linux", "linux64"]:
        for nodeName in ["hclinux", "libcdk", "clientSdk"]:
            node = vmware.LookupNode(nodeName, hosttype)
            headerDirs.update(
                [
                    stageEnv.Dir(d)
                    for d in node[0].env["CPPPATH"]
                    if vmware.IsFromDir(stageEnv.Dir(d).abspath, openSourceSrc.abspath)
                ]
            )
            files.update(stageEnv.Flatten([s.sources for s in node[0].sources]))

    files = set([f for f in files if f.abspath.startswith(openSourceSrc.abspath)])

    # Project files
    projectFiles = [
        "buildNumber.h.in",
        "configure.ac",
        "extraTranslations.hh",
        "gtk/Makefile.inc",
        "gtk/resource.xml",
        "lib/cdk/Headers.inc",
        "lib/cdk/Makefile.inc",
        "lib/libview/Makefile.inc",
        "Makefile.am",
        "Makefile.inc",
        "sdk/Makefile.inc",
        "horizon-client.desktop.in.in",
    ]

    files.update([openSourceSrc.File(f) for f in projectFiles])

    OPEN_SOURCE_ARCHIVE_NAME = "Omnissa-Horizon-Client-source-%s-%s-%s" % (
        marketVersion,
        clientVersion,
        vmware.BuildNumber(),
    )
    openSourceStageDir = stageDir.Dir(OPEN_SOURCE_ARCHIVE_NAME)
    stageEnv.Execute(vmware.Mkdir(openSourceStageDir))

    # Copy header files
    for d in headerDirs:
        for f in vmware.EnumerateSourceDir(d, dirfilter=nonrecursiveDirFilter):
            if f.name.endswith((".h", ".hpp")):
                fileName = stageEnv.File(f).abspath[len(openSourceSrc.abspath) + 1 :]
                nodes += stageEnv.LinkCopy(
                    openSourceStageDir.File(fileName), openSourceSrc.File(fileName)
                )

    # Copy source files
    for f in files:
        nodes += stageEnv.LinkCopy(
            openSourceStageDir.File(f.abspath[len(openSourceSrc.abspath) + 1 :]), f
        )

    # Copy project files in directory
    projectDirs = [
        "doc",
        "icons",
        "icudata",
        "m4",
        "po",
    ]
    for d in projectDirs:
        nodes += vmware.DirCopy(
            vmware.EnumerateSourceDir(openSourceSrc.Dir(d)),
            openSourceSrc.Dir(d),
            openSourceStageDir.Dir(d),
            stageEnv,
        )

    # Generate 'configure'
    stageEnv.LoadTool("autotools")
    openSourceConfigureSource = [
        openSourceStageDir.File("configure.ac"),
        openSourceStageDir.File("horizon-client.desktop.in.in"),
        openSourceStageDir.File("gtk/Makefile.inc"),
        openSourceStageDir.File("lib/cdk/Headers.inc"),
        openSourceStageDir.File("lib/cdk/Makefile.inc"),
        openSourceStageDir.File("lib/libview/Makefile.inc"),
        openSourceStageDir.File("Makefile.am"),
        openSourceStageDir.File("Makefile.inc"),
        openSourceStageDir.File("sdk/Makefile.inc"),
        openSourceStageDir.Dir("doc"),
        openSourceStageDir.Dir("icudata"),
        openSourceStageDir.Dir("m4"),
        openSourceStageDir.Dir("po"),
    ]
    intlToolize = os.path.join(intlToolBinRoot, "intltoolize")
    intltoolizeCmd = "prefix=%s %s -c -f --automake" % (
        stageEnv["INTLTOOL_ROOT"],
        intlToolize,
    )
    nodes += stageEnv.Command(
        openSourceStageDir.File("configure"),
        openSourceConfigureSource,
        vmware.ActionWithDisplay(
            "export PERL5LIB=$PERL5LIB "
            "&& $CD $OPENSOURCEDIR && $INTTLTOOLIZE "
            "&& prefix=$PREFIX $AUTORECONF -f -i -v",
            "Generate open source configure",
        ),
        OPENSOURCEDIR=openSourceStageDir,
        INTTLTOOLIZE=intltoolizeCmd,
        PREFIX=stageEnv["INTLTOOL_ROOT"],
    )
    stageEnv.AddIntlToolDeps(nodes)

    SConscript("#bora/scons/package/hccrt/linux/createOpenSourceBuildEnv.py")

    if vmware.ReleasePackagesDir():
        publishDir = stageEnv.Dir(vmware.ReleasePackagesDir())
        # Make the archive
        openSourceArchive = ArchiveDirectory(
            OPEN_SOURCE_ARCHIVE_NAME, publishDir, stageDir, stageEnv
        )
        stageEnv.NoCacheIfBuildNumberInfoOverridden(openSourceArchive)

        nodes += openSourceArchive
    return nodes


def stageHorizonrxtestClient():
    """publishHorizonrxtestClient
    Pubish the binaries for horizonrxtest client.
    """
    nodes = []
    TARGET_NAME = "horizonrxtestClient"
    hosts = ["linux64"]
    nodeNames = [
        "horizonrxtest",
        "rxci_socket_vchan",
    ]
    publishDir = vmware.ReleasePackagesDir()
    if not publishDir:
        return nodes
    for host in hosts:
        zipSources = []

        # list test app and compiled librarys
        for appName in nodeNames:
            binaryNodes = vmware.pkg.LookupDeliverableNodes(appName, host)
            for node in binaryNodes:
                zipSources.append((node.dir.abspath, node.name))

        # list third party librarys
        rxFilelist = [
            "libcurl.so.*",
            "libssl.so.*",
            "libcrypto.so.*",
            "libz.so.*",
        ]
        horizonrRxTestEnv = vmware.LookupEnv("horizonrxtest-env", "linux64")
        for item in horizonrRxTestEnv["REDIST"]:
            for rule in rxFilelist:
                if fnmatch(os.path.basename(item), rule):
                    zipSources.append((os.path.dirname(item), os.path.basename(item)))
        for item in horizonrRxTestEnv["GTEST_REDIST"]:
            zipSources.append((os.path.dirname(str(item)), os.path.basename(str(item))))

        libstd = horizonrRxTestEnv.GetGCCRedist("libstdc++.so.6")
        zipSources.append((os.path.dirname(libstd), os.path.basename(libstd)))
        horizonrxScriptPath = "#bora/apps/horizonrxtest/componentTest/deploy/"
        deployFiles = [
            "horizonrxtest_deploy.py",
            "horizonrxtest_health_check.py",
            "horizonrxtest_run_cases.py",
            "horizonrxtest_undeploy.py",
        ]
        for file in deployFiles:
            zipSources.append((horizonrxScriptPath, file))
        zipSources.append((horizonrxScriptPath, "utility"))

        zipFile = File(
            os.path.join(
                publishDir, "tests", "horizonrxtest", host, "%s.zip" % TARGET_NAME
            )
        )
        zipNode = stageEnv.Zip(zipFile, zipSources)
        stageEnv.Depends(zipNode, binaryNodes)
        nodes += zipNode
    return nodes


def stageHorizonrxUt():
    """stageHorizonrxUt
    Pubish the binaries for horizonrxUt
    """
    nodes = []
    UT_TARGET_NAME = "horizonrxut"
    hosts = ["linux64"]
    utNodeNames = [
        "horizonrxut",
        "rxSampleUnitTest",
    ]
    publishDir = vmware.ReleasePackagesDir()
    if not publishDir:
        return nodes

    for host in hosts:
        zipSources = []

        # list test app and compiled librarys
        for appName in utNodeNames:
            binaryNodes = vmware.pkg.LookupDeliverableNodes(appName, host)
            for node in binaryNodes:
                zipSources.append((node.dir.abspath, node.name))

        # list third party librarys for ut
        horizonrRxTestEnv = vmware.LookupEnv("horizonrxut-env", "linux64")
        for item in horizonrRxTestEnv["GTEST_REDIST"]:
            zipSources.append((os.path.dirname(str(item)), os.path.basename(str(item))))

        libstd = horizonrRxTestEnv.GetGCCRedist("libstdc++.so.6")
        zipSources.append((os.path.dirname(libstd), os.path.basename(libstd)))
        deploySrc = "#bora/apps/horizonrxtest/componentTest/deploy"
        zipSources.append((deploySrc, "horizonut_deploy.py"))
        zipSources.append((deploySrc, "horizonut_run_cases.py"))
        zipSources.append((deploySrc, "horizonut_lin.ini"))
        zipSources.append((deploySrc, "utility"))
        configPath = "#bora/apps/horizonrxtest/unitTest/sample"
        zipSources.append((configPath, "rxSampleUnitTest.json"))

        zipFile = File(
            os.path.join(
                publishDir, "tests", "horizonrxtest", host, "%s.zip" % UT_TARGET_NAME
            )
        )
        zipNode = stageEnv.Zip(zipFile, zipSources)
        stageEnv.Depends(zipNode, binaryNodes)
        nodes += zipNode
    return nodes


def stagePrintRedirComponentTest():
    """stagePrintRedirComponentTest
    Stage print redirection CI test packages and binaries
    """
    nodes = []
    zipNodes = []
    targetName = "printRedirTest.zip"
    host = "linux64"
    printTestDir = os.path.join(
        vmware.DirAbsPath(vmware.BuildRoot()), vmware.BuildType(), "printRedirTest"
    )

    scriptsSrc = "#bora/apps/printRedir/componentTest/posixClient"
    for node in [
        "prclient",
        "prlinuxcupsppd",
        "prvdpplugin",
        "omnissabaselib",
    ]:
        zipNodes += vmware.pkg.LookupDeliverableNodes(node, host)
    src = os.path.join(
        vmware.GetGobuildComponent(stageEnv.GetGCCComponentName()),
        "usr",
        "x86_64-vmk-linux-gnu",
        "lib64",
        "libstdc++.so.6",
    )
    dst = File("%s/libstdc++.so.6" % printTestDir).abspath
    zipNodes += stageEnv.LinkCopy(dst, src)

    zipNodes += vmware.DirCopy(
        vmware.EnumerateSourceDir(scriptsSrc),
        Dir(scriptsSrc),
        Dir(printTestDir),
        stageEnv,
    )

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(publishDir, "tests", host)
        zipSources = []
        prPluginEnv = vmware.LookupEnv("prvdpplugin-env", host)

        for file in prPluginEnv["REDIST"]:
            if os.path.basename(file).startswith(
                (
                    "libcrypto",
                    "libssl",
                    "libpng",
                    "libz",
                )
            ):
                fileAbsPath = os.path.abspath(file)
                zipSources.append(
                    (os.path.dirname(fileAbsPath), os.path.basename(file))
                )

        for node in zipNodes:
            zipSources.append((node.dir.abspath, node.name))
        nodes += stageEnv.Zip(File(os.path.join(publishDir, targetName)), zipSources)
    return nodes


def stagePrintRedirUnitTest():
    """stagePrintRedirUnitTest
    Stage the binaries for printer redirection UT.
    """
    stageEnv.LoadTool("zip")
    nodes = []
    targetName = ["printredirut"]

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(publishDir, "tests")
        deliverableNodesNames = ["printredirut"]
        extraZipSources = []
        host = "linux64"

        testEnv = vmware.LookupEnv("printredirut-env", host)

        for file in testEnv["REDIST"]:
            if os.path.basename(file).startswith(
                (
                    "libcrypto",
                    "libssl",
                    "libz",
                )
            ):
                fileAbsPath = os.path.abspath(file)
                extraZipSources.append(
                    (os.path.dirname(fileAbsPath), os.path.basename(file))
                )

        zipFilePath = os.path.join(publishDir, host, "printredirut.zip")
        nodes += vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetName,
            host,
            deliverableNodesNames,
            extraZipSources,
            zipFilePath,
            excludedFileFormats=[".map"],
        )
        stageEnv.AddZipDeps(nodes)
    return nodes


def addDepsToZipSources(env, names, zipSources):
    deps = []
    for name in names:
        deps += list(env[name])

    for dep in deps:
        depAbsPath = File(dep).abspath
        zipSources.append(os.path.split(depAbsPath))


def stageClientFIDO2UnitTest():
    """Stage FIDO2 Client unit test library."""

    zipNode = []

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(publishDir, "tests")
        zipSources = []

        binaryNodes = vmware.pkg.LookupDeliverableNodes(
            "fido2ClientUnitTest", "linux64"
        )
        for node in binaryNodes:
            zipSources.append((node.dir.abspath, node.name))

        testEnv = vmware.LookupEnv("fido2ClientUnitTest-env", host="linux64")
        addDepsToZipSources(testEnv, ["OPENSSL_REDIST"], zipSources)
        libzDir = os.path.join(
            vmware.GetConanComponent(stageEnv, "zlib").env["LIBPATH"][0]
        )
        zipSources.append((libzDir, "libz.so.1"))
        libUdevCmp = vmware.GetConanComponent(stageEnv, "libudev")
        zipSources.append((os.path.join(libUdevCmp.env["LIBPATH"][0]), "libudev.so.1"))

        zipFile = File(os.path.join(publishDir, "linux64", "fido2ClientUnitTest.zip"))
        zipNode = stageEnv.Zip(zipFile, zipSources)
        stageEnv.Depends(zipNode, binaryNodes)

    return zipNode


def stageClipboardUnitTest():
    """stageClipboardUnitTest
    Stage the binaries for Clipboard Unit Test.
    """
    nodes = []
    targetName = ["clipboardUnitTest"]
    deliverableNodesNames = [
        "clipboardUnitTest",
        "omnissabaselib",
    ]
    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(publishDir, "tests")

        extraZipSources = []
        host = "linux64"

        testEnv = vmware.LookupEnv("clipboardUnitTest-env", host)
        for ssl_lib in testEnv["OPENSSL_REDIST"]:
            extraZipSources.append(os.path.split(ssl_lib))

        for file in testEnv["REDIST"]:
            if os.path.basename(file).startswith(
                (
                    "libpng",
                    "libsigc",
                    "libz",
                )
            ):
                fileAbsPath = os.path.abspath(file)
                extraZipSources.append(
                    (os.path.dirname(fileAbsPath), os.path.basename(file))
                )

        extraZipSources.append(
            (
                vmware.GetConanComponent(stageEnv, "libfuse").env["LIBPATH"][0],
                "libfuse.so.2",
            )
        )

        zipFilePath = os.path.join(publishDir, host, "clipboardUnitTest.zip")
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetName,
            host,
            deliverableNodesNames,
            extraZipSources,
            zipFilePath,
        )
        if zipNode is not None:
            nodes += zipNode

    return nodes


def stageFcpUnitTest():
    """stageFcpUnitTest
    Stage the binaries for file copy/paste unit test.
    """
    nodes = []
    targetName = ["fcpUnitTest"]
    deliverableNodesNames = [
        "fcpUnitTest",
        "omnissabaselib",
    ]
    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(publishDir, "tests")

        extraZipSources = []
        host = "linux64"

        testEnv = vmware.LookupEnv("fcpUnitTest-env", host)
        for ssl_lib in testEnv["OPENSSL_REDIST"]:
            extraZipSources.append(os.path.split(ssl_lib))

        for file in testEnv["REDIST"]:
            if os.path.basename(file).startswith(
                (
                    "libpcre",
                    "libpng",
                    "libsigc",
                    "libz",
                )
            ):
                fileAbsPath = os.path.abspath(file)
                extraZipSources.append(
                    (os.path.dirname(fileAbsPath), os.path.basename(file))
                )

        extraZipSources.append(
            (
                vmware.GetConanComponent(stageEnv, "libfuse").env["LIBPATH"][0],
                "libfuse.so.2",
            )
        )

        zipFilePath = os.path.join(publishDir, host, "fcpUnitTest.zip")
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetName,
            host,
            deliverableNodesNames,
            extraZipSources,
            zipFilePath,
        )
        if zipNode is not None:
            nodes += zipNode

    return nodes


def stageClipboardClientComponentTest():
    """stageClipboardClientComponentTest
    Stage the binaries for Clipboard Component Test.
    """

    nodes = []
    targetName = ["mksvchanComponentTest"]

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        testEnv = vmware.LookupEnv("mksvchanclient-env", "linux64")

        publishDir = os.path.join(publishDir, "tests")
        deliverableNodesNames = [
            "mksvchanComponentTest",
            "mksvchanclient",
            "vdpservice",
            "omnissabaselib",
        ]

        extraZipSources = []

        for file in testEnv["REDIST"]:
            if os.path.basename(file).startswith(
                (
                    "libcrypto",
                    "libssl",
                    "libpng",
                )
            ):
                fileAbsPath = os.path.abspath(file)
                extraZipSources.append(
                    (os.path.dirname(fileAbsPath), os.path.basename(file))
                )

        extraZipSources.append(
            (
                stageEnv.Dir(
                    "#bora/apps/rde/mksvchan/tests/" "componenttest/clipboard"
                ).abspath,
                "mksvchan_client_linux.ini",
            )
        )
        extraZipSources.append(
            (
                stageEnv.Dir(
                    "#bora/apps/rde/mksvchan/tests/componenttest/" "clipboard"
                ).abspath,
                "configuration",
            )
        )

        testDataSrc = (
            "#bora/apps/rde/mksvchan/tests/componenttest/" "clipboard/testData"
        )
        extraZipSources.append((Dir(testDataSrc).abspath, "."))
        extraZipSources.append(
            (
                vmware.GetConanComponent(stageEnv, "libfuse").env["LIBPATH"][0],
                "libfuse.so.2",
            )
        )

        host = "linux64"
        zipFilePath = os.path.join(publishDir, host, "mksvchanComponentTestClient.zip")
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetName,
            host,
            deliverableNodesNames,
            extraZipSources,
            zipFilePath,
        )
        if zipNode is not None:
            nodes += zipNode

    return nodes


def stageTsdrComponentClientTest():
    """stageTsdrComponentClientTest

    Stage the binaries for Tsdr Component Client Test.
    """

    nodes = []
    targetNames = ["tsdrComponentTest"]

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(vmware.DirAbsPath(publishDir), "tests")
        deliverableNodesNames = [
            "tsdrComponentTest",
            "tsdrClient",
            "vdpservice",
            "omnissabaselib",
        ]
        extraZipSources = []
        extraZipSources.append(
            (
                stageEnv.Dir("#bora/apps/rde/tsdr/tests/componenttest").abspath,
                "tsdr_client_lin.ini",
            )
        )

        host = "linux64"
        testEnv = vmware.LookupEnv("tsdrComponentTest-env", host)
        # Publish 3-part dependency
        for ssl_lib in testEnv["OPENSSL_REDIST"]:
            extraZipSources.append(os.path.split(ssl_lib))
        for file in testEnv["REDIST"]:
            if os.path.basename(file).startswith(
                (
                    "libglibmm",
                    "libpng",
                    "libsigc",
                    "libz",
                )
            ):
                fileAbsPath = os.path.abspath(file)
                extraZipSources.append(
                    (os.path.dirname(fileAbsPath), os.path.basename(file))
                )
        zipFilePath = os.path.join(publishDir, host, "tsdrComponentTestClient.zip")
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetNames,
            host,
            deliverableNodesNames,
            extraZipSources,
            zipFilePath,
        )
        if zipNode is not None:
            nodes += zipNode

    return nodes


def stageTsdrClientUnitTest():
    """stageTsdrClientUnitTest

    Stage the binaries for Tsdr Client Unit Test.
    """

    nodes = []
    targetName = ["tsdrClientUnitTest"]
    deliverableNodesNames = [
        "tsdrClientUnitTest",
        "omnissabaselib",
    ]

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(vmware.DirAbsPath(publishDir), "tests")
        extraZipSources = []

        host = "linux64"
        testEnv = vmware.LookupEnv("tsdrClientUnitTest-env", host)

        # Publish 3-part dependency
        for ssl_lib in testEnv["OPENSSL_REDIST"]:
            extraZipSources.append(os.path.split(ssl_lib))
        for file in testEnv["REDIST"]:
            if os.path.basename(file).startswith(
                (
                    "libglibmm",
                    "libpng",
                    "libsigc",
                    "libz",
                )
            ):
                fileAbsPath = os.path.abspath(file)
                extraZipSources.append(
                    (os.path.dirname(fileAbsPath), os.path.basename(file))
                )
        libUdevDir = vmware.GetConanComponent(testEnv, "libudev").env["LIBPATH"][0]
        extraZipSources.append((libUdevDir, "libudev.so.1"))

        zipFilePath = os.path.join(publishDir, host, "tsdrClientUnitTest.zip")
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetName,
            host,
            deliverableNodesNames,
            extraZipSources,
            zipFilePath,
            excludedFileFormats=[".map"],
        )
        if zipNode is not None:
            nodes += zipNode

    return nodes


def stageTSMMRClientComponentTest():
    """stageTSMMRClientComponentTest

    Stage the binaries for tsmmr Component Test.
    Please note this feature is only support for Windows and Linux platform.
    """

    nodes = []
    targetNames = ["tsmmrComponentTest"]

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(publishDir, "tests")
        deliverableNodesNames = ["tsmmrComponentTest", "tsmmrClient", "vdpservice"]
        host = "linux64"
        extraZipSources = []
        extraZipSources.append(
            (
                stageEnv.Dir(
                    "#bora/apps/rde/tsmmr/tests/componenttest/" "deployScript"
                ).abspath,
                "tsmmr_client_lin.ini",
            )
        )

        zipFilePath = os.path.join(publishDir, host, "tsmmrComponentTestClient.zip")
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetNames,
            host,
            deliverableNodesNames,
            extraZipSources,
            zipFilePath,
        )
        if zipNode is not None:
            nodes += zipNode

    return nodes


def stageSmartcardClientComponentTest():
    """stageSmartcardClientComponentTest

    Stage the binaries for Smartcard Component Test.
    Please note this feature is only support for MAC and Linux platform.
    """

    nodes = []
    targetNames = ["scredirvchanComponentTest"]

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(publishDir, "tests")
        deliverableNodesNames = ["scredirvchanComponentTest", "scredirvchan", "sccheck"]
        extraZipSources = []
        testDataSrc = "#bora/apps/rde/scredirvchan/tests/componenttest"
        extraZipSources.append(
            (stageEnv.Dir(testDataSrc).abspath, "scredirvchan_client_linux.ini")
        )
        extraZipSources.append((Dir(testDataSrc).abspath, "testData"))

        host = "linux64"
        zipFilePath = os.path.join(
            publishDir, host, "scredirvchanComponentTestClient.zip"
        )
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetNames,
            host,
            deliverableNodesNames,
            extraZipSources,
            zipFilePath,
        )
        if zipNode is not None:
            nodes += zipNode

    return nodes


buildRoot = vmware.BuildRoot()
buildType = vmware.BuildType()

# Intermediate directory that will not be staged to gobuild component exports
# or Deliverables page.
buildIntDir = (
    vmware.Dir(vmware.BuildRoot()).Dir(vmware.BuildType()).Dir(vmware.Product())
)

stageEnv = vmware.Host().DefaultEnvironment()
stageEnv.LoadTool("python")
stageEnv.LoadTool("findutils")
stageEnv.LoadTool("dpkg")

clientVersion = vmware.ProductVersionNumber()
marketVersion = vmware.ProductVersionNumber("HorizonClientYYMM")
stageDir = stageEnv.Dir(vmware.pkg.stagePath)
scriptPath = stageDir.Dir("vmis/scripts")
vmisDir = stageDir.Dir("vmis")
vmisSource = stageDir.Dir("vmis/tar/install/vmware-installer")
vmisVersion = "2.1.0"

vmware.pkg.ArchiveDirectory = ArchiveDirectory
vmware.pkg.clientVersion = clientVersion
vmware.pkg.CopyFilesInList = CopyFilesInList
vmware.pkg.debPackageDirName = "omnissa-horizon-client-deb"
vmware.pkg.DirLinkCopy = DirLinkCopy
vmware.pkg.GetFileForPattern = GetFileForPattern
vmware.pkg.GetGobuildComponentPath = GetGobuildComponentPath
vmware.pkg.GetLibPackageInfo = GetLibPackageInfo
vmware.pkg.marketVersion = marketVersion
vmware.pkg.scriptPath = scriptPath
vmware.pkg.stageDir = stageDir
vmware.pkg.stageEnv = stageEnv
vmware.pkg.vmisDependency = "vmware-installer=%s" % vmisVersion
vmware.pkg.vmisDir = vmisDir
vmware.pkg.vmisSource = vmisSource

# We don't stage anything to gobuild component exports right now, so we must
# create an empty directory for builds to succeed.
try:
    os.makedirs(vmware.DirAbsPath(vmware.pkg.stagePath))
except os.error:
    # This is usually thrown if the directory already exists.
    pass

if vmware.ReleasePackagesDir():
    publishDir = stageEnv.Dir(vmware.ReleasePackagesDir())

stageEnv.LoadTool("intltool")
stageEnv.LoadTool("gettext")
intlToolBinRoot = os.path.join(stageEnv["INTLTOOL_ROOT"], "bin")
intlToolUpdate = os.path.join(intlToolBinRoot, "intltool-update")
intlToolMerge = os.path.join(intlToolBinRoot, "intltool-merge")
intlToolExtract = os.path.join(intlToolBinRoot, "intltool-extract")
VMWARE_HORIZON_CLIENT_ARCHIVE_NAME = "Omnissa-Horizon-Client-Linux-%s-%s-%s" % (
    marketVersion,
    clientVersion,
    vmware.BuildNumber(),
)
vmHzCltDir = stageDir.Dir(VMWARE_HORIZON_CLIENT_ARCHIVE_NAME)
vmware.pkg.vmHzCltDir = vmHzCltDir

componentNodeParse()

allNodes = []
# allNodes += stageBundleInstaller()
allNodes += stageDebInstaller()
allNodes += stageRpmInstaller()
# allNodes += stageFabulatechJsonFile()


if not vmware.LocalOpts.GetBool("ENABLE_CODE_COV", False):
    # For coverage build, don't publish
    allNodes += stageSDKArchive()
    allNodes += stageUSBSDKArchive()
    allNodes += stageVMHZCltArchiveAll()
    allNodes += stageOpenSourceArchive()
    allNodes += stagePOTFile()
    allNodes += SConscript("#bora/scons/package/hccrt/linux/symbols.py")
allNodes += stageRmksStubArchive()
allNodes += stageCrtboraTestBinaries()
allNodes += stageRdeClientTest()
allNodes += stageDctClientComponentTest()
allNodes += stageUnitTest()
allNodes += stageViewClientApiTest()
allNodes += stageClientUICdkSdkUnitTest()
allNodes += stageRTAVUnitTest()
allNodes += stageRTAVClientComponentTest()
# allNodes += SConscript("#bora/scons/package/hccrt/linux/symbols.py")
allNodes += stageHorizonrxtestClient()
allNodes += stageHorizonrxUt()
allNodes += stagePrintRedirComponentTest()
allNodes += stagePrintRedirUnitTest()
allNodes += stageClientFIDO2UnitTest()
allNodes += stageURLComponentTestClient()
allNodes += stageURLUnitTest()
allNodes += stageClientUSBUnitTest()
allNodes += stageUSBClientComponentTest()
allNodes += stageClipboardUnitTest()
allNodes += stageClipboardClientComponentTest()
allNodes += stageFcpUnitTest()
allNodes += stageTsdrComponentClientTest()
allNodes += stageTsdrClientUnitTest()
allNodes += stageTSMMRClientComponentTest()
allNodes += stageSmartcardClientComponentTest()
# Stage cc data at the end to include all data.
allNodes += vmware.pkg.StageCodeCoverageData(stageEnv, "hclin", allNodes)

if not vmware.LocalOpts.GetBool("ENABLE_CODE_COV", False):
    # For coverage build, don't publish
    viewClientNodes = stageViewClient()
    vmware.Alias("viewClient", viewClientNodes)

    allNodes += viewClientNodes

vmware.RegisterNode(allNodes, "hclin")
vmware.Alias("hccrt", allNodes)
vmware.Alias("hclin", allNodes)
