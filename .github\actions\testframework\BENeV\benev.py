# Copyright (c) Omnissa, LLC. All rights reserved.
# This product is protected by copyright and intellectual property laws in the
# United States and other countries as well as by international treaties.
# -- Omnissa Restricted

import argparse
import glob
import os
import shutil
import sys
import xml.etree.ElementTree as ET
try:
    import simplejson as json
except ImportError:
    import json


class BENeV():
    """A class for operations related to BENeV tests."""

    def execute_benev(self, benev_test_type='stable', do_mem_leak_check=True,
                      benev_gflags=False, benev_test_grep=None,
                      opencpp_args=None, stable_benev_path=None, topology=None,
                      enable_raw_channel=False):
        """Setup the environment and run the mocha tests."""
        npmTestMod = sys.platform[:3]

        if stable_benev_path is not None:
            stable_benev_path = os.path.abspath(stable_benev_path)
            stable_benev_peer_path = '{}/benev/blastSocketPeer/x64/benevPeer{}'.format(
                stable_benev_path, '.exe' if sys.platform == 'win32' else '')

        os.chdir(os.path.join('benev', 'nodejs'))

        benev_peer_path = ('../blastSocketPeer/x64/benevPeer{}'
                           .format('.exe' if sys.platform == 'win32' else ''))
        benev_peer_dir = os.path.dirname(benev_peer_path)

        if sys.platform.startswith("linux"):
            os.system('chmod +x {}'.format(benev_peer_path))
            if stable_benev_path is not None:
                os.system('chmod +x {}'.format(stable_benev_peer_path))
            # linux needs to copy below libs while windows finds them in appblastlibs package
            for file in glob.glob('../../test/x64/ut/libssl*'):
               shutil.copy(file, benev_peer_dir)
            for file in glob.glob('../../test/x64/ut/libcrypto*'):
               shutil.copy(file, benev_peer_dir)

        # We *must* use the internal NPM registry
        self.setup_npm()

        mocha_opts = [
            '--benevPeerPath={}'.format(benev_peer_path),
            '--doMemLeakCheck={}'.format('true' if do_mem_leak_check else 'false'),
            '--bwCapperPath={}'.format('bandwidth-capping-proxy.js')
        ]
        if topology is not None:
            mocha_opts.extend([
                '--stableBenevPeerPath={}'.format(stable_benev_peer_path),
                '--topology=topologies/{}'.format(topology)
            ])
        if enable_raw_channel:
            mocha_opts.append('--enableRawChannel')

        if (sys.platform.startswith("win") and benev_gflags):
            os.system('C:/"Program Files (x86)\\Windows Kits\\10\\Debuggers\\x64\\gflags.exe " ' +
                      '/p /enable benevpeer.exe /full')

        if benev_test_grep is not None:
            mocha_opts.append('--grep="{}"'.format(benev_test_grep))
            os.system("npm run allTests-{} -- {}".format(npmTestMod, ' '.join(mocha_opts)))

        elif benev_test_type == 'stable':
            if sys.platform.startswith("linux"):
                import resource

                # Bump our corefile size as high as we can -- to our "hard"
                # limit
                _, core_hard_limit = resource.getrlimit(resource.RLIMIT_CORE)
                resource.setrlimit(resource.RLIMIT_CORE, (core_hard_limit,
                                                          core_hard_limit))

            cmd = 'npm'
            if opencpp_args is not None:
                # if we're using opencpp, we need to pass the absolute
                # path to npm to OpenCppCoverage
                cmd = '{} c:\\PROGRA~1\\nodejs\\npm.cmd'.format(opencpp_args)

            os.system("{} run test-{} -- {}".format(cmd, npmTestMod, ' '.join(mocha_opts)))

        elif benev_test_type == 'unstable':
            os.system("npm run unstable-{} -- {}".format(npmTestMod, ' '.join(mocha_opts)))

        if (sys.platform.startswith("win") and benev_gflags):
            os.system('C:/"Program Files (x86)\\Windows Kits\\10\\Debuggers\\x64\\gflags.exe " ' +
                      '/p /disable benevpeer.exe /full')


    def get_test_cases(self, output):
        """Get BENeV stable test case list and output to file."""
        os.chdir(os.path.join('benev', 'nodejs'))

        # install benev dependencies
        self.setup_npm()

        # install + run mocha-decaf
        os.system('npm install mocha-decaf')
        os.system('node ./node_modules/mocha-decaf/main.js -b --exit '
                  '--reporter mocha-junit-reporter test.js '
                  '--grep="Unstable|MX" --invert')

        test_list = []
        tree = ET.parse('test-results.xml')
        root = tree.getroot()
        for testsuite in root.iter('testsuite'):
            if testsuite.attrib['tests'] == '0' and \
                    testsuite.attrib['name'] != 'Root Suite':
                test_list.append(testsuite.attrib['name'])

        with open(output, 'w') as output_file:
            output_file.write(json.dumps(test_list))


    def setup_npm(self):
        """Run npm install using internal registry"""
        # npm config set will save whatever we pass in the user's .npmrc, so we
        # need to reference the env var for the token instead of putting it in
        # plaintext
        if sys.platform.startswith("win"):
            artifactory_token_env = "%ARTIFACTORY_READ_TOKEN%"
        else:
            artifactory_token_env = "$ARTIFACTORY_READ_TOKEN"

        os.system("npm config set "
                  "//artifactory.build.omnissa.com/artifactory/api/npm/:_authToken " +
                  artifactory_token_env)
        os.system("npm config set registry "
                  "https://artifactory.build.omnissa.com/artifactory/api/npm/npm")
        os.system("npm install")


def main(argv):
    """
    main() for benev.py, will be executed if this file is called
    directly.
    """
    parser = argparse.ArgumentParser()
    parser.add_argument('--execute_benev', action='store_true',
                        help='Execute benev test cases')
    parser.add_argument('--benev_test_type', choices=['stable', 'unstable'],
                        default='stable')
    parser.add_argument('--disable_mem_leak_check', action='store_true')
    parser.add_argument('--enable_raw_channel', action='store_true')
    parser.add_argument('--enable_benev_gflags', action='store_true')
    parser.add_argument('--get_test_cases', action='store_true',
                        help='List test cases without running them')
    parser.add_argument('--output',
                        help='File to output test case list to')
    parser.add_argument('--benev_test_grep',
                        help='Test cases to pass to mocha via --grep')
    parser.add_argument('--opencppcoverage_args',
                        help='Run the tests with OpenCppCoverage. Expects '
                             'the full list of arguments to opencpp, like '
                             '"OpenCppCoverage --export_type:binary --"')
    parser.add_argument('--stable_benev_path',
                        help='Path to root dir of stable (old) benev')
    parser.add_argument('--topology',
                        help='Topology json file')
    args = parser.parse_args()

    my_benev = BENeV()
    if args.execute_benev:
        my_benev.execute_benev(args.benev_test_type,
                               not args.disable_mem_leak_check,
                               args.enable_benev_gflags,
                               args.benev_test_grep,
                               args.opencppcoverage_args,
                               args.stable_benev_path,
                               args.topology,
                               args.enable_raw_channel,)
    if args.get_test_cases:
        my_benev.get_test_cases(args.output)


if __name__ == "__main__":
    main(sys.argv[1:])