{"euc_endpoint_telemetry_sdk": {"repository": "euc-uem/endpoint-telemetry-yukon", "workflow": "tlm-win", "branch": "main", "change": "6c1b4ab5f714d9e2a6f97b8f873b139071b499d4", "job": "build", "buildtypes": {"beta": "release", "obj": "obj", "release": "release"}}, "pcoip-soft-psg": {"repository": "euc-ra/psg", "workflow": "pcoip-soft-psg", "branch": "main", "change": "a25898417bb15a01d218c812626cb6fad6afdc13", "buildtypes": {"obj": "obj", "beta": "beta", "release": "release"}, "job": {"Linux": "build-pcoip-soft-psg-lnxbuild-gh", "Windows": "build-pcoip-soft-psg-winhzn-gh", "Darwin": "build-pcoip-soft-psg-lnxbuild-gh"}, "unzipFiles": "True"}, "pcoip-soft-server": {"repository": "euc-ra/pcoip-soft-server", "workflow": "pcoip-soft-server", "branch": "main", "change": "5379ce7e6e03f6ad167608333f5cb7fab4a3efc9", "job": {"Windows": "build-pcoip-soft-server-winhzn-gh", "Darwin": "build-pcoip-soft-server-winhzn-gh"}, "buildtypes": {"obj": "beta", "beta": "beta", "release": "release"}, "unzipFiles": "True"}, "pcoip-soft-interface": {"repository": "euc-ra/pcoip-soft-server", "workflow": "pcoip-soft-interface", "job": "build-pcoip-soft-interface", "buildtypes": "release", "branch": "v4.1.0", "change": "db6bca551a9da828f5cb41dd9fece193da8d090c", "unzipFiles": "True"}, "vmware-horizon-logger": {"repository": "euc-vdi/horizon-commonlogger", "branch": "master", "change": "2c1e4a1a41256dae27129e1941341573e09e2939", "workflow": "build", "job": "build", "unzipFiles": "True"}, "vmware-swiftmq": {"repository": "euc-ra/horizon-swiftmq", "branch": "release/jdk17-rebranded", "change": "c172a1ced97c83c2dc7e4e25cb1e4fbd67dd7dc9", "workflow": "build", "job": "build-swiftmq", "unzipFiles": "True"}, "vmware-swiftmq-jdk11": {"repository": "euc-ra/horizon-swiftmq", "branch": "main", "change": "d9b294eeb7e9f79ffbb61ca35849154f836358a3", "workflow": "build", "job": "build-swiftmq", "unzipFiles": "True"}, "windows-installkit": {"repository": "euc-vdi/windows-installkit", "workflow": "windows-installkit", "branch": "master", "change": "aaa4f5dfef11b012f832e7cc947153c034bdbc66", "job": "build-windows-installkit", "buildtypes": {"obj": "obj", "beta": "beta", "release": "release"}}, "cart-webrtc": {"repository": "euc-vdi/cart-webrtc", "workflow": "cart_webrtc", "branch": "main", "change": "09d0be2d5f94b1a5b3fedc21c4f16b4e7d11487a", "job": "build-cart-webrtc-win", "unzipFiles": "True", "buildtypes": {"beta": "beta", "obj": "obj", "opt": "release", "release": "release"}}, "msteamsapi": {"repository": "euc-eng/msteamsapi", "workflow": "m<PERSON><PERSON><PERSON><PERSON>", "branch": "master", "change": "3074e2bed7868ce54cd71517c81bfc7477a8cfc4", "job": "build-msteamsapi-winhzn-gh", "buildtypes": "release", "unzipFiles": "False"}}