# Copyright (c) Omnissa, LLC. All rights reserved.
# This product is protected by copyright and intellectual property laws in the
# United States and other countries as well as by international treaties.
# -- Omnissa Restricted

"""boraness.py

This file generates the preprocessor definitions for the build environment when
compiling C and C++ code.
"""


def defines(env):
    defines = {}
    buildtype = env.BuildType()

    debug = buildtype == "obj" or buildtype == "beta"
    devel = buildtype == "obj" or buildtype == "opt"
    log = buildtype == "obj"
    beta = buildtype == "beta"
    release = buildtype == "release"
    opt = buildtype == "opt"

    if devel:
        defines.update({"VMX86_DEVEL": None})

    if debug:
        defines.update({"VMX86_DEBUG": None})
    if log:
        defines.update({"VMX86_LOG": None})

    # VMX86_TOOLS preprocessor definition is still relied up on in places, especially in
    # bora-vmsoft. It isn't ideal (because we are Omnissa now, not VMware), but
    # we need to keep it for compatibility with existing code.
    if env.Host().IsGuest():
        defines.update({"VMX86_TOOLS": None})

    # Special build-specific pieces.
    if release:
        defines.update({"VMX86_RELEASE": None})
    elif beta:
        defines.update({"VMX86_BETA": None})
    elif opt:
        defines.update({"VMX86_OPT": None})

    if env.Host().IsLinux() and not env.Host().IsKernelModule():
        # These flags are used across all bora code to deal with various
        # funniness in glibc and friends
        #
        # Note: g++ is calling cc1plus internally with -D_GNU_SOURCE, and
        # because glibc features.h in included via vm_cpp11.h,
        # _XOPEN_SOURCE is actually set to 700 for all C++ code.
        defines.update(
            {
                "_XOPEN_SOURCE": "600",
                "_BSD_SOURCE": None,
                "_SVID_SOURCE": None,
                "_DEFAULT_SOURCE": None,
                "_LARGEFILE64_SOURCE": None,
                "_FILE_OFFSET_BITS": "64",
            }
        )

    return defines


def generate(env):
    """ """
    env.Append(
        CPPDEFINES=defines(env),
    )
