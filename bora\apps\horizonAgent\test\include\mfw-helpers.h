/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */
#pragma once

/*
 * mfw-helpers.h
 *
 *    Helpful defines when working with vmock and mfw.
 */

// Function pointers for overloaded functions
typedef CORE::MessageHandler::respType (CORE::MessageFrameWork::*FPSendMsg)(
   LPCTSTR, LPCTSTR, const CORE::PropertyBag &, CORE::PropertyBag &,
   CORE::MessageHandler::PRESPFRAGMENT, void *, CORE::MessageChannel *, DWORD, HANDLE,
   CORE::MsgBinary *, bool, CORE::MsgBinary *, CORE::tstr *, bool);

typedef CORE::MessageHandler::respType (CORE::MessageFrameWork::*FPSendMsg3)(LPCTSTR, LPCTSTR,
                                                                             CORE::PropertyBag &);

typedef void (CORE::MessageFrameWork::*FPPostMsg)(LPCTSTR, LPCTSTR, const CORE::PropertyBag &,
                                                  CORE::MessageChannel *, CORE::MsgBinary *, bool,
                                                  bool);

typedef void (CORE::MessageFrameWork::*FPPostMsg2)(LPCTSTR, LPCTSTR);

typedef CORE::wstr (*FPreadRegistryPolicyOverride)(const wchar_t *, const wchar_t *, bool);

typedef CORE::wstr (*FPreadRegistry)(const wchar_t *, const wchar_t *, bool);

typedef unsigned long (CORE::wstr::*FPreadRegistryNonStatic)(const wchar_t *, unsigned long, bool);

typedef bool (CORE::wstr::*FPwriteRegistry)(const wchar_t *, unsigned long, bool) const;

typedef CORE::MessageChannel *(CORE::MessageFrameWork::*FPConnectChannel)(
   CORE::MessageFrameWork::channelTypes, LPCTSTR, LPCTSTR, LPCTSTR, LPCTSTR, LPCTSTR, LPCTSTR,
   unsigned short, CORE::MessageFrameWork::channelErrorTypes *, CORE::PropertyBag *);

typedef void (CORE::MessageFrameWork::*FPCloseChannel)(CORE::MessageChannel *);

// Matchers
MATCHER_P(msgchannelMatch, param, "Cast to CORE::MessageChannel* and compare")
{
   return arg == reinterpret_cast<CORE::MessageChannel *>(param);
}

MATCHER_P(propertyBagMatch, param, "Compare two PropertyBag objects")
{
   return *param == arg;
}

MATCHER_P(propertyNameExists, propertyName, "The bag contains property name")
{
   PropertyBag bagCopy = arg;
   return bagCopy.contains(propertyName);
}

MATCHER_P(propertyNameDoesntExist, propertyName, "The bag doesn't contain property name")
{
   PropertyBag bagCopy = arg;
   return !bagCopy.contains(propertyName);
}

MATCHER_P(regPathContains, regKeyName, "Check regKeyName in path")
{
   wstr regPath = arg;
   return regPath.findi(regKeyName) != -1;
}

MATCHER_P2(propertyNameEquals, propertyName, value,
           "The bag contains the given property and that value is expected")
{
   PropertyBag bagCopy = arg;
   return bagCopy.get(propertyName, L"").compare(value) == 0;
}

MATCHER_P2(propertySubBagMatch, subBagName, param, "Compare a subBag with a PropertyBag object")
{
   PropertyBag bagCopy = arg;
   PropertyBag childBag = bagCopy.getBag(subBagName);
   return childBag == *param;
}

MATCHER_P4(twoPropertyNamesEqual, propertyName1, value1, propertyName2, value2,
           "The bag contains the given properties and those values are expected")
{
   PropertyBag bagCopy = arg;
   return ((bagCopy.get(propertyName1, L"").compare(value1) == 0) &&
           (bagCopy.get(propertyName2, L"").compare(value2) == 0));
}
