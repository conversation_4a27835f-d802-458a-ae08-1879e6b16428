/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

#include "stdafx.h"

#include <common/i18n/StringConverter.h>

namespace coreutil = svmga::core::util;
namespace i18n = svmga::common::i18n;
using namespace svmga::core::util;

VmType MachineType::_vmType = VmType::Unknown;

MachineType::MachineType() :
   _GuestInfo(new coreutil::GuestInfo()),
   _cpi(coreutil::CustomizationPersistentInfo::GetInstance())
{}

VmType
MachineType::GetMachineType()
{
   if (_vmType != VmType::Unknown) {
      return _vmType;
   }

   if (_cpi->IsUniversalPrep()) {
      AgentHelperWrapper *ahu = coreutil::AgentHelperWrapper::GetInstance();

      // TODO: UBI-261 (https://omnissa.atlassian.net/browse/UBI-261)
      // Mac address should be provided by a refactor'ed instance (remove redundancy in other
      // projects).
      std::wstring masterMacAddr = _cpi->GetGoldenImageMacAddress();
      std::wstring currentMacAddr = ahu->GetCurrentMacAddress();

      if (masterMacAddr.length() == 0) {
         // First time to launch ga service in golden image.
         _cpi->MarkGoldenImageMacAddress(currentMacAddr);
         SYSMSG_FUNC(Debug, _T("Saved master/golden image mac address: %ws"),
                     currentMacAddr.c_str());

         // No need another trip to registry.
         masterMacAddr = currentMacAddr;
      }

      // TODO: Replace the logic with agentHelper's LCM determination.
      if (masterMacAddr.compare(currentMacAddr) == 0) {
         _vmType = VmType::Master;
      } else if (!_cpi->IsTemplateCustomizationDone()) {
         _vmType = VmType::InternalTemplate;
      } else {
         // Mac address changed, and IT customization done. Must be a clone.
         _vmType = VmType::Clone;
      }
   } else {
      // To support legacy vSphere-based hypervisor through backdoor
      // Following block contains the logic to identify which type of
      // internal vm the ga is booted into.
      //

      if (_GuestInfo->IsGuestInfoVmStateSet("ngaFork")) {
         if (_GuestInfo->IsGuestInfoVmStateSet("forked")) {
            if (_cpi->IsVmForked()) {
               //
               // guestinfo.ngaFork == 1
               // guestinfo.forked == 1
               // VmForked == 1
               //
               _vmType = VmType::Clone;
            }
         } else if (_GuestInfo->IsGuestInfoVmStateSet("replica")) {
            //
            // guestinfo.replica == 1
            //
            _vmType = VmType::Replica;
         } else if (_GuestInfo->IsGuestInfoVmStateSet("it")) {
            //
            // guestinfo.it == 1
            //
            _vmType = VmType::InternalTemplate;
         } else {
            //
            // guestinfo.ngaFork == 1
            // guestinfo.forked == 0
            //
            _vmType = VmType::Parent;
         }
      } else if (_GuestInfo->IsGuestInfoVmStateSet("it")) {
         //
         // guestinfo.it == 1
         //
         _vmType = VmType::InternalTemplate;
      } else if (_GuestInfo->IsGuestInfoVmStateSet("replica")) {
         //
         // guestinfo.replica == 1
         //
         _vmType = VmType::Replica;
      } else {
         //
         // guestinfo.ngaFork == 0
         // guestinfo.it == 0
         // guestinfo.replica == 0
         //
         _vmType = VmType::Master;
      }
   }

   return _vmType;
}

std::wstring
MachineType::GetMachineTypeStringW()
{
   return i18n::StringConverter::Utf8ToWString(GetMachineTypeString());
}

std::string
MachineType::GetMachineTypeString()
{
   switch (_vmType) {
   case VmType::Master:
      return std::string(MACHINE_TYPE_MASTER);

   case VmType::InternalTemplate:
      return std::string(MACHINE_TYPE_IT);

   case VmType::Replica:
      return std::string(MACHINE_TYPE_REPLICA);

   case VmType::Parent:
      return std::string(MACHINE_TYPE_PARENT);

   case VmType::Clone:
      return std::string(MACHINE_TYPE_CLONE);
   };

   return std::string(MACHINE_TYPE_UNKNOWN);
}

bool
MachineType::IsMaster()
{
   GetMachineType();
   return _vmType == VmType::Master ? true : false;
}

bool
MachineType::IsInternalTemplate()
{
   GetMachineType();
   return _vmType == VmType::InternalTemplate ? true : false;
}

bool
MachineType::IsReplica()
{
   GetMachineType();
   return _vmType == VmType::Replica ? true : false;
}

bool
MachineType::IsParent()
{
   GetMachineType();
   return _vmType == VmType::Parent ? true : false;
}

bool
MachineType::IsClone()
{
   GetMachineType();
   return _vmType == VmType::Clone ? true : false;
}