/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */
#pragma once

#include "utilHttpRequest.h"
#include "utilIESettings.h"

#ifndef WINHTTP_NO_OPENSSL
#   include "sslHandler.h"
#endif

#include <coreref.h>
#include <fstream>
#include <functional>
#include <cedar/windows/resource.h>
#include <cedar/windows/resource_winhttp.h>
#include <mutex>

/*
 * Definitions for the WinHttpRequest class
 *   Implements the HttpRequest class using WinHTTP API
 */
using namespace cedar::windows;


class WinHttpRequest : public HttpRequest, public CORE::coreref {
public:
   /*
    * WinHttpRequest()
    *
    *   Constructor
    */
   WinHttpRequest();
   WinHttpRequest(bool ignoreAllCertErrors, bool enableCertRevocation,
                  std::map<std::string, std::string> headers,
                  std::multimap<std::string, std::string> queryParams);

   /*
    * ~WinHttpRequest()
    *
    *   Destructor
    */
   virtual ~WinHttpRequest();

   /*
    * getSync()
    *
    *   Perform a synchronous GET request
    */
   bool getSync(const std::string &url, std::string &response) override;

   /*
    * postSync()
    *
    *   Perform a synchronous POST request
    */
   bool postSync(HttpRequest::CONTENT_TYPE contentType, const std::string &url,
                 const std::string &body, std::string &response) override;

   /*
    * putSync()
    *
    *   Perform a synchronous PUT request
    */
   bool putSync(HttpRequest::CONTENT_TYPE contentType, const std::string &url,
                const std::string &body, std::string &response) override;

   /*
    * putSyncFile
    *
    *   Perform a synchronous PUT request. Send the contents of the file as
    *   part of the message body
    */
   bool putSyncFile(HttpRequest::CONTENT_TYPE contentType, const std::string &url,
                    const std::string &filename, std::string &response) override;

   /*
    * deleteSync
    *
    *   Perform a synchronous DELETE request
    */
   bool deleteSync(const std::string &url, std::string &response) override;

   /*
    * getAsync()
    *
    *   Perform an asynchronous GET request
    */
   bool getAsync(const std::string &url,
                 std::function<void(HttpRequest *, std::string)> cb) override;

   /*
    * postAsync()
    *
    *   Perform an asynchronous POST request
    */
   bool postAsync(HttpRequest::CONTENT_TYPE contentType, const std::string &url,
                  const std::string &body,
                  std::function<void(HttpRequest *, std::string)> cb) override;

   /*
    * putAsync()
    *
    *   Perform an asynchronous PUT request
    */
   bool putAsync(HttpRequest::CONTENT_TYPE contentType, const std::string &url,
                 const std::string &body,
                 std::function<void(HttpRequest *, std::string)> cb) override;

   bool putAsync(HttpRequest::CONTENT_TYPE contentType, const std::string &url,
                 const std::vector<char> &body, size_t bodySize,
                 std::function<void(HttpRequest *, std::string)> cb) override;

   /*
    * putAsyncFile()
    *
    *   Perform an asynchronous PUT request, the contents of a given file will
    *   be uploaded as part of the HTTP request.
    */
   bool putAsyncFile(HttpRequest::CONTENT_TYPE contentType, const std::string &url,
                     const std::string &filename,
                     std::function<void(HttpRequest *, std::string)> cb) override;

   /*
    * ignoreAllCertErrors()
    *
    *   Indicates to the class that certificate errors should be ignored
    */
   void ignoreAllCertErrors() override;

   /*
    * ignoreWrongHostCertError()
    *
    *   Indicate that wrong host certificate errors should be ignored
    */
   void ignoreWrongHostCertError() override;

   /*
    * buildChainUsingStore()
    *
    *   Used in conjunction with the 'setCAThumbprints' method. If we're unable
    *   to get the certificate chain due to using an older OS, then try to build
    *   the certificate chain using the provided cert store. The certificate
    *   store is not closed; closing the store is the responsibility of the
    *   caller.
    */
   void buildChainUsingStore(void *certStore) override;

   /*
    * enableCertRevocation()
    *
    *   Indicates to the class that certificate revocation checks should be
    * made.
    */
   void enableCertRevocation() override;

   /*
    * setHeaders()
    *
    *   Set the HTTP headers to use in the request
    */
   void setHeaders(const std::map<std::string, std::string> &headers) override;

   /*
    * setQueryParams()
    *
    *   Set the query parameters
    */
   void setQueryParams(const std::multimap<std::string, std::string> &params) override;

   /*
    * setCAThumbprints()
    *
    *   Sets the thumbprints for the server. If, during the HTTP request, the
    *     thumbprints given through this function do not match the thumbprints
    *     obtained from the server, then the request will fail.
    */
   void setCAThumbprint(const std::string &thumbprint) override;

   /*
    * setCATrustStore()
    *
    *    Give a PEM-encoded list of Certificate Authorities to trust when making
    *    a connection. Multiple Certificate Authories are accepted.
    *
    *    string example:
    *    -----BEGIN CERTIFICATE-----
    *    ...
    *    -----END CERTIFICATE-----
    *    ...
    *    -----BEGIN CERTIFICATE-----
    *    ...
    *    -----END CERTIFICATE
    *    ...
    */
   void setCATrustStore(const std::string &CAStore) override;
   void setCATrustStore(const std::vector<std::string> &CAStoreThumbprints) override;

   void setTimeout(unsigned int timeout_s) override;

   void setProxyServerList(const std::string &proxyList) override;

   void setProxyBypass(const std::string &proxyBypass) override;

   void setProxyCredentials(const std::wstring &username, const std::wstring &password) override;

   void setProxyPACURL(const std::string &proxyPACURL);

   bool cancelRequest() override;

   /*
    * getResponseCode()
    *
    *   Return the response code received
    */
   unsigned int getResponseCode() override;

   /*
    * getResponseHeaders()
    *
    *   Return the HTTP headers of the response
    */
   std::string getResponseHeaders() override;

   /*
    * forceCertStoreChain
    *
    *   Try to build the server's certificate chain using ONLY the provided
    *   certificate store. Used in conjunction with buildChainUsingStore() and
    *   setCAThumbprints().
    */
   void forceCertStoreChain();

protected:
   virtual void finalizeHeaders();

   virtual bool requestSucceeded();

   CORE::wstr m_action;
   CORE::wstr m_fullURL;
   CORE::wstr m_hostName;
   CORE::wstr m_urlPath;
   CORE::wstr m_extraInfo;
   CORE::wstr m_proxyServerList;
   CORE::wstr m_proxyBypass;
   CORE::wstr m_proxyUser;
   CORE::wstr m_proxyPw;
   CORE::wstr m_proxyPACURL;
   std::vector<char> m_body;
   std::string m_uploadFileName;
   std::map<std::string, std::string> m_headers;
   std::multimap<std::string, std::string> m_queryParams;
   unsigned int m_responseCode;
   CORE::wstr m_responseHeaders;
   unsigned long long m_totalDataToSend;
   // We don't close this in order to improve performance. Otherwise we would
   // need to close and reopen the store after every request
   HCERTSTORE m_certStore;
   bool m_onlyUseProvidedStore;

   IEProxyConfig m_IEProxySettings;

private:
   void CloseHandles();

   enum ACTION { GET = 0, POST, PUT, Delete };

   void AddContentTypeHeader(HttpRequest::CONTENT_TYPE contentType);

   bool performAsync(ACTION action, const std::string &url,
                     std::function<void(HttpRequest *, std::string)> cb);

   bool performSync(ACTION action, const std::string &url, const std::string &body = std::string(),
                    const std::string &filename = std::string());

   void importQueryParams(CORE::wstr queryString);

   /*
    * getHeaderStr()
    *
    *   Get the header string and length. Derived from the headers set in
    *     setHeaders().
    */
   void getHeaderStr(std::string &header, size_t &headerLen) const;

   /*
    * getQueryParamStr()
    *
    *   Get the query parameter string. Derived from setQueryParams().
    */
   void getQueryParamStr(std::string &str, size_t &queryParamLen) const;

   bool SendRequestComplete();

   bool ReceiveResponse();

   bool UploadData();

   bool WriteComplete(DWORD bytesWritten);

   bool CheckHeaders();

   bool DataAvailable(DWORD len);

   bool DataComplete(LPSTR buff, DWORD len);

   bool QueryData();

   bool SendingRequest();

   void RequestCallback(HINTERNET handle, DWORD code, void *info, DWORD length);

   static void CALLBACK WinHttpCallback(HINTERNET handle, DWORD_PTR context, DWORD code, void *info,
                                        DWORD length);

   static void Synchronize(HttpRequest *req, std::string resp);

   PCCERT_CHAIN_CONTEXT GetCertChain();

   wstr getPACURL();

   bool getProxyURL(const wstr &PACURL, NetProxyConfig &proxyConfig);

   bool m_ignoreAllCertErrors;
   bool m_ignoreWrongHostError;
   bool m_enableCertRevocation;
   bool m_serverCertVerified;

   std::vector<std::string> m_CAThumbprints;
   unique_internet m_hSession;
   unique_internet m_hConnect;
   unique_internet m_hRequest;
   std::string m_response;

   unique_event m_SyncEvent;
   unique_event m_cancelEvent;
   // temporary storage for a canceled request
   HANDLE m_canceledRequest;
   bool m_requestComplete;
   std::recursive_mutex m_handleLock;

   std::ifstream m_uploadStream;
   unsigned long long m_dataSent;

   std::vector<char> m_uploadBuffer;

   unsigned int m_TotalTimeout_ms = 150000;

   const unsigned long long MAX_REQUEST_SIZE_BYTES = (__int64)((DWORD)~0);
   const size_t MAX_FILE_CHUNK_SIZE_BYTES = 4194304; // 4 MiB

   // WinHttp has 4 different timeouts. The defaults are INFINITE, 60s, 30s,
   // 30s respectively. Infinite timeouts are unacceptable, so use 30s instead.
   // This determines the ratios below. Truncation is preferable as it
   // guarantees that synchronous requests will timeout before the
   // WaitForSingleObject call. Thus the call will not unblock prematurely.
   const double m_ResolveTimeoutRatio = 0.2;
   const double m_ConnectTimeoutRatio = 0.4;
   const double m_SendTimeoutRatio = 0.2;
   const double m_ReceiveTimeoutRatio = 0.2;

   // Handles SSL requests that may need to be made
#ifndef WINHTTP_NO_OPENSSL
   SSLHandler m_sslHandler;
#endif

   std::function<void(HttpRequest *, std::string)> m_callback;
};


class WinHttpRequestFactory : public IHttpRequestFactory {
public:
   virtual std::shared_ptr<HttpRequest> GetHttpRequestClient() override;
};
