/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * unityWindow.h --
 *
 *      Windows-specific UnityWindow implementation.
 */

#pragma once

#include "cui/core/property.hh"
#include "cui/unity/unityWindow.hh"
#include "wui/imglib/imgutil.h"
#include "wui/mksShared/mksScreenView.h"
#include "wui/trackableWindow.h"
#include "wui/wuiTypes.h"

#include "imageUtilTypes.h"

// Forward declarations so we don't have to include gdiplus.h in this header,
// which would have wide-ranging effects.
namespace Gdiplus {
class Bitmap;
class Graphics;
class Region;
} // namespace Gdiplus

namespace wui {

/*
 * This property gets set on unity windows (see apps\lib\wui\unitywindow.cpp)
 * so that other code can easily tell that a particular window is a unity window.
 */
const LPCWSTR UNITY_WINDOW_PROPERTY_NAME = L"vmw_unity_window";
const LPCWSTR UNITY_WINDOW_ID_PROPERTY_NAME = L"vmw_unity_window_id";

const unsigned int UNITY_WATCH_FOCUS_WAIT_MS = 500;

// Forward declarations
class UnityMgr;

// ************************************************************************
// CWindowOverride: A base class that provides overriden versions of CWindow
// methods that perform common object lifecycle management and error handling.

class CWindowOverride : public CWindow {
public:
   HICON SetIcon(HICON hIcon, BOOL bBigIcon = TRUE);
};

#define CONTROL_BIT (1 << 0)
#define WIN_BIT (1 << 1)
#define ESC_BIT (1 << 2)

// ************************************************************************
// UnityWindow: The host window that shows the contents of a guest
// window in Unity mode.

typedef CWinTraits<WS_POPUP, WS_EX_TOOLWINDOW | WS_EX_NOACTIVATE> CUnityWinTraits;

class UnityWindow :
   public CWindowImpl<UnityWindow, CWindowOverride, CUnityWinTraits>,
   public wui::TrackableWindow,
   public cui::UnityWindow,
   public wui::MKSScreenView {
public:
   static ATL::CWndClassInfo &GetWndClassInfo()
   {
      /*
       * Mostly copied from the DECLARE_WND_CLASS macro, with the icon-loading
       * code added to give unity windows a default icon.
       */
      const int OIC_WINLOGO = 32517;
      static LPCTSTR szWndClassName = _T("HorizonUnityHostWndClass");
      static WTL::CIcon hLargeIcon = ImgUtil_LoadIconSize(
         NULL, IDI_WINLOGO, GetSystemMetrics(SM_CXICON), GetSystemMetrics(SM_CYICON));
      static WTL::CIcon hSmallIcon = ImgUtil_LoadIconSize(
         NULL, OIC_WINLOGO, GetSystemMetrics(SM_CXSMICON), GetSystemMetrics(SM_CYSMICON));

      static ATL::CWndClassInfo wc = {
         {sizeof(WNDCLASSEX), CS_HREDRAW | CS_VREDRAW | CS_DBLCLKS, StartWindowProc, 0, 0, NULL,
          hLargeIcon, NULL, (HBRUSH)(COLOR_WINDOW + 1), NULL, szWndClassName, hSmallIcon},
         NULL,
         NULL,
         IDC_ARROW,
         TRUE,
         0,
         _T("")};

      return wc;
   }

   // Construction/destruction
   UnityWindow(wui::UnityMgr *pUnityMgr, UnityWindowId windowId);
   ~UnityWindow();

   // Maps
   BEGIN_MSG_MAP(UnityWindow)
   CHAIN_MSG_MAP(wui::TrackableWindow)
   MSG_WM_CREATE(OnCreate)
   MSG_WM_DESTROY(OnDestroy)
   MSG_WM_PARENTNOTIFY(OnParentNotify)
   MSG_WM_ERASEBKGND(OnEraseBkgnd)
   MSG_WM_ACTIVATE(OnActivate)
#ifdef MOUSE_ACTIVATION_BORDER_WORKAROUND
   MSG_WM_MOUSEACTIVATE(OnMouseActivate)
#endif
   MSG_WM_ENTERSIZEMOVE(OnEnterSizeMove)
   MSG_WM_EXITSIZEMOVE(OnExitSizeMove)
   MSG_WM_WINDOWPOSCHANGING(OnWindowPosChanging)
   MSG_WM_WINDOWPOSCHANGED(OnWindowPosChanged)
   MESSAGE_RANGE_HANDLER_EX(WM_MOUSEFIRST, WM_MOUSELAST, OnMouseMessage)
   MSG_WM_KEYDOWN(OnKeyMessage)
   MSG_WM_SYSKEYDOWN(OnKeyMessage)
   MSG_WM_KEYUP(OnKeyMessage)
   MSG_WM_SYSKEYUP(OnKeyMessage)
   MSG_WM_SYSCOMMAND(OnSysCommand)
   MSG_WM_GETMINMAXINFO(OnGetMinMaxInfo)
   MSG_WM_NCLBUTTONDOWN(OnNCLButtonDown)
   MSG_WM_NCCALCSIZE(OnNCCalcSize)
   MESSAGE_HANDLER_EX(WM_DWMSENDICONICTHUMBNAIL, OnSendThumbnail)
   MESSAGE_HANDLER_EX(WM_DWMSENDICONICLIVEPREVIEWBITMAP, OnSendLivePreview)
   MESSAGE_HANDLER_EX(WM_TOUCH, OnTouch)
   END_MSG_MAP()

   // Message handlers
   LRESULT OnCreate(LPCREATESTRUCT lpcs);
   virtual void OnDestroy();
   void OnParentNotify(UINT message, UINT nChildID, LPARAM lParam);
   virtual BOOL OnEraseBkgnd(HDC hdc);
   void OnActivate(UINT uState, BOOL bMinimized, HWND hwndOther);
   int OnMouseActivate(CWindow wndTopLevel, UINT nHitTest, UINT message);
   void OnEnterSizeMove();
   void OnExitSizeMove();
   void OnWindowPosChanging(WINDOWPOS *pwp);
   void OnWindowPosChanged(WINDOWPOS *pwp);
   LRESULT OnMouseMessage(UINT uMsg, WPARAM wParam, LPARAM lParam);
   void OnKeyMessage(UINT nChar, UINT nRepCnt, UINT nFlags);
   void OnSysCommand(UINT uID, CPoint pt);
   void OnGetMinMaxInfo(LPMINMAXINFO lpMMI);
   void OnNCLButtonDown(UINT nHitTest, CPoint pt);
   LRESULT OnNCCalcSize(BOOL bCalcValidRects, LPARAM lParam);
   LRESULT OnSendThumbnail(UINT uMsg, WPARAM wParam, LPARAM lParam);
   LRESULT OnSendLivePreview(UINT uMsg, WPARAM wParam, LPARAM lParam);
   LRESULT OnTouch(UINT uMsg, WPARAM wParam, LPARAM lParam);

   // Operations
   void DrawBorderDecorations(Gdiplus::Graphics &gr);
   void ChangeZOrder(const cui::UnityWindow &otherWindow, HDWP &hdwp) const;
   void UpdateOverlays();
   void WatchForFocus(bool forceActivate = false,
                      unsigned int timeoutMs = UNITY_WATCH_FOCUS_WAIT_MS);
   void RequestWindowMoveResize(const CRect &rc);
   void Activate();
   void EnableRealTimeAeroPeekUpdates(bool enable);

   // Properties
   void GetRequiredStyles(DWORD &dwStyles, DWORD &dwExStyles);
   bool IsInSizeMove() const { return m_bInSizeMove; }
   CRect GetCurrentRect() const { return m_rcWindowRect; }

   // Callbacks
   void OnInitialUpdate();
   void OnCloakedEvent(bool cloaked);
   void OnWindowContentsUpdated(const cui::Size &size, const cui::PNGData &data);
   void RequestWindowContents();

   // Overrides of UnityWindow methods
   virtual bool Cleanup();
   virtual void Move(const cui::Rect &rc);
   virtual void SetRegion(RegionPtr region);
   virtual void SetWindowTitle(const utf::string &title);
   virtual void SetMaximizeButtonPosition(const cui::Rect &guestButtonRect);
   virtual void OrderInFrontOfWindow(const cui::UnityWindow &otherWindow);
   virtual bool IsInFrontOfWindow(const cui::UnityWindow &otherWindow) const;
   virtual void OrderFront();
   virtual cui::MKSOverlayBitmapID CreateBorderOverlayBitmap();

   virtual void OnWindowOverlayIconAdded(const utf::string &description, const cui::PNGData &data);
   virtual void OnWindowOverlayIconDeleted();
   virtual void SetGuestApp(cui::GuestApp *guestApp) override;

   // Disambiguate which SetIcon this class wants to use.
   using CWindowOverride::SetIcon;

protected:
   /*
    * Data used to hold the window image and region, and other UI data like
    * where the window and badge go.
    */
   WTL::CRgn m_GuestWindowRgn;
   Gdiplus::Region *mOuterBorderRgnPtr;
   Gdiplus::Region *mMiddleBorderRgnPtr;
   Gdiplus::Region *mInnerBorderRgnPtr;
   CRect m_rcWindowRect;

   // BUM stuff
   void OnMksWindowChanged();

   // Data/properties used during a host-driven move loop.
   bool m_bInSizeMove;                // are we in a size/move loop?
   bool m_bIsResizableWithWinKeyDown; // is Win key is down for window with resizable frame (for
                                      // aero snap)?
   bool m_allowNextRegionUpdate;      // should we allow the next "region" update despite
                                      //   being in an optimized move
   CRect m_rcLastSizeMovePos;         // the last window RECT that has been set in the loop
   CRect m_rcPosBeforeSizeMove;       // the window RECT before the move operation
   sigc::connection mExitSizeMoveCnx;
   sigc::connection mAbortOptimizedMoveCnx;
   unsigned int mOptimizedMoveBeginTimeoutMs;

   // Data/properties used for DnD operation.
   UnityDndDraggingState m_dndDraggingState;

   // Data/properties used for Aero Peek support.
   ImageInfo mLastWindowContents;
   bool mLastWindowContentsValid;
   bool mRequestingWindowContents;
   sigc::connection mRequestWindowContentsCnx;
   bool mRealTimeAeroPeekUpdatesEnabled;

   // Other tidbits of UI data.
   int m_nVertWheelDistance;   // accumulated vertical mouse wheel distance
   int m_nHorzWheelDistance;   // accumulated horizontal mouse wheel distance
   bool m_bGuestWndHasARegion; // has the guest ever given us a region for this window?
   bool mWndHasBeenShown;      // has the window been shown at least once?
   bool mUseAltGrWorkaround;   // Use the workaround for bug 621912?
   cui::WeakPtr<wui::UnityMgr> mpUnityMgr;
   sigc::connection mSetFocusCnx;
   sigc::connection mWatchingForFocusCnx;
   sigc::connection mWaitForMKSWindowCnx;
   bool mBecomingMaximized; // true = the window is being maximized, see bug 690086
   sigc::connection mIgnoreNextActivationResetCnx;
   HIMC mHimc;
   bool mRestoredFromFullScreened;

   // Optimized window move stuff.
   CRect mToleranceRect;
   bool mIgnoreMouseMessages;
   bool mDelayingOptimizedMove;
   bool mOptimizedResizeChangedFullWindowDrag;
   bool mAbortingOptimizedMove;

   // Optimized window resize stuff.
   int mBorderHitTest;

   bool mWindowIconBigSet;
   bool mWindowIconSmallSet;

   bool mNeedFakeKeyUpForIME;

   // Functions providing optional DnD or copy/paste logic for derived classes.
   virtual utf::string GetVMFilePath() const { return ""; }
   virtual utf::string GetVMName() const { return ""; }
   virtual void HandleMksWindowChange() {}
   virtual bool IsMouseUpdateAllowed() const { return true; }
   virtual void OnWindowActivated(bool activated) {}
   virtual void OnWindowDragActive(bool active) {}
   virtual void UpdateRemoteClipboard() {}
   virtual void UpdateLocalClipboard() {}

   // Functions used for the border decorations.
   void CalcBorderRegions(const WTL::CRgn &rgn);
   void CalcBorderRegions(const CRect &rc);
   Gdiplus::Region *MakeFrameRegion(const Gdiplus::Region *windowRgnPtr, const int frameSize);

   // Callbacks
   void OnWindowAttrWillChange(UnityWindowAttribute attr, bool value);
   void OnWindowAttrChanged(UnityWindowAttribute attr, bool value);
   void OnWindowTypeChanged();
   void OnWindowIconChanged(UnityIconType type);
   void OnGuestAppChanged();
   void OnGetWindowIconDone(const cui::PNGData &iconData, UnityIconSize iconSize);
   void OnGetWindowIconAbort(bool bCancelled, cui::Error e, UnityIconSize iconSize);
   void OnBlockSetFocusTimeout();
   virtual void OnDelaySetFocusTimeout();
   void OnWatchForFocusTimeout();
   void OnWaitForMKSWindowTimeout();

   // Overridden by crt::win32::UnityWindow
   virtual void OnMoveSizeDone(int x, int y, int cx, int cy);
   virtual void OnMoveSizeAbort(bool bCancelled, cui::Error e);

   // Other UI stuff
   void ModifyAppToolWindowStyles(DWORD dwRemove, DWORD dwAdd);
   HRESULT SetTaskbarBtnProperties(const cui::GuestApp &guestApp);
   wui::string GenerateAppID(const cui::GuestApp &guestApp) const;
   HRESULT GetPropertyStore(IPropertyStore **ppPropStore);
   HRESULT SetStringProperty(IPropertyStore *pPropStore, REFPROPERTYKEY propkey, LPCWSTR value);
   HRESULT EnablePinning(IPropertyStore *pPropStore, bool enable);
   void SetWindowIconsFromGuestApp(const cui::GuestApp &guestApp);
   void SetWindowIconFromGuestApp(const cui::GuestApp::IconList &icons, BOOL bigIcon);
   void RedrawUnityWindow();
   void UpdateTransparency();
   Gdiplus::Bitmap *MakeAeroPeekBitmap(UINT width, UINT height, HBITMAP overlayBitmap);
   void AdjustMKSRenderedView();

   // Virtual method for the derived class to override
   virtual CSize CalculateMKSOffsetSize() const;
   virtual bool IsPosChangingAllowed(int x, int y, int cx, int cy) const { return true; }

   void ModifyWindowForOptimizedResize(bool starting);

   // Overridden from CWindowImpl
   virtual void OnFinalMessage(HWND hWnd);

   bool ShouldBeginOptimizedMove(OptimizedUnityWindowMoveType moveType) const;
   void BeginOptimizedMove(OptimizedUnityWindowMoveType moveType, bool initialMousePositionIsValid);
   void OnBeginOptimizedMoveTimeout();
   void AbortOptimizedMove();
   void ResetOptimizedMove();
   bool OptimizedMoveOnMouseMessage(UINT uMsg, WPARAM wParam, LPARAM lParam);
   RECT AlignWindowToCursor();
   void AlignCursorToWindow();

   virtual bool IsIMEPassthroughEnabled();
   void ChangeIMEStatus(bool focused, bool minimized);
   bool mIsFromSameUnityMgr;
   bool ShouldHideKeypressFromGuest(const VScancode vscan, const UINT vkey);
   void SetCtrlWinEscape(UINT nChar, bool isKeyDown);

   void SetAppBar(bool bCreate);
   void RemoveAppBar();
   void TopAndActivate();

   cui::PNGData mOverlayIcon;
   utf::string mOverlayIconDescription;
   std::vector<sigc::connection> mConnections;

private:
   // Drawing parameters for the border decorations.
   static const int outerBorderSize = 1;
   static const BYTE outerBorderAlpha = 128; // 50% opaque
   static const int middleBorderSize = 1;
   static const BYTE middleBorderAlpha = 153; // 60% opaque
   static const int innerBorderSize = 1;
   static const BYTE innerBorderAlpha = 64; // 25% opaque
   static const int thinBorderSize = 1;
   static const BYTE thinBorderAlpha = 230; // 90% opaque
   cui::UnityKeyEvent mLastScancode;
   int mCtrlWinEscapeActive; // 0 bit - crtl, 1 bit - win, 2 bit - esc
#ifdef CRTBORATEST
   friend class UnityMgrUnitTest_TestSynchronizeWindowZOrder_Test;
   friend class UnityWindowUnitTest_TestOnIMENotify_Test;
   friend class cui::CUIUnityWindowUnitTest_TestMove_Test;
   friend class cui::CUIUnityMgrUnitTest_TestUpdateUnityWindowState_Test;
#endif
};


} // end namespace wui
