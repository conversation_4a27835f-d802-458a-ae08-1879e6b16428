/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

#pragma once

#include <common/windows/singleton/Singleton.h >

// TODO: UBI-282: Remove the hardcoded paths and use the registry to get the paths.
#define SYSPREP_COMMAND "C:\\Windows\\System32\\Sysprep\\sysprep.exe"
#define SYSPREP_ARGS "/generalize /oobe /mode:vm /quit /unattend:%s"
#define SYSPREP_UNATTEND_ANSWER_FILE_PATH "C:\\Progra~1\\Omnissa\\Horizon\\Agent\\unattend.xml"
#define AGENT_HELPER_FULL_PATH "C:\\Program Files\\Omnissa\\Horizon\\Agent\\bin\\hzaprep.exe"
#define AGENT_HELPER_PARAM_SERVICE_MODE "/service"
#define ACKSTR L"Ack"

namespace svmga {
namespace core {
namespace util {

/*
 * The module to ask agent helper the basic information such as:
 * is the mac address changed after paring?
 * if yes, then it's not master vm/golden image any more
 */

class AgentHelperWrapper : public Singleton<AgentHelperWrapper> {
public:
   AgentHelperWrapper();
   ~AgentHelperWrapper();

   static void Init() {};

   virtual bool SysprepGeneralize();
   virtual std::wstring GetSysprepInterceptionString();
   virtual DWORD RunProcess(LPCWSTR exe, LPCWSTR params);
   virtual DWORD RunProcessAsync(LPCWSTR exe, LPCWSTR params);
   virtual bool LaunchTemplateHelper();
   virtual bool LaunchCloneHelper(std::wstring adServerName, std::wstring adSite);
   virtual std::wstring GetCurrentMacAddress();

private:
   // Cache the helper process information so that
   // we can close the process handle after the process is done.
   PROCESS_INFORMATION helperProc = {0};
};

} // namespace util
} // namespace core
} // namespace svmga