# Copyright (c) Omnissa, LLC. All rights reserved.
# This product is protected by copyright and intellectual property laws in the
# United States and other countries as well as by international treaties.
# -- Omnissa Restricted

"""macos

Mac OS specific Horizon packaging

Maintainers: vdm-client@vmware.<NAME_EMAIL>

"""
import os
import re
import vmware

from fnmatch import fnmatch
from SCons.Script import Dir, File, SConscript
from vmware.utils import SkipDSStore

log = vmware.GetLogger("packaging")

productName = vmware.ProductName()
marketVersion = vmware.ProductVersionNumber("HorizonClientYYMM")
productVersion = "%s-%s-%s" % (
    marketVersion,
    vmware.ProductVersionNumber(),
    vmware.BuildNumber(),
)

productTextSubst = [
    ("@MARKET_VERSION@", marketVersion),
]

stageEnv = vmware.pkg.stageEnv
stagePath = vmware.pkg.stagePath
localEnv = vmware.pkg.localEnv

stageEnv.LoadTool(
    [
        "cds-repo",
        "copychmod",
        "coreutils",
        "findutils",
        "gettext",
        "linkcopy",
        "p7zip",
        "tar",
        "tar-builder",
        "unzip",
        "zip-builder",
        "artifactory",
    ]
)

pcoipSoftClientsRoot = vmware.GetGobuildComponent("pcoip-soft-clients")
webrtcRoot = vmware.GetGobuildComponent("cart-webrtc")
deemRoot = vmware.GetGobuildComponent("euc_endpoint-telemetry-sdk")
deemPackage = deemRoot
deemIntegrated = vmware.LocalOpts.GetBool("DEEM_INTEGRATED", True)
buildTypeVMwareSigned = vmware.SignBinaries()
buildTypeNotarized = vmware.LocalOpts.GetBool("NOTARIZE_BUNDLE", False)
rmksBundleName = "Horizon Protocol"
rmksBundleID = "com.omnissa." + (
    "horizon.protocol" if buildTypeVMwareSigned else "eng.remotemks"
)
plistbuddy = "/usr/libexec/PlistBuddy"
signingScript = File("#bora/support/scripts/macosSignBinaries.py")
if deemIntegrated:
    dmgResourcesRoot = Dir("#horizonclient/install/mac/dmg_resources")
else:
    dmgResourcesRoot = Dir("#horizonclient/view/openClient/cocoa/dmg_resources")

lang_map = {
    "en": "English",
    "fr": "French",
    "de": "German",
    "es": "Spanish",
    "ja": "Japanese",
    "ko": "Korean",
    "zh_CN": "zh-Hans",
    "zh_TW": "zh-Hant",
}

crossBuildEnv = vmware.pkg.CrossBuildEnv(vmware.pkg.CrossBuildEnv.CROSS_BUILD_UNIVERSAL)


def nonrecursiveDirFilter(root, dirs):
    del dirs[:]


def addDepsToZipSources(env, names, zipSources):
    deps = []
    for name in names:
        deps += list(env[name])

    for dep in deps:
        depAbsPath = File(dep).abspath
        zipSources.append(os.path.split(depAbsPath))


def cmdNotarization(target, onlyStaple=False):
    cmds = []

    scriptPath = vmware.FileAbsPath(
        "#horizonclient/view/openClient/cocoa/macosNotarize.py"
    )
    # This is an app-specific password
    # Refer to: https://support.apple.com/en-us/102654
    pwd = os.environ.get("NOTARIZE_APP_SPECIFIC_PASSWORD")

    cmds += [
        scriptPath,
        "--apple-id <EMAIL>",
        "--password %s" % pwd,
        "--team-id S2ZMFGQM93",
        "--verbose",
    ]
    if onlyStaple:
        cmds += ["--only-staple"]
    else:
        cmds += [
            "--no-staple",
            "--timeout 30",
            "--unique-id %s" % vmware.BuildNumber(),
        ]
    cmds += ['"%s"' % target.abspath]

    return " ".join(cmds)


def stageViewClient():
    """
    Stage the binaries for horizon-protocol.
    """
    stagePP = vmware.PathPrefixer(vmware.pkg.stagePath)
    viewClientPP = stagePP / "viewClient"
    nodes = []
    targetName = ["viewClient"]
    publishDir = vmware.ReleasePackagesDir()

    stageEnv.LoadTool("libssl")

    host = "mac64"
    zipSources = []

    rmksEnv = vmware.LookupEnv("viewClient", host)
    rmksEnv.LoadTool(["libx264"])

    deps = (
        [
            rmksEnv["FFMPEG_AVCODEC_REDIST"],
            rmksEnv["FFMPEG_AVUTIL_REDIST"],
            rmksEnv["FFMPEG_AVFORMAT_REDIST"],
            rmksEnv["LIBPNG_REDIST"][0].abspath,
            os.path.join(
                rmksEnv["LIBX264_LIBPATH"], os.readlink(rmksEnv["LIBX264_REDIST"])
            ),
        ]
        + rmksEnv["OPENSSL_FIPS_REDIST"]
        + [vmware.pkg.LookupDeliverableNodes("omnissabaselib", host)[0].abspath]
    )

    for dep in deps:
        nodes += stageEnv.LinkCopy(
            File(viewClientPP / "mac64" / os.path.basename(dep)), os.path.abspath(dep)
        )

    for node in vmware.LookupNode("viewClient", host):
        nodes += stageEnv.LinkCopy(File(viewClientPP / "mac64" / node.name), node)

    if publishDir is not None:
        deliverableNodesNames = ["viewClient"]

        for dep in deps:
            zipSources.append(
                (os.path.dirname(os.path.abspath(dep)), os.path.basename(dep))
            )

        zipFileName = "viewClient-%s-%s.zip" % (vmware.BuildNumber(), host)
        zipFilePath = File(os.path.join(publishDir, "viewClient", zipFileName))
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetName,
            host,
            deliverableNodesNames,
            zipSources,
            zipFilePath,
        )
        if zipNode is not None:
            nodes += zipNode

    return nodes


def stage_resources(pp):
    nodes = []

    # Compile xib to nib

    xibDir = Dir("#horizonclient/view/openClient/cocoa/English.lproj")
    strDir = Dir("#horizonclient/view/openClient/cocoa/LocalizedStrings")
    for node in vmware.EnumerateSourceDir(xibDir):
        for lang in os.listdir(strDir.abspath):
            filename, ext = os.path.splitext(node.name)
            if ext == ".xib":
                strFile = os.path.join(strDir.abspath, lang, filename + ".strings")
                target = pp + "%s.lproj/%s.nib" % (lang, filename)
                action = vmware.ActionWithDisplay(
                    "$IBTOOL --errors --warnings --notices "
                    "--minimum-deployment-target 13.0 "
                    "--output-format human-readable-text "
                    "--import-strings-file $STRING "
                    "--compile $TARGET $SOURCE",
                    "Compiling $TARGET",
                )
                nodes += stageEnv.Command(target, node, action, STRING=strFile)
            else:
                target = pp + "%s.lproj/%s" % (lang, node.name)
                nodes += stageEnv.FileCopy(target, node)

    # Copy languageSpecificMacToWinKeymap

    mapDir = Dir("#horizonclient/view/openClient/cocoa/languageSpecificMacToWinKeymap")
    nodes += vmware.DirCopy(
        vmware.EnumerateSourceDir(mapDir, SkipDSStore),
        mapDir,
        Dir(pp + "languageSpecificMacToWinKeymap"),
        stageEnv,
    )

    nodes += stageEnv.FileCopy(
        pp + node.name, File("#osl/hcmac/open_source_licenses.txt")
    )

    # Compile cdk.strings and po
    poDir = Dir("#horizonclient/view/openClient/po")
    for node in vmware.EnumerateSourceDir(poDir):
        if not node.name.endswith(".po"):
            continue
        filename, ext = os.path.splitext(node.name)
        target = pp + "%s.lproj/cdk.strings" % lang_map[filename]
        nodes += stageEnv.Command(
            target,
            node,
            vmware.ActionWithDisplay(
                "$MSGCAT --stringtable-output -o $TARGET $SOURCE", "Compiling $TARGET"
            ),
        )
        target = File(pp / "locale" / filename / "LC_MESSAGES" / "horizon-client.mo")
        nodes += stageEnv.Command(
            target,
            node,
            vmware.ActionWithDisplay("$MSGFMT -o $TARGET $SOURCE", "Compiling $TARGET"),
        )

    stageEnv.NoCache(nodes)
    return nodes


def stage_bin(appPP, libPP, fwPP):
    nodes = []

    # libcrtbora and libomnissabase are dependencies of horizon-client
    # However, we need to call UnifiedLookupNode to build fat binary.
    omnissabaselibNode = vmware.pkg.UnifiedLookupNode(
        "omnissabaselib", crossBuildEnv=crossBuildEnv
    )
    libcrtboraNode = vmware.pkg.UnifiedLookupNode(
        "libcrtbora", crossBuildEnv=crossBuildEnv
    )
    stageEnv.Depends(libcrtboraNode, omnissabaselibNode)

    libcdsNode = vmware.pkg.UnifiedLookupNode("libcds", crossBuildEnv=crossBuildEnv)
    stageEnv.Depends(libcrtboraNode, libcdsNode)

    rtavCliLibNode = vmware.pkg.UnifiedLookupNode(
        "rtavCliLib", crossBuildEnv=crossBuildEnv
    )
    stageEnv.Depends(libcrtboraNode, rtavCliLibNode)

    binNodes = vmware.pkg.UnifiedLookupNode("hcmac", crossBuildEnv=crossBuildEnv)
    stageEnv.Depends(binNodes, libcrtboraNode)
    nodes += vmware.pkg.StageBinsWithDeps(binNodes, appPP / "MacOS", fwPP)

    vmipcNode = vmware.pkg.UnifiedLookupNode("libvmipc", crossBuildEnv=crossBuildEnv)
    nodes += vmware.pkg.StageBinsWithDeps(vmipcNode, fwPP, fwPP)

    # Required by cdsHelper
    vmidNode = vmware.pkg.UnifiedLookupNode("fusionId", crossBuildEnv=crossBuildEnv)
    nodes += vmware.pkg.StageBinsWithDeps(vmidNode, fwPP, fwPP)

    nodes += stage_privilegedHelper(libPP / "LaunchServices", "cdsHelper")

    return nodes


def stage_usb(libPP, fwPP):
    nodes = []

    nodes += vmware.pkg.StageBinsWithDeps(
        vmware.pkg.UnifiedLookupNode(
            "viewClientServices-suid", crossBuildEnv=crossBuildEnv
        ),
        libPP,
        fwPP,
    )

    nodes += vmware.pkg.StageBinsWithDeps(
        vmware.pkg.UnifiedLookupNode("viewClientServices", crossBuildEnv=crossBuildEnv),
        libPP,
        fwPP,
    )

    nodes += vmware.pkg.StageBinsWithDeps(
        vmware.pkg.UnifiedLookupNode("usbArbitrator", crossBuildEnv=crossBuildEnv),
        libPP,
        fwPP,
    )

    files = [
        File("#horizonclient/view/openClient/cocoa/InitUsbServices.tool"),
        File("#horizonclient/view/openClient/cocoa/services.sh"),
        File("#horizonclient/view/openClient/cocoa/relaunch.sh"),
    ]
    for node in files:
        nodes += stageEnv.CopyChmod(libPP + node.name, node, CHMOD_MODE="0755")

    # libhorizon-usbd.dylib will be copied as dependency of the vdpservice
    # plugin

    return nodes


def stage_pcoip(fwPP):
    nodes = []
    files = [
        File(
            os.path.join(
                pcoipSoftClientsRoot,
                "mac",
                "universal",
                "client",
                "libpcoip_client.dylib",
            )
        ),
    ]
    nodes += vmware.pkg.StageBinsWithDeps(files, fwPP, fwPP)
    return nodes


def stage_plugins(libPP, fwPP):
    nodes = []
    pluginPP = libPP / "pcoip" / "vchan_plugins"
    vdpservicePP = pluginPP / "vdpservice"

    files = (
        vmware.pkg.UnifiedLookupNode("vdpservice", crossBuildEnv=crossBuildEnv)
        + vmware.pkg.UnifiedLookupNode("rdpvcbridge", crossBuildEnv=crossBuildEnv)
        + vmware.pkg.UnifiedLookupNode("scredirvchan", crossBuildEnv=crossBuildEnv)
        + vmware.pkg.UnifiedLookupNode("rtavPlugin", crossBuildEnv=crossBuildEnv)
    )

    nodes += vmware.pkg.StageBinsWithDeps(files, pluginPP, fwPP)

    files = (
        vmware.pkg.UnifiedLookupNode("mksvchanclient", crossBuildEnv=crossBuildEnv)
        + vmware.pkg.UnifiedLookupNode("rdeClient", crossBuildEnv=crossBuildEnv)
        + vmware.pkg.UnifiedLookupNode("tsdrClient", crossBuildEnv=crossBuildEnv)
        + vmware.pkg.UnifiedLookupNode(
            "usbRedirectionClient", crossBuildEnv=crossBuildEnv
        )
        + vmware.pkg.UnifiedLookupNode("prvdpplugin", crossBuildEnv=crossBuildEnv)
        + vmware.pkg.UnifiedLookupNode("html5mmrClient", crossBuildEnv=crossBuildEnv)
        + vmware.pkg.UnifiedLookupNode("fido2Client", crossBuildEnv=crossBuildEnv)
        + [
            File(
                os.path.join(
                    webrtcRoot,
                    "apple_macos-universal",
                    "lib",
                    "libwebrtc_sharedlib.dylib",
                )
            )
        ]
    )

    msteamsDir = os.path.join(
        vmware.GetGobuildComponent("msteamsapi"), "apple_macos-universal"
    )
    slimCVLib = [
        File(os.path.join(msteamsDir, "lib", "libMicrosoft.SlimCV.VBM.dylib")),
    ]

    # The slimCV library dependency needs to be placed in the vdpservice folder, so it
    # uses a custom option for the destination folder. All other dependencies are placed
    # in Frameworks
    nodes += vmware.pkg.StageBinsWithDeps(
        files, vdpservicePP, fwPP, slimCVLib, vdpservicePP
    )

    # Add dependencies
    deps = vmware.pkg.UnifiedLookupNode("horizon-usbd", crossBuildEnv=crossBuildEnv)
    stageEnv.Depends(nodes, deps)

    return nodes


def stage_lldpd(libPP, fwPP):
    lldpdRoot = vmware.GetConanComponent(stageEnv, "lldpd")
    if not lldpdRoot:
        raise Exception("lldpd is not available in conan")

    lldpdPackageFolder = lldpdRoot.package_folder
    lldpdLibFolder = os.path.join(lldpdPackageFolder, "lib")
    lldpdBinFolder = os.path.join(lldpdPackageFolder, "bin")

    lldpdPP = libPP / "lldpd"
    files = [
        File(os.path.join(lldpdBinFolder, "lldpd")),
        File(os.path.join(lldpdBinFolder, "lldpcli")),
        File(os.path.join(lldpdLibFolder, "liblldpctl.4.dylib")),
    ]

    nodes = []
    for f in files:
        nodes += stageEnv.FileCopy(lldpdPP + f.name, f)

    return nodes


def stage_urlfilter(pp):
    nodes = []
    urlFilterPP = pp / "horizon-urlFilterApp" / "Contents"

    nodes += vmware.pkg.StageBinsWithDeps(
        vmware.pkg.UnifiedLookupNode("horizon-urlFilter", crossBuildEnv=crossBuildEnv),
        urlFilterPP / "MacOS",
        urlFilterPP / "MacOS",
    )

    nodes += vmware.pkg.StageNode(
        vmware.LookupNode("horizon-urlFilter-plist"),
        vmware.pkg.StagePrefixer(pp),
        textSubst=[],
    )

    resNodes = vmware.EnumerateSourceDir(
        Dir("#bora/apps/rde/urlRedirection/urlFilterMac/resources"), SkipDSStore
    )
    resNodes += vmware.LookupNode("horizon-urlFilter-resources")
    nodes += vmware.pkg.StageNode(
        resNodes,
        vmware.pkg.StagePrefixer(urlFilterPP / "Resources", resourceBasename),
        copier=vmware.pkg.FileCopier(),
        textSubst=[],
        substituteList=None,
    )

    iconNode = File("#horizonclient/view/openClient/cocoa/icons/horizon.icns")
    nodes += stageEnv.FileCopy(urlFilterPP / "Resources" + iconNode.name, iconNode)
    return nodes


def stage_urlNativeMessageHostMac(pp):
    nodes = []
    urlNativeMessageHostMacPP = pp / "horizon_url_native_host"
    nodes += vmware.pkg.StageBinsWithDeps(
        vmware.pkg.UnifiedLookupNode(
            "horizon_url_native_host", crossBuildEnv=crossBuildEnv
        ),
        urlNativeMessageHostMacPP,
        urlNativeMessageHostMacPP,
    )
    manifestNode = File(
        "#bora/apps/rde/urlRedirection/urlNativeMessageHost/urlNativeMessageHostMac/horizon_url_native_host.json"
    )
    nodes += stageEnv.FileCopy(
        urlNativeMessageHostMacPP + manifestNode.name, manifestNode
    )
    return nodes


def stage_messages(pp):
    nodes = []
    src = Dir("#horizonclient/view/openClient/cocoa/messages/")
    nodes += vmware.DirCopy(
        vmware.EnumerateSourceDir(src), src, Dir(pp / "messages"), stageEnv
    )
    return nodes


def stage_client_ui_cdk_sdk_unittests(stageEnv, stagePP):
    """
    Stage all files for client team's unit tests.

    """
    utPP = stagePP / "tests"
    actions = []
    copyNodes = []

    nodes = vmware.LookupNode("libcdkUnitTest")
    copyNodes += vmware.pkg.StageBinsWithDeps(nodes, utPP, utPP)

    nodes = vmware.LookupNode("clientSdkUnitTest")
    copyNodes += vmware.pkg.StageBinsWithDeps(nodes, utPP, utPP)

    testEnv = vmware.LookupEnv("libcrtbora")
    conanOcmock = vmware.GetConanComponent(testEnv, "ocmock")
    OCMockFramework = os.path.join(
        conanOcmock.env["FRAMEWORKPATH"][0], "OCMock.framework"
    )
    basename = os.path.basename(OCMockFramework)
    targetDir = Dir(utPP / basename)
    target = File(utPP / basename / conanOcmock.env["FRAMEWORKS"][0])
    nodes = stageEnv.Command(
        target, [], vmware.pkg.CopyFolderAction(Dir(OCMockFramework), targetDir)
    )
    stageEnv.NoCache(nodes)
    copyNodes += nodes

    # copy horizonrxut into tests
    zipSources = []
    binNodes = vmware.LookupNode("horizonrxut", "mac64")
    copyNodes += vmware.pkg.StageBinsWithDeps(binNodes, utPP, utPP)

    # Get all the libs required.
    for file in getGtestFilePaths():
        zipSources.append((file.dir.abspath, file.name))

    # Get run case and deployment python script
    deploySrc = "#bora/apps/horizonrxtest/componentTest/deploy"
    zipSources.append((deploySrc, "utility"))
    zipSources.append((deploySrc, "horizonut_run_cases.py"))
    zipSources.append((deploySrc, "horizonut_deploy.py"))

    frameworks = [
        (os.path.join(stageEnv["SHARED_FRAMEWORK_PATH"]), "XCTest.framework"),
        (os.path.join(stageEnv["SHARED_FRAMEWORK_PATH"]), "XCTestCore.framework"),
        (os.path.join(stageEnv["SHARED_FRAMEWORK_PATH"]), "XCTestSupport.framework"),
        (os.path.join(stageEnv["SHARED_FRAMEWORK_PATH"]), "XCUIAutomation.framework"),
        (os.path.join(stageEnv["SHARED_FRAMEWORK_PATH"]), "XCUnit.framework"),
    ]
    for frameworkPath, framework in frameworks:
        actions += vmware.pkg.CopyFolderAction(
            Dir(os.path.join(frameworkPath, framework)), Dir(utPP / framework)
        )

    copyNodes += stageEnv.Command(Dir(utPP), [], actions)

    # Publish to release dir
    releaseDir = vmware.ReleasePackagesDir()
    if releaseDir is None:
        return copyNodes

    zipSources.append((vmware.DirAbsPath(utPP), "."))
    zipName = "unittest-Omnissa-Horizon-Client-Mac-Intel-%s.zip" % productVersion
    zipFile = File(os.path.join(releaseDir, "tests", zipName))
    copyNodes += stageEnv.Zip(zipFile, zipSources)
    return copyNodes


def crtbora_skip_files(rootPath, basename):
    return basename != ".DS_Store" and basename != "proxyApp-template-info.plist"


def resourceBasename(node):
    """
    Determine a uniquely-identifying path suffix.

    It is common practice to name localized variants of resources identically
    and instead distiguish them by their parent directories,
    e.g.: */English.lproj/mainMenu.nib/

    When staging these resources we need to remove the extraneous path prefix,
    e.g.: In Fusion, resources under
    bora/apps/vmplayer/macos/resources/English.lproj/ need to be staged in
    path/to/staged/VMware Fusion.app/Contents/Resources/English.lproj/.

    Given a file node determine a file path suffix that is at least the node's
    basename and at most the node's full path.

    Results:
        A string containing the resources uniquely identifying path suffix

    Side Effects:
        None

    """

    # XXX this should be moved to common resource grabbing code for libs.
    # XXX this regex is assuming Unix style paths.

    # This function is called to stage many categories of resources (docker,
    # common DUI resources). This means we must handle different
    # possible prefixes. If adding a new category to this regex, include
    # another sub-expression OR'ed in to the larger and ensure that the new
    # sub-expression only identifies 1 group (the path suffix you want to
    # return).

    pattern = """
     (?:macos)?/[^/]*resources[^/]*/(.*)
    |dui/resources/(.*)
    |urlFilterMac/resources/(.*)
    """
    mo = re.search(pattern, node, re.VERBOSE)

    # The suffix we want is the first (only) non-None string in the groups
    # tuple.
    suffixes = [suffix for suffix in mo.groups() if suffix is not None]
    if len(suffixes) != 1:
        raise vmware.ScriptError(
            "Matched on multiple resource categories: %s" % str(suffixes)
        )

    return suffixes[0]


def stage_node(buildTreeNodeList, prefixFunc, copier=None, substituteList=None):
    """Some files need to have the product information substituted in
    during the packaging stage. This will happen if copier is blank or if a
    file's basename appears in substituteList
    """
    return vmware.pkg.StageNode(
        buildTreeNodeList, prefixFunc, productTextSubst, copier, substituteList
    )


def executable_bundle_nodes(
    pp,
    nodePrefix,
    stripAsSharedLib=False,
    plistPP=None,
    substituteList=None,
    runtimeLibs=[],
    resourceNodes=[],
):
    """Makes nodes for an executable bundle

    Executable bundles always have only a Contents directory,
    and within that directory is lots of "stuff"
    Targets should define the following nodes, to be moved to directories:
        <nodePrefix>-bin        <pp>/Contents/MacOS
        <nodePrefix>-resources  <pp>/Contents/Resources
        <nodePrefix>-plist      <pp>/Contents
    """
    return vmware.pkg.StageBundle(
        pp,
        nodePrefix,
        resourceBasename,
        stripAsSharedLib,
        plistPP,
        substituteList,
        productTextSubst,
        runtimeLibs=runtimeLibs,
        resourceNodes=resourceNodes,
    )


def stage_docker(libPP):
    """The proxy app support files: proxyApp-template-*"""

    # We store the plist template separately from the proxy app folder template
    # so Spotlight does not index the proxy app folder template as a working
    # app users can use.
    # dockerResourceDir contains product-specific resources for dockers
    dockerResourceDir = Dir("#bora/apps/crtbora/macos/resources-docker")
    resourceNodes = vmware.EnumerateSourceDir(dockerResourceDir, SkipDSStore)
    nodes = executable_bundle_nodes(
        libPP / "proxyApp-template-app",
        "docker",
        plistPP=libPP,
        resourceNodes=resourceNodes,
    )

    return nodes


def stage_privilegedHelper(pp, helper):
    nodes = []
    node = vmware.pkg.UnifiedLookupNode(helper, crossBuildEnv=crossBuildEnv)
    execName = node[0].name
    nodes += stage_node(
        node, lambda node: pp + execName, copier=vmware.pkg.StripCopier()
    )
    nodes += stageEnv.LinkCopy(
        pp + execName + ".plist", vmware.LookupNode(helper + "-launchdPlist")
    )
    stageEnv.NoCache(nodes)
    return nodes


def sign_bundle(bundlePP, deps):
    contentPP = bundlePP / "Contents"
    signaturePlistFile = stageEnv.File(contentPP / "_CodeSignature" / "CodeResources")
    sign_actions, sign_deps = sign_action((bundlePP).Dir())
    node = stageEnv.Command(signaturePlistFile, deps + sign_deps, sign_actions)
    stageEnv.NoCache(node)
    return node


def stage_crtbora_test_bundle(stagePP, stageEnv):
    """stage_crtbora_test_bundle
    Stage the bundle for crtbora API Test.
    """
    apiTestPP = stagePP / "APITest"
    pkgbuildPath = "/usr/bin/pkgbuild"

    nodes = []

    # Create the test bundle.
    targetName = "crtboraApiTest"
    bundleName = "%s.app" % targetName
    uiPP = apiTestPP / bundleName
    contentPP = uiPP / "Contents"
    resPP = contentPP / "Resources"
    testPkg = resPP / "test.pkg"
    testUnsignedPkg = resPP / "test_unsigned.pkg"

    nodes += executable_bundle_nodes(uiPP, targetName, plistPP=contentPP)

    # Copy the Dock to test bundle.
    libraryPP = contentPP / "Library"
    nodes += stage_docker(libraryPP)

    fwPP = contentPP / "Frameworks"
    testEnv = vmware.LookupEnv("libcrtbora")
    conanOcmock = vmware.GetConanComponent(testEnv, "ocmock")
    frameworks = [
        (
            os.path.join(conanOcmock.env["FRAMEWORKPATH"][0], "OCMock.framework"),
            conanOcmock.env["FRAMEWORKS"][0],
        ),
        (os.path.join(stageEnv["SHARED_FRAMEWORK_PATH"], "XCTest.framework"), "XCTest"),
        (
            os.path.join(stageEnv["SHARED_FRAMEWORK_PATH"], "XCTestCore.framework"),
            "XCTestCore",
        ),
        (
            os.path.join(stageEnv["SHARED_FRAMEWORK_PATH"], "XCTestSupport.framework"),
            "XCTestSupport",
        ),
        (
            os.path.join(stageEnv["SHARED_FRAMEWORK_PATH"], "XCUIAutomation.framework"),
            "XCUIAutomation",
        ),
        (os.path.join(stageEnv["SHARED_FRAMEWORK_PATH"], "XCUnit.framework"), "XCUnit"),
    ]
    for framework, name in frameworks:
        basename = os.path.basename(framework)
        targetDir = Dir(fwPP / basename)
        target = File(fwPP / basename / name)
        nodes += stageEnv.Command(
            target, nodes, vmware.pkg.CopyFolderAction(Dir(framework), targetDir)
        )
        stageEnv.AddPostAction(target, "$CHMOD -R +w " + targetDir.abspath)

    dataSrc = Dir("#bora/apps/crtbora/componentTest/appRemoting/data")
    nodes += vmware.DirCopy(
        vmware.EnumerateSourceDir(dataSrc),
        dataSrc,
        Dir(contentPP / "Resources"),
        stageEnv,
    )
    retinaJson = File(
        "#horizonclient/view/openClient/cocoa/tests/testCdkScreenUtil_retina.json"
    )
    nodes += stageEnv.LinkCopy(resPP + retinaJson.name, retinaJson)

    notchJson = File(
        "#horizonclient/view/openClient/cocoa/tests/testCdkScreenUtil_notch.json"
    )
    nodes += stageEnv.LinkCopy(resPP + notchJson.name, notchJson)

    scripts = [
        File("#bora/apps/crtbora/componentTest/appRemoting/scripts/crtboraApiTest.py"),
        File("#bora/apps/crtbora/componentTest/appRemoting/scripts/macAlertMonitor.sh"),
    ]
    for f in scripts:
        nodes += stageEnv.LinkCopy(apiTestPP + f.name, f)

    # Build test package
    test_deps = []
    test_actions = [
        vmware.ActionWithDisplay(
            [
                '"$PKGBUILD" --identifier com.omnissa.horizon.test '
                "--nopayload $TARGET"
            ],
            "Generating $TARGET",
        ),
    ]

    if buildTypeVMwareSigned:
        sign_actions, sign_deps = sign_action(testPkg)
        test_actions += sign_actions
        test_deps += sign_deps

    testPkgNodes = stageEnv.Command(
        File(testPkg), test_deps, test_actions, PKGBUILD=pkgbuildPath
    )
    nodes += testPkgNodes
    stageEnv.NoCache(testPkgNodes)

    testUnsignedPkgNodes = stageEnv.Command(
        File(testUnsignedPkg),
        nodes,
        vmware.ActionWithDisplay(
            [
                '"$PKGBUILD" --identifier com.omnissa.horizon.test_unsigned '
                "--nopayload $TARGET"
            ],
            "Generating $TARGET",
        ),
        PKGBUILD=pkgbuildPath,
    )
    stageEnv.NoCache(testUnsignedPkgNodes)
    nodes += testUnsignedPkgNodes

    # Publish for ob/sb.
    publishDir = vmware.ReleasePackagesDir()
    if publishDir is None:
        return nodes

    # Do the codesign.
    if buildTypeVMwareSigned:
        nodes += sign_bundle(uiPP, nodes)

    # Zip the bundle to publish dir.
    zipFile = File(os.path.join(publishDir, "tests", "mac64", "%s.zip" % targetName))
    zipNodes = stageEnv.P7ZipDir(zipFile, Dir(apiTestPP), ".", "0644")
    stageEnv.Depends(zipNodes, nodes)
    nodes += zipNodes
    stageEnv.NoCache(nodes)

    vmware.Alias("crtboraApiTest-stage", nodes)

    return nodes


def stage_rdeClient_test_bundle(stagePP, stageEnv):
    """stage_rdeClient_test_bundle
    Stage the bundle for mac rdeClient Test.
    """
    apiTestPP = stagePP / "APITest"

    nodes = []

    # Create the test bundle.
    targetName = "rdeClientTest"
    bundleName = "%s.app" % targetName
    binNodeName = "%s-bin" % targetName
    uiPP = apiTestPP / bundleName
    contentPP = uiPP / "Contents"
    nodes += executable_bundle_nodes(uiPP, targetName, plistPP=contentPP)

    dataSrc = Dir("#bora/apps/rde/rdeSvc/tests/componentTest/rdeClientTest/data")
    nodes += vmware.DirCopy(
        vmware.EnumerateSourceDir(dataSrc),
        dataSrc,
        Dir(contentPP / "Resources"),
        stageEnv,
    )

    # Publish for ob/sb.
    publishDir = vmware.ReleasePackagesDir()
    if publishDir is None:
        return nodes

    # Do the codesign.
    if buildTypeVMwareSigned:
        nodes += sign_bundle(uiPP, nodes)

    # Zip the bundle to publish dir.
    zipSource = []
    zipSource.append((vmware.DirAbsPath(apiTestPP), bundleName))
    appRemotingDir = vmware.DirAbsPath("#bora/apps/crtbora/componentTest/appRemoting")
    zipSource.append((appRemotingDir + "/scripts", "crtboraApiTest.py"))
    zipSource.append((appRemotingDir + "/scripts", "macAlertMonitor.sh"))
    zipFile = File(os.path.join(publishDir, "tests", "mac64", "%s.zip" % targetName))
    nodes += stageEnv.Zip(zipFile, zipSource)
    return nodes


def stage_viewClient_test(stagePath, stageEnv):
    """stage_viewClient_test
    Stage the binaries for viewClient Api Test.
    """

    utPP = stagePath / "viewClientApiTest"

    nodes = []
    targetName = "viewClientApiTest"

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is None:
        return nodes

    host = "mac64"
    zipSources = []

    node = vmware.LookupNode(targetName)
    nodes += vmware.pkg.StageBinsWithDeps(node, utPP, utPP)

    zipFile = File(os.path.join(publishDir, "tests", host, targetName + ".zip"))
    zipSources = [(vmware.DirAbsPath(utPP), ".")]
    nodes += stageEnv.Zip(zipFile, zipSources)
    return nodes


def getZipSourcesFromNodeLookup(nodeNames, host, includeAllFiles=False):
    """
    Retrieves a list of zip sources that can be passed into env.Zip().

    :param nodeNames: List of node names to look up.
    :param host: Host of the node to use in the lookup.
    :param includeAllFiles: True if all files for this node (e.g. pdb, lib)
                            should be included.
    :return: List of tuples in a format compatible with env.Zip().
    """
    zipSources = []
    for nodeName in nodeNames:
        if includeAllFiles:
            nodes = vmware.pkg.LookupDeliverableNodes(
                nodeName, host, crossBuildEnv=crossBuildEnv
            )
        else:
            nodes = [vmware.LookupNode(nodeName, host)[0]]
        for node in nodes:
            zipSources.append((node.dir.abspath, node.name))
    return zipSources


def getRXComponentTestCommonDeps():
    commonDepsSources = []

    libcdsNode = vmware.pkg.UnifiedLookupNode("libcds", crossBuildEnv=crossBuildEnv)
    libcds = (libcdsNode[0].dir.abspath, libcdsNode[0].name)
    commonDepsSources.append(libcds)
    commonDepsSources += getZipSourcesFromNodeLookup(
        [
            "omnissabaselib",
        ],
        "mac64",
        True,
    )

    return commonDepsSources


def stage_crtbora_rxdnd_component_test(stageEnv):
    """stage_crtbora_rxdnd_component_test

    Stage the test library for crtbora RX Component Test.

    """
    nodes = []
    targetName = ["crtboraRXDnDComponentTest"]

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        deliverableNodesNames = [
            "crtboraRXDnDComponentTest",
            "mksvchanclient",
            "tsdrClient",
            "vdpservice",
        ]
        extraZipSources = []
        extraZipSources.append(
            (
                stageEnv.Dir(
                    "#bora/apps/crtbora/componentTest/" "remoteExperience"
                ).abspath,
                "crtboraRXComponentTest_client_mac.ini",
            )
        )

        testDataNode = stageEnv.FetchFromArtifactory(
            "horizon-cart-local/remote-experience/dnd/testFiles.zip",
            extract=True,
            extractTargetFiles=["testFiles/DnDTestFile.txt"],
        )
        testDataPath = os.path.dirname(os.path.dirname(testDataNode[0].abspath))
        extraZipSources.append((testDataPath, "."))

        extraZipSources.extend(getRXComponentTestCommonDeps())

        testEnv = vmware.LookupEnv("crtboraRXDnDComponentTest-env", "mac64")
        testEnv.LoadTool(
            [
                "gettext",
                "libglib2",
                "libglibmm",
                "libpcre2",
                "libpng",
                "libsigc",
            ]
        )
        deps = [
            "GETTEXT_REDIST",
            "SIGC_REDIST",
            "GLIB_REDIST",
            "GLIBMM_REDIST",
            "PCRE2_REDIST",
            "LIBPNG_REDIST",
        ]
        addDepsToZipSources(testEnv, deps, extraZipSources)

        zipFilePath = File(
            os.path.join(
                publishDir, "tests", "mac64", "crtboraRXDnDComponentTestClient.zip"
            )
        )
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetName,
            "mac64",
            deliverableNodesNames,
            extraZipSources,
            zipFilePath,
            [".map"],
            crossBuildEnv=crossBuildEnv,
        )
        stageEnv.Depends(zipNode, testDataNode)

        if zipNode is not None:
            nodes += zipNode
    return nodes


def stage_crtbora_rxfa_component_test(stagePP, stageEnv):
    """stage_crtbora_rxfa_component_test

    Stage the test library for crtbora RX FA Component Test.

    """
    nodes = []
    targetName = ["crtboraRXFAComponentTest"]

    publishDir = vmware.ReleasePackagesDir()
    deliverableNodesNames = ["crtboraRXFAComponentTest"]
    extraZipSources = []
    extraZipSources.append(
        (
            stageEnv.Dir("#bora/apps/crtbora/componentTest/" "fileAssociation").abspath,
            "fileAssociation_mac.ini",
        )
    )

    testDataSrc = "#bora/apps/crtbora/componentTest/fileAssociation/"
    extraZipSources.append((Dir(testDataSrc).abspath, "res"))

    dockerPP = stagePP / "horizon-docker"
    dockerNodes = stage_docker(dockerPP)
    extraZipSources.append((Dir(stagePP).abspath, "horizon-docker"))

    testEnv = vmware.LookupEnv("crtboraRXFAComponentTest-env", host="mac64")
    testEnv.LoadTool(
        [
            "gettext",
            "libglib2",
            "libglibmm",
            "libpcre2",
            "libpng",
            "libsigc",
        ]
    )

    deps = [
        "GETTEXT_REDIST",
        "SIGC_REDIST",
        "GLIB_REDIST",
        "GLIBMM_REDIST",
        "PCRE2_REDIST",
        "LIBPNG_REDIST",
    ]
    addDepsToZipSources(testEnv, deps, extraZipSources)

    if publishDir is not None:
        extraZipSources.extend(getRXComponentTestCommonDeps())

        zipFilePath = File(
            os.path.join(publishDir, "tests", "mac64", "crtboraRXFAComponentTest.zip")
        )
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetName,
            "mac64",
            deliverableNodesNames,
            extraZipSources,
            zipFilePath,
            crossBuildEnv=crossBuildEnv,
        )
        if zipNode is not None:
            nodes += zipNode

    stageEnv.Depends(nodes, dockerNodes)
    return nodes


def stage_remotemks_bundle(resPP, fwPP):
    uiPP = resPP / (rmksBundleName + ".app")
    appPP = uiPP / "Contents"
    macPP = appPP / "MacOS"
    nodes = []

    viewClientNode = vmware.pkg.UnifiedLookupNode(
        "viewClient", crossBuildEnv=crossBuildEnv
    )
    nodes += vmware.pkg.StageBinsWithDeps(viewClientNode, macPP, fwPP)

    # Copy info.plist
    substArgs = [
        ("@RMKS_BUNDLE_NAME@", rmksBundleName),
        ("@RMKS_BUNDLE_ID@", rmksBundleID),
        ("@RMKS_BUNDLE_DISPLAY_NAME@", vmware.ProductName()),
        ("@BUILD_NUMBER@", vmware.BuildNumber()),
        ("@VIEW_VERSION@", vmware.ProductVersionNumber()),
    ]
    node = File("#horizonclient/view/openClient/horizonprotocol.Info.plist.in")
    nodes += stageEnv.TextSubst(appPP + "Info.plist", node, TEXTSUBSTARGS=substArgs)

    # FIPs module
    viewClientEnv = vmware.LookupEnv("viewClient", host="mac64")
    files = [File(viewClientEnv["OPENSSL_FIPS_MODULE_SO_PATH"])]
    nodes += vmware.pkg.StageBinsWithDeps(files, fwPP, fwPP)
    return nodes


def stage_ffmpeg(fwPP):
    stageEnv.LoadTool(["symlink"])

    nodes = []

    # StageBinsWithDeps will resolve soft links and copy the real dylib file.
    # But these libs are loaded in the runtime with hard coded names so we must
    # symlink them manually
    def _stage_lib(libRoot, name):
        nodes = []
        realName = os.readlink(os.path.join(libRoot, name))
        files = [
            File(os.path.join(libRoot, realName)),
        ]
        nodes += vmware.pkg.StageBinsWithDeps(files, fwPP, fwPP)
        nodes += stageEnv.Symlink(
            fwPP.File(name), fwPP.File(realName), relpath=realName
        )
        return nodes

    viewClientEnv = vmware.LookupEnv("viewClient", host="mac64")
    # If you have to change the version number listed here because of a major
    # version change, you MUST also ensure that our code runs correctly.
    # This is important because major version changes may bring ABI changes.
    #
    # Refer to "#define FFMPEG_LIBRARY_NAME" and "#define FFMPEG_UTIL_NAME" in
    # bora/apps/rde/viewClient/win32/winFFmpeg.c
    # bora/apps/rde/viewClient/linux/X11FFmpeg.c
    # bora/apps/rde/viewClient/macos/macosFFmpeg.c
    #
    # To test, ensure that a Blast H264 desktop or app session can be
    # connected successfully.
    nodes += _stage_lib(
        viewClientEnv["FFMPEG_LIBPATH"],
        os.path.basename(viewClientEnv["FFMPEG_AVCODEC_REDIST"]),
    )
    nodes += _stage_lib(
        viewClientEnv["FFMPEG_LIBPATH"],
        os.path.basename(viewClientEnv["FFMPEG_AVUTIL_REDIST"]),
    )
    nodes += _stage_lib(
        viewClientEnv["FFMPEG_LIBPATH"],
        os.path.basename(viewClientEnv["FFMPEG_AVFORMAT_REDIST"]),
    )

    viewClientEnv.LoadTool(["libx264"])
    nodes += _stage_lib(
        viewClientEnv["LIBX264_LIBPATH"],
        os.path.basename(viewClientEnv["LIBX264_REDIST"]),
    )
    return nodes


def stage_printerredir(libPP, fwPP):
    nodes = []
    nodes += vmware.pkg.StageBinsWithDeps(
        vmware.pkg.UnifiedLookupNode("prclient", crossBuildEnv=crossBuildEnv),
        libPP,
        fwPP,
    )
    return nodes


def stage_scannerredir(libPP, fwPP):
    nodes = []
    rdpvcbridgePP = libPP / "pcoip" / "vchan_plugins" / "rdpvcbridge"
    nodes += vmware.pkg.StageBinsWithDeps(
        vmware.pkg.UnifiedLookupNode("scannerRedirClient", crossBuildEnv=crossBuildEnv),
        rdpvcbridgePP,
        fwPP,
    )

    return nodes


def stage_telemetry(libPP, fwPP):
    nodes = []
    rdpvcbridgePP = libPP / "pcoip" / "vchan_plugins" / "rdpvcbridge"
    nodes += vmware.pkg.StageBinsWithDeps(
        vmware.pkg.UnifiedLookupNode("tlm_rdpvcbridge", crossBuildEnv=crossBuildEnv),
        rdpvcbridgePP,
        fwPP,
    )
    return nodes


def stage_icon(resPP):
    nodes = []
    files = [
        File("#horizonclient/view/openClient/icons/windows_logo_small.tiff"),
        File("#horizonclient/view/openClient/cocoa/icons/application.icns"),
        File("#horizonclient/view/openClient/cocoa/icons/desktop.icns"),
        File("#horizonclient/view/openClient/cocoa/icons/desktop_windows365.icns"),
        File("#horizonclient/view/openClient/cocoa/icons/multi_launch.icns"),
        File("#horizonclient/view/openClient/cocoa/icons/horizon.icns"),
    ]
    for node in files:
        if node.name.endswith("_2x.png"):
            filename = node.name.replace("_2x.png", "@2x.png")
        else:
            filename = node.name
        nodes += stageEnv.FileCopy(resPP + filename, node)

    # Compile image assets
    target = File(resPP + "Assets.car")
    node = Dir("#horizonclient/view/openClient/cocoa/view/view/Images.xcassets")
    nodes += stageEnv.Command(
        target,
        node,
        vmware.ActionWithDisplay(
            "$ACTOOL --output-format human-readable-text --notices --warnings "
            "--enable-on-demand-resources NO --target-device mac "
            "--minimum-deployment-target 13.0 --platform macosx "
            "--product-type com.apple.product-type.application "
            "--compile ${TARGET.dir} $SOURCE",
            "Compiling $TARGET",
        ),
    )
    stageEnv.NoCache(nodes)
    return nodes


def dct_skip_files(rootPath, basename):
    return basename != ".DS_Store"


def stage_support(pp):
    nodes = []
    dctPP = pp / "dct"
    dctDirs = [
        "#horizonclient/view/openClient/dct/mac",
        "#horizonclient/view/openClient/dct/common",
    ]
    for dctDir in dctDirs:
        for node in vmware.EnumerateSourceDir(Dir(dctDir), dct_skip_files):
            # Copy file and set permissions to read and execute
            nodes += stageEnv.CopyChmod(dctPP + node.name, node, CHMOD_MODE="0755")

    configFilesNode = Dir("#horizonclient/view/openClient/dct/configFiles/mac")
    nodes += vmware.DirCopy(
        vmware.EnumerateSourceDir(configFilesNode, dct_skip_files),
        configFilesNode,
        Dir(dctPP / "configFiles"),
        stageEnv,
    )
    return nodes


def stage_plist(pp):
    nodes = []
    substArgs = [
        ("@PRODUCT_VIEW_CLIENT_NAME@", vmware.ProductName()),
        ("@BUILD_NUMBER@", vmware.BuildNumber()),
        ("@VIEW_VERSION@", vmware.ProductVersionNumber()),
        ("@MARKET_VERSION@", marketVersion),
    ]
    node = File("#horizonclient/view/openClient/Info.plist.in")
    nodes += stageEnv.TextSubst(pp + "Info.plist", node, TEXTSUBSTARGS=substArgs)
    return nodes


def stage_libcdk_sdk(releasePP):
    libcdkEnv = vmware.LookupEnv("libcdk-env")

    nodes = []
    sdkName = "%s-sdk-%s.MacOSX" % (productName.replace(" ", "-"), productVersion)
    rootPP = vmware.PathPrefixer(vmware.Host().ComponentBuildPath("libcdk-sdk"))
    sdkPP = rootPP / sdkName
    libPP = sdkPP / "lib"
    includePP = sdkPP / "include"

    # Copy headers
    for d in libcdkEnv["CPPPATH"]:
        if d.startswith("#bora"):
            continue
        for f in vmware.EnumerateSourceDir(
            Dir(d),
            filefilter=lambda root, f: f.endswith(".h")
            and not f.startswith("cdkCryptoki"),
            dirfilter=nonrecursiveDirFilter,
        ):
            nodes += stageEnv.FileCopy(includePP.File(f.name), f)

    # Copy lib
    for f in vmware.pkg.UnifiedLookupNode("libcdk", crossBuildEnv=crossBuildEnv):
        nodes += stageEnv.FileCopy(libPP.File(f.name), f)
    stageEnv.NoCache(nodes)
    nodes = stageEnv.Command(
        releasePP.File(sdkName + ".tgz"),
        nodes,
        vmware.ActionWithDisplay(
            "$TAR -zcf $TARGET -C $ROOT $SDK_NAME", "Creating libcdk SDK $TARGET"
        ),
        ROOT=Dir(rootPP),
        SDK_NAME=sdkName,
    )
    stageEnv.NoCache(nodes)
    return nodes


def stage_client_sdk_C_headers(targetPP):
    nodes = []

    headersDir = [
        "#horizonclient/view/openClient/sdk/semi-public",
        "#horizonclient/view/openClient/sdk/semi-public/c",
        "#horizonclient/view/openClient/sdk/semi-public/c/macos",
    ]
    includeFilefilter = (
        lambda root, f: f.endswith(".h")
        and not f.endswith("Int.h")
        and not f.endswith("Priv.h")
        and f != "pch.h"
        and f != ".DS_Store"
    )

    for d in headersDir:
        headerDir = Dir(d)
        headerFiles = vmware.EnumerateSourceDir(
            headerDir, includeFilefilter, dirfilter=nonrecursiveDirFilter
        )
        nodes += vmware.DirCopy(headerFiles, headerDir, Dir(targetPP), stageEnv)
    stageEnv.NoCache(nodes)

    return nodes


def stage_ci_viewclient_stub(stagePP, releasePP):
    stubPP = stagePP / "viewClientStub"
    nodes = []

    viewClientStubNode = vmware.pkg.UnifiedLookupNode(
        "viewClientStub", crossBuildEnv=crossBuildEnv
    )
    nodes += vmware.pkg.StageBinsWithDeps(viewClientStubNode, stubPP, stubPP)

    viewClientStubZipFileName = "%s-%s-remotemks-stub-macos.tar.gz" % (
        productName.replace(" ", "-"),
        productVersion,
    )

    nodes = stageEnv.Command(
        releasePP.File(viewClientStubZipFileName),
        stagePP.Dir(),
        vmware.ActionWithDisplay(
            "$TAR -zcf $TARGET -C $SOURCE $ROOT", "Creating remotemks stub $TARGET"
        ),
        ROOT="viewClientStub",
    )
    stageEnv.NoCache(nodes)
    return nodes


def stage_dct_client_component_test(stageEnv):
    # Stage the binaries for DCT Component Test
    nodes = []

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(publishDir, "tests")
        extraZipSources = []
        hosts = ["mac64"]
        for host in hosts:
            # Add the DCT test cases
            boraDCTTestDir = Dir("#bora/apps/horizonDCT/componentTest")
            testCasesDir = boraDCTTestDir.Dir("testCases/mac")
            extraZipSources.append((testCasesDir.abspath, "."))

            # Add the test deployment config files
            deployConfigsDir = boraDCTTestDir.Dir("deploymentConfigs")
            extraZipSources.append((deployConfigsDir.abspath, "dct_client_mac.ini"))

            # Add DCT scripts
            clientDCTDir = Dir("#horizonclient/view/openClient/dct")
            extraZipSources.append((clientDCTDir.Dir("common").abspath, "."))
            extraZipSources.append((clientDCTDir.Dir("mac").abspath, "."))

            deliverableNodes = vmware.pkg.LookupDeliverableNodes(
                "rxDCTComponentTestClient", host, crossBuildEnv=crossBuildEnv
            )
            for node in deliverableNodes:
                extraZipSources.append((node.dir.abspath, node.name))

            zipFilePath = os.path.join(publishDir, host, "rxDCTComponentTestClient.zip")
            zipNode = stageEnv.Zip(File(zipFilePath), extraZipSources)
            if zipNode is not None:
                nodes += zipNode

    return nodes


def stage_dmg_extras(dmgPP):
    nodes = []

    if not deemIntegrated:
        nodes += stageEnv.FileCopy(
            dmgPP.File("Learn More.webloc"), dmgResourcesRoot.File("Learn More.webloc")
        )

    backgroundDir = dmgResourcesRoot.Dir("background")
    nodes += vmware.DirCopy(
        vmware.EnumerateSourceDir(backgroundDir, SkipDSStore),
        backgroundDir,
        dmgPP.Dir(".background"),
        stageEnv,
    )

    nodes += stageEnv.CopyChmod(
        dmgPP.File(".DS_Store"), dmgResourcesRoot.File("DS_Store"), CHMOD_MODE="0444"
    )

    nodes += stageEnv.Command(
        dmgPP.File(".metadata_never_index"),
        [],
        vmware.ActionWithDisplay("$TOUCH $TARGET", "Touching $TARGET"),
    )

    stageEnv.NoCache(nodes)
    return nodes


def sign_cmd(params):
    cmd = [
        "%(signingscript)s",
        "--identity=%(identity)s",
    ]
    if "entitlementsDir" in params:
        cmd += [
            "--entitlements=%(entitlementsDir)s",
        ]
    if "soleEntitlements" in params:
        cmd += [
            "--soleEntitlements=%(soleEntitlements)s",
        ]
    if "keychainFile" in params:
        cmd += [
            "--keychainFile=%(keychainFile)s",
        ]
    if "keychainPass" in params:
        cmd += [
            "--keychainPass=%(keychainPass)s",
        ]
    cmd += [
        "--hardenedRuntime",
        '"%(signTarget)s"',
    ]

    return " ".join(cmd) % params


def sign_action(target, entitlementsPP=None):
    """
    Get an action list and dependency nodes for signing the target.

    Signing doesn't change the content of file, and doesn't generate new file,
    so we cannot return a list of nodes like other method. We return a list of
    actions which are required to sign the target, the caller can use these
    actions while building final target node.

    This method also returns a list of dependency nodes, the caller need to add
    this list into dependency while building final product.

    Results:
        A tuple containing the action list and dependency nodes

    Side Effects:
        None

    """
    actions = []
    nodes = [signingScript]
    signParams = {}
    signParams["signingscript"] = signingScript
    isBundle = str(target).endswith(".app")
    isRMKS = rmksBundleName in str(target)
    if buildTypeVMwareSigned:
        identity = stageEnv["ESCAPE"](
            "Developer ID Application: Wandering WiFi LLC (S2ZMFGQM93)"
        )
        signParams["keychainPass"] = os.environ.get("KEYCHAIN_PASSWORD")

    if vmware.BuildType() in ["release"]:
        soleEntitlements = File(
            "#horizonclient/view/openClient/cocoa/entitlements/" "distribution.xcent"
        )
    else:
        soleEntitlements = File(
            "#horizonclient/view/openClient/cocoa/entitlements/" "debug.xcent"
        )

    nodes += [soleEntitlements]

    signParams["identity"] = identity
    signParams["soleEntitlements"] = stageEnv["ESCAPE"](soleEntitlements.get_abspath())

    if isRMKS:
        entitlementsPath = "#horizonclient/view/openClient/cocoa/entitlements/"
        provision = entitlementsPath + "horizonprotocol.provisionprofile"

        nodes += stageEnv.LinkCopy(
            entitlementsPP.File(rmksBundleID + ".provisionprofile"), File(provision)
        )

        nodes += stageEnv.LinkCopy(
            entitlementsPP.File(rmksBundleName + ".app.entitlements"),
            File(entitlementsPath + "horizonprotocol.entitlements"),
        )

        signParams["entitlementsDir"] = stageEnv["ESCAPE"](
            vmware.DirAbsPath(entitlementsPP)
        )

    signParams["signTarget"] = (
        vmware.DirAbsPath(target) if isBundle else vmware.FileAbsPath(target)
    )

    actions += [
        vmware.ActionWithDisplay(sign_cmd(signParams), "Signing %s" % str(target))
    ]
    vmware.AddNewerThanNodes(target)
    return (actions, nodes)


def stage_packages(relPP, uiPP, deps, strip=False):
    pkgbuildPath = "/usr/bin/pkgbuild"
    plutilPath = "/usr/bin/plutil"
    productbuildPath = "/usr/bin/productbuild"
    setFilePath = os.path.join(
        stageEnv["ENV"]["DEVELOPER_DIR"], "usr", "bin", "SetFile"
    )
    rezPath = os.path.join(stageEnv["ENV"]["DEVELOPER_DIR"], "usr", "bin", "Rez")

    payloadPP = relPP / "packages" / "payload"
    bundlePP = payloadPP / (productName + ".app")
    resPP = bundlePP / "Contents" / "Resources"
    rmksPP = resPP / (rmksBundleName + ".app")
    componentPP = relPP / "packages" / "component"
    componentPlist = componentPP / (productName.replace(" ", ".") + ".plist")
    componentPkg = componentPP / (productName.replace(" ", ".") + ".pkg")
    productPP = relPP / "packages" / "product"
    productPkg = productPP / (productName + ".pkg")

    distribution = File("#horizonclient/install/mac/distribution.dist")
    productPkgIcon = stageEnv.FetchFromArtifactory(
        "horizon-cart-local/hcmac/icons-2412/install.rsrc",
        sha256="1f94fe85daf06c5c0e2d0982316463971612d310d9a181a2aa2fa18c9df32ba7",
    )[0]

    # Copy uninstall app
    uninstallAppName = "Uninstall " + productName + ".app"
    uninstallPP = relPP / uninstallAppName
    uninstallBundlePP = productPP / uninstallAppName
    uninstall_deps = []

    # Copy the bundle, strip it
    uninstall_actions = [
        vmware.pkg.CopyFolderAction(
            Dir(uninstallPP),
            Dir(uninstallBundlePP),
            ignore_patterns="*.dSYM" if strip else None,
        ),
    ]

    if buildTypeVMwareSigned:
        sign_actions, sign_deps = sign_action(uninstallBundlePP)
        uninstall_actions += sign_actions
        uninstall_deps += sign_deps

    # Copy the bundle, strip it
    actions = [
        vmware.pkg.CopyFolderAction(
            Dir(uiPP), Dir(bundlePP), ignore_patterns="*.dSYM" if strip else None
        ),
    ]

    actions += uninstall_actions
    deps += uninstall_deps

    # Copy signed uninstall app to resource
    actions += [
        vmware.pkg.CopyFolderAction(
            Dir(uninstallBundlePP), Dir(resPP / uninstallAppName)
        ),
    ]

    # Create encrypted template app package
    encryptPP = bundlePP / "Contents" / "Library"
    encryptedFolderTarget = Dir(encryptPP).abspath + "/templates.tar.gz"
    if buildTypeVMwareSigned:
        docker_sign_actions, docker_sign_deps = sign_action(bundlePP)
        actions += docker_sign_actions
        deps += docker_sign_deps

    actions += [
        vmware.ActionWithDisplay(
            [
                f'/bin/rm -rf "{encryptedFolderTarget}"',
            ],
            "Cleaning old templates",
        ),
        vmware.ActionWithDisplay(
            [
                f'/usr/bin/tar -cvf - -C \
                  "{Dir( bundlePP / "Contents" / "Library").abspath}" \
                  "proxyApp-template-app" \
                  "horizon-urlFilterApp" | openssl enc -aes-256-cbc -e -salt \
                  -pass pass:omnissa -out "{encryptedFolderTarget}"',
            ],
            "Encrypting and archiving new templates",
        ),
        vmware.ActionWithDisplay(
            [
                f'/bin/rm -rf \
                  "{Dir(encryptPP / "proxyApp-template-app").abspath}" \
                  "{Dir(encryptPP / "horizon-urlFilterApp").abspath}"',
            ],
            "Cleaning raw templates",
        ),
    ]

    # Sign encrypted template app package
    if buildTypeVMwareSigned:
        sign_template_actions, sign_template_deps = sign_action(
            encryptPP / "templates.tar.gz"
        )
        actions += sign_template_actions
        deps += sign_template_deps

    # Sign the bundle
    if buildTypeVMwareSigned:
        sign_actions, sign_deps = sign_action(
            rmksPP, relPP / "entitlements" / "horizonprotocol"
        )
        actions += sign_actions
        deps += sign_deps
        sign_actions, sign_deps = sign_action(bundlePP)
        actions += sign_actions
        deps += sign_deps

    actions += [
        vmware.ActionWithDisplay(
            [
                '"$PKGBUILD" --analyze --root "$PAYLOAD" $TARGET',
                '"$PLUTIL" -replace BundleIsRelocatable -bool NO $TARGET',
                '"$PLUTIL" -replace BundleIsVersionChecked -bool NO $TARGET',
            ],
            "Generating $TARGET",
        ),
    ]

    # Build component package plist
    plistNodes = stageEnv.Command(
        File(componentPlist),
        deps,
        actions,
        PRODUCTVERSION=productVersion,
        PKGBUILD=pkgbuildPath,
        PLUTIL=plutilPath,
        PAYLOAD=Dir(payloadPP),
        SIGNINGSCRIPT=signingScript,
    )
    stageEnv.NoCache(plistNodes)

    # Build component package
    componentPkgNodes = stageEnv.Command(
        File(componentPkg),
        plistNodes,
        vmware.ActionWithDisplay(
            [
                '"$PKGBUILD" --root "$PAYLOAD" --scripts "$SCRIPTS" '
                "--install-location /Applications "
                '--component-plist "$PLIST" $TARGET'
            ],
            "Generating $TARGET",
        ),
        PKGBUILD=pkgbuildPath,
        PAYLOAD=Dir(payloadPP),
        SCRIPTS=vmware.DirAbsPath("#horizonclient/install/mac/scripts"),
        PLIST=File(componentPlist),
    )
    stageEnv.NoCache(componentPkgNodes)

    # Build product package
    product_deps = componentPkgNodes
    product_deps += [File(os.path.join(deemPackage, "Deem.InstallerHelper.pkg"))]
    product_actions = [
        vmware.ActionWithDisplay(
            [
                '"$PRODUCTBUILD" --distribution "$DIST" --resources "$RESOURCES" '
                '--package-path "$PKG1" --package-path "$PKG2" $TARGET'
            ],
            "Generating $TARGET",
        )
    ]
    if buildTypeVMwareSigned:
        sign_actions, sign_deps = sign_action(productPkg)
        product_actions += sign_actions
        product_deps += sign_deps

    # Set install package icon
    product_actions += [
        vmware.ActionWithDisplay(
            [
                '"$REZ" -append "$ICON" -o $TARGET',
                '"$SETFILE" -a C $TARGET',
            ],
            "Customize icon of $TARGET",
        )
    ]

    # Notarize the final install PKG, CDS also pack it.
    if buildTypeNotarized:
        product_actions += [
            vmware.ActionWithDisplay(
                [
                    cmdNotarization(Dir(productPP)),
                    cmdNotarization(File(productPkg), onlyStaple=True),
                    cmdNotarization(Dir(uninstallBundlePP), onlyStaple=True),
                ],
                "Notarizing $TARGET",
            )
        ]

    # The installer build depends upon the icon being downloaded from Artifactory.
    product_deps.append(productPkgIcon)

    productNodes = stageEnv.Command(
        File(productPkg),
        product_deps,
        product_actions,
        PRODUCTBUILD=productbuildPath,
        DIST=distribution,
        RESOURCES=vmware.DirAbsPath("#horizonclient/install/mac/resources"),
        PKG1=Dir(componentPP),
        PKG2=deemPackage,
        PRODUCTVERSION=productVersion,
        SETFILE=setFilePath,
        REZ=rezPath,
        ICON=productPkgIcon,
        SIGNINGSCRIPT=signingScript,
    )
    return productNodes


def stage_dmg(target, dmgPP, uiPP, relPP, deps):
    nodes = []
    actions = []
    strip = vmware.BuildType() in ["release"]

    if deemIntegrated:
        nodes += stage_packages(relPP, uiPP, deps, strip)
        dmgPP = relPP / "packages" / "product"
        bundlePP = dmgPP / (productName + ".app")
    else:
        bundlePP = dmgPP / (productName + ".app")
        rmksPP = bundlePP / "Contents" / "Resources" / (rmksBundleName + ".app")

        # We cannot use vmware.DirCopy here since:
        # 1. appNodes doesn't include all the files inside app bundle, like the
        #    dependencies and symbol files copied by vmware.pkg.StageBinsWithDeps.
        # 2. uiPP is under BuildRoot, so vmware.EnumerateSourceDir cannot be used.
        # As we have to copy the entire directory, it's hard to set what's the
        # target, and signing and creating dmg should not start until this copying
        # job is finished, so I use an action list to do them
        # (copying, signing, creating dmg) one by one, and use the dmg node as the
        # final target.

        actions += [
            vmware.pkg.CopyFolderAction(
                Dir(uiPP), Dir(bundlePP), ignore_patterns="*.dSYM" if strip else None
            ),
        ]

        if buildTypeVMwareSigned:
            sign_actions, sign_deps = sign_action(
                rmksPP, relPP / "entitlements" / "horizonprotocol"
            )
            actions += sign_actions
            deps += sign_deps
            sign_actions, sign_deps = sign_action(bundlePP)
            actions += sign_actions
            deps += sign_deps

    # Stage dmg Extras
    nodes += stage_dmg_extras(dmgPP)

    # Build dmg and sign
    setFilePath = os.path.join(
        stageEnv["ENV"]["DEVELOPER_DIR"], "usr", "bin", "SetFile"
    )
    iconFile = dmgResourcesRoot.File("disk.icns")
    deps += [iconFile]

    # A complex dmg creation script with multiple steps
    # Creating dmg file
    scriptPath = vmware.FileAbsPath("#bora/install/desktop/macos/pkg-dmg")
    cmd = [
        (
            "%s "
            '--config cmd_SetFile="$SETFILE" '
            "--config cmd_chmod=/usr/bin/true "
            "--config cmd_bless=/usr/sbin/bless "
            "--config cmd_diskutil=/usr/sbin/diskutil "
            "--config makehybrid=0 "
            '--source "$DMGDIR" '
            '--target "$TARGET" '
            '--icon "$ICON" '
            '--volname "$APPNAME" '
            "%s"
        )
        % (
            scriptPath,
            "" if deemIntegrated else "--symlink /Applications:'Applications'",
        )
    ]

    actions += [vmware.ActionWithDisplay(cmd, "Creating dmg from $DMGDIR")]

    # Sign the dmg
    if buildTypeVMwareSigned:
        sign_actions, sign_deps = sign_action(target)
        actions += sign_actions
        deps += sign_deps

    dmgNode = stageEnv.Command(
        target,
        nodes + deps,
        actions,
        SETFILE=setFilePath,
        DMGDIR=Dir(dmgPP),
        ICON=iconFile,
        APPNAME=productName,
        SIGNINGSCRIPT=signingScript,
        SIGNTARGET=target,
        BUNDLE=Dir(bundlePP),
    )

    stageEnv.NoCache(nodes + dmgNode)
    return dmgNode


def stage_cds(releasePP, bundlePP, deps):
    # CDS
    cdsProductID = "viewcrt-mac"
    cdsPayloadName = "com.omnissa.horizon.tar"
    cdsMimeType = "omnissa/mac-zip"
    cdsArch = "all"
    # For minimumHostOSMajorVersion
    # macOS 13.0  --> 22
    # macOS 14.0  --> 23
    # macOS 15.0  --> 24
    cdsCustomParams = {
        "minimumHostOSMajorVersion": "22",
        "marketVersion": marketVersion,
    }
    cdsEarliestUpgradeVersion = "8.10.0"
    cdsResourcePath = "#horizonclient/install/mac/cds_resources/bulletins"
    cdsLanguages = "zh-Hant,de,en,fr,ja,ko,zh-Hans,es"

    cdsPP = releasePP / "cds"
    cdsTargetPP = (
        cdsPP / cdsProductID / vmware.ProductVersionNumber() / vmware.BuildNumber()
    )
    tmpPP = vmware.PathPrefixer(vmware.Host().ComponentBuildPath("cds-payload"))
    payloadNode = stageEnv.Command(
        tmpPP.File(cdsPayloadName),
        deps,
        vmware.ActionWithDisplay(
            [
                "$MKDIR -p $ROOT/payload",
                "/bin/cp -af $BUNDLE $ROOT/payload",
                "/usr/bin/ditto -c --keepParent $ROOT/payload $TARGET.abspath",
            ],
            "Creating CDS payload $TARGET",
        ),
        ROOT=Dir(tmpPP).abspath,
        BUNDLE=File(bundlePP),
    )

    cdsBulletinNodes = stageEnv.CDSBulletinMacOS(
        cdsTargetPP.File(cdsPayloadName + ".tar"),
        payloadNode,
        cdsMimeType,
        cdsProductID,
        cdsArch,
        vmware.ProductVersionNumber(),
        vmware.BuildNumber(),
        [cdsProductID + "," + cdsEarliestUpgradeVersion],
        cdsCustomParams,
    )

    cdsMetaDataNodes = stageEnv.CDSMetadata(
        cdsTargetPP.File("metadata.xml.gz"),
        cdsBulletinNodes,
        cdsResourcePath,
        cdsLanguages,
        secureDownload=True,
    )

    cdsRepoNodes = stageEnv.CDSRepo(Dir(cdsPP), cdsPP.Dir(cdsProductID))
    stageEnv.Depends(cdsRepoNodes, cdsMetaDataNodes)

    stageEnv.NoCache(payloadNode + cdsBulletinNodes + cdsMetaDataNodes)
    return cdsRepoNodes


def stage_uninstaller(relPP):
    nodes = []
    appName = "Uninstall " + productName
    uiPP = relPP / (appName + ".app")
    appPP = uiPP / "Contents"
    resPP = appPP / "Resources"
    fwPP = appPP / "Frameworks"
    libPP = appPP / "Library"
    binNodes = vmware.pkg.UnifiedLookupNode(
        "hcmac-uninstall", crossBuildEnv=crossBuildEnv
    )
    nodes += vmware.pkg.StageBinsWithDeps(binNodes, appPP / "MacOS", fwPP)

    # Info.plist
    substArgs = [
        ("@PRODUCT_VIEW_CLIENT_NAME@", appName),
        ("@BUILD_NUMBER@", vmware.BuildNumber()),
        ("@VIEW_VERSION@", vmware.ProductVersionNumber()),
        ("@MARKET_VERSION@", marketVersion),
    ]
    node = File("#horizonclient/install/mac/uninstall/Info.plist.in")
    nodes += stageEnv.TextSubst(appPP + "Info.plist", node, TEXTSUBSTARGS=substArgs)

    # Icon
    icon = File("#horizonclient/install/mac/uninstall/uninstall.icns")
    nodes += stageEnv.FileCopy(resPP + icon.name, icon)

    # tool
    tool = File("#horizonclient/install/mac/uninstall/uninstall.tool")
    nodes += stageEnv.FileCopy(libPP + tool.name, tool)

    # Language
    xibDir = Dir("#horizonclient/install/mac/uninstall/Base.lproj")
    strDir = Dir("#horizonclient/install/mac/uninstall/LocalizedStrings")
    for node in vmware.EnumerateSourceDir(xibDir):
        for lang in os.listdir(strDir.abspath):
            filename, ext = os.path.splitext(node.name)
            strFile = os.path.join(strDir.abspath, lang, filename + ".strings")
            if ext == ".xib":
                target = resPP + "%s.lproj/%s.nib" % (lang, filename)
                action = vmware.ActionWithDisplay(
                    "$IBTOOL --errors --warnings --notices "
                    "--minimum-deployment-target 13.0 "
                    "--output-format human-readable-text "
                    "--import-strings-file $STRING "
                    "--compile $TARGET $SOURCE",
                    "Compiling $TARGET",
                )
                nodes += stageEnv.Command(target, node, action, STRING=strFile)
            else:
                target = resPP + "%s.lproj/%s" % (lang, filename + ".strings")
                nodes += stageEnv.FileCopy(target, strFile)
    stageEnv.NoCache(nodes)
    return nodes


def zipHorizonRxTest(stagePath, publishAppPath):
    nodes = []
    zipFile = File(stagePath + "horizonrxtestClient.zip")

    zipSource = [(vmware.DirAbsPath(stagePath), "horizonrxtestClient.app")]
    nodes += stageEnv.Zip(zipFile, zipSource)
    return nodes


def stageHorizonRxTest(stagePath):
    publishDir = vmware.ReleasePackagesDir()
    nodes = []
    nodes += stageHorizonRxTestClientApp(stagePath)
    if publishDir:
        publishAppPath = os.path.join(publishDir, "tests", "horizonrxtest", "mac64/")
        nodes += zipHorizonRxTest(stagePath, publishAppPath)
        srcFilePath = stagePath + "horizonrxtestClient.zip"
        dstFilePath = publishAppPath + "horizonrxtestClient.zip"
        nodes += stageEnv.LinkCopy(dstFilePath, srcFilePath)
    return nodes


def stageHorizonRxDeployScripts(DSPath):
    nodes = []
    deploySrc = "#bora/apps/horizonrxtest/componentTest/deploy"
    files = vmware.EnumerateSourceDir(deploySrc)
    deployFiles = [
        "horizonrxtest_deploy.py",
        "horizonrxtest_health_check.py",
        "horizonrxtest_run_cases.py",
        "horizonrxtest_undeploy.py",
        "rx_test_env.py",
        "rx_util.py",
        "utils.py",
    ]
    newFiles = []
    newFiles = [file for file in files if file.name in deployFiles]
    nodes += vmware.DirCopy(newFiles, Dir(deploySrc), Dir(DSPath), stageEnv)
    return nodes


def stageHorizonRxfwLibs(fwPP):
    nodes = []
    componentNode = "rxci_socket_vchan"
    binaryNodes = vmware.pkg.StageDeliverables(stageEnv, componentNode, "mac64", fwPP)
    nodes += binaryNodes
    return nodes


def getGtestFilePaths():
    # list third party librarys for ut
    horizonrRxTestEnv = vmware.LookupEnv("horizonrxut-env", "mac64")
    fileNames = []
    for item in horizonrRxTestEnv["GTEST_REDIST"]:
        fileNames.append(File(item))
        dsym = str(item) + ".dSYM"
        if os.path.exists(dsym):
            fileNames.append(Dir(dsym))

    return fileNames


def stageHorizonRxBin(appPP, fwPP):
    nodes = []
    binNodes = vmware.LookupNode("horizonrxtest", "mac64")
    nodes += vmware.pkg.StageBinsWithDeps(binNodes, appPP / "MacOS", fwPP)
    return nodes


def stageHorizonRxTestClientApp(relPP):
    """Publish horizonrxTestClient.app."""
    pp = relPP
    uiPP = pp / ("horizonrxtestClient.app")
    appPP = uiPP / "Contents"
    fwPP = appPP / "Frameworks"
    deployScriptPP = appPP / "Deploy"
    zipSources = []
    nodes = []
    nodes += stageHorizonRxBin(appPP, fwPP)
    nodes += stageHorizonRxfwLibs(fwPP)
    nodes += stageHorizonRxDeployScripts(deployScriptPP)

    stageEnv.AddPostAction(nodes, "$CHMOD 0755 $TARGET")
    return nodes


def stageClientFido2ComponentTest(stageEnv):
    """stageClientFido2ComponentTest
    Stage the binaries for FIDO2 Component Test.
    """
    nodes = []
    targetName = ["fido2ComponentTest"]
    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(publishDir, "tests")
        deliverableNodesNames = [
            "fido2ComponentTest",
            "fido2Client",
            "vdpservice",
        ]
        zipSources = []
        for filename in [
            "fido2_client_mac.ini",
            "fido2Action.py",
            "fido2TcpClient.py",
            "fido2TcpServer.py",
            "fido2Utils.py",
        ]:
            zipSources.append(
                (
                    stageEnv.Dir(
                        "#bora/apps/rde/fido2/tests/componentTest/deploy"
                    ).abspath,
                    filename,
                )
            )

        zipFilePath = File(
            os.path.join(publishDir, "mac64", "fido2ComponentTestClient.zip")
        )
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetName,
            "mac64",
            deliverableNodesNames,
            zipSources,
            zipFilePath,
            excludedFileFormats=[".map"],
            crossBuildEnv=crossBuildEnv,
        )
        if zipNode is not None:
            nodes += zipNode
    return nodes


def stageClientFIDO2UnitTest(stageEnv):
    """Stage FIDO2 Client unit test library."""

    zipNode = []

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(publishDir, "tests")
        zipSources = []

        binaryNodes = vmware.pkg.LookupDeliverableNodes("fido2ClientUnitTest", "mac64")
        for node in binaryNodes:
            zipSources.append((node.dir.abspath, node.name))

        testEnv = vmware.LookupEnv("fido2ClientUnitTest-env", host="mac64")
        addDepsToZipSources(testEnv, ["OPENSSL_REDIST"], zipSources)

        zipFile = File(os.path.join(publishDir, "mac64", "fido2ClientUnitTest.zip"))
        zipNode = stageEnv.Zip(zipFile, zipSources)
        stageEnv.Depends(zipNode, binaryNodes)

    return zipNode


def stageTlmRdpvcbridgeUnitTest(stageEnv):
    """TlmRdpvcbridgeUnitTest

    Stage the binaries for tlm rdpvcbridge plugin Unit Test.
    """

    nodes = []
    targetName = ["tlm_rdpvcbridgeUT"]

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(vmware.DirAbsPath(publishDir), "tests")
        deliverableNodesNames = ["tlm_rdpvcbridgeUT"]

        extraZipSources = []
        zipFilePath = os.path.join(publishDir, "mac64", "tlmRdpvcbridgeUnitTest.zip")
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetName,
            "mac64",
            deliverableNodesNames,
            extraZipSources,
            zipFilePath,
            excludedFileFormats=[".map"],
        )
        if zipNode is not None:
            nodes += zipNode

    return nodes


def stageURLComponentTestClient(stageEnv):
    """URLComponentTest

    Stage the binaries for URL Redirection Component Test.

    """

    nodes = []
    targetName = ["urlComponentTest"]

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(vmware.DirAbsPath(publishDir), "tests")
        deliverableNodesNames = ["urlComponentTest", "urlFilterPlugin"]

        extraZipSources = []
        prefix = "mac"
        extraZipSources.append(
            (
                stageEnv.Dir(
                    "#bora/apps/rde/urlRedirection/tests/" "componenttest/"
                ).abspath,
                "url_client_%s.ini" % prefix,
            )
        )
        extraZipSources.append(
            (
                stageEnv.Dir(
                    "#bora/apps/rde/urlRedirection/tests/" "componenttest/"
                ).abspath,
                "com.omnissa.horizon.client.mac.plist",
            )
        )

        testEnv = vmware.LookupEnv("urlComponentTest-env", host="mac64")
        testEnv.LoadTool(
            [
                "gettext",
                "libglib2",
                "libglibmm",
                "libpcre2",
            ]
        )

        deps = [
            "GETTEXT_REDIST",
            "GLIB_REDIST",
            "GLIBMM_REDIST",
            "PCRE2_REDIST",
        ]
        addDepsToZipSources(testEnv, deps, extraZipSources)

        zipFilePath = os.path.join(publishDir, "mac64", "urlComponentTestClient.zip")
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetName,
            "mac64",
            deliverableNodesNames,
            extraZipSources,
            zipFilePath,
        )
        if zipNode is not None:
            nodes += zipNode

    return nodes


def stageURLUnitTest(stageEnv):
    """URLUnitTest

    Stage the binaries for URL Redirection Unit Test.

    """

    nodes = []
    targetName = ["urlUnitTest"]

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(vmware.DirAbsPath(publishDir), "tests")
        extraZipSources = []
        zipFilePath = os.path.join(publishDir, "mac64", "urlUnitTest.zip")
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetName,
            "mac64",
            targetName,
            extraZipSources,
            zipFilePath,
            excludedFileFormats=[".map"],
        )
        if zipNode is not None:
            nodes += zipNode

    return nodes


usbTestDepFilelist = [
    "lib*.dylib",
]


def stageClientUSBUnitTest(stageEnv):
    """stageClientUSBUnitTest

    Stage the unit test library for USB client components.

    """
    nodes = []
    targetName = "usbTest"
    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(publishDir, "tests")
        deploySrc = "#bora/apps/viewusb/framework/usb/test/unitTestSet"
        deploySrc = stageEnv.Dir(deploySrc).abspath
        zipSources = [
            (deploySrc, "usb_ut_deploy.py"),
            (deploySrc, "usb_ut_run_all_cases.py"),
        ]
        zipSources.append((deploySrc, "usb_ut_mac.ini"))

        testNodes = vmware.pkg.LookupDeliverableNodes(targetName, "mac64")
        testLibs = [
            "usbMmfwUnitTest",
            "usbViewUsbLibUnitTest",
            "usbStringStoreUnitTest",
            "usbUrbTrxUnitTest",
            "usbDevConfigUnitTest",
            "usbDevFilterUnitTest",
            "usbUsbdUnitTest",
            "usbRedirectionClientUnitTest",
        ]
        testLibs = ["lib%s" % name for name in testLibs]

        for node in testNodes:
            if node.name.startswith(tuple(testLibs)) and node.name.endswith(".dylib"):
                zipSources.append((node.dir.abspath, node.name))

        # other dependencies
        usbTestEnv = vmware.LookupEnv("usbComponentTest-env", "mac64")
        usbTestEnv.LoadTool(
            [
                "gettext",
                "libglib2",
                "libglibmm",
                "libpcre2",
                "libsigc",
            ]
        )
        deps = [
            "SIGC_REDIST",
            "GETTEXT_REDIST",
            "GLIB_REDIST",
            "GLIBMM_REDIST",
            "PCRE2_REDIST",
            "OPENSSL_REDIST",
        ]
        addDepsToZipSources(usbTestEnv, deps, zipSources)
        zipFile = File(os.path.join(publishDir, "mac64", "usbUnitTestClient.zip"))
        zipNode = stageEnv.Zip(zipFile, zipSources)
        nodes += zipNode

    return nodes


def stageUSBClientComponentTest(stageEnv):
    """stageUSBClientComponentTest

    Stage the binaries for usb Component Test.
    """

    nodes = []
    targetName = "usbTest"
    vmware.PathPrefixer(vmware.pkg.stagePath) / targetName

    deploySrc = "#bora/apps/viewusb/framework/usb/test/componenttest/deploy"
    testDataSrc = "#bora/apps/viewusb/framework/usb/test/componenttest/plugin/testData"
    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(publishDir, "tests")
        deliverableNodesNames = [
            "horizon-usbd",
            "usbRedirectionClient",
            "vdpservice",
            "usbArbitrator-podbin",
            "usbArbLib-podbin",
            "usbEnum-podbin",
            "usbEnumArb-podbin",
            "usbDeviceProperties-podbin",
            "usbStringPod-podbin",
        ]
        zipSources = []
        testNodes = vmware.pkg.LookupDeliverableNodes("usbTest", "mac64")
        for node in testNodes:
            if node.name.startswith(
                (
                    "usbComponentTest",
                    "libusbComponentTest",
                    "usbEngineTest",
                    "usbUdeTest",
                    "usbMmfwClientTest",
                    "usbMmfwServerTest",
                )
            ):
                zipSources.append((node.dir.abspath, node.name))

        for name in ["usbArbitrator"]:
            nodes = vmware.pkg.UnifiedLookupNode(name, crossBuildEnv=crossBuildEnv)
            for node in nodes:
                zipSources.append((node.dir.abspath, node.name))

        zipSources.append((stageEnv.Dir(deploySrc).abspath, "usb_run_cases.py"))
        zipSources.append((stageEnv.Dir(testDataSrc).abspath, "memdebug_config.json"))

        zipSources.append((stageEnv.Dir(deploySrc).abspath, "usb_client_mac.ini"))
        zipSources.append((stageEnv.Dir(deploySrc).abspath, "usbarbitrator_mac.sh"))
        zipSources.append((stageEnv.Dir(deploySrc).abspath, "fix_paths.sh"))

        zipSources.append((stageEnv.Dir(testDataSrc).abspath, "deployUtils.py"))
        zipSources.append((stageEnv.Dir(testDataSrc).abspath, "deviceUtils.py"))
        zipSources.append((stageEnv.Dir(testDataSrc).abspath, "policyUtils.py"))
        zipSources.append((stageEnv.Dir(deploySrc).abspath, "prepare.sh"))

        # other dependencies
        usbTestEnv = vmware.LookupEnv("usbComponentTest-env", "mac64")
        usbTestEnv.LoadTool(
            [
                "gettext",
                "libglib2",
                "libglibmm",
                "libpcre2",
                "libsigc",
            ]
        )

        deps = [
            "SIGC_REDIST",
            "GETTEXT_REDIST",
            "GLIB_REDIST",
            "GLIBMM_REDIST",
            "PCRE2_REDIST",
        ]
        addDepsToZipSources(usbTestEnv, deps, zipSources)

        # Use usbComponentTestClient.zip to adapt to the CI deploy script.
        zipFile = File(os.path.join(publishDir, "mac64", "usbComponentTestClient.zip"))
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            [],
            "mac64",
            deliverableNodesNames,
            zipSources,
            zipFile,
            [".map"],
            crossBuildEnv=crossBuildEnv,
        )
        nodes += zipNode
    return nodes


def stageHorizonRxUt():
    """Publish the binaries for horizonrxut."""
    releaseDir = vmware.ReleasePackagesDir()
    zipNode = []
    if releaseDir:
        publishDir = os.path.join(releaseDir, "tests", "horizonrxtest", "mac64/")
        targetName = "horizonrxut"
        nodeNames = [
            "horizonrxut",
            "rxSampleUnitTest",
        ]
        zipSources = []

        # Get the UT Framework and sample test cases binaries.
        for nodeName in nodeNames:
            binaryNodes = vmware.pkg.LookupDeliverableNodes(nodeName, "mac64")
            for node in binaryNodes:
                zipSources.append((node.dir.abspath, node.name))

        # Get all the libs required.
        for file in getGtestFilePaths():
            zipSources.append((file.dir.abspath, file.name))

        # Get run case and deployment python script
        deploySrc = "#bora/apps/horizonrxtest/componentTest/deploy"
        zipSources.append((deploySrc, "utility"))
        zipSources.append((deploySrc, "horizonut_run_cases.py"))
        zipSources.append((deploySrc, "horizonut_deploy.py"))

        configPath = "#bora/apps/horizonrxtest/unitTest/sample"
        zipSources.append((configPath, "rxSampleUnitTest.json"))

        zipFile = File(os.path.join(publishDir, "%s.zip" % targetName))
        zipNode = stageEnv.Zip(zipFile, zipSources)
        stageEnv.Depends(zipNode, binaryNodes)
    return zipNode


def stageRTAVClientComponentTest(stageEnv):
    """Stage RTAV CI test packages and binaries for client side

    stageEnv specifies a stage environment object.
    """
    nodes = []
    targetName = ["rtavTestNodeClient"]

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        testsSrc = "#bora/apps/rde/rtav/tests"
        deploySrc = testsSrc + "/testFramework/deploy/client"
        utilsSrc = testsSrc + "/testFramework/utils"
        testSuiteSrc = testsSrc + "/testFramework/testSuite"
        testCasesSrc = testsSrc + "/testCases"

        publishDir = os.path.join(publishDir, "tests")
        deliverableNodesNames = [
            "rtavCliLib",
            "rtavPlugin",
            "rtavSetting",
            "rtavTestNodeClient",
            "omnissabaselib",
        ]

        extraZipSources = []

        rtavPluginEnv = vmware.LookupEnv("rtavPlugin-env", "mac64")
        deps = [
            "LIBPNG_REDIST",
            "OPENSSL_REDIST",
        ]
        addDepsToZipSources(rtavPluginEnv, deps, extraZipSources)

        x264_path = rtavPluginEnv["LIBX264_LIBPATH"]
        extraZipSources.append((x264_path, "."))

        avcodecLib = rtavPluginEnv["FFMPEG_AVCODEC_REDIST"]
        avutilLib = rtavPluginEnv["FFMPEG_AVUTIL_REDIST"]
        extraZipSources.append(
            (os.path.dirname(avcodecLib), os.path.basename(avcodecLib))
        )
        extraZipSources.append(
            (os.path.dirname(avutilLib), os.path.basename(avutilLib))
        )
        extraZipSources.append((Dir(deploySrc).abspath, "."))
        extraZipSources.append((Dir(utilsSrc).abspath, "."))
        extraZipSources.append((Dir(testSuiteSrc).abspath, "."))
        extraZipSources.append((Dir(testCasesSrc).abspath, "."))
        zipFilePath = os.path.join(publishDir, "mac64", "rtavClientComponentTest.zip")
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetName,
            "mac64",
            deliverableNodesNames,
            extraZipSources,
            zipFilePath,
            crossBuildEnv=crossBuildEnv,
        )
        if zipNode is not None:
            nodes += zipNode

    return nodes


def stageRTAVUnitTest(stageEnv):
    """stageRTAVUnitTest

    Stage the test library for RTAV Unit Test.

    """

    zipNode = []
    publishDir = vmware.ReleasePackagesDir()

    if publishDir is not None:
        nodeNames = [
            "rtavUnitTest",
            "omnissabaselib",
        ]
        zipSources = []

        for nodeName in nodeNames:
            binaryNodes = vmware.pkg.LookupDeliverableNodes(nodeName, "mac64")
            for node in binaryNodes:
                zipSources.append((node.dir.abspath, node.name))

        testEnv = vmware.LookupEnv("rtavUnitTest-env", host="mac64")
        deps = [
            "LIBPNG_REDIST",
            "OPENSSL_REDIST",
        ]
        addDepsToZipSources(testEnv, deps, zipSources)

        zipFile = File(os.path.join(publishDir, "tests", "mac64", "rtavUnitTest.zip"))
        zipNode = stageEnv.Zip(zipFile, zipSources)
        stageEnv.Depends(zipNode, binaryNodes)

    return zipNode


def stagePrintRedirComponentTest():
    """
    Stage print redirection CI test packages and binaries
    """
    nodes = []
    zipNodes = []
    targetName = "printRedirTest.zip"
    host = "mac64"
    printTestDir = os.path.join(
        vmware.DirAbsPath(vmware.BuildRoot()), vmware.BuildType(), "printRedirTest"
    )

    scriptsSrc = "#bora/apps/printRedir/componentTest/posixClient"
    zipNodes += vmware.pkg.LookupDeliverableNodes(
        "prvdpplugin", host, crossBuildEnv=crossBuildEnv
    )
    zipNodes += vmware.pkg.LookupDeliverableNodes(
        "prclient", host, crossBuildEnv=crossBuildEnv
    )
    zipNodes += vmware.pkg.LookupDeliverableNodes(
        "omnissabaselib", host, crossBuildEnv=crossBuildEnv
    )

    zipNodes += vmware.DirCopy(
        vmware.EnumerateSourceDir(scriptsSrc),
        Dir(scriptsSrc),
        Dir(printTestDir),
        stageEnv,
    )

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(publishDir, "tests", host)
        zipSources = []

        testEnv = vmware.LookupEnv("prclient-env", host)
        deps = [
            "LIBPNG_REDIST",
            "OPENSSL_REDIST",
        ]
        addDepsToZipSources(testEnv, deps, zipSources)

        for node in zipNodes:
            zipSources.append((node.dir.abspath, node.name))
        nodes += stageEnv.Zip(File(os.path.join(publishDir, targetName)), zipSources)
    return nodes


def stagePrintRedirUnitTest():
    """stagePrintRedirUnitTest
    Stage the binaries for printer redirection UT.
    """

    nodes = []
    targetName = ["printredirut"]
    zipNode = []

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(publishDir, "tests")
        deliverableNodesNames = ["printredirut"]
        zipSources = []

        for nodeName in deliverableNodesNames:
            binaryNodes = vmware.pkg.LookupDeliverableNodes(nodeName, "mac64")
            for node in binaryNodes:
                zipSources.append((node.dir.abspath, node.name))
        zipFile = File(os.path.join(publishDir, "mac64", "printredirut.zip"))
        zipNode = stageEnv.Zip(zipFile, zipSources)
        stageEnv.Depends(zipNode, binaryNodes)

    return zipNode


def stageClientSdkSCAuthUnitTest():
    zipNode = []

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(publishDir, "tests")
        deliverableNodesNames = [
            "clientSdkSCAuthUT",
            "omnissabaselib",
            "libcrtbora",
            "libcds",
            "rtavCliLib",
        ]
        zipSources = []

        for nodeName in deliverableNodesNames:
            binaryNodes = vmware.pkg.LookupDeliverableNodes(nodeName, "mac64")
            for node in binaryNodes:
                zipSources.append((node.dir.abspath, node.name))
        zipFile = File(os.path.join(publishDir, "mac64", "clientSdkSCAuthUnitTest.zip"))
        zipNode = stageEnv.Zip(zipFile, zipSources)
        stageEnv.Depends(zipNode, binaryNodes)

    return zipNode


def stageClipboardUnitTest(stageEnv):
    """stageClipboardUnitTest
    Stage the binaries for Clipboard Unit Test.
    """
    nodes = []
    targetName = ["clipboardUnitTest"]
    deliverableNodesNames = [
        "clipboardUnitTest",
        "omnissabaselib",
    ]

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(publishDir, "tests")

        extraZipSources = []
        host = "mac64"

        testEnv = vmware.LookupEnv("clipboardUnitTest-env", host)
        deps = [
            "LIBPNG_REDIST",
            "OPENSSL_REDIST",
            "SIGC_REDIST",
            "PCRE2_REDIST",
        ]
        addDepsToZipSources(testEnv, deps, extraZipSources)

        zipFilePath = os.path.join(publishDir, host, "clipboardUnitTest.zip")
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetName,
            host,
            deliverableNodesNames,
            extraZipSources,
            zipFilePath,
            crossBuildEnv=crossBuildEnv,
        )
        if zipNode is not None:
            nodes += zipNode

    return nodes


def stageClipboardClientComponentTest(stageEnv):
    """stageClipboardClientComponentTest
    Stage the binaries for Clipboard Component Test.
    """

    nodes = []
    targetName = ["mksvchanComponentTest"]

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(publishDir, "tests")
        deliverableNodesNames = [
            "mksvchanComponentTest",
            "mksvchanclient",
            "vdpservice",
            "omnissabaselib",
        ]

        extraZipSources = [
            (
                stageEnv.Dir(
                    "#bora/apps/rde/mksvchan/tests/" "componenttest/clipboard"
                ).abspath,
                "mksvchan_client_mac.ini",
            ),
            (
                stageEnv.Dir(
                    "#bora/apps/rde/mksvchan/tests/componenttest/" "clipboard"
                ).abspath,
                "configuration",
            ),
            (
                stageEnv.Dir(
                    "#bora/apps/rde/mksvchan/tests/componenttest/" "clipboard/testData"
                ).abspath,
                ".",
            ),
        ]

        host = "mac64"
        testEnv = vmware.LookupEnv("mksvchanComponentTest-env", host)

        deps = [
            "GETTEXT_REDIST",
            "SIGC_REDIST",
            "GLIB_REDIST",
            "GLIBMM_REDIST",
            "LIBPNG_REDIST",
            "OPENSSL_REDIST",
            "PCRE2_REDIST",
        ]
        addDepsToZipSources(testEnv, deps, extraZipSources)

        zipFilePath = os.path.join(publishDir, host, "mksvchanComponentTestClient.zip")
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetName,
            host,
            deliverableNodesNames,
            extraZipSources,
            zipFilePath,
            crossBuildEnv=crossBuildEnv,
        )
        if zipNode is not None:
            nodes += zipNode

    return nodes


def stageSmartcardClientComponentTest(stageEnv):
    """stageSmartcardClientComponentTest

    Stage the binaries for Smartcard Component Test.
    Please note this feature is only support for MAC and Linux platform.
    """

    nodes = []
    targetName = ["scredirvchanComponentTest"]

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(publishDir, "tests")
        deliverableNodesNames = ["scredirvchanComponentTest", "scredirvchan", "sccheck"]
        extraZipSources = []
        testDataSrc = "#bora/apps/rde/scredirvchan/tests/componenttest"
        extraZipSources.append(
            (stageEnv.Dir(testDataSrc).abspath, "scredirvchan_client_mac.ini")
        )
        extraZipSources.append((Dir(testDataSrc).abspath, "testData"))

        testEnv = vmware.LookupEnv("scredirvchanComponentTest-env", host="mac64")
        testEnv.LoadTool(
            [
                "gettext",
                "libsigc",
                "libglib2",
                "libglibmm",
                "libpcre2",
            ]
        )

        deps = [
            "SIGC_REDIST",
            "GETTEXT_REDIST",
            "GLIB_REDIST",
            "GLIBMM_REDIST",
            "PCRE2_REDIST",
        ]
        addDepsToZipSources(testEnv, deps, extraZipSources)

        host = "mac64"
        zipFilePath = os.path.join(
            publishDir, host, "scredirvchanComponentTestClient.zip"
        )
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetName,
            host,
            deliverableNodesNames,
            extraZipSources,
            zipFilePath,
            crossBuildEnv=crossBuildEnv,
        )
        if zipNode is not None:
            nodes += zipNode

    return nodes


def stageScannerRedirUnitTest(stageEnv):
    """stageScannerRedirUnitTest
    Stage the binaries for Scanner Redirection UT.
    """

    nodes = []
    targetName = ["scannerRedirUt"]

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(publishDir, "tests")
        deliverableNodesNames = ["scannerRedirUt"]
        host = "mac64"
        zipFilePath = os.path.join(publishDir, host, "scannerRedirUt.zip")

        extraZipSources = []
        testEnv = vmware.LookupEnv("scannerRedirUt-env", host)
        deps = [
            "GTEST_REDIST",
            "LIBPNG_REDIST",
        ]
        addDepsToZipSources(testEnv, deps, extraZipSources)

        nodes += vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetName,
            host,
            deliverableNodesNames,
            extraZipSources,
            zipFilePath,
        )
    return nodes


def stageTsdrComponentClientTest(stageEnv):
    """stageTsdrComponentClientTest

    Stage the binaries for Tsdr Component Client Test.
    """

    nodes = []
    targetNames = ["tsdrComponentTest"]

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(vmware.DirAbsPath(publishDir), "tests")
        deliverableNodesNames = [
            "tsdrClient",
            "tsdrComponentTest",
            "vdpservice",
            "omnissabaselib",
        ]
        host = "mac64"
        extraZipSources = [
            (
                stageEnv.Dir("#bora/apps/rde/tsdr/tests/componenttest").abspath,
                "tsdr_client_mac.ini",
            ),
        ]

        testEnv = vmware.LookupEnv("tsdrComponentTest-env", host)
        deps = [
            "GETTEXT_REDIST",
            "SIGC_REDIST",
            "GLIB_REDIST",
            "GLIBMM_REDIST",
            "LIBPNG_REDIST",
            "OPENSSL_REDIST",
            "PCRE2_REDIST",
        ]
        addDepsToZipSources(testEnv, deps, extraZipSources)

        zipFilePath = os.path.join(publishDir, host, "tsdrComponentTestClient.zip")
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetNames,
            host,
            deliverableNodesNames,
            extraZipSources,
            zipFilePath,
            crossBuildEnv=crossBuildEnv,
        )
        if zipNode is not None:
            nodes += zipNode

    return nodes


def stageTsdrClientUnitTest(stageEnv):
    """stageTsdrClientUnitTest

    Stage the binaries for Tsdr Client Unit Test.
    """

    nodes = []
    targetName = ["tsdrClientUnitTest"]
    deliverableNodesNames = [
        "tsdrClientUnitTest",
        "omnissabaselib",
    ]

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(vmware.DirAbsPath(publishDir), "tests")
        host = "mac64"
        extraZipSources = []

        # Gather the list of all third party dependencies.
        testEnv = vmware.LookupEnv("tsdrClientUnitTest-env", host)
        deps = [
            "SIGC_REDIST",
            "GETTEXT_REDIST",
            "GLIB_REDIST",
            "GLIBMM_REDIST",
            "PCRE2_REDIST",
            "LIBPNG_REDIST",
            "OPENSSL_REDIST",
        ]
        addDepsToZipSources(testEnv, deps, extraZipSources)

        zipFilePath = os.path.join(publishDir, host, "tsdrClientUnitTest.zip")
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetName,
            host,
            deliverableNodesNames,
            extraZipSources,
            zipFilePath,
            excludedFileFormats=[".map"],
            crossBuildEnv=crossBuildEnv,
        )
        if zipNode is not None:
            nodes += zipNode

    return nodes


def stage_hcmac(relPP):
    """Omnissa Horizon Client.app"""
    dmgPP = relPP / "dmg"
    uiPP = relPP / (productName + ".app")
    appPP = uiPP / "Contents"
    resPP = appPP / "Resources"
    fwPP = appPP / "Frameworks"
    libPP = appPP / "Library"
    nodes = []
    nodes += stage_bin(appPP, libPP, fwPP)
    nodes += stage_ffmpeg(fwPP)
    nodes += stage_urlfilter(libPP)
    nodes += stage_urlNativeMessageHostMac(libPP)
    # nodes += stage_vespa(libPP)
    nodes += stage_plugins(libPP, fwPP)
    nodes += stage_lldpd(libPP, fwPP)
    nodes += stage_usb(libPP, fwPP)
    nodes += stage_pcoip(fwPP)
    nodes += stage_docker(libPP)
    nodes += stage_remotemks_bundle(resPP, fwPP)
    nodes += stage_printerredir(libPP, fwPP)
    nodes += stage_scannerredir(libPP, fwPP)
    nodes += stage_telemetry(libPP, fwPP)
    nodes += stage_uninstaller(relPP)

    # Seperate Debug info and the rest
    debugKitFile = relPP.File(
        "%s-%s-debugkit.tar.gz" % (productName.replace(" ", "-"), productVersion)
    )
    cmd = [
        "$CD '$LOCATION.abspath' && \
         $FIND '$SOURCEDIR.name' -name '*.dSYM' \
            -exec $TAR -zcf '$DEBUGKIT.abspath' {} +",
    ]
    action = vmware.ActionWithDisplay(cmd, "Creating Debug Kit from $SOURCEDIR")
    debugKitNodes = stageEnv.Command(
        debugKitFile,
        nodes,
        action,
        LOCATION=Dir(relPP),
        SOURCEDIR=Dir(uiPP),
        DEBUGKIT=debugKitFile,
    )
    stageEnv.NoCache(debugKitNodes)
    nodes += [debugKitNodes]

    # These resource nodes should not be dependency of debugkit
    nodes += stage_plist(appPP)
    nodes += stage_messages(libPP)
    nodes += stage_resources(resPP)
    nodes += stage_icon(resPP)
    nodes += stage_support(libPP)

    # Build dmg files
    dmgFileName = "%s-%s.dmg" % (productName.replace(" ", "-"), productVersion)
    dmgFile = relPP.File(dmgFileName)
    dmgNodes = stage_dmg(dmgFile, dmgPP, uiPP, relPP, nodes)

    if not vmware.ReleasePackagesDir():
        return debugKitNodes + dmgNodes

    # Publish to release dir
    releasePP = vmware.PathPrefixer(vmware.ReleasePackagesDir())
    releaseNodes = []

    releaseNodes += stage_dct_client_component_test(stageEnv)

    for node in dmgNodes:
        releaseNodes += stageEnv.FileCopy(releasePP.File(node.name), node)

    if not vmware.LocalOpts.GetBool("ENABLE_CODE_COV", False):
        # For coverage build, don't publish
        releaseNodes += stageEnv.FileCopy(
            releasePP.File(debugKitFile.name), debugKitFile
        )

        # These actions do not need to happen for the beta bundle
        # This check will be removed once the Teams optimization feature is made GA
        releaseNodes += stageEnv.TextSubst(
            releasePP.File(
                "%s-%s-wireless-mac-platform-install.html"
                % (productName.replace(" ", "-"), productVersion)
            ),
            File(
                "#horizonclient/view/openClient/cocoa/"
                "view-mac-wireless-install.html.in"
            ),
            TEXTSUBSTARGS=[
                ("@BUILD_URL@", vmware.LocalOpts.GetString("BUILD_URL", "")),
                ("@BUILD_NUMBER@", vmware.BuildNumber()),
                ("@VERSION@", productVersion),
            ],
        )
        cds_payload = relPP / "packages" / "product" if deemIntegrated else dmgPP
        releaseNodes += stage_cds(
            releasePP, cds_payload / (productName + ".pkg"), dmgNodes
        )

    releaseNodes += stage_ci_viewclient_stub(relPP, releasePP)
    stageEnv.NoCache(releaseNodes)
    return releaseNodes


# Here 's where the work really starts #
stagePath = vmware.PathPrefixer(
    os.path.join(vmware.BuildRoot(), vmware.Product(), vmware.BuildType(), "hcmac")
)

projectName = "Horizon.Client.Macos"
vmware.pkg.ClientProjectName = projectName
vmware.pkg.ClientFramework = "net8.0-macos"
vmware.pkg.ClientRootPath = vmware.Dir("#horizonclient/view/oneClient").abspath
vmware.pkg.ClientArtifactsPath = vmware.Dir(
    os.path.join(vmware.BuildRoot(), "build", projectName)
).abspath
vmware.pkg.ClientConfigFile = os.path.join(
    vmware.pkg.ClientArtifactsPath, "NuGet.Config"
)
vmware.pkg.ClientTestResultsPath = os.path.join(vmware.BuildRoot(), "testResults")

allNodes = stage_hcmac(stagePath)

# Stage UT test library of UI/libcdk/SDK
allNodes += stage_client_ui_cdk_sdk_unittests(stageEnv, stagePath)

# Stage crtboraApiTest component test binaries
allNodes += stage_crtbora_test_bundle(stagePath, stageEnv)

# Stage rdeClient component test binaries
allNodes += stage_rdeClient_test_bundle(stagePath, stageEnv)

# Stage viewClientApiTest component test binaries
allNodes += stage_viewClient_test(stagePath, stageEnv)

# Stage crtboraRXDnDComponentTest test library
allNodes += stage_crtbora_rxdnd_component_test(stageEnv)

# Stage crtboraRXFAComponentTest test library
allNodes += stage_crtbora_rxfa_component_test(stagePath, stageEnv)

# Stage horizonRxTest test library
allNodes += stageHorizonRxTest(stagePath)

# Stage horizonRxUt test library
allNodes += stageHorizonRxUt()

# Stage CI test library of RTAV
allNodes += stageRTAVClientComponentTest(stageEnv)

# Stage UT test library of RTAV
allNodes += stageRTAVUnitTest(stageEnv)

# Stage printer redirection CI and UT test binaries
allNodes += stagePrintRedirComponentTest()
allNodes += stagePrintRedirUnitTest()

# Stage FIDO2 Client component and unit test library
allNodes += stageClientFido2ComponentTest(stageEnv)
allNodes += stageClientFIDO2UnitTest(stageEnv)

# Stage USB Client unit test library
allNodes += stageClientUSBUnitTest(stageEnv)

# Stage USB Client component test bits
allNodes += stageUSBClientComponentTest(stageEnv)

# Stage Clipboard unit test bits
allNodes += stageClipboardUnitTest(stageEnv)

# Stage Clipboard component test bits
allNodes += stageClipboardClientComponentTest(stageEnv)

# Stage smartcard client component test bits
allNodes += stageSmartcardClientComponentTest(stageEnv)
# Stage smartcard client unit test library
allNodes += stageClientSdkSCAuthUnitTest()

# Stage Telementry UT test binaries
allNodes += stageTlmRdpvcbridgeUnitTest(stageEnv)

# Stage URL Redirection Component and UT test binaries
allNodes += stageURLComponentTestClient(stageEnv)
allNodes += stageURLUnitTest(stageEnv)

# Stage Scanner Redirection Unit Test binaries
allNodes += stageScannerRedirUnitTest(stageEnv)
# Stage TSDR Client unit test library
allNodes += stageTsdrClientUnitTest(stageEnv)

# Stage TSDR Client component test bits
allNodes += stageTsdrComponentClientTest(stageEnv)

# If you don't want to touch oneClient and run fossa,
# please use this alias.
vmware.Alias("prod", allNodes)

if not vmware.LocalOpts.GetBool("ENABLE_CODE_COV", False):
    # For coverage build, don't publish
    viewClientNodes = stageViewClient()
    vmware.Alias("viewClient", viewClientNodes)

    allNodes += viewClientNodes

# Stage code coverage data at the end to include all data.
allNodes += vmware.pkg.StageCodeCoverageData(stageEnv, "hcmac", allNodes)

vmware.Alias("hcmac", allNodes)
vmware.Alias("hccrt", allNodes)
