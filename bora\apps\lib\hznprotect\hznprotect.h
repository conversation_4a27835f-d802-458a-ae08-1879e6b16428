/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * bora/apps/lib/hznprotect.h -
 *
 *    hznprotect.h is header file for implementation of user's key input
 *    protection.
 */

#pragma once


#ifdef __cplusplus
extern "C" {
#endif

#define BLOCK_KEYLOGGER_WINDOW_PROPERTY_NAME L"hzn_block_keylogger"
#define BLOCK_KEYLOGGER_GPO_ON ((HANDLE)0x0b4B4C01)  // 0xb K L 0x1
#define BLOCK_KEYLOGGER_GPO_OFF ((HANDLE)0x0b4B4C00) // 0xb K L 0x0

#define BLOCK_SENDINPUT_WINDOW_PROPERTY_NAME L"hzn_block_sendinput"
#define BLOCK_SENDINPUT_GPO_ON ((HANDLE)0x0b534901)  // 0xb S I 0x1
#define BLOCK_SENDINPUT_GPO_OFF ((HANDLE)0x0b534900) // 0xb S I 0x0

#define WIN11_22H2_OS_BUILD 22621

BOOL HznProtect_InstallHooks(void);
BOOL HznProtect_Init(void);
void HznProtect_Exit(void);
BOOL HznProtect_OpenDriver(void);
void HznProtect_CloseDriver(void);
BOOL HznProtect_IsDriverOpened(void);
void HznProtect_SetEncryptStatus(HWND hWnd, BOOL focused);
BOOL HznProtect_DecryptMessage(PKBDLLHOOKSTRUCT msg);
char *HznProtect_GetStatistics(void);
BOOL HznProtect_AddAllowedDlls(BYTE *buffer, int length);
BOOL HznProtect_IsMyselfProtected(void);
BOOL HznProtect_IsSupported(void);
UINT HznProtect_SendInput(UINT cInputs, LPINPUT pInputs, INT cbSize);
BOOL HznProtect_IsSendInputBlocked(void);
BOOL HznProtect_IsAMD64(void);

#ifdef VMWPROTECT_TEST
void HznProtect_InitTester(void *vmwptester);
#endif

#ifdef __cplusplus
}
#endif
