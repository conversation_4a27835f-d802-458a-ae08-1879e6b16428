{"vcAddress": "wdc1-vc01.omnissa.com", "snapshotName": "Base Runner State", "runners": [{"name": "VD-BUB-U22-XX", "labels": "non-vgpu,vdub-benev,vdub-ut", "location": "blast-dev-team-datacenter/vm/Hzn-Rx-DevOps-US/Github Runners/Linux/BUB"}, {"name": "VD-BUB-W10-XX-NG", "labels": "win10,non-vgpu,vdub-ut,vdub-bitb", "location": "blast-dev-team-datacenter/vm/Hzn-Rx-DevOps-US/Github Runners/Windows/BUB"}, {"name": "VD-BAV-W10-XX-NG", "labels": "win10,non-vgpu,vdub-blast-audio,vdub-blast-video", "location": "blast-dev-team-datacenter/vm/Hzn-Rx-DevOps-US/Github Runners/Windows/BAV/Non vGPU"}, {"name": "VD-DSP-W10-XX-NG", "labels": "win10,non-vgpu,vdub-display", "location": "blast-dev-team-datacenter/vm/Hzn-Rx-DevOps-US/Github Runners/Windows/DSP/Non-vGPU/Win 10"}, {"name": "VD-DSP-W11-XX-NG", "labels": "win11,non-vgpu,vdub-display", "location": "blast-dev-team-datacenter/vm/Hzn-Rx-DevOps-US/Github Runners/Windows/DSP/Non-vGPU/Win 11"}]}