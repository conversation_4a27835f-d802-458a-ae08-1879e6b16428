# Copyright (c) Omnissa, LLC. All rights reserved.
# This product is protected by copyright and intellectual property laws in the
# United States and other countries as well as by international treaties.
# -- Omnissa Restricted

"""horizonagent

This file builds the horizonagent target using scons

Maintainers: <EMAIL>.

"""

from vmware import ProductDefinition, Host
import horizon
import vmware


class HorizonAgent(ProductDefinition):
    buildHosts = [
        ("win64", "windows2016-clean"),
    ]
    versionPrefix = "VIEW"
    vmx86Flag = "VMX86_HORIZON_VIEW"
    configDefaults = {
        "USE_NEW_GLIB": True,
        "GOBUILD_FILTER_EXCEPTIONS": [
            "sim",
        ],
        "USE_OPENSSL3": True,
        "SSL_DIRECT_LINK": True,
    }
    default = "horizonagent"

    def __init__(self, *args):
        super(HorizonAgent, self).__init__(*args)

        # Tell SCons about DaaS Agent as it relates to vm_product_versions.h.
        vmware.SetProductVersionMacroPrefix("horizondaasagent", "HORIZON_DAAS_AGENT")

        # Tell SCons about updatetool as it relates to vm_product_versions.h.
        vmware.SetProductVersionMacroPrefix("horizonupdatetool", "HORIZON_UPDATETOOL")

        # Tell SCons about viewvc as it relates to vm_product_versions.h.
        vmware.SetProductVersionMacroPrefix("viewvc", "VIEWVC")

        # Tell SCons about horizon tools as it relates to vm_product_versions.h.
        vmware.SetProductVersionMacroPrefix("horizontool", "HORIZONTOOL")

        # Tell SCons about HorizonYYMM, mfw, mks vchan
        # as they relates to vm_product_versions.h.
        vmware.SetProductVersionMacroPrefix("HorizonYYMM", "HORIZON_YYMM")
        vmware.SetProductVersionMacroPrefix("mfw", "MFW")
        vmware.SetProductVersionMacroPrefix("horizonmksvchanagent", "MKSVCHANDEV")

        # Tell SCons about USB Agent it relates to vm_product_versions.h.
        vmware.SetProductVersionMacroPrefix("horizonusbagent", "HORIZON_USB_AGENT")

    def build(self):
        # LOCAL_JAVA_BUILD variable can be set in Local.sc
        #
        # If set, then we do not load any of the platform specific modules.
        # Modules for win32, win64 components depend on cayman components
        # that need cayman_msvc_desktop.
        # cayman_msvc_desktop invokes visual studio 2017 license check.
        #
        # For java modules, maven is used and hence for anyone locally building
        # java components, we skip loading of platform specific modules to
        # avoid visual studio licensing checks.
        localJavaBuild = vmware.LocalOpts.GetBool("LOCAL_JAVA_BUILD", False)

        # This is so bora-vmsoft libs linked statically don't try to export
        # functionality using dllexport.
        vmware.LocalOpts["VMSOFTLIBS_DEFINE_EXPORT"] = True

        # Build the none host specific stuff first.
        self.RunModules(
            "generic",
            [
                "modules/view/horizoncommon/mfwj.py",
                "modules/view/horizonagent/horizon-maven.py",
            ],
        )

        # Don't load win64 components for local java builds
        if not localJavaBuild:
            bh = ["win64"]
            bhWithArm64 = ["win64", "win-arm64"]

            # vnc-win32-hlsl, win32 only (must be run before
            # vncServer.py)
            self.RunModules(
                "win32",
                [
                    "apps/updateBldNumRes.py",
                    "modules/vnc-win32-hlsl.py",
                ],
            )

            self.RunModules(
                "generic",
                [
                    "modules/guestrpc.py",
                    # Required by 'trapapi' in modules/omnissabaselib.py
                    "modules/makex86.py",
                ],
            )

            # Load all lib namespaces.
            self.RunModules(
                ["win32", "win64", "win-arm64"],
                [
                    "modules/libs.py",
                    "modules/tools/libs.py",
                ],
            )

            # Load all lib namespaces.
            self.RunModules(
                ["win32", "win64"],
                ["modules/tools/instutil.py"],
            )

            self.RunModules(
                "generic",
                [
                    "modules/view/blast/blastperfmanifest.py",
                    "modules/view/horizonagent/hzMonPolicyConfig.py",
                ],
            )

            self.RunModules(
                "win32",
                [
                    "modules/driverVer.py",
                    "apps/updateDrvRes.py",
                    "modules/view/rdf/inputMonitor.py",
                    "modules/view/rdf/svgaGtests-ScreenPatternGen.py",
                    "modules/view/tests/win32VNCDisplayTest-hlsl.py",
                ],
            )

            # horizonCommon static lib modules that are used on win32 and
            # win64.
            self.RunModules(
                ["win32", "win64", "win-arm64"],
                [
                    "modules/cedar/base.py",
                    "modules/cedar/config.py",
                    "modules/cedar/log.py",
                    "modules/cedar/task.py",
                    "modules/cedar/ipclib.py",
                    "modules/cedar/hzipc.py",
                    "modules/view/rxUnitTestLib.py",
                    "modules/view/rdeLibs.py",
                    "modules/view/horizonagent/objectMap.py",
                    "modules/view/horizonagent/objectMapTest.py",
                    "modules/view/horizonagent/objectMapTestApp.py",
                    "modules/view/horizonagent/smlib.py",
                    "modules/view/horizonagent/objectMapJson.py",
                    "modules/view/horizonagent/smlibTest.py",
                    "modules/view/horizoncommon/windbgExtensionLib.py",
                    # Product modules.
                    "modules/view/rderft/pingPcoipVchanClient.py",
                    "modules/view/rderft/pingRdpVcbridgeClient.py",
                    "modules/view/rderft/pingVdpServiceClient.py",
                    "modules/view/rderft/rdpvcbridgeTestClient.py",
                    "modules/view/rderft/vdpTest.py",
                    "modules/view/horizonagent/hzMonServiceProto.py",
                    "modules/omnissabaselib.py",
                    "modules/omnissastring.py",
                ],
            )

            # build flattening
            self.RunModules(
                ["win32", "win64", "win-arm64"],
                [
                    "modules/view/horizoncommon/messageframework.py",
                ],
                exports={
                    "forceOpenSSL": 0,
                },
            )

            # modules also needed by arm64 modules.
            self.RunModules(
                ["win32", "win64", "win-arm64"],
                [
                    "apps/viewvc/vvc-view-node.py",
                    "apps/viewvc/vvc-view-node2.py",
                    "apps/viewusb/envs.py",
                    "apps/viewusb/devConfig.py",
                    "apps/viewusb/devFilter.py",
                    "apps/viewusb/stringstore.py",
                    "modules/view/rderft/pcoip_vchan.py",
                    "modules/view/rderft/pcoip_mfw.py",
                    "modules/view/rderft/rdpvcbridge.py",
                    "modules/view/rderft/readbackWindowClient.py",
                    "modules/view/rderft/hznime.py",
                    "modules/view/horizonagent/scredirUtil.py",
                    "modules/view/horizonagent/scredir_caching.py",
                    "modules/view/horizonagent/ntDllLoader.py",
                    "modules/view/horizonagent/interceptUtil.py",
                    "modules/view/horizonagent/nativeApiUtil.py",
                    "modules/view/horizonagent/nativeApiUtilUnitTest.py",
                    "modules/view/horizonagent/securitymanager.py",
                    "modules/view/horizonagent/secmgr.py",
                    # Uses securitymanager-mt.
                    "modules/view/horizonagent/ksplib.py",
                    "modules/view/horizonagent/ws_winauth.py",
                    "modules/view/horizonagent/wsnm_common.py",
                    "modules/view/horizonagent/wssm_common.py",
                    "modules/view/rdf/inputdevtap.py",
                    "modules/view/rderft/appStub.py",
                    "modules/view/rderft/watermarkDrawer.py",
                    "modules/view/rderft/vdpservice.py",
                    "modules/view/rderft/rdsAadAuthServer.py",
                    "modules/view/rderft/rdeServer.py",
                    "modules/view/rderft/rdeServerTest.py",
                    "modules/view/rderft/rtavTestNodeServer.py",
                    "modules/view/rderft/vchanStub.py",
                    "modules/view/rderft/unityShell.py",
                    "modules/vmToolsHook.py",
                    "modules/vmToolsHookProc.py",
                ],
            )

            #  modules dependent on libs above but are not needed on arm64
            self.RunModules(
                ["win32", "win64"],
                [
                    "modules/view/rdf/resolutionSetLib.py",
                    "modules/view/rdf/resolutionSetTest.py",
                    "modules/view/blast/tools/blastSetTopology.py",
                    "modules/view/blast/vmlibsdevenv.py",
                ],
            )

            self.RunModules(
                ["win32"],
                [
                    "modules/view/rderft/blackScreenDetection.py",
                ],
            )

            # Run x64 and Arm64 binary modules.
            self.RunModules(
                bhWithArm64,
                [
                    # NGVC Agent binary modules.
                    "modules/view/horizonagent/appTap.py",
                    "modules/view/horizonagent/omnissa-ic-ga.py",
                    "modules/view/horizonagent/omnissa-ic-ga-unittest.py",
                    "modules/view/horizonagent/omnissa-ic-nga.py",
                    "apps/printredir/prGraphics.py",
                    "apps/printredir/prmon.py",
                    "apps/printredir/prProcessor.py",
                    "apps/printredir/prPS.py",
                    "apps/printredir/prService.py",
                    "apps/printredir/printRedirProto.py",
                    "apps/printredir/printredir.py",
                    "apps/printredir/prserverapi.py",
                    "apps/printredir/prUI.py",
                    "apps/printredir/prUIRes.py",
                    "apps/printredir/prvdpplugin.py",
                    "apps/viewusb/vhublib.py",
                    "apps/viewusb/ws_usbstor.py",
                    "apps/viewusb/ws_vhub.py",
                    "apps/viewusb/ws_vdpvhub.py",
                    "modules/view/horizonagent/azureDetect.py",
                    "modules/view/horizonagent/azureDetectApp.py",
                    # Required by cdsclient and horizon-updatetool
                    "modules/cdsng.py",
                    "modules/installutillib.py",
                    "modules/view/horizonagent/hzaprep.py",
                    "modules/view/horizonagent/hzaprepTest.py",
                    "modules/view/horizonagent/awsBucketClient.py",
                    "modules/view/horizonagent/azureBlobTest.py",
                    "modules/view/horizonagent/cata_fwPlugin.py",
                    "modules/view/horizonagent/cata_fwService.py",
                    "modules/view/horizonagent/cata_fwSM.py",
                    "modules/view/horizonagent/cata_fwTests.py",
                    "modules/view/horizonagent/certificateUtility.py",
                    "modules/view/horizonagent/cdsclient.py",
                    "modules/view/horizonagent/cdsclientTest.py",
                    "modules/view/horizonagent/daCommonUtil.py",
                    "modules/view/horizonagent/daResources.py",
                    "modules/view/horizonagent/daSystemUtil.py",
                    "modules/view/horizonagent/downgradeTool.py",
                    "modules/view/horizonagent/directoryWatcherTest.py",
                    "modules/view/horizonagent/hashTests.py",
                    "modules/view/horizonagent/hzDCT.py",
                    "modules/view/horizonagent/horizon-updatetool.py",
                    "modules/view/horizoncommon/hzMonApi.py",
                    "modules/view/horizoncommon/mfw_java.py",
                    "modules/view/horizoncommon/mfw_test.py",
                    "modules/view/horizonagent/mockAV.py",
                    "modules/view/horizonagent/mockAVClient.py",
                    "modules/view/horizonagent/thirdPartyWrappers.py",
                    "modules/view/horizonagent/mqtt_client.py",
                    "modules/view/horizonagent/ksmlib.py",
                    "modules/view/horizonagent/ksmlibTest.py",
                    "modules/view/horizonagent/ksmlibTestApp.py",
                    "modules/view/horizonagent/ksmNotifier.py",
                    "modules/view/horizonagent/mfwcom.py",
                    "modules/view/horizonagent/mfwgen.py",
                    "modules/view/horizonagent/pcoipTestCtrl.py",
                    "modules/view/horizonagent/authUtil.py",
                    "modules/view/horizonagent/rdsAadAuth.py",
                    "modules/view/horizonagent/rdsTestApp.py",
                    "modules/view/horizonagent/rdsTestAppInner.py",
                    "modules/view/horizonagent/rdsTestLib.py",
                    "modules/view/horizonagent/rxDCTComponentTestAgent.py",
                    "modules/view/horizonagent/dcttestapp.py",
                    "modules/view/horizonagent/secmgrtest.py",
                    "modules/view/horizonagent/secmgrTestRunner.py",
                    "modules/view/horizonagent/smProcDump.py",
                    "modules/view/horizonagent/wscredf.py",
                    "modules/view/horizonagent/perfmon.py",
                    "modules/view/horizonagent/ws_admin.py",
                    "modules/view/horizonagent/ws_appendersupport.py",
                    "modules/view/horizonagent/ws_applaunchmgr.py",
                    "modules/view/horizonagent/ws_applaunchmgrTest.py",
                    "modules/view/horizonagent/ws_applaunchmgr_operationTest.py",
                    "modules/view/horizonagent/ws_cluskeymgr.py",
                    "modules/view/horizonagent/ws_consolesupport.py",
                    "modules/view/horizonagent/ws_cpakeymgr.py",
                    "modules/view/horizonagent/ws_configmgr.py",
                    "modules/view/horizonagent/ws_crl.py",
                    "modules/view/horizonagent/ws_dct.py",
                    "modules/view/horizonagent/ws_daas.py",
                    "modules/view/horizonagent/ws_filesystem.py",
                    "modules/view/horizonagent/ws_java_bridge.py",
                    "modules/view/horizonagent/ws_java_native.py",
                    "modules/view/horizonagent/ws_java_service.py",
                    "modules/view/horizonagent/ws_java_starter.py",
                    "modules/view/horizonagent/ws_javaview.py",
                    "modules/view/horizonagent/ws_ldap.py",
                    "modules/view/horizonagent/ws_perfmon.py",
                    "modules/view/horizonagent/ws_propertiessupport.py",
                    "modules/view/horizonagent/ws_scripts.py",
                    "modules/view/horizonagent/ws_updatemgr.py",
                    "modules/view/horizonagent/ws_vmx.py",
                    "modules/view/horizonAgent/ws_lcm.py",
                    "modules/view/horizonagent/hzMonHelper.py",
                    "modules/view/horizonagent/hzMonApiTestClient.py",
                    "modules/view/horizonagent/smtrack.py",
                    "modules/view/horizonagent/logonmon.py",
                    "modules/view/horizonagent/smsrv.py",
                    "modules/view/horizonagent/smctrl.py",
                    "modules/view/horizonagent/smctrlTest.py",
                    "modules/view/horizonagent/smctrlTestApp.py",
                    "modules/view/horizonagent/smlibTestApp.py",
                    "modules/view/horizonagent/smtrackTest.py",
                    "modules/view/horizonagent/smtrackTestApp.py",
                    "modules/view/rderft/tsdrProto.py",
                    "modules/view/horizonagent/tsdrServer.py",
                    "modules/view/horizonagent/tsdrServerUnitTest.py",
                    "modules/view/horizonagent/tsdr_test.py",
                    "modules/view/horizonagent/vdmadmin.py",
                    "modules/view/horizonagent/vdmexport.py",
                    "modules/view/horizonagent/vdmimport.py",
                    "modules/view/horizonagent/vdmPerfmon.py",
                    "modules/view/horizonagent/vmlm.py",
                    "modules/view/horizonagent/vmprereq-updatetool.py",
                    "modules/view/horizonagent/hznusm.py",
                    "modules/view/horizonagent/hznusmlib.py",
                    "modules/view/horizonagent/winHttpTests.py",
                    "modules/view/horizonagent/ws_onrampkeymgr.py",
                    "modules/view/horizonagent/wslogonscriptlauncher.py",
                    "modules/view/horizonagent/wsnm_desktop.py",
                    "modules/view/horizonagent/wsnm_desktopTest.py",
                    "modules/view/horizonagent/wssm.py",
                    "modules/view/horizonagent/wsnm.py",
                    "modules/view/horizonagent/wsnm_commonTest.py",
                    "modules/view/horizonagent/wsnm_credcache.py",
                    "modules/view/horizonagent/wsnm_certlogon.py",
                    "modules/view/horizonagent/wsnm_helpdesk.py",
                    "modules/view/horizonagent/wsnm_jms.py",
                    "modules/view/horizonagent/wsnm_jms-test.py",
                    "modules/view/horizonagent/wsnm_jmsbridge.py",
                    "modules/view/horizonagent/wsnm_mqtt.py",
                    "modules/view/horizonagent/wsnm_mqttTest.py",
                    "modules/view/horizonagent/wsnm_psgc.py",
                    "modules/view/horizonagent/psgctest.py",
                    "modules/view/horizonagent/wsnm_scredir.py",
                    "modules/view/horizonagent/wsnm_xmlapi.py",
                    "modules/view/horizonagent/wssm_desktop.py",
                    "modules/view/horizonagent/wssm_desktopTest.py",
                    "modules/view/horizonagent/wssm_helpdesk.py",
                    "modules/view/horizonagent/wssm_resources.py",
                    "modules/view/rderft/dndServerUnitTest.py",
                    "modules/view/rderft/html5mmrNativeMessagingHost.py",
                    "modules/view/rderft/perfTracker.py",
                    "modules/view/rderft/whfbRedirection.py",
                    "modules/view/rderft/whfbUnitTest.py",
                    "modules/view/rderft/html5mmrServer.py",
                    "modules/view/rderft/html5mmrServerTest.py",
                    "modules/view/rderft/mksvchanserverLib.py",
                    "modules/view/rderft/mksvchanserver.py",
                    "modules/view/rderft/mksvchanComponentTest.py",
                    "modules/view/rderft/mksvchanDnDComponentTest.py",
                    "modules/view/rderft/perfTrackerWaveDllMock.py",
                    # clipboardServerUnitTest.py needs to be added after mksvchanserverLib.py
                    "modules/view/rderft/clipboardServerUnitTest.py",
                    "modules/view/rderft/scredirvchanTestApp.py",
                    "modules/view/rderft/fcpServerUnitTest.py",
                    "modules/view/rderft/screenCaptureServerUnitTest.py",
                    "modules/view/rderft/tsdrComponentTest.py",
                    "modules/view/rderft/tsmmrComponentTest.py",
                    "modules/view/rderft/tsmmrTestWmplayer.py",
                    "modules/view/rderft/usbRedirectionServer.py",
                    "modules/view/rdf/audiodevtap.py",
                    "modules/view/rdf/audiodevtap-examples.py",
                    "modules/view/rdf/audiodevtapConfigTool.py",
                    "modules/view/rdf/blittest.py",
                    "modules/view/rdf/rdsManager.py",
                    "apps/viewusb/udeTest.py",
                    "apps/viewusb/usbAgentTest.py",
                    "apps/viewvc/vvc-view-hub.py",
                    "apps/viewvc/vvc-view-hub2.py",
                    "modules/view/blast/pipelib.py",
                    "modules/view/blast/abctrl.py",
                    "modules/view/blast/abcommon.py",
                    "modules/view/blast/abRdeStub.py",
                    "modules/view/blast/tests/abRdeStubTest.py",
                    "modules/view/blast/customaction.py",
                    "modules/cedar/telemetry.py",
                    "modules/view/horizonAgent/etlmapi_mock.py",
                    "modules/view/horizonagent/hzMonService.py",
                    "modules/view/horizonagent/hzMonServiceTest.py",
                    # UT modules
                    "modules/view/horizonagent/winCertTests.py",
                    "modules/view/horizonrxtest/rxTestCred.py",
                    "modules/view/horizonrxtest/rxTestNodeServer.py",
                    "modules/view/horizonrxtest/socket_vchan.py",
                    "modules/view/horizonrxtest/socket_mfw.py",
                    "modules/view/horizonrxtest/horizonrxut.py",
                    "modules/view/horizonagent/ws_lcmTest.py",
                    "modules/view/horizonagent/ws_updatemgrTest.py",
                ],
            )

            # Run x64-only binary modules.
            self.RunModules(
                bh,
                [
                    "apps/printredir/prTestPrint.py",
                    "apps/printredir/prtestserver.py",
                    "modules/view/horizonagent/adamInstUtil.py",
                    "modules/view/rdf/svgadevtap.py",
                    "modules/view/blast/vncNvEnc.py",
                    "modules/view/blast/vncReplay.py",
                    "modules/view/blast/wave.py",
                    "modules/view/blast/service.py",
                    "modules/view/blast/sessionrecorder.py",
                    "modules/view/blast/worker.py",
                    "modules/view/vncServer.py",
                    "modules/view/blast/bitb.py",
                    "modules/view/rderft/rtavProto.py",
                    "modules/view/rderft/rtavLibs.py",
                    "modules/view/rderft/hzbuslib.py",
                    "modules/view/rderft/rtavPlugin.py",
                    "modules/view/rderft/teamsHelper.py",
                ],
            )

            self.RunModules(
                ["win32", "win64", "win-arm64"],
                [
                    "modules/view/drivers/vmwrdsdrvdiag.py",
                    "modules/view/horizonagent/certStoreIntercept.py",
                    "modules/view/horizonagent/enrollment_test.py",
                    "modules/view/horizonagent/es_diag.py",
                    "modules/view/drivers/tests/geoRedirTest.py",
                    "modules/view/drivers/tests/hznReflectTest.py",
                    "modules/view/horizonagent/lacu_test.py",
                    "modules/view/horizonagent/scredir_vchanclient.py",
                    "modules/view/horizonagent/hznsci.py",
                    "modules/view/horizonagent/hznsci_test.py",
                    "modules/view/horizonagent/ws_diag.py",
                    "modules/view/horizonagent/ws_dllhost.py",
                    "modules/view/horizonagent/ws_klog.py",
                    "modules/view/horizonagent/wsauth.py",
                    "modules/view/horizonagent/wsauth_test.py",
                    "modules/view/horizonagent/wsnm_certauthority.py",
                    "modules/view/horizonagent/wsnm_certenroll.py",
                    "modules/view/rderft/autociClientRdp.py",
                    "modules/view/rderft/autociClientVdp.py",
                    "modules/view/rderft/browserPluginIE.py",
                    "modules/view/rderft/hzwebauthn.py",
                    "modules/view/rderft/hzwebauthnUnitTest.py",
                    "modules/view/rderft/pcoipVchanConnCBTest.py",
                    "modules/view/rderft/pingPcoipVchanServer.py",
                    "modules/view/rderft/pingRdpVcbridgeServer.py",
                    "modules/view/rderft/pingVdpServiceServer.py",
                    "modules/view/rderft/rdpvcbridgeTestServer.py",
                    "modules/view/rderft/readbackWindowServer.py",
                    "modules/view/rderft/rpcChannelTestServer.py",
                    "modules/view/rderft/tsdrTest.py",
                    "modules/view/rderft/uncService.py",
                    "modules/view/rderft/scannerRedirProto.py",
                    "modules/view/rderft/scannerRedirAgent.py",
                    "modules/view/rderft/sdrProto.py",
                    "modules/view/rderft/sdrServer.py",
                    "modules/view/rderft/tsmmrServer.py",
                    "modules/view/rderft/uncRedirect.py",
                    "modules/view/rderft/unityShellTests.py",
                    "modules/view/rderft/urlNativeMessageHost.py",
                    "modules/view/rderft/urlProtocolLaunchHelper.py",
                    "modules/view/rderft/urlProtocolIntercept.py",
                    "modules/view/rderft/vdpservicepriv.py",
                    "modules/view/rderft/vdpserviceprivunittest.py",
                    "modules/view/rderft/vdpserviceWsnmPlugin.py",
                    "modules/view/rderft/rxgservice.py",
                    "modules/view/horizonagent/whfbWsksp.py",
                    "modules/view/horizonagent/wsksp.py",
                    "modules/view/rdf/hznaudioendpoint.py",
                    "modules/view/rdf/hznaudioendpointUnitTest.py",
                    "modules/view/rderft/tsmmrServerDShow.py",
                ],
            )

            # Run binary modules that are built on both win32 and win64
            # and weren't included in any of the previous lists.
            self.RunModules(
                ["win32", "win64"],
                [
                    "modules/view/horizonagent/wsksp_test.py",
                    "modules/view/rderft/sdoSensor.py",
                ],
            )

            self.RunModules(
                "win-kernel",
                [
                    "modules/view/horizonagent/drivers/hznbus.py",
                    "modules/view/horizonagent/drivers/hznvwebcam.py",
                    "modules/view/horizonagent/drivers/hznvaudioin.py",
                    "modules/view/drivers/hzngeoloc.py",
                    "modules/view/drivers/hznvscrd.py",
                    "modules/view/horizonagent/drivers/omnksm.py",
                    "modules/view/drivers/hznrdsinput.py",
                    "modules/view/drivers/hznrdsaudio.py",
                    "modules/view/drivers/hznrdsdd.py",
                    "modules/view/drivers/hznicpdr.py",
                    "modules/view/drivers/hznvaudio.py",
                    "modules/view/drivers/hznvdisplay.py",
                    "modules/view/drivers/hznsdo.py",
                    "modules/view/drivers/hznvudpd.py",
                    "modules/view/drivers/hznvhub.py",
                    "modules/view/drivers/hznflstor.py",
                    "modules/view/drivers/hznufhid.py",
                    "modules/view/drivers/vmwude.py",
                    "modules/view/drivers/vmwusbt.py",
                    "modules/view/drivers/hznvidd.py",
                    "modules/view/drivers/hznvidd2.py",
                    "modules/view/drivers/hzncdrfilter.py",
                    "modules/view/drivers/omnsdr.py",
                    "modules/view/drivers/hznregreflection.py",
                    # vmkbd.py is directly under modules folder and not under
                    # modules/view/drivers folder because workstation also uss it
                    "modules/vmkbd.py",
                ],
            )

    def customizeDefaultEnv(self, env):
        horizon.customizeDefaultEnv(env)

        env.Append(
            CPPDEFINES={
                # Required for lib/blastSockets used by appblast
                "VVCHUB_SUPPORT": None,
                # Required for apptap
                "UNITY_FOR_VIEW": None,
            }
        )

        if Host().IsWindows():
            if "MSVC_VERSION_TUPLE" in env and env["MSVC_VERSION_TUPLE"] >= (14, 0):
                env.Append(
                    CPPFLAGS=[
                        "/wd4477",  # Formatting wstr into %s in many, many places.
                    ]
                )
