name: appblastlibs-ut

on:
  workflow_call:
    inputs:
      buildtype:
        type: string
        description: Build type
        required: true
      enable_tests:
        type: string
        description: Enable tests
        required: true 

  workflow_dispatch:
    inputs:
      runNumber:
        type: string
        description: 'run number of appblastlibs'
        required: true
      runAttempt:
        type: string
        description: 'run attempt'
        required: true
        default: '1'
      buildtype:
        type: choice
        description: Build type
        required: true
        default: beta
        options:
          - beta
          - obj
          - release
      enable_tests:
        type: choice
        description: Enable tests
        required: true
        default: all
        options:
          - all
          - AbCertMgr
          - BENeV
          - BitB
          - Blast
          - Display
          - Networking
          - VmwAudio
          - VmwVideo
          - VncRegEncPerfTest
          - VvcSessionManager

env:
  RUNNUMBER: ${{ github.event_name == 'workflow_dispatch' && inputs.runNumber || github.run_number }}
  RUNATTEMPT: ${{ github.event_name == 'workflow_dispatch' && inputs.runAttempt || github.run_attempt }}
  CODECOV_WIN_INCLUSIONS: >-
    bora/apps/rde/blast/appblast,
    bora/lib/vnc,
    bora/lib/vncConnectionManager,
    bora/lib/vvclib,
    bora-vmsoft/hznvaudio/audiooutconfig/lib,
    bora-vmsoft/hznvaudio/devtap/audiodevtap
  CODECOV_LIN_INCLUSIONS: >-
    bora/lib/blastSockets,
    bora/lib/vnc,
    bora/lib/vncConnectionManager,
    bora/lib/vvclib

jobs:
  AbCertMgr:
    if: ${{ inputs.enable_tests == 'all' ||
            inputs.enable_tests == 'AbCertMgr' }}
    runs-on:
      - self-hosted
      - windows
      - vdub-ut

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Execute UT-AbCertMgr
        uses: ./.github/actions/testframework/UT-AbCertMgr
        with:
          artifactoryApiToken: ${{ secrets.ARTIFACTORY_GITHUB_DELIVERABLES_API_TOKEN }}
          artifactoryBaseUrl: ${{ vars.ARTIFACTORY_GITHUB_DELIVERABLES_URL }}
          useOpenCppCoverage: true
          codecovInclusions: ${{ env.CODECOV_WIN_INCLUSIONS }}
          reportDbUri: ${{ secrets.VDUB_REPORT_DB_W }}
          reportDbName: ${{ secrets.VDUB_REPORT_DB_NAME }}
          reportDbCollection: ${{ secrets.VDUB_REPORT_COL_NAME }}
          runNumber: $RUNNUMBER
          runAttempt: $RUNATTEMPT
          buildtype: ${{ inputs.buildtype }}

  BENeV:
    if: ${{ inputs.buildtype == 'obj' &&
            (inputs.enable_tests == 'all' ||
             inputs.enable_tests == 'BENeV') }}
    strategy:
      fail-fast: false
      matrix:
        os: [Linux, Windows]
        rawChannel: ['', rawChannel]
        exclude:
          - os: Linux
            rawChannel: rawChannel
    runs-on:
      - ${{ matrix.os }}
      - self-hosted
      - vdub-benev
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Execute BENeV ( ${{ runner.os }} )
        uses: ./.github/actions/testframework/BENeV
        with:
          artifactoryApiToken: ${{ secrets.ARTIFACTORY_GITHUB_DELIVERABLES_API_TOKEN }}
          artifactoryBaseUrl: ${{ vars.ARTIFACTORY_GITHUB_DELIVERABLES_URL }}
          artifactoryReadToken: ${{ secrets.ORG_OMNISSA_ART_READONLY_TOKEN }}
          enableRawChannel: ${{ matrix.rawChannel }}
          useOpenCppCoverage: true
          useGcov: true
          reportDbUri: ${{ secrets.VDUB_REPORT_DB_W }}
          reportDbName: ${{ secrets.VDUB_REPORT_DB_NAME }}
          reportDbCollection: ${{ secrets.VDUB_REPORT_COL_NAME }}
          runNumber: $RUNNUMBER
          runAttempt: $RUNATTEMPT
          buildtype: ${{ inputs.buildtype }}

  BENeV-n-1:
    #if: ${{ inputs.buildtype == 'obj' &&
    #        (inputs.enable_tests == 'all' ||
    #         inputs.enable_tests == 'BENeV') &&
    #        github.event_name != 'pull_request' }}
    if: false
    strategy:
      fail-fast: false
      matrix:
        os: [Linux, Windows]
        topology: [stableAgent, stableClient]
    runs-on:
      - ${{ matrix.os }}
      - self-hosted
      - vdub-benev
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Execute BENeV ( ${{ runner.os }} )
        uses: ./.github/actions/testframework/BENeV
        with:
          artifactoryApiToken: ${{ secrets.ARTIFACTORY_GITHUB_DELIVERABLES_API_TOKEN }}
          artifactoryBaseUrl: ${{ vars.ARTIFACTORY_GITHUB_DELIVERABLES_URL }}
          artifactoryReadToken: ${{ secrets.ORG_OMNISSA_ART_READONLY_TOKEN }}
          topology: ${{ matrix.topology }}
          # We specifically don't want to collect coverage data when testing
          # with n-1 builds because coverage data may be mapped to the wrong
          # lines of code
          useOpenCppCoverage: false
          useGcov: false
          reportDbUri: ${{ secrets.VDUB_REPORT_DB_W }}
          reportDbName: ${{ secrets.VDUB_REPORT_DB_NAME }}
          reportDbCollection: ${{ secrets.VDUB_REPORT_COL_NAME }}
          runNumber: $RUNNUMBER
          runAttempt: $RUNATTEMPT
          buildtype: ${{ inputs.buildtype }}

  BitB:
    if: ${{ inputs.buildtype == 'obj' &&
            (inputs.enable_tests == 'all' ||
             inputs.enable_tests == 'BitB') }}
    strategy:
      fail-fast: false
      matrix:
        protocol: [BEAT, TCP]
        encoder: [ADAPTIVE, BLAST_CODEC, H264, HEVC]
        label: [Non-vGPU, vGPU]
        clientType: [Win]
        exclude:
          - encoder: HEVC
            label: Non-vGPU
        include:
          - clienttype: Web
            label: Non-vGPU
          - clienttype: Web
            label: vGPU
    runs-on:
      - self-hosted
      - windows
      - vdub-bitb
      - ${{ matrix.label }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          sparse-checkout: .github

      - name: Run BitB
        uses: ./.github/actions/testframework/BitB
        with:
          protocol: ${{ matrix.protocol }}
          encoder: ${{ matrix.encoder }}
          clienttype: Mock${{ matrix.clientType }}Client
          artifactoryApiToken: ${{ secrets.ARTIFACTORY_GITHUB_DELIVERABLES_API_TOKEN }}
          artifactoryBaseUrl: ${{ vars.ARTIFACTORY_GITHUB_DELIVERABLES_URL }}
          reportDbUri: ${{ secrets.VDUB_REPORT_DB_W }}
          reportDbName: ${{ secrets.VDUB_REPORT_DB_NAME }}
          reportDbCollection: ${{ secrets.VDUB_REPORT_COL_NAME }}
          runNumber: $RUNNUMBER
          runAttempt: $RUNATTEMPT
          buildtype: ${{ inputs.buildtype }}

  Blast:
    if: ${{ inputs.enable_tests == 'all' ||
            inputs.enable_tests == 'Blast' }}
    strategy:
      matrix:
        os: [Windows, Linux]
    runs-on:
      - self-hosted
      - ${{ matrix.os }}
      - vdub-ut
    steps:
      # checkout is needed to get the Blast-UT action
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run Blast-UT
        uses: ./.github/actions/testframework/Blast-UT
        with:
          artifactoryApiToken: ${{ secrets.ARTIFACTORY_GITHUB_DELIVERABLES_API_TOKEN }}
          artifactoryBaseUrl: ${{ vars.ARTIFACTORY_GITHUB_DELIVERABLES_URL }}
          useOpenCppCoverage: true
          codecovInclusions: ${{ env.CODECOV_WIN_INCLUSIONS }}
          useGcov: true
          reportDbUri: ${{ secrets.VDUB_REPORT_DB_W }}
          reportDbName: ${{ secrets.VDUB_REPORT_DB_NAME }}
          reportDbCollection: ${{ secrets.VDUB_REPORT_COL_NAME }}
          runNumber: $RUNNUMBER
          runAttempt: $RUNATTEMPT
          buildtype: ${{ inputs.buildtype }}

  Display:
    if: ${{ inputs.enable_tests == 'all' ||
            inputs.enable_tests == 'Display' }}
    strategy:
      fail-fast: false
      matrix:
        os: [win10, win11]
        type: [vgpu, non-vgpu]
        driverName: [hznvidd64.msi, hznvidd2x64.msi, '']
        exclude:
          - os: win11
            type: vgpu
          - os: win11
            type: non-vgpu
    runs-on:
      - self-hosted
      - ${{ matrix.os }}
      - ${{ matrix.type }}
      - vdub-display
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run Display tests
        uses: ./.github/actions/testframework/Display
        with:
          driverName: ${{ matrix.driverName }}
          artifactoryApiToken: ${{ secrets.ARTIFACTORY_GITHUB_DELIVERABLES_API_TOKEN }}
          artifactoryBaseUrl: ${{ vars.ARTIFACTORY_GITHUB_DELIVERABLES_URL }}
          useOpenCppCoverage: true
          codecovInclusions: ${{ env.CODECOV_WIN_INCLUSIONS }}
          reportDbUri: ${{ secrets.VDUB_REPORT_DB_W }}
          reportDbName: ${{ secrets.VDUB_REPORT_DB_NAME }}
          reportDbCollection: ${{ secrets.VDUB_REPORT_COL_NAME }}
          runNumber: $RUNNUMBER
          runAttempt: $RUNATTEMPT
          buildtype: ${{ inputs.buildtype }}

  Networking:
    if: ${{ inputs.enable_tests == 'all' ||
            inputs.enable_tests == 'Networking' }}
    strategy:
      matrix:
        os: [Windows, Linux]
    runs-on:
      - self-hosted
      - ${{ matrix.os }}
      - vdub-ut
    steps:
      # checkout is needed to get the UT-Networking action
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run UT-Networking
        uses: ./.github/actions/testframework/UT-Networking
        with:
          artifactoryApiToken: ${{ secrets.ARTIFACTORY_GITHUB_DELIVERABLES_API_TOKEN }}
          artifactoryBaseUrl: ${{ vars.ARTIFACTORY_GITHUB_DELIVERABLES_URL }}
          useOpenCppCoverage: true
          codecovInclusions: ${{ env.CODECOV_WIN_INCLUSIONS }}
          useGcov: true
          reportDbUri: ${{ secrets.VDUB_REPORT_DB_W }}
          reportDbName: ${{ secrets.VDUB_REPORT_DB_NAME }}
          reportDbCollection: ${{ secrets.VDUB_REPORT_COL_NAME }}
          runNumber: $RUNNUMBER
          runAttempt: $RUNATTEMPT
          buildtype: ${{ inputs.buildtype }}

  VmwAudio:
    if: ${{ inputs.enable_tests == 'all' ||
            inputs.enable_tests == 'VmwAudio' }}
    runs-on:
      - self-hosted
      - windows
      - vdub-blast-audio
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run VmwAudio tests
        uses: ./.github/actions/testframework/VmwAudio
        with:
          artifactoryApiToken: ${{ secrets.ARTIFACTORY_GITHUB_DELIVERABLES_API_TOKEN }}
          artifactoryBaseUrl: ${{ vars.ARTIFACTORY_GITHUB_DELIVERABLES_URL }}
          useOpenCppCoverage: true
          codecovInclusions: ${{ env.CODECOV_WIN_INCLUSIONS }}
          reportDbUri: ${{ secrets.VDUB_REPORT_DB_W }}
          reportDbName: ${{ secrets.VDUB_REPORT_DB_NAME }}
          reportDbCollection: ${{ secrets.VDUB_REPORT_COL_NAME }}
          runNumber: $RUNNUMBER
          runAttempt: $RUNATTEMPT
          buildtype: ${{ inputs.buildtype }}

  VmwVideo:
    if: ${{ inputs.buildtype == 'obj' &&
            (inputs.enable_tests == 'all' ||
             inputs.enable_tests == 'VmwVideo') }}
    strategy:
      fail-fast: false
      matrix:
        encoder: [BLAST_CODEC, H264, HEVC]
        driverName: [hznvidd64.msi, hznvidd2x64.msi]
        label: [vgpu, non-vgpu]
        exclude:
          - encoder: HEVC
            label: non-vgpu
    runs-on:
      - self-hosted
      - windows
      - vdub-blast-video
      - ${{ matrix.label }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          sparse-checkout: .github

      - name: Run BitB
        uses: ./.github/actions/testframework/BitB
        with:
          encoder: ${{ matrix.encoder }}
          driverName: ${{ matrix.driverName }}
          artifactoryApiToken: ${{ secrets.ARTIFACTORY_GITHUB_DELIVERABLES_API_TOKEN }}
          artifactoryBaseUrl: ${{ vars.ARTIFACTORY_GITHUB_DELIVERABLES_URL }}
          reportDbUri: ${{ secrets.VDUB_REPORT_DB_W }}
          reportDbName: ${{ secrets.VDUB_REPORT_DB_NAME }}
          reportDbCollection: ${{ secrets.VDUB_REPORT_COL_NAME }}
          runNumber: $RUNNUMBER
          runAttempt: $RUNATTEMPT
          buildtype: ${{ inputs.buildtype }}

  VncRegEncPerfTest:
    if: ${{ inputs.enable_tests == 'all' ||
            inputs.enable_tests == 'VncRegEncPerfTest' }}
    strategy:
      fail-fast: false
      matrix:
        type: [nvidia, Linux, intel, amd]
    runs-on:
      - self-hosted
      - ${{ matrix.type }}
      - vdub-vnc-perf
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run VncRegEncPerfTest tests
        uses: ./.github/actions/testframework/VncRegEncPerfTest
        with:
          artifactoryApiToken: ${{ secrets.ARTIFACTORY_GITHUB_DELIVERABLES_API_TOKEN }}
          artifactoryBaseUrl: ${{ vars.ARTIFACTORY_GITHUB_DELIVERABLES_URL }}
          useOpenCppCoverage: true
          useGcov: true
          reportDbUri: ${{ secrets.VDUB_REPORT_DB_W }}
          reportDbName: ${{ secrets.VDUB_REPORT_DB_NAME }}
          reportDbCollection: ${{ secrets.VDUB_REPORT_COL_NAME }}
          runNumber: $RUNNUMBER
          runAttempt: $RUNATTEMPT
          buildtype: ${{ inputs.buildtype }}

  VvcSessionManager:
    if: ${{ inputs.enable_tests == 'all' ||
            inputs.enable_tests == 'VvcSessionManager' }}
    strategy:
      fail-fast: false
      matrix:
        os: [Windows, Linux]
    runs-on: [cart-bj, ut, self-hosted, '${{ matrix.os }}']
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Run Case
        uses: ./.github/actions/testframework/runcase
        with:
          productWorkflow: appblastlibs
          runNumber: $RUNNUMBER
          runAttempt: $RUNATTEMPT
          productWorkflowJob: build-appblastlibs-${{ runner.os == 'Windows' && 'winhzn-gh' || 'lnxbuild-gh' }}
          artifactoryBaseUrl: ${{ vars.ARTIFACTORY_GITHUB_DELIVERABLES_URL }}
          artifactoryApiToken: ${{ secrets.ARTIFACTORY_GITHUB_DELIVERABLES_API_TOKEN }}
          reportDbUri: ${{ secrets.VDUB_REPORT_DB_W }}
          reportDbName: ${{ secrets.VDUB_REPORT_DB_NAME }}
          reportDbCollection: ${{ secrets.VDUB_REPORT_COL_NAME }}
          testFilePaths: test/x64/ut/*
          deployCommand: chmod 777 test/x64/ut/testVvcSessionManager
          testCommand: >
            ${{ runner.os == 'Linux' && 'export LD_LIBRARY_PATH=test/x64/ut/;' || '' }}
            test/x64/ut/testVvcSessionManager${{ runner.os == 'Windows' && '.exe' || '' }}
            --gtest_output=xml:unitTest.xml
          buildtype: ${{ inputs.buildtype }}
          pdbPath: test/x64/ut/testVvcSessionManager.pdb
          useGcov: true
