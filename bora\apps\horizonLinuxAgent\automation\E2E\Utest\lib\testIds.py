import lib.kits,lib.auto
from itertools import chain

auto = lib.auto

TestCaseIDs = {
        'features':
        {
            'cdr': [40345,40346,40347,40348,40349,40383,40384,40385],
            'clipboard':[40401,40407,40408,40409],
            'usb':[41444,41450,41449,41448],
            'truesso':[41370,41371,41372,41373,41378],
            'sso':[41167,41168,41174],
            'printer':[41050,41054,41069,41073],
            'multiplesession':[]
        },
        'scenarios':{
            'udp':[40287,40301],
            'tcp':[40272,40282],
            'disConReconnect':[41460],
            'rdsinstall':[40921],
            'sessionstolen':[41303],
            'repeatConnectionH264':[41152],
            'installation':[40601,40603],
            'logoff':[41461],
            'autofit':[40225],
            'PendingConnection':[],
            'InvalidConnection':[],
            'BrokerTimeout':[],
            'ReconnectProvisionedVM':[],
            'Multimon':[],
            'Multimon4K':[],
            'HelpdeskWorking':[],
            'HelpdeskBlast':[],
            'HelpdeskProcess' : [],
            'HelpdeskHistory' : [],
            'HelpdeskDisconnect' : [],
            'HelpdeskReconnect' : [],
            'DEMSmartPolicy' : [],
            'multisession':[41138,41510,41511,41512,41513],
            'multiapps':[41514,],
            'vadcinstall':[41528,41529,41530]
        }
	}

def assignTestCaseIds(features,scenarios):
	# declare scenarios in your testcases and the Ids will be picked up from 
    # here, if creating a new scenario add it in TestCaseIds
    global tclist
    tclist = [TestCaseIDs['features'][x] for x in features]
    tclist = list(chain(*tclist))
    scenarioList = [TestCaseIDs['scenarios'][x] for x in scenarios]
    tclist.extend(list(chain(*scenarioList)))
    
def getTestCaseList():
    return tclist