﻿<?xml version="1.0" encoding="utf-8"?>

<WixLocalization Culture="ko-kr" Codepage="949" xmlns="http://schemas.microsoft.com/wix/2006/localization">
   <String Id="LANGID">1042</String>

   <!-- Installshield Strings -->
   <String Id="IDS_COMPLUS_PROGRESSTEXT_COST">COM+ 애플리케이션 공간을 계산하는 중: [1]</String>
   <String Id="IDS_COMPLUS_PROGRESSTEXT_INSTALL">COM+ 애플리케이션을 설치하는 중: [1]</String>
   <String Id="IDS_COMPLUS_PROGRESSTEXT_UNINSTALL">COM+ 애플리케이션을 제거하는 중: [1]</String>
   <String Id="IDS_PROGMSG_IIS_CREATEAPPPOOL">애플리케이션 풀 %s을(를) 생성하는 중</String>
   <String Id="IDS_PROGMSG_IIS_CREATEAPPPOOLS">애플리케이션 풀을 생성하는 중...</String>
   <String Id="IDS_PROGMSG_IIS_CREATEVROOT">IIS 가상 디렉토리 %s을(를) 생성하는 중</String>
   <String Id="IDS_PROGMSG_IIS_CREATEVROOTS">IIS 가상 디렉토리를 생성하는 중...</String>
   <String Id="IDS_PROGMSG_IIS_CREATEWEBSERVICEEXTENSION">웹 서비스 확장을 생성하는 중</String>
   <String Id="IDS_PROGMSG_IIS_CREATEWEBSERVICEEXTENSIONS">웹 서비스 확장을 생성하는 중...</String>
   <String Id="IDS_PROGMSG_IIS_EXTRACT">IIS 가상 디렉토리에 대한 정보를 추출하는 중...</String>
   <String Id="IDS_PROGMSG_IIS_EXTRACTDONE">IIS 가상 디렉토리에 대한 정보 추출 완료...</String>
   <String Id="IDS_PROGMSG_IIS_REMOVEAPPPOOL">애플리케이션 풀을 제거하는 중</String>
   <String Id="IDS_PROGMSG_IIS_REMOVEAPPPOOLS">애플리케이션 풀을 제거하는 중...</String>
   <String Id="IDS_PROGMSG_IIS_REMOVESITE">%d 포트에서 웹 사이트를 제거하는 중</String>
   <String Id="IDS_PROGMSG_IIS_REMOVEVROOT">IIS 가상 디렉토리 %s을(를) 제거하는 중</String>
   <String Id="IDS_PROGMSG_IIS_REMOVEVROOTS">IIS 가상 디렉토리를 제거하는 중...</String>
   <String Id="IDS_PROGMSG_IIS_REMOVEWEBSERVICEEXTENSION">웹 서비스 확장을 제거하는 중</String>
   <String Id="IDS_PROGMSG_IIS_REMOVEWEBSERVICEEXTENSIONS">웹 서비스 확장을 제거하는 중...</String>
   <String Id="IDS_PROGMSG_IIS_ROLLBACKAPPPOOLS">애플리케이션 풀을 롤백하는 중...</String>
   <String Id="IDS_PROGMSG_IIS_ROLLBACKVROOTS">가상 디렉토리와 웹 사이트의 변경 내용을 롤백하는 중...</String>
   <String Id="IDS_PROGMSG_IIS_ROLLBACKWEBSERVICEEXTENSIONS">웹 서비스 확장을 롤백하는 중...</String>
   <String Id="IDS_PROGMSG_XML_COSTING">XML 파일 공간을 계산하는 중...</String>
   <String Id="IDS_PROGMSG_XML_CREATE_FILE">XML 파일 %s을(를) 생성하는 중...</String>
   <String Id="IDS_PROGMSG_XML_FILES">XML 파일을 변경하는 중...</String>
   <String Id="IDS_PROGMSG_XML_REMOVE_FILE">XML 파일 %s을(를) 제거하는 중...</String>
   <String Id="IDS_PROGMSG_XML_ROLLBACK_FILES">XML 파일 변경 내용을 롤백하는 중...</String>
   <String Id="IDS_PROGMSG_XML_UPDATE_FILE">XML 파일 %s을(를) 업데이트하는 중...</String>


   <!-- LaunchCondition Error Messages -->
   <String Id="MINIMUM_REQUIRED_OS">이 제품은 Windows 10, Windows Server 2016 이상 OS에만 설치할 수 있습니다.</String>
   <String Id="DENY_INSTALL_DOMAIN_CONTROLLER">이 제품은 도메인 컨트롤러에 설치할 수 없습니다.</String>
   <String Id="NEED_ADMIN">이 소프트웨어를 설치/제거하려면 관리자 권한이 필요합니다.</String>


   <!-- Feature Table -->
   <String Id="FEATURE_NAME_CORE">Core</String>
   <String Id="FEATURE_DESC_CORE">[ProductName] 핵심 기능</String>
   <String Id="FEATURE_NAME_CORRETTO">Corretto</String>
   <String Id="FEATURE_DESC_CORRETTO">Corretto JDK 배포를 포함한 [ProductName] 핵심 기능</String>
   <String Id="FEATURE_NAME_BELLSOFT">Bellsoft</String>
   <String Id="FEATURE_DESC_BELLSOFT">Bellsoft JDK 배포를 포함한 [ProductName] 핵심 기능</String>
   <String Id="FEATURE_NAME_RDSH3D">3D RDSH</String>
   <String Id="FEATURE_DESC_RDSH3D">이 기능은 RDSH 및 물리적 PC 세션에서 하드웨어 3D 가속화를 사용하도록 설정합니다.</String>
   <String Id="FEATURE_NAME_CLIENTDRIVEREDIRECTION">클라이언트 드라이브 리디렉션</String>
   <String Id="FEATURE_DESC_CLIENTDRIVEREDIRECTION">Horizon Client에서 로컬 드라이브를 원격 데스크톱 및 애플리케이션과 공유하도록 허용합니다. 설치되어 있지 않으면 복사/붙여넣기 기능과 파일 및 폴더 끌어서 놓기 기능이 사용하지 않도록 설정됩니다.</String>
   <String Id="FEATURE_NAME_NGVC">Instant Clone Agent</String>
   <String Id="FEATURE_DESC_NGVC">Instant Clone Agent는 VMware vSphere 7.0 이상에서 실행되는 가상 시스템에서만 설치할 수 있습니다.</String>
   <String Id="FEATURE_DESC_PCOIP_PHYSICAL">이 기능은 PCoIP 서버 구성 요소를 데스크톱에 설치합니다.</String>
   <String Id="FEATURE_NAME_RTAV">실시간 오디오-비디오</String>
   <String Id="FEATURE_DESC_RTAV">실시간 오디오-비디오를 통해 사용자는 로컬로 연결된 오디오 및 비디오 주변 장치를 원격 데스크톱에 리디렉션하여 사용할 수 있습니다.</String>
   <String Id="FEATURE_NAME_VMWPRINT">Horizon Integrated Printing</String>
   <String Id="FEATURE_DESC_VMWPRINT">Horizon Integrated Printing 리디렉션입니다.</String>

   <String Id="FEATURE_NAME_SCANNERREDIRECTION">스캐너 리디렉션</String>
   <String Id="FEATURE_DESC_SCANNERREDIRECTION">스캐너 리디렉션 기능을 사용하도록 설정합니다.</String>
   <String Id="FEATURE_NAME_SERIALPORTREDIRECTION">직렬 포트 리디렉션</String>
   <String Id="FEATURE_DESC_SERIALPORTREDIRECTION">직렬 포트 리디렉션 기능을 사용하도록 설정합니다.</String>
   <String Id="FEATURE_NAME_SMARTCARD">스마트 카드 리디렉션</String>
   <String Id="FEATURE_DESC_SMARTCARD">스마트 카드 리디렉션 기능을 사용하도록 설정합니다.</String>
   <String Id="FEATURE_NAME_TSMMR">TSMMR</String>
   <String Id="FEATURE_DESC_TSMMR">터미널 서비스 멀티미디어 리디렉션.</String>
   <String Id="FEATURE_NAME_URLREDIRECTION">URL 컨텐츠 리디렉션</String>
   <String Id="FEATURE_DESC_URLREDIRECTION">서버 세션에서 클라이언트 디바이스로 또는 그 반대로 URL 컨텐츠를 리디렉션합니다.</String>
   <String Id="FEATURE_NAME_UNCREDIRECTION">UNC 경로 리디렉션</String>
   <String Id="FEATURE_DESC_UNCREDIRECTION">서버 세션에서 클라이언트 디바이스로 또는 그 반대로 UNC 경로를 리디렉션합니다.</String>
   <String Id="FEATURE_NAME_USB">USB 리디렉션</String>
   <String Id="FEATURE_DESC_USB">USB 리디렉션입니다. USB 리디렉션을 안전하게 사용하는 방법에 대한 지침을 보려면 Horizon 보안 문서를 참조하십시오.</String>
   <String Id="FEATURE_NAME_HZNVAUDIO">Horizon 오디오</String>
   <String Id="FEATURE_DESC_HZNVAUDIO">Horizon 가상 오디오 드라이버</String>
   <String Id="FEATURE_NAME_HTML5MMR">HTML5 멀티미디어 리디렉션</String>
   <String Id="FEATURE_DESC_HTML5MMR">HTML5 멀티미디어 리디렉션 사용</String>
   <String Id="FEATURE_NAME_GEOREDIR">지리적 위치 리디렉션</String>
   <String Id="FEATURE_DESC_GEOREDIR">원격 데스크톱에 대한 클라이언트의 지리적 위치 리디렉션 허용</String>
   <String Id="FEATURE_NAME_SDOSENSOR">SDO 센서 리디렉션</String>
   <String Id="FEATURE_DESC_SDOSENSOR">SDO(Simple Device Orientation) 센서 리디렉션 기능을 사용하도록 설정하고 디바이스 방향 변경 사항을 원격 데스크톱에 보고합니다.</String>
   <String Id="FEATURE_NAME_STORAGEDRIVE">스토리지 드라이브 리디렉션</String>
   <String Id="FEATURE_DESC_STORAGEDRIVE">원격 데스크톱에 대한 클라이언트의 스토리지 드라이브 리디렉션을 허용합니다.</String>
   <String Id="FEATURE_NAME_PERFTRACKER">Horizon Performance Tracker</String>
   <String Id="FEATURE_DESC_PERFTRACKER">Horizon Performance Tracker 사용</String>
   <String Id="FEATURE_NAME_HYBRIDLOGON">하이브리드 로그온</String>
   <String Id="FEATURE_DESC_HYBRIDLOGON">인증되지 않은 사용자가 자격 증명을 입력하지 않아도 네트워크 리소스에 액세스할 수 있도록 하는 하이브리드 로그온을 사용하도록 설정합니다.</String>
   <String Id="FEATURE_NAME_HELPDESK">Horizon Agent용 헬프 데스크 플러그인</String>
   <String Id="FEATURE_DESC_HELPDESK">Horizon Agent용 헬프 데스크 플러그인입니다.</String>

   <!-- Control Panel Strings -->
   <String Id="Url">https://www.omnissa.com/</String>

   <!-- Firewall Strings -->
   <String Id="BlastUDPFirewallExceptionName">Omnissa Horizon Blast UDP 트래픽 예외</String>

   <!-- UI Dialog Strings -->
   <String Id="IDS__DisplayName_Custom">사용자 지정</String>
   <String Id="IDS__DisplayName_Minimal">최소</String>
   <String Id="IDS__DisplayName_Typical">일반</String>
   <String Id="INTEL_UNS_DESC">Intel User Notification 서비스를 제공합니다.</String>
   <String Id="IDS_LicenseAcceptance">설치하면 다음에 동의하는 것으로 간주됨:</String>
   <String Id="IDS_GeneralTerms">일반 약관</String>
   <String Id="IDS_CANCEL">취소</String>
   <String Id="IDS_CANCEL2">취소(&amp;C)</String>
   <String Id="IDS_OK">확인</String>
   <String Id="IDS_BACK">&lt; 뒤로(&amp;B)</String>
   <String Id="IDS_NEXT">다음(&amp;N) &gt;</String>
   <String Id="IDS_FINISH">마침(&amp;F)</String>
   <String Id="IDS__IsCancelDlg_No">아니요(&amp;N)</String>
   <String Id="IDS__IsCancelDlg_Yes">예(&amp;Y)</String>
   <String Id="IDS__IsAdminInstallBrowse_LookIn">찾는 위치(&amp;L):</String>
   <String Id="IDS__IsAdminInstallBrowse_UpOneLevel">한 수준 위로</String>
   <String Id="IDS__IsAdminInstallBrowse_BrowseDestination">대상 폴더를 찾습니다.</String>
   <String Id="IDS__IsAdminInstallBrowse_ChangeDestination">{&amp;MSSansBold8}현재 대상 폴더 변경</String>
   <String Id="IDS__IsAdminInstallBrowse_CreateFolder">새 폴더 생성</String>
   <String Id="IDS__IsAdminInstallBrowse_FolderName">폴더 이름(&amp;F):</String>
   <String Id="IDS__IsAdminInstallPoint_Install">설치(&amp;I)</String>
   <String Id="IDS__IsAdminInstallPoint_SpecifyNetworkLocation">제품의 서버 이미지를 생성할 네트워크 위치를 지정하십시오.</String>
   <String Id="IDS__IsAdminInstallPoint_EnterNetworkLocation">네트워크 위치를 입력하거나 변경을 클릭하여 위치를 찾으십시오. 지정한 네트워크 위치에 [ProductName]의 서버 이미지를 생성하려면 설치를 클릭하고, 마법사를 끝내려면 취소를 클릭하십시오.</String>
   <String Id="IDS__IsAdminInstallPoint_NetworkLocationFormatted">{&amp;MSSansBold8}네트워크 위치</String>
   <String Id="IDS__IsAdminInstallPoint_NetworkLocation">네트워크 위치(&amp;N):</String>
   <String Id="IDS__IsAdminInstallPoint_Change">변경(&amp;C)...</String>
   <String Id="IDS__IsAdminInstallPointWelcome_Wizard">{&amp;TahomaBold10}[ProductName] 설치 관리자 시작</String>
   <String Id="IDS__IsAdminInstallPointWelcome_ServerImage">설치 관리자가 지정된 네트워크 위치에 [ProductName]의 서버 이미지를 생성합니다. 계속하려면 다음을 클릭하십시오.</String>
   <String Id="ProductVersion">{&amp;Arial9}제품 버전: [ProductVersionString]</String>
   <String Id="IDS__IsCancelDlg_ConfirmCancel">[ProductName] 설치를 취소하시겠습니까?</String>
   <String Id="IDS__IsInstallRolesConfirmDlg_Message">설치 관리자가 운영 체제에 필요한 역할을 설치합니다. 계속하려면 "확인"을 클릭하십시오.</String>
   <String Id="ConnectionServer_TitleDesc">이 시스템에서 연결할 Horizon Connection Server를 입력합니다.</String>
   <String Id="ConnectionServer_Title">{&amp;MSSansBold8}Horizon Connection Server에 등록</String>
   <String Id="ConnectionServer_Text">이 시스템을 Horizon Connection Server에 등록하려면 Horizon Connection Server(표준 또는 복제본 인스턴스)의 서버 이름과 관리자 로그인 자격 증명을 입력하십시오.</String>
   <String Id="ConnectionServer_ServerNote">(호스트 이름 또는 IP 주소)</String>
   <String Id="ConnectionServerLogin_Text1">현재 로그온한 사용자로 인증(&amp;A)</String>
   <String Id="ConnectionServerLogin_Text2">관리자 자격 증명 지정(&amp;C)</String>
   <String Id="ConnectionServerLogin_Title">인증:</String>
   <String Id="ConnectionServer_Username">사용자 이름(&amp;U):</String>
   <String Id="ConnectionServer_UsernameNote">(도메인\사용자)</String>
   <String Id="ConnectionServer_Password">암호(&amp;P):</String>
   <String Id="IDS__IsCustomSelectionDlg_SelectFeatures">설치하려는 프로그램 기능을 선택합니다.</String>
   <String Id="IDS__IsCustomSelectionDlg_ClickFeatureIcon">기능의 설치 방법을 변경하려면 아래 목록에 있는 아이콘을 클릭하십시오.</String>
   <String Id="IDS__IsCustomSelectionDlg_CustomSetup">{&amp;MSSansBold8}사용자 지정 설치</String>
   <String Id="IDS__IsCustomSelectionDlg_Change">변경(&amp;C)...</String>
   <String Id="IDS__IsCustomSelectionDlg_FeatureDescription">기능 설명</String>
   <String Id="IDS__IsCustomSelectionDlg_InstallTo">설치 위치:</String>
   <String Id="IDS__IsCustomSelectionDlg_MultilineDescription">현재 선택한 항목에 대한 설명</String>
   <String Id="IDS__IsCustomSelectionDlg_FeaturePath">&lt;선택된 기능 경로&gt;</String>
   <String Id="IDS__IsCustomSelectionDlg_FeatureSize">기능 크기</String>
   <String Id="IDS__IsCustomSelectionDlg_Help">도움말(&amp;H)</String>
   <String Id="IDS__IsCustomSelectionDlg_Space">공간(&amp;S)</String>
   <String Id="IDS_SetupTips_CustomSetupDescription">사용자 지정 설치에서는 프로그램 기능을 선택하여 설치할 수 있습니다.</String>
   <String Id="IDS_SetupTips_CustomSetup">{&amp;MSSansBold8}사용자 지정 설치 팁</String>
   <String Id="IDS_SetupTips_WillNotBeInstalled">설치되지 않습니다.</String>
   <String Id="IDS_SetupTips_Advertise">처음 사용할 때 설치됩니다. 해당 기능에서 이 옵션을 지원할 때만 사용 가능합니다.</String>
   <String Id="IDS_SetupTips_InstallState">다음은 기능의 설치 상태를 표시합니다.</String>
   <String Id="IDS_SetupTips_AllInstalledLocal">로컬 하드 드라이브에 모두 설치됩니다.</String>
   <String Id="IDS_SetupTips_IconInstallState">기능 이름 옆의 아이콘은 해당 기능의 설치 상태를 나타냅니다. 각 기능의 설치 상태 메뉴를 보려면 해당 아이콘을 클릭하십시오.</String>
   <String Id="IDS_SetupTips_Network">네트워크에서 실행되도록 설치합니다. 해당 기능에서 이 옵션을 지원할 때만 사용 가능합니다.</String>
   <String Id="IDS_SetupTips_SubFeaturesInstalledLocal">일부 하위 기능을 로컬 하드 드라이브에 설치합니다. 해당 기능에서 이 옵션을 지원할 때만 사용 가능합니다.</String>
   <String Id="DesktopConfig_Subtitle">다음은 Horizon 데스크톱 기능을 구성하는 데 사용되는 정보입니다.</String>
   <String Id="DesktopConfig_Title">{&amp;MSSansBold8}데스크톱 OS 구성</String>
   <String Id="DesktopConfig_Text">이 운영 체제에서 [ProductName]의 모드를 선택합니다.</String>
   <String Id="DesktopConfig_RDSHMode">필요한 RDSH(원격 데스크톱 세션 호스트) 역할이 이 운영 체제에 설치되어 있지 않습니다.

"다음"을 클릭하여 필요한 역할/기능을 설치합니다. 설치가 완료되면 운영 체제를 다시 시작해야 합니다.

다시 시작한 후에 RDS 모드에서 설치를 계속하려면 [ProductName] 설치 관리자를 다시 시작해야 합니다.</String>
   <String Id="DesktopConfig_DesktopMode">이 에이전트는 데스크톱 VDI 모드에서 구성됩니다.</String>
   <String Id="DesktopConfig_InstallingRolesSuccess">필요한 운영 체제 역할/기능을 설치했습니다.
운영 체제를 다시 시작하고 [ProductName] 설치 관리자를 다시 시작하십시오.</String>
   <String Id="DesktopConfig_InstallingRolesFail">오류: 설치 관리자가 필요한 운영 체제 역할/기능을 설치할 수 없습니다.</String>
   <String Id="IDS__IsDesktopConfigDlg_RDSMode">RDS 모드</String>
   <String Id="IDS__IsDesktopConfigDlg_DesktopMode">데스크톱 모드</String>
   <String Id="InstallRolesConfirm_InstallingRoles">이 시스템에 필요한 역할/기능을 구성하는 동안 잠시 기다려 주십시오.</String>
   <String Id="IDS__IsFeatureDetailsDlg_DiskSpaceRequirements">{&amp;MSSansBold8}디스크 공간 요구 사항</String>
   <String Id="IDS__IsFeatureDetailsDlg_SpaceRequired">선택한 기능의 설치에 필요한 디스크 공간입니다.</String>
   <String Id="IDS__IsFeatureDetailsDlg_VolumesTooSmall">선택된 볼륨에는 현재 선택한 기능을 설치할 만큼 충분한 디스크 공간이 없습니다. 해당 볼륨에서 파일을 제거하거나 로컬 드라이브에 기능을 적게 설치하거나 다른 대상 드라이브를 선택할 수 있습니다.</String>
   <String Id="IDS__IsFilesInUse_Retry">재시도(&amp;R)</String>
   <String Id="IDS__IsFilesInUse_Ignore">무시(&amp;I)</String>
   <String Id="IDS__IsFilesInUse_Exit">종료(&amp;E)</String>
   <String Id="IDS__IsFilesInUse_FilesInUse">{&amp;MSSansBold8}사용 중인 파일</String>
   <String Id="IDS__IsFilesInUse_FilesInUseMessage">업데이트해야 할 파일 중 일부를 사용하고 있습니다.</String>
   <String Id="IDS__IsFilesInUse_ApplicationsUsingFiles">다음 애플리케이션에서는 이 설치 프로그램이 업데이트해야 하는 파일을 사용하고 있습니다. 이러한 애플리케이션을 닫고 "재시도"를 클릭하여 계속합니다.

참고: 다음 목록에서 [ProductName]을(를) 보려면 "무시"를 클릭하여 계속하십시오.</String>
   <String Id="IDS__IsBrowseFolderDlg_LookIn">찾는 위치(&amp;L):</String>
   <String Id="IDS__IsBrowseFolderDlg_UpOneLevel">한 수준 위로</String>
   <String Id="IDS__IsBrowseFolderDlg_BrowseDestFolder">대상 폴더를 찾습니다.</String>
   <String Id="IDS__IsBrowseFolderDlg_ChangeCurrentFolder">{&amp;MSSansBold8}현재 대상 폴더 변경</String>
   <String Id="IDS__IsBrowseFolderDlg_CreateFolder">새 폴더 생성</String>
   <String Id="IDS__IsBrowseFolderDlg_FolderName">폴더 이름(&amp;F):</String>
   <String Id="IDS__IsWelcomeDlg_WelcomeProductName">{&amp;TahomaBold10}[ProductName] 설치 마법사 시작</String>
   <String Id="IDS__IsWelcomeDlg_InstallProductName">설치 마법사가 [ProductName]을(를) 컴퓨터에 설치합니다. 계속하려면 다음을 클릭하십시오.</String>
   <String Id="InstallWelcome_UpgradeLine1">설치 마법사가 컴퓨터에서 [ProductName]을(를) 업그레이드합니다. 계속하려면 다음을 클릭하십시오.</String>
   <String Id="IDS__IsWelcomeDlg_WarningCopyright">Copyright (C) [CopyrightYears] Omnissa. All rights reserved. 이 제품은 미국 및 다른 나라들의 저작권과 지적재산권법, 그리고 국제조약에 의해 보호를 받습니다. "Omnissa"는 Omnissa, LLC, Omnissa International Unlimited Company 및/또는 해당 자회사를 나타냅니다.</String>
   <String Id="IpProtocolConfig_DlgDesc">통신 프로토콜 선택</String>
   <String Id="IpProtocolConfig_DlgTitle">{&amp;MSSansBold8}네트워크 프로토콜 구성</String>
   <String Id="GoldenImage_DlgDesc">Select whether this machine will be used as a Golden Image</String>
   <String Id="GoldenImage_DlgTitle">{&amp;MSSansBold8}Golden Image Selection</String>
   <String Id="GoldenImage_CheckBoxText">This machine will be used as a Golden Image</String>
   <String Id="ConnectionServer_IpText">이 Horizon Agent 인스턴스를 구성하는 데 사용할 프로토콜 지정:</String>
   <String Id="ConnectionServer_IPv4Desc">이 에이전트는 모든 연결의 설정에 IPv4 프로토콜을 선택하도록 구성됩니다.</String>
   <String Id="ConnectionServer_IPv6Desc">이 에이전트는 모든 연결의 설정에 IPv6 프로토콜을 선택하도록 구성됩니다.</String>
   <String Id="ConnectionServer_Dual4Desc">이 에이전트는 모든 연결을 설정하기 위해 IPv4 프로토콜을 기본으로 사용하는 혼합 IP 모드를 지원하도록 구성됩니다.</String>
   <String Id="ConnectionServer_Dual6Desc">이 에이전트는 모든 연결을 설정하기 위해 IPv6 프로토콜을 기본으로 사용하는 혼합 IP 모드를 지원하도록 구성됩니다.</String>
   <String Id="IpProtocolConfig_FipsText">FIPS 규격 암호화를 사용하여 이 제품을 설치할지 지정하십시오.</String>
   <String Id="IpProtocolConfig_FipsDisabledDesc">이 에이전트 인스턴스는 FIPS 규격 없이 작동합니다.</String>
   <String Id="IpProtocolConfig_FipsEnabledDesc">이 에이전트 인스턴스는 FIPS 규격 암호화 대상으로 구성됩니다.</String>
   <String Id="FipsConfig_Disabled">사용 안 함</String>
   <String Id="FipsConfig_Enabled">사용</String>
   <String Id="IDS__AgreeToLicense_0">일반 약관에 동의하지 않음(&amp;D)</String>
   <String Id="IDS__AgreeToLicense_1">일반 약관에 동의함(&amp;A)</String>
   <String Id="IDS__IsLicenseDlg_ReadLicenseAgreement">다음 일반 약관을 자세히 읽어 주십시오.</String>
   <String Id="IDS__IsLicenseDlg_LicenseAgreement">{&amp;MSSansBold8}일반 약관</String>
   <String Id="IDS__IsMaintenanceDlg_Modify">{&amp;MSSansBold8}수정(&amp;M)</String>
   <String Id="IDS__IsMaintenanceDlg_Repair">{&amp;MSSansBold8}복구(&amp;P)</String>
   <String Id="IDS__IsMaintenanceDlg_Remove">{&amp;MSSansBold8}제거(&amp;R)</String>
   <String Id="IDS__IsMaintenanceDlg_MaitenanceOptions">프로그램을 수정, 복구 또는 제거합니다.</String>
   <String Id="IDS__IsMaintenanceDlg_ProgramMaintenance">{&amp;MSSansBold8}프로그램 유지 보수</String>
   <String Id="IDS__IsMaintenanceDlg_ModifyMessage">사용자가 설치된 기능을 변경할 수 있습니다.</String>
   <String Id="IDS__IsMaintenanceDlg_RepairMessage">프로그램 설치 오류를 복구합니다. 이 옵션은 없거나 손상된 파일, 바로 가기 및 레지스트리 항목을 고칩니다.</String>
   <String Id="IDS__IsMaintenanceDlg_RemoveProductName">컴퓨터에서 [ProductName]을(를) 제거합니다.</String>
   <String Id="IDS__IsMaintenanceWelcome_WizardWelcome">{&amp;TahomaBold10}[ProductName] 설치 관리자 시작</String>
   <String Id="IDS__IsMaintenanceWelcome_MaintenanceOptionsDescription">설치 관리자를 사용하여 [ProductName]을(를) 수정, 복구 또는 제거할 수 있습니다. 계속하려면 다음을 클릭하십시오.</String>
   <String Id="IDS_PRODUCTNAME_INSTALLSHIELD">[ProductName] - 설치 마법사</String>
   <String Id="IDS__IsMsiRMFilesInUse_CloseRestart">자동으로 애플리케이션을 닫고 다시 시작합니다.</String>
   <String Id="IDS__IsMsiRMFilesInUse_RebootAfter">애플리케이션을 닫지 마십시오. 다시 부팅해야 합니다.</String>
   <String Id="IDS__IsMsiRMFilesInUse_ApplicationsUsingFiles">다음 애플리케이션이 이 설치 프로그램이 업데이트해야 할 파일을 사용하고 있습니다.</String>
   <String Id="IDS__IsDiskSpaceDlg_OutOfDiskSpace">{&amp;MSSansBold8}디스크 공간 부족</String>
   <String Id="IDS__IsDiskSpaceDlg_DiskSpace">설치에 필요한 디스크 공간이 사용 가능한 디스크 공간을 초과합니다.</String>
   <String Id="IDS__IsDiskSpaceDlg_HighlightedVolumes">선택된 볼륨에는 현재 선택한 기능을 설치할 만큼 충분한 디스크 공간이 없습니다. 해당 볼륨에서 파일을 제거하거나 설치를 취소할 수 있습니다.</String>
   <String Id="RdpChoice_EnableRdp">이 컴퓨터에서 원격 데스크톱 기능 사용(&amp;E)</String>
   <String Id="RdpChoice_NoRdp">이 컴퓨터에서 원격 데스크톱 기능 사용 안 함(&amp;D)</String>
   <String Id="RdpConfig_Subtitle">다음은 원격 데스크톱 기능을 구성하는 데 사용되는 정보입니다.</String>
   <String Id="RdpConfig_Title">{&amp;MSSansBold8}원격 데스크톱 프로토콜 구성</String>
   <String Id="RdpConfig_Text">원격 데스크톱 지원을 설정하려면 [ProductName]이(가) 필요합니다. RDP 포트 [RDP_PORT_NUMBER] 및 View Framework 채널 [FRAMEWORK_CHANNEL_PORT]에 대한 방화벽 예외가 추가됩니다. 어떻게 하시겠습니까?</String>
   <String Id="IDS__IsVerifyReadyDlg_Install">설치(&amp;I)</String>
   <String Id="IDS__IsVerifyReadyDlg_WizardReady">설치할 준비가 되었습니다.</String>
   <String Id="ReadyToInstall_RdshNote">참고: 이 OS에서 RDS 역할을 사용할 수 없습니다. [ProductName]에서는 단일 데스크톱 연결만 지원합니다.</String>
   <String Id="IDS__IsVerifyReadyDlg_ClickInstall">설치를 시작하려면 "설치"를 클릭하고 마법사를 끝내려면 "취소"를 클릭하십시오.</String>
   <String Id="IDS__IsVerifyReadyDlg_ReadyRepair">{&amp;MSSansBold8}프로그램 복구 준비 완료</String>
   <String Id="IDS__IsVerifyReadyDlg_ReadyInstall">{&amp;MSSansBold8}프로그램 설치 준비 완료</String>
   <String Id="ReadyToInstall_InstallDir">[ProductName] 설치 위치:

[INSTALLDIR]</String>
   <String Id="ReadyToInstall_MsgSanPolicy_NGVC">참고: Instant Clone Agent(NGVC) 기능의 요구 사항에 따라 VDS SAN 정책이 "모두 온라인"으로 설정됩니다.</String>
   <String Id="IDS__IsVerifyRemoveAllDlg_Remove">제거(&amp;R)</String>
   <String Id="IDS__IsVerifyRemoveAllDlg_ChoseRemoveProgram">시스템에서 프로그램을 제거하도록 선택했습니다.</String>
   <String Id="IDS__IsVerifyRemoveAllDlg_ClickRemove">컴퓨터에서 [ProductName]을(를) 제거하려면 제거를 클릭하십시오. 제거한 후에는 이 프로그램을 더 이상 사용할 수 없습니다.</String>
   <String Id="IDS__IsVerifyRemoveAllDlg_ClickBack">설정을 검토하거나 변경하려면 뒤로를 클릭하십시오.</String>
   <String Id="IDS__IsVerifyRemoveAllDlg_RemoveProgram">{&amp;MSSansBold8}프로그램 제거</String>
   <String Id="IDS__IsFatalError_NotModified">시스템이 수정되지 않았습니다. 나중에 설치를 완료하려면 설치 프로그램을 다시 실행하십시오.</String>
   <String Id="IDS__IsFatalError_ClickFinish">마법사를 끝내려면 마침을 클릭하십시오.</String>
   <String Id="IDS__IsFatalError_KeepOrRestore">나중에 설치를 계속하도록 시스템에 설치된 기존 요소를 유지하거나 설치 이전 상태로 시스템을 복원할 수 있습니다.</String>
   <String Id="IDS__IsFatalError_RestoreOrContinueLater">마법사를 끝내려면 복원이나 나중에 계속을 클릭하십시오.</String>
   <String Id="IDS__IsFatalError_WizardCompleted">{&amp;TahomaBold10}설치 관리자 완료</String>
   <String Id="IDS__IsFatalError_WizardInterrupted">마법사가 중단되어 [ProductName] 설치를 완료할 수 없습니다.</String>
   <String Id="IDS__IsFatalError_UninstallWizardInterrupted">마법사가 중단되어 [ProductName] 제거를 완료할 수 없습니다.</String>
   <String Id="IDS__IsExitDialog_WizardCompleted">{&amp;TahomaBold10}설치 관리자 완료</String>
   <String Id="IDS__IsExitDialog_InstallSuccess">[ProductName]을(를) 성공적으로 설치했습니다. 마법사를 끝내려면 마침을 클릭하십시오.</String>
   <String Id="IDS__IsExitDialog_UninstallSuccess">[ProductName]을(를) 성공적으로 제거했습니다. 마법사를 끝내려면 마침을 클릭하십시오.</String>
   <String Id="IDS__IsExitDialog_InstallingRolesSuccess">설치 관리자가 RDS 모드에서 [ProductName]을(를) 설치하는 데 필요한 역할/기능을 사용하여 운영 체제를 구성했습니다.

마법사를 종료하려면 "마침"을 클릭합니다.</String>
   <String Id="IDS__IsErrorDlg_InstallerInfo">[ProductName] 설치 관리자 정보</String>
   <String Id="IDS__IsErrorDlg_Abort">중단(&amp;A)</String>
   <String Id="IDS__IsErrorDlg_Yes">예(&amp;Y)</String>
   <String Id="IDS__IsErrorDlg_No">아니요(&amp;N)</String>
   <String Id="IDS__IsErrorDlg_Ignore">무시(&amp;I)</String>
   <String Id="IDS__IsErrorDlg_OK">확인(&amp;O)</String>
   <String Id="IDS__IsErrorDlg_Retry">재시도(&amp;R)</String>
   <String Id="IDS__IsInitDlg_WelcomeWizard">{&amp;TahomaBold10}[ProductName] 설치 관리자 시작</String>
   <String Id="IDS__IsInitDlg_PreparingWizard">[ProductName] 프로그램 설치 과정을 안내하는 설치 관리자를 준비하고 있습니다. 잠시 기다려 주십시오.</String>
   <String Id="IDS__IsUserExit_NotModified">시스템이 수정되지 않았습니다. 나중에 이 프로그램을 설치하려면 설치 프로그램을 다시 실행하십시오.</String>
   <String Id="IDS__IsUserExit_ClickFinish">마법사를 끝내려면 마침을 클릭하십시오.</String>
   <String Id="IDS__IsUserExit_KeepOrRestore">나중에 설치를 계속하도록 시스템에 설치된 기존 요소를 유지하거나 설치 이전 상태로 시스템을 복원할 수 있습니다.</String>
   <String Id="IDS__IsUserExit_RestoreOrContinue">마법사를 끝내려면 복원이나 나중에 계속을 클릭하십시오.</String>
   <String Id="IDS__IsUserExit_WizardCompleted">{&amp;TahomaBold10}설치 관리자 완료</String>
   <String Id="IDS__IsUserExit_WizardInterrupted">마법사가 중단되어 [ProductName] 설치를 완료할 수 없습니다.</String>
   <String Id="IDS__IsUserExit_UninstallWizardInterrupted">마법사가 중단되어 [ProductName] 제거를 완료할 수 없습니다.</String>
   <String Id="IDS__IsProgressDlg_UninstallingFeatures2">선택한 프로그램 기능을 설치하고 있습니다.</String>
   <String Id="IDS__IsProgressDlg_UninstallingFeatures">선택한 프로그램 기능을 제거하고 있습니다.</String>
   <String Id="IDS__IsProgressDlg_WaitUninstall2">설치 관리자가 [ProductName]을(를) 설치하는 동안 잠시 기다려 주십시오. 이 작업은 몇 분 정도 걸릴 수 있습니다.</String>
   <String Id="IDS__IsProgressDlg_WaitUninstall">설치 관리자가 [ProductName]을(를) 제거하는 동안 잠시 기다려 주십시오. 이 작업은 몇 분 정도 걸릴 수 있습니다.</String>
   <String Id="IDS__IsProgressDlg_InstallingProductName">{&amp;MSSansBold8}[ProductName] 설치</String>
   <String Id="IDS__IsProgressDlg_Uninstalling">{&amp;MSSansBold8}[ProductName] 제거</String>
   <String Id="IDS__IsProgressDlg_SecHidden">(지금은 숨김)초</String>
   <String Id="IDS__IsProgressDlg_Status">상태:</String>
   <String Id="IDS__IsProgressDlg_Hidden">(지금은 숨김)</String>
   <String Id="IDS__IsProgressDlg_HiddenTimeRemaining">(지금은 숨김)남은 예상 시간:</String>
   <String Id="IDS__IsProgressDlg_ProgressDone">진행 완료</String>
   <String Id="IDS__IsResumeDlg_Resuming">{&amp;TahomaBold10}[ProductName] 설치 관리자 재개</String>
   <String Id="IDS__IsResumeDlg_ResumeSuspended">설치 관리자가 컴퓨터에서 [ProductName]의 일시 중단된 설치를 완료합니다. 계속하려면 다음을 클릭하십시오.</String>
   <String Id="IDS__IsResumeDlg_WizardResume">설치 관리자가 컴퓨터에서 [ProductName]의 설치를 완료합니다. 계속하려면 다음을 클릭하십시오.</String>


   <!-- Error Strings -->
   <String Id="MsgWSWCInstalled">호환되지 않는 버전의 Horizon Client가 이 컴퓨터에 이미 설치되어 있으므로 설치를 계속할 수 없습니다.

[ProductName] 설치를 계속하려면 Horizon Client를 제거한 후 이 설치 관리자를 다시 실행하십시오.</String>
   <String Id="MsgClientRunning">에이전트 설치를 계속할 수 없습니다. 활성 Horizon Client 세션이 검색되었습니다.</String>
   <String Id="MsgDowngradeDetected">설치 관리자에서 새 버전의 [ProductName]이(가) 이미 설치된 것을 감지했습니다.</String>
   <String Id="MsgManualUninstallRequired">이 설치 관리자는 기존에 설치된 제품에 대해 업그레이드를 수행할 수 없습니다. 이 설치를 계속하기 전에 기존 제품을 제거하십시오.</String>
   <String Id="MsgMustReboot">설치를 계속하려면 시스템을 다시 부팅해야 합니다.</String>
   <String Id="MsgServerInstalled">Horizon Connection Server가 이 컴퓨터에 이미 설치되어 있으므로 설치를 계속할 수 없습니다.

[ProductName] 설치를 계속하려면 Connection Server를 제거한 후 이 설치 관리자를 다시 실행하십시오.</String>
   <String Id="MsgUnsupportedOldVersion">이 제품의 지원되지 않는 이전 버전이 이미 설치되어 있어 이 제품을 설치할 수 없습니다. 이 제품을 설치하기 전에 이를 제거하고 시스템을 다시 부팅하십시오.</String>
   <String Id="MsgUrlRedirectionInstalled">URL 리디렉션이 활성화된 [ProductName]을(를) 설치하려고 하지만 이미 Horizon Client에서 URL 리디렉션을 사용하도록 설정되어 있으므로 이 작업은 지원되지 않습니다. 작업을 계속하면 URL 리디렉션 없이 Agent가 설치됩니다. Agent 모드에서 URL 리디렉션을 수행하려면 먼저 Client를 제거하고 Agent를 설치해야 합니다.</String>
   <String Id="MsgUNCRedirectionInstalled">UNC 리디렉션을 사용하도록 설정된 [ProductName]을(를) 설치하려고 하지만 이미 Horizon Client에서 UNC 리디렉션을 사용하도록 설정되어 있으므로 이 작업은 지원되지 않습니다. 작업을 계속하면 UNC 리디렉션 없이 Agent가 설치됩니다. Agent 모드에서 UNC 리디렉션을 수행하려면 먼저 Client를 제거하고 Agent를 설치해야 합니다.</String>
   <String Id="MsgVdmLoopbackIp">'localhost' IP 주소를 설정하는 중에 오류가 발생했습니다. IP 프로토콜을 선택하고 해당 프로토콜이 이 시스템에 설치되어 있는지 확인하십시오.</String>
   <String Id="MsgWindowsUpdateInProgress">Windows Update가 현재 진행 중입니다. Windows Update를 완료하고 Horizon Agent를 설치하기 전에 시스템을 재부팅하십시오.</String>
   <String Id="MsgWindowsUpdateAndRestartPending">설치를 계속하려면 시스템을 업데이트하거나 재부팅해야 합니다.</String>
   <String Id="NoRepairAllowed">활성 Horizon 세션이 진행 중입니다. [ProductName] 복구를 계속할 수 없습니다.</String>
   <String Id="MsgInstallationAbortifSVIInstalled">이 설치 관리자는 기존 제품 설치를 통해 업그레이드를 수행할 수 없습니다. Horizon View Composer 기능은 버전 8.1에서 더 이상 지원되지 않습니다. 이 빌드를 설치하려면 먼저 이전 빌드를 제거합니다.</String>
   <String Id="SettingsFileInvalid">설치 관리자 설정 파일("[SETTINGS_FILE]")을 구문 분석하지 못했습니다.

[SettingsFileErrorLine] 줄에 오류가 있습니다.</String>


   <!-- Action Text Strings -->
   <String Id="ActionText_RdpConfig">RDP 구성을 수행하는 중</String>
   <String Id="ConfigUserInit">UserInit 프로세스를 등록하는 중: wssm.exe</String>
   <String Id="IDS_ACTIONTEXT_1">[1]</String>
   <String Id="IDS_ACTIONTEXT_1b">[1]</String>
   <String Id="IDS_ACTIONTEXT_1c">[1]</String>
   <String Id="IDS_ACTIONTEXT_1d">[1]</String>
   <String Id="IDS_ACTIONTEXT_Advertising">애플리케이션을 보급하는 중</String>
   <String Id="IDS_ACTIONTEXT_AllocatingRegistry">레지스트리 공간을 할당하는 중</String>
   <String Id="IDS_ACTIONTEXT_AppCommandLine">애플리케이션: [1], 명령줄: [2]</String>
   <String Id="IDS_ACTIONTEXT_AppId">AppId: [1]{{, AppType: [2]}}</String>
   <String Id="IDS_ACTIONTEXT_AppIdAppTypeRSN">AppId: [1]{{, AppType: [2], 사용자: [3], RSN: [4]}}</String>
   <String Id="IDS_ACTIONTEXT_Application">애플리케이션: [1]</String>
   <String Id="IDS_ACTIONTEXT_BindingExes">실행 파일을 바인딩하는 중</String>
   <String Id="IDS_ACTIONTEXT_ClassId">클래스 ID: [1]</String>
   <String Id="IDS_ACTIONTEXT_ClsID">클래스 ID: [1]</String>
   <String Id="IDS_ACTIONTEXT_ComponentIDQualifier">구성 요소 ID: [1], 인증자: [2]</String>
   <String Id="IDS_ACTIONTEXT_ComponentIdQualifier2">구성 요소 ID: [1], 인증자: [2]</String>
   <String Id="IDS_ACTIONTEXT_ComputingSpace">필요한 공간을 확인하는 중</String>
   <String Id="IDS_ACTIONTEXT_ComputingSpace2">필요한 공간을 확인하는 중</String>
   <String Id="IDS_ACTIONTEXT_ComputingSpace3">필요한 공간을 확인하는 중</String>
   <String Id="IDS_ACTIONTEXT_ContentTypeExtension">MIME 컨텐츠 유형: [1], 확장: [2]</String>
   <String Id="IDS_ACTIONTEXT_ContentTypeExtension2">MIME 컨텐츠 유형: [1], 확장: [2]</String>
   <String Id="IDS_ACTIONTEXT_CopyingNetworkFiles">파일을 네트워크로 복사하는 중</String>
   <String Id="IDS_ACTIONTEXT_CopyingNewFiles">새 파일을 복사하는 중</String>
   <String Id="IDS_ACTIONTEXT_CreatingDuplicate">중복 파일을 생성하는 중</String>
   <String Id="IDS_ACTIONTEXT_CreatingFolders">폴더를 생성하는 중</String>
   <String Id="IDS_ACTIONTEXT_CreatingShortcuts">바로 가기를 생성하는 중</String>
   <String Id="IDS_ACTIONTEXT_DeletingServices">서비스를 삭제하는 중</String>
   <String Id="IDS_ACTIONTEXT_EnvironmentStrings">환경 문자열을 업데이트하는 중</String>
   <String Id="IDS_ACTIONTEXT_EvaluateLaunchConditions">시작 조건을 평가하는 중</String>
   <String Id="IDS_ACTIONTEXT_Extension">확장: [1]</String>
   <String Id="IDS_ACTIONTEXT_Extension2">확장: [1]</String>
   <String Id="IDS_ACTIONTEXT_Feature">기능: [1]</String>
   <String Id="IDS_ACTIONTEXT_FeatureColon">기능: [1]</String>
   <String Id="IDS_ACTIONTEXT_File">파일: [1]</String>
   <String Id="IDS_ACTIONTEXT_File2">파일: [1]</String>
   <String Id="IDS_ACTIONTEXT_FileDependencies">파일: [1], 종속성: [2]</String>
   <String Id="IDS_ACTIONTEXT_FileDir">파일: [1], 디렉토리: [9]</String>
   <String Id="IDS_ACTIONTEXT_FileDir2">파일: [1], 디렉토리: [9]</String>
   <String Id="IDS_ACTIONTEXT_FileDir3">파일: [1], 디렉토리: [9]</String>
   <String Id="IDS_ACTIONTEXT_FileDirSize">파일: [1], 디렉토리: [9], 크기: [6]</String>
   <String Id="IDS_ACTIONTEXT_FileDirSize2">파일: [1], 디렉토리: [9], 크기: [6]</String>
   <String Id="IDS_ACTIONTEXT_FileDirSize3">파일: [1], 디렉토리: [9], 크기: [6]</String>
   <String Id="IDS_ACTIONTEXT_FileDirSize4">파일: [1], 디렉토리: [2], 크기: [3]</String>
   <String Id="IDS_ACTIONTEXT_FileDirectorySize">파일: [1], 디렉토리: [9], 크기: [6]</String>
   <String Id="IDS_ACTIONTEXT_FileFolder">파일: [1], 폴더: [2]</String>
   <String Id="IDS_ACTIONTEXT_FileFolder2">파일: [1], 폴더: [2]</String>
   <String Id="IDS_ACTIONTEXT_FileSectionKeyValue">파일: [1], 섹션: [2], 키: [3], 값: [4]</String>
   <String Id="IDS_ACTIONTEXT_FileSectionKeyValue2">파일: [1], 섹션: [2], 키: [3], 값: [4]</String>
   <String Id="IDS_ACTIONTEXT_Folder">폴더: [1]</String>
   <String Id="IDS_ACTIONTEXT_Folder1">폴더: [1]</String>
   <String Id="IDS_ACTIONTEXT_Font">글꼴: [1]</String>
   <String Id="IDS_ACTIONTEXT_Font2">글꼴: [1]</String>
   <String Id="IDS_ACTIONTEXT_FoundApp">찾은 애플리케이션: [1]</String>
   <String Id="IDS_ACTIONTEXT_FreeSpace">사용 가능한 공간: [1]</String>
   <String Id="IDS_ACTIONTEXT_GeneratingScript">실행할 스크립트 작업을 생성하는 중:</String>
   <String Id="IDS_ACTIONTEXT_InitializeODBCDirs">ODBC 디렉토리를 초기화하는 중</String>
   <String Id="IDS_ACTIONTEXT_InstallODBC">ODBC 구성 요소를 설치하는 중</String>
   <String Id="IDS_ACTIONTEXT_InstallServices">새 서비스를 설치하는 중</String>
   <String Id="IDS_ACTIONTEXT_InstallingSystemCatalog">시스템 카탈로그를 설치하는 중</String>
   <String Id="IDS_ACTIONTEXT_KeyName">키: [1], 이름: [2]</String>
   <String Id="IDS_ACTIONTEXT_KeyNameValue">키: [1], 이름: [2], 값: [3]</String>
   <String Id="IDS_ACTIONTEXT_LibId">LibID: [1]</String>
   <String Id="IDS_ACTIONTEXT_Libid2">LibID: [1]</String>
   <String Id="IDS_ACTIONTEXT_MigratingFeatureStates">관련 애플리케이션에서 기능 상태를 마이그레이션하는 중</String>
   <String Id="IDS_ACTIONTEXT_MovingFiles">파일을 이동하는 중</String>
   <String Id="IDS_ACTIONTEXT_NameValueAction">이름: [1], 값: [2], 작업 [3]</String>
   <String Id="IDS_ACTIONTEXT_NameValueAction2">이름: [1], 값: [2], 작업 [3]</String>
   <String Id="IDS_ACTIONTEXT_PatchingFiles">파일을 패치하는 중</String>
   <String Id="IDS_ACTIONTEXT_ProgID">ProgID: [1]</String>
   <String Id="IDS_ACTIONTEXT_ProgID2">ProgID: [1]</String>
   <String Id="IDS_ACTIONTEXT_PropertySignature">속성: [1], 서명: [2]</String>
   <String Id="IDS_ACTIONTEXT_PublishProductFeatures">제품 기능을 게시하는 중</String>
   <String Id="IDS_ACTIONTEXT_PublishProductInfo">제품 정보를 게시하는 중</String>
   <String Id="IDS_ACTIONTEXT_PublishingQualifiedComponents">정규 구성 요소를 게시하는 중</String>
   <String Id="IDS_ACTIONTEXT_RegUser">사용자를 등록하는 중</String>
   <String Id="IDS_ACTIONTEXT_RegisterClassServer">클래스 서버를 등록하는 중</String>
   <String Id="IDS_ACTIONTEXT_RegisterExtensionServers">확장 서버를 등록하는 중</String>
   <String Id="IDS_ACTIONTEXT_RegisterFonts">글꼴을 등록하는 중</String>
   <String Id="IDS_ACTIONTEXT_RegisterMimeInfo">MIME 정보를 등록하는 중</String>
   <String Id="IDS_ACTIONTEXT_RegisterTypeLibs">형식 라이브러리를 등록하는 중</String>
   <String Id="IDS_ACTIONTEXT_RegisteringComPlus">COM+ 애플리케이션 및 구성 요소를 등록하는 중</String>
   <String Id="IDS_ACTIONTEXT_RegisteringModules">모듈을 등록하는 중</String>
   <String Id="IDS_ACTIONTEXT_RegisteringProduct">제품을 등록하는 중</String>
   <String Id="IDS_ACTIONTEXT_RegisteringProgIdentifiers">프로그램 식별자를 등록하는 중</String>
   <String Id="IDS_ACTIONTEXT_RemoveApps">애플리케이션을 제거하는 중</String>
   <String Id="IDS_ACTIONTEXT_RemovingBackup">백업 파일을 제거하는 중</String>
   <String Id="IDS_ACTIONTEXT_RemovingDuplicates">중복 파일을 제거하는 중</String>
   <String Id="IDS_ACTIONTEXT_RemovingFiles">파일을 제거하는 중</String>
   <String Id="IDS_ACTIONTEXT_RemovingFolders">폴더를 제거하는 중</String>
   <String Id="IDS_ACTIONTEXT_RemovingIni">INI 파일 항목을 제거하는 중</String>
   <String Id="IDS_ACTIONTEXT_RemovingMoved">이동한 파일을 제거하는 중</String>
   <String Id="IDS_ACTIONTEXT_RemovingODBC">ODBC 구성 요소를 제거하는 중</String>
   <String Id="IDS_ACTIONTEXT_RemovingRegistry">시스템 레지스트리 값을 제거하는 중</String>
   <String Id="IDS_ACTIONTEXT_RemovingShortcuts">바로 가기를 제거하는 중</String>
   <String Id="IDS_ACTIONTEXT_RollingBack">작업을 롤백하는 중:</String>
   <String Id="IDS_ACTIONTEXT_SearchForRelated">관련 애플리케이션을 검색하는 중</String>
   <String Id="IDS_ACTIONTEXT_SearchInstalled">설치한 애플리케이션을 검색하는 중</String>
   <String Id="IDS_ACTIONTEXT_SearchingQualifyingProducts">정품을 검색하는 중</String>
   <String Id="IDS_ACTIONTEXT_ServerConfig">Horizon Connection Server 구성</String>
   <String Id="IDS_ACTIONTEXT_Service">서비스: [1]</String>
   <String Id="IDS_ACTIONTEXT_Service2">서비스: [2]</String>
   <String Id="IDS_ACTIONTEXT_Service3">서비스: [1]</String>
   <String Id="IDS_ACTIONTEXT_Service4">서비스: [1]</String>
   <String Id="IDS_ACTIONTEXT_Shortcut">바로 가기: [1]</String>
   <String Id="IDS_ACTIONTEXT_Shortcut1">바로 가기: [1]</String>
   <String Id="IDS_ACTIONTEXT_StartingServices">서비스를 시작하는 중</String>
   <String Id="IDS_ACTIONTEXT_StoppingServices">서비스를 중지하는 중</String>
   <String Id="IDS_ACTIONTEXT_UnpublishProductFeatures">제품 기능의 게시를 취소하는 중</String>
   <String Id="IDS_ACTIONTEXT_UnpublishQualified">정규 구성 요소의 게시를 취소하는 중</String>
   <String Id="IDS_ACTIONTEXT_UnpublishingProductInfo">제품 정보의 게시를 취소하는 중</String>
   <String Id="IDS_ACTIONTEXT_UnregTypeLibs">형식 라이브러리의 등록을 취소하는 중</String>
   <String Id="IDS_ACTIONTEXT_UnregisterClassServers">클래스 서버 등록 취소</String>
   <String Id="IDS_ACTIONTEXT_UnregisterExtensionServers">확장 서버의 등록을 취소하는 중</String>
   <String Id="IDS_ACTIONTEXT_UnregisterModules">모듈의 등록을 취소하는 중</String>
   <String Id="IDS_ACTIONTEXT_UnregisteringComPlus">COM+ 애플리케이션 및 구성 요소의 등록을 취소하는 중</String>
   <String Id="IDS_ACTIONTEXT_UnregisteringFonts">글꼴 등록을 취소하는 중</String>
   <String Id="IDS_ACTIONTEXT_UnregisteringMimeInfo">MIME 정보 등록을 취소하는 중</String>
   <String Id="IDS_ACTIONTEXT_UnregisteringProgramIds">프로그램 식별자의 등록을 취소하는 중</String>
   <String Id="IDS_ACTIONTEXT_UpdateComponentRegistration">구성 요소 등록을 업데이트하는 중</String>
   <String Id="IDS_ACTIONTEXT_UpdateEnvironmentStrings">환경 문자열을 업데이트하는 중</String>
   <String Id="IDS_ACTIONTEXT_Validating">설치 유효성을 검사하는 중</String>
   <String Id="IDS_ACTIONTEXT_BlastUDPInstall">UDP 통신 설정을 구성하는 중</String>
   <String Id="IDS_ACTIONTEXT_BlastUDPUninstall">UDP 통신 설정을 정리하는 중</String>
   <String Id="IDS_ACTIONTEXT_WritingINI">INI 파일 값을 쓰는 중</String>
   <String Id="IDS_ACTIONTEXT_WritingRegistry">시스템 레지스트리 값을 쓰는 중</String>
   <String Id="UnconfigUserInit">UserInit 프로세스의 등록을 취소하는 중: wssm.exe</String>
   <String Id="VM_WaitForpairing_ProgressText">Waiting for agent pairing to complete...</String>

   <!-- UIText Strings -->
   <String Id="IDS_UITEXT_Available">사용 가능</String>
   <String Id="IDS_UITEXT_Bytes">바이트</String>
   <String Id="IDS_UITEXT_CompilingFeaturesCost">이 기능에 대한 공간을 계산하는 중...</String>
   <String Id="IDS_UITEXT_Differences">차이</String>
   <String Id="IDS_UITEXT_DiskSize">디스크 크기</String>
   <String Id="IDS_UITEXT_FeatureCompletelyRemoved">이 기능은 완전히 제거됩니다.</String>
   <String Id="IDS_UITEXT_FeatureContinueNetwork">이 기능은 계속 네트워크에서 실행됨</String>
   <String Id="IDS_UITEXT_FeatureFreeSpace">이 기능을 제거하면 하드 드라이브에 [1]의 공간이 확보됩니다.</String>
   <String Id="IDS_UITEXT_FeatureInstalledCD">이 기능 및 모든 하위 기능은 CD에서 실행되도록 설치됩니다.</String>
   <String Id="IDS_UITEXT_FeatureInstalledCD2">이 기능은 CD에서 실행되도록 설치됩니다.</String>
   <String Id="IDS_UITEXT_FeatureInstalledLocal">이 기능 및 모든 하위 기능은 로컬 하드 드라이브에 설치됩니다.</String>
   <String Id="IDS_UITEXT_FeatureInstalledLocal2">이 기능은 로컬 하드 드라이브에 설치됩니다.</String>
   <String Id="IDS_UITEXT_FeatureInstalledNetwork">이 기능 및 모든 하위 기능은 네트워크에서 실행되도록 설치됩니다.</String>
   <String Id="IDS_UITEXT_FeatureInstalledNetwork2">이 기능은 네트워크에서 실행되도록 설치됩니다.</String>
   <String Id="IDS_UITEXT_FeatureInstalledRequired">필요할 때 설치됩니다.</String>
   <String Id="IDS_UITEXT_FeatureInstalledWhenRequired">이 기능은 필요할 때 설치되도록 설정됩니다.</String>
   <String Id="IDS_UITEXT_FeatureInstalledWhenRequired2">이 기능은 필요할 때 설치됩니다.</String>
   <String Id="IDS_UITEXT_FeatureLocal">이 기능은 로컬 하드 드라이브에 설치됩니다.</String>
   <String Id="IDS_UITEXT_FeatureLocal2">이 기능은 로컬 하드 드라이브에 설치됩니다.</String>
   <String Id="IDS_UITEXT_FeatureNetwork">이 기능은 네트워크에서 실행되도록 설치됩니다.</String>
   <String Id="IDS_UITEXT_FeatureNetwork2">이 기능은 네트워크에서 실행할 수 있습니다.</String>
   <String Id="IDS_UITEXT_FeatureNotAvailable">이 기능을 사용할 수 없게 됩니다.</String>
   <String Id="IDS_UITEXT_FeatureOnCD">이 기능은 CD에서 실행되도록 설치됩니다.</String>
   <String Id="IDS_UITEXT_FeatureOnCD2">이 기능은 CD에서 실행할 수 있습니다.</String>
   <String Id="IDS_UITEXT_FeatureRemainLocal">이 기능은 로컬 하드 드라이브에 그대로 남아 있게 됩니다.</String>
   <String Id="IDS_UITEXT_FeatureRemoveNetwork">이 기능은 로컬 하드 드라이브에서 제거되지만 네트워크에서 계속 실행할 수 있습니다.</String>
   <String Id="IDS_UITEXT_FeatureRemovedCD">이 기능은 로컬 하드 드라이브에서 제거되지만 CD에서 계속 실행할 수 있습니다.</String>
   <String Id="IDS_UITEXT_FeatureRemovedUnlessRequired">이 기능은 로컬 하드 드라이브에서 제거되지만 필요할 때 설치되도록 설정됩니다.</String>
   <String Id="IDS_UITEXT_FeatureRequiredSpace">이 기능을 사용하려면 하드 드라이브에 [1]의 사용 가능 공간이 필요합니다.</String>
   <String Id="IDS_UITEXT_FeatureRunFromCD">이 기능은 그대로 CD에서 실행됩니다.</String>
   <String Id="IDS_UITEXT_FeatureSpaceFree">이 기능을 제거하면 하드 드라이브에 [1]의 공간이 확보됩니다. [3]개의 하위 기능 중 [2]개가 선택되었으며, 하위 기능을 제거하면 하드 드라이브에 [4]의 공간이 확보됩니다.</String>
   <String Id="IDS_UITEXT_FeatureSpaceFree2">이 기능을 제거하면 하드 드라이브에 [1]의 공간이 확보됩니다. [3]개의 하위 기능 중 [2]개가 선택되었으며, 이 하위 기능을 사용하려면 하드 드라이브에 [4]의 공간이 필요합니다.</String>
   <String Id="IDS_UITEXT_FeatureSpaceFree3">이 기능을 사용하려면 하드 드라이브에 [1]의 공간이 필요합니다. [3]개의 하위 기능 중 [2]개가 선택되었으며, 하위 기능을 제거하면 하드 드라이브에 [4]의 공간이 확보됩니다.</String>
   <String Id="IDS_UITEXT_FeatureSpaceFree4">이 기능을 사용하려면 하드 드라이브에 [1]의 공간이 필요합니다. [3]개의 하위 기능 중 [2]개가 선택되었으며, 이 하위 기능을 사용하려면 하드 드라이브에 [4]의 공간이 필요합니다.</String>
   <String Id="IDS_UITEXT_FeatureUnavailable">이 기능을 사용할 수 없게 됩니다.</String>
   <String Id="IDS_UITEXT_FeatureUninstallNoNetwork">이 기능은 완전히 제거되며 네트워크에서 실행할 수 없게 됩니다.</String>
   <String Id="IDS_UITEXT_FeatureWasCD">이 기능은 CD에서 실행되었지만 이제 필요할 때 설치되도록 설정됩니다.</String>
   <String Id="IDS_UITEXT_FeatureWasCDLocal">이 기능은 CD에서 실행되었지만 이제 로컬 하드 드라이브에 설치됩니다.</String>
   <String Id="IDS_UITEXT_FeatureWasOnNetworkInstalled">이 기능은 네트워크에서 실행되었지만 이제 필요할 때 설치됩니다.</String>
   <String Id="IDS_UITEXT_FeatureWasOnNetworkLocal">이 기능은 네트워크에서 실행되었지만 이제 로컬 하드 드라이브에 설치됩니다.</String>
   <String Id="IDS_UITEXT_FeatureWillBeUninstalled">이 기능은 완전히 제거되며 CD에서 실행할 수 없게 됩니다.</String>
   <String Id="IDS_UITEXT_Folder">Fldr|새 폴더</String>
   <String Id="IDS_UITEXT_GB">GB</String>
   <String Id="IDS_UITEXT_KB">KB</String>
   <String Id="IDS_UITEXT_MB">MB</String>
   <String Id="IDS_UITEXT_Required">필수</String>
   <String Id="IDS_UITEXT_TimeRemaining">남은 시간: {[1]분 }[2]초</String>
   <String Id="IDS_UITEXT_Volume">볼륨</String>


   <!-- Error Table Strings -->
   <String Id="IDS_ERROR_0">{{치명적인 오류: }}</String>
   <String Id="IDS_ERROR_1">오류 [1].</String>
   <String Id="IDS_ERROR_2">경고 [1].</String>
   <String Id="IDS_ERROR_4">정보 [1].</String>
   <String Id="IDS_ERROR_5">내부 오류 [1]입니다. [2]{, [3]}{, [4]}</String>
   <String Id="IDS_ERROR_7">{{디스크 꽉 참: }}</String>
   <String Id="IDS_ERROR_8">작업 [Time]: [1]. [2]</String>
   <String Id="IDS_ERROR_9">[ProductName]</String>
   <String Id="IDS_ERROR_10">{[2]}{, [3]}{, [4]}</String>
   <String Id="IDS_ERROR_11">메시지 유형: [1], 인수: [2]</String>
   <String Id="IDS_ERROR_12">=== 로깅 시작: [Date] [Time] ===</String>
   <String Id="IDS_ERROR_13">=== 로깅 중지: [Date] [Time] ===</String>
   <String Id="IDS_ERROR_14">작업 시작 [Time]: [1].</String>
   <String Id="IDS_ERROR_15">작업 끝 [Time]: [1]. 반환 값 [2].</String>
   <String Id="IDS_ERROR_16">남은 시간: {[1]분 }{[2]초}</String>
   <String Id="IDS_ERROR_17">메모리가 부족합니다. 다른 애플리케이션을 종료한 후 다시 시도하십시오.</String>
   <String Id="IDS_ERROR_18">설치 관리자가 더 이상 응답하지 않습니다.</String>
   <String Id="IDS_ERROR_19">설치 관리자가 중간에 중단되었습니다.</String>
   <String Id="IDS_ERROR_20">[ProductName]을(를) 구성하는 동안 잠시 기다려 주십시오.</String>
   <String Id="IDS_ERROR_21">필요한 정보를 모으는 중...</String>
   <String Id="IDS_ERROR_22">이 애플리케이션의 이전 버전을 제거하는 중</String>
   <String Id="IDS_ERROR_23">이 애플리케이션의 이전 버전을 제거하기 위해 준비하는 중</String>
   <String Id="IDS_ERROR_32">{[ProductName] }설치를 완료했습니다.</String>
   <String Id="IDS_ERROR_33">{[ProductName] }설치하지 못했습니다.</String>
   <String Id="IDS_ERROR_1101">파일 읽기 오류: [2]. {{ 시스템 오류 [3].}} 파일이 있고 그 파일에 액세스할 수 있는지 확인하십시오.</String>
   <String Id="IDS_ERROR_1301">[3] 파일을 생성할 수 없습니다. 이 이름을 가진 디렉토리가 이미 있습니다. 설치를 취소하고 다른 위치에 설치해 보십시오.</String>
   <String Id="IDS_ERROR_1302">다음 디스크를 넣으십시오. [2]</String>
   <String Id="IDS_ERROR_1303">설치 관리자가 이 디렉토리에 액세스할 권한이 없습니다. [2]. 설치를 계속할 수 없습니다. 관리자로 로그온하거나 시스템 관리자에게 문의하십시오.</String>
   <String Id="IDS_ERROR_1304">[2] 파일에 쓰는 동안 오류가 발생했습니다. 해당 디렉토리에 액세스할 수 있는지 확인하십시오.</String>
   <String Id="IDS_ERROR_1305">[2] 파일을 읽는 동안 오류가 발생했습니다. 파일이 있고 그 파일에 액세스할 수 있는지 확인하십시오.</String>
   <String Id="IDS_ERROR_1306">다른 애플리케이션에서 [2] 파일을 단독으로 사용하고 있습니다. 다른 애플리케이션을 모두 닫은 후 재시도를 클릭하십시오.</String>
   <String Id="IDS_ERROR_1307">디스크 공간이 부족해서 [2] 파일을 설치할 수 없습니다. 디스크 공간을 늘린 후 재시도를 클릭하거나, 취소를 클릭하여 끝내십시오.</String>
   <String Id="IDS_ERROR_1308">소스 파일이 없음: [2]. 파일이 있고 그 파일에 액세스할 수 있는지 확인하십시오.</String>
   <String Id="IDS_ERROR_1309">파일 읽기 오류: [3]. {{ 시스템 오류 [2].}} 파일이 있고 그 파일에 액세스할 수 있는지 확인하십시오.</String>
   <String Id="IDS_ERROR_1310">파일에 쓰는 동안 오류 발생: [3]. {{ 시스템 오류 [2].}} 해당 디렉토리에 액세스할 수 있는지 확인하십시오.</String>
   <String Id="IDS_ERROR_1311">소스 파일이 없음{{(cabinet)}}: [2]. 파일이 있고 그 파일에 액세스할 수 있는지 확인하십시오.</String>
   <String Id="IDS_ERROR_1312">[2] 디렉토리를 생성할 수 없습니다. 이 이름을 가진 파일이 이미 있습니다. 파일 이름을 바꾸거나 파일을 제거한 후 재시도를 클릭하거나, 취소를 클릭하여 끝내십시오.</String>
   <String Id="IDS_ERROR_1313">[2] 볼륨을 현재 사용할 수 없습니다. 다른 볼륨을 선택하십시오.</String>
   <String Id="IDS_ERROR_1314">지정한 경로 [2]을(를) 사용할 수 없습니다.</String>
   <String Id="IDS_ERROR_1315">지정한 폴더 [2]에 쓸 수 없습니다.</String>
   <String Id="IDS_ERROR_1316">[2] 파일을 읽는 동안 네트워크 오류가 발생했음</String>
   <String Id="IDS_ERROR_1317">[2] 디렉토리를 생성하는 동안 오류가 발생했음</String>
   <String Id="IDS_ERROR_1318">[2] 디렉토리를 생성하는 동안 네트워크 오류가 발생했음</String>
   <String Id="IDS_ERROR_1319">소스 파일 캐비닛 [2]을(를) 여는 동안 네트워크 오류가 발생했습니다.</String>
   <String Id="IDS_ERROR_1320">지정한 경로 [2]이(가) 너무 깁니다.</String>
   <String Id="IDS_ERROR_1321">설치 관리자가 [2] 파일을 수정할 권한이 없습니다.</String>
   <String Id="IDS_ERROR_1322">[2] 경로의 일부가 시스템에서 허용하는 길이를 초과합니다.</String>
   <String Id="IDS_ERROR_1323">[2] 경로에 폴더 경로로 사용할 수 없는 단어가 들어 있습니다.</String>
   <String Id="IDS_ERROR_1324">[2] 경로에 사용할 수 없는 문자가 들어 있습니다.</String>
   <String Id="IDS_ERROR_1325">[2]은(는) 유효한 약식 파일 이름이 아닙니다.</String>
   <String Id="IDS_ERROR_1326">파일 보안을 가져오는 동안 오류가 발생했습니다. [3] GetLastError: [2]</String>
   <String Id="IDS_ERROR_1327">유효하지 않은 드라이브: [2]</String>
   <String Id="IDS_ERROR_1328">[2] 파일에 패치를 적용하는 동안 오류가 발생했습니다. 파일이 다른 방법으로 업데이트되어 이 패치로 더 이상 수정할 수 없습니다. 자세한 내용은 패치 벤더에 문의하십시오. {{시스템 오류: [3]}}</String>
   <String Id="IDS_ERROR_1329">캐비닛 파일 [2]이(가) 디지털로 서명되어 있지 않아 필요한 파일을 설치할 수 없습니다. 캐비닛 파일이 손상된 것 같습니다.</String>
   <String Id="IDS_ERROR_1330">캐비닛 파일 [2]의 디지털 서명이 잘못되었기 때문에 필요한 파일을 설치할 수 없습니다. 캐비닛 파일이 손상된 것 같습니다.{ WinVerifyTrust에서 [3] 오류를 반환했습니다.}</String>
   <String Id="IDS_ERROR_1331">[2] 파일을 올바로 복사하지 못했습니다. CRC 오류입니다.</String>
   <String Id="IDS_ERROR_1332">[2] 파일을 올바로 패치하지 못했습니다. CRC 오류입니다.</String>
   <String Id="IDS_ERROR_1333">[2] 파일을 올바로 패치하지 못했습니다. CRC 오류입니다.</String>
   <String Id="IDS_ERROR_1334">캐비닛 파일 '[3]'에서 찾을 수 없어서 '[2]' 파일을 설치할 수 없습니다. 네트워크 오류이거나, CD-ROM에서 읽기 오류이거나, 이 패키지에 문제가 있을 수 있습니다.</String>
   <String Id="IDS_ERROR_1335">설치에 필요한 캐비닛 파일 '[2]'이(가) 손상되어 사용할 수 없습니다. 네트워크 오류이거나, CD-ROM에서 읽기 오류이거나, 이 패키지에 문제가 있을 수 있습니다.</String>
   <String Id="IDS_ERROR_1336">설치를 완료하는 데 필요한 임시 파일을 생성하는 동안 오류가 발생했습니다. 폴더: [3]. 시스템 오류 코드: [2]</String>
   <String Id="IDS_ERROR_1401">[2] 키를 생성하지 못했습니다. {{ 시스템 오류 [3].}} 이 키에 대한 액세스 권한이 충분한지 확인하거나 고객 지원 담당자에게 문의하십시오.</String>
   <String Id="IDS_ERROR_1402">[2] 키를 열 수 없습니다. {{ 시스템 오류 [3].}} 이 키에 대한 액세스 권한이 충분한지 확인하거나 고객 지원 담당자에게 문의하십시오.</String>
   <String Id="IDS_ERROR_1403">[3] 키에서 [2] 값을 삭제하지 못했습니다. {{ 시스템 오류 [4].}} 이 키에 대한 액세스 권한이 충분한지 확인하거나 고객 지원 담당자에게 문의하십시오.</String>
   <String Id="IDS_ERROR_1404">[2] 키를 삭제하지 못했습니다. {{ 시스템 오류 [3].}} 이 키에 대한 액세스 권한이 충분한지 확인하거나 고객 지원 담당자에게 문의하십시오.</String>
   <String Id="IDS_ERROR_1405">[3] 키에서 [2] 값을 읽지 못했습니다. {{ 시스템 오류 [4].}} 이 키에 대한 액세스 권한이 충분한지 확인하거나 고객 지원 담당자에게 문의하십시오.</String>
   <String Id="IDS_ERROR_1406">[3] 키에 [2] 값을 쓰지 못했습니다. {{ 시스템 오류 [4].}} 이 키에 대한 액세스 권한이 충분한지 확인하거나 고객 지원 담당자에게 문의하십시오.</String>
   <String Id="IDS_ERROR_1407">[2] 키의 값 이름을 가져오지 못했습니다. {{ 시스템 오류 [3].}} 이 키에 대한 액세스 권한이 충분한지 확인하거나 고객 지원 담당자에게 문의하십시오.</String>
   <String Id="IDS_ERROR_1408">[2] 키의 하위 키 이름을 가져오지 못했습니다. {{ 시스템 오류 [3].}} 이 키에 대한 액세스 권한이 충분한지 확인하거나 고객 지원 담당자에게 문의하십시오.</String>
   <String Id="IDS_ERROR_1409">[2] 키의 보안 정보를 읽지 못했습니다. {{ 시스템 오류 [3].}} 이 키에 대한 액세스 권한이 충분한지 확인하거나 고객 지원 담당자에게 문의하십시오.</String>
   <String Id="IDS_ERROR_1410">사용 가능한 레지스트리 공간을 늘리지 못했습니다. [2]KB 정도의 사용 가능한 레지스트리 공간이 있어야 이 애플리케이션을 설치할 수 있습니다.</String>
   <String Id="IDS_ERROR_1500">다른 설치가 진행 중입니다. 이 설치를 계속하기 전에 다른 설치를 완료해야 합니다.</String>
   <String Id="IDS_ERROR_1501">보안 데이터에 액세스하는 동안 오류가 발생했습니다. Windows Installer를 올바르게 구성했는지 확인한 후 다시 설치하십시오.</String>
   <String Id="IDS_ERROR_1502">[2] 사용자가 [3] 제품의 설치를 시작한 적이 있습니다. 이 사용자가 다시 설치 프로그램을 실행해야 해당 제품을 사용할 수 있습니다. 현재 설치는 계속 진행됩니다.</String>
   <String Id="IDS_ERROR_1503">[2] 사용자가 [3] 제품의 설치를 시작한 적이 있습니다. 이 사용자가 다시 설치 프로그램을 실행해야 해당 제품을 사용할 수 있습니다.</String>
   <String Id="IDS_ERROR_1601">디스크 공간 부족 -- 볼륨: '[2]', 필요한 공간: [3]KB, 사용 가능한 공간: [4]KB. 디스크 공간을 늘린 후 다시 시도하십시오.</String>
   <String Id="IDS_ERROR_1602">취소하시겠습니까?</String>
   <String Id="IDS_ERROR_1603">[2][3] 파일을 다음 프로세스에서 사용하고 있습니다. {이름: [4], ID: [5], 창 제목: [6]}. 이 애플리케이션을 닫은 후 다시 시도하십시오.</String>
   <String Id="IDS_ERROR_1604">[2] 제품이 이미 설치되어 있으므로 이 제품을 설치할 수 없습니다. 두 제품은 호환되지 않습니다.</String>
   <String Id="IDS_ERROR_1605">디스크 공간 부족 -- 볼륨: [2], 필요한 공간: [3]KB, 사용 가능한 공간: [4]KB. 롤백 기능을 해제하면 충분한 디스크 공간이 생깁니다. 끝내려면 중단을 클릭하고, 사용 가능한 디스크 공간을 다시 확인하려면 재시도를, 롤백하지 않고 계속하려면 무시를 클릭하십시오.</String>
   <String Id="IDS_ERROR_1606">네트워크 위치 [2]에 액세스할 수 없습니다.</String>
   <String Id="IDS_ERROR_1607">다음 애플리케이션을 닫아야 설치를 계속할 수 있습니다.</String>
   <String Id="IDS_ERROR_1608">지금 설치하려는 제품의 이전 버전이 이 시스템에 설치되어 있지 않습니다.</String>
   <String Id="IDS_ERROR_1609">보안 설정을 적용하는 동안 오류가 발생했습니다. [2]은(는) 유효한 사용자 또는 그룹이 아닙니다. 패키지에 문제가 있거나 네트워크의 도메인 컨트롤러에 연결하는 데 문제가 있을 수 있습니다. 네트워크 연결을 확인하고 재시도를 클릭하거나, 설치를 마치려면 취소를 클릭하십시오. 사용자의 SID를 찾을 수 없습니다. 시스템 오류 [3]</String>
   <String Id="IDS_ERROR_1651">관리자 사용자가 보급 상태에 있는 사용자별 관리 애플리케이션 또는 시스템별 애플리케이션에 대한 패치를 적용하지 못했습니다.</String>
   <String Id="IDS_ERROR_1701">[2] 키가 잘못되었습니다. 정확한 키를 입력했는지 확인하십시오.</String>
   <String Id="IDS_ERROR_1702">[2]의 구성을 계속하려면 시스템을 다시 시작해야 합니다. 지금 다시 시작하려면 예를 클릭하고 나중에 다시 시작하려면 아니요를 클릭하십시오.</String>
   <String Id="IDS_ERROR_1703">[2]의 변경된 구성을 적용하려면 시스템을 다시 시작해야 합니다. 지금 다시 시작하려면 예를 클릭하고 나중에 다시 시작하려면 아니요를 클릭하십시오.</String>
   <String Id="IDS_ERROR_1704">[2]의 설치가 현재 일시 중단된 상태입니다. 작업을 계속하려면 설치하면서 변경된 내용을 취소해야 합니다. 변경된 내용을 취소하시겠습니까?</String>
   <String Id="IDS_ERROR_1705">이전에 실행한 이 제품의 설치 작업이 적용된 상태입니다. 작업을 계속하려면 설치하면서 변경된 내용을 취소해야 합니다. 변경된 내용을 취소하시겠습니까?</String>
   <String Id="IDS_ERROR_1706">[2] 제품의 유효한 소스를 찾을 수 없습니다. Windows Installer를 계속할 수 없습니다.</String>
   <String Id="IDS_ERROR_1707">설치 작업을 완료했습니다.</String>
   <String Id="IDS_ERROR_1708">설치 작업이 실패했습니다.</String>
   <String Id="IDS_ERROR_1709">제품: [2] -- [3]</String>
   <String Id="IDS_ERROR_1710">컴퓨터를 이전 상태로 복원하거나 나중에 다시 설치해야 합니다. 복원하시겠습니까?</String>
   <String Id="IDS_ERROR_1711">설치 정보를 디스크에 쓰는 동안 오류가 발생했습니다. 디스크 공간이 충분한지 확인하고 재시도를 클릭하거나, 설치를 끝내려면 취소를 클릭하십시오.</String>
   <String Id="IDS_ERROR_1712">사용자의 컴퓨터를 이전 상태로 복원하는 데 필요한 파일 중 일부를 찾을 수 없습니다. 복원할 수 없습니다.</String>
   <String Id="IDS_ERROR_1713">[2](이)가 필요한 제품 중 하나를 설치할 수 없습니다. 헬프 데스크 부서에 문의하십시오. {{시스템 오류: [3].}}</String>
   <String Id="IDS_ERROR_1714">[2]의 이전 버전을 제거할 수 없습니다. 헬프 데스크 부서에 문의하십시오. {{시스템 오류 [3].}}</String>
   <String Id="IDS_ERROR_1715">[2]을(를) 설치했습니다.</String>
   <String Id="IDS_ERROR_1716">[2]을(를) 구성했습니다.</String>
   <String Id="IDS_ERROR_1717">[2]을(를) 제거했습니다.</String>
   <String Id="IDS_ERROR_1718">디지털 서명 정책에 따라 [2] 파일이 거부되었습니다.</String>
   <String Id="IDS_ERROR_1719">Windows Installer 서비스에 액세스할 수 없습니다. 고객 지원 담당자에게 문의하여 Windows Installer 서비스가 제대로 등록되어 사용할 수 있는지 확인하십시오.</String>
   <String Id="IDS_ERROR_1720">Windows Installer 패키지에 문제가 있습니다. 설치를 완료하기 위해 필요한 스크립트를 실행할 수 없습니다. 고객 지원 담당자나 패키지 벤더에 문의하십시오. 사용자 지정 작업 [2] 스크립트 오류 [3], [4]: [5] [6]행, [7]열, [8]</String>
   <String Id="IDS_ERROR_1721">Windows Installer 패키지에 문제가 있습니다. 설치를 완료하기 위해 필요한 프로그램을 실행할 수 없습니다. 고객 지원 담당자나 패키지 벤더에 문의하십시오. 작업: [2], 위치: [3], 명령: [4]</String>
   <String Id="IDS_ERROR_1722">Windows Installer 패키지에 문제가 있습니다. 설치 프로그램의 일부로 실행한 프로그램이 예상대로 완료되지 않았습니다. 고객 지원 담당자나 패키지 벤더에 문의하십시오. 작업 [2], 위치: [3], 명령: [4]</String>
   <String Id="IDS_ERROR_1723">Windows Installer 패키지에 문제가 있습니다. 설치를 완료하기 위해 필요한 DLL을 실행할 수 없습니다. 고객 지원 담당자나 패키지 벤더에 문의하십시오. 작업 [2], 항목: [3], 라이브러리: [4]</String>
   <String Id="IDS_ERROR_1724">제거를 완료했습니다.</String>
   <String Id="IDS_ERROR_1725">제거하지 못했습니다.</String>
   <String Id="IDS_ERROR_1726">보급을 완료했습니다.</String>
   <String Id="IDS_ERROR_1727">보급하지 못했습니다.</String>
   <String Id="IDS_ERROR_1728">구성을 완료했습니다.</String>
   <String Id="IDS_ERROR_1729">구성하지 못했습니다.</String>
   <String Id="IDS_ERROR_1730">관리자만이 이 애플리케이션을 제거할 수 있습니다. 이 애플리케이션을 제거하려면 관리자로 로그온하거나 헬프 데스크 부서에 문의하십시오.</String>
   <String Id="IDS_ERROR_1731">제품 [2]의 소스 설치 패키지와 클라이언트 패키지가 동기화되어 있지 않습니다. 설치 패키지 '[3]'의 유효한 복사본을 사용하여 다시 설치하십시오.</String>
   <String Id="IDS_ERROR_1732">[2]의 설치를 완료하려면 컴퓨터를 다시 시작해야 합니다. 현재 다른 사용자가 이 컴퓨터에 로그온되어 있으므로 컴퓨터를 다시 시작하면 해당 사용자의 작업이 손실될 수 있습니다. 지금 다시 시작하시겠습니까?</String>
   <String Id="IDS_ERROR_1801">[2] 경로가 잘못되었습니다. 올바른 경로를 지정하십시오.</String>
   <String Id="IDS_ERROR_1802">메모리가 부족합니다. 다른 애플리케이션을 종료한 후 다시 시도하십시오.</String>
   <String Id="IDS_ERROR_1803">[2] 드라이브에 디스크가 없습니다. 디스크를 넣은 후 재시도를 클릭하거나, 취소를 클릭하여 이전에 선택한 볼륨으로 다시 돌아가십시오.</String>
   <String Id="IDS_ERROR_1804">[2] 드라이브에 디스크가 없습니다. 디스크를 넣은 후 재시도를 클릭하거나, 취소를 클릭하여 찾아보기 대화상자에서 다른 볼륨을 선택하십시오.</String>
   <String Id="IDS_ERROR_1805">[2] 폴더가 없습니다. 기존 폴더 경로를 입력하십시오.</String>
   <String Id="IDS_ERROR_1806">이 폴더를 읽을 수 있는 권한이 없습니다.</String>
   <String Id="IDS_ERROR_1807">설치할 올바른 대상 폴더를 확인할 수 없습니다.</String>
   <String Id="IDS_ERROR_1901">소스 설치 데이터베이스를 읽는 동안 오류가 발생했습니다. [2].</String>
   <String Id="IDS_ERROR_1902">재부팅 작업을 예약하고 있습니다. [2] 파일의 이름을 [3](으)로 바꿉니다. 작업을 완료하려면 다시 부팅해야 합니다.</String>
   <String Id="IDS_ERROR_1903">재부팅 작업을 예약하고 있습니다. [2] 파일을 삭제합니다. 작업을 완료하려면 다시 부팅해야 합니다.</String>
   <String Id="IDS_ERROR_1904">[2] 모듈을 등록하지 못했습니다. HRESULT [3]. 고객 지원 담당자에게 문의하십시오.</String>
   <String Id="IDS_ERROR_1905">[2] 모듈의 등록을 취소하지 못했습니다. HRESULT [3]. 고객 지원 담당자에게 문의하십시오.</String>
   <String Id="IDS_ERROR_1906">[2] 패키지를 캐시하지 못했습니다. 오류: [3]. 고객 지원 담당자에게 문의하십시오.</String>
   <String Id="IDS_ERROR_1907">[2] 글꼴을 등록하지 못했습니다. 글꼴을 설치할 수 있는 충분한 사용 권한이 있는지, 그리고 이 글꼴을 시스템에서 지원하는지 확인하십시오.</String>
   <String Id="IDS_ERROR_1908">[2] 글꼴의 등록을 취소하지 못했습니다. 글꼴을 제거할 수 있는 충분한 사용 권한이 있는지 확인하십시오.</String>
   <String Id="IDS_ERROR_1909">[2] 바로 가기를 생성하지 못했습니다. 대상 폴더가 있고 그 파일에 액세스할 수 있는지 확인하십시오.</String>
   <String Id="IDS_ERROR_1910">[2] 바로 가기를 제거하지 못했습니다. 바로 가기 파일이 있고 그 파일에 액세스할 수 있는지 확인하십시오.</String>
   <String Id="IDS_ERROR_1911">[2] 파일의 형식 라이브러리를 등록하지 못했습니다. 고객 지원 담당자에게 문의하십시오.</String>
   <String Id="IDS_ERROR_1912">[2] 파일의 형식 라이브러리 등록을 취소하지 못했습니다. 고객 지원 담당자에게 문의하십시오.</String>
   <String Id="IDS_ERROR_1913">INI 파일 [2][3]을(를) 업데이트하지 못했습니다. 파일이 있고 그 파일에 액세스할 수 있는지 확인하십시오.</String>
   <String Id="IDS_ERROR_1914">시스템을 다시 부팅할 때 파일 [2](으)로 [3]을(를) 대체하도록 스케줄링하지 못했습니다. 파일 [3]에 대한 쓰기 사용 권한이 있는지 확인하십시오.</String>
   <String Id="IDS_ERROR_1915">ODBC 드라이버 관리자를 제거하는 동안 오류가 발생했습니다. ODBC 오류 [2]: [3]. 고객 지원 담당자에게 문의하십시오.</String>
   <String Id="IDS_ERROR_1916">ODBC 드라이버 관리자를 설치하는 동안 오류가 발생했습니다. ODBC 오류 [2]: [3]. 고객 지원 담당자에게 문의하십시오.</String>
   <String Id="IDS_ERROR_1917">ODBC 드라이버 [4]을(를) 제거하는 동안 오류가 발생했습니다. ODBC 오류 [2]: [3]. ODBC 드라이버를 제거할 수 있는 권한이 있는지 확인하십시오.</String>
   <String Id="IDS_ERROR_1918">ODBC 드라이버 [4]을(를) 설치하는 동안 오류가 발생했습니다. ODBC 오류 [2]: [3]. [4] 파일이 있고 그 파일에 액세스할 수 있는지 확인하십시오.</String>
   <String Id="IDS_ERROR_1919">ODBC 데이터 소스 [4]을(를) 구성하는 동안 오류가 발생했습니다. ODBC 오류 [2]: [3]. [4] 파일이 있고 그 파일에 액세스할 수 있는지 확인하십시오.</String>
   <String Id="IDS_ERROR_1920">서비스 [2]([3])을(를) 시작하지 못했습니다. 시스템 서비스를 시작할 수 있는 권한이 있는지 확인하십시오.</String>
   <String Id="IDS_ERROR_1921">서비스 [2]([3])을(를) 중지하지 못했습니다. 시스템 서비스를 중지할 수 있는 권한이 있는지 확인하십시오.</String>
   <String Id="IDS_ERROR_1922">서비스 [2]([3])을(를) 삭제하지 못했습니다. 시스템 서비스를 제거할 수 있는 권한이 있는지 확인하십시오.</String>
   <String Id="IDS_ERROR_1923">서비스 [2]([3])을(를) 설치하지 못했습니다. 시스템 서비스를 설치할 수 있는 권한이 있는지 확인하십시오.</String>
   <String Id="IDS_ERROR_1924">환경 변수 [2]을(를) 업데이트하지 못했습니다. 환경 변수를 수정할 수 있는 권한이 있는지 확인하십시오.</String>
   <String Id="IDS_ERROR_1925">이 시스템의 모든 사용자가 사용하도록 제품을 설치할 권한이 없습니다. 관리자로 로그온한 후 이 설치를 다시 시도하십시오.</String>
   <String Id="IDS_ERROR_1926">[3] 파일에 대해 파일 보안을 설정하지 못했습니다. 오류: [2]. 이 파일의 보안 설정을 수정할 수 있는 권한이 있는지 확인하십시오.</String>
   <String Id="IDS_ERROR_1927">구성 요소 서비스(COM+ 1.0)가 이 컴퓨터에 설치되어 있지 않습니다. 설치를 완료하려면 구성 요소 서비스가 있어야 합니다. 구성 요소 서비스는 Windows 2000에서 사용할 수 있습니다.</String>
   <String Id="IDS_ERROR_1928">COM+ 애플리케이션을 등록하는 동안 오류가 발생했습니다. 자세한 내용은 헬프 데스크 담당자에게 문의하십시오.</String>
   <String Id="IDS_ERROR_1929">COM+ 애플리케이션 등록을 취소하는 동안 오류가 발생했습니다. 자세한 내용은 헬프 데스크 담당자에게 문의하십시오.</String>
   <String Id="IDS_ERROR_1930">서비스 '[2]'([3])의 설명을 변경할 수 없습니다.</String>
   <String Id="IDS_ERROR_1931">Windows가 [2] 시스템 파일을 보호하고 있기 때문에 Windows Installer 서비스가 해당 파일을 업데이트할 수 없습니다. 이 프로그램이 제대로 작동하려면 운영 체제를 업데이트해야 합니다. {{패키지 버전: [3], OS 보호 버전: [4]}}</String>
   <String Id="IDS_ERROR_1932">Windows Installer 서비스가 보호된 Windows 파일 [2]을(를) 업데이트할 수 없습니다. {{패키지 버전: [3], OS 보호 버전: [4], SFP 오류: [5]}}</String>
   <String Id="IDS_ERROR_1933">Windows Installer 서비스가 하나 이상의 보호된 Windows 파일을 업데이트할 수 없습니다. SFP 오류: [2]. 보호된 파일 목록: [3]</String>
   <String Id="IDS_ERROR_1934">시스템의 정책에 따라 사용자 설치를 할 수 없습니다.</String>
   <String Id="IDS_ERROR_1935">어셈블리 구성 요소 [2]을(를) 설치하는 동안 오류가 발생했습니다. HRESULT: [3]. {{어셈블리 인터페이스: [4], 기능: [5], 어셈블리 이름: [6]}}</String>
   <String Id="IDS_ERROR_1936">[6] 어셈블리를 설치하는 동안 오류가 발생했습니다. 어셈블리가 명명되지 않았거나 최소 키 길이로 서명되지 않았습니다. HRESULT: [3]. {{어셈블리 인터페이스: [4], 기능: [5], 구성 요소: [2]}}</String>
   <String Id="IDS_ERROR_1937">[6] 어셈블리를 설치하는 동안 오류가 발생했습니다. 서명 또는 카탈로그를 확인하지 못했거나 올바르지 않습니다. HRESULT: [3]. {{어셈블리 인터페이스: [4], 기능: [5], 구성 요소: [2]}}</String>
   <String Id="IDS_ERROR_1938">[6] 어셈블리를 설치하는 동안 오류가 발생했습니다. 하나 이상의 어셈블리 모듈을 찾을 수 없습니다. HRESULT: [3]. {{어셈블리 인터페이스: [4], 기능: [5], 구성 요소: [2]}}</String>
   <String Id="IDS_ERROR_2101">운영 체제에서 지원하지 않는 바로 가기입니다.</String>
   <String Id="IDS_ERROR_2102">잘못된 .ini 작업: [2]</String>
   <String Id="IDS_ERROR_2103">셸 폴더 [2]에 대한 경로를 확인할 수 없습니다.</String>
   <String Id="IDS_ERROR_2104">.ini 파일을 쓰는 중: [3]: 시스템 오류: [2].</String>
   <String Id="IDS_ERROR_2105">[3] 바로 가기를 생성하지 못했습니다. 시스템 오류: [2].</String>
   <String Id="IDS_ERROR_2106">[3] 바로 가기를 삭제하지 못했습니다. 시스템 오류: [2].</String>
   <String Id="IDS_ERROR_2107">형식 라이브러리 [2]을(를) 등록하는 동안 [3] 오류가 발생했습니다.</String>
   <String Id="IDS_ERROR_2108">형식 라이브러리 [2]의 등록을 취소하는 동안 [3] 오류가 발생했습니다.</String>
   <String Id="IDS_ERROR_2109">.ini 작업에 대한 섹션이 없습니다.</String>
   <String Id="IDS_ERROR_2110">.ini 작업에 대한 키가 없습니다.</String>
   <String Id="IDS_ERROR_2111">실행 중인 애플리케이션을 검색하지 못했으므로 성능 데이터를 가져올 수 없습니다. 반환된 등록 작업: [2].</String>
   <String Id="IDS_ERROR_2112">실행 중인 애플리케이션을 검색하지 못했으므로 성능 인덱스를 가져올 수 없습니다. 반환된 등록 작업: [2].</String>
   <String Id="IDS_ERROR_2113">실행 중인 애플리케이션을 검색하지 못했습니다.</String>
   <String Id="IDS_ERROR_2200">데이터베이스: [2]. 데이터베이스 개체를 생성하지 못했습니다. 모드 = [3].</String>
   <String Id="IDS_ERROR_2201">데이터베이스: [2]. 메모리 부족으로 인해 초기화하지 못했습니다.</String>
   <String Id="IDS_ERROR_2202">데이터베이스: [2]. 메모리 부족으로 인해 데이터에 액세스하지 못했습니다.</String>
   <String Id="IDS_ERROR_2203">데이터베이스: [2]. 데이터베이스 파일을 열 수 없습니다. 시스템 오류 [3].</String>
   <String Id="IDS_ERROR_2204">데이터베이스: [2]. 테이블이 이미 있습니다. [3].</String>
   <String Id="IDS_ERROR_2205">데이터베이스: [2]. 테이블이 없습니다. [3].</String>
   <String Id="IDS_ERROR_2206">데이터베이스: [2]. 테이블을 삭제할 수 없습니다. [3].</String>
   <String Id="IDS_ERROR_2207">데이터베이스: [2]. 의도 위반.</String>
   <String Id="IDS_ERROR_2208">데이터베이스: [2]. 매개 변수가 부족하여 실행할 수 없습니다.</String>
   <String Id="IDS_ERROR_2209">데이터베이스: [2]. 커서의 상태가 잘못되었습니다.</String>
   <String Id="IDS_ERROR_2210">데이터베이스: [2]. [3]열의 업데이트 데이터 형식이 잘못되었습니다.</String>
   <String Id="IDS_ERROR_2211">데이터베이스: [2]. 데이터베이스 테이블 [3]을(를) 생성할 수 없습니다.</String>
   <String Id="IDS_ERROR_2212">데이터베이스: [2]. 데이터베이스가 쓰기 가능 상태가 아닙니다.</String>
   <String Id="IDS_ERROR_2213">데이터베이스: [2]. 데이터베이스 테이블을 저장하는 동안 오류가 발생했습니다.</String>
   <String Id="IDS_ERROR_2214">데이터베이스: [2]. 내보낼 파일을 쓰는 동안 오류가 발생했습니다. [3].</String>
   <String Id="IDS_ERROR_2215">데이터베이스: [2]. 가져올 파일을 열 수 없습니다. [3].</String>
   <String Id="IDS_ERROR_2216">데이터베이스: [2]. 가져올 파일 형식 오류: [3], [4]행.</String>
   <String Id="IDS_ERROR_2217">데이터베이스: [2]. CreateOutputDatabase [3]의 상태가 잘못되었습니다.</String>
   <String Id="IDS_ERROR_2218">데이터베이스: [2]. 테이블 이름을 제공하지 않았습니다.</String>
   <String Id="IDS_ERROR_2219">데이터베이스: [2]. 설치 관리자 데이터베이스 형식이 잘못되었습니다.</String>
   <String Id="IDS_ERROR_2220">데이터베이스: [2]. 행/필드 데이터가 잘못되었습니다.</String>
   <String Id="IDS_ERROR_2221">데이터베이스: [2]. 가져올 파일의 코드 페이지가 충돌합니다. [3].</String>
   <String Id="IDS_ERROR_2222">데이터베이스: [2]. 변환 또는 병합 코드 페이지 [3]이(가) 데이터베이스 코드 페이지 [4]과(와) 다릅니다.</String>
   <String Id="IDS_ERROR_2223">데이터베이스: [2]. 데이터베이스가 동일하여 변환이 생성되지 않았습니다.</String>
   <String Id="IDS_ERROR_2224">데이터베이스: [2]. GenerateTransform: 데이터베이스가 손상되었습니다. 테이블: [3].</String>
   <String Id="IDS_ERROR_2225">데이터베이스: [2]. 변환: 임시 테이블을 변환할 수 없습니다. 테이블: [3].</String>
   <String Id="IDS_ERROR_2226">데이터베이스: [2]. 변환하지 못했습니다.</String>
   <String Id="IDS_ERROR_2227">데이터베이스: [2]. SQL 쿼리에 잘못된 식별자 '[3]'이(가) 있습니다. [4].</String>
   <String Id="IDS_ERROR_2228">데이터베이스: [2]. SQL 쿼리에 알 수 없는 '[3]' 테이블이 있습니다. [4].</String>
   <String Id="IDS_ERROR_2229">데이터베이스: [2]. SQL 쿼리의 '[3]' 테이블을 로드할 수 없습니다. [4].</String>
   <String Id="IDS_ERROR_2230">데이터베이스: [2]. SQL 쿼리에 반복되는 '[3]' 테이블이 있습니다. [4].</String>
   <String Id="IDS_ERROR_2231">데이터베이스: [2]. SQL 쿼리에 ')'가 없습니다. [3].</String>
   <String Id="IDS_ERROR_2232">데이터베이스: [2]. SQL 쿼리에 예기치 않은 '[3]' 토큰이 있습니다. [4].</String>
   <String Id="IDS_ERROR_2233">데이터베이스: [2]. SQL 쿼리의 SELECT 절에 열이 없습니다. [3].</String>
   <String Id="IDS_ERROR_2234">데이터베이스: [2]. SQL 쿼리의 ORDER BY 절에 열이 없습니다. [3].</String>
   <String Id="IDS_ERROR_2235">데이터베이스: [2]. SQL 쿼리에 '[3]'열이 없거나 모호합니다. [4].</String>
   <String Id="IDS_ERROR_2236">데이터베이스: [2]. SQL 쿼리에 잘못된 연산자 '[3]'이(가) 있습니다. [4].</String>
   <String Id="IDS_ERROR_2237">데이터베이스: [2]. 쿼리 문자열이 없거나 잘못되었습니다. [3].</String>
   <String Id="IDS_ERROR_2238">데이터베이스: [2]. SQL 쿼리에 FROM 절이 없습니다. [3].</String>
   <String Id="IDS_ERROR_2239">데이터베이스: [2]. INSERT SQL 문에 값이 부족합니다.</String>
   <String Id="IDS_ERROR_2240">데이터베이스: [2]. UPDATE SQL 문에 업데이트 열이 없습니다.</String>
   <String Id="IDS_ERROR_2241">데이터베이스: [2]. INSERT SQL 문에 삽입 열이 없습니다.</String>
   <String Id="IDS_ERROR_2242">데이터베이스: [2]. '[3]'열이 반복됩니다.</String>
   <String Id="IDS_ERROR_2243">데이터베이스: [2]. 테이블 생성을 위한 기본 열이 정의되어 있지 않습니다.</String>
   <String Id="IDS_ERROR_2244">데이터베이스: [2]. SQL 쿼리 [4]에 잘못된 유형 지정자 '[3]'이(가) 있습니다.</String>
   <String Id="IDS_ERROR_2245">오류 [3](으)로 인해 IStorage::Stat가 실패했습니다.</String>
   <String Id="IDS_ERROR_2246">데이터베이스: [2]. 설치 관리자 변환 형식이 잘못되었습니다.</String>
   <String Id="IDS_ERROR_2247">데이터베이스: [2] 스트림 읽기/쓰기를 변환하지 못했습니다.</String>
   <String Id="IDS_ERROR_2248">데이터베이스: [2] GenerateTransform/Merge: 기본 테이블의 열 유형이 참조 테이블과 일치하지 않습니다. 테이블: [3] 열 번호: [4].</String>
   <String Id="IDS_ERROR_2249">데이터베이스: [2] GenerateTransform: 기본 테이블의 열이 참조 테이블보다 많습니다. 테이블: [3].</String>
   <String Id="IDS_ERROR_2250">데이터베이스: [2] 변환: 기존 행을 추가할 수 없습니다. 테이블: [3].</String>
   <String Id="IDS_ERROR_2251">데이터베이스: [2] 변환: 존재하지 않는 행을 삭제할 수 없습니다. 테이블: [3].</String>
   <String Id="IDS_ERROR_2252">데이터베이스: [2] 변환: 기존 테이블을 추가할 수 없습니다. 테이블: [3].</String>
   <String Id="IDS_ERROR_2253">데이터베이스: [2] 변환: 존재하지 않는 테이블을 삭제할 수 없습니다. 테이블: [3].</String>
   <String Id="IDS_ERROR_2254">데이터베이스: [2] 변환: 존재하지 않는 행을 업데이트할 수 없습니다. 테이블: [3].</String>
   <String Id="IDS_ERROR_2255">데이터베이스: [2] 변환: 이 이름을 가진 열이 이미 있습니다. 테이블: [3] 열: [4].</String>
   <String Id="IDS_ERROR_2256">데이터베이스: [2] GenerateTransform/Merge: 기본 테이블의 기본 키 수가 참조 테이블과 일치하지 않습니다. 테이블: [3].</String>
   <String Id="IDS_ERROR_2257">데이터베이스: [2]. 읽기 전용 테이블을 수정하려고 했습니다. [3].</String>
   <String Id="IDS_ERROR_2258">데이터베이스: [2]. 매개 변수의 유형이 일치하지 않습니다. [3].</String>
   <String Id="IDS_ERROR_2259">데이터베이스: [2] 테이블을 업데이트하지 못했습니다.</String>
   <String Id="IDS_ERROR_2260">Storage CopyTo에 실패했습니다. 시스템 오류: [3].</String>
   <String Id="IDS_ERROR_2261">[2] 스트림을 제거할 수 없습니다. 시스템 오류: [3].</String>
   <String Id="IDS_ERROR_2262">스트림이 없습니다. [2]. 시스템 오류: [3].</String>
   <String Id="IDS_ERROR_2263">[2] 스트림을 열 수 없습니다. 시스템 오류: [3].</String>
   <String Id="IDS_ERROR_2264">[2] 스트림을 제거할 수 없습니다. 시스템 오류: [3].</String>
   <String Id="IDS_ERROR_2265">스토리지를 커밋할 수 없습니다. 시스템 오류: [3].</String>
   <String Id="IDS_ERROR_2266">스토리지를 롤백할 수 없습니다. 시스템 오류: [3].</String>
   <String Id="IDS_ERROR_2267">[2] 스토리지를 삭제할 수 없습니다. 시스템 오류: [3].</String>
   <String Id="IDS_ERROR_2268">데이터베이스: [2]. 병합: [3] 테이블에서 병합 충돌이 보고되었습니다.</String>
   <String Id="IDS_ERROR_2269">데이터베이스: [2]. 병합: 두 데이터베이스의 '[3]' 테이블에 포함된 열 개수가 다릅니다.</String>
   <String Id="IDS_ERROR_2270">데이터베이스: [2]. GenerateTransform/Merge: 기본 테이블의 열 이름이 참조 테이블과 일치하지 않습니다. 테이블: [3] 열 번호: [4].</String>
   <String Id="IDS_ERROR_2271">변환에 대한 SummaryInformation 쓰기에 실패했습니다.</String>
   <String Id="IDS_ERROR_2272">데이터베이스: [2]. 데이터베이스가 읽기 전용으로 열려 있어 MergeDatabase가 변경 내용을 쓸 수 없습니다.</String>
   <String Id="IDS_ERROR_2273">데이터베이스: [2]. MergeDatabase: 기본 데이터베이스에 대한 참조가 참조 데이터베이스로 전달되었습니다.</String>
   <String Id="IDS_ERROR_2274">데이터베이스: [2]. MergeDatabase: 오류 테이블에 오류를 쓸 수 없습니다. 미리 정의된 오류 테이블에 NULL을 허용하지 않는 열로 인해 발생할 수 있습니다.</String>
   <String Id="IDS_ERROR_2275">데이터베이스: [2]. 지정된 [3] 수정 작업이 테이블 조인에 적합하지 않습니다.</String>
   <String Id="IDS_ERROR_2276">데이터베이스: [2]. 코드 페이지 [3]은(는) 시스템에서 지원되지 않습니다.</String>
   <String Id="IDS_ERROR_2277">데이터베이스: [2]. 테이블 [3]을(를) 저장하지 못했습니다.</String>
   <String Id="IDS_ERROR_2278">데이터베이스: [2]. SQL 쿼리의 WHERE 절이 식 개수 제한(32개)을 초과했습니다. [3].</String>
   <String Id="IDS_ERROR_2279">데이터베이스: [2] 변환: 기본 테이블 [3]에 열이 너무 많습니다.</String>
   <String Id="IDS_ERROR_2280">데이터베이스: [2]. [4] 테이블에 대한 [3]열을 생성할 수 없습니다.</String>
   <String Id="IDS_ERROR_2281">[2] 스트림 이름을 바꿀 수 없습니다. 시스템 오류: [3].</String>
   <String Id="IDS_ERROR_2282">스트림 이름 [2]이(가) 잘못되었습니다.</String>
   <String Id="IDS_ERROR_2302">패치 알림: 지금까지 [2]바이트가 패치되었습니다.</String>
   <String Id="IDS_ERROR_2303">볼륨 정보를 가져오는 동안 오류가 발생했습니다. GetLastError: [2].</String>
   <String Id="IDS_ERROR_2304">사용 가능한 디스크 공간을 가져오는 동안 오류가 발생했습니다. GetLastError: [2]. 볼륨: [3].</String>
   <String Id="IDS_ERROR_2305">패치 스레드를 기다리는 동안 오류가 발생했습니다. GetLastError: [2].</String>
   <String Id="IDS_ERROR_2306">패치 적용을 위한 스레드를 생성할 수 없습니다. GetLastError: [2].</String>
   <String Id="IDS_ERROR_2307">소스 파일 키 이름이 null입니다.</String>
   <String Id="IDS_ERROR_2308">대상 파일 이름이 null입니다.</String>
   <String Id="IDS_ERROR_2309">패치가 이미 진행 중인데 [2] 파일을 패치하려고 합니다.</String>
   <String Id="IDS_ERROR_2310">진행 중인 패치가 없는데 패치를 계속하려고 합니다.</String>
   <String Id="IDS_ERROR_2315">경로 구분 기호가 없습니다. [2].</String>
   <String Id="IDS_ERROR_2318">파일이 없습니다. [2].</String>
   <String Id="IDS_ERROR_2319">파일 특성을 설정하는 동안 오류가 발생했습니다. [3] GetLastError: [2].</String>
   <String Id="IDS_ERROR_2320">파일에 쓸 수 없습니다. [2].</String>
   <String Id="IDS_ERROR_2321">파일을 생성하는 동안 오류가 발생했습니다. [2].</String>
   <String Id="IDS_ERROR_2322">사용자가 취소했습니다.</String>
   <String Id="IDS_ERROR_2323">파일 특성이 잘못되었습니다.</String>
   <String Id="IDS_ERROR_2324">파일을 열 수 없습니다. [3] GetLastError: [2].</String>
   <String Id="IDS_ERROR_2325">파일에 대한 파일 시간을 가져올 수 없습니다. [3] GetLastError: [2].</String>
   <String Id="IDS_ERROR_2326">FileToDosDateTime에 오류가 있습니다.</String>
   <String Id="IDS_ERROR_2327">디렉토리를 제거할 수 없습니다. [3] GetLastError: [2].</String>
   <String Id="IDS_ERROR_2328">파일에 대한 파일 버전을 가져오는 동안 오류가 발생했습니다. [2].</String>
   <String Id="IDS_ERROR_2329">파일을 삭제하는 동안 오류가 발생했습니다. [3]. GetLastError: [2].</String>
   <String Id="IDS_ERROR_2330">파일 특성을 가져오는 동안 오류가 발생했습니다. [3]. GetLastError: [2].</String>
   <String Id="IDS_ERROR_2331">[2] 라이브러리를 로드하거나 [3] 진입점을 찾는 동안 오류가 발생했습니다.</String>
   <String Id="IDS_ERROR_2332">파일 특성을 가져오는 동안 오류가 발생했습니다. GetLastError: [2].</String>
   <String Id="IDS_ERROR_2333">파일 특성을 설정하는 동안 오류가 발생했습니다. GetLastError: [2].</String>
   <String Id="IDS_ERROR_2334">파일 시간을 파일의 현지 시간으로 변환하는 동안 오류가 발생했습니다. [3]. GetLastError: [2].</String>
   <String Id="IDS_ERROR_2335">경로: [2]이(가) [3]의 상위 항목이 아닙니다.</String>
   <String Id="IDS_ERROR_2336">경로에 임시 파일을 생성하는 동안 오류가 발생했습니다. [3]. GetLastError: [2].</String>
   <String Id="IDS_ERROR_2337">파일을 닫을 수 없습니다. [3] GetLastError: [2].</String>
   <String Id="IDS_ERROR_2338">파일에 대한 리소스를 업데이트할 수 없습니다. [3] GetLastError: [2].</String>
   <String Id="IDS_ERROR_2339">파일에 대한 파일 시간을 설정할 수 없습니다. [3] GetLastError: [2].</String>
   <String Id="IDS_ERROR_2340">파일에 대한 리소스를 업데이트할 수 없습니다. [3], 리소스가 없습니다.</String>
   <String Id="IDS_ERROR_2341">파일에 대한 리소스를 업데이트할 수 없습니다. [3], 리소스가 너무 큽니다.</String>
   <String Id="IDS_ERROR_2342">파일에 대한 리소스를 업데이트할 수 없습니다. [3] GetLastError: [2].</String>
   <String Id="IDS_ERROR_2343">지정한 경로가 비어 있습니다.</String>
   <String Id="IDS_ERROR_2344">파일의 유효성을 검사하는 데 필요한 IMAGEHLP.DLL 파일을 찾을 수 없습니다. [2].</String>
   <String Id="IDS_ERROR_2345">[2]: 파일에 유효한 체크섬 값이 들어 있지 않습니다.</String>
   <String Id="IDS_ERROR_2347">사용자가 무시했습니다.</String>
   <String Id="IDS_ERROR_2348">캐비닛 스트림에서 읽는 동안 오류가 발생했습니다.</String>
   <String Id="IDS_ERROR_2349">다른 정보를 사용하여 복사를 재개했습니다.</String>
   <String Id="IDS_ERROR_2350">FDI 서버 오류</String>
   <String Id="IDS_ERROR_2351">파일 키 '[2]'이(가) '[3]' 캐비닛에 없습니다. 설치를 계속할 수 없습니다.</String>
   <String Id="IDS_ERROR_2352">캐비닛 파일 서버를 초기화할 수 없습니다. 필요한 'CABINET.DLL' 파일이 없을 수 있습니다.</String>
   <String Id="IDS_ERROR_2353">캐비닛이 아닙니다.</String>
   <String Id="IDS_ERROR_2354">캐비닛을 처리할 수 없습니다.</String>
   <String Id="IDS_ERROR_2355">캐비닛이 손상되었습니다.</String>
   <String Id="IDS_ERROR_2356">스트림에서 캐비닛을 찾을 수 없습니다. [2].</String>
   <String Id="IDS_ERROR_2357">특성을 설정할 수 없습니다.</String>
   <String Id="IDS_ERROR_2358">파일이 사용 중인지 여부를 확인하는 동안 오류가 발생했습니다. [3]. GetLastError: [2].</String>
   <String Id="IDS_ERROR_2359">대상 파일을 생성할 수 없습니다. 파일이 사용 중일 수 있습니다.</String>
   <String Id="IDS_ERROR_2360">진행률이 표시됩니다.</String>
   <String Id="IDS_ERROR_2361">다음 캐비닛이 필요합니다.</String>
   <String Id="IDS_ERROR_2362">폴더를 찾을 수 없습니다. [2].</String>
   <String Id="IDS_ERROR_2363">폴더의 하위 폴더를 열거할 수 없습니다. [2].</String>
   <String Id="IDS_ERROR_2364">CreateCopier 호출에 잘못된 열거형 상수가 있습니다.</String>
   <String Id="IDS_ERROR_2365">실행 파일 [2]에 대해 BindImage를 수행할 수 없습니다.</String>
   <String Id="IDS_ERROR_2366">사용자 오류입니다.</String>
   <String Id="IDS_ERROR_2367">사용자가 중단했습니다.</String>
   <String Id="IDS_ERROR_2368">네트워크 리소스 정보를 가져오지 못했습니다. 오류 [2], 네트워크 경로 [3]. 확장 오류: 네트워크 공급자 [5], 오류 코드 [4], 오류 설명 [6].</String>
   <String Id="IDS_ERROR_2370">[2] 파일에 대한 CRC 체크섬 값이 잘못되었습니다.{ 헤더에는 체크섬 값이 [3](으)로 나타나며 계산 값은 [4]입니다.}</String>
   <String Id="IDS_ERROR_2371">[2] 파일에 패치를 적용할 수 없습니다. GetLastError: [3].</String>
   <String Id="IDS_ERROR_2372">[2] 패치 파일이 손상되었거나 형식이 잘못되었습니다. [3] 파일을 패치하는 중입니다. GetLastError: [4].</String>
   <String Id="IDS_ERROR_2373">[2] 파일이 올바른 패치 파일이 아닙니다.</String>
   <String Id="IDS_ERROR_2374">[2] 파일이 패치 파일 [3]의 올바른 대상 파일이 아닙니다.</String>
   <String Id="IDS_ERROR_2375">알 수 없는 패치 오류: [2].</String>
   <String Id="IDS_ERROR_2376">캐비닛을 찾을 수 없습니다.</String>
   <String Id="IDS_ERROR_2379">읽기 위해 파일을 여는 동안 오류가 발생했습니다. [3] GetLastError: [2].</String>
   <String Id="IDS_ERROR_2380">쓰기 위해 파일을 여는 동안 오류가 발생했습니다. [3]. GetLastError: [2].</String>
   <String Id="IDS_ERROR_2381">디렉토리가 없습니다. [2].</String>
   <String Id="IDS_ERROR_2382">드라이브가 준비되지 않았습니다. [2].</String>
   <String Id="IDS_ERROR_2401">32비트 운영 체제에서 [2] 키에 대해 64비트 레지스트리 작업이 시도되었습니다.</String>
   <String Id="IDS_ERROR_2402">메모리가 부족합니다.</String>
   <String Id="IDS_ERROR_2501">롤백 스크립트 열거자를 생성할 수 없습니다.</String>
   <String Id="IDS_ERROR_2502">설치가 진행되고 있지 않을 때 InstallFinalize를 호출했습니다.</String>
   <String Id="IDS_ERROR_2503">진행 중으로 표시되지 않은 RunScript를 호출했습니다.</String>
   <String Id="IDS_ERROR_2601">[2] 속성 값이 잘못되었습니다. '[3]'</String>
   <String Id="IDS_ERROR_2602">미디어 테이블에 [2] 테이블 항목 '[3]'과(와) 관련된 항목이 없습니다.</String>
   <String Id="IDS_ERROR_2603">테이블 이름 [2]이(가) 중복되었습니다.</String>
   <String Id="IDS_ERROR_2604">[2] 속성이 정의되지 않았습니다.</String>
   <String Id="IDS_ERROR_2605">[3] 또는 [4]에서 [2] 서버를 찾을 수 없습니다.</String>
   <String Id="IDS_ERROR_2606">[2] 속성 값이 올바른 전체 경로가 아닙니다. '[3]'.</String>
   <String Id="IDS_ERROR_2607">파일 설치에 필요한 미디어 테이블이 없거나 비어 있습니다.</String>
   <String Id="IDS_ERROR_2608">개체에 대한 보안 설명자를 생성할 수 없습니다. 오류: '[2]'.</String>
   <String Id="IDS_ERROR_2609">초기화하기 전에 제품 설정을 마이그레이션하려고 합니다.</String>
   <String Id="IDS_ERROR_2611">[2] 파일이 압축된 것으로 표시되어 있지만 관련된 미디어 항목이 캐비닛을 지정하지 않습니다.</String>
   <String Id="IDS_ERROR_2612">'[2]'열에 스트림이 없습니다. 기본 키: '[3]'.</String>
   <String Id="IDS_ERROR_2613">RemoveExistingProducts 작업 순서가 잘못되었습니다.</String>
   <String Id="IDS_ERROR_2614">설치 패키지에서 IStorage 개체에 액세스할 수 없습니다.</String>
   <String Id="IDS_ERROR_2615">소스 확인 오류로 인해 [2] 모듈의 등록 취소를 건너뛰었습니다.</String>
   <String Id="IDS_ERROR_2616">수반되는 파일 [2]에 상위 항목이 없습니다.</String>
   <String Id="IDS_ERROR_2617">공유 구성 요소 [2]을(를) 구성 요소 테이블에서 찾을 수 없습니다.</String>
   <String Id="IDS_ERROR_2618">격리된 애플리케이션 구성 요소 [2]을(를) 구성 요소 테이블에서 찾을 수 없습니다.</String>
   <String Id="IDS_ERROR_2619">격리된 구성 요소 [2], [3]이(가) 동일한 기능에 속하지 않습니다.</String>
   <String Id="IDS_ERROR_2620">격리된 애플리케이션 구성 요소 [2]의 키 파일이 파일 테이블에 없습니다.</String>
   <String Id="IDS_ERROR_2621">바로 가기 [2]의 리소스 DLL 또는 리소스 ID 정보가 잘못 설정되었습니다.</String>
   <String Id="IDS_ERROR_2701">기능의 깊이가 허용되는 트리 깊이인 [2] 수준을 초과합니다.</String>
   <String Id="IDS_ERROR_2702">기능 테이블 레코드([2])가 특성 필드에 존재하지 않는 상위 항목을 참조합니다.</String>
   <String Id="IDS_ERROR_2703">루트 소스 경로에 대한 속성 이름이 정의되지 않았습니다. [2]</String>
   <String Id="IDS_ERROR_2704">루트 디렉토리 속성이 정의되어 있지 않습니다. [2]</String>
   <String Id="IDS_ERROR_2705">잘못된 테이블: [2]. 트리로 연결할 수 없습니다.</String>
   <String Id="IDS_ERROR_2706">소스 경로가 생성되지 않았습니다. 디렉토리 테이블에 [2] 항목에 대한 경로가 없습니다.</String>
   <String Id="IDS_ERROR_2707">대상 경로가 생성되지 않았습니다. 디렉토리 테이블에 [2] 항목에 대한 경로가 없습니다.</String>
   <String Id="IDS_ERROR_2708">파일 테이블에 항목이 없습니다.</String>
   <String Id="IDS_ERROR_2709">지정된 구성 요소 이름('[2]')을 구성 요소 테이블에서 찾을 수 없습니다.</String>
   <String Id="IDS_ERROR_2710">요청된 'Select' 상태는 이 구성 요소에 적합하지 않습니다.</String>
   <String Id="IDS_ERROR_2711">지정된 기능 이름('[2]')을 기능 테이블에서 찾을 수 없습니다.</String>
   <String Id="IDS_ERROR_2712">[2] 작업에서 모덜리스 대화상자 [3](으)로부터 잘못된 값이 반환되었습니다.</String>
   <String Id="IDS_ERROR_2713">null을 허용하지 않는 열에 null 값이 있습니다('[4]' 테이블, '[3]'열의 '[2]').</String>
   <String Id="IDS_ERROR_2714">기본 폴더 이름에 대한 값이 잘못되었습니다. [2].</String>
   <String Id="IDS_ERROR_2715">지정된 파일 키('[2]')를 파일 테이블에서 찾을 수 없습니다.</String>
   <String Id="IDS_ERROR_2716">구성 요소 '[2]'에 대해 임의 하위 구성 요소 이름을 생성할 수 없습니다.</String>
   <String Id="IDS_ERROR_2717">작업 조건이 잘못되었거나 사용자 지정 작업 '[2]'을(를) 호출하는 동안 오류가 발생했습니다.</String>
   <String Id="IDS_ERROR_2718">제품 코드 '[2]'의 패키지 이름이 누락되었습니다.</String>
   <String Id="IDS_ERROR_2719">'[2]' 소스에 UNC와 드라이브 문자 경로가 둘 다 없습니다.</String>
   <String Id="IDS_ERROR_2720">소스 목록 키를 여는 동안 오류가 발생했습니다. 오류: '[2]'</String>
   <String Id="IDS_ERROR_2721">사용자 지정 작업 [2]을(를) 이진 테이블 스트림에서 찾을 수 없습니다.</String>
   <String Id="IDS_ERROR_2722">사용자 지정 작업 [2]을(를) 파일 테이블에서 찾을 수 없습니다.</String>
   <String Id="IDS_ERROR_2723">사용자 지정 작업 [2]이(가) 지원되지 않는 유형을 지정합니다.</String>
   <String Id="IDS_ERROR_2724">실행 중인 미디어의 볼륨 레이블 '[2]'이(가) 미디어 테이블에 지정된 레이블 '[3]'과(와) 일치하지 않습니다. 이러한 상황은 미디어 테이블에 항목이 하나만 있을 때만 허용됩니다.</String>
   <String Id="IDS_ERROR_2725">잘못된 데이터베이스 테이블</String>
   <String Id="IDS_ERROR_2726">작업을 찾을 수 없습니다. [2].</String>
   <String Id="IDS_ERROR_2727">디렉토리 테이블에 디렉토리 항목 '[2]'이(가) 없습니다.</String>
   <String Id="IDS_ERROR_2728">테이블 정의 오류: [2]</String>
   <String Id="IDS_ERROR_2729">설치 엔진이 초기화되지 않았습니다.</String>
   <String Id="IDS_ERROR_2730">데이터베이스에 잘못된 값이 있습니다. 테이블: '[2]', 기본 키: '[3]', 열: '[4]'</String>
   <String Id="IDS_ERROR_2731">선택 관리자가 초기화되지 않았습니다.</String>
   <String Id="IDS_ERROR_2732">디렉토리 관리자가 초기화되지 않았습니다.</String>
   <String Id="IDS_ERROR_2733">'[4]' 테이블의 '[3]'열에 잘못된 외래 키('[2]')가 있습니다.</String>
   <String Id="IDS_ERROR_2734">다시 설치 모드 문자가 잘못되었습니다.</String>
   <String Id="IDS_ERROR_2735">사용자 지정 작업 '[2]'이(가) 처리되지 않은 예외를 발생시키고 중지되었습니다. 사용자 지정 작업에 액세스 위반과 같은 내부 오류가 발생했기 때문일 수 있습니다.</String>
   <String Id="IDS_ERROR_2736">사용자 지정 작업 임시 파일을 생성하지 못했습니다. [2].</String>
   <String Id="IDS_ERROR_2737">사용자 지정 작업 [2], 항목 [3], 라이브러리 [4]에 액세스할 수 없음</String>
   <String Id="IDS_ERROR_2738">사용자 지정 작업 [2]에 대한 VBScript 런타임에 액세스할 수 없습니다.</String>
   <String Id="IDS_ERROR_2739">사용자 지정 작업 [2]에 대한 JavaScript 런타임에 액세스할 수 없습니다.</String>
   <String Id="IDS_ERROR_2740">사용자 지정 작업 [2] 스크립트 오류 [3], [4]: [5] [6]행, [7]열, [8].</String>
   <String Id="IDS_ERROR_2741">[2] 제품에 대한 구성 정보가 손상되었습니다. 잘못된 정보: [2].</String>
   <String Id="IDS_ERROR_2742">서버로 마샬링하지 못했습니다. [2].</String>
   <String Id="IDS_ERROR_2743">사용자 지정 작업 [2]을(를) 실행할 수 없습니다. 위치: [3], 명령: [4].</String>
   <String Id="IDS_ERROR_2744">사용자 지정 작업 [2]에서 EXE를 호출하지 못했습니다. 위치: [3], 명령: [4].</String>
   <String Id="IDS_ERROR_2745">[2] 변환이 [3] 패키지에 적합하지 않습니다. 언어가 [4]이어야 하는데 [5]입니다.</String>
   <String Id="IDS_ERROR_2746">[2] 변환이 [3] 패키지에 적합하지 않습니다. 제품이 [4]이어야 하는데 [5]입니다.</String>
   <String Id="IDS_ERROR_2747">[2] 변환이 [3] 패키지에 적합하지 않습니다. 제품 버전이 [4]보다 이전 버전이어야 하는데 [5]입니다.</String>
   <String Id="IDS_ERROR_2748">[2] 변환이 [3] 패키지에 적합하지 않습니다. 제품 버전이 [4]이거나 그 이전 버전이어야 하는데 [5]입니다.</String>
   <String Id="IDS_ERROR_2749">[2] 변환이 [3] 패키지에 적합하지 않습니다. 제품 버전이 [4]이어야 하는데 [5]입니다.</String>
   <String Id="IDS_ERROR_2750">[2] 변환이 [3] 패키지에 적합하지 않습니다. 제품 버전이 [4]이거나 그 이후 버전이어야 하는데 [5]입니다.</String>
   <String Id="IDS_ERROR_2751">[2] 변환이 [3] 패키지에 적합하지 않습니다. 제품 버전이 [4]보다 이후 버전이어야 하는데 [5]입니다.</String>
   <String Id="IDS_ERROR_2752">[4] 패키지의 하위 스토리지로 저장된 [2] 변환을 열 수 없습니다.</String>
   <String Id="IDS_ERROR_2753">'[2]' 파일이 설치용으로 표시되지 않았습니다.</String>
   <String Id="IDS_ERROR_2754">'[2]' 파일이 올바른 패치 파일이 아닙니다.</String>
   <String Id="IDS_ERROR_2755">[3] 패키지를 설치하는 동안 서버에서 예기치 않은 오류 [2]을(를) 반환했습니다.</String>
   <String Id="IDS_ERROR_2756">'[2]' 속성이 하나 이상의 테이블에서 디렉토리 속성으로 사용되었지만 값이 할당되지 않았습니다.</String>
   <String Id="IDS_ERROR_2757">[2] 변환에 대한 요약 정보를 생성할 수 없습니다.</String>
   <String Id="IDS_ERROR_2758">[2] 변환에 MSI 버전이 없습니다.</String>
   <String Id="IDS_ERROR_2759">[2] 변환 [3] 버전이 엔진과 호환되지 않습니다. 최소: [4], 최대: [5].</String>
   <String Id="IDS_ERROR_2760">[2] 변환이 [3] 패키지에 적합하지 않습니다. 업그레이드 코드가 [4]이어야 하는데 [5]입니다.</String>
   <String Id="IDS_ERROR_2761">트랜잭션을 시작할 수 없습니다. 전역 뮤텍스가 제대로 초기화되지 않았습니다.</String>
   <String Id="IDS_ERROR_2762">스크립트 레코드를 쓸 수 없습니다. 트랜잭션이 시작되지 않았습니다.</String>
   <String Id="IDS_ERROR_2763">스크립트를 실행할 수 없습니다. 트랜잭션이 시작되지 않았습니다.</String>
   <String Id="IDS_ERROR_2765">AssemblyName 테이블에 어셈블리 이름이 없습니다. 구성 요소: [4].</String>
   <String Id="IDS_ERROR_2766">[2] 파일은 잘못된 MSI 스토리지 파일입니다.</String>
   <String Id="IDS_ERROR_2767">추가 데이터가 없습니다{[2] 열거 시}.</String>
   <String Id="IDS_ERROR_2768">패치 패키지의 변환이 잘못되었습니다.</String>
   <String Id="IDS_ERROR_2769">사용자 지정 작업 [2]이(가) [3]개의 MSIHANDLE을 닫지 않았습니다.</String>
   <String Id="IDS_ERROR_2770">캐시된 폴더 [2]이(가) 내부 캐시 폴더 테이블에 정의되지 않았습니다.</String>
   <String Id="IDS_ERROR_2771">[2] 기능의 업그레이드에 구성 요소가 누락되었습니다.</String>
   <String Id="IDS_ERROR_2772">새로운 업그레이드 기능 [2]은(는) 리프 기능이어야 합니다.</String>
   <String Id="IDS_ERROR_2801">알 수 없는 메시지 -- [2] 유형. 아무 작업도 수행되지 않습니다.</String>
   <String Id="IDS_ERROR_2802">[2] 이벤트에 대한 게시자를 찾을 수 없습니다.</String>
   <String Id="IDS_ERROR_2803">대화상자 보기가 [2] 대화상자에 대한 레코드를 찾지 못했습니다.</String>
   <String Id="IDS_ERROR_2804">[2] 대화상자에서 [3] 컨트롤 활성화 시 CMsiDialog가 [3] 조건을 평가하지 못했습니다.</String>
   <String Id="IDS_ERROR_2806">[2] 대화상자가 [3] 조건을 평가하지 못했습니다.</String>
   <String Id="IDS_ERROR_2807">[2] 작업이 인식되지 않았습니다.</String>
   <String Id="IDS_ERROR_2808">[2] 대화상자의 기본 단추가 잘못 정의되었습니다.</String>
   <String Id="IDS_ERROR_2809">[2] 대화상자에서 다음 컨트롤 포인터가 순환을 형성하지 않습니다. [3]에서 [4](으)로의 포인터가 있지만 추가 포인터는 없습니다.</String>
   <String Id="IDS_ERROR_2810">[2] 대화상자에서 다음 컨트롤 포인터가 순환을 형성하지 않습니다. [3]과(와) [5] 둘 다에서 [4](으)로의 포인터가 있습니다.</String>
   <String Id="IDS_ERROR_2811">[2] 대화상자에서 [3] 컨트롤에 포커스를 두어야 하는데 가능하지 않습니다.</String>
   <String Id="IDS_ERROR_2812">[2] 이벤트를 인식할 수 없습니다.</String>
   <String Id="IDS_ERROR_2813">[2] 인수를 사용하여 EndDialog 이벤트가 호출되었지만 해당 대화상자에 상위 항목이 있습니다.</String>
   <String Id="IDS_ERROR_2814">[2] 대화상자에서 [3] 컨트롤이 존재하지 않는 컨트롤 [4]을(를) 다음 컨트롤로 지정합니다.</String>
   <String Id="IDS_ERROR_2815">ControlCondition 테이블에 [2] 대화상자에 대한 조건 없는 행이 있습니다.</String>
   <String Id="IDS_ERROR_2816">EventMapping 테이블이 [3] 이벤트에 대해 [2] 대화상자의 잘못된 컨트롤 [4]을(를) 참조합니다.</String>
   <String Id="IDS_ERROR_2817">[2] 이벤트가 [3] 대화상자의 [4] 컨트롤에 대한 특성을 설정하지 못했습니다.</String>
   <String Id="IDS_ERROR_2818">ControlEvent 테이블의 EndDialog에 인식할 수 없는 인수 [2]이(가) 있습니다.</String>
   <String Id="IDS_ERROR_2819">[2] 대화상자의 [3] 컨트롤에 속성이 연결되어야 합니다.</String>
   <String Id="IDS_ERROR_2820">이미 초기화된 처리기를 초기화하려고 했습니다.</String>
   <String Id="IDS_ERROR_2821">이미 초기화된 대화상자를 초기화하려고 했습니다. [2].</String>
   <String Id="IDS_ERROR_2822">모든 컨트롤을 추가할 때까지 [2] 대화상자에 대해 다른 메소드를 호출할 수 없습니다.</String>
   <String Id="IDS_ERROR_2823">이미 초기화된 컨트롤을 초기화하려고 했습니다. [2] 대화상자에 [3]이(가) 있습니다.</String>
   <String Id="IDS_ERROR_2824">대화상자 특성 [3]에는 [2]개 이상의 필드 레코드가 필요합니다.</String>
   <String Id="IDS_ERROR_2825">컨트롤 특성 [3]에는 [2]개 이상의 필드 레코드가 필요합니다.</String>
   <String Id="IDS_ERROR_2826">[2] 대화상자의 [3] 컨트롤이 [4] 대화상자의 경계에서 [5]픽셀만큼 벗어났습니다.</String>
   <String Id="IDS_ERROR_2827">[2] 대화상자의 라디오 단추 그룹 [3]에 포함된 [4] 단추가 [5] 그룹의 경계에서 [6]픽셀만큼 벗어났습니다.</String>
   <String Id="IDS_ERROR_2828">[2] 대화상자에서 [3] 컨트롤을 제거하려고 했으나 해당 컨트롤이 대화상자에 속하지 않습니다.</String>
   <String Id="IDS_ERROR_2829">초기화되지 않은 대화상자를 사용하려고 합니다.</String>
   <String Id="IDS_ERROR_2830">[2] 대화상자에서 초기화되지 않은 컨트롤을 사용하려고 합니다.</String>
   <String Id="IDS_ERROR_2831">[2] 대화상자의 [3] 컨트롤이 [5] 특성 [4]을(를) 지원하지 않습니다.</String>
   <String Id="IDS_ERROR_2832">[2] 대화상자가 [3] 특성을 지원하지 않습니다.</String>
   <String Id="IDS_ERROR_2833">[3] 대화상자의 [4] 컨트롤이 [2] 메시지를 무시했습니다.</String>
   <String Id="IDS_ERROR_2834">[2] 대화상자의 다음 포인터가 단일 루프를 형성하지 않습니다.</String>
   <String Id="IDS_ERROR_2835">[2] 컨트롤이 [3] 대화상자에 없습니다.</String>
   <String Id="IDS_ERROR_2836">[2] 대화상자의 [3] 컨트롤에 포커스를 둘 수 없습니다.</String>
   <String Id="IDS_ERROR_2837">[2] 대화상자에 있는 [3] 컨트롤의 경우 winproc가 [4]을(를) 반환해야 합니다.</String>
   <String Id="IDS_ERROR_2838">선택 테이블의 [2] 항목에 대한 상위 항목이 자기 자신입니다.</String>
   <String Id="IDS_ERROR_2839">[2] 속성을 설정하지 못했습니다.</String>
   <String Id="IDS_ERROR_2840">대화상자 이름 불일치 오류입니다.</String>
   <String Id="IDS_ERROR_2841">오류 대화상자에 확인 단추가 없습니다.</String>
   <String Id="IDS_ERROR_2842">오류 대화상자에 텍스트 필드가 없습니다.</String>
   <String Id="IDS_ERROR_2843">표준 대화상자에는 ErrorString 특성을 사용할 수 없습니다.</String>
   <String Id="IDS_ERROR_2844">Errorstring이 설정되지 않은 경우 오류 대화상자를 실행할 수 없습니다.</String>
   <String Id="IDS_ERROR_2845">단추의 전체 너비가 오류 대화상자의 크기를 초과합니다.</String>
   <String Id="IDS_ERROR_2846">SetFocus가 오류 대화상자에서 필요한 컨트롤을 찾지 못했습니다.</String>
   <String Id="IDS_ERROR_2847">[2] 대화상자의 [3] 컨트롤에 아이콘과 비트맵 스타일이 모두 설정되어 있습니다.</String>
   <String Id="IDS_ERROR_2848">[2] 대화상자에서 기본 단추로 설정하려는 [3] 컨트롤이 존재하지 않습니다.</String>
   <String Id="IDS_ERROR_2849">[2] 대화상자의 [3] 컨트롤이 정수 값을 지정할 수 없는 유형입니다.</String>
   <String Id="IDS_ERROR_2850">볼륨 유형을 인식할 수 없습니다.</String>
   <String Id="IDS_ERROR_2851">[2] 아이콘에 대한 데이터가 잘못되었습니다.</String>
   <String Id="IDS_ERROR_2852">[2] 대화상자를 사용하려면 먼저 컨트롤을 하나 이상 추가해야 합니다.</String>
   <String Id="IDS_ERROR_2853">[2] 대화상자는 모덜리스 대화상자이므로 execute 메소드를 호출하면 안 됩니다.</String>
   <String Id="IDS_ERROR_2854">[2] 대화상자에 첫 번째 활성 컨트롤로 지정된 [3] 컨트롤이 없습니다.</String>
   <String Id="IDS_ERROR_2855">[2] 대화상자의 라디오 단추 그룹 [3]에 있는 단추가 두 개 미만입니다.</String>
   <String Id="IDS_ERROR_2856">[2] 대화상자의 다른 복사본을 생성하고 있습니다.</String>
   <String Id="IDS_ERROR_2857">선택 테이블에 지정된 [2] 디렉토리를 찾을 수 없습니다.</String>
   <String Id="IDS_ERROR_2858">[2] 비트맵의 데이터가 잘못되었습니다.</String>
   <String Id="IDS_ERROR_2859">테스트 오류 메시지입니다.</String>
   <String Id="IDS_ERROR_2860">[2] 대화상자의 취소 단추가 잘못 정의되었습니다.</String>
   <String Id="IDS_ERROR_2861">[2] 대화상자 [3] 컨트롤의 라디오 단추에 대한 다음 포인터가 순환을 형성하지 않습니다.</String>
   <String Id="IDS_ERROR_2862">[2] 대화상자의 [3] 컨트롤에 대한 특성이 유효한 아이콘 크기를 정의하지 않습니다. 크기를 16으로 설정합니다.</String>
   <String Id="IDS_ERROR_2863">[2] 대화상자의 [3] 컨트롤에는 크기가 [5]x[5]인 [4] 아이콘이 필요한데 이 크기를 사용할 수 없습니다. 사용 가능한 첫 번째 크기를 로드합니다.</String>
   <String Id="IDS_ERROR_2864">[2] 대화상자 [3] 컨트롤이 찾아보기 이벤트를 받았으나 현재 선택 항목에 대해 구성 가능한 디렉토리가 없습니다. 가능한 원인: 찾아보기 단추가 제대로 작성되지 않았습니다.</String>
   <String Id="IDS_ERROR_2865">[2] 빌보드의 [3] 컨트롤이 [4] 빌보드의 경계에서 [5]픽셀만큼 벗어났습니다.</String>
   <String Id="IDS_ERROR_2866">[2] 대화상자는 [3] 인수를 반환할 수 없습니다.</String>
   <String Id="IDS_ERROR_2867">오류 대화상자 속성이 설정되지 않았습니다.</String>
   <String Id="IDS_ERROR_2868">오류 대화상자 [2]에 오류 스타일 비트가 설정되지 않았습니다.</String>
   <String Id="IDS_ERROR_2869">[2] 대화상자에 오류 스타일 비트가 설정되었으나 오류 대화상자가 아닙니다.</String>
   <String Id="IDS_ERROR_2870">[2] 대화상자의 [3] 컨트롤에 대한 도움말 문자열 [4]에 구분 문자가 없습니다.</String>
   <String Id="IDS_ERROR_2871">[2] 테이블이 최신이 아닙니다. [3].</String>
   <String Id="IDS_ERROR_2872">[2] 대화상자의 CheckPath 컨트롤 이벤트 인수가 잘못되었습니다.</String>
   <String Id="IDS_ERROR_2873">[2] 대화상자의 [3] 컨트롤에 잘못된 문자열 길이 제한이 지정되었습니다. [4].</String>
   <String Id="IDS_ERROR_2874">텍스트 글꼴을 [2](으)로 변경하지 못했습니다.</String>
   <String Id="IDS_ERROR_2875">텍스트 색을 [2](으)로 변경하지 못했습니다.</String>
   <String Id="IDS_ERROR_2876">[2] 대화상자의 [3] 컨트롤에서 문자열을 잘라내야 합니다. [4].</String>
   <String Id="IDS_ERROR_2877">이진 데이터 [2]을(를) 찾을 수 없음</String>
   <String Id="IDS_ERROR_2878">[2] 대화상자 [3] 컨트롤에 다음 값이 지정된 것 같습니다. [4]. 이 값은 잘못되었거나 중복된 값입니다.</String>
   <String Id="IDS_ERROR_2879">[2] 대화상자의 [3] 컨트롤에서 마스크 문자열을 구문 분석할 수 없습니다. [4].</String>
   <String Id="IDS_ERROR_2880">나머지 컨트롤 이벤트는 수행하지 마십시오.</String>
   <String Id="IDS_ERROR_2881">CMsiHandler를 초기화하지 못했습니다.</String>
   <String Id="IDS_ERROR_2882">대화상자 창 클래스를 등록하지 못했습니다.</String>
   <String Id="IDS_ERROR_2883">[2] 대화상자에 대한 CreateNewDialog가 실패했습니다.</String>
   <String Id="IDS_ERROR_2884">[2] 대화상자에 대한 창을 생성하지 못했습니다.</String>
   <String Id="IDS_ERROR_2885">[2] 대화상자에서 [3] 컨트롤을 생성하지 못했습니다.</String>
   <String Id="IDS_ERROR_2886">[2] 테이블을 생성하지 못했습니다.</String>
   <String Id="IDS_ERROR_2887">[2] 테이블에 대한 커서를 생성하지 못했습니다.</String>
   <String Id="IDS_ERROR_2888">[2] 보기를 실행하지 못했습니다.</String>
   <String Id="IDS_ERROR_2889">[2] 대화상자의 [3] 컨트롤에 대한 창을 생성하지 못했습니다.</String>
   <String Id="IDS_ERROR_2890">처리기가 초기화된 대화상자를 생성하지 못했습니다.</String>
   <String Id="IDS_ERROR_2891">[2] 대화상자에 대한 창을 삭제하지 못했습니다.</String>
   <String Id="IDS_ERROR_2892">[2]은(는) 정수만 사용할 수 있는 컨트롤인데 [3]은(는) 올바른 정수 값이 아닙니다.</String>
   <String Id="IDS_ERROR_2893">[2] 대화상자의 [3] 컨트롤에는 [5]자 미만의 속성 값을 사용할 수 있습니다. 값 [4]은(는) 이 제한을 초과하기 때문에 잘렸습니다.</String>
   <String Id="IDS_ERROR_2894">RICHED20.DLL을 로드하지 못했습니다. GetLastError()에서 다음을 반환했습니다. [2].</String>
   <String Id="IDS_ERROR_2895">RICHED20.DLL을 해제하지 못했습니다. GetLastError()에서 다음을 반환했습니다. [2].</String>
   <String Id="IDS_ERROR_2896">[2] 작업을 실행하지 못했습니다.</String>
   <String Id="IDS_ERROR_2897">이 시스템에 [2] 글꼴을 생성하지 못했습니다.</String>
   <String Id="IDS_ERROR_2898">[2] 텍스트 스타일에 대해 시스템은 [4] 문자 세트에 '[3]' 글꼴을 생성했습니다.</String>
   <String Id="IDS_ERROR_2899">[2] 텍스트 스타일을 생성하지 못했습니다. GetLastError()에서 다음을 반환했습니다. [3].</String>
   <String Id="IDS_ERROR_2901">[2] 작업에 대한 매개 변수가 잘못되었습니다. 매개 변수 [3].</String>
   <String Id="IDS_ERROR_2902">[2] 작업의 호출 순서가 잘못되었습니다.</String>
   <String Id="IDS_ERROR_2903">[2] 파일이 없습니다.</String>
   <String Id="IDS_ERROR_2904">[2] 파일에 대해 BindImage를 수행할 수 없습니다.</String>
   <String Id="IDS_ERROR_2905">스크립트 파일 [2]에서 레코드를 읽을 수 없습니다.</String>
   <String Id="IDS_ERROR_2906">스크립트 파일 [2]에 헤더가 없습니다.</String>
   <String Id="IDS_ERROR_2907">안전한 보안 설명자를 생성할 수 없습니다. 오류: [2].</String>
   <String Id="IDS_ERROR_2908">[2] 구성 요소를 등록할 수 없습니다.</String>
   <String Id="IDS_ERROR_2909">[2] 구성 요소의 등록을 취소하지 못했습니다.</String>
   <String Id="IDS_ERROR_2910">사용자의 보안 ID를 확인할 수 없습니다.</String>
   <String Id="IDS_ERROR_2911">[2] 폴더를 제거할 수 없습니다.</String>
   <String Id="IDS_ERROR_2912">재시작 시 [2] 파일을 제거하도록 스케줄링할 수 없습니다.</String>
   <String Id="IDS_ERROR_2919">압축된 파일에 대해 캐비닛이 지정되지 않았습니다. [2].</String>
   <String Id="IDS_ERROR_2920">[2] 파일에 대해 소스 디렉토리가 지정되지 않았습니다.</String>
   <String Id="IDS_ERROR_2924">스크립트 [2] 버전이 지원되지 않습니다. 스크립트 버전: [3], 최소 버전: [4], 최대 버전: [5].</String>
   <String Id="IDS_ERROR_2927">ShellFolder ID [2]이(가) 잘못되었습니다.</String>
   <String Id="IDS_ERROR_2928">소스의 최대 수를 초과했습니다. '[2]' 소스를 건너뜁니다.</String>
   <String Id="IDS_ERROR_2929">게시 루트를 확인할 수 없습니다. 오류: [2].</String>
   <String Id="IDS_ERROR_2932">스크립트 데이터에서 [2] 파일을 생성할 수 없습니다. 오류: [3].</String>
   <String Id="IDS_ERROR_2933">롤백 스크립트 [2]을(를) 초기화할 수 없습니다.</String>
   <String Id="IDS_ERROR_2934">[2] 변환의 보안을 유지할 수 없습니다. 오류 [3].</String>
   <String Id="IDS_ERROR_2935">[2] 변환의 보안을 해제할 수 없습니다. 오류 [3].</String>
   <String Id="IDS_ERROR_2936">[2] 변환을 찾을 수 없습니다.</String>
   <String Id="IDS_ERROR_2937">시스템 파일 보호 카탈로그를 설치할 수 없습니다. 카탈로그: [2], 오류: [3].</String>
   <String Id="IDS_ERROR_2938">캐시에서 시스템 파일 보호 카탈로그를 검색할 수 없습니다. 카탈로그: [2], 오류: [3].</String>
   <String Id="IDS_ERROR_2939">캐시에서 시스템 파일 보호 카탈로그를 삭제할 수 없습니다. 카탈로그: [2], 오류: [3].</String>
   <String Id="IDS_ERROR_2940">소스 확인을 위한 디렉토리 관리자가 제공되지 않았습니다.</String>
   <String Id="IDS_ERROR_2941">[2] 파일의 CRC를 계산할 수 없습니다.</String>
   <String Id="IDS_ERROR_2942">[2] 파일에 대해 BindImage 작업이 실행되지 않았습니다.</String>
   <String Id="IDS_ERROR_2943">이 버전의 Windows는 64비트 패키지의 배포를 지원하지 않습니다. [2] 스크립트는 64비트 패키지용입니다.</String>
   <String Id="IDS_ERROR_2944">GetProductAssignmentType을 실패했습니다.</String>
   <String Id="IDS_ERROR_2945">오류 [3](으)로 인해 ComPlus 애플리케이션 [2]을(를) 설치하지 못했습니다.</String>
   <String Id="IDS_ERROR_3001">이 목록의 패치에 잘못된 배열 정보가 있습니다. [2][3][4][5][6][7][8][9][10][11][12][13][14][15][16].</String>
   <String Id="IDS_ERROR_3002">패치 [2]에 잘못된 배열 정보가 있습니다. </String>
   <String Id="IDS_ERROR_25032">설치 관리자가 LSI 드라이버를 설치하지 못했습니다.</String>
   <String Id="IDS_ERROR_25520">[3]\[4]에 대한 보안 설명자를 생성하지 못함, 시스템 오류: [2]</String>
   <String Id="IDS_ERROR_25521">개체 [3]에 보안 설명자를 설정하지 못함, 시스템 오류: [2]</String>
   <String Id="IDS_ERROR_25522">알 수 없는 개체 유형 [3], 시스템 오류: [2]</String>
   <String Id="IDS_ERROR_27500">이 설치 프로그램이 IIS 가상 루트를 구성하려면 Internet Information Server 4.0 이상이 필요합니다. IIS 4.0 이상이 설치되어 있는지 확인하십시오.</String>
   <String Id="IDS_ERROR_27501">IIS 가상 루트를 구성하려면 관리자 권한이 있어야 합니다.</String>
   <String Id="IDS_ERROR_27502">[2] '[3]'에 연결할 수 없습니다. [4]</String>
   <String Id="IDS_ERROR_27503">[2] '[3]'에서 버전 문자열을 가져오는 동안 오류가 발생했습니다. [4]</String>
   <String Id="IDS_ERROR_27504">SQL 버전 요구 사항이 충족되지 않았습니다. [3]. 이 설치를 수행하려면 [2] [4] 이상이 필요합니다.</String>
   <String Id="IDS_ERROR_27505">SQL 스크립트 파일 [2]을(를) 열 수 없습니다.</String>
   <String Id="IDS_ERROR_27506">[2] SQL 스크립트([3]번째 줄)를 실행하는 동안 오류가 발생했습니다. [4]</String>
   <String Id="IDS_ERROR_27507">데이터베이스 서버에 연결하거나 데이터베이스 서버를 검색하려면 MDAC를 설치해야 합니다.</String>
   <String Id="IDS_ERROR_27508">COM+ 애플리케이션 [2]을(를) 설치하는 동안 오류가 발생했습니다. [3]</String>
   <String Id="IDS_ERROR_27509">COM+ 애플리케이션 [2]을(를) 제거하는 동안 오류가 발생했습니다. [3]</String>
   <String Id="IDS_ERROR_27510">COM+ 애플리케이션 [2]을(를) 설치하는 동안 오류가 발생했습니다. Microsoft(R) .NET 클래스 라이브러리를 로드할 수 없습니다. .NET 서비스 구성 요소를 등록하려면 Microsoft(R) .NET Framework가 설치되어 있어야 합니다.</String>
   <String Id="IDS_ERROR_27511">SQL 스크립트 파일 [2]을(를) 실행할 수 없습니다. 연결이 열려 있지 않습니다. [3]</String>
   <String Id="IDS_ERROR_27512">[2] '[3]'에 대한 트랜잭션을 시작하는 동안 오류가 발생했습니다. [4] 데이터베이스. [5]</String>
   <String Id="IDS_ERROR_27513">[2] '[3]'에 대한 트랜잭션을 커밋하는 동안 오류가 발생했습니다. [4] 데이터베이스. [5]</String>
   <String Id="IDS_ERROR_27514">이 설치를 수행하려면 Microsoft SQL Server가 필요합니다. 지정된 '[3]' 서버는 MSDE(Microsoft SQL Server Desktop Engine)입니다.</String>
   <String Id="IDS_ERROR_27515">[2] '[3]'에서 스키마 버전을 가져오는 동안 오류가 발생했습니다. 데이터베이스: '[4]'. [5]</String>
   <String Id="IDS_ERROR_27516">[2] '[3]'에 스키마 버전을 쓰는 동안 오류가 발생했습니다. 데이터베이스: '[4]'. [5]</String>
   <String Id="IDS_ERROR_27517">이 설치를 수행하려면 COM+ 애플리케이션을 설치하기 위한 관리자 권한이 필요합니다. 관리자로 로그온한 후 이 설치를 다시 시도하십시오.</String>
   <String Id="IDS_ERROR_27518">"[2]" COM+ 애플리케이션이 NT 서비스로 실행되도록 구성되어 있기 때문에 시스템에 COM+ 1.5 이상이 필요합니다. 사용 중인 시스템에는 COM+ 1.0이 설치되어 있으므로 이 애플리케이션은 설치되지 않습니다.</String>
   <String Id="IDS_ERROR_27519">[2] XML 파일을 업데이트하는 동안 오류가 발생했습니다. [3]</String>
   <String Id="IDS_ERROR_27520">[2] XML 파일을 여는 동안 오류가 발생했습니다. [3]</String>
   <String Id="IDS_ERROR_27521">이 설치 프로그램을 실행하려면 XML 파일을 구성하기 위해 MSXML 3.0 이상이 필요합니다. 버전 3.0 이상이 설치되어 있는지 확인하십시오.</String>
   <String Id="IDS_ERROR_27522">[2] XML 파일을 생성하는 동안 오류가 발생했습니다. [3]</String>
   <String Id="IDS_ERROR_27523">서버를 로드하는 동안 오류가 발생했습니다.</String>
   <String Id="IDS_ERROR_27524">NetApi32.DLL을 로드하는 동안 오류가 발생했습니다. ISNetApi.dll을 사용하려면 NetApi32.DLL이 올바르게 로드되어야 하며 NT 기반 운영 체제가 필요합니다.</String>
   <String Id="IDS_ERROR_27525">서버를 찾을 수 없습니다. 지정한 서버가 있는지 확인하십시오. 서버 이름은 비워둘 수 없습니다.</String>
   <String Id="IDS_ERROR_27526">ISNetApi.dll에서 지정되지 않은 오류가 발생했습니다.</String>
   <String Id="IDS_ERROR_27527">버퍼가 너무 작습니다.</String>
   <String Id="IDS_ERROR_27528">액세스가 거부되었습니다. 관리 권한을 확인하십시오.</String>
   <String Id="IDS_ERROR_27529">컴퓨터가 올바르지 않습니다.</String>
   <String Id="IDS_ERROR_27530">정의되지 않은 switch case입니다.</String>
   <String Id="IDS_ERROR_27531">처리되지 않은 예외입니다.</String>
   <String Id="IDS_ERROR_27532">이 서버 또는 도메인에 대해 사용자 이름이 올바르지 않습니다.</String>
   <String Id="IDS_ERROR_27533">암호의 대/소문자가 일치하지 않습니다.</String>
   <String Id="IDS_ERROR_27534">목록이 비어 있습니다.</String>
   <String Id="IDS_ERROR_27535">액세스 위반입니다.</String>
   <String Id="IDS_ERROR_27536">그룹을 가져오는 동안 오류가 발생했습니다.</String>
   <String Id="IDS_ERROR_27537">그룹에 사용자를 추가하는 동안 오류가 발생했습니다. 이 도메인 또는 서버에 대해 그룹이 있는지 확인하십시오.</String>
   <String Id="IDS_ERROR_27538">사용자를 생성하는 동안 오류가 발생했습니다.</String>
   <String Id="IDS_ERROR_27539">NetAPI에서 ERROR_NETAPI_ERROR_NOT_PRIMARY가 반환되었습니다.</String>
   <String Id="IDS_ERROR_27540">지정한 사용자가 이미 있습니다.</String>
   <String Id="IDS_ERROR_27541">지정한 그룹이 이미 있습니다.</String>
   <String Id="IDS_ERROR_27542">암호가 잘못되었습니다. 암호가 네트워크 암호 정책을 준수하는지 확인하십시오.</String>
   <String Id="IDS_ERROR_27543">잘못된 이름입니다.</String>
   <String Id="IDS_ERROR_27544">잘못된 그룹입니다.</String>
   <String Id="IDS_ERROR_27545">사용자 이름은 비워 둘 수 없으며 도메인\사용자 이름 형식으로 입력해야 합니다.</String>
   <String Id="IDS_ERROR_27546">사용자 임시 디렉토리에서 INI 파일을 로드하거나 생성하는 동안 오류가 발생했습니다.</String>
   <String Id="IDS_ERROR_27547">ISNetAPI.dll이 로드되지 않았거나 dll을 로드하는 동안 오류가 발생했습니다. 이 작업을 수행하려면 해당 dll을 로드해야 합니다. dll이 SUPPORTDIR 디렉토리에 있는지 확인하십시오.</String>
   <String Id="IDS_ERROR_27548">새로운 사용자 정보가 들어 있는 INI 파일을 사용자의 임시 디렉토리에서 삭제하는 동안 오류가 발생했습니다.</String>
   <String Id="IDS_ERROR_27549">PDC(Primary Domain Controller)를 가져오는 동안 오류가 발생했습니다.</String>
   <String Id="IDS_ERROR_27550">사용자를 생성하려면 모든 필드에 값을 입력해야 합니다.</String>
   <String Id="IDS_ERROR_27551">[2]에 대한 ODBC 드라이버를 찾을 수 없습니다. [2] 데이터베이스 서버에 연결하려면 이 드라이버가 필요합니다.</String>
   <String Id="IDS_ERROR_27552">[4] 데이터베이스를 생성하는 동안 오류가 발생했습니다. 서버: [2] [3]. [5]</String>
   <String Id="IDS_ERROR_27553">[4] 데이터베이스에 연결하는 동안 오류가 발생했습니다. 서버: [2] [3]. [5]</String>
   <String Id="IDS_ERROR_27554">[2] 연결을 여는 동안 오류가 발생했습니다. 이 연결과 연결된 유효한 데이터베이스 메타데이터가 없습니다.</String>
   <String Id="IDS_ERROR_28030">설치 관리자가 USB 드라이버를 설치하지 못했습니다. 성공적으로 설치하려면 시스템을 다시 시작하고 이 설치 관리자를 다시 시작하십시오.</String>
   <String Id="IDS_ERROR_28033">%s에 연결할 수 없습니다. TCP 포트 %d에서 TCP 연결 오류가 발생했습니다. 입력한 서버 이름을 확인하십시오.</String>
   <String Id="IDS_ERROR_28034">%s의 LDAP에 연결할 수 없습니다. 입력한 서버 이름을 확인하십시오.</String>
   <String Id="IDS_ERROR_28035">자격 증명이 잘못되었습니다. 지정된 서버의 관리자 사용자 이름 및 암호를 입력하십시오.</String>
   <String Id="IDS_ERROR_28036">%s의 LDAP에 바인딩하지 못했습니다. %s.</String>
   <String Id="IDS_ERROR_28037">%s의 LDAP를 업데이트하지 못했습니다. 액세스가 거부되었습니다. 지정된 서버에 대한 충분한 액세스 권한이 있는 관리자 사용자 이름 및 암호를 입력하십시오.</String>
   <String Id="IDS_ERROR_28038">%s의 LDAP를 업데이트하지 못했습니다. 스키마 위반이 발생했습니다. 지정된 서버가 이전 소프트웨어 버전을 실행하고 있기 때문에 이 에이전트 설치를 지원하기 위해서는 해당 서버를 업그레이드해야 할 수 있습니다.</String>
   <String Id="IDS_ERROR_28039">%s의 LDAP를 업데이트하지 못했습니다. %s.</String>
   <String Id="IDS_ERROR_28040">사용자 이름 형식이 잘못되었습니다.</String>
   <String Id="IDS_ERROR_28041">이 에이전트는 이미 %s에 등록되어 있습니다. 이 설치를 계속하고 기존 등록 정보를 업데이트하시겠습니까?</String>
   <String Id="IDS_ERROR_28042">이 에이전트는 %s에 여러 번 등록되었습니다. Horizon Administrator를 사용하여 이 시스템에 대한 항목을 제거하고 이 설치를 다시 시도하십시오.</String>
   <String Id="IDS_ERROR_28045">관리자 자격 증명을 지정할 경우 사용자 이름과 암호를 입력해야 합니다.</String>
   <String Id="IDS_ERROR_28046">%s의 LDAP에서 중요 보안 정보를 가져오지 못했습니다. 지정된 서버가 이전 소프트웨어 버전을 실행하고 있기 때문에 이 에이전트 설치를 지원하기 위해서는 해당 서버를 업그레이드해야 할 수 있습니다.</String>
   <String Id="IDS_ERROR_28053">DLL을 등록하지 못했습니다. 자세한 내용은 최근 %TEMP%\vminst*.log 파일을 참조하십시오.</String>
   <String Id="IDS_ERROR_28060">Intel HECI 디바이스 드라이버를 설치하는 동안 오류가 발생했습니다.</String>
   <String Id="IDS_ERROR_28062">이 컴퓨터의 에이전트가 이미 %s에 가상 시스템으로 등록되어 있습니다. 이 컴퓨터의 컴퓨터 이름을 변경하거나 연결 서버에서 Horizon Administrator를 사용하여 가상 시스템 항목을 제거하고 이 설치를 다시 시도하십시오.</String>
   <String Id="IDS_ERROR_28065">설치 관리자가 스마트 카드 리디렉터 드라이버를 설치하지 못했습니다.</String>
   <String Id="IDS_ERROR_28089">연결 서버 %s은(는) 이 에이전트를 완벽히 지원할 수 있는 소프트웨어 버전을 실행하고 있지 않습니다. 계속하려면 먼저 연결 서버를 업그레이드해야 합니다. 무시하고 계속할 경우 나중에 이 에이전트를 다시 설치해야만 전체 RDS 호스트 기능을 활용할 수 있습니다. 이 설치를 계속하시겠습니까?</String>
   <String Id="IDS_ERROR_28090">설치 프로그램에서 기본값이 아닌 레지스트리 값을 발견했습니다.

HKLM\SYSTEM\CurrentControlSet\Control\TerminalServer\WinStations\RDP-Tcp\MaxInstanceCount

HKLM\SYSTEM\CurrentControlSet\Control\TerminalServer\WinStations\Horizon-PCOIP\MaxInstanceCount

Horizon에서는 세션 제한을 관리하므로 이러한 레지스트리 설정을 사용하면 예기치 않은 동작이 발생합니다.</String>
   <String Id="IDS_ERROR_28092">설치 관리자가 Horizon Virtual Webcam Driver를 설치하지 못했습니다. 성공적으로 설치하려면 시스템을 다시 시작하고 이 설치 관리자를 다시 시작하십시오.</String>
   <String Id="IDS_ERROR_28096">USB 리디렉션을 안전하게 사용하는 방법에 대한 지침을 보려면 Horizon 보안 문서를 참조하십시오.</String>
   <String Id="IDS_ERROR_28100">일부 설치 관리자 확인을 사용하지 않도록 설정한 상태로 Omnissa Horizon Agent를 설치합니다. 이 방법은 권장되지 않으며 결과적으로 설치가 지원되지 않게 됩니다. 설치를 취소하고 확인을 사용하도록 설정한 상태로 설치 관리자를 다시 실행하십시오.</String>
   <String Id="IDS_ERROR_28109">VC%d.%d nonredist가 시스템에 설치되어 있지 않습니다. 계속하면 Horizon Agent 설치가 실패할 수 있습니다. msvc_debug_runtime_installer Conan build에서 VC%d.%d nonredist 설치 관리자를 다운로드할 수 있습니다.</String>
   <String Id="IDS_ERROR_28110">비즈니스용 Skype의 가상화 팩을 설치하려면 .NET Framework 4.0 이상이 있어야 합니다. .NET Framework를 설치하고 설치 관리자를 다시 실행하여 이 기능을 설치하십시오.</String>
   <String Id="IDS_ERROR_28111">계속하려면 기능을 추가 또는 제거하십시오.</String>
   <String Id="IDS_ERROR_28113">수정 설치 중에 "Instant Clone Agent"(NGVC) 기능을 변경할 수 없습니다. 복제 기능을 추가하거나 제거하려면 에이전트를 제거했다가 다시 설치하십시오.</String>
   <String Id="IDS_ERROR_28114">이 설치를 수정하거나 복구하려면 관리 권한이 있어야 합니다. 권한이 상승된 명령 프롬프트에서 다음 명령을 실행할 수도 있습니다.

msiexec.exe /i [DATABASE]</String>
   <String Id="IDS_ERROR_28115">이 패치를 설치하려면 관리자 권한이 있어야 합니다. 권한이 상승된 명령 프롬프트에서 다음 명령을 실행해야 할 수 있습니다.

msiexec.exe /p [PATCH]</String>

   <String Id="IDS_ERROR_28116">Print Spooler 서비스가 실행되고 있지 않습니다. Horizon Integrated Printing 기능이 설치되지 않았을 수 있습니다.</String>

   <!-- L10n properties for merge module services -->
   <String Id="IDS_PCOIPSG_DISPLAY_NAME">Omnissa Horizon PCoIP 보안 게이트웨이</String>
   <String Id="IDS_PCOIPSG_DESCRIPTION">PCoIP 게이트웨이 서비스를 제공합니다.</String>

   <String Id="IDS_WSNM_SERVICE_DISPLAY_NAME">Omnissa Horizon Agent</String>
   <String Id="WsnmServiceDescription">Horizon Agent 서비스를 제공합니다.</String>

   <String Id="IDS_WSSH_SERVICE_DISPLAY_NAME">Omnissa Horizon 스크립트 호스트</String>
   <String Id="IDS_WSSH_SERVICE_DESCRIPTION">스크립트 호스트 서비스를 제공합니다.</String>

   <String Id="IDS_VMLM_SERVICE_DISPLAY_NAME">Omnissa Horizon 로그온 모니터</String>
   <String Id="IDS_VMLM_SERVICE_DESCRIPTION">로그온 모니터링 서비스를 제공합니다.</String>

   <String Id="IDS_HZMON_SERVICE_DISPLAY_NAME">Omnissa Horizon Monitoring Service</String>
   <String Id="IDS_HZMON_SERVICE_DESCRIPTION">모니터링 서비스를 제공합니다.</String>

   <String Id="IDS_VMWRXG_SERVICE_DISPLAY_NAME">Omnissa Horizon 원격 환경 서비스</String>
   <String Id="IDS_VMWRXG_SERVICE_DESCRIPTION">원격 환경 일반 서비스를 제공합니다.</String>

   <String Id="IDS_AUTORESTART">성공적으로 완료되면 시스템을 자동으로 다시 시작</String>

   <String Id="IDS_AUTORESTART_MODIFY">필요한 경우 성공적으로 완료되면 시스템을 자동으로 다시 시작</String>

</WixLocalization>
