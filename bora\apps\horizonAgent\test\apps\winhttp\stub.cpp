/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * stubs.cpp
 *
 *      Implementation of stub functions needed when linking in other parts of
 *      bora and bora-vmsoft.
 *
 */

#include "stdafx.h"
#include "str.h"


// Macro used in most stub functions.
#define WINHTTP_TEST_VLOG(format, logLevel)                                                        \
   va_list arguments;                                                                              \
   va_start(arguments, format);                                                                    \
   char *result = Str_Vasprintf(NULL, format, arguments);                                          \
   if (result) {                                                                                   \
      CORE::sysmsg(logLevel, CORE::mstr(result)._wstr());                                          \
      free(result);                                                                                \
   }                                                                                               \
   va_end(arguments);


/*
 *----------------------------------------------------------------------------
 *
 * Panic --
 *
 *      Stub Panic implementation.  Does nothing.
 *
 * Returns:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

extern "C" void
Panic(LPCSTR, ...)
{}


/*
 *----------------------------------------------------------------------------
 *
 * Debug --
 *
 *      Writes a Trace message to the log.
 *
 * Returns:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

extern "C" void
Debug(LPCSTR format, ...)
{
   WINHTTP_TEST_VLOG(format, CORE::Trace);
}


/*
 *----------------------------------------------------------------------------
 *
 * Log --
 *
 *      Writes a Debug message to the log.
 *
 * Returns:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

extern "C" void
Log(LPCSTR format, ...)
{
   WINHTTP_TEST_VLOG(format, CORE::Debug);
}


/*
 *----------------------------------------------------------------------------
 *
 * Warning --
 *
 *      Writes a Warn message to the log.
 *
 * Returns:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

extern "C" void
Warning(LPCSTR format, ...)
{
   WINHTTP_TEST_VLOG(format, CORE::Warn);
}
