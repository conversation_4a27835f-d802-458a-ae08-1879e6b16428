/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */
package com.omnissa.vdi.orchestratorj.keyvault;

import java.math.BigInteger;
import java.security.SecureRandom;

import com.omnissa.vdi.commonutils.JCA;
import com.omnissa.vdi.mfwj.PropertyBag;

public class KeyVaultTransform {
    private static final String TRANS_SEPARATOR = "/";

    private SecureRandom sr;

    public enum EncryptionName {
        AES(false), RSA(true);

        private boolean asymmetric;

        EncryptionName(boolean asymmetric) {
            this.asymmetric = asymmetric;
        }

        public static EncryptionName fromString(String type) {
            return valueOf(type.toUpperCase());
        }

        public boolean isAsymmetric() {
            return asymmetric;
        }
    }

    public enum EncryptionMode {
        // values from bcrypt.h
        CBC("ChainingModeCBC"), ECB("ChainingModeECB"), <PERSON><PERSON>(
                "ChainingModeCFB"), <PERSON><PERSON>(
                        "ChainingModeCCM"), GCM("ChainingModeGCM");

        private final String mode;

        EncryptionMode(String mode) {
            this.mode = mode;
        }

        public String asString() {
            return mode;
        }

        public static EncryptionMode fromString(String type) {
            return valueOf(type.toUpperCase());
        }
    }

    public enum EncryptionPadding {
        // values from wincrypt.h
        NoPadding(0), PKCS5Padding(1), RandomPadding(2), ZeroPadding(3);

        private final int padding;

        EncryptionPadding(int padding) {
            this.padding = padding;
        }

        public int asInteger() {
            return padding;
        }

        public String asIntegerString() {
            return Integer.toString(padding);
        }

        public static EncryptionPadding fromString(String type) {
            for (EncryptionPadding p : EncryptionPadding.values()) {
                if (p.name().equalsIgnoreCase(type)) {
                    return p;
                }
            }

            throw new IllegalArgumentException(type);
        }

        public static EncryptionPadding fromValue(int padding) {
            for (EncryptionPadding p : values()) {
                if (p.asInteger() == padding) {
                    return p;
                }
            }

            throw new IllegalArgumentException(Integer.toString(padding));
        }
    }

    public enum RsaPadding {
        // values from wincrypt.h and bcrypt.h
        NoPadding(1), PKCS1Padding(2), OAEPPadding(4), PSSPadding(8);

        private final int padding;

        RsaPadding(int padding) {
            this.padding = padding;
        }

        public int asInteger() {
            return padding;
        }

        public String asIntegerString() {
            return Integer.toString(padding);
        }

        public static RsaPadding fromString(String type) {
            for (RsaPadding p : RsaPadding.values()) {
                if (p.name().equalsIgnoreCase(type)) {
                    return p;
                }
            }

            throw new IllegalArgumentException(type);
        }

        public static RsaPadding fromValue(int padding) {
            for (RsaPadding p : values()) {
                if (p.asInteger() == padding) {
                    return p;
                }
            }

            throw new IllegalArgumentException(Integer.toString(padding));
        }
    }

    public enum HashAlgorithmName {
        SHA1("SHA1"), SHA256("SHA256"), SHA384("SHA384"), SHA512("SHA512");

        private final String algName;

        HashAlgorithmName(String algName) {
            this.algName = algName;
        }

        public static HashAlgorithmName fromString(String typestr) {
            if (typestr != null) {
                for (HashAlgorithmName alg : HashAlgorithmName.values()) {
                    if (alg.algName.equalsIgnoreCase(typestr)) {
                        return alg;
                    }
                }
            }

            throw new IllegalArgumentException(
                    "No such hash algorithm: " + typestr);
        }

        @Override
        public String toString() {
            return algName;
        }
    }

    public enum DigestAlgorithmName {
        SHA1("SHA-1"), SHA256("SHA-256"), SHA384("SHA-384"), SHA512("SHA-512");

        private final String algName;

        DigestAlgorithmName(String algName) {
            this.algName = algName;
        }

        public static DigestAlgorithmName fromHash(HashAlgorithmName name) {
            String nn = name.name();
            if (nn != null) {
                for (DigestAlgorithmName alg : DigestAlgorithmName.values()) {
                    if (alg.name().equals(nn)) {
                        return alg;
                    }
                }
            }

            throw new IllegalArgumentException(
                    "No such digest algorithm: " + nn);
        }

        public static DigestAlgorithmName fromString(String typestr) {
            if (typestr != null) {
                for (DigestAlgorithmName alg : DigestAlgorithmName.values()) {
                    if (alg.algName.equalsIgnoreCase(typestr)) {
                        return alg;
                    }
                }
            }

            throw new IllegalArgumentException(
                    "No such digest algorithm: " + typestr);
        }

        @Override
        public String toString() {
            return algName;
        }
    }

    private final boolean aead;

    private final EncryptionName algorithm;

    private EncryptionMode mode;

    private EncryptionPadding blockPadding;

    private RsaPadding rsaPadding;

    private HashAlgorithmName hashAlgorithmName;

    private byte[] iv;

    private byte[] salt;

    private byte[] extraData;

    private int tagLength;

    private boolean foreign;

    /**
     * Build a transform object from an algorithm name. Other parts of the
     * transform are defaulted.
     *
     * @param algorithm
     *            the name of the cipher algorithm
     * @throws KeyVaultException
     *             if a cipher algorithm is not specified
     */
    public KeyVaultTransform(EncryptionName algorithm)
            throws KeyVaultException {
        this(algorithm, null, null, null);
    }

    /**
     * Build a transform object from individual elements. Only algorithm is
     * mandatory, null for any other parameter means use default.
     *
     * Use this method variant for block ciphers only!
     *
     * @param algorithm
     *            the name of the cipher algorithm
     * @param mode
     *            the cipher mode
     * @param blockPadding
     *            the padding mode for block ciphers (supply null if algorithm
     *            is not a block cipher)
     * @throws KeyVaultException
     *             if a cipher algorithm is not specified
     */
    public KeyVaultTransform(EncryptionName algorithm, EncryptionMode mode,
            EncryptionPadding blockPadding) throws KeyVaultException {
        this(algorithm, mode, blockPadding, null);
    }

    /**
     * Build a transform object from individual elements. Only algorithm is
     * mandatory, null for any other parameter means use default.
     *
     * @param algorithm
     *            the name of the cipher algorithm
     * @param mode
     *            the cipher mode
     * @param blockPadding
     *            the padding mode for block ciphers (supply null if algorithm
     *            is not a block cipher)
     * @param rsaPadding
     *            the padding mode for rsa (supply null if algorithm is a block
     *            cipher)
     * @throws KeyVaultException
     *             if a cipher algorithm is not specified
     */
    public KeyVaultTransform(EncryptionName algorithm, EncryptionMode mode,
            EncryptionPadding blockPadding, RsaPadding rsaPadding)
            throws KeyVaultException {
        if (algorithm == null) {
            throw new KeyVaultException("Missing algorithm name");
        }

        this.algorithm = algorithm;
        this.mode = mode != null ? mode : defaultMode(algorithm);
        this.blockPadding = blockPadding != null ? blockPadding
                : defaultBlockPadding(algorithm);
        this.rsaPadding = rsaPadding != null ? rsaPadding
                : defaultRsaPadding(algorithm);
        aead = (mode == EncryptionMode.CCM) || (mode == EncryptionMode.GCM);
    }

    /**
     * Build a Transform object from a transform specification. A transform
     * specification has the format "algorithm[/mode[/padding]]", where missing
     * components have defaults.
     *
     * @param specification
     *            the transform specification
     * @throws KeyVaultException
     *             Generic KeyVault exception
     *
     * @see com.omnissa.vdi.orchestratorj.keyvault.KeyVaultTransform.EncryptionName
     * @see com.omnissa.vdi.orchestratorj.keyvault.KeyVaultTransform.EncryptionMode
     * @see com.omnissa.vdi.orchestratorj.keyvault.KeyVaultTransform.EncryptionPadding
     */
    public KeyVaultTransform(String specification) throws KeyVaultException {
        this(specification, null, null);
    }

    /**
     * Build a Transform object from a mandatory transform specification and
     * optional initialization vector and salt. A transform specification has
     * the format "algorithm[/mode[/padding]]", where missing components have
     * defaults.
     *
     * @param specification
     *            the transform specification
     * @param iv
     *            the initialization vector, or null for a zero iv
     * @param salt
     *            the salt, or null for a zero salt
     * @throws KeyVaultException
     *             Generic KeyVault exception
     *
     * @see com.omnissa.vdi.orchestratorj.keyvault.KeyVaultTransform.EncryptionName
     * @see com.omnissa.vdi.orchestratorj.keyvault.KeyVaultTransform.EncryptionMode
     * @see com.omnissa.vdi.orchestratorj.keyvault.KeyVaultTransform.EncryptionPadding
     */
    public KeyVaultTransform(String specification, byte[] iv, byte[] salt)
            throws KeyVaultException {
        if (specification == null) {
            throw new KeyVaultException("Empty transform specification");
        }

        String[] components = specification.split("[" + TRANS_SEPARATOR + "]",
                3);
        if (components.length == 0) {
            throw new KeyVaultException(
                    "Invalid transform specification: " + specification);
        }

        EncryptionName algorithm;
        try {
            algorithm = EncryptionName.fromString(components[0]);
        } catch (IllegalArgumentException e) {
            throw new KeyVaultException(
                    "Invalid algorithm in transform specification: "
                            + e.getMessage());
        }

        EncryptionMode mode = defaultMode(algorithm);
        if (components.length > 1) {
            try {
                mode = EncryptionMode.fromString(components[1]);
            } catch (IllegalArgumentException e) {
                throw new KeyVaultException(
                        "Invalid mode in transform specification: "
                                + e.getMessage());
            }
        }

        EncryptionPadding padding = defaultBlockPadding(algorithm);
        RsaPadding rsaPadding = defaultRsaPadding(algorithm);
        if (components.length > 2) {
            try {
                if (EncryptionName.RSA.equals(algorithm)) {
                    rsaPadding = RsaPadding.fromString(components[2]);
                } else {
                    padding = EncryptionPadding.fromString(components[2]);
                }
            } catch (IllegalArgumentException e) {
                throw new KeyVaultException(
                        "Invalid padding in transform specification: "
                                + e.getMessage());
            }
        }

        this.algorithm = algorithm;
        this.mode = mode;
        blockPadding = padding;
        this.rsaPadding = rsaPadding;
        this.iv = iv;
        this.salt = salt;
        aead = (mode == EncryptionMode.CCM) || (mode == EncryptionMode.GCM);
    }

    /**
     * Returns the initialization vector or the AEAD nonce
     *
     * @return the initialization vector (may be null)
     */
    public byte[] getIV() {
        return iv;
    }

    /**
     * Returns the salt or the AEAD tag
     *
     * @return the salt (may be null)
     */
    public byte[] getSalt() {
        return salt;
    }

    /**
     * Set the hash algorithm name.
     *
     * @param hashAlgorithmName
     *            The name of the hashing algorithm
     */
    public void setHashAlgorithmName(HashAlgorithmName hashAlgorithmName) {
        this.hashAlgorithmName = hashAlgorithmName;
    }

    /**
     * Generate a random initialization vector of a length appropriate to the
     * configured cipher algorithm and mode. NB: may not set anything if an IV
     * is not compatible with the cipher algorithm. For AEAD ciphers, this
     * parameter is used as a nonce.
     */
    public void setIV() {
        if (!algorithm.isAsymmetric()) {
            int byteLength = 16;
            if (mode != null) {
                switch (mode) {
                case CCM:
                    byteLength = 11;
                    break;
                case GCM:
                    byteLength = 12;
                    break;
                default:
                    break;
                }
            }
            // provide length in bits
            setIV(toBits(byteLength));
        }
    }

    /**
     * Set the initialization vector. NB: typically this should be the same byte
     * length as the block. For AEAD ciphers, this parameter is used as a nonce.
     *
     * @param iv
     *            the initialization vector
     */
    public void setIV(byte[] iv) {
        this.iv = iv;
    }

    /**
     * Generate a random initialization vector of the given bit length. NB:
     * typically this should be the same bit length as the block. For AEAD
     * ciphers, this is used as a nonce.
     *
     * @param bitLength
     *            the bit length
     */
    public void setIV(int bitLength) {
        iv = randBytes(bitLength);
    }

    /**
     * Generate a random salt of a length dependent on the configured hash
     * algorithm. For AEAD encryption, this is the tag length.
     */
    public void setSalt() {
        int bitLength;
        if (aead) {
            bitLength = toBits(12);
        } else if (hashAlgorithmName != null) {
            switch (hashAlgorithmName) {
            case SHA512:
                bitLength = 512;
                break;
            case SHA384:
                bitLength = 384;
                break;
            case SHA256:
                bitLength = 256;
                break;
            case SHA1:
            default:
                bitLength = 160;
                break;
            }
        } else {
            bitLength = 160;
        }
        setSalt(bitLength);
    }

    /**
     * Set the salt. NB: typically this should be the same byte length as the
     * hash, or if no hash, half the length of the key. For AEAD decryption,
     * this is the tag.
     *
     * @param salt
     *            The salt
     */
    public void setSalt(byte[] salt) {
        this.salt = salt;
    }

    /**
     * Generate a random salt of the given bit length. NB: typically this should
     * be the same bit length as the hash, or if no hash, half the length of the
     * key. For AEAD encryption, this is the tag length in bits.
     *
     * @param bitLength
     *            The bit length
     */
    public void setSalt(int bitLength) {
        if (aead) {
            // AEAD tag length in bytes
            tagLength = toBytes(bitLength);
        } else {
            salt = randBytes(bitLength);
        }
    }

    /**
     * Tell KeyVault that the data to be encrypted will be decrypted elsewhere,
     * or that data to be decrypted was encrypted elsewhere. This stops KeyVault
     * from applying backwards-compatibility fixes like buffer reversal.
     *
     * @param foreign
     *            true if encrypted data should be treated as foreign
     */
    public void setForeign(boolean foreign) {
        this.foreign = foreign;
    }

    /**
     * Specify additional authenticated data for AEAD cipher modes, or an OAEP
     * padding label for RSA.
     *
     * @param extraData
     *            OAEP label or AAD
     */
    public void setExtraData(byte[] extraData) {
        this.extraData = extraData;
    }

    /**
     * Generate a property bag suitable for passing to KeyVault.
     *
     * @return a property bag containing elements of the transform. The property
     *         bag may be empty.
     */
    public PropertyBag toPropertyBag() {
        PropertyBag transform = new PropertyBag();

        if (mode != null) {
            transform.add("XF_CHAIN_MODE", mode.asString());
        }

        if (rsaPadding != null) {
            transform.add("XF_RSA_PADDING", rsaPadding.asIntegerString());
        } else if (blockPadding != null) {
            transform.add("XF_BLOCK_PADDING", blockPadding.asIntegerString());
        }

        if (hashAlgorithmName != null) {
            if (rsaPadding == RsaPadding.OAEPPadding) {
                transform.add("XF_PAD_ALGID", hashAlgorithmName.algName);
            } else {
                transform.add("XF_ALGID", hashAlgorithmName.algName);
            }
        }

        if (extraData != null) {
            if (rsaPadding == RsaPadding.OAEPPadding) {
                transform.addBinary("XF_PAD_LABEL", extraData);
            } else {
                transform.addBinary("XF_AAD", extraData);
            }
        }

        if ((iv != null) && (iv.length > 0)) {
            transform.addBinary("XF_IV", iv);
        }

        if ((salt != null) && (salt.length > 0)) {
            if (aead) {
                transform.addBinary("XF_TAG", salt);
            } else {
                // deprecated
                transform.addBinary("KP_SALT", salt);
            }
        } else if (tagLength > 0) {
            transform.addInt("XF_TAGLEN", tagLength);
        }

        if (foreign) {
            transform.addBool("XF_FOREIGN", true);
        }

        return transform;
    }

    /**
     * Extract intelligence from the provided property bag to update this
     * transform.
     *
     * @param bag
     *            the property bag possibly containing update information
     */
    public void updateFromPropertyBag(PropertyBag bag) {
        if (aead && bag.contains("XF_TAG")) {
            setSalt(bag.getBinaryByteArray("XF_TAG"));
            tagLength = 0;
        }
    }

    /**
     * Generate a transform specification.
     *
     * @return a fully-qualified transform specification. NB: iv and salt are
     *         not included in a transform specification.
     *
     * @see com.omnissa.vdi.orchestratorj.keyvault.KeyVaultTransform#toString
     * @see com.omnissa.vdi.orchestratorj.keyvault.KeyVaultTransform#getIV
     * @see com.omnissa.vdi.orchestratorj.keyvault.KeyVaultTransform#getSalt
     */
    public String toSpec() {
        StringBuilder sb = new StringBuilder(algorithm.name());
        sb.append(TRANS_SEPARATOR);
        if (mode != null) {
            sb.append(mode.name());
        } else if (algorithm.isAsymmetric()) {
            /*
             * Chaining mode doesn't make sense for asymmetric keys but Java
             * wants one so we set Electronic Code Book, which effectively means
             * no chaining.
             */
            sb.append(EncryptionMode.ECB.name());
        }
        sb.append(TRANS_SEPARATOR);
        if (EncryptionName.RSA.equals(algorithm)) {
            if (rsaPadding != null) {
                sb.append(rsaPadding.name());
            }
        } else if (blockPadding != null) {
            sb.append(blockPadding.name());
        }
        return sb.toString();
    }

    @Override
    public String toString() {
        String i = iv != null ? new BigInteger(1, iv).toString(16) : "00";
        String s = salt != null ? new BigInteger(1, salt).toString(16) : "00";
        return "KeyVaultTransform[" + toSpec() + ", iv=" + i + ", salt=" + s
                + "]";
    }

    private EncryptionMode defaultMode(EncryptionName algorithm) {
        return algorithm.isAsymmetric() ? null : EncryptionMode.CBC;
    }

    private EncryptionPadding defaultBlockPadding(EncryptionName algorithm) {
        return algorithm.isAsymmetric() ? null : EncryptionPadding.NoPadding;
    }

    private RsaPadding defaultRsaPadding(EncryptionName algorithm) {
        return algorithm.isAsymmetric() ? RsaPadding.NoPadding : null;
    }

    private byte[] randBytes(int bitLength) {
        if (sr == null) {
            sr = JCA.newSecureRandom();
        }

        byte[] rb = new byte[toBytes(bitLength)];
        sr.nextBytes(rb);
        return rb;
    }

    private int toBits(int byteLength) {
        return byteLength * 8;
    }

    private int toBytes(int bitLength) {
        return bitLength / 8;
    }
}
