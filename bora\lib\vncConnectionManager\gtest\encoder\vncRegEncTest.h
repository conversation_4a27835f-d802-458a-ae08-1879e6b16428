/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * vncRegEncTest.h --
 *
 *    Definitions for vncRegEncTest.cpp.
 *
 */

#include <gtest/gtest.h>
#include <filesystem>
#include <memory>

#include <nvEncodeAPI.h>
#include <nvml.h>

// bora/apps/cedar/include
#include <cedar/unique_any.h>
#include "cedar/platform.h"

#if !CEDAR_PLATFORM_WINDOWS
#   include <unistd.h>
#else
#   include <dxgi.h>
#   include "mock/dxgiMock.h"
#   include "mock/d3d11Devicemock.h"
#   include "mock/d3d11DeviceContextMock.h"
#   include "mock/d3d11TextureMock.h"
#   include <win32uRegistry.h>
#   include <vpl/mfxdispatcher.h>
#   include <vpl/mfxvideo.h>
#   include <core/Factory.h>
#endif // CEDAR_PLATFORM_WINDOWS

// bora/lib/public
#include "imageUtil.h"
#include "str.h"

// bora/lib/vnc
#include "vncBitmask.h"

// bora/lib/blastCodec
#include "blastCache.h"

// bora/lib/vncConnectionManager/encode
#include "vncEncodeRegion.h"
#include "vncEncodeManager.h"

//  bora/public
#include "dll_load_defs.h"
#include "horizonPaths.h"

#include "utMock.h"
#include "rxUTLog.h"


#define RETURN_MOCK(x)                                                                             \
   if (nameStr == #x) {                                                                            \
      return reinterpret_cast<FARPROC>(Mock##x);                                                   \
   }

/*
 * We retry encode again for boost frames since some encoder
 * retry based on buffer we capped it at 1200 times. For encoder
 * that provides next boost time we cap at 60 frames.
 */
static const uint32 MAX_ENCODE_BUFFER_RETRY = 1200;
static const uint32 MAX_ENCODE_BOOST_TIME_RETRY = 60;

// Values used for VNCRegionEncoder_InitUpdateCache
// (Borrowed from vncRegEncPerf.cpp)
static const uint32 kRegEncCacheSizeEntries = 400;
static const uint32 kRegEncCacheSizeKb = 100 * 1024;
static const uint32 kRegEncCacheCaps =
   VNC_ADAPTIVE_CACHE_CAP_DISABLE_OFFSCREEN_SURFACE | VNC_ADAPTIVE_CACHE_CAP_REPLAY |
   VNC_ADAPTIVE_CACHE_CAP_RAW_RECTANGLES | VNC_ADAPTIVE_CACHE_CAP_BACK_COPY |
   VNC_ADAPTIVE_CACHE_CAP_END_SUB_RECTANGLE;

using unique_globalCache =
   cedar::unique_any<BlastGlobalCache *, decltype(&BlastGlobalCache_Destroy),
                     BlastGlobalCache_Destroy>;

using ::testing::_;
using ::testing::AtLeast;
using ::testing::AtMost;
using ::testing::Invoke;

#if defined(_WIN32)
struct ID3D11Device;
using OSDevicePtr = ID3D11Device *;
struct ID3D11Texture2D;
using OSTexturePtr = ID3D11Texture2D *;

static const char separator = '\\';

#else

class VncFbPtr;
struct vncServerLinuxGL;
using OSDevicePtr = vncServerLinuxGL *;

struct GLTextureWrapper {
   GLTextureWrapper(OSDevicePtr d, VncFbPtr &fb);
   ~GLTextureWrapper();

   int target;
   unsigned int id;
   OSDevicePtr device;

private:
   GLTextureWrapper() = delete;
};
using OSTexturePtr = GLTextureWrapper *;


static const char separator = '/';

#endif


class VncBitmaskPtr {
public:
   VncBitmaskPtr(int w, int h, int mcu) { _bitmask = VNCBitmask_Create(w, h, mcu); }

   ~VncBitmaskPtr(void) { VNCBitmask_Destroy(_bitmask); }

   VNCBitmask *operator*(void) { return _bitmask; }

   void Set(void) { VNCBitmask_Set(_bitmask); }

   void Clear(void) { VNCBitmask_Clear(_bitmask); }

private:
   VncBitmaskPtr() = delete;
   VNCBitmask *_bitmask;
};

class VncFbPtr {
public:
   VncFbPtr(ImageInfo &image) : _image(image) {}

   ~VncFbPtr() {}

   const uint8 *operator*() { return _image.data; }

   const int Pitch() { return _image.bytesPerLine; }

   const int Width() { return _image.width; }

   const int Height() { return _image.height; }

   const void *Data() { return reinterpret_cast<void *>(_image.data); }

private:
   VncFbPtr() = delete;
   ImageInfo _image;
};

extern "C" {
extern VNCServerOSInterface VNCServerOS;
}

#if CEDAR_PLATFORM_WINDOWS

extern NVENCSTATUS NVENCAPI
MockNvEncodeAPICreateInstance(NV_ENCODE_API_FUNCTION_LIST *functionList);
extern nvmlReturn_t MocknvmlInit(void);
extern nvmlReturn_t MocknvmlShutdown(void);
extern const char *MocknvmlErrorString(nvmlReturn_t result);
extern nvmlReturn_t MocknvmlDeviceGetCount(unsigned int *deviceCount);
extern nvmlReturn_t MocknvmlDeviceGetHandleByIndex(unsigned int index, nvmlDevice_t *device);
extern nvmlReturn_t MocknvmlDeviceGetEncoderUtilization(nvmlDevice_t device,
                                                        unsigned int *utilization,
                                                        unsigned int *samplingPeriodUs);
extern nvmlReturn_t MocknvmlDeviceGetEncoderCapacity(nvmlDevice_t device,
                                                     nvmlEncoderType_t encoderQueryType,
                                                     unsigned int *encoderCapacity);
extern nvmlReturn_t MocknvmlDeviceGetMemoryInfo(nvmlDevice_t device, nvmlMemory_t *memory);
extern nvmlReturn_t MocknvmlDeviceGetUtilizationRates(nvmlDevice_t device,
                                                      nvmlUtilization_t *utilization);
extern nvmlReturn_t MocknvmlDeviceGetProcessUtilization(nvmlDevice_t device,
                                                        nvmlProcessUtilizationSample_t *utilization,
                                                        unsigned int *processCount,
                                                        unsigned long long lastSeenTimeStamp);
extern nvmlReturn_t MocknvmlSystemGetProcessName(unsigned int pid, char *name, unsigned int length);
extern nvmlReturn_t MocknvmlDeviceGetGraphicsRunningProcesses(nvmlDevice_t device,
                                                              unsigned int *infoCount,
                                                              nvmlProcessInfo_t *gInfos);
extern nvmlReturn_t MocknvmlDeviceGetComputeRunningProcesses(nvmlDevice_t device,
                                                             unsigned int *infoCount,
                                                             nvmlProcessInfo_t *cInfos);
extern nvmlReturn_t MocknvmlSystemGetDriverVersion(char *version, unsigned int length);
extern nvmlReturn_t MocknvmlSystemGetNVMLVersion(char *version, unsigned int length);
extern nvmlReturn_t MocknvmlDeviceGetName(nvmlDevice_t device, char *name, unsigned int length);
extern nvmlReturn_t MocknvmlDeviceGetGridLicensableFeatures(
   nvmlDevice_t device, nvmlGridLicensableFeatures_t *pGridLicensableFeatures);
extern nvmlReturn_t MocknvmlDeviceGetVirtualizationMode(nvmlDevice_t device,
                                                        nvmlGpuVirtualizationMode_t *mode);

extern mfxLoader MFX_CDECL MockMFXLoad(void);
extern void MFX_CDECL MockMFXUnload(mfxLoader loader);
extern mfxConfig MFX_CDECL MockMFXCreateConfig(mfxLoader loader);
extern mfxStatus MFX_CDECL MockMFXCreateSession(mfxLoader loader, mfxU32 i, mfxSession *session);
extern mfxStatus MFX_CDECL MockMFXClose(mfxSession session);
extern mfxStatus MFX_CDECL MockMFXEnumImplementations(mfxLoader loader, mfxU32 i,
                                                      mfxImplCapsDeliveryFormat format,
                                                      mfxHDL *idesc);
extern mfxStatus MFX_CDECL MockMFXDispReleaseImplDescription(mfxLoader loader, mfxHDL hdl);
extern mfxStatus MFX_CDECL MockMFXSetConfigFilterProperty(mfxConfig config, const mfxU8 *name,
                                                          mfxVariant value);
extern mfxStatus MFX_CDECL MockMFXQueryIMPL(mfxSession session, mfxIMPL *impl);
extern mfxStatus MFX_CDECL MockMFXQueryVersion(mfxSession session, mfxVersion *version);
extern mfxStatus MFX_CDECL MockMFXVideoENCODE_Query(mfxSession session, mfxVideoParam *in,
                                                    mfxVideoParam *out);
extern mfxStatus MFX_CDECL MockMFXVideoENCODE_QueryIOSurf(mfxSession session, mfxVideoParam *par,
                                                          mfxFrameAllocRequest *request);
extern mfxStatus MFX_CDECL MockMFXMemory_GetSurfaceForEncode(mfxSession session,
                                                             mfxFrameSurface1 **surface);
extern mfxStatus MFX_CDECL MockMFXVideoENCODE_Init(mfxSession session, mfxVideoParam *par);
extern mfxStatus MFX_CDECL MockMFXVideoENCODE_Reset(mfxSession session, mfxVideoParam *par);
extern mfxStatus MFX_CDECL MockMFXVideoENCODE_Close(mfxSession session);
extern mfxStatus MFX_CDECL MockMFXVideoENCODE_GetVideoParam(mfxSession session, mfxVideoParam *par);
extern mfxStatus MFX_CDECL MockMFXVideoENCODE_GetEncodeStat(mfxSession session,
                                                            mfxEncodeStat *stat);
extern mfxStatus MFX_CDECL MockMFXVideoENCODE_EncodeFrameAsync(mfxSession session,
                                                               mfxEncodeCtrl *ctrl,
                                                               mfxFrameSurface1 *surface,
                                                               mfxBitstream *bs,
                                                               mfxSyncPoint *syncp);
extern mfxStatus MFX_CDECL MockMFXVideoCORE_SyncOperation(mfxSession session, mfxSyncPoint syncp,
                                                          mfxU32 wait);
extern mfxStatus MFX_CDECL MockMFXVideoCORE_SetFrameAllocator(mfxSession session,
                                                              mfxFrameAllocator *allocator);
extern mfxStatus MFX_CDECL MockMFXVideoCORE_SetHandle(mfxSession session, mfxHandleType type,
                                                      mfxHDL hdl);
extern AMF_RESULT MockAMFInit(amf_uint64 version, amf::AMFFactory **ppFactory);
extern AMF_RESULT MockAMFQueryVersion(amf_uint64 *pVersion);

#endif // CEDAR_PLATFORM_WINDOWS

class VncRegEnc {
public:
   VncRegEnc(VNCRegEncConfig config)
   {
      VNCServerOS.TracingEvent = TracingEvent;
      VNCServerOS.CreateDevice = CreateDeviceWrapper;
      VNCServerOS.DestroyDevice = DestroyDeviceWrapper;

      VNCEncodeManager_PowerOn();
      config.dynamicConfig.encoderGlobalQualityLevel = CONFIG_ENCODER_GLOBAL_QUALITY_LEVEL_DEFAULT;

      if (!VNCRegionEncoder_CalculateBlastCaps(
             config.numScreens, config.clientDecoderCaps, VNCVIEW_BLASTCAP_STABLE, config.caps,
             &config.dynamicConfig, &config.blastCodecProtocolVersion, &config.blastCodecCaps)) {
         config.dynamicConfig.allowBlastCodec = FALSE;
      }

      _regEnc = VNCRegionEncoder_Create(&config);

      VNCEncodeBufInit(&_buf, MAX_INT32);

      VNCCacheType cacheType = VNCRegionEncoder_GetCacheType(_regEnc);
      if (cacheType != CACHE_TYPE_NONE) {
         if (cacheType == CACHE_TYPE_BLAST_CODEC_SHARED) {
            ASSERT(config.dynamicConfig.enableBlastCodecSharedCache);
            BlastGlobalCacheConfig config = {
               .cacheSizeBytes = kRegEncCacheSizeKb * 1024,
               .cacheEntryCount = BLAST_CACHE_INVALID_INDEX,
               .blastCaps = VNCVIEW_BLASTCAP_STABLE,
               .decode = FALSE,
               .x2CacheAllowCompression = TRUE,
            };
            _globalCache = unique_globalCache(BlastGlobalCache_Create(&config));
            BlastGlobalCache_SetLogFlags(_globalCache.get(), 0, FALSE);
         }
         VNCRegionEncoder_InitUpdateCache(_regEnc, kRegEncCacheCaps, kRegEncCacheSizeEntries,
                                          kRegEncCacheSizeEntries, kRegEncCacheSizeKb, 0,
                                          _globalCache.get());
      }
   }
   ~VncRegEnc(void)
   {
      VNCEncodeBufDestroy(&_buf);
      VNCRegionEncoder_Destroy(_regEnc);
      VNCEncodeManager_PowerOff();
   }

   const char *Name(void) { return VNCRegionEncoder_GetName(_regEnc); }

   VNCError Encode(VNCRegEncFrameState state)
   {
      VNCRegionEncoder_PrepareEncode(_regEnc, &_buf, 0);
      VNCError ret = VNCRegionEncoder_Encode(_regEnc, &state, &_buf);
      if (_globalCache.get()) {
         BlastGlobalCache_Commit(_globalCache.get());
      }
      return ret;
   }

   VNCRegEncState GetEncodeState(double *thisBoostTime)
   {
      VNCRegEncState encodeState;

      VNCRegionEncoder_GetEncodeState(_regEnc, &encodeState, thisBoostTime);

      return encodeState;
   }

   uint32 BufferedRectCount(void) { return VNCEncodeBufRectCount(&_buf); }

   void ResetBuffer(void)
   {
      VNCEncodeBufDestroy(&_buf);
      VNCEncodeBufInit(&_buf, MAX_INT32);
   }

   Bool Resize(int x, int y, int w, int h)
   {
      VMRect rect{x, y, x + w, y + h};
      return VNCRegionEncoder_Resize(_regEnc, &rect);
   }

   static void TracingEvent(char *eventId, BlastTrace_TracingLevel eventLevel, uint64 eventKeyword,
                            char *eventVal1Name, uint32 eventVal1)
   {}

   static OSDevicePtr CreateDevice();
   static Bool CreateDeviceWrapper(uint16_t vendorId, void *pAdapterLuid, void **ppDevice,
                                   void **ppDeviceContext, void *pCriticalSection);

   static void DestroyDevice(OSDevicePtr pDevice);
   static void DestroyDeviceWrapper(void **pDevice, void **pDeviceContext, void *pCriticalSection);

   static OSTexturePtr CreateTexture(OSDevicePtr pDevice, VncFbPtr &fb);
   static void DestroyTexture(OSTexturePtr pTexture);
   static void UpdateFrameTexture(VNCRegEncFrameState &state, OSTexturePtr pTexture);

private:
   VncRegEnc() = delete;

   VNCRegionEncoder *_regEnc;
   VNCEncodeBuf _buf;
   unique_globalCache _globalCache;
};


enum class PixelProviderType {
   SW,
   HW,
};

class VncRegEncTest : public ::testing::Test {
public:
   VncRegEncTest() {};
   virtual ~VncRegEncTest() {};

protected:
   static void SetUpTestCase()
   {
      VERIFY(images.empty());

      // We load all the .png files in the executable's directory.
      char programPath[PATH_MAX];
#if CEDAR_PLATFORM_WINDOWS
      VERIFY(GetModuleFileNameA(nullptr, programPath, sizeof(programPath)) > 0);
#else
      VERIFY(readlink("/proc/self/exe", programPath, sizeof(programPath)) > 0);
#endif
      auto dir = std::filesystem::canonical(std::filesystem::path(programPath).parent_path());
      for (auto fileName : imageFileNames) {
         auto path = dir / fileName;
         if (!std::filesystem::exists(path.string())) {
            continue;
         }
         fprintf(stderr, "Adding PNG read from: %s\n", path.string().c_str());
         ImageInfo img{};
         VERIFY(ImageUtil_ReadPNG(&img, path.string().c_str(), 0));
         images.push_back(img);
      }
   }

   static void TearDownTestCase()
   {
      for (auto it = images.begin(); it != images.end();) {
         ImageUtil_FreeImageData(&*it);
         it = images.erase(it);
      }
   }

   virtual void SetUp()
   {
      memset(&_config, 0, sizeof(_config));
      Rect_SetXYWH(&_config.region.rect, 0, 0, 800, 600);
      _config.numScreens = 1;
      _config.caps = BitVector_Alloc(VNCMaxCap);
   }

   virtual void TearDown(void) { BitVector_Free(_config.caps); }

   ImageInfo &GetImage(int i)
   {
      VERIFY(!images.empty());
      return images[i % images.size()];
   }

   void WaitFrameEncodeComplete(VncRegEnc &regEnc, VNCRegEncFrameState &state);
   void TestFrameEncode(VncRegEnc &regEnc, VNCError statusOnEmptyDiff, PixelProviderType pp);
   void UpdateFrameBuffer(void *pDest, VncFbPtr &src);

   VNCRegEncConfig _config;
   static std::vector<ImageInfo> images;

   static inline std::filesystem::path imageFileNames[] = {"fb.png"};
};

#if CEDAR_PLATFORM_WINDOWS


class VncNvidiaRegEncTest : public VncRegEncTest {
public:
   VncNvidiaRegEncTest() {};
   virtual ~VncNvidiaRegEncTest() {}

protected:
   virtual void SetUp(void)
   {
      VncRegEncTest::SetUp();

      VMOCK_M(_mockLoadLibrary, Win32U_LoadLibrary)
         .OnCall(_)
         .WillByDefault(reinterpret_cast<HMODULE>(~0llu));
      VMOCK_M(_mockFreeLibrary, FreeLibrary).OnCall(_).WillByDefault(TRUE);
      VMOCK_M(_mockGetProcAddress, GetProcAddress)
         .OnCall(_, _)
         .WillByDefault([](HMODULE handle, const char *name) {
            std::string nameStr(name);
            RETURN_MOCK(NvEncodeAPICreateInstance);
            RETURN_MOCK(nvmlInit);
            RETURN_MOCK(nvmlShutdown);
            RETURN_MOCK(nvmlErrorString);
            RETURN_MOCK(nvmlDeviceGetCount);
            RETURN_MOCK(nvmlDeviceGetHandleByIndex);
            RETURN_MOCK(nvmlDeviceGetEncoderUtilization);
            RETURN_MOCK(nvmlDeviceGetEncoderCapacity);
            RETURN_MOCK(nvmlDeviceGetMemoryInfo);
            RETURN_MOCK(nvmlDeviceGetUtilizationRates);
            RETURN_MOCK(nvmlDeviceGetProcessUtilization);
            RETURN_MOCK(nvmlSystemGetProcessName);
            RETURN_MOCK(nvmlDeviceGetGraphicsRunningProcesses);
            RETURN_MOCK(nvmlDeviceGetComputeRunningProcesses);
            RETURN_MOCK(nvmlSystemGetDriverVersion);
            RETURN_MOCK(nvmlSystemGetNVMLVersion);
            RETURN_MOCK(nvmlDeviceGetName);
            RETURN_MOCK(nvmlDeviceGetGridLicensableFeatures);
            RETURN_MOCK(nvmlDeviceGetVirtualizationMode);

            // This shouldn't be reached. Function is not mocked. FAIL here
            ADD_FAILURE() << nameStr << ": Function is not mocked";
            return reinterpret_cast<FARPROC>(0ull);
         });
   }

   VMOCK_T(Win32U_LoadLibrary) _mockLoadLibrary;
   VMOCK_T(FreeLibrary) _mockFreeLibrary;
   VMOCK_T(GetProcAddress) _mockGetProcAddress;
};

class VncIntelRegEncTest : public VncRegEncTest {
public:
   VncIntelRegEncTest() {};
   virtual ~VncIntelRegEncTest() {}

protected:
   virtual void SetUp(void)
   {
      VncRegEncTest::SetUp();

      VMOCK_M(_mockLoadLibrary2, Win32U_LoadLibrary)
         .OnCall(_)
         .WillByDefault(reinterpret_cast<HMODULE>(~0llu));
      VMOCK_M(_mockFreeLibrary, FreeLibrary).OnCall(_).WillByDefault(TRUE);
      VMOCK_M(_mockGetProcAddress, GetProcAddress)
         .OnCall(_, _)
         .WillByDefault([](HMODULE handle, const char *name) {
            std::string nameStr(name);
            RETURN_MOCK(MFXLoad);
            RETURN_MOCK(MFXUnload);
            RETURN_MOCK(MFXCreateConfig);
            RETURN_MOCK(MFXCreateSession);
            RETURN_MOCK(MFXClose);
            RETURN_MOCK(MFXEnumImplementations);
            RETURN_MOCK(MFXDispReleaseImplDescription);
            RETURN_MOCK(MFXSetConfigFilterProperty);
            RETURN_MOCK(MFXQueryIMPL);
            RETURN_MOCK(MFXQueryVersion);
            RETURN_MOCK(MFXVideoENCODE_Query);
            RETURN_MOCK(MFXVideoENCODE_QueryIOSurf);
            RETURN_MOCK(MFXMemory_GetSurfaceForEncode);
            RETURN_MOCK(MFXVideoENCODE_Init);
            RETURN_MOCK(MFXVideoENCODE_Reset);
            RETURN_MOCK(MFXVideoENCODE_Close);
            RETURN_MOCK(MFXVideoENCODE_GetVideoParam);
            RETURN_MOCK(MFXVideoENCODE_GetEncodeStat);
            RETURN_MOCK(MFXVideoENCODE_EncodeFrameAsync);
            RETURN_MOCK(MFXVideoCORE_SyncOperation);
            RETURN_MOCK(MFXVideoCORE_SetFrameAllocator);
            RETURN_MOCK(MFXVideoCORE_SetHandle);

            // This shouldn't be reached. Function is not mocked. FAIL here
            ADD_FAILURE() << nameStr << ": Function is not mocked";
            return reinterpret_cast<FARPROC>(0ull);
         });
   }

   VMOCK_T(Win32U_LoadLibrary) _mockLoadLibrary2;
   VMOCK_T(FreeLibrary) _mockFreeLibrary;
   VMOCK_T(GetProcAddress) _mockGetProcAddress;

   void TestFrameEncodeIntel(VncRegEnc &regEnc, PixelProviderType pp);
};


class VncAMDRegEncTest : public VncRegEncTest {
public:
   VncAMDRegEncTest() {};
   virtual ~VncAMDRegEncTest() {}

protected:
   virtual void SetUp(void)
   {
      VncRegEncTest::SetUp();

      VMOCK_M(_mockCreateDXGIFactory1, CreateDXGIFactory1)
         .OnCall(_, _)
         .WillByDefault([](REFIID riid, void **ppFactory) -> HRESULT {
            if (ppFactory) {
               *ppFactory = new MockIDXGIFactory1();
            }
            return S_OK;
         });

      VMOCK_M(_mockD3D11CreateDevice, D3D11CreateDevice)
         .OnCall(_, _, _, _, _, _, _, _, _, _)
         .WillByDefault([](IDXGIAdapter *pAdapter, D3D_DRIVER_TYPE DriverType, HMODULE Software,
                           UINT Flags, const D3D_FEATURE_LEVEL *pFeatureLevels, UINT FeatureLevels,
                           UINT SDKVersion, ID3D11Device **ppDevice,
                           D3D_FEATURE_LEVEL *pFeatureLevel,
                           ID3D11DeviceContext **ppImmediateContext) {
            if (ppDevice) {
               *ppDevice = new MockD3D11Device();
            }
            if (pFeatureLevel) {
               *pFeatureLevel = D3D_FEATURE_LEVEL_11_0;
            }
            if (ppImmediateContext) {
               *ppImmediateContext = reinterpret_cast<ID3D11DeviceContext *>(0x5678);
            }
            return S_OK;
         });

      VMOCK_M(_mockWin32U_RegOpenKeyEx, Win32U_RegOpenKeyEx)
         .OnCall(_, _, _, _, _)
         .WillByDefault([](HKEY, LPCSTR, DWORD, REGSAM, PHKEY phkResult) -> LONG {
            if (phkResult) {
               *phkResult = reinterpret_cast<HKEY>(0x1234);
            }
            return ERROR_SUCCESS;
         });

      VMOCK_M(_mockWin32U_RegQueryDWORDValue, Win32U_RegQueryDWORDValue)
         .OnCall(_, _, _)
         .WillByDefault([](HKEY, LPCSTR, LPDWORD lpData) -> LONG {
            if (lpData) {
               *lpData = 0; // Simulate a VM
            }
            return ERROR_SUCCESS;
         });

      VMOCK_M(_mockRegCloseKey, RegCloseKey).OnCall(_).WillByDefault([](HKEY) -> LONG {
         return ERROR_SUCCESS;
      });

      VMOCK_M(_mockLoadLibraryAMD, Win32U_LoadLibrary)
         .OnCall(_)
         .WillByDefault(reinterpret_cast<HMODULE>(~0llu));

      VMOCK_M(_mockFreeLibraryAMD, FreeLibrary).OnCall(_).WillByDefault(TRUE);

      VMOCK_M(_mockGetProcAddressAMD, GetProcAddress)
         .OnCall(_, _)
         .WillByDefault([](HMODULE handle, const char *name) {
            std::string nameStr(name);
            RETURN_MOCK(AMFInit);
            RETURN_MOCK(AMFQueryVersion);

            // This shouldn't be reached. Function is not mocked. FAIL here
            ADD_FAILURE() << nameStr << ": Function is not mocked";
            return reinterpret_cast<FARPROC>(0ull);
         });
   }

   VMOCK_T(CreateDXGIFactory1) _mockCreateDXGIFactory1;
   VMOCK_T(D3D11CreateDevice) _mockD3D11CreateDevice;
   VMOCK_T(Win32U_RegOpenKeyEx) _mockWin32U_RegOpenKeyEx;
   VMOCK_T(Win32U_RegQueryDWORDValue) _mockWin32U_RegQueryDWORDValue;
   VMOCK_T(RegCloseKey) _mockRegCloseKey;
   VMOCK_T(Win32U_LoadLibrary) _mockLoadLibraryAMD;
   VMOCK_T(FreeLibrary) _mockFreeLibraryAMD;
   VMOCK_T(GetProcAddress) _mockGetProcAddressAMD;

   void TestFrameEncodeAMD(VncRegEnc &regEnc, PixelProviderType pp);
};

#endif // CEDAR_PLATFORM_WINDOWS
