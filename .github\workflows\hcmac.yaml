name: hcmac
run-name: >
  ${{ github.workflow }}
  ${{ github.event_name == 'workflow_dispatch' && inputs.buildtype ||
      (github.event_name == 'pull_request' && 'obj' || 'beta') }}
  ${{ github.event_name == 'pull_request' &&
      format(' - {0} (#{1})', github.event.pull_request.title, github.event.pull_request.number)
      || '' }}
concurrency:
  # This section ensure that multiple PR pushes will cancel superseded builds.
  # Builds on main, release/* and feature/* branches will not be canceled to
  # assist in root causing build breakages.
  group: ${{ github.workflow }}-${{
      (github.ref == 'refs/heads/main' ||
       startsWith(github.ref, 'refs/heads/feature/') ||
       startsWith(github.ref, 'refs/heads/release/')) &&
      github.run_id || github.ref
    }}-${{ inputs.buildtype }}
  cancel-in-progress: true
on:
  pull_request:
  push:
    branches:
      - 'main'
      - 'release/**'
      - 'feature/**'
    paths-ignore:
      - .github/RunnerResetConfig.json
      - .github/workflows/runner_app_config.yaml
      - .github/workflows/rx-devop-nightly-*.yaml
  workflow_dispatch:
    inputs:
      buildtype:
        type: choice
        description: Build type
        required: True
        default: beta
        options:
          - beta
          - obj
          - opt
          - release
      notarizing:
        type: boolean
        description: Notarizing (Do not enable it but only when it's necessary as it has a limit of 75 per day)
        required: False
        default: false
      conan_develop:
        type: boolean
        description: I am testing conan packages and need to enable the conan-develop remote
        required: True
        default: false
      conan_sandbox:
        type: boolean
        description: I am testing conan compiler upgrade and need to enable the conan-sandbox remote
        required: false
        default: false
jobs:
  file-check:
    runs-on: [lnxbuild-gh, self-hosted]
    permissions:
      contents: read
      pull-requests: read
    outputs:
      enable-build: ${{ steps.filter.outputs.result }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          sparse-checkout: .github/workflow-filters.yaml
          sparse-checkout-cone-mode: false

      - name: Check if build should be run
        id: filter
        uses: euc-eng/filter-paths@v1
        with:
          filtersFile: .github/workflow-filters.yaml
          label: hcmac

  build-hcmac:
    needs: file-check
    if: ${{ needs.file-check.outputs.enable-build == 'true' }}
    runs-on: [self-hosted, macbuildp-atl, sip_disabled]
    permissions:
      actions: read
      contents: read
      pull-requests: read
    steps:
      - name: Initialize environment variables
        run: |
          echo "NOTARIZE_BUNDLE=False" >> $GITHUB_ENV
        shell: bash

      - name: Set notarization flag
        if: ${{ inputs.notarizing ||
                (github.event_name == 'workflow_dispatch' &&
                 inputs.buildtype == 'release' &&
                 (startsWith(github.ref, 'refs/heads/release/') ||
                  github.ref == 'refs/heads/main')) }}
        run: echo "NOTARIZE_BUNDLE=True" >> $GITHUB_ENV
        shell: bash

      - name: Checkout code
        uses: actions/checkout@v4
        with:
          lfs: true

      - name: Run SCons
        uses: ./.github/actions/scons
        with:
          buildtype: ${{ github.event_name == 'pull_request' && 'obj' || inputs.buildtype }}
          product: hccrt
          extraParams: NOTARIZE_BUNDLE=${{ env.NOTARIZE_BUNDLE }} FOSSA_DEPS=1
          conanDevelop: ${{ inputs.conan_develop }}
          conanSandbox: ${{ inputs.conan_sandbox }}
          artifactoryApiToken: ${{ secrets.ARTIFACTORY_GITHUB_DELIVERABLES_API_TOKEN }}
          artifactoryUrl: ${{ vars.ARTIFACTORY_GITHUB_DELIVERABLES_URL }}
          artifactoryReadToken: ${{ secrets.ORG_OMNISSA_ART_READONLY_TOKEN }}
          artifactoryReadUser: ${{ vars.ORG_OMNISSA_ART_READONLY_USER }}
          macRunnerKeychainPassword: ${{ secrets.HORIZON_MAC_RUNNER_KEYCHAIN_PASSWORD }}
          macNotarizeAppSpecificPassword: ${{ secrets.HORIZON_MAC_NOTARIZE_APP_SPECIFIC_PASSWORD }}

      - name: Run Fossa
        if: ${{ inputs.buildtype == 'release' || github.event_name == 'pull_request' }}
        uses: ./.github/actions/fossa
        with:
          product: 'hcmac'
          fossa-api-key: ${{ secrets.ORG_OMNISSA_FOSSA_KEY }}
          omnissaArtifactoryToken: ${{ secrets.ORG_OMNISSA_ART_READONLY_TOKEN }}

  UT:
    # Suspend ut workflow due to host migration of ut runners.
    needs: build-hcmac
    if: ${{ vars.ENABLE_UNIT_TEST_HOSTED_IN_BJ_MAC == 'true' }}
    secrets: inherit
    uses: ./.github/workflows/hcmac_ut.yaml
    with:
      buildtype:  ${{ github.event_name == 'pull_request' && 'obj' || inputs.buildtype }}

  hcmac-overall-status:
    needs:
      - build-hcmac
      - UT
    if: ${{ !cancelled() }}
    timeout-minutes: 10
    runs-on: [lnxbuild-gh, self-hosted]
    permissions:
      actions: write
      contents: read
      pull-requests: read
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Check overall workflow status
        uses: ./.github/actions/check-status
        with:
          workflowId: 'hcmac.yaml'
          jobs: ${{ toJson(needs) }}
          buildtype: ${{ inputs.buildtype }}
          slackWebhookUrl: ${{ secrets.CART_SLACK_WEBHOOK_URL }}
          dashboardUrl: ${{ vars.CART_BUILD_DASHBOARD }}
          slackBranches: ${{ vars.DAILY_BUILD_BRANCHES }}
