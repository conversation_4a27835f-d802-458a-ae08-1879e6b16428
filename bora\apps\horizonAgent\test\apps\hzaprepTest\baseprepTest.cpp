/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * baseprepTest.cpp
 *
 *      Unit tests for BasePrep, as part of Nutanix support for
 *      Horizon Agent.
 */

#include <functional>
#include "utMock.h"
#include "gtest/gtest.h"

#include <Windows.h>
#include <WinSock2.h>
#include <vdm_plugins.h>
#include "MessageFrameWork.h"

using namespace CORE;

#include <WtsApi32.h>
#include "mfwService.h"
#include "mfw-helpers.h"
#include "gmock-helpers.h"

#include "stdafx.h"
#include "basePrep.h"

using namespace testing;

// Defines
#define MOCK_HINT L"mockHint"
#define EMPTY_BROKER_NAME L""
#define MOCK_BROKER_FQDN L"mockBrokerFQDN"
#define MOCK_QUEUE_NAME L"mockQueueName"
#define MOCK_MESSAGE_HINT L"mockHint"

#define SERVER_DN_REG_PATH L"HKLM\\" HORIZON_VDM_NODE_MANAGER_REG_ROOT_W "\\Server DN"

const wstr mMasterKeyName = L"HPm";
const wstr mPHsKeyPairName = L"PHs";

class BasePrepTest : public Test {
protected:
   int getParGen(PropertyBag &bag) { return bag.getInt(PAR_GEN, 0); }

   void freeMsgBinary(MsgBinary *msgBinary)
   {
      if (msgBinary == NULL) {
         return;
      }

      delete msgBinary;
   }
};


/*
 *-----------------------------------------------------------------------------
 *
 * basePrepTest::persistentKeyPairEmptyNameTest
 *
 *      Tests an attempt to create the persistent key pair with an empty name.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(BasePrepTest, persistentKeyPairEmptyNameTest)
{
   // Set up the test
   wstr keyPairName = L"";
   BasePrep basePrep;

   // Assert result = false
   EXPECT_FALSE(basePrep.createPersistentKeyPair(keyPairName, 0));
}


/*
 *-----------------------------------------------------------------------------
 *
 * basePrepTest::persistentKeyPairHintLatestBrokenTest
 *
 *      Tests an attempt to create the persistent key pair where the
 *      kvCallWithRetry() call with HINT_LATEST returns kvCallBroken, meaning
 *      we're unable to get the message processed by KeyVault.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(BasePrepTest, persistentKeyPairHintLatestBrokenTest)
{
   // Set up the test
   PropertyBag response;
   wstr masterKeyName = L"HPm";

   VMOCK(&BasePrep::kvCallWithRetry)
      .ExpectCall(StrEq(HINT_LATEST), propertyNameEquals(PAR_NAME, masterKeyName), GMOCK_BLANK_ARG3)
      .WillOnce(DoAll(SetArgReferee<2>(response), Return(BasePrep::kvCallResult::kvCallBroken)));

   VMOCK(&BasePrep::kvCallWithRetry).ExpectCall(StrEq(HINT_ADD_MASTER), GMOCK_BLANK_ARG4).Times(0);

   BasePrep basePrep;
   EXPECT_FALSE(basePrep.createPersistentKeyPair(L"mockPersistentKeyPairName", VAL_KEY_LENGTH));
}


/*
 *-----------------------------------------------------------------------------
 *
 * basePrepTest::persistentKeyPairHintLatestFailThenBrokenTest
 *
 *      Tests an attempt to create the persistent key pair where the initial
 *      kvCallWithRetry() with HINT_LATEST returns kvCallFailure, then the
 *      second kvCallWithRetry() call with HINT_ADD_MASTER returns
 *      kvCallBroken.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(BasePrepTest, persistentKeyPairHintLatestFailThenBrokenTest)
{
   PropertyBag response;
   wstr masterKeyName = L"HPm";

   VMOCK(&BasePrep::kvCallWithRetry)
      .ExpectCall(StrEq(HINT_LATEST), propertyNameEquals(PAR_NAME, masterKeyName), GMOCK_BLANK_ARG3)
      .WillOnce(DoAll(SetArgReferee<2>(response), Return(BasePrep::kvCallResult::kvCallFailure)));

   VMOCK(&BasePrep::kvCallWithRetry)
      .ExpectCall(StrEq(HINT_ADD_MASTER),
                  twoPropertyNamesEqual(PAR_NAME, masterKeyName, PAR_LENGTH, 2048),
                  GMOCK_BLANK_ARG3)
      .WillOnce(DoAll(SetArgReferee<2>(response), Return(BasePrep::kvCallResult::kvCallBroken)));

   VMOCK(&BasePrep::kvCallWithRetry).ExpectCall(StrEq(HINT_ADD_KEYPAIR), GMOCK_BLANK_ARG4).Times(0);

   BasePrep basePrep;
   EXPECT_FALSE(basePrep.createPersistentKeyPair(L"mockPersistentKeyPairName", VAL_KEY_LENGTH));
}


/*
 *-----------------------------------------------------------------------------
 *
 * basePrepTest::persistentKeyPairHintLatestFailTwiceTest
 *
 *      Tests an attempt to create the persistent key pair where both the
 *      kvCallWithRetry() with HINT_LATEST and kvCallWithRetry() with
 *      HINT_ADD_MASTER returns kvCallFailure.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(BasePrepTest, persistentKeyPairHintLatestFailTwiceTest)
{
   PropertyBag response;
   wstr masterKeyName = L"HPm";

   // unexpected call
   VMOCK(&BasePrep::kvCallWithRetry)
      .ExpectCall(StrEq(HINT_LATEST), propertyNameEquals(PAR_NAME, masterKeyName), GMOCK_BLANK_ARG3)
      .WillOnce(DoAll(SetArgReferee<2>(response), Return(BasePrep::kvCallResult::kvCallFailure)));

   VMOCK(&BasePrep::kvCallWithRetry)
      .ExpectCall(StrEq(HINT_ADD_MASTER),
                  twoPropertyNamesEqual(PAR_NAME, masterKeyName, PAR_LENGTH, 2048),
                  GMOCK_BLANK_ARG3)
      .WillOnce(DoAll(SetArgReferee<2>(response), Return(BasePrep::kvCallResult::kvCallFailure)));

   VMOCK(&BasePrep::kvCallWithRetry).ExpectCall(StrEq(HINT_ADD_KEYPAIR), GMOCK_BLANK_ARG4).Times(0);

   BasePrep basePrep;
   EXPECT_FALSE(basePrep.createPersistentKeyPair(L"mockPersistentKeyPairName", VAL_KEY_LENGTH));
}


/*
 *-----------------------------------------------------------------------------
 *
 * basePrepTest::persistentKeyPairParGenFailTest
 *
 *      Tests an attempt to create the persistent key pair where
 *      kvCallWithRetry() with HINT_LATEST fails, then kvCallWithRetry() with
 *      HINT_ADD_MASTER succeeds, but getting PAR_GEN returns 0.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(BasePrepTest, persistentKeyPairParGenFailTest)
{
   wstr masterKeyName = L"HPm";
   PropertyBag response;
   response.addInt(PAR_GEN, 0);

   VMOCK(&BasePrep::kvCallWithRetry)
      .ExpectCall(StrEq(HINT_LATEST), propertyNameEquals(PAR_NAME, masterKeyName), GMOCK_BLANK_ARG3)
      .WillOnce(DoAll(SetArgReferee<2>(response), Return(BasePrep::kvCallResult::kvCallFailure)));

   VMOCK(&BasePrep::kvCallWithRetry)
      .ExpectCall(StrEq(HINT_ADD_MASTER),
                  twoPropertyNamesEqual(PAR_NAME, masterKeyName, PAR_LENGTH, 2048),
                  GMOCK_BLANK_ARG3)
      .WillOnce(DoAll(SetArgReferee<2>(response), Return(BasePrep::kvCallResult::kvCallSuccess)));

   EXPECT_FALSE(getParGen(response) != 0);

   VMOCK(&BasePrep::kvCallWithRetry).ExpectCall(StrEq(HINT_ADD_KEYPAIR), GMOCK_BLANK_ARG4).Times(0);

   BasePrep basePrep;
   EXPECT_FALSE(basePrep.createPersistentKeyPair(L"mockPersistentKeyPairName", VAL_KEY_LENGTH));
}


/*
 *-----------------------------------------------------------------------------
 *
 * basePrepTest::persistentKeyPairHintAddKeyPairBrokenTest
 *
 *      Tests an attempt to create the persistent key pair where
 *      kvCallWithRetry() with HINT_LATEST returns kvCallSuccess, getting
 *      PAR_GEN returns nonzero, then kvCallWithRetry() with HINT_ADD_KEYPAIR
 *      returns kvCallBroken.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(BasePrepTest, persistentKeyPairHintAddKeyPairBrokenTest)
{
   wstr masterKeyName = L"HPm";
   wstr persistentKeyPairName = L"mockPersistentKeyPairName";
   PropertyBag response;
   response.addInt(PAR_GEN, 1);

   VMOCK(&BasePrep::kvCallWithRetry)
      .ExpectCall(StrEq(HINT_LATEST), propertyNameEquals(PAR_NAME, masterKeyName), GMOCK_BLANK_ARG3)
      .WillOnce(DoAll(SetArgReferee<2>(response), Return(BasePrep::kvCallResult::kvCallSuccess)));

   EXPECT_TRUE(getParGen(response) != 0);

   response.clear();

   VMOCK(&BasePrep::kvCallWithRetry)
      .ExpectCall(StrEq(HINT_ADD_KEYPAIR), propertyNameEquals(PAR_NAME, persistentKeyPairName),
                  GMOCK_BLANK_ARG3)
      .WillOnce(DoAll(SetArgReferee<2>(response), Return(BasePrep::kvCallResult::kvCallBroken)));

   BasePrep basePrep;
   EXPECT_FALSE(basePrep.createPersistentKeyPair(persistentKeyPairName, VAL_KEY_LENGTH));
}


/*
 *-----------------------------------------------------------------------------
 *
 * basePrepTest::persistentKeyPairHintAddKeyPairFailTest
 *
 *      Tests an attempt to create the persistent key pair where
 *      kvCallWithRetry() with HINT_LATEST returns kvCallSuccess, getting
 *      PAR_GEN returns nonzero, then kvCallWithRetry() with HINT_ADD_KEYPAIR
 *      returns kvCallFailure.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(BasePrepTest, persistentKeyPairHintAddKeyPairFailTest)
{
   wstr masterKeyName = L"HPm";
   wstr persistentKeyPairName = L"mockPersistentKeyPairName";
   PropertyBag response;
   response.addInt(PAR_GEN, 1);

   VMOCK(&BasePrep::kvCallWithRetry)
      .ExpectCall(StrEq(HINT_LATEST), propertyNameEquals(PAR_NAME, masterKeyName), GMOCK_BLANK_ARG3)
      .WillOnce(DoAll(SetArgReferee<2>(response), Return(BasePrep::kvCallResult::kvCallSuccess)));

   EXPECT_TRUE(getParGen(response) != 0);

   response.clear();

   VMOCK(&BasePrep::kvCallWithRetry)
      .ExpectCall(StrEq(HINT_ADD_KEYPAIR), propertyNameEquals(PAR_NAME, persistentKeyPairName),
                  GMOCK_BLANK_ARG3)
      .WillOnce(DoAll(SetArgReferee<2>(response), Return(BasePrep::kvCallResult::kvCallFailure)));

   BasePrep basePrep;
   EXPECT_FALSE(basePrep.createPersistentKeyPair(persistentKeyPairName, VAL_KEY_LENGTH));
}


/*
 *-----------------------------------------------------------------------------
 *
 * basePrepTest::persistentKeyPairSuccessTest
 *
 *      Tests a successful attempt to create the persistent key pair.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(BasePrepTest, persistentKeyPairSuccessTest)
{
   wstr masterKeyName = L"HPm";
   wstr persistentKeyPairName = L"mockPersistentKeyPairName";
   PropertyBag response;
   response.addInt(PAR_GEN, 1);

   VMOCK(&BasePrep::kvCallWithRetry)
      .ExpectCall(StrEq(HINT_LATEST), propertyNameEquals(PAR_NAME, masterKeyName), GMOCK_BLANK_ARG3)
      .WillOnce(DoAll(SetArgReferee<2>(response), Return(BasePrep::kvCallResult::kvCallSuccess)));

   EXPECT_TRUE(getParGen(response) != 0);

   response.clear();

   VMOCK(&BasePrep::kvCallWithRetry)
      .ExpectCall(StrEq(HINT_ADD_KEYPAIR), propertyNameEquals(PAR_NAME, persistentKeyPairName),
                  GMOCK_BLANK_ARG3)
      .WillOnce(DoAll(SetArgReferee<2>(response), Return(BasePrep::kvCallResult::kvCallSuccess)));

   BasePrep basePrep;
   EXPECT_TRUE(basePrep.createPersistentKeyPair(persistentKeyPairName, VAL_KEY_LENGTH));
}


/*
 *-----------------------------------------------------------------------------
 *
 * basePrepTest::removeEmptyKeyPairNameTest
 *
 *      Tests an attempt to remove a key pair with an empty name from the
 *      KeyVault.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(BasePrepTest, removeEmptyKeyPairNameTest)
{
   // Set up variables
   PropertyBag response;
   wstr keyPairName = L"";

   VMOCK(&BasePrep::kvCallWithRetry).ExpectCall(StrEq(HINT_REMOVE), GMOCK_BLANK_ARG4).Times(0);

   // Assert result = false
   BasePrep basePrep;
   EXPECT_FALSE(basePrep.remove(keyPairName));
}


/*
 *-----------------------------------------------------------------------------
 *
 * basePrepTest::removeKeyPairBrokenTest
 *
 *      Tests an attempt to remove a key pair from the KeyVault where
 *      kvCallWithRetry() with HINT_REMOVE returns kvCallBroken.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(BasePrepTest, removeKeyPairBrokenTest)
{
   // Set up variables
   PropertyBag response;
   wstr keyPairName = L"keyPair";

   VMOCK(&BasePrep::kvCallWithRetry)
      .ExpectCall(StrEq(HINT_REMOVE), propertyNameEquals(PAR_NAME, keyPairName), GMOCK_BLANK_ARG3)
      .WillOnce(DoAll(SetArgReferee<2>(response), Return(BasePrep::kvCallResult::kvCallBroken)));

   // Assert result = false
   BasePrep basePrep;
   EXPECT_FALSE(basePrep.remove(keyPairName));
}


/*
 *-----------------------------------------------------------------------------
 *
 * basePrepTest::removeKeyPairFailTest
 *
 *      Tests an attempt to remove a key pair from the KeyVault where
 *      kvCallWithRetry() with HINT_REMOVE returns kvCallFailure.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(BasePrepTest, removeKeyPairFailTest)
{
   // Set up variables
   PropertyBag response;
   wstr keyPairName = L"keyPair";

   VMOCK(&BasePrep::kvCallWithRetry)
      .ExpectCall(StrEq(HINT_REMOVE), propertyNameEquals(PAR_NAME, keyPairName), GMOCK_BLANK_ARG3)
      .WillOnce(DoAll(SetArgReferee<2>(response), Return(BasePrep::kvCallResult::kvCallFailure)));

   BasePrep basePrep;
   EXPECT_FALSE(basePrep.remove(keyPairName));
}


/*
 *-----------------------------------------------------------------------------
 *
 * basePrepTest::removeKeyPairSuccessTest
 *
 *      Tests a successful attempt to remove a key pair in the KeyVault.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(BasePrepTest, removeKeyPairSuccessTest)
{
   PropertyBag response;
   wstr keyPairName = L"keyPair";

   VMOCK(&BasePrep::kvCallWithRetry)
      .ExpectCall(StrEq(HINT_REMOVE), propertyNameEquals(PAR_NAME, keyPairName), GMOCK_BLANK_ARG3)
      .WillOnce(DoAll(SetArgReferee<2>(response), Return(BasePrep::kvCallResult::kvCallSuccess)));

   BasePrep basePrep;
   EXPECT_TRUE(basePrep.remove(keyPairName));
}


/*
 *-----------------------------------------------------------------------------
 *
 * basePrepTest::generateNonceBrokenTest
 *
 *      Tests an attempt to generate a nonce where kvCallWithRetry() with
 *      HINT_GET_RANDOM returns kvCallBroken.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(BasePrepTest, generateNonceBrokenTest)
{
   // Set up variables
   int length = 16;
   MsgBinary *nonce = new MsgBinary();

   PropertyBag response;

   VMOCK(&BasePrep::kvCallWithRetry)
      .ExpectCall(StrEq(HINT_GET_RANDOM), propertyNameEquals(PAR_LENGTH, length), _, 0, nonce)
      .WillOnce(DoAll(SetArgReferee<2>(response), Return(BasePrep::kvCallResult::kvCallBroken)));

   // Get result of function
   BasePrep basePrep;
   EXPECT_FALSE(basePrep.generateNonce(length, *nonce));

   freeMsgBinary(nonce);
}


/*
 *-----------------------------------------------------------------------------
 *
 * basePrepTest::generateNonceFailTest
 *
 *      Tests an attempt to generate a nonce where kvCallWithRetry() with
 *      HINT_GET_RANDOM returns kvCallFailure.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(BasePrepTest, generateNonceFailTest)
{
   // Set up variables
   int length = 16;
   MsgBinary *nonce = new MsgBinary();

   PropertyBag response;

   VMOCK(&BasePrep::kvCallWithRetry)
      .ExpectCall(StrEq(HINT_GET_RANDOM), propertyNameEquals(PAR_LENGTH, length), _, 0, nonce)
      .WillOnce(DoAll(SetArgReferee<2>(response), Return(BasePrep::kvCallResult::kvCallFailure)));

   // Get result of function
   BasePrep basePrep;
   EXPECT_FALSE(basePrep.generateNonce(length, *nonce));

   freeMsgBinary(nonce);
}


/*
 *-----------------------------------------------------------------------------
 *
 * basePrepTest::generateNonceSuccessTest
 *
 *      Tests a successful attempt to generate a nonce
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(BasePrepTest, generateNonceSuccessTest)
{
   // Set up variables
   int length = 16;
   MsgBinary *nonce = new MsgBinary();

   PropertyBag response;

   VMOCK(&BasePrep::kvCallWithRetry)
      .ExpectCall(StrEq(HINT_GET_RANDOM), propertyNameEquals(PAR_LENGTH, length), _, 0, nonce)
      .WillOnce(DoAll(SetArgReferee<2>(response), Return(BasePrep::kvCallResult::kvCallSuccess)));

   // Get result of function
   BasePrep basePrep;
   EXPECT_TRUE(basePrep.generateNonce(length, *nonce));

   freeMsgBinary(nonce);
}


/*
 *-----------------------------------------------------------------------------
 *
 * basePrepTest::getVmGuidServerDnNotSetTest
 *
 *      Tests an attempt to get VM GUID where the server DN is not set in the
 *      registry.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(BasePrepTest, getVmGuidServerDnNotSetTest)
{
   // Set variables
   wstr vmGuid = L"";

   // Mock readRegistry
   VMOCK((FPreadRegistry)&wstr::readRegistry)
      .ExpectCall(StrEq(SERVER_DN_REG_PATH), _, _)
      .WillOnce(Return(L""));

   // Mock CORE::ldapUtils::nameFromDn
   VMOCK(&CORE::ldapUtils::nameFromDn).ExpectCall(_).Times(0);

   BasePrep basePrep;
   EXPECT_FALSE(basePrep.getVmGuid(vmGuid));
   EXPECT_TRUE(vmGuid.empty());
}


/*
 *-----------------------------------------------------------------------------
 *
 * basePrepTest::getVmGuidEmptyTest
 *
 *      Tests an attempt to get VM GUID where the server DN exists in registry,
 *      but the VM GUID is empty.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(BasePrepTest, getVmGuidEmptyTest)
{
   wstr vmGuid = L"";
   wstr mockServerDn = L"mockServerDn";
   wstr mockNameFromDn = L"";

   // Mock readRegistry
   VMOCK((FPreadRegistry)&wstr::readRegistry)
      .ExpectCall(StrEq(SERVER_DN_REG_PATH), _, _)
      .WillOnce(Return(mockServerDn));

   VMOCK(&CORE::ldapUtils::nameFromDn).ExpectCall(mockServerDn).WillOnce(Return(mockNameFromDn));

   BasePrep basePrep;
   EXPECT_FALSE(basePrep.getVmGuid(vmGuid));
   EXPECT_TRUE(vmGuid.empty());
}


/*
 *-----------------------------------------------------------------------------
 *
 * basePrepTest::getVmGuidSuccessTest
 *
 *      Tests a successful attempt to get VM GUID.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(BasePrepTest, getVmGuidSuccessTest)
{
   wstr vmGuid = L"";
   wstr mockServerDn = L"mockServerDn";
   wstr mockNameFromDn = L"mockGuid";

   // Mock readRegistry
   VMOCK((FPreadRegistry)&wstr::readRegistry)
      .ExpectCall(StrEq(SERVER_DN_REG_PATH), _, _)
      .WillOnce(Return(mockServerDn));

   VMOCK(&CORE::ldapUtils::nameFromDn).ExpectCall(mockServerDn).WillOnce(Return(mockNameFromDn));

   BasePrep basePrep;
   EXPECT_TRUE(basePrep.getVmGuid(vmGuid));
   EXPECT_TRUE(!vmGuid.empty());
   EXPECT_STREQ(vmGuid, mockNameFromDn);
}


/*
 *-----------------------------------------------------------------------------
 *
 * basePrepTest::keyExistsBrokenTest
 *
 *      Tests an attempt to look for a keyName in the key vault, but fails to
 *      execute the KeyVault operation.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(BasePrepTest, keyExistsBrokenTest)
{
   wstr keyName = L"keyName";

   VMOCK(&BasePrep::kvCallWithRetry)
      .ExpectCall(StrEq(HINT_LATEST), propertyNameEquals(PAR_NAME, keyName), GMOCK_BLANK_ARG3)
      .WillOnce(Return(BasePrep::kvCallResult::kvCallBroken));

   // Mock function expect return false
   BasePrep basePrep;
   EXPECT_FALSE(basePrep.keyExists(keyName));
}


/*
 *-----------------------------------------------------------------------------
 *
 * basePrepTest::keyExistsFailureTest
 *
 *      Tests an attempt to look for a keyName in the key vault, but fails to
 *      check that the key exists.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(BasePrepTest, keyExistsFailureTest)
{
   wstr keyName = L"keyName";
   PropertyBag response;

   VMOCK(&BasePrep::kvCallWithRetry)
      .ExpectCall(StrEq(HINT_LATEST), propertyNameEquals(PAR_NAME, keyName), GMOCK_BLANK_ARG3)
      .WillOnce(Return(BasePrep::kvCallResult::kvCallFailure));

   // Mock function expect return false
   BasePrep basePrep;
   EXPECT_FALSE(basePrep.keyExists(keyName));
}


/*
 *-----------------------------------------------------------------------------
 *
 * basePrepTest::keyExistsErrorPresentTest
 *
 *      Tests an attempt to look for a keyName in the key vault, but the key
 *      check fails after successfully executing the KeyVault operation.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(BasePrepTest, keyExistsErrorPresentTest)
{
   wstr keyName = L"keyName";

   VMOCK(&BasePrep::kvCallWithRetry)
      .ExpectCall(StrEq(HINT_LATEST), propertyNameEquals(PAR_NAME, keyName), GMOCK_BLANK_ARG3)
      .WillOnce([](auto, auto, PropertyBag &response, auto, auto) {
         response.setError(1, L"");
         return BasePrep::kvCallResult::kvCallSuccess;
      });

   BasePrep basePrep;
   EXPECT_FALSE(basePrep.keyExists(keyName));
}


/*
 *-----------------------------------------------------------------------------
 *
 * basePrepTest::keyExistsSuccessTest
 *
 *      Tests a successful attempt to look for a keyName in the key vault.
 *
 *      TODO test case is currently disabled because a bug was discovered
 *      in production code. Will add this test case back in once the bug
 *      is resolved.
 *
 *-----------------------------------------------------------------------------
 */

// TEST_F(basePrepTest, keyExistsSuccessTest)
// {
//     wstr keyName = L"keyName";

//     VMOCK(&BasePrep::kvCallWithRetry)
//         .ExpectCall(StrEq(HINT_LATEST), propertyNameEquals(PAR_NAME, keyName), _, _, _)
//         .WillOnce([](const wstr, const PropertyBag &params, PropertyBag &response, MsgBinary *,
//         MsgBinary *)
//         {
//             response.setError(0, L"");
//             return BasePrep::kvCallResult::kvCallSuccess;
//         });

//     BasePrep basePrep;
//     EXPECT_TRUE(basePrep.keyExists(keyName));
// }


/*
 *-----------------------------------------------------------------------------
 *
 * basePrepTest::getPublicKeyEmptyKeyNameTest
 *
 * Tests an attempt to get the public key where keyName is empty.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(BasePrepTest, getPublicKeyEmptyKeyNameTest)
{
   // Set up variables
   wstr keyName = L"";
   wstr sigData = L"";
   MsgBinary pubKey, signature;

   VMOCK(&BasePrep::kvCallWithRetry).ExpectCall(StrEq(HINT_GET_PUBKEY), GMOCK_BLANK_ARG4).Times(0);

   BasePrep basePrep;
   EXPECT_FALSE(basePrep.getPublicKey(keyName, sigData, pubKey, signature));
}


/*
 *-----------------------------------------------------------------------------
 *
 * basePrepTest::getPublicKeyEmptySigDataBrokenTest
 *
 *      Tests an attempt to get the public key where sigData is empty and
 *      kvCallWithRetry() with HINT_GET_PUBKEY returns kvCallBroken.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(BasePrepTest, getPublicKeyEmptySigDataKvCallBrokenTest)
{
   // Set up variables
   wstr keyName = L"keyName";
   wstr sigData = L"";
   MsgBinary pubKey, signature;

   VMOCK(&BasePrep::kvCallWithRetry)
      .ExpectCall(StrEq(HINT_GET_PUBKEY), propertyNameEquals(PAR_NAME, keyName), GMOCK_BLANK_ARG3)
      .WillOnce(Return(BasePrep::kvCallResult::kvCallBroken));

   // Mock function expect return false
   BasePrep basePrep;
   EXPECT_FALSE(basePrep.getPublicKey(keyName, sigData, pubKey, signature));
}


/*
 *-----------------------------------------------------------------------------
 *
 * basePrepTest::getPublicKeyEmptySigDataFailureTest
 *
 *      Tests an attempt to get the public key where sigData is empty and
 *      kvCallWithRetry() with HINT_GET_PUBKEY returns kvCallFailure.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(BasePrepTest, getPublicKeyEmptySigDataKvCallFailureTest)
{
   wstr keyName = L"keyName";
   wstr sigData = L"";
   MsgBinary pubKey, signature;

   VMOCK(&BasePrep::kvCallWithRetry)
      .ExpectCall(StrEq(HINT_GET_PUBKEY), propertyNameEquals(PAR_NAME, keyName), GMOCK_BLANK_ARG3)
      .WillOnce(Return(BasePrep::kvCallResult::kvCallFailure));

   BasePrep basePrep;
   EXPECT_FALSE(basePrep.getPublicKey(keyName, sigData, pubKey, signature));
}


/*
 *-----------------------------------------------------------------------------
 *
 * basePrepTest::getPublicKeyEmptySigDataSuccessTest
 *
 *      Tests an attempt to get the public key where sigData is empty and
 *      kvCallWithRetry() with HINT_GET_PUBKEY returns kvCallSuccess.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(BasePrepTest, getPublicKeyEmptySigDataKvCallSuccessTest)
{
   wstr keyName = L"keyName";
   wstr sigData = L"";
   MsgBinary pubKey, signature;
   PropertyBag response;

   VMOCK(&BasePrep::kvCallWithRetry)
      .ExpectCall(StrEq(HINT_GET_PUBKEY), propertyNameEquals(PAR_NAME, keyName), GMOCK_BLANK_ARG3)
      .WillOnce(DoAll(SetArgReferee<2>(response), Return(BasePrep::kvCallResult::kvCallSuccess)));

   BasePrep basePrep;
   EXPECT_TRUE(basePrep.getPublicKey(keyName, sigData, pubKey, signature));
}


/*
 *-----------------------------------------------------------------------------
 *
 * basePrepTest::getPublicKeyNonEmptySigDataSuccessTest
 *
 *      Tests an attempt to get the public key where sigData is not empty and
 *      kvCallWithRetry() with HINT_GET_PUBKEY returns kvCallSuccess.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(BasePrepTest, getPublicKeyNonEmptySigDataSuccessTest)
{
   wstr keyName = L"keyName";
   wstr sigDataToSign = L"sigData";
   mstr pubKeyData = "somePubKeyData";
   MsgBinary pubKey;
   MsgBinary signature;
   PropertyBag response;

   // Used for validating the params PropertyBag
   PropertyBag mockParams;
   mockParams.addBinary(PAR_BINDATATOSIGN, sigDataToSign.p(), sigDataToSign.length());

   pubKey.set(pubKeyData.p_upd(), pubKeyData.s(), true);

   mstr sigDataHashed = "hashedsigdata";
   response.setBinary(PAR_BINDATASIGNED, sigDataHashed.p(), sigDataHashed.s());

   VMOCK(&BasePrep::kvCallWithRetry)
      .ExpectCall(StrEq(HINT_GET_PUBKEY),
                  twoPropertyNamesEqual(PAR_NAME, keyName, PAR_BINDATATOSIGN,
                                        mockParams.get(PAR_BINDATATOSIGN)),
                  GMOCK_BLANK_ARG3)
      .WillOnce([&](auto, auto, PropertyBag &resp, auto, MsgBinary *binResp) {
         resp = response;
         binResp = &pubKey;
         return BasePrep::kvCallResult::kvCallSuccess;
      });

   BasePrep basePrep;
   EXPECT_TRUE(basePrep.getPublicKey(keyName, sigDataToSign, pubKey, signature));

   mstr validateSigData;
   validateSigData.setBinary(signature.pBinary, signature.sBinary);
   EXPECT_STREQ(validateSigData.c_str(), sigDataHashed.c_str());

   mstr validatePubKey;
   validatePubKey.setBinary(pubKey.pBinary, pubKey.sBinary);
   EXPECT_STREQ(validatePubKey.c_str(), pubKeyData.c_str());
}


/*
 *-----------------------------------------------------------------------------
 *
 * basePrepTest::connectAndSendEmptyBrokerFqdnTest
 *
 *      Tests an attempt to connect to the specified broker via the Framework
 *      channel, but the broker FQDN is empty.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(BasePrepTest, connectAndSendEmptyBrokerFqdnTest)
{
   wstr brokerFqdn = L"";
   wstr queueName = L"queueName";
   wstr messageHint = L"messageHint";
   PropertyBag request, response;

   VMOCK((FPConnectChannel)&MessageFrameWork::ConnectChannel)
      .ExpectCall(GMOCK_BLANK_ARG10)
      .Times(0);

   VMOCK((FPSendMsg)&MessageFrameWork::SendMsg).ExpectCall(GMOCK_BLANK_ARG14).Times(0);

   VMOCK((FPCloseChannel)&MessageFrameWork::CloseChannel).ExpectCall(_).Times(0);

   BasePrep basePrep;
   EXPECT_FALSE(basePrep.connectAndSend(brokerFqdn, queueName, messageHint, request, response));
}


/*
 *-----------------------------------------------------------------------------
 *
 * basePrepTest::connectAndSendEmptyQueueNameTest
 *
 *      Tests an attempt to connect to the specified broker via the Framework
 *      channel, but the queue name is empty.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(BasePrepTest, connectAndSendEmptyQueueNameTest)
{
   wstr brokerFqdn = L"brokerFqdn";
   wstr queueName = L"";
   wstr messageHint = L"messageHint";
   PropertyBag request, response;

   VMOCK((FPConnectChannel)&MessageFrameWork::ConnectChannel)
      .ExpectCall(GMOCK_BLANK_ARG10)
      .Times(0);

   VMOCK((FPSendMsg)&MessageFrameWork::SendMsg).ExpectCall(GMOCK_BLANK_ARG14).Times(0);

   VMOCK((FPCloseChannel)&MessageFrameWork::CloseChannel).ExpectCall(_).Times(0);

   BasePrep basePrep;
   EXPECT_FALSE(basePrep.connectAndSend(brokerFqdn, queueName, messageHint, request, response));
}


/*
 *-----------------------------------------------------------------------------
 *
 * basePrepTest::connectAndSendEmptyMessageHintTest
 *
 *      Tests an attempt to connect to the specified broker via the Framework
 *      channel, but the message hint is empty.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(BasePrepTest, connectAndSendEmptyMessageHintTest)
{
   wstr brokerFqdn = L"brokerFqdn";
   wstr queueName = L"queueName";
   wstr messageHint = L"";
   PropertyBag request, response;

   VMOCK((FPConnectChannel)&MessageFrameWork::ConnectChannel)
      .ExpectCall(GMOCK_BLANK_ARG10)
      .Times(0);

   VMOCK((FPSendMsg)&MessageFrameWork::SendMsg).ExpectCall(GMOCK_BLANK_ARG14).Times(0);

   VMOCK((FPCloseChannel)&MessageFrameWork::CloseChannel).ExpectCall(_).Times(0);

   BasePrep basePrep;
   EXPECT_FALSE(basePrep.connectAndSend(brokerFqdn, queueName, messageHint, request, response));
}


/*
 *-----------------------------------------------------------------------------
 *
 * basePrepTest::connectAndSendChannelDoesNotExistTest
 *
 *      Tests an attempt to connect to the specified broker via the Framework
 *      channel, but the message channel is null.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(BasePrepTest, connectAndSendChannelDoesNotExistTest)
{
   wstr brokerFqdn = L"brokerFqdn";
   wstr queueName = L"queueName";
   wstr messageHint = L"messageHint";
   PropertyBag request, response;

   VMOCK((FPConnectChannel)&MessageFrameWork::ConnectChannel)
      .ExpectCall(GMOCK_BLANK_ARG10)
      .WillOnce(Return(reinterpret_cast<MessageChannel *>(NULL)));

   VMOCK((FPSendMsg)&MessageFrameWork::SendMsg).ExpectCall(GMOCK_BLANK_ARG14).Times(0);

   VMOCK((FPCloseChannel)&MessageFrameWork::CloseChannel).ExpectCall(_).Times(0);

   BasePrep basePrep;
   EXPECT_FALSE(basePrep.connectAndSend(brokerFqdn, queueName, messageHint, request, response));
}


/*
 *-----------------------------------------------------------------------------
 *
 * basePrepTest::connectAndSendMessageFailTest
 *
 *      Tests an attempt to connect to the specified broker via the Framework
 *      channel, but the attempt to send the message returns MsgError after
 *      a retry attempt.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(BasePrepTest, connectAndSendMessageFailTest)
{
   PropertyBag request, response;

   VMOCK((FPConnectChannel)&MessageFrameWork::ConnectChannel)
      .ExpectCall(MessageFrameWork::Socket, GMOCK_BLANK_ARG9)
      .WillOnce(Return(reinterpret_cast<MessageChannel *>(0x12345678)));

   VMOCK((FPSendMsg)&MessageFrameWork::SendMsg)
      .ExpectCall(StrEq(MOCK_QUEUE_NAME), StrEq(MOCK_MESSAGE_HINT), GMOCK_BLANK_ARG12)
      .WillOnce(DoAll(SetArgReferee<3>(response), Return(CORE::MessageHandler::MsgSystemBusy)))
      .WillOnce(DoAll(SetArgReferee<3>(response), Return(CORE::MessageHandler::MsgError)));

   VMOCK((FPCloseChannel)&MessageFrameWork::CloseChannel)
      .ExpectCall(reinterpret_cast<MessageChannel *>(0x12345678))
      .WillOnce(Return());

   BasePrep basePrep;
   EXPECT_FALSE(basePrep.connectAndSend(MOCK_BROKER_FQDN, MOCK_QUEUE_NAME, MOCK_MESSAGE_HINT,
                                        request, response));
}


/*
 *-----------------------------------------------------------------------------
 *
 * basePrepTest::connectAndSendMessageFailTest
 *
 *      Tests a successful attempt to connect to the specified broker via the
 *      Framework channel.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(BasePrepTest, connectAndSendMessageSuccessTest)
{
   PropertyBag request, response;

   VMOCK((FPConnectChannel)&MessageFrameWork::ConnectChannel)
      .ExpectCall(MessageFrameWork::Socket, _, _, StrEq(MOCK_BROKER_FQDN), GMOCK_BLANK_ARG6)
      .WillOnce(Return(reinterpret_cast<MessageChannel *>(0x12345678)));

   VMOCK((FPSendMsg)&MessageFrameWork::SendMsg)
      .ExpectCall(StrEq(MOCK_QUEUE_NAME), StrEq(MOCK_MESSAGE_HINT), GMOCK_BLANK_ARG12)
      .WillOnce(DoAll(SetArgReferee<3>(response), Return(CORE::MessageHandler::MsgOk)));

   VMOCK((FPCloseChannel)&MessageFrameWork::CloseChannel).ExpectCall(_).WillOnce(Return());

   BasePrep basePrep;
   EXPECT_TRUE(basePrep.connectAndSend(MOCK_BROKER_FQDN, MOCK_QUEUE_NAME, MOCK_MESSAGE_HINT,
                                       request, response));
}


/*
 *-----------------------------------------------------------------------------
 *
 * basePrepTest::connectAndSendMessageFailTest
 *
 *      Tests a successful attempt to connect to the specified broker via the
 *      Framework channel where MFW system is busy at first.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(BasePrepTest, connectAndSendMessageBusyThenSuccessTest)
{
   PropertyBag request, response;

   VMOCK((FPConnectChannel)&MessageFrameWork::ConnectChannel)
      .ExpectCall(MessageFrameWork::Socket, _, _, StrEq(MOCK_BROKER_FQDN), GMOCK_BLANK_ARG6)
      .WillOnce(Return(reinterpret_cast<MessageChannel *>(0x12345678)));

   VMOCK((FPSendMsg)&MessageFrameWork::SendMsg)
      .ExpectCall(StrEq(MOCK_QUEUE_NAME), StrEq(MOCK_MESSAGE_HINT), GMOCK_BLANK_ARG12)
      .WillOnce(DoAll(SetArgReferee<3>(response), Return(CORE::MessageHandler::MsgSystemBusy)))
      .WillOnce(DoAll(SetArgReferee<3>(response), Return(CORE::MessageHandler::MsgOk)));

   VMOCK((FPCloseChannel)&MessageFrameWork::CloseChannel).ExpectCall(_).WillOnce(Return());

   BasePrep basePrep;
   EXPECT_TRUE(basePrep.connectAndSend(MOCK_BROKER_FQDN, MOCK_QUEUE_NAME, MOCK_MESSAGE_HINT,
                                       request, response));
}


/*
 *-----------------------------------------------------------------------------
 *
 * basePrepTest::kvCallSuccessTest
 *
 *      Tests a successful attempt to send a message to the KeyVault queue.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(BasePrepTest, kvCallSuccessTest)
{
   PropertyBag params, response;
   MsgBinary *bin = new MsgBinary();
   MsgBinary *binResp = new MsgBinary();

   // Use any hint (e.g. HINT_ADD_KEYPAIR) to test the function
   VMOCK((FPSendMsg)&MessageFrameWork::SendMsg)
      .ExpectCall(StrEq(QUEUE_KEYVAULT), StrEq(HINT_ADD_KEYPAIR), _, _, _, _, _, _, _, bin, _,
                  binResp, _, _)
      .WillOnce(DoAll(SetArgReferee<3>(response), Return(CORE::MessageHandler::MsgOk)));

   BasePrep basePrep;

   BasePrep::kvCallResult result =
      basePrep.kvCallWithRetry(HINT_ADD_KEYPAIR, params, response, bin, binResp);
   EXPECT_EQ(result, BasePrep::kvCallResult::kvCallSuccess);

   freeMsgBinary(bin);
   freeMsgBinary(binResp);
}


/*
 *-----------------------------------------------------------------------------
 *
 * basePrepTest::kvCallMsgSystemBusyTest
 *
 *      Tests an attempt to send a message to the KeyVault queue where the
 *      KeyVault queue system is busy. In this case, response should be empty.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(BasePrepTest, kvCallMsgSystemBusyTest)
{
   PropertyBag params, response;
   MsgBinary *bin = new MsgBinary();
   MsgBinary *binResp = new MsgBinary();

   response.setError(KeyVaultError::KvErrFailed, L"Key vault operation failed.");

   VMOCK((FPSendMsg)&MessageFrameWork::SendMsg)
      .ExpectCall(StrEq(QUEUE_KEYVAULT), StrEq(HINT_ADD_KEYPAIR), GMOCK_BLANK_ARG12)
      .Times(MAX_KV_RETRIES)
      .WillRepeatedly(
         DoAll(SetArgReferee<3>(response), Return(CORE::MessageHandler::MsgSystemBusy)));

   BasePrep basePrep;
   BasePrep::kvCallResult result =
      basePrep.kvCallWithRetry(HINT_ADD_KEYPAIR, params, response, bin, binResp);
   EXPECT_EQ(result, BasePrep::kvCallResult::kvCallBroken);
   EXPECT_EQ(response.getError(), 0);

   freeMsgBinary(bin);
   freeMsgBinary(binResp);
}


/*
 *-----------------------------------------------------------------------------
 *
 * basePrepTest::kvCallMsgErrorAndKvErrLockedTest
 *
 *      Tests an attempt to send a message to the KeyVault queue where the
 *      attempt returns MsgOk, and the response contains the KV error
 *      KVErrLocked.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(BasePrepTest, kvCallKvErrLockedTest)
{
   PropertyBag params, response;
   MsgBinary *bin = new MsgBinary();
   MsgBinary *binResp = new MsgBinary();

   response.setError(KeyVaultError::KVErrLocked, L"Key vault is locked.");

   // Mock the result of SendMsg to be anything that's not MsgOk, MsgSystemBusy, or MsgError.
   VMOCK((FPSendMsg)&MessageFrameWork::SendMsg)
      .ExpectCall(StrEq(QUEUE_KEYVAULT), StrEq(HINT_ADD_KEYPAIR), GMOCK_BLANK_ARG12)
      .Times(MAX_KV_RETRIES)
      .WillRepeatedly(
         DoAll(SetArgReferee<3>(response), Return(CORE::MessageHandler::respType::MsgSizeTooBig)));

   BasePrep basePrep;
   BasePrep::kvCallResult result =
      basePrep.kvCallWithRetry(HINT_ADD_KEYPAIR, params, response, bin, binResp);
   EXPECT_EQ(result, BasePrep::kvCallResult::kvCallBroken);
   EXPECT_EQ(response.getError(), KeyVaultError::KVErrLocked);

   freeMsgBinary(bin);
   freeMsgBinary(binResp);
}


/*
 *-----------------------------------------------------------------------------
 *
 * basePrepTest::kvCallMsgErrorNoResponseTest
 *
 *      Tests an attempt to send a message to the KeyVault queue where the
 *      attempt returns an error, and the response is empty. In this case
 *      kvCallWithRetry() should return kvCallFailure.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(BasePrepTest, kvCallMsgErrorNoResponseTest)
{
   PropertyBag params, response;
   MsgBinary *bin = new MsgBinary();
   MsgBinary *binResp = new MsgBinary();

   response.setError(KeyVaultError::KvErrFailed, L"Key vault operation failed.");

   VMOCK((FPSendMsg)&MessageFrameWork::SendMsg)
      .ExpectCall(StrEq(QUEUE_KEYVAULT), StrEq(HINT_ADD_KEYPAIR), GMOCK_BLANK_ARG12)
      .Times(MAX_KV_RETRIES)
      .WillRepeatedly(
         DoAll(SetArgReferee<3>(response), Return(CORE::MessageHandler::respType::MsgError)));

   BasePrep basePrep;
   BasePrep::kvCallResult result =
      basePrep.kvCallWithRetry(HINT_ADD_KEYPAIR, params, response, bin, binResp);
   EXPECT_EQ(result, BasePrep::kvCallResult::kvCallFailure);
   EXPECT_EQ(response.getError(), KeyVaultError::KvErrFailed);

   freeMsgBinary(bin);
   freeMsgBinary(binResp);
}


/*
 *-----------------------------------------------------------------------------
 *
 * basePrepTest::kvCallMfwOtherErrTest
 *
 *      Tests an attempt to send a message to the KeyVault queue where MFW
 *      returns SystemBusy (as an example), meaning that we are unable to get
 *      the message processed by KeyVault.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(BasePrepTest, kvCallMfwOtherErrTest)
{
   PropertyBag params, response;
   MsgBinary *bin = new MsgBinary();
   MsgBinary *binResp = new MsgBinary();

   VMOCK((FPSendMsg)&MessageFrameWork::SendMsg)
      .ExpectCall(StrEq(QUEUE_KEYVAULT), StrEq(HINT_ADD_KEYPAIR), GMOCK_BLANK_ARG12)
      .WillOnce(Return(CORE::MessageHandler::MsgSystemBusy));

   BasePrep basePrep;
   BasePrep::kvCallResult result =
      basePrep.kvCallWithRetry(HINT_ADD_KEYPAIR, params, response, bin, binResp);
   EXPECT_EQ(result, BasePrep::kvCallResult::kvCallBroken);

   freeMsgBinary(bin);
   freeMsgBinary(binResp);
}


/*
 *-----------------------------------------------------------------------------
 *
 * basePrepTest::getHostNameSuccessTest
 *
 *      Validate that getHostName() returns the correct hostname.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(BasePrepTest, getHostNameSuccessTest)
{
   wstr hostname = wstr::getComputerName();
   BasePrep basePrep;
   EXPECT_STREQ(basePrep.getHostName(), hostname);
}


/*
 *-----------------------------------------------------------------------------
 *
 * basePrepTest::readRegServerFqdnEmptyTest
 *
 *      Tests an attempt to read the server FQDNs from the registry where
 *      the registry value is empty.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(BasePrepTest, readRegServerFqdnEmptyTest)
{
   // TODO use the macro REG_SRVS_FQDNS
   VMOCK((FPreadRegistry)&wstr::readRegistry)
      .ExpectCall(regPathContains(L"\\Agent\\Configuration\\Broker"), _, _)
      .WillOnce(Return(L""));

   BasePrep basePrep;
   EXPECT_EQ(basePrep.readRegServerFQDNs().size(), 0);
}


/*
 *-----------------------------------------------------------------------------
 *
 * basePrepTest::readRegServerFqdnSuccessTest
 *
 *      Tests an attempt to read the server FQDNs from the registry where
 *      the registry value contains some FQDNs.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(BasePrepTest, readRegServerFqdnSuccessTest)
{
   VMOCK((FPreadRegistry)&wstr::readRegistry)
      .ExpectCall(regPathContains(L"\\Agent\\Configuration\\Broker"), _, _)
      .WillOnce(Return(L"mockFqdn1 mockFqdn2"));

   BasePrep basePrep;
   vwstr serverFQDNs = basePrep.readRegServerFQDNs();
   ASSERT_TRUE(serverFQDNs.size() == 2);
   EXPECT_TRUE(serverFQDNs[0] == L"mockFqdn1" && serverFQDNs[1] == L"mockFqdn2");
}


/*
 *-----------------------------------------------------------------------------
 *
 * basePrepTest::writeRegPoolIdFailTest
 *
 *      Tests an attempt to write the pool ID to the registry where the
 *      registry write fails.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(BasePrepTest, writeRegPoolIdFailTest)
{
   wstr poolID = L"";

   VMOCK((FPwriteRegistry)&wstr::writeRegistry)
      .ExpectCall(regPathContains(L"\\Agent\\Configuration\\PoolID"), REG_SZ, _)
      .WillOnce(Return(false));

   BasePrep basePrep;
   EXPECT_FALSE(basePrep.writeRegPoolID(poolID));
}


/*
 *-----------------------------------------------------------------------------
 *
 * basePrepTest::writeRegPoolIdSuccessTest
 *
 *      Tests a successful attempt to write the pool ID to the registry.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(BasePrepTest, writeRegPoolIdSuccessTest)
{
   wstr poolID = L"mockPoolID";

   VMOCK((FPwriteRegistry)&wstr::writeRegistry)
      .ExpectCall(regPathContains(L"\\Agent\\Configuration\\PoolID"), REG_SZ, _)
      .WillOnce(Return(true));

   BasePrep basePrep;
   EXPECT_TRUE(basePrep.writeRegPoolID(poolID));
}


/*
 *-----------------------------------------------------------------------------
 *
 * basePrepTest::readRegPoolIdEmptyValueTest
 *
 *      Tests an attempt to read the pool ID from the registry where the
 *      readRegistry() call returns empty string.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(BasePrepTest, readRegPoolIdEmptyValueTest)
{
   VMOCK((FPreadRegistry)&wstr::readRegistry)
      .ExpectCall(regPathContains(L"\\Agent\\Configuration\\PoolID"), _, _)
      .WillOnce(Return(L""));

   BasePrep basePrep;
   wstr poolID = basePrep.readRegPoolID();
   EXPECT_TRUE(poolID.empty());
}


/*
 *-----------------------------------------------------------------------------
 *
 * basePrepTest::readRegPoolIdSuccessTest
 *
 *      Tests an attempt to read the pool ID from the registry where the
 *      readRegistry() call returns the pool ID.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(BasePrepTest, readRegPoolIdSuccessTest)
{
   wstr poolID = L"mockPoolID";

   VMOCK((FPreadRegistry)&wstr::readRegistry)
      .ExpectCall(regPathContains(L"\\Agent\\Configuration\\PoolID"), _, _)
      .WillOnce(Return(poolID));

   BasePrep basePrep;
   EXPECT_STREQ(basePrep.readRegPoolID(), poolID);
}