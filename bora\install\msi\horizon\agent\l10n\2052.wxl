﻿<?xml version="1.0" encoding="utf-8"?>

<WixLocalization Culture="zh-cn" Codepage="936" xmlns="http://schemas.microsoft.com/wix/2006/localization">
   <String Id="LANGID">2052</String>

   <!-- Installshield Strings -->
   <String Id="IDS_COMPLUS_PROGRESSTEXT_COST">正在核算 COM+ 应用程序的成本: [1]</String>
   <String Id="IDS_COMPLUS_PROGRESSTEXT_INSTALL">正在安装 COM+ 应用程序: [1]</String>
   <String Id="IDS_COMPLUS_PROGRESSTEXT_UNINSTALL">正在卸载 COM+ 应用程序: [1]</String>
   <String Id="IDS_PROGMSG_IIS_CREATEAPPPOOL">正在创建应用程序池 %s</String>
   <String Id="IDS_PROGMSG_IIS_CREATEAPPPOOLS">正在创建应用程序池...</String>
   <String Id="IDS_PROGMSG_IIS_CREATEVROOT">正在创建 IIS 虚拟目录 %s</String>
   <String Id="IDS_PROGMSG_IIS_CREATEVROOTS">正在创建 IIS 虚拟目录...</String>
   <String Id="IDS_PROGMSG_IIS_CREATEWEBSERVICEEXTENSION">正在创建 Web 服务扩展</String>
   <String Id="IDS_PROGMSG_IIS_CREATEWEBSERVICEEXTENSIONS">正在创建 Web 服务扩展...</String>
   <String Id="IDS_PROGMSG_IIS_EXTRACT">正在提取 IIS 虚拟目录的信息...</String>
   <String Id="IDS_PROGMSG_IIS_EXTRACTDONE">已提取 IIS 虚拟目录信息...</String>
   <String Id="IDS_PROGMSG_IIS_REMOVEAPPPOOL">正在删除应用程序池</String>
   <String Id="IDS_PROGMSG_IIS_REMOVEAPPPOOLS">正在删除应用程序池...</String>
   <String Id="IDS_PROGMSG_IIS_REMOVESITE">正在从端口 %d 删除网站</String>
   <String Id="IDS_PROGMSG_IIS_REMOVEVROOT">正在删除 IIS 虚拟目录 %s</String>
   <String Id="IDS_PROGMSG_IIS_REMOVEVROOTS">正在删除 IIS 虚拟目录...</String>
   <String Id="IDS_PROGMSG_IIS_REMOVEWEBSERVICEEXTENSION">正在删除 Web 服务扩展</String>
   <String Id="IDS_PROGMSG_IIS_REMOVEWEBSERVICEEXTENSIONS">正在删除 Web 服务扩展...</String>
   <String Id="IDS_PROGMSG_IIS_ROLLBACKAPPPOOLS">正在回滚应用程序池...</String>
   <String Id="IDS_PROGMSG_IIS_ROLLBACKVROOTS">正在回滚虚拟目录和网站的更改...</String>
   <String Id="IDS_PROGMSG_IIS_ROLLBACKWEBSERVICEEXTENSIONS">正在回滚 Web 服务扩展...</String>
   <String Id="IDS_PROGMSG_XML_COSTING">正在核算 XML 文件的成本...</String>
   <String Id="IDS_PROGMSG_XML_CREATE_FILE">正在创建 XML 文件 %s...</String>
   <String Id="IDS_PROGMSG_XML_FILES">正在执行 XML 文件的更改...</String>
   <String Id="IDS_PROGMSG_XML_REMOVE_FILE">正在删除 XML 文件 %s...</String>
   <String Id="IDS_PROGMSG_XML_ROLLBACK_FILES">正在回滚 XML 文件更改...</String>
   <String Id="IDS_PROGMSG_XML_UPDATE_FILE">正在更新 XML 文件 %s...</String>


   <!-- LaunchCondition Error Messages -->
   <String Id="MINIMUM_REQUIRED_OS">此产品只能安装在 Windows 10、Windows Server 2016 或更高版本的操作系统上。</String>
   <String Id="DENY_INSTALL_DOMAIN_CONTROLLER">无法在域控制器上安装此产品。</String>
   <String Id="NEED_ADMIN">您需要有管理员特权，才能安装/卸载此软件。</String>


   <!-- Feature Table -->
   <String Id="FEATURE_NAME_CORE">核心</String>
   <String Id="FEATURE_DESC_CORE">[ProductName] 核心功能</String>
   <String Id="FEATURE_NAME_CORRETTO">Corretto</String>
   <String Id="FEATURE_DESC_CORRETTO">包含 Corretto JDK 发行版的 [ProductName] 核心功能</String>
   <String Id="FEATURE_NAME_BELLSOFT">Bellsoft</String>
   <String Id="FEATURE_DESC_BELLSOFT">包含 Bellsoft JDK 发行版的 [ProductName] 核心功能</String>
   <String Id="FEATURE_NAME_RDSH3D">3D RDSH</String>
   <String Id="FEATURE_DESC_RDSH3D">此功能在 RDSH 会话和物理 PC 会话中启用硬件 3D 加速。</String>
   <String Id="FEATURE_NAME_CLIENTDRIVEREDIRECTION">客户端驱动器重定向</String>
   <String Id="FEATURE_DESC_CLIENTDRIVEREDIRECTION">允许 Horizon Clients 与远程桌面和应用程序共享本地驱动器。如果未安装，将禁用复制/粘贴和拖放文件和文件夹功能。</String>
   <String Id="FEATURE_NAME_NGVC">Instant Clone Agent</String>
   <String Id="FEATURE_DESC_NGVC">Instant Clone Agent 仅应安装在运行 VMware vSphere 7.0 或更高版本的虚拟机上。</String>
   <String Id="FEATURE_DESC_PCOIP_PHYSICAL">此功能会在您的桌面上安装 PCoIP 服务器组件。</String>
   <String Id="FEATURE_NAME_RTAV">实时音频-视频</String>
   <String Id="FEATURE_DESC_RTAV">通过实时音频-视频功能，用户可将本地连接的音频和视频外围设备重定向到远程桌面，以供使用。</String>
   <String Id="FEATURE_NAME_VMWPRINT">Horizon Integrated Printing</String>
   <String Id="FEATURE_DESC_VMWPRINT">Horizon Integrated Printing 重定向。</String>

   <String Id="FEATURE_NAME_SCANNERREDIRECTION">扫描仪重定向</String>
   <String Id="FEATURE_DESC_SCANNERREDIRECTION">启用扫描仪重定向功能。</String>
   <String Id="FEATURE_NAME_SERIALPORTREDIRECTION">串行端口重定向</String>
   <String Id="FEATURE_DESC_SERIALPORTREDIRECTION">启用串行端口重定向功能。</String>
   <String Id="FEATURE_NAME_SMARTCARD">智能卡重定向</String>
   <String Id="FEATURE_DESC_SMARTCARD">启用智能卡重定向功能。</String>
   <String Id="FEATURE_NAME_TSMMR">TSMMR</String>
   <String Id="FEATURE_DESC_TSMMR">终端服务多媒体重定向。</String>
   <String Id="FEATURE_NAME_URLREDIRECTION">URL 内容重定向</String>
   <String Id="FEATURE_DESC_URLREDIRECTION">将 URL 内容从服务器会话重定向到客户端设备，或者从客户端设备重定向到服务器会话。</String>
   <String Id="FEATURE_NAME_UNCREDIRECTION">UNC 路径重定向</String>
   <String Id="FEATURE_DESC_UNCREDIRECTION">将 UNC 路径从服务器会话重定向到客户端设备，或者从客户端设备重定向到服务器会话。</String>
   <String Id="FEATURE_NAME_USB">USB 重定向</String>
   <String Id="FEATURE_DESC_USB">USB 重定向。有关安全使用 USB 重定向的指导，请参阅《Horizon 安全指南》文档。</String>
   <String Id="FEATURE_NAME_HZNVAUDIO">Horizon 音频</String>
   <String Id="FEATURE_DESC_HZNVAUDIO">Horizon 虚拟音频驱动程序</String>
   <String Id="FEATURE_NAME_HTML5MMR">HTML5 多媒体重定向</String>
   <String Id="FEATURE_DESC_HTML5MMR">启用 HTML5 多媒体重定向</String>
   <String Id="FEATURE_NAME_GEOREDIR">地理位置重定向</String>
   <String Id="FEATURE_DESC_GEOREDIR">启用客户端地理位置到远程桌面的重定向</String>
   <String Id="FEATURE_NAME_SDOSENSOR">SDO 传感器重定向</String>
   <String Id="FEATURE_DESC_SDOSENSOR">启用简单设备方向 (SDO) 传感器重定向功能，向远程桌面报告设备方向更改。</String>
   <String Id="FEATURE_NAME_STORAGEDRIVE">存储驱动器重定向</String>
   <String Id="FEATURE_DESC_STORAGEDRIVE">启用客户端存储驱动器到远程桌面的重定向。</String>
   <String Id="FEATURE_NAME_PERFTRACKER">Horizon Performance Tracker</String>
   <String Id="FEATURE_DESC_PERFTRACKER">启用 Horizon Performance Tracker</String>
   <String Id="FEATURE_NAME_HYBRIDLOGON">混合登录</String>
   <String Id="FEATURE_DESC_HYBRIDLOGON">启用混合登录后，将允许未验证用户直接访问网络资源，而无需输入凭据。</String>
   <String Id="FEATURE_NAME_HELPDESK">适用于 Horizon Agent 的技术支持插件</String>
   <String Id="FEATURE_DESC_HELPDESK">适用于 Horizon Agent 的技术支持插件。</String>

   <!-- Control Panel Strings -->
   <String Id="Url">https://www.omnissa.com/</String>

   <!-- Firewall Strings -->
   <String Id="BlastUDPFirewallExceptionName">Omnissa Horizon Blast UDP 流量异常</String>

   <!-- UI Dialog Strings -->
   <String Id="IDS__DisplayName_Custom">自定义安装</String>
   <String Id="IDS__DisplayName_Minimal">最小安装</String>
   <String Id="IDS__DisplayName_Typical">典型安装</String>
   <String Id="INTEL_UNS_DESC">提供 Intel User Notification 服务。</String>
   <String Id="IDS_LicenseAcceptance">安装即表示您同意 </String>
   <String Id="IDS_GeneralTerms">通用条款</String>
   <String Id="IDS_CANCEL">取消</String>
   <String Id="IDS_CANCEL2">取消(&amp;C)</String>
   <String Id="IDS_OK">确定</String>
   <String Id="IDS_BACK">&lt; 上一步(&amp;B)</String>
   <String Id="IDS_NEXT">下一步(&amp;N) &gt;</String>
   <String Id="IDS_FINISH">结束(&amp;F)</String>
   <String Id="IDS__IsCancelDlg_No">否(&amp;N)</String>
   <String Id="IDS__IsCancelDlg_Yes">是(&amp;Y)</String>
   <String Id="IDS__IsAdminInstallBrowse_LookIn">查找范围(&amp;L):</String>
   <String Id="IDS__IsAdminInstallBrowse_UpOneLevel">向上一级</String>
   <String Id="IDS__IsAdminInstallBrowse_BrowseDestination">浏览至目标文件夹。</String>
   <String Id="IDS__IsAdminInstallBrowse_ChangeDestination">{&amp;MSSansBold8}更改当前的目标文件夹</String>
   <String Id="IDS__IsAdminInstallBrowse_CreateFolder">新建文件夹</String>
   <String Id="IDS__IsAdminInstallBrowse_FolderName">文件夹名称(&amp;F):</String>
   <String Id="IDS__IsAdminInstallPoint_Install">安装(&amp;I)</String>
   <String Id="IDS__IsAdminInstallPoint_SpecifyNetworkLocation">指定产品的服务器映像的网络位置。</String>
   <String Id="IDS__IsAdminInstallPoint_EnterNetworkLocation">输入网络位置或单击“更改”以浏览到某个位置。单击“安装”可以在指定的网络位置创建 [ProductName] 的服务器映像，单击“取消”可以退出该向导。</String>
   <String Id="IDS__IsAdminInstallPoint_NetworkLocationFormatted">{&amp;MSSansBold8}网络位置</String>
   <String Id="IDS__IsAdminInstallPoint_NetworkLocation">网络位置(&amp;N):</String>
   <String Id="IDS__IsAdminInstallPoint_Change">更改(&amp;C)...</String>
   <String Id="IDS__IsAdminInstallPointWelcome_Wizard">{&amp;TahomaBold10}欢迎使用 [ProductName] 的安装程序</String>
   <String Id="IDS__IsAdminInstallPointWelcome_ServerImage">安装程序将在指定的网络位置创建 [ProductName] 的服务器映像。要继续，请单击“下一步”。</String>
   <String Id="ProductVersion">{&amp;Arial9}产品版本: [ProductVersionString]</String>
   <String Id="IDS__IsCancelDlg_ConfirmCancel">是否确定要取消 [ProductName] 的安装?</String>
   <String Id="IDS__IsInstallRolesConfirmDlg_Message">安装程序将向操作系统安装所需的角色。单击“确定”以继续。</String>
   <String Id="ConnectionServer_TitleDesc">输入此计算机将连接到的 Horizon Connection Server。</String>
   <String Id="ConnectionServer_Title">{&amp;MSSansBold8}向 Horizon Connection Server 注册</String>
   <String Id="ConnectionServer_Text">输入 Horizon Connection Server (标准或副本实例) 的服务器名称和管理员登录凭据，以便向 Horizon Connection Server 注册这台计算机。</String>
   <String Id="ConnectionServer_ServerNote">(主机名或 IP 地址)</String>
   <String Id="ConnectionServerLogin_Text1">验证为当前登录用户(&amp;A)</String>
   <String Id="ConnectionServerLogin_Text2">指定管理员凭据(&amp;C)</String>
   <String Id="ConnectionServerLogin_Title">身份验证:</String>
   <String Id="ConnectionServer_Username">用户名(&amp;U):</String>
   <String Id="ConnectionServer_UsernameNote">(域\用户)</String>
   <String Id="ConnectionServer_Password">密码(&amp;P):</String>
   <String Id="IDS__IsCustomSelectionDlg_SelectFeatures">选择要安装的程序功能。</String>
   <String Id="IDS__IsCustomSelectionDlg_ClickFeatureIcon">单击以下列表中的图标，更改功能的安装方式。</String>
   <String Id="IDS__IsCustomSelectionDlg_CustomSetup">{&amp;MSSansBold8}自定义安装</String>
   <String Id="IDS__IsCustomSelectionDlg_Change">更改(&amp;C)...</String>
   <String Id="IDS__IsCustomSelectionDlg_FeatureDescription">功能描述</String>
   <String Id="IDS__IsCustomSelectionDlg_InstallTo">安装到:</String>
   <String Id="IDS__IsCustomSelectionDlg_MultilineDescription">当前所选项的多行说明</String>
   <String Id="IDS__IsCustomSelectionDlg_FeaturePath">&lt;选定的功能路径&gt;</String>
   <String Id="IDS__IsCustomSelectionDlg_FeatureSize">功能大小</String>
   <String Id="IDS__IsCustomSelectionDlg_Help">帮助(&amp;H)</String>
   <String Id="IDS__IsCustomSelectionDlg_Space">空间(&amp;S)</String>
   <String Id="IDS_SetupTips_CustomSetupDescription">“自定义安装”允许您有选择地安装程序功能。</String>
   <String Id="IDS_SetupTips_CustomSetup">{&amp;MSSansBold8}自定义安装提示</String>
   <String Id="IDS_SetupTips_WillNotBeInstalled">将不会被安装。</String>
   <String Id="IDS_SetupTips_Advertise">将在第一次使用时安装。(只在该功能支持此选项时有效。)</String>
   <String Id="IDS_SetupTips_InstallState">此安装状态说明该功能...</String>
   <String Id="IDS_SetupTips_AllInstalledLocal">将完全安装到本地硬盘上。</String>
   <String Id="IDS_SetupTips_IconInstallState">功能名称旁边的图标表示该功能的安装状态。单击图标可下拉每个功能的安装状态菜单。</String>
   <String Id="IDS_SetupTips_Network">将被安装为从网络运行。(只在该功能支持此选项时有效。)</String>
   <String Id="IDS_SetupTips_SubFeaturesInstalledLocal">将一些子功能安装到本地硬盘。(只在该功能有子功能时有效。)</String>
   <String Id="DesktopConfig_Subtitle">以下信息用于配置 Horizon 桌面功能</String>
   <String Id="DesktopConfig_Title">{&amp;MSSansBold8}桌面操作系统配置</String>
   <String Id="DesktopConfig_Text">选择 [ProductName] 在此操作系统中的模式:</String>
   <String Id="DesktopConfig_RDSHMode">此操作系统中未安装所需的远程桌面会话主机 (RDSH) 角色。

单击“下一步”以安装所需的角色/功能。完成后，必须重新启动操作系统。

重新启动后，还必须重新启动 [ProductName] 安装程序，才能继续在 RDS 模式下对其进行安装。</String>
   <String Id="DesktopConfig_DesktopMode">将在桌面 VDI 模式下配置此代理。</String>
   <String Id="DesktopConfig_InstallingRolesSuccess">已成功安装所需的操作系统角色/功能。
请重新启动操作系统，然后再重新启动 [ProductName] 安装程序。</String>
   <String Id="DesktopConfig_InstallingRolesFail">错误: 安装程序无法安装所需的操作系统角色/功能!</String>
   <String Id="IDS__IsDesktopConfigDlg_RDSMode">RDS 模式</String>
   <String Id="IDS__IsDesktopConfigDlg_DesktopMode">桌面模式</String>
   <String Id="InstallRolesConfirm_InstallingRoles">正在此系统中配置所需的角色/功能，请稍候...</String>
   <String Id="IDS__IsFeatureDetailsDlg_DiskSpaceRequirements">{&amp;MSSansBold8}磁盘空间要求</String>
   <String Id="IDS__IsFeatureDetailsDlg_SpaceRequired">安装选定功能所需的磁盘空间。</String>
   <String Id="IDS__IsFeatureDetailsDlg_VolumesTooSmall">突出显示的卷没有足够的可用磁盘空间可用于当前所选功能。您可以从突出显示的卷中删除文件，选择在本地驱动器上安装少一些的功能，或者选择不同的目标驱动器。</String>
   <String Id="IDS__IsFilesInUse_Retry">重试(&amp;R)</String>
   <String Id="IDS__IsFilesInUse_Ignore">忽略(&amp;I)</String>
   <String Id="IDS__IsFilesInUse_Exit">退出(&amp;E)</String>
   <String Id="IDS__IsFilesInUse_FilesInUse">{&amp;MSSansBold8}文件正在使用</String>
   <String Id="IDS__IsFilesInUse_FilesInUseMessage">一些需要更新的文件正在使用中。</String>
   <String Id="IDS__IsFilesInUse_ApplicationsUsingFiles">以下应用程序正在使用此安装程序需要更新的文件。请关闭这些应用程序，然后单击“重试”以继续。

注意: 如果您在以下列表中看到 [ProductName]，请单击“忽略”以继续。</String>
   <String Id="IDS__IsBrowseFolderDlg_LookIn">查找范围(&amp;L):</String>
   <String Id="IDS__IsBrowseFolderDlg_UpOneLevel">向上一级</String>
   <String Id="IDS__IsBrowseFolderDlg_BrowseDestFolder">浏览至目标文件夹。</String>
   <String Id="IDS__IsBrowseFolderDlg_ChangeCurrentFolder">{&amp;MSSansBold8}更改当前的目标文件夹</String>
   <String Id="IDS__IsBrowseFolderDlg_CreateFolder">新建文件夹</String>
   <String Id="IDS__IsBrowseFolderDlg_FolderName">文件夹名称(&amp;F):</String>
   <String Id="IDS__IsWelcomeDlg_WelcomeProductName">{&amp;TahomaBold10}欢迎使用 [ProductName] 的安装向导</String>
   <String Id="IDS__IsWelcomeDlg_InstallProductName">安装向导将在计算机上安装 [ProductName]。要继续，请单击“下一步”。</String>
   <String Id="InstallWelcome_UpgradeLine1">安装向导将升级计算机上的 [ProductName]。要继续，请单击“下一步”。</String>
   <String Id="IDS__IsWelcomeDlg_WarningCopyright">版权所有 (C) [CopyrightYears] Omnissa。保留所有权利。此产品受美国及其他国家或地区的版权法和知识产权法及国际条约的保护。“Omnissa”一词是指 Omnissa, LLC、Omnissa International Unlimited Company 和/或其子公司。</String>
   <String Id="IpProtocolConfig_DlgDesc">选择通信协议</String>
   <String Id="IpProtocolConfig_DlgTitle">{&amp;MSSansBold8}网络协议配置</String>
   <String Id="GoldenImage_DlgDesc">Select whether this machine will be used as a Golden Image</String>
   <String Id="GoldenImage_DlgTitle">{&amp;MSSansBold8}Golden Image Selection</String>
   <String Id="GoldenImage_CheckBoxText">This machine will be used as a Golden Image</String>
   <String Id="ConnectionServer_IpText">指定要用于配置此 Horizon Agent 实例的协议:</String>
   <String Id="ConnectionServer_IPv4Desc">此代理将配置为选择 IPv4 协议来建立所有连接。</String>
   <String Id="ConnectionServer_IPv6Desc">此代理将配置为选择 IPv6 协议来建立所有连接。</String>
   <String Id="ConnectionServer_Dual4Desc">此代理将配置为支持混合 IP 模式，在建立所有连接时将首选 IPv4 协议。</String>
   <String Id="ConnectionServer_Dual6Desc">此代理将配置为支持混合 IP 模式，在建立所有连接时将首选 IPv6 协议。</String>
   <String Id="IpProtocolConfig_FipsText">指定是否在安装此产品时使用符合 FIPS 的加密。</String>
   <String Id="IpProtocolConfig_FipsDisabledDesc">此代理实例将在不符合 FIPS 标准的情况下运行。</String>
   <String Id="IpProtocolConfig_FipsEnabledDesc">此代理实例将针对符合 FIPS 标准的加密进行配置。</String>
   <String Id="FipsConfig_Disabled">已禁用</String>
   <String Id="FipsConfig_Enabled">已启用</String>
   <String Id="IDS__AgreeToLicense_0">我不接受通用条款(&amp;D)</String>
   <String Id="IDS__AgreeToLicense_1">我接受通用条款(&amp;A)</String>
   <String Id="IDS__IsLicenseDlg_ReadLicenseAgreement">请仔细阅读以下通用条款。</String>
   <String Id="IDS__IsLicenseDlg_LicenseAgreement">{&amp;MSSansBold8}通用条款</String>
   <String Id="IDS__IsMaintenanceDlg_Modify">{&amp;MSSansBold8}修改(&amp;M)</String>
   <String Id="IDS__IsMaintenanceDlg_Repair">{&amp;MSSansBold8}修复(&amp;P)</String>
   <String Id="IDS__IsMaintenanceDlg_Remove">{&amp;MSSansBold8}删除(&amp;R)</String>
   <String Id="IDS__IsMaintenanceDlg_MaitenanceOptions">修改、修复或移除程序。</String>
   <String Id="IDS__IsMaintenanceDlg_ProgramMaintenance">{&amp;MSSansBold8}程序维护</String>
   <String Id="IDS__IsMaintenanceDlg_ModifyMessage">允许用户更改安装的功能。</String>
   <String Id="IDS__IsMaintenanceDlg_RepairMessage">修复程序中的安装错误。该选项可修复丢失或损坏的文件、快捷方式和注册表项。</String>
   <String Id="IDS__IsMaintenanceDlg_RemoveProductName">从您的计算机中移除 [ProductName]。</String>
   <String Id="IDS__IsMaintenanceWelcome_WizardWelcome">{&amp;TahomaBold10}欢迎使用 [ProductName] 的安装程序</String>
   <String Id="IDS__IsMaintenanceWelcome_MaintenanceOptionsDescription">安装程序将允许您修改、修复或移除 [ProductName]。要继续，请单击“下一步”。</String>
   <String Id="IDS_PRODUCTNAME_INSTALLSHIELD">[ProductName] - 安装向导</String>
   <String Id="IDS__IsMsiRMFilesInUse_CloseRestart">自动关闭并尝试重新启动应用程序。</String>
   <String Id="IDS__IsMsiRMFilesInUse_RebootAfter">不关闭应用程序。(需要重新启动。)</String>
   <String Id="IDS__IsMsiRMFilesInUse_ApplicationsUsingFiles">以下应用程序正在使用此安装程序需要更新的文件。</String>
   <String Id="IDS__IsDiskSpaceDlg_OutOfDiskSpace">{&amp;MSSansBold8}磁盘空间不足</String>
   <String Id="IDS__IsDiskSpaceDlg_DiskSpace">安装所需的磁盘空间超过了可用磁盘空间。</String>
   <String Id="IDS__IsDiskSpaceDlg_HighlightedVolumes">突出显示的卷没有足够的可用磁盘空间可用于当前所选功能。您可以从突出显示的卷中删除文件，或者取消安装。</String>
   <String Id="RdpChoice_EnableRdp">启用该计算机的远程桌面功能(&amp;E)</String>
   <String Id="RdpChoice_NoRdp">不启用该计算机的远程桌面功能(&amp;D)</String>
   <String Id="RdpConfig_Subtitle">以下信息用于配置远程桌面功能</String>
   <String Id="RdpConfig_Title">{&amp;MSSansBold8}远程桌面协议配置</String>
   <String Id="RdpConfig_Text">[ProductName] 需要开启远程桌面支持。将为 RDP 端口 [RDP_PORT_NUMBER] 和 View Framework 通道 [FRAMEWORK_CHANNEL_PORT] 添加防火墙例外。您希望怎样做?</String>
   <String Id="IDS__IsVerifyReadyDlg_Install">安装(&amp;I)</String>
   <String Id="IDS__IsVerifyReadyDlg_WizardReady">向导已就绪，可以开始安装。</String>
   <String Id="ReadyToInstall_RdshNote">注意: 此操作系统中未启用 RDS 角色。[ProductName] 仅支持单个桌面连接。</String>
   <String Id="IDS__IsVerifyReadyDlg_ClickInstall">单击“安装”开始安装，或单击“取消”退出该向导。</String>
   <String Id="IDS__IsVerifyReadyDlg_ReadyRepair">{&amp;MSSansBold8}准备修复程序</String>
   <String Id="IDS__IsVerifyReadyDlg_ReadyInstall">{&amp;MSSansBold8}准备安装程序</String>
   <String Id="ReadyToInstall_InstallDir">[ProductName] 将安装到: 

[INSTALLDIR]</String>
   <String Id="ReadyToInstall_MsgSanPolicy_NGVC">注意: 按照 Instant Clone Agent (NGVC) 功能的要求，VDS SAN 策略将被设置为“全部联机”。</String>
   <String Id="IDS__IsVerifyRemoveAllDlg_Remove">移除(&amp;R)</String>
   <String Id="IDS__IsVerifyRemoveAllDlg_ChoseRemoveProgram">您已选择从系统中删除程序。</String>
   <String Id="IDS__IsVerifyRemoveAllDlg_ClickRemove">单击“移除”从您的计算机中移除 [ProductName]。移除后，将无法再使用此程序。</String>
   <String Id="IDS__IsVerifyRemoveAllDlg_ClickBack">如果要查看或更改任何设置，请单击“上一步”。</String>
   <String Id="IDS__IsVerifyRemoveAllDlg_RemoveProgram">{&amp;MSSansBold8}移除程序</String>
   <String Id="IDS__IsFatalError_NotModified">尚未修改您的系统。若要在其他时间完成安装，请再次运行安装程序。</String>
   <String Id="IDS__IsFatalError_ClickFinish">单击“结束”退出向导。</String>
   <String Id="IDS__IsFatalError_KeepOrRestore">您可以保留系统中当前已安装的所有内容，以后再继续此安装过程，也可以将系统还原到安装前的原始状态。</String>
   <String Id="IDS__IsFatalError_RestoreOrContinueLater">单击“还原”或“以后再继续”退出向导。</String>
   <String Id="IDS__IsFatalError_WizardCompleted">{&amp;TahomaBold10}安装已完成</String>
   <String Id="IDS__IsFatalError_WizardInterrupted">向导在 [ProductName] 完全安装前已被中断。</String>
   <String Id="IDS__IsFatalError_UninstallWizardInterrupted">向导在 [ProductName] 完全卸载前被中断。</String>
   <String Id="IDS__IsExitDialog_WizardCompleted">{&amp;TahomaBold10}安装已完成</String>
   <String Id="IDS__IsExitDialog_InstallSuccess">安装程序已成功安装 [ProductName]。单击“结束”退出向导。</String>
   <String Id="IDS__IsExitDialog_UninstallSuccess">安装程序已成功卸载 [ProductName]。单击“结束”退出向导。</String>
   <String Id="IDS__IsExitDialog_InstallingRolesSuccess">安装程序已成功为操作系统配置在 RDS 模式下安装 [ProductName] 所需的角色/功能。

单击“完成”以退出向导。</String>
   <String Id="IDS__IsErrorDlg_InstallerInfo">[ProductName] 安装程序信息</String>
   <String Id="IDS__IsErrorDlg_Abort">中止(&amp;A)</String>
   <String Id="IDS__IsErrorDlg_Yes">是(&amp;Y)</String>
   <String Id="IDS__IsErrorDlg_No">否(&amp;N)</String>
   <String Id="IDS__IsErrorDlg_Ignore">忽略(&amp;I)</String>
   <String Id="IDS__IsErrorDlg_OK">确定(&amp;O)</String>
   <String Id="IDS__IsErrorDlg_Retry">重试(&amp;R)</String>
   <String Id="IDS__IsInitDlg_WelcomeWizard">{&amp;TahomaBold10}欢迎使用 [ProductName] 的安装程序</String>
   <String Id="IDS__IsInitDlg_PreparingWizard">[ProductName] 安装正在准备安装程序，它将指导您完成此程序的安装过程。请稍候。</String>
   <String Id="IDS__IsUserExit_NotModified">系统尚未修改。要稍后安装此程序，请再次运行安装程序。</String>
   <String Id="IDS__IsUserExit_ClickFinish">单击“结束”退出向导。</String>
   <String Id="IDS__IsUserExit_KeepOrRestore">您可以保留系统中当前已安装的所有内容，以后再继续此安装过程，也可以将系统还原到安装前的原始状态。</String>
   <String Id="IDS__IsUserExit_RestoreOrContinue">单击“还原”或“以后再继续”退出向导。</String>
   <String Id="IDS__IsUserExit_WizardCompleted">{&amp;TahomaBold10}安装已完成</String>
   <String Id="IDS__IsUserExit_WizardInterrupted">向导在 [ProductName] 完全安装前已被中断。</String>
   <String Id="IDS__IsUserExit_UninstallWizardInterrupted">向导在 [ProductName] 完全卸载前被中断。</String>
   <String Id="IDS__IsProgressDlg_UninstallingFeatures2">正在安装所选程序功能。</String>
   <String Id="IDS__IsProgressDlg_UninstallingFeatures">正在卸载所选程序功能。</String>
   <String Id="IDS__IsProgressDlg_WaitUninstall2">安装程序正在安装 [ProductName]，请稍候。这可能需要几分钟。</String>
   <String Id="IDS__IsProgressDlg_WaitUninstall">安装程序正在卸载 [ProductName]，请稍候。这可能需要几分钟。</String>
   <String Id="IDS__IsProgressDlg_InstallingProductName">{&amp;MSSansBold8}正在安装 [ProductName]</String>
   <String Id="IDS__IsProgressDlg_Uninstalling">{&amp;MSSansBold8}正在卸载 [ProductName]</String>
   <String Id="IDS__IsProgressDlg_SecHidden">(现在隐藏) 秒</String>
   <String Id="IDS__IsProgressDlg_Status">状态:</String>
   <String Id="IDS__IsProgressDlg_Hidden">(现在隐藏)</String>
   <String Id="IDS__IsProgressDlg_HiddenTimeRemaining">(现在隐藏) 估计的剩余时间:</String>
   <String Id="IDS__IsProgressDlg_ProgressDone">已完成</String>
   <String Id="IDS__IsResumeDlg_Resuming">{&amp;TahomaBold10}正在继续运行 [ProductName] 的安装程序</String>
   <String Id="IDS__IsResumeDlg_ResumeSuspended">安装程序将在计算机上完成已挂起的 [ProductName] 安装。要继续，请单击“下一步”。</String>
   <String Id="IDS__IsResumeDlg_WizardResume">安装程序将在计算机上完成 [ProductName] 的安装。要继续，请单击“下一步”。</String>


   <!-- Error Strings -->
   <String Id="MsgWSWCInstalled">无法继续安装，因为此计算机上已安装了不兼容版本的 Horizon Client。

要继续安装 [ProductName]，请卸载该 Horizon Client，然后重新运行此安装程序。</String>
   <String Id="MsgClientRunning">无法继续安装代理。检测到一个活动的 Horizon Client 会话。</String>
   <String Id="MsgDowngradeDetected">安装程序检测到已经安装 [ProductName] 的更高版本。</String>
   <String Id="MsgManualUninstallRequired">此安装程序无法在现有产品安装基础上执行升级。请卸载现有产品，然后再继续本次安装。</String>
   <String Id="MsgMustReboot">在继续安装前，必须重新启动操作系统。</String>
   <String Id="MsgServerInstalled">无法继续安装，因为此计算机上已安装了 Horizon Connection Server。

要继续安装 [ProductName]，请卸载该连接服务器，然后重新运行此安装程序。</String>
   <String Id="MsgUnsupportedOldVersion">无法进行安装，此计算机上已经安装了该产品不受支持的旧版本。请先卸载旧版产品，重新启动系统，然后再安装该产品。</String>
   <String Id="MsgUrlRedirectionInstalled">您正在尝试安装启用了 URL 重定向的 [ProductName]，但 URL 重定向已由 Horizon Client 启用。因此不支持该操作。如果您继续，则安装的 Agent 将没有 URL 重定向功能。您必须先卸载 Client 再安装 Agent，这样才能在 Agent 模式下实现 URL 重定向。</String>
   <String Id="MsgUNCRedirectionInstalled">您正在尝试安装启用了 UNC 重定向的 [ProductName]，但 UNC 重定向已由 Horizon Client 启用。因此不支持该操作。如果您继续，则安装的 Agent 将没有 UNC 重定向功能。您必须先卸载 Client 再安装 Agent，这样才能在 Agent 模式下实现 UNC 重定向。</String>
   <String Id="MsgVdmLoopbackIp">尝试建立“localhost”IP 地址时发生错误。请确保已选择 IP 协议，且该协议已安装在本计算机上。</String>
   <String Id="MsgWindowsUpdateInProgress">Windows Update 当前正在运行。请等待 Windows Update 完成并重新引导系统，然后再安装 Horizon Agent。</String>
   <String Id="MsgWindowsUpdateAndRestartPending">在继续安装前，必须更新并重新引导系统。</String>
   <String Id="NoRepairAllowed">活动的 Horizon 会话正在进行中。[ProductName] 修复无法继续。</String>
   <String Id="MsgInstallationAbortifSVIInstalled">此安装程序无法对现有产品安装执行升级。从版本 8.1 开始，Horizon View Composer 功能将不再受支持。要安装此内部版本，请先卸载以前的内部版本。</String>
   <String Id="SettingsFileInvalid">无法解析安装程序设置文件:“[SETTINGS_FILE]”

在第 [SettingsFileErrorLine] 行处出错。</String>


   <!-- Action Text Strings -->
   <String Id="ActionText_RdpConfig">正在执行 RDP 配置</String>
   <String Id="ConfigUserInit">正在注册 UserInit 进程: wssm.exe</String>
   <String Id="IDS_ACTIONTEXT_1">[1]</String>
   <String Id="IDS_ACTIONTEXT_1b">[1]</String>
   <String Id="IDS_ACTIONTEXT_1c">[1]</String>
   <String Id="IDS_ACTIONTEXT_1d">[1]</String>
   <String Id="IDS_ACTIONTEXT_Advertising">正在通知应用程序</String>
   <String Id="IDS_ACTIONTEXT_AllocatingRegistry">正在分配注册表空间</String>
   <String Id="IDS_ACTIONTEXT_AppCommandLine">应用程序: [1]，命令行: [2]</String>
   <String Id="IDS_ACTIONTEXT_AppId">AppId: [1]{{，AppType: [2]}}</String>
   <String Id="IDS_ACTIONTEXT_AppIdAppTypeRSN">AppId: [1]{{，AppType: [2]，用户: [3]，RSN: [4]}}</String>
   <String Id="IDS_ACTIONTEXT_Application">应用程序: [1]</String>
   <String Id="IDS_ACTIONTEXT_BindingExes">正在绑定可执行文件</String>
   <String Id="IDS_ACTIONTEXT_ClassId">类 ID: [1]</String>
   <String Id="IDS_ACTIONTEXT_ClsID">类 ID: [1]</String>
   <String Id="IDS_ACTIONTEXT_ComponentIDQualifier">组件 ID: [1]，限定符: [2]</String>
   <String Id="IDS_ACTIONTEXT_ComponentIdQualifier2">组件 ID: [1]，限定符: [2]</String>
   <String Id="IDS_ACTIONTEXT_ComputingSpace">正在计算所需空间</String>
   <String Id="IDS_ACTIONTEXT_ComputingSpace2">正在计算所需空间</String>
   <String Id="IDS_ACTIONTEXT_ComputingSpace3">正在计算所需空间</String>
   <String Id="IDS_ACTIONTEXT_ContentTypeExtension">MIME 内容类型: [1]，扩展名: [2]</String>
   <String Id="IDS_ACTIONTEXT_ContentTypeExtension2">MIME 内容类型: [1]，扩展名: [2]</String>
   <String Id="IDS_ACTIONTEXT_CopyingNetworkFiles">正在向网络复制文件</String>
   <String Id="IDS_ACTIONTEXT_CopyingNewFiles">正在复制新文件</String>
   <String Id="IDS_ACTIONTEXT_CreatingDuplicate">正在创建副本文件</String>
   <String Id="IDS_ACTIONTEXT_CreatingFolders">正在创建文件夹</String>
   <String Id="IDS_ACTIONTEXT_CreatingShortcuts">正在创建快捷方式</String>
   <String Id="IDS_ACTIONTEXT_DeletingServices">正在删除服务</String>
   <String Id="IDS_ACTIONTEXT_EnvironmentStrings">正在更新环境字符串</String>
   <String Id="IDS_ACTIONTEXT_EvaluateLaunchConditions">正在评估启动条件</String>
   <String Id="IDS_ACTIONTEXT_Extension">扩展名: [1]</String>
   <String Id="IDS_ACTIONTEXT_Extension2">扩展名: [1]</String>
   <String Id="IDS_ACTIONTEXT_Feature">功能: [1]</String>
   <String Id="IDS_ACTIONTEXT_FeatureColon">功能: [1]</String>
   <String Id="IDS_ACTIONTEXT_File">文件: [1]</String>
   <String Id="IDS_ACTIONTEXT_File2">文件: [1]</String>
   <String Id="IDS_ACTIONTEXT_FileDependencies">文件: [1]，依赖关系: [2]</String>
   <String Id="IDS_ACTIONTEXT_FileDir">文件: [1]，目录: [9]</String>
   <String Id="IDS_ACTIONTEXT_FileDir2">文件: [1]，目录: [9]</String>
   <String Id="IDS_ACTIONTEXT_FileDir3">文件: [1]，目录: [9]</String>
   <String Id="IDS_ACTIONTEXT_FileDirSize">文件: [1]，目录: [9]，大小: [6]</String>
   <String Id="IDS_ACTIONTEXT_FileDirSize2">文件: [1]，目录: [9]，大小: [6]</String>
   <String Id="IDS_ACTIONTEXT_FileDirSize3">文件: [1]，目录: [9]，大小: [6]</String>
   <String Id="IDS_ACTIONTEXT_FileDirSize4">文件: [1]，目录: [2]，大小: [3]</String>
   <String Id="IDS_ACTIONTEXT_FileDirectorySize">文件: [1]，目录: [9]，大小: [6]</String>
   <String Id="IDS_ACTIONTEXT_FileFolder">文件: [1]，文件夹: [2]</String>
   <String Id="IDS_ACTIONTEXT_FileFolder2">文件: [1]，文件夹: [2]</String>
   <String Id="IDS_ACTIONTEXT_FileSectionKeyValue">文件: [1]，节: [2]，键: [3]，值: [4]</String>
   <String Id="IDS_ACTIONTEXT_FileSectionKeyValue2">文件: [1]，节: [2]，键: [3]，值: [4]</String>
   <String Id="IDS_ACTIONTEXT_Folder">文件夹: [1]</String>
   <String Id="IDS_ACTIONTEXT_Folder1">文件夹: [1]</String>
   <String Id="IDS_ACTIONTEXT_Font">字体: [1]</String>
   <String Id="IDS_ACTIONTEXT_Font2">字体: [1]</String>
   <String Id="IDS_ACTIONTEXT_FoundApp">找到的应用程序: [1]</String>
   <String Id="IDS_ACTIONTEXT_FreeSpace">可用空间: [1]</String>
   <String Id="IDS_ACTIONTEXT_GeneratingScript">正在生成脚本操作，用于:</String>
   <String Id="IDS_ACTIONTEXT_InitializeODBCDirs">正在初始化 ODBC 目录</String>
   <String Id="IDS_ACTIONTEXT_InstallODBC">正在安装 ODBC 组件</String>
   <String Id="IDS_ACTIONTEXT_InstallServices">正在安装新服务</String>
   <String Id="IDS_ACTIONTEXT_InstallingSystemCatalog">正在安装系统目录</String>
   <String Id="IDS_ACTIONTEXT_KeyName">键: [1]，名称: [2]</String>
   <String Id="IDS_ACTIONTEXT_KeyNameValue">键: [1]，名称: [2]，值: [3]</String>
   <String Id="IDS_ACTIONTEXT_LibId">LibID: [1]</String>
   <String Id="IDS_ACTIONTEXT_Libid2">LibID: [1]</String>
   <String Id="IDS_ACTIONTEXT_MigratingFeatureStates">正在从相关应用程序迁移功能状态</String>
   <String Id="IDS_ACTIONTEXT_MovingFiles">正在移动文件</String>
   <String Id="IDS_ACTIONTEXT_NameValueAction">名称: [1]，值: [2]，操作 [3]</String>
   <String Id="IDS_ACTIONTEXT_NameValueAction2">名称: [1]，值: [2]，操作 [3]</String>
   <String Id="IDS_ACTIONTEXT_PatchingFiles">正在修补文件</String>
   <String Id="IDS_ACTIONTEXT_ProgID">ProgID: [1]</String>
   <String Id="IDS_ACTIONTEXT_ProgID2">ProgID: [1]</String>
   <String Id="IDS_ACTIONTEXT_PropertySignature">属性: [1]，签名: [2]</String>
   <String Id="IDS_ACTIONTEXT_PublishProductFeatures">正在发布产品功能</String>
   <String Id="IDS_ACTIONTEXT_PublishProductInfo">正在发布产品信息</String>
   <String Id="IDS_ACTIONTEXT_PublishingQualifiedComponents">正在发布合格的组件</String>
   <String Id="IDS_ACTIONTEXT_RegUser">正在注册用户</String>
   <String Id="IDS_ACTIONTEXT_RegisterClassServer">正在注册类服务器</String>
   <String Id="IDS_ACTIONTEXT_RegisterExtensionServers">正在注册扩展服务器</String>
   <String Id="IDS_ACTIONTEXT_RegisterFonts">正在注册字体</String>
   <String Id="IDS_ACTIONTEXT_RegisterMimeInfo">正在注册 MIME 信息</String>
   <String Id="IDS_ACTIONTEXT_RegisterTypeLibs">正在注册类型库</String>
   <String Id="IDS_ACTIONTEXT_RegisteringComPlus">正在注册 COM+ 应用程序和组件</String>
   <String Id="IDS_ACTIONTEXT_RegisteringModules">正在注册模块</String>
   <String Id="IDS_ACTIONTEXT_RegisteringProduct">正在注册产品</String>
   <String Id="IDS_ACTIONTEXT_RegisteringProgIdentifiers">正在注册程序标识符</String>
   <String Id="IDS_ACTIONTEXT_RemoveApps">正在删除应用程序</String>
   <String Id="IDS_ACTIONTEXT_RemovingBackup">正在删除备份文件</String>
   <String Id="IDS_ACTIONTEXT_RemovingDuplicates">正在删除重复的文件</String>
   <String Id="IDS_ACTIONTEXT_RemovingFiles">正在删除文件</String>
   <String Id="IDS_ACTIONTEXT_RemovingFolders">正在删除文件夹</String>
   <String Id="IDS_ACTIONTEXT_RemovingIni">正在删除 INI 文件项</String>
   <String Id="IDS_ACTIONTEXT_RemovingMoved">正在删除移动过的文件</String>
   <String Id="IDS_ACTIONTEXT_RemovingODBC">正在删除 ODBC 组件</String>
   <String Id="IDS_ACTIONTEXT_RemovingRegistry">正在删除系统注册表值</String>
   <String Id="IDS_ACTIONTEXT_RemovingShortcuts">正在删除快捷方式</String>
   <String Id="IDS_ACTIONTEXT_RollingBack">正在回滚操作:</String>
   <String Id="IDS_ACTIONTEXT_SearchForRelated">正在搜索相关的应用程序</String>
   <String Id="IDS_ACTIONTEXT_SearchInstalled">正在搜索已安装的应用程序</String>
   <String Id="IDS_ACTIONTEXT_SearchingQualifyingProducts">正在搜索合格产品</String>
   <String Id="IDS_ACTIONTEXT_ServerConfig">配置 Horizon Connection Server</String>
   <String Id="IDS_ACTIONTEXT_Service">服务: [1]</String>
   <String Id="IDS_ACTIONTEXT_Service2">服务: [2]</String>
   <String Id="IDS_ACTIONTEXT_Service3">服务: [1]</String>
   <String Id="IDS_ACTIONTEXT_Service4">服务: [1]</String>
   <String Id="IDS_ACTIONTEXT_Shortcut">快捷方式: [1]</String>
   <String Id="IDS_ACTIONTEXT_Shortcut1">快捷方式: [1]</String>
   <String Id="IDS_ACTIONTEXT_StartingServices">正在启动服务</String>
   <String Id="IDS_ACTIONTEXT_StoppingServices">正在停止服务</String>
   <String Id="IDS_ACTIONTEXT_UnpublishProductFeatures">正在取消发布产品功能</String>
   <String Id="IDS_ACTIONTEXT_UnpublishQualified">正在取消发布合格组件</String>
   <String Id="IDS_ACTIONTEXT_UnpublishingProductInfo">正在取消发布产品信息</String>
   <String Id="IDS_ACTIONTEXT_UnregTypeLibs">正在取消注册类型库</String>
   <String Id="IDS_ACTIONTEXT_UnregisterClassServers">正在取消注册类服务器</String>
   <String Id="IDS_ACTIONTEXT_UnregisterExtensionServers">正在取消注册扩展服务器</String>
   <String Id="IDS_ACTIONTEXT_UnregisterModules">正在取消注册模块</String>
   <String Id="IDS_ACTIONTEXT_UnregisteringComPlus">正在取消注册 COM+ 应用程序和组件</String>
   <String Id="IDS_ACTIONTEXT_UnregisteringFonts">正在取消注册字体</String>
   <String Id="IDS_ACTIONTEXT_UnregisteringMimeInfo">正在取消注册 MIME 信息</String>
   <String Id="IDS_ACTIONTEXT_UnregisteringProgramIds">正在取消注册程序标识符</String>
   <String Id="IDS_ACTIONTEXT_UpdateComponentRegistration">正在更新组件注册</String>
   <String Id="IDS_ACTIONTEXT_UpdateEnvironmentStrings">正在更新环境字符串</String>
   <String Id="IDS_ACTIONTEXT_Validating">正在验证安装</String>
   <String Id="IDS_ACTIONTEXT_BlastUDPInstall">正在设置 UDP 通信设置</String>
   <String Id="IDS_ACTIONTEXT_BlastUDPUninstall">正在清除 UDP 通信设置</String>
   <String Id="IDS_ACTIONTEXT_WritingINI">正在写入 INI 文件值</String>
   <String Id="IDS_ACTIONTEXT_WritingRegistry">正在写入系统注册表值</String>
   <String Id="UnconfigUserInit">正在取消注册 UserInit 进程: wssm.exe</String>
   <String Id="VM_WaitForpairing_ProgressText">Waiting for agent pairing to complete...</String>

   <!-- UIText Strings -->
   <String Id="IDS_UITEXT_Available">可用</String>
   <String Id="IDS_UITEXT_Bytes">字节</String>
   <String Id="IDS_UITEXT_CompilingFeaturesCost">正在计算这项功能的开销...</String>
   <String Id="IDS_UITEXT_Differences">差额</String>
   <String Id="IDS_UITEXT_DiskSize">磁盘大小</String>
   <String Id="IDS_UITEXT_FeatureCompletelyRemoved">这项功能将被完全删除。</String>
   <String Id="IDS_UITEXT_FeatureContinueNetwork">这项功能将继续从网络上运行</String>
   <String Id="IDS_UITEXT_FeatureFreeSpace">这项功能可释放 [1] 硬盘空间。</String>
   <String Id="IDS_UITEXT_FeatureInstalledCD">这项功能及其所有子功能都将安装为从光盘运行。</String>
   <String Id="IDS_UITEXT_FeatureInstalledCD2">这项功能将安装为从光盘运行。</String>
   <String Id="IDS_UITEXT_FeatureInstalledLocal">这项功能及其所有子功能都将安装在本地硬盘上。</String>
   <String Id="IDS_UITEXT_FeatureInstalledLocal2">这项功能将安装在本地硬盘上。</String>
   <String Id="IDS_UITEXT_FeatureInstalledNetwork">这项功能及其所有子功能都将被安装为从网络运行。</String>
   <String Id="IDS_UITEXT_FeatureInstalledNetwork2">这项功能将安装为从网络运行。</String>
   <String Id="IDS_UITEXT_FeatureInstalledRequired">在需要时安装。</String>
   <String Id="IDS_UITEXT_FeatureInstalledWhenRequired">这项功能将设置为在需要时安装。</String>
   <String Id="IDS_UITEXT_FeatureInstalledWhenRequired2">这项功能将在需要时安装。</String>
   <String Id="IDS_UITEXT_FeatureLocal">这项功能将安装在本地硬盘上。</String>
   <String Id="IDS_UITEXT_FeatureLocal2">这项功能将安装在您的本地硬盘上。</String>
   <String Id="IDS_UITEXT_FeatureNetwork">这项功能将安装为从网络运行。</String>
   <String Id="IDS_UITEXT_FeatureNetwork2">这项功能将从网络运行。</String>
   <String Id="IDS_UITEXT_FeatureNotAvailable">这项功能将不可用。</String>
   <String Id="IDS_UITEXT_FeatureOnCD">这项功能将安装为从光盘运行。</String>
   <String Id="IDS_UITEXT_FeatureOnCD2">这项功能将从光盘运行。</String>
   <String Id="IDS_UITEXT_FeatureRemainLocal">这项功能将保留在本地硬盘上。</String>
   <String Id="IDS_UITEXT_FeatureRemoveNetwork">这项功能将从本地硬盘上删除，但仍可以从网络运行。</String>
   <String Id="IDS_UITEXT_FeatureRemovedCD">这项功能将从本地硬盘上删除，但仍可以从光盘运行。</String>
   <String Id="IDS_UITEXT_FeatureRemovedUnlessRequired">这项功能将从本地硬盘上删除，但会设置为在需要时安装。</String>
   <String Id="IDS_UITEXT_FeatureRequiredSpace">这项功能需要占用 [1] 硬盘空间。</String>
   <String Id="IDS_UITEXT_FeatureRunFromCD">这项功能将继续从光盘运行</String>
   <String Id="IDS_UITEXT_FeatureSpaceFree">这项功能可释放 [1] 硬盘空间。已选择 [3] 项子功能中的 [2] 项。这些子功能可释放 [4] 硬盘空间。</String>
   <String Id="IDS_UITEXT_FeatureSpaceFree2">这项功能可释放 [1] 硬盘空间。已选择 [3] 项子功能中的 [2] 项。这些子功能需要 [4] 硬盘空间。</String>
   <String Id="IDS_UITEXT_FeatureSpaceFree3">这项功能需要 [1] 硬盘空间。已选择 [3] 项子功能中的 [2] 项。这些子功能可释放 [4] 硬盘空间。</String>
   <String Id="IDS_UITEXT_FeatureSpaceFree4">这项功能需要 [1] 硬盘空间。已选择 [3] 项子功能中的 [2] 项。这些子功能需要 [4] 硬盘空间。</String>
   <String Id="IDS_UITEXT_FeatureUnavailable">这项功能将不可用。</String>
   <String Id="IDS_UITEXT_FeatureUninstallNoNetwork">这项功能将被完全卸载，您将无法从网络运行该功能。</String>
   <String Id="IDS_UITEXT_FeatureWasCD">这项功能以前是从光盘运行，但将被设置为在需要时安装。</String>
   <String Id="IDS_UITEXT_FeatureWasCDLocal">这项功能以前是从光盘运行，但将被安装在本地硬盘上。</String>
   <String Id="IDS_UITEXT_FeatureWasOnNetworkInstalled">这项功能以前是从网络运行，但将在需要时安装。</String>
   <String Id="IDS_UITEXT_FeatureWasOnNetworkLocal">这项功能以前是从网络运行，但将被安装在本地硬盘上。</String>
   <String Id="IDS_UITEXT_FeatureWillBeUninstalled">这项功能将被完全卸载，您将无法从光盘运行该功能。</String>
   <String Id="IDS_UITEXT_Folder">Fldr|新建文件夹</String>
   <String Id="IDS_UITEXT_GB">GB</String>
   <String Id="IDS_UITEXT_KB">KB</String>
   <String Id="IDS_UITEXT_MB">MB</String>
   <String Id="IDS_UITEXT_Required">必需</String>
   <String Id="IDS_UITEXT_TimeRemaining">剩余时间: {[1] 分 }[2] 秒</String>
   <String Id="IDS_UITEXT_Volume">卷</String>


   <!-- Error Table Strings -->
   <String Id="IDS_ERROR_0">{{致命错误: }}</String>
   <String Id="IDS_ERROR_1">错误 [1]。</String>
   <String Id="IDS_ERROR_2">警告 [1]。</String>
   <String Id="IDS_ERROR_4">信息 [1]。</String>
   <String Id="IDS_ERROR_5">内部错误 [1]。[2]{，[3]}{，[4]}</String>
   <String Id="IDS_ERROR_7">{{磁盘已满: }}</String>
   <String Id="IDS_ERROR_8">操作 [Time]: [1]。[2]</String>
   <String Id="IDS_ERROR_9">[ProductName]</String>
   <String Id="IDS_ERROR_10">{[2]}{，[3]}{，[4]}</String>
   <String Id="IDS_ERROR_11">消息类型: [1]，参数: [2]</String>
   <String Id="IDS_ERROR_12">=== 记录开始: [Date]  [Time] ===</String>
   <String Id="IDS_ERROR_13">=== 记录停止: [Date]  [Time] ===</String>
   <String Id="IDS_ERROR_14">操作开始 [Time]: [1]。</String>
   <String Id="IDS_ERROR_15">操作结束 [Time]: [1]。返回值 [2]。</String>
   <String Id="IDS_ERROR_16">剩余时间: {[1] 分 }{[2] 秒}</String>
   <String Id="IDS_ERROR_17">内存不足。请关闭其他应用程序，然后再重试。</String>
   <String Id="IDS_ERROR_18">安装程序不再响应。</String>
   <String Id="IDS_ERROR_19">安装程序提前终止。</String>
   <String Id="IDS_ERROR_20">Windows 正在配置 [ProductName]，请稍等</String>
   <String Id="IDS_ERROR_21">正在收集所需信息...</String>
   <String Id="IDS_ERROR_22">正在删除此应用程序的旧版本</String>
   <String Id="IDS_ERROR_23">正在准备删除此应用程序的旧版本</String>
   <String Id="IDS_ERROR_32">{[ProductName] }的安装已成功完成。</String>
   <String Id="IDS_ERROR_33">{[ProductName] }安装失败。</String>
   <String Id="IDS_ERROR_1101">读取文件 [2] 时出错。{{系统错误 [3]。}}请验证该文件是否存在，以及您是否可以访问该文件。</String>
   <String Id="IDS_ERROR_1301">无法创建文件 [3]。具有此名称的目录已存在。请取消安装，然后尝试安装到其他位置。</String>
   <String Id="IDS_ERROR_1302">请插入磁盘: [2]</String>
   <String Id="IDS_ERROR_1303">安装程序没有足够的特权访问此目录: [2]。无法继续安装。请以管理员身份登录或者联系您的系统管理员。</String>
   <String Id="IDS_ERROR_1304">写入文件 [2] 时出错。请验证您是否有权访问该目录。</String>
   <String Id="IDS_ERROR_1305">读取文件 [2] 时出错。请验证该文件是否存在，以及您是否可以访问该文件。</String>
   <String Id="IDS_ERROR_1306">另一个应用程序正在独占访问文件 [2]。请关闭其他所有的应用程序，然后单击“重试”。</String>
   <String Id="IDS_ERROR_1307">磁盘空间不足，无法安装文件 [2]。请释放一些磁盘空间并单击“重试”，或者单击“取消”退出。</String>
   <String Id="IDS_ERROR_1308">找不到源文件: [2]。请验证该文件是否存在，以及您是否可以访问该文件。</String>
   <String Id="IDS_ERROR_1309">读取文件 [3] 时出错。{{系统错误 [2]。}}请验证该文件是否存在，以及您是否可以访问该文件。</String>
   <String Id="IDS_ERROR_1310">写入文件 [3] 时出错。{{系统错误 [2]。}}请验证您是否有权访问该目录。</String>
   <String Id="IDS_ERROR_1311">找不到源文件{{(cabinet)}}: [2]。请验证该文件是否存在，以及您是否可以访问该文件。</String>
   <String Id="IDS_ERROR_1312">无法创建目录 [2]。同名文件已存在。请重命名或删除该文件，然后单击“重试”，或单击“取消”退出。</String>
   <String Id="IDS_ERROR_1313">卷 [2] 当前不可用。请选择其他卷。</String>
   <String Id="IDS_ERROR_1314">指定的路径 [2] 不可用。</String>
   <String Id="IDS_ERROR_1315">无法写入指定的文件夹 [2]。</String>
   <String Id="IDS_ERROR_1316">尝试读取文件 [2] 时发生网络错误</String>
   <String Id="IDS_ERROR_1317">尝试创建目录 [2] 时发生错误</String>
   <String Id="IDS_ERROR_1318">尝试创建目录 [2] 时发生网络错误</String>
   <String Id="IDS_ERROR_1319">尝试打开源文件包 [2] 时发生网络错误。</String>
   <String Id="IDS_ERROR_1320">指定的路径过长 [2]。</String>
   <String Id="IDS_ERROR_1321">安装程序没有足够的特权修改文件 [2]。</String>
   <String Id="IDS_ERROR_1322">路径 [2] 的一部分超出了系统允许的长度。</String>
   <String Id="IDS_ERROR_1323">路径 [2] 包含无效的文件夹名。</String>
   <String Id="IDS_ERROR_1324">路径 [2] 中包含一个无效字符。</String>
   <String Id="IDS_ERROR_1325">[2] 不是有效的短文件名。</String>
   <String Id="IDS_ERROR_1326">获取文件安全权限时发生错误: [3] GetLastError: [2]</String>
   <String Id="IDS_ERROR_1327">无效驱动器: [2]</String>
   <String Id="IDS_ERROR_1328">对文件 [2] 应用修补程序时出错。可能已采用其他方式更新该文件，因而无法再使用此修补程序对其进行修改。有关详细信息，请与修补程序供应商联系。{{系统错误: [3]}}</String>
   <String Id="IDS_ERROR_1329">无法安装所需的文件，因为 CAB 文件 [2] 未经过数字签名。这可能表示 CAB 文件已损坏。</String>
   <String Id="IDS_ERROR_1330">无法安装所需的文件，因为 CAB 文件 [2] 的数字签名无效。这可能表示该 CAB 文件已损坏。{ WinVerifyTrust 返回错误 [3]。}</String>
   <String Id="IDS_ERROR_1331">未能正确复制 [2] 文件: CRC 错误。</String>
   <String Id="IDS_ERROR_1332">未能正确修补 [2] 文件: CRC 错误。</String>
   <String Id="IDS_ERROR_1333">未能正确修补 [2] 文件: CRC 错误。</String>
   <String Id="IDS_ERROR_1334">无法安装文件“[2]”，原因是在 CAB 文件“[3]”中找不到该文件。这可能表示网络错误、从 CD-ROM 读取时出错，或此软件包存在问题。</String>
   <String Id="IDS_ERROR_1335">此安装所需的 CAB 文件“[2]”损坏，已无法使用。这可能表示网络错误、从 CD-ROM 读取时出错，或此软件包存在问题。</String>
   <String Id="IDS_ERROR_1336">在创建完成此安装所需的临时文件时出现错误。文件夹: [3]。系统错误代码: [2]</String>
   <String Id="IDS_ERROR_1401">无法创建键 [2]。{{系统错误 [3]。}}请验证您是否有足够的权限访问该键，或者与技术支持人员联系。</String>
   <String Id="IDS_ERROR_1402">无法打开键 [2]。{{系统错误 [3]。}}请验证您是否有足够的权限访问该键，或者与技术支持人员联系。</String>
   <String Id="IDS_ERROR_1403">无法从键 [3] 中删除值 [2]。{{系统错误 [4]。}}请验证您是否有足够的权限访问该键，或者与技术支持人员联系。</String>
   <String Id="IDS_ERROR_1404">无法删除键 [2]。{{系统错误 [3]。}}请验证您是否有足够的权限访问该键，或者与技术支持人员联系。</String>
   <String Id="IDS_ERROR_1405">无法从键 [3] 中读取值 [2]。{{系统错误 [4]。}}请验证您是否有足够的权限访问该键，或者与技术支持人员联系。</String>
   <String Id="IDS_ERROR_1406">无法将值 [2] 写入键 [3]。{{系统错误 [4]。}}请验证您是否有足够的权限访问该键，或者与技术支持人员联系。</String>
   <String Id="IDS_ERROR_1407">无法获取键 [2] 的值名称。{{系统错误 [3]。}}请验证您是否有足够的权限访问该键，或者与技术支持人员联系。</String>
   <String Id="IDS_ERROR_1408">无法获取键 [2] 的子键名称。{{系统错误 [3]。}}请验证您是否有足够的权限访问该键，或者与技术支持人员联系。</String>
   <String Id="IDS_ERROR_1409">无法读取键 [2] 的安全信息。{{系统错误 [3]。}}请验证您是否有足够的权限访问该键，或者与技术支持人员联系。</String>
   <String Id="IDS_ERROR_1410">无法增加可用的注册表空间。安装此应用程序需要 [2] KB 的可用注册表空间。</String>
   <String Id="IDS_ERROR_1500">另一个安装过程正在进行中。您必须先完成这个安装，才能继续本次安装。</String>
   <String Id="IDS_ERROR_1501">访问安全数据时出错。请确定 Windows 安装程序已正确配置，然后尝试重新安装。</String>
   <String Id="IDS_ERROR_1502">用户 [2] 以前启动过产品 [3] 的安装程序。使用该产品之前，此用户需要再次运行安装程序。您当前的安装将继续进行。</String>
   <String Id="IDS_ERROR_1503">用户 [2] 以前启动过产品 [3] 的安装程序。使用该产品之前，此用户需要再次运行安装程序。</String>
   <String Id="IDS_ERROR_1601">磁盘空间不足 -- 卷: “[2]”；所需空间: [3] KB；可用空间: [4] KB。请释放一些磁盘空间并重试。</String>
   <String Id="IDS_ERROR_1602">是否确定要取消?</String>
   <String Id="IDS_ERROR_1603">文件 [2][3] 正在使用{ 使用者为以下进程: 名称: [4]，ID: [5]，窗口标题: [6]}。请关闭该应用程序并重试。</String>
   <String Id="IDS_ERROR_1604">产品 [2] 已经安装，因此无法安装本产品。这两种产品不兼容。</String>
   <String Id="IDS_ERROR_1605">磁盘空间不足 -- 卷: [2]；所需空间: [3] KB；可用空间: [4] KB。如果回滚被禁用，则有足够的可用空间。单击“中止”退出，单击“重试”重新查看可用磁盘空间，单击“忽略”继续，但不执行回滚。</String>
   <String Id="IDS_ERROR_1606">无法访问网络位置 [2]。</String>
   <String Id="IDS_ERROR_1607">在继续安装前，应关闭以下应用程序:</String>
   <String Id="IDS_ERROR_1608">在将安装此产品的计算机上未发现任何以前安装的兼容产品。</String>
   <String Id="IDS_ERROR_1609">应用安全设置时遇到错误。[2] 不是有效的用户或用户组。这可能是因为软件包存在问题，或连接网络上的域控制器时出现问题。请检查您的网络连接，然后单击“重试”，或单击“取消”结束安装。无法找到用户的 SID，系统错误 [3]</String>
   <String Id="IDS_ERROR_1651">管理员未能向处于播发状态、按用户或计算机管理的应用程序应用修补程序。</String>
   <String Id="IDS_ERROR_1701">键 [2] 无效。请验证是否输入了正确的键。</String>
   <String Id="IDS_ERROR_1702">安装程序必须重新启动系统，才能继续配置 [2]。单击“是”可立即重新启动，单击“否”可稍后重新启动。</String>
   <String Id="IDS_ERROR_1703">您必须重新启动系统，才能使 [2] 中的配置更改生效。单击“是”可立即重新启动，单击“否”可稍后重新启动。</String>
   <String Id="IDS_ERROR_1704">[2] 的安装过程正处于挂起状态。您必须撤消该安装所做的更改才能继续。是否要撤消那些更改?</String>
   <String Id="IDS_ERROR_1705">本产品的前一次安装正在进行。您必须撤消该安装所做的更改才能继续。是否要撤消那些更改?</String>
   <String Id="IDS_ERROR_1706">无法找到产品 [2] 的有效源。Windows 安装程序无法继续。</String>
   <String Id="IDS_ERROR_1707">安装操作已成功完成。</String>
   <String Id="IDS_ERROR_1708">安装操作失败。</String>
   <String Id="IDS_ERROR_1709">产品: [2] -- [3]</String>
   <String Id="IDS_ERROR_1710">您可以将计算机还原到以前的状态，也可以稍后继续安装。是否要还原?</String>
   <String Id="IDS_ERROR_1711">将安装信息写入磁盘时发生错误。请检查以确保有足够的磁盘空间可用，然后单击“重试”，或单击“取消”结束安装。</String>
   <String Id="IDS_ERROR_1712">找不到将计算机还原为先前状态所需的一个或多个文件。无法进行还原。</String>
   <String Id="IDS_ERROR_1713">[2] 无法安装所需的产品之一。请与技术支持小组联系。{{系统错误: [3]。}}</String>
   <String Id="IDS_ERROR_1714">无法移除 [2] 的旧版本。请与技术支持小组联系。{{系统错误 [3]。}}</String>
   <String Id="IDS_ERROR_1715">已安装 [2]。</String>
   <String Id="IDS_ERROR_1716">已配置 [2]。</String>
   <String Id="IDS_ERROR_1717">已移除 [2]。</String>
   <String Id="IDS_ERROR_1718">文件 [2] 被数字签名策略拒绝。</String>
   <String Id="IDS_ERROR_1719">无法访问 Windows Installer 服务。请与技术支持人员联系，确认该服务已正确注册并启用。</String>
   <String Id="IDS_ERROR_1720">此 Windows Installer 软件包存在问题。完成此安装所需的一个脚本无法运行。请与技术支持人员或软件包供应商联系。自定义操作 [2] 脚本错误 [3]，[4]: [5] 行 [6]，列 [7]，[8]</String>
   <String Id="IDS_ERROR_1721">此 Windows Installer 软件包存在问题。完成此安装所需的一个程序无法运行。请与技术支持人员或软件包供应商联系。操作: [2]，位置: [3]，命令: [4]</String>
   <String Id="IDS_ERROR_1722">此 Windows Installer 软件包存在问题。作为此安装程序一部分运行的一个程序未按预期完成。请与技术支持人员或软件包供应商联系。操作 [2]，位置: [3]，命令: [4]</String>
   <String Id="IDS_ERROR_1723">此 Windows Installer 软件包存在问题。完成此安装所需的一个 DLL 无法运行。请与技术支持人员或软件包供应商联系。操作 [2]，条目: [3]，库: [4]</String>
   <String Id="IDS_ERROR_1724">移除成功完成。</String>
   <String Id="IDS_ERROR_1725">移除失败。</String>
   <String Id="IDS_ERROR_1726">播发成功完成。</String>
   <String Id="IDS_ERROR_1727">播发失败。</String>
   <String Id="IDS_ERROR_1728">配置成功完成。</String>
   <String Id="IDS_ERROR_1729">配置失败。</String>
   <String Id="IDS_ERROR_1730">您必须用管理员权限移除此应用程序。要移除此应用程序，请以管理员身份登录，或联系技术支持小组寻求帮助。</String>
   <String Id="IDS_ERROR_1731">产品 [2] 的源安装软件包与客户端软件包不同步。请使用安装软件包“[3]”的有效副本再次尝试安装。</String>
   <String Id="IDS_ERROR_1732">必须重新启动计算机才能完成 [2] 的安装。当前有其他用户登录到此计算机，重新启动可能会使其丢失工作数据。是否要立即重新启动?</String>
   <String Id="IDS_ERROR_1801">路径 [2] 无效。请指定一个有效路径。</String>
   <String Id="IDS_ERROR_1802">内存不足。请关闭其他应用程序，然后再重试。</String>
   <String Id="IDS_ERROR_1803">驱动器 [2] 中没有磁盘。请插入磁盘并单击“重试”，或单击“取消”返回之前选择的卷。</String>
   <String Id="IDS_ERROR_1804">驱动器 [2] 中没有磁盘。请插入磁盘并单击“重试”，或单击“取消”返回浏览对话框，以选择其他的卷。</String>
   <String Id="IDS_ERROR_1805">文件夹 [2] 不存在。请输入一个现有文件夹的路径。</String>
   <String Id="IDS_ERROR_1806">您没有足够的特权读取此文件夹。</String>
   <String Id="IDS_ERROR_1807">无法确定安装所需的有效目标文件夹。</String>
   <String Id="IDS_ERROR_1901">尝试读取源安装数据库时出错: [2]。</String>
   <String Id="IDS_ERROR_1902">正在安排重新启动操作: 正在将文件 [2] 重命名为 [3]。只有重新启动后操作才能完成。</String>
   <String Id="IDS_ERROR_1903">正在安排重新启动操作: 正在删除文件 [2]。只有重新启动后操作才能完成。</String>
   <String Id="IDS_ERROR_1904">模块 [2] 注册失败。HRESULT [3]。请与您的技术支持人员联系。</String>
   <String Id="IDS_ERROR_1905">取消注册模块 [2] 失败。HRESULT [3]。请与您的技术支持人员联系。</String>
   <String Id="IDS_ERROR_1906">缓存包 [2] 失败。错误: [3]。请与您的技术支持人员联系。</String>
   <String Id="IDS_ERROR_1907">无法注册字体 [2]。请验证您是否有足够的权限安装字体，以及系统是否支持此字体。</String>
   <String Id="IDS_ERROR_1908">无法取消注册字体 [2]。请验证您是否有足够的权限删除字体。</String>
   <String Id="IDS_ERROR_1909">无法创建快捷方式 [2]。请确定目标文件夹存在，而且您可以访问该文件夹。</String>
   <String Id="IDS_ERROR_1910">无法删除快捷方式 [2]。请确认该快捷方式文件存在，并且您可以访问该文件。</String>
   <String Id="IDS_ERROR_1911">无法将文件 [2] 注册到类型库中。请与技术支持人员联系。</String>
   <String Id="IDS_ERROR_1912">无法取消将文件 [2] 注册到类型库中。请与技术支持人员联系。</String>
   <String Id="IDS_ERROR_1913">无法更新 INI 文件 [2][3]。请确认该文件存在，并且您可以访问该文件。</String>
   <String Id="IDS_ERROR_1914">无法安排在重新启动时用文件 [2] 替换文件 [3]。请验证您是否拥有对文件 [3] 的写入权限。</String>
   <String Id="IDS_ERROR_1915">删除 ODBC 驱动程序管理器时出错，ODBC 错误 [2]: [3]。请与技术支持人员联系。</String>
   <String Id="IDS_ERROR_1916">安装 ODBC 驱动程序管理器时出错，ODBC 错误 [2]: [3]。请与技术支持人员联系。</String>
   <String Id="IDS_ERROR_1917">删除 ODBC 驱动程序 [4] 时出错，ODBC 错误 [2]: [3]。请验证您是否有足够的特权删除 ODBC 驱动程序。</String>
   <String Id="IDS_ERROR_1918">安装 ODBC 驱动程序 [4] 时出错，ODBC 错误 [2]: [3]。请验证文件 [4] 是否存在以及您是否可以访问该文件。</String>
   <String Id="IDS_ERROR_1919">配置 ODBC 数据源 [4] 时出错，ODBC 错误 [2]: [3]。请验证文件 [4] 是否存在以及您是否可以访问该文件。</String>
   <String Id="IDS_ERROR_1920">服务 [2] ([3]) 启动失败。请验证您是否有足够的特权启动系统服务。</String>
   <String Id="IDS_ERROR_1921">无法停止服务 [2] ([3])。请验证您是否有足够的特权停止系统服务。</String>
   <String Id="IDS_ERROR_1922">无法删除服务 [2] ([3])。请验证您是否有足够的特权删除系统服务。</String>
   <String Id="IDS_ERROR_1923">无法安装服务 [2] ([3])。请验证您是否有足够的特权安装系统服务。</String>
   <String Id="IDS_ERROR_1924">无法更新环境变量 [2]。请验证您是否有足够的特权修改环境变量。</String>
   <String Id="IDS_ERROR_1925">您没有足够的特权为这台计算机的所有用户完成此安装。请以管理员的身份登录，然后重新尝试进行此安装。</String>
   <String Id="IDS_ERROR_1926">无法为文件 [3] 设置文件安全性。错误: [2]。请验证您是否有足够的特权修改此文件的安全权限。</String>
   <String Id="IDS_ERROR_1927">此计算机上未安装组件服务 (COM+ 1.0)。此安装需要组件服务才能成功完成。Windows 2000 提供有组件服务。</String>
   <String Id="IDS_ERROR_1928">注册 COM+ 应用程序时出错。有关详细信息，请与技术支持人员联系。</String>
   <String Id="IDS_ERROR_1929">取消注册 COM+ 应用程序时出错。有关详细信息，请与技术支持人员联系。</String>
   <String Id="IDS_ERROR_1930">无法更改服务“[2]”([3]) 的描述。</String>
   <String Id="IDS_ERROR_1931">Windows Installer 服务无法更新系统文件 [2]，因为该文件受 Windows 保护。您可能需要更新操作系统才能使此程序正常运行。{{软件包版本: [3]，操作系统保护的版本: [4]}}</String>
   <String Id="IDS_ERROR_1932">Windows Installer 服务无法更新受保护的 Windows 文件 [2]。{{软件包版本: [3]，操作系统保护的版本: [4]，SFP 错误: [5]}}</String>
   <String Id="IDS_ERROR_1933">Windows Installer 服务无法更新一个或多个受保护的 Windows 文件。SFP 错误: [2]。受保护文件列表: [3]</String>
   <String Id="IDS_ERROR_1934">计算机上的策略禁止用户安装。</String>
   <String Id="IDS_ERROR_1935">安装程序集组件 [2] 的过程中遇到错误。HRESULT: [3]。{{程序集接口: [4]，函数: [5]，程序集名称: [6]}}</String>
   <String Id="IDS_ERROR_1936">安装程序集“[6]”的过程中遇到错误。此程序集未严格命名，或签名未达到最小密钥长度。HRESULT: [3]。{{程序集接口: [4]，函数: [5]，组件: [2]}}</String>
   <String Id="IDS_ERROR_1937">安装程序集“[6]”的过程中遇到错误。签名或目录无法验证或无效。HRESULT: [3]。{{程序集接口: [4]，函数: [5]，组件: [2]}}</String>
   <String Id="IDS_ERROR_1938">安装程序集“[6]”的过程中遇到错误。该程序集的一个或多个模块无法找到。HRESULT: [3]。{{程序集接口: [4]，函数: [5]，组件: [2]}}</String>
   <String Id="IDS_ERROR_2101">操作系统不支持快捷方式。</String>
   <String Id="IDS_ERROR_2102">无效的 .ini 操作: [2]</String>
   <String Id="IDS_ERROR_2103">无法解析 Shell 文件夹 [2] 的路径。</String>
   <String Id="IDS_ERROR_2104">正在写入 .ini 文件: [3]: 系统错误: [2]。</String>
   <String Id="IDS_ERROR_2105">快捷方式创建 [3] 失败。系统错误: [2]。</String>
   <String Id="IDS_ERROR_2106">快捷方式删除 [3] 失败。系统错误: [2]。</String>
   <String Id="IDS_ERROR_2107">注册类型库 [2] 时出现错误 [3]。</String>
   <String Id="IDS_ERROR_2108">取消注册类型库 [2] 时出现错误 [3]。</String>
   <String Id="IDS_ERROR_2109">.ini 操作缺少节。</String>
   <String Id="IDS_ERROR_2110">.ini 操作缺少项。</String>
   <String Id="IDS_ERROR_2111">检测正在运行的应用程序失败，无法获取性能数据。注册操作返回: [2]。</String>
   <String Id="IDS_ERROR_2112">检测正在运行的应用程序失败，无法获取性能索引。注册操作返回: [2]。</String>
   <String Id="IDS_ERROR_2113">检测正在运行的应用程序失败。</String>
   <String Id="IDS_ERROR_2200">数据库: [2]。创建数据库对象失败，模式 = [3]。</String>
   <String Id="IDS_ERROR_2201">数据库: [2]。初始化失败，内存不足。</String>
   <String Id="IDS_ERROR_2202">数据库: [2]。数据访问失败，内存不足。</String>
   <String Id="IDS_ERROR_2203">数据库: [2]。无法打开数据库文件。系统错误 [3]。</String>
   <String Id="IDS_ERROR_2204">数据库: [2]。表已存在: [3]。</String>
   <String Id="IDS_ERROR_2205">数据库: [2]。表不存在: [3]。</String>
   <String Id="IDS_ERROR_2206">数据库: [2]。无法丢弃表: [3]。</String>
   <String Id="IDS_ERROR_2207">数据库: [2]。与意向不符。</String>
   <String Id="IDS_ERROR_2208">数据库: [2]。参数不足，无法执行。</String>
   <String Id="IDS_ERROR_2209">数据库: [2]。光标处于无效状态。</String>
   <String Id="IDS_ERROR_2210">数据库: [2]。列 [3] 中的更新数据类型无效。</String>
   <String Id="IDS_ERROR_2211">数据库: [2]。无法创建数据库表 [3]。</String>
   <String Id="IDS_ERROR_2212">数据库: [2]。数据库未处于可写状态。</String>
   <String Id="IDS_ERROR_2213">数据库: [2]。保存数据库表时出错。</String>
   <String Id="IDS_ERROR_2214">数据库: [2]。写入导出文件时出错: [3]。</String>
   <String Id="IDS_ERROR_2215">数据库: [2]。无法打开导入文件: [3]。</String>
   <String Id="IDS_ERROR_2216">数据库: [2]。导入文件格式错误: [3]，行 [4]。</String>
   <String Id="IDS_ERROR_2217">数据库: [2]。错误状态下无法执行 CreateOutputDatabase [3]。</String>
   <String Id="IDS_ERROR_2218">数据库: [2]。未提供表名。</String>
   <String Id="IDS_ERROR_2219">数据库: [2]。安装程序数据库格式无效。</String>
   <String Id="IDS_ERROR_2220">数据库: [2]。行/字段数据无效。</String>
   <String Id="IDS_ERROR_2221">数据库: [2]。导入文件中存在代码页冲突: [3]。</String>
   <String Id="IDS_ERROR_2222">数据库: [2]。转换或合并代码页 [3] 与数据库代码页 [4] 不同。</String>
   <String Id="IDS_ERROR_2223">数据库: [2]。数据库相同。未生成转换。</String>
   <String Id="IDS_ERROR_2224">数据库: [2]。生成转换: 数据库损坏。表: [3]。</String>
   <String Id="IDS_ERROR_2225">数据库: [2]。转换: 无法转换临时表。表: [3]。</String>
   <String Id="IDS_ERROR_2226">数据库: [2]。转换失败。</String>
   <String Id="IDS_ERROR_2227">数据库: [2]。SQL 查询中的标识符“[3]”无效: [4]。</String>
   <String Id="IDS_ERROR_2228">数据库: [2]。SQL 查询中存在未知表“[3]”: [4]。</String>
   <String Id="IDS_ERROR_2229">数据库: [2]。无法在 SQL 查询中加载表“[3]”: [4]。</String>
   <String Id="IDS_ERROR_2230">数据库: [2]。SQL 查询中的表“[3]”重复: [4]。</String>
   <String Id="IDS_ERROR_2231">数据库: [2]。SQL 查询中缺少“)”: [3]。</String>
   <String Id="IDS_ERROR_2232">数据库: [2]。SQL 查询中存在意外的令牌“[3]”: [4]。</String>
   <String Id="IDS_ERROR_2233">数据库: [2]。SQL 查询的 SELECT 子句中未包含列: [3]。</String>
   <String Id="IDS_ERROR_2234">数据库: [2]。SQL 查询的 ORDER BY 子句中未包含列: [3]。</String>
   <String Id="IDS_ERROR_2235">数据库: [2]。SQL 查询中不存在列“[3]”或该列不明确: [4]。</String>
   <String Id="IDS_ERROR_2236">数据库: [2]。SQL 查询中的运算符“[3]”无效: [4]。</String>
   <String Id="IDS_ERROR_2237">数据库: [2]。查询字符串无效或缺失: [3]。</String>
   <String Id="IDS_ERROR_2238">数据库: [2]。SQL 查询中缺少 FROM 子句: [3]。</String>
   <String Id="IDS_ERROR_2239">数据库: [2]。INSERT SQL 语句中值的数量不足。</String>
   <String Id="IDS_ERROR_2240">数据库: [2]。UPDATE SQL 语句中缺少更新列。</String>
   <String Id="IDS_ERROR_2241">数据库: [2]。INSERT SQL 语句中缺少插入列。</String>
   <String Id="IDS_ERROR_2242">数据库: [2]。列“[3]”重复。</String>
   <String Id="IDS_ERROR_2243">数据库: [2]。没有为创建表定义主列。</String>
   <String Id="IDS_ERROR_2244">数据库: [2]。SQL 查询 [4] 中的类型说明符“[3]”无效。</String>
   <String Id="IDS_ERROR_2245">IStorage::Stat 失败，出现错误 [3]。</String>
   <String Id="IDS_ERROR_2246">数据库: [2]。安装程序转换格式无效。</String>
   <String Id="IDS_ERROR_2247">数据库: [2] 转换流读/写失败。</String>
   <String Id="IDS_ERROR_2248">数据库: [2] 生成转换/合并: 基表中的列类型与引用表不匹配。表: [3] 列号: [4]。</String>
   <String Id="IDS_ERROR_2249">数据库: [2] 生成转换: 基表中的列多于引用表中的列。表: [3]。</String>
   <String Id="IDS_ERROR_2250">数据库: [2] 转换: 无法添加现有行。表: [3]。</String>
   <String Id="IDS_ERROR_2251">数据库: [2] 转换: 无法删除不存在的行。表: [3]。</String>
   <String Id="IDS_ERROR_2252">数据库: [2] 转换: 无法添加现有表。表: [3]。</String>
   <String Id="IDS_ERROR_2253">数据库: [2] 转换: 无法删除不存在的表。表: [3]。</String>
   <String Id="IDS_ERROR_2254">数据库: [2] 转换: 无法更新不存在的行。表: [3]。</String>
   <String Id="IDS_ERROR_2255">数据库: [2] 转换: 已存在同名的列。表: [3] 列: [4]。</String>
   <String Id="IDS_ERROR_2256">数据库: [2] 生成转换/合并: 基表中的主键数与引用表不匹配。表: [3]。</String>
   <String Id="IDS_ERROR_2257">数据库: [2]。试图修改只读表: [3]。</String>
   <String Id="IDS_ERROR_2258">数据库: [2]。参数中的类型不匹配: [3]。</String>
   <String Id="IDS_ERROR_2259">数据库: [2] 表更新失败</String>
   <String Id="IDS_ERROR_2260">存储 CopyTo 失败。系统错误: [3]。</String>
   <String Id="IDS_ERROR_2261">无法移除流 [2]。系统错误: [3]。</String>
   <String Id="IDS_ERROR_2262">流不存在: [2]。系统错误: [3]。</String>
   <String Id="IDS_ERROR_2263">无法打开流 [2]。系统错误: [3]。</String>
   <String Id="IDS_ERROR_2264">无法移除流 [2]。系统错误: [3]。</String>
   <String Id="IDS_ERROR_2265">无法提交存储。系统错误: [3]。</String>
   <String Id="IDS_ERROR_2266">无法回滚存储。系统错误: [3]。</String>
   <String Id="IDS_ERROR_2267">无法删除存储 [2]。系统错误: [3]。</String>
   <String Id="IDS_ERROR_2268">数据库: [2]。合并: 已报告 [3] 个表中存在合并冲突。</String>
   <String Id="IDS_ERROR_2269">数据库: [2]。合并: 两个数据库的“[3]”表中的列数不同。</String>
   <String Id="IDS_ERROR_2270">数据库: [2]。生成转换/合并: 基表中的列名与引用表不匹配。表: [3] 列号: [4]。</String>
   <String Id="IDS_ERROR_2271">针对转换的 SummaryInformation 写入操作失败。</String>
   <String Id="IDS_ERROR_2272">数据库: [2]。MergeDatabase 不会写入任何更改，因为该数据库是以只读方式打开的。</String>
   <String Id="IDS_ERROR_2273">数据库: [2]。MergeDatabase: 对基数据库的引用被作为引用数据库传递。</String>
   <String Id="IDS_ERROR_2274">数据库: [2]。MergeDatabase: 无法将错误写入错误表。原因可能是预定义的错误表中存在不可为空的列。</String>
   <String Id="IDS_ERROR_2275">数据库: [2]。指定的修改 [3] 操作对表联接无效。</String>
   <String Id="IDS_ERROR_2276">数据库: [2]。系统不支持代码页 [3]。</String>
   <String Id="IDS_ERROR_2277">数据库: [2]。保存表 [3] 失败。</String>
   <String Id="IDS_ERROR_2278">数据库: [2]。SQL 查询的 WHERE 子句中表达式数量超过了 32 个的限制: [3]。</String>
   <String Id="IDS_ERROR_2279">数据库: [2] 转换: 基表 [3] 中的列过多。</String>
   <String Id="IDS_ERROR_2280">数据库: [2]。无法为表 [4] 创建列 [3]。</String>
   <String Id="IDS_ERROR_2281">无法重命名流 [2]。系统错误: [3]。</String>
   <String Id="IDS_ERROR_2282">流名称无效 [2]。</String>
   <String Id="IDS_ERROR_2302">修补程序通知: 到目前为止已修补 [2] 个字节。</String>
   <String Id="IDS_ERROR_2303">获取卷信息时出错。GetLastError: [2]。</String>
   <String Id="IDS_ERROR_2304">获取磁盘可用空间时出错。GetLastError: [2]。卷: [3]。</String>
   <String Id="IDS_ERROR_2305">等待修补程序线程时出错。GetLastError: [2]。</String>
   <String Id="IDS_ERROR_2306">无法为修补应用程序创建线程。GetLastError: [2]。</String>
   <String Id="IDS_ERROR_2307">源文件键名为空。</String>
   <String Id="IDS_ERROR_2308">目标文件名为空。</String>
   <String Id="IDS_ERROR_2309">在修补程序运行时尝试修补文件 [2]。</String>
   <String Id="IDS_ERROR_2310">在没有修补程序运行时尝试继续修补。</String>
   <String Id="IDS_ERROR_2315">缺少路径分隔符: [2]。</String>
   <String Id="IDS_ERROR_2318">文件不存在: [2]。</String>
   <String Id="IDS_ERROR_2319">设置文件属性时出错: [3] GetLastError: [2]。</String>
   <String Id="IDS_ERROR_2320">文件不可写入: [2]。</String>
   <String Id="IDS_ERROR_2321">创建文件时出错: [2]。</String>
   <String Id="IDS_ERROR_2322">用户已取消。</String>
   <String Id="IDS_ERROR_2323">无效的文件属性。</String>
   <String Id="IDS_ERROR_2324">无法打开文件: [3] GetLastError: [2]。</String>
   <String Id="IDS_ERROR_2325">无法获取文件的文件时间: [3] GetLastError: [2]。</String>
   <String Id="IDS_ERROR_2326">FileToDosDateTime 出错。</String>
   <String Id="IDS_ERROR_2327">无法移除目录: [3] GetLastError: [2]。</String>
   <String Id="IDS_ERROR_2328">获取文件的文件版本信息时出错: [2]。</String>
   <String Id="IDS_ERROR_2329">删除文件时出错: [3]。GetLastError: [2]。</String>
   <String Id="IDS_ERROR_2330">获取文件属性时出错: [3]。GetLastError: [2]。</String>
   <String Id="IDS_ERROR_2331">加载库 [2] 或查找入口点 [3] 时出错。</String>
   <String Id="IDS_ERROR_2332">获取文件属性时出错。GetLastError: [2]。</String>
   <String Id="IDS_ERROR_2333">设置文件属性时出错。GetLastError: [2]。</String>
   <String Id="IDS_ERROR_2334">将文件的文件时间转换为本地时间时出错: [3]。GetLastError: [2]。</String>
   <String Id="IDS_ERROR_2335">路径: [2] 不是 [3] 的父项。</String>
   <String Id="IDS_ERROR_2336">在路径中创建临时文件时出错: [3]。GetLastError: [2]。</String>
   <String Id="IDS_ERROR_2337">无法关闭文件: [3] GetLastError: [2]。</String>
   <String Id="IDS_ERROR_2338">无法更新文件的资源: [3] GetLastError: [2]。</String>
   <String Id="IDS_ERROR_2339">无法设置文件的文件时间: [3] GetLastError: [2]。</String>
   <String Id="IDS_ERROR_2340">无法更新文件的资源: [3]，缺少资源。</String>
   <String Id="IDS_ERROR_2341">无法更新文件的资源: [3]，资源太大。</String>
   <String Id="IDS_ERROR_2342">无法更新文件的资源: [3] GetLastError: [2]。</String>
   <String Id="IDS_ERROR_2343">指定的路径为空。</String>
   <String Id="IDS_ERROR_2344">无法找到所需的文件 IMAGEHLP.DLL 来验证文件: [2]。</String>
   <String Id="IDS_ERROR_2345">[2]: 文件未包含有效的校验和值。</String>
   <String Id="IDS_ERROR_2347">用户忽略。</String>
   <String Id="IDS_ERROR_2348">尝试从 CAB 流读取时出错。</String>
   <String Id="IDS_ERROR_2349">使用不同信息继续复制。</String>
   <String Id="IDS_ERROR_2350">FDI 服务器错误</String>
   <String Id="IDS_ERROR_2351">在 CAB 文件“[3]”中未找到文件键“[2]”。安装无法继续。</String>
   <String Id="IDS_ERROR_2352">无法初始化 CAB 文件服务器。可能缺少所需的文件“CABINET.DLL”。</String>
   <String Id="IDS_ERROR_2353">不是 CAB 文件。</String>
   <String Id="IDS_ERROR_2354">无法处理 CAB 文件。</String>
   <String Id="IDS_ERROR_2355">损坏的 CAB 文件。</String>
   <String Id="IDS_ERROR_2356">在流中找不到 CAB 文件: [2]。</String>
   <String Id="IDS_ERROR_2357">无法设置属性。</String>
   <String Id="IDS_ERROR_2358">确定文件是否正在使用时出错: [3]。GetLastError: [2]。</String>
   <String Id="IDS_ERROR_2359">无法创建目标文件 – 文件可能正在使用中。</String>
   <String Id="IDS_ERROR_2360">进度停滞。</String>
   <String Id="IDS_ERROR_2361">需要下一个 CAB 文件。</String>
   <String Id="IDS_ERROR_2362">未找到文件夹: [2]。</String>
   <String Id="IDS_ERROR_2363">无法枚举文件夹的子文件夹: [2]。</String>
   <String Id="IDS_ERROR_2364">CreateCopier 调用中存在错误枚举常量。</String>
   <String Id="IDS_ERROR_2365">无法对 exe 文件 [2] 执行 BindImage。</String>
   <String Id="IDS_ERROR_2366">用户失败。</String>
   <String Id="IDS_ERROR_2367">用户中止。</String>
   <String Id="IDS_ERROR_2368">未能获得网络资源信息。错误 [2]，网络路径 [3]。扩展错误: 网络提供商 [5]，错误代码 [4]，错误描述 [6]。</String>
   <String Id="IDS_ERROR_2370">[2] 文件的 CRC 校验和值无效。{文件的标头说明校验和值为 [3]，而其计算值为 [4]。}</String>
   <String Id="IDS_ERROR_2371">无法将修补程序应用到文件 [2]。GetLastError: [3]。</String>
   <String Id="IDS_ERROR_2372">修补程序文件 [2] 已损坏或格式无效。正在尝试修补文件 [3]。GetLastError: [4]。</String>
   <String Id="IDS_ERROR_2373">文件 [2] 不是有效的修补程序文件。</String>
   <String Id="IDS_ERROR_2374">文件 [2] 不是修补程序文件 [3] 的有效目标文件。</String>
   <String Id="IDS_ERROR_2375">未知的修补错误: [2]。</String>
   <String Id="IDS_ERROR_2376">找不到 CAB 文件。</String>
   <String Id="IDS_ERROR_2379">打开文件进行读取时出错: [3] GetLastError: [2]。</String>
   <String Id="IDS_ERROR_2380">打开文件进行写入时出错: [3]。GetLastError: [2]。</String>
   <String Id="IDS_ERROR_2381">目录不存在: [2]。</String>
   <String Id="IDS_ERROR_2382">驱动器未就绪: [2]。</String>
   <String Id="IDS_ERROR_2401">尝试在 32 位操作系统上对项 [2] 进行 64 位注册表操作。</String>
   <String Id="IDS_ERROR_2402">内存不足。</String>
   <String Id="IDS_ERROR_2501">无法创建回滚脚本枚举器。</String>
   <String Id="IDS_ERROR_2502">在未进行安装的情况下调用了 InstallFinalize。</String>
   <String Id="IDS_ERROR_2503">在未标记为正在进行的情况下调用了 RunScript。</String>
   <String Id="IDS_ERROR_2601">属性 [2] 的值无效: “[3]”</String>
   <String Id="IDS_ERROR_2602">[2] 表条目“[3]”在 Media 表中没有关联的条目。</String>
   <String Id="IDS_ERROR_2603">表名称 [2] 重复。</String>
   <String Id="IDS_ERROR_2604">[2] 属性未定义。</String>
   <String Id="IDS_ERROR_2605">在 [3] 或 [4] 中找不到服务器 [2]。</String>
   <String Id="IDS_ERROR_2606">属性 [2] 的值不是有效的完整路径: “[3]”。</String>
   <String Id="IDS_ERROR_2607">未找到 Media 表或该表为空 (安装文件时需要使用)。</String>
   <String Id="IDS_ERROR_2608">无法为对象创建安全描述符。错误: “[2]”。</String>
   <String Id="IDS_ERROR_2609">尝试在初始化前迁移产品设置。</String>
   <String Id="IDS_ERROR_2611">文件 [2] 标记为已压缩，但关联的介质条目未指定 CAB 文件。</String>
   <String Id="IDS_ERROR_2612">在“[2]”列中未找到流。主键: “[3]”。</String>
   <String Id="IDS_ERROR_2613">RemoveExistingProducts 操作顺序不正确。</String>
   <String Id="IDS_ERROR_2614">无法从安装软件包访问 IStorage 对象。</String>
   <String Id="IDS_ERROR_2615">由于源解析失败，已跳过对模块 [2] 的取消注册。</String>
   <String Id="IDS_ERROR_2616">缺少辅助文件 [2] 父项。</String>
   <String Id="IDS_ERROR_2617">在 Component 表中未找到共享组件 [2]。</String>
   <String Id="IDS_ERROR_2618">在 Component 表中未找到独立的应用程序组件 [2]。</String>
   <String Id="IDS_ERROR_2619">独立组件 [2]、[3] 不属于同一功能。</String>
   <String Id="IDS_ERROR_2620">独立应用程序组件 [2] 的密钥文件不在 File 表中。</String>
   <String Id="IDS_ERROR_2621">快捷方式 [2] 的资源 DLL 或资源 ID 信息设置不正确。</String>
   <String Id="IDS_ERROR_2701">功能的深度超过允许的 [2] 级树深度。</String>
   <String Id="IDS_ERROR_2702">Feature 表记录 ([2]) 引用了“属性”字段中不存在的父项。</String>
   <String Id="IDS_ERROR_2703">未定义根源路径的属性名称: [2]</String>
   <String Id="IDS_ERROR_2704">未定义根目录属性: [2]</String>
   <String Id="IDS_ERROR_2705">无效的表: [2]；无法作为树进行链接。</String>
   <String Id="IDS_ERROR_2706">未创建源路径。Directory 表中的条目 [2] 无路径。</String>
   <String Id="IDS_ERROR_2707">未创建目标路径。Directory 表中的条目 [2] 无路径。</String>
   <String Id="IDS_ERROR_2708">在 File 表中未找到条目。</String>
   <String Id="IDS_ERROR_2709">在 Component 表中未找到指定的组件名称 (“[2]”)。</String>
   <String Id="IDS_ERROR_2710">请求的“Select”状态对于此组件是非法的。</String>
   <String Id="IDS_ERROR_2711">在 Feature 表中未找到指定的功能名称 (“[2]”)。</String>
   <String Id="IDS_ERROR_2712">在操作 [2] 中，无模式对话框的返回结果无效: [3]。</String>
   <String Id="IDS_ERROR_2713">不可为空的列中存在空值 (“[4]”表的“[3]”列中的“[2]”)。</String>
   <String Id="IDS_ERROR_2714">默认文件夹名称的值无效: [2]。</String>
   <String Id="IDS_ERROR_2715">在 File 表中未找到指定的文件键 (“[2]”)。</String>
   <String Id="IDS_ERROR_2716">无法为组件“[2]”创建随机子组件名称。</String>
   <String Id="IDS_ERROR_2717">操作条件不正确或调用自定义操作“[2]”时出错。</String>
   <String Id="IDS_ERROR_2718">产品代码“[2]”缺少软件包名称。</String>
   <String Id="IDS_ERROR_2719">在源“[2]”中未找到 UNC 和驱动器盘符路径。</String>
   <String Id="IDS_ERROR_2720">打开源列表键时出错。错误: “[2]”</String>
   <String Id="IDS_ERROR_2721">在 Binary 表流中未找到自定义操作 [2]。</String>
   <String Id="IDS_ERROR_2722">在 File 表中未找到自定义操作 [2]。</String>
   <String Id="IDS_ERROR_2723">自定义操作 [2] 指定了不受支持的类型。</String>
   <String Id="IDS_ERROR_2724">所运行介质上的卷标“[2]”与 Media 表中给出的卷标“[3]”不匹配。只有在 Media 表中仅有 1 个条目时才允许这种情况。</String>
   <String Id="IDS_ERROR_2725">数据库表无效</String>
   <String Id="IDS_ERROR_2726">未找到操作: [2]。</String>
   <String Id="IDS_ERROR_2727">Directory 表中不存在目录条目“[2]”。</String>
   <String Id="IDS_ERROR_2728">表定义错误: [2]</String>
   <String Id="IDS_ERROR_2729">安装引擎未初始化。</String>
   <String Id="IDS_ERROR_2730">数据库中的值错误。表: “[2]”；主键: “[3]”；列: “[4]”</String>
   <String Id="IDS_ERROR_2731">选择管理器未初始化。</String>
   <String Id="IDS_ERROR_2732">目录管理器未初始化。</String>
   <String Id="IDS_ERROR_2733">“[4]”表的“[3]”列中的外键 (“[2]”) 错误。</String>
   <String Id="IDS_ERROR_2734">重新安装模式字符无效。</String>
   <String Id="IDS_ERROR_2735">自定义操作“[2]”导致了无法处理的异常，已经停止。这可能是自定义操作的内部错误 (如访问冲突) 导致的。</String>
   <String Id="IDS_ERROR_2736">生成自定义操作临时文件失败: [2]。</String>
   <String Id="IDS_ERROR_2737">无法访问自定义操作 [2]、条目 [3]、库 [4]</String>
   <String Id="IDS_ERROR_2738">无法访问自定义操作 [2] 的 VBScript 运行时。</String>
   <String Id="IDS_ERROR_2739">无法访问自定义操作 [2] 的 JavaScript 运行时。</String>
   <String Id="IDS_ERROR_2740">自定义操作 [2] 脚本错误 [3]，[4]: [5] 行 [6]，列 [7]，[8]。</String>
   <String Id="IDS_ERROR_2741">产品 [2] 的配置信息损坏。无效的信息: [2]。</String>
   <String Id="IDS_ERROR_2742">封送到服务器失败: [2]。</String>
   <String Id="IDS_ERROR_2743">无法执行自定义操作 [2]，位置: [3]，命令: [4]。</String>
   <String Id="IDS_ERROR_2744">自定义操作 [2] 调用 EXE 失败，位置: [3]，命令: [4]。</String>
   <String Id="IDS_ERROR_2745">转换 [2] 对于软件包 [3] 无效。所需语言为 [4]，而找到的语言为 [5]。</String>
   <String Id="IDS_ERROR_2746">转换 [2] 对于软件包 [3] 无效。所需产品为 [4]，而找到的产品为 [5]。</String>
   <String Id="IDS_ERROR_2747">转换 [2] 对于软件包 [3] 无效。所需产品版本应低于 [4]，而找到的产品版本为 [5]。</String>
   <String Id="IDS_ERROR_2748">转换 [2] 对于软件包 [3] 无效。所需产品版本应不高于 [4]，而找到的产品版本为 [5]。</String>
   <String Id="IDS_ERROR_2749">转换 [2] 对于软件包 [3] 无效。所需产品版本应为 [4]，而找到的产品版本为 [5]。</String>
   <String Id="IDS_ERROR_2750">转换 [2] 对于软件包 [3] 无效。所需产品版本应不低于 [4]，而找到的产品版本为 [5]。</String>
   <String Id="IDS_ERROR_2751">转换 [2] 对于软件包 [3] 无效。所需产品版本应高于 [4]，而找到的产品版本为 [5]。</String>
   <String Id="IDS_ERROR_2752">无法打开存储为软件包 [4] 的子存储的转换 [2]。</String>
   <String Id="IDS_ERROR_2753">文件“[2]”未标记为安装。</String>
   <String Id="IDS_ERROR_2754">文件“[2]”不是有效的修补程序文件。</String>
   <String Id="IDS_ERROR_2755">尝试安装软件包 [3] 时服务器返回意外错误 [2]。</String>
   <String Id="IDS_ERROR_2756">属性“[2]”在一个或多个表中用作目录属性，但从未向其分配值。</String>
   <String Id="IDS_ERROR_2757">无法为转换 [2] 创建摘要信息。</String>
   <String Id="IDS_ERROR_2758">转换 [2] 不包含 MSI 版本。</String>
   <String Id="IDS_ERROR_2759">转换 [2] 版本 [3] 与引擎不兼容；最低版本: [4]，最高版本: [5]。</String>
   <String Id="IDS_ERROR_2760">转换 [2] 对于软件包 [3] 无效。所需升级代码为 [4]，而找到的升级代码为 [5]。</String>
   <String Id="IDS_ERROR_2761">无法开始事务。全局互斥体未正确初始化。</String>
   <String Id="IDS_ERROR_2762">无法写入脚本记录。事务未启动。</String>
   <String Id="IDS_ERROR_2763">无法运行脚本。事务未启动。</String>
   <String Id="IDS_ERROR_2765">AssemblyName 表中缺少程序集名称: 组件: [4]。</String>
   <String Id="IDS_ERROR_2766">文件 [2] 为无效的 MSI 存储文件。</String>
   <String Id="IDS_ERROR_2767">{枚举 [2] 时}没有其他数据。</String>
   <String Id="IDS_ERROR_2768">修补程序包中的转换无效。</String>
   <String Id="IDS_ERROR_2769">自定义操作 [2] 未关闭 [3] MSIHANDLE。</String>
   <String Id="IDS_ERROR_2770">内部缓存文件夹表中未定义缓存文件夹 [2]。</String>
   <String Id="IDS_ERROR_2771">功能 [2] 的升级缺少一个组件。</String>
   <String Id="IDS_ERROR_2772">新的升级功能 [2] 必须是叶功能。</String>
   <String Id="IDS_ERROR_2801">未知消息 -- 类型 [2]。未采取任何操作。</String>
   <String Id="IDS_ERROR_2802">找不到事件 [2] 的发布服务器。</String>
   <String Id="IDS_ERROR_2803">对话框视图未找到对话框 [2] 的记录。</String>
   <String Id="IDS_ERROR_2804">激活对话框 [2] 上的控件 [3] 时，CMsiDialog 未能评估条件 [3]。</String>
   <String Id="IDS_ERROR_2806">对话框 [2] 未能评估条件 [3]。</String>
   <String Id="IDS_ERROR_2807">操作 [2] 未被识别。</String>
   <String Id="IDS_ERROR_2808">对话框 [2] 上未正确定义“默认”按钮。</String>
   <String Id="IDS_ERROR_2809">在对话框 [2] 上，后续控件指针未形成循环。存在从 [3] 指向 [4] 的指针，但没有其他指针。</String>
   <String Id="IDS_ERROR_2810">在对话框 [2] 上，后续控件指针未形成循环。存在从 [3] 和 [5] 指向 [4] 的指针。</String>
   <String Id="IDS_ERROR_2811">在对话框 [2] 上，控件 [3] 必须获得焦点，但是它无法获得焦点。</String>
   <String Id="IDS_ERROR_2812">事件 [2] 未被识别。</String>
   <String Id="IDS_ERROR_2813">使用参数 [2] 调用了 EndDialog 事件，但该对话框具有父项。</String>
   <String Id="IDS_ERROR_2814">在对话框 [2] 上，控件 [3] 将不存在的控件 [4] 命名为下一控件。</String>
   <String Id="IDS_ERROR_2815">ControlCondition 表中有一行未包含对话框 [2] 的条件。</String>
   <String Id="IDS_ERROR_2816">EventMapping 表为事件 [3] 引用了对话框 [2] 上的无效控件 [4]。</String>
   <String Id="IDS_ERROR_2817">事件 [2] 未能设置对话框 [3] 上控件 [4] 的属性。</String>
   <String Id="IDS_ERROR_2818">在 ControlEvent 表中，EndDialog 具有未识别的参数 [2]。</String>
   <String Id="IDS_ERROR_2819">对话框 [2] 上的控件 [3] 需要一个与之链接的属性。</String>
   <String Id="IDS_ERROR_2820">尝试初始化一个已初始化的处理程序。</String>
   <String Id="IDS_ERROR_2821">尝试初始化一个已初始化的对话框: [2]。</String>
   <String Id="IDS_ERROR_2822">在添加所有控件后，才能为对话框 [2] 调用其他方法。</String>
   <String Id="IDS_ERROR_2823">尝试初始化对话框 [2] 上已初始化的控件: [3]。</String>
   <String Id="IDS_ERROR_2824">对话框属性 [3] 需要一个至少包含 [2] 个字段的记录。</String>
   <String Id="IDS_ERROR_2825">控件属性 [3] 需要一个至少包含 [2] 个字段的记录。</String>
   <String Id="IDS_ERROR_2826">对话框 [2] 上的控件 [3] 超出对话框 [4] 的边界 [5] 个像素。</String>
   <String Id="IDS_ERROR_2827">对话框 [2] 上的单选按钮组 [3] 的按钮 [4] 超出组 [5] 的边界 [6] 个像素。</String>
   <String Id="IDS_ERROR_2828">尝试从对话框 [2] 中移除控件 [3]，但是该控件不属于该对话框。</String>
   <String Id="IDS_ERROR_2829">尝试使用未初始化的对话框。</String>
   <String Id="IDS_ERROR_2830">尝试使用对话框 [2] 上未初始化的控件。</String>
   <String Id="IDS_ERROR_2831">对话框 [2] 上的控件 [3] 不支持 [5] 属性 [4]。</String>
   <String Id="IDS_ERROR_2832">对话框 [2] 不支持属性 [3]。</String>
   <String Id="IDS_ERROR_2833">对话框 [3] 上的控件 [4] 忽略了消息 [2]。</String>
   <String Id="IDS_ERROR_2834">对话框 [2] 上的后续指针不构成单循环。</String>
   <String Id="IDS_ERROR_2835">在对话框 [3] 上找不到控件 [2]。</String>
   <String Id="IDS_ERROR_2836">对话框 [2] 上的控件 [3] 无法获得焦点。</String>
   <String Id="IDS_ERROR_2837">对话框 [2] 上的控件 [3] 需要 winproc 返回 [4]。</String>
   <String Id="IDS_ERROR_2838">选择表中的项 [2] 将其自身作为父项。</String>
   <String Id="IDS_ERROR_2839">设置属性 [2] 失败。</String>
   <String Id="IDS_ERROR_2840">错误对话框名称不匹配。</String>
   <String Id="IDS_ERROR_2841">在错误对话框上未找到“确定”按钮。</String>
   <String Id="IDS_ERROR_2842">在错误对话框上未找到文本字段。</String>
   <String Id="IDS_ERROR_2843">标准对话框不支持 ErrorString 属性。</String>
   <String Id="IDS_ERROR_2844">如果未设置 Errorstring，则无法执行错误对话框。</String>
   <String Id="IDS_ERROR_2845">按钮的总宽度超出错误对话框的大小。</String>
   <String Id="IDS_ERROR_2846">SetFocus 在错误对话框上未找到所需控件。</String>
   <String Id="IDS_ERROR_2847">对话框 [2] 上的控件 [3] 同时设置了图标和位图样式。</String>
   <String Id="IDS_ERROR_2848">尝试将控件 [3] 设置为对话框 [2] 上的默认按钮，但该控件不存在。</String>
   <String Id="IDS_ERROR_2849">对话框 [2] 上的控件 [3] 属于不能为整数值的类型。</String>
   <String Id="IDS_ERROR_2850">未识别的卷类型。</String>
   <String Id="IDS_ERROR_2851">图标 [2] 的数据无效。</String>
   <String Id="IDS_ERROR_2852">使用对话框 [2] 之前，必须至少向其添加一个控件。</String>
   <String Id="IDS_ERROR_2853">对话框 [2] 为无模式对话框。不应在其上调用 execute 方法。</String>
   <String Id="IDS_ERROR_2854">在对话框 [2] 上，控件 [3] 被指定为第一个活动控件，但是没有这样的控件。</String>
   <String Id="IDS_ERROR_2855">对话框 [2] 上的单选按钮组 [3] 的按钮数少于 2。</String>
   <String Id="IDS_ERROR_2856">正在创建对话框 [2] 的另一个副本。</String>
   <String Id="IDS_ERROR_2857">在选择表中提到了目录 [2]，但找不到该目录。</String>
   <String Id="IDS_ERROR_2858">位图 [2] 的数据无效。</String>
   <String Id="IDS_ERROR_2859">测试错误消息。</String>
   <String Id="IDS_ERROR_2860">对话框 [2] 上未正确定义“取消”按钮。</String>
   <String Id="IDS_ERROR_2861">在对话框 [2] 上，控件 [3] 的单选按钮的后续指针未构成循环。</String>
   <String Id="IDS_ERROR_2862">对话框 [2] 上控件 [3] 的属性未定义有效的图标大小。正在将该大小设置为 16。</String>
   <String Id="IDS_ERROR_2863">对话框 [2] 上的控件 [3] 需要的图标 [4] 的大小为 [5]x[5]，但该大小不存在。正在加载第一个可用大小。</String>
   <String Id="IDS_ERROR_2864">对话框 [2] 上的控件 [3] 接收了一个浏览事件，但没有适用于当前所选内容的可配置目录。可能的原因: 浏览按钮编写不正确。</String>
   <String Id="IDS_ERROR_2865">布告栏 [2] 上的控件 [3] 超出布告栏 [4] 的边界 [5] 个像素。</String>
   <String Id="IDS_ERROR_2866">不允许对话框 [2] 返回参数 [3]。</String>
   <String Id="IDS_ERROR_2867">未设置错误对话框属性。</String>
   <String Id="IDS_ERROR_2868">错误对话框 [2] 未设置错误样式位。</String>
   <String Id="IDS_ERROR_2869">对话框 [2] 设置了错误样式位，但它不是错误对话框。</String>
   <String Id="IDS_ERROR_2870">对话框 [2] 上控件 [3] 的帮助字符串 [4] 不包含分隔符。</String>
   <String Id="IDS_ERROR_2871">[2] 表过期: [3]。</String>
   <String Id="IDS_ERROR_2872">对话框 [2] 上 CheckPath 控件事件的参数无效。</String>
   <String Id="IDS_ERROR_2873">在对话框 [2] 上，控件 [3] 具有无效的字符串长度限制: [4]。</String>
   <String Id="IDS_ERROR_2874">将文本字体更改为 [2] 失败。</String>
   <String Id="IDS_ERROR_2875">将文本颜色更改为 [2] 失败。</String>
   <String Id="IDS_ERROR_2876">对话框 [2] 上的控件 [3] 必须截断字符串: [4]。</String>
   <String Id="IDS_ERROR_2877">未找到二进制数据 [2]</String>
   <String Id="IDS_ERROR_2878">在对话框 [2] 上，控件 [3] 具有可能值: [4]。这是一个无效值或重复值。</String>
   <String Id="IDS_ERROR_2879">对话框 [2] 上的控件 [3] 无法解析掩码字符串: [4]。</String>
   <String Id="IDS_ERROR_2880">不执行剩余的控件事件。</String>
   <String Id="IDS_ERROR_2881">CMsiHandler 初始化失败。</String>
   <String Id="IDS_ERROR_2882">对话框窗口类注册失败。</String>
   <String Id="IDS_ERROR_2883">对话框 [2] 的 CreateNewDialog 失败。</String>
   <String Id="IDS_ERROR_2884">为对话框 [2] 创建窗口失败。</String>
   <String Id="IDS_ERROR_2885">在对话框 [2] 上创建控件 [3] 失败。</String>
   <String Id="IDS_ERROR_2886">创建 [2] 表失败。</String>
   <String Id="IDS_ERROR_2887">为 [2] 表创建光标失败。</String>
   <String Id="IDS_ERROR_2888">执行 [2] 视图失败。</String>
   <String Id="IDS_ERROR_2889">为对话框 [2] 上的控件 [3] 创建窗口失败。</String>
   <String Id="IDS_ERROR_2890">处理程序未能创建已初始化的对话框。</String>
   <String Id="IDS_ERROR_2891">未能删除对话框 [2] 的窗口。</String>
   <String Id="IDS_ERROR_2892">控件 [2] 只能使用整数，[3] 不是有效的整数值。</String>
   <String Id="IDS_ERROR_2893">对话框 [2] 上的控件 [3] 可接受最长 [5] 个字符的属性值。值 [4] 超出了此限制，因而已被截断。</String>
   <String Id="IDS_ERROR_2894">加载 RICHED20.DLL 失败。GetLastError() 返回: [2]。</String>
   <String Id="IDS_ERROR_2895">释放 RICHED20.DLL 失败。GetLastError() 返回: [2]。</String>
   <String Id="IDS_ERROR_2896">执行操作 [2] 失败。</String>
   <String Id="IDS_ERROR_2897">未能在此系统上创建任何 [2] 字体。</String>
   <String Id="IDS_ERROR_2898">对于 [2] 文本样式，系统在 [4] 字符集中创建了“[3]”字体。</String>
   <String Id="IDS_ERROR_2899">未能创建 [2] 文本样式。GetLastError() 返回: [3]。</String>
   <String Id="IDS_ERROR_2901">操作 [2] 的参数无效: 参数 [3]。</String>
   <String Id="IDS_ERROR_2902">未按顺序调用操作 [2]。</String>
   <String Id="IDS_ERROR_2903">缺少文件 [2]。</String>
   <String Id="IDS_ERROR_2904">无法对文件 [2] 执行 BindImage。</String>
   <String Id="IDS_ERROR_2905">无法从脚本文件 [2] 中读取记录。</String>
   <String Id="IDS_ERROR_2906">脚本文件 [2] 中缺少标头。</String>
   <String Id="IDS_ERROR_2907">无法创建安全的安全描述符。错误: [2]。</String>
   <String Id="IDS_ERROR_2908">无法注册组件 [2]。</String>
   <String Id="IDS_ERROR_2909">无法取消注册组件 [2]。</String>
   <String Id="IDS_ERROR_2910">无法确定用户的安全 ID。</String>
   <String Id="IDS_ERROR_2911">无法移除文件夹 [2]。</String>
   <String Id="IDS_ERROR_2912">无法调度文件 [2] 在重新启动时移除。</String>
   <String Id="IDS_ERROR_2919">没有为压缩文件指定 CAB 文件: [2]。</String>
   <String Id="IDS_ERROR_2920">没有为文件 [2] 指定源目录。</String>
   <String Id="IDS_ERROR_2924">不支持脚本 [2] 版本。脚本版本: [3]，最低版本: [4]，最高版本: [5]。</String>
   <String Id="IDS_ERROR_2927">ShellFolder ID [2] 无效。</String>
   <String Id="IDS_ERROR_2928">超出源的最大数量。正在跳过源“[2]”。</String>
   <String Id="IDS_ERROR_2929">无法确定发布根。错误: [2]。</String>
   <String Id="IDS_ERROR_2932">无法从脚本数据创建文件 [2]。错误: [3]。</String>
   <String Id="IDS_ERROR_2933">无法初始化回滚脚本 [2]。</String>
   <String Id="IDS_ERROR_2934">无法保护转换 [2]。错误 [3]。</String>
   <String Id="IDS_ERROR_2935">无法取消保护转换 [2]。错误 [3]。</String>
   <String Id="IDS_ERROR_2936">无法找到转换 [2]。</String>
   <String Id="IDS_ERROR_2937">Windows Installer 无法安装系统文件保护目录。目录: [2]，错误: [3]。</String>
   <String Id="IDS_ERROR_2938">Windows Installer 无法从缓存中检索系统文件保护目录。目录: [2]，错误: [3]。</String>
   <String Id="IDS_ERROR_2939">Windows Installer 无法从缓存中删除系统文件保护目录。目录: [2]，错误: [3]。</String>
   <String Id="IDS_ERROR_2940">未对源解析提供目录管理器。</String>
   <String Id="IDS_ERROR_2941">无法计算文件 [2] 的 CRC。</String>
   <String Id="IDS_ERROR_2942">尚未对 [2] 文件执行 BindImage 操作。</String>
   <String Id="IDS_ERROR_2943">此版本的 Windows 不支持部署 64 位软件包。脚本 [2] 适用于 64 位软件包。</String>
   <String Id="IDS_ERROR_2944">GetProductAssignmentType 失败。</String>
   <String Id="IDS_ERROR_2945">安装 ComPlus App [2] 失败，返回错误 [3]。</String>
   <String Id="IDS_ERROR_3001">此列表中的修补程序包含不正确的排序信息: [2][3][4][5][6][7][8][9][10][11][12][13][14][15][16]。</String>
   <String Id="IDS_ERROR_3002">修补程序 [2] 包含无效的排序信息。 </String>
   <String Id="IDS_ERROR_25032">安装程序未能安装 LSI 驱动程序。</String>
   <String Id="IDS_ERROR_25520">未能为 [3]\[4] 创建安全描述符，系统错误: [2]</String>
   <String Id="IDS_ERROR_25521">未能在对象 [3] 上设置安全描述符，系统错误: [2]</String>
   <String Id="IDS_ERROR_25522">未知的对象类型 [3]，系统错误: [2]</String>
   <String Id="IDS_ERROR_27500">此安装程序需要 Internet Information Server 4.0 或更高版本才能配置 IIS 虚拟根目录。请确保安装了 IIS 4.0 或更高版本。</String>
   <String Id="IDS_ERROR_27501">此安装程序需要管理员特权来配置 IIS 虚拟根目录。</String>
   <String Id="IDS_ERROR_27502">无法连接到 [2]“[3]”。[4]</String>
   <String Id="IDS_ERROR_27503">从 [2]“[3]”中检索版本字符串时发生错误。[4]</String>
   <String Id="IDS_ERROR_27504">不满足 SQL 版本要求: [3]。此安装需要 [2] [4] 或更高版本。</String>
   <String Id="IDS_ERROR_27505">无法打开 SQL 脚本文件 [2]。</String>
   <String Id="IDS_ERROR_27506">执行 SQL 脚本 [2] 时发生错误。行 [3]。[4]</String>
   <String Id="IDS_ERROR_27507">连接或浏览数据库服务器需要安装 MDAC。</String>
   <String Id="IDS_ERROR_27508">安装 COM+ 应用程序 [2] 时发生错误。[3]</String>
   <String Id="IDS_ERROR_27509">卸载 COM+ 应用程序 [2] 时发生错误。[3]</String>
   <String Id="IDS_ERROR_27510">安装 COM+ 应用程序 [2] 时发生错误。无法加载 Microsoft(R) .NET 类库。注册 .NET 服务组件需要安装 Microsoft(R) .NET Framework。</String>
   <String Id="IDS_ERROR_27511">无法执行 SQL 脚本文件 [2]。连接未打开: [3]</String>
   <String Id="IDS_ERROR_27512">开始 [2]“[3]”的事务时发生错误。数据库 [4]。[5]</String>
   <String Id="IDS_ERROR_27513">提交 [2]“[3]”的事务时发生错误。数据库 [4]。[5]</String>
   <String Id="IDS_ERROR_27514">此安装需要 Microsoft SQL Server。指定的服务器“[3]”是 Microsoft SQL Server Desktop Engine (MSDE)。</String>
   <String Id="IDS_ERROR_27515">从 [2]“[3]”中检索模式版本时发生错误。数据库: “[4]”。[5]</String>
   <String Id="IDS_ERROR_27516">向 [2]“[3]”中写入模式版本时发生错误。数据库: “[4]”。[5]</String>
   <String Id="IDS_ERROR_27517">此安装需要管理员特权才能安装 COM+ 应用程序。请以管理员身份登录，然后重新尝试此安装。</String>
   <String Id="IDS_ERROR_27518">COM+ 应用程序“[2]”被配置作为 NT 服务运行；这需要在系统中安装 COM+ 1.5 或更高版本。您的系统中安装了 COM+ 1.0，因此不会安装该应用程序。</String>
   <String Id="IDS_ERROR_27519">更新 XML 文件 [2] 时发生错误。[3]</String>
   <String Id="IDS_ERROR_27520">打开 XML 文件 [2] 时发生错误。[3]</String>
   <String Id="IDS_ERROR_27521">此安装程序需要 MSXML 3.0 或更高版本才能配置 XML 文件。请确保您已安装 3.0 版或更高版本。</String>
   <String Id="IDS_ERROR_27522">创建 XML 文件 [2] 时发生错误。[3]</String>
   <String Id="IDS_ERROR_27523">加载服务器时发生错误。</String>
   <String Id="IDS_ERROR_27524">加载 NetApi32.DLL 时发生错误。ISNetApi.dll 需要正确加载 NetApi32.DLL，并且需要基于 NT 的操作系统。</String>
   <String Id="IDS_ERROR_27525">未找到服务器。请确认指定的服务器存在。服务器名称不能为空。</String>
   <String Id="IDS_ERROR_27526">ISNetApi.dll 中未指定的错误。</String>
   <String Id="IDS_ERROR_27527">缓冲区太小。</String>
   <String Id="IDS_ERROR_27528">访问被拒绝。请检查管理权限。</String>
   <String Id="IDS_ERROR_27529">计算机无效。</String>
   <String Id="IDS_ERROR_27530">未定义的 switch case。</String>
   <String Id="IDS_ERROR_27531">未处理的异常。</String>
   <String Id="IDS_ERROR_27532">用户名对于此服务器或域无效。</String>
   <String Id="IDS_ERROR_27533">区分大小写的密码不匹配。</String>
   <String Id="IDS_ERROR_27534">列表为空。</String>
   <String Id="IDS_ERROR_27535">访问冲突。</String>
   <String Id="IDS_ERROR_27536">获取组时发生错误。</String>
   <String Id="IDS_ERROR_27537">将用户添加到组中时发生错误。请确认该组对于此域或服务器存在。</String>
   <String Id="IDS_ERROR_27538">创建用户时发生错误。</String>
   <String Id="IDS_ERROR_27539">从 NetAPI 返回的 ERROR_NETAPI_ERROR_NOT_PRIMARY。</String>
   <String Id="IDS_ERROR_27540">指定的用户已存在。</String>
   <String Id="IDS_ERROR_27541">指定的组已存在。</String>
   <String Id="IDS_ERROR_27542">密码无效。请确认该密码符合网络密码策略。</String>
   <String Id="IDS_ERROR_27543">名称无效。</String>
   <String Id="IDS_ERROR_27544">组无效。</String>
   <String Id="IDS_ERROR_27545">用户名不能为空，并且格式必须是域\用户名。</String>
   <String Id="IDS_ERROR_27546">在用户临时目录中加载或创建 INI 文件时发生错误。</String>
   <String Id="IDS_ERROR_27547">未加载 ISNetAPI.dll 或加载该 dll 时出错。执行此操作时需要加载该 dll。请确认该 dll 在 SUPPORTDIR 目录中。</String>
   <String Id="IDS_ERROR_27548">从用户临时目录中删除包含新用户信息的 INI 文件时发生错误。</String>
   <String Id="IDS_ERROR_27549">在获取主域控制器 (PDC) 时发生错误。</String>
   <String Id="IDS_ERROR_27550">每个字段都必须有值才能创建一个用户。</String>
   <String Id="IDS_ERROR_27551">未找到 [2] 的 ODBC 驱动程序。这是连接到 [2] 数据库服务器所必需的。</String>
   <String Id="IDS_ERROR_27552">创建数据库 [4] 时发生错误。服务器: [2] [3]。[5]</String>
   <String Id="IDS_ERROR_27553">连接到数据库 [4] 时发生错误。服务器: [2] [3]。[5]</String>
   <String Id="IDS_ERROR_27554">尝试打开连接 [2] 时发生错误。没有与此连接相关的有效的数据库元数据。</String>
   <String Id="IDS_ERROR_28030">安装程序未成功安装 USB 驱动程序。为确保安装成功，请重新启动您的计算机并运行此安装程序。</String>
   <String Id="IDS_ERROR_28033">无法连接到 %s。TCP 端口 %d 上出现 TCP 连接故障。请检查输入的服务器名。</String>
   <String Id="IDS_ERROR_28034">无法连接到 %s 的 LDAP。请检查输入的服务器名。</String>
   <String Id="IDS_ERROR_28035">凭据无效。请输入指定服务器的管理员用户名和密码。</String>
   <String Id="IDS_ERROR_28036">未成功绑定到 %s 的 LDAP。%s。</String>
   <String Id="IDS_ERROR_28037">未成功更新 %s 的 LDAP。访问被拒绝。请输入在指定服务器上拥有足够权限的管理员用户名和密码。</String>
   <String Id="IDS_ERROR_28038">未成功更新 %s 的 LDAP。模式冲突。指定的服务器可能正在运行旧版软件，需要通过升级来支持此代理安装。</String>
   <String Id="IDS_ERROR_28039">未成功更新 %s 的 LDAP。%s。</String>
   <String Id="IDS_ERROR_28040">无效的用户名格式。</String>
   <String Id="IDS_ERROR_28041">此代理已经在 %s 中进行了注册。是否要继续进行此项安装并更新现有的注册信息?</String>
   <String Id="IDS_ERROR_28042">此代理在 %s 中注册了很多次。请使用 Horizon Administrator 删除该计算机的对应项目，然后重新尝试此安装。</String>
   <String Id="IDS_ERROR_28045">如果要指定管理员凭据，您必须输入一个用户名和密码。</String>
   <String Id="IDS_ERROR_28046">未能成功地从 %s 的 LDAP 中获取关键的安全信息。指定的服务器可能正在运行旧版软件，需要通过升级来支持此代理安装。</String>
   <String Id="IDS_ERROR_28053">DLL 注册失败。如需了解详细信息，请查看最新的 %TEMP%\vminst*.log 文件。</String>
   <String Id="IDS_ERROR_28060">安装 Intel HECI 设备驱动程序时出错。</String>
   <String Id="IDS_ERROR_28062">此计算机的某个代理已在 %s 注册为虚拟机。请更改此计算机的计算机名，或通过 Horizon Administrator 从连接服务器上移除此虚拟机条目，然后重新尝试此安装。</String>
   <String Id="IDS_ERROR_28065">安装程序未能安装 Smartcard Redirector 驱动程序。</String>
   <String Id="IDS_ERROR_28089">连接服务器 %s 未运行足够的软件版本，无法完全支持此代理。您应先升级连接服务器，然后再继续。如果仍继续，则需要稍后重新安装此代理才能获取完整的 RDS 主机功能。是否要继续安装?</String>
   <String Id="IDS_ERROR_28090">设置检测到非默认的注册表值:

HKLM\SYSTEM\CurrentControlSet\Control\TerminalServer\WinStations\RDP-Tcp\MaxInstanceCount

HKLM\SYSTEM\CurrentControlSet\Control\TerminalServer\WinStations\Horizon-PCOIP\MaxInstanceCount

由于 Horizon 管理会话限制，因此这些注册表设置将导致意外行为。</String>
   <String Id="IDS_ERROR_28092">安装程序未成功安装 Horizon Virtual Webcam 驱动程序。为确保安装成功，请重新启动您的计算机并运行此安装程序。</String>
   <String Id="IDS_ERROR_28096">有关安全使用 USB 重定向的指导，请参阅《Horizon 安全指南》文档。</String>
   <String Id="IDS_ERROR_28100">您正在某些安装程序检查已禁用的情况下安装 Omnissa Horizon Agent。不建议这样做，这样将导致安装不受支持，请取消并在启用这些检查后重新运行安装程序。</String>
   <String Id="IDS_ERROR_28109">计算机上未安装 VC%d.%d nonredist。如果继续，Horizon Agent 安装可能会失败。您可以从 msvc_debug_runtime_installer Conan 内部版本下载 VC%d.%d nonredist 安装程序。</String>
   <String Id="IDS_ERROR_28110">适用于 Skype for Business 的 Virtualization Pack 要求具备 .NET Framework 4.0 或更高版本，然后才能安装此功能。请安装 .NET Framework，然后重新启动安装程序以安装此功能。</String>
   <String Id="IDS_ERROR_28111">请添加或移除一项功能以继续。</String>
   <String Id="IDS_ERROR_28113">在修改安装过程中，无法更改“Instant Clone Agent”(NGVC) 功能。如果要添加或移除克隆功能，请卸载并重新安装代理。</String>
   <String Id="IDS_ERROR_28114">您必须具有管理特权才能修改或修复此安装。您也可以从权限提升的命令提示符中运行以下命令:

msiexec.exe /i [DATABASE]</String>
   <String Id="IDS_ERROR_28115">您必须具有管理特权才能安装此修补程序。您可能需要从权限提升的命令提示符中运行以下命令:

msiexec.exe /p [PATCH]</String>

   <String Id="IDS_ERROR_28116">Print Spooler 服务未运行。可能未安装 Horizon Integrated Printing 功能。</String>

   <!-- L10n properties for merge module services -->
   <String Id="IDS_PCOIPSG_DISPLAY_NAME">Omnissa Horizon PCoIP 安全网关</String>
   <String Id="IDS_PCOIPSG_DESCRIPTION">提供 PCoIP 网关服务。</String>

   <String Id="IDS_WSNM_SERVICE_DISPLAY_NAME">Omnissa Horizon Agent</String>
   <String Id="WsnmServiceDescription">提供 Horizon Agent 服务。</String>

   <String Id="IDS_WSSH_SERVICE_DISPLAY_NAME">Omnissa Horizon 脚本主机</String>
   <String Id="IDS_WSSH_SERVICE_DESCRIPTION">提供脚本主机服务。</String>

   <String Id="IDS_VMLM_SERVICE_DISPLAY_NAME">Omnissa Horizon 登录监控器</String>
   <String Id="IDS_VMLM_SERVICE_DESCRIPTION">提供登录监控服务。</String>

   <String Id="IDS_HZMON_SERVICE_DISPLAY_NAME">Omnissa Horizon Monitoring Service</String>
   <String Id="IDS_HZMON_SERVICE_DESCRIPTION">提供监控服务。</String>

   <String Id="IDS_VMWRXG_SERVICE_DISPLAY_NAME">Omnissa Horizon 远程体验服务</String>
   <String Id="IDS_VMWRXG_SERVICE_DESCRIPTION">提供远程体验通用服务。</String>

   <String Id="IDS_AUTORESTART">成功完成时自动重新启动系统</String>

   <String Id="IDS_AUTORESTART_MODIFY">如有必要，在成功完成后自动重新启动系统</String>

</WixLocalization>
