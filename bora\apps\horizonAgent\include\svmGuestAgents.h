/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/* svmGuestAgents.h
 *
 *    This file contains all data structures, #defines, and error codes shared
 *    by the native and service guest agents. Since this header file is
 *    included by the native agent no reference to the WIN32 platform should
 *    be included here.
 */

#pragma once

#include "horizonPaths.h"

#include "vm_basic_types.h"

#define MALLOC(x) HeapAlloc(GetProcessHeap(), 0, (x))
#define FREE(x) HeapFree(GetProcessHeap(), 0, (x))

#define FlagOn(_F, _SF) ((_F) & (_SF))
#define SetFlag(_F, _SF) ((_F) |= (_SF))
#define ClearFlag(_F, _SF) ((_F) &= ~(_SF))

/* The registry key root for windows services */
#define SVM_WIN_SRV_ROOT_REGKEY L"SYSTEM\\CurrentControlSet\\Services"

#define NETLOGON_PARAMETERS_KEYPATH L"SYSTEM\\CurrentControlSet\\Services\\Netlogon\\Parameters"

#define SYSPREP_STATE_KEYPATH L"SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Setup\\State"

#define SVM_NGA_CACHED_MACHINE_NAMES_REGKEY                                                        \
   L"System\\CurrentControlSet\\Control\\Lsa\\CachedMachineNames"

/* The registry root key for storing customization persistent info for native
 * agent */
#define SVM_WIN_CUSTOM_PERSIST_NGA_REGKEY L"nga"

/* The registry root key for storing customization persistent info for guest
 * agent*/
#define SVM_WIN_CUSTOM_PERSIST_GA_REGKEY L"ga"

#define HIC_VMFORKED_VALUE_NAME L"VmForked"

/* Native agent logger name */
#define SVM_NGA_LOGGER_NAME L"vmware-viewcomposer-nga.log"

/* Usermode svm service name */
#define SVM_GA_SRV_NAME L"vmware-viewcomposer-ga"

/* Profile List Key */
#define PROFILE_LIST_KEY L"SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\ProfileList"

/* Setup command line interception key */
#define SYSTEM_SETUP_KEY L"SYSTEM\\Setup"

//
// Horizon Instant Clones Agent Keys
//
#define HIC_COMPANY_NAME L"Omnissa"
#define HIC_SUITE_NAME L"Horizon"
#define HIC_PRODUCT_NAME L"Instant Clone Agent"
#define HIC_AGENT_NAME L"Agent"
#define HIC_GUEST_INFO_REG_KEY L"guestInfo"
#define HIC_SETUP_REG_KEY L"setup"
#define HIC_GA_REG_KEY L"ga"
#define HIC_NGA_REG_KEY L"nga"
#define HIC_SUPPORT_REG_KEY L"Support"
#define HIC_EXTERNAL_REG_KEY L"External"
#define HIC_AGENT_INTEGRATION_REG_KEY L"AgentIntegration"
#define HIC_AV_REGKEY L"AV"
#define HIC_DAAS_REGKEY L"DAAS"
#define HIC_GA_SERVICE_NAME L"omn-instantclone-ga"
#define HIC_AGENT_CONFIGURATION L"Configuration"
#define HIC_HORIZON_AGENT_KEY L"HorizonAgent"

#define HIC_KEY_PATH L"SOFTWARE\\" HIC_COMPANY_NAME L"\\" HIC_SUITE_NAME L"\\" HIC_PRODUCT_NAME
#define HIC_GA_KEY_PATH HIC_KEY_PATH L"\\" HIC_GA_REG_KEY
#define HIC_NGA_KEY_PATH HIC_KEY_PATH L"\\" HIC_NGA_REG_KEY
#define HIC_GUEST_INFO_KEY_PATH HIC_KEY_PATH L"\\" HIC_GUEST_INFO_REG_KEY
#define HIC_SETUP_KEY_PATH HIC_KEY_PATH L"\\" HIC_SETUP_REG_KEY

#define HIC_SUPPORT_KEY_PATH HIC_KEY_PATH L"\\" HIC_SUPPORT_REG_KEY
#define HIC_EXTERNAL_KEY_PATH HIC_KEY_PATH L"\\" HIC_EXTERNAL_REG_KEY

#define HIC_AGENT_INTEGRATION_KEY_PATH HIC_KEY_PATH L"\\" HIC_AGENT_INTEGRATION_REG_KEY
#define HIC_AGENT_INTEGRATION_AV_KEY_PATH HIC_AGENT_INTEGRATION_KEY_PATH L"\\" HIC_AV_REGKEY
#define HIC_AGENT_INTEGRATION_DAAS_KEY_PATH HIC_AGENT_INTEGRATION_KEY_PATH L"\\" HIC_DAAS_REGKEY

#define HIC_GA_SERVICE_KEYPATH L"SYSTEM\\CurrentControlSet\\services\\" HIC_GA_SERVICE_NAME
#define HIC_AGENT_CONFIG                                                                           \
   L"SOFTWARE"                                                                                     \
   L"\\" HIC_COMPANY_NAME L"\\" HIC_SUITE_NAME L"\\" HIC_AGENT_NAME L"\\" HIC_AGENT_CONFIGURATION
#define HIC_HORIZON_AGENT_KEY_PATH HIC_KEY_PATH L"\\" HIC_HORIZON_AGENT_KEY

//
// View Agent - Node Manager
//
#define SVM_NODE_MANAGER_REGKEY HORIZON_VDM_NODE_MANAGER_REG_ROOT_W

//
// Native agent log file name
//
#define HIC_NGA_LOG_FILE_PATH                                                                      \
   L"\\??\\C:\\ProgramData\\" HIC_COMPANY_NAME L"\\" HIC_SUITE_NAME L"\\logs\\omnissa-ic-nga.log"

//
// Boot Execute Verification
//
#define SVM_BOOTEXECUTE_VALUE _T("System32\\omnissa-ic-nga.exe")
#define SVM_BOOTEXECUTE_VALUE_NAME _T("BootExecute")
#define SVM_SESSION_MANAGER_KEYPATH L"SYSTEM\\CurrentControlSet\\Control\\Session Manager"

// SVI Use Verification
#define SVM_USE_SVI_VALUE_NAME _T("Use SVI")

/*
 * The maximum output, in bytes, we can accept from either the pre or post
 * sync scripts.
 */
#define SVM_SCRIPTS_MAX_OUTPUT 4096

/* Registry Hive types */
typedef enum { SVM_HT_SYSTEM = 0, SVM_HT_SOFTWARE, SVM_HT_SECURITY, SVM_HT_NUM_HIVES } SvmHiveType;

/* Default hives' names */
static const wchar_t *defaultHivesNames[SVM_HT_NUM_HIVES] = {L"SYSTEM", L"SOFTWARE", L"SECURITY"};

/* Registry Entry operations */
typedef enum {
   SVM_REO_SAVE_RESTORE = 0,
   SVM_REO_SAVE_RESTORE_UPPERCASE,
   SVM_REO_DELETEONLY
} SvmRegEntryOp;

/*
 * Registry entry structure.
 * hive: which hive
 * keyPath: hive-relative path to the key
 * value: name of value NULL if entry is entire key
 * type: expected type of value if entry is a value
 * fileName: file name to use when saving this key entry. Can be NULL.
 * operation:  What operation to perform with the registry entry.
 */
typedef struct _SvmRegEntry {
   SvmHiveType hive;
   wchar_t *keyPath;
   wchar_t *value;
   uint32 type;
   wchar_t *fileName;
   SvmRegEntryOp regOp;
} SvmRegEntry;

/* the HKEY_LOCAL_MACHINE string for the native app */
static const wchar_t hklmStr[] = L"HKLM";

/*
 * The following are a list of registry entries that we change in order to set
 * the computer name of a new clone.
 * There is currently one known issue with the way we set the computer name
 * (see bug 174858). We also are aware of several registry values that we
 * decided not to change since they do not change when the computer name is
 * changed manually:
 *    - HKLM\Software\Microsoft\SchedulingAgent
 *    - HKU\.DEFAULT\Software\Microsoft\Windows Media\WMSD\General
 *    - HKU\S-1-5-19\Software\Microsoft\Windows\ShellNoRoam
 *    - HKU\S-1-5-20\Software\Microsoft\Windows\ShellNoRoam
 */
static const SvmRegEntry SvmComputerNameRegEntries[] = {
   {SVM_HT_SYSTEM, L"CurrentControlSet\\Control\\ComputerName\\ComputerName", L"ComputerName",
    REG_SZ, NULL, SVM_REO_SAVE_RESTORE_UPPERCASE},
   {SVM_HT_SYSTEM, L"CurrentControlSet\\Control\\ComputerName\\ActiveComputerName", L"ComputerName",
    REG_SZ, NULL, SVM_REO_SAVE_RESTORE_UPPERCASE},
   {SVM_HT_SYSTEM, L"CurrentControlSet\\Services\\TcpIp\\Parameters", L"NV Hostname", REG_SZ, NULL,
    SVM_REO_SAVE_RESTORE},
   {SVM_HT_SYSTEM, L"CurrentControlSet\\Services\\TcpIp\\Parameters", L"Hostname", REG_SZ, NULL,
    SVM_REO_SAVE_RESTORE},
   {SVM_HT_SYSTEM, L"CurrentControlSet\\Services\\EventLog", L"ComputerName", REG_SZ, NULL,
    SVM_REO_SAVE_RESTORE_UPPERCASE}};

#ifndef HKEY_LOCAL_MACHINE
#   define HKEY_LOCAL_MACHINE ((HANDLE)(ULONG_PTR)((LONG)0x80000002))
#endif

#define MAX_HOSTNAME_LENGTH 64
#define ALLOWED_HOSTNAME_CHARACTERS                                                                \
   "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-"
#define DIGITS "0123456789"

#define CUSTOMIZATION_STATE_PENDING "pending"
#define CUSTOMIZATION_STATE_RUNNING "running"
#define CUSTOMIZATION_STATE_SUCCESS "success"
#define CUSTOMIZATION_STATE_ERROR "error"
#define CUSTOMIZATION_STATE_BEGIN "CustomizationBegin"
#define CUSTOMIZATION_STATE_SPECIALIZE "specialize"
#define CUSTOMIZATION_STATE_OOBE "oobe"
#define CUSTOMIZATION_STATE_SNAPSHOTTING "snapshotting"
#define CUSTOMIZATION_STATE_POST_SNAPSHOTTING "postSnapshotting"

#define CUSTOMIZATION_FLAG_NONE 0x0000000
#define CUSTOMIZATION_FLAG_SHUTDOWN_REQUIRED 0x0000001
#define CUSTOMIZATION_FLAG_SHUTDOWN_REQUESTED 0x0000002
#define CUSTOMIZATION_FLAG_SHUTDOWN_COMPLETED 0x0000004
#define CUSTOMIZATION_FLAG_REBOOT_REQUIRED 0x0000008
#define CUSTOMIZATION_FLAG_REBOOT_REQUESTED 0x0000010
#define CUSTOMIZATION_FLAG_REBOOT_COMPLETED 0x0000020
#define CUSTOMIZATION_FLAG_RESYNC 0x00000040
#define CUSTOMIZATION_FLAG_SYSPREP 0x00000080
#define CUSTOMIZATION_FLAG_CLONEPREP 0x00000100
#define CUSTOMIZATION_FLAG_FAST_REFRESH 0x00000200
#define CUSTOMIZATION_FLAG_FAST_REFRESH_COMPLETED 0x00000400
#define CUSTOMIZATION_FLAG_SYSPREP_DOMAIN_JOIN 0x00000800
#define CUSTOMIZATION_FLAG_RESYNC_CLEANUP 0x00001000
#define CUSTOMIZATION_FLAG_PERSISTENT_DISKS_ENABLED 0x00002000
#define CUSTOMIZATION_FLAG_UNIVERSALPREP 0x00004000

#define INIT_CLONE_CUSTOMIZATION_TIME_CMD                                                          \
   "info-set guestinfo.support.clone.TotalCustomizationTime 0"

#define INIT_CLONE_SYSPREP_TIME_CMD "info-set guestinfo.support.clone.TotalSysprepTime 0"

#define GET_CUSTOMIZATION_FLAGS_CMD "info-get guestinfo.AgentCustomizationFlags"

#define DEFAULT_NETLOGON_FLAGS_STRING _T("0x2fffffff")
#define DEFAULT_NETLOGON_FLAGS_DWORD 0x2FFFFFFF

#define ONE_SECOND 1000
#define HALF_MINUTE 30000
#define ONE_MINUTE 60000
#define TWO_MINUTES 120000
#define FIVE_MINUTES 300000

#if ENABLE_BASE_APP_MERGE
#   define AV_BASE_MERGE_VALUE_NAME _T("BaseMergeStatus")
#   define DEFAULT_AV_BASE_MERGE_WAIT_TIME 3600000
#   define AV_BASE_MERGE_NONE 0
#   define AV_BASE_MERGE_RUNNING 1
#   define AV_BASE_MERGE_COMPLETE 2
#endif

//
// Values defined in InstantClonesGuestAgent.h
//

/*
 * This registry value is located under NGVC_GA_KEYPATH.
 * It holds a status value that Instant Clones agent notifies View agent.
 * The possible values are defined in enum NotifyVdmStatusValue.
 */
#define NGA_NOTIFY_VDM_STATUS_REGVAL L"NotifyVdmStatusValue"
#define VDM_STATUS_VAL_WAIT_FOR_CHECKPOINTING 2
#define VDM_STATUS_VAL_CUSTOMIZATION_POST_CHECKPOINTING 3
