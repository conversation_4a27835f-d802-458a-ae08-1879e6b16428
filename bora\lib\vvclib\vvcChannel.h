/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * vvcChannel.h
 *
 * VVC channel interface implementation.
 *
 */

#ifndef _VVCCHANNEL_H
#define _VVCCHANNEL_H

#include "userlock.h"

#include "vvclibInt.h"

#define VVC_RAW_CHANNEL(channel) (channel->flags & VVC_CHANNEL_RAW)
#define VVC_INPROC_CHANNEL(channel) (channel->listener->pluginId == VVC_PLUGIN_ID_CORE_PROTOCOL)

#define VVC_MAX_RAW_CHANNELS 4

#define VVC_RAW_RECV_BUF_SIZE VVC_TRANSPORT_RECV_BUF_SIZE
#define VVC_RAW_RECV_BUF_THRESHOLD (VVC_RAW_RECV_BUF_SIZE - 1024)
#define VVC_RAW_RECV_ALLOC_SIZE (16 * 1024)
#define VVC_RAW_PRE_COMMIT_RECV_SIZE (2 * 1024)

typedef void (*onConnect)(void *cbData);

/*
 * Raw channel equivalent of VvcRecvState, with optimizations for VVC zero copy
 * and diminished malloc/frees, with circular usage of recvBuf, in a lockless
 * manner.
 */
typedef struct vvcRawRecvState {
   uint8 *recvBuf;
   uint8 *recvBufEnd;
   unsigned int recvBufSize;
   unsigned int recvBufOffset;
   Atomic_uint32 recvBufCount;
} VvcRawRecvState;


typedef struct vvcRawChanContext {
   MXUserRecLock *pollLock;
   AsyncSocket *parent;          // listening socket on the server
   AsyncSocket *asock;           // connect/accept socket
   AsyncSocket *unverifiedAsock; // BEAT server-side channel-asock match
   VvcChannel *channel;
   VvcRawRecvState *recvState;
   Bool isServer;
   VvcRawChanType type;
   unsigned char serialNo; // TCP server-side channel-asock match
                           // 255 active raw channels should be sufficient
   void *preCommitBuf;     // client-side pre-commit buffer used for AsyncBWE error trap

   union {
      void *openChan; // client side
   } openReq;

   // poll callbacks
   onConnect rawChannelOnConnect;

   uint32 queuedBytes;
   int32 bytesToSend;

   uint64 sentBytes;
   uint64 savedSentBytes;

   double cachedBW;
   double cachedBWTime;

   // Additional fields for BENIT support
   AsyncSocket *beatAsock; // "VVC" type raw channels start as BEAT, TCP is
   AsyncSocket *bweAsock;  // added on-demand, then both asock ptrs are assigned
   MXUserRecLock *bwePollLock;
   uint64 switchByteCount;      // recv cutoff sent by peer initiating the switch
   uint64 localSwitchByteCount; // send cutoff for self initiated switch
   VvcRawChanType switchTo;     // protocol initiator will switch into for sends
   Bool switchInitiator;
   Atomic_uint32 tcpConnectInProgress;

   VvcSessionActiveTransportType lastKnownPeerTransportType;
} vvcRawChanCtx;

vvcRawChanCtx *VvcRawChanBeatConnect(VvcOpenChan *openChan);
vvcRawChanCtx *VvcRawChanTcpConnect(VvcChannel *channel, void *connectionCookie,
                                    unsigned char serialNo);
VvcStatus VvcRawChannelSend(VvcChannel *channel, uint8 *buf, size_t len, void *clientData);
void VvcRawChannelClose(VvcChannel *channel);
void VvcAcceptChannelInt(VvcChannel *channel, VvcOpenChan *openChan);
void VvcRawChannelAssignAsock(VvcChannel *channel, AsyncSocket *asock, Bool isClient);
VvcRawChanType VvcGetRawChannelType(char *rawChannelType);
const char *VvcGetRawChannelName(VvcRawChanType type);
void VvcRawChannelAccept(VvcChannel *channel, VvcOpenChan *openChan, uint8 *initialData,
                         size_t initialDataLen);
void VvcRawChannelFailover(VvcSession *session, char *name, VvcRawChanType channelType,
                           uint32 *flags, vvcRawChanCtx **rawCtx);
Bool VvcRawChannelClientFailover(VvcSession *session, char *name);

void VvcRawChannelCtrlOpConnect(VvcSession *session, uint32 channelId, VvcRawChanType switchTo);
void VvcRawChannelCtrlOpSwitch(VvcSession *session, uint32 channelId, VvcRawChanType switchTo,
                               uint64 switchAt);
void VvcRawAsockErrorCb(int error, AsyncSocket *asock, void *data);

void VvcRawChannelAssignBweAsock(VvcChannel *channel, AsyncSocket *asock);

Bool VvcRawChannelSwitchProtocol(VvcChannel *channel, VvcRawChanType switchTo);

Bool VvcRawChannelConvertToRegular(VvcChannel *channel);

VvcStatus VvcRawChannelGetNetworkStats(VvcChannel *channel, VvcNetworkStats *stats, uint64 nowMs);

VvcStatus VvcGetActiveRawChannelInfo(VvcSession *session, VvcNetworkStats *rawNetworkStats,
                                     uint64 *rawSentBytesDelta, uint32 *protocol,
                                     uint32 *peerProtocol);

void VvcRawChannelCtrlOpCommit(VvcChannel *channel);
void VvcRawChannelCtrlOpCommit2(VvcChannel *channel);
void VvcRawChannelCtrlOpCommitAck(VvcChannel *channel);

#endif
