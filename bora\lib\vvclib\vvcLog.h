/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */


/*
 * vvcLog.h
 *
 * Logging for VVC
 *
 */


#ifndef __VVC_LOG_H
#define __VVC_LOG_H


#include "vvclib.h"
#include "vvcPerf.h"

// Log level - set by user (vvclib defaults to VVCLOG_INFO)
extern VvcLogLevel gCurLogLevel;
// Mem log level - set by user (defaults to VVCMEMLOG_NONE)
extern VvcMemLogLevel gCurMemLogLevel;
extern VvcPktTraceLevel gCurPktTraceLevel;

// Log performance data - set by user
extern Bool gLogPerfData;


/*
 * Bora style logging functions - to be implemented by binary importing this lib
 */

extern void Panic(const char *fmt, // IN
                  ...);            // IN (opt)

extern void Warning(const char *fmt, // IN
                    ...);            // IN (opt)

extern void Log(const char *fmt, // IN
                ...);            // IN (opt)


/*
 * Initialize logging and allocate resources
 */
void VvcLogInit();

/*
 * Free logging relevant allocated resources
 */
void VvcLogUninit();

/*
 * Checks if we are to log anything at this log level
 */
Bool IsLogLevelActive(VvcLogLevel logLevel);

/*
 * Sets gCurLogLevel to passed in strLogLevel
 */
Bool VvcSetLogLevel(char *strLogLevel);

/*
 * Sets gCurMemLogLevel to passed in strMemLogLevel
 */
Bool VvcSetMemLogLevel(char *strMemLogLevel);

/*
 * Sets packet trace level to desired value
 */
Bool VvcSetPktTraceLevel(char *strPktTraceLevel);

/*
 * Resets gCurLogLevel to its original value
 */
void VvcResetLogLevel();

/*
 * Resets gCurMemLogLevel to its original value
 */
void VvcResetMemLogLevel();

/*
 * Resets packet trace level back to the config value
 */
void VvcResetPktTraceLevel();

/*
 * Caches performance data - for logging purposes
 */
void VvcLog_CacheSessionPerfData(VvcPerfCounterSet *stats);


/*
 * All log statements contain this prefix
 */
#define VVC_LOG_PRFX "VVC: "

/*
 * Macros for various log levels
 */
#define VvcVerbose(fmt, ...)                                                                       \
   do {                                                                                            \
      if (gCurLogLevel >= VVCLOG_VERBOSE) {                                                        \
         Log(VVC_LOG_PRFX "(VERBOSE) " fmt, ##__VA_ARGS__);                                        \
      }                                                                                            \
   } while (0)

#define VvcTrace(fmt, ...)                                                                         \
   do {                                                                                            \
      if (gCurLogLevel >= VVCLOG_TRACE) {                                                          \
         Log(VVC_LOG_PRFX "(TRACE) " fmt, ##__VA_ARGS__);                                          \
      }                                                                                            \
   } while (0)

#define VvcDebug(fmt, ...)                                                                         \
   do {                                                                                            \
      if (gCurLogLevel >= VVCLOG_DEBUG) {                                                          \
         Log(VVC_LOG_PRFX "(DEBUG) " fmt, ##__VA_ARGS__);                                          \
      }                                                                                            \
   } while (0)

#define VvcLog(fmt, ...)                                                                           \
   do {                                                                                            \
      if (gCurLogLevel >= VVCLOG_INFO) {                                                           \
         Log(VVC_LOG_PRFX fmt, ##__VA_ARGS__);                                                     \
      }                                                                                            \
   } while (0)

#define VvcWarning(fmt, ...)                                                                       \
   do {                                                                                            \
      if (gCurLogLevel >= VVCLOG_WARN) {                                                           \
         Warning(VVC_LOG_PRFX fmt, ##__VA_ARGS__);                                                 \
      }                                                                                            \
   } while (0)

#define VvcError(fmt, ...)                                                                         \
   do {                                                                                            \
      if (gCurLogLevel >= VVCLOG_ERROR) {                                                          \
         Warning(VVC_LOG_PRFX "(ERROR) " fmt, ##__VA_ARGS__);                                      \
      }                                                                                            \
   } while (0)

#define VvcFatal(fmt, ...)                                                                         \
   do {                                                                                            \
      if (gCurLogLevel >= VVCLOG_FATAL) {                                                          \
         Panic(VVC_LOG_PRFX fmt, ##__VA_ARGS__);                                                   \
      }                                                                                            \
   } while (0)

// This will only get compiled into beta and obj builds
#if defined(VMX86_DEBUG)
#   define VvcMemTrace(fmt, ...)                                                                   \
      do {                                                                                         \
         if (gCurMemLogLevel >= VVCMEMLOG_ALL) {                                                   \
            Log(VVC_LOG_PRFX "(MEMTRACE) " fmt, ##__VA_ARGS__);                                    \
         }                                                                                         \
      } while (0)
#else
#   define VvcMemTrace(fmt, ...)
#endif

#define VvcPktTrace(fmt, ...)                                                                      \
   do {                                                                                            \
      if (gCurPktTraceLevel >= VVCPKTTRACE_ALL) {                                                  \
         Log(VVC_LOG_PRFX "(PKTTRACE) " fmt, ##__VA_ARGS__);                                       \
      }                                                                                            \
   } while (0)

#define LOG_IF_MAIN(instance, fmt, ...)                                                            \
   do {                                                                                            \
      if (gCurLogLevel >= VVCLOG_DEBUG && instance->flags & VVC_INSTANCE_MAIN) {                   \
         VvcDebug(fmt, ##__VA_ARGS__);                                                             \
      }                                                                                            \
   } while (0)


/*
 * Log function implementation for Allow Virtual Channel
 */
void VvcLog_VChanValidate(int logLevel,         // IN
                          const char *funcName, // IN
                          const char *fmt,      // IN
                          ...);                 // IN

#ifdef WIN32

/*
 * Logs last error code - windows only
 */
void LogLastErr(char *funcName, char *errMsg, ...);

#endif // WIN32


#endif // __VVC_LOG_H
