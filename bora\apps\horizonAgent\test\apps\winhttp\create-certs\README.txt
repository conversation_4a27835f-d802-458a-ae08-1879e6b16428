horizon-core-agent-test.crt and horizon-core-agent-exp.crt were created using
Horizon Intermediate CA (horizon-inter.crt) from
bora\apps\horizonAgent\test\apps\wsnm_mqtt_test\file\wsnm_mqtt_test

They were created using the same private key. For example:

openssl req -new -key horizon-core-agent-test.key -out horizon-core-agent-exp.csr -config horizon-core-agent-exp.cnf
openssl x509 -req -in horizon-core-agent-exp.csr -CA horizon-inter.crt -CAkey horizon-inter.key -CAcreateserial -out horizon-core-agent-exp.crt -days 1 -sha256 -extfile v3_ext.cnf


**********************
This is how I created the certificates that are used in the CRL tests.
**********************

ROOT:

openssl genrsa -out revoke-root.key 4096
openssl req -x509 -new -nodes -key revoke-root.key -sha256 -days 3650 -out revoke-root.crt -config revoke-root.cnf

openssl genrsa -out intermediateA.key 4096
openssl req -new -key intermediateA.key -out intermediateA.csr -config intermediateA.cnf

openssl ca -batch -config ca.cnf -extensions v3_ca -in intermediateA.csr -out intermediateA.crt -keyfile revoke-root.key -cert revoke-root.crt -days 3650


openssl genrsa -out intermediateB.key 4096
openssl req -new -key intermediateB.key -out intermediateB.csr -config intermediateB.cnf

openssl ca -batch -config ca.cnf -extensions v3_ca -in intermediateB.csr -out intermediateB.crt -keyfile revoke-root.key -cert revoke-root.crt -days 3650


# This generates a blank CRL
openssl ca -gencrl -config ca.cnf -out crl/crl.pem -keyfile revoke-root.key -cert revoke-root.crt
# This revokes the certificate in the internal database
openssl ca -config ca.cnf -revoke newcerts/1001.pem -keyfile revoke-root.key -cert revoke-root.crt
# Calling this again will generate the updated CRL
openssl ca -gencrl -config ca.cnf -out crl/crl2.pem -keyfile revoke-root.key -cert revoke-root.crt
# Now DER encode it
openssl crl -in crl/crl2.pem -outform DER -out crl/hzagent-1.crl


CD to interA

openssl genrsa -out leafA.key 2048
openssl req -new -key leafA.key -out leafA.csr -config leafA.cnf

openssl ca -batch -config ca.cnf -extensions v3_ca -in leafA.csr -out leafA.crt -keyfile intermediateA.key -cert intermediateA.crt -days 3650

openssl genrsa -out leafB.key 2048
openssl req -new -key leafB.key -out leafB.csr -config leafB.cnf

openssl ca -batch -config ca.cnf -extensions v3_ca -in leafB.csr -out leafB.crt -keyfile intermediateA.key -cert intermediateA.crt -days 3650

openssl ca -gencrl -config ca.cnf -out crl/crl.pem -keyfile intermediateA.key -cert intermediateA.crt
openssl ca -config ca.cnf -revoke newcerts/2001.pem -keyfile intermediateA.key -cert intermediateA.crt
openssl ca -gencrl -config ca.cnf -out crl/crl2.pem -keyfile intermediateA.key -cert intermediateA.crt
openssl crl -in crl/crl2.pem -outform DER -out crl/hzagent-2.crl


CD to interB

openssl genrsa -out leafC.key 2048
openssl req -new -key leafC.key -out leafC.csr -config leafC.cnf

openssl ca -batch -config ca.cnf -extensions v3_ca -in leafC.csr -out leafC.crt -keyfile intermediateB.key -cert intermediateB.crt -days 3650

openssl ca -gencrl -config ca.cnf -out crl/crl.pem -keyfile intermediateB.key -cert intermediateB.crt
openssl crl -in crl/crl.pem -outform DER -out crl/hzagent-3.crl