<?xml version="1.0" encoding="utf-8"?>

<!--
*******************************************************************************
   Copyright (c) Omnissa, LLC. All rights reserved.
   This product is protected by copyright and intellectual property laws in the
   United States and other countries as well as by international treaties.
   - Omnissa Restricted

   Components.wxi  (product: Horizon Agent)

      WiX include for all components included in this product.
*******************************************************************************
-->

<Include>

   <!-- Merge Modules -->
   <DirectoryRef Id="INSTALLDIR">
      <Merge Id="Blast" Language="0" SourceFile="$(var.MsmStageDir)\Blast64.msm" DiskId="1" />
      <Merge Id="DCT" Language="0" SourceFile="$(var.MsmStageDir)\HorizonDct64.msm" DiskId="1" />
      <Merge Id="NGVC" Language="0" SourceFile="$(var.MsmStageDir)\OmnissaICAgent64.msm" DiskId="1" />
      <Merge Id="SerialPortRedirection" Language="1033" SourceFile="$(var.CONAN_FABULATECH_SERIALPORT_ROOT)\win64\spr-rdp-srv-x64.msm" DiskId="1" />
      <!-- The next two msms are registry-only so they don't have their own cab files. -->
      <Merge Id="UnityTouch" Language="0" SourceFile="$(var.RDE_RFT_UNITYTOUCH_X64)\VMwareUnityTouchx64.msm" DiskId="1" />
      <Merge Id="Hzncdrfilter" Language="0" SourceFile="$(var.MsmStageDir)\Hzncdrfilter64.msm" DiskId="1" />
      <Merge Id="HznVaudio" Language="0" SourceFile="$(var.MsmStageDir)\HznVaudio64.msm" DiskId="1" />
      <Merge Id="HznVscrd" Language="0" SourceFile="$(var.MsmStageDir)\HznVscrd64.msm" DiskId="1" />
      <Merge Id="HznVudpd" Language="0" SourceFile="$(var.MsmStageDir)\HznVudpd64.msm" DiskId="1" />
      <Merge Id="Hznsdo" Language="0" SourceFile="$(var.MsmStageDir)\Hznsdo64.msm" DiskId="1" />
      <Merge Id="Omnsdr" Language="0" SourceFile="$(var.MsmStageDir)\OmnissaOmnsdr64.msm" DiskId="1" />
      <Merge Id="HznVdisplay" Language="0" SourceFile="$(var.MsmStageDir)\HznVdisplay64.msm" DiskId="1" />
      <Merge Id="HznVidd" Language="0" SourceFile="$(var.MsmStageDir)\hznvidd64.msm" DiskId="1" />
      <Merge Id="HznVidd2" Language="0" SourceFile="$(var.MsmStageDir)\hznvidd2x64.msm" DiskId="1" />
      <Merge Id="PerfTracker" Language="0" SourceFile="$(var.RDE_RFT_PERFTRACKER_X64)\VMwareHorizonPerfTracker64.msm" DiskId="1" />
      <Merge Id="Server-Jre" Language="0" SourceFile="$(var.MsmStageDir)\VMwareViewServerJre64.msm" DiskId="1" />
      <!-- TODO disabled until Corretto JDK is approved to be shipped -->
      <!-- <Merge Id="Server-Jre-Corretto" Language="0" SourceFile="$(var.MsmStageDir)\VMwareViewServerJreCorretto64.msm" DiskId="1" /> -->
      <?if $(var.EXCLUDE_SESSION_MONITOR)=0?>
         <!-- Because this logic is optional, it reuses an existing cabinet.
              This allows us to avoid "cabinet unused" warnings if this logic is not run.
              If this condition is ever removed, we should create new cab files for each
              merge module.
          -->
         <Merge Id="Omnksm" Language="0" SourceFile="$(var.MsmStageDir)\OmnissaKsm.msm" DiskId="1" />
      <?endif?>
      <Merge Id="Wsauth" Language="0" SourceFile="$(var.MsmStageDir)\Wsauth64.msm" DiskId="1" />
   </DirectoryRef>

   <DirectoryRef Id="SCANNER_REDIRECTION">
      <Merge Id="ScannerRDPPFModule" Language="1033" SourceFile="$(var.CONAN_FABULATECH_SCANNER_ROOT)\win64\ScanPFModule-srv-x64.msm" DiskId="1" />
   </DirectoryRef>

   <DirectoryRef Id="TARGETDIR">
      <Merge Id="HznRds" Language="0" SourceFile="$(var.MsmStageDir)\HznRds64.msm" DiskId="1" />
      <Merge Id="Hznicpdr" Language="0" SourceFile="$(var.MsmStageDir)\Hznicpdr64.msm" DiskId="1" />
      <Merge Id="Hzngeoloc" Language="0" SourceFile="$(var.MsmStageDir)\Hzngeoloc64.msm" DiskId="1" />
   </DirectoryRef>

   <DirectoryRef Id="System64Folder">
      <Merge Id="Netlink" Language="1033" SourceFile="$(var.CONAN_FABULATECH_COMMON_ROOT)\win64\netlink-server-side-x64.msm" DiskId="1" />
      <Merge Id="Jail" Language="1033" SourceFile="$(var.CONAN_FABULATECH_COMMON_ROOT)\win64\DrvJailCore-x64.msm" DiskId="1" />
      <Merge Id="ScannerRDPSysModule" Language="1033" SourceFile="$(var.CONAN_FABULATECH_SCANNER_ROOT)\win64\ScanSysModule-srv-x64.msm" DiskId="1" />
   </DirectoryRef>

   <DirectoryRef Id="BIN">
      <Merge Id="AgentOpenSSL" Language="0" SourceFile="$(var.MsmStageDir)\VMwareHorizonAgentOpenSSL64.msm" DiskId="1" />
      <Merge Id="PSG" Language="0" SourceFile="$(var.MsmStageDir)\VMwareViewPsg64.msm" DiskId="1" />
   </DirectoryRef>

   <DirectoryRef Id="LIB">
      <Merge Id="Crypto" Language="0" SourceFile="$(var.MsmStageDir)\VMwareViewCrypto64.msm" DiskId="1" />
   </DirectoryRef>

   <DirectoryRef Id="INSTALLDIR">
      <Component Id="_golden_image.reg" Guid="E7840376-6606-4EAF-8FD8-4526EA8762D2">
         <Condition>GOLDEN_IMAGE_INSTALL=1</Condition>
         <RegistryValue Id="_golden_image_reg"
                        Root="HKLM"
                        Key="SOFTWARE\Omnissa\Horizon\Instant Clone Agent\Setup"
                        Name="GoldenImage"
                        Value="1"
                        Type="integer" />
      </Component>

      <Component Id="_32_agentAutoUpdate_BuildNumber.reg" Guid="230B84FE-83A8-4676-9B95-8CC3DA0D6DF5" Win64="no">
         <RegistryValue Id="_32_agentAutoUpdate_BuildNumber"
                        Root="HKLM"
                        Key="SOFTWARE\Omnissa\AgentVersions"
                        Name="View-Agent"
                        Value="$(var.MSIProductVersion).$(var.BUILD_NUMBER)"
                        Type="string" />
      </Component>

      <Component Id="_32_pcoipAudioFwd.reg" Guid="DF91D42D-C02E-421E-8EC0-3AA7A50ADF79" Win64="no">
         <RegistryValue Id="pcoipAudioFwd32"
                        Root="HKLM"
                        Key="SOFTWARE\Omnissa\Horizon\USB\PCoIPAudioForwarding"
                        Name="Disabled"
                        Value="0"
                        Type="integer" />
      </Component>

      <Component Id="_Registry_FipsEnabled" Guid="0908FDB6-356C-43EA-9C10-A2282EF1ACAE">
         <Condition>VDM_FIPS_ENABLED=1</Condition>
         <RegistryValue Id="vdmFipsEnabled"
                        Root="HKLM"
                        Key="SOFTWARE\Omnissa\Horizon"
                        Name="FipsMode"
                        Value="[VDM_FIPS_ENABLED]"
                        Type="integer" />
      </Component>

      <Component Id="_Registry_IpProtocolUsage" Guid="1920FC47-7A5D-40BD-9000-12CCABA9EC0E">
         <RegistryValue Id="IpProtocolUsage"
                        Root="HKLM"
                        Key="SOFTWARE\Omnissa\Horizon"
                        Name="IpProtocolUsage"
                        Value="[VDM_IP_PROTOCOL_USAGE]"
                        Type="string" />
      </Component>

      <Component Id="_Registry_SetDefaultMgmtPort" Guid="718A59EE-9857-41D1-879F-3482AEBA613A">
         <RegistryValue Id="MgmtPortDefault"
                        Root="HKLM"
                        Key="SOFTWARE\Omnissa\Horizon\Node Manager"
                        Name="Management Port"
                        Value="32111"
                        Type="string" />
      </Component>

      <!-- Install on RDSH and physical machines with Win 10 RS4 and above -->
      <Component Id="_Registry_bEnumerateHWBeforeSW" Guid="1EF75452-11DA-4091-8177-99CA563D368B">
         <Condition><![CDATA[TerminalServer Or (MsiNTProductType=1 And WINDOWS_10_RS4_AND_ABOVE=1 And (VM="" And Not TERA_HOST_CARD_PRESENT=1)) Or VDM_FORCE_RDS_INSTALL=1]]></Condition>
         <RegistryValue Id="bEnumerateHWBeforeSW"
                        Root="HKLM"
                        Key="SOFTWARE\Policies\Microsoft\Windows NT\Terminal Services"
                        Name="bEnumerateHWBeforeSW"
                        Value="1"
                        Type="integer" />
      </Component>

      <!-- Registry key value to mark VMware to Omnissa Registry Migration Done -->
      <Component Id="_Registry_Migration" Guid="F2BBC6AF-ADA7-4BCA-BC05-EC98C7FA7816" Permanent="yes">
         <RegistryValue Id="agentRegistryMigrated"
                        Root="HKLM"
                        Key="SOFTWARE\Omnissa\Horizon"
                        Name="AgentRegistryMigrated"
                        Value="1"
                        Type="string" />
      </Component>

      <Component Id="_arp.ico" Guid="67245FCA-D59E-4724-8358-7663DE650169">
         <CreateFolder>
            <Permission User="Administrators"
                        GenericAll="yes" />
            <Permission User="Everyone"
                        GenericExecute="yes"
                        Read="yes"
                        ReadAttributes="yes"
                        ReadExtendedAttributes="yes"
                        ReadPermission="yes"
                        Synchronize="yes" />
         </CreateFolder>
         <RemoveFolder Id="rmVDM" Directory="VDM_DIR" On="uninstall" />
         <RemoveFolder Id="rmAppData" Directory="APPDATA_DIR" On="uninstall" />
         <CopyFile Id="cacert_pem"
                SourceProperty="VMWARE_AGENT_INSTALLPATH_CERT"
                SourceName="cacert.pem"
                DestinationDirectory="CERT"
                DestinationName="cacert.pem" />
         <File Id="arp.ico"
               Name="arp.ico"
               KeyPath="yes"
               Source="$(var.SRCROOT)\install\msi\horizon\common\icons\setup.ico" />
         <RegistryValue Id="ForceDesktopAgent"
                        Root="HKLM"
                        Key="SOFTWARE\Omnissa\Horizon\Agent\Configuration"
                        Name="ForceDesktopAgent"
                        Value="[VdmForceDesktopAgent]"
                        Type="string" />
         <RegistryValue Id="arpIcon2"
                        Root="HKLM"
                        Key="SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\[ProductCode]"
                        Name="DisplayIcon"
                        Value="[INSTALLDIR]arp.ico"
                        Type="string" />
         <RegistryKey Id="arpIcon3"
                      Root="HKLM"
                      Key="SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\[ProductCode]"
                      ForceCreateOnInstall="yes" />
         <RegistryValue Id="brokerAddress"
                        Root="HKLM"
                        Key="SOFTWARE\Omnissa\Horizon\Agent\Configuration"
                        Name="Broker"
                        Value="[VDM_SERVER_NAME]"
                        Type="string" />
         <RegistryValue Id="buildNumber"
                        Root="HKLM"
                        Key="SOFTWARE\Omnissa\Horizon"
                        Name="BuildNumber"
                        Value="[BuildNumber]"
                        Type="string" />
         <RegistryValue Id="installLogsDir"
                        Root="HKLM"
                        Key="SOFTWARE\Omnissa\Horizon"
                        Name="InstallLogsDir"
                        Value="[CommonAppDataFolder]Omnissa\Horizon\logs"
                        Type="string" />
         <RegistryValue Id="installPath"
                        Root="HKLM"
                        Key="SOFTWARE\Omnissa\Horizon"
                        Name="AgentInstallPath"
                        Value="[INSTALLDIR]"
                        Type="string" />
         <!-- App Volumes depends on the InstalledWithTSRole registry key.
              Do not modify without consulting AV, among others. -->
         <RegistryValue Id="terminalServerRole"
                        Root="HKLM"
                        Key="SOFTWARE\Omnissa\Horizon"
                        Name="InstalledWithTSRole"
                        Value="[TerminalServer]"
                        Type="string" />
         <RegistryValue Id="vcManaged"
                        Root="HKLM"
                        Key="SOFTWARE\Omnissa\Horizon\Node Manager"
                        Name="Managed"
                        Value="[VDM_VC_MANAGED_AGENT]"
                        Type="integer" />
         <RegistryValue Id="versionNumber"
                        Root="HKLM"
                        Key="SOFTWARE\Omnissa\Horizon"
                        Name="ProductVersion"
                        Value="[ProductVersion]"
                        Type="string" />
         <RegistryValue Id="mfwSessionMonitor"
                        Root="HKLM"
                        Key="SOFTWARE\Omnissa\Horizon\Node Manager"
                        Name="UseSessionMonitorEvents"
                        Value="True"
                        Type="string" />
         <RemoveRegistryKey Id="volatileR"
                            Action="removeOnUninstall"
                            Key="SOFTWARE\Omnissa\Horizon\Agent\Configuration\VolatileSettings"
                            Root="HKLM" />
      </Component>

      <Component Id="_open_source_licenses.txt" Guid="3C0812B6-32CE-4C98-AAAC-F7C33758F1EC">
         <File Source="$(var.OSL_TXT_FILE)" KeyPath="yes" />
      </Component>

      <Component Id="_pcoip_server_fips_mode.reg" Guid="E0EC2A06-8D7B-4E02-941B-A3CCD33338FC" Transitive="yes">
         <Condition>VDM_FIPS_ENABLED=1</Condition>
         <RegistryValue Id="PCoIP_pcoip_enable_fips_mode"
                        Root="HKLM"
                        Key="SOFTWARE\Teradici\PCoIP\pcoip_admin"
                        Name="pcoip.enable_fips_mode"
                        Value="1"
                        Type="integer" />
      </Component>

      <Component Id="_pcoip_tcpport.reg" Guid="9726B007-43F4-4734-B4F8-CCDE0B8E89B9" Transitive="yes">
         <Condition>TerminalServer</Condition>
         <RegistryValue Id="PCoIP_pcoip_tcpport"
                        Root="HKLM"
                        Key="SOFTWARE\Teradici\PCoIP\pcoip_admin"
                        Name="pcoip.tcpport"
                        Value="4173"
                        Type="integer" />
      </Component>

      <Component Id="_pcoip_udpport.reg" Guid="2FDD0E4E-896D-4A43-A0D7-73527FF07F66" Transitive="yes">
         <Condition>TerminalServer</Condition>
         <RegistryValue Id="PCoIP_pcoip_udpport"
                        Root="HKLM"
                        Key="SOFTWARE\Teradici\PCoIP\pcoip_admin"
                        Name="pcoip.udpport"
                        Value="4173"
                        Type="integer" />
      </Component>

      <!--Default Audio endpoint enumerator settings for RDSH-->
      <Component Id="_audio_enumerator_def.reg" Guid="49A77070-C170-41D6-A332-11D7D95A2198" Transitive="yes">
         <Condition><![CDATA[TerminalServer Or (MsiNTProductType=1 And WINDOWS_10_RS4_AND_ABOVE=1 And (VM="" And Not TERA_HOST_CARD_PRESENT=1)) Or VDM_FORCE_RDS_INSTALL=1]]></Condition>
         <RegistryValue Id = "Audio_enumerator_def.dll"
                        Root="HKLM"
                        Key="SYSTEM\CurrentControlSet\Control\Terminal Server\WinStations\Horizon-RDS"
                        Name="AudioEnumeratorDll"
                        Value="hznaudioendpoint.dll"
                        Type="string" />
         <RegistryValue Id = "Use_audio_endpoint_def.reg"
                        Root="HKLM"
                        Key="SYSTEM\CurrentControlSet\Control\Terminal Server\WinStations\Horizon-RDS"
                        Name="fUseHznAudioEndpoint"
                        Value="1"
                        Type="integer" />
      </Component>

      <Component Id="_nodeManagerSecuredQueues.reg" Guid="BA1041E3-053A-48E3-9158-ECDFD92FA16C">
         <RegistryValue Id="eventLoggerServiceQueueExePath"
                        Root="HKLM"
                        Key="SOFTWARE\Omnissa\Horizon\Node Manager\SecuredQueues"
                        Name="EventLoggerService"
                        Value="[BIN]wsnm_jms"
                        Type="string" />
         <RegistryValue Id="jmsBridgeQueueExePath"
                        Root="HKLM"
                        Key="SOFTWARE\Omnissa\Horizon\Node Manager\SecuredQueues"
                        Name="JMSBridge"
                        Value="[BIN]wsnm_jms"
                        Type="string" />
         <RegistryValue Id="pcoipMfwQueueExePath"
                        Root="HKLM"
                        Key="SOFTWARE\Omnissa\Horizon\Node Manager\SecuredQueues"
                        Name="pcoip-mfw"
                        Value="[TERADICI_PCOIP_SERVER_X64]pcoip_server_win32"
                        Type="string" />
         <RegistryValue Id="protocolProviderQueueExePath"
                        Root="HKLM"
                        Key="SOFTWARE\Omnissa\Horizon\Node Manager\SecuredQueues"
                        Name="ProtocolProvider"
                        Value="[System64Folder]svchost"
                        Type="string" />
         <RegistryValue Id="timingProfilerServiceQueueExePath"
                        Root="HKLM"
                        Key="SOFTWARE\Omnissa\Horizon\Node Manager\SecuredQueues"
                        Name="TimingProfilerService"
                        Value="[BIN]wsnm_jms"
                        Type="string" />
         <RegistryValue Id="agentSessionHelpDeskQueueExePath"
                        Root="HKLM"
                        Key="SOFTWARE\Omnissa\Horizon\Node Manager\SecuredSessionQueues"
                        Name="AgentSessionHelpDesk"
                        Value="[BIN]wssm"
                        Type="string" />
         <RegistryValue Id="desktopInstanceQueueExePath"
                        Root="HKLM"
                        Key="SOFTWARE\Omnissa\Horizon\Node Manager\SecuredSessionQueues"
                        Name="DesktopInstance"
                        Value="[BIN]wssm"
                        Type="string" />
         <RegistryValue Id="pcoipVchanQueueExePath"
                        Root="HKLM"
                        Key="SOFTWARE\Omnissa\Horizon\Node Manager\SecuredSessionQueues"
                        Name="pcoip_vchan_"
                        Value="[TERADICI_PCOIP_SERVER_X64]pcoip_server_win32"
                        Type="string" />
         <RegistryValue Id="sessionManagerQueueExePath"
                        Root="HKLM"
                        Key="SOFTWARE\Omnissa\Horizon\Node Manager\SecuredSessionQueues"
                        Name="SessionManager"
                        Value="[BIN]wssm"
                        Type="string" />
      </Component>

      <Component Id="_ws_perfMon.dll_reg" Guid="88A66FE3-B28F-48EC-89C7-152075BC5E78">
         <RegistryValue Id="ws_perfmonCpuPercentage"
                        Root="HKLM"
                        Key="SOFTWARE\Omnissa\Horizon\Performance Stats"
                        Name="CpuPercentage"
                        Value="0"
                        Type="string" />
      </Component>

      <!-- This registry value is designed to increase the service-start timeout value to 90s.
           This was brought about by this bug:
           https://bugzilla.eng.vmware.com/show_bug.cgi?id=2085160
           (This registry value requires a restart in order to take effect) -->
      <Component Id="_services_pipe_timeout_reg" Guid="80C5FB98-952D-46A0-B596-41DD90C08F67">
         <RegistryValue Id="_services_pipe_timeout"
                        Root="HKLM"
                        Key="SYSTEM\CurrentControlSet\Control"
                        Name="ServicesPipeTimeout"
                        Value="90000"
                        Type="integer" />
      </Component>

      <!-- This registry value will be equal to 1 in case the deployment
           machine is a physical machine, or 0 if it's a virtual machine. -->
      <Component Id="_vm_type_setting_reg" Guid="BB31F109-E570-452d-BD0C-2D03C71D2453">
         <RegistryValue Id="_vm_type_setting"
                        Root="HKLM"
                        Key="SOFTWARE\Omnissa\Horizon"
                        Name="HorizonDeploymentType"
                        Value="[VM_TYPE_REG]"
                        Type="integer" />
      </Component>
   </DirectoryRef>

   <DirectoryRef Id="BIN">
      <Component Id="_evd_configurationapi.dll" Guid="2D1BEDF9-5C1B-4D60-BDD5-7E933538B408">
         <Condition>WINDOWS_10_RS5_AND_ABOVE=1 And (IsWindowsWVD=1 Or TerminalServer)</Condition>
         <File Id="EVDConfigurationAPI.dll"
               Source="$(var.SRCROOT)\apps\horizonAgent\Frozen_Files\EVDConfigurationAPI\EVDConfigurationAPI.dll" KeyPath="yes" />
      </Component>

      <Component Id="_abctrl.dll" Guid="1D1C69A2-7A5C-48EB-B547-259967F66B35">
         <File Source="$(var.StageDir)\abctrl.dll" KeyPath="yes" />
      </Component>

      <Component Id="_hzaprep.exe" Guid="A1B2C3D4-E5F6-7890-ABCD-EF1234567890">
         <File Source="$(var.StageDir)\hzaprep.exe" KeyPath="yes" />
      </Component>

      <Component Id="_bin_wsnm_scredir.dll" Guid="543CAB2C-4035-442F-81FA-59BA4B411563">
         <File Source="$(var.StageDir)\wsnm_scredir.dll" KeyPath="yes" />
         <RegistryValue Id="pluginWsnmScredir1"
                        Root="HKLM"
                        Key="SOFTWARE\Omnissa\Horizon\Plugins\wsnm\scredir"
                        Name="Filename"
                        Value="wsnm_scredir.dll"
                        Type="string" />
      </Component>

      <Component Id="_bin_wsnm_helpdesk.dll" Guid="028F63DE-110F-4330-9B04-74BA7C4506E8">
            <File Source="$(var.StageDir)\wsnm_helpdesk.dll" KeyPath="yes" />
            <RegistryValue Id="pluginWsnmHelpdesk"
                           Root="HKLM"
                           Key="SOFTWARE\Omnissa\Horizon\Plugins\wsnm\helpdesk"
                           Name="Filename"
                           Value="wsnm_helpdesk.dll"
                           Type="string" />
            <RegistryValue Id="pluginWsnmHelpdeskDepends"
                           Root="HKLM"
                           Key="SOFTWARE\Omnissa\Horizon\Plugins\wsnm\helpdesk"
                           Name="DependsOn"
                           Value="perfmon"
                           Type="string" />
      </Component>

      <Component Id="_bin_wssm_helpdesk.dll" Guid="691CDA02-C3AB-154A-013A-BE0A1357814A">
            <File Source="$(var.StageDir)\wssm_helpdesk.dll" KeyPath="yes" />
            <RegistryValue Id="pluginWssmHelpdesk"
                           Root="HKLM"
                           Key="SOFTWARE\Omnissa\Horizon\Plugins\wssm\helpdesk"
                           Name="Filename"
                           Value="wssm_helpdesk.dll"
                           Type="string" />
      </Component>
   </DirectoryRef>

   <DirectoryRef Id="SCRIPTS">
      <Component Id="_remove_patch.ps1" Guid="2668632E-A2C8-4B44-ABB0-20879D6B731E">
         <File Source="$(var.GOBUILD_WINDOWS_INSTALLKIT_ROOT)\scripts\patching\remove-patch.ps1"
               KeyPath="yes" />
      </Component>
   </DirectoryRef>

   <DirectoryRef Id="SYS_DRIVERS">
      <Component Id="_vmkbd.sys" Guid="d68ad488-8281-4c1e-9186-f6bc9326d033" SharedDllRefCount="yes" Transitive="yes" DiskId="1">
         <Condition>Not VM=""</Condition>
         <File Source="$(var.BuildDir)\vmkbd\Win7$(var.driverConfig)\x64\bin\vmkbd.sys" KeyPath="yes" />
         <RegistryValue Id="UpperFilterKbd"
                        Root="HKLM"
                        Key="SYSTEM\CurrentControlSet\Control\Class\{4D36E96B-E325-11CE-BFC1-08002BE10318}"
                        Name="UpperFilters"
                        Value="vmkbd[~]"
                        Type="string" />
         <RegistryValue Id="UpperFilterMouse"
                        Root="HKLM"
                        Key="SYSTEM\CurrentControlSet\Control\Class\{4D36E96F-E325-11CE-BFC1-08002BE10318}"
                        Name="UpperFilters"
                        Value="vmkbd[~]"
                        Type="string" />
         <RegistryValue Id="vmkbdErrorControl"
                        Root="HKLM"
                        Key="SYSTEM\CurrentControlSet\Services\vmkbd"
                        Name="ErrorControl"
                        Value="1"
                        Type="integer" />
         <RegistryValue Id="vmkbdStart"
                        Root="HKLM"
                        Key="SYSTEM\CurrentControlSet\Services\vmkbd"
                        Name="Start"
                        Value="3"
                        Type="integer" />
         <RegistryValue Id="vmkbdType"
                        Root="HKLM"
                        Key="SYSTEM\CurrentControlSet\Services\vmkbd"
                        Name="Type"
                        Value="1"
                        Type="integer" />
      </Component>
   </DirectoryRef>

   <DirectoryRef Id="UMDF_DRIVERS">
      <Component Id="_hznvidd.reg" Guid="749CE5C3-8D2C-4D71-A6EC-D88ED2FF9587">
         <RegistryValue Id="HznviddTerminateIndirectOnStall"
                        Root="HKLM"
                        Key="SYSTEM\CurrentControlSet\Control\GraphicsDrivers"
                        Name="TerminateIndirectOnStall"
                        Value="0"
                        Type="integer" />
      </Component>
   </DirectoryRef>

   <DirectoryRef Id="TERADICI_PCOIP_SERVER">
      <Component Id="_psg.reg" Guid="02417911-A659-B282-C7B8-F9E658D9B385" KeyPath="yes">
         <RegistryValue Id="PSG_InternalBindIP"
                        Root="HKLM"
                        Key="SOFTWARE\Teradici\SecurityGateway"
                        Name="InternalBindIP"
                        Value="[VDM_LOOPBACK_IP]"
                        Type="string" />
      </Component>

      <Component Id="_32_pcoip_vchan.dll" Guid="99E0A4AF-1CA9-44C9-8D45-21A07C36D388" Win64="no">
         <File Source="$(var.BinRoot)\x86\pcoip_vchan.dll" KeyPath="yes" />
      </Component>

      <Component Id="_hznicpdr.reg" Guid="8E32E710-1AFB-46D7-8BC1-93DC82EF093E" KeyPath="yes">
         <RegistryValue Id="HZNICPDR_InterceptModules_64"
                        Action="append"
                        Root="HKLM"
                        Key="SYSTEM\CurrentControlSet\Services\hznicpdr"
                        Name="Modules"
                        Type="multiString">
            <MultiStringValue>[System64Folder]hznsci.dll</MultiStringValue>
         </RegistryValue>
         <RegistryValue Id="HZNICPDR_InterceptModules_32"
                        Action="append"
                        Root="HKLM"
                        Key="SYSTEM\CurrentControlSet\Services\hznicpdr"
                        Name="Wow64Modules"
                        Type="multiString">
            <MultiStringValue>[System64Folder]hznsci.dll</MultiStringValue>
         </RegistryValue>
      </Component>
   </DirectoryRef>

   <DirectoryRef Id="TERADICI_PCOIP_SERVER_INTEL_SDK_X64">
      <Component Id="_64_mfxplugin64_screen_capture.dll" Guid="1AC02F87-87B8-45D5-AAAB-21AA97123393">
         <File Source="$(var.StageDir)\22d62c07e672408fbb4cc20ed7a053e4\mfxplugin64_screen_capture.dll"
               KeyPath="yes" />
      </Component>

      <Component Id="_64_plugin.cfg" Guid="554C77A4-C88D-471B-88CF-1A69C6AC8089">
         <File Id="plugin.cfg64"
               Name="plugin.cfg"
               KeyPath="yes"
               Source="$(var.StageDir)\22d62c07e672408fbb4cc20ed7a053e4\plugin.cfg"/>
      </Component>
   </DirectoryRef>


   <DirectoryRef Id="TERADICI_PCOIP_SERVER_X64">
      <Component Id="_64_OmnResolutionSet.dll" Guid="24D58B86-39ED-4148-A911-1A6F9FB1F928">
         <File Id="OmnResolutionSet.dll64"
               Name="OmnResolutionSet.dll"
               KeyPath="yes"
               Source="$(var.StageDir)\OmnResolutionSet.dll"/>
         <RegistryValue Id="resolutionSetSdk64"
                        Root="HKLM"
                        Key="SOFTWARE\Omnissa\Horizon\SetResolutionSDK"
                        Name="InstallPath"
                        Value="[TERADICI_PCOIP_SERVER_X64]"
                        Type="string" />
      </Component>

      <Component Id="_64_audiodevtap.dll" Guid="6EC9A7EF-5CEE-4B3F-ADF5-1F4DCF469926">
         <File Id="audiodevtap.dll64"
               Name="audiodevtap.dll"
               KeyPath="yes"
               Source="$(var.StageDir)\audiodevtap.dll"/>
      </Component>

      <Component Id="_64_inputdevtap.dll" Guid="B8F73563-2A3E-4448-81D5-5626419E8804">
         <File Id="inputdevtap.dll64"
               Name="inputdevtap.dll"
               KeyPath="yes"
               Source="$(var.StageDir)\inputdevtap.dll"/>
      </Component>

      <?if $(var.SSL_VERSION) = "3.0" ?>
         <Component Id="_64_pcoip_libcrypto_3.dll" Guid="CAC13913-88E7-4639-AE67-CF89F9AA437C">
            <File Id="libcrypto_3.dll64"
                  Name="libcrypto-3-x64.dll"
                  KeyPath="yes"
                  Source="$(var.GOBUILD_PCOIP_SOFT_SERVER_ROOT)\win64\agent\libcrypto-3-x64.dll"/>
         </Component>

         <Component Id="_64_pcoip_libssl_3.dll" Guid="A39C3DFB-6BBA-4A7F-93A2-0D9B537BDB64">
            <File Id="libssl_3.dll64"
                  Name="libssl-3-x64.dll"
                  KeyPath="yes"
                  Source="$(var.GOBUILD_PCOIP_SOFT_SERVER_ROOT)\win64\agent\libssl-3-x64.dll"/>
         </Component>

         <Component Id="_64_pcoip_fips.dll" Guid="51BD6F80-E62F-43D1-A5ED-9DA37527FB36">
            <File Id="fips.dll64"
                  Name="fips.dll"
                  KeyPath="yes"
                  Source="$(var.GOBUILD_PCOIP_SOFT_SERVER_ROOT)\win64\agent\fips.dll"/>
         </Component>
      <?else?>
         <Component Id="_64_pcoip_libeay32.dll" Guid="4CEA1971-CF9B-4DFC-B21D-FB83602EC72B">
            <File Id="libeay32.dll64"
                  Name="libeay32.dll"
                  KeyPath="yes"
                  Source="$(var.GOBUILD_PCOIP_SOFT_SERVER_ROOT)\win64\agent\libeay32.dll"/>
         </Component>

         <Component Id="_64_pcoip_ssleay32.dll" Guid="A0477A9A-7BE0-40EE-B391-D5579637ECC6">
            <File Id="ssleay32.dll64"
                  Name="ssleay32.dll"
                  KeyPath="yes"
                  Source="$(var.GOBUILD_PCOIP_SOFT_SERVER_ROOT)\win64\agent\ssleay32.dll"/>
         </Component>
      <?endif?>

      <Component Id="_64_pcoip_libidn.dll" Guid="1A1CC588-B736-4AFC-8464-47A76616A534">
         <File Id="libidn.dll64"
               Name="idn-12.dll"
               KeyPath="yes"
               Source="$(var.GOBUILD_PCOIP_SOFT_SERVER_ROOT)\win64\agent\idn-12.dll"/>
      </Component>

      <Component Id="_64_pcoip_server_win32.exe" Guid="91322B8E-7B21-43F2-9684-E86380C102E3">
         <File Id="pcoip_server_win32.exe64"
               Name="pcoip_server_win32.exe"
               KeyPath="yes"
               Source="$(var.GOBUILD_PCOIP_SOFT_SERVER_ROOT)\win64\agent\pcoip_server_win32.exe"/>
         <RegistryValue Id="TeraHostPath"
                        Root="HKLM"
                        Key="SOFTWARE\Teradici\PCoIP"
                        Name="TeraHostPath"
                        Value="[TERADICI_PCOIP_SERVER_X64]pcoip_server_win32.exe"
                        Type="string" />
      </Component>

      <Component Id="_pcoip_vchan64.dll" Guid="0472FE74-196C-4839-9B89-35048E04B983">
         <File Id="pcoip_vchan64.dll"
               Name="pcoip_vchan.dll"
               KeyPath="yes"
               Source="$(var.BinRoot)\x64\pcoip_vchan.dll"/>
         <RegistryValue Id="VMwareServer"
                        Root="HKLM"
                        Key="SOFTWARE\Teradici\VChan\Plugins\Horizon_Server"
                        Name="dll"
                        Value="[TERADICI_PCOIP_SERVER_X64]pcoip_vchan.dll"
                        Type="string" />
      </Component>

      <Component Id="_64_pthreadVC2.dll" Guid="49A5EB05-EDB2-4D6F-B8D9-7E86F15109F2">
         <File Id="pthreadVC2.dll64"
               Name="pthreadVC2.dll"
               KeyPath="yes"
               Source="$(var.GOBUILD_PCOIP_SOFT_SERVER_ROOT)\win64\agent\pthreadVC2.dll"/>
      </Component>

      <Component Id="_64_svgadevtap.dll" Guid="EE1E99A5-970E-41B9-B033-A7675435B971">
         <File Id="svgadevtap.dll64"
               Name="svgadevtap.dll"
               KeyPath="yes"
               Source="$(var.StageDir)\svgadevtap.dll">
            <CopyFile Id="dupSvgadevtap.dll"
                      DestinationDirectory="BIN"
                      DestinationName="svgadevtap.dll" />
         </File>
      </Component>

      <Component Id="_64_svga_zlib1.dll" Guid="5C643569-37D8-4D77-991E-B2A5E7DF4EA0">
         <File Id="svga_zlib1.dll64"
               Name="zlib1.dll"
               KeyPath="yes"
               Source="$(var.CONAN_ZLIB_ROOT)\zlib1.dll">
            <CopyFile Id="dupSvgadevtapZlib.dll"
                      DestinationDirectory="BIN"
                      DestinationName="zlib1.dll" />
         </File>
      </Component>

      <Component Id="_64_svga_libpng16.dll" Guid="25B08A81-F175-4525-8EAA-6AB5514B2722">
         <File Id="svga_libpng16.dll64"
               Name="libpng16.dll"
               KeyPath="yes"
               Source="$(var.CONAN_LIBPNG_ROOT)\libpng16.dll">
            <CopyFile Id="dupSvgadevtapLibpng.dll"
                      DestinationDirectory="BIN"
                      DestinationName="libpng16.dll" />
         </File>
      </Component>

      <Component Id="_64_ICuiSDK64.dll" Guid="585A485A-33C9-4E44-8349-82EF12E26933">
         <File Source="$(var.StageDir)\ICuiSDK64.dll" KeyPath="yes" />
      </Component>

      <Component Id="_64_libmfxsw64.dll" Guid="9DE5A197-3327-4CA3-A6CB-6C2CD995F1EB">
         <File Source="$(var.StageDir)\libmfxsw64.dll" KeyPath="yes" />
      </Component>

      <Component Id="_64_pcoip_agent_win64.dll" Guid="C784D205-FBAA-40BB-A11F-1D7DFF582179">
         <Condition>Not VDM_IP_PROTOCOL_USAGE="Dual4" And Not VDM_IP_PROTOCOL_USAGE="Dual6"</Condition>
         <File Id="pcoip_agent_win64.dll64"
               Name="pcoip_agent_win64.dll"
               KeyPath="yes"
               Source="$(var.GOBUILD_PCOIP_SOFT_SERVER_ROOT)\win64\agent\pcoip_agent_win64.dll"/>
         <RegistryValue Id="AgentDLL64"
                        Root="HKLM"
                        Key="SOFTWARE\Teradici\PCoIP"
                        Name="AgentDLL"
                        Value="[TERADICI_PCOIP_SERVER_X64]pcoip_agent_win64.dll"
                        Type="string" />
      </Component>

      <Component Id="_64_pcoip_perf_provider64.dll" Guid="6329C2B4-E8D3-485A-A017-8C799C3461F0">
         <File Id="pcoip_perf_provider64.dll64"
               Name="pcoip_perf_provider64.dll"
               KeyPath="yes"
               Source="$(var.GOBUILD_PCOIP_SOFT_SERVER_ROOT)\win64\agent\pcoip_perf_provider64.dll"/>
         <ServiceControl Id="scPerfHost"
                         Name="perfhost"
                         Start="both"
                         Stop="both"
                         Wait="no" />
      </Component>

      <Component Id="_pcoip_vchan_path.reg" Guid="A55DC7C7-2057-4DA8-B59A-2C3491F0F294">
         <RegistryValue Id="PCoIPVChanPathId"
                        Root="HKLM"
                        Key="SOFTWARE\Teradici\PCoIP"
                        Name="PCoIPVChanPath"
                        Value="[TERADICI_PCOIP_SERVER]"
                        Type="string" />
      </Component>
   </DirectoryRef>


   <DirectoryRef Id="SYSTEM_FOLDER">
      <Component Id="_vdp_rdpvcbridge.dll" Guid="A0DAEECC-B11B-428F-B2C3-4A6D02B92F61">
         <File Source="$(var.StageDir)\vdp_rdpvcbridge.dll"
               KeyPath="yes" />
      </Component>

     <Component Id="_hznsci.dll" Guid="{8B17CE36-1646-451F-AD7C-6BD06BFF2C60}">
       <File Id="hznsci.dll"
             Name="hznsci.dll"
             KeyPath="yes"
             Source="$(var.StageDir)\hznsci.dll"/>
     </Component>

     <Component Id="_certStoreIntercept.dll" Guid="7B331801-E41D-46DC-8A47-C7D67BCBE33A">
       <File KeyPath="yes"
             Source="$(var.StageDir)\certStoreIntercept.dll"/>
     </Component>

      <!-- This preserves the agent identity over a silent upgrade with RDSH
           in the future this could become the normal case -->
      <Component Id="_nm_registry_update_preserve" Guid="E4FE9E21-249E-45F4-A638-30CEDE9F4CE3" >
         <Condition>VDM_SKIP_ADD_LDAP_MACHINE_ENTRY</Condition>
         <RegistryValue Id="nmAgentIdentity"
                        Root="HKLM"
                        Key="SOFTWARE\Omnissa\Horizon\Node Manager"
                        Name="Agent Identity"
                        Value="[NM_AGENT_IDENTITY]"
                        Type="string" />
         <RegistryValue Id="nmAgentPublicKey"
                        Root="HKLM"
                        Key="SOFTWARE\Omnissa\Horizon\Node Manager"
                        Name="Agent Public Key"
                        Value="[NM_AGENT_PUBLIC_KEY]"
                        Type="string" />
         <RegistryValue Id="nmAgentPrivateKey"
                        Root="HKLM"
                        Key="SOFTWARE\Omnissa\Horizon\Node Manager"
                        Name="Agent Private Key"
                        Value="[NM_AGENT_PRIVATE_KEY]"
                        Type="string" />
         <RegistryValue Id="nmAgentKeyReference"
                        Root="HKLM"
                        Key="SOFTWARE\Omnissa\Horizon\Node Manager"
                        Name="Agent Key Reference"
                        Value="[NM_AGENT_KEY_REFERENCE]"
                        Type="string" />
         <RegistryValue Id="nmServerDN"
                        Root="HKLM"
                        Key="SOFTWARE\Omnissa\Horizon\Node Manager"
                        Name="Server DN"
                        Value="[NM_SERVER_DN]"
                        Type="string" />
         <RegistryValue Id="nmBrokerPublicKey"
                        Root="HKLM"
                        Key="SOFTWARE\Omnissa\Horizon\Node Manager"
                        Name="Broker Public Key"
                        Value="[NM_BROKER_PUBLIC_KEY]"
                        Type="string" />
         <RegistryValue Id="nmMsMode"
                        Root="HKLM"
                        Key="SOFTWARE\Omnissa\Horizon\Node Manager"
                        Name="MS Mode"
                        Value="[NM_MS_MODE]"
                        Type="string" />
         <RegistryValue Id="nmDisconnectLimitMinutes"
                        Root="HKLM"
                        Key="SOFTWARE\Omnissa\Horizon\Node Manager"
                        Name="DisconnectLimitMinutes"
                        Value="[NM_DISCONNECT_LIMIT_MINUTES]"
                        Type="string" />
         <RegistryValue Id="nmIdleLimitMinutes"
                        Root="HKLM"
                        Key="SOFTWARE\Omnissa\Horizon\Node Manager"
                        Name="IdleLimitMinutes"
                        Value="[NM_IDLE_LIMIT_MINUTES]"
                        Type="string" />
      </Component>
   </DirectoryRef>


   <DirectoryRef Id="HORIZON_REMOTE_EXPERIENCE_X64">
      <Component Id="_whfbRedirection.dll" Guid="57423E54-88CE-45C4-9155-0C89D116927F">
         <File Source="$(var.StageDir)\whfbRedirection.dll" KeyPath="yes" />
      </Component>
      <Component Id="_ipclibRpc_dynamic_md.dll" Guid="BCD5C05B-C525-4792-8148-55AA13D0AE8A">
         <File Source="$(var.StageDir)\ipclibRpc-dynamic-md.dll" KeyPath="yes" />
      </Component>
   </DirectoryRef>


   <DirectoryRef Id="SYSTEM_FOLDER64">
      <Component Id="_32_vdp_rdpvcbridge.dll" Guid="6BFDE225-3EC7-4510-B16E-F857EAC5FAB3" Transitive="yes" Win64="no">
         <File Id="vdp_rdpvcbridge.dll32"
               Name="vdp_rdpvcbridge.dll"
               KeyPath="yes"
               Source="$(var.Stage32Dir)\vdp_rdpvcbridge.dll" />
      </Component>

      <Component Id="_32_hznsci.dll" Guid="{0A50FB6E-A8AA-4BC8-86D7-1EA1D608883B}" Win64="no">
         <File Id="hznsci.dll32"
               Name="hznsci.dll"
               KeyPath="yes"
               Source="$(var.Stage32Dir)\hznsci.dll"/>
      </Component>
   </DirectoryRef>

   <!--  Used for VMware Integrated Scanner Redirection for Mac -->
   <DirectoryRef Id="VIEW_SCANNER_REDIR">
      <Component Id="_32_scannerredirhorizonds.dll" Guid="34E3EB1E-2102-4EAF-B619-F197E62D9B73" Win64="no">
         <File Id="scannerRedirHorizonds.dll32"
               Name="scannerredir32.ds"
               KeyPath="yes"
               Source="$(var.Stage32Dir)\scannerredirHorizonds.dll" />
      </Component>
   </DirectoryRef>

   <DirectoryRef Id="VIEW_SCANNER_REDIR64">
      <Component Id="_64_scannerredirhorizonds.dll" Guid="C5FA5117-A9CB-4A03-9E72-471079B4F6B3">
         <File Id="scannerRedirHorizonds.dll64"
               Name="scannerredir64.ds"
               KeyPath="yes"
               Source="$(var.StageDir)\scannerredirHorizonds.dll" />
      </Component>
   </DirectoryRef>

   <DirectoryRef Id="HORIZON_REMOTE_EXPERIENCE_UPDATETOOL">
      <Component Id="_64_horizon_updatetool.exe" Guid="E0E68DFF-1117-42A2-9D04-2530FE98EF44">
         <File Source="$(var.StageDir)\horizon-updatetool.exe"
               KeyPath="yes" />
         <RegistryValue Id="UpdateToolPath"
                        Root="HKLM"
                        Key="SOFTWARE\Omnissa\Horizon\Agent\Update"
                        Name="UpdateToolPath"
                        Value="[HORIZON_REMOTE_EXPERIENCE_UPDATETOOL]horizon-updatetool.exe"
                        Type="string" />
      </Component>

      <Component Id="_64_cdsclient.dll" Guid="2B2EB22F-7E36-413C-B1D1-C2473AADA7FF">
         <File Source="$(var.StageDir)\cdsclient.dll"
               KeyPath="yes" />
      </Component>

      <Component Id="_64_iconv.dll" Guid="D5446E67-A37C-4F1A-9424-CCFE62209B93">
         <File Id="_iconv_2_dll" Source="$(var.CONAN_LIBICONV_ROOT)\iconv-2.dll"
               Name="iconv-2.dll"
               KeyPath="yes" />
      </Component>

      <Component Id="_64_libcds.dll" Guid="30573380-C7A2-4223-A2C0-B127745C12D6">
         <File Source="$(var.StageDir)\libcds.dll"
               KeyPath="yes" />
      </Component>

      <Component Id="_64_libcurl.dll" Guid="A9B1C346-89D1-4080-AE5F-09E666C32018">
         <File KeyPath="yes"
               Source="$(var.CONAN_CURL_ROOT)\libcurl.dll" />
      </Component>

      <Component Id="_64_libxml2.dll" Guid="B7C93F4C-513F-41DB-91BD-7B0207F7960E">
         <File KeyPath="yes"
               Source="$(var.CONAN_LIBXML2_ROOT)\libxml2.dll" />
      </Component>

      <Component Id="_64_zlib1.dll" Guid="EBE40461-D4CB-4D11-B2C3-38A8A27B2454">
         <File Source="$(var.CONAN_ZLIB_ROOT)\zlib1.dll"
               KeyPath="yes" />
      </Component>

      <Component Id="_64_glib_2.0.dll" Guid="FA4141F1-707F-486E-85D0-8B850B181976">
         <File Source="$(var.CONAN_GLIB_ROOT)\glib-2.0-0.dll"
               KeyPath="yes" />
      </Component>

      <Component Id="_64_intl.dll" Guid="85327688-2B72-4EB4-9512-1647FD11CB0E">
         <File Source="$(var.CONAN_GETTEXT_ROOT)\intl-8.dll"
               KeyPath="yes" />
      </Component>

      <Component Id="_64_pcre2_8.dll" Guid="2EFB9FEF-2FED-47D6-B82F-D1C002B71C7A">
         <File Source="$(var.CONAN_PCRE2_ROOT)\pcre2-8.dll"
               KeyPath="yes" />
      </Component>

      <?if $(var.SSL_VERSION) = "3.0" ?>
         <Component Id="_64_libcrypto_3.dll" Guid="3E8698DC-A461-442C-9021-487024E1CA4A">
            <File Id="libcrypto_3_x64.dll" Source="$(var.StageDir)\libcrypto-3-x64.dll"
                  KeyPath="yes" />
         </Component>

         <Component Id="_64_libssl_3.dll" Guid="F64754E3-D4A5-46CB-A1D7-0CC87C304BA9">
            <File Id="libssl_3_x64.dll" Source="$(var.StageDir)\libssl-3-x64.dll"
                  KeyPath="yes" />
         </Component>

         <Component Id="_64_fips.dll" Guid="83348B25-83C7-4A3B-A6E9-6DD889387101">
            <File Id="fips_x64.dll" Source="$(var.StageDir)\fips.dll"
                  KeyPath="yes" />
         </Component>
      <?else?>
         <Component Id="_64_libeay32.dll" Guid="9E748B3C-8F04-405D-AFB7-602E94E5E62A">
            <File Id="libeay32_x64.dll" Source="$(var.StageDir)\libeay32.dll"
                  KeyPath="yes" />
         </Component>

         <Component Id="_64_ssleay32.dll" Guid="77B8A2BE-610C-4BC8-93A0-959ED9BF106C">
            <File Id="ssleay32_x64.dll" Source="$(var.StageDir)\ssleay32.dll"
                  KeyPath="yes" />
         </Component>
      <?endif?>
   </DirectoryRef>

   <DirectoryRef Id="HORIZON_REMOTE_EXPERIENCE">
      <Component Id="_hzipc_x86.dll" Guid="1D40A1C7-20BD-46F8-98A6-B64B7127DE02">
         <File Id="hzipc_x86.dll" Source="$(var.Stage32Dir)\hzipc.dll"
               KeyPath="yes" />
      </Component>
   </DirectoryRef>

   <DirectoryRef Id="HORIZON_REMOTE_EXPERIENCE_X64">
      <Component Id="_hzipc_x64.dll" Guid="43D76799-B5A2-4FFA-AFB6-1DB7317CB9D4">
         <File Id="hzipc_x64.dll" Source="$(var.StageDir)\hzipc.dll"
               KeyPath="yes" />
      </Component>
      <Component Id="_html5NativeMessagingHost.exe" Guid="91D17FB9-99B5-4887-A0B8-C9FBA5610D1F">
         <File Id="html5mmrNativeMessagingHost.exe"
               Name="html5mmrNativeMessagingHost.exe"
               KeyPath="yes"
               Source="$(var.StageDir)\html5mmrNativeMessagingHost.exe" />
      </Component>

      <Component Id="_html5NativeMessagingHost_manifest.json" Guid="93AA27A7-82A7-43DE-A85F-B17F6D17AF45">
         <File Id="html5mmrNativeMessagingHost_manifest.json"
               Name="html5mmrNativeMessagingHost_manifest.json"
               Source="$(var.StageDir)\html5mmrNativeMessagingHost_manifest.json" />
         <RegistryKey Root="HKLM" Key="SOFTWARE\Google\Chrome\NativeMessagingHosts\com.horizon.html5mmr">
            <RegistryValue Type="string" Value="[#html5mmrNativeMessagingHost_manifest.json]" />
         </RegistryKey>
         <RegistryKey Root="HKLM" Key="SOFTWARE\Microsoft\Edge\NativeMessagingHosts\com.horizon.html5mmr">
              <RegistryValue Type="string" Value="[#html5mmrNativeMessagingHost_manifest.json]" />
         </RegistryKey>
      </Component>
      <Component Id="_omnrxgservice.exe" Guid="{7E86FBC0-CAC9-42F5-96AA-D982EF91ED3E}">
         <File KeyPath="yes"
               Source="$(var.StageDir)\omnrxgservice.exe" />
               <ServiceInstall Id="omnrxgservice"
                               Type="ownProcess"
                               Name="omnrxgservice"
                               DisplayName="!(loc.IDS_VMWRXG_SERVICE_DISPLAY_NAME)"
                               Start="auto"
                               ErrorControl="normal"
                               Account="LocalSystem"
                               Interactive="no"
                               Vital="yes"
                               Description="!(loc.IDS_VMWRXG_SERVICE_DESCRIPTION)"/>
               <ServiceControl Id="omnrxgservice"
                               Name="omnrxgservice"
                               Start="install"
                               Stop="both"
                               Remove="uninstall"
                               Wait="yes"/>
      </Component>

      <!--  Used for storage drive redirection -->
      <Component Id="_sdrserviceplugin.dll" Guid="{6B8FC01C-DB3B-45BB-BDA9-0682E821B374}">
         <File KeyPath="yes"
               Source="$(var.StageDir)\sdrserviceplugin.dll" />
         <RegistryKey Root="HKLM"
                      Key="Software\Omnissa\Horizon\Remote Experience Generic Service\Plugins\sdr">
            <RegistryValue Type="string"
                           Name="dll"
                           Value="[#sdrserviceplugin.dll]" />
         </RegistryKey>
      </Component>

      <Component Id="_sdrserverutil.exe" Guid="{CDF0D9CD-F2FE-459D-B829-5E2ABFC20B8B}">
         <File KeyPath="yes"
               Source="$(var.StageDir)\sdrserverutil.exe" />
      </Component>

      <Component Id="_sdr_sidechanneltype_reg" Guid="{CAF280B6-DAF1-423E-A05A-6EEDFDB15DC0}">
         <RegistryValue Id="sdr_sidechanneltype"
                        Root="HKLM"
                        Key="SOFTWARE\Omnissa\Horizon\SDRTrans"
                        Name="sideChannelType"
                        Value="vvc"
                        Type="string" />
      </Component>

      <!--  Used for VMware Integrated Scanner Redirection for Mac -->
      <Component Id="_scannerredirserviceplugin.dll" Guid="{EFC1DF8E-CB98-4B60-ACDB-26A0F24909E8}">
         <File KeyPath="yes"
               Source="$(var.StageDir)\scannerredirserviceplugin.dll" />
         <RegistryKey Root="HKLM"
                      Key="Software\Omnissa\Horizon\Remote Experience Generic Service\Plugins\scanner">
            <RegistryValue Type="string"
                           Name="dll"
                           Value="[#scannerredirserviceplugin.dll]" />
         </RegistryKey>
      </Component>

      <Component Id="_scannerredirtray.exe" Guid="{B6F16B8A-6EED-4CAC-9FDD-97E50BDAF5F7}">
         <File KeyPath="yes"
               Source="$(var.StageDir)\scannerRedirTray.exe" />
         <RegistryKey Root="HKLM"
                      Key="SOFTWARE\Omnissa\Horizon\UnityShell\Run">
            <RegistryValue Type="string"
                           Name="ScannerRedirectionForMac"
                           Value="[#scannerRedirTray.exe]" />
         </RegistryKey>
      </Component>
      
      <Component Id="_geoRedir_reg_install_64" Guid="{D4B7D155-F40C-4F50-973D-A32EB3D50151}">
         <RegistryKey Root="HKLM" Key="SOFTWARE\Omnissa\Horizon\GEOREDIR">
            <RegistryValue Type="integer" Name="installed" Value="1"/>
         </RegistryKey>
      </Component>      
   </DirectoryRef>

   <DirectoryRef Id="SYSTEM_REPL_IMPORT_SCRIPTS">
      <Component Id="_wslogonscriptlauncher.exe" Guid="68A88915-F730-4FC0-A80C-1D9E4923CF31">
         <File Source="$(var.StageDir)\wslogonscriptlauncher.exe" KeyPath="yes" />
         <RegistryValue Id="hybridLogonEnabledReg"
                        Root="HKLM"
                        Key="SOFTWARE\Omnissa\Horizon\Agent\HybridLogon"
                        Name="Enabled"
                        Value="true"
                        Type="string" />
      </Component>
   </DirectoryRef>
</Include>
