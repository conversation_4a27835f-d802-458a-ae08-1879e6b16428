# Copyright (c) Omnissa, LLC. All rights reserved.
# This product is protected by copyright and intellectual property laws in the
# United States and other countries as well as by international treaties.
# -- Omnissa Restricted

"""winhttpTests: Build file for testing the WinHTTP portion of wsnm_common


Maintainers: <EMAIL>

"""
import vmware


NODE_NAME = "winHttpTests"
WSNM_COMMON_NODE_NAME = "wsnm_common-static"

env = vmware.Host().DefaultEnvironment()
env.LoadTool(
    [
        "horizonUtils",
        "libssl",
        "msvcrt",
    ]
)
env.LoadTool("mfw", linkStatic=False, linkLocal=True)
env.LoadTool("jsoncpp", staticRuntime=False)
env.LoadTool("gtest", linkDynamic=True)


env.Append(
    LINKFLAGS=[
        "-subsystem:console",
    ],
    CPPDEFINES={
        "NOMINMAX": None,
        "UNICODE": None,
        "_UNICODE": None,
    },
    CPPPATH=[
        "#bora/apps/cedar/include",
        "#bora/apps/horizonAgent/lib/wsnm_common",
        "#bora/apps/horizonAgent/include",
        "#bora/public",
        "#bora/lib/public",
        "#bora/apps/horizonAgent/test/include",
    ],
    CCFLAGS=[
        # Disable Inline Function Expansion, so that we can
        # hook/mock under Release compilation
        "/Ob0",
        # A large number of test cases necessitates this flag
        "/bigobj",
    ],
    SHLIBFLAGS=[
        # Disable MSVC's ICF (Identical Code Folding) optimization
        # to prevent inline hook issues under Release compilation
        "/OPT:NOICF",
    ],
    LIBS=[
        "advapi32.lib",
        "crypt32.lib",
        "ncrypt.lib",
        "netapi32.lib",
        "ole32.lib",
        "winhttp.lib",
        # For OpenSSL
        "crypt32.lib",
        "oleaut32.lib",
        "user32.lib",
        "ncrypt.lib",
    ],
)

env["AGENT_TARGET"] = NODE_NAME

e = vmware.Executable(NODE_NAME, env=env)
e.GetEnv().AddRdeVersionInfo(e, NODE_NAME + ".exe", "Agent WinHttp Testing Executable")
e.addSubdirs(
    [
        "apps/horizonAgent/test/apps/winhttp",
    ]
)
e.addGlobalStaticLibs(
    [
        WSNM_COMMON_NODE_NAME,
    ]
)
e.addStaticLibs(
    "vmlibs",
    [
        "string",
        # For OpenSSL
        "config",
        "dict",
        "err",
        "file",
        "lock",
        "misc",
        "productState",
        "ssl",
        "unicode",
        "user",
        "uuid",
        "win32auth",
        "winregistry",
        "wmi",
    ],
)

node = e.createProgramNode()
vmware.RegisterNode(node, NODE_NAME)
vmware.Alias("%s-build" % NODE_NAME, node)
