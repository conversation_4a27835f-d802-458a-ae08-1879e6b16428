<?xml version="1.0" encoding="UTF-8"?>
<Wix xmlns="http://schemas.microsoft.com/wix/2006/wi"
     xmlns:util="http://schemas.microsoft.com/wix/UtilExtension"
     xmlns:bal="http://schemas.microsoft.com/wix/BalExtension"
     xmlns:netfx="http://schemas.microsoft.com/wix/NetFxExtension">

   <!-- https://docs.microsoft.com/en-us/dotnet/framework/migration-guide/how-to-determine-which-versions-are-installed -->
   <?define NetFxMinRelease = 378389 ?>
   <?define NetFxRedistLink = http://go.microsoft.com/fwlink/?LinkId=863265 ?>
   <?define NetFxEulaLink = NetfxLicense.rtf ?>

   <Bundle Name="Omnissa Horizon Client"
           Version="$(var.VIEWCLIENT_VERSION)"
           Manufacturer="Omnissa, LLC"
           UpgradeCode="80DDA089-E499-4962-9B6A-6A971A8B8fC9"
           IconSourceFile="..\..\..\..\view\OpenClient\win32\rc\horizon-client.ico"
           Condition="VersionNT > v6.0">
      <BootstrapperApplicationRef Id="ManagedBootstrapperApplicationHost">
         <Payload SourceFile="..\ViewClientBA\BootstrapperCore.config"/>
         <Payload SourceFile="$(var.VIEWCLIENTBA_OUTPUT_DIR)\ViewClientBA.dll"/>
         <?foreach lang in de;es;fr;ja;ko;zh-CN;zh-TW ?>
         <Payload Name='$(var.lang)\ViewClientBA.resources.dll'
                  SourceFile="$(var.VIEWCLIENTBA_OUTPUT_DIR)\$(var.lang)\ViewClientBA.resources.dll" />
         <?endforeach?>
         <Payload SourceFile="$(var.WIX_ROOT)\Microsoft.Deployment.WindowsInstaller.dll"/>
         <Payload SourceFile="$(var.WIX_ROOT)\wix.dll"/>
         <Payload SourceFile="$(var.WIX_ROOT)\winterop.dll"/>
         <Payload SourceFile="$(var.SRCROOT)\install\windows\ViewClientBA\ViewClientBA\Resources\$(var.NetFxEulaLink)"/>
      </BootstrapperApplicationRef>

      <!-- Bootstrapper variables used internally -->
      <Variable Name="DefaultFolder" Type="string" Value="Omnissa\Omnissa Horizon Client"/>
      <Variable Name="ProgramName" Type="string" Value="horizon-client.exe"/>
      <Variable Name="YYMMVersion" Type="string" Value="$(var.VIEWCLIENT_YYMM_VERSION)"/>

      <!-- Optional command line property that can be passed to the MSIs -->
      <Variable Name="VDM_INSTALLER_CHECKS" bal:Overridable="yes" Persisted="yes" />
      <Variable Name="DESKTOP_SHORTCUT" bal:Overridable="yes" Persisted="yes" Type="numeric" Value="1" />
      <Variable Name="STARTMENU_SHORTCUT" bal:Overridable="yes" Persisted="yes" Type="numeric" Value="1" />
      <Variable Name="AUTO_UPDATE_ENABLED" bal:Overridable="yes" Persisted="yes" Type="string" Value="true" />
      <Variable Name="DOTNET_RUNTIME_PATCH" bal:Overridable="yes" Persisted="yes" Type="numeric" Value="-1" />
      <Variable Name="SKIP_DOTNET_RUNTIME_INSTALL" bal:Overridable="yes" Persisted="yes" Type="string" Value="0" />
      <Variable Name="INSTALL_DEEM_AGENT" bal:Overridable="yes" Persisted="yes" Type="string" Value="1" />

      <util:RegistrySearchRef Id="NETFRAMEWORK45"/>

      <Chain>
         <!--.NET requirement. Check the NetFxExtension for more information-->
         <PackageGroupRef Id="NetFxRedist"/>

         <PackageGroupRef Id="DotNetWindowsDesktopRuntime" />

         <?if $(var.ConfigurationType) = "Debug"?>
            <!-- The debug/obj runtime -->
            <PackageGroupRef Id="VC_14_NonRedist"/>
         <?else?>
            <!-- C-runtime libraries -->
            <PackageGroupRef Id="VC_14_Redist"/>
         <?endif?>

         <PackageGroupRef Id="Etlm" />

         <!-- Client MSI package -->
         <PackageGroupRef Id="ViewClientMSIs"/>

         <?if $(var.IsBuildOpt) = False And $(var.BuildType) = "Nobeta" And $(var.Configuration) = "Release"?>
         <!-- Horizon HTML5MMR -->
            <PackageGroupRef Id="HTML5MMRMSIs"/>
         <?endif?>

          <!-- Horizon Media Optimization for Teams Redirection
          - As the Teams feature is always included and no longer selectable,
          - the TeamsRedir MSM is no longer built.  It is included directly
          - in the Horizon Client installer.
          <PackageGroupRef Id="TEAMSREDIRMSIs"/>
          -->

      </Chain>
   </Bundle>

   <Fragment>
      <PayloadGroup Id="ViewClientPayload_x64">
         <Payload Id="x64CoreCab" SourceFile="$(var.DEST)\x64\Core.cab" />
         <Payload Id="x64ClientCab" SourceFile="$(var.DEST)\x64\Components.cab" />
         <Payload Id="x64RMKSCab" SourceFile="$(var.DEST)\x64\RMKSComponents.cab" />
      </PayloadGroup>

      <PackageGroup Id="ViewClientMSIs">
         <!--x64 Package-->
         <MsiPackage Id="ViewClientx64"
                     Name="Omnissa Horizon Client (x64).msi"
                     SourceFile="$(var.DEST)\x64\$(var.VIEWCLIENT_NAME64).msi"
                     Cache="yes"
                     Compressed="yes"
                     EnableFeatureSelection="yes"
                     InstallCondition="VersionNT64">
            <!--Assign variables to MSI's properties for the burn engine-->
            <MsiProperty Name="INSTALLDIR" Value="[INSTALLDIR]"/>
            <MsiProperty Name="VIEW_CLIENT_INSTALL_DIR" Value="[INSTALLDIR]"/>
            <MsiProperty Name="VDM_IP_PROTOCOL_USAGE" Value="[VDM_IP_PROTOCOL_USAGE]"/>
            <MsiProperty Name="VDM_SERVER" Value="[VDM_SERVER]"/>
            <MsiProperty Name="LOGINASCURRENTUSER_DISPLAY" Value="[LOGINASCURRENTUSER_DISPLAY]"/>
            <MsiProperty Name="LOGINASCURRENTUSER_DEFAULT" Value="[LOGINASCURRENTUSER_DEFAULT]"/>
            <MsiProperty Name="VDM_FIPS_ENABLED" Value="[VDM_FIPS_ENABLED]"/>
            <MsiProperty Name="URL_FILTERING_ENABLED" Value="[URL_FILTERING_ENABLED]"/>
            <MsiProperty Name="ENABLE_UNC_REDIRECTION" Value="[ENABLE_UNC_REDIRECTION]"/>
            <MsiProperty Name="VDM_INSTALLER_CHECKS" Value="[VDM_INSTALLER_CHECKS]"/>
            <MsiProperty Name="DESKTOP_SHORTCUT" Value="[DESKTOP_SHORTCUT]"/>
            <MsiProperty Name="STARTMENU_SHORTCUT" Value="[STARTMENU_SHORTCUT]"/>
            <MsiProperty Name="KEYLOGGER_BLOCKING_SUPPORTED" Value="[KEYLOGGER_BLOCKING_SUPPORTED]"/>
            <MsiProperty Name="AUTO_UPDATE_ENABLED" Value="[AUTO_UPDATE_ENABLED]"/>
            <PayloadGroupRef Id="ViewClientPayload_x64" />
         </MsiPackage>
      </PackageGroup>
   </Fragment>

   <?if $(var.IsBuildOpt) = False And $(var.BuildType) = "Nobeta" And $(var.Configuration) = "Release"?>
      <Fragment>
         <PackageGroup Id="HTML5MMRMSIs">
            <MsiPackage Id="HTML5MMRx64"
                        Name="Horizon HTML5 MMR (x64).msi"
                        SourceFile="$(var.DEST)\Horizon_Html5mmr_x64.msi"
                        Cache="yes"
                        Compressed="yes"
                        InstallCondition="VersionNT64">
               <MsiProperty Name="VIEW_CLIENT_INSTALL_DIR" Value="[INSTALLDIR]"/>
            </MsiPackage>
         </PackageGroup>
      </Fragment>
   <?endif?>

    <Fragment>
        <PackageGroup Id="TEAMSREDIRMSIs">
            <MsiPackage Id="TEAMSREDIRx64"
                        Name="Horizon Media Optimization for Microsoft Teams (x64).msi"
                        SourceFile="$(var.DEST)\Horizon_TeamsRedir_x64.msi"
                        Cache="yes"
                        Compressed="yes"
                        InstallCondition="VersionNT64">
                <MsiProperty Name="VIEW_CLIENT_INSTALL_DIR" Value="[INSTALLDIR]"/>
            </MsiPackage>
        </PackageGroup>
    </Fragment>

   <Fragment>
         <?define InstallDeemAgentCondition = "INSTALL_DEEM_AGENT = 1"?>
         <util:RegistrySearch Id="DeemAgentVersionIntegrated"
                              Variable="VersionIntegrated"
                              Result="value"
                              Root="HKLM"
                              Key="SOFTWARE\WorkspaceONE\Endpoint Telemetry\Installer\Integration"
                              Value="HorizonClient"
                              Win64="yes"/>
        <PackageGroup Id="Etlm">
            <ExePackage Id="Etlm"
                        Cache="yes"
                        Compressed="yes"
                        PerMachine="yes"
                        Permanent="no"
                        DetectCondition="VersionIntegrated = &quot;$(var.ETLM_INSTALLER_FULL_VERSION)&quot;"
                        InstallCondition="VersionNT64 And $(var.InstallDeemAgentCondition)"
                        Vital="no"
                        SourceFile="$(var.GITHUB_EUC_TLM_SDK_ROOT)\Omnissa-Telemetry-Agent.exe"
                        InstallCommand="/install /silent /v INTEGRATION=HorizonClient ARPSYSTEMCOMPONENT=1 REBOOT=ReallySuppress"
                        UninstallCommand="/uninstall /silent /v INTEGRATION=HorizonClient REBOOT=ReallySuppress"
                        RepairCommand="/repair /silent /v INTEGRATION=HorizonClient ARPSYSTEMCOMPONENT=1 REBOOT=ReallySuppress">
               <ExitCode Value="3010" Behavior="scheduleReboot" />
               <ExitCode Value="1641" Behavior="scheduleReboot" />
            </ExePackage>
        </PackageGroup>
    </Fragment>

   <!-- .NET redist -->
   <Fragment>
      <util:RegistrySearchRef Id="NETFRAMEWORK45"/>

      <WixVariable Id="WixMbaPrereqPackageId" Value="NetFxRedist" />
      <WixVariable Id="WixMbaPrereqLicenseUrl" Value="$(var.NetFxEulaLink)" />
      <WixVariable Id="PreqbaThemeXml" Value="ClientBootstrapperHelp.xml" />
      <WixVariable Id="PreqbaThemeWxl" Value="ClientBootstrapperHelp.wxl" />
      <WixVariable Id="NetFxDetectCondition" Value="NETFRAMEWORK45 &gt;= $(var.NetFxMinRelease)" Overridable="yes" />
      <WixVariable Id="NetFxInstallCondition" Value="" Overridable="yes" />
      <WixVariable Id="NetFxPackageDirectory" Value="redist\" Overridable="yes" />

      <PackageGroup Id="NetFxRedist">
         <ExePackage
               InstallCommand="/q /norestart /ChainingPackage &quot;[WixBundleName]&quot; /log &quot;[NetFxFullLog].html&quot;"
               PerMachine="yes"
               DetectCondition="!(wix.NetFxDetectCondition)"
               InstallCondition="!(wix.NetFxInstallCondition)"
               Id="NetFxRedist"
               Vital="yes"
               Permanent="yes"
               Protocol="netfx4"
               DownloadUrl="$(var.NetFxRedistLink)"
               LogPathVariable="NetFxFullLog"
               Compressed="no"
               Name="!(wix.NetFxPackageDirectory)NDP472-*********-x86-x64-AllOS-ENU.exe">
            <RemotePayload
               CertificatePublicKey="C090C1A2CAFA9B967D9C87C7FE02F7C01FBDE4F2"
               CertificateThumbprint="5EAD300DC7E4D637948ECB0ED829A072BD152E17"
               Description="Microsoft .NET Framework 4.7.2 Setup"
               Hash="31FC0D305A6F651C9E892C98EB10997AE885EB1E"
               ProductName="Microsoft .NET Framework 4.7.2"
               Size="83943272"
               Version="4.7.3081.0" />
         </ExePackage>
      </PackageGroup>
   </Fragment>

  <Fragment>
      <WixVariable Id="DotNetRuntimeDetectCondition" Value="DOTNET_RUNTIME_PATCH &gt;= 7" Overridable="yes"/>
      <?define SkipDotNetRuntimeInstallCondition = "SKIP_DOTNET_RUNTIME_INSTALL = 1"?>

      <PackageGroup Id="DotNetWindowsDesktopRuntime">
         <ExePackage Id="DotNetWindowsDesktopRuntime_x64"
                     InstallCommand="/install /quiet /norestart /log &quot;[DotNetRuntimeLog]&quot;"
                     RepairCommand="/repair /quiet /norestart /log &quot;[DotNetRuntimeLog]&quot;"
                     PerMachine="yes"
                     DetectCondition="!(wix.DotNetRuntimeDetectCondition)"
                     InstallCondition="VersionNT64 And NOT $(var.SkipDotNetRuntimeInstallCondition)"
                     Vital="no"
                     Permanent="yes"
                     Protocol="burn"
                     LogPathVariable="DotNetRuntimeLog"
                     Compressed="yes"
                     SourceFile="$(var.CONAN_DOTNET_REDISTS_ROOT)\win\redist\windowsdesktop-runtime-$(var.CONAN_DOTNET_REDISTS_VERSION)-win-x64.exe">
            <!-- Ignore newer version installed error from vcredist installer -->
            <ExitCode Value="1638" Behavior="success" />
         </ExePackage>
      </PackageGroup>
   </Fragment>

   <!-- global defines-->
   <Fragment>
      <?define ProductInstalled=5?>
   </Fragment>

   <?if $(var.ConfigurationType) = "Debug"?>
      <!-- The debug/obj runtime -->
      <Fragment>
         <PackageGroup Id="VC_14_NonRedist">
            <ExePackage Id="vc_14_nonredist_x64" Cache="no" Compressed="yes" PerMachine="yes" Permanent="yes" Vital="no"
                        SourceFile="$(var.CONAN_MSVC_DEBUG_RUNTIME_INSTALLER_ROOT)\win64\vc140_nonredists_release_and_debug_x64.exe"
                        InstallCommand="/install /quiet /norestart"
                        InstallCondition="VersionNT64">
            </ExePackage>
         </PackageGroup>
      </Fragment>
   <?else?>
      <!-- VC 2022 release runtime redists -->
      <Fragment>
         <!-- Install on 64bit OS -->
         <PackageGroup Id="VC_14_Redist">
            <ExePackage Id="vc_14_redist_x64" Cache="no" Compressed="yes" PerMachine="yes" Permanent="yes" Vital="yes"
                        SourceFile="$(var.CONAN_MSVC_REDISTS_ROOT)\win\exe\1033\vcredist_x64.exe"
                        InstallCommand="/install /quiet /norestart"
                        InstallCondition="VersionNT64">
               <!-- Ignore newer version installed error from vcredist installer -->
               <ExitCode Value="1638" Behavior="success" />
            </ExePackage>
         </PackageGroup>
      </Fragment>
   <?endif?>

</Wix>
