import vmware
import os

envCommon = vmware.Host().DefaultEnvironment()

objMainVMString = os.path.join(
    vmware.Host().ComponentBuildPath("omnissastring"),
    "SUBDIRS/bora/apps/vmappsdkWin32/vmstring",
    "dummy.obj",
)

vmware.LoadTool(envCommon, ["libglibmm", "libglib2", "atlmfc", "msvcrt"])

envCommon.Prepend(
    CPPDEFINES={
        "USERLEVEL": None,
        "_WINDOWS": None,
        "UNICODE": None,
        "_UNICODE": None,
        "_WTL_NO_WTYPES": None,
        "_WTL_NO_CSTRING": None,
    },
    CPPPATH=[
        "#bora/public",
        "#bora/lib/public",
        "#bora/apps/lib",
    ],
)

envDLL = envCommon.Clone()
envStaticLib = envCommon.Clone()

envDLL.Append(
    CPPDEFINES={
        "_USRDLL": None,
        "VMSTRING_EXPORT_SOURCE": None,
        "VMX86_IMPORT_DLLDATA": None,
    },
    SHLIBFLAGS=[
        "-subsystem:windows",
        "-incremental:no",
    ],
    CPPPATH=[
        "#bora/apps/vmappsdkWin32/vmstring",
    ],
    LIBS=[
        "kernel32.lib",
        "user32.lib",
        "gdi32.lib",
        "Ws2_32.lib",
        "oldnames.lib",
        "comsuppw.lib",
        "oleaut32.lib",
        vmware.FileAbsPath(objMainVMString),
    ],
)

envStaticLib.Append(
    CPPDEFINES={
        "VMSTRING_EXPORT": '""',
    }
)

exeDLL = vmware.Executable("omnissastring", archiveSubdirs=True, env=envDLL)
exeStaticLib = vmware.Executable("omnissastring-static", env=envStaticLib)

exeDLL.addSharedLibs(["omnissabaselib"])
exeDLL.addResources(envDLL, ["#bora/apps/vmappsdkWin32/vmstring/vmstring.rc"])

dllSubdirNodes = exeDLL.addSubdirs(["apps/lib/stringxx", "apps/vmappsdkWin32/vmstring"])
exeStaticLib.addSubdirs(["apps/lib/stringxx"])

# subdirs linked as libraries, so run gen def script to create .def file
defGenNode = exeDLL.createDefFile(
    envCommon, "#bora/apps/vmappsdkWin32/vmstring/vmstring.def", dllSubdirNodes
)
exeDLL.addDefFile(defGenNode[0])

nodeDLL = exeDLL.createSharedLibraryNode(implib=True, export=True)
nodeStaticLib = exeStaticLib.createStaticLibraryNode(export=True)

# Note: crtbora wants both the dll and the lib in the registered node
# so it can know how to stage the lib file.
vmware.RegisterNode(nodeDLL, "omnissastring")
vmware.RegisterNode(nodeStaticLib, "omnissastring-static")

vmware.Alias("omnissastring-build", nodeDLL + nodeStaticLib)
