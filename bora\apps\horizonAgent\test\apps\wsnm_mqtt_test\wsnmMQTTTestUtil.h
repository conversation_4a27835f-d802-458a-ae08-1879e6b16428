/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * wsnmMQTTTestUtil.h
 *
 *      Main header file for wsnm_mqttTest.
 *      Handles the environment setup and behaviour of the test cases.
 */

#pragma once

// Gtest dependencies
#include <gmock/gmock.h>
#include <gtest/gtest.h>
#include <windows.h>

#include <utils/HzRegUtils.h>

// wsnm_mqtt dependencies
#include <stdafx.h>
#include <TlHelp32.h>
#include <mqttManager.h>
#include <mqttManagerUtil.h>
#include <mqttConfig.h>
#include <wsnm_mqtt.h>
#include <certMonitor.h>
#include <templateHandler.h>
#include "utMock.h"
#include "gmock-helpers.h"
#include "mfw-helpers.h"
#include "mocks/mockFile.h"
#include "mocks/mockDirectory.h"
#include "mocks/mockHttpRequest.h"

#include "utilCorestring.h"
#include "utilFile.h"
#include "TestLaunchProcess.h"


// Use do-while to prevent redefinition errors
#define READ_FILE_INTO_STRING(filePath, stringName, binary)                                        \
   do {                                                                                            \
      WinFile myfile(filePath);                                                                    \
      stringName = myfile.ReadFileIntoString(binary);                                              \
      EXPECT_TRUE(!stringName.empty());                                                            \
   } while (0);

// Misc utils
#include <winregistry.h>
#include <ssl.h>


using namespace ::testing;

constexpr const char *uemCommand = R"({
   "uemEnrollment" : {
      "serverName": "John Doe",
      "ogName": "vlabsmdm",
      "userName": "astart",
      "password": "Vlabs12#"
   },
   "METADATA" : {
      "AGENTCOMMAND" : "HandleCustomization"
   },
   "COMMAND" : "HandleCustomization",
   "ID" : "uem-test-id"
   })";

#define MQTT_CONFIG_PATH HORIZON_VDM_REG_ROOT_A "\\Node Manager"
#define MQTT_CONFIG_IPC_INCLUDE_PATH HORIZON_VDM_REG_ROOT_A "\\Node Manager\\ipcincludelist"


/*
 * Use VMOCK_V to avoid redefinition errors. VMOCK names the mock object based
 * on the line number where it is declared. Since we're using macros, they all
 * share the same line number.
 */
#define MOCK_MFW_REG_WITH_RESTARTS(restarts, disconnects)                                          \
   VMOCK_V(RegisterMsgTransportMock, (FPSendMsg) & MessageFrameWork::SendMsg)                      \
      .ExpectCall(StrEq(QUEUE_DESKTOPMANAGER), StrEq(L"NotifyQueueRegisterMessageTransport"),      \
                  GMOCK_BLANK_ARG12)                                                               \
      .Times(restarts)                                                                             \
      .WillRepeatedly(Return(CORE::MessageHandler::respType::MsgOk));                              \
   VMOCK_V(QueueRemoveMock, (FPSendMsg) & MessageFrameWork::SendMsg)                               \
      .ExpectCall(StrEq(QUEUE_DESKTOPMANAGER), StrEq(L"NotifyQueueRemove"), GMOCK_BLANK_ARG12)     \
      .Times(restarts)                                                                             \
      .WillRepeatedly(Return(CORE::MessageHandler::respType::MsgOk));                              \
   VMOCK_V(SetSSOSettingsMock, (FPSendMsg) & MessageFrameWork::SendMsg)                            \
      .ExpectCall(StrEq(QUEUE_DESKTOPMANAGER), StrEq(L"SetSSOv2Settings"), GMOCK_BLANK_ARG12)      \
      .Times(disconnects)                                                                          \
      .WillRepeatedly(Return(CORE::MessageHandler::respType::MsgOk));

#define MOCK_MFW_REGISTRATION(n) MOCK_MFW_REG_WITH_RESTARTS(1, n)

#define MOCK_EVENT_LOGGER(n)                                                                       \
   PropertyBag mock_getUserComputerNamesResponse;                                                  \
   mock_getUserComputerNamesResponse.set(L"ComputerNameNetBIOS", L"vloz-machine");                 \
   VMOCK_V(GetUserComputerNamesMock, (FPSendMsg) & MessageFrameWork::SendMsg)                      \
      .ExpectCall(StrEq(QUEUE_WINAUTH), StrEq(L"getUserComputerNames"), GMOCK_BLANK_ARG12)         \
      .Times(::testing::Between(0, 1))                                                             \
      .WillRepeatedly(DoAll(SetArgReferee<3>(mock_getUserComputerNamesResponse),                   \
                            Return(CORE::MessageHandler::respType::MsgOk)));                       \
   VMOCK_V(WriteWindowsEventMock, (FPSendMsg) & MessageFrameWork::SendMsg)                         \
      .ExpectCall(StrEq(MESSAGE_FRAMEWORK_SYSTEM_QUEUE), StrEq(L"WriteWindowsEvent"),              \
                  GMOCK_BLANK_ARG12)                                                               \
      .Times(n)                                                                                    \
      .WillRepeatedly(Return(CORE::MessageHandler::respType::MsgOk));

#define MOCK_NETWORK_CRED_UPDATED(n)                                                               \
   VMOCK_V(NetworkCredentialsUpdatedMock, (FPPostMsg) & MessageFrameWork::PostMsg)                 \
      .ExpectCall(StrEq(QUEUE_DESKTOPMANAGER), StrEq(L"NetworkCredentialsUpdated"),                \
                  propertyNameEquals(L"forceReconnect", L"true"), GMOCK_BLANK_ARG4)                \
      .Times(n);

#define MOCK_UEM_INSTALLED()                                                                       \
   VMOCK(&UEMHandler::getAirwatchInstallDir).Will(Return(L"C:\\Path\\To\\Airwatch"));

#define MOCK_UEM_NOT_INSTALLED() VMOCK(&UEMHandler::getAirwatchInstallDir).Will(Return(L""));

#define MOCK_CUSTOM_ACTION_ATTEMPTED(key)                                                          \
   VMOCK_V(CustomActionAttemptedMock, (FPreadRegistry) & wstr::readRegistry)                       \
      .WillRepeatedly([&CustomActionAttemptedMock](const wchar_t *path, const wchar_t *defVal,     \
                                                   bool useWow6432) {                              \
         wstr regPath = path;                                                                      \
         if (regPath.findi(key) != -1)                                                             \
            return wstr(L"1");                                                                     \
         return CustomActionAttemptedMock.CallRealFunc(path, defVal, useWow6432);                  \
      });

#define MOCK_ON_PREM_AD_ATTEMPTED() MOCK_CUSTOM_ACTION_ATTEMPTED(AGENT_CONFIG_ONPREM_AD)

#define MOCK_WAIT_FOR_HYBRID_JOIN() MOCK_CUSTOM_ACTION_ATTEMPTED(AGENT_CONFIG_WAIT_FOR_HYBRID_JOIN)

/*****************************
 * Class MqttTestEnvironment
 *
 * To setup the base environment before starting test
 * could be used for global initializations of some values
 *
 * ****************************/

class MqttTestEnvironment : public Environment {
public:
   MqttTestEnvironment() {}
   ~MqttTestEnvironment() {}

   void SetUp() override { SSL_Init(NULL, NULL, NULL); }

   void TearDown() override { SSL_Exit(); }
};


/***********************************************
 * Class MqttTestFramework
 *
 * Base class to handle functionality that occurs within every test suite.
 *
 * TODO:: Add sysmsg construtor to either pass messages to the console or to the
 * logs
 *
 * *********************************************/

class MqttTestFramework {
public:
   MqttTestFramework()
   {
      mTestCase = UnitTest::GetInstance();
      std::string testSuitName(mTestCase->current_test_case()->name());
      std::string testCaseName(mTestCase->current_test_info()->name());
      mFullTestName = testSuitName + "." + testCaseName;
   }

   ~MqttTestFramework()
   {
      mTestCase = nullptr;
      mFullTestName = "";
   }

protected:
   void LogTestStart()
   {
      SYSMSG_FUNC(Debug, L"%s Test Start", util::corestr::stdStrToWstr(mFullTestName));
   }

   void LogTestResult()
   {
      std::string testEndSep = "\n================================";

      std::string testResult;
      if (Test::HasFailure()) {
         testResult = "FAILED" + testEndSep;
         SYSMSG_FUNC(Debug, L"%s Error: %s", util::corestr::stdStrToWstr(mFullTestName),
                     util::corestr::stdStrToWstr(testResult));
      } else {
         testResult = "PASSED" + testEndSep;
         SYSMSG_FUNC(Debug, L"%s Okay: %s", util::corestr::stdStrToWstr(mFullTestName),
                     util::corestr::stdStrToWstr(testResult));
      }
   }

   std::string GetTestNameA()
   {
      std::string testCaseName(mTestCase->current_test_case()->name());
      std::string testName(mTestCase->current_test_info()->name());
      return testCaseName + "." + testName;
   }

   std::wstring GetTestName() { return CORE::mstr::to_wstr(GetTestNameA().c_str()).c_str(); }

private:
   UnitTest *mTestCase;
   std::string mFullTestName;
};


/*************************************
 * class MqttTest
 *
 * Base class which is inherited by every  test to provide
 * functionality for each test
 * ***************************************/

class MqttTest : public Test, public MqttTestFramework {
public:
   MqttTest() { LogTestStart(); }
   ~MqttTest() { LogTestResult(); }
};


/*
 * class MqttTestUtil
 *
 *    Helpful utilities that can be used in all tests.
 */

class MqttTestUtil {
public:
   MqttTestUtil() {}
   ~MqttTestUtil() {}

protected:
   std::string mMQTTEndpointHost = "127.0.0.1";
   std::string mMQTTEndpointPort = "8883";

   std::wstring mConfigDir = (_wgetenv(L"config_dir") == NULL)
                                ? std::filesystem::current_path().wstring()
                                : _wgetenv(L"config_dir");
   std::string mConfigDirA = (getenv("config_dir") == NULL)
                                ? std::filesystem::current_path().string()
                                : getenv("config_dir");
   // The install directory of the mosquitto broker
   std::wstring mMosquittoDir = _wgetenv(L"mosquitto_dir");


   void StartApp() { gMqttConfig.init(); }

   void StopApp() { gMqttConfig.uninit(); }

   void SetRegistryNodeManager(const char *key, const char *val)
   {
      SetRegistryValue(MQTT_CONFIG_PATH, key, val);
   }

   void SetRegistryNodeManager(const char *key, const CORE::vmstr &val)
   {
      CORE::mstr entireKey("HKLM\\");
      entireKey.append(MQTT_CONFIG_PATH);
      entireKey.append("\\");
      entireKey.append(key);
      val.writeRegistry(entireKey);
   }

   void DeleteRegistryNodeManager(const char *key) { DelRegistryValue(MQTT_CONFIG_PATH, key); }

   void DeleteRegistryHzMon(const char *key)
   {
      DelRegistryValue(HORIZON_MON_REG_ROOT_A "\\Config", key);
   }

   void SetRegistryIPCIncludeList(const char *exePath, const char *enabled)
   {
      SetRegistryValue(MQTT_CONFIG_IPC_INCLUDE_PATH, exePath, enabled);
   }

   void DeleteRegistryIPCIncludeList(const char *exePath)
   {
      DelRegistryValue(MQTT_CONFIG_IPC_INCLUDE_PATH, exePath);
   }

   void DeleteLCMSpecInRegistry()
   {
      DeleteRegistryNodeManager("mqtt_Name");
      DeleteRegistryNodeManager(MQTT_CA_THUMBPRINTS_REG);
      DeleteRegistryNodeManager("mqtt_PendingCAThumbprints");
      DeleteRegistryNodeManager("mqtt_ShortTemplateId_pend");
      DeleteRegistryNodeManager("mqtt_VmId_pend");
      DeleteRegistryNodeManager("mqtt_EdgeId_pend");
      DeleteRegistryNodeManager("mqtt_AgentIdentity_pend");
      DeleteRegistryNodeManager("mqtt_ShortAgentIdentity");
      DeleteRegistryNodeManager("mqtt_ShortAgentIdentity_pend");
      DeleteRegistryNodeManager("mqtt_ShortTemplateId");
      DeleteRegistryNodeManager("mqtt_VmId");
      DeleteRegistryNodeManager("mqtt_EdgeId");
      DeleteRegistryNodeManager("mqtt_AgentIdentity");
      DeleteRegistryNodeManager("mqtt_Otp");
      DeleteRegistryNodeManager("mqtt_OtpUrl");
      DeleteRegistryNodeManager("mqtt_UnwantedFields");
      DeleteRegistryNodeManager("mqtt_BootstrapTopic");
      DeleteRegistryNodeManager("mqtt_PendingBootstrapTopic");
      DeleteRegistryNodeManager("mqtt_EndpointUrl");
      DeleteRegistryNodeManager("mqtt_PendingEndpointUrl");
      DeleteRegistryNodeManager("mqtt_Paired");
      DeleteRegistryNodeManager("mqtt_ClientThumbprints");
      DeleteRegistryNodeManager("mqtt_PendingClientThumbprints");
      DeleteRegistryNodeManager("mqtt_aws_mac");
      DeleteRegistryNodeManager("mqtt_aws_refresh_token");
      DeleteRegistryNodeManager("mqtt_aws_lcm_url");
      DeleteRegistryNodeManager("mqtt_aws_pair_version");
      DeleteRegistryNodeManager("mqtt_aws_access_token");
      DeleteRegistryNodeManager("mqtt_aws_refresh_url");
      DeleteRegistryNodeManager("HTTP Proxy Server");
      DeleteRegistryNodeManager("Pending HTTP Proxy Server");
      DeleteRegistryNodeManager("HTTP Proxy Bypass");
      DeleteRegistryNodeManager("Pending HTTP Proxy Bypass");
      DeleteRegistryNodeManager("HTTP Proxy Auto Config URL");
      DeleteRegistryNodeManager("Pending HTTP Proxy Auto Config URL");
      DeleteRegistryNodeManager("mqtt_SkipDomainJoin");
      DeleteRegistryNodeManager("mqtt_TemplateId");
      DeleteRegistryNodeManager(MQTT_MIGRATION_MSG_RCVD);
      DeleteRegistryNodeManager("MS Transport");
      DeleteRegistryNodeManager("mqtt_MigrationStatus");
      DeleteRegistryHzMon("endpoint");
      DeleteRegistryHzMon("pending_endpoint");
      DeleteRegistryNodeManager("DisableHibernationSupport");
   }

   void SetRegistryValue(const char *subpath, const char *var, const char *val,
                         HKEY base = HKEY_LOCAL_MACHINE)
   {
      LONG error = WinReg_SetSZ(base, subpath, var, val);
      if (error == ERROR_SUCCESS) {
         SYSMSG_FUNC(Debug, L"Set the Registry variable: %s Key: %s value: %s",
                     util::corestr::stdStrToWstr(var), util::corestr::stdStrToWstr(subpath),
                     util::corestr::stdStrToWstr(val));
      } else {
         SYSMSG_FUNC(Debug,
                     L"Set the Registry failed: %s Key: %s value: %s with "
                     L"error code 0x%x",
                     util::corestr::stdStrToWstr(var), util::corestr::stdStrToWstr(subpath),
                     util::corestr::stdStrToWstr(val), error);
      }
   }

   void SetRegistryDwordVal(const char *subpath, const char *var, DWORD val,
                            HKEY base = HKEY_LOCAL_MACHINE)
   {
      LONG error = WinReg_SetDWORD(base, subpath, var, val);
      if (error == ERROR_SUCCESS) {
         SYSMSG_FUNC(Debug, L"Set the Registry variable: %s Key: %s value: %d",
                     util::corestr::stdStrToWstr(var), util::corestr::stdStrToTstr(subpath), val);
      } else {
         SYSMSG_FUNC(Debug,
                     L"Set the Registry failed: %s key: %s value: %d with "
                     L"error code 0x%x",
                     util::corestr::stdStrToTstr(var), util::corestr::stdStrToTstr(subpath), val,
                     error);
      }
   }

   void DelRegistryValue(const char *subpath, const char *var)
   {
      LONG error = WinReg_DeleteValue(HKEY_LOCAL_MACHINE, subpath, var);
      if (error == ERROR_SUCCESS) {
         SYSMSG_FUNC(Debug, L"WinReg_DeleteValue deleted: %S Key: %S", var, subpath);
      } else {
         SYSMSG_FUNC(Trace, L"WinReg_DeleteValue failed: %S Key: %S with code 0x%x", var, subpath,
                     error);
      }
   }

   static LONG RegistryCallBack(HKEY base, const char *path, const char *key, const char *className,
                                FILETIME *lastwrite, void *data, char *carryon)
   {
      if (path == NULL || key == NULL || data == NULL) {
         if (carryon) {
            *carryon = false;
         }
         return ERROR_SUCCESS;
      }

      std::vector<std::string> *Subkeys = reinterpret_cast<std::vector<std::string> *>(data);

      std::string abspath = std::string(path) + "\\" + std::string(key);
      Subkeys->emplace_back(abspath);

      if (carryon) {
         *carryon = true;
      }

      return ERROR_SUCCESS;
   }

   void DeleteRegistrySubKeys(std::vector<std::string> &keys, HKEY base = HKEY_LOCAL_MACHINE)
   {
      if (keys.size() == 0) {
         return;
      }

      LONG error;
      for (std::string &subKey : keys) {
         std::vector<std::string> subKeys;
         WinReg_EnumerateSubkeys(base, subKey.c_str(), RegistryCallBack, (void *)&subKeys);
         DeleteRegistrySubKeys(subKeys);

         error = WinReg_DeleteKey(base, subKey.c_str());
         if (error == ERROR_SUCCESS) {
            SYSMSG_FUNC(Debug, L"WinReg_DeleteKey deleted the key '%S'", subKey.c_str());
         } else {
            SYSMSG_FUNC(Debug,
                        L"WinReg_DeleteKey failed to delete the registry key '%S' "
                        L"with error code 0x%x",
                        subKey.c_str(), error);
         }
      }
   }


   /*
    *--------------------------------------------------------------------------
    *
    * MqttManagerHookUpFile --
    *
    *    Creates MockFile, MockFileFactory objects and hooks them into
    *    MqttManager.
    *
    * Results:
    *    None
    *
    * Side effects:
    *    MqttManager will use the mocked file objects.
    *
    *--------------------------------------------------------------------------
    */

   void MqttManagerHookUpFile(std::shared_ptr<MockFile> &azureFile,
                              std::shared_ptr<MockFile> &awsFile,
                              std::shared_ptr<MockFileFactory> &fileFactory)
   {
      azureFile = std::make_shared<MockFile>();
      awsFile = std::make_shared<MockFile>();
      fileFactory = std::make_shared<MockFileFactory>();

      wstr azureOtpFilePath = gMqttConfig.GetLcmSpecFilePath();
      wstr awsOtpFilePath = gMqttConfig.GetAwsOtpFilePath();
      std::filesystem::path azurePath(azureOtpFilePath.c_str());
      std::filesystem::path awsPath(awsOtpFilePath.c_str());

      // These are mainly used for logging. So just give the default paths.
      EXPECT_CALL(*azureFile, GetFilePathString()).WillRepeatedly(Return(azureOtpFilePath.c_str()));
      EXPECT_CALL(*awsFile, GetFilePathString()).WillRepeatedly(Return(awsOtpFilePath.c_str()));
      EXPECT_CALL(*azureFile, GetParentDirectoryPath())
         .WillRepeatedly(Return(azurePath.parent_path()));
      EXPECT_CALL(*awsFile, GetParentDirectoryPath()).WillRepeatedly(Return(awsPath.parent_path()));

      EXPECT_CALL(*fileFactory, GetFile(Matcher<const wstr &>(azureOtpFilePath)))
         .WillRepeatedly(Return(azureFile));
      EXPECT_CALL(*fileFactory, GetFile(Matcher<const wstr &>(awsOtpFilePath)))
         .WillRepeatedly(Return(awsFile));

      MqttManager::GetInstance().SetFileFactory(fileFactory);
   }


   /*
    *--------------------------------------------------------------------------
    *
    * MqttManagerHookUpFile2 --
    *
    *    Creates MockFile, MockFileFactory objects and hooks them into MqttManager.
    *    Allows for two files to be created for each file type (azure or aws).
    *
    * Results:
    *    None
    *
    * Side effects:
    *    MqttManager will use the mocked file objects.
    *
    *--------------------------------------------------------------------------
    */

   void MqttManagerHookUpFiles2(std::shared_ptr<MockFile> &azureFile1,
                                std::shared_ptr<MockFile> &awsFile1,
                                std::shared_ptr<MockFile> &azureFile2,
                                std::shared_ptr<MockFile> &awsFile2,
                                std::shared_ptr<MockFileFactory> &fileFactory)
   {
      azureFile1 = std::make_shared<MockFile>();
      awsFile1 = std::make_shared<MockFile>();
      azureFile2 = std::make_shared<MockFile>();
      awsFile2 = std::make_shared<MockFile>();
      fileFactory = std::make_shared<MockFileFactory>();

      wstr azureOtpFilePath = gMqttConfig.GetLcmSpecFilePath();
      wstr awsOtpFilePath = gMqttConfig.GetAwsOtpFilePath();
      std::filesystem::path azurePath(azureOtpFilePath.c_str());
      std::filesystem::path awsPath(awsOtpFilePath.c_str());

      // These are mainly used for logging. So just give the default paths.
      EXPECT_CALL(*azureFile1, GetFilePathString())
         .WillRepeatedly(Return(azureOtpFilePath.c_str()));
      EXPECT_CALL(*azureFile2, GetFilePathString())
         .WillRepeatedly(Return(azureOtpFilePath.c_str()));
      EXPECT_CALL(*awsFile1, GetFilePathString()).WillRepeatedly(Return(awsOtpFilePath.c_str()));
      EXPECT_CALL(*awsFile2, GetFilePathString()).WillRepeatedly(Return(awsOtpFilePath.c_str()));
      EXPECT_CALL(*azureFile1, GetParentDirectoryPath())
         .WillRepeatedly(Return(azurePath.parent_path()));
      EXPECT_CALL(*azureFile2, GetParentDirectoryPath())
         .WillRepeatedly(Return(azurePath.parent_path()));
      EXPECT_CALL(*awsFile1, GetParentDirectoryPath())
         .WillRepeatedly(Return(awsPath.parent_path()));
      EXPECT_CALL(*awsFile2, GetParentDirectoryPath())
         .WillRepeatedly(Return(awsPath.parent_path()));

      EXPECT_CALL(*fileFactory, GetFile(Matcher<const wstr &>(azureOtpFilePath)))
         .WillOnce(Return(azureFile1))
         .WillOnce(Return(azureFile2));
      EXPECT_CALL(*fileFactory, GetFile(Matcher<const wstr &>(awsOtpFilePath)))
         .WillOnce(Return(awsFile1))
         .WillOnce(Return(awsFile2));

      MqttManager::GetInstance().SetFileFactory(fileFactory);
   }


   /*
    *--------------------------------------------------------------------------
    *
    * MqttManagerHookUpFile3 --
    *
    *    Creates MockFile, MockFileFactory objects and hooks them into MqttManager.
    *    Allows for three files to be created for each file type (azure or aws).
    *
    *    Additionally returns the third file repeatedly after two invocations.
    *
    * Results:
    *    None
    *
    * Side effects:
    *    MqttManager will use the mocked file objects.
    *
    *--------------------------------------------------------------------------
    */

   void MqttManagerHookUpFiles3(std::shared_ptr<MockFile> &azureFile1,
                                std::shared_ptr<MockFile> &awsFile1,
                                std::shared_ptr<MockFile> &azureFile2,
                                std::shared_ptr<MockFile> &awsFile2,
                                std::shared_ptr<MockFile> &azureFile3,
                                std::shared_ptr<MockFile> &awsFile3,
                                std::shared_ptr<MockFileFactory> &fileFactory)
   {
      azureFile1 = std::make_shared<MockFile>();
      awsFile1 = std::make_shared<MockFile>();
      azureFile2 = std::make_shared<MockFile>();
      awsFile2 = std::make_shared<MockFile>();
      azureFile3 = std::make_shared<MockFile>();
      awsFile3 = std::make_shared<MockFile>();
      fileFactory = std::make_shared<MockFileFactory>();

      wstr azureOtpFilePath = gMqttConfig.GetLcmSpecFilePath();
      wstr awsOtpFilePath = gMqttConfig.GetAwsOtpFilePath();
      std::filesystem::path azurePath(azureOtpFilePath.c_str());
      std::filesystem::path awsPath(awsOtpFilePath.c_str());

      // These are mainly used for logging. So just give the default paths.
      EXPECT_CALL(*azureFile1, GetFilePathString())
         .WillRepeatedly(Return(azureOtpFilePath.c_str()));
      EXPECT_CALL(*azureFile2, GetFilePathString())
         .WillRepeatedly(Return(azureOtpFilePath.c_str()));
      EXPECT_CALL(*azureFile3, GetFilePathString())
         .WillRepeatedly(Return(azureOtpFilePath.c_str()));
      EXPECT_CALL(*awsFile1, GetFilePathString()).WillRepeatedly(Return(awsOtpFilePath.c_str()));
      EXPECT_CALL(*awsFile2, GetFilePathString()).WillRepeatedly(Return(awsOtpFilePath.c_str()));
      EXPECT_CALL(*awsFile3, GetFilePathString()).WillRepeatedly(Return(awsOtpFilePath.c_str()));
      EXPECT_CALL(*azureFile1, GetParentDirectoryPath())
         .WillRepeatedly(Return(azurePath.parent_path()));
      EXPECT_CALL(*azureFile2, GetParentDirectoryPath())
         .WillRepeatedly(Return(azurePath.parent_path()));
      EXPECT_CALL(*azureFile3, GetParentDirectoryPath())
         .WillRepeatedly(Return(azurePath.parent_path()));
      EXPECT_CALL(*awsFile1, GetParentDirectoryPath())
         .WillRepeatedly(Return(awsPath.parent_path()));
      EXPECT_CALL(*awsFile2, GetParentDirectoryPath())
         .WillRepeatedly(Return(awsPath.parent_path()));
      EXPECT_CALL(*awsFile3, GetParentDirectoryPath())
         .WillRepeatedly(Return(awsPath.parent_path()));

      EXPECT_CALL(*fileFactory, GetFile(Matcher<const wstr &>(azureOtpFilePath)))
         .WillOnce(Return(azureFile1))
         .WillOnce(Return(azureFile2))
         .WillRepeatedly(Return(azureFile3));
      EXPECT_CALL(*fileFactory, GetFile(Matcher<const wstr &>(awsOtpFilePath)))
         .WillOnce(Return(awsFile1))
         .WillOnce(Return(awsFile2))
         .WillRepeatedly(Return(awsFile3));

      MqttManager::GetInstance().SetFileFactory(fileFactory);
   }


   /*
    *--------------------------------------------------------------------------
    *
    * MqttManagerClearFileFactory --
    *
    *    Clears the FileFactory object in MqttManager.
    *
    * Results:
    *    None.
    *
    * Side effects:
    *    None.
    *
    *--------------------------------------------------------------------------
    */

   void MqttManagerClearFileFactory() { MqttManager::GetInstance().SetFileFactory(nullptr); }


   /*
    *--------------------------------------------------------------------------
    *
    * MqttManagerClearDirectoryFactory --
    *
    *    Clears the DirectoryFactory object in MqttManager.
    *
    * Results:
    *    None.
    *
    * Side effects:
    *    None.
    *
    *--------------------------------------------------------------------------
    */

   void MqttManagerClearDirectoryFactory()
   {
      MqttManager::GetInstance().SetDirectoryFactory(nullptr);
   }


   /*
    *--------------------------------------------------------------------------
    *
    * SetupValidFile --
    *
    *    Modifies a MockFile object to behave like a real file would;
    *    it has contents and accurately tracks when it has been deleted.
    *    The mocked file also has the highest permissions.
    *
    * Results:
    *    None
    *
    * Side effects:
    *    None.
    *
    *--------------------------------------------------------------------------
    */

   void SetupValidFile(bool &bFileExists, const std::string &fileContents,
                       std::shared_ptr<MockFile> file)
   {
      bFileExists = true;
      EXPECT_CALL(*file, FileExists()).WillRepeatedly([&bFileExists]() { return bFileExists; });
      EXPECT_CALL(*file, OwnedByHighestPrivilegedAccount()).WillOnce([&bFileExists]() {
         return bFileExists;
      });
      EXPECT_CALL(*file, FileDelete()).WillOnce([&bFileExists]() {
         bFileExists = false;
         return true;
      });
      EXPECT_CALL(*file, ReadFileIntoString_impl(_)).WillOnce(Return(fileContents));
   }


   /*
    *--------------------------------------------------------------------------
    *
    * MqttManagerHookUpHttpRequests --
    *
    *    Hooks up two HTTP requests into the MqttManager.
    *    Usually the first request is the fetch lcm request and the second is
    *    the redeem otp request. However, you can hook any two requests you
    *    need.
    *
    * Results:
    *    None
    *
    * Side effects:
    *    MqttManager will use the mocked http request objects
    *
    *--------------------------------------------------------------------------
    */

   void MqttManagerHookUpHttpRequests(unsigned int numOfRequests,
                                      std::shared_ptr<MockHttpRequest> &firstReq,
                                      std::shared_ptr<MockHttpRequest> &secondReq,
                                      std::shared_ptr<MockHttpRequest> &thirdReq,
                                      std::shared_ptr<MockHttpRequest> &fourthReq,
                                      std::shared_ptr<MockHttpRequestFactory> &reqFactory)
   {
      reqFactory = std::make_shared<MockHttpRequestFactory>();
      firstReq = std::make_shared<MockHttpRequest>();
      secondReq = std::make_shared<MockHttpRequest>();
      thirdReq = std::make_shared<MockHttpRequest>();
      fourthReq = std::make_shared<MockHttpRequest>();

      switch (numOfRequests) {
      case 4:
         EXPECT_CALL(*reqFactory, GetHttpRequestClient())
            .WillOnce(Return(firstReq))
            .WillOnce(Return(secondReq))
            .WillOnce(Return(thirdReq))
            .WillOnce(Return(fourthReq));
         break;
      case 3:
         EXPECT_CALL(*reqFactory, GetHttpRequestClient())
            .WillOnce(Return(firstReq))
            .WillOnce(Return(secondReq))
            .WillOnce(Return(thirdReq));
         break;
      case 2:
         EXPECT_CALL(*reqFactory, GetHttpRequestClient())
            .WillOnce(Return(firstReq))
            .WillOnce(Return(secondReq));
         break;
      case 1:
         EXPECT_CALL(*reqFactory, GetHttpRequestClient()).WillOnce(Return(firstReq));
         break;
      default:
         // Shouldn't happen
         EXPECT_TRUE(false);
      }

      MqttManager::GetInstance().SetHttpRequestFactory(reqFactory);
   }


   /*
    *--------------------------------------------------------------------------
    *
    * SetupHttpPostRequestMock --
    *
    *    Modifies a MockHttpRequest (GET request) object to behave as per the
    *    given parameters.
    *
    *    If the mocked response is empty, we assume we want the request to fail.
    *
    *    If the httpError is set, we assume that the request should return the
    *    given code when asked via GetResponse.
    *
    *    Validates the URL and validates a given header.
    *    Currently limited to a single header.
    *
    * Results:
    *    None
    *
    * Side effects:
    *    None.
    *
    *--------------------------------------------------------------------------
    */

   void SetupHttpPostRequestMock(std::shared_ptr<MockHttpRequest> mockReq,
                                 const std::string &expectedUrl, const std::string &expectedPayload,
                                 const std::string &mockedResponse,
                                 const std::pair<std::string, std::string> &headerToCheck,
                                 unsigned int httpError = 0)
   {
      if (mockedResponse.empty()) {
         if (expectedPayload.empty()) {
            EXPECT_CALL(*mockReq, postSync(_, StrEq(expectedUrl), _, _)).WillOnce(Return(false));
         } else {
            EXPECT_CALL(*mockReq, postSync(_, StrEq(expectedUrl), StrEq(expectedPayload), _))
               .WillOnce(Return(false));
         }
      } else {
         if (expectedPayload.empty()) {
            EXPECT_CALL(*mockReq, postSync(_, StrEq(expectedUrl), _, _))
               .WillOnce(DoAll(SetArgReferee<3>(mockedResponse), Return(true)));
         } else {
            EXPECT_CALL(*mockReq, postSync(_, StrEq(expectedUrl), StrEq(expectedPayload), _))
               .WillOnce(DoAll(SetArgReferee<3>(mockedResponse), Return(true)));
         }
      }
      if (!headerToCheck.first.empty()) {
         EXPECT_CALL(*mockReq, setHeaders(EntryInMap(headerToCheck.first, headerToCheck.second)));
      }
      if (httpError) {
         EXPECT_CALL(*mockReq, getResponseCode()).WillOnce(Return(httpError));
      }

      EXPECT_CALL(*mockReq, enableCertRevocation()).Times(0);
   }


   /*
    *--------------------------------------------------------------------------
    *
    * GenerateOtpRedemptionResponse --
    *
    *    Creates a response to the OTP redemption request.
    *
    * Results:
    *    A string containing the response.
    *
    * Side effects:
    *    None.
    *
    *--------------------------------------------------------------------------
    */

   std::string GenerateOtpRedemptionResponse(const std::string &caCrt, const std::string &clientCrt)
   {
      CORE::mstr caCrtEncoded(caCrt);
      CORE::mstr clientCrtEncoded(clientCrt);
      caCrtEncoded = caCrtEncoded.base64Encode();
      clientCrtEncoded = clientCrtEncoded.base64Encode();

      std::string responseFmt = R"({)"
                                R"("mqttServerHost":"%s",)"
                                R"("mqttServerPort":"%s",)"
                                R"("wsEnabled":false,)"
                                R"("sslEnabled":true,)"
                                R"("agentBootstrapTopic":"desktop/agent/bootstrap",)"
                                R"("caCrt":"%s",)"
                                R"("clientCrt":"%s")"
                                R"(})";
      CORE::mstr response = CORE::mstr::printf(responseFmt.c_str(), mMQTTEndpointHost.c_str(),
                                               mMQTTEndpointPort.c_str(), caCrtEncoded.c_str(),
                                               clientCrtEncoded.c_str());
      return std::string(response.c_str());
   }

   std::wstring GenerateMosquittoPublishMessage(const std::wstring &topic, const std::wstring &msg)
   {
      std::wstring mosquittoClientCaPath = mConfigDir + L"\\mosquitto_broker_all_ca.crt";
      std::wstring mosquittoClientCertPath = mConfigDir + L"\\mosquitto_client.crt";
      std::wstring mosquittoClientKeyPath = mConfigDir + L"\\mosquitto_client.key";

      std::wstring fmt =
         LR"("%s\mosquitto_pub.exe" -h localhost -p 8883 --insecure -i pubclient -t "%s" -m "%s" )"
         LR"(--cafile "%s" --cert "%s" --key "%s")";
      return wstr::printf(fmt.c_str(), mMosquittoDir.c_str(), topic.c_str(), msg.c_str(),
                          mosquittoClientCaPath.c_str(), mosquittoClientCertPath.c_str(),
                          mosquittoClientKeyPath.c_str())
         .c_str();
   }


   /*
    *--------------------------------------------------------------------------
    *
    * GenerateMosquittoConfContent --
    *
    *    Get the content of the mosquitto conf file.
    *
    * Results:
    *    A string that should be the content of the mosquitto conf file.
    *
    * Side effects:
    *    None.
    *
    *--------------------------------------------------------------------------
    */

   std::string GenerateMosquittoConfContent(const std::string &caFileName,
                                            const std::string &certFileName,
                                            const std::string &keyFileName, bool tlsv13 = false)
   {
      std::string contents;
      contents += "listener 8883\n";
      contents += "cafile " + mConfigDirA + "\\" + caFileName + "\n";
      contents += "certfile " + mConfigDirA + "\\" + certFileName + "\n";
      contents += "keyfile " + mConfigDirA + "\\" + keyFileName + "\n";
      contents += "require_certificate true\n";
      contents += "tls_version tlsv";
      contents += tlsv13 ? "1.3\n" : "1.2\n";
      contents += "allow_anonymous true\n";
      return contents;
   }


   /*
    *--------------------------------------------------------------------------
    *
    * KillActiveMosquittoProcess --
    *
    *    Kill any active mosquitto process that is running.
    *
    * Results:
    *    None.
    *
    * Side effects:
    *    None.
    *
    *--------------------------------------------------------------------------
    */

   void KillActiveMosquittoProcess()
   {
      std::wstring mosquittoName = L"mosquitto.exe";
      HANDLE hSnapShot = CreateToolhelp32Snapshot(TH32CS_SNAPALL, NULL);
      if (hSnapShot == INVALID_HANDLE_VALUE) {
         SYSMSG_FUNC(Debug, "CreateToolhelp32Snapshot failed with error code: %d", GetLastError());
         return;
      }

      PROCESSENTRY32 pEntry;
      pEntry.dwSize = sizeof(pEntry);
      BOOL hRes = Process32First(hSnapShot, &pEntry);
      while (hRes) {
         if (!mosquittoName.compare(pEntry.szExeFile)) {
            HANDLE hProcess = OpenProcess(PROCESS_TERMINATE, 0, (DWORD)pEntry.th32ProcessID);
            if (hProcess != NULL) {
               TerminateProcess(hProcess, 9);
               CloseHandle(hProcess);
               SYSMSG_FUNC(Debug, "Terminated active mosquitto process");
               break;
            }
         }
         hRes = Process32Next(hSnapShot, &pEntry);
      }
      CloseHandle(hSnapShot);
   }


   /*
    *--------------------------------------------------------------------------
    *
    * CloseProcess --
    *
    *    Close a given process
    *
    * Results:
    *    True if successful, false otherwise.
    *
    * Side effects:
    *    None.
    *
    *--------------------------------------------------------------------------
    */

   bool CloseProcess(PROCESS_INFORMATION &pi)
   {
      DWORD exitCode;
      if (!GetExitCodeProcess(pi.hProcess, &exitCode)) {
         SYSMSG_FUNC(Debug, "GetExitCodeProcess failed with error code: %s", wstr::formatError());
         return false;
      }

      if (!TerminateProcess(pi.hProcess, exitCode)) {
         SYSMSG_FUNC(Debug, "Terminate process failed with error: %s", wstr::formatError());
         return false;
      }

      WaitForSingleObject(pi.hProcess, INFINITE);

      CloseHandle(pi.hProcess);
      CloseHandle(pi.hThread);
      return true;
   }


   /*
    *--------------------------------------------------------------------------
    *
    * WriteMosquittoConfFile --
    *
    *    Write the mosquitto conf file to the given file name.
    *
    * Results:
    *    True if successful, false otherwise.
    *
    * Side effects:
    *    None.
    *
    *--------------------------------------------------------------------------
    */

   bool WriteMosquittoConfFile(const std::wstring &mosquittoConfFileName,
                               const std::string &mosquittoCa, const std::string &mosquittoCert,
                               const std::string &mosquittoKey)
   {
      auto content = GenerateMosquittoConfContent(mosquittoCa, mosquittoCert, mosquittoKey);

      std::wstring mosquittoConf = mConfigDir + L"\\" + mosquittoConfFileName;
      WinFile mosquittoConfFile(mosquittoConf);
      if (mosquittoConfFile.FileExists()) {
         mosquittoConfFile.FileDelete();
      }
      if (!mosquittoConfFile.WriteStringToFile(content)) {
         SYSMSG_FUNC(Debug, "Failed to write mosquitto conf file");
         return false;
      }
      return true;
   }


   /*
    *----------------------------------------------------------------------------
    *
    * GetMosquittoBrokerRunCmd
    *
    *    Returns the full command line to run the mosquitto broker (with the given conf file).
    *
    * Results:
    *    The command line to run the mosquitto broker.
    *
    * Side effects:
    *    None.
    *
    *----------------------------------------------------------------------------
    */

   std::wstring GetMosquittoBrokerRunCmd(const std::wstring &mosquittoConfFileName)
   {
      return L"\"" + mMosquittoDir + L"\\mosquitto.exe\" -c \"" + mConfigDir + L"\\" +
             mosquittoConfFileName + L"\" -v";
   }


   /*
    *----------------------------------------------------------------------------
    *
    * StartMosquittoBroker
    *
    *    Starts the mosquitto broker with the given credentials.
    *
    * Results:
    *    True if the broker started successfully, false otherwise.
    *
    * Side effects:
    *    A separate mosquitto process will be launched.
    *
    *----------------------------------------------------------------------------
    */

   bool StartMosquittoBroker(const std::wstring &testName, const std::string &mosquittoCa,
                             const std::string &mosquittoCert, const std::string &mosquittoKey,
                             const std::wstring &mosquittoLogFilePath, PROCESS_INFORMATION &pi,
                             HANDLE &fileHandle)
   {
      std::wstring mosquittoConfFileName = testName + L".conf";
      if (!WriteMosquittoConfFile(mosquittoConfFileName, mosquittoCa, mosquittoCert,
                                  mosquittoKey)) {
         SYSMSG_FUNC(Debug, "Failed to write mosquitto conf file");
         return false;
      }

      auto mosquittoCmd = GetMosquittoBrokerRunCmd(mosquittoConfFileName);
      if (!ProcUtil::LaunchProcessWithStdOut(mosquittoCmd, mosquittoLogFilePath, pi, fileHandle,
                                             false)) {
         SYSMSG_FUNC(Debug, "Failed to launch mosquitto broker");
         return false;
      }
      return true;
   }


   /*
    *----------------------------------------------------------------------------
    *
    * LaunchProcessWaitForCompletion
    *
    *    Starts a process (with the given command) and waits for it to complete.
    *
    * Results:
    *    True if the process started successfully and completed, false otherwise.
    *
    * Side effects:
    *    A separate process will be launched.
    *
    *----------------------------------------------------------------------------
    */

   bool LaunchProcessWaitForCompletion(const std::wstring &cmdStr)
   {
      PROCESS_INFORMATION pi = {0};
      STARTUPINFO info = {0};
      info.cb = sizeof(info);
      LPWSTR cmd = const_cast<wchar_t *>(cmdStr.c_str());
      if (!CreateProcess(NULL, cmd, NULL, NULL, FALSE, NORMAL_PRIORITY_CLASS, NULL, NULL, &info,
                         &pi)) {
         SYSMSG_FUNC(Debug, "CreateProcess failed to launch the command: %s Error: %s", cmd,
                     wstr::formatError());
         return false;
      }
      cedar::windows::unique_process piHandle(pi.hProcess);
      cedar::windows::unique_thread piThread(pi.hThread);

      auto waitResult = WaitForSingleObject(piHandle.get(), INFINITE);
      if (waitResult != WAIT_OBJECT_0) {
         SYSMSG_FUNC(Debug, "WaitForSingleObject failed to wait for the process: %s Error: %s", cmd,
                     wstr::formatError());
         return false;
      }
      return true;
   }


   /*
    *----------------------------------------------------------------------------
    *
    * PublishMosquittoMessage
    *
    *    Publish a given message to a given topic using the mosquitto client.
    *
    * Results:
    *    True if the process started successfully and completed, false otherwise.
    *
    * Side effects:
    *    A separate process will be launched.
    *
    *----------------------------------------------------------------------------
    */

   bool PublishMosquittoMessage(const std::wstring &topic, const std::wstring &msg)
   {
      auto mosquittoCmd = GenerateMosquittoPublishMessage(topic, msg);
      if (!LaunchProcessWaitForCompletion(mosquittoCmd)) {
         SYSMSG_FUNC(Debug, "Failed to publish msg %s to topic %s", msg.c_str(), topic.c_str());
         return false;
      }
      return true;
   }


   /*
    *----------------------------------------------------------------------------
    *
    * SetupMockHttpRequestProxy
    *
    *    Set the expecation that the mock http request will not use any proxy.
    *
    * Results:
    *    None.
    *
    * Side effects:
    *    None.
    *
    *----------------------------------------------------------------------------
    */

   void SetupMockHttpRequestProxy(std::shared_ptr<MockHttpRequest> mockReq)
   {
      EXPECT_CALL(*mockReq, setProxyServerList(_)).WillOnce(Return());
      EXPECT_CALL(*mockReq, setProxyBypass(_)).WillOnce(Return());
      EXPECT_CALL(*mockReq, setProxyPACURL(_)).WillOnce(Return());
   }


   /*
    *----------------------------------------------------------------------------
    *
    * SetupMockHttpRequestProxy
    *
    *    Set the expectation that the mock http request will not use any proxy.
    *
    * Results:
    *    None.
    *
    * Side effects:
    *    None.
    *
    *----------------------------------------------------------------------------
    */

   void SetupMockHttpRequestProxy(std::shared_ptr<MockHttpRequest> mockReq,
                                  const std::string &proxyServer, const std::string &proxyBypass,
                                  const std::string &pacurl)
   {
      EXPECT_CALL(*mockReq, setProxyServerList(StrEq(proxyServer))).WillOnce(Return());
      EXPECT_CALL(*mockReq, setProxyBypass(StrEq(proxyBypass))).WillOnce(Return());
      EXPECT_CALL(*mockReq, setProxyPACURL(StrEq(pacurl))).WillOnce(Return());
   }


   /*
    *--------------------------------------------------------------------------
    *
    * MqttManagerHookUpDirectory --
    *
    *    Creates MockDirectory, MockDirectoryFactory objects and hooks them into
    *    MqttManager.
    *
    * Results:
    *    None
    *
    * Side effects:
    *    MqttManager will use the mocked directory objects.
    *
    *--------------------------------------------------------------------------
    */

   void MqttManagerHookUpDirectory(std::shared_ptr<MockDirectory> &mockDir,
                                   std::shared_ptr<MockDirectoryFactory> &dirFactory)
   {
      mockDir = std::make_shared<MockDirectory>();
      dirFactory = std::make_shared<MockDirectoryFactory>();
      EXPECT_CALL(*dirFactory, GetDirectory(Matcher<const IFile &>(_)))
         .WillRepeatedly(Return(mockDir));
      MqttManager::GetInstance().SetDirectoryFactory(dirFactory);
   }


   enum AllTopics {
      BOOTSTRAP = 0,
      COMMANDS,
      CONFIG,
      TEMPLATE,
      OTP,
      IPC,
      RESPONSE,
      STATUS,
      EVENT_LOGGER
   };


   /*
    *--------------------------------------------------------------------------
    *
    * GetTopicName --
    *
    *    Gets the topic name for a particular topic type.
    *
    * Results:
    *    The topic string.
    *
    *--------------------------------------------------------------------------
    */

   std::wstring GetTopicName(AllTopics topicType, const std::wstring &edgeId,
                             const std::wstring &templateId, const std::wstring &vmId)
   {
      wchar_t topic[MAX_PATH];
      std::wstring topicS;
      std::wstring topicStruct = edgeId + L"/" + templateId + L"/" + vmId;
      switch (topicType) {
      case AllTopics::BOOTSTRAP: {
         return L"desktop/agent/bootstrap";
      }
      case AllTopics::COMMANDS: {
         wstr topicTemplate = util::corestr::stdStrToWstr(MQTT_COMMANDS_TOPIC);
         _snwprintf_s(topic, MAX_PATH, _TRUNCATE, topicTemplate.c_str(), topicStruct.c_str());
         topicS = topic;
         return topicS;
      }
      case AllTopics::TEMPLATE: {
         wstr topicTemplate = util::corestr::stdStrToWstr(MQTT_DTEMPLATE_TOPIC);
         _snwprintf_s(topic, MAX_PATH, _TRUNCATE, topicTemplate.c_str(), topicStruct.c_str());
         topicS = topic;
         return topicS;
      }
      case AllTopics::OTP: {
         wstr topicTemplate = util::corestr::stdStrToWstr(MQTT_OTP_TOKEN_TOPIC);
         _snwprintf_s(topic, MAX_PATH, _TRUNCATE, topicTemplate.c_str(), topicStruct.c_str());
         topicS = topic;
         return topicS;
      }
      case AllTopics::RESPONSE: {
         wstr topicTemplate = util::corestr::stdStrToWstr(MQTT_RESPONSE_TOPIC);
         _snwprintf_s(topic, MAX_PATH, _TRUNCATE, topicTemplate.c_str(), topicStruct.c_str());
         topicS = topic;
         return topicS;
      }
      case AllTopics::STATUS: {
         wstr topicTemplate = util::corestr::stdStrToWstr(MQTT_DESKTOPSTATUS_TOPIC);
         _snwprintf_s(topic, MAX_PATH, _TRUNCATE, topicTemplate.c_str(), topicStruct.c_str());
         topicS = topic;
         return topicS;
      }
      case AllTopics::EVENT_LOGGER: {
         wstr topicTemplate = util::corestr::stdStrToWstr(MQTT_EVENTLOGGER_TOPIC);
         _snwprintf_s(topic, MAX_PATH, _TRUNCATE, topicTemplate.c_str(), topicStruct.c_str());
         topicS = topic;
         return topicS;
      }
      default:
         break;
      }
      return L"";
   }


   /*
    *--------------------------------------------------------------------------
    *
    * SubscribeToTopic --
    *
    *    Starts a mosquitto client (in a separate process) to subscribe to a given topic.
    *
    * Results:
    *    True if the process started successfully, false otherwise.
    *
    * Side Effects:
    *    Messages received will be written to the given file path.
    *
    *--------------------------------------------------------------------------
    */

   bool SubscribeToTopic(const std::wstring &filePath, AllTopics topic, const std::wstring &edgeId,
                         const std::wstring &tempId, const std::wstring &vmId,
                         PROCESS_INFORMATION &pi, HANDLE &fileHandle)
   {
      std::wstring mosquittoClientCaPath = mConfigDir + L"\\mosquitto_broker_ca.crt";
      std::wstring mosquittoClientCertPath = mConfigDir + L"\\mosquitto_client.crt";
      std::wstring mosquittoClientKeyPath = mConfigDir + L"\\mosquitto_client.key";

      wstr subCmdFmt =
         LR"("%s\mosquitto_sub.exe" -h localhost -p 8883 --insecure -i subclient -t "%s" --cafile %s --cert %s --key %s)";

      auto subCmd = wstr::printf(subCmdFmt.c_str(), mMosquittoDir.c_str(),
                                 GetTopicName(topic, edgeId, tempId, vmId).c_str(),
                                 mosquittoClientCaPath.c_str(), mosquittoClientCertPath.c_str(),
                                 mosquittoClientKeyPath.c_str());

      return ProcUtil::LaunchProcessWithStdOut(subCmd.c_str(), filePath, pi, fileHandle);
   }


   /*
    *----------------------------------------------------------------------------
    *
    * FindClientConnected
    *
    *    Determines if a given client connected to the broker.
    *
    * Results:
    *    True if the given client connected, false otherwise.
    *
    * Side effects:
    *    pos is updated to the end of the log statement where the client connected.
    *
    *----------------------------------------------------------------------------
    */

   bool FindClientConnected(const std::string &brokerLog, const std::string &expectedClientId,
                            size_t &pos)
   {
      if (pos == std::string::npos) {
         return false;
      }

      // Connected log statement looks like this:
      // New client connected from 127.0.0.1:50816 as edgeGatewayIdshortTemplateId_vmId
      std::string expectedLogStatement = "New client connected from 127.0.0.1:";
      std::string expectedClientIdStr = "as " + expectedClientId;

      while (pos != std::string::npos) {
         pos = brokerLog.find(expectedLogStatement, pos);
         if (pos == std::string::npos) {
            return false;
         }
         // Find the next space
         pos += expectedLogStatement.length();
         pos = brokerLog.find(' ', pos);
         if (pos == std::string::npos) {
            return false;
         }
         pos++;

         size_t asStartPos = pos;
         pos = brokerLog.find(expectedClientIdStr.c_str(), pos, expectedClientIdStr.length());
         if (pos == asStartPos) {
            pos += expectedClientIdStr.length();
            return true;
         }
         // Revert to 'as' spot and keep searching
         pos = asStartPos;
      }

      pos = std::string::npos;
      return false;
   }


   /*
    *----------------------------------------------------------------------------
    *
    * FindPublish
    *
    *    Determines if a given client published a message to the broker.
    *
    * Results:
    *    True if the given client published a message, false otherwise.
    *
    * Side effects:
    *    pos is updated to the end of the log statement where the client published.
    *
    *----------------------------------------------------------------------------
    */

   bool FindPublish(const std::string &brokerLog, const std::string &clientId, size_t &pos)
   {
      if (pos == std::string::npos) {
         return false;
      }

      std::string expectedLogStatement = "Received PUBLISH from " + clientId;
      pos = brokerLog.find(expectedLogStatement, pos);
      if (pos == std::string::npos) {
         return false;
      }
      pos += expectedLogStatement.length();
      return true;
   }


   /*
    *----------------------------------------------------------------------------
    *
    * FindClientDisconnected
    *
    *    Determines if a given client disconnected from the broker.
    *
    * Results:
    *    True if the given client disconnected, false otherwise.
    *
    * Side effects:
    *    pos is updated to the end of the log statement where the client published.
    *
    *----------------------------------------------------------------------------
    */

   bool FindClientDisconnected(const std::string &brokerLog, const std::string &clientId,
                               size_t &pos)
   {
      if (pos == std::string::npos) {
         return false;
      }
      std::string expectedLogStatement = "Client " + clientId + " disconnected";
      pos = brokerLog.find(expectedLogStatement, pos);
      if (pos == std::string::npos) {
         return false;
      }
      pos += expectedLogStatement.length();
      return true;
   }


   /*
    *----------------------------------------------------------------------------
    *
    * FindAllSubscriptions
    *
    *    Determines if a given client subscribed to all expected topics.
    *
    * Results:
    *    True if the given client subscribed to all expected topics, false otherwise.
    *
    * Side effects:
    *    pos is updated to the end of the log statement where the client subscribed.
    *
    *----------------------------------------------------------------------------
    */

   bool FindAllSubscriptions(const std::string &brokerLog, const std::string &edgeId,
                             const std::string &templateId, const std::string &vmId, size_t &pos)
   {
      if (pos == std::string::npos) {
         return false;
      }
      // Find the lower-bound
      std::string agentIdentity = edgeId + templateId + "_" + vmId;
      std::string expectedLogStatement = "Received SUBSCRIBE from " + agentIdentity;

      pos = brokerLog.find(expectedLogStatement, pos);
      if (pos == std::string::npos) {
         return false;
      }
      pos += expectedLogStatement.length();

      size_t firstSubscription = pos;

      // Find the upper-bound. The Agent will always subscribe before publishing.
      size_t tempPos = pos;
      if (!FindPublish(brokerLog, agentIdentity, tempPos)) {
         return false;
      }
      size_t publishPos = tempPos;

      // Now make sure all the subscriptions happened
      wstr edgeIdw = mstr::to_wstr(edgeId.c_str());
      wstr templateIdw = mstr::to_wstr(templateId.c_str());
      wstr vmIdw = mstr::to_wstr(vmId.c_str());

      size_t lastSubscription = 0;
      std::vector<AllTopics> subscriptionsToFind{AllTopics::BOOTSTRAP, AllTopics::COMMANDS,
                                                 AllTopics::TEMPLATE, AllTopics::CONFIG,
                                                 AllTopics::OTP};
      for (const auto &topic : subscriptionsToFind) {
         tempPos = firstSubscription;

         std::wstring topicName =
            GetTopicName(topic, edgeIdw.c_str(), templateIdw.c_str(), vmIdw.c_str());
         std::string topicNameStr(wstr::to_mstr(topicName.c_str()).c_str());

         tempPos = brokerLog.find(topicNameStr, tempPos);
         if (tempPos == std::string::npos) {
            return false;
         }
         if (tempPos > publishPos) {
            return false;
         }
         if (tempPos > lastSubscription) {
            lastSubscription = tempPos + topicName.length();
         }
      }
      pos = lastSubscription;

      return true;
   }


   /*
    *--------------------------------------------------------------------------
    *
    * ParseJsonMsgs --
    *
    *    Reads the given file and parses the JSON messages in it.
    *
    * Results:
    *    None
    *
    * Side Effects:
    *    None
    *
    *--------------------------------------------------------------------------
    */

   void ParseJsonMsgs(const std::wstring &subscribeLogPath, std::vector<Json::Value> &jsonMsgs)
   {
      jsonMsgs.clear();

      WinFile subLogFile(subscribeLogPath);
      mstr subLog(subLogFile.ReadFileIntoString());

      vmstr rawMsgs = subLog.split("\n", false);

      for (const auto &msg : rawMsgs) {
         Json::Reader reader;
         Json::Value jsonMsg;
         EXPECT_TRUE(reader.parse(msg.c_str(), jsonMsg));
         jsonMsgs.push_back(jsonMsg);
      }
   }
};
