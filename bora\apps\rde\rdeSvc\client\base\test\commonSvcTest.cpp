/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * commonSvcTest.cpp -
 *
 *    Test functions in commonSvc.cpp
 */

#include <gmock/gmock.h>
#include <gtest/gtest.h>
#include "commonSvcUTMock.h"
#include "commonSvcUtil.h"
#include "fakeVdpService.h"
#include "rdeClientBaseUnitTest.h"
#include "rxUTLog.h"
#include "utMock.h"
#include "shared/rdeSvc_defines.h"
#include "shared/commonSvcMsg.h"

using ::testing::_;
using ::testing::Invoke;
using ::testing::NiceMock;


class CommonSvcUnitTest : public RdeClientBaseUnitTest {
   void SetUp() override
   {
      VDP_SERVICE_QUERY_INTERFACE api = {0};
      void *channelHandle = NULL;
      if (!VDPService_ServerInit("CommonSvcUnitTest", &api, &channelHandle)) {
         FAIL() << "Failed to initialize vdp service for CommonSvcUnitTest\n";
      }
      SetAPIEntry(api);
   }

   void TearDown() override { VDPService_ServerExit(); }
};


/*
 *-----------------------------------------------------------------------------
 *
 * CommonSvcUnitTest::TestConstructor --
 *
 *      Test commonSvc constructor.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CommonSvcUnitTest, TestConstructor)
{
   CommonSvcUTMock *commonSvc = new NiceMock<CommonSvcUTMock>();
#if defined(__APPLE__) || defined(_WIN32)
   EXPECT_TRUE(commonSvc->GetFeatureEnablementCapacity() & BLOCK_KEY_LOGGER_MASK);
   EXPECT_TRUE(commonSvc->GetFeatureEnablementCapacity() & BLOCK_SCREEN_CAPTURE_MASK);
#endif
#if defined(_WIN32)
   EXPECT_TRUE(commonSvc->GetFeatureEnablementCapacity() & BLOCK_SEND_INPUT_MASK);
#endif
   delete commonSvc;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CommonSvcUnitTest::TestOnInvoked --
 *
 *      Test OnInvoked method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CommonSvcUnitTest, TestOnInvoked)
{
   CommonSvcUTMock *commonSvc = new NiceMock<CommonSvcUTMock>();
#if defined(__APPLE__) || defined(_WIN32)
   // Test decode error.
   void *dummyContextHandle = (void *)0x123;
   VMOCK(CommonSvcCommand_Decode).Will(false);
   VMOCK(&util::ChannelContextHandle::GetCommand).Times(0);
   commonSvc->OnInvoked(dummyContextHandle);

   // Test invalid data.
   VMOCK(&util::ChannelContextHandle::GetCommand).WillRepeatedly(FEATURE_ENABLEMENT_MSG);
   VMOCK(CommonSvcCommand_Decode).WillOnce([](void *context, CommonSvcCommand *cmd) {
      cmd->paramCount = 1;
      cmd->params = (VDP_RPC_VARIANT *)malloc(sizeof(VDP_RPC_VARIANT));
      cmd->params[0].blobVal = {0};
      cmd->params[0].blobVal.blobData = NULL;
      return true;
   });
   VMOCK(CommonSvcCommand_Clear).WillRepeatedly([](CommonSvcCommand *cmd) { free(cmd->params); });
   commonSvc->OnInvoked(dummyContextHandle);

   // Test invalid cmd.
   FeatureEnablementCommand fakedInvalidCmd;
   fakedInvalidCmd.commandType = FEATURE_ENABLEMENT_CMD_NONE;
   VMOCK(CommonSvcCommand_Decode)
      .WillRepeatedly([&fakedInvalidCmd](void *context, CommonSvcCommand *cmd) {
         cmd->paramCount = 1;
         cmd->params = (VDP_RPC_VARIANT *)malloc(sizeof(VDP_RPC_VARIANT));
         cmd->params[0].blobVal = {0};
         cmd->params[0].blobVal.blobData = (char *)(&fakedInvalidCmd);
         cmd->params[0].blobVal.size = sizeof(FeatureEnablementCommand);
         return true;
      });
   VMOCK(&CommonSvcUTMock::ProcessFeatureEnablementCommandMock).Times(0);
   commonSvc->OnInvoked(dummyContextHandle);

   // Test valid cmd.
   FeatureEnablementCommand fakedValidCmd;
   fakedValidCmd.commandType = FEATURE_ENABLEMENT_CMD_STATUS;
   fakedValidCmd.data.status = 2;
   VMOCK(CommonSvcCommand_Decode)
      .WillRepeatedly([&fakedValidCmd](void *context, CommonSvcCommand *cmd) {
         cmd->paramCount = 1;
         cmd->params = (VDP_RPC_VARIANT *)malloc(sizeof(VDP_RPC_VARIANT));
         cmd->params[0].blobVal = {0};
         cmd->params[0].blobVal.blobData = (char *)(&fakedValidCmd);
         cmd->params[0].blobVal.size = sizeof(FeatureEnablementCommand);
         return true;
      });
   VMOCK(&CommonSvcUTMock::ProcessFeatureEnablementCommandMock).Times(1);
   commonSvc->OnInvoked(dummyContextHandle);
   EXPECT_TRUE(commonSvc->GetFeatureEnableStatus() == fakedValidCmd.data.status);

   // Test cmd delayed for feature option.
   fakedValidCmd.data.status |= FEATURE_OPTION_MASK;
   VMOCK(&CommonSvcUTMock::ProcessFeatureEnablementCommandMock).Times(0);
   commonSvc->OnInvoked(dummyContextHandle);
   EXPECT_TRUE(commonSvc->GetFeatureEnableStatus() == fakedValidCmd.data.status);

   // Test feature option.
   VMOCK(&util::ChannelContextHandle::GetCommand).WillRepeatedly(FEATURE_OPTION_MSG);
   std::map<std::string, std::vector<uint8>> fakedOptions;
   fakedOptions["fakedKey"] = {1, 2, 3};
   EXPECT_CALL(*commonSvc, ParseFeatureOptionsMock(_))
      .WillOnce([&fakedOptions](util::ChannelContextHandle *context) { return fakedOptions; });
   VMOCK(&CommonSvcUTMock::ProcessFeatureEnablementCommandMock).Times(1);
   commonSvc->OnInvoked(dummyContextHandle);
   EXPECT_TRUE(commonSvc->GetFeatureOptions().size() == fakedOptions.size() &&
               commonSvc->GetFeatureOptions().contains("fakedKey") &&
               commonSvc->GetFeatureOptions()["fakedKey"].size() ==
                  fakedOptions["fakedKey"].size() &&
               commonSvc->GetFeatureOptions()["fakedKey"][0] == fakedOptions["fakedKey"][0] &&
               commonSvc->GetFeatureOptions()["fakedKey"][1] == fakedOptions["fakedKey"][1] &&
               commonSvc->GetFeatureOptions()["fakedKey"][2] == fakedOptions["fakedKey"][2]);
#endif
   delete commonSvc;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CommonSvcUnitTest::TestOnObjectStateChanged --
 *
 *      Test OnObjectStateChanged method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CommonSvcUnitTest, TestOnObjectStateChanged)
{
   CommonSvcUTMock *commonSvc = new NiceMock<CommonSvcUTMock>();
   VMOCK(&CommonSvcUTMock::GetObjectState).WillOnce(VDP_RPC_OBJ_DISCONNECTED);
#if defined(__APPLE__) || defined(_WIN32)
   VMOCK(&CommonSvcUTMock::SendClientFeatureEnablementCapacityMock).Times(0);
#endif
   commonSvc->OnObjectStateChanged();

   VMOCK(&CommonSvcUTMock::GetObjectState).WillOnce(VDP_RPC_OBJ_CONNECTED);
#if defined(__APPLE__) || defined(_WIN32)
   VMOCK(&CommonSvcUTMock::SendClientFeatureEnablementCapacityMock).Times(1);
#endif
   commonSvc->OnObjectStateChanged();
   delete commonSvc;
}


#if defined(__APPLE__) || defined(_WIN32)
/*
 *-----------------------------------------------------------------------------
 *
 * CommonSvcUnitTest::TestSendClientFeatureEnablementCapacity --
 *
 *      Test SendClientFeatureEnablementCapacity method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CommonSvcUnitTest, TestSendClientFeatureEnablementCapacity)
{
   CommonSvcUTMock *commonSvc = new NiceMock<CommonSvcUTMock>();
   bool antiKeyLoggerCommandMatch = false;
   bool antiScreenshotCommandMatch = false;
   EXPECT_CALL(*commonSvc, SendMsg(_, _, _, _))
      .WillOnce(Invoke([&antiKeyLoggerCommandMatch, &antiScreenshotCommandMatch](
                          uint32 id, const char *cookie, const char *msg, uint32 msgLen) {
         FeatureEnablementCommand *cmd = (FeatureEnablementCommand *)msg;
         if (id == FEATURE_ENABLEMENT_MSG && msgLen == sizeof(FeatureEnablementCommand)) {
            antiKeyLoggerCommandMatch = cmd->data.capacity | BLOCK_KEY_LOGGER_MASK;
            antiScreenshotCommandMatch = cmd->data.capacity | BLOCK_SCREEN_CAPTURE_MASK;
         }
         return true;
      }));
   commonSvc->SendClientFeatureEnablementCapacityMock();
   EXPECT_TRUE(antiKeyLoggerCommandMatch);
   EXPECT_TRUE(antiScreenshotCommandMatch);
   delete commonSvc;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CommonSvcUnitTest::TestProcessFeatureEnablementCommand --
 *
 *      Test ProcessFeatureEnablementCommand method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CommonSvcUnitTest, TestProcessFeatureEnablementCommand)
{
   CommonSvcUTMock *commonSvc = new NiceMock<CommonSvcUTMock>();
   bool antiKeyLoggerCommandMatch = false;
   EXPECT_CALL(*commonSvc, NotifyFeatureEnablementMock(_, _, _, _, _))
      .WillRepeatedly(Invoke(
         [&antiKeyLoggerCommandMatch, &commonSvc](uint64 status, uint64 featureMask,
                                                  std::map<std::string, std::vector<uint8>> options,
                                                  RdeChannelMessageType channelType, int msgType) {
            if (status == commonSvc->GetFeatureEnableStatus() &&
                featureMask == BLOCK_KEY_LOGGER_MASK &&
                channelType == RDE_CHANNEL_APP_PROTECTION_MSG &&
                msgType == RDE_CHANNEL_BLOCK_KEY_LOGGER_ENABLE_MSG) {
               antiKeyLoggerCommandMatch = true;
            }
         }));
   commonSvc->ProcessFeatureEnablementCommand();
   EXPECT_TRUE(antiKeyLoggerCommandMatch);

   // Test feature enablement and option for antiScreenshot.
   bool antiScreenshotCommandMatch = false;
   std::map<std::string, std::vector<uint8>> fakedOptions;
   fakedOptions[ALLOW_SCREEN_RECORDING_OPTION_KEY] = {1};
   fakedOptions[ALLOW_ARM_NO_ANTIKEYLOGGER_OPTION_KEY] = {1};
   commonSvc->SetFeatureOptions(fakedOptions);
   EXPECT_CALL(*commonSvc, NotifyFeatureEnablementMock(_, _, _, _, _))
      .WillRepeatedly(Invoke([&antiScreenshotCommandMatch,
                              &commonSvc](uint64 status, uint64 featureMask,
                                          std::map<std::string, std::vector<uint8>> options,
                                          RdeChannelMessageType channelType, int msgType) {
         if (status == commonSvc->GetFeatureEnableStatus() &&
             featureMask == BLOCK_SCREEN_CAPTURE_MASK &&
             channelType == RDE_CHANNEL_APP_PROTECTION_MSG &&
             msgType == RDE_CHANNEL_BLOCK_SCREEN_CAPTURE_ENABLE_MSG &&
             commonSvc->GetFeatureOptions().contains(ALLOW_SCREEN_RECORDING_OPTION_KEY) &&
             commonSvc->GetFeatureOptions()[ALLOW_SCREEN_RECORDING_OPTION_KEY].size() > 0 &&
             commonSvc->GetFeatureOptions()[ALLOW_SCREEN_RECORDING_OPTION_KEY][0] == 1 &&
             commonSvc->GetFeatureOptions().contains(ALLOW_ARM_NO_ANTIKEYLOGGER_OPTION_KEY) &&
             commonSvc->GetFeatureOptions()[ALLOW_ARM_NO_ANTIKEYLOGGER_OPTION_KEY].size() > 0 &&
             commonSvc->GetFeatureOptions()[ALLOW_ARM_NO_ANTIKEYLOGGER_OPTION_KEY][0] == 1) {
            antiScreenshotCommandMatch = true;
         }
      }));
   commonSvc->ProcessFeatureEnablementCommand();
   EXPECT_TRUE(antiScreenshotCommandMatch);

   delete commonSvc;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CommonSvcUnitTest::TestNotifyFeatureEnablement --
 *
 *      Test NotifyFeatureEnablement method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CommonSvcUnitTest, TestNotifyFeatureEnablement)
{
   CommonSvcUTMock *commonSvc = new NiceMock<CommonSvcUTMock>();
   std::map<std::string, std::vector<uint8>> options;
   std::vector<uint8> optionVal = {1, 2, 3};
   options["dummyOptionKey"] = optionVal;
   bool antiKeyLoggerCommandMatch = false;
   EXPECT_CALL(*commonSvc, NotifyRdeCommonVDPObserver(_, _))
      .WillOnce(
         Invoke([&antiKeyLoggerCommandMatch, &optionVal](const char *cookie, const void *msg) {
            RdeChannelMessage *pRdeChannelMsg = (RdeChannelMessage *)msg;
            if (pRdeChannelMsg->msgType !=
                (uint32)RDE_SET_CHANNEL_MSG_TYPE(RDE_CHANNEL_APP_PROTECTION_MSG,
                                                 RDE_CHANNEL_BLOCK_KEY_LOGGER_ENABLE_MSG)) {
               UTConsoleLog("Msg type not match.");
               antiKeyLoggerCommandMatch = false;
               return false;
            }

            uint8 status = 0;
            int offset = 0;
            memcpy(&status, pRdeChannelMsg->payload, sizeof(status));
            if (!status) {
               UTConsoleLog("Msg data not match.");
               antiKeyLoggerCommandMatch = false;
               return false;
            }

            offset += sizeof(status);
            uint32 keySize = 0;
            memcpy(&keySize, pRdeChannelMsg->payload + offset, sizeof(keySize));
            if (keySize != strlen("dummyOptionKey")) {
               UTConsoleLog("Msg key size not match %d.", keySize);
               antiKeyLoggerCommandMatch = false;
               return false;
            }

            offset += sizeof(keySize);
            if (memcmp(pRdeChannelMsg->payload + offset, "dummyOptionKey", keySize)) {
               UTConsoleLog("Msg key value not match.");
               antiKeyLoggerCommandMatch = false;
               return false;
            }

            offset += keySize;
            uint32 valSize = 0;
            memcpy(&valSize, pRdeChannelMsg->payload + offset, sizeof(valSize));
            if (valSize != optionVal.size()) {
               UTConsoleLog("Msg keyVal size not match %d.", keySize);
               antiKeyLoggerCommandMatch = false;
               return false;
            }

            offset += sizeof(valSize);
            if (memcmp(pRdeChannelMsg->payload + offset, optionVal.data(), valSize)) {
               UTConsoleLog("Msg keyVal value not match.");
               antiKeyLoggerCommandMatch = false;
               return false;
            }
            antiKeyLoggerCommandMatch = true;
            return true;
         }));
   commonSvc->NotifyFeatureEnablement(BLOCK_KEY_LOGGER_MASK, BLOCK_KEY_LOGGER_MASK, options,
                                      RDE_CHANNEL_APP_PROTECTION_MSG,
                                      RDE_CHANNEL_BLOCK_KEY_LOGGER_ENABLE_MSG);
   EXPECT_TRUE(antiKeyLoggerCommandMatch);
   delete commonSvc;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CommonSvcUnitTest::TestParseFeatureOptions --
 *
 *      Test ParseFeatureOptions method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CommonSvcUnitTest, TestParseFeatureOptions)
{
   CommonSvcUTMock *commonSvc = new NiceMock<CommonSvcUTMock>();
   // Test invalid context.
   EXPECT_TRUE(commonSvc->ParseFeatureOptionsMock(NULL).empty());

   // Test valid context
   util::ChannelContextHandle *fakedContext = (util::ChannelContextHandle *)0x123;
   std::vector<util::Variant> keys = {
      util::Variant("char"),  util::Variant("short"),  util::Variant("unsigned short"),
      util::Variant("int32"), util::Variant("uint32"), util::Variant("int64"),
      util::Variant("float"), util::Variant("double"), util::Variant("string"),
      util::Variant("blob"),
   };
   VDP_RPC_BLOB blob = {0};
   blob.blobData = (char *)"a blob";
   blob.size = strlen(blob.blobData);
   std::vector<util::Variant> vals = {
      util::Variant('1'),          util::Variant((short)(2)),    util::Variant((unsigned short)(3)),
      util::Variant((int32)(4)),   util::Variant((uint32)(5)),   util::Variant((int64)(6)),
      util::Variant((float)(7.1)), util::Variant((double)(8.2)), util::Variant("a string"),
      util::Variant(&blob),
   };
   VMOCK(&util::ChannelContextHandle::GetParamCount).WillRepeatedly(keys.size() + vals.size());
   int index = 0;
   bool isKey = true;
   VMOCK(&util::ChannelContextHandle::GetParam)
      .WillRepeatedly([&index, &isKey, &keys, &vals](int i, VDP_RPC_VARIANT *copy) {
         *((util::Variant *)copy) = isKey ? keys[index] : vals[index];
         if (!isKey) {
            index++;
         }
         isKey = !isKey;
         return true;
      });
   auto result = commonSvc->ParseFeatureOptionsMock(fakedContext);
   EXPECT_TRUE(result.contains("char") && result["char"].size() == sizeof(char) &&
               result["char"][0] == '1');
   EXPECT_TRUE(result.contains("short") && result["short"].size() == sizeof(short) &&
               *(short *)(result["short"].data()) == 2);
   EXPECT_TRUE(result.contains("unsigned short") &&
               result["unsigned short"].size() == sizeof(unsigned short) &&
               *(unsigned short *)(result["unsigned short"].data()) == 3);
   EXPECT_TRUE(result.contains("int32") && result["int32"].size() == sizeof(int32) &&
               *(int32 *)(result["int32"].data()) == 4);
   EXPECT_TRUE(result.contains("uint32") && result["uint32"].size() == sizeof(uint32) &&
               *(int32 *)(result["uint32"].data()) == 5);
   EXPECT_TRUE(result.contains("int64") && result["int64"].size() == sizeof(int64) &&
               *(int64 *)(result["int64"].data()) == 6);
   EXPECT_TRUE(result.contains("float") && result["float"].size() == sizeof(float) &&
               *(float *)(result["float"].data()) == (float)(7.1));
   EXPECT_TRUE(result.contains("double") && result["double"].size() == sizeof(double) &&
               *(double *)(result["double"].data()) == (double)(8.2));
   EXPECT_TRUE(
      result.contains("string") && result["string"].size() == strlen("a string") &&
      !memcmp(result["string"].data(), std::string("a string").data(), result["string"].size()));
   EXPECT_TRUE(result.contains("blob") && result["blob"].size() == blob.size &&
               !memcmp(result["blob"].data(), blob.blobData, blob.size));
   delete commonSvc;
}
#endif


/*
 *-----------------------------------------------------------------------------
 *
 * CommonSvcUnitTest::TestRequestNetworkStateEnableDisplayGPO --
 *
 *      Test RequestNetworkStateEnableDisplayGPO method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CommonSvcUnitTest, TestRequestNetworkStateEnableDisplayGPO)
{
   CommonSvcUTMock *commonSvc = new NiceMock<CommonSvcUTMock>();
   EXPECT_CALL(*commonSvc, SendMsg(_, _, _, _))
      .WillOnce(Invoke([](uint32 id, const char *cookie, const char *msg, uint32 msgLen) {
         NetworkStateGPOCommand *cmd = (NetworkStateGPOCommand *)msg;
         EXPECT_TRUE(id == NETWORK_STATE_GPO_MSG &&
                     cmd->commandType == NETWORK_STATE_GPO_CMD_REQUEST_ENABLE_DISPLAY &&
                     msgLen == sizeof(NetworkStateGPOCommand));
         return true;
      }));
   commonSvc->RequestNetworkStateEnableDisplayGPO();
   testing::Mock::VerifyAndClearExpectations(commonSvc);
   delete commonSvc;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CommonSvcUnitTest::TestRequestNetworkStateIntervalGPO --
 *
 *      Test RequestNetworkStateIntervalGPO method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CommonSvcUnitTest, TestRequestNetworkStateIntervalGPO)
{
   CommonSvcUTMock *commonSvc = new NiceMock<CommonSvcUTMock>();
   EXPECT_CALL(*commonSvc, SendMsg(_, _, _, _))
      .WillOnce(Invoke([](uint32 id, const char *cookie, const char *msg, uint32 msgLen) {
         NetworkStateGPOCommand *cmd = (NetworkStateGPOCommand *)msg;
         EXPECT_TRUE(id == NETWORK_STATE_GPO_MSG &&
                     cmd->commandType == NETWORK_STATE_GPO_CMD_REQUEST_INTERVAL &&
                     msgLen == sizeof(NetworkStateGPOCommand));
         return true;
      }));
   commonSvc->RequestNetworkStateIntervalGPO();
   testing::Mock::VerifyAndClearExpectations(commonSvc);
   delete commonSvc;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CommonSvcUnitTest::TestProcessNetworkStateEnableDisplayCommand --
 *
 *      Test ProcessNetworkStateEnableDisplayCommand method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CommonSvcUnitTest, TestProcessNetworkStateEnableDisplayCommand)
{
   CommonSvcUTMock *commonSvc = new NiceMock<CommonSvcUTMock>();
   EXPECT_CALL(*commonSvc, NotifyRdeCommonVDPObserver(_, _))
      .WillOnce(Invoke([](const char *cookie, const void *msg) {
         RdeChannelMessage *pRdeChannelMsg = (RdeChannelMessage *)msg;
         EXPECT_TRUE(pRdeChannelMsg->msgType == (uint32)RDE_SET_CHANNEL_MSG_TYPE(
                                                   RDE_CHANNEL_NETWORK_STATE_SETTING_MSG,
                                                   RDE_CHANNEL_NETWORK_STATE_ENABLE_DISPLAY_MSG));
         bool enableDisplay = false;
         memcpy(&enableDisplay, pRdeChannelMsg->payload, sizeof(enableDisplay));
         EXPECT_TRUE(enableDisplay);
         return true;
      }));
   commonSvc->ProcessNetworkStateEnableDisplayCommand(true);

   EXPECT_CALL(*commonSvc, NotifyRdeCommonVDPObserver(_, _))
      .WillOnce(Invoke([](const char *cookie, const void *msg) {
         RdeChannelMessage *pRdeChannelMsg = (RdeChannelMessage *)msg;
         EXPECT_TRUE(pRdeChannelMsg->msgType == (uint32)RDE_SET_CHANNEL_MSG_TYPE(
                                                   RDE_CHANNEL_NETWORK_STATE_SETTING_MSG,
                                                   RDE_CHANNEL_NETWORK_STATE_ENABLE_DISPLAY_MSG));
         bool enableDisplay = true;
         memcpy(&enableDisplay, pRdeChannelMsg->payload, sizeof(enableDisplay));
         EXPECT_FALSE(enableDisplay);
         return true;
      }));
   commonSvc->ProcessNetworkStateEnableDisplayCommand(false);

   testing::Mock::VerifyAndClearExpectations(commonSvc);
   delete commonSvc;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CommonSvcUnitTest::TestProcessNetworkStateIntervalCommand --
 *
 *      Test ProcessNetworkStateIntervalCommand method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CommonSvcUnitTest, TestProcessNetworkStateIntervalCommand)
{
   CommonSvcUTMock *commonSvc = new NiceMock<CommonSvcUTMock>();
   EXPECT_CALL(*commonSvc, NotifyRdeCommonVDPObserver(_, _))
      .WillOnce(Invoke([](const char *cookie, const void *msg) {
         RdeChannelMessage *pRdeChannelMsg = (RdeChannelMessage *)msg;
         EXPECT_TRUE(pRdeChannelMsg->msgType ==
                     (uint32)RDE_SET_CHANNEL_MSG_TYPE(RDE_CHANNEL_NETWORK_STATE_SETTING_MSG,
                                                      RDE_CHANNEL_NETWORK_STATE_INTERVAL_MSG));
         uint32 interval = 0;
         memcpy(&interval, pRdeChannelMsg->payload, sizeof(interval));
         EXPECT_TRUE(interval == 5);
         return true;
      }));
   commonSvc->ProcessNetworkStateIntervalCommand(5);
   testing::Mock::VerifyAndClearExpectations(commonSvc);
   delete commonSvc;
}