/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */


/*
 * vvcDataTransportSwitch.c
 */

#include "vvclibInt.h"
#include "vvcCfg.h"
#include "vvcLog.h"
#include "vvcBandwidthDetectionAPI.h"
#include "vvcMultiAsockBackend.h"
#include "vvcDataTransportSwitch.h"
#include "vvcChannel.h"
#include "fecAsyncSocket.h"
#include "vvcSendQueue.h"
#include "vvcCtrl.h"


void VvcRegisterDataTransportSwitchPollCb(VvcSession *session);

void VvcUnregisterDataTransportSwitchPollCb(VvcSession *session);

void VvcDataTransportSwitchPollCb(void *clientData);

Bool VvcDataTransportSwitchNeeded(VvcSession *session, VvcNetworkStats *networkStats);

Bool VvcDataTransportBeatToTcpSwitchNeeded(VvcSession *session, VvcNetworkStats *networkStats);

Bool VvcDataTransportTcpToBeatSwitchNeeded(VvcSession *session, VvcNetworkStats *networkStats);

double VvcGetRttVariancePercentage(VvcNetworkStats *networkStats);

Bool VvcDataTransportShouldActOnSwitch(VvcSession *session, Bool switchNeeded, Bool isTCP);


/*
 *-----------------------------------------------------------------------------
 *
 * VvcDataTransportSwitch_Start --
 *
 *    Determine if DataTransportSwitch is applicable for this VvcSession.
 *    If yes, then Start DataTransportSwitch which involves -
 *    - Reading the trigger Profile from config/registry
 *    - Starting the DataTransportSwitchPollCb.
 *
 *    Note1: This function is invoked from VvcCtrlOnSessionEstablished().
 *    Note2: This function should be called if negotiatedDoConcurrentTransports
 *           is TRUE.
 *
 * Results:
 *    Bool - TRUE if VvcDataTransportSwitch Started, FALSE otherwise.
 *
 * Side effects:
 *    session->dataTransportSwitch is initialized.
 *
 *-----------------------------------------------------------------------------
 */

Bool
VvcDataTransportSwitch_Start(VvcSessionHandle sessionHandle) // IN
{
   VvcSession *session = (VvcSession *)sessionHandle;
   VvcAsockBackend *ctrlAsockBe = NULL;

   ASSERT(session);
   ASSERT(!ISLOCKED_SESSION(session));

   LOCK_SESSION(session);

   if (session->state != VvcSessionEstablished) {
      VvcError("Session: %p is not established, state: %d.\n", session, session->state);
      UNLOCK_SESSION(session);
      return FALSE;
   }

   ASSERT(session->negotiatedDoConcurrentTransports);

   /*
    * VvcSession::dataTransportSwitch must already be allocated and initiated
    * before VvcDataTransportSwitch_Start().
    * Server-Side: Applications will push SwitchPolicy to VVC.
    * Client-Side: OP_INIT_ACK will contain SwitchPolicy as an ExtHeader.
    */
   if (!session->dataTransportSwitch) {
      VvcError("VvcSession::dataTransportSwitch is not initialized, "
               "session: %p.\n",
               session);
      UNLOCK_SESSION(session);
      return FALSE;
   }

   /*
    * If CtrlAsock can not estimate Bandwidth, then we will be using Vvc's
    * Bw Estimator for that.
    */

   ctrlAsockBe = VvcGetControlAsockBackend(session);
   if (ctrlAsockBe != NULL) {
      session->dataTransportSwitch->isVvcBweUsed = !ctrlAsockBe->isEndToEndConnection;
      VVC_RELEASE_ASOCKBE(ctrlAsockBe, VvcTagAsockBeGeneric);
      ctrlAsockBe = NULL;
   } else {
      session->dataTransportSwitch->isVvcBweUsed = FALSE;
   }

   session->dataTransportSwitch->doForceSwitch = FALSE;

   /*
    * We are about to complete VvcDataTransportSwitch_Start().
    * Make sure that when we start switchPollCb, we start with a BEAT Socket
    * as Active AsockBe.
    *
    * There are two scenarios via which ForceSwitch is requested:
    *
    * 1. VvcDataTransportSwitch_Start() has completed *before* BEAT AsockBe
    *    gets pushed.
    *    In this case, ForceSwitch immediately in the AddAsockBe of BEAT.
    *    See vvcMultiAsockBackend.c::VvcAddAsockBackend().
    *
    * 2. VvcDataTransportSwitch_Start() is completing *after* BEAT AsockBe
    *    gets pushed.
    *    In this case, do a ForceSwitch here.
    */

   if (VvcGetNumAsockBackends(session) > 1 && VvcIsActiveAsockBackendTCP(session)) {
      VvcLog("%s: Requesting a ForceSwitch, session: %p.\n", __FUNCTION__, session);
      VvcDataTransportsSwitch_ForceSwitch(session);
   }

   /*
    * Split long line log to avoid crash on Android
    * TODO: Rollback the change once bora/lib/log issue is fixed for Andriod
    * See bug 2318075
    */
   VvcLog("%s: DataTransportSwitch Started for VvcSession: %p, "
          "isVvcBweUsed for TCP: %s, "
          "dataTransportSwitchCbPeriodMS: %.2f, "
          "beatToTcp Bw in Kbps: %.2f, "
          "beatToTcp Bw in KBps: %.2f, "
          "beatToTcp PktLossPercentage: %.2f, "
          "beatToTcp RttMS: %.2f, "
          "beatToTcp RttVarPercentage: %.2f, "
          "\n",
          __FUNCTION__, session, session->dataTransportSwitch->isVvcBweUsed == TRUE ? "Yes" : "No",
          session->dataTransportSwitch->dataTransportSwitchCbPeriodMS,
          session->dataTransportSwitch->beatToTcpBwKbps,
          session->dataTransportSwitch->beatToTcpBwKbps / 8,
          session->dataTransportSwitch->beatToTcpPktLossPercentage,
          session->dataTransportSwitch->beatToTcpRttMS,
          session->dataTransportSwitch->beatToTcpRttVarPercentage);
   VvcLog("%s: tcpToBeat Bw in Kbps: %.2f, "
          "tcpToBeat Bw in KBps: %.2f, "
          "tcpToBeat RttVarPercentage: %.2f, "
          "tcpToBeat RttMS: %.2f, "
          "beatToTcp Threshold: %u, "
          "tcpToBeat Threshold: %u, "
          "isSwitchingAlwaysEnabled: %s, "
          "switchCountMax: %d. "
          "\n",
          __FUNCTION__, session->dataTransportSwitch->tcpToBeatBwKbps,
          session->dataTransportSwitch->tcpToBeatBwKbps / 8,
          session->dataTransportSwitch->tcpToBeatRttVarPercentage,
          session->dataTransportSwitch->tcpToBeatRttMS,
          session->dataTransportSwitch->beatToTcpThreshold,
          session->dataTransportSwitch->tcpToBeatThreshold,
          session->dataTransportSwitch->isSwitchingAlwaysEnabled == TRUE ? "Yes" : "No",
          session->dataTransportSwitch->switchCountMax);

   // Start pollCb to do DataTransportSwitching
   VvcRegisterDataTransportSwitchPollCb(session);

   UNLOCK_SESSION(session);

   return TRUE;
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcDataTransportSwitch_Stop --
 *
 *    Stop the DataTransportSwitch if already started for a VvcSession.
 *    Stopping DataTransportSwitch involves -
 *    - De-allocate session->dataTransportSwitch
 *    - Stop the DataTransportSwitchPollCb.
 *
 *    Note: This function is invoked from VvcMultiAsockBackendClose().
 *
 * Results:
 *    Bool - TRUE if VvcDataTransportSwitch Stopped, FALSE otherwise.
 *
 * Side effects:
 *    session->dataTransportSwitch is free-ed.
 *
 *-----------------------------------------------------------------------------
 */

Bool
VvcDataTransportSwitch_Stop(VvcSessionHandle sessionHandle) // IN
{
   VvcSession *session = (VvcSession *)sessionHandle;

   ASSERT(session);
   ASSERT(session->negotiatedDoConcurrentTransports);

   LOCK_SESSION(session);

   if (!session->dataTransportSwitch) {
      UNLOCK_SESSION(session);
      return FALSE;
   }

   if (!session->stoppingDataTransportSwitch) {
      VvcUnregisterDataTransportSwitchPollCb(session);
   }

   UNLOCK_SESSION(session);

   return TRUE;
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcDataTransportSwitch_ResetCurrentSwitchCount --
 *
 *    Reset the switchCountCurr to zero.
 *    Upon NC, when network is reconnected, we reset the switchCountCurr.
 *
 * Results:
 *    None.
 *
 * Side effects:
 *    session->dataTransportSwitch->swirchCountCurr is is reset to zero.
 *
 *-----------------------------------------------------------------------------
 */

void
VvcDataTransportSwitch_ResetCurrentSwitchCount(VvcSessionHandle sessionHandle) // IN
{
   VvcSession *session = (VvcSession *)sessionHandle;
   Bool isSessionLocked = FALSE;

   ASSERT(session);
   ASSERT(session->negotiatedDoConcurrentTransports);

   isSessionLocked = ISLOCKED_SESSION(session);

   if (!isSessionLocked) {
      LOCK_SESSION(session);
   }

   if (!session->dataTransportSwitch) {
      goto exit;
   }

   session->dataTransportSwitch->switchCountCurr = 0;

exit:
   if (!isSessionLocked) {
      UNLOCK_SESSION(session);
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcDataTransportSwitch_SetSwitchingPolicy --
 *
 *    Set the Switching Policy Params to a VvcSession::dataTransportSwitch.
 *    Agent-Side: Invoked by public API VVCLIB_SetSwitchingPolicy().
 *                BlastSockets layer calls this public API to push the Policy.
 *    Client-Side: OP_INIT_ACK contains an Extension which has the Policy params
 *                 VvcCtrl layer will invoke this func when the extension is
 *                 received.
 *
 * Results:
 *    Returns TRUE if Transport Switch Policy is Set, FALSE otherwise.
 *
 * Side effects:
 *    Members of VvcSession::dataTransportSwitch will be updated.
 *
 *-----------------------------------------------------------------------------
 */

Bool
VvcDataTransportSwitch_SetSwitchingPolicy(VvcSessionHandle sessionHandle,            // IN
                                          VvcDataTransportSwitchPolicyParams params) // IN
{
   VvcSession *session = (VvcSession *)sessionHandle;
   Bool ok = FALSE;
   Bool isSessionLocked = FALSE;

   ASSERT(session);

   isSessionLocked = ISLOCKED_SESSION(session);

   if (!isSessionLocked) {
      LOCK_SESSION(session);
   }

   if (session->dataTransportSwitch) {
      VvcError("VvcSession already has SwitchPolicy configured. "
               "SwitchPolicy can be set only once for a Session's lifetime.\n");
      goto exit;
   }

   // Allocate the VvcSession::dataTransportSwitch now
   session->dataTransportSwitch = Util_SafeCalloc(1, sizeof *(session->dataTransportSwitch));

   // Record swithPollCb time interval
   if (params.transportSwitchCbPeriodMS < 1000) {
      params.transportSwitchCbPeriodMS = 1000;
   }

   session->dataTransportSwitch->dataTransportSwitchCbPeriodMS = params.transportSwitchCbPeriodMS;

   // Record beatToTcp trigger parameters
   session->dataTransportSwitch->beatToTcpBwKbps = params.beatToTcpBwKbps;
   session->dataTransportSwitch->beatToTcpPktLossPercentage = params.beatToTcpPktLossPercentage;
   session->dataTransportSwitch->beatToTcpRttMS = params.beatToTcpRttMS;
   session->dataTransportSwitch->beatToTcpRttVarPercentage = params.beatToTcpRttVarPercentage;

   // Record tcpToBeat trigger parameters
   session->dataTransportSwitch->tcpToBeatBwKbps = params.tcpToBeatBwKbps;
   session->dataTransportSwitch->tcpToBeatRttVarPercentage = params.tcpToBeatRttVarPercentage;
   session->dataTransportSwitch->tcpToBeatRttMS = params.tcpToBeatRttMS;

   // Record threshold values for switching
   session->dataTransportSwitch->beatToTcpThreshold = params.beatToTcpThreshold;
   session->dataTransportSwitch->tcpToBeatThreshold = params.tcpToBeatThreshold;
   session->dataTransportSwitch->currSwitchNeededCnt = 0;

   // Record if diagnostic switching is enabled or not
   session->dataTransportSwitch->isSwitchingAlwaysEnabled = params.isSwitchingAlwaysEnabled;

   // Record the max allowed switches and initialize switchCountCurr to 0
   session->dataTransportSwitch->switchCountMax = params.switchCountMax;
   session->dataTransportSwitch->switchCountCurr = 0;

   ok = TRUE;

exit:
   if (!isSessionLocked) {
      UNLOCK_SESSION(session);
   }

   return ok;
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcDataTransportSwitch_GetSwitchingPolicy --
 *
 *    Get the Switching Policy Params from a VvcSession::dataTransportSwitch.
 *    Server-Side: BlastSockets uses public API VVCLIB_SetSwitchingPolicy() to
 *                 Set the SwitchPolicy.
 *                 While constructing OP_INIT_ACK, Agent-Side will uses
 *                 VvcDataTransportSwitch_GetSwitchingPolicy() and constructs
 *                 a Ext Header consisting of SwitchPolicy Params and sends it
 *                 to Client.
 *    Client-Side: This API is currently not required to be used on Client-Side.
 *    Note: See func VvcDataTransportSwitch_SetSwitchingPolicy() for details.
 *
 * Results:
 *    Returns TRUE if Transport Switch Policy is Get as a params struct,
 *    FALSE otherwise.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

Bool
VvcDataTransportSwitch_GetSwitchingPolicy(VvcSessionHandle sessionHandle,             // IN
                                          VvcDataTransportSwitchPolicyParams *params) // OUT
{
   VvcSession *session = (VvcSession *)sessionHandle;
   Bool ok = FALSE;
   Bool isSessionLocked = FALSE;

   ASSERT(session);
   ASSERT(params);

   isSessionLocked = ISLOCKED_SESSION(session);

   if (!isSessionLocked) {
      LOCK_SESSION(session);
   }

   if (!session->dataTransportSwitch) {
      VvcError("VvcSession's DataTransportSswitch hasn't Started. "
               "Use VvcDataTransportSwitch_Start() first, "
               "session: %p.\n",
               session);
      goto exit;
   }

   // Record swithPollCb time interval
   params->transportSwitchCbPeriodMS = session->dataTransportSwitch->dataTransportSwitchCbPeriodMS;

   // Record beatToTcp trigger parameters
   params->beatToTcpBwKbps = session->dataTransportSwitch->beatToTcpBwKbps;
   params->beatToTcpPktLossPercentage = session->dataTransportSwitch->beatToTcpPktLossPercentage;
   ;
   params->beatToTcpRttMS = session->dataTransportSwitch->beatToTcpRttMS;
   params->beatToTcpRttVarPercentage = session->dataTransportSwitch->beatToTcpRttVarPercentage;

   // Record tcpToBeat trigger parameters
   params->tcpToBeatBwKbps = session->dataTransportSwitch->tcpToBeatBwKbps;
   params->tcpToBeatRttVarPercentage = session->dataTransportSwitch->tcpToBeatRttVarPercentage;
   params->tcpToBeatRttMS = session->dataTransportSwitch->tcpToBeatRttMS;

   // Record threshold values for switching
   params->beatToTcpThreshold = session->dataTransportSwitch->beatToTcpThreshold;
   params->tcpToBeatThreshold = session->dataTransportSwitch->tcpToBeatThreshold;

   // Record if diagnostic switching is enabled or not
   params->isSwitchingAlwaysEnabled = session->dataTransportSwitch->isSwitchingAlwaysEnabled;

   // Record the max allowed switches and initialize switchCountCurr to 0
   params->switchCountMax = session->dataTransportSwitch->switchCountMax;

   ok = TRUE;

exit:
   if (!isSessionLocked) {
      UNLOCK_SESSION(session);
   }

   return ok;
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcDataTransportsSwitch_ForceSwitch --
 *
 *    Ask to do a Forced Switch in the next invocation of switchPollCb.
 *    A Forced Switch is not counted towards "dataTransportSwitchCountMax".
 *    In other words, only the switches decided by BENIT are counted towards
 *    switchCountMax.
 *    Currently, ForceSwitch is invoked in response to WaitForBEAT=FALSE.
 *    If a BEAT AsockBe is pushed to VvcSession after the session has gone to
 *    Established state (i.e. after the OP_INIT exchange), then use ForceSwitch
 *    to start using the BEAT AsockBe.
 *
 * Results:
 *    Returns TRUE if request to Force a switch was put in, FALSE otherwise.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

Bool
VvcDataTransportsSwitch_ForceSwitch(VvcSessionHandle sessionHandle) // IN
{
   VvcSession *session = (VvcSession *)sessionHandle;
   Bool ok = FALSE;
   Bool isSessionLocked = FALSE;

   ASSERT(session);

   isSessionLocked = ISLOCKED_SESSION(session);

   if (!isSessionLocked) {
      LOCK_SESSION(session);
   }

   if (!session->dataTransportSwitch) {
      VvcError("VvcSession's DataTransportSswitch hasn't Started. "
               "Use VvcDataTransportSwitch_Start() first, "
               "session: %p.\n",
               session);
      goto exit;
   }

   session->dataTransportSwitch->doForceSwitch = TRUE;
   ok = TRUE;

exit:
   if (!isSessionLocked) {
      UNLOCK_SESSION(session);
   }

   VvcLog("Request to do a ForceSwitch: %s.\n", ok ? "Succeeded" : "Failed");
   return ok;
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcRegisterDataTransportSwitchPollCb --
 *
 *    Register the transport switch poll callback using the poll library.
 *
 * Results:
 *    None.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

void
VvcRegisterDataTransportSwitchPollCb(VvcSession *session)
{
   VvcInstance *instance = session->instance;
   VvcStatus status;
   VvcStatus (*pollCallbackFn)(VvcInstancePollCb callback, void *clientData, Bool periodic,
                               uint32 timeoutUs) = NULL;
   uint32 dataTransportSwitchCbPeriodUS;

   ASSERT(session);
   ASSERT(ISLOCKED_SESSION(session));
   ASSERT(session->negotiatedDoConcurrentTransports);
   ASSERT(session->dataTransportSwitch);

   if (session->stoppingDataTransportSwitch) {
      VvcWarning("%s: Session %p (sessionId %d): Data transport switch cannot "
                 "restart once stopped\n",
                 __FUNCTION__, session, session->sessionId);
      return;
   }

   LOCK_INSTANCE(instance);
   pollCallbackFn = instance->instanceBe.pollCallback;
   UNLOCK_INSTANCE(instance);

   if (pollCallbackFn == NULL) {
      VvcLog("%s: instanceBe's pollCallback is NULL, VVC is "
             "probably uninitializing. Don't register transport pollCb for "
             "session %u instance %s\n",
             __FUNCTION__, session->sessionId, instance->name);
      return;
   }

   dataTransportSwitchCbPeriodUS =
      session->dataTransportSwitch->dataTransportSwitchCbPeriodMS * MILLI_SEC_IN_SEC;

   /*
    * XXX Unlock/relock session because poll library uses a lower-rank
    * lock. Also grab an extra session ref for the poll callback's clientData.
    */
   VVC_ADD_REF_SESSION(session, VvcTagTransportSwitchPollCb);

   UNLOCK_SESSION(session);
   status = pollCallbackFn(VvcDataTransportSwitchPollCb,
                           session, // clientData
                           FALSE,   // isPeriodic
                           dataTransportSwitchCbPeriodUS);
   LOCK_SESSION(session);

   if (!VVC_SUCCESS(status)) {
      VvcError("Failed to register VvcDataTransportSwitchPollCb, "
               "instance: %s, session:%p, sessionId: %d, status: %d\n",
               instance->name, session, session->sessionId, status);

      /* undo the ref-count */
      VVC_RELEASE_SESSION(session, VvcTagTransportSwitchPollCb);
   } else {
      VvcDebug("Registered VvcDataTransportSwitchPollCb, "
               "instance: %s, session:%p, sessionId: %d.\n",
               instance->name, session, session->sessionId);
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcUnregisterDataTransportSwitchPollCb --
 *
 *    Unregister the VvcDataTransportSwitchPollCb.
 *
 * Results:
 *    None.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

void
VvcUnregisterDataTransportSwitchPollCb(VvcSession *session) // IN
{
   VvcInstance *instance;
   VvcStatus status = VVC_STATUS_ERROR;
   VvcStatus (*pollRemoveFn)(VvcInstancePollCb callback, void *clientData, Bool periodic) = NULL;

   ASSERT(session);
   instance = session->instance;

   ASSERT(ISLOCKED_SESSION(session));
   ASSERT(session->negotiatedDoConcurrentTransports);

   session->stoppingDataTransportSwitch = TRUE;

   ASSERT(!ISLOCKED_INSTANCE(instance));
   LOCK_INSTANCE(instance);
   pollRemoveFn = instance->instanceBe.pollRemove;
   UNLOCK_INSTANCE(instance);

   if (pollRemoveFn == NULL) {
      VvcLog("%s: instanceBe's pollRemove function is NULL, VVC is "
             "probably uninitializing. Nothing to do: session %u instance %s\n",
             __FUNCTION__, session->sessionId, instance->name);
      return;
   }

   /*
    * XXX Unlock/relock session because poll library's lock is lower rank than
    * session lock.
    */
   UNLOCK_SESSION(session);
   status = pollRemoveFn(VvcDataTransportSwitchPollCb,
                         session, // clientData
                         FALSE);  // isPeriodic
   LOCK_SESSION(session);

   if (!VVC_SUCCESS(status)) {
      VvcLog("%s: VvcDataTransportSwitchPollCb is not registered, or "
             "has already been unregistered, instance: %s, session:%p, "
             "sessionId: %d, status: %d\n",
             __FUNCTION__, instance->name, session, session->sessionId, status);
      return;
   }

   VvcLog("%s: Unregistered VvcDataTransportSwitchPollCb, instance: %s, "
          "session:%p, sessionId: %d\n",
          __FUNCTION__, instance->name, session, session->sessionId);

   /* Release the session ref-count added when switchPollCb was registered */
   VVC_RELEASE_SESSION(session, VvcTagTransportSwitchPollCb);
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcRawChannelTransportSwitch --
 *
 *    Initiate protocol switch for all active raw channels.
 *
 * Results:
 *    None.
 *
 * Side effects:
 *    BENIT selected protocol switch initiated for active raw channels.
 *
 *-----------------------------------------------------------------------------
 */

static void
VvcRawChannelTransportSwitch(VvcSession *session, VvcRawChanType switchTo)
{
   VvcChannel *channel;
   int nChannels;
   void *connectOp;
   size_t connectOpLen;
   DblLnkLst_Links *link;
   Bool isServer, msgQueued = FALSE;

   nChannels = session->numRawChannels;

   VvcDebug("%s: Switch %d raw channels to %s", __FUNCTION__, nChannels,
            switchTo == VVC_RAW_CHAN_TCP ? "TCP" : "BEAT");

   DblLnkLst_ForEach(link, &session->channelList)
   {
      channel = DblLnkLst_Container(link, VvcChannel, sessionLink);
      if (VVC_RAW_CHANNEL(channel) && channel->state == VvcChannelOpen) {

         /*
          * Careful, channel state OPEN or numRawChannels counter does not
          * guarantee channel is operational with connected asyncsockets.
          */
         if (UNLIKELY(!channel->rawCtx->asock)) {
            VvcLog("Skip channel %s for protocol switch, no active socket", channel->name);
            goto loop;
         }

         /*
          * The first switch also needs to establish the TCP connection.  Subsequent switches have
          * both connections available and can immediate send the switch op to the peer with the
          * choice of protocol and the cutoff byte count.
          */
         if (!channel->rawCtx->bweAsock) { // <-- first switch
            /*
             * Server needs to tell the client to connect if it needs to switch.
             * If the client does the switch first, then it will just go ahead with the
             * connect, ie. the client should never send this Connect control op.
             */
            isServer = channel->session->transportBe.flags & VVC_TRANSPORT_BE_SERVER;
            if (isServer) {
               VvcLog("%s: Request client to initiate TCP connection for raw channel %s",
                      __FUNCTION__, channel->name);
               connectOp =
                  VvcBuildRawConnectOp(session, channel->channelId, switchTo, &connectOpLen);
               VvcQueueMessage(session->ctrlChannel, connectOp, connectOpLen, FALSE,
                               VvcDefaultCtrlMsg, 0, 0);
               msgQueued |= TRUE;
            } else {
               /*
                * Client does not initiate the first switch, and in general always follows the agent
                * side protocol switch. This is an optimization for the server to install recv() on
                * the new TCP socket knowing that client will not switch  until it gets the
                * SWITCH_ACK when it does iniitiate a switch in future.
                */
               VvcLog("%s: Defer protocol switch for channel %s until server switch", __FUNCTION__,
                      channel->name);
            }
            channel->rawCtx->switchInitiator = TRUE;
         } else { // <-- subsequent switches
            /*
             * This path is taken when peer has already setup the TCP
             * connection on its switch.
             */
            VvcLog("%s: Both protocols established", __FUNCTION__);

            // BEAT socket pointer should be set earlier when bweAsock was ready
            ASSERT(channel->rawCtx->beatAsock);
            if (!channel->rawCtx->beatAsock) {
               ASSERT(FECAsyncSocket_IsFecSocket(channel->rawCtx->asock));
               channel->rawCtx->beatAsock = channel->rawCtx->asock;
            }

            msgQueued |= VvcRawChannelSwitchProtocol(channel, switchTo);
         }
      loop:
         if (--nChannels == 0) {
            break;
         }
      }
      if (msgQueued) {
         VvcDispatchSendQueues(session, VvcDispatchSendTriggerOnCtrl);
      }
   }
}

/*
 *-----------------------------------------------------------------------------
 *
 * VvcDataTransportSwitchPollCb --
 *
 *    This is pollCb which is invoked every VVC_DATATRANSPORTSWITCHCB_PERIOD_MS
 *    1. Get the NetworkStats from the current Active AsockBe.
 *    2. Decide if Active AsockBe needs to be switched.
 *    3. Switch the active AsockBe based on result of 2 above.
 *
 * Results:
 *    None.
 *
 * Side effects:
 *    Active AsockBe might be switched after returning from this func.
 *
 *-----------------------------------------------------------------------------
 */

void
VvcDataTransportSwitchPollCb(void *clientData) // IN
{
   VvcSession *session = clientData;
   VvcStatus status = VVC_STATUS_ERROR;
   VvcNetworkStats networkStats;
   Bool shouldReregisterPoll = TRUE;
   Bool forcedSwitch;

   ASSERT(session);
   ASSERT(!ISLOCKED_SESSION(session));
   ASSERT(session->negotiatedDoConcurrentTransports);

   LOCK_SESSION(session);

   if (session->stoppingDataTransportSwitch) {
      VvcDebug("%s: VvcSession %p (sessionId %d): Data transport is being "
               "stopped, bailing out\n",
               __FUNCTION__, session, session->sessionId);
      shouldReregisterPoll = FALSE;
      goto exit;
   }

   if (VvcGetNumAsockBackends(session) == 0) {
      VvcDebug("%s: VvcSession: %p has no AsockBackends, DataTransportSwitch is"
               " Not Applicable currently.\n",
               __FUNCTION__, session);
      goto exit;
   }

   // VvcGetNetworkStatsFromTransportBe() internally unlocks/relocks session.
   status = VvcGetNetworkStatsFromTransportBe(session, &networkStats);

   if (status == VVC_STATUS_NOT_FOUND) {

      ASSERT(session->dataTransportSwitch->isVvcBweUsed);

      /*
       * If current Active AsockBe can not estimate Bw, then get
       * the networkStats from Vvc's Bw Estimator.
       */

      networkStats.bandwidthBytesPerSecond = VvcBandwidthDetection_GetImmediateBandwidth(
         session->bwDetection, VVC_BANDWIDTH_APPLY_MULTIPLIER | VVC_BANDWIDTH_APPLY_MINIMUM_RATE);
      networkStats.rttSeconds = VvcBandwidthDetection_GetSmoothedRTT(session->bwDetection);
      /*
       * rttVariance & packetLoss are not calculated & exposed by VVC's
       * Bw Estimator.
       */
      networkStats.rttVarianceSeconds = 0;
      networkStats.packetLossPercent = 0;

      status = VVC_STATUS_SUCCESS;
   }

   if (status != VVC_STATUS_SUCCESS) {
      VvcError("%s: Failed to get NetworkStats.\n", __FUNCTION__);
      goto exit;
   }

   /*
    * Raw channels, when present, can have more activity than session transport.
    * "VVC" type raw channel honor BENIT choice of protocol. For switch
    * determination, if protocols match (most likely they will), pick stats
    * with greater b/w between session and cached max b/w of raw channels.
    */
   if (session->cachedRawChannelId && session->rawChannelType == VVC_RAW_CHAN_VVC) {
      VvcChannel *channel = VvcGetChannelById(session, session->cachedRawChannelId);
      if (channel) {
         AsyncSocket *asock;
         AsyncSocketNetworkStats asockStats;
         VvcNetworkStats netStats;
         ASSERT(channel->rawCtx && channel->rawCtx->asock);

         asock = channel->rawCtx->asock;

         VvcDebug("%s: compare raw channel and session bw (session %f, raw %f)"
                  "raw-beat: %s, session-beat: %s",
                  __FUNCTION__, networkStats.bandwidthBytesPerSecond, session->cachedBW,
                  FECAsyncSocket_IsFecSocket(asock) ? "yes" : "no",
                  VvcIsActiveAsockBackendTCP(session) ? "no" : "yes");

         if (FECAsyncSocket_IsFecSocket(asock) == !VvcIsActiveAsockBackendTCP(session) &&
             session->cachedBW > networkStats.bandwidthBytesPerSecond) {
            UNLOCK_SESSION(session);
            int ret = AsyncSocket_GetNetworkStats(asock, &asockStats);
            LOCK_SESSION(session);

            if (session->stoppingDataTransportSwitch) {
               VvcDebug("%s: VvcSession %p (sessionId %d): Data transport is being "
                        "stopped, bailing out\n",
                        __FUNCTION__, session, session->sessionId);
               shouldReregisterPoll = FALSE;
               goto exit;
            }

            if (ret == ASOCKERR_SUCCESS) {
               VvcNwStatsFromAsockStats(&asockStats, &netStats);
               if (netStats.bandwidthBytesPerSecond > networkStats.bandwidthBytesPerSecond) {
                  networkStats = netStats;
               }
            }
         }
      }
   }

   forcedSwitch = session->dataTransportSwitch->doForceSwitch;

   if (VvcDataTransportSwitchNeeded(session, &networkStats)) {
      // Switch provided we have 2 AsockBackends
      if (VvcGetNumAsockBackends(session) != 2 || session->activeAsockBackendIndex == -1) {
         VvcLog("%s: VvcSession: %p has %d AsockBackends, "
                "activeAsockBackendIndex %d, "
                "DataTransportSwitch is Not Applicable.\n",
                __FUNCTION__, session, session->numAsockBackends, session->activeAsockBackendIndex);
         goto exit;
      }

      VvcLog("%s: Switching %s for session: %p, sessionId: %d\n", __FUNCTION__,
             VvcIsActiveAsockBackendTCP(session) ? "TCP -> BEAT" : "BEAT -> TCP", session,
             session->sessionId);

      // save switchTo protocol before changing activeAsockBackendIndex
      VvcRawChanType switchTo =
         VvcIsActiveAsockBackendTCP(session) ? VVC_RAW_CHAN_BEAT : VVC_RAW_CHAN_TCP;

      // Switch the activeAsockBackendIndex
      session->activeAsockBackendIndex = session->activeAsockBackendIndex ^ 1;

      // Iterate over all raw VVC-type channels and initiate protocol switch
      if (session->numRawChannels && (session->rawChannelType == VVC_RAW_CHAN_VVC) &&
          !forcedSwitch) {
         VvcRawChannelTransportSwitch(session, switchTo);
      }
   }

   // disable switch poll on max switches
   if (session->dataTransportSwitch->switchCountCurr ==
       session->dataTransportSwitch->switchCountMax) {
      session->stoppingDataTransportSwitch = TRUE;
      shouldReregisterPoll = FALSE;
   }

exit:
   // Re-register the transport switch poll function.
   if (shouldReregisterPoll) {
      VvcRegisterDataTransportSwitchPollCb(session);
   }

   UNLOCK_SESSION(session);

   // Release the session ref-count added when the pollCb was registered.
   VVC_RELEASE_SESSION(session, VvcTagTransportSwitchPollCb);

   return;
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcDataTransportSwitchNeeded --
 *
 *    Decide if Active AsockBe needs to be switched or not
 *
 * Results:
 *    Returns TRUE if switch is needed, FALSE otherwise.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

Bool
VvcDataTransportSwitchNeeded(VvcSession *session,           // IN
                             VvcNetworkStats *networkStats) // IN
{
   Bool ok = FALSE;
   Bool isTCP = FALSE;

   ASSERT(session);
   ASSERT(networkStats);
   ASSERT(ISLOCKED_SESSION(session));

   /*
    * A Forced Switch is not counted towards "dataTransportSwitchCountMax".
    * In other words, only the switches decided by BENIT are counted towards
    * switchCountMax.
    */

   if (session->dataTransportSwitch->doForceSwitch == TRUE) {
      VvcLog("ForceSwitch for VvcSssion: %p.\n", session);
      session->dataTransportSwitch->doForceSwitch = FALSE;
      return TRUE;
   }

   /*
    * If switchCountMax switches have already happened, further switches are
    * not allowed regardless of network conditions.
    */
   if (session->dataTransportSwitch->switchCountMax != -1 &&
       (session->dataTransportSwitch->switchCountCurr + 1 >
        session->dataTransportSwitch->switchCountMax)) {
      return FALSE;
   }

   isTCP = VvcIsActiveAsockBackendTCP(session);

   VvcTrace("%s: session: %p, sessionId: %d, "
            "activeAsockBackendIndex: %d, (%s) "
            "bandwidthKiloBytesPerSecond: %.2f, "
            "packetLossPercent: %.2f, "
            "rttSeconds: %.4f, "
            "rttVarianceSeconds: %.4f, "
            "rttVariancePercentage: %.2f%%\n",
            __FUNCTION__, session, session->sessionId, session->activeAsockBackendIndex,
            isTCP ? "TCP" : "BEAT", networkStats->bandwidthBytesPerSecond / 1000.0,
            networkStats->packetLossPercent, networkStats->rttSeconds,
            networkStats->rttVarianceSeconds, VvcGetRttVariancePercentage(networkStats));

   /*
    * If Diagnostic Mode Switching is Enabled, then switch the active
    * dataTransport regardless of network conditions.
    */

   if (session->dataTransportSwitch->isSwitchingAlwaysEnabled) {
      session->dataTransportSwitch->switchCountCurr += 1;
      return TRUE;
   }

   if (isTCP) {
      /* If currently on TCP, check if we should switch to BEAT. */
      ok = VvcDataTransportTcpToBeatSwitchNeeded(session, networkStats);
   } else {
      /* If currently on BEAT, check if we should switch to TCP. */
      ok = VvcDataTransportBeatToTcpSwitchNeeded(session, networkStats);
   }

   if (ok) {
      session->dataTransportSwitch->switchCountCurr += 1;
   }

   return ok;
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcDataTransportBeatToTcpSwitchNeeded --
 *
 *    Decide if Active AsockBe needs to be switched from BEAT to TCP.
 *    XXX: As we refine the switching decision tree based on experiments,
 *         this func should change/evolve in the future.
 *
 * Results:
 *    Returns TRUE if switch is needed, FALSE otherwise.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

Bool
VvcDataTransportBeatToTcpSwitchNeeded(VvcSession *session,           // IN
                                      VvcNetworkStats *networkStats) // IN
{
   Bool ok = FALSE;

   Bool bwTrigger = FALSE;
   double bwKBps = 0.0;

   Bool pktLossTrigger = FALSE;
   double pktLossPercentage = 0.0;

   Bool rttTrigger = FALSE;
   double rttMS = 0.0;

   Bool rttVarPercentageTrigger = FALSE;
   double rttVarPercentage = 0.0;

   ASSERT(session);
   ASSERT(networkStats);
   ASSERT(ISLOCKED_SESSION(session));

   ASSERT(!VvcIsActiveAsockBackendTCP(session));
   ASSERT(session->dataTransportSwitch);

   bwKBps = session->dataTransportSwitch->beatToTcpBwKbps / 8;
   pktLossPercentage = session->dataTransportSwitch->beatToTcpPktLossPercentage;
   rttMS = session->dataTransportSwitch->beatToTcpRttMS;
   rttVarPercentage = session->dataTransportSwitch->beatToTcpRttVarPercentage;

   /* Switch BEAT to TCP (as per default values - can be overwritten by config):
    *     Bw is >= 25000 KBps i.e 25 MBps i.e. 200 Mbps
    *     PktLoss is <= 0.1% &&
    *     Rtt is <= 2ms
    *
    * For non-zero but small rttVarPercentage (5%), we could switch to TCP
    * - provided the BW, pktLoss and RTT Triggers are already met.
    * Similarly, do not switch to TCP if rttVariance is high (regardless of high
    * BW, low loss)
    */

   if (networkStats->bandwidthBytesPerSecond >= (bwKBps * 1000)) {
      bwTrigger = TRUE;
   }

   if (networkStats->packetLossPercent <= pktLossPercentage) {
      pktLossTrigger = TRUE;
   }

   if ((networkStats->rttSeconds * (double)1000) <= rttMS) {
      rttTrigger = TRUE;
   }

   if (VvcGetRttVariancePercentage(networkStats) <= rttVarPercentage) {
      rttVarPercentageTrigger = TRUE;
   }

   if (bwTrigger && pktLossTrigger && rttTrigger && rttVarPercentageTrigger) {
      ok = TRUE;
   }

   ok = VvcDataTransportShouldActOnSwitch(session, ok, FALSE);

   return ok;
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcDataTransportTcpToBeatSwitchNeeded --
 *
 *    Decide if Active AsockBe needs to be switched from TCP to BEAT.
 *    XXX: As we refine the switching decision tree based on experiments,
 *         this func should change/evolve in the future.
 *
 * Results:
 *    Returns TRUE if switch is needed, FALSE otherwise.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

Bool
VvcDataTransportTcpToBeatSwitchNeeded(VvcSession *session,           // IN
                                      VvcNetworkStats *networkStats) // IN
{
   Bool ok = FALSE;

   Bool bwTrigger = FALSE;
   double bwKBps = 0.0;

   Bool rttVarPercentageTrigger = FALSE;
   double rttVarPercentage = 0.0;
   double rttVarPercentageNwStats = 0.0;

   Bool rttTrigger = FALSE;
   double rttMS = 0.0;

   ASSERT(session);
   ASSERT(networkStats);
   ASSERT(ISLOCKED_SESSION(session));

   ASSERT(VvcIsActiveAsockBackendTCP(session));
   ASSERT(session->dataTransportSwitch);

   bwKBps = session->dataTransportSwitch->tcpToBeatBwKbps / 8;
   rttVarPercentage = session->dataTransportSwitch->tcpToBeatRttVarPercentage;
   rttMS = session->dataTransportSwitch->tcpToBeatRttMS;

   if (session->dataTransportSwitch->isVvcBweUsed) { // Using VvcBwe
      /*
       * Switch TCP to BEAT (when using VvcBwe):
       *       if Bw is <= 10000 KBps i.e 10 MBps i.e. 80 Mbps &&
       *       if Rtt is >= 2 ms
       *
       *    Above are default values - can be overwritten by config/registry.
       */

      if (networkStats->bandwidthBytesPerSecond <= (bwKBps * 1000)) {
         bwTrigger = TRUE;
      }

      if ((networkStats->rttSeconds * (double)1000) >= rttMS) {
         rttTrigger = TRUE;
      }

      // VvcBwe does not expose rttVariancePercentage

      if (bwTrigger && rttTrigger) {
         ok = TRUE;
      }
   }

   if (!session->dataTransportSwitch->isVvcBweUsed) { // Using AsyncBwe
      /*
       * Switch TCP to BEAT (Using AsyncBwe):
       *       if Bw is <= 10000 KBps i.e 10 MBps i.e. 80 Mbps &&
       *       if rttVarPercentage is >= 25% || rtt is >= 2 ms
       *
       *    Above are default values - can be overwritten by config/registry.
       */

      rttVarPercentageNwStats = VvcGetRttVariancePercentage(networkStats);

      /*
       * rtt variance by itself fluctuates frequently and so may trigger switch
       * readily. Temper it with packetLossPercent check in order to ensure
       * elevated rtt variance levels over an extended period of time.
       */
      if (rttVarPercentageNwStats >= rttVarPercentage && networkStats->packetLossPercent > 2.0) {
         rttVarPercentageTrigger = TRUE;
      }

      if (networkStats->bandwidthBytesPerSecond <= (bwKBps * 1000)) {
         bwTrigger = TRUE;
      }

      if ((networkStats->rttSeconds * (double)1000) >= rttMS) {
         rttTrigger = TRUE;
      }

      /*
       * When loss is introduced, AsyncBwe will respond by reporting higher
       * rttVariance but keeping the bandwidth estimate same as before the loss
       * was introduced (so if BW estimate was high before the loss, it will
       * stay at high values).
       * Therefore, a high rttVariance by itself should trigger a switch.
       *
       * Also, the BW estimate may take some time to climb up. So switch only
       * if BW estimate is low and RTT is high.
       */
      if (rttVarPercentageTrigger || (bwTrigger && rttTrigger)) {
         ok = TRUE;
      }
   }

   ok = VvcDataTransportShouldActOnSwitch(session, ok, TRUE);

   return ok;
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcGetRttVariancePercentage --
 *
 *    Calculate rttVariancePercentage from VvcNetworkStats - which contains
 *    rttSeconds and rttVarianceSeconds
 *
 * Results:
 *    Returns rttVariancePercentage.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

double
VvcGetRttVariancePercentage(VvcNetworkStats *networkStats) // IN
{
   double rttMS = 0.0;
   double rttVarianceMS = 0.0;
   double rttVariancePercentage = 0.0;

   ASSERT(networkStats);

   rttMS = networkStats->rttSeconds * (double)1000;
   rttVarianceMS = networkStats->rttVarianceSeconds * (double)1000;

   if (rttMS > 0.0) {
      rttVariancePercentage = (rttVarianceMS / rttMS) * (double)100;
   }

   return rttVariancePercentage;
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcDataTransportShouldActOnSwitch --
 *
 *    Act on the switching decision only if switchNeeded is TRUE for a certain
 *    number of continuous invocations of switchPollCb.
 *    This is a crude way to build a trust model before we actually switch.
 *
 * Results:
 *    Returns TRUE if we should act on the switch is needed, FALSE otherwise.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

Bool
VvcDataTransportShouldActOnSwitch(VvcSession *session, // IN
                                  Bool switchNeeded,   // IN
                                  Bool isTCP)          // IN
{
   Bool ok = FALSE;

   ASSERT(session);
   ASSERT(ISLOCKED_SESSION(session));
   ASSERT(session->dataTransportSwitch);

   if (!switchNeeded) {
      // Switching is not needed, reset currSwitchNeededCnt and exit
      session->dataTransportSwitch->currSwitchNeededCnt = 0;
      goto exit;
   }

   session->dataTransportSwitch->currSwitchNeededCnt += 1;

   // Switch TCP -> BEAT if currSwitchNeededCnt >= tcpToBeatThreshold
   if (isTCP && (session->dataTransportSwitch->currSwitchNeededCnt >=
                 session->dataTransportSwitch->tcpToBeatThreshold)) {

      // Act on the switch - reset currSwitchNeededCnt
      session->dataTransportSwitch->currSwitchNeededCnt = 0;
      ok = TRUE;
      goto exit;
   }

   // Switch BEAT -> TCP if currSwitchNeededCnt >= beatToTcpThreshold
   if (!isTCP && (session->dataTransportSwitch->currSwitchNeededCnt >=
                  session->dataTransportSwitch->beatToTcpThreshold)) {

      // Act on the switch - reset currSwitchNeededCnt
      session->dataTransportSwitch->currSwitchNeededCnt = 0;
      ok = TRUE;
      goto exit;
   }

exit:
   return ok;
}
