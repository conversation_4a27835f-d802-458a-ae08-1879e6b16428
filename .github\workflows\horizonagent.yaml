name: horizonagent
run-name: >
  ${{ github.workflow }}
  ${{ github.event_name == 'workflow_dispatch' && inputs.buildtype ||
      (github.event_name == 'pull_request' && 'obj' || 'beta') }}
  ${{ github.event_name == 'pull_request' &&
      format(' - {0} (#{1})', github.event.pull_request.title, github.event.pull_request.number)
      || '' }}
concurrency:
  # This section ensure that multiple PR pushes will cancel superseded builds.
  # Builds on main, release/* and feature/* branches will not be canceled to
  # assist in root causing build breakages.
  group: ${{ github.workflow }}-${{
      (github.ref == 'refs/heads/main' ||
       startsWith(github.ref, 'refs/heads/feature/') ||
       startsWith(github.ref, 'refs/heads/release/')) &&
      github.run_id || github.ref
    }}-${{ inputs.buildtype }}
  cancel-in-progress: true
on:
  pull_request:
  push:
    branches:
      - 'main'
      - 'release/**'
      - 'feature/**'
    paths-ignore:
      - .github/RunnerResetConfig.json
      - .github/workflows/runner_app_config.yaml
      - .github/workflows/rx-devop-nightly-*.yaml
  workflow_dispatch:
    inputs:
      buildtype:
        type: choice
        description: Build type
        required: True
        default: beta
        options:
          - beta
          - obj
          - release
      conan_develop:
        type: boolean
        description: I am testing conan packages and need to enable the conan-develop remote
        required: True
        default: false
      conan_sandbox:
        type: boolean
        description: I am testing conan compiler upgrade and need to enable the conan-sandbox remote
        required: false
        default: false
env:
  BUILDTYPE: ${{ github.event_name == 'workflow_dispatch' && inputs.buildtype ||
                 (github.event_name == 'pull_request' && 'obj' || 'beta') }}
jobs:
  file-check:
    runs-on: [lnxbuild-gh, self-hosted]
    permissions:
      contents: read
      pull-requests: read
    outputs:
      enable-build: ${{ steps.filter.outputs.result }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          sparse-checkout: .github/workflow-filters.yaml
          sparse-checkout-cone-mode: false

      - name: Check if build should be run
        id: filter
        uses: euc-eng/filter-paths@v1
        with:
          filtersFile: .github/workflow-filters.yaml
          label: horizonagent

  build-horizonagent:
    needs: file-check
    if: ${{ needs.file-check.outputs.enable-build == 'true' }}
    runs-on: [winhzn-gh, self-hosted]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          lfs: true

      - name: Run SCons
        uses: ./.github/actions/scons
        with:
          buildtype: ${{ github.event_name == 'pull_request' && 'obj' || inputs.buildtype }}
          product: horizonagent
          conanDevelop: ${{ inputs.conan_develop }}
          conanSandbox: ${{ inputs.conan_sandbox }}
          artifactoryApiToken: ${{ secrets.ARTIFACTORY_GITHUB_DELIVERABLES_API_TOKEN }}
          artifactoryUrl: ${{ vars.ARTIFACTORY_GITHUB_DELIVERABLES_URL }}
          artifactoryReadToken: ${{ secrets.ORG_OMNISSA_ART_READONLY_TOKEN }}
          artifactoryReadUser: ${{ vars.ORG_OMNISSA_ART_READONLY_USER }}
          azureSigningTenantId: ${{ vars.AZURE_BINARY_SIGNING_TENANT_ID }}
          azureSigningTestKeyCertName: ${{ vars.AZURE_BINARY_SIGNING_OFFICIAL_KEY_CERT_NAME }}
          azureSigningTestKeyClientId: ${{ vars.AZURE_BINARY_SIGNING_OFFICIAL_KEY_CLIENT_ID }}
          azureSigningTestKeySecret: ${{ secrets.AZURE_BINARY_SIGNING_OFFICIAL_KEY_CLIENT_SECRET }}
          azureSigningUrl: ${{ vars.AZURE_BINARY_SIGNING_OFFICIAL_KEY_URL }}
          extraParams: FOSSA_DEPS=1 COMPILE_DB=1 compiledb horizonagent

      - name: Run Fossa
        if: ${{ inputs.buildtype == 'release' || github.event_name == 'pull_request' }}
        uses: ./.github/actions/fossa
        with:
          product: 'horizonagent'
          fossa-api-key: ${{ secrets.ORG_OMNISSA_FOSSA_KEY }}
          omnissaArtifactoryToken: ${{ secrets.ORG_OMNISSA_ART_READONLY_TOKEN }}

  UT:
    if: ${{ vars.ENABLE_UNIT_TEST_HOSTED_IN_MV == 'true' }}
    needs: build-horizonagent
    secrets: inherit
    uses: ./.github/workflows/horizonagent_ut.yaml
    with:
      buildtype: ${{ github.event_name == 'pull_request' && 'obj' || inputs.buildtype }}

  sonar-upload:
    runs-on: [winhzn-gh, self-hosted]
    needs:
      - build-horizonagent
      - UT
    # we want to run sonar if dependent test jobs are successful or skipped, but
    # there's no function for skipped() so we use !failure() and !cancelled()
    if: ${{ !failure() && !cancelled() &&
            needs.build-horizonagent.result == 'success' }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Upload results to SonarQube
        uses: ./.github/actions/testframework/cart-sonar
        with:
          sonarProjectKey: cart-horizonagent
          productWorkflowJob: build-horizonagent
          utJobs: rx-*,vd-*,ar-*,UT-Auth-Whfb,UT-Agent-IC,UT-Updatetool-*,UT-Agent-Html5mmrServer
          utJobsObjOnly: UT-Agent-Core-*,UT-Agent-HzaPrep,wsnm_mqtt,winHttp
          artifactoryApiToken: ${{ secrets.ARTIFACTORY_GITHUB_DELIVERABLES_API_TOKEN }}
          artifactoryBaseUrl: ${{ vars.ARTIFACTORY_GITHUB_DELIVERABLES_URL }}
          artifactoryReadToken: ${{ secrets.ORG_OMNISSA_ART_READONLY_TOKEN }}
          artifactoryReadUser: ${{ vars.ORG_OMNISSA_ART_READONLY_USER }}
          languageMode: C++
          compileCommands: compile_commands.json
          sources: >
            bora/apps/crtbora/common,
            bora/apps/crtbora/win32,
            bora/apps/horizonAgent/apps/tsdr/agent,
            bora/apps/horizonAgent/apps/vmware-svi-ga,
            bora/apps/horizonAgent/include,
            bora/apps/horizonAgent/lib/thirdPartyWrappers,
            bora/apps/horizonAgent/lib/ws_admin,
            bora/apps/horizonAgent/lib/ws_applaunchmgr,
            bora/apps/horizonAgent/lib/ws_updatemgr,
            bora/apps/horizonAgent/lib/ws_winauth,
            bora/apps/horizonAgent/lib/wsnm_common,
            bora/apps/horizonAgent/lib/wsnm_desktop,
            bora/apps/horizonAgent/lib/wsnm_mqtt,
            bora/apps/horizonAgent/lib/wssm_common,
            bora/apps/horizonAgent/lib/wssm_desktop,
            bora/apps/horizonCommon,
            bora/apps/lib/cui/dnd,
            bora/apps/lib/windowWatermark,
            bora/apps/lib/wui/dnd,
            bora/apps/rde/fido2/agent,
            bora/apps/rde/fido2/common,
            bora/apps/rde/html5mmr/common,
            bora/apps/rde/html5mmr/server,
            bora/apps/rde/mksvchan/common,
            bora/apps/rde/mksvchan/server,
            bora/apps/rde/perfTracker,
            bora/apps/rde/rdeSvc/server,
            bora/apps/rde/rdeSvc/shared,
            bora/apps/rde/rds/audio,
            bora/apps/rde/rds/util,
            bora/apps/rde/sdoSensor/sdoDriver,
            bora/apps/rde/tsdr/common,
            bora/apps/rde/whfbRedirection,
            bora/apps/rde/unityPlugin,
            bora/apps/rde/usbRedirection/common,
            bora/apps/rde/usbRedirection/server,
            bora/apps/rde/vdpservice/lib/shared,
            bora/apps/updatetool,
            bora/apps/viewusb/framework/usb,
            bora/lib/dnd,
            bora/lib/public/dnd,
            bora-vmsoft/hznvaudioin,
            bora-vmsoft/hznbus,
            bora-vmsoft/hznufhid,
            bora-vmsoft/hznvhub,
            bora-vmsoft/hznflstor,
          exclusions: '**/java'
          buildtype: ${{ env.BUILDTYPE }}
          sonarHostUrl: ${{ vars.ORG_EUC_SONAR_HOST }}
          sonarToken: ${{ secrets.ORG_EUC_SONAR_TOKEN }}
          qualityGateWait: true
          conanDevelop: ${{ inputs.conan_develop }}
          conanSandbox: ${{ inputs.conan_sandbox }}

  horizonagent-overall-status:
    needs:
      - build-horizonagent
      - UT
      - sonar-upload
    if: ${{ !cancelled() }}
    timeout-minutes: 10
    runs-on: [lnxbuild-gh, self-hosted]
    permissions:
      actions: write
      contents: read
      pull-requests: read
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Check overall workflow status
        uses: ./.github/actions/check-status
        with:
          workflowId: 'horizonagent.yaml'
          jobs: ${{ toJson(needs) }}
          buildtype: ${{ inputs.buildtype }}
          slackWebhookUrl: ${{ secrets.CART_SLACK_WEBHOOK_URL }}
          dashboardUrl: ${{ vars.CART_BUILD_DASHBOARD }}
          slackBranches: ${{ vars.DAILY_BUILD_BRANCHES }}
