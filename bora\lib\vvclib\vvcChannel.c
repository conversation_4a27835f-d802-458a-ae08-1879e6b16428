/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * vvcChannel.c
 *
 *     VVC channels lifecycle, IO and event handling.
 *
 */

#include "vvcChannel.h"
#include "asyncSocketBase.h"
#include "asyncBweSocket.h"
#include "fecAsyncSocket.h"
#include "vvcBandwidthDetectionAPI.h"
#include "vvcCtrl.h"
#include "vvcSendQueue.h"
#include "vvcDebug.h"
#include "blastSocket.h"
#include "vvcMultiAsockBackend.h"
#include "vvclib.h"

#ifdef _WIN32
#   include "win32uRegistry.h"
#   include "iphlpapi.h"
#else
#   include <netinet/in.h>
#endif

int VvcRawChannelRecv(vvcRawChanCtx *ctx, AsyncSocket *asock);

/*
 *-----------------------------------------------------------------------------
 *
 * VvcCreateChannel --
 *
 *    Create channel object
 *
 * Results:
 *    VvcChannel *
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

VvcChannel *
VvcCreateChannel(VvcListener *listener,    // IN
                 uint32 channelId,         // IN
                 char *name,               // IN
                 uint32 priority,          // IN
                 uint32 latency,           // IN
                 uint32 priorityVersion,   // IN
                 uint32 trafficType,       // IN
                 uint32 timeout,           // IN
                 uint32 flags,             // IN
                 uint32 traceFlags,        // IN
                 VvcSession *session,      // IN
                 VvcChannelEvents *events, // IN
                 void *clientData)         // IN
{
   size_t nameLen;
   uint64 now;

   VvcChannel *channel = Util_SafeCalloc(1, sizeof *channel + VVCCOMMON_TAGS_SIZE);

   channel->common.magic = VVC_CHANNEL_MAGIC;
   channel->common.refCount.value = 1;
   channel->common.clientData = clientData;
   channel->common.tags = (Atomic_uint32 *)(channel + 1);
   channel->common.tags[VvcTagCreation].value = 1;

   channel->channelId = channelId;

   if (name) {
      nameLen = Str_Strlen(name, VVC_MAX_CHANNEL_NAME_LEN);
      channel->name = Util_SafeMalloc(nameLen + 1);
      Str_Strcpy(channel->name, name, nameLen + 1);
   }

   channel->state = VvcChannelInit;
   channel->closeReason = 0xffffffff;

   channel->priority = priority;
   channel->latency = latency;
   channel->latencyTimeoutMS = 0;
   channel->priorityVersion = priorityVersion;
   channel->trafficType = trafficType;
   channel->timeout = timeout;

   /*
    * Strip the raw channel flag from proxy sessions. Useful in both worker and
    * RX process openChannel paths. Proxy sessions pass the flag across process
    * and session boundary, but the channel itself is not created as raw.
    */
   if (!(session->instance->flags & VVC_INSTANCE_MAIN) && flags & VVC_CHANNEL_RAW) {
      flags &= ~VVC_CHANNEL_RAW;
   }

   channel->flags = flags;
   channel->traceFlags = traceFlags;

   channel->currentQueuedBytes = 0;
   channel->totalQueuedBytes = 0;

   channel->currentBeSendSizeAvg = 0;

   if (!VvcAddTokenToHandleMapping(flags & VVC_CHANNEL_RAW ? VvcHandleTypeRawChannel
                                                           : VvcHandleTypeChannel,
                                   (VvcCommonHandle)channel, channel->channelId, channel->name)) {
      VvcError("%s: Failed calling VvcAddTokenToHandleMapping() "
               "for channel:0x%p, id=%d\n",
               __FUNCTION__, channel, channelId);
      /*
       * We don't expect failure here and will need to investigate to see if
       * it's related to the algorithm used to generate the unique token handle.
       */
      VERIFY(FALSE);
   }

   // Take a reference to the listener
   VVC_ADD_REF_LISTENER(listener, VvcTagChildChannel);
   channel->listener = listener;

   channel->session = session;
   // Reference parent
   VVC_ADD_REF_SESSION(session, VvcTagChildChannel);

   channel->events = *events;
   if (session->negotiatedEnableVVCPauseResume && channel->flags & VVC_CHANNEL_PAUSERESUME) {
      VvcLog("[VVC PauseResume] %s: Registered onPause and onResume events for "
             "channel: %s, id: %u.\n",
             __FUNCTION__, channel->name, channel->channelId);
   } else {
      channel->events.onPause = NULL;
      channel->events.onResume = NULL;
   }

   /*
    * Initialize channel counters with current sample interval from session,
    * so that channel counters are sync'ed with the session.
    */
   if (channelId == VVC_CONTROL_CHANNEL_ID ||
       VvcBandwidthDetection_IsBwdChannel(session->bwDetection, channelId, NULL)) {
      now = session->counterSet.inBps.rd0;
   } else if (ISLOCKED_SESSION(session)) {
      now = session->counterSet.inBps.rd0;
   } else {
      LOCK_SESSION(session);
      now = session->counterSet.inBps.rd0;
      UNLOCK_SESSION(session);
   }

   DblLnkLst_Init(&channel->mptSendQ);

   DblLnkLst_Init(&channel->sendQueue);
   channel->outstandingSends = 0;
   channel->channelOnCloseQueued = FALSE;
   channel->pendingQueueOnCloseAfterSends = FALSE;

   DblLnkLst_Init(&channel->pendingRecvMsgs);

   channel->recvBufferInitialSize = session->channelRecvBufInitialSize;

   channel->sndUnaMsg = NULL;
   channel->sndUna = SeqNum_16Zero;

   channel->sndNxtMsg = NULL;
   channel->sndNxt = SeqNum_16Zero;

   channel->schedNxtMsg = NULL;
   channel->schedNxt = SeqNum_16Zero;

   channel->rcvNxt = SeqNum_16Zero;
   channel->highAckSeq = SeqNum_16Zero;

   channel->rcvMsgReassemblyQ = VvcPriorityQBySeqOfRecvMsgs_Create();

   channel->lastMptAckSendTs = Hostinfo_SystemTimerUS();
   channel->mptUnackedBytes = 0;

   VvcDebug("Channel created (0x%p | 0x%p), name: %s, instance: %s, "
            "listener: %s, sessionId: %d (0x%p) channelId: %d, "
            "totalQueuedBytes: %" FMT64 "u, flags: %#0x\n",
            channel, TOKEN_HANDLE(channel), channel->name ? channel->name : "-",
            channel->listener->instance->name, channel->listener->name, channel->session->sessionId,
            channel->session, channel->channelId, channel->totalQueuedBytes, channel->flags);

   return channel;
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcDestroyChannel --
 *
 *    Destroy channel object
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

void
VvcDestroyChannel(VvcChannel *channel) // IN
{
   DblLnkLst_Links *curr;
   DblLnkLst_Links *next;
   VvcRecvBufHdr *msgBuf;

   VvcListener *listener = channel->listener;
   VvcSession *session = channel->session;

   VvcDebug("Channel destroyed (0x%p | 0x%p), name: %s, instance: %s, "
            "listener: %s, sessionId: %d (0x%p) channelId: %d, "
            "totalQueuedBytes: %" FMT64 "u\n",
            channel, TOKEN_HANDLE(channel), channel->name ? channel->name : "-",
            channel->listener->instance->name, channel->listener->name, channel->session->sessionId,
            channel->session, channel->channelId, channel->totalQueuedBytes);

   ASSERT(channel->common.refCount.value == 0);

   DblLnkLst_ForEachSafe(curr, next, &channel->pendingRecvMsgs)
   {
      msgBuf = DblLnkLst_Container(curr, VvcRecvBufHdr, link);
      DblLnkLst_Unlink1(&msgBuf->link);
      VvcReleaseRecvBuf(msgBuf);
   }

   /*
    * For raw channels, close the asyncsockets and free the context.
    * AsyncSockets are not closed during channel-close because async TCP
    * connect code could still hold a ref on the channel and be in the
    * middle of connection setup using this asock.
    */
   if (VVC_RAW_CHANNEL(channel) && channel->rawCtx) {
      VvcRawChannelClose(channel);

      MXUser_DestroyRecLock(channel->rawCtx->pollLock);
      MXUser_DestroyRecLock(channel->rawCtx->bwePollLock);

      if (channel->rawCtx->recvState) {
         free(channel->rawCtx->recvState->recvBuf);
         free(channel->rawCtx->recvState);
      }

      if (channel->rawCtx->preCommitBuf) {
         free(channel->rawCtx->preCommitBuf);
         channel->rawCtx->preCommitBuf = NULL;
      }

      if (VVC_RAW_CHANNEL(channel)) {
         ASSERT(channel->session->numRawChannels > 0);
         channel->session->numRawChannels--;
      }

      free(channel->rawCtx);
      channel->rawCtx = NULL;
   }

   if (channel->name) {
      free(channel->name);
   }

   if (channel->featureName) {
      free(channel->featureName);
   }

   VVC_RELEASE_LISTENER(listener, VvcTagChildChannel);

   // Deref parent
   VVC_RELEASE_SESSION(session, VvcTagChildChannel);


   /*
    * This will free each VvcRecvMsg including its held buffer; and then
    * the overall queue structure holding any such data.
    */
   VvcPriorityQBySeqOfRecvMsgs_Destroy(&channel->rcvMsgReassemblyQ);

   memset(channel, 0xfe, sizeof *channel);
   free(channel);
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcAbortChannel --
 *
 *    Do a local close of the channel. No on-close handler is called.
 *    This synchronously closes the channel and is intended for situations
 *    where the network is down. MptSendQ and SendQ of the channel are purged.
 *    XXX: Chunks belonging to this channel which are already in sendTree
 *         are not purged as part of AbortChannel.
 *
 * Results:
 *    VvcStatus
 *
 * Side effects:
 *    SendQ and MptSendQ of the channel are purged.
 *
 *-----------------------------------------------------------------------------
 */

static void
VvcAbortChannel(VvcChannel *channel)
{
   /*
    * VvcAbortChannel() avoids typical channel-closing helpers in favor of
    * mostly in-line cleanup, because those helpers tend to have
    * asynchronous or even potential networking ops (like attempting
    * to send a ..._CLOSE message and wait for ..._CLOSE_ACK). Our goal is
    * a quick destruction in a network-down situation (including
    * purging the queues, as noted in the doc header above -- so the networking
    * ops would not occur even if attempted).
    */

   VvcSession *session = channel->session;

   /*
    * This should only be called by VVCLIB_CloseChannel which locks the
    * channel.
    */
   ASSERT(ISLOCKED_CHANNEL(channel));

   VvcDebug("Channel aborting, instance: %s, sessionId: %d, "
            "listener: %s, name: %s, channelId: %d\n",
            session->instance->name, session->sessionId, channel->listener->name,
            channel->name ? channel->name : "na", channel->channelId);

   channel->closeReason = VvcCloseChannelAbort;
   channel->state = VvcChannelClosing;

   ASSERT(ISLOCKED_INSTANCE(session->instance));
   VvcPurgeMptSendQueue(channel);
   VvcPurgeSendQueue(channel);

   VvcLog("AbortChannel for channel %s almost done.\n", channel->name);
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcCloseChannelInt --
 *
 *    Close a channel.  This is an async operation, from the extensions POV
 *    the channel is not closed until it receives an onClose event.
 *
 * Results:
 *    VvcStatus
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

VvcStatus
VvcCloseChannelInt(VvcChannel *channel,          // IN
                   VvcCloseChannelReason reason) // IN
{
   VvcSession *session;

   void *openAckOp;
   size_t openAckOpLen;
   uint32 openStatus;

   void *closeOp;
   size_t closeOpLen;
   uint32 closeOpReason;

   Bool eventQueued = FALSE;
   Bool msgQueued = FALSE;
   Bool ncEnabled = FALSE;
   Bool ncDeclined = FALSE;
   VvcStatus status = VVC_STATUS_SUCCESS;

   Bool isChannelLocked = FALSE;
   Bool isInstanceLocked = FALSE;

   ASSERT(channel);

   VVC_ADD_REF_CHANNEL(channel, VvcTagFuncCloseChannelInt);

   session = channel->session;

   isChannelLocked = ISLOCKED_CHANNEL(channel);
   if (!isChannelLocked) {
      LOCK_CHANNEL(channel);
   }

   isInstanceLocked = ISLOCKED_INSTANCE(session->instance);
   if (!isInstanceLocked) {
      LOCK_INSTANCE(session->instance);
   }

   if (channel->state != VvcChannelAcceptPaused && channel->state != VvcChannelOpen &&
       channel->state != VvcChannelInit) {
      VvcWarning("Channel already closing, instance: %s, "
                 "sessionId: %d, listener: %s, name: %s, channelId: %d, "
                 "state: %s\n",
                 session->instance->name, session->sessionId, channel->listener->name,
                 channel->name ? channel->name : "na", channel->channelId,
                 VvcDebugChannelStateToString(channel->state));

      /*
       * If a channel is already in a Closing state but an additional explicit
       * CloseChannelInt is being invoked with reason = Abort, then Abort
       * the channel.
       * Currently, the only trigger point for this "second" order of
       * CloseChannelInt() is via the VVCLIB_CloseNCDeclinedChannels() - which
       * comes into picture when a new HandleUpgradeRequest is seen before
       * the asyncsocket stack firing a socketErrorCb.
       */

      if (channel->state == VvcChannelClosing && reason == VvcCloseChannelAbort) {
         VvcAbortChannel(channel);
         eventQueued |= VvcQueueChannelOnClose(channel, VvcQueueOnChannelCloseTriggerAbort);
      }

      goto exit;
   }

   if (channel->flags & VVC_CHANNEL_EXTENSION_RECV_BUFFER) {
      /*
       * Attempt to dispatch any pending received messages to API user
       * for a graceful close.
       * TODO: This is some legacy code, here because originally
       * ...QueueChannelOnClose() was invoked here and would in fact execute
       * similar code. It's questionable whether this
       * (unlikely) path will work (in particular VvcChannelOnRecvEvCb()
       * currently short-circuits if channel already closed, which is
       * set below: VvcChannelClosed state) -- and even if it did work,
       * whether that's desired in an Abort case (does the user destroy
       * associated data after calling Abort?). So the to-do is to consider
       * the appropriate actions to take here. Perhaps these events should
       * not be queued, and EXISTING queued events should additionally be
       * purged.
       * A ticket has been filed to make a decision and act.
       */
      if (channel->recvBuffer && DblLnkLst_IsLinked(&channel->pendingRecvMsgs)) {
         VvcQueueChannelOnRecvEvent(channel, NULL);
         eventQueued = TRUE;
      }

      channel->recvBuffer = NULL;
      channel->recvBufferLen = 0;
      channel->recvBufferRecvMin = 0;
      channel->recvBufferRecvLen = 0;
   }

   /*
    * Determine if we should abort the channel instead of trying to send
    * a close channel op.
    */
   ncEnabled = session->negotiatedDoChannelResync;
   ncDeclined = (ncEnabled && (channel->flags & VVC_CHANNEL_DECLINE_NC));

   if ((session->asockXBeDown || reason == VvcCloseChannelAbort) && (ncDeclined || !ncEnabled)) {
      if (eventQueued) {
         VvcDispatchEvents(session->instance);
         eventQueued = FALSE;
      }

      VvcAbortChannel(channel);
      eventQueued |= VvcQueueChannelOnClose(channel, VvcQueueOnChannelCloseTriggerAbort);
      goto exit;
   }

   if (session->state != VvcSessionEstablished) {
      VvcWarning("Session is not established, instance: %s, "
                 "sessionId: %d, session: 0x%p, listener: %s, name: %s, "
                 "channelId: %d, sessionState: %s. Skipping close channel "
                 "sequence.\n",
                 session->instance->name, session->sessionId, session, channel->listener->name,
                 channel->name ? channel->name : "na", channel->channelId,
                 VvcDebugSessionStateToString(session->state));
      goto exit;
   }

   channel->closeReason = reason;

   if (channel->state == VvcChannelAcceptPaused) {
      channel->state = VvcChannelClosing;

      switch (channel->closeReason) {
      case VvcCloseChannelNormal:
         openStatus = VVC_OPEN_CHAN_STATUS_CLOSED;
         break;

      case VvcCloseChannelError:
      case VvcCloseChannelRejected:
         openStatus = VVC_OPEN_CHAN_STATUS_REJECTED;
         break;

      default:
         openStatus = VVC_OPEN_CHAN_STATUS_REJECTED;
         break;
      }

      // Send VVC_CTRL_OP_OPEN_CHAN_ACK
      openAckOp = VvcBuildOpenChanAckOp(channel->channelId, openStatus, 0, 0, &openAckOpLen);

      eventQueued |= VvcQueueMessage(session->ctrlChannel, openAckOp, openAckOpLen, FALSE,
                                     VvcDefaultCtrlMsg, 0, 0);
      msgQueued = TRUE;

      // Queue channel onClose event
      eventQueued |= VvcQueueChannelOnClose(channel, VvcQueueOnChannelCloseTriggerPendingOpen);
   } else {
      channel->state = VvcChannelClosing;

      switch (channel->closeReason) {
      case VvcCloseChannelNormal:
         closeOpReason = VVC_CLOSE_CHAN_REASON_NORMAL;
         break;

      case VvcCloseChannelError:
         closeOpReason = VVC_CLOSE_CHAN_REASON_ERROR;
         break;

      case VvcCloseChannelRejected:
         closeOpReason = VVC_CLOSE_CHAN_REASON_REJECTED;
         break;

      default:
         closeOpReason = VVC_CLOSE_CHAN_REASON_ERROR;
         break;
      }

      // Send VVC_CTRL_OP_CLOSE_CHAN
      closeOp = VvcBuildCloseChanOp(channel->channelId, closeOpReason, &closeOpLen);

      /*
       * This is a control message (i.e. sent on VVC_CONTROL_CHANNEL_ID),
       * however the message is queued on the channels send queue so that
       * the close is sent as the last message.
       * If close message is failed to be sent, we rely on the
       * other code paths (Ex: session close or asockXBeDown) to call
       * VvcQueueChannelOnClose(). If there are gaps in that path the
       * channel may never receive closeOpAck and "onClose" may never be
       * queued. Even in that case, session close will call
       * VvcQueueChannelOnClose() and the channel will be closed atleast on
       * session close.
       *
       * Exception for raw channels that cannot send close op inline.
       */

      eventQueued |= VvcQueueMessage(VVC_RAW_CHANNEL(channel) ? session->ctrlChannel : channel,
                                     closeOp, closeOpLen, TRUE, VvcDefaultCtrlMsg, 0, 0);
      msgQueued = TRUE;
   }

   VvcLog("Channel closing, instance: %s, sessionId: %d, "
          "listener: %s, name: %s, channelId: %d, reason: %d\n",
          session->instance->name, session->sessionId, channel->listener->name,
          channel->name ? channel->name : "na", channel->channelId, reason);

exit:
   /*
    * Always unlock channel & instance before the VvcDispatchEvents() &
    * VvcDispatchSendQueues() call below.
    */
   UNLOCK_CHANNEL(channel);
   UNLOCK_INSTANCE(session->instance);

   if (eventQueued) {
      VvcDispatchEvents(session->instance);
   }

   if (msgQueued) {
      VvcDispatchSendQueues(session, VvcDispatchSendTriggerOnCtrl);
   }

   /*
    * If we entered VvcCloseChannelInt() with locks held, re-acquire them
    * before exiting.
    */
   if (isChannelLocked) {
      LOCK_CHANNEL(channel);
   }

   if (isInstanceLocked) {
      LOCK_INSTANCE(session->instance);
   }

   VVC_RELEASE_CHANNEL(channel, VvcTagFuncCloseChannelInt);

   return status;
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcChannelCheckAndDispatchRecv --
 *
 *    For channel consumers that provide a recv buffer, queue the onRecv event
 *    if sufficient bytes have been received.
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

void
VvcChannelCheckAndDispatchRecv(VvcChannel *channel)
{
   VvcSession *session = channel->session;

   // Dispatch onRecv if we have the minimum bytes requested
   if (channel->recvBufferRecvLen >= channel->recvBufferRecvMin) {
      LOCK_INSTANCE(session->instance);
      VvcQueueEvent(session->instance, channel->listener->pluginId, VvcEvChannelOnRecv,
                    (VvcCommon *)channel, 0, (void *)channel->recvBuffer,
                    channel->recvBufferRecvLen, (void *)VVC_ONRECV_EXTENSION_BUFFER,
                    VvcChannelOnRecvEvCb);

      channel->recvBuffer = 0;
      channel->recvBufferLen = 0;
      channel->recvBufferRecvMin = 0;
      channel->recvBufferRecvLen = 0;
      UNLOCK_INSTANCE(session->instance);
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcAcceptChannelInt --
 *
 *    Potentially async half of VVC acceptChannel interface. Called inline for
 *    regular channels, called after commit op is received for raw channels.
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

void
VvcAcceptChannelInt(VvcChannel *channel,   // IN
                    VvcOpenChan *openChan) // IN
{
   Bool messageQueued = FALSE;
   void *openAckOp;
   size_t openAckOpLen;
   VvcOpenChanAck *openChanAck;
   VvcListener *listener;
   VvcSession *session;
   VvcStatus openStatus;

   listener = openChan->listener;
   session = openChan->session;
   ASSERT(ISLOCKED_SESSION(session));

   if (!(channel->flags & VVC_CHANNEL_ACCEPT_PAUSED)) {
      // Send VVC_CTRL_OP_OPEN_CHAN_ACK

      // Encode channel type client choses (raw or not) in openStatus
      openStatus =
         VVC_RAW_CHANNEL(channel) ? VVC_OPEN_CHAN_STATUS_SUCCESS_RAW : VVC_OPEN_CHAN_STATUS_SUCCESS;

      openAckOp = VvcBuildOpenChanAckOp(openChan->channelId, openStatus, openChan->ackInitialData,
                                        openChan->ackInitialDataLen, &openAckOpLen);

      if (!session->negotiatedDoConcurrentTransports || VVC_RAW_CHANNEL(channel)) {
         VvcQueueMessage(session->ctrlChannel, openAckOp, openAckOpLen, FALSE, VvcDefaultCtrlMsg, 0,
                         0);
      } else {
         /*
          * When BENIT is negotiated and Channel is about to be accepted,
          * queue the OPEN_CHAN_ACK_OP on the channel's sendQueue.
          *
          * Pass param #4 (Bool ctrlMsg) of VvcQueueMessage() as TRUE:
          * - This adds VVC_CHUNK_FLAG_CTRL to the chunk before Asock Send.
          * - The receiver interprets that as a msg received on data channel but
          *   meant to be processed on Control Channel - not dispatching app
          *   event callbacks but CtrlChannel event callbacks.
          * - See comments from VvcBuildChunkHeader() for more details.
          */
         VvcDebug("Queue CHAN_ACK_OP on data channel, channel->name: %s, "
                  "channelId: %d, channel: 0x%p, listener->name: %s, "
                  "listener: 0x%p, session: 0x%p, session->sessionid: %d\n",
                  channel->name, channel->channelId, channel, channel->listener->name,
                  channel->listener, channel->session, channel->session->sessionId);

         VvcQueueMessage(channel, openAckOp, openAckOpLen, TRUE, VvcDefaultCtrlMsg, 0, 0);
      }
      messageQueued = TRUE;
   }

   /*
    * TODO: handle abstraction.  Return a handle that will not match a
    * previously destroyed handle.
    */

   LOCK_INSTANCE(session->instance);
   openChanAck = VvcCreateOpenChanAck(openChan->channelId, VVC_STATUS_SUCCESS,
                                      openChan->initialData, openChan->initialDataLen);
   VvcQueueEvent(session->instance, channel->listener->pluginId, VvcEvChannelOnOpen,
                 (VvcCommon *)channel, 0, 0, 0, (void *)openChanAck, VvcChannelOnOpenEvCb);
   UNLOCK_INSTANCE(session->instance);

   VvcDebug("Channel accepted%s, instance: %s, sessionId: %d, "
            "listener: %s, name: %s, channel: 0x%p | 0x%p, channelId: %d, "
            "priority: %d, latency: %d, priority version: %d, "
            "traffic type: %d, traceFlags: 0x%x\n",
            (channel->flags & VVC_CHANNEL_ACCEPT_PAUSED) ? " (paused)" : "",
            listener->instance->name, session->sessionId, listener->name,
            openChan->name ? openChan->name : "-", channel, TOKEN_HANDLE(channel),
            channel->channelId, channel->priority, channel->latency, channel->priorityVersion,
            channel->trafficType, channel->traceFlags);

   if (messageQueued) {
      VvcDispatchSendQueues(session, VvcDispatchSendTriggerOnCtrl);
   }

   VvcDispatchEvents(session->instance);
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcRawChannelAccept --
 *
 * Raw channels special handling.
 * Create rawChan context and connect to the listening end.
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

void
VvcRawChannelAccept(VvcChannel *channel,   // IN
                    VvcOpenChan *openChan, // IN
                    uint8 *initialData,    // IN
                    size_t initialDataLen) // IN
{
   ASSERT(VVC_RAW_CHANNEL(channel));

   // grab channel and openChan refs for ConnectCb
   VVC_ADD_REF_CHANNEL(channel, VvcTagRawConnect);
   VVC_ADD_REF_OPEN_CHAN(openChan, VvcTagRawConnect);

   if (openChan->rawChannelType == VVC_RAW_CHAN_ERROR) {
      VvcError("%s: raw channel open failed", __FUNCTION__);
      channel->rawCtx = NULL;
      goto out;
   }

   if (initialData && initialDataLen) {
      openChan->ackInitialDataLen = initialDataLen;
      openChan->ackInitialData = Util_SafeMalloc(initialDataLen);
      memcpy(openChan->ackInitialData, initialData, initialDataLen);
   }

   if (openChan->rawChannelType == VVC_RAW_CHAN_BEAT) {
      openChan->raw.beat.channel = channel;
      channel->rawCtx = VvcRawChanBeatConnect(openChan);
   } else if (openChan->rawChannelType == VVC_RAW_CHAN_TCP) {
      uint8 sNo = openChan->raw.tcp.serialNo;
      ASSERT(sNo > 0);
      openChan->session->nonce = openChan->raw.tcp.nonce;
      VvcLog("%s: Saved nonce 0x%x and serialNo %d for session %d", __FUNCTION__,
             openChan->session->nonce, sNo, openChan->session->sessionId);
      channel->rawCtx = VvcRawChanTcpConnect(channel, openChan, sNo);
   } else {
      ASSERT(openChan->rawChannelType == VVC_RAW_CHAN_VVC);
      openChan->raw.vvc.channel = channel;
      channel->rawCtx = VvcRawChanBeatConnect(openChan);
      // save TCP info for future BENIT driven connect
      ASSERT(openChan->raw.vvc.nonce && openChan->raw.vvc.serialNo);
      openChan->session->nonce = openChan->raw.vvc.nonce;
      channel->rawCtx->serialNo = openChan->raw.vvc.serialNo;
   }

   /*
    * Mark start of raw channel creation: save openChan from now until openAck
    * is sent to assist fallback on connection errors. No additional refcount
    * necessary as openChan pointer exits only during its lifetime.
    */
   channel->rawCtx->openReq.openChan = openChan;

out:
   if (channel->rawCtx) {
      channel->rawCtx->type = openChan->rawChannelType;
   } else {
      // channel will attempt fallback to regular, so release above refs
      VVC_RELEASE_OPEN_CHAN(openChan, VvcTagRawConnect);
      VVC_RELEASE_CHANNEL(channel, VvcTagRawConnect);
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcFallbackToRegularChannel --
 *
 *    Client side fallback from raw to regular channel.
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

void
VvcFallbackToRegularChannel(void *data)
{
   vvcRawChanCtx *ctx = (vvcRawChanCtx *)data;
   VvcChannel *channel = ctx->channel;
   VvcOpenChan *openChan = ctx->openReq.openChan;

   ASSERT(openChan);
   ASSERT(!ISLOCKED_SESSION(channel->session));

   VvcLog("%s: Raw channel %s creation hit a snag, fallback to regular", __FUNCTION__,
          channel->name);

   VvcRawChannelClose(channel);

   LOCK_SESSION(channel->session);

   // convert channel to regular, do not reference ctx beyond this point
   VvcRawChannelConvertToRegular(channel);

   // complete openChannel op notifying a regular channel
   VvcAcceptChannelInt(channel, openChan);

   UNLOCK_SESSION(channel->session);
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcRawAsockErrorCb --
 *
 * Asyncsocket error handling for raw channels:
 * - network connection terminated, sockets closed
 * - channel closed event queued, that will fire onCloseCb
 * - session is not closed like multiasockBE error path. This can result in
 *   degraded experience (ex. raw audio will go silent while mks is fine)
 * - NC is not supported (raw packets do not contain vvc framing)
 * - VVC close sequence not implemented
 *
 * Results:
 *    None
 *
 * Side effects:
 *   Graceful exit of raw channels involes sending channelClose message to peer
 *   with the reason, closing asyncsockets and firing the channelCloseCb and
 *   destroying the channel itself.
 *
 *-----------------------------------------------------------------------------
 */

void
VvcRawAsockErrorCb(int error, AsyncSocket *asock, void *data)
{
   Bool eventQueued = FALSE;
   vvcRawChanCtx *ctx = (vvcRawChanCtx *)data;
   VvcChannel *channel = ctx->channel;
   VvcSession *session = channel->session;
   VvcAsockBackend *asockBE;
   Bool isTCP = !FECAsyncSocket_IsFecSocket(asock);
   void *recvBuf = NULL;

   ASSERT(ctx->channel);
   ASSERT(session);

   VvcLog("%s: Error %d on %s AsyncSocket %d", __FUNCTION__, error, isTCP ? "TCP" : "BEAT",
          asock->id);

   if (channel->state == VvcChannelClosing || channel->state == VvcChannelClosed ||
       channel->state == VvcChannelPeerClosed) {
      VvcLog("Channel %d already closing/closed, state: %s\n", channel->channelId,
             VvcDebugChannelStateToString(channel->state));
      AsyncSocket_CancelRecvEx(asock, NULL, NULL, NULL, TRUE);
      return;
   }

   ASSERT(!ISLOCKED_SESSION(session));
   LOCK_SESSION(session);

   if (error == ASOCKERR_GENERIC) {
      VvcLog("Received generic Asyncsocket error, error:%d.\n", AsyncSocket_GetGenericErrno(asock));
   }

   if (session->state != VvcSessionEstablished) {
      VvcLog("Session %d is not established, listener: %s, channel: %s, "
             "channelId: %d, sessionState: %s\n",
             session->sessionId, channel->listener->name, channel->name ? channel->name : "na",
             channel->channelId, VvcDebugSessionStateToString(session->state));
      // Channel will be closed by session, so simply return now
      UNLOCK_SESSION(session);
      return;
   }

   // Fallback to regular channel on the client if openChan is ongoing
   if (ctx->openReq.openChan && !ctx->isServer) {
      // Cancel the early pre-commit recv() which allowed us to get in this error callback
      if (isTCP) {
         AsyncSocket_CancelRecvEx(asock, NULL, &recvBuf, NULL, TRUE);
         ASSERT(recvBuf);
         VvcDebug("%s: Cancel pre-commit recv (channel %s, buf %p)", __FUNCTION__, channel->name,
                  recvBuf);
         free(recvBuf);
      }

      // Fallback to regular channel
      if (session->instance->instanceBe.pollCallback) {
         session->instance->instanceBe.pollCallback(VvcFallbackToRegularChannel, ctx, FALSE, 0);
         UNLOCK_SESSION(session);
         return;
      }
   }

   /*
    * Connection already established, so this is point of no return (fallback). As raw sockets are
    * attached to their respective channels, they constitute a fault domain and do not need to take
    * down the entire user session. It depends on whether the user can continue with the missing
    * functionality until next reconnect.
    * For MKS it does not make sense to keep going with a blacked out client view, so we take down
    * the session for reconnect. New features on raw can decide whether to take down session or not.
    */

   channel->state = VvcChannelClosing;
   // queue channel onClose event
   channel->closeReason = VvcCloseChannelError;
   VvcDebug("Queuing channel close callback with reason asock error, "
            "channel name: %s, chan id: %d, session: 0x%p, sessionId: %d\n",
            channel->name, channel->channelId, session, session->sessionId);

   eventQueued = VvcQueueChannelOnClose(channel, VvcQueueOnChannelCloseTriggerError);

   AsyncSocket_CancelRecvEx(asock, NULL, NULL, NULL, TRUE);

   VVC_ADD_REF_SESSION(session, VvcTagAsockBeErrorHandler);
   UNLOCK_SESSION(session);

   if (eventQueued) {
      VvcDispatchEvents(session->instance);
   }

   VvcLog("%s: Network error propagated to session asockBackend %d", __FUNCTION__,
          session->activeAsockBackendIndex);
   asockBE = VvcGetActiveAsockBackend(session);
   if (asockBE) {
      VvcMultiAsockBackendErrorHandler(ASOCKERR_NOTCONNECTED, asockBE);
      VVC_RELEASE_ASOCKBE(asockBE, VvcTagAsockBeGeneric);
   }
   VVC_RELEASE_SESSION(session, VvcTagAsockBeErrorHandler);
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcRawChannelRecvCb --
 *
 *    Receive loop for the asock.
 *
 *    BENIT logic for deferred switch at cutoff byte count for raw channels.
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

static void
VvcRawChannelRecvCb(void *buf,          // IN
                    int len,            // IN
                    AsyncSocket *asock, // IN
                    void *cbData)       // IN
{
   vvcRawChanCtx *ctx = cbData;
   VvcChannel *channel = ctx->channel;
   VvcSession *session = channel->session;
   VvcRawRecvState *recvState;
   char *buffer;
   int err;
   uint32 outstanding;
   size_t deliverLen;

   recvState = ctx->recvState;

   /*
    * Reason why "ASSERT(ctx->asock == asock);" does not always hold true:
    *
    * For BEAT and TCP raw channels above condition always holds true as
    * they only ever claim a single asyncsocket.
    *
    * "VVC" type raw channels on the other hand manage up to 2 sockets (BEAT
    * and TCP) concurrently. BENIT could switch protocol (like BEAT -> TCP)
    * anytime and when the corresponding asyncsocket connection is ready, that
    * socket is immediately made active for future sends (ex. this is done by
    * pointing ctx->asock to ctx->bweAsock when switchTo is TCP). But the
    * receive loop continues on the old socket since the remote peer has not
    * switched yet. Peer may or may not switch in future, and until it does we
    * have different protocols in both directions and the ASSERT will not hold
    * true. If and when the remote peer switches to match the other side,
    * receive loop will be updated on at the receiving end with the new asock,
    * and then the above condition will be TRUE again.
    */

   ASSERT(len > 0);
   ASSERT(!ISLOCKED_SESSION(session));
   ASSERT(!ISLOCKED_INSTANCE(session->instance));

   if (channel->state == VvcChannelClosed) {
      VvcLog("%s: Channel %u with asock %d is closed, recv length %d\n", __FUNCTION__,
             channel->channelId, AsyncSocket_GetID(asock), len);
      return;
   }

   /*
    * ASSERT that a VVC raw channel is not receiving any packets on switched out
    * AsyncBWE socket.
    */
   ASSERT(!VVC_RAW_CHANNEL(channel) || (ctx->type != VVC_RAW_CHAN_VVC) ||
          (ctx->switchTo == VVC_RAW_CHAN_TCP) || (ctx->bweAsock != asock));

   /*
    * Updating received bytes is critical for BENIT switch to trigger at the
    * negotiated byte count.
    */
   channel->recvedBytes += len;

   // XXX: attempt to remove extra copies
   if (channel->flags & VVC_CHANNEL_EXTENSION_RECV_BUFFER) {
      buffer = buf;
      LOCK_SESSION(session);
      // optimize away the need for queueing if there is room in recvBuffer
      if (channel->recvBuffer && !DblLnkLst_IsLinked(&channel->pendingRecvMsgs)) {
         deliverLen = MIN(len, channel->recvBufferLen - channel->recvBufferRecvLen);
         memcpy(channel->recvBuffer + channel->recvBufferRecvLen, buffer, deliverLen);
         buffer += deliverLen;
         len -= deliverLen;
         channel->recvBufferRecvLen += deliverLen;
         VvcChannelCheckAndDispatchRecv(channel);
      }
      if (len) {
         VvcRecvBufHdr *bufHdr = VvcGetRecvBuf(len);
         memcpy(bufHdr + 1, buffer, len);
         bufHdr->recvLen = len;
         DblLnkLst_LinkLast(&channel->pendingRecvMsgs, &bufHdr->link);
         if (channel->recvBuffer) {
            VvcQueueChannelOnRecvEvent(channel, NULL);
         }
      }

      // update session bytes recv counters
      session->recvedBytes += len;
      if (FECAsyncSocket_IsFecSocket(asock)) {
         session->udpRecvedBytes += len;
         if (UNLIKELY(ctx->lastKnownPeerTransportType != VvcSessionActiveTransportTypeUDP)) {
            ctx->lastKnownPeerTransportType = VvcSessionActiveTransportTypeUDP;
         }
      } else {
         session->tcpRecvedBytes += len;
         if (UNLIKELY(ctx->lastKnownPeerTransportType != VvcSessionActiveTransportTypeTCP)) {
            ctx->lastKnownPeerTransportType = VvcSessionActiveTransportTypeTCP;
         }
      }

      UNLOCK_SESSION(session);
   } else {
      /*
       * Raw channel receives are zero-copy in VVC. Unlike traditional channels,
       * there is no reason to memcpy them for message composition or header
       * strip-off.
       */
      ASSERT(recvState->recvBuf + len <= recvState->recvBufEnd);
      if ((uint8 *)buf >= recvState->recvBuf && (uint8 *)buf < recvState->recvBufEnd) {
         recvState->recvBufOffset += len;
         /*
          * Counter indicates when it is safe for recvBuf to wrap around for
          * reuse. It is not sufficient to just look at buffer pointer
          * passed down in VVCLIB_RecvComplete() because the plugin can release
          * buffers in any order and we cannot assume sequential completions.
          * And also because that API does not provide length of buffer released.
          */
         Atomic_Inc32(&recvState->recvBufCount);
      }

      outstanding = Atomic_ReadInc32(&channel->outstandingRecvs);
      if (outstanding && (outstanding % 100) == 0) {
         VvcWarning("Outstanding uncompleted receives, channel: %s, "
                    "outstanding: %u\n",
                    channel->name, outstanding);
      }

      // XXX: these counters should be atomic and not require a lock
      LOCK_SESSION(session);
      session->recvedBytes += len;
      if (FECAsyncSocket_IsFecSocket(asock)) {
         session->udpRecvedBytes += len;
      } else {
         session->tcpRecvedBytes += len;
      }
      UNLOCK_SESSION(session);

      // handoff recv buffer handling to the event dispatch thread
      LOCK_INSTANCE(session->instance);
      VvcQueueEvent(session->instance, channel->listener->pluginId, VvcEvChannelOnRecv,
                    (VvcCommon *)channel, 0, buf, len, (void *)VVC_ONRECV_RAW_BUFFER,
                    VvcChannelOnRecvEvCb);
      UNLOCK_INSTANCE(session->instance);
   }

   VvcDispatchEvents(session->instance);

   /*
    * Check if a protocol switch was waiting for the last byte to be read on
    * this pipe.
    */
   if (UNLIKELY(ctx->switchByteCount) && ctx->switchByteCount == channel->recvedBytes) {
      AsyncSocket *asockCur, *asockNxt;

      VvcLog("%s: Switch recv to the requested protocol %d at %" FMT64 "u", __FUNCTION__,
             ctx->switchTo, channel->recvedBytes);

      ctx->switchByteCount = 0;

      // Only type VVC channels do protocol switch
      ASSERT(ctx->type == VVC_RAW_CHAN_VVC);
      // Both connections are setup
      ASSERT(ctx->beatAsock && ctx->bweAsock);

      // We received on this asock
      asockCur = asock;

      // We want to switch to the other asock
      if (ctx->switchTo == VVC_RAW_CHAN_TCP) {
         ASSERT(asockCur == ctx->beatAsock);
         asockNxt = ctx->bweAsock;
      } else {
         ASSERT(asockCur == ctx->bweAsock);
         asockNxt = ctx->beatAsock;
      }

      // Switch the loop to the new asock
      asock = asockNxt;
   }

   // do the next receive
   err = VvcRawChannelRecv(ctx, asock);
   if (err != ASOCKERR_SUCCESS) {
      VvcRawAsockErrorCb(err, asock, ctx);
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcRawChannelRecv --
 *
 *    Generic routine for asyncSocket non-blocking recv for raw channels.
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

int
VvcRawChannelRecv(vvcRawChanCtx *ctx, AsyncSocket *asock)
{
   int err;
   void *buf;
   int len;
   VvcRawRecvState *recvState = ctx->recvState;

   ASSERT(recvState->recvBufSize >= recvState->recvBufOffset);

   /*
    * Use the preallocated recvBuf circularly for receives. Unlike traditional
    * channels, we can do a buffer zero-copy handoff to the plugin with raw. No
    * buffering is necessary in VVC.
    *
    * The threshold limit is when we wait for all receives within recvBuf to
    * complete: when recvBufCount drops to 0 *and* we are beyond the threshold,
    * that is when we can wrap around to reuse the entire recvBuf for receives
    * During this wait period, we malloc (in else below) new smaller buffer(s),
    * which are zero-copy too.
    */
   if (recvState->recvBufOffset <= VVC_RAW_RECV_BUF_THRESHOLD) {
      buf = recvState->recvBuf + recvState->recvBufOffset;
      len = recvState->recvBufSize - recvState->recvBufOffset;
   } else {
      buf = Util_SafeMalloc(VVC_RAW_RECV_ALLOC_SIZE);
      len = VVC_RAW_RECV_ALLOC_SIZE;
   }

   err = AsyncSocket_RecvPartial(asock, buf, len, VvcRawChannelRecvCb, ctx);

   if (err != ASOCKERR_SUCCESS) {
      VvcLog("%s: Asock receive failed: %s", __FUNCTION__, AsyncSocket_Err2String(err));
   }

   return err;
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcAllocRawRecvState --
 *
 *    Alloc a sizeable receive buffer once for circular consumption - enables
 *    zero copy.
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

VvcRawRecvState *
VvcAllocRawRecvState()
{
   VvcRawRecvState *recvState;

   recvState = Util_SafeCalloc(1, sizeof *recvState);
   // Allocate recvBuf
   recvState->recvBufSize = VVC_TRANSPORT_RECV_BUF_SIZE;
   recvState->recvBuf = Util_SafeMalloc(recvState->recvBufSize);
   recvState->recvBufEnd = recvState->recvBuf + VVC_TRANSPORT_RECV_BUF_SIZE;

   return recvState;
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcRawChannelAsockSetup --
 *
 *   Readying the connected socket for IO.
 *   Save server asock in ctx (client asock was previously saved).
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

static void
VvcRawChannelAsockSetup(vvcRawChanCtx *ctx, AsyncSocket *asock)
{
   VvcDebug("Saving %s raw channel asock for channel %d",
            ctx->type == VVC_RAW_CHAN_TCP ? "TCP" : "BEAT", ctx->channel->channelId);

   ctx->asock = asock;

   if (ctx->unverifiedAsock == asock) {
      ctx->unverifiedAsock = NULL;
   }

   ctx->recvState = VvcAllocRawRecvState();

   AsyncSocket_SetErrorFn(asock, VvcRawAsockErrorCb, ctx);
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcRawChannelAssignAsock --
 *
 *    Called post auth verification and pairing asock to correct channel on server side, and on
 *    reception of COMMIT op on the client side. Assigns asock and some housekeeping prior to poll
 *    receives on this connection.
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

void
VvcRawChannelAssignAsock(VvcChannel *channel, AsyncSocket *asock, Bool isClient)
{
   vvcRawChanCtx *ctx = channel->rawCtx;

   // client has asock saved, server is about to
   ASSERT((isClient && ctx->asock) || !ctx->asock);

   // setup for first recv
   VvcRawChannelAsockSetup(ctx, asock);

   /*
    * Raw channel asyncsockets and their locks are created at multiple locations,
    * so save the lock pointer locally here, if not already.
    */
   if (!ctx->pollLock) {
      ctx->pollLock = AsyncSocketGetPollParams(asock)->lock;
   }

   VvcAsockBackendSetOptions(asock);

   /*
    * On the server, the step after client verification using auth TLVs is sending the OPEN_COMMIT
    * control op on the control channel. In response to the commit op, client sends OPEN_CHAN_ACK
    * success results. So on the server assert that the state of raw channel is not VvcChannelOpen.
    */
   ASSERT(isClient || channel->state != VvcChannelOpen);
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcRawChannelConnectCommon --
 *
 *    Raw channel connect callback post auth verification on server side,
 *    and post sending auth message (TLVs) on client side.
 *    Kicks off the first recv().
 *
 * Results:
 *    None.
 *
 * Side effects:
 *    Raw channel is now ready for user IO.
 *
 *-----------------------------------------------------------------------------
 */

void
VvcRawChannelConnectCommon(void *cbData)
{
   int err;
   VvcChannel *channel;
   vvcRawChanCtx *ctx;
   VvcChannelHandle cHandle = (VvcChannelHandle)cbData;

   if (!VVC_GET_CHANNEL(cHandle, channel)) {
      VvcLog("%s: Raw channel not present, connection failed\n", __FUNCTION__);
      return;
   }

   ctx = channel->rawCtx;

   VvcLog("Secure %s connection established for raw channel %d\n", VvcGetRawChannelName(ctx->type),
          channel->channelId);

   // kick off first read on the asock
   err = VvcRawChannelRecv(ctx, ctx->asock);
   if (err != ASOCKERR_SUCCESS) {
      VvcRawAsockErrorCb(err, ctx->asock, ctx);
   }

   VVC_YIELD_CHANNEL(channel);
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcRawChannelPreCommitRecv --
 *
 *    Recv callback to span entire channel open operation.
 *    This callback wil likely never fire since it is non-partial and we do not expect large
 *    error responses or application data prior to channel open.
 *
 * Results:
 *    None.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

static void
VvcRawChannelPreCommitRecv(unsigned char *buf, // IN
                           int bufLen,         // IN
                           AsyncSocket *asock, // IN
                           void *clientData)   // IN
{
   // We don't expect to receive this much data pre-commit, so ASSERT to catch any occurance
   ASSERT(0);

   // do not free buf here due to rearming of recv() in AsyncSockets

   VvcWarning("%s: pre-commit recv(), discard %d bytes", __FUNCTION__, bufLen);
}


/*
 *-----------------------------------------------------------------------------
 *
 * VVCLIB_RawChanConnectCb --
 *
 *    TCP connect completion callback from blastSocket layer. Successful connect
 *    results in accepting the channel (sending OPEN_ACK back to peer), while a
 *    failed connect results in fallback to regular. On channel accept, also do
 *    the first recv() here.
 *
 *    Asock param is NULL for failed connection.
 *
 *    This also serves vvclib passdown of a connected TCP socket to assign to
 *    the "VVC" channel ahead of a BENIT protocol switch. The switch could be
 *    local (client) or at the peer (server), that is currently operating over
 *    BEAT. This TCP connection does not happen at channel creation time, so
 *    openChan is NULL for this case.
 *
 *    This is used only on the client end.
 *
 * Results:
 *    None
 *
 * Side effects:
 *    On success, TCP raw channel is now operational.
 *    On error, channel is destroyed.
 *
 *-----------------------------------------------------------------------------
 */

void
VVCLIB_RawChanConnectCb(void *cbData,           // IN
                        void *connectionCookie, // IN
                        AsyncSocket *asock)     // IN
{
   VvcChannelHandle cHandle = (VvcChannelHandle)cbData;
   VvcOpenChan *openChan = (VvcOpenChan *)connectionCookie;
   VvcChannel *channel;
   VvcSession *session;
   vvcRawChanCtx *ctx;
   Bool destroyChannel = FALSE;
   void *recvBuf;

   if (!VVC_GET_CHANNEL(cHandle, channel)) {
      Warning("%s: Raw channel not present", __FUNCTION__);
      return;
   }

   VvcDebug("%s: TCP connection %s for raw channel %d", asock ? "established" : "failed",
            __FUNCTION__, channel->channelId);

   ASSERT(VVC_RAW_CHANNEL(channel));
   ctx = channel->rawCtx;
   session = channel->session;

   ASSERT(!ISLOCKED_SESSION(session));
   LOCK_SESSION(session);

   if (asock) {
      // reset error handler to a vvc local routine
      AsyncSocket_SetErrorFn(asock, VvcRawAsockErrorCb, ctx);

      /*
       * Introduce an early receive with the sole purpose of catching any failures or socket errors
       * after SSL connection is established and before the COMMIT op arrives.  This will protect
       * against any middle-box stripping ALPN for the next hop, that will cause WebSocket upgrade
       * failure and server will return an HTTP error and terminate the connection.  This will
       * result in error callback on the client, which can then fallback to regular channel.
       *
       * So having a recv() here enables extra fallback coverage when something in the network is
       * misbehaving, until the final OPEN_COMMIT op from the server.
       */
      recvBuf = Util_SafeMalloc(VVC_RAW_PRE_COMMIT_RECV_SIZE);
      VvcDebug("%s: Alloc pre-commit recv buffer 0x%p", __FUNCTION__, recvBuf);
      ctx->preCommitBuf = recvBuf;
      /*
       * Block on recv() until commit, or some socket error. Even in error path we do not expect to
       * land in recv callback due to large buffer size. On commit op cancel this recv to install
       * the actual recv loop for this asock.
       */
      AsyncSocket_Recv(asock, recvBuf, VVC_RAW_PRE_COMMIT_RECV_SIZE, VvcRawChannelPreCommitRecv,
                       cbData);

      if (openChan) {
         // asock not assigned to channel awaiting AsyncBwe wrap in blastSockets
         ASSERT(!ctx->asock);
         // assign it now
         ctx->asock = asock;
         UNLOCK_SESSION(channel->session);
      } else {
         // BENIT TCP socket on the client
         VvcLog("%s: TCP asock %d claimed by raw channel %d", __FUNCTION__,
                AsyncSocket_GetID(asock), channel->channelId);
         VvcRawChannelAssignBweAsock(channel, asock);
         VvcAsockBackendSetOptions(asock);
         UNLOCK_SESSION(channel->session);
         // COMMIT op for BENIT's TCP will arrive async, so release channel ref here
         VVC_RELEASE_CHANNEL(channel, VvcTagRawConnect);
      }
   } else {
      if (openChan) {
         // poll callback fallback to regular channel creation
         if (channel->session->instance->instanceBe.pollCallback) {
            channel->session->instance->instanceBe.pollCallback(VvcFallbackToRegularChannel, ctx,
                                                                FALSE, 0);
            UNLOCK_SESSION(channel->session);
         } else {
            VvcStatus st;
            VvcLog("%s: Rejecting TCP raw channel %s", __FUNCTION__, channel->name);
            UNLOCK_SESSION(channel->session);
            st = VVCLIB_RejectChannel(connectionCookie, 0, 0, 0);
            if (!VVC_SUCCESS(st)) {
               VvcError("%s: Failed to reject TCP raw channel:%s, error:%d", __FUNCTION__,
                        channel->name, st);
            }
            destroyChannel = TRUE;
         }
      } else {
         VvcLog("%s: TCP connection for raw channel %s failed, stay on BEAT", __FUNCTION__,
                channel->name);
         UNLOCK_SESSION(channel->session);
      }
   }

   VVC_YIELD_CHANNEL(channel);

   if (destroyChannel) {
      // Deref create tag resulting in channel destroy
      VVC_RELEASE_CHANNEL(channel, VvcTagCreation);
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvRawChannelCtrlOpCommit --
 *
 *    Client-side raw open commit handling. Client sends the open ack, starts recv() on the
 *    sockets and informs the plugin that raw channel is now open.
 *
 * Results:
 *    None
 *
 * Side effects:
 *    OpenChannel operation has now concluded successfully for raw channels at the client.
 *
 *-----------------------------------------------------------------------------
 */

void
VvcRawChannelCtrlOpCommit(VvcChannel *channel)
{
   VvcSession *session = channel->session;
   vvcRawChanCtx *ctx;
   VvcOpenChan *openChan;
   AsyncSocket *asock;
   void *recvBuf = NULL;

   ASSERT(!channel->rawCtx->isServer);
   ASSERT(channel->rawCtx->asock);
   ASSERT(VVC_RAW_CHANNEL(channel));

   ctx = channel->rawCtx;
   openChan = ctx->openReq.openChan;
   ASSERT(openChan);
   asock = ctx->asock;

   LOCK_SESSION(session);

   if (!VvcSessionIsUp(session)) {
      VvcLog("Received VVC_CTRL_OP_RAW_OPEN_COMMIT when session is closing or in an error state, "
             "instance: %s, state: %s\n",
             session->instance->name, VvcDebugSessionStateToString(session->state));
      goto exit;
   }

   /*
    * Mark raw channel creation as done for fallback paths. Any socket error from this point will
    * result in session teardown as it is too late to fallback.
    */
   ctx->openReq.openChan = NULL;

   /*
    * Complete raw openChan ack now that peer has confirmed it has an authenticated connected
    * socket assigned to the channel.
    */
   VvcAcceptChannelInt(channel, openChan);

   // channel can be closed while raw create handshake is ongoing
   if (channel->state == VvcChannelOpen || channel->state == VvcChannelAcceptPaused) {
      ASSERT(channel->rawCtx->rawChannelOnConnect);
      ASSERT(channel->session->instance->instanceBe.pollCallback);

      // setup for first recv for TCP (BEAT did it earlier)
      if (!FECAsyncSocket_IsFecSocket(asock)) {
         // Cancel early pre-commit recv()
         AsyncSocket_CancelRecvEx(asock, NULL, &recvBuf, NULL, TRUE);
         ASSERT(recvBuf && (recvBuf == ctx->preCommitBuf));
         VvcDebug("%s: Cancel pre-commit recv (channel %s, buf %p)", __FUNCTION__, channel->name,
                  recvBuf);
         free(recvBuf);
         ctx->preCommitBuf = NULL;

         VvcRawChannelAssignAsock(channel, ctx->asock, TRUE);
      }

      // kickoff the first recv in poll context
      channel->session->instance->instanceBe.pollCallback(ctx->rawChannelOnConnect,
                                                          TOKEN_HANDLE(channel), FALSE, 0);
   } else {
      /*
       * Very unlikely to get here since client just forwarded auth TLVs and we already checked
       * session state when commit arrived. Cannot proceed to opening the channel from here.
       */
      VvcLog("%s: %s raw channel %s is not open (state: %s)", __FUNCTION__,
             VvcGetRawChannelName(ctx->type), channel->name,
             VvcDebugChannelStateToString(channel->state));
   }

exit:
   // release refs taken on entry of ChannelAccept
   VVC_RELEASE_CHANNEL(channel, VvcTagRawConnect);
   if (openChan) {
      VVC_RELEASE_OPEN_CHAN(openChan, VvcTagRawConnect);
   }

   UNLOCK_SESSION(session);
   VVC_RELEASE_CHANNEL(channel, VvcTagFindChannelFromSession);
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcRawChannelCtrlOpCommit2 --
 *
 *    Client-side raw BENIT TCP commit handling (only server sends commit ops).
 *    "2" stands for the second BENIT connection, first COMMIT of BEAT came earlier.
 *
 * Results:
 *    None
 *
 * Side effects:
 *    BENIT TCP connection ready. Switch client in response to agent switch.
 *
 *-----------------------------------------------------------------------------
 */

void
VvcRawChannelCtrlOpCommit2(VvcChannel *channel)
{
   VvcSession *session = channel->session;
   vvcRawChanCtx *ctx;
   Bool msgQueued = FALSE;
   void *recvBuf = NULL;
   int partialRecvd = 0;
   void *commitAckOp;
   size_t commitAckOpLen;

   ASSERT(VVC_RAW_CHANNEL(channel));
   ASSERT(!channel->rawCtx->isServer);
   ASSERT(channel->rawCtx->asock && channel->rawCtx->bweAsock);
   ASSERT(channel->rawCtx->asock != channel->rawCtx->bweAsock);

   ctx = channel->rawCtx;

   LOCK_SESSION(session);

   if (!VvcSessionIsUp(session)) {
      VvcLog("Received VVC_CTRL_OP_RAW_OPEN_COMMIT when session is closing or in an error state, "
             "instance: %s, state: %s\n",
             session->instance->name, VvcDebugSessionStateToString(session->state));
      goto exit;
   }

   VvcLog("Secure %s TCP connection established for raw channel %d",
          VvcGetRawChannelName(ctx->type), channel->channelId);

   // Cancel early pre-commit recv() installed to catch HTTP errors
   ASSERT(ctx->bweAsock);
   VvcDebug("%s: Cancel pre-commit recv (channel %s, buf %p)", __FUNCTION__, channel->name,
            recvBuf);
   AsyncSocket_CancelRecvEx(ctx->bweAsock, &partialRecvd, &recvBuf, NULL, TRUE);
   ASSERT(recvBuf && (recvBuf == ctx->preCommitBuf));
   ASSERT(partialRecvd == 0);
   free(recvBuf);
   ctx->preCommitBuf = NULL;

   /*
    * Send COMMIT_ACK op to the server, which can then initiate switch to TCP.
    *
    * Note that client does not send COMMIT_ACK for openChannel BEAT/TCP connections as they have
    * OpenChannelAck that can help synchronize on the server side.
    */
   commitAckOp = VvcBuildRawCommitAckOp(channel, &commitAckOpLen);
   VvcQueueMessage(session->ctrlChannel, commitAckOp, commitAckOpLen, FALSE, VvcDefaultCtrlMsg, 0,
                   0);
   msgQueued = TRUE;

   /*
    * Client may have a switch pending awaiting first move by the server. Commit2 implies server has
    * initiated a switch which resulted in this TCP connection, so now is a good time for client to
    * follow up on that deferred switch.
    *
    * Otherwise, just force client raw channel to switch in response to agent switch (tandem switch)
    * for continuous bandwidth estimation on asyncBWE socket.
    */
   if (ctx->switchInitiator) {
      // "deferred" means client had attempted a switch earlier
      VvcLog("%s: Deferred switch to TCP for channel %s", __FUNCTION__, channel->name);
   } else {
      // "tandem" means client is forced to switch after agent switches
      VvcLog("%s: Tandem switch to TCP for channel %s", __FUNCTION__, channel->name);
      ctx->switchInitiator = TRUE;
   }

   /*
    * Tandem switching implies client switches with server, and we know server is switching as it
    * requested this new TCP connection for switching.
    */
   ASSERT(ctx->switchInitiator);
   msgQueued |= VvcRawChannelSwitchProtocol(channel, VVC_RAW_CHAN_TCP);
   channel->rawCtx->switchInitiator = FALSE;

exit:
   UNLOCK_SESSION(session);
   if (msgQueued) {
      VvcDispatchSendQueues(channel->session, VvcDispatchSendTriggerOnCtrl);
   }
   VVC_RELEASE_CHANNEL(channel, VvcTagFindChannelFromSession);
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcRawChannelCtrlOpCommitAck --
 *
 *    Server-side raw BENIT TCP commit ack handling (only client sends commit acks).
 *    This function is only invoked for BENIT's TCP connection to hint the server that it is now
 *    safe to perform a switch.
 *
 * Results:
 *    None
 *
 * Side effects:
 *    BENIT TCP connection ready, initiate server switch.
 *
 *-----------------------------------------------------------------------------
 */

void
VvcRawChannelCtrlOpCommitAck(VvcChannel *channel)
{
   VvcSession *session = channel->session;
   vvcRawChanCtx *ctx = channel->rawCtx;
   Bool msgQueued = FALSE;

   ASSERT(VVC_RAW_CHANNEL(channel));
   ASSERT(channel->rawCtx->isServer);
   ASSERT(channel->rawCtx->asock && channel->rawCtx->bweAsock);
   ASSERT(channel->rawCtx->asock != channel->rawCtx->bweAsock);

   LOCK_SESSION(session);
   ASSERT(ctx->switchInitiator);
   if (ctx->switchInitiator) {
      // BENIT TCP connection is now ready for use, trigger the switch
      msgQueued = VvcRawChannelSwitchProtocol(channel, VVC_RAW_CHAN_TCP);
      channel->rawCtx->switchInitiator = FALSE;
   }
   UNLOCK_SESSION(session);

   if (msgQueued) {
      VvcDispatchSendQueues(channel->session, VvcDispatchSendTriggerOnCtrl);
   }
   VVC_RELEASE_CHANNEL(channel, VvcTagFindChannelFromSession);
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcRawChannelBeatSetupMsgSendCb --
 *
 *    BEAT setup msg send cb.
 *    Auth message sent, so it is OK for client to accept this channel.
 *    Similar in functionality to TCP equivalent VVCLIB_RawChanConnectCb.
 *
 * Results:
 *    None
 *
 * Side effects:
 *    Channel is accepted on the client after having done all the connection
 *    and auth setup.
 *
 *-----------------------------------------------------------------------------
 */

static void
VvcRawChannelBeatSetupMsgSendCb(void *buf, int len, AsyncSocket *asock, void *cbData)
{
   VvcOpenChan *openChan = (VvcOpenChan *)cbData;
   VvcChannel *channel;

   channel = openChan->raw.beat.channel;
   ASSERT(channel->rawCtx);

   free(buf);
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcRawChannelBeatConnectCb --
 *
 *    Connect callback for raw channel BEAT connection.
 *    Construct and send auth setup message as the first packet on the secure
 *    connection.
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

static void
VvcRawChannelBeatConnectCb(AsyncSocket *asock, void *userData)
{
   VvcStatus st;
   int err = ASOCKERR_GENERIC;
   VvcChannel *channel;
   vvcRawChanCtx *ctx;
   char *setupMsg = NULL;
   int len = 0;
   VvcOpenChan *openChan = (VvcOpenChan *)userData;

   VvcLog("%s: Raw channel connectCb issued for asock %d (0x%p)", __FUNCTION__,
          AsyncSocket_GetID(asock), asock);

   /*
    * client sends auth message after connection is established and the server
    * verifies it.
    */
   channel = openChan->raw.beat.channel;
   ASSERT(channel->rawCtx);
   VERIFY(channel->flags & VVC_CHANNEL_RAW);

   ctx = channel->rawCtx;
   VERIFY(ctx->channel != NULL);
   ctx->rawChannelOnConnect = VvcRawChannelConnectCommon;

   ASSERT(!ctx->isServer);

   VvcRawChannelAssignAsock(channel, asock, TRUE);

   st = VvcGetSetupMsgFromTransportBe(channel->session, &setupMsg, &len);
   if (VVC_SUCCESS(st)) {
      err = AsyncSocket_Send(asock, setupMsg, len, VvcRawChannelBeatSetupMsgSendCb, userData);
   }
   if (err != ASOCKERR_SUCCESS) {
      free(setupMsg);
      VvcError("%s: Could not send BEAT setup message: %s", __FUNCTION__,
               AsyncSocket_Err2String(err));
      // release refs taken prior to beatConnect
      VVC_RELEASE_CHANNEL(channel, VvcTagRawConnect);
      VVC_RELEASE_OPEN_CHAN(openChan, VvcTagRawConnect);
      VvcRawAsockErrorCb(err, asock, ctx);
   } else {
      VvcDebug("%s: sent BEAT setup message", __FUNCTION__);
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcRawChannelBeatListenCb --
 *
 *    Server-side raw connect callback for BEAT.
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

#ifdef _WIN32
static void
VvcRawChannelBeatListenCb(AsyncSocket *asock, void *userData)
{
   VvcStatus st;
   vvcRawChanCtx *ctx;

   VvcLog("%s: connectCb issued for asock %d (0x%p)\n", __FUNCTION__, AsyncSocket_GetID(asock),
          asock);

   ctx = (vvcRawChanCtx *)userData;
   ASSERT(ctx->isServer);
   ASSERT(ctx->channel);
   ASSERT(VVC_RAW_CHANNEL(ctx->channel));
   ASSERT(!ctx->asock);
   ASSERT(ctx->channel->rawCtx == ctx);

   AsyncSocket_SetErrorFn(asock, VvcRawAsockErrorCb, ctx);

   /*
    * Save the unverified asock for channel matching post verification.
    *
    * XXX: unify BEAT channel matching like TCP using serialNo in future,
    * then we can get rid of unverifiedAsock variable.
    */
   ctx->unverifiedAsock = asock;

   st = VvcVerifySetupMsgFromTransportBe(asock, ctx->channel->session);
   VvcDebug("%s: BEAT setup msg verification %sinitiated for asock 0x%p:\n", __FUNCTION__,
            st == VVC_STATUS_SUCCESS ? "" : "not ", asock);

   // post-verification read is triggered in VvcAddAsockBackend

   if (st != VVC_STATUS_SUCCESS) {
      VvcRawAsockErrorCb(ASOCKERR_GENERIC, asock, ctx);
   }
}
#endif


/*
 *-----------------------------------------------------------------------------
 *
 * VvcRawChanTcpConnect --
 *
 *    Connect side implementation for TCP raw channel.
 *    TcpConnect is not fully contained within VVC like BeatConnect since
 *    endpoint URL is readily available in blastSockets layer.
 *    Someday we can extend getAuxFlowInfoCb to fetch this from blastSockets so
 *    VVC can do TCP connects as well, using getSetupMsgCb for auth info.
 *
 * Results:
 *    vvcRawChanCtx - raw channel context on successful connect request
 *    NULL otherwise
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

vvcRawChanCtx *
VvcRawChanTcpConnect(VvcChannel *channel,    // IN
                     void *connectionCookie, // IN
                     unsigned char serialNo) // IN
{
   vvcRawChanCtx *ctx;
   Bool ret;

   ASSERT(VVC_RAW_CHANNEL(channel));
   if (channel->session->transportBe.flags & VVC_TRANSPORT_BE_SERVER) {
      VvcWarning("%s: Cannot connect from server peer", __FUNCTION__);
      return NULL;
   }

   ret = channel->session->transportBe.tcpConnect(channel->session, serialNo, TOKEN_HANDLE(channel),
                                                  connectionCookie);
   if (!ret) {
      return NULL;
   }

   ctx = (vvcRawChanCtx *)Util_SafeCalloc(1, sizeof(*ctx));
   ctx->channel = channel;
   ctx->isServer = FALSE;
   ctx->rawChannelOnConnect = VvcRawChannelConnectCommon;

   VvcLog("%s: TCP connect initiated for raw channel %s", __FUNCTION__, channel->name);

   return ctx;
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcRawChanBeatConnect --
 *
 *    Connect side implementation for BEAT raw channel.
 *
 * Results:
 *    vvcRawChanCtx - raw channel context on successful connect request
 *    NULL otherwise
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

vvcRawChanCtx *
VvcRawChanBeatConnect(VvcOpenChan *openChan) // IN
{
   VvcStatus st;
   int errorOut;
   size_t infoLen;
   vvcRawChanCtx *ctx;
   VvcSession *session;
   VvcAuxiliaryFlowInfo info = {0};
   AsyncSocketPollParams pollParams;
   FECSocketOptionsStatic staticOpts;
   SSLVerifyParam sslParams;
   VvcChannel *channel;
   uint16 beatPort, connPort;

   channel = openChan->raw.beat.channel;

   beatPort = openChan->raw.beat.port;

   ASSERT(VVC_RAW_CHANNEL(channel));
   ASSERT(beatPort < (1 << 16));

   if (channel->session->transportBe.flags & VVC_TRANSPORT_BE_SERVER) {
      VvcWarning("%s: Cannot connect from server peer", __FUNCTION__);
      return NULL;
   }

   // channel already has a reference on session and instance
   session = channel->session;
   ASSERT(ISLOCKED_SESSION(session));

   infoLen = sizeof(info);
   /*
    * Not using VVCLIB_GetInfo because that entails taking gGlobalLock which
    * would force us to release session lock first. Since instance is referenced
    * by the channel, it's fine to directly call the internal
    * VvcGetInfoAuxiliaryFlowInfo implementation while holding the session lock.
    */
   st = VvcGetInfoAuxiliaryFlowInfo(session->instance, VvcInfoAuxiliaryFlowInfo, 0,
                                    &session->sessionId, sizeof(session->sessionId), &info,
                                    &infoLen, &sslParams);
   if (st != VVC_STATUS_SUCCESS) {
      VvcError("AuxFlowInfo unavailable, aborting BEAT raw channel creation");
      return NULL;
   }

   VvcLog("%s: Connecting BEAT raw channel %d\n", __FUNCTION__, channel->channelId);

#ifdef VMX86_DEVEL
   VvcLog("SSL thumbprint: %s", sslParams.expectedThumbprintString);
#endif

   ctx = (vvcRawChanCtx *)Util_SafeCalloc(1, sizeof(*ctx));
   ctx->channel = channel;
   ctx->isServer = FALSE;

   ctx->pollLock = MXUser_CreateRecLock("VvcRawChanLock", RANK_UNRANKED);
   pollParams.pollClass = session->blastSocketThreadEnabled ? POLL_DEFAULT_CS_NET : POLL_CS_MAIN;
   pollParams.flags = 0;
   pollParams.lock = ctx->pollLock;

   staticOpts = FECSocketOptionsStatic_CreateDefault();

   // For clients connecting to BSG, HMAC is a requirement for BEAT
   if (info.afiHMACKeySize) {
      if (!SSL_GetThumbprintTypeByDigestName(info.afiHMACAlgo, &staticOpts.hmacAlgorithm)) {
         VvcWarning("%s: Invalid BEAT HMAC algorithm [%s], aborting BEAT "
                    "raw channel creation",
                    __FUNCTION__, info.afiHMACAlgo);
         free(ctx);
         return NULL;
      }
   }

   /*
    * Set the same keepalive timer as blastSockets (for secondary). Default
    * fec timeout (1hr) causes rexmit failures from home networks (probably NAT
    * holes for UDP time out aggressively) when idle.
    */
   staticOpts.keepaliveTimeout =
      MIN(TRANSPORT_KEEPALIVE_TIMEOUT_SEC / 2, FEC_SOCKET_OPTIONS_KEEPALIVE_SECS);

   VERIFY(info.afiPort <= 0xffff);

   // save ctx for use in connectCb
   channel->rawCtx = ctx;

   UNLOCK_SESSION(session);

#ifdef BENEV
   connPort = beatPort;
#else
   connPort = info.afiPort;
#endif

   ctx->asock = FECAsyncSocket_Connect(
      info.afiDest, connPort, VvcRawChannelBeatConnectCb, (void *)openChan, 0, &pollParams, TRUE,
      SSL_DefaultClientContext(), &sslParams, (info.afiLabel & 0xFFFF0000) | (beatPort & 0xFFFF),
      info.afiHMACKeyBuf, info.afiHMACKeySize, &staticOpts, &errorOut);
   if (ctx->asock == NULL) {
      VvcError("%s: Remote connect failed, error = %d (%s)", __FUNCTION__, errorOut,
               AsyncSocket_Err2String(errorOut));
      free(ctx);
      LOCK_SESSION(session);
      return NULL;
   }

   AsyncSocket_SetErrorFn(ctx->asock, VvcRawAsockErrorCb, ctx);

   VvcLog("%s: BEAT raw channel connection requested to destination %s:%d\n", __FUNCTION__,
          info.afiDest, beatPort);
   LOCK_SESSION(session);

   return ctx;
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcGetUdpPortRangesFromRegistry --
 *
 *      On Windows, obtain BEAT sidechannel port ranges reserved for UDP.
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

#ifdef _WIN32
static LONG
VvcGetUdpPortRangesFromRegistry(unsigned int *portStart1, // OUT
                                unsigned int *portStart2, // OUT
                                unsigned int *portStart3, // OUT
                                unsigned int *numPorts)   // OUT
{
   char path[1024];
   HKEY key;
   LONG res;
   int st;
   char *value = 0;
   uint32 type, valueLen, len;

   st =
      Str_Snprintf(path, sizeof(path), "System\\CurrentControlSet\\Services\\hznvudpd\\Parameters");
   ASSERT(st > 0);

   res = Win32U_RegOpenKeyEx(HKEY_LOCAL_MACHINE, path, 0, KEY_QUERY_VALUE, &key);
   if (res != ERROR_SUCCESS) {
      VvcWarning("%s: Win32U_RegOpenKeyEx failed for %s: 0x%lx\n", __FUNCTION__, path, res);
      return res;
   }

   res =
      Win32U_AllocRegQueryValueEx(key, "UdpBeatSideChannelStartPorts", 0, &type, &value, &valueLen);
   if (res != ERROR_SUCCESS || type != REG_MULTI_SZ) {
      VvcWarning("%s: Win32U_AllocRegQueryValueEx failed for %s: 0x%lx\n", __FUNCTION__, path, res);
      goto out;
   }

   // Obtain up to 3 port ranges (current count of sidechannel port ranges is 3)
   *portStart1 = *portStart2 = *portStart3 = 0;
   len = strlen(value) + 1;
   if (sscanf(value, "%d", portStart1) < 1) {
      VvcWarning("%s: Port ranges not configured for BEAT raw channels", __FUNCTION__);
      res = ERROR_INVALID_DATA;
      goto out;
   } else if (valueLen > len + 1 && sscanf(value + len, "%d", portStart2) < 1) {
      VvcWarning("%s: Invalid BEAT port range 2", __FUNCTION__);
   } else {
      len += strlen(value + len) + 1;
      if (valueLen > len + 1 && sscanf(value + len, "%d", portStart3) < 1) {
         VvcWarning("%s: Invalid BEAT port range 3", __FUNCTION__);
      }
   }

   free(value);
   value = 0;

   res =
      Win32U_AllocRegQueryValueEx(key, "UdpBeatSideChannelNumPorts", 0, &type, &value, &valueLen);
   if (res != ERROR_SUCCESS || type != REG_SZ) {
      VvcWarning("%s: Win32U_AllocRegQueryValueEx failed for %s: 0x%lx\n", __FUNCTION__, path, res);
      goto out;
   }

   *numPorts = atoi(value);

out:
   if (key) {
      RegCloseKey(key);
   }
   if (value) {
      free(value);
   }

   return res;
}
#endif


/*
 *-----------------------------------------------------------------------------
 *
 * VvcRawChanTcpListen --
 *
 *    Unlike BEAT, TCP listening socket is not created here. Blast Service is
 *    already listening for incoming connections on the single (consolidated)
 *    Blast port (usually 22443). This function simply does some init
 *    anticipating a connection from the peer (horizon client).
 *
 * Results:
 *    vvcRawChanCtx - raw channel context on success
 *    NULL otherwise
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

static vvcRawChanCtx *
VvcRawChanTcpListen(void)
{
   vvcRawChanCtx *ctx;
   ctx = (vvcRawChanCtx *)calloc(1, sizeof(*ctx));
   ctx->isServer = TRUE;
   ctx->rawChannelOnConnect = VvcRawChannelConnectCommon;
   return ctx;
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcRawChanBeatListen --
 *
 *    Listen for BEAT connections on a suitable port which is then communicated
 *    to the connecting end.
 *
 * Results:
 *    vvcRawChanCtx - raw channel context on success
 *    NULL otherwise
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

static vvcRawChanCtx *
VvcRawChanBeatListen(VvcSession *session) // IN
{
   VvcStatus st = VVC_STATUS_ERROR;
   FECSocketOptionsStatic staticOpts;
   AsyncSocketPollParams pollParams;
   vvcRawChanCtx *ctx;
   void *sslCtx;

   /*
    * Regular vvc channels are overlaid on established transport connections,
    * so either peer can initiate channelOpen. Raw channels however need to
    * form new connections, hence limited to server-side listen/channelOpen.
    */
   if (!(session->transportBe.flags & VVC_TRANSPORT_BE_SERVER)) {
      VvcWarning("%s: Cannot listen on client peer", __FUNCTION__);
      return NULL;
   }

   sslCtx = VvcGetSslContextFromTransportBe(session);
   if (!sslCtx) {
      VvcWarning("%s: Blast SSL context missing, is UDP enabled?", __FUNCTION__);
      return NULL;
   }

   ASSERT(ISLOCKED_SESSION(session));
   UNLOCK_SESSION(session);

   ctx = (vvcRawChanCtx *)calloc(1, sizeof(*ctx));
   ctx->pollLock = MXUser_CreateRecLock("VvcRawChanLock", VVC_RAW_CHANNEL_LOCK);
   ctx->isServer = TRUE;
   ctx->rawChannelOnConnect = VvcRawChannelConnectCommon;

   staticOpts = FECSocketOptionsStatic_CreateDefault();

   pollParams.pollClass = session->blastSocketThreadEnabled ? POLL_DEFAULT_CS_NET : POLL_CS_MAIN;
   pollParams.flags = 0;
   pollParams.lock = ctx->pollLock;

#ifdef _WIN32
   LONG res;
   unsigned int portStart1, portStart2, portStart3, numPorts = 0;
   uint64 token;
   SOCKET fd;
   int errorOut;
   unsigned int port = 0;
   DWORD cbOutBuffer = 0;
   const char *addrStr;
   struct addrinfo hints, *addr;

   res = VvcGetUdpPortRangesFromRegistry(&portStart1, &portStart2, &portStart3, &numPorts);
   if (UNLIKELY(res != ERROR_SUCCESS) || numPorts == 0) {
      VvcWarning("%s: VvcGetUdpPortRangeFromRegistry failed: 0x%lx", __FUNCTION__, res);
      goto out;
   }

   VvcDebug("%s: UDP portStarts %d %d %d, numPorts %d", __FUNCTION__, portStart1, portStart2,
            portStart3, numPorts);

#   ifdef BENEV
   unsigned int portArr[3] = {portStart1, portStart2, portStart3};
   for (int i = 0; i < sizeof portArr; i++) {
      res = DeletePersistentUdpPortReservation(ntohs(portArr[i]), numPorts);
      if (res != ERROR_SUCCESS && res != ERROR_NOT_FOUND) {
         VvcWarning("%s: DeletePersistentUdpPortReservation failed: 0x%lx for portStart: %u\n",
                    __FUNCTION__, res, portArr[i]);
      }
      res = CreatePersistentUdpPortReservation(ntohs(portArr[i]), numPorts, &token);
      if (res != ERROR_SUCCESS) {
         VvcDebug("%s: CreatePersistentUdpPortReservation failed: 0x%lx for portStart: %u\n",
                  __FUNCTION__, res, portArr[i]);
      }
   }
#   endif

   res = LookupPersistentUdpPortReservation(ntohs(portStart1), numPorts, &token);
   if (res != ERROR_SUCCESS && portStart2) {
      res = LookupPersistentUdpPortReservation(ntohs(portStart2), numPorts, &token);
      if (res != ERROR_SUCCESS && portStart3) {
         res = LookupPersistentUdpPortReservation(ntohs(portStart3), numPorts, &token);
      }
   }

   if (res != ERROR_SUCCESS) {
      VvcWarning("%s: LookupPersistentUdpPortReservation failed: 0x%lx\n", __FUNCTION__, res);
      goto out;
   }

   memset(&hints, 0, sizeof(hints));
   hints.ai_family = AF_UNSPEC;
   hints.ai_socktype = SOCK_DGRAM;
   hints.ai_flags = AI_PASSIVE;
   hints.ai_protocol = 0;
   addrStr = "0.0.0.0";

   res = getaddrinfo(addrStr, NULL, &hints, &addr);
   VERIFY(res == ERROR_SUCCESS);

   fd = socket(AF_INET, addr->ai_socktype, addr->ai_protocol);
   if (fd == INVALID_SOCKET) {
      VvcWarning("%s: socket creation failed", __FUNCTION__);
      goto out;
   }
   res = WSAIoctl(fd, SIO_ASSOCIATE_PORT_RESERVATION, &token, sizeof(token), NULL, 0, &cbOutBuffer,
                  NULL, NULL);
   if (res != ERROR_SUCCESS) {
      VvcWarning("%s: WSAIoctl failed\n", __FUNCTION__);
      goto out;
   }

   // coverity[negative_returns]
   res = bind(fd, addr->ai_addr, addr->ai_addrlen);
   VERIFY(res == ERROR_SUCCESS);
   freeaddrinfo(addr);

   ctx->parent =
      FECAsyncSocket_ListenOnBoundSocket(fd, INVALID_SOCKET, VvcRawChannelBeatListenCb, ctx,
                                         &pollParams, TRUE, sslCtx, &staticOpts, &errorOut);
   if (!ctx->parent) {
      VvcWarning("FEC socket listen failed: %d\n", errorOut);
   } else {
      st = VVC_STATUS_SUCCESS;
   }

#else
   VvcWarning("Beat raw channels are not yet supported");
   goto out;
#endif

out:
   if (st != VVC_STATUS_SUCCESS) {
      MXUser_DestroyRecLock(pollParams.lock);
      free(ctx);
      ctx = NULL;
   }

   LOCK_SESSION(session);
   return ctx;
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcRawCreateContext --
 *
 *    Init raw channel context.
 *
 * Results:
 *    Raw context on success, NULL otherwise.
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

static vvcRawChanCtx *
VvcRawCreateContext(VvcSession *session) // IN
{
   vvcRawChanCtx *ctx = NULL;

   switch (session->rawChannelType) {
   case VVC_RAW_CHAN_BEAT:
      ctx = VvcRawChanBeatListen(session);
      break;
   case VVC_RAW_CHAN_TCP:
      ctx = VvcRawChanTcpListen();
      break;
   case VVC_RAW_CHAN_VVC:
      // Raw channel type VVC supports BENIT switching, and starts as BEAT
      ctx = VvcRawChanBeatListen(session);
      break;
   default:
      VvcWarning("Invalid raw channel type: %d", session->rawChannelType);
      break;
   }

   return ctx;
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcRawChannelFailover --
 *
 *    Failover scenarios at raw channel create time on server.
 *
 * Results:
 *    None
 *
 * Side effects:
 *    Certain conditions may cause failover of raw to regular channel type.
 *
 *-----------------------------------------------------------------------------
 */

void
VvcRawChannelFailover(VvcSession *session,        // IN
                      char *name,                 // IN
                      VvcRawChanType channelType, // IN
                      uint32 *flags,              // IN/OUT
                      vvcRawChanCtx **rawCtx)     // OUT
{
   VvcQoSPolicyParams qp;

   ASSERT(*flags & VVC_CHANNEL_RAW);

   memset(&qp, VVC_QOS_INVALID_VALUE, sizeof qp);

   if (!session->negotiatedRawChannelSupport || session->negotiatedVerMajor < 4) {
      VvcLog("Raw channels not negotiated, fallback to regular: %s, sessionId: %d", name,
             session->sessionId);
      *flags ^= VVC_CHANNEL_RAW;
   } else if (!session->tcpBweNegotiated) {
      VvcLog("Raw channels dependency AsyncBwe not negotiated, fallback to regular: %s, "
             "sessionId: %d",
             name, session->sessionId);
      *flags ^= VVC_CHANNEL_RAW;
   } else if (!(*flags & VVC_CHANNEL_DECLINE_NC)) {
      VvcLog("Raw channels do not support NC, fallback to regular: %s, sessionId: %d", name,
             session->sessionId);
      *flags ^= VVC_CHANNEL_RAW;
   } else if (session->numRawChannels >= VVC_MAX_RAW_CHANNELS) {
      VvcLog("Raw channels exceeding max, fallback to regular: %s, sessionId: %d", name,
             session->sessionId);
      *flags ^= VVC_CHANNEL_RAW;
   } else if (!FECAsyncSocket_IsInit() && channelType != VVC_RAW_CHAN_TCP) {
      VvcLog("UDP is disabled, fallback raw channel to regular: %s", name);
      *flags ^= VVC_CHANNEL_RAW;
   } else if (session->qosPolicy && memcmp(&session->qosPolicy->v1, &qp.v1, sizeof qp.v1)) {
      VvcLog("QoS policy in place, fallback raw channel to regular: %s", name);
      *flags ^= VVC_CHANNEL_RAW;
   } else if (!(*rawCtx = VvcRawCreateContext(session))) {
      // keep this as the final check due to allocation of rawCtx
      VvcLog("Null raw context, fallback to regular: %s", name);
      *flags ^= VVC_CHANNEL_RAW;
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcRawChannelClientFailover --
 *
 *    Failover scenarios at raw channel accept time on client.
 *
 * Results:
 *    TRUE if raw channels cannot be created, FALSE otherwise
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

Bool
VvcRawChannelClientFailover(VvcSession *session, // IN
                            char *name)          // IN
{
   VvcQoSPolicyParams qp;
   memset(&qp, VVC_QOS_INVALID_VALUE, sizeof qp);

   if (!session->negotiatedRawChannelSupport) {
      VvcLog("Raw channels not negotiated, fallback to regular: %s, "
             "sessionId: %d",
             name, session->sessionId);
      return TRUE;
   }
   if (session->qosPolicy && memcmp(&session->qosPolicy->v1, &qp.v1, sizeof qp.v1)) {
      VvcLog("QoS policy in place, fallback raw channel to regular: %s", name);
      return TRUE;
   }

   return FALSE;
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcRawSendCompleteCb --
 *
 *    Raw channel send callback
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

static void
VvcRawSendCompleteCb(void *buf, int len, AsyncSocket *asock, void *cbData)
{
   VvcSessionTransportSendComplete(cbData, VVC_STATUS_SUCCESS, buf, len, NULL);
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcRawChannelSend --
 *
 *      Raw channel send handling. Session is locked by caller.
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

VvcStatus
VvcRawChannelSend(VvcChannel *channel, // IN
                  uint8 *buf,          // IN
                  size_t len,          // IN
                  void *clientData)    // IN
{
   int err;
   AsyncSocket *asock;
   VvcSession *session;
   Bool eventQueued;
   VvcSendCompletionContext *sendCompletionContext;
   vvcRawChanCtx *ctx;

   sendCompletionContext = (VvcSendCompletionContext *)clientData;
   VvcInstance *instance = sendCompletionContext->msg->channel->session->instance;

   ctx = channel->rawCtx;
   ASSERT(ctx->asock);

   if (UNLIKELY(channel->state != VvcChannelOpen)) {
      VvcError("Failed to send, raw channel %s is not conected (state %d)\n",
               channel->name ? channel->name : channel->listener->name, channel->state);
      return VVC_STATUS_TRANSPORT_ERROR;
   }

   session = channel->session;
   asock = ctx->asock;

   // raw channel lock order: asock lock -> session/instance/channel lock
   ASSERT(ISLOCKED_SESSION(session));
   UNLOCK_SESSION(session);

   if (UNLIKELY(AsyncSocket_GetState(asock) != AsyncSocketConnected)) {
      VvcError("Failed to send, raw channel %s not connected. Asock state: %d\n",
               channel->name ? channel->name : channel->listener->name,
               AsyncSocket_GetState(asock));
      LOCK_SESSION(session);
      return VVC_STATUS_TRANSPORT_ERROR;
   }

   err = AsyncSocket_Send(asock, buf, (int)len, VvcRawSendCompleteCb, sendCompletionContext);
   if (err != ASOCKERR_SUCCESS) {
      VvcError("%s: Send failed for raw channel %d: %s", __FUNCTION__, channel->channelId,
               AsyncSocket_Err2String(err));
      LOCK_SESSION(session);
      return VVC_STATUS_TRANSPORT_ERROR;
   }

   eventQueued =
      VVC_RELEASE_SEND_COMPLETION_CONTEXT(sendCompletionContext, FALSE, VvcTagSendMsg, NULL);
   if (eventQueued) {
      VvcDispatchEvents(instance);
   }

   LOCK_SESSION(session);

   // update session and channel bytes sent counters
   session->sentBytes += len;
   session->rawSentBytes += len;
   if (FECAsyncSocket_IsFecSocket(asock)) {
      session->udpSentBytes += len;
   } else {
      session->tcpSentBytes += len;
   }
   ctx->sentBytes += len;

   // BENIT switch watch

   // sentBytes cannot exceed switch cutoff due to message boundary
   ASSERT((ctx->localSwitchByteCount == 0) || (ctx->sentBytes <= ctx->localSwitchByteCount));

   if (UNLIKELY(ctx->localSwitchByteCount && (ctx->sentBytes == ctx->localSwitchByteCount))) {
      ctx->localSwitchByteCount = 0;
      VvcDebug("%s: Swap sender and passive sockets at sent bytes %" FMT64 "u", __FUNCTION__,
               ctx->sentBytes);
      if (FECAsyncSocket_IsFecSocket(asock)) {
         ctx->asock = ctx->bweAsock;
      } else {
         ctx->asock = ctx->beatAsock;
      }
   }

   return VVC_STATUS_SUCCESS;
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcRawChannelClose --
 *
 *      Close asyncsockets on raw channel close. Also called for cleanup when
 *      raw channel gets degraded to regular by the client.
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

void
VvcRawChannelClose(VvcChannel *channel) // IN
{
   AsyncSocket *asock, *activeAsock;
   struct vvcRawChanContext *ctx = channel->rawCtx;
   Bool sessionLocked;
   int id, err;

   VvcLog("%s: channel %s, state %s", __FUNCTION__, channel->name,
          VvcDebugChannelStateToString(channel->state));

   sessionLocked = ISLOCKED_SESSION(channel->session);
   if (sessionLocked) {
      UNLOCK_SESSION(channel->session);
   }

   asock = activeAsock = ctx->unverifiedAsock ? ctx->unverifiedAsock : ctx->asock;
   if (asock && (AsyncSocket_GetState(asock) != AsyncSocketClosed)) {
      id = AsyncSocket_GetID(asock);
      err = AsyncSocket_Close(asock);
      if (err != ASOCKERR_SUCCESS) {
         VvcError("Raw channel %d asyncsocket %d close failed: %s", channel->channelId, id,
                  AsyncSocket_Err2String(err));
      } else {
         VvcLog("Raw channel %d asyncsocket %d closed", channel->channelId, id);
      }

      ASSERT(err == ASOCKERR_SUCCESS); // close() not expected to fail
      ctx->unverifiedAsock = ctx->asock = NULL;
   }

   /*
    * In "VVC" type raw channels, rawCtx->asock points to either bweAsock
    * or beatAsock; so check which one remains open and close it.
    * Non-VVC raw channels (type BEAT/TCP) do not use beatAsock or bweAsock
    * pointers, their single asock is at rawCtx->asock.
    */
   if (ctx->bweAsock) {
      asock = (activeAsock == ctx->bweAsock) ? ctx->beatAsock : ctx->bweAsock;
      ASSERT(asock);
      if (asock && (AsyncSocket_GetState(asock) != AsyncSocketClosed)) {
         id = AsyncSocket_GetID(asock);
         err = AsyncSocket_Close(asock);
         if (err != ASOCKERR_SUCCESS) {
            VvcError("Raw channel %d asyncsocket %d close failed: %s", channel->channelId, id,
                     AsyncSocket_Err2String(err));
         } else {
            VvcLog("Raw channel %d asyncsocket %d closed", channel->channelId, id);
         }
         ASSERT(err == ASOCKERR_SUCCESS);
      }
      ctx->bweAsock = ctx->beatAsock = NULL;
   }

   // Close listening BEAT socket on the server side
   asock = ctx->parent;
   if (asock) {
      // assert to find out if listening socket gets closed elsewhere
      ASSERT(AsyncSocket_GetState(asock) != AsyncSocketClosed);
      if (AsyncSocket_GetState(asock) != AsyncSocketClosed) {
         id = AsyncSocket_GetID(asock);
         err = AsyncSocket_Close(asock);
         if (err != ASOCKERR_SUCCESS) {
            VvcLog("Raw channel %d listening asyncsocket %d close failed: %s", channel->channelId,
                   id, AsyncSocket_Err2String(err));
         } else {
            VvcLog("Raw channel %d listening asyncsocket %d closed", channel->channelId, id);
         }
         ASSERT(err == ASOCKERR_SUCCESS); // close() not expected to fail
         ctx->parent = NULL;
      }
   }

   if (sessionLocked) {
      LOCK_SESSION(channel->session);
   }
}


/* *-----------------------------------------------------------------------------
 *
 * VvcGetRawChannelType
 *
 *    Converts registry key string into enum value to be used by TLV.
 *
 * Results:
 *    Enum value that represents channel type
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

VvcRawChanType
VvcGetRawChannelType(char *rawChannelType) // IN
{
   if (Str_Strcasecmp(rawChannelType, VVC_RAW_CHANNEL_TYPE_BEAT) == 0) {
      return VVC_RAW_CHAN_BEAT;
   } else if (Str_Strcasecmp(rawChannelType, VVC_RAW_CHANNEL_TYPE_TCP) == 0) {
      return VVC_RAW_CHAN_TCP;
   } else if (Str_Strcasecmp(rawChannelType, VVC_RAW_CHANNEL_TYPE_VVC) == 0) {
      return VVC_RAW_CHAN_VVC;
   }

   return VVC_RAW_CHAN_ERROR;
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcGetRawChannelName --
 *
 *      Get strings for logs
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

const char *
VvcGetRawChannelName(VvcRawChanType type)
{
   switch (type) {
   case VVC_RAW_CHAN_BEAT:
      return "BEAT";
   case VVC_RAW_CHAN_TCP:
      return "TCP";
   case VVC_RAW_CHAN_VVC:
      return "VVC";
   default:
      return "none";
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcRawChannelTcpConnect --
 *
 *      Client initiates a new TCP connection for raw channel.
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

Bool
VvcRawChannelTcpConnect(VvcChannel *channel,           // IN
                        Bool *connectAlreadyInitiated) // OUT
{
   // channel ref released in VVCLIB_RawChanConnectCb
   VVC_ADD_REF_CHANNEL(channel, VvcTagRawConnect);

   if (Atomic_ReadIfEqualWrite(&channel->rawCtx->tcpConnectInProgress, 0, 1) == 0) {
      *connectAlreadyInitiated = FALSE;
      /*
       * NULL value of connectionCookie helps identify Benit driven TCP connect in
       * VVCLIB_RawChanConnectCb().
       */
      return channel->session->transportBe.tcpConnect(channel->session, channel->rawCtx->serialNo,
                                                      TOKEN_HANDLE(channel),
                                                      NULL /*connectionCookie*/);
   } else {
      VvcLog("%s: TCP connect already in progress", __FUNCTION__);
      *connectAlreadyInitiated = TRUE;
      return FALSE;
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcRawChannelCtrlOpConnect --
 *
 *      Client side handling of server's request to proceed with TCP connect().
 *      channelId: raw channel inherting this connection
 *      connectTo: protocol for connection (always TCP)
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

void
VvcRawChannelCtrlOpConnect(VvcSession *session,      // IN
                           uint32 channelId,         // IN
                           VvcRawChanType connectTo) // IN
{
   VvcChannel *channelIt, *channel = NULL;
   Bool sessionLocked;
   DblLnkLst_Links *link;
   Bool connectInProgress = FALSE;

   ASSERT(channelId != VVC_CONTROL_CHANNEL_ID);

   sessionLocked = ISLOCKED_SESSION(session);

   if (!sessionLocked) {
      LOCK_SESSION(session);
   }

   DblLnkLst_ForEach(link, &session->channelList)
   {
      channelIt = DblLnkLst_Container(link, VvcChannel, sessionLink);
      if (channelId == channelIt->channelId) {
         channel = channelIt;
         break;
      }
   }

   if (!channel || !VVC_RAW_CHANNEL(channel)) {
      VvcLog("%s: Raw channel %d does not exist for connect", __FUNCTION__, channelId);
      goto exit;
   }

   if (channel->state != VvcChannelOpen) {
      VvcLog("%s: Channel %s not open, cannot connect", __FUNCTION__, channel->name);
      goto exit;
   }

   if (!channel->rawCtx->asock) {
      VvcLog("%s: Channel %s not connected", __FUNCTION__, channel->name);
      goto exit;
   }

   ASSERT(!(channel->session->transportBe.flags & VVC_TRANSPORT_BE_SERVER));
   ASSERT(channel->rawCtx->serialNo);

   /*
    * Benit capable raw channels (type VVC) start out as BEAT, so the only
    * remaining protocol to connect is TCP.
    */
   ASSERT(connectTo == VVC_RAW_CHAN_TCP);
   if (connectTo != VVC_RAW_CHAN_TCP) {
      VvcLog("%s: Invalid connect protocol, expected: TCP, "
             "requested: BEAT",
             __FUNCTION__);
      goto exit;
   }

   /*
    * If existing connection has failed for some reason and the server is
    * requesting a new connection, we expect cleanup using error handling, but
    * if a stale pointer exists, print a log- we expect server requesting TCP
    * connection to ensure there are no leaks.
    */
   if (channel->rawCtx->bweAsock) {
      VvcLog("%s: Unexpected or stale connected TCP socket pointer (%p)", __FUNCTION__,
             channel->rawCtx->bweAsock);
      channel->rawCtx->bweAsock = NULL;
   }

   if (!VvcRawChannelTcpConnect(channel, &connectInProgress) && !connectInProgress) {
      VvcError("%s: TCP connect failed to switch raw channel %s", __FUNCTION__, channel->name);
      // XXX: connect and switch retry attempts is a potential enhancement
   }

exit:
   if (!sessionLocked) {
      UNLOCK_SESSION(session);
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcRawChannelCtrlOpSwitch --
 *
 *      Handle peer's request to switch to the BENIT recommended protocol.
 *      Both server (agent) and client can request protocol switch at arbitrary
 *      times when conditions are deemed conducive for a switch by BENIT.
 *
 *      Requestor selects the protocol (switchTo) and the cutoff byte count
 *      (switchAt) at which it will switch over to the selected connection. The
 *      receiver of this switch request notes the switch parameters, and
 *      modifies the receive loop to read from the correct connection either
 *      immediately or deferred, depending on when the cutoff byte count has
 *      been read from the connection being switched out.
 *
 * Results:
 *    None
 *
 * Side effects:
 *    Immediate or deferred switch of the receive loop socket.
 *
 *-----------------------------------------------------------------------------
 */

void
VvcRawChannelCtrlOpSwitch(VvcSession *session,     // IN
                          uint32 channelId,        // IN
                          VvcRawChanType switchTo, // IN
                          uint64 switchAt)         // IN
{
   VvcChannel *channelIt, *channel = NULL;
   Bool sessionLocked;
   DblLnkLst_Links *link;
   Bool kickOffRecv = FALSE;
   vvcRawChanCtx *ctx;

   ASSERT(channelId != VVC_CONTROL_CHANNEL_ID);

   sessionLocked = ISLOCKED_SESSION(session);
   if (!sessionLocked) {
      LOCK_SESSION(session);
   }

   DblLnkLst_ForEach(link, &session->channelList)
   {
      channelIt = DblLnkLst_Container(link, VvcChannel, sessionLink);
      if (channelId == channelIt->channelId) {
         channel = channelIt;
         break;
      }
   }

   if (!channel || !VVC_RAW_CHANNEL(channel)) {
      VvcLog("%s: Raw channel %d does not exist for switch", __FUNCTION__, channelId);
      goto exit;
   }

   if (channel->state != VvcChannelOpen) {
      VvcLog("%s: Channel %s not open, cannot switch", __FUNCTION__, channel->name);
      goto exit;
   }

   if (!channel->rawCtx->asock) {
      VvcLog("%s: Channel %s not connected", __FUNCTION__, channel->name);
      goto exit;
   }

   if (!channel->rawCtx->bweAsock) {
      VvcWarning("%s: BWE channel not yet established, cannot switch channel %s to TCP",
                 __FUNCTION__, channel->name);
      goto exit;
   }

   // verify sender will switch at the said barrier
   ASSERT(channel->recvedBytes <= switchAt);

   ctx = channel->rawCtx;

   /*
    * In a perfectly synchronized steady state, switchTo should be obvious
    * and not needed as op parameter. We still send it and use it to
    * confirm expected state, and save last switched protocol.
    */
   ctx->switchTo = switchTo;

   if (channel->recvedBytes == switchAt) {
      VvcLog("%s: Instant switch at %" FMT64 "u for channel %s", __FUNCTION__, switchAt,
             channel->name);
      kickOffRecv = TRUE;
   } else {
      VvcLog("%s: Deferred switch at %" FMT64 "u for channel %s (%" FMT64 "u)", __FUNCTION__,
             switchAt, channel->name, channel->recvedBytes);
      /*
       * Save cutoff byte counter for future switch, continue receiving on
       * current socket till we reach this byte count for this channel.
       */
      ctx->switchByteCount = switchAt;
      goto exit;
   }

   /*
    * SwitchTo BEAT is currently not possible due to BENIT restriction of a
    * single BEAT->TCP switch. This will change in future with multi-switch
    * support and we will remove this ASSERT at that time.
    */
   ASSERT(switchTo == VVC_RAW_CHAN_TCP);

   // Both connections are setup
   ASSERT(ctx->beatAsock && ctx->bweAsock);

   if (!sessionLocked) {
      UNLOCK_SESSION(session);
   }

   // We are ready to kick off recv on the switched-in asock by peer
   if (kickOffRecv) {
      AsyncSocket *asockCur, *asockNxt;
      int err;

      // We want to switch into the correct asock
      if (switchTo == VVC_RAW_CHAN_TCP) {
         asockNxt = ctx->bweAsock;
         // presumably currently receiving asock is the other one
         asockCur = ctx->beatAsock;
      } else {
         asockNxt = ctx->beatAsock;
         asockCur = ctx->bweAsock;
      }

      /*
       * Peer has switched protocol at this byteCount, so we do not expect to
       * read more content on this socket until the next switch, so safe to
       * cancel receive on this socket.
       * AsyncBWE cannot stay operational with CancelRecv like BEAT so we will
       * ASSERT bweAsock does not receive any packet at VVC level in recv loop.
       */
      if (FECAsyncSocket_IsFecSocket(asockCur)) {
         AsyncSocket_CancelRecvEx(asockCur, NULL, NULL, NULL, TRUE);
      }

      err = VvcRawChannelRecv(ctx, asockNxt);
      if (err != ASOCKERR_SUCCESS) {
         VvcLog("%s: Asock receive failed for channel %s: %s", __FUNCTION__, channel->name,
                AsyncSocket_Err2String(err));
         VvcRawAsockErrorCb(err, asockNxt, ctx);
      }
   }

   return;

exit:
   if (!sessionLocked) {
      UNLOCK_SESSION(session);
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcRawChannelSwitchProtocol --
 *
 *      BENIT triggered switch culminates here for raw channels at the
 *      initiating end: notify peer intent to switch at the cutoff byte count,
 *      and direct all future sends on the chosen protocol (switchTo) socket.
 *
 * Results:
 *    TRUE if msg queued.
 *
 * Side effects:
 *    Channel switches protocol for sends
 *
 *-----------------------------------------------------------------------------
 */

Bool
VvcRawChannelSwitchProtocol(VvcChannel *channel, VvcRawChanType switchTo)
{
   size_t switchOpLen;
   void *switchOp;
   VvcSession *session = channel->session;
   Bool msgQueued = FALSE;
   vvcRawChanCtx *ctx;

   // Both connections setup
   ASSERT(channel->rawCtx->beatAsock && channel->rawCtx->bweAsock);

   ctx = channel->rawCtx;

   /*
    * Unlikely scenario of channel already operating at switchTo;
    * log it and skip the switch.
    */
   if (UNLIKELY((switchTo == VVC_RAW_CHAN_TCP && !FECAsyncSocket_IsFecSocket(ctx->asock)) ||
                (switchTo == VVC_RAW_CHAN_BEAT && FECAsyncSocket_IsFecSocket(ctx->asock)))) {
      VvcLog("%s: Raw channel %s is already using %s, no switch needed", __FUNCTION__,
             channel->name, switchTo == VVC_RAW_CHAN_TCP ? "TCP" : "BEAT");
      goto out;
   }

   // Inform peer intent to switch, provide protocol and cutoff byte count
   ctx->localSwitchByteCount = channel->totalQueuedBytes;
   switchOp = VvcBuildRawSwitchOp(session, channel->channelId, switchTo, ctx->localSwitchByteCount,
                                  &switchOpLen);
   VvcQueueMessage(session->ctrlChannel, switchOp, switchOpLen, FALSE, VvcDefaultCtrlMsg, 0, 0);
   msgQueued = TRUE;

   VvcLog("%s: Initiating protocol switch for channel %s, switchTo %s, switchAt %" FMT64 "u, "
          "sentBytes %" FMT64 "u",
          __FUNCTION__, channel->name, switchTo == VVC_RAW_CHAN_TCP ? "TCP" : "BEAT",
          ctx->localSwitchByteCount, ctx->sentBytes);

   // Immediately commence using preferred (switchTo) socket for sends
   if (ctx->sentBytes == ctx->localSwitchByteCount) {
      ctx->localSwitchByteCount = 0;
      VvcDebug("%s: Swap sender and passive sockets at sent bytes %" FMT64 "u", __FUNCTION__,
               ctx->sentBytes);
      if (switchTo == VVC_RAW_CHAN_TCP) {
         channel->rawCtx->asock = channel->rawCtx->bweAsock;
      } else {
         channel->rawCtx->asock = channel->rawCtx->beatAsock;
      }
   }

out:
   return msgQueued;
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcRawChannelAssignBweAsock --
 *
 *      When BENIT driven TCP connection is ready, the initiator triggers the switch by:
 *      1. notifying peer intent to switch at totalQueuedBytes cutoff byte count
 *      2. start sending paylod on the switched-in protocol
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

void
VvcRawChannelAssignBweAsock(VvcChannel *channel, AsyncSocket *asock)
{
   struct vvcRawChanContext *ctx = channel->rawCtx;

   // BENIT's TCP connection implies active BEAT socket for this channel
   ASSERT(FECAsyncSocket_IsFecSocket(channel->rawCtx->asock));

   /*
    * Save BEAT socket which actively processes received packets until peer
    * also switches to TCP.
    */
   ASSERT(!ctx->beatAsock);
   ctx->beatAsock = ctx->asock;

   ASSERT(!ctx->bweAsock);
   ctx->bweAsock = asock;
   ctx->bwePollLock = AsyncSocketGetPollParams(asock)->lock;

   /*
    * Server side TCP connection is ready after sending COMMIT op, so if this end was the switch
    * initiator, this is the time to send SWITCH op. Client side switch op handling happens after
    * the commit op handling which guarantees TCP connection was fully setup.
    */
   if (ctx->isServer) {
      VvcLog("Secure %s TCP connection established for raw channel %d",
             VvcGetRawChannelName(ctx->type), channel->channelId);
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcRawChannelConvertToRegular --
 *
 *    Early on in the channel creation proocess, if we fail to create raw channels for any reason
 *    (there are many error scenarios when new connections are being created), we try to fallback to
 *    regular channels, while keeping the same channel. This function is used to undo some of the
 *    work that identifies the channel as 'raw' so that it is ready to be repurposed.
 *
 *    Used on the client side to tackle AcceptChannel failures and on the server when client tells
 *    the worker about its fallback to regular, so that worker can undo its initial request for raw
 *    and treat it like a regular channel.
 *
 * Results:
 *    TRUE on success
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

Bool
VvcRawChannelConvertToRegular(VvcChannel *channel)
{
   ASSERT(VVC_RAW_CHANNEL(channel));
   ASSERT(ISLOCKED_SESSION(channel->session));

   // make sure we are in the early stages of channel creation
   ASSERT(!channel->rawCtx || channel->rawCtx->openReq.openChan || channel->rawCtx->isServer);

   /*
    * Convert raw channel to regular prior to onOpen callback.
    * Involves these numbered steps:
    */

   // 1. cleanup raw state
   if (channel->rawCtx) {
      ASSERT(!channel->rawCtx->asock);
      MXUser_DestroyRecLock(channel->rawCtx->pollLock);
      MXUser_DestroyRecLock(channel->rawCtx->bwePollLock);
      free(channel->rawCtx);
      channel->rawCtx = NULL;

      channel->session->numRawChannels--;
   }

   // 2. re-tokenize the handle
   ASSERT(VVC_HANDLE_TYPE_RAW_CHANNEL(TOKEN_HANDLE(channel)));
   VvcRemoveTokenFromHandleMapping(TOKEN_HANDLE(channel), __FUNCTION__);
   if (!VvcAddTokenToHandleMapping(VvcHandleTypeChannel, (VvcCommonHandle)channel,
                                   channel->channelId, channel->name)) {
      VvcError("%s: Failed calling VvcAddTokenToHandleMapping() "
               "for channel %s",
               __FUNCTION__, channel->name);
      return FALSE;
   }

   // 3. reset the flag
   channel->flags &= ~VVC_CHANNEL_RAW;

   VvcLog("%s: Converted channel %s from raw to regular", __FUNCTION__, channel->name);

   return TRUE;
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcRawChannelGetNetworkStats --
 *
 *    Get network stats for a specific raw channel's connection.
 *
 * Results:
 *    VvcStatus
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

VvcStatus
VvcRawChannelGetNetworkStats(VvcChannel *channel,    // IN
                             VvcNetworkStats *stats, // OUT
                             uint64 nowMs)           // IN
{
   AsyncSocketNetworkStats asockStats;
   VvcSession *session = channel->session;

   ASSERT(ISLOCKED_SESSION(session));

   /*
    * Raw channel asock lock ordering requires releasing session lock.
    * Safe to unlock session as we are holding a ref on the channel.
    */
   UNLOCK_SESSION(session);
   int ret = AsyncSocket_GetNetworkStats(channel->rawCtx->asock, &asockStats);
   LOCK_SESSION(session);
   if (ret != ASOCKERR_SUCCESS) {
      return VVC_STATUS_ERROR;
   }

   memset(stats, 0, sizeof *stats);
   VvcNwStatsFromAsockStats(&asockStats, stats);
   VvcNwStatsBWConstraints(session, stats);

   channel->rawCtx->cachedBW = stats->bandwidthBytesPerSecond;
   channel->rawCtx->cachedBWTime = nowMs;

   return VVC_STATUS_SUCCESS;
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcGetActiveRawChannelInfo --
 *
 *    Get the most active raw channel (using max b/w), and some metrics needed for comparison
 *    with session's main connection metrics in SessionInfo handler.
 *
 * Results:
 *    VvcStatus
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

VvcStatus
VvcGetActiveRawChannelInfo(VvcSession *session,       // IN
                           VvcNetworkStats *pNwStats, // OUT
                           uint64 *rawSentBytesDelta, // OUT
                           uint32 *protocol,          // OUT
                           uint32 *peerProtocol)      // OUT
{
   VvcChannel *channel = NULL;
   VvcStatus status = VVC_STATUS_ERROR;

   ASSERT(ISLOCKED_SESSION(session));

   if (session->numRawChannels == 0) {
      return status;
   }

   if (session->cachedRawChannelId) {
      if ((channel = VvcFindChannelFromSession(session, NULL, session->cachedRawChannelId))) {
         status = VvcRawChannelGetNetworkStats(channel, pNwStats, Hostinfo_SystemTimerMS());
         if (!VVC_SUCCESS(status)) {
            VVC_RELEASE_CHANNEL(channel, VvcTagFindChannelFromSession);
            channel = NULL;
         }
      }
   } else {
      if (VvcGetRawChannelMaxBw(session, Hostinfo_SystemTimerMS(), pNwStats)) {
         if (session->cachedRawChannelId &&
             (channel = VvcFindChannelFromSession(session, NULL, session->cachedRawChannelId))) {
            status = VVC_STATUS_SUCCESS;
         }
      }
   }

   if (channel) {
      *rawSentBytesDelta = channel->rawCtx->sentBytes - channel->rawCtx->savedSentBytes;
      channel->rawCtx->savedSentBytes = channel->rawCtx->sentBytes;
      *protocol = FECAsyncSocket_IsFecSocket(channel->rawCtx->asock)
                     ? VvcSessionActiveTransportTypeUDP
                     : VvcSessionActiveTransportTypeTCP;
      *peerProtocol = channel->rawCtx->lastKnownPeerTransportType;
      VVC_RELEASE_CHANNEL(channel, VvcTagFindChannelFromSession);
   }

   return status;
}
