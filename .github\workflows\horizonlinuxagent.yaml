name: horizonlinuxagent
run-name: >
  ${{ github.workflow }}
  ${{ github.event_name == 'workflow_dispatch' && inputs.buildtype ||
      (github.event_name == 'pull_request' && 'obj' || 'beta') }}
  ${{ github.event_name == 'pull_request' &&
      format(' - {0} (#{1})', github.event.pull_request.title, github.event.pull_request.number)
      || '' }}
concurrency:
  # This section ensure that multiple PR pushes will cancel superseded builds.
  # Builds on main, release/* and feature/* branches will not be canceled to
  # assist in root causing build breakages.
  group: ${{ github.workflow }}-${{
      (github.ref == 'refs/heads/main' ||
       startsWith(github.ref, 'refs/heads/feature/') ||
       startsWith(github.ref, 'refs/heads/release/')) &&
      github.run_id || github.ref
    }}-${{ inputs.buildtype }}
  cancel-in-progress: true
on:
  pull_request:
  push:
    branches:
      - 'main'
      - 'release/**'
      - 'feature/**'
    paths-ignore:
      - .github/RunnerResetConfig.json
      - .github/workflows/runner_app_config.yaml
      - .github/workflows/rx-devop-nightly-*.yaml
  workflow_dispatch:
    inputs:
      buildtype:
        type: choice
        description: Build type
        required: True
        default: beta
        options:
          - beta
          - obj
          - release
      conan_develop:
        type: boolean
        description: I am testing conan packages and need to enable the conan-develop remote
        required: True
        default: false
      conan_sandbox:
        type: boolean
        description: I am testing conan compiler upgrade and need to enable the conan-sandbox remote
        required: false
        default: false
jobs:
  file-check:
    runs-on: [lnxbuild-gh, self-hosted]
    permissions:
      contents: read
      pull-requests: read
    outputs:
      enable-build: ${{ steps.filter.outputs.result }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          sparse-checkout: .github/workflow-filters.yaml
          sparse-checkout-cone-mode: false

      - name: Check if build should be run
        id: filter
        uses: euc-eng/filter-paths@v1
        with:
          filtersFile: .github/workflow-filters.yaml
          label: horizonlinuxagent

  build-horizonlinuxagent:
    needs: file-check
    if: ${{ needs.file-check.outputs.enable-build == 'true' }}
    runs-on: [lnxbuild-gh, self-hosted]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          lfs: true

      - name: Run SCons
        uses: ./.github/actions/scons
        with:
          buildtype: ${{ github.event_name == 'pull_request' && 'obj' || inputs.buildtype }}
          product: horizonlinuxagent
          conanDevelop: ${{ inputs.conan_develop }}
          conanSandbox: ${{ inputs.conan_sandbox }}
          artifactoryApiToken: ${{ secrets.ARTIFACTORY_GITHUB_DELIVERABLES_API_TOKEN }}
          artifactoryUrl: ${{ vars.ARTIFACTORY_GITHUB_DELIVERABLES_URL }}
          artifactoryReadToken: ${{ secrets.ORG_OMNISSA_ART_READONLY_TOKEN }}
          artifactoryReadUser: ${{ vars.ORG_OMNISSA_ART_READONLY_USER }}
          linSigningKeyId: ${{ vars.ORG_OMNISSA_LINUX_GPG_SIGNING_KEY_ID }}
          linSigningKeyPath: ${{ vars.ORG_OMNISSA_LINUX_GPG_SIGNING_DIR }}
          linSigningKeyPassword: ${{ secrets.ORG_OMNISSA_LINUX_GPG_SIGNING_KEY_PASSWORD }}
          extraParams: > 
            FOSSA_DEPS=1 COMPILE_DB=1 compiledb horizonlinuxagent
            ${{ github.event_name == 'pull_request' && 'ENABLE_CODE_COV=1' || '' }}

      - name: Run Fossa
        if: ${{ inputs.buildtype == 'release' || github.event_name == 'pull_request' }}
        uses: ./.github/actions/fossa
        with:
          product: 'horizonlinuxagent'
          fossa-api-key: ${{ secrets.ORG_OMNISSA_FOSSA_KEY }}
          omnissaArtifactoryToken: ${{ secrets.ORG_OMNISSA_ART_READONLY_TOKEN }}
          fossaExtraParams: --config ${{ github.workspace }}/.fossa.yml --fossa-deps-file ${{ github.workspace }}/fossa-deps.yml  ${{ github.workspace }}/bora/apps/

  UT:
    needs: build-horizonlinuxagent
    if: ${{ vars.ENABLE_UNIT_TEST_HOSTED_IN_BJ_LIN == 'true' }}
    secrets: inherit
    uses: ./.github/workflows/horizonlinuxagent_ut.yaml
    with:
      buildtype:  ${{ github.event_name == 'pull_request' && 'obj' || inputs.buildtype }}
      useGcov: ${{ github.event_name == 'pull_request' }}

  run-horizonlinuxagent-c-sonar:
    runs-on: [lnxbuild-gh, self-hosted]
    if: ${{ github.event_name == 'pull_request' && vars.ENABLE_SONAR_SCAN_BJ_FOR_LIN == 'true'}}
    needs:
      - build-horizonlinuxagent
      - UT
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Sonar scan
        uses: ./.github/actions/testframework/sonar-project-action/linuxvdi-c
        with:
          productWorkflowJob: build-horizonlinuxagent
          artifactoryBaseUrl: ${{ vars.ARTIFACTORY_GITHUB_DELIVERABLES_URL }}
          artifactoryApiToken: ${{ secrets.ARTIFACTORY_GITHUB_DELIVERABLES_API_TOKEN }}
          artifactoryReadToken: ${{ secrets.ORG_OMNISSA_ART_READONLY_TOKEN }}
          artifactoryReadUser: ${{ vars.ORG_OMNISSA_ART_READONLY_USER }}
          sonarHostUrl: ${{ vars.ORG_EUC_SONAR_HOST }}
          sonarToken: ${{ secrets.ORG_EUC_SONAR_TOKEN }}

  run-horizonlinuxagent-java-sonar:
    runs-on: [lnxbuild-gh, self-hosted]
    if: ${{ github.event_name == 'pull_request' && vars.ENABLE_SONAR_SCAN_BJ_FOR_LIN == 'true'}}
    needs:
      - build-horizonlinuxagent
      - UT
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Sonar scan
        uses: ./.github/actions/testframework/sonar-project-action/linuxvdi-java
        with:
          productWorkflowJob: build-horizonlinuxagent
          artifactoryBaseUrl: ${{ vars.ARTIFACTORY_GITHUB_DELIVERABLES_URL }}
          artifactoryApiToken: ${{ secrets.ARTIFACTORY_GITHUB_DELIVERABLES_API_TOKEN }}
          artifactoryReadToken: ${{ secrets.ORG_OMNISSA_ART_READONLY_TOKEN }}
          artifactoryReadUser: ${{ vars.ORG_OMNISSA_ART_READONLY_USER }}
          sonarHostUrl: ${{ vars.ORG_EUC_SONAR_HOST }}
          sonarToken: ${{ secrets.ORG_EUC_SONAR_TOKEN }}

  horizonlinuxagent-overall-status:
    needs:
      - build-horizonlinuxagent
      - UT
      - run-horizonlinuxagent-java-sonar
      - run-horizonlinuxagent-c-sonar
    if: ${{ !cancelled() }}
    timeout-minutes: 10
    runs-on: [lnxbuild-gh, self-hosted]
    permissions:
      actions: write
      contents: read
      pull-requests: read
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Check overall workflow status
        uses: ./.github/actions/check-status
        with:
          workflowId: 'horizonlinuxagent.yaml'
          jobs: ${{ toJson(needs) }}
          buildtype: ${{ inputs.buildtype }}
          slackWebhookUrl: ${{ secrets.CART_SLACK_WEBHOOK_URL }}
          dashboardUrl: ${{ vars.CART_BUILD_DASHBOARD }}
          slackBranches: ${{ vars.DAILY_BUILD_BRANCHES }}
