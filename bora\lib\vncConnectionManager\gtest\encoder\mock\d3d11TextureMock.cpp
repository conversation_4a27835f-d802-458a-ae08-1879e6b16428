/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

#include "d3d11TextureMock.h"
#include "d3d11DeviceMock.h"
#include <iostream>

MockD3D11Texture2D::MockD3D11Texture2D(MockD3D11Device *device, const D3D11_TEXTURE2D_DESC *pDesc,
                                       const D3D11_SUBRESOURCE_DATA *pInitialData) :
   m_device(device)
{
   if (m_device) {
      m_device->AddRef();
   }
   if (pDesc) {
      m_desc = *pDesc;
      if (pInitialData && pDesc->MipLevels > 0) {
         m_initialData.assign(pInitialData, pInitialData + pDesc->MipLevels);
      }
   } else {
      memset(&m_desc, 0, sizeof(m_desc));
   }
}

MockD3D11Texture2D::~MockD3D11Texture2D()
{
   if (m_device) {
      m_device->Release();
   }
}

ULONG
MockD3D11Texture2D::AddRef()
{
   return ++m_refCount;
}

ULONG
MockD3D11Texture2D::Release()
{
   if (--m_refCount == 0) {
      delete this;
      return 0;
   }
   return m_refCount;
}

HRESULT
MockD3D11Texture2D::QueryInterface(REFIID riid, void **ppvObject)
{
   if (riid == __uuidof(ID3D11Texture2D) || riid == __uuidof(ID3D11Resource) ||
       riid == __uuidof(ID3D11DeviceChild) || riid == __uuidof(IUnknown)) {
      *ppvObject = static_cast<ID3D11Texture2D *>(this);
      AddRef();
      return S_OK;
   }
   *ppvObject = nullptr;
   return E_NOINTERFACE;
}

void
MockD3D11Texture2D::GetDevice(ID3D11Device **ppDevice)
{
   if (ppDevice == nullptr) {
      throw std::invalid_argument("ppDevice cannot be null");
   }

   if (m_device) {
      // If m_device is valid, return it and add a reference
      *ppDevice = m_device;
      m_device->AddRef();
   } else {
      *ppDevice = nullptr;
   }
}

void
MockD3D11Texture2D::GetType(D3D11_RESOURCE_DIMENSION *pResourceDimension)
{
   *pResourceDimension = D3D11_RESOURCE_DIMENSION_TEXTURE2D;
}

void
MockD3D11Texture2D::SetEvictionPriority(UINT EvictionPriority)
{
   // No-op
}

UINT
MockD3D11Texture2D::GetEvictionPriority()
{
   return 0;
}

void
MockD3D11Texture2D::GetDesc(D3D11_TEXTURE2D_DESC *pDesc)
{
   if (pDesc) {
      *pDesc = m_desc;
   }
}

void
MockD3D11Texture2D::SetDesc(const D3D11_TEXTURE2D_DESC &desc)
{
   m_desc = desc;
}