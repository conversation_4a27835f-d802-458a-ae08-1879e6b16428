/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * servicePrep.h
 *
 *      Class that implements /service mode for Pool Creation (Internal Template)
 *
 */

#pragma once
#include "baseprep.h"

class ServicePrep : public BasePrep {
public:
   ServicePrep() = default;
   ~ServicePrep() = default;

private:
};
