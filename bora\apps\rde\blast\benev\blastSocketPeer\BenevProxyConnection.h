/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

#ifndef _BENEV_PROXY_CONNECTION_H
#define _BENEV_PROXY_CONNECTION_H


#include <map>
#include <mutex>
#include <string>
#include <vector>

#include "horizon.h"
#include "vvclib.h"

#include "BenevSocket.h"


/*
 *----------------------------------------------------------------------
 *
 * BenevProxyConnection --
 *
 *      Represents a single proxy connection with a downstream and an upstream
 *      socket. Data received from one end is sent out of the other end.
 *
 *----------------------------------------------------------------------
 */

class BenevProxyConnection {
public:
   typedef enum { TCPSocket, VVCSocket } SocketType;

   struct TCPSocketParam {
      TCPSocketParam(const char *h = "", unsigned short p = 0) : host(h), port(p) {}

      std::string host;
      unsigned short port;
   };

   struct VVCSocketParam {
      VVCSocketParam(const char *chnm = "", const char *ftrnm = "",
                     int32 sid = VVC_INVALID_SESSIONID, uint32 chp = VVC_TRAFFIC_TYPE_REAL_TIME_2,
                     uint32 chf = 0) :
         channelName(chnm),
         featureName(ftrnm),
         sessionId(sid),
         channelPriority(chp),
         channelFlags(chf)
      {}

      std::string channelName;
      std::string featureName;
      int32 sessionId;
      uint32 channelPriority;
      uint32 channelFlags;
   };

   struct SocketParam {
      SocketParam() : tcp(), vvc() {}

      SocketParam(const TCPSocketParam &t) : tcp(t), vvc() {}

      SocketParam(const VVCSocketParam &v) : tcp(), vvc(v) {}

      TCPSocketParam tcp;
      VVCSocketParam vvc;
   };

   BenevProxyConnection(uint32 connId, BenevSocket *downSock, SocketType upSockType,
                        const SocketParam &upSockParams);

   ~BenevProxyConnection();

   unsigned getConnId() { return mConnId; }

   void start();
   std::map<std::string, std::string> stop();

   void checkBytesPerSecond();

private:
   static const char *sClassName;

   enum ConnectionState { Connecting, Connected, Closing, Closed };

   /* VVC Channel event callbacks */
   static void upstreamOnConnectCb(BenevSocket *socket, void *cbData);
   static void socketOnRecvCb(void *buf, int bufLen, BenevSocket *socket, void *cbData);
   static void socketOnSentCb(void *buf, int bufLen, BenevSocket *socket, void *cbData);
   static void socketOnErrorCb(int err, BenevSocket *socket, void *cbData);

   void upstreamConnected();
   void dataReceived(BenevSocket *srcSock, void *buf, int bufLen);
   void dataSent(BenevSocket *dstSock, void *buf, int bufLen);

   bool connectToUpstream();
   void closeSockets();

   // the unique connection ID
   uint32 mConnId;
   MXUserExclLock *mLock;
   ConnectionState mState;
   bool mDataInTransit;

   SocketType mUpSockType;
   SocketParam mUpSockParams;

   BenevSocket *mDownSock;
   int mDownSockBufLen;
   uint8 mDownSockBuf[8192];
   uint64 mDownSockReceivedBytes;
   uint64 mDownSockSentBytes;
   uint64 mDownBytesSentThisSecond;
   std::vector<uint64> mDownSockBytesSentPerSecond;

   BenevSocket *mUpSock;
   int mUpSockBufLen;
   uint8 mUpSockBuf[8192];
   uint64 mUpSockReceivedBytes;
   uint64 mUpSockSentBytes;
   uint64 mUpBytesSentThisSecond;
   std::vector<uint64> mUpSockBytesSentPerSecond;

   uint64 mUpSendStartTime;
   uint64 mUpLastSentTime;
   double mUpAvgBw;

   bool mRawChannelStatus;

   // Used to protect mUpBytesSentThisSecond & mDownBytesSentThisSecond
   std::mutex mLockGuard;
};


#endif
