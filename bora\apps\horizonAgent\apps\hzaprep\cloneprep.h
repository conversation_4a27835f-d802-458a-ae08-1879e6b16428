/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * cloneprep.h
 *
 *      This class implements functions that are specific to preparing the
 *      clone.
 *
 */

#pragma once
#include "baseprep.h"

#define WINDEPLOY_PATH L"oobe\\windeploy.exe"
#define FULL_WINDEPLOY_PATH L"%SYSTEMROOT%\\System32\\oobe\\windeploy.exe"
#define DHCP_SERVICE_NAME L"Dhcp"
#define UNATTEND_FILE_PATH L"%SYSTEMROOT%\\Panther\\unattend.xml"
#define TOTAL_UNATTEND_VALUES 5

enum unAttendFieldProcessed {
   USERNAME,
   JOINDOMAIN,
   DOMAINNAME,
   PASSWORD,
   COMPUTERNAME,
   UNATTEND_FIELD_COUNT
};

class ClonePrep : public BasePrep {
public:
   bool init();
   bool retrieveDomainJoinCreds();
   bool populateUnAttendFileWithCreds();
   bool populateSysprepData();
   static bool expandEnvStr(const wchar_t *strToExpand, wstr &expandedStr);
   static int runWinDeploy();
   static bool startDhcpService();
   static bool readLinesFromFile(wstr filename, std::vector<wstr> &lines);

private:
   wstr mDomainName;
   wstr mUserName;
   wstr mPassword;
   wstr mComputerName;
   wstr mJoinDomain;
};
