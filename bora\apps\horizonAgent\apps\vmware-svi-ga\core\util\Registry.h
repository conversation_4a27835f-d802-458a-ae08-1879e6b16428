/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

#pragma once

#include <common/windows/registry/registry.h>
#include <common/windows/singleton/Singleton.h>

namespace svmga {
namespace core {
namespace util {

#define KEY_ACCESS_MASK KEY_QUERY_VALUE | KEY_READ | KEY_WRITE | KEY_WOW64_64KEY
#define KEY_ACCESS_MASK_SYSPREP KEY_ACCESS_MASK | KEY_NOTIFY
#define HKLM HKEY_LOCAL_MACHINE

namespace reg = svmga::common::windows::registry;

enum class RegType {
   Service,
   Ga,
   Nga,
   External,
   SessionMgr,
   Netlogon,
   AgentIntegration,
   Sysprep,
   AV,
   CachedMachines,
   ProfileList,
   Support,
   GuestInfo,    // To save vmx backdoor value (vmConfig.extraConfig)
   Setup,        // Horizon setup
   SystemSetup,  // System setup
   AgentConfig,  // Horizon Agent config (out side of ga/nga)
   NodeManager,  // Node Manager
   HorizonAgent, // Horizon Agent put all reconfig/refresh related values here
   Unknown
};

class Registry : public Singleton<Registry> {
public:
   Registry();

   //
   // String
   //
   virtual HRESULT GetValue(const std::wstring &valName, std::wstring &strValue,
                            RegType Type = RegType::Ga);

   //
   // DWORD
   //
   virtual HRESULT GetValue(const std::wstring &valName, const DWORD &defVal, DWORD &dwValue,
                            RegType Type = RegType::Ga);

   //
   // Multistring
   //
   virtual HRESULT GetValue(const std::wstring &valName, std::vector<std::wstring> value,
                            RegType Type = RegType::Ga);

   //
   // String
   //
   virtual HRESULT SetValue(const std::wstring &valName, std::wstring strValue,
                            RegType Type = RegType::Ga);

   //
   // DWORD
   //
   virtual HRESULT SetValue(const std::wstring &valName, DWORD dwValue, RegType Type = RegType::Ga);

   //
   // MultiString
   //
   virtual HRESULT SetValue(const std::wstring &valName, BYTE *value, const size_t len,
                            RegType Type = RegType::Ga);

   virtual HRESULT CreateKey(HKEY hkParent, std::wstring strKeyPath);
   virtual HRESULT FlushKey(RegType Type = RegType::Ga);

   virtual HRESULT WaitForChange(DWORD dwDuration, bool &bResult, HANDLE &hWaitEvent,
                                 HANDLE hShutdownEvent, RegType Type = RegType::Ga);

   virtual HRESULT WaitForDword(const std::wstring &ValueName, DWORD expectedValue,
                                DWORD dwDuration, bool &bResult, RegType Type = RegType::Ga);

   virtual HRESULT WaitForString(const std::wstring &ValueName, std::wstring strExpectedValue,
                                 DWORD dwDuration, bool &bResult, HANDLE hShutdownEvent,
                                 RegType Type = RegType::Ga);

   virtual HRESULT WaitForString(const std::wstring &valueName, const std::wstring &expectedValue,
                                 int totalWaitTimeInMs, RegType Type = RegType::Ga);

   virtual HRESULT WaitForValueExists(const std::wstring &ValueName, DWORD dwDuration,
                                      RegType Type = RegType::Ga);

private:
   reg::ExistingRegKey *GetKey(HKEY hkParent, std::wstring strPath, DWORD dwMask = KEY_ACCESS_MASK);
   reg::ExistingRegKey *GetKeyObj(RegType Type);

private:
   reg::ExistingRegKey *_svcKey;
   reg::ExistingRegKey *_gaKey;
   reg::ExistingRegKey *_gaSupportKey;
   reg::ExistingRegKey *_ngaKey;
   reg::ExistingRegKey *_extKey;
   reg::ExistingRegKey *_smKey;
   reg::ExistingRegKey *_nlKey;
   reg::ExistingRegKey *_aiKey;
   reg::ExistingRegKey *_sysprepKey;
   reg::ExistingRegKey *_avKey;
   reg::ExistingRegKey *_lsaCMNKey;
   reg::ExistingRegKey *_profileListKey;
   reg::ExistingRegKey *_guestInfoKey;
   reg::ExistingRegKey *_setupKey;
   reg::ExistingRegKey *_systemSetupKey;
   reg::ExistingRegKey *_agentConfigKey;
   reg::ExistingRegKey *_nodeManagerKey;
   reg::ExistingRegKey *_horizonAgentKey;
};

} // namespace util
} // namespace core
} // namespace svmga