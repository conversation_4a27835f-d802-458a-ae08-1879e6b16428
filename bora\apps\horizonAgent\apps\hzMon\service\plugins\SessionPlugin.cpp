/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

#include "SessionPlugin.h"
#include "PluginFactory.h"

#include <AgentTags.h>
#include <metrics/SessionMetricInfo.h>
#include <utils/AgentmonUtil.h>
#include <utils/HzSessionUtils.h>
#include <utils/MachineUtils.h>
#include <utils/OsVerUtil.h>

#include <smWinAgentFormatters.h>

using horizon::sm::SmEventCategory;
using horizon::sm::SmEventId;
using horizon::sm::SmSessionType;
using horizon::sm::smSessionTypeToString;
using HzSessionUtils::SessionHelper;
using std::move;
using namespace std::chrono;


/*
 *----------------------------------------------------------------------
 *
 * SessionPlugin::SessionPlugin --
 *
 *   Constructor
 *
 *   Plugins which currently rely on session plugin events include:
 *   Registry, Perf, ETW
 *
 *   Policy plugin also relies on session events but is disabled.
 *
 * Results:
 *   None.
 *
 * Side Effects:
 *   None.
 *
 *----------------------------------------------------------------------
 */

SessionPlugin::SessionPlugin() : Plugin()
{
   mSessMandatoryMetricInfoVec = GetSessMandatoryMetricInfo();
   mSessTimeMetricInfoVec = GetSessTimeMetricInfo();
   mSessGuestMetricInfoVec = GetSessGuestMetricInfo();
   mLogonSegmentMetricInfoVec = GetLogonSegmentMetricInfo();
}


/*
 *----------------------------------------------------------------------
 *
 * SessionPlugin::OnProfilesChanged --
 *
 *   If any of the supported profile is enabled, iterate over
 *   existing active sessions. Add them to map if they're not
 *   present
 *
 * Results:
 *   None.
 *
 * Side Effects:
 *   None.
 *
 *----------------------------------------------------------------------
 */

void
SessionPlugin::OnProfilesChanged()
{
   if (Plugin::IsProfileEnabled(mSupportedProfiles)) {
      LOG_TRACE("%s plugin is enabled", GetName());
   } else {
      LOG_TRACE("%s plugin is disabled", GetName());
      mWinSessionMap.clear();
   }
}


/*
 *----------------------------------------------------------------------
 *
 * SessionPlugin::OnWinSessionTerminated --
 *
 *   Collect session timing metrics for one last time and
 *   then send session_delete event
 *
 * Results:
 *   None.
 *
 * Side Effects:
 *   None.
 *
 *----------------------------------------------------------------------
 */

void
SessionPlugin::OnWinSessionTerminated(DWORD sessionId) // IN
{
   WinSession &winSess = mWinSessionMap[sessionId];
   if (!winSess.GetNumHzConnections()) {
      LOG_DEBUG("No horizon sessions associated with windows session with "
                "sessionId=%d",
                sessionId);
      return;
   }

   Json::Value sessionDeleteEventData(Json::objectValue);
   CollectMandatorySessionData(winSess, sessionDeleteEventData);
   HzMonData sessionDeleteData = HzMonData::CreateHzMonData(MessageType::EVENT)
                                    .SetKey("Sess_Delete")
                                    .SetPluginData(move(sessionDeleteEventData))
                                    .SetProfile(MetricProfile::MANDATORY)
                                    .SetWinSessionId(sessionId);

   Plugin::SendPushData(sessionDeleteData);
}


/*
 *----------------------------------------------------------------------
 *
 * SessionPlugin::OnSessionEvent --
 *
 *   Forward session event to correct session
 *
 * Results:
 *   None
 *
 * Side Effects:
 *   None.
 *
 *----------------------------------------------------------------------
 */

void
SessionPlugin::OnSessionEvent(const SessionEventInfo &sessionEventInfo) // IN
{
   if (!Plugin::IsProfileEnabled(mSupportedProfiles)) {
      LOG_TRACE("Ignore session event as profiles are disabled");
      return;
   }

   DWORD sessionId;

   switch (sessionEventInfo.eventCategory) {

   case SmEventCategory::WinSession: {
      auto eventDataPtr = sessionEventInfo.GetEventData<WinSessionEventData>();
      sessionId = eventDataPtr->sessionId;
      auto eventId = sessionEventInfo.eventId;

      mWinSessionMap[sessionId].HandleSessionEvent(sessionEventInfo.eventId,
                                                   sessionEventInfo.eventRaisedAt, *eventDataPtr);

      if (eventId == SmEventId::WinSessionTerminated && mWinSessionMap.count(sessionId) &&
          !mWinSessionMap[sessionId].IsPreExistingSession()) {
         OnWinSessionTerminated(sessionId);
         mWinSessionMap.erase(sessionId);
      }
   } break;

   case SmEventCategory::Connection: {
      auto eventDataPtr = sessionEventInfo.GetEventData<ConnectionEventData>();
      sessionId = eventDataPtr->sessionId;
      mWinSessionMap[sessionId].HandleSessionEvent(sessionEventInfo.eventId,
                                                   sessionEventInfo.eventRaisedAt, *eventDataPtr);
   } break;
   }
}


/*
 *----------------------------------------------------------------------
 *
 * SessionPlugin::CollectMandatorySessionData --
 *
 *   Adds mandatory session data model attributes to Json.
 *
 * Results:
 *   None.
 *
 * Side Effects:
 *   None.
 *
 *----------------------------------------------------------------------
 */

void
SessionPlugin::CollectMandatorySessionData(const WinSession &winSess,      // IN
                                           Json::Value &sessionPluginData) // OUT
{
   for (const auto &metricInfo : mSessMandatoryMetricInfoVec) {
      switch (metricInfo.mId) {
      case MetricIdentifier::SESS_TEMPLATE_TYPE:
         sessionPluginData[metricInfo.mName] =
            AgentMonUtil::IsVMWProtocolProviderSupported() ? "RDSH" : "VDI";
         break;
      case MetricIdentifier::SESS_STATUS:
         sessionPluginData[metricInfo.mName] = winSess.GetSessionState();
         break;
      case MetricIdentifier::SESS_TYPE:
         sessionPluginData[metricInfo.mName] =
            StringUtils::WStringToString(smSessionTypeToString(winSess.GetSessionType()));
         break;
      }
   }
}


/*
 *----------------------------------------------------------------------
 *
 * SessionPlugin::CollectSessionMetricData --
 *
 *   Adds session specific metric data to Json
 *
 * Results:
 *   None.
 *
 * Side Effects:
 *   None.
 *
 *----------------------------------------------------------------------
 */

void
SessionPlugin::CollectSessionMetricData(DWORD sessionId,                // IN
                                        const WinSession &winSess,      // IN
                                        Json::Value &sessionPluginData) // OUT
{
   for (const auto &metricInfo : mSessMandatoryMetricInfoVec) {
      switch (metricInfo.mId) {
      case MetricIdentifier::SESS_CONNECTION_ID:
         sessionPluginData[metricInfo.mName] = winSess.GetLastHzSessionGuid();
         break;
      case MetricIdentifier::SESS_USERNAME:
         sessionPluginData[metricInfo.mName] =
            SessionHelper::GetInstance().GetSessionData(sessionId, true).userName;
         break;
      case MetricIdentifier::SESS_UPN: {
         auto upn = winSess.GetUPN();
         if (upn.length()) {
            AgentMonUtil::ToLowerCase(upn);
            sessionPluginData[metricInfo.mName] = upn;
         } else {
            LOG_TRACE("No upn available for sessionId=%d", sessionId);
         }
      } break;
      case MetricIdentifier::SESS_USER_DN:
         sessionPluginData[metricInfo.mName] = winSess.GetUserId();
         break;
      case MetricIdentifier::SESS_LOGGED_IN_USER_DOMAIN:
         sessionPluginData[metricInfo.mName] = winSess.GetUserDomain();
         break;
      }
   }
}


/*
 *----------------------------------------------------------------------
 *
 * SessionPlugin::CollectSessionTimeMetrics --
 *
 *   Adds session timing metrics to Json, and updates winSession times.
 *
 * Results:
 *   None.
 *
 * Side Effects:
 *   None.
 *
 *----------------------------------------------------------------------
 */

void
SessionPlugin::CollectSessionTimeMetrics(WinSession &winSession,      // OUT
                                         Json::Value &sessPluginData) // OUT
{
   /*
    * We allow UpdateTime call since that would help us get the
    * right state, which is a mandatory attribute
    */
   winSession.UpdateTime();

   if (winSession.IsPreExistingSession()) {
      return;
   }
   winSession.CollectMetrics();

   /*
    * All winsession logon metrics are in units of milliseconds and
    * need to be converted to seconds.
    */
   bool haveLogonMetrics = winSession.HaveLogonMetrics();
   bool isAppSession = winSession.GetSessionType() == SmSessionType::Application;

   for (const auto &metricInfo : mSessTimeMetricInfoVec) {
      switch (metricInfo.mId) {
      case MetricIdentifier::SESS_LOGON_DURATION:
         if (haveLogonMetrics) {
            sessPluginData[metricInfo.mName] = winSession.GetLogonDuration() / 1000;
         }
         break;
      case MetricIdentifier::SESS_VDMS_LOGON:
         if (haveLogonMetrics && !isAppSession) {
            sessPluginData[metricInfo.mName] = winSession.GetLogonToShellStartTime() / 1000;
         }
         break;
      case MetricIdentifier::SESS_INTERACTIVE_DURATION:
      case MetricIdentifier::SESS_SHELL_LOAD_TIME:
         if (haveLogonMetrics && !isAppSession) {
            sessPluginData[metricInfo.mName] = winSession.GetShellLoadTime() / 1000;
         }
         break;
      case MetricIdentifier::SESS_LOGON_TIMESTAMP: {
         std::string timestamp = winSession.GetLogonTimestamp();
         if (timestamp.size()) {
            sessPluginData[metricInfo.mName] = ReaderMessage::GetTSPrefix() + timestamp;
         }
      } break;
      case MetricIdentifier::SESS_PROFILE_LOAD_TIME:
         if (haveLogonMetrics) {
            sessPluginData[metricInfo.mName] = winSession.GetProfileLoadTime() / 1000;
         }
         break;
      case MetricIdentifier::SESS_MISC_LOGON_TIME:
         if (haveLogonMetrics && !isAppSession) {
            sessPluginData[metricInfo.mName] = winSession.GetMiscLogonTime() / 1000;
         }
         break;
      case MetricIdentifier::SESS_ACTIVE:
         sessPluginData[metricInfo.mName] = winSession.GetActiveTime();
         break;
      case MetricIdentifier::SESS_IDLE:
         sessPluginData[metricInfo.mName] = winSession.GetIdleTime();
         break;
      case MetricIdentifier::SESS_DISCONN:
         sessPluginData[metricInfo.mName] = winSession.GetDisconnectedTime();
         break;
      case MetricIdentifier::SESS_TOTAL_DISCONN:
         sessPluginData[metricInfo.mName] = winSession.GetTotalDisconnectedTime();
         break;
      case MetricIdentifier::SESS_TOTAL_DURATION:
         sessPluginData[metricInfo.mName] = winSession.GetTotalDuration();
         break;
      case MetricIdentifier::SESS_CURR_DISCONN_DUR:
         if (auto val = winSession.GetCurrentDisconnectTime().value_or(0)) {
            sessPluginData[metricInfo.mName] = static_cast<double>(val);
         }
         break;
      }
   }
}


/*
 *----------------------------------------------------------------------
 *
 * SessionPlugin::CollectSessionGuestMetrics --
 *
 *   Collects session metrics for guest VM
 *
 * Results:
 *   None.
 *
 * Side Effects:
 *   None.
 *
 *----------------------------------------------------------------------
 */

void
SessionPlugin::CollectSessionGuestMetrics(Json::Value &sessPluginData) // OUT
{
   for (const auto &metricInfo : mSessGuestMetricInfoVec) {
      switch (metricInfo.mId) {
      case MetricIdentifier::OS_VERSION: {
         sessPluginData[metricInfo.mName] = OsVerUtil::GetOSVersion().c_str();
      } break;
      case MetricIdentifier::COMPUTER_NAME: {
         std::wstring hostnameW = MachineUtils::GetHostName();
         StringUtils::XString hostNameX(hostnameW);
         sessPluginData[metricInfo.mName] = hostNameX.c_str();
      } break;
      }
   }
}


/*
 *----------------------------------------------------------------------
 *
 * SessionPlugin::PreCollectMetricsData --
 *
 *    Iterates over each WinSession object and updates the logged on
 *    user domain name. Since WinSession::UpdateSession() only needs
 *    to be called once, this functionality is performed in
 *    PreCollectMetricsData.
 *
 * Results:
 *    None.
 *
 * Side Effects:
 *    None.
 *
 *----------------------------------------------------------------------
 */

void
SessionPlugin::PreCollectMetricsData()
{
   for (auto &[sessionId, winSess] : mWinSessionMap) {
      winSess.UpdateSession();
   }
}


/*
 *----------------------------------------------------------------------
 *
 * SessionPlugin::OnSessionLogon --
 *
 *   Sends logon segment metrics upon session logon and upon session
 *   reconnections.
 *
 * Results:
 *   None.
 *
 * Side Effects:
 *   None.
 *
 *----------------------------------------------------------------------
 */

void
SessionPlugin::OnSessionLogon(DWORD sessionId) // IN
{
   WinSession &winSession = mWinSessionMap[sessionId];

   if (!winSession.GetNumHzConnections() || winSession.IsPreExistingSession()) {
      LOG_WARN("Dropping logon metrics for sessionId=%d", sessionId);
      return;
   }

   for (const auto &metricInfo : mLogonSegmentMetricInfoVec) {

      Json::Value logonData(Json::objectValue);
      double start = 0.0;
      double end = 0.0;
      double duration = 0.0;
      std::string category;

      switch (metricInfo.mId) {
      case MetricIdentifier::LOGON_GPO_LOAD:
         start = winSession.GetUserGpoStartTime();
         end = winSession.GetUserGpoEndTime();
         duration = winSession.GetUserGpoLoadTime();
         category = "clientLogon";
         break;
      case MetricIdentifier::LOGON_SCRIPT:
         start = winSession.GetLogonScriptStartTime();
         end = winSession.GetLogonScriptEndTime();
         duration = winSession.GetLogonScriptLoadTime();
         category = "agentPrepare";
         break;
      case MetricIdentifier::LOGON_PROFILE_LOAD:
         start = winSession.GetProfileStartTime();
         end = winSession.GetProfileEndTime();
         duration = winSession.GetProfileLoadTime();
         category = "clientLogon";
         break;
      case MetricIdentifier::LOGON_INTERACTIVE:
         if (winSession.GetSessionType() == SmSessionType::Application) {
            continue;
         }
         start = winSession.GetShellStartTime();
         end = winSession.GetShellEndTime();
         duration = winSession.GetShellLoadTime();
         category = "clientLogon";
         break;
      }

      logonData[PROTO_KEY_LOGON_START] =
         ReaderMessage::GetTSPrefix() + std::to_string((uint64_t)start);
      logonData[PROTO_KEY_LOGON_END] = ReaderMessage::GetTSPrefix() + std::to_string((uint64_t)end);
      logonData[PROTO_KEY_LOGON_DURATION] = duration;
      logonData[PROTO_KEY_LOGON_CATEGORY] = category;
      logonData[PROTO_KEY_LOGON_TYPE] = metricInfo.mName;
      if (AgentTags::GetInstance()->IsTitanAgent()) {
         logonData[PROTO_KEY_DSPEC_ID] = winSession.GetDSpecId();
      } else {
         logonData[PROTO_KEY_DSPEC_ID] = winSession.GetLastHzSessionGuid();
      }
      logonData[PROTO_KEY_SESSION_GUID] = winSession.GetSessionGuid();
      logonData[PROTO_KEY_USER_ID] = winSession.GetUserId();
      logonData[PROTO_KEY_WIN_SESSION_ID] = std::to_string(winSession.GetSessionId());
      milliseconds ms = duration_cast<milliseconds>(system_clock::now().time_since_epoch());
      logonData[JSON_KEY_TIMESTAMP] = ReaderMessage::GetTSPrefix() + std::to_string(ms.count());

      HzMonData metricData =
         HzMonData::CreateHzMonData(MessageType::LOGON)
            .SetKey("Logon_Metrics_" + std::to_string(winSession.GetSessionId()))
            .SetPluginData(move(logonData))
            .SetProfile(MetricProfile::MANDATORY)
            .SetWinSessionId(winSession.GetSessionId());

      Plugin::SendPushData(metricData);
   }
}


/*
 *----------------------------------------------------------------------
 *
 * SessionPlugin::CollectMetricsData --
 *
 *   Collects horizon session data for each winsession that has a
 *   connected horizon session.
 *
 * Results:
 *   None.
 *
 * Side Effects:
 *   None.
 *
 *----------------------------------------------------------------------
 */

void
SessionPlugin::CollectMetricsData(MetricProfile profileToCollect, // IN
                                  ReaderMessage &data)            // OUT
{
   for (auto &[sessionId, winSession] : mWinSessionMap) {

      if (!winSession.GetNumHzConnections() || winSession.IsPreExistingSession()) {
         LOG_TRACE("Not collecting metrics for sessionId=%d as "
                   "it either has no horizon sessions or it's a"
                   "reconnected session",
                   sessionId);
         continue;
      }
      LOG_TRACE("Collecting session metrics for sessionId=%d", sessionId);
      Json::Value sessionPluginData(Json::objectValue);

      switch (profileToCollect) {
      case MetricProfile::MANDATORY:
         CollectSessionTimeMetrics(winSession, sessionPluginData);
         CollectSessionGuestMetrics(sessionPluginData);
         CollectSessionMetricData(sessionId, winSession, sessionPluginData);
         CollectMandatorySessionData(winSession, sessionPluginData);
         break;
      default:
         LOG_WARN("Unsupported profile received : %s", ProfileEnumToStr(profileToCollect));
         return;
      }

      HzMonData metricData = HzMonData::CreateHzMonData(MessageType::METRIC)
                                .SetKey("Sess_Metrics_" + std::to_string(sessionId))
                                .SetPluginData(move(sessionPluginData))
                                .SetProfile(profileToCollect)
                                .SetWinSessionId(sessionId);

      Plugin::AddMetricsToData(metricData, data);
   }
}


/*
 *----------------------------------------------------------------------
 *
 * SessionPlugin::OnHzmonData --
 *
 *   Forward session event to correct session and handle logon events
 *   if required.
 *
 * Results:
 *   None
 *
 * Side Effects:
 *   None.
 *
 *----------------------------------------------------------------------
 */

void
SessionPlugin::OnHzmonData(const HzMonData &data) // IN
{
   if (data.GetKey() == "lastInpTime" || data.GetKey() == "logonTime" || data.GetKey() == "upn") {
      const auto &val = data.GetValue();
      int sessionId = data.GetWinSessionId();
      if (sessionId != -1 && mWinSessionMap.count(sessionId)) {
         mWinSessionMap[sessionId].HandleHzmonData(data);

         if (data.GetKey() == "logonTime") {
            OnSessionLogon(sessionId);
         }
      }
   }
}


/*
 *----------------------------------------------------------------------
 *
 * SessionPlugin::Create --
 *
 *   Function registered with the factory to create this plugin
 *
 * Results:
 *   std::unique_ptr<Plugin>
 *
 * Side Effects:
 *   None.
 *
 *----------------------------------------------------------------------
 */

std::unique_ptr<Plugin>
SessionPlugin::Create()
{
   return std::make_unique<SessionPlugin>();
}

// Register this plugin with factory
bool SessionPlugin::sRegistered =
   PluginFactory::Register(SessionPlugin::GetName(), SessionPlugin::Create);
