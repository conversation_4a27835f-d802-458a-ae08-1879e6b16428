/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

#include "stdafx.h"

#include "utilWinHttpRequest.h"


/*
 * SimpleTest.cpp
 *
 *    Test cases that require no setup.
 *
 */

class SimpleTest : public ::testing::Test {
public:
   SimpleTest() {};

   ~SimpleTest() {};

   void SetUp() {}

   void TearDown() {}

protected:
   const std::string mGetUrlUnsecured = "http://www.omnissa.com/";
};


/*
 * SimpleTest::testEmptyUrl --
 *
 *    Tests that an empty URL returns false and empty response.
 */

TEST_F(SimpleTest, testEmptyUrl)
{
   std::string url = "";
   std::string response;

   WinHttpRequest req;
   EXPECT_TRUE(!req.getSync(url, response));
   EXPECT_TRUE(response.empty());
}


/*
 * SimpleTest::testHttpUrl --
 *
 *    Tests that an HTTP URL returns false and empty response. This is because we only support
 *    HTTPS URLs.
 */

TEST_F(SimpleTest, testHttpUrl)
{
   std::string url = mGetUrlUnsecured;
   std::string response;

   WinHttpRequest req;
   EXPECT_TRUE(!req.getSync(mGetUrlUnsecured, response));
   EXPECT_TRUE(response.empty());
}


/*
 * SimpleTest::testBadUrl --
 *
 *    Tests that a bad URL returns false and an empty response.
 */

TEST_F(SimpleTest, testBadUrl)
{
   std::string url = "badurlbadurl";
   std::string response;

   WinHttpRequest req;
   EXPECT_TRUE(!req.getSync(url, response));
   EXPECT_TRUE(response.empty());
}


/*
 * SimpleTest::testGetUnreachable --
 *
 *    Tests that an unreachable URL returns false and an empty response.
 */

TEST_F(SimpleTest, testGetUnreachable)
{
   std::string getUrl = "https://thisiteshouldnotexist12345.com/";
   std::string response;

   WinHttpRequest req;
   EXPECT_TRUE(!req.getSync(getUrl, response));
}
