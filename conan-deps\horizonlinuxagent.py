# Copyright (c) Omnissa, LLC. All rights reserved.
# This product is protected by copyright and intellectual property laws in the
# United States and other countries as well as by international treaties.
# -- Omnissa Restricted

import os
import sys

# Access peer file scons_bootstrap.py.
sys.path.append(os.path.dirname(__file__))
from scons_bootstrap import ConanSConsBootstrap


class ConanHorizonLinuxAgent(ConanSConsBootstrap):
    settings = "os", "arch", "build_type"

    def requirements(self):
        super().requirements()

        # Append required packages instead of inserting them, unless you know what you do
        self.requires("glibc/2.17")
        self.requires("zlib/1.3.1", options={"shared": True})
        self.requires("jsoncpp/1.9.5")
        self.requires("libfuse/2.9.9", options={"shared": True})
        self.requires("autorandr/1.13.3")
        self.requires("keyutils/1.5.5")
        self.requires("uriparser/0.9.8")
        self.requires("libepoxy/1.5.10", options={"shared": True})
        self.requires("libx265/3.4", options={"shared": True})
        self.requires("opus/1.4")
        self.requires("abseil/20240116.2")
        self.requires("protobuf/3.25.3")
        self.requires("protobuf-c/1.5.0")
        self.requires("libxml2/2.13.8", options={"shared": True})
        self.requires("xorg-proto/2023.2")
        self.requires("libx11/1.8.7", options={"shared": True})
        self.requires("libxkbfile/1.1.3", options={"shared": True})
        self.requires("libxi/1.8.1", options={"shared": True})
        self.requires("libxcomposite/0.4.6", options={"shared": True})
        self.requires("libxscrnsaver/1.2.4", options={"shared": True})
        self.requires("libxdmcp/1.1.4", options={"shared": True})
        self.requires("libxau/1.0.11", options={"shared": True})
        self.requires("libxcb/1.16", options={"shared": True})
        self.requires("libxdamage/1.1.6", options={"shared": True})
        self.requires("libxext/1.3.6", options={"shared": True})
        self.requires("libxfixes/6.0.1", options={"shared": True})
        self.requires("libxtst/1.2.4", options={"shared": True})
        self.requires("libxcursor/1.2.2", options={"shared": True})
        self.requires("libxinerama/1.1.5", options={"shared": True})
        self.requires("libxrandr/1.5.4", options={"shared": True})
        self.requires("libxrender/0.9.11", options={"shared": True})
        self.requires("mesa/23.3.6", options={"shared": True})
        self.requires("freetype/2.13.2", options={"shared": True})
        self.requires("openssl/3.0.16")
        self.requires("openssl_fips_validated/3.0.9")
        self.requires("harfbuzz/8.3.0", options={"shared": True})
        self.requires("libevent/2.1.12", options={"shared": True})
        self.requires("ogg/1.3.2")
        self.requires("speex/1.2rc2")
        self.requires("speexdsp/1.2rc3")
        self.requires("theora/1.1.1")
        self.requires("v4l-utils/1.20.0", options={"shared": True})
        self.requires("libbpf/1.2.0")
        self.requires("linux-headers-generic/6.5.9")
        self.requires("libjpeg-turbo/3.0.1")
        self.requires("gtest/1.17.0", options={"shared": True})
        self.requires("libyuv/1882")
        self.requires("snappy/1.1.7")
        self.requires("cups/2.3.6")
        self.requires("gdk-pixbuf-xlib/2.40.2", options={"shared": True})
        self.requires("gdk-pixbuf/2.42.10", options={"shared": True})
        self.requires("libffi/3.4.4", options={ "shared" : True})
        self.requires("pcre2/10.42", options={ "shared" : True})
        self.requires("glib/2.84.1", options={ "shared" : True})
        self.requires("glibmm/2.44.0", options={"shared": True})
        self.requires("at-spi2-core/2.51.0", options={"shared" : True})
        self.requires("fastlz/0.1.0")
        self.requires("gnome-menus/3.36", options={ "shared" : True})
        self.requires("libudev/248.13", options={"shared": True})
        self.requires("pulseaudio/17.0", options={"shared" : True})
        self.requires("dbus/1.15.8", options={"shared" : True})
        self.requires("libsndfile/1.2.2", options={"shared": True})
        self.requires("pango/1.50.7", options={"shared": True})
        self.requires("pangomm/2.34.0", options={"shared": True})
        self.requires("boost/1.86.0")
        self.requires("gtk/3.24.42", options={"shared": True})
        self.requires("gtkmm/3.10.1", options={"shared": True})

        isx86_64 = self.settings.arch == "x86_64"
        if isx86_64:
            self.requires("libx264/164.20220124", options={"shared": True})
            self.requires("ffmpeg/7.0.1", options={"shared": True})
            self.requires("ffmpeg-vdpau/7.0.1", options={"with_vdpau": True, "shared": True})
            self.requires("nvidia_sdk_12.0/12.0")
            self.requires("nvidia_sdk_8.1/8.1")
        self.requires("cairo/1.18.0", options={"shared": True})
        self.requires("cairomm/1.10.0", options={"shared": True})
        self.requires("libpng/1.6.48", options={"shared": True})
        self.requires("pixman/0.44.2", options={"shared": True})
        self.requires("expat/2.7.1", options={"shared": True})
        self.requires("fontconfig/2.15.0", options={"shared": True})
        self.requires("libdbusmenu/16.04.0", options={"shared": True})
        self.requires("libiconv/1.17", options={"shared": True})
        self.requires("libindicator/12.10.1", options={"shared": True})
        self.requires("atkmm/2.22.7", options={"shared": True})
        self.requires("libappindicator/12.10.0", options={"shared": True})
        self.requires("libsigcpp/2.10.8", options={"shared": True})
        self.requires("libxcvt/0.1.2")
        self.requires("libnotify/0.7.7", options={"shared": True})
        self.requires("util_linux/2.39.3", options={"shared": True})

    def build_requirements(self):
        super().build_requirements()

        self.tool_requires("7zip/24.09")
        self.tool_requires("protobuf/3.25.3")
        self.tool_requires("protobuf-c/1.5.0")
        self.tool_requires("libmagic/5.45")
        self.tool_requires("tar/1.35")
        self.tool_requires("rpm/4.19.1")
        self.tool_requires("gawk/5.3.0")
        self.tool_requires("gnupg/2.2.42")
        self.tool_requires("findutils/4.10.0")
        self.tool_requires("coreutils/9.4")
        self.tool_requires("gettext/0.22.5")

