/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/* global require: false, module: false */

"use strict";

var express = require('express');
var process = require('process');
var cp = require('child_process');
var fs = require('fs');
var os = require('os');
var path = require('path');
var _ = require('underscore');
var EventEmitter = require('events');
var systemConfig = require('./systemConfig');
var request = require('request');
var {boraRoot, boraBuildRoot, waitUntilListening} = require('./utils');

// Request ID for Benev cmd requests
var requestId = 1;

/*
 * Global full path log file name sent from BenevPeer to make sure
 * NodeJS code uses the same log file.
 */
var benevLogFileName = "";

/*
 * This is the "master" Benev object that spawns off various Benev network
 * nodes (e.g. benevPeers).
 */
function Benev() {
   this.client = null;
   this.server = null;
   this.bwCapper = null;
   this.pythonHttpServer = null;
}

var benevDefaultOptions = {
   listenPort: 3000,
   benevPeerPath: path.join(boraBuildRoot,
                        "appblastlibs/",
                        "beta/",
                        "benev/",
                        "blastSocketPeer/",
                        process.arch,
                        process.platform === 'win32' ? "benevPeer.exe" : "benevPeer"),
   topology: "",
   bwCapperPath: "bandwidth-capping-proxy.js",
   vvcAPITestPath: path.join(boraBuildRoot,
                        "appblastlibs/",
                        "beta/",
                        "test/",
                        process.arch,
                        process.platform === 'win32' ? "vvcServerTestTrigger.exe" : "vvcServerTestTrigger"),
   doMemLeakCheck: true,
   overwriteHorizonVvcDll: true,
   removeAllContainers: false,
   branding: 'new',
   enableRawChannel: false
};

/*
 *-----------------------------------------------------------------------
 * Runs procdump.exe on benevPeer process to collect memory dumps.
 * Current supported memory dump types are 'crash' and 'hang'.
 * This section is for tests running on Windows only.
 * Setup: This section needs procdump.exe in C:/ProcDump/ to run
 *        successfully
 *-----------------------------------------------------------------------
 */
function launchProcDump(dumpType, benevMode, benevPid, branding) {
   if (process.platform === "linux") {
      console.log("This function only supports on Windows.");
      return;
   }

   let commandLineOpts = ["-accepteula"];

   switch(dumpType) {
      case "crash":
         commandLineOpts.push("-ma");
         commandLineOpts.push("-e");
         commandLineOpts.push(`${benevPid}`);
         break;

      case "hang":
         commandLineOpts.push("-ma");
         commandLineOpts.push(`${benevPid}`);
         break;

      default:
         console.log('Unsupported dump type for ProcDump.');
         return;
   }

   try {
      let procDumpPath = "C:/ProcDump/procdump.exe";

      if (!fs.existsSync(procDumpPath)) {
         procDumpPath = "C:/Jenkins/procdump.exe";
      }

      if (fs.existsSync(procDumpPath)) {
         let benevDumpDir = "C:/ProgramData/Omnissa/Horizon/Dumps";
         if (branding === 'old') {
            benevDumpDir = "C:/ProgramData/VMware/VDM/Dumps";
         }
         let benevDumpFile = `benevpeer-${benevMode}-${benevPid}-${dumpType}.dmp`;
         let benevDumpPath = `${benevDumpDir}/${benevDumpFile}`;

         commandLineOpts.push(benevDumpPath);
         cp.spawn(procDumpPath, commandLineOpts);

         let commandLineOptsMessage = commandLineOpts.join(" ");
         console.log(`***** Spawned ${procDumpPath} ${commandLineOptsMessage} ***** `);
      } else {
         console.log(`${procDumpPath} not found. Skipping ProcDump.`);
      }
   } catch(error) {
      console.log('Error during ProcDump launch for benevPeer. ' + error);
   }
}

function startBenev(options, listenCb) {
   var app = express();
   var benev = new Benev();

   if (options.topology && fs.existsSync(options.topology)) {
      parseTopology(benev, options);
   }


   /*
    *---------------------------------------------------------------------------
    * Launch the benevPeer in the mode specified by :benevMode.
    *---------------------------------------------------------------------------
    */

   app.post('/:benevMode/launch', validateModeOrFail, function (req, res) {
      var benevMode = req.params.benevMode;
      const args = [
         "-mode",
         benevMode[0],
         "-node",
         `${options.doMemLeakCheck}`
      ];

      if (req.query.options !== "") {
         let opts = JSON.parse(req.query.options);
         if (opts.reset !== null && opts.reset === 'true') {
            opts.branding = options.branding;
            systemConfig.setRegKeysForWindows(opts);
         }
      }

      let benevPeerPath = options.benevPeerPath;
      if (!fs.existsSync(benevPeerPath)) {
         benevPeerPath = path.join('.', 'benevPeer');
      }

      let bsp = cp.spawn(
         benevPeerPath,
         args,
         { stdio: ['pipe', 'pipe', process.stderr, null, 'pipe'] });
      benev[benevMode] = bsp;
      console.log(`****** Spawned Benev ${benevMode} pid ${bsp.pid} path ${benevPeerPath} ****** `);

      launchProcDump("crash", benevMode, bsp.pid, options.branding);

      bsp.name = benevMode;
      bsp.exited = false;
      bsp.exitCode = 0;
      bsp.exitSignal = null;
      bsp.evtObj = new EventEmitter();

      bsp.on('error', function (error) {
         res.send("ERROR IN SPAWN: " + error);
         bsp.evtObj = null;
         benev[benevMode] = null;
      });

      bsp.stdio[4].once('data', function (data) {
         getBenevLogFileName(bsp);
         res.send(data.toString());
      });

      bsp.once('exit', (code, signal) => {
         console.log(`${benevMode}-${bsp.pid} exited ` +
                     `with code ${code} and signal ${signal}`);
         bsp.exited = true;
         bsp.exitCode = code;
         bsp.exitSignal = signal;
         bsp.evtObj.emit('childProcExit', code, signal);
      });
   });


   /*
    *---------------------------------------------------------------------------
    * Send "start" command to benevPeer.
    *---------------------------------------------------------------------------
    */

   app.post('/:benevMode/start',
         validateModeOrFail,
         function (req, res) {
      var protocol = req.query.protocol;
      var host = req.query.host;
      var tcpPort = req.query.tcpPort;
      var udpPort = req.query.udpPort;
      var useSSL = req.query.useSSL;
      var certFilePathName = req.query.certFilePathName;
      var checkRevocation = req.query.checkRevocation;
      var enableCrlCache = req.query.enableCrlCache;
      var alwaysLoadSSLLib = req.query.alwaysLoadSSLLib;
      var enableRawChannel = req.query.enableRawChannel;
      var cmd = [
         'start',
         ['TCP', 'BEAT', 'BENIT'].includes(protocol) ? `protocol=${protocol}` : ``,
         host !== undefined ? `host=${host}` : ``,
         tcpPort !== undefined ? `tcp-port=${tcpPort}` : ``,
         udpPort !== undefined ? `udp-port=${udpPort}` : ``,
         useSSL !== undefined ? `usessl=${useSSL}` : ``,
         certFilePathName != undefined ? `certfilepathname=${certFilePathName}` : ``,
         checkRevocation != undefined ? `checkrevocation=${checkRevocation}` : ``,
         enableCrlCache != undefined ? `enablecrlcache=${enableCrlCache}` : ``,
         alwaysLoadSSLLib != undefined ? `alwaysloadssllib=${alwaysLoadSSLLib}` : ``,
         enableRawChannel != undefined ? `enableRawChannel=${enableRawChannel}` : ``
      ];
      let mode = req.params.benevMode;
      if (mode === 'server') {
         benev[mode].enableRawChannel = enableRawChannel;
      }
      sendCmd(benev[mode], cmd, res);
   });


   /*
    *---------------------------------------------------------------------------
    * Send "set-vauth" command to benevPeer.
    *---------------------------------------------------------------------------
    */

   app.post('/:benevMode/set-vauth',
         validateModeOrFail,
         function (req, res) {
      var cmd = ['set-vauth'];
      var vauth = req.query.vauth;
      if (vauth) {
        cmd.push(vauth);
      }

      sendCmd(benev[req.params.benevMode], cmd, res);
   });


   /*
    *---------------------------------------------------------------------------
    * Send "set-route-token" command to benevPeer.
    *---------------------------------------------------------------------------
    */

   app.post('/:benevMode/set-route-token',
         validateModeOrFail,
         function (req, res) {
      let cmd = ['set-route-token'];
      let routeToken = req.query.routeToken;
      if (routeToken) {
         cmd.push(routeToken);
      }

      sendCmd(benev[req.params.benevMode], cmd, res);
   });


   /*
    *---------------------------------------------------------------------------
    * Send"close" command to benevPeer.
    *---------------------------------------------------------------------------
    */

   app.delete('/:benevMode/close',
         validateModeOrFail,
         function (req, res) {
      var cmd = ['close'];
      let mode = req.params.benevMode;
      let cb = function(resObj) {
         if (mode === 'server') {
            if (typeof resObj.data !== 'object' ||
                !resObj.data.hasOwnProperty('rawChannel')) {
               console.log('Missing raw channel status in server close response');
               // Return true as we need to handle older BenevPeers.
               return true;
            }

            if (benev[mode].enableRawChannel !== resObj.data.rawChannel) {
               console.log('Raw Channel check failure, expected: %s, actual: %s',
                           benev[mode].enableRawChannel, resObj.data.rawChannel);
               return false;
            }
            console.log('Raw Channel check success, expected: %s, actual: %s',
                        benev[mode].enableRawChannel, resObj.data.rawChannel);
            return true;
         }
         return true;
      }
      sendCmd(benev[mode], cmd, res, cb);
   });


   /*
    *---------------------------------------------------------------------------
    * Send "close channel" command to benevPeer.
    *---------------------------------------------------------------------------
    */

   app.delete('/:benevMode/close-channel',
         validateModeOrFail,
         function (req, res) {
      var cmd = ['close-channel'];
      sendCmd(benev[req.params.benevMode], cmd, res);
   });


   /*
    *---------------------------------------------------------------------------
    * Send "list commands" command to benevPeer.
    *---------------------------------------------------------------------------
    */

   app.get('/:benevMode/list-commands',
         validateModeOrFail,
         function (req, res) {
      var cmd = ['list-commands'];
      sendCmd(benev[req.params.benevMode], cmd, res);
   });


   /*
    *---------------------------------------------------------------------------
    * Send "get stats" command to benevPeer.
    *---------------------------------------------------------------------------
    */

   app.get('/:benevMode/get-stats',
         validateModeOrFail,
         function (req, res) {
      var cmd = ['get-stats'];
      sendCmd(benev[req.params.benevMode], cmd, res);
   });


   /*
    *---------------------------------------------------------------------------
    * Send "get perf metrics" command to benevPeer.
    *---------------------------------------------------------------------------
    */

   app.get('/:benevMode/get-perf-metrics',
         validateModeOrFail,
         function (req, res) {
      var cmd = [
         'get-perf-metrics'
      ];

      if (req.query.featureName) {
         cmd += [`featureName=${req.query.featureName}`];
      }

      sendCmd(benev[req.params.benevMode], cmd, res);
   });


   /*
    *---------------------------------------------------------------------------
    * Handle "delete dump" command and dump benevPeer process.
    *---------------------------------------------------------------------------
    */

   app.delete('/:benevMode/dump', validateModeOrFail, function (req, res) {
      var benevMode = req.params.benevMode;
      var pid = benev[benevMode].pid;

      console.log(`Dumping ${benevMode}-${pid}..`);

      launchProcDump("hang", benevMode, pid, options.branding);

      console.log(`Dumping ${benevMode}-${pid} done..`);

      res.send('dumpdone');
   });


   /*
    *---------------------------------------------------------------------------
    * Send "exit" command to benevPeer and check if the other benevPeer has
    * already exited or not. If the other benevPeer has already exited and the
    * memLeakCheck flag is set, then change the loglevel and memloglevels to
    * their initial values.
    *---------------------------------------------------------------------------
    */

   app.delete('/:benevMode/exit', validateModeOrFail, function (req, res) {
      var mode = req.params.benevMode;
      var otherPeer = (mode === "client") ? "server" : "client";
      var cmd = ['exit'];

      let bsp = benev[mode];

      function exitResp(bsp) {
         if (bsp.exitCode === 0) {
            return 'exit';
         } else {
            return (`exited with code ${bsp.exitCode} and ` +
                    `signal ${bsp.exitSignal}`);
         }
      }

      if (bsp === null) {
         res.send("never-created");
         return;
      } else if (bsp.exited) {
         res.send(exitResp(bsp));
         return;
      }

      bsp.evtObj.once('childProcExit', () => {
         res.send(exitResp(bsp));
      });

      // The exitCb means the benev peer has sent us an "about to exit" message,
      // which means that it cleanly exited. But 'childProcExit' event above
      // truly means that the child process has exited.
      sendExitToBenevPeer(benev[mode], cmd, options, function (errMsg) {
         if (errMsg) {
            res.send(errMsg);
         }

         if (benev[otherPeer] === null && options.doMemLeakCheck) {
            if (process.platform === 'win32') {
               const execFile = require('child_process').execFile;
               try {
                  execFile('Regedit', ['/s', 'Unset.reg']);
               } catch (error) {
                  console.log(`Failed to reset the log level reg keys`);
               }
            }
         }
      });
   });


   /*
    *---------------------------------------------------------------------------
    * Set the Benev loglevel to the passed in value.
    *---------------------------------------------------------------------------
    */

   app.put('/:benevMode/set-logLevel',
         validateModeOrFail,
         function (req, res) {
      var type = req.query.type;
      var cmd = ['set-loglevel', type];
      sendCmd(benev[req.params.benevMode], cmd, res);
   });


   /*
    *---------------------------------------------------------------------------
    * Set the Benev memLoglevel to the passed in value.
    *---------------------------------------------------------------------------
    */

   app.put('/:benevMode/set-memloglevel',
         validateModeOrFail,
         function (req, res) {
      var type = req.query.type;
      var cmd = ['set-memloglevel', type];
      sendCmd(benev[req.params.benevMode], cmd, res);
   });


   /*
    *---------------------------------------------------------------------------
    * Runs the memory leak detector on the benevPeer's logfile.
    *---------------------------------------------------------------------------
    */
   app.get('/:benevMode/memleakcheck',
         validateModeOrFail,
         function (req, res) {
      var mode = req.params.benevMode;
      var pid = benev[mode].pid;

      var curr_dir = process.cwd();
      process.chdir('../memLeakDetector');
      var spawn = require('child_process').spawn;
      var pyMemLeakChild;
      var resStr = 'Exited with code ';

      if (!options.doMemLeakCheck) {
         console.log("WARNING: The memory leak detector will not track Vvc " +
                     "messages, channels, etc. if Vvc logLevel is not trace " +
                     "and Vvc memLogLevel is not all");
      }

      console.log(`\nSpawning memory leak detector for ${mode}-${pid}`);
      pyMemLeakChild = spawn('python', ['memleakdetector.py', benevLogFileName],
                             { stdio: ['pipe', 'pipe', process.stderr] });
      pyMemLeakChild.stdout.on('data', function (data) {
         console.log(data.toString());
      });
      pyMemLeakChild.on('exit', function (code) {
         resStr += code;
         console.log(resStr);
         res.send(resStr);
      });

      process.chdir(curr_dir);
   });


   /*
    *---------------------------------------------------------------------------
    * Checks the benevPerr's logFile for connection error codes.
    *
    * TODO: Change this to query the BENeV Peer for the close reason.
    *---------------------------------------------------------------------------
    */
   app.get('/:benevMode/connect-error-check',
         validateModeOrFail,
         function (req, res) {
      let mode = req.params.benevMode;
      let pid = benev[mode].pid;

      let response = {
         command: 'connect-error-check',
         data: null, // The found error code. Null means no error code.
         result: 'failure'
      }

      console.log(`Checking for connection error codes for ${mode}-${pid}`);
      fs.readFile(benevLogFileName, 'utf8', (err, data) => {
         if (err) {
            console.log(`Error while reading log file for ${mode}-${pid}: ${err}`);
            res.send(response);
            return;
         }

         // Find the connect error message, if it exists
         let failureAlertIndex = data.indexOf('Blast_Connect_Failure_Alert');
         if (failureAlertIndex === -1) {
            console.log('No blast connect failure alert log line found');
            response.result = 'success';
            res.send(response);
            return;
         }

         // Find the start of the error code, if it exists
         let errorCodeIndex = data.indexOf('VDPCONNECT', failureAlertIndex);
         if (errorCodeIndex === -1) {
            console.log('Missing VDPCONNECT error code in the ' +
                        'blast connect failure alert log line');
            res.send(response);
            return;
         }

         // Gets the next newline character, if it exists
         // Makes sure that the error code is on the same line as the failure alert
         // If a newline character is not found, there is not another line
         let endLineIndex = data.indexOf('\n', failureAlertIndex);
         if (endLineIndex != -1 && errorCodeIndex > endLineIndex) {
            console.log('VDPCONNECT error code does not exist on the same ' +
                        'log line as the blast connect failure alert');
            res.send(response);
            return;
         }

         let errorCode = '';

         // The error code will start with VDPCONNECT and end right before a space
         for (let i = errorCodeIndex; i < data.length; i ++) {
            let character = data.charAt(i);

            if (character === ' ') {
               break;
            } else {
               errorCode += character;
            }
         }

         console.log(`Found blast connect failure alert error code: ${errorCode}`);
         response.data = errorCode;
         response.result = 'success';
         res.send(response);
      });
   });

   /*
    *---------------------------------------------------------------------------
    * Send "start-proxy" command to benevPeer.
    *---------------------------------------------------------------------------
    */

   app.post('/:benevMode/start-proxy/', validateModeOrFail, function (req, res) {
      let cmd = 'start-proxy';
      let cmdArgs = _.chain(req.query)
          .pairs()  // convert to (name, value) pairs
          .map((nvp) => {return [nvp[0], nvp[1]].join('=');})
          .value();

      sendCmd(benev[req.params.benevMode],
                         [cmd].concat(cmdArgs),
                         res);
   });


   /*
    *---------------------------------------------------------------------------
    * Send "stop-proxy" command to benevPeer.
    *---------------------------------------------------------------------------
    */

   app.post('/:benevMode/stop-proxy/', validateModeOrFail, function (req, res) {
      let cmd = 'stop-proxy';
      let cmdArgs = _.chain(req.query)
          .pairs()  // convert to (name, value) pairs
          .map((nvp) => {return [nvp[0], nvp[1]].join('=');})
          .value();

      sendCmd(benev[req.params.benevMode],
                         [cmd].concat(cmdArgs),
                         res);
   });


   /*
    *---------------------------------------------------------------------------
    * Launch the bandwidth capper.
    *---------------------------------------------------------------------------
    */

   app.post('/launch-bwcapper/', function (req, res) {
      launchBWCapper(benev, req.query, options, res);
   });


   /*
    *---------------------------------------------------------------------------
    * Kill the bandwidth capper.
    *---------------------------------------------------------------------------
    */

   app.delete('/kill-bwcapper/', function (req, res) {
      killBWCapper(benev, res);
   });


   /*
    *---------------------------------------------------------------------------
    * Launch the blast secure gateway.
    *---------------------------------------------------------------------------
    */

   app.post('/launch-bsg/', function (req, res) {
      launchBSG(benev, res);
   });


   /*
    *---------------------------------------------------------------------------
    * Kill the blast secure gateway.
    *---------------------------------------------------------------------------
    */

   app.delete('/kill-bsg/', function (req, res) {
      killBSG(benev, res);
   });


   /*
    *---------------------------------------------------------------------------
    * Adds a route to the blast secure gateway.
    *
    * The client must try to connect through it within 60 seconds.
    *---------------------------------------------------------------------------
    */

   app.post('/add-bsg-route/', function (req, res) {
      addBSGRoute(benev, req.query, res);
   });


   /*
    *---------------------------------------------------------------------------
    * Removes a route from the blast secure gateway.
    *---------------------------------------------------------------------------
    */

   app.delete('/remove-bsg-route/', function (req, res) {
      removeBSGRoute(benev, req.query, res);
   });


   /*
    *---------------------------------------------------------------------------
    * Initial setup to run the Vvc API tests - writes config file.
    *---------------------------------------------------------------------------
    */

   app.post('/:benevMode/setup-vvc-api-tests/', validateModeOrFail, function (req, res) {
      var host = req.query.host ? req.query.host : '127.0.0.1';
      var port = req.query.port ? req.query.port : '50132';
      let vvcAPITestPath = path.resolve(options.vvcAPITestPath);

      if (!fs.existsSync(vvcAPITestPath)) {
         vvcAPITestPath = path.resolve(path.join('../../test/x64', 'vvcServerTestTrigger'));
      }

      let configs = 'PeerIPAddress=' + host + os.EOL + os.EOL +
                    'Port=' + port + os.EOL + os.EOL +
                    'TestFilesPath=' + path.dirname(vvcAPITestPath);

      fs.writeFileSync('C:\\Users\\<USER>\\vvc_testconfig.ini', configs, 'utf8');

      res.send('setup-vvc-api-tests');
   });


   /*
    *---------------------------------------------------------------------------
    * Perform setup for running out-of-proc Vvc API tests. There are two main
    * parts to this: launch WSNM and make sure that horizon-vvc.dll is in the
    * expected location.
    *---------------------------------------------------------------------------
    */

   app.get('/setup-oop/', function (req, res) {
      let response = {
         command: 'setup-oop',
         result: 'failure'
      };

      // WSNM is required for the OOP features to communicate with Vvc
      function startWSNM() {
         function isServiceRunning(serviveName) {
            return (cp.spawnSync('sc', ['query', serviveName]).stdout.toString().indexOf('RUNNING') > 0);
         }

         function startService(serviveName) {
            cp.spawnSync('sc', ['start', serviveName]);
            return isServiceRunning(serviveName);
         }

         // Out-of-proc Vvc API tests can NOT run without WSNM service running
         if (!isServiceRunning('wsnm')) {
            console.log('\nStarting WSNM...');
            if (!startService('wsnm')) {
               console.log('\n WSNM cannot be started, out-of-proc Vvc ' +
                           'API tests can NOT run\n');
               console.log('Look at the BENeV readme for help installing WSNM.');
               console.log('If you don\'t care about running out-of-proc tests,' +
                           'try running \'npm run testInproc\' instead.');
               res.send(response);
               return;
            }
         }

         console.log('\nWSNM service is running...');
      }


      // The Vvc API tests expect horizon-vvc.dll in a hardcoded location
      function moveVvcDll() {
         function isOSWin64() {
            return process.arch === 'x64' || process.env.hasOwnProperty('PROCESSOR_ARCHITEW6432');
         }

         const path = require('path');

         // bora/apps/rde/vvc/tests/FunctionTest/vvcServerTestApp/vvcServerTestApp.cpp#23
         var dstDllPath = (isOSWin64() ? 'C:/Program Files/Common Files/Omnissa/Blast/x64' :
                                         'C:/Program Files/Common Files/Omnissa/Blast');
         var dstDLLPathName = path.join(dstDllPath, 'horizon-vvc.dll');
         if (options.branding === 'old') {
            dstDllPath = (isOSWin64() ? 'C:/Program Files/Common Files/VMware/VMware Blast/x64' :
                                        'C:/Program Files/Common Files/VMware/VMware Blast');
            dstDLLPathName = path.join(dstDllPath, 'vmware-vvc.dll');
         }

         /*
          * Out-of-proc Vvc API tests can NOT run without horizon-vvc.dll
          * located in the above folder.
          * The overwriteHorizonVvcDll flag indicates that we should try to use
          * the locally built horizon-vvc.dll. If a local build doesn't exist,
          * we'll try to use whatever is located at dstDllPath.
          * If the flag is set to false then we'll only try to use a
          * horizon-vvc.dll that is already at VVC_NODE_ROOT_PATH
          * (i.e. one that came from a horizonagent installation).
          */
         var useLocalDll = options.overwriteHorizonVvcDll;
         var jenkinsPath = path.resolve('../../x64/horizon-vvc.dll');
         if (options.branding === 'old') {
            jenkinsPath = path.resolve('../../stableBenev/x64/vmware-vvc.dll');
         }

         if (useLocalDll) {
            let srcDllPathName = null;
            let localBuildPath = path.join(boraBuildRoot,
                                           "build/",
                                           "horizon-vvc/",
                                           "beta/",
                                           "win64/",
                                           "horizon-vvc.dll");
            if (options.branding === 'old') {
               localBuildPath = path.join(boraBuildRoot,
                                          "build/",
                                          "vmware-vvc/",
                                          "beta/",
                                          "win64/",
                                          "vmware-vvc.dll");
            }
            // Check Jenkins and local build paths
            if (fs.existsSync(jenkinsPath)) {
               srcDllPathName = jenkinsPath;
            } else if (fs.existsSync(localBuildPath)) {
               srcDllPathName = localBuildPath;
            }

            if (srcDllPathName !== null) {
               // Copy local build to dstDLLPathName
               try {
                  if (!fs.existsSync(dstDllPath)) {
                     console.log('Creating destination folder:\n' + dstDllPath);
                     fs.mkdirSync(dstDllPath, { recursive: true });
                  }

                  fs.copyFileSync(srcDllPathName, dstDLLPathName);

                  console.log('Using build:\n' + srcDllPathName);
                  response.result = 'success';
                  return;
               } catch (error) {
                  console.log('ERROR: Unable to copy\n' + srcDllPathName +
                              ' to\n' + dstDLLPathName)
                  console.log('\n' + error);
                  /*
                   * If Horizon Agent is installed, sometimes the dll will be
                   * locked by the OOP features. horizon-vvc.dll exists, but
                   * we can't overwrite it. Alert the user that we won't use
                   * their local build and continue.
                   */
                  if (error.code === 'EBUSY' || error.code === 'EPERM') {
                     console.log('\n\n***WARNING: horizon-vvc.dll is locked and ' +
                                 'can\'t be overwritten by the local build.')
                     console.log('Test will continue with the file originally ' +
                                 'at:\n' + dstDLLPathName + '\n');
                     response.result = 'success';
                     return;
                  }
               }
            }
         }

         // Either useLocalDll === false or there was no local horizon-vvc.dll
         if (fs.existsSync(jenkinsPath)) {
            response.result = 'success';
         }
      }

      startWSNM();
      moveVvcDll();

      res.send(response);
   });


   /*
    *---------------------------------------------------------------------------
    * Launch the Vvc API tests.
    *---------------------------------------------------------------------------
    */

   app.post('/:benevMode/run-vvc-api-tests/', validateModeOrFail, function (req, res) {
      var benevMode = req.params.benevMode;

      var testMode = req.query.dooutofproc === 'true' ? 'out-of-proc' : 'in-proc';
      var tests = req.query.tests;
      const args = [
         '--log_level=all',
         '--report_level=detailed',
         tests ? `--run_test=${tests}` : '--run_test=*'
      ];

      let vvcAPITestPath = options.vvcAPITestPath;
      if (!fs.existsSync(vvcAPITestPath)) {
         vvcAPITestPath = path.join('../../test/x64', 'vvcServerTestTrigger');
      }

      console.log(`\n****** Launching ${testMode} Vvc API Tests ******\n`);

      if (req.query.dooutofproc === 'true') {
         const dirname = require('path').dirname;
         const resolve = require('path').resolve;

         if (options.branding === 'old') {
            vvcAPITestPath = path.join('../../stableBenev/test/x64', 'vvcServerTestTrigger');
         }

         let vvcAPIServerTestPath = path.join(dirname(resolve(vvcAPITestPath)),
                                              'vvcServerTestApp.exe');
         let vvcAPIServerTestRunner = null;

         /*
          * For Allow Channel List test, we need to load in vdpService.dll instead of
          * vvcServerTestPlugin.dll because the security check will need to verify the
          * full path name of the API module.
          */
         if (req.query.doallowchannellist === 'true') {
            let vvcAPIServerTestPluginPath = path.join(dirname(resolve(vvcAPITestPath)),
                                                       'vvcServerTestPlugin.dll');
            let vvcAPIVdpServicePath = path.join(dirname(resolve(vvcAPITestPath)),
                                                 'vdpService.dll');
            fs.copyFileSync(vvcAPIServerTestPluginPath, vvcAPIVdpServicePath);

            var allowChannel = req.query.allowchannel === 'true' ?
                               'allowing' : 'disallowing';
            console.log(`Virtual Channel Allow List is enabled ` +
                        `and ${allowChannel} channel.\n`);
         }

          vvcAPIServerTestRunner = cp.spawn(
            vvcAPIServerTestPath,
            req.query.doallowchannellist === 'true' ? ['-allowchannellist'] : [],
            { stdio: ['pipe', process.stdout, process.stderr] });

          vvcAPIServerTestRunner.exitCode = 0;
          vvcAPIServerTestRunner.exitSignal = null;
          vvcAPIServerTestRunner.evtObj = new EventEmitter();

          vvcAPIServerTestRunner.on('error', function (error) {
              res.send('ERROR IN SPAWN: ' + error);
              vvcAPIServerTestRunner.evtObj = null;
          });
      }

      let vvcAPITestRunner = cp.spawn(
         vvcAPITestPath,
         args,
         { stdio: ['pipe', process.stdout, process.stderr] });

      vvcAPITestRunner.exitCode = 0;
      vvcAPITestRunner.exitSignal = null;
      vvcAPITestRunner.evtObj = new EventEmitter();

      vvcAPITestRunner.on('error', function (error) {
         res.send('ERROR IN SPAWN: ' + error);
         vvcAPITestRunner.evtObj = null;
      });

      vvcAPITestRunner.once('exit', (code, signal) => {
         let resultStr = `vvcAPITest: exited with code ${code} ` +
                         `and signal ${signal}`;
         console.log(resultStr);
         res.send(resultStr);
         vvcAPITestRunner.evtObj.emit('childProcExit', code, signal);
      });
   });


   /*
    *---------------------------------------------------------------------------
    * Write a string to the host's config file.
    *---------------------------------------------------------------------------
    */
   app.post('/:benevMode/write-config/', validateModeOrFail, function (req, res) {
      let cmd = 'write-config';
      let response = {
         command: cmd,
         result: 'failed'
      };

      let configStr = req.query.config;
      if (configStr === undefined) {
         console.log(`ERROR: Insufficent arguments for ${cmd}, no config string given`);
         res.send(response);
         return;
      }

      let config = JSON.parse(configStr);
      let mode = req.query.mode ? req.query.mode : 'w';

      systemConfig.writeConfigs(config, mode);

      response.result = 'success';
      res.send(response);
   });


   /*
    *---------------------------------------------------------------------------
    * Setup the host's config file.
    *---------------------------------------------------------------------------
    */
    app.post('/:benevMode/prime-config/', validateModeOrFail, function(req, res) {
      let cmd = 'prime-config';
      let response = {
         command: cmd,
         result: 'failed'
      };

      let defaultOptsStr = req.query.defaultOpts;
      if (defaultOptsStr === undefined) {
         console.log(`ERROR: Insufficent arguments for ${cmd}, no default options given`);
         res.send(response);
         return;
      }

      let defaultOpts = JSON.parse(defaultOptsStr);
      defaultOpts.branding = options.branding;
      systemConfig.primeConfigs(defaultOpts, (error) => {
         if (!error) {
            console.log("Configs are primed for testing\n");
            response.result = 'success';
         } else {
            response.result = error.message;
         }

         res.send(response);
      });
   });


   /*
    *---------------------------------------------------------------------------
    * Clear the host's config file.
    *---------------------------------------------------------------------------
    */
    app.post('/:benevMode/clear-config/', validateModeOrFail, function(req, res) {
      let cmd = 'clear-config';
      let response = {
         command: cmd,
         result: 'success'
      };

      systemConfig.clearConfigs();

      res.send(response);
   });


   /*
    *---------------------------------------------------------------------------
    * Set the Vvc API test reg keys.
    *---------------------------------------------------------------------------
    */
    app.post('/:benevMode/set-vvc-api-test-reg-keys/', validateModeOrFail, function(req, res) {
      let cmd = 'set-vvc-api-test-reg-keys';
      let response = {
         command: cmd,
         result: 'success'
      };

      let optsStr = req.query.opts;
      if (optsStr === undefined) {
         console.log(`ERROR: Insufficent arguments for ${cmd}, no options given`);
         response.result = 'failed';
         res.send(response);
         return;
      }

      let opts = JSON.parse(optsStr);
      opts.branding = options.branding;
      opts.benevPeerPath = options.benevPeerPath;
      opts.benevMode = req.params.benevMode;
      systemConfig.setRegKeysForVvcAPITests(opts);

      res.send(response);
   });


   /*
    *---------------------------------------------------------------------------
    * Unset the Vvc API test reg keys.
    *---------------------------------------------------------------------------
    */
    app.post('/:benevMode/unset-vvc-api-test-reg-keys/', validateModeOrFail, function(req, res) {
      let cmd = 'unset-vvc-api-test-reg-keys';
      let response = {
         command: cmd,
         result: 'success'
      };

      systemConfig.unsetRegKeysForVvcAPITests();
      console.log("Reg keys are unset from vvc api testing\n");

      res.send(response);
   });


   /*
    *---------------------------------------------------------------------------
    * Set the Raw Channel Type Reg key.
    *---------------------------------------------------------------------------
    */
    app.post('/:benevMode/set-raw-type-reg-keys/', validateModeOrFail, function(req, res) {
      let cmd = 'set-raw-type-reg-keys';
      let response = {
         command: cmd,
         result: 'success'
      };

      let blastProtocol = req.query.blastProtocol;
      let rawChannelType;
      switch(blastProtocol) {
         case 'TCP':
         case 'BEAT':
            rawChannelType = blastProtocol.toLowerCase();
            break;
         case 'BENIT':
            rawChannelType = 'vvc';
            break;
         default:
            console.log(`ERROR: Incorrect arguments for ${cmd}, protocol `
                        + `${blastProtocol} not supported`);
            response.result = 'failed';
            res.send(response);
            return;
      }

      systemConfig.setRegKeyForRawChannelType(rawChannelType);

      res.send(response);
   });


   /*
    *---------------------------------------------------------------------------
    * Validate Node Server is up.
    *---------------------------------------------------------------------------
    */

   app.get('/alive/', function (req, res) {
      res.send("node server alive");
   });

   /*
    *---------------------------------------------------------------------------
    * Start the server to listen on specific port.
    *---------------------------------------------------------------------------
    */

   app.listen(options.listenPort, function () {
      console.log("Server listening on port:", options.listenPort);
      if (listenCb) {
         listenCb();
      }
   });


   /*
    *-----------------------------------------------------------------------------
    * isValidBenevMode:
    *
    *    Validates that a passed in string matches one of the Benev modes.
    *
    *-----------------------------------------------------------------------------
    */

   function isValidBenevMode(benevMode) {
      if (benevMode !== "server" && benevMode !== "client") {
         console.log("Error: Received a command for " + benevMode + ".",
                     "Can only process commands for 'client' or 'server'.");
         return false;
      }

      return true;
   }


   /*
    *-----------------------------------------------------------------------------
    * validateModeOrFail:
    *
    *    First function in a chain that the above endpoints will go through.
    *    Will fail and exit from the chain if isValidBenevMode fails.
    *
    *-----------------------------------------------------------------------------
    */
   function validateModeOrFail(req, res, next) {
      if (isValidBenevMode(req.params.benevMode)) {
         next();
      } else {
         res.sendStatus(404);
      }
   }

   function agentStart(benevMode, agentIP, clientIP, fileToSend, port, req, res) {
      console.log("Inside Agent API, with values: ");
      console.log(`${benevMode}, ${agentIP}, ${clientIP}, ${fileToSend}, ${port}`);
      var ackMessage;
      var inStream = fs.createReadStream(fileToSend);
      var correct = fs.readFileSync(fileToSend);
      var afterReceive;
      var afterProcess;
      var maxBuf = 1024*1024*1024;

      //This will start the ncat listener, which connects to the agent proxy
      const{ exec }= require('child_process');
      exec(`ncat --recv-only -l ${agentIP} ${port} > output.txt`,
         {maxBuffer: maxBuf}, (error, stdout, stderr) => {
         afterReceive = new Date();
         console.log(error);
         var received = fs.readFileSync('./output.txt');
         if (received.equals(correct)){
            ackMessage = 'success';
            console.log('Data transfer to agent succeeded.');
         } else {
            ackMessage = 'failure';
            console.log('Data transfer to agent was a FAILURE.');
         }
         afterProcess = new Date();
         var processTime = afterProcess-afterReceive;
         let ackSend = {
            'processTime': processTime,
            'ackMessage': ackMessage
         };
         let ackS = JSON.stringify(ackSend);
         //This sends the ack back to the client, without the proxy
         fs.writeFile('./ack.json', ackS, () => {
            setTimeout(() => {
               exec(`ncat --send-only ${clientIP} 11112 < ack.json`, (err, stdout, stderr) => {
                  console.log('Finished sending ack back to client.');
                  console.log(err);
                  console.log(stdout);
                  console.log(stderr);
               });
            }, 1000);
         });
      });
      console.log(`****** Spawned Benev ${benevMode} netcat port ${port} ****** `);
      res.status(200).send('POST request for netcat finished');
   }

   function clientStart(benevMode, agentIP, clientIP, fileToSend, port, req, res) {
      var inStream = fs.createReadStream(fileToSend)

      /* "Beforesend", "afterSend", and "afterAckReceive" are measured in client VM
       * "afterReceive" and "afterProcess" are measured in Agent VM
       * Because of time drift, we cannot do "AfterReceive" - "beforeSend", so we instead
       * do "afterAckReceive" - "beforeSend" and subtract out "afterProcess-afterReceive"
       * to account for the ack processing time.
       * However, it ignores the time spent sending the ack.
       */
      var beforeSend;
      var afterSend;
      var afterReceive;
      var afterProcess;
      var afterAckReceive;

      const{exec} = require('child_process');

      var args = ['-i1', '-f', 'port 53000', '-P', '-w', 'my.pcapng'];

      var tshark = cp.spawn(
         'tshark',
         args,
	 { stdio: ['pipe', 'pipe', process.stderr, null, 'pipe'] }
      );

      setTimeout(() => { //Using a timeout to ensure tshark is started before, will use "spawn" event
         console.log('Message will begin sending to agent.');
         beforeSend = new Date();
         //ncat command to send the file to agent through proxy
         exec(`ncat --send-only ${clientIP} ${port} < ${fileToSend}`, (error, stdout, stderr) => {
            console.log('Message has been sent to agent.');
            afterSend = new Date();
            console.log(error);

	    if (error) {
               res.status(400);
	    }

            console.log(stdout);
            console.log(stderr);

            exec(`ncat -l ${clientIP} 11112 > ackOut.json`, (err, sout, serr) => {
               tshark.kill();
               afterAckReceive = new Date();

               if (err){
                  console.log(err);
                  res.status(400);
               } else if (sout) {
                  console.log(sout);
               } else if (serr) {
                  console.log(serr);
               }

               console.log('Received ack on client end.');
               let ackFile = fs.readFileSync('./ackOut.json');
               let ack = JSON.parse(ackFile);
               console.log(ack);

               console.log(`Approx. time: ${afterAckReceive - beforeSend - ack.processTime - 1000} ms`);

               /*The following tshark commands will give the statistics on the file transfer
                *"e" is the error, "o" is the stdout, "i" is the stdin*/
               exec('tshark -q -z conv,tcp -Y "tcp.port==53000" -r my.pcapng', (e, o, i) => {
                  console.log(e);
                  console.log(o);
                  console.log(i);
               });
               exec('tshark -q -z conv,udp -Y "udp.port==53000" -r my.pcapng', (e, o, i) => {
                  console.log(e);
                  console.log(o);
                  console.log(i);
               });
               exec('tshark -q -z conv,ip -Y "tcp.port==53000 && udp.port==53000" -r my.pcapng', (e, o, i) => {
                  console.log(e);
                  console.log(o);
                  ack.body = o; // o is the stdout, sending it all to the controller VM
                  console.log(i);
                  setTimeout(() => {
                     if (ack.ackMessage == 'success'){
                        res.status(200).send(ack);
                     } else {
                        console.log('Message send was a failure.');
                        res.status(500).send(ack);
		     }
                  }, 1000);
                });
            });
	});
      }, 1000);
      console.log(`****** Spawned Benev ${benevMode} netcat port ${port} ****** `);
   }


   /*
    *---------------------------------------------------------------------------
    * Launch the netcat process in the mode specified by :benevMode.
    *---------------------------------------------------------------------------
    */

   app.post('/:benevMode/launch-ncat/:agentIP/:clientIP/:fileToSend/:port',
      validateModeOrFail, function (req, res) {
      var benevMode = req.params.benevMode;
      var agentIP = req.params.agentIP;
      var clientIP = req.params.clientIP;
      var fileToSend = req.params.fileToSend;
      var port = req.params.port;

      if (benevMode == "server") {
         agentStart(benevMode, agentIP, clientIP, fileToSend, port, req, res);
      } else if (benevMode == "client") {
         clientStart(benevMode, agentIP, clientIP, fileToSend, port, req, res);
      } else {
         console.log("Error, invalid peermode.");
      }
   });


   /*
    *---------------------------------------------------------------------------
    * Launch Python HTTP server to host CRL file for SSL CRL Cache.
    *---------------------------------------------------------------------------
    */
   app.get('/launchhttpserver/', function (req, res) {
      let response = {
         command: 'launchhttpserver',
         result: 'failed'
      };

      console.log('Spawning Python HTTP server...');

      var curr_dir = process.cwd();

      try {
         process.chdir('./sslcrlfiles');
         benev.pythonHttpServer = cp.spawn('python', ['-m', 'http.server', '--bind', '127.0.0.1']);
         console.log('Spawned Python HTTP server in ' + process.cwd() + '\n');
         response.result = 'success';
      } catch(error) {
         console.log('Failed to start Python HTTP server. ' + error);
      }

      process.chdir(curr_dir);

      res.send(response);
   });


   /*
    *---------------------------------------------------------------------------
    * Kill Python HTTP server.
    *---------------------------------------------------------------------------
    */

   app.delete('/killhttpserver/', function (req, res) {
      console.log('\nKilling Python HTTP server...');

      let response = {
         command: 'killhttpserver',
         result: 'success'
      };

      benev.pythonHttpServer.once('exit', (code, signal) => {
         res.send(response);
      });

      if (!benev.pythonHttpServer.kill()) {
         //We have failed to kill Python HTTP server and the test is now invalid
         response.result = 'failed';
         res.send(response);
      } else {
          console.log('Successfully killed Python HTTP server\n');
      }
   });
}


/*
 *-----------------------------------------------------------------------------
 * printSendCmd
 *
 *    Logs the command about to be sent.
 *
 *-----------------------------------------------------------------------------
 */

 function printSendCmd(bsp, cmd) {
   console.log(`=> Sending command [${cmd}] to ${bsp.name}-${bsp.pid}`);
 }


/*
 *-----------------------------------------------------------------------------
 * sendCmd:
 *
 *    Function to send requested commands to benevPeer with a requestId
 *    and send response from benevPeer back to test program with the same
 *    requestId.
 *    We listen until we get a response for this requestId.
 *
 *-----------------------------------------------------------------------------
 */

function sendCmd(bsp, cmdAndArgs, res, validateCb) {
   if (bsp !== null) {
      let reqId = getRequestId()
      cmdAndArgs.push('reqId=' + reqId);
      printSendCmd(bsp, cmdAndArgs);
      cmdAndArgs.push('\n');
      bsp.stdin.write(cmdAndArgs.join(' '), 'utf8');
      recvAndSyncResponse(bsp, reqId, res, validateCb);
   } else {
      res.send("socket closed");
   }
}


/*
 *-----------------------------------------------------------------------------
 * recvAndSyncResponse:
 *
 *    Function to receive and check if the response is for the requested reqId.
 *    If not, wait for more data till we receive a vaid response.
 *
 *    Note: Responses may be conjoined, i.e., we may receive multiple json
 *    responses in a single event (ex: {json1}\r\n{json2}\r\n).
 *
 *-----------------------------------------------------------------------------
 */

function recvAndSyncResponse(bsp, reqId, res, validateCb) {
   bsp.stdio[4].once('data', function (data) {
      // May receive multiple conjoined responses
      let splitData = data.toString().split("\r\n");
      // Ignore the last cut.
      // Although not seen in testing, we may receive partial responses.
      let partialData = splitData.pop();
      if (partialData) {
         console.log('Partial response received: ' + partialData + '. This is ' +
                     'not expected and will likely cause a test failure.');
      };
      for (let i = 0; i < splitData.length; i++) {
         let responseObj = JSON.parse(splitData[i]);
         if(responseObj.reqId == reqId) {
            console.log('<= Received response [%j] from %s-%s', responseObj, bsp.name, bsp.pid);
            let success = true;
            if (validateCb) {
               success = validateCb(responseObj);
            }
            res.statusCode = success ? 200 : 502;
            res.send(responseObj);
            return;
         }
      }
      recvAndSyncResponse(bsp, reqId, res);
   });
}


/*
 *-----------------------------------------------------------------------------
 * sendExitToBenevPeer:
 *
 *    Function to send exit command to benevPeer and send response
 *    from benevPeer back to test program.
 *
 *-----------------------------------------------------------------------------
 */

function sendExitToBenevPeer(bsp, cmd, options, exitCb) {
   if (bsp === null) {
      return;
   }

   printSendCmd(bsp, cmd)
   cmd.push('\n');
   bsp.stdin.write(cmd.join(''), 'utf8');
   bsp.stdio[4].once('data', function (data) {
      exitCb();
   });
}


/*
 *-----------------------------------------------------------------------------
 * sendStartConnectionToBenevPeer:
 *
 *    Function to give start/connect command to benevPeer and send
 *    response from benevPeer back to test program.
 *
 *-----------------------------------------------------------------------------
 */

function sendStartConnectionToBenevPeer(bsp, cmd, protocol, host, tcpPort, res) {
   if (bsp !== null) {
      const cmdLine = [
         cmd,
         ['TCP', 'BEAT', 'BENIT'].includes(protocol) ? `protocol=${protocol}` : ``,
         host !== undefined ? `host=${host}` : ``,
         tcpPort !== undefined ? `tcp-port=${tcpPort}` : ``,
      ].join(' ') + '\n';

      bsp.stdin.write(cmdLine, 'utf8');
      bsp.stdio[4].once('data', function (data) {
         res.send(data.toString());
      });
   } else {
      res.send("socket closed");
   }
}


/*
 *-----------------------------------------------------------------------------
 * getBenevLogFileName:
 *
 *    Return the specified BENeV peer's log file path.
 *
 *-----------------------------------------------------------------------------
 */
function getBenevLogFileName(bsp) {
   if (bsp === null) {
      return;
   }

   bsp.stdin.write('get-logfilename\n', 'utf8');
   bsp.stdio[4].once('data', function (response) {
      benevLogFileName = JSON.parse(response).data;
   });
}


/*
 *-----------------------------------------------------------------------------
 * launchBWCapper:
 *
 *    Launch a bandwidth capping proxy based on the attributes from a
 *    bw-capper object in a topology.
 *
 *-----------------------------------------------------------------------------
 */

function launchBWCapper(benev, bw_capper_params, options, res) {
   var args = [];
   var bwCapper = {
      process: null,
       topology: null,
       sourcePath: path.join(boraRoot,
                          "apps/",
                          "asyncSocketProxy/",
                          "bandwidth-capping-proxy.js")
   };

   for (var key in bw_capper_params) {
      if (key !== "type" && key !== "name" && key !== "//") {
         var option = '--' + key;
         args.push(option);
         args.push(bw_capper_params[key]);
      }
   }

   // Save the params in case we want to stop / start this again.
   bwCapper.topology = bw_capper_params;

   // When bandwidth capper is unavailable locally, copy from source path
   try {
      if (!fs.existsSync(options.bwCapperPath)) {
         // copy the bandwidth capping proxy file to current directory
         fs.copyFileSync(bwCapper.sourcePath, path.join(process.cwd(), "bandwidth-capping-proxy.js"));
      }
   } catch (error) {
      console.log("File copy failed ", error);
   }

   console.log("Launching bandwidth capper:");
   bwCapper.process = cp.fork(options.bwCapperPath, args);

   let response = {
      command: 'launch-bwcapper',
      result: 'failure'
   };

   waitUntilListening('http://localhost:8123').then(() => {
      // Bandwidth capper started listening
      response.result = 'success';
      res.send(response);
   }).catch(() => {
      // Bandwidth capper didn't start listening
      res.send(response);
   });

   benev.bwCapper = bwCapper;
}



/*
 *-----------------------------------------------------------------------------
 * killBWCapper:
 *
 *    Kill the bandwidth capping proxy
 *
 *-----------------------------------------------------------------------------
 */

function killBWCapper(benev, res) {
   console.log("Killing bandwidth capper:");

   let response = {
      command: 'kill-bwcapper',
      result: 'success'
   };

   benev.bwCapper.process.once('exit', (code, signal) => {
      res.send(response);
   });
   if (!benev.bwCapper.process.kill()) {
      //We have failed to kill the bandwidth capper and the test is now invalid
      response.result = 'failed';
      res.send(response);
   }
}


/*
 *-----------------------------------------------------------------------------
 * launchBSG:
 *
 *    Launch a blast secure gateway.
 *
 *    The client must try to connect through it within 60 seconds.
 *
 *-----------------------------------------------------------------------------
 */

 function launchBSG(benev, res) {
   console.log("Launching blast secure gateway");

   let response = {
      command: 'launch-bsg',
      result: 'failure'
   };

   if (!benev.bsg) {
      var bsg = {
         process: null
      };

      const bsgPath = '/home/<USER>/bsg/lib/absg/run-absg.js';

      try {
         bsg.process = cp.fork(bsgPath);
      } catch (err) {
         console.log(err);
         res.send(response);
         return;
      }

      benev.bsg = bsg;
   }

   const bsgAddr = 'http://localhost:8123';

   waitUntilListening(`${bsgAddr}/health`, 20000).then(() => {
      request.post(`${bsgAddr}/startWait`, (err, bsgRes) => {
         if (err || bsgRes.statusCode !== 200) {
            const errMsg = err ? err : `Code ${bsgRes.statusCode} - ${bsgRes.body}`
            console.log(`Failed to start bsg: ${errMsg}`);
            res.send(response);
            return;
         }

         // We need to wait to give the UDP Forwarder a chance to launch
         setTimeout(() => {
            response.result = 'success';
            res.send(response);
         }, 1000);
      });
   }).catch(() => {
      // Promise was rejected
      console.log('The bsg never started listening');
      res.send(response);
   });
}


/*
 *-----------------------------------------------------------------------------
 * killBSG:
 *
 *    Kill the blast secure gateway.
 *
 *-----------------------------------------------------------------------------
 */

function killBSG(benev, res) {
   console.log("Killing blast secure gateway");

   let response = {
      command: 'kill-bsg',
      result: 'success'
   };

   if (!benev.bsg) {
      console.log('The blast secure gateway is not running');
      response.result = 'failure';
      res.send(response);
      return;
   }

   benev.bsg.process.once('exit', (code, signal) => {
      delete benev.bsg;
      res.send(response);
   });

   if (!benev.bsg.process.kill()) {
      //We have failed to kill the blast secure gateway and the test is now invalid
      console.log('The blast secure gateway could not be killed');
      response.result = 'failed';
      res.send(response);
   }
}


/*
 *-----------------------------------------------------------------------------
 * addBSGRoute:
 *
 *    Adds a route to a blast secure gateway.
 *
 *    The client must try to connect through it within 60 seconds.
 *
 *-----------------------------------------------------------------------------
 */

 function addBSGRoute(benev, params, res) {
   console.log("Adding BSG Route");

   let response = {
      command: 'add-bsg-route',
      result: 'failure'
   };

   if (!benev.bsg) {
      console.log('The blast secure gateway is not running');
      res.send(response);
      return;
   }

   const bsgAddr = 'http://localhost:8123';

   const agentIp = params['agentIp'];
   const agentPort = params['agentPort'];

   if (!agentIp || !agentPort) {
      console.log('You must specify agentIp and agentPort');
      res.send(response);
      return;
   }

   const routeBody = {
      port_address: agentIp,
      port_port: agentPort,
   };

   const portKey = params['portKey'];

   if (!portKey) {
      console.log('You must specify portKey');
      res.send(response);
      return;
   }

   request.put(`${bsgAddr}/routes/${portKey}`, { json: routeBody }, (err, bsgRes) => {
      if (err || bsgRes.statusCode !== 200) {
         const errMsg = err ? err : `Code ${bsgRes.statusCode} - ${bsgRes.body}`
         console.log(`Failed to add route: ${errMsg}`);
         res.send(response);
         return;
      }

      const routeToken = bsgRes.body.ext_token;
      if (!routeToken) {
         console.log('Did not recieve a route token from the bsg');
         res.send(response);
         return;
      }

      response.result = 'success';
      response.data = routeToken;
      res.send(response);
   });
}


/*
 *-----------------------------------------------------------------------------
 * removeBSGRoute:
 *
 *    Removes a route from a blast secure gateway.
 *
 *-----------------------------------------------------------------------------
 */

 function removeBSGRoute(benev, params, res) {
   console.log("Removing BSG Route");

   let response = {
      command: 'remove-bsg-route',
      result: 'failure'
   };

   if (!benev.bsg) {
      console.log('The blast secure gateway is not running');
      res.send(response);
      return;
   }

   const bsgAddr = 'http://localhost:8123';
   const portKey = params['portKey'];

   if (!portKey) {
      console.log('You must specify portKey');
      res.send(response);
      return;
   }

   request.delete(`${bsgAddr}/routes/${portKey}`, (err, bsgRes) => {
      if (err || bsgRes.statusCode !== 200) {
         const errMsg = err ? err : `Code ${bsgRes.statusCode} - ${bsgRes.body}`
         console.log(`Failed to remove route: ${errMsg}`);
         res.send(response);
         return;
      }

      response.result = 'success';
      res.send(response);
   });
}


/*
 *-----------------------------------------------------------------------------
 * parseTopology:
 *
 *    Parse a given topology and apply any specified options.
 *
 *-----------------------------------------------------------------------------
 */

function parseTopology(benev, options) {
   fs.readFile(options.topology, "utf8", function (err, data) {
      if (err) {
         throw err;
      }
      var topology = JSON.parse(data);
      if (!topology.nodes) {
         console.log("Could not find nodes in topology.");
         return;
      }
      topology.nodes.forEach(function (node, index, array) {
         if (node.type === "bw-capper") {
            launchBWCapper(benev, node, options);
         }
      });
   });
}


/*
 *-----------------------------------------------------------------------------
 * getRequestId:
 *
 *    Utility function to generate a unique request ID.
 *    Uses the global var: requestId.
 *
 *-----------------------------------------------------------------------------
 */

function getRequestId() {
   return requestId++;
}


module.exports = {
   benevDefaultOptions: benevDefaultOptions,
   startBenev: startBenev
};
