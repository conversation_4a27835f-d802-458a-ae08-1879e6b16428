# Copyright (c) Omnissa, LLC. All rights reserved.
# This product is protected by copyright and intellectual property laws in the
# United States and other countries as well as by international treaties.
# -- Omnissa Restricted

"""
logwrapper.py

A wrapper around the standard Python logging infrastructure to allow color,
customizable log prefixes, log level customization per module, etc.

This relies on some custom core SCons changes to add some command line options
to SCons, allowing it to also support the customization. Currently, core SCons
messages are not color-enabled.
"""
import datetime
import logging
import os
import sys

import SCons
from vmware.exception import ConfigError, ScriptError

DEFAULT_LOG_LEVEL = logging.WARNING

# A global dictionary of name:(description, defaultLevel) pairs
_LOGGER_TABLE = {
    "aliases": ("Information about vmware.Alias() calls", logging.WARNING),
    "sourcelist": ("Information about the source listing", logging.INFO),
    "coverity": ("Information about coverity analysis", logging.INFO),
    "subdir": ("Information about subdirs", logging.WARNING),
    "deprecated": ("Info about calls to deprecated functions", logging.WARNING),
    "main": ("Messages related to main control flow", logging.INFO),
    "notfound": ("Info about missing foreign nodes", logging.WARNING),
    "isconsd": ("log messages from iscons server", logging.INFO),
    "isconsc": ("log messages from iscons client", logging.INFO),
    "globals": ("Info about usage of global variables", logging.WARNING),
    "locals": ("Info about usage of local variables", logging.INFO),
    "log": ("Messages about the logging system", logging.INFO),
    "forker": ("Messages from forkers", logging.INFO),
    "wait": ("Messages from the waiting source builder", logging.WARNING),
    "vtools": ("Messages about the vtool system", logging.WARNING),
    "gradle": ("Messages about gradle targets", logging.WARNING),
    "maven": ("Messages about maven targets", logging.WARNING),
    "status": (
        "Messages about currents status during script execution phase",
        logging.WARNING,
    ),
    "scanner": ("Messages from the source code scanner", logging.WARNING),
    "env": ("Messages about environments", logging.INFO),
    "hacks": (
        "Messages from code that is a hack or workaround for a bug",
        logging.WARNING,
    ),
    "compcache": ("Messages related to the component cache", logging.WARNING),
    "compnodes": ("Messages related to custom node types", logging.WARNING),
    "packaging": ("Messages related to packaging", logging.INFO),
    "noderegistry": ("Messages related to the global node registry", logging.INFO),
    "binary-verification": (
        "Messages showing information about "
        "binaries and link-time binary verification",
        logging.WARNING,
    ),
    "wix": ("Information on wix generation", logging.WARNING),
    "depcop": ("Information on dependency enforcement", logging.WARNING),
    "conan": ("Information about Conan dependencies", logging.INFO),
}


# A dict of named loglevel values that map to those found in the logging
# module, so that you can say LOGGING=main=DEBUG on the command line, for
# example.
_LOG_VALUE_MAP = {
    "DEBUG": logging.DEBUG,
    "NOTSET": logging.NOTSET,
    "INFO": logging.INFO,
    "WARNING": logging.WARNING,
    "ERROR": logging.ERROR,
    "CRITICAL": logging.CRITICAL,
    "ALL": 1,
}


def PrintAvailableLoggers():
    """Print the available loggers to stdout"""
    log = GetLogger("log")
    log.info("==============================================================")
    log.info(" AVAILABLE LOGGERS")
    log.info("==============================================================")
    for name, desc in sorted(_LOGGER_TABLE.items(), key=lambda tup: tup[0]):
        log.info(" %s: %s" % (name, desc[0]))


def GetLoggersHelpString():
    return "    Run scons LOGGING=help for more information\n"


def handleError(self, record):
    """
    We override the default error handler to give a more helpful error
    than backtrace that stops inside the logging module when do
    something like mess up a log format specifier
    """
    raise ScriptError(
        "Invalid log format specifier at %s:%s" % (record.pathname, record.lineno)
    )


logging.Handler.handleError = handleError


class MyFormatter(logging.Formatter, object):
    """
    A custom formatter to allow the printing of milliseconds with a custom
    date format. The Python logging module does not allow printing of
    milliseconds with a custom date format.
    """

    converter = datetime.datetime.fromtimestamp

    def formatTime(self, record, datefmt=None):
        ct = self.converter(record.created)
        if datefmt:
            s = ct.strftime(datefmt)
            if "%f" in datefmt:
                s = s[:-3]
        else:
            t = ct.strftime("%Y-%m-%d %H:%M:%S")
            s = "%s,%03d" % (t, record.msecs)
        return s

    def format(self, record):
        replace = os.path.normpath(
            os.path.join(os.path.dirname(sys.argv[0]), os.path.pardir, os.path.pardir)
        )
        record.pathname = record.pathname.replace(replace + "/", "#")
        record.fnln = "%s:%d" % (record.pathname, record.lineno)
        return super(MyFormatter, self).format(record)


def addANSIColoring(fn):
    """
    A replacement emitter for the standard Python logging module, providing
    ANSI coloring to console messages from the logging module based on log
    level.
    """

    def new(*args):
        levelno = args[1].levelno
        if levelno >= 50:
            color = "\x1b[31m"  # red
        elif levelno >= 40:
            color = "\x1b[31m"  # red
        elif levelno >= 30:
            color = "\x1b[33m"  # yellow
        elif levelno >= 20:
            color = "\x1b[32m"  # green
        elif levelno >= 10:
            color = "\x1b[35m"  # pink
        else:
            color = "\x1b[0m"  # normal
        args[1].msg = color + str(args[1].msg) + "\x1b[0m"  # normal
        return fn(*args)

    return new


def SetLoggingOptions(
    logSpecString="",
    overrideDefaultLogLevel=DEFAULT_LOG_LEVEL,
    showModule=True,
    showLevel=True,
    showDate=False,
    showTime=True,
    showMillis=True,
    showFileAndLineNumbers=False,
):
    """
    Set logging options based on the provided values.
    """
    if "help" in logSpecString:
        PrintAvailableLoggers()
        sys.exit(0)

    try:
        # Set the real SCons options
        SCons.Script.SetOption("logging_show_date", showDate)
        SCons.Script.SetOption("logging_show_time", showTime)
        SCons.Script.SetOption("logging_show_millis", showMillis)
        SCons.Script.SetOption("logging_show_level", showLevel)
        SCons.Script.SetOption("logging_show_module", showModule)
    except TypeError:
        pass

    for name, desc in _LOGGER_TABLE.items():
        logger = GetLogger(name)
        # Do not call parent handlers, otherwise we get duplicate messages
        logger.propagate = False
        # If DEFAULT_LOGLEVEL has not changed, apply the per-logger defaults
        if overrideDefaultLogLevel != DEFAULT_LOG_LEVEL:
            logger.setLevel(desc[1])
        # Apply a per-logger format
        SetLoggingFormat(
            logger,
            showModule,
            showLevel,
            showDate,
            showTime,
            showMillis,
            showFileAndLineNumbers,
        )

    # Process our command line option. Assume the string is a list of comma
    # separate pairs of loggername=loglevel.
    logspecpairs = logSpecString.split(",")
    for alogspec in logspecpairs:
        # The default value for LOGGING is '', so don't barf on that case
        if len(alogspec) == 0:
            continue

        try:
            (loggername, loggerlevel) = alogspec.split("=")
            logger = GetLogger(loggername)

            # lookup log value to see if it is a special value
            key = loggerlevel.upper()
            if key in _LOG_VALUE_MAP:
                val = _LOG_VALUE_MAP[key]
            else:
                # if it's not a recognized name, then it must be a number
                val = int(loggerlevel)
        except ValueError:
            PrintAvailableLoggers()
            raise ConfigError("Logger channel '%s' not found" % alogspec)

        logger.setLevel(val)

    if logSpecString != "":
        log = GetLogger("log")
        log.warn("Setting log levels according to %s" % logSpecString)


def _getLoggingFormat(
    showModule=True,
    showLevel=True,
    showDate=False,
    showTime=True,
    showMillis=True,
    showFileAndLineNumbers=False,
):
    """
    Return the logging string based on configuration parameters.
    """
    format = ""
    if showDate and not showTime:
        dateFormat = "%d/%m/%Y"
    elif showTime and not showDate:
        dateFormat = "%H:%M:%S"
    elif showTime and showDate:
        dateFormat = "%d/%m/%Y %H:%M:%S"
    else:
        dateFormat = ""
    if showMillis and showTime:
        dateFormat = "%s.%%f" % dateFormat
    if showDate or showTime:
        format += "%(asctime)s "

    if showModule:
        format += "%(name)-10s "

    if showLevel:
        format += "%(levelname)-8s "

    if showFileAndLineNumbers:
        format += "%(fnln)-55s "

    format += "%(message)s"

    return (format, dateFormat)


def SetLoggingFormat(
    logger,
    showModule,
    showLevel,
    showDate,
    showTime,
    showMillis,
    showFileAndLineNumbers,
):
    """
    Set the logstring format based on options provided.
    """
    format, dateFormat = _getLoggingFormat(
        showModule, showLevel, showDate, showTime, showMillis, showFileAndLineNumbers
    )
    formatter = MyFormatter(fmt=format, datefmt=dateFormat)
    handler = logging.StreamHandler(sys.stdout)
    handler.setFormatter(formatter)
    logger.handlers = []
    logger.addHandler(handler)


def InitLogging():
    """
    Initialize our custom logging infrastructure.
    """
    # This call sets up the root logger's StreamHandler. The level is set to
    # NOTSET so that the stream handler doesn't filter out any log messages that
    # the root logger lets through.
    format, dateFormat = _getLoggingFormat()
    logging.basicConfig(level=logging.NOTSET, format=format, datefmt=dateFormat)

    # Set the root logger's log level and default format
    rootlogger = logging.getLogger("")
    # The default logging level for the root logger is WARNING. Unless a
    # loglevel logger is specified on the command line, it will inherit the root
    # logger's logging level.
    rootlogger.setLevel(DEFAULT_LOG_LEVEL)
    formatter = MyFormatter(fmt=format, datefmt=dateFormat)
    handler = logging.StreamHandler(sys.stdout)
    handler.setFormatter(formatter)
    rootlogger.handlers = []
    rootlogger.addHandler(handler)

    # Set the per-module default log levels
    for name, desc in _LOGGER_TABLE.items():
        logger = logging.getLogger(name)
        logger.setLevel(desc[1])


# Save the old logging emitter
_oldEmit = logging.StreamHandler.emit


def IsColoredLoggingSupportedByPlatform():
    """Returns True if colored logging is supported by the current OS."""
    return os.name != "nt" or sys.getwindowsversion().major >= 10


def LoggingSetColor(val):
    """
    Enable log coloring for consoles that support ANSI color codes.
    """
    if val:
        if os.name == "nt":
            # Call SetConsoleMode() to opt into the new virtual terminal
            # processing feature, which supports colored logging.
            import ctypes

            kernel32 = ctypes.windll.kernel32
            kernel32.SetConsoleMode(kernel32.GetStdHandle(-11), 7)

        logging.StreamHandler.emit = addANSIColoring(logging.StreamHandler.emit)
    else:
        logging.StreamHandler.emit = _oldEmit


def GetLogger(name):
    """
    Only return a logger if its in the _LOGGER_TABLE.
    """
    if name in _LOGGER_TABLE:
        return logging.getLogger(name)
    else:
        PrintAvailableLoggers()
        raise ScriptError("No logger named '%s'" % name)
