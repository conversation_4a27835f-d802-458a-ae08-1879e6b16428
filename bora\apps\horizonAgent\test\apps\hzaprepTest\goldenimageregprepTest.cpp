/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * goldenimageregprepTest.cpp
 *
 *      Unit tests for GoldenImageRegPrep, as part of Nutanix support for
 *      Horizon Agent.
 */

#include "utMock.h"
#include "gtest/gtest.h"
#include <WinSock2.h>
#include "messageFramework.h"

using namespace CORE;

#include "mfw-helpers.h"
#include "gmock-helpers.h"
#include "stdafx.h"
#include "goldenimageregprep.h"

using namespace testing;

#define BROKER_FQDN L"brokerFQDN"

class GoldenImageRegPrepTest : public Test {

protected:
   void setMockNonce(MsgBinary &msgBinary)
   {
      wstr someData = L"somedata";
      msgBinary.set(someData.p_upd(), someData.s(), true);
   }

   void setRequest(PropertyBag &request, const wstr &vmGuid, const MsgBinary &pubKey,
                   const MsgBinary &nonce, const MsgBinary &signature)
   {
      request.add(PAR_GOLDEN_IMAGE_GUID, vmGuid);
      request.addBinary(PAR_PHS_PUBLIC_KEY, pubKey.pBinary, pubKey.sBinary);
      request.addBinary(PAR_NONCE, nonce.pBinary, nonce.sBinary);
      request.addBinary(PAR_PHS_POP_BLOB, signature.pBinary, signature.sBinary);
      request.addBinary(PAR_SIGNATURE, signature.pBinary, signature.sBinary);
   }
};


/*
 *-----------------------------------------------------------------------------
 *
 * GoldenImageRegPrepTest::destructorRemoveNoneTest
 *
 *      Tests the destructor of GoldenImageRegPrep when failing to remove
 *      mPHsKeyPairName and mMasterKeyName.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GoldenImageRegPrepTest, destructorRemoveNoneTest)
{
   VMOCK(&GoldenImageRegPrep::remove).ExpectCall(StrEq(KEYPAR_PH_SIGN)).WillOnce(Return(false));

   VMOCK(&GoldenImageRegPrep::remove).ExpectCall(StrEq(MASTER_KEY_NAME)).WillOnce(Return(false));

   {
      GoldenImageRegPrep gi;
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * GoldenImageRegPrepTest::destructorRemoveNoneTest
 *
 *      Tests the destructor of GoldenImageRegPrep when successfully removing
 *      mPHsKeyPairName and mMasterKeyName.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GoldenImageRegPrepTest, destructorRemoveAllTest)
{
   VMOCK(&GoldenImageRegPrep::remove).ExpectCall(StrEq(L"PHs")).WillOnce(Return(true));

   VMOCK(&GoldenImageRegPrep::remove).ExpectCall(StrEq(MASTER_KEY_NAME)).WillOnce(Return(true));

   {
      GoldenImageRegPrep gi;
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * goldenImageRegPrepTest::createPersistentKeyPairFailTest
 *
 *      Tests an attempt to create the golden image, but fails to create the
 *      persistent key pair.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GoldenImageRegPrepTest, createPersistentKeyPairFailTest)
{
   VMOCK(&GoldenImageRegPrep::createPersistentKeyPair)
      .ExpectCall(StrEq(KEYPAR_PH_SIGN), VAL_KEY_LENGTH)
      .WillOnce(Return(false));

   VMOCK(&GoldenImageRegPrep::generateNonce).Times(0);
   VMOCK(&GoldenImageRegPrep::getVmGuid).Times(0);
   VMOCK(&GoldenImageRegPrep::getPublicKey).Times(0);
   VMOCK(&GoldenImageRegPrep::connectAndSend).Times(0);

   GoldenImageRegPrep gi;
   EXPECT_FALSE(gi.registerGoldenImage(BROKER_FQDN));
}


/*
 *-----------------------------------------------------------------------------
 *
 * goldenImageRegPrepTest::generateNonceFailTest
 *
 *      Tests an attempt to create the golden image, but fails to generate a
 *      nonce.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GoldenImageRegPrepTest, generateNonceFailTest)
{
   MsgBinary nonce;

   VMOCK(&GoldenImageRegPrep::createPersistentKeyPair)
      .ExpectCall(StrEq(KEYPAR_PH_SIGN), VAL_KEY_LENGTH)
      .WillOnce(Return(true));

   VMOCK(&GoldenImageRegPrep::generateNonce)
      .ExpectCall(VAL_NONCE_SIZE, _)
      .WillOnce(DoAll(SetArgReferee<1>(nonce), Return(false)));

   VMOCK(&GoldenImageRegPrep::getVmGuid).Times(0);
   VMOCK(&GoldenImageRegPrep::getPublicKey).Times(0);
   VMOCK(&GoldenImageRegPrep::connectAndSend).Times(0);

   GoldenImageRegPrep gi;
   EXPECT_FALSE(gi.registerGoldenImage(BROKER_FQDN));
}


/*
 *-----------------------------------------------------------------------------
 *
 * goldenImageRegPrepTest::getVmGuidFailTest
 *
 *      Tests an attempt to create the golden image, but fails to get the VM
 *      GUID.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GoldenImageRegPrepTest, getVmGuidFailTest)
{
   wstr vmGuid;
   MsgBinary nonce;

   VMOCK(&GoldenImageRegPrep::createPersistentKeyPair)
      .ExpectCall(StrEq(KEYPAR_PH_SIGN), VAL_KEY_LENGTH)
      .WillOnce(Return(true));

   VMOCK(&GoldenImageRegPrep::generateNonce)
      .ExpectCall(VAL_NONCE_SIZE, _)
      .WillOnce(DoAll(SetArgReferee<1>(nonce), Return(true)));

   VMOCK(&GoldenImageRegPrep::getVmGuid)
      .ExpectCall(_)
      .WillOnce(DoAll(SetArgReferee<0>(vmGuid), Return(false)));

   VMOCK(&GoldenImageRegPrep::getPublicKey).Times(0);
   VMOCK(&GoldenImageRegPrep::connectAndSend).Times(0);

   GoldenImageRegPrep gi;
   EXPECT_FALSE(gi.registerGoldenImage(BROKER_FQDN));
}


/*
 *-----------------------------------------------------------------------------
 *
 * goldenImageRegPrepTest::getPublicKeyFailTest
 *
 *      Tests an attempt to create the golden image, but fails to get the
 *      public key from KeyVault.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GoldenImageRegPrepTest, getPublicKeyFailTest)
{
   wstr vmGuid = L"12345678";
   MsgBinary nonce;
   setMockNonce(nonce);

   VMOCK(&GoldenImageRegPrep::createPersistentKeyPair)
      .ExpectCall(StrEq(KEYPAR_PH_SIGN), VAL_KEY_LENGTH)
      .WillOnce(Return(true));

   VMOCK(&GoldenImageRegPrep::generateNonce)
      .ExpectCall(VAL_NONCE_SIZE, _)
      .WillOnce([&](wstr, MsgBinary &msgBinary) {
         setMockNonce(msgBinary);
         return true;
      });

   VMOCK(&GoldenImageRegPrep::getVmGuid)
      .ExpectCall(_)
      .WillOnce(DoAll(SetArgReferee<0>(vmGuid), Return(true)));

   MsgBinary pubKey, signature;
   wstr sigData;
   sigData.setBinary(nonce.pBinary, nonce.sBinary);
   sigData.append(vmGuid);

   VMOCK(&GoldenImageRegPrep::getPublicKey)
      .ExpectCall(StrEq(KEYPAR_PH_SIGN), sigData, _, _)
      .WillOnce(DoAll(SetArgReferee<2>(pubKey), SetArgReferee<3>(signature), Return(false)));

   VMOCK(&GoldenImageRegPrep::connectAndSend).Times(0);

   GoldenImageRegPrep gi;
   EXPECT_FALSE(gi.registerGoldenImage(BROKER_FQDN));
}


/*
 *-----------------------------------------------------------------------------
 *
 * goldenImageRegPrepTest::connectAndSendFailTest
 *
 *      Tests an attempt to create the golden image, but fails to connect to
 *      the broker via Framework channel and send the specified message.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GoldenImageRegPrepTest, connectAndSendFailTest)
{
   wstr vmGuid = L"12345678";
   MsgBinary nonce;
   setMockNonce(nonce);

   VMOCK(&GoldenImageRegPrep::createPersistentKeyPair)
      .ExpectCall(StrEq(KEYPAR_PH_SIGN), VAL_KEY_LENGTH)
      .WillOnce(Return(true));

   VMOCK(&GoldenImageRegPrep::generateNonce)
      .ExpectCall(VAL_NONCE_SIZE, _)
      .WillOnce([&](wstr, MsgBinary &msgBinary) {
         setMockNonce(msgBinary);
         return true;
      });

   VMOCK(&GoldenImageRegPrep::getVmGuid)
      .ExpectCall(_)
      .WillOnce(DoAll(SetArgReferee<0>(vmGuid), Return(true)));

   MsgBinary pubKey, signature;
   wstr sigData;
   sigData.setBinary(nonce.pBinary, nonce.sBinary);
   sigData.append(vmGuid);

   VMOCK(&GoldenImageRegPrep::getPublicKey)
      .ExpectCall(StrEq(KEYPAR_PH_SIGN), sigData, _, _)
      .WillOnce(DoAll(SetArgReferee<2>(pubKey), SetArgReferee<3>(signature), Return(true)));

   PropertyBag request, response;
   setRequest(request, vmGuid, pubKey, nonce, signature);

   VMOCK(&GoldenImageRegPrep::connectAndSend)
      .ExpectCall(StrEq(BROKER_FQDN), StrEq(QUEUE_AGENT_PAIRING), StrEq(HINT_GI_REGISTRATION),
                  request, _)
      .WillOnce(DoAll(SetArgReferee<4>(response), Return(false)));

   GoldenImageRegPrep gi;
   EXPECT_FALSE(gi.registerGoldenImage(BROKER_FQDN));
}


/*
 *-----------------------------------------------------------------------------
 *
 * goldenImageRegPrepTest::emptyDataTest
 *
 *      Tests an attempt to create the golden image, but the data retrieved
 *      from the response bag's binary is empty.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GoldenImageRegPrepTest, emptyDataTest)
{
   wstr vmGuid = L"12345678";
   MsgBinary nonce;
   setMockNonce(nonce);

   VMOCK(&GoldenImageRegPrep::createPersistentKeyPair)
      .ExpectCall(StrEq(KEYPAR_PH_SIGN), VAL_KEY_LENGTH)
      .WillOnce(Return(true));

   VMOCK(&GoldenImageRegPrep::generateNonce)
      .ExpectCall(VAL_NONCE_SIZE, _)
      .WillOnce([&](wstr, MsgBinary &msgBinary) {
         setMockNonce(msgBinary);
         return true;
      });

   VMOCK(&GoldenImageRegPrep::getVmGuid)
      .ExpectCall(_)
      .WillOnce(DoAll(SetArgReferee<0>(vmGuid), Return(true)));

   MsgBinary pubKey, signature;
   wstr sigData;
   sigData.setBinary(nonce.pBinary, nonce.sBinary);
   sigData.append(vmGuid);

   VMOCK(&GoldenImageRegPrep::getPublicKey)
      .ExpectCall(StrEq(KEYPAR_PH_SIGN), sigData, _, _)
      .WillOnce(DoAll(SetArgReferee<2>(pubKey), SetArgReferee<3>(signature), Return(true)));

   PropertyBag request;
   setRequest(request, vmGuid, pubKey, nonce, signature);

   VMOCK(&GoldenImageRegPrep::connectAndSend)
      .ExpectCall(StrEq(BROKER_FQDN), StrEq(QUEUE_AGENT_PAIRING), StrEq(HINT_GI_REGISTRATION),
                  request, _)
      .WillOnce(Return(true));

   GoldenImageRegPrep gi;
   EXPECT_FALSE(gi.registerGoldenImage(BROKER_FQDN));
}


/*
 *-----------------------------------------------------------------------------
 *
 * goldenImageRegPrepTest::emptyResultSigTest
 *
 *      Tests an attempt to create the golden image, but the result signature
 *      is empty. This occurs when the nonce is mismatched with the result
 *      signature.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GoldenImageRegPrepTest, emptyResultSigTest)
{
   wstr vmGuid = L"12345678";
   MsgBinary nonce;
   setMockNonce(nonce);

   VMOCK(&GoldenImageRegPrep::createPersistentKeyPair)
      .ExpectCall(StrEq(KEYPAR_PH_SIGN), VAL_KEY_LENGTH)
      .WillOnce(Return(true));

   VMOCK(&GoldenImageRegPrep::generateNonce)
      .ExpectCall(VAL_NONCE_SIZE, _)
      .WillOnce([&](wstr, MsgBinary &msgBinary) {
         setMockNonce(msgBinary);
         return true;
      });

   VMOCK(&GoldenImageRegPrep::getVmGuid)
      .ExpectCall(_)
      .WillOnce(DoAll(SetArgReferee<0>(vmGuid), Return(true)));

   MsgBinary pubKey, signature;
   wstr sigData;
   sigData.setBinary(nonce.pBinary, nonce.sBinary);
   sigData.append(vmGuid);

   VMOCK(&GoldenImageRegPrep::getPublicKey)
      .ExpectCall(StrEq(KEYPAR_PH_SIGN), sigData, _, _)
      .WillOnce(DoAll(SetArgReferee<2>(pubKey), SetArgReferee<3>(signature), Return(true)));

   PropertyBag request;
   setRequest(request, vmGuid, pubKey, nonce, signature);

   VMOCK(&GoldenImageRegPrep::connectAndSend)
      .ExpectCall(StrEq(BROKER_FQDN), StrEq(QUEUE_AGENT_PAIRING), StrEq(HINT_GI_REGISTRATION),
                  request, _)
      .WillOnce([](auto, auto, auto, auto, PropertyBag &resp) {
         wstr someData = L"someOtherData";
         resp.addBinary(PAR_RESULT_SIG, someData.p_upd(), someData.s());
         return true;
      });

   GoldenImageRegPrep gi;
   EXPECT_FALSE(gi.registerGoldenImage(BROKER_FQDN));
}


/*
 *-----------------------------------------------------------------------------
 *
 * goldenImageRegPrepTest::emptyResultTextTest
 *
 *      Tests an attempt to create the golden image, but the result text is
 *      empty.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GoldenImageRegPrepTest, emptyResultTextTest)
{
   wstr vmGuid = L"12345678";
   MsgBinary nonce;
   setMockNonce(nonce);

   VMOCK(&GoldenImageRegPrep::createPersistentKeyPair)
      .ExpectCall(StrEq(KEYPAR_PH_SIGN), VAL_KEY_LENGTH)
      .WillOnce(Return(true));

   VMOCK(&GoldenImageRegPrep::generateNonce)
      .ExpectCall(VAL_NONCE_SIZE, _)
      .WillOnce([&](wstr, MsgBinary &msgBinary) {
         setMockNonce(msgBinary);
         return true;
      });

   VMOCK(&GoldenImageRegPrep::getVmGuid)
      .ExpectCall(_)
      .WillOnce(DoAll(SetArgReferee<0>(vmGuid), Return(true)));

   MsgBinary pubKey, signature;
   wstr sigData;
   sigData.setBinary(nonce.pBinary, nonce.sBinary);
   sigData.append(vmGuid);

   VMOCK(&GoldenImageRegPrep::getPublicKey)
      .ExpectCall(StrEq(KEYPAR_PH_SIGN), sigData, _, _)
      .WillOnce(DoAll(SetArgReferee<2>(pubKey), SetArgReferee<3>(signature), Return(true)));

   PropertyBag request;
   setRequest(request, vmGuid, pubKey, nonce, signature);

   VMOCK(&GoldenImageRegPrep::connectAndSend)
      .ExpectCall(StrEq(BROKER_FQDN), StrEq(QUEUE_AGENT_PAIRING), StrEq(HINT_GI_REGISTRATION),
                  request, _)
      .WillOnce([](auto, auto, auto, auto, PropertyBag &response) {
         wstr someData = L"someData";
         response.addBinary(PAR_RESULT_SIG, someData.p_upd(), someData.s());
         return true;
      });

   GoldenImageRegPrep gi;
   EXPECT_FALSE(gi.registerGoldenImage(BROKER_FQDN));
}


/*
 *-----------------------------------------------------------------------------
 *
 * goldenImageRegPrepTest::mismatchedAckTest
 *
 *      Tests an attempt to create the golden image, but the acknowledgement
 *      text is mismatched.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GoldenImageRegPrepTest, mismatchedAckTest)
{
   wstr vmGuid = L"12345678";
   MsgBinary nonce;
   setMockNonce(nonce);

   VMOCK(&GoldenImageRegPrep::createPersistentKeyPair)
      .ExpectCall(StrEq(KEYPAR_PH_SIGN), VAL_KEY_LENGTH)
      .WillOnce(Return(true));

   VMOCK(&GoldenImageRegPrep::generateNonce)
      .ExpectCall(VAL_NONCE_SIZE, _)
      .WillOnce([&](wstr, MsgBinary &msgBinary) {
         setMockNonce(msgBinary);
         return true;
      });

   VMOCK(&GoldenImageRegPrep::getVmGuid)
      .ExpectCall(_)
      .WillOnce(DoAll(SetArgReferee<0>(vmGuid), Return(true)));

   MsgBinary pubKey, signature;
   wstr sigData;
   sigData.setBinary(nonce.pBinary, nonce.sBinary);
   sigData.append(vmGuid);

   VMOCK(&GoldenImageRegPrep::getPublicKey)
      .ExpectCall(StrEq(KEYPAR_PH_SIGN), sigData, _, _)
      .WillOnce(DoAll(SetArgReferee<2>(pubKey), SetArgReferee<3>(signature), Return(true)));

   PropertyBag request;
   setRequest(request, vmGuid, pubKey, nonce, signature);

   VMOCK(&GoldenImageRegPrep::connectAndSend)
      .ExpectCall(StrEq(BROKER_FQDN), StrEq(QUEUE_AGENT_PAIRING), StrEq(HINT_GI_REGISTRATION),
                  request, _)
      .WillOnce([](auto, auto, auto, auto, PropertyBag &response) {
         wstr someData = L"someData";
         response.addBinary(PAR_RESULT_SIG, someData.p_upd(), someData.s());
         response.add(PAR_RESULT_TEXT, L"Syn");
         return true;
      });

   GoldenImageRegPrep gi;
   EXPECT_FALSE(gi.registerGoldenImage(BROKER_FQDN));
}


/*
 *-----------------------------------------------------------------------------
 *
 * goldenImageRegPrepTest::mismatchedAckTest
 *
 *      Tests a successful attempt to create the golden image.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GoldenImageRegPrepTest, successTest)
{
   wstr vmGuid = L"12345678";
   MsgBinary nonce;
   setMockNonce(nonce);

   VMOCK(&GoldenImageRegPrep::createPersistentKeyPair)
      .ExpectCall(StrEq(KEYPAR_PH_SIGN), VAL_KEY_LENGTH)
      .WillOnce(Return(true));

   VMOCK(&GoldenImageRegPrep::generateNonce)
      .ExpectCall(VAL_NONCE_SIZE, _)
      .WillOnce([&](wstr, MsgBinary &msgBinary) {
         setMockNonce(msgBinary);
         return true;
      });

   VMOCK(&GoldenImageRegPrep::getVmGuid)
      .ExpectCall(_)
      .WillOnce(DoAll(SetArgReferee<0>(vmGuid), Return(true)));

   MsgBinary pubKey, signature;
   wstr sigData;
   sigData.setBinary(nonce.pBinary, nonce.sBinary);
   sigData.append(vmGuid);

   VMOCK(&GoldenImageRegPrep::getPublicKey)
      .ExpectCall(StrEq(KEYPAR_PH_SIGN), sigData, _, _)
      .WillOnce(DoAll(SetArgReferee<2>(pubKey), SetArgReferee<3>(signature), Return(true)));

   PropertyBag request;
   setRequest(request, vmGuid, pubKey, nonce, signature);

   VMOCK(&GoldenImageRegPrep::connectAndSend)
      .ExpectCall(StrEq(BROKER_FQDN), StrEq(QUEUE_AGENT_PAIRING), StrEq(HINT_GI_REGISTRATION),
                  request, _)
      .WillOnce([](auto, auto, auto, auto, PropertyBag &response) {
         wstr someData = L"someData";
         response.addBinary(PAR_RESULT_SIG, someData.p_upd(), someData.s());
         response.add(PAR_RESULT_TEXT, L"Ack");
         return true;
      });

   GoldenImageRegPrep gi;
   EXPECT_TRUE(gi.registerGoldenImage(BROKER_FQDN));
}
