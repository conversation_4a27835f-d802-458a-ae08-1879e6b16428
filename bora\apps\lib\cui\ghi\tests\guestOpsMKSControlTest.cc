/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * guestOpsMKSControlTest.cc -
 *
 *    Unit Test of ghi guestOpsMKSControl.cc
 */

#include "stdafx.h"
#include <gmock/gmock.h>
#include <gtest/gtest.h>
#include "common/tests/baseUnitTest.hh"
#include "cui/core/destroyNotifier.hh"
#include "ghi/guestOpsGuestExecInfo.hh"
#include "ghi/guestOpsUtil.hh"
#include "guestOpsMKSControlUTMock.hh"
#include "guestrpc/ghiDisplaysDPI.h"
#include "guestrpc/ghiGetBinaryHandlers.h"
#include "guestrpc/ghiGetExecInfoHash.h"
#include "guestrpc/ghiOverlayIcon.h"
#include "guestrpc/ghiSetFocusedWindow.h"
#include "guestrpc/ghiShellAction.h"
#include "guestrpc/ghiStartMenu.h"
#include "guestrpc/ghiTrayIcon.h"
#include "guestrpc/unity.h"
#include "imageUtil.h"
#include "mksUTMock.hh"
#include "vmUTMock.hh"
#include "rxUTLog.h"
#include "xdrutil.h"
#include "utMock.h"

using namespace crt::common;
using namespace crt::common::test;

using ::testing::_;
using ::testing::Invoke;
using ::testing::NiceMock;


class GHIGuestOpsMKSControlUnitTest : public BaseUnitTest {};


struct GuestWindowIconData {
   UnityWindowId windowID;
   UnityIconType iconType;
   UnityIconSize iconSize;
   cui::PNGData pngData;
   cui::GuestOpsMKSControl::GetGuestWindowIconDoneSlot doneSlot;
   cui::AbortSlot abortSlot;
};

struct GuestTrayIcon {
   cui::PNGData pngData;
   utf::string tooltip;
   utf::string blacklistKey;
};


namespace cui {

/*
 *-----------------------------------------------------------------------------
 *
 * GHIGuestOpsMKSControlUnitTest::TestGetCanUseShellLocationScheme --
 *
 *      Test GetCanUseShellLocationScheme method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GHIGuestOpsMKSControlUnitTest, TestGetCanUseShellLocationScheme)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();
   GuestOpsMKSControlUTMock *guestOps = dynamic_cast<GuestOpsMKSControlUTMock *>(vm->GetGuestOps());

   // Test canDoGuestShellAction capacity is false.
   std::string fakedMsg = "14=0 ";
   guestOps->OnGHIUpdateNotified(GHI_GUEST_CAP_FEATURES_VIEW_REMOTE, (const uint8 *)fakedMsg.data(),
                                 fakedMsg.size());
   EXPECT_FALSE(guestOps->canDoGuestShellAction);
   EXPECT_FALSE(guestOps->GetCanUseShellLocationScheme("x-horizon-share"));
   EXPECT_FALSE(guestOps->GetCanUseShellLocationScheme("http"));

   // Test canDoGuestShellAction capacity is true.
   fakedMsg = "14=1 ";
   guestOps->OnGHIUpdateNotified(GHI_GUEST_CAP_FEATURES_VIEW_REMOTE, (const uint8 *)fakedMsg.data(),
                                 fakedMsg.size());
   EXPECT_TRUE(guestOps->canDoGuestShellAction);
   EXPECT_FALSE(guestOps->GetCanUseShellLocationScheme("x-horizon-share"));
   EXPECT_TRUE(guestOps->GetCanUseShellLocationScheme("http"));

   delete vm;
}


static bool gAborted = false;

/*
 *-----------------------------------------------------------------------------
 *
 * FakedOnAbort --
 *
 *      Faked abort callback.
 *
 * Result:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

void
FakedOnAbort(bool cancelled, // IN
             cui::Error err) // IN
{
   gAborted = true;
}


/*
 *-----------------------------------------------------------------------------
 *
 * GHIGuestOpsMKSControlUnitTest::TestSetResolution --
 *
 *      Test SetResolution method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GHIGuestOpsMKSControlUnitTest, TestSetResolution)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();
   GuestOpsMKSControlUTMock *guestOps = dynamic_cast<GuestOpsMKSControlUTMock *>(vm->GetGuestOps());

   // Test invalid mksControlClient.
   guestOps->verboseLogging = true;
   gAborted = false;
   VMOCK(&mksctrl::MKSControlClientBase::SetResolution).Times(0);
   guestOps->SetResolution(800, 600, &FakedOnAbort);
   EXPECT_TRUE(gAborted);

   // Test valid mksControlClient.
   vm->GetMKS();
   bool match = false;
   VMOCK(&mksctrl::MKSControlClientBase::SetResolution)
      .WillOnce([&match](int width, int height, cui::AbortSlot onAbort, cui::DoneSlot onDone) {
         match = width == 800 && height == 600;
      });
   guestOps->SetResolution(800, 600, &FakedOnAbort);
   EXPECT_TRUE(match);

   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * GHIGuestOpsMKSControlUnitTest::TestSetDisplayTopology --
 *
 *      Test SetDisplayTopology method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GHIGuestOpsMKSControlUnitTest, TestSetDisplayTopology)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();
   GuestOpsMKSControlUTMock *guestOps = dynamic_cast<GuestOpsMKSControlUTMock *>(vm->GetGuestOps());
   std::vector<cui::Rect> rects = {cui::Rect::FromLTRB(0, 0, 800, 600),
                                   cui::Rect::FromLTRB(800, 0, 1920, 1080)};

   // Test invalid mksControlClient.
   guestOps->verboseLogging = true;
   gAborted = false;
   VMOCK(&mksctrl::MKSControlClientBase::SetDisplayTopology).Times(0);
   guestOps->SetDisplayTopology(rects, &FakedOnAbort);
   EXPECT_TRUE(gAborted);

   // Test valid mksControlClient.
   vm->GetMKS();
   bool match = false;
   VMOCK(&mksctrl::MKSControlClientBase::SetDisplayTopology)
      .WillOnce([&match, &rects](const std::vector<cui::Rect> &monitors, cui::AbortSlot onAbort,
                                 cui::DoneSlot onDone) {
         match = monitors.size() == rects.size();
         for (int i = 0; i < (int)rects.size() && match; i++) {
            match = monitors[i] == rects[i];
         }
      });
   guestOps->SetDisplayTopology(rects, &FakedOnAbort);
   EXPECT_TRUE(match);

   delete vm;
}


static utf::string gWindowPath = "";
static utf::string gExecPath = "";

/*
 *-----------------------------------------------------------------------------
 *
 * FakedGetUnityWindowPathDone --
 *
 *      Faked GetUnityWindowPathDone callback.
 *
 * Result:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

void
FakedGetUnityWindowPathDone(const utf::string &windowPath, // IN
                            const utf::string &execPath)   // IN
{
   gWindowPath = windowPath;
   gExecPath = execPath;
}


/*
 *-----------------------------------------------------------------------------
 *
 * GHIGuestOpsMKSControlUnitTest::TestGetUnityWindowPathAndItsCallback --
 *
 *      Test GetUnityWindowPath and OnGetUnityWindowPathResponse method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GHIGuestOpsMKSControlUnitTest, TestGetUnityWindowPathAndItsCallback)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();
   GuestOpsMKSControlUTMock *guestOps = dynamic_cast<GuestOpsMKSControlUTMock *>(vm->GetGuestOps());
   uint32 windowId = 123;

   // Test too short msgLen in response.
   gAborted = false;
   bool msgMatch = false;
   EXPECT_CALL(*guestOps, SendGHIRequestMock(_, _, _, _, _, _))
      .WillOnce(Invoke([&msgMatch](GHIChannelType channel, const char *msgName,
                                   const uint8 *msgData, uint32 msgDataLen, cui::AbortSlot onAbort,
                                   mksctrl::GHIResponseSlot onDone) {
         std::string msg = "123";
         msgMatch = channel == GHI_CHANNEL_TOOLS_USER &&
                    utf::string(msgName) == UNITY_RPC_GET_WINDOW_PATH &&
                    std::string((char *)msgData) == msg && msgDataLen == msg.size() + 1;
         onDone((const uint8 *)"", (uint32)0);
      }));
   guestOps->GetUnityWindowPath(windowId, &FakedGetUnityWindowPathDone, &FakedOnAbort);
   EXPECT_TRUE(msgMatch);
   testing::Mock::VerifyAndClearExpectations(guestOps);
   EXPECT_TRUE(gAborted);

   // Test too long msgLen in response.
   gAborted = false;
   msgMatch = false;
   EXPECT_CALL(*guestOps, SendGHIRequestMock(_, _, _, _, _, _))
      .WillOnce(Invoke([&msgMatch](GHIChannelType channel, const char *msgName,
                                   const uint8 *msgData, uint32 msgDataLen, cui::AbortSlot onAbort,
                                   mksctrl::GHIResponseSlot onDone) {
         std::string msg = "123";
         msgMatch = channel == GHI_CHANNEL_TOOLS_USER &&
                    utf::string(msgName) == UNITY_RPC_GET_WINDOW_PATH &&
                    std::string((char *)msgData) == msg && msgDataLen == msg.size() + 1;
         onDone((const uint8 *)"", (uint32)1024 * 24 + 1);
      }));
   guestOps->GetUnityWindowPath(windowId, &FakedGetUnityWindowPathDone, &FakedOnAbort);
   EXPECT_TRUE(msgMatch);
   testing::Mock::VerifyAndClearExpectations(guestOps);
   EXPECT_TRUE(gAborted);

   // Test empty msg buffer in response.
   gAborted = false;
   msgMatch = false;
   EXPECT_CALL(*guestOps, SendGHIRequestMock(_, _, _, _, _, _))
      .WillOnce(Invoke([&msgMatch](GHIChannelType channel, const char *msgName,
                                   const uint8 *msgData, uint32 msgDataLen, cui::AbortSlot onAbort,
                                   mksctrl::GHIResponseSlot onDone) {
         std::string msg = "123";
         msgMatch = channel == GHI_CHANNEL_TOOLS_USER &&
                    utf::string(msgName) == UNITY_RPC_GET_WINDOW_PATH &&
                    std::string((char *)msgData) == msg && msgDataLen == msg.size() + 1;
         onDone((const uint8 *)NULL, (uint32)1);
      }));
   guestOps->GetUnityWindowPath(windowId, &FakedGetUnityWindowPathDone, &FakedOnAbort);
   EXPECT_TRUE(msgMatch);
   testing::Mock::VerifyAndClearExpectations(guestOps);
   EXPECT_TRUE(gAborted);

   // Test invalid msg buffer in response.
   gAborted = false;
   msgMatch = false;
   EXPECT_CALL(*guestOps, SendGHIRequestMock(_, _, _, _, _, _))
      .WillOnce(Invoke([&msgMatch](GHIChannelType channel, const char *msgName,
                                   const uint8 *msgData, uint32 msgDataLen, cui::AbortSlot onAbort,
                                   mksctrl::GHIResponseSlot onDone) {
         std::string msg = "123";
         msgMatch = channel == GHI_CHANNEL_TOOLS_USER &&
                    utf::string(msgName) == UNITY_RPC_GET_WINDOW_PATH &&
                    std::string((char *)msgData) == msg && msgDataLen == msg.size() + 1;
         onDone((const uint8 *)"\0", (uint32)1);
      }));
   guestOps->GetUnityWindowPath(windowId, &FakedGetUnityWindowPathDone, &FakedOnAbort);
   EXPECT_TRUE(msgMatch);
   testing::Mock::VerifyAndClearExpectations(guestOps);
   EXPECT_TRUE(gAborted);

   // Test too long msg buffer in response.
   gAborted = false;
   msgMatch = false;
   VMOCK(strnlen).WillOnce(1024 * 24);
   EXPECT_CALL(*guestOps, SendGHIRequestMock(_, _, _, _, _, _))
      .WillOnce(Invoke([&msgMatch](GHIChannelType channel, const char *msgName,
                                   const uint8 *msgData, uint32 msgDataLen, cui::AbortSlot onAbort,
                                   mksctrl::GHIResponseSlot onDone) {
         std::string msg = "123";
         msgMatch = channel == GHI_CHANNEL_TOOLS_USER &&
                    utf::string(msgName) == UNITY_RPC_GET_WINDOW_PATH &&
                    std::string((char *)msgData) == msg && msgDataLen == msg.size() + 1;
         onDone((const uint8 *)"fakedWindowPath", (uint32)1);
      }));
   guestOps->GetUnityWindowPath(windowId, &FakedGetUnityWindowPathDone, &FakedOnAbort);
   EXPECT_TRUE(msgMatch);
   testing::Mock::VerifyAndClearExpectations(guestOps);
   EXPECT_TRUE(gAborted);

   // Test invalid UTF8 msg buffer in response.
   gAborted = false;
   msgMatch = false;
   VMOCK_V(mymock, strnlen).WillRepeatedly([&mymock](const char s[], size_t maxlen) {
      return mymock.CallRealFunc(s, maxlen);
   });
   EXPECT_CALL(*guestOps, SendGHIRequestMock(_, _, _, _, _, _))
      .WillOnce(Invoke([&msgMatch](GHIChannelType channel, const char *msgName,
                                   const uint8 *msgData, uint32 msgDataLen, cui::AbortSlot onAbort,
                                   mksctrl::GHIResponseSlot onDone) {
         std::string msg = "123";
         msgMatch = channel == GHI_CHANNEL_TOOLS_USER &&
                    utf::string(msgName) == UNITY_RPC_GET_WINDOW_PATH &&
                    std::string((char *)msgData) == msg && msgDataLen == msg.size() + 1;
         onDone((const uint8 *)"\xf8\xa1\xa1\xa1\xa1", (uint32)1);
      }));
   guestOps->GetUnityWindowPath(windowId, &FakedGetUnityWindowPathDone, &FakedOnAbort);
   EXPECT_TRUE(msgMatch);
   testing::Mock::VerifyAndClearExpectations(guestOps);
   EXPECT_TRUE(gAborted);

   // Test empty execPath in response.
   gAborted = false;
   msgMatch = false;
   std::string fakedWindowPath = "fakedWindowPath";
   gWindowPath = "111";
   gExecPath = "111";
   EXPECT_CALL(*guestOps, SendGHIRequestMock(_, _, _, _, _, _))
      .WillOnce(Invoke([&msgMatch, &fakedWindowPath](GHIChannelType channel, const char *msgName,
                                                     const uint8 *msgData, uint32 msgDataLen,
                                                     cui::AbortSlot onAbort,
                                                     mksctrl::GHIResponseSlot onDone) {
         std::string msg = "123";
         msgMatch = channel == GHI_CHANNEL_TOOLS_USER &&
                    utf::string(msgName) == UNITY_RPC_GET_WINDOW_PATH &&
                    std::string((char *)msgData) == msg && msgDataLen == msg.size() + 1;
         onDone((const uint8 *)fakedWindowPath.data(), (uint32)fakedWindowPath.size() + 1);
      }));
   guestOps->GetUnityWindowPath(windowId, &FakedGetUnityWindowPathDone, &FakedOnAbort);
   EXPECT_TRUE(msgMatch);
   testing::Mock::VerifyAndClearExpectations(guestOps);
   EXPECT_FALSE(gAborted);
   EXPECT_TRUE(gWindowPath == fakedWindowPath.c_str());
   EXPECT_TRUE(gExecPath == "");

   // Test valid execPath in response.
   gAborted = false;
   msgMatch = false;
   std::string fakedExecPath = "fakedExecPath";
   std::string fakedPaths = fakedWindowPath + " " + fakedExecPath + " ";
   std::vector<uint8> fakedPathsBuffer;
   fakedPathsBuffer.resize(fakedPaths.size());
   memcpy(fakedPathsBuffer.data(), fakedPaths.data(), fakedPaths.size());
   fakedPathsBuffer[fakedWindowPath.size()] = (uint8)'\0';
   fakedPathsBuffer[fakedPathsBuffer.size() - 1] = (uint8)'\0';
   gWindowPath = "111";
   gExecPath = "111";
   EXPECT_CALL(*guestOps, SendGHIRequestMock(_, _, _, _, _, _))
      .WillOnce(Invoke([&msgMatch, &fakedPathsBuffer,
                        &fakedPaths](GHIChannelType channel, const char *msgName,
                                     const uint8 *msgData, uint32 msgDataLen,
                                     cui::AbortSlot onAbort, mksctrl::GHIResponseSlot onDone) {
         std::string msg = "123";
         msgMatch = channel == GHI_CHANNEL_TOOLS_USER &&
                    utf::string(msgName) == UNITY_RPC_GET_WINDOW_PATH &&
                    std::string((char *)msgData) == msg && msgDataLen == msg.size() + 1;
         onDone((const uint8 *)fakedPathsBuffer.data(), (uint32)fakedPaths.size());
      }));
   guestOps->GetUnityWindowPath(windowId, &FakedGetUnityWindowPathDone, &FakedOnAbort);
   EXPECT_TRUE(msgMatch);
   testing::Mock::VerifyAndClearExpectations(guestOps);
   EXPECT_FALSE(gAborted);
   EXPECT_TRUE(gWindowPath == fakedWindowPath.c_str());
   EXPECT_TRUE(gExecPath == fakedExecPath.c_str());

   // Test invalid msgLen in response.
   gAborted = false;
   msgMatch = false;
   EXPECT_CALL(*guestOps, SendGHIRequestMock(_, _, _, _, _, _))
      .WillOnce(Invoke([&msgMatch, &fakedPathsBuffer](GHIChannelType channel, const char *msgName,
                                                      const uint8 *msgData, uint32 msgDataLen,
                                                      cui::AbortSlot onAbort,
                                                      mksctrl::GHIResponseSlot onDone) {
         std::string msg = "123";
         msgMatch = channel == GHI_CHANNEL_TOOLS_USER &&
                    utf::string(msgName) == UNITY_RPC_GET_WINDOW_PATH &&
                    std::string((char *)msgData) == msg && msgDataLen == msg.size() + 1;
         onDone((const uint8 *)fakedPathsBuffer.data(), (uint32)1);
      }));
   guestOps->GetUnityWindowPath(windowId, &FakedGetUnityWindowPathDone, &FakedOnAbort);
   EXPECT_TRUE(msgMatch);
   testing::Mock::VerifyAndClearExpectations(guestOps);
   EXPECT_TRUE(gAborted);

   delete vm;
}


static utf::string gIconDescription = "";
static cui::GuestApp::IconList gIcons = {};

/*
 *-----------------------------------------------------------------------------
 *
 * FakedGetGuestExecInfoDone --
 *
 *      Faked GetGuestExecInfoDone callback.
 *
 * Result:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

void
FakedGetGuestExecInfoDone(const utf::string &iconDescription,   // IN
                          const cui::GuestApp::IconList &icons) // IN
{
   gIconDescription = iconDescription;
   gIcons = icons;
}


/*
 *-----------------------------------------------------------------------------
 *
 * GHIGuestOpsMKSControlUnitTest::TestGetGuestExecInfoAndItsCallback --
 *
 *      Test GetGuestExecInfo and OnGetGuestExecInfoDone method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GHIGuestOpsMKSControlUnitTest, TestGetGuestExecInfoAndItsCallback)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();
   GuestOpsMKSControlUTMock *guestOps = dynamic_cast<GuestOpsMKSControlUTMock *>(vm->GetGuestOps());

   // Test empty path.
   gAborted = false;
   EXPECT_CALL(*guestOps, SendGHIRequestMock(_, _, _, _, _, _)).Times(0);
   guestOps->cui::GuestOpsMKSControl::GetGuestExecInfo("", &FakedGetGuestExecInfoDone,
                                                       &FakedOnAbort);
   testing::Mock::VerifyAndClearExpectations(guestOps);
   EXPECT_TRUE(gAborted);

   // Test parse binary failure with valid path.
   std::string fakedPath = "fakedPath";
   bool msgMatch = false;
   gAborted = false;
   EXPECT_CALL(*guestOps, SendGHIRequestMock(_, _, _, _, _, _))
      .WillRepeatedly(Invoke([&msgMatch, &fakedPath](GHIChannelType channel, const char *msgName,
                                                     const uint8 *msgData, uint32 msgDataLen,
                                                     cui::AbortSlot onAbort,
                                                     mksctrl::GHIResponseSlot onDone) {
         msgMatch = channel == GHI_CHANNEL_TOOLS_USER &&
                    utf::string(msgName) == UNITY_RPC_GET_BINARY_INFO &&
                    std::string((char *)msgData) == fakedPath && msgDataLen == fakedPath.size() + 1;
         onDone((const uint8 *)"", (uint32)0);
      }));
   VMOCK(ToolsGhi_ParseGetBinaryInfo).WillOnce(false);
   guestOps->cui::GuestOpsMKSControl::GetGuestExecInfo(fakedPath.c_str(),
                                                       &FakedGetGuestExecInfoDone, &FakedOnAbort);
   EXPECT_TRUE(msgMatch);
   EXPECT_TRUE(gAborted);

   // Test parsed binary has no description.
   msgMatch = false;
   gAborted = false;
   ToolsGhiIconContainer *fakedIconContainer = new ToolsGhiIconContainer();
   fakedIconContainer->description = NULL;
   VMOCK(ToolsGhi_ParseGetBinaryInfo)
      .WillOnce([&fakedIconContainer](const char *result, size_t resultLen,
                                      ToolsGhiIconContainer **iconContainer) {
         *iconContainer = fakedIconContainer;
         return true;
      });
   guestOps->cui::GuestOpsMKSControl::GetGuestExecInfo(fakedPath.c_str(),
                                                       &FakedGetGuestExecInfoDone, &FakedOnAbort);
   EXPECT_TRUE(msgMatch);
   EXPECT_TRUE(gAborted);

   // Test parsed binary has invalid description.
   msgMatch = false;
   gAborted = false;
   fakedIconContainer = new ToolsGhiIconContainer();
   fakedIconContainer->description = "123";
   VMOCK(ToolsGhi_ParseGetBinaryInfo)
      .WillRepeatedly([&fakedIconContainer](const char *result, size_t resultLen,
                                            ToolsGhiIconContainer **iconContainer) {
         *iconContainer = fakedIconContainer;
         return true;
      });
   VMOCK(cui::GuestOpsUtilCheckValidUTF8).WillOnce(false);
   guestOps->cui::GuestOpsMKSControl::GetGuestExecInfo(fakedPath.c_str(),
                                                       &FakedGetGuestExecInfoDone, &FakedOnAbort);
   EXPECT_TRUE(msgMatch);
   EXPECT_TRUE(gAborted);

   /*
    * Test one icon with invalid size, one icon with invalid buffer
    * and one valid icon.
    */
   VMOCK(cui::GuestOpsUtilCheckValidUTF8).WillRepeatedly(true);
   msgMatch = false;
   gAborted = false;
   fakedIconContainer = new ToolsGhiIconContainer();
   fakedIconContainer->description = "123";

   ToolsGhiIcon fakedInvalidSizeIcon;
   fakedInvalidSizeIcon.width = 2;
   fakedInvalidSizeIcon.height = 2;
   fakedInvalidSizeIcon.bgraSize = 4;
   fakedInvalidSizeIcon.bgraData = NULL;
   fakedIconContainer->icons.push_back(fakedInvalidSizeIcon);

   ToolsGhiIcon fakedInvalidBufferIcon;
   fakedInvalidBufferIcon.width = 2;
   fakedInvalidBufferIcon.height = 2;
   fakedInvalidBufferIcon.bgraSize = 16;
   fakedInvalidBufferIcon.bgraData = (const uint8 *)0x123;
   fakedIconContainer->icons.push_back(fakedInvalidBufferIcon);

   ToolsGhiIcon fakedValidIcon;
   std::vector<uint8> fakedBuffer = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1,
                                     1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0};
   fakedValidIcon.width = 3;
   fakedValidIcon.height = 3;
   fakedValidIcon.bgraSize = 36;
   fakedValidIcon.bgraData = (const uint8 *)fakedBuffer.data();
   fakedIconContainer->icons.push_back(fakedValidIcon);

   VMOCK(DynBuf_Destroy).WillRepeatedly([](DynBuf *b) { b->data = NULL; });
   VMOCK(ImageUtil_ConstructPNGBuffer)
      .WillRepeatedly([&fakedInvalidBufferIcon, &fakedBuffer](const ImageInfo *image,
                                                              const ImagePngWriteOptions *options,
                                                              DynBuf *imageData) {
         if (image->data != const_cast<uint8 *>(fakedInvalidBufferIcon.bgraData)) {
            imageData->data = (char *)fakedBuffer.data();
            imageData->size = fakedBuffer.size();
         }
         return image->data != const_cast<uint8 *>(fakedInvalidBufferIcon.bgraData);
      });
   guestOps->cui::GuestOpsMKSControl::GetGuestExecInfo(fakedPath.c_str(),
                                                       &FakedGetGuestExecInfoDone, &FakedOnAbort);
   EXPECT_TRUE(msgMatch);
   EXPECT_FALSE(gAborted);
   EXPECT_TRUE(gIconDescription == "123");
   EXPECT_TRUE(gIcons.size() == 1 && gIcons.front().width == 3 && gIcons.front().height == 3);

   testing::Mock::VerifyAndClearExpectations(guestOps);
   delete vm;
}


static cui::GuestApp::FileHandlerList gFileHandlers = {};
static cui::GuestApp::URLHandlerList gUrlHandlers = {};

/*
 *-----------------------------------------------------------------------------
 *
 * FakedOnGetGuestExecHandlersDone --
 *
 *      Faked OnGetGuestExecHandlersDone callback.
 *
 * Result:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

void
FakedOnGetGuestExecHandlersDone(const cui::GuestApp::FileHandlerList &fileHandlers, // IN
                                const cui::GuestApp::URLHandlerList &urlHandlers)   // IN
{
   gFileHandlers = fileHandlers;
   gUrlHandlers = urlHandlers;
}


/*
 *-----------------------------------------------------------------------------
 *
 * GHIGuestOpsMKSControlUnitTest::TestGetGuestExecHandlersAndItsCallback --
 *
 *      Test GetGuestExecHandlers and OnGetGuestExecHandlersDone method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GHIGuestOpsMKSControlUnitTest, TestGetGuestExecHandlersAndItsCallback)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();
   GuestOpsMKSControlUTMock *guestOps = dynamic_cast<GuestOpsMKSControlUTMock *>(vm->GetGuestOps());

   // Test empty path.
   gAborted = false;
   EXPECT_CALL(*guestOps, SendGHIRequestMock(_, _, _, _, _, _)).Times(0);
   guestOps->GetGuestExecHandlers("", &FakedOnGetGuestExecHandlersDone, &FakedOnAbort);
   testing::Mock::VerifyAndClearExpectations(guestOps);
   EXPECT_TRUE(gAborted);

   // Test invalid GHIBinaryHandlers structure.
   std::string fakedPath = "fakedPath";
   bool msgMatch = false;
   gAborted = false;
   EXPECT_CALL(*guestOps, SendGHIRequestMock(_, _, _, _, _, _))
      .WillRepeatedly(Invoke([&msgMatch, &fakedPath](GHIChannelType channel, const char *msgName,
                                                     const uint8 *msgData, uint32 msgDataLen,
                                                     cui::AbortSlot onAbort,
                                                     mksctrl::GHIResponseSlot onDone) {
         msgMatch = channel == GHI_CHANNEL_TOOLS_USER &&
                    utf::string(msgName) == UNITY_RPC_GET_BINARY_HANDLERS &&
                    std::string((char *)msgData) == fakedPath && msgDataLen == fakedPath.size() + 1;
         onDone((const uint8 *)"", (uint32)0);
      }));
   VMOCK(XdrUtil_Deserialize).WillOnce(false);
   guestOps->GetGuestExecHandlers(fakedPath.c_str(), &FakedOnGetGuestExecHandlersDone,
                                  &FakedOnAbort);
   EXPECT_TRUE(msgMatch);
   EXPECT_TRUE(gAborted);

   // Test null GHIBinaryHandlersList.
   msgMatch = false;
   gAborted = false;
   VMOCK(XdrUtil_Deserialize)
      .WillOnce([](const void *data, size_t dataLen, void *xdrProc, void *dest) {
         GHIBinaryHandlers *handlerInfo = (GHIBinaryHandlers *)dest;
         handlerInfo->GHIBinaryHandlers_u.handlersV1 = NULL;
         return true;
      });
   guestOps->GetGuestExecHandlers(fakedPath.c_str(), &FakedOnGetGuestExecHandlersDone,
                                  &FakedOnAbort);
   EXPECT_TRUE(msgMatch);
   EXPECT_TRUE(gAborted);

   // Test null handler item.
   msgMatch = false;
   gAborted = false;
   GHIBinaryHandlersList fakedHandlersList;
   VMOCK(XdrUtil_Deserialize)
      .WillOnce([&fakedHandlersList](const void *data, size_t dataLen, void *xdrProc, void *dest) {
         GHIBinaryHandlers *handlerInfo = (GHIBinaryHandlers *)dest;
         fakedHandlersList.handlers.handlers_len = 1;
         fakedHandlersList.handlers.handlers_val = NULL;
         handlerInfo->GHIBinaryHandlers_u.handlersV1 = &fakedHandlersList;
         return true;
      });
   guestOps->GetGuestExecHandlers(fakedPath.c_str(), &FakedOnGetGuestExecHandlersDone,
                                  &FakedOnAbort);
   EXPECT_TRUE(msgMatch);
   EXPECT_FALSE(gAborted);
   EXPECT_TRUE(gFileHandlers.empty());
   EXPECT_TRUE(gUrlHandlers.empty());

   // Test invalid handler suffix for file handler.
   msgMatch = false;
   gAborted = false;
   GHIBinaryHandlersDetails fakedDetails;
   VMOCK(XdrUtil_Deserialize)
      .WillOnce([&fakedHandlersList, &fakedDetails](const void *data, size_t dataLen, void *xdrProc,
                                                    void *dest) {
         GHIBinaryHandlers *handlerInfo = (GHIBinaryHandlers *)dest;
         fakedDetails.suffix = (char *)"\xf8\xa1\xa1\xa1\xa1";
         fakedHandlersList.handlers.handlers_len = 1;
         fakedHandlersList.handlers.handlers_val = &fakedDetails;
         handlerInfo->GHIBinaryHandlers_u.handlersV1 = &fakedHandlersList;
         return true;
      });
   guestOps->GetGuestExecHandlers(fakedPath.c_str(), &FakedOnGetGuestExecHandlersDone,
                                  &FakedOnAbort);
   EXPECT_TRUE(msgMatch);
   EXPECT_FALSE(gAborted);
   EXPECT_TRUE(gFileHandlers.empty());
   EXPECT_TRUE(gUrlHandlers.empty());

   // Test invalid handler type for file handler.
   msgMatch = false;
   gAborted = false;
   VMOCK(XdrUtil_Deserialize)
      .WillOnce([&fakedHandlersList, &fakedDetails](const void *data, size_t dataLen, void *xdrProc,
                                                    void *dest) {
         GHIBinaryHandlers *handlerInfo = (GHIBinaryHandlers *)dest;
         fakedDetails.suffix = (char *)"";
         fakedHandlersList.handlers.handlers_len = 1;
         fakedHandlersList.handlers.handlers_val = &fakedDetails;
         handlerInfo->GHIBinaryHandlers_u.handlersV1 = &fakedHandlersList;
         return true;
      });
   guestOps->GetGuestExecHandlers(fakedPath.c_str(), &FakedOnGetGuestExecHandlersDone,
                                  &FakedOnAbort);
   EXPECT_TRUE(msgMatch);
   EXPECT_FALSE(gAborted);
   EXPECT_TRUE(gFileHandlers.empty());
   EXPECT_TRUE(gUrlHandlers.empty());

   // Test invalid mimetype for file handler.
   msgMatch = false;
   gAborted = false;
   VMOCK(XdrUtil_Deserialize)
      .WillOnce([&fakedHandlersList, &fakedDetails](const void *data, size_t dataLen, void *xdrProc,
                                                    void *dest) {
         GHIBinaryHandlers *handlerInfo = (GHIBinaryHandlers *)dest;
         fakedDetails.suffix = (char *)".exe";
         fakedDetails.mimetype = (char *)"\xf8\xa1\xa1\xa1\xa1";
         fakedHandlersList.handlers.handlers_len = 1;
         fakedHandlersList.handlers.handlers_val = &fakedDetails;
         handlerInfo->GHIBinaryHandlers_u.handlersV1 = &fakedHandlersList;
         return true;
      });
   guestOps->GetGuestExecHandlers(fakedPath.c_str(), &FakedOnGetGuestExecHandlersDone,
                                  &FakedOnAbort);
   EXPECT_TRUE(msgMatch);
   EXPECT_FALSE(gAborted);
   EXPECT_TRUE(gFileHandlers.empty());
   EXPECT_TRUE(gUrlHandlers.empty());

   // Test invalid UTI for file handler.
   msgMatch = false;
   gAborted = false;
   VMOCK(XdrUtil_Deserialize)
      .WillOnce([&fakedHandlersList, &fakedDetails](const void *data, size_t dataLen, void *xdrProc,
                                                    void *dest) {
         GHIBinaryHandlers *handlerInfo = (GHIBinaryHandlers *)dest;
         fakedDetails.suffix = (char *)".exe";
         fakedDetails.mimetype = (char *)"csv";
         fakedDetails.UTI = (char *)"\xf8\xa1\xa1\xa1\xa1";
         fakedHandlersList.handlers.handlers_len = 1;
         fakedHandlersList.handlers.handlers_val = &fakedDetails;
         handlerInfo->GHIBinaryHandlers_u.handlersV1 = &fakedHandlersList;
         return true;
      });
   guestOps->GetGuestExecHandlers(fakedPath.c_str(), &FakedOnGetGuestExecHandlersDone,
                                  &FakedOnAbort);
   EXPECT_TRUE(msgMatch);
   EXPECT_FALSE(gAborted);
   EXPECT_TRUE(gFileHandlers.empty());
   EXPECT_TRUE(gUrlHandlers.empty());

   // Test invalid friendlyName for file handler.
   msgMatch = false;
   gAborted = false;
   VMOCK(XdrUtil_Deserialize)
      .WillOnce([&fakedHandlersList, &fakedDetails](const void *data, size_t dataLen, void *xdrProc,
                                                    void *dest) {
         GHIBinaryHandlers *handlerInfo = (GHIBinaryHandlers *)dest;
         fakedDetails.suffix = (char *)".exe";
         fakedDetails.mimetype = (char *)"csv";
         fakedDetails.UTI = (char *)"UTI";
         fakedDetails.friendlyName = (char *)"\xf8\xa1\xa1\xa1\xa1";
         fakedHandlersList.handlers.handlers_len = 1;
         fakedHandlersList.handlers.handlers_val = &fakedDetails;
         handlerInfo->GHIBinaryHandlers_u.handlersV1 = &fakedHandlersList;
         return true;
      });
   guestOps->GetGuestExecHandlers(fakedPath.c_str(), &FakedOnGetGuestExecHandlersDone,
                                  &FakedOnAbort);
   EXPECT_TRUE(msgMatch);
   EXPECT_FALSE(gAborted);
   EXPECT_TRUE(gFileHandlers.empty());
   EXPECT_TRUE(gUrlHandlers.empty());

   // Test valid fileHandler.
   GHIBinaryHandlersActionURIPair fakedUrlPair;
   msgMatch = false;
   gAborted = false;
   VMOCK(XdrUtil_Deserialize)
      .WillOnce([&fakedHandlersList, &fakedDetails, &fakedUrlPair](const void *data, size_t dataLen,
                                                                   void *xdrProc, void *dest) {
         GHIBinaryHandlers *handlerInfo = (GHIBinaryHandlers *)dest;
         fakedDetails.suffix = (char *)".exe";
         fakedDetails.mimetype = (char *)"csv";
         fakedDetails.UTI = (char *)"UTI";
         fakedDetails.friendlyName = (char *)"friendlyName";
         fakedDetails.actionURIs.actionURIs_len = 1;
         fakedUrlPair.verb = (char *)"verb";
         fakedUrlPair.actionURI = (char *)"x-horizon-action:///run";
         fakedDetails.actionURIs.actionURIs_val = &fakedUrlPair;
         fakedHandlersList.handlers.handlers_len = 1;
         fakedHandlersList.handlers.handlers_val = &fakedDetails;
         handlerInfo->GHIBinaryHandlers_u.handlersV1 = &fakedHandlersList;
         return true;
      });
   guestOps->GetGuestExecHandlers(fakedPath.c_str(), &FakedOnGetGuestExecHandlersDone,
                                  &FakedOnAbort);
   EXPECT_TRUE(msgMatch);
   EXPECT_FALSE(gAborted);
   EXPECT_TRUE(gFileHandlers.size() == 1 && gFileHandlers.front().handlerType == "exe" &&
               gFileHandlers.front().mimeType == "csv" && gFileHandlers.front().uti == "UTI" &&
               gFileHandlers.front().humanReadableName == "friendlyName");
   EXPECT_TRUE(gUrlHandlers.empty());

   // Test invalid URL value for url handler.
   gFileHandlers.clear();
   msgMatch = false;
   gAborted = false;
   VMOCK(XdrUtil_Deserialize)
      .WillOnce([&fakedHandlersList, &fakedDetails](const void *data, size_t dataLen, void *xdrProc,
                                                    void *dest) {
         GHIBinaryHandlers *handlerInfo = (GHIBinaryHandlers *)dest;
         fakedDetails.suffix = (char *)"exe";
         fakedDetails.mimetype = (char *)"csv";
         fakedDetails.UTI = (char *)"UTI";
         fakedDetails.friendlyName = (char *)"friendlyName";
         fakedDetails.actionURIs.actionURIs_len = 1;
         fakedDetails.actionURIs.actionURIs_val = NULL;
         fakedHandlersList.handlers.handlers_len = 1;
         fakedHandlersList.handlers.handlers_val = &fakedDetails;
         handlerInfo->GHIBinaryHandlers_u.handlersV1 = &fakedHandlersList;
         return true;
      });
   guestOps->GetGuestExecHandlers(fakedPath.c_str(), &FakedOnGetGuestExecHandlersDone,
                                  &FakedOnAbort);
   EXPECT_TRUE(msgMatch);
   EXPECT_FALSE(gAborted);
   EXPECT_TRUE(gFileHandlers.empty());
   EXPECT_TRUE(gUrlHandlers.size() == 1 && gUrlHandlers.front().handlerType == "exe" &&
               gUrlHandlers.front().humanReadableName == "friendlyName" &&
               gUrlHandlers.front().actionList.empty());

   // Test invalid verb for url handler.
   gUrlHandlers.clear();
   msgMatch = false;
   gAborted = false;
   VMOCK(XdrUtil_Deserialize)
      .WillOnce([&fakedHandlersList, &fakedDetails, &fakedUrlPair](const void *data, size_t dataLen,
                                                                   void *xdrProc, void *dest) {
         GHIBinaryHandlers *handlerInfo = (GHIBinaryHandlers *)dest;
         fakedDetails.suffix = (char *)"exe";
         fakedDetails.mimetype = (char *)"csv";
         fakedDetails.UTI = (char *)"UTI";
         fakedDetails.friendlyName = (char *)"friendlyName";
         fakedDetails.actionURIs.actionURIs_len = 1;
         fakedUrlPair.verb = (char *)"\xf8\xa1\xa1\xa1\xa1";
         fakedDetails.actionURIs.actionURIs_val = &fakedUrlPair;
         fakedHandlersList.handlers.handlers_len = 1;
         fakedHandlersList.handlers.handlers_val = &fakedDetails;
         handlerInfo->GHIBinaryHandlers_u.handlersV1 = &fakedHandlersList;
         return true;
      });
   guestOps->GetGuestExecHandlers(fakedPath.c_str(), &FakedOnGetGuestExecHandlersDone,
                                  &FakedOnAbort);
   EXPECT_TRUE(msgMatch);
   EXPECT_FALSE(gAborted);
   EXPECT_TRUE(gFileHandlers.empty());
   EXPECT_TRUE(gUrlHandlers.size() == 1 && gUrlHandlers.front().handlerType == "exe" &&
               gUrlHandlers.front().humanReadableName == "friendlyName" &&
               gUrlHandlers.front().actionList.empty());

   // Test invalid actionURI for url handler.
   gUrlHandlers.clear();
   msgMatch = false;
   gAborted = false;
   VMOCK(XdrUtil_Deserialize)
      .WillOnce([&fakedHandlersList, &fakedDetails, &fakedUrlPair](const void *data, size_t dataLen,
                                                                   void *xdrProc, void *dest) {
         GHIBinaryHandlers *handlerInfo = (GHIBinaryHandlers *)dest;
         fakedDetails.suffix = (char *)"exe";
         fakedDetails.mimetype = (char *)"csv";
         fakedDetails.UTI = (char *)"UTI";
         fakedDetails.friendlyName = (char *)"friendlyName";
         fakedDetails.actionURIs.actionURIs_len = 1;
         fakedUrlPair.verb = (char *)"verb";
         fakedUrlPair.actionURI = (char *)"\xf8\xa1\xa1\xa1\xa1";
         fakedDetails.actionURIs.actionURIs_val = &fakedUrlPair;
         fakedHandlersList.handlers.handlers_len = 1;
         fakedHandlersList.handlers.handlers_val = &fakedDetails;
         handlerInfo->GHIBinaryHandlers_u.handlersV1 = &fakedHandlersList;
         return true;
      });
   guestOps->GetGuestExecHandlers(fakedPath.c_str(), &FakedOnGetGuestExecHandlersDone,
                                  &FakedOnAbort);
   EXPECT_TRUE(msgMatch);
   EXPECT_FALSE(gAborted);
   EXPECT_TRUE(gFileHandlers.empty());
   EXPECT_TRUE(gUrlHandlers.size() == 1 && gUrlHandlers.front().handlerType == "exe" &&
               gUrlHandlers.front().humanReadableName == "friendlyName" &&
               gUrlHandlers.front().actionList.empty());

   // Test valid url handler.
   gUrlHandlers.clear();
   msgMatch = false;
   gAborted = false;
   VMOCK(XdrUtil_Deserialize)
      .WillOnce([&fakedHandlersList, &fakedDetails, &fakedUrlPair](const void *data, size_t dataLen,
                                                                   void *xdrProc, void *dest) {
         GHIBinaryHandlers *handlerInfo = (GHIBinaryHandlers *)dest;
         fakedDetails.suffix = (char *)"exe";
         fakedDetails.mimetype = (char *)"csv";
         fakedDetails.UTI = (char *)"UTI";
         fakedDetails.friendlyName = (char *)"friendlyName";
         fakedDetails.actionURIs.actionURIs_len = 1;
         fakedUrlPair.verb = (char *)"verb";
         fakedUrlPair.actionURI = (char *)"x-horizon-action:///run";
         fakedDetails.actionURIs.actionURIs_val = &fakedUrlPair;
         fakedHandlersList.handlers.handlers_len = 1;
         fakedHandlersList.handlers.handlers_val = &fakedDetails;
         handlerInfo->GHIBinaryHandlers_u.handlersV1 = &fakedHandlersList;
         return true;
      });
   guestOps->GetGuestExecHandlers(fakedPath.c_str(), &FakedOnGetGuestExecHandlersDone,
                                  &FakedOnAbort);
   EXPECT_TRUE(msgMatch);
   EXPECT_FALSE(gAborted);
   EXPECT_TRUE(gFileHandlers.empty());
   EXPECT_TRUE(gUrlHandlers.size() == 1 && gUrlHandlers.front().handlerType == "exe" &&
               gUrlHandlers.front().humanReadableName == "friendlyName" &&
               gUrlHandlers.front().actionList.size() == 1 &&
               gUrlHandlers.front().actionList.front().actionURI == "verb" &&
               gUrlHandlers.front().actionList.front().targetURI == "x-horizon-action:///run");

   testing::Mock::VerifyAndClearExpectations(guestOps);
   delete vm;
}


static utf::string gExecInfoHash = "";

/*
 *-----------------------------------------------------------------------------
 *
 * FakedOnGetExecInfoHashDone --
 *
 *      Faked OnGetExecInfoHashDone callback.
 *
 * Result:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

void
FakedOnGetExecInfoHashDone(const utf::string &execInfoHash) // IN
{
   gExecInfoHash = execInfoHash;
}


/*
 *-----------------------------------------------------------------------------
 *
 * GHIGuestOpsMKSControlUnitTest::TestGetExecInfoHashAndItsCallback --
 *
 *      Test GetExecInfoHash and OnGetExecInfoHashDone method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GHIGuestOpsMKSControlUnitTest, TestGetExecInfoHashAndItsCallback)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();
   GuestOpsMKSControlUTMock *guestOps = dynamic_cast<GuestOpsMKSControlUTMock *>(vm->GetGuestOps());

   // Test empty path.
   gAborted = false;
   EXPECT_CALL(*guestOps, SendGHIRequestMock(_, _, _, _, _, _)).Times(0);
   guestOps->GetExecInfoHash("", &FakedOnGetExecInfoHashDone, &FakedOnAbort);
   testing::Mock::VerifyAndClearExpectations(guestOps);
   EXPECT_TRUE(gAborted);
   EXPECT_TRUE(gExecInfoHash.empty());

   // Test invalid GHIGetExecInfoHashReply structure.
   bool msgMatch = false;
   gAborted = false;
   EXPECT_CALL(*guestOps, SendGHIRequestMock(_, _, _, _, _, _))
      .WillRepeatedly(Invoke([&msgMatch](GHIChannelType channel, const char *msgName,
                                         const uint8 *msgData, uint32 msgDataLen,
                                         cui::AbortSlot onAbort, mksctrl::GHIResponseSlot onDone) {
         msgMatch =
            channel == GHI_CHANNEL_TOOLS_USER && utf::string(msgName) == GHI_RPC_GET_EXEC_INFO_HASH;
         onDone((const uint8 *)"", (uint32)0);
      }));
   VMOCK(XdrUtil_Deserialize).WillOnce(false);
   guestOps->GetExecInfoHash("fakedPath", &FakedOnGetExecInfoHashDone, &FakedOnAbort);
   EXPECT_TRUE(msgMatch);
   EXPECT_TRUE(gAborted);
   EXPECT_TRUE(gExecInfoHash.empty());

   // Test invalid GHIGetExecInfoHashReplyV1 pointer.
   msgMatch = false;
   gAborted = false;
   VMOCK(XdrUtil_Deserialize)
      .WillOnce([](const void *data, size_t dataLen, void *xdrProc, void *dest) {
         GHIGetExecInfoHashReply *replyMsg = (GHIGetExecInfoHashReply *)dest;
         replyMsg->GHIGetExecInfoHashReply_u.replyV1 = NULL;
         return true;
      });
   guestOps->GetExecInfoHash("fakedPath", &FakedOnGetExecInfoHashDone, &FakedOnAbort);
   EXPECT_TRUE(msgMatch);
   EXPECT_TRUE(gAborted);
   EXPECT_TRUE(gExecInfoHash.empty());

   // Test invalid GHIGetExecInfoHashReplyV1 pointer.
   msgMatch = false;
   gAborted = false;
   VMOCK(XdrUtil_Deserialize)
      .WillOnce([](const void *data, size_t dataLen, void *xdrProc, void *dest) {
         GHIGetExecInfoHashReply *replyMsg = (GHIGetExecInfoHashReply *)dest;
         replyMsg->GHIGetExecInfoHashReply_u.replyV1 = NULL;
         return true;
      });
   guestOps->GetExecInfoHash("fakedPath", &FakedOnGetExecInfoHashDone, &FakedOnAbort);
   EXPECT_TRUE(msgMatch);
   EXPECT_TRUE(gAborted);
   EXPECT_TRUE(gExecInfoHash.empty());

   // Test invalid execHash.
   msgMatch = false;
   gAborted = false;
   GHIGetExecInfoHashReplyV1 fakedReplay;
   VMOCK(XdrUtil_Deserialize)
      .WillOnce([&fakedReplay](const void *data, size_t dataLen, void *xdrProc, void *dest) {
         GHIGetExecInfoHashReply *replyMsg = (GHIGetExecInfoHashReply *)dest;
         fakedReplay.execHash = (char *)"\xf8\xa1\xa1\xa1\xa1";
         replyMsg->GHIGetExecInfoHashReply_u.replyV1 = &fakedReplay;
         return true;
      });
   guestOps->GetExecInfoHash("fakedPath", &FakedOnGetExecInfoHashDone, &FakedOnAbort);
   EXPECT_TRUE(msgMatch);
   EXPECT_TRUE(gAborted);
   EXPECT_TRUE(gExecInfoHash.empty());

   // Test valid execHash.
   msgMatch = false;
   gAborted = false;
   VMOCK(XdrUtil_Deserialize)
      .WillOnce([&fakedReplay](const void *data, size_t dataLen, void *xdrProc, void *dest) {
         GHIGetExecInfoHashReply *replyMsg = (GHIGetExecInfoHashReply *)dest;
         fakedReplay.execHash = (char *)"fakedHash";
         replyMsg->GHIGetExecInfoHashReply_u.replyV1 = &fakedReplay;
         return true;
      });
   guestOps->GetExecInfoHash("fakedPath", &FakedOnGetExecInfoHashDone, &FakedOnAbort);
   EXPECT_TRUE(msgMatch);
   EXPECT_FALSE(gAborted);
   EXPECT_TRUE(gExecInfoHash == "fakedHash");

   testing::Mock::VerifyAndClearExpectations(guestOps);
   delete vm;
}


static cui::PNGData gPngData = {};

/*
 *-----------------------------------------------------------------------------
 *
 * FakedGetGuestWindowIconDone --
 *
 *      Faked GetGuestWindowIconDone callback.
 *
 * Result:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

void
FakedGetGuestWindowIconDone(const cui::PNGData &pngData) // IN
{
   gPngData = pngData;
}


/*
 *-----------------------------------------------------------------------------
 *
 * GHIGuestOpsMKSControlUnitTest::TestGetGuestWindowIconAndItsCallbacks --
 *
 *      Test GetGuestWindowIcon, GetGuestWindowIconChunk,
 *      OnGetGuestWindowIconChunkDone and OnGetGuestWindowIconChunkAbort method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GHIGuestOpsMKSControlUnitTest, TestGetGuestWindowIconAndItsCallbacks)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();
   GuestOpsMKSControlUTMock *guestOps = dynamic_cast<GuestOpsMKSControlUTMock *>(vm->GetGuestOps());

   // Test invalid icon chunk in response.
   bool msgMatch = false;
   gAborted = false;
   UnityWindowId fakedWindowID = 123;
   UnityIconType fakedIconType = UNITY_ICON_TYPE_MAIN;
   UnityIconSize fakedIconSize = 4;
   GuestWindowIconData fakedIconData;
   // expectedMsg: WindowId IconType newReceivedIconSize currentIconDataSize IconChunkMaxSize.
   std::string expectedMsg = "123 0 4 0 32768";
   EXPECT_CALL(*guestOps, SendGHIRequestMock(_, _, _, _, _, _))
      .WillRepeatedly(
         Invoke([&msgMatch, &expectedMsg](GHIChannelType channel, const char *msgName,
                                          const uint8 *msgData, uint32 msgDataLen,
                                          cui::AbortSlot onAbort, mksctrl::GHIResponseSlot onDone) {
            std::string actualMsg = (char *)msgData;
            msgMatch = channel == GHI_CHANNEL_TOOLS_USER &&
                       utf::string(msgName) == UNITY_RPC_GET_ICON_DATA &&
                       expectedMsg == (char *)msgData && msgDataLen == expectedMsg.size() + 1;
            onDone((const uint8 *)"", (uint32)0);
         }));
   guestOps->GetGuestWindowIcon(fakedWindowID, fakedIconType, fakedIconSize,
                                &FakedGetGuestWindowIconDone, &FakedOnAbort);
   EXPECT_TRUE(msgMatch);
   EXPECT_TRUE(gAborted);

   // Test valid icon chunk in response.
   msgMatch = false;
   gAborted = false;
   fakedIconSize = 8;
   // ExpectedMsg: WindowId IconType newReceivedIconSize currentIconDataSize IconChunkMaxSize.
   std::string firstExpectedMsg = "123 0 8 0 32768";
   std::string secondExpectedMsg = "123 0 8 4 32768";
   // fakedResponse: fullPngLen chunkLen chunk
   std::vector<uint8> fakedFirstResponse = {'8', ' ', '4', ' ', '1', '1', '1', '1'};
   std::vector<uint8> fakedSecondResponse = {'8', ' ', '4', ' ', '0', '0', '0', '0'};
   int chunkIndex = 0;
   EXPECT_CALL(*guestOps, SendGHIRequestMock(_, _, _, _, _, _))
      .WillRepeatedly(Invoke([&msgMatch, &firstExpectedMsg, &secondExpectedMsg, &fakedFirstResponse,
                              &fakedSecondResponse, &chunkIndex](
                                GHIChannelType channel, const char *msgName, const uint8 *msgData,
                                uint32 msgDataLen, cui::AbortSlot onAbort,
                                mksctrl::GHIResponseSlot onDone) {
         msgMatch =
            channel == GHI_CHANNEL_TOOLS_USER && utf::string(msgName) == UNITY_RPC_GET_ICON_DATA;
         if (msgMatch) {
            if (chunkIndex == 0) {
               chunkIndex++;
               msgMatch =
                  firstExpectedMsg == (char *)msgData && msgDataLen == firstExpectedMsg.size() + 1;
               onDone((const uint8 *)fakedFirstResponse.data(), (uint32)fakedFirstResponse.size());
            } else if (chunkIndex == 1) {
               chunkIndex++;
               msgMatch = secondExpectedMsg == (char *)msgData &&
                          msgDataLen == secondExpectedMsg.size() + 1;
               onDone((const uint8 *)fakedSecondResponse.data(),
                      (uint32)fakedSecondResponse.size());
            } else {
               msgMatch = false;
            }
         }
      }));
   guestOps->GetGuestWindowIcon(fakedWindowID, fakedIconType, fakedIconSize,
                                &FakedGetGuestWindowIconDone, &FakedOnAbort);
   EXPECT_TRUE(msgMatch);
   EXPECT_FALSE(gAborted);
   EXPECT_TRUE(gPngData.size() == 8);

   // Test abort in response.
   msgMatch = false;
   gAborted = false;
   gPngData.clear();
   EXPECT_CALL(*guestOps, SendGHIRequestMock(_, _, _, _, _, _))
      .WillRepeatedly(Invoke(
         [&msgMatch, &firstExpectedMsg](GHIChannelType channel, const char *msgName,
                                        const uint8 *msgData, uint32 msgDataLen,
                                        cui::AbortSlot onAbort, mksctrl::GHIResponseSlot onDone) {
            msgMatch = channel == GHI_CHANNEL_TOOLS_USER &&
                       utf::string(msgName) == UNITY_RPC_GET_ICON_DATA &&
                       firstExpectedMsg == (char *)msgData &&
                       msgDataLen == firstExpectedMsg.size() + 1;
            onAbort(false, cui::Error("Faked abort error."));
         }));
   guestOps->GetGuestWindowIcon(fakedWindowID, fakedIconType, fakedIconSize,
                                &FakedGetGuestWindowIconDone, &FakedOnAbort);
   EXPECT_TRUE(msgMatch);
   EXPECT_TRUE(gAborted);
   EXPECT_TRUE(gPngData.empty());

   testing::Mock::VerifyAndClearExpectations(guestOps);
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * GHIGuestOpsMKSControlUnitTest::TestOpenGuestFile --
 *
 *      Test OpenGuestFile method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GHIGuestOpsMKSControlUnitTest, TestOpenGuestFile)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();
   GuestOpsMKSControlUTMock *guestOps = dynamic_cast<GuestOpsMKSControlUTMock *>(vm->GetGuestOps());

   // Test empty path.
   EXPECT_CALL(*guestOps, SendGHIRequestMock(_, _, _, _, _, _)).Times(0);
   guestOps->OpenGuestFile("");
   testing::Mock::VerifyAndClearExpectations(guestOps);

   // Test valid path.
   std::string fakedPath = "fakedPath";
   bool msgMatch = false;
   EXPECT_CALL(*guestOps, SendGHIRequestMock(_, _, _, _, _, _))
      .WillOnce(Invoke([&msgMatch, &fakedPath](GHIChannelType channel, const char *msgName,
                                               const uint8 *msgData, uint32 msgDataLen,
                                               cui::AbortSlot onAbort,
                                               mksctrl::GHIResponseSlot onDone) {
         msgMatch = channel == GHI_CHANNEL_TOOLS_USER &&
                    utf::string(msgName) == UNITY_RPC_SHELL_OPEN &&
                    std::string((char *)msgData) == fakedPath && msgDataLen == fakedPath.size() + 1;
      }));
   guestOps->OpenGuestFile(fakedPath.c_str());
   EXPECT_TRUE(msgMatch);

   testing::Mock::VerifyAndClearExpectations(guestOps);
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * GHIGuestOpsMKSControlUnitTest::TestSetFocusedWindow --
 *
 *      Test SetFocusedWindow method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GHIGuestOpsMKSControlUnitTest, TestSetFocusedWindow)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();
   GuestOpsMKSControlUTMock *guestOps = dynamic_cast<GuestOpsMKSControlUTMock *>(vm->GetGuestOps());

   guestOps->verboseLogging = true;
   bool msgMatch = false;
   int fakedWindowId = 123;
   EXPECT_CALL(*guestOps, SendGHIRequestMock(_, _, _, _, _, _))
      .WillOnce(Invoke([&msgMatch, &fakedWindowId](GHIChannelType channel, const char *msgName,
                                                   const uint8 *msgData, uint32 msgDataLen,
                                                   cui::AbortSlot onAbort,
                                                   mksctrl::GHIResponseSlot onDone) {
         GHISetFocusedWindow eventMsg;
         memset(&eventMsg, 0, sizeof eventMsg);
         if (!XdrUtil_Deserialize((char *)msgData, msgDataLen, (void *)xdr_GHISetFocusedWindow,
                                  &eventMsg)) {
            msgMatch = false;
            return;
         }
         cui::Guard xdrFree(sigc::bind(sigc::ptr_fun(xdr_free),
                                       reinterpret_cast<xdrproc_t>(xdr_GHISetFocusedWindow),
                                       reinterpret_cast<char *>(&eventMsg)));
         GHISetFocusedWindowV1 *v1ptr = eventMsg.GHISetFocusedWindow_u.setFocusedWindowV1;
         if (v1ptr == NULL) {
            msgMatch = false;
            return;
         }
         msgMatch = channel == GHI_CHANNEL_TOOLS_USER &&
                    utf::string(msgName) == GHI_RPC_SET_FOCUSED_WINDOW &&
                    eventMsg.ver == GHI_SET_FOCUSED_WINDOW_V1 && fakedWindowId == v1ptr->windowId;
      }));
   guestOps->SetFocusedWindow(fakedWindowId);
   EXPECT_TRUE(msgMatch);

   testing::Mock::VerifyAndClearExpectations(guestOps);
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * GHIGuestOpsMKSControlUnitTest::TestStartNotificationAreaUpdates --
 *
 *      Test StartNotificationAreaUpdates method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GHIGuestOpsMKSControlUnitTest, TestStartNotificationAreaUpdates)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();
   GuestOpsMKSControlUTMock *guestOps = dynamic_cast<GuestOpsMKSControlUTMock *>(vm->GetGuestOps());

   bool msgMatch = false;
   EXPECT_CALL(*guestOps, SendGHIRequestMock(_, _, _, _, _, _))
      .WillOnce(Invoke([&msgMatch](GHIChannelType channel, const char *msgName,
                                   const uint8 *msgData, uint32 msgDataLen, cui::AbortSlot onAbort,
                                   mksctrl::GHIResponseSlot onDone) {
         msgMatch = channel == GHI_CHANNEL_TOOLS_USER &&
                    utf::string(msgName) == GHI_RPC_TRAY_ICON_START_UPDATES;
      }));
   guestOps->StartNotificationAreaUpdates();
   EXPECT_TRUE(msgMatch);

   testing::Mock::VerifyAndClearExpectations(guestOps);
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * GHIGuestOpsMKSControlUnitTest::TestStopNotificationAreaUpdates --
 *
 *      Test StopNotificationAreaUpdates method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GHIGuestOpsMKSControlUnitTest, TestStopNotificationAreaUpdates)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();
   GuestOpsMKSControlUTMock *guestOps = dynamic_cast<GuestOpsMKSControlUTMock *>(vm->GetGuestOps());

   bool msgMatch = false;
   EXPECT_CALL(*guestOps, SendGHIRequestMock(_, _, _, _, _, _))
      .WillOnce(Invoke([&msgMatch](GHIChannelType channel, const char *msgName,
                                   const uint8 *msgData, uint32 msgDataLen, cui::AbortSlot onAbort,
                                   mksctrl::GHIResponseSlot onDone) {
         msgMatch = channel == GHI_CHANNEL_TOOLS_USER &&
                    utf::string(msgName) == GHI_RPC_TRAY_ICON_STOP_UPDATES;
      }));
   guestOps->StopNotificationAreaUpdates();
   EXPECT_TRUE(msgMatch);

   testing::Mock::VerifyAndClearExpectations(guestOps);
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * GHIGuestOpsMKSControlUnitTest::TestSendNotificationAreaEvent --
 *
 *      Test SendNotificationAreaEvent method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GHIGuestOpsMKSControlUnitTest, TestSendNotificationAreaEvent)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();
   GuestOpsMKSControlUTMock *guestOps = dynamic_cast<GuestOpsMKSControlUTMock *>(vm->GetGuestOps());

   bool msgMatch = false;
   utf::string fakedIconId = "fakedIconId";
   int actualEventType = -1;
   int fakedX = 200;
   int fakedY = 300;
   EXPECT_CALL(*guestOps, SendGHIRequestMock(_, _, _, _, _, _))
      .WillRepeatedly(
         Invoke([&msgMatch, &fakedIconId, fakedX, fakedY, &actualEventType](
                   GHIChannelType channel, const char *msgName, const uint8 *msgData,
                   uint32 msgDataLen, cui::AbortSlot onAbort, mksctrl::GHIResponseSlot onDone) {
            GHITrayIconEvent eventMsg;
            memset(&eventMsg, 0, sizeof eventMsg);
            if (!XdrUtil_Deserialize((char *)msgData, msgDataLen, (void *)xdr_GHITrayIconEvent,
                                     &eventMsg)) {
               msgMatch = false;
               return;
            }
            cui::Guard xdrFree(sigc::bind(sigc::ptr_fun(xdr_free),
                                          reinterpret_cast<xdrproc_t>(xdr_GHITrayIconEvent),
                                          reinterpret_cast<char *>(&eventMsg)));
            GHITrayIconEventV1 *v1ptr = eventMsg.GHITrayIconEvent_u.trayIconEventV1;
            if (v1ptr == NULL) {
               msgMatch = false;
               return;
            }
            msgMatch = channel == GHI_CHANNEL_TOOLS_USER &&
                       utf::string(msgName) == GHI_RPC_TRAY_ICON_SEND_EVENT &&
                       eventMsg.ver == GHI_TRAY_ICON_EVENT_V1 && fakedIconId == v1ptr->iconID &&
                       (int)v1ptr->x == fakedX && (int)v1ptr->y == fakedY;
            actualEventType = v1ptr->event;
         }));

   // Test left mouse click.
   cui::notificationArea::EventType fakedEvent = cui::notificationArea::LEFT_CLICK;
   guestOps->SendNotificationAreaEvent(fakedIconId, fakedEvent, fakedX, fakedY);
   EXPECT_TRUE(msgMatch);
   EXPECT_TRUE(actualEventType == GHI_TRAY_ICON_EVENT_LEFT_CLICK);

   // Test left mouse double click.
   fakedEvent = cui::notificationArea::LEFT_DOUBLE_CLICK;
   msgMatch = false;
   guestOps->SendNotificationAreaEvent(fakedIconId, fakedEvent, fakedX, fakedY);
   EXPECT_TRUE(msgMatch);
   EXPECT_TRUE(actualEventType == GHI_TRAY_ICON_EVENT_LBUTTONDBLCLK);

   // Test right mouse click.
   fakedEvent = cui::notificationArea::RIGHT_CLICK;
   msgMatch = false;
   guestOps->SendNotificationAreaEvent(fakedIconId, fakedEvent, fakedX, fakedY);
   EXPECT_TRUE(msgMatch);
   EXPECT_TRUE(actualEventType == GHI_TRAY_ICON_EVENT_RIGHT_CLICK);

   testing::Mock::VerifyAndClearExpectations(guestOps);
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * GHIGuestOpsMKSControlUnitTest::TestGetNotificationAreaItemIcon --
 *
 *      Test GetNotificationAreaItemIcon method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GHIGuestOpsMKSControlUnitTest, TestGetNotificationAreaItemIcon)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();
   GuestOpsMKSControlUTMock *guestOps = dynamic_cast<GuestOpsMKSControlUTMock *>(vm->GetGuestOps());

   // Test valid icon id.
   std::string fakedMsg = "fakedPngData";
   GHITrayIconV1 fakedTrayIconDetail;
   std::vector<uint8> fakedPngData = {1, 2, 3, 4};
   bool msgMatch = false;
   VMOCK(XdrUtil_Deserialize)
      .WillOnce([&msgMatch, &fakedMsg, &fakedTrayIconDetail,
                 &fakedPngData](const void *data, size_t dataLen, void *xdrProc, void *dest) {
         msgMatch = fakedMsg == (char *)data && dataLen == fakedMsg.size();
         if (msgMatch) {
            GHITrayIcon *handlerInfo = (GHITrayIcon *)dest;
            fakedTrayIconDetail.iconID = (char *)"fakedIconId";
            fakedTrayIconDetail.op = GHI_TRAY_ICON_OP_ADD;
            fakedTrayIconDetail.flags |= GHI_TRAY_ICON_FLAG_PNGDATA;
            fakedTrayIconDetail.pngData.pngData_val = (char *)fakedPngData.data();
            fakedTrayIconDetail.pngData.pngData_len = fakedPngData.size();
            fakedTrayIconDetail.tooltip = NULL;
            fakedTrayIconDetail.blacklistKey = NULL;
            handlerInfo->GHITrayIcon_u.trayIconV1 = &fakedTrayIconDetail;
         }
         return msgMatch;
      });
   guestOps->OnGuestTrayIconUpdateCBMock((const uint8 *)fakedMsg.data(), (uint32)fakedMsg.size());
   EXPECT_TRUE(msgMatch);
   EXPECT_TRUE(guestOps->GetNotificationAreaItemIcon("fakedIconId") == fakedPngData);

   // Test invalid iconID.
   EXPECT_TRUE(msgMatch);
   EXPECT_TRUE(guestOps->GetNotificationAreaItemIcon("invalidIconID").empty());

   testing::Mock::VerifyAndClearExpectations(guestOps);
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * GHIGuestOpsMKSControlUnitTest::TestGetNotificationAreaItemTooltip --
 *
 *      Test GetNotificationAreaItemTooltip method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GHIGuestOpsMKSControlUnitTest, TestGetNotificationAreaItemTooltip)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();
   GuestOpsMKSControlUTMock *guestOps = dynamic_cast<GuestOpsMKSControlUTMock *>(vm->GetGuestOps());

   // Test valid tooltip.
   std::string fakedMsg = "fakedTooltip";
   GHITrayIconV1 fakedTrayIconDetail;
   bool msgMatch = false;
   VMOCK(XdrUtil_Deserialize)
      .WillOnce([&msgMatch, &fakedMsg, &fakedTrayIconDetail](const void *data, size_t dataLen,
                                                             void *xdrProc, void *dest) {
         msgMatch = fakedMsg == (char *)data && dataLen == fakedMsg.size();
         if (msgMatch) {
            GHITrayIcon *handlerInfo = (GHITrayIcon *)dest;
            fakedTrayIconDetail.iconID = (char *)"fakedIconId";
            fakedTrayIconDetail.op = GHI_TRAY_ICON_OP_ADD;
            fakedTrayIconDetail.flags |= GHI_TRAY_ICON_FLAG_TOOLTIP;
            fakedTrayIconDetail.pngData.pngData_val = NULL;
            fakedTrayIconDetail.pngData.pngData_len = 0;
            fakedTrayIconDetail.tooltip = (char *)"fakedTooltip";
            fakedTrayIconDetail.blacklistKey = NULL;
            handlerInfo->GHITrayIcon_u.trayIconV1 = &fakedTrayIconDetail;
         }
         return msgMatch;
      });
   guestOps->OnGuestTrayIconUpdateCBMock((const uint8 *)fakedMsg.data(), (uint32)fakedMsg.size());
   EXPECT_TRUE(msgMatch);
   EXPECT_TRUE(guestOps->GetNotificationAreaItemTooltip("fakedIconId") == "fakedTooltip");

   // Test invalid tooltip.
   EXPECT_TRUE(msgMatch);
   EXPECT_TRUE(guestOps->GetNotificationAreaItemTooltip("invalidIconID").empty());

   testing::Mock::VerifyAndClearExpectations(guestOps);
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * GHIGuestOpsMKSControlUnitTest::TestGetNotificationAreaItemBlacklistKey --
 *
 *      Test GetNotificationAreaItemBlacklistKey method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GHIGuestOpsMKSControlUnitTest, TestGetNotificationAreaItemBlacklistKey)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();
   GuestOpsMKSControlUTMock *guestOps = dynamic_cast<GuestOpsMKSControlUTMock *>(vm->GetGuestOps());

   // Test valid black list key.
   std::string fakedMsg = "fakedBlackListKey";
   GHITrayIconV1 fakedTrayIconDetail;
   bool msgMatch = false;
   VMOCK(XdrUtil_Deserialize)
      .WillOnce([&msgMatch, &fakedMsg, &fakedTrayIconDetail](const void *data, size_t dataLen,
                                                             void *xdrProc, void *dest) {
         msgMatch = fakedMsg == (char *)data && dataLen == fakedMsg.size();
         if (msgMatch) {
            GHITrayIcon *handlerInfo = (GHITrayIcon *)dest;
            fakedTrayIconDetail.iconID = (char *)"fakedIconId";
            fakedTrayIconDetail.op = GHI_TRAY_ICON_OP_ADD;
            fakedTrayIconDetail.flags |= GHI_TRAY_ICON_FLAG_BLACKLISTKEY;
            fakedTrayIconDetail.pngData.pngData_val = NULL;
            fakedTrayIconDetail.pngData.pngData_len = 0;
            fakedTrayIconDetail.tooltip = NULL;
            fakedTrayIconDetail.blacklistKey = (char *)"fakedBlackListKey";
            handlerInfo->GHITrayIcon_u.trayIconV1 = &fakedTrayIconDetail;
         }
         return msgMatch;
      });
   guestOps->OnGuestTrayIconUpdateCBMock((const uint8 *)fakedMsg.data(), (uint32)fakedMsg.size());
   EXPECT_TRUE(msgMatch);
   EXPECT_TRUE(guestOps->GetNotificationAreaItemBlacklistKey("fakedIconId") == "fakedBlackListKey");

   // Test invalid black list key.
   EXPECT_TRUE(msgMatch);
   EXPECT_TRUE(guestOps->GetNotificationAreaItemBlacklistKey("invalidIconID").empty());

   testing::Mock::VerifyAndClearExpectations(guestOps);
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * GHIGuestOpsMKSControlUnitTest::TestOrderUnityWindowsToTop --
 *
 *      Test OrderUnityWindowsToTop method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GHIGuestOpsMKSControlUnitTest, TestOrderUnityWindowsToTop)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();
   GuestOpsMKSControlUTMock *guestOps = dynamic_cast<GuestOpsMKSControlUTMock *>(vm->GetGuestOps());

   std::list<uint32> fakedWindows = {123, 456};
   std::string expectedMsg = "123 456";
   bool msgMatch = false;
   EXPECT_CALL(*guestOps, SendGHIRequestMock(_, _, _, _, _, _))
      .WillOnce(Invoke([&msgMatch, &expectedMsg](GHIChannelType channel, const char *msgName,
                                                 const uint8 *msgData, uint32 msgDataLen,
                                                 cui::AbortSlot onAbort,
                                                 mksctrl::GHIResponseSlot onDone) {
         msgMatch =
            channel == GHI_CHANNEL_TOOLS_USER && utf::string(msgName) == UNITY_RPC_WINDOW_SETTOP &&
            std::string((char *)msgData) == expectedMsg && msgDataLen == expectedMsg.size() + 1;
      }));
   guestOps->OrderUnityWindowsToTop(fakedWindows);
   EXPECT_TRUE(msgMatch);

   testing::Mock::VerifyAndClearExpectations(guestOps);
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * GHIGuestOpsMKSControlUnitTest::TestMinimizeUnityWindow --
 *
 *      Test MinimizeUnityWindow method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GHIGuestOpsMKSControlUnitTest, TestMinimizeUnityWindow)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();
   GuestOpsMKSControlUTMock *guestOps = dynamic_cast<GuestOpsMKSControlUTMock *>(vm->GetGuestOps());

   bool msgMatch = false;
   std::string expectedMsg = "123";
   EXPECT_CALL(*guestOps, SendGHIRequestMock(_, _, _, _, _, _))
      .WillOnce(
         Invoke([&msgMatch, &expectedMsg](GHIChannelType channel, const char *msgName,
                                          const uint8 *msgData, uint32 msgDataLen,
                                          cui::AbortSlot onAbort, mksctrl::GHIResponseSlot onDone) {
            msgMatch = channel == GHI_CHANNEL_TOOLS_USER &&
                       utf::string(msgName) == UNITY_RPC_WINDOW_MINIMIZE &&
                       std::string((char *)msgData) == expectedMsg &&
                       msgDataLen == expectedMsg.size() + 1;
         }));
   guestOps->MinimizeUnityWindow(123);
   EXPECT_TRUE(msgMatch);

   testing::Mock::VerifyAndClearExpectations(guestOps);
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * GHIGuestOpsMKSControlUnitTest::TestUnminimizeUnityWindow --
 *
 *      Test UnminimizeUnityWindow method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GHIGuestOpsMKSControlUnitTest, TestUnminimizeUnityWindow)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();
   GuestOpsMKSControlUTMock *guestOps = dynamic_cast<GuestOpsMKSControlUTMock *>(vm->GetGuestOps());

   bool msgMatch = false;
   std::string expectedMsg = "123";
   EXPECT_CALL(*guestOps, SendGHIRequestMock(_, _, _, _, _, _))
      .WillOnce(
         Invoke([&msgMatch, &expectedMsg](GHIChannelType channel, const char *msgName,
                                          const uint8 *msgData, uint32 msgDataLen,
                                          cui::AbortSlot onAbort, mksctrl::GHIResponseSlot onDone) {
            msgMatch = channel == GHI_CHANNEL_TOOLS_USER &&
                       utf::string(msgName) == UNITY_RPC_WINDOW_UNMINIMIZE &&
                       std::string((char *)msgData) == expectedMsg &&
                       msgDataLen == expectedMsg.size() + 1;
         }));
   guestOps->UnminimizeUnityWindow(123);
   EXPECT_TRUE(msgMatch);

   testing::Mock::VerifyAndClearExpectations(guestOps);
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * GHIGuestOpsMKSControlUnitTest::TestMaximizeUnityWindow --
 *
 *      Test MaximizeUnityWindow method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GHIGuestOpsMKSControlUnitTest, TestMaximizeUnityWindow)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();
   GuestOpsMKSControlUTMock *guestOps = dynamic_cast<GuestOpsMKSControlUTMock *>(vm->GetGuestOps());

   bool msgMatch = false;
   std::string expectedMsg = "123";
   EXPECT_CALL(*guestOps, SendGHIRequestMock(_, _, _, _, _, _))
      .WillOnce(
         Invoke([&msgMatch, &expectedMsg](GHIChannelType channel, const char *msgName,
                                          const uint8 *msgData, uint32 msgDataLen,
                                          cui::AbortSlot onAbort, mksctrl::GHIResponseSlot onDone) {
            msgMatch = channel == GHI_CHANNEL_TOOLS_USER &&
                       utf::string(msgName) == UNITY_RPC_WINDOW_MAXIMIZE &&
                       std::string((char *)msgData) == expectedMsg &&
                       msgDataLen == expectedMsg.size() + 1;
         }));
   guestOps->MaximizeUnityWindow(123);
   EXPECT_TRUE(msgMatch);

   testing::Mock::VerifyAndClearExpectations(guestOps);
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * GHIGuestOpsMKSControlUnitTest::TestUnmaximizeUnityWindow --
 *
 *      Test UnmaximizeUnityWindow method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GHIGuestOpsMKSControlUnitTest, TestUnmaximizeUnityWindow)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();
   GuestOpsMKSControlUTMock *guestOps = dynamic_cast<GuestOpsMKSControlUTMock *>(vm->GetGuestOps());

   bool msgMatch = false;
   std::string expectedMsg = "123";
   EXPECT_CALL(*guestOps, SendGHIRequestMock(_, _, _, _, _, _))
      .WillOnce(
         Invoke([&msgMatch, &expectedMsg](GHIChannelType channel, const char *msgName,
                                          const uint8 *msgData, uint32 msgDataLen,
                                          cui::AbortSlot onAbort, mksctrl::GHIResponseSlot onDone) {
            msgMatch = channel == GHI_CHANNEL_TOOLS_USER &&
                       utf::string(msgName) == UNITY_RPC_WINDOW_UNMAXIMIZE &&
                       std::string((char *)msgData) == expectedMsg &&
                       msgDataLen == expectedMsg.size() + 1;
         }));
   guestOps->UnmaximizeUnityWindow(123);
   EXPECT_TRUE(msgMatch);

   testing::Mock::VerifyAndClearExpectations(guestOps);
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * GHIGuestOpsMKSControlUnitTest::TestStickUnityWindow --
 *
 *      Test StickUnityWindow method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GHIGuestOpsMKSControlUnitTest, TestStickUnityWindow)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();
   GuestOpsMKSControlUTMock *guestOps = dynamic_cast<GuestOpsMKSControlUTMock *>(vm->GetGuestOps());

   bool msgMatch = false;
   std::string expectedMsg = "123";
   EXPECT_CALL(*guestOps, SendGHIRequestMock(_, _, _, _, _, _))
      .WillOnce(Invoke([&msgMatch, &expectedMsg](GHIChannelType channel, const char *msgName,
                                                 const uint8 *msgData, uint32 msgDataLen,
                                                 cui::AbortSlot onAbort,
                                                 mksctrl::GHIResponseSlot onDone) {
         msgMatch =
            channel == GHI_CHANNEL_TOOLS_USER && utf::string(msgName) == UNITY_RPC_WINDOW_STICK &&
            std::string((char *)msgData) == expectedMsg && msgDataLen == expectedMsg.size() + 1;
      }));
   guestOps->StickUnityWindow(123);
   EXPECT_TRUE(msgMatch);

   testing::Mock::VerifyAndClearExpectations(guestOps);
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * GHIGuestOpsMKSControlUnitTest::TestUnstickUnityWindow --
 *
 *      Test UnstickUnityWindow method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GHIGuestOpsMKSControlUnitTest, TestUnstickUnityWindow)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();
   GuestOpsMKSControlUTMock *guestOps = dynamic_cast<GuestOpsMKSControlUTMock *>(vm->GetGuestOps());

   bool msgMatch = false;
   std::string expectedMsg = "123";
   EXPECT_CALL(*guestOps, SendGHIRequestMock(_, _, _, _, _, _))
      .WillOnce(Invoke([&msgMatch, &expectedMsg](GHIChannelType channel, const char *msgName,
                                                 const uint8 *msgData, uint32 msgDataLen,
                                                 cui::AbortSlot onAbort,
                                                 mksctrl::GHIResponseSlot onDone) {
         msgMatch =
            channel == GHI_CHANNEL_TOOLS_USER && utf::string(msgName) == UNITY_RPC_WINDOW_UNSTICK &&
            std::string((char *)msgData) == expectedMsg && msgDataLen == expectedMsg.size() + 1;
      }));
   guestOps->UnstickUnityWindow(123);
   EXPECT_TRUE(msgMatch);

   testing::Mock::VerifyAndClearExpectations(guestOps);
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * GHIGuestOpsMKSControlUnitTest::TestCloseUnityWindowRequest --
 *
 *      Test CloseUnityWindowRequest method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GHIGuestOpsMKSControlUnitTest, TestCloseUnityWindowRequest)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();
   GuestOpsMKSControlUTMock *guestOps = dynamic_cast<GuestOpsMKSControlUTMock *>(vm->GetGuestOps());

   bool msgMatch = false;
   std::string expectedMsg = "123";
   EXPECT_CALL(*guestOps, SendGHIRequestMock(_, _, _, _, _, _))
      .WillOnce(Invoke([&msgMatch, &expectedMsg](GHIChannelType channel, const char *msgName,
                                                 const uint8 *msgData, uint32 msgDataLen,
                                                 cui::AbortSlot onAbort,
                                                 mksctrl::GHIResponseSlot onDone) {
         msgMatch =
            channel == GHI_CHANNEL_TOOLS_USER && utf::string(msgName) == UNITY_RPC_WINDOW_CLOSE &&
            std::string((char *)msgData) == expectedMsg && msgDataLen == expectedMsg.size() + 1;
      }));
   guestOps->CloseUnityWindowRequest(123);
   EXPECT_TRUE(msgMatch);

   testing::Mock::VerifyAndClearExpectations(guestOps);
   delete vm;
}


static int gX = 0;
static int gY = 0;
static int gWidth = 0;
static int gHeight = 0;

/*
 *-----------------------------------------------------------------------------
 *
 * FakedOnMoveResizeUnityWindowDone --
 *
 *      Faked OnMoveResizeUnityWindowDone callback.
 *
 * Result:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

void
FakedOnMoveResizeUnityWindowDone(int x,      // IN
                                 int y,      // IN
                                 int width,  // IN
                                 int height) // IN
{
   gX = x;
   gY = y;
   gWidth = width;
   gHeight = height;
}


/*
 *-----------------------------------------------------------------------------
 *
 * GHIGuestOpsMKSControlUnitTest::TestMoveResizeUnityWindowAndItsCallback --
 *
 *      Test MoveResizeUnityWindow and OnMoveResizeUnityWindowDone method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GHIGuestOpsMKSControlUnitTest, TestMoveResizeUnityWindowAndItsCallback)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();
   GuestOpsMKSControlUTMock *guestOps = dynamic_cast<GuestOpsMKSControlUTMock *>(vm->GetGuestOps());

   // Test parse failure and abort.
   bool msgMatch = false;
   gAborted = false;
   UnityWindowId fakedWindowId = 123;
   int fakedX = 0;
   int fakedY = 2;
   int fakedWidth = 1920;
   int fakedHeight = 1080;
   std::string expectedMsg = std::to_string(fakedWindowId) + " " + std::to_string(fakedX) + " " +
                             std::to_string(fakedY) + " " + std::to_string(fakedWidth) + " " +
                             std::to_string(fakedHeight);
   std::string fakedWrongResponse =
      std::to_string(fakedX) + " " + std::to_string(fakedY) + " " + std::to_string(fakedWidth);
   EXPECT_CALL(*guestOps, SendGHIRequestMock(_, _, _, _, _, _))
      .WillOnce(
         Invoke([&msgMatch, &expectedMsg, &fakedWrongResponse](
                   GHIChannelType channel, const char *msgName, const uint8 *msgData,
                   uint32 msgDataLen, cui::AbortSlot onAbort, mksctrl::GHIResponseSlot onDone) {
            msgMatch = channel == GHI_CHANNEL_TOOLS_USER &&
                       utf::string(msgName) == UNITY_RPC_WINDOW_MOVE_RESIZE &&
                       std::string((char *)msgData) == expectedMsg &&
                       msgDataLen == expectedMsg.size() + 1;
            onDone((const uint8 *)fakedWrongResponse.data(), (uint32)fakedWrongResponse.size());
         }));
   guestOps->MoveResizeUnityWindow(fakedWindowId, fakedX, fakedY, fakedWidth, fakedHeight,
                                   &FakedOnAbort, &FakedOnMoveResizeUnityWindowDone);
   EXPECT_TRUE(msgMatch);
   EXPECT_TRUE(gAborted);
   testing::Mock::VerifyAndClearExpectations(guestOps);

   // Test parse succeed.
   gAborted = false;
   std::string fakedResponse = std::to_string(fakedX) + " " + std::to_string(fakedY) + " " +
                               std::to_string(fakedWidth) + " " + std::to_string(fakedHeight);
   EXPECT_CALL(*guestOps, SendGHIRequestMock(_, _, _, _, _, _))
      .WillOnce(Invoke([&msgMatch, &expectedMsg,
                        &fakedResponse](GHIChannelType channel, const char *msgName,
                                        const uint8 *msgData, uint32 msgDataLen,
                                        cui::AbortSlot onAbort, mksctrl::GHIResponseSlot onDone) {
         msgMatch = channel == GHI_CHANNEL_TOOLS_USER &&
                    utf::string(msgName) == UNITY_RPC_WINDOW_MOVE_RESIZE &&
                    std::string((char *)msgData) == expectedMsg &&
                    msgDataLen == expectedMsg.size() + 1;
         onDone((const uint8 *)fakedResponse.data(), (uint32)fakedResponse.size());
      }));
   guestOps->MoveResizeUnityWindow(fakedWindowId, fakedX, fakedY, fakedWidth, fakedHeight,
                                   &FakedOnAbort, &FakedOnMoveResizeUnityWindowDone);
   EXPECT_TRUE(msgMatch);
   EXPECT_FALSE(gAborted);
   EXPECT_TRUE(gX == fakedX);
   EXPECT_TRUE(gY == fakedY);
   EXPECT_TRUE(gWidth == fakedWidth);
   EXPECT_TRUE(gHeight == fakedHeight);

   testing::Mock::VerifyAndClearExpectations(guestOps);
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * GHIGuestOpsMKSControlUnitTest::TestSetUnityDesktopWorkAreas --
 *
 *      Test SetUnityDesktopWorkAreas method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GHIGuestOpsMKSControlUnitTest, TestSetUnityDesktopWorkAreas)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();
   GuestOpsMKSControlUTMock *guestOps = dynamic_cast<GuestOpsMKSControlUTMock *>(vm->GetGuestOps());

   guestOps->verboseLogging = true;
   bool msgMatch = false;
   std::vector<cui::Rect> fakedWorkAreas = {cui::Rect::FromLTRB(0, 0, 800, 600),
                                            cui::Rect::FromLTRB(800, 0, 1920, 1080)};
   std::string expectedMsg = "2 , 0 0 800 600 , 800 0 1120 1080";
   EXPECT_CALL(*guestOps, SendGHIRequestMock(_, _, _, _, _, _))
      .WillOnce(
         Invoke([&msgMatch, &expectedMsg](GHIChannelType channel, const char *msgName,
                                          const uint8 *msgData, uint32 msgDataLen,
                                          cui::AbortSlot onAbort, mksctrl::GHIResponseSlot onDone) {
            msgMatch = channel == GHI_CHANNEL_TOOLS_USER &&
                       utf::string(msgName) == UNITY_RPC_DESKTOP_WORK_AREA_SET &&
                       std::string((char *)msgData) == expectedMsg &&
                       msgDataLen == expectedMsg.size() + 1;
         }));
   guestOps->SetUnityDesktopWorkAreas(fakedWorkAreas);
   EXPECT_TRUE(msgMatch);

   testing::Mock::VerifyAndClearExpectations(guestOps);
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * GHIGuestOpsMKSControlUnitTest::TestToggleStartUI --
 *
 *      Test ToggleStartUI method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GHIGuestOpsMKSControlUnitTest, TestToggleStartUI)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();
   GuestOpsMKSControlUTMock *guestOps = dynamic_cast<GuestOpsMKSControlUTMock *>(vm->GetGuestOps());

   bool msgMatch = false;
   EXPECT_CALL(*guestOps, SendGHIRequestMock(_, _, _, _, _, _))
      .WillOnce(Invoke([&msgMatch](GHIChannelType channel, const char *msgName,
                                   const uint8 *msgData, uint32 msgDataLen, cui::AbortSlot onAbort,
                                   mksctrl::GHIResponseSlot onDone) {
         msgMatch = channel == GHI_CHANNEL_TOOLS_USER &&
                    utf::string(msgName) == GHI_RPC_TOGGLE_START_UI && msgData == NULL &&
                    msgDataLen == 0;
      }));
   guestOps->ToggleStartUI();
   EXPECT_TRUE(msgMatch);

   testing::Mock::VerifyAndClearExpectations(guestOps);
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * GHIGuestOpsMKSControlUnitTest::TestSetDisplayScaling --
 *
 *      Test SetDisplayScaling method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GHIGuestOpsMKSControlUnitTest, TestSetDisplayScaling)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();
   GuestOpsMKSControlUTMock *guestOps = dynamic_cast<GuestOpsMKSControlUTMock *>(vm->GetGuestOps());
   guestOps->verboseLogging = true;

   // Test abort due to destroying.
   gAborted = false;
   std::vector<cui::DisplayScaling> fakedDisplays = {
      {cui::Rect::FromLTRB(0, 0, 800, 600), 96},
      {cui::Rect::FromLTRB(800, 0, 1920, 1080), 192},
   };
   VMOCK(&cui::DestroyNotifier::IsDestroying).WillOnce(true);
   EXPECT_CALL(*guestOps, SendGHIRequestMock(_, _, _, _, _, _)).Times(0);
   guestOps->SetDisplayScaling(2, false, fakedDisplays, &FakedOnAbort);
   EXPECT_TRUE(gAborted);
   testing::Mock::VerifyAndClearExpectations(guestOps);

   // Test abort due to invalid percent.
   gAborted = false;
   VMOCK(&cui::DestroyNotifier::IsDestroying).WillRepeatedly(false);
   EXPECT_CALL(*guestOps, SendGHIRequestMock(_, _, _, _, _, _)).Times(0);
   guestOps->SetDisplayScaling(-1, false, fakedDisplays, &FakedOnAbort);
   EXPECT_TRUE(gAborted);
   testing::Mock::VerifyAndClearExpectations(guestOps);

   // Test valid parse.
   bool msgMatch = false;
   gAborted = false;
   EXPECT_CALL(*guestOps, SendGHIRequestMock(_, _, _, _, _, _))
      .WillOnce(Invoke([&msgMatch, &fakedDisplays](GHIChannelType channel, const char *msgName,
                                                   const uint8 *msgData, uint32 msgDataLen,
                                                   cui::AbortSlot onAbort,
                                                   mksctrl::GHIResponseSlot onDone) {
         GHIDisplaysDPIInfo eventMsg;
         memset(&eventMsg, 0, sizeof eventMsg);
         if (!XdrUtil_Deserialize((char *)msgData, msgDataLen, (void *)xdr_GHIDisplaysDPIInfo,
                                  &eventMsg)) {
            msgMatch = false;
            return;
         }
         cui::Guard xdrFree(sigc::bind(sigc::ptr_fun(xdr_free),
                                       reinterpret_cast<xdrproc_t>(xdr_GHIDisplaysDPIInfo),
                                       reinterpret_cast<char *>(&eventMsg)));
         GHIDisplaysDPIInfoV1 *v1ptr = eventMsg.GHIDisplaysDPIInfo_u.displaysDPIInfoV1;
         if (v1ptr == NULL) {
            msgMatch = false;
            return;
         }
         msgMatch = channel == GHI_CHANNEL_TOOLS_USER &&
                    utf::string(msgName) == GHI_RPC_SET_DISPLAY_SCALING &&
                    eventMsg.ver == GHI_DISPLAYS_DPI_INFO_V1 && v1ptr->percent == 2 &&
                    v1ptr->reset == 0 && v1ptr->numDisplays == fakedDisplays.size() &&
                    v1ptr->displayDPIInfo.displayDPIInfo_len == fakedDisplays.size() &&
                    v1ptr->displayDPIInfo.displayDPIInfo_val[0].x ==
                       fakedDisplays[0].rcDisplay.GetOrigin().x &&
                    v1ptr->displayDPIInfo.displayDPIInfo_val[0].y ==
                       fakedDisplays[0].rcDisplay.GetOrigin().y &&
                    v1ptr->displayDPIInfo.displayDPIInfo_val[0].width ==
                       fakedDisplays[0].rcDisplay.Width() &&
                    v1ptr->displayDPIInfo.displayDPIInfo_val[0].height ==
                       fakedDisplays[0].rcDisplay.Height() &&
                    v1ptr->displayDPIInfo.displayDPIInfo_val[0].DPI == fakedDisplays[0].DPI &&
                    v1ptr->displayDPIInfo.displayDPIInfo_val[1].x ==
                       fakedDisplays[1].rcDisplay.GetOrigin().x &&
                    v1ptr->displayDPIInfo.displayDPIInfo_val[1].y ==
                       fakedDisplays[1].rcDisplay.GetOrigin().y &&
                    v1ptr->displayDPIInfo.displayDPIInfo_val[1].width ==
                       fakedDisplays[1].rcDisplay.Width() &&
                    v1ptr->displayDPIInfo.displayDPIInfo_val[1].height ==
                       fakedDisplays[1].rcDisplay.Height() &&
                    v1ptr->displayDPIInfo.displayDPIInfo_val[1].DPI == fakedDisplays[1].DPI;
      }));
   guestOps->SetDisplayScaling(2, false, fakedDisplays, &FakedOnAbort);
   EXPECT_TRUE(msgMatch);
   EXPECT_FALSE(gAborted);

   testing::Mock::VerifyAndClearExpectations(guestOps);
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * GHIGuestOpsMKSControlUnitTest::TestSetDarkMode --
 *
 *      Test SetDarkMode method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GHIGuestOpsMKSControlUnitTest, TestSetDarkMode)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();
   GuestOpsMKSControlUTMock *guestOps = dynamic_cast<GuestOpsMKSControlUTMock *>(vm->GetGuestOps());

   // Test set darkMode.
   bool msgMatch = false;
   bool isDarkMode = true;
   EXPECT_CALL(*guestOps, SendGHIRequestMock(_, _, _, _, _, _))
      .WillRepeatedly(
         Invoke([&msgMatch, &isDarkMode](GHIChannelType channel, const char *msgName,
                                         const uint8 *msgData, uint32 msgDataLen,
                                         cui::AbortSlot onAbort, mksctrl::GHIResponseSlot onDone) {
            bool actualDarkMode = std::string((char *)msgData) != "0";
            msgMatch = channel == GHI_CHANNEL_TOOLS_USER &&
                       utf::string(msgName) == GHI_RPC_SET_DARK_MODE &&
                       !(actualDarkMode ^ isDarkMode) && msgDataLen == 2;
         }));
   guestOps->SetDarkMode(isDarkMode);
   EXPECT_TRUE(msgMatch);

   // Test unset darkMode.
   isDarkMode = false;
   msgMatch = false;
   guestOps->SetDarkMode(isDarkMode);
   EXPECT_TRUE(msgMatch);

   testing::Mock::VerifyAndClearExpectations(guestOps);
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * GHIGuestOpsMKSControlUnitTest::TestSetUnityOptions --
 *
 *      Test SetUnityOptions method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GHIGuestOpsMKSControlUnitTest, TestSetUnityOptions)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();
   GuestOpsMKSControlUTMock *guestOps = dynamic_cast<GuestOpsMKSControlUTMock *>(vm->GetGuestOps());

   bool msgMatch = false;
   uint32 fakedFeatureMask = 123;
   EXPECT_CALL(*guestOps, SendGHIRequestMock(_, _, _, _, _, _))
      .WillOnce(Invoke([&msgMatch, fakedFeatureMask](GHIChannelType channel, const char *msgName,
                                                     const uint8 *msgData, uint32 msgDataLen,
                                                     cui::AbortSlot onAbort,
                                                     mksctrl::GHIResponseSlot onDone) {
         UnityOptions eventMsg;
         memset(&eventMsg, 0, sizeof eventMsg);
         if (!XdrUtil_Deserialize((char *)msgData, msgDataLen, (void *)xdr_UnityOptions,
                                  &eventMsg)) {
            msgMatch = false;
            return;
         }
         cui::Guard xdrFree(sigc::bind(sigc::ptr_fun(xdr_free),
                                       reinterpret_cast<xdrproc_t>(xdr_UnityOptions),
                                       reinterpret_cast<char *>(&eventMsg)));
         UnityOptionsV1 *v1ptr = eventMsg.UnityOptions_u.unityOptionsV1;
         if (v1ptr == NULL) {
            msgMatch = false;
            return;
         }
         msgMatch = channel == GHI_CHANNEL_TOOLS_USER &&
                    utf::string(msgName) == UNITY_RPC_SET_OPTIONS &&
                    eventMsg.ver == UNITY_OPTIONS_V1 && v1ptr->featureMask == (int)fakedFeatureMask;
      }));
   guestOps->SetUnityOptions(fakedFeatureMask);
   EXPECT_TRUE(msgMatch);

   testing::Mock::VerifyAndClearExpectations(guestOps);
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * GHIGuestOpsMKSControlUnitTest::TestSetUnityDesktopConfig --
 *
 *      Test SetUnityDesktopConfig method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GHIGuestOpsMKSControlUnitTest, TestSetUnityDesktopConfig)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();
   GuestOpsMKSControlUTMock *guestOps = dynamic_cast<GuestOpsMKSControlUTMock *>(vm->GetGuestOps());

   bool msgMatch = false;
   cui::GuestOpsMKSControl::DesktopList fakedDesktops = {
      cui::Point(1, 2),
      cui::Point(3, 4),
   };
   uint32 fakedActiveDesktopID = fakedDesktops.size() - 1;
   std::string expectedMsg = "{1,2} {3,4} 1";
   EXPECT_CALL(*guestOps, SendGHIRequestMock(_, _, _, _, _, _))
      .WillOnce(
         Invoke([&msgMatch, expectedMsg](GHIChannelType channel, const char *msgName,
                                         const uint8 *msgData, uint32 msgDataLen,
                                         cui::AbortSlot onAbort, mksctrl::GHIResponseSlot onDone) {
            msgMatch = channel == GHI_CHANNEL_TOOLS_USER &&
                       utf::string(msgName) == UNITY_RPC_DESKTOP_CONFIG_SET &&
                       expectedMsg == (char *)msgData && msgDataLen == expectedMsg.size() + 1;
         }));
   guestOps->SetUnityDesktopConfig(fakedDesktops, fakedActiveDesktopID);
   EXPECT_TRUE(msgMatch);

   testing::Mock::VerifyAndClearExpectations(guestOps);
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * GHIGuestOpsMKSControlUnitTest::TestSetUnityActiveDesktop --
 *
 *      Test SetUnityActiveDesktop method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GHIGuestOpsMKSControlUnitTest, TestSetUnityActiveDesktop)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();
   GuestOpsMKSControlUTMock *guestOps = dynamic_cast<GuestOpsMKSControlUTMock *>(vm->GetGuestOps());

   bool msgMatch = false;
   uint32 fakedActiveDesktopID = 123;
   std::string expectedMsg = std::to_string(fakedActiveDesktopID);
   EXPECT_CALL(*guestOps, SendGHIRequestMock(_, _, _, _, _, _))
      .WillOnce(
         Invoke([&msgMatch, expectedMsg](GHIChannelType channel, const char *msgName,
                                         const uint8 *msgData, uint32 msgDataLen,
                                         cui::AbortSlot onAbort, mksctrl::GHIResponseSlot onDone) {
            msgMatch = channel == GHI_CHANNEL_TOOLS_USER &&
                       utf::string(msgName) == UNITY_RPC_DESKTOP_ACTIVE_SET &&
                       expectedMsg == (char *)msgData && msgDataLen == expectedMsg.size() + 1;
         }));
   guestOps->SetUnityActiveDesktop(fakedActiveDesktopID);
   EXPECT_TRUE(msgMatch);

   testing::Mock::VerifyAndClearExpectations(guestOps);
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * GHIGuestOpsMKSControlUnitTest::TestSetUnityWindowDesktop --
 *
 *      Test SetUnityWindowDesktop method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GHIGuestOpsMKSControlUnitTest, TestSetUnityWindowDesktop)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();
   GuestOpsMKSControlUTMock *guestOps = dynamic_cast<GuestOpsMKSControlUTMock *>(vm->GetGuestOps());

   bool msgMatch = false;
   UnityWindowId fakedWindowID = 123;
   UnityDesktopId fakedDesktopID = 456;
   std::string expectedMsg = std::to_string(fakedWindowID) + " " + std::to_string(fakedDesktopID);
   EXPECT_CALL(*guestOps, SendGHIRequestMock(_, _, _, _, _, _))
      .WillOnce(
         Invoke([&msgMatch, expectedMsg](GHIChannelType channel, const char *msgName,
                                         const uint8 *msgData, uint32 msgDataLen,
                                         cui::AbortSlot onAbort, mksctrl::GHIResponseSlot onDone) {
            msgMatch = channel == GHI_CHANNEL_TOOLS_USER &&
                       utf::string(msgName) == UNITY_RPC_WINDOW_DESKTOP_SET &&
                       expectedMsg == (char *)msgData && msgDataLen == expectedMsg.size() + 1;
         }));
   guestOps->SetUnityWindowDesktop(fakedWindowID, fakedDesktopID);
   EXPECT_TRUE(msgMatch);

   testing::Mock::VerifyAndClearExpectations(guestOps);
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * GHIGuestOpsMKSControlUnitTest::TestRequestUnityGuestWindowContents --
 *
 *      Test RequestUnityGuestWindowContents method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GHIGuestOpsMKSControlUnitTest, TestRequestUnityGuestWindowContents)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();
   GuestOpsMKSControlUTMock *guestOps = dynamic_cast<GuestOpsMKSControlUTMock *>(vm->GetGuestOps());

   bool msgMatch = false;
   std::list<UnityWindowId> fakedWindowIDs = {123, 456, 789};
   EXPECT_CALL(*guestOps, SendGHIRequestMock(_, _, _, _, _, _))
      .WillOnce(Invoke([&msgMatch, fakedWindowIDs](GHIChannelType channel, const char *msgName,
                                                   const uint8 *msgData, uint32 msgDataLen,
                                                   cui::AbortSlot onAbort,
                                                   mksctrl::GHIResponseSlot onDone) {
         UnityWindowContentsRequest eventMsg;
         memset(&eventMsg, 0, sizeof eventMsg);
         if (!XdrUtil_Deserialize((char *)msgData, msgDataLen,
                                  (void *)xdr_UnityWindowContentsRequest, &eventMsg)) {
            msgMatch = false;
            return;
         }
         cui::Guard xdrFree(sigc::bind(sigc::ptr_fun(xdr_free),
                                       reinterpret_cast<xdrproc_t>(xdr_UnityWindowContentsRequest),
                                       reinterpret_cast<char *>(&eventMsg)));
         UnityWindowContentsRequestV1 *v1ptr = eventMsg.UnityWindowContentsRequest_u.requestV1;
         if (v1ptr == NULL) {
            msgMatch = false;
            return;
         }
         std::vector<UnityWindowId> expectedWindowIds{fakedWindowIDs.begin(), fakedWindowIDs.end()};
         msgMatch = channel == GHI_CHANNEL_TOOLS_USER &&
                    utf::string(msgName) == UNITY_RPC_WINDOW_CONTENTS_REQUEST &&
                    eventMsg.ver == UNITY_WINDOW_CONTENTS_V1 &&
                    v1ptr->windowID.windowID_len == expectedWindowIds.size() &&
                    v1ptr->windowID.windowID_val[0] == expectedWindowIds[0] &&
                    v1ptr->windowID.windowID_val[1] == expectedWindowIds[1] &&
                    v1ptr->windowID.windowID_val[2] == expectedWindowIds[2];
      }));
   guestOps->RequestUnityGuestWindowContents(fakedWindowIDs);
   EXPECT_TRUE(msgMatch);

   testing::Mock::VerifyAndClearExpectations(guestOps);
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * GHIGuestOpsMKSControlUnitTest::TestUnityConfirmOperation --
 *
 *      Test UnityConfirmOperation method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GHIGuestOpsMKSControlUnitTest, TestUnityConfirmOperation)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();
   GuestOpsMKSControlUTMock *guestOps = dynamic_cast<GuestOpsMKSControlUTMock *>(vm->GetGuestOps());

   bool msgMatch = false;
   int fakedSequence = 11;
   UnityWindowId fakedWindowId = 123;
   cui::GuestOpsMKSControl::UnityOperationType fakedOpType =
      cui::GuestOpsMKSControl::UNITY_OPERATION_TYPE_MINIMIZE;
   cui::GuestOps::UnityOperation fakedOp = {fakedSequence, fakedWindowId, fakedOpType};
   EXPECT_CALL(*guestOps, SendGHIRequestMock(_, _, _, _, _, _))
      .WillOnce(Invoke([&msgMatch, fakedSequence, fakedWindowId,
                        fakedOpType](GHIChannelType channel, const char *msgName,
                                     const uint8 *msgData, uint32 msgDataLen,
                                     cui::AbortSlot onAbort, mksctrl::GHIResponseSlot onDone) {
         UnityConfirmOperation eventMsg;
         memset(&eventMsg, 0, sizeof eventMsg);
         if (!XdrUtil_Deserialize((char *)msgData, msgDataLen, (void *)xdr_UnityConfirmOperation,
                                  &eventMsg)) {
            msgMatch = false;
            return;
         }
         cui::Guard xdrFree(sigc::bind(sigc::ptr_fun(xdr_free),
                                       reinterpret_cast<xdrproc_t>(xdr_UnityConfirmOperation),
                                       reinterpret_cast<char *>(&eventMsg)));
         UnityConfirmOperationV1 *v1ptr = eventMsg.UnityConfirmOperation_u.unityConfirmOpV1;
         if (v1ptr == NULL) {
            msgMatch = false;
            return;
         }
         msgMatch = channel == GHI_CHANNEL_TOOLS_USER &&
                    utf::string(msgName) == UNITY_RPC_CONFIRM_OPERATION &&
                    eventMsg.ver == UNITY_OP_V1 && v1ptr->sequence == fakedSequence &&
                    (UnityWindowId)v1ptr->windowId == fakedWindowId &&
                    v1ptr->details.op == fakedOpType && v1ptr->allow;
      }));
   guestOps->UnityConfirmOperation(fakedOp, true);
   EXPECT_TRUE(msgMatch);

   testing::Mock::VerifyAndClearExpectations(guestOps);
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * GHIGuestOpsMKSControlUnitTest::TestShowUnityWindow --
 *
 *      Test ShowUnityWindow method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GHIGuestOpsMKSControlUnitTest, TestShowUnityWindow)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();
   GuestOpsMKSControlUTMock *guestOps = dynamic_cast<GuestOpsMKSControlUTMock *>(vm->GetGuestOps());

   bool msgMatch = false;
   std::string expectedMsg = "123";
   EXPECT_CALL(*guestOps, SendGHIRequestMock(_, _, _, _, _, _))
      .WillOnce(
         Invoke([&msgMatch, &expectedMsg](GHIChannelType channel, const char *msgName,
                                          const uint8 *msgData, uint32 msgDataLen,
                                          cui::AbortSlot onAbort, mksctrl::GHIResponseSlot onDone) {
            msgMatch =
               channel == GHI_CHANNEL_TOOLS_USER && utf::string(msgName) == UNITY_RPC_WINDOW_SHOW &&
               std::string((char *)msgData) == expectedMsg && msgDataLen == expectedMsg.size() + 1;
         }));
   guestOps->ShowUnityWindow(123);
   EXPECT_TRUE(msgMatch);

   testing::Mock::VerifyAndClearExpectations(guestOps);
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * GHIGuestOpsMKSControlUnitTest::TestHideUnityWindow --
 *
 *      Test HideUnityWindow method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GHIGuestOpsMKSControlUnitTest, TestHideUnityWindow)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();
   GuestOpsMKSControlUTMock *guestOps = dynamic_cast<GuestOpsMKSControlUTMock *>(vm->GetGuestOps());

   bool msgMatch = false;
   std::string expectedMsg = "123";
   EXPECT_CALL(*guestOps, SendGHIRequestMock(_, _, _, _, _, _))
      .WillOnce(
         Invoke([&msgMatch, &expectedMsg](GHIChannelType channel, const char *msgName,
                                          const uint8 *msgData, uint32 msgDataLen,
                                          cui::AbortSlot onAbort, mksctrl::GHIResponseSlot onDone) {
            msgMatch =
               channel == GHI_CHANNEL_TOOLS_USER && utf::string(msgName) == UNITY_RPC_WINDOW_HIDE &&
               std::string((char *)msgData) == expectedMsg && msgDataLen == expectedMsg.size() + 1;
         }));
   guestOps->HideUnityWindow(123);
   EXPECT_TRUE(msgMatch);

   testing::Mock::VerifyAndClearExpectations(guestOps);
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * GHIGuestOpsMKSControlUnitTest::TestEnterUnity --
 *
 *      Test EnterUnity method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GHIGuestOpsMKSControlUnitTest, TestEnterUnity)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();
   GuestOpsMKSControlUTMock *guestOps = dynamic_cast<GuestOpsMKSControlUTMock *>(vm->GetGuestOps());

   bool msgMatch = false;
   EXPECT_CALL(*guestOps, SendGHIRequestMock(_, _, _, _, _, _))
      .WillOnce(Invoke([&msgMatch](GHIChannelType channel, const char *msgName,
                                   const uint8 *msgData, uint32 msgDataLen, cui::AbortSlot onAbort,
                                   mksctrl::GHIResponseSlot onDone) {
         msgMatch = channel == GHI_CHANNEL_TOOLS_USER && utf::string(msgName) == UNITY_RPC_ENTER &&
                    msgData == NULL && msgDataLen == 0;
      }));
   guestOps->EnterUnity();
   EXPECT_TRUE(msgMatch);

   testing::Mock::VerifyAndClearExpectations(guestOps);
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * GHIGuestOpsMKSControlUnitTest::TestExitUnity --
 *
 *      Test ExitUnity method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GHIGuestOpsMKSControlUnitTest, TestExitUnity)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();
   GuestOpsMKSControlUTMock *guestOps = dynamic_cast<GuestOpsMKSControlUTMock *>(vm->GetGuestOps());

   bool msgMatch = false;
   EXPECT_CALL(*guestOps, SendGHIRequestMock(_, _, _, _, _, _))
      .WillOnce(Invoke([&msgMatch](GHIChannelType channel, const char *msgName,
                                   const uint8 *msgData, uint32 msgDataLen, cui::AbortSlot onAbort,
                                   mksctrl::GHIResponseSlot onDone) {
         msgMatch = channel == GHI_CHANNEL_TOOLS_USER && utf::string(msgName) == UNITY_RPC_EXIT &&
                    msgData == NULL && msgDataLen == 0;
      }));
   guestOps->ExitUnity();
   EXPECT_TRUE(msgMatch);

   testing::Mock::VerifyAndClearExpectations(guestOps);
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * GHIGuestOpsMKSControlUnitTest::TestRequestFullUnityUpdate --
 *
 *      Test RequestFullUnityUpdate method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GHIGuestOpsMKSControlUnitTest, TestRequestFullUnityUpdate)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();
   GuestOpsMKSControlUTMock *guestOps = dynamic_cast<GuestOpsMKSControlUTMock *>(vm->GetGuestOps());

   bool msgMatch = false;
   EXPECT_CALL(*guestOps, SendGHIRequestMock(_, _, _, _, _, _))
      .WillOnce(Invoke([&msgMatch](GHIChannelType channel, const char *msgName,
                                   const uint8 *msgData, uint32 msgDataLen, cui::AbortSlot onAbort,
                                   mksctrl::GHIResponseSlot onDone) {
         msgMatch = channel == GHI_CHANNEL_TOOLS_USER &&
                    utf::string(msgName) == UNITY_RPC_GET_UPDATE_FULL && msgData == NULL &&
                    msgDataLen == 0;
      }));
   guestOps->RequestFullUnityUpdate();
   EXPECT_TRUE(msgMatch);

   testing::Mock::VerifyAndClearExpectations(guestOps);
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * GHIGuestOpsMKSControlUnitTest::TestUnitySendMouseWheel --
 *
 *      Test UnitySendMouseWheel method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GHIGuestOpsMKSControlUnitTest, TestUnitySendMouseWheel)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();
   GuestOpsMKSControlUTMock *guestOps = dynamic_cast<GuestOpsMKSControlUTMock *>(vm->GetGuestOps());

   bool msgMatch = false;
   int32 fakedDeltaX = 1;
   int32 fakedDeltaY = 2;
   int32 fakedDeltaZ = 3;
   uint32 fakedModifierFlags = 0xff;
   EXPECT_CALL(*guestOps, SendGHIRequestMock(_, _, _, _, _, _))
      .WillOnce(
         Invoke([&msgMatch, fakedDeltaX, fakedDeltaY, fakedDeltaZ, fakedModifierFlags](
                   GHIChannelType channel, const char *msgName, const uint8 *msgData,
                   uint32 msgDataLen, cui::AbortSlot onAbort, mksctrl::GHIResponseSlot onDone) {
            UnityMouseWheel eventMsg;
            memset(&eventMsg, 0, sizeof eventMsg);
            if (!XdrUtil_Deserialize((char *)msgData, msgDataLen, (void *)xdr_UnityMouseWheel,
                                     &eventMsg)) {
               msgMatch = false;
               return;
            }
            cui::Guard xdrFree(sigc::bind(sigc::ptr_fun(xdr_free),
                                          reinterpret_cast<xdrproc_t>(xdr_UnityMouseWheel),
                                          reinterpret_cast<char *>(&eventMsg)));
            UnityMouseWheelV1 *v1ptr = eventMsg.UnityMouseWheel_u.mouseWheelV1;
            if (v1ptr == NULL) {
               msgMatch = false;
               return;
            }
            msgMatch = channel == GHI_CHANNEL_TOOLS_USER &&
                       utf::string(msgName) == UNITY_RPC_SEND_MOUSE_WHEEL &&
                       eventMsg.ver == UNITY_MOUSE_WHEEL_V1 && v1ptr->deltaX == fakedDeltaX &&
                       v1ptr->deltaY == fakedDeltaY && v1ptr->deltaZ == fakedDeltaZ &&
                       v1ptr->modifierFlags == fakedModifierFlags;
         }));
   guestOps->UnitySendMouseWheel(fakedDeltaX, fakedDeltaY, fakedDeltaZ, fakedModifierFlags);
   EXPECT_TRUE(msgMatch);

   testing::Mock::VerifyAndClearExpectations(guestOps);
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * GHIGuestOpsMKSControlUnitTest::TestOnUnityUpdateCB --
 *
 *      Test OnUnityUpdateCB and OnGHIUpdateNotified method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GHIGuestOpsMKSControlUnitTest, TestOnUnityUpdateCB)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();
   GuestOpsMKSControlUTMock *guestOps = dynamic_cast<GuestOpsMKSControlUTMock *>(vm->GetGuestOps());
   guestOps->verboseLogging = true;

   // Test callback when unity if off.
   std::vector<uint8> fakedMsg = {'u', 'n', 'i',  't', 'y', ' ', 'r', 'e', 'a', 'd',
                                  'y', ' ', '\0', ' ', '1', '2', '3', ' ', '1', ' '};
   bool msgMatch = false;
   EXPECT_CALL(*guestOps, SendGHIRequestMock(_, _, _, _, _, _))
      .WillOnce(Invoke([&msgMatch](GHIChannelType channel, const char *msgName,
                                   const uint8 *msgData, uint32 msgDataLen, cui::AbortSlot onAbort,
                                   mksctrl::GHIResponseSlot onDone) {
         msgMatch = channel == GHI_CHANNEL_TOOLS_USER && utf::string(msgName) == UNITY_RPC_EXIT;
      }));
   guestOps->FakeGuestToHostresponse(UNITY_GUEST_PUSH_UPDATE_CMD, (char *)fakedMsg.data(),
                                     fakedMsg.size());
   EXPECT_TRUE(msgMatch);
   testing::Mock::VerifyAndClearExpectations(guestOps);

   // Test callback when unity is on.
   guestOps->UpdateUnityState(true);
   EXPECT_CALL(*guestOps, SendGHIRequestMock(_, _, _, _, _, _)).Times(0);
   guestOps->FakeGuestToHostresponse(UNITY_GUEST_PUSH_UPDATE_CMD, (char *)fakedMsg.data(),
                                     fakedMsg.size());
   testing::Mock::VerifyAndClearExpectations(guestOps);
   delete vm;
}


static UnityWindowId gWindowOverlayIconAddedWindowId = 0;
static utf::string gWindowOverlayIconAddedDescription = "";
static cui::PNGData gWindowOverlayIconAddedPngData = {};
static bool gWindowOverlayIconAddedFromUnityUpdate = false;
static bool gWindowOverlayIconAddedCallbackCalled = false;

/*
 *-----------------------------------------------------------------------------
 *
 * FakedOnWindowOverlayIconAdded --
 *
 *    Faked callback when we have received window overlay icon from the guest.
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

void
FakedOnWindowOverlayIconAdded(const UnityWindowId id,         // IN
                              const utf::string &description, // IN
                              const cui::PNGData &data,       // IN
                              bool fromUnityUpdate)           // IN
{
   gWindowOverlayIconAddedWindowId = id;
   gWindowOverlayIconAddedDescription = description;
   gWindowOverlayIconAddedPngData = data;
   gWindowOverlayIconAddedFromUnityUpdate = fromUnityUpdate;
   gWindowOverlayIconAddedCallbackCalled = true;
}


static UnityWindowId gWindowOverlayIconDeletedWindowId = 0;
static bool gWindowOverlayIconDeletedCallbackCalled = false;

/*
 *-----------------------------------------------------------------------------
 *
 * FakedOnWindowOverlayIconDeleted --
 *
 *    Faked callback when guest tells us the overlay icon should be deleted.
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

void
FakedOnWindowOverlayIconDeleted(const UnityWindowId id) // IN
{
   gWindowOverlayIconDeletedWindowId = id;
   gWindowOverlayIconDeletedCallbackCalled = true;
}


/*
 *-----------------------------------------------------------------------------
 *
 * GHIGuestOpsMKSControlUnitTest::TestOnGuestWindowOverlayIconUpdateCB --
 *
 *      Test OnGuestWindowOverlayIconUpdateCB and OnGHIUpdateNotified method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GHIGuestOpsMKSControlUnitTest, TestOnGuestWindowOverlayIconUpdateCB)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();
   GuestOpsMKSControlUTMock *guestOps = dynamic_cast<GuestOpsMKSControlUTMock *>(vm->GetGuestOps());

   // Test icon is added.
   guestOps->windowOverlayIconAdded.connect(sigc::ptr_fun(&FakedOnWindowOverlayIconAdded));
   GHIOverlayIconV1 fakedGhiOverlayIconV1;
   GHIOverlayIcon fakedGhiOverlayIcon;
   memset(&fakedGhiOverlayIcon, 0, sizeof fakedGhiOverlayIcon);
   memset(&fakedGhiOverlayIconV1, 0, sizeof fakedGhiOverlayIconV1);
   fakedGhiOverlayIcon.ver = GHI_OVERLAY_ICON_V1;
   fakedGhiOverlayIcon.GHIOverlayIcon_u.overlayIconV1 = &fakedGhiOverlayIconV1;
   fakedGhiOverlayIconV1.op = GHI_OVERLAY_ICON_OP_ADD;
   UnityWindowId fakedWindowId = 123;
   std::string fakedDescription = "fakedDescription";
   std::vector<uint8> fakedPngData = {1, 2, 3, 4};
   fakedGhiOverlayIconV1.windowID = fakedWindowId;
   fakedGhiOverlayIconV1.description = (char *)fakedDescription.c_str();
   fakedGhiOverlayIconV1.pngData.pngData_len = fakedPngData.size();
   fakedGhiOverlayIconV1.pngData.pngData_val = (char *)fakedPngData.data();
   XDR xdrs;
   EXPECT_TRUE(DynXdr_Create(&xdrs) != NULL);
   EXPECT_TRUE(xdr_GHIOverlayIcon(&xdrs, &fakedGhiOverlayIcon));
   gWindowOverlayIconAddedCallbackCalled = false;
   guestOps->FakeGuestToHostresponse(GHI_GUEST_WINDOW_OVERLAY_ICON_UPDATE,
                                     reinterpret_cast<char *>(DynXdr_Get(&xdrs)),
                                     xdr_getpos(&xdrs));
   EXPECT_TRUE(gWindowOverlayIconAddedWindowId == fakedWindowId);
   EXPECT_TRUE(gWindowOverlayIconAddedDescription == fakedDescription.c_str());
   EXPECT_TRUE(gWindowOverlayIconAddedPngData == fakedPngData);
   EXPECT_TRUE(gWindowOverlayIconAddedFromUnityUpdate);
   EXPECT_TRUE(gWindowOverlayIconAddedCallbackCalled);

   // Test icon is deleted.
   guestOps->windowOverlayIconDeleted.connect(sigc::ptr_fun(&FakedOnWindowOverlayIconDeleted));
   memset(&fakedGhiOverlayIcon, 0, sizeof fakedGhiOverlayIcon);
   memset(&fakedGhiOverlayIconV1, 0, sizeof fakedGhiOverlayIconV1);
   fakedGhiOverlayIcon.ver = GHI_OVERLAY_ICON_V1;
   fakedGhiOverlayIcon.GHIOverlayIcon_u.overlayIconV1 = &fakedGhiOverlayIconV1;
   fakedGhiOverlayIconV1.op = GHI_OVERLAY_ICON_OP_DELETE;
   fakedWindowId = 456;
   fakedDescription = "fakedDeleteDescription";
   fakedPngData = {2, 3, 4, 5};
   fakedGhiOverlayIconV1.windowID = fakedWindowId;
   fakedGhiOverlayIconV1.description = (char *)fakedDescription.c_str();
   fakedGhiOverlayIconV1.pngData.pngData_len = fakedPngData.size();
   fakedGhiOverlayIconV1.pngData.pngData_val = (char *)fakedPngData.data();
   memset(&xdrs, 0, sizeof xdrs);
   EXPECT_TRUE(DynXdr_Create(&xdrs) != NULL);
   EXPECT_TRUE(xdr_GHIOverlayIcon(&xdrs, &fakedGhiOverlayIcon));
   gWindowOverlayIconDeletedCallbackCalled = false;
   guestOps->FakeGuestToHostresponse(GHI_GUEST_WINDOW_OVERLAY_ICON_UPDATE,
                                     reinterpret_cast<char *>(DynXdr_Get(&xdrs)),
                                     xdr_getpos(&xdrs));
   EXPECT_TRUE(gWindowOverlayIconDeletedWindowId == fakedWindowId);
   EXPECT_TRUE(gWindowOverlayIconDeletedCallbackCalled);

   // Test unknown operation.
   guestOps->windowOverlayIconDeleted.connect(sigc::ptr_fun(&FakedOnWindowOverlayIconDeleted));
   memset(&fakedGhiOverlayIcon, 0, sizeof fakedGhiOverlayIcon);
   memset(&fakedGhiOverlayIconV1, 0, sizeof fakedGhiOverlayIconV1);
   fakedGhiOverlayIcon.ver = GHI_OVERLAY_ICON_V1;
   fakedGhiOverlayIcon.GHIOverlayIcon_u.overlayIconV1 = &fakedGhiOverlayIconV1;
   fakedGhiOverlayIconV1.op = (GHIOverlayIconOp)(GHI_OVERLAY_ICON_OP_DELETE + 10000);
   fakedWindowId = 568;
   fakedDescription = "fakedDeleteDescription";
   fakedPngData = {2, 3, 4, 5};
   fakedGhiOverlayIconV1.windowID = fakedWindowId;
   fakedGhiOverlayIconV1.description = (char *)fakedDescription.c_str();
   fakedGhiOverlayIconV1.pngData.pngData_len = fakedPngData.size();
   fakedGhiOverlayIconV1.pngData.pngData_val = (char *)fakedPngData.data();
   memset(&xdrs, 0, sizeof xdrs);
   EXPECT_TRUE(DynXdr_Create(&xdrs) != NULL);
   EXPECT_TRUE(xdr_GHIOverlayIcon(&xdrs, &fakedGhiOverlayIcon));
   gWindowOverlayIconAddedCallbackCalled = false;
   gWindowOverlayIconDeletedCallbackCalled = false;
   guestOps->FakeGuestToHostresponse(GHI_GUEST_WINDOW_OVERLAY_ICON_UPDATE,
                                     reinterpret_cast<char *>(DynXdr_Get(&xdrs)),
                                     xdr_getpos(&xdrs));
   EXPECT_FALSE(gWindowOverlayIconAddedCallbackCalled);
   EXPECT_FALSE(gWindowOverlayIconDeletedCallbackCalled);

   // Test empty icon data.
   gWindowOverlayIconDeletedWindowId = 0;
   guestOps->windowOverlayIconDeleted.connect(sigc::ptr_fun(&FakedOnWindowOverlayIconDeleted));
   memset(&fakedGhiOverlayIcon, 0, sizeof fakedGhiOverlayIcon);
   memset(&fakedGhiOverlayIconV1, 0, sizeof fakedGhiOverlayIconV1);
   fakedGhiOverlayIcon.ver = GHI_OVERLAY_ICON_V1;
   fakedGhiOverlayIcon.GHIOverlayIcon_u.overlayIconV1 = NULL;
   memset(&xdrs, 0, sizeof xdrs);
   EXPECT_TRUE(DynXdr_Create(&xdrs) != NULL);
   EXPECT_TRUE(xdr_GHIOverlayIcon(&xdrs, &fakedGhiOverlayIcon));
   gWindowOverlayIconAddedCallbackCalled = false;
   gWindowOverlayIconDeletedCallbackCalled = false;
   guestOps->FakeGuestToHostresponse(GHI_GUEST_WINDOW_OVERLAY_ICON_UPDATE,
                                     reinterpret_cast<char *>(DynXdr_Get(&xdrs)),
                                     xdr_getpos(&xdrs));
   EXPECT_FALSE(gWindowOverlayIconAddedCallbackCalled);
   EXPECT_FALSE(gWindowOverlayIconDeletedCallbackCalled);

   // Test invalid GHIOverlayIcon structure.
   gWindowOverlayIconDeletedWindowId = 0;
   guestOps->windowOverlayIconDeleted.connect(sigc::ptr_fun(&FakedOnWindowOverlayIconDeleted));
   memset(&fakedGhiOverlayIcon, 0, sizeof fakedGhiOverlayIcon);
   memset(&fakedGhiOverlayIconV1, 0, sizeof fakedGhiOverlayIconV1);
   fakedGhiOverlayIcon.ver = GHI_OVERLAY_ICON_V1;
   fakedGhiOverlayIcon.GHIOverlayIcon_u.overlayIconV1 = &fakedGhiOverlayIconV1;
   fakedGhiOverlayIconV1.op = GHI_OVERLAY_ICON_OP_DELETE;
   fakedWindowId = 568;
   fakedDescription = "fakedDeleteDescription";
   fakedPngData = {2, 3, 4, 5};
   fakedGhiOverlayIconV1.windowID = fakedWindowId;
   fakedGhiOverlayIconV1.description = (char *)fakedDescription.c_str();
   fakedGhiOverlayIconV1.pngData.pngData_len = fakedPngData.size();
   fakedGhiOverlayIconV1.pngData.pngData_val = (char *)fakedPngData.data();
   memset(&xdrs, 0, sizeof xdrs);
   EXPECT_TRUE(DynXdr_Create(&xdrs) != NULL);
   EXPECT_TRUE(xdr_GHIOverlayIcon(&xdrs, &fakedGhiOverlayIcon));
   VMOCK(XdrUtil_Deserialize).WillOnce(false);
   gWindowOverlayIconAddedCallbackCalled = false;
   gWindowOverlayIconDeletedCallbackCalled = false;
   guestOps->FakeGuestToHostresponse(GHI_GUEST_WINDOW_OVERLAY_ICON_UPDATE,
                                     reinterpret_cast<char *>(DynXdr_Get(&xdrs)),
                                     xdr_getpos(&xdrs));
   EXPECT_TRUE(gWindowOverlayIconDeletedWindowId == 0);
   EXPECT_FALSE(gWindowOverlayIconAddedCallbackCalled);
   EXPECT_FALSE(gWindowOverlayIconDeletedCallbackCalled);

   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * GHIGuestOpsMKSControlUnitTest::TestOnGuestTrayIconUpdateCB --
 *
 *      Test OnGuestTrayIconUpdateCB method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GHIGuestOpsMKSControlUnitTest, TestOnGuestTrayIconUpdateCB)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();
   GuestOpsMKSControlUTMock *guestOps = dynamic_cast<GuestOpsMKSControlUTMock *>(vm->GetGuestOps());

   // Test add tray icon.
   std::vector<uint8> fakedPngData = {1, 2, 3, 4};
   GHITrayIconV1 fakedGhiTrayIconV1;
   GHITrayIcon fakedGhiTrayIcon;
   memset(&fakedGhiTrayIcon, 0, sizeof fakedGhiTrayIcon);
   memset(&fakedGhiTrayIconV1, 0, sizeof fakedGhiTrayIconV1);
   fakedGhiTrayIcon.ver = GHI_TRAY_ICON_V1;
   fakedGhiTrayIcon.GHITrayIcon_u.trayIconV1 = &fakedGhiTrayIconV1;
   fakedGhiTrayIconV1.op = GHI_TRAY_ICON_OP_ADD;
   fakedGhiTrayIconV1.iconID = (char *)"fakedIconId";
   fakedGhiTrayIconV1.flags |=
      GHI_TRAY_ICON_FLAG_PNGDATA | GHI_TRAY_ICON_FLAG_TOOLTIP | GHI_TRAY_ICON_FLAG_BLACKLISTKEY;
   fakedGhiTrayIconV1.pngData.pngData_val = (char *)fakedPngData.data();
   fakedGhiTrayIconV1.pngData.pngData_len = fakedPngData.size();
   fakedGhiTrayIconV1.tooltip = (char *)"fakedTooltip";
   fakedGhiTrayIconV1.blacklistKey = (char *)"fakedBlackListKey";
   XDR xdrs;
   EXPECT_TRUE(DynXdr_Create(&xdrs) != NULL);
   EXPECT_TRUE(xdr_GHITrayIcon(&xdrs, &fakedGhiTrayIcon));
   guestOps->FakeGuestToHostresponse(
      GHI_GUEST_TRAY_ICON_UPDATE, reinterpret_cast<char *>(DynXdr_Get(&xdrs)), xdr_getpos(&xdrs));
   EXPECT_TRUE(guestOps->GetNotificationAreaItemIcon("fakedIconId") == fakedPngData);
   EXPECT_TRUE(guestOps->GetNotificationAreaItemTooltip("fakedIconId") == "fakedTooltip");
   EXPECT_TRUE(guestOps->GetNotificationAreaItemBlacklistKey("fakedIconId") == "fakedBlackListKey");

   // Test modify tray icon.
   memset(&fakedGhiTrayIcon, 0, sizeof fakedGhiTrayIcon);
   memset(&fakedGhiTrayIconV1, 0, sizeof fakedGhiTrayIconV1);
   fakedPngData = {2, 3, 4, 5};
   fakedGhiTrayIcon.ver = GHI_TRAY_ICON_V1;
   fakedGhiTrayIcon.GHITrayIcon_u.trayIconV1 = &fakedGhiTrayIconV1;
   fakedGhiTrayIconV1.op = GHI_TRAY_ICON_OP_MODIFY;
   fakedGhiTrayIconV1.iconID = (char *)"fakedIconId";
   fakedGhiTrayIconV1.flags |=
      GHI_TRAY_ICON_FLAG_PNGDATA | GHI_TRAY_ICON_FLAG_TOOLTIP | GHI_TRAY_ICON_FLAG_BLACKLISTKEY;
   fakedGhiTrayIconV1.pngData.pngData_val = (char *)fakedPngData.data();
   fakedGhiTrayIconV1.pngData.pngData_len = fakedPngData.size();
   fakedGhiTrayIconV1.tooltip = (char *)"fakedTooltipNew";
   fakedGhiTrayIconV1.blacklistKey = (char *)"fakedBlackListKeyNew";
   memset(&xdrs, 0, sizeof xdrs);
   EXPECT_TRUE(DynXdr_Create(&xdrs) != NULL);
   EXPECT_TRUE(xdr_GHITrayIcon(&xdrs, &fakedGhiTrayIcon));
   guestOps->FakeGuestToHostresponse(
      GHI_GUEST_TRAY_ICON_UPDATE, reinterpret_cast<char *>(DynXdr_Get(&xdrs)), xdr_getpos(&xdrs));
   EXPECT_TRUE(guestOps->GetNotificationAreaItemIcon("fakedIconId") == fakedPngData);
   EXPECT_TRUE(guestOps->GetNotificationAreaItemTooltip("fakedIconId") == "fakedTooltipNew");
   EXPECT_TRUE(guestOps->GetNotificationAreaItemBlacklistKey("fakedIconId") ==
               "fakedBlackListKeyNew");

   // Test modify tray icon with invalid iconID.
   memset(&fakedGhiTrayIcon, 0, sizeof fakedGhiTrayIcon);
   memset(&fakedGhiTrayIconV1, 0, sizeof fakedGhiTrayIconV1);
   fakedGhiTrayIcon.ver = GHI_TRAY_ICON_V1;
   fakedGhiTrayIcon.GHITrayIcon_u.trayIconV1 = &fakedGhiTrayIconV1;
   fakedGhiTrayIconV1.op = GHI_TRAY_ICON_OP_MODIFY;
   fakedGhiTrayIconV1.iconID = (char *)"fakedIconIdOld";
   fakedGhiTrayIconV1.flags |=
      GHI_TRAY_ICON_FLAG_PNGDATA | GHI_TRAY_ICON_FLAG_TOOLTIP | GHI_TRAY_ICON_FLAG_BLACKLISTKEY;
   fakedGhiTrayIconV1.pngData.pngData_val = (char *)fakedPngData.data();
   fakedGhiTrayIconV1.pngData.pngData_len = fakedPngData.size();
   fakedGhiTrayIconV1.tooltip = (char *)"fakedTooltipOld";
   fakedGhiTrayIconV1.blacklistKey = (char *)"fakedBlackListKeyOld";
   memset(&xdrs, 0, sizeof xdrs);
   EXPECT_TRUE(DynXdr_Create(&xdrs) != NULL);
   EXPECT_TRUE(xdr_GHITrayIcon(&xdrs, &fakedGhiTrayIcon));
   guestOps->FakeGuestToHostresponse(
      GHI_GUEST_TRAY_ICON_UPDATE, reinterpret_cast<char *>(DynXdr_Get(&xdrs)), xdr_getpos(&xdrs));
   EXPECT_TRUE(guestOps->GetNotificationAreaItemIcon("fakedIconId") == fakedPngData);
   EXPECT_TRUE(guestOps->GetNotificationAreaItemTooltip("fakedIconId") == "fakedTooltipNew");
   EXPECT_TRUE(guestOps->GetNotificationAreaItemBlacklistKey("fakedIconId") ==
               "fakedBlackListKeyNew");
   EXPECT_TRUE(guestOps->GetNotificationAreaItemIcon("fakedIconIdOld").empty());
   EXPECT_TRUE(guestOps->GetNotificationAreaItemTooltip("fakedIconIdOld").size() == 0);
   EXPECT_TRUE(guestOps->GetNotificationAreaItemBlacklistKey("fakedIconIdOld").size() == 0);

   // Test delete tray icon with invalid iconID.
   memset(&fakedGhiTrayIcon, 0, sizeof fakedGhiTrayIcon);
   memset(&fakedGhiTrayIconV1, 0, sizeof fakedGhiTrayIconV1);
   fakedGhiTrayIcon.ver = GHI_TRAY_ICON_V1;
   fakedGhiTrayIcon.GHITrayIcon_u.trayIconV1 = &fakedGhiTrayIconV1;
   fakedGhiTrayIconV1.op = GHI_TRAY_ICON_OP_DELETE;
   fakedGhiTrayIconV1.iconID = (char *)"fakedIconIdOld";
   fakedGhiTrayIconV1.flags |=
      GHI_TRAY_ICON_FLAG_PNGDATA | GHI_TRAY_ICON_FLAG_TOOLTIP | GHI_TRAY_ICON_FLAG_BLACKLISTKEY;
   fakedGhiTrayIconV1.pngData.pngData_val = (char *)fakedPngData.data();
   fakedGhiTrayIconV1.pngData.pngData_len = fakedPngData.size();
   fakedGhiTrayIconV1.tooltip = (char *)"fakedTooltipOld";
   fakedGhiTrayIconV1.blacklistKey = (char *)"fakedBlackListKeyOld";
   memset(&xdrs, 0, sizeof xdrs);
   EXPECT_TRUE(DynXdr_Create(&xdrs) != NULL);
   EXPECT_TRUE(xdr_GHITrayIcon(&xdrs, &fakedGhiTrayIcon));
   guestOps->FakeGuestToHostresponse(
      GHI_GUEST_TRAY_ICON_UPDATE, reinterpret_cast<char *>(DynXdr_Get(&xdrs)), xdr_getpos(&xdrs));
   EXPECT_TRUE(guestOps->GetNotificationAreaItemIcon("fakedIconId") == fakedPngData);
   EXPECT_TRUE(guestOps->GetNotificationAreaItemTooltip("fakedIconId") == "fakedTooltipNew");
   EXPECT_TRUE(guestOps->GetNotificationAreaItemBlacklistKey("fakedIconId") ==
               "fakedBlackListKeyNew");
   EXPECT_TRUE(guestOps->GetNotificationAreaItemIcon("fakedIconIdOld").empty());
   EXPECT_TRUE(guestOps->GetNotificationAreaItemTooltip("fakedIconIdOld").size() == 0);
   EXPECT_TRUE(guestOps->GetNotificationAreaItemBlacklistKey("fakedIconIdOld").size() == 0);

   // Test delete tray icon.
   memset(&fakedGhiTrayIcon, 0, sizeof fakedGhiTrayIcon);
   memset(&fakedGhiTrayIconV1, 0, sizeof fakedGhiTrayIconV1);
   fakedGhiTrayIcon.ver = GHI_TRAY_ICON_V1;
   fakedGhiTrayIcon.GHITrayIcon_u.trayIconV1 = &fakedGhiTrayIconV1;
   fakedGhiTrayIconV1.op = GHI_TRAY_ICON_OP_DELETE;
   fakedGhiTrayIconV1.iconID = (char *)"fakedIconId";
   fakedGhiTrayIconV1.flags |=
      GHI_TRAY_ICON_FLAG_PNGDATA | GHI_TRAY_ICON_FLAG_TOOLTIP | GHI_TRAY_ICON_FLAG_BLACKLISTKEY;
   fakedGhiTrayIconV1.pngData.pngData_val = (char *)fakedPngData.data();
   fakedGhiTrayIconV1.pngData.pngData_len = fakedPngData.size();
   fakedGhiTrayIconV1.tooltip = (char *)"fakedTooltipNew";
   fakedGhiTrayIconV1.blacklistKey = (char *)"fakedBlackListKeyNew";
   memset(&xdrs, 0, sizeof xdrs);
   EXPECT_TRUE(DynXdr_Create(&xdrs) != NULL);
   EXPECT_TRUE(xdr_GHITrayIcon(&xdrs, &fakedGhiTrayIcon));
   guestOps->FakeGuestToHostresponse(
      GHI_GUEST_TRAY_ICON_UPDATE, reinterpret_cast<char *>(DynXdr_Get(&xdrs)), xdr_getpos(&xdrs));
   EXPECT_TRUE(guestOps->GetNotificationAreaItemIcon("fakedIconId").empty());
   EXPECT_TRUE(guestOps->GetNotificationAreaItemTooltip("fakedIconId").size() == 0);
   EXPECT_TRUE(guestOps->GetNotificationAreaItemBlacklistKey("fakedIconId").size() == 0);

   // Test invalid tray iconID.
   memset(&fakedGhiTrayIcon, 0, sizeof fakedGhiTrayIcon);
   memset(&fakedGhiTrayIconV1, 0, sizeof fakedGhiTrayIconV1);
   fakedGhiTrayIcon.ver = GHI_TRAY_ICON_V1;
   fakedGhiTrayIcon.GHITrayIcon_u.trayIconV1 = &fakedGhiTrayIconV1;
   fakedGhiTrayIconV1.op = GHI_TRAY_ICON_OP_ADD;
   fakedGhiTrayIconV1.iconID = (char *)"\xf8\xa1\xa1\xa1\xa1";
   fakedGhiTrayIconV1.flags |=
      GHI_TRAY_ICON_FLAG_PNGDATA | GHI_TRAY_ICON_FLAG_TOOLTIP | GHI_TRAY_ICON_FLAG_BLACKLISTKEY;
   fakedGhiTrayIconV1.pngData.pngData_val = (char *)fakedPngData.data();
   fakedGhiTrayIconV1.pngData.pngData_len = fakedPngData.size();
   fakedGhiTrayIconV1.tooltip = (char *)"fakedTooltip";
   fakedGhiTrayIconV1.blacklistKey = (char *)"fakedBlackListKey";
   memset(&xdrs, 0, sizeof xdrs);
   EXPECT_TRUE(DynXdr_Create(&xdrs) != NULL);
   EXPECT_TRUE(xdr_GHITrayIcon(&xdrs, &fakedGhiTrayIcon));
   guestOps->FakeGuestToHostresponse(
      GHI_GUEST_TRAY_ICON_UPDATE, reinterpret_cast<char *>(DynXdr_Get(&xdrs)), xdr_getpos(&xdrs));
   EXPECT_TRUE(guestOps->GetNotificationAreaItemIcon("fakedIconId").empty());
   EXPECT_TRUE(guestOps->GetNotificationAreaItemTooltip("fakedIconId").size() == 0);
   EXPECT_TRUE(guestOps->GetNotificationAreaItemBlacklistKey("fakedIconId").size() == 0);

   // Test invalid tray icon detail.
   memset(&fakedGhiTrayIcon, 0, sizeof fakedGhiTrayIcon);
   memset(&fakedGhiTrayIconV1, 0, sizeof fakedGhiTrayIconV1);
   fakedGhiTrayIcon.ver = GHI_TRAY_ICON_V1;
   fakedGhiTrayIcon.GHITrayIcon_u.trayIconV1 = NULL;
   memset(&xdrs, 0, sizeof xdrs);
   EXPECT_TRUE(DynXdr_Create(&xdrs) != NULL);
   EXPECT_TRUE(xdr_GHITrayIcon(&xdrs, &fakedGhiTrayIcon));
   guestOps->FakeGuestToHostresponse(
      GHI_GUEST_TRAY_ICON_UPDATE, reinterpret_cast<char *>(DynXdr_Get(&xdrs)), xdr_getpos(&xdrs));
   EXPECT_TRUE(guestOps->GetNotificationAreaItemIcon("fakedIconId").empty());
   EXPECT_TRUE(guestOps->GetNotificationAreaItemTooltip("fakedIconId").size() == 0);
   EXPECT_TRUE(guestOps->GetNotificationAreaItemBlacklistKey("fakedIconId").size() == 0);

   // Test vnvalid GHITrayIcon structure.
   memset(&fakedGhiTrayIcon, 0, sizeof fakedGhiTrayIcon);
   memset(&fakedGhiTrayIconV1, 0, sizeof fakedGhiTrayIconV1);
   fakedGhiTrayIcon.ver = GHI_TRAY_ICON_V1;
   fakedGhiTrayIcon.GHITrayIcon_u.trayIconV1 = &fakedGhiTrayIconV1;
   fakedGhiTrayIconV1.op = GHI_TRAY_ICON_OP_ADD;
   fakedGhiTrayIconV1.iconID = (char *)"fakedIconId";
   fakedGhiTrayIconV1.flags |=
      GHI_TRAY_ICON_FLAG_PNGDATA | GHI_TRAY_ICON_FLAG_TOOLTIP | GHI_TRAY_ICON_FLAG_BLACKLISTKEY;
   fakedGhiTrayIconV1.pngData.pngData_val = (char *)fakedPngData.data();
   fakedGhiTrayIconV1.pngData.pngData_len = fakedPngData.size();
   fakedGhiTrayIconV1.tooltip = (char *)"fakedTooltip";
   fakedGhiTrayIconV1.blacklistKey = (char *)"fakedBlackListKey";
   memset(&xdrs, 0, sizeof xdrs);
   EXPECT_TRUE(DynXdr_Create(&xdrs) != NULL);
   EXPECT_TRUE(xdr_GHITrayIcon(&xdrs, &fakedGhiTrayIcon));
   VMOCK(XdrUtil_Deserialize).WillOnce(false);
   guestOps->FakeGuestToHostresponse(
      GHI_GUEST_TRAY_ICON_UPDATE, reinterpret_cast<char *>(DynXdr_Get(&xdrs)), xdr_getpos(&xdrs));
   EXPECT_TRUE(guestOps->GetNotificationAreaItemIcon("fakedIconId").empty());
   EXPECT_TRUE(guestOps->GetNotificationAreaItemTooltip("fakedIconId").size() == 0);
   EXPECT_TRUE(guestOps->GetNotificationAreaItemBlacklistKey("fakedIconId").size() == 0);

   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * GHIGuestOpsMKSControlUnitTest::TestClearAllTrayIcons --
 *
 *      Test ClearAllTrayIcons method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GHIGuestOpsMKSControlUnitTest, TestClearAllTrayIcons)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();
   GuestOpsMKSControlUTMock *guestOps = dynamic_cast<GuestOpsMKSControlUTMock *>(vm->GetGuestOps());

   // Test ClearAllTrayIcons from StartNotificationAreaUpdates.
   std::vector<uint8> fakedPngData = {1, 2, 3, 4};
   GHITrayIconV1 fakedGhiTrayIconV1;
   GHITrayIcon fakedGhiTrayIcon;
   memset(&fakedGhiTrayIcon, 0, sizeof fakedGhiTrayIcon);
   memset(&fakedGhiTrayIconV1, 0, sizeof fakedGhiTrayIconV1);
   fakedGhiTrayIcon.ver = GHI_TRAY_ICON_V1;
   fakedGhiTrayIcon.GHITrayIcon_u.trayIconV1 = &fakedGhiTrayIconV1;
   fakedGhiTrayIconV1.op = GHI_TRAY_ICON_OP_ADD;
   fakedGhiTrayIconV1.iconID = (char *)"fakedIconId";
   fakedGhiTrayIconV1.flags |=
      GHI_TRAY_ICON_FLAG_PNGDATA | GHI_TRAY_ICON_FLAG_TOOLTIP | GHI_TRAY_ICON_FLAG_BLACKLISTKEY;
   fakedGhiTrayIconV1.pngData.pngData_val = (char *)fakedPngData.data();
   fakedGhiTrayIconV1.pngData.pngData_len = fakedPngData.size();
   fakedGhiTrayIconV1.tooltip = (char *)"fakedTooltip";
   fakedGhiTrayIconV1.blacklistKey = (char *)"fakedBlackListKey";
   XDR xdrs;
   EXPECT_TRUE(DynXdr_Create(&xdrs) != NULL);
   EXPECT_TRUE(xdr_GHITrayIcon(&xdrs, &fakedGhiTrayIcon));
   guestOps->FakeGuestToHostresponse(
      GHI_GUEST_TRAY_ICON_UPDATE, reinterpret_cast<char *>(DynXdr_Get(&xdrs)), xdr_getpos(&xdrs));
   EXPECT_TRUE(guestOps->GetNotificationAreaItemIcon("fakedIconId") == fakedPngData);
   EXPECT_TRUE(guestOps->GetNotificationAreaItemTooltip("fakedIconId") == "fakedTooltip");
   EXPECT_TRUE(guestOps->GetNotificationAreaItemBlacklistKey("fakedIconId") == "fakedBlackListKey");
   guestOps->StartNotificationAreaUpdates();
   EXPECT_TRUE(guestOps->GetNotificationAreaItemIcon("fakedIconId").empty());
   EXPECT_TRUE(guestOps->GetNotificationAreaItemTooltip("fakedIconId").size() == 0);
   EXPECT_TRUE(guestOps->GetNotificationAreaItemBlacklistKey("fakedIconId").size() == 0);

   // Test ClearAllTrayIcons from StopNotificationAreaUpdates.
   memset(&fakedGhiTrayIcon, 0, sizeof fakedGhiTrayIcon);
   memset(&fakedGhiTrayIconV1, 0, sizeof fakedGhiTrayIconV1);
   fakedGhiTrayIcon.ver = GHI_TRAY_ICON_V1;
   fakedGhiTrayIcon.GHITrayIcon_u.trayIconV1 = &fakedGhiTrayIconV1;
   fakedGhiTrayIconV1.op = GHI_TRAY_ICON_OP_ADD;
   fakedGhiTrayIconV1.iconID = (char *)"fakedIconId";
   fakedGhiTrayIconV1.flags |=
      GHI_TRAY_ICON_FLAG_PNGDATA | GHI_TRAY_ICON_FLAG_TOOLTIP | GHI_TRAY_ICON_FLAG_BLACKLISTKEY;
   fakedGhiTrayIconV1.pngData.pngData_val = (char *)fakedPngData.data();
   fakedGhiTrayIconV1.pngData.pngData_len = fakedPngData.size();
   fakedGhiTrayIconV1.tooltip = (char *)"fakedTooltip";
   fakedGhiTrayIconV1.blacklistKey = (char *)"fakedBlackListKey";
   memset(&xdrs, 0, sizeof xdrs);
   EXPECT_TRUE(DynXdr_Create(&xdrs) != NULL);
   EXPECT_TRUE(xdr_GHITrayIcon(&xdrs, &fakedGhiTrayIcon));
   guestOps->FakeGuestToHostresponse(
      GHI_GUEST_TRAY_ICON_UPDATE, reinterpret_cast<char *>(DynXdr_Get(&xdrs)), xdr_getpos(&xdrs));
   EXPECT_TRUE(guestOps->GetNotificationAreaItemIcon("fakedIconId") == fakedPngData);
   EXPECT_TRUE(guestOps->GetNotificationAreaItemTooltip("fakedIconId") == "fakedTooltip");
   EXPECT_TRUE(guestOps->GetNotificationAreaItemBlacklistKey("fakedIconId") == "fakedBlackListKey");
   guestOps->StopNotificationAreaUpdates();
   EXPECT_TRUE(guestOps->GetNotificationAreaItemIcon("fakedIconId").empty());
   EXPECT_TRUE(guestOps->GetNotificationAreaItemTooltip("fakedIconId").size() == 0);
   EXPECT_TRUE(guestOps->GetNotificationAreaItemBlacklistKey("fakedIconId").size() == 0);

   delete vm;
}


static std::vector<cui::GuestOps::UnityOperation> gUnityOperation = {};

/*
 *-----------------------------------------------------------------------------
 *
 * FakedOnUnityRequestOperation --
 *
 *    Faked callback from the guest to request an operation such as window
 *    minimize.
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

void
FakedOnUnityRequestOperation(const std::vector<cui::GuestOps::UnityOperation> &unityOperation) // IN
{
   gUnityOperation = unityOperation;
}


/*
 *-----------------------------------------------------------------------------
 *
 * GHIGuestOpsMKSControlUnitTest::TestOnUnityGuestRequestOperationCB --
 *
 *      Test OnUnityGuestRequestOperationCB method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GHIGuestOpsMKSControlUnitTest, TestOnUnityGuestRequestOperationCB)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();
   GuestOpsMKSControlUTMock *guestOps = dynamic_cast<GuestOpsMKSControlUTMock *>(vm->GetGuestOps());

   // Test valid operation.
   int fakedWindowId = 123;
   int fakedSequence = 456;
   guestOps->unityRequestOperation.connect(sigc::ptr_fun(&FakedOnUnityRequestOperation));
   UnityRequestOperationV1 fakedUnityRequestV1;
   UnityRequestOperation fakedUnityRequest;
   memset(&fakedUnityRequest, 0, sizeof fakedUnityRequest);
   memset(&fakedUnityRequestV1, 0, sizeof fakedUnityRequestV1);
   fakedUnityRequest.ver = UNITY_OP_V1;
   fakedUnityRequest.UnityRequestOperation_u.unityRequestOpV1 = &fakedUnityRequestV1;
   fakedUnityRequestV1.windowId = fakedWindowId;
   fakedUnityRequestV1.sequence = fakedSequence;
   fakedUnityRequestV1.details.op = MINIMIZE;
   XDR xdrs;
   EXPECT_TRUE(DynXdr_Create(&xdrs) != NULL);
   EXPECT_TRUE(xdr_UnityRequestOperation(&xdrs, &fakedUnityRequest));
   gUnityOperation.clear();
   guestOps->FakeGuestToHostresponse(UNITY_GUEST_REQUEST_OPERATION,
                                     reinterpret_cast<char *>(DynXdr_Get(&xdrs)),
                                     xdr_getpos(&xdrs));
   EXPECT_TRUE(gUnityOperation.size() == 1 &&
               gUnityOperation[0].type == cui::GuestOpsMKSControl::UNITY_OPERATION_TYPE_MINIMIZE &&
               (int)gUnityOperation[0].windowId == fakedWindowId &&
               gUnityOperation[0].sequence == fakedSequence);

   // Test UnityRequestOperationV1 pointer is NULL.
   gUnityOperation.clear();
   memset(&fakedUnityRequest, 0, sizeof fakedUnityRequest);
   memset(&fakedUnityRequestV1, 0, sizeof fakedUnityRequestV1);
   fakedUnityRequest.ver = UNITY_OP_V1;
   fakedUnityRequest.UnityRequestOperation_u.unityRequestOpV1 = NULL;
   memset(&xdrs, 0, sizeof xdrs);
   EXPECT_TRUE(DynXdr_Create(&xdrs) != NULL);
   EXPECT_TRUE(xdr_UnityRequestOperation(&xdrs, &fakedUnityRequest));
   gUnityOperation.clear();
   guestOps->FakeGuestToHostresponse(UNITY_GUEST_REQUEST_OPERATION,
                                     reinterpret_cast<char *>(DynXdr_Get(&xdrs)),
                                     xdr_getpos(&xdrs));
   EXPECT_TRUE(gUnityOperation.empty());

   // Test Invalid UnityRequestOperation structure.
   memset(&fakedUnityRequest, 0, sizeof fakedUnityRequest);
   memset(&fakedUnityRequestV1, 0, sizeof fakedUnityRequestV1);
   fakedUnityRequest.ver = UNITY_OP_V1;
   fakedUnityRequest.UnityRequestOperation_u.unityRequestOpV1 = &fakedUnityRequestV1;
   fakedUnityRequestV1.windowId = fakedWindowId;
   fakedUnityRequestV1.sequence = fakedSequence;
   fakedUnityRequestV1.details.op = MINIMIZE;
   memset(&xdrs, 0, sizeof xdrs);
   EXPECT_TRUE(DynXdr_Create(&xdrs) != NULL);
   EXPECT_TRUE(xdr_UnityRequestOperation(&xdrs, &fakedUnityRequest));
   gUnityOperation.clear();
   VMOCK(XdrUtil_Deserialize).Will(false);
   guestOps->FakeGuestToHostresponse(UNITY_GUEST_REQUEST_OPERATION,
                                     reinterpret_cast<char *>(DynXdr_Get(&xdrs)),
                                     xdr_getpos(&xdrs));
   EXPECT_TRUE(gUnityOperation.empty());

   // Test unknown operation type.
   gUnityOperation.clear();
   memset(&fakedUnityRequest, 0, sizeof fakedUnityRequest);
   memset(&fakedUnityRequestV1, 0, sizeof fakedUnityRequestV1);
   fakedUnityRequestV1.windowId = fakedWindowId;
   fakedUnityRequestV1.sequence = fakedSequence;
   fakedUnityRequestV1.details.op = UNITYOP_UNKNOWN;
   VMOCK(XdrUtil_Deserialize)
      .WillOnce(
         [&fakedUnityRequestV1](const void *data, size_t dataLen, void *xdrProc, void *dest) {
            UnityRequestOperation *unityRequest = (UnityRequestOperation *)dest;
            unityRequest->UnityRequestOperation_u.unityRequestOpV1 = &fakedUnityRequestV1;
            return true;
         });
   guestOps->FakeGuestToHostresponse(UNITY_GUEST_REQUEST_OPERATION, "", 0);
   EXPECT_TRUE(gUnityOperation.empty());

   delete vm;
}


static UnityWindowId gOnWindowContentsUpdateWindowId = 0;
static cui::Size gOnWindowContentsUpdateSize;
static cui::PNGData gOnWindowContentsUpdatePngData = {};

/*
 *-----------------------------------------------------------------------------
 *
 * FakedOnWindowContentsUpdated --
 *
 *    Faked callback when new window contents arrive from the guest.
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

void
FakedOnWindowContentsUpdated(const UnityWindowId windowId, // IN
                             const cui::Size size,         // IN
                             const cui::PNGData &pngData)  // IN
{
   gOnWindowContentsUpdateWindowId = windowId;
   gOnWindowContentsUpdateSize = size;
   gOnWindowContentsUpdatePngData = pngData;
}


/*
 *-----------------------------------------------------------------------------
 *
 * GHIGuestOpsMKSControlUnitTest::TestOnGuestWindowContentCBs --
 *
 *      Test valid scenario in
 *          OnGuestWindowContentStartCB,
 *          OnGuestWindowContentChunkCB,
 *          OnGuestWindowContentEndCB
 *      method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GHIGuestOpsMKSControlUnitTest, TestOnGuestWindowContentCBs)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();
   GuestOpsMKSControlUTMock *guestOps = dynamic_cast<GuestOpsMKSControlUTMock *>(vm->GetGuestOps());

   gOnWindowContentsUpdateSize = {0, 0};
   gOnWindowContentsUpdatePngData.clear();
   gOnWindowContentsUpdateWindowId = 0;

   // Content update start.
   int fakedWindowId = 123;
   int fakedImageWidth = 2;
   int fakedImageHeight = 4;
   int fakedImageLength = fakedImageWidth * fakedImageHeight;
   guestOps->windowContentsUpdated.connect(sigc::ptr_fun(&FakedOnWindowContentsUpdated));
   UnityWindowContentsStartV1 fakedUnityWindowContentsStartV1;
   UnityWindowContentsStart fakedUnityWindowContentsStart;
   memset(&fakedUnityWindowContentsStart, 0, sizeof fakedUnityWindowContentsStart);
   memset(&fakedUnityWindowContentsStartV1, 0, sizeof fakedUnityWindowContentsStartV1);
   fakedUnityWindowContentsStart.ver = UNITY_WINDOW_CONTENTS_V1;
   fakedUnityWindowContentsStart.UnityWindowContentsStart_u.startV1 =
      &fakedUnityWindowContentsStartV1;
   fakedUnityWindowContentsStartV1.windowID = fakedWindowId;
   fakedUnityWindowContentsStartV1.imageWidth = fakedImageWidth;
   fakedUnityWindowContentsStartV1.imageHeight = fakedImageHeight;
   fakedUnityWindowContentsStartV1.imageLength = fakedImageLength;
   XDR xdrs;
   memset(&xdrs, 0, sizeof xdrs);
   EXPECT_TRUE(DynXdr_Create(&xdrs) != NULL);
   EXPECT_TRUE(xdr_UnityWindowContentsStart(&xdrs, &fakedUnityWindowContentsStart));
   guestOps->FakeGuestToHostresponse(UNITY_GUEST_WINDOW_CONTENTS_START,
                                     reinterpret_cast<char *>(DynXdr_Get(&xdrs)),
                                     xdr_getpos(&xdrs));

   // Content update in progress with chunk.
   std::vector<uint8> fakedImageData = {1, 2, 3, 4, 5, 6, 7, 8};
   UnityWindowContentsChunkV1 fakedUnityWindowContentsChunkV1;
   UnityWindowContentsChunk fakedUnityWindowContentsChunk;
   memset(&fakedUnityWindowContentsChunk, 0, sizeof fakedUnityWindowContentsChunk);
   memset(&fakedUnityWindowContentsChunkV1, 0, sizeof fakedUnityWindowContentsChunkV1);
   fakedUnityWindowContentsChunk.ver = UNITY_WINDOW_CONTENTS_V1;
   fakedUnityWindowContentsChunk.UnityWindowContentsChunk_u.chunkV1 =
      &fakedUnityWindowContentsChunkV1;
   fakedUnityWindowContentsChunkV1.windowID = fakedWindowId;
   fakedUnityWindowContentsChunkV1.chunkID = 0;
   fakedUnityWindowContentsChunkV1.data.data_len = fakedImageData.size();
   fakedUnityWindowContentsChunkV1.data.data_val = (char *)fakedImageData.data();
   memset(&xdrs, 0, sizeof xdrs);
   EXPECT_TRUE(DynXdr_Create(&xdrs) != NULL);
   EXPECT_TRUE(xdr_UnityWindowContentsChunk(&xdrs, &fakedUnityWindowContentsChunk));
   guestOps->FakeGuestToHostresponse(UNITY_GUEST_WINDOW_CONTENTS_CHUNK,
                                     reinterpret_cast<char *>(DynXdr_Get(&xdrs)),
                                     xdr_getpos(&xdrs));

   // Content update end.
   UnityWindowContentsEndV1 fakedUnityWindowContentsEndV1;
   UnityWindowContentsEnd fakedUnityWindowContentsEnd;
   memset(&fakedUnityWindowContentsEnd, 0, sizeof fakedUnityWindowContentsEnd);
   memset(&fakedUnityWindowContentsEndV1, 0, sizeof fakedUnityWindowContentsEndV1);
   fakedUnityWindowContentsEnd.ver = UNITY_WINDOW_CONTENTS_V1;
   fakedUnityWindowContentsEnd.UnityWindowContentsEnd_u.endV1 = &fakedUnityWindowContentsEndV1;
   fakedUnityWindowContentsEndV1.windowID = fakedWindowId;
   memset(&xdrs, 0, sizeof xdrs);
   EXPECT_TRUE(DynXdr_Create(&xdrs) != NULL);
   EXPECT_TRUE(xdr_UnityWindowContentsEnd(&xdrs, &fakedUnityWindowContentsEnd));
   guestOps->FakeGuestToHostresponse(UNITY_GUEST_WINDOW_CONTENTS_END,
                                     reinterpret_cast<char *>(DynXdr_Get(&xdrs)),
                                     xdr_getpos(&xdrs));
   EXPECT_TRUE((int)gOnWindowContentsUpdateWindowId == fakedWindowId &&
               gOnWindowContentsUpdateSize.width == fakedImageWidth &&
               gOnWindowContentsUpdateSize.height == fakedImageHeight &&
               gOnWindowContentsUpdatePngData == fakedImageData);

   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * GHIGuestOpsMKSControlUnitTest::TestOnGuestWindowContentStartCB --
 *
 *      Test invalid scenario in OnGuestWindowContentStartCB method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GHIGuestOpsMKSControlUnitTest, TestOnGuestWindowContentStartCB)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();
   GuestOpsMKSControlUTMock *guestOps = dynamic_cast<GuestOpsMKSControlUTMock *>(vm->GetGuestOps());

   gOnWindowContentsUpdateSize = {0, 0};
   gOnWindowContentsUpdatePngData.clear();
   gOnWindowContentsUpdateWindowId = 0;

   // Test image size is too big.
   int fakedWindowId = 123;
   int fakedImageWidth = 2;
   int fakedImageHeight = 4;
   int fakedImageLength = 2048 * 2048 * 32 + 1;
   guestOps->windowContentsUpdated.connect(sigc::ptr_fun(&FakedOnWindowContentsUpdated));
   UnityWindowContentsStartV1 fakedUnityWindowContentsStartV1;
   UnityWindowContentsStart fakedUnityWindowContentsStart;
   memset(&fakedUnityWindowContentsStart, 0, sizeof fakedUnityWindowContentsStart);
   memset(&fakedUnityWindowContentsStartV1, 0, sizeof fakedUnityWindowContentsStartV1);
   fakedUnityWindowContentsStart.ver = UNITY_WINDOW_CONTENTS_V1;
   fakedUnityWindowContentsStart.UnityWindowContentsStart_u.startV1 =
      &fakedUnityWindowContentsStartV1;
   fakedUnityWindowContentsStartV1.windowID = fakedWindowId;
   fakedUnityWindowContentsStartV1.imageWidth = fakedImageWidth;
   fakedUnityWindowContentsStartV1.imageHeight = fakedImageHeight;
   fakedUnityWindowContentsStartV1.imageLength = fakedImageLength;
   XDR xdrs;
   memset(&xdrs, 0, sizeof xdrs);
   EXPECT_TRUE(DynXdr_Create(&xdrs) != NULL);
   EXPECT_TRUE(xdr_UnityWindowContentsStart(&xdrs, &fakedUnityWindowContentsStart));
   guestOps->FakeGuestToHostresponse(UNITY_GUEST_WINDOW_CONTENTS_START,
                                     reinterpret_cast<char *>(DynXdr_Get(&xdrs)),
                                     xdr_getpos(&xdrs));

   std::vector<uint8> fakedImageData = {1, 2, 3, 4, 5, 6, 7, 8};
   UnityWindowContentsChunkV1 fakedUnityWindowContentsChunkV1;
   UnityWindowContentsChunk fakedUnityWindowContentsChunk;
   memset(&fakedUnityWindowContentsChunk, 0, sizeof fakedUnityWindowContentsChunk);
   memset(&fakedUnityWindowContentsChunkV1, 0, sizeof fakedUnityWindowContentsChunkV1);
   fakedUnityWindowContentsChunk.ver = UNITY_WINDOW_CONTENTS_V1;
   fakedUnityWindowContentsChunk.UnityWindowContentsChunk_u.chunkV1 =
      &fakedUnityWindowContentsChunkV1;
   fakedUnityWindowContentsChunkV1.windowID = fakedWindowId;
   fakedUnityWindowContentsChunkV1.chunkID = 0;
   fakedUnityWindowContentsChunkV1.data.data_len = fakedImageData.size();
   fakedUnityWindowContentsChunkV1.data.data_val = (char *)fakedImageData.data();
   memset(&xdrs, 0, sizeof xdrs);
   EXPECT_TRUE(DynXdr_Create(&xdrs) != NULL);
   EXPECT_TRUE(xdr_UnityWindowContentsChunk(&xdrs, &fakedUnityWindowContentsChunk));
   guestOps->FakeGuestToHostresponse(UNITY_GUEST_WINDOW_CONTENTS_CHUNK,
                                     reinterpret_cast<char *>(DynXdr_Get(&xdrs)),
                                     xdr_getpos(&xdrs));

   UnityWindowContentsEndV1 fakedUnityWindowContentsEndV1;
   UnityWindowContentsEnd fakedUnityWindowContentsEnd;
   memset(&fakedUnityWindowContentsEnd, 0, sizeof fakedUnityWindowContentsEnd);
   memset(&fakedUnityWindowContentsEndV1, 0, sizeof fakedUnityWindowContentsEndV1);
   fakedUnityWindowContentsEnd.ver = UNITY_WINDOW_CONTENTS_V1;
   fakedUnityWindowContentsEnd.UnityWindowContentsEnd_u.endV1 = &fakedUnityWindowContentsEndV1;
   fakedUnityWindowContentsEndV1.windowID = fakedWindowId;
   memset(&xdrs, 0, sizeof xdrs);
   EXPECT_TRUE(DynXdr_Create(&xdrs) != NULL);
   EXPECT_TRUE(xdr_UnityWindowContentsEnd(&xdrs, &fakedUnityWindowContentsEnd));
   guestOps->FakeGuestToHostresponse(UNITY_GUEST_WINDOW_CONTENTS_END,
                                     reinterpret_cast<char *>(DynXdr_Get(&xdrs)),
                                     xdr_getpos(&xdrs));
   EXPECT_TRUE(gOnWindowContentsUpdateWindowId == 0 && gOnWindowContentsUpdateSize.width == 0 &&
               gOnWindowContentsUpdateSize.height == 0 && gOnWindowContentsUpdatePngData.empty());

   // Test image is empty.
   gOnWindowContentsUpdateSize = {0, 0};
   gOnWindowContentsUpdatePngData.clear();
   gOnWindowContentsUpdateWindowId = 0;
   memset(&fakedUnityWindowContentsStart, 0, sizeof fakedUnityWindowContentsStart);
   memset(&fakedUnityWindowContentsStartV1, 0, sizeof fakedUnityWindowContentsStartV1);
   fakedUnityWindowContentsStart.ver = UNITY_WINDOW_CONTENTS_V1;
   fakedUnityWindowContentsStart.UnityWindowContentsStart_u.startV1 =
      &fakedUnityWindowContentsStartV1;
   fakedUnityWindowContentsStartV1.windowID = fakedWindowId;
   fakedUnityWindowContentsStartV1.imageWidth = 0;
   fakedUnityWindowContentsStartV1.imageHeight = 0;
   fakedUnityWindowContentsStartV1.imageLength = fakedImageLength;
   memset(&xdrs, 0, sizeof xdrs);
   EXPECT_TRUE(DynXdr_Create(&xdrs) != NULL);
   EXPECT_TRUE(xdr_UnityWindowContentsStart(&xdrs, &fakedUnityWindowContentsStart));
   guestOps->FakeGuestToHostresponse(UNITY_GUEST_WINDOW_CONTENTS_START,
                                     reinterpret_cast<char *>(DynXdr_Get(&xdrs)),
                                     xdr_getpos(&xdrs));
   EXPECT_TRUE((int)gOnWindowContentsUpdateWindowId == fakedWindowId &&
               gOnWindowContentsUpdateSize.width == 0 && gOnWindowContentsUpdateSize.height == 0 &&
               gOnWindowContentsUpdatePngData.empty());

   // Test UnityWindowContentsStartV1 pointer is NULL.
   gOnWindowContentsUpdateSize = {0, 0};
   gOnWindowContentsUpdatePngData.clear();
   gOnWindowContentsUpdateWindowId = 0;
   memset(&fakedUnityWindowContentsStart, 0, sizeof fakedUnityWindowContentsStart);
   fakedUnityWindowContentsStart.ver = UNITY_WINDOW_CONTENTS_V1;
   fakedUnityWindowContentsStart.UnityWindowContentsStart_u.startV1 = NULL;
   memset(&xdrs, 0, sizeof xdrs);
   EXPECT_TRUE(DynXdr_Create(&xdrs) != NULL);
   EXPECT_TRUE(xdr_UnityWindowContentsStart(&xdrs, &fakedUnityWindowContentsStart));
   guestOps->FakeGuestToHostresponse(UNITY_GUEST_WINDOW_CONTENTS_START,
                                     reinterpret_cast<char *>(DynXdr_Get(&xdrs)),
                                     xdr_getpos(&xdrs));
   EXPECT_TRUE(gOnWindowContentsUpdateWindowId == 0 && gOnWindowContentsUpdateSize.width == 0 &&
               gOnWindowContentsUpdateSize.height == 0 && gOnWindowContentsUpdatePngData.empty());

   // Test invalid UnityWindowContentsStart structure.
   gOnWindowContentsUpdateSize = {0, 0};
   gOnWindowContentsUpdatePngData.clear();
   gOnWindowContentsUpdateWindowId = 0;
   memset(&fakedUnityWindowContentsStart, 0, sizeof fakedUnityWindowContentsStart);
   fakedUnityWindowContentsStart.ver = UNITY_WINDOW_CONTENTS_V1;
   fakedUnityWindowContentsStart.UnityWindowContentsStart_u.startV1 =
      &fakedUnityWindowContentsStartV1;
   fakedUnityWindowContentsStartV1.windowID = fakedWindowId;
   fakedUnityWindowContentsStartV1.imageWidth = 0;
   fakedUnityWindowContentsStartV1.imageHeight = 0;
   fakedUnityWindowContentsStartV1.imageLength = fakedImageLength;
   memset(&xdrs, 0, sizeof xdrs);
   EXPECT_TRUE(DynXdr_Create(&xdrs) != NULL);
   EXPECT_TRUE(xdr_UnityWindowContentsStart(&xdrs, &fakedUnityWindowContentsStart));
   VMOCK(XdrUtil_Deserialize).WillOnce(false);
   guestOps->FakeGuestToHostresponse(UNITY_GUEST_WINDOW_CONTENTS_START,
                                     reinterpret_cast<char *>(DynXdr_Get(&xdrs)),
                                     xdr_getpos(&xdrs));
   EXPECT_TRUE(gOnWindowContentsUpdateWindowId == 0 && gOnWindowContentsUpdateSize.width == 0 &&
               gOnWindowContentsUpdateSize.height == 0 && gOnWindowContentsUpdatePngData.empty());

   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * GHIGuestOpsMKSControlUnitTest::TestOnGuestWindowContentChunkCB --
 *
 *      Test invalid scenario in OnGuestWindowContentChunkCB method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GHIGuestOpsMKSControlUnitTest, TestOnGuestWindowContentChunkCB)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();
   GuestOpsMKSControlUTMock *guestOps = dynamic_cast<GuestOpsMKSControlUTMock *>(vm->GetGuestOps());

   gOnWindowContentsUpdateSize = {0, 0};
   gOnWindowContentsUpdatePngData.clear();
   gOnWindowContentsUpdateWindowId = 0;

   // Test invalid UnityWindowContentsChunk structure.
   int fakedWindowId = 123;
   int fakedImageWidth = 2;
   int fakedImageHeight = 4;
   int fakedImageLength = fakedImageWidth * fakedImageHeight;
   guestOps->windowContentsUpdated.connect(sigc::ptr_fun(&FakedOnWindowContentsUpdated));
   UnityWindowContentsStartV1 fakedUnityWindowContentsStartV1;
   UnityWindowContentsStart fakedUnityWindowContentsStart;
   memset(&fakedUnityWindowContentsStart, 0, sizeof fakedUnityWindowContentsStart);
   memset(&fakedUnityWindowContentsStartV1, 0, sizeof fakedUnityWindowContentsStartV1);
   fakedUnityWindowContentsStart.ver = UNITY_WINDOW_CONTENTS_V1;
   fakedUnityWindowContentsStart.UnityWindowContentsStart_u.startV1 =
      &fakedUnityWindowContentsStartV1;
   fakedUnityWindowContentsStartV1.windowID = fakedWindowId;
   fakedUnityWindowContentsStartV1.imageWidth = fakedImageWidth;
   fakedUnityWindowContentsStartV1.imageHeight = fakedImageHeight;
   fakedUnityWindowContentsStartV1.imageLength = fakedImageLength;
   XDR xdrs;
   memset(&xdrs, 0, sizeof xdrs);
   EXPECT_TRUE(DynXdr_Create(&xdrs) != NULL);
   EXPECT_TRUE(xdr_UnityWindowContentsStart(&xdrs, &fakedUnityWindowContentsStart));
   guestOps->FakeGuestToHostresponse(UNITY_GUEST_WINDOW_CONTENTS_START,
                                     reinterpret_cast<char *>(DynXdr_Get(&xdrs)),
                                     xdr_getpos(&xdrs));

   std::vector<uint8> fakedImageData = {1, 2, 3, 4, 5, 6, 7, 8};
   UnityWindowContentsChunkV1 fakedUnityWindowContentsChunkV1;
   UnityWindowContentsChunk fakedUnityWindowContentsChunk;
   memset(&fakedUnityWindowContentsChunk, 0, sizeof fakedUnityWindowContentsChunk);
   memset(&fakedUnityWindowContentsChunkV1, 0, sizeof fakedUnityWindowContentsChunkV1);
   fakedUnityWindowContentsChunk.ver = UNITY_WINDOW_CONTENTS_V1;
   fakedUnityWindowContentsChunk.UnityWindowContentsChunk_u.chunkV1 =
      &fakedUnityWindowContentsChunkV1;
   fakedUnityWindowContentsChunkV1.windowID = fakedWindowId;
   fakedUnityWindowContentsChunkV1.chunkID = 0;
   fakedUnityWindowContentsChunkV1.data.data_len = 49152 + 1;
   fakedUnityWindowContentsChunkV1.data.data_val = (char *)fakedImageData.data();
   memset(&xdrs, 0, sizeof xdrs);
   EXPECT_TRUE(DynXdr_Create(&xdrs) != NULL);
   xdr_UnityWindowContentsChunk(&xdrs, &fakedUnityWindowContentsChunk);
   guestOps->FakeGuestToHostresponse(UNITY_GUEST_WINDOW_CONTENTS_CHUNK,
                                     reinterpret_cast<char *>(DynXdr_Get(&xdrs)),
                                     xdr_getpos(&xdrs));

   UnityWindowContentsEndV1 fakedUnityWindowContentsEndV1;
   UnityWindowContentsEnd fakedUnityWindowContentsEnd;
   memset(&fakedUnityWindowContentsEnd, 0, sizeof fakedUnityWindowContentsEnd);
   memset(&fakedUnityWindowContentsEndV1, 0, sizeof fakedUnityWindowContentsEndV1);
   fakedUnityWindowContentsEnd.ver = UNITY_WINDOW_CONTENTS_V1;
   fakedUnityWindowContentsEnd.UnityWindowContentsEnd_u.endV1 = &fakedUnityWindowContentsEndV1;
   fakedUnityWindowContentsEndV1.windowID = fakedWindowId;
   memset(&xdrs, 0, sizeof xdrs);
   EXPECT_TRUE(DynXdr_Create(&xdrs) != NULL);
   EXPECT_TRUE(xdr_UnityWindowContentsEnd(&xdrs, &fakedUnityWindowContentsEnd));
   guestOps->FakeGuestToHostresponse(UNITY_GUEST_WINDOW_CONTENTS_END,
                                     reinterpret_cast<char *>(DynXdr_Get(&xdrs)),
                                     xdr_getpos(&xdrs));
   EXPECT_TRUE(gOnWindowContentsUpdateWindowId == 0 && gOnWindowContentsUpdateSize.width == 0 &&
               gOnWindowContentsUpdateSize.height == 0 && gOnWindowContentsUpdatePngData.empty());

   // Test windowContentChunkV1 pointer is NULL.
   memset(&fakedUnityWindowContentsChunk, 0, sizeof fakedUnityWindowContentsChunk);
   fakedUnityWindowContentsChunk.ver = UNITY_WINDOW_CONTENTS_V1;
   fakedUnityWindowContentsChunk.UnityWindowContentsChunk_u.chunkV1 = NULL;
   memset(&xdrs, 0, sizeof xdrs);
   EXPECT_TRUE(DynXdr_Create(&xdrs) != NULL);
   EXPECT_TRUE(xdr_UnityWindowContentsChunk(&xdrs, &fakedUnityWindowContentsChunk));
   guestOps->FakeGuestToHostresponse(UNITY_GUEST_WINDOW_CONTENTS_CHUNK,
                                     reinterpret_cast<char *>(DynXdr_Get(&xdrs)),
                                     xdr_getpos(&xdrs));
   EXPECT_TRUE(gOnWindowContentsUpdateWindowId == 0 && gOnWindowContentsUpdateSize.width == 0 &&
               gOnWindowContentsUpdateSize.height == 0 && gOnWindowContentsUpdatePngData.empty());

   // Test chunk size is too big.
   memset(&fakedUnityWindowContentsChunk, 0, sizeof fakedUnityWindowContentsChunk);
   memset(&fakedUnityWindowContentsChunkV1, 0, sizeof fakedUnityWindowContentsChunkV1);
   fakedUnityWindowContentsChunk.ver = UNITY_WINDOW_CONTENTS_V1;
   fakedUnityWindowContentsChunk.UnityWindowContentsChunk_u.chunkV1 = NULL;
   fakedUnityWindowContentsChunkV1.windowID = fakedWindowId;
   fakedUnityWindowContentsChunkV1.chunkID = 0;
   fakedUnityWindowContentsChunkV1.data.data_len = fakedImageData.size();
   fakedUnityWindowContentsChunkV1.data.data_val = (char *)fakedImageData.data();
   memset(&xdrs, 0, sizeof xdrs);
   EXPECT_TRUE(DynXdr_Create(&xdrs) != NULL);
   EXPECT_TRUE(xdr_UnityWindowContentsChunk(&xdrs, &fakedUnityWindowContentsChunk));
   VMOCK(XdrUtil_Deserialize)
      .WillOnce([&fakedUnityWindowContentsChunkV1](const void *data, size_t dataLen, void *xdrProc,
                                                   void *dest) {
         UnityWindowContentsChunk *handlerInfo = (UnityWindowContentsChunk *)dest;
         fakedUnityWindowContentsChunkV1.data.data_len = 49152 + 1;
         handlerInfo->UnityWindowContentsChunk_u.chunkV1 = &fakedUnityWindowContentsChunkV1;
         return true;
      });
   guestOps->FakeGuestToHostresponse(UNITY_GUEST_WINDOW_CONTENTS_CHUNK,
                                     reinterpret_cast<char *>(DynXdr_Get(&xdrs)),
                                     xdr_getpos(&xdrs));
   EXPECT_TRUE(gOnWindowContentsUpdateWindowId == 0 && gOnWindowContentsUpdateSize.width == 0 &&
               gOnWindowContentsUpdateSize.height == 0 && gOnWindowContentsUpdatePngData.empty());

   // Test windowId not match.
   VMOCK_V(mymock, XdrUtil_Deserialize)
      .WillRepeatedly([&mymock](const void *data, size_t dataLen, void *xdrProc, void *dest) {
         return mymock.CallRealFunc(data, dataLen, xdrProc, dest);
      });
   memset(&fakedUnityWindowContentsChunk, 0, sizeof fakedUnityWindowContentsChunk);
   memset(&fakedUnityWindowContentsChunkV1, 0, sizeof fakedUnityWindowContentsChunkV1);
   fakedUnityWindowContentsChunk.ver = UNITY_WINDOW_CONTENTS_V1;
   fakedUnityWindowContentsChunk.UnityWindowContentsChunk_u.chunkV1 =
      &fakedUnityWindowContentsChunkV1;
   fakedUnityWindowContentsChunkV1.windowID = fakedWindowId + 1;
   fakedUnityWindowContentsChunkV1.chunkID = 0;
   fakedUnityWindowContentsChunkV1.data.data_len = fakedImageData.size();
   fakedUnityWindowContentsChunkV1.data.data_val = (char *)fakedImageData.data();
   memset(&xdrs, 0, sizeof xdrs);
   EXPECT_TRUE(DynXdr_Create(&xdrs) != NULL);
   EXPECT_TRUE(xdr_UnityWindowContentsChunk(&xdrs, &fakedUnityWindowContentsChunk));
   guestOps->FakeGuestToHostresponse(UNITY_GUEST_WINDOW_CONTENTS_CHUNK,
                                     reinterpret_cast<char *>(DynXdr_Get(&xdrs)),
                                     xdr_getpos(&xdrs));
   EXPECT_TRUE(gOnWindowContentsUpdateWindowId == 0 && gOnWindowContentsUpdateSize.width == 0 &&
               gOnWindowContentsUpdateSize.height == 0 && gOnWindowContentsUpdatePngData.empty());

   // Test chunkID not match.
   memset(&fakedUnityWindowContentsChunk, 0, sizeof fakedUnityWindowContentsChunk);
   memset(&fakedUnityWindowContentsChunkV1, 0, sizeof fakedUnityWindowContentsChunkV1);
   fakedUnityWindowContentsChunk.ver = UNITY_WINDOW_CONTENTS_V1;
   fakedUnityWindowContentsChunk.UnityWindowContentsChunk_u.chunkV1 =
      &fakedUnityWindowContentsChunkV1;
   fakedUnityWindowContentsChunkV1.windowID = fakedWindowId;
   fakedUnityWindowContentsChunkV1.chunkID = 100;
   fakedUnityWindowContentsChunkV1.data.data_len = fakedImageData.size();
   fakedUnityWindowContentsChunkV1.data.data_val = (char *)fakedImageData.data();
   memset(&xdrs, 0, sizeof xdrs);
   EXPECT_TRUE(DynXdr_Create(&xdrs) != NULL);
   EXPECT_TRUE(xdr_UnityWindowContentsChunk(&xdrs, &fakedUnityWindowContentsChunk));
   guestOps->FakeGuestToHostresponse(UNITY_GUEST_WINDOW_CONTENTS_CHUNK,
                                     reinterpret_cast<char *>(DynXdr_Get(&xdrs)),
                                     xdr_getpos(&xdrs));
   EXPECT_TRUE(gOnWindowContentsUpdateWindowId == 0 && gOnWindowContentsUpdateSize.width == 0 &&
               gOnWindowContentsUpdateSize.height == 0 && gOnWindowContentsUpdatePngData.empty());

   // Test no enough space for window image data
   memset(&fakedUnityWindowContentsChunk, 0, sizeof fakedUnityWindowContentsChunk);
   memset(&fakedUnityWindowContentsChunkV1, 0, sizeof fakedUnityWindowContentsChunkV1);
   fakedUnityWindowContentsChunk.ver = UNITY_WINDOW_CONTENTS_V1;
   fakedUnityWindowContentsChunk.UnityWindowContentsChunk_u.chunkV1 =
      &fakedUnityWindowContentsChunkV1;
   fakedUnityWindowContentsChunkV1.windowID = fakedWindowId;
   fakedUnityWindowContentsChunkV1.chunkID = 0;
   fakedUnityWindowContentsChunkV1.data.data_len = fakedImageData.size();
   fakedUnityWindowContentsChunkV1.data.data_val = (char *)fakedImageData.data();
   memset(&xdrs, 0, sizeof xdrs);
   EXPECT_TRUE(DynXdr_Create(&xdrs) != NULL);
   EXPECT_TRUE(xdr_UnityWindowContentsChunk(&xdrs, &fakedUnityWindowContentsChunk));
   int oldSize = guestOps->mWindowContents.buffer.size();
   guestOps->mWindowContents.buffer.resize(1 + oldSize);
   guestOps->FakeGuestToHostresponse(UNITY_GUEST_WINDOW_CONTENTS_CHUNK,
                                     reinterpret_cast<char *>(DynXdr_Get(&xdrs)),
                                     xdr_getpos(&xdrs));
   EXPECT_TRUE(gOnWindowContentsUpdateWindowId == 0 && gOnWindowContentsUpdateSize.width == 0 &&
               gOnWindowContentsUpdateSize.height == 0 && gOnWindowContentsUpdatePngData.empty());
   guestOps->mWindowContents.buffer.resize(oldSize);

   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * GHIGuestOpsMKSControlUnitTest::TestOnGuestWindowContentEndCB --
 *
 *      Test invalid scenario in OnGuestWindowContentEndCB method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GHIGuestOpsMKSControlUnitTest, TestOnGuestWindowContentEndCB)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();
   GuestOpsMKSControlUTMock *guestOps = dynamic_cast<GuestOpsMKSControlUTMock *>(vm->GetGuestOps());

   gOnWindowContentsUpdateSize = {0, 0};
   gOnWindowContentsUpdatePngData.clear();
   gOnWindowContentsUpdateWindowId = 0;

   // Test invalid UnityWindowContentsEnd structure.
   int fakedWindowId = 123;
   int fakedImageWidth = 2;
   int fakedImageHeight = 4;
   int fakedImageLength = fakedImageWidth * fakedImageHeight + 1;
   guestOps->windowContentsUpdated.connect(sigc::ptr_fun(&FakedOnWindowContentsUpdated));
   UnityWindowContentsStartV1 fakedUnityWindowContentsStartV1;
   UnityWindowContentsStart fakedUnityWindowContentsStart;
   memset(&fakedUnityWindowContentsStart, 0, sizeof fakedUnityWindowContentsStart);
   memset(&fakedUnityWindowContentsStartV1, 0, sizeof fakedUnityWindowContentsStartV1);
   fakedUnityWindowContentsStart.ver = UNITY_WINDOW_CONTENTS_V1;
   fakedUnityWindowContentsStart.UnityWindowContentsStart_u.startV1 =
      &fakedUnityWindowContentsStartV1;
   fakedUnityWindowContentsStartV1.windowID = fakedWindowId;
   fakedUnityWindowContentsStartV1.imageWidth = fakedImageWidth;
   fakedUnityWindowContentsStartV1.imageHeight = fakedImageHeight;
   fakedUnityWindowContentsStartV1.imageLength = fakedImageLength;
   XDR xdrs;
   memset(&xdrs, 0, sizeof xdrs);
   EXPECT_TRUE(DynXdr_Create(&xdrs) != NULL);
   EXPECT_TRUE(xdr_UnityWindowContentsStart(&xdrs, &fakedUnityWindowContentsStart));
   guestOps->FakeGuestToHostresponse(UNITY_GUEST_WINDOW_CONTENTS_START,
                                     reinterpret_cast<char *>(DynXdr_Get(&xdrs)),
                                     xdr_getpos(&xdrs));

   std::vector<uint8> fakedImageData = {1, 2, 3, 4, 5, 6, 7, 8};
   UnityWindowContentsChunkV1 fakedUnityWindowContentsChunkV1;
   UnityWindowContentsChunk fakedUnityWindowContentsChunk;
   memset(&fakedUnityWindowContentsChunk, 0, sizeof fakedUnityWindowContentsChunk);
   memset(&fakedUnityWindowContentsChunkV1, 0, sizeof fakedUnityWindowContentsChunkV1);
   fakedUnityWindowContentsChunk.ver = UNITY_WINDOW_CONTENTS_V1;
   fakedUnityWindowContentsChunk.UnityWindowContentsChunk_u.chunkV1 =
      &fakedUnityWindowContentsChunkV1;
   fakedUnityWindowContentsChunkV1.windowID = fakedWindowId;
   fakedUnityWindowContentsChunkV1.chunkID = 0;
   fakedUnityWindowContentsChunkV1.data.data_len = fakedImageData.size();
   fakedUnityWindowContentsChunkV1.data.data_val = (char *)fakedImageData.data();
   memset(&xdrs, 0, sizeof xdrs);
   EXPECT_TRUE(DynXdr_Create(&xdrs) != NULL);
   xdr_UnityWindowContentsChunk(&xdrs, &fakedUnityWindowContentsChunk);
   guestOps->FakeGuestToHostresponse(UNITY_GUEST_WINDOW_CONTENTS_CHUNK,
                                     reinterpret_cast<char *>(DynXdr_Get(&xdrs)),
                                     xdr_getpos(&xdrs));

   UnityWindowContentsEndV1 fakedUnityWindowContentsEndV1;
   UnityWindowContentsEnd fakedUnityWindowContentsEnd;
   memset(&fakedUnityWindowContentsEnd, 0, sizeof fakedUnityWindowContentsEnd);
   memset(&fakedUnityWindowContentsEndV1, 0, sizeof fakedUnityWindowContentsEndV1);
   fakedUnityWindowContentsEnd.ver = UNITY_WINDOW_CONTENTS_V1;
   fakedUnityWindowContentsEnd.UnityWindowContentsEnd_u.endV1 = &fakedUnityWindowContentsEndV1;
   fakedUnityWindowContentsEndV1.windowID = fakedWindowId;
   memset(&xdrs, 0, sizeof xdrs);
   EXPECT_TRUE(DynXdr_Create(&xdrs) != NULL);
   EXPECT_TRUE(xdr_UnityWindowContentsEnd(&xdrs, &fakedUnityWindowContentsEnd));
   VMOCK(XdrUtil_Deserialize).Will(false);
   guestOps->FakeGuestToHostresponse(UNITY_GUEST_WINDOW_CONTENTS_END,
                                     reinterpret_cast<char *>(DynXdr_Get(&xdrs)),
                                     xdr_getpos(&xdrs));
   EXPECT_TRUE(gOnWindowContentsUpdateWindowId == 0 && gOnWindowContentsUpdateSize.width == 0 &&
               gOnWindowContentsUpdateSize.height == 0 && gOnWindowContentsUpdatePngData.empty());

   // Test windowContentEndV1 pointer is NULL.
   VMOCK_V(mymock, XdrUtil_Deserialize)
      .WillRepeatedly([&mymock](const void *data, size_t dataLen, void *xdrProc, void *dest) {
         return mymock.CallRealFunc(data, dataLen, xdrProc, dest);
      });
   memset(&fakedUnityWindowContentsEnd, 0, sizeof fakedUnityWindowContentsEnd);
   fakedUnityWindowContentsEnd.ver = UNITY_WINDOW_CONTENTS_V1;
   fakedUnityWindowContentsEnd.UnityWindowContentsEnd_u.endV1 = NULL;
   memset(&xdrs, 0, sizeof xdrs);
   EXPECT_TRUE(DynXdr_Create(&xdrs) != NULL);
   EXPECT_TRUE(xdr_UnityWindowContentsEnd(&xdrs, &fakedUnityWindowContentsEnd));
   guestOps->FakeGuestToHostresponse(UNITY_GUEST_WINDOW_CONTENTS_END,
                                     reinterpret_cast<char *>(DynXdr_Get(&xdrs)),
                                     xdr_getpos(&xdrs));
   EXPECT_TRUE(gOnWindowContentsUpdateWindowId == 0 && gOnWindowContentsUpdateSize.width == 0 &&
               gOnWindowContentsUpdateSize.height == 0 && gOnWindowContentsUpdatePngData.empty());

   // Test windowId not match.
   memset(&fakedUnityWindowContentsEnd, 0, sizeof fakedUnityWindowContentsEnd);
   memset(&fakedUnityWindowContentsEndV1, 0, sizeof fakedUnityWindowContentsEndV1);
   fakedUnityWindowContentsEnd.ver = UNITY_WINDOW_CONTENTS_V1;
   fakedUnityWindowContentsEnd.UnityWindowContentsEnd_u.endV1 = &fakedUnityWindowContentsEndV1;
   fakedUnityWindowContentsEndV1.windowID = fakedWindowId + 1;
   memset(&xdrs, 0, sizeof xdrs);
   EXPECT_TRUE(DynXdr_Create(&xdrs) != NULL);
   EXPECT_TRUE(xdr_UnityWindowContentsEnd(&xdrs, &fakedUnityWindowContentsEnd));
   guestOps->FakeGuestToHostresponse(UNITY_GUEST_WINDOW_CONTENTS_END,
                                     reinterpret_cast<char *>(DynXdr_Get(&xdrs)),
                                     xdr_getpos(&xdrs));
   EXPECT_TRUE(gOnWindowContentsUpdateWindowId == 0 && gOnWindowContentsUpdateSize.width == 0 &&
               gOnWindowContentsUpdateSize.height == 0 && gOnWindowContentsUpdatePngData.empty());

   // Test incomplete window contents.
   memset(&fakedUnityWindowContentsEnd, 0, sizeof fakedUnityWindowContentsEnd);
   memset(&fakedUnityWindowContentsEndV1, 0, sizeof fakedUnityWindowContentsEndV1);
   fakedUnityWindowContentsEnd.ver = UNITY_WINDOW_CONTENTS_V1;
   fakedUnityWindowContentsEnd.UnityWindowContentsEnd_u.endV1 = &fakedUnityWindowContentsEndV1;
   fakedUnityWindowContentsEndV1.windowID = fakedWindowId;
   memset(&xdrs, 0, sizeof xdrs);
   EXPECT_TRUE(DynXdr_Create(&xdrs) != NULL);
   EXPECT_TRUE(xdr_UnityWindowContentsEnd(&xdrs, &fakedUnityWindowContentsEnd));
   guestOps->FakeGuestToHostresponse(UNITY_GUEST_WINDOW_CONTENTS_END,
                                     reinterpret_cast<char *>(DynXdr_Get(&xdrs)),
                                     xdr_getpos(&xdrs));
   EXPECT_TRUE(gOnWindowContentsUpdateWindowId == 0 && gOnWindowContentsUpdateSize.width == 0 &&
               gOnWindowContentsUpdateSize.height == 0 && gOnWindowContentsUpdatePngData.empty());

   delete vm;
}


static cui::ActionTargetURIPair gActionTargetUrlPair = cui::ActionTargetURIPair("", "");
static std::list<utf::string> gLocationUrls = {};

/*
 *-----------------------------------------------------------------------------
 *
 * FakedOnHostShellActionUpdated --
 *
 *    Faked callback when shell action are received from guest.
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

void
FakedOnHostShellActionUpdated(const cui::ActionTargetURIPair &actionTargetUrlPair, // IN
                              const std::list<utf::string> &locationUrls)          // IN
{
   gActionTargetUrlPair = actionTargetUrlPair;
   gLocationUrls = locationUrls;
}


/*
 *-----------------------------------------------------------------------------
 *
 * GHIGuestOpsMKSControlUnitTest::TestOnGuestHostShellActionCB --
 *
 *      Test OnGuestHostShellActionCB method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GHIGuestOpsMKSControlUnitTest, TestOnGuestHostShellActionCB)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();
   GuestOpsMKSControlUTMock *guestOps = dynamic_cast<GuestOpsMKSControlUTMock *>(vm->GetGuestOps());

   // Test valid actions.
   guestOps->hostShellAction.connect(sigc::ptr_fun(&FakedOnHostShellActionUpdated));
   std::vector<GHIShellActionLocation> fakedLocations = {
      {(char *)"fakedLocation123"},
      {(char *)"fakedLocation456"},
   };
   std::string fakedTargetURI = "fakedTargetURI";
   std::string fakedActionURI = "fakedActionURI";
   GHIShellActionV1 fakedGHIShellActionV1;
   GHIShellAction fakedGHIShellAction;
   memset(&fakedGHIShellAction, 0, sizeof fakedGHIShellAction);
   memset(&fakedGHIShellActionV1, 0, sizeof fakedGHIShellActionV1);
   fakedGHIShellAction.ver = GHI_SHELL_ACTION_V1;
   fakedGHIShellAction.GHIShellAction_u.actionV1 = &fakedGHIShellActionV1;
   fakedGHIShellActionV1.targetURI = (char *)fakedTargetURI.c_str();
   fakedGHIShellActionV1.actionURI = (char *)fakedActionURI.c_str();
   fakedGHIShellActionV1.locations.locations_len = fakedLocations.size();
   fakedGHIShellActionV1.locations.locations_val = fakedLocations.data();
   XDR xdrs;
   memset(&xdrs, 0, sizeof xdrs);
   EXPECT_TRUE(DynXdr_Create(&xdrs) != NULL);
   EXPECT_TRUE(xdr_GHIShellAction(&xdrs, &fakedGHIShellAction));
   gActionTargetUrlPair = cui::ActionTargetURIPair("", "");
   gLocationUrls.clear();
   guestOps->FakeGuestToHostresponse(
      GHI_GUEST_HOST_SHELL_ACTION, reinterpret_cast<char *>(DynXdr_Get(&xdrs)), xdr_getpos(&xdrs));
   EXPECT_TRUE(gActionTargetUrlPair.actionURI == fakedActionURI.c_str());
   EXPECT_TRUE(gActionTargetUrlPair.targetURI == fakedTargetURI.c_str());
   EXPECT_TRUE(gLocationUrls.size() == fakedLocations.size() &&
               gLocationUrls.front() == fakedLocations[0].location &&
               gLocationUrls.back() == fakedLocations[1].location);

   // Test invalid location.
   fakedLocations = {
      {(char *)"\xF8\xA1\xA1\xA1\xA1"},
      {(char *)"\xF8\xA1\xA1\xA1\xA1\xA1"},
   };
   memset(&fakedGHIShellAction, 0, sizeof fakedGHIShellAction);
   memset(&fakedGHIShellActionV1, 0, sizeof fakedGHIShellActionV1);
   fakedGHIShellAction.ver = GHI_SHELL_ACTION_V1;
   fakedGHIShellAction.GHIShellAction_u.actionV1 = &fakedGHIShellActionV1;
   fakedGHIShellActionV1.targetURI = (char *)fakedTargetURI.c_str();
   fakedGHIShellActionV1.actionURI = (char *)fakedActionURI.c_str();
   fakedGHIShellActionV1.locations.locations_len = fakedLocations.size();
   fakedGHIShellActionV1.locations.locations_val = fakedLocations.data();
   memset(&xdrs, 0, sizeof xdrs);
   EXPECT_TRUE(DynXdr_Create(&xdrs) != NULL);
   EXPECT_TRUE(xdr_GHIShellAction(&xdrs, &fakedGHIShellAction));
   gActionTargetUrlPair = cui::ActionTargetURIPair("", "");
   gLocationUrls.clear();
   guestOps->FakeGuestToHostresponse(
      GHI_GUEST_HOST_SHELL_ACTION, reinterpret_cast<char *>(DynXdr_Get(&xdrs)), xdr_getpos(&xdrs));
   EXPECT_TRUE(gActionTargetUrlPair.actionURI == "");
   EXPECT_TRUE(gActionTargetUrlPair.targetURI == "");
   EXPECT_TRUE(gLocationUrls.empty());

   // Test invalid targetURI.
   fakedLocations = {
      {(char *)"fakedLocation123"},
      {(char *)"fakedLocation456"},
   };
   memset(&fakedGHIShellAction, 0, sizeof fakedGHIShellAction);
   memset(&fakedGHIShellActionV1, 0, sizeof fakedGHIShellActionV1);
   fakedGHIShellAction.ver = GHI_SHELL_ACTION_V1;
   fakedGHIShellAction.GHIShellAction_u.actionV1 = &fakedGHIShellActionV1;
   fakedGHIShellActionV1.targetURI = (char *)"\xF8\xA1\xA1\xA1\xA1";
   fakedGHIShellActionV1.actionURI = (char *)fakedActionURI.c_str();
   fakedGHIShellActionV1.locations.locations_len = fakedLocations.size();
   fakedGHIShellActionV1.locations.locations_val = fakedLocations.data();
   memset(&xdrs, 0, sizeof xdrs);
   EXPECT_TRUE(DynXdr_Create(&xdrs) != NULL);
   EXPECT_TRUE(xdr_GHIShellAction(&xdrs, &fakedGHIShellAction));
   gActionTargetUrlPair = cui::ActionTargetURIPair("", "");
   gLocationUrls.clear();
   guestOps->FakeGuestToHostresponse(
      GHI_GUEST_HOST_SHELL_ACTION, reinterpret_cast<char *>(DynXdr_Get(&xdrs)), xdr_getpos(&xdrs));
   EXPECT_TRUE(gActionTargetUrlPair.actionURI == "");
   EXPECT_TRUE(gActionTargetUrlPair.targetURI == "");
   EXPECT_TRUE(gLocationUrls.empty());

   // Test invalid actionURI.
   fakedLocations = {
      {(char *)"fakedLocation123"},
      {(char *)"fakedLocation456"},
   };
   memset(&fakedGHIShellAction, 0, sizeof fakedGHIShellAction);
   memset(&fakedGHIShellActionV1, 0, sizeof fakedGHIShellActionV1);
   fakedGHIShellAction.ver = GHI_SHELL_ACTION_V1;
   fakedGHIShellAction.GHIShellAction_u.actionV1 = &fakedGHIShellActionV1;
   fakedGHIShellActionV1.targetURI = (char *)fakedTargetURI.c_str();
   fakedGHIShellActionV1.actionURI = (char *)"\xF8\xA1\xA1\xA1\xA1";
   fakedGHIShellActionV1.locations.locations_len = fakedLocations.size();
   fakedGHIShellActionV1.locations.locations_val = fakedLocations.data();
   memset(&xdrs, 0, sizeof xdrs);
   EXPECT_TRUE(DynXdr_Create(&xdrs) != NULL);
   EXPECT_TRUE(xdr_GHIShellAction(&xdrs, &fakedGHIShellAction));
   gActionTargetUrlPair = cui::ActionTargetURIPair("", "");
   gLocationUrls.clear();
   guestOps->FakeGuestToHostresponse(
      GHI_GUEST_HOST_SHELL_ACTION, reinterpret_cast<char *>(DynXdr_Get(&xdrs)), xdr_getpos(&xdrs));
   EXPECT_TRUE(gActionTargetUrlPair.actionURI == "");
   EXPECT_TRUE(gActionTargetUrlPair.targetURI == "");
   EXPECT_TRUE(gLocationUrls.empty());

   // Test actionDetailsPtr pointer is NULL.
   memset(&fakedGHIShellAction, 0, sizeof fakedGHIShellAction);
   memset(&fakedGHIShellActionV1, 0, sizeof fakedGHIShellActionV1);
   fakedGHIShellAction.ver = GHI_SHELL_ACTION_V1;
   fakedGHIShellAction.GHIShellAction_u.actionV1 = NULL;
   memset(&xdrs, 0, sizeof xdrs);
   EXPECT_TRUE(DynXdr_Create(&xdrs) != NULL);
   EXPECT_TRUE(xdr_GHIShellAction(&xdrs, &fakedGHIShellAction));
   gActionTargetUrlPair = cui::ActionTargetURIPair("", "");
   gLocationUrls.clear();
   guestOps->FakeGuestToHostresponse(
      GHI_GUEST_HOST_SHELL_ACTION, reinterpret_cast<char *>(DynXdr_Get(&xdrs)), xdr_getpos(&xdrs));
   EXPECT_TRUE(gActionTargetUrlPair.actionURI == "");
   EXPECT_TRUE(gActionTargetUrlPair.targetURI == "");
   EXPECT_TRUE(gLocationUrls.empty());

   // Test invalid GHIShellAction structure.
   memset(&fakedGHIShellAction, 0, sizeof fakedGHIShellAction);
   memset(&fakedGHIShellActionV1, 0, sizeof fakedGHIShellActionV1);
   fakedGHIShellAction.ver = GHI_SHELL_ACTION_V1;
   fakedGHIShellAction.GHIShellAction_u.actionV1 = &fakedGHIShellActionV1;
   fakedGHIShellActionV1.targetURI = (char *)fakedTargetURI.c_str();
   fakedGHIShellActionV1.actionURI = (char *)fakedActionURI.c_str();
   fakedGHIShellActionV1.locations.locations_len = fakedLocations.size();
   fakedGHIShellActionV1.locations.locations_val = fakedLocations.data();
   memset(&xdrs, 0, sizeof xdrs);
   EXPECT_TRUE(DynXdr_Create(&xdrs) != NULL);
   EXPECT_TRUE(xdr_GHIShellAction(&xdrs, &fakedGHIShellAction));
   gActionTargetUrlPair = cui::ActionTargetURIPair("", "");
   gLocationUrls.clear();
   VMOCK(XdrUtil_Deserialize).Will(false);
   guestOps->FakeGuestToHostresponse(
      GHI_GUEST_HOST_SHELL_ACTION, reinterpret_cast<char *>(DynXdr_Get(&xdrs)), xdr_getpos(&xdrs));
   EXPECT_TRUE(gActionTargetUrlPair.actionURI == "");
   EXPECT_TRUE(gActionTargetUrlPair.targetURI == "");
   EXPECT_TRUE(gLocationUrls.empty());

   delete vm;
}


static std::set<utf::string> gLaunchMenuTypeSet = {};

/*
 *-----------------------------------------------------------------------------
 *
 * FakedOnLaunchMenuChanged --
 *
 *    Faked callback when the contents of one or more launch menus change.
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

void
FakedOnLaunchMenuChanged(const std::set<utf::string> &launchMenuTypeSet) // IN
{
   gLaunchMenuTypeSet = launchMenuTypeSet;
}


/*
 *-----------------------------------------------------------------------------
 *
 * GHIGuestOpsMKSControlUnitTest::TestOnGuestLaunchMenuChangeCB --
 *
 *      Test OnGuestLaunchMenuChangeCB method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(GHIGuestOpsMKSControlUnitTest, TestOnGuestLaunchMenuChangeCB)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();
   GuestOpsMKSControlUTMock *guestOps = dynamic_cast<GuestOpsMKSControlUTMock *>(vm->GetGuestOps());

   // Test valid update.
   guestOps->launchMenuChanged.connect(sigc::ptr_fun(&FakedOnLaunchMenuChanged));
   std::vector<GHILaunchMenuKey> fakedLaunchMenuKeys = {
      (char *)UNITY_START_MENU_LAUNCH_FOLDER,
      (char *)UNITY_START_MENU_FIXED_FOLDER,
      (char *)UNITY_START_MENU_ALL_HANDLERS_FOLDER,
      (char *)UNITY_START_MENU_RESOLVED_LAUNCH_FOLDER,
      (char *)UNITY_START_MENU_RECENT_DOCUMENTS_FOLDER,
   };
   GHIStartMenuChangedV1 fakedGHIStartMenuChangedV1;
   GHIStartMenuChanged fakedGHIStartMenuChanged;
   memset(&fakedGHIStartMenuChanged, 0, sizeof fakedGHIStartMenuChanged);
   memset(&fakedGHIStartMenuChangedV1, 0, sizeof fakedGHIStartMenuChangedV1);
   fakedGHIStartMenuChanged.ver = GHI_STARTMENU_CHANGED_V1;
   fakedGHIStartMenuChanged.GHIStartMenuChanged_u.ghiStartMenuChangedV1 =
      &fakedGHIStartMenuChangedV1;
   fakedGHIStartMenuChangedV1.keys.keys_len = fakedLaunchMenuKeys.size();
   fakedGHIStartMenuChangedV1.keys.keys_val = (GHILaunchMenuKey *)fakedLaunchMenuKeys.data();
   XDR xdrs;
   memset(&xdrs, 0, sizeof xdrs);
   EXPECT_TRUE(DynXdr_Create(&xdrs) != NULL);
   EXPECT_TRUE(xdr_GHIStartMenuChanged(&xdrs, &fakedGHIStartMenuChanged));
   gLaunchMenuTypeSet.clear();
   guestOps->FakeGuestToHostresponse(
      GHI_GUEST_LAUNCHMENU_CHANGE, reinterpret_cast<char *>(DynXdr_Get(&xdrs)), xdr_getpos(&xdrs));
   static std::set<utf::string> expectedMenuTypes{fakedLaunchMenuKeys.begin(),
                                                  fakedLaunchMenuKeys.end()};
   EXPECT_TRUE(gLaunchMenuTypeSet.size() == fakedLaunchMenuKeys.size() &&
               expectedMenuTypes == gLaunchMenuTypeSet);

   // Test invalid menu type.
   fakedLaunchMenuKeys.clear();
   fakedLaunchMenuKeys.push_back((char *)"\xF8\xA1\xA1\xA1\xA1");
   memset(&fakedGHIStartMenuChanged, 0, sizeof fakedGHIStartMenuChanged);
   memset(&fakedGHIStartMenuChangedV1, 0, sizeof fakedGHIStartMenuChangedV1);
   fakedGHIStartMenuChanged.ver = GHI_STARTMENU_CHANGED_V1;
   fakedGHIStartMenuChanged.GHIStartMenuChanged_u.ghiStartMenuChangedV1 =
      &fakedGHIStartMenuChangedV1;
   fakedGHIStartMenuChangedV1.keys.keys_len = fakedLaunchMenuKeys.size();
   fakedGHIStartMenuChangedV1.keys.keys_val = (GHILaunchMenuKey *)fakedLaunchMenuKeys.data();
   memset(&xdrs, 0, sizeof xdrs);
   EXPECT_TRUE(DynXdr_Create(&xdrs) != NULL);
   EXPECT_TRUE(xdr_GHIStartMenuChanged(&xdrs, &fakedGHIStartMenuChanged));
   guestOps->FakeGuestToHostresponse(
      GHI_GUEST_LAUNCHMENU_CHANGE, reinterpret_cast<char *>(DynXdr_Get(&xdrs)), xdr_getpos(&xdrs));
   EXPECT_TRUE(gLaunchMenuTypeSet.empty());

   // Test menuChangedMsgV1Ptr pointer is NULL.
   fakedLaunchMenuKeys.clear();
   fakedLaunchMenuKeys.push_back((char *)UNITY_START_MENU_LAUNCH_FOLDER);
   memset(&fakedGHIStartMenuChanged, 0, sizeof fakedGHIStartMenuChanged);
   fakedGHIStartMenuChanged.ver = GHI_STARTMENU_CHANGED_V1;
   fakedGHIStartMenuChanged.GHIStartMenuChanged_u.ghiStartMenuChangedV1 = NULL;
   memset(&xdrs, 0, sizeof xdrs);
   EXPECT_TRUE(DynXdr_Create(&xdrs) != NULL);
   EXPECT_TRUE(xdr_GHIStartMenuChanged(&xdrs, &fakedGHIStartMenuChanged));
   guestOps->FakeGuestToHostresponse(
      GHI_GUEST_LAUNCHMENU_CHANGE, reinterpret_cast<char *>(DynXdr_Get(&xdrs)), xdr_getpos(&xdrs));
   EXPECT_TRUE(gLaunchMenuTypeSet.empty());

   // Test msgLen is zero.
   fakedLaunchMenuKeys.clear();
   fakedLaunchMenuKeys.push_back((char *)UNITY_START_MENU_LAUNCH_FOLDER);
   memset(&fakedGHIStartMenuChanged, 0, sizeof fakedGHIStartMenuChanged);
   memset(&fakedGHIStartMenuChangedV1, 0, sizeof fakedGHIStartMenuChangedV1);
   fakedGHIStartMenuChanged.ver = GHI_STARTMENU_CHANGED_V1;
   fakedGHIStartMenuChanged.GHIStartMenuChanged_u.ghiStartMenuChangedV1 =
      &fakedGHIStartMenuChangedV1;
   fakedGHIStartMenuChangedV1.keys.keys_len = fakedLaunchMenuKeys.size();
   fakedGHIStartMenuChangedV1.keys.keys_val = (GHILaunchMenuKey *)fakedLaunchMenuKeys.data();
   memset(&xdrs, 0, sizeof xdrs);
   EXPECT_TRUE(DynXdr_Create(&xdrs) != NULL);
   EXPECT_TRUE(xdr_GHIStartMenuChanged(&xdrs, &fakedGHIStartMenuChanged));
   VMOCK(XdrUtil_Deserialize).Will(false);
   guestOps->FakeGuestToHostresponse(GHI_GUEST_LAUNCHMENU_CHANGE,
                                     reinterpret_cast<char *>(DynXdr_Get(&xdrs)), 0);
   EXPECT_TRUE(gLaunchMenuTypeSet.empty());

   // Test invalid GHIStartMenuChanged structure.
   fakedLaunchMenuKeys.clear();
   fakedLaunchMenuKeys.push_back((char *)UNITY_START_MENU_LAUNCH_FOLDER);
   memset(&fakedGHIStartMenuChanged, 0, sizeof fakedGHIStartMenuChanged);
   memset(&fakedGHIStartMenuChangedV1, 0, sizeof fakedGHIStartMenuChangedV1);
   fakedGHIStartMenuChanged.ver = GHI_STARTMENU_CHANGED_V1;
   fakedGHIStartMenuChanged.GHIStartMenuChanged_u.ghiStartMenuChangedV1 =
      &fakedGHIStartMenuChangedV1;
   fakedGHIStartMenuChangedV1.keys.keys_len = fakedLaunchMenuKeys.size();
   fakedGHIStartMenuChangedV1.keys.keys_val = (GHILaunchMenuKey *)fakedLaunchMenuKeys.data();
   memset(&xdrs, 0, sizeof xdrs);
   EXPECT_TRUE(DynXdr_Create(&xdrs) != NULL);
   EXPECT_TRUE(xdr_GHIStartMenuChanged(&xdrs, &fakedGHIStartMenuChanged));
   VMOCK(XdrUtil_Deserialize).Will(false);
   guestOps->FakeGuestToHostresponse(
      GHI_GUEST_LAUNCHMENU_CHANGE, reinterpret_cast<char *>(DynXdr_Get(&xdrs)), xdr_getpos(&xdrs));
   EXPECT_TRUE(gLaunchMenuTypeSet.empty());

   delete vm;
}

} // namespace cui