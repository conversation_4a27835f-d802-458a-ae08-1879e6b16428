/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */
#pragma once

#include "cedar/windows/resource.h"

/*
 * TestLaunchProcess --
 *
 *    Utilities that help launch processes
 */

class ProcUtil {
public:
   /*
    *--------------------------------------------------------------------------
    *
    * LaunchProcessWithStdOut --
    *
    *    Kick off another process. Do not wait for the process to complete. Redirect stdout
    *    to a given file.
    *
    * Results:
    *    True if the process started successfully, false otherwise.
    *
    * Side effects:
    *    None.
    *
    *--------------------------------------------------------------------------
    */

   static bool LaunchProcessWithStdOut(const std::wstring &cmdStr, const std::wstring &filePath,
                                       PROCESS_INFORMATION &pi, HANDLE &fileHandle,
                                       bool appendFile = false)
   {
      SECURITY_ATTRIBUTES sa;
      sa.nLength = sizeof(sa);
      sa.lpSecurityDescriptor = NULL;
      sa.bInheritHandle = TRUE;

      fileHandle = CreateFile(
         filePath.c_str(), FILE_ALL_ACCESS, FILE_SHARE_WRITE | FILE_SHARE_READ | FILE_SHARE_DELETE,
         &sa, appendFile ? OPEN_ALWAYS : CREATE_ALWAYS, FILE_ATTRIBUTE_NORMAL, NULL);
      if (fileHandle == INVALID_HANDLE_VALUE) {
         SYSMSG_FUNC(Debug, "CreateFile failed to create file: %s Error: %s", filePath.c_str(),
                     wstr::formatError());
         return false;
      }

      if (appendFile) {
         SetFilePointer(fileHandle, 0, NULL, FILE_END);
      }

      STARTUPINFO info = {0};
      info.hStdOutput = fileHandle;
      info.hStdError = fileHandle;
      info.dwFlags = STARTF_USESTDHANDLES;
      info.cb = sizeof(info);
      LPWSTR cmd = const_cast<wchar_t *>(cmdStr.c_str());

      if (!CreateProcess(NULL, cmd, NULL, NULL, TRUE, NORMAL_PRIORITY_CLASS | CREATE_NO_WINDOW,
                         NULL, NULL, &info, &pi)) {
         SYSMSG_FUNC(Debug, "CreateProcess failed to launch the command: %s Error: %s",
                     cmdStr.c_str(), wstr::formatError());
         if (fileHandle) {
            CloseHandle(fileHandle);
         }
         return false;
      }
      return true;
   }

   static bool LaunchProcessWithStdOutAndWait(const std::wstring &cmdStr,
                                              const std::wstring &filePath, bool appendFile = false,
                                              DWORD timeout = INFINITE)
   {
      PROCESS_INFORMATION processInfo = {0};
      HANDLE hFile = NULL;

      if (!LaunchProcessWithStdOut(cmdStr, filePath, processInfo, hFile, appendFile)) {
         SYSMSG_FUNC(Debug, "Failed to launch process with stdout: %s", cmdStr.c_str());
         return false;
      }

      cedar::windows::unique_process piHandle(processInfo.hProcess);
      cedar::windows::unique_thread piThread(processInfo.hThread);

      auto waitResult = WaitForSingleObject(piHandle.get(), timeout);
      if (waitResult != WAIT_OBJECT_0) {
         SYSMSG_FUNC(Debug, "WaitForSingleObject failed to wait for the process: %s Error: %s",
                     cmdStr, wstr::formatError());
         CloseHandle(hFile);
         return false;
      }
      CloseHandle(hFile);
      return true;
   }

   /*
    *--------------------------------------------------------------------------
    *
    * CloseProcess --
    *
    *    Close a given process
    *
    * Results:
    *    True if successful, false otherwise.
    *
    * Side effects:
    *    None.
    *
    *--------------------------------------------------------------------------
    */

   static bool CloseProcess(PROCESS_INFORMATION &pi)
   {
      DWORD exitCode;
      if (!GetExitCodeProcess(pi.hProcess, &exitCode)) {
         SYSMSG_FUNC(Debug, "GetExitCodeProcess failed with error code: %s", wstr::formatError());
         return false;
      }

      if (!TerminateProcess(pi.hProcess, exitCode)) {
         SYSMSG_FUNC(Debug, "Terminate process failed with error: %s", wstr::formatError());
         return false;
      }

      WaitForSingleObject(pi.hProcess, INFINITE);

      CloseHandle(pi.hProcess);
      CloseHandle(pi.hThread);
      return true;
   }
};