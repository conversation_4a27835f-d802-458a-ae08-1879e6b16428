name: appblastlibs
run-name: >
  ${{ github.workflow }}
  ${{ github.event_name == 'workflow_dispatch' && inputs.buildtype ||
      (github.event_name == 'pull_request' && 'obj' || 'beta') }}
  ${{ github.event_name == 'pull_request' &&
      format(' - {0} (#{1})', github.event.pull_request.title, github.event.pull_request.number)
      || '' }}
# Note the concurrency block for appblastlibs is in the build-appblastlibs
# job instead of being here like it is for the other workflows. This is so
# the test jobs will not be cancelled by concurrency.
# TODO: move concurrency back here once we can reliably clean up test runners
# after each run
on:
  pull_request:
  push:
    branches:
      - 'main'
      - 'release/**'
      - 'feature/**'
    paths-ignore:
      - .github/RunnerResetConfig.json
      - .github/workflows/runner_app_config.yaml
      - .github/workflows/rx-devop-nightly-*.yaml
  workflow_dispatch:
    inputs:
      buildtype:
        type: choice
        description: Build type
        required: True
        default: beta
        options:
          - beta
          - obj
          - release
      conan_develop:
        type: boolean
        description: I am testing conan packages and need to enable the conan-develop remote
        required: True
        default: false
      conan_sandbox:
        type: boolean
        description: I am testing conan compiler upgrade and need to enable the conan-sandbox remote
        required: false
        default: false
      enable_tests:
        type: choice
        description: Enable tests
        required: True
        default: all
        options:
          - all
          - none
          - AbCertMgr
          - BENeV
          - BitB
          - Blast
          - Display
          - Networking
          - VmwAudio
          - VmwVideo
          - VncRegEncPerfTest
          - VvcSessionManager

env:
  CODECOV_WIN_INCLUSIONS: >-
    bora/apps/rde/blast/appblast,
    bora/lib/blastSockets,
    bora/lib/udpfec,
    bora/lib/vnc,
    bora/lib/vncConnectionManager,
    bora/lib/vvclib,
    bora-vmsoft/hznvaudio/audiooutconfig/lib,
    bora-vmsoft/hznvaudio/devtap/audiodevtap
  CODECOV_LIN_INCLUSIONS: >-
    bora/lib/blastSockets,
    bora/lib/udpfec,
    bora/lib/vnc,
    bora/lib/vncConnectionManager,
    bora/lib/vvclib
  BUILDTYPE: ${{ github.event_name == 'workflow_dispatch' && inputs.buildtype ||
                 (github.event_name == 'pull_request' && 'obj' || 'beta') }}
  
jobs:
  file-check:
    runs-on: [lnxbuild-gh, self-hosted]
    permissions:
      contents: read
      pull-requests: read
    outputs:
      enable-build: ${{ steps.filter.outputs.result }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          sparse-checkout: .github/workflow-filters.yaml
          sparse-checkout-cone-mode: false

      - name: Check if build should be run
        id: filter
        uses: euc-eng/filter-paths@v1
        with:
          filtersFile: .github/workflow-filters.yaml
          label: appblastlibs

  build-appblastlibs:
    needs: file-check
    if: ${{ needs.file-check.outputs.enable-build == 'true' }}
    strategy:
      matrix:
        label: [lnxbuild-gh, winhzn-gh]
    runs-on:
      - ${{ matrix.label }}
      - self-hosted
    concurrency:
      # This section ensure that multiple PR pushes will cancel superseded builds.
      # Builds on main, release/* and feature/* branches will not be canceled to
      # assist in root causing build breakages.
      # Note that since this concurrency is set at the job level, it also needs
      # to include the matrix config so the matrix jobs don't cancel each other.
      group: ${{ github.workflow }}-${{
          (github.ref == 'refs/heads/main' ||
           startsWith(github.ref, 'refs/heads/feature/') ||
           startsWith(github.ref, 'refs/heads/release/')) &&
          github.run_id || github.ref
        }}-${{ inputs.buildtype }}-${{ matrix.label }}
      cancel-in-progress: true
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          lfs: true

      - name: Run SCons
        uses: ./.github/actions/scons
        with:
          buildtype: ${{ github.event_name == 'pull_request' && 'obj' || inputs.buildtype }}
          product: appblastlibs
          conanDevelop: ${{ inputs.conan_develop }}
          conanSandbox: ${{ inputs.conan_sandbox }}
          artifactoryApiToken: ${{ secrets.ARTIFACTORY_GITHUB_DELIVERABLES_API_TOKEN }}
          artifactoryUrl: ${{ vars.ARTIFACTORY_GITHUB_DELIVERABLES_URL }}
          artifactoryReadToken: ${{ secrets.ORG_OMNISSA_ART_READONLY_TOKEN }}
          artifactoryReadUser: ${{ vars.ORG_OMNISSA_ART_READONLY_USER }}
          azureSigningTenantId: ${{ vars.AZURE_BINARY_SIGNING_TENANT_ID }}
          azureSigningTestKeyCertName: ${{ vars.AZURE_BINARY_SIGNING_OFFICIAL_KEY_CERT_NAME }}
          azureSigningTestKeyClientId: ${{ vars.AZURE_BINARY_SIGNING_OFFICIAL_KEY_CLIENT_ID }}
          azureSigningTestKeySecret: ${{ secrets.AZURE_BINARY_SIGNING_OFFICIAL_KEY_CLIENT_SECRET }}
          azureSigningUrl: ${{ vars.AZURE_BINARY_SIGNING_OFFICIAL_KEY_URL }}
          extraParams: >
            COMPILE_DB=1 compiledb appblastlibs
            ${{ runner.os == 'Linux' && 'ENABLE_CODE_COV=1' || '' }}

  UT:
    needs: build-appblastlibs
    if: ${{ vars.ENABLE_UNIT_TEST_HOSTED_IN_MV == 'true' }}
    secrets: inherit
    uses: ./.github/workflows/appblastlibs_ut.yaml
    with:
      buildtype: ${{ github.event_name == 'pull_request' && 'obj' || inputs.buildtype }}
      enable_tests: ${{ github.event_name == 'workflow_dispatch' && inputs.enable_tests || 'all' }}

  sonar-upload-windows:
    needs:
      - build-appblastlibs
      - UT
    # we want to run sonar if dependent test jobs are successful or skipped, but
    # there's no function for skipped() so we use !failure() and !cancelled()
    # also skip sonar if any tests were disabled in manual runs
    if: ${{ !failure() && !cancelled() &&
            needs.build-appblastlibs.result == 'success' &&
            (github.event_name != 'workflow_dispatch' || inputs.enable_tests == 'all') }}
    runs-on: [winhzn-gh, self-hosted]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Upload results to SonarQube
        uses: ./.github/actions/testframework/cart-sonar
        with:
          sonarProjectKey: cart-appblastlibs-windows
          productWorkflowJob: build-appblastlibs-winhzn-gh
          utJobs: >
            Blast-Windows,Networking-Windows,AbCertMgr,Display-*,
            VmwAudio,VncRegEncPerfTest-nvidia,VncRegEncPerfTest-intel,VncRegEncPerfTest-amd,
            VvcSessionManager-Windows
          utJobsObjOnly: BENeV*-Windows*
          artifactoryApiToken: ${{ secrets.ARTIFACTORY_GITHUB_DELIVERABLES_API_TOKEN }}
          artifactoryBaseUrl: ${{ vars.ARTIFACTORY_GITHUB_DELIVERABLES_URL }}
          artifactoryReadToken: ${{ secrets.ORG_OMNISSA_ART_READONLY_TOKEN }}
          artifactoryReadUser: ${{ vars.ORG_OMNISSA_ART_READONLY_USER }}
          languageMode: C++
          compileCommands: compile_commands.json
          sources: ${{ env.CODECOV_WIN_INCLUSIONS }}
          buildtype: ${{ env.BUILDTYPE }}
          sonarHostUrl: ${{ vars.ORG_EUC_SONAR_HOST }}
          sonarToken: ${{ secrets.ORG_EUC_SONAR_TOKEN }}
          qualityGateWait: true
          conanDevelop: ${{ inputs.conan_develop }}
          conanSandbox: ${{ inputs.conan_sandbox }}

  sonar-upload-linux:
    needs:
      - build-appblastlibs
      - UT
    # we want to run sonar if dependent test jobs are successful or skipped, but
    # there's no function for skipped() so we use !failure() and !cancelled()
    # also skip sonar if any tests were disabled in manual runs
    if: ${{ !failure() && !cancelled() &&
            needs.build-appblastlibs.result == 'success' &&
            (github.event_name != 'workflow_dispatch' || inputs.enable_tests == 'all') }}
    runs-on: [lnxbuild-gh, self-hosted]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Upload results to SonarQube
        uses: ./.github/actions/testframework/cart-sonar
        with:
          sonarProjectKey: cart-appblastlibs-linux
          product: appblastlibs
          productWorkflowJob: build-appblastlibs-lnxbuild-gh
          utJobs: >
            Blast-Linux,Networking-Linux,VncRegEncPerfTest-Linux,
            VvcSessionManager-Linux
          utJobsObjOnly: BENeV*-Linux*
          artifactoryApiToken: ${{ secrets.ARTIFACTORY_GITHUB_DELIVERABLES_API_TOKEN }}
          artifactoryBaseUrl: ${{ vars.ARTIFACTORY_GITHUB_DELIVERABLES_URL }}
          artifactoryReadToken: ${{ secrets.ORG_OMNISSA_ART_READONLY_TOKEN }}
          artifactoryReadUser: ${{ vars.ORG_OMNISSA_ART_READONLY_USER }}
          languageMode: C++
          compileCommands: compile_commands.json
          gcnoTarfile: cc_gcno_Linux_appblastlibs.tar.gz
          sources: ${{ env.CODECOV_LIN_INCLUSIONS }}
          buildtype: ${{ env.BUILDTYPE }}
          sonarHostUrl: ${{ vars.ORG_EUC_SONAR_HOST }}
          sonarToken: ${{ secrets.ORG_EUC_SONAR_TOKEN }}
          qualityGateWait: true
          conanDevelop: ${{ inputs.conan_develop }}
          conanSandbox: ${{ inputs.conan_sandbox }}

  appblastlibs-overall-status:
    needs:
      - build-appblastlibs
      - UT
      - sonar-upload-windows
      - sonar-upload-linux
    if: ${{ !cancelled() }}
    timeout-minutes: 10
    runs-on: [lnxbuild-gh, self-hosted]
    permissions:
      actions: write
      contents: read
      pull-requests: read
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Check overall workflow status
        uses: ./.github/actions/check-status
        with:
          workflowId: 'appblastlibs.yaml'
          jobs: ${{ toJson(needs) }}
          buildtype: ${{ inputs.buildtype }}
          slackWebhookUrl: ${{ secrets.CART_SLACK_WEBHOOK_URL }}
          slackBranches: ${{ vars.DAILY_BUILD_BRANCHES }}
