/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * hznprotect.c --
 *
 *      Works with hznprotect driver to protect keyboard input
 *      of our application.
 */

#include <stdio.h>
#include <process.h>
#include <windows.h>
#include <winreg.h>
#include <ntddkbd.h>

#include "config.h"
#include "trapapi.h"
#include "keyboardMapping.h"
#include "mutexRankLib.h"
#include "userlock.h"
#include "util.h"
#include "vm_assert.h"
#include "vm_atomic.h"
#include "win32Util.h"
#include "windowsu.h"
#include "hznprotect.h"
#include "hznprotectDefs.h"
#include "hznprotect_version.h"

#define LOGLEVEL_MODULE HznProtect
#include "log.h"
#include "loglevel_user.h"
#define LOGPFX "HznProtect: "


#define FlagOn(_F, _SF) ((_F) & (_SF))
#define SetFlag(_F, _SF) ((_F) |= (_SF))
#define ClearFlag(_F, _SF) ((_F) &= ~(_SF))


#ifdef VMWPROTECT_TEST
#   include "vmwProtectTesterWrapper.h"
static void *g_vmwptester = NULL;
#endif


#define NT_SUCCESS(Status) (((NTSTATUS)(Status)) >= 0)


__kernel_entry NTSTATUS NTAPI RtlGetVersion(PRTL_OSVERSIONINFOW lpVersionInformation);

__kernel_entry NTSTATUS NTAPI RtlWow64GetProcessMachines(HANDLE hProcess, USHORT *pProcessMachine,
                                                         USHORT *pNativeMachine);

static LRESULT CALLBACK LowLevelKeyboardCB(int nCode, WPARAM wParam, LPARAM lParam);

typedef DWORD(WINAPI *IMMPROCESSKEYPROC)(HWND hWnd, HKL hkl, UINT uVKey, LPARAM lParam,
                                         DWORD dwHotKeyID);

typedef LRESULT(WINAPI *CTFIMMNOTIFYPROC)(DWORD nCode, WPARAM wParam, LPARAM lParam);

typedef BOOL(CALLBACK *INITMESSAGEPUMPHOOK)(DWORD dwCmd, void *pvParam);

typedef BOOL(WINAPI *REGISTER_MPH_PROC)(INITMESSAGEPUMPHOOK pInit);

typedef BOOL(WINAPI *UNREGISTER_MPH_PROC)(VOID);

typedef BOOL(WINAPI *INTERNALGETMESSAGEPROC)(LPMSG lpMsg, HWND hwnd, UINT wMsgFilterMin,
                                             UINT wMsgFilterMax, UINT flags, BOOL fGetMessage);

typedef BOOL(WINAPI *WAITMESSAGEEXPROC)(UINT fsWakeMask, DWORD dwTimeout);

typedef DWORD(WINAPI *GETQUEUESTATUSPROC)(IN UINT flags);

typedef DWORD(WINAPI *MSGWAITFORMULTIPLEOBJECTSEXPROC)(DWORD nCount, CONST HANDLE *pHandles,
                                                       DWORD dwMilliseconds, DWORD dwWakeMask,
                                                       DWORD dwFlags);

typedef struct tagMESSAGEPUMPHOOK {
   DWORD cbSize;
   INTERNALGETMESSAGEPROC pfnInternalGetMessage;
   WAITMESSAGEEXPROC pfnWaitMessageEx;
   GETQUEUESTATUSPROC pfnGetQueueStatus;
   MSGWAITFORMULTIPLEOBJECTSEXPROC pfnMsgWaitForMultipleObjectsEx;
} MESSAGEPUMPHOOK, *PMESSAGEPUMPHOOK;

#pragma pack(push, 1)
typedef union MSG_LPARAM {
   struct {
      DWORD repeatCount : 16;     // 0-15
      DWORD scanCode : 8;         // 16-23
      DWORD extendedKey : 1;      // 24
      DWORD reserved : 4;         // 25-28
      DWORD contextCode : 1;      // 29
      DWORD previousKeyState : 1; // 30
      DWORD transitionState : 1;  // 31
   };
   LPARAM value;
} MSG_LPARAM;
#pragma pack(pop)

#pragma pack(push, 1)
typedef union {
   struct {
      DWORD extraInfo;
      VKeycode vkCode;
      UINT8 scanCode;
      Bool keyUp;
   };
   UINT64 value;
} KbdMsgInfo;
#pragma pack(pop)

static struct KEYBOARD {
   HANDLE hThread;
   UINT32 threadId;
   Atomic_uint32 enableHook;

   union {
      KbdMsgInfo kbdMsgInfosDbg[0x100]; // 2 * (VSCAN_CODE_BITS + 1)
      Atomic_uint64 kbdMsgInfos[0x100]; // 2 * (VSCAN_CODE_BITS + 1)
   };
   Atomic_uint32 kbdMsgTimes[0x100]; // 2 * (VSCAN_CODE_BITS + 1)

   BOOL installLowLevelHookLater;
} keyboard;

static struct HZNPROTECT {
   HANDLE hUser32;
   HANDLE hImm32;
   RTL_OSVERSIONINFOW osVersionInfo;
   USHORT processMachine;
   USHORT nativeMachine;

   HHOOK hCallWinprocHook;
   HANDLE driverHandle;
   IoctlProbeDriverReply driverInfo;
   Atomic_uint32 blockSendInput;

   Atomic_uint32 mainThreadId;
   HANDLE statisticsThread;
} hznProtect;


static const USHORT numPadCvt[] = {
   MAKEWORD(VK_INSERT, VK_NUMPAD0), MAKEWORD(VK_END, VK_NUMPAD1),    MAKEWORD(VK_DOWN, VK_NUMPAD2),
   MAKEWORD(VK_NEXT, VK_NUMPAD3),   MAKEWORD(VK_LEFT, VK_NUMPAD4),   MAKEWORD(VK_CLEAR, VK_NUMPAD5),
   MAKEWORD(VK_RIGHT, VK_NUMPAD6),  MAKEWORD(VK_HOME, VK_NUMPAD7),   MAKEWORD(VK_UP, VK_NUMPAD8),
   MAKEWORD(VK_PRIOR, VK_NUMPAD9),  MAKEWORD(VK_DELETE, VK_DECIMAL),
};


/*
 * The MphInternalGetMessage() call may be recursive.
 * We observe 2 levels of recursion from PeekMessageW() call.
 * Currently, we set the max recursion depth to 4.
 *
 * tls_recurLevel == 0 means not in a MphInternalGetMessage() call.
 * tls_recurLevel > 0 means we are in MphInternalGetMessage() call,
 * and the value of tls_recurLevel is the recursion level, starts from 1.
 *
 * tls_currentMsgs[] saves tag and KbdMsgNode in use of each recursion level.
 * tls_currentMsgs[0] is not used.
 * Use "tls_currentMsgs[tls_recurLevel]" to get the tag and KbdMsgNode of
 * current level of recursive call.
 */
static __thread DWORD tls_recurLevel;

static __thread DWORD tls_cUserMPHs; // if non-zero, we should call user MPH

static struct HOOKS {
   IMMPROCESSKEYPROC realImmProcessKey;
   CTFIMMNOTIFYPROC realCtfImmNotify;

   REGISTER_MPH_PROC realRegisterMPH;
   UNREGISTER_MPH_PROC realUnregisterMPH;

   CRITICAL_SECTION lockMPH;
   MESSAGEPUMPHOOK realMPH; // original system function pointers of MPH
   MESSAGEPUMPHOOK userMPH; // dynamic function pointers of user MPH

   LONG cInitUserMPH;                 // reference count of user MPH init
   INITMESSAGEPUMPHOOK fnInitUserMPH; // user MPH Init function
} hooks;


#define UIAH_INITIALIZE 0
#define UIAH_UNINITIALIZE 1
#define UIAH_UNHOOK 2

// the return bits of ImmProcessHotKey
#define IPHK_HOTKEY 0x0001
#define IPHK_PROCESSBYIME 0x0002
#define IPHK_CHECKCTRL 0x0004
// NT only
#define IPHK_SKIPTHISKEY 0x0010


#define VSCAN_KEY_UP_BIT 0x80

/*
 * Reuse the reserved value of `VK_NAVIGATION_VIEW` to indicate a KbdMsgInfo in
 * `keyboard.kbdMsgInfos` was not decrypted by LowLevelKeyboardCB().
 */
#define VK_INVALID 0x88


/*
 * Uncomment this line to output sensitive logs
 */
// #define HZNPROTECT_DEBUG 1


static unsigned __stdcall StatisticsThreadMain(void *clientData);


#include "kbdFilterBitmaps.inc"


/*
 *-----------------------------------------------------------------------------
 *
 * StaticInit --
 *
 *       Initialize static variables.
 *
 * Results:
 *       None.
 *
 * Side effects:
 *       None.
 *
 *-----------------------------------------------------------------------------
 */

static void
StaticInit(void)
{
   /*
    * Make sure OS loads `user32.dll` and `imm32.dll` for us.
    */
   static volatile BOOL unused;
   unused = ImmIsIME(GetKeyboardLayout(0));

   hznProtect.hUser32 = GetModuleHandleW(L"USER32.dll");
   hznProtect.hImm32 = GetModuleHandleW(L"IMM32.dll");

   VERIFY(hznProtect.hUser32 != NULL && hznProtect.hImm32 != NULL);

   NTSTATUS status;

   hznProtect.osVersionInfo.dwOSVersionInfoSize = sizeof hznProtect.osVersionInfo;
   status = RtlGetVersion(&hznProtect.osVersionInfo);
   VERIFY(NT_SUCCESS(status));

   status = RtlWow64GetProcessMachines(GetCurrentProcess(), &hznProtect.processMachine,
                                       &hznProtect.nativeMachine);
   VERIFY(NT_SUCCESS(status));
}


#ifdef HZNPROTECT_DLL
/*
 *-----------------------------------------------------------------------------
 *
 * DllMain --
 *
 *       Dll entry function.
 *
 * Results:
 *       TRUE for success, FALSE otherwise.
 *
 * Side effects:
 *       None.
 *
 *-----------------------------------------------------------------------------
 */

BOOL WINAPI
DllMain(HINSTANCE hThisModule, // IN: handle to this DLL module
        DWORD reason,          // IN: reason for calling function
        LPVOID reserved)       // IN: reason for unload this DLL
{
   switch (reason) {

   case DLL_PROCESS_ATTACH: {
      StaticInit();
      DisableThreadLibraryCalls(hThisModule);
   } break;

   default:
      break;

   } // switch (reason)

   return TRUE;
}
#endif // HZNPROTECT_DLL


/*
 *-----------------------------------------------------------------------------
 *
 * ProbeDriver --
 *
 *      Verify the version of hznprotect driver and read configurations.
 *
 * Results:
 *      TRUE if success, FALSE otherwise (last error is set).
 *
 * Side effects:
 *      "g_driverInfo" is modified.
 *
 *-----------------------------------------------------------------------------
 */

static BOOL
ProbeDriver(HANDLE handle) // IN
{
   BOOL ret;
   ULONG bytes = 0;

   ret = DeviceIoControl(handle, IOCTL_PROBE_DRIVER, NULL, 0, &hznProtect.driverInfo,
                         sizeof hznProtect.driverInfo, &bytes, NULL);
   if (!ret) {
      return FALSE;
   }

   if (bytes < sizeof hznProtect.driverInfo) {
      SetLastError(ERROR_DATATYPE_MISMATCH);
      return FALSE;
   }

   if (hznProtect.driverInfo.majorVersion != HZNPROTECT_MAJORVERSION ||
       hznProtect.driverInfo.minorVersion != HZNPROTECT_MINORVERSION) {
      SetLastError(ERROR_REVISION_MISMATCH);
      return FALSE;
   }

   SetLastError(ERROR_SUCCESS);
   return TRUE;
}


/*
 *----------------------------------------------------------------------
 *
 * HookKeyboardThreadMain --
 *
 *      The keyboard hook thread
 *
 * Results:
 *      Always 0.
 *
 * Side effects:
 *      Keys are handled via a keyboard hook.
 *
 *----------------------------------------------------------------------
 */

static unsigned __stdcall HookKeyboardThreadMain(void *clientData) // IN
{
   MXUserSemaphore *threadSignal = clientData;
   HANDLE hCurrentThread = GetCurrentThread();

   (void)Win32U_SetThreadDescription(hCurrentThread, "keyboard thread");

   if (!SetThreadPriority(hCurrentThread, THREAD_PRIORITY_TIME_CRITICAL)) {
      DWORD err = GetLastError();

      Log_Level(HZN_LOG_ERROR, LOGPFX "%s: SetThreadPriority failed with %d", __func__, err);
      ASSERT(FALSE);
      /*
       * keep going in release build
       */
   }

   /*
    * Install the keyboard hook procedure
    */
   HMODULE hThisModule = W32Util_GetModuleByAddress(LowLevelKeyboardCB);
   VERIFY(hThisModule != NULL);

   HHOOK hHook = SetWindowsHookExW(WH_KEYBOARD_LL, LowLevelKeyboardCB, hThisModule, 0);
   if (hHook == NULL) {
      DWORD err = GetLastError();

      Log_Level(HZN_LOG_ERROR, LOGPFX "%s: SetWindowsHookExW failed with %d", __func__, err);
      ASSERT(FALSE);
      goto out;
   }

   MXUser_UpSemaphore(threadSignal);

   Log_Level(HZN_LOG_INFO, LOGPFX "%s: keyboard thread starts\n", __func__);

   MSG msg;
   BOOL ret;

   while ((ret = GetMessageW(&msg, NULL, 0, 0)) != 0) {
      if (ret == -1) {
         DWORD err = GetLastError();

         Log_Level(HZN_LOG_ERROR, LOGPFX "%s: GetMessageW failed with %d", __func__, err);
         goto unhook;
      }

      TranslateMessage(&msg);
      DispatchMessageW(&msg);
   }

   /*
    * WM_QUIT received.
    */

unhook:
   if (!UnhookWindowsHookEx(hHook)) {
      DWORD err = GetLastError();

      if (err == ERROR_INVALID_HOOK_HANDLE) {
         /*
          * The hook was probably already invalidated by Windows
          * (because we timed out).
          */
      } else {
         Log_Level(HZN_LOG_ERROR, LOGPFX "%s: UnhookWindowsHookEx failed with %d", __func__, err);
         ASSERT(FALSE);
         goto out;
      }
   }

out:
   _endthreadex(0);
   return 0;
}


/*
 *-----------------------------------------------------------------------------
 *
 * HznProtect_Init --
 *
 *      Initialize driver handle.
 *
 *      The keyboard thread is created to install and handle WH_KEYBOARD_LL
 *      if HznProtect_InstallHooks() is called before.
 *      Will panic if the keyboard thread is not responding.
 *
 *      This function needs be called after HznProtect_InstallHooks().
 *
 * Results:
 *      TRUE if success, FALSE otherwise.
 *
 * Side effects:
 *      Starts a thread.
 *
 *-----------------------------------------------------------------------------
 */

BOOL
HznProtect_Init(void)
{
   Log_Level(HZN_LOG_INFO,
             LOGPFX "%s: OS version %d.%d build number %ld "
                    "process machine %X native machine %X",
             __func__, hznProtect.osVersionInfo.dwMajorVersion,
             hznProtect.osVersionInfo.dwMinorVersion, hznProtect.osVersionInfo.dwBuildNumber,
             hznProtect.processMachine, hznProtect.nativeMachine);

   if (hznProtect.driverHandle == NULL) {
      Log_Level(HZN_LOG_WARNING,
                LOGPFX "%s: cannot find required driver, "
                       "anti-keylogger will be disabled",
                __func__);
      return FALSE;
   }

   Log_Level(HZN_LOG_INFO, LOGPFX "%s: majorVersion: %d, minorVersion: %d", __func__,
             hznProtect.driverInfo.majorVersion, hznProtect.driverInfo.minorVersion);

   /*
    * Follow-up steps after HznProtect_InstallHooks() is called.
    *
    * This is a producer-consumer problem:
    *
    * We installed hook to MESSAGEPUMPHOOK::pfnInternalGetMessage,
    * aka MphInternalGetMessage(), which consumes KbdMsgInfo from
    * "keyboard.kbdMsgInfos".
    *
    * The KbdMsgInfo is produced by LowLevelKeyboardCB()
    * (WH_KEYBOARD_LL handler) run in "keyboard thread", saved into
    * "keyboard.kbdMsgInfos".
    *
    * The entry point of the "keyboard thread" is HookKeyboardThreadMain().
    * When the "keyboard thread" starts, the low level hook WH_KEYBOARD_LL
    * will be set in HookKeyboardThreadMain().
    */
   if (keyboard.installLowLevelHookLater) {
      int inputThreadRetries = Config_GetLong(10, "mks.inputThreadRetries");
      int inputThreadTimeout = Config_GetLong(5000, "mks.inputThreadTimeout");
      MXUserSemaphore *threadSignal;

      threadSignal = MXUser_CreateSemaphore("hookKeyboardThreadSignal", RANK_LEAF);
      keyboard.hThread = (HANDLE)_beginthreadex(NULL, // default security
                                                0,    // default stack size
                                                HookKeyboardThreadMain, threadSignal,
                                                0, // run immediately
                                                &keyboard.threadId);
      VERIFY(keyboard.hThread != NULL);

      int i = 0;
      BOOL success = FALSE;
      while (i < inputThreadRetries && !success) {
         success = MXUser_TimedDownSemaphore(threadSignal, inputThreadTimeout);
         i++;
      }

      if (!success) {
         Panic(LOGPFX "%s: keyboard thread is not responding\n", __func__);
      }

      MXUser_DestroySemaphore(threadSignal);

      // We only want to do it in client process, not rmks process
      hznProtect.statisticsThread = (HANDLE)_beginthreadex(NULL, // default security
                                                           0,    // default stack size
                                                           StatisticsThreadMain, NULL,
                                                           0, // run immediately
                                                           NULL);
   }

   Atomic_Write32(&hznProtect.blockSendInput, TRUE);

   return TRUE;
}


/*
 *-----------------------------------------------------------------------------
 *
 * HznProtect_Exit --
 *
 *      Close driver handle.
 *
 *      The keyboard thread will be terminated if it has been started before.
 *      Will panic if the keyboard thread is not responding.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      Stops a thread.
 *
 *-----------------------------------------------------------------------------
 */

void
HznProtect_Exit(void)
{
   if (hznProtect.hCallWinprocHook != NULL) {
      UnhookWindowsHookEx(hznProtect.hCallWinprocHook);
      hznProtect.hCallWinprocHook = NULL;
   }

   Atomic_Write32(&keyboard.enableHook, FALSE);
   Atomic_Write32(&hznProtect.blockSendInput, FALSE);

   if (keyboard.hThread != NULL) {
      PostThreadMessageW(keyboard.threadId, WM_QUIT, 0, 0);

      DWORD status = WaitForSingleObject(keyboard.hThread, INFINITE);
      VERIFY(status == WAIT_OBJECT_0);
      CloseHandle(keyboard.hThread);

      keyboard.hThread = NULL;
      keyboard.threadId = 0;
   }

   if (hznProtect.statisticsThread != NULL) {
      PostThreadMessageW(GetThreadId(hznProtect.statisticsThread), WM_QUIT, 0, 0);
      DWORD status = WaitForSingleObject(hznProtect.statisticsThread, INFINITE);
      VERIFY(status == WAIT_OBJECT_0);
      CloseHandle(hznProtect.statisticsThread);

      hznProtect.statisticsThread = NULL;
   }

   HznProtect_CloseDriver();
}


/*
 *-----------------------------------------------------------------------------
 *
 * HznProtect_IsDriverOpened --
 *
 *      Tell if hznprotect driver has been opened.
 *
 * Results:
 *      TRUE if installed, FALSE otherwise.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

BOOL
HznProtect_IsDriverOpened(void)
{
   return hznProtect.driverHandle != NULL;
}


/*
 *-----------------------------------------------------------------------------
 *
 * SetDriverEncryptStatus --
 *
 *      Set encrypt status to hznprotect driver.
 *      TRUE to start encryption.
 *      FALSE to stop encryption.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      keyboard.enableHook is changed accordingly.
 *
 *-----------------------------------------------------------------------------
 */

static void
SetDriverEncryptStatus(BOOL enableFilter) // IN
{
   /*
    * We need to enable low level keyboard hook before we send IOCTL_GRAB to
    * driver to make sure we do not lost any encrypted keys.
    *
    * We need to disable low level keyboard hook after we send IOCTL_UNGRAB to
    * driver to make sure all encrypted keys can be seen in the hook, although
    * we cannot 100% sure about this case since Raw Input Thread may have some
    * delay when reading keystroke from driver.
    */
   if (enableFilter) {
      Atomic_Write32(&keyboard.enableHook, TRUE);
   }

   if (hznProtect.driverHandle != NULL) {
      ULONG bytes = 0;
      BOOL ret;

      ret = DeviceIoControl(hznProtect.driverHandle, enableFilter ? IOCTL_GRAB : IOCTL_UNGRAB, NULL,
                            0, NULL, 0, &bytes, NULL);
      if (!ret) {
         DWORD err = GetLastError();

         Log_Level(HZN_LOG_ERROR, LOGPFX "%s: %s encryption request failed with %d", __func__,
                   enableFilter ? "enable" : "disable", err);
      }
   } else {
      Log_Level(HZN_LOG_ERROR, LOGPFX "%s: driver handle is not valid", __func__);
   }

   if (!enableFilter) {
      Atomic_Write32(&keyboard.enableHook, FALSE);
   }

   Log_Level(HZN_LOG_INFO, LOGPFX "%s: encryption %sd\n", __func__,
             enableFilter ? "enable" : "disable");
}


/*
 *-----------------------------------------------------------------------------
 *
 * GetKeystroke --
 *
 *      Get user keystroke from driver in FIPS mode.
 *
 * Results:
 *      TRUE if success, or FALSE.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

static BOOL
GetKeystroke(UINT32 extraInfo,            // IN
             UINT8 tag,                   // IN
             PKEYBOARD_INPUT_DATA output) // OUT
{
   if (hznProtect.driverHandle == NULL) {
      Log_Level(HZN_LOG_ERROR, LOGPFX "%s: driver handle is not valid", __func__);
      return FALSE;
   }

   BOOL ret;
   ULONG bytesReturned = 0;
   KEYBOARD_INPUT_DATA input = {
      .MakeCode = tag & VSCAN_CODE_BITS,
      .ExtraInformation = extraInfo,
   };

   ret = DeviceIoControl(hznProtect.driverHandle, IOCTL_GET_KEYS, &input, sizeof input, output,
                         sizeof *output, &bytesReturned, NULL);

   if (!ret || bytesReturned != sizeof *output) {
      DWORD err = GetLastError();

      Log_Level(HZN_LOG_ERROR, LOGPFX "%s: IOCTL_GET_KEYS failed with %d", __func__, err);
      return FALSE;
   }

   return TRUE;
}


/*
 *-----------------------------------------------------------------------------
 *
 * HznProtect_DecryptMessage --
 *
 *      Decrypt message in KBDLLHOOKSTRUCT.
 *
 * Results:
 *      TRUE if success, FALSE otherwise.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

BOOL
HznProtect_DecryptMessage(PKBDLLHOOKSTRUCT pMsg) // IN/OUT:
{
   if (pMsg->dwExtraInfo == 0) {
      return FALSE; // `pMsg->dwExtraInfo` is NOT valid
   }

   KEYBOARD_INPUT_DATA kbdData = {0};

   if (!GetKeystroke(pMsg->dwExtraInfo, pMsg->scanCode & VSCAN_CODE_BITS, &kbdData)) {
      return FALSE; // `pMsg->dwExtraInfo` is NOT valid
   }

   if (HIBYTE(kbdData.UnitId) == HIBYTE_NOT_ENCRYPTED) {
      pMsg->dwExtraInfo = kbdData.ExtraInformation;
      return TRUE; // `pMsg` was NOT encrypted
   }

   UINT8 scanCode = kbdData.MakeCode & VSCAN_CODE_BITS;

   UINT vKey = MapVirtualKeyW(scanCode, MAPVK_VSC_TO_VK);
   if ((GetKeyState(VK_NUMLOCK) & 1) && (GetKeyState(VK_SHIFT) & 0x8000) == 0) {
      for (int i = 0; i < ARRAYSIZE(numPadCvt); i++) {
         USHORT item = numPadCvt[i];
         if (LOBYTE(item) == LOBYTE(vKey)) {
            /*
             * keep extra bits, but change VK value
             */
            vKey &= ~0xFF;
            vKey |= (UINT)(HIBYTE(item));
            break;
         }
      }
   }

   pMsg->vkCode = vKey;
   pMsg->scanCode = scanCode;
   pMsg->dwExtraInfo = kbdData.ExtraInformation;

   /*
    * Fix key-up/down according to KEYBOARD_INPUT_DATA::Flags.
    */
   if (FlagOn(kbdData.Flags, KEY_BREAK)) {
      SetFlag(pMsg->flags, LLKHF_UP);
   } else { // KEY_MAKE
      ClearFlag(pMsg->flags, LLKHF_UP);
   }

   return TRUE;
}


/*
 *-----------------------------------------------------------------------------
 *
 * ReadDecryptedMessage --
 *
 *      Read already decrypted message from "keyboard.kbdMsgInfos" into lpMsg.
 *
 *      If the KbdMsgInfo found by lpMsg is empty (LowLevelKeyboardCB() has
 *      not saved anything in it yet), lpMsg is unchanged.
 *
 * Notes:
 *      We have the following call graph:
 *         MphInternalGetMessage()
 *          ├─pfnInternalGetMessage() in "g_userMPH" or "g_realMPH"
 *          │  ├─ImmProcessKeyHook()
 *          │  │  ├─ReadDecryptedMessage()    <--- 1
 *          │  │  ├─g_ImmProcessKeyFn()
 *          │  └─TF_NotifyHook()
 *          │     ├─ReadDecryptedMessage()    <--- 2
 *          │     ├─g_TF_NotifyFn()
 *          └─DecryptKeystrokeMessage()
 *
 *      No extraInfo is available in ImmProcessKeyHook and TF_NotifyHook,
 *      so this function is designed to read decrypted message from
 *      "keyboard.kbdMsgInfos", which is updated in another thread by
 *      LowLevelKeyboardCB.
 *
 * Results:
 *      TRUE if success, or FALSE.
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

static BOOL
ReadDecryptedMessage(LPMSG lpMsg,       // IN/OUT:
                     BOOL checkMsgTime, // IN:
                     PCSTR caller)      // IN:
{
   MSG_LPARAM lParam = {
      .value = lpMsg->lParam,
   };

   UINT8 tag = lParam.scanCode & VSCAN_CODE_BITS;

   UINT8 index = tag;
   if (lParam.transitionState) { // transitionState = KF_UP
      SetFlag(index, VSCAN_KEY_UP_BIT);
   }

   if (checkMsgTime) {
      if (lpMsg->time != Atomic_Read32(&keyboard.kbdMsgTimes[index])) {
         return FALSE;
      }
   }

   KbdMsgInfo kbdMsgInfo = {
      .value = Atomic_Read64(&keyboard.kbdMsgInfos[index]),
   };

#ifdef HZNPROTECT_DEBUG
   Log_Level(HZN_LOG_INFO,
             LOGPFX "%s: message being decrypted: wParam(vkCode) 0x%08X"
                    " lParam 0x%08X tag 0x%08X, tls_recurLevel 0x%08X "
                    "extraInfo: 0x%08X vkCode 0x%08X",
             __func__, lpMsg->wParam, lpMsg->lParam, index, tls_recurLevel, kbdMsgInfo.extraInfo,
             kbdMsgInfo.vkCode);
#endif
   if (kbdMsgInfo.value == 0) {
      // The message is not encrypted by us
      return FALSE;
   }

   /*
    * If the scancode is unchanged, then we do not need to update the VK code.
    */
   if (lParam.scanCode == kbdMsgInfo.scanCode) {
      return TRUE;
   }

   lParam.scanCode = kbdMsgInfo.scanCode;
   lpMsg->lParam = lParam.value;

   if (lpMsg->wParam != VK_PROCESSKEY && kbdMsgInfo.vkCode != VK_INVALID) {
      lpMsg->wParam = kbdMsgInfo.vkCode;
   }

   return TRUE;
}


/*
 *----------------------------------------------------------------------
 *
 * LowLevelKeyboardCB --
 *
 *      The keyboard hook callback function, called when our hook is
 *      installed and there is keyboard input to process.
 *
 *      Refer to HookKeyboardFunc() in
 *      bora/apps/rde/viewClient/win32/winKeyboard.c
 *
 *      Refer to "Notes" in ReadDecryptedMessage().
 *
 * Results:
 *      1 to absorb the key.
 *      CallNextHookEx() to pass the key on.
 *
 * Side effects:
 *      "keyboard.kbdMsgInfos" is updated.
 *
 *----------------------------------------------------------------------
 */

static LRESULT CALLBACK
LowLevelKeyboardCB(int nCode,     // IN
                   WPARAM wParam, // IN
                   LPARAM lParam) // IN
{
   /*
    * Be a good citizen -- chain to next hook if action code is negative,
    * as MSDN documentation tells us to do.
    */
   if (nCode < 0) {
      goto out;
   }

   if (Atomic_Read32(&keyboard.enableHook) != TRUE) {
      goto out;
   }

   PKBDLLHOOKSTRUCT pOrigMsg = (PKBDLLHOOKSTRUCT)lParam;
   /*
    * Copy KBDLLHOOKSTRUCT to avoid leaking the decrypted contents
    * to other applications via CallNextHookEx().
    */
   KBDLLHOOKSTRUCT kbdMsg = *pOrigMsg;
   DWORD extraInfo = (DWORD)kbdMsg.dwExtraInfo;
   UINT8 tag = kbdMsg.scanCode & VSCAN_CODE_BITS;

   KbdMsgInfo kbdMsgInfo = {
      .extraInfo = extraInfo,
      .scanCode = kbdMsg.scanCode,
      .vkCode = VK_INVALID,
      .keyUp = FlagOn(kbdMsg.flags, LLKHF_UP) ? TRUE : FALSE,
   };

   if (!HznProtect_DecryptMessage(&kbdMsg)) {
      Log_Level(HZN_LOG_ERROR, LOGPFX "%s: Failed to decrypt message: 0x%08X", __func__, extraInfo);

      if (Atomic_Read32(&hznProtect.blockSendInput)) {
         /*
          * Ignore all invalid (failed to decrypt) keystrokes
          * when block SendInput() is enabled.
          */
         kbdMsgInfo.value = 0;
      }

      goto save_kbd_msg;
   }

   kbdMsgInfo.scanCode = kbdMsg.scanCode;
   kbdMsgInfo.vkCode = kbdMsg.vkCode;

   if (FlagOn(kbdMsg.flags, LLKHF_UP)) {
      kbdMsgInfo.keyUp = TRUE;
   } else {
      kbdMsgInfo.keyUp = FALSE;
   }

#ifdef VMWPROTECT_TEST
   if (g_vmwptester != NULL) {
      int down = FlagOn(kbdMsg.flags, LLKHF_UP) ? FALSE : TRUE;
      /*
       * send out the encrypted and decrypted message.
       */
      LowLevelHookWrapper(g_vmwptester, down, pOrigMsg, &kbdMsg);
   }
#endif

save_kbd_msg:
   UINT8 index = tag;
   if (FlagOn(pOrigMsg->flags, LLKHF_UP)) { // transitionState = KF_UP
      SetFlag(index, VSCAN_KEY_UP_BIT);
   }

   Atomic_Write64(&keyboard.kbdMsgInfos[index], kbdMsgInfo.value);
   Atomic_Write32(&keyboard.kbdMsgTimes[index], kbdMsg.time);

#ifdef HZNPROTECT_DEBUG
   Log_Level(HZN_LOG_INFO,
             LOGPFX "%s: message into list: wParam 0x%08X tag 0x%08X "
                    "vkCode 0x%08X extraInfo 0x%08X time 0x%08X, "
                    "kbdMsgInfos[tag].vkCode 0x%08X",
             __func__, wParam, index, kbdMsg.vkCode, dwExtraInfo, kbdMsg.time, kbdMsgInfo.vkCode);
#endif

out:
   return CallNextHookEx(NULL, nCode, wParam, lParam);
}


/*
 *----------------------------------------------------------------------
 *
 * DecryptKeystrokeMessage --
 *
 *      If the lpMsg is a keystroke message (WM_KEYUP or WM_KEYDOWN),
 *      decrypt the message in lpMsg.
 *
 * Results:
 *      TRUE if keystroke is decrypted or can be skipped.
 *      FALSE if fail to decrypt.
 *
 * Side effects:
 *      If clearCurrentMsg is TRUE, the "tls_currentMsgs[tls_recurLevel]"
 *      will be cleared.
 *
 *      Application will ASSERT if "keyboard.kbdMsgInfos[tag].extraInfo"
 *      does not match with GetMessageExtraInfo().
 *
 *----------------------------------------------------------------------
 */

static BOOL
DecryptKeystrokeMessage(LPMSG lpMsg) // IN/OUT:
{
   if (lpMsg->message != WM_KEYUP && lpMsg->message != WM_KEYDOWN) {
      ASSERT(FALSE);
   }

   MSG_LPARAM lParam = {
      .value = lpMsg->lParam,
   };

   DWORD extraInfo = (DWORD)GetMessageExtraInfo();

   if (!Atomic_Read32(&hznProtect.blockSendInput) && extraInfo == 0) {
      /*
       * WORKAROUND:
       * strange behavior, that sometimes extraInfo returned
       * by GetMessageExtraInfo is 0 instead of the extraInfo set in our driver,
       * usually this happens when user press
       * and hold a key, then move and click mouse.
       * Here we see if we can decrypt the key from
       * cached key stoke which is saved in LowLevelKeyboardCB
       */
      return ReadDecryptedMessage(lpMsg, TRUE, __func__);
   }

   KBDLLHOOKSTRUCT kbdMsg = {
      .scanCode = lParam.scanCode & VSCAN_CODE_BITS,
      .dwExtraInfo = extraInfo,
   };

   if (!HznProtect_DecryptMessage(&kbdMsg)) {
      if (Atomic_Read32(&hznProtect.blockSendInput) && Atomic_Read32(&keyboard.enableHook)) {
         /*
          * Ignore all invalid (failed to decrypt) keystrokes
          * when block SendInput() is enabled.
          */
         return FALSE;
      }

      /*
       * Ignore decryption error when block SendInput() is OFF
       * or encryption is disabled (due to lost of focus).
       */
      return TRUE;
   }

   /*
    * If the scancode is unchanged, then we do not need to update the VK code.
    */
   if (lParam.scanCode == kbdMsg.scanCode) {
      return TRUE;
   }

   /*
    * Write "scanCode" from cache into bit 16~23 of "lpMsg->lParam".
    */
   lParam.scanCode = kbdMsg.scanCode;
   lpMsg->lParam = lParam.value;

   if (lpMsg->wParam != VK_PROCESSKEY) {
      lpMsg->wParam = kbdMsg.vkCode;
   }

   return TRUE;
}


/*
 *-----------------------------------------------------------------------------
 *
 * ImmProcessKeyHook --
 *
 *      Hook of ImmProcessKey() to decrypt key scancodes in IMM when
 *      tls_recurLevel > 0.
 *
 * Results:
 *      Same as return value of ImmProcessKey().
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

static DWORD WINAPI
ImmProcessKeyHook(HWND hWnd,        // IN:
                  HKL hkl,          // IN:
                  UINT uVKey,       // IN:
                  LPARAM lParam,    // IN:
                  DWORD dwHotKeyID) // IN:
{
   ASSERT(hooks.realImmProcessKey != NULL);

   if (Atomic_Read32(&keyboard.enableHook) != TRUE) {
      return hooks.realImmProcessKey(hWnd, hkl, uVKey, lParam, dwHotKeyID);
   }

   MSG msg = {
      .wParam = uVKey,
      .lParam = lParam,
   };
   DWORD ret;

   if (tls_recurLevel > 0) {
#ifdef HZNPROTECT_DEBUG
      Log_Level(HZN_LOG_INFO,
                LOGPFX "%s: start to decrypt message: wParam 0x%08X "
                       "lParam 0x%08X",
                __func__, msg.wParam, msg.lParam);
#endif

      if (!ReadDecryptedMessage(&msg, FALSE, __func__)) {
         if (Atomic_Read32(&hznProtect.blockSendInput)) {
            return IPHK_SKIPTHISKEY;
         }
      }

#ifdef VMWPROTECT_TEST
      if (g_vmwptester != NULL) {
         ImmProcessKeyHookWrapper(g_vmwptester, &msg);
      }
#endif

#ifdef HZNPROTECT_DEBUG
      Log_Level(HZN_LOG_INFO, LOGPFX "%s: decrypted message: wParam 0x%08X lParam 0x%08X", __func__,
                msg.wParam, msg.lParam);
#endif
   }

   ret = hooks.realImmProcessKey(hWnd, hkl, msg.wParam, msg.lParam, dwHotKeyID);

   if (tls_recurLevel > 0) {
      /*
       * If this message is the IME hotkey, it will not be passed to
       * application or hook procedure. MphInternalGetMessage() will
       * not return for this message.
       *
       * Refer to xxxScanSysQueue():
       *        dwImmRet = xxxImmProcessKey( ptiCurrent->pq,
       *                                     pwnd,
       *                                     message,
       *                                     wParamTemp,
       *                                     lParam);
       *        if ( dwImmRet & (IPHK_HOTKEY | IPHK_SKIPTHISKEY) ) {
       *            dwImmRet = 0;
       *            goto SkipMessage;
       *        }
       */
      if (ret & (IPHK_HOTKEY | IPHK_SKIPTHISKEY)) {
#ifdef HZNPROTECT_DEBUG
         Log_Level(HZN_LOG_INFO, LOGPFX "%s: eaten message: wParam 0x%08X lParam 0x%08X ", __func__,
                   msg.wParam, msg.lParam);
#endif
      }
   }

   return ret;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CtfImmNotifyHook --
 *
 *      Hook of CtfImmNotify(), a thin wrapper to TF_Notify(),
 *      to decrypt key scancodes in TSF when tls_recurLevel > 0.
 *
 * Results:
 *      Same as return value of CtfImmNotify().
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

static LRESULT WINAPI
CtfImmNotifyHook(DWORD nCode,   // IN:
                 WPARAM wParam, // IN:
                 LPARAM lParam) // IN:
{
   ASSERT(hooks.realCtfImmNotify != NULL);

   if (Atomic_Read32(&keyboard.enableHook) != TRUE) {
      return hooks.realCtfImmNotify(nCode, wParam, lParam);
   }

   MSG msg = {
      .wParam = wParam,
      .lParam = lParam,
   };
   LRESULT ret;

   /*
    * TF_Notify() will call
    *    SYSTHREAD::OnKeyboardEvent(this, wParam, lParam,
    *                               LOWORD(nCode) == 2);
    * when HIWORD(nCode) == 2.
    */
   if ((tls_recurLevel > 0) && (HIWORD(nCode) == 2)) {
      LPARAM *plParamForKeyMsg = NULL;

#ifdef HZNPROTECT_DEBUG
      Log_Level(HZN_LOG_INFO,
                LOGPFX "%s: start to decrypt message: wParam 0x%08X "
                       "lParam 0x%08X ",
                __func__, msg.wParam, msg.lParam);
#endif

      /*
       * Windows 11 22H2 is first known public build that breaks
       * msctf!TF_Notify by changing its lParam to point to SYSTHREAD struct.
       * The original lParam is stored at pointer sized offset from the start of
       * the struct.
       */
      if (hznProtect.osVersionInfo.dwBuildNumber >= WIN11_22H2_OS_BUILD && lParam != 0) {
         plParamForKeyMsg = (LPARAM *)lParam + 1;
         msg.lParam = *plParamForKeyMsg;
      }

      if (!ReadDecryptedMessage(&msg, FALSE, __func__)) {
         if (Atomic_Read32(&hznProtect.blockSendInput)) {
            /*
             * XXX: A guessed value from TF_Notify to skip this msg.
             *
             * In CThreadInputMgr::_ProcessHotKey()
             *     LABEL_51:
             *        ThreadInputManagerTelemetry::HotKeyFailed(...);
             *        return 0i64;
             *     }
             */
            return 0;
         }
      }

      if (plParamForKeyMsg != NULL) {
         *plParamForKeyMsg = msg.lParam;
         msg.lParam = lParam;
      }

#ifdef HZNPROTECT_DEBUG
      Log_Level(HZN_LOG_INFO, LOGPFX "%s: decrypted message: wParam 0x%08X lParam 0x%08X", __func__,
                msg.wParam, msg.lParam);
#endif

#ifdef VMWPROTECT_TEST
      if (g_vmwptester != NULL) {
         TFNotifyHookWrapper(g_vmwptester, &msg);
      }
#endif
   }

   ret = hooks.realCtfImmNotify(nCode, msg.wParam, msg.lParam);

#ifdef HZNPROTECT_DEBUG
   if (HIWORD(nCode) == 2) {
      Log_Level(HZN_LOG_INFO, LOGPFX "%s: return value of TF_Notify %d\n", __func__, ret);
   }
#endif
   if ((tls_recurLevel > 0) && (HIWORD(nCode) == 2)) {
      /*
       * If TF_Notify() returns a non-zero value, the message will be skipped.
       * MphInternalGetMessage() will not return for this message.
       *
       * Refer to xxxCallCtfHook():
       *    __int64 __fastcall xxxCallCtfHook(int nCode_HIWORD,
       *                                      int nCode_LOWORD,
       *                                      __int64 wParam,
       *                                      __int64 lParam)
       *    {
       *      unsigned int nCode; // ebp
       *    ...
       *      nCode = (unsigned __int16)nCode_LOWORD |
       *              ((unsigned __int16)nCode_HIWORD << 16);
       *    ...
       *                return fnHkINDWORD(nCode,
       *                                   wParam,
       *                                   lParam,
       *                                   0,
       *                                   *(_QWORD *)(gpsi + 856i64),
       *                                   (__int64)&v16);
       *    ...
       *    }
       *
       * Also refer to xxxScanSysQueue():
       *            v78 = 0;
       *    ...
       *            if ( (v208 & 2) != 0 )
       *              v78 = 2;
       *    ...
       *            v81 = xxxCallCtfHook(2i64, v78, v190, v192);
       *            v78 = v81;
       *    ...
       *              if ( !v78 )
       *                goto LABEL_245;
       *              goto SkipMessage;
       */
      if (ret - 2 > 1) {
#ifdef HZNPROTECT_DEBUG
         Log_Level(HZN_LOG_INFO, LOGPFX "%s: eaten message: wParam 0x%08X lParam 0x%08X ", __func__,
                   msg.wParam, msg.lParam);
#endif
      }
   }

   return ret;
}


/*
 *-----------------------------------------------------------------------------
 *
 * MphInternalGetMessage --
 *
 *      Hook of NtUserRealInternalGetMessage() to decrypt key scancodes
 *      for main thread.
 *
 *      "tls_recurLevel" is added by 1 when invoking pfnInternalGetMessage()
 *      (user MPH function or the original system function),
 *      reset when pfnInternalGetMessage() returns.
 *
 * Results:
 *      The return value of pfnInternalGetMessage().
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

static BOOL WINAPI
MphInternalGetMessage(PMSG lpMsg,         // OUT:
                      HWND hWnd,          // IN:
                      UINT wMsgFilterMin, // IN:
                      UINT wMsgFilterMax, // IN:
                      UINT flags,         // IN:
                      BOOL fGetMessage)   // IN:
{
   INTERNALGETMESSAGEPROC pfnInternalGetMessage =
      (tls_cUserMPHs > 0) ? hooks.userMPH.pfnInternalGetMessage : // user MPH function
         hooks.realMPH.pfnInternalGetMessage;                     // original system function

   if (GetCurrentThreadId() != Atomic_Read32(&hznProtect.mainThreadId)) {
      return pfnInternalGetMessage(lpMsg, hWnd, wMsgFilterMin, wMsgFilterMax, flags, fGetMessage);
   }

   BOOL ret;

   tls_recurLevel++;

get_next_msg:
   ret = pfnInternalGetMessage(lpMsg, hWnd, wMsgFilterMin, wMsgFilterMax, flags, fGetMessage);

   if (ret > 0) {
      if (lpMsg->message == WM_KEYUP || lpMsg->message == WM_KEYDOWN) {
#ifdef HZNPROTECT_DEBUG
         Log_Level(HZN_LOG_INFO,
                   LOGPFX "%s: start to decrypt message: 0x%08X wParam 0x%08X"
                          " lParam 0x%08X  extraInfo 0x%08X  time 0x%08X ",
                   __func__, lpMsg->message, lpMsg->wParam, lpMsg->lParam, GetMessageExtraInfo(),
                   lpMsg->time);
#endif

         if (!DecryptKeystrokeMessage(lpMsg)) {
            if (Atomic_Read32(&hznProtect.blockSendInput) && Atomic_Read32(&keyboard.enableHook)) {
               if (fGetMessage) {
                  goto get_next_msg;
               } else {
                  ret = FALSE;
               }
            }
         }

#ifdef HZNPROTECT_DEBUG
         Log_Level(HZN_LOG_INFO,
                   LOGPFX "%s: decrypted message: 0x%08X  wParam 0x%08X "
                          "lParam 0x%08X ",
                   __func__, lpMsg->message, lpMsg->wParam, lpMsg->lParam);
      } else if (lpMsg->message == WM_CHAR) {
         Log_Level(HZN_LOG_INFO,
                   LOGPFX "%s: get wm_char message: 0x%08X wParam 0x%08X"
                          " lParam 0x%08X  extraInfo 0x%08X  time 0x%08X ",
                   __func__, lpMsg->message, lpMsg->wParam, lpMsg->lParam, GetMessageExtraInfo(),
                   lpMsg->time);
#endif
      }
   }

   tls_recurLevel--;

#ifdef VMWPROTECT_TEST
   if (g_vmwptester != NULL && !fGetMessage) {
      if (lpMsg->message == WM_KEYUP || lpMsg->message == WM_SYSKEYUP ||
          lpMsg->message == WM_KEYDOWN || lpMsg->message == WM_SYSKEYDOWN) {
         PeekMessageHookWrapper(g_vmwptester, lpMsg);
      }
   }
#endif

   return ret;
}


/*
 *-----------------------------------------------------------------------------
 *
 * MphWaitMessageEx --
 *
 *      Hook of NtUserRealWaitMessageEx() to call user MPH function or the
 *      original system function.
 *
 * Results:
 *      Return value of pfnWaitMessageEx().
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

static BOOL WINAPI
MphWaitMessageEx(UINT fsWakeMask, // IN:
                 DWORD dwTimeout) // IN:
{
   WAITMESSAGEEXPROC pfnWaitMessageEx =
      (tls_cUserMPHs > 0) ? hooks.userMPH.pfnWaitMessageEx : // user MPH function
         hooks.realMPH.pfnWaitMessageEx;                     // original system function

   return pfnWaitMessageEx(fsWakeMask, dwTimeout);
}


/*
 *-----------------------------------------------------------------------------
 *
 * MphGetQueueStatus --
 *
 *      Hook of RealGetQueueStatus() to call user MPH function or the original
 *      system function.
 *
 * Results:
 *      Return value of pfnGetQueueStatus().
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

static DWORD WINAPI
MphGetQueueStatus(UINT flags) // IN:
{
   GETQUEUESTATUSPROC pfnGetQueueStatus =
      (tls_cUserMPHs > 0) ? hooks.userMPH.pfnGetQueueStatus : // user MPH function
         hooks.realMPH.pfnGetQueueStatus;                     // original system function

   return pfnGetQueueStatus(flags);
}


/*
 *-----------------------------------------------------------------------------
 *
 * MphMsgWaitForMultipleObjectsEx --
 *
 *      Hook of RealMsgWaitForMultipleObjectsEx() to call user MPH function or
 *      the original system function.
 *
 * Results:
 *      Return value of pfnMsgWaitForMultipleObjectsEx().
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

static DWORD WINAPI
MphMsgWaitForMultipleObjectsEx(DWORD nCount,           // IN:
                               CONST HANDLE *pHandles, // IN:
                               DWORD dwMilliseconds,   // IN:
                               DWORD dwWakeMask,       // IN:
                               DWORD dwFlags)          // IN:
{
   MSGWAITFORMULTIPLEOBJECTSEXPROC pfnMsgWaitForMultipleObjectsEx =
      (tls_cUserMPHs > 0) ? hooks.userMPH.pfnMsgWaitForMultipleObjectsEx : // user MPH function
         hooks.realMPH.pfnMsgWaitForMultipleObjectsEx;                     // original function

   return pfnMsgWaitForMultipleObjectsEx(nCount, pHandles, dwMilliseconds, dwWakeMask, dwFlags);
}


/*
 *-----------------------------------------------------------------------------
 *
 * InitVmwMPH --
 *
 *      Callback function used in RegisterMessagePumpHook() call to set up MPH
 *      for current process.
 *
 *      When "dwCmd" is UIAH_INITIALIZE, "pvParam" is override with our own
 *      hook functions, the original system functions are saved to "g_realMPH".
 *
 *      When "dwCmd" is UIAH_UNINITIALIZE, "g_realMPH" is cleared.
 *
 * Results:
 *      TRUE of success, or FALSE.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

static BOOL CALLBACK
InitVmwMPH(DWORD dwCmd,   // IN:
           void *pvParam) // IN/OUT:
{
   PMESSAGEPUMPHOOK pRealMPH = (PMESSAGEPUMPHOOK)pvParam;
   BOOL ret = FALSE;

   switch (dwCmd) {

   case UIAH_INITIALIZE:
      if ((pRealMPH == NULL) || (pRealMPH->cbSize != sizeof(MESSAGEPUMPHOOK))) {
         break;
      }

      /*
       * Save the original system functions so that HznProtect can call
       * them later
       */
      hooks.realMPH = *pRealMPH;

      /*
       * Replace the functions that HznProtect needs to override
       */
      pRealMPH->pfnInternalGetMessage = MphInternalGetMessage;
      pRealMPH->pfnWaitMessageEx = MphWaitMessageEx;
      pRealMPH->pfnGetQueueStatus = MphGetQueueStatus;
      pRealMPH->pfnMsgWaitForMultipleObjectsEx = MphMsgWaitForMultipleObjectsEx;

      ret = TRUE;
      break;

   case UIAH_UNINITIALIZE:
      ZeroMemory(&hooks.realMPH, sizeof hooks.realMPH);
      ret = TRUE;

      break;

   } // switch (dwCmd)

   return ret;
}


/*
 *-----------------------------------------------------------------------------
 *
 * RegisterMphHook --
 *
 *      Hook of RegisterMessagePumpHook() to let HznProtect be the man
 *      in the middle of MPH.
 *
 *      Under the current implementation of MPH's, only one set of MPH's per
 *      process can be installed. However, DUser engine need the MPH to work.
 *      In order to let HznProtect and DUser engine share the only one
 *      MPH installation point, we implement a hack here:
 *
 *      HznProtect will always be the only MPH handler from the OS' point
 *      of view. "g_RegisterMessagePumpHookFn" is called with InitVmwMPH in
 *      HznProtect_InstallHooks(), which installs our MPH handler to OS.
 *
 *      When DUser engine installs its MPH, RegisterMphHook() is called. DUser
 *      engine's MPH will be saved to "g_userMPH", and "tls_cUserMPHs" is set
 *      in order to tell our MPH handlers to call DUser engine's MPH functions.
 *
 * Results:
 *      TRUE if success, or FALSE.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

static BOOL
RegisterMphHook(INITMESSAGEPUMPHOOK pfnInitMPH) // IN:
{
   BOOL init = FALSE;
   BOOL ret = FALSE;

   if (pfnInitMPH == NULL) {
      SetLastError(ERROR_INVALID_PARAMETER);
      return FALSE;
   }

   EnterCriticalSection(&hooks.lockMPH);

   if (hooks.cInitUserMPH == 0) {
      MESSAGEPUMPHOOK tempMPH = hooks.realMPH;

      /*
       * First time we are initializing.
       */
      ASSERT(hooks.fnInitUserMPH == NULL);
      hooks.fnInitUserMPH = pfnInitMPH;

      if (!hooks.fnInitUserMPH(UIAH_INITIALIZE, &tempMPH) ||
          (tempMPH.cbSize != sizeof hooks.userMPH)) {
         goto unlock;
      }

      hooks.userMPH = tempMPH;
      init = TRUE;
   } else {
      if (hooks.fnInitUserMPH == pfnInitMPH) {
         /*
          * Initializing a second time with the same callback.
          */
         init = TRUE;
      }
   }

   if (init) {
      /*
       * Initialize MPH's on this thread.
       *
       * Always use InitVmwMPH() since only one set of MPH's per process
       * can be installed.
       */
      if (hooks.realRegisterMPH(InitVmwMPH)) {
         tls_cUserMPHs++;
         hooks.cInitUserMPH++;
         ret = TRUE;
      }
   }

unlock:
   LeaveCriticalSection(&hooks.lockMPH);
   return ret;
}


/*
 *-----------------------------------------------------------------------------
 *
 * UnregisterMphHook --
 *
 *      Hook of UnregisterMessagePumpHook() to let HznProtect be the man
 *      in the middle of MPH.
 *
 *      Refer to comment of RegisterMphHook().
 *
 * Results:
 *      TRUE if success, or FALSE.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

static BOOL
UnregisterMphHook(VOID)
{
   BOOL ret = FALSE;

   EnterCriticalSection(&hooks.lockMPH);

   if (hooks.cInitUserMPH <= 0) {
      /*
       * UnregisterMessagePumpHook() called without matching
       * RegisterMessagePumpHook()
       */
      goto unlock;
   }

   /*
    * Uninitialize this thread's WMH.
    * When the reference count reaches 0, the thread will no longer be hooked.
    */
   if (tls_cUserMPHs <= 0) {
      goto unlock;
   }

   if (!hooks.realUnregisterMPH()) {
      goto unlock;
   }

   tls_cUserMPHs--;

   if (--hooks.cInitUserMPH == 0) {
      /*
       * Final unload: make callback and reset
       */
      hooks.fnInitUserMPH(UIAH_UNINITIALIZE, NULL);

      ZeroMemory(&hooks.userMPH, sizeof hooks.userMPH);
      hooks.fnInitUserMPH = NULL;
   }

   ret = TRUE;

unlock:
   LeaveCriticalSection(&hooks.lockMPH);
   return ret;
}


/*
 *-----------------------------------------------------------------------------
 *
 * vm_libc_strncpy_mod --
 *
 *      strncpy() copied from `bora/vmcore/public/vm_libc_string_common.h`
 *      to make sure the call to strncpy() is inlined.
 *
 *      Unlike strncpy(), the extra space in `dst` will not be set to zero.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

static __forceinline void
vm_libc_strncpy_mod(char *dst,       // IN:
                    const char *src, // IN:
                    const size_t n)  // IN:
{
   size_t size = 0;

   while (size < n && *src != '\0') {
      *dst++ = *src++;
      size++;
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * PatchImmApiEntries -
 *
 *      Hook ImmProcessKey() and CtfImmNotify() by patching function pointers
 *      in `gImmApiEntries` of `user32.dll`.
 *
 *      typedef struct {
 *          BOOL (WINAPI* ImmWINNLSEnableIME)(HWND, BOOL);
 *          BOOL (WINAPI* ImmWINNLSGetEnableStatus)(HWND);
 *          LRESULT (WINAPI* ImmSendIMEMessageExW)(HWND, LPARAM);
 *          LRESULT (WINAPI* ImmSendIMEMessageExA)(HWND, LPARAM);
 *      ...
 *          DWORD (WINAPI* ImmProcessKey)(HWND, HKL, UINT, LPARAM, DWORD);
 *      ...
 *          LRESULT (WINAPI* CtfImmNotify)(DWORD, WPARAM, LPARAM);
 *      ...
 *      } ImmApiEntries;
 *
 *      `gImmApiEntries` (struct ImmApiEntries) in `user32.dll` is initialized
 *      by `user32.dll!_InitializeImmEntryTable` when `imm32.dll` is loaded:
 *
 *         KernelBase.dll!GetProcAddress
 *         user32.dll!_InitializeImmEntryTable
 *         user32.dll!User32InitializeImmEntryTable
 *         imm32.dll!_ImmDllInitialize
 *         ntdll.dll!LdrpCallInitRoutine --> DLL_PROCESS_ATTACH
 *         ntdll.dll!LdrpInitializeNode
 *         ntdll.dll!LdrpInitializeGraphRecurse
 *         ntdll.dll!LdrpPrepareModuleForExecution
 *         ntdll.dll!LdrpLoadDllInternal
 *         ntdll.dll!LdrpLoadDll
 *         ntdll.dll!LdrLoadDll
 *         KernelBase.dll!LoadLibraryExW
 *         user32.dll!_InitializeImmEntryTable
 *         user32.dll!_UserClientDllInitialize
 *         ntdll.dll!LdrpCallInitRoutine
 *         ntdll.dll!LdrpInitializeNode
 *         ntdll.dll!LdrpInitializeGraphRecurse
 *         ntdll.dll!LdrpInitializeGraphRecurse
 *         ntdll.dll!LdrpInitializeProcess
 *         ntdll.dll!_LdrpInitialize
 *         ntdll.dll!LdrpInitialize
 *         ntdll.dll!LdrInitializeThunk
 *
 *      The value of pointers in `gImmApiEntries` comes from the address of
 *      corresponding functions in `imm32.dll`.
 *
 *      `gImmApiEntries` is in `.data` section of `user32.dll`. To locate it,
 *      this function gets the value of first 4 pointers (ImmWINNLSEnableIME,
 *      ImmWINNLSGetEnableStatus, ImmSendIMEMessageExW, ImmSendIMEMessageExA)
 *      of `gImmApiEntries` from the export table of `imm32.dll`, then search
 *      them from the beginning of the `.data` section of `user32.dll`.
 *
 *      The 4 pointers used to locate `gImmApiEntries` belong to 4 public
 *      functions of `user32.dll`: WINNLSEnableIME(), WINNLSGetEnableStatus(),
 *      SendIMEMessageExW(), and SendIMEMessageExA(). They all obsoleted, but
 *      should be stable.
 *
 *      After getting the address of `gImmApiEntries`, we find `ImmProcessKey`
 *      and `CtfImmNotify` in it using the address read from the export table
 *      of `imm32.dll`, replace them with the address of ImmProcessKeyHook()
 *      and CtfImmNotifyHook(). After then, `user32.dll` will call our hooked
 *      functions instead of the functions in `imm32.dll`.
 *
 *      After hooking, the call stack could be:
 *
 *      ImmProcessKeyHook():
 *
 *         imm32.dll!ImmProcessKey
 *         hznprotect.dll!ImmProcessKeyHook
 *         user32.dll!__ClientImmProcessKey
 *         ntdll.dll!KiUserCallbackDispatcherContinue
 *         win32u.dll!NtUserRealInternalGetMessage
 *         hznprotect.dll!MphInternalGetMessage
 *         user32.dll!__ClientGetMessageMPH
 *         ntdll.dll!KiUserCallbackDispatcherContinue
 *         win32u.dll!NtUserGetMessage
 *         user32.dll!GetMessageW
 *         msctf.dll!CThreadInputMgr::GetMessageW
 *         [Managed to Native Transition]
 *         WindowsBase.dll!System.Windows.Threading.Dispatcher.GetMessage
 *         WindowsBase.dll!System.Windows.Threading.Dispatcher.PushFrameImpl
 *         WindowsBase.dll!System.Windows.Threading.Dispatcher.PushFrame
 *         WindowsBase.dll!System.Windows.Threading.Dispatcher.Run
 *         PresentationFramework.dll!System.Windows.Application.RunDispatcher
 *         PresentationFramework.dll!System.Windows.Application.RunInternal
 *         PresentationFramework.dll!System.Windows.Application.Run
 *         VMware.Horizon.Client.UI.dll!VMware.Horizon.Client.UI.App.Main
 *
 *      CtfImmNotifyHook():
 *
 *         msctf.dll!TF_Notify
 *         imm32.dll!CtfImmNotify
 *         hznprotect.dll!CtfImmNotifyHook
 *         user32.dll!CtfHookProcWorker
 *         user32.dll!CallHookWithSEH
 *         user32.dll!__fnHkINDWORD
 *         ntdll.dll!KiUserCallbackDispatcherContinue
 *         win32u.dll!NtUserRealInternalGetMessage
 *         hznprotect.dll!MphInternalGetMessage
 *         user32.dll!__ClientGetMessageMPH
 *         ntdll.dll!KiUserCallbackDispatcherContinue
 *         win32u.dll!NtUserGetMessage
 *         user32.dll!GetMessageW
 *         msctf.dll!CThreadInputMgr::GetMessageW
 *         [Managed to Native Transition]
 *         WindowsBase.dll!System.Windows.Threading.Dispatcher.GetMessage
 *         WindowsBase.dll!System.Windows.Threading.Dispatcher.PushFrameImpl
 *         WindowsBase.dll!System.Windows.Threading.Dispatcher.PushFrame
 *         WindowsBase.dll!System.Windows.Threading.Dispatcher.Run
 *         PresentationFramework.dll!System.Windows.Application.RunDispatcher
 *         PresentationFramework.dll!System.Windows.Application.RunInternal
 *         PresentationFramework.dll!System.Windows.Application.Run
 *         VMware.Horizon.Client.UI.dll!VMware.Horizon.Client.UI.App.Main
 *
 *
 * Results:
 *      TRUE if success, or FALSE (last error is set).
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

static BOOL
PatchImmApiEntries(void)
{
#define STR_IN_UINT64(name, v0, v1, v2)                                                            \
   const UINT64 str_##name##_0 = v0##ULL;                                                          \
   const UINT64 str_##name##_1 = v1##ULL;                                                          \
   const UINT64 str_##name##_2 = v2##ULL;

   /*
    * In order to fool disassembler and competitor, we encode key function
    * name strings (ASCII) into UINT64 variables (in little endian format).
    */
   STR_IN_UINT64(ImmProcessKey, 0x65636F72506D6D49, 0x00000079654B7373, 0)
   STR_IN_UINT64(CtfImmNotify, 0x6F4E6D6D49667443, 0x0000000079666974, 0)
   STR_IN_UINT64(ImmWINNLSEnableIME, 0x4C4E4E49576D6D49, 0x49656C62616E4553, 0x000000000000454D)
   STR_IN_UINT64(ImmWINNLSGetEnableStatus, 0x4C4E4E49576D6D49, 0x62616E4574654753,
                 0x737574617453656C)
   STR_IN_UINT64(ImmSendIMEMessageExW, 0x49646E65536D6D49, 0x67617373654D454D, 0x0000000057784565)
   STR_IN_UINT64(ImmSendIMEMessageExA, 0x49646E65536D6D49, 0x67617373654D454D, 0x0000000041784565)

#undef STR_IN_UINT64

   PROC immApiEntry1 = NULL; // ImmWINNLSEnableIME
   PROC immApiEntry2 = NULL; // ImmWINNLSGetEnableStatus
   PROC immApiEntry3 = NULL; // ImmSendIMEMessageExW
   PROC immApiEntry4 = NULL; // ImmSendIMEMessageExA

   UINT_PTR rvaBase;
   PIMAGE_DOS_HEADER pDosHeader;
   PIMAGE_NT_HEADERS pNtHeader;

   /*
    * Find export table of `imm32.dll`.
    */
   rvaBase = (UINT_PTR)hznProtect.hImm32;
   pDosHeader = (PIMAGE_DOS_HEADER)rvaBase;
   pNtHeader = (PIMAGE_NT_HEADERS)(rvaBase + pDosHeader->e_lfanew);

   PIMAGE_EXPORT_DIRECTORY pExportDir;
   IMAGE_DATA_DIRECTORY exportData =
      pNtHeader->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_EXPORT];
   if (exportData.VirtualAddress == 0 || exportData.Size < sizeof *pExportDir) {
      SetLastError(ERROR_BAD_EXE_FORMAT);
      return FALSE;
   }

   pExportDir = (PIMAGE_EXPORT_DIRECTORY)(rvaBase + exportData.VirtualAddress);

   PULONG functions = (PULONG)(rvaBase + pExportDir->AddressOfFunctions);
   PSHORT ordinals = (PSHORT)(rvaBase + pExportDir->AddressOfNameOrdinals);
   PULONG names = (PULONG)(rvaBase + pExportDir->AddressOfNames);
   DWORD maxName = pExportDir->NumberOfNames;
   DWORD maxFunc = pExportDir->NumberOfFunctions;
   DWORD found = 0;

   /*
    * Implement GetProcAddress(hImm32, ...) for ImmProcessKey, CtfImmNotify,
    * ImmWINNLSEnableIME, ImmWINNLSGetEnableStatus, ImmSendIMEMessageExW,
    * and ImmSendIMEMessageExA at the same time.
    *
    * Avoid using GetProcAddress() to fool the disassembler and competitor.
    */

   for (DWORD i = 0; i < maxName; i++) {
      ULONG ordinal = ordinals[i];

      if (ordinal >= maxFunc) {
         break;
      }

      PCSTR name = (PCSTR)(rvaBase + names[i]);

      UINT64 nameBuf[3] = {0};
      vm_libc_strncpy_mod((PSTR)nameBuf, name, sizeof nameBuf);

      /*
       * Check if `name` in [
       *    ImmProcessKey, CtfImmNotify,
       *    ImmWINNLSEnableIME, ImmWINNLSGetEnableStatus,
       *    ImmSendIMEMessageExW, ImmSendIMEMessageExA
       * ] or not using a fast way,
       * which is also obfuscated to the disassembler and competitor.
       */

      if (nameBuf[0] == str_ImmProcessKey_0 && nameBuf[1] == str_ImmProcessKey_1) {
         hooks.realImmProcessKey = (IMMPROCESSKEYPROC)(rvaBase + functions[ordinal]);
         found++;
         continue;
      }

      if (nameBuf[0] == str_CtfImmNotify_0 && nameBuf[1] == str_CtfImmNotify_1) {
         hooks.realCtfImmNotify = (CTFIMMNOTIFYPROC)(rvaBase + functions[ordinal]);
         found++;
         continue;
      }

      if (nameBuf[0] == str_ImmWINNLSEnableIME_0) {
         if (nameBuf[1] == str_ImmWINNLSEnableIME_1 && nameBuf[2] == str_ImmWINNLSEnableIME_2) {
            immApiEntry1 = (PROC)(rvaBase + functions[ordinal]);
            found++;
            continue;
         } // `name` ends with "SEnableIME"

         ASSERT(str_ImmWINNLSGetEnableStatus_0 == str_ImmWINNLSEnableIME_0);
         if (nameBuf[1] == str_ImmWINNLSGetEnableStatus_1 &&
             nameBuf[2] == str_ImmWINNLSGetEnableStatus_2) {
            immApiEntry2 = (PROC)(rvaBase + functions[ordinal]);
            found++;
            continue;
         } // `name` ends with "SGetEnableStatus"
      } // `name` starts with "ImmWINNL"

      if (nameBuf[0] == str_ImmSendIMEMessageExW_0 && nameBuf[1] == str_ImmSendIMEMessageExW_1) {
         if (nameBuf[2] == str_ImmSendIMEMessageExW_2) {
            immApiEntry3 = (PROC)(rvaBase + functions[ordinal]);
            found++;
            continue;
         } // `name` ends with "eExW"

         ASSERT(str_ImmSendIMEMessageExA_0 == str_ImmSendIMEMessageExW_0);
         ASSERT(str_ImmSendIMEMessageExA_1 == str_ImmSendIMEMessageExW_1);
         if (nameBuf[2] == str_ImmSendIMEMessageExA_2) {
            immApiEntry4 = (PROC)(rvaBase + functions[ordinal]);
            found++;
            continue;
         } // `name` ends with "eExA"
      } // `name` starts with "ImmSendIMEMessag"

      if (found == 6) {
         break;
      }
   } // for each name in EAT of `imm32.dll`

   if (found != 6) {
      hooks.realImmProcessKey = NULL;
      hooks.realCtfImmNotify = NULL;
      SetLastError(ERROR_PROC_NOT_FOUND);
      return FALSE;
   }

   /*
    * Find section table of `user32.dll`.
    */
   rvaBase = (UINT_PTR)hznProtect.hUser32;
   pDosHeader = (PIMAGE_DOS_HEADER)rvaBase;
   pNtHeader = (PIMAGE_NT_HEADERS)(rvaBase + pDosHeader->e_lfanew);

   USHORT sectionCount = pNtHeader->FileHeader.NumberOfSections;
   PIMAGE_SECTION_HEADER pSectionHeader = IMAGE_FIRST_SECTION(pNtHeader);
   PUINT_PTR dataSection = NULL;
   PUINT_PTR dataSectionEnd = NULL;

   /*
    * Find `.data` section in the section table of `user32.dll`.
    */
   for (USHORT i = 0; i < sectionCount; i++, pSectionHeader++) {
      UINT64 dataSectionName = 0x000000617461642EULL; // ".data", little endian
      UINT64 sectionNameBuf = 0;

      _STATIC_ASSERT(sizeof sectionNameBuf == sizeof pSectionHeader->Name);
      vm_libc_strncpy_mod((PSTR)&sectionNameBuf, (PCCH)pSectionHeader->Name, sizeof sectionNameBuf);

      if (dataSectionName == sectionNameBuf) {
         DWORD dataSectionSize =
            max(pSectionHeader->SizeOfRawData, pSectionHeader->Misc.VirtualSize);
         dataSection = (PUINT_PTR)(rvaBase + pSectionHeader->VirtualAddress);
         dataSectionEnd = (PUINT_PTR)(rvaBase + pSectionHeader->VirtualAddress + dataSectionSize);

         break;
      }
   } // for each section in `user32.dll`

   if (dataSection == NULL) {
      SetLastError(ERROR_SXS_SECTION_NOT_FOUND);
      return FALSE;
   }

   /*
    * Per https://learn.microsoft.com/en-us/archive/msdn-magazine/2002/february/
    *     inside-windows-win32-portable-executable-file-format-in-detail
    *    "Once mapped into memory, sections always start on at least a page
    *     boundary."
    */
   if ((UINT_PTR)dataSection % PAGE_SIZE != 0) {
      SetLastError(ERROR_MAPPED_ALIGNMENT);
      return FALSE;
   }

   /*
    * Find `gImmApiEntries` in the `.data` section of `user32.dll`, then
    * patch `gImmApiEntries.ImmProcessKey` and `gImmApiEntries.CtfImmNotify`
    * in it.
    */
   for (PUINT_PTR p = dataSection; p < dataSectionEnd; p++) {
      if (*(p + 0) == (UINT_PTR)immApiEntry1 && *(p + 1) == (UINT_PTR)immApiEntry2 &&
          *(p + 2) == (UINT_PTR)immApiEntry3 && *(p + 3) == (UINT_PTR)immApiEntry4) {
         /*
          * `p` points to `gImmApiEntries` now.
          * `gImmApiEntries` has 60 function pointers in it.
          */
         PUINT_PTR pEnd = min(p + 60, dataSectionEnd);

         for (PUINT_PTR p1 = p + 4; p1 < pEnd; p1++) {
            if (*p1 == (UINT_PTR)hooks.realImmProcessKey) {
               *p1 = (UINT_PTR)ImmProcessKeyHook;

               for (PUINT_PTR p2 = p + 4; p2 < pEnd; p2++) {
                  if (*p2 == (UINT_PTR)hooks.realCtfImmNotify) {
                     *p2 = (UINT_PTR)CtfImmNotifyHook;

                     return TRUE;
                  } // `gImmApiEntries.CtfImmNotify` patched.
               } // for each pointer in `gImmApiEntries`

               *p1 = (UINT_PTR)hooks.realImmProcessKey; // restore on failure

               goto err_out;
            } // `gImmApiEntries.ImmProcessKey` patched.
         } // for each pointer in `gImmApiEntries`

         goto err_out;
      } // found `gImmApiEntries` (struct ImmApiEntries)
   } // for each pointer in the `.data` section of `user32.dll`

err_out:
   SetLastError(ERROR_PATCH_TARGET_NOT_FOUND);
   return FALSE;
}


/*
 *-----------------------------------------------------------------------------
 *
 * HznProtect_SetEncryptStatus -
 *
 *      Given a window and its focused status, this function will decide
 *      whether the given window should be grabbed/ungrabbed by driver.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

void
HznProtect_SetEncryptStatus(HWND hWnd,    // IN:
                            BOOL focused) // IN:
{
   if (hznProtect.driverHandle == NULL) {
      ASSERT(FALSE);
      return;
   }

   if (!focused) {
      SetDriverEncryptStatus(FALSE);
      Atomic_Write32(&hznProtect.blockSendInput, FALSE);
      return;
   }

   HANDLE blockKeylogger =
      (hWnd == NULL) ? NULL : GetPropW(hWnd, BLOCK_KEYLOGGER_WINDOW_PROPERTY_NAME);
   BOOL blockKeyloggerEnabled = blockKeylogger == NULL || // MWC window
                                blockKeylogger == BLOCK_KEYLOGGER_GPO_ON;
   BOOL blockSendInputEnabled = FALSE;

   if (blockKeyloggerEnabled) {
      HANDLE blockSendInput =
         (hWnd == NULL) ? NULL : GetPropW(hWnd, BLOCK_SENDINPUT_WINDOW_PROPERTY_NAME);
      blockSendInputEnabled = blockSendInput == NULL || // MWC window
                              blockSendInput == BLOCK_SENDINPUT_GPO_ON;
   } // blockKeyloggerEnabled

   Atomic_Write32(&hznProtect.blockSendInput, blockSendInputEnabled);
   SetDriverEncryptStatus(blockKeyloggerEnabled);
}


/*
 *-----------------------------------------------------------------------------
 *
 * CallWinprocCB -
 *
 *      Call back of WH_CALLWNDPROC hook. Check whether current message is
 *      WM_ACTIVATE. MWC windows, seamless windows and VDI top windows are
 *      all from main thread of UI process. This function will get their
 *      WM_ACTIVATE event before wndProc handles it. When the message shows
 *      the window is activated, then grab hznprotect driver, otherwise ungrab
 *      the driver.
 *
 * Results:
 *      Return value of CallNextHookEx.
 *
 * Side effects:
 *      Set vmwportect driver encryption status.
 *
 *-----------------------------------------------------------------------------
 */

static LRESULT CALLBACK
CallWinprocCB(int nCode,     // IN:
              WPARAM wParam, // IN:
              LPARAM lParam) // IN:
{
   /*
    * From MSDN, if nCode is less than zero, the hook procedure
    * must return the value returned by CallNextHookEx.
    */
   if (nCode < 0) {
      return CallNextHookEx(NULL, nCode, wParam, lParam);
   }

   CWPSTRUCT *cwp = (CWPSTRUCT *)lParam;

   switch (cwp->message) {
   case WM_ACTIVATE:
      /*
       * cwp->wParam: The low-order word specifies whether the window is
       *              being activated or deactivated.
       * WA_ACTIVE       1
       * WA_CLICKACTIVE  2
       * WA_INACTIVE     0
       */
      BOOL activated = LOWORD(cwp->wParam) > 0;
      HWND parent = GetAncestor((HWND)cwp->hwnd, GA_ROOTOWNER);

      Log_Level(HZN_LOG_VERBOSE,
                LOGPFX "%s: receive WM_ACTIVATE for "
                       "window=0x%x, active=%d, parent window=0x%x",
                __func__, (HWND)cwp->hwnd, activated, parent);

      HznProtect_SetEncryptStatus(parent, activated);
      break;

   default:
      break;
   }

   return CallNextHookEx(NULL, nCode, wParam, lParam);
}


/*
 *-----------------------------------------------------------------------------
 *
 * HznProtect_OpenDriver --
 *
 *      Open driver handle.
 *
 * Results:
 *      TRUE if success, or FALSE (last error is set).
 *
 * Side effects:
 *      "g_driverHandle" and "g_driverInfo" is modified.
 *
 *-----------------------------------------------------------------------------
 */

BOOL
HznProtect_OpenDriver(void)
{
   BOOL ok;
   HANDLE hDriver;

   if (hznProtect.driverHandle != NULL) {
      Log_Level(HZN_LOG_VERBOSE, LOGPFX "%s: driver already open", __func__);
      return TRUE;
   }

   hDriver = CreateFileW(HZNPROTECT_WIN32_DEVICE_NAME, GENERIC_READ | GENERIC_WRITE,
                         FILE_SHARE_READ | FILE_SHARE_WRITE,
                         NULL,                  // no SECURITY_ATTRIBUTES structure
                         OPEN_EXISTING,         // No special create flags
                         FILE_ATTRIBUTE_NORMAL, // No special attributes
                         NULL);
   if (hDriver == INVALID_HANDLE_VALUE) {
      return FALSE;
   }

   ok = ProbeDriver(hDriver);
   if (!ok) {
      DWORD err = GetLastError();
      CloseHandle(hDriver);
      SetLastError(err);
      return FALSE;
   }

   hznProtect.driverHandle = hDriver;
   SetLastError(ERROR_SUCCESS);
   return TRUE;
}


/*
 *-----------------------------------------------------------------------------
 *
 * HznProtect_CloseDriver --
 *
 *      Close driver handle.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      "g_driverHandle" and "g_driverInfo" is modified.
 *
 *-----------------------------------------------------------------------------
 */

void
HznProtect_CloseDriver(void)
{
   if (hznProtect.driverHandle != NULL) {
      CloseHandle(hznProtect.driverHandle);
      hznProtect.driverHandle = NULL;
      RtlZeroMemory(&hznProtect.driverInfo, sizeof hznProtect.driverInfo);
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * HznProtect_InstallHooks --
 *
 *      Open driver handle and install hooks with TrapAPI.
 *
 *      This function should be invoked on main thread
 *      (the thread that runs the message loop).
 *
 * Results:
 *      TRUE if success, or FALSE (last error is set).
 *
 * Side effects:
 *      keyboard.installHook is set to TRUE.
 *      g_mainThreadId is set to the thread that calls this function.
 *      "g_driverHandle" and "g_driverInfo" is modified.
 *
 *-----------------------------------------------------------------------------
 */

BOOL
HznProtect_InstallHooks(void)
{
   DWORD err;
   DWORD mainThreadId = GetCurrentThreadId();

   if (!Atomic_CMPXCHG32(&hznProtect.mainThreadId, 0, mainThreadId)) {
      err = ERROR_ALREADY_INITIALIZED;
      goto err_out;
   }

   if (!HznProtect_OpenDriver()) {
      err = GetLastError();
      Log_Level(HZN_LOG_INFO, LOGPFX "HznProtect_OpenDriver returns %d", err);
      /*
       * Cannot find the driver.
       * Skip hooks and tell the caller we are OK.
       */
      if (err == ERROR_FILE_NOT_FOUND) {
         goto success;
      }

      /*
       * Something was wrong.
       */
      goto err_out;
   }

   InitializeCriticalSection(&hooks.lockMPH);

   TrapApiDef user32APIsToHook[] = {{"RegisterMessagePumpHook", NULL, RegisterMphHook, {0}},
                                    {"UnregisterMessagePumpHook", NULL, UnregisterMphHook, {0}},
                                    {0}};

   TrapModuleDef hookTable[] = {{"USER32.dll", user32APIsToHook}, {NULL, NULL}};

   BOOL ok;

   ok = TrapAPI_Trap(hookTable);
   if (!ok) {
      ASSERT(FALSE);
      err = ERROR_HOOK_NOT_INSTALLED;
      goto err_close_driver;
   }

   ok = TrapAPI_AreAllTrapsSet(hookTable);
   if (!ok) {
      err = ERROR_HOOK_NOT_INSTALLED;
      goto err_untrap_and_close_driver;
   }

   hooks.realRegisterMPH = (REGISTER_MPH_PROC)TrapAPI_GetRealProcAddress(
      hookTable, "RegisterMessagePumpHook", "USER32.dll");
   hooks.realUnregisterMPH = (UNREGISTER_MPH_PROC)TrapAPI_GetRealProcAddress(
      hookTable, "UnregisterMessagePumpHook", "USER32.dll");

   if (hooks.realRegisterMPH == NULL || hooks.realUnregisterMPH == NULL) {
      err = ERROR_PROC_NOT_FOUND;
      goto err_untrap_and_close_driver;
   }

   hznProtect.hCallWinprocHook =
      SetWindowsHookExW(WH_CALLWNDPROC, CallWinprocCB, NULL, mainThreadId);
   if (hznProtect.hCallWinprocHook == NULL) {
      err = GetLastError();
      goto err_untrap_and_close_driver;
   }

   if (!hooks.realRegisterMPH(InitVmwMPH)) {
      err = ERROR_HOOK_NOT_INSTALLED;
      goto err_unhook_and_untrap_and_close_driver;
   }

   if (!PatchImmApiEntries()) {
      ASSERT(FALSE);
      err = GetLastError();
      goto err_unreg_and_unhook_and_untrap_and_close_driver;
   }

   /*
    * WH_KEYBOARD_LL will be set later in HookKeyboardThreadMain(),
    * in the "keyboard thread" started by HznProtect_Init().
    */
   keyboard.installLowLevelHookLater = TRUE;

success:
   SetLastError(ERROR_SUCCESS);
   return TRUE;

err_unreg_and_unhook_and_untrap_and_close_driver:
   hooks.realUnregisterMPH();

err_unhook_and_untrap_and_close_driver:
   UnhookWindowsHookEx(hznProtect.hCallWinprocHook);
   hznProtect.hCallWinprocHook = NULL;

err_untrap_and_close_driver:
   TrapAPI_Untrap(hookTable);

err_close_driver:
   HznProtect_CloseDriver();

   DeleteCriticalSection(&hooks.lockMPH);
   RtlZeroMemory(&hooks.lockMPH, sizeof hooks.lockMPH);

err_out:
   SetLastError(err);
   return FALSE;
}


/*
 *-----------------------------------------------------------------------------
 *
 * HznProtect_GetStatistics --
 *
 *      Get the statistics information from driver
 *
 * Results:
 *      The string. Caller needs to free it.
 *
 * Side effects:
 *     None.
 *
 *-----------------------------------------------------------------------------
 */

char *
HznProtect_GetStatistics(void)
{
   BOOL ret = FALSE;
   ULONG bytes = 0;
   char *out = NULL;

   if (hznProtect.driverHandle == NULL) {
      return out;
   }

   UINT8 reply[1000] = {0};
   ret = DeviceIoControl(hznProtect.driverHandle, IOCTL_GET_STATISTICS, NULL, 0, &reply,
                         sizeof reply, &bytes, NULL);

   if (!ret) {
      DWORD err = GetLastError();

      Log_Level(HZN_LOG_ERROR, LOGPFX "%s: IOCTL_GET_STATISTICS failed with %d", __func__, err);
      return out;
   }
   // NUL terminated
   if (bytes > 2) {
      out = Util_SafeMalloc(bytes);
      memcpy(out, reply, bytes);
   }
   return out;
}


/*
 *-----------------------------------------------------------------------------
 *
 * statisticsTimerProc --
 *
 *      TIMEPROC which get statistic information from driver and log them.
 *
 * Results:
 *      None
 *
 * Side effects:
 *     None.
 *
 *-----------------------------------------------------------------------------
 */

static void CALLBACK
statisticsTimerProc(HWND hwnd,        // handle to window
                    UINT uMsg,        // WM_TIMER message
                    UINT_PTR idEvent, // timer identifier
                    DWORD dwTime)     // current system time
{
   char *out = HznProtect_GetStatistics();
   if (out != NULL) {
      // use %ls here since the string is WCHAR
      Log_Level(HZN_LOG_INFO,
                LOGPFX
                "Some libraries are blocked, add their paths to "
                "HKLM\\SYSTEM\\CurrentControlSet\\Services\\hznprotect\\Parameters\\AllowedDll "
                "with type REG_MULTI_SZ if they should be trusted:\n%ls",
                out);
      free(out);
   }
}


/*
 *----------------------------------------------------------------------
 *
 * StatisticsThreadMain --
 *
 *      The thread of log statistics.
 *
 * Results:
 *      Always 0.
 *
 * Side effects:
 *      None
 *
 *----------------------------------------------------------------------
 */

static unsigned __stdcall StatisticsThreadMain(void *clientData) // IN
{
   MSG msg;
   BOOL ret;
   UINT_PTR timerID = 0;
   if (hznProtect.driverHandle == NULL) {
      goto out;
   }
   timerID = SetTimer(NULL, 0, 60 * 1000, (TIMERPROC)statisticsTimerProc);

   while ((ret = GetMessageW(&msg, NULL, 0, 0)) != 0) {
      if (ret == -1) {
         DWORD err = GetLastError();

         Log_Level(HZN_LOG_ERROR, LOGPFX "%s: GetMessageW failed with %d", __func__, err);
         goto out;
      }

      TranslateMessage(&msg);
      DispatchMessageW(&msg);
   }
   if (timerID != 0) {
      KillTimer(NULL, timerID);
   }
out:
   _endthreadex(0);
   return 0;
}


/*
 *-----------------------------------------------------------------------------
 *
 * HznProtect_AddAllowedDlls --
 *
 *      Add allowed dll's paths to driver. The input buffer should be WCHAR,
 *      each path is NUL-separated.
 *
 * Results:
 *      The status.
 *
 * Side effects:
 *     None.
 *
 *-----------------------------------------------------------------------------
 */

BOOL
HznProtect_AddAllowedDlls(BYTE *buffer, // IN
                          int length)   // IN
{
   BOOL ret = FALSE;
   ULONG bytes = 0;

   if (hznProtect.driverHandle == NULL) {
      return ret;
   }

   if (length < sizeof(WCHAR) || buffer[length - 1] != 0 || buffer[length - 2] != 0) {
      SetLastError(ERROR_INVALID_PARAMETER);
      return ret;
   }

   ret = DeviceIoControl(hznProtect.driverHandle, IOCTL_ADD_ALLOWEDDLLS, buffer, length, NULL, 0,
                         &bytes, NULL);

   if (!ret) {
      DWORD err = GetLastError();

      Log_Level(HZN_LOG_ERROR, LOGPFX "%s: IOCTL_ADD_ALLOWEDDLLS failed with %d", __func__, err);
   }
   return ret;
}


/*
 *-----------------------------------------------------------------------------
 *
 * HznProtect_IsMyselfProtected --
 *
 *      Get if the calling process is protected.
 *
 * Results:
 *      TRUE or FALSE.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

BOOL
HznProtect_IsMyselfProtected(void)
{
   BOOL ret;
   ULONG bytes = 0;

   if (hznProtect.driverHandle == NULL) {
      Log_Level(HZN_LOG_ERROR, LOGPFX "%s: driver handle not opened", __func__);
      return FALSE;
   }

   IoctlGetMyselfProtectedReply reply = {0};
   ret = DeviceIoControl(hznProtect.driverHandle, IOCTL_GET_MYSELF_PROTECTED, NULL, 0, &reply,
                         sizeof reply, &bytes, NULL);

   if (!ret) {
      DWORD err = GetLastError();

      Log_Level(HZN_LOG_ERROR, LOGPFX "%s: ioctl failed with %d", __func__, err);
      return FALSE;
   } else if (bytes < sizeof reply) {
      Log_Level(HZN_LOG_ERROR, LOGPFX "%s: ioctl reply size too small: %lu", __func__, bytes);
      return FALSE;
   }

   Log_Level(HZN_LOG_VERBOSE, LOGPFX "%s: myself is%s protected", __func__,
             reply.isProtected ? "" : " not");

   return reply.isProtected != 0;
}


/*
 *-----------------------------------------------------------------------------
 *
 * HznProtect_IsSupported --
 *
 *      Get if hznprotect is supported on current platform.
 *      This function must be in sync with the one in the windows installer.
 *
 * Results:
 *      TRUE or FALSE.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

BOOL
HznProtect_IsSupported(void)
{
   // Must be Win10+
   if (hznProtect.osVersionInfo.dwMajorVersion < 10) {
      return FALSE;
   }

   // Must be x64
   if (!HznProtect_IsAMD64()) {
      return FALSE;
   }

   return TRUE;
}


/*
 *-----------------------------------------------------------------------------
 *
 * HznProtect_IsAMD64 --
 *
 *      Get whether it is AMD64 platform or not.
 *
 * Results:
 *      TRUE or FALSE.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

BOOL
HznProtect_IsAMD64(void)
{
   return hznProtect.nativeMachine == IMAGE_FILE_MACHINE_AMD64;
}


/*
 *-----------------------------------------------------------------------------
 *
 * HznProtect_SendInput --
 *
 *       Call IOCTL_PREPARE_INTERNAL_SENDINPUT to get valid ExtraInformation
 *       (in INPUT::KEYBDINPUT::dwExtraInfo) for the INPUTs before calling
 *       SendInput() if hznprotect driver is available, or call SendInput()
 *       directly.
 *
 * Returns:
 *       Same as SendInput().
 *       Returns the number of events that it successfully
 *       inserted into the keyboard or mouse input stream.
 *
 * Side effects:
 *       None.
 *
 *-----------------------------------------------------------------------------
 */

UINT
HznProtect_SendInput(UINT cInputs, LPINPUT pInputs, INT cbSize)
{
   if (cInputs == 0 || cbSize != sizeof(INPUT)) {
      SetLastError(ERROR_INVALID_PARAMETER);
      return 0;
   }

   if (hznProtect.driverHandle != NULL) {
      BOOL ret;
      DWORD bytesReturned = 0;
      DWORD bInputs = cInputs * sizeof(INPUT);

      ret = DeviceIoControl(hznProtect.driverHandle, IOCTL_PREPARE_INTERNAL_SENDINPUT, pInputs,
                            bInputs, pInputs, bInputs, &bytesReturned, NULL);
      if (!ret || bytesReturned != bInputs) {
         return 0;
      }
   } // hznProtect.driverHandle != NULL

   return SendInput(cInputs, pInputs, cbSize);
}


/*
 *-----------------------------------------------------------------------------
 *
 * HznProtect_IsSendInputBlocked --
 *
 *      Get if "block SendInput()" is ON or OFF.
 *
 * Results:
 *      TRUE if "block SendInput()" is ON, or FALSE.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

BOOL
HznProtect_IsSendInputBlocked(void)
{
   return Atomic_Read32(&hznProtect.blockSendInput) ? TRUE : FALSE;
}


#ifdef VMWPROTECT_TEST
/*
 * Refer to "CRT initialization":
 * https://learn.microsoft.com/en-us/cpp/c-runtime-library/crt-initialization
 */
#   pragma section(".CRT$XCU", read)
__declspec(allocate(".CRT$XCU")) void (*myStaticInit)(void) = StaticInit;

#   ifdef _M_X64
#      pragma comment(linker, "/include:myStaticInit")
#   else
#      pragma comment(linker, "/include:_myStaticInit")
#   endif


/*
 *-----------------------------------------------------------------------------
 *
 * HznProtect_InitTester --
 *
 *      Init the g_vmwptester.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *     None.
 *
 *-----------------------------------------------------------------------------
 */

void
HznProtect_InitTester(void *vmwptester) // IN:
{
   g_vmwptester = vmwptester;
}
#endif
