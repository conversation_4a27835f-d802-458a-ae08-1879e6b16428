# ************************************************************************
# Copyright (c) Omnissa, LLC. All rights reserved.
# This product is protected by copyright and intellectual property laws in the
# United States and other countries as well as by international treaties.
# -- Omnissa Restricted
# ************************************************************************

#
# vncConnectionMgrTest.py
#
#       The integration test for VNCConnectionManager.
#       Mailing lists: <EMAIL>

import vmware
import os

Import("env_opts")

vncTestName = "vncConnectionMgrTest"
env = vmware.DefaultEnvironment()
prodhost = vmware.ProductHost()


# rxUnitTestLib.py should be added before real test component scons
rxUnitTestLibNodeName = "rxUnitTestLib"
rxUnitTestLibNode = vmware.LookupNode(rxUnitTestLibNodeName, host=env.Host().Name())

rxTestLibDir = rxUnitTestLibNode[0].dir.abspath
rxTestLibName = rxUnitTestLibNodeName

if prodhost.IsWindows():
    # Linking on Windows requires the .lib extension.
    rxTestLibName += ".lib"

hlslNodeNames = ["vnc-win32-hlsl", "vncConnectionMgrTest-hlsl"]

vncTestSubdirs = [
    "lib/vncConnectionManager/gtest",
    "lib/vncConnectionManager/gtest/blastCodec",
    "lib/vncConnectionManager/gtest/encoder",
    "lib/vncConnectionManager/gtest/keyboardTest",
    "lib/vncConnectionManager/gtest/multithread",
]
kbdHandlerTestSubdir = "lib/vncConnectionManager/gtest/keyboardHandlerTest"

##### Link with googletest #####
#
# Dynamically link to support VMOCK
env.LoadTool("gtest", linkDynamic=True)

# Common
env.LoadTool(
    [
        "amf_sdk",
        "libboost",
        "libjpeg",
        "libogg",
        "libopus",
        "libpng",
        "libspeexdsp",
        "libssl",
        "libx264",
        "libyuv",
        "libz",
        "onevpl",
        "protobuf-c-3",
        "vm-product",
    ]
)

env.LoadTool("nvidia_sdk", version="12.0")

env.Append(
    CPPPATH=[
        "#bora/public",
        "#bora/apps/cedar/include",
        "#bora/lib/public",
        "#bora/lib/blastCodec",
        "#bora/lib/vnc",
        "#bora/lib/vnc/win32",
        "#bora/lib/vncConnectionManager",
        "#bora/lib/vncConnectionManager/encode",
        "#bora/lib/vncConnectionManager/gtest",
        "#bora/lib/vncConnectionManager/gtest/common",
        "#bora/lib/vncConnectionManager/gtest/encode",
        "#bora/lib/vncConnectionManager/gtest/keyboardTest",
        "#bora/lib/vncConnectionManager/gtest/vncHLSLTest",
        "#bora/apps/horizonrxtest/unitTest/public",
        "#bora/apps/horizonrxtest/unitTest/lib",
        vmware.HeaderDirectory("vnc"),
    ],
)

# vncLibs for all platforms
vncLibs = [
    "asyncsocket",
    "blastSockets",
    "blastCodec",
    "blit",
    "cityhash",
    "config",
    "coreDump",
    "crypto",
    "d3des",
    "dict",
    "err",
    "file",
    "hashMap",
    "image",
    "keyLocator",
    "keySafe",
    "keyboard",
    "lfqueue",
    "lock",
    "log",
    "mempool",
    "misc",
    "panic",
    "poll",
    "pollDefault",
    "productState",
    "raster",
    "rbtree",
    "rectangle",
    "region",
    "sig",
    "slab",
    "sound",
    "soundlib",
    "ssl",
    "sslRemap",
    "string",
    "thread",
    "udpProxy",
    "udpfec",
    "unicode",
    "user",
    "uuid",
    "version",
    "vnc",
    "vncConnectionManager",
    "vvclib",
]

env.Append(
    STATICLIBPATH={rxTestLibName: rxTestLibDir},
    STATICLIBS=[rxTestLibName],
    LIBPATH=[rxTestLibDir],
)

# Windows-only
if prodhost.IsWindows():
    nvapiLib = "nvapi64.lib" if vmware.Host().Is64Bit() else "nvapi.lib"
    env.LoadTool("amd_rapidfire_sdk", dllName="RapidFire")
    env.LoadTool(
        [
            "atlmfc",
            "intel_sdk",
            "libxdr",
            "msvcrt",
        ]
    )
    env.LoadTool("jsoncpp", staticRuntime=False)
    env.Append(
        CPPDEFINES={
            "_UNICODE": None,
            "UNICODE": None,
        },
        CPPPATH=[
            "#bora/lib/vncConnectionManager/win32",
            "#bora/lib/vncConnectionManager/win32/capture",
            "#bora/lib/vncConnectionManager/win32/topology",
            "#bora-vmsoft/svga/hznvidd",
            "#bora-vmsoft/svga/vdisplay",
            "#bora-vmsoft/svga/wddm/include",
        ],
        CCFLAGS=[
            "-D__SSE2__ -D__SSE4__",
            "/Ob0",
            "/bigobj",
        ],
        SHLIBFLAGS=[
            "/OPT:NOICF",
        ],
        LINKFLAGS=[
            "/MANIFEST:NO",  # Disable default manifest
        ],
        LIBS=[
            nvapiLib,
            "advapi32.lib",
            "bcrypt.lib",
            "crypt32.lib",
            "d3d9.lib",
            "d3d11.lib",
            "d3dcompiler.lib",
            "ddraw.lib",
            "delayimp.lib",
            "dxgi.lib",
            "dxguid.lib",
            "dxva2.lib",
            "dwmapi.lib",
            "gdi32.lib",
            "glu32.lib",
            "iphlpapi.lib",
            "kernel32.lib",
            "legacy_stdio_definitions.lib",
            "legacy_stdio_wide_specifiers.lib",
            "magnification.lib",
            "msimg32.lib",
            "netapi32.lib",
            "oldnames.lib",
            "ole32.lib",
            "oleaut32.lib",
            "psapi.lib",
            "qwave.lib",
            "setupapi.lib",
            "shell32.lib",
            "user32.lib",
            "uuid.lib",
            "wbemuuid.lib",
            "winmm.lib",
            "ws2_32.lib",
            "Wtsapi32.lib",
            "yuv.lib",
        ],
    )

    for hlslNodeName in hlslNodeNames:
        env.Append(CPPPATH=vmware.HeaderDirectory(hlslNodeName, "win32"))

    vncLibs += [
        "rdsutils",
        "win32auth",
        "win32cfgmgr",
        "win32tsf",
        "wmi",
    ]

    vncTestSubdirs += [
        "lib/vnc/win32",
        "lib/vncConnectionManager/gtest/vncHLSLTest",
        kbdHandlerTestSubdir,
        "lib/vncConnectionManager/gtest/win32",
    ]

# Linux-only
elif prodhost.IsLinux():
    env.LoadTool(
        [
            "cvt",
            "xorg",
        ]
    )
    env.Append(
        CCFLAGS=[
            "-Og",
            "-msse2",
            "-msse4",
            "-Wno-sign-compare",
            "-fPIC",
            "-fno-inline",
        ],
        CPPPATH=[
            "#bora/lib/vncConnectionManager/linux",
        ],
        STATICLIBS=[
            "jpeg",
            "ogg",
            "opus",
            "speexdsp",
            "yuv",
        ],
        LIBS=[
            "dl",
            "m",
            "pthread",
            "stdc++",
            "X11",
            "Xcursor",
            "Xdamage",
            "Xext",
            "Xfixes",
            "Xi",
            "Xinerama",
            "Xrandr",
            "Xtst",
            "z",
        ],
    )

e = vmware.Executable(vncTestName, env=env)
e.addStaticLibs("vmlibs", vncLibs)
e.addGlobalStaticLibs(
    [
        "vncNvEncSDK8",
        "vncNvEncSDK12",
        "vncReplay",
        "vncReplayHardware",
    ]
)

e.addSubdirs(
    [
        "apps/horizonrxtest/unitTest/lib",
        "apps/horizonrxtest/unitTest/public",
    ],
    tree="bora",
)
e.addSubdirs(vncTestSubdirs)

node = e.createProgramNode()

# Add image artifacts used in RegEnc UT
pngHost = "win32" if vmware.Host().IsWindows() else "linux64"
pngNodes = vmware.LookupNode("vnc-png-artifacts", host=pngHost)
env.Depends(node, dependency=pngNodes)
for png in pngNodes:
    vmware.RegisterNode([png], vncTestName)

if vmware.Host().IsWindows():
    for hlslNodeName in hlslNodeNames:
        hlslNodes = vmware.LookupNode(hlslNodeName, host="win32")
        env.Depends(node, dependency=hlslNodes)

vmware.RegisterNode(node, vncTestName)

# Stage redistributables like libpng when building vncConnectionMgrTest.
for n in env.get("REDIST") or []:
    vmware.RegisterNode([File(n)], vncTestName)

# libstdc++ is not a normal, explicit shared lib of vncConnectionMgrTest,
# but we must stage it on linux. On windows the C++ runtime can be installed
# manually as a pre-requisite.
if vmware.Host().IsLinux():
    vmware.RegisterNode([env["LIBSTDCXX_REDIST"]], vncTestName)

if vmware.Host().IsWindows():
    testFiles = "#bora/%s/keyspecs/*.keyspec" % kbdHandlerTestSubdir
    vmware.RegisterNode(Flatten(vmware.GlobSourceDir(testFiles)), vncTestName)

vmware.Alias("%s-build" % vncTestName, node)
