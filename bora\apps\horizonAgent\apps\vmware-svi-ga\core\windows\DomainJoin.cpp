/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

#include "stdafx.h"

#include <DsGetDC.h>
#include <Iphlpapi.h>
#include <Lm.h>
#include <Mstcpip.h>
#include <Ws2tcpip.h>
#include <core/windows/DomainJoin.h>
#include <set>
#include <stdexcept>

#include <common/util/DomainServices.h>
#include <common/util/Util.h>
#include <common/windows/registry/SecureRegistry.h>
#include <common/windows/security/Encryption.h>

#define SECURITY_WIN32
#include <Security.h>

namespace coreutil = svmga::core::util;
namespace commutil = svmga::common::util;

using namespace svmga::common::windows;
using namespace svmga::core::windows;
using namespace svmga::core::util;

DomainJoin::DomainJoin() :
   _GuestInfo(new coreutil::GuestInfo()),
   _DomainServices(new commutil::DomainServices()),
   _Network(Network::GetInstance()),
   _Registry(coreutil::Registry::GetInstance()),
   _System(coreutil::System::GetInstance()),
   _Support(coreutil::Support::GetInstance()),
   _MachineType(coreutil::MachineType::GetInstance()),
   _CustState(coreutil::CustomizationState::GetInstance()),
   _cpi(coreutil::CustomizationPersistentInfo::GetInstance())
{}

bool
DomainJoin::VerifyTrust(int iTries)
{
   HRESULT hr = S_OK;
   bool bResult = false;
   DWORD dwDelay = 0;
   DWORD dwMaxRetries = 0;
   DWORD dwStatus = 0;
   size_t domainNameLen = MAX_PATH;
   NET_API_STATUS status = NERR_Success;
   NETLOGON_INFO_2 *pNetInfo2 = NULL;

   hr =
      _Registry->GetValue(TRUST_VERIFY_RETRIES_VALUE_NAME, iTries, dwMaxRetries, RegType::Service);

   //
   // Assuming IPv4 will be faster on a dual stack setup so not adding
   // delay for now if IPv4 is enabled. This needs to be tested on a
   // dual stack when that feature is supported
   //
   if (_Network->IsV6Only()) {
      hr = _Registry->GetValue(TRUST_VERIFY_DELAY_VALUE_NAME, DEFAULT_TRUSTVERIFY_IPV6_DELAY,
                               dwDelay, RegType::Service);
   } else {
      hr = _Registry->GetValue(TRUST_VERIFY_DELAY_VALUE_NAME, DEFAULT_TRUSTVERIFY_DELAY, dwDelay,
                               RegType::Service);
   }

   TCHAR *szDomainNameW = (TCHAR *)_domainName.c_str();
   for (DWORD i = 0; i < dwMaxRetries; i++) {
      status = I_NetLogonControl2(NULL, NETLOGON_CONTROL_TC_VERIFY, 2, (LPBYTE) & (szDomainNameW),
                                  (LPBYTE *)&pNetInfo2);
      if (status != NERR_Success) {
         SYSMSG_FUNC(Debug, _T("I_NetLogonControl2 Failed (%d): Domain: %ws, 0x%X"), i,
                     szDomainNameW, status);

         Sleep(dwDelay);

         //
         // Release/Renew IP
         //
         _Network->RenewIP();

         //
         // Sleep to give network time to stabilize
         //
         Sleep(dwDelay);

         //
         // Restart netlogon if I_NetLogonControl2 fails
         // This might recover the failure
         //
         DWORD dwStatus = 0;
         hr = _System->ControlSvc(NETLOGON_SERVICE_NAME, NGVC_SERVICE_RESTART, dwStatus,
                                  SERVICE_NO_RETRIES, SERVICE_NO_DELAY);
         if (FAILED(hr)) {
            SYSMSG_FUNC(Debug, _T("Failed Restarting NetLogon Service: 0x%X"), hr);
         } else {
            SYSMSG_FUNC(Debug, _T("Restarted Netlogon Service"));

            //
            // A longer delay may be needed to give netlogon time to fully start
            // and be ready.
            //
            WaitForSingleObject(_System->GetShutdownEvent(), _cpi->GetPostNetlogonStartDelay());
         }

         //
         // A longer delay may be needed to give netlogon time to fully start
         // and be ready.
         //
         WaitForSingleObject(_System->GetShutdownEvent(), _cpi->GetPostNetlogonStartDelay());
      } else {
         //
         // Validate results
         //
         if (pNetInfo2 == NULL) {
            SYSMSG_FUNC(Debug, _T("pNetInfo == NULL"));
            continue;
         }

         SYSMSG_FUNC(Debug, _T("Flags: (0x%X): %ws"), pNetInfo2->netlog2_flags,
                     LogTrustFlags(pNetInfo2->netlog2_flags).c_str());

         SYSMSG_FUNC(Debug, _T("Trusted DC Name: %ws"), pNetInfo2->netlog2_trusted_dc_name);

         SYSMSG_FUNC(Debug, _T("TC Connection Status: 0x%X"),
                     pNetInfo2->netlog2_tc_connection_status);

         if (FlagOn(pNetInfo2->netlog2_flags, NETLOGON_VERIFY_STATUS_RETURNED)) {
            SYSMSG_FUNC(Debug, _T("Trust Verification Status: 0x%X"),
                        pNetInfo2->netlog2_pdc_connection_status);
         } else {
            SYSMSG_FUNC(Debug, _T("Secure Channel To PDC From BDC Status: 0x%X"),
                        pNetInfo2->netlog2_pdc_connection_status);
         }

         if (pNetInfo2->netlog2_pdc_connection_status == NERR_Success &&
             pNetInfo2->netlog2_tc_connection_status == NERR_Success) {
            bResult = true;
            break;
         }

         NetApiBufferFree(pNetInfo2);
         pNetInfo2 = NULL;

         Sleep(dwDelay);
      }
   }

   if (pNetInfo2 != NULL) {
      NetApiBufferFree(pNetInfo2);
      pNetInfo2 = NULL;
   }

   return bResult;
}

bool
DomainJoin::VerifyTrustEx(bool bChangePwd)
{
   bool bResult = false;
   std::wstring preferredServerDc;
   std::wstring preferredAgentDc;

   if (GetHostName().empty() || GetDomainName().empty()) {
      return false;
   }

   //
   // Get string for current customization stage
   //
   std::string strMachineType = _MachineType->GetMachineTypeString();

   //
   // Verify the trust with default DC
   //
   SYSMSG_FUNC(Debug, _T("Attempting trust verification with default DC"));
   bResult = VerifyTrust();
   if (bResult) {
      SYSMSG_FUNC(Debug, _T("%S Trust Verification Succeeded with default DC"),
                  strMachineType.c_str());
      goto Cleanup;
   }

   //
   // Get preferred DC set by server - on which computer acc was updated
   // Establish secure channel with this DC and retry trust verification
   // if this preferred DC is in current site
   //
   preferredServerDc = GetPreferredDC();
   if (preferredServerDc.empty()) {
      SYSMSG_FUNC(Error, _T("Failed to get preferred DC set by server"));
   } else {
      SYSMSG_FUNC(Debug, _T("Attempting trust verification with server preferred DC %ws"),
                  preferredServerDc.c_str());

      bResult = VerifyTrustWithPreferredDc(preferredServerDc);
      if (bResult) {
         SYSMSG_FUNC(Debug, _T("%S Trust Verification succeeded with preferred DC %ws"),
                     strMachineType.c_str(), preferredServerDc.c_str());
         goto Cleanup;
      }
   }

   //
   // Get preferred DC set by agent on IT - on which domain join was performed
   // Establish secure channel with this DC and retry trust verification
   // if this DC is in current site and different from server preferred dc
   // TODO: This approach might not be needed since we are using preferred
   // DC sent by server. For now keeping a single try with this DC.
   //
   preferredAgentDc = RetrieveRegDomainJoinDCName();
   if (preferredAgentDc.empty()) {
      SYSMSG_FUNC(Error, _T("Failed to get preferred DC set by agent"));
   } else if (_wcsicmp(preferredAgentDc.c_str(), preferredServerDc.c_str()) != 0) {
      SYSMSG_FUNC(Debug, _T("Attempting trust verification with agent preferred DC %ws"),
                  preferredAgentDc.c_str());

      bResult = VerifyTrustWithPreferredDc(preferredAgentDc, 1);
      if (bResult) {
         SYSMSG_FUNC(Debug, _T("%S Trust Verification succeeded with preferred DC %ws"),
                     strMachineType.c_str(), preferredAgentDc.c_str());
         goto Cleanup;
      }
   }

   //
   // Update the machine password to system generated password and retry
   //
   if (bChangePwd) {
      bResult = ChangeMachinePassword(true);
      if (bResult) {
         bResult = VerifyTrust();
         if (bResult) {
            SYSMSG_FUNC(Debug, _T("%S Trust Verification Succeeded With New Password"),
                        strMachineType.c_str());
            goto Cleanup;
         } else {
            SYSMSG_FUNC(Error, _T("%S Trust Verification Failed With New Password"),
                        strMachineType.c_str());
         }
      } else {
         SYSMSG_FUNC(Error, _T("%S Failed Changing Machine Password"), strMachineType);
      }
   }


Cleanup:
   return bResult;
}

bool
DomainJoin::VerifyTrustWithPreferredDc(std::wstring dcName, int iTries)
{
   // Check if preferred DC is in current site
   if (!_DomainServices->IsDcInCurrentSite(dcName)) {
      SYSMSG_FUNC(Error,
                  _T("DC %ws not found in current site.")
                  _T("Skipping trust verify with preferred dc."),
                  dcName.c_str());
      return false;
   }

   // Establish secure channel with DC
   if (!_DomainServices->SetPreferredDc(GetHostName(), GetDomainName(), dcName)) {
      SYSMSG_FUNC(Error, _T("Failed to set preferred DC: %ws."), dcName.c_str());
      return false;
   }

   // Verify Trust
   if (!VerifyTrust(iTries)) {
      SYSMSG_FUNC(Error, _T("Trust Verification with preferred DC %ws failed"), dcName.c_str());
      return false;
   }

   return true;
}

std::string
DomainJoin::GetErrorDiagnostics(std::string strErrorType)
{
   std::string output;
   output.reserve(256);

   if (!strErrorType.compare(NGVC_CATEGORY_TRUST_VERIFY)) {
      std::wstring trustError = DiagnoseTrustVerifyErrors();
      output.assign(_CustState->GetDescription(SVM_PS_TRUST_VERIFICATION_FAILED));
      output.append(". ");
      output.append(trustError.begin(), trustError.end());
   } else {
      SYSMSG_FUNC(Debug, _T("Could not diagnose error for %S"), strErrorType.c_str());
   }

   return output;
}


/**
 * Generates one of the following log:
 * 1. Clone Trust Verification Failed. Failed to get DCs in current site <Site
 *    Name>. Server preferred DC was <DC Name>.
 * 2. Clone Trust Verification Failed. Server preferred DC <DC Name> not found
 *    in current site. DCs (DC1, DC2) found in current site <Site Name>
 * 3. Clone Trust Verification Failed. Server preferred DC <DC Name> found in
 *    current site. DCs (DC1, DC2) found in current site <Site Name>
 */

std::wstring
DomainJoin::DiagnoseTrustVerifyErrors()
{
   std::wstring error;
   std::wstring siteName;
   std::wstring srvPreferredDc;
   error.reserve(256);

   // Get server preferred DC
   srvPreferredDc = GetPreferredDC();

   // Get site name and list of DCs in current site
   _DomainServices->ClearCache();
   siteName = _DomainServices->GetCurrentSite();

   std::set<std::wstring> dcSet = _DomainServices->GetDCsInCurrentSite(_domainName);
   if (dcSet.empty()) {
      error.assign(L"Failed to get DCs in current site ");
      error.append(siteName);
      error.append(L". Server preferred DC was ");
      error.append(srvPreferredDc);
      error.append(L".");
      return error;
   }

   if (!_DomainServices->IsDcInCurrentSite(srvPreferredDc)) {
      error.assign(L"Server preferred DC ");
      error.append(srvPreferredDc);
      error.append(L" not found in current site. ");
   } else {
      error.assign(L"Server preferred DC ");
      error.append(srvPreferredDc);
      error.append(L" found in current site. ");
   }

   // Log name of all DCs in current site
   error.append(L"DCs (");
   for each (std::wstring dc in dcSet) {
      // Extract DC Host name and append to error log
      error.append(dc.substr(dc.find_first_of(L'\\') + 1));
      error.append(L", ");
   }
   error.pop_back(); // Remove space
   error.pop_back(); // Remove comma
   error.append(L") found in current site (");
   error.append(siteName);
   error.append(L").");

   return error;
}

std::wstring
DomainJoin::LogTrustFlags(DWORD dwFlags)
{
   std::wstring strFlags;

   if (FlagOn(dwFlags, NETLOGON_REPLICATION_NEEDED)) {
      if (!strFlags.empty()) {
         strFlags.append(_T(" | "));
      }
      strFlags.append(_T("NETLOGON_REPLICATION_NEEDED"));
   }

   if (FlagOn(dwFlags, NETLOGON_REPLICATION_IN_PROGRESS)) {
      if (!strFlags.empty()) {
         strFlags.append(_T(" | "));
      }
      strFlags.append(_T("NETLOGON_REPLICATION_IN_PROGRESS"));
   }

   if (FlagOn(dwFlags, NETLOGON_FULL_SYNC_REPLICATION)) {
      if (!strFlags.empty()) {
         strFlags.append(_T(" | "));
      }
      strFlags.append(_T("NETLOGON_FULL_SYNC_REPLICATION"));
   }

   if (FlagOn(dwFlags, NETLOGON_REDO_NEEDED)) {
      if (!strFlags.empty()) {
         strFlags.append(_T(" | "));
      }
      strFlags.append(_T("NETLOGON_REDO_NEEDED"));
   }

   if (FlagOn(dwFlags, NETLOGON_HAS_IP)) {
      if (!strFlags.empty()) {
         strFlags.append(_T(" | "));
      }
      strFlags.append(_T("NETLOGON_HAS_IP"));
   }

   if (FlagOn(dwFlags, NETLOGON_HAS_TIMESERV)) {
      if (!strFlags.empty()) {
         strFlags.append(_T(" | "));
      }
      strFlags.append(_T("NETLOGON_HAS_TIMESERV"));
   }

   if (FlagOn(dwFlags, NETLOGON_DNS_UPDATE_FAILURE)) {
      if (!strFlags.empty()) {
         strFlags.append(_T(" | "));
      }
      strFlags.append(_T("NETLOGON_DNS_UPDATE_FAILURE"));
   }

   if (FlagOn(dwFlags, NETLOGON_VERIFY_STATUS_RETURNED)) {
      if (!strFlags.empty()) {
         strFlags.append(_T(" | "));
      }
      strFlags.append(_T("NETLOGON_VERIFY_STATUS_RETURNED"));
   }

   return strFlags;
}

/*!
 * @see DomainJoin::JoinMachineToDomain.
 */
HRESULT
DomainJoin::JoinMachineToDomain(bool bClearPwd)
{
   HRESULT hr = S_OK;
   DWORD rc = NERR_Success;
   bool bHavePreferredDC = false;
   size_t machinePwdLen = 0;
   WCHAR *szMachinePwdW = NULL;
   std::wstring strPreferredDC;
   std::wstring curDCName;

   if (!GetUnobfuscatedMachinePwd((void **)&szMachinePwdW, machinePwdLen, true, false, bClearPwd)) {
      SYSMSG_FUNC(Debug, _T("Failed getting machine password"));
      goto Cleanup;
   }

   //
   // Get Preferred DC
   //
   strPreferredDC = GetPreferredDC();
   if (strPreferredDC.empty()) {
      SYSMSG_FUNC(Debug, _T("Attempting to join %ws to domain %ws using Default DC"),
                  _hostName.c_str(), _domainName.c_str());
      strPreferredDC.assign(_domainName);
   } else {
      SYSMSG_FUNC(Debug,
                  _T("Attempting to join %ws to domain %ws using ")
                  _T("Preferred DC: %ws"),
                  _hostName.c_str(), _domainName.c_str(), strPreferredDC.c_str());
      bHavePreferredDC = true;
   }

   //
   // Join Machine to Domain
   //
   rc = JoinMachineToDomainEx(strPreferredDC, szMachinePwdW);
   if (rc != NERR_Success) {
      //
      // Join failed. First try was with preferred DC, retry with default DC
      //
      if (bHavePreferredDC == true) {
         SYSMSG_FUNC(Debug, _T("Domain Join Failed With Preferred DC, ")
                            _T("Retrying With Default DC"));

         rc = JoinMachineToDomainEx(_domainName.c_str(), (LPCWSTR)szMachinePwdW);
         if (rc != NERR_Success) {
            SYSMSG_FUNC(Debug, _T("Domain Join Failed With Default DC"));
            hr = HRESULT_FROM_WIN32(rc);
            goto Cleanup;
         }
      } else {
         //
         // No preferred DC, First try was with default DC
         //
         SYSMSG_FUNC(Debug, _T("Domain Join Failed With Default DC, No Preferred DC"));
         hr = HRESULT_FROM_WIN32(rc);
         goto Cleanup;
      }
   }

   //
   // Domain Join Succeeded, save current DC Name
   //
   curDCName = _DomainServices->GetCurrentDC(_domainName);
   SYSMSG_FUNC(Debug, _T("Domain Join Succeeded With %s DC: %ws"),
               bHavePreferredDC ? L"Preferred" : L"Default", curDCName.c_str());

   SaveRegDomainJoinDCName(curDCName);
   hr = S_OK;

Cleanup:

   secureFree((void **)&szMachinePwdW, machinePwdLen);

   return hr;
}

DWORD
DomainJoin::JoinMachineToDomainEx(std::wstring strDomain, LPCWSTR szMachinePwd)
{
   HRESULT hr = S_OK;
   int iReboots = 0;
   DWORD i = 0;
   DWORD dwMaxRetries = 0;
   bool bIpRenewAttempted = false;
   NET_API_STATUS rc = NERR_Success;

   hr = _Registry->GetValue(DOMAIN_JOIN_RETRIES_VALUE_NAME, MAX_DOMAIN_JOIN_RETRIES, dwMaxRetries,
                            RegType::Service);

   for (i = 0; i < dwMaxRetries; i++) {
      //
      // Join to the domain
      //
      rc = ::NetJoinDomain(NULL, (LPCWSTR)strDomain.c_str(), NULL, NULL, (LPCWSTR)szMachinePwd,
                           NETSETUP_JOIN_DOMAIN | NETSETUP_DOMAIN_JOIN_IF_JOINED |
                              NETSETUP_JOIN_UNSECURE | NETSETUP_JOIN_WITH_NEW_NAME |
                              NETSETUP_MACHINE_PWD_PASSED);
      if (rc != NERR_Success) {
         hr = HRESULT_FROM_WIN32(rc);
         SYSMSG_FUNC(Debug, _T("Domain Join Failed. rc: 0x%X, hr: 0x%X"), rc, hr);

         // For multi-nic setup, IP polling could return before IP on all
         // networks are available. Renew IP synchronously to ensure all
         // adapters have valid IP address available.
         if ((rc == ERROR_BAD_NETPATH || rc == ERROR_NO_SUCH_DOMAIN) && !bIpRenewAttempted &&
             !_cpi->IsIpRenewed()) {
            SYSMSG_FUNC(Debug,
                        _T("Renewing IP Address to recover from error ")
                        _T("%d."),
                        rc);
            bIpRenewAttempted = true;
            if (_Network->RenewIP()) {
               _cpi->MarkIpRenewed();
               continue;
            }
         }

         //
         // This is a work-around for the network adapater issues
         //
         if (rc == ERROR_NO_SUCH_DOMAIN) {
            iReboots = _cpi->GetDomainJoinRebootCount();

            if (iReboots < 2) {
               SYSMSG_FUNC(Debug, _T("Rebooting to recover from error 1355: ")
                                  _T("ERROR_NO_SUCH_DOMAIN"));
               _cpi->SetDomainJoinRebootCount(++iReboots);
               _System->Reboot(INFINITE);
               break;
            } else {
               SYSMSG_FUNC(Debug,
                           _T("Max domain join reboots attempted. ")
                           _T("rc: 0x%X, hr: 0x%X"),
                           rc, hr);
               //
               // Delay between domain join attempts
               //
               Sleep(2000);
            }
         } else {
            //
            // Delay between domain join attempts
            //
            Sleep(1000);
         }
      } else {
         //
         // Clear the password in guestinfo on success, unless its secured
         // in registry
         //
         if (!_cpi->IsMachinePwdInfoSecured()) {
            ClearMachinePwdGuestInfoValues();
         }

         SYSMSG_FUNC(Debug, _T("Domain Join successful"));

         if (_MachineType->IsInternalTemplate()) {
            _cpi->SetITDomainJoinFinished();
         }

         break;
      }
   }

   if (i >= dwMaxRetries) {
      if (_MachineType->IsInternalTemplate()) {
         _cpi->SetITDomainJoinFinished();
      }

      SYSMSG_FUNC(Debug,
                  _T("Max Domain Join Retries Attempted.  Giving Up: rc: ")
                  _T("0x%X, HRESULT: 0x%X"),
                  rc, hr);
   }

   return rc;
}

HRESULT
DomainJoin::RenameMachine()
{
   HRESULT hr = S_OK;
   bool bResult = false;
   std::string strMachineNameA;

   //
   // Get Machine Name
   //
   if (!_GuestInfo->GetValue(HOSTNAME_VALUE_NAME, strMachineNameA)) {
      SYSMSG_FUNC(Debug, _T("Failed getting hostname."));
      hr = E_INVALIDARG;
      goto Cleanup;
   } else {
      //
      // Make sure the hostname is still valid
      //
      if (!ValidateHostname(strMachineNameA.c_str())) {
         SYSMSG_FUNC(Error, _T("Hostname %S is invalid"), strMachineNameA.c_str());
         hr = E_INVALIDARG;
         goto Cleanup;
      } else {
         SYSMSG_FUNC(Debug, _T("Hostname %S is valid"), strMachineNameA.c_str());
      }
   }

   bResult = SetComputerNameExA(ComputerNamePhysicalDnsHostname, strMachineNameA.c_str());
   if (bResult == false) {
      hr = HRESULT_FROM_WIN32(GetLastError());
      SYSMSG_FUNC(Error, _T("SetComputerNameEx Failed : 0x%X"), hr);
      goto Cleanup;
   }

   _cpi->MarkCloneRenamed();

Cleanup:

   return hr;
}

std::wstring
DomainJoin::GetHostName()
{
   size_t converted = 0;
   size_t nameLen = 0;
   std::string strHostNameA;
   WCHAR szHostNameW[512] = {_T('\0')};

   if (!_hostName.empty()) {
      return _hostName;
   }

   if (_GuestInfo->GetValue(HOSTNAME_VALUE_NAME, strHostNameA)) {
      mbstowcs_s(&converted, szHostNameW, _countof(szHostNameW), strHostNameA.c_str(), _TRUNCATE);
   } else {
      SYSMSG_FUNC(Debug, _T("Failed getting host name."));
   }

   _hostName.assign(szHostNameW);
   return _hostName;
}

std::wstring
DomainJoin::GetDomainName()
{
   size_t converted = 0;
   std::string strDomainNameA;
   WCHAR szDomainNameW[512] = {_T('\0')};

   if (!_domainName.empty()) {
      return _domainName;
   }

   if (_GuestInfo->GetValue(FQDN_VALUE_NAME, strDomainNameA)) {
      mbstowcs_s(&converted, szDomainNameW, _countof(szDomainNameW), strDomainNameA.c_str(),
                 _TRUNCATE);
   } else {
      SYSMSG_FUNC(Debug, _T("Failed getting domain name."));
   }

   _domainName.assign(szDomainNameW);
   return _domainName;
}

bool
DomainJoin::SaveRegDomainJoinDCName(std::wstring dcName)
{
   HRESULT hr = S_OK;
   bool bResult = true;
   std::wstring formattedDcName;

   //
   // Save DC Name in format: hic.local\ad1.hic.local
   //
   formattedDcName.assign(GetDomainName());
   formattedDcName.push_back('\\');
   formattedDcName.append(dcName);

   hr = _Registry->SetValue(CURRENT_DC_VALUE_NAME, dcName, RegType::Service);
   if (FAILED(hr)) {
      SYSMSG_FUNC(Debug, _T("Failed Saving DC Name: %ws, 0x%X"), dcName.c_str(), hr);
      bResult = false;
   }

   return bResult;
}

std::wstring
DomainJoin::RetrieveRegDomainJoinDCName()
{
   HRESULT hr = S_OK;
   std::wstring dcName;

   hr = _Registry->GetValue(CURRENT_DC_VALUE_NAME, dcName, RegType::Service);
   if (FAILED(hr)) {
      SYSMSG_FUNC(Debug, _T("Failed Getting DC Name: 0x%X"), hr);
   }

   return dcName;
}

std::wstring
DomainJoin::GetPreferredSite()
{
   size_t converted = 0;
   std::string strPreferredSiteA;
   WCHAR szPreferredSiteW[256] = {_T('\0')};
   std::wstring strRet;

   //
   // Get Preferred site
   //
   if (_GuestInfo->GetValue(PREFERRED_SITE_VALUE_NAME, strPreferredSiteA)) {
      mbstowcs_s(&converted, szPreferredSiteW, _countof(szPreferredSiteW),
                 strPreferredSiteA.c_str(), _TRUNCATE);
      strRet.assign(GetDomainName());
      strRet.append(_T("\\"));
      strRet.append(szPreferredSiteW);
   } else {
      SYSMSG_FUNC(Debug, _T("Failed getting preferred site"));
   }

   SecureZeroMemory(szPreferredSiteW, sizeof(szPreferredSiteW));

   return strRet;
}

std::wstring
DomainJoin::GetPreferredDC()
{
   size_t converted = 0;
   std::string strPreferredDCA;
   WCHAR szPreferredDCW[256] = {_T('\0')};
   std::wstring strRet;

   //
   // Get Preferred DC
   //
   if (_GuestInfo->GetValue(PREFERRED_DC_VALUE_NAME, strPreferredDCA)) {
      mbstowcs_s(&converted, szPreferredDCW, _countof(szPreferredDCW), strPreferredDCA.c_str(),
                 _TRUNCATE);
      strRet.assign(GetDomainName());
      strRet.append(_T("\\"));
      strRet.append(szPreferredDCW);
   } else {
      SYSMSG_FUNC(Debug, _T("Failed getting preferred DC"));
   }

   SecureZeroMemory(szPreferredDCW, sizeof(szPreferredDCW));

   return strRet;
}

bool
DomainJoin::SetPreferredSite()
{
   std::wstring curSiteName = _DomainServices->GetCurrentSite();
   char szSiteName[128] = {'\0'}; // Maximum length of a DNS name is 63 bytes per label

   errno_t eRet =
      wcstombs_s(NULL, szSiteName, sizeof(szSiteName), curSiteName.c_str(), curSiteName.length());
   if (0 != eRet) {
      SYSMSG_FUNC(Warn,
                  _T("Failed to convert siteName string (%ws). Error: 0x%X. ")
                  _T("Failed setting guestinfo.%s"),
                  curSiteName.c_str(), eRet, PREFERRED_SITE_VALUE_NAME);
      return false;
   }

   if (!_GuestInfo->SetValue(PREFERRED_SITE_VALUE_NAME, szSiteName)) {
      SYSMSG_FUNC(Warn, _T("Failed setting guestinfo.%s"), PREFERRED_SITE_VALUE_NAME);
      return false;
   }

   SYSMSG_FUNC(Debug, _T("Set guest-info preferred site name to %ws"), curSiteName.c_str());

   return true;
}

bool
DomainJoin::SetPreferredDC()
{
   char szDcFqdn[256] = {'\0'}; // Maximum length of domain name FQDN is 255 bytes

   // Get domain name to query current DC
   GetDomainName();
   std::wstring curDCName = _DomainServices->GetCurrentDC(_domainName);

   errno_t eRet =
      wcstombs_s(NULL, szDcFqdn, sizeof(szDcFqdn), curDCName.c_str(), curDCName.length());
   if (0 != eRet) {
      SYSMSG_FUNC(Warn,
                  _T("Failed to convert DC Name string (%ws). Error: 0x%X. ")
                  _T("Failed setting guestinfo.%s"),
                  curDCName.c_str(), eRet, PREFERRED_DC_VALUE_NAME);
      return false;
   }

   if (!_GuestInfo->SetValue(PREFERRED_DC_VALUE_NAME, szDcFqdn)) {
      SYSMSG_FUNC(Debug, _T("Failed setting guestinfo.%s"), PREFERRED_DC_VALUE_NAME);
      return false;
   }

   SYSMSG_FUNC(Debug, _T("Set guest-info preferred DC name: %ws"), curDCName.c_str());

   return true;
}

bool
DomainJoin::JoinCloneToDomain(bool bClearPwd)
{
   bool bResult = false;
   size_t machinePwdLen = 0;
   char *szMachinePwd = NULL;
   SecureRegistry sr;

   if (!GetUnobfuscatedMachinePwd((void **)&szMachinePwd, machinePwdLen, false, false, bClearPwd)) {
      SYSMSG_FUNC(Debug, _T("Failed getting machine password"));
      goto Cleanup;
   }

   //
   // Set machine password
   //
   if (!sr.StorePrivateString(MACHINEACC_KEYNAME, szMachinePwd)) {
      SYSMSG_FUNC(Debug, _T("Failed setting machine password"));
      goto Cleanup;
   }

   bResult = true;

Cleanup:
   secureFree((void **)&szMachinePwd, machinePwdLen);

   return bResult;
}

bool
DomainJoin::JoinCloneToDomainWithNetJoinDomain()
{
   HRESULT hr = S_OK;
   bool bResult = false;
   windev::RenewIp renewIp;

   _Support->SetDomainJoinStartTime();

   _cpi->IncrementNetJoinDomainAttempts();

   hr = JoinMachineToDomain(false);
   if (SUCCEEDED(hr)) {
      _cpi->MarkCloneDomainJoined();
      _cpi->MarkCloneDomainJoinedWithNetJoinDomain();

      //
      // Have observed the machine shutting down and causing
      // netlogon to fail to complete domain join/setting SPNs.
      // Adding a delay here to give netlogon time to complete.
      //
      DWORD dwDelay = 0;
      hr = _Registry->GetValue(DOMAIN_JOIN_REBOOT_DELAY_VALUE_NAME,
                               DEFAULT_DOMAIN_JOIN_REBOOT_DELAY, dwDelay, RegType::Service);
      Sleep(dwDelay);

      _cpi->MarkCloneRebooted();

      SYSMSG_FUNC(Debug, _T("Clone: Domain Join Completed: Domain Join Time: %dms"),
                  _Support->GetTotalDomainJoinTime());
      SYSMSG_FUNC(Debug, _T("Clone: Marked Clone Rebooted.  About To Reboot"));

      _System->Reboot(INFINITE);
      bResult = true;
      goto Cleanup;
   } else {
      SYSMSG_FUNC(Debug,
                  _T("Clone: Domain Join With NetJoinDomain Failed: 0x%X, ")
                  _T("Domain Join Time: %dms"),
                  hr, _Support->GetTotalDomainJoinTime());

      std::string error =
         renewIp.getErrorDiagnostics(_CustState->GetDescription(SVM_PS_FAILED_TO_JOIN_DOMAIN));

      SYSMSG_FUNC(Error, _T("CLONE: %S"), error.c_str());

      //
      // Reboot after each NetJoinDomain failure.  If this is our last attempt
      // with NetJoinDomain, we'll skip the reboot so the error can be handled
      // in the caller.
      //
      if (_cpi->IsNetJoinDomainEnabled()) {
         SYSMSG_FUNC(Debug, L"Initiating NetJoinDomain Failed Reboot");
         _System->Reboot(INFINITE);
      } else {
         SYSMSG_FUNC(Debug, L"Max NetJoinDomain Attempts Reached Without "
                            L"Success, Skipping Reboot");
      }
   }

Cleanup:

   return bResult;
}

HRESULT
DomainJoin::JoinCloneToDomainSysprep()
{
   HRESULT hr = S_OK;
   bool bResult = false;
   std::string strCloneUuid;

   //
   // Log the UUID of the Clone
   //
   if (!_GuestInfo->GetValue(GUESTINFO_CLONE_UUID, strCloneUuid)) {
      SYSMSG_FUNC(Debug, _T("Failed Getting Clone UUID"));
      goto Cleanup;
   }

   SYSMSG_FUNC(Debug, _T("Clone UUID: %S"), strCloneUuid.c_str());

   //
   // Fetch domain and host name
   //
   GetHostName();
   GetDomainName();

   //
   // Join to domain
   //
   hr = JoinMachineToDomain();
   if (FAILED(hr)) {
      SYSMSG_FUNC(Error, _T("Domain Join Failed: 0x%X"), hr);
      goto Cleanup;
   }

   _cpi->MarkCloneDomainJoined();

   SYSMSG_FUNC(Debug, _T("Machine has been renamed and domain joined. ")
                      _T("Needs to shutdown."));

   _cpi->MarkCloneShutdownNeeded();
   SYSMSG_FUNC(Debug, _T("Marked Clone Shutdown Needed."));
   SYSMSG_FUNC(Debug, _T("Total Clone Domain Join Time: %dms"), _Support->GetTotalDomainJoinTime());

Cleanup:

   return hr;
}


/*
 * Create base64 encoded checksum string using SHA-256 hash of input data.
 * Use secureFree to free memory allocated to returned checksum.
 */

char *
DomainJoin::CalculateChecksum(VOID *szData, DWORD dwSizeInBytes)
{
   byte md_value[HASH_SIZE] = {'\0'};
   unsigned int md_len = 0;
   char *szEncodedHash = NULL;
   size_t dataLength = 0;

   //
   // Get SHA-256 hash of given value
   //
   md_len = Encryption::computeHash((char *)szData, dwSizeInBytes, md_value);

   //
   // Base64 encode the hash
   //
   if (!Encryption::encode(md_value, md_len, &szEncodedHash, &dataLength)) {
      SYSMSG_FUNC(Debug, _T("Failed Encoding Checksum"));
   }

   SecureZeroMemory(md_value, md_len);
   md_len = 0;

   return szEncodedHash;
}

void
DomainJoin::ClearMachinePassword()
{
   if (!_GuestInfo->SetValue(MACHINE_PWD_VALUE_NAME, "0")) {
      SYSMSG_FUNC(Debug, _T("Failed setting guestinfo.machinePasswd."));
   } else {
      SYSMSG_FUNC(Debug, _T("Cleared guestinfo.machinePasswd"));
   }
}

void
DomainJoin::ClearMachinePasswordChecksum()
{
   if (!_GuestInfo->SetValue(MACHINE_PWD_CHECKSUM_VALUE_NAME, "0")) {
      SYSMSG_FUNC(Debug, _T("Failed setting guestinfo.machinePasswdChecksum."));
   } else {
      SYSMSG_FUNC(Debug, _T("Cleared guestinfo.machinePasswdChecksum"));
   }
}

bool
DomainJoin::RequestMachinePasswordChange()
{
   bool bResult = false;
   std::wstring domainName;
   std::wstring SvmPwdResetScriptCommand;


   //
   // Prepare command string
   //
   SvmPwdResetScriptCommand.assign(_T("nltest.exe /sc_change_pwd:"));
   SvmPwdResetScriptCommand.append(_domainName);

   //
   // Execute the command
   //
   if (!commutil::Util::RunScript(SvmPwdResetScriptCommand)) {
      SYSMSG_FUNC(Warn, _T("Failed to execute machine password reset command: %ws"),
                  SvmPwdResetScriptCommand.c_str());
   } else {
      SYSMSG_FUNC(Debug, _T("Succeeded executing machine password reset command"));
      bResult = true;
   }

   return bResult;
}

bool
DomainJoin::ValidateHostname(const char *szHostname)
{
   size_t len = 0;
   size_t index = 0;

   //
   // Check for NULL string
   //
   if (szHostname == NULL) {
      SYSMSG_FUNC(Debug, _T("Hostname Is NULL"));
      return false;
   }

   //
   // Check length.  Max 15 chars.
   //
   len = strlen(szHostname);

   if (len <= 0) {
      SYSMSG_FUNC(Debug, _T("Hostname Too Short. Length: %d"), len);
      return false;
   }

   if (len > 15) {
      SYSMSG_FUNC(Debug, _T("Hostname Too Long. Length: %d"), len);
      return false;
   }

   //
   // Make sure all characters are allowed characters
   //
   index = strspn(szHostname, ALLOWED_HOSTNAME_CHARACTERS);
   if (index != len) {
      SYSMSG_FUNC(Debug, _T("Hostname contains invalid characters"));
      return false;
   }

   //
   // Name must not consist entirely of digits
   //
   index = strspn(szHostname, DIGITS);
   if (index == len) {
      SYSMSG_FUNC(Debug, _T("Hostname may not consist entirely of digits"));
      return false;
   }

   return true;
}

bool
DomainJoin::ValidateMachinePassword(VOID *szMachinePwd, size_t pwdLen, bool *pbSkipped)
{
   bool bResult = false;
   size_t serverChecksumLen = 0;
   char *szCloneChecksum = NULL;
   char *szServerChecksum = NULL;

   if (szMachinePwd == NULL || pwdLen <= 0 || pbSkipped == NULL) {
      SYSMSG_FUNC(Debug, _T("Invalid Arguments"));
      goto Cleanup;
   }

   //
   // Get server checksum
   //
   serverChecksumLen = GetMachinePwdChksum(&szServerChecksum);
   if (0 == serverChecksumLen) {
      SYSMSG_FUNC(Debug, _T("Failed Getting Server Checksum, Skipping ")
                         _T("Password Validation"));
      *pbSkipped = true;
      goto Cleanup;
   } else {
      //
      // Calculate machine password checksum
      //
      szCloneChecksum = CalculateChecksum(szMachinePwd, pwdLen);
      if (szCloneChecksum == NULL) {
         SYSMSG_FUNC(Debug, _T("Failed Computing Clone Password Checksum"));
         goto Cleanup;
      }

      //
      // Compare clone and server checksum
      //
      if (memcmp(szCloneChecksum, szServerChecksum, serverChecksumLen) != 0) {
         SYSMSG_FUNC(Debug, _T("Password Checksums Do Not Match!!"));
         goto Cleanup;
      } else {
         SYSMSG_FUNC(Debug, _T("Password Checksums Match"));
         bResult = true;
      }
   }

Cleanup:

   if (szCloneChecksum != NULL) {
      SecureZeroMemory(szCloneChecksum, strlen(szCloneChecksum));
      HeapFree(GetProcessHeap(), 0, szCloneChecksum);
      szCloneChecksum = NULL;
   }

   if (0 != serverChecksumLen) {
      SecureFreeMachinePwdChksum(&szServerChecksum, serverChecksumLen);
   }

   return bResult;
}

bool
DomainJoin::ChangeMachinePassword(bool bOverride)
{
   HRESULT hr = S_OK;
   bool bSuccess = false;
   bool bAnyFailed = false;
   DWORD dwTotalTime = 0;
   DWORD dwStatus = 0;
   DWORD dwMaxRetries = 0;
   DWORD dwIPv6Delay = 0;

   //
   // bOverride lets us change the password even if password change
   // is disabled
   //

   if (_MachineType->IsInternalTemplate()) {
      if (!bOverride && !_cpi->IsTemplateMachinePwdChangeEnabled()) {
         SYSMSG_FUNC(Debug, _T("IT Machine Password Change Disabled"));
         return true;
      }

      if (_cpi->IsTemplateMachinePasswordChanged()) {
         SYSMSG_FUNC(Debug, _T("IT Machine Password Already Reset"));
         return true;
      }

      SYSMSG_FUNC(Debug, _T("Changing IT Machine Password"));
      _Support->SetMachinePwdChangeStartTime();
   } else if (_MachineType->IsClone()) {
      if (!bOverride && !_cpi->IsCloneMachinePwdChangeEnabled()) {
         SYSMSG_FUNC(Debug, _T("Clone Machine Password Change Disabled"));
         return true;
      }

      if (_cpi->IsCloneMachinePasswordChanged()) {
         SYSMSG_FUNC(Debug, _T("Clone Machine Password Already Reset"));
         return true;
      }

      SYSMSG_FUNC(Debug, _T("Changing Clone Machine Password"));
      _Support->SetMachinePwdChangeStartTime();
   }

   //
   // Change Machine Password
   //
   if (!RequestMachinePasswordChange()) {
      bAnyFailed = true;
      SYSMSG_FUNC(Debug, _T("Failed changing machine password"));

      hr = _Registry->GetValue(PWD_CHANGE_RETRIES_VALUE_NAME, MAX_PWDCHANGE_RETRIES, dwMaxRetries,
                               RegType::Service);

      //
      // Assuming IPv4 will be faster on a dual stack setup so not
      // adding delay for now if IPv4 is enabled. This needs to be
      // tested on a dual stack when that feature is supported
      //
      int iIpv6Delay = 0;
      if (_Network->IsV6Only()) {
         hr = _Registry->GetValue(PWD_CHANGE_IPV6_DELAY_VALUE_NAME, MAX_PWDCHANGE_IPV6_DELAY,
                                  dwIPv6Delay, RegType::Service);
      }

      for (DWORD i = 0; i < dwMaxRetries; i++) {
         if (!RequestMachinePasswordChange()) {
            bAnyFailed = true;
            SYSMSG_FUNC(Debug, _T("Failed changing machine password (retry)"));

            //
            // For pure IPv6, it seems like netlogon takes time to
            // establish connection with DC. Trust Verification and MC
            // passwd change returns STATUS_NO_LOGON_SERVERS. So
            // skipping restart for netlogon, and adding delay to give
            // it time to reach DC
            //
            if (_Network->IsV6Only() && i != 0) {
               Sleep(dwIPv6Delay);
               continue;
            }

            //
            // Sleep to give network time to come back online
            //
            Sleep(1000);

            //
            // Release/Renew IP
            //
            _Network->RenewIP();

            //
            // Sleep to give network time to stabilize
            //
            Sleep(1000);

            //
            // There are two attempts to restart netlogon here for the
            // following reasons:
            // 1. The restart in success case was added first as a fix for
            //    an issue where the password change failed on the
            //    first attempt, succeeded on the second, but netlogon
            //    started too early and the domain trust issue
            //    therefore reproduced.
            // 2. In above logic, the first attempt to change the password
            //    can fail and the second can succeed. If this happens,
            //    the restart in success case is the only restart
            //    attempt. Without it, we repro the domain trust
            //    failure.
            // 3. The restart in fialure case only happens if the first AND
            //    second attempt to change the password fails. But this
            //    results in a second restart happening at 747.
            // 4. Logic could be changed to not restart in success case if
            //    a restart happened in failure case, but I don't know
            //    what will happen then. For example, I don't know if
            //    the timing will be right and we won't repro the issue
            //    again.
            //
            //    Thus, leaving it as-is for now and we can revisit
            //    this later. We do need to watch for any issues that
            //    might arise due to netlogon not expecting the
            //    restarts.
            //

            //
            // Restart netlogon if I_NetLogonControl2 fails
            // This should recover the failure
            //
            hr = _System->ControlSvc(NETLOGON_SERVICE_NAME, NGVC_SERVICE_RESTART, dwStatus);
            if (FAILED(hr)) {
               SYSMSG_FUNC(Debug,
                           _T("Failed Restarting NetLogon Service ")
                           _T("(Failure): 0x%X"),
                           hr);
            } else {
               SYSMSG_FUNC(Debug, _T("Restarted Netlogon Service"));

               //
               // A longer delay may be needed to give netlogon time to fully
               // start and be ready.
               //
               WaitForSingleObject(_System->GetShutdownEvent(), _cpi->GetPostNetlogonStartDelay());
            }
         } else {
            bSuccess = true;
            break;
         }
      }

      if (bSuccess == false) {
         SYSMSG_FUNC(Debug, _T("Failed changing machine password ")
                            _T("(after all retries)"));
         SYSMSG_FUNC(Debug, _T("Total Machine Password Change Time: %dms"),
                     _Support->GetTotalMachinePwdChangeTime());
         return false;
      } else {
         //
         // Password change (eventually) succeeded
         //
         if (_MachineType->IsInternalTemplate()) {
            _cpi->MarkTemplateMachinePasswordChanged();
         } else if (_MachineType->IsClone()) {
            _cpi->MarkCloneMachinePasswordChanged();
         }

         //
         // If any attempts failed, let's assume the network wasn't
         // ready and netlogon started too early. So, restart netlogon
         //
         if (bAnyFailed == true) {
            hr = _System->ControlSvc(NETLOGON_SERVICE_NAME, NGVC_SERVICE_RESTART, dwStatus);
            if (FAILED(hr)) {
               SYSMSG_FUNC(Debug,
                           _T("Failed Restarting NetLogon Service ")
                           _T("(Success): 0x%X"),
                           hr);
               return true;
            }

            //
            // A longer delay may be needed to give netlogon time to fully start
            // and be ready.
            //
            WaitForSingleObject(_System->GetShutdownEvent(), _cpi->GetPostNetlogonStartDelay());
         }
      }
   } else {
      //
      // Initial Password change request succeeded
      //
      bSuccess = true;
      SYSMSG_FUNC(Debug, _T("Reset machine password"));

      if (_MachineType->IsInternalTemplate()) {
         _cpi->MarkTemplateMachinePasswordChanged();
      } else if (_MachineType->IsClone()) {
         _cpi->MarkCloneMachinePasswordChanged();
      }
   }

   SYSMSG_FUNC(Debug, _T("Total Machine Password Change Time: %dms"),
               _Support->GetTotalMachinePwdChangeTime());

   return bSuccess;
}

VOID
DomainJoin::LogUPN()
{
   HRESULT hr = S_OK;
   std::wstring strValue;

   hr = _Registry->GetValue(SVM_NGA_UPN_NAME, strValue, RegType::CachedMachines);
   if (FAILED(hr)) {
      SYSMSG_FUNC(Debug, _T("Failed Reading Value: %ws, 0x%X"), SVM_NGA_UPN_NAME, hr);
   } else {
      SYSMSG_FUNC(Debug, _T("UPN: %ws"), strValue.c_str());
   }
}

HRESULT
DomainJoin::UpdateUPN()
{
   HRESULT hr = S_OK;
   DWORD cbName = 0;

   //
   // Get UPN format name (user@domain)
   // A side effect of this call is LSASS updates the UPN in the
   // registry
   //
   cbName = 0;
   if (!GetUserNameEx(NameUserPrincipal, NULL, &cbName)) {
      TCHAR *szUPN = new TCHAR[cbName];
      if (!GetUserNameEx(NameUserPrincipal, szUPN, &cbName)) {
         hr = HRESULT_FROM_WIN32(GetLastError());
         SYSMSG_FUNC(Debug, _T("GetUserNameEx Failed: 0x%X"), hr);
      } else {
         SYSMSG_FUNC(Debug, _T("Updated UPN"));
      }
      delete[] szUPN;
   }

   return hr;
}


/**
 * Read machine password and checksum from guest-info and save it in secure
 * registry. Also clear guest-info values.
 */

bool
DomainJoin::SecureMachinePwdInfo()
{
   bool bRet = false;
   char *szMachinePwd = NULL;
   char *szMachinePwdChkcum = NULL;
   SecureRegistry sr;

   if (_cpi->IsMachinePwdInfoSecured()) {
      SYSMSG_FUNC(Trace, _T("Machine password info is already secured"));
      return true;
   }

   if (_cpi->IsSecureMachinePwdInfoDisabled()) {
      SYSMSG_FUNC(Trace, _T("Secure machine password info disabled"));
      return true;
   }

   //
   // Get password and checksum from guest-info
   //
   size_t pwdLen = 0;
   bRet = _GuestInfo->GetValue(MACHINE_PWD_VALUE_NAME, &szMachinePwd, &pwdLen);
   if (false == bRet || 0 == pwdLen) {
      SYSMSG_FUNC(Warn, _T("Failed getting machine password"));
      goto Cleanup;
   }

   size_t chksumLen = 0;
   bRet = _GuestInfo->GetValue(MACHINE_PWD_CHECKSUM_VALUE_NAME, &szMachinePwdChkcum, &chksumLen);
   if (false == bRet || 0 == chksumLen) {
      SYSMSG_FUNC(Warn, _T("Failed getting machine password checksum"));
      goto Cleanup;
   }

   //
   // Store password and checksum in secure registry
   //
   bRet = sr.StorePrivateString(NGVC_MACHINE_PWD_SECRET_KEY, szMachinePwd);
   if (false == bRet) {
      SYSMSG_FUNC(Warn, _T("Failed securing machine password"));
      goto Cleanup;
   }

   bRet = sr.StorePrivateString(NGVC_MACHINE_PWD_CHKCUM_SECRET_KEY, szMachinePwdChkcum);
   if (false == bRet) {
      SYSMSG_FUNC(Warn, _T("Failed securing machine password checksum"));
      sr.DeleteKey(NGVC_MACHINE_PWD_SECRET_KEY);
      goto Cleanup;
   }

   if (!_cpi->MarkMachinePwdInfoSecured()) {
      SYSMSG_FUNC(Warn, _T("Failed marking machine password info secured"));
      sr.DeleteKey(NGVC_MACHINE_PWD_CHKCUM_SECRET_KEY);
      sr.DeleteKey(NGVC_MACHINE_PWD_SECRET_KEY);
      goto Cleanup;
   }

   //
   // Clear the guestinfo only if machine password and checksum are secured
   //
   ClearMachinePwdGuestInfoValues();

   bRet = true;

Cleanup:

   if (NULL != szMachinePwdChkcum) {
      SecureZeroMemory(szMachinePwdChkcum, chksumLen);
      _GuestInfo->Free(&szMachinePwdChkcum);
      chksumLen = 0;
   }

   if (NULL != szMachinePwd) {
      SecureZeroMemory(szMachinePwd, pwdLen);
      _GuestInfo->Free(&szMachinePwd);
      pwdLen = 0;
   }

   return bRet;
}

void
DomainJoin::ClearMachinePwdGuestInfoValues()
{
   ClearMachinePassword();
   ClearMachinePasswordChecksum();
}

bool
DomainJoin::ClearSecuredMachinePwdInfo()
{
   bool bRet = true;
   SecureRegistry sr;

   if (!_cpi->IsMachinePwdInfoSecured()) {
      return true;
   }

   if (!sr.DeleteKey(NGVC_MACHINE_PWD_SECRET_KEY)) {
      bRet = false;
   }

   if (!sr.DeleteKey(NGVC_MACHINE_PWD_CHKCUM_SECRET_KEY)) {
      bRet = false;
   }

   if (!_cpi->ClearSecuredMachinePwdInfo()) {
      bRet = false;
   }

   if (true == bRet) {
      SYSMSG_FUNC(Trace, _T("Clearing secured machine password info"));
   } else {
      SYSMSG_FUNC(Debug, _T("Errors encountered while clearing secured ")
                         _T("machine password info"));
   }

   return bRet;
}

size_t
DomainJoin::GetMachinePwd(char **pszMachinePwd)
{
   size_t pwdLen = 0;
   char *szPwd = NULL;

   if (NULL == pszMachinePwd) {
      return 0;
   }

   //
   // Try retrieving machine password from secure registry
   // If this fails, check to see if the guest info values still exist,
   // and try retrieving from there.
   //
   if (_cpi->IsMachinePwdInfoSecured()) {
      SecureRegistry sr;
      pwdLen = sr.RetrievePrivateString(NGVC_MACHINE_PWD_SECRET_KEY, &szPwd);
      if (NULL != szPwd && 0 != pwdLen) {
         SYSMSG_FUNC(Trace, _T("Retrieved secured machine password"));
         *pszMachinePwd = szPwd;
         return pwdLen;
      }
      SYSMSG_FUNC(Error, _T("Failed retrieving secured machine password"));
   }

   //
   // Try retrieving machine password from guest info
   //
   bool bRet = _GuestInfo->GetValue(MACHINE_PWD_VALUE_NAME, &szPwd, &pwdLen);
   if (!bRet || 0 == pwdLen) {
      SYSMSG_FUNC(Error, _T("Failed getting machine password"));
      return 0;
   }

   if (pwdLen == 1 && szPwd[0] == '0') {
      SYSMSG_FUNC(Error, _T("Found invalid machine password"));
      _GuestInfo->Free(&szPwd);
      return 0;
   }

   SYSMSG_FUNC(Trace, _T("Got machine password from guest-info"));
   *pszMachinePwd = szPwd;
   return pwdLen;
}

void
DomainJoin::SecureFreeMachinePwd(char **pszMachinePwd, size_t &pwdLen)
{
   if (NULL == pszMachinePwd || NULL == *pszMachinePwd || 0 == pwdLen) {
      return;
   }

   if (_cpi->IsMachinePwdInfoSecured()) {
      SecureRegistry sr;
      sr.FreePrivateString(pszMachinePwd, pwdLen);
   } else {
      SecureZeroMemory(*pszMachinePwd, pwdLen);
      _GuestInfo->Free(pszMachinePwd);
      pwdLen = 0;
   }
}

size_t
DomainJoin::GetMachinePwdChksum(char **pszMachinePwdChksum)
{
   size_t chksumLen = 0;
   char *szPwdChksum = NULL;

   if (NULL == pszMachinePwdChksum) {
      return 0;
   }

   //
   // Try retrieving machine password checksum from secure registry
   // If this fails, check to see if the guest info values still exist,
   // and try retrieving from there.
   //
   if (_cpi->IsMachinePwdInfoSecured()) {
      SecureRegistry sr;
      chksumLen = sr.RetrievePrivateString(NGVC_MACHINE_PWD_CHKCUM_SECRET_KEY, &szPwdChksum);
      if (NULL != szPwdChksum && 0 != chksumLen) {
         SYSMSG_FUNC(Trace, _T("Retrieved secured machine password checksum"));
         *pszMachinePwdChksum = szPwdChksum;
         return chksumLen;
      }
      SYSMSG_FUNC(Error, _T("Failed retrieving secured machine password checksum"));
   }

   //
   // Try retrieving machine password checksum from guest info
   //
   bool bRet = _GuestInfo->GetValue(MACHINE_PWD_CHECKSUM_VALUE_NAME, &szPwdChksum, &chksumLen);
   if (!bRet || 0 == chksumLen) {
      SYSMSG_FUNC(Error, _T("Failed getting machine password checksum"));
      return 0;
   }

   if (chksumLen == 1 && szPwdChksum[0] == '0') {
      SYSMSG_FUNC(Error, _T("Found invalid machine password checksum"));
      _GuestInfo->Free(&szPwdChksum);
      return 0;
   }

   SYSMSG_FUNC(Trace, _T("Got machine password checksum from guest-info"));
   *pszMachinePwdChksum = szPwdChksum;
   return chksumLen;
}

void
DomainJoin::SecureFreeMachinePwdChksum(char **pszMachinePwdChksum, size_t &chksumLen)
{
   if (NULL == pszMachinePwdChksum || NULL == *pszMachinePwdChksum || 0 == chksumLen) {
      return;
   }

   if (_cpi->IsMachinePwdInfoSecured()) {
      SecureRegistry sr;
      sr.FreePrivateString(pszMachinePwdChksum, chksumLen);
   } else {
      SecureZeroMemory(*pszMachinePwdChksum, chksumLen);
      _GuestInfo->Free(pszMachinePwdChksum);
      chksumLen = 0;
   }
}


/*
 * Obfuscate data using host name and clone UUID.
 * Use secureFree to free memory allocated to pszMcPwd.
 */

bool
DomainJoin::ObfuscateHicData(const unsigned char *pData, size_t &dataSize, char **szObfuscated,
                             size_t &obfuscatedLen)
{
   bool bRet = false;
   char *szMachineName = NULL;
   char *szCloneUuid = NULL;

   //
   // Get hostname
   //
   if (!_GuestInfo->GetValue(HOSTNAME_VALUE_NAME, &szMachineName)) {
      SYSMSG_FUNC(Error, _T("Failed getting hostname"));
      goto Cleanup;
   }

   if (!ValidateHostname(szMachineName)) {
      SYSMSG_FUNC(Error, _T("Hostname is invalid"));
      goto Cleanup;
   }

   SYSMSG_FUNC(Debug, _T("Hostname %S is valid"), szMachineName);

   //
   // Get clone UUID
   //
   if (!_GuestInfo->GetValue(GUESTINFO_CLONE_UUID, &szCloneUuid)) {
      SYSMSG_FUNC(Error, _T("Failed Getting Clone UUID"));
      goto Cleanup;
   }

   if (!Encryption::obfuscate(szMachineName, szCloneUuid, pData, dataSize, szObfuscated,
                              &obfuscatedLen)) {
      SYSMSG_FUNC(Error, _T("Failed to obfuscate HIC data"));
      goto Cleanup;
   }

   bRet = true;

Cleanup:
   _GuestInfo->Free(&szCloneUuid);
   _GuestInfo->Free(&szMachineName);

   return bRet;
}


/*
 * Unobfuscate data using host name and clone UUID.
 * Use secureFree to free memory allocated to pszMcPwd.
 */

bool
DomainJoin::UnobfuscatedHicData(const char *pszObfuscatedData, unsigned char **unobfuscatedData,
                                size_t &unobfuscatedSize)
{
   bool bRet = false;
   char *szMachineName = NULL;
   char *szCloneUuid = NULL;

   if (pszObfuscatedData == NULL || unobfuscatedData == NULL) {
      SYSMSG_FUNC(Debug, _T("Invalid Input"));
      return false;
   }

   //
   // Get hostname
   //
   if (!_GuestInfo->GetValue(HOSTNAME_VALUE_NAME, &szMachineName)) {
      SYSMSG_FUNC(Error, _T("Failed getting hostname"));
      goto Cleanup;
   }

   if (!ValidateHostname(szMachineName)) {
      SYSMSG_FUNC(Error, _T("Hostname is invalid"));
      goto Cleanup;
   }

   SYSMSG_FUNC(Debug, _T("Hostname %S is valid"), szMachineName);

   //
   // Get clone UUID
   //
   if (!_GuestInfo->GetValue(GUESTINFO_CLONE_UUID, &szCloneUuid)) {
      SYSMSG_FUNC(Error, _T("Failed Getting Clone UUID"));
      goto Cleanup;
   }

   if (!Encryption::unobfuscate(szMachineName, szCloneUuid, pszObfuscatedData, unobfuscatedData,
                                &unobfuscatedSize)) {
      SYSMSG_FUNC(Debug, _T("Failed unobfuscating HIC data"));
      goto Cleanup;
   }

   bRet = true;

Cleanup:
   _GuestInfo->Free(&szCloneUuid);
   _GuestInfo->Free(&szMachineName);

   return bRet;
}


/*
 * Use secureFree to free memory allocated to pszMcPwd.
 */

bool
DomainJoin::GetUnobfuscatedMachinePwd(void **pszMcPwd, size_t &size, bool bOutWideChar,
                                      bool bRawMcPwd, bool bClearPwd)
{
   bool bRet = false;
   errno_t eRet = 0;
   size_t obfuscatedMachinePwdLen = 0;
   char *szObfuscatedMachinePwd = NULL;
   size_t machinePwdLen = 0;
   byte *bMachinePwd = NULL;
   size_t machinePwdWLen = 0;
   WCHAR *szMachinePwdW = NULL;
   size_t machinePwdWSize = 0;
   void *validationData = NULL;
   size_t validationSize = 0;
   bool bSkipValidation = false;

   // Wide char output for raw machine password not implemented.
   if (pszMcPwd == NULL || (bRawMcPwd && bOutWideChar)) {
      SYSMSG_FUNC(Debug, _T("Invalid input"));
      return false;
   }

   //
   // Get Machine Password and unobfuscate it
   //
   obfuscatedMachinePwdLen = GetMachinePwd(&szObfuscatedMachinePwd);
   if (0 == obfuscatedMachinePwdLen) {
      SYSMSG_FUNC(Error, _T("Failed getting machine password"));
      goto Cleanup;
   }

   if (!UnobfuscatedHicData(szObfuscatedMachinePwd, &bMachinePwd, machinePwdLen)) {
      SYSMSG_FUNC(Debug, _T("Failed unobfuscating machine password"));
      goto Cleanup;
   }

   //
   // Prepare for validation
   // If machine password is not binary, then convert to wide char for checksum
   // validation.
   //
   if (bRawMcPwd) {
      validationData = bMachinePwd;
      validationSize = machinePwdLen;
   } else {
      // Append null char for string type machine password
      // unobfuscate allocates extra space for padding during decryption so its
      // safe to access next char
      bMachinePwd[machinePwdLen] = '\0';

      //
      // Convert to wide char
      //
      machinePwdWSize = (machinePwdLen + 1) * sizeof(WCHAR);
      szMachinePwdW = (WCHAR *)HeapAlloc(GetProcessHeap(), HEAP_ZERO_MEMORY, machinePwdWSize);
      if (szMachinePwdW == NULL) {
         SYSMSG_FUNC(Error, _T("HeapAlloc Failed"));
         return false;
      }

      eRet = mbstowcs_s(&machinePwdWLen, szMachinePwdW, machinePwdWSize, (char *)bMachinePwd,
                        _TRUNCATE);
      if (eRet != 0 || (machinePwdWLen - 1) != machinePwdLen) {
         SYSMSG_FUNC(Warn,
                     _T("Error formatting machine password from size(%d) to ")
                     _T("size(%d). ErrorCode: %d"),
                     machinePwdLen, machinePwdWLen - 1, eRet);
      }
      machinePwdWLen -= 1; // Remove NULL char size added by mbstowcs_s

      validationData = szMachinePwdW;
      validationSize = machinePwdWLen * sizeof(WCHAR);
   }

   //
   // Verify the decrypted password matches what the server intended to
   // send. If not, return an error
   //
   if (!ValidateMachinePassword(validationData, validationSize, &bSkipValidation)) {
      if (bSkipValidation) {
         SYSMSG_FUNC(Warn, _T("Machine Password Validation Was Skipped"));
      } else {
         SYSMSG_FUNC(Error, _T("Machine Password Is Invalid"));
         goto Cleanup;
      }
   } else {
      SYSMSG_FUNC(Debug, _T("Machine Password Is Valid"));
   }

   if (bOutWideChar) {
      *pszMcPwd = szMachinePwdW;
      size = machinePwdWLen;
      Encryption::secureFree((void **)&bMachinePwd, machinePwdLen);
   } else {
      *pszMcPwd = bMachinePwd;
      size = machinePwdLen;
      secureFree((void **)&szMachinePwdW, machinePwdWSize);
   }
   bRet = true;

Cleanup:

   if (!bRet) {
      secureFree((void **)&szMachinePwdW, machinePwdWSize);
      Encryption::secureFree((void **)&bMachinePwd, machinePwdLen);
   }

   validationData = NULL;
   validationSize = 0;
   SecureFreeMachinePwd(&szObfuscatedMachinePwd, obfuscatedMachinePwdLen);

   //
   // Clear the machine password values in guestinfo.  If we might do domain
   // join later with NetJoinDomain, we need to preserve the machine password
   // and checksum in guestinfo until then.
   //
   if (bClearPwd == true) {
      ClearMachinePwdGuestInfoValues();
   }

   return bRet;
}

bool
DomainJoin::BackupMachinePassword()
{
   bool bRet = false;
   size_t machinePwdLen = 0;
   unsigned char *machinePwd = NULL;
   size_t onfuscatedLen = 0;
   char *szObfuscatedMcPwd = NULL;
   char *machinePwdChecksum = NULL;
   SecureRegistry sr;

   //
   // Read current machine password
   //
   machinePwdLen = sr.RetrievePrivateData(MACHINEACC_KEYNAME, (void **)&machinePwd);
   if (machinePwdLen == 0) {
      SYSMSG_FUNC(Error, _T("Failed to read machine password"));
      goto Cleanup;
   }

   //
   // Obfuscate it
   //
   if (!ObfuscateHicData(machinePwd, machinePwdLen, &szObfuscatedMcPwd, onfuscatedLen)) {
      SYSMSG_FUNC(Error, _T("Failed to obfuscate machine password"));
      goto Cleanup;
   }

   //
   // Save obfuscated machine password to guest-info
   //
   if (!_GuestInfo->SetValue(MACHINE_PWD_VALUE_NAME, szObfuscatedMcPwd)) {
      SYSMSG_FUNC(Error, _T("Failed setting guestinfo.machinePasswd"));
      goto Cleanup;
   }

   //
   // Save machine password checksum to guest-info
   //
   machinePwdChecksum = CalculateChecksum(machinePwd, machinePwdLen);
   if (machinePwdChecksum == NULL) {
      SYSMSG_FUNC(Warn, _T("Failed calculating machine password checksum"));
      goto Cleanup;
   }

   if (!_GuestInfo->SetValue(MACHINE_PWD_CHECKSUM_VALUE_NAME, machinePwdChecksum)) {
      SYSMSG_FUNC(Warn, _T("Failed setting guestinfo.machinePasswdChecksum"));
      goto Cleanup;
   }

   SYSMSG_FUNC(Debug, _T("Created backup of machine password in guest info"));

   bRet = true;

Cleanup:
   secureFree((void **)&machinePwdChecksum, machinePwdLen);
   secureFree((void **)&szObfuscatedMcPwd, onfuscatedLen);
   sr.FreePrivateData((void **)&machinePwd, machinePwdLen);

   return bRet;
}

bool
DomainJoin::RestoreMachinePassword()
{
   bool bRet = false;
   void *sysPwd = NULL;
   size_t sysPwdLen = 0;
   char *machinePwd = NULL;
   size_t machinePwdLen = 0;
   SecureRegistry sr;

   // Get machine password from guest info
   if (!GetUnobfuscatedMachinePwd((void **)&machinePwd, machinePwdLen, false, true)) {
      SYSMSG_FUNC(Warn, _T("Failed getting machine password"));
      goto Cleanup;
   }

   // Get current machine password
   sysPwdLen = sr.RetrievePrivateData(MACHINEACC_KEYNAME, &sysPwd);
   if (0 == sysPwdLen) {
      SYSMSG_FUNC(Error, _T("Failed to read machine password"));
      goto Cleanup;
   }

   if (0 == memcmp(sysPwd, machinePwd, sysPwdLen)) {
      SYSMSG_FUNC(Info, _T("Machine passwords match. Skipping restore..."));
      bRet = true;
      goto Cleanup;
   }

   SYSMSG_FUNC(Info, _T("Machine passwords DONT Match. Restoring..."));

   //
   // Restore password in reg
   //
   if (!sr.StorePrivateData(MACHINEACC_KEYNAME, machinePwd, machinePwdLen)) {
      SYSMSG_FUNC(Error, _T("Failed to store private machine data"));
      goto Cleanup;
   }

   SYSMSG_FUNC(Debug, _T("Successfully restored machine password"));

   bRet = true;

Cleanup:
   if (sysPwd != NULL) {
      sr.FreePrivateData(&sysPwd, sysPwdLen);
      sysPwd = NULL;
   }

   secureFree((void **)&machinePwd, machinePwdLen);

   return bRet;
}

void
DomainJoin::secureFree(void **pData, size_t &size)
{
   if (NULL == pData || NULL == *pData || 0 == size) {
      return;
   }

   SecureZeroMemory(*pData, size);
   HeapFree(GetProcessHeap(), 0, *pData);
   *pData = NULL;
   size = 0;
}

void
DomainJoin::printBinary(void *pData, size_t &size)
{
   std::stringstream dataStream;

   for (int i = 0; i < size; i++) {
      dataStream << std::setfill('0') << std::setw(2) << std::hex
                 << (unsigned int)((char *)pData)[i];
      dataStream << " ";
   }

   SYSMSG_FUNC(Debug, _T("Binary Data: [%d]: <%S>"), size, dataStream.str().c_str());
}