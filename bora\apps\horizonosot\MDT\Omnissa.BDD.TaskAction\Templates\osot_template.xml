<?xml version="1.0"?>
<sequence version="3.00" name="Omnissa OSOT Task Sequence" description="A complete task sequence for deploying an optimized operating system">
  <globalVarList>
    <variable name="OSGUID" property="OSGUID">{68bbbd83-4165-453d-a646-dfcb15e2436a}</variable>
    <variable name="DestinationDisk" property="DestinationDisk">0</variable>
    <variable name="DestinationPartition" property="DestinationPartition">1</variable>
    <variable name="DestinationOSVariable" property="DestinationOSVariable">OSDisk</variable>
    <variable name="DestinationOSRefresh" property="DestinationOSRefresh"></variable>
    <variable name="DestinationOSDriveLetter" property="DestinationOSDriveLetter"></variable>
    <variable name="DestinationOSInstallType" property="DestinationOSInstallType">ByVariable</variable>
    <variable name="OSGUID" property="OSGUID">{68bbbd83-4165-453d-a646-dfcb15e2436a}</variable>
  </globalVarList>
  <group name="Initialization" disable="false" continueOnError="false" description="Initialize the TS environment" expand="false">
    <step type="BDD_Gather" name="Gather local only" disable="false" continueOnError="false" successCodeList="0 3010" description="" startIn="">
      <defaultVarList>
        <variable name="GatherLocalOnly" property="GatherLocalOnly">true</variable>
        <variable name="RulesFile" property="RulesFile"></variable>
      </defaultVarList>
      <action>cscript.exe "%SCRIPTROOT%\ZTIGather.wsf"</action>
    </step>
  </group>
  <group name="Validation" disable="false" continueOnError="false" description="Test" expand="false">
    <condition>
      <operator type="or">
        <expression type="SMS_TaskSequence_VariableConditionExpression">
          <variable name="Variable">PHASE</variable>
          <variable name="Operator">equals</variable>
          <variable name="Value">VALIDATION</variable>
        </expression>
      </operator>
    </condition>
    <step type="BDD_Validate" name="Validate" successCodeList="0 3010" description="" startIn="" disable="false" continueOnError="false">
      <defaultVarList>
        <variable name="ImageSize" property="ImageSize">0</variable>
        <variable name="ImageProcessorSpeed" property="ImageProcessorSpeed">800</variable>
        <variable name="ImageMemory" property="ImageMemory">768</variable>
        <variable name="VerifyOS" property="VerifyOS">CLIENT</variable>
      </defaultVarList>
      <action>cscript.exe "%SCRIPTROOT%\ZTIValidate.wsf"</action>
    </step>
    <step name="Check BIOS" disable="false" continueOnError="false" successCodeList="0 3010" description="" startIn="">
      <action>cscript.exe "%SCRIPTROOT%\ZTIBIOSCheck.wsf"</action>
      <defaultVarList>
        <variable name="RunAsUser" property="RunAsUser">false</variable>
        <variable name="SMSTSRunCommandLineUserName" property="SMSTSRunCommandLineUserName"></variable>
        <variable name="SMSTSRunCommandLineUserPassword" property="SMSTSRunCommandLineUserPassword"></variable>
        <variable name="LoadProfile" property="LoadProfile">false</variable>
      </defaultVarList>
    </step>
    <step name="Next Phase" disable="false" continueOnError="false" successCodeList="0 3010" description="" startIn="">
      <action>cscript.exe "%SCRIPTROOT%\ZTINextPhase.wsf"</action>
      <defaultVarList>
        <variable name="RunAsUser" property="RunAsUser">false</variable>
        <variable name="SMSTSRunCommandLineUserName" property="SMSTSRunCommandLineUserName"></variable>
        <variable name="SMSTSRunCommandLineUserPassword" property="SMSTSRunCommandLineUserPassword"></variable>
        <variable name="LoadProfile" property="LoadProfile">false</variable>
      </defaultVarList>
    </step>
  </group>
  <group name="Preinstall" disable="false" continueOnError="false" description="" expand="false">
    <condition>
      <expression type="SMS_TaskSequence_VariableConditionExpression">
        <variable name="Variable">PHASE</variable>
        <variable name="Operator">equals</variable>
        <variable name="Value">PREINSTALL</variable>
      </expression>
    </condition>
    <step type="BDD_Gather" name="Gather local only" disable="false" continueOnError="false" successCodeList="0 3010" description="" startIn="">
      <defaultVarList>
        <variable name="GatherLocalOnly" property="GatherLocalOnly">true</variable>
        <variable name="RulesFile" property="RulesFile"></variable>
      </defaultVarList>
      <action>cscript.exe "%SCRIPTROOT%\ZTIGather.wsf"</action>
    </step>
    <step type="BDD_Validate" name="Validate" disable="false" continueOnError="false" successCodeList="0 3010" description="" startIn="">
      <defaultVarList>
        <variable name="ImageSize" property="ImageSize">0</variable>
        <variable name="ImageProcessorSpeed" property="ImageProcessorSpeed">800</variable>
        <variable name="ImageMemory" property="ImageMemory">768</variable>
        <variable name="VerifyOS" property="VerifyOS">CLIENT</variable>
      </defaultVarList>
      <action>cscript.exe "%SCRIPTROOT%\ZTIValidate.wsf"</action>
    </step>
    <step type="SMS_TaskSequence_PartitionDiskAction" name="Format and Partition Disk (BIOS)" description="" disable="true" continueOnError="false" runIn="WinPEandFullOS" successCodeList="0 3010">
      <condition></condition>
      <defaultVarList>
        <variable name="OSDDiskIndex" property="DiskIndex">0</variable>
        <variable name="OSDDiskPartitions1Type" property="OSDDiskPartitions1Type">Recovery</variable>
        <variable name="DONotCreateExtraPartition" property="DONotCreateExtraPartition"></variable>
        <variable name="OSDPartitions0Type" property="Partitions0Type">Primary</variable>
        <variable name="OSDPartitions0FileSystem" property="Partitions0FileSystem">NTFS</variable>
        <variable name="OSDPartitions0Bootable" property="Partitions0Bootable">True</variable>
        <variable name="OSDPartitions0QuickFormat" property="Partitions0QuickFormat">True</variable>
        <variable name="OSDPartitions0VolumeName" property="Partitions0VolumeName">System Reserved</variable>
        <variable name="OSDPartitions0Size" property="Partitions0Size">499</variable>
        <variable name="OSDPartitions0SizeUnits" property="Partitions0SizeUnits">MB</variable>
        <variable name="OSDPartitions0VolumeLetterVariable" property="Partitions0VolumeLetterVariable"></variable>
        <variable name="OSDPartitions1Type" property="Partitions1Type">Primary</variable>
        <variable name="OSDPartitions1FileSystem" property="Partitions1FileSystem">NTFS</variable>
        <variable name="OSDPartitions1Bootable" property="Partitions1Bootable">False</variable>
        <variable name="OSDPartitions1QuickFormat" property="Partitions1QuickFormat">True</variable>
        <variable name="OSDPartitions1VolumeName" property="Partitions1VolumeName">Windows</variable>
        <variable name="OSDPartitions1Size" property="Partitions1Size">100</variable>
        <variable name="OSDPartitions1SizeUnits" property="Partitions1SizeUnits">%</variable>
        <variable name="OSDPartitions1VolumeLetterVariable" property="Partitions1VolumeLetterVariable">OSDisk</variable>
        <variable name="OSDPartitions" property="Partitions">2</variable>
        <variable name="OSDPartitionStyle" property="PartitionStyle">MBR</variable>
      </defaultVarList>
      <action>cscript.exe "%SCRIPTROOT%\ZTIDiskpart.wsf"</action>
    </step>
    <step type="SMS_TaskSequence_PartitionDiskAction" name="Format and Partition Disk (UEFI)" description="" runIn="WinPE" successCodeList="0" disable="false" continueOnError="false">
      <condition>
        <expression type="SMS_TaskSequence_VariableConditionExpression">
          <variable name="Operator">equals</variable>
          <variable name="Value">True</variable>
          <variable name="Variable">IsUEFI</variable>
        </expression>
      </condition>
      <defaultVarList>
        <variable name="OSDDiskIndex" property="DiskIndex">0</variable>
        <variable name="OSDDiskPartitions1Type" property="OSDDiskPartitions1Type">Recovery</variable>
        <variable name="DONotCreateExtraPartition" property="DONotCreateExtraPartition"></variable>
        <variable name="OSDPartitions0Type" property="Partitions0Type">EFI</variable>
        <variable name="OSDPartitions0FileSystem" property="Partitions0FileSystem">FAT32</variable>
        <variable name="OSDPartitions0Bootable" property="Partitions0Bootable">True</variable>
        <variable name="OSDPartitions0QuickFormat" property="Partitions0QuickFormat">True</variable>
        <variable name="OSDPartitions0VolumeName" property="Partitions0VolumeName">Boot</variable>
        <variable name="OSDPartitions0Size" property="Partitions0Size">100</variable>
        <variable name="OSDPartitions0SizeUnits" property="Partitions0SizeUnits">MB</variable>
        <variable name="OSDPartitions0VolumeLetterVariable" property="Partitions0VolumeLetterVariable"></variable>
        <variable name="OSDPartitions1Type" property="Partitions1Type">MSR</variable>
        <variable name="OSDPartitions1FileSystem" property="Partitions1FileSystem">NTFS</variable>
        <variable name="OSDPartitions1Bootable" property="Partitions1Bootable">False</variable>
        <variable name="OSDPartitions1QuickFormat" property="Partitions1QuickFormat">True</variable>
        <variable name="OSDPartitions1VolumeName" property="Partitions1VolumeName"></variable>
        <variable name="OSDPartitions1Size" property="Partitions1Size">16</variable>
        <variable name="OSDPartitions1SizeUnits" property="Partitions1SizeUnits">MB</variable>
        <variable name="OSDPartitions1VolumeLetterVariable" property="Partitions1VolumeLetterVariable"></variable>
        <variable name="OSDPartitions2Type" property="Partitions2Type">Primary</variable>
        <variable name="OSDPartitions2FileSystem" property="Partitions2FileSystem">NTFS</variable>
        <variable name="OSDPartitions2Bootable" property="Partitions2Bootable">False</variable>
        <variable name="OSDPartitions2QuickFormat" property="Partitions2QuickFormat">True</variable>
        <variable name="OSDPartitions2VolumeName" property="Partitions2VolumeName">Windows</variable>
        <variable name="OSDPartitions2Size" property="Partitions2Size">100</variable>
        <variable name="OSDPartitions2SizeUnits" property="Partitions2SizeUnits">%</variable>
        <variable name="OSDPartitions2VolumeLetterVariable" property="Partitions2VolumeLetterVariable"></variable>
        <variable name="OSDPartitions" property="Partitions">3</variable>
        <variable name="OSDPartitionStyle" property="PartitionStyle">GPT</variable>
      </defaultVarList>
      <action>cscript.exe "%SCRIPTROOT%\ZTIDiskpart.wsf"</action>
    </step>
    <step name="Copy scripts" disable="false" continueOnError="false" successCodeList="0 3010" description="" startIn="">
      <action>cscript.exe "%SCRIPTROOT%\LTICopyScripts.wsf"</action>
      <defaultVarList>
        <variable name="RunAsUser" property="RunAsUser">false</variable>
        <variable name="SMSTSRunCommandLineUserName" property="SMSTSRunCommandLineUserName"></variable>
        <variable name="SMSTSRunCommandLineUserPassword" property="SMSTSRunCommandLineUserPassword"></variable>
        <variable name="LoadProfile" property="LoadProfile">false</variable>
      </defaultVarList>
    </step>
    <step name="Configure" disable="false" continueOnError="false" successCodeList="0 3010" description="" startIn="">
      <action>cscript.exe "%SCRIPTROOT%\ZTIConfigure.wsf"</action>
      <defaultVarList>
        <variable name="RunAsUser" property="RunAsUser">false</variable>
        <variable name="SMSTSRunCommandLineUserName" property="SMSTSRunCommandLineUserName"></variable>
        <variable name="SMSTSRunCommandLineUserPassword" property="SMSTSRunCommandLineUserPassword"></variable>
        <variable name="LoadProfile" property="LoadProfile">false</variable>
      </defaultVarList>
    </step>
    <step type="SMS_TaskSequence_RunCommandLineAction" name="Enable BitLocker (Offline)" description="" disable="true" continueOnError="true" startIn="" successCodeList="0 3010" runIn="WinPEandFullOS">
      <action>cscript.exe "%SCRIPTROOT%\ZTIBDE.wsf"</action>
      <defaultVarList>
        <variable name="RunAsUser" property="RunAsUser">false</variable>
        <variable name="SMSTSRunCommandLineUserName" property="SMSTSRunCommandLineUserName"></variable>
        <variable name="SMSTSRunCommandLineUserPassword" property="SMSTSRunCommandLineUserPassword"></variable>
        <variable name="LoadProfile" property="LoadProfile">false</variable>
      </defaultVarList>
    </step>
    <step type="BDD_InjectDrivers" name="Inject Drivers" description="" disable="false" continueOnError="false" runIn="WinPEandFullOS" successCodeList="0 3010">
      <defaultVarList>
        <variable name="DriverSelectionProfile" property="DriverSelectionProfile">All Drivers</variable>
        <variable name="DriverInjectionMode" property="DriverInjectionMode">AUTO</variable>
      </defaultVarList>
      <action>cscript.exe "%SCRIPTROOT%\ZTIDrivers.wsf"</action>
    </step>
    <step type="BDD_InstallUpdatesOffline" name="Apply Patches" disable="false" continueOnError="false" successCodeList="0 3010" description="" startIn="">
      <action>cscript.exe "%SCRIPTROOT%\ZTIPatches.wsf"</action>
      <defaultVarList>
        <variable name="PackageSelectionProfile" property="PackageSelectionProfile">All Packages</variable>
      </defaultVarList>
    </step>
    <step name="Next Phase" disable="false" continueOnError="false" successCodeList="0 3010" description="" startIn="">
      <action>cscript.exe "%SCRIPTROOT%\ZTINextPhase.wsf"</action>
      <defaultVarList>
        <variable name="RunAsUser" property="RunAsUser">false</variable>
        <variable name="SMSTSRunCommandLineUserName" property="SMSTSRunCommandLineUserName"></variable>
        <variable name="SMSTSRunCommandLineUserPassword" property="SMSTSRunCommandLineUserPassword"></variable>
        <variable name="LoadProfile" property="LoadProfile">false</variable>
      </defaultVarList>
    </step>
  </group>
  <group name="Install" disable="false" continueOnError="false" description="" expand="false">
    <condition>
      <expression type="SMS_TaskSequence_VariableConditionExpression">
        <variable name="Variable">PHASE</variable>
        <variable name="Operator">equals</variable>
        <variable name="Value">INSTALL</variable>
      </expression>
    </condition>
    <step type="BDD_InstallOS" name="Install Operating System" description="" disable="false" continueOnError="false" runIn="WinPEandFullOS" successCodeList="0 3010">
      <defaultVarList>
        <variable name="OSGUID" property="OSGUID">{68bbbd83-4165-453d-a646-dfcb15e2436a}</variable>
        <variable name="DestinationDisk" property="DestinationDisk">0</variable>
        <variable name="DestinationPartition" property="DestinationPartition">1</variable>
        <variable name="DestinationOSDriveLetter" property="DestinationOSDriveLetter"></variable>
        <variable name="DestinationOSVariable" property="DestinationOSVariable">OSDisk</variable>
        <variable name="DestinationOSRefresh" property="DestinationOSRefresh"></variable>
        <variable name="DestinationOSInstallType" property="DestinationOSInstallType">ByVariable</variable>
      </defaultVarList>
      <action>cscript.exe "%SCRIPTROOT%\LTIApply.wsf"</action>
    </step>
    <step name="Next Phase" disable="false" continueOnError="false" successCodeList="0 3010" description="" startIn="">
      <action>cscript.exe "%SCRIPTROOT%\ZTINextPhase.wsf"</action>
      <defaultVarList>
        <variable name="RunAsUser" property="RunAsUser">false</variable>
        <variable name="SMSTSRunCommandLineUserName" property="SMSTSRunCommandLineUserName"></variable>
        <variable name="SMSTSRunCommandLineUserPassword" property="SMSTSRunCommandLineUserPassword"></variable>
        <variable name="LoadProfile" property="LoadProfile">false</variable>
      </defaultVarList>
    </step>
  </group>
  <group name="Postinstall" disable="false" continueOnError="false" description="" expand="false">
    <condition>
      <expression type="SMS_TaskSequence_VariableConditionExpression">
        <variable name="Variable">PHASE</variable>
        <variable name="Operator">equals</variable>
        <variable name="Value">POSTINSTALL</variable>
      </expression>
    </condition>
    <step name="Copy Scripts" disable="false" continueOnError="false" successCodeList="0 3010" description="" startIn="">
      <action>cscript.exe "%SCRIPTROOT%\LTICopyScripts.wsf"</action>
      <defaultVarList>
        <variable name="RunAsUser" property="RunAsUser">false</variable>
        <variable name="SMSTSRunCommandLineUserName" property="SMSTSRunCommandLineUserName"></variable>
        <variable name="SMSTSRunCommandLineUserPassword" property="SMSTSRunCommandLineUserPassword"></variable>
        <variable name="LoadProfile" property="LoadProfile">false</variable>
      </defaultVarList>
    </step>
    <step name="Configure" disable="false" continueOnError="false" successCodeList="0 3010" description="" startIn="">
      <action>cscript.exe "%SCRIPTROOT%\ZTIConfigure.wsf"</action>
      <defaultVarList>
        <variable name="RunAsUser" property="RunAsUser">false</variable>
        <variable name="SMSTSRunCommandLineUserName" property="SMSTSRunCommandLineUserName"></variable>
        <variable name="SMSTSRunCommandLineUserPassword" property="SMSTSRunCommandLineUserPassword"></variable>
        <variable name="LoadProfile" property="LoadProfile">false</variable>
      </defaultVarList>
    </step>
    <step name="Inject Drivers" disable="false" continueOnError="false" successCodeList="0 3010" description="" startIn="">
      <action>cscript.exe "%SCRIPTROOT%\ZTIdrivers.wsf"</action>
      <defaultVarList>
        <variable name="RunAsUser" property="RunAsUser">false</variable>
        <variable name="SMSTSRunCommandLineUserName" property="SMSTSRunCommandLineUserName"></variable>
        <variable name="SMSTSRunCommandLineUserPassword" property="SMSTSRunCommandLineUserPassword"></variable>
        <variable name="LoadProfile" property="LoadProfile">false</variable>
      </defaultVarList>
    </step>
    <step type="BDD_InstallUpdatesOffline" name="Apply Patches" disable="false" continueOnError="false" successCodeList="0 3010" description="" startIn="">
      <action>cscript.exe "%SCRIPTROOT%\ZTIPatches.wsf"</action>
      <defaultVarList>
        <variable name="PackageSelectionProfile" property="PackageSelectionProfile">All Packages</variable>
      </defaultVarList>
    </step>
    <step name="Next Phase" disable="false" continueOnError="false" successCodeList="0 3010" description="" startIn="">
      <action>cscript.exe "%SCRIPTROOT%\ZTINextPhase.wsf"</action>
      <defaultVarList>
        <variable name="RunAsUser" property="RunAsUser">false</variable>
        <variable name="SMSTSRunCommandLineUserName" property="SMSTSRunCommandLineUserName"></variable>
        <variable name="SMSTSRunCommandLineUserPassword" property="SMSTSRunCommandLineUserPassword"></variable>
        <variable name="LoadProfile" property="LoadProfile">false</variable>
      </defaultVarList>
    </step>
    <step type="SMS_TaskSequence_RebootAction" name="Restart computer" description="" disable="false" continueOnError="false" runIn="WinPEandFullOS" successCodeList="0 3010">
      <defaultVarList>
        <variable name="Message" property="Message"></variable>
        <variable name="MessageTimeout" property="MessageTimeout">60</variable>
        <variable name="Target" property="Target"></variable>
      </defaultVarList>
      <action>smsboot.exe /target:WinPE</action>
    </step>
  </group>
  <group name="State Restore" disable="false" continueOnError="false" description="" expand="true">
    <condition>
      <expression type="SMS_TaskSequence_VariableConditionExpression">
        <variable name="Variable">PHASE</variable>
        <variable name="Operator">equals</variable>
        <variable name="Value">STATERESTORE</variable>
      </expression>
    </condition>
    <step type="BDD_Gather" name="Gather local only" disable="false" continueOnError="false" successCodeList="0 3010" description="" startIn="">
      <defaultVarList>
        <variable name="GatherLocalOnly" property="GatherLocalOnly">true</variable>
        <variable name="RulesFile" property="RulesFile"></variable>
      </defaultVarList>
      <action>cscript.exe "%SCRIPTROOT%\ZTIGather.wsf"</action>
    </step>
    <step name="Post-Apply Cleanup" disable="false" continueOnError="false" successCodeList="0 3010" description="" startIn="">
      <action>cscript.exe "%SCRIPTROOT%\LTIApply.wsf" /post</action>
      <defaultVarList>
        <variable name="RunAsUser" property="RunAsUser">false</variable>
        <variable name="SMSTSRunCommandLineUserName" property="SMSTSRunCommandLineUserName"></variable>
        <variable name="SMSTSRunCommandLineUserPassword" property="SMSTSRunCommandLineUserPassword"></variable>
        <variable name="LoadProfile" property="LoadProfile">false</variable>
      </defaultVarList>
    </step>
    <step type="BDD_RecoverDomainJoin" name="Recover From Domain " description="" disable="true" continueOnError="false" runIn="WinPEandFullOS" successCodeList="0 3010">
      <defaultVarList>
        <variable name="DomainErrorRecovery" property="DomainErrorRecovery">Auto</variable>
      </defaultVarList>
      <action>cscript.exe "%SCRIPTROOT%\ZTIDomainJoin.wsf" </action>
    </step>
    <step name="Tattoo" disable="true" continueOnError="true" successCodeList="0 3010" description="" startIn="">
      <action>cscript.exe "%SCRIPTROOT%\ZTITatoo.wsf"</action>
      <defaultVarList>
        <variable name="RunAsUser" property="RunAsUser">false</variable>
        <variable name="SMSTSRunCommandLineUserName" property="SMSTSRunCommandLineUserName"></variable>
        <variable name="SMSTSRunCommandLineUserPassword" property="SMSTSRunCommandLineUserPassword"></variable>
        <variable name="LoadProfile" property="LoadProfile">false</variable>
      </defaultVarList>
    </step>
    <step name="Opt In to CEIP and WER" disable="true" continueOnError="true" successCodeList="0 3010" description="" startIn="">
      <action>cscript.exe "%SCRIPTROOT%\ZTIOptIn.wsf" /CEIP:YES /WER:YES</action>
      <defaultVarList>
        <variable name="RunAsUser" property="RunAsUser">false</variable>
        <variable name="SMSTSRunCommandLineUserName" property="SMSTSRunCommandLineUserName"></variable>
        <variable name="SMSTSRunCommandLineUserPassword" property="SMSTSRunCommandLineUserPassword"></variable>
        <variable name="LoadProfile" property="LoadProfile">false</variable>
      </defaultVarList>
    </step>
    <step type="BDD_InstallRoles" name="Install Roles and Features" description="" disable="true" continueOnError="false" runIn="WinPEandFullOS" successCodeList="0 3010">
      <defaultVarList>
        <variable name="OSRoleIndex" property="OSRoleIndex">13</variable>
        <variable name="OSRoles" property="OSRoles"></variable>
        <variable name="OSRoleServices" property="OSRoleServices"></variable>
        <variable name="OSFeatures" property="OSFeatures">NetFx3</variable>
      </defaultVarList>
      <action>cscript.exe "%SCRIPTROOT%\ZTIOSRole.wsf"</action>
    </step>
    <step name="Windows Update (Pre-Application Installation)" disable="false" continueOnError="true" successCodeList="0 3010" description="" startIn="">
      <action>cscript.exe "%SCRIPTROOT%\ZTIWindowsUpdate.wsf"</action>
      <defaultVarList>
        <variable name="RunAsUser" property="RunAsUser">false</variable>
        <variable name="SMSTSRunCommandLineUserName" property="SMSTSRunCommandLineUserName"></variable>
        <variable name="SMSTSRunCommandLineUserPassword" property="SMSTSRunCommandLineUserPassword"></variable>
        <variable name="LoadProfile" property="LoadProfile">false</variable>
      </defaultVarList>
    </step>
    <group expand="true" name="Other Tools" description="Install Other Tools Services, Drivers and commandline tools." disable="false" continueOnError="false">
      <action />
      <step type="BDD_VMWARE_TOOLS" name="VMware Tools" description="" disable="false" continueOnError="false" successCodeList="0 3010">
        <defaultVarList>
          <variable name="ExeInstaller" property="ExeInstaller">VMware-tools-12.1.5-20735119-x86_64.exe</variable>
          <variable name="Feature" property="Feature">CBHelper,Perfmon,ServiceDiscovery,Hgfs,VMCI,SVGA,VMXNet3,PVSCSI,EFIFW,MemCtl,Mouse,MouseUsb,VSS,Drivers,TrayIcon,VGAuth,Common,Toolbox,Plugins,Unity</variable>
        </defaultVarList>
        <action>"%DeployRoot%\Other Tools\%ExeInstaller%" /s /v"/qb-! ADDLOCAL=%Feature% REBOOT=R"</action>
      </step>
      <step type="SMS_TaskSequence_RebootAction" name="Restart computer" description="" disable="false" continueOnError="false" runIn="WinPEandFullOS" successCodeList="0 3010">
        <defaultVarList>
          <variable name="SMSRebootMessage" property="Message"></variable>
          <variable name="SMSRebootTimeout" property="MessageTimeout">60</variable>
          <variable name="SMSRebootTarget" property="Target"></variable>
        </defaultVarList>
        <action>smsboot.exe /target:WinPE</action>
      </step>
    </group>
    <step type="BDD_InstallApplication" name="Install Applications" disable="false" continueOnError="false" successCodeList="0 3010" description="" startIn="">
      <action>cscript.exe "%SCRIPTROOT%\ZTIApplications.wsf"</action>
      <defaultVarList>
        <variable name="ApplicationGUID" property="ApplicationGUID"></variable>
        <variable name="ApplicationSuccessCodes" property="ApplicationSuccessCodes">0 3010</variable>
      </defaultVarList>
    </step>
    <step name="Windows Update (Post-Application Installation)" disable="false" continueOnError="true" successCodeList="0 3010" description="" startIn="">
      <action>cscript.exe "%SCRIPTROOT%\ZTIWindowsUpdate.wsf"</action>
      <defaultVarList>
        <variable name="RunAsUser" property="RunAsUser">false</variable>
        <variable name="SMSTSRunCommandLineUserName" property="SMSTSRunCommandLineUserName"></variable>
        <variable name="SMSTSRunCommandLineUserPassword" property="SMSTSRunCommandLineUserPassword"></variable>
        <variable name="LoadProfile" property="LoadProfile">false</variable>
      </defaultVarList>
    </step>
    <group expand="true" name="Omnissa OSOT Optimize and Generalize" description="Optimize the machine for virtual usage" disable="false" continueOnError="false">
      <action />
      <step name="Copy OSOT to local drive" disable="false" continueOnError="false" successCodeList="0 3010" description="" startIn="">
        <action>xcopy /y /f "%DeployRoot%\Omnissa\OSOT\*" "%SystemDrive%\OSOT\"</action>
        <defaultVarList>
          <variable name="RunAsUser" property="RunAsUser">false</variable>
          <variable name="SMSTSRunCommandLineUserName" property="SMSTSRunCommandLineUserName"></variable>
          <variable name="SMSTSRunCommandLineUserPassword" property="SMSTSRunCommandLineUserPassword"></variable>
          <variable name="LoadProfile" property="LoadProfile">false</variable>
        </defaultVarList>
      </step>
      <step type="BDD_OSOT_OPTIMIZE" name="Optimize" description="" disable="false" continueOnError="false" successCodeList="0 3010">
        <defaultVarList>
          <variable name="TemplateCfg" property="TemplateCfg">Omnissa Templates\Windows 10, 11 and Server 2019, 2022</variable>
          <variable name="VisualEffectCfg" property="VisualEffectCfg">-VisualEffect balanced DisableHardwareAcceleration</variable>
          <variable name="NotificationCfg" property="NotificationCfg">-Notification Disable</variable>
          <variable name="WindowsUpdateCfg" property="WindowsUpdateCfg">-WindowsUpdate Disable</variable>
          <variable name="WindowsSearchCfg" property="WindowsSearchCfg">-WindowsSearch SearchBoxAsInputBox</variable>
          <variable name="StoreAppCfg" property="StoreAppCfg">-StoreApp remove-all --exclude WebExtension</variable>
          <variable name="BackgroundColorCfg" property="BackgroundColorCfg">-Background #0063B1 EnableCustomization</variable>
          <variable name="SecurityCfg" property="SecurityCfg">-SecurityCenter Enable -Firewall Enable -AntiVirus Enable -SmartScreen Disable -HVCI Disable -OneDrive Enable</variable>
          <variable name="OptimizeCfg" property="OptimizeCfg">-o -t Omnissa%20Templates%5CWindows%2010,%2011%20and%20Server%202019,%202022 -VisualEffect balanced DisableHardwareAcceleration -Notification Disable -WindowsUpdate Disable -WindowsSearch SearchBoxAsInputBox -StoreApp remove-all --exclude WebExtension -Background #0063B1 EnableCustomization -SecurityCenter Enable -Firewall Enable -AntiVirus Enable -SmartScreen Disable -HVCI Disable -OneDrive Enable -v</variable>
          <variable name="EnableCommonOptions" property="EnableCommonOptions">true</variable>
          <variable name="ApplyOptimization" property="ApplyOptimization"></variable>
          <variable name="OsotToolFileName" property="OsotToolFileName">@@PRODUCT_NAME@@</variable>
        </defaultVarList>
        <action>%DeployRoot%\Tools\%Architecture%\ServiceUI.exe -process:TSProgressUI.exe %SystemDrive%\OSOT\%OsotToolFileName% %OptimizeCfg%</action>
      </step>
      <step type="SMS_TaskSequence_RunCommandLineAction" name="Retrieve optimization result report to MDT server" description="Optimization result report will be saved to &lt;deployment share&gt;\OptimizationResults\" disable="false" continueOnError="false" startIn="" successCodeList="0 3010" runIn="WinPEandFullOS">
        <defaultVarList>
          <variable name="PackageID" property="PackageID"></variable>
          <variable name="RunAsUser" property="RunAsUser">false</variable>
          <variable name="SMSTSRunCommandLineUserName" property="SMSTSRunCommandLineUserName"></variable>
          <variable name="SMSTSRunCommandLineUserPassword" property="SMSTSRunCommandLineUserPassword"></variable>
          <variable name="LoadProfile" property="LoadProfile">false</variable>
        </defaultVarList>
        <action>xcopy /y /f "%ProgramData%\Omnissa\Omnissa OS Optimization Tool\Archive\Report_*.html" "%DeployRoot%\OptimizationResults\"</action>
      </step>
	  <step type="SMS_TaskSequence_RebootAction" name="Restart computer" description="" disable="false" continueOnError="false" runIn="WinPEandFullOS" successCodeList="0 3010">
        <defaultVarList>
          <variable name="SMSRebootMessage" property="Message"></variable>
          <variable name="SMSRebootTimeout" property="MessageTimeout">60</variable>
          <variable name="SMSRebootTarget" property="Target"></variable>
        </defaultVarList>
        <action>smsboot.exe /target:WinPE</action>
      </step>
      <step type="BDD_OSOT_GENERALIZE" name="Generalize" description="Generalizing the Windows image removes computer-specific information so that the image can be safely cloned and deployed throughout the enterprise." disable="false" continueOnError="false" successCodeList="0 3010">
        <defaultVarList>
          <variable name="TimeZone" property="TimeZone">Eastern Standard Time</variable>
          <variable name="InputLocale" property="InputLocale">en-US</variable>
          <variable name="SystemLocale" property="SystemLocale">en-US</variable>
          <variable name="AdminUsername" property="AdminUsername">administrator</variable>
          <variable name="AdminPassword" property="AdminPassword"></variable>
          <variable name="Autologon" property="Autologon">true</variable>
          <variable name="CopyProfile" property="CopyProfile">true</variable>
          <variable name="GeneralizeCfg" property="GeneralizeCfg">-mdtg %7B'TimeZone':'Eastern%20Standard%20Time','InputLocale':'en-US','SystemLocale':'en-US','AdminUsername':'administrator','AdminPassword':'','Autologon':true,'CopyProfile':true%7D</variable>
          <variable name="OsotToolFileName" property="OsotToolFileName">@@PRODUCT_NAME@@</variable>
        </defaultVarList>
        <action>%DeployRoot%\Tools\%Architecture%\ServiceUI.exe -process:TSProgressUI.exe %SystemDrive%\OSOT\%OsotToolFileName% %GeneralizeCfg%</action>
      </step>
      <step type="SMS_TaskSequence_RebootAction" name="Restart computer" description="" disable="false" continueOnError="false" runIn="WinPEandFullOS" successCodeList="0 3010">
        <defaultVarList>
          <variable name="SMSRebootMessage" property="Message"></variable>
          <variable name="SMSRebootTimeout" property="MessageTimeout">60</variable>
          <variable name="SMSRebootTarget" property="Target"></variable>
        </defaultVarList>
        <action>smsboot.exe /target:WinPE</action>
      </step>
      <step name="Remove temporary OSOT" disable="false" continueOnError="false" successCodeList="0 3010" description="" startIn="">
        <action>%DeployRoot%\Tools\%Architecture%\ServiceUI.exe -process:TSProgressUI.exe "%windir%\system32\cmd.exe" /c rmdir /S /Q %SystemDrive%\OSOT\</action>
        <defaultVarList>
          <variable name="RunAsUser" property="RunAsUser">false</variable>
          <variable name="SMSTSRunCommandLineUserName" property="SMSTSRunCommandLineUserName"></variable>
          <variable name="SMSTSRunCommandLineUserPassword" property="SMSTSRunCommandLineUserPassword"></variable>
          <variable name="LoadProfile" property="LoadProfile">false</variable>
        </defaultVarList>
      </step>
    </group>
    <step type="SMS_TaskSequence_EnableBitLockerAction" name="Enable BitLocker" continueOnError="true" successCodeList="0 3010" description="" startIn="" disable="true">
      <action>cscript.exe "%SCRIPTROOT%\ZTIBde.wsf"</action>
      <condition>
        <expression type="SMS_TaskSequence_VariableConditionExpression">
          <variable name="Variable">BdeInstallSuppress</variable>
          <variable name="Operator">notEquals</variable>
          <variable name="Value">YES</variable>
        </expression>
      </condition>
      <defaultVarList>
        <variable name="OSDBitLockerTargetDrive" property="BdeTargetDriveLetter"></variable>
        <variable name="OSDBitLockerMode" property="BdeInstall">TPM</variable>
        <variable name="OSDBitLockerStartupKeyDrive" property="BdeKeyLocation"></variable>
        <variable name="OSDBitLockerCreateRecoveryPassword" property="BdeRecoveryPassword">AD</variable>
        <variable name="OSDBitLockerWaitForEncryption" property="WaitForEncryption">false</variable>
      </defaultVarList>
    </step>
    <step name="Apply Local GPO Package" description="" disable="true" continueOnError="false" startIn="" successCodeList="0 3010">
      <action>cscript.exe "%SCRIPTROOT%\ZTIApplyGPOPack.wsf"</action>
      <condition>
        <expression type="SMS_TaskSequence_VariableConditionExpression">
          <variable name="Variable">ApplyGPOPack</variable>
          <variable name="Operator">notEquals</variable>
          <variable name="Value">NO</variable>
        </expression>
      </condition>
      <defaultVarList>
        <variable name="RunAsUser" property="RunAsUser">false</variable>
        <variable name="SMSTSRunCommandLineUserName" property="SMSTSRunCommandLineUserName"></variable>
        <variable name="SMSTSRunCommandLineUserPassword" property="SMSTSRunCommandLineUserPassword"></variable>
        <variable name="LoadProfile" property="LoadProfile">false</variable>
      </defaultVarList>
    </step>
    <group expand="true" name="Omnissa Horizon Agents" description="Install Horizon Agents" disable="false" continueOnError="false">
      <action />
      <step type="SMS_TaskSequence_RunCommandLineAction" name="Remote Desktop Services" description="Enable for RDSH" disable="true" continueOnError="false" startIn="" successCodeList="0 3010" runIn="WinPEandFullOS">
        <defaultVarList>
          <variable name="PackageID" property="PackageID"></variable>
          <variable name="RunAsUser" property="RunAsUser">false</variable>
          <variable name="SMSTSRunCommandLineUserName" property="SMSTSRunCommandLineUserName"></variable>
          <variable name="SMSTSRunCommandLineUserPassword" property="SMSTSRunCommandLineUserPassword"></variable>
          <variable name="LoadProfile" property="LoadProfile">false</variable>
        </defaultVarList>
        <action>dism /online /enable-feature /featurename:Remote-Desktop-Services /featurename:AppServer /NoRestart</action>
      </step>
      <step type="SMS_TaskSequence_RebootAction" name="Restart computer" description="" disable="true" continueOnError="false" runIn="WinPEandFullOS" successCodeList="0 3010">
        <defaultVarList>
          <variable name="SMSRebootMessage" property="Message"></variable>
          <variable name="SMSRebootTimeout" property="MessageTimeout">60</variable>
          <variable name="SMSRebootTarget" property="Target"></variable>
        </defaultVarList>
        <action>smsboot.exe /target:WinPE</action>
      </step>
      <step type="BDD_HORIZON_AGENT" name="Horizon Agent" description="" disable="false" continueOnError="false" successCodeList="0 3010">
        <defaultVarList>
          <variable name="ExeInstaller" property="ExeInstaller">Omnissa-Horizon-Agent-x86_64-2303-8.9.0-21435111.exe</variable>
          <variable name="IP" property="IP">IPv4</variable>
          <variable name="RDP" property="RDP">1</variable>
          <variable name="UNCPathRedirection" property="UNCPathRedirection">0</variable>
          <variable name="URLContentRedirection" property="URLContentRedirection">0</variable>
          <variable name="Feature" property="Feature">HznVidd,TSMMR,BlastUDP,Core,RTAV,NGVC,ClientDriveRedirection,HznVaudio,PrintRedir,HelpDesk,RDP</variable>
        </defaultVarList>
        <action>"%DeployRoot%\Omnissa\HorizonAgent\%ExeInstaller%" /s /v"/qb-! VDM_VC_MANAGED_AGENT=1 VDM_IP_PROTOCOL_USAGE=%IP% RDP_CHOICE=%RDP% URL_FILTERING_ENABLED=%URLContentRedirection% ENABLE_UNC_REDIRECTION=%UNCPathRedirection% ADDLOCAL=%Feature% REBOOT=R"</action>
      </step>
      <step type="BDD_HORIZON_DEM" name="Dynamic Environment Manager" description="" disable="false" continueOnError="false" successCodeList="0 3010">
        <defaultVarList>
          <variable name="Installer" property="Installer">Omnissa Dynamic Environment Manager Enterprise 10.8.0.1064 x64.msi</variable>
          <variable name="Feature" property="Feature">FlexEngine,FlexMigrate,FlexProfilesSelfSupport</variable>
        </defaultVarList>
        <action>msiexec /i "%DeployRoot%\Omnissa\DEM\%Installer%" /qb-! ADDLOCAL=%Feature% REBOOT=R</action>
      </step>
      <step type="BDD_HORIZON_AV" name="App Volumes Agent" description="" disable="true" continueOnError="false" successCodeList="0 3010">
        <defaultVarList>
          <variable name="Installer" property="Installer">App Volumes Agent.msi</variable>
          <variable name="IP" property="IP">appvolumes.domain.com</variable>
          <variable name="PORT" property="PORT">443</variable>
          <variable name="EnforceSSLCertificateValidation" property="EnforceSSLCertificateValidation">1</variable>
        </defaultVarList>
        <action>msiexec /i "%DeployRoot%\Omnissa\AppVolumesAgent\%Installer%" /qb-! MANAGER_ADDR=%IP% MANAGER_PORT=%PORT% EnforceSSLCertificateValidation=%EnforceSSLCertificateValidation% REBOOT=R</action>
      </step>
    </group>
    <group expand="false" name="NVIDIA vGPU" description="" disable="true" continueOnError="false">
      <action />
      <step type="SMS_TaskSequence_RunCommandLineAction" name="Enable RDP" description="" disable="false" continueOnError="false" startIn="" successCodeList="0 3010" runIn="WinPEandFullOS">
        <defaultVarList>
          <variable name="PackageID" property="PackageID"></variable>
          <variable name="RunAsUser" property="RunAsUser">false</variable>
          <variable name="SMSTSRunCommandLineUserName" property="SMSTSRunCommandLineUserName"></variable>
          <variable name="SMSTSRunCommandLineUserPassword" property="SMSTSRunCommandLineUserPassword"></variable>
          <variable name="LoadProfile" property="LoadProfile">false</variable>
        </defaultVarList>
        <action>reg add "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Terminal Server" /v fDenyTSConnections /t REG_DWORD /d 0 /f</action>
      </step>
      <step type="SMS_TaskSequence_RunCommandLineAction" name="NVIDIA vGPU VM Graphics Driver" description="" disable="false" continueOnError="false" startIn="" successCodeList="0 3010" runIn="WinPEandFullOS">
        <defaultVarList>
          <variable name="PackageID" property="PackageID"></variable>
          <variable name="RunAsUser" property="RunAsUser">false</variable>
          <variable name="SMSTSRunCommandLineUserName" property="SMSTSRunCommandLineUserName"></variable>
          <variable name="SMSTSRunCommandLineUserPassword" property="SMSTSRunCommandLineUserPassword"></variable>
          <variable name="LoadProfile" property="LoadProfile">false</variable>
        </defaultVarList>
        <action>%DeployRoot%\NVIDIA\setup.exe -s -n</action>
      </step>
      <step type="SMS_TaskSequence_RunCommandLineAction" name="Copy NVIDIA vGPU License Token" description="" disable="false" continueOnError="false" startIn="" successCodeList="0 3010" runIn="WinPEandFullOS">
        <defaultVarList>
          <variable name="PackageID" property="PackageID"></variable>
          <variable name="RunAsUser" property="RunAsUser">false</variable>
          <variable name="SMSTSRunCommandLineUserName" property="SMSTSRunCommandLineUserName"></variable>
          <variable name="SMSTSRunCommandLineUserPassword" property="SMSTSRunCommandLineUserPassword"></variable>
          <variable name="LoadProfile" property="LoadProfile">false</variable>
        </defaultVarList>
        <action>xcopy /y /f "%DeployRoot%\NVIDIA\*.tok" "%ProgramFiles%\NVIDIA Corporation\vGPU Licensing\ClientConfigToken\"</action>
      </step>
    </group>
    <group expand="true" name="Omnissa OSOT Finalize" description="Cleanup" disable="false" continueOnError="false">
      <step type="BDD_OSOT_FINALIZE" name="Finalize" description="Finalize should be run once all other updates and changes have been made to the image. This is typically the last step before shutting down, snapshotting and using the image for Horizon. This runs various system clean up tasks and removes information making the image ready for cloning." disable="false" continueOnError="false" successCodeList="0 3010">
        <defaultVarList>
          <variable name="FinalizeCfg" property="FinalizeCfg">-f 0 1 2 3 4 5 6 7 8 9 10</variable>
          <variable name="OsotToolFileName" property="OsotToolFileName">@@PRODUCT_NAME@@</variable>
        </defaultVarList>
        <action>%DeployRoot%\Tools\%Architecture%\ServiceUI.exe -process:TSProgressUI.exe %DeployRoot%\Omnissa\OSOT\%OsotToolFileName% %FinalizeCfg%</action>
      </step>
      <step type="SMS_TaskSequence_RebootAction" name="Restart computer" description="" disable="false" continueOnError="false" runIn="WinPEandFullOS" successCodeList="0 3010">
        <defaultVarList>
          <variable name="SMSRebootMessage" property="Message"></variable>
          <variable name="SMSRebootTimeout" property="MessageTimeout">60</variable>
          <variable name="SMSRebootTarget" property="Target"></variable>
        </defaultVarList>
        <action>smsboot.exe /target:WinPE</action>
      </step>
      <action />
      <step type="SMS_TaskSequence_RunCommandLineAction" name="Shutdown" description="" disable="false" continueOnError="true" startIn="" successCodeList="0 3010" runIn="WinPEandFullOS">
        <defaultVarList>
          <variable name="PackageID" property="PackageID"></variable>
          <variable name="RunAsUser" property="RunAsUser">false</variable>
          <variable name="SMSTSRunCommandLineUserName" property="SMSTSRunCommandLineUserName"></variable>
          <variable name="SMSTSRunCommandLineUserPassword" property="SMSTSRunCommandLineUserPassword"></variable>
          <variable name="LoadProfile" property="LoadProfile">false</variable>
        </defaultVarList>
        <action>shutdown -s -t 10</action>
      </step>
    </group>
  </group>
</sequence>