/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

package com.omnissa.vdi.agent.messageserver;

import java.security.PrivateKey;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.jms.ExceptionListener;
import javax.jms.JMSException;
import javax.jms.Session;
import javax.jms.TopicConnection;
import javax.jms.TopicConnectionFactory;
import javax.jms.TopicSession;

import com.omnissa.vdi.logger.Logger;
import org.apache.commons.lang3.StringUtils;

import com.swiftmq.jms.SwiftMQConnectionFactory;
import com.omnissa.vdi.messagesecurity.Identity;
import com.omnissa.vdi.messagesecurity.MessageSecurityException;
import com.omnissa.vdi.messagesecurity.WrappedMessageSecurityHandler;
import com.omnissa.vdi.messagesecurity.swiftmq.BrokerUpdateUtility;

public class CustomizationSupport implements javax.jms.ExceptionListener {
    private static final Logger log = Logger.getLogger(CustomizationSupport.class);
    private static String activeRouter;
    private static AgentJmsConfig config;
    private static TopicConnection topicConnection;
    // Static reference to singleton instance
    static private CustomizationSupport custSupportInstance = null;

    private static List<String> getBrokerList() {
        List<String> brokers = new ArrayList<>(config.getBrokers());
        Collections.shuffle(brokers);
        return brokers;
    }

    private static final int STANDARD_JMS_PORT = 4001;

    private static final int SECURE_JMS_PORT = 4002;

    public static Map<String, String> createJmsPropertiesMap(String broker,
            boolean useSecurePort, boolean isDaaSAgent, boolean isDaasLegacyPairingMode) {
        Map<String, String> props = new HashMap<String, String>();
        if (useSecurePort || isDaasLegacyPairingMode) {
            if (Main.isSimulated()) {
                props.put(SwiftMQConnectionFactory.SOCKETFACTORY,
                        "com.omnissa.vdi.messagesecurity.swiftmq.FileBasedSSLSocketFactory");
            } else if (isDaaSAgent) {
                props.put(SwiftMQConnectionFactory.SOCKETFACTORY,
                        "com.omnissa.vdi.agent.messageserver.DaasSSLSocketFactory");
            } else {
                props.put(SwiftMQConnectionFactory.SOCKETFACTORY,
                        "com.omnissa.vdi.agent.messageserver.AgentSSLSocketFactory");
            }
            props.put(SwiftMQConnectionFactory.PORT, "" + SECURE_JMS_PORT);
        } else {
            props.put(SwiftMQConnectionFactory.SOCKETFACTORY,
                    "com.swiftmq.net.PlainSocketFactory");
            props.put(SwiftMQConnectionFactory.PORT, "" + STANDARD_JMS_PORT);
        }
        props.put(SwiftMQConnectionFactory.HOSTNAME, broker);
        props.put(SwiftMQConnectionFactory.KEEPALIVEINTERVAL, "20000");
        props.put(SwiftMQConnectionFactory.JMS_TTL, "1800000");
        // SwiftMQ scalability recommendations
        props.put(SwiftMQConnectionFactory.SMQP_PRODUCER_REPLY_INTERVAL, "2");
        props.put(SwiftMQConnectionFactory.SMQP_CONSUMER_CACHE_SIZE, "2");
        props.put(SwiftMQConnectionFactory.INPUT_BUFFER_SIZE, "4192");
        props.put(SwiftMQConnectionFactory.OUTPUT_BUFFER_SIZE, "4192");
        return props;
    }

    @Override
    public void onException(JMSException arg0) {
    }


    /**
     * Create or retrieve the singleton instance of CustomizationSupport
     *
     * @return CustomizationSupport
     */
    static public CustomizationSupport GetSingletonInstance() {
        try {
            if (custSupportInstance == null) {
                custSupportInstance = new CustomizationSupport();
            }
            return custSupportInstance;
        } catch (Throwable t) {
            String msg = "Failed to fetch the instance: " + t;
            log.warn(msg);
            log.debug(msg, t);
            throw new RuntimeException(msg, t);
        }
    }


    public static boolean sendCustomizationMsg(String custState) {
        config = new AgentRegistryConfig();
        config.init();
        log.debug("Sending CUSTOMIZATION message with param {}", custState);

        while (true) {
            List<String> brokerList = getBrokerList();

            for (String broker : brokerList) {
                activeRouter = broker;
                log.debug("sendCustomizationMsg: Using connection broker {}",
                        activeRouter);
                try {
                    Map<String, String> props = createJmsPropertiesMap(
                            activeRouter, false, false, false);

                    TopicConnectionFactory topicConnectionFactory = (TopicConnectionFactory) SwiftMQConnectionFactory
                            .create(props);
                    topicConnection = topicConnectionFactory
                            .createTopicConnection();
                    topicConnection.setExceptionListener(custSupportInstance);
                    topicConnection.start();

                    PrivateKey signer = null;
                    if (config.isPaired()) {
                        log.debug("Using paired signing key");
                        signer = config.getPrivateKey();
                        if (signer == null) {
                            // No private key for signing
                            throw new MessageSecurityException("Paired key does not exist");
                        }
                    } else {
                        log.debug("Machine is not paired, can't send the Customization message.");
                        return false;
                    }
                    AgentMessageSecurityHandler handler = new AgentMessageSecurityHandler(false);
                    handler.configure(config.getMsMode(), config.getIdentity(), signer,
                                    config.getBrokerPublicKey(), null, config);
                    WrappedMessageSecurityHandler wrappedHandler = new WrappedMessageSecurityHandler(handler);

                    TopicSession session = topicConnection.createTopicSession(false, Session.AUTO_ACKNOWLEDGE);
                    final long DEFAULT_CUSTOMIZATION_MESSAGE_TTL_IN_MILLSECONDS = TimeUnit.SECONDS.toMillis(10);
                    final long DEFAULT_CUSTOMIZATION_RESPONSE_TIMEOUT_IN_MILLSECONDS = TimeUnit.SECONDS.toMillis(15);

                    BrokerUpdateUtility updater = new BrokerUpdateUtility(session,
                            "desktopcontrol", "CUSTOMIZATION", "CUSTOMIZATIONREPLY",
                            wrappedHandler,
                            config.getBrokers().size(),
                            DEFAULT_CUSTOMIZATION_RESPONSE_TIMEOUT_IN_MILLSECONDS,
                            DEFAULT_CUSTOMIZATION_MESSAGE_TTL_IN_MILLSECONDS);
                    // Parameters to send with this message.
                    Identity identity = config.getIdentity();
                    updater.addParameterToSend("CUSTOMIZATION_STATE", custState);
                    updater.addParameterToSend("AGENT_IDENTITY", identity.toString());
                    // Parameters to receive.
                    updater.addParameterToReceive("OPERATION_TYPE");
                    updater.updateOverJms(new BrokerUpdateUtility.LoopChecker() {
                        @Override
                        public boolean isRunning() {
                            return true;
                        }
                    });

                    if (updater.okResponseReceived()) {
                        log.info("Received response to CUSTOMIZATION message");
                        // Read the OPERATION_TYPE value and save it in registry.
                        String operationType = updater
                            .getReceivedParameter("OPERATION_TYPE");
                        // Set it to registry.
                        config.setIcaParameter("OPERATION_TYPE", operationType);
                        return true;
                    }
                } catch (InterruptedException e) {
                    if (log.isDebugEnabled()) {
                        log.debug(
                                "sendCustomizationMsg: Interrupted while connecting to JMS server {}",
                                activeRouter, e);
                    }
                } catch (Exception e) {
                    log.debug("Unable to send CUSTOMIZATION message to " + activeRouter, e);
                } finally {
                    try {
                        topicConnection.stop();
                        topicConnection.setExceptionListener(null);
                        topicConnection.close();
                        topicConnection = null;
                    } catch (JMSException e) {
                        log.debug("Exception during teardown: " + e.getMessage(), e);
                    }
                    
                }
            }

            /*
             * We get here if there are no brokers defined, or connections to
             * all brokers in the list failed. Sleep before trying the list
             * again.
             */
            if (!brokerList.isEmpty()) {
                log.warn("Unable to connect to any listed host."
                        + " The agent will continue to retry: " + brokerList);
            }

            // Sleep and try again.
            try {
                Thread.sleep(15000);
            } catch (InterruptedException e) {
                if (brokerList.isEmpty()) {
                    throw new RuntimeException("No brokers defined");
                }
                break;
            }
        }
        return false;
    }
};
