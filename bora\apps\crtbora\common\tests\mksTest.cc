/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * mksTest.cc -
 *
 *    Unit Test of mks.cc
 */

#include <gmock/gmock.h>
#include <gtest/gtest.h>
#include "common/tests/baseUnitTest.hh"
#include "common/module.hh"
#include "gcd.h"
#include "cuiTypes.hh"
#ifdef _WIN32
#   include "hznprotect/hznprotect.h"
#endif
#include "mksUTMock.hh"
#include "vmUTMock.hh"
#include "ViewControlUTMock.hh"
#include "rxUTLog.h"
#include "utMock.h"
#include "util.h"


using ::testing::_;
using ::testing::An;
using ::testing::Invoke;
using ::testing::NiceMock;
using ::testing::Return;

using namespace crt::common;
using namespace crt::common::test;

class MKSUnitTest : public BaseUnitTest {};


/*
 *-----------------------------------------------------------------------------
 *
 * MKSUnitTest::SetDisplayTopology --
 *
 *    Test SetDisplayTopology method. following 2 methods are included
 *       virtual bool SetDisplayTopology(const std::vector<RdeChannelDisplayInfo> &displayInfos);
 *       virtual bool SetDisplayTopology(const std::vector<MKSDisplayTopologyPacket> &monitors);
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MKSUnitTest, SetDisplayTopology)
{
   std::vector<RdeChannelDisplayInfo> displayInfos = {};
   std::vector<RdeChannelDisplayInfo> expectedDisplayInfos = {};

   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();

   MKSUTMock *mks = new NiceMock<MKSUTMock>(vm);

   EXPECT_TRUE(mks != NULL);

   // 1. displayInfos.size() <= 0 test
   bool retValue = mks->SetDisplayTopology(displayInfos);
   EXPECT_TRUE(!retValue);

   // 2. RDE_CHANNEL_DPI_SYNC_SERVER_DEFAULT
   EXPECT_CALL(*mks, SendGHIRequest(_, _, _, _, _, _)).Times(0);
   mks->SetRemoteDpiVersion(RDE_CHANNEL_DPI_SYNC_SERVER_DEFAULT);
   displayInfos = {
      {VMRect{-20, -16, 2540, 1584}, 32, 1, 240},
      {VMRect{2540, -16, 4460, 1064}, 32, 0, 144},
      {VMRect{4460, -16, 5900, 884}, 32, 0, 120},
      {VMRect{-20, 1584, 1260, 2352}, 32, 0, 96},
   };
   retValue = mks->SetDisplayTopology(displayInfos);
   EXPECT_TRUE(retValue);

   testing::Mock::VerifyAndClearExpectations(mks);

   // 3. RDE_CHANNEL_DPI_SYNC_NON_IDD_DRIVER
   EXPECT_CALL(*mks, SendGHIRequest(_, _, _, _, _, _)).Times(1).WillOnce(Return());
   EXPECT_CALL(*mks, SetDisplayTopology(An<const std::vector<MKSDisplayTopologyPacket> &>()))
      .Times(1);
   mks->SetRemoteDpiVersion(RDE_CHANNEL_DPI_SYNC_NON_IDD_DRIVER);
   retValue = mks->SetDisplayTopology(displayInfos);
   EXPECT_TRUE(retValue);
   testing::Mock::VerifyAndClearExpectations(mks);

   // 4. RDE_CHANNEL_DPI_SYNC_IDD_DRIVER
   EXPECT_CALL(*mks, SendGHIRequest(_, _, _, _, _, _)).Times(1).WillOnce(Return());
   EXPECT_CALL(*mks, SetDisplayTopology(An<const std::vector<MKSDisplayTopologyPacket> &>()))
      .Times(0);
   mks->SetRemoteDpiVersion(RDE_CHANNEL_DPI_SYNC_IDD_DRIVER);
   retValue = mks->SetDisplayTopology(displayInfos);
   EXPECT_TRUE(retValue);
   testing::Mock::VerifyAndClearExpectations(mks);

   delete mks;
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKSUnitTest::OnRdeCommonUpdateNotified --
 *
 *    Test SetDisplayTopology method. following 2 methods are included
 *       MKS::OnRdeCommonUpdateNotified(GHIGuestToHostMessageType type, // IN
 *                                  const uint8 *msg,               // IN
 *                                  uint32 msgLen)                  // IN
 *       MKS::ProcessRdeCommonDpiSyncMsg(const uint8 *msg,                // IN
 *                                  uint32 msgLen)
 *       MKS::ProcessRdeCommonDisplayMsg(const uint8 *msg,                // IN
 *                                          uint32 msgLen)
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MKSUnitTest, OnRdeCommonUpdateNotified)
{
   std::vector<RdeChannelDisplayInfo> displayInfos = {};

   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();

   MKSUTMock *mks = new NiceMock<MKSUTMock>(vm);

   EXPECT_TRUE(mks != NULL);

   // 1. GHI_GUEST_RDE_COMMON_GENERIC
   //  1.1 RDE_CHANNEL_DPI_SYNC_MSG
   int packSize = sizeof(uint32);
   int msgHeaderSize = sizeof(RdeChannelMessage) - sizeof(uint8);
   int msgLen = msgHeaderSize + sizeof(RdeChannelDPISync) + packSize;
   RdeChannelMessage *pMsg = reinterpret_cast<RdeChannelMessage *>(Util_SafeCalloc(1, msgLen));
   EXPECT_TRUE(pMsg != nullptr);
   pMsg->msgSize = msgLen;
   pMsg->msgType =
      RDE_SET_CHANNEL_MSG_TYPE(RDE_CHANNEL_DPI_SYNC_MSG, RDE_CHANNEL_DPI_SYNC_VERSION_MSG);
   RdeChannelDPISync *pRdeChannelDPISync = (RdeChannelDPISync *)(pMsg->payload);
   pRdeChannelDPISync->data.version = RDE_CHANNEL_DPI_SYNC_NON_IDD_DRIVER;

   // 1.1.1: mPendingDsiplayInfos.size() == 0
   mks->SetRemoteDpiVersion(0);
   mks->SetPendingDisplayInfos(displayInfos);

   EXPECT_CALL(*mks, SetDisplayTopology(An<const std::vector<RdeChannelDisplayInfo> &>())).Times(0);
   mks->OnRdeCommonUpdateNotified(GHI_GUEST_RDE_COMMON_GENERIC, (const uint8 *)pMsg, 0);
   testing::Mock::VerifyAndClearExpectations(mks);

   // 1.1.2: mPendingDsiplayInfos.size() != 0
   displayInfos = {
      {VMRect{0, 0, 1920, 1200}, 32, 1, 192},
      {VMRect{1920, 0, 3840, 1200}, 32, 0, 144},
   };
   mks->SetRemoteDpiVersion(0);
   mks->SetPendingDisplayInfos(displayInfos);

   EXPECT_CALL(*mks, SetDisplayTopology(An<const std::vector<RdeChannelDisplayInfo> &>())).Times(1);
   mks->OnRdeCommonUpdateNotified(GHI_GUEST_RDE_COMMON_GENERIC, (const uint8 *)pMsg, 0);
   testing::Mock::VerifyAndClearExpectations(mks);

   free(pMsg);
   pMsg = nullptr;

   // 1.2 RDE_CHANNEL_DISPLAY_INFO_MSG
   // 1.2.1: normal
   unsigned int monitorCount = displayInfos.size();
   packSize = sizeof(uint32);
   msgHeaderSize = sizeof(RdeChannelMessage) - sizeof(uint8);
   int countSize = sizeof(uint32);
   msgLen = msgHeaderSize + countSize + sizeof(RdeChannelDisplayInfo) * monitorCount + packSize;
   pMsg = reinterpret_cast<RdeChannelMessage *>(Util_SafeCalloc(1, msgLen));
   EXPECT_TRUE(pMsg != nullptr);

   pMsg->msgSize = msgLen;
   pMsg->msgType = RDE_SET_CHANNEL_MSG_TYPE(RDE_CHANNEL_DISPLAY_MSG, RDE_CHANNEL_DISPLAY_INFO_MSG);
   RdeChannelDisplaysInfo *pRdeChannelDisplaysInfo = (RdeChannelDisplaysInfo *)(pMsg->payload);
   pRdeChannelDisplaysInfo->count = monitorCount;

   int i = 0;
   for (auto displayInfo : displayInfos) {
      pRdeChannelDisplaysInfo->displayInfos[i] = displayInfo;
      i++;
   }

   mks->SetRemoteDpiVersion(0);
   mks->SetPendingDisplayInfos(displayInfos);

   EXPECT_CALL(*mks, SetDisplayTopology(An<const std::vector<MKSDisplayTopologyPacket> &>()))
      .Times(1);
   mks->OnRdeCommonUpdateNotified(GHI_GUEST_RDE_COMMON_GENERIC, (const uint8 *)pMsg, 0);
   testing::Mock::VerifyAndClearExpectations(mks);

   // 1.2.2: pRdeChannelDisplaysInfo->count==0
   pRdeChannelDisplaysInfo->count = 0;
   EXPECT_CALL(*mks, SetDisplayTopology(An<const std::vector<MKSDisplayTopologyPacket> &>()))
      .Times(0);
   mks->OnRdeCommonUpdateNotified(GHI_GUEST_RDE_COMMON_GENERIC, (const uint8 *)pMsg, 0);
   testing::Mock::VerifyAndClearExpectations(mks);

   // 1.2.3: displayInfos != mPendingDisplayInfos
   pRdeChannelDisplaysInfo->count = monitorCount;
   pRdeChannelDisplaysInfo->displayInfos[0].bpp = 0;
   EXPECT_CALL(*mks, SetDisplayTopology(An<const std::vector<MKSDisplayTopologyPacket> &>()))
      .Times(0);
   mks->OnRdeCommonUpdateNotified(GHI_GUEST_RDE_COMMON_GENERIC, (const uint8 *)pMsg, 0);
   testing::Mock::VerifyAndClearExpectations(mks);

   // 1.2.4: mPendingDsiplayInfos.size() == 0
   delete mks;
   mks = new NiceMock<MKSUTMock>(vm);
   EXPECT_TRUE(mks != NULL);
   displayInfos.clear();
   mks->SetPendingDisplayInfos(displayInfos);
   pRdeChannelDisplaysInfo->displayInfos[0].bpp = 32;
   EXPECT_CALL(*mks, SetDisplayTopology(An<const std::vector<MKSDisplayTopologyPacket> &>()))
      .Times(0);
   mks->OnRdeCommonUpdateNotified(GHI_GUEST_RDE_COMMON_GENERIC, (const uint8 *)pMsg, 0);
   testing::Mock::VerifyAndClearExpectations(mks);

   delete mks;
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKSUnitTest::ProcessRdeCommonBlockKeyLoggerMsg --
 *
 *    Test ProcessRdeCommonBlockKeyLoggerMsg method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MKSUnitTest, ProcessRdeCommonBlockKeyLoggerMsg)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();
   MKSUTMock *mks = new NiceMock<MKSUTMock>(vm);
   EXPECT_TRUE(mks != NULL);
   mks->blockKeyLoggerEnabled = false;
   EXPECT_FALSE(mks->blockKeyLoggerEnabled);

   RdeChannelMessage channelMsg{};
   channelMsg.msgType = RDE_CHANNEL_BLOCK_KEY_LOGGER_ENABLE_MSG;
   channelMsg.msgSize = sizeof(RdeChannelMessage);
   channelMsg.payload[0] = 1;
   channelMsg.msgType = RDE_SET_CHANNEL_MSG_TYPE(RDE_CHANNEL_APP_PROTECTION_MSG,
                                                 RDE_CHANNEL_BLOCK_KEY_LOGGER_ENABLE_MSG);
   mks->OnRdeCommonUpdateNotified(GHI_GUEST_RDE_COMMON_GENERIC, (const uint8 *)(&channelMsg), 0);
   EXPECT_TRUE(mks->blockKeyLoggerEnabled);
   UTConsoleLog("Test antiKeyLogger switch to enabled.");

   channelMsg.payload[0] = 0;
   mks->OnRdeCommonUpdateNotified(GHI_GUEST_RDE_COMMON_GENERIC, (const uint8 *)(&channelMsg), 0);
   EXPECT_FALSE(mks->blockKeyLoggerEnabled);
   UTConsoleLog("Test antiKeyLogger switch to disabled.");

#ifdef _WIN32
   {
      EXPECT_FALSE(mks->allowArmNoAntiKeyloggerEnabled);
      // Test allowArmNoAntiKeylogger switch to enabled.
      std::map<std::string, std::vector<uint8>> options;
      options["allowArmNoAntiKeylogger"] = {1};
      std::vector<uint8> optionBuffer;
      for (auto option : options) {
         uint32 keySize = option.first.size();
         optionBuffer.insert(optionBuffer.end(), (uint8 *)&keySize,
                             (uint8 *)&keySize + sizeof keySize);
         optionBuffer.insert(optionBuffer.end(), (uint8 *)option.first.c_str(),
                             (uint8 *)option.first.c_str() + keySize);
         uint32 valSize = option.second.size();
         optionBuffer.insert(optionBuffer.end(), (uint8 *)&valSize,
                             (uint8 *)&valSize + sizeof valSize);
         optionBuffer.insert(optionBuffer.end(), (uint8 *)option.second.data(),
                             (uint8 *)option.second.data() + valSize);
      }

      uint8 featureEnableStatus = 1;
      RdeChannelMessage channelMsg{};
      channelMsg.msgSize = sizeof channelMsg + optionBuffer.size();
      channelMsg.msgType = RDE_SET_CHANNEL_MSG_TYPE(RDE_CHANNEL_APP_PROTECTION_MSG,
                                                    RDE_CHANNEL_BLOCK_KEY_LOGGER_ENABLE_MSG);

      std::vector<uint8> buffer;
      buffer.insert(buffer.end(), (uint8 *)&channelMsg,
                    (uint8 *)&channelMsg + sizeof channelMsg - sizeof(uint8));
      buffer.insert(buffer.end(), (uint8 *)&featureEnableStatus,
                    (uint8 *)&featureEnableStatus + sizeof featureEnableStatus);
      buffer.insert(buffer.end(), optionBuffer.begin(), optionBuffer.end());
      {
         mks->allowArmNoAntiKeyloggerEnabled = false;
         VMOCK(HznProtect_IsAMD64).Will(TRUE);
         mks->OnRdeCommonUpdateNotified(GHI_GUEST_RDE_COMMON_GENERIC,
                                        (const uint8 *)(buffer.data()), 0);
         EXPECT_TRUE(mks->blockKeyLoggerEnabled);
         EXPECT_TRUE(mks->allowArmNoAntiKeyloggerEnabled);
      }
      {
         mks->allowArmNoAntiKeyloggerEnabled = false;
         VMOCK(HznProtect_IsAMD64).Will(FALSE);
         mks->OnRdeCommonUpdateNotified(GHI_GUEST_RDE_COMMON_GENERIC,
                                        (const uint8 *)(buffer.data()), 0);
         EXPECT_FALSE(mks->blockKeyLoggerEnabled);
         EXPECT_TRUE(mks->allowArmNoAntiKeyloggerEnabled);
      }
   }
#endif

   delete mks;
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKSUnitTest::ProcessRdeCommonBlockSendInputMsg --
 *
 *    Test ProcessRdeCommonBlockSendInputMsg method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MKSUnitTest, ProcessRdeCommonBlockSendInputMsg)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();
   MKSUTMock *mks = new NiceMock<MKSUTMock>(vm);
   EXPECT_TRUE(mks != NULL);
   mks->blockSendInputEnabled = false;
   EXPECT_FALSE(mks->blockSendInputEnabled);

   RdeChannelMessage channelMsg{};
   channelMsg.msgType = RDE_CHANNEL_BLOCK_SEND_INPUT_ENABLE_MSG;
   channelMsg.msgSize = sizeof(RdeChannelMessage);
   channelMsg.payload[0] = 1;
   channelMsg.msgType = RDE_SET_CHANNEL_MSG_TYPE(RDE_CHANNEL_APP_PROTECTION_MSG,
                                                 RDE_CHANNEL_BLOCK_SEND_INPUT_ENABLE_MSG);
   mks->OnRdeCommonUpdateNotified(GHI_GUEST_RDE_COMMON_GENERIC, (const uint8 *)(&channelMsg), 0);
   EXPECT_TRUE(mks->blockSendInputEnabled);
   UTConsoleLog("Test block SendInput() switch to enabled.");

   channelMsg.payload[0] = 0;
   mks->OnRdeCommonUpdateNotified(GHI_GUEST_RDE_COMMON_GENERIC, (const uint8 *)(&channelMsg), 0);
   EXPECT_FALSE(mks->blockSendInputEnabled);
   UTConsoleLog("Test block SendInput() switch to disabled.");

#ifdef _WIN32
   {
      {
         channelMsg.payload[0] = 0;
         mks->allowArmNoAntiKeyloggerEnabled = true;
         VMOCK(HznProtect_IsAMD64).Will(FALSE);
         mks->OnRdeCommonUpdateNotified(GHI_GUEST_RDE_COMMON_GENERIC, (const uint8 *)(&channelMsg),
                                        0);
         EXPECT_FALSE(mks->blockSendInputEnabled);
      }
      {
         channelMsg.payload[0] = 1;
         mks->allowArmNoAntiKeyloggerEnabled = true;
         VMOCK(HznProtect_IsAMD64).Will(FALSE);
         mks->OnRdeCommonUpdateNotified(GHI_GUEST_RDE_COMMON_GENERIC, (const uint8 *)(&channelMsg),
                                        0);
         EXPECT_FALSE(mks->blockSendInputEnabled);
      }
      {
         channelMsg.payload[0] = 1;
         mks->allowArmNoAntiKeyloggerEnabled = true;
         VMOCK(HznProtect_IsAMD64).Will(TRUE);
         mks->OnRdeCommonUpdateNotified(GHI_GUEST_RDE_COMMON_GENERIC, (const uint8 *)(&channelMsg),
                                        0);
         EXPECT_TRUE(mks->blockSendInputEnabled);
      }
      {
         channelMsg.payload[0] = 1;
         mks->allowArmNoAntiKeyloggerEnabled = false;
         VMOCK(HznProtect_IsAMD64).Will(TRUE);
         mks->OnRdeCommonUpdateNotified(GHI_GUEST_RDE_COMMON_GENERIC, (const uint8 *)(&channelMsg),
                                        0);
         EXPECT_TRUE(mks->blockSendInputEnabled);
      }
   }
#endif

   delete mks;
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKSUnitTest::ProcessRdeCommonBlockScreenCaptureMsg --
 *
 *    Test ProcessRdeCommonBlockScreenCaptureMsg method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MKSUnitTest, ProcessRdeCommonBlockScreenCaptureMsg)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();
   MKSUTMock *mks = new NiceMock<MKSUTMock>(vm);
   EXPECT_TRUE(mks != NULL);
   mks->blockKeyLoggerEnabled = false;
   EXPECT_FALSE(mks->blockKeyLoggerEnabled);

   // Test antiScreenshot switch to enabled.
   RdeChannelMessage channelMsg{};
   channelMsg.msgType = RDE_CHANNEL_BLOCK_SCREEN_CAPTURE_ENABLE_MSG;
   channelMsg.msgSize = sizeof(RdeChannelMessage);
   channelMsg.payload[0] = 1;
   channelMsg.msgType = RDE_SET_CHANNEL_MSG_TYPE(RDE_CHANNEL_APP_PROTECTION_MSG,
                                                 RDE_CHANNEL_BLOCK_SCREEN_CAPTURE_ENABLE_MSG);
   mks->OnRdeCommonUpdateNotified(GHI_GUEST_RDE_COMMON_GENERIC, (const uint8 *)(&channelMsg), 0);
   EXPECT_TRUE(mks->blockScreenCaptureEnabled);

   // Test antiScreenshot switch to disabled.
   channelMsg.payload[0] = 0;
   mks->OnRdeCommonUpdateNotified(GHI_GUEST_RDE_COMMON_GENERIC, (const uint8 *)(&channelMsg), 0);
   EXPECT_FALSE(mks->blockScreenCaptureEnabled);

   // Test allowScreenRecording switch to enabled.
   std::vector<uint8> buffer;
   channelMsg.msgSize = sizeof(RdeChannelMessage) + buffer.size() - 1;
   buffer.insert(buffer.end(), (uint8 *)&channelMsg,
                 (uint8 *)&channelMsg + sizeof channelMsg - sizeof(uint8));
   uint8 blockScreenCapture = 1;
   buffer.insert(buffer.end(), (uint8 *)&blockScreenCapture,
                 (uint8 *)&blockScreenCapture + sizeof blockScreenCapture);
   std::string keyVal = "allowScreenRecording";
   uint32 keySize = keyVal.size();
   buffer.insert(buffer.end(), (uint8 *)&keySize, (uint8 *)&keySize + sizeof keySize);
   buffer.insert(buffer.end(), (uint8 *)keyVal.c_str(), (uint8 *)keyVal.c_str() + keySize);
   uint8 val = 1;
   uint32 valSize = sizeof(val);
   buffer.insert(buffer.end(), (uint8 *)&valSize, (uint8 *)&valSize + sizeof valSize);
   buffer.insert(buffer.end(), &val, &val + valSize);
   mks->allowScreenRecordingEnabled = false;
   mks->OnRdeCommonUpdateNotified(GHI_GUEST_RDE_COMMON_GENERIC, (const uint8 *)(buffer.data()), 0);
   EXPECT_TRUE(mks->blockScreenCaptureEnabled);

   delete mks;
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKSUnitTest::ProcessRdeCommonNetworkStateEnableDisplaySettingMsg --
 *
 *    Test ProcessRdeCommonNetworkStateSettingMsg method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MKSUnitTest, ProcessRdeCommonNetworkStateEnableDisplaySettingMsg)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();
   MKSUTMock *mks = new NiceMock<MKSUTMock>(vm);
   EXPECT_TRUE(mks != NULL);

   EXPECT_FALSE(mks->networkWarningUIEnableDisplay);

   RdeChannelMessage channelMsg{};
   channelMsg.msgSize = sizeof(RdeChannelMessage);
   channelMsg.payload[0] = true;
   channelMsg.msgType = RDE_SET_CHANNEL_MSG_TYPE(RDE_CHANNEL_NETWORK_STATE_SETTING_MSG,
                                                 RDE_CHANNEL_NETWORK_STATE_ENABLE_DISPLAY_MSG);
   mks->OnRdeCommonUpdateNotified(GHI_GUEST_RDE_COMMON_GENERIC, (const uint8 *)(&channelMsg), 0);
   EXPECT_TRUE(mks->networkWarningUIEnableDisplay);

   delete mks;
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKSUnitTest::ProcessRdeCommonNetworkStateIntervalSettingMsg --
 *
 *    Test ProcessRdeCommonNetworkStateSettingMsg method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MKSUnitTest, ProcessRdeCommonNetworkStateIntervalSettingMsg)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();
   MKSUTMock *mks = new NiceMock<MKSUTMock>(vm);
   EXPECT_TRUE(mks != NULL);

   const int msgHeaderSize = sizeof(RdeChannelMessage) - sizeof(uint8);
   int mallocSize = msgHeaderSize + sizeof(uint32);
   RdeChannelMessage *pRdeChannelMsg = (RdeChannelMessage *)malloc(mallocSize);
   EXPECT_TRUE(pRdeChannelMsg != nullptr);

   pRdeChannelMsg->msgSize = mallocSize;
   pRdeChannelMsg->msgType = RDE_SET_CHANNEL_MSG_TYPE(RDE_CHANNEL_NETWORK_STATE_SETTING_MSG,
                                                      RDE_CHANNEL_NETWORK_STATE_INTERVAL_MSG);
   uint32 *pValue = reinterpret_cast<uint32 *>(pRdeChannelMsg->payload);
   *pValue = 5;

   mks->OnRdeCommonUpdateNotified(GHI_GUEST_RDE_COMMON_GENERIC, (const uint8 *)pRdeChannelMsg, 0);
   EXPECT_TRUE(mks->networkWarningUIShowingInterval.Get() == 5);

   free(pRdeChannelMsg);
   delete mks;
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKSUnitTest::SetUnityHostCursorScale --
 *
 *    Test cui::mks::SetUnityHostCursorScale method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MKSUnitTest, DISABLED_SetUnityHostCursorScale)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();

   MKSUTMock *mks = new NiceMock<MKSUTMock>(vm);
   EXPECT_TRUE(mks != NULL);

   mks->SetVdpArgs(0, "broker", "connectionUserMode", 0, FALSE, 0, "fqdn", "httpProxy", 0, FALSE,
                   std::vector<uint8>(1, 0), 0, "sslCipherString", "sslCipherSuites", "", "");

   mks->Connect("ngp_client", "target1");

   mks->SetUnityHostCursorScale(0.5);

   // Wait for MKS to send response back to UI
   sleep(2);

   EXPECT_TRUE(mks->GetNotifyUIFromRMKS());

   dispatch_sync(dispatch_get_main_queue(), [mks]() { delete mks; });
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKSUnitTest::SendUnityTouchEvent --
 *
 *    Test cui::mks::SendUnityTouchEvent method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MKSUnitTest, DISABLED_SendUnityTouchEvent)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();

   MKSUTMock *mks = new NiceMock<MKSUTMock>(vm);
   EXPECT_TRUE(mks != NULL);

   mks->SetVdpArgs(0, "broker", "connectionUserMode", 0, FALSE, 0, "fqdn", "httpProxy", 0, FALSE,
                   std::vector<uint8>(1, 0), 0, "sslCipherString", "sslCipherSuites", "", "");

   mks->Connect("ngp_client", "target1");

   cui::UnityTouchEvent touchEvent;
   for (int i = 0; i < 8; i++) {
      cui::UnityTouchPoint touchPoints;
      touchPoints.x = i + 1;
      touchPoints.y = i + 2;
      touchPoints.contactId = i + 3;
      touchPoints.flags = i;
      touchEvent.points.push_back(touchPoints);
   }
   mks->SendUnityTouchEvent(touchEvent);

   // Wait for MKS to send response back to UI
   sleep(1);

   EXPECT_TRUE(mks->GetNotifyUIFromRMKS());

   dispatch_sync(dispatch_get_main_queue(), [mks]() { delete mks; });
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKSUnitTest::SetAllowGrabInUnity --
 *
 *    Test cui::mks::SetAllowGrabInUnity method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MKSUnitTest, DISABLED_SetAllowGrabInUnity)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();

   MKSUTMock *mks = new NiceMock<MKSUTMock>(vm);
   EXPECT_TRUE(mks != NULL);

   mks->SetVdpArgs(0, "broker", "connectionUserMode", 0, FALSE, 0, "fqdn", "httpProxy", 0, FALSE,
                   std::vector<uint8>(1, 0), 0, "sslCipherString", "sslCipherSuites", "", "");

   mks->Connect("ngp_client", "target1");

   mks->SetAllowGrabInUnity(TRUE);

   // Wait for MKS to send response back to UI
   sleep(1);

   EXPECT_TRUE(mks->GetNotifyUIFromRMKS());

   dispatch_sync(dispatch_get_main_queue(), [mks]() { delete mks; });
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKSUnitTest::GetAllowGrabInUnity --
 *
 *    Test cui::mks::GetAllowGrabInUnity method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MKSUnitTest, DISABLED_GetAllowGrabInUnity)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();

   MKSUTMock *mks = new NiceMock<MKSUTMock>(vm);
   EXPECT_TRUE(mks != NULL);

   mks->SetVdpArgs(0, "broker", "connectionUserMode", 0, FALSE, 0, "fqdn", "httpProxy", 0, FALSE,
                   std::vector<uint8>(1, 0), 0, "sslCipherString", "sslCipherSuites", "", "");

   mks->Connect("ngp_client", "target1");

   mks->SetAllowGrabInUnity(TRUE);

   // Wait for MKS to send response back to UI
   sleep(1);

   EXPECT_TRUE(mks->GetNotifyUIFromRMKS());

   /*
    * Verify value returned by GetAllowGrabInUnity() is same as set using
    * SetAllowGrabInUnity()
    */
   EXPECT_TRUE(mks->GetAllowGrabInUnity());

   dispatch_sync(dispatch_get_main_queue(), [mks]() { delete mks; });
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKSUnitTest::OnReconnectionWaitTimeout --
 *
 *    Test OnReconnectionWaitTimeout method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MKSUnitTest, TestOnReconnectionWaitTimeout)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();

   ViewControlUTMock *viewControl = new NiceMock<ViewControlUTMock>();
   EXPECT_TRUE(viewControl != NULL);

   MKSUTMock *mks = new NiceMock<MKSUTMock>(vm, viewControl);
   EXPECT_TRUE(mks != NULL);

   mks->SetConnectionState(csPending);
   EXPECT_CALL(*viewControl, SetVDPReconnectAttemptCount(_)).Times(1);

   mks->OnReconnectionWaitTimeout();

   testing::Mock::VerifyAndClearExpectations(viewControl);

   delete mks;
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKSUnitTest::SetAllowDragMotionUngrab --
 *
 *    Test SetAllowDragMotionUngrab method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MKSUnitTest, TestSetAllowDragMotionUngrab)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();

   ViewControlUTMock *viewControl = new NiceMock<ViewControlUTMock>();
   EXPECT_TRUE(viewControl != NULL);

   MKSUTMock *mks = new NiceMock<MKSUTMock>(vm, viewControl);
   EXPECT_TRUE(mks != NULL);

   EXPECT_CALL(*viewControl, SetAllowButtonDownMotionUngrab(_)).Times(1);

   mks->SetAllowDragMotionUngrab(true);

   testing::Mock::VerifyAndClearExpectations(viewControl);

   delete mks;
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKSUnitTest::SetMouseInMKSWindow --
 *
 *    Test SetMouseInMKSWindow method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MKSUnitTest, TestSetMouseInMKSWindow)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();

   ViewControlUTMock *viewControl = new NiceMock<ViewControlUTMock>();
   EXPECT_TRUE(viewControl != NULL);

   MKSUTMock *mks = new NiceMock<MKSUTMock>(vm, viewControl);
   EXPECT_TRUE(mks != NULL);

   EXPECT_CALL(*viewControl, SetMouseInMKSWindow(_)).Times(1);

   mks->SetMouseInMKSWindow(true);

   testing::Mock::VerifyAndClearExpectations(viewControl);

   delete mks;
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKSUnitTest::SetAllowDragMotionUngrab --
 *
 *    Test cui::mks::SetAllowDragMotionUngrab method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MKSUnitTest, DISABLED_SetAllowDragMotionUngrab)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();

   MKSUTMock *mks = new NiceMock<MKSUTMock>(vm);
   EXPECT_TRUE(mks != NULL);

   mks->SetVdpArgs(0, "broker", "connectionUserMode", 0, FALSE, 0, "fqdn", "httpProxy", 0, FALSE,
                   std::vector<uint8>(1, 0), 0, "sslCipherString", "sslCipherSuites", "", "");

   mks->Connect("ngp_client", "target1");

   mks->SetAllowDragMotionUngrab(TRUE);

   // Wait for MKS to send response back to UI
   sleep(1);

   EXPECT_TRUE(mks->GetNotifyUIFromRMKS());

   dispatch_sync(dispatch_get_main_queue(), [mks]() { delete mks; });
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKSUnitTest::SetHostShortcutsAreEnabled --
 *
 *    Test cui::mks::SetHostShortcutsAreEnabled method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MKSUnitTest, DISABLED_SetHostShortcutsAreEnabled)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();

   MKSUTMock *mks = new NiceMock<MKSUTMock>(vm);
   EXPECT_TRUE(mks != NULL);

   mks->SetVdpArgs(0, "broker", "connectionUserMode", 0, FALSE, 0, "fqdn", "httpProxy", 0, FALSE,
                   std::vector<uint8>(1, 0), 0, "sslCipherString", "sslCipherSuites", "", "");

   mks->Connect("ngp_client", "target1");

   mks->SetHostShortcutsAreEnabled(TRUE);

   // Wait for MKS to send response back to UI
   sleep(1);

   EXPECT_TRUE(mks->GetNotifyUIFromRMKS());

   dispatch_sync(dispatch_get_main_queue(), [mks]() { delete mks; });
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKSUnitTest::GetHostShortcutsAreEnabled --
 *
 *    Test cui::mks::GetHostShortcutsAreEnabled method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MKSUnitTest, DISABLED_GetHostShortcutsAreEnabled)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();

   MKSUTMock *mks = new NiceMock<MKSUTMock>(vm);
   EXPECT_TRUE(mks != NULL);

   mks->SetVdpArgs(0, "broker", "connectionUserMode", 0, FALSE, 0, "fqdn", "httpProxy", 0, FALSE,
                   std::vector<uint8>(1, 0), 0, "sslCipherString", "sslCipherSuites", "", "");

   mks->Connect("ngp_client", "target1");

   mks->SetHostShortcutsAreEnabled(TRUE);

   // Wait for MKS to send response back to UI
   sleep(1);

   EXPECT_TRUE(mks->GetNotifyUIFromRMKS());

   /*
    * Verify value returned by GetHostShortcutsAreEnabled() is same as set
    * using SetHostShortcutsAreEnabled()
    */
   EXPECT_TRUE(mks->GetHostShortcutsAreEnabled());

   dispatch_sync(dispatch_get_main_queue(), [mks]() { delete mks; });
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKSUnitTest::SetKeyBindings --
 *
 *    Test cui::mks::SetKeyBindings method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MKSUnitTest, DISABLED_SetKeyBindings)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();

   MKSUTMock *mks = new NiceMock<MKSUTMock>(vm);
   EXPECT_TRUE(mks != NULL);

   mks->SetVdpArgs(0, "broker", "connectionUserMode", 0, FALSE, 0, "fqdn", "httpProxy", 0, FALSE,
                   std::vector<uint8>(1, 0), 0, "sslCipherString", "sslCipherSuites", "", "");

   mks->Connect("ngp_client", "target1");

   std::vector<cui::MKS::KeyBinding> bindings;
   cui::MKS::KeyBinding binding = {};
   bindings.push_back(binding);

   mks->SetKeyBindings(bindings);

   // Wait for MKS to send response back to UI
   sleep(1);

   EXPECT_TRUE(mks->GetNotifyUIFromRMKS());

   dispatch_sync(dispatch_get_main_queue(), [mks]() { delete mks; });
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKSUnitTest::SetOneToOneKeyBindings --
 *
 *    Test cui::mks::SetOneToOneKeyBindings method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MKSUnitTest, DISABLED_SetOneToOneKeyBindings)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();

   MKSUTMock *mks = new NiceMock<MKSUTMock>(vm);
   EXPECT_TRUE(mks != NULL);

   mks->SetVdpArgs(0, "broker", "connectionUserMode", 0, FALSE, 0, "fqdn", "httpProxy", 0, FALSE,
                   std::vector<uint8>(1, 0), 0, "sslCipherString", "sslCipherSuites", "", "");

   mks->Connect("ngp_client", "target1");

   std::vector<cui::MKS::OneToOneKeyBinding> bindings;
   cui::MKS::OneToOneKeyBinding binding = {};
   bindings.push_back(binding);

   mks->SetOneToOneKeyBindings(bindings);

   // Wait for MKS to send response back to UI
   sleep(1);

   EXPECT_TRUE(mks->GetNotifyUIFromRMKS());

   dispatch_sync(dispatch_get_main_queue(), [mks]() { delete mks; });
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKSUnitTest::SendUnicodeCodePoint --
 *
 *    Test cui::mks::SendUnicodeCodePoint method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MKSUnitTest, DISABLED_SendUnicodeCodePoint)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();

   MKSUTMock *mks = new NiceMock<MKSUTMock>(vm);
   EXPECT_TRUE(mks != NULL);

   mks->SetVdpArgs(0, "broker", "connectionUserMode", 0, FALSE, 0, "fqdn", "httpProxy", 0, FALSE,
                   std::vector<uint8>(1, 0), 0, "sslCipherString", "sslCipherSuites", "", "");

   mks->Connect("ngp_client", "target1");

   mks->SendUnicodeCodePoint(0, 230724);

   // Wait for MKS to send response back to UI
   sleep(1);

   EXPECT_TRUE(mks->GetNotifyUIFromRMKS());

   dispatch_sync(dispatch_get_main_queue(), [mks]() { delete mks; });
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKSUnitTest::SetEatKeys --
 *
 *    Test cui::mks::SetEatKeys method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MKSUnitTest, DISABLED_SetEatKeys)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();

   MKSUTMock *mks = new NiceMock<MKSUTMock>(vm);
   EXPECT_TRUE(mks != NULL);

   mks->SetVdpArgs(0, "broker", "connectionUserMode", 0, FALSE, 0, "fqdn", "httpProxy", 0, FALSE,
                   std::vector<uint8>(1, 0), 0, "sslCipherString", "sslCipherSuites", "", "");

   mks->Connect("ngp_client", "target1");

   mks->SetEatKeys(TRUE, FALSE, TRUE, FALSE, TRUE, FALSE, TRUE, FALSE);

   // Wait for MKS to send response back to UI
   sleep(1);

   EXPECT_TRUE(mks->GetNotifyUIFromRMKS());

   dispatch_sync(dispatch_get_main_queue(), [mks]() { delete mks; });
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKSUnitTest::SetMouseInMKSWindow --
 *
 *    Test cui::mks::SetMouseInMKSWindow method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MKSUnitTest, DISABLED_SetMouseInMKSWindow)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();

   MKSUTMock *mks = new NiceMock<MKSUTMock>(vm);
   EXPECT_TRUE(mks != NULL);

   mks->SetVdpArgs(0, "broker", "connectionUserMode", 0, FALSE, 0, "fqdn", "httpProxy", 0, FALSE,
                   std::vector<uint8>(1, 0), 0, "sslCipherString", "sslCipherSuites", "", "");

   mks->Connect("ngp_client", "target1");

   mks->SetMouseInMKSWindow(TRUE);

   // Wait for MKS to send response back to UI
   sleep(1);

   EXPECT_TRUE(mks->GetNotifyUIFromRMKS());

   dispatch_sync(dispatch_get_main_queue(), [mks]() { delete mks; });
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKSUnitTest::SetCursorGrabAllowed --
 *
 *    Test cui::mks::SetCursorGrabAllowed method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MKSUnitTest, DISABLED_SetCursorGrabAllowed)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();

   MKSUTMock *mks = new NiceMock<MKSUTMock>(vm);
   EXPECT_TRUE(mks != NULL);

   mks->SetVdpArgs(0, "broker", "connectionUserMode", 0, FALSE, 0, "fqdn", "httpProxy", 0, FALSE,
                   std::vector<uint8>(1, 0), 0, "sslCipherString", "sslCipherSuites", "", "");

   mks->Connect("ngp_client", "target1");

   mks->SetCursorGrabAllowed(TRUE);

   // Wait for MKS to send response back to UI
   sleep(1);

   EXPECT_TRUE(mks->GetNotifyUIFromRMKS());

   dispatch_sync(dispatch_get_main_queue(), [mks]() { delete mks; });
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKSUnitTest::GetHotKeyPrefix --
 *
 *     Test cui::mks::GetHotKeyPrefix method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MKSUnitTest, DISABLED_GetHotKeyPrefix)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();

   MKSUTMock *mks = new NiceMock<MKSUTMock>(vm);
   EXPECT_TRUE(mks != NULL);

   mks->SetVdpArgs(0, "broker", "connectionUserMode", 0, FALSE, 0, "fqdn", "httpProxy", 0, FALSE,
                   std::vector<uint8>(1, 0), 0, "sslCipherString", "sslCipherSuites", "", "");

   mks->Connect("ngp_client", "target1");

   std::string funcName = "ViewControlMgrSetHotkeyPrefix";
   mks->TriggerFuncInRMKS(funcName);

   // Wait for MKS to send response back to UI
   sleep(1);

   utf::string command = "ControlC";
   EXPECT_EQ(mks->GetHotKeyPrefix(), command);

   dispatch_sync(dispatch_get_main_queue(), [mks]() { delete mks; });
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKSUnitTest::ReleaseAllKeys --
 *
 *    Test cui::mks::ReleaseAllKeys method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MKSUnitTest, DISABLED_ReleaseAllKeys)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();

   MKSUTMock *mks = new NiceMock<MKSUTMock>(vm);
   EXPECT_TRUE(mks != NULL);

   mks->SetVdpArgs(0, "broker", "connectionUserMode", 0, FALSE, 0, "fqdn", "httpProxy", 0, FALSE,
                   std::vector<uint8>(1, 0), 0, "sslCipherString", "sslCipherSuites", "", "");

   mks->Connect("ngp_client", "target1");

   mks->ReleaseAllKeys();

   // Wait for MKS to send response back to UI
   sleep(1);

   EXPECT_TRUE(mks->GetNotifyUIFromRMKS());

   dispatch_sync(dispatch_get_main_queue(), [mks]() { delete mks; });
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKSUnitTest::GetAttemptDnDUngrab--
 *
 *    Test cui::mks::GetAttemptDnDUngrab metho
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MKSUnitTest, DISABLED_GetAttemptDnDUngrab)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();

   MKSUTMock *mks = new NiceMock<MKSUTMock>(vm);
   EXPECT_TRUE(mks != NULL);

   mks->SetVdpArgs(0, "broker", "connectionUserMode", 0, FALSE, 0, "fqdn", "httpProxy", 0, FALSE,
                   std::vector<uint8>(1, 0), 0, "sslCipherString", "sslCipherSuites", "", "");

   mks->Connect("ngp_client", "target1");

   std::string funcName = "ViewControlMgr_NotifyGHDnDUngrabAttempt";
   mks->TriggerFuncInRMKS(funcName);

   // Wait for MKS to send response back to UI
   sleep(1);

   int x = 0, y = 0;
   mks->GetAttemptDnDUngrab(x, y);

   EXPECT_EQ(x, 1);
   EXPECT_EQ(y, 2);

   dispatch_sync(dispatch_get_main_queue(), [mks]() { delete mks; });
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKSUnitTest::DropDelayedModifierKeys --
 *
 *    Test cui::mks::DropDelayedModifierKeys method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MKSUnitTest, DISABLED_DropDelayedModifierKeys)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();

   MKSUTMock *mks = new NiceMock<MKSUTMock>(vm);
   EXPECT_TRUE(mks != NULL);

   mks->SetVdpArgs(0, "broker", "connectionUserMode", 0, FALSE, 0, "fqdn", "httpProxy", 0, FALSE,
                   std::vector<uint8>(1, 0), 0, "sslCipherString", "sslCipherSuites", "", "");

   mks->Connect("ngp_client", "target1");

   mks->DropDelayedModifierKeys();

   // Wait for MKS to send response back to UI
   sleep(1);

   EXPECT_TRUE(mks->GetNotifyUIFromRMKS());

   dispatch_sync(dispatch_get_main_queue(), [mks]() { delete mks; });
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKSUnitTest::GetGHDnDUngrab--
 *
 *    Test cui::mks::GetGHDnDUngrab method.
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MKSUnitTest, DISABLED_GetGHDnDUngrab)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();

   MKSUTMock *mks = new NiceMock<MKSUTMock>(vm);
   EXPECT_TRUE(mks != NULL);

   mks->SetVdpArgs(0, "broker", "connectionUserMode", 0, FALSE, 0, "fqdn", "httpProxy", 0, FALSE,
                   std::vector<uint8>(1, 0), 0, "sslCipherString", "sslCipherSuites", "", "");

   mks->Connect("ngp_client", "target1");

   std::string funcName = "ViewControlMgr_NotifyGHDnDUngrab";
   mks->TriggerFuncInRMKS(funcName);

   // Wait for MKS to send response back to UI
   sleep(1);

   EXPECT_TRUE(mks->GetGHDnDUngrab());

   dispatch_sync(dispatch_get_main_queue(), [mks]() { delete mks; });
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKSUnitTest::EnableSound--
 *
 *    Test cui::mks::EnableSound method.
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MKSUnitTest, DISABLED_EnableSound)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();

   MKSUTMock *mks = new NiceMock<MKSUTMock>(vm);
   EXPECT_TRUE(mks != NULL);

   mks->SetVdpArgs(0, "broker", "connectionUserMode", 0, FALSE, 0, "fqdn", "httpProxy", 0, FALSE,
                   std::vector<uint8>(1, 0), 0, "sslCipherString", "sslCipherSuites", "", "");

   mks->Connect("ngp_client", "target1");

   mks->EnableSound(TRUE);

   // Wait for MKS to send response back to UI
   sleep(1);

   EXPECT_TRUE(mks->GetNotifyUIFromRMKS());

   dispatch_sync(dispatch_get_main_queue(), [mks]() { delete mks; });
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKSUnitTest::SetHostShortcutsAreEnabled --
 *
 *    Test SetHostShortcutsAreEnabled method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MKSUnitTest, TestSetHostShortcutsAreEnabled)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();

   ViewControlUTMock *viewControl = new NiceMock<ViewControlUTMock>();
   EXPECT_TRUE(viewControl != NULL);

   MKSUTMock *mks = new NiceMock<MKSUTMock>(vm, viewControl);
   EXPECT_TRUE(mks != NULL);

   EXPECT_CALL(*viewControl, SetPreference(_, _)).Times(1);
   EXPECT_CALL(*mks, GetHostShortcutsAreEnabled()).Times(1);

   mks->SetHostShortcutsAreEnabled(true);

   testing::Mock::VerifyAndClearExpectations(viewControl);

   delete mks;
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKSUnitTest::EnableSound--
 *
 *    Test EnableSound method.
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MKSUnitTest, TestEnableSound)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();

   ViewControlUTMock *viewControl = new NiceMock<ViewControlUTMock>();
   EXPECT_TRUE(viewControl != NULL);

   MKSUTMock *mks = new NiceMock<MKSUTMock>(vm, viewControl);
   EXPECT_TRUE(mks != NULL);

   EXPECT_CALL(*viewControl, EnableSound(_)).Times(1);

   mks->EnableSound(true);

   testing::Mock::VerifyAndClearExpectations(viewControl);

   delete mks;
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKSUnitTest::SetCursorGrabAllowed --
 *
 *    Test SetCursorGrabAllowed method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MKSUnitTest, TestSetCursorGrabAllowed)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();

   ViewControlUTMock *viewControl = new NiceMock<ViewControlUTMock>();
   EXPECT_TRUE(viewControl != NULL);

   MKSUTMock *mks = new NiceMock<MKSUTMock>(vm, viewControl);
   EXPECT_TRUE(mks != NULL);

   EXPECT_CALL(*viewControl, SetCursorGrabAllowed(_)).Times(1);

   mks->SetCursorGrabAllowed(true);

   testing::Mock::VerifyAndClearExpectations(viewControl);

   delete mks;
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKSUnitTest::SendUnicodeCodePoint --
 *
 *    Test SendUnicodeCodePoint method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MKSUnitTest, TestSendUnicodeCodePoint)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();

   ViewControlUTMock *viewControl = new NiceMock<ViewControlUTMock>();
   EXPECT_TRUE(viewControl != NULL);

   MKSUTMock *mks = new NiceMock<MKSUTMock>(vm, viewControl);
   EXPECT_TRUE(mks != NULL);

   EXPECT_CALL(*viewControl, SendUnicodeCodePoint(_, _)).Times(1);

   mks->SendUnicodeCodePoint(0, 230724);

   testing::Mock::VerifyAndClearExpectations(viewControl);

   delete mks;
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKSUnitTest::SetKeyBindings --
 *
 *    Test cui::mks::SetKeyBindings method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MKSUnitTest, TestSetKeyBindings)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();

   ViewControlUTMock *viewControl = new NiceMock<ViewControlUTMock>();
   EXPECT_TRUE(viewControl != NULL);

   MKSUTMock *mks = new NiceMock<MKSUTMock>(vm, viewControl);
   EXPECT_TRUE(mks != NULL);

   EXPECT_CALL(*viewControl, SetInputMappings(_, _)).Times(1);

   std::vector<cui::MKS::KeyBinding> bindings;
   cui::MKS::KeyBinding binding = {};
   bindings.push_back(binding);

   mks->SetKeyBindings(bindings);

   testing::Mock::VerifyAndClearExpectations(viewControl);

   delete mks;
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKSUnitTest::SetOneToOneKeyBindings --
 *
 *    Test SetOneToOneKeyBindings method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MKSUnitTest, TestSetOneToOneKeyBindings)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();

   ViewControlUTMock *viewControl = new NiceMock<ViewControlUTMock>();
   EXPECT_TRUE(viewControl != NULL);

   MKSUTMock *mks = new NiceMock<MKSUTMock>(vm, viewControl);
   EXPECT_TRUE(mks != NULL);

   EXPECT_CALL(*viewControl, SetInputMappings(_, _)).Times(1);

   std::vector<cui::MKS::OneToOneKeyBinding> bindings;
   cui::MKS::OneToOneKeyBinding binding = {};
   bindings.push_back(binding);

   mks->SetOneToOneKeyBindings(bindings);

   testing::Mock::VerifyAndClearExpectations(viewControl);

   delete mks;
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKSUnitTest::SetEatKeys --
 *
 *    Test SetEatKeys method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MKSUnitTest, TestSetEatKeys)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();

   ViewControlUTMock *viewControl = new NiceMock<ViewControlUTMock>();
   EXPECT_TRUE(viewControl != NULL);

   MKSUTMock *mks = new NiceMock<MKSUTMock>(vm, viewControl);
   EXPECT_TRUE(mks != NULL);

   EXPECT_CALL(*viewControl, SetEatKeys(_, _, _, _, _, _, _, _)).Times(1);

   mks->SetEatKeys(TRUE, FALSE, TRUE, FALSE, TRUE, FALSE, TRUE, FALSE);

   testing::Mock::VerifyAndClearExpectations(viewControl);

   delete mks;
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKSUnitTest::ReleaseAllKeys --
 *
 *    Test ReleaseAllKeys method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MKSUnitTest, TestReleaseAllKeys)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();

   ViewControlUTMock *viewControl = new NiceMock<ViewControlUTMock>();
   EXPECT_TRUE(viewControl != NULL);

   MKSUTMock *mks = new NiceMock<MKSUTMock>(vm, viewControl);
   EXPECT_TRUE(mks != NULL);

   EXPECT_CALL(*viewControl, ReleaseAllKeys(_, _)).Times(1);

   mks->ReleaseAllKeys();

   testing::Mock::VerifyAndClearExpectations(viewControl);

   delete mks;
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKSUnitTest::DropDelayedModifierKeys --
 *
 *    Test DropDelayedModifierKeys method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MKSUnitTest, TestDropDelayedModifierKeys)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();

   ViewControlUTMock *viewControl = new NiceMock<ViewControlUTMock>();
   EXPECT_TRUE(viewControl != NULL);

   MKSUTMock *mks = new NiceMock<MKSUTMock>(vm, viewControl);
   EXPECT_TRUE(mks != NULL);

   EXPECT_CALL(*viewControl, DropDelayedModKeys(_, _)).Times(1);

   mks->DropDelayedModifierKeys();

   testing::Mock::VerifyAndClearExpectations(viewControl);

   delete mks;
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKSUnitTest::Reconnect --
 *
 *    Test Reconnect method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MKSUnitTest, Reconnect)
{
   utf::string target("20.252.62.178:8443;{\"a\":\"N95P18beYZV4ntjFMZpR85AMGsHTSuHu5d8NTgn7\", \
      \"r\":\"1EEAD33D-5CA6-488F-BCE1-0371F253966F\",\"thumbprint\":\"40 C0 23 E3 94 3D BD 98 48 34 7A BB F7 DC 0C 0D BE E7 60 DC\", \
      \"thumbprint256\":\"9F 02 8E 60 A1 24 1A DA AA D7 36 DE 32 2B E1 28 07 40 FD 65 93 EE 83 5B 14 D7 DD 73 A9 F4 4B 2F\"}");
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();
   MKSUTMock *mks = new NiceMock<MKSUTMock>(vm);
   EXPECT_TRUE(mks != NULL);

   VMOCK(&mksctrl::ViewControlClient::SendProtocolRedirectReconnectReq)
      .WillOnce([](const char *vdpTarget) { return; });

   bool retValue = mks->Reconnect(target);
   EXPECT_TRUE(retValue);

   VMOCK(&MKS::GetMKSControlClient).WillOnce((mksctrl::MKSControlClientBase *)NULL);
   VMOCK(&mksctrl::ViewControlClient::SendProtocolRedirectReconnectReq).Times(0);
   retValue = mks->Reconnect(target);
   EXPECT_FALSE(retValue);

   delete mks;
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKSUnitTest::OnConnectionStateChanged --
 *
 *    Test OnConnectionStateChanged method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MKSUnitTest, OnConnectionStateChanged)
{
   auto vm = new NiceMock<VMUTMock>("target1");
   vm->Init();
   MKSUTMock *mks = new NiceMock<MKSUTMock>(vm);
   EXPECT_TRUE(mks != NULL);

   mks->SetProtocol(MKS_DISPLAYPROTOCOL_VNC);
   mks->SetProperty("allowBlastDynamicPath", "TRUE");
   VMOCK(&MKS::GetConnectionStateReason).Will(VDPCONNECT_NETWORK_FAILURE_WITH_CONTINUITY);
#if defined(__linux__) || defined(_WIN32)
   VMOCK(&MKS::RequestProtocolRedirectReconnect).Times(1);
   mks->OnConnectionStateChanged();
#endif

   VMOCK(&MKS::RequestProtocolRedirectReconnect).Times(0);
   mks->OnConnectionStateChanged();

   delete mks;
   delete vm;
}
