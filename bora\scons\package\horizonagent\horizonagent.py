# Copyright (c) Omnissa, LLC. All rights reserved.
# This product is protected by copyright and intellectual property laws in the
# United States and other countries as well as by international treaties.
# -- Omnissa Restricted

"""horizonagent
Packaging script for horizonagent
"""

import os
import glob
import sys
import SCons
import vmware
import vtools.common
from vmware.visualstudio import generateSolution, getSolutionOpenAction
from SCons.Script import Dir, Execute, File, SConscript  # For flake8

log = vmware.GetLogger("packaging")

CODEQL_SCAN = vmware.LocalOpts.GetBool("CODEQL_SCAN", False)
CODE_COV = vmware.LocalOpts.GetBool("ENABLE_CODE_COV", False)

RDETEST_NAME = "rdeServerComponentTest"

printerDrvDir = os.path.join(
    vmware.DirAbsPath(vmware.BuildRoot()), vmware.BuildType(), "printerMsmDrv"
)
printTestDir = os.path.join(
    vmware.DirAbsPath(vmware.BuildRoot()), vmware.BuildType(), "printRedirTest"
)

toolsInstDir = vmware.LookupNode("toolsinstutil", host="win64")[0].dir.abspath


#
# Following are a number of node lists that package components to
# different locations.  The locations are:-
#
#   - stageNodesCommon
#       Components needed by both agent and server installers.
#
#   - stageNodesInstaller, stageNodesInstallerx86, stageNodesInstallerx64
#       Components needed by agent installer only.
#
#   - stageNodesCompcache, stageNodesCompcachex86 (only)
#       Components put into the gobuild compcache that are consumed by other
#       products.
#
#   - stageNodesPublish
#       Components that only appear on the Deliverables page.
#
#   - publishNodes
#       A list of components that need to be published to the Deliverables
#       page.  Components in this list MUST also appear in one of the node
#       lists above otherwise NOTHING is published.
#
#   - nonStagedVisualStudioProjectNodes
#       A list of components which are not staged/publish but should be
#       include when generating Visual Studio solution (e.g. static libraries)
#
#   - driverNodes
#       A list of drivers with accompanying inf files.  Driver is signed and a
#       cat file generated and signed from inf.  Resulting files are staged to
#       placed into installerStage path.
#

# List of files needed both by the horizonagent and horizonserver installers
stageNodesCommon = [
    "agentjmsObfuscated",
    "agentjms-testObfuscated",
    "commonutilsObfuscated",
    "events-clientObfuscated",
    "events-commonObfuscated",
    "jmswrapperObfuscated",
    "messagesecurityObfuscated",
    "mfwjObfuscated",
    "orchestratorjObfuscated",
    "securitymanagerObfuscated",
    "sslObfuscated",
    "timingprofiler-commonObfuscated",
    "updateBldNumRes",
    "winauthObfuscated",
    "ws_adminDLL",
    "ws_adminLIB",
    "ws_crl",
    "ws_dct",
    "ws_appenderObfuscated",
    "ws_appendersupport",
    "ws_configmgr",
    "ws_consolesupport",
    "ws_diag",
    "ws_dllhost",
    "ws_filesystem",
    "ws_java_bridgeDLL",
    "ws_java_nativeNODEP",
    "ws_java_service",
    "ws_java_starter",
    "ws_javaview",
    "ws_klog",
    "ws_propertiessupport",
    "ws_perfmon",
    "ws_propertiesObfuscated",
    "ws_scripts",
    "ws_vmx",
    "ws_winauthDLL",
    "wsnm",
    "wsnm_certauthority",
    "wsnm_certenroll",
    "ws_lcm",
]

# List of signed files that are needed by horizonagent and its consumers.
signedCommon = {
    "wsauth",
}

# List of files needed by the horizonagent installer
stageNodesInstaller = [
    # 'generic' host deliverables produced by Java.
    "commons-lang-dep",
    "commons-net-dep",
    "commons-validator-dep",
    "guava-dep",
    "jdom-dep",
    "dom4j-dep",
    "jms-dep",
    "json-dep",
    "MessageFrameWork",
    "swiftmq-dep",
    "xercesImpl-dep",
    "xml-apis-dep",
    # Binaries where we need both x86 and x64.
    "vdpservice",
    "vdpservicepriv",
]

# List of signed files that are needed by horizonagent installer
signedInstaller = {
    "certStoreIntercept",
    "hznaudioendpoint",
    "hznsci",
    "wsksp",
    "whfbWsksp",
}

# List of frozen binaries that are needed by the horizonagent installer.
frozenInstaller = {
    "ksmNotifier",
}

# List of signed files supported on ARM64
signedModulesARM64 = {
    "hznsci",
    "wsauth",
}

# List of agent installer nodes that compile only on x86
stageNodesInstallerx86 = [
    # The x64 versions of the following are already staged in
    # bora/scons/package/appblastlibs/appblast.py so we only need to stage
    # the x86 versions.
    "resolutionSetLib",
    "tsmmrServer",
    "tsmmrServerDShow",
]

# List of agent installer nodes that compile only on x64
stageNodesInstallerx64 = [
    "appTap",
    "cdsClient",
    "cdsClientTest",
    "daResources_CHS",
    "daResources_CHT",
    "daResources_DEU",
    "daResources_ENU",
    "daResources_ESP",
    "daResources_FRA",
    "daResources_JPN",
    "daResources_KOR",
    "horizon-updatetool",
    "html5mmrNativeMessagingHost",
    "html5mmrServer",
    "hzDCT",
    "hzMonApi",
    "hzMonService",
    "ipclibRpc-dynamic-md",
    "libcds",
    "mfw_java",
    "mksvchanserver",
    "omnissa-ic-ga",
    "omnissa-ic-ga-unittest",
    "omnissa-ic-nga",
    "printredir",
    "prServerApi",
    "prvdpplugin",
    "rdsManager",
    "rtavPlugin",
    "smProcDump",
    "teamsHelper",
    "tsdrvdisvc",
    "tsmmrServer",
    "tsmmrServerDShow",
    "usbRedirectionServer",
    "vdpservice_wsnmplugin",
    "vhublib",
    "vmprereq-updatetool",
    "hznusmlib",
    "whfbRedirection",
    "ws_applaunchmgr",
    "ws_daas",
    "ws_updatemgr",
    "ws_usbstor",
    "ws_vdpvhub",
    "ws_vhub",
    "wscredf",
    "wslogonscriptlauncher",
    "wsnm_certlogon",
    "wsnm_credcache",
    "wsnm_desktop",
    "wsnm_helpdesk",
    "wsnm_jms",
    "wsnm_jmsbridge",
    "wsnm_mqtt",
    "wsnm_psgc",
    "wsnm_scredir",
    "wsnm_xmlapi",
    "wssm",
    "wssm_de_DE_407",
    "wssm_desktop",
    "wssm_en_us_409",
    "wssm_es_ES_40A",
    "wssm_fr_FR_40C",
    "wssm_helpdesk",
    "wssm_ja_JP_411",
    "wssm_ko_KR_412",
    "wssm_zh_CN_804",
    "wssm_zh_TW_404",
    "vmlm",
]

stageNodesInstallerx64 = stageNodesInstallerx64

# List of agent installer nodes that compile only on ARM64
stageNodesInstallerARM64 = [
    # stage only arm64 version here
]

# A list of components which are not staged/publish but should be
# include when generating Visual Studio solution (e.g. static libraries).
# Node in this list should have a '[node_name]-build' alias
nonStagedVisualStudioProjectNodes = [
    "authUtil-static-md",
    "authUtil-static-mt",
    "awsBucketClient",
    "azureBlobTest",
    "cata_fwPlugin",
    "cata_fwService",
    "cata_fwSM",
    "cata_fwTests",
    "certificateUtility",
    "daCommonUtil-static",
    "daSystemUtil-static",
    "directoryWatcherTest",
    "enrollment_test",
    "etlmapi_mock",
    "hashTests",
    "hzaprepTest",
    "hzMonApiTestClient",
    "hzMonHelper",
    "hznReflectTest",
    "installutillib-static-md",
    "installutillib-static-mt",
    "interceptUtil-static-md",
    "interceptUtil-static-mt",
    "ksmlib-static-md",
    "ksmlib-static-mt",
    "ksmlibTest-static",
    "ksplib-static",
    "libcds",
    "mqtt_client",
    "mockAV",
    "ntDllLoader-md",
    "ntDllLoader-mt",
    "nativeApiUtil-static-mt",
    "objectMap-static-md",
    "objectMap-static-mt",
    "objectMapJson-static-md",
    "objectMapJson-static-mt",
    "objectMapTest-static",
    "pcoipTestCtrl",
    "rdsTestLib",
    "rdsTestApp",
    "rdsTestAppInner",
    "scredirUtil-static",
    "secmgrTestRunner",
    "smctrl-static",
    "smctrlTest-static",
    "smlib-static-md",
    "smlib-static-mt",
    "smlibTest-static",
    "smtrack-static",
    "smtrackTest-static",
    "smsrv-static",
    "thirdPartyWrappers-static",
    "vvc-view-hub-unicode-md",
    "vvc-view-hub-utf8-md",
    "vvc-view-hub2-unicode-md",
    "winCertTests",
    "winHttpTests",
    "ws_applaunchmgrTest",
    "ws_applaunchmgr_operationTest",
    "wsnm_common-static",
    "wsnm_commonTest",
    "wsnm_desktopTest",
    "wsnm_jms-test",
    "wssm_common",
    "wssm_desktopTest",
    "ws_lcmTest",
    "ws_updatemgrTest",
    "vmlibs-asyncsocket",
    "vmlibs-vncConnectionManager",
    "vncServer",
]

# A list of drivers with accompanying inf files.  Driver is signed and a
# cat file generated and signed from inf
driverNodes = ["hznusm"]

kernelDriverNodes = {
    "hznbus": [("", "x64")],
    "hznflstor": [
        ("Win10", "x64"),
        ("Win10", "arm64"),
    ],
    "omnksm": [("", "x64")],
    "omnsdr": [("Win10", "x64")],
    "hznufhid": [
        ("Win10", "x64"),
        ("Win10", "arm64"),
    ],
    "hznvhub": [
        ("Win10", "x64"),
        ("Win10", "arm64"),
    ],
    "hznvwebcam": [("Win8.1", "x64")],
    "hznvaudioin": [("Win8.1", "x64")],
    "hznregreflection": [("Win10", "x64")],
}

signedUriCabNodes = {
    "urlProtocolIntercept-signed": [
        (["win32", "win64", "win-arm64"], [".dll"]),
        "hznurlfilter",
    ],
    "uncRedirection-signed": [
        (["win32", "win64", "win-arm64"], [".dll"]),
        "uncRedirection",
    ],
}

# List of all node names that should have a project when the "devenv" command
# is run. Each node must register an alias '[node_name]-[host]-stage' for each
# supported build host that builds the node and optionally stages it into a
# common directory.
visualStudioProjectNodes = []

# List of nodes that need to be staged and consumed by other products in the
# gobuild compcache
stageNodesCompcache = [
    "adamInstUtil",
    "agentjms-javadoc",
    "agentjms-test-javadoc",
    "commonutils-javadoc",
    "es_diag",
    "events-client-javadoc",
    "events-common-javadoc",
    "jmswrapper-javadoc",
    "ksmlib-static-md",
    "ksmlib-static-mt",
    "messagesecurity-javadoc",
    "messagesecurity-tests",
    "mfwcom",
    "mfwgen",
    "orchestratorj-javadoc",
    "psgctest",
    "scredir_vchanclient",
    "tsdrvdisvc",
    "securitymanager-javadoc",
    "securitymanager-md",
    "securitymanager-md-ldap",
    "securitymanager-mt",
    "secmgr-node",
    "secmgr-net",
    "secmgr-lone",
    "securitymanager-tests",
    "secmgrtest",
    "ssl-javadoc",
    "timingprofiler-common-javadoc",
    "vdmadmin",
    "vdmexport",
    "vdmimport",
    "vdmPerfmon",
    "winauth-javadoc",
    "ws_appender-javadoc",
    "ws_cluskeymgr",
    "ws_onrampkeymgr",
    "ws_cpakeymgr",
    "ws_consolesupport_mfw",
    "ws_java_bridge-exe",
    "ws_ldap",
    "ws_properties-javadoc",
    "ws_winauthLIB",
]

# List of extra files that need to be staged in the compcache
# for the other products to be consumed.  It is a list of tuples
# with first entry of tuple as file name and second entry as
# destination within compcache/extras/ folder
stageExtraFilesInCompcache = [
    (
        "#bora/apps/horizonAgent/java/commonutils/src/main/resources/fips.properties.txt",
        "txtFiles/commonutils",
    ),
    (
        "#bora/apps/horizonAgent/java/messagesecurity/src/test/resources/MessageSecurityQueryTool.bat",
        "batFiles/messagesecurity/",
    ),
    (
        "#bora/apps/horizonAgent/apps/vdmadmin/list-checkedout-unentitled.xsl",
        "xsltFiles/vdmadmin",
    ),
    (
        "#bora/apps/horizonAgent/apps/vdmadmin/list-checkedout-unentitled_w.xsl",
        "xsltFiles/vdmadmin",
    ),
    (
        "#bora/apps/horizonAgent/apps/vdmadmin/unentitled-machines.xsl",
        "xsltFiles/vdmadmin",
    ),
    (
        "#bora/apps/horizonAgent/apps/vdmadmin/unentitled-machines_w.xsl",
        "xsltFiles/vdmadmin",
    ),
    (
        "#bora/apps/horizonAgent/apps/vdmadmin/unentitled-policies.xsl",
        "xsltFiles/vdmadmin",
    ),
    (
        "#bora/apps/horizonAgent/apps/vdmadmin/unentitled-policies_w.xsl",
        "xsltFiles/vdmadmin",
    ),
    ("#bora/apps/horizonAgent/lib/vdmPerfmon/vdmPerfmonDLL.ini", "iniFiles/vdmPerfmon"),
]

# List of extra files that need to be signed and staged in the compcache.
stageSignExtraFilesInCompcache = [
    "#bora/apps/horizonAgent/dct/TitanDCT.ps1",
    "#bora/apps/horizonAgent/lib/wsnm_certauthority/dspublish.ps1",
]

# List of header files that need to be staged in the compcache for the
# other products to be consumed
stageHeadersInCompcache = [
    # These header files are published for vdmtools in server branch
    "#bora/apps/horizonAgent/include/enableBitMaskOperators.h",
    "#bora/apps/horizonAgent/include/ksmIoDefs.h",
    "#bora/apps/horizonAgent/include/ksmlib.h",
    "#bora/apps/horizonAgent/include/securitymanager.h",
    "#bora/apps/horizonAgent/include/vdm_plugins.h",
    "#bora/apps/horizonAgent/include/ws_dct.h",
    "#bora/apps/horizonAgent/lib/ws_admin/vdmcrypto.h",
    "#bora/apps/horizonAgent/lib/vdmPerfmon/vdmPerfmonDLL.h",
    "#bora/apps/horizonAgent/lib/ws_admin/ws_admin.h",
    "#bora/apps/horizonAgent/lib/ws_winauth/ws_winauth.h",
]

# List of msm nodes that need to be staged and consumed by other products
stageInstallerNodesCompcache = [
    "crypto",
    "dct-msm",
    "openssl-msm",
    "psg-msm",
    "server-jre",
    # TODO Corretto is disabled until approved
    # "server-jre-corretto",
    "omnksm-msm",
    "regredirect-msm",
]


# This is the list of node names that should be published to the Deliverables
# page
stageNodesPublish = {
    "appStub",
    "appTap",
    "appTapLaunch",
    "appTapScan",
    "audiodevtapConfigTool",
    "awsBucketClient",
    "azureBlobTest",
    "azureDetectApp",
    "browserPluginIE",
    "daCommonUtilTest",
    "daSystemUtilTest",
    "directoryWatcherTest",
    "hzipc",
    "hznAgentDowngradeTool",
    "hzaprep",
    "hzaprepTest",
    "hzWebauthn",
    "ksmlibTestApp",
    "lacu_test",
    "mksvchanserver",
    "mqtt_client",
    "mockAV",
    "objectMapTestApp",
    "pcoip_mfw",
    "perfTracker",
    "printredir",
    "prmon",
    "prService",
    "prServerApi",
    "prvdpplugin",
    "rdeServer",
    "rdpvcbridge",
    "scannerRedirHorizonDS",
    "scannerRedirServicePlugin",
    "scannerRedirTray",
    "sdrserverutil",
    "sdrserviceplugin",
    "smctrlTestApp",
    "smlibTestApp",
    "smtrackTestApp",
    "tsdrvdisvc",
    "tsdr_test",
    "tsmmrServer",
    "tsmmrServerDShow",
    "urlNativeMessageHost",
    "urlProtocolLaunchHelper",
    "uncService",
    "unityShell",
    "usbRedirectionServer",
    "vchanStub",
    "vhublib",
    "omnrxgservice",
    "winHttpTests",
    "ws_applaunchmgrTest",
    "ws_applaunchmgr_operationTest",
    "wsauth_test",
    "wsksp_test",
    "wsnm_commonTest",
    "wsnm_desktopTest",
    "wsnm_jms-test",
    "wsnm_mqttTest",
    "wsnm_scredir",
    "wssm_desktopTest",
    "ws_usbstor",
    "ws_vdpvhub",
    "ws_vhub",
    "ws_lcmTest",
}

# List of installer publish nodes
# installer nodes are unique since their lookup needs
# packaged = True. Also they have arch mentioned to the
# node. So lets keep it separate from stageNodesPublish
stageInstallerNodesPublish = [
    "horizon-agent-msi",
    "ngvc-agent-msi",
    "horizon-updatetool-msi",
]

# publishNodes is list of nodes to be published to the Deliverables
# page (a.k.a publishDir).  This list is used by StageDeliverables to determine
# if a staged node should be copied to publishDir THEREFORE adding a node to
# this map alone WILL NOT result in that node being published.
#
# Nodes must also be added to one of the other node stage lists above that use
# StageDeliverables;
#       stageNodesCommon
#       stageNodesInstaller, stageNodesInstallerx86, stageNodesInstallerx64
#       stageNodesCompcache, stageInstallerNodesCompcache,
#       stageNodesPublish, stageInstallerNodesPublish
#
# This is a map which contains the node as the key and subdirectory under
# publish directory as the value.
# We publish files that are needed to be downloaded by developers or consumed
# separately by gobuild consumers. For example, horizonclient needs only few
# files and it can download only the files needed from publish directory.
# For rest of the consumers who want to reduce the download times, compcache
# can be used directly.
publishNodesMap = {
    "appTap": "tests",
    "appTapLaunch": "tests",
    "appTapScan": "tests",
    "audiodevtapConfigTool": "tests",
    "azureDetectApp": "tests",
    "horizon-agent-msi-x64": "agent-installer",
    "omnissa-ic-ga-unittest": "tests",
    "scredir_vchanclient": "shared-client",
    "smprocdump-msi-x64": "agent-installer",
    "ws_diag": "shared-client",
    "ws_dllhost": "shared-client",
    "ws_klog": "shared-client",
    "ws_winauthDLL": "shared-client",
    "wsauth": "shared-client",
    "wsauth_test": "tests",
    "wsksp_test": "tests",
    "wsnm": "shared-client",
    "daCommonUtilTest": "tests",
    "daSystemUtilTest": "tests",
    "ngvc-agent-msi-x64": "agent-installer",
    "horizon-updatetool-msi-x64": "agent-installer",
    "omnksm-msi-x64": "agent-installer",
    "regredirect-msi-x64": "agent-installer",
    "winHttpTests": "tests",
    "wsnm_scredir": "tests",
    "tsdr_test": "tests",
    "tsdrvdisvc": "tests",
}

# publishNodeHistory keeps a history of nodes published to Deliverables page
# in order to prevent duplicates.  Duplicates can happen if a node appears
# in multiple stageNodes lists.
publishNodeHistory = []

#
# These are nodes for appblast which are inside Blast.msm / Blast64.msm,
# this node list is used to include them in the debugging extras
#
appblastNodes = [
    "abctrl",
    "CustomAction",
    "VMBlastRec",
    "VMBlastS",
    "VMBlastW",
    "vvc-view-node",
    "vvc-view-node2",
    "wavedll",
]
nonStagedVisualStudioProjectNodes.extend(appblastNodes)

#
# These are nodes that zipped inside hzn-csso-es zip file
#
cssoesNodes = [
    "enrollment_test",
    "es_diag",
    "wsnm_certauthority",
    "wsnm_certenroll",
]

#
# These are nodes that are zipped up inside the hzn-cata-fw zip
#
cataFwNodes = [
    "cata_fwPlugin",
    "cata_fwService",
    "cata_fwSM",
    "cata_fwTests",
]

#
# These are nodes that are zipped up inside the hzn-rdsh-test-fw zip
#
rdshTestFwNodes = [
    "MockWinClient",
    "pcoipTestCtrl",
    "rdsTestApp",
    "rdsTestAppInner",
    "rdsTestLib",
    "smlibTestApp",
]
rdshTestFwNodes += cataFwNodes

#
# These are nodes that need to be put in a signed cab file ready for signing by
# Microsoft. Format is node name, hosts, file extensions to include.
# The nodes are registered below.
#
signedCabNodes = {
    # XXX TODO: Is win32 wsauth no longer used once client is 64-bit only?
    "certStoreIntercept-signed": (["win32", "win64"], [".dll"]),
    "wsauth-signed": (["win32", "win64", "win-arm64"], [".dll"]),
    "wsksp-signed": (["win32", "win64"], [".dll"]),
    "whfbWsksp-signed": (["win32", "win64"], [".dll"]),
    "hznsci-signed": (["win32", "win64", "win-arm64"], [".dll"]),
    "hznaudioendpoint-signed": (["win32", "win64"], [".dll"]),
}

artifactorySignedFileRoot = "horizon-cart-local/drivers/"


def addDepsToZipSources(env, names, zipSources):
    """
    Add all dependencies which are specied by names and their pdb to zip
    """
    deps = []
    for name in names:
        dep = env[name]
        if isinstance(dep, list):
            deps += dep
        else:
            deps.append(dep)

    for dep in deps:
        depAbsPath = File(dep).abspath
        zipSources.append(os.path.split(depAbsPath))
        pdb = os.path.splitext(depAbsPath)[0] + ".pdb"
        if os.path.exists(pdb):
            zipSources.append(os.path.split(pdb))


def StageDeliverables(stageEnv, name, host, hostPath, packaged=False, frozen=False):
    """Wrapper function for staging deliverables

    Wrapper for vmware.pkg.StageDeliverables which will also publishes nodes to
    the Deliverables page (a.k.a publishDir) when that node appears in the
    stageNodesPublish list.
    """
    global publishNodeHistory

    stagedNodes = []
    if frozen:
        if host != "win-arm64" or name in signedModulesARM64:
            frozenNodes = vmware.pkg.LookupDeliverableNodes("%s-frozen" % name, host)
            # Stage frozen files to the hostpath
            stagedNodes = stageEnv.Install(hostPath, frozenNodes)
    else:
        # Stage nodes to the given hostpath
        stagedNodes = vmware.pkg.StageDeliverables(
            stageEnv, name, host, hostPath, packaged=packaged
        )

    publishDir = vmware.ReleasePackagesDir()
    deliverables = []

    if publishDir is not None:
        # Staged nodes into publishDir (a.k.a Deliverables page)
        publishDirPP = vmware.PathPrefixer(publishDir)

        # Always publish build nodes of files that are Microsoft signed.
        # This is useful since we can download the unsigned files from official
        # builds for Microsoft signing
        if name in signedCommon or name in signedInstaller:
            microsoftUnsignedDir = Dir(publishDirPP / "microsoft-unsigned" / host)

            if frozen:
                # Stage the build nodes to deliverables page
                if host != "win-arm64" or name in signedModulesARM64:
                    deliverables += vmware.pkg.StageDeliverables(
                        stageEnv,
                        "%s-signed" % name,
                        host,
                        microsoftUnsignedDir.abspath,
                        packaged=packaged,
                        signBinaries=False,
                    )
            else:
                # The build nodes have been staged to the host path
                # So publish them from the host path to deliverables page
                deliverables += stageEnv.Install(microsoftUnsignedDir, stagedNodes)

        # For rest of the nodes that are not meant to be Microsoft signed,
        # stage them in the publish directory if they are listed
        # in the publishNodesMap
        if name in publishNodesMap:
            subdir = publishNodesMap[name]
            if subdir is None:
                destPublishDir = Dir(publishDirPP / host)
            else:
                destPublishDir = Dir(publishDirPP / subdir / host)

            for node in stagedNodes:
                dstPublishFile = destPublishDir.File(node.name).abspath
                if dstPublishFile in publishNodeHistory:
                    continue

                publishNodeHistory.append(dstPublishFile)
                deliverables += stageEnv.LinkCopy(dstPublishFile, node.abspath)

    return stagedNodes + deliverables


def StageDeliverablesIntoZip(stageEnv, nodes, host, hostPath, zipFile, files=None):
    """Stages deliverables into a zip

    Takes in all the nodes that needs to be zipped to the given zip file.
    Stages the node first by calling vmware.pkg.StageDeliverables which handles
    staging and digital signing. And then zips all the files in the given
    nodes to the given zip file.
    Optionally use files param to include other files
    """
    files = files or []
    zipSources = []
    for name in nodes:
        stageNodes = vmware.pkg.StageDeliverables(stageEnv, name, host, hostPath)
        for stageNode in stageNodes:
            zipSources.append((stageNode.dir.abspath, stageNode.name))
        for file in files:
            zipSources.append((file.dir.abspath, file.name))

    return stageEnv.Zip(zipFile, zipSources)


def StageInstallerDeliverables(
    installerStagePath,
    platformArch,
    stageEnv,
    name,
    host,
    packaged=False,
    frozen=False,
):
    """
    Stages deliverables into the specified installer source directory.
    """
    hostPath = vmware.DirAbsPath(os.path.join(installerStagePath, platformArch))

    return StageDeliverables(
        stageEnv, name, host, hostPath, packaged=packaged, frozen=frozen
    )


def stagePCoIPServerGPO(platformArch):
    """Stage PCoIP server GPO files to 'shared-client' publish folder"""
    nodes = []
    if platformArch in hostMapPublish:
        publishDir = vmware.ReleasePackagesDir()
        if publishDir is not None:
            publishDirPP = vmware.PathPrefixer(publishDir)
            gpoDest = Dir(publishDirPP / "shared-client" / platformArch / "GPO")
            gpoSrc = os.path.join(
                vmware.GetGobuildComponent("pcoip_soft_server"), "adm"
            )
            files = vmware.EnumerateSourceDir(gpoSrc)
            nodes += vmware.DirCopy(files, Dir(gpoSrc), Dir(gpoDest), stageEnv)
    return nodes


def stageCdsScripts(stageEnv):
    """
    Stage CDS scripts to publish folder
    """
    nodes = []
    srcCdsPyFiles = [
        "AgentCdsRepo.py",
        "AgentUpdateMeta.py",
        "AgentUpdateMetaData.py",
        "bulletins-en.xml",
    ]
    publishDir = vmware.ReleasePackagesDir()
    publishDirPP = vmware.PathPrefixer(publishDir)

    cdsPyFilesDest = Dir(publishDirPP / "cds-scripts")
    cdsPyFilesSrc = Dir("#bora/install/msi/horizon/agent")

    cdsScriptsDest = Dir(publishDirPP / "cds-scripts" / "scripts")
    cdsScriptsSrc = Dir("#bora/apps/cdsng/scripts")
    cdsScriptFiles = vmware.EnumerateSourceDir(cdsScriptsSrc)
    nodes += vmware.DirCopy(
        cdsScriptFiles, Dir(cdsScriptsSrc), cdsScriptsDest, stageEnv
    )

    for file in srcCdsPyFiles:
        srcFile = Dir(cdsPyFilesSrc).File(file)
        desFile = Dir(cdsPyFilesDest).File(file)
        nodes += stageEnv.FileCopy(desFile, srcFile)
    return nodes


def generateCDS(env):
    """
    Generate CDS-ready payload for horizon agent build
    """
    horizonExe = vmware.LookupNode("horizon-agent-x64-node", packaged=True)
    agentCdsRepoPy = Dir("#bora/install/msi/horizon/agent").File("AgentCdsRepo.py")
    cdsDir = os.path.join(publishDir, "cds")
    CDS_RESOURCES = os.path.join(vmware.DirAbsPath(vmware.BuildRoot()), "cds-resources")
    displayName = "Horizon Enterprise Agent"
    eulaFileLocation = Dir("#bora/install/msi/horizon/agent/cds-files").File(
        "eula.en.html"
    )
    descriptionFileLocation = Dir("#bora/install/msi/horizon/agent/cds-files").File(
        "description.en.html"
    )

    env.LoadTool("python")

    tarExe = env["TAR"]
    cdsngDir = Dir("#bora/apps/cdsng")

    substArgs = [
        ("@@DISPLAY_NAME@@", displayName),
        ("@@HZE_VERSION@@", vmware.ProductVersionNumber()),
    ]

    env.LoadTool("textsubst")
    newdescriptionFile = env.TextSubst(
        os.path.join(CDS_RESOURCES, "description.en.html"),
        descriptionFileLocation.abspath,
        TEXTSUBSTARGS=substArgs,
    )

    cdsAction = (
        '"$PYTHON" "${SOURCES[0]}" '
        f'-name="Horizon-Enterprise-Agent" '
        f'-displayName="{displayName}" '
        f"-version={vmware.ProductVersionNumber()} "
        f"-build={vmware.BuildNumber()} "
        f'-visibleto="horizon-enterprise-agent-update-manager",\
"1.0.0",{vmware.BuildNumber()} '
        '-descriptionFileLocation="${SOURCES[1]}" '
        f'-eulaFileLocation="{eulaFileLocation}" '
        '-amd64file="${SOURCES[2]}" '
        f'-amd64installargs="/s /v /qn REBOOT=ReallySuppress" '
        '-extraFiles="${SOURCES[2]}" '
        f"-takesInstallArgs=true "
        f'-tarExe="{tarExe}" '
        f'-cdsngDir="{cdsngDir}" '
        f'-publishdir="{Dir(publishDir)}"'
    )

    cdsAction = vmware.ActionWithDisplay(
        cdsAction, "Generating Horizon Agent CDS payload - $TARGET"
    )

    cdsCommand = env.Command(
        cdsDir,
        [agentCdsRepoPy, newdescriptionFile, horizonExe],
        [Mkdir(CDS_RESOURCES), Delete(cdsDir), cdsAction, Delete(CDS_RESOURCES)],
    )
    env.NoCache(cdsCommand)
    return cdsCommand


def generateMetadataConfig(env):
    """
    Generate metadata.json alongside agent exe
    horizon agent build
    """
    horizonExe = vmware.LookupNode("horizon-agent-x64-node", packaged=True)
    env.LoadTool("python")

    updatetoolVersion = env.ExtractMacro(
        "vm_product_versions.h", "HORIZON_UPDATETOOL_VERSION"
    )

    metadataScript = Dir("#bora/install/msi/horizon/agent").File(
        "AgentUpdateMetadata.py"
    )

    configAction = (
        "${SOURCES[0]} -B ${SOURCES[1]} "
        '--name="HZE-Agent" '
        f"--version={vmware.ProductVersionNumber()} "
        f"--build={vmware.BuildNumber()} "
        f"--updatetoolVersion={updatetoolVersion} "
        '--displayName="Horizon Agent Installer" '
        '--filename="${SOURCES[2]}" '
        "--outputfile=${TARGET.abspath}"
    )

    configAction = vmware.ActionWithDisplay(
        configAction, "Generating Horizon Agent metadata - $TARGET"
    )
    publishStagePath = Dir(publishDir)
    jsonName = "Omnissa-Horizon-Agent-x86_64-%s-%s-%s.exe-metadata.json" % (
        vmware.ProductVersionNumber("HorizonYYMM"),
        vmware.ProductVersionNumber(),
        vmware.BuildNumber(),
    )
    metadataFile = Dir(publishStagePath).File(jsonName)
    configCommand = env.Command(
        metadataFile, ["$PYTHON", metadataScript, horizonExe], configAction
    )
    env.NoCache(configCommand)
    return configCommand


def generateMspPatch(env, platformArch):
    """
    Generate a horizon agent patch (.msp) between this build and a previous
    horizon agent build
    """
    caymanWindowsInstallKitPP = vmware.PathPrefixer(
        vmware.GetGobuildComponent("windows-installkit")
    )
    caymanHorizonagentPP = vmware.PathPrefixer(
        vmware.GetGobuildComponent("horizonagent")
    )

    createPatchScript = Dir(caymanWindowsInstallKitPP / "scripts" / "patching").File(
        "create_patch.py"
    )
    oldAgentExe = glob.glob(
        Dir(caymanHorizonagentPP).File("Omnissa-Horizon-Agent-x86_64-*").abspath
    )[0]
    newAgentExe = vmware.LookupNode(
        "horizon-agent-%s-node" % platformArch, packaged=True
    )[0]
    outputPath = (
        Dir(vmware.phase.ComponentDir("agent-msi")).Dir(platformArch).Dir("patch")
    )
    wixPatchFile = Dir("#bora/install/msi/horizon/agent").File("Patch.wxs")
    conanWixPath = env["WIX_ROOT"]
    msimspPath = Dir(env["SDKBINROOT"]).Dir("x86").File("MsiMsp.exe")

    env.LoadTool("python")
    patchAction = (
        f"$PYTHON {createPatchScript} "
        f"--install-type exe "
        f"--old-installer {oldAgentExe} "
        f"--new-installer $SOURCE.abspath "
        f"--output-path {outputPath} "
        f"--wix-patch-file {wixPatchFile} "
        f"--wix-path {conanWixPath} "
        f'--msimsp-path "{msimspPath}" '
        f"--ignore-jre true"
    )

    patchAction = vmware.ActionWithDisplay(
        patchAction, "Generating Horizon Agent patch - $TARGET"
    )
    patchBuildFile = outputPath.Dir("build").File("patch.msp")
    patchCommand = env.Command(patchBuildFile, newAgentExe, patchAction)
    env.NoCache(patchCommand)

    # Set a side effect on a dummy node to ensure that x86 and x64 patch
    # generation actions are not run at the same time.
    agentMsiDir = Dir(vmware.phase.ComponentDir("agent-msi"))
    env.SideEffect(agentMsiDir.File("dummy_patch_side_effect"), patchBuildFile)

    patchFinalFilename = (
        os.path.splitext(os.path.basename(newAgentExe.abspath))[0] + ".msp"
    )
    stagedPatchFile = os.path.join(publishDir, patchFinalFilename)
    vmware.Alias("horizon-agent-patch-%s" % platformArch, stagedPatchFile)
    return env.LinkCopy(stagedPatchFile, patchBuildFile)


def stageCoreTestsZip(stageEnv, host, publishDir):
    """
    Stages WSNM and WSSM tests to a zip file and publish them to the tests
    folder.
    """
    coreTestZipFile = "CoreTest.zip"
    publishDirPP = vmware.PathPrefixer(publishDir)
    coreTestZipFile = Dir(publishDirPP / "tests" / host).File(coreTestZipFile)

    # Add wsnmMqttTest dependencies

    mqttEnv = vmware.LookupEnv("wsnm_mqtt-env", host=host)

    coreTestZipSources = [
        os.path.split(File(redist).abspath) for redist in mqttEnv["OPENSSL_REDIST"]
    ]
    coreTestZipSources += [(mqttEnv["MQTT_REDIST_DIR"], "paho-mqtt3as.dll")]
    coreTestZipSources += [(mqttEnv["MQTT_REDIST_DIR"], "paho-mqtt3as.pdb")]
    for name in os.listdir(mqttEnv["OPENSSL_BINPATH"]):
        if ".pdb" in name:
            coreTestZipSources += [(mqttEnv["OPENSSL_BINPATH"], name)]

    if "wsauth" in vmware.pkg.frozenFilesDetails:
        stageEnv.LoadTool("artifactory")
        frozenFilesExtractNode = stageEnv.FetchFromArtifactory(
            artifactorySignedFileRoot + vmware.pkg.frozenFilesDetails["wsauth"]["file"],
            sha256=vmware.pkg.frozenFilesDetails["wsauth"]["sha256"],
            extract=True,
            extractTargetFiles=["x64/wsauth.dll"],
            extractFolderSuffix=host,
            registerNodeName=vmware.pkg.frozenFilesDetails["wsauth"]["file"] + host,
        )
        coreTestZipSources += [
            (os.path.dirname(frozenFilesExtractNode[0].abspath), "wsauth.dll")
        ]
    else:
        nodes = vmware.LookupNode("wsauth", host)
        coreTestZipSources += [(os.path.dirname(nodes[0].abspath), "wsauth.dll")]

    # Add supporting files
    wsnmTestRoot = "#bora/apps/horizonAgent/test/apps/wsnm_mqtt_test/"
    deployConfigsDir = Dir(os.path.join(wsnmTestRoot, "file"))
    coreTestZipSources.append((deployConfigsDir.abspath, "."))

    winHttpTestRoot = "#bora/apps/horizonAgent/test/apps/winhttp/"
    deployWinhttpFilesDir = Dir(os.path.join(winHttpTestRoot, "files"))
    coreTestZipSources.append((deployWinhttpFilesDir.abspath, "."))

    # stage ws_applaunchmgrTest node
    stageNodes = vmware.pkg.StageDeliverables(
        stageEnv, "ws_applaunchmgrTest", host, publishStagePath
    )
    # stage ws_applaunchmgr_operationTest node
    stageNodes += vmware.pkg.StageDeliverables(
        stageEnv, "ws_applaunchmgr_operationTest", host, publishStagePath
    )
    # stage WsnmMqttTest node
    stageNodes += vmware.pkg.StageDeliverables(
        stageEnv, "wsnm_mqttTest", host, publishStagePath
    )
    # stage wsnm_updatemgrTest node
    stageNodes += vmware.pkg.StageDeliverables(
        stageEnv, "ws_updatemgrTest", host, publishStagePath
    )
    # stage wssm_desktopTest node
    stageNodes += vmware.pkg.StageDeliverables(
        stageEnv, "wssm_desktopTest", host, publishStagePath
    )
    # stage wsnm_desktopTest node
    stageNodes += vmware.pkg.StageDeliverables(
        stageEnv, "wsnm_desktopTest", host, publishStagePath
    )
    # stage lacu_test node
    stageNodes += vmware.pkg.StageDeliverables(
        stageEnv, "lacu_test", host, publishStagePath
    )
    # stage wsnm_commonTest node
    stageNodes += vmware.pkg.StageDeliverables(
        stageEnv, "wsnm_commonTest", host, publishStagePath
    )
    # stage hznReflectTest node
    stageNodes += vmware.pkg.StageDeliverables(
        stageEnv, "hznReflectTest", host, publishStagePath
    )
    # stage mqtt_client node
    stageNodes += vmware.pkg.StageDeliverables(
        stageEnv, "mqtt_client", host, publishStagePath
    )
    # stage winHttpTest node
    stageNodes += vmware.pkg.StageDeliverables(
        stageEnv, "winHttpTests", host, publishStagePath
    )

    stageNodes += vmware.pkg.StageDeliverables(
        stageEnv, "MessageFrameWork", host, publishStagePath
    )

    # stage ws_lcmTest node
    stageNodes += vmware.pkg.StageDeliverables(
        stageEnv, "ws_lcmTest", host, publishStagePath
    )

    # stage html5mmrServerTest node
    stageNodes += stageHtml5mmrServerTest(stageEnv)

    for stageNode in stageNodes:
        coreTestZipSources.append((stageNode.dir.abspath, stageNode.name))

    # Add hzMonServiceTest dependencies
    hzMonCommonDeps = [
        "hzMonApi",
    ]

    hzMonServiceTestZipSources = []
    for name in hzMonCommonDeps:
        stageNodes = vmware.pkg.StageDeliverables(
            stageEnv, name, host, publishStagePath
        )
        for stageNode in stageNodes:
            coreTestZipSources.append((stageNode.dir.abspath, stageNode.name))

    testNodes = [
        "hzMonServiceTest",
        "hzMonApiTestClient",
    ]
    for name in testNodes:
        stageNodes = vmware.pkg.StageDeliverables(
            stageEnv, name, host, publishStagePath
        )
        for stageNode in stageNodes:
            coreTestZipSources.append((stageNode.dir.abspath, stageNode.name))

    for host in ["win64"]:
        testEnv = vmware.LookupEnv("wsnm_desktopTest-env", host)
        deps = [
            "GTEST_REDIST",
        ]
        addDepsToZipSources(testEnv, deps, coreTestZipSources)

    return stageEnv.Zip(coreTestZipFile, coreTestZipSources)


def stageHzaPrepTests(stageEnv, host, publishDir):
    """
    Publishes hzaPrep test zip files to the tests folder.
    """
    zipFile = "hzaprepTest.zip"
    publishDirPP = vmware.PathPrefixer(publishDir)
    zipFile = Dir(publishDirPP / "tests" / host).File(zipFile)

    hzaprepTestZipSources = []

    # stage hzaprepTest node
    stageNodes = vmware.pkg.StageDeliverables(
        stageEnv, "hzaprepTest", host, publishStagePath
    )

    stageNodes += vmware.pkg.StageDeliverables(
        stageEnv, "MessageFrameWork", host, publishStagePath
    )

    for stageNode in stageNodes:
        hzaprepTestZipSources.append((stageNode.dir.abspath, stageNode.name))

    for host in ["win64"]:
        testEnv = vmware.LookupEnv("hzaprepTest-env", host)
        deps = [
            "GTEST_REDIST",
        ]
        addDepsToZipSources(testEnv, deps, hzaprepTestZipSources)

    return stageEnv.Zip(zipFile, hzaprepTestZipSources)


def stageUpdateToolTestsZip(stageEnv, host, publishDir):
    """
    Publishes updatetool test zip files to the tests folder
    """
    updatetoolTestZipFile = "UpdateToolTest.zip"
    publishDirPP = vmware.PathPrefixer(publishDir)
    updateToolZipFile = Dir(publishDirPP / "tests" / host).File(updatetoolTestZipFile)
    zipSources = []

    cdsClientEnv = vmware.LookupEnv("cdsClientTest-env", host=host)

    # Add cdsClient's dependencies
    cdsClientEnv.LoadTool(
        [
            "gettext",
            "libiconv",
            "libpcre2",
            "libxml",
        ]
    )
    deps = [
        "GLIB_REDIST",
        "CURL_REDIST",
        "OPENSSL_REDIST",
        "ZLIB_REDIST",
        "GTEST_REDIST",
        "LIBICONV_REDIST",
        "GETTEXT_REDIST",
        "PCRE2_REDIST",
        "REDISTRIBUTE_LIBXML2_DLL",
    ]
    addDepsToZipSources(cdsClientEnv, deps, zipSources)

    stageNodes = vmware.pkg.StageDeliverables(
        stageEnv, "libcds", host, publishStagePath
    )
    stageNodes += vmware.pkg.StageDeliverables(
        stageEnv, "cdsClientTest", host, publishStagePath
    )

    for stageNode in stageNodes:
        zipSources.append((stageNode.dir.abspath, stageNode.name))

    return stageEnv.Zip(updateToolZipFile, zipSources)


def stageRdsTestZips(stageEnv, host, publishDir):
    """
    Publishes rds test zip files to the tests folder.
    """
    testsDir = Dir(publishDir).Dir("tests")
    zipFile = testsDir.File("blittest-windows.zip")
    zipFiles = vmware.pkg.CreateZipStageNode(
        stageEnv, ["blitTest"], host, ["blitTest"], [], zipFile.abspath
    )

    for host, arch in hostMapPublish.items():
        zipFile = testsDir.File("hznaudioendpointComponentTest-%s.zip" % arch)
        zipFiles += vmware.pkg.CreateZipStageNode(
            stageEnv,
            ["hznaudioendpoint"],
            host,
            ["hznaudioendpoint"],
            [],
            zipFile.abspath,
        )
        zipFile = testsDir.File("hznaudioendpointUnitTest-%s.zip" % arch)
        zipFiles += vmware.pkg.CreateZipStageNode(
            stageEnv,
            ["hznaudioendpointUnitTest"],
            host,
            ["hznaudioendpointUnitTest"],
            [],
            zipFile.abspath,
            excludedFileFormats=[".map"],
        )

    return zipFiles


def stageDevtaps(stageEnv, host, publishDir):
    """
    Publishes devtap files to the devtap folder.
    """
    devtapDir = Dir(publishDir).Dir("devtap")

    zipFile = devtapDir.File("inputdevtap-x64.zip")
    zipFiles = vmware.pkg.CreateZipStageNode(
        stageEnv,
        ["inputdevtap"],
        "win64",
        ["inputdevtap"],
        [],
        zipFile.abspath,
        excludedFileFormats=[".map"],
    )

    zipFile = devtapDir.File("audiodevtap-x64.zip")
    zipFiles += vmware.pkg.CreateZipStageNode(
        stageEnv,
        ["audiodevtap"],
        "win64",
        ["audiodevtap"],
        [],
        zipFile.abspath,
        excludedFileFormats=[".map"],
    )

    zipFile = devtapDir.File("svgadevtap-x64.zip")
    zipFiles += vmware.pkg.CreateZipStageNode(
        stageEnv,
        ["svgadevtap"],
        "win64",
        ["svgadevtap"],
        [],
        zipFile.abspath,
        excludedFileFormats=[".map"],
    )

    return zipFiles


def stageArm64Binaries(
    stageEnv, publishDir, extrasStageDirRoot, extrasStageNodes, exportArm64Nodes
):
    # Create a node for ARM64 compiled binaries inside extrasstage
    arm64host = "win-arm64"
    arm64arch = "ARM64"
    extrasStageArm64Dir = os.path.join(extrasStageDirRoot, arm64host)
    for name in exportArm64Nodes:
        if not vmware.pkg.NodeExists(name, arm64host):
            continue

        node = vmware.LookupNode(name, arm64host)
        if os.path.splitext(node[0].abspath)[1] == ".lib":
            continue

        extrasStageNodes += vmware.pkg.StageDeliverables(
            stageEnv, name, arm64host, extrasStageArm64Dir, signBinaries=False
        )

    # Zip all the arm64 compiled binaries into the node 'win-arm64'
    zipFileName = arm64host + ".zip"
    return stageEnv.Zip(
        os.path.join(publishDir, arm64host, zipFileName),
        [(node.dir.abspath, node.name) for node in extrasStageNodes],
    )


def minifyJsFiles(stageEnv, sourceDir, targetDir):
    # Minify all js files only inside folder sourceDir, ignore subfolders
    minifiedFiles = []

    jsFiles = vmware.GlobSourceDir(os.path.join(sourceDir, "*.js"))
    for file in jsFiles:
        targetPath = os.path.join(targetDir, os.path.basename(file.abspath))
        node = stageEnv.uglifyJS(file.abspath, targetPath)
        stageEnv.NoCache(node)
        minifiedFiles.append(node)

    return minifiedFiles


def stageMinifiedExtension(
    stageEnv, srcPath, extensionName, zipFileName, rootDir, extraRdeRftDir, allNodes
):
    log.info(f"Staging minified {extensionName} extension.")
    extension = "extension"
    targetFile = os.path.join(rootDir, extensionName, extension, zipFileName)
    extraExtensionDir = os.path.join(extraRdeRftDir, extensionName)

    ignoreFileExt = ".js"
    filter = lambda path, f: not (f.endswith(ignoreFileExt))
    copyFiles = []
    minifiedJsFiles = []
    for path in srcPath:
        absSrcPath = Dir(path).abspath
        # Copy everything (except js) from absSrcPath to extraRdeRftDir
        copyFiles.append(
            vmware.utils.StageCopyDirectory(
                absSrcPath, Dir(extraExtensionDir), stageEnv.LinkCopy, filefilter=filter
            )
        )
        # Minify Js files and copy to extraRdeRftDir
        minifiedJsFiles.append(minifyJsFiles(stageEnv, absSrcPath, extraExtensionDir))

    allNodes.append(copyFiles)
    allNodes.append(minifiedJsFiles)

    # Now Zip entire extraExtensionDir to targetFile
    zipSources = stageEnv.Zip(targetFile, [(extraExtensionDir, ".")])
    stageEnv.Depends(zipSources, copyFiles)
    stageEnv.Depends(zipSources, minifiedJsFiles)
    allNodes.append(zipSources)


def stageAllMinifiedRdeRftExt(stageEnv, rootDir, extraRdeRftDir, allNodes):
    log.info("Staging all minified extensions.")
    stageEnv.LoadTool("uglifier")
    extraRdeRftDir = os.path.join(extraRdeRftDir, "rderftext")

    geoSrcPath = []
    geoSrcPath.append("#bora/apps/rde/gpsRedirection/browserExtension")
    geoextensionName = "gpsRedirection"
    geoZipFile = "geoRedir_chrome_browser_extension.min.zip"
    stageMinifiedExtension(
        stageEnv,
        geoSrcPath,
        geoextensionName,
        geoZipFile,
        rootDir,
        extraRdeRftDir,
        allNodes,
    )

    bcrSrcPath = []
    bcrSrcPath.append("#bora/apps/rde/html5mmr/web/browserRedir/browserExtension")
    bcrextensionName = "bcrRedirection"
    bcrZipFile = "bcrRedir_chrome_browser_extension.min.zip"
    stageMinifiedExtension(
        stageEnv,
        bcrSrcPath,
        bcrextensionName,
        bcrZipFile,
        rootDir,
        extraRdeRftDir,
        allNodes,
    )

    html5SrcPath = []
    html5SrcPath.append("#bora/apps/rde/html5mmr/web/html5mmr/browserExtension/common")
    html5SrcPath.append("#bora/apps/rde/html5mmr/web/html5mmr/browserExtension/chrome")
    html5ExtensionName = "html5mmr"
    html5ZipFile = "html5mmr_chrome_browser_extension.min.zip"
    stageMinifiedExtension(
        stageEnv,
        html5SrcPath,
        html5ExtensionName,
        html5ZipFile,
        rootDir,
        extraRdeRftDir,
        allNodes,
    )

    chromeContentRedirSrcPath = []
    chromeContentRedirSrcPath.append("#bora/apps/rde/urlRedirection/extensions/chrome")
    chromeContentRedirSrcPath.append("#bora/apps/rde/urlRedirection/extensions/common")
    chromeContentRedirExtensionName = "chromeUrlContentRedir"
    chromeContentRedirZipFile = "chrome_urlContentRedir_browser_extension.min.zip"
    stageMinifiedExtension(
        stageEnv,
        chromeContentRedirSrcPath,
        chromeContentRedirExtensionName,
        chromeContentRedirZipFile,
        rootDir,
        extraRdeRftDir,
        allNodes,
    )

    firefoxcontentRedirSrcPath = []
    firefoxcontentRedirSrcPath.append(
        "#bora/apps/rde/urlRedirection/extensions/firefox"
    )
    firefoxcontentRedirSrcPath.append("#bora/apps/rde/urlRedirection/extensions/common")
    firefoxContentRedirExtensionName = "firefoxUrlContentRedir"
    firefoxcontentRedirZipFile = "firefox_urlContentRedir_browser_extension.min.zip"
    stageMinifiedExtension(
        stageEnv,
        firefoxcontentRedirSrcPath,
        firefoxContentRedirExtensionName,
        firefoxcontentRedirZipFile,
        rootDir,
        extraRdeRftDir,
        allNodes,
    )

    webrtcRedirSdkExtPath = []
    webrtcRedirSdkExtPath.append("#bora/apps/rde/html5mmr/web/webrtc/browserExtension")
    webrtcRedirSdkExtName = "webrtcRedirectionSDK"
    sdkExtZipFileName = "webrtcRedir_sdk_chrome_browser_extension.min.zip"
    stageMinifiedExtension(
        stageEnv,
        webrtcRedirSdkExtPath,
        webrtcRedirSdkExtName,
        sdkExtZipFileName,
        rootDir,
        extraRdeRftDir,
        allNodes,
    )
    stageEnv.addUglifyDep(allNodes)


def stageGeoExtension(stageEnv, rootDir, allNodes):
    geoPath = "#bora/apps/rde/gpsRedirection/browserExtension"
    geoDestDir = "gpsRedirection"
    geoZipFileName = "geoRedir_chrome_browser_extension.zip"
    extension = "extension"
    geoTargetDir = os.path.join(rootDir, geoDestDir, extension, geoZipFileName)
    allNodes += stageEnv.Zip(geoTargetDir, [(Dir(geoPath).abspath, ".")])


def stageBCRExtension(stageEnv, rootDir, allNodes):
    bcrPath = "#bora/apps/rde/html5mmr/web/browserRedir/browserExtension"
    bcrExtensionDir = "bcrRedirection"
    bcrZipFileName = "browserRedir_chrome_browser_extension.zip"
    extension = "extension"
    bcrTargetDir = os.path.join(rootDir, bcrExtensionDir, extension, bcrZipFileName)
    allNodes += stageEnv.Zip(bcrTargetDir, [(Dir(bcrPath).abspath, ".")])


def stageHtml5mmrExtension(stageEnv, rootDir, allNodes):
    zipSources = []
    html5Path1 = "#bora/apps/rde/html5mmr/web/html5mmr/browserExtension/common"
    html5Path2 = "#bora/apps/rde/html5mmr/web/html5mmr/browserExtension/chrome"
    zipSources.append((Dir(html5Path1).abspath, "."))
    zipSources.append((Dir(html5Path2).abspath, "."))
    html5ExtensionDir = "html5mmr"
    extension = "extension"
    html5ZipFileName = "html5mmr_chrome_browser_extension.zip"
    html5TargetDir = os.path.join(
        rootDir, html5ExtensionDir, extension, html5ZipFileName
    )
    allNodes += stageEnv.Zip(html5TargetDir, zipSources)


def stageWebRTCRedirSDKExtension(stageEnv, rootDir, allNodes):
    sdkExtPath = "#bora/apps/rde/html5mmr/web/webrtc/browserExtension"
    webrtcRedirSdkDir = "webrtcRedirectionSDK"
    sdkExtZip = "webrtcRedir_sdk_chrome_browser_extension.zip"
    extension = "extension"
    sdkExtTargetDir = os.path.join(rootDir, webrtcRedirSdkDir, extension, sdkExtZip)
    allNodes += stageEnv.Zip(sdkExtTargetDir, [(Dir(sdkExtPath).abspath, ".")])


def stagChromeContentRedirExtension(stageEnv, rootDir, allNodes):
    zipSources = []
    contentPath1 = "#bora/apps/rde/urlRedirection/extensions/common"
    contentPath2 = "#bora/apps/rde/urlRedirection/extensions/chrome"
    zipSources.append((Dir(contentPath1).abspath, "."))
    zipSources.append((Dir(contentPath2).abspath, "."))
    contentRedirExtensionDir = "chromeUrlContentRedir"
    extension = "extension"
    contentRedirZipFileName = "chrome_urlContentRedir_browser_extension.zip"
    contentRedirTargetDir = os.path.join(
        rootDir, contentRedirExtensionDir, extension, contentRedirZipFileName
    )
    allNodes += stageEnv.Zip(contentRedirTargetDir, zipSources)


"""
# this function is causing build failures, because of parallel zip calls.
def stagFirefoxContentRedirExtension(stageEnv, rootDir, allNodes):
    zipSources = []
    contentPath1 = "#bora/apps/rde/urlRedirection/extensions/common"
    contentPath2 = "#bora/apps/rde/urlRedirection/extensions/firefox"
    zipSources.append((Dir(contentPath1).abspath, "."))
    zipSources.append((Dir(contentPath2).abspath, "."))
    contentRedirExtensionDir = "firefoxUrlContentRedir"
    extension = "extension"
    contentRedirZipFileName = "firefox_urlContentRedir_browser_extension.zip"
    contentRedirTargetDir = os.path.join(
        rootDir, contentRedirExtensionDir, extension, contentRedirZipFileName
    )
    allNodes += stageEnv.Zip(contentRedirTargetDir, zipSources)
"""


def stageTeamsRedirection(stageEnv, rootDir, extraRdeRftDir, allNodes):
    log.info("Staging Teams Redirection shim")
    buildScriptsPath = "#bora/apps/rde/html5mmr/web/webrtc/buildscripts"
    teamsShimPath = "#bora/apps/rde/html5mmr/web/webrtc/shim"
    extraShimDir = os.path.join(extraRdeRftDir, "rderftext", "teamsRedirection")

    shimFile = File(os.path.join(extraShimDir, "vdiVMwarePeerConnection.js"))
    nonMinified = stageEnv.FileCopy(shimFile, Dir(teamsShimPath).File("webRTCRedir.js"))
    redirScriptFile = Dir(teamsShimPath).File("webRTCRedir.py")
    addHeaderScriptFile = Dir(buildScriptsPath).File("addCopyHeader.py")

    # Run webRTCRedir.py on webRTCRedir.js with fixLogging
    stageEnv.LoadTool("python")
    stageEnv.AddPostAction(
        nonMinified,
        f"$PYTHON {redirScriptFile.abspath} fixLogging $TARGET",
    )

    # Add header file
    stageEnv.AddPostAction(
        nonMinified,
        (
            f"$PYTHON "
            f"{addHeaderScriptFile.abspath} "
            f"shim "
            f"$TARGET "
            f"{vmware.BuildNumber()}"
        ),
    )
    stageEnv.NoCacheIfBuildNumberInfoOverridden(nonMinified)
    allNodes.append(nonMinified)

    # Minify shim and add header comment
    extraShimMinDir = os.path.join(Dir(extraShimDir).abspath, "minified")
    minified = minifyJsFiles(stageEnv, Dir(extraShimDir).abspath, extraShimMinDir)
    shimFileMin = File(
        os.path.join(extraShimDir, "minified", "vdiVMwarePeerConnection.js")
    )
    stageEnv.AddPostAction(
        minified,
        (
            f"$PYTHON "
            f"{addHeaderScriptFile.abspath} "
            f"shimMin "
            f"$TARGET "
            f"{vmware.BuildNumber()}"
        ),
    )
    allNodes.append(minified)

    # Create teams_webrtc_redirection zip file to be published
    teamsShimDir = "teamsRedirection"
    teamsZip = "teams_webrtc_redirection_%s.zip" % vmware.BuildNumber()
    teamsRedirTargetFile = os.path.join(rootDir, teamsShimDir, teamsZip)
    zipResult = stageEnv.Zip(
        teamsRedirTargetFile, [(shimFile.dir.abspath, shimFile.name)]
    )

    stageEnv.Depends(zipResult, nonMinified)
    allNodes.append(zipResult)

    # Create teams_webrtc_redirection_min zip file to be published
    minTeamsZip = "teams_webrtc_redirection_%s.min.zip" % vmware.BuildNumber()
    minTeamsRedirTargetFile = os.path.join(rootDir, teamsShimDir, minTeamsZip)
    zipMinResult = stageEnv.Zip(
        minTeamsRedirTargetFile, [(shimFileMin.dir.abspath, shimFileMin.name)]
    )
    stageEnv.Depends(zipMinResult, minified)
    allNodes.append(zipMinResult)


def stageWebrtcRedirectionSDK(stageEnv, rootDir, extraRdeRftDir, allNodes):
    webrtcRedirSDKPath = "#bora/apps/rde/html5mmr/web/webrtc/"
    buildScriptsPath = "#bora/apps/rde/html5mmr/web/webrtc/buildscripts"
    extraTeamsDir = os.path.join(extraRdeRftDir, "rderftext", "teamsRedirection")
    extraSDKDir = os.path.join(extraTeamsDir, "sdk")
    addHeaderScriptFile = Dir(buildScriptsPath).File("addCopyHeader.py")
    sdkDir = os.path.join(webrtcRedirSDKPath, "sdk")

    # minify SDK file and add header comment
    minified = minifyJsFiles(stageEnv, Dir(sdkDir).abspath, extraSDKDir)
    stageEnv.AddPostAction(
        minified,
        (
            f"$PYTHON "
            f"{addHeaderScriptFile.abspath} "
            f"sdk "
            f"$TARGET "
            f"{vmware.BuildNumber()}"
        ),
    )
    allNodes.append(minified)

    allNodes += stageEnv.FileCopy(
        Dir(extraSDKDir).File("package.json"),
        Dir(sdkDir).File("package.json"),
    )

    allNodes += stageEnv.FileCopy(
        Dir(extraSDKDir).File("README.md"),
        Dir(sdkDir).File("README.md"),
    )

    allNodes += stageEnv.FileCopy(
        Dir(extraSDKDir).File("RELEASES"),
        Dir(sdkDir).File("RELEASES"),
    )

    # Create Horizon-WebRTC-Redir-SDK zip and publish
    webrtcRedirSdkDir = "webrtcRedirectionSDK"
    sdkZip = "Horizon-WebRTC-Redir-SDK-%s.zip" % vmware.BuildNumber()
    sdkTargetFile = os.path.join(rootDir, webrtcRedirSdkDir, sdkZip)
    zipSources = [
        (Dir(webrtcRedirSDKPath).abspath, "sample"),
        (Dir(extraTeamsDir).abspath, "sdk"),
    ]
    zipResult = stageEnv.Zip(sdkTargetFile, zipSources)
    stageEnv.Depends(zipResult, minified)
    allNodes += zipResult


def stageHzWebRtcRedir(stageEnv, rootDir, extraRdeRftDir, allNodes):
    log.info("staging Teams Redirection shim and SDK.")
    stageTeamsRedirection(stageEnv, rootDir, extraRdeRftDir, allNodes)
    stageWebrtcRedirectionSDK(stageEnv, rootDir, extraRdeRftDir, allNodes)


def stageAllNonMinifiedRdeRftExt(stageEnv, rootDir, allNodes):
    log.info("Staging all non minified extensions.")
    stageGeoExtension(stageEnv, rootDir, allNodes)
    stageBCRExtension(stageEnv, rootDir, allNodes)
    stageHtml5mmrExtension(stageEnv, rootDir, allNodes)
    stageWebRTCRedirSDKExtension(stageEnv, rootDir, allNodes)
    stagChromeContentRedirExtension(stageEnv, rootDir, allNodes)
    # stagFirefoxContentRedirExtension(stageEnv, rootDir, allNodes)


def stageRdeRftExt(stageEnv, publishDir, extraRdeRftDir, allNodes):
    extensionTargetDir = "rde-rft-ext"
    rootDir = os.path.join(publishDir, extensionTargetDir)
    stageAllNonMinifiedRdeRftExt(stageEnv, rootDir, allNodes)
    stageAllMinifiedRdeRftExt(stageEnv, rootDir, extraRdeRftDir, allNodes)
    stageHzWebRtcRedir(stageEnv, rootDir, extraRdeRftDir, allNodes)


def stageCataFwTestsFramework(stageEnv, host, publishDir, publishStagePath):
    """Stages Client/Agent Test Framework (cata-fw)"""
    cataFwZipFile = "hzn-cata-fw-%s-INTERNAL-%s-%s.zip" % (
        winPlatform,
        vmware.ProductVersionNumber(),
        vmware.BuildNumber(),
    )
    cataFwZipFile = Dir(publishDir).File(cataFwZipFile)
    # Add cata-fw dependencies
    cataFwTestsEnv = vmware.LookupEnv("cata_fwTests-env", host)

    cataFwTestsDeps = []
    for name in [
        "LIBICONV_REDIST",
        "GETTEXT_REDIST",
        "REDISTRIBUTE_MFW_DLL",
        "GLIB_REDIST",
    ]:
        dep = cataFwTestsEnv[name]
        if isinstance(dep, list):
            cataFwTestsDeps += [File(x) for x in dep]
        else:
            cataFwTestsDeps.append(File(dep))

    cataFwZipNode = StageDeliverablesIntoZip(
        stageEnv, cataFwNodes, host, publishStagePath, cataFwZipFile, cataFwTestsDeps
    )
    vmware.Alias("cata-fw-%s-stage" % (host), cataFwZipNode)
    return cataFwZipNode


def stageRdshTestFramework(stageEnv, host, publishDir, publishStagePath):
    """Stages RDSH Test Framework"""
    winPlatform = host.replace("win", "")
    rdshTestFwZipFile = "hzn-rdsh-test-fw-%s-INTERNAL-%s-%s.zip" % (
        winPlatform,
        vmware.ProductVersionNumber(),
        vmware.BuildNumber(),
    )
    rdshTestFwZipFile = Dir(publishDir).File(rdshTestFwZipFile)
    rdshStageDir = os.path.join(productBuildRoot, host, "rdshTestFwStage")
    pcoipTestServerDep = stageEnv.LinkCopy(
        Dir(rdshStageDir).File("pcoipTestServer.exe"),
        Dir(publishStagePath).File("cata_fwSM.exe"),
    )

    rdsTestAppRoot = "#bora/apps/horizonAgent/test/apps/rdsTestApp"
    cataFwTestsEnv = vmware.LookupEnv("cata_fwTests-env", host)
    mockWinClientEnv = vmware.LookupEnv("MockWinClient-env", host)
    mfw_test_nodes = vmware.LookupNode("mfw_test", host=stageEnv.Host().Name())

    rdshTestFwDeps = []
    for name in [
        "LIBICONV_REDIST",
        "GETTEXT_REDIST",
        "REDISTRIBUTE_MFW_DLL",
        "GLIB_REDIST",
    ]:
        dep = cataFwTestsEnv[name]
        if isinstance(dep, list):
            rdshTestFwDeps += [File(x) for x in dep]
        else:
            rdshTestFwDeps.append(File(dep))

    rdshTestFwDeps += [
        mfw_test_nodes[0],
        File(os.path.join(rdsTestAppRoot, "RDSHTestFrameworkInstall.cmd")),
        File(os.path.join(rdsTestAppRoot, "RDSHTestFrameworkInstall.reg")),
        File(os.path.join(rdsTestAppRoot, "config.ini")),
        File(mockWinClientEnv["LOG4CXX_DLL_PATH"]),
    ]

    rdshTestFwDeps += [File(x) for x in pcoipTestServerDep]
    rdshTestFwDeps += [File(x) for x in mockWinClientEnv["REDIST"]]

    rdshTestFwZipNodes = StageDeliverablesIntoZip(
        stageEnv,
        rdshTestFwNodes,
        host,
        publishStagePath,
        rdshTestFwZipFile,
        rdshTestFwDeps,
    )

    stageEnv.Depends(rdshTestFwZipNodes, pcoipTestServerDep)
    vmware.Alias("rdsh-test-fw-%s-stage" % (host), rdshTestFwZipNodes)
    return rdshTestFwZipNodes


def stageVncServer(stageEnv, host, publishDir, publishStagePath):
    """Stages vncServer"""
    vncServerZipFile = "hzn-vncServer-%s-INTERNAL-%s-%s.zip" % (
        winPlatform,
        vmware.ProductVersionNumber(),
        vmware.BuildNumber(),
    )
    vncServerZipFile = Dir(publishDir).File(vncServerZipFile)
    zipSources = []
    stageNodes = vmware.pkg.StageDeliverables(
        stageEnv, "vncServer", host, publishStagePath
    )
    for stageNode in stageNodes:
        zipSources.append((stageNode.dir.abspath, stageNode.name))
    vncServerEnv = vmware.LookupEnv("vncServer-env", host)

    # Loop over all of vncServer's open source dependencies.
    zipSourceFiles = []
    for redistFile in vncServerEnv["REDIST"] + vncServerEnv["MSVC_CRT_REDIST"]:
        # Definitely stage the file.
        fileObject = File(redistFile)
        zipSourceFiles.append(fileObject)

        # Stage a pdb too if one exists.
        pdbObject = File(os.path.splitext(fileObject.abspath)[0] + ".pdb")
        if pdbObject.exists():
            zipSourceFiles.append(pdbObject)

    for file in zipSourceFiles:
        zipSources.append((file.dir.abspath, file.name))

    return stageEnv.Zip(vncServerZipFile, zipSources)


def stageToolsMsms(stageEnv):
    """Extracts Tools msms"""
    toolsDrvMsmStageP = Dir(stagePP / "svga")
    toolsRoot = vmware.GetGobuildComponent("tools")
    toolsWinDrvZip = stageEnv.Glob(os.path.join(toolsRoot, "tools-windrv-*.zip"))[0]
    extractRoot = os.path.join(toolsRoot, "extract")
    unzipNode = stageEnv.UnZip(toolsWinDrvZip, extractRoot, clean=True)
    srcFile = stageEnv.Glob(
        os.path.join(
            extractRoot,
            (
                "build/*/*/bora-vmsoft/build/*/install/"
                "windriver/installer/VMwareVmVideo.msm"
            ),
        )
    )
    # Stage msm for 32 bit driver
    copyNode32 = stageEnv.LinkCopy(toolsDrvMsmStageP.File("VMwareVmVideo.msm"), srcFile)
    Depends(copyNode, unzipNode)
    allNodes += copyNode

    # Stage msm for 64 bit driver
    srcFile = stageEnv.Glob(
        os.path.join(
            extractRoot,
            (
                "build/*/*/bora-vmsoft/build/*/install/"
                "windriver/installer/VMwareVmVideo64.msm"
            ),
        )
    )
    copyNode64 = stageEnv.LinkCopy(
        toolsDrvMsmStageP.File("VMwareVmVideo64.msm"), srcFile
    )
    Depends(copyNode, unzipNode)
    return copyNode32 + copyNode64


def stageDctComponentTest(stageEnv, host, publishDir, publishStagePath):
    """Stages publish DCT component test zip"""
    dctComponentTestZipFile = "rxDCTComponentTestAgent.zip"
    publishDirPP = vmware.PathPrefixer(publishDir)
    dctComponentTestZipFile = Dir(publishDirPP / "tests" / host).File(
        dctComponentTestZipFile
    )

    DCTTestRoot = "#bora/apps/horizonDCT/componentTest"
    DCTRoot = "#bora/apps/horizonAgent"
    zipRoot = vmware.GetConanBuildComponent(stageEnv, "7zip").env["PATH"]
    zipSources = []
    testNodes = [
        "rxDCTComponentTestAgent",
        "ws_diag",
        "dcttestapp",
    ]
    for name in testNodes:
        stageNodes = vmware.pkg.StageDeliverables(
            stageEnv, name, host, publishStagePath
        )
        for stageNode in stageNodes:
            zipSources.append((stageNode.dir.abspath, stageNode.name))
    # Add DCT scripts to test zip
    zipSources.append((Dir(DCTRoot).abspath, "DCT"))
    # Add deployment configs to test zip
    deployConfigsDir = Dir(os.path.join(DCTTestRoot, "deploymentConfigs"))
    zipSources.append((deployConfigsDir.abspath, "dct_agent_win.ini"))
    # Add test case configs to test zip
    commonTestCasesDir = Dir(os.path.join(DCTTestRoot, "testCases", "windowsCommon"))
    zipSources.append((commonTestCasesDir.abspath, "."))
    agentTestCasesDir = Dir(os.path.join(DCTTestRoot, "testCases", "windowsAgent"))
    zipSources.append((agentTestCasesDir.abspath, "."))
    # Add digital signature to unit test powershell scripts
    dctWinCommScriptFiles = [
        File(
            os.path.join(DCTTestRoot, "unitTest/windowsCommon/config-parser.Tests.ps1")
        ),
        File(os.path.join(DCTTestRoot, "unitTest/windowsCommon/vdm-common.Tests.ps1")),
        File(
            os.path.join(DCTTestRoot, "unitTest/windowsCommon/plugin-manager.Tests.ps1")
        ),
        File(
            os.path.join(DCTTestRoot, "unitTest/windowsCommon/vdm-unittest-common.ps1")
        ),
    ]
    tnodes = vmware.DirCopy(
        dctWinCommScriptFiles,
        Dir(os.path.join(DCTTestRoot, "unitTest/windowsCommon/")),
        Dir(binPath),
        stageEnv,
    )
    dctWinAgentScriptFiles = [
        File(
            os.path.join(DCTTestRoot, "unitTest/windowsAgent/vdm-datacollect.Tests.ps1")
        ),
        File(
            os.path.join(
                DCTTestRoot, "unitTest/windowsAgent/vdm-unittest-execution.ps1"
            )
        ),
    ]
    tnodes += vmware.DirCopy(
        dctWinAgentScriptFiles,
        Dir(os.path.join(DCTTestRoot, "unitTest/windowsAgent/")),
        Dir(binPath),
        stageEnv,
    )
    stageEnv.SignFile(tnodes)

    # Add digitally signed powershell scripts
    agentDCTPSFiles = [
        "elevate.ps1",
        "vdm-common.ps1",
        "vdm-debug.ps1",
        "vdm-help.ps1",
        "vdm-loglevel-common.ps1",
        "vdm-query.ps1",
        "vdm-support.ps1",
        "vdm-product-common.ps1",
        "vdm-datacollect.ps1",
        "plugin-manager.ps1",
        "config-parser.ps1",
        "config-parser.Tests.ps1",
        "plugin-manager.Tests.ps1",
        "vdm-common.Tests.ps1",
        "vdm-unittest-common.ps1",
        "vdm-datacollect.Tests.ps1",
        "vdm-unittest-execution.ps1",
    ]
    for file in agentDCTPSFiles:
        zipSources.append((binPath, file))

    # Add DCT deps 7za to test zip
    zip7zDir = Dir(zipRoot)
    zipSources.append((zip7zDir.abspath, "7za.exe"))
    zipSources.append((zip7zDir.abspath, "7za.dll"))
    zipSources.append((zip7zDir.abspath, "7zxa.dll"))

    return stageEnv.Zip(dctComponentTestZipFile, zipSources)


def stageTsdrServerUnitTest(stageEnv, host, publishDir):
    """Stages publish tsdr agent unit test zip"""
    publishDirPP = vmware.PathPrefixer(publishDir)
    tsdrUnitTestZipFile = Dir(publishDirPP / "tests" / host).File(
        "tsdrAgentUnitTest.zip"
    )

    TsdrTestRoot = "#bora/apps/horizonAgent/apps/tsdr/tests/unitTest"
    zipSources = []
    # Add deployment configs to test zip
    zipSources.append((Dir(TsdrTestRoot).abspath, "horizonut_tsdr_agent_win.ini"))

    nodes = []
    targetName = ["tsdrServerUnitTest"]
    zipNode = vmware.pkg.CreateZipStageNode(
        stageEnv,
        targetName,
        "win64",
        targetName,
        zipSources,
        tsdrUnitTestZipFile,
        excludedFileFormats=[".map"],
    )

    if zipNode is not None:
        nodes += zipNode

    return nodes


def stageHorizonRxTestAgent(stageEnv, host, publishDir):
    """Stage horizonrxtest files for the component test"""
    publishDirPP = vmware.PathPrefixer(publishDir)
    horizonRxTestAgentZipFile = Dir(publishDirPP / "tests").File(
        "horizonrxtestAgent.zip"
    )

    targetNames = [
        "hznaudioendpoint",
        "MessageFrameWork",
        "rdsManager",
        "resolutionSetLib",
        "rxci_socket_server",
        "rxci_socket_vchan",
        "rxTestNodeServer",
        "rxTestCred",
        "hznsci",
    ]

    # deploy, undeploy python and ini files
    extrazipSources = []
    srcRoot = Dir("#bora/apps/horizonrxtest/componentTest/deploy")
    extrazipSources.append(
        (Dir(srcRoot).abspath, "horizonrxtest_server_win_flat_rdsh.ini")
    )
    extrazipSources.append((Dir(srcRoot).abspath, "horizonrxtest_server_win_vdi.ini"))
    extrazipSources.append((Dir(srcRoot).abspath, "horizonrxtest_undeploy.py"))
    extrazipSources.append((Dir(srcRoot).abspath, "horizonrxtest_deploy.py"))
    extrazipSources.append((Dir(srcRoot).abspath, "horizonrxtest_health_check.py"))
    extrazipSources.append((Dir(srcRoot).abspath, "startRxTestNodeServer.bat"))
    extrazipSources.append((Dir(srcRoot).abspath, "wsnmlessRegister.reg"))
    extrazipSources.append((Dir(srcRoot).abspath, "utility"))

    # 3rd deps
    buildEnv = vmware.LookupEnv("rxci_socket_server-env", "win64")
    libs = buildEnv["MSVC_RELEASE_CRT_REDIST"] + buildEnv["OPENSSL_REDIST"]
    for lib in libs:
        f = stageEnv.File(lib)
        extrazipSources.append((f.dir.abspath, f.name))

    # drivers
    hznviddNode = vmware.LookupNode("hznvidd-msi-x64", packaged=True)
    for node in hznviddNode:
        extrazipSources.append((node.dir.abspath, node.name))

    hznvidd2Node = vmware.LookupNode("hznvidd2-msi-x64", packaged=True)
    for node in hznvidd2Node:
        extrazipSources.append((node.dir.abspath, node.name))

    # The horizonrxtest uses drivers in frozen files here because the drivers are
    # Not the test targets.
    drivers = {
        "hznrdsinput": [
            "Win8Release",
            ["hznrdsinput.cat", "hznrdsinput.inf", "hznrdsinput.sys"],
        ],
        "hznrdsdd": [
            "Win7Release",
            ["hznrdsdd.cat", "hznrdsdd.dll", "hznrdsdd.inf", "hznrdsmn.sys"],
        ],
    }
    for driver, driverInfo in drivers.items():
        extractTargetFiles = []
        for file in driverInfo[1]:
            extractTargetFiles += ["{}/x64/bin/{}".format(driverInfo[0], file)]

        files = stageEnv.FetchFromArtifactory(
            artifactorySignedFileRoot + vmware.pkg.frozenFilesDetails[driver]["file"],
            sha256=vmware.pkg.frozenFilesDetails[driver]["sha256"],
            extract=True,
            extractTargetFiles=extractTargetFiles,
            registerNodeName=vmware.pkg.frozenFilesDetails[driver]["file"],
        )

        for f in files:
            extrazipSources.append((f.dir.abspath, f.name))

    # create zip file
    zipNode = vmware.pkg.CreateZipStageNode(
        stageEnv,
        targetNames,
        host,
        targetNames,
        extrazipSources,
        horizonRxTestAgentZipFile,
        excludedFileFormats=[".map", ".pdb", ".lib"],
    )

    return zipNode or []


def stageHorizonRxUT(stageEnv, host, publishDir):
    """Stage horizonrxtest files for the unit test"""
    publishDirPP = vmware.PathPrefixer(publishDir)
    horizonRxUtZipFile = Dir(publishDirPP / "tests").File("horizonrxut.zip")

    targetNames = [
        "horizonrxut",
    ]

    # deploy, undeploy python files
    extrazipSources = []
    srcRoot = Dir("#bora/apps/horizonrxtest/componentTest/deploy")
    extrazipSources.append((Dir(srcRoot).abspath, "horizonut_run_cases.py"))
    extrazipSources.append((Dir(srcRoot).abspath, "horizonut_deploy.py"))
    extrazipSources.append((Dir(srcRoot).abspath, "utility"))

    # 3rd libs
    buildEnv = vmware.LookupEnv("horizonrxut-env", host)
    deps = buildEnv["MSVC_RELEASE_CRT_REDIST"] + buildEnv["REDIST"]
    if vmware.BuildType() == "obj":
        deps += buildEnv["MSVC_DEBUG_CRT_REDIST"]
        deps += buildEnv["WINDOWS_DEBUG_CRT_REDIST"]
    for lib in deps:
        f = stageEnv.File(lib)
        extrazipSources.append((f.dir.abspath, f.name))

    # create zip file
    zipNode = vmware.pkg.CreateZipStageNode(
        stageEnv,
        targetNames,
        host,
        targetNames,
        extrazipSources,
        horizonRxUtZipFile,
        excludedFileFormats=[".map", ".pdb", ".lib"],
    )

    return zipNode or []


def stageHzWebauthnTests(stageEnv, host, publishDir):
    """
    Publishes hzwebauthn test zip files to the tests folder.
    """
    testsDir = Dir(publishDir).Dir("tests")

    # UT files
    targetName = ["hzwebauthnUnitTest"]
    zipFiles = []
    for host, arch in hostMapPublish.items():
        zipFile = testsDir.File("hzwebauthnUnitTest-%s.zip" % arch)
        zipFiles += vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetName,
            host,
            targetName,
            [],
            zipFile.abspath,
            excludedFileFormats=[".map"],
        )

    # CI files
    deliverableNodesNames = [
        "hzWebauthn",
        "hznsci",
    ]
    zipSources = []
    for filename in [
        "fido2_server_win_vdi.ini",
        "fido2_server_win_rdsh.ini",
        "fido2Action.py",
        "fido2TcpClient.py",
        "fido2TcpServer.py",
        "fido2Utils.py",
    ]:
        zipSources.append(
            (
                stageEnv.Dir("#bora/apps/rde/fido2/tests/componentTest/deploy").abspath,
                filename,
            )
        )
    # CI driver files
    drivers = {
        "hznicpdr": "Win8",
    }
    for driver, osFolder in drivers.items():
        if vmware.BuildType() == "obj":
            driverBuildType = "Debug"
        else:
            driverBuildType = "Release"
        driverBuildDir = os.path.join(
            vmware.DirAbsPath(vmware.BuildRoot()),
            vmware.Product(),
            driver,
            osFolder + driverBuildType,
            "x64",
            "bin",
        )
        for fileType in [
            ".inf",
            ".cat",
            ".pdb",
            ".sys",
        ]:
            file = driver + fileType
            zipSources.append((driverBuildDir, file))

    zipFile = testsDir.File("fido2ComponentTestServer.zip")
    zipFiles += vmware.pkg.CreateZipStageNode(
        stageEnv,
        [],
        "win64",
        deliverableNodesNames,
        zipSources,
        zipFile.abspath,
        excludedFileFormats=[".map"],
    )
    return zipFiles


def buildCIMsiNodes(stageEnv, moduleName, mode, copyNodeMap):
    """
    Builds MSI node that are required by component test.
    """
    arch = "x64"
    fragmentName = mode
    nodes = []
    wixEnv = stageEnv.Clone()
    stageDir = Dir(installerStagePath).Dir(arch)
    msiIntermediateBuildDir = Dir(
        os.path.join(
            vmware.DirAbsPath(vmware.BuildRoot()),
            vmware.BuildType(),
            "apps",
            moduleName,
        )
    )
    wixBuildDir = Dir(msiIntermediateBuildDir).Dir(arch)
    wixEnv.Append(
        WIXDEFINES={
            "MSIProductVersion": vmware.ProductVersionNumber(),
            "ARCH": arch,
            "StageDir": stageDir.abspath,
            "MSI_TYPE": mode,
            "GOBUILD_PCOIP_SOFT_SERVER_ROOT": vmware.GetGobuildComponent(
                "pcoip-soft-server"
            ),
            "GOBUILD_WINDOWS_INSTALLKIT_ROOT": vmware.GetGobuildComponent(
                "windows-installkit"
            ),
        },
        WIXCANDLEFLAGS=[
            "-arch",
            arch,
            "-ext",
            "WixFirewallExtension",
            "-ext",
            "WixUIExtension",
            "-sw1086",
            "-sw1006",
            "-wx",
        ],
        WIXLIGHTFLAGS=[
            "-ext",
            "WixUIExtension",
            "-sw1072",
            "-wx",
            "-sice:ICE03",
            "-sice:ICE80",
            "-sice:ICE30",
            "-sice:ICE69",
        ],
        WIXLIGHTLOC=[
            "-cultures:en-us",
        ],
    )
    if moduleName == "PrintRedir":
        wixEnv.Append(
            WIXDEFINES={
                "PRINTER_DRIVER_DIR": printerDrvDir,
            }
        )
    if moduleName == "RTAV":
        rtavEnv = vmware.LookupEnv("rtavPlugin-env", "win64")
        avcodecLib = os.path.basename(rtavEnv["FFMPEG_AVCODEC_REDIST"])
        avutilLib = os.path.basename(rtavEnv["FFMPEG_AVUTIL_REDIST"])
        x264Lib = os.path.basename(rtavEnv["LIBX264_REDIST"])
        wixEnv.Append(
            WIXDEFINES={
                "AVCODEC_LIB": avcodecLib,
                "AVUTIL_LIB": avutilLib,
                "X264_LIB": x264Lib,
                "ToolsInstDir": toolsInstDir,
            }
        )

    # Step 1: prepare the wix build env.
    nodeWixEnv = wixEnv.Clone()
    nodeWixEnv.Append(
        WIXDEFINES={
            "wixBuildDir": Dir(wixBuildDir).abspath,
            "fragmentName": fragmentName,
        },
        WIXLIGHTFLAGS=[
            "-sice:ICE61",  # This warning is generated if max version
            # is not defined. We dont want to define an
            # arbitary max version
            "-sice:ICE82",  # This warning is generated when Sequence
            # number is same for all actions.
            # We can safely ignore the warning.
        ],
    )

    # Step 2: build the msi-specific normal wixobj.
    wixObjNode = nodeWixEnv.Wixobj(
        wixBuildDir.File("%s-%s-%s-msi.wixobj" % (moduleName, fragmentName, arch)),
        "#bora/install/msi/%s/%s.wxs" % (moduleName, mode),
    )

    # Step 3: create the .msi node.
    wixName = "Horizon-%s-%s-1.0.0-%s" % (moduleName, mode, arch)
    wixMsiFile = wixBuildDir.File(wixName + ".msi")
    wixMsiNode = nodeWixEnv.Wixmsi(
        wixMsiFile,
        wixObjNode,
    )
    publishDir = vmware.ReleasePackagesDir()
    msiDir = Dir(publishDir).Dir("tests")
    copyNode = nodeWixEnv.LinkCopy(Dir(msiDir).File(wixMsiFile.name), wixMsiFile)
    Depends(copyNode, wixMsiNode)
    nodes += copyNode

    return nodes


def stagePrintRedirTest(stageEnv):
    """Stage print redirection CI test packages and binaries

    stageEnv specifies a stage environment object.
    """
    zipNodes = []
    nodes = []
    targetName = "printRedirTest.zip"
    host = "win64"
    scriptsSrc = "#bora/apps/printRedir/componentTest"

    printMsiNodes = buildCIMsiNodes(stageEnv, "PrintRedir", "Agent", copyNodeMap)
    for node in printMsiNodes:
        zipNodes += stageEnv.LinkCopy(os.path.join(printTestDir, node.name), node)
    stageEnv.Depends(zipNodes, printMsiNodes)
    zipNodes += vmware.LookupNode("prTestPrint", host)

    zipNodes += vmware.DirCopy(
        vmware.EnumerateSourceDir(scriptsSrc),
        Dir(scriptsSrc),
        Dir(printTestDir),
        stageEnv,
    )
    publishDir = vmware.ReleasePackagesDir()
    if publishDir:
        publishDir = os.path.join(publishDir, "tests")
        zipSources = []
        buildEnv = vmware.LookupEnv("printredir-env", host)
        zipNodes += buildEnv["ZLIB_REDIST"]
        for node in zipNodes:
            zipSources.append((node.dir.abspath, node.name))
        nodes += stageEnv.Zip(File(os.path.join(publishDir, targetName)), zipSources)

    return nodes


def stageRTAVServerComponentTest(stageEnv):
    """Stage RTAV CI test packages and binaries for server side

    stageEnv specifies a stage environment object.
    """
    nodes = []
    targetName = ["rtavTestNodeServer"]

    extraZipSources = []
    rtavMsiNodes = buildCIMsiNodes(stageEnv, "RTAV", "Agent", copyNodeMap)
    if rtavMsiNodes is not None:
        extraZipSources.append((rtavMsiNodes[0].dir.abspath, rtavMsiNodes[0].name))

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(publishDir, "tests")
        deliverableNodesNames = ["rtavTestNodeServer"]
        testsSrc = "#bora/apps/rde/rtav/tests"
        deploySrc = testsSrc + "/testFramework/deploy/server"
        utilsSrc = testsSrc + "/testFramework/utils"
        serverUtilsSrc = testsSrc + "/testFramework/serverUtils"

        node = vmware.LookupNode("rtavTestNodeServer", "win32")
        extraZipSources.append((node[0].dir.abspath, node[0].name))
        extraZipSources.append((Dir(deploySrc).abspath, "."))
        extraZipSources.append((Dir(utilsSrc).abspath, "."))
        extraZipSources.append((Dir(serverUtilsSrc).abspath, "."))
        zipFilePath = os.path.join(publishDir, "rtavServerComponentTest.zip")
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetName,
            "win64",
            deliverableNodesNames,
            extraZipSources,
            zipFilePath,
        )
        if zipNode is not None:
            nodes += zipNode

    return nodes


def stageFabulatechJsonFile(stageEnv):
    nodes = []
    if not vmware.ReleasePackagesDir():
        return nodes
    FabulatechInPublishDir = stageEnv.Dir(
        os.path.join(vmware.ReleasePackagesDir(), "fabulatech")
    )
    stageEnv.Execute(vmware.Mkdir(FabulatechInPublishDir))
    arch_folder = "win64"
    for item in ["fabulatech_common", "fabulatech_scanner", "fabulatech_serialport"]:
        conanCommonDir = vmware.GetConanComponent(stageEnv, item).package_folder
        jsonFileSrc = stageEnv.File(
            os.path.join(
                conanCommonDir,
                "win",
                arch_folder,
                "fabulatech_drop_components_version.json",
            )
        )
        jsonFileDst = FabulatechInPublishDir.File(f"{item}_version.json")
        jsonFileCopy = stageEnv.LinkCopy(jsonFileDst, jsonFileSrc)
        nodes += jsonFileCopy
    return nodes


def stageUSBServerComponentTest(stageEnv):
    """stageUSBServerComponentTest

    Stage the Agent binaries for USB CI test.
    """

    nodes = []
    targetName = "usbTest"
    vmware.PathPrefixer(vmware.pkg.stagePath) / targetName

    publishDir = vmware.ReleasePackagesDir()
    deploySrc = "#bora/apps/viewusb/framework/usb/test/componenttest/deploy"
    testDataSrc = "#bora/apps/viewusb/framework/usb/test/componenttest/plugin/testData"
    if publishDir is not None:
        publishDir = os.path.join(publishDir, "tests")
        zipSources = []

        testNodes = vmware.pkg.LookupDeliverableNodes("usbTest", "win64")
        for node in testNodes:
            if node.name.startswith(
                (
                    "usbSessionConsoleTest",
                    "usbSessionLoaderTest",
                    "usbUdeTest",
                )
            ):
                zipSources.append((node.dir.abspath, node.name))

        zipSources.append((stageEnv.Dir(deploySrc).abspath, "usb_server_win_vdi.ini"))
        zipSources.append((stageEnv.Dir(deploySrc).abspath, "usb_server_win_rdsh.ini"))
        zipSources.append((stageEnv.Dir(testDataSrc).abspath, "deviceUtils.ps1"))
        zipSources.append((stageEnv.Dir(testDataSrc).abspath, "regUtils.ps1"))

        # Publish driver files
        drivers = {
            "hznflstor": "Win10",
            "hznufhid": "Win10",
            "vmwusbt": "Win10",
            "hznregreflection": "Win10",
            "hznvhub": "Win10",
        }
        for driver, osFolder in drivers.items():
            if vmware.BuildType() == "obj":
                driverBuildType = "Debug"
            else:
                driverBuildType = "Release"
            if driver == "vmwusbt":
                driverBuildDir = os.path.join(
                    vmware.DirAbsPath(vmware.BuildRoot()),
                    vmware.Product(),
                    driver,
                    osFolder + driverBuildType,
                    "x64",
                    "bin",
                )
            else:
                targetNode = vmware.LookupNode(driver, "win-kernel")
                for n in Flatten(targetNode):
                    if n.get_abspath().endswith(".pdb") and "x64" in n.abspath:
                        tmpDir = n.dir.abspath
                driverBuildDir = os.path.join(tmpDir, "microsoft-unsigned", "x64")
            for fileType in [
                ".inf",
                ".cat",
                ".pdb",
                ".sys",
            ]:
                file = driver + fileType
                zipSources.append((driverBuildDir, file))

        # Publish 3rd party dependency
        testEnv = vmware.LookupEnv("viewusb-env", "win64")
        testEnv.LoadTool(
            [
                "gettext",
                "libglib2",
                "libglibmm",
                "libiconv",
                "libpcre2",
                "libsigc",
            ]
        )
        deps = [
            "SIGC_REDIST",
            "GLIBMM_REDIST",
            "LIBICONV_REDIST",
            "GETTEXT_REDIST",
            "GLIB_REDIST",
            "PCRE2_REDIST",
        ]
        addDepsToZipSources(testEnv, deps, zipSources)

        # Use usbComponentTestServer.zip to adapt to the CI deploy script.
        zipFile = File(os.path.join(publishDir, "usbComponentTestServer.zip"))
        zipNode = stageEnv.Zip(zipFile, zipSources)
        nodes += zipNode
    return nodes


def stageUSBServerUnitTest(stageEnv):
    """stageUSBServerUnitTest

    Stage the binaries for Agent USB Unit Test.
    """

    nodes = []
    targetName = "usbTest"

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(publishDir, "tests")

        deploySrc = "#bora/apps/viewusb/framework/usb/test/unitTestSet"
        deploySrc = stageEnv.Dir(deploySrc).abspath

        zipSources = [
            (deploySrc, "usb_ut_deploy.py"),
            (deploySrc, "usb_ut_run_all_cases.py"),
            (deploySrc, "usb_ut_win.ini"),
        ]

        testNodes = vmware.pkg.LookupDeliverableNodes("usbTest", "win64")

        testLibs = [
            "usbWsVhubCommonTest",
            "usbWsVhubUnitTest",
            "usbWsVdpVhubUnitTest",
            "usbVhublibUnitTest",
            "usbRedirectionServerUnitTest",
            "usbWsUsbStorUnitTest",
        ]

        for node in testNodes:
            if node.name.startswith(tuple(testLibs)) and not node.name.endswith(
                (".lib", ".map")
            ):
                zipSources.append((node.dir.abspath, node.name))

        # other dependencies
        usbTestEnv = vmware.LookupEnv("viewusb-env", "win64")
        for redist in usbTestEnv["OPENSSL_REDIST"]:
            comp = os.path.split(redist)
            zipSources.append((comp[0], comp[1]))

        usbTestEnv.LoadTool(
            [
                "libsigc",
                "libglibmm",
                "gettext",
                "libglib2",
                "libpcre2",
            ]
        )

        deps = [
            "SIGC_REDIST",
            "GLIBMM_REDIST",
            "GETTEXT_REDIST",
            "GLIB_REDIST",
            "PCRE2_REDIST",
            "LIBICONV_REDIST",
        ]
        addDepsToZipSources(usbTestEnv, deps, zipSources)

        # MessageFrameWork.dll
        mfw_node = vmware.LookupNode("MessageFrameWork", host=stageEnv.Host().Name())[0]
        zipSources.append(((mfw_node.dir.abspath), mfw_node.name))

        zipFile = File(os.path.join(publishDir, "usbUnitTestServer.zip"))
        zipNode = stageEnv.Zip(zipFile, zipSources)
        nodes += zipNode

    return nodes


def publishZipFilesToDeliverablesPage(stageEnv, nodes, dirname, zipname):
    """
    Zip the files to the Deliverables page
    """
    zipNode = []
    publishDir = vmware.ReleasePackagesDir()
    if publishDir:
        zipSources = []
        for node in nodes:
            zipSources.append((node.dir.abspath, node.name))
        zipNode = stageEnv.Zip(
            File(os.path.join(publishDir, dirname, zipname)), zipSources
        )
    return zipNode


def stageUnityWin(stageEnv, copyNodeMap):
    """
    Stages all Unity-related files. These are not in exportsNodeMap because the
    current gobuild directory structure doesn't include the architecture, which
    exportsNodeMap forces.
    """
    guestUnityDir = os.path.join(vmware.pkg.stagePath, "GuestUnity")
    redistDir = os.path.join(guestUnityDir, "redist")

    # Stage all built dlls, exes, and pdbs.
    sourceNodes = [
        ("vmToolsHook", "win32"),
        ("vmToolsHook", "win64"),
        ("vmToolsHook", "win-arm64"),
        ("vmToolsHookProc", "win32"),
        ("vmToolsHookProc", "win64"),
        ("vmToolsHookProc", "win-arm64"),
    ]
    nodes = []
    for node, host in sourceNodes:
        unityNodes = vmware.pkg.StageDeliverables(stageEnv, node, host, redistDir)
        nodes += unityNodes
        vmware.Alias("%s-%s-stage" % (name, host), unityNodes)
        if name not in visualStudioProjectNodes:
            visualStudioProjectNodes.append(name)

    # Zip the files to the Deliverables page if needed.
    nodes += publishZipFilesToDeliverablesPage(
        stageEnv, nodes, "win-generic", "VMwareGuestUnity.zip"
    )

    # Build the Unity Touch msms.
    for arch in ["x64"]:
        wixEnv = stageEnv.Clone()
        wixEnv.Append(
            WIXDEFINES={
                "ARCH": arch,
            },
            WIXCANDLEFLAGS=[
                "-arch",
                arch,
                "-sw1006",
                "-sw1086",
            ],
            WIXLIGHTFLAGS=[
                "-sw1079",
            ],
        )
        filename = "VMwareUnityTouch%s" % arch
        msmBuildDir = os.path.join(
            vmware.DirAbsPath(vmware.BuildRoot()),
            vmware.BuildType(),
            "apps",
            "unityTouch",
            arch,
        )
        wixObjNode = wixEnv.Wixobj(
            Dir(msmBuildDir).File("%s.wixobj" % filename),
            "#bora/install/msi/msm/UnityTouch/UnityTouch.wxs",
        )
        wixBuildDir = Dir(os.path.join(guestUnityDir, arch))
        nodes += wixEnv.Wixmsm(wixBuildDir.File("%s.msm" % filename), [wixObjNode])

    return nodes


def stageRdeServerComponentTest(stageEnv):
    """stageRdeServerComponentTest

    Stage the binaries for RdeServer Component Test.
    Please note currently it is done for the Windows platform.
    In the future we will add support for other platforms.
    """

    nodes = []
    targetName = "rdeServerTest"
    vmware.PathPrefixer(vmware.pkg.stagePath) / targetName

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(publishDir, "tests")
        for host in ["win64"]:
            # Publish rdeServerTest package
            zipSources = []

            # Gather the list of all dependencies to copy.
            testEnv = vmware.LookupEnv("rdeServerTest-env", host)
            deps = [
                "SIGC_REDIST",
                "GLIBMM_REDIST",
                "GETTEXT_REDIST",
                "REDISTRIBUTE_MFW_DLL",
                "GLIB_REDIST",
                "PCRE2_REDIST",
                "ZLIB_REDIST",
                "GTEST_REDIST",
                "LIBICONV_REDIST",
                "LIBPNG_REDIST",
            ]
            addDepsToZipSources(testEnv, deps, zipSources)

            toTestNode = []
            toTestNode += vmware.pkg.LookupDeliverableNodes("rdeServerTest", host)
            for node in toTestNode:
                zipSources.append((node.dir.abspath, node.name))

            for node in vmware.pkg.LookupDeliverableNodes("blastSetTopology", host):
                zipSources.append((node.dir.abspath, node.name))

            for node in vmware.pkg.LookupDeliverableNodes("inputdevtap", host):
                zipSources.append((node.dir.abspath, node.name))

            for node in vmware.pkg.LookupDeliverableNodes("hznime", host):
                zipSources.append((node.dir.abspath, node.name))

            scriptDir = Dir(
                "#bora/apps/rde/rdeSvc/tests/componentTest/rdeServerTest/scripts"
            )
            zipSources.append((scriptDir.abspath, "testimage.png"))
            zipSources.append((scriptDir.abspath, "rdeServerTest_deploy.ps1"))
            zipSources.append((scriptDir.abspath, "rdeServerTest_runcase.ps1"))
            zipSources.append((scriptDir.abspath, "rdeServerTest_undeploy.ps1"))
            zipSources.append(
                (scriptDir.abspath, "rdeServerUnityTouchTest_runcase.ps1")
            )

            # Becare following nodes, they are copied to same folder, so
            # nodes which have same file name may be overriden.
            sourceNodes = [
                ("unityShell", host),
                ("vmToolsHook", "win32"),
                ("vmToolsHook", "win64"),
                ("vmToolsHook", "win-arm64"),
                ("vmToolsHookProc", "win32"),
                ("vmToolsHookProc", "win64"),
                ("vmToolsHookProc", "win-arm64"),
            ]
            for module, unityHost in sourceNodes:
                unityNodes = vmware.pkg.LookupDeliverableNodes(module, unityHost)
                for node in unityNodes:
                    zipSources.append((node.dir.abspath, node.name))

            # Use rdeServerComponentTest.zip to adapt to the CI deploy script.
            testHost = vmware.HostObject(host)
            buildDir = (
                stageEnv.Copy().Dir(testHost.ComponentBuildPath(RDETEST_NAME)).abspath
            )

            stageDest = os.path.join(buildDir, "rdetmp")

            stageFilterDest = os.path.join(stageDest, "Unity Filters")
            filterSourceDir = vmware.DirAbsPath("#bora-vmsoft/lib/unityFilter/filters")
            nodes += stageEnv.FileCopy(
                Dir(stageFilterDest).File("horizonfilters.txt"),
                Dir(filterSourceDir).File("horizonfilters.txt"),
            )

            scriptDir = Dir(stageDest)
            zipSources.append((scriptDir.abspath, "Unity Filters"))

            # publish windows dependencies
            if vmware.BuildType() == "obj":
                rootDir = stageEnv["MSVC_NON_REDISTS_ROOT"]
                subDir = "Microsoft.VC140.DebugCRT"
                srcFiles = [
                    "msvcp140d_atomic_wait.dll",
                ]
            else:
                rootDir = stageEnv["MSVC_REDISTS_ROOT"]
                subDir = "Microsoft.VC140.CRT"
                srcFiles = [
                    "msvcp140_atomic_wait.dll",
                ]
            if host == "win32":
                arch = "x86"
            else:
                arch = "x64"
            fullPath = os.path.join(rootDir, "win", "dll", arch, subDir)
            for file in srcFiles:
                zipSources.append((fullPath, file))
            # zip & publish
            zipFile = File(os.path.join(publishDir, host, RDETEST_NAME + ".zip"))
            zipNode = stageEnv.Zip(zipFile, zipSources)
            nodes += zipNode

    return nodes


def stageSdrServerComponentTest(stageEnv):
    """Stage storage drive binaries for Component Test.

    stageEnv specifies a stage environment object.
    """
    nodes = []

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is None:
        return nodes

    host = "win64"
    arch = "x64"

    zipNodes = []
    zipNodes += vmware.LookupNode("rdpvcbridge", host)
    zipNodes += vmware.LookupNode("omnrxgservice", host)
    zipNodes += vmware.LookupNode("sdrserviceplugin", host)
    zipNodes += vmware.LookupNode("sdrserverutil", host)
    zipNodes += SConscript("../viewdrivers/msi/omnsdr.py", exports=["arch"])

    zipSources = []
    for zipNode in zipNodes:
        zipSources.append((zipNode.dir.abspath, zipNode.name))
    zipFile = os.path.join(publishDir, "tests", host, "sdrServerTest.zip")
    zipNode = stageEnv.Zip(zipFile, zipSources)
    nodes += zipNode

    return nodes


def stageClipboardServerComponentTest(stageEnv):
    """stageClipboardServerComponentTest
    Stage the binaries for clipboard Component Test.
    """

    nodes = []
    targetName = ["mksvchanComponentTest"]

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(publishDir, "tests")
        deliverableNodesNames = [
            "mksvchanserver",
            "vdpservice",
            "fcpComponentTestVerifier",
        ]
        extraZipSources = []
        extraZipSources.append(
            (
                stageEnv.Dir(
                    "#bora/apps/rde/mksvchan/tests/componenttest/" "clipboard"
                ).abspath,
                "mksvchan_server_win.ini",
            )
        )

        testDataSrc = (
            "#bora/apps/rde/mksvchan/tests/componenttest/" "clipboard/testData"
        )
        extraZipSources.append((Dir(testDataSrc).abspath, "."))

        # Gather the list of all dependencies to copy.
        testEnv = vmware.LookupEnv("mksvchanComponentTest-env", "win64")
        deps = [
            "SIGC_REDIST",
            "GLIBMM_REDIST",
            "GETTEXT_REDIST",
            "ZLIB_REDIST",
            "GLIB_REDIST",
            "PCRE2_REDIST",
            "LIBICONV_REDIST",
            "LIBPNG_REDIST",
        ]
        addDepsToZipSources(testEnv, deps, extraZipSources)

        zipFilePath = os.path.join(
            publishDir, "win64", "mksvchanComponentTestServer.zip"
        )
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetName,
            "win64",
            deliverableNodesNames,
            extraZipSources,
            zipFilePath,
        )
        if zipNode is not None:
            nodes += zipNode

    return nodes


def stageHtml5mmrServerTest(stageEnv):
    """html5mmrServerTest
    Stage the binaries for Html5mmrServerTest
    """

    nodes = []
    targetName = ["html5mmrServerTest"]

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(publishDir, "tests")
        deliverableNodesNames = [
            "html5mmrServerTest",
            "vdpservice",
        ]
        zipSources = []
        # Gather the list of all dependencies to copy.
        testEnv = vmware.LookupEnv("html5mmrServerTest-env", "win64")
        deps = [
            "GETTEXT_REDIST",
            "GLIB_REDIST",
            "GLIBMM_REDIST",
            "GTEST_REDIST",
            "LIBICONV_REDIST",
            "OPENSSL_REDIST",
            "PCRE2_REDIST",
            "SIGC_REDIST",
        ]
        addDepsToZipSources(testEnv, deps, zipSources)

        zipFilePath = os.path.join(publishDir, "win64", "html5mmrServerTest.zip")
        excludedFileFormats = [".map"]
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetName,
            "win64",
            deliverableNodesNames,
            zipSources,
            zipFilePath,
            excludedFileFormats,
        )
        if zipNode is not None:
            nodes += zipNode

    return nodes


def stageSmartcardServerComponentTest(stageEnv):
    """stageSmartcardServerComponentTest

    Stage the binaries for Smartcard Component Test.
    """

    nodes = []
    targetNames = ["scredirvchanTestApp"]

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir += "/tests"

        deliverableNodesNames = ["scredirvchanTestApp"]

        extraZipSources = []
        targetPath = Dir("#bora/apps/rde/scredirvchan/tests/componenttest").abspath
        extraZipSources.append((targetPath, "scredirvchan_server_win_rdsh.ini"))
        extraZipSources.append((targetPath, "scredirvchan_server_win_vdi.ini"))

        # publish drivers
        driverNames = ["hznicpdr", "hznvscrd"]

        for driverName in driverNames:
            driverNodes = vmware.LookupNode(driverName, host="win-kernel")
            for file in Flatten(driverNodes):
                if (
                    "Win7Release" in file.abspath or "Win8Release" in file.abspath
                ) and "x64" in file.abspath:
                    extraZipSources.append((file.dir.abspath, file.name))

        host = "win64"
        zipFilePath = os.path.join(
            publishDir, host, "scredirvchanComponentTestServer.zip"
        )
        excludedFileFormats = [".map"]
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetNames,
            host,
            deliverableNodesNames,
            extraZipSources,
            zipFilePath,
            excludedFileFormats,
        )
        nodes += zipNode

    return nodes


def stageMKSVchanDnDComponentTest(stageEnv):
    """stageMKSVchanDnDComponentTest
    Stage the binaries for dnd Component Test.
    """
    nodes = []
    targetName = ["mksvchanDnDComponentTest"]
    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(publishDir, "tests")
        deliverableNodesNames = ["mksvchanDnDComponentTest", "dndComponentTestVerifier"]
        extraZipSources = []
        extraZipSources.append(
            (
                stageEnv.Dir("#bora/apps/rde/mksvchan/tests/componenttest/dnd").abspath,
                "mksvchanDnD_server_win.ini",
            )
        )

        stageEnv.LoadTool(["artifactory"])
        testDataNode = stageEnv.FetchFromArtifactory(
            "horizon-cart-local/remote-experience/dnd/testFiles.zip",
            extract=True,
            extractTargetFiles=["testFiles/DnDTestFile.txt"],
        )
        testDataPath = os.path.dirname(os.path.dirname(testDataNode[0].abspath))
        extraZipSources.append((testDataPath, "."))

        vmware.RegisterNode(testDataNode, "dnd-testdata", host="win64", space="build")

        env = vmware.LookupEnv("mksvchanDnDComponentTest-env", "win64")
        zlib = env["ZLIB_REDIST"][0].abspath
        extraZipSources.append(
            (os.path.dirname(os.path.abspath(zlib)), os.path.basename(zlib))
        )
        env.LoadTool(["libiconv"])
        addDepsToZipSources(env, ["LIBICONV_REDIST", "LIBPNG_REDIST"], extraZipSources)

        zipFilePath = File(
            os.path.join(publishDir, "win64", "mksvchanDnDComponentTestServer.zip")
        )
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetName,
            "win64",
            deliverableNodesNames,
            extraZipSources,
            zipFilePath,
        )
        stageEnv.Depends(zipNode, testDataNode)

        if zipNode is not None:
            nodes += zipNode
    return nodes


def stageTsdrComponentServerTest(stageEnv):
    """stageTsdrComponentServerTest

    Stage the binaries for Tsdr Component Server Test.
    Please note currently it is done for the Windows platform.
    In the future we will add support for other platforms.
    """

    nodes = []
    targetNames = ["tsdrComponentTest"]

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(publishDir, "tests")

        deliverableNodesNames = ["vdpservice"]
        extraZipSources = []
        targetPath = Dir("#bora/apps/rde/tsdr/tests/componenttest").abspath
        extraZipSources.append((targetPath, "tsdr_server_win_rdsh.ini"))
        extraZipSources.append((targetPath, "tsdr_server_win_vdi.ini"))
        extraZipSources.append((targetPath, "SetViewSessionType.bat"))

        # Publish 3-part dependency
        testEnv = vmware.LookupEnv("tsdrComponentTest-env", "win64")
        deps = [
            "SIGC_REDIST",
            "GLIBMM_REDIST",
            "GETTEXT_REDIST",
            "GLIB_REDIST",
            "PCRE2_REDIST",
        ]
        addDepsToZipSources(testEnv, deps, extraZipSources)

        # driver
        driverName = "hzncdrfilter"
        driverNodes = vmware.LookupNode(driverName, host="win-kernel")
        for file in Flatten(driverNodes):
            if "Win8.1Release" in file.abspath and "x64" in file.abspath:
                extraZipSources.append((file.dir.abspath, file.name))

        # Publish tsdrComponentTest package
        zipFilePath = os.path.join(publishDir, "win64", "tsdrComponentTestServer.zip")
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetNames,
            "win64",
            deliverableNodesNames,
            extraZipSources,
            zipFilePath,
        )
        if zipNode is not None:
            nodes += zipNode

    return nodes


def stageTSMMRServerComponentTestWindows(stageEnv):
    """stageTSMMRServerComponentTestWindows

    Stage the binaries for tsmmr Component Test.
    Please note this feature is only support for Windows platform.
    """

    nodes = []
    targetNames = ["tsmmrComponentTest"]

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(publishDir, "tests")
        # Zip vdpservice and tsmmrServer + wmplayer.exe
        deliverableNodesNames = [
            "wmplayer",
            "tsmmrServer",
            "vdpservice",
            "tsmmrServerDShow",
        ]
        host = "win64"
        extraZipSources = []
        extraZipSources.append(
            (
                stageEnv.Dir(
                    "#bora/apps/rde/tsmmr/tests/componenttest/" "deployScript"
                ).abspath,
                "tsmmr_server_win.ini",
            )
        )

        # driver
        driverNames = ["hznicpdr", "hznvaudio"]
        for driverName in driverNames:
            driverNodes = vmware.LookupNode(driverName, host="win-kernel")
            for file in Flatten(driverNodes):
                if (
                    "Win8Release" in file.abspath or "Win8.1Release" in file.abspath
                ) and "x64" in file.abspath:
                    extraZipSources.append((file.dir.abspath, file.name))

        zipFilePath = os.path.join(publishDir, host, "tsmmrComponentTestServer.zip")
        excludedFileFormats = [".map"]
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetNames,
            host,
            deliverableNodesNames,
            extraZipSources,
            zipFilePath,
            excludedFileFormats,
        )
        if zipNode is not None:
            nodes += zipNode

    return nodes


def stageDnDUt(stageEnv):
    """stageDnDUt

    Stage the binaries for DnD server UT.
    Please note currently it is done for the Windows platform.
    In the future we will add support for other platforms.
    """

    nodes = []
    targetName = "dndServerUnitTest"
    vmware.PathPrefixer(vmware.pkg.stagePath) / targetName
    stageEnv.LoadTool("zip-3.0")

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(publishDir, "tests")
        host = "win64"
        # Publish DnD UT package
        zipSources = []
        utNodes = vmware.pkg.LookupDeliverableNodes("dndServerUnitTest", host)
        for node in utNodes:
            zipSources.append((node.dir.abspath, node.name))

        # Gather the list of all dependencies to copy.
        testEnv = vmware.LookupEnv("dndServerUnitTest-env", host)
        deps = [
            "SIGC_REDIST",
            "GLIBMM_REDIST",
            "GETTEXT_REDIST",
            "ZLIB_REDIST",
            "GLIB_REDIST",
            "PCRE2_REDIST",
            "GTEST_REDIST",
            "LIBICONV_REDIST",
            "LIBPNG_REDIST",
        ]
        addDepsToZipSources(testEnv, deps, zipSources)

        for filename in [
            "testPDF.pdf",
            "testPNG.png",
            "testMP4.mp4",
            "testTXT.txt",
        ]:
            zipSources.append(
                (
                    stageEnv.Dir("#bora/apps/rde/mksvchan/UT/" "testfiles").abspath,
                    filename,
                )
            )

        testDataNode = vmware.LookupNode("dnd-testdata", host=host)
        testDataPath = os.path.dirname(os.path.dirname(testDataNode[0].abspath))
        zipSources.append((testDataPath, "."))

        # Use dndUT.zip to adapt to the CI deploy script.
        # Now the script only download the zip file with
        # name *UT.zip.
        zipNode = stageEnv.Zip(
            File(os.path.join(publishDir, host, "dndUT.zip")), zipSources
        )
        stageEnv.Depends(zipNode, utNodes + testDataNode)
        nodes += zipNode

    return nodes


def stageScreenCaptureUnitTest(stageEnv):
    """ScreenCaptureUnitTest

    Stage the binaries for Screen Capture Unit Test.
    """

    nodes = []
    targetName = [
        "screenCaptureServerUt",
        "vdpservice",
        "inputdevtap",
    ]

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(publishDir, "tests")
        extraZipSources = []
        host = "win64"
        testEnv = vmware.LookupEnv("screenCaptureServerUt-env", host)
        deps = [
            "SIGC_REDIST",
            "GLIBMM_REDIST",
            "GETTEXT_REDIST",
            "REDISTRIBUTE_MFW_DLL",
            "GLIB_REDIST",
            "PCRE2_REDIST",
            "ZLIB_REDIST",
            "LIBICONV_REDIST",
            "LIBPNG_REDIST",
        ]
        addDepsToZipSources(testEnv, deps, extraZipSources)

        zipFilePath = os.path.join(publishDir, host, "screenCaptureServerRdeUt.zip")
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetName,
            host,
            targetName,
            extraZipSources,
            zipFilePath,
            excludedFileFormats=[".map"],
        )
        if zipNode is not None:
            nodes += zipNode

    return nodes


def stageClipboardServerUnitTest(stageEnv):
    """stageClipboardServerUnitTest
    Stage the binaries for clipboard Unit Test.
    """

    nodes = []
    targetName = ["clipboardServerUnitTest"]

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(publishDir, "tests")

        extraZipSources = []
        host = "win64"

        # Gather the list of all dependencies to copy.
        testEnv = vmware.LookupEnv("clipboardServerUnitTest-env", host)
        deps = [
            "SIGC_REDIST",
            "GLIBMM_REDIST",
            "GETTEXT_REDIST",
            "GLIB_REDIST",
            "PCRE2_REDIST",
            "ZLIB_REDIST",
            "OPENSSL_REDIST",
            "LIBICONV_REDIST",
            "LIBPNG_REDIST",
        ]
        addDepsToZipSources(testEnv, deps, extraZipSources)

        if vmware.BuildType() == "obj":
            msvcNonRDir = stageEnv["MSVC_NON_REDISTS_ROOT"]
            mfcFilePath = os.path.join(
                msvcNonRDir,
                "win",
                "dll",
                "x64",
                "Microsoft.VC140.DebugMFC",
                "mfc140ud.dll",
            )
            extraZipSources.append(os.path.split(mfcFilePath))

        zipFilePath = os.path.join(publishDir, host, "clipboardServerUnitTest.zip")
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv, targetName, host, targetName, extraZipSources, zipFilePath
        )
        if zipNode is not None:
            nodes += zipNode

    return nodes


def stageFCPServerUnitTest(stageEnv):
    """stageFCPServerUnitTest
    Stage the binaries for fcp Unit Test.
    """

    nodes = []
    targetName = ["fcpServerUnitTest"]

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(publishDir, "tests")

        extraZipSources = []
        host = "win64"

        # Gather the list of all dependencies to copy.
        testEnv = vmware.LookupEnv("fcpServerUnitTest-env", host)
        deps = [
            "SIGC_REDIST",
            "GLIBMM_REDIST",
            "GETTEXT_REDIST",
            "GLIB_REDIST",
            "PCRE2_REDIST",
            "ZLIB_REDIST",
            "OPENSSL_REDIST",
            "LIBICONV_REDIST",
            "LIBPNG_REDIST",
        ]
        addDepsToZipSources(testEnv, deps, extraZipSources)

        if vmware.BuildType() == "obj":
            msvcNonRDir = stageEnv["MSVC_NON_REDISTS_ROOT"]
            mfcFilePath = os.path.join(
                msvcNonRDir,
                "win",
                "dll",
                "x64",
                "Microsoft.VC140.DebugMFC",
                "mfc140ud.dll",
            )
            extraZipSources.append(os.path.split(mfcFilePath))

        zipFilePath = os.path.join(publishDir, host, "fcpServerUnitTest.zip")
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetName,
            host,
            targetName,
            extraZipSources,
            zipFilePath,
            excludedFileFormats=[".map"],
        )
        if zipNode is not None:
            nodes += zipNode

    return nodes


def stageCommonDrivers(stageEnv):
    """stageCommonDrivers
    Stage common driver that needs publishing.
    """

    nodes = []

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(publishDir, "drivers")

        # Drivers
        driverNames = [
            "hzncdrfilter",
            "hznicpdr",
            "hznflstor",
            "hzngeoloc",
            "hznrdsaudio",
            "hznrdsdd",
            "hznrdsinput",
            "hznregreflection",
            "hznsdo",
            "hznufhid",
            "hznvaudio",
            "hznvdisplay",
            "hznvhub",
            "hznvscrd",
            "hznvudpd",
            "hznvidd",
            "hznvidd2",
            "vmkbd",
            "hznvaudioin",
            "hznvwebcam",
            "omnsdr",
        ]

        extraZipSourcesWin7 = []
        extraZipSourcesWin8 = []
        extraZipSourcesWin10 = []
        extraZipSourcesWin10Arm64 = []

        for driverName in driverNames:
            driverNodes = vmware.LookupNode(driverName, host="win-kernel")
            for file in Flatten(driverNodes):
                if ("Win7Release" in file.abspath) and "x64" in file.abspath:
                    extraZipSourcesWin7.append((file.dir.abspath, file.name))

                if (
                    "Win8Release" in file.abspath or "Win8.1Release" in file.abspath
                ) and "x64" in file.abspath:
                    extraZipSourcesWin8.append((file.dir.abspath, file.name))

                if ("Win10Release" in file.abspath) and "x64" in file.abspath:
                    extraZipSourcesWin10.append((file.dir.abspath, file.name))

                if ("Win10Release" in file.abspath) and "arm64" in file.abspath:
                    extraZipSourcesWin10Arm64.append((file.dir.abspath, file.name))

        zipFilePath = os.path.join(publishDir, "Horizon-Drivers-x64-Win7.zip")
        driverHost = "win-kernel"

        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            [],
            driverHost,
            [],
            extraZipSourcesWin7,
            zipFilePath,
            excludedFileFormats=[".map"],
        )

        nodes += zipNode

        zipFilePath = os.path.join(publishDir, "Horizon-Drivers-x64-Win8.zip")
        driverHost = "win-kernel"

        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            [],
            driverHost,
            [],
            extraZipSourcesWin8,
            zipFilePath,
            excludedFileFormats=[".map"],
        )

        nodes += zipNode

        zipFilePath = os.path.join(publishDir, "Horizon-Drivers-x64-Win10.zip")
        driverHost = "win-kernel"

        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            [],
            driverHost,
            [],
            extraZipSourcesWin10,
            zipFilePath,
            excludedFileFormats=[".map"],
        )

        nodes += zipNode

        zipFilePath = os.path.join(publishDir, "Horizon-Drivers-arm64-Win10.zip")
        driverHost = "win-kernel"

        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            [],
            driverHost,
            [],
            extraZipSourcesWin10Arm64,
            zipFilePath,
            excludedFileFormats=[".map"],
        )

        nodes += zipNode

    return nodes


def buildPerfTrackerMsm(pfStageEnv, buildDir, perfTrackerName):
    """Build and stage Msm package, Because PerfTracker is integrated into
    Agent Installer, MSI and standalone installer are not needed.
    """
    nodes = []
    installPP = vmware.PathPrefixer("#bora/apps/rde/perfTracker/Installer")
    baseDir = pfStageEnv.Dir(vmware.phase.ComponentDir(perfTrackerName))

    vmware.pkg.stagePath = vmware.StageRoot()
    vmware.pkg.packagePP = vmware.PathPrefixer(vmware.pkg.stagePath)
    stagePP = vmware.pkg.packagePP / perfTrackerName

    # Stage perfTracker DLL and EXE to a temporary directory
    tmpDir = os.path.join(buildDir, "pfTmp")
    stageNode = vmware.pkg.StageDeliverables(
        pfStageEnv, perfTrackerName, "win64", tmpDir
    )
    nodes += stageNode

    env = pfStageEnv.Copy()
    msmName = "VMwareHorizonPerfTracker64"
    env.Append(
        WIXDEFINES={
            "BUILDROOT": tmpDir,
            "VERSION": vmware.ProductVersionNumber(),
        },
        WIXCANDLEFLAGS=[
            "-ext",
            "WixUIExtension",
            "-arch",
            "x64",
        ],
        WIXLIGHTFLAGS=[
            "-ext",
            "WixUIExtension",
            "-spdb",
            "-sw1076",
        ],
    )

    msmObj = env.Wixobj(
        baseDir.File("%s_msm.wixobj" % msmName),
        installPP.File("VMware.Horizon.PerfTracker.Msm/perfTrackerMsm.wxs"),
    )

    msm = env.Wixmsm(baseDir.File("%s.msm" % msmName), msmObj)
    env.SignFile(msm)
    nodes += msm
    env.Depends(msm, stageNode)
    nodes += env.FileCopyEx(stagePP.Dir("x64"), msm)
    return nodes


def publishPerfTracker(env, buildDir, publishPP, perfTrackerName):
    """Publish perfTracker, including msm file"""
    nodes = []
    zipSources = []
    deliverableNodes = vmware.pkg.LookupDeliverableNodes(perfTrackerName, "win64")
    for node in deliverableNodes:
        zipSources.append((node.dir.abspath, node.name))
    zipFile = env.File(publishPP / "win-generic" / ("%s.zip" % perfTrackerName))
    nodes = env.Zip(zipFile, zipSources)

    return nodes


def stagePerfTrackerComponentTest(env, buildDir, publishPP):
    """Stage deliverables of component test"""
    TEST_NAME = "perfTrackerComponentTest"
    nodes = []
    zipSources = []

    deliverableNodes = vmware.pkg.LookupDeliverableNodes(TEST_NAME, "win64")
    for node in deliverableNodes:
        zipSources.append((node.dir.abspath, node.name))
    deliverableNodes = vmware.pkg.LookupDeliverableNodes("WaveDllMock", "win64")
    for node in deliverableNodes:
        zipSources.append((node.dir.abspath, node.name))
    zipSources.append(
        (env.Dir("#bora/apps/rde/perfTracker").abspath, "TestSettings_x64.testsettings")
    )
    zipSources.append(
        (
            env.Dir("#bora/apps/rde/perfTracker/componenttest").abspath,
            "trx-to-junit.xslt",
        )
    )
    zipSources.append(
        (
            env.Dir("#bora/apps/rde/perfTracker/componenttest").abspath,
            "perftracker_server_win.ini",
        )
    )
    zipSources.append(
        (
            env.Dir("#bora/apps/rde/perfTracker/componenttest").abspath,
            "perftracker_run_cases.bat",
        )
    )

    zipFile = env.File(publishPP / "tests" / "win64" / ("%s.zip" % TEST_NAME))
    nodes += env.Zip(zipFile, zipSources)

    return nodes


def buildAndPublishPerfTrackerAll(stageEnv):
    """Publish perfTracker and test suite, including msm file"""
    nodes = []
    PERFTRACKER_NAME = "perfTracker"
    pfStageEnv = stageEnv.Copy()

    pfStageEnv.LoadTool(["wix", "linkcopy", "zip-3.0"])
    pfStageEnv.Replace(FILECOPYEX_COPIER=pfStageEnv.LinkCopy)

    pfStageEnv.LoadTool("digitalsign")

    pfHost = vmware.HostObject("win64")
    buildDir = pfStageEnv.Dir(pfHost.ComponentBuildPath(PERFTRACKER_NAME)).abspath
    nodes = buildPerfTrackerMsm(pfStageEnv, buildDir, PERFTRACKER_NAME)
    if vmware.ReleasePackagesDir():
        publishPP = vmware.PathPrefixer(vmware.ReleasePackagesDir())
        nodes += publishPerfTracker(pfStageEnv, buildDir, publishPP, PERFTRACKER_NAME)

        # This is commented out while we are migrating these tests from the legacy test tools
        # to Nuget packages `MSTest.TestFramework` and `MSTest.TestAdapter`.
        # nodes += stagePerfTrackerComponentTest(pfStageEnv, buildDir, publishPP)
    return nodes


def addNodesToCopyMap(copyNodeMap, name, stageNodes):
    """
    Adds copy nodes to the map which stores the various pending LinkCopy nodes.
    This allows installers to add dependencies so they only execute after the
    files are copied.
    """
    if name in copyNodeMap:
        copyNodeMap[name] += stageNodes
    else:
        copyNodeMap[name] = stageNodes


def stageBinariesForExportNodes(stageEnv, hosts, publishDir, exportNodes, copyNodeMap):
    """
    Publishes binaries to specify folder.
    """
    stagePP = vmware.PathPrefixer(vmware.pkg.stagePath)
    nodes = []
    crossBuildEnv = None

    for name in exportNodes:
        for host in hosts:
            try:
                vmware.LookupNode(name, host=host, beQuieter=True, missingOk=True)
            except vmware.ScriptError:
                log.info(
                    "There is no %s build of %s, so will not zip it." % (host, name)
                )
                continue
            stagePath = Dir(stagePP / hosts[host]).abspath
            log.debug("Staging %s[%s] to %s." % (name, host, stagePath))
            stageNodes = vmware.pkg.StageDeliverables(
                stageEnv, name, host, stagePath, crossBuildEnv=crossBuildEnv
            )
            # Remember this info for later inclusion in the Visual Studio
            # project generation target.
            vmware.Alias("%s-%s-stage" % (name, host), stageNodes)
            if name not in visualStudioProjectNodes:
                visualStudioProjectNodes.append(name)

            if publishDir:
                # Zip it to the Deliverables page. Include zip in stage nodes
                # to avoid conflicts between zip and wix.
                stageNodes += stageEnv.Zip(
                    os.path.join(publishDir, host, name + ".zip"),
                    [(node.dir.abspath, node.name) for node in stageNodes],
                )

            nodes += stageNodes

            # Allow for later access of stage nodes if needed.
            addNodesToCopyMap(copyNodeMap, name, stageNodes)
    return nodes


def stageUrlNativeMessageManifest(stageEnv):
    nodes = []
    targetName = "urlRedirection"
    stagePP = vmware.PathPrefixer(vmware.pkg.stagePath) / targetName
    stageEnv.LoadTool("zip-3.0")

    manifestSrc = (
        "#bora/apps/rde/urlRedirection/urlNativeMessageHost/"
        "horizon-url-native-host-manifest.json"
    )
    for host in hostMapPublish:
        # Stage urlRedirection Native Message manifest json
        manifestDest = stagePP / hostMapPublish[host]
        nodes += stageEnv.LinkCopy(
            File(manifestDest / "horizon-url-native-host-manifest.json"), manifestSrc
        )

        # Installer path for manifest.json
        manifestDestInstall = os.path.join(installerStagePath, hostMapPublish[host])
        nodes += stageEnv.LinkCopy(
            File(
                os.path.join(
                    manifestDestInstall, "horizon-url-native-host-manifest.json"
                )
            ),
            manifestSrc,
        )

    return nodes


def stageUrlRedirectionWindows(stageEnv):
    nodes = []
    targetName = "urlRedirection"
    stageEnv.LoadTool("zip-3.0")
    # Zip urlRedirection extension in the Deliverables page.
    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        for host in hostMapPublish:
            # Publish chrome extensions
            extensionSrc = "#bora/apps/rde/urlRedirection/extensions"
            zipFile = File(
                os.path.join(publishDir, host, targetName + "-extensions.zip")
            )
            zipNodes = stageEnv.ZipDir(zipFile, Dir(extensionSrc), ".")
            nodes += zipNodes
    return nodes


def stageUriRdLsaSignFiles(stageEnv):
    stageEnv.LoadTool(
        [
            "digitalsign",
            "makecab",
        ]
    )

    nodes = []
    productBuildRoot = os.path.join(
        vmware.DirAbsPath(vmware.BuildRoot()), vmware.BuildType(), vmware.Product()
    )
    msUnsigned = Dir(publishDir).Dir("microsoft-unsigned")

    for name in signedUriCabNodes:
        nodeName = name.replace("-signed", "")
        pluginName = signedUriCabNodes[name][1]
        msUnsignedNodes = []

        if vmware.BuildType() == "release" or vmware.BuildType() == "beta":
            useSigned = True if pluginName in vmware.pkg.frozenFilesDetails else False
        else:
            useSigned = False

        for host in hostMapInstaller:
            try:
                vmware.LookupNode(nodeName, host=host, beQuieter=True, missingOk=True)
            except vmware.ScriptError:
                log.info(
                    "There is no %s build of %s, we will not stage it."
                    % (host, nodeName)
                )
                continue

            destPath = os.path.join(installerStagePath, hostMapInstaller[host])
            # The check code is to avoid the same node.

            if pluginName in vmware.pkg.frozenFilesDetails:
                stageEnv.LoadTool("artifactory")
                if pluginName == "hznurlfilter":
                    fileName = "horizon-url-protocol-intercept"
                else:
                    fileName = pluginName

                if host == "win-arm64":
                    hostPath = "arm64ec"
                else:
                    hostPath = hostMapInstaller[host]

                # downloadTarget needs to be overridden or else SCons will error
                # out and say that different environments were used to run the
                # same action.
                downloadTarget = os.path.join(
                    productBuildRoot, "artifactory", f"{fileName}-{host}.zip"
                )

                frozenFilesExtractNode = stageEnv.FetchFromArtifactory(
                    artifactorySignedFileRoot
                    + vmware.pkg.frozenFilesDetails[pluginName]["file"],
                    sha256=vmware.pkg.frozenFilesDetails[pluginName]["sha256"],
                    downloadTarget=downloadTarget,
                    extract=True,
                    extractTargetFiles=[
                        "Win8.1Release/{}/{}.dll".format(hostPath, fileName)
                    ],
                    registerNodeName=vmware.pkg.frozenFilesDetails[pluginName]["file"]
                    + host,
                )
                frozenPath = os.path.dirname(frozenFilesExtractNode[0].abspath)

            if useSigned:
                stagePath = vmware.DirAbsPath(Dir(msUnsigned).Dir(host))
            else:
                stagePath = vmware.DirAbsPath(destPath)
            unsignedNodes = vmware.pkg.StageDeliverables(
                stageEnv, nodeName, host, stagePath
            )
            stageEnv.Depends(unsignedNodes, frozenFilesExtractNode)
            if nodeName not in visualStudioProjectNodes:
                visualStudioProjectNodes.append(nodeName)
            msUnsignedNodes += unsignedNodes
            vmware.RegisterNode(unsignedNodes, name, host=host, space="build")

            # Zip the dll to publish directory.
            if publishDir:
                stageNodes = stageEnv.Zip(
                    os.path.join(publishDir, host, nodeName + ".zip"),
                    [(node.dir.abspath, node.name) for node in unsignedNodes],
                )
                stageEnv.Depends(stageNodes, unsignedNodes)
                nodes += stageNodes

            # Stage the frozen files if useSigned is True and the frozen folder
            # exist, else stage the unsigned files.
            if useSigned:
                nodes += vmware.DirCopy(
                    frozenFilesExtractNode, Dir(frozenPath), Dir(destPath), stageEnv
                )
            else:
                nodes += unsignedNodes

        # Generate cab file and publish them.
        cabNode = stageEnv.StageAndGenerateCabFiles(
            productBuildRoot, {name: signedUriCabNodes[name][0]}, nodeName
        )
        stageEnv.Depends(cabNode, msUnsignedNodes)
        # stage to microsoft-unsigned/host/
        cabFullPathName = msUnsigned.File(cabNode[0].name)
        nodes += stageEnv.LinkCopy(cabFullPathName, cabNode[0])
        stageEnv.SignFile(cabFullPathName)
    return nodes


def stageWhfbRedirectionTestZip(stageEnv, host, publishDir):
    """Stages publish windows hello for business unit test zip"""
    nodeName = "whfbUnitTest"
    publishDirPP = vmware.PathPrefixer(publishDir)
    whfbUnitTestZipFile = Dir(publishDirPP / "tests" / host).File("%s.zip" % (nodeName))

    extrazipSources = []
    buildEnv = vmware.LookupEnv("whfbUnitTest-env", host)
    deps = buildEnv["GTEST_REDIST"]
    for lib in deps:
        f = stageEnv.File(lib)
        extrazipSources.append((f.dir.abspath, f.name))

    zipFiles = vmware.pkg.CreateZipStageNode(
        stageEnv,
        [nodeName],
        host,
        [nodeName],
        extrazipSources,
        whfbUnitTestZipFile.abspath,
        [],
    )
    return zipFiles


def generateDevenvStageNodes(stageEnv, visualStudioNodeMap):
    """
    Generate the stage commands used by `scons devenv` when invoking any
    project that requires copying supporting dlls in order for the binary
    to run.
    """
    for nodeName in ["hzMonService", "hzMonServiceTest"]:
        absPaths = []
        for host in ["win64", "win-arm64"]:
            nodeEnv = vmware.LookupEnv("%s-env" % nodeName, host)
            node = vmware.LookupNode(nodeName, host)
            nodeDir = node[0].dir

            sourceNodes = [
                nodeEnv["REDISTRIBUTE_MFW_DLL"],
                nodeEnv["MQTT_AS_DLL"],
            ]
            sourceNodes.extend(
                nodeEnv[
                    "REDIST" if nodeName == "hzMonServiceTest" else "OPENSSL_REDIST"
                ]
            )
            copyNodes = []
            for sourceNode in sourceNodes:
                f = stageEnv.File(sourceNode)
                copyNodes += stageEnv.LinkCopy(nodeDir.File(f.name), f)

            # hzMonServiceTest has some additional dependencies as described in
            # https://omnissa.atlassian.net/wiki/x/v4iKAw
            if nodeName == "hzMonServiceTest":
                for name in [
                    "hzMonApiTestClient",
                    "hzMonApi",
                    "etlmapi_mock",
                    "etlmapi_mock_v2",
                ]:
                    copyNodes += vmware.pkg.StageDeliverables(
                        stageEnv, name, host, nodeDir.abspath
                    )

            vmware.Alias(nodeName + f"-{host}-stage", copyNodes + [node])
            absPaths.append(nodeDir.abspath)

        # Add abspaths for all supported hosts
        visualStudioNodeMap[nodeName] = (nodeName + "-%s-stage", None) + tuple(absPaths)


def publishPrinterDriver(
    stageEnv,
    driverName,
    driverNodes,
    publishFolder,
    resourcePublishFolder="",
    driverResourceNodes=[],
):
    """Publish the printer driver stage nodes

    driverName specifies the driver name
    driverNodes specifies the nodes of the driver
    publishFolder specifies the driver folder name
    resourcePublishFolder specifies the driver resource folder name
    driverResourceNodes specifies the nodes of the driver resource files
    """
    nodes = []
    stagePP = vmware.PathPrefixer(vmware.pkg.stagePath)

    for host in ["win64"]:
        hostType = "x64"
        inputNodes = []
        stagePath = Dir(stagePP / hostType / publishFolder).abspath
        stageResourcePath = Dir(stagePP / hostType / resourcePublishFolder).abspath
        for node in driverNodes:
            nodes += vmware.pkg.StageDeliverables(stageEnv, node, host, stagePath)
            inputNodes += vmware.LookupNode(node, host)

        # Generate and sign catalog file node
        catNode = vmware.pkg.CreateDriverCatNode(
            stageEnv, stagePath, host, nodes, driverName
        )
        nodes += catNode
        inputNodes += catNode
        publishDriverNodes = []
        msmDrvDstDir = os.path.join(printerDrvDir, hostType)

        if vmware.BuildType() == "release" or vmware.BuildType() == "beta":
            useSigned = True if driverName in vmware.pkg.frozenFilesDetails else False
        else:
            useSigned = False

        extractTargetFiles = []
        for node in inputNodes:
            extractTargetFiles += [
                "{}/{}".format(hostType, os.path.basename(str(node)))
            ]

        # Folder that have Microsoft signed driver
        stageEnv.LoadTool("artifactory")
        frozenFilesExtractNode = stageEnv.FetchFromArtifactory(
            artifactorySignedFileRoot
            + vmware.pkg.frozenFilesDetails[driverName]["file"],
            sha256=vmware.pkg.frozenFilesDetails[driverName]["sha256"],
            extract=True,
            extractTargetFiles=extractTargetFiles,
            registerNodeName=vmware.pkg.frozenFilesDetails[driverName]["file"],
        )
        frozenFolder = os.path.dirname(frozenFilesExtractNode[0].abspath)

        for node in inputNodes:
            basename = os.path.basename(str(node))
            msmDrvDstFile = Dir(msmDrvDstDir).File(basename)
            if useSigned:
                msmDrvSrcFile = vmware.PathPrefixer(frozenFolder) / basename
            else:
                msmDrvSrcFile = Dir(stagePath).File(basename)
            publishDriverNodes += stageEnv.LinkCopy(msmDrvDstFile, msmDrvSrcFile)
            stageEnv.Depends(publishDriverNodes, frozenFilesExtractNode)

        # Use the driver resource files from unsigned folder.
        # Because we have stripped them from driver package.
        resourceNodes = []
        for node in driverResourceNodes:
            resourceNodes += vmware.pkg.StageDeliverables(
                stageEnv, node, host, stageResourcePath
            )
        for node in resourceNodes:
            msmDrvDstFile = Dir(msmDrvDstDir).File(node.name)
            publishDriverNodes += stageEnv.LinkCopy(msmDrvDstFile, node)

        # Make the nodes build sequential.
        # Because if LinkCopy happens before inf2cat, it will
        # cause inf2cat compile errors
        stageEnv.Depends(publishDriverNodes, catNode)
        nodes += publishDriverNodes

    return nodes


def publishHtml5mmrNativeMessagingHostDeps(hosts):
    """Publish html5mmrNativeMessagingHost.exe's json file dependency."""
    nodes = []
    installerStagex64Dir = os.path.join(productBuildRoot, "installerStage")
    stagePP = vmware.PathPrefixer(installerStagex64Dir)
    for host in ["win64"]:
        # html5mmrStagePP = stagePP
        nodes += stageEnv.LinkCopy(
            File(stagePP / hosts[host] / "html5mmrNativeMessagingHost_manifest.json"),
            "#bora/apps/rde/html5mmr/nativeMessaging/manifest.json",
        )
    return nodes


def buildDependForMergeModules(stageEnv, arch):
    # Build some dependent wixobj files that all merge modules will depend
    # upon. Currently this is only Directories.
    wixEnv = stageEnv.Clone()
    wixEnv.Append(
        WIXDEFINES={
            "MSIProductVersion": vmware.ProductVersionNumber(),
            "OBJDIR": vmware.BuildType(),
            "ARCH": arch,
            "WIXROOT": vmware.DirAbsPath("#bora/install/msi/msm"),
        },
        WIXCANDLEFLAGS=["-arch", arch, "-sw1086", "-wx"],
    )
    wixObjPath = os.path.join(
        vmware.DirAbsPath(vmware.BuildRoot()),
        vmware.BuildType(),
        "apps",
        "rde-installers",
        "Directories-%s.wixobj" % arch,
    )
    return wixEnv.Wixobj(
        File(wixObjPath), "#bora/install/msi/msm/rdeIncludes/Directories.wxs"
    )


def buildCommonMsiNodes(
    wixEnv,
    wixMsmNode,
    wixMsmName,
    wixBuildDir,
    moduleName,
    fragmentName,
    arch,
    wixObjDirNode,
    msiDir,
):
    """Build the .msi nodes for module.

    wixEnv specifies the wix environment object.
    wixMsmNode specifies the .msm node generated for module.
    wixMsmName specifies the .msm file name(without .msm postfix).
    wixBuildDir specifies the intermediate build directory.
    moduleName specifies the module name.
    fragmentName specifies if it is Agent fragment.
    arch specifies x64.
    wixObjDirNode specifies the wixobj directory node.
    """

    nodes = []

    # Step 1: register the *.msm node.
    # This is necessary as the .msi generation depends on the .msm.
    vmware.RegisterNode(wixMsmNode, wixMsmName + ".msm")
    # Step 2: prepare the wix build env.
    nodeWixEnv = wixEnv.Clone()
    nodeWixEnv.Append(
        WIXDEFINES={
            "wixBuildDir": Dir(wixBuildDir).abspath,
            "fragmentName": fragmentName,
        },
        WIXLIGHTFLAGS=[
            "-sice:ICE61",  # This warning is generated if max version
            # is not defined. We dont want to define an
            # arbitary max version
            "-sice:ICE82",  # This warning is generated when Sequence
            # number is same for all actions.
            # We can safely ignore the warning.
        ],
    )
    # Step 3: build the msi-specific "fragment" wixobj.
    wixFragmentObjNode = nodeWixEnv.Wixobj(
        wixBuildDir.File(
            "%s-%sFragment-%s-msi.wixobj" % (moduleName, fragmentName, arch)
        ),
        "#bora/install/msi/%s/components.wxs" % moduleName,
    )
    # Step 4: let the .msi fragment obj build depend on the .msm node.
    Depends(wixFragmentObjNode, vmware.LookupNode(wixMsmName + ".msm", packaged=True))

    # Step 5: build the msi-specific normal wixobj.
    wixObjNode = nodeWixEnv.Wixobj(
        wixBuildDir.File("%s-%s-%s-msi.wixobj" % (moduleName, fragmentName, arch)),
        "#bora/install/msi/%s/%s.wxs" % (moduleName, moduleName),
    )
    # Step 7: let the .msi obj build depend on the print redir .msm.
    Depends(wixObjNode, wixMsmNode)
    # Step 8: create the .msi node.
    wixMsiFile = wixBuildDir.File(wixMsmName + ".msi")
    wixMsiNode = nodeWixEnv.Wixmsi(
        wixMsiFile, [wixFragmentObjNode, wixObjNode, wixObjDirNode]
    )
    copyNode = nodeWixEnv.LinkCopy(Dir(msiDir).File(wixMsiFile.name), wixMsiFile)
    Depends(copyNode, wixMsiNode)
    nodes += copyNode

    return nodes


def buildMergeModules(stageEnv, copyNodeMap, msmInfoMap):
    """
    Builds and publishes all merge modules that are required by the consumers.
    """

    directoryWixObjMap = {}
    arch = "x64"
    directoryWixObjMap[arch] = buildDependForMergeModules(stageEnv, arch)

    mode = "Agent"
    nodes = []

    winInstallKit = vmware.GetGobuildComponent("windows-installkit")
    stageDir = vmware.DirAbsPath(vmware.pkg.stagePath)
    zlibConanDir = Dir(vmware.GetConanComponent(stageEnv, "zlib").env["BINPATH"][0])

    # Now build each individual msm in map.
    for msm in msmInfoMap:
        msmTuple = msmInfoMap[msm]
        # Step 1: set up some initial variables, including the
        # parameters that we will send into candle.
        msmIntermediateBuildDir = Dir(
            os.path.join(
                vmware.DirAbsPath(vmware.BuildRoot()), vmware.BuildType(), "apps", msm
            )
        )
        msmBuildDir = Dir(msmIntermediateBuildDir).Dir(arch)

        wixEnv = stageEnv.Clone()
        wixEnv.Append(
            WIXDEFINES={
                "MSIProductVersion": vmware.ProductVersionNumber(),
                "ARCH": arch,
                "WIXROOT": vmware.DirAbsPath("#bora/install/msi/msm"),
                "OBJDIR": vmware.BuildType(),
                "%s_BUILDDIR" % msm: stageDir,
                "GOBUILD_WINDOWS_INSTALLKIT_ROOT": winInstallKit,
                "CONAN_ZLIB_ROOT": zlibConanDir.abspath,
                "SSL_BIN_DIR_32": os.path.join(stageDir, "openssl", "win32"),
                "SSL_BIN_DIR_64": os.path.join(stageDir, "openssl", "win64"),
                "SSL_VERSION": "3.0",
                "MSM_TYPE": mode,
                "SRCROOT": vmware.DirAbsPath("#bora"),
                "StageDir": installerStagePath,
            },
            WIXCANDLEFLAGS=[
                "-arch",
                arch,
                "-ext",
                "WixFirewallExtension",
                "-ext",
                "WixUIExtension",
                "-sw1086",
                "-sw1006",
                "-wx",
            ],
            WIXLIGHTFLAGS=[
                "-ext",
                "WixUIExtension",
                "-sw1072",
                "-wx",
                "-sice:ICE03",
                "-sice:ICE80",
                "-sice:ICE30",
                "-sice:ICE69",
            ],
            WIXLIGHTLOC=[
                "-cultures:en-us",
            ],
        )

        # Note: the msm-specific wixobj files need to depend upon the
        # staging nodes, because the stagedir (e.g.
        # VDPService_BUILDDIR) is the gobuild components. Without this
        # dependency, SCons may try to compile the msm's before the
        # files are staged.
        copyNodeDeps = []
        for dep in msmTuple[0]:
            copyNodeDeps += copyNodeMap[dep]

        # Step 2: build the msm-specific "fragment" wixobj.
        wixFragmentObjNode = wixEnv.Wixobj(
            msmBuildDir.File("%s-%sFragment-%s.wixobj" % (msm, mode, arch)),
            "#bora/install/msi/msm/%s/%s.wxs" % (msm, mode),
        )
        Depends(wixFragmentObjNode, copyNodeDeps)

        # Step 3: build the msm-specific normal wixobj.
        wixObjNode = wixEnv.Wixobj(
            msmBuildDir.File("%s-%s-%s.wixobj" % (msm, mode, arch)),
            "#bora/install/msi/msm/%s/%s.wxs" % (msm, msm),
        )
        Depends(wixObjNode, copyNodeDeps)

        # Step 4: link the msm together using the "Directory" wixobj,
        # the msm-specific "fragment" wixobj, and the msm-specific
        # normal wixobj.
        directoryWixObjNode = directoryWixObjMap[arch]
        wixStageDir = Dir(os.path.join(stageDir, arch))

        # Note: consumers are written to expect 1.0.0 in the file name.
        # TODO: Remove version number entirely when rde-rft-all is removed
        # also remove version number in agent.py
        wixName = "VMware-%s-%s-1.0.0-%s" % (msm, mode, arch)

        wixMsmFile = msmBuildDir.File(wixName + ".msm")
        wixMsmNode = wixEnv.Wixmsm(
            wixMsmFile,
            [wixFragmentObjNode, wixObjNode, directoryWixObjNode],
        )
        nodes += wixEnv.LinkCopy(wixStageDir.File(wixMsmFile.name), wixMsmFile)
        publishDir = vmware.ReleasePackagesDir()
        testsDir = Dir(publishDir).Dir("tests")
    return nodes


def publishAdditionalDependencies(
    stageEnv, supportedHosts, nodeName, deps, isPath=False
):
    # Publish agent additional dependencies (3rd party library).
    nodes = []
    stagePP = vmware.PathPrefixer(vmware.pkg.stagePath)

    for host in supportedHosts:
        nodeEnv = vmware.LookupEnv("%s-env" % (nodeName), host)
        tgtStagePP = Dir(installerStagePath).Dir(hosts[host])
        for dep in deps:
            # First copy the actual dependency dll or exe.
            if isPath:
                srcFile = nodeEnv[dep]
            else:
                srcFile = nodeEnv[dep].abspath
            targetFile = tgtStagePP.File(os.path.basename(srcFile)).abspath
            copyNode = stageEnv.FileCopy(targetFile, srcFile)
            stageEnv.SignFile(copyNode)
            nodes += copyNode

            # Next copy the lib and/or pdb, if they exist.
            root, oldExt = os.path.splitext(srcFile)
            for ext in [".lib", ".pdb"]:
                source = root + ext
                if os.path.isfile(source):
                    destPath = os.path.dirname(targetFile)
                    nodes += stageEnv.LinkCopy(
                        File(os.path.join(destPath, os.path.basename(source))), source
                    )
    return nodes


def publishRdeSdk(stageEnv, copyNodeMap):
    rdeSdkHeaderFiles = [
        "#bora/public/horizon.h",
        "#bora/public/vm_assert.h",
        "#bora/public/vm_basic_defs.h",
        "#bora/public/vm_basic_types.h",
    ]
    rdeSdkHeaderDirs = [
        "#bora/apps/rde/vdpservice/public",
    ]
    rdeSdkNodeNames = [
        "rdpvcbridge",
        "vdpservice",
        "vdpservicepriv",
        "vdpserviceprivunittest",
    ]
    rdeSdkHeaderFiles += [
        "#bora/apps/rde/rdpvcbridge/dll/vdp_rdpvcbridge.h",
        "#bora/apps/rde/rdpvcbridge/dll/vdp_rdpvcbridge_import.cpp",
        "#bora/apps/rde/vdpservice/public/vdpService_import.cpp",
    ]

    nodes = []
    if publishDir:
        stagePP = vmware.PathPrefixer(publishDir) / "rde-sdk"

        # First publish public dir
        includeStageDir = Dir(stagePP / "public")
        for headerFile in rdeSdkHeaderFiles:
            file = File(headerFile)
            nodes += stageEnv.LinkCopy(includeStageDir.File(file.name), file)
        for headerDir in rdeSdkHeaderDirs:
            files = vmware.EnumerateSourceDir(
                headerDir,
                filefilter=lambda root, f: f.endswith(".h") or f.endswith(".hh"),
            )
            nodes += vmware.DirCopy(files, Dir(headerDir), includeStageDir, stageEnv)

    # Second publish binary dir
    nodes += stageBinariesForExportNodes(
        stageEnv, hosts, publishDir, rdeSdkNodeNames, copyNodeMap
    )
    vmware.Alias("rde-sdk-publish", nodes)
    addNodesToCopyMap(copyNodeMap, "rde-sdk-publish", nodes)
    return nodes


def stageAndSignOpenssl(stageEnv):
    # stage and sign openssl for vdpService fragment
    nodes = []
    stagePP = vmware.PathPrefixer(vmware.pkg.stagePath)

    for host in ["win32", "win64"]:
        vdpServiceEnv = vmware.LookupEnv("vdpservice-env", host)
        opensslStageDir = stagePP / "openssl" / host

        for redist in vdpServiceEnv["OPENSSL_FIPS_REDIST"]:
            copyNode = stageEnv.FileCopy(
                File(opensslStageDir / os.path.basename(redist)), redist
            )
            stageEnv.SignFile(copyNode)
            nodes += copyNode

            # copy pdb
            ext = ".pdb"
            root, oldExt = os.path.splitext(redist)
            source = root + ext
            if os.path.isfile(source):
                nodes += stageEnv.LinkCopy(
                    File(opensslStageDir / os.path.basename(source)), source
                )

        # copy libs
        opensslLibs = vdpServiceEnv["OPENSSL_LIBS"]
        for file in opensslLibs:
            nodes += stageEnv.LinkCopy(
                File(opensslStageDir / os.path.basename(file)), file
            )

    vdpServiceNode = vmware.LookupNode("vdpservice")
    vdpServiceEnv.Depends(vdpServiceNode, nodes)
    return nodes


def AddNodeToVisualStudioProjectNodes(nodeName):
    for host in hostMapInstaller:
        if not vmware.pkg.NodeExists(nodeName, host):
            continue

        node = vmware.LookupNode(nodeName, host)

        vmware.Alias("%s-%s-stage" % (nodeName, host), node)
        if nodeName not in visualStudioProjectNodes:
            visualStudioProjectNodes.append(nodeName)


vmware.pkg.localEnv = vmware.BuildHostEnvironment()
vmware.pkg.stageEnv = vmware.DefaultEnvironment()
vmware.pkg.stagePath = vmware.StageRoot()

stageEnv = vmware.pkg.stageEnv.Copy()
stageEnv.LoadTool(
    [
        "linkcopy",
        "digitalsign",
        "zip-3.0",
        "makecab",
        "horizonUtils",
        "extract-macro",
        "wix",
    ]
)

hostMapInstaller = {
    "win32": "x86",
    "win64": "x64",
    "win-arm64": "ARM64",
    "generic": "generic",
}

hostMapDriverInstaller = {
    "win64": "x64",
}

hostMapCompcache = {"win32": "x86", "win64": "x64", "generic": "generic"}

hostMapInstallerCompcache = {
    "win32": "x86",
    "win64": "x64",
}

hostMapPublish = {
    "win32": "x86",
    "win64": "x64",
}

signedArchs = [
    "x86",
    "x64",
    "ARM64",
]

allNodes = []

# To get access to vmware.pkg.StageDeliverables.
SConscript("#bora/scons/package/common/horizon-stage.py")


productBuildRoot = os.path.join(
    vmware.DirAbsPath(vmware.BuildRoot()), vmware.BuildType(), vmware.Product()
)
installerStagePath = os.path.join(productBuildRoot, "installerStage")
compcacheStagePath = vmware.PathPrefixer(vmware.pkg.stagePath)

# Kill switch for Session Monitor components. Set to something other that 0
# to exclude omnksm and hznusm.
excludeSessionMonitor = 0
Export("excludeSessionMonitor")

# For official/sandbox release builds, check for signed flag of these nodes.
# If the flag is 1, add these nodes to their corresponding stage frozen
# nodes so that during staging, the binaries will be picked up from the
# frozen directory.
stageFrozenCommonNodes = []
stageFrozenInstallerNodes = []
if vmware.BuildType() in ["release", "beta"]:
    for node in signedCommon:
        if node in vmware.pkg.frozenFilesDetails:
            log.info("Using frozen files for node %s" % node)
            stageFrozenCommonNodes.append(node)
            AddNodeToVisualStudioProjectNodes(node)
        else:
            stageNodesCommon.append(node)

    for node in signedInstaller:
        if node in vmware.pkg.frozenFilesDetails:
            log.info("Using frozen files for node %s" % node)
            stageFrozenInstallerNodes.append(node)
            AddNodeToVisualStudioProjectNodes(node)
        else:
            stageNodesInstaller.append(node)

    for node in frozenInstaller:
        if node in vmware.pkg.frozenFilesDetails:
            log.info("Using frozen files for node %s" % node)
            stageFrozenInstallerNodes.append(node)
            AddNodeToVisualStudioProjectNodes(node)
        else:
            stageNodesInstallerx64.append(node)
else:
    # For non-release and local builds, stage the nodes from the
    # built directory instead of Frozen directory
    stageNodesCommon.extend(signedCommon)
    stageNodesInstaller.extend(signedInstaller)
    stageNodesInstallerx64.extend(frozenInstaller)
    """
    # For PR 2936574, we need signed audioEndPoint driver for beta build
    if vmware.BuildType() == "beta":
        if "audioEndPoint" in vmware.pkg.frozenFilesDetails:
            log.info("Using frozen files for node audioEndPoint")
            stageFrozenInstallerNodes.append("audioEndPoint")
            stageNodesInstaller.remove("audioEndPoint")
    """

# List of all stage nodes. At the moment this is only used by the debugging zip
# file creation code.
allStageNodes = []
allStageNodes.extend(stageNodesCommon)
allStageNodes.extend(stageNodesInstaller)
allStageNodes.extend(stageNodesInstallerx86)
allStageNodes.extend(stageNodesInstallerx64)
allStageNodes.extend(stageNodesCompcache)
allStageNodes.extend(stageInstallerNodesCompcache)
allStageNodes.extend(stageNodesPublish)
allStageNodes.extend(driverNodes)
allStageNodes.extend(appblastNodes)

# Keeps track of the vmware-signed binaries that later need to be
# signed by Microsoft
lsaSignedNodes = []

# Loop over all hosts publishing common components.
for host in hostMapInstaller:
    platformArch = hostMapInstaller[host]
    publishDir = Dir(vmware.ReleasePackagesDir())

    for name in stageNodesCommon:
        if not vmware.pkg.NodeExists(name, host):
            continue

        installerNodes = StageInstallerDeliverables(
            installerStagePath, platformArch, stageEnv, name, host
        )
        # Register the LSA binaries. They need to be registered in order to
        # package them in a cab file later.
        if name in signedCommon or name in signedInstaller:
            filteredLsaNodes = list(
                filter(lambda n: installerStagePath in n.abspath, installerNodes)
            )
            vmware.RegisterNode(
                filteredLsaNodes, "%s-signed" % name, host=host, space="build"
            )
            lsaSignedNodes += filteredLsaNodes
        allNodes += installerNodes

        # Remember this info for later inclusion in the Visual Studio project
        # generation target.
        vmware.Alias("%s-%s-stage" % (name, host), installerNodes)
        if name not in visualStudioProjectNodes:
            visualStudioProjectNodes.append(name)

        # Remove publish nodes from the installerNodes to
        # avoid duplicate entries
        if publishDir is not None:
            installerNodes = list(
                filter(lambda n: publishDir.abspath not in n.abspath, installerNodes)
            )

        if host in hostMapCompcache:
            for installerNode in installerNodes:
                installerNodePath = installerNode.abspath
                linkCopyNode = stageEnv.LinkCopy(
                    os.path.join(
                        Dir(compcacheStagePath / platformArch).abspath,
                        installerNode.name,
                    ),
                    installerNodePath,
                )
                allNodes += linkCopyNode

    allNodes += stagePCoIPServerGPO(host)

    # Stage signed files from Microsoft
    if platformArch in signedArchs:
        frozenNodes = []
        for name in stageFrozenCommonNodes:
            # Publish the vmware-only signed versions so that they can be
            # easily accessible for Microsoft signing
            if name in signedCommon or name in signedInstaller:
                vmwareSignedDir = vmware.DirAbsPath(
                    os.path.join(installerStagePath, platformArch, "vmware-signed")
                )
                microsoftUnsignedNodes = vmware.pkg.StageDeliverables(
                    stageEnv, name, host, vmwareSignedDir
                )
                vmware.RegisterNode(
                    microsoftUnsignedNodes, "%s-signed" % name, host=host, space="build"
                )
                lsaSignedNodes += microsoftUnsignedNodes
                allNodes += microsoftUnsignedNodes

            frozenFilesExtractNode = []
            if name in vmware.pkg.frozenFilesDetails:
                stageEnv.LoadTool("artifactory")
                frozenFilesExtractNode = stageEnv.FetchFromArtifactory(
                    artifactorySignedFileRoot
                    + vmware.pkg.frozenFilesDetails[name]["file"],
                    sha256=vmware.pkg.frozenFilesDetails[name]["sha256"],
                    extract=True,
                    extractTargetFiles=["{}/{}.dll".format(platformArch, name)],
                    extractFolderSuffix=host,
                    registerNodeName=vmware.pkg.frozenFilesDetails[name]["file"] + host,
                )

            # Register the node with frozen suffix so that the lookup
            # will give the files from frozen directory
            regNode = vmware.RegisterNode(
                frozenFilesExtractNode, "%s-frozen" % name, host=host, space="build"
            )
            stageEnv.Depends(regNode, frozenFilesExtractNode)

            # Stage in installer directory
            installerFrozenNodes = StageInstallerDeliverables(
                installerStagePath, platformArch, stageEnv, name, host, frozen=True
            )
            stageEnv.Depends(installerFrozenNodes, frozenFilesExtractNode)
            frozenNodes += installerFrozenNodes

            # Remove publish nodes from the installerFrozenNodes to
            # avoid duplicate entries
            if publishDir is not None:
                installerFrozenNodes = list(
                    filter(
                        lambda n: publishDir.abspath not in n.abspath,
                        installerFrozenNodes,
                    )
                )

            # Stage the signed files in compcache
            if host in hostMapCompcache:
                compcacheDir = Dir(compcacheStagePath / platformArch)
                for installerNode in installerFrozenNodes:
                    frozenCompNodes = stageEnv.LinkCopy(
                        compcacheDir.File(installerNode.name), installerNode.abspath
                    )
                    frozenNodes += frozenCompNodes

        allNodes += frozenNodes

nodeMapping = {
    "win32": stageNodesInstallerx86,
    "win64": stageNodesInstallerx64,
    "win-arm64": stageNodesInstallerARM64,
    "generic": [],
}

# Stage MSVC UMDF drivers files
for host in hostMapDriverInstaller:
    installerDir = os.path.join(installerStagePath, hostMapDriverInstaller[host])
    publishDir = vmware.ReleasePackagesDir()

    for name in driverNodes:
        if vmware.BuildType() == "release" or vmware.BuildType() == "beta":
            useSigned = True if name in vmware.pkg.frozenFilesDetails else False
        else:
            useSigned = False

        stageNodes = vmware.pkg.CreateMsvcDriverStageNodes(
            stageEnv, name, host, publishDir, installerDir, useSigned
        )

        vmware.Alias("%s-%s-stage" % (name, host), stageNodes)

        # Remember this info for later inclusion in the Visual Studio project
        # generation target.
        visualStudioProjectNodes.append(name)

        allNodes += stageNodes

for driverName, configs in kernelDriverNodes.items():
    publishDir = vmware.ReleasePackagesDir()

    for config in configs:
        msbuildConfigOS = config[0]
        host = config[1]
        installerDir = os.path.join(installerStagePath, host)
        if vmware.BuildType() == "release" or vmware.BuildType() == "beta":
            useSigned = True if driverName in vmware.pkg.frozenFilesDetails else False
        else:
            useSigned = False

        stageNodes = vmware.pkg.CreateMsvcKernelDriverStageNodes(
            stageEnv,
            driverName,
            msbuildConfigOS,
            host,
            publishDir,
            installerDir,
            useSigned,
        )
        vmware.Alias("%s-%s-%s-stage" % (driverName, msbuildConfigOS, host), stageNodes)

        #  Remember this info for later inclusion in the
        #  Visual Studio project generation target.
        visualStudioProjectNodes.append(
            "%s-%s-%s" % (driverName, msbuildConfigOS, host)
        )

        allNodes += stageNodes


# Stage files for the horizonagent installer
for host in hostMapInstaller:
    platformArch = hostMapInstaller[host]
    nodeNames = stageNodesInstaller + nodeMapping[host]

    if vmware.pkg.NodeExists("hzMonService", host):
        tmpNodeEnv = vmware.LookupEnv("hzMonService-env", host=host)
        pahoMqttSrcDll = File(tmpNodeEnv["MQTT_AS_DLL"])
        pahoMqttStageNode = stageEnv.FileCopy(
            Dir(installerStagePath)
            .Dir(platformArch)
            .Dir("paho-mqtt-stage")
            .File(pahoMqttSrcDll.name),
            pahoMqttSrcDll,
        )
        stageEnv.SignFile(pahoMqttStageNode)
        allNodes += pahoMqttStageNode

    for name in nodeNames:
        if not vmware.pkg.NodeExists(name, host):
            continue

        stageNodes = StageInstallerDeliverables(
            installerStagePath, platformArch, stageEnv, name, host
        )
        # Register the LSA binaries. They need to be registered in order to
        # package them in a cab file later.
        if name in signedCommon or name in signedInstaller:
            filteredLsaNodes = list(
                filter(lambda n: installerStagePath in n.abspath, stageNodes)
            )
            vmware.RegisterNode(
                filteredLsaNodes, "%s-signed" % name, host=host, space="build"
            )
            lsaSignedNodes += filteredLsaNodes
        allNodes += stageNodes

        # Remember this info for later inclusion in the Visual Studio project
        # generation target.
        vmware.Alias("%s-%s-stage" % (name, host), stageNodes)
        visualStudioProjectNodes.append(name)

    # Stage signed files from Microsoft
    if platformArch in signedArchs:
        frozenNodes = []
        for name in stageFrozenInstallerNodes:
            # Publish the vmware-only signed versions so that they can be
            # easily accessible for Microsoft signing
            if name in signedCommon or name in signedInstaller:

                if host != "win-arm64" or name in signedModulesARM64:
                    vmwareSignedDir = vmware.DirAbsPath(
                        os.path.join(installerStagePath, platformArch, "vmware-signed")
                    )
                    microsoftUnsignedNodes = vmware.pkg.StageDeliverables(
                        stageEnv, name, host, vmwareSignedDir
                    )
                    vmware.RegisterNode(
                        microsoftUnsignedNodes,
                        "%s-signed" % name,
                        host=host,
                        space="build",
                    )
                    lsaSignedNodes += microsoftUnsignedNodes
                    allNodes += microsoftUnsignedNodes

            # KSM notifier only supports x64.
            if name == "ksmNotifier" and platformArch != "x64":
                continue

            if name in vmware.pkg.frozenFilesDetails:
                fileExtension = "exe" if name == "ksmNotifier" else "dll"
                stageEnv.LoadTool("artifactory")
                frozenFilesNode = stageEnv.FetchFromArtifactory(
                    artifactorySignedFileRoot
                    + vmware.pkg.frozenFilesDetails[name]["file"],
                    sha256=vmware.pkg.frozenFilesDetails[name]["sha256"],
                    extract=True,
                    extractTargetFiles=[
                        "{}/{}.{}".format(platformArch, name, fileExtension)
                    ],
                    extractFolderSuffix=host,
                    registerNodeName=vmware.pkg.frozenFilesDetails[name]["file"] + host,
                )

            # Register the node with frozen suffix so that the lookup
            # will give the files from frozen directory
            regNode = vmware.RegisterNode(
                frozenFilesNode, "%s-frozen" % name, host=host, space="build"
            )
            stageEnv.Depends(regNode, frozenFilesNode)

            # Stage in the installer directory
            installerFrozenNodes = StageInstallerDeliverables(
                installerStagePath, platformArch, stageEnv, name, host, frozen=True
            )
            frozenNodes += installerFrozenNodes

        allNodes += frozenNodes

# All paths are relative to current directory
# viewdrivers path are temporary and will be changed
# once we remove the dependency on it from other builds

msmMap = {
    "msm/crypto": [],
    "msm/dct": ["x64"],
    "msm/ngvc-agent": ["x64"],
    "msm/openssl": ["x64"],
    "msm/psg": ["x64"],
    "msm/server-jre": ["x64"],
    "msm/smprocdump": ["x64"],
    "msm/omnksm": ["x64"],
    "msm/regredirect": ["x64"],
    "msm/hznusm": ["x64"],
    "msm/wsauth": ["x86", "x64"],
    "../viewdrivers/msm/vmwusbt": ["x64"],
    "../viewdrivers/msm/hznvscrd": ["x64"],
    "../viewdrivers/msm/hznrds": ["x64"],
    "../viewdrivers/msm/hznicpdr": ["x64"],
    "../viewdrivers/msm/hznsdo": ["x64"],
    "../viewdrivers/msm/vmwude": ["x64"],
    "../viewdrivers/msm/hznvudpd": ["x64"],
    "../viewdrivers/msm/hzngeoloc": ["x64"],
    "../viewdrivers/msm/hznvaudio": ["x64"],
    "../viewdrivers/msm/hznvdisplay": ["x64"],
    "../viewdrivers/msm/hznvidd": ["x64"],
    "../viewdrivers/msm/hznvidd2": ["x64"],
    "../viewdrivers/msm/hzncdrfilter": ["x64"],
    "../viewdrivers/msm/omnsdr": ["x64"],
    "../viewdrivers/msm/vmkbd": ["x64"],
}

# Build msms
msmNodes = []
for msm, archs in msmMap.items():
    if archs:
        for arch in archs:
            # Export installerStagePath root and binPath as a convenience
            binPath = vmware.DirAbsPath(os.path.join(installerStagePath, arch))
            msmNodes += SConscript(
                [
                    "%s.py" % msm,
                ],
                exports=["arch", "installerStagePath", "binPath"],
            )
    else:
        # archs is not defined so call the sconscript without arch exports
        msmNodes += SConscript(
            [
                "%s.py" % msm,
            ],
            exports=["installerStagePath"],
        )

SConscript("#bora/scons/package/appblastlibs/appblast.py")
appblastMsmNode = vmware.LookupNode("appblast-msm-x64", packaged=True)
msmNodes += appblastMsmNode

opensslMsmNode = vmware.LookupNode("openssl-msm-x64", packaged=True)
# appblast stages the OpenSSL binaries that the Agent's openssl msm depends on
stageEnv.Depends(opensslMsmNode, appblastMsmNode)

# Stage msms
msmStageDir = Dir(vmware.PathPrefixer(installerStagePath) / "msm")
Execute(vmware.Mkdir(msmStageDir))
for msmNode in msmNodes:
    stageEnv.AddPostAction(
        msmNode,
        stageEnv.LinkCopy(msmStageDir.File(msmNode.name).abspath, msmNode.abspath),
    )

allNodes += msmNodes


#  Build view agent msi and bootstrapper exe
exeNodes = []
msiMap = {
    "msi/updatetool.py": ["x64"],  # This file must be listed before agent.py
    "msi/agent.py": ["x64"],
    "msi/agentconnect.py": ["x64"],
    "msi/ngvc-agent.py": ["x64"],
    "msi/smprocdump.py": ["x64"],
    "msi/omnksm.py": ["x64"],
    "msi/regredirect.py": ["x64"],
    "../viewdrivers/msi/hzngeoloc.py": ["x64"],
    "../viewdrivers/msi/hznvidd.py": ["x64"],
    "../viewdrivers/msi/hznvidd2.py": ["x64"],
}

# Declare dependancy list for each msiMap entry
msiDepMap = {
    "msi/agent.py": allNodes,
    "msi/agentconnect.py": allNodes,
    "msi/ngvc-agent.py": None,
    "msi/smprocdump.py": None,
    "msi/updatetool.py": None,
    "msi/omnksm.py": None,
    "msi/regredirect.py": None,
    "../viewdrivers/msi/hzngeoloc.py": None,
    "../viewdrivers/msi/hznvidd.py": None,
    "../viewdrivers/msi/hznvidd2.py": None,
}

for msi, archs in msiMap.items():
    for arch in archs:
        exe = SConscript(msi, exports=["arch", "installerStagePath"])
        stageEnv.Depends(exe, msiDepMap[msi])
        if isinstance(exe, SCons.Node.NodeList):
            exeNodes.extend(exe)
        else:
            exeNodes.append(exe)

allNodes += exeNodes

# Stage viewdrivers tools
toolsNodesMap = {
    "vmwrdsdrvdiag": ["win32", "win64"],
}
toolsStageNodes = []
toolsStageDirPP = compcacheStagePath / "rds" / "tools"

for node in toolsNodesMap:
    for host in toolsNodesMap[node]:
        toolsStageNodes += vmware.pkg.StageDeliverables(
            stageEnv,
            node,
            host,
            Dir(toolsStageDirPP / hostMapInstallerCompcache[host]).abspath,
        )

allNodes += toolsStageNodes

# Stage viewdrivers tests
testsDriversNodesMap = {
    "hzngeoloc": {
        "geoRedirTest": ["win64"],
    },
}
testsStageNodes = []
testsStageDirPP = compcacheStagePath / "tests"

# Skip Test stages for CodeQL scan
if not CODEQL_SCAN:
    for drv in testsDriversNodesMap:
        for drvTest in testsDriversNodesMap[drv]:
            testHosts = testsDriversNodesMap[drv][drvTest]
            for host in testHosts:
                dirName = hostMapInstallerCompcache[host]
                dest = Dir(testsStageDirPP / drv / dirName).abspath
                testsStageNodes += vmware.pkg.StageDeliverables(
                    stageEnv, drvTest, host, dest
                )

    allNodes += testsStageNodes


# Stage all modules binaries, libs, debug info and
# module specific files that need to be consumed
for host in hostMapCompcache:

    platformArch = hostMapCompcache[host]
    nodeNames = stageNodesCompcache

    hostPath = Dir(compcacheStagePath / platformArch).abspath
    for name in nodeNames:
        if not vmware.pkg.NodeExists(name, host):
            continue

        node = StageDeliverables(stageEnv, name, host, hostPath)
        allNodes += node

        # Remember this info for later inclusion in the Visual Studio project
        # generation target.
        vmware.Alias("%s-%s-stage" % (name, host), node)
        if name not in visualStudioProjectNodes:
            visualStudioProjectNodes.append(name)

# Publish binaries from rde-rft-all
copyNodeMap = {}
hznprpsNodes = ["prPS"]
hznprupdNodes = ["prGraphics", "prUI", "prProcessor"]
hznprupdResourceNodes = ["prUIRes"]
allNodes += publishPrinterDriver(stageEnv, "hznprps", hznprpsNodes, "hznprps")
allNodes += publishPrinterDriver(
    stageEnv, "hznprupd", hznprupdNodes, "hznprupd", "prUIRes", hznprupdResourceNodes
)

exportNodes = [
    "appStub",
    "browserPluginIE",
    "autociClientRdp",
    "autociClientVdp",
    "hci",
    "html5mmrNativeMessagingHost",
    "html5mmrServer",
    "hznime",
    "inputdevtap",
    "mksvchanserver",
    "pcoip_mfw",
    "pcoip_vchan",
    "pcoipVchanConnCBTest",
    "perfTracker",
    "pingPcoipVchanServer",
    "pingPcoipVchanClient",
    "pingRdpVcbridgeServer",
    "pingRdpVcbridgeClient",
    "pingVdpServiceServer",
    "pingVdpServiceClient",
    "printredir",
    "prmon",
    "prService",
    "prServerApi",
    "prvdpplugin",
    "rdeServer",
    "rdpvcbridgeTestServer",
    "rdpvcbridgeTestClient",
    "readbackWindowServer",
    "readbackWindowClient",
    "rpcChannelTestClient",
    "rpcChannelTestServer",
    "rtavPlugin",
    "scannerRedirHorizonDS",
    "scannerRedirServicePlugin",
    "scannerRedirTray",
    "sdoSensorTools",
    "sdrserverutil",
    "sdrserviceplugin",
    "teamsHelper",
    "tsdrTest",
    "tsmmrServer",
    "tsmmrServerDShow",
    "urlNativeMessageHost",
    "urlProtocolLaunchHelper",
    "uncService",
    "unityShell",
    "unityShellTests",
    "usbRedirectionServer",
    "vchanStub",
    "vhublib",
    "omnrxgservice",
    "ws_vhub",
    "ws_usbstor",
    "ws_vdpvhub",
]

exportArm64Nodes = [
    "abctrl",
    "abRdeStub",
    "appStub",
    "appTap",
    "audiodevtap",
    "audiodevtapConfigTool",
    "autociClientRdp",
    "autociClientVdp",
    "AWSDataDumper",
    "azureDetectApp",
    "certStoreIntercept",
    "customaction",
    "es_diag",
    "EventLogProvider",
    "html5mmrNativeMessagingHost",
    "html5mmrServer",
    "horizonrxut",
    "hzDCT",
    "hzMonApi",
    "hzMonService",
    "hznaudioendpoint",
    "hznime",
    "hzWebauthn",
    "ksmlibTestApp",
    "ksmNotifier",
    "LocalDiskConfigProvider",
    "LocalDiskDumper",
    "mfwcom",
    "mfwgen",
    "NetworkProvider",
    "omnissa-ic-ga",
    "omnissa-ic-nga",
    "pcoipVchanConnCBTest",
    "PerfMonProvider",
    "pingPcoipVchanClient",
    "pingPcoipVchanServer",
    "pingRdpVcbridgeClient",
    "pingRdpVcbridgeServer",
    "pingVdpServiceClient",
    "prGraphics",
    "printredir",
    "prmon",
    "ProcessProvider",
    "prProcessor",
    "prPS",
    "prServerApi",
    "prService",
    "prUI",
    "prvdpplugin",
    "psgctest",
    "rdpvcbridge",
    "rdpvcbridgeTestClient",
    "rdpvcbridgeTestServer",
    "readbackWindowClient",
    "readbackWindowServer",
    "RegistryProvider",
    "rpcChannelTestClient",
    "rpcChannelTestServer",
    "rxTestCred",
    "rxTestNodeServer",
    "scannerRedirAgent",
    "scredir_vchanclient",
    "sdrServer",
    "smctrlTestApp",
    "smlibTestApp",
    "smProcDump",
    "smtrackTestApp",
    "socket_vchan",
    "SystemProvider",
    "tsdrServer",
    "tsmmrServer",
    "tsmmrServerDShow",
    "usbRedirectionServer" "unityShell",
    "UserAgent",
    "V4VCustomAction",
    "vdmadmin",
    "vdmexport",
    "vdmimport",
    "vdmPerfmon",
    "vhublib",
    "vmlm",
    "vmToolsHook",
    "vmToolsHookProc",
    "uncsvc",
    "omnrxgservice",
    "hznsci",
    "hznusm",
    "hznusmlib",
    "whfbRedirection",
    "wmplayer",
    "ws_adminDLL",
    "ws_applaunchmgr",
    "ws_configmgr",
    "ws_consolesupport",
    "ws_consolesupport_mfw",
    "ws_crl",
    "ws_daas",
    "ws_dct",
    "ws_filesystem",
    "ws_java_bridgeDLL",
    "ws_java_bridge-exe",
    "ws_java_nativeNODEP",
    "ws_java_starter",
    "ws_javaview",
    "ws_klog",
    "ws_ldap",
    "ws_onrampkeymgr",
    "ws_perfmon",
    "ws_propertiessupport",
    "ws_scripts",
    "ws_updatemgr",
    "ws_usbstor",
    "ws_vdpvhub",
    "ws_vhub",
    "ws_winauthDLL",
    "wsauth",
    "wscredf",
    "wslogonscriptlauncher",
    "wsnm",
    "wsnm_certauthority",
    "wsnm_certenroll",
    "wsnm_certlogon",
    "wsnm_credcache",
    "wsnm_desktop",
    "wsnm_helpdesk",
    "wsnm_jms",
    "wsnm_jmsbridge",
    "wsnm_mqtt",
    "wsnm_psgc",
    "wsnm_scredir",
    "wsnm_xmlapi",
    "wssm",
    "wssm_desktop",
    "wssm_helpdesk",
]

hosts64 = {}
hosts64["win64"] = "x64"
hosts32 = {}
hosts32["win32"] = "x86"
hosts = hosts64.copy()
hosts.update(hosts32)

publishDir = vmware.ReleasePackagesDir()
allNodes += stageBinariesForExportNodes(
    stageEnv, hosts, publishDir, exportNodes, copyNodeMap
)

# Build merge module (MSM) from rde-rft-all
msmInfoMap = {}

allNodes += publishRdeSdk(stageEnv, copyNodeMap)
allNodes += stageAndSignOpenssl(stageEnv)
allNodes += buildMergeModules(stageEnv, copyNodeMap, msmInfoMap)
allNodes += buildAndPublishPerfTrackerAll(stageEnv)
allNodes += publishHtml5mmrNativeMessagingHostDeps(hosts)
allNodes += stageUrlNativeMessageManifest(stageEnv)
allNodes += stageUrlRedirectionWindows(stageEnv)
allNodes += stageUriRdLsaSignFiles(stageEnv)
# Skip Test stages for CodeQL scan
if not CODEQL_SCAN:
    allNodes += stagePrintRedirTest(stageEnv)
    allNodes += stageRTAVServerComponentTest(stageEnv)
    allNodes += stageUSBServerComponentTest(stageEnv)
    allNodes += stageUSBServerUnitTest(stageEnv)
    allNodes += stageRdeServerComponentTest(stageEnv)
    allNodes += stageSdrServerComponentTest(stageEnv)
    allNodes += stageClipboardServerComponentTest(stageEnv)
    allNodes += stageClipboardServerUnitTest(stageEnv)
    allNodes += stageFCPServerUnitTest(stageEnv)
    allNodes += stageMKSVchanDnDComponentTest(stageEnv)
    allNodes += stageTsdrComponentServerTest(stageEnv)
    allNodes += stageTSMMRServerComponentTestWindows(stageEnv)
    allNodes += stageDnDUt(stageEnv)
    allNodes += stageScreenCaptureUnitTest(stageEnv)
    allNodes += stageSmartcardServerComponentTest(stageEnv)
    allNodes += stageCommonDrivers(stageEnv)
# Copy all Unity-related files to our custom directory.
allNodes += stageUnityWin(stageEnv, copyNodeMap)

# Publish additional dependencies
nodeName = "rtavPlugin"
rtavDeps = [
    "FFMPEG_AVCODEC_REDIST",
    "FFMPEG_AVUTIL_REDIST",
]
publishAdditionalDependencies(stageEnv, hosts64, nodeName, rtavDeps, isPath=True)


# Publish additional dependencies of rdeSvc.
rdehosts = {
    "win64": "x64",
    "win32": "x86",
}
for host in rdehosts:
    rdeServerEnv = vmware.LookupEnv("rdeServer-env", host)
    stagePP = vmware.PathPrefixer(vmware.pkg.stagePath)
    rdeServerStagePP = stagePP / rdehosts[host]

    # Gather the list of all dependencies to copy.
    deps = []
    for name in [
        "SIGC_REDIST",
        "GLIBMM_REDIST",
        "LIBICONV_REDIST",
        "GETTEXT_REDIST",
        "REDISTRIBUTE_MFW_DLL",
        "GLIB_REDIST",
        "PCRE2_REDIST",
    ]:
        dep = rdeServerEnv[name]
        if isinstance(dep, list):
            deps += dep
        else:
            deps.append(dep)

    for dep in deps:
        dep = File(dep).abspath
        # First copy the actual dependency dll or exe.
        copyNode = stageEnv.FileCopy(
            File(rdeServerStagePP / os.path.basename(dep)).abspath, dep
        )
        stageEnv.SignFile(copyNode)
        allNodes += copyNode

        # Next copy the lib and/or pdb, if they exist.
        for ext in [".lib", ".pdb"]:
            root, oldExt = os.path.splitext(dep)
            source = root + ext
            if os.path.isfile(source):
                allNodes += stageEnv.LinkCopy(
                    File(rdeServerStagePP / os.path.basename(source)), source
                )


# Publish watermarkDrawer.
for host in hosts:
    rdeServerStagePP = stagePP / hosts[host]
    allNodes += vmware.pkg.StageDeliverables(
        stageEnv, "watermarkDrawer", host, Dir(rdeServerStagePP).abspath
    )

# Stage all the installer binaries (msm, cab and msi)
for host in hostMapInstallerCompcache:
    platformArch = hostMapInstallerCompcache[host]
    hostPath = Dir(compcacheStagePath / platformArch).abspath

    for name in stageInstallerNodesCompcache:
        name = "%s-%s" % (name, platformArch)
        if not vmware.pkg.NodeExists(name, host, packaged=True):
            continue

        node = StageDeliverables(stageEnv, name, host, hostPath, packaged=True)
        allNodes += node

# Stage files in the compcache
extrasCompcacheDir = Dir(compcacheStagePath).Dir("extras")
for srcFile, destDir in stageExtraFilesInCompcache:
    destDir = extrasCompcacheDir.Dir(destDir)
    srcFileNode = File(srcFile)
    allNodes += stageEnv.FileCopy(destDir.File(srcFileNode.name), srcFileNode)

# Stage and sign files in compcache
signedExtrasCompcacheDir = extrasCompcacheDir.Dir("signed")
for srcFile in stageSignExtraFilesInCompcache:
    srcFileNode = File(srcFile)
    copySrcFileNode = stageEnv.FileCopy(
        signedExtrasCompcacheDir.File(srcFileNode.name), srcFileNode
    )
    stageEnv.SignFile(copySrcFileNode)
    allNodes += copySrcFileNode

# Stage header files in the compcache
headerCompcacheDir = Dir(compcacheStagePath).Dir("headers")
for headerFile in stageHeadersInCompcache:
    headerFileNode = File(headerFile)
    allNodes += stageEnv.FileCopy(
        headerCompcacheDir.File(headerFileNode.name), headerFileNode
    )

# Create entries in the project file for entries in stageNodesPublish. These
# nodes *do not* go in allNodes because they are just local build aliases.
for name in stageNodesPublish:
    if name in stageFrozenCommonNodes or name in stageFrozenInstallerNodes:
        # Skip any nodes that have their frozen flag set or we will end up with
        # duplicate copy nodes.
        log.info(
            "Node %s has its frozen flag set. Not creating a staging " "node." % name
        )
        continue

    for host in hostMapPublish:
        if not vmware.pkg.NodeExists(name, host):
            continue

        dir = os.path.join(installerStagePath, hostMapPublish[host])
        publishVSNodes = vmware.pkg.StageDeliverables(stageEnv, name, host, dir)
        vmware.Alias("%s-%s-stage" % (name, host), publishVSNodes)

    if name not in visualStudioProjectNodes:
        visualStudioProjectNodes.append(name)

# Publish fabulatech drop components version json file
allNodes += stageFabulatechJsonFile(stageEnv)

# Zip each component separately in the Deliverables page (a.k.a publishDir)
publishDir = vmware.ReleasePackagesDir()
if publishDir is not None:

    # Horizon extras stage folder
    extrasStageDirRoot = os.path.join(productBuildRoot, "extrasStage")
    extrasStageNodes = []

    allNodes += stageArm64Binaries(
        stageEnv, publishDir, extrasStageDirRoot, extrasStageNodes, exportArm64Nodes
    )

    stageRdeRftExt(stageEnv, publishDir, extrasStageDirRoot, allNodes)

    for host in hostMapPublish:
        publishStagePath = os.path.join(productBuildRoot, "publishStage", host)
        for name in stageNodesPublish:
            # Allow for missing nodes in case not all platforms
            # build all deliverables.
            if not vmware.pkg.NodeExists(name, host):
                continue

            # Stage and publish to Deliverables page.
            allNodes += StageDeliverables(stageEnv, name, host, publishStagePath)

        for name in stageInstallerNodesPublish:
            # Add arch suffix for installer nodes
            node = "%s-%s" % (name, hostMapPublish[host])
            if not vmware.pkg.NodeExists(node, host, packaged=True):
                continue

            # Stage and publish to Deliverables page.
            allNodes += StageDeliverables(
                stageEnv, node, host, publishStagePath, packaged=True
            )

        # Stage and publish csso-es files
        winPlatform = host.replace("win", "")
        cssoesZipFile = "hzn-csso-es-%s-INTERNAL-%s-%s.zip" % (
            winPlatform,
            vmware.ProductVersionNumber(),
            vmware.BuildNumber(),
        )
        cssoesZipFile = Dir(publishDir).File(cssoesZipFile)
        allNodes += StageDeliverablesIntoZip(
            stageEnv, cssoesNodes, host, publishStagePath, cssoesZipFile
        )

        if host == "win64":
            allNodes += stageDevtaps(stageEnv, host, publishDir)
            # Skip Test stages for CodeQL scan
            if not CODEQL_SCAN:
                allNodes += stageCataFwTestsFramework(
                    stageEnv, host, publishDir, publishStagePath
                )
                allNodes += stageRdshTestFramework(
                    stageEnv, host, publishDir, publishStagePath
                )
                allNodes += stageVncServer(stageEnv, host, publishDir, publishStagePath)
                allNodes += stageDctComponentTest(
                    stageEnv, host, publishDir, publishStagePath
                )
                allNodes += stageCoreTestsZip(stageEnv, host, publishDir)
                allNodes += stageHzaPrepTests(stageEnv, host, publishDir)
                allNodes += stageRdsTestZips(stageEnv, host, publishDir)
                allNodes += stageTsdrServerUnitTest(stageEnv, host, publishDir)
                allNodes += stageHorizonRxTestAgent(stageEnv, host, publishDir)
                allNodes += stageHorizonRxUT(stageEnv, host, publishDir)
                allNodes += stageHzWebauthnTests(stageEnv, host, publishDir)
                allNodes += stageWhfbRedirectionTestZip(stageEnv, host, publishDir)
                allNodes += stageUpdateToolTestsZip(stageEnv, host, publishDir)

        # Stage all pdb files for the extras zip
        extrasStageDir = os.path.join(extrasStageDirRoot, host)
        for name in allStageNodes:
            if not vmware.pkg.NodeExists(name, host):
                continue

            # Don't publish the libraries in the debugging zip as they aren't
            # needed.
            node = vmware.LookupNode(name, host)
            if os.path.splitext(node[0].abspath)[1] == ".lib":
                continue

            extrasStageNodes += vmware.pkg.StageDeliverables(
                stageEnv, name, host, extrasStageDir, signBinaries=False
            )

        # Also stage the signed pdb files in the extras zip
        if hostMapPublish[host] in signedArchs:
            platformArch = hostMapPublish[host]
            for name in stageFrozenCommonNodes + stageFrozenInstallerNodes:
                frozenFilesExtractNode = []
                if name in vmware.pkg.frozenFilesDetails:
                    stageEnv.LoadTool("artifactory")
                    frozenFilesExtractNode = stageEnv.FetchFromArtifactory(
                        artifactorySignedFileRoot
                        + vmware.pkg.frozenFilesDetails[name]["file"],
                        sha256=vmware.pkg.frozenFilesDetails[name]["sha256"],
                        extract=True,
                        extractTargetFiles=["{}/{}.pbd".format(platformArch, name)],
                        registerNodeName=vmware.pkg.frozenFilesDetails[name]["file"],
                    )
                    frozenFilesDir = os.path.dirname(frozenFilesExtractNode[0].abspath)

                # Stage the signed files in the extras zip
                extrasStageNodes += vmware.utils.StageCopyDirectory(
                    frozenFilesDir, Dir(extrasStageDir), stageEnv.LinkCopy
                )
                stageEnv.Depends(extrasStageNodes, frozenFilesExtractNode)

        # Stage the python test API.
        pythonZipFile = Dir(publishDir).File(
            "Omnissa-Horizon-Agent-PythonAPI-%s.zip"
            % ("x86" if host == "win32" else "x86_64")
        )

        # MessageFrameWork.dll
        mfw_node = vmware.LookupNode("MessageFrameWork", host=stageEnv.Host().Name())[0]

        pythonZipSources = [
            ("#bora/apps/horizonAgent/test/lib/pythonsupport", "mfw"),
            ("#bora/apps/horizonAgent/test/lib/pythonsupport", "tests"),
            ("#bora/apps/horizonAgent/test/lib/pythonsupport", "wssm"),
            (mfw_node.dir.abspath, mfw_node.name),
        ]

        # pythonsupport files are used in horizonserver builds here :
        # https://opengrok.eng.vmware.com/source/xref/view-server-main.perforce.1666/mojo/vdi/make/mk/targets-windows-tests.mk#570
        # These files end up in apifvt.dir,
        # which is zipped into Vmware-viewtest-<buildid>.zip
        # deliverable which is published. Scale test teams consume this file
        # and make use of powershell cmdlets. So we stage them in compcache.

        for i in range(3):
            pythonsupportPP = vmware.PathPrefixer(pythonZipSources[i][0])
            srcDir = Dir(pythonsupportPP).Dir(pythonZipSources[i][1])
            destDir = (
                Dir(extrasCompcacheDir).Dir("pythonsupport").Dir(pythonZipSources[i][1])
            )
            allNodes += vmware.DirCopy(
                vmware.EnumerateSourceDir(srcDir), srcDir, destDir, stageEnv
            )

        allNodes += stageEnv.Zip(pythonZipFile, pythonZipSources)

        # x86 and x64 wsauth msms are needed by the client.
        wsauthMsmDest = os.path.join(publishDir, "shared-client", host)
        allNodes += vmware.pkg.StageDeliverables(
            stageEnv,
            "wsauth-msm-%s" % hostMapPublish[host],
            host,
            wsauthMsmDest,
            signBinaries=False,
            packaged=True,
        )

    # Copy the obfuscation mapping file to the extras zip to enable debugging
    # of the obfuscated Java files
    coreLibMapNode = vmware.LookupNode("ObfuscationMapcore-lib", host="generic")
    agentLibMapNode = vmware.LookupNode("ObfuscationMapagent-lib", host="generic")

    # Create and publish the zip file.
    zipSources = [
        (extrasStageDirRoot, "win32"),
        (extrasStageDirRoot, "win64"),
    ]
    # For obj builds, we do not obfuscate and the map file will not exist.
    if coreLibMapNode:
        zipSources.append((coreLibMapNode[0].dir.abspath, coreLibMapNode[0].name))
    if agentLibMapNode:
        zipSources.append((agentLibMapNode[0].dir.abspath, agentLibMapNode[0].name))

    # Stage some crucial 3rd party pdbs in the extras zip
    mqttEnv = vmware.LookupEnv("wsnm_mqtt-env", host=host)
    zipSources += [(mqttEnv["MQTT_REDIST_DIR"], "paho-mqtt3as.dll")]
    zipSources += [(mqttEnv["MQTT_REDIST_DIR"], "paho-mqtt3as.pdb")]

    horizonExtrasZipFile = "hzn-horizonextras-INTERNAL-%s-%s.zip" % (
        vmware.ProductVersionNumber(),
        vmware.BuildNumber(),
    )
    horizonExtrasZipFile = Dir(publishDir).File(horizonExtrasZipFile)
    if not CODE_COV and not CODEQL_SCAN:
        allNodes += stageEnv.Zip(horizonExtrasZipFile, zipSources)
    stageEnv.Depends(horizonExtrasZipFile, extrasStageNodes)

    # Stage the final view agent executable
    for exe in exeNodes:
        allNodes += stageEnv.LinkCopy(Dir(publishDir).File(exe.name), exe)

    # Generate and sign cab files for drivers and lsa binaries.
    # Each binary will have its own cab file.
    for binary in signedCabNodes:
        cabFileName = binary.replace("-signed", "")
        cabNode = stageEnv.StageAndGenerateCabFiles(
            productBuildRoot, {binary: signedCabNodes[binary]}, cabFileName
        )
        stageEnv.Depends(cabNode, lsaSignedNodes)
        cabFullPathName = (
            Dir(publishDir).Dir("microsoft-unsigned").File(cabNode[0].name)
        )
        allNodes += stageEnv.LinkCopy(cabFullPathName, cabNode[0])
        stageEnv.SignFile(cabFullPathName)

    # If horizonagent is a gobuild dependency, it means that this is
    # the horizonagentpatch build.
    if vtools.common.GobuildComponentPathExists("horizonagent"):
        patchNode = generateMspPatch(stageEnv, "x64")
        stageEnv.SignFile(patchNode)
        allNodes += patchNode

# Support the "devenv" command, which automatically generates a solution file.
visualStudioNodeMap = {}
visualStudioX86StageDir = os.path.join(installerStagePath, "x86")
visualStudioX64StageDir = os.path.join(installerStagePath, "x64")
visualStudioARM64StageDir = os.path.join(installerStagePath, "ARM64")
for projectNode in visualStudioProjectNodes:
    if (
        vmware.pkg.NodeExists(projectNode, "win32")
        or vmware.pkg.NodeExists(projectNode, "win64")
        or vmware.pkg.NodeExists(projectNode, "ARM64")
    ):
        visualStudioNodeMap[projectNode] = (
            projectNode + "-%s-stage",
            visualStudioX86StageDir,
            visualStudioX64StageDir,
            visualStudioARM64StageDir,
        )
# Add non staged project nodes
for projectNode in nonStagedVisualStudioProjectNodes:
    if vmware.pkg.NodeExists(projectNode, "win32") or vmware.pkg.NodeExists(
        projectNode, "win64"
    ):
        visualStudioNodeMap[projectNode] = None

# Generate any nodes that require that supporting dlls be copied.
generateDevenvStageNodes(stageEnv, visualStudioNodeMap)

visualStudioSolutionNode = generateSolution(visualStudioNodeMap)
vmware.Alias("devenv", getSolutionOpenAction(stageEnv, visualStudioSolutionNode))

# Generate CDS payload node and publish CDS scripts
if publishDir is not None:
    allNodes += generateCDS(stageEnv)
    allNodes += stageCdsScripts(stageEnv)

# Generate agent metadata node
allNodes += generateMetadataConfig(stageEnv)

# Also add the logic to the official builds. This will ensure that we have
# tinderbox and official build coverage of generateSolution(). In this case,
# we don't want to put the solution open action in allNodes, because it would
# be unexpected.
allNodes += visualStudioSolutionNode
# Skip CodeCoverage stage for CodeQL scan
if not CODEQL_SCAN:
    # Package code coverage data at last
    allNodes += vmware.pkg.StageCodeCoverageData(stageEnv, "horizonagent", allNodes)

vmware.Alias("horizonagent", allNodes)
