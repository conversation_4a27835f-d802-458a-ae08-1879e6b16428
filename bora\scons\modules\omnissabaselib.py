# Copyright (c) Omnissa, LLC. All rights reserved.
# This product is protected by copyright and intellectual property laws in the
# United States and other countries as well as by international treaties.
# -- Omnissa Restricted

"""omnissabaselib - A huge chunk of bora/lib in aggregate form.

omnissabaselib (omnissabase.dll, libomnissabase.so.0, libomnissabase.dylib)
contains and exports functions from libraries within bora/lib.  Symbols
are not exported by default.  Instead, its associated module definition
file, omnissabaselib.pdef, specifies which symbols to export.

Maintainers:
   Platform Infrastructure <<EMAIL>>
   Everyone who touches bora/lib <<EMAIL>>
"""

import os
import vmware

env = vmware.Host().DefaultEnvironment()
vmx86Flag = vmware.GetProductDefinition().GetVmx86Flag()
pname = vmware.GetJustProductName(vmware.GetRequestedProduct())

log = vmware.GetLogger("main")
log.info("Compiled with vmx86 flag: %s" % vmx86Flag)

# Common Environment
env.LoadTool(
    [
        "libpng",
        "libssl",
        "libz",
        "vm-product",
    ]
)

if vmware.Host().IsWindows():
    env.Append(LIBS=["iphlpapi.lib"])

env.Append(
    CPPPATH=[
        "#bora/public",
        "#bora/lib/public",
        "#bora/lib/distribute",
        "#bora/apps/lib",
        "#bora/apps/lib/public",
    ]
)

# Host-specific Environment
if vmware.Host().IsWindows():
    env.LoadTool(
        [
            "atlmfc",
            "libpng",
            "libxdr",
            "msvcrt",
            "perl",
        ]
    )
    if env["MSVC_VERSION_TUPLE"] < (12, 0):
        env.LoadTool("wdk")
        env.Append(
            CPPPATH=[
                "$WDKROOT/inc/crt",
            ],
            LIBPATH=[
                os.path.join("$WDKROOT", "lib/w2k/i386"),
            ],
        )

    env.Append(
        CPPDEFINES={
            "_MBCS": None,
            "VMX86_IMPORT_DLLDATA": None,
            "OMNISSABASELIB": None,
        },
        LINKFLAGS=["/subsystem:console", "/NODEFAULTLIB:LIBC.lib", "/DLL"],
        LIBS=[
            "kernel32.lib",
            "user32.lib",
            "gdi32.lib",
            "advapi32.lib",
            "ole32.lib",
            "uuid.lib",
            "ws2_32.lib",
            "winmm.lib",
            "Wininet.lib",
            "mpr.lib",
            "userenv.lib",
            "oleaut32.lib",
            "oldnames.lib",
            "setupapi.lib",  # needed by lib/usblib
            "shell32.lib",
            "shlwapi.lib",
            "Dbghelp.lib",
            "Mswsock.lib",
            "bcrypt.lib",
            "crypt32.lib",
            "winspool.lib",
            "Version.lib",
            "wldap32.lib",
            "wbemuuid.lib",
            "Dhcpcsvc.lib",
            "winhttp.lib",
            "cryptui.lib",
            "winscard.lib",
            "qwave.lib",
        ],
        SHLIBFLAGS=[
            "-subsystem:windows",
            "-DLL",
            "-nologo",
        ],
    )
    if vmware.Host().IsARM64EC():
        env.Append(
            SHLIBFLAGS=[
                "-machine:ARM64EC",
                "/opt:nolbr",
            ]
        )
    elif vmware.Host().IsARM64():
        env.Append(SHLIBFLAGS=["-machine:ARM64"])
    elif vmware.Host().Is64Bit():
        env.Append(SHLIBFLAGS=["-machine:X64"])
    else:
        env.Append(SHLIBFLAGS=["-machine:X86"])

elif vmware.Host().IsLinux():
    env.LoadTool(
        [
            "pcsc-lite",
            "xorg",
            "libx-xkbfile",
        ]
    )
    env.Append(
        CCFLAGS=[
            "-fPIC",
        ],
        LIBS=[
            "dl",
            "m",
            "png",
            "pthread",
            "X11",
            "z",
        ],
    )

elif vmware.Host().IsMac():
    env.Append(
        CCFLAGS=[
            "-fPIC",
        ],
        SHLIBFLAGS=[
            "-all_load",
            "-Wl,-single_module",
        ],
        FRAMEWORKS=[
            "CoreAudio",
            "AudioToolbox",
            "AudioUnit",
            "CoreFoundation",
            "Foundation",
            "AVFoundation",
            "CoreServices",
            "DiskArbitration",
            "IOBluetooth",
            "IOKit",
            "Security",
            "SystemConfiguration",
        ],
        LIBS=[
            "bz2",
            "png",
            "z",
        ],
    )


e = vmware.Executable("omnissabaselib", env=env)


# Host-specific default vmlibs exceptions
defaultVmlibsExceptions = []
if vmware.Host().IsLinux():
    defaultVmlibsExceptions = [
        "aioHttp",
        "keyboard",
        "sound",
        "vmlicense",
    ]


e.addDefaultLibs("vmlibs", exceptions=defaultVmlibsExceptions)


# Common libs
e.addStaticLibs(
    "vmlibs",
    [
        "bufferRegion",
        "blastSockets",
        "cityhash",
        "cpuidInfo",
        "d3des",
        "dnd",
        "dynxdr",
        "guestRpc",
        "hashMap",
        "hgFileCopy",
        "hgfs",
        "hostDisk",
        "image",
        "keyboard",
        "lock",
        "raster",
        "region",
        "remoteDevice",
        "sig",
        "slab",
        "thread",
        "udpProxy",
        "udpfec",
        "unityWindowTracker",
        "usbarblib",
        "usbenum",
        "usbenumarb",
        "usblib",
        "usbmisc",
        "user",
        "version",
        "vmclientrmks",
        "vdplib",
        "vvclib",
        "workerLib",
    ],
)

# Host-specific libs
if vmware.Host().IsWindows():
    e.addStaticLibs(
        "vmlibs",
        [
            "guestproto",
            "registrywin32",
            "win32auth",
            "winregistry",
            "wmi",
        ],
    )

    e.addSubdirs(["apps/omnissabaselib"])
    e.addResources(env, ["#bora/apps/omnissabaselib/omnissabaselib.rc"])
    e.addSubdirs(["lib/decoder"])
    e.addStaticLibs("vmlibs", ["trapapi"])

elif vmware.Host().IsMac():
    e.addStaticLibs(
        "vmlibs",
        [
            "appleXPC",
            "location",
            "serviceUser",
            "vvclib",
        ],
    )

basename = "omnissabase"
staticSuffix = "-static"

#
# Module definition (Win32), linker script (POSIX) shenanigans
#
# XXX Not yet supported on Mac OS, because the Apple linker is super strict
#     about exporting symbols not found in the library, and I don't have
#     the cycles required to audit omnissabaselib.pdef for non-Mac functions.
#
if not vmware.Host().IsMac():
    pdefNodes = [
        "#bora/apps/omnissabaselib/omnissabaselib.pdef",
    ]

    def generateSSLWrappersFunc(target, source, env):
        """Generates module-definition file for wrapped OpenSSL functions."""
        suffix = "\tNONAME" if vmware.Host().IsWindows() else ""
        f = open(target[0].abspath, "a")
        f.writelines(["%s%s\n" % (e, suffix) for e in env["OPENSSL_WRAPPER_NAMES"]])
        f.close()

    appendSSLWrappersAct = vmware.ActionWithDisplay(
        generateSSLWrappersFunc, "Generating OpenSSL wrappers " "pdef file..."
    )

    if "OPENSSL_WRAPPER_NAMES" in env:
        sslTargetPdef = os.path.join(
            vmware.LibraryDirectory("omnissabaselib"), "ssl.pdef"
        )
        env.Command(
            sslTargetPdef, env.Value(env["OPENSSL_WRAPPER_NAMES"]), appendSSLWrappersAct
        )
        pdefNodes.append(sslTargetPdef)

    # Pass this so omnissabaselib.pdef can avoid adding some functions that
    # Horizon doesn't compile.
    pdefDefs = {
        "VMX86_FLAG": vmx86Flag,
    }
    if vmware.Host().IsARM64EC():
        pdefDefs["IS_WIN_ARM64EC"] = True
    e.addCrossPlatformPdef(env, pdefNodes, pdefDefs)


sharedNode = e.createSharedLibraryNode(
    basename=basename, implib=vmware.Host().IsWindows(), export=True
)

if vmware.Host().IsLinux():
    env.AddPcscLiteDeps([], sharedNode)

staticNode = e.createStaticLibraryNode(basename=basename + staticSuffix, export=True)

if vmware.Host().IsLinux():
    env.AddPcscLiteDeps([], staticNode)

vmware.RegisterEnv("omnissabaselib-env", env)

if pname in ["hccrt"]:
    vmware.RegisterNode(sharedNode, "omnissabaselib")
    vmware.RegisterNode(staticNode, "omnissabaselib-static")
    vmware.Alias("omnissabaselib-build", [sharedNode, staticNode])
else:
    vmware.RegisterNode(staticNode, "omnissabaselib-static")
    vmware.Alias("omnissabaselib-build", [staticNode])
