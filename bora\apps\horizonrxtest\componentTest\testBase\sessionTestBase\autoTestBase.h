/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * autoTestBase.h --
 *
 *      Base class of auto tests.
 */

#pragma once

#include "testBase.h"


class AutoTestBase : public TestBase {
public:
   static void SetSessionManager(SessionManagementInterface *sessionMgr);
   static SessionManagementInterface *GetSessionManager()
   {
      ASSERT(mSessionManager != nullptr);
      return mSessionManager;
   }
   static int RunAllTests(uint32 flags);

protected:
   virtual void SetUp();
   virtual void TearDown();

   static void SetUpTestCase();
   static void TearDownTestCase();

private:
   static VmwHorizonClientProtocol mClientProtocol;
   static SessionManagementInterface *mSessionManager;
};
