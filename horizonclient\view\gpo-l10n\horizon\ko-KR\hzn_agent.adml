﻿<?xml version="1.0" encoding="utf-8"?>
<policyDefinitionResources xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" revision="1.0" schemaVersion="1.0" xmlns="http://www.microsoft.com/GroupPolicy/PolicyDefinitions">

   <displayName>Omnissa Horizon Agent</displayName>
   <description>Horizon Agent 구성 설정</description>
   <resources>
      <stringTable>
         <string id="SUPPORTED_Windows10_1607_VM">최소 Windows 10/Windows Server 2016 VDI 버전 1607 가상 시스템</string>

         <string id="Agent_Configuration">Agent 구성</string>

         <string id="Collaboration">공동 작업</string>

         <string id="Agent_Security">Agent 보안</string>

         <string id="Unity_Touch_Hosted_Apps">Unity Touch 및 호스팅된 애플리케이션</string>

         <string id="Unity_Filter">Unity 필터 규칙 목록</string>

         <string id="Unity_Filter_Rules_Desc">이 정책은 호스팅된 애플리케이션을 원격으로 사용할 때 창에 대한 필터 규칙을 지정합니다. 필터 규칙은 Horizon Agent에서 사용자 지정 애플리케이션을 지원하는 데 사용됩니다. 이 GPO는 창 배경색이 검은색이거나 드롭다운 창 크기가 제대로 조정되지 않는 것과 같은 창 표시 문제가 있을 때 사용해야 합니다.

규칙을 설정하는 첫 번째 단계는 규칙을 적용할 창의 특성을 확인하는 것입니다. 다음과 같이 식별 가능한 많은 특성이 있을 수 있습니다.

1. 사용자 지정 규칙에서 classname=XYZ로 식별되는 Windows 클래스 이름
2. company=XYZ로 식별되는 제품 회사
3. product=XYZ로 식별되는 제품 이름
4. major=XYZ로 식별되는 제품 주 버전
5. minor=XYZ로 식별되는 제품 부 버전
6. build=XYZ로 식별되는 제품 빌드 번호
7. revision=XYZ로 식별되는 제품 수정 번호

기본 특성으로 &quot;Window 클래스 이름&quot;만 사용하는 것이 가장 일반적입니다(예: classname=CustomClassName). 하지만 하나의 특정 제품으로 규칙을 제한해야 하는 경우에 대비하여 다른 특성이 제공됩니다. 실행 파일의 [파일 속성] 창에서 이러한 특성을 찾을 수 있으며, 이러한 특성을 검색할 때는 특수 문자를 포함하여 대/소문자가 정확히 일치해야 합니다. 여러 특성이 제공될 경우 규칙이 창에 적용되려면 모든 특성을 일치시켜야 합니다.

일단 특성을 식별한 후에는 작업을 선택해야 합니다. 작업은 action=block 또는 action=map이어야 합니다. action=block은 창을 클라이언트에 원격으로 사용하지 않도록 Horizon Agent에 지시합니다. 클라이언트에 너무 크거나 정상적인 창 포커스 동작을 방해하는 창이 표시될 때 사용됩니다. action=map은 창을 하드 코딩된 특정 형식으로 처리하도록 Horizon Agent에 지시합니다.

action=map을 설정하는 경우 창을 매핑할 형식도 지정해야 합니다. 이 작업은 type=XYZ를 포함하여 수행합니다. 사용 가능한 모든 형식 값 목록은 다음과 같습니다. normal, panel, dialog, tooltip, splash, toolbar, dock, desktop, widget, combobox, startscreen, sidepanel, taskbar, metrofullscreen, metrodocked

잘못 동작하는 애플리케이션을 수정하기 위해 설정할 수 있는 두 가지 규칙 예제는 다음과 같습니다.

1. 원격으로 사용하지 않아야 하는 창을 필터링할 수 있습니다.
   - 클래스 이름이 MyClassName인 모든 창을 차단하려면 규칙 &quot;classname=MyClassName;action=block&quot;을 사용합니다.
   - 제품 MyProduct에서 모든 창을 차단하려면 규칙 &quot;product=MyProduct;action=block&quot;을 사용합니다.
2. 창을 올바른 형식으로 매핑할 수 있습니다. 창이 잘못된 형식으로 매핑되었는지를 확인하는 것은 매우 어려우므로 Omnissa 지원 팀에서 이 작업을 지시한 경우에만 수행합니다.
   - 사용자 지정 클래스를 콤보 상자 형식으로 매핑하려면 규칙 &quot;classname=MyClassName;action=map;type=combobox&quot;를 사용합니다.

참고: 이 GPO는 %ProgramData%\Omnissa\RdeServer\Unity Filters에 설치된 필터링 규칙보다 우선 순위가 낮습니다.</string>

         <string id="Smartcard_Redirection">스마트 카드 리디렉션</string>

         <string id="Local_Reader_Access">로컬 판독기 액세스</string>

         <string id="True_SSO_Configuration">True SSO 구성</string>

         <string id="Whfb_Certificate_Redirection">Whfb 인증서 리디렉션</string>

         <string id="Whfb_Certificate_Allowed_Applications">허용된 실행 파일 목록</string>

         <string id="Whfb_Certificate_Allowed_Applications_Desc">리디렉션된 Whfb 인증서를 사용할 수 있는 실행 파일 목록</string>

         <string id="View_USB_Configuration">Horizon USB 구성</string>

         <string id="Client_Downloadable_only_settings">클라이언트 다운로드 가능한 설정만</string>

         <string id="Recursive_Domain_Enumeration">신뢰된 도메인의 재귀적 열거</string>

         <string id="Recursive_Domain_Enumeration_Desc">서버가 상주하는 도메인이 신뢰하는 모든 도메인이 열거되는지 확인합니다. 완전한 신뢰 체인을 설정하기 위해 각 신뢰된 도메인에서 신뢰하는 도메인 또한 열거되고 모든 신뢰된 도메인을 찾을 때까지 프로세스가 재귀적으로 계속됩니다. 로그인 시 모든 신뢰된 도메인을 클라이언트에 사용할 수 있도록 이 정보가 Horizon Connection Server에 전달됩니다.

이 속성은 기본적으로 사용하도록 설정됩니다. 사용되지 않도록 설정된 경우, 직접 신뢰된 도메인만 열거되며 원격 도메인 컨트롤러에 연결되지 않습니다.

참고: 복합 도메인 관계를 가진 환경에서(예: 포리스트의 도메인 사이에서 신뢰된 여러 포리스트 구조를 사용) 이 프로세스 완료에는 몇 분이 걸릴 수 있습니다.</string>

         <string id="Force_MMR_to_use_overlay">MMR이 소프트웨어 오버레이를 강제로 사용하도록 설정</string>

         <string id="Force_MMR_to_use_overlay_Desc">MMR은 더 나은 성능을 위해 하드웨어 오버레이를 사용하여 비디오를 재생하려고 합니다. 그러나 여러 디스플레이를 사용할 때 하드웨어 오버레이는 기본 디스플레이 또는 WMP가 시작된 디스플레이 중 하나에만 존재합니다. WMP를 다른 디스플레이로 끌어오면 비디오가 검은색 사각형으로 표시됩니다. MMR이 모든 디스플레이에서 작동되는 소프트웨어 오버레이를 강제로 사용하도록 하려면 이 옵션을 사용합니다.</string>

         <string id="Enable_multi_media_acceleration">멀티미디어 가속화 사용</string>

         <string id="Enable_multi_media_acceleration_Desc">에이전트에 MMR(멀티미디어 리디렉션)이 사용되도록 설정되어 있는지 여부를 지정합니다. MMR은 TCP 소켓을 통해 원격 시스템의 특정 코덱에서 클라이언트로 멀티미디어 데이터를 직접 전달하는 Microsoft DirectShow 필터입니다. 그런 다음 데이터는 재생되는 클라이언트에서 바로 디코딩됩니다. 클라이언트의 리소스가 부족하여 로컬 멀티미디어 디코딩을 처리할 수 없는 경우 관리자는 MMR을 사용하지 않도록 설정할 수 있습니다.

참고: Horizon Client 비디오 디스플레이 하드웨어에 오버레이 지원 기능이 없는 경우 MMR은 올바르게 작동하지 않습니다. MMR 정책은 오프라인 데스크톱 세션에 적용되지 않습니다.</string>

         <string id="AllowDirectRDP">Direct RDP 허용</string>

         <string id="AllowDirectRDP_Desc">비 Horizon Client가 RDP를 사용하여 Horizon 데스크톱에 직접 연결할 수 있는지 여부를 결정합니다. 사용되지 않도록 설정되면 에이전트는 Horizon Client 또는 Horizon Portal을 통한 Horizon 관리 연결만 허용합니다.

이 속성은 기본적으로 사용하도록 설정됩니다.</string>

         <string id="AllowSingleSignon">Single Signon 허용</string>

         <string id="AllowSingleSignon_Desc">SSO(Single Sign-On)가 사용자를 Horizon 데스크톱에 연결하는 데 사용되는지 여부를 결정합니다. 사용하도록 설정되면 사용자는 Horizon Client 또는 Horizon Portal에 연결될 때 자신의 자격 증명만 입력하면 됩니다. 사용되지 않도록 설정되면 사용자는 원격 연결이 설정될 때 다시 인증해야 합니다.

이 속성을 사용하려면 Horizon Agent의 보안 인증 구성 요소가 데스크톱에 설치되어야 하며, 이 속성은 기본적으로 사용되도록 설정됩니다.</string>

         <string id="AutoPopulateLogonUI">로그온 UI 자동 채우기</string>

         <string id="AutoPopulateLogonUI_Desc">로그온 UI 인터페이스의 사용자 이름 필드가 자동으로 채워지는지 여부를 결정합니다. 이 속성은 기본적으로 사용하도록 설정되며 Single Sign On이 사용되지 않도록 설정되었거나 작동하지 않는 경우에만 RDS에 적용됩니다.</string>

         <string id="ConnectionTicketTimeout">연결 티켓 시간 초과</string>

         <string id="ConnectionTicketTimeout_Desc">Horizon 연결 티켓이 유효한 시간(초)을 지정합니다. 연결 티켓은 Horizon Agent에 연결할 때 Horizon Client에서 사용되며 확인 및 Single Sign-On 목적으로 사용됩니다.

보안상의 이유로 이러한 티켓은 지정된 기간 내에만 유효합니다. 이 속성을 명시적으로 설정하지 않으면 기본값인 900초가 적용됩니다.</string>

         <string id="CredentialFilterExceptions">자격 증명 필터 예외</string>

         <string id="CredentialFilterExceptions_Desc">에이전트 CredentialFilter를 로드하도록 허용되지 않는 세미콜론으로 구분된 실행 파일 이름 목록입니다. 파일 이름에는 경로 및 접미사가 없어야 합니다.</string>

         <string id="RDPVcBridgeUnsupportedClients">RDPVcBridge 지원되지 않는 클라이언트</string>

         <string id="RDPVcBridgeUnsupportedClients_Desc">RDPVcBridge를 지원하지 않는 쉼표로 구분된 Horizon Client 유형 목록입니다.</string>

         <string id="Disable_Time_Zone_sync">표준 시간대 동기화 사용 안 함</string>

         <string id="Disable_Time_Zone_sync_Desc">Horizon 데스크톱의 표준 시간대가 연결된 클라이언트의 표준 시간대와 동기화되는지 여부를 결정합니다. 사용하도록 설정되면 이 속성은 Horizon Client 구성 정책의 '표준 시간대 전달 사용 안 함' 속성이 사용 안 함으로 설정되지 않은 경우에만 적용됩니다.

이 속성은 기본적으로 사용하지 않도록 설정됩니다.</string>

         <string id="Keep_Time_Zone_sync_disconnect">연결 해제 시 표준 시간대 동기화 유지(VDI)</string>

         <string id="Keep_Time_Zone_sync_disconnect_Desc">[표준 시간대 동기화]를 사용하도록 설정하고 이 속성을 사용하도록 설정하면 원격 데스크톱의 표준 시간대가 가장 최근에 연결이 끊긴 클라이언트의 표준 시간대와 동기화된 상태로 유지됩니다.

이 속성을 사용하지 않도록 설정하면 최종 사용자 세션의 연결이 끊어질 때 원격 데스크톱의 표준 시간대가 복원됩니다.

이 설정은 원격 데스크톱 서비스 역할이 사용되도록 설정될 경우 RDSH 호스트에 적용되지 않습니다.

이 속성은 기본적으로 사용하지 않도록 설정됩니다.</string>

         <string id="Keep_Time_Zone_sync_logoff">로그오프 시 표준 시간대 동기화 유지(VDI)</string>

         <string id="Keep_Time_Zone_sync_logoff_Desc">[표준 시간대 동기화]를 사용하도록 설정하고 이 속성을 사용하도록 설정하면 원격 데스크톱의 표준 시간대가 가장 최근에 로그오프된 클라이언트의 표준 시간대와 동기화된 상태로 유지됩니다.

이 속성을 사용하지 않도록 설정하면 최종 사용자 세션이 로그오프될 때 원격 데스크톱의 표준 시간대가 복원됩니다.

이 설정은 원격 데스크톱 서비스 역할이 사용되도록 설정될 경우 RDSH 호스트에 적용되지 않습니다.

이 속성은 기본적으로 사용하지 않도록 설정됩니다.</string>

          <string id="Enable_ClientMediaPerm_Popup">브라우저 리디렉션을 사용한 화면 공유를 위해 탭, 화면 및 애플리케이션 선택기 사용</string>

          <string id="Enable_ClientMediaPerm_Popup_Desc">사용하도록 설정되면 브라우저 리디렉션을 사용하여 화면을 공유하면 브라우저 탭, 화면 또는 애플리케이션을 선택하는 선택기가 표시됩니다. 이 속성은 기본적으로 사용하도록 설정됩니다.</string>

		  <string id="Toggle_Display_Settings_Control">디스플레이 설정 제어 전환</string>

         <string id="Toggle_Display_Settings_Control_Desc">Horizon Client가 연결된 동안 디스플레이 제어판 애플릿의 [설정] 페이지를 사용하지 않도록 설정할지 결정합니다.

이 속성은 PCoIP 프로토콜을 사용하는 세션에만 적용됩니다. 이 속성은 기본적으로 사용하도록 설정됩니다.</string>

         <string id="DpiSync">DPI 동기화</string>

         <string id="DpiSync_Desc">원격 세션의 시스템 전체 DPI 설정을 조정합니다. 사용하도록 설정되었거나 구성되지 않은 경우에는 원격 세션의 시스템 전체 DPI 설정이 클라이언트 운영 체제의 해당 DPI 설정과 일치하도록 설정됩니다. 사용하지 않도록 설정되면 원격 세션에 대한 시스템 전체 DPI 설정을 변경하지 마십시오.</string>

         <string id="DpiSyncPerMonitor">모니터당 DPI 동기화</string>

         <string id="DpiSyncPerMonitor_Desc">원격 세션 중에 여러 모니터에서 DPI 설정을 조정합니다. 사용하도록 설정되거나 구성되지 않은 경우 원격 세션 중에 모든 모니터의 DPI 설정이 클라이언트 운영 체제의 DPI 설정과 일치하도록 변경됩니다. DPI 설정이 사용자 지정된 경우 사용자 지정된 DPI 설정이 일치합니다. 사용하지 않도록 설정된 경우, 사용자는 새 원격 세션의 연결을 끊었다가 연결하여 DPI 변경 사항을 모든 모니터에 적용해야 합니다.</string>

         <string id="DisplayScaling">디스플레이 크기 조정</string>

         <string id="DisplayScaling_Desc">디스플레이 크기 조정 기능이 에이전트 측에서 허용되는지 여부를 제어합니다. 사용하도록 설정되거나 구성되지 않은 경우 디스플레이 크기 조정은 에이전트 측에서 허용되며 디스플레이 크기 조정 기능의 최종 켜짐 또는 꺼짐 상태는 클라이언트 측 구성에 따라 다릅니다. 사용하지 않도록 설정하면 클라이언트 측 구성에 관계없이 디스플레이 크기 조정 기능이 사용되지 않도록 설정됩니다. 이 구성은 모니터당 DPI 동기화를 사용하지 않도록 설정한 경우에만 적용됩니다.</string>

         <string id="DisallowCollaboration">공동 작업 끄기</string>

         <string id="DisallowCollaboration_Desc">이 설정은 Horizon Agent VM에서 공동 작업을 허용할지 여부를 구성합니다. 사용하도록 설정하면 공동 작업 기능이 완전히 꺼집니다. 이 설정을 사용하지 않도록 설정하거나 이를 구성하지 않은 경우 이 기능은 풀 수준에서 제어됩니다. 이 설정을 적용하려면 Horizon Agent 시스템을 재부팅해야 합니다.</string>

         <string id="AllowCollaborationInviteByIM">IM으로 공동 작업자 초대 허용</string>

         <string id="AllowCollaborationInviteByIM_Desc">이 설정은 사용자가 설치된 IM(인스턴트 메시지) 애플리케이션을 사용하여 공동 작업 초대를 보낼 수 있는지 여부를 구성합니다. 사용하지 않도록 설정하면 IM 애플리케이션이 설치되어 있어도 사용자가 IM을 사용하여 초대할 수 없습니다. 이 설정은 기본적으로 사용하도록 설정됩니다.</string>

         <string id="AllowCollaborationInviteByEmail">이메일로 공동 작업자 초대 허용</string>

         <string id="AllowCollaborationInviteByEmail_Desc">이 설정은 사용자가 설치된 이메일 애플리케이션을 사용하여 공동 작업 초대를 보낼 수 있는지 여부를 구성합니다. 사용하지 않도록 설정하면 이메일 애플리케이션이 설치되어 있어도 사용자가 이메일을 사용하여 초대할 수 없습니다. 이 설정은 기본적으로 사용하도록 설정됩니다.</string>

         <string id="AllowCollaborationControlPassing">공동 작업자에 대한 제어 전달 허용</string>

         <string id="AllowCollaborationControlPassing_Desc">이 설정은 사용자가 공동 작업 동안 다른 공동 작업자에게 입력 제어를 전달하도록 허용하는지 여부를 구성합니다. 이 설정은 기본적으로 사용하도록 설정됩니다.</string>

         <string id="MaxCollaboratorCount">최대 초대 가능 공동 작업자 수</string>

         <string id="MaxCollaboratorCount_Desc">이 설정은 사용자가 세션에 참여하도록 초대할 수 있는 최대 공동 작업자 수를 구성합니다. 기본 최대값은 5입니다.</string>

         <string id="CollaborationEmailInviteDelimiter">mailto: 링크에서 여러 이메일 주소에 사용되는 구분 기호</string>

         <string id="CollaborationEmailInviteDelimiter_Desc">이 설정은 mailto: 링크에서 여러 이메일 주소에 사용되는 구분 기호를 구성합니다. 이 정책이 구성되지 않으면 주요 이메일 클라이언트와의 최적의 호환성을 보장하기 위해 이메일 주소를 구분하는 데 기본값인 &quot;;&quot;(공백 없는 세미콜론)이 사용됩니다.

기본 이메일 클라이언트에서 이 구분 기호를 사용할 때 문제가 발생하는 경우 &quot;, &quot;(쉼표와 공백 1개) 또는 &quot;; &quot;(세미콜론과 공백 1개)과 같은 기타 조합을 사용할 수 있습니다. 이 값은 mailto: 링크에 사용되기 전에 URI로 인코딩되므로 이 항목을 URI 인코딩 값으로 설정하지 마십시오.</string>

         <string id="CollaborationClipboardIncludeOutlookURL">클립보드 텍스트에 Outlook 형식의 URL 포함</string>

         <string id="CollaborationClipboardIncludeOutlookURL_Desc">이 설정을 사용하도록 설정하면 Outlook 형식의 초대 URL이 클립보드 초대 텍스트에 포함됩니다. 최종 사용자가 클립보드 초대 텍스트를 이메일에 붙여넣을 것으로 예상되는 경우 이 설정을 사용하도록 설정합니다. 이 설정은 기본적으로 사용하지 않도록 설정됩니다.</string>

         <string id="CollaborationServerURLs">초대 메시지에 포함할 서버 URL</string>

         <string id="CollaborationServerURLs_Desc">이 설정을 사용하여 공동 작업 초대에 포함된 기본 URL을 재정의할 수 있습니다. 배포에 둘 이상의 서버 URL(예: 다른 내부 및 외부 URL 또는 포드당 URL)이 사용될 경우 이 값을 설정하는 것이 좋습니다.

이 값을 설정할 경우 첫 번째 열에는 선택적 포트가 있는 URL(예: &quot;horizon-ca.corp.int&quot; 또는 &quot;horizon-ca.corp.int:2323&quot;)이 포함되고, 두 번째 열에는 URL에 대한 간단한 설명(예: &quot;California 포드&quot; 또는 &quot;회사 네트워크&quot;)이 포함되어야 합니다. 설명은 목록에 여러 서버가 있을 때만 사용합니다.</string>

         <string id="UnAuthenticatedAccessEnabled">인증되지 않은 액세스 사용</string>

         <string id="UnAuthenticatedAccessEnabled_Desc">이 설정을 지정하면 인증되지 않은 액세스 기능을 사용할 수 있습니다. 변경 사항을 적용하려면 시스템을 다시 부팅해야 합니다. 인증되지 않은 액세스는 기본적으로 사용되도록 설정됩니다.</string>

         <string id="RdsAadAuthEnabled">Azure Active Directory Single Signon 사용</string>

         <string id="RdsAadAuthEnabled_Desc">이 설정은 Azure Active Directory Single Signon 기능을 사용하도록 설정합니다. 변경 사항을 적용하려면 시스템을 다시 부팅해야 합니다. 이 기능을 기본적으로 사용하도록 설정합니다.  이 기능은 Azure Active Directory 가입된 시스템에 따라 다릅니다.</string>

         <string id="CommandsToRunOnConnect">연결 시 실행할 명령</string>

         <string id="CommandsToRunOnConnect_Desc">처음 세션이 연결될 때 실행될 명령 목록입니다.</string>

         <string id="CommandsToRunOnReconnect">다시 연결 시 실행할 명령</string>

         <string id="CommandsToRunOnReconnect_Desc">연결이 끊긴 후 세션이 다시 연결될 때 실행될 명령 목록입니다.</string>

         <string id="CommandsToRunOnDisconnect">연결 해제 시 실행할 명령</string>

         <string id="CommandsToRunOnDisconnect_Desc">세션의 연결이 끊길 때 실행될 명령 목록입니다.</string>

         <string id="ShowDiskActivityIcon">디스크 활동 아이콘 표시</string>

         <string id="ShowDiskActivityIcon_Desc">시스템 트레이에 디스크 활동 아이콘을 표시합니다. 단일 프로세스에서만 사용할 수 있는 '시스템 추적 NT 커널 로거'를 사용합니다. 다른 목적에 필요할 경우에는 사용하지 않도록 설정하십시오. 기본값은 사용입니다.</string>

         <string id="SSO_retry_timeout">Single Sign-On 다시 시도 시간 초과</string>

         <string id="SSO_retry_timeout_Desc">Single Sign-On이 다시 시도되기까지의 시간(밀리초)을 지정합니다. Single Sign-On 다시 시도를 사용하지 않도록 설정하려면 0으로 설정합니다. 기본값은 5,000밀리초입니다.</string>

         <string id="Win10PhysicalAgentAudioOption">단일 세션 Windows 10 물리적 원격 데스크톱 시스템에 대한 오디오 옵션</string>

         <string id="Win10PhysicalAgentAudioOption_Desc">Horizon Windows 10 물리적 원격 데스크톱 시스템 세션에서 사용할 오디오 디바이스를 지정합니다. 기본값은 Horizon Client 끝점에 연결된 오디오 디바이스를 사용하는 것입니다.</string>

         <string id="WaitForLogoff">로그오프 시간 초과 대기</string>

         <string id="WaitForLogoff_Desc">로그온을 시도하기 전에 사용자의 이전 세션이 로그오프를 마칠 때까지 기다리는 시간(초)을 지정합니다. 대기를 사용하지 않도록 설정하고 즉시 실패하도록 하려면 0으로 설정합니다. 기본값은 10초입니다.</string>

         <string id="UseClientAudioDevice">Horizon Client 끝점에 연결된 오디오 디바이스 사용</string>

         <string id="UsePhysicalMachineAudioDevice">Horizon Windows 10 물리적 원격 데스크톱 끝점에 연결된 오디오 디바이스 사용</string>

         <string id="VDI_idle_time_till_disconnect">연결 끊기 전 유휴 시간(VDI)</string>

         <string id="VDI_idle_time_till_disconnect_Desc">사용자의 비활동 상태로 인해 VDI 데스크톱 세션 연결이 끊어질 때까지의 시간을 지정합니다.
이 설정을 사용하지 않도록 설정하거나 구성하지 않으면 VDI 데스크톱 세션의 연결이 끊어지지 않습니다. &quot;안 함&quot;을 선택하면 동일한 결과가 나타납니다.
참고: 연결이 끊어진 후 데스크톱 풀 또는 시스템이 자동으로 로그오프되도록 구성한 경우 해당 설정이 적용됩니다.</string>

         <string id="Accept_SSL_encr_framework_channel">SSL 암호화된 프레임워크 채널 허용</string>

         <string id="Accept_SSL_encr_framework_channel_Desc">SSL 암호화된 프레임워크 채널 허용 

사용: SSL 사용, 레거시 클라이언트가 SSL 없이 연결하도록 허용
사용 안 함: SSL 사용 안 함
강제 적용: SSL 사용, 레거시 클라이언트 연결 거부</string>

         <string id="Enable">사용</string>

         <string id="Disable">사용 안 함</string>

         <string id="Enforce">강제 적용</string>

         <string id="Allow_smartcard_local_access">애플리케이션이 로컬 스마트 카드 판독기에 액세스하도록 허용</string>

         <string id="Allow_smartcard_local_access_Desc">사용하도록 설정되면 애플리케이션은 스마트 카드 리디렉션 기능이 설치된 경우에도 모든 '로컬' 스마트 카드 판독기에 액세스할 수 있습니다.

이 설정은 원격 데스크톱 서비스 역할이 사용되도록 설정될 경우 RDP 또는 RDSH 호스트에 적용되지 않습니다.

사용하도록 설정되면 데스크톱에 로컬 판독기가 있는지 모니터링되고, 로컬 판독기가 감지되면 스마트 카드 리디렉션이 로컬 판독기에 대한 액세스 허용을 해제합니다. 리디렉션은 다음 번에 사용자가 세션에 연결할 때까지 해제된 상태로 남아 있습니다.

참고: 로컬 액세스가 사용되도록 설정되면 애플리케이션은 더 이상 클라이언트에 있는 원격 판독기에 액세스할 수 없습니다.

이 설정은 기본적으로 사용하지 않도록 설정됩니다.</string>

         <string id="Local_Reader_Name">로컬 판독기 이름</string>

         <string id="Local_Reader_Name_Desc">로컬 액세스를 사용하도록 설정하기 위해 모니터링할 로컬 판독기의 이름을 지정합니다. 기본적으로 로컬 액세스를 사용하도록 설정하려면 판독기에 카드가 삽입되어 있어야 합니다. '삽입된 스마트 카드 필요' 설정을 사용하여 이러한 요구 사항을 사용하지 않도록 설정할 수 있습니다.


기본값은 모든 판독기에 대해 이 기능이 사용되도록 설정되는 것입니다.</string>

         <string id="Require_an_inserted_smart_card">삽입된 스마트 카드 필요</string>

         <string id="Require_an_inserted_smart_card_Desc">사용하도록 설정되면 로컬 판독기에 카드가 삽입된 경우에만 로컬 판독기 액세스가 사용되도록 설정됩니다. 사용하지 않도록 설정되면 로컬 판독기가 감지되기만 하면 로컬 액세스가 사용되도록 설정됩니다.

이 설정은 기본적으로 사용하도록 설정됩니다.</string>

         <string id="Disable_true_SSO">True SSO 사용 안 함</string>

         <string id="Disable_true_SSO_Desc">이 옵션이 사용되도록 설정되면 에이전트에서 해당 기능이 사용되지 않도록 설정됩니다.</string>

         <string id="Cert_wait_timeout">인증서 대기 시간 초과</string>

         <string id="Cert_wait_timeout_Desc">인증서가 에이전트에 도착할 때까지의 시간 초과 기간을 초 단위로 지정합니다.</string>

         <string id="Min_key_size">최소 키 크기</string>

         <string id="Min_key_size_Desc">사용되는 최소 크기의 키</string>

         <string id="All_key_sizes">모든 키 크기</string>

         <string id="All_key_sizes_Desc">사용할 수 있는 모든 키의 크기입니다. 최대 5개 크기를 지정할 수 있습니다. 예: 1024,2048,3072,4096</string>

         <string id="Keys_to_precreate">미리 생성할 키의 수</string>

         <string id="Keys_to_precreate_Desc">RDSH 환경에서 미리 생성할 키의 수</string>

         <string id="Cert_min_validity">인증서에 필요한 최소 유효 기간</string>

         <string id="Cert_min_validity_Desc">사용자 다시 연결에 사용되는 인증서에 필요한 최소 유효 기간(분)입니다.</string>

         <string id="Enable_Unity_Touch">Unity Touch 사용</string>

         <string id="Enable_Unity_Touch_Desc">이 정책은 Unity Touch 기능이 Horizon Agent에서 사용되도록 설정되는지 여부를 지정합니다. 이 설정의 기본값은 Unity Touch가 사용되도록 설정되는 것입니다.

Windows 10에서 Unity Touch가 사용되도록 설정되면 하위 정책은 Horizon Agent에서 Unity Touch에 대한 UWP(범용 Windows 플랫폼) 애플리케이션 지원이 사용되도록 설정되어 있는지를 지정합니다. Unity Touch에서 UWP 지원은 기본적으로 사용되도록 설정됩니다. Unity Touch 정책이 구성되지 않은 경우 Unity Touch에 대한 UWP 지원이 Windows 10에서 사용되도록 설정됩니다.</string>

         <string id="Enable_system_tray_redir">호스팅된 애플리케이션을 위한 시스템 트레이 리디렉션 사용</string>

         <string id="Enable_system_tray_redir_Desc">이 정책은 호스팅된 애플리케이션을 원격으로 사용할 때 시스템 트레이 리디렉션을 사용하도록 설정할지 여부를 지정합니다. 이 설정의 기본값은 시스템 트레이 리디렉션이 사용되도록 설정되는 것입니다.</string>

         <string id="Enable_user_prof_customization">호스팅된 애플리케이션에 대한 사용자 프로파일 사용자 지정 사용</string>

         <string id="Enable_user_prof_customization_Desc">이 정책은 호스팅된 애플리케이션을 원격으로 사용할 때 사용자 프로파일 사용자 지정을 실행할지 여부를 지정합니다. 이 경우 사용자 프로파일이 생성되고, Windows 테마가 사용자 지정되고, 등록된 시작 애플리케이션이 실행됩니다. 기본값은 사용 안 함입니다.</string>

         <string id="AllowTinyOrOffscreenWindows">비어 있거나 화면을 벗어난 창에 대한 업데이트 전송</string>

         <string id="AllowTinyOrOffscreenWindows_Desc">이 정책은 Horizon Client가 비어 있거나 화면을 벗어난 창에 대한 업데이트를 수신할지 여부를 지정합니다. 이 값이 사용되지 않도록 설정되면 2x2픽셀보다 작거나 화면을 완전히 벗어난 창에 대한 정보가 Horizon Client로 전송되지 않습니다. 기본적으로는 사용되지 않도록 설정됩니다.</string>

         <string id="MinimalHookingModeEnabled">Windows 후크의 사용량 제한</string>

         <string id="MinimalHookingModeEnabled_Desc">이 정책은 호스팅된 애플리케이션을 원격으로 사용할 때나 Unity Touch를 사용할 때 대부분의 후크를 해제합니다. 이 정책은 OS 수준 후크가 설정되어 있을 때 호환성 또는 성능 문제가 있는 애플리케이션에 사용하기 위한 것입니다. 예를 들어 이 설정은 대부분의 Windows 활성 보조 기술 및 in-process 후크의 사용을 사용하지 않도록 설정합니다. 이 정책은 기본적으로 사용하지 않도록 설정됩니다. 즉, 기본적으로 모든 기본 후크를 사용합니다.</string>

         <string id="LaunchAppWhenArgsAreDifferent">인수가 다른 경우에만 호스팅된 애플리케이션의 새 인스턴스 시작</string>

         <string id="LaunchAppWhenArgsAreDifferent_Desc">이 정책은 애플리케이션의 기존 인스턴스가 연결이 끊어진 프로토콜 세션 내부에서 이미 실행되고 있을 때 호스팅된 애플리케이션이 시작되는 경우의 동작을 제어합니다. 사용하지 않도록 설정하면 애플리케이션의 기존 인스턴스가 활성화됩니다. 사용하도록 설명하면 명령줄 매개 변수가 일치하는 경우에만 애플리케이션의 기존 인스턴스가 활성화됩니다. 이 정책의 기본값은 사용 안 함입니다.</string>

         <string id="Exclude_Vid_Pid">Vid/Pid 디바이스 제외</string>

         <string id="Exclude_Vid_Pid_Desc">지정된 벤더 ID 및 제품 ID를 갖는 디바이스를 전달하지 않도록 제외합니다.

구문: {m|o}:&lt;vid-&lt;xxxx&gt;_pid-&lt;xxxx|*&gt;&gt;[;...]
merge-flag:
m=클라이언트 설정이 에이전트 설정과 병합됨
o=에이전트 설정이 클라이언트 설정을 재정의함

예: m:vid-0781_pid-554c;vid-0781_pid-9999</string>

         <string id="Exclude_Vid_Pid_Rel">Vid/Pid/Rel 디바이스 제외</string>

         <string id="Exclude_Vid_Pid_Rel_Desc">지정된 벤더 ID, 제품 ID 및 릴리스 번호를 갖는 디바이스를 전달하지 않도록 제외합니다.

구문: {m|o}:&lt;vid-&lt;xxxx&gt;_pid-&lt;xxxx&gt;_rel-&lt;xxxx|*&gt;&gt;[;...]
merge-flag:
m=클라이언트 설정이 에이전트 설정과 병합됨
o=에이전트 설정이 클라이언트 설정을 재정의함

예: m:vid-0781_pid-554c_rel-0100;vid-0781_pid-9999_rel-0010</string>

         <string id="Include_Vid_Pid">Vid/Pid 디바이스 포함</string>

         <string id="Include_Vid_Pid_Desc">지정된 벤더 ID 및 제품 ID를 갖는 디바이스를 전달할 수 있도록 포함합니다.

구문: {m|o}:&lt;vid-&lt;xxxx&gt;_pid-&lt;xxxx|*&gt;&gt;[;...]

merge-flag:
m=클라이언트 설정이 에이전트 설정과 병합됨
o=에이전트 설정이 클라이언트 설정을 재정의함

예: o:vid-0781_pid-554c;vid-0781_pid-9999</string>

         <string id="Include_Vid_Pid_Rel">Vid/Pid/Rel 디바이스 포함</string>

         <string id="Include_Vid_Pid_Rel_Desc">지정된 벤더 ID, 제품 ID 및 릴리스 번호를 갖는 디바이스를 전달할 수 있도록 포함합니다.

구문: {m|o}:&lt;vid-&lt;xxxx&gt;_pid-&lt;xxxx&gt;_rel-&lt;xxxx|*&gt;&gt;[;...]

merge-flag:
m=클라이언트 설정이 에이전트 설정과 병합됨
o=에이전트 설정이 클라이언트 설정을 재정의함

예: o:vid-0781_pid-554c_rel-0100;vid-0781_pid-9999_rel-0010</string>

         <string id="Exclude_device_family">디바이스 제품군 제외</string>

         <string id="Exclude_device_family_Desc">디바이스 제품군을 전달하지 않도록 제외합니다.

구문: {m|o}:&lt;family-name&gt;[;...]

merge-flag:
m=클라이언트 설정이 에이전트 설정과 병합됨
o=에이전트 설정이 클라이언트 설정을 재정의함

예: o:bluetooth;audio-in</string>

         <string id="Include_device_family">디바이스 제품군 포함</string>

         <string id="Include_device_family_Desc">디바이스 제품군을 전달할 수 있도록 포함합니다.

구문: {m|o}:&lt;family-name&gt;[;...]

merge-flag:
m=클라이언트 설정이 에이전트 설정과 병합됨
o=에이전트 설정이 클라이언트 설정을 재정의함

예: m:storage;audio-out</string>

         <string id="Exclude_all">모든 디바이스 제외</string>

         <string id="Exclude_all_Desc">포함 필터 규칙을 통해 포함되지 않는 한 모든 디바이스를 차단합니다.

기본값: 모든 디바이스 허용</string>

         <string id="HidOpt_Include_Vid_Pid">HID 최적화 Vid/Pid 디바이스 포함</string>

         <string id="HidOpt_Include_Vid_Pid_Desc">지정된 벤더 ID 및 제품 ID를 갖는 HID 디바이스를 최적화할 수 있도록 포함합니다.

구문: &lt;vid-&lt;xxxx&gt;_pid-&lt;xxxx|*&gt;&gt;[;...]

예: vid-056a_pid-0302;vid-046d_pid-c628</string>

         <string id="Exclude_Auto_Vid_Pid">자동 연결 Vid/Pid 디바이스 제외</string>

         <string id="Exclude_Auto_Vid_Pid_Desc">지정된 벤더 ID 및 제품 ID를 갖는 디바이스를 자동으로 전달하지 않도록 제외합니다.

구문: {m|o}:&lt;vid-&lt;xxxx&gt;_pid-&lt;xxxx|*&gt;&gt;[;...]
merge-flag:
m=클라이언트 설정이 에이전트 설정과 병합됨
o=에이전트 설정이 클라이언트 설정을 재정의함

예: m:vid-0781_pid-554c;vid-0781_pid-9999</string>

         <string id="Exclude_auto_device_family">자동 연결 디바이스 제품군 제외</string>

         <string id="Exclude_auto_device_family_Desc">디바이스 제품군을 자동으로 전달하지 않도록 제외합니다.

구문: {m|o}:&lt;family-name&gt;[;...]

merge-flag:
m=클라이언트 설정이 에이전트 설정과 병합됨
o=에이전트 설정이 클라이언트 설정을 재정의함

예: o:storage;hid</string>

         <string id="Exclude_Vid_Pid_from_Split">분할에서 Vid/Pid 디바이스 제외</string>

         <string id="Exclude_Vid_Pid_from_Split_Desc">해당 벤더 ID 및 제품 ID로 지정된 복합 디바이스의 구성 요소 디바이스가 필터링을 위해 별도 디바이스로 처리되지 않도록 제외합니다.

구문: {m|o}:&lt;vid-&lt;xxxx&gt;_pid-&lt;xxxx|*&gt;&gt;[;...]
merge-flag:
m=클라이언트 설정이 에이전트 설정과 병합됨
o=에이전트 설정이 클라이언트 설정을 재정의함

예: o:vid-0781_pid-554c;vid-0781_pid-9999</string>

         <string id="Split_Vid_Pid_Device">Vid/Pid 디바이스 분할</string>

         <string id="Split_Vid_Pid_Device_Desc">해당 벤더 및 제품 ID로 지정된 복합 디바이스의 구성 요소 디바이스를 필터링을 위해 별도 디바이스로 처리합니다.

구문: {m|o}:&lt;vid-&lt;xxxx&gt;_pid-&lt;xxxx|*&gt;(exintf:xx[;exintf:xx])&gt;[;...]
merge-flag:
m=클라이언트 설정이 에이전트 설정과 병합됨
o=에이전트 설정이 클라이언트 설정을 재정의함

예: o:vid-0781_pid-554c(exintf:01;exintf:02);vid-0781_pid-9999(exintf:02)</string>

         <string id="Allow_other_input_devices">기타 입력 디바이스 허용</string>

         <string id="Allow_other_input_devices_Desc">HID 부팅 가능 디바이스, 키보드 및 마우스 디바이스가 아닌 입력 디바이스를 전달하도록 허용합니다.

기본값: 전달 허용</string>

         <string id="Allow_Default">허용 - 기본 클라이언트 설정</string>

         <string id="Allow_Override">허용 - 클라이언트 설정 재정의</string>

         <string id="Disable_Default">사용 안 함 - 기본 클라이언트 설정</string>

         <string id="Disable_Override">사용 안 함 - 클라이언트 설정 재정의</string>

         <string id="Allow_HID_Bootable">HID 부팅 가능 허용</string>

         <string id="Allow_HID_Bootable_Desc">부팅 가능 입력 디바이스(HID 부팅 가능 디바이스라고도 함)를 전달하도록 허용합니다.

기본값: 전달 허용</string>

         <string id="Allow_Audio_Input_devices">오디오 입력 디바이스 허용</string>

         <string id="Allow_Audio_Input_devices_Desc">오디오 입력 디바이스를 전달하도록 허용합니다.

기본값: 전달 허용</string>

         <string id="Allow_Audio_Output_devices">오디오 출력 디바이스 허용</string>

         <string id="Allow_Audio_Output_devices_Desc">오디오 출력 디바이스를 전달하도록 허용합니다.

기본값: 전달 차단</string>

         <string id="Allow_keyboard_mouse">키보드 및 마우스 디바이스 허용</string>

         <string id="Allow_keyboard_mouse_Desc">키보드 및 마우스 디바이스를 전달하도록 허용합니다.

기본값: 전달 차단</string>

         <string id="Allow_Video_Devices">비디오 디바이스 허용</string>

         <string id="Allow_Video_Devices_Desc">비디오 디바이스를 전달하도록 허용합니다.

기본값: 전달 허용</string>

         <string id="Allow_Smart_Cards">스마트 카드 허용</string>

         <string id="Allow_Smart_Cards_Desc">스마트 카드 디바이스를 전달하도록 허용합니다.

기본값: 전달 차단</string>

         <string id="Allow_Auto_Device_Splitting">자동 디바이스 분할 허용</string>

         <string id="Allow_Auto_Device_Splitting_Desc">별도의 디바이스로 자동 처리되지 않도록 모든 복합 디바이스의 구성 요소 디바이스를 제외합니다.</string>

         <string id="Proxy_default_ie_autodetect">기본 자동 감지 프록시</string>

         <string id="Proxy_default_ie_autodetect_Desc">기본 IE 연결 설정입니다. 인터넷 속성, LAN 설정에서 자동으로 설정 검색을 켭니다.</string>

         <string id="Default_proxy_server">기본 프록시 서버</string>

         <string id="Default_proxy_server_Desc">프록시 서버에 대한 기본 IE 연결 설정입니다. 인터넷 속성, LAN 설정에서 사용해야 할 프록시 서버를 지정합니다.</string>

         <string id="Update_Java_Proxy">Java 애플릿에 대한 프록시 설정</string>

         <string id="Update_Java_Proxy_Desc">브라우저 설정을 우회하여 직접 연결하도록 Java 프록시를 설정합니다. 클라이언트 IP 투명성을 사용하여 Java 애플릿에 대한 네트워크를 리디렉션하도록 Java 프록시를 설정합니다. Java 프록시 설정을 원래 설정으로 복원하려면 기본값을 설정합니다.</string>

         <string id="Use_Client_IP">Java 프록시에 대해 클라이언트 IP 투명성 사용</string>

         <string id="Use_Direct_Connect">Java 프록시에 대해 직접 연결 사용</string>

         <string id="Use_Default">Java 프록시에 대해 기본값 사용</string>

         <string id="Enable_white_list">허용 목록 사용</string>

         <string id="Enable_black_list">거부 목록 사용</string>

         <string id="Horizon_HTML5_FEATURES">Horizon HTML5 기능</string>

         <string id="Enable_HTML5_FEATURES">Horizon HTML5 기능 사용</string>

         <string id="Enable_HTML5_FEATURES_Desc">Horizon HTML5 기능을 사용하도록 설정합니다. 이 정책을 &quot;사용&quot;으로 설정하면 Horizon HTML5 멀티미디어 리디렉션, 지리적 위치 리디렉션, 브라우저 리디렉션 또는 Microsoft Teams에 대한 미디어 최적화를 사용할 수 있습니다.  이 정책을 &quot;사용 안 함&quot;으로 설정하면 Horizon HTML5 기능을 사용할 수 없습니다. 이 설정은 다음 로그온 시 적용됩니다.</string>

         <string id="HTML5FEATURES_Disable_AudoDetect_Intranet">인트라넷 자동 감지 사용 안 함</string>

         <string id="HTML5FEATURES_Disable_AudoDetect_Intranet_Desc">이 정책이 &quot;사용&quot;이면 다음 로그온 동안, 인트라넷 설정 &quot;다른 영역에 나열되지 않은 모든 로컬(인트라넷) 사이트 포함&quot; 및 &quot;프록시 서버를 우회하는 모든 사이트 포함&quot;이 사용하지 않도록 설정됩니다. 이 정책이 &quot;사용 안 함&quot;이면 IE 로컬 인트라넷 영역이 변경되지 않습니다.

참고: (1) Edge 브라우저가 Horizon HTML5 멀티미디어 리디렉션을 사용하도록 설정되어 있거나 (2) 지리적 위치 리디렉션이 사용되도록 설정된 경우 이 정책을 &quot;사용&quot;으로 설정해야 합니다.</string>

         <string id="Horizon_HTML5MMR">Horizon HTML5 멀티미디어 리디렉션</string>

         <string id="Enable_HTML5_MMR">Horizon HTML5 멀티미디어 리디렉션 사용</string>

         <string id="Enable_HTML5_MMR_Desc">Horizon HTML5 멀티미디어 리디렉션을 사용하도록 설정합니다. 이 설정은 다음 로그온 시 적용됩니다.</string>

         <string id="HTML5MMRUrlList">Horizon HTML5 멀티미디어 리디렉션에 대한 URL 목록을 사용하도록 설정합니다.</string>

         <string id="HTML5MMRUrlBlockList">Horizon HTML5 멀티미디어 리디렉션에 대한 제외 URL 목록입니다.</string>

         <string id="HTML5MMRUrlList_Desc">Horizon HTML5 멀티미디어 리디렉션을 사용하도록 설정할 URL 목록을 지정합니다. &quot;값 이름&quot; 열에 url 패턴(예: &quot;https://www.youtube.com/*&quot;)을 지정합니다. &quot;값&quot; 열은 나중에 사용되도록 예약되어 있으므로 비어 두는 것이 좋습니다.</string>

         <string id="HTML5MMRUrlBlockList_Desc">Horizon HTML5 멀티미디어 리디렉션에서 제외될 URL 목록을 지정합니다. &quot;값 이름&quot; 열에 url 패턴(예: &quot;https://www.youtube.com/*&quot;)을 지정합니다. &quot;값&quot; 열은 나중에 사용하도록 예약되어 있으며 비워 두어야 합니다.</string>

         <string id="HTML5MMR_Enable_Chrome">Horizon HTML5 멀티미디어 리디렉션에 대해 Chrome 브라우저 사용</string>

         <string id="HTML5MMR_Enable_Chrome_Desc">이 정책은 Horizon HTML5 멀티미디어 리디렉션이 &quot;사용&quot;인 경우에만 사용됩니다. 구성되지 않으면 기본적으로 &quot;Horizon HTML5 멀티미디어 리디렉션&quot;이 사용으로 설정되는 것과 같은 결과가 적용됩니다.</string>

         <string id="HTML5MMR_Enable_Edge">Horizon HTML5 멀티미디어 리디렉션에 대해 레거시 버전의 Microsoft Edge 브라우저 사용</string>

         <string id="HTML5MMR_Enable_Edge_Desc">이 정책은 Horizon HTML5 멀티미디어 리디렉션이 &quot;사용&quot;인 경우에만 사용됩니다. 구성되지 않으면 기본적으로 &quot;Horizon HTML5 멀티미디어 리디렉션&quot;이 사용으로 설정되는 것과 같은 결과가 적용됩니다. </string>

         <string id="HTML5MMR_Enable_Edge_Chromium">Horizon HTML5 멀티미디어 리디렉션에 대해 Microsoft Edge(Chromium) 브라우저 사용</string>

         <string id="HTML5MMR_Enable_Edge_Chromium_Desc">이 정책은 Horizon HTML5 멀티미디어 리디렉션이 &quot;사용&quot;인 경우에만 사용됩니다. 구성되지 않으면 기본적으로 &quot;Horizon HTML5 멀티미디어 리디렉션&quot;이 사용으로 설정되는 것과 같은 결과가 적용됩니다. </string>

         <string id="HTML5MMR_Auto_Adjust_Visual_Effect">Windows 시각적 효과를 자동으로 조정</string>

         <string id="HTML5MMR_Auto_Adjust_Visual_Effect_Desc">이 정책은 Horizon HTML5 멀티미디어 리디렉션에 대한 창의 시각적 효과를 자동으로 조정하는 데 사용됩니다. 구성되지 않거나 사용하지 않도록 설정하면 창의 시각적 효과가 자동으로 조정되지 않습니다.</string>

         <string id="Horizon_GEO_REDIR">Horizon 지리적 위치 리디렉션</string>

         <string id="Enable_GEO_REDIR">Horizon 지리적 위치 리디렉션 사용</string>

         <string id="Enable_GEO_REDIR_Desc">Horizon 지리적 위치 리디렉션 기능을 사용하도록 설정합니다. 이 설정은 다음 로그온 시 적용됩니다.</string>

         <string id="Enable_GEO_REDIR_For_Chrome">Chrome Browser에 대해 Horizon 지리적 위치 리디렉션 사용</string>

         <string id="Enable_GEO_REDIR_For_Chrome_Desc">Chrome Browser에 대해 Horizon 지리적 위치 리디렉션 기능을 사용하도록 설정합니다. 이 설정은 다음 로그온 시 적용됩니다.</string>

         <string id="Enable_GEO_REDIR_For_Edge_Chromium">Microsoft Edge(Chromium) 브라우저에 대해 Horizon 지리적 위치 리디렉션 사용</string>

         <string id="Enable_GEO_REDIR_For_Edge_Chromium_Desc">Microsoft Edge(Chromium) 브라우저에 대해 Horizon 지리적 위치 리디렉션 기능을 사용하도록 설정합니다. 이 설정은 다음 로그온 시 적용됩니다.</string>

         <string id="GeoRedirUrlList">Horizon 지리적 위치 리디렉션에 대한 URL 목록을 사용하도록 설정합니다.</string>

         <string id="GeoRedirUrlList_Desc">지리적 위치 리디렉션 기능을 사용하도록 설정할 url 목록을 지정합니다. &quot;값 이름&quot; 열에 url 패턴(예: &quot;https://www.youtube.com/*&quot;)을 지정합니다. &quot;값&quot; 열은 나중에 사용되도록 예약되어 있으므로 비어 두는 것이 좋습니다. 이 URL 목록은 (1) 모든 RDSH 및 VDI 환경의 Google Chrome 및 Microsoft Edge(Chromium) 브라우저용 Horizon 지리적 위치 리디렉션 확장 및 (2) RDSH 및 Windows 7 VDI 환경의 Internet Explorer용 Horizon 지리적 위치 리디렉션 플러그인에 사용됩니다.</string>

         <string id="GeoRedirDistanceDelta">위치 업데이트를 보고할 최소 거리 설정</string>

         <string id="GeoRedirDistanceDelta_Desc">새 위치을 에이전트에 보고하기 위해, 에이전트에 보고된 마지막 업데이트와 클라이언트의 위치 업데이트 간의 최소 거리를 지정합니다. 기본적으로 사용되는 최소 거리는 75미터입니다.</string>

         <string id="Horizon_BROWSER_REDIR">Horizon 브라우저 리디렉션</string>

         <string id="Enable_BROWSER_REDIR">Horizon 브라우저 리디렉션 사용</string>

         <string id="Enable_BROWSER_REDIR_Desc">Horizon 브라우저 리디렉션 기능을 사용하도록 설정합니다. 이 설정은 다음 로그온 시 적용됩니다. Horizon 브라우저 리디렉션을 사용하도록 설정하면 Horizon 고급 브라우저 리디렉션도 사용하도록 설정됩니다.</string>

         <string id="Enable_BROWSER_REDIR_For_Chrome">Chrome Browser에 대해 Horizon 브라우저 리디렉션 사용</string>

         <string id="Enable_BROWSER_REDIR_For_Chrome_Desc">Chrome Browser에 대해 Horizon 브라우저 리디렉션 기능을 사용하도록 설정합니다. 이 설정은 다음 로그온 시 적용됩니다.</string>

         <string id="Enable_BROWSER_REDIR_For_Edge_Chromium">Microsoft Edge(Chromium) 브라우저에 대해 Horizon 브라우저 리디렉션 기능 사용</string>

         <string id="Enable_BROWSER_REDIR_For_Edge_Chromium_Desc">Microsoft Edge(Chromium) 브라우저에 대해 Horizon 브라우저 리디렉션 기능을 사용하도록 설정합니다. 이 설정은 다음 로그온 시 적용됩니다.</string>

         <string id="BrowserRedirFallbackWhitelistErr">허용 목록 위반 후 자동 폴백 사용</string>

         <string id="BrowserRedirFallbackWhitelistErr_Desc">사용자 지정 주소 표시줄에 입력하거나, 브라우저의 주소 표시줄에 입력하거나, 리디렉션된 탭 내에서 이동하여 브라우저 리디렉션을 통해 리디렉션된 탭에서 URL로 이동하고, 새 URL이 브라우저 리디렉션 또는 고급 브라우저 리디렉션 URL 목록에 나열되지 않으면 이 설정을 사용하도록 설정한 후에 새 URL이 에이전트에 대한 로드로 자동 폴백됩니다. 이때 새 URL이 고급 브라우저 리디렉션 URL 목록에도 있는 경우 고급 브라우저 리디렉션을 사용하여 리디렉션됩니다. [Horizon 브라우저 리디렉션에 대한 URL 목록 사용] 또는 [고급 Horizon 브라우저 리디렉션에 대한 URL 목록 사용] 아래에 설정되지 않은 URL을 탐색하려고 하면 이 설정과 관계없이 에이전트에서 가져오기 및 렌더링하기 위해 즉시 폴백됩니다.</string>

         <string id="BrowserRedirFetchFromServer">브라우저 리디렉션 기능에 대한 에이전트 측 가져오기 사용</string>

         <string id="BrowserRedirFetchFromServer_Desc">브라우저 리디렉션 기능을 사용할 때 클라이언트 대신 에이전트에서 웹 사이트 컨텐츠를 가져올 수 있도록 설정합니다. 이 설정은 기본적으로 사용하지 않도록 설정됩니다.</string>

         <string id="BrowserRedirShowErrPage">자동 폴백 전에 오류 정보가 포함된 페이지 표시</string>

         <string id="BrowserRedirShowErrPage_Desc">이 설정은 &quot;허용 목록 위반 후 자동 폴백 사용&quot;을 사용하도록 설정했으며 허용 목록 위반이 발생하는 경우에만 사용됩니다. 이 경우 이 설정을 사용하도록 설정하면 5초 카운트다운 후 페이지가 표시되며, 그 후 탭이 에이전트에서 위반을 일으킨 URL을 가져와 렌더링하기 위해 자동으로 폴백됩니다. 이 설정을 사용하지 않도록 설정하면 사용자에게 5초 카운트다운 주의가 표시되지 않고 탭이 에이전트 측 렌더링을 위해 즉시 폴백됩니다.</string>

         <string id="BrowserRedirUrlList">Horizon 브라우저 리디렉션에 대한 URL 목록 사용</string>

         <string id="BrowserRedirUrlList_Desc">브라우저 리디렉션 기능에 대한 모든 URL을 지정합니다. 이러한 URL은 Chrome의 주소 표시줄이나 사용자 지정 주소 표시줄에 입력하여 방문할 수 있습니다. 이러한 URL은 목록의 다른 URL부터 또는 에이전트 측의 렌더링된 페이지부터 탐색하여 방문할 수도 있습니다. &quot;값 이름&quot; 열에 url 패턴(예: &quot;https://www.youtube.com/*&quot;)을 지정합니다. &quot;값&quot; 열은 나중에 사용되도록 예약되어 있으므로 비어 두는 것이 좋습니다. URL이 브라우저 리디렉션 및 고급 브라우저 리디렉션 URL 목록의 패턴과 일치하면 향상된 브라우저 리디렉션이 우선합니다.</string>

         <string id="EnhBrowserRedirUrlList">고급 Horizon 브라우저 리디렉션에 대한 URL 목록 사용</string>

         <string id="EnhBrowserRedirUrlList_Desc">고급 브라우저 리디렉션 기능에 대한 모든 URL을 지정합니다. 이러한 URL은 Chrome의 주소 표시줄에 입력하거나, 목록의 다른 URL 또는 에이전트 측 렌더링된 페이지에서 해당 위치로 이동하여 방문할 수 있습니다. &quot;값 이름&quot; 열에 url 패턴(예: &quot;https://www.youtube.com/*&quot;)을 지정합니다. &quot;값&quot; 열은 나중에 사용되도록 예약되어 있으므로 비어 두는 것이 좋습니다. URL이 브라우저 리디렉션 및 고급 브라우저 리디렉션 URL 목록의 패턴과 일치하면 향상된 브라우저 리디렉션이 우선합니다.</string>

         <string id="BrowserRedirNavUrlList">Horizon 브라우저 리디렉션에 대한 탐색 URL 목록 사용</string>

         <string id="BrowserRedirNavUrlList_Desc">사용자 지정 주소 표시줄에 직접 입력하거나 다른 목록의 URL부터 탐색을 시작하여 사용자가 탐색할 수 있는 URL을 지정합니다. 이러한 URL은 Chrome의 주소 표시줄에 직접 입력하거나 에이전트 측의 렌더링된 페이지부터 탐색하여 방문할 수 없습니다. &quot;값 이름&quot; 열에 url 패턴(예: &quot;https://www.youtube.com/*&quot;)을 지정합니다. &quot;값&quot; 열은 나중에 사용되도록 예약되어 있으므로 비어 두는 것이 좋습니다.</string>

         <string id="Horizon_WebRTC_REDIR_FEATURES">Horizon WebRTC 리디렉션 기능</string>

         <string id="Enable_Teams_Redir">Microsoft Teams에 대한 미디어 최적화 사용</string>

         <string id="Enable_Teams_Redir_Desc">이 설정은 Microsoft Teams 최적화를 사용하거나 사용하지 않도록 설정하는 데 사용합니다.

Horizon Agent가 설치되면 에이전트에 teamsEnabled 레지스트리 키가 생성되어 Microsoft Teams 최적화를 사용하도록 설정됩니다.  기본적으로 사용자는 Horizon Client에서 [WebRTC 기반 애플리케이션에 대한 미디어 최적화] 설정을 구성하여 Microsoft Teams 최적화를 사용하거나 사용하지 않을 수 있습니다.

이 정책이 [사용]인 경우 Microsoft Teams 최적화를 사용하도록 설정됩니다. Microsoft Teams 최적화를 사용하도록 설정하고 [클라이언트 측 WebRTC 최적화 강제 적용]을 선택하면 Teams 미디어 최적화가 끝점에서 강제로 실행되며, 모든 클라이언트 설정 또는 관리자 정책(예: Chrome Client용 Chrome 수준 사용자 정책)이 무시됩니다. Microsoft Teams 최적화를 사용하도록 설정하고 [클라이언트 측 WebRTC 최적화 강제 적용]을 선택 취소하면 사용자는 Horizon Client [WebRTC 기반 애플리케이션에 대한 미디어 최적화] 설정을 구성하여 Microsoft Teams 최적화를 사용하거나 사용하지 않을 수 있습니다.

이 정책에 대해 [사용 안 함]을 선택하면 Microsoft Teams 최적화를 사용하지 않도록 설정되고 사용할 수 없게 됩니다. Horizon Client [WebRTC 기반 애플리케이션에 대한 미디어 최적화] 설정은 아무 영향도 미치지 않습니다.

기본적으로 이 정책은 [구성되지 않음]이지만 정책이 변경된 후 [구성되지 않음]으로 다시 변경되면 teamsEnabled 레지스트리 키가 제거되고 Microsoft Teams 최적화를 사용하지 않습니다.

이 설정은 다음 로그온 시 적용됩니다.</string>

         <string id="Enable_Electron_App_Redir">일반 Electron 애플리케이션에 대한 미디어 최적화를 사용하도록 설정</string>

         <string id="Enable_Electron_App_Redir_Desc">이 설정은 Electron 애플리케이션 최적화를 사용하거나 사용하지 않도록 설정하는 데 사용합니다.

[사용] 또는 [구성되지 않음]인 경우 Electron 애플리케이션 최적화를 사용하도록 설정됩니다. 또한 최종 사용자가 최적화를 강제로 사용하도록 하려면(끝점에서 지원되는 경우) [사용]을 선택하고 [클라이언트 측 WebRTC 최적화 강제 적용]을 선택합니다. [구성되지 않음]을 선택하면 클라이언트 설정(사용 가능한 경우)이 적용됩니다.
세부 정보:
사용하도록 설정하고 [클라이언트 측 WebRTC 최적화 강제 적용]을 선택 취소하면 사용자는 Horizon Client [WebRTC 기반 애플리케이션에 대한 미디어 최적화] 설정을 구성하여 Electron 애플리케이션 최적화를 사용하거나 사용하지 않을 수 있습니다. 이 옵션을 선택하면 끝점에서 Electron 애플리케이션 미디어 최적화가 강제로 적용되거나, 클라이언트 설정이나 다른 관리자 정책(예: Chrome 클라이언트용 Chrome 수준 사용자 정책)이 무시됩니다.
기본적으로 Electron 애플리케이션 최적화 설정은 [구성되지 않음]으로, Electron 애플리케이션 최적화를 사용하도록 설정하고 사용자가 [WebRTC 기반 애플리케이션에 대한 미디어 최적화] 설정을 구성할 수 있도록 합니다.
[사용 안 함]을 선택하면 Electron 애플리케이션 최적화를 사용하지 않도록 설정되고 사용할 수 없게 됩니다. Horizon Client [WebRTC 기반 애플리케이션에 대한 미디어 최적화] 설정은 아무 영향도 미치지 않습니다.

이 설정은 다음 로그온 시 적용됩니다.</string>

         <string id="Horizon_WebRTC_SDK_WEBAPP">Horizon WebRTC 리디렉션 SDK Web 애플리케이션 지원</string>

         <string id="WebRTC_SDK_Enable_Web_App_Redir">Web 애플리케이션에 대한 미디어 최적화 사용</string>

         <string id="WebRTC_SDK_Enable_Web_App_Redir_Desc">이 설정은 Web 애플리케이션 최적화를 사용하거나 사용하지 않도록 설정하는 데 사용합니다. [사용]인 경우 Web 애플리케이션 최적화를 사용하도록 설정됩니다.</string>

         <string id="WebRTC_SDK_Enable_Web_App_Redir_Chrome">Horizon WebRTC 리디렉션 SDK Web 애플리케이션 지원에 대해 Chrome 브라우저 사용</string>

         <string id="WebRTC_SDK_Enable_Web_App_Redir_Chrome_Desc">이 정책은 Horizon WebRTC 리디렉션 SDK Web 애플리케이션 지원이 &quot;사용&quot;인 경우에만 사용됩니다. 구성되지 않으면 기본적으로 &quot;Web 애플리케이션에 대한 미디어 최적화 사용&quot;이 사용으로 설정되는 것과 같은 결과가 적용됩니다.</string>

         <string id="WebRTC_SDK_Enable_Web_App_Redir_Edge">Horizon WebRTC 리디렉션 SDK Web 애플리케이션 지원에 대해 Chromium Edge 브라우저 사용</string>

         <string id="WebRTC_SDK_Enable_Web_App_Redir_Edge_Desc">이 정책은 Horizon WebRTC 리디렉션 SDK Web 애플리케이션 지원이 &quot;사용&quot;인 경우에만 사용됩니다. 구성되지 않으면 기본적으로 &quot;Web 애플리케이션에 대한 미디어 최적화 사용&quot;이 사용으로 설정되는 것과 같은 결과가 적용됩니다.</string>

         <string id="WebRTC_SDK_Web_App_Redir_Allow_List">Horizon WebRTC 리디렉션 SDK Web 애플리케이션 지원에 대해 URL 목록 사용</string>

         <string id="WebRTC_SDK_Web_App_Redir_Allow_List_Desc">Horizon WebRTC 리디렉션 SDK Web 애플리케이션 지원에 대한 모든 URL을 지정합니다 이러한 URL은 Chrome의 주소 표시줄에 입력하여 방문할 수 있습니다. 이러한 URL은 목록의 다른 URL부터 또는 에이전트 측의 렌더링된 페이지부터 탐색하여 방문할 수도 있습니다. &quot;값 이름&quot; 열에 url 패턴(예: &quot;https://www.youtube.com/*&quot;)을 지정합니다. &quot;값&quot; 열은 나중에 사용되도록 예약되어 있으므로 비어 두는 것이 좋습니다.</string>

         <string id="Enable_AEC_Teams_Redir">Microsoft Teams에 대한 미디어 최적화를 위해 소프트웨어 어쿠스틱 에코 제거 사용</string>

         <string id="Enable_AEC_Teams_Redir_Desc">이 설정은 Microsoft Teams에 대한 미디어 최적화를 위해 소프트웨어 AEC(어쿠스틱 에코 제거)를 구성하는 데 사용됩니다.

[사용]이면 소프트웨어에서 AEC를 사용하도록 설정됩니다. 최적의 오디오 품질 및 성능을 위해서는 [권장 AEC 알고리즘 사용]을 선택하십시오. CPU 사용량은 적지만 오디오 품질이 저하되는 AEC 알고리즘을 사용하려면 [권장 AEC 알고리즘 사용]의 선택을 취소합니다. 이 옵션은 부동 소수점 성능이 낮은 로우 엔드 프로세서에 유용합니다. 권장되는 AEC 알고리즘을 사용하는 것이 좋으며 대부분의 경우 적합합니다.

[사용 안 함]이면 AEC가 소프트웨어에서 사용되지 않도록 설정되고 더 이상 사용되지 않습니다.

[구성되지 않음]이면 권장 알고리즘을 사용하여 소프트웨어에서 AEC를 사용하도록 설정됩니다. Windows 클라이언트를 사용하는 경우 하드웨어 AEC를 사용할 수 없는 경우 소프트웨어 AEC가 사용됩니다. 하드웨어 AEC를 사용할 수 있는 경우(예: 헤드셋에 기본 제공 AEC가 있는 경우) 소프트웨어 AEC가 사용되지 않습니다. Windows 이외의 클라이언트를 사용하는 경우 하드웨어 AEC를 사용할 수 있는지와 관계없이 소프트웨어 AEC가 사용됩니다.</string>

         <string id="Enable_Datachannel_Teams_Redir">Microsoft Teams에 대한 미디어 최적화를 위해 데이터 채널 사용</string>
         <string id="Enable_Datachannel_Teams_Redir_Desc">해당 설정은 Microsoft Teams에 대한 미디어 최적화를 위해 데이터 채널을 사용하거나 사용하지 않도록 설정하는 데 사용됩니다.

[사용]으로 설정하면 데이터 채널이 Microsoft Teams에 대한 미디어 최적화를 위해 사용되며, 데이터 채널이 요구되는 기능도 사용할 수 있게 됩니다(예: 실시간 자막).

[사용 안 함]으로 설정하면 데이터 채널이 Microsoft Teams에 대한 미디어 최적화를 위해 사용되지 않으며, 데이터 채널이 요구되는 기능도 사용할 수 없게 됩니다.

[구성되지 않음]으로 설정하면 데이터 채널을 사용할 수 있게 됩니다.</string>

         <string id="Video_Cpu_Overuse_Threshold">CPU 과용 임계값 구성</string>

         <string id="Video_Cpu_Overuse_Threshold_Desc"> CPU 사용량이 임계값을 초과할 경우 전송된 비디오 해상도가 줄어들어 클라이언트 CPU 사용량이 낮아집니다. 기본 임계값은 85입니다. 영상 통화를 하는 동안 클라이언트 CPU 사용을 줄이려면 해당 정책을 [사용]으로 설정하고 값을 85 미만으로 설정하십시오. 기본 임계값을 85로 사용하려면 해당 정책을 [사용 안 함] 또는 [구성되지 않음]으로 설정하십시오. CPU 과용을 감지하지 않으려면 해당 정책을 [사용]으로 설정하고 값을 0으로 설정하십시오. 이 설정은 다음 로그온 시 적용됩니다.</string>

         <string id="Enable_Sharing_Client_Screen_InAppSession">Microsoft Teams 애플리케이션을 게시된 애플리케이션으로 사용하는 동안 클라이언트 데스크톱 화면 공유 허용</string>

         <string id="Enable_Sharing_Client_Screen_InAppSession_Desc">Microsoft Teams 애플리케이션을 게시된 애플리케이션으로 사용하는 경우 화면 공유 기능이 클라이언트 데스크톱 화면을 공유합니다. Microsoft Teams를 게시된 애플리케이션으로 사용하는 동안 화면 공유 기능을 비활성화하려면 이 정책을 비활성화합니다. 정책이 활성화되거나 구성되지 않은 경우 클라이언트 데스크톱 화면을 공유할 수 있습니다.</string>

         <string id="Enable_E911">Microsoft Teams용 E911 사용</string>

         <string id="Enable_E911_Desc">Microsoft Teams가 최적화된 모드에서 실행되는 동안 클라이언트는 E911 데이터를 Microsoft에 전송합니다. Microsoft와 E911 데이터를 공유하지 않도록 설정하려면 &quot;사용 안 함&quot;을 선택합니다. &quot;사용&quot; 또는 &quot;구성되지 않음&quot;이면 클라이언트 E911 데이터가 Microsoft와 공유됩니다.</string>

         <string id="Enable_HID">Microsoft Teams에 대해 클라이언트 HID 디바이스 사용 버튼 사용</string>

         <string id="Enable_HID_Desc">Microsoft Teams가 최적화된 모드에서 실행되는 동안 사용자는 클라이언트 HID 디바이스 버튼을 사용하여 Microsoft Teams와 상호 작용할 수 있습니다. 클라이언트 HID 디바이스 지원을 사용하지 않도록 설정하려면 &quot;사용 안 함&quot;을 선택합니다. &quot;사용&quot; 또는 &quot;구성되지 않음&quot;인 경우 클라이언트 HID 디바이스 지원이 허용됩니다.</string>

         <string id="Enable_Webrtc_Appshare">Microsoft Teams에 대한 개별 애플리케이션 공유 사용</string>

         <string id="Enable_Webrtc_Appshare_Desc">Microsoft Teams가 최적화된 모드에서 실행되는 동안 이 옵션을 사용하면 사용자가 개별 애플리케이션을 공유할 수 있습니다. 애플리케이션 공유를 사용하지 않도록 설정하려면 [사용 안 함]을 선택합니다. [사용] 또는 [구성되지 않음]이면 애플리케이션 공유가 허용됩니다.</string>

         <string id="Enable_Webrtc_Appshare_Givecontrol">Microsoft Teams의 개별 애플리케이션 공유에 대한 제어 권한 부여 사용</string>

         <string id="Enable_Webrtc_Appshare_Givecontrol_Desc">Microsoft Teams가 최적화된 모드로 실행되는 동안 사용자는 이 옵션을 사용하여 공유된 개별 애플리케이션을 제어할 수 있습니다. 개별 애플리케이션을 공유하는 동안 제어 권한 부여를 사용하지 않도록 설정하려면 이 정책을 &quot;사용 안 함&quot;으로 설정합니다. &quot;사용&quot; 또는 &quot;구성되지 않음&quot;이면 개별 애플리케이션을 공유하는 동안 제어 권한을 부여할 수 있습니다.</string>

         <string id="CustomBackgroundImages">Microsoft Teams 사용자 지정 배경 이미지</string>

         <string id="Enable_Background_Effects">Microsoft Teams의 배경 효과를 사용하도록 설정</string>

         <string id="Enable_Background_Effects_Desc">Microsoft Teams가 최적화된 모드로 실행되는 동안 사용자는 통화 및 회의에 사용할 가상 배경을 선택할 수 있습니다. 배경 효과 지원을 사용하지 않도록 설정하려면 &quot;사용 안 함&quot;을 선택합니다. &quot;사용&quot; 또는 &quot;구성되지 않음&quot;이면 배경 효과 지원이 허용됩니다.</string>

         <string id="ForceEnableCustomBackgroundImages">Microsoft Teams에 대해 사용자 지정 배경 이미지 기능을 강제로 사용하도록 설정하거나 사용하지 않도록 설정해야 함</string>

         <string id="ForceEnableCustomBackgroundImagesDesc">Microsoft Teams가 최적화된 모드에서 실행되는 동안 사용자는 통화 및 회의 중에 사용자 지정 배경 이미지를 적용할 수 있습니다. 사용자 지정 배경 이미지 지원을 사용하지 않도록 설정하려면 &quot;사용 안 함&quot;을 선택합니다. 강제로 사용자가 사용자 지정 배경 이미지만 사용하고 Microsoft Teams &quot;배경 효과&quot; UI에 제공된 스톡 이미지가 적용되지 않도록 하려면 &quot;사용&quot;을 선택합니다. &quot;구성되지 않음&quot;이면 사용자는 사용자 지정 배경 이미지 사용과 Microsoft Teams 제공 UI 이미지를 자신의 재량에 따라 전환할 수 있습니다.</string>

         <string id="CustomBackgroundImagesFolderPath">Microsoft Teams에 대해 사용자 지정 배경 이미지의 폴더 지정</string>

         <string id="CustomBackgroundImagesFolderPathDesc">Microsoft Teams가 최적화된 모드에서 실행되는 동안 사용자는 관리자가 업로드한 이미지 폴더에서 선택한 사용자 지정 배경 이미지를 적용할 수 있습니다. &quot;사용 안 함&quot; 또는 &quot;구성되지 않음&quot;이면 이미지를 업로드해야 하는 폴더는 C:\Program Files\Common Files\Omnissa\Remote Experience\x64\MSTeamsBackgroundImages입니다. 다른 폴더를 사용하려면 &quot;사용&quot;을 선택하고 사용자 지정 배경 이미지 폴더 텍스트 상자에 폴더 경로를 지정합니다(예: &quot;C:\Users\<USER>\CustomBackgroundImagesFolder&quot;).</string>

         <string id="CustomBackgroundDefaultImageName">사용자 오류 발생 시 적용할 기본 사용자 지정 배경 이미지 선택</string>

         <string id="CustomBackgroundDefaultImageNameDesc">사용자가 imageName 레지스트리 값을 비워 두거나 사용자 지정 배경 이미지 기능을 사용하도록 설정할 때 잘못된 사용자 지정 이미지 이름을 입력하는 경우에 적용할 기본 사용자 지정 이미지 이름을 지정합니다.</string>

         <string id="Disable_Mirrored_Video">Microsoft Teams에서 미러링된 자체 미리 보기 사용 안 함</string>

         <string id="Disable_Mirrored_Video_Desc">기본적으로 자체 미리 보기 비디오는 최적화된 모드에서 Microsoft Teams에 대해 미러링됩니다. 이 옵션을 설정하면 미러링된 비디오가 사용되지 않도록 설정됩니다.</string>
         
         <string id="Enable_Webrtc_ProxyProbeUrl">사용자 지정 프록시 프로브 URL을 사용하여 작업 프록시 서버를 감지합니다.</string>

         <string id="Enable_Webrtc_ProxyProbeUrl_Desc">여러 프록시 서버가 구성된 경우 사용자 지정 프록시 프로브 URL을 지정하여 작업 중인 프록시 서버를 검색하고 Microsoft Teams 호출에서 사용합니다. 예: https://teams.microsoft.com</string>

         <string id="Horizon_AppTap_Config">Horizon AppTap 구성</string>

         <string id="ProcessIgnoreList">빈 애플리케이션 세션을 감지한 경우 무시할 프로세스</string>

         <string id="ProcessIgnoreList_Desc">빈 애플리케이션 세션을 감지한 경우 무시할 프로세스 목록을 지정합니다. 프로세스 파일 이름 또는 전체 경로를 지정할 수 있습니다. 이러한 값은 대/소문자를 구분하지 않는 방식으로 평가됩니다. 환경 변수는 경로에서 허용되지 않습니다. UNC 네트워크 경로는 허용됩니다(예: \\Omnissa\temp\app.exe).</string>

         <string id="VDI_disconnect_time_till_logoff">연결이 끊어진 세션 시간제한(VDI)</string>

         <string id="VDI_disconnect_time_till_logoff_Desc">연결이 끊어진 VDI 데스크톱 세션이 자동으로 로그오프될 때까지의 시간을 지정합니다.
&quot;안 함&quot;을 선택하면 이 시스템 연결이 끊어진 VDI 데스크톱 세션이 로그오프되지 않습니다. &quot;즉시&quot;를 선택하면 연결이 끊어진 세션이 즉시 로그오프됩니다.

유사한 설정이 Horizon Connection Server 관리자에 존재하고 데스크톱 풀 설정에서 찾을 수 있으며, 이름은 &quot;연결 해제 후 자동 로그오프&quot;입니다. 이 설정과 Horizon Connection Server 관리자 설정이 모두 구성된 경우 여기에서 선택한 값이 우선적으로 적용됩니다.
예를 들어, 여기에서 &quot;안 함&quot;을 선택하면 Horizon Connection Server 관리자 설정과 관계없이 이 시스템 연결이 끊어진 세션이 로그오프되지 않습니다.</string>

         <string id="RDS_idle_time_till_disconnect">연결이 끊어질 때까지의 RDS 유휴 시간</string>

         <string id="RDS_idle_time_till_disconnect_Desc">유휴 원격 데스크톱 서비스 세션 연결이 자동으로 끊어질 때까지의 시간을 지정합니다.
&quot;안 함&quot;을 선택하면 이 시스템의 원격 데스크톱 서비스 세션 연결이 끊어지지 않습니다.</string>

         <string id="RDS_disconnect_time_till_logoff">로그오프할 때까지 RDS 연결이 끊긴 시간</string>

         <string id="RDS_disconnect_time_till_logoff_Desc">연결이 끊어진 원격 데스크톱 서비스 세션이 자동으로 로그오프될 때까지의 시간을 지정합니다.
&quot;안 함&quot;을 선택하면 이 시스템 연결이 끊어진 원격 데스크톱 서비스 세션이 로그오프되지 않습니다.</string>

         <string id="RDS_active_time_till_disconnect">연결이 끊어질 때까지의 RDS 연결 시간</string>

         <string id="RDS_active_time_till_disconnect_Desc">자동으로 연결이 끊기기 전에 원격 데스크톱 서비스 세션이 활성 상태를 유지할 수 있는 최대 시간을 지정합니다.
&quot;안 함&quot;을 선택하면 이 시스템의 원격 데스크톱 서비스 세션 연결이 끊어지지 않습니다.</string>

         <string id="RDS_end_session_time_limit">시간 제한에 도달한 경우 RDS 종료 세션</string>

         <string id="RDS_end_session_time_limit_Desc">연결을 끊지 않고 시간 초과된 원격 데스크톱 서비스 세션을 종료할지 여부를 지정합니다.
설정되면 활성 또는 유휴 세션에 대한 시간 제한에 도달한 후에 세션이 종료됩니다(사용자가 로그오프되고 서버에서 세션이 삭제됨). 기본적으로 원격 데스크톱 서비스 세션은 시간 제한에 도달한 후에 연결이 끊어집니다.</string>

         <string id="RDS_threshold_connecting_session">연결 중인 세션 임계값</string>

         <string id="RDS_threshold_connecting_session_Desc">다시 연결 중인 세션을 제외하고 RDSH 시스템에 동시에 로그온할 수 있는 최대 세션 수를 지정합니다.

사용하도록 설정한 경우 세션 임계값이 처음에는 20으로 설정되지만 사용 사례에 따라 변경해야 합니다. 0을 선택하면 연결 중인 세션 임계값이 사용되지 않도록 설정됩니다.

이 정책은 기본적으로 사용하지 않도록 설정되므로 정책이 구성되지 않으면 연결 중인 세션 임계값이 사용되지 않도록 설정됩니다.</string>

         <string id="RDS_threshold_load_index">인덱스 로드 임계값</string>

         <string id="RDS_threshold_load_index_Desc">다시 연결 중인 세션을 제외하고 RDSH 시스템이 세션 로그온 거부를 시작하는 최소 인덱스 로드를 지정합니다.

사용하도록 설정한 경우 로드 임계값이 처음에 0으로 설정되나, 사용 사례에 맞게 변경해야 합니다. 0을 선택하면 인덱스 로드 임계값이 사용되지 않도록 설정됩니다.

이 정책은 기본적으로 사용하지 않도록 설정되므로 정책이 구성되지 않으면 인덱스 로드 임계값이 사용되지 않도록 설정됩니다.</string>

         <string id="Prewarm_disconnect_time_till_logoff">사전 웜 세션 시간 제한</string>

         <string id="Prewarm_disconnect_time_till_logoff_Desc">사전 웜 세션이 자동으로 로그오프될 때까지의 시간을 지정합니다.</string>

         <string id="EnableUWPOnRDSH">RDSH 플랫폼에서 UWP 지원 사용</string>

         <string id="EnableUWPOnRDSH_Desc">이 정책은 UWP 애플리케이션을 지원하는 운영 체제 버전이 있는 RDSH 팜에서 해당 애플리케이션을 스캔 및 실행할 수 있는지 여부를 제어합니다. 이 정책은 VDI 애플리케이션 원격 작업과 같은 데스크톱 OS 플랫폼에는 적용되지 않습니다. 사용하도록 설정하면 UWP 애플리케이션을 RDSH 팜에서 호스팅된 애플리케이션으로 사용할 수 있습니다. GPO를 적용하려면 wsnm 서비스를 다시 시작하거나 RDSH 서버를 다시 시작해야 합니다. 이 설정이 기본적으로 사용하거나 사용하지 않도록 설정되어 있는지 여부와 지원되는 플랫폼에 대해서는 Omnissa 설명서를 참조하십시오.</string>

        <string id="HandleLegalNoticeInWindow">법적 알림 메시지를 창으로 리디렉션</string>

        <string id="HandleLegalNoticeInWindow_Desc">이 정책을 사용하도록 설정하면 법적 알림이 지정된 크기 창의 Horizon Client로 리디렉션됩니다. 이 정책의 너비와 높이는 픽셀 단위로 지정됩니다. 높은 DPI 모니터의 경우 DPI를 기준으로 크기를 몇 배씩 늘립니다. 이 기능은 RDSH 호스팅된 애플리케이션에서만 지원됩니다.
이 정책은 기본적으로 사용하지 않도록 설정됩니다. GPO를 적용하려면 RDSH 서버 및 Horizon Client를 다시 시작해야 합니다.</string>

        <string id="TIME_NEVER">안 함</string>

         <string id="TIME_1MIN">1분</string>

         <string id="TIME_5MIN">5분</string>

         <string id="TIME_10MIN">10분</string>

         <string id="TIME_15MIN">15분</string>

         <string id="TIME_30MIN">30분</string>

         <string id="TIME_1HR">1시간</string>

         <string id="TIME_2HR">2시간</string>

         <string id="TIME_3HR">3시간</string>

         <string id="TIME_6HR">6시간</string>

         <string id="TIME_8HR">8시간</string>

         <string id="TIME_10HR">10시간</string>

         <string id="TIME_12HR">12시간</string>

         <string id="TIME_18HR">18시간</string>

         <string id="TIME_1D">1일</string>

         <string id="TIME_2D">2일</string>

         <string id="TIME_3D">3일</string>

         <string id="TIME_4D">4일</string>

         <string id="TIME_5D">5일</string>

         <string id="TIME_1W">1주</string>

         <string id="TIME_IMMEDIATELY">즉시</string>

         <string id="EnableBatStatRedir">배터리 상태 리디렉션 사용</string>

         <string id="EnableDisplayNetworkState">네트워크 상태 표시 사용</string>
         <string id="EnableDisplayNetworkStateExplain">이 설정을 사용하면 Horizon Client UI에 네트워크 상태 메시지를 표시할지 여부를 구성할 수 있습니다. 사용하도록 설정되면 네트워크 연결 상태가 좋지 않을 때 최종 사용자에게 네트워크 상태 알림이 수신됩니다. 사용하지 않도록 설정되면 네트워크 연결 상태가 좋지 않을 때 최종 사용자에게 네트워크 상태 알림이 수신되지 않습니다. 이 속성은 기본적으로 사용하도록 설정됩니다.</string>

         <string id="EnableBatStatRedir_Desc">이 정책은 배터리 상태 리디렉션이 사용하도록 설정되어 있는지 여부를 제어합니다. 이 정책이 구성되지 않으면 배터리 상태 리디렉션이 사용하도록 설정됩니다.</string>
         <string id="Horizon_WaterMark">워터마크</string>
         <string id="Horizon_Watermark_Config">워터마크 구성</string>
         <string id="Desktop_Watermark_Configuration_Desc">이 설정을 사용하면 가상 데스크톱에 표시되는 워터마크를 구성할 수 있습니다. &quot;텍스트&quot; 영역에서 워터마크에 표시되는 내용을 설정할 수 있습니다. 옵션은 다음과 같습니다.

%ViewClient_IP_Address%
%ViewClient_Broker_UserName%
%ViewClient_Broker_DomainName%
%COMPUTERNAME%
%USERDOMAIN%
%USERNAME%
%ViewClient_ConnectTime%   - 날짜(월/일/년)
%ViewClient_ConnectTicks%  - 시간(시:분:초)

다음은 &quot;텍스트&quot;의 예입니다.
%USERDOMAIN%\%USERNAME%
%COMPUTERNAME% 의 %ViewClient_ConnectTime%
%ViewClient_IP_Address%

&quot;텍스트&quot;의 문자 제한은 256자이며 확장 후에는 1024자로 제한됩니다.

&quot;이미지 레이아웃&quot;은 워터마크의 레이아웃을 지정합니다. 타일, 다중 및 중앙을 지원합니다. 다중이면 워터마크가 중앙과 각 모서리에 배치됩니다. 애플리케이션 세션의 경우 이 설정은 무시되고 레이아웃은 항상 타일이 됩니다.
&quot;텍스트 회전&quot;을 사용하면 워터마크 텍스트에 대한 회전 각도를 선택할 수 있습니다.
&quot;불투명도&quot;를 사용하면 텍스트의 투명도를 선택할 수 있습니다.
&quot;여백&quot;은 워터마크와 가상 데스크톱 화면 가장자리 사이의 거리를 지정하며, 타일 레이아웃에만 적용됩니다.
[텍스트 색]은 공백으로 구분된 RGB 색 값을 사용하여 워터마크 텍스트 색을 십진수로 지정하고, 텍스트 외곽선은 대비 색으로 렌더링됩니다. 기본적으로 텍스트는 흰색으로 렌더링되고 개요는 검은색으로 렌더링됩니다.
[글꼴 크기]는 워터마크 텍스트의 크기를 지정합니다. 이 값이 0이면 기본 글꼴 크기가 적용됩니다.
&quot;새로 고침 간격&quot;은 워터마크가 새로 고쳐지는 간격(초)을 지정합니다. 0을 지정하면 워터마크 업데이트를 사용하지 않도록 설정됩니다. 최댓값은 86400초(24시간)입니다.
</string>
         <string id="Tile">타일</string>
         <string id="Multiple">다중</string>
         <string id="Center">중앙</string>
         <string id="TextColor">텍스트 색</string>
         <string id="FontSize">글꼴 크기</string>
         <string id="RefreshInterval">새로 고침 간격</string>
         <string id="BlockScreenCapture">화면 캡처 차단</string>
         <string id="BlockScreenCapture_Desc">최종 사용자가 끝점에서 가상 데스크톱 또는 원격 애플리케이션의 스크린샷을 생성할 수 있는지 여부를 결정합니다. 이 설정은 Windows용 Horizon Client 및 Mac용 Horizon Client 2106 이상에서만 적용할 수 있습니다. 기본값은 사용 안 함으로, 최종 사용자가 디바이스에서 스크린샷을 생성할 수 있도록 합니다.

사용: 최종 사용자가 Windows 또는 macOS 디바이스에서 가상 데스크톱 또는 가상 애플리케이션의 스크린샷을 생성하지 못하도록 차단합니다.

사용 안 함: 최종 사용자가 끝점에서 스크린샷을 생성하도록 허용합니다.


&quot;Horizon Mac Client에 대한 화면 녹화 허용&quot;은 &quot;화면 캡처 차단&quot; GPO를 사용하도록 설정한 경우 최종 사용자가 끝점에서 가상 데스크톱 또는 원격 애플리케이션의 화면 녹화를 수행할 수 있는지 여부를 결정합니다. 이 설정은 Mac용 Horizon Client 2309 이상에서만 적용할 수 있습니다. 기본값은 선택 취소로, 최종 사용자가 디바이스에서 화면 녹화를 수행하도록 허용하지 않습니다.

선택함: 최종 사용자가 macOS 디바이스에서 가상 데스크톱 또는 원격 애플리케이션의 화면 녹화를 수행하도록 허용합니다.

선택 취소함: 최종 사용자가 macOS 디바이스에서 화면 녹화를 수행하지 못하도록 차단합니다.</string>
         <string id="BlockThumbnailRepresentationWhenMinimized">최소화 시 축소 이미지 표현 차단</string>
         <string id="BlockThumbnailRepresentationWhenMinimized_Desc">원격 데스크톱의 축소 이미지를 마우스로 가리키면 창이 최소화될 때 원격 데스크톱 컨텐츠가 표시될지 여부를 결정합니다.
사용하도록 설정되면 창이 최소화될 때 창 축소 이미지 및 라이브 미리 보기에 대해 원격 데스크톱 컨텐츠 대신 Horizon Client 애플리케이션 아이콘이 표시됩니다.
사용하지 않도록 설정되거나 구성하지 않은 경우 최소화하기 전 마지막 원격 데스크톱의 스냅샷이 창 축소 이미지 및 라이브 미리 보기에 대해 표시됩니다. 이 GPO는 Windows 끝점에만 적용됩니다.</string>
         <string id="ScreenCaptureForMediaOffloaded">미디어 오프로드 솔루션에 대한 화면 캡처</string>
         <string id="ScreenCaptureForMediaOffloaded_Desc">미디어 세션이 끝점으로 오프로드될 때 최종 사용자가 VDI 에이전트 데스크톱에 대한 화면 캡처를 수행할 수 있도록 설정합니다.</string>
         <string id="AntiKeyLogger">Keylogger 차단</string>
         <string id="AntiKeyLogger_Desc">끝점이 키보드와 Horizon Client 간의 통신을 암호화하여 끝점의 키 로깅 맬웨어를 방지할지 여부를 결정합니다. 가상 시스템의 GPO 설정에 관계없이 Horizon Server에 대한 초기 연결은 항상 보호됩니다. 초기 인증 후 이 설정은 끝점의 모든 입력을 암호화할지 여부를 결정합니다. 이 설정은 Mac용 Horizon Client 2111 및 Windows용 Horizon Client 2203 이상에서만 적용할 수 있습니다. 기본값은 사용 안 함입니다.

사용: 키보드와 Horizon Client 간의 모든 키 입력을 암호화합니다.

사용 안 함: 키 입력은 끝점에서 정상적으로 전달됩니다.</string>
         <string id="BlockSendInput">합성 키 입력 차단</string>
         <string id="BlockSendInput_Desc">끝점이 끝점에서 가상 데스크톱 또는 애플리케이션으로 키 입력을 자동화하는 스크립트를 차단하는지 여부를 결정합니다. 가상 시스템의 GPO 설정에 관계없이 Horizon Server에 대한 초기 연결은 항상 보호됩니다. 초기 인증 후 이 설정은 끝점의 모든 합성 키 입력이 차단되었는지 여부를 결정합니다. 이 설정은 Windows용 Horizon Client 2312 이상에서만 적용할 수 있습니다. 기본값은 사용하지 않도록 설정됩니다.

&quot;Keylogger 차단&quot;을 사용하도록 설정하지 않으면 이 설정이 적용되지 않습니다.

사용: 끝점에서 가상 데스크톱 또는 가상 애플리케이션으로의 모든 합성 키 입력을 차단합니다.

사용 안 함: Horizon Client는 평소와 같이 합성 키 입력을 전달합니다.</string>
         <string id="AllowFIDO2AuthenticatorAccess">FIDO2 인증자 액세스 허용</string>
         <string id="AllowFIDO2AuthenticatorAccess_Desc">원격 데스크톱의 애플리케이션이 끝점의 FIDO2 인증자에 액세스할 수 있는지 여부를 결정합니다. 사용하지 않도록 설정된 경우 원격 데스크톱의 애플리케이션이 끝점의 FIDO2 인증자에 액세스할 수 없습니다. 사용하도록 설정되거나 구성되지 않은 경우 원격 데스크톱의 애플리케이션이 끝점의 FIDO2 인증자에 액세스할 수 있습니다.</string>
         <string id="FIDO2AllowList">FIDO2 허용 목록</string>
         <string id="FIDO2AllowList_Desc">끝점의 FIDO2 인증자에 액세스할 수 있는 애플리케이션 목록입니다.

구문:
   appname1.exe;appname2.exe

이 설정이 구성되지 않았거나 사용하지 않도록 설정되면 기본 목록이 사용됩니다. 기본 목록:
   chrome.exe;firefox.exe;msedge.exe</string>

         <string id="WaitForHybridJoin">하이브리드 가입 대기 구성</string>

         <string id="WaitForHybridJoin_Desc">이 GPO(그룹 정책 개체)는 Microsoft 하이브리드 Entra ID 가입 프로세스와 관련하여 에이전트의 동작을 제어합니다. 에이전트는 데스크톱 또는 애플리케이션 요청을 처리하기 전에 하이브리드 가입 프로세스가 완료될 때까지 기다려야 하는지 여부를 결정합니다.

사용 안 함 또는 구성되지 않음: 이 설정이 사용되지 않도록 설정되거나 구성되지 않은 경우 에이전트는 하이브리드 가입 프로세스가 완료될 때까지 기다리지 않습니다. 즉, 시스템이 Entra ID에 완전히 통합되기 전에 에이전트가 요청을 즉시 처리하기 시작할 수 있습니다.

사용: 사용하도록 설정되면 에이전트는 시스템이 Entra ID를 통한 하이브리드 가입 프로세스를 완료할 때까지 기다립니다. 이 프로세스가 완료된 후에만 에이전트는 자신을 사용 가능으로 표시하여 데스크톱 또는 애플리케이션 요청을 처리할 준비가 되었음을 나타냅니다.

이 기능을 사용하도록 설정하는 것은 에이전트가 요청을 처리하기 전에 Entra ID에 완전히 통합되도록 하는 데 매우 중요합니다. 이 통합은 Azure/Office 리소스에 대한 SSO(Single Sign-On)와 같은 기능을 사용하고 디바이스가 관리 목적으로 Entra ID에서 인식되는 데 필요합니다. 하지만 이 기능을 사용하도록 설정하면 에이전트가 하이브리드 가입 프로세스가 완료될 때까지 대기하므로 시스템 가용성이 크게 지연될 수 있습니다.
         </string>

         <string id="IpPrefix">Horizon Agent에서 사용하는 서브넷 구성</string>

         <string id="IpPrefixDesc">두 개 이상의 NIC가 있는 가상 시스템에 Horizon Agent를 설치할 때 Horizon Agent에서 사용하는 서브넷을 구성해야 합니다. 서브넷은 Horizon Agent에서 클라이언트 프로토콜 연결용으로 연결 서버 또는 연결 서비스 인스턴스에 제공할 네트워크 주소를 결정합니다.

구문:
   n.n.n.n/m

이 예제에서 n.n.n.n은 TCP/IP 서브넷이고 m은 서브넷 마스크의 비트 수입니다.

값 예:
   ***********/21

이 예제에서 *********** ~ ************* 범위의 IP 주소만 Horizon Agent에서 사용할 수 있습니다.
         </string>

      </stringTable>

      <presentationTable>
         <presentation id="MaxCollaboratorCount">
            <decimalTextBox refId="MaxCollaboratorCount_DB" defaultValue="5">최대</decimalTextBox>
         </presentation>

         <presentation id="CollaborationEmailInviteDelimiter">
            <textBox refId="CollaborationEmailInviteDelimiter_TB">
               <label>이메일 주소 간 구분 기호</label>
            </textBox>
         </presentation>

         <presentation id="CollaborationServerURLs">
            <listBox refId="CollaborationServerURLs_list">외부 서버 URL 및 이름 목록</listBox>
         </presentation>

         <presentation id="ConnectionTicketTimeout">
            <decimalTextBox refId="ConnectionTicketTimeout_DB" defaultValue="120">연결 티켓 시간 초과</decimalTextBox>
         </presentation>

         <presentation id="CredentialFilterExceptions">
            <textBox refId="CredentialFilterExceptions_TB">
               <label>자격 증명 필터 예외</label>
            </textBox>
         </presentation>
         <presentation id="RDPVcBridgeUnsupportedClients">
            <textBox refId="RDPVcBridgeUnsupportedClients_TB">
               <label>RDPVcBridge 지원되지 않는 클라이언트</label>
            </textBox>
         </presentation>

         <presentation id="CommandsToRunOnConnect">
            <listBox refId="CommandsToRunOnConnect_list">명령</listBox>
         </presentation>

         <presentation id="CommandsToRunOnReconnect">
            <listBox refId="CommandsToRunOnReconnect_list">명령</listBox>
         </presentation>

         <presentation id="CommandsToRunOnDisconnect">
            <listBox refId="CommandsToRunOnDisconnect_list">명령</listBox>
         </presentation>

         <presentation id="SSO_retry_timeout">
            <decimalTextBox refId="SSO_retry_timeout_DB" defaultValue="5000">Single Sign-On 다시 시도 시간 초과</decimalTextBox>
         </presentation>

         <presentation id="RDS_threshold_connecting_session">
            <decimalTextBox refId="RDS_threshold_connecting_session_DB" defaultValue="20">연결 중인 세션 임계값</decimalTextBox>
         </presentation>

         <presentation id="RDS_threshold_load_index">
            <decimalTextBox refId="RDS_threshold_load_index_DB" defaultValue="0">인덱스 로드 임계값</decimalTextBox>
         </presentation>

         <presentation id="Win10PhysicalAgentAudioOption">
            <dropdownList refId="Win10PhysicalAgentAudioOption_DDL" defaultItem="0">단일 세션 Windows 10 물리적 원격 데스크톱 시스템에 대한 오디오 옵션</dropdownList>
         </presentation>

         <presentation id="WaitForLogoff">
            <decimalTextBox refId="WaitForLogoff_DB" defaultValue="10">로그오프 시간 초과 대기</decimalTextBox>
         </presentation>

         <presentation id="Accept_SSL_encr_framework_channel">
            <dropdownList refId="Accept_SSL_encr_framework_channel_DDL" defaultItem="0">SSL 암호화된 프레임워크 채널 허용</dropdownList>
         </presentation>

         <presentation id="Local_Reader_Name">
            <textBox refId="Local_Reader_Name_TB">
               <label>로컬 판독기 이름</label>
            </textBox>
         </presentation>

         <presentation id="Cert_wait_timeout">
            <decimalTextBox refId="Cert_wait_timeout_DB" defaultValue="40">인증서 대기 시간 초과</decimalTextBox>
         </presentation>

         <presentation id="Min_key_size">
            <decimalTextBox refId="Min_key_size_DB" defaultValue="1024">최소 키 크기</decimalTextBox>
         </presentation>

         <presentation id="All_key_sizes">
            <textBox refId="All_key_sizes_TB">
               <label>모든 키 크기</label>
                  <defaultValue>2048</defaultValue>
            </textBox>
         </presentation>

         <presentation id="Keys_to_precreate">
            <decimalTextBox refId="Keys_to_precreate_DB" defaultValue="5">미리 생성할 키의 수</decimalTextBox>
         </presentation>

         <presentation id="Cert_min_validity">
            <decimalTextBox refId="Cert_min_validity_DB" defaultValue="10">인증서에 필요한 최소 유효 기간</decimalTextBox>
         </presentation>

         <presentation id="WhfbCertificateAllowedApplicationsList">
            <multiTextBox refId="Whfb_Certificate_Allowed_Applications_list_TB">허용된 실행 파일 목록</multiTextBox>
         </presentation>

         <presentation id="Exclude_Vid_Pid">
            <textBox refId="Exclude_Vid_Pid_TB">
               <label>Vid/Pid 디바이스 제외</label>
            </textBox>
         </presentation>

         <presentation id="Exclude_Vid_Pid_Rel">
            <textBox refId="Exclude_Vid_Pid_Rel_TB">
               <label>Vid/Pid/Rel 디바이스 제외</label>
            </textBox>
         </presentation>

         <presentation id="Include_Vid_Pid">
            <textBox refId="Include_Vid_Pid_TB">
               <label>Vid/Pid 디바이스 포함</label>
            </textBox>
         </presentation>

         <presentation id="Include_Vid_Pid_Rel">
            <textBox refId="Include_Vid_Pid_Rel_TB">
               <label>Vid/Pid/Rel 디바이스 포함</label>
            </textBox>
         </presentation>

         <presentation id="Exclude_device_family">
            <textBox refId="Exclude_device_family_TB">
               <label>디바이스 제품군 제외</label>
            </textBox>
         </presentation>

         <presentation id="Include_device_family">
            <textBox refId="Include_device_family_TB">
               <label>디바이스 제품군 포함</label>
            </textBox>
         </presentation>

         <presentation id="HidOpt_Include_Vid_Pid">
            <textBox refId="HidOpt_Include_Vid_Pid_TB">
               <label>HID 최적화 Vid/Pid 디바이스 포함</label>
            </textBox>
         </presentation>

         <presentation id="Exclude_Auto_Vid_Pid">
            <textBox refId="Exclude_Auto_Vid_Pid_TB">
               <label>자동 연결 Vid/Pid 디바이스 제외</label>
            </textBox>
         </presentation>

         <presentation id="Exclude_auto_device_family">
            <textBox refId="Exclude_auto_device_family_TB">
               <label>자동 연결 디바이스 제품군 제외</label>
            </textBox>
         </presentation>

         <presentation id="Exclude_Vid_Pid_from_Split">
            <textBox refId="Exclude_Vid_Pid_from_Split_TB">
               <label>분할에서 Vid/Pid 디바이스 제외</label>
            </textBox>
         </presentation>

         <presentation id="Split_Vid_Pid_Device">
            <textBox refId="Split_Vid_Pid_Device_TB">
               <label>Vid/Pid 디바이스 분할</label>
            </textBox>
         </presentation>

         <presentation id="Allow_other_input_devices">
            <dropdownList refId="Allow_other_input_devices_DDL" defaultItem="0">기타 입력 디바이스 허용</dropdownList>
         </presentation>

         <presentation id="Allow_HID_Bootable">
            <dropdownList refId="Allow_HID_Bootable_DDL" defaultItem="0">HID 부팅 가능 허용</dropdownList>
         </presentation>

         <presentation id="Allow_Audio_Input_devices">
            <dropdownList refId="Allow_Audio_Input_devices_DDL" defaultItem="0">오디오 입력 디바이스 허용</dropdownList>
         </presentation>

         <presentation id="Allow_Audio_Output_devices">
            <dropdownList refId="Allow_Audio_Output_devices_DDL" defaultItem="0">오디오 출력 디바이스 허용</dropdownList>
         </presentation>

         <presentation id="Allow_keyboard_mouse">
            <dropdownList refId="Allow_keyboard_mouse_DDL" defaultItem="0">키보드 및 마우스 디바이스 허용</dropdownList>
         </presentation>

         <presentation id="Allow_Video_Devices">
            <dropdownList refId="Allow_Video_Devices_DDL" defaultItem="0">비디오 디바이스 허용</dropdownList>
         </presentation>

         <presentation id="Allow_Smart_Cards">
            <dropdownList refId="Allow_Smart_Cards_DDL" defaultItem="0">스마트 카드 허용</dropdownList>
         </presentation>

         <presentation id="Allow_Auto_Device_Splitting">
            <dropdownList refId="Allow_Auto_Device_Splitting_DDL" defaultItem="0">자동 디바이스 분할 허용</dropdownList>
         </presentation>

         <presentation id="Accept_SSL_encr_framework_channel_2">
            <dropdownList refId="Accept_SSL_encr_framework_channel_2_DDL" defaultItem="0">SSL 암호화된 프레임워크 채널 허용</dropdownList>
         </presentation>

         <presentation id="Default_proxy_server">
            <textBox refId="Default_proxy_server_TB">
               <label>기본 프록시 서버</label>
            </textBox>
         </presentation>

         <presentation id="Update_Java_Proxy">
            <dropdownList refId="Update_Java_Proxy_DDL" defaultItem="2">Java 애플릿에 대한 프록시 설정</dropdownList>
         </presentation>

         <presentation id="HTML5MMRUrlList">
            <listBox refId="HTML5MMRUrlList_list">Horizon HTML5 멀티미디어 리디렉션을 사용하도록 설정할 URL 목록입니다.</listBox>
         </presentation>

         <presentation id="HTML5MMRUrlBlockList">
            <listBox refId="HTML5MMRUrlBlockList_list">Horizon HTML5 멀티미디어 리디렉션을 제외할 URL 목록입니다.</listBox>
         </presentation>

         <presentation id="GeoRedirUrlList">
            <listBox refId="GeoRedirUrlList_list">Horizon 지리적 위치 리디렉션 기능을 사용하도록 설정할 URL 목록입니다.</listBox>
         </presentation>

         <presentation id="GeoRedirDistanceDelta">
            <textBox refId="GeoRedirDistanceDelta_value">
               <label>최소 거리(미터)</label>
               <defaultValue>75</defaultValue>
            </textBox>
         </presentation>

         <presentation id="WebrtcProxyProbeUrl">
            <textBox refId="WebrtcProxyProbeUrl_value">
               <label>URL을 사용하여 webrtc 호출에 대한 프록시 서버 검색</label>
            </textBox>
         </presentation>


         <presentation id="BrowserRedirUrlList">
            <listBox refId="BrowserRedirUrlList_list">Horizon 브라우저 리디렉션 기능을 사용하도록 설정할 URL 목록입니다.</listBox>
         </presentation>

         <presentation id="EnhBrowserRedirUrlList">
            <listBox refId="EnhBrowserRedirUrlList_list">Horizon 고급 브라우저 리디렉션 기능을 사용하도록 설정할 URL 목록입니다.</listBox>
         </presentation>

         <presentation id="BrowserRedirNavUrlList">
            <listBox refId="BrowserRedirNavUrlList_list">Horizon 브라우저 리디렉션 기능에 대해 탐색을 사용하도록 설정할 URL 목록입니다.</listBox>
         </presentation>

         <presentation id="WebRTC_SDK_Web_App_Redir_Allow_List">
            <listBox refId="WebRTC_SDK_Web_App_Redir_Allow_List_list">Web 애플리케이션 지원을 위해 Horizon WebRTC SDK를 사용하도록 설정할 URL 목록입니다.</listBox>
         </presentation>

         <presentation id="SetForceNonOptimizedOptions">
            <checkBox refId="AUTO_DETECT_EXTERNAL_CONNECTIONS_CHKBOX">외부 연결 자동 감지</checkBox>
            <textBox refId="SetForceNonOptimizedEnvVar_TB">
               <label>환경 변수 이름:</label>
            </textBox>
         </presentation>

         <presentation id="UnityFilterRules_Filter">
            <listBox refId="UnityFilterRules_FilterList">Unity 필터 규칙</listBox>
         </presentation>

         <presentation id="Enable_Unity_Touch">
            <checkBox refId="Enabled_UWP_For_UnityTouch_CB" defaultChecked="true">Windows 10에서 Unity Touch에 대한 UWP(범용 Windows 플랫폼) 애플리케이션 지원을 사용하도록 설정합니다.</checkBox>
         </presentation>

         <presentation id="ProcessIgnoreList">
            <multiTextBox refId="ProcessIgnoreList_TB">빈 애플리케이션 세션을 감지할 때 무시할 프로세스</multiTextBox>
         </presentation>

         <presentation id="VDI_idle_time_till_disconnect">
            <dropdownList refId="VDI_idle_time_till_disconnect_minutes" noSort="true" defaultItem="0">유휴 시간 초과</dropdownList>
         </presentation>

         <presentation id="VDI_disconnect_time_till_logoff">
            <dropdownList refId="VDI_disconnect_time_till_logoff_minutes" noSort="true" defaultItem="0">연결 끊기 시간 초과</dropdownList>
         </presentation>

         <presentation id="RDS_idle_time_till_disconnect">
            <dropdownList refId="RDS_idle_time_till_disconnect_milliseconds" noSort="true" defaultItem="0">RDS 유휴 시간 초과</dropdownList>
         </presentation>

         <presentation id="RDS_disconnect_time_till_logoff">
            <dropdownList refId="RDS_disconnect_time_till_logoff_milliseconds" noSort="true" defaultItem="0">RDS 연결 끊기 시간 초과</dropdownList>
         </presentation>

         <presentation id="RDS_active_time_till_disconnect">
            <dropdownList refId="RDS_active_time_till_disconnect_milliseconds" noSort="true" defaultItem="0">RDS 연결 시간 초과</dropdownList>
         </presentation>

         <presentation id="Prewarm_disconnect_time_till_logoff">
            <dropdownList refId="Prewarm_disconnect_time_till_logoff_minutes" noSort="true" defaultItem="5">사전 웜 시간 초과</dropdownList>
         </presentation>

         <presentation id ="watermark_configuration">
            <multiTextBox refId="Text">텍스트</multiTextBox>
            <dropdownList refId="ImageFit" noSort="true" defaultItem="0">이미지 레이아웃</dropdownList>
            <decimalTextBox refId="Rotation" defaultValue="45">텍스트 회전</decimalTextBox>
            <decimalTextBox refId="Opacity" defaultValue="50">불투명도</decimalTextBox>
            <decimalTextBox refId="Margin" defaultValue="50">여백</decimalTextBox>
            <textBox refId="TextColor">
               <label>텍스트 색</label>
               <defaultValue>255 255 255</defaultValue>
            </textBox>
            <decimalTextBox refId="FontSize" defaultValue="0">글꼴 크기</decimalTextBox>
            <decimalTextBox refId="RefreshInterval" defaultValue="0">새로 고침 간격</decimalTextBox>
         </presentation>

        <presentation id="AppSignInWindow">
            <decimalTextBox refId="app_sign_in_window_width" defaultValue="800" spinStep="2">법적 알림 창 너비: </decimalTextBox>
            <decimalTextBox refId="app_sign_in_window_height" defaultValue="600" spinStep="2">법적 알림 창 높이: </decimalTextBox>
        </presentation>
        <presentation id="Video_Cpu_Overuse_Threshold">
            <decimalTextBox refId="Video_Cpu_Overuse_Threshold_data" defaultValue="85">비디오 CPU 과용 임계값</decimalTextBox>
         </presentation>
         <presentation id="Webrtc_Recommended_AEC">
            <checkBox refId="Webrtc_Recommended_AEC_CB" defaultChecked="true"> 권장 AEC 알고리즘 사용 </checkBox>
         </presentation>
         <presentation id="Force_Enable_Teams_Redir">
            <checkBox refId="Force_Enable_Teams_Redir_Value" defaultChecked="false"> 클라이언트 측 WebRTC 최적화 강제 적용 </checkBox>
         </presentation>
         <presentation id="Force_Enable_Electron_App_Redir">
            <checkBox refId="Force_Enable_Electron_App_Redir_Value" defaultChecked="false"> 클라이언트 측 WebRTC 최적화 강제 적용 </checkBox>
         </presentation>
         <presentation id="FIDO2AllowList">
            <textBox refId="FIDO2AllowList_TB">
               <label>FIDO2 허용 목록</label>
               <defaultValue>chrome.exe;firefox.exe;msedge.exe</defaultValue>
            </textBox>
         </presentation>
         <presentation id="AllowScreenRecording">
            <checkBox refId="AllowScreenRecording_CB" defaultChecked="false"> Horizon Mac Client에 대한 화면 녹화 허용 </checkBox>
         </presentation>
         <presentation id="CustomBackgroundImagesFolderPath">
            <textBox refId="CustomBackgroundImagesFolder_Path">
               <label>사용자 지정 배경 이미지 폴더</label>
            </textBox>
         </presentation>
         <presentation id="CustomBackgroundDefaultImageName">
            <textBox refId="CustomBackgroundDefaultImageName_Value">
               <label>기본 이미지 이름</label>
            </textBox>
         </presentation>
         <presentation id="EnableDisplayNetworkState">
            <decimalTextBox refId="NetworkWarningInterval_CB" defaultValue="5">네트워크 경고 팝업 메시지의 시간 간격(분)입니다. 최대 60분, 최소 1분. 기본값은 5분입니다.</decimalTextBox>
         </presentation>
         <presentation id="IpPrefix" >
            <textBox refId="IpPrefixTextBox" >
               <label >IP 접두사</label>
            </textBox>
         </presentation>
      </presentationTable>
   </resources>
</policyDefinitionResources>
