/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/* global require:false module:false */


"use strict";

var _ = require('underscore');
var fs = require('fs');
var os = require('os');
var path = require('path');
const execFile = require('child_process').execFile;
const execFileSync = require('child_process').execFileSync;
var waitMs = require('./utils').waitMs;
var stringToRegMultiSz = require('./utils').stringToRegMultiSz;

// Object which contains path to host config file, config strings applied, etc.
var config = {};
// Options that the test suite is using
var benevTestOptions = {};

const write_access_octal_equivalent_value = parseInt('0200', 8);

/*
 *-----------------------------------------------------------------------------
 * primeConfigs:
 *
 *    This function sets up the config files to be used by BENeV and returns
 *    a config object which contains the path to the config file as well as a
 *    map of all the strings in the config file.
 *
 *-----------------------------------------------------------------------------
 */

function primeConfigs(defaultOptions, doneCb) {
   config.strs = {};
   benevTestOptions = defaultOptions;

   if (process.platform === "win32") {
      // Set config.path
      setWindowsProxyConfig(defaultOptions.branding);

      // Log levels are controlled by the registry on Windows
      setRegKeysForWindows(defaultOptions).then(() => {
         doneCb();
      }).catch((error) => {
         doneCb(error);
      });

   } else if (process.platform === "linux") {
      // Set config.path
      setLinuxProxyConfig(defaultOptions.branding);

      // Log levels are controlled by the config file on Linux
      if (benevTestOptions.doMemLeakCheck) {
         let strs = {
            'vvc.logLevel': "trace",
            'vvc.memLogLevel': "all"
         };

         writeConfigs(strs);
      }
      doneCb();
   }
}


/*
 *-----------------------------------------------------------------------------
 * clearConfigs:
 *
 *    Resets the system's config files back to what they were before the test
 *    suite ran.
 *
 *-----------------------------------------------------------------------------
 */

function clearConfigs() {
   // Windows cleanup involves unsetting reg keys and removing the temp config file
   if (process.platform === 'win32') {
      // Clean up memory leak related reg keys
      if (benevTestOptions.doMemLeakCheck) {
         try {
            execFile('Regedit', ['/s', 'Unset.reg']);
         } catch (error) {
            console.log(`Failed to reset the log level reg keys`);
         }
      }

      // Clean up Vvc API related reg keys
      unsetRegKeysForVvcAPITests();
      // Replace our temp config file with the original
      fs.rename(config.tempPath, config.path, (err) => {
         if (err && err.code != 'ENOENT') {
            console.log(`Error in renaming Windows config file: ${err}`);
            throw err;
         }
      });

   } else if (process.platform === 'linux') {
      // No teardown necessary currently
   }

   console.log("Test configs are cleared");
}


/*
 *-----------------------------------------------------------------------------
 * writeConfigs:
 *
 *    Writes the new config options (passed as JSON) to the system's config
 *    file.
 *
 *-----------------------------------------------------------------------------
 */

function writeConfigs(configs, mode) {
   config.strs = Object.assign(config.strs, configs);

   let cfgStr = '';
   _.each(config.strs, function (value, key) {
      cfgStr += key;
      cfgStr += '=';
      cfgStr += value;
      cfgStr += os.EOL;
   });

   try {
      fs.writeFileSync(config.path,
                       cfgStr,
                       {
                          encoding: "utf8",
                          flag: mode
                       });
   } catch(err) {
      console.log(`Error in writing to config file "${config.path}"`);
      throw(err);
   }
}


/*
 *-----------------------------------------------------------------------------
 * setWindowsProxyConfig:
 *
 *    Renames local Windows config file to a temp name so that any new test
 *    settings won't muck up a user's existing environment. Both client and
 *    server will read this file.
 *
 *-----------------------------------------------------------------------------
 */

function setWindowsProxyConfig(branding) {
   let base = process.env.APPDATA;
   config.path = path.join(base,
                           "Omnissa",
                           "config.ini");
   if (branding == 'old') {
      config.path = path.join(base,
                           'VMware',
                           'config.ini');
   }

   config.tempPath = config.path + "temp";

   /*
    * If there's an existing config file, rename it so that it won't be
    * overwritten. In clearConfigs we'll rename it back.
    */
   fs.rename(config.path, config.path + "temp", (err) => {
      if (err && err.code != 'ENOENT') {
         console.log(`Error in renaming Windows config file: ${err}`);
         throw err;
      }
   });
}


/*
 *-----------------------------------------------------------------------------
 * setLinuxProxyConfig:
 *
 *    Sets proxy server and client executable's config to the key/value pairs
 *    in the cfg object for linux. Both client and server will read this file.
 *
 *-----------------------------------------------------------------------------
 */

function setLinuxProxyConfig(branding) {
   const CFG_DIR_COMPONENTS = ['.benev', '.omnissa'];
   if (branding == 'old') {
      CFG_DIR_COMPONENTS[1] = '.vmware';
   }

   var cfgDir = os.homedir();
   var cfgFile;

   for (let dir of CFG_DIR_COMPONENTS) {
      cfgDir = path.join(cfgDir, dir);
      try {
         fs.mkdirSync(cfgDir);
      } catch (err) {
         if (err.code != 'EEXIST') {
            console.log(`Error in creating config directory "$(cfgDir}"`);
            throw (err);
         }
      }
   }

   config.path = path.join(cfgDir, 'config');
}


/*
 *-----------------------------------------------------------------------------
 * setRegKeysForWindowsForAllowChannelList:
 *
 *    This function prepares all registry keys for Allow Channel List test.
 *
 *    When test with feature enabled, turn on the feature by adding the
 *    registry key (AllowVirtualChannels) and all necessary keys. Then base
 *    on allow/disallow test, modify the channel name for the test.
 *
 *    When test with feature disabled, remove the registry key
 *    (AllowVirtualChannels) to turn off the feature.
 *
 *-----------------------------------------------------------------------------
 */

function setRegKeysForWindowsForAllowChannelList(opts, rootRegKeyLocation, vvcAPITestDirectory) {
   if (opts.benevMode == "client") {
      return "";
   }

   let vvc32bitRegKeysLocation = rootRegKeyLocation + 'Blast\\vvc\\lib]';
   let vvc64bitRegKeysLocation = rootRegKeyLocation + 'Blast\\vvc\\lib\\x64]';
   let agentInstallRegKeysLocation = rootRegKeyLocation + 'RemoteExperienceAgent]';
   let virtualChannelRegKeyLocation = rootRegKeyLocation + 'VirtualChannels';
   if (opts.branding === 'old') {
      vvc32bitRegKeysLocation = rootRegKeyLocation + 'VMware Blast\\vvc\\lib]';
      vvc64bitRegKeysLocation = rootRegKeyLocation + 'VMware Blast\\vvc\\lib\\x64]';
      agentInstallRegKeysLocation = rootRegKeyLocation + 'VMware VDM\\RemoteExperienceAgent]';
      virtualChannelRegKeyLocation = rootRegKeyLocation + 'VMware VDM\\VirtualChannels';
   }

   let vvc32bitRegKeys = '';
   let vvc64bitRegKeys = '';
   let agentInstallRegKeys = '';
   let virtualChannelRegKeys = '';
   let virtualChannelFeatureRegKeys = '';

   if (opts.doAllowChannelList === true) {
      // Add registry keys for horizon-vvc.dll to pass the security check
      let vvcCommonKey = '@=\"C:\\\\Program Files\\\\Common Files\\\\Omnissa\\\\Blast';
      let vvc32bitKey = vvcCommonKey + '\\\\horizon-vvc.dll\"';
      let vvc64bitKey = vvcCommonKey + '\\\\x64\\\\horizon-vvc.dll\"';
      if (opts.branding === 'old') {
         vvcCommonKey = '@=\"C:\\\\Program Files\\\\Common Files\\\\VMware\\\\VMware Blast';
         vvc32bitKey = vvcCommonKey + '\\\\vmware-vvc.dll\"';
         vvc64bitKey = vvcCommonKey + '\\\\x64\\\\vmware-vvc.dll\"';
      }
      vvc32bitRegKeys = os.EOL + os.EOL + vvc32bitRegKeysLocation + os.EOL + vvc32bitKey;
      vvc64bitRegKeys = os.EOL + os.EOL + vvc64bitRegKeysLocation + os.EOL + vvc64bitKey;

      // Add registry key for agent install path for vvcServerTestApp.exe to load vdpService.dll.
      let agentInstallPath = path.resolve(path.join(vvcAPITestDirectory, '/../'));
      agentInstallPath = agentInstallPath.split('\\').join('\\\\');
      let agentInstallPathKey = '\"InstallPath\"=\"' + agentInstallPath + '\\\\\"';
      agentInstallRegKeys = os.EOL + os.EOL + agentInstallRegKeysLocation + os.EOL + agentInstallPathKey;

      // Add registry key [AllowVirtualChannels = 1] to turn on Allow Channel List feature.
      let virtualChannelKeyLocation = virtualChannelRegKeyLocation + "]";
      let virtualChannelKeys = '\"AllowVirtualChannels\"=\"1\"';
      virtualChannelRegKeys = os.EOL + os.EOL + virtualChannelKeyLocation  + os.EOL + virtualChannelKeys;

      /*
       * Add registry key [BenevTest] to simulate RX feature. First clear both channel names [VVCTest] and [VVCTest1].
       * Then base on either Allow or Disallow test, add [VVCTest1] as the allowed channel name or [VVCTest]
       * as the disallowed channel name. Use the full path name of vvcServerTestApp.exe as the RX process opening
       * the channel.
       */
      let virtualChannelFeatureKeyLocation = virtualChannelRegKeyLocation + "\\BenevTest]";
      let virtualChannelFeatureKeyDeleteChannelName = '\"VVCTest\"=-' + os.EOL + '\"VVCTest1\"=-';
      let virtualChannelFeatureKeys = (opts.allowChannel === true ? '\"VVCTest1\"=' : '\"VVCTest\"=') +
                                      '\"' + agentInstallPath + '\\\\x64\\\\vvcServerTestApp.exe\"';
      virtualChannelFeatureRegKeys = os.EOL + os.EOL + virtualChannelFeatureKeyLocation  + os.EOL +
                                     virtualChannelFeatureKeyDeleteChannelName + os.EOL + virtualChannelFeatureKeys;
   } else {
      // Remove registry key [AllowVirtualChannels] to turn off Allow Channel List feature.
      let virtualChannelKeyLocation = virtualChannelRegKeyLocation + "]";
      let virtualChannelKeys = '\"AllowVirtualChannels\"=-';
      virtualChannelRegKeys = os.EOL + os.EOL + virtualChannelKeyLocation  + os.EOL + virtualChannelKeys;
   }

   return vvc32bitRegKeys + vvc64bitRegKeys + agentInstallRegKeys +
          virtualChannelRegKeys + virtualChannelFeatureRegKeys;
}


/*
 *-----------------------------------------------------------------------------
 * setRegKeysForRawChannelTest:
 *
 *    This function sets all registry keys for Raw Channel test.
 *
 *-----------------------------------------------------------------------------
 */
function setRegKeysForRawChannelTest(opts, rootRegKeyLocation)
{
   if (opts.branding != "new" || opts.agentRawBeatPort == null) {
      return "";
   }

   let rawChannelRegKeysLocation = rootRegKeyLocation + 'Blast\\vvc]';
   let rawChannelRegKeys = os.EOL + os.EOL + rawChannelRegKeysLocation + os.EOL +
         '\"RawChannelsEnabled\"=' + (opts.enableRawChannel ? '\"1\"' : '\"0\"');

   let clientVvcRegKeyLocation = rootRegKeyLocation + 'Client\\vvc]';
   rawChannelRegKeys += os.EOL + os.EOL + clientVvcRegKeyLocation + os.EOL +
         '\"RawChannelsEnabled\"=' + (opts.enableRawChannel ? '\"1\"' : '\"0\"');

   let udpPortRangeRegLocation = '[HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services\\hznvudpd\\Parameters]';
   let udpPortRangeRegKeys = os.EOL + os.EOL + udpPortRangeRegLocation + os.EOL +
         '\"UdpBeatSideChannelStartPorts\"=hex(7):' +
         stringToRegMultiSz(opts.agentRawBeatPort.toString() + '\n' +
                            (opts.agentRawBeatPort + 10).toString() + '\n' +
                            (opts.agentRawBeatPort + 20).toString()) +
          os.EOL +'\"UdpBeatSideChannelNumPorts\"=\"10\"';

   return rawChannelRegKeys + udpPortRangeRegKeys;
}


/*
 *-----------------------------------------------------------------------------
 * setRegKeysForRawChannelTest:
 *
 *    This function sets all registry keys for Raw Channel test.
 *
 *-----------------------------------------------------------------------------
 */
function setRegKeyForRawChannelType(rawChannelType)
{
   if (process.platform != 'win32') {
      return;
   }

   let regKeyLocation = '[HKEY_LOCAL_MACHINE\\SOFTWARE\\Omnissa\\Horizon\\Blast\\vvc]';
   let rawChannelRegKeys = os.EOL + os.EOL + regKeyLocation + os.EOL +
         '\"RawChannelType\"=\"' + rawChannelType + '\"';

   try {
      fs.chmodSync('SetRawChannelType.reg', write_access_octal_equivalent_value);
   } catch (error) {
      console.log(`ERROR: Unable to make SetRawChannelType.reg writable`);
      throw(error);
   }

   let regHeader = 'Windows Registry Editor Version 5.00' + os.EOL + os.EOL;

   try {
      let regValue = regHeader + rawChannelRegKeys;
      fs.writeFileSync('SetRawChannelType.reg', regValue, 'utf8');
   } catch (error) {
      console.log('ERROR: Unable to write to SetRawChannelType.reg.');
      throw(error);
   }

   try {
      execFileSync('reg', ['import', 'SetRawChannelType.reg']);
   } catch (error) {
      console.log('ERROR: Unable to set registry keys. Please make sure ' +
                  'this command prompt is being run with administrator privileges.');
      throw(error);
   }
}


/*
 *-----------------------------------------------------------------------------
 * setRegKeysForWindows:
 *
 *    This function edits reg keys that the tests need. If
 *    memory leak checks are enabled, it'll change the logLevel and
 *    memLogLevel reg keys to 'trace' and 'all' respectively.
 *    The keys needed for Vvc API tests are configured in a separate method.
 *
 *-----------------------------------------------------------------------------
 */

async function setRegKeysForWindows(opts) {
   const dirname = require('path').dirname;
   const resolve = require('path').resolve;

   let rootRegKeyLocation = '[HKEY_LOCAL_MACHINE\\SOFTWARE\\Omnissa\\Horizon\\';
   let clientMemLeakRegKeysLocation = rootRegKeyLocation + 'Client\\vvc]';
   let agentMemLeakRegKeysLocation = rootRegKeyLocation + 'Blast\\vvc]';

   if (opts.branding == 'old') {
      rootRegKeyLocation = '[HKEY_LOCAL_MACHINE\\SOFTWARE\\VMware, Inc.\\';
      clientMemLeakRegKeysLocation = rootRegKeyLocation + 'VMware VDM\\Client\\vvc]';
      agentMemLeakRegKeysLocation = rootRegKeyLocation + 'VMware Blast\\vvc]';
   }

   try {
      fs.chmodSync('Unset.reg', write_access_octal_equivalent_value);
      fs.chmodSync('Set.reg', write_access_octal_equivalent_value);
      // Strip '[' character from the path
      execFile('Regedit', ['/e', 'Unset.reg', rootRegKeyLocation.substring(1)]);
      console.log('NOTE: Log level reg keys will be edited to run certain ' +
                  'tests. They will be reverted upon exiting.');
   } catch (error) {
      console.log('ERROR: Unable to make Set.reg and Unset.reg writable\n', error);
      throw(error);
   }

   let clientMemLeakRegKeys = '';
   let agentMemLeakRegKeys = '';
   if (benevTestOptions.doMemLeakCheck) {
      let keys = '\"logLevel\"=\"trace\"' + os.EOL + '\"memLogLevel\"=\"all\"';

      clientMemLeakRegKeys = os.EOL + os.EOL + clientMemLeakRegKeysLocation + os.EOL + keys;
      agentMemLeakRegKeys = os.EOL + os.EOL + agentMemLeakRegKeysLocation + os.EOL + keys;
   }

   // Setup registry keys for Raw Channel test.
   let rawChannelRegKeys = setRegKeysForRawChannelTest(opts, rootRegKeyLocation);

   let regHeader = 'Windows Registry Editor Version 5.00' + os.EOL + os.EOL;

   // Retry writing to Set.reg in case of failure to open/write. See bugs 3102922 / 3208567.
   let writeComplete = false;
   let retryCount = 0, maxRetryCount = 3;
   while (!writeComplete) {
      try {
         fs.writeFileSync('Set.reg', regHeader + clientMemLeakRegKeys + agentMemLeakRegKeys + rawChannelRegKeys, 'utf8');
         writeComplete = true;
      } catch (error) {
         if (retryCount >= maxRetryCount) {
            console.log('ERROR: Unable to write to Set.reg.\n', error);
            throw(error);
         }

         // Do exponential backoff for retries
         retryCount++;
         let timeout = Math.pow(2, retryCount) * 100;
         console.log(`Writing to Set.reg failed, will retry in ${timeout} ms`);
         await waitMs(timeout);
      }
   }

   try {
      execFile('Regedit', ['/s', 'Set.reg']);
   } catch (error) {
      console.log('ERROR: Unable to set registry keys. Please make sure ' +
                  'this command prompt is being run with administrator privileges.\n',
                  error);
      throw(error);
   }
}

/*
 *-----------------------------------------------------------------------------
 * setRegKeysForVvcAPITests:
 *
 *    This function edits reg keys that configure paths for the
 *    plugins needed to run the Vvc API tests
 *
 *-----------------------------------------------------------------------------
 */

function setRegKeysForVvcAPITests(opts, branding) {
   if (process.platform != 'win32') {
      return;
   }

   const dirname = require('path').dirname;
   const resolve = require('path').resolve;

   let rootRegKeyLocation = '[HKEY_LOCAL_MACHINE\\SOFTWARE\\Omnissa\\Horizon\\';
   let vvcAPITestClientRegKeyLocation = rootRegKeyLocation + 'Client\\vvc\\Plugins\\vvcClientTest]';
   let vvcAPITestServerRegKeyLocation = rootRegKeyLocation + 'Blast\\vvc\\Plugins\\vvcServerTest]';

   if (opts.branding == 'old') {
      rootRegKeyLocation = '[HKEY_LOCAL_MACHINE\\SOFTWARE\\VMware, Inc.\\';
      vvcAPITestClientRegKeyLocation = rootRegKeyLocation + 'VMware VDM\\Client\\vvc\\Plugins\\vvcClientTest]';
      vvcAPITestServerRegKeyLocation = rootRegKeyLocation + 'VMware Blast\\vvc\\Plugins\\vvcServerTest]';
   }

   // Configure paths for Vvc API test plugins
   let vvcAPITestPath = opts.vvcAPITestPath;
   if (!fs.existsSync(vvcAPITestPath)) {
      vvcAPITestPath = path.join('../../test/x64', 'vvcServerTestTrigger');
   }

   if (opts.branding == 'old') {
      let stableBenevDirectory = dirname(opts.benevPeerPath);
      vvcAPITestPath = path.join(stableBenevDirectory,
                                 '../../../test/x64/vvcServerTestTrigger');
   }

   let vvcAPITestDirectory = dirname(resolve(vvcAPITestPath));
   vvcAPITestDirectory = vvcAPITestDirectory.split('\\').join('\\\\');

   let vvcAPITestFilename = '\"filename\"=\"';

   let vvcAPITestRegKeys;
   
   if (opts.benevMode == "client") {
      vvcAPITestRegKeys = vvcAPITestClientRegKeyLocation + os.EOL +
                          vvcAPITestFilename + vvcAPITestDirectory +
                          '\\\\vvcClientTestPlugin.dll\"';
   } else if (opts.doOutOfProc === false) { // in-proc server
      /*
       * Registry key [HKLM\SOFTWARE\Omnissa\Horizon\Blast\vvc\Plugins\vvcServerTest]
       * is used to save the path of vvcServerTestPlugin.dll in "filename(REG_SZ)"
       * for BenevPeer.exe to load in in-proc mode.
       * For out-of-proc mode this registry key cannot be present because
       * vvcServerTestPlugin.dll needs to be loaded by vvcServerTestApp.exe instead
       * of BenevPeer.exe; otherwise the test will always run in-proc mode.
       * See https://omnissa.atlassian.net/wiki/spaces/BNDT/pages/75629296/Vvc+API+Tests
       * for details.
       */
      vvcAPITestRegKeys = vvcAPITestServerRegKeyLocation + os.EOL +
                          vvcAPITestFilename + vvcAPITestDirectory +
                          '\\\\vvcServerTestPlugin.dll\"';
   } else { // out-of-proc server
      // Delete vvcServerTestPlugin registry entry
      vvcAPITestRegKeys = vvcAPITestServerRegKeyLocation + os.EOL +
                          vvcAPITestFilename.slice(0, -1) + '-' + os.EOL + os.EOL;
      // Disable UnnamedSharedMemoryChannel in order to run out-of-proc Vvc API test
      if (opts.branding == 'old') {
         vvcAPITestRegKeys += rootRegKeyLocation + 'VMware VDM\\Node Manager]' + os.EOL +
                              '\"EnableUnnamedSharedMemoryChannel\"=\"false\"';
      } else {
         vvcAPITestRegKeys += rootRegKeyLocation + 'Node Manager]' + os.EOL +
                              '\"EnableUnnamedSharedMemoryChannel\"=\"false\"';
      }
   }

   try {
      fs.chmodSync('UnsetVvcAPITests.reg', write_access_octal_equivalent_value);
      fs.chmodSync('SetVvcAPITests.reg', write_access_octal_equivalent_value);
      // Strip '[' character from the path
      execFileSync('reg', ['export', rootRegKeyLocation.substring(1), 'UnsetVvcAPITests.reg', '/Y']);
   } catch (error) {
      console.log(`ERROR: Unable to make SetVvcAPITests.reg and UnsetVvcAPITests.reg writable`);
      throw(error);
   }

   let regHeader = 'Windows Registry Editor Version 5.00' + os.EOL + os.EOL;

   // Prepare all registry keys for Allow Channel List test.
   let allowChannelListRegKeys = setRegKeysForWindowsForAllowChannelList(
                                    opts, rootRegKeyLocation, vvcAPITestDirectory);

   try {
      let regValue = regHeader + vvcAPITestRegKeys + allowChannelListRegKeys;
      fs.writeFileSync('SetVvcAPITests.reg', regValue, 'utf8');
   } catch (error) {
      console.log('ERROR: Unable to write to SetVvcAPITestKeys.reg.');
      throw(error);
   }

   try {
      execFileSync('reg', ['import', 'SetVvcAPITests.reg']);
   } catch (error) {
      console.log('ERROR: Unable to set registry keys. Please make sure ' +
                  'this command prompt is being run with administrator privileges.');
      throw(error);
   }
}

/*
 *-----------------------------------------------------------------------------
 * unsetVvcAPITests:
 *
 *    This function edits reg keys configured to the paths of the
 *    plugins needed to run the Vvc API tests
 *    The paths to the plugins are set to null.
 *-----------------------------------------------------------------------------
 */

function unsetRegKeysForVvcAPITests() {
   if (process.platform != 'win32') {
      return;
   }

   try {
      execFile('Regedit', ['/s', 'UnsetVvcAPITests.reg']);
   } catch (error) {
      console.log(`Failed to reset the Vvc API tests reg keys`);
   }
}

module.exports = {
   primeConfigs: primeConfigs,
   clearConfigs: clearConfigs,
   writeConfigs: writeConfigs,
   setRegKeysForWindows: setRegKeysForWindows,
   setRegKeysForVvcAPITests: setRegKeysForVvcAPITests,
   unsetRegKeysForVvcAPITests: unsetRegKeysForVvcAPITests,
   setRegKeyForRawChannelType: setRegKeyForRawChannelType
};
