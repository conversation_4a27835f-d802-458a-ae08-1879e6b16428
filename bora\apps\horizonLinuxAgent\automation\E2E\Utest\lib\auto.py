# -*- coding: cp1252 -*-
# Copyright (c) Omnissa, LLC. All rights reserved.
# This product is protected by copyright and intellectual property laws in the
# United States and other countries as well as by international treaties.
# -- Omnissa Restricted

import configparser
import yaml
import os

BUILDWEB_SANDBOX_THRESHOLD = 60000000
GITHUB_BUILDNUM_THRESHOLD = 8000000000
configFile = 'auto.ini'
if 'UAUTO' in os.environ:
    configFile = os.environ['UAUTO']
print(configFile)

def GetTestFeatures():
    tests = {}
    tests['cdr'] = feature.get('cdr') == 'true'
    tests['usb'] = feature.get('usb') == 'true'
    tests['printer'] = feature.get('printer') == 'true'
    tests['clipboard'] = feature.get('clipboard') == 'true'
    tests['audioin'] = feature.get('audioin') == 'true'

    tests['sso'] = feature.get('sso') == 'true'
    tests['truesso'] = feature.get('truesso') == 'true'
    tests['smartcard'] = feature.get('smartcard') == 'true'

    tests['multiplesession'] = feature.get('multiplesession') == 'true'
    tests['nohostapp'] = feature.get('nohostapp') == 'true'

    tests['managed'] = feature.get('managed') == 'true'
    tests['vadc'] = feature.get('vadc') == 'true'
    return tests

fileExt = os.path.splitext(configFile)[-1]
print(f'file extention: {fileExt}')
if fileExt == '.ini':
    cp = configparser.ConfigParser()
    cp.optionxform = str
    cp.read(configFile)
    buildInfo = dict(cp.items('buildinfo'))
    horizonData = dict(cp.items('horizon'))
    vcenterData = dict(cp.items('vcenter'))
    agentData = dict(cp.items('agent'))
    poolData = dict(cp.items('pool'))
    toolData = dict(cp.items('tool'))
    clientData = dict(cp.items('client'))
    workflowData = dict(cp.items('workflow'))
    rmtData = dict(cp.items('rmtclients')) if cp.has_section('rmtclients') else {}
    feature = dict(cp.items('feature'))
elif fileExt in ('.yml', 'yaml'):
    cfg_value = {}
    try:
        with open(configFile, 'r') as f:
            cfg_value = yaml.safe_load(f)
    except yaml.YAMLError as exc:
        print(exc)
    raceTrackData = cfg_value.get('racetrack')
    horizonData = cfg_value.get('horizon')
    vcenterData = cfg_value.get('vcenter')
    agentData = cfg_value.get('agent')
    clientData = cfg_value.get('client')
    buildInfo = cfg_value.get('buildinfo')
    feature = cfg_value.get('feature')
    toolData = cfg_value.get('tool')
    rmtData = cfg_value.get('rmtclients', {})
    features = GetTestFeatures()

def GetConfigData():
    poolInfo = {}
    poolInfo['pool'] = poolData['pool']
    poolInfo['baseVM'] = poolData['baseVM']
    poolInfo['ssFresh'] = poolData['ssFresh']
    poolInfo['ssReady'] = poolData['ssReady']
    poolInfo['VADC'] = True if horizonData['vadc'].lower() == 'true' else False
    poolInfo['farm'] = horizonData['farm'] 
    poolInfo['branch'] = buildInfo['branch']
    poolInfo['installArg'] = agentData['installArg']
    poolInfo['user'] = agentData['user']
    poolInfo['domain'] = horizonData['domain']
    poolInfo['password'] = agentData['password']
    poolInfo['SSODesktopType'] = agentData['SSODesktopType']
    poolInfo['DEMEnable'] = agentData['DEMEnable']
    poolInfo['DEMNetworkPath'] = agentData['DEMNetworkPath']

    return poolInfo

def GetInstallArg():
    installArg = f" --product {buildInfo['product']}" + \
                 f" -B {buildInfo['branch']}" + \
                 f" --buildtype {buildInfo['buildType']}"
    if buildInfo.get('buildnum', None):
        installArg += f" -n {buildInfo['buildnum']}"

    if buildInfo.get('isSandbox') == 'true' :
        installArg += " --sandbox yes"

    if feature.get('fips') == 'true':
        installArg += " -f yes"

    if feature.get('managed') == 'true':
        installArg += " -M no"
        installArg += f" -b {horizonData['broker']}"
        installArg += f" -d {horizonData['domain']}"
        installArg += f" -u {horizonData['user']}"
        installArg += f" -p {horizonData['password']}"

    if feature.get('vadc') == 'true':
        installArg += " --vadc yes"
    if feature.get('multiplesession') == 'true':
        installArg += " --multiple-session"
    if feature.get('nothostapp') == 'true':
        installArg += " --no-hosted-app"
    if feature.get('force') == 'true':
        installArg += " --force"

    if feature.get('sso') == 'false':
        installArg += " -S no"

    if feature.get('truesso') == 'true':
        installArg += " -T yes"

    if feature.get('smartcard') == 'true':
        installArg += " -m yes"

    if feature.get('cdr') == 'false':
        installArg += " -F no"
    else:
        installArg += " -F yes"

    if feature.get('clipboard') == 'false':
        installArg += " -C no"
    else:
        installArg += " -C yes"

    if feature.get('usb') == 'true':
        installArg += " -U yes"
    else:
        installArg += " -U no"

    if feature.get('printer') == 'false':
        installArg += " -P no"
    else:
        installArg += " -P yes"

    if feature.get('audioin') == 'false':
        installArg += " -a no"
    else:
        installArg += " -a yes"

    print(installArg)
    return installArg

buildNum = buildInfo.get('buildnum', None)
product = buildInfo.get('product', 'horizonlinuxagent')
branch = buildInfo.get('branch', 'main')
buildtype = buildInfo.get('buildType', 'beta')
sendEmail = "true"
FeatureDescription = ""
isSandbox = False
buildNum = int(buildNum) if buildNum else None
print(f"build number: {buildNum}")
if buildNum:
    if buildNum < BUILDWEB_SANDBOX_THRESHOLD:
        isSandbox = False
    elif BUILDWEB_SANDBOX_THRESHOLD < buildNum < GITHUB_BUILDNUM_THRESHOLD:
        isSandbox = True
    elif buildNum >= GITHUB_BUILDNUM_THRESHOLD:
        github = True
else:
    github = buildInfo.get('github', False)
    if github and github.lower() == 'true':
        github = True

isrpm = horizonData.get('rpm', False)
if isrpm and isrpm.lower() == 'true':
    isrpm = True

isvadc = True if horizonData['vadc'].lower() == 'true' else False
#if isvadc and isvadc.lower() == 'true':
#    isvadc = True

isipv6 = horizonData.get('ipv6', False)
if isipv6 and isipv6.lower() == 'true':
    isipv6 = True

isfips = horizonData.get('fips', False)
if isfips and isfips.lower() == 'true':
    isfips = True

isphysical = horizonData.get('physical', False)
if isphysical and isphysical.lower() == 'true':
    isphysical = True

isAgentDownload = agentData.get('agentDownload', False)
if isAgentDownload and isAgentDownload.lower() == 'true':
    isAgentDownload = True

# github assistant server
ghaserver = toolData.get('ghaserver', False)
