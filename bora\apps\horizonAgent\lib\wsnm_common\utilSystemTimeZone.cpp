/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

#include "utilSystemTimeZone.h"
#include "properocommon.h"
#include <cedar/config/windows/registry_storage.h>

/*
 *-----------------------------------------------------------------------------
 *
 * SystemTimeZone::GetFunctionPointers --
 *
 *    Finds the address of function GetDynamicTimeZoneInformation
 *    and SetDynamicTimeZoneInformation from kernel32.dll
 *    Save them in pGetDTZI and pSetDTZI
 *    Log all failed cases.
 * Results:
 *    true if addresses of both function are found, false otherwise.
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

bool
SystemTimeZone::GetFunctionPointers()
{
   // address is already pulled, so return.
   if (pGetDTZI != nullptr && pSetDTZI != nullptr) {
      return true;
   }

   HMODULE hmodKernel = GetModuleHandle(m_kernel32.c_str());
   if (hmodKernel == NULL) {
      SYSMSG_FUNC(Error, L"Unable to retrieve the handle of module %s.", m_kernel32.c_str());
      return false;
   }

   *(void **)&pGetDTZI = GetProcAddress(hmodKernel, m_getDynamicTz.c_str());
   if (pGetDTZI == nullptr) {
      SYSMSG_FUNC(Error, L"Unable to get address of Fn %S.", m_getDynamicTz.c_str());
      return false;
   }

   *(void **)&pSetDTZI = GetProcAddress(hmodKernel, m_setDynamicTz.c_str());

   if (pSetDTZI == nullptr) {
      SYSMSG_FUNC(Error, L"Unable to get address of Fn %S.", m_setDynamicTz.c_str());
      return false;
   }
   // We got the address of both function pGetDTZI & pSetDTZI successfully
   return true;
}


/*
 *-----------------------------------------------------------------------------
 *
 * SystemTimeZone::FindStdTimezoneInRegistry --
 *
 *   If the timezone name do not have any  match under
 *   HKLM\Software\Microsoft\Windows NT\CurrentVersion\Time Zones\
 *
 *   Try to iterate under
 *   HKLM\Software\Microsoft\Windows NT\CurrentVersion\Time Zones\<tzName>\Std
 *   and see if there is a match
 *
 *   If reg key match is found keep it open and copy it to return parameter
 *   foundTimeZoneRegKey
 *
 *   Fix for bug 792133.
 *
 * Results:
 *    true if a match is found, false otherwise
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

bool
SystemTimeZone::FindStdTimezoneInRegistry(
   const wstr &findTimeZone, // IN , Time zone name that need to be set.
   wstr &foundTimeZone,      // OUT, Found time zone name exists under std key.
   HKEY &foundTimeZoneRegKey // OUT, Found in RegKey, Ensure to close it.
)
{
   // Open parent key that contains timezone subkeys.
   using cedar::windows::unique_regkey;
   unique_regkey hIterKey;

   foundTimeZoneRegKey = NULL;
   foundTimeZone.clear();

   auto result = RegOpenKeyEx(HKEY_LOCAL_MACHINE, m_tzRootRegKey, 0, KEY_READ, hIterKey.put());
   if (result != ERROR_SUCCESS) {
      SYSMSG_FUNC(Error, L"Unable to open registry %s.", m_tzRootRegKey);
      return false;
   }

   // Determine how may keys to iterate, and how big they are.
   DWORD keyCount = 0;
   DWORD maxKeyLen = 0;
   result = RegQueryInfoKey(hIterKey.get(), NULL, NULL, NULL, &keyCount, &maxKeyLen, NULL, NULL,
                            NULL, NULL, NULL, NULL);

   if (result != ERROR_SUCCESS) {
      SYSMSG_FUNC(Error, L"Unable to query key count and size for: %s", m_tzRootRegKey);
      return false;
   }

   LPWSTR subkeyName = (LPWSTR)malloc((maxKeyLen + 1) * sizeof(WCHAR));
   if (subkeyName == NULL) {
      SYSMSG_FUNC(Error, L"Failed to allocate key name buffer. Out of memory.");
      return false;
   }

   // Iterate through all subkeys
   for (DWORD index = 0; index < keyCount; index++) {
      DWORD subkeyNameLen = maxKeyLen + 1; // Add 1 for null terminator
      // Enumerate the subkeys one by one
      result =
         RegEnumKeyEx(hIterKey.get(), index, subkeyName, &subkeyNameLen, NULL, NULL, NULL, NULL);

      if (result != ERROR_SUCCESS) {
         SYSMSG_FUNC(Warn, L"Unable to enumerate subkey number %d for: %s.", index, m_tzRootRegKey);
         continue;
      }

      // Read the reg key
      unique_regkey hSubKey = NULL;
      wstr tzSubRegkey = m_tzRootRegKey;
      tzSubRegkey << subkeyName;

      result = RegOpenKeyEx(HKEY_LOCAL_MACHINE, tzSubRegkey, 0, KEY_READ, hSubKey.put());
      if (result != ERROR_SUCCESS) {
         SYSMSG_FUNC(Warn, L"Unable to read timezone subkey: %s", tzSubRegkey);
         continue;
      }

      // Read the "Std" value
      DWORD type;
      wchar_t stdname[1024];
      DWORD stdsize = sizeof(stdname);
      result = RegQueryValueEx(hSubKey.get(), m_tzStdSubKey, 0, &type, (LPBYTE)stdname, &stdsize);
      if (result != ERROR_SUCCESS || type != REG_SZ) {
         SYSMSG_FUNC(Warn, L"Unable to read timezone subkey \"Std\" value for: %s", subkeyName);
         continue;
      }

      // Does this Std value match the one we're looking for?
      if (findTimeZone.comparei(stdname) == 0) {
         SYSMSG_FUNC(Debug, L"Found matching standard timezone: %s", stdname);
         foundTimeZone = stdname;
         // Releases the ownership, callee function will read this.
         foundTimeZoneRegKey = hSubKey.release();
         break;
      }
   }
   free(subkeyName);
   return !foundTimeZone.empty();
}


/*
 *-----------------------------------------------------------------------------
 *
 * SystemTimeZone::FindTimeZoneInRegistry --
 *
 *   Find a match of "tzTimeZone" under the registry key
 *   HKLM\Software\Microsoft\Windows NT\CurrentVersion\Time Zones\
 *
 *   if found "tzTimeZone" is supported by the OS.
 *   Find key "tzinfo" and "dltname" from the above reg key.
 *   If tzinfo is not found, we can't support "tzTimeZone"
 *   If dltname is not found ignore.
 *
 * Results:
 *    true if a tzTimeZone match found and tzinfo subkey is present.
 *    false otherwise.
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

bool
SystemTimeZone::FindTimeZoneInRegistry(
   const wstr &tzTimeZone, // IN  Search for tzTimeZone in specified regPath
   wstr &foundTimeZone,    // OUT Matching foundTimeZone found.
   TZREG &tzinfo,          // OUT Filled tzinfo copied from subkey TZI
   wstr &dltName           // OUT Daylight name found under subkey Dlt
)
{
   foundTimeZone = tzTimeZone;
   wstr findKey = m_tzRootRegKey;
   findKey << tzTimeZone;

   cedar::windows::unique_regkey foundTimeZoneRegKey;
   DWORD type = 0;

   auto result1 = RegOpenKeyEx(HKEY_LOCAL_MACHINE, findKey, 0, KEY_READ, foundTimeZoneRegKey.put());

   if (result1 != ERROR_SUCCESS) {
      SYSMSG_FUNC(Warn, L"timezone %s not found, looking for any std match", tzTimeZone);

      if (FindStdTimezoneInRegistry(tzTimeZone, foundTimeZone, *foundTimeZoneRegKey.put()) ==
          false) {
         SYSMSG_FUNC(Error, L"No match found for std for %s in registry.", tzTimeZone);
         return false;
      }
   }

   if (!foundTimeZoneRegKey) {
      SYSMSG_FUNC(Error, L"foundTimeZoneRegKey is NULL.");
      return false;
   }

   SYSMSG_FUNC(Debug, L"Key %s found in registry.", findKey);
   DWORD tzsize = sizeof(tzinfo);
   auto result2 =
      RegQueryValueEx(foundTimeZoneRegKey.get(), m_tziSubKey, 0, &type, (LPBYTE)&tzinfo, &tzsize);
   if (result2 != ERROR_SUCCESS || type != REG_BINARY || tzsize != sizeof(tzinfo)) {
      SYSMSG_FUNC(Warn, L"Timezone TZI not available in registry for %s.", findKey);
      return false;
   }

   wchar_t dltnameKey[128];
   tzsize = sizeof(dltnameKey);
   auto result3 = RegQueryValueEx(foundTimeZoneRegKey.get(), m_dltSubKey, 0, &type,
                                  (LPBYTE)dltnameKey, &tzsize);

   if (result3 != ERROR_SUCCESS || type != REG_SZ) {
      dltnameKey[0] = 0;
   }
   dltName.assign(dltnameKey);

   SYSMSG_FUNC(Debug, L"DltName found = %s.", dltName);
   // Done with all searching, close the registry key.
   return true;
}


/*
 *-----------------------------------------------------------------------------
 *
 * SystemTimeZone::GetCurrentTimeZone --
 *
 *   Test in case we can get the current time zone using the api
 *   pGetDTZI or GetTimeZoneInformation.
 *   If succeed fill the out parameter.
 *
 *   Output Params tzApiUsed
 *   API_USED_TZNONE if unable to get timezone.
 *   API_USED_GETDYNAMICTZINFO if GetDynamicTimeZoneInformation api succeed
 *   API_USED_GETTZINFOif GetTimeZoneInformation api succeed
 * Results:
 *    true if able to find out current time zone
 *    false otherwise.
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

bool
SystemTimeZone::GetCurrentTimeZone(DYNAMIC_TIME_ZONE_INFORMATION *dynamicTimeZone, // OUT
                                   TIME_ZONE_INFORMATION *timeZone,                // OUT
                                   TZAPI &tzApiUsed, // OUT - rvalue of api after setting timezone
                                   wstr &errorMsg    // OUT, Error message to be returned
)
{
   tzApiUsed = API_USED_TZNONE;
   if (GetFunctionPointers()) {
      DWORD result = pGetDTZI(dynamicTimeZone);
      if (result == TIME_ZONE_ID_INVALID) {
         // pGetDTZI Api failed
         SYSMSG_FUNC(Error,
                     L"GetDynamicTimeZoneInformation failed, "
                     L"error = %s",
                     wstr::formatError());
         errorMsg = L"GetDynamicTimeZoneInformation failed.";
      } else {
         tzApiUsed = API_USED_GETDYNAMICTZINFO;
         SYSMSG_FUNC(Debug,
                     L"Retrieved current timezone =%s "
                     L"with API GetDynamicTimeZoneInformation",
                     dynamicTimeZone->StandardName);
         return true;
      }
   }

   DWORD result = GetTimeZoneInformation(timeZone);
   if (result == TIME_ZONE_ID_INVALID) {
      SYSMSG_FUNC(Error, L"GetTimeZoneInformation failed, error = %s", wstr::formatError());
      errorMsg = L"GetTimeZoneInformation failed.";
      return false;
   } else {
      tzApiUsed = API_USED_GETTZINFO;
      SYSMSG_FUNC(Debug,
                  L"Retrieved current timezone = %s "
                  L"with API GetTimeZoneInformation",
                  timeZone->StandardName);
      errorMsg = L"";
      return true;
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * SystemTimeZone::RestoreTimeZone--
 *
 *   Restore the time zone based upon the input sent.
 *   call GetDynamicTimeZoneInformation if possible
 *   SetTimeZoneInfoApi otherwise.
 *
 * Results:
 *    true if able to restore the time zone.
 *    false otherwise.
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

bool
SystemTimeZone::RestoreTimeZone(
   const TZAPI tzApiUsed,
   const DYNAMIC_TIME_ZONE_INFORMATION &dynamicTimeZone, // IN, Existing timezone
   const TIME_ZONE_INFORMATION &timeZone                 // IN, Existing timezone
)
{
   bool result = false;
   switch (tzApiUsed) {
   case API_USED_TZNONE:
      SYSMSG_FUNC(Warn, L"Timezone is invalid, can't restore.");
      break;
   case API_USED_GETDYNAMICTZINFO:
      result = CallSetDynamicTimeZoneInfoApi(dynamicTimeZone);
      break;
   case API_USED_GETTZINFO:
      result = CallSetTimeZoneInfoApi(timeZone);
      break;
   default:
      SYSMSG_FUNC(Warn, L"Timezone is invalid, can't restore.");
   }
   return result;
}


/*
 *-----------------------------------------------------------------------------
 *
 * SystemTimeZone::CallSetDynamicTimeZoneInfoApi--
 *
 *   call SetDynamicTimeZoneInformation API with given input
 *   to set the current time zone.
 *
 * Results:
 *    true if able to set the time zone.
 *    false otherwise.
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

bool
SystemTimeZone::CallSetDynamicTimeZoneInfoApi(
   DYNAMIC_TIME_ZONE_INFORMATION timeZone // IN - Timezone to be set
)
{
   if (!GetFunctionPointers()) {
      return false;
   }

   if (!pSetDTZI(&timeZone)) {
      SYSMSG_FUNC(Error, L"SetDynamicTimeZoneInformation Api failed, error = %s",
                  wstr::formatError());
      return false;
   }
   SYSMSG_FUNC(Debug, L"SetDynamicTimeZoneInformation succeeded, Timezone set to %s",
               timeZone.StandardName);
   return true;
}


/*
 *-----------------------------------------------------------------------------
 *
 * SystemTimeZone::CallSetTimeZoneInfo--
 *
 *   call SetTimeZoneInformation API with given input
 *   to set the current time zone.
 *
 * Results:
 *    true if able to set the time zone.
 *    false otherwise.
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

bool
SystemTimeZone::CallSetTimeZoneInfoApi(const TIME_ZONE_INFORMATION timeZone // IN Timezone to be set
)
{
   if (!SetTimeZoneInformation(&timeZone)) {
      SYSMSG_FUNC(Error, L"SetTimeZoneInformation api failed, error = %s", wstr::formatError());
      return false;
   }
   SYSMSG_FUNC(Debug, L"SetTimeZoneInformation Api Succeeded");
   return true;
}


/*
 *-----------------------------------------------------------------------------
 *
 * SystemTimeZone::CallSetDynamicTimeZoneInfo --
 *
 *    Take the input parameters and push them to
 *    DYNAMIC_TIME_ZONE_INFORMATION structure.
 *    Call CallSetDynamicTimeZoneInfo function to set time zone after that.
 *
 * Results:
 *    true if able to set the time zone.
 *    false otherwise.
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

bool
SystemTimeZone::CallSetDynamicTimeZoneInfo(
   const wstr &tzTimeZone,                    // IN Find this and below params from registry.
   const wstr &foundTimeZone,                 // IN
   const TZREG &tzinfo,                       // IN
   const bool &tzDynamicDaylightTimeDisabled, // IN
   const wstr &dltName                        // IN
)
{
   if (!GetFunctionPointers()) {
      return false;
   }

   DYNAMIC_TIME_ZONE_INFORMATION clientTimeZone;
   clientTimeZone.Bias = tzinfo.Bias;
   clientTimeZone.StandardBias = tzinfo.StandardBias;
   clientTimeZone.DaylightBias = tzinfo.DaylightBias;
   clientTimeZone.DaylightDate = tzinfo.DaylightDate;
   clientTimeZone.StandardDate = tzinfo.StandardDate;

   wcsncpy_s(clientTimeZone.StandardName, NUM_ITEMS(clientTimeZone.StandardName), tzTimeZone,
             _TRUNCATE);

   wcsncpy_s(clientTimeZone.TimeZoneKeyName, NUM_ITEMS(clientTimeZone.TimeZoneKeyName),
             foundTimeZone, _TRUNCATE);

   wcsncpy_s(clientTimeZone.DaylightName, NUM_ITEMS(clientTimeZone.DaylightName), dltName,
             _TRUNCATE);

   SYSMSG_FUNC(Debug, L"StandardName = %s, TimeZoneKeyName = %s, DaylightName = %s",
               clientTimeZone.StandardName, clientTimeZone.TimeZoneKeyName,
               clientTimeZone.DaylightName);

   clientTimeZone.DynamicDaylightTimeDisabled = tzDynamicDaylightTimeDisabled;
   if (clientTimeZone.DynamicDaylightTimeDisabled == TRUE) {
      SYSMSG_FUNC(Trace, L"Set StandardDate & DaylightDate \
         to NULL as DaylightSaving is disabled.")
      memset(&(clientTimeZone.StandardDate), 0, sizeof(SYSTEMTIME));
      memset(&(clientTimeZone.DaylightDate), 0, sizeof(SYSTEMTIME));
   }
   return CallSetDynamicTimeZoneInfoApi(clientTimeZone);
}


/*
 *-----------------------------------------------------------------------------
 *
 * SystemTimeZone::CallSetTimeZoneInfo --
 *
 *    Take the input parameters and push them to
 *    TIME_ZONE_INFORMATION structure.
 *    Call CallSetTimeZoneInfo function to set time zone after that.
 *
 * Results:
 *    true if able to set the time zone.
 *    false otherwise.
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

bool
SystemTimeZone::CallSetTimeZoneInfo(
   const wstr &tzTimeZone,                    // IN  TimeZone Name
   const TZREG &tzinfo,                       // IN, Matching tzinfo found in the registry
   const bool &tzDynamicDaylightTimeDisabled, // IN Daylightsaving
   const wstr &dltName                        // IN Day light saving name.
)
{
   TIME_ZONE_INFORMATION timeZoneInfo = {0};
   if (GetTimeZoneInformation(&timeZoneInfo) == TIME_ZONE_ID_INVALID) {
      SYSMSG_FUNC(Error, L"GetTimeZoneInformation api failed, error = %s", wstr::formatError());
      return false;
   }

   TIME_ZONE_INFORMATION tmZone = {0};
   tmZone.Bias = tzinfo.Bias;
   tmZone.StandardBias = tzinfo.StandardBias;
   wcscpy_s(tmZone.StandardName, tzTimeZone.c_str());
   wcscpy_s(tmZone.DaylightName, dltName.c_str());
   tmZone.DaylightBias = tzinfo.DaylightBias;
   tmZone.DaylightDate = tzinfo.DaylightDate;
   tmZone.StandardDate = tzinfo.StandardDate;
   return CallSetTimeZoneInfoApi(tmZone);
}


/*
 *-----------------------------------------------------------------------------
 *
 * SystemTimeZone::GetTzOffset --
 *
 *    If we have only the timezoneOffset to set the time
 *    first parse the time zone.
 *    The offset string can have sample string as "hh:mm" or "-hh:mm"
 *
 *    Parse the string
 *      if format is wrong return false
 *      else convert the offset to minutes and save the value in tzbias param.
 *
 *    Now we will use the minutes as a bias, means on UTC.
 *    For example if input is "01:20", it's 80 minutes
 *       We will set current time as UTC time + 80 minutes.
 *    Similarly
         "-1:30" =  UTC - 90 minutes
 *        "2:00" =  UTC + 120 minutes
 *        "-0:10" = UTC - 10 minutes
 *    etc
 *    Fom the offset check in case timezone needs to be updated.
 * Results:
 *    true if input is valid and time needs to be updated
 *    false otherwise.
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

bool
SystemTimeZone::GetTzOffset(const wstr &timezoneOffset, // IN
                            LONG &tzbias // OUT parse the input and convert it to minutes.
)
{
   wstr tzOffset = timezoneOffset;
   tzOffset.trim();
   bool isNegative = (tzOffset.find(L'-') != -1);
   if (isNegative) {
      tzOffset = tzOffset.substr(1);
   }
   int tzOffsetHr = 0;
   int tzOffsetMin = 0;
   vwstr parts = tzOffset.split(L':');
   if (parts.size() == 2) {
      tzOffsetHr = _wtoi(parts[0].c_str());
      tzOffsetMin = _wtoi(parts[1].c_str());
      SYSMSG_FUNC(Debug, L"Time offset of client from GMT. Hour = %d minute = %d", tzOffsetHr,
                  tzOffsetMin);
   } else {
      SYSMSG_FUNC(Warn, L"Invalid input %s for tzOffset.", tzOffset);
      return false;
   }

   LONG timeOffsetMin = tzOffsetHr * 60 + tzOffsetMin;
   if (isNegative) {
      timeOffsetMin = -1 * timeOffsetMin;
   }

   if (timeOffsetMin == 0) {
      SYSMSG_FUNC(Warn, L"Invalid input, timeOffsetMin = 0.");
      return false;
   }

   /*
    * MS Bias used in TimezoneInformation is -ve of
    * time offset from GMT in minutes
    */
   LONG clientBias = -timeOffsetMin;
   SYSMSG_FUNC(Debug, L"The client bias is = %d", clientBias);

   TIME_ZONE_INFORMATION currentTimeZone = {0};
   DWORD tzInfo = GetTimeZoneInformation(&currentTimeZone);
   if (tzInfo == TIME_ZONE_ID_INVALID) {
      SYSMSG_FUNC(Error, L"GetTimeZoneInformation failed, error = %s", wstr::formatError());
      return false;
   }

   bool tzUpdateNeeded = (currentTimeZone.Bias != clientBias);
   if (tzUpdateNeeded) {
      SYSMSG_FUNC(Debug, L"Time need to be updated, currentbias = %d", currentTimeZone.Bias);
      tzbias = clientBias;
      return true;
   }
   SYSMSG_FUNC(Debug, L"No need to update the time, with offset %s", tzOffset);
   return false;
}


/*
 *-----------------------------------------------------------------------------
 *
 * SystemTimeZone::SetTimeFromStandardName --
 *
 *    We have tzTimeZoneName and
 *    Find the details like tzInfo and Dlt from registry using tzTimeZoneName
 *    Set the current time zone by calling respective api
 *
 * Results:
 *    true if input is valid and able to set the time zone.
 *    false otherwise.
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

bool
SystemTimeZone::SetTimeFromStandardName(const wstr &tzTimeZoneName,   // IN
                                        const bool &bDayLightDisabled // IN
)
{
   if (tzTimeZoneName.empty()) {
      SYSMSG_FUNC(Info, L"tzTimeZone is empty.");
      return false;
   }

   SYSMSG_FUNC(Debug, L"tzTimeZoneName=%s bDayLightDisabled=%d", tzTimeZoneName, bDayLightDisabled);

   TZREG tzinfo = {0};
   wstr dltName;
   wstr foundTimeZone;

   bool success = FindTimeZoneInRegistry(tzTimeZoneName, foundTimeZone, tzinfo, dltName);

   if (!success) {
      return false;
   }

   if (GetFunctionPointers() && CallSetDynamicTimeZoneInfo(tzTimeZoneName, foundTimeZone, tzinfo,
                                                           bDayLightDisabled, dltName)) {
      return true;
   }
   return CallSetTimeZoneInfo(tzTimeZoneName, tzinfo, bDayLightDisabled, dltName);
}


/*
 *-----------------------------------------------------------------------------
 *
 * SystemTimeZone::SetTimeFromOffset --
 *
 *    Set the current time zone as UTC (+/-) tzTimezoneOffset
 *    If the format of offset is correct convert tzTimezoneOffset to mintues
 *    Consider other params as 0 or NULL.
 *    Set the current time zone.
 *
 * Results:
 *    true if input is valid and able to set the time zone.
 *    false otherwise.
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

bool
SystemTimeZone::SetTimeFromOffset(const wstr &tzTimezoneOffset // IN
)
{
   SYSMSG_FUNC(Debug, L"tzTimezoneOffset=%s", tzTimezoneOffset);
   if (tzTimezoneOffset.empty()) {
      SYSMSG_FUNC(Warn, L"timezoneOffset is empty.");
      return false;
   }

   // We have only offset, apply it to UTC timezone.
   LONG tzBias = 0;
   bool succ = GetTzOffset(tzTimezoneOffset, tzBias);
   if (!succ) {
      return false;
   }
   TZREG tzinfo = {0};
   tzinfo.Bias = tzBias;

   // Pass blank DltName and bDayLightDisabled as false for timezoneOffset
   wstr emptyDltName = L"";
   // Value for bDayLightDisabled is false when we are using SetTimeFromOffset
   const bool bDayLightDisabled = false;
   wstr tzTimeZone = L"";
   wstr tzFoundTimeZone = L"";

   bool tzSet = false;
   if (GetFunctionPointers() && CallSetDynamicTimeZoneInfo(tzTimeZone, tzFoundTimeZone, tzinfo,
                                                           bDayLightDisabled, emptyDltName)) {
      return true;
   }

   return CallSetTimeZoneInfo(tzTimeZone, tzinfo, bDayLightDisabled, emptyDltName);
}


/*
 *-----------------------------------------------------------------------------
 *
 * SystemTimeZone::SetTimeZone --
 *
 *    Wrapper function to call SetTimeFromStandardName or SetTimeFromOffset
 *    If tzTimeZoneName is none-empty call SetTimeFromStandardName
 *    if tzTimezoneOffset is none-empty call SetTimeFromOffset
 *    Log error otherwise
 *    Note : tzTimeZoneName and tzTimezoneOffset can't be used at same time.
 *
 * Results:
 *    true if input is valid and able to set the time zone.
 *    false otherwise.
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

bool
SystemTimeZone::SetTimeZone(const wstr &tzTimeZoneName,    // IN
                            const bool &bDayLightDisabled, // IN
                            const wstr &tzTimezoneOffset   // IN
)
{
   SYSMSG_FUNC(Debug, L"tzTimeZoneName = %s, bDayLightDisabled = %d, tzTimezoneOffset=%s",
               tzTimeZoneName, bDayLightDisabled, tzTimezoneOffset);

   if (!tzTimeZoneName.empty()) {
      return SetTimeFromStandardName(tzTimeZoneName, bDayLightDisabled);
   } else if (!tzTimezoneOffset.empty()) {
      return SetTimeFromOffset(tzTimezoneOffset);
   }
   SYSMSG_FUNC(Warn, L"tzTimeZoneName & tzTimezoneOffset are empty");
   return false;
}
