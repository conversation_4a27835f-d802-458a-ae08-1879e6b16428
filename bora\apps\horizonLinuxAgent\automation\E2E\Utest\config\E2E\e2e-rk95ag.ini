[buildinfo]
product = horizonlinuxagent
branch = main
buildType = beta
buildnum = 
testtype = 
github = true
description = Rocky Linux 9.5

[vcenter]
address = a1-vcorigami-l01.omnissa.com
admin = "<EMAIL>"
password = "47bhZcP7B=5q#6'#hEZ"

[horizon]
broker = linuxcartcs1.linuxcart.com
brokerPassword = "H0r1z0nA93nt@"
user = Administrator
password = "H0r1z0nA93nt@"
domain = linuxcart
farm = 
pool = e2e-rk95ag
namingpattern = e2e-rk95ag
uagEnabled = false
uaguser = admin
uagpwd = P@ssw0rd
uag = *******
uagport = 1234
vadc = False

[pool]
pool = e2e-rk95ag
baseVM = e2e-rk95ag
ssFresh = ReadyToInstallOb
CustomSpecName = lxd
ssReady = InstantClone

[agent]
user = linuxcart1
rootpassword = H0r1z0n@
password = H0r1z0n@
baseVM = e2e-rk95ag
SSODesktopType = "UseGnomeClassic"
agentBuild = 
installArg = -U yes --webcam no
ipv6 = false
agentIP = ***********

[client]
wincdrfolder = c:\CDR
recordSndIdx = 1

[feature]
cdr = true
printer = false
clipboard = false
usb = false
sso = true

[tool]
loadBrokerPSModule = true
sikulipath = C:\automation\sikuliX1.11
remoteclient = localhost
shadowclient = localhost
remoterunning = false
email = <EMAIL> , <EMAIL>
batrunning = false
ghaserver = 

[workflow]

