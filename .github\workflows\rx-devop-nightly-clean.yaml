name: R<PERSON>-<PERSON><PERSON>-Nightly-Runner-Cleanup

on:
  schedule:
    - cron: 15 14 * * 1-5
  workflow_dispatch:
    inputs:
      runnerGroup:
        type: choice
        description: Runner Group
        required: True
        options:
          - BUB_U22
          - BUB_W10_NG
          - BAV_W10_NG
          - BAV_W10_G
          - BIT_W10_G
          - DSP_W10_NG
          - DSP_W10_G
          - DSP_W11_NG
          - DSP_W11_G

env:
  BUB_U22: 8
  BUB_W10_NG: 8
  BAV_W10_NG: 6
  BAV_W10_G: 4
  BIT_W10_G: 8
  DSP_W10_NG: 4
  DSP_W10_G: 4
  DSP_W11_NG: 5
  DSP_W11_G: 4


jobs:

  create-runner-list:
    if: ${{ vars.ENABLE_UNIT_TEST_HOSTED_IN_MV == 'true' }}

    outputs:
      dispatch_run_list: ${{ steps.build_dispatch_run_list.outputs.runner_list }}
      nightly_run_list: ${{ steps.build_nightly_run_list.outputs.runner_list }}

    runs-on:
      - self-hosted
      - vdub-run-helper

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          sparse-checkout: .github

      - name: Set Dispatch Runner Matrix
        id: build_dispatch_run_list
        if: github.event_name == 'workflow_dispatch'
        shell: bash
        run : >
          python3 .github/actions/testframework/common/reboot_runner_list.py
          --runner_groups ${{ inputs.runnerGroup }}
  
      - name: Set Nightly Runner Matrix
        id: build_nightly_run_list
        if: github.event_name == 'schedule'
        shell: bash
        run: >
          python3 .github/actions/testframework/common/reboot_runner_list.py 
          --runner_groups BAV_W10_G,BIT_W10_G,DSP_W10_NG,DSP_W11_NG,DSP_W10_G,DSP_W11_G



  clean-runners:
    needs: create-runner-list
    strategy:
      fail-fast: false
      matrix:
        runner-list: ${{ github.event_name == 'schedule' &&
                          fromJSON(needs.create-runner-list.outputs.nightly_run_list) ||
                          fromJSON(needs.create-runner-list.outputs.dispatch_run_list) }}
    runs-on:
      - self-hosted
      - ${{ matrix.runner-list }}

    steps:
      - name: Cleaning Windows directories
        if: runner.os == 'Windows'
        uses: euc-eng/sh-runner-utility/clean-directory@v1
        with:
          globList: 'c:\ProgramData\VMware\**\*.log,c:\actions-runner\_diag\*.log'
          olderThanDays: "3"

      - name: Cleaning Linux directories
        if: runner.os == 'Linux'
        uses: euc-eng/sh-runner-utility/clean-directory@v1
        with:
          globList: "'/home/<USER>/actions-runner/**/*.log'"
          olderThanDays: "3"
