/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */
package com.omnissa.vdi.orchestratorj.keyvault;

import java.math.BigInteger;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.UnrecoverableKeyException;
import java.security.cert.Certificate;
import java.security.cert.X509Certificate;
import java.util.List;
import java.util.Map;

import javax.crypto.SecretKey;
import javax.security.auth.x500.X500Principal;

import com.omnissa.vdi.commonutils.Signature;
import com.omnissa.vdi.orchestratorj.keyvault.KeyVaultTransform.HashAlgorithmName;

/**
 * Java interface for KeyVault. Where a bare name is not enough, a KeyName
 * object is used. If only a name is needed we simply expect a String.
 */
public interface KeyVault {
    /**
     * Most KeyVault objects exist in the default namespace but there are other
     * namespaces for specific purposes.
     *
     * SHARED - This namespace comprises KeyVault objects owned by other
     * processes but shared with this process. This interface allows certain
     * operations to be performed using shared objects, but the namespace itself
     * can be modified only by native code.
     *
     * COLLECTIONS - This namespace allows related KeyVault objects to be
     * jointly administered. Currently, it comprises only managed collections
     * (that is, pools of automatically generated operational keys).
     *
     * TODO: This interface allows no interaction with collections at present.
     */
    public enum NameSpace {
        SHARED, COLLECTIONS;
    }

    /**
     * Selection mode for disambiguating multiple valid certificates.
     *
     * FirstValid - The first valid certificate will be returned. (Current and
     * default behaviour.)
     *
     * EarliestStart - The certificate with the earliest start date will be
     * returned.
     *
     * LatestExpiry - The certificate with the latest expiration date will be
     * returned.
     */
    public enum ChainSelectionMode {
        FirstValid, EarliestStart, LatestExpiry
    }

    /**
     * Algorithm for hashing.
     */
    public enum HashAlgorithm {
        SHA1, SHA224, SHA256, SHA384, SHA512;
    }

    /**
     * Certificate file formats to be imported
     */
    public enum CertificateFileFormat {
        pem, pfx;
    }

    /**
     * Generate a new operational context of the given length, with the given
     * name. If an operational context with this name already exists, increment
     * the generation number.
     *
     * An operational context is used in conjunction with a master key to
     * generate a non-persistent operational key of the same length. Operational
     * contexts are always persisted.
     *
     * @param length
     *            Key length in bits
     * @param name
     *            Name to use
     * @return the generation number of the generated operational context.
     * @throws KeyVaultException
     *             if length is invalid or a problem occurs.
     * @see #addMaster(String name)
     * @see #deriveKey(String name, KeyName master, KeyName context)
     */
    int addContext(int length, String name) throws KeyVaultException;

    /**
     * Generate a new symmetric operational key of the given length, with the
     * given name. If an operational key with this name already exists,
     * increment the generation number. The operational key will not be
     * persisted.
     *
     * @param length
     *            Key length in bits
     * @param name
     *            Name to add the key as
     * @return the generation number of the generated operational key.
     * @throws KeyVaultException
     *             if length is invalid or a problem occurs.
     */
    int addKey(int length, String name) throws KeyVaultException;

    /**
     * Generate a new symmetric operational key of the given length, with the
     * given name. If an operational key with this name already exists,
     * increment the generation number. The operational key will be persisted,
     * protected by the latest generation of the named master key.
     *
     * @param length
     *            Key length in bits
     * @param name
     *            Name to add the key as
     * @param master
     *            Name of the master key for key persistence
     * @return the generation number of the generated operational key.
     * @throws KeyVaultException
     *             if length is invalid, or if master cannot be found in the
     *             default namespace, or if a problem occurs.
     */
    int addKey(int length, String name, String master) throws KeyVaultException;

    /**
     * Generate a new operational keypair of the given length, with the given
     * name. If an operational keypair with this name already exists, increment
     * the generation number. The operational keypair will not be persisted.
     *
     * @param length
     *            Key length in bits
     * @param name
     *            Name to add the keypair as
     *
     * @return the generation number of the generated operational keypair.
     * @throws KeyVaultException
     *             if length is invalid or a problem occurs.
     */
    int addKeyPair(int length, String name) throws KeyVaultException;

    /**
     * Generate a new operational keypair of the given length, with the given
     * name. If an operational keypair with this name already exists, increment
     * the generation number. The operational keypair will be persisted,
     * protected by the latest generation of the named master key.
     *
     * @param length
     *            Key length in bits
     * @param name
     *            Name to add the keypair as
     * @param master
     *            Name of the master key for keypair persistence
     *
     * @return the generation number of the generated operational keypair.
     * @throws KeyVaultException
     *             if length is invalid, or if master cannot be found in the
     *             default namespace, or if a problem occurs.
     */
    int addKeyPair(int length, String name, String master)
            throws KeyVaultException;

    /**
     * Generate a new master key with the given name. If a master key with this
     * name already exists, increment the generation number. Master keys are
     * always persisted.
     *
     * @param name
     *            name of the master key
     * @return the generation number of the generated master key.
     * @throws KeyVaultException
     *             if a problem occurs.
     */
    int addMaster(String name) throws KeyVaultException;

    /**
     * Create a self-signed certificate with the given subject/issuer name and
     * lifetime in days. The certificate will be added to the specified Windows
     * certificate store under the given friendly name. The associated RSA
     * keypair will be generated with the specified length and will be marked
     * exportable. Specify whether to overwrite any existing certificate with
     * the same friendly name.
     *
     * @param name
     *            subject name for the new certificate
     * @param signatureAlgorithm
     *            signature algorithm
     * @param keyLength
     *            length of key in bits (valid values depend on signature
     *            algorithm)
     * @param lifetime
     *            number of days before expiry
     * @param friendlyName
     *            name of certificate entry in the certificate store
     * @param storeName
     *            name of the certificate store (default is "MY")
     * @param overwriteExisting
     *            true if an existing certificate can be silently replaced
     * @return the generated certificate.
     * @throws KeyVaultException
     *             if name or friendyName is null, or if signatureAlgorithm or
     *             keyLength is invalid, or if a problem occurs.
     */
    Certificate addSelfSignedCertificate(String name,
            Signature.Algorithm signatureAlgorithm, int keyLength, int lifetime,
            String friendlyName, String storeName, boolean overwriteExisting)
            throws KeyVaultException;

    /**
     * Remove any invalid (date-range) certificates with the given name in the
     * given store.
     *
     * Extended validation is skipped.
     *
     * @param friendlyName
     *            name of certificate entry in the certificate store
     * @param storeName
     *            name of the certificate store (default is "MY")
     * @throws KeyVaultException
     *             if friendlyName is null or a problem occurs.
     */
    void purgeInvalidCertificates(String friendlyName, String storeName)
            throws KeyVaultException;

    /**
     * Remove any invalid (date-range) certificates with the given name in the
     * given store.
     *
     * Extended validation is skipped.
     *
     * @param friendlyName
     *            name of certificate entry in the certificate store
     * @param storeName
     *            name of the certificate store (default is "MY")
     * @param advanceDays
     *            number of days to auto-renew the certificate prior to its
     *            expiry.
     * @throws KeyVaultException
     *             if friendlyName is null or a problem occurs.
     */
    void purgeInvalidCertificates(String friendlyName, String storeName,
            int advanceDays) throws KeyVaultException;

    /**
     * Remove any invalid (date-range) certificates with the given name in the
     * given store.
     *
     * Extended validation is enabled.
     *
     * @param friendlyName
     *            name of certificate entry in the certificate store
     * @param storeName
     *            name of the certificate store (default is "MY")
     * @param hostName
     *            The expected host name, for server certificate extended
     *            validation. Null means client certificate extended validation.
     * @param intendedUse
     *            An array of EKU OIDs that the certificate must have (if empty,
     *            all valid certificates are suitable)
     * @param advanceDays
     *            number of days to auto-renew the certificate prior to its
     *            expiry.
     * @throws KeyVaultException
     *             if friendlyName is null or a problem occurs.
     */
    void purgeInvalidCertificates(String friendlyName, String storeName,
            String hostName, String[] intendedUse, int advanceDays)
            throws KeyVaultException;

    /**
     * Remove any invalid (date-range) certificates with the given name in the
     * given store.
     *
     * Extended validation is enabled.
     *
     * @param friendlyName
     *            name of certificate entry in the certificate store
     * @param storeName
     *            name of the certificate store (default is "MY")
     * @param hostName
     *            The expected host name, for server certificate extended
     *            validation. Null means client certificate extended validation.
     * @param intendedUse
     *            An array of EKU OIDs that the certificate must have (if empty,
     *            all valid certificates are suitable)
     * @param advanceDays
     *            number of days to auto-renew the certificate prior to its
     *            expiry.
     * @param acceptSelfSignedCerts
     *            Accept self-signed certificate when noManagedCertificate is
     *            set
     * @throws KeyVaultException
     *             if friendlyName is null or a problem occurs.
     */
    void purgeInvalidCertificates(String friendlyName, String storeName,
            String hostName, String[] intendedUse, int advanceDays,
            boolean acceptSelfSignedCerts) throws KeyVaultException;

    /**
     * Clear the KeyVault, removing all items.
     *
     * @return the number of items removed.
     * @throws KeyVaultException
     *             if a problem occurs.
     */
    int clear() throws KeyVaultException;

    /**
     * Decipher the supplied ciphertext using the specified operational key (not
     * keypair) and default cipher parameters.
     *
     * Default cipher parameters are AES/CBC/NoPadding, iv=00, salt=00.
     *
     * @param cipherKey
     *            Specifier of the operational key to use
     * @param data
     *            The ciphertext
     *
     * @return the plaintext
     * @throws KeyVaultException
     *             if cipherKey cannot be found in the default namespace or a
     *             problem occurs.
     */
    byte[] decipher(KeyName cipherKey, byte[] data) throws KeyVaultException;

    /**
     * Decipher the supplied ciphertext using the specified operational key or
     * keypair, and the given transform. If an operational keypair is specified,
     * the private half of the keypair is used to decipher the data.
     *
     * @param cipherKey
     *            Specifier of the operational key or keypair to use
     * @param data
     *            The ciphertext
     * @param transform
     *            The cipher parameters
     * @return the plaintext
     * @throws KeyVaultException
     *             if cipherKey cannot be found in the default namespace, or the
     *             transform is incompatible, or if a problem occurs.
     */
    byte[] decipher(KeyName cipherKey, byte[] data, KeyVaultTransform transform)
            throws KeyVaultException;

    /**
     * Decipher the supplied ciphertext using a PBKDF2 derivation of the
     * specified master key, using default cipher parameters and the given salt.
     *
     * Default cipher parameters are AES/CBC/NoPadding, iv=00, salt=00.
     *
     * @param master
     *            Specifier of the master key to use
     * @param hashalg
     *            The hash algorithm
     * @param iterations
     *            The number of rounds
     * @param salt
     *            The salt
     * @param data
     *            The ciphertext
     *
     * @return the plaintext
     * @throws KeyVaultException
     *             if the master key cannot be found, or salt is longer than the
     *             master key, or if a problem occurs.
     */
    byte[] decipherWithDerivedKey(KeyName master, HashAlgorithm hashalg,
            int iterations, byte[] salt, byte[] data) throws KeyVaultException;

    /**
     * Decipher the supplied ciphertext using a PBKDF2 derivation of the
     * specified master key, using the given transform and salt.
     *
     * @param master
     *            Specifier of the master key to use
     * @param hashalg
     *            The hash algorithm
     * @param iterations
     *            The number of rounds
     * @param salt
     *            The salt
     * @param data
     *            The ciphertext
     * @param transform
     *            The cipher parameters
     *
     * @return the plaintext
     * @throws KeyVaultException
     *             if the master key cannot be found, or salt is longer than the
     *             master key, or if a problem occurs.
     */
    byte[] decipherWithDerivedKey(KeyName master, HashAlgorithm hashalg,
            int iterations, byte[] salt, byte[] data,
            KeyVaultTransform transform) throws KeyVaultException;

    /**
     * Derive a symmetric operational key from the specified master key and
     * operational context, with the given name. If an operational key with this
     * name already exists, increment the generation number. The length of the
     * derived key matches the length of the specified operational context. The
     * operational key will not be persisted.
     *
     * @param name
     *            Name to add the key as
     * @param master
     *            Specifier of the master key to use
     * @param context
     *            Specifier of the operational context to use
     *
     * @return the generation number of the derived operational key.
     * @throws KeyVaultException
     *             if the specified master key or operational context does not
     *             exist, or if a problem occurs.
     * @see #addMaster(String name)
     * @see #addContext(int length, String name)
     */
    int deriveKey(String name, KeyName master, KeyName context)
            throws KeyVaultException;

    /**
     * Encipher the supplied plaintext using the latest generation of the named
     * operational key (not keypair) and default cipher parameters.
     *
     * Default cipher parameters are AES/CBC/NoPadding, iv=00, salt=00.
     *
     * @param cipherKey
     *            The operational key to use
     * @param data
     *            The plaintext
     *
     * @return the ciphertext
     * @throws KeyVaultException
     *             if cipherKey cannot be found in the default namespace, or if
     *             a problem occurs.
     */
    byte[] encipher(String cipherKey, byte[] data) throws KeyVaultException;

    /**
     * Encipher the supplied plaintext using the latest generation of the named
     * operational key or keypair, and the given transform. If an operational
     * keypair is named, the public half of the keypair is used to encipher the
     * data.
     *
     * @param cipherKey
     *            The operational key or keypair to use
     * @param data
     *            The plaintext
     * @param transform
     *            The cipher parameters
     *
     * @return the ciphertext
     * @throws KeyVaultException
     *             if cipherKey cannot be found in the default namespace, or the
     *             transform is incompatible, or if a problem occurs.
     */
    byte[] encipher(String cipherKey, byte[] data, KeyVaultTransform transform)
            throws KeyVaultException;

    /**
     * Encipher the supplied plaintext using a PBKDF2 derivation of the
     * specified master key, using default cipher parameters and a random salt.
     *
     * Default cipher parameters are AES/CBC/NoPadding, iv=00, salt=00.
     *
     * @param master
     *            Specifier of the master key to use
     * @param hashalg
     *            The hash algorithm
     * @param iterations
     *            The number of rounds
     * @param salt
     *            The salt (populated on return)
     * @param data
     *            The plaintext
     *
     * @return the ciphertext
     * @throws KeyVaultException
     *             if the master key cannot be found, or salt is longer than the
     *             master key, or if a problem occurs.
     */
    byte[] encipherWithDerivedKey(KeyName master, HashAlgorithm hashalg,
            int iterations, byte[] salt, byte[] data) throws KeyVaultException;

    /**
     * Encipher the supplied plaintext using a PBKDF2 derivation of the
     * specified master key, using the given transform and a random salt.
     *
     * @param master
     *            Specifier of the master key to use
     * @param hashalg
     *            The hash algorithm
     * @param iterations
     *            The number of rounds
     * @param salt
     *            The salt (populated on return)
     * @param data
     *            The plaintext
     * @param transform
     *            The cipher parameters
     *
     * @return the ciphertext
     * @throws KeyVaultException
     *             if the master key cannot be found, or salt is longer than the
     *             master key, or if a problem occurs.
     */
    byte[] encipherWithDerivedKey(KeyName master, HashAlgorithm hashalg,
            int iterations, byte[] salt, byte[] data,
            KeyVaultTransform transform) throws KeyVaultException;

    /**
     * @param keyname
     *            The name of the key
     * @return true if the specified KeyVault item exists.
     * @throws KeyVaultException
     *             if a problem occurs.
     */
    boolean exists(KeyName keyname) throws KeyVaultException;

    /**
     * Generate an opaque blob containing certificates and private keys from the
     * given store with the given friendly name. The blob is enciphered using a
     * PBKDF2 derivation of the specified master key, using a random salt and
     * transform.
     *
     * All certificate uses are suitable. Extended validation is skipped.
     *
     * @param friendlyName
     *            The certificate entry description to match (case insensitive)
     * @param storeName
     *            The name of the certificate container in the local Machine
     *            store
     * @param master
     *            Specifier of the master key from which an operational key will
     *            be derived
     * @param hashalg
     *            The hash algorithm
     * @param iterations
     *            The number of rounds
     *
     * @return the blob
     * @throws KeyVaultException
     *             if master cannot be found or a problem occurs.
     */
    byte[] exportCertificates(String friendlyName, String storeName,
            KeyName master, HashAlgorithm hashalg, int iterations)
            throws KeyVaultException;

    /**
     * Generate an opaque blob containing certificates and private keys from the
     * given store with the given friendly name. The blob is enciphered using a
     * PBKDF2 derivation of the specified master key, using a random salt and
     * transform.
     *
     * Extended validation is enabled.
     *
     * @param friendlyName
     *            The certificate entry description to match (case insensitive)
     * @param storeName
     *            The name of the certificate container in the local Machine
     *            store
     * @param hostName
     *            The expected host name, for server certificate extended
     *            validation. Null means client certificate extended validation.
     * @param intendedUse
     *            An array of EKU OIDs that the certificate must have (if empty,
     *            all valid certificates are suitable)
     * @param master
     *            Specifier of the master key from which an operational key will
     *            be derived
     * @param hashalg
     *            The hash algorithm
     * @param iterations
     *            The number of rounds
     *
     * @return the blob
     * @throws KeyVaultException
     *             if master cannot be found or a problem occurs.
     */
    byte[] exportCertificates(String friendlyName, String storeName,
            String hostName, String[] intendedUse, KeyName master,
            HashAlgorithm hashalg, int iterations) throws KeyVaultException;

    /**
     * Close the KeyVault context and release resources.
     *
     * @throws KeyVaultException
     *             if a problem occurs.
     */
    void finish() throws KeyVaultException;

    /**
     * Generate a CSR request and installs the private key in keystore.
     *
     * @param subjectDN
     *            The subject Name used for CSR generation.
     * @param keySize
     *            length of key in bits (valid values depend on signature
     *            algorithm)
     * @param lifeTime
     *            number of days before expiry
     * @param signatureAlgorithm
     *            signature algorithm
     * @param subjectAltNames
     *            Subject Alternative names (mandatory if subject name is
     *            wildcard)
     * @param storeName
     *            name of the certificate store (default is "MY")
     * @return the csr request
     *
     * @throws KeyVaultException
     *             if private key cannot be generated and store orCSR can be
     *             generated.
     */
    byte[] generatePKCS10(String subjectDN, int keySize, int lifeTime,
            Signature.Algorithm signatureAlgorithm,
            List<String> subjectAltNames, String storeName)
            throws KeyVaultException;

    /**
     * Get the valid certificate chain with the longest lifetime in the machine
     * personal certificate store with the given friendly name. If there are no
     * valid certificates, but an expired certificate exists with the given
     * friendly name, then return that certificate chain.
     *
     * All certificate uses are suitable. Extended validation is skipped.
     *
     * @param friendlyName
     *            the friendly name of the certificate to retrieve
     * @return the certificate chain if found.
     * @throws KeyVaultException
     *             if no qualifying certificates exist or a problem occurs.
     */
    Certificate[] getCertificateChain(String friendlyName)
            throws KeyVaultException;

    /**
     * Get the valid certificate chain with the longest lifetime in the
     * specified store with the given friendly name. If there are no valid
     * certificates, but an expired certificate exists with the given friendly
     * name, then return that certificate chain.
     *
     * All certificate uses are suitable. Extended validation is skipped.
     *
     * @param friendlyName
     *            The friendly name of the certificate to retrieve
     * @param storeName
     *            The name of the certificate store
     * @return the certificate chain if found.
     * @throws KeyVaultException
     *             if no qualifying certificates exist or a problem occurs.
     */
    Certificate[] getCertificateChain(String friendlyName, String storeName)
            throws KeyVaultException;

    /**
     * Get the valid certificate chain with the longest lifetime in the
     * specified store with the given friendly name. If there are no valid
     * certificates, but an expired certificate exists with the given friendly
     * name, then return that certificate chain.
     *
     * All certificate uses are suitable. Extended validation is skipped.
     *
     * @param friendlyName
     *            The friendly name of the certificate to retrieve
     * @param storeName
     *            The name of the certificate store
     * @param acceptSelfSignedCerts
     *            Accept self-signed certs when noManagedCert flag is set
     * @return the certificate chain if found.
     * @throws KeyVaultException
     *             if no qualifying certificates exist or a problem occurs.
     */
    Certificate[] getCertificateChain(String friendlyName, String storeName,
            boolean acceptSelfSignedCerts) throws KeyVaultException;

    /**
     * Get the valid certificate chain with the specified selection mode in the
     * specified store with the given friendly name. If there are no valid
     * certificates, but an expired certificate exists with the given friendly
     * name, then return that certificate chain.
     *
     * All certificate uses are suitable. Extended validation is skipped.
     *
     * @param friendlyName
     *            The friendly name of the certificate
     * @param storeName
     *            The name of the certificate store
     * @param mode
     *            Defines which of several valid certificates should be
     *            returned. Default: FirstValid.
     * @return the certificate chain if found.
     * @throws KeyVaultException
     *             if no qualifying certificates exist or a problem occurs.
     */
    Certificate[] getCertificateChain(String friendlyName, String storeName,
            ChainSelectionMode mode) throws KeyVaultException;

    /**
     * Get the valid certificate chain with the specified selection mode in the
     * specified store with the given friendly name. If there are no valid
     * certificates, but an expired certificate exists with the given friendly
     * name, then return that certificate chain.
     *
     * Extended validation is enabled.
     *
     * @param friendlyName
     *            The friendly name of the certificate
     * @param storeName
     *            The name of the certificate store
     * @param hostName
     *            The expected host name, for server certificate extended
     *            validation. Null means client certificate extended validation.
     * @param intendedUse
     *            An array of EKU OIDs that the certificate must have (if empty,
     *            all valid certificates are suitable)
     * @param mode
     *            Defines which of several valid certificates should be
     *            returned. Default: FirstValid.
     * @return the certificate chain if found.
     * @throws KeyVaultException
     *             if no qualifying certificates exist or a problem occurs.
     */
    Certificate[] getCertificateChain(String friendlyName, String storeName,
            String hostName, String[] intendedUse, ChainSelectionMode mode)
            throws KeyVaultException;

    /**
     * Get the valid certificate chain with the specified selection mode in the
     * specified store with the given friendly name. If there are no valid
     * certificates, but an expired certificate exists with the given friendly
     * name, then return that certificate chain.
     *
     * Extended validation is enabled.
     *
     * @param friendlyName
     *            The friendly name of the certificate
     * @param storeName
     *            The name of the certificate store
     * @param hostName
     *            The expected host name, for server certificate extended
     *            validation. Null means client certificate extended validation.
     * @param intendedUse
     *            An array of EKU OIDs that the certificate must have (if empty,
     *            all valid certificates are suitable)
     * @param mode
     *            Defines which of several valid certificates should be
     *            returned. Default: FirstValid.
     * @param acceptSelfSignedCerts
     *            Accept self-signed certificate when noManagedCertificate is
     *            set
     * @return the certificate chain if found.
     * @throws KeyVaultException
     *             if no qualifying certificates exist or a problem occurs.
     */
    Certificate[] getCertificateChain(String friendlyName, String storeName,
            String hostName, String[] intendedUse, ChainSelectionMode mode,
            boolean acceptSelfSignedCerts) throws KeyVaultException;

    /**
     * Get all valid certificates in the machine personal certificate store with
     * the given friendly name.
     *
     * All certificate uses are suitable. Extended validation is skipped.
     *
     * @param friendlyName
     *            The friendly name of the certificates
     * @return the certificates if found, empty if none found.
     * @throws KeyVaultException
     *             if a problem occurs.
     */
    Certificate[] getEndEntityCertificates(String friendlyName)
            throws KeyVaultException;

    /**
     * Get all valid certificates in the specified store with the given friendly
     * name.
     *
     * All certificate uses are suitable. Extended validation is skipped.
     *
     * @param friendlyName
     *            The friendly name of the certificates
     * @param storeName
     *            The name of the certificate store
     * @return the certificates if found, empty if none found.
     * @throws KeyVaultException
     *             if a problem occurs.
     */
    Certificate[] getEndEntityCertificates(String friendlyName,
            String storeName) throws KeyVaultException;

    /**
     * Get all valid certificates in the specified store with the given friendly
     * name.
     *
     * Extended validation is enabled.
     *
     * @param friendlyName
     *            The friendly name of the certificates
     * @param storeName
     *            The name of the certificate store
     * @param hostName
     *            The expected host name, for server certificate extended
     *            validation. Null means client certificate extended validation.
     * @param intendedUse
     *            An array of EKU OIDs that the certificate must have (if empty,
     *            all valid certificates are suitable)
     * @return the certificates if found, empty if none found.
     * @throws KeyVaultException
     *             if a problem occurs.
     */
    Certificate[] getEndEntityCertificates(String friendlyName,
            String storeName, String hostName, String[] intendedUse)
            throws KeyVaultException;

    /**
     * Get all valid certificates in the specified store with the given friendly
     * name.
     *
     * Extended validation is enabled.
     *
     * @param friendlyName
     *            The friendly name of the certificates
     * @param storeName
     *            The name of the certificate store
     * @param hostName
     *            The expected host name, for server certificate extended
     *            validation. Null means client certificate extended validation.
     * @param intendedUse
     *            An array of EKU OIDs that the certificate must have (if empty,
     *            all valid certificates are suitable)
     * @param acceptSelfSignedCerts
     *            Accept self-signed certificate when noManagedCertificate is
     *            set
     * @return the certificates if found, empty if none found.
     * @throws KeyVaultException
     *             if a problem occurs.
     */
    Certificate[] getEndEntityCertificates(String friendlyName,
            String storeName, String hostName, String[] intendedUse,
            boolean acceptSelfSignedCerts) throws KeyVaultException;

    /**
     * Get all valid certificates in the specified store with the given friendly
     * name.
     *
     * Extended validation is enabled.
     *
     * @param friendlyName
     *            The friendly name of the certificates
     * @param storeName
     *            The name of the certificate store
     * @param hostName
     *            The expected host name, for server certificate extended
     *            validation. Null means client certificate extended validation.
     * @param intendedUse
     *            An array of EKU OIDs that the certificate must have (if empty,
     *            all valid certificates are suitable)
     * @param acceptSelfSignedCerts
     *            Accept self-signed certificate when noManagedCertificate is
     *            set
     * @param includeExpiredCerts
     *            Flag to include expired certs
     * @return the certificates if found, empty if none found.
     * @throws KeyVaultException
     *             if a problem occurs.
     */
    Certificate[] getEndEntityCertificates(String friendlyName,
            String storeName, String hostName, String[] intendedUse,
            boolean acceptSelfSignedCerts, boolean includeExpiredCerts)
            throws KeyVaultException;

    /**
     * Count the certificates in the machine personal certificate store. A
     * certificate qualifies if it has the given friendly name and an associated
     * private key. It is valid if it has also not expired.
     *
     * All certificate uses are suitable.
     *
     * @param friendlyName
     *            The friendly name of the certifactes
     * @return the total number of certificates, the number that are valid, and
     *         the number that would be valid if only they hadn't expired.
     * @throws KeyVaultException
     *             if a problem occurs.
     */
    CertificateStoreCounts getCertificateCounts(String friendlyName)
            throws KeyVaultException;

    /**
     * Count the certificates in the specified store. A certificate qualifies if
     * it has the given friendly name and an associated private key. It is valid
     * if it has also not expired.
     *
     * All certificate uses are suitable. Extended validation is skipped.
     *
     * @param friendlyName
     *            The friendly name of the certificates
     * @param storeName
     *            The name of the certificate store
     * @return the total number of certificates, the number that are valid, and
     *         the number that would be valid if only they hadn't expired.
     * @throws KeyVaultException
     *             if a problem occurs.
     */
    CertificateStoreCounts getCertificateCounts(String friendlyName,
            String storeName) throws KeyVaultException;

    /**
     * Count the certificates in the specified store. A certificate qualifies if
     * it has the given friendly name and an associated private key. It is valid
     * if it has also not expired.
     *
     * Extended validation is enabled.
     *
     * @param friendlyName
     *            The friendly name of the certificates
     * @param storeName
     *            The name of the certificate store
     * @param hostName
     *            The expected host name, for server certificate extended
     *            validation. Null means client certificate extended validation.
     * @param intendedUse
     *            An array of EKU OIDs that the certificate must have (if empty,
     *            all qualifying certificates are suitable)
     * @return the total number of certificates, the number that are valid, and
     *         the number that would be valid if only they hadn't expired.
     * @throws KeyVaultException
     *             if a problem occurs.
     */
    CertificateStoreCounts getCertificateCounts(String friendlyName,
            String storeName, String hostName, String[] intendedUse)
            throws KeyVaultException;

    /**
     * Get the hostname associated with the provided certificate in the machine
     * personal certificate store.
     *
     * @param certificate
     *            The certificate
     *
     * @return the private key if found.
     * @throws KeyVaultException
     *             if the certificate cannot be matched or a problem occurs.
     */
    String getCertificateHost(X509Certificate certificate)
            throws KeyVaultException;

    /**
     * Get the hostname associated with the provided certificate in a specific
     * store.
     *
     * If the certificate contains more than one SAN, the first is returned.
     *
     * @param certificate
     *            The certificate
     * @param storeName
     *            The name of the certificate store
     *
     * @return the hostname.
     * @throws KeyVaultException
     *             if the certificate cannot be matched or a problem occurs.
     */
    String getCertificateHost(X509Certificate certificate, String storeName)
            throws KeyVaultException;

    /**
     * Get the hostname associated with the provided certificate in a specific
     * store.
     *
     * @param certificate
     *            The certificate
     * @param storeName
     *            The name of the certificate store
     * @param sanIndex
     *            If the certificate contains more than one SAN, choose one by
     *            supplying a positive or negative (counting backwards) value
     *            (default 0)
     *
     * @return the hostname.
     * @throws KeyVaultException
     *             if the certificate cannot be matched or a problem occurs.
     */
    String getCertificateHost(X509Certificate certificate, String storeName,
            int sanIndex) throws KeyVaultException;

    /**
     * Get the private key associated with the certificate identified by the
     * issuer/serial combination in a specific store.
     *
     * This method variant is DEPRECATED in favour of the provided-certificate
     * variant. The problem with this variant is that Java and Windows may not
     * produce the same issuer string, and hence the private key may not be
     * found.
     *
     * @param issuer
     *            The issuer of the certificate
     * @param serial
     *            The serial number
     * @param storeName
     *            The name of the certificate store
     * @return the private key if found.
     * @throws KeyVaultException
     *             if no qualifying certificates exist or a problem occurs.
     * @throws NoSuchAlgorithmException
     *             The algorithm specified is not supported
     * @throws UnrecoverableKeyException
     *             The key cannot be retrieved
     */
    @Deprecated
    PrivateKey getCertificateKey(X500Principal issuer, BigInteger serial,
            String storeName) throws KeyVaultException,
            NoSuchAlgorithmException, UnrecoverableKeyException;

    /**
     * Get the private key associated with the provided certificate in the
     * machine personal certificate store.
     *
     * @param certificate
     *            The certificate
     * @return the private key if found.
     * @throws KeyVaultException
     *             if the certificate cannot be matched or a problem occurs.
     * @throws NoSuchAlgorithmException
     *             if the private key cannot be translated from Windows.
     * @throws UnrecoverableKeyException
     *             if the private key is not accessible.
     */
    PrivateKey getCertificateKey(X509Certificate certificate)
            throws KeyVaultException, NoSuchAlgorithmException,
            UnrecoverableKeyException;

    /**
     * Get the private key associated with the provided certificate in a
     * specific store.
     *
     * @param certificate
     *            The certificate
     * @param storeName
     *            The name of the certificate store
     * @return the private key if found.
     * @throws KeyVaultException
     *             if the certificate cannot be matched or a problem occurs.
     * @throws NoSuchAlgorithmException
     *             if the private key cannot be translated from Windows.
     * @throws UnrecoverableKeyException
     *             if the private key is not accessible.
     */
    PrivateKey getCertificateKey(X509Certificate certificate, String storeName)
            throws KeyVaultException, NoSuchAlgorithmException,
            UnrecoverableKeyException;

    /**
     * Get the private key associated with the provided certificate in a
     * specific store.
     *
     * @param certificate
     *            The certificate
     * @param storeName
     *            The name of the certificate store
     * @param acceptSelfSignedCerts
     *            Accept self-signed certificate in case of noManagedCert is set
     * @return the private key if found.
     * @throws KeyVaultException
     *             if the certificate cannot be matched or a problem occurs.
     * @throws NoSuchAlgorithmException
     *             if the private key cannot be translated from Windows.
     * @throws UnrecoverableKeyException
     *             if the private key is not accessible.
     */
    PrivateKey getCertificateKey(X509Certificate certificate, String storeName,
            boolean acceptSelfSignedCerts) throws KeyVaultException,
            NoSuchAlgorithmException, UnrecoverableKeyException;

    /**
     * Compute a CRC32 checksum of the given byte array.
     *
     * @param data
     *            the data for which the checksum is required.
     * @return the checksum.
     * @throws KeyVaultException
     *             if the checksum cannot be computed.
     */
    long getCRC32(byte[] data) throws KeyVaultException;

    /**
     * Get an application data entry with the specified name.
     *
     * @param name
     *            the name of the data entry.
     * @return the data if found.
     * @throws KeyVaultException
     *             if the data entry cannot be found in the default namespace,
     *             or the data cannot be deciphered, or a problem occurs.
     */
    byte[] getData(KeyName name) throws KeyVaultException;

    /**
     * Create a multiBlob containing the specified operational key or keypair.
     * The multiBlob is protected by the latest generation of the named
     * cipherKey, and the given transform.
     *
     * @param name
     *            the name of the key or keypair.
     * @param cipherKey
     *            specifier of the cipher key to use
     * @param transform
     *            the cipher transform parameters
     * @return the blob
     * @throws KeyVaultException
     *             If the key cannot be found, or another error occurs
     */
    byte[] getKey(String name, KeyName cipherKey, KeyVaultTransform transform)
            throws KeyVaultException;

    /**
     * Get the public half of a specified keypair, including proof of possession
     * of the private key.
     *
     * CAUTION: This will not work with synthetic keypairs, which do not have a
     * private key. Use getPublicKey instead.
     *
     * @param pkp
     *            key specification (modified on success to contain key and
     *            proof).
     * @throws KeyVaultException
     *             if the keypair cannot be found in the default namespace, or a
     *             problem occurs.
     * @throws NoSuchAlgorithmException
     *             if the keypair type is not handled.
     */
    void getPublic(PublicKeyWithProof pkp)
            throws KeyVaultException, NoSuchAlgorithmException;

    /**
     * Get the public half of a specified keypair.
     *
     * @param name
     *            the name of the keypair.
     * @return the public key if found.
     * @throws KeyVaultException
     *             if the keypair cannot be found in the default namespace, or a
     *             problem occurs.
     * @throws NoSuchAlgorithmException
     *             if the keypair type is not handled.
     */
    PublicKey getPublicKey(KeyName name)
            throws KeyVaultException, NoSuchAlgorithmException;

    /**
     * Get a sequence of random bytes of the given length, using the default
     * algorithm.
     *
     * @param numBytes
     *            the number of bytes to return
     * @return the random byte array of length numBytes.
     * @throws KeyVaultException
     *             if numBytes &lt; 1 or cannot be satisfied, or if a problem
     *             occurs.
     */
    byte[] getRandom(int numBytes) throws KeyVaultException;

    /**
     * Get a sequence of random bytes of the given length, using the specified
     * algorithm.
     *
     * @param numBytes
     *            the number of bytes to return
     * @param algorithm
     *            the random number generation algorithm to use
     * @return the random byte array of length numBytes
     * @throws KeyVaultException
     *             if numBytes &lt; 1 or cannot be satisfied, or the given
     *             algorithm cannot be found, or if a problem occurs.
     */
    byte[] getRandom(int numBytes, String algorithm) throws KeyVaultException;

    /**
     * Get a sequence of random bytes using an auxiliary seed and the default
     * algorithm. The returned random bytes overwrite the auxiliary seed.
     *
     * @param seed
     *            the byte array containing the auxiliary seed
     * @throws KeyVaultException
     *             if seed length &lt; 1 or cannot be satisfied, or if a problem
     *             occurs.
     */
    void getRandom(byte[] seed) throws KeyVaultException;

    /**
     * Get a sequence of random bytes using an auxiliary seed and the specified
     * algorithm. The returned random bytes overwrite the auxiliary seed.
     *
     * @param seed
     *            the byte array containing the auxiliary seed
     * @param algorithm
     *            the random number generation algorithm to use
     * @throws KeyVaultException
     *             if seed length &lt; 1 or cannot be satisfied, or the given
     *             algorithm cannot be found, or if a problem occurs.
     */
    void getRandom(byte[] seed, String algorithm) throws KeyVaultException;

    /**
     * Seed the PRNG, using the default algorithm.
     *
     * @param seed
     *            the byte array containing the seed
     * @throws KeyVaultException
     *             if seed is null or cannot be set, or if a problem occurs.
     */
    void setRandomSeed(byte[] seed) throws KeyVaultException;

    /**
     * Seed the PRNG, using the specified algorithm.
     *
     * @param seed
     *            the byte array containing the seed
     * @param algorithm
     *            the random number generation algorithm to use
     * @throws KeyVaultException
     *             if seed is null or cannot be set, or the given algorithm
     *             cannot be found, or if a problem occurs.
     */
    void setRandomSeed(byte[] seed, String algorithm) throws KeyVaultException;

    /**
     * Process an opaque blob containing certificates and private keys.
     * Certificate entries will be created as specified in the blob. Existing
     * certificates will NOT be replaced.
     *
     * @param certificateBlob
     *            An opaque and partially enciphered blob, with metadata to
     *            describe the enciphering parameters and contents.
     *
     * @return the number of certificate entries imported (may be zero).
     * @throws KeyVaultException
     *             if a problem occurs.
     */
    int importCertificates(byte[] certificateBlob) throws KeyVaultException;

    /**
     * Process an opaque blob containing certificates and private keys.
     * Certificate entries will be created as specified in the blob.
     *
     * @param certificateBlob
     *            An opaque and partially enciphered blob, with metadata to
     *            describe the enciphering parameters and contents.
     * @param allowOverwrite
     *            If true, existing certificates will be replaced.
     *
     * @return the number of certificate entries imported (may be zero)
     * @throws KeyVaultException
     *             if a problem occurs.
     */
    int importCertificates(byte[] certificateBlob, boolean allowOverwrite)
            throws KeyVaultException;

    /**
     * Process an file image containing certificates and private keys.
     * Certificate entries will be created as specified in the file.
     *
     * @param certificateFileImage
     *            A file image(in pem or pfx format) containing certificates and
     *            private keys.
     * @param friendlyName
     *            name of certificate entry in the certificate store
     * @param storeName
     *            name of the certificate store (default is "MY")
     * @param password
     *            password for .pfx format certs
     * @param certificateFileFormat
     *            format in which certificate is passed,.pem or .pfx
     * @param hostname
     *            The expected host name, for server certificate extended
     *            validation. Null means client certificate extended validation.
     * @param intendedUse
     *            The expected intended use of the certificate
     *
     * @return count of imported certificates
     * @throws KeyVaultException
     *             if a problem occurs.
     */
    int importEncodedCertificates(byte[] certificateFileImage,
            String friendlyName, String storeName, byte[] password,
            CertificateFileFormat certificateFileFormat, String hostname,
            String[] intendedUse) throws KeyVaultException;

    /**
     * Returns a repeatable identity derived from the value of a specified
     * kvBlob and the given salt.
     *
     * @param name
     *            Name of the KeyVault key.
     * @param salt
     *            The salt.
     * @return The repeatable identity
     * @throws KeyVaultException
     *             Generic KeyVault exception
     */
    String id(KeyName name, String salt) throws KeyVaultException;

    /**
     * Return the properties of the specified KeyVault object.
     *
     * @param specifier
     *            Specifier of the KeyVault object.
     * @return the properties of the object if found.
     * @throws KeyVaultException
     *             if the object does not exist or a problem occurs.
     */
    Map<String, String> info(KeyName specifier) throws KeyVaultException;

    /**
     * Initiate the key vault
     *
     * @param rekey
     *            Whether to rekey or not
     * @return true on success
     * @throws KeyVaultException
     *             if a problem occurs.
     */
    boolean init(boolean rekey) throws KeyVaultException;

    /**
     * Return the highest generation number for the specified KeyVault object,
     * or 0 if no generations can be found.
     *
     * @param specifier
     *            Specifier of the KeyVault object (the generation element is
     *            ignored)
     * @return latest generation (0 if not found)
     * @throws KeyVaultException
     *             if a problem occurs.
     */
    int latest(KeyName specifier) throws KeyVaultException;

    /**
     * List all existing KeyVault objects matching the specified name. The name
     * element of specifier can be null to mean all KeyVault objects in the
     * specified namespace. The returned array may be empty.
     *
     * @param specifier
     *            Specifier to match (the generation element is ignored)
     * @return all matching items
     * @throws KeyVaultException
     *             if a problem occurs.
     */
    String[] list(KeyName specifier) throws KeyVaultException;

    /**
     * Set an application data entry from the supplied raw data, with the given
     * name, protected by the the latest generation of the named operational key
     * (not keypair) and default cipher parameters. If a data entry with this
     * name already exists, increment the generation number.
     *
     * Default cipher parameters are AES/CBC/NoPadding, iv=(rand), salt=(rand).
     *
     * @param data
     *            The data to be stored
     * @param name
     *            The name the data will be stored under
     * @param opKey
     *            The operational key used to encrypt the data in the store
     *
     * @return the generation number of the application data entry.
     * @throws KeyVaultException
     *             if opKey cannot be found in the default namespace, or if a
     *             problem occurs.
     */
    int putData(byte[] data, String name, String opKey)
            throws KeyVaultException;

    /**
     * Set an application data entry from the supplied raw data, with the given
     * name, protected by the latest generation of the named operational key or
     * keypair, and the given transform. If a data entry with this name already
     * exists, increment the generation number. If the iv and/or salt fields of
     * the transform are null, appropriate values will be randomly generated.
     *
     * If an operational keypair is named, the public half of the keypair is
     * used to encipher the data.
     *
     * @param data
     *            The data to be stored
     * @param name
     *            The name the data will be stored under
     * @param opKey
     *            The operational key used to encrypt the data in the store
     * @param transform
     *            The cipher parameters
     *
     * @return the generation number of the application data entry.
     * @throws KeyVaultException
     *             if opKey cannot be found in the default namespace, or the
     *             transform is incompatible, or if a problem occurs.
     */
    int putData(byte[] data, String name, String opKey,
            KeyVaultTransform transform) throws KeyVaultException;

    /**
     * Set an operational key or keypair from the supplied multiBlob.
     *
     * @param keyBlob
     *            the multiBlob
     * @param allowOverwrite,
     *            if true, existing keys will be replaced.
     * @return true on success
     * @throws KeyVaultException
     *             If some error occurs
     */
    boolean putKey(byte[] keyBlob, boolean allowOverwrite)
            throws KeyVaultException;

    /**
     * Remove from KeyVault all generations of application data that have the
     * given name.
     *
     * @param name
     *            the item name
     *
     * @return the number of items removed.
     * @throws KeyVaultException
     *             if a problem occurs.
     */
    int removeData(String name) throws KeyVaultException;

    /**
     * Remove from KeyVault all generations of keys or operational contexts that
     * have the given name.
     *
     * @param name
     *            the item name
     *
     * @return the number of items removed.
     * @throws KeyVaultException
     *             if any generation of the named item is a master key or
     *             operational context with dependent operational keys, or an
     *             operational key with dependent application data, or if a
     *             problem occurs.
     */
    int removeKey(String name) throws KeyVaultException;

    /**
     * Sign the given hash with the private half of the specified keypair. The
     * signature algorithm associated with the keypair must be specified in the
     * transform.
     *
     * @param name
     *            The keypair name to be used for signing.
     * @param data
     *            The hash (digest) to be signed. This must be the correct
     *            length for the specified keypair algorithm.
     * @param transform
     *            The cipher transform parameters.
     * @return The signature bytes.
     * @throws KeyVaultException
     *             If some error occurs
     */
    byte[] signHash(String name, byte[] data, KeyVaultTransform transform)
            throws KeyVaultException;

    /**
     * Generate a signature over the provided data using the private half of the
     * specified keypair. The signature algorithm associated with the keypair
     * must be specified in the transform.
     *
     * NOTE: the provided data will be hashed as a side-effect. If the data is
     * pre-hashed, consider signHash instead.
     *
     * @param name
     *            The name of the keypair to be used for signing.
     * @param data
     *            The raw data to be signed.
     * @param transform
     *            The cipher transform parameters.
     * @return The signature.
     * @throws KeyVaultException
     *             If the signature cannot be generated
     */
    byte[] generateSignature(String name, byte[] data,
            KeyVaultTransform transform) throws KeyVaultException;

    /**
     * Verify the given signature with the provided data and the public half of
     * the specified keypair. The signature algorithm associated with the
     * keypair must be specified in the transform.
     *
     * NOTE: the provided data will be hashed as a side-effect. Do not pre-hash
     * unless you want double hashing.
     *
     * @param name
     *            The name of the keypair to be used for verification.
     * @param data
     *            The raw data that was signed.
     * @param signature
     *            The signature.
     * @param transform
     *            The cipher transform parameters.
     * @throws KeyVaultException
     *             If the signature cannot be verified
     */
    void verifySignature(KeyName name, byte[] data, byte[] signature,
            KeyVaultTransform transform) throws KeyVaultException;

    /**
     * Remove a specific certificate from the named store.
     *
     * @deprecated This method variant is DEPRECATED in favour of the
     *             provided-certificate variant. The problem with this variant
     *             is that Java and Windows may not produce the same issuer
     *             string, and hence the private key may not be found.
     *
     * @param issuer
     *            Issuer principal.
     * @param serialNumber
     *            Serial number of the certificate.
     * @param storeName
     *            Store containing the certificate.
     * @throws KeyVaultException
     *             if a problem occurs.
     */
    @Deprecated
    void removeCertificate(X500Principal issuer, BigInteger serialNumber,
            String storeName) throws KeyVaultException;

    /**
     * Remove a specific certificate from the named store.
     *
     * @param certificate
     *            x509 certificate.
     * @param storeName
     *            Store containing the certificate.
     * @throws KeyVaultException
     *             if a problem occurs.
     */
    void removeCertificate(X509Certificate certificate, String storeName)
            throws KeyVaultException;

    /**
     * Set the friendly name of the identified certificate.
     *
     * @param issuer
     *            Issuer principal.
     * @param serialNumber
     *            Serial number of the certificate.
     * @param storeName
     *            Store containing the certificate.
     * @param newFriendlyName
     *            New friendly name.
     * @throws KeyVaultException
     *             if a problem occurs.
     */
    void setFriendlyName(X500Principal issuer, BigInteger serialNumber,
            String storeName, String newFriendlyName) throws KeyVaultException;

    /**
     * Set the friendly name of the certificate.
     *
     * @param certificate
     *            Certificate.
     * @param storeName
     *            Store containing the certificate.
     * @param newFriendlyName
     *            New friendly name.
     * @throws KeyVaultException
     *             If a problem occurs
     */
    void setFriendlyName(X509Certificate certificate, String storeName,
            String newFriendlyName) throws KeyVaultException;

    /**
     * Validate the given certificate chain.
     *
     * @param chain
     *            Certificate chain to validate.
     * @param subject
     *            Expected subject name.
     * @return The results of the validation.
     * @throws KeyVaultException
     *             if a problem occurs.
     */
    CertificateValidationResults validateCertificateChain(
            X509Certificate[] chain, String subject) throws KeyVaultException;

    /**
     * Create a synthetic key pair in KeyVault using the supplied public key.
     *
     * @param pubKey
     *            The public key to put in KeyVault.
     * @param signature
     *            The signature which is used to validate that the caller has
     *            private key.
     * @param keyName
     *            Name to add the key pair as.
     * @param dataToSign
     *            Data to sign to validate that the caller has private key.
     * @param hashAlgoName
     *            Algorithm used to sign data.
     * @return the generation of the key pair in KeyVault.
     * @throws KeyVaultException
     *             if a problem occurs.
     * @deprecated
     */
    @Deprecated
    int putPublic(byte[] pubKey, byte[] signature, String keyName,
            String dataToSign, HashAlgorithmName hashAlgoName)
            throws KeyVaultException;

    /**
     * Create a synthetic key pair in KeyVault using the supplied public key.
     *
     * @param pkp
     *            The public key to put in KeyVault, with proof.
     * @return the generation of the synthetic key pair.
     * @throws KeyVaultException
     *             if a problem occurs.
     */
    int putPublic(PublicKeyWithProof pkp) throws KeyVaultException;

    /**
     * Create a synthetic key pair in KeyVault using the supplied public key.
     *
     * CAUTION: This method should be used only if pubKey can be trusted. If you
     * don't have this assurance, use putPublic instead.
     *
     * @param pubKey
     *            The public key to put in KeyVault (trusted).
     * @param keyName
     *            Name to add the key pair as.
     * @return the generation of the key pair in KeyVault.
     * @throws KeyVaultException
     *             if a problem occurs.
     */
    int putPublicKey(byte[] pubKey, String keyName) throws KeyVaultException;

    /**
     * Wrap the supplied symmetric key with the specified operational keypair.
     *
     * @param key
     *            The key to wrap
     * @param wrappingKey
     *            The operational keypair to wrap it with
     *
     * @return the wrapped key
     * @throws KeyVaultException
     *             if wrappingKey cannot be found, or if a problem occurs.
     */
    byte[] wrapKey(SecretKey key, KeyName wrappingKey) throws KeyVaultException;

    /**
     * Unwrap the supplied wrapped key with the specified operational keypair.
     *
     * @param wrappedKey
     *            The key to unwrap
     * @param wrappingKey
     *            The operational keypair to unwrap it with
     *
     * @return the unwrapped key
     * @throws KeyVaultException
     *             if wrappingKey cannot be found, or if a problem occurs.
     */
    SecretKey unwrapKey(byte[] wrappedKey, KeyName wrappingKey)
            throws KeyVaultException;
}
