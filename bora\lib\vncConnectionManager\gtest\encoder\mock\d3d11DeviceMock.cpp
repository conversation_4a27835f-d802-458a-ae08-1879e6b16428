/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

#include "d3d11DeviceMock.h"
#include "d3d11DeviceContextMock.h"
#include "d3d11TextureMock.h"
#include <iostream>

class MockD3D11Texture2D;

MockD3D11Device::MockD3D11Device()
{
   // Constructor
}

ULONG
MockD3D11Device::AddRef()
{
   return ++m_refCount;
}

ULONG
MockD3D11Device::Release()
{
   if (--m_refCount == 0) {
      delete this;
      return 0;
   }
   return m_refCount;
}

HRESULT
MockD3D11Device::QueryInterface(REFIID riid, void **ppvObject)
{
   if (riid == __uuidof(ID3D11Device) || riid == __uuidof(IUnknown)) {
      *ppvObject = static_cast<ID3D11Device *>(this);
      AddRef();
      return S_OK;
   }
   *ppvObject = nullptr;
   return E_NOINTERFACE;
}

void
MockD3D11Device::GetImmediateContext(ID3D11DeviceContext **ppContext)
{
   if (ppContext) {
      *ppContext = new MockD3D11DeviceContext(this);
   }
}

HRESULT
MockD3D11Device::CreateTexture2D(const D3D11_TEXTURE2D_DESC *pDesc,
                                 const D3D11_SUBRESOURCE_DATA *pInitialData,
                                 ID3D11Texture2D **ppTexture2D)
{

   if (ppTexture2D) {
      *ppTexture2D = new MockD3D11Texture2D(this, pDesc, pInitialData);
      return S_OK;
   }
   return E_INVALIDARG;
}