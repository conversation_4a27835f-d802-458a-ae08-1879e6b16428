/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

#include "stdafx.h"

using namespace svmga::core::util;

Registry::Registry() :
   _svc<PERSON><PERSON>(NULL),
   _ga<PERSON><PERSON>(NULL),
   _nga<PERSON>ey(NULL),
   _gaSupport<PERSON>ey(NULL),
   _extKey(NULL),
   _sm<PERSON><PERSON>(NULL),
   _nl<PERSON>ey(NULL),
   _ai<PERSON>ey(NULL),
   _sysprepKey(NULL),
   _av<PERSON>ey(NULL),
   _lsaCMNKey(NULL),
   _profileList<PERSON>ey(NULL),
   _guestInfo<PERSON>ey(NULL),
   _setupKey(NULL),
   _systemSetupKey(NULL),
   _agentConfigKey(NULL),
   _nodeManagerKey(NULL),
   _horizonAgentKey(NULL)
{}

HRESULT
Registry::<PERSON><PERSON><PERSON><PERSON>(HKEY hkParent, std::wstring strKeyPath)
{
   boost::shared_ptr<reg::NewRegKey> regKey;

   regKey = boost::shared_ptr<reg::NewRegKey>(
      new reg::NewRegKey(hkParent, strKeyPath, REG_OPTION_NON_VOLATILE, KEY_ACCESS_MASK));

   return S_OK;
}

reg::ExistingRegKey *
Registry::GetKey(HKEY hkParent, std::wstring strPath, DWORD dwMask)
{
   HRESULT hr = S_OK;
   reg::ExistingRegKey *theKey = NULL;

   theKey = new reg::ExistingRegKey(hkParent, strPath, dwMask, hr);
   if (theKey != NULL && SUCCEEDED(hr)) {
      return theKey;
   }

   hr = CreateKey(hkParent, strPath);
   if (FAILED(hr)) {
      SYSMSG_FUNC(Error, L"Failed Creating Key: %ws, 0x%X", strPath.c_str(), hr);
      return theKey;
   }

   theKey = new reg::ExistingRegKey(hkParent, strPath, dwMask, hr);
   if (FAILED(hr)) {
      SYSMSG_FUNC(Error, L"Failed Opening Key: %ws, 0x%X", strPath.c_str(), hr);
   }

   return theKey;
}

reg::ExistingRegKey *
Registry::GetKeyObj(RegType Type)
{
   switch (Type) {
   case RegType::Service:
      if (_svcKey == NULL) {
         _svcKey = GetKey(HKLM, HIC_GA_SERVICE_KEYPATH);
      }
      return _svcKey;

   case RegType::Ga:
      if (_gaKey == NULL) {
         _gaKey = GetKey(HKLM, HIC_GA_KEY_PATH);
      }
      return _gaKey;

   case RegType::Support:
      if (_gaSupportKey == NULL) {
         _gaSupportKey = GetKey(HKLM, HIC_SUPPORT_KEY_PATH);
      }
      return _gaSupportKey;

   case RegType::Nga:
      if (_ngaKey == NULL) {
         _ngaKey = GetKey(HKLM, HIC_NGA_KEY_PATH);
      }
      return _ngaKey;

   case RegType::External:
      if (_extKey == NULL) {
         _extKey = GetKey(HKLM, HIC_EXTERNAL_KEY_PATH);
      }
      return _extKey;

   case RegType::SessionMgr:
      if (_smKey == NULL) {
         _smKey = GetKey(HKLM, SVM_SESSION_MANAGER_KEYPATH);
      }
      return _smKey;

   case RegType::Netlogon:
      if (_nlKey == NULL) {
         _nlKey = GetKey(HKLM, NETLOGON_PARAMETERS_KEYPATH);
      }
      return _nlKey;

   case RegType::AgentIntegration:
      if (_aiKey == NULL) {
         _aiKey = GetKey(HKLM, HIC_AGENT_INTEGRATION_KEY_PATH);
      }
      return _aiKey;

   case RegType::Sysprep:
      if (_sysprepKey == NULL) {
         _sysprepKey = GetKey(HKLM, SYSPREP_STATE_KEYPATH, KEY_ACCESS_MASK_SYSPREP);
      }
      return _sysprepKey;

   case RegType::AV:
      if (_avKey == NULL) {
         _avKey = GetKey(HKLM, HIC_AGENT_INTEGRATION_AV_KEY_PATH);
      }
      return _avKey;

   case RegType::CachedMachines:
      if (_lsaCMNKey == NULL) {
         _lsaCMNKey = GetKey(HKLM, SVM_NGA_CACHED_MACHINE_NAMES_REGKEY);
      }
      return _lsaCMNKey;

   case RegType::ProfileList:
      if (_profileListKey == NULL) {
         _profileListKey = GetKey(HKLM, PROFILE_LIST_KEY);
      }
      return _profileListKey;

   case RegType::GuestInfo:
      if (_guestInfoKey == NULL) {
         _guestInfoKey = GetKey(HKLM, HIC_GUEST_INFO_KEY_PATH);
      }
      return _guestInfoKey;

   case RegType::Setup:
      if (_setupKey == NULL) {
         _setupKey = GetKey(HKLM, HIC_SETUP_KEY_PATH);
      }
      return _setupKey;

   case RegType::SystemSetup:
      if (_systemSetupKey == NULL) {
         _systemSetupKey = GetKey(HKLM, SYSTEM_SETUP_KEY);
      }
      return _systemSetupKey;

   case RegType::AgentConfig:
      if (_agentConfigKey == NULL) {
         _agentConfigKey = GetKey(HKLM, HIC_AGENT_CONFIG);
      }
      return _agentConfigKey;

   case RegType::NodeManager:
      if (_nodeManagerKey == NULL) {
         _nodeManagerKey = GetKey(HKLM, SVM_NODE_MANAGER_REGKEY);
      }
      return _nodeManagerKey;

   case RegType::HorizonAgent:
      if (_horizonAgentKey == NULL) {
         _horizonAgentKey = GetKey(HKLM, HIC_HORIZON_AGENT_KEY_PATH);
      }
      return _horizonAgentKey;

   default:
      SYSMSG_FUNC(Error, _T("Unknown Registry Type Specified: %d"), Type);
   };

   return NULL;
}

//
// Service Values
//

HRESULT
Registry::GetValue(const std::wstring &valName, const DWORD &defVal, DWORD &dwValue, RegType Type)
{
   reg::ExistingRegKey *theKey = NULL;

   theKey = GetKeyObj(Type);
   if (theKey == NULL) {
      return E_FAIL;
   }

   return theKey->ReadDwordValue(valName, defVal, dwValue);
}

HRESULT
Registry::GetValue(const std::wstring &valName, std::wstring &strValue, RegType Type)
{
   reg::ExistingRegKey *theKey = NULL;

   theKey = GetKeyObj(Type);
   if (theKey == NULL) {
      return E_FAIL;
   }

   return theKey->ReadStringValue(valName, strValue);
}

//
// Multi string
//
HRESULT
Registry::GetValue(const std::wstring &valName, std::vector<std::wstring> value, RegType Type)
{
   reg::ExistingRegKey *theKey = NULL;

   theKey = GetKeyObj(Type);
   if (theKey == NULL) {
      return E_FAIL;
   }

   return theKey->ReadMultiStringValue(valName, value);
}

HRESULT
Registry::SetValue(const std::wstring &valName, std::wstring strValue, RegType Type)
{
   reg::ExistingRegKey *theKey = NULL;

   theKey = GetKeyObj(Type);
   if (theKey == NULL) {
      return E_FAIL;
   }

   return theKey->WriteStringValue(valName, REG_SZ, strValue);
}

HRESULT
Registry::SetValue(const std::wstring &valName, DWORD dwValue, RegType Type)
{
   reg::ExistingRegKey *theKey = NULL;

   theKey = GetKeyObj(Type);
   if (theKey == NULL) {
      SYSMSG_FUNC(Error, L"Failed Getting KeyObj: Value Name: %ws, Value: %d, Type: %d",
                  valName.c_str(), dwValue, Type);
      return E_FAIL;
   }

   return theKey->WriteDwordValue(valName, dwValue);
}

HRESULT
Registry::SetValue(const std::wstring &valName, BYTE *value, const size_t len, RegType Type)
{
   reg::ExistingRegKey *theKey = NULL;

   theKey = GetKeyObj(Type);
   if (theKey == NULL) {
      return E_FAIL;
   }

   return theKey->WriteMultiStringValue(valName, value, len);
}

HRESULT
Registry::WaitForChange(DWORD dwDuration, bool &bResult, HANDLE &hWaitEvent, HANDLE hShutdownEvent,
                        RegType Type)
{
   reg::ExistingRegKey *theKey = NULL;

   theKey = GetKeyObj(Type);
   if (theKey == NULL) {
      return E_FAIL;
   }

   return theKey->WaitForChange(bResult, hWaitEvent);
}

HRESULT
Registry::WaitForDword(const std::wstring &ValueName, DWORD expectedValue, DWORD dwDuration,
                       bool &bResult, RegType Type)
{
   reg::ExistingRegKey *theKey = NULL;

   theKey = GetKeyObj(Type);
   if (theKey == NULL) {
      return E_FAIL;
   }

   return theKey->WaitForDwordValue(ValueName, expectedValue, dwDuration, bResult);
}

HRESULT
Registry::WaitForString(const std::wstring &ValueName, std::wstring strExpectedValue,
                        DWORD dwDuration, bool &bResult, HANDLE hShutdownEvent, RegType Type)
{
   reg::ExistingRegKey *theKey = NULL;

   theKey = GetKeyObj(Type);
   if (theKey == NULL) {
      return E_FAIL;
   }

   return theKey->WaitForStringValue(ValueName, strExpectedValue, dwDuration, bResult,
                                     hShutdownEvent);
}

HRESULT
Registry::WaitForString(const std::wstring &valueName, const std::wstring &expectedValue,
                        int totalWaitTimeInMs, RegType Type)
{
   reg::ExistingRegKey *theKey = NULL;

   theKey = GetKeyObj(Type);
   if (theKey == NULL) {
      return E_FAIL;
   }

   return theKey->WaitForStringValue(valueName, expectedValue, totalWaitTimeInMs);
}

HRESULT
Registry::WaitForValueExists(const std::wstring &ValueName, DWORD dwDuration, RegType Type)
{
   reg::ExistingRegKey *theKey = NULL;

   theKey = GetKeyObj(Type);
   if (theKey == NULL) {
      return E_FAIL;
   }

   return theKey->WaitForValueExists(ValueName, dwDuration);
}

HRESULT
Registry::FlushKey(RegType Type)
{
   reg::ExistingRegKey *theKey = NULL;

   theKey = GetKeyObj(Type);
   if (theKey == NULL) {
      return E_FAIL;
   }

   return theKey->FlushKey();
}