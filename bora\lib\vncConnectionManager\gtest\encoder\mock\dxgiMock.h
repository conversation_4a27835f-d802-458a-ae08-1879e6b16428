/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

#include <dxgi.h>

#define PCI_VENDOR_ID_AMD (0x1002)

// Mock IDXGIAdapter class
class MockIDXGIAdapter : public IDXGIAdapter {
public:
   ULONG STDMETHODCALLTYPE AddRef() override { return 1; }
   ULONG STDMETHODCALLTYPE Release() override { return 1; }
   HRESULT STDMETHODCALLTYPE QueryInterface(REFIID riid, void **ppvObject) override
   {
      if (!ppvObject) {
         return E_POINTER;
      }
      *ppvObject = nullptr;
      if (riid == __uuidof(IUnknown) || riid == __uuidof(IDXGIObject) ||
          riid == __uuidof(IDXGIAdapter)) {
         *ppvObject = static_cast<IDXGIAdapter *>(this);
         return S_OK;
      }
      return S_OK;
   }
   // IDXGIObject
   HRESULT STDMETHODCALLTYPE SetPrivateData(REFGUID, UINT, const void *) override
   {
      return E_NOTIMPL;
   }
   HRESULT STDMETHODCALLTYPE SetPrivateDataInterface(REFGUID, const IUnknown *) override
   {
      return E_NOTIMPL;
   }
   HRESULT STDMETHODCALLTYPE GetPrivateData(REFGUID, UINT *, void *) override { return E_NOTIMPL; }
   HRESULT STDMETHODCALLTYPE GetParent(REFIID, void **) override { return E_NOTIMPL; }
   // IDXGIAdapter
   HRESULT STDMETHODCALLTYPE EnumOutputs(UINT, IDXGIOutput **) override { return E_NOTIMPL; }

   HRESULT STDMETHODCALLTYPE GetDesc(DXGI_ADAPTER_DESC *pDesc) override
   {
      if (!pDesc) {
         return E_INVALIDARG;
      }
      // Fill with dummy values
      wcsncpy_s(pDesc->Description, L"Mock Adapter", _countof(pDesc->Description));
      pDesc->VendorId = PCI_VENDOR_ID_AMD;
      return S_OK;
   }

   HRESULT STDMETHODCALLTYPE CheckInterfaceSupport(REFGUID, LARGE_INTEGER *) override
   {
      return E_NOTIMPL;
   }
};

// Mock IDXGIFactory1 class
class MockIDXGIFactory1 : public IDXGIFactory1 {
public:
   // IDXGIObject
   HRESULT STDMETHODCALLTYPE SetPrivateData(const GUID &guid, UINT DataSize,
                                            const void *pData) override
   {
      (void)guid;
      (void)DataSize;
      (void)pData;
      return E_NOTIMPL;
   }
   HRESULT STDMETHODCALLTYPE SetPrivateDataInterface(const GUID &guid,
                                                     const IUnknown *pUnknown) override
   {
      (void)guid;
      (void)pUnknown;
      return E_NOTIMPL;
   }
   HRESULT STDMETHODCALLTYPE GetPrivateData(const GUID &guid, UINT *pDataSize, void *pData) override
   {
      (void)guid;
      (void)pDataSize;
      (void)pData;
      return E_NOTIMPL;
   }
   HRESULT STDMETHODCALLTYPE GetParent(const IID &riid, void **ppParent) override
   {
      (void)riid;
      (void)ppParent;
      return E_NOTIMPL;
   }

   // Mock methods from IDXGIFactory
   HRESULT STDMETHODCALLTYPE EnumAdapters(UINT Adapter, IDXGIAdapter **ppAdapter) override
   {
      if (!ppAdapter) {
         return E_INVALIDARG;
      }
      *ppAdapter = new MockIDXGIAdapter(); // Simulate a valid adapter
      return S_OK;
   }
   HRESULT STDMETHODCALLTYPE MakeWindowAssociation(HWND WindowHandle, UINT Flags) override
   {
      (void)WindowHandle;
      (void)Flags;
      return E_NOTIMPL;
   }
   HRESULT STDMETHODCALLTYPE GetWindowAssociation(HWND *pWindowHandle) override
   {
      (void)pWindowHandle;
      return E_NOTIMPL;
   }
   HRESULT STDMETHODCALLTYPE CreateSwapChain(IUnknown *pDevice, DXGI_SWAP_CHAIN_DESC *pDesc,
                                             IDXGISwapChain **ppSwapChain) override
   {
      (void)pDevice;
      (void)pDesc;
      (void)ppSwapChain;
      return E_NOTIMPL;
   }
   HRESULT STDMETHODCALLTYPE CreateSoftwareAdapter(HMODULE Module,
                                                   IDXGIAdapter **ppAdapter) override
   {
      (void)Module;
      (void)ppAdapter;
      return E_NOTIMPL;
   }

   // IDXGIFactory1
   HRESULT STDMETHODCALLTYPE EnumAdapters1(UINT Adapter, IDXGIAdapter1 **ppAdapter) override
   {
      (void)Adapter;
      (void)ppAdapter;
      return E_NOTIMPL;
   }
   BOOL STDMETHODCALLTYPE IsCurrent() override { return TRUE; }

   // IUnknown
   HRESULT STDMETHODCALLTYPE QueryInterface(REFIID riid, void **ppvObject) override
   {
      if (!ppvObject) {
         return E_POINTER;
      }
      *ppvObject = nullptr;
      if (riid == __uuidof(IUnknown) || riid == __uuidof(IDXGIFactory) ||
          riid == __uuidof(IDXGIFactory1)) {
         *ppvObject = static_cast<IDXGIFactory1 *>(this);
         return S_OK;
      }
      return E_NOINTERFACE;
   }
   ULONG STDMETHODCALLTYPE AddRef() override { return 1; }
   ULONG STDMETHODCALLTYPE Release() override { return 1; }
};