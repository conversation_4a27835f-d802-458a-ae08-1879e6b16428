/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

#include "stdafx.h"

namespace coreutil = svmga::core::util;
using namespace svmga::core::util;

CustType CustomizationType::_custType = CustType::Unknown;

CustomizationType::CustomizationType() :
   _custFlags(coreutil::CustomizationFlags::GetInstance()),
   _cpi(coreutil::CustomizationPersistentInfo::GetInstance())
{
   /**
    * TODO: Evaluate the possibility of moving _custType assignment here.
    * Then delete all the GetCustomizationType() calls in other public functions.
    */
}

CustType
CustomizationType::GetCustomizationType()
{
   if (_custType != CustType::Unknown) {
      return _custType;
   }

   if (_cpi->IsUniversalPrep()) {
      _custType = CustType::UniversalPrep;
   } else if (_custFlags->IsFlagOn(CUSTOMIZATION_FLAG_SYSPREP)) {
      _custType = CustType::SysPrep;
   } else {
      // By default (cloneprep) CUSTOMIZATION_FLAG_CLONEPREP is not on.
      _custType = CustType::ClonePrep;
   }

   return _custType;
}

bool
CustomizationType::IsSysPrep()
{
   GetCustomizationType();
   return _custType == CustType::SysPrep;
}

bool
CustomizationType::IsSysprepDomainJoinEnabled()
{
   return _custFlags->IsFlagOn(CUSTOMIZATION_FLAG_SYSPREP_DOMAIN_JOIN);
}

bool
CustomizationType::IsClonePrep()
{
   GetCustomizationType();
   return _custType == CustType::ClonePrep;
}

bool
CustomizationType::IsUniversalPrep()
{
   GetCustomizationType();
   return _custType == CustType::UniversalPrep;
}

bool
CustomizationType::IsFastRefresh()
{
   return _custFlags->IsFlagOn(CUSTOMIZATION_FLAG_FAST_REFRESH);
}