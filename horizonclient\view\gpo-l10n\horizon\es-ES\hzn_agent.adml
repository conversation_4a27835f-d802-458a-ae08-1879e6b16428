﻿<?xml version="1.0" encoding="utf-8"?>
<policyDefinitionResources xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" revision="1.0" schemaVersion="1.0" xmlns="http://www.microsoft.com/GroupPolicy/PolicyDefinitions">

   <displayName>Omnissa Horizon Agent</displayName>
   <description>Ajustes de configuración de Horizon Agent</description>
   <resources>
      <stringTable>
         <string id="SUPPORTED_Windows10_1607_VM">Al menos una máquina virtual Windows Server 2016 VDI versión 1607 o Windows 10</string>

         <string id="Agent_Configuration">Configuración de Agent</string>

         <string id="Collaboration">Colaboración</string>

         <string id="Agent_Security">Seguridad de Agent</string>

         <string id="Unity_Touch_Hosted_Apps">Unity Touch y aplicaciones alojadas</string>

         <string id="Unity_Filter">Lista de reglas de filtro de Unity</string>

         <string id="Unity_Filter_Rules_Desc">Esta política especifica las reglas de filtros para ventanas cuando se procesan de modo remoto las aplicaciones hospedadas. Horizon Agent usa las reglas de filtros para dar soporte a las aplicaciones personalizadas. Este GPO se puede usar si tiene un problema de visualización de ventanas, como una ventana con un fondo negro o una ventana desplegable cuyo tamaño no se ajuste correctamente.

El primer paso para establecer una regla es determinar las características de las ventanas a las que quiere que se aplique. Existen muchas características posibles que se pueden identificar:

1. Nombre de clase de la ventana, identificada en una regla personalizada como classname=XYZ
2. Empresa del producto, identificada como company=XYZ
3. Nombre del producto, identificada como product=XYZ
4. Versión principal del producto, identificada como major=XYZ
5. Versión secundaria del producto, identificada como minor=XYZ
6. Número de compilación del producto, identificada como build=XYZ
7. Número de revisión del producto, identificada como revision=XYZ

Es más común utilizar solo &quot;Nombre de clase de ventana&quot; como la característica preferida (por ejemplo, classname=CustomClassName). Sin embargo, debe especificar el resto de características en caso de que necesite limitar las reglas a un producto específico. Puede encontrar estas características en la ventana Propiedades de archivos de un archivo ejecutable, y deben coincidir exactamente en mayúsculas, minúsculas y caracteres especiales. Cuando se especifican varias características, todas deben coincidir para que se aplique la regla a la ventana.

Tras identificar las características, el siguiente paso es elegir una acción. La acción debe ser action=block o action=map. action=block hace que Horizon Agent no envíe de forma remota la ventana al cliente. Esto se usa cuando aparece en el cliente una ventana demasiado grande o que interfiere con el comportamiento habitual para centrar las ventanas. action=map hace que Horizon Agent trate la ventana como un tipo codificado de forma rígida.

Si establece action=map, también tendrá que especificar el tipo al que asignar la ventana. Esto se lleva a cabo incluyendo type=XYZ. A continuación aparece una lista con todos los tipos disponibles: normal, panel, dialog, tooltip, splash, toolbar, dock, desktop, widget, combobox, startscreen, sidepanel, taskbar, metrofullscreen, metrodocked.

A continuación le mostramos dos ejemplos de reglas que puede establecer para solucionar una aplicación con un comportamiento erróneo:

1. Puede excluir una ventana que no debe enviar de forma remota.
   - Para bloquear todas las ventanas con el nombre de clase MyClassName, use la regla &quot;classname=MyClassName;action=block&quot;
   - Para bloquear todas las ventanas del producto MyProduct, use la regla &quot;product=MyProduct;action=block&quot;.
2. Puede asignar una ventana al tipo correcto. Esto solo suele ser necesario si el equipo de soporte de Omnissa se lo indica, ya que es complicado determinar si una ventana se asignó al tipo incorrecto.
   - Para asignar una clase personalizada al tipo combobox, use la regla &quot;classname=MyClassName;action=map;type=combobox&quot;

Nota: Este GPO tiene menos prioridad que las reglas de filtro que están instaladas en %ProgramData%\Omnissa\RdeServer\Unity Filters</string>

         <string id="Smartcard_Redirection">Redireccionamiento de tarjeta inteligente</string>

         <string id="Local_Reader_Access">Acceso a lectores locales</string>

         <string id="True_SSO_Configuration">Configuración de True SSO</string>

         <string id="Whfb_Certificate_Redirection">Redireccionamiento de certificado Whfb</string>

         <string id="Whfb_Certificate_Allowed_Applications">Lista de ejecutables permitidos</string>

         <string id="Whfb_Certificate_Allowed_Applications_Desc">Lista de ejecutables que pueden utilizar el certificado Whfb redireccionado</string>

         <string id="View_USB_Configuration">Configuración USB de Horizon</string>

         <string id="Client_Downloadable_only_settings">Ajustes de solo descarga del cliente</string>

         <string id="Recursive_Domain_Enumeration">Enumeración recursiva de dominios de confianza</string>

         <string id="Recursive_Domain_Enumeration_Desc">Determina si se enumeran todos los dominios de confianza del dominio en el que se encuentra el servidor. Para establecer una cadena completa de confianza, los dominios en los que confían cada dominio de confianza también se enumeran y el proceso continúa recursivamente hasta que se detectan todos los dominios de confianza. Esta información se envía a Horizon Connection Server para garantizar que todos los dominios de confianza estén disponibles cuando el cliente inicia sesión.

Esta propiedad está habilitada de forma predeterminada. Cuando está deshabilitada, solo se enumeran los dominios de confianza directa y no se establece la conexión a controladores de dominios remotos.

Nota: En entornos con relaciones de dominio complejas (como las que usan varias estructuras de bosques con confianza entre dominios), este proceso puede tardar varios minutos en completarse.</string>

         <string id="Force_MMR_to_use_overlay">Forzar al redireccionamiento multimedia a usar superposición de software</string>

         <string id="Force_MMR_to_use_overlay_Desc">El redireccionamiento multimedia (MMR) tratará de usar la superposición de hardware a la hora de reproducir vídeos para lograr un mayor rendimiento. No obstante, a la hora de trabajar con varias pantallas, la superposición de hardware solo se da en una de ellas: o bien la pantalla principal o la pantalla en la que se ha iniciado el Reproductor de Windows Media. Si se arrastra el Reproductor de Windows Media a otra pantalla, el vídeo se muestra como un rectángulo negro. Utilice esta opción para forzar al MMR a usar la superposición de software, que funciona en todas las pantallas.</string>

         <string id="Enable_multi_media_acceleration">Habilitar la aceleración multimedia</string>

         <string id="Enable_multi_media_acceleration_Desc">Especifica si el redireccionamiento multimedia (MMR) está habilitado en el agente. MMR es un filtro de Microsoft DirectShow que reenvía directamente al cliente datos multimedia desde códecs específicos del sistema remoto a través de un socket TCP. Después, los datos se descodifican directamente en cliente, donde se reproducen. Los administradores pueden deshabilitar el MMR si el cliente no dispone de suficientes recursos para procesar la decodificación multimedia local.

Nota: El MMR no funciona correctamente si el hardware de reproducción de vídeo de Horizon Client no es compatible con la superposición. La directiva de MMR no se aplica a sesiones de escritorios sin conexión.</string>

         <string id="AllowDirectRDP">Permitir RDP directo</string>

         <string id="AllowDirectRDP_Desc">Determina si los clientes que no son Horizon pueden conectarse directamente a escritorios Horizon mediante RDP. Cuando se deshabilita, el agente solo permite conexiones gestionadas por Horizon mediante Horizon Client o Horizon Portal.

Esta propiedad está habilitada de forma predeterminada.</string>

         <string id="AllowSingleSignon">Permitir inicio de sesión único</string>

         <string id="AllowSingleSignon_Desc">Determina si se usa el inicio de sesión único (SSO) para conectar usuarios a escritorios Horizon. Cuando se habilita, los usuarios solo deben introducir sus credenciales al conectarse con Horizon Client o Horizon Portal. Cuando se deshabilita, los usuarios deben volver a autenticarse cuando se realiza la conexión remota.

Esta propiedad requiere la instalación en el escritorio del componente de autenticación segura de Horizon Agent y está habilitada de forma predeterminada.</string>

         <string id="AutoPopulateLogonUI">Rellenar automáticamente la interfaz de usuario de inicio de sesión</string>

         <string id="AutoPopulateLogonUI_Desc">Determina si el campo de nombre de usuario en la interfaz Logonui se rellena automáticamente o no. Esta propiedad está habilitada de forma predeterminada y solo se aplica en RDS cuando el inicio de sesión único está deshabilitado o no funciona.</string>

         <string id="ConnectionTicketTimeout">Tiempo de espera del ticket de conexión</string>

         <string id="ConnectionTicketTimeout_Desc">Especifica durante cuánto tiempo (en segundos) es válido el ticket de conexión de Horizon. Los clientes Horizon usan el ticket de conexión al conectarse a Horizon Agent; se utiliza con fines de verificación y de inicio de sesión único.

Por motivos de seguridad, estos tickets solo son válidos durante el período de tiempo especificado. Si no se establece esta propiedad de forma explícita, se aplica un valor por defecto de 900 segundos.</string>

         <string id="CredentialFilterExceptions">Excepciones de filtro de credenciales</string>

         <string id="CredentialFilterExceptions_Desc">Una lista de nombres de archivo ejecutables, separados por punto y coma, a los que no se les permite cargar el filtro de credenciales del agente. Los nombres de archivo no deben incluir la ruta ni el sufijo.</string>

         <string id="RDPVcBridgeUnsupportedClients">Clientes no compatibles con RDPVcBridge</string>

         <string id="RDPVcBridgeUnsupportedClients_Desc">Una lista separada por comas de tipos de Horizon Client que no admiten RDPVcBridge.</string>

         <string id="Disable_Time_Zone_sync">Deshabilitar sincronización de zona horaria</string>

         <string id="Disable_Time_Zone_sync_Desc">Determina si se sincroniza la zona horaria del escritorio Horizon con la del cliente conectado. Cuando se habilita, esta propiedad solo surte efecto si la propiedad 'Deshabilitar reenvío de zona horaria' de la directiva de configuración de Horizon Client no está deshabilitada.

Esta propiedad está deshabilitada de forma predeterminada.</string>

         <string id="Keep_Time_Zone_sync_disconnect">Mantener sincronización de zona horaria al desconectarse (VDI)</string>

         <string id="Keep_Time_Zone_sync_disconnect_Desc">Si 'Sincronización de zona horaria' está habilitada y esta propiedad también, la zona horaria del escritorio remoto permanecerá sincronizada con la zona horaria del cliente que se desconectó por última vez.

Si esta propiedad está deshabilitada, la zona horaria del escritorio remoto se restaurará cuando se desconecte la sesión del usuario final.

Este ajuste no se aplica a hosts RDSH cuando esté habilitada la función de Servicios de Escritorios remotos.

Esta propiedad está deshabilitada de forma predeterminada.</string>

         <string id="Keep_Time_Zone_sync_logoff">Mantener sincronización de zona horaria al cerrar sesión (VDI)</string>

         <string id="Keep_Time_Zone_sync_logoff_Desc">Si 'Sincronización de zona horaria' está habilitada y esta propiedad también, la zona horaria del escritorio remoto permanecerá sincronizada con la zona horaria del cliente que cerró sesión por última vez.

Si esta propiedad está deshabilitada, la zona horaria del escritorio remoto se restaurará cuando se cierre la sesión del usuario final.

Este ajuste no se aplica a hosts RDSH cuando esté habilitada la función de Servicios de Escritorios remotos.

Esta propiedad está deshabilitada de forma predeterminada.</string>

          <string id="Enable_ClientMediaPerm_Popup">Habilitar selector de pestañas, pantallas y aplicaciones para compartir pantalla con el redireccionamiento de navegador</string>

          <string id="Enable_ClientMediaPerm_Popup_Desc">Cuando se habilita, se muestra un selector para seleccionar la pestaña de navegador, la pantalla o la aplicación al compartir pantalla con el redireccionamiento del navegador. Esta propiedad está habilitada de forma predeterminada.</string>

		  <string id="Toggle_Display_Settings_Control">Cambiar control de configuración de pantalla</string>

         <string id="Toggle_Display_Settings_Control_Desc">Determina si se deshabilita la página de configuración del applet Panel de control de pantalla cuando se conecta un Horizon Client.

Esta propiedad solo se aplica a sesiones que utilicen el protocolo PCoIP. Esta propiedad está habilitada de forma predeterminada.</string>

         <string id="DpiSync">Sincronización de PPP</string>

         <string id="DpiSync_Desc">Ajusta la configuración PPP del sistema para la sesión remota. Cuando se habilita o no se configura, la configuración PPP del sistema para la sesión remota es igual a la configuración PPP del sistema operativo cliente. Cuando se deshabilita, nunca se modifica la configuración PPP del sistema para la sesión remota.</string>

         <string id="DpiSyncPerMonitor">Sincronización de PPP por monitor</string>

         <string id="DpiSyncPerMonitor_Desc">Ajusta la configuración de PPP en varios monitores durante una sesión remota. Cuando está habilitada o no está configurada, la configuración de PPP en todos los monitores cambia para que coincida con la configuración PPP del sistema operativo cliente durante una sesión remota. Si se personaliza la configuración de PPP, se compara con la configuración PPP personalizada. Cuando esta opción está deshabilitada, los usuarios deberán desconectarse y conectarse a una nueva sesión remota para que los cambios de PPP surtan efecto en todos los monitores.</string>

         <string id="DisplayScaling">Ajuste de escala de la pantalla</string>

         <string id="DisplayScaling_Desc">Controle si la función de ajuste de escala de la pantalla se permite o no en el lado del agente. Cuando está habilitada o no está configurada, se permitirá el ajuste de escala de la pantalla en el lado del agente, y el estado de activado o desactivado de la función de ajuste de escala de la pantalla dependerá de la configuración del cliente. Cuando se deshabilita, la función de ajuste de escala de la pantalla estará deshabilitada, independientemente de la configuración del cliente. Esta configuración solo se aplica cuando la sincronización de PPP por monitor está deshabilitada.</string>

         <string id="DisallowCollaboration">Desactivar colaboración</string>

         <string id="DisallowCollaboration_Desc">Esta opción establece si se permite la colaboración en la máquina virtual de Horizon Agent. Si está habilitada, la función de colaboración está totalmente desactivada. Si está deshabilitada o no está configurada, esta función se controla en el nivel de grupo. Para que esta opción tenga efecto, es necesario reiniciar las máquinas Horizon Agent.</string>

         <string id="AllowCollaborationInviteByIM">Permitir invitar colaboradores por IM</string>

         <string id="AllowCollaborationInviteByIM_Desc">Esta opción configura si los usuarios pueden enviar invitaciones de colaboración usando una aplicación de mensajería instantánea (IM). Si está deshabilitada, el usuario no puede enviar invitaciones con IM, aunque esté instalada una aplicación de IM. Esta configuración está habilitada de forma predeterminada.</string>

         <string id="AllowCollaborationInviteByEmail">Permitir invitar colaboradores por correo electrónico</string>

         <string id="AllowCollaborationInviteByEmail_Desc">Esta opción configura si los usuarios pueden enviar invitaciones de colaboración usando una aplicación de correo electrónico instalada. Si está deshabilitada, el usuario no puede enviar invitaciones por correo electrónico, aunque esté instalada una aplicación de correo electrónico. Esta configuración está habilitada de forma predeterminada.</string>

         <string id="AllowCollaborationControlPassing">Permitir que el control se transfiera a los colaboradores</string>

         <string id="AllowCollaborationControlPassing_Desc">Esta opción configura si los usuarios pueden transferir el control de entradas a otros colaboradores durante la colaboración. Esta configuración está habilitada de forma predeterminada.</string>

         <string id="MaxCollaboratorCount">Número máximo de colaboradores invitados</string>

         <string id="MaxCollaboratorCount_Desc">Esta opción configura el número máximo de colaboradores que un usuario puede invitar para que se una a la sesión. El máximo predeterminado es 5.</string>

         <string id="CollaborationEmailInviteDelimiter">Separador usado entre varias direcciones de correo electrónico en vínculos &quot;mailto:&quot;.</string>

         <string id="CollaborationEmailInviteDelimiter_Desc">Esta opción configura el separador usado entre varias direcciones de correo electrónico en vínculos &quot;mailto:&quot;. Cuando esta directiva no está configurada, el valor predeterminado es &quot;;&quot; (punto y coma sin espacio) para separar las direcciones de correo electrónico, de forma que se garantice la mejor compatibilidad con clientes de correo electrónico más comunes.

Si el cliente de correo electrónico predeterminado tiene problemas con este separador, puede probar otras combinaciones como &quot;, &quot; (coma más un espacio) o &quot;; &quot; (punto y coma con un espacio). Este valor estará codificado por un URI antes de colocarlo en el vínculo &quot;mailto:&quot;, así que no establezca esta entrada en un valor codificado por un URI.</string>

         <string id="CollaborationClipboardIncludeOutlookURL">Incluir URL con formato de Outlook en el texto del portapapeles</string>

         <string id="CollaborationClipboardIncludeOutlookURL_Desc">Cuando esta opción está habilitada, se incluirá una URL de invitación con formato Outlook en el texto de la invitación del portapapeles. Habilite esta opción si espera que los usuarios finales peguen el texto de la invitación del portapapeles en un correo electrónico. Este ajuste está deshabilitado de forma predeterminada.</string>

         <string id="CollaborationServerURLs">URL del servidor que se incluirán en un mensaje de invitación</string>

         <string id="CollaborationServerURLs_Desc">Esta opción le permite anular la URL predeterminada incluida en las invitaciones de colaboración. Se recomienda que configure este valor si su implementación usa más de una URL de servidor (por ejemplo, URL interna y externa diferentes o URL por pod).

Al configurar los valores, la primera columna debe incluir la URL con un puerto opcional (por ejemplo &quot;horizon-ca.corp.int&quot; o &quot;horizon-ca.corp.int:2323&quot;) y la segunda columna debe incluir una breve descripción de la URL (por ejemplo, &quot;Pod de California&quot; o &quot;Red corporativa&quot;). La descripción solo se utiliza si existen varios servidores en la lista.</string>

         <string id="UnAuthenticatedAccessEnabled">Habilitar la función Acceso sin autenticar</string>

         <string id="UnAuthenticatedAccessEnabled_Desc">Esta opción habilita la función Acceso sin autenticar. Se debe reiniciar el sistema para que este cambio surta efecto. El acceso sin autenticar está habilitado de forma predeterminada.</string>

         <string id="RdsAadAuthEnabled">Habilitar el inicio de sesión único de Azure Active Directory</string>

         <string id="RdsAadAuthEnabled_Desc">Este ajuste habilita la función de inicio de sesión único de Azure Active Directory. Se debe reiniciar el sistema para que este cambio surta efecto. Esta función está habilitada de forma predeterminada.  Esta función depende del sistema al que se una Azure Active Directory.</string>

         <string id="CommandsToRunOnConnect">Comandos para ejecutar en Connect</string>

         <string id="CommandsToRunOnConnect_Desc">La lista de comandos que se ejecutarán al conectar una sesión por primera vez.</string>

         <string id="CommandsToRunOnReconnect">Comandos que se ejecutan al reconectar</string>

         <string id="CommandsToRunOnReconnect_Desc">La lista de comandos que se ejecutarán al volver a conectar una sesión después de una desconexión.</string>

         <string id="CommandsToRunOnDisconnect">Comandos que se ejecutan al desconectar</string>

         <string id="CommandsToRunOnDisconnect_Desc">Especifica una lista de comandos que se ejecutarán al desconectar una sesión.</string>

         <string id="ShowDiskActivityIcon">Mostrar icono de actividad de disco</string>

         <string id="ShowDiskActivityIcon_Desc">Muestra un icono de actividad de disco en la bandeja del sistema. Utiliza el &quot;Registrador del kernel de NT de seguimiento del sistema&quot;, que solo puede ser utilizado por un único proceso; debe desactivarse si se necesita para otros fines. Habilitado de forma predeterminada.</string>

         <string id="SSO_retry_timeout">Tiempo de espera para nuevo intento de inicio de sesión único</string>

         <string id="SSO_retry_timeout_Desc">Especifica cuánto tiempo (milisegundos) transcurre hasta un nuevo intento de inicio de sesión único. Establezca en 0 para deshabilitar el nuevo intento de inicio de sesión único. El valor predeterminado es 5.000 milisegundos.</string>

         <string id="Win10PhysicalAgentAudioOption">Opción de audio para máquina de escritorio remoto físico con Windows 10 de sesión única</string>

         <string id="Win10PhysicalAgentAudioOption_Desc">Especifica qué dispositivos de audio se utilizarán en la sesión de la máquina de escritorio remoto físico con Windows 10 de Horizon. De forma predeterminada, se utilizarán los dispositivos de audio asociados al endpoint de Horizon Client.</string>

         <string id="WaitForLogoff">Esperar a que se agote el tiempo de espera de cierre de sesión</string>

         <string id="WaitForLogoff_Desc">Especifica el tiempo en segundos que se debe esperar hasta que se cierre la sesión anterior del usuario antes de intentar iniciar otra sesión. Establezca el valor en 0 para deshabilitar el tiempo de espera y generar un error inmediatamente. El valor predeterminado es de 10 segundos.</string>

         <string id="UseClientAudioDevice">Utilizar dispositivos de audio conectados al endpoint de Horizon Client</string>

         <string id="UsePhysicalMachineAudioDevice">Usar dispositivos de audio conectados al endpoint de escritorio remoto físico con Windows 10 de Horizon</string>

         <string id="VDI_idle_time_till_disconnect">Tiempo de inactividad hasta desconexión (VDI)</string>

         <string id="VDI_idle_time_till_disconnect_Desc">Especifica el tiempo que debe transcurrir para que se desconecte una sesión de escritorio VDI debido a la inactividad del usuario.
Si esta opción está deshabilitada o no está configurada, las sesiones de escritorios VDI no se desconectarán nunca. Si se selecciona &quot;Nunca&quot;, se obtendrá el mismo efecto.
Nota: Si la máquina o el grupo de escritorios están configurados para cerrar sesión automáticamente después de una desconexión, se aplicará esta configuración.</string>

         <string id="Accept_SSL_encr_framework_channel">Aceptar canal de marco con cifrado SSL</string>

         <string id="Accept_SSL_encr_framework_channel_Desc">Aceptar canal de marco con cifrado SSL 

Habilitar: habilitar SSL, permitir que los clientes heredados se conecten sin SSL
Deshabilitar: deshabilitar SSL
Forzar: habilitar SSL, evitar conexiones de clientes heredados</string>

         <string id="Enable">Habilitar</string>

         <string id="Disable">Deshabilitar</string>

         <string id="Enforce">Forzar</string>

         <string id="Allow_smartcard_local_access">Permitir el acceso de aplicaciones a lectores de tarjetas inteligentes</string>

         <string id="Allow_smartcard_local_access_Desc">Si se habilita, las aplicaciones podrán acceder a todos los lectores de tarjetas inteligentes &quot;locales&quot;, incluso cuando esté instalada la función de redireccionamiento de tarjetas inteligentes.

Esta configuración no se aplica a hosts RDP o RDSH cuando esté habilitada la función de Servicios de Escritorios remotos.

Cuando se habilita, se supervisa el escritorio en busca de la presencia de un lector local y, cuando se detecta, el redireccionamiento de tarjeta inteligente se desactiva y se permite el acceso a los lectores locales. El redireccionamiento permanece desactivado hasta la próxima vez que el usuario se conecte a la sesión.

NOTA: Cuando se habilita el acceso local, las aplicaciones ya no pueden acceder a lectores remotos presentes en el cliente.

Este ajuste está deshabilitado de forma predeterminada.</string>

         <string id="Local_Reader_Name">Nombre de lector local</string>

         <string id="Local_Reader_Name_Desc">Especifica el nombre de un lector local que supervisar con el fin de habilitar el acceso local. Por defecto, debe haber una tarjeta introducida en el lector para habilitar el acceso local; puede deshabilitar este requisito mediante la configuración &quot;Requerir que haya una tarjeta inteligente introducida&quot;.


El valor predeterminado es que la función esté habilitada para todos los lectores</string>

         <string id="Require_an_inserted_smart_card">Requerir que haya una tarjeta inteligente introducida</string>

         <string id="Require_an_inserted_smart_card_Desc">Si se habilita, solo se habilitará el acceso a lectores locales si hay una tarjeta introducida en el lector local. Si se deshabilita, se habilita el acceso local siempre y cuando se detecte un lector local.

Esta configuración está habilitada de forma predeterminada.</string>

         <string id="Disable_true_SSO">Deshabilitar True SSO</string>

         <string id="Disable_true_SSO_Desc">Si se habilita esta opción, se deshabilita la función en el agente</string>

         <string id="Cert_wait_timeout">Tiempo de espera de certificados</string>

         <string id="Cert_wait_timeout_Desc">Especifica el tiempo de espera (en segundos) de los certificados para llegar al agente</string>

         <string id="Min_key_size">Tamaño mínimo de claves</string>

         <string id="Min_key_size_Desc">Claves de tamaño mínimo utilizadas</string>

         <string id="All_key_sizes">Todos los tamaños de clave</string>

         <string id="All_key_sizes_Desc">Todos los tamaños de clave que pueden usarse. Se pueden especificar un máximo de 5 tamaños. Ejemplo: 1024,2048,3072,4096</string>

         <string id="Keys_to_precreate">Cantidad de claves que crear previamente</string>

         <string id="Keys_to_precreate_Desc">Cantidad de claves que crear previamente en un entorno RDSH</string>

         <string id="Cert_min_validity">Periodo mínimo de validez necesario para un certificado</string>

         <string id="Cert_min_validity_Desc">Periodo mínimo de validez (en minutos) necesario para un certificado cuando se vuelve a usar para la reconexión de usuarios</string>

         <string id="Enable_Unity_Touch">Habilitar Unity Touch</string>

         <string id="Enable_Unity_Touch_Desc">Esta directiva especifica si se habilita la función Unity Touch en Horizon Agent. El valor predeterminado de esta configuración es que Unity Touch esté habilitado.

En Windows 10, si se habilita Unity Touch, la subdirectiva especifica si se habilita el soporte de la aplicación de Universal Windows Platform (UWP) para Unity Touch en Horizon Agent. De forma predeterminada, el soporte de UWP para Unity Touch está habilitado. Si la directiva de Unity Touch no está configurada, el soporte de UWP para Unity Touch estará habilitado en Windows 10.</string>

         <string id="Enable_system_tray_redir">Habilitar el redireccionamiento de la bandeja del sistema para aplicaciones alojadas</string>

         <string id="Enable_system_tray_redir_Desc">Esta directiva especifica si se debe habilitar el redireccionamiento de la bandeja del sistema con aplicaciones alojadas remotas. El valor por defecto de esta configuración es que el redireccionamiento de la bandeja del sistema esté habilitado.</string>

         <string id="Enable_user_prof_customization">Habilitar la personalización de perfiles de usuario para aplicaciones alojadas</string>

         <string id="Enable_user_prof_customization_Desc">Esta directiva especifica si se debe ejecutar la personalización de perfiles de usuario con aplicaciones alojadas remotas. Se genera un perfil de usuario, se personaliza el tema de Windows y se ejecutan aplicaciones de inicio registradas. El valor predeterminado es deshabilitado.</string>

         <string id="AllowTinyOrOffscreenWindows">Enviar actualizaciones de ventanas vacías o no visibles</string>

         <string id="AllowTinyOrOffscreenWindows_Desc">Esta directiva especifica si Horizon Client debe recibir actualizaciones de ventanas vacías o no visibles. Cuando se deshabilita este valor, no se envía a Horizon Client información relativa a ventanas de menos de 2 x 2 píxeles o que se encuentren totalmente fuera de pantalla. Deshabilitada por defecto.</string>

         <string id="MinimalHookingModeEnabled">Límite de uso de enlaces de Windows</string>

         <string id="MinimalHookingModeEnabled_Desc">Esta directiva desactiva la mayoría de enlaces con aplicaciones alojadas remotas o cuando se utiliza Unity Touch. Está diseñada para usarse con aplicaciones que presentan problemas de compatibilidad o rendimiento cuando se establecen enlaces en el nivel del sistema operativo. Por ejemplo, esta directiva deshabilita la mayoría de los enlaces activos de accesibilidad y en proceso de Windows. Esta directiva está deshabilitada de forma predeterminada, por lo que utilizamos todos nuestros enlaces favoritos por defecto.</string>

         <string id="LaunchAppWhenArgsAreDifferent">Iniciar nuevas instancias de aplicaciones alojadas únicamente si los argumentos son diferentes</string>

         <string id="LaunchAppWhenArgsAreDifferent_Desc">Esta directiva controla el comportamiento cuando una aplicación alojada se inicia, pero una instancia existente de la aplicación ya se ejecuta dentro de una sesión de protocolo desconectada. Cuando está deshabilitada, se activará la instancia existente de la aplicación. Cuando se habilita, la instancia existente de la aplicación solo se activará si coinciden los parámetros de la línea de comandos. Esta directiva está deshabilitada de forma predeterminada.</string>

         <string id="Exclude_Vid_Pid">Excluir un dispositivo Vid/Pid</string>

         <string id="Exclude_Vid_Pid_Desc">Evitar que un dispositivo con identificadores de proveedor y de producto específicos se reenvíe.

Sintaxis: {m|o}:&lt;vid-&lt;xxxx&gt;_pid-&lt;xxxx|*&gt;&gt;[;...]
Marcador de fusión:
m= la configuración del cliente se fusiona con la del agente
o= la configuración del agente anula la del cliente

Por ejemplo: m:vid-0781_pid-554c;vid-0781_pid-9999</string>

         <string id="Exclude_Vid_Pid_Rel">Excluir un dispositivo Vid/Pid/Rel</string>

         <string id="Exclude_Vid_Pid_Rel_Desc">Evitar que se reenvíe un dispositivo con un identificador de proveedor, identificador de producto y número de versión específicos.

Sintaxis: {m|o}:&lt;vid-&lt;xxxx&gt;_pid-&lt;xxxx&gt;_rel-&lt;xxxx|*&gt;&gt;[;...]
merge-flag:
m= la configuración del cliente se fusiona con la del agente
o= la configuración del agente anula la del cliente

Por ejemplo: m:vid-0781_pid-554c_rel-0100;vid-0781_pid-9999_rel-0010</string>

         <string id="Include_Vid_Pid">Incluir un dispositivo Vid/Pid</string>

         <string id="Include_Vid_Pid_Desc">Incluir un dispositivo con identificadores de proveedor y de producto específicos que pueda reenviarse.

Sintaxis: {m|o}:&lt;vid-&lt;xxxx&gt;_pid-&lt;xxxx|*&gt;&gt;[;...]

Marcador de fusión:
m= la configuración del cliente se fusiona con la del agente
o= la configuración del agente anula la del cliente

Por ejemplo: o:vid-0781_pid-554c;vid-0781_pid-9999</string>

         <string id="Include_Vid_Pid_Rel">Incluir un dispositivo Vid/Pid/Rel</string>

         <string id="Include_Vid_Pid_Rel_Desc">Incluir un dispositivo con un identificador de proveedor, identificador de producto y número de versión específicos que se puede reenviar.

Sintaxis: {m|o}:&lt;vid-&lt;xxxx&gt;_pid-&lt;xxxx&gt;_rel-&lt;xxxx|*&gt;&gt;[;...]

merge-flag:
m= la configuración del cliente se fusiona con la del agente
o= la configuración del agente anula la del cliente

Por ejemplo: o:vid-0781_pid-554c_rel-0100;vid-0781_pid-9999_rel-0010</string>

         <string id="Exclude_device_family">Excluir familia de dispositivos</string>

         <string id="Exclude_device_family_Desc">Evitar que una familia de dispositivos se reenvíe.

Sintaxis: {m|o}:&lt;family-name&gt;[;...]

Marcador de fusión:
m= la configuración del cliente se fusiona con la del agente
o= la configuración del agente anula la del cliente

Por ejemplo: o:bluetooth;entrada de audio</string>

         <string id="Include_device_family">Incluir familia de dispositivos</string>

         <string id="Include_device_family_Desc">Incluir una familia de dispositivos que puede reenviarse.

Sintaxis: {m|o}:&lt;family-name&gt;[;...]

Marcador de fusión:
m= la configuración del cliente se fusiona con la del agente
o= la configuración del agente anula la del cliente

Por ejemplo: m:almacenamiento;salida de audio</string>

         <string id="Exclude_all">Excluir todos los dispositivos</string>

         <string id="Exclude_all_Desc">Bloquear todos los dispositivos a no ser que se incluyan mediante la regla de filtrado de inclusión.

Por defecto: permitir todos los dispositivos</string>

         <string id="HidOpt_Include_Vid_Pid">Incluir el dispositivo VID/PID de optimización de HID</string>

         <string id="HidOpt_Include_Vid_Pid_Desc">Incluir un dispositivo de HID con identificadores de proveedor y de producto específicos que pueda optimizarse.

Sintaxis: &lt;vid-&lt;xxxx&gt;_pid-&lt;xxxx|*&gt;&gt;[;...]

Por ejemplo: vid-056a_pid-0302;vid-046d_pid-c628</string>

         <string id="Exclude_Auto_Vid_Pid">Evitar conexión automática de dispositivo Vid/Pid</string>

         <string id="Exclude_Auto_Vid_Pid_Desc">Evita que un dispositivo con un identificador de proveedor y de producto específicos se reenvíe automáticamente.

Sintaxis: {m|o}:&lt;vid-&lt;xxxx&gt;_pid-&lt;xxxx|*&gt;&gt;[;...]
Marcador de fusión:
m= la configuración del cliente se fusiona con la del agente
o= la configuración del agente anula la del cliente

Por ejemplo: m:vid-0781_pid-554c;vid-0781_pid-9999</string>

         <string id="Exclude_auto_device_family">Evitar conexión automática de familia de dispositivos</string>

         <string id="Exclude_auto_device_family_Desc">Evita que una familia de dispositivos se reenvíe automáticamente.

Sintaxis: {m|o}:&lt;nombre-familia&gt;[;...]

marcador de fusión:
m=La configuración del cliente se fusionará con la del agente
o=La configuración del agente anulará la del cliente

Por ejemplo: o:storage;hid</string>

         <string id="Exclude_Vid_Pid_from_Split">Evitar que un dispositivo Vid/Pid se divida</string>

         <string id="Exclude_Vid_Pid_from_Split_Desc">Evitar que los dispositivos componentes de un dispositivo compuesto identificado por sus identificadores de proveedor y de producto se traten como dispositivos separados para el filtrado.

Sintaxis: {m|o}:&lt;vid-&lt;xxxx&gt;_pid-&lt;xxxx|*&gt;&gt;[;...]
Marcador de fusión:
m= la configuración del cliente se fusiona con la del agente
o= la configuración del agente anula la del cliente

Por ejemplo: o:vid-0781_pid-554c;vid-0781_pid-9999</string>

         <string id="Split_Vid_Pid_Device">Dividir un dispositivo Vid/Pid</string>

         <string id="Split_Vid_Pid_Device_Desc">Tratar los dispositivos componentes de un dispositivo compuesto identificado por sus identificadores de proveedor y de producto como dispositivos separados para el filtrado.

Sintaxis: {m|o}:&lt;vid-&lt;xxxx&gt;_pid-&lt;xxxx|*&gt;(exintf:xx[;exintf:xx])&gt;[;...]
Marcador de fusión:
m= la configuración del cliente se fusiona con la del agente
o= la configuración del agente anula la del cliente

Por ejemplo: o:vid-0781_pid-554c(exintf:01;exintf:02);vid-0781_pid-9999(exintf:02)</string>

         <string id="Allow_other_input_devices">Permitir otros dispositivos de entrada</string>

         <string id="Allow_other_input_devices_Desc">Permitir que se reenvíen otros dispositivos de entrada además de dispositivos HID de arranque, teclados y mouses.

Por defecto: permitir reenvío</string>

         <string id="Allow_Default">Permitir - Configuración de cliente predeterminada</string>

         <string id="Allow_Override">Permitir - Anular configuración de cliente</string>

         <string id="Disable_Default">Deshabilitar - Configuración de cliente predeterminada</string>

         <string id="Disable_Override">Deshabilitar - Anular configuración de cliente</string>

         <string id="Allow_HID_Bootable">Permitir dispositivos HID de arranque</string>

         <string id="Allow_HID_Bootable_Desc">Permitir que se reenvíen dispositivos de entrada de arranque (también conocidos como dispositivos HID de arranque).

Por defecto: permitir reenvío</string>

         <string id="Allow_Audio_Input_devices">Permitir dispositivos de entrada de audio</string>

         <string id="Allow_Audio_Input_devices_Desc">Permitir que se reenvíen dispositivos de entrada de audio.

Por defecto: permitir reenvío</string>

         <string id="Allow_Audio_Output_devices">Permitir dispositivos de salida de audio</string>

         <string id="Allow_Audio_Output_devices_Desc">Permitir que se reenvíen dispositivos de salida de audio.

Por defecto: bloquear reenvío</string>

         <string id="Allow_keyboard_mouse">Permitir dispositivos de teclado y mouse</string>

         <string id="Allow_keyboard_mouse_Desc">Permitir que se reenvíen dispositivos de teclado y mouse.

Por defecto: bloquear reenvío</string>

         <string id="Allow_Video_Devices">Permitir dispositivos de vídeo</string>

         <string id="Allow_Video_Devices_Desc">Permitir que se reenvíen dispositivos de vídeo.

Por defecto: permitir reenvío</string>

         <string id="Allow_Smart_Cards">Permitir tarjetas inteligentes</string>

         <string id="Allow_Smart_Cards_Desc">Permitir que se reenvíen tarjetas inteligentes.

Por defecto: bloquear reenvío</string>

         <string id="Allow_Auto_Device_Splitting">Permitir la división automática del dispositivo</string>

         <string id="Allow_Auto_Device_Splitting_Desc">Evitar que los dispositivos componentes de cualquier dispositivo compuesto se traten como dispositivos separados de forma automática.</string>

         <string id="Proxy_default_ie_autodetect">Detección automática de proxy predeterminada</string>

         <string id="Proxy_default_ie_autodetect_Desc">Configuración de conexión de IE predeterminada. Activa los ajustes de detección automática en Propiedades de Internet, Ajustes de la red de área local</string>

         <string id="Default_proxy_server">Servidor proxy predeterminado</string>

         <string id="Default_proxy_server_Desc">Configuración de conexión de IE predeterminada para el servidor proxy. Especifica el servidor proxy que debe utilizarse en Propiedades de Internet, Ajustes de la red de área local</string>

         <string id="Update_Java_Proxy">Establecer proxy para applet Java</string>

         <string id="Update_Java_Proxy_Desc">Hace que el proxy de Java conecte directamente y omita la configuración del navegador. Hace que el proxy de Java utilice la transparencia de IP del cliente para redireccionar la red para el applet Java. Establecer el valor predeterminado para restablecer los ajustes originales de configuración del proxy de Java.</string>

         <string id="Use_Client_IP">Utilizar transparencia de IP del cliente para proxy de Java</string>

         <string id="Use_Direct_Connect">Utilizar conexión directa para proxy de Java</string>

         <string id="Use_Default">Utilizar valor predeterminado para proxy de Java</string>

         <string id="Enable_white_list">Habilitar lista blanca</string>

         <string id="Enable_black_list">Habilitar lista negra</string>

         <string id="Horizon_HTML5_FEATURES">Funciones HTML5 de Horizon</string>

         <string id="Enable_HTML5_FEATURES">Habilitar las funciones HTML5 de Horizon</string>

         <string id="Enable_HTML5_FEATURES_Desc">Habilita las funciones HTML5 de Horizon. Si esta directiva está establecida en &quot;Habilitado&quot;, se podrán usar el redireccionamiento multimedia HTML5, el redireccionamiento de geolocalización, el redireccionamiento de navegador o la optimización de medios para Microsoft Teams de Horizon.  Si esta directiva se establece en &quot;Deshabilitado&quot;, no se podrá usar ninguna de las funciones HTML5 de Horizon. Esta configuración surte efecto en el siguiente inicio de sesión.</string>

         <string id="HTML5FEATURES_Disable_AudoDetect_Intranet">Deshabilitar Detectar redes intranet automáticamente</string>

         <string id="HTML5FEATURES_Disable_AudoDetect_Intranet_Desc">Si esta directiva está habilitada, los ajustes de intranet &quot;Incluir todos los sitios locales (intranet) no mostrados en otras zonas&quot; e &quot;Incluir todos los sitios que no utilicen el servidor proxy&quot; se deshabilitarán durante el siguiente inicio de sesión. Si está deshabilitada, no se realizará ningún cambio en la zona de intranet IE Local.

Nota: Es necesario que esta directiva esté habilitada cuando (1) el navegador Edge esté habilitado para el redireccionamiento multimedia HTML5 de Horizon o (2) el redireccionamiento de geolocalización esté habilitado.</string>

         <string id="Horizon_HTML5MMR">Redireccionamiento multimedia HTML5 de Horizon</string>

         <string id="Enable_HTML5_MMR">Habilitar Redireccionamiento multimedia HTML5 de Horizon</string>

         <string id="Enable_HTML5_MMR_Desc">Habilita el redireccionamiento multimedia HTML5 de Horizon. Esta configuración surte efecto en el siguiente inicio de sesión.</string>

         <string id="HTML5MMRUrlList">Habilita la lista de URL para Redireccionamiento multimedia HTML5 de Horizon.</string>

         <string id="HTML5MMRUrlBlockList">Excluye la lista de URL para Redireccionamiento multimedia HTML5 de Horizon.</string>

         <string id="HTML5MMRUrlList_Desc">Especifica la lista de URL para Redireccionamiento multimedia HTML5 de Horizon. Especifica el patrón de URL en la columna &quot;Nombre de valor&quot;, por ejemplo, &quot;https://www.youtube.com/*&quot;. La columna &quot;Valor&quot; debe estar vacía y se reserva para su uso en el futuro.</string>

         <string id="HTML5MMRUrlBlockList_Desc">Especifica la lista de URL que se excluirá del Redireccionamiento multimedia HTML5 de Horizon. Especifica el patrón de URL en la columna &quot;Nombre de valor&quot;, por ejemplo, &quot;https://www.youtube.com/*&quot;. La columna &quot;Valor&quot; está reservada para su uso en el futuro y debe estar vacía.</string>

         <string id="HTML5MMR_Enable_Chrome">Habilita el navegador Chrome para Redireccionamiento multimedia HTML5 de Horizon.</string>

         <string id="HTML5MMR_Enable_Chrome_Desc">Esta directiva solo se usa cuando el redireccionamiento multimedia HTML5 de Horizon está habilitado. Si no se configura, el valor predeterminado será el mismo que el de &quot;Habilitar redireccionamiento multimedia HTML5 de Horizon&quot;.</string>

         <string id="HTML5MMR_Enable_Edge">Habilitar la versión heredada del navegador Microsoft Edge para el Redireccionamiento multimedia HTML5 de Horizon</string>

         <string id="HTML5MMR_Enable_Edge_Desc">Esta directiva solo se usa cuando el redireccionamiento multimedia HTML5 de Horizon está habilitado. Si no se configura, el valor predeterminado será el mismo que el de &quot;Habilitar redireccionamiento multimedia HTML5 de Horizon&quot;. </string>

         <string id="HTML5MMR_Enable_Edge_Chromium">Habilitar el navegador Microsoft Edge para el Redireccionamiento multimedia HTML5 de Horizon</string>

         <string id="HTML5MMR_Enable_Edge_Chromium_Desc">Esta directiva solo se usa cuando el redireccionamiento multimedia HTML5 de Horizon está habilitado. Si no se configura, el valor predeterminado será el mismo que el de &quot;Habilitar redireccionamiento multimedia HTML5 de Horizon&quot;. </string>

         <string id="HTML5MMR_Auto_Adjust_Visual_Effect">Ajustar automáticamente el efecto visual de las ventanas</string>

         <string id="HTML5MMR_Auto_Adjust_Visual_Effect_Desc">Esta directiva se utiliza para ajustar automáticamente el efecto visual de la ventana para el redireccionamiento multimedia HTML5 de Horizon. Si no está configurado o está deshabilitado, el efecto visual de las ventanas no se ajustará de forma automática.</string>

         <string id="Horizon_GEO_REDIR">Redireccionamiento de geolocalización de Horizon</string>

         <string id="Enable_GEO_REDIR">Habilitar el redireccionamiento de geolocalización de Horizon</string>

         <string id="Enable_GEO_REDIR_Desc">Habilita la función de redireccionamiento de geolocalización de Horizon. Esta configuración surte efecto en el siguiente inicio de sesión.</string>

         <string id="Enable_GEO_REDIR_For_Chrome">Habilitar el redireccionamiento de geolocalización de Horizon para el navegador Chrome</string>

         <string id="Enable_GEO_REDIR_For_Chrome_Desc">Habilita la función de redireccionamiento de geolocalización de Horizon para el navegador Chrome. Esta configuración surte efecto en el siguiente inicio de sesión.</string>

         <string id="Enable_GEO_REDIR_For_Edge_Chromium">Habilitar el redireccionamiento de geolocalización de Horizon para el navegador Microsoft Edge (Chromium)</string>

         <string id="Enable_GEO_REDIR_For_Edge_Chromium_Desc">Habilita la función de redireccionamiento de geolocalización de Horizon para el navegador Microsoft Edge (Chromium). Esta configuración surte efecto en el siguiente inicio de sesión.</string>

         <string id="GeoRedirUrlList">Habilitar lista de URL de redireccionamiento de geolocalización de Horizon.</string>

         <string id="GeoRedirUrlList_Desc">Especifica la lista de URL para habilitar la función de redireccionamiento de geolocalización. Especifica el patrón de URL en la columna &quot;Nombre de valor&quot;, por ejemplo, &quot;https://www.youtube.com/*&quot;. La columna &quot;Valor&quot; debe estar vacía y se reserva para su uso en el futuro. Esta lista de URL la usarán (1) la extensión de redireccionamiento de geolocalización de Horizon para los navegadores Google Chrome y Microsoft Edge (Chromium) en todos los entornos RDSH y VDI, y (2) el complemento de redireccionamiento de geolocalización de Horizon para el navegador Internet Explorer en entornos RDSH y VDI de Windows 7.</string>

         <string id="GeoRedirDistanceDelta">Establecer la distancia mínima para notificar las actualizaciones de ubicación</string>

         <string id="GeoRedirDistanceDelta_Desc">Especifica la distancia mínima entre una ubicación actualizada en el cliente y la última actualización de la que se ha informado al agente para que se le informe de la nueva. De forma predeterminada, la distancia mínima utilizada es 75 metros.</string>

         <string id="Horizon_BROWSER_REDIR">Redireccionamiento de navegador de Horizon</string>

         <string id="Enable_BROWSER_REDIR">Habilitar el redireccionamiento de navegador de Horizon</string>

         <string id="Enable_BROWSER_REDIR_Desc">Habilita la función de redireccionamiento de navegador de Horizon. Esta configuración surte efecto en el siguiente inicio de sesión. Tenga en cuenta que, al habilitar el redireccionamiento de navegador de Horizon, también se habilitará el redireccionamiento de navegador mejorado de Horizon.</string>

         <string id="Enable_BROWSER_REDIR_For_Chrome">Habilitar el redireccionamiento de navegador de Horizon para el navegador Chrome</string>

         <string id="Enable_BROWSER_REDIR_For_Chrome_Desc">Habilita la función de redireccionamiento de navegador de Horizon para el navegador Chrome. Esta configuración surte efecto en el siguiente inicio de sesión.</string>

         <string id="Enable_BROWSER_REDIR_For_Edge_Chromium">Habilitar la función de redireccionamiento de navegador de Horizon para el navegador Microsoft Edge (Chromium)</string>

         <string id="Enable_BROWSER_REDIR_For_Edge_Chromium_Desc">Habilita la función de redireccionamiento de navegador de Horizon para el navegador Microsoft Edge (Chromium). Esta configuración surte efecto en el siguiente inicio de sesión.</string>

         <string id="BrowserRedirFallbackWhitelistErr">Habilitar la reserva automática tras una infracción a la lista de permitidos</string>

         <string id="BrowserRedirFallbackWhitelistErr_Desc">Cuando esta opción está habilitada, al acceder a una URL desde una pestaña redireccionada mediante el redireccionamiento de navegador introduciéndola en la barra de direcciones personalizada, en la barra de direcciones del navegador o accediendo desde la pestaña redireccionada, si la nueva URL no aparece en la lista de direcciones URL del redireccionamiento de navegador o del redireccionamiento de navegador mejorado, la nueva URL volverá a cargarse automáticamente en el agente. En ese momento, si la nueva URL también se encuentra en la lista de URL de redireccionamiento de navegador mejorado, se redireccionará mediante el redireccionamiento de navegador mejorado. Tenga en cuenta que, al intentar acceder a una URL que no tenga activada la opción &quot;Habilitar la lista de URL para el redireccionamiento de navegador de Horizon&quot; o &quot;Habilitar la lista de URL para el redireccionamiento de navegador mejorado de Horizon&quot;, se pasará inmediatamente a la recuperación y procesamiento en el agente, independientemente de este ajuste.</string>

         <string id="BrowserRedirFetchFromServer">Habilitar la recuperación del lado del agente para la función Redireccionamiento de navegador</string>

         <string id="BrowserRedirFetchFromServer_Desc">Habilite la recuperación de contenido de sitios web desde el agente en lugar del cliente cuando utilice la función Redireccionamiento de navegador. Este ajuste está deshabilitado de forma predeterminada.</string>

         <string id="BrowserRedirShowErrPage">Mostrar una página con información de error antes de la reserva automática</string>

         <string id="BrowserRedirShowErrPage_Desc">Este ajuste solo se utiliza si está habilitada la opción &quot;Habilitar la reserva automática tras una infracción a la lista de permitidos&quot; y hay una infracción de la lista de permitidos. En ese caso, si este ajuste está habilitado, se mostrará una página con una cuenta atrás de 5 segundos, después de la cual la pestaña volverá automáticamente a la recuperación y representación de la URL que causó la infracción en el agente. Si este ajuste está deshabilitado, la pestaña volverá directamente a la representación en el lado del agente, sin proporcionar al usuario una advertencia de 5 segundos.</string>

         <string id="BrowserRedirUrlList">Habilitar la lista de URL para el redireccionamiento de navegador de Horizon</string>

         <string id="BrowserRedirUrlList_Desc">Especifica todas las URL para la función Redireccionamiento de navegador. Para visitar estas URL, puedes escribirlas en la barra de direcciones de Chrome o en la barra de direcciones personalizada. Para visitar estas URL, también puedes acceder a ellas desde otra URL de la lista o desde cualquier página representada por el lado del agente. Especifica el patrón de URL en la columna &quot;Nombre de valor&quot;, por ejemplo, &quot;https://www.youtube.com/*&quot;. La columna &quot;Valor&quot; debe estar vacía y se reserva para su uso en el futuro. Si una URL coincide con un patrón en las listas de direcciones URL de redireccionamiento de navegador y redireccionamiento de navegador mejorado, tendrá prioridad el redireccionamiento de navegador mejorado.</string>

         <string id="EnhBrowserRedirUrlList">Habilitar la lista de URL para el redireccionamiento de navegador mejorado de Horizon</string>

         <string id="EnhBrowserRedirUrlList_Desc">Especifica todas las URL para la función Redireccionamiento de navegador mejorado. Estas direcciones URL se pueden visitar escribiéndolas en la barra de direcciones de Chrome, accediendo a ellas desde otra URL de la lista, o bien desde cualquier página del lado del agente. Especifica el patrón de URL en la columna &quot;Nombre de valor&quot;, por ejemplo, &quot;https://www.youtube.com/*&quot;. La columna &quot;Valor&quot; debe estar vacía y se reserva para su uso en el futuro. Si una URL coincide con un patrón en las listas de direcciones URL de redireccionamiento de navegador y redireccionamiento de navegador mejorado, tendrá prioridad el redireccionamiento de navegador mejorado.</string>

         <string id="BrowserRedirNavUrlList">Habilitar la lista de URL de navegación para el redireccionamiento de navegador de Horizon</string>

         <string id="BrowserRedirNavUrlList_Desc">Especifica las URL a las que puede desplazarse un usuario, ya sea escribiéndolas directamente en la barra de direcciones personalizada o a partir de una URL de la otra lista. Estas URL no se pueden visitar escribiéndolas directamente en la barra de direcciones de Chrome ni a partir de cualquier página representada por el lado del agente. Especifica el patrón de URL en la columna &quot;Nombre de valor&quot;, por ejemplo, &quot;https://www.youtube.com/*&quot;. La columna &quot;Valor&quot; debe estar vacía y se reserva para su uso en el futuro.</string>

         <string id="Horizon_WebRTC_REDIR_FEATURES">Funciones de redireccionamiento de Horizon WebRTC</string>

         <string id="Enable_Teams_Redir">Habilitar optimización de medios para Microsoft Teams</string>

         <string id="Enable_Teams_Redir_Desc">Este ajuste se utiliza para habilitar o deshabilitar la optimización de Microsoft Teams.

Cuando Horizon Agent está instalado, se crea una clave de registro teamsEnabled en el agente que habilita la optimización de Microsoft Teams.  De forma predeterminada, el usuario tiene la opción de utilizar o no la optimización de Microsoft Teams configurando la opción &quot;Optimización de medios para aplicaciones basadas en WebRTC&quot; en Horizon Client.

Si esta directiva está habilitada, la optimización de Microsoft Teams estará habilitada. Si está habilitada y la opción &quot;Forzar optimización de WebRTC en el cliente&quot; está marcada, se forzará la optimización de medios de Teams en el endpoint y se ignorará cualquier ajuste del cliente o cualquier otra directiva de administración (por ejemplo, la directiva de usuario de nivel de Chrome para el cliente Chrome). Si está habilitada y &quot;Forzar optimización de WebRTC en el cliente&quot; no está seleccionada, el usuario tendrá la opción de utilizar o no la optimización de Microsoft Teams configurando el ajuste &quot;Optimización de medios para aplicaciones basadas en WebRTC&quot; de Horizon Client.

Si esta directiva está deshabilitada, la optimización de Microsoft Teams estará deshabilitada y no se podrá utilizar. El ajuste &quot;Optimización de medios para aplicaciones basadas en WebRTC&quot; de Horizon Client no tendrá ningún efecto.

De forma predeterminada, el valor de esta directiva es &quot;Sin configurar&quot;, pero si se cambia la directiva y, a continuación, se vuelve a cambiar a &quot;Sin configurar&quot;, se eliminará la clave de registro teamsEnabled y no se utilizará la optimización de Microsoft Teams.

Esta configuración surte efecto en el siguiente inicio de sesión.</string>

         <string id="Enable_Electron_App_Redir">Habilitar la optimización de medios para aplicaciones Electron generales</string>

         <string id="Enable_Electron_App_Redir_Desc">Este ajuste se utiliza para habilitar o deshabilitar la optimización de aplicaciones Electron.

Si está habilitado o no está configurado, se habilitará la optimización de aplicaciones Electron. Además, si desea forzar al usuario final a utilizar la optimización (si se admite en el dispositivo), elija &quot;Habilitado&quot; y seleccione &quot;Forzar optimización de WebRTC en el cliente&quot;. &quot;Sin configurar&quot; respetará la configuración del cliente si está disponible.
Detalles:
Si está habilitada y si &quot;Forzar optimización de WebRTC en el cliente&quot; no está seleccionada, el usuario tendrá la opción de utilizar o no la optimización de aplicaciones Electron configurando el ajuste &quot;Optimización de medios para aplicaciones basadas en WebRTC&quot; de Horizon Client. Si está activada, se forzará la optimización de medios de aplicaciones Electron en la configuración del cliente y del endpoint, o se ignorará cualquier otra directiva de administración (por ejemplo, la directiva de usuario de nivel de Chrome para el cliente Chrome).
De forma predeterminada, el ajuste de optimización de aplicaciones Electron es &quot;Sin configurar&quot;, lo que habilita la optimización de aplicaciones Electron y permite al usuario configurar el ajuste &quot;Optimización de medios para aplicaciones basadas en WebRTC&quot;.
Si el valor es &quot;Deshabilitado&quot;, la optimización de aplicaciones Electron estará deshabilitada y no se podrá utilizar. El ajuste &quot;Optimización de medios para aplicaciones basadas en WebRTC&quot; de Horizon Client no tendrá ningún efecto.

Esta configuración surte efecto en el siguiente inicio de sesión.</string>

         <string id="Horizon_WebRTC_SDK_WEBAPP">Compatibilidad con el SDK de redireccionamiento de Horizon WebRTC en aplicaciones web</string>

         <string id="WebRTC_SDK_Enable_Web_App_Redir">Habilitar optimización de medios para aplicaciones web</string>

         <string id="WebRTC_SDK_Enable_Web_App_Redir_Desc">Este ajuste permite habilitar o deshabilitar la optimización de aplicaciones web. Si se habilita, se habilitará también la optimización de aplicaciones web.</string>

         <string id="WebRTC_SDK_Enable_Web_App_Redir_Chrome">Habilitar en el navegador Chrome la compatibilidad con el SDK de redireccionamiento de Horizon WebRTC en aplicaciones web</string>

         <string id="WebRTC_SDK_Enable_Web_App_Redir_Chrome_Desc">Esta directiva solo se usa cuando el ajuste Compatibilidad con el SDK de redireccionamiento de Horizon WebRTC en aplicaciones web está &quot;habilitado&quot;. Si no se configura, el valor predeterminado será el mismo que el valor de &quot;Habilitar optimización de medios para aplicaciones web&quot;.</string>

         <string id="WebRTC_SDK_Enable_Web_App_Redir_Edge">Habilitar en el navegador Chromium Edge la compatibilidad con el SDK de redireccionamiento de Horizon WebRTC en aplicaciones web</string>

         <string id="WebRTC_SDK_Enable_Web_App_Redir_Edge_Desc">Esta directiva solo se usa cuando el ajuste Compatibilidad con el SDK de redireccionamiento de Horizon WebRTC en aplicaciones web está &quot;habilitado&quot;. Si no se configura, el valor predeterminado será el mismo que el valor de &quot;Habilitar optimización de medios para aplicaciones web&quot;.</string>

         <string id="WebRTC_SDK_Web_App_Redir_Allow_List">Habilitar en una lista de URL la compatibilidad con el SDK de redireccionamiento de Horizon WebRTC en aplicaciones web</string>

         <string id="WebRTC_SDK_Web_App_Redir_Allow_List_Desc">Especifica todas las URL donde se habilitará la compatibilidad con el SDK de redireccionamiento de Horizon WebRTC en aplicaciones web. Para visitar estas URL, puede escribirlas en la barra de direcciones de Chrome. Para visitar estas URL, también puedes acceder a ellas desde otra URL de la lista o desde cualquier página representada por el lado del agente. Especifica el patrón de URL en la columna &quot;Nombre de valor&quot;, por ejemplo, &quot;https://www.youtube.com/*&quot;. La columna &quot;Valor&quot; debe estar vacía y se reserva para su uso en el futuro.</string>

         <string id="Enable_AEC_Teams_Redir">Habilitar la cancelación de eco acústico de software para la optimización de medios para Microsoft Teams</string>

         <string id="Enable_AEC_Teams_Redir_Desc">Esta opción se utiliza para configurar la cancelación de eco acústico (AEC) de software para la optimización de medios para Microsoft Teams.

Si se establece el valor &quot;Habilitado&quot;, la AEC estará habilitada en el software. Seleccione &quot;Usar algoritmo de AEC recomendado&quot; para obtener un rendimiento y una calidad de audio óptimos. Desactive &quot;Usar algoritmo de AEC recomendado&quot; para utilizar un algoritmo de AEC que utilice menos CPU, pero que afectará a la calidad del audio. Esta opción es útil para procesadores de gama baja con poca potencia de punto flotante. Es aconsejable utilizar el algoritmo de AEC recomendado, ya que resultará adecuado en la mayoría de los casos.

Si se establece el valor &quot;Deshabilitado&quot;, la AEC se deshabilitará en el software y dejará de utilizarse.

Si se establece el valor &quot;Sin configurar&quot;, la AEC se habilitará en el software usando el algoritmo recomendado. Al utilizar un cliente Windows, se utilizará la AEC de software si la AEC de hardware no está disponible. Si la AEC de hardware está disponible (p. ej., si los auriculares tienen AEC integrada), no se utilizará la AEC de software. Al utilizar un cliente que no sea Windows, se utilizará la AEC de software independientemente de si la AEC de hardware está disponible o no.</string>

         <string id="Enable_Datachannel_Teams_Redir">Habilitar canal de datos para optimización de medios para Microsoft Teams</string>
         <string id="Enable_Datachannel_Teams_Redir_Desc">Esta opción se utiliza para habilitar o deshabilitar el canal de datos para la optimización de medios para Microsoft Teams.

Si está &quot;Habilitado&quot;, el canal de datos se podrá utilizar para la optimización de medios para Microsoft Teams, y las funciones que requieren el canal de datos estarán disponibles (p. ej., subtítulos en directo).

Si está &quot;Deshabilitado&quot;, el canal de datos no se podrá utilizar para la optimización de medios para Microsoft Teams, y las funciones que requieren el canal de datos no estarán disponibles.

Si está establecido el valor &quot;Sin configurar&quot;, el canal de datos estará habilitado.</string>

         <string id="Video_Cpu_Overuse_Threshold">Configurar umbral de uso excesivo de CPU</string>

         <string id="Video_Cpu_Overuse_Threshold_Desc"> Si el uso de CPU es superior al umbral, se reducirá la resolución de vídeo enviada, lo que reducirá el uso de CPU del cliente. El umbral predeterminado es 85. Para reducir la CPU del cliente durante las videollamadas, establezca esta directiva en &quot;Habilitado&quot; con un valor inferior a 85. Establezca esta directiva en &quot;Deshabilitado&quot; o &quot;Sin configurar&quot; para utilizar el umbral predeterminado de 85. Para no detectar el uso excesivo de CPU, establezca esta directiva en &quot;Habilitado&quot; con el valor 0. Esta configuración surtirá efecto en el siguiente inicio de sesión.</string>

         <string id="Enable_Sharing_Client_Screen_InAppSession">Al utilizar la aplicación Microsoft Teams como una aplicación publicada, permita el uso compartido de la pantalla del escritorio cliente</string>

         <string id="Enable_Sharing_Client_Screen_InAppSession_Desc">Cuando se utiliza la aplicación Microsoft Teams como una aplicación publicada; la función de compartir pantalla compartirá la pantalla del escritorio cliente. Desactive esta directiva para desactivar la función de uso compartido de pantalla mientras utiliza Microsoft Teams como una aplicación publicada. Si la directiva está activada o no está configurada, se podrá compartir la pantalla del escritorio cliente.</string>

         <string id="Enable_E911">Habilitar E911 para Microsoft Teams</string>

         <string id="Enable_E911_Desc">Mientras Microsoft Teams se ejecuta en modo optimizado, el cliente enviará datos E911 a Microsoft. Para deshabilitar el uso compartido de datos E911 con Microsoft, seleccione &quot;Deshabilitado&quot;. Si está habilitado o no está configurado, los datos del cliente E911 se compartirán con Microsoft.</string>

         <string id="Enable_HID">Habilitar el uso del botón de dispositivos HID cliente para Microsoft Teams</string>

         <string id="Enable_HID_Desc">Mientras Microsoft Teams se ejecuta en modo optimizado, el usuario puede utilizar el botón de dispositivos HID cliente para interactuar con Microsoft Teams. Para deshabilitar la compatibilidad con dispositivos HID cliente, seleccione &quot;Deshabilitado&quot;. Si está habilitado o no está configurado, se permitirá la compatibilidad con dispositivos HID cliente.</string>

         <string id="Enable_Webrtc_Appshare">Habilite el uso compartido de aplicaciones individuales para Microsoft Teams</string>

         <string id="Enable_Webrtc_Appshare_Desc">Microsoft Teams se ejecuta en modo optimizado, esta opción permite al usuario compartir aplicaciones individuales. Para deshabilitar el uso compartido de aplicaciones, seleccione &quot;Deshabilitado&quot;. Si está habilitado o no está configurado, se permitirá el uso compartido de aplicaciones.</string>

         <string id="Enable_Webrtc_Appshare_Givecontrol">Habilitar dar control para compartir aplicaciones individuales en Microsoft Teams</string>

         <string id="Enable_Webrtc_Appshare_Givecontrol_Desc">Microsoft Teams se ejecuta en modo optimizado. Esta opción permite al usuario dar el control sobre una aplicación individual compartida. Para deshabilitar la opción de dar el control mientras se comparten aplicaciones individuales, establezca esta directiva en &quot;Deshabilitado&quot;. Si se habilita o no se configura, se permitirá dar el control mientras se comparten aplicaciones individuales.</string>

         <string id="CustomBackgroundImages">Microsoft Teams imágenes de fondo personalizadas</string>

         <string id="Enable_Background_Effects">Habilitar efectos de fondo para Microsoft Teams</string>

         <string id="Enable_Background_Effects_Desc">Mientras Microsoft Teams se ejecuta en modo optimizado, los usuarios pueden seleccionar un fondo virtual para las llamadas y las reuniones. Para deshabilitar la compatibilidad con los efectos de fondo, seleccione &quot;Deshabilitado&quot;. Si está habilitado o no está configurado, se permitirá la compatibilidad con los efectos de fondo.</string>

         <string id="ForceEnableCustomBackgroundImages">Fuerce la habilitación o deshabilitación de la función de imágenes de fondo personalizadas para Microsoft Teams</string>

         <string id="ForceEnableCustomBackgroundImagesDesc">Mientras Microsoft Teams se ejecuta en modo optimizado, los usuarios pueden aplicar imágenes de fondo personalizadas durante las llamadas y las reuniones. Para deshabilitar la compatibilidad con imágenes de fondo personalizadas, seleccione &quot;Deshabilitado&quot;. Para forzar a los usuarios a utilizar solo imágenes de fondo personalizadas y evitar que se apliquen las imágenes de archivo proporcionadas en la interfaz de usuario de &quot;efectos de fondo&quot; de Microsoft Teams, seleccione &quot;Habilitado&quot;. Si selecciona &quot;Sin configurar&quot;, los usuarios podrán cambiar cuando quieran entre las imágenes de fondo personalizadas y las imágenes proporcionadas en la interfaz de usuario de Microsoft Teams.</string>

         <string id="CustomBackgroundImagesFolderPath">Especifique la carpeta para las imágenes de fondo personalizadas de Microsoft Teams</string>

         <string id="CustomBackgroundImagesFolderPathDesc">Mientras Microsoft Teams se ejecuta en modo optimizado, los usuarios pueden aplicar imágenes de fondo personalizadas seleccionadas desde una carpeta de imágenes cargadas por el administrador. Si está deshabilitado o no configurado, la carpeta en la que se deben cargar las imágenes es C:\Program Files\Common Files\Omnissa\Remote Experience\x64\MSTeamsBackgroundImages. Para utilizar una carpeta diferente, seleccione &quot;Habilitado&quot; y especifique la ruta a la carpeta en el cuadro de texto de la carpeta de imágenes de fondo personalizada, por ejemplo &quot;C:\Usuarios\Administrador\CustomBackgroundImagesFolder&quot;.</string>

         <string id="CustomBackgroundDefaultImageName">Seleccione una imagen de fondo personalizada predeterminada que se aplicará en caso de error del usuario</string>

         <string id="CustomBackgroundDefaultImageNameDesc">Especifique un nombre de imagen personalizado predeterminado que se aplicará en caso de que el usuario deje vacío el valor de registro imageName o introduzca un nombre de imagen personalizado no válido cuando la función de imagen de fondo personalizada esté habilitada.</string>

         <string id="Disable_Mirrored_Video">Deshabilitar la vista previa reflejada en Microsoft Teams</string>

         <string id="Disable_Mirrored_Video_Desc">De forma predeterminada, la vista previa del vídeo se muestra reflejada para Microsoft Teams en modo optimizado. Si establece esta opción, se deshabilitará el vídeo reflejado.</string>
         
         <string id="Enable_Webrtc_ProxyProbeUrl">Utilice la URL de sondeo de proxy personalizada para detectar el servidor proxy en funcionamiento.</string>

         <string id="Enable_Webrtc_ProxyProbeUrl_Desc">Cuando se configuran varios servidores proxy, especifique la URL de sondeo de proxy personalizada para sondear el servidor proxy en funcionamiento y utilícela en la Microsoft Teams llamada. Por ejemplo, https://teams.microsoft.com</string>

         <string id="Horizon_AppTap_Config">Configuración de Horizon AppTap</string>

         <string id="ProcessIgnoreList">Procesos que se deben ignorar al detectar una sesión de aplicación vacía</string>

         <string id="ProcessIgnoreList_Desc">Especifica la lista de procesos que se deben ignorar al detectar sesiones de aplicaciones vacías. Puede especificar el nombre de archivo o la ruta completa de un proceso. Estos valores se evalúan sin distinguir entre mayúsculas y minúsculas. No se permiten variables de entorno en las rutas. Las rutas de red UNC están permitidas (por ejemplo, \\Omnissa\temp\app.exe).</string>

         <string id="VDI_disconnect_time_till_logoff">Límite de tiempo de sesión desconectada (VDI)</string>

         <string id="VDI_disconnect_time_till_logoff_Desc">Especifica el tiempo que debe transcurrir para que se cierre automáticamente una sesión de escritorio VDI desconectada.
Si se selecciona &quot;Nunca&quot;, las sesiones de escritorio VDI desconectadas en esta máquina no se cerrarán nunca. Si se selecciona &quot;Inmediatamente&quot;, las sesiones desconectadas se cerrarán inmediatamente.

Existe una opción similar en Horizon Connection Server Administrator. Esta opción se encuentra en la configuración del grupo de escritorios y se llama &quot;Cerrar sesión automáticamente tras desconectarse&quot;. Si se configura esta opción y la de Horizon Connection Server Administrator, tendrá prioridad el valor seleccionado aquí.
Por ejemplo, si selecciona &quot;Nunca&quot; aquí, se evitará que las sesiones desconectadas (en esta máquina) se cierren, independientemente de lo que esté configurado en Horizon Connection Server Administrator.</string>

         <string id="RDS_idle_time_till_disconnect">Tiempo de inactividad de RDS hasta la desconexión</string>

         <string id="RDS_idle_time_till_disconnect_Desc">Especifica el tiempo tras el cual se desconectará automáticamente una sesión inactiva de Servicios de Escritorio remoto.
Si se selecciona &quot;Nunca&quot;, las sesiones de Servicios de Escritorio remoto en esta máquina no se desconectarán nunca.</string>

         <string id="RDS_disconnect_time_till_logoff">Tiempo de desconexión de RDS hasta el cierre de sesión</string>

         <string id="RDS_disconnect_time_till_logoff_Desc">Especifica el tiempo que debe transcurrir para que se cierre automáticamente una sesión de Servicios de Escritorio remoto desconectada.
Si se selecciona &quot;Nunca&quot;, las sesiones de Servicios de Escritorio remoto desconectadas en esta máquina no se cerrarán nunca.</string>

         <string id="RDS_active_time_till_disconnect">Tiempo de conexión de RDS hasta la desconexión</string>

         <string id="RDS_active_time_till_disconnect_Desc">Especifica la cantidad máxima de tiempo durante el que una sesión de Servicios de Escritorio remoto puede estar activa antes de que se desconecte automáticamente.
Si se selecciona &quot;Nunca&quot;, las sesiones de Servicios de Escritorio remoto en esta máquina no se desconectarán nunca.</string>

         <string id="RDS_end_session_time_limit">Finalización de sesión de RDS cuando se alcanza el límite de tiempo</string>

         <string id="RDS_end_session_time_limit_Desc">Especifica si finalizar una sesión de Servicios de Escritorio remoto que ha agotado el tiempo de espera en lugar de desconectarla.
Si se configura, la sesión finalizará (la sesión del usuario se cerrará y se eliminará del servidor) cuando se alcance el límite de tiempo para las sesiones activas o inactivas. De forma predeterminada, las sesiones de Servicios de Escritorio remoto se desconectan después de alcanzar su límite de tiempo.</string>

         <string id="RDS_threshold_connecting_session">Umbral de la sesión de conexión</string>

         <string id="RDS_threshold_connecting_session_Desc">Especifica el número máximo de sesiones que pueden iniciar sesión simultáneamente en la máquina RDSH, excluyendo las sesiones de reconexión.

Si se habilita, el valor del umbral de sesión se establece inicialmente en 20, pero deberá cambiarlo según el caso. Si se selecciona 0, se deshabilitará el umbral de conexión de la sesión.

Esta directiva está deshabilitada de forma predeterminada, por lo que si la directiva no está configurada, se deshabilitará el umbral de la sesión de conexión.</string>

         <string id="RDS_threshold_load_index">Umbral de índice de carga</string>

         <string id="RDS_threshold_load_index_Desc">Especifica el índice de carga mínimo en el que la máquina RDSH comenzará a denegar los inicios de sesión, excluyendo las sesiones de reconexión.

Si se habilita, el valor del umbral de carga se establece inicialmente en 0, pero deberá cambiarlo según el caso. Si se selecciona 0, se deshabilitará el umbral de índice de carga.

Esta directiva está deshabilitada de forma predeterminada, por lo que si la directiva no está configurada, se deshabilitará el umbral de índice de carga.</string>

         <string id="Prewarm_disconnect_time_till_logoff">Límite de tiempo de sesión de precalentamiento</string>

         <string id="Prewarm_disconnect_time_till_logoff_Desc">Especifica el tiempo que debe transcurrir para que se cierre automáticamente una sesión de precalentamiento.</string>

         <string id="EnableUWPOnRDSH">Habilitar soporte para UWP en plataformas RDSH</string>

         <string id="EnableUWPOnRDSH_Desc">Esta directiva controla si las aplicaciones de UWP pueden examinarse e iniciarse en granjas RDSH con una versión del sistema operativo que admita aplicaciones de UWP. Esta directiva no se aplica a las plataformas de SO de escritorio, como la comunicación remota de la aplicación VDI. Si se habilita, las aplicaciones de UWP se pueden usar como aplicaciones alojadas de granjas RDSH. Es necesario reiniciar el servicio wsnm o reiniciar el servidor RDSH para que el GPO surta efecto. En la documentación de Omnissa puede consultar las plataformas compatibles y si este ajuste estará habilitado o deshabilitado de forma predeterminada.</string>

        <string id="HandleLegalNoticeInWindow">Redireccionar mensajes de aviso legal como una ventana</string>

        <string id="HandleLegalNoticeInWindow_Desc">Cuando se habilita, esta directiva redirecciona los avisos legales a Horizon Client en una ventana con el tamaño especificado. La anchura y la altura de esta política se especifican en píxeles. Para monitores con PPP altos, los tamaños se multiplicarán en función de los PPP. Esta funcionalidad solo es compatible con las aplicaciones alojadas en RDSH.
Esta directiva está deshabilitada de forma predeterminada. Es necesario reiniciar el servidor RDSH y Horizon Client para que el GPO surta efecto.</string>

        <string id="TIME_NEVER">Nunca</string>

         <string id="TIME_1MIN">1 minuto</string>

         <string id="TIME_5MIN">5 minutos</string>

         <string id="TIME_10MIN">10 minutos</string>

         <string id="TIME_15MIN">15 minutos</string>

         <string id="TIME_30MIN">30 minutos</string>

         <string id="TIME_1HR">1 hora</string>

         <string id="TIME_2HR">2 horas</string>

         <string id="TIME_3HR">3 horas</string>

         <string id="TIME_6HR">6 horas</string>

         <string id="TIME_8HR">8 horas</string>

         <string id="TIME_10HR">10 horas</string>

         <string id="TIME_12HR">12 horas</string>

         <string id="TIME_18HR">18 horas</string>

         <string id="TIME_1D">1 día</string>

         <string id="TIME_2D">2 días</string>

         <string id="TIME_3D">3 días</string>

         <string id="TIME_4D">4 días</string>

         <string id="TIME_5D">5 días</string>

         <string id="TIME_1W">1 semana</string>

         <string id="TIME_IMMEDIATELY">Inmediatamente</string>

         <string id="EnableBatStatRedir">Habilitar el redireccionamiento del estado de la batería</string>

         <string id="EnableDisplayNetworkState">Habilitar la visualización del estado de la red</string>
         <string id="EnableDisplayNetworkStateExplain">Este ajuste permite establecer si desea mostrar mensajes de estado de la red en la interfaz de usuario de Horizon Client. Cuando está habilitado, si la conexión de la red es deficiente, el usuario final recibirá una notificación sobre el estado de red. Cuando está deshabilitado, si la conexión de la red es deficiente, el usuario final recibirá una notificación sobre el estado de red. Esta propiedad está habilitada de forma predeterminada.</string>

         <string id="EnableBatStatRedir_Desc">Esta directiva controla si el redireccionamiento del estado de la batería está habilitado. Cuando esta directiva no está configurada, el redireccionamiento del estado de la batería está habilitado.</string>
         <string id="Horizon_WaterMark">Marca de agua</string>
         <string id="Horizon_Watermark_Config">Configuración de marca de agua</string>
         <string id="Desktop_Watermark_Configuration_Desc">Esta opción le permite configurar una marca de agua para que aparezca en el escritorio virtual. En el área &quot;Texto&quot; puede definir lo que se mostrará en la marca de agua. Las opciones son:

%ViewClient_IP_Address%
%ViewClient_Broker_UserName%
%ViewClient_Broker_DomainName%
%COMPUTERNAME%
%USERDOMAIN%
%USERNAME%
%ViewClient_ConnectTime% - Fecha (mes/día/año)
%ViewClient_ConnectTicks% - Hora (hora:minuto:segundo)

Este es un ejemplo del área &quot;Texto&quot;:
%USERDOMAIN%\%USERNAME%
%COMPUTERNAME% estuvo activo durante %ViewClient_ConnectTime%
%ViewClient_IP_Address%

El límite de caracteres del área &quot;Texto&quot; es de 256 caracteres, y de 1024 caracteres después de la expansión.

&quot;Diseño de imagen&quot; especifica el diseño de la marca de agua. Se admiten Mosaico, Múltiple y Centro. La opción Múltiple colocará la marca de agua en el centro y en cada una de las esquinas. En las sesiones de aplicaciones, esta opción se ignora y el diseño siempre será de mosaico.
&quot;Rotación de texto&quot; permite seleccionar un ángulo de rotación para el texto de la marca de agua.
&quot;Opacidad&quot; permite seleccionar la transparencia del texto.
&quot;Margen&quot; especifica la distancia entre la marca de agua y el borde de la pantalla del escritorio virtual, y solo se aplica al diseño de mosaico.
&quot;Color del texto&quot; especifica el color del texto de la marca de agua utilizando valores de color RGB separados por espacios en decimal, y el contorno del texto se representa en color de contraste. De forma predeterminada, el texto se representa en blanco y el contorno aparece en negro.
&quot;Tamaño de fuente&quot; especifica el tamaño del texto de la marca de agua. Cuando este valor es 0, se aplicará el tamaño de fuente predeterminado.
&quot;Intervalo de actualización&quot; especifica el intervalo en segundos durante el que se actualiza la marca de agua. Cuando se especifica 0, se deshabilitará la actualización de la marca de agua. El máximo es 86400 segundos (24 horas).
</string>
         <string id="Tile">Mosaico</string>
         <string id="Multiple">Múltiple</string>
         <string id="Center">Centro</string>
         <string id="TextColor">Color del texto</string>
         <string id="FontSize">Tamaño de fuente</string>
         <string id="RefreshInterval">Intervalo de actualización</string>
         <string id="BlockScreenCapture">Bloqueo de capturas de pantalla</string>
         <string id="BlockScreenCapture_Desc">Determina si el usuario final puede tomar capturas de pantalla de su aplicación remota o escritorio virtual desde su dispositivo. Esta opción solo se puede aplicar en las versiones de Horizon Client 2106 y posteriores para Windows y para Mac. El valor predeterminado está deshabilitado, lo que permite al usuario final tomar capturas de pantalla desde su dispositivo.

Habilitar: bloquea a los usuarios finales para que no puedan sacar capturas de pantalla de las aplicaciones virtuales o el escritorio virtual desde sus dispositivos Windows o macOS.

Deshabilitar: permite que los usuarios finales tomen capturas de pantalla desde su dispositivo.


&quot;Permitir las grabaciones de pantalla en Horizon Mac Client&quot; determina si el usuario final puede realizar grabaciones de pantalla de su escritorio virtual o sus aplicaciones remotas desde su dispositivo cuando el GPO &quot;Bloqueo de capturas de pantalla&quot; está habilitado. Este ajuste solo se puede aplicar en Horizon Client 2309 para Mac y versiones posteriores. Está desmarcada de forma predeterminada, lo que no permite que los usuarios hagan grabaciones de pantalla desde sus dispositivos.

Marcada: permite a los usuarios finales hacer grabaciones de pantalla del escritorio virtual o las aplicaciones virtuales desde sus dispositivos macOS.

Sin marcar: impide que los usuarios finales hagan grabaciones de pantalla desde sus dispositivos macOS.</string>
         <string id="BlockThumbnailRepresentationWhenMinimized">Bloquear la representación de miniaturas cuando se minimiza</string>
         <string id="BlockThumbnailRepresentationWhenMinimized_Desc">Determina si al colocar el cursor sobre la miniatura del escritorio remoto, se mostrará el contenido del escritorio remoto cuando se minimice su ventana.
Cuando está habilitada, se mostrará el icono de la aplicación Horizon Client en lugar del contenido del escritorio remoto para la miniatura de la ventana y la vista previa dinámica cuando la ventana esté minimizada.
Cuando está deshabilitada o no está configurada, se mostrará la última instantánea del escritorio remoto antes de minimizarse como miniatura de la ventana y vista previa dinámica. Este GPO solo se aplica en endpoints de Windows.</string>
         <string id="ScreenCaptureForMediaOffloaded">Capturas de pantalla para solución de descarga multimedia</string>
         <string id="ScreenCaptureForMediaOffloaded_Desc">Habilite el usuario final para que haga capturas de pantalla para el escritorio del agente VDI cuando la sesión de medios se descargue en los dispositivos.</string>
         <string id="AntiKeyLogger">Bloqueo de registrador de teclas</string>
         <string id="AntiKeyLogger_Desc">Determina si el dispositivo cifra la comunicación entre el teclado y Horizon Client para evitar el malware de registro de claves en el dispositivo. La conexión inicial a Horizon Server siempre está protegida, independientemente de la configuración de GPO de la máquina virtual. Después de la autenticación inicial, esta opción determina si todos los tipos de escritura en el dispositivo están cifrados. Esta opción solo se puede aplicar en Horizon Client 2111 para Mac y Horizon Client 2203 para Windows o versiones posteriores. El valor predeterminado está deshabilitado.

Habilitar: se cifran todas las pulsaciones de teclas entre el teclado y el Horizon Client.

Deshabilitar: las pulsaciones de teclas se comunican normalmente en el dispositivo.</string>
         <string id="BlockSendInput">Bloqueo de pulsaciones de teclas sintéticas</string>
         <string id="BlockSendInput_Desc">Determina si el endpoint bloquea scripts que automatizan las pulsaciones de teclas del endpoint en una aplicación o un escritorio virtuales. La conexión inicial a Horizon Server siempre está protegida, independientemente de la configuración de GPO de la máquina virtual. Después de la autenticación inicial, este ajuste determina si se bloquean todas las pulsaciones de teclas sintéticas en el endpoint. Este ajuste solo se puede aplicar en Horizon Client 2312 para Windows o versiones posteriores. El valor predeterminado es deshabilitado.

Si &quot;Bloqueo de registrador de teclas&quot; no está habilitado, este ajuste no tendrá efecto.

Habilitado: Todas las pulsaciones de teclas sintéticas del endpoint se bloquearán en aplicaciones virtuales o escritorios virtuales.

Deshabilitado: Horizon Client reenviará las pulsaciones de teclas sintéticas como siempre.</string>
         <string id="AllowFIDO2AuthenticatorAccess">Permitir acceso al autenticador de FIDO2</string>
         <string id="AllowFIDO2AuthenticatorAccess_Desc">Determina si las aplicaciones del escritorio remoto pueden acceder a los autenticadores FIDO2 del endpoint. Cuando se deshabilita, las aplicaciones del escritorio remoto no pueden acceder a los autenticadores FIDO2 del endpoint. Cuando está habilita o no está configurada, las aplicaciones del escritorio remoto pueden acceder a los autenticadores FIDO2 del endpoint.</string>
         <string id="FIDO2AllowList">Lista de permitidos de FIDO2</string>
         <string id="FIDO2AllowList_Desc">Una lista de aplicaciones que pueden acceder a los autenticadores FIDO2 del endpoint.

La sintaxis es:
   nombreaplicación1.exe;nombreaplicación2.exe

Cuando esta opción no está configurada o está deshabilitada, se utilizará la lista predeterminada. La lista predeterminada es:
   chrome.exe;firefox.exe;msedge.exe</string>

         <string id="WaitForHybridJoin">Configurar la espera de unión híbrida</string>

         <string id="WaitForHybridJoin_Desc">Este objeto de directiva de grupo (GPO) controla el comportamiento del agente en relación con el proceso de unión híbrida de Microsoft Entra ID. Determina si el agente debe esperar a que finalice el proceso de unión híbrida antes de poder atender las solicitudes de aplicaciones o escritorios.

Deshabilitado o no configurado: cuando este ajuste está deshabilitado o no está configurado, el agente no esperará a que se complete el proceso de unión híbrida. Eso significa que el agente podrá empezar a atender solicitudes de inmediato, posiblemente antes de que la máquina se integre completamente en Entra ID.

Habilitado: cuando está habilitado, el agente esperará a que la máquina complete correctamente el proceso de unión híbrida con Entra ID. Solo después de que se complete este proceso, el agente se marcará a sí mismo como DISPONIBLE, lo que indicará que está listo para atender solicitudes de aplicaciones o escritorios.

Habilitar esta función es fundamental para garantizar que el agente esté totalmente integrado en Entra ID antes de comenzar a atender solicitudes. Esta integración es necesaria para funciones como el inicio de sesión único (SSO) en los recursos de Azure/Office y para que se reconozca el dispositivo en Entra ID en tareas de administración. Sin embargo, es importante tener en cuenta que habilitar esta función puede provocar un retraso significativo en la disponibilidad de las máquinas, ya que el agente esperará a que finalice el proceso de unión híbrida.
         </string>

         <string id="IpPrefix">Configurar la subred que utiliza Horizon Agent</string>

         <string id="IpPrefixDesc">Cuando instala Horizon Agent en una máquina virtual que tiene más de una NIC, debe configurar la subred que utiliza Horizon Agent. La subred determina qué dirección de red proporciona Horizon Agent a la instancia de Connection Server o Connection Service para las conexiones de protocolo del cliente.

La sintaxis es:
   n.n.n.n/m

En este ejemplo, n.n.n.n es la subred TCP/IP y m es el número de bits en la máscara de subred.

Valor de ejemplo:
   ***********/21

En este ejemplo, Horizon Agent solo acepta direcciones IP del rango entre *********** y *************.
         </string>

      </stringTable>

      <presentationTable>
         <presentation id="MaxCollaboratorCount">
            <decimalTextBox refId="MaxCollaboratorCount_DB" defaultValue="5">Número máximo</decimalTextBox>
         </presentation>

         <presentation id="CollaborationEmailInviteDelimiter">
            <textBox refId="CollaborationEmailInviteDelimiter_TB">
               <label>Separador entre direcciones de correo electrónico</label>
            </textBox>
         </presentation>

         <presentation id="CollaborationServerURLs">
            <listBox refId="CollaborationServerURLs_list">Lista de nombres y URL de servidores externos</listBox>
         </presentation>

         <presentation id="ConnectionTicketTimeout">
            <decimalTextBox refId="ConnectionTicketTimeout_DB" defaultValue="120">Tiempo de espera del ticket de conexión</decimalTextBox>
         </presentation>

         <presentation id="CredentialFilterExceptions">
            <textBox refId="CredentialFilterExceptions_TB">
               <label>Excepciones de filtro de credenciales</label>
            </textBox>
         </presentation>
         <presentation id="RDPVcBridgeUnsupportedClients">
            <textBox refId="RDPVcBridgeUnsupportedClients_TB">
               <label>Clientes no compatibles con RDPVcBridge</label>
            </textBox>
         </presentation>

         <presentation id="CommandsToRunOnConnect">
            <listBox refId="CommandsToRunOnConnect_list">Comandos</listBox>
         </presentation>

         <presentation id="CommandsToRunOnReconnect">
            <listBox refId="CommandsToRunOnReconnect_list">Comandos</listBox>
         </presentation>

         <presentation id="CommandsToRunOnDisconnect">
            <listBox refId="CommandsToRunOnDisconnect_list">Comandos</listBox>
         </presentation>

         <presentation id="SSO_retry_timeout">
            <decimalTextBox refId="SSO_retry_timeout_DB" defaultValue="5000">Tiempo de espera para nuevo intento de inicio de sesión único</decimalTextBox>
         </presentation>

         <presentation id="RDS_threshold_connecting_session">
            <decimalTextBox refId="RDS_threshold_connecting_session_DB" defaultValue="20">Valor del umbral de la sesión de conexión</decimalTextBox>
         </presentation>

         <presentation id="RDS_threshold_load_index">
            <decimalTextBox refId="RDS_threshold_load_index_DB" defaultValue="0">Valor de umbral de índice de carga</decimalTextBox>
         </presentation>

         <presentation id="Win10PhysicalAgentAudioOption">
            <dropdownList refId="Win10PhysicalAgentAudioOption_DDL" defaultItem="0">Opción de audio para máquina de escritorio remoto físico con Windows 10 de sesión única</dropdownList>
         </presentation>

         <presentation id="WaitForLogoff">
            <decimalTextBox refId="WaitForLogoff_DB" defaultValue="10">Esperar a que se agote el tiempo de espera de cierre de sesión</decimalTextBox>
         </presentation>

         <presentation id="Accept_SSL_encr_framework_channel">
            <dropdownList refId="Accept_SSL_encr_framework_channel_DDL" defaultItem="0">Aceptar canal de marco con cifrado SSL</dropdownList>
         </presentation>

         <presentation id="Local_Reader_Name">
            <textBox refId="Local_Reader_Name_TB">
               <label>Nombre de lector local</label>
            </textBox>
         </presentation>

         <presentation id="Cert_wait_timeout">
            <decimalTextBox refId="Cert_wait_timeout_DB" defaultValue="40">Tiempo de espera de certificados</decimalTextBox>
         </presentation>

         <presentation id="Min_key_size">
            <decimalTextBox refId="Min_key_size_DB" defaultValue="1024">Tamaño mínimo de claves</decimalTextBox>
         </presentation>

         <presentation id="All_key_sizes">
            <textBox refId="All_key_sizes_TB">
               <label>Todos los tamaños de clave</label>
                  <defaultValue>2048</defaultValue>
            </textBox>
         </presentation>

         <presentation id="Keys_to_precreate">
            <decimalTextBox refId="Keys_to_precreate_DB" defaultValue="5">Cantidad de claves que crear previamente</decimalTextBox>
         </presentation>

         <presentation id="Cert_min_validity">
            <decimalTextBox refId="Cert_min_validity_DB" defaultValue="10">Periodo mínimo de validez necesario para un certificado</decimalTextBox>
         </presentation>

         <presentation id="WhfbCertificateAllowedApplicationsList">
            <multiTextBox refId="Whfb_Certificate_Allowed_Applications_list_TB">Lista de ejecutables permitidos</multiTextBox>
         </presentation>

         <presentation id="Exclude_Vid_Pid">
            <textBox refId="Exclude_Vid_Pid_TB">
               <label>Excluir un dispositivo Vid/Pid</label>
            </textBox>
         </presentation>

         <presentation id="Exclude_Vid_Pid_Rel">
            <textBox refId="Exclude_Vid_Pid_Rel_TB">
               <label>Excluir un dispositivo Vid/Pid/Rel</label>
            </textBox>
         </presentation>

         <presentation id="Include_Vid_Pid">
            <textBox refId="Include_Vid_Pid_TB">
               <label>Incluir un dispositivo Vid/Pid</label>
            </textBox>
         </presentation>

         <presentation id="Include_Vid_Pid_Rel">
            <textBox refId="Include_Vid_Pid_Rel_TB">
               <label>Incluir un dispositivo Vid/Pid/Rel</label>
            </textBox>
         </presentation>

         <presentation id="Exclude_device_family">
            <textBox refId="Exclude_device_family_TB">
               <label>Excluir familia de dispositivos</label>
            </textBox>
         </presentation>

         <presentation id="Include_device_family">
            <textBox refId="Include_device_family_TB">
               <label>Incluir familia de dispositivos</label>
            </textBox>
         </presentation>

         <presentation id="HidOpt_Include_Vid_Pid">
            <textBox refId="HidOpt_Include_Vid_Pid_TB">
               <label>Incluir el dispositivo VID/PID de optimización de HID</label>
            </textBox>
         </presentation>

         <presentation id="Exclude_Auto_Vid_Pid">
            <textBox refId="Exclude_Auto_Vid_Pid_TB">
               <label>Evitar conexión automática de dispositivo Vid/Pid</label>
            </textBox>
         </presentation>

         <presentation id="Exclude_auto_device_family">
            <textBox refId="Exclude_auto_device_family_TB">
               <label>Evitar conexión automática de familia de dispositivos</label>
            </textBox>
         </presentation>

         <presentation id="Exclude_Vid_Pid_from_Split">
            <textBox refId="Exclude_Vid_Pid_from_Split_TB">
               <label>Evitar que un dispositivo Vid/Pid se divida</label>
            </textBox>
         </presentation>

         <presentation id="Split_Vid_Pid_Device">
            <textBox refId="Split_Vid_Pid_Device_TB">
               <label>Dividir un dispositivo Vid/Pid</label>
            </textBox>
         </presentation>

         <presentation id="Allow_other_input_devices">
            <dropdownList refId="Allow_other_input_devices_DDL" defaultItem="0">Permitir otros dispositivos de entrada</dropdownList>
         </presentation>

         <presentation id="Allow_HID_Bootable">
            <dropdownList refId="Allow_HID_Bootable_DDL" defaultItem="0">Permitir dispositivos HID de arranque</dropdownList>
         </presentation>

         <presentation id="Allow_Audio_Input_devices">
            <dropdownList refId="Allow_Audio_Input_devices_DDL" defaultItem="0">Permitir dispositivos de entrada de audio</dropdownList>
         </presentation>

         <presentation id="Allow_Audio_Output_devices">
            <dropdownList refId="Allow_Audio_Output_devices_DDL" defaultItem="0">Permitir dispositivos de salida de audio</dropdownList>
         </presentation>

         <presentation id="Allow_keyboard_mouse">
            <dropdownList refId="Allow_keyboard_mouse_DDL" defaultItem="0">Permitir dispositivos de teclado y mouse</dropdownList>
         </presentation>

         <presentation id="Allow_Video_Devices">
            <dropdownList refId="Allow_Video_Devices_DDL" defaultItem="0">Permitir dispositivos de vídeo</dropdownList>
         </presentation>

         <presentation id="Allow_Smart_Cards">
            <dropdownList refId="Allow_Smart_Cards_DDL" defaultItem="0">Permitir tarjetas inteligentes</dropdownList>
         </presentation>

         <presentation id="Allow_Auto_Device_Splitting">
            <dropdownList refId="Allow_Auto_Device_Splitting_DDL" defaultItem="0">Permitir la división automática del dispositivo</dropdownList>
         </presentation>

         <presentation id="Accept_SSL_encr_framework_channel_2">
            <dropdownList refId="Accept_SSL_encr_framework_channel_2_DDL" defaultItem="0">Aceptar canal de marco con cifrado SSL</dropdownList>
         </presentation>

         <presentation id="Default_proxy_server">
            <textBox refId="Default_proxy_server_TB">
               <label>Servidor proxy predeterminado</label>
            </textBox>
         </presentation>

         <presentation id="Update_Java_Proxy">
            <dropdownList refId="Update_Java_Proxy_DDL" defaultItem="2">Establecer proxy para applet Java</dropdownList>
         </presentation>

         <presentation id="HTML5MMRUrlList">
            <listBox refId="HTML5MMRUrlList_list">La lista de URL para Redireccionamiento multimedia HTML5 de Horizon.</listBox>
         </presentation>

         <presentation id="HTML5MMRUrlBlockList">
            <listBox refId="HTML5MMRUrlBlockList_list">La lista de URL que se excluirá del Redireccionamiento multimedia HTML5 de Horizon.</listBox>
         </presentation>

         <presentation id="GeoRedirUrlList">
            <listBox refId="GeoRedirUrlList_list">La lista de URL para habilitar la función de redireccionamiento de geolocalización de Horizon.</listBox>
         </presentation>

         <presentation id="GeoRedirDistanceDelta">
            <textBox refId="GeoRedirDistanceDelta_value">
               <label>La distancia mínima en metros</label>
               <defaultValue>75</defaultValue>
            </textBox>
         </presentation>

         <presentation id="WebrtcProxyProbeUrl">
            <textBox refId="WebrtcProxyProbeUrl_value">
               <label>Utilice la URL para sondear el servidor proxy para las llamadas webrtc</label>
            </textBox>
         </presentation>


         <presentation id="BrowserRedirUrlList">
            <listBox refId="BrowserRedirUrlList_list">La lista de URL para habilitar la función de redireccionamiento de navegador de Horizon.</listBox>
         </presentation>

         <presentation id="EnhBrowserRedirUrlList">
            <listBox refId="EnhBrowserRedirUrlList_list">La lista de URL para habilitar la función de redireccionamiento de navegador mejorado de Horizon.</listBox>
         </presentation>

         <presentation id="BrowserRedirNavUrlList">
            <listBox refId="BrowserRedirNavUrlList_list">La lista de URL para habilitar la navegación para la función de redireccionamiento de navegador de Horizon.</listBox>
         </presentation>

         <presentation id="WebRTC_SDK_Web_App_Redir_Allow_List">
            <listBox refId="WebRTC_SDK_Web_App_Redir_Allow_List_list">La lista de URL donde se habilitará la compatibilidad con el SDK de Horizon WebRTC en aplicaciones web.</listBox>
         </presentation>

         <presentation id="SetForceNonOptimizedOptions">
            <checkBox refId="AUTO_DETECT_EXTERNAL_CONNECTIONS_CHKBOX">Detectar conexiones externas automáticamente</checkBox>
            <textBox refId="SetForceNonOptimizedEnvVar_TB">
               <label>El nombre de variable de entorno:</label>
            </textBox>
         </presentation>

         <presentation id="UnityFilterRules_Filter">
            <listBox refId="UnityFilterRules_FilterList">Reglas de filtro de Unity</listBox>
         </presentation>

         <presentation id="Enable_Unity_Touch">
            <checkBox refId="Enabled_UWP_For_UnityTouch_CB" defaultChecked="true">Habilitar el soporte de la aplicación de Universal Windows Platform (UWP) para Unity Touch en Windows 10.</checkBox>
         </presentation>

         <presentation id="ProcessIgnoreList">
            <multiTextBox refId="ProcessIgnoreList_TB">Procesos que se deben ignorar al detectar sesiones de aplicaciones vacías</multiTextBox>
         </presentation>

         <presentation id="VDI_idle_time_till_disconnect">
            <dropdownList refId="VDI_idle_time_till_disconnect_minutes" noSort="true" defaultItem="0">Tiempo de espera de inactividad</dropdownList>
         </presentation>

         <presentation id="VDI_disconnect_time_till_logoff">
            <dropdownList refId="VDI_disconnect_time_till_logoff_minutes" noSort="true" defaultItem="0">Tiempo de espera de desconexión</dropdownList>
         </presentation>

         <presentation id="RDS_idle_time_till_disconnect">
            <dropdownList refId="RDS_idle_time_till_disconnect_milliseconds" noSort="true" defaultItem="0">Tiempo de espera de inactividad de RDS</dropdownList>
         </presentation>

         <presentation id="RDS_disconnect_time_till_logoff">
            <dropdownList refId="RDS_disconnect_time_till_logoff_milliseconds" noSort="true" defaultItem="0">Tiempo de espera de desconexión de RDS</dropdownList>
         </presentation>

         <presentation id="RDS_active_time_till_disconnect">
            <dropdownList refId="RDS_active_time_till_disconnect_milliseconds" noSort="true" defaultItem="0">Tiempo de espera de conexión de RDS</dropdownList>
         </presentation>

         <presentation id="Prewarm_disconnect_time_till_logoff">
            <dropdownList refId="Prewarm_disconnect_time_till_logoff_minutes" noSort="true" defaultItem="5">Tiempo de espera de precalentamiento</dropdownList>
         </presentation>

         <presentation id ="watermark_configuration">
            <multiTextBox refId="Text">Texto</multiTextBox>
            <dropdownList refId="ImageFit" noSort="true" defaultItem="0">Diseño de imagen</dropdownList>
            <decimalTextBox refId="Rotation" defaultValue="45">Rotación de texto</decimalTextBox>
            <decimalTextBox refId="Opacity" defaultValue="50">Opacidad</decimalTextBox>
            <decimalTextBox refId="Margin" defaultValue="50">Margen</decimalTextBox>
            <textBox refId="TextColor">
               <label>Color del texto</label>
               <defaultValue>255 255 255</defaultValue>
            </textBox>
            <decimalTextBox refId="FontSize" defaultValue="0">Tamaño de fuente</decimalTextBox>
            <decimalTextBox refId="RefreshInterval" defaultValue="0">Intervalo de actualización</decimalTextBox>
         </presentation>

        <presentation id="AppSignInWindow">
            <decimalTextBox refId="app_sign_in_window_width" defaultValue="800" spinStep="2">Ancho de ventana de aviso legal: </decimalTextBox>
            <decimalTextBox refId="app_sign_in_window_height" defaultValue="600" spinStep="2">Altura de ventana de aviso legal: </decimalTextBox>
        </presentation>
        <presentation id="Video_Cpu_Overuse_Threshold">
            <decimalTextBox refId="Video_Cpu_Overuse_Threshold_data" defaultValue="85">Umbral de uso excesivo de CPU de vídeo</decimalTextBox>
         </presentation>
         <presentation id="Webrtc_Recommended_AEC">
            <checkBox refId="Webrtc_Recommended_AEC_CB" defaultChecked="true"> Usar algoritmo de AEC recomendado </checkBox>
         </presentation>
         <presentation id="Force_Enable_Teams_Redir">
            <checkBox refId="Force_Enable_Teams_Redir_Value" defaultChecked="false"> Forzar optimización de WebRTC en el cliente </checkBox>
         </presentation>
         <presentation id="Force_Enable_Electron_App_Redir">
            <checkBox refId="Force_Enable_Electron_App_Redir_Value" defaultChecked="false"> Forzar optimización de WebRTC en el cliente </checkBox>
         </presentation>
         <presentation id="FIDO2AllowList">
            <textBox refId="FIDO2AllowList_TB">
               <label>Lista de permitidos de FIDO2</label>
               <defaultValue>chrome.exe;firefox.exe;msedge.exe</defaultValue>
            </textBox>
         </presentation>
         <presentation id="AllowScreenRecording">
            <checkBox refId="AllowScreenRecording_CB" defaultChecked="false"> Permitir las grabaciones de pantalla en Horizon Mac Client </checkBox>
         </presentation>
         <presentation id="CustomBackgroundImagesFolderPath">
            <textBox refId="CustomBackgroundImagesFolder_Path">
               <label>Carpeta de imágenes de fondo personalizadas</label>
            </textBox>
         </presentation>
         <presentation id="CustomBackgroundDefaultImageName">
            <textBox refId="CustomBackgroundDefaultImageName_Value">
               <label>Nombre de imagen predeterminado</label>
            </textBox>
         </presentation>
         <presentation id="EnableDisplayNetworkState">
            <decimalTextBox refId="NetworkWarningInterval_CB" defaultValue="5">El intervalo de tiempo del mensaje emergente de advertencia de red en minutos. Máximo 60 minutos, mínimo 1 minuto. El valor predeterminado es 5 minutos.</decimalTextBox>
         </presentation>
         <presentation id="IpPrefix" >
            <textBox refId="IpPrefixTextBox" >
               <label >Prefijo de IP</label>
            </textBox>
         </presentation>
      </presentationTable>
   </resources>
</policyDefinitionResources>
