import unittest
import pytest
from testCaseBase import TestCaseBase, broker, agent, catch_exception
import lib.kits
import lib.vadc
import lib.bdweb as bdweb
import configparser
import lib.auto
import lib.Tool as tool
vadc = lib.vadc.vadc

cached_values = lib.kits.cached_values

class Workflow(TestCaseBase):
    
    @pytest.fixture(autouse=True)
    def get_args(self, request):
        self.buildnum = int(request.config.getoption("--buildnum"))
        self.Feature = request.config.getoption("--featurename")
        self.updateChrome = request.config.getoption("--updatechromedriver")
        lib.auto.sendEmail = request.config.getoption("--sendemail")
        print("Printing buildnum %s" % self.buildnum)
        if self.buildnum == -1:
            print('Getting Latest Build')
            self.buildnum = bdweb.getLatestBuildNumber()
            print('Build Number: %s' % self.buildnum)
        config = configparser.ConfigParser()
        config.optionxform = str
        config.read(lib.auto.configFile)
        config['buildinfo']['buildnum'] = self.buildnum
        with open(lib.auto.configFile, 'w') as configfile:
            config.write(configfile)
        lib.auto.buildInfo['buildnum'] = self.buildnum
        lib.auto.buildNum = self.buildnum

    """
    LinuxVDI e2e
    """
    def setUp(self):
        """
        -----------------------------------------------------------
        @Mandatory:
        baseVM: base VM name
        ssFresh: snapshot which is ready to install ob, need create
                  before the test running
        CustomSpecName: vCenter customization specification name, should be a Linux one
        installArg: installer script paramters

        @Optional:
        pool: will create Full Clone Pool with the name
        VcID: will use the vCenter of auto.ini if None
        VmFolder: will use same vmFolder as baseVM if None
        HostOrCluster: will use same HostOrCluster as base VM if None
        ResourcePool: will use same ResourcePool as base VM if None
        datastore: will use same datastore as baseVM if None
        NamingPattern: IC pool VM naming pattern
        MaximumCount: IC pool VM quantity

        """
        self.poolInfo = lib.auto.GetConfigData()
        if self.updateChrome == "true":
            tool.updateChromeDriver()
        self.Description = 'E2E tests'
    
        if self._testMethodName == 'test_01':
            self.Description = "Test Manual pool creation"
            self.Name = self.Description
            self.TCMSID = '1'
        if self._testMethodName == 'test_02':
            self.Description = 'PendingConnection'
            self.Name = self.Description
            self.TCMSID = '2'
        if self._testMethodName == 'test_03':
            self.Description = 'InvalidConnection'
            self.Name = self.Description
            self.TCMSID = '3'
        if self._testMethodName == 'test_04':
            self.Description = 'BrokerTimeout'
            self.Name = self.Description
            self.TCMSID = '4'
        if self._testMethodName == 'test_05':
            self.Description = 'ReconnectProvisionedVM'
            self.Name = self.Description
            self.TCMSID = '5'
        if self._testMethodName == 'test_06':
            self.Description = "CollabConnectionbyAPI"
            self.Name = self.Description
            self.TCMSID = '6'
        if self._testMethodName == 'test_07':
            self.Description = 'PendingConnection'
            self.Name = self.Description
            self.TCMSID = '7'
        if self._testMethodName == 'test_08':
            self.Description = 'Multimon'
            self.Name = self.Description
            self.TCMSID = '8'
        if self._testMethodName == 'test_09':
            self.Description = 'Multimon4K'
            self.Name = self.Description
            self.TCMSID = '9'
        if self._testMethodName == 'test_10':
            self.Description = 'HelpdeskWorking'
            self.Name = self.Description
            self.TCMSID = '10'
        if self._testMethodName == 'test_11':
            self.Description = "HelpdeskBlast"
            self.Name = self.Description
            self.TCMSID = '11'
        if self._testMethodName == 'test_12':
            self.Description = 'HelpdeskClient'
            self.Name = self.Description
            self.TCMSID = '12'
        if self._testMethodName == 'test_13':
            self.Description = 'HelpdeskProcess'
            self.Name = self.Description
            self.TCMSID = '13'
        if self._testMethodName == 'test_14':
            self.Description = 'HelpdeskHistory'
            self.Name = self.Description
            self.TCMSID = '14'
        if self._testMethodName == 'test_15':
            self.Description = 'Disconnect_Logoff'
            self.Name = self.Description
            self.TCMSID = '15'
        if self._testMethodName == 'test_16':
            self.Description = "Disconnect_Logoff"
            self.Name = self.Description
            self.TCMSID = '16'
        if self._testMethodName == 'test_17':
            self.Description = "DEM smart policy"
            self.Name = self.Description
            self.TCMSID = '17'
       # reset per workflow variables for agent/broker/client
        broker.brokerDomain = self.poolInfo['domain']
        agent.user          = self.poolInfo['user']
        agent.password      = self.poolInfo['password']
        TestCaseBase.setUp(self)

    def tearDown(self):
        """
        This function will be called after each case running
        """
        TestCaseBase.tearDown(self)

    @catch_exception
    def test_01(self):
        self.New_ManualPool(self.poolInfo, ssoDesktopType=self.poolInfo['SSODesktopType'])

    @catch_exception 
    def test_02(self):
        self.PendingConnection()

    @catch_exception 
    def test_03(self):
        self.InvalidConnection()
    
    @catch_exception 
    def test_04(self):
        self.BrokerTimeout()

    @catch_exception 
    def test_05(self):
        self.ReconnectProvisionedVM()

    def test_06(self):
        #self.CollabConnectionbyAPI(users={"rha":"win,cmd"}, removeUsers=None, times=2, scenario='end', bypassBSG=False, h264=True)
        pass

    def test_07(self):
        #self.CollabConnectionbyAPI(users={"rha":"win,cmd"}, removeUsers=None, times=2, scenario='end', bypassBSG=False, h264=True)
        pass

    def test_08(self):
        self.Multimon(remote=False, resList=['0+0+1920x1080', '1920+0+1920x1080', '0+1080+1920x1080', '1920+1080+1920x1080'])

    def test_09(self):
        self.Multimon4K()

    @catch_exception
    def test_10(self):
        self.HelpdeskWorking()

    @catch_exception
    def test_11(self):
        self.HelpdeskBlast()

    @catch_exception
    def test_12(self):
        self.HelpdeskClient()
        pass

    @catch_exception
    def test_13(self):
        self.HelpdeskProcess()

    @catch_exception
    def test_14(self):
        self.HelpdeskHistory()

    @catch_exception
    def test_15(self):
        broker.Disconnect_Logoff(pool=self.poolInfo['pool'], action='disconnect')
        self.HelpdeskDisconnect()

    @catch_exception
    def test_16(self):
        self.HelpdeskReconnect()
        broker.Disconnect_Logoff(pool=self.poolInfo['pool'], action='logoff')

    @catch_exception
    def test_17(self):
        self.DEMSmartPolicy(self.poolInfo)

if __name__ == '__main__':
    unittest.main()