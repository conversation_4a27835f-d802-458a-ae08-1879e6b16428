# ************************************************************************
# Copyright (c) Omnissa, LLC. All rights reserved.
# This product is protected by copyright and intellectual property laws in the
# United States and other countries as well as by international treaties.
# -- Omnissa Restricted
# ************************************************************************

#
# vncServer.sc
#
#   The Blast server used in the Horizon/View agents.
#   Mailing lists: <EMAIL>, <EMAIL>

import os
import vmware
import vtools.common

Import("env_opts")

vncName = "vncServer"
env = vmware.DefaultEnvironment()

hlslNodeNames = ["vnc-win32-hlsl"]

env.Append(
    CPPDEFINES={
        "USERLEVEL": None,
        "VMX86_DESKTOP": None,
    }
)

vncServerSubdirs = [
    "apps/rde/vncServer",
]

env.Prepend(
    CPPPATH=[
        "#bora/public",
        "#bora/lib/public",
        "#bora/lib/vncConnectionManager",
    ]
)

if env.Host().IsLinux():
    env.Append(
        LINKFLAGS=["-Wl,-rpath -Wl,'$$ORIGIN'"],
    )

if vmware.Host().IsLinux():
    vncServerSubdirs += [
        "apps/rde/vvc/vvcProxyStub/hub",
        "apps/rde/vvc/vvcProxyStub/common",
        "apps/rde/vvc/vvcProxyStub/common/utils/posix",
        # Do not move this to vmlibs, it requires VVCPROXYHUB_EXPORTS
        "lib/vncConnectionManager",
        # Do not move this to vmlibs, it requires VVCHUB_SUPPORT
        "lib/blastSockets",
    ]

    vmware.LoadTool(
        env,
        tool=[
            "alsa",
            "cvt",
            "ffmpeg",
            "keyutils",
            "libssl",
            "xorg",
            "libx264",
            "libyuv",
            "libz",
            "nvidia_sdk",
        ],
    )

    env.Append(
        CCFLAGS=[
            "-Wno-unused-value",
            "-DVVCPROXYHUB_EXPORTS",
            "-DLINUX_VIEW_AGENT",
            "-DVVCHUB_SUPPORT",
        ],
        CPPPATH=[
            "#bora/apps/rde/vvc/vvcProxyStub/hub",
            "#bora/apps/rde/vvc/vvcProxyStub/common",
            "#bora/apps/rde/vvc/vvcProxyStub/common/utils/include",
            "#bora/apps/lib/public",
        ],
        STATICLIBS=[
            "jpeg",
            "ogg",
            "opus",
            "speexdsp",
            "yuv",
        ],
        LIBS=[
            "dl",
            "m",
            "pthread",
            "stdc++",
            "X11",
            "Xcursor",
            "Xdamage",
            "Xext",
            "Xfixes",
            "Xi",
            "Xinerama",
            "Xrandr",
            "Xtst",
        ],
    )

    if vmware.BuildType() in ["beta", "obj", "opt"]:
        # stack unwinding code in Util_Backtrace still depends on these
        env.Append(CCFLAGS=["-fno-omit-frame-pointer"])
        env.Append(LINKFLAGS=["-Wl,--export-dynamic"])

if vmware.Host().IsWindows():
    nvapiLib = "nvapi64.lib" if vmware.Host().Is64Bit() else "nvapi.lib"
    env.LoadTool(["intel_sdk", "nvidia_sdk"])
    env.LoadTool("amd_rapidfire_sdk", dllName="RapidFire")
    env.Append(
        LIBS=[
            nvapiLib,
            "d3d9.lib",
            "dxva2.lib",
            "dwmapi.lib",
            "legacy_stdio_definitions.lib",
            "legacy_stdio_wide_specifiers.lib",
            "magnification.lib",
            "yuv.lib",
            "cryptui.lib",
            "ncrypt.lib",
        ]
    )

    env.LoadTool(
        [
            "msvcrt",
            "atlmfc",
            "fmtlib",
            "libjpeg",
            "libpng",
            "libz",
            "libssl",
            "libxdr",
            "libyuv",
            "libx264",
        ]
    )

    env.LoadTool("mfw")

    env.Append(
        LINKFLAGS=[
            '-base:"0x69500000"',
            "-entry:wWinMainCRTStartup",
            "-delayload:ddraw.dll",
            "-delayload:d3d11.dll",
            "-delayload:dxgi.dll",
            "-subsystem:console",
        ]
    )

    env.Append(
        CPPDEFINES={
            "_UNICODE": None,
            "UNICODE": None,
            "WINVER": "0x0A00",  # Windows 10 and up
        }
    )

    env.Append(
        LIBS=[
            "ws2_32.lib",
            "dxguid.lib",
            "uuid.lib",
            "oldnames.lib",
            "setupapi.lib",
            "netapi32.lib",
            "ntdll.lib",
            "delayimp.lib",
            "kernel32.lib",
            "user32.lib",
            "gdi32.lib",
            "advapi32.lib",
            "ole32.lib",
            "oleaut32.lib",
            "shell32.lib",
            "msimg32.lib",
            "bcrypt.lib",
            "crypt32.lib",
            "wbemuuid.lib",
            "ddraw.lib",
            "d3d11.lib",
            "dxgi.lib",
            "winmm.lib",
            "glu32.lib",
            "Wtsapi32.lib",
            "qwave.lib",
            "Rpcrt4.lib",
            "Secur32.lib",
            "Shlwapi.lib",
            "version.lib",
            "psapi.lib",
            "magnification.lib",
        ]
    )

    env.Append(
        CPPPATH=[
            "#bora/lib/vncConnectionManager/win32",
            "#bora/lib/vncConnectionManager/win32/capture",
            "#bora/lib/vncConnectionManager/win32/topology",
            "#bora-vmsoft/svga/hznvidd",
            "#bora-vmsoft/svga/vdisplay",
            "#bora-vmsoft/svga/wddm/include",
        ]
    )

vmware.LoadTool(
    env,
    [
        "libjpeg",
        "libogg",
        "libopus",
        "libpng",
        "libspeexdsp",
        "libz",
        "protobuf-c-3",
        "vm-product",
    ],
)

if vmware.Host().IsWindows():
    env.Append(LIBS=["iphlpapi.lib"])

e = vmware.Executable(vncName, env=env)
vncLibs = [
    "asyncsocket",
    "blastCodec",
    "blastControl",
    "cityhash",
    "config",
    "coreDump",
    "crypto",
    "d3des",
    "dict",
    "err",
    "file",
    "hashMap",
    "image",
    "keyboard",
    "lfqueue",
    "lock",
    "log",
    "mempool",
    "misc",
    "panic",
    "poll",
    "pollDefault",
    "productState",
    "raster",
    "rbtree",
    "rectangle",
    "region",
    "sig",
    "slab",
    "sound",
    "soundlib",
    "ssl",
    "string",
    "thread",
    "udpfec",
    "udpProxy",
    "unicode",
    "user",
    "uuid",
    "version",
    "vnc",
    "vvclib",
]

if vmware.Host().IsWindows():
    vncLibs += [
        "blastSockets",
        "kbdlayoutid",
        "rdsutils",
        "sslRemap",
        "uuid",
        "vncConnectionManager",
        "win32auth",
        "win32cfgmgr",
        "win32tsf",
        "wmi",
        "vnc/win32",
    ]
    e.addGlobalStaticLibs(
        [
            "cedarBase",
            "cedarConfig",
            "cedarLog",
            "cedarTask",
            "ipclib-static-md",
            "objectMap-static-md",
            "smlib-static-md",
            "vvc-view-hub-unicode-md",
            "vvc-view-hub2-unicode-md",
        ]
    )

if vmware.Host().IsLinux():
    vncLibs += [
        "win32tsf",
    ]

subdirs = e.addStaticLibs("vmlibs", vncLibs)

e.addGlobalStaticLibs(
    [
        "vncNvEncSDK8",
        "vncNvEncSDK12",
        "vncReplay",
        "vncReplayHardware",
    ]
)
e.addSubdirs(vncServerSubdirs)

node = e.createProgramNode()

if vmware.Host().IsLinux():
    env.AddAlsaDeps(subdirs, node)

if vmware.Host().IsWindows():
    for hlslNodeName in hlslNodeNames:
        hlslNode = vmware.LookupNode(hlslNodeName, host="win32")
        env.Depends(node, dependency=hlslNode)

vmware.RegisterNode(node, vncName)

# Stage redistributables like libx264 when building vncServer.
for n in env.get("REDIST") or []:
    vmware.RegisterNode([File(n)], vncName)

# libstdc++ is not a normal, explicit shared lib of vncServer.
# but we must stage it on linux. On windows the C++ runtime can be
# installed manually as a pre-requisite.
if "LIBSTDCXX_REDIST" in env:
    vmware.RegisterNode([env["LIBSTDCXX_REDIST"]], vncName)

vmware.RegisterEnv("%s-env" % vncName, env)
vmware.Alias("%s-build" % vncName, node)
