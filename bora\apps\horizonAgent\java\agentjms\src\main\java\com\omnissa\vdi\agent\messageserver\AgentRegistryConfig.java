/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */
package com.omnissa.vdi.agent.messageserver;

import java.net.NetworkInterface;
import java.security.KeyPair;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

import javax.naming.ldap.LdapName;
import javax.naming.ldap.Rdn;

import com.omnissa.vdi.messagesecurity.Identity.IdentityType;
import com.omnissa.vdi.messagesecurity.JMSUtil;
import org.apache.commons.lang3.StringUtils;

import com.omnissa.vdi.common.winauth.WinAuthUtils;
import com.omnissa.vdi.commonutils.NetworkAddressUtil;
import com.omnissa.vdi.commonutils.NetworkAddressUtil.VDINetworkException;
import com.omnissa.vdi.commonutils.RenderList;
import com.omnissa.vdi.commonutils.SslUtils;
import com.omnissa.vdi.commonutils.SslUtils.sslRole;
import com.omnissa.vdi.crypto.DestroyableString;
import com.omnissa.vdi.crypto.SecurityManager;
import com.omnissa.vdi.crypto.SecurityManagerException;
import com.omnissa.vdi.logger.Logger;
import com.omnissa.vdi.messagesecurity.Identity;
import com.omnissa.vdi.messagesecurity.JMSMessageSecurity;
import com.omnissa.vdi.messagesecurity.MessageSecurityBase;
import com.omnissa.vdi.messagesecurity.MessageSecurityException;
import com.omnissa.vdi.messagesecurity.MessageSecurityHandler.MSSecMode;
import com.omnissa.vdi.ssl.CertificateManager;
import com.omnissa.vdi.ssl.CertificateManagerException;

import net.propero.workspace.windowsagent.properties.WSProperties;
import net.propero.workspace.windowsagent.properties.WSPropertiesNative;

public class AgentRegistryConfig extends AgentJmsConfig {
    final static String AGENT_IDENTITY = "Agent Identity";

    final private static String AGENT_LEGACY_PRIVATE_KEY = "Agent Private Key";

    final private static String AGENT_PUBLIC_KEY = "Agent Public Key";

    final private static String BROKER_PUBLIC_KEY = "Broker Public Key";

    final private static String ENCRYPTION_PUBLIC_KEY = "Encryption Public Key";

    final static String BROKER_SSL_CERTIFICATE_THUMBPRINT = "Broker SSL Certificate Thumbprint";

    final static String AGENT_SSL_CERTIFICATE_THUMBPRINT = "Agent SSL Certificate Thumbprint";

    final static String SSL_CERTIFICATE_KEY_CHECK_INTERVAL = "SSL Certificate Key Check Interval";

    final static String SSL_CERTIFICATE_KEY_SIZE = "SSL Certificate Key Size";

    final static String SSL_CERTIFICATE_VALIDITY_PERIOD = "SSL Certificate Validity Period";

    final static String CLIENT_CIPHER_SUITES = "ClientSSLCipherSuites";

    final static String MANAGED_CERTIFICATE_ADVANCE_ROLLOVER = "ManagedCertificateAdvanceRollOver";

    final static String THUMBPRINT_ALGORITHMS = "SSLThumbprintAlgorithms";

    final static String PREFERRED_THUMBPRINT_ALGORITHM = "SSLPreferredThumbprintAlgorithm";

    final static String CLIENT_SIGNATURE_SCHEMES = "SSLClientSignatureSchemes";

    final static String CERTIFICATE_SIGNATURE_ALGORITHM = "SSLCertificateSignatureAlgorithm";

    final static String NAMED_GROUPS = "SSLNamedGroups";

    final static String CLIENT_SECURE_PROTOCOLS = "ClientSSLSecureProtocols";

    final static String DAAS_AGENT = "DaaSAgent";

    final static String DAAS_AGENT_MANAGED = "DaaSAgentManaged";

    final private static String GENERATED_KEY_REF = "Generated Key Reference";

    final private static Logger log = Logger
            .getLogger(AgentRegistryConfig.class);

    final private static String PRIVATE_KEY_REF = "Agent Key Reference";

    final private static String ENCRYPTION_KEY_REF = "Encryption Key Reference";

    final private static String INVALIDATE_MACHINE_IDENTITY = "InvalidateMachineIdentity";

    final private static String REG_KEY_AGENT_CONFIG = "Agent\\Configuration";

    final private static String PAIRED_MAC_ADDRESS = "Paired MAC Address";

    final private static String PAIRED_IDENTITY = "Paired Identity";

    final private static String AGENT_SERVER_DN = "Server DN";

    final private static String AGENT_SERVER_POOL_DN = "Server Pool DN";

    final private static String AGENT_INFRASTRUCTURE_ZONE = "Infrastructure Zone";

    final static String REG_KEY_NODE_MANAGER = "Node Manager";

    final static String REG_KEY_ICA_HZN_INTEGRATION = "Instant Clone Agent\\HorizonAgent";

    final private static String REG_KEY_VMW_VDM = "HKLM\\SOFTWARE\\Omnissa\\Horizon";

    final private static String IP_PREFIX = "IpPrefix";

    final private static int MAC_ADDRESS_ATTEMPTS = 60;

    final static String CMS_MESSAGE_TTL = "Cms Message TTL";

    final static String CHANGEKEY_MESSAGE_TTL = "Changekey Message TTL";

    final static String CHANGEKEY_RESPONSE_TIMEOUT = "Changekey Response Timeout";

    final static String USE_SSO_FOR_ENCRYPTION = "Use SSO";

    final private static String REG_SECURITY_KEY_VMW = REG_KEY_VMW_VDM
            + "\\Security";

    final private static String NO_MANAGED_CERTIFICATE = "NoManagedCertificate";

    final private static String REQUIRE_NEW_IDENTITY = "requireNewIdentity";

    final private static String GOLDEN_IMAGE_PUBLIC_KEY = "Golden Image Public Key";

    final private static String GOLDEN_IMAGE_KEY_REF = "Golden Image Key Reference";

    final private static String CREATE_RPS_KEY = "CreateRPsKey";

    final private static String RPS_PUBLIC_KEY = "RPs Public Key";

    final private static String RPS_KEY_REF = "RPs Key Reference";

    // PoC only
    final private static String TRIGGER_GOLDEN_IMAGE = "triggerGoldenImage";

    final private WSProperties agent = new WSProperties(REG_KEY_AGENT_CONFIG,
            true);

    private Identity agentIdentity;

    private PrivateKey agentLegacyPrivateKey;

    private KeyPair agentPairedKey;

    /**
     * Two mins, 30 seconds is default for async report generation
     */
    final static int DEFAULT_ASYNC_SESSION_INTERVAL_SECS = 150;

    private KeyPair agentEncryptionKey;

    final private int asyncSessionSeconds;

    private PublicKey brokerPublicKey;

    private List<String> brokers;

    private String infrastructureZone;

    final private boolean isManaged;

    private boolean isPaired;

    final private SecurityManager manager = SecurityManager.manager();

    private MSSecMode msMode;

    final private WSProperties nm = new WSProperties(REG_KEY_NODE_MANAGER, true);

    final private WSProperties ica = new WSProperties(REG_KEY_ICA_HZN_INTEGRATION, false);

    private String serverDn;

    private String serverDnsName;

    private int managedCertificateAdvanceRollOver;

    private String serverPoolDn;

    final private static long DEFAULT_CMS_MESSAGE_TTL_IN_MILLSECONDS = TimeUnit.MINUTES.toMillis(5);

    final private static long DEFAULT_CHANGEKEY_MESSAGE_TTL_IN_MILLSECONDS = TimeUnit.SECONDS.toMillis(10);
    final private static long DEFAULT_CHANGEKEY_RESPONSE_TIMEOUT_IN_MILLSECONDS = TimeUnit.SECONDS.toMillis(15);

    final private static long DEFAULT_DAAS_CHANGEKEY_MESSAGE_TTL_IN_MILLSECONDS = TimeUnit.SECONDS.toMillis(15);
    final private static long DEFAULT_DAAS_CHANGEKEY_RESPONSE_TIMEOUT_IN_MILLSECONDS = TimeUnit.SECONDS.toMillis(20);

    public AgentRegistryConfig() {

        String rawServerPoolDn = nm.getProperty("Server Pool DN", null);

        serverPoolDn = normaliseDn(rawServerPoolDn);

        String modeStr = nm.getProperty("MS Mode", "ON");
        MSSecMode modeEnum;
        try {
            modeEnum = MSSecMode.valueOf(modeStr);
        } catch (IllegalArgumentException iae) {
            log.error("Message security mode is invalid, setting to ON. Invalid value: "
                    + modeStr);
            modeEnum = MSSecMode.ON;
        }
        msMode = modeEnum;
        isManaged = nm.getProperty("Managed", true);

        asyncSessionSeconds = nm.getProperty("AsyncSessionSeconds", DEFAULT_ASYNC_SESSION_INTERVAL_SECS);

        String optStr = nm.getProperty("MS Options", null);
        if (optStr != null) {
            JMSMessageSecurity.setOptions(optStr.split(","));
        }
    }

    @Override
    public void init() {
        confirmMachineIdentity();

        initData();

        // Register hook to be called when JVM shuts down.
        Runtime.getRuntime().addShutdownHook(new Thread("ShutdownHook") {
            @Override
            public void run() {
                shutdown();
            }
        });
    }

    @Override
    public void reinit() {
        initData();
    }

    private void initData() {
        String legacyKey = nm.getProperty(AGENT_LEGACY_PRIVATE_KEY, null);
        String pairedKeyRef = nm.getProperty(PRIVATE_KEY_REF, null);
        String encryptionKeyRef = nm.getProperty(ENCRYPTION_KEY_REF, null);
        String ssoEncryption = nm.getProperty(USE_SSO_FOR_ENCRYPTION, null);
        String brokerKey = nm.getProperty(BROKER_PUBLIC_KEY, null);
        String ident = nm.getProperty(AGENT_IDENTITY, null);
        infrastructureZone = nm.getProperty(AGENT_INFRASTRUCTURE_ZONE, "site1");

        String rawServerDn = nm.getProperty(AGENT_SERVER_DN, null);
        String rawServerPoolDn = nm.getProperty(AGENT_SERVER_POOL_DN, null);

        /*
         * serverDn and serverPoolDn are used in topic names, new swiftmq auth
         * does a case-sensitive string match for permissions so we must ensure
         * both are lower case. The server DN written by the unmanaged agent
         * installer is not lowercased.
         * As seen in https://omnissa.atlassian.net/browse/HZN-2087 it is
         * possible that the serverPoolDn was set to an empty string in the
         * AgentRegistryConfig contructor, so we try to set it here again to
         * get the correct value.
         * Note: The server Pool DN will be set to an empty string here when
         * this function is called from init(), but will be set to the correct
         * string when this function is called due to a successful CHANGEID.
         */
        serverDn = normaliseDn(rawServerDn);
        serverPoolDn = normaliseDn(rawServerPoolDn);
        agentLegacyPrivateKey = stringToPrivateKey(legacyKey);
        agentPairedKey = loadAgentKey();
        agentEncryptionKey = loadEncryptionKey();

        /*
         * Encryption key is used to replace hardcoded SSO key. We need to share
         * the public key with our broker to enable this.
         */
        boolean isEncryptionKeyPaired = false;
        if (!isDaaSAgent()) {
            isEncryptionKeyPaired = encryptionKeyRef != null
                    && encryptionKeyRef.length() > 0 && ssoEncryption != null;
        } else {
            /*
             * DaaS doesn't support encryption key so set flag to true to
             * prevent encryption key state from triggering pairing
             */
            isEncryptionKeyPaired = true;
        }

        isPaired = pairedKeyRef != null && pairedKeyRef.length() > 0
                && isEncryptionKeyPaired;

        brokerPublicKey = stringToPublicKey(brokerKey);
        agentIdentity = getIdentity(ident);
        JMSUtil.identityType = IdentityType.AGENT;
        configureSSL();
    }

    /**
     * Perform any last minute actions to ensure good state before JVM shuts down.
     */
    private void shutdown() {
        log.debug("Shutting down AgentRegistryConfig");

        /*
         * See bug 2592408, need to ensure all changes are flushed to disk so
         * that we have consistent state on restart.
         */
        nm.flushToDisk();
    }

    /**
     * Configure SSL ciphers suites and secure protocols
     */
    private void configureSSL() {
        /*
         * process client cipher suites
         */

        String[] suites = RenderList.stringToArray(agent.getProperty(
                CLIENT_CIPHER_SUITES, null));

        if (log.isDebugEnabled()) {
            if (suites == null) {
                log.debug("Resetting SSL client enabled cipherSuites");
            } else {
                log.debug("Overriding SSL client enabled cipherSuites: "
                        + Arrays.toString(suites));
            }
        }

        SslUtils.setCipherSuites(sslRole.CLIENT, suites);

        /*
         * process client secure protocols
         */

        String[] protocols = RenderList.stringToArray(agent.getProperty(
                CLIENT_SECURE_PROTOCOLS, null));

        if (log.isDebugEnabled()) {
            if (protocols == null) {
                log.debug("Resetting SSL client enabled protocols");
            } else {
                log.debug("Overriding SSL client enabled protocols: "
                        + Arrays.toString(protocols));
            }
        }

        SslUtils.setSecureProtocols(sslRole.CLIENT, protocols);

        SslUtils.setPreferredProtocol(sslRole.CLIENT,
                protocols != null ? protocols[0] : null);

        /*
         * process certificate signature algorithm
         */
        SslUtils.setCertificateSignatureAlgorithm(
                agent.getProperty(CERTIFICATE_SIGNATURE_ALGORITHM, null));

        /*
         * process thumbprint algorithms
         */

        SslUtils.setThumbprintAlgorithms(RenderList
                .stringToList(agent.getProperty(THUMBPRINT_ALGORITHMS, null)));

        /*
         * process preferred thumbprint algorithm
         */

        SslUtils.setPreferredThumbprintAlgorithm(
                agent.getProperty(PREFERRED_THUMBPRINT_ALGORITHM, null));

        /*
         * process client signature schemes
         */

        SslUtils.setSignatureSchemes(sslRole.CLIENT, RenderList.stringToList(
                agent.getProperty(CLIENT_SIGNATURE_SCHEMES, null)));

        /*
         * process named groups
         */

        SslUtils.setNamedGroups(
                RenderList.stringToList(agent.getProperty(NAMED_GROUPS, null)));

        /*
         * process NoManagedCertificate flag
         */
        SslUtils.setNoManagedCertificate(agent.getPropertyEx(
                REG_SECURITY_KEY_VMW, NO_MANAGED_CERTIFICATE, false));
    }

    @Override
    public int getAsyncSessionSeconds() {
        return asyncSessionSeconds;
    }

    @Override
    public PublicKey getBrokerPublicKey() {
        return brokerPublicKey;
    }

    @Override
    public Collection<String> getBrokers() {
        /*
         * NOTE: reading the broker list directly from the registry is a
         * temporary for handling the case where a replica broker is added to
         * the POD while the agent is connected and then all other brokers go
         * off-line.
         *
         * The broker list was read once in the constructor which meant in the
         * above usecase the list wasn't re-read.
         *
         * The plan is to revert to reading the broker list once only once
         * further agent refactoring work has happened to make AgentJmsConfig
         * immutable.
         */
        String broker = agent.getProperty("Broker", "").trim();
        if (broker.length() == 0) {
            brokers = Collections.emptyList();
        } else {
            brokers = Collections.unmodifiableList(Arrays.asList(broker
                    .split("\\s+")));
        }

        return brokers;
    }

    @Override
    public void setBrokers(String brokersParam) {
        if (brokersParam.length() == 0) {
           return;
        }
        agent.setProperty("Broker", brokersParam.trim());
        agent.flushToDisk();
    }

    @Override
    public void setIcaParameter(String name, String value) {
        if (name.length() == 0) {
            return;
        }
        if (value.length() == 0) {
            return;
        }
        ica.setProperty(name, value);
        return;
    }

    @Override
    public Identity getIdentity() {
        return agentIdentity;
    }

    @Override
    public Identity getNonCachedIdentity() {
        String ident = nm.getProperty(AGENT_IDENTITY, null);
        Identity nonCachedIdentity = getIdentity(ident);
        return nonCachedIdentity;
    }

    /**
     * Create Identity based on supplied string, null on error or not defined.
     *
     * @param ident The string used to create the identity
     * @return The identity
     */
    private Identity getIdentity(String ident) {
        if (ident != null && ident.length() > 0) {
            try {
                return new Identity(ident);
            } catch (MessageSecurityException e) {
                log.warn("Identity not valid: " + ident);
            }
        }

        return null;
    }

    @Override
    public String getInfrastructureZone() {
        return infrastructureZone;
    }

    @Override
    protected PrivateKey getLegacyPrivateKey() {
        return agentLegacyPrivateKey;
    }


    @Override
    public MSSecMode getMsMode() {
        return msMode;
    }

    @Override
    public void setMsMode(MSSecMode mode) {
        if (nm.setProperty("MS Mode", mode.name())) {
            nm.flushToDisk();
            msMode = mode;
        } else {
            log.debug("Cannot set MS Mode in registry");
        }
    }

    @Override
    public int getManagedCertificateAdvanceRollOver() {
        return agent.getProperty(MANAGED_CERTIFICATE_ADVANCE_ROLLOVER, 0);
    }

    @Override
    public PrivateKey getPrivateKey() {
        if (agentPairedKey == null) {
            return null;
        } else {
            return agentPairedKey.getPrivate();
        }
    }

    @Override
    public PublicKey getPublicKey() {
        if (agentPairedKey == null) {
            return null;
        } else {
            return agentPairedKey.getPublic();
        }
    }

    @Override
    public KeyPair getEncryptionKey() {
        return agentEncryptionKey;
    }

    @Override
    public String getServerDn() {
        return serverDn;
    }

    @Override
    public String getServerDnsName() {
        if( serverDnsName == null ) {
            serverDnsName = WinAuthUtils.getLocalComputerName();
        }
        return serverDnsName;
    }

    @Override
    public String getServerPoolDn() {
        return serverPoolDn;
    }


    @Override
    public boolean isManaged() {
        return isManaged;
    }

    @Override
    public boolean isPaired() {
        return isPaired;
    }

    private KeyPair loadAgentKey() {
        String pairedKey = nm.getProperty(PRIVATE_KEY_REF, "");
        String generatedKey = nm.getProperty(GENERATED_KEY_REF, "");
        String pubKey = nm.getProperty(AGENT_PUBLIC_KEY, "");

        boolean paired = pairedKey.length() > 0;
        String regValue = paired ? pairedKey : generatedKey;
        PrivateKey priv = null;
        if (regValue.length() > 0) {
            // New: get keypair as data from keyvault
            try (DestroyableString data = manager.getData(regValue)) {
                if (!data.isEmpty()) {
                    // We have data, should be able to decode it
                    try {
                        priv = MessageSecurityBase.bytesToPrivateKey(
                                data.getBytes(),
                                JMSMessageSecurity.getKeyPairAlgorithm());
                    } catch (Exception e) {
                        log.error("Unable to decode data from " + regValue);
                        throw e;
                    }
                }
            } catch (SecurityManagerException sme) {
                log.debug("Cannot retrieve keypair from keyvault: " + sme);
            } catch (Exception e) {
                log.debug("Failure during key retrieval", e);
            }

            if (priv == null) {
                log.warn("Cannot retrieve key data for " + regValue);
                if (paired) {
                    // leave reference intact in case failure is transient
                    return null;
                }
            }
        }

        if (!paired && priv != null && pubKey.length() == 0) {
            /*
             * If we haven't yet paired successfully, we need both generated key
             * and matching public key for the CHANGEKEY message. If we only
             * have the generated private key it's of no use, so we need to
             * regenerate.
             */
            log.warn("Found generated key for pairing but missing"
                    + " the public portion, will generate a new key.");
            priv = null;
        }

        if (priv != null) {
            // We have stored a locally generated key pair
            return new KeyPair(stringToPublicKey(pubKey), priv);
        }

        // Nothing saved yet. Generate a new random one and store it.
        KeyPair pair = getGeneratedKeyPair();
        String ref;
        try (DestroyableString data = DestroyableString
                .from(pair.getPrivate().getEncoded())) {
            ref = manager.putData(data, SecurityManager.KeyType.LSEK);
        } catch (SecurityManagerException sme) {
            log.warn("Cannot store new key for pairing: " + sme.getMessage());
            return null;
        }

        // Write paired key back to registry
        String pubStr = JMSMessageSecurity.keyToString(pair.getPublic());
        nm.setProperty(GENERATED_KEY_REF, ref);
        nm.setProperty(AGENT_PUBLIC_KEY, pubStr);
        nm.flushToDisk();
        return pair;
    }

    private KeyPair loadEncryptionKey() {
        String encryptionKeyRef = nm.getProperty(ENCRYPTION_KEY_REF, "");
        String pubKey = nm.getProperty(ENCRYPTION_PUBLIC_KEY, "");

        PrivateKey priv = null;
        if (encryptionKeyRef.length() > 0) {
            try (DestroyableString data = manager.getData(encryptionKeyRef)) {
                if (!data.isEmpty()) {
                    // We have data, should be able to decode it
                    try {
                        priv = MessageSecurityBase.bytesToPrivateKey(
                                data.getBytes(), JMSMessageSecurity
                                        .getEncryptionKeyPairAlgorithm());
                    } catch (Exception e) {
                        log.error("Unable to decode data from " + encryptionKeyRef);
                        throw e;
                    }
                }
            } catch (SecurityManagerException sme) {
                log.debug("Cannot retrieve encryption keypair from keyvault: "
                        + sme);
            } catch (Exception e) {
                log.debug("Failure during key retrieval", e);
            }

            if (priv == null) {
                log.warn("Cannot retrieve key data for " + encryptionKeyRef);
            }
        }

        if (priv != null && pubKey.length() == 0) {
            log.warn("Found encrypted key but missing"
                    + " the public portion, will generate a new key.");
            priv = null;
        }

        if (priv != null) {
            // We have stored a locally generated key pair
            return new KeyPair(stringToEncryptionPublicKey(pubKey), priv);
        }

        // Nothing saved yet. Generate a new random one and store it.
        log.debug("Creating new encryption key pair");
        KeyPair pair = generateEncryptionKeyPair();
        String ref;
        try (DestroyableString data = DestroyableString
                .from(pair.getPrivate().getEncoded())) {
            ref = manager.putData(data, SecurityManager.KeyType.LSEK);
        } catch (SecurityManagerException sme) {
            log.warn("Cannot store new encryption key: " + sme.getMessage());
            return null;
        }

        // Write encryption keypair back to registry
        String pubStr = JMSMessageSecurity.keyToString(pair.getPublic());
        nm.setProperty(ENCRYPTION_KEY_REF, ref);
        nm.setProperty(ENCRYPTION_PUBLIC_KEY, pubStr);
        nm.flushToDisk();
        return pair;
    }

    @Override
    protected void pairOverJmsComplete() {
        // Move the key reference to its paired location for next startup
        String cur = nm.getProperty(PRIVATE_KEY_REF, "");
        if (cur.length() > 0) {
            log.warn("Agent paired key reference already set, left as-is");
            return;
        }

        String ref = nm.getProperty(GENERATED_KEY_REF, "");
        nm.setProperty(PRIVATE_KEY_REF, ref);
        nm.setProperty(GENERATED_KEY_REF, "");
        setManagedByDaasAgent(false); // no-op outside of Daas mode
        nm.flushToDisk();
        log.debug("Agent paired key reference saved to final location");
        isPaired = true;
        /*
         * Set the Paired Mac Address value in registry if RePairing keypair is
         * present (this will only be true on a golden image), or if "Paired
         * MAC Address" is not set.
         * This entry needs to be set so that if subsequently this machine is
         * used for cloning then this entry can be used for confirming machine
         * identity change by comparing it with the clone's MAC address.
         * If this machine is never cloned then saving the MAC address here
         * should be harmless.
         */
        String storedMac = nm.getProperty(PAIRED_MAC_ADDRESS, null);
        if (getRePairingKey() != null || storedMac == null) {
            storedMac = getMacAddr();
            if (storedMac == null) {
                log.error("Couldn't retrieve MAC address");
            } else {
                nm.setProperty(PAIRED_MAC_ADDRESS, storedMac);
            }
        }
    }

    @Override
    public String getBrokerSSLCertificateThumbprint() {
        return nm.getProperty(BROKER_SSL_CERTIFICATE_THUMBPRINT, null);
    }

    @Override
    public void setBrokerSSLCertificateThumbprint(String thumbprint) {
        log.debug("setting broker thumbprint:" + thumbprint);
        nm.setProperty(BROKER_SSL_CERTIFICATE_THUMBPRINT, thumbprint);
        nm.flushToDisk();
    }

    @Override
    public void setUseSSOKeyForEncryption(String useSSOKeyForEncryption) {
        log.debug("setting use SSO:" + useSSOKeyForEncryption);
        nm.setProperty(USE_SSO_FOR_ENCRYPTION, useSSOKeyForEncryption);
        nm.flushToDisk();
    }

    @Override
    public boolean getUseSSOKeyForEncryption() {
        return nm.getProperty(USE_SSO_FOR_ENCRYPTION, true);
    }

    @Override
    public String getAgentSSLCertificateThumbprint() {
        return nm.getProperty(AGENT_SSL_CERTIFICATE_THUMBPRINT, null);
    }

    @Override
    public void setAgentSSLCertificateThumbprint(String thumbprint) {
        log.debug("setting agent thumbprint:" + thumbprint);
        nm.setProperty(AGENT_SSL_CERTIFICATE_THUMBPRINT, thumbprint);
        nm.flushToDisk();
    }

    @Override
    public int getSSLCertificateKeyCheckInterval() {
        return nm.getProperty(SSL_CERTIFICATE_KEY_CHECK_INTERVAL,
                DEFAULT_SSL_CERTIFICATE_KEY_CHECK_INTERVAL);
    }

    @Override
    public int getSSLCertificateKeysize() {
        return nm.getProperty(SSL_CERTIFICATE_KEY_SIZE,
                DEFAULT_SSL_CERTIFICATE_KEY_SIZE);
    }

    @Override
    public int getSSLCertificateValidityPeriod() {
        return nm.getProperty(SSL_CERTIFICATE_VALIDITY_PERIOD,
                DEFAULT_SSL_CERTIFICATE_VALIDITY_PERIOD);
    }

    @Override
    public boolean isDaaSAgent() {
        return nm.getProperty(DAAS_AGENT, false);
    }

    @Override
    public boolean isDaaSLegacyPairingMode() {
        return isDaaSAgent() && !MSSecMode.ENHANCED.name()
                .equalsIgnoreCase(nm.getProperty("MS Mode", null));
    }

    @Override
    public boolean isManagedByDaasAgent() {
        return isDaaSAgent() && nm.getProperty(DAAS_AGENT_MANAGED, true);
    }

    @Override
    public void setManagedByDaasAgent(boolean managedByDaasAgent) {
        if (isDaaSAgent()) {
            nm.setProperty(DAAS_AGENT_MANAGED,
                    Boolean.toString(managedByDaasAgent));
        }
    }

    @Override
    public long getCmsMessageTtl() {
        return nm.getProperty(CMS_MESSAGE_TTL, (int) DEFAULT_CMS_MESSAGE_TTL_IN_MILLSECONDS);
    }

    @Override
    public long getChangeKeyMessageTtl() {
        return nm.getProperty(CHANGEKEY_MESSAGE_TTL,
                (int) (isDaaSAgent()
                        ? DEFAULT_DAAS_CHANGEKEY_MESSAGE_TTL_IN_MILLSECONDS
                        : DEFAULT_CHANGEKEY_MESSAGE_TTL_IN_MILLSECONDS));
    }

    @Override
    public long getChangeKeyResponseTimeout() {
        return nm.getProperty(CHANGEKEY_RESPONSE_TIMEOUT,
                (int) (isDaaSAgent()
                        ? DEFAULT_DAAS_CHANGEKEY_RESPONSE_TIMEOUT_IN_MILLSECONDS
                        : DEFAULT_CHANGEKEY_RESPONSE_TIMEOUT_IN_MILLSECONDS));
    }

    @Override
    public void setGoldenImageKey(KeyPair goldenImageKeyPair) {

        /*
         * Golden image key may be overwritten by broker, so clear out old key
         * if present.
         */
        deleteKey(nm.getProperty(GOLDEN_IMAGE_KEY_REF, ""));

        // Push the golden image key to keyvault
        String ref = "";
        try (DestroyableString data = DestroyableString
                .from(goldenImageKeyPair.getPrivate().getEncoded())) {
            ref = manager.putData(data, SecurityManager.KeyType.LSEK);
        } catch (SecurityManagerException sme) {
            log.error("Unable to store golden image key: {}", sme.getMessage());
            return;
        }

        // Write golden image key reference back to registry
        nm.setProperty(GOLDEN_IMAGE_KEY_REF, ref);
        nm.setProperty(GOLDEN_IMAGE_PUBLIC_KEY,
                JMSMessageSecurity.keyToString(goldenImageKeyPair.getPublic()));
        nm.flushToDisk();
    }

    /**
     * Deletes a key from keyvault using the given key reference.
     *
     * @param keyRef
     *            reference to a key in keyvault.
     */
    private void deleteKey(String keyRef) {
        if (StringUtils.isBlank(keyRef)) {
            return;
        }

        log.debug("Removing data for key reference {}", keyRef);

        try {
            manager.removeData(keyRef);
        } catch (Exception e) {
            /*
             * May not exist so not too worried. We tried, nothing else we can
             * do.
             */
            log.debug("Cannot remove key data from keyvault: {}", keyRef);
        }
    }

    @Override
    public KeyPair getGoldenImageKeypair() {
       /*
        * Is there a golden image keypair or a RePairing keypair?
        * Look first for the golden image keypair, if this is an AWS clone then
        * it should be available. If no golden image keypair is present then look
        * for the RePairing keypair, if this is a Nutanix clone then it should be
        * available.
        */
        KeyPair goldenImageKeypair = getGoldenImageKey();

        if (goldenImageKeypair == null) {
            log.warn("No golden image keypair available.");
            goldenImageKeypair = getRePairingKey();
            if (goldenImageKeypair == null) {
                log.warn("No RPs keypair available.");
                return null;
            }
        }
        return goldenImageKeypair;
    }

    /**
     * Stores RPs keypair in the KeyVault and sets the values in node
     * manager registry location.
     *
     * @param rePairingKeyPair
     *            Keypair to be stored.
     */
    @Override
    public void setRePairingKey(KeyPair rePairingKeyPair) {
        // Push the RPs key to keyvault
        log.debug("Storing RPs key pair to KeyVault.");
        String ref = "";
        try (DestroyableString data = DestroyableString
                .from(rePairingKeyPair.getPrivate().getEncoded())) {
            ref = manager.putData(data, SecurityManager.KeyType.LSEK);
        } catch (SecurityManagerException sme) {
            log.error("Unable to store RPs key: {}", sme.getMessage());
            return;
        }

        // Write RePairing key reference back to registry
        nm.setProperty(RPS_KEY_REF, ref);
        nm.setProperty(RPS_PUBLIC_KEY,
                JMSMessageSecurity.keyToString(rePairingKeyPair.getPublic()));
        nm.flushToDisk();
    }


    /**
     * Check if createRepairingKeyPair is set or not.
     */
    @Override
    public boolean shouldCreateRePairingKeyPair() {
        return nm.getProperty(CREATE_RPS_KEY, false);
    }
	

    /**
     * Create RePairing signing (RPs) keypair and store it in KeyVault
     */
    @Override
    public KeyPair createRePairingKeyPair() {
        try {
            KeyPair rePairingKeyPair = JMSMessageSecurity.makeKeyPair();
            // Store the RePairing key pair.
            setRePairingKey(rePairingKeyPair);
            return rePairingKeyPair;
        } catch (MessageSecurityException sme) {
            log.debug("Failed to create RPs keypair in keyvault: "
                    + sme);
        }
        return null;
    }


    /**
     * Retrieves the RePairing keypair from KeyVault, if not present will return
     * null.
     */
    @Override
    public KeyPair getRePairingKey() {
        log.trace("Retrieving RPs key pair.");
        // Get keypair from keyvault using reference stored in registry
        String pubKey = nm.getProperty(RPS_PUBLIC_KEY, "");
        String keyRef = nm.getProperty(RPS_KEY_REF, "");

        PrivateKey priv = null;
        if (keyRef.length() == 0) {
            log.debug("RPs key reference not set");
            return null;
        }

        try (DestroyableString data = manager.getData(keyRef)) {
            if (!data.isEmpty()) {
                // We have data, should be able to decode it
                priv = MessageSecurityBase.bytesToPrivateKey(data.getBytes(),
                        JMSMessageSecurity.getKeyPairAlgorithm());
            }
        } catch (MessageSecurityException sme) {
            log.debug("Cannot retrieve RPs keypair from keyvault: "
                    + sme);
        } catch (SecurityManagerException sme) {
            log.debug("Cannot retrieve RPs keypair from keyvault: "
                    + sme);
        } catch (Exception e) {
            log.debug("Failure during RPs key retrieval", e);
        }

        if (priv == null) {
            log.warn("Cannot retrieve key data for " + keyRef);
            return null;
        }

        if (pubKey.length() == 0) {
            log.warn("Found RPs key but missing the public portion");
            return null;
        }

        return new KeyPair(stringToPublicKey(pubKey), priv);
    }


    @Override
    public KeyPair getGoldenImageKey() {
        // Get keypair from keyvault using reference stored in registry
        String pubKey = nm.getProperty(GOLDEN_IMAGE_PUBLIC_KEY, "");
        String keyRef = nm.getProperty(GOLDEN_IMAGE_KEY_REF, "");

        PrivateKey priv = null;
        if (keyRef.length() == 0) {
            log.trace("Golden image key reference not set");
            return null;
        }

        try (DestroyableString data = manager.getData(keyRef)) {
            if (!data.isEmpty()) {
                // We have data, should be able to decode it
                priv = MessageSecurityBase.bytesToPrivateKey(data.getBytes(),
                        JMSMessageSecurity.getKeyPairAlgorithm());
            }
        } catch (MessageSecurityException sme) {
            log.debug("Cannot retrieve golden image keypair from keyvault: "
                    + sme);
        } catch (SecurityManagerException sme) {
            log.debug("Cannot retrieve golden image keypair from keyvault: "
                    + sme);
        } catch (Exception e) {
            log.debug("Failure during golden image key retrieval", e);
        }

        if (priv == null) {
            log.warn("Cannot retrieve key data for " + keyRef);
            return null;
        }

        if (pubKey.length() == 0) {
            log.warn("Found golden image key but missing the public portion");
            return null;
        }

        return new KeyPair(stringToPublicKey(pubKey), priv);
    }

    @Override
    public boolean invalidateIdentity() {
        return agent.getProperty(INVALIDATE_MACHINE_IDENTITY, true);
    }

    @Override
    public boolean requireNewIdentity() {
        return nm.getProperty(REQUIRE_NEW_IDENTITY, false);

    }

    @Override
    public void setRequireNewIdentity(boolean requireNewIdentity) {
        nm.setProperty(REQUIRE_NEW_IDENTITY,
                requireNewIdentity ? "true" : null);
    }

    @Override
    public void setNewIdentity(String identity, String serverDn,
            String serverPoolDn, String publicKey, String privateKey) {

        // Push identity to known registry locations used for initial pairing
        nm.setProperty(AgentRegistryConfig.AGENT_IDENTITY, identity);
        nm.setProperty(AgentRegistryConfig.AGENT_PUBLIC_KEY, publicKey);
        nm.setProperty(AgentRegistryConfig.AGENT_LEGACY_PRIVATE_KEY,
                privateKey);
        nm.setProperty(AgentRegistryConfig.AGENT_SERVER_DN, serverDn);
        nm.setProperty(AgentRegistryConfig.AGENT_SERVER_POOL_DN, serverPoolDn);
        setRequireNewIdentity(false);

        // Clear golden image registry entries so we don't do this again
        String keyRef = nm.getProperty(GOLDEN_IMAGE_KEY_REF, "");
        nm.setProperty(GOLDEN_IMAGE_KEY_REF, null);
        nm.setProperty(GOLDEN_IMAGE_PUBLIC_KEY, null);

        // Clear out the repairing key pair:
        log.trace("Clearing out RPs keypair related values.");
        String rePairingKeyRef = nm.getProperty(RPS_KEY_REF, "");
        nm.setProperty(RPS_KEY_REF, null);
        nm.setProperty(RPS_PUBLIC_KEY, null);

        // Ensure changes are persisted.
        nm.flushToDisk();

        // Clear the golden image key out of keyvault now we've finished with it
        deleteKey(keyRef);
        deleteKey(rePairingKeyRef);
    }

    @Override
    public boolean triggerGoldenImage() {
        // PoC only
        return nm.getProperty(TRIGGER_GOLDEN_IMAGE, false);
    }

    @Override
    public void setTriggerGoldenImage(boolean triggerGoldenImage) {
        nm.setProperty(TRIGGER_GOLDEN_IMAGE,
                triggerGoldenImage ? "true" : null);
    }

    /**
     * Validate if there are any changes in the system from the previously
     * paired state. Eg. MAC address or identity changes
     */
    public void confirmMachineIdentity() {
        if (!invalidateIdentity()) {
            log.info("Skipping the machine identity check");
            return;
        }

        boolean isInVM = nm.getProperty("InVM", false);
        boolean isGoldenImage = false;
        if (getGoldenImageKeypair() != null) {
            log.debug("This is a golden image.");
            isGoldenImage = true;
        }

        /*
         * Don't need to do the machine identity check for non-vSphere machines
         * (this typically means physical machines), with the exception of AWS
         * VMs which are now supported. In that case, we want to do the check
         * after cloning if the VM is a golden image to prepare a new identity.
         */
        if (!isInVM && !isGoldenImage) {
            log.info("Machine identity check not required");
            return;
        }

        log.debug("Confirming the machine identity");

        Identity identity = getNonCachedIdentity();
        String storedMac = nm.getProperty(PAIRED_MAC_ADDRESS, null);
        String storedIdentity = nm.getProperty(PAIRED_IDENTITY, null);
        String currentMac = getMacAddr();
        String currentIdentity = identity != null ? identity.toString() : null;

        if (currentMac == null) {
            String msg = "Failed to get current MAC address. Restarting JMS.";
            log.error(msg);
            throw new RuntimeException(msg);
        } else {
            log.debug("Current MAC: {}", currentMac);
        }

        if (StringUtils.equalsIgnoreCase(storedMac, currentMac)) {
            // MAC unchanged, nothing further to do.
            log.debug("Machine identity confirmed");
            return;
        }

        if (storedMac == null) {
            log.trace(
                    "First time through, store current values. MAC:{}, identity:{}",
                    currentMac, currentIdentity);
            nm.setProperty(PAIRED_MAC_ADDRESS, currentMac);
            nm.setProperty(PAIRED_IDENTITY, currentIdentity);
            nm.flushToDisk();
            return;
        }

        /*
         * Current MAC doesn't match stored MAC, so is stored MAC from a
         * different NIC? See if we can find the matching NIC for the stored
         * MAC. If no match found, assume VM has changed and we need to wipe
         * agent pairing. See bug #2517350 for further details.
         */
        NetworkInterface networkInterface = null;
        try {
            log.trace("Getting network interface for stored MAC {}", storedMac);
            networkInterface = NetworkAddressUtil
                    .getNetworkInterfaceFromMacAddress(storedMac,
                            getIPPrefix());
        } catch (VDINetworkException ne) {
            // Unlikely to happen given we already obtained currentMac
            log.warn("Unable to obtain network interface for MAC {}",
                    storedMac);
        }

        if (networkInterface != null) {
            /*
             * NIC for stored MAC found, so can assume this is the same VM and
             * we don't need to wipe the pairing data.
             */
            log.debug("Match found, machine identity confirmed");
            return;
        }

        /*
         * NIC for paired MAC not found, so have to assume this is a
         * different/cloned VM and wipe pairing data
         */

        log.debug("Current MAC: {}, previously paired MAC: {}", currentMac,
                storedMac);
        log.debug("Current identity: {}, previously paired identity: {}",
                currentIdentity, storedIdentity);
        log.debug(
                "Mismatch found between current mac and previously paired mac,"
                        + " reinitiating the pairing process");

        String privateKey = nm.getProperty(PRIVATE_KEY_REF);
        String encryptionKey = nm.getProperty(ENCRYPTION_KEY_REF);

        // Update registry with new values
        nm.setProperty(PAIRED_MAC_ADDRESS, currentMac);
        nm.setProperty(PAIRED_IDENTITY, currentIdentity);
        nm.setProperty(PRIVATE_KEY_REF, null);
        nm.setProperty(AGENT_PUBLIC_KEY, null);
        nm.setProperty(GENERATED_KEY_REF, null);
        nm.setProperty(ENCRYPTION_KEY_REF, null);
        nm.setProperty(ENCRYPTION_PUBLIC_KEY, null);
        nm.setProperty(AGENT_SSL_CERTIFICATE_THUMBPRINT, null);
        nm.setProperty(BROKER_SSL_CERTIFICATE_THUMBPRINT, null);
        nm.setProperty(USE_SSO_FOR_ENCRYPTION, null);

        if (isGoldenImage) {
            log.debug("Change of identity required");
            setRequireNewIdentity(true);
        }

        /*
         * See bug 2592408, need to flush changes to disk immediately to reduce
         * risk of agent restarting before they are committed.
         */
        nm.flushToDisk();

        /*
         * Remove keys from keyvault
         */
        deleteKey(privateKey);
        deleteKey(encryptionKey);

        /*
         * Delete any old certs we may have previously created for old identity
         */
        if (storedIdentity != null) {
            log.debug("Removing certs for identity {}", storedIdentity);
            try {
                CertificateManager cm = getCertificateManager();
                cm.deleteCertificates(storedIdentity);
            } catch (CertificateManagerException e) {
                log.error("Unable to delete certificates: " + e.getMessage(),
                        e);
            }
        }
    }

    /**
     * First creates a string representation of this LDAP name in a format
     * defined by RFC 2253, then converts it to lower-case for easy string
     * comparison. An invalid DN (including one with zero components), or an
     * already normalised string, results in the original string instance being
     * returned.
     *
     * NOTE: This is a copy of ldapcontextpool's Util.normaliseDN
     *
     * @param dn
     *            The DN to use
     * @return The normalised DN, lowercased.
     */
    static private String normaliseDn(String dn) {
        String newDn = dn;

        if (dn != null) {
            try {
                List<Rdn> rdns = (new LdapName(dn)).getRdns();
                if (rdns != null && rdns.size() > 0) {
                    newDn = (new LdapName(rdns)).toString();
                    newDn = (dn.equals(newDn) ? dn : newDn).toLowerCase();
                }
            } catch (Exception e) {
            }
        }
        return newDn;
    }

    private String getIPPrefix() {
        return WSPropertiesNative.getPropertyEx(REG_KEY_VMW_VDM, IP_PREFIX,
                null, true);
    }

    private String getMacAddr() {
        final int MAX_RETRY_COUNT = MAC_ADDRESS_ATTEMPTS;
        final long RETRY_DELAY_MS = 1000; // 1 second
        String macAddr = null;

        for (int retry = 0; retry < MAX_RETRY_COUNT; retry++) {
            try {
                NetworkInterface ni = NetworkAddressUtil.getNetworkInterface(getIPPrefix());
                macAddr = NetworkAddressUtil.getMacAddressStringFromNetworkInterface(ni);

                if (macAddr != null) {
                    break; // Exit loop if MAC address is retrieved successfully
                }
            } catch (VDINetworkException e) {
                log.warn("Attempt {}: Failed to retrieve MAC address. Error: {}",
                       retry + 1, e.getMessage());
            }

            try {
                Thread.sleep(RETRY_DELAY_MS);
            } catch (InterruptedException e) {
                log.error("Retry interrupted while waiting for MAC address", e);
                Thread.currentThread().interrupt(); // Restore the interrupted status
                break;
            }
        }

        if (macAddr == null) {
            log.error("Exhausted all {} attempts to retrieve MAC address",
                    MAX_RETRY_COUNT);
        }

        return macAddr;
    }
}
