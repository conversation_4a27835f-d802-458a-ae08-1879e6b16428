/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

#include "stdafx.h"

#define SYSPREP_GENERALIZATION_TIMEOUT_MS                                                          \
   5 * 60 * 1000 // Usually finishes in 2 minutes. Buffer it to 5 minutes.
#define WAIT_FOR_AGENT_HELPER_TO_STARTUP_MS                                                        \
   5000 // Usually finishes in 2 seconds. Buffer it to 5 seconds.
#define MFW_SEND_MSG_TIMT_OUT_IN_MS                                                                \
   10 * 60 * 1000 // Wait for 10 minutes then timeout so IC agent can proceed and fail

using namespace svmga::core::util;

AgentHelperWrapper::AgentHelperWrapper() {}

AgentHelperWrapper::~AgentHelperWrapper() {}

void
ReleaseHelperProcess(PROCESS_INFORMATION &helperProc)
{
   // Release the helper process resources.
   if (helperProc.hThread) {
      CloseHandle(helperProc.hThread);
      helperProc.hThread = NULL;
   }

   if (helperProc.hProcess) {
      TerminateProcess(helperProc.hProcess, 0);
      // TerminateProcess is async, wait till the process end is signalled
      WaitForSingleObject(helperProc.hProcess, INFINITE);
      CloseHandle(helperProc.hProcess);
      helperProc.hProcess = NULL;
   }
}

bool
AgentHelperWrapper::SysprepGeneralize()
{
   // Format the params first.
   wstr params = wstr::printf(_T(SYSPREP_ARGS), _T(SYSPREP_UNATTEND_ANSWER_FILE_PATH));
   DWORD exitCode = RunProcess(_T(SYSPREP_COMMAND), params);

   // This is the most common error STILL_ACTIVE(259), which means the process is still running, but
   // we timed out on it. The process is still running because the invisible error dialog is modal
   // and we cannot close it.
   if (exitCode == STILL_ACTIVE) {
      SYSMSG_FUNC(Error, _T("Sysprep generalize failed with an invisible error dialog. Please fix ")
                         _T("the issue in your golden image."));
   } else if (exitCode != EXIT_SUCCESS) {
      SYSMSG_FUNC(Error, _T("Sysprep generalize failed with exit code: %d"), exitCode);
   }

   return exitCode == EXIT_SUCCESS;
}

DWORD
AgentHelperWrapper::RunProcess(LPCWSTR exe, LPCWSTR params)
{
   DWORD result = -1;
   PROCESS_INFORMATION proc = {0};
   STARTUPINFO info = {0};
   info.cb = sizeof(info);

   wstr cmdline = wstr::printf(L"\"%s\" %s", exe, params);
   SYSMSG_FUNC(Debug, _T("Executing command line: %s"), cmdline.p());

   if (!CreateProcessW(exe, cmdline.p_upd(), 0, 0, FALSE, 0, NULL, 0, &info, &proc)) {
      DWORD err = ::GetLastError();
      SYSMSG_FUNC(Error, _T("Unable to create process with the above command. Error code: %d"),
                  err);
      return err;
   }

   WaitForSingleObject(proc.hProcess, SYSPREP_GENERALIZATION_TIMEOUT_MS);
   if (!GetExitCodeProcess(proc.hProcess, &result)) {
      DWORD err = ::GetLastError();
      SYSMSG_FUNC(Error, _T("Unable to get exit code from the above command. Error code: %d"), err);
      CloseHandle(proc.hThread);
      CloseHandle(proc.hProcess);
      return err;
   }

   SYSMSG_FUNC(Debug, _T("Command line: %s finished with exit code: %d"), cmdline.p(), result);
   CloseHandle(proc.hThread);
   CloseHandle(proc.hProcess);

   return result;
}

std::wstring
AgentHelperWrapper::GetSysprepInterceptionString()
{
   std::wstring cmdline;

   // Leading and trailing " will ensure the space will not truncate the command line
   cmdline.append(_T("\""));
   cmdline.append(_T(AGENT_HELPER_FULL_PATH));
   cmdline.append(_T("\""));

   return cmdline;
}

// TODO: UBI-261 (https://omnissa.atlassian.net/browse/UBI-261)
// Mac address should be provided by a refactor'ed instance (remove redundancy in other projects).
std::wstring
AgentHelperWrapper::GetCurrentMacAddress()
{
   std::wstring ret;

   IP_ADAPTER_INFO AdapterInfo[16];
   DWORD dwBufLen = sizeof(AdapterInfo);
   DWORD dwStatus = GetAdaptersInfo(AdapterInfo, &dwBufLen);

   if (dwStatus != ERROR_SUCCESS) {
      SYSMSG_FUNC(Error, _T("GetAdaptersInfo failed with status: %d"), dwStatus);
      return ret;
   }

   PIP_ADAPTER_INFO pAdapterInfo = AdapterInfo;
   do {
      for (int i = 0; i < pAdapterInfo->AddressLength; i++) {
         WCHAR sec[4] = {0};

         if (i == pAdapterInfo->AddressLength - 1)
            swprintf(sec, 4, L"%02X", (int)pAdapterInfo->Address[i]);
         else
            swprintf(sec, 4, L"%02X:", (int)pAdapterInfo->Address[i]);
         ret.append(sec);
      }
      pAdapterInfo = pAdapterInfo->Next;

   } while (pAdapterInfo);

   return ret;
}

DWORD
AgentHelperWrapper::RunProcessAsync(LPCWSTR exe, LPCWSTR params)
{
   if (helperProc.hProcess) {
      // The process is already running.
      SYSMSG_FUNC(Error, _T("Second launch of the agent helper is NOT allowed"));
      return ERROR_ALREADY_EXISTS;
   }

   PROCESS_INFORMATION proc = {0};
   STARTUPINFO info = {0};
   info.cb = sizeof(info);

   wstr cmdline = wstr::printf(L"\"%s\" %s", exe, params);
   SYSMSG_FUNC(Debug, _T("Executing command line: %s"), cmdline.p());

   if (!CreateProcessW(exe, cmdline.p_upd(), 0, 0, FALSE, 0, NULL, 0, &info, &proc)) {
      DWORD err = ::GetLastError();
      SYSMSG_FUNC(Error, _T("Unable to create process with the above command. Error code: %d"),
                  err);
      return err;
   }

   SYSMSG_FUNC(Debug, _T("Command line: %s is executed asynchronizedly. Process handle: %d"),
               cmdline.p(), proc.hProcess);
   helperProc = proc;

   return ERROR_SUCCESS;
}

// Encapsulated synchronized message sending to the agent helper and wait for the response.
bool
SendMsgToHelper(LPCTSTR queue, LPCTSTR hint, PropertyBag pbIn)
{
   MessageFrameWork *mfw = NULL;
   MessageChannel *msgChannel = NULL;
   bool bReady = false;
   PropertyBag pbOut;
   MessageHandler::respType resp = MessageHandler::MsgError;
   wstr textToCheck;
   bool ret = false;

   SysMessageModule(0);
   mfw = MessageFrameWork::Start();
   if (!mfw) {
      SYSMSG_FUNC(Error, _T("Failed to start MessageFrameWork"));
      goto Cleanup;
   }

   SYSMSG_FUNC(Debug, _T("Successfully started MessageFrameWork"));

   // Ready the framework for use.
   bReady = mfw->Ready();

   if (!bReady) {
      SYSMSG_FUNC(Error, _T("MessageFrameWork is not ready to use"));
      goto Cleanup;
   }

   SYSMSG_FUNC(Debug, _T("MessageFrameWork is ready to use"));

   msgChannel = MessageFrameWork::System()->ConnectChannel(MessageFrameWork::SharedMemory);
   if (!msgChannel) {
      SYSMSG_FUNC(Error, _T("Failed to connect to the message channel using shared memory"));
      goto Cleanup;
   }

   SYSMSG_FUNC(Debug, _T("Successfully connected to the message channel using shared memory"));

   SYSMSG_FUNC(Debug, _T("About to send a message to agent helper over mfw with queue/hint: %s/%s"),
               queue, hint);
   resp = MessageFrameWork::System()->SendMsg(queue, hint, pbIn, pbOut, NULL, NULL, msgChannel,
                                              MFW_SEND_MSG_TIMT_OUT_IN_MS);

   if (resp != MessageHandler::MsgOk) {
      if (resp == MessageHandler::MsgNoQueueHandler) {
         SYSMSG_FUNC(Error, _T("No queue handler was found. Possibly Horizon Agent might be still ")
                            _T("running and intercepted the message"));
      } else {
         // Simple client doesn't need to process each error code type.
         SYSMSG_FUNC(Error,
                     _T("Failed to send above message to agent helper over mfw. respType: %s"),
                     MessageHandler::respTypeName(resp));
      }
      goto Cleanup;
   }

   // Also we need to check the error text is not there and Ack is honored
   textToCheck = pbOut.getErrorText();
   if (textToCheck.length() != 0) {
      SYSMSG_FUNC(Error, _T("The response bag contains an error text: %s"), textToCheck);
      goto Cleanup;
   }

   textToCheck = pbOut.get(L"resultText", L"");
   if (textToCheck.compare(ACKSTR)) {
      SYSMSG_FUNC(
         Error,
         _T("The response bag does not contain an Ack in the resultText. Actual resultText: %s"),
         textToCheck);
      goto Cleanup;
   }

   SYSMSG_FUNC(
      Debug,
      _T("Successfully sent above message to agent helper over mfw and got expected response"));

   // The message is sent successfully.
   ret = true;

Cleanup:
   // Release the message channel
   if (msgChannel) {
      MessageFrameWork::System()->CloseChannel(msgChannel);
      msgChannel = NULL;
   }

   // Stop the message framework
   if (mfw) {
      MessageFrameWork::Stop();
      mfw = NULL;
   }

   return ret;
}

bool
AgentHelperWrapper::LaunchTemplateHelper()
{
   // Launch the helper tool in service mode
   RunProcessAsync(_T(AGENT_HELPER_FULL_PATH), _T(AGENT_HELPER_PARAM_SERVICE_MODE));

   // TODO: UBI-316 (https://omnissa.atlassian.net/browse/UBI-316)
   // Replace this blind wait with a service readiness query to agent helper.
   Sleep(WAIT_FOR_AGENT_HELPER_TO_STARTUP_MS);

   bool bRet = SendMsgToHelper(L"TemplateHelper", L"PrepInternalTemplate", PropertyBag());

   // Need to close the helper process handle so helper can be removed
   ReleaseHelperProcess(helperProc);

   return bRet;
}

bool
AgentHelperWrapper::LaunchCloneHelper(std::wstring adServerName, std::wstring adSite)
{
   // Launch the helper tool in service mode
   RunProcessAsync(_T(AGENT_HELPER_FULL_PATH), _T(AGENT_HELPER_PARAM_SERVICE_MODE));

   // TODO: UBI-316 (https://omnissa.atlassian.net/browse/UBI-316)
   // Replace this blind wait with a service readiness query to agent helper.
   Sleep(WAIT_FOR_AGENT_HELPER_TO_STARTUP_MS);

   PropertyBag pbIn;
   pbIn.set(L"ad-server-name", adServerName);
   pbIn.set(L"ad-site", adSite);

   bool bRet = SendMsgToHelper(L"CloneHelper", L"PrepClone", pbIn);

   // Need to close the helper process handle so helper can be removed
   ReleaseHelperProcess(helperProc);

   return bRet;
}