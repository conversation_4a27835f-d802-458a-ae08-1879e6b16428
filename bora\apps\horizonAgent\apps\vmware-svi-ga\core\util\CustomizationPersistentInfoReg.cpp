/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*!
 * @file CustomizationPersistentInfoReg.cpp --
 * Implements the guest customization related persistent information stored
 * in registry.
 */

#include "stdafx.h"

#include <core/util/CustomizationPersistentInfoReg.h>
#include <core/util/NotifyViewAgent.h>
#include <horizonPaths.h>

namespace reg = svmga::common::windows::registry;

using namespace svmga::core::util;

//
// Constant to allow this 32-bit agent access 64 bit registry keys.
//
const unsigned long CustomizationPersistentInfoReg::WOW64_ACCESS_64KEY = 0x0100;

//
// Constants below are related to parent keys for all the registry values.
//
const std::wstring CustomizationPersistentInfoReg::SoftwareRegistryKeyName = _T("SOFTWARE");
const std::wstring CustomizationPersistentInfoReg::CompanyRegistryKeyName = HIC_COMPANY_NAME;
const std::wstring CustomizationPersistentInfoReg::SuiteRegistryKeyName = HIC_SUITE_NAME;
const std::wstring CustomizationPersistentInfoReg::ProductRegistryKeyName = HIC_PRODUCT_NAME;
const std::wstring CustomizationPersistentInfoReg::ProductRegistryKeyPath = HIC_KEY_PATH;
const std::wstring CustomizationPersistentInfoReg::GuestAgentRegistryKeyName = HIC_GA_REG_KEY;
const std::wstring CustomizationPersistentInfoReg::NativeAgentRegistryKeyName = HIC_NGA_REG_KEY;

const std::wstring CustomizationPersistentInfoReg::GoldenImageValueName = _T("GoldenImage");

//
// Horizon Agent Config values
//
const std::wstring CustomizationPersistentInfoReg::PoolIdValueName = _T("PoolID");

const std::wstring CustomizationPersistentInfoReg::OperationTypeValueName = _T("OPERATION_TYPE");

const std::wstring CustomizationPersistentInfoReg::MachinePwdValueName = _T("MachinePwd");

const std::wstring CustomizationPersistentInfoReg::VmForkedValueName = _T("VmForked");

const std::wstring CustomizationPersistentInfoReg::TemplateCustomizationValueName =
   _T("TemplateCustomization");

//
// Sysprep Values
//
const std::wstring CustomizationPersistentInfoReg::SysprepCompletedValueName =
   _T("CloneSysprepCompleted");
const std::wstring CustomizationPersistentInfoReg::SysprepGeneralizeCompletedValueName =
   _T("CloneSysprepGeneralizeCompleted");
const std::wstring CustomizationPersistentInfoReg::SysprepSpecializeCompletedValueName =
   _T("CloneSysprepSpecializeCompleted");
const std::wstring CustomizationPersistentInfoReg::SysprepOobeCompletedValueName =
   _T("CloneSysprepOobeCompleted");
const std::wstring CustomizationPersistentInfoReg::FastRefreshCompletedValueName =
   _T("CloneFastRefreshCompleted");

//
// Domain Join Values
//
const std::wstring CustomizationPersistentInfoReg::TemplateDomainJoinedValueName =
   _T("TemplateJoinedDomain");
const std::wstring CustomizationPersistentInfoReg::CloneDomainJoinedValueName =
   _T("CloneJoinedDomain");
const std::wstring CustomizationPersistentInfoReg::CloneDomainJoinedWithNetJoinDomainValueName =
   _T("CloneJoinedDomainWithNetJoinDomain");
const std::wstring CustomizationPersistentInfoReg::DomainJoinRebootCountValueName =
   _T("DomainJoinRebootCount");

const std::wstring CustomizationPersistentInfoReg::PreVerifyTrustRebootsValueName =
   _T("PreVerifyTrustReboots");
const std::wstring CustomizationPersistentInfoReg::MaxPreVerifyTrustRebootsValueName =
   _T("MaxPreVerifyTrustReboots");

const std::wstring CustomizationPersistentInfoReg::VerifyTrustFailedRebootsValueName =
   _T("VerifyTrustFailedReboots");
const std::wstring CustomizationPersistentInfoReg::MaxVerifyTrustFailedRebootsValueName =
   _T("MaxVerifyTrustFailedReboots");

const std::wstring CustomizationPersistentInfoReg::NetJoinDomainAttemptsValueName =
   _T("NetJoinDomainAttempts");
const std::wstring CustomizationPersistentInfoReg::MaxNetJoinDomainAttemptsValueName =
   _T("MaxNetJoinDomainAttempts");

const std::wstring CustomizationPersistentInfoReg::SkipCloneprepDomainJoinValueName =
   _T("SkipCloneprepDomainJoin");

const std::wstring CustomizationPersistentInfoReg::PostNetlogonStartDelayValueName =
   _T("PostNetlogonStartDelay");

//
// Machine Password Values
//
const std::wstring CustomizationPersistentInfoReg::TemplateMachinePasswordChangedValueName =
   _T("TemplateMachinePasswordChanged");
const std::wstring CustomizationPersistentInfoReg::CloneMachinePasswordChangedValueName =
   _T("CloneMachinePasswordChanged");
const std::wstring CustomizationPersistentInfoReg::TemplateMachinePwdChangeEnabledValueName =
   _T("ChangeTemplateMachinePassword");
const std::wstring CustomizationPersistentInfoReg::CloneMachinePwdChangeEnabledValueName =
   _T("ChangeCloneMachinePassword");
const std::wstring CustomizationPersistentInfoReg::MachinePwdInfoSecuredValueName =
   _T("MachinePwdInfoSecured");
const std::wstring CustomizationPersistentInfoReg::SecureMachinePwdInfoDisabledValueName =
   _T("SecureMachinePwdInfoDisabled");

//
// Rebooted Values
//
const std::wstring CustomizationPersistentInfoReg::CloneRebootedValueName = _T("CloneRebooted");
const std::wstring CustomizationPersistentInfoReg::TemplateRebootedValueName =
   _T("TemplateRebooted");
const std::wstring CustomizationPersistentInfoReg::ReplicaRebootedValueName = _T("ReplicaRebooted");

//
// Shutdown Values
//
const std::wstring CustomizationPersistentInfoReg::TemplateShutdownValueName =
   _T("TemplateShutdownNeeded");
const std::wstring CustomizationPersistentInfoReg::CloneShutdownValueName = _T("CloneShutdown");

//
// Renamed Values
//
const std::wstring CustomizationPersistentInfoReg::TemplateRenamedValueName = _T("TemplateRenamed");
const std::wstring CustomizationPersistentInfoReg::ParentRenamedValueName = _T("ParentRenamed");
const std::wstring CustomizationPersistentInfoReg::CloneRenamedValueName = _T("CloneRenamed");

//
// Mac Address Values
//
const std::wstring CustomizationPersistentInfoReg::MacAddressResetValueName =
   _T("CloneMacAddressReset");
const std::wstring CustomizationPersistentInfoReg::GoldenImageMacAddressValueName =
   _T("GoldenImageMacAddress");

//
// IP Functions
//
const std::wstring CustomizationPersistentInfoReg::IpReleaseOnShutdownValueName =
   _T("IpReleaseOnShutdown");
const std::wstring CustomizationPersistentInfoReg::IpRenewedValueName = _T("IpRenewed");

//
// IPv6 Values
//
const std::wstring CustomizationPersistentInfoReg::IPv6SupportEnabledValueName = _T("IPv6Enabled");

//
// GPUpdate Values
//
const std::wstring CustomizationPersistentInfoReg::GPUpdateEnabledOnCloneValueName =
   _T("GPUpdateEnabledOnClone");
const std::wstring CustomizationPersistentInfoReg::GPUpdateEnabledOnITValueName =
   _T("GPUpdateEnabledOnIT");

//
// DHCP Work Around
//
const std::wstring CustomizationPersistentInfoReg::DisableDhcpServiceValueName =
   _T("DisableDhcpService");

//
// Script Values
//
const std::wstring CustomizationPersistentInfoReg::PostCustScriptDisabledValueName =
   _T("PostCustomizationScriptDisabled");
const std::wstring CustomizationPersistentInfoReg::ScriptsSecuredValueName = _T("ScriptsSecured");
const std::wstring CustomizationPersistentInfoReg::PostCustScriptSecuredValueName =
   _T("PostCustScriptSecured");
const std::wstring CustomizationPersistentInfoReg::PreShutdownScriptSecuredValueName =
   _T("PreShutdownScriptSecured");
const std::wstring CustomizationPersistentInfoReg::PostCustScriptConfiguredValueName =
   _T("PostCustScriptConfigured");
const std::wstring CustomizationPersistentInfoReg::PreShutdownScriptConfiguredValueName =
   _T("PreShutdownScriptConfigured");
const std::wstring CustomizationPersistentInfoReg::ScriptLogOutputValueName = _T("ScriptLogOutput");

//
// License Activation Values
//
const std::wstring CustomizationPersistentInfoReg::LicenseActivationEnabledValueName =
   _T("LicenseActivationEnabled");
const std::wstring CustomizationPersistentInfoReg::LicenseActivationEnabledForSysprepValueName =
   _T("LicenseActivationEnabledForSysprep");
const std::wstring CustomizationPersistentInfoReg::LicenseActivatedValueName =
   _T("LicenseActivated");
const std::wstring CustomizationPersistentInfoReg::LicenseRearmEnabledValueName =
   _T("LicenseRearmEnabled");
const std::wstring CustomizationPersistentInfoReg::LicenseRearmCompletedValueName =
   _T("LicenseRearmCompleted");

//
// Template Customization Values
//
const std::wstring CustomizationPersistentInfoReg::TemplateDomainJoinFinishedValueName =
   _T("TemplateDomainJoinFinished");
const std::wstring CustomizationPersistentInfoReg::ITCustomizationStartTimeSetValueName =
   _T("ITCustomizationStartTimeSet");

//
// Clone Customization Values
//
const std::wstring CustomizationPersistentInfoReg::CloneCustomizationCompletedValueName =
   _T("CloneCustomizationCompleted");

//
// Replica Customization Values
//
const std::wstring CustomizationPersistentInfoReg::ReplicaCustomizationCompletedValueName =
   _T("ReplicaCustomizationCompleted");

//
// Pre/Post Integration Values
//
const std::wstring CustomizationPersistentInfoReg::PreIntegrationValuesParsedValueName =
   _T("ClonePreIntegrationValuesParsed");
const std::wstring CustomizationPersistentInfoReg::PostIntegrationValuesParsedValueName =
   _T("ClonePostIntegrationValuesParsed");

//
// Profile Redirection
//
const std::wstring CustomizationPersistentInfoReg::ProfilesRedirectedValueName =
   _T("ProfilesRedirected");

//
// Persistent Disk Ready
//
const std::wstring CustomizationPersistentInfoReg::PersistentDisksReadyValueName =
   _T("PersistentDisksReady");

//
// NL Flags
//
const std::wstring CustomizationPersistentInfoReg::NLFlagsDisabledValueName = _T("NLFlagsDisabled");

//
// Service Notify Values
//
const std::wstring CustomizationPersistentInfoReg::ServiceNotifyEnabledValueName =
   _T("ServiceNotifyEnabled");
const std::wstring CustomizationPersistentInfoReg::ServiceNotifyMaxRestartsValueName =
   _T("ServiceNotifyMaxRestarts");
const std::wstring CustomizationPersistentInfoReg::ServiceNotifyStartDelayValueName =
   _T("ServiceNotifyStartDelay");

const std::wstring CustomizationPersistentInfoReg::ServiceMaxRetriesValueName =
   _T("ServiceMaxRetries");
const std::wstring CustomizationPersistentInfoReg::ServiceRetryDelayValueName =
   _T("ServiceRetryDelay");

const std::wstring CustomizationPersistentInfoReg::ServiceShutdownDelayValueName =
   _T("ServiceShutdownDelay");

/*!
 * @see CustomizationPersistentInfoReg::CustomizationPersistentInfoReg
 */

CustomizationPersistentInfoReg::CustomizationPersistentInfoReg() :
   _Registry(Registry::GetInstance())
{
   if (!ValidateParentKeys()) {
      SYSMSG_FUNC(Error, L"Parent Key Validation Failed");
   }

   SYSMSG_FUNC(Trace, _T("Opened guest registry key for accessing customization info."));
}


/*!
 * @see CustomizationPersistentInfoReg::FlushKeys
 */

void
CustomizationPersistentInfoReg::FlushKeys()
{
   _Registry->FlushKey(RegType::Service);
   _Registry->FlushKey(RegType::Ga);
   _Registry->FlushKey(RegType::Nga);
   _Registry->FlushKey(RegType::GuestInfo);
   _Registry->FlushKey(RegType::SystemSetup);
   _Registry->FlushKey(RegType::NodeManager);

   /*
    * No need to flush these keys:
    * RegType::Setup since it is ONLY for bootstrapping
    * RegType::AgentConfig since it is ONLY for Horizon Agent/Agent Helper to write
    * RegType::HorizonAgent since we will keep the OperationType as clone LCM indicator
    */

   SYSMSG_FUNC(Debug, _T("Registry Keys Flushed"));
}


/*!
 * @see CustomizationPersistentInfoReg::ValidateParentKeys
 */

bool
CustomizationPersistentInfoReg::ValidateParentKeys()
{
   HRESULT hr = S_OK;
   bool bExists = false;

   //
   // Software
   //
   reg::ExistingRegKey softwareRegKey = reg::ExistingRegKey(
      HKEY_LOCAL_MACHINE, SoftwareRegistryKeyName, KEY_ALL_ACCESS | WOW64_ACCESS_64KEY, hr);
   if (FAILED(hr)) {
      SYSMSG_FUNC(Error, L"Failed Opening Key: %ws, 0x%X", SoftwareRegistryKeyName.c_str(), hr);
      return false;
   }

   //
   // Omnissa
   //
   bExists = false;
   hr = softwareRegKey.SubKeyExists(CompanyRegistryKeyName, bExists);
   if (bExists == false) {
      reg::NewRegKey companyRegKey(softwareRegKey, CompanyRegistryKeyName, 0,
                                   KEY_ALL_ACCESS | WOW64_ACCESS_64KEY);
   }

   reg::ExistingRegKey companyRegKey = reg::ExistingRegKey(softwareRegKey, CompanyRegistryKeyName,
                                                           KEY_ALL_ACCESS | WOW64_ACCESS_64KEY, hr);
   if (FAILED(hr)) {
      SYSMSG_FUNC(Error, L"Failed Opening Key: %ws, 0x%X", CompanyRegistryKeyName.c_str(), hr);
      return false;
   }

   //
   // Horizon
   //
   bExists = false;
   hr = companyRegKey.SubKeyExists(SuiteRegistryKeyName, bExists);
   if (bExists == false) {
      reg::NewRegKey suiteRegKey(companyRegKey, SuiteRegistryKeyName, 0,
                                 KEY_ALL_ACCESS | WOW64_ACCESS_64KEY);
   }

   reg::ExistingRegKey suiteRegKey = reg::ExistingRegKey(companyRegKey, SuiteRegistryKeyName,
                                                         KEY_ALL_ACCESS | WOW64_ACCESS_64KEY, hr);
   if (FAILED(hr)) {
      SYSMSG_FUNC(Error, L"Failed Opening Key: %ws, 0x%X", SuiteRegistryKeyName.c_str(), hr);
      return false;
   }

   //
   // Instant Clone Agent
   //
   bExists = false;
   hr = suiteRegKey.SubKeyExists(ProductRegistryKeyName, bExists);
   if (bExists == false) {
      reg::NewRegKey productRegKey(suiteRegKey, HIC_PRODUCT_NAME, 0,
                                   KEY_ALL_ACCESS | WOW64_ACCESS_64KEY);
   }

   reg::ExistingRegKey productRegKey = reg::ExistingRegKey(suiteRegKey, ProductRegistryKeyName,
                                                           KEY_ALL_ACCESS | WOW64_ACCESS_64KEY, hr);
   if (FAILED(hr)) {
      SYSMSG_FUNC(Error, L"Failed Opening Key: %ws, 0x%X", ProductRegistryKeyName.c_str(), hr);
      return false;
   }

   //
   // ga
   //
   bExists = false;
   hr = productRegKey.SubKeyExists(GuestAgentRegistryKeyName, bExists);
   if (bExists == false) {
      reg::NewRegKey gaRegKey(productRegKey, GuestAgentRegistryKeyName, 0,
                              KEY_ALL_ACCESS | WOW64_ACCESS_64KEY);
   }

   //
   // nga
   //
   bExists = false;
   hr = productRegKey.SubKeyExists(NativeAgentRegistryKeyName, bExists);
   if (bExists == false) {
      reg::NewRegKey ngaRegKey(productRegKey, NativeAgentRegistryKeyName, 0,
                               KEY_ALL_ACCESS | WOW64_ACCESS_64KEY);
   }

   return true;
}

//@see CustomizationPersistentInfoReg::SetCustomizationResult.
void
CustomizationPersistentInfoReg::SetCustomizationResult(SvmPolicyState psState,
                                                       NotifyVdmStatusValue vsValue, bool force)
{
   HRESULT hr = S_OK;
   NotifyViewAgent nva;

   if (force) {
      hr = _Registry->SetValue(POLICY_STATE_REGVAL, psState, RegType::Service);
      if (vsValue == CustomizationSucceeded) {
         nva.MarkCustomizationSucceeded();
      } else {
         nva.MarkCustomizationFailed();
      }
   } else {
      DWORD val = 0;
      hr = _Registry->GetValue(POLICY_STATE_REGVAL, SVM_PS_ILLEGAL_STATE, val, RegType::Service);
      if (val == SVM_PS_ILLEGAL_STATE || val == SVM_PS_SUCCESS) {
         _Registry->SetValue(POLICY_STATE_REGVAL, psState, RegType::Service);
         /*
          * For vsValue, only CustomizationSucceeded or CustomizationFailed
          * should be specified.
          */
         if (vsValue == CustomizationSucceeded) {
            nva.MarkCustomizationSucceeded();
         } else {
            nva.MarkCustomizationFailed();
         }
      }
   }
}

bool
CustomizationPersistentInfoReg::IsUniversalPrep()
{
   // TODO: Might need a separate regkey path to store this value
   DWORD val = 0;
   _Registry->GetValue(GoldenImageValueName, 0, val, RegType::Setup);
   return val == 1;
}

bool
CustomizationPersistentInfoReg::IsPoolIdSet()
{
   std::wstring val;
   _Registry->GetValue(PoolIdValueName, val, RegType::AgentConfig);
   return !val.empty();
}

std::wstring
CustomizationPersistentInfoReg::GetOperationType()
{
   std::wstring val;
   _Registry->GetValue(OperationTypeValueName, val, RegType::HorizonAgent);
   return val;
}

bool
CustomizationPersistentInfoReg::IsMachinePwdSet()
{
   std::wstring val;
   _Registry->GetValue(MachinePwdValueName, val, RegType::AgentConfig);
   return !val.empty();
}

void
CustomizationPersistentInfoReg::ClearMachinePwd()
{
   _Registry->SetValue(MachinePwdValueName, 0, RegType::AgentConfig);
}

//
// Fork Functions
//
bool
CustomizationPersistentInfoReg::IsVmForked()
{
   DWORD val = 0;
   _Registry->GetValue(VmForkedValueName, 0, val, RegType::Nga);
   return val == 1;
}

void
CustomizationPersistentInfoReg::MarkVmForked()
{
   _Registry->SetValue(VmForkedValueName, 1, RegType::Nga);
}

void
CustomizationPersistentInfoReg::ClearVmForked()
{
   _Registry->SetValue(VmForkedValueName, 0, RegType::Nga);
}


//
// Sysprep Functions
//
bool
CustomizationPersistentInfoReg::IsSysprepCompleted()
{
   DWORD val = 0;
   _Registry->GetValue(SysprepCompletedValueName, 0, val);
   return val == 1;
}

void
CustomizationPersistentInfoReg::MarkSysprepCompleted()
{
   _Registry->SetValue(SysprepCompletedValueName, 1);
}

bool
CustomizationPersistentInfoReg::IsSysprepGeneralizeCompleted()
{
   DWORD val = 0;
   _Registry->GetValue(SysprepGeneralizeCompletedValueName, 0, val);
   return val == 1;
}

void
CustomizationPersistentInfoReg::MarkSysprepGeneralizeCompleted()
{
   _Registry->SetValue(SysprepGeneralizeCompletedValueName, 1);
}

bool
CustomizationPersistentInfoReg::IsSysprepSpecializeCompleted()
{
   DWORD val = 0;
   _Registry->GetValue(SysprepSpecializeCompletedValueName, 0, val);
   return val == 1;
}

void
CustomizationPersistentInfoReg::MarkSysprepSpecializeCompleted()
{
   _Registry->SetValue(SysprepSpecializeCompletedValueName, 1);
}

bool
CustomizationPersistentInfoReg::IsSysprepOobeCompleted()
{
   DWORD val = 0;
   _Registry->GetValue(SysprepOobeCompletedValueName, 0, val);
   return val == 1;
}

void
CustomizationPersistentInfoReg::MarkSysprepOobeCompleted()
{
   _Registry->SetValue(SysprepOobeCompletedValueName, 1);
}

bool
CustomizationPersistentInfoReg::IsFastRefreshCompleted()
{
   DWORD val = 0;
   _Registry->GetValue(FastRefreshCompletedValueName, 0, val);
   return val == 1;
}

void
CustomizationPersistentInfoReg::MarkFastRefreshCompleted()
{
   _Registry->SetValue(FastRefreshCompletedValueName, 1);
}

//
// Domain Join Functions
//
bool
CustomizationPersistentInfoReg::IsTemplateDomainJoined()
{
   DWORD val = 0;
   _Registry->GetValue(TemplateDomainJoinedValueName, 0, val);
   return val == 1;
}

void
CustomizationPersistentInfoReg::MarkTemplateDomainJoined()
{
   _Registry->SetValue(TemplateDomainJoinedValueName, 1);
}

bool
CustomizationPersistentInfoReg::IsCloneDomainJoined()
{
   DWORD val = 0;
   _Registry->GetValue(CloneDomainJoinedValueName, 0, val);
   return val == 1;
}

bool
CustomizationPersistentInfoReg::IsCloneDomainJoinedWithNetJoinDomain()
{
   DWORD val = 0;
   _Registry->GetValue(CloneDomainJoinedWithNetJoinDomainValueName, 0, val);
   return val == 1;
}

void
CustomizationPersistentInfoReg::MarkCloneDomainJoined()
{
   _Registry->SetValue(CloneDomainJoinedValueName, 1);
}

void
CustomizationPersistentInfoReg::MarkCloneDomainJoinedWithNetJoinDomain()
{
   _Registry->SetValue(CloneDomainJoinedWithNetJoinDomainValueName, 1);
}

int
CustomizationPersistentInfoReg::GetDomainJoinRebootCount()
{
   DWORD val = 0;
   _Registry->GetValue(DomainJoinRebootCountValueName, 0, val);
   return val;
}

void
CustomizationPersistentInfoReg::SetDomainJoinRebootCount(int iCount)
{
   _Registry->SetValue(DomainJoinRebootCountValueName, iCount);
}

bool
CustomizationPersistentInfoReg::SkipCloneprepDomainJoin()
{
   DWORD val = 0;
   _Registry->GetValue(SkipCloneprepDomainJoinValueName, 0, val);
   return val == 1;
}


//
// Pre-Verify Trust Reboots
//
bool
CustomizationPersistentInfoReg::IsPreVerifyTrustRebootEnabled()
{
   DWORD dwMaxReboots = 0;
   DWORD dwCompletedReboots = 0;

   dwMaxReboots = GetMaxPreVerifyTrustReboots();
   dwCompletedReboots = GetPreVerifyTrustReboots();

   if (dwMaxReboots == 0 || (dwCompletedReboots >= dwMaxReboots)) {
      return false;
   }

   return true;
}

void
CustomizationPersistentInfoReg::IncrementPreVerifyTrustReboots()
{
   DWORD dwReboots = GetPreVerifyTrustReboots();
   _Registry->SetValue(PreVerifyTrustRebootsValueName, ++dwReboots);
}

int
CustomizationPersistentInfoReg::GetPreVerifyTrustReboots()
{
   DWORD dwReboots = 0;
   _Registry->GetValue(PreVerifyTrustRebootsValueName, 0, dwReboots);
   return dwReboots;
}

int
CustomizationPersistentInfoReg::GetMaxPreVerifyTrustReboots()
{
   DWORD dwReboots = 0;
   _Registry->GetValue(MaxPreVerifyTrustRebootsValueName, 0, dwReboots);
   return dwReboots;
}

//
// Verify Trust Failed Reboots
//
bool
CustomizationPersistentInfoReg::IsVerifyTrustFailedRebootEnabled()
{
   DWORD dwMaxReboots = 0;
   DWORD dwCompletedReboots = 0;

   dwMaxReboots = GetMaxVerifyTrustFailedReboots();
   dwCompletedReboots = GetVerifyTrustFailedReboots();

   if (dwMaxReboots == 0 || (dwCompletedReboots >= dwMaxReboots)) {
      return false;
   }

   return true;
}

int
CustomizationPersistentInfoReg::GetVerifyTrustFailedReboots()
{
   DWORD dwReboots = 0;
   _Registry->GetValue(VerifyTrustFailedRebootsValueName, 0, dwReboots);
   return dwReboots;
}

void
CustomizationPersistentInfoReg::IncrementVerifyTrustFailedReboots()
{
   DWORD dwReboots = GetVerifyTrustFailedReboots();
   _Registry->SetValue(VerifyTrustFailedRebootsValueName, ++dwReboots);
}

int
CustomizationPersistentInfoReg::GetMaxVerifyTrustFailedReboots()
{
   DWORD dwReboots = 0;
   _Registry->GetValue(MaxVerifyTrustFailedRebootsValueName, 0, dwReboots);
   return dwReboots;
}

//
// Verify Trust Failed use NetJoinDomain
//
bool
CustomizationPersistentInfoReg::IsNetJoinDomainEnabled()
{
   DWORD dwMaxAttempts = 0;
   DWORD dwCompletedAttempts = 0;

   dwMaxAttempts = GetMaxNetJoinDomainAttempts();
   dwCompletedAttempts = GetNetJoinDomainAttempts();

   if (dwMaxAttempts == 0 || (dwCompletedAttempts >= dwMaxAttempts)) {
      return false;
   }

   return true;
}

int
CustomizationPersistentInfoReg::GetNetJoinDomainAttempts()
{
   DWORD dwAttempts = 0;
   _Registry->GetValue(NetJoinDomainAttemptsValueName, 0, dwAttempts);
   return dwAttempts;
}

void
CustomizationPersistentInfoReg::IncrementNetJoinDomainAttempts()
{
   DWORD dwAttempts = GetNetJoinDomainAttempts();
   _Registry->SetValue(NetJoinDomainAttemptsValueName, ++dwAttempts);
}

int
CustomizationPersistentInfoReg::GetMaxNetJoinDomainAttempts()
{
   DWORD dwAttempts = 0;
   _Registry->GetValue(MaxNetJoinDomainAttemptsValueName, 0, dwAttempts);
   return dwAttempts;
}

DWORD
CustomizationPersistentInfoReg::GetPostNetlogonStartDelay()
{
   DWORD dwDelay = 0;
   _Registry->GetValue(PostNetlogonStartDelayValueName, 0, dwDelay);
   return dwDelay;
}

//
// Machine Password Functions
//
bool
CustomizationPersistentInfoReg::IsTemplateMachinePasswordChanged()
{
   DWORD val = 0;
   _Registry->GetValue(TemplateMachinePasswordChangedValueName, 0, val);
   return val == 1;
}

void
CustomizationPersistentInfoReg::MarkTemplateMachinePasswordChanged()
{
   _Registry->SetValue(TemplateMachinePasswordChangedValueName, 1);
}

bool
CustomizationPersistentInfoReg::IsCloneMachinePasswordChanged()
{
   DWORD val = 0;
   _Registry->GetValue(CloneMachinePasswordChangedValueName, 0, val);
   return val == 1;
}

void
CustomizationPersistentInfoReg::MarkCloneMachinePasswordChanged()
{
   _Registry->SetValue(CloneMachinePasswordChangedValueName, 1);
}

bool
CustomizationPersistentInfoReg::IsTemplateMachinePwdChangeEnabled()
{
   DWORD val = 0;
   _Registry->GetValue(TemplateMachinePwdChangeEnabledValueName, 0, val);
   return val == 1;
}

bool
CustomizationPersistentInfoReg::IsCloneMachinePwdChangeEnabled()
{
   DWORD val = 0;
   _Registry->GetValue(CloneMachinePwdChangeEnabledValueName, 0, val);
   return val == 1;
}

bool
CustomizationPersistentInfoReg::IsSecureMachinePwdInfoDisabled()
{
   DWORD val = 0;
   _Registry->GetValue(SecureMachinePwdInfoDisabledValueName, 0, val);
   return val == 1;
}

bool
CustomizationPersistentInfoReg::IsMachinePwdInfoSecured()
{
   DWORD val = 0;
   _Registry->GetValue(MachinePwdInfoSecuredValueName, 0, val);
   return val == 1;
}

bool
CustomizationPersistentInfoReg::MarkMachinePwdInfoSecured()
{
   return (S_OK == _Registry->SetValue(MachinePwdInfoSecuredValueName, 1));
}

bool
CustomizationPersistentInfoReg::ClearSecuredMachinePwdInfo()
{
   return (S_OK == _Registry->SetValue(MachinePwdInfoSecuredValueName, 2));
}

//
// Rename Functions
//
bool
CustomizationPersistentInfoReg::IsTemplateRenamed()
{
   DWORD val = 0;
   _Registry->GetValue(TemplateRenamedValueName, 0, val);
   return val == 1;
}

void
CustomizationPersistentInfoReg::MarkTemplateRenamed()
{
   _Registry->SetValue(TemplateRenamedValueName, 1);
}

bool
CustomizationPersistentInfoReg::IsParentRenamed()
{
   DWORD val = 0;
   _Registry->GetValue(ParentRenamedValueName, 0, val);
   return val == 1;
}

void
CustomizationPersistentInfoReg::MarkParentRenamed()
{
   _Registry->SetValue(ParentRenamedValueName, 1);
}

bool
CustomizationPersistentInfoReg::IsCloneRenamed()
{
   DWORD val = 0;
   _Registry->GetValue(CloneRenamedValueName, 0, val);
   return val == 1;
}

void
CustomizationPersistentInfoReg::MarkCloneRenamed()
{
   _Registry->SetValue(CloneRenamedValueName, 1);
}


//
// Reboot Functions
//
bool
CustomizationPersistentInfoReg::IsCloneRebooted()
{
   DWORD val = 0;
   _Registry->GetValue(CloneRebootedValueName, 0, val, RegType::Nga);
   return val == 1;
}

void
CustomizationPersistentInfoReg::MarkCloneRebooted()
{
   _Registry->SetValue(CloneRebootedValueName, 1, RegType::Nga);
}

bool
CustomizationPersistentInfoReg::IsTemplateRebooted()
{
   DWORD val = 0;
   _Registry->GetValue(TemplateRebootedValueName, 0, val, RegType::Nga);
   return val == 1;
}

void
CustomizationPersistentInfoReg::MarkTemplateRebooted()
{
   _Registry->SetValue(TemplateRebootedValueName, 1, RegType::Nga);
}

bool
CustomizationPersistentInfoReg::IsReplicaRebooted()
{
   DWORD val = 0;
   _Registry->GetValue(ReplicaRebootedValueName, 0, val, RegType::Nga);
   return val == 1;
}

void
CustomizationPersistentInfoReg::MarkReplicaRebooted()
{
   _Registry->SetValue(ReplicaRebootedValueName, 1, RegType::Nga);
}

//
// Shutdown Functions
//
bool
CustomizationPersistentInfoReg::IsTemplateShutdownNeeded()
{
   DWORD val = 0;
   _Registry->GetValue(TemplateShutdownValueName, 0, val);
   return val == 1;
}

void
CustomizationPersistentInfoReg::MarkTemplateShutdownNeeded()
{
   _Registry->SetValue(TemplateShutdownValueName, 1);
}

void
CustomizationPersistentInfoReg::MarkTemplateShutdownDone()
{
   _Registry->SetValue(TemplateShutdownValueName, 0);
}

bool
CustomizationPersistentInfoReg::IsCloneShutdownNeeded()
{
   DWORD val = 0;
   _Registry->GetValue(CloneShutdownValueName, 0, val);
   return val == 1;
}

void
CustomizationPersistentInfoReg::MarkCloneShutdownNeeded()
{
   _Registry->SetValue(CloneShutdownValueName, 1);
}

void
CustomizationPersistentInfoReg::MarkCloneShutdownDone()
{
   _Registry->SetValue(CloneShutdownValueName, 0);

   SYSMSG_FUNC(Debug, _T("Marked Clone Shutdown Done. ")
                      _T("About To Shutdown"));
}

//
// Mac Address Functions
//
bool
CustomizationPersistentInfoReg::IsMacAddressReset()
{
   DWORD val = 0;
   _Registry->GetValue(MacAddressResetValueName, 0, val);
   return val == 1;
}

void
CustomizationPersistentInfoReg::MarkMacAddressReset()
{
   _Registry->SetValue(MacAddressResetValueName, 1);
}

std::wstring
CustomizationPersistentInfoReg::GetGoldenImageMacAddress()
{
   std::wstring val;
   _Registry->GetValue(GoldenImageMacAddressValueName, val);
   return val;
}

void
CustomizationPersistentInfoReg::MarkGoldenImageMacAddress(const std::wstring &entry)
{
   _Registry->SetValue(GoldenImageMacAddressValueName, entry);
}

//
// IP Functions
//
int
CustomizationPersistentInfoReg::GetIpReleaseOnShutdownState()
{
   DWORD val = 0;
   _Registry->GetValue(IpReleaseOnShutdownValueName, 1, val);
   return val;
}

bool
CustomizationPersistentInfoReg::IsIpRenewed()
{
   DWORD val = 0;
   _Registry->GetValue(IpRenewedValueName, 0, val);
   return val == 1;
}

bool
CustomizationPersistentInfoReg::MarkIpRenewed()
{
   return (S_OK == _Registry->SetValue(IpRenewedValueName, 1));
}

bool
CustomizationPersistentInfoReg::ClearIpRenewed()
{
   return (S_OK == _Registry->SetValue(IpRenewedValueName, 0));
}

//
// IPv6 Functions
//
bool
CustomizationPersistentInfoReg::IsIPv6SupportEnabled()
{
   DWORD val = 0;
   _Registry->GetValue(IPv6SupportEnabledValueName, 1, val);
   return val == 1;
}

//
// Dhcp Fix Functions
//
bool
CustomizationPersistentInfoReg::DisableDhcpService()
{
   DWORD val = 0;
   _Registry->GetValue(DisableDhcpServiceValueName, 1, val);
   return val == 1;
}

//
// GPUpdate Functions
//
bool
CustomizationPersistentInfoReg::IsGPUpdateEnabledOnClone()
{
   DWORD val = 0;
   _Registry->GetValue(GPUpdateEnabledOnCloneValueName, 1, val);
   return val == 1;
}

bool
CustomizationPersistentInfoReg::IsGPUpdateEnabledOnIT()
{
   DWORD val = 0;
   _Registry->GetValue(GPUpdateEnabledOnITValueName, 1, val);
   return val == 1;
}

//
// Script Functions
//
bool
CustomizationPersistentInfoReg::IsPostCustScriptDisabled()
{
   DWORD val = 0;
   _Registry->GetValue(PostCustScriptDisabledValueName, 0, val);
   return val == 1;
}

bool
CustomizationPersistentInfoReg::AreScriptsSecured()
{
   DWORD val = 0;
   _Registry->GetValue(ScriptsSecuredValueName, 0, val);
   return val == 1;
}

void
CustomizationPersistentInfoReg::MarkScriptsSecured()
{
   _Registry->SetValue(ScriptsSecuredValueName, 1);
}

bool
CustomizationPersistentInfoReg::IsPostCustScriptSecured()
{
   DWORD val = 0;
   _Registry->GetValue(PostCustScriptSecuredValueName, 0, val);
   return val == 1;
}

bool
CustomizationPersistentInfoReg::IsPreShutdownScriptSecured()
{
   DWORD val = 0;
   _Registry->GetValue(PreShutdownScriptSecuredValueName, 0, val);
   return val == 1;
}

void
CustomizationPersistentInfoReg::MarkPostCustScriptSecured()
{
   _Registry->SetValue(PostCustScriptSecuredValueName, 1);
}

void
CustomizationPersistentInfoReg::MarkPreShutdownScriptSecured()
{
   _Registry->SetValue(PreShutdownScriptSecuredValueName, 1);
}

bool
CustomizationPersistentInfoReg::IsPostCustScriptConfigured()
{
   DWORD val = 0;
   _Registry->GetValue(PostCustScriptConfiguredValueName, 0, val);
   return val == 1;
}

bool
CustomizationPersistentInfoReg::IsPreShutdownScriptConfigured()
{
   DWORD val = 0;
   _Registry->GetValue(PreShutdownScriptConfiguredValueName, 0, val);
   return val == 1;
}

void
CustomizationPersistentInfoReg::MarkPostCustScriptConfigured()
{
   _Registry->SetValue(PostCustScriptConfiguredValueName, 1);
}

void
CustomizationPersistentInfoReg::MarkPreShutdownScriptConfigured()
{
   _Registry->SetValue(PreShutdownScriptConfiguredValueName, 1);
}

bool
CustomizationPersistentInfoReg::IsLogScriptOutputEnabled()
{
   DWORD val = 0;
   _Registry->GetValue(ScriptLogOutputValueName, 1, val);
   return val == 1;
}


//
// License Activation Functions
//
bool
CustomizationPersistentInfoReg::IsLicenseActivationEnabled(bool bSysprep)
{
   DWORD val = 0;

   //
   // Default to enabled for Sysprep, disabled for cloneprep
   //
   if (bSysprep) {
      _Registry->GetValue(LicenseActivationEnabledForSysprepValueName, 1, val);
   } else {
      _Registry->GetValue(LicenseActivationEnabledValueName, 0, val);
   }
   return val == 1;
}

bool
CustomizationPersistentInfoReg::IsLicenseActivated()
{
   DWORD val = 0;
   _Registry->GetValue(LicenseActivatedValueName, 0, val);
   return val == 1;
}

void
CustomizationPersistentInfoReg::MarkLicenseActivated()
{
   _Registry->SetValue(LicenseActivatedValueName, 1);
}

bool
CustomizationPersistentInfoReg::IsLicenseRearmEnabled()
{
   DWORD val = 0;
   _Registry->GetValue(LicenseRearmEnabledValueName, 1, val);
   return val == 1;
}

bool
CustomizationPersistentInfoReg::IsLicenseRearmCompleted()
{
   DWORD val = 0;
   _Registry->GetValue(LicenseRearmCompletedValueName, 0, val);
   return val == 1;
}

void
CustomizationPersistentInfoReg::MarkLicenseRearmCompleted()
{
   _Registry->SetValue(LicenseRearmCompletedValueName, 1);
}


//
// Template Customization Functions
//
bool
CustomizationPersistentInfoReg::IsTemplateCustomizationNeeded()
{
   DWORD val = 0;
   _Registry->GetValue(TemplateCustomizationValueName, 0, val);
   return val == 1;
}

void
CustomizationPersistentInfoReg::MarkTemplateCustomizationNeeded()
{
   _Registry->SetValue(TemplateCustomizationValueName, 1);
}

void
CustomizationPersistentInfoReg::MarkTemplateCustomizationDone()
{
   _Registry->SetValue(TemplateCustomizationValueName, 2);
}

bool
CustomizationPersistentInfoReg::IsTemplateCustomizationDone()
{
   DWORD val = 0;
   _Registry->GetValue(TemplateCustomizationValueName, 0, val);
   return val == 2;
}

bool
CustomizationPersistentInfoReg::IsITDomainJoinFinished()
{
   DWORD val = 0;
   _Registry->GetValue(TemplateDomainJoinFinishedValueName, 0, val);
   return val == 1;
}

void
CustomizationPersistentInfoReg::SetITDomainJoinFinished()
{
   _Registry->SetValue(TemplateDomainJoinFinishedValueName, 1);
}

//
// Clone Customization Fuctions
//
bool
CustomizationPersistentInfoReg::IsCloneCustomizationCompleted()
{
   DWORD val = 0;
   _Registry->GetValue(CloneCustomizationCompletedValueName, 0, val);
   return val == 1;
}

void
CustomizationPersistentInfoReg::MarkCloneCustomizationCompleted()
{
   _Registry->SetValue(CloneCustomizationCompletedValueName, 1);
}

//
// IT Customization Fuctions
//
bool
CustomizationPersistentInfoReg::IsITCustomizationStartTimeSet()
{
   DWORD val = 0;
   _Registry->GetValue(ITCustomizationStartTimeSetValueName, 0, val);
   return val == 1;
}

void
CustomizationPersistentInfoReg::MarkITCustomizationStartTimeSet()
{
   _Registry->SetValue(ITCustomizationStartTimeSetValueName, 1);
}

//
// Replica Customization Functions
//
bool
CustomizationPersistentInfoReg::IsReplicaCustomizationCompleted()
{
   DWORD val = 0;
   _Registry->GetValue(ReplicaCustomizationCompletedValueName, 0, val);
   return val == 1;
}

void
CustomizationPersistentInfoReg::MarkReplicaCustomizationCompleted()
{
   _Registry->SetValue(ReplicaCustomizationCompletedValueName, 1);
}

//
// Pre/Post Intregration Values
//
bool
CustomizationPersistentInfoReg::PreIntegrationValuesParsed()
{
   DWORD val = 0;
   _Registry->GetValue(PreIntegrationValuesParsedValueName, 0, val);
   return val == 1;
}

void
CustomizationPersistentInfoReg::MarkPreIntegrationValuesParsed()
{
   _Registry->SetValue(PreIntegrationValuesParsedValueName, 1);
}

bool
CustomizationPersistentInfoReg::PostIntegrationValuesParsed()
{
   DWORD val = 0;
   _Registry->GetValue(PostIntegrationValuesParsedValueName, 0, val);
   return val == 1;
}

void
CustomizationPersistentInfoReg::MarkPostIntegrationValuesParsed()
{
   _Registry->SetValue(PostIntegrationValuesParsedValueName, 1);
}

//
// Profile Redirection
//
bool
CustomizationPersistentInfoReg::AreProfilesRedirected()
{
   DWORD val = 0;
   _Registry->GetValue(ProfilesRedirectedValueName, 0, val);
   return val == 1;
}

void
CustomizationPersistentInfoReg::MarkProfilesRedirected()
{
   _Registry->SetValue(ProfilesRedirectedValueName, 1);
}

//
// Persistent Disks Ready
//
bool
CustomizationPersistentInfoReg::ArePersistentDisksReady()
{
   DWORD val = 0;
   _Registry->GetValue(PersistentDisksReadyValueName, 0, val);
   return val == 1;
}

void
CustomizationPersistentInfoReg::MarkPersistentDisksReady()
{
   _Registry->SetValue(PersistentDisksReadyValueName, 1);
}

//
// NL Flags
//
bool
CustomizationPersistentInfoReg::IsNLFlagEnabled()
{
   DWORD val = 0;
   _Registry->GetValue(NLFlagsDisabledValueName, 0, val);
   return val == 0;
}

//
// Service Notify Values
//

bool
CustomizationPersistentInfoReg::IsServiceNotifyEnabled()
{
   DWORD dwDefault = TRUE;
   DWORD val = 0;
   _Registry->GetValue(ServiceNotifyEnabledValueName, dwDefault, val);
   return val == 1;
}

DWORD
CustomizationPersistentInfoReg::ServiceNotifyGetMaxRestarts(DWORD dwDefault)
{
   DWORD val = dwDefault;
   _Registry->GetValue(ServiceNotifyMaxRestartsValueName, dwDefault, val);
   return val;
}

DWORD
CustomizationPersistentInfoReg::ServiceNotifyGetStartDelay(DWORD dwDefault)
{
   DWORD val = dwDefault;
   _Registry->GetValue(ServiceNotifyStartDelayValueName, dwDefault, val);
   return val;
}

DWORD
CustomizationPersistentInfoReg::ServiceGetMaxRetries(DWORD dwDefault)
{
   DWORD val = dwDefault;
   _Registry->GetValue(ServiceMaxRetriesValueName, dwDefault, val);
   return val;
}

DWORD
CustomizationPersistentInfoReg::ServiceGetRetryDelay(DWORD dwDefault)
{
   DWORD val = dwDefault;
   _Registry->GetValue(ServiceRetryDelayValueName, dwDefault, val);
   return val;
}

DWORD
CustomizationPersistentInfoReg::GetServiceShutdownDelay(DWORD dwDefault)
{
   DWORD val = dwDefault;
   _Registry->GetValue(ServiceShutdownDelayValueName, dwDefault, val);
   return val;
}

std::wstring
CustomizationPersistentInfoReg::GetGuestInfoValue(const std::wstring &entry)
{
   std::wstring val;
   _Registry->GetValue(entry, val, RegType::GuestInfo);
   return val;
}

void
CustomizationPersistentInfoReg::SetGuestInfoValue(const std::wstring &entry,
                                                  const std::wstring &value)
{
   _Registry->SetValue(entry, value, RegType::GuestInfo);
}