/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */
#pragma once

struct AsyncSocket {
   int id;
};

struct rqContext {
   uint32 x[25];
   int p, q;
};

class MockWebSocketServerImpl : public WebSocketServerImpl {
public:
   using WebSocketServerImpl::WebSocketServerImpl;

   // Make protected static methods public
   using WebSocketServerImpl::OnAcceptCb;
   using WebSocketServerImpl::OnAcceptSslCb;
   using WebSocketServerImpl::OnPollCommandCb;
   using WebSocketServerImpl::PollThreadProc;

   // Make protected instance methods public
   using WebSocketServerImpl::AppendPendingSslSocket;
   using WebSocketServerImpl::AsyncSocket2Conn;
   using WebSocketServerImpl::CloseSocket;
   using WebSocketServerImpl::GetWebSocketInstance;
   using WebSocketServerImpl::OnAccept;
   using WebSocketServerImpl::OnPollCommand;
   using WebSocketServerImpl::QueueCommandItem;
   using WebSocketServerImpl::RemovePendingSslSocket;

   // Make protected member variables public
   using WebSocketServerImpl::mAcceptCallback;
   using WebSocketServerImpl::mAsyncSocketListen;
   using WebSocketServerImpl::mCallbackContext;
   using WebSocketServerImpl::mCommandItems;
   using WebSocketServerImpl::mConnections;
   using WebSocketServerImpl::mExitPoll;
   using WebSocketServerImpl::mImplicitPoll;
   using WebSocketServerImpl::mPendingSslSockets;
   using WebSocketServerImpl::mPollThread;
   using WebSocketServerImpl::mPort;
   using WebSocketServerImpl::mServerName;
   using WebSocketServerImpl::mSslCtx;
   using WebSocketServerImpl::mThisSessionId;

   static std::shared_ptr<MockWebSocketServerImpl> GetInstance(std::string serverName,
                                                               uint16_t httpPort, DWORD processId,
                                                               DWORD sessionId)
   {
      VMOCK(GetCurrentProcessId).ExpectCall().WillOnce(Return(processId));
      VMOCK(ProcessIdToSessionId)
         .ExpectCall(_, _)
         .WillOnce(Invoke([sessionId](DWORD, LPDWORD pSessionId) {
            *pSessionId = sessionId;
            return TRUE;
         }));

      return std::make_shared<MockWebSocketServerImpl>(serverName, httpPort, True, nullptr);
   }
};

class MockWebSocketConnImpl : public WebSocketConnImpl {
public:
   using WebSocketConnImpl::WebSocketConnImpl;

   // Mock method for virtual Bool Start()
   MOCK_METHOD(Bool, Start, (), (override));
   MOCK_METHOD(Bool, OnAsyncSocketDataReceived, (void *, int, AsyncSocket *), (override));
   MOCK_METHOD(void, OnHttpClientSendHandshake, (void *, int, AsyncSocket *), (override));
   MOCK_METHOD(Bool, ReadNext, (), (override));
   MOCK_METHOD(Bool, SendPong, (), (override));
   MOCK_METHOD(Bool, SendPing, (), (override));
   MOCK_METHOD(Bool, SendClose, (), (override));

   // Expose protected static methods
   using WebSocketConnImpl::OnConnectCb;
   using WebSocketConnImpl::OnErrorCb;
   using WebSocketConnImpl::OnHandshakeCb;
   using WebSocketConnImpl::OnHttpClientReceiveHandshakeCb;
   using WebSocketConnImpl::OnHttpClientSendHandshakeCb;
   using WebSocketConnImpl::OnHttpHandshakeSendCb;
   using WebSocketConnImpl::OnRecvCb;
   using WebSocketConnImpl::OnSendCb;

   // Expose protected member functions
   using WebSocketConnImpl::Abort;
   using WebSocketConnImpl::AsyncSend;
   using WebSocketConnImpl::ClearFrames;
   using WebSocketConnImpl::FireCallback;
   using WebSocketConnImpl::HandleContFrame;
   using WebSocketConnImpl::HandleFrame;
   using WebSocketConnImpl::OnAsyncSocketDataReceived;
   using WebSocketConnImpl::OnAsyncSocketDataSent;
   using WebSocketConnImpl::OnAsyncSocketError;
   using WebSocketConnImpl::OnHandshake;
   using WebSocketConnImpl::OnHttpClientReceiveHandshake;
   using WebSocketConnImpl::OnHttpClientSendHandshake;
   using WebSocketConnImpl::OnHttpHandshakeSend;
   using WebSocketConnImpl::ParseFrame;
   using WebSocketConnImpl::ParseHttpWebSocketRequestHeader;
   using WebSocketConnImpl::ParseHttpWebSocketResponseHeader;
   using WebSocketConnImpl::ReadNext;
   using WebSocketConnImpl::ReadToBuffer;
   using WebSocketConnImpl::ReadToPartialHeader;
   using WebSocketConnImpl::SendWebSocket;
   using WebSocketConnImpl::Start;
   using WebSocketConnImpl::StartClientHttpHandshake;
   using WebSocketConnImpl::Stop;

   // Expose protected member variables
   using WebSocketConnImpl::mAcceptString;
   using WebSocketConnImpl::mAsyncSocket;
   using WebSocketConnImpl::mBufferEncoded;
   using WebSocketConnImpl::mBufferEncodedIndex;
   using WebSocketConnImpl::mBufferEncodedLength;
   using WebSocketConnImpl::mBufferHandshake;
   using WebSocketConnImpl::mCallback;
   using WebSocketConnImpl::mContext;
   using WebSocketConnImpl::mContFrames;
   using WebSocketConnImpl::mFrame;
   using WebSocketConnImpl::mId;
   using WebSocketConnImpl::mPendingClose;
   using WebSocketConnImpl::mPort;
   using WebSocketConnImpl::mRandomContext;
   using WebSocketConnImpl::mServerName;
   using WebSocketConnImpl::mSocketServer;
   using WebSocketConnImpl::mThisSessionId;

   // Expose the Callback struct
   using Callback = WebSocketConnImpl::Callback;

   void CallOnHttpClientSendHandshake(void *buf, int len, AsyncSocket *asyncSocket)
   {
      WebSocketConnImpl::OnHttpClientSendHandshake(buf, len, asyncSocket);
   }

   Bool CallStart() { return WebSocketConnImpl::Start(); }

   Bool CallReadNext() { return WebSocketConnImpl::ReadNext(); }

   Bool CallSendPong() { return WebSocketConnImpl::SendPong(); }

   Bool CallSendPing() { return WebSocketConnImpl::SendPing(); }

   Bool CallSendClose() { return WebSocketConnImpl::SendClose(); }

   Bool CallOnAsyncSocketDataReceived(void *buf, int len, AsyncSocket *asyncSocket)
   {
      return WebSocketConnImpl::OnAsyncSocketDataReceived(buf, len, asyncSocket);
   }
};

class MockWebSocketNamedPipeConnImpl : public WebSocketNamedPipeConnImpl {
public:
   // Inherit constructors
   using WebSocketNamedPipeConnImpl::WebSocketNamedPipeConnImpl;

   // Make protected methods public
   using WebSocketNamedPipeConnImpl::OnAsyncSocketDataReceived;
   using WebSocketNamedPipeConnImpl::OnHttpClientSendHandshake;
   using WebSocketNamedPipeConnImpl::ReadNext;
   using WebSocketNamedPipeConnImpl::Start;

   // Make protected methods from base class public
   using WebSocketConnImpl::FireCallback;
   using WebSocketConnImpl::HandleContFrame;
   using WebSocketConnImpl::HandleFrame;
   using WebSocketConnImpl::ParseFrame;
   using WebSocketConnImpl::ReadToBuffer;
   using WebSocketConnImpl::ReadToPartialHeader;
};

class MockWebSocketNamedPipeServerImpl : public WebSocketNamedPipeServerImpl {
public:
   using WebSocketNamedPipeServerImpl::mImplicitPoll;
   using WebSocketNamedPipeServerImpl::mPollThread;
   using WebSocketNamedPipeServerImpl::mServerName;
   using WebSocketNamedPipeServerImpl::WebSocketNamedPipeServerImpl;
};
