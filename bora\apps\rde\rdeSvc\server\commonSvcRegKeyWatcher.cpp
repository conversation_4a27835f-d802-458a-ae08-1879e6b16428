/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * commonSvcRegKeyWatcher.cpp --
 *
 *    Implement the class member functions and get registy
 *    key watcher instance.
 */

#include <windows.h>
#include "commonSvcRegKeyWatcher.h"


#define WATERMARK_USER_SETTINGS "HKCU\\" HORIZON_WATERMARK_REG_GPO_ROOT_A
#define APP_PROTECTION_USER_SETTINGS "HKCU\\" HORIZON_VDM_AGENT_REG_GPO_ROOT_A "\\Configuration"
#define APP_PROTECTION_MACHINE_SETTINGS "HKLM\\" HORIZON_VDM_AGENT_REG_GPO_ROOT_A "\\Configuration"

#define LEGACY_WATERMARK_USER_SETTINGS "HKCU\\" LEGACY_HORIZON_WATERMARK_REG_GPO_ROOT_A
#define LEGACY_APP_PROTECTION_USER_SETTINGS                                                        \
   "HKCU\\" LEGACY_HORIZON_VDM_AGENT_REG_GPO_ROOT_A "\\Configuration"
#define LEGACY_APP_PROTECTION_MACHINE_SETTINGS                                                     \
   "HKLM\\" LEGACY_HORIZON_VDM_AGENT_REG_GPO_ROOT_A "\\Configuration"

static HWND g_MainThreadWaterMarkHwnd = NULL;
static HWND g_MainThreadAppProtectionHwnd = NULL;
static std::set<std::string> g_AppProtectionRegValueNames = {
   "Screen-capture Blocking",
   "Allow Screen-recording",
   "Allow Connections from Horizon Client for Windows without Antikeylogger service if the device "
   "is ARM-based",
   "Key Logger Blocking",
   "BlockSendInput",
   "Thumbnail Representation Blocking",
};


/*
 *----------------------------------------------------------------------------
 *
 * ServerCommonSvcRegKeyWatcherHandler::ServerCommonSvcRegKeyWatcherHandler --
 *
 *    Constructor.
 *
 * Results:
 *    None.
 *
 * Side effects:
 *    None.
 *
 *----------------------------------------------------------------------------
 */

ServerCommonSvcRegKeyWatcherHandler::ServerCommonSvcRegKeyWatcherHandler() {}


/*
 *----------------------------------------------------------------------------
 *
 * ServerCommonSvcRegKeyWatcherHandler::~ServerCommonSvcRegKeyWatcherHandler --
 *
 *    Destructor.
 *
 * Results:
 *    None.
 *
 * Side effects:
 *    None.
 *
 *----------------------------------------------------------------------------
 */

ServerCommonSvcRegKeyWatcherHandler::~ServerCommonSvcRegKeyWatcherHandler()
{
   if (mPollThread) {
      /*
       * If there aren't anymore poll items then I can shut down the poll
       * thread. If we call StopScheduler before RemoveAllItems, which may
       * create one dump in the stress env.
       */
      mPollThread->RemoveAllItems(true);
      mPollThread->StopScheduler();
      mPollThread = nullptr;
   }
}


/*
 *----------------------------------------------------------------------------
 *
 * ServerCommonSvcRegKeyWatcherHandler::StartWatch --
 *
 *    Create the watcher task and start watching the registry key
 *    changes for the session.
 *
 * Results:
 *    None.
 *
 * Side effects:
 *    None.
 *
 *----------------------------------------------------------------------------
 */

void
ServerCommonSvcRegKeyWatcherHandler::StartWatch(HWND hwnd,                // IN
                                                Watcher_Type watcherType) // IN
{
   if ((watcherType == WATERMARK_GPO && mKeyWatchers.contains(watcherType)) ||
       ((watcherType == APP_PROTECTION_USER_GPO || watcherType == APP_PROTECTION_MACHINE_GPO) &&
        mKeyWatchers.contains(watcherType))) {
      Log("%s: watcher already exists for type %d\n", __FUNCTION__, watcherType);
      return;
   }
   std::string keyPath = KeyPathFromType(watcherType);
   if (keyPath.length() == 0) {
      Log("%s: invalid type %d\n", __FUNCTION__, watcherType);
      return;
   }

   DWORD sessionID = ProcessUtils::GetSessionID(true, WTS_CURRENT_SESSION);
   if (watcherType == WATERMARK_GPO) {
      g_MainThreadWaterMarkHwnd = hwnd;
      auto watcher = new CommonSvcRegKeyWatcher(keyPath, CommonSvcRegKeyWatcherCb);
      mKeyWatchers[watcherType] = watcher;
      watcher->SetSessionID(sessionID);
      watcher->Start(GetPollThread());
      Log("%s: %s\n", __FUNCTION__, keyPath.c_str());

      std::string legacyKeyPath = LegacyKeyPathFromType(watcherType);
      if (legacyKeyPath.length() != 0) {
         auto legacyWatcher = new CommonSvcRegKeyWatcher(legacyKeyPath, CommonSvcRegKeyWatcherCb);
         mLegacyKeyWatchers[watcherType] = legacyWatcher;
         legacyWatcher->SetSessionID(sessionID);
         legacyWatcher->Start(GetPollThread());
         Log("%s: %s\n", __FUNCTION__, legacyKeyPath.c_str());
      } else {
         Log("%s: invalid type %d for legacy.\n", __FUNCTION__, watcherType);
      }

      /*
       * If the session registry key is changed after the first read
       * but before starting watching. The change won't be monitored by the
       * watcher, so the registry status will use the UEM or GPO value. For
       * covering the time series situation, we can call the callback again
       * after the watching task is ready.
       */
      CommonSvcRegKeyWatcherCb(watcher);
   } else if (watcherType == APP_PROTECTION_USER_GPO || watcherType == APP_PROTECTION_MACHINE_GPO) {
      g_MainThreadAppProtectionHwnd = hwnd;
      auto keyWatcher = new VMRegKeyWatcher(keyPath);
      mKeyWatchers[watcherType] = keyWatcher;
      keyWatcher->SetSessionID(sessionID);
      keyWatcher->Start(GetPollThread());
      Log("%s: %s\n", __FUNCTION__, keyPath.c_str());
      CommonSvcRegValueWatcher *lastValueWatcher = NULL;
      std::vector<RCPtr<VMRegValueWatcher>> valueWatchers;
      for (auto valueName : g_AppProtectionRegValueNames) {
         Log("%s: %s\n", __FUNCTION__, valueName.c_str());
         auto valueWatcher = new CommonSvcRegValueWatcher(valueName, CommonSvcRegValueWatcherCb);
         valueWatchers.push_back(valueWatcher);
         valueWatcher->SetSessionID(sessionID);
         valueWatcher->Start(keyWatcher);
         lastValueWatcher = valueWatcher;
      }
      mValueWatchers[watcherType] = valueWatchers;

      std::string legacyKeyPath = LegacyKeyPathFromType(watcherType);
      if (legacyKeyPath.length() != 0) {
         auto legacyKeyWatcher = new VMRegKeyWatcher(legacyKeyPath);
         mLegacyKeyWatchers[watcherType] = legacyKeyWatcher;
         legacyKeyWatcher->SetSessionID(sessionID);
         legacyKeyWatcher->Start(GetPollThread());
         Log("%s: %s\n", __FUNCTION__, legacyKeyPath.c_str());
         std::vector<RCPtr<VMRegValueWatcher>> valueWatchers;
         for (auto valueName : g_AppProtectionRegValueNames) {
            Log("%s: %s\n", __FUNCTION__, valueName.c_str());
            auto valueWatcher = new CommonSvcRegValueWatcher(valueName, CommonSvcRegValueWatcherCb);
            valueWatchers.push_back(valueWatcher);
            valueWatcher->SetSessionID(sessionID);
            valueWatcher->Start(legacyKeyWatcher);
         }
         mLegacyValueWatchers[watcherType] = valueWatchers;
      } else {
         Log("%s: invalid type %d for legacy.\n", __FUNCTION__, watcherType);
      }

      /*
       * If the session registry key is changed after the first read
       * but before starting watching. The change won't be monitored by the
       * watcher, so the registry status will use the UEM or GPO value. For
       * covering the time series situation, we can call the callback again
       * after the watching task is ready.
       */
      if (lastValueWatcher != NULL) {
         CommonSvcRegValueWatcherCb(lastValueWatcher);
      }
   }
}


/*
 *----------------------------------------------------------------------------
 *
 * ServerCommonSvcRegKeyWatcherHandler::StopWatch --
 *
 *    Stop watching the registry key change.
 *
 * Results:
 *    None.
 *
 * Side effects:
 *    None.
 *
 *----------------------------------------------------------------------------
 */

void
ServerCommonSvcRegKeyWatcherHandler::StopWatch(Watcher_Type watcherType)
{
   if (watcherType == WATERMARK_GPO && mKeyWatchers.contains(watcherType)) {
      mKeyWatchers[watcherType]->Stop();
      Log("%s: type %d\n", __FUNCTION__, watcherType);
      mKeyWatchers.erase(watcherType);
   } else if ((watcherType == APP_PROTECTION_USER_GPO ||
               watcherType == APP_PROTECTION_MACHINE_GPO) &&
              mValueWatchers.contains(watcherType)) {
      for (auto valueWatcher : mValueWatchers[watcherType]) {
         valueWatcher->Stop();
      }
      mValueWatchers[watcherType].clear();
      mKeyWatchers[watcherType]->Stop();
      Log("%s: type %d\n", __FUNCTION__, watcherType);
      mKeyWatchers.erase(watcherType);
   }

   if (watcherType == WATERMARK_GPO && mLegacyKeyWatchers.contains(watcherType)) {
      mLegacyKeyWatchers[watcherType]->Stop();
      Log("%s: type %d\n", __FUNCTION__, watcherType);
      mLegacyKeyWatchers.erase(watcherType);
   } else if ((watcherType == APP_PROTECTION_USER_GPO ||
               watcherType == APP_PROTECTION_MACHINE_GPO) &&
              mLegacyValueWatchers.contains(watcherType)) {
      for (auto valueWatcher : mLegacyValueWatchers[watcherType]) {
         valueWatcher->Stop();
      }
      mLegacyValueWatchers[watcherType].clear();
      mLegacyKeyWatchers[watcherType]->Stop();
      Log("%s: type %d\n", __FUNCTION__, watcherType);
      mLegacyKeyWatchers.erase(watcherType);
   }
}


/*
 *----------------------------------------------------------------------------
 *
 * ServerCommonSvcRegKeyWatcherHandler::GetInstance --
 *
 *    Get ServerCommonSvcRegKeyWatcherHandler instance.
 *
 * Results:
 *    ServerCommonSvcRegKeyWatcherHandler object.
 *
 * Side effects:
 *    None.
 *
 *----------------------------------------------------------------------------
 */

ServerCommonSvcRegKeyWatcherHandler *
ServerCommonSvcRegKeyWatcherHandler::GetInstance()
{
   static ServerCommonSvcRegKeyWatcherHandler gServerCommonSvcRegKeyWatcherHandler;
   return &gServerCommonSvcRegKeyWatcherHandler;
}


/*
 *----------------------------------------------------------------------------
 *
 * ServerCommonSvcRegKeyWatcherHandler::CommonSvcRegKeyWatcherCb --
 *
 *    When the registry key is changed during the session, the
 *    function is called. Post a message to main thread window.
 *
 * Results:
 *    Return true to keep watching for changes.
 *
 * Side effects:
 *    None.
 *
 *----------------------------------------------------------------------------
 */

bool
ServerCommonSvcRegKeyWatcherHandler::CommonSvcRegKeyWatcherCb(VMRegKeyWatcher *watcher) // IN
{
   if (watcher->KeyPath() == WATERMARK_USER_SETTINGS ||
       watcher->KeyPath() == LEGACY_WATERMARK_USER_SETTINGS) {
      Log("%s: watermark registry key is changed\n", __FUNCTION__);
      ::PostMessage(g_MainThreadWaterMarkHwnd, WATERMARK_GPO_CHANGED, 0, 0);
   } else {
      Log("%s: un-implemented path %s\n", __FUNCTION__, watcher->KeyPath().c_str());
   }
   return true;
}


/*
 *----------------------------------------------------------------------------
 *
 * ServerCommonSvcRegKeyWatcherHandler::CommonSvcRegValueWatcherCb --
 *
 *    When the registry value is changed during the session, the
 *    function is called. Post a message to main thread window.
 *
 * Results:
 *    None.
 *
 * Side effects:
 *    None.
 *
 *----------------------------------------------------------------------------
 */

void
ServerCommonSvcRegKeyWatcherHandler::CommonSvcRegValueWatcherCb(VMRegValueWatcher *watcher) // IN
{
   if (g_AppProtectionRegValueNames.contains(watcher->ValueName())) {
      Log("%s: app protection %s registry value %s is changed\n", __FUNCTION__,
          watcher->ValuePath() == APP_PROTECTION_USER_SETTINGS ? "user" : "machine",
          watcher->ValuePath().c_str());
      ::PostMessage(g_MainThreadAppProtectionHwnd, APP_PROTECTION_GPO_CHANGED, 0, 0);
   } else {
      Log("%s: un-implemented path %s name %s\n", __FUNCTION__, watcher->ValuePath().c_str(),
          watcher->ValueName().c_str());
   }
}


/*
 *----------------------------------------------------------------------------
 *
 * ServerCommonSvcRegKeyWatcherHandler::GetPollThread --
 *
 *    Get the VMPollThread object used by the registry monitor task.
 *    using this object to run tasks on a separate thread.
 *
 * Results:
 *    Returns the VMPollThread object used by the CommonSvcKeyWatcher.
 *
 * Side Effects:
 *    The VMPollThread scheduler is started if it's not already running.
 *
 *----------------------------------------------------------------------------
 */

RCPtr<VMPollThread>
ServerCommonSvcRegKeyWatcherHandler::GetPollThread()
{
   if (!mPollThread) {
      mPollThread = new VMPollThread("CommonSvcRegKeyMonPollThread");
      if (!mPollThread->StartScheduler()) {
         mPollThread = nullptr;
      }
      Log("%s: %s to start Poll Thread.\n", __FUNCTION__,
          mPollThread != nullptr ? "Succeeded" : "Failed");
   }
   return mPollThread;
}


/*
 *----------------------------------------------------------------------------
 *
 * ServerCommonSvcRegKeyWatcherHandler::KeyPathFromType --
 *
 *    Get the keyPath for the given type.
 *
 * Results:
 *    KeyPath string if type is valid, empty string otherwise.
 *
 * Side Effects:
 *    None.
 *
 *----------------------------------------------------------------------------
 */

std::string
ServerCommonSvcRegKeyWatcherHandler::KeyPathFromType(Watcher_Type watcherType) // IN
{
   switch (watcherType) {
   case WATERMARK_GPO: {
      return WATERMARK_USER_SETTINGS;
   }
   case APP_PROTECTION_USER_GPO: {
      return APP_PROTECTION_USER_SETTINGS;
   }
   case APP_PROTECTION_MACHINE_GPO: {
      return APP_PROTECTION_MACHINE_SETTINGS;
   }
   default: {
      return "";
   }
   }
}


/*
 *----------------------------------------------------------------------------
 *
 * ServerCommonSvcRegKeyWatcherHandler::LegacyKeyPathFromType --
 *
 *    Get the legacy keyPath for the given type.
 *
 * Results:
 *    KeyPath string if type is valid, empty string otherwise.
 *
 * Side Effects:
 *    None.
 *
 *----------------------------------------------------------------------------
 */

std::string
ServerCommonSvcRegKeyWatcherHandler::LegacyKeyPathFromType(Watcher_Type watcherType) // IN
{
   switch (watcherType) {
   case WATERMARK_GPO: {
      return LEGACY_WATERMARK_USER_SETTINGS;
   }
   case APP_PROTECTION_USER_GPO: {
      return LEGACY_APP_PROTECTION_USER_SETTINGS;
   }
   case APP_PROTECTION_MACHINE_GPO: {
      return LEGACY_APP_PROTECTION_MACHINE_SETTINGS;
   }
   default: {
      return "";
   }
   }
}


/*
 *----------------------------------------------------------------------------
 *
 * CommonSvc_GetRegKeyWatcherInstance --
 *
 *    Get the registy key watcher instance for the windows server.
 *
 * Results:
 *    None.
 *
 * Side effects:
 *    None.
 *
 *----------------------------------------------------------------------------
 */

void *
CommonSvc_GetRegKeyWatcherInstance(void)
{
   return ServerCommonSvcRegKeyWatcherHandler::GetInstance();
}
