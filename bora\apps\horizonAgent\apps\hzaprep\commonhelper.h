/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * CommonHelper.h
 *
 *      Class that implements some common functionality.
 *
 */

#pragma once

#include <MessageFrameWork.h>
#include <mutex>

/*
 * -----------------
 * Global variables
 * -----------------
 */

extern bool g_bShutdown;
extern HANDLE g_hShutdown;

enum hzaprepMode { none_mode = 0, sysprep_mode, service_mode, register_mode };

enum hzaprepError {
   HZAPREP_SUCCESS = 0,
   HZAPREP_MFW_FAILED,
   HZAPREP_MFW_SERVICE_FAILED,
   HZAPREP_FAILED,
   HZAPREP_ARGUMENT_MISSIMG,
   HZAPREP_INVALID_ARGUMENT,
   HZAPREP_REGISTRATION_FAILED,
   HZAPREP_NOT_GOLDEN_IMAGE,
};

class CommonHelper {
public:
   bool initMFW(bool isServer);
   void shutdownMFW();
   static CommonHelper &getInstance();
   bool isMFWInitialized();

   static hzaprepMode mode;

private:
   CommonHelper();
   CommonHelper(const CommonHelper &) = delete;
   CommonHelper &operator=(const CommonHelper &) = delete;
   ~CommonHelper();

   bool mMFWInitialized;
   std::mutex mMFWInitMutex;
};
