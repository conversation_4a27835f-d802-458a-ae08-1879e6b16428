/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */
#pragma once

#include <functional>
#include <iomanip>
#include <map>
#include <sstream>

/*
 * HttpRequest.h --
 *
 *   Pure virtual class for implementing HTTP requests
 */
class HttpRequest {
public:
   enum CONTENT_TYPE {
      NONE = 0,
      CSS,
      CSV,
      HTML,
      JSON,
      PLAIN,
      XML,
      OCTET_STREAM,
      ZIP,
      CONTENT_TYPE_SIZE
   };

   /*
    * getSync()
    *
    *   Perform a synchronous GET request
    */
   virtual bool getSync(const std::string &url, std::string &response) = 0;

   /*
    * postSync()
    *
    *   Perform a synchronous POST request
    */
   virtual bool postSync(HttpRequest::CONTENT_TYPE contentType, const std::string &url,
                         const std::string &body, std::string &response) = 0;

   /*
    * putSync()
    *
    *   Perform a synchronous PUT request
    */
   virtual bool putSync(HttpRequest::CONTENT_TYPE contentType, const std::string &url,
                        const std::string &body, std::string &response) = 0;

   /*
    * putSyncFile()
    *
    *   Perform a synchronous PUT request, where the contents of the request
    *   are obtained from a file
    */
   virtual bool putSyncFile(HttpRequest::CONTENT_TYPE contentType, const std::string &url,
                            const std::string &filename, std::string &response) = 0;

   /*
    * deleteSync()
    *
    *   Perform a synchronous DELETE request
    */
   virtual bool deleteSync(const std::string &url, std::string &response) = 0;

   /*
    * getAsync()
    *
    *   Perform an asynchronous GET request
    */
   virtual bool getAsync(const std::string &url,
                         std::function<void(HttpRequest *, std::string)> cb) = 0;

   /*
    * postAsync()
    *
    *   Perform an asynchronous POST request
    */
   virtual bool postAsync(HttpRequest::CONTENT_TYPE contentType, const std::string &url,
                          const std::string &body,
                          std::function<void(HttpRequest *, std::string)> cb) = 0;

   /*
    * putAsync()
    *
    *   Perform an asynchronous PUT request
    */
   virtual bool putAsync(HttpRequest::CONTENT_TYPE contentType, const std::string &url,
                         const std::string &body,
                         std::function<void(HttpRequest *, std::string)> cb) = 0;

   virtual bool putAsync(HttpRequest::CONTENT_TYPE contentType, const std::string &url,
                         const std::vector<char> &body, size_t bodySize,
                         std::function<void(HttpRequest *, std::string)> cb) = 0;

   /*
    * putAsyncFile()
    *
    *   Perform an asynchronous PUT request, where the contents of the request
    *   are obtained from a file
    */
   virtual bool putAsyncFile(HttpRequest::CONTENT_TYPE contentType, const std::string &url,
                             const std::string &filename,
                             std::function<void(HttpRequest *, std::string)> cb) = 0;

   /*
    * ignoreAllCertErrors()
    *
    *   Indicate that SSL certificate errors should be ignored
    */
   virtual void ignoreAllCertErrors() = 0;

   /*
    * ignoreWrongHostCertError()
    *
    *   Indicate that any mismatch between the certificate CN/SAN and the
    *   hostname should be ignored.
    */
   virtual void ignoreWrongHostCertError() = 0;

   /*
    * buildChainUsingStore()
    *
    *   Used in conjunction with the 'setCAThumbprints' method. Gives this
    *   class a wider range of certificate stores to use when attempting to
    *   build a certificate chain.
    */
   virtual void buildChainUsingStore(void *certStore) = 0;

   /*
    * enableCertRevocation()
    *
    *   Indicate that SSL certificate revocation checks should be made
    */
   virtual void enableCertRevocation() = 0;

   /*
    * setHeaders()
    *
    *   Set the HTTP headers to use in the request
    */
   virtual void setHeaders(const std::map<std::string, std::string> &headers) = 0;

   /*
    * setQueryParams()
    *
    *   Set the query params (e.g. vmware.com/path?<query_params>). Handles uri
    *   encoding.
    */
   virtual void setQueryParams(const std::multimap<std::string, std::string> &params) = 0;

   /*
    * setCAThumbprint()
    *
    *   Sets the thumbprints for the server. If this client encounters an SSL
    *   error (the root CA is not trusted), and if the CA's hash matches the
    *   hash provided here, then the request will continue.
    */
   virtual void setCAThumbprint(const std::string &thumbprint) = 0;

   /*
    * setCATrustStore()
    *
    *   Sets trusted Certificate Authorities for the server. If this client
    *   encounters an SSL error (the root CA is not trusted), and if the CA
    *   is in the list provided here, then the request will continue.
    *   This method accepts a list of concatenated PEM certificates.
    *
    *   Alternatively, it will accept a vector of CA trust store thumbprints.
    */

   virtual void setCATrustStore(const std::string &CAStore) = 0;
   virtual void setCATrustStore(const std::vector<std::string> &CAStoreThumbprints) = 0;

   /*
    * setTimeout()
    *
    *   Sets the total timeout value, in seconds, for the entire HTTP request
    */
   virtual void setTimeout(unsigned int timeout_s) = 0;

   /*
    * setProxyServerList()
    *
    *   Sets the proxy server list. Delimited by a semicolon.
    */
   virtual void setProxyServerList(const std::string &proxyList) = 0;

   /*
    * setProxyBypass()
    *
    *   Sets the proxy bypass list.
    */
   virtual void setProxyBypass(const std::string &proxyBypass) = 0;

   /*
    * setProxyCredentials
    *
    *   Sets the credentials for the proxy server
    */
   virtual void setProxyCredentials(const std::wstring &username, const std::wstring &password) = 0;

   /*
    * setProxyPACURL
    *
    *   Sets the proxy PAC file URL
    */
   virtual void setProxyPACURL(const std::string &proxyPACURL) = 0;

   /*
    * cancelRequest()
    *
    *   Cancels the request. Blocks until the request has been successfully
    *   cancelled; it is safe to delete this object only when the call returns.
    */
   virtual bool cancelRequest() = 0;

   /*
    * getResponseCode()
    *
    *   Returns the HTTP status code received
    */
   virtual unsigned int getResponseCode() = 0;

   /*
    * getResponseHeaders()
    *
    *   Returns the headers received in the response
    */
   virtual std::string getResponseHeaders() = 0;

   /*
    * uriEncode()
    *
    *   URI-encode the given string. Note that corestring does not contain
    *     a way to uri-encode a string, but it does contain uri-decode.
    */
   static std::string uriEncode(const std::string &str, bool ignoreForwardSlash = false)
   {
      std::ostringstream encoded;
      encoded.fill('0');
      encoded << std::hex;

      for (std::string::const_iterator i = str.begin(), n = str.end(); i != n; ++i) {
         std::string::value_type c = *i;

         if (std::isalnum(c) || c == '-' || c == '_' || c == '.' ||
             (c == '/' && ignoreForwardSlash)) {
            encoded << c;
            continue;
         }

         encoded << std::uppercase;
         encoded << '%' << std::setw(2) << static_cast<int>(static_cast<unsigned char>(c));
         encoded << std::nouppercase;
      }

      return encoded.str();
   }
};


class IHttpRequestFactory {
public:
   virtual std::shared_ptr<HttpRequest> GetHttpRequestClient() = 0;
};
