﻿<?xml version="1.0" encoding="utf-8"?>
<policyDefinitionResources xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" revision="1.0" schemaVersion="1.0" xmlns="http://www.microsoft.com/GroupPolicy/PolicyDefinitions">

   <displayName>Omnissa Horizon Agent</displayName>
   <description>Horizon Agent configuration settings</description>
   <resources>
      <stringTable>
         <string id="SUPPORTED_Windows10_1607_VM">At least Windows 10 / Windows Server 2016 VDI Version 1607 Virtual Machine</string>

         <string id="Agent_Configuration">Agent Configuration</string>

         <string id="Collaboration">Collaboration</string>

         <string id="Agent_Security">Agent Security</string>

         <string id="Unity_Touch_Hosted_Apps">Unity Touch and Hosted Apps</string>

         <string id="Unity_Filter">Unity Filter rule list</string>

         <string id="Unity_Filter_Rules_Desc">This policy specifies the filter rules for window when remoting Hosted Apps. Filter rules are used by the Horizon Agent to support custom applications. This GPO should be used if you have a window display problem, such as a window having a black background or a drop-down window not being sized correctly.

The first step in setting a rule is to determine the characteristics of the window(s) that you want the rule to apply to. There are many possible characteristics that can be identified:

1. Window classname, identified in a custom rule as classname=XYZ
2. Product company, identified as company=XYZ
3. Product name, identified as product=XYZ
4. Product major version, identified as major=XYZ
5. Product minor version, identified as minor=XYZ
6. Product build number, identified as build=XYZ
7. Product revision number, identified as revision=XYZ

It is most common to only use "Window classname" as the preferred characteristic (e.g. classname=CustomClassName). However, the other characteristics are provided in case you need to limit rules to one specific product; you can find these characteristics in the File Properties window of an executable and they must be an exact case-sensitive match, including any special characters. When multiple characteristics are provided, all must match in order for the rule to apply to the window.

Once you have the characteristics identified, the next step is to choose an action. The action must be either action=block or action=map. action=block tells the Horizon Agent to not remote the window to the client. This is used when a window shows up on the client that is too large or interferes with normal window focus behavior. action=map tells the Horizon Agent to treat the window as a certain hardcoded type.

If you set action=map, you also need to specify the type to map the window to. This is done by including type=XYZ. Here is a list of all available type values: normal, panel, dialog, tooltip, splash, toolbar, dock, desktop, widget, combobox, startscreen, sidepanel, taskbar, metrofullscreen, metrodocked.

Here are two examples of rules that you could set to fix a misbehaving application:

1. You can filter out a window that should not be remoted.
   - To block all windows with class name MyClassName, use rule "classname=MyClassName;action=block"
   - To block all windows from product MyProduct, use rule "product=MyProduct;action=block".
2. You can map a window to the correct type. This usually is only necessary if Omnissa support instructs you to do this, as it is hard to determine if a window is being mapped to the wrong type.
   - To map a custom class to type combo box, use rule "classname=MyClassName;action=map;type=combobox"

Note: This GPO has lower priority than the filtering rules that are installed in %ProgramData%\Omnissa\RdeServer\Unity Filters</string>

         <string id="Smartcard_Redirection">Smartcard Redirection</string>

         <string id="Local_Reader_Access">Local Reader Access</string>

         <string id="True_SSO_Configuration">True SSO Configuration</string>

         <string id="Whfb_Certificate_Redirection">Whfb Certificate Redirection</string>

         <string id="Whfb_Certificate_Allowed_Applications">List of allowed executables</string>

         <string id="Whfb_Certificate_Allowed_Applications_Desc">List of executables that are allowed to use redirected Whfb Certificate</string>

         <string id="View_USB_Configuration">Horizon USB Configuration</string>

         <string id="Client_Downloadable_only_settings">Client Downloadable only Settings</string>

         <string id="Recursive_Domain_Enumeration">Recursive Enumeration of Trusted Domains</string>

         <string id="Recursive_Domain_Enumeration_Desc">Determines if every domain trusted by the domain in which the server resides is enumerated. In order to establish a complete chain of trust, the domains trusted by each trusted domain are also enumerated and the process continues recursively until all trusted domains are discovered. This information is passed to Horizon Connection Server in order to ensure that all trusted domains are available to the client on login.

This property is enabled by default. When disabled, only directly trusted domains are enumerated and connection to remote domain controllers does not take place.

Note: In environments with complex domain relationships — such as those that use multiple forest structures with trust between domains in their forests — this process can take a few minutes to complete.</string>

         <string id="Force_MMR_to_use_overlay">Force MMR to use software overlay</string>

         <string id="Force_MMR_to_use_overlay_Desc">MMR will try to use the hardware overlay to play back video for better performance. However, when working with multiple displays the hardware overlay will only exist on one of the displays: either the primary display or the display where WMP was started. If WMP is dragged to another display the video is shown as a black rectangle. Use this option to force MMR to use a software overlay which will work on all displays.</string>

         <string id="Enable_multi_media_acceleration">Enable multi-media acceleration</string>

         <string id="Enable_multi_media_acceleration_Desc">Specifies if multimedia redirection (MMR) is enabled on the agent. MMR is a Microsoft DirectShow filter that forwards multimedia data from specific codecs on the remote system directly through a TCP socket to the client. The data is then decoded directly on the client, where it is played. Administrators can disable MMR if the client has insufficient resources to handle local multimedia decoding.

Note: MMR will not work correctly if the Horizon Client video display hardware does not have overlay support. MMR policy does not apply to Offline Desktop sessions.</string>

         <string id="AllowDirectRDP">Allow Direct RDP</string>

         <string id="AllowDirectRDP_Desc">Determines if non-Horizon clients can connect directly to Horizon desktops using RDP. When disabled, the agent will only permit Horizon-managed connections via Horizon Client or Horizon Portal.

This property is enabled by default.</string>

         <string id="AllowSingleSignon">Allow Single Signon</string>

         <string id="AllowSingleSignon_Desc">Determines if single sign-on (SSO) is used to connect users to Horizon desktops. When enabled, users are only required to enter their credentials when connecting with Horizon Client or Horizon Portal. When disabled, users must reauthenticate when the remote connection is made.

This property requires that the Secure Authentication component of Horizon Agent is installed on the desktop, and is enabled by default.</string>

         <string id="AutoPopulateLogonUI">Auto Populate Logon UI</string>

         <string id="AutoPopulateLogonUI_Desc">Determines if the username field in the Logonui interface is automatically populated or not. This property is enabled by default and is only applicable in RDS case when either Single Sign On is disabled or is not working.</string>

         <string id="ConnectionTicketTimeout">Connection Ticket Timeout</string>

         <string id="ConnectionTicketTimeout_Desc">Specifies the time in seconds for which the Horizon connection ticket is valid. The connection ticket is used by Horizon clients when connecting to Horizon Agent and is used for verification and single sign-on purposes.

For security reasons, these tickets are only valid within the specified time period. If this property is not explicitly set, a default of 900 seconds applies.</string>

         <string id="CredentialFilterExceptions">Credential Filter Exceptions</string>

         <string id="CredentialFilterExceptions_Desc">A semi-colon separated list of executable file names that is not allowed to load the agent CredentialFilter. The file names must be without path and suffix.</string>

         <string id="RDPVcBridgeUnsupportedClients">RDPVcBridge Unsupported Clients</string>

         <string id="RDPVcBridgeUnsupportedClients_Desc">A comma separated list of horizon client types which do not support RDPVcBridge.</string>

         <string id="Disable_Time_Zone_sync">Disable Time Zone Synchronization</string>

         <string id="Disable_Time_Zone_sync_Desc">Determines if the time zone of the Horizon desktop is synchronized with that of the connected client. When enabled, this property will only apply if the 'Disable time zone forwarding' property of the Horizon Client Configuration policy is not set to disabled.

This property is disabled by default.</string>

         <string id="Keep_Time_Zone_sync_disconnect">Keep Time Zone Synchronization on Disconnect (VDI)</string>

         <string id="Keep_Time_Zone_sync_disconnect_Desc">If 'Time Zone Synchronization' is enabled and this property is enabled, the time zone of the Remote Desktop will remain synchronized with the time zone of the client that was most recently disconnected.

If this property is disabled, then the time zone of the Remote Desktop will be restored when the end user session is disconnected.

This setting does not apply to RDSH hosts when the Remote Desktop Services role is enabled.

This property is disabled by default.</string>

         <string id="Keep_Time_Zone_sync_logoff">Keep Time Zone Synchronization on Logoff (VDI)</string>

         <string id="Keep_Time_Zone_sync_logoff_Desc">If 'Time Zone Synchronization' is enabled and this property is enabled, the time zone of the Remote Desktop will remain synchronized with the time zone of the client that was most recently logged off.

If this property is disabled, then the time zone of the Remote Desktop will be restored when the end user session is logged off.

This setting does not apply to RDSH hosts when the Remote Desktop Services role is enabled.

This property is disabled by default.</string>

          <string id="Enable_ClientMediaPerm_Popup">Enable tab, screen, and application picker for screensharing with browser redirection</string>

          <string id="Enable_ClientMediaPerm_Popup_Desc">When enabled, a picker to select the browser tab, screen, or application will be shown when screensharing with browser redirection. This property is enabled by default.</string>

		  <string id="Toggle_Display_Settings_Control">Toggle Display Settings Control</string>

         <string id="Toggle_Display_Settings_Control_Desc">Determines whether to disable the Settings page of the Display Control Panel applet while a Horizon Client is connected.

This property only applies to sessions using the PCoIP protocol. This property is enabled by default.</string>

         <string id="DpiSync">DPI Synchronization</string>

         <string id="DpiSync_Desc">Adjusts the system-wide DPI setting for the remote session. When enabled or not configured, sets the system-wide DPI setting for the remote session to match the corresponding DPI setting on the Client operating system. When disabled, never changes the system-wide DPI setting for the remote session.</string>

         <string id="DpiSyncPerMonitor">DPI Synchronization Per Monitor</string>

         <string id="DpiSyncPerMonitor_Desc">Adjusts the DPI setting in multiple monitors during a remote session. When enabled or not configured, the DPI setting in all monitors changes to match the client operating system’s DPI setting during a remote session. If the DPI setting is customized, the customized DPI setting is matched. When disabled, users must disconnect and connect to a new remote session to make DPI changes take effect in all monitors.</string>

         <string id="DisplayScaling">Display Scaling</string>

         <string id="DisplayScaling_Desc">Control whether display scaling feature is allowed or not by Agent side. When enabled or not configured, the display scaling is allowed by Agent side, and final on or off state of display scaling feature depends on client side configuration. When disabled, display scaling feature is disabled regardless of client side configuration. This configuration only takes effect when DPI synchronization per monitor is disabled.</string>

         <string id="DisallowCollaboration">Turn off collaboration</string>

         <string id="DisallowCollaboration_Desc">This setting configures whether collaboration is allowed on the Horizon Agent VM. If enabled, the collaboration feature is turned off entirely. If disabled or not configured, the feature is controlled at a pool level. Horizon Agent machines must be rebooted for this setting to take effect.</string>

         <string id="AllowCollaborationInviteByIM">Allow inviting collaborators by IM</string>

         <string id="AllowCollaborationInviteByIM_Desc">This setting configures whether users are allowed to send collaboration invitations using an installed Instant Message (IM) application. If disabled, the user is not allowed to invite using IM even if an IM application is installed. This setting is enabled by default.</string>

         <string id="AllowCollaborationInviteByEmail">Allow inviting collaborators by e-mail</string>

         <string id="AllowCollaborationInviteByEmail_Desc">This setting configures whether users are allowed to send collaboration invitations using an installed e-mail application. If disabled, the user is not allowed to invite using e-mail even if an e-mail application is installed. This setting is enabled by default.</string>

         <string id="AllowCollaborationControlPassing">Allow control passing to collaborators</string>

         <string id="AllowCollaborationControlPassing_Desc">This setting configures whether users are allowed to pass input control to other collaborators during collaboration. This setting is enabled by default.</string>

         <string id="MaxCollaboratorCount">Maximum number of invited collaborators</string>

         <string id="MaxCollaboratorCount_Desc">This setting configures the maximum number of collaborators that a user can invite to join their session. The default maximum is 5.</string>

         <string id="CollaborationEmailInviteDelimiter">Separator used for multiple e-mail addresses in mailto: links</string>

         <string id="CollaborationEmailInviteDelimiter_Desc">This setting configures the separator used for multiple e-mail addresses in mailto: links. When this policy is not configured, the value defaults to ";" (semicolon without a space) to separate e-mail addresses to ensure best compatibility with mainstream e-mail clients.

If your default e-mail client has problems with this separator, you can try other combinations such as ", " (comma plus one space) or "; " (semicolon plus one space). This value will be URI-encoded before being placed in the mailto: link, so do not set this entry to a URI-encoded value.</string>

         <string id="CollaborationClipboardIncludeOutlookURL">Include Outlook-formatted URL in clipboard text</string>

         <string id="CollaborationClipboardIncludeOutlookURL_Desc">When this setting is enabled, an Outlook-formatted invitation URL is included in clipboard invitation text. Enable this setting if you expect end users to paste clipboard invitation text into an e-mail. This setting is disabled by default.</string>

         <string id="CollaborationServerURLs">Server URLs to include in invitation message</string>

         <string id="CollaborationServerURLs_Desc">This setting allows you to override the default URL included in collaboration invitations. It is recommended that you set this value if your deployment uses more than one server URL (e.g. different internal and external URLs or per-pod URLs).

When setting the values, the first column should include the URL with an optional port (e.g. "horizon-ca.corp.int" or "horizon-ca.corp.int:2323") and the second column should include a short description of the URL (e.g. "California Pod" or "Corporate Network"). The description is only used if there are multiple servers in the list.</string>

         <string id="UnAuthenticatedAccessEnabled">Enable Unauthenticated Access</string>

         <string id="UnAuthenticatedAccessEnabled_Desc">This setting enables the Unauthenticated Access feature. The system must be rebooted in order for this change to take effect. Unauthenticated Access is enabled by default.</string>

         <string id="RdsAadAuthEnabled">Enable Azure Active Directory Single Signon</string>

         <string id="RdsAadAuthEnabled_Desc">This setting enables the Azure Active Directory Single Signon feature. The system must be rebooted in order for this change to take effect. This feature is enabled by default.  This feature depends on the system being Azure Active Directory joined.</string>

         <string id="CommandsToRunOnConnect">Commands To Run On Connect</string>

         <string id="CommandsToRunOnConnect_Desc">The list of commands to be run when a session is connected for the first time.</string>

         <string id="CommandsToRunOnReconnect">Commands To Run On Reconnect</string>

         <string id="CommandsToRunOnReconnect_Desc">The list of commands to be run when a session is re-connected after a disconnect.</string>

         <string id="CommandsToRunOnDisconnect">Commands To Run On Disconnect</string>

         <string id="CommandsToRunOnDisconnect_Desc">The list of commands to be run when a session is disconnected.</string>

         <string id="ShowDiskActivityIcon">Show Disk Activity Icon</string>

         <string id="ShowDiskActivityIcon_Desc">Shows a disk activity icon in the system tray. Uses the 'System Trace NT Kernel Logger' which can only be used by a single process, disable if needed for other purposes. Default is Enabled.</string>

         <string id="SSO_retry_timeout">Single sign-on retry timeout</string>

         <string id="SSO_retry_timeout_Desc">Specifies the time in milliseconds after which single sign-on is retried. Set to 0 to disable single sign-on retry. The default value is 5000 milliseconds.</string>

         <string id="Win10PhysicalAgentAudioOption">Audio option for single session Windows 10 physical Remote Desktop machine</string>

         <string id="Win10PhysicalAgentAudioOption_Desc">Specifies which audio devices to use in the Horizon Windows 10 physical Remote Desktop machine session. The default is to use audio devices attached to Horizon Client endpoint.</string>

         <string id="WaitForLogoff">Wait for logoff timeout</string>

         <string id="WaitForLogoff_Desc">Specifies the time in seconds to wait for the user's previous session to finish logging off before attempting logon. Set to 0 to disable the wait and fail immediately. The default value is 10 seconds.</string>

         <string id="UseClientAudioDevice">Use audio devices attached to Horizon Client endpoint</string>

         <string id="UsePhysicalMachineAudioDevice">Use audio devices attached to Horizon Windows 10 physical Remote Desktop endpoint</string>

         <string id="VDI_idle_time_till_disconnect">Idle Time Until Disconnect (VDI)</string>

         <string id="VDI_idle_time_till_disconnect_Desc">Specifies the amount of time after which a VDI Desktop session will disconnect due to user inactivity.
If this setting is either disabled or unconfigured, then the VDI Desktop Sessions will never be disconnected. Selecting "Never" will have the same effect.
Note: If the desktop pool or machine is configured to logoff automatically after a disconnect, those settings will be honored.</string>

         <string id="Accept_SSL_encr_framework_channel">Accept SSL encrypted framework channel</string>

         <string id="Accept_SSL_encr_framework_channel_Desc">Accept SSL encrypted framework channel

Enable: Enable SSL, allow legacy clients to connect without SSL
Disable: Disable SSL
Enforce: Enable SSL, refuse legacy client connections</string>

         <string id="Enable">Enable</string>

         <string id="Disable">Disable</string>

         <string id="Enforce">Enforce</string>

         <string id="Allow_smartcard_local_access">Allow applications access to Local Smart Card readers</string>

         <string id="Allow_smartcard_local_access_Desc">If enabled, applications will be able to access all 'local' Smart Card readers even when the Smart Card Redirection feature is installed.

This setting does not apply to RDP or to RDSH hosts when the Remote Desktop Services role is enabled.

When enabled the desktop will be monitored for the presence of a local reader and when detected the smartcard redirection will switch off allowing access to the local readers. The redirection will remain off until the next time the user connects to the session.

NOTE: When local access is enabled, applications can no longer access remote readers present on the client.

This setting is disabled by default.</string>

         <string id="Local_Reader_Name">Local Reader Name</string>

         <string id="Local_Reader_Name_Desc">Specifies the name of a local reader to monitor in order to enable local access. By default the reader must have a card inserted to enable local access; you can disable that requirement using the 'Require an inserted Smart Card' setting.


The default value is that the feature is enabled for all readers</string>

         <string id="Require_an_inserted_smart_card">Require an inserted Smart Card</string>

         <string id="Require_an_inserted_smart_card_Desc">If enabled, local reader access will only be enabled if the local reader has a card inserted. If disabled, local access will be enabled as long as a local reader is detected.

This setting is enabled by default.</string>

         <string id="Disable_true_SSO">Disable True SSO</string>

         <string id="Disable_true_SSO_Desc">Disables the feature on the agent if this option is enabled</string>

         <string id="Cert_wait_timeout">Certificate wait timeout</string>

         <string id="Cert_wait_timeout_Desc">Specifies timeout period of certificates to arrive on the agent in seconds</string>

         <string id="Min_key_size">Minimum key size</string>

         <string id="Min_key_size_Desc">Keys of minimum size that are used</string>

         <string id="All_key_sizes">All key sizes</string>

         <string id="All_key_sizes_Desc">All sizes of keys that can be used. Maximum 5 sizes can be specified. Example: 1024,2048,3072,4096</string>

         <string id="Keys_to_precreate">Number of keys to pre-create</string>

         <string id="Keys_to_precreate_Desc">Number of keys to pre-create on RDSH environment</string>

         <string id="Cert_min_validity">Minimum validity period required for a certificate</string>

         <string id="Cert_min_validity_Desc">Minimum validity period(in minutes) required for a certificate when it is being reused for user reconnect</string>

         <string id="Enable_Unity_Touch">Enable Unity Touch</string>

         <string id="Enable_Unity_Touch_Desc">This policy specifies whether the Unity Touch functionality is enabled on the Horizon Agent. The default for this setting is that Unity Touch is enabled.

On Windows 10, if Unity Touch is enabled, sub-policy specifies whether Univeral Windows Platform(UWP) app support for Unity Touch is enabled on the Horizon Agent. The default for UWP support on Unity Touch is that UWP support is enabled for Unity Touch. If the Unity Touch policy is not configured, UWP support for Unity Touch is enabled on Windows 10.</string>

         <string id="Enable_system_tray_redir">Enable system tray redirection for Hosted Apps</string>

         <string id="Enable_system_tray_redir_Desc">This policy specifies whether system tray redirection should be enabled while remoting Hosted Apps. The default for this setting is that system tray redirection is enabled.</string>

         <string id="Enable_user_prof_customization">Enable user profile customization for Hosted Apps</string>

         <string id="Enable_user_prof_customization_Desc">This policy specifies whether to run user profile customization while remoting Hosted Apps. This will generate a user's profile, customize the Windows theme, and run registered startup applications. The default value is disabled.</string>

         <string id="AllowTinyOrOffscreenWindows">Send updates for empty or offscreen windows</string>

         <string id="AllowTinyOrOffscreenWindows_Desc">This policy specifies whether the Horizon client should receive updates about empty or offscreen windows. When this value is disabled, information about windows that are smaller than 2x2 pixels or located entirely offscreen will not be sent to the Horizon client. It is disabled by default.</string>

         <string id="MinimalHookingModeEnabled">Limit usage of Windows hooks</string>

         <string id="MinimalHookingModeEnabled_Desc">This policy turns off most hooks when remoting Hosted Apps or when using Unity Touch. It is intended to be used for applications that have compatibility or performance issues when OS-level hooks are set. For example, this setting disables use of most Windows active accessibility and in-process hooks. This policy is disabled by default, meaning that we use all of our preferred hooks by default.</string>

         <string id="LaunchAppWhenArgsAreDifferent">Only launch new instances of Hosted Apps if arguments are different</string>

         <string id="LaunchAppWhenArgsAreDifferent_Desc">This policy controls the behavior when a Hosted App is launched but an existing instance of the application is already running inside of a disconnected protocol session. When disabled, the existing instance of the application will be activated. When enabled, the existing instance of the application will only be activated if the command-line parameters match. The default value of this policy is disabled.</string>

         <string id="Exclude_Vid_Pid">Exclude Vid/Pid Device</string>

         <string id="Exclude_Vid_Pid_Desc">Exclude a device with a specified Vendor ID and Product ID from being forwarded.

Syntax: {m|o}:&lt;vid-&lt;xxxx&gt;_pid-&lt;xxxx|*&gt;&gt;[;...]
merge-flag:
m=Client setting will merge with agent setting
o=Agent setting will override client setting

For example: m:vid-0781_pid-554c;vid-0781_pid-9999</string>

         <string id="Exclude_Vid_Pid_Rel">Exclude Vid/Pid/Rel Device</string>

         <string id="Exclude_Vid_Pid_Rel_Desc">Exclude a device with a specified Vendor ID, Product ID and Release Number from being forwarded.

Syntax: {m|o}:&lt;vid-&lt;xxxx&gt;_pid-&lt;xxxx&gt;_rel-&lt;xxxx|*&gt;&gt;[;...]
merge-flag:
m=Client setting will merge with agent setting
o=Agent setting will override client setting

For example: m:vid-0781_pid-554c_rel-0100;vid-0781_pid-9999_rel-0010</string>

         <string id="Include_Vid_Pid">Include Vid/Pid Device</string>

         <string id="Include_Vid_Pid_Desc">Include a device with a specified Vendor ID and Product ID that can be forwarded.

Syntax: {m|o}:&lt;vid-&lt;xxxx&gt;_pid-&lt;xxxx|*&gt;&gt;[;...]

merge-flag:
m=Client setting will merge with agent setting
o=Agent setting will override client setting

For example: o:vid-0781_pid-554c;vid-0781_pid-9999</string>

         <string id="Include_Vid_Pid_Rel">Include Vid/Pid/Rel Device</string>

         <string id="Include_Vid_Pid_Rel_Desc">Include a device with a specified Vendor ID, Product ID, and Release Number that can be forwarded.

Syntax: {m|o}:&lt;vid-&lt;xxxx&gt;_pid-&lt;xxxx&gt;_rel-&lt;xxxx|*&gt;&gt;[;...]

merge-flag:
m=Client setting will merge with agent setting
o=Agent setting will override client setting

For example: o:vid-0781_pid-554c_rel-0100;vid-0781_pid-9999_rel-0010</string>

         <string id="Exclude_device_family">Exclude Device Family</string>

         <string id="Exclude_device_family_Desc">Exclude a family of devices from being forwarded.

Syntax: {m|o}:&lt;family-name&gt;[;...]

merge-flag:
m=Client setting will merge with agent setting
o=Agent setting will override client setting

For example: o:bluetooth;audio-in</string>

         <string id="Include_device_family">Include Device Family</string>

         <string id="Include_device_family_Desc">Include a family of devices that can be forwarded.

Syntax: {m|o}:&lt;family-name&gt;[;...]

merge-flag:
m=Client setting will merge with agent setting
o=Agent setting will override client setting

For example: m:storage;audio-out</string>

         <string id="Exclude_all">Exclude All Devices</string>

         <string id="Exclude_all_Desc">Block all devices unless they are included via Include filter rule.

Default: Allow all devices</string>

         <string id="HidOpt_Include_Vid_Pid">Include HID Optimization Vid/Pid Device</string>

         <string id="HidOpt_Include_Vid_Pid_Desc">Include a HID device with a specified Vendor ID and Product ID that can be optimized.

Syntax: &lt;vid-&lt;xxxx&gt;_pid-&lt;xxxx|*&gt;&gt;[;...]

For example: vid-056a_pid-0302;vid-046d_pid-c628</string>

         <string id="Exclude_Auto_Vid_Pid">Exclude Automatically Connection Vid/Pid Device</string>

         <string id="Exclude_Auto_Vid_Pid_Desc">Exclude a device with a specified Vendor ID and Product ID from being automatically forwarded.

Syntax: {m|o}:&lt;vid-&lt;xxxx&gt;_pid-&lt;xxxx|*&gt;&gt;[;...]
merge-flag:
m=Client setting will merge with agent setting
o=Agent setting will override client setting

For example: m:vid-0781_pid-554c;vid-0781_pid-9999</string>

         <string id="Exclude_auto_device_family">Exclude Automatically Connection Device Family</string>

         <string id="Exclude_auto_device_family_Desc">Exclude a family of devices from being automatically forwarded.

Syntax: {m|o}:&lt;family-name&gt;[;...]

merge-flag:
m=Client setting will merge with agent setting
o=Agent setting will override client setting

For example: o:storage;hid</string>

         <string id="Exclude_Vid_Pid_from_Split">Exclude Vid/Pid Device from Split</string>

         <string id="Exclude_Vid_Pid_from_Split_Desc">Exclude the component devices of a composite device specified by its Vendor ID and Product ID from being treated as separate devices for filtering.

Syntax: {m|o}:&lt;vid-&lt;xxxx&gt;_pid-&lt;xxxx|*&gt;&gt;[;...]
merge-flag:
m=Client setting will merge with agent setting
o=Agent setting will override client setting

For example: o:vid-0781_pid-554c;vid-0781_pid-9999</string>

         <string id="Split_Vid_Pid_Device">Split Vid/Pid Device</string>

         <string id="Split_Vid_Pid_Device_Desc">Treat the component devices of a composite device specified by its Vendor and Product ID as separate devices for filtering.

Syntax: {m|o}:&lt;vid-&lt;xxxx&gt;_pid-&lt;xxxx|*&gt;(exintf:xx[;exintf:xx])&gt;[;...]
merge-flag:
m=Client setting will merge with agent setting
o=Agent setting will override client setting

For example: o:vid-0781_pid-554c(exintf:01;exintf:02);vid-0781_pid-9999(exintf:02)</string>

         <string id="Allow_other_input_devices">Allow other input devices</string>

         <string id="Allow_other_input_devices_Desc">Allow input devices other than hid-bootable devices, keyboards and mouse device to be forwarded.

Default: Allow Forwarding</string>

         <string id="Allow_Default">Allow - Default Client Setting</string>

         <string id="Allow_Override">Allow - Override Client Setting</string>

         <string id="Disable_Default">Disable - Default Client Setting</string>

         <string id="Disable_Override">Disable - Override Client Setting</string>

         <string id="Allow_HID_Bootable">Allow HID-Bootable</string>

         <string id="Allow_HID_Bootable_Desc">Allow bootable input devices (also known as hid-bootable devices) to be forwarded.

Default: Allow Forwarding</string>

         <string id="Allow_Audio_Input_devices">Allow Audio Input Devices</string>

         <string id="Allow_Audio_Input_devices_Desc">Allow audio input devices to be forwarded.

Default: Allow Forwarding</string>

         <string id="Allow_Audio_Output_devices">Allow Audio Output Devices</string>

         <string id="Allow_Audio_Output_devices_Desc">Allow audio output devices to be forwarded.

Default: Block Forwarding</string>

         <string id="Allow_keyboard_mouse">Allow keyboard and Mouse Devices</string>

         <string id="Allow_keyboard_mouse_Desc">Allow keyboard and mouse devices to be forwarded.

Default: Block Forwarding</string>

         <string id="Allow_Video_Devices">Allow Video Devices</string>

         <string id="Allow_Video_Devices_Desc">Allow video devices to be forwarded.

Default: Allow Forwarding</string>

         <string id="Allow_Smart_Cards">Allow Smart Cards</string>

         <string id="Allow_Smart_Cards_Desc">Allow smart-card devices to be forwarded.

Default: Block Forwarding</string>

         <string id="Allow_Auto_Device_Splitting">Allow Auto Device Splitting</string>

         <string id="Allow_Auto_Device_Splitting_Desc">Exclude the component devices of any composite device from being automatically treated as separate devices.</string>

         <string id="Proxy_default_ie_autodetect">Default auto detect proxy</string>

         <string id="Proxy_default_ie_autodetect_Desc">Default IE connection setting. It turns on automactially detect settings in Internet Properties, Local Area Network Settings</string>

         <string id="Default_proxy_server">Default proxy server</string>

         <string id="Default_proxy_server_Desc">Default IE connection setting for proxy server. It specifies the proxy server that should use in Internet Properties, Local Area Network Settings</string>

         <string id="Update_Java_Proxy">Set proxy for Java applet</string>

         <string id="Update_Java_Proxy_Desc">Setting Java proxy to direct connect to by pass the browser's setting. Setting Java proxy to use client ip transparency to redirect network for Java applet. Set default to restore Java proxy settings to original settings.</string>

         <string id="Use_Client_IP">Use client ip transparency for Java proxy</string>

         <string id="Use_Direct_Connect">Use direct connect for Java proxy</string>

         <string id="Use_Default">Use the default value for Java proxy</string>

         <string id="Enable_white_list">Enable white list</string>

         <string id="Enable_black_list">Enable black list</string>

         <string id="Horizon_HTML5_FEATURES">Horizon HTML5 Features</string>

         <string id="Enable_HTML5_FEATURES">Enable Horizon HTML5 Features</string>

         <string id="Enable_HTML5_FEATURES_Desc">Enable Horizon HTML5 Features. If this policy is set to "Enabled", Horizon HTML5 Multimedia Redirection, Geolocation Redirection, Browser Redirection, or Media Optimization for Microsoft Teams can be used.  If this policy is set to "Disabled", none of the Horizon HTML5 features can be used. The setting takes effect at next logon.</string>

         <string id="HTML5FEATURES_Disable_AudoDetect_Intranet">Disable Automatically Detect Intranet</string>

         <string id="HTML5FEATURES_Disable_AudoDetect_Intranet_Desc">When the policy is "Enabled", the intranet setting "Include all local (intranet) sites not listed in other zones" and "Include all sites that bypass the proxy server" will be disabled during the next logon. When the policy is "Disabled", no change will be made to the IE Local intranet zone.

Note: It is required that this policy is "Enabled" when (1) Edge browser is enabled for Horizon HTML5 Multimedia Redirection, or (2) Geolocation Redirection is enabled.</string>

         <string id="Horizon_HTML5MMR">Horizon HTML5 Multimedia Redirection</string>

         <string id="Enable_HTML5_MMR">Enable Horizon HTML5 Multimedia Redirection</string>

         <string id="Enable_HTML5_MMR_Desc">Enable Horizon HTML5 Multimedia Redirection. The setting takes effect at next logon.</string>

         <string id="HTML5MMRUrlList">Enable URL list for Horizon HTML5 Multimedia Redirection.</string>

         <string id="HTML5MMRUrlBlockList">Exempt URL list for Horizon HTML5 Multimedia Redirection.</string>

         <string id="HTML5MMRUrlList_Desc">Specifies the url list to enable the Horizon HTML5 Multimedia Redirection. Specify the url pattern in the "Value name" column, e.g "https://www.youtube.com/*". The "Value" column should be empty and it is reserved for the future use.</string>

         <string id="HTML5MMRUrlBlockList_Desc">Specifies the url list to be exempted from the Horizon HTML5 Multimedia Redirection. Specify the url pattern in the "Value name" column, e.g "https://www.youtube.com/*". The "Value" column is reserved for future use and should be empty.</string>

         <string id="HTML5MMR_Enable_Chrome">Enable Chrome Browser for Horizon HTML5 Multimedia Redirection</string>

         <string id="HTML5MMR_Enable_Chrome_Desc">This policy is only used when the Horizon HTML5 Multimedia Redirection is "enabled". If not configured, the default value will be the same as the "Enable Horizon HTML5 Multimedia Redirection" enablement.</string>

         <string id="HTML5MMR_Enable_Edge">Enable legacy version of Microsoft Edge Browser for Horizon HTML5 Multimedia Redirection</string>

         <string id="HTML5MMR_Enable_Edge_Desc">This policy is only used when the Horizon HTML5 Multimedia Redirection is "enabled". If not configured, the default value will be the same as the "Enable Horizon HTML5 Multimedia Redirection" enablement. </string>

         <string id="HTML5MMR_Enable_Edge_Chromium">Enable Microsoft Edge (Chromium) Browser for Horizon HTML5 Multimedia Redirection</string>

         <string id="HTML5MMR_Enable_Edge_Chromium_Desc">This policy is only used when the Horizon HTML5 Multimedia Redirection is "enabled". If not configured, the default value will be the same as the "Enable Horizon HTML5 Multimedia Redirection" enablement. </string>

         <string id="HTML5MMR_Auto_Adjust_Visual_Effect">Automatically adjust windows visual effect</string>

         <string id="HTML5MMR_Auto_Adjust_Visual_Effect_Desc">This policy is used to adjust window's visual effect automatically for the Horizon HTML5 Multimedia Redirection. If not configured or disabled, the windows visual effect will not be adjusted automatcially.</string>

         <string id="Horizon_GEO_REDIR">Horizon Geolocation Redirection</string>

         <string id="Enable_GEO_REDIR">Enable Horizon Geolocation Redirection</string>

         <string id="Enable_GEO_REDIR_Desc">Enable Horizon Geolocation Redirection feature. The setting takes effect at next logon.</string>

         <string id="Enable_GEO_REDIR_For_Chrome">Enable Horizon Geolocation Redirection for Chrome Browser</string>

         <string id="Enable_GEO_REDIR_For_Chrome_Desc">Enable Horizon Geolocation Redirection feature for Chrome Browser. The setting takes effect at next logon.</string>

         <string id="Enable_GEO_REDIR_For_Edge_Chromium">Enable Horizon Geolocation Redirection for Microsoft Edge (Chromium) Browser</string>

         <string id="Enable_GEO_REDIR_For_Edge_Chromium_Desc">Enable Horizon Geolocation Redirection feature for Microsoft Edge (Chromium) Browser. The setting takes effect at next logon.</string>

         <string id="GeoRedirUrlList">Enable URL list for Horizon Geolocation Redirection.</string>

         <string id="GeoRedirUrlList_Desc">Specifies the url list to enable the Geolocation Redirection feature. Specify the url pattern in the "Value name" column, e.g "https://www.youtube.com/*". The "Value" column should be empty and it is reserved for the future use. This url list will be used by (1) Horizon Horizon Geolocation Redirection extension for Google Chrome and Microsoft Edge (Chromium) browser on all RDSH and VDI environments, and by (2) Horizon Horizon Geolocation Redirection plugin for Internet Explorer on RDSH and Windows 7 VDI environments.</string>

         <string id="GeoRedirDistanceDelta">Set the minimum distance for which to report location updates</string>

         <string id="GeoRedirDistanceDelta_Desc">Specifies the minimum distance between a location update in the client and the last update reported to the agent for which the new location must be reported to the agent. By default, the minimum distance used is 75 metres.</string>

         <string id="Horizon_BROWSER_REDIR">Horizon Browser Redirection</string>

         <string id="Enable_BROWSER_REDIR">Enable Horizon Browser Redirection</string>

         <string id="Enable_BROWSER_REDIR_Desc">Enable Horizon Browser Redirection feature. The setting takes effect at next logon. Note that enabling Horizon Browser Redirection also enables Horizon Enhanced Browser Redirection.</string>

         <string id="Enable_BROWSER_REDIR_For_Chrome">Enable Horizon Browser Redirection for Chrome Browser</string>

         <string id="Enable_BROWSER_REDIR_For_Chrome_Desc">Enable Horizon Browser Redirection feature for Chrome Browser. The setting takes effect at next logon.</string>

         <string id="Enable_BROWSER_REDIR_For_Edge_Chromium">Enable Horizon Browser Redirection feature for Microsoft Edge (Chromium) Browser</string>

         <string id="Enable_BROWSER_REDIR_For_Edge_Chromium_Desc">Enable Horizon Browser Redirection feature for Microsoft Edge (Chromium) Browser. The setting takes effect at next logon.</string>

         <string id="BrowserRedirFallbackWhitelistErr">Enable automatic fallback after a whitelist violation</string>

         <string id="BrowserRedirFallbackWhitelistErr_Desc">Navigating to a URL from a tab redirected using Browser Redirection by entering in the custom address bar, in the browser's address bar, or browsing from within the redirected tab, and if the new URL is not listed in the Browser Redirection or Enhanced Browser Redirection URL list, the new URL will automatically fall back to loading on the agent when this setting is enabled. At this time, if the new URL is also in the Enhanced Browser Redirection URL list, it will be redirected using Enhanced Browser Redirection. Note that attempting to navigate to a URL not set under "Enable URL list for Horizon Browser Redirection" or under "Enable URL list for Enhanced Horizon Browser Redirection" will immediately fallback to fetch-and-render on the agent, irrespective of this setting.</string>

         <string id="BrowserRedirFetchFromServer">Enable agent-side fetch for Browser Redirection feature</string>

         <string id="BrowserRedirFetchFromServer_Desc">Enable fetching website contents from the agent instead of the client when using the Browser Redirection feature. This setting is disabled by default.</string>

         <string id="BrowserRedirShowErrPage">Show a page with error information before automatic fallback</string>

         <string id="BrowserRedirShowErrPage_Desc">This setting is only used if "Enable automatic fallback after a whitelist violation" is enabled and there is a whitelist violation. In that case, if this setting is enabled, a page will be shown with a countdown of 5 seconds after which the tab will automatically fall back to fetching and rendering the URL which caused the violation on the agent. If this setting is disabled, the tab will directly fall back to agent-side rendering, without providing the user a 5-second warning.</string>

         <string id="BrowserRedirUrlList">Enable URL list for Horizon Browser Redirection</string>

         <string id="BrowserRedirUrlList_Desc">Specifies all the URLs for the Browser Redirection feature. These URLs can be visited either by typing them in Chrome's address bar or in the custom address bar. These URLs can also be visited by navigating to them starting from another URL in the list, or from any agent-side rendered page. Specify the url pattern in the "Value name" column, e.g "https://www.youtube.com/*". The "Value" column should be empty and it is reserved for the future use. If a URL matches a pattern in both the Browser Redirection and Enhanced Browser Redirection URL lists, Enhanced Browser Redirection will take precedence.</string>

         <string id="EnhBrowserRedirUrlList">Enable URL list for Enhanced Horizon Browser Redirection</string>

         <string id="EnhBrowserRedirUrlList_Desc">Specifies all the URLs for the Enhanced Browser Redirection feature. These URLs can be visited either by typing them in Chrome's address bar, by navigating to them starting from another URL in the list, or from any agent-side rendered page. Specify the url pattern in the "Value name" column, e.g "https://www.youtube.com/*". The "Value" column should be empty and it is reserved for the future use. If a URL matches a pattern in both the Browser Redirection and Enhanced Browser Redirection URL lists, Enhanced Browser Redirection will take precedence.</string>

         <string id="BrowserRedirNavUrlList">Enable Navigation URL list for Horizon Browser Redirection</string>

         <string id="BrowserRedirNavUrlList_Desc">Specifies the URLs that a user is allowed to navigate to, either by entering it directly in the custom address bar or navigating to them starting from a URL in the other list. These URLs cannot be visited by directly typing them in Chrome's address bar, or navigating to them starting from any agent-side rendered page. Specify the url pattern in the "Value name" column, e.g "https://www.youtube.com/*". The "Value" column should be empty and it is reserved for the future use.</string>

         <string id="Horizon_WebRTC_REDIR_FEATURES">Horizon WebRTC Redirection Features</string>

         <string id="Enable_Teams_Redir">Enable Media Optimization for Microsoft Teams</string>

         <string id="Enable_Teams_Redir_Desc">This setting is used to enable or disable Microsoft Teams Optimization.

When Horizon Agent is installed, a teamsEnabled registry key is created on the agent which enables Microsoft Teams Optimization.  By default, the user has the option to use or not use Microsoft Teams Optimization by configuring the "Media Optimization for WebRTC based applications" setting in Horizon client.

If this policy is "Enabled", Microsoft Teams Optimization is enabled. If enabled and "Force client side WebRTC optimization" is checked, Teams Media Optimization is forced on the end point and any client setting or any other Admin Policy (e.g. Chrome level User policy for Chrome Client) is ignored. If enabled and "Force client side WebRTC optimization" is unchecked, the user has the option to use or not use Microsoft Teams Optimization by configuring the Horizon client "Media Optimization for WebRTC based applications" setting.

If this policy is "Disabled", Microsoft Teams Optimization is disabled and cannot be used. The Horizon client "Media Optimization for WebRTC based applications" setting will not have any effect.

By default, this policy is "Not Configured", but if the policy is changed and then changed back to "Not Configured", it will remove the teamsEnabled registry key, and Microsoft Teams Optimization will not be used.

The setting takes effect at next logon.</string>

         <string id="Enable_Electron_App_Redir">Enable Media Optimization for general Electron Apps</string>

         <string id="Enable_Electron_App_Redir_Desc">This setting is used to enable or disable Electron App Optimization.

If "Enabled" or "Not Configured", Electron App Optimization is enabled. In addition, if you want to force end user to use Optimization (if supported on end point), choose "Enabled" and select "Force client side WebRTC optimization". "Not Configured" will honor client setting if available.
Details:
If enabled and if  "Force client side WebRTC optimization" is unchecked, the user has the option to use or not use Electron App Optimization by configuring the Horizon client "Media Optimization for WebRTC based applications" setting. If checked, Electron App Media Optimization is forced on end point and client setting or any other Admin Policy (e.g. Chrome level User policy for Chrome Client) is ignored.
By default, the Electron App Optimization setting is "Not Configured", enabling Electron App Optimization and allowing the user to configure the "Media Optimization for WebRTC based applications" setting.
If "Disabled", Electron App Optimization is disabled and cannot be used. The Horizon client "Media Optimization for WebRTC based applications" setting will not have any effect.

The setting takes effect at next logon.</string>

         <string id="Horizon_WebRTC_SDK_WEBAPP">Horizon WebRTC Redirection SDK Web App Support</string>

         <string id="WebRTC_SDK_Enable_Web_App_Redir">Enable Media Optimization for web applications</string>

         <string id="WebRTC_SDK_Enable_Web_App_Redir_Desc">This setting is used to enable or disable web app Optimization. If "Enabled", web app Optimization is enabled.</string>

         <string id="WebRTC_SDK_Enable_Web_App_Redir_Chrome">Enable Chrome Browser for Horizon WebRTC Redirection SDK Web App Support</string>

         <string id="WebRTC_SDK_Enable_Web_App_Redir_Chrome_Desc">This policy is only used when the Horizon WebRTC Redirection SDK Web App Support is "enabled". If not configured, the default value will be the same as the "Enable Media Optimization for web applications" enablement.</string>

         <string id="WebRTC_SDK_Enable_Web_App_Redir_Edge">Enable Chromium Edge Browser for Horizon WebRTC Redirection SDK Web App Support</string>

         <string id="WebRTC_SDK_Enable_Web_App_Redir_Edge_Desc">This policy is only used when the Horizon WebRTC Redirection SDK Web App Support is "enabled". If not configured, the default value will be the same as the "Enable Media Optimization for web applications" enablement.</string>

         <string id="WebRTC_SDK_Web_App_Redir_Allow_List">Enable URL list for Horizon WebRTC Redirection SDK Web App Support</string>

         <string id="WebRTC_SDK_Web_App_Redir_Allow_List_Desc">Specifies all the URLs for the Horizon WebRTC Redirection SDK Web App Support. These URLs can be visited by typing them in Chrome's address bar. These URLs can also be visited by navigating to them starting from another URL in the list, or from any agent-side rendered page. Specify the url pattern in the "Value name" column, e.g "https://www.youtube.com/*". The "Value" column should be empty and it is reserved for the future use.</string>

         <string id="Enable_AEC_Teams_Redir">Enable software acoustic echo cancellation for Media Optimization for Microsoft Teams</string>

         <string id="Enable_AEC_Teams_Redir_Desc">This setting is used to configure Software Acoustic Echo Cancellation (AEC) for Media Optimization for Microsoft Teams.

If "Enabled", AEC is enabled in software. Check "Use recommended AEC algorithm" for optimal audio quality and performance. Uncheck "Use recommended AEC algorithm" to use an AEC algorithm that uses less CPU but compromises audio quality. This option is useful for low end processors with low floating point power. Using the recommended AEC algorithm is strongly recommended and is ideal in most cases.

If "Disabled",  AEC will be disabled in software and will no longer be used.

If "Not Configured", AEC is enabled in software, using the recommended algorithm. When using Windows client, software AEC will be used if hardware AEC is not available. If hardware AEC is available (e.g. if the headset has built-in AEC), software AEC will not be used. When using non-Windows client, software AEC will be used irrespective if hardware AEC is available.</string>

         <string id="Enable_Datachannel_Teams_Redir">Enable datachannel for Media Optimization for Microsoft Teams</string>
         <string id="Enable_Datachannel_Teams_Redir_Desc">This setting is used to enable or disable the data channel for Media Optimization for Microsoft Teams.

If "Enabled", the data channel can be used for Media Optimization for Microsoft Teams, and features that require the data channel are available (e.g. live captions).

If "Disabled", the data channel cannot be used for Media Optimization for Microsoft Teams, and features that require the data channel will not be available.

If "Not Configured", the data channel is enabled.</string>

         <string id="Video_Cpu_Overuse_Threshold">Configure CPU overuse threshold</string>

         <string id="Video_Cpu_Overuse_Threshold_Desc"> If CPU usage is above the threshold, sent video resolution will be reduced, which will lower client CPU usage. Default threshold is 85. To reduce client CPU during video calls, set this policy to "Enabled" with a value less than 85. Set this policy to "Disabled" or "Not Configured" to use the default threshold of 85. To not detect CPU overuse, set this policy to "Enabled" with a value of 0. This setting takes effect at next logon.</string>

         <string id="Enable_Sharing_Client_Screen_InAppSession">While using Microsoft Teams application as a published application, allow sharing the client desktop screen</string>

         <string id="Enable_Sharing_Client_Screen_InAppSession_Desc">When using the Microsoft Teams application as a published application, the screen share feature will share the client desktop screen. Deactivate this policy to deactivate the screen share feature while using Microsoft Teams as a published application. If the policy is activated or not configured, the client desktop screen can be shared.</string>

         <string id="Enable_E911">Enable E911 for Microsoft Teams</string>

         <string id="Enable_E911_Desc">While Microsoft Teams runs in optimized mode, the client will send E911 data to Microsoft. To disable sharing E911 data with Microsoft, select "Disabled". If "Enabled" or "Not Configured", client E911 data will be shared with Microsoft.</string>

         <string id="Enable_HID">Enable using client HID devices button for Microsoft Teams</string>

         <string id="Enable_HID_Desc">While Microsoft Teams runs in optimized mode, user can use client HID devices button to interact with Microsoft Teams. To disable client HID devices support, select "Disabled". If "Enabled" or "Not Configured", client HID devices support will be allowed.</string>

         <string id="Enable_Webrtc_Appshare">Enable individual application sharing for Microsoft Teams</string>

         <string id="Enable_Webrtc_Appshare_Desc">While Microsoft Teams runs in optimized mode, this option allows user to share individual application. To disable application sharing, select "Disabled". If "Enabled" or "Not Configured", application sharing will be allowed.</string>

         <string id="Enable_Webrtc_Appshare_Givecontrol">Enable giving control for individual application sharing for Microsoft Teams</string>

         <string id="Enable_Webrtc_Appshare_Givecontrol_Desc">While Microsoft Teams runs in optimized mode, this option allows user to give control of a shared individual application. To disable giving control while sharing individual applications, set this policy to "Disabled". If "Enabled" or "Not Configured", giving control while sharing individual applications will be allowed.</string>

         <string id="CustomBackgroundImages">Microsoft Teams Custom Background Images</string>

         <string id="Enable_Background_Effects">Enable background effects for Microsoft Teams</string>

         <string id="Enable_Background_Effects_Desc">While Microsoft Teams runs in optimized mode, users can select a virtual background for calls and meetings. To disable background effects support, select "Disabled". If "Enabled" or "Not Configured", background effects support will be allowed.</string>

         <string id="ForceEnableCustomBackgroundImages">Force custom background images feature to be enabled or disabled for Microsoft Teams</string>

         <string id="ForceEnableCustomBackgroundImagesDesc">While Microsoft Teams runs in optimized mode, users can apply custom background images during calls and meetings. To disable custom background image support, select "Disabled". To force users to only use custom background images and prevent the stock images provided in Microsoft Teams "background effects" UI from being applied, select "Enabled". If "Not Configured", then users will be able to switch between using custom background images and Microsoft Teams provided UI images at their own discretion.</string>

         <string id="CustomBackgroundImagesFolderPath">Specify the folder for custom background images for Microsoft Teams</string>

         <string id="CustomBackgroundImagesFolderPathDesc">While Microsoft Teams runs in optimized mode, users can apply custom background images selected from a folder of images uploaded by the admin. If "Disabled" or "Not Configured", the folder which the images should be uploaded to is C:\Program Files\Common Files\Omnissa\Remote Experience\x64\MSTeamsBackgroundImages. To use a different folder, select "Enabled" and specify the path to the folder in the custom background image folder textbox, e.g "C:\Users\<USER>\CustomBackgroundImagesFolder".</string>

         <string id="CustomBackgroundDefaultImageName">Choose a default custom background image to be applied in case of user error</string>

         <string id="CustomBackgroundDefaultImageNameDesc">Specify a default custom image name to be applied in case the user leaves the imageName registry value empty or inputs an invalid custom image name when the custom background image feature is enabled.</string>

         <string id="Disable_Mirrored_Video">Disable mirrored self-preview in Microsoft Teams</string>

         <string id="Disable_Mirrored_Video_Desc">By default, the self-preview video is mirrored for Microsoft Teams in optimized mode. Setting this option will disable the mirrored video.</string>
         
         <string id="Enable_Webrtc_ProxyProbeUrl">Use the custom proxy probe url to detect the working proxy server.</string>

         <string id="Enable_Webrtc_ProxyProbeUrl_Desc">When multiple proxy servers are configured, specify the custom proxy probe url to probe the working proxy server and use it in the Microsoft Teams call. E.g https://teams.microsoft.com</string>

         <string id="Horizon_AppTap_Config">Horizon AppTap Configuration</string>

         <string id="ProcessIgnoreList">Processes to ignore when detecting empty application session</string>

         <string id="ProcessIgnoreList_Desc">Specifies the list of processes to ignore when detecting empty application sessions. You can specify either a process filename or a full path. These values are evaluated in a case-insensitive manner. Environment variables are not allowable in paths. UNC network paths are allowable (e.g. \\Omnissa\temp\app.exe).</string>

         <string id="VDI_disconnect_time_till_logoff">Disconnected Session Time Limit (VDI)</string>

         <string id="VDI_disconnect_time_till_logoff_Desc">Specifies the amount of time after which a disconnected VDI Desktop session will automatically log off.
If "Never" is selected, disconnected VDI Desktop sessions on this machine will never log off. If "Immediately" is selected, sessions that are disconnected will immediately be logged off.

A similar setting exists in Horizon Connection Server Administrator, it can be found in Desktop Pool Settings and is named "Automatically logoff after disconnect". If both this setting and the Horizon Connection Server Administrator setting are configured, the value selected here takes precedence.
For example, selecting "Never" here will prevent a disconnected session (on this machine) from ever logging off, regardless of what is set on Horizon Connection Server Administrator.</string>

         <string id="RDS_idle_time_till_disconnect">RDS Idle Time Until Disconnect</string>

         <string id="RDS_idle_time_till_disconnect_Desc">Specifies the amount of time after which an idle Remote Desktop Services session will automatically disconnect.
If "Never" is selected, Remote Desktop Services sessions on this machine will never disconnect.</string>

         <string id="RDS_disconnect_time_till_logoff">RDS Disconnected Time Until Logoff</string>

         <string id="RDS_disconnect_time_till_logoff_Desc">Specifies the amount of time after which a disconnected Remote Desktop Services session will automatically log off.
If "Never" is selected, disconnected Remote Desktop Services sessions on this machine will never log off.</string>

         <string id="RDS_active_time_till_disconnect">RDS Connection Time Until Disconnect</string>

         <string id="RDS_active_time_till_disconnect_Desc">Specifies the maximum amount of time that a Remote Desktop Services session can be active before it is automatically disconnected.
If "Never" is selected, Remote Desktop Services sessions on this machine will never disconnect.</string>

         <string id="RDS_end_session_time_limit">RDS End Session When Time Limit Reached</string>

         <string id="RDS_end_session_time_limit_Desc">Specifies whether to end a Remote Desktop Services session that has timed out instead of disconnecting.
If set, the session will be ended (the user is logged off and the session is deleted from the server) after time limits for active or idle sessions have been reached. By default, Remote Desktop Services sessions are disconnected after reaching their time limits.</string>

         <string id="RDS_threshold_connecting_session">Connecting Session Threshold</string>

         <string id="RDS_threshold_connecting_session_Desc">Specifies the maximum number of sessions that can concurrently log onto the RDSH machine, exempting reconnecting sessions.

If enabled, the session threshold value is initially set to 20 but should be changed according to use case. If 0 is selected, then the connecting session threshold is disabled.

This policy is disabled by default, so if the policy is not configured, then the connecting session threshold will be disabled.</string>

         <string id="RDS_threshold_load_index">Load Index Threshold</string>

         <string id="RDS_threshold_load_index_Desc">Specifies the minimum load index at which the RDSH machine will start denying session logons, exempting reconnecting sessions.

If enabled, the load threshold value is initially set to 0 but should be changed according to use case. If 0 is selected, then the load index threshold is disabled.

This policy is disabled by default, so if the policy is not configured, then the load index threshold will be disabled.</string>

         <string id="Prewarm_disconnect_time_till_logoff">Prewarm Session Time Limit</string>

         <string id="Prewarm_disconnect_time_till_logoff_Desc">Specifies the amount of time after which a prewarm session will automatically log off.</string>

         <string id="EnableUWPOnRDSH">Enable UWP support on RDSH platforms</string>

         <string id="EnableUWPOnRDSH_Desc">This policy controls whether UWP applications can be scanned and launched on RDSH farms with an operating system version that supports UWP applications. This policy does not apply to desktop OS platforms, such as VDI app remoting. When enabled, UWP applications can be used as Hosted Apps from RDSH farms. Need to restart wsnm service or restart RDSH server to make the GPO take effect. Refer to Omnissa documentation for supported platforms and whether this setting is enabled or disabled by default.</string>

        <string id="HandleLegalNoticeInWindow">Redirect legal notice messages as a window</string>

        <string id="HandleLegalNoticeInWindow_Desc">When enabled, this policy will redirect legal notices to the Horizon client in a window with the specified size. The width and height in this policy are specified in pixels. For high DPI monitors, the sizes will be multiplied based on the DPI. This functionality is only supported for RDSH Hosted Apps.
This policy is disabled by default. Need to restart RDSH server and Horizon client to make the GPO take effect.</string>

        <string id="TIME_NEVER">Never</string>

         <string id="TIME_1MIN">1 minute</string>

         <string id="TIME_5MIN">5 minutes</string>

         <string id="TIME_10MIN">10 minutes</string>

         <string id="TIME_15MIN">15 minutes</string>

         <string id="TIME_30MIN">30 minutes</string>

         <string id="TIME_1HR">1 hour</string>

         <string id="TIME_2HR">2 hours</string>

         <string id="TIME_3HR">3 hours</string>

         <string id="TIME_6HR">6 hours</string>

         <string id="TIME_8HR">8 hours</string>

         <string id="TIME_10HR">10 hours</string>

         <string id="TIME_12HR">12 hours</string>

         <string id="TIME_18HR">18 hours</string>

         <string id="TIME_1D">1 day</string>

         <string id="TIME_2D">2 days</string>

         <string id="TIME_3D">3 days</string>

         <string id="TIME_4D">4 days</string>

         <string id="TIME_5D">5 days</string>

         <string id="TIME_1W">1 week</string>

         <string id="TIME_IMMEDIATELY">Immediately</string>

         <string id="EnableBatStatRedir">Enable Battery State Redirection</string>

         <string id="EnableDisplayNetworkState">Enable Displaying Network State</string>
         <string id="EnableDisplayNetworkStateExplain">This setting allows you to configure whether to display network state messages on the Horizon client UI. When enabled, if the network connection is poor, the end user will receive a network state notification. When disabled, if the network connection is poor, the end user will not receive a network state notification. This property is enabled by default.</string>

         <string id="EnableBatStatRedir_Desc">This policy controls whether battery state redirection is enabled. When this policy is not configured, battery state redirection is enabled.</string>
         <string id="Horizon_WaterMark">Watermark</string>
         <string id="Horizon_Watermark_Config">Watermark Configuration</string>
         <string id="Desktop_Watermark_Configuration_Desc">This setting enables you to configure a watermark to appear on your virtual desktop. In the "Text" area you can set what will be displayed in the watermark. Options are:

%ViewClient_IP_Address%
%ViewClient_Broker_UserName%
%ViewClient_Broker_DomainName%
%COMPUTERNAME%
%USERDOMAIN%
%USERNAME%
%ViewClient_ConnectTime%   - Date in month/day/year
%ViewClient_ConnectTicks%  - Time in hour:minute:second

Here is an example for "Text":
%USERDOMAIN%\%USERNAME%
%COMPUTERNAME% on %ViewClient_ConnectTime%
%ViewClient_IP_Address%

The character limit for the "Text" is 256 characters, and it's limited to 1024 characters after expansion.

"Image Layout" specifies the layout of the watermark. We support Tile, Multiple and Center. Multiple puts the watermark in the center and in each corner. For APP sessions, this setting is ignored and the layout will always be Tile.
"Text Rotation" allows you to select a rotation angle for the watermark text.
"Opacity" lets you chose the transparency of the text.
"Margin" specifies the distance between the watermark and the edge of the virtual desktop screen, it only applies to the Tile layout.
"Text Color" specifies the color of the watermark text using space-separeted RGB color values in decimal, and the text outline is rendered in contrasting color. By default, the text is rendered in white and the outline is in black.
"Font Size" specifies the size of the watermark text. When this value is 0, the default font size will be applied.
"Refresh Interval" specifies the interval in seconds the watermark is refreshed. When 0 is specified, the watermark update is disabled. The maximum is 86400 seconds (24 hours).
</string>
         <string id="Tile">Tile</string>
         <string id="Multiple">Multiple</string>
         <string id="Center">Center</string>
         <string id="TextColor">Text Color</string>
         <string id="FontSize">Font Size</string>
         <string id="RefreshInterval">Refresh Interval</string>
         <string id="BlockScreenCapture">Screen-capture Blocking</string>
         <string id="BlockScreenCapture_Desc">Determines whether the end user can take screenshots of their virtual desktop or remote app from their end point. This setting can only be enforced on the Horizon Client for Windows and Horizon Client for Mac 2106 and later. Default is disabled, allowing the end user to take screenshots from their device.

Enable: Blocks end users from taking screenshots of the virtual desktop or virtual applications from their Windows or macOS devices.

Disable: Allows end users to take screenshots from their endpoint.


"Allow screen recording for Horizon Mac Client" determines whether the end user can take screen-recording of their virtual desktop or remote app from their end point when "Screen-capture Blocking" GPO is enabled. This setting can only be enforced on the Horizon Client for Mac 2309 and later. Default is unchecked, which means disallowing the end user to take screen-recording from their device.

Checked: Allows end users to take screen-recording of the virtual desktop or virtual applications from their macOS devices.

Unchecked: Blocks end users from taking screen-recording from their macOS devices.</string>
         <string id="BlockThumbnailRepresentationWhenMinimized">Block Thumbnail Representation When Minimized</string>
         <string id="BlockThumbnailRepresentationWhenMinimized_Desc">Determines whether hovering the thumbnail of remote desktop will show remote desktop content when its window is minimized.
When enabled, Horizon client application icon instead of the remote desktop content will be showed for window thumbnail and live preview when its window is minimized.
When disabled or not configured, the last remote desktop's snapshot before being minimized will be showed for its window thumbnail and live preview. This GPO only takes effect on Windows endpoints.</string>
         <string id="ScreenCaptureForMediaOffloaded">Screen-capture For Media Offloaded Solution</string>
         <string id="ScreenCaptureForMediaOffloaded_Desc">Enable end user to take screen capture for VDI agent desktop when media session is offloaded to end points.</string>
         <string id="AntiKeyLogger">Keylogger Blocking</string>
         <string id="AntiKeyLogger_Desc">Determines if the end point encrypts the communication between the keyboard and the Horizon Client to avoid key logging malware on the end point. The initial connection to the Horizon server is always protected, regardless of the GPO setting in the virtual machine. After the initial authentication, this setting determines if all typing on the end point is encrypted. This setting can only be enforced on the Horizon Client for Mac 2111 and Horizon Client for Windows 2203 or later. Default is disabled.

Enable: Encrypt all keystrokes between the keyboard and the Horizon Client.

Disable: Keystrokes are communicated normally on the end point.



"Allow Connections from Horizon Client for Windows without Antikeylogger service if the device is ARM-based" determines whether users on ARM-based Windows devices can connect to their virtual desktop or remote application using the Horizon Client for Windows when the "Keylogger Blocking" GPO is enabled but the Antikeylogger service fails to start and the device is ARM-based.

This setting applies to Horizon Client for Windows 2506 and later.

Default Behavior (unchecked):
If the Horizon Client for Windows fails to start the Antikeylogger service, the user will be disconnected. This is the secure default behavior to prevent connections when keylogger protection cannot be guaranteed. This applies to all Horizon Client devices, including those that do not support Antikeylogging.

Checked:
If the Horizon Client for Windows fails to start the Antikeylogger service, the user will be disconnected, with an exception for ARM-based Windows devices. This setting is available as a workaround until Antikeylogger support is available on ARM-based Windows devices.

Important: This GPO should not be used once ARM-based support for Antikeylogger service is available.</string>
         <string id="BlockSendInput">Synthetic Keystroke Blocking</string>
         <string id="BlockSendInput_Desc">Determines if the endpoint blocks scripts that automate keystrokes from the endpoint into a virtual desktop or application. The initial connection to the Horizon server is always protected, regardless of the GPO setting in the virtual machine. After the initial authentication, this setting determines if all synthetic keystrokes on the endpoint are blocked. This setting can only be enforced on the Horizon Client for Windows 2312 or later. The default is disabled.

If "Keylogger Blocking" is not enabled, this setting has no effect.

Enable: Block all synthetic keystrokes from the endpoint into virtual desktop or virtual applications.

Disable: Horizon Client forwards synthetic keystrokes as usual.</string>
         <string id="AllowFIDO2AuthenticatorAccess">Allow FIDO2 authenticator access</string>
         <string id="AllowFIDO2AuthenticatorAccess_Desc">Determines whether applications in remote desktop can access endpoint's FIDO2 authenticators. When disabled, applications in remote desktop are not allowed to access endpoint's FIDO2 authenticators. When enabled or not configured, applications in remote desktop are allowed to access endpoint's FIDO2 authenticators.</string>
         <string id="FIDO2AllowList">FIDO2 allow list</string>
         <string id="FIDO2AllowList_Desc">A list of applications that can access endpoint's FIDO2 authenticators.

The syntax is:
   appname1.exe;appname2.exe

When this setting is not configured or disabled, the default list is used. The default list is:
   chrome.exe;firefox.exe;msedge.exe</string>

         <string id="WaitForHybridJoin">Configure wait for hybrid join</string>

         <string id="WaitForHybridJoin_Desc">This Group Policy Object (GPO) controls the behavior of the agent in relation to the Microsoft Hybrid Entra ID join process. It determines whether the agent should wait for the completion of the hybrid join process before it can serve desktop or application requests.

Disabled or Not Configured: When this setting is disabled or not configured, the agent will not wait for the hybrid join process to complete. This means that the agent can start serving requests immediately, potentially before the machine is fully integrated into Entra ID.

Enabled: When enabled, the agent will wait for the machine to successfully complete the hybrid join process with Entra ID. Only after this process is complete will the agent mark itself as AVAILABLE, indicating that it is ready to serve desktop or application requests.

Enabling this feature is crucial for ensuring that the agent is fully integrated into Entra ID before it starts serving requests. This integration is necessary for features such as Single Sign-On (SSO) into Azure/Office resources and for the device to be recognized in Entra ID for management purposes. However, it's important to note that enabling this feature might cause a significant delay in the availability of machines, as the agent waits for the hybrid join process to complete.
         </string>

         <string id="IpPrefix">Configure the subnet that Horizon Agent uses</string>

         <string id="IpPrefixDesc">When you install Horizon Agent on a virtual machine that has more than one NIC, you must configure the subnet that Horizon Agent uses. The subnet determines which network address Horizon Agent provides to the Connection Server or Connection Service instance for client protocol connections.

The syntax is:
   n.n.n.n/m

In this example, n.n.n.n is the TCP/IP subnet and m is the number of bits in the subnet mask.

Example value:
   ***********/21

In this example only IP addresses in the range *********** to ************* are accepted for use by the Horizon Agent.
         </string>

      </stringTable>

      <presentationTable>
         <presentation id="MaxCollaboratorCount">
            <decimalTextBox refId="MaxCollaboratorCount_DB" defaultValue="5">Maximum</decimalTextBox>
         </presentation>

         <presentation id="CollaborationEmailInviteDelimiter">
            <textBox refId="CollaborationEmailInviteDelimiter_TB">
               <label>Separator between e-mail addresses</label>
            </textBox>
         </presentation>

         <presentation id="CollaborationServerURLs">
            <listBox refId="CollaborationServerURLs_list">List of external server URLs and names</listBox>
         </presentation>

         <presentation id="ConnectionTicketTimeout">
            <decimalTextBox refId="ConnectionTicketTimeout_DB" defaultValue="120">Connection Ticket Timeout</decimalTextBox>
         </presentation>

         <presentation id="CredentialFilterExceptions">
            <textBox refId="CredentialFilterExceptions_TB">
               <label>Credential Filter Exceptions</label>
            </textBox>
         </presentation>
         <presentation id="RDPVcBridgeUnsupportedClients">
            <textBox refId="RDPVcBridgeUnsupportedClients_TB">
               <label>RDPVcBridge Unsupported Clients</label>
            </textBox>
         </presentation>

         <presentation id="CommandsToRunOnConnect">
            <listBox refId="CommandsToRunOnConnect_list">Commands</listBox>
         </presentation>

         <presentation id="CommandsToRunOnReconnect">
            <listBox refId="CommandsToRunOnReconnect_list">Commands</listBox>
         </presentation>

         <presentation id="CommandsToRunOnDisconnect">
            <listBox refId="CommandsToRunOnDisconnect_list">Commands</listBox>
         </presentation>

         <presentation id="SSO_retry_timeout">
            <decimalTextBox refId="SSO_retry_timeout_DB" defaultValue="5000">Single sign-on retry timeout</decimalTextBox>
         </presentation>

         <presentation id="RDS_threshold_connecting_session">
            <decimalTextBox refId="RDS_threshold_connecting_session_DB" defaultValue="20">Connecting Session Threshold Value</decimalTextBox>
         </presentation>

         <presentation id="RDS_threshold_load_index">
            <decimalTextBox refId="RDS_threshold_load_index_DB" defaultValue="0">Load Index Threshold Value</decimalTextBox>
         </presentation>

         <presentation id="Win10PhysicalAgentAudioOption">
            <dropdownList refId="Win10PhysicalAgentAudioOption_DDL" defaultItem="0">Audio option for single session Windows 10 physical Remote Desktop machine</dropdownList>
         </presentation>

         <presentation id="WaitForLogoff">
            <decimalTextBox refId="WaitForLogoff_DB" defaultValue="10">Wait for logoff timeout</decimalTextBox>
         </presentation>

         <presentation id="Accept_SSL_encr_framework_channel">
            <dropdownList refId="Accept_SSL_encr_framework_channel_DDL" defaultItem="0">Accept SSL encrypted framework channel</dropdownList>
         </presentation>

         <presentation id="Local_Reader_Name">
            <textBox refId="Local_Reader_Name_TB">
               <label>Local Reader Name</label>
            </textBox>
         </presentation>

         <presentation id="Cert_wait_timeout">
            <decimalTextBox refId="Cert_wait_timeout_DB" defaultValue="40">Certificate wait timeout</decimalTextBox>
         </presentation>

         <presentation id="Min_key_size">
            <decimalTextBox refId="Min_key_size_DB" defaultValue="1024">Minimum key size</decimalTextBox>
         </presentation>

         <presentation id="All_key_sizes">
            <textBox refId="All_key_sizes_TB">
               <label>All key sizes</label>
                  <defaultValue>2048</defaultValue>
            </textBox>
         </presentation>

         <presentation id="Keys_to_precreate">
            <decimalTextBox refId="Keys_to_precreate_DB" defaultValue="5">Number of keys to pre-create</decimalTextBox>
         </presentation>

         <presentation id="Cert_min_validity">
            <decimalTextBox refId="Cert_min_validity_DB" defaultValue="10">Minimum validity period required for a certificate</decimalTextBox>
         </presentation>

         <presentation id="WhfbCertificateAllowedApplicationsList">
            <multiTextBox refId="Whfb_Certificate_Allowed_Applications_list_TB">List of allowed executables</multiTextBox>
         </presentation>

         <presentation id="Exclude_Vid_Pid">
            <textBox refId="Exclude_Vid_Pid_TB">
               <label>Exclude Vid/Pid Device</label>
            </textBox>
         </presentation>

         <presentation id="Exclude_Vid_Pid_Rel">
            <textBox refId="Exclude_Vid_Pid_Rel_TB">
               <label>Exclude Vid/Pid/Rel Device</label>
            </textBox>
         </presentation>

         <presentation id="Include_Vid_Pid">
            <textBox refId="Include_Vid_Pid_TB">
               <label>Include Vid/Pid Device</label>
            </textBox>
         </presentation>

         <presentation id="Include_Vid_Pid_Rel">
            <textBox refId="Include_Vid_Pid_Rel_TB">
               <label>Include Vid/Pid/Rel Device</label>
            </textBox>
         </presentation>

         <presentation id="Exclude_device_family">
            <textBox refId="Exclude_device_family_TB">
               <label>Exclude Device Family</label>
            </textBox>
         </presentation>

         <presentation id="Include_device_family">
            <textBox refId="Include_device_family_TB">
               <label>Include Device Family</label>
            </textBox>
         </presentation>

         <presentation id="HidOpt_Include_Vid_Pid">
            <textBox refId="HidOpt_Include_Vid_Pid_TB">
               <label>Include HID Optimization Vid/Pid Device</label>
            </textBox>
         </presentation>

         <presentation id="Exclude_Auto_Vid_Pid">
            <textBox refId="Exclude_Auto_Vid_Pid_TB">
               <label>Exclude Automatically Connection Vid/Pid Device</label>
            </textBox>
         </presentation>

         <presentation id="Exclude_auto_device_family">
            <textBox refId="Exclude_auto_device_family_TB">
               <label>Exclude Automatically Connection Device Family</label>
            </textBox>
         </presentation>

         <presentation id="Exclude_Vid_Pid_from_Split">
            <textBox refId="Exclude_Vid_Pid_from_Split_TB">
               <label>Exclude Vid/Pid Device from Split</label>
            </textBox>
         </presentation>

         <presentation id="Split_Vid_Pid_Device">
            <textBox refId="Split_Vid_Pid_Device_TB">
               <label>Split Vid/Pid Device</label>
            </textBox>
         </presentation>

         <presentation id="Allow_other_input_devices">
            <dropdownList refId="Allow_other_input_devices_DDL" defaultItem="0">Allow other input devices</dropdownList>
         </presentation>

         <presentation id="Allow_HID_Bootable">
            <dropdownList refId="Allow_HID_Bootable_DDL" defaultItem="0">Allow HID-Bootable</dropdownList>
         </presentation>

         <presentation id="Allow_Audio_Input_devices">
            <dropdownList refId="Allow_Audio_Input_devices_DDL" defaultItem="0">Allow Audio Input Devices</dropdownList>
         </presentation>

         <presentation id="Allow_Audio_Output_devices">
            <dropdownList refId="Allow_Audio_Output_devices_DDL" defaultItem="0">Allow Audio Output Devices</dropdownList>
         </presentation>

         <presentation id="Allow_keyboard_mouse">
            <dropdownList refId="Allow_keyboard_mouse_DDL" defaultItem="0">Allow keyboard and Mouse Devices</dropdownList>
         </presentation>

         <presentation id="Allow_Video_Devices">
            <dropdownList refId="Allow_Video_Devices_DDL" defaultItem="0">Allow Video Devices</dropdownList>
         </presentation>

         <presentation id="Allow_Smart_Cards">
            <dropdownList refId="Allow_Smart_Cards_DDL" defaultItem="0">Allow Smart Cards</dropdownList>
         </presentation>

         <presentation id="Allow_Auto_Device_Splitting">
            <dropdownList refId="Allow_Auto_Device_Splitting_DDL" defaultItem="0">Allow Auto Device Splitting</dropdownList>
         </presentation>

         <presentation id="Accept_SSL_encr_framework_channel_2">
            <dropdownList refId="Accept_SSL_encr_framework_channel_2_DDL" defaultItem="0">Accept SSL encrypted framework channel</dropdownList>
         </presentation>

         <presentation id="Default_proxy_server">
            <textBox refId="Default_proxy_server_TB">
               <label>Default proxy server</label>
            </textBox>
         </presentation>

         <presentation id="Update_Java_Proxy">
            <dropdownList refId="Update_Java_Proxy_DDL" defaultItem="2">Set proxy for Java applet</dropdownList>
         </presentation>

         <presentation id="HTML5MMRUrlList">
            <listBox refId="HTML5MMRUrlList_list">The url list to enable the Horizon HTML5 Multimedia Redirection.</listBox>
         </presentation>

         <presentation id="HTML5MMRUrlBlockList">
            <listBox refId="HTML5MMRUrlBlockList_list">The url list to exempt the Horizon HTML5 Multimedia Redirection.</listBox>
         </presentation>

         <presentation id="GeoRedirUrlList">
            <listBox refId="GeoRedirUrlList_list">The url list to enable the Horizon Geolocation Redirection feature.</listBox>
         </presentation>

         <presentation id="GeoRedirDistanceDelta">
            <textBox refId="GeoRedirDistanceDelta_value">
               <label>The minimum distance in metres</label>
               <defaultValue>75</defaultValue>
            </textBox>
         </presentation>

         <presentation id="WebrtcProxyProbeUrl">
            <textBox refId="WebrtcProxyProbeUrl_value">
               <label>Use the url to probe proxy server for webrtc calls</label>
            </textBox>
         </presentation>


         <presentation id="BrowserRedirUrlList">
            <listBox refId="BrowserRedirUrlList_list">The url list to enable the Horizon Browser Redirection feature.</listBox>
         </presentation>

         <presentation id="EnhBrowserRedirUrlList">
            <listBox refId="EnhBrowserRedirUrlList_list">The url list to enable the Horizon Enhanced Browser Redirection feature.</listBox>
         </presentation>

         <presentation id="BrowserRedirNavUrlList">
            <listBox refId="BrowserRedirNavUrlList_list">The url list to enable navigation for the Horizon Browser Redirection feature.</listBox>
         </presentation>

         <presentation id="WebRTC_SDK_Web_App_Redir_Allow_List">
            <listBox refId="WebRTC_SDK_Web_App_Redir_Allow_List_list">The url list to enable the Horizon WebRTC SDK for web application support.</listBox>
         </presentation>

         <presentation id="SetForceNonOptimizedOptions">
            <checkBox refId="AUTO_DETECT_EXTERNAL_CONNECTIONS_CHKBOX">Automatically detect external connections</checkBox>
            <textBox refId="SetForceNonOptimizedEnvVar_TB">
               <label>The name of environment variable:</label>
            </textBox>
         </presentation>

         <presentation id="UnityFilterRules_Filter">
            <listBox refId="UnityFilterRules_FilterList">Unity Filter rules</listBox>
         </presentation>

         <presentation id="Enable_Unity_Touch">
            <checkBox refId="Enabled_UWP_For_UnityTouch_CB" defaultChecked="true">Enable Universal Windows Platform(UWP) app support for Unity Touch on Windows 10.</checkBox>
         </presentation>

         <presentation id="ProcessIgnoreList">
            <multiTextBox refId="ProcessIgnoreList_TB">Processes to ignore when detecting empty application sessions</multiTextBox>
         </presentation>

         <presentation id="VDI_idle_time_till_disconnect">
            <dropdownList refId="VDI_idle_time_till_disconnect_minutes" noSort="true" defaultItem="0">Idle Timeout</dropdownList>
         </presentation>

         <presentation id="VDI_disconnect_time_till_logoff">
            <dropdownList refId="VDI_disconnect_time_till_logoff_minutes" noSort="true" defaultItem="0">Disconnect Timeout</dropdownList>
         </presentation>

         <presentation id="RDS_idle_time_till_disconnect">
            <dropdownList refId="RDS_idle_time_till_disconnect_milliseconds" noSort="true" defaultItem="0">RDS Idle Timeout</dropdownList>
         </presentation>

         <presentation id="RDS_disconnect_time_till_logoff">
            <dropdownList refId="RDS_disconnect_time_till_logoff_milliseconds" noSort="true" defaultItem="0">RDS Disconnect Timeout</dropdownList>
         </presentation>

         <presentation id="RDS_active_time_till_disconnect">
            <dropdownList refId="RDS_active_time_till_disconnect_milliseconds" noSort="true" defaultItem="0">RDS Connection Timeout</dropdownList>
         </presentation>

         <presentation id="Prewarm_disconnect_time_till_logoff">
            <dropdownList refId="Prewarm_disconnect_time_till_logoff_minutes" noSort="true" defaultItem="5">Prewarm Timeout</dropdownList>
         </presentation>

         <presentation id ="watermark_configuration">
            <multiTextBox refId="Text">Text</multiTextBox>
            <dropdownList refId="ImageFit" noSort="true" defaultItem="0">Image Layout</dropdownList>
            <decimalTextBox refId="Rotation" defaultValue="45">Text Rotation</decimalTextBox>
            <decimalTextBox refId="Opacity" defaultValue="50">Opacity</decimalTextBox>
            <decimalTextBox refId="Margin" defaultValue="50">Margin</decimalTextBox>
            <textBox refId="TextColor">
               <label>Text Color</label>
               <defaultValue>255 255 255</defaultValue>
            </textBox>
            <decimalTextBox refId="FontSize" defaultValue="0">Font Size</decimalTextBox>
            <decimalTextBox refId="RefreshInterval" defaultValue="0">Refresh Interval</decimalTextBox>
         </presentation>

        <presentation id="AppSignInWindow">
            <decimalTextBox refId="app_sign_in_window_width" defaultValue="800" spinStep="2">Legal notice window width: </decimalTextBox>
            <decimalTextBox refId="app_sign_in_window_height" defaultValue="600" spinStep="2">Legal notice window height: </decimalTextBox>
        </presentation>
        <presentation id="Video_Cpu_Overuse_Threshold">
            <decimalTextBox refId="Video_Cpu_Overuse_Threshold_data" defaultValue="85">Video CPU overuse threshold</decimalTextBox>
         </presentation>
         <presentation id="Webrtc_Recommended_AEC">
            <checkBox refId="Webrtc_Recommended_AEC_CB" defaultChecked="true"> Use recommended AEC algorithm </checkBox>
         </presentation>
         <presentation id="Force_Enable_Teams_Redir">
            <checkBox refId="Force_Enable_Teams_Redir_Value" defaultChecked="false"> Force client side WebRTC optimization </checkBox>
         </presentation>
         <presentation id="Force_Enable_Electron_App_Redir">
            <checkBox refId="Force_Enable_Electron_App_Redir_Value" defaultChecked="false"> Force client side WebRTC optimization </checkBox>
         </presentation>
         <presentation id="FIDO2AllowList">
            <textBox refId="FIDO2AllowList_TB">
               <label>FIDO2 Allow list</label>
               <defaultValue>chrome.exe;firefox.exe;msedge.exe</defaultValue>
            </textBox>
         </presentation>
         <presentation id="AllowScreenRecording">
            <checkBox refId="AllowScreenRecording_CB" defaultChecked="false"> Allow screen recording for Horizon Mac client </checkBox>
         </presentation>
         <presentation id="AllowConnectionFromWindowsARMClientWithoutAntiKeyLogger">
            <checkBox refId="AllowConnectionFromWindowsARMClientWithoutAntiKeyLogger_CB" defaultChecked="false">Allow Connections from Horizon Client for Windows without Antikeylogger service if the device is ARM-based</checkBox>
         </presentation>
         <presentation id="CustomBackgroundImagesFolderPath">
            <textBox refId="CustomBackgroundImagesFolder_Path">
               <label>Custom background image folder</label>
            </textBox>
         </presentation>
         <presentation id="CustomBackgroundDefaultImageName">
            <textBox refId="CustomBackgroundDefaultImageName_Value">
               <label>Default image name</label>
            </textBox>
         </presentation>
         <presentation id="EnableDisplayNetworkState">
            <decimalTextBox refId="NetworkWarningInterval_CB" defaultValue="5">The time interval for network warning pop-up message, in minutes. Maximum 60 minutes, minimum 1 minute. The default value is 5 minutes.</decimalTextBox>
         </presentation>
         <presentation id="IpPrefix" >
            <textBox refId="IpPrefixTextBox" >
               <label >IP Prefix</label>
            </textBox>
         </presentation>
      </presentationTable>
   </resources>
</policyDefinitionResources>
