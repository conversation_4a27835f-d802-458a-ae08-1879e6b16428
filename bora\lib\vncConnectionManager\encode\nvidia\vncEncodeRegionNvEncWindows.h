/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 *
 * vncEncodeRegionNvEncWindows.h --
 *
 *      Windows-specific functions for the Nvidia HW encoder.
 */

#if defined(_WIN32)

#   define NVML_LIBRARY_NAME "nvml.dll"
#   define NVSMI_REG_PATH "SOFTWARE\\NVIDIA Corporation\\Global\\NVSMI"
#   define NVSMI_REG_KEY "NVSMIPATH"
#   ifdef _WIN64
#      define NVENCODE_LIBRARY_NAME "nvEncodeAPI64.dll"
#   else
#      define NVENCODE_LIBRARY_NAME "nvEncodeAPI.dll"
#   endif

#   define DLSYM(name, dllHandle)                                                                  \
      do {                                                                                         \
         FARPROC *fnPtr = (FARPROC *)&nvidiasdk.name;                                              \
         *fnPtr = GET_SYM(dllHandle, #name);                                                       \
         if (*fnPtr == NULL) {                                                                     \
            REGENCWARN("GetProcAddress: Failed to resolve %s: %d", #name, GetLastError());         \
            goto exit;                                                                             \
         }                                                                                         \
      } while (0)
#   define DLSYM_NOFAIL(name, dllHandle)                                                           \
      do {                                                                                         \
         FARPROC *fnPtr = (FARPROC *)&nvidiasdk.name;                                              \
         *fnPtr = GET_SYM(dllHandle, #name);                                                       \
         if (*fnPtr == NULL) {                                                                     \
            REGENCWARN("GetProcAddress: Failed to resolve %s: %d", #name, GetLastError());         \
         }                                                                                         \
      } while (0)

#   define ENTER_CRITICAL_SECTION(a)                                                               \
      if ((a)->pD3d11DeviceContext) {                                                              \
         EnterCriticalSection(&(a)->csD3d11DeviceContext);                                         \
      }

#   define LEAVE_CRITICAL_SECTION(a)                                                               \
      if ((a)->pD3d11DeviceContext) {                                                              \
         LeaveCriticalSection(&(a)->csD3d11DeviceContext);                                         \
      }

#   define FILE_DUMP_PREFIX

#   include <d3d11_1.h>

void VNCEncodeRegionNvEncCloseHandles(VNCRegionEncoderNvEnc *regEnc);


/*
 *-----------------------------------------------------------------------------
 *
 * VNCEncodeRegionNvEncGetNvmlAbsolutePath --
 *
 *      Compute the path to the NVML library by reading the registry.
 *
 * Results:
 *      A string of the absolute path to the library, which must be freed by
 *      the caller, if successful; NULL otherwise.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

static char *
VNCEncodeRegionNvEncGetNvmlAbsolutePath()
{
#   define NVSMIPATH_MAXLEN 1024
   char nvsmiPath[NVSMIPATH_MAXLEN];
   unsigned long nvsmiPathLen = sizeof(nvsmiPath);

   if (RegGetValueA(HKEY_LOCAL_MACHINE, NVSMI_REG_PATH, NVSMI_REG_KEY, RRF_RT_REG_SZ, NULL,
                    &nvsmiPath, &nvsmiPathLen) != ERROR_SUCCESS) {
      Log("%s: Unable to read registry value %s\\%s\n", __FUNCTION__, NVSMI_REG_PATH,
          NVSMI_REG_KEY);
      return NULL;
   }
   return Str_Asprintf(NULL, "%s\\%s", nvsmiPath, NVML_LIBRARY_NAME);
#   undef NVSMIPATH_MAXLEN
}


/*
 *-----------------------------------------------------------------------------
 *
 * VNCEncodeRegionNvEncRegisterResource --
 *
 *      Register a D3D texture with NVENC.
 *
 * Results:
 *      The registered resource handle if successful; NULL otherwise.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

static void *
VNCEncodeRegionNvEncRegisterResource(VNCRegionEncoderNvEnc *regEnc,     // IN
                                     void *resourceToRegister,          // IN
                                     NV_ENC_BUFFER_FORMAT bufferFormat) // IN
{
   NV_ENC_REGISTER_RESOURCE param;
   NVENCSTATUS status;

   /* Register the D3D resource (input surface) with the encoder. */
   memset(&param, 0, sizeof(param));
   param.version = NV_ENC_REGISTER_RESOURCE_VER;
   param.resourceToRegister = resourceToRegister;
   param.resourceType = NV_ENC_INPUT_RESOURCE_TYPE_DIRECTX;
   param.bufferFormat = bufferFormat;
   param.width = Rect_Width(&regEnc->base.config.region.rect);
   param.height = Rect_Width(&regEnc->base.config.region.rect);

   status = regEnc->encodeFns.nvEncRegisterResource(regEnc->hNVEnc, &param);

   return NV_ENC_SUCCEEDED(status) && param.registeredResource != NULL ? param.registeredResource
                                                                       : NULL;
}


/*
 *-----------------------------------------------------------------------------
 *
 * VNCEncodeRegionNvEncRegisterTexture --
 *
 *      Create a new D3D11 texture associated with the given shared handle,
 *      and register it with NVENC.
 *
 * Results:
 *      Returns TRUE if successful, FALSE otherwise.
 *
 * Side effects:
 *      An additional handle slot will be occupied.
 *
 *-----------------------------------------------------------------------------
 */

static Bool
VNCEncodeRegionNvEncRegisterTexture(VNCRegionEncoderNvEnc *regEnc,         // IN
                                    const VNCRegEncFrameState *frameState, // IN
                                    int *allocTexIdx)                      // OUT
{
   Bool success = FALSE;
   HRESULT result;
   IID iidD3D11Texture2D = IID_ID3D11Texture2D;
   IID iidDXGIKeyedMutex = IID_IDXGIKeyedMutex;
   IID iidD3D11Device1 = IID_ID3D11Device1;
   ID3D11Device1 *pDevice1 = NULL;
   void *resourceToRegister = NULL;
   void *registeredResource;
   int texIdx;
   NV_ENC_BUFFER_FORMAT bufferFormat;

   /* Find a free slot for this handle. */
   texIdx = VNCEncodeRegionNvEncFindFreeHandleSlot(regEnc);

   /*
    * If there is no available slot for this handle, take it as a hint that the
    * pixel providers have been invalidated and replace the old handles with
    * the new incoming ones (bug 2270135).
    */
   if (texIdx < 0) {
      REGENC_RLOG(3, "out of input resources, freeing old textures/handles");
      VNCEncodeRegionNvEncCloseHandles(regEnc);
      texIdx = VNCEncodeRegionNvEncFindFreeHandleSlot(regEnc);
   }
   VERIFY(texIdx >= 0 && texIdx < TOTAL_INPUT_RESOURCE);

   regEnc->pSharedHandle[texIdx] = (HANDLE)frameState->sharedHandle;

   *allocTexIdx = texIdx;

   if (regEnc->pD3d11Device) {
      D3D11_TEXTURE2D_DESC desc = {0};
      if (regEnc->useUnifiedD3DDevice) {
         REGENC_RLOG(3, "new shared texture %p (slot %d)", frameState->texture, texIdx);
         if (FAILED(result = ID3D11Texture2D_QueryInterface(
                       (ID3D11Texture2D *)frameState->texture, &iidD3D11Texture2D,
                       (void **)&regEnc->pD3d11Texture[texIdx]))) {
            REGENCWARN_HRESULT(result, "%s: Failed to query D3D11 texture (device %p)",
                               __FUNCTION__, regEnc->pD3d11Device);
            goto exit;
         }
      } else {
         REGENC_RLOG(3, "new shared handle %p (slot %d)", frameState->sharedHandle, texIdx);

         if (FAILED(result = ID3D11Device_QueryInterface(regEnc->pD3d11Device, &iidD3D11Device1,
                                                         (void **)&pDevice1))) {
            REGENCWARN_HRESULT(result, "%s: Failed to query ID3D11Device1 (device %p)",
                               __FUNCTION__, regEnc->pD3d11Device);
            goto exit;
         }

         if (FAILED(result = ID3D11Device1_OpenSharedResource1(
                       pDevice1, regEnc->pSharedHandle[texIdx], &iidD3D11Texture2D,
                       (void **)&regEnc->pD3d11Texture[texIdx]))) {
            REGENCWARN_HRESULT(result,
                               "%s: OpenSharedResource1 failed"
                               " (device %p, handle %p)",
                               __FUNCTION__, regEnc->pD3d11Device, regEnc->pSharedHandle[texIdx]);
            goto exit;
         }
      }

      /*
       * Check if the current texture support keyed mutex. If it does it means we
       * need to acquire sync prior to accessing the texture.
       */
      if (FAILED(result = ID3D11Texture2D_QueryInterface(
                    regEnc->pD3d11Texture[texIdx], &iidDXGIKeyedMutex,
                    (void **)&regEnc->pD3d11TextureKeyedMutex[texIdx]))) {
         REGENCLG0_HRESULT(result,
                           "%s: The D3D11 texture does not support keyed mutex"
                           " (device %p, handle %p)",
                           __FUNCTION__, regEnc->pD3d11Device, regEnc->pSharedHandle[texIdx]);
      } else {
         REGENCLG0_HRESULT(result,
                           "%s: The D3D11 texture does support keyed mutex"
                           " (device %p, handle %p)",
                           __FUNCTION__, regEnc->pD3d11Device, regEnc->pSharedHandle[texIdx]);
      }

      resourceToRegister = regEnc->pD3d11Texture[texIdx];
      ID3D11Texture2D_GetDesc(regEnc->pD3d11Texture[texIdx], &desc);
      ASSERT(desc.Format == DXGI_FORMAT_B8G8R8A8_UNORM ||
             desc.Format == DXGI_FORMAT_R10G10B10A2_UNORM);

      if (desc.Format == DXGI_FORMAT_R10G10B10A2_UNORM) {
         REGENC_RLOG(1, "%s: Texture format ARGB10", __FUNCTION__);
         bufferFormat = NV_ENC_BUFFER_FORMAT_ARGB10;
      } else {
         REGENC_RLOG(1, "%s: Texture format ARGB", __FUNCTION__);
         bufferFormat = NV_ENC_BUFFER_FORMAT_ARGB;
      }

      if (regEnc->fhandleRaw) {
         desc.Usage = D3D11_USAGE_STAGING;
         desc.BindFlags = 0;
         desc.CPUAccessFlags = D3D11_CPU_ACCESS_READ;
         desc.MiscFlags = 0;
         if (FAILED(result =
                       ID3D11Device_CreateTexture2D(regEnc->pD3d11Device, &desc, NULL,
                                                    &regEnc->pD3d11TextureMappable[texIdx]))) {
            REGENCWARN_HRESULT(result, "%s: CreateTexture2D for staging texture failed",
                               __FUNCTION__);
            goto exit;
         }
      }
   } else {
      return FALSE;
   }

   /* Register the texture we just created with NVENC. */
   VERIFY(regEnc->inputRegHandle[texIdx] == NULL);

   registeredResource =
      VNCEncodeRegionNvEncRegisterResource(regEnc, resourceToRegister, bufferFormat);
   if (registeredResource == NULL) {
      REGENCWARN("%s: Failed to register resource %p with encoder", __FUNCTION__,
                 resourceToRegister);
      goto exit;
   }
   regEnc->inputRegHandle[texIdx] = registeredResource;

   success = TRUE;

exit:
   if (pDevice1 != NULL) {
      ID3D11Device1_Release(pDevice1);
   }

   return success;
}


/*
 *-----------------------------------------------------------------------------
 * VNCEncodeRegionNvEncDumpARGB --
 *
 *      Dump uncompressed surface contents.
 *
 *      NOTE: Will fill the disk at the following rate:
 *      - 1080p@30Hz: 237 MB/s
 *      - 2160p@30Hz: 949 MB/s
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      Generates EVEN BIGGER files.
 *
 *-----------------------------------------------------------------------------
 */

static void
VNCEncodeRegionNvEncDumpARGB(VNCRegionEncoderNvEnc *regEnc, // IN
                             int textureIdx)                // IN
{
   VERIFY(vmx86_debug);
   if (regEnc->fhandleRaw) {
      HRESULT result;
      size_t bufferSize;
      uint8 *buffer = NULL;

      if (regEnc->pD3d11DeviceContext) {
         /*
          * In DirectX 11, we need to explicitly copy the input texture to a
          * mappable one, then map the latter.
          */
         D3D11_MAPPED_SUBRESOURCE mappedSubRes = {0};

         ENTER_CRITICAL_SECTION(regEnc)
         ID3D11DeviceContext_CopyResource(
            regEnc->pD3d11DeviceContext,
            (ID3D11Resource *)regEnc->pD3d11TextureMappable[textureIdx],
            (ID3D11Resource *)regEnc->pD3d11Texture[textureIdx]);
         result =
            ID3D11DeviceContext_Map(regEnc->pD3d11DeviceContext,
                                    (ID3D11Resource *)regEnc->pD3d11TextureMappable[textureIdx], 0,
                                    D3D11_MAP_READ, 0, &mappedSubRes);
         if (FAILED(result)) {
            REGENCWARN_HRESULT(result, "%s: Failed to map D3D11 texture for read-back",
                               __FUNCTION__);
            LEAVE_CRITICAL_SECTION(regEnc);
            return;
         }
         bufferSize = (size_t)mappedSubRes.RowPitch * regEnc->height;
         buffer = Util_SafeMalloc(bufferSize);
         memcpy(buffer, mappedSubRes.pData, bufferSize);
         ID3D11DeviceContext_Unmap(regEnc->pD3d11DeviceContext,
                                   (ID3D11Resource *)regEnc->pD3d11TextureMappable[textureIdx], 0);
         LEAVE_CRITICAL_SECTION(regEnc)

         /*
          * Log the data to a file, which can be played with a specialized image
          * viewer.
          */
         if (fwrite(buffer, bufferSize, 1, regEnc->fhandleRaw) == 0) {
            REGENCWARN("Write to bitstream dump file failed");
            VNCEncodeRegionCloseFile(&regEnc->fhandleRaw);
         }
         free(buffer);
      }
   }
}


/*
 *-----------------------------------------------------------------------------
 * VNCEncodeRegionNvEncAcquireSync --
 *
 *      Acquires the keyed mutex for the texture at the given index.
 *
 * Results:
 *      TRUE if success, FALSE otherwise.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

static Bool
VNCEncodeRegionNvEncAcquireSync(VNCRegionEncoderNvEnc *regEnc, // IN
                                uint32 texIdx)                 // IN
{
   if (regEnc->pD3d11Device != NULL && regEnc->pD3d11TextureKeyedMutex[texIdx] != NULL) {
      HRESULT result;

      result = IDXGIKeyedMutex_AcquireSync(regEnc->pD3d11TextureKeyedMutex[texIdx], 0, 1000);
      if (FAILED(result)) {
         REGENCWARN_HRESULT(result, "%s: Failed to acquire the keyed mutex. texIdx %d",
                            __FUNCTION__, texIdx);
         return FALSE;
      }
   }

   return TRUE;
}


/*
 *-----------------------------------------------------------------------------
 *
 * VNCEncodeRegionNvEncCreateOS --
 *
 *      Create the OS specific region encoder properties.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

static VNCError
VNCEncodeRegionNvEncCreateOS(VNCRegionEncoderNvEnc *regEnc, // IN
                             void *adapterId)               // IN
{
   void **ppDevice = &regEnc->pD3d11Device;
   void **ppDeviceContext = NULL;
   void *pCriticalSection = NULL;
   if (vmx86_debug && regEnc->fhandleRaw) {
      ppDeviceContext = &regEnc->pD3d11DeviceContext;
      pCriticalSection = &regEnc->csD3d11DeviceContext;
   }
   if (VNCServerOS.CreateDevice(PCI_VENDOR_ID_NVIDIA, adapterId, ppDevice, ppDeviceContext,
                                pCriticalSection)) {
      return VNC_SUCCESS;
   }
   return VNC_ERROR_HW_ENCODE_FAILED;
}


/*
 *-----------------------------------------------------------------------------
 *
 * VNCEncodeRegionNvEncDestroyOS --
 *
 *      Destroy the OS specific region encoder properties.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

static void
VNCEncodeRegionNvEncDestroyOS(VNCRegionEncoderNvEnc *regEnc) // IN
{
   if (regEnc->pD3d11Device) {
      VNCServerOS.DestroyDevice(&regEnc->pD3d11Device, &regEnc->pD3d11DeviceContext,
                                &regEnc->csD3d11DeviceContext);
   }
}


/*
 *-----------------------------------------------------------------------------
 * VNCEncodeRegionNvEncReleaseSync --
 *
 *      Releases the keyed mutex for the texture at the given index.
 *
 * Results:
 *      TRUE if success, FALSE otherwise.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

static Bool
VNCEncodeRegionNvEncReleaseSync(VNCRegionEncoderNvEnc *regEnc, // IN
                                uint32 texIdx)                 // IN
{
   if (regEnc->pD3d11Device != NULL && regEnc->pD3d11TextureKeyedMutex[texIdx] != NULL) {
      HRESULT result;

      result = IDXGIKeyedMutex_ReleaseSync(regEnc->pD3d11TextureKeyedMutex[texIdx], 0);
      if (FAILED(result)) {
         REGENCWARN_HRESULT(result, "%s: Failed to release the keyed mutex. texIdx %d",
                            __FUNCTION__, texIdx);
         return FALSE;
      }
   }

   return TRUE;
}
#endif
