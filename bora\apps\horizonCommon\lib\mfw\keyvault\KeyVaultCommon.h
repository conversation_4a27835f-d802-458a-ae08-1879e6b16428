/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/** \file KeyVaultCommon.h
 *
 *  Common data types used in KeyVault
 */

#pragma once

#ifdef WIN32
#   ifndef KeyVaultCommon

#      include <shlwapi.h>
#      pragma comment(lib, "shlwapi")

// #define CORE_ERRLOG_NO_FUNCTION_NAME
#      ifndef CORE_ERRLOG_NO_FUNCTION_NAME
#         ifndef MESSAGE_FRAMEWORK_DLL
#            define CORE_ERRLOG_NO_FUNCTION_NAME
#         endif
#      endif
#      define CORE_ERRLOG_PREFIX TEXT("KeyVault")
#      include <corelogerr.h>
#      include <cedar/unique_any.h>

#      include "KeyVaultPublic.h"


namespace CORE {

using unique_bcrypt_handle =
   cedar::unique_any<BCRYPT_KEY_HANDLE, decltype(&::BCryptDestroyKey), ::BCryptDestroyKey>;
/*
 * Definition for lsa root key name
 */

#      define LSAROOTNAME(prog) (tstr(TEXT("KeyVault_")) + prog + TEXT("_root"))


/*
 * Definition for registry location
 */

#      define KEYPATH tstr::dropFirstPath(utils::regPath()) + TEXT("\\KeyVaultCNG\\")
#      define KEYPATH_CAPI tstr::dropFirstPath(utils::regPath()) + TEXT("\\KeyVault\\")


/*
 * Definitions for variables in the key main PropertyBag
 */

#      define KEY_CURRGEN TEXT("currGeneration")
#      define KEY_IN_CAPI TEXT("capiKey")
#      define KEY_FROM_CAPI TEXT("importedFromCAPI")
#      define KEY_WONT_LOAD TEXT("cannotImportFromCAPI")
#      define KEY_CAPI_DEAD TEXT("\\capiUnusable")
#      define KEY_CAPI_REMOVED TEXT("\\capiRemoved")
#      define KEY_CAPI_UNSHARED TEXT("\\capiUnshared")


/*
 * Definitions for variables in the key generation PropertyBag
 */

#      define KEY_NAMESPACE TEXT("namespace")
#      define KEY_NAME TEXT("name")
#      define KEY_GEN TEXT("generation")
#      define KEY_TYPE TEXT("type")
#      define KEY_SUBTYPE TEXT("subType")
#      define KEY_SYNTHETIC TEXT("synthetic") // only for KeyPair
#      define KEY_LENGTH TEXT("keyLength")
#      define KEY_PERSIST TEXT("persist")
#      define KEY_ENCIPHER_NAMESPACE TEXT("encipherNamespace")
#      define KEY_ENCIPHER_NAME TEXT("encipherName")
#      define KEY_ENCIPHER_GEN TEXT("encipherGen")
#      define KEY_TRANSFORM TEXT("transform")
#      define KEY_APPDATASIZE TEXT("sizeOfData")

#      define KEY_HKEY TEXT("cryptHandle")
#      define KEY_DATA TEXT("blobData")
#      define KEY_LOADED_CAPI TEXT("keyFromCAPI")

/*
 * Definitions for the KEY_SUBTYPE
 */

#      define SUBTYPE_SYMMETRIC TEXT("Symmetric")
#      define SUBTYPE_ASYMMETRIC TEXT("Asymmetric")

/*
 * Definitions for builtin keynames
 */

#      define SECURITYMGRSSO_KEYNAME TEXT("SECURITYMGRSSO")
#      define SECURITYMGRSESS_KEYNAME TEXT("SECURITYMGRSESS")
#      define SUPPLIED_KEYNAME TEXT("#")

/*
 * Definitions for namespaces
 */

#      define NAMESPACE_SHARED TEXT("shared")
enum keyNameSpace { namespace_none, namespace_shared };
#      define HIGHEST_NAMESPACE namespace_shared


/*
 * Definitions for variables in the parameter PropertyBag
 */

#      define KEYPAR_NAME TEXT("name")                       // string
#      define KEYPAR_GEN TEXT("gen")                         // uint
#      define KEYPAR_ENCIPHERNAME TEXT("encipherName")       // string
#      define KEYPAR_ENCIPHERGEN TEXT("encipherGen")         // uint
#      define KEYPAR_LENGTH TEXT("length")                   // uint
#      define KEYPAR_PERSIST TEXT("persist")                 // bool
#      define KEYPAR_TRUSTED TEXT("trusted")                 // bool
#      define KEYPAR_TRANSFORM TEXT("transform")             // bag
#      define KEYPAR_DERIVEFROMKEY TEXT("deriveFromKey")     // string
#      define KEYPAR_CONTEXT TEXT("context")                 // string
#      define KEYPAR_DECIPHER TEXT("decipher")               // bool
#      define KEYPAR_CHECKDEPENDENTS TEXT("checkDependents") // bool
#      define KEYPAR_OPTIMEOUT_MS TEXT("opTimeoutMs")        // DWORD
#      define KEYPAR_BINDATA_TO_SIGN TEXT("binDataToSign")   // binary
#      define KEYPAR_BINDATA_SIGNED TEXT("binDataSigned")    // binary
#      define KEYPAR_SALT TEXT("salt")                       // binary

#      define KEYPAR_TRANSFORM_XF_AAD TEXT("XF_AAD")       // binary
#      define KEYPAR_TRANSFORM_XF_KEY TEXT("XF_KEY")       // binary
#      define KEYPAR_TRANSFORM_XF_TAG TEXT("XF_TAG")       // binary
#      define KEYPAR_TRANSFORM_XF_TAGLEN TEXT("XF_TAGLEN") // uint
#      define KEYPAR_TRANSFORM_XF_ALGID TEXT("XF_ALGID")   // string
#      define KEYPAR_TRANSFORM_KP_ALGID TEXT("KP_ALGID")   // string


/*
 * Definitions for key collections
 */

#      define KEYCOL_NAME TEXT("collectionName")
#      define KEYCOL_MINCOUNT TEXT("collectionMinKeycount")
#      define KEYCOL_MAXCOUNT TEXT("collectionMaxKeycount")
#      define KEYCOL_KEYLENGTH TEXT("collectionKeylength")
#      define KEYCOL_ALGORITHM TEXT("collectionAlgorithm")
#      define KEYCOL_MANAGED TEXT("collectionManaged")
#      define KEYCOL_PERSISTENT TEXT("collectionPersistent")
#      define KEYCOL_KEYS TEXT("collectionKeys")
#      define KEYCOL_OPENEXISTING TEXT("collectionOpenExisting")

#      define KEYCOL_KEYGEN_INTERVAL_MS TEXT("collectionKeygenIntervalMS")

/*
 * Definitions for response values in the response PropertyBag
 */

#      define RESP_COUNT TEXT("count") // used for all counts
#      define RESP_LIST TEXT("names")  // comma separated list of "name#gen"
#      define RESP_GEN TEXT("gen")
#      define RESP_COLL_COUNT TEXT("collectionCount")
#      define RESP_COLL_LOADED TEXT("collectionIsLoaded")
#      define RESP_COLL_NAME KEYCOL_NAME
#      define RESP_COLL_KEYS KEYCOL_KEYS

/*
 * Default values
 */

#      define DEF_KEYCOL_MINCOUNT 1
#      define DEF_KEYCOL_MAXCOUNT 100
#      define DEF_KEYCOL_MINCOUNT_LIMIT DEF_KEYCOL_MAXCOUNT
#      define DEF_KEYCOL_MAXCOUNT_LIMIT DEF_KEYCOL_MINCOUNT_LIMIT
#      define DEF_KEYGEN_INTERVAL_MS 200
#      define DEF_KEYGEN_INTERVAL_MS_LIMIT 10000


/*
 * Definitions for the KEY_TYPE
 */

#      define TYPE_MASTER TEXT("MasterKey")
#      define TYPE_OPERATIONAL TEXT("OperationalKey")
#      define TYPE_CONTEXT TEXT("OperationalContext")
#      define TYPE_APPDATA TEXT("ApplicationData")


/*
 * Definitions for the signature transform parametera
 */

#      define KV_SHA1_ALGORITHM TEXT("SHA1")
#      define KV_SHA256_ALGORITHM TEXT("SHA256")
#      define KV_SHA384_ALGORITHM TEXT("SHA384")
#      define KV_SHA512_ALGORITHM TEXT("SHA512")

#      define KV_MAKE_STR_HELPER(x) #x
#      define KV_MAKE_STR(x) KV_MAKE_STR_HELPER(x)

#      define KP_TRANSFORM_ALG_NAME TEXT(KV_MAKE_STR(KP_ALGID))
#      define XF_TRANSFORM_REV_BYTES TEXT("ReverseBytes")

/*
 * Definitions for hash info
 */

typedef struct {
   BCRYPT_ALG_HANDLE hAlg;
   DWORD hashObjSize;
   DWORD hashSize;
   BCRYPT_PKCS1_PADDING_INFO pad;
} HashInfo;

typedef enum {
   HASH_1,
   HASH_256,
   HASH_384,
   HASH_512,
   HASH_256_HMAC,
   HASH_384_HMAC,
   HASH_512_HMAC,
   HASH_LAST
} hashIndex;

/*
 * Definitions for blob headers
 */

#      pragma pack(push, 1)
typedef struct {
   DWORD totSize;
   DWORD blobSize;
   DWORD bagEntries;
   DWORD bagBinSize;
   DWORD bagStringSize;
   DWORD signatureSize;
} regBinHeader;

typedef struct {
   DWORD nblobs;
   DWORD totSize;
   DWORD bagEntries;
   DWORD bagBinSize;
   DWORD bagStringSize;
} multiBlobHeader;
#      pragma pack(pop)


/*
 * Use the DETAILED_TRACE to trace all saved handles and all
 * destroyed handles to verify that we don't have any handle leaks
 */
// #define DETAILED_TRACE
#      ifdef DETAILED_TRACE
#         define TRACE_KEY_USAGE(keytype, set, keyname, method, handle)                            \
            sysmsg(Warn, TEXT("TRACE_KEY_USAGE: %s %s for %s in %s handle=0x%X"),                  \
                   mstr(keytype)._tstr(), set ? TEXT("set") : TEXT("deleted"), keyname.fullName(), \
                   mstr(method)._tstr(), handle)
#      else
#         define TRACE_KEY_USAGE(keytype, set, keyname, method, handle)
#      endif


#      define KV_ENTER_THREAD_GATE(gate_ptr, respBag)                                              \
         corethreadgateWrapper gatewrapper(gate_ptr);                                              \
         if (!gatewrapper.enter()) {                                                               \
            respBag.setError(KVErrLocked);                                                         \
            return false;                                                                          \
         }

#      define KV_ENTER_THREAD_GATE_MSG_HANDLER(gate_ptr, respBag)                                  \
         corethreadgateWrapper gatewrapper(gate_ptr);                                              \
         if (!gatewrapper.enter()) {                                                               \
            respBag.setError(KVErrLocked);                                                         \
            return MessageHandler::MsgError;                                                       \
         }

/*
 *--------------------------------------------------------------------------
 *
 * TempData - helper class to delete/release temporary variables
 *
 *--------------------------------------------------------------------------
 */
class TempData {
public:
   PropertyBag *pbag;
   BYTE *pdata;
   DWORD sdata;
   HKEY hreg;

   unique_bcrypt_handle hDupkey;
   BCRYPT_KEY_HANDLE hkey;
   BCRYPT_HASH_HANDLE hhash;
   BYTE *phash;

   PropertyBag transform;

   TempData();
   TempData(PropertyBag &transform);
   ~TempData();
};


/*
 *--------------------------------------------------------------------------
 *
 * KeyName - data structure to pass around keyname info
 *
 *--------------------------------------------------------------------------
 */
class KeyName {
public:
   keyNameSpace nameSpace;
   tstr name;
   DWORD gen;

   KeyName()
   {
      nameSpace = namespace_none;
      gen = 0;
   }
   KeyName(tstr &n, DWORD g = 0)
   {
      nameSpace = namespace_none, name = n;
      gen = g;
   }
   KeyName(PropertyBag *bag);

   // setName return false on bad syntax
   // syntax: "[namespace##]name[#gen]"
   bool setName(LPCTSTR name, bool allowNamespace = false, bool allowEmptyName = false,
                PropertyBag *resp = NULL);

   // returns full name with all parts as syntax above
   tstr fullName();

   inline bool isBuiltinSsoKey() { return name.comparei(SECURITYMGRSSO_KEYNAME) == 0; }

   inline bool isBuiltinSessKey() { return name.comparei(SECURITYMGRSESS_KEYNAME) == 0; }

   inline bool isSuppliedKey() { return name.compare(SUPPLIED_KEYNAME) == 0; }
};


/*
 *--------------------------------------------------------------------------
 *
 * keyVaultSort - provides sorting function for keys
 *
 *--------------------------------------------------------------------------
 */
class keyVaultSort {
public:
   bool operator()(tstr *k1, tstr *k2) const
   {
      return CompareString(LOCALE_USER_DEFAULT, NORM_IGNORECASE, k1->p(), (int)k1->s(), k2->p(),
                           (int)k2->s()) == CSTR_LESS_THAN;
   }
};


/*
 *--------------------------------------------------------------------------
 *
 * SignDataInfo -
 *
 *--------------------------------------------------------------------------
 */
class SignDataInfo {
public:
   void *sigBuffer;
   size_t sigLen;
   void *dataBuffer;
   size_t dataLen;
   tstr algName;
   bool reverseBytes;

   SignDataInfo();

   bool Init(PropertyBag &src, bool reqData = false, bool reqSig = false);
};


/*
 *--------------------------------------------------------------------------
 *
 * KeyCollectionInfo -
 *
 *--------------------------------------------------------------------------
 */
class KeyCollectionInfo {
public:
   tstr name;
   DWORD minKeyCount;
   DWORD maxKeyCount;
   DWORD keyLength;
   tstr algName;
   bool isManaged;
   bool isPersisted;

   KeyCollectionInfo();
   bool operator==(KeyCollectionInfo &x);
   bool operator!=(KeyCollectionInfo &x);
   void init(PropertyBag &src);
};


/*
 *--------------------------------------------------------------------------
 *
 * For conversion of CAPI PUBLICKEYBLOB to CNG BCRYPT_RSAPUBLIC_BLOB
 *
 *--------------------------------------------------------------------------
 */
typedef struct {
   ULONG Magic1;
   ULONG Magic2;
   ULONG Magic3;
   ULONG bitLen;
   ULONG pubExp;
   // BYTE  modulus[bitLen / 8]; // Little-endian
} CAPI_PUBLIC_BLOB;

typedef struct {
   ULONG Magic;
   ULONG BitLength;
   ULONG cbPublicExp;
   ULONG cbModulus;
   ULONG cbPrime1; // =0
   ULONG cbPrime2; // =0
   // BYTE  PublicExponent[cbPublicExp] // Big-endian.
   // BYTE  Modulus[cbModulus] // Big-endian.
} CNG_PUBLIC_BLOB;

}; // namespace CORE


/*
 *--------------------------------------------------------------------------
 *
 * forward declarations of classes
 *
 *--------------------------------------------------------------------------
 */

class KeyVault;
class KeyVaultImplementation;
class KeyCollection;


/*
 *--------------------------------------------------------------------------
 *
 * utility methods
 *
 *--------------------------------------------------------------------------
 */

bool isKeypair(PropertyBag *keybag);

NTSTATUS KV_ExportKey(BCRYPT_KEY_HANDLE hKey, LPCWSTR blobType, MsgBinary &bin, DWORD header = 0,
                      DWORD tralier = 0, DWORD *psize = NULL);

NTSTATUS KV_CreateHash(HashInfo *phash, TempData &temp, void *pSecret = NULL, DWORD sSecret = 0);

// in = 0 means that the hash is in TempData
NTSTATUS KV_SignHash(HashInfo *phash, TempData &tmp, MsgBinary &out, MsgBinary *in = 0);

bool KV_GetNcryptFromBcryptKey(__in BCRYPT_KEY_HANDLE bKey, __out NCRYPT_KEY_HANDLE *nKeyOut,
                               __in LPCWSTR pszBlobType = BCRYPT_RSAPRIVATE_BLOB);

NTSTATUS KV_DecodeBasicConstraintsExtension(__in PCCERT_CONTEXT pCertCtx, __out bool &fCA);

bool InternalCreateSelfSignedCert(PropertyBag &params, PropertyBag &response,
                                  PCERT_EXTENSIONS pAltNameExt, WorkItem *pWorkItem,
                                  NCRYPT_PROV_HANDLE *pHCryptProv = nullptr,
                                  NCRYPT_KEY_HANDLE *pHKey = nullptr);
/*
 *--------------------------------------------------------------------------
 *
 * CAPI utility method
 *
 * For old implementation compatibility but is used in a way that we
 * cannot remove it when we cut backward compatibility and remove the
 * KeyVaultCAPI.cpp
 *
 *--------------------------------------------------------------------------
 */

// For the id command
bool KV_CAPI_BuildHashForId(bool hmac, BCRYPT_KEY_HANDLE hkey, DWORD keyLength, ALG_ID algid,
                            MsgBinary &in1, MsgBinary &in2, MsgBinary &out);


#   endif // KeyVaultCommon
#endif    // WIN32
