# Horizon Clients and Agents repository

This repository contains the source for the Horizon Clients and Agents. This README will walk you through how to sync and build the source.

## Set up GitHub Access

Refer to https://omnissa.atlassian.net/wiki/spaces/EUCE/pages/83133115/How+to+Access+GitHub+Enterprise and https://omnissa.atlassian.net/wiki/spaces/EUCE/pages/83133295/Provisioning+an+SSH+Key+and+Configuring+SSO+for+Access for how to do initial setup for your machine.

## Git LFS Setup

This repository uses Git Large File Storage (LFS) for handling large files. Before cloning or after your first clone, ensure Git LFS is installed:

1. Download and install Git LFS from https://git-lfs.github.com/ .
    * On Windows, this is an optional feature in the Git installer's `Select Components` page: a checkbox named `Git LFS (Large File Support)`.
2. Set up Git LFS for your user account by running: `git lfs install`. If it is properly installed, you will see a message saying `Git LFS initialized.` If you don't, search online for troubleshooting tips.
3. On Windows, if you encounter errors like `<PERSON>rror downloading object` or `Smudge error` when attempting git operations like `git clone` or `git rebase`, try the following:
    * If you are using the `GIT_SSH` environment variable to access github via `ssh.exe`, make sure that `GIT_SSH` is using backslashes. e.g. `\Program Files\OpenSSH-Win64-v9.2.2.0p1-Beta\ssh.exe`
4. On Windows, if you are using PowerShell you may encounter `Permission denied (publickey)... failed to fetch objects from 'https://github.com/euc-vdi/cart.git/info/lfs` due to not being able to prompt for credentials for the nested `git-lfs` invocations.  The workaround is to run `git config --global credential.helper cache` to cache the credentials from the git prompt ahead of the nested `git-lfs` invocations.

## Clone main branch

Click the big green "Code" button above, choose ssh, and follow the instructions.

## Install Python and the required modules

### Installing Python

In order to build CART code, you must have Python installed and it's path is in PATH env variable. To see if you have Python installed, you can run:
- Windows: `python --version`
- Linux and Mac: `python3 --version`

If Python is installed, you should see a version outputted. If you don't, go to https://www.python.org/downloads/ and install Python.

### Installing the required modules

Run the following command:
- `pip install --upgrade -r github-deps/requirements.txt -i https://artifactory.air-watch.com/artifactory/api/pypi/uem-pypi-virtual/simple`
- Note: python3 users may need to use `pip3` depending on which version of pip is installed on your machine
- Note: Run the command from inside the cart directory of the cloned code.

This will install `migration_tools` and `artifactory_download` PYPI Library, which are needed by the initial gobuild+SCons bootstrapping code.

## Create Artifactory environment variable

In order to build correctly you need to create an identity token from the below website and set the `ARTIFACTORY_READ_TOKEN` environment variable in your build environment.
- Go to: https://artifactory.build.omnissa.com/ui/user_profile

## Windows-only extra steps

On Windows, you will want to do the following additional steps:
1. Enable Developer Mode by going to Start and typing "Use developer features", then choosing the result that matches that text, and ensuring that "Developer Mode" checkbox is set to "On".
2. Enable Windows long path support in two ways:
  - Run `git config --global core.longpaths true`
  - Follow the steps in https://learn.microsoft.com/en-us/windows/win32/fileio/maximum-file-path-limitation?tabs=registry
    - Basically: create REG_DWORD `HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\FileSystem\LongPathsEnabled` and set it to 1

## Linux-only extra step
On Linux, you might encounter difficulties proceeding with the SCons build due to restrictions on installing Python packages during the build process using pip or pip3. Most distributions prefer managing Python packages through their own package manager. The error message you may see is as follows:
```
The SCons bootstrapper encountered at least one missing module: migration_tools. Installing dependencies now...
error: externally-managed-environment

× This environment is externally managed
╰─> To install Python packages system-wide, try apt install
    python3-xyz, where xyz is the package you are trying to
    install.
```
To resolve this issue, consider creating a virtual Python environment for compilation.
- Set up a virtual Python environment in a designated directory. For example, to use `~/build-venv` as your designated venv directory, run the command: `python3 -m venv ~/build-venv`.
- Either add the activation script to your `~/.bashrc` by adding the line: `. ~/build-venv/bin/activate`, or if you want to use this virtual environment only for building scons, run it manually each time you open a new terminal to build.

## Try building!

Here are some build commands that may be helpful for you  (note: use your OS-appropriate slashes in command):
- Windows Horizon Agent (horizonagent): `scons\bin\scons PRODUCT=horizonagent`
- Linux Horizon Agent (horizonlinuxagent): `scons/bin/scons PRODUCT=horizonlinuxagent`
- Windows Horizon Client (hcwin): `scons\bin\scons PRODUCT=hccrt`
- Linux Horizon Client (hclin): `scons/bin/scons PRODUCT=hccrt`
- Android Horizon Client (hcandroid): `scons/bin/scons PRODUCT=hccrt GOBUILD_HOSTTYPE=linux-centos8-1-fw`
- MacOS Horizon Client (hcmac): `scons/bin/scons PRODUCT=hccrt`
- iOS Horizon Client (hcios): `scons/bin/scons PRODUCT=hccrt GOBUILD_HOSTTYPE=macosx-monterey-dvm-fw`
- Horizon common libraries: `scons/bin/scons PRODUCT=horizoncommon`
- AppBlast team libraries and tests: `scons/bin/scons PRODUCT=appblastlibs`

If you want to build product installers for builds that support them (e.g. horizonagent and hcwin), additionally pass `RELEASE_PACKAGES_DIR=bora/build/publish`.

If you want to learn more about SCons command-line flag, pass `-h` to SCons. Then try again with `-H` to see more options.

## More SCons tips and tricks

### Remote caching

We have added remote caching support to SCons, which allows you to download files from a central cache when a build runner has already built the same file with the same flags and dependencies. If you have a high throughput network connection, this can save a lot of time when compiling. This is automatically enabled if you have <= 200ms ping time to the cache (which is currently in the Atlanta datacenter), but can be useful in other cases. To enable remote caching, you pass `REMOTE_CACHE_FETCH=True` to SCons.

Here is a very simple command which is the equivalent of the SCons ping time check:
```
curl -w "%{time_total}\n" -o /dev/null -s http://bazel-remote.build.omnissa.com:8080/cas/e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855
```
which matches how SCons simulates the transfer of an empty cas object to get more accurate timing.

If you would like to troubleshoot remote cache behavior, you can pass `--cache-debug=cache.txt` to have SCons log more details about remote caching to that file `cache.txt`. You can also use `--cache-debug=-` to output the information to stdout, although that can be a bit unwieldy.

### Using a Local.sc file

If you have any command-line parameters which you commonly provide, you can create a file named `Local.sc` at the root of your repository and SCons will automatically pick them up. Here is an example:
```python
RELEASE_PACKAGES_DIR="bora/build/publish"
REMOTE_CACHE_FETCH=True
```

### Getting more detailed logging

If you want very verbose logging, you can pass the `VERBOSE=True` command line argument to SCons. Otherwise, SCons supports per-topic logging and you can see a full list of loggers by passing `LOGGING=help` to SCons. Per-topic logging is enabled using `LOGGING=<logger>=<level>` (e.g. `LOGGING=conan=debug`).

Passing `LOGGING=conan=debug` is especially useful if you want to see full paths to Conan dependencies. This can help in cases where you need to manually copy a dynamic library as part of running tests.

## Requesting builds

As a shortcut for requesting builds, you can run the Python script `request-builds.py` in the root folder. It defaults to running all product builds with buildtype=beta in the current branch. Run `python request-builds.py -h` for more details.

## Configure your IDE for automatic formatting

Automatic formatting makes your life so much easier. Many IDEs support automatically running clang-format - if your IDE is not on this list, please do some research and then update this file with how to enable it.

### Installing
We recommend using the `clang-format` binary from this repository instead of any pre-installed with your IDE to avoid formatting differences between different clang-format versions. You can find our clang-format binary in the `devtools/clang-format` folder:
* Windows (64-bit): `devtools/clang-format/win64/clang-format.exe`
* Linux (64-bit): `devtools/clang-format/lin64/clang-format`
* MacOS: `devtools/clang-format/macos-universal/clang-format`

### Visual Studio

#### Text Editor > Code Cleanup
```
[X] Run Code Cleanup profile on Save
```

#### Text Editor > Code Cleanup > Configure Code Cleanup
```
Included fixers:
   Format document (C++)
```

Note the default included fixes have "Format document" which is not the same
thing, we must add "Format document (C++)".

#### Text Editor > C/C++ > Code Style > Formatting
Ensure the following options are selected:
```
[X] Enable ClangFormat support
ClangFormat execution
  [X] Run ClangFormat for all formatting scenarios
[X] Use custom path to clang-format.exe
    And enter the path to the correct platform-specific clang-format executable
    underneath the `devtools/clang-format` folder.
```

### VSCode

#### Install the C/C++ Extension

Chances are that you have already installed this extension as part of using VSCode, because it will ask if you want to install the extension. If not, you can [find it here](https://marketplace.visualstudio.com/items?itemName=ms-vscode.cpptools).

#### Configure the C/C++ Extension

There are two ways to open settings for the C/C++ extension:
1. Open File > Preferences > Extensions, type `C/C++` in the search field, then click the settings gear next to the `C/C++` extension.
2. Open File > Preferences > Settings, type `@ext:ms-vscode.cpptools`.

Next, in the left sidebar of the extension settings page, choose Extensions > C/C++ > Formatting. In the field `C_Cpp: CLang_format_path`, enter the path to the correct platform-specific clang-format executable underneath the `devtools/clang-format` folder.

#### How to Format

See the [Code formatting](https://code.visualstudio.com/docs/cpp/cpp-ide#_code-formatting) portion of the VSCode docs for more details.

### Emacs 🙂

1. Copy the `clang-format` executable (e.g. `clang-format.exe`) from the `devtools` build above to a location outside of the git tree. For example: `C:\src`.
2. Download `clang-format.el` from [here](https://github.com/llvm-mirror/clang/blob/master/tools/clang-format/clang-format.el)
3. Place `clang-format.el` in the `site-lisp` directory of your emacs installation.  
For example: `C:\Program Files\emacs-30.1\emacs-30.1\share\emacs\site-lisp`.
4. Add the following lines to your `~/.emacs` file, with the correct path to the `clang-format` executable, and then restart emacs:
```
(require 'clang-format)
(setq clang-format-executable "/src/cart/devtools/clang-format/win64/clang-format.exe")
```
5. Reformat any source file with `M-x clang-format-buffer`, and/or add to auto-save as described [here](https://eklitzke.org/smarter-emacs-clang-format).

## Updating Conan dependencies

So you want to update an OSS package consumed using Conan? This section is intended to help.

For more information about how to build open source dependencies (including how to build a new version or OSS package that we haven't previously built), see [euc-eng/conan-recipes](https://github.com/euc-eng/conan-recipes). This section assumes that you have already built the OSS package you want to update to.

### Updating in cart builds ###

All Conan dependencies are managed in the top-level `conan-deps` folder of this repository. Here are steps you can follow to update Conan dependencies.
1. Look through the `conan-deps` folder for files with suffix `.py` and look for any references to the package that you want to update.
2. Make the relevant updates.
3. For each `.py` file you modified, delete the corresponding `.lock` file. For example, if you updated `hcwin.py` then you should delete `hcwin.lock`.
4. Regenerate the deleted lockfiles by running `scons/bin/scons PRODUCT=conan update-lockfiles`
5. Run `git diff` to see which lines changed. If unrelated lines changed, consider reverting those parts of the change. It's really up to you on that point; it is nice to keep things up to date but requires a bit more validation as you bring in unrelated recipe revision changes.

### Caveats ###

The above steps will work if the package and version you want has been merged to [euc-eng/conan-recipes](https://github.com/euc-eng/conan-recipes) main branch and one or more builds have completed.

If instead your package has only been built as part of a push to a topic branch, it is in a separate `euc-develop` Conan remote and needs to be opted into. The method to do this is as follows:
- When doing local builds (including `update-lockfiles` command above), you can provide the `CONAN_DEVELOP=True` command-line flag to SCons. But NOTE: if you do provide `CONAN_DEVELOP=True` to the `update-lockfiles` command, you MUST revert the lockfile changes after merging your conan-recipes change to main and regenerate the lockfiles again!
- When requesting builds through the GitHub Actions web UI, you must check the checkbox titled `I am testing conan packages and need to enable the conan-develop remote`. 
- When requesting builds through the `request-builds.py` script, you must pass the `--conan-develop` parameter.
