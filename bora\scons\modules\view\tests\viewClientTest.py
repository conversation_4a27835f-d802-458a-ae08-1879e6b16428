# Copyright (c) Omnissa, LLC. All rights reserved.
# This product is protected by copyright and intellectual property laws in the
# United States and other countries as well as by international treaties.
# -- Omnissa Restricted

import vmware
import os

Import("env_opts")

vncName = "viewClientTest"

env = vmware.Host().DefaultEnvironment()

##### Link with googletest #####
#
# Normally gtest is linked dynamically by products that use it.
# However, this test will be run on Jenkins and static linkage
# simplifies the run process, removing the reliance on looking
# in cayman directories for gtest.dll which change every so often.
env.LoadTool("gtest", linkDynamic=True)

if vmware.Host().IsMac():
    env.Append(FRAMEWORKS=["Foundation"])

env.Append(
    CPPPATH=[
        "#bora/public",
        "#bora/lib/public",
        "#bora/lib/vnc",
    ],
)

# vncLibs for all platforms
vncLibs = [
    "config",
    "coreDump",
    "dict",
    "err",
    "file",
    "lock",
    "log",
    "misc",
    "panic",
    "poll",
    "pollDefault",
    "productState",
    "sig",
    "string",
    "thread",
    "unicode",
    "user",
    "uuid",
    "version",
    "vdplib",
    "vnc",
]

if vmware.Host().IsLinux():
    env.Append(
        LIBS=[
            "dl",
        ],
    )
elif vmware.Host().IsWindows():
    vncLibs += ["wmi"]
    env.LoadTool(
        [
            "msvcrt",
        ]
    )
    env.Append(
        LIBS=[
            "advapi32.lib",
            "bcrypt.lib",
            "crypt32.lib",
            "d3d11.lib",
            "dcomp.lib",
            "ddraw.lib",
            "dxgi.lib",
            "dxguid.lib",
            "gdi32.lib",
            "glu32.lib",
            "imm32.lib",
            "iphlpapi.lib",
            "kernel32.lib",
            "msimg32.lib",
            "netapi32.lib",
            "ntdll.lib",
            "oldnames.lib",
            "ole32.lib",
            "oleaut32.lib",
            "qwave.lib",
            "setupapi.lib",
            "shell32.lib",
            "shlwapi.lib",
            "tdh.lib",
            "user32.lib",
            "uuid.lib",
            "wbemuuid.lib",
            "winmm.lib",
            "ws2_32.lib",
            "Wtsapi32.lib",
        ],
    )
e = vmware.Executable(vncName, env=env)
e.addStaticLibs("vmlibs", vncLibs)

e.addSubdirs(
    [
        "apps/rde/viewClient/tests",
    ],
    tree="bora",
)

node = e.createProgramNode()
vmware.RegisterNode(node, vncName)
vmware.Alias(vncName + "-build", node)
