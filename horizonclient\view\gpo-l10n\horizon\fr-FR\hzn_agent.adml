﻿<?xml version="1.0" encoding="utf-8"?>
<policyDefinitionResources xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" revision="1.0" schemaVersion="1.0" xmlns="http://www.microsoft.com/GroupPolicy/PolicyDefinitions">

   <displayName>Omnissa Horizon Agent</displayName>
   <description>Paramètres de configuration d'Horizon Agent</description>
   <resources>
      <stringTable>
         <string id="SUPPORTED_Windows10_1607_VM">Machine virtuelle avec au moins Windows 10/Windows Server 2016 VDI Version 1607</string>

         <string id="Agent_Configuration">Configuration de l'agent</string>

         <string id="Collaboration">Collaboration</string>

         <string id="Agent_Security">Sécurité de l'agent</string>

         <string id="Unity_Touch_Hosted_Apps">Unity Touch et applications hébergées</string>

         <string id="Unity_Filter">Liste des règles de filtre Unity</string>

         <string id="Unity_Filter_Rules_Desc">Cette stratégie spécifie les règles de filtre pour la fenêtre lors de l'accès à distance aux applications hébergées. Les règles de filtres sont utilisées par Horizon Agent pour prendre en charge les applications personnalisées. Cet objet de stratégie de groupe doit être utilisé en cas de problème d'affichage de fenêtre. Par exemple, une fenêtre déroulante ayant un arrière-plan noir ou une fenêtre déroulante de taille incorrecte.

La première étape de définition d'une règle consiste à déterminer les caractéristiques de la ou des fenêtres auxquelles appliquer la règle. De nombreuses caractéristiques possibles peuvent être identifiées :

1. Nom de classe de fenêtre, identifié dans une règle personnalisée par classname=XYZ
2. Société du produit, identifiée par company=XYZ
3. Nom du produit, identifié par product=XYZ
4. Version majeure du produit, identifiée par major=XYZ
5. Version mineure du produit, identifiée par minor=XYZ
6. Numéro de build du produit, identifié par build=XYZ
7. Numéro de révision du produit, identifié par revision=XYZ

Il est le plus courant d'utiliser uniquement « Nom de classe de fenêtre » comme caractéristique préférée (par exemple, classname=CustomClassName). En revanche, les autres caractéristiques sont fournies au cas où vous auriez besoin de limiter les règles à un produit spécifique. Ces caractéristiques sont disponibles dans la fenêtre Propriétés du fichier d'un exécutable et elles doivent être une correspondance exacte sensible à la casse incluant des caractères spéciaux. Lorsque plusieurs caractéristiques sont fournies, toutes doivent correspondre afin que la règle s'applique à cette fenêtre.

Une fois que les caractéristiques sont identifiées, l'étape suivante consiste à choisir une action. Cette action doit être action=block ou action=map. action=block indique à Horizon Agent de ne pas connecter à distance la fenêtre au client. Cette option est utilisée lorsqu'une fenêtre s'affiche sur le client, qui est trop grande ou qui interfère avec le comportement normal du focus de la fenêtre. action=map indique à Horizon Agent de traiter la fenêtre comme un certain type codé en dur.

Si vos définissez action=map, vous devez également spécifier le type auquel mapper la fenêtre. Cela s'effectue en incluant type=XYZ. Voici une liste de toutes les valeurs de types disponibles : normal, panel, dialog, tooltip, splash, toolbar, dock, desktop, widget, combobox, startscreen, sidepanel, taskbar, metrofullscreen, metrodocked.

Voici deux exemples de règles que vous pouvez définir pour résoudre une application malveillante :

1. Vous pouvez filtrer une fenêtre qui ne doit pas être déportée.
   - Pour bloquer toutes les fenêtres avec le nom de classe MyClassName, utilisez la règle « classname=MyClassName;action=block »   - Pour bloquer toutes les fenêtres du produit MyProduct, utilisez la règle « product=MyProduct;action=block ».
2. Vous pouvez mapper une fenêtre au type correct. En général, cette opération est uniquement nécessaire si Omnissa vous demande de le faire, car il est difficile de déterminer si une fenêtre est mappée sur un type incorrect.
   - Pour mapper une classe personnalisée au type zone de liste déroulante, utilisez la règle « classname=MyClassName;action=map;type=combobox »

Remarque : cet objet de stratégie de groupe (GPO) présente une priorité inférieure aux règles de filtrage installées dans %ProgramData%\Omnissa\RdeServer\Unity Filters</string>

         <string id="Smartcard_Redirection">Redirection de carte à puce</string>

         <string id="Local_Reader_Access">Accès au lecteur local</string>

         <string id="True_SSO_Configuration">Configuration de True SSO</string>

         <string id="Whfb_Certificate_Redirection">Redirection de certificat WHfB</string>

         <string id="Whfb_Certificate_Allowed_Applications">Liste des exécutables autorisés</string>

         <string id="Whfb_Certificate_Allowed_Applications_Desc">Liste des exécutables autorisés à utiliser le certificat WHfB redirigé</string>

         <string id="View_USB_Configuration">Configuration d'USB pour Horizon</string>

         <string id="Client_Downloadable_only_settings">Paramètres téléchargeables uniquement par le client</string>

         <string id="Recursive_Domain_Enumeration">Énumération récursive de domaines approuvés</string>

         <string id="Recursive_Domain_Enumeration_Desc">Détermine si le domaine dans lequel le serveur réside énumère chaque domaine approuvé. Pour établir une chaîne d'approbation complète, les domaines approuvés par chaque domaine approuvé sont également énumérés et le processus se poursuit de manière récursive jusqu'à ce que tous les domaines approuvés soient détectés. Ces informations sont transmises à Horizon Connection Server afin de garantir que tous les domaines approuvés sont disponibles pour le client lors de la connexion.

Cette propriété est activée par défaut. Lorsque cette option est désactivée, seuls les domaines directement approuvés sont énumérés et la connexion aux contrôleurs de domaine distants n'a pas lieu.

Remarque : dans des environnements contenant des relations de domaine complexes (telles que celles utilisant plusieurs structures de forêt avec approbations entre domaines de leurs forêts), ce processus peut prendre plusieurs minutes.</string>

         <string id="Force_MMR_to_use_overlay">Forcer MMR à utiliser la superposition logicielle</string>

         <string id="Force_MMR_to_use_overlay_Desc">MMR tentera d'utiliser la superposition matérielle pour lire la vidéo pour de meilleures performances. Toutefois, lorsque vous utilisez plusieurs écrans, la superposition matérielle n'existe que sur l'un des écrans : le principal ou celui sur lequel WMP a été démarré. Si WMP est glissé sur un autre écran, la vidéo s'affiche sous la forme d'un rectangle noir. Utilisez cette option pour forcer MMR à utiliser une superposition logicielle qui fonctionnera sur tous les écrans.</string>

         <string id="Enable_multi_media_acceleration">Activer l'accélération multimédia</string>

         <string id="Enable_multi_media_acceleration_Desc">Spécifie si la redirection multimédia (MMR) est activée sur l'agent. MMR est un filtre de Microsoft DirectShow qui permet de transférer des données multimédia de codecs spécifiques sur le système distant au client directement via un socket TCP. Les données sont ensuite décodées directement sur le client, lorsqu'elles sont lues. Les administrateurs peuvent désactiver MMR si le client ne dispose pas de ressources suffisantes pour gérer le décodage multimédia local.

Remarque : MMR ne fonctionnera pas correctement si le matériel d'affichage vidéo d'Horizon Client ne prend pas en charge la superposition. La stratégie MMR ne s'applique pas aux sessions de poste de travail hors ligne.</string>

         <string id="AllowDirectRDP">Autoriser RDP direct</string>

         <string id="AllowDirectRDP_Desc">Détermine si les clients non-Horizon peuvent se connecter directement à des postes de travail Horizon avec RDP. Lorsque cette propriété est désactivée, l'agent n'autorisera que les connexions gérées par Horizon via Horizon Client ou Horizon Portal.

Cette propriété est activée par défaut.</string>

         <string id="AllowSingleSignon">Autoriser l'authentification unique</string>

         <string id="AllowSingleSignon_Desc">Détermine si l'authentification Single Sign-On (SSO) est utilisée pour connecter des utilisateurs à des postes de travail Horizon. Lorsque cette propriété est activée, les utilisateurs ne doivent entrer leurs informations d'identification que lors de la connexion avec Horizon Client ou Horizon Portal. Lorsque cette propriété est désactivée, les utilisateurs doivent s'authentifier de nouveau lorsque la connexion à distance est effectuée.

Cette propriété nécessite que le composant Secure Authentication d'Horizon Agent soit installé sur le poste de travail. Elle est activée par défaut.</string>

         <string id="AutoPopulateLogonUI">Renseigner automatiquement l'interface utilisateur de l'ouverture de session</string>

         <string id="AutoPopulateLogonUI_Desc">Détermine si le champ Nom d'utilisateur dans l'interface utilisateur de l'ouverture de session est automatiquement renseigné ou non. Cette propriété est activée par défaut et n'est applicable que dans le cas de RDS lorsque Single Sign-On est désactivé ou ne fonctionne pas.</string>

         <string id="ConnectionTicketTimeout">Délai d'expiration du ticket de connexion</string>

         <string id="ConnectionTicketTimeout_Desc">Spécifie la durée en secondes pendant laquelle le ticket de connexion Horizon est valide. Le ticket de connexion est utilisé par les clients Horizon Client lors de la connexion à Horizon Agent et est utilisé à des fins de vérification et de Single Sign-On.

Pour des raisons de sécurité, ces tickets ne sont valides que dans la période spécifiée. Si cette propriété n'est pas définie explicitement, une valeur par défaut de 900 secondes s'applique.</string>

         <string id="CredentialFilterExceptions">Exceptions du filtre d'informations d'identification</string>

         <string id="CredentialFilterExceptions_Desc">Liste de noms de fichier exécutable séparés par des points-virgules non autorisés à charger l'agent CredentialFilter. Les noms de fichier ne doivent pas comporter de chemin d'accès ou de suffixe.</string>

         <string id="RDPVcBridgeUnsupportedClients">Clients RDPVcBridge non pris en charge</string>

         <string id="RDPVcBridgeUnsupportedClients_Desc">Liste de types d'instances d'Horizon Client séparés par des virgules qui ne prennent pas en charge RDPVcBridge.</string>

         <string id="Disable_Time_Zone_sync">Désactiver la synchronisation du fuseau horaire</string>

         <string id="Disable_Time_Zone_sync_Desc">Détermine si le fuseau horaire du poste de travail Horizon est synchronisé avec celui du client connecté. Lorsqu'elle est activée, cette propriété ne s'applique que si le paramètre Désactiver le transfert de fuseau horaire de la stratégie Configuration d'Horizon Client n'est pas définie sur désactivé.

Cette propriété est désactivée par défaut.</string>

         <string id="Keep_Time_Zone_sync_disconnect">Conserver la synchronisation du fuseau horaire lors de la déconnexion (VDI)</string>

         <string id="Keep_Time_Zone_sync_disconnect_Desc">Si le paramètre « Synchronisation du fuseau horaire » est activé et que cette propriété est activée, le fuseau horaire du poste de travail distant reste synchronisé avec le fuseau horaire du dernier client déconnecté.

Si cette propriété est désactivée, le fuseau horaire du poste de travail distant sera restauré lorsque la session d'utilisateur final sera déconnectée.

Ce paramètre ne s'applique pas aux hôtes RDSH lorsque le rôle Services Bureau à distance est activé.

Cette propriété est désactivée par défaut.</string>

         <string id="Keep_Time_Zone_sync_logoff">Conserver la synchronisation du fuseau horaire lors de la fermeture de session (VDI)</string>

         <string id="Keep_Time_Zone_sync_logoff_Desc">Si le paramètre « Synchronisation du fuseau horaire » est activé et que cette propriété est activée, le fuseau horaire du poste de travail distant reste synchronisé avec le fuseau horaire du client dont la fermeture de session a été la plus récente.

Si cette propriété est désactivée, le fuseau horaire du poste de travail distant sera restauré lorsque la session d'utilisateur final sera fermée.

Ce paramètre ne s'applique pas aux hôtes RDSH lorsque le rôle Services Bureau à distance est activé.

Cette propriété est désactivée par défaut.</string>

          <string id="Enable_ClientMediaPerm_Popup">Activer l'onglet, l'écran et le sélecteur d'application pour le partage d'écran avec la redirection de navigateur</string>

          <string id="Enable_ClientMediaPerm_Popup_Desc">Lorsque cette option est activée, un sélecteur permettant de sélectionner l'onglet du navigateur, l'écran ou l'application s'affiche lors du partage d'écran avec la redirection de navigateur. Cette propriété est activée par défaut.</string>

		  <string id="Toggle_Display_Settings_Control">Basculer le contrôle des paramètres d'affichage</string>

         <string id="Toggle_Display_Settings_Control_Desc">Détermine s'il faut désactiver la page Paramètres de l'applet Panneau de configuration d'affichage lorsqu'un client Horizon Client est connecté.

Cette propriété ne s'applique qu'aux sessions utilisant le protocole PCoIP. Cette propriété est activée par défaut.</string>

         <string id="DpiSync">Synchronisation DPI</string>

         <string id="DpiSync_Desc">Ajuste le paramètre DPI à l'échelle du système de la session distante. Lorsque cette propriété est activée ou non configurée, définit le paramètre DPI à l'échelle du système de la session distante pour qu'il corresponde au paramètre DPI correspondant sur le système d'exploitation client. Lorsque la propriété est désactivée, ne change jamais le paramètre DPI à l'échelle du système de la session distante.</string>

         <string id="DpiSyncPerMonitor">Synchronisation DPI par moniteur</string>

         <string id="DpiSyncPerMonitor_Desc">Ajuste le paramètre DPI dans plusieurs moniteurs au cours d'une session distante. Lorsque cette option est activée ou non configurée, le paramètre DPI dans tous les moniteurs change pour correspondre au paramètre DPI du système d'exploitation client lors d'une session distante. Si le paramètre DPI est personnalisé, le paramètre DPI personnalisé est mis en correspondance. Lorsque cette option est désactivée, les utilisateurs doivent se déconnecter et se connecter à une nouvelle session distante pour que les modifications DPI prennent effet sur tous les moniteurs.</string>

         <string id="DisplayScaling">Mise à l'échelle de l'affichage</string>

         <string id="DisplayScaling_Desc">Contrôlez si la fonctionnalité de mise à l'échelle de l'affichage est autorisée ou non côté agent. Lorsqu'elle est activée ou non configurée, la mise à l'échelle de l'affichage est autorisée côté agent et l'état final d'activation ou de désactivation de la fonctionnalité de mise à l'échelle de l'affichage dépend de la configuration côté client. Lorsque cette option est désactivée, la fonctionnalité de mise à l'échelle de l'affichage est désactivée, quelle que soit la configuration côté client. Cette configuration ne prend effet que lorsque la synchronisation PPP par moniteur est désactivée.</string>

         <string id="DisallowCollaboration">Désactiver la collaboration</string>

         <string id="DisallowCollaboration_Desc">Ce paramètre configure si la collaboration est autorisée sur la VM Horizon Agent. Si elle est activée, la fonctionnalité de collaboration est désactivée complètement. Si elle est désactivée ou non configurée, la fonctionnalité est contrôlée au niveau d'un pool. Les machines Horizon Agent doivent être redémarrées pour appliquer ce paramètre.</string>

         <string id="AllowCollaborationInviteByIM">Autoriser l'invitation de collaborateurs par messagerie instantanée</string>

         <string id="AllowCollaborationInviteByIM_Desc">Ce paramètre configure l'octroi aux utilisateurs de l'autorisation d'envoyer des invitations de collaboration à l'aide d'une application de messagerie instantanée installée. S'il est désactivé, l'utilisateur n'est pas autorisé à inviter à l'aide d'une messagerie instantanée, même si une application de messagerie instantanée est installée. Ce paramètre est activé par défaut.</string>

         <string id="AllowCollaborationInviteByEmail">Autoriser l'invitation de collaborateurs par e-mail</string>

         <string id="AllowCollaborationInviteByEmail_Desc">Ce paramètre configure l'octroi aux utilisateurs de l'autorisation d'envoyer des invitations de collaboration à l'aide d'une application de messagerie électronique installée. S'il est désactivé, l'utilisateur n'est pas autorisé à envoyer des invitations à l'aide d'un e-mail, même si une application de messagerie électronique est installée. Ce paramètre est activé par défaut.</string>

         <string id="AllowCollaborationControlPassing">Autoriser le contrôle de transmission à des collaborateurs</string>

         <string id="AllowCollaborationControlPassing_Desc">Ce paramètre configure si les utilisateurs sont autorisés à transmettre le contrôle d'entrée à d'autres collaborateurs lors de la collaboration. Ce paramètre est activé par défaut.</string>

         <string id="MaxCollaboratorCount">Nombre maximal de collaborateurs invités</string>

         <string id="MaxCollaboratorCount_Desc">Ce paramètre configure le nombre maximal de collaborateurs qu'un utilisateur peut inviter à rejoindre sa session. La valeur par défaut maximale est de 5.</string>

         <string id="CollaborationEmailInviteDelimiter">Séparateur utilisé pour plusieurs adresses électroniques dans les liens mailto:</string>

         <string id="CollaborationEmailInviteDelimiter_Desc">Ce paramètre configure le séparateur utilisé pour plusieurs adresses électroniques dans les liens mailto:. Lorsque cette stratégie n'est pas configurée, la valeur est définie par défaut sur « ; » (un point-virgule sans espace) pour séparer les adresses électroniques afin de garantir la meilleure compatibilité avec les clients de messagerie traditionnels.

Si votre client de messagerie par défaut a des problèmes avec ce séparateur, vous pouvez essayer d'autres combinaisons telles que « ,  » (une virgule et un espace) ou « ;  » (un point-virgule et un espace). Cette valeur sera codée par URI avant d'être placée dans le lien mailto:. Par conséquent, ne définissez pas cette entrée sur une valeur codée par URI.</string>

         <string id="CollaborationClipboardIncludeOutlookURL">Inclure l'URL au format Outlook dans le texte du Presse-papiers</string>

         <string id="CollaborationClipboardIncludeOutlookURL_Desc">Lorsque ce paramètre est activé, une URL d'invitation au format Outlook est incluse dans le texte d'invitation du Presse-papiers. Activez ce paramètre si vous vous attendez à ce que les utilisateurs finaux collent le texte d'invitation du Presse-papiers dans un e-mail. Ce paramètre est désactivé par défaut.</string>

         <string id="CollaborationServerURLs">URL de serveur à inclure dans un message d'invitation</string>

         <string id="CollaborationServerURLs_Desc">Ce paramètre vous permet de remplacer l'URL par défaut incluse dans les invitations de collaboration. Il vous est recommandé de définir cette valeur si votre déploiement utilise plusieurs URL de serveur (par exemple, différentes URL internes et externes ou des URL par espace).

Lors de la définition des valeurs, la première colonne doit comporter l'URL avec un port facultatif (par exemple, « horizon-ca.corp.int » ou « horizon-ca.corp.int:2323 ») et la seconde colonne doit inclure une courte description de l'URL (par exemple, « Espace Californie » ou « Réseau d'entreprise »). La description n'est utilisée que si la liste contient plusieurs serveurs.</string>

         <string id="UnAuthenticatedAccessEnabled">Activer l'accès non authentifié</string>

         <string id="UnAuthenticatedAccessEnabled_Desc">Ce paramètre active la fonctionnalité Accès non authentifié. Le système doit être redémarré pour que ce changement prenne effet. Accès non authentifié est activé par défaut.</string>

         <string id="RdsAadAuthEnabled">Activer Single Sign-on d'Azure Active Directory</string>

         <string id="RdsAadAuthEnabled_Desc">Ce paramètre active la fonctionnalité Single Sign-on d'Azure Active Directory. Le système doit être redémarré pour que ce changement prenne effet. Cette fonctionnalité est activée par défaut.  Cette fonctionnalité dépend du système associé à Azure Active Directory.</string>

         <string id="CommandsToRunOnConnect">Commandes à exécuter lors de la connexion</string>

         <string id="CommandsToRunOnConnect_Desc">Liste de commandes à exécuter lorsqu'une session est connectée pour la première fois.</string>

         <string id="CommandsToRunOnReconnect">Commandes à exécuter lors de la reconnexion</string>

         <string id="CommandsToRunOnReconnect_Desc">Liste de commandes à exécuter lorsqu'une session est reconnectée après une déconnexion.</string>

         <string id="CommandsToRunOnDisconnect">Commandes à exécuter lors de la déconnexion</string>

         <string id="CommandsToRunOnDisconnect_Desc">Liste de commandes à exécuter lorsqu'une session est déconnectée.</string>

         <string id="ShowDiskActivityIcon">Afficher l'icône d'activité du disque</string>

         <string id="ShowDiskActivityIcon_Desc">Affiche une icône d'activité du disque dans la barre d'état système. Utilise « Suivi du noyau NT de trace système » qui ne peut être utilisé que par un processus unique ; désactivez-le si nécessaire pour d'autres fins. La valeur par défaut est Activé.</string>

         <string id="SSO_retry_timeout">Délai de nouvelle tentative de l'authentification unique</string>

         <string id="SSO_retry_timeout_Desc">Spécifie la durée en millisecondes après laquelle l'authentification unique est de nouveau tentée. Définissez la valeur sur 0 pour désactiver la nouvelle tentative d'authentification unique. La valeur par défaut est de 5 000 millisecondes.</string>

         <string id="Win10PhysicalAgentAudioOption">Option audio pour une machine de poste de travail distant physique Windows 10 à session unique</string>

         <string id="Win10PhysicalAgentAudioOption_Desc">Spécifie quels périphériques audio utiliser dans la session de la machine de poste de travail distant physique Windows 10 pour Horizon. L'option par défaut consiste à utiliser des périphériques audio associés au point de terminaison Horizon Client.</string>

         <string id="WaitForLogoff">Attendre le délai d'expiration de la fermeture de session</string>

         <string id="WaitForLogoff_Desc">Spécifie le délai d'attente en secondes de la fermeture de la session précédente de l'utilisateur avant toute tentative d'ouverture de session. Définissez cette valeur sur 0 pour désactiver l'attente et provoquer un échec immédiat. La valeur par défaut est de 10 secondes.</string>

         <string id="UseClientAudioDevice">Utiliser des périphériques audio attachés au point de terminaison Horizon Client</string>

         <string id="UsePhysicalMachineAudioDevice">Utiliser des périphériques audio attachés à un point de terminaison de poste de travail distant physique Windows 10 pour Horizon</string>

         <string id="VDI_idle_time_till_disconnect">Durée d'inactivité jusqu'à la déconnexion (VDI)</string>

         <string id="VDI_idle_time_till_disconnect_Desc">Spécifie la durée après laquelle une session de poste de travail VDI se déconnecte en raison de l'inactivité de l'utilisateur.
Si ce paramètre est désactivé ou n'est pas configuré, les sessions de poste de travail VDI ne seront jamais déconnectées. La sélection de « Jamais » aura le même effet.
Remarque : si le pool de postes de travail ou la machine est configuré pour fermer automatiquement sa session après une déconnexion, ces paramètres seront respectés.</string>

         <string id="Accept_SSL_encr_framework_channel">Accepter le canal d'infrastructure chiffré SSL</string>

         <string id="Accept_SSL_encr_framework_channel_Desc">Accepter le canal d'infrastructure chiffré SSL

Activer : activez SSL, autorisez les clients hérités à se connecter sans SSL
Désactiver : désactivez SSL
Appliquer : activez SSL, refusez les connexions clientes héritées</string>

         <string id="Enable">Activer</string>

         <string id="Disable">Désactiver</string>

         <string id="Enforce">Appliquer</string>

         <string id="Allow_smartcard_local_access">Autoriser les applications à accéder aux lecteurs de carte à puce locaux</string>

         <string id="Allow_smartcard_local_access_Desc">Si cette option est activée, les applications pourront accéder à tous les lecteurs de carte à puce « locaux » même si la fonctionnalité Redirection de carte à puce est installée.

Ce paramètre ne s'applique pas à RDP ou aux hôtes RDSH lorsque le rôle Services Bureau à distance est activé.

Lorsque ce paramètre est activé, le poste de travail sera surveillé pour détecter la présence d'un lecteur local. Lorsqu'un lecteur local est détecté, la redirection de carte à puce désactive l'autorisation d'accès aux lecteurs locaux. La redirection reste désactivée jusqu'à ce qu'un utilisateur se connecte de nouveau à la session.

REMARQUE : lorsque l'accès local est activé, les applications ne peuvent plus accéder aux lecteurs distants présents sur le client.

Ce paramètre est désactivé par défaut.</string>

         <string id="Local_Reader_Name">Nom du lecteur local</string>

         <string id="Local_Reader_Name_Desc">Spécifie le nom d'un lecteur local à surveiller pour activer l'accès local. Par défaut, une carte à puce doit être insérée dans le lecteur pour activer l'accès local. Vous pouvez désactiver cette exigence avec le paramètre « Exiger une carte à puce insérée ».


La valeur par défaut est que cette fonctionnalité est activée pour tous les lecteurs</string>

         <string id="Require_an_inserted_smart_card">Exiger une carte à puce insérée</string>

         <string id="Require_an_inserted_smart_card_Desc">Si ce paramètre est activé, l'accès au lecteur local n'est activé que si une carte est insérée dans le lecteur local. Si ce paramètre est désactivé, l'accès local est activé tant qu'un lecteur local est détecté.

Ce paramètre est activé par défaut.</string>

         <string id="Disable_true_SSO">Désactiver True SSO</string>

         <string id="Disable_true_SSO_Desc">Désactive la fonctionnalité sur l'agent si cette option est activée</string>

         <string id="Cert_wait_timeout">Délai d'attente de certificat</string>

         <string id="Cert_wait_timeout_Desc">Spécifie le délai d'expiration des certificats pour arriver sur l'agent, en secondes</string>

         <string id="Min_key_size">Taille de clé minimale</string>

         <string id="Min_key_size_Desc">Clés de taille minimale utilisées</string>

         <string id="All_key_sizes">Toutes les tailles de clé</string>

         <string id="All_key_sizes_Desc">Toutes les tailles de clé pouvant être utilisées. 5 tailles au maximum peuvent être spécifiées. Exemple : 1024,2048,3072,4096</string>

         <string id="Keys_to_precreate">Nombre de clés à créer au préalable</string>

         <string id="Keys_to_precreate_Desc">Nombre de clés à créer au préalable sur un environnement RDSH</string>

         <string id="Cert_min_validity">Période de validité minimale requise pour un certificat</string>

         <string id="Cert_min_validity_Desc">Période de validité minimale (en minutes) requise pour un certificat réutilisé pour reconnecter un utilisateur</string>

         <string id="Enable_Unity_Touch">Activer Unity Touch</string>

         <string id="Enable_Unity_Touch_Desc">Cette stratégie spécifie si la fonctionnalité Unity Touch est activée sur Horizon Agent. La valeur par défaut de ce paramètre est qu'Unity Touch est activé.

Sous Windows 10, si Unity Touch est activé, la sous-stratégie spécifie si la prise en charge de l'application de plateforme Windows universelle (UWP) pour Unity Touch est activée sur Horizon Agent. Par défaut, la prise en charge d'UWP est activée pour Unity Touch. Si la stratégie Unity Touch n'est pas configurée, la prise en charge d'UWP pour Unity Touch est activée sur Windows 10.</string>

         <string id="Enable_system_tray_redir">Activer la redirection de la barre d'état système pour les applications hébergées</string>

         <string id="Enable_system_tray_redir_Desc">Cette stratégie spécifie si la redirection de la barre d'état système doit être activée lors de l'utilisation à distance d'applications hébergées. La valeur par défaut de ce paramètre est que la redirection de la barre d'état système est activée.</string>

         <string id="Enable_user_prof_customization">Activer la personnalisation de profil d'utilisateur pour les applications hébergées</string>

         <string id="Enable_user_prof_customization_Desc">Cette stratégie spécifie s'il faut exécuter la personnalisation de profil d'utilisateur lors de l'utilisation à distance d'applications hébergées. Cela générera un profil d'utilisateur, personnalisera le thème Windows et exécutera des applications de démarrage enregistrées. La valeur par défaut est désactivé.</string>

         <string id="AllowTinyOrOffscreenWindows">Envoyer des mises à jour pour les fenêtres vides ou en dehors de l'écran</string>

         <string id="AllowTinyOrOffscreenWindows_Desc">Cette stratégie spécifie si Horizon Client doit recevoir des mises à jour sur les fenêtres vides ou en dehors de l'écran. Lorsque cette valeur est désactivée, les informations sur les fenêtres inférieures à 2 x 2 pixels ou se trouvant en dehors de l'écran ne sont pas envoyées à Horizon Client. Elle est désactivée par défaut.</string>

         <string id="MinimalHookingModeEnabled">Limiter l'utilisation de hooks Windows</string>

         <string id="MinimalHookingModeEnabled_Desc">Cette stratégie désactive la plupart des hooks lors de l'utilisation à distance d'applications hébergées ou lors de l'utilisation d'Unity Touch. Elle est conçue pour être utilisée pour des applications ayant des problèmes de compatibilité ou de performances lorsque des hooks de niveau système d'exploitation sont définis. Par exemple, ce paramètre désactive l'utilisation de la plupart des hooks d'accessibilité et contenus dans un processus actifs de Windows. Cette stratégie est désactivée par défaut, ce qui signifie que nous utilisons tous nos hooks préférés par défaut.</string>

         <string id="LaunchAppWhenArgsAreDifferent">Ne lancer que de nouvelles instances d'applications hébergées si des arguments sont différents</string>

         <string id="LaunchAppWhenArgsAreDifferent_Desc">Cette stratégie contrôle le comportement lorsqu'une application hébergée est lancée, mais qu'une instance existante de l'application est déjà en cours d'exécution dans une session de protocole déconnectée. Si cette option est désactivée, l'instance existante de l'application sera activée. Si cette option est activée, l'instance existante de l'application ne sera activée que si les paramètres de ligne de commande correspondent. La valeur par défaut de cette stratégie est Désactivé.</string>

         <string id="Exclude_Vid_Pid">Exclure un périphérique Vid/Pid</string>

         <string id="Exclude_Vid_Pid_Desc">Excluez un périphérique avec des ID de fournisseur et de produit spécifiés de la transmission.

Syntaxe : {m|o}:&lt;vid-&lt;xxxx&gt;_pid-&lt;xxxx|*&gt;&gt;[;...]
merge-flag:
m = le paramètre client fusionnera avec le paramètre agent
o = le paramètre agent remplacera le paramètre client

Par exemple : m:vid-0781_pid-554c;vid-0781_pid-9999</string>

         <string id="Exclude_Vid_Pid_Rel">Exclure le périphérique Vid/Pid/Rel</string>

         <string id="Exclude_Vid_Pid_Rel_Desc">Excluez de la transmission un périphérique portant un ID de fournisseur, un ID de produit et un numéro de version spécifiés.

Syntaxe : {m|o}:&lt;vid-&lt;xxxx&gt;_pid-&lt;xxxx&gt;_rel-&lt;xxxx|*&gt;&gt;[;...]
merge-flag:
m = le paramètre client fusionnera avec le paramètre agent
o = le paramètre agent remplacera le paramètre client

Par exemple : m:vid-0781_pid-554c_rel-0100;vid-0781_pid-9999_rel-0010</string>

         <string id="Include_Vid_Pid">Inclure un périphérique Vid/Pid</string>

         <string id="Include_Vid_Pid_Desc">Incluez un périphérique avec des ID de fournisseur et de produit spécifiés pouvant être transmis.

Syntaxe : {m|o}:&lt;vid-&lt;xxxx&gt;_pid-&lt;xxxx|*&gt;&gt;[;...]
merge-flag:
m = le paramètre client fusionnera avec le paramètre agent
o = le paramètre agent remplacera le paramètre client

Par exemple : o:vid-0781_pid-554c;vid-0781_pid-9999</string>

         <string id="Include_Vid_Pid_Rel">Inclure le périphérique Vid/Pid/Rel</string>

         <string id="Include_Vid_Pid_Rel_Desc">Incluez un périphérique pouvant être transmis portant un ID de fournisseur, un ID de produit et un numéro de version spécifiés.

Syntaxe : {m|o}:&lt;vid-&lt;xxxx&gt;_pid-&lt;xxxx&gt;_rel-&lt;xxxx|*&gt;&gt;[;...]

merge-flag:
m = le paramètre client fusionnera avec le paramètre agent
o = le paramètre agent remplacera le paramètre client

Par exemple : o:vid-0781_pid-554c_rel-0100;vid-0781_pid-9999_rel-0010</string>

         <string id="Exclude_device_family">Exclure une famille de périphériques</string>

         <string id="Exclude_device_family_Desc">Excluez une famille de périphériques de la transmission.

Syntaxe : {m|o}:&lt;family-name&gt;[;...]

merge-flag:
m = le paramètre client fusionnera avec le paramètre agent
o = le paramètre agent remplacera le paramètre client

Par exemple : o:bluetooth;audio-in</string>

         <string id="Include_device_family">Inclure une famille de périphériques</string>

         <string id="Include_device_family_Desc">Incluez une famille de périphériques pouvant être transmise.

Syntaxe : {m|o}:&lt;family-name&gt;[;...]

merge-flag:
m = le paramètre client fusionnera avec le paramètre agent
o = le paramètre agent remplacera le paramètre client

Par exemple : m:storage;audio-out</string>

         <string id="Exclude_all">Exclure tous les périphériques</string>

         <string id="Exclude_all_Desc">Bloquez tous les périphériques, sauf s'ils sont inclus via la règle de filtre d'inclusion.

Par défaut : autoriser tous les périphériques</string>

         <string id="HidOpt_Include_Vid_Pid">Inclure le périphérique VID/PID d'optimisation HID</string>

         <string id="HidOpt_Include_Vid_Pid_Desc">Incluez un périphérique HID avec des ID de fournisseur et de produit spécifiés qui peuvent être optimisés.

Syntaxe : &lt;vid-&lt;xxxx&gt;_pid-&lt;xxxx|*&gt;&gt;[;...]

Par exemple : vid-056a_pid-0302;vid-046d_pid-c628</string>

         <string id="Exclude_Auto_Vid_Pid">Exclure le périphérique VID/PID de connexion automatiquement</string>

         <string id="Exclude_Auto_Vid_Pid_Desc">Excluez un périphérique avec des ID de fournisseur et de produit spécifiés du transfert automatique.

Syntaxe : {m|o}:&lt;vid-&lt;xxxx&gt;_pid-&lt;xxxx|*&gt;&gt;[;...]
merge-flag:
m = le paramètre client fusionnera avec le paramètre agent
o = le paramètre agent remplacera le paramètre client

Par exemple : m:vid-0781_pid-554c;vid-0781_pid-9999</string>

         <string id="Exclude_auto_device_family">Exclure la famille de périphériques de connexion automatiquement</string>

         <string id="Exclude_auto_device_family_Desc">Excluez une famille de périphériques du transfert automatique.

Syntaxe : {m|o}:&lt;family-name&gt;[;...]

merge-flag:
m = le paramètre client fusionnera avec le paramètre agent
o = le paramètre agent remplacera le paramètre client

Par exemple : o:storage;hid</string>

         <string id="Exclude_Vid_Pid_from_Split">Exclure un périphérique Vid/Pid du fractionnement</string>

         <string id="Exclude_Vid_Pid_from_Split_Desc">Excluez les périphériques de composant d'un périphérique composite spécifié par ses ID de fournisseur et de produit d'être traités comme des périphériques distincts pour le filtrage.

Syntaxe : {m|o}:&lt;vid-&lt;xxxx&gt;_pid-&lt;xxxx|*&gt;&gt;[;...]
merge-flag:
m = le paramètre client fusionnera avec le paramètre agent
o = le paramètre agent remplacera le paramètre client

Par exemple : o:vid-0781_pid-554c;vid-0781_pid-9999</string>

         <string id="Split_Vid_Pid_Device">Fractionner un périphérique Vid/Pid</string>

         <string id="Split_Vid_Pid_Device_Desc">Traitez les périphériques de composant d'un périphérique composite spécifié par ses ID de fournisseur et de produit comme des périphériques distincts pour le filtrage.

Syntaxe : {m|o}:&lt;vid-&lt;xxxx&gt;_pid-&lt;xxxx|*&gt;(exintf:xx[;exintf:xx])&gt;[;...]
merge-flag:
m = le paramètre client fusionnera avec le paramètre agent
o = le paramètre agent remplacera le paramètre client

Par exemple : o:vid-0781_pid-554c(exintf:01;exintf:02);vid-0781_pid-9999(exintf:02)</string>

         <string id="Allow_other_input_devices">Autoriser d'autres périphériques d'entrée</string>

         <string id="Allow_other_input_devices_Desc">Autorisez le transfert des périphériques d'entrée autres que les périphériques démarrables par HID, les claviers et les souris.

Par défaut : autoriser le transfert</string>

         <string id="Allow_Default">Autoriser - Paramètre client par défaut</string>

         <string id="Allow_Override">Autoriser - Remplacer le paramètre client</string>

         <string id="Disable_Default">Désactiver - Paramètre client par défaut</string>

         <string id="Disable_Override">Désactiver - Remplacer le paramètre client</string>

         <string id="Allow_HID_Bootable">Autoriser les périphériques HID démarrables</string>

         <string id="Allow_HID_Bootable_Desc">Autorisez le transfert des périphériques d'entrée démarrables (aussi appelés périphériques démarrables par HID).

Par défaut : autoriser le transfert</string>

         <string id="Allow_Audio_Input_devices">Autoriser les périphériques d'entrée audio</string>

         <string id="Allow_Audio_Input_devices_Desc">Autorisez le transfert des périphériques d'entrée audio.

Par défaut : autoriser le transfert</string>

         <string id="Allow_Audio_Output_devices">Autoriser les périphériques de sortie audio</string>

         <string id="Allow_Audio_Output_devices_Desc">Autorisez le transfert des périphériques de sortie audio.

Par défaut : bloquer le transfert</string>

         <string id="Allow_keyboard_mouse">Autoriser les périphériques clavier et souris</string>

         <string id="Allow_keyboard_mouse_Desc">Autorisez le transfert des claviers et des souris.

Par défaut : bloquer le transfert</string>

         <string id="Allow_Video_Devices">Autoriser les périphériques vidéo</string>

         <string id="Allow_Video_Devices_Desc">Autorisez le transfert de périphériques vidéo.

Par défaut : autoriser le transfert</string>

         <string id="Allow_Smart_Cards">Autoriser les périphériques de carte à puce</string>

         <string id="Allow_Smart_Cards_Desc">Autorisez le transfert des périphériques de carte à puce.

Par défaut : bloquer le transfert</string>

         <string id="Allow_Auto_Device_Splitting">Autoriser le fractionnement automatique de périphérique</string>

         <string id="Allow_Auto_Device_Splitting_Desc">Excluez les périphériques de composant d'un périphérique composite d'être traités automatiquement comme des périphériques distincts.</string>

         <string id="Proxy_default_ie_autodetect">Détection automatique par défaut du proxy</string>

         <string id="Proxy_default_ie_autodetect_Desc">Paramètre de connexion IE par défaut. Active Détecter automatiquement les paramètres dans Propriétés Internet, Paramètres de réseau local</string>

         <string id="Default_proxy_server">Serveur proxy par défaut</string>

         <string id="Default_proxy_server_Desc">Paramètre de connexion IE par défaut pour le serveur proxy. Spécifie le serveur proxy à utiliser dans Propriétés Internet, Paramètres de réseau local</string>

         <string id="Update_Java_Proxy">Définir le proxy pour l'applet Java</string>

         <string id="Update_Java_Proxy_Desc">Définition du proxy Java pour se connecter directement et contourner le paramètre du navigateur. Définition du proxy Java pour utiliser Transparence IP de client afin de rediriger le réseau pour l'applet Java. Définissez la restauration par défaut des paramètres du proxy Java aux paramètres d'origine.</string>

         <string id="Use_Client_IP">Utiliser Transparence IP de Client pour le proxy Java</string>

         <string id="Use_Direct_Connect">Utiliser la connexion directe pour le proxy Java</string>

         <string id="Use_Default">Utiliser la valeur par défaut pour le proxy Java</string>

         <string id="Enable_white_list">Activer la liste blanche</string>

         <string id="Enable_black_list">Activer la liste noire</string>

         <string id="Horizon_HTML5_FEATURES">Fonctionnalités HTML5 d'Horizon</string>

         <string id="Enable_HTML5_FEATURES">Activer les fonctionnalités HTML5 d'Horizon</string>

         <string id="Enable_HTML5_FEATURES_Desc">Activez les fonctionnalités HTML5 d'Horizon. Si cette stratégie est définie sur Activé, vous pouvez utiliser Redirection multimédia HTML5 d'Horizon, Redirection de géolocalisation, Redirection de navigateur ou Optimisation des supports pour Microsoft Teams.  Si cette stratégie est définie sur Désactivé, vous ne pouvez utiliser aucune des fonctionnalités HTML5 d'Horizon. Ce paramètre prendra effet à la prochaine ouverture de session.</string>

         <string id="HTML5FEATURES_Disable_AudoDetect_Intranet">Désactiver la détection automatique du réseau intranet</string>

         <string id="HTML5FEATURES_Disable_AudoDetect_Intranet_Desc">Lorsque la stratégie est définie sur Activé, les paramètres d'intranet « Inclure tous les sites locaux (intranet) non mentionnés dans d'autres zones » et « Inclure tous les sites qui n'utilisent pas de serveur proxy » seront désactivés lors de la prochaine connexion. Lorsque la stratégie est définie sur Désactivé, la zone intranet local IE n'est pas modifiée.

Remarque : il est nécessaire que cette stratégie soit définie sur « Activé » lorsque (1) le navigateur Edge est activé pour la redirection multimédia HTML5 d'Horizon ou (2) la redirection de géolocalisation est activée.</string>

         <string id="Horizon_HTML5MMR">Redirection multimédia HTML5 d'Horizon</string>

         <string id="Enable_HTML5_MMR">Activer la redirection multimédia HTML5 d'Horizon</string>

         <string id="Enable_HTML5_MMR_Desc">Activez la redirection multimédia HTML5 d'Horizon. Ce paramètre prendra effet à la prochaine ouverture de session.</string>

         <string id="HTML5MMRUrlList">Activez la liste d'URL pour la redirection multimédia HTML5 d'Horizon.</string>

         <string id="HTML5MMRUrlBlockList">Exclure la liste d'URL pour la redirection multimédia HTML5 d'Horizon.</string>

         <string id="HTML5MMRUrlList_Desc">Spécifie la liste d'URL permettant d'activer la redirection multimédia HTML5 d'Horizon. Spécifiez le modèle d'URL dans la colonne Nom de valeur, par ex. « https://www.youtube.com/* ». La colonne Valeur doit être vide. Elle est réservée pour une future utilisation.</string>

         <string id="HTML5MMRUrlBlockList_Desc">Spécifie la liste d'URL à exclure de la redirection multimédia HTML5 d'Horizon. Spécifiez le modèle d'URL dans la colonne Nom de valeur, par ex. « https://www.youtube.com/* ». La colonne « Valeur » est réservée pour une utilisation ultérieure et doit être vide.</string>

         <string id="HTML5MMR_Enable_Chrome">Activer le navigateur Chrome pour la redirection multimédia HTML5 d'Horizon</string>

         <string id="HTML5MMR_Enable_Chrome_Desc">Cette stratégie n'est utilisée que lorsque la redirection multimédia HTML5 d'Horizon est activée. Si elle n'est pas configurée, la valeur par défaut sera la même que pour « Activer la redirection multimédia HTML5 d'Horizon ».</string>

         <string id="HTML5MMR_Enable_Edge">Activer la version héritée du navigateur Microsoft Edge pour la redirection multimédia HTML5 d'Horizon</string>

         <string id="HTML5MMR_Enable_Edge_Desc">Cette stratégie n'est utilisée que lorsque la redirection multimédia HTML5 d'Horizon est activée. Si elle n'est pas configurée, la valeur par défaut sera la même que pour « Activer la redirection multimédia HTML5 d'Horizon ». </string>

         <string id="HTML5MMR_Enable_Edge_Chromium">Activer le navigateur Microsoft Edge (Chromium) pour la redirection multimédia HTML5 d'Horizon</string>

         <string id="HTML5MMR_Enable_Edge_Chromium_Desc">Cette stratégie n'est utilisée que lorsque la redirection multimédia HTML5 d'Horizon est activée. Si elle n'est pas configurée, la valeur par défaut sera la même que pour « Activer la redirection multimédia HTML5 d'Horizon ». </string>

         <string id="HTML5MMR_Auto_Adjust_Visual_Effect">Ajuster automatiquement l'effet visuel des fenêtres</string>

         <string id="HTML5MMR_Auto_Adjust_Visual_Effect_Desc">Cette stratégie est utilisée pour ajuster automatiquement l'effet visuel de la fenêtre pour la redirection multimédia HTML5 d'Horizon. S'il n'est ni configuré ni désactivé, l'effet visuel des fenêtres n'est pas ajusté automatiquement.</string>

         <string id="Horizon_GEO_REDIR">Redirection de géolocalisation Horizon</string>

         <string id="Enable_GEO_REDIR">Activer la redirection de géolocalisation Horizon</string>

         <string id="Enable_GEO_REDIR_Desc">Activez la fonctionnalité de redirection de géolocalisation Horizon. Ce paramètre prendra effet à la prochaine ouverture de session.</string>

         <string id="Enable_GEO_REDIR_For_Chrome">Activer la redirection de géolocalisation Horizon pour le navigateur Chrome</string>

         <string id="Enable_GEO_REDIR_For_Chrome_Desc">Activez la fonctionnalité de redirection de géolocalisation Horizon pour le navigateur Chrome. Ce paramètre prendra effet à la prochaine ouverture de session.</string>

         <string id="Enable_GEO_REDIR_For_Edge_Chromium">Activer la redirection de géolocalisation Horizon pour le navigateur Microsoft Edge (Chromium)</string>

         <string id="Enable_GEO_REDIR_For_Edge_Chromium_Desc">Activez la fonctionnalité de redirection de géolocalisation Horizon pour le navigateur Microsoft Edge (Chromium). Ce paramètre prendra effet à la prochaine ouverture de session.</string>

         <string id="GeoRedirUrlList">Activez la liste d'URL pour la redirection de géolocalisation Horizon.</string>

         <string id="GeoRedirUrlList_Desc">Spécifie la liste d'URL pour activer la fonctionnalité de redirection de géolocalisation. Spécifiez le modèle d'URL dans la colonne Nom de valeur, par ex. « https://www.youtube.com/* ». La colonne Valeur doit être vide. Elle est réservée pour une future utilisation. Cette liste d'URL est utilisée par (1) l'extension de redirection de géolocalisation Horizon des navigateurs Google Chrome et Microsoft Edge (Chromium) sur tous les environnements RDSH et VDI et par (2) le plug-in de redirection de géolocalisation Horizon pour Internet Explorer sur les environnements VDI RDSH et Windows 7.</string>

         <string id="GeoRedirDistanceDelta">Définir la distance minimale pour laquelle signaler des mises à jour d'emplacement</string>

         <string id="GeoRedirDistanceDelta_Desc">Spécifie la distance minimale entre une mise à jour d'emplacement dans le client et la dernière mise à jour signalée à l'agent, dont le nouvel emplacement doit être signalé à l'agent. Par défaut, la distance minimale utilisée est de 75 mètres.</string>

         <string id="Horizon_BROWSER_REDIR">Redirection de navigateur Horizon</string>

         <string id="Enable_BROWSER_REDIR">Activer la redirection de navigateur Horizon</string>

         <string id="Enable_BROWSER_REDIR_Desc">Activez la fonctionnalité de redirection d'Horizon Browser. Ce paramètre prendra effet à la prochaine ouverture de session. Notez que l'activation de la redirection d'Horizon Browser active également la redirection améliorée d'Horizon Browser.</string>

         <string id="Enable_BROWSER_REDIR_For_Chrome">Activer la redirection d'Horizon Browser pour le navigateur Chrome</string>

         <string id="Enable_BROWSER_REDIR_For_Chrome_Desc">Activez la fonctionnalité de redirection d'Horizon Browser pour le navigateur Chrome. Ce paramètre prendra effet à la prochaine ouverture de session.</string>

         <string id="Enable_BROWSER_REDIR_For_Edge_Chromium">Activer la fonctionnalité de redirection d'Horizon Browser pour le navigateur Microsoft Edge (Chromium)</string>

         <string id="Enable_BROWSER_REDIR_For_Edge_Chromium_Desc">Activez la fonctionnalité de redirection d'Horizon Browser pour le navigateur Microsoft Edge (Chromium). Ce paramètre prendra effet à la prochaine ouverture de session.</string>

         <string id="BrowserRedirFallbackWhitelistErr">Activer le secours automatique après une violation de la liste blanche</string>

         <string id="BrowserRedirFallbackWhitelistErr_Desc">Lorsque vous accédez à une URL à partir d'un onglet redirigé à l'aide de la redirection de navigateur en vous rendant dans la barre d'adresses personnalisée, dans la barre d'adresses du navigateur ou en naviguant à partir de l'onglet redirigé, si la nouvelle URL n'est pas répertoriée dans la liste d'URL de redirection de navigateur ou de redirection améliorée du navigateur, la nouvelle URL revient automatiquement au chargement sur l'agent lorsque ce paramètre est activé. À ce stade, si la nouvelle URL se trouve également dans la liste d'URL de redirection améliorée du navigateur, elle est redirigée à l'aide de cette dernière. Notez que la tentative d'accès à une URL non définie sous « Activer la liste d'URL pour la redirection d'Horizon Browser » ou sous « Activer la liste d'URL pour la redirection améliorée d'Horizon Browser » revient immédiatement à l'extraction et au rendu dans l'agent, indépendamment de ce paramètre.</string>

         <string id="BrowserRedirFetchFromServer">Activer l'extraction côté agent pour la fonctionnalité de redirection de navigateur</string>

         <string id="BrowserRedirFetchFromServer_Desc">Activer l'extraction du contenu du site Web à partir de l'agent au lieu du client lors de l'utilisation de la fonctionnalité de redirection de navigateur. Ce paramètre est désactivé par défaut.</string>

         <string id="BrowserRedirShowErrPage">Afficher une page contenant des informations d'erreur avant le secours automatique</string>

         <string id="BrowserRedirShowErrPage_Desc">Ce paramètre n'est activé que si l'option « Activer le secours automatique après une violation de la liste blanche » est activée et qu'il existe une violation de la liste blanche. Dans ce cas, si ce paramètre est activé, une page s'affichera avec un compte à rebours de 5 secondes après lequel l'onglet reviendra automatiquement à l'extraction et au rendu de l'URL qui a entraîné la violation dans l'agent. Si ce paramètre est désactivé, l'onglet reviendra directement au rendu côté agent, sans fournir à l'utilisateur un avertissement de 5 secondes.</string>

         <string id="BrowserRedirUrlList">Activer la liste d'URL pour la redirection d'Horizon Browser</string>

         <string id="BrowserRedirUrlList_Desc">Spécifie toutes les URL de la fonctionnalité de redirection de navigateur. Vous pouvez consulter ces URL en les entrant dans la barre d'adresses de Chrome ou dans la barre d'adresses personnalisée. Vous pouvez également consulter ces URL en y accédant à partir d'une autre URL de la liste ou à partir de n'importe quelle page rendue côté agent. Spécifiez le modèle d'URL dans la colonne Nom de valeur, par ex. « https://www.youtube.com/* ». La colonne Valeur doit être vide. Elle est réservée pour une future utilisation. Si une URL correspond à un modèle dans les listes d'URL de redirection de navigateur et de redirection améliorée du navigateur, cette dernière est prioritaire.</string>

         <string id="EnhBrowserRedirUrlList">Activer la liste d'URL pour la redirection améliorée d'Horizon Browser</string>

         <string id="EnhBrowserRedirUrlList_Desc">Spécifie toutes les URL de la fonctionnalité de redirection améliorée du navigateur. Vous pouvez consulter ces URL en les saisissant dans la barre d'adresses de Chrome, en y accédant à partir d'une autre URL de la liste ou à partir de n'importe quelle page rendue côté agent. Spécifiez le modèle d'URL dans la colonne Nom de valeur, par ex. « https://www.youtube.com/* ». La colonne Valeur doit être vide. Elle est réservée pour une future utilisation. Si une URL correspond à un modèle dans les listes d'URL de redirection de navigateur et de redirection améliorée du navigateur, cette dernière est prioritaire.</string>

         <string id="BrowserRedirNavUrlList">Activer la liste d'URL de navigation pour la redirection d'Horizon Browser</string>

         <string id="BrowserRedirNavUrlList_Desc">Spécifie les URL auxquelles un utilisateur est autorisé à accéder, en les entrant directement dans la barre d'adresses personnalisée ou en y accédant à partir d'une URL de l'autre liste. Vous ne pouvez pas consulter ces URL en les entrant directement dans la barre d'adresses de Chrome ou en y accédant à partir de n'importe quelle page rendue côté agent. Spécifiez le modèle d'URL dans la colonne Nom de valeur, par ex. « https://www.youtube.com/* ». La colonne Valeur doit être vide. Elle est réservée pour une future utilisation.</string>

         <string id="Horizon_WebRTC_REDIR_FEATURES">Fonctionnalités de redirection d'Horizon WebRTC</string>

         <string id="Enable_Teams_Redir">Activer l'optimisation des supports pour Microsoft Teams</string>

         <string id="Enable_Teams_Redir_Desc">Ce paramètre est utilisé pour activer ou désactiver l'optimisation de Microsoft Teams.

Lorsqu'Horizon Agent est installé, une clé de registre teamsEnabled est créée sur l'agent, ce qui active l'optimisation de Microsoft Teams.  Par défaut, l'utilisateur peut utiliser ou non l'optimisation de Microsoft Teams en configurant le paramètre « Optimisation des supports pour les applications basées sur WebRTC » dans Horizon Client.

Si cette stratégie est « Activée », l'optimisation de Microsoft Teams est activée. Si elle est activée et si l'option « Forcer l'optimisation de WebRTC côté client » est cochée, l'optimisation des supports Teams est forcée sur le point de terminaison et tout autre paramètre client, ou toute autre stratégie d'administration (par exemple : une stratégie d'utilisateur au niveau de Chrome pour le client Chrome), est ignoré. Si elle est activée et si l'option « Forcer l'optimisation de WebRTC côté client » est décochée, l'utilisateur peut utiliser ou non l'optimisation de Microsoft Teams en configurant le paramètre « Optimisation des supports pour les applications basées sur WebRTC » d'Horizon Client.

Si cette stratégie est « Désactivée », l'optimisation de Microsoft Teams est désactivée et ne peut pas être utilisée. Le paramètre « Optimisation des supports pour les applications basées sur WebRTC » d'Horizon Client n'a aucun effet.

Par défaut, cette stratégie est « Non configurée ». Toutefois, si la stratégie est modifiée, puis remplacée par « Non configurée », elle supprime la clé de registre teamsEnabled et l'optimisation de Microsoft Teams n'est pas utilisée.

Ce paramètre prendra effet à la prochaine ouverture de session.</string>

         <string id="Enable_Electron_App_Redir">Activer l'optimisation des supports pour les applications Electron générales</string>

         <string id="Enable_Electron_App_Redir_Desc">Ce paramètre permet d'activer ou de désactiver l'optimisation de l'application Electron.

Si l'option « Activée » ou « Non configurée » est cochée, l'optimisation de l'application Electron est activée. En outre, si vous souhaitez forcer l'utilisateur final à utiliser l'optimisation (si elle est prise en charge sur le point de terminaison), choisissez « Activée » et sélectionnez « Forcer l'optimisation de WebRTC côté client ». L'option « Non configurée » respecte le paramètre client s'il est disponible.
Détails :
Si cette option est activée et si l'option « Forcer l'optimisation de WebRTC côté client » est décochée, l'utilisateur peut utiliser ou non l'optimisation de l'application Electron en configurant le paramètre « Optimisation des supports pour les applications basées sur WebRTC » d'Horizon Client. Si cette option est cochée, l'optimisation des supports de l'application Electron est forcée sur le point de terminaison et le paramètre client, ou toute autre stratégie d'administration (par exemple, une stratégie d'utilisateur au niveau de Chrome pour le client Chrome), est ignoré.
Par défaut, le paramètre Optimisation de l'application Electron est « Non configurée », ce qui active l'optimisation de l'application Electron et permet à l'utilisateur de configurer le paramètre « Optimisation des supports pour les applications basées sur WebRTC ».
Si l'option « Désactivée » est cochée, l'optimisation de l'application Electron est désactivée et ne peut pas être utilisée. Le paramètre « Optimisation des supports pour les applications basées sur WebRTC » d'Horizon Client n'a aucun effet.

Ce paramètre prendra effet à la prochaine ouverture de session.</string>

         <string id="Horizon_WebRTC_SDK_WEBAPP">Prise en charge des applications Web du SDK de redirection Horizon WebRTC</string>

         <string id="WebRTC_SDK_Enable_Web_App_Redir">Activer l'optimisation des supports pour les applications Web</string>

         <string id="WebRTC_SDK_Enable_Web_App_Redir_Desc">Ce paramètre permet d'activer ou de désactiver l'optimisation des applications Web. Si l'option « Activée » ou « Non configurée » est cochée, l'optimisation des applications Web est activée.</string>

         <string id="WebRTC_SDK_Enable_Web_App_Redir_Chrome">Activer le navigateur Chrome pour la prise en charge des applications Web du SDK de redirection Horizon WebRTC</string>

         <string id="WebRTC_SDK_Enable_Web_App_Redir_Chrome_Desc">Cette stratégie n'est utilisée qu'avec l'activation de la prise en charge des applications Web du SDK de redirection Horizon WebRTC. Si elle n'est pas configurée, la valeur par défaut est la même que celle de l'activation de l'optimisation des supports pour les applications Web.</string>

         <string id="WebRTC_SDK_Enable_Web_App_Redir_Edge">Activer le navigateur Chromium Edge pour la prise en charge des applications Web du SDK de redirection Horizon WebRTC</string>

         <string id="WebRTC_SDK_Enable_Web_App_Redir_Edge_Desc">Cette stratégie n'est utilisée qu'avec l'activation de la prise en charge des applications Web du SDK de redirection Horizon WebRTC. Si elle n'est pas configurée, la valeur par défaut est la même que celle de l'activation de l'optimisation des supports pour les applications Web.</string>

         <string id="WebRTC_SDK_Web_App_Redir_Allow_List">Activer la liste d'URL pour la prise en charge des applications Web du SDK de redirection Horizon WebRTC</string>

         <string id="WebRTC_SDK_Web_App_Redir_Allow_List_Desc">Spécifie toutes les URL pour la prise en charge des applications Web du SDK de redirection Horizon WebRTC. Vous pouvez consulter ces URL en les entrant dans la barre d'adresses de Chrome. Vous pouvez également consulter ces URL en y accédant à partir d'une autre URL de la liste ou à partir de n'importe quelle page rendue côté agent. Spécifiez le modèle d'URL dans la colonne Nom de valeur, par ex. « https://www.youtube.com/* ». La colonne Valeur doit être vide. Elle est réservée pour une future utilisation.</string>

         <string id="Enable_AEC_Teams_Redir">Activer l'annulation de l'écho acoustique du logiciel pour l'optimisation des supports pour Microsoft Teams</string>

         <string id="Enable_AEC_Teams_Redir_Desc">Ce paramètre est utilisé pour configurer l'annulation de l'écho acoustique (AEC) du logiciel pour l'optimisation des supports pour Microsoft Teams.

S'il est défini sur Activé, l'AEC est activée dans le logiciel. Cochez « Utiliser l'algorithme AEC recommandé » pour optimiser la qualité audio et les performances. Décochez « Utiliser l'algorithme AEC recommandé » pour utiliser un algorithme AEC qui utilise moins le CPU, mais compromet la qualité audio. Cette option est utile pour les processeurs bas de gamme dotés d'une puissance de calcul faible des nombres en virgule flottante. L'utilisation de l'algorithme AEC recommandé est fortement recommandée et est idéale dans la majorité des cas.

Si elle est définie sur Désactivé, l'AEC est désactivée dans le logiciel et n'est plus utilisée.

Si elle est définie sur Non configuré, l'AEC est activée dans le logiciel à l'aide de l'algorithme recommandé. Lorsque vous utilisez le client Windows, l'AEC du logiciel est utilisée si celle du matériel n'est pas disponible. Si l'AEC du matériel est disponible (par exemple, si le casque dispose d'une AEC intégrée), celle du logiciel n'est pas utilisée. Lors de l'utilisation d'un client autre que Windows, l'AEC du logiciel est utilisée, indépendamment de la disponibilité de celle du matériel.</string>

         <string id="Enable_Datachannel_Teams_Redir">Activer le canal de données pour l'optimisation des supports pour Microsoft Teams</string>
         <string id="Enable_Datachannel_Teams_Redir_Desc">Ce paramètre est utilisé pour activer ou désactiver le canal de données pour l'optimisation des supports pour Microsoft Teams.

Si vous sélectionnez l'option Activé, vous pouvez utiliser le canal de données pour l'optimisation des supports pour Microsoft Teams, et les fonctionnalités qui nécessitent le canal de données sont disponibles (par exemple, des légendes en direct).

Si vous sélectionnez l'option Désactivé, vous ne pouvez pas utiliser le canal de données pour l'optimisation des supports pour Microsoft Teams, et les fonctionnalités qui nécessitent le canal de données ne sont pas disponibles.

Si vous sélectionnez l'option Non configuré, le canal de données est activé.</string>

         <string id="Video_Cpu_Overuse_Threshold">Configurer le seuil de surutilisation du CPU</string>

         <string id="Video_Cpu_Overuse_Threshold_Desc"> Si l'utilisation du CPU dépasse le seuil, la résolution vidéo envoyée sera réduite, ce qui diminuera l'utilisation du CPU client. Le seuil par défaut est de 85. Pour réduire le CPU client lors des appels vidéo, définissez cette stratégie sur Activé avec une valeur inférieure à 85. Définissez cette stratégie sur Désactivé ou Non configuré pour utiliser le seuil par défaut de 85. Pour ne pas détecter une utilisation excessive de CPU, définissez cette stratégie sur Activé avec une valeur de 0. Ce paramètte s'applique à l'ouverture de session suivante.</string>

         <string id="Enable_Sharing_Client_Screen_InAppSession">Lors de l'utilisation de l'application Microsoft Teams comme application publiée, autoriser le partage de l'écran du poste de travail client</string>

         <string id="Enable_Sharing_Client_Screen_InAppSession_Desc">Lors de l'utilisation de l'application Microsoft Teams comme application publiée, la fonctionnalité de partage d'écran partage l'écran du poste de travail client. Désactivez cette stratégie pour désactiver la fonctionnalité de partage d'écran lors de l'utilisation de Microsoft Teams comme application publiée. Si la stratégie est activée ou qu'elle n'est pas configurée, vous pouvez partager l'écran du poste de travail client.</string>

         <string id="Enable_E911">Activer E911 pour Microsoft Teams</string>

         <string id="Enable_E911_Desc">Lorsque Microsoft Teams s'exécute en mode optimisé, le client envoie des données E911 à Microsoft. Pour désactiver le partage de données E911 avec Microsoft, sélectionnez « Désactivé ». Si l'option « Activé » ou « Non configuré » est sélectionnée, les données E911 du client sont partagées avec Microsoft.</string>

         <string id="Enable_HID">Activer l'utilisation du bouton des périphériques HID du client pour Microsoft Teams</string>

         <string id="Enable_HID_Desc">Bien que Microsoft Teams s'exécute en mode optimisé, l'utilisateur peut utiliser le bouton des périphériques HID du client pour interagir avec Microsoft Teams. Pour désactiver la prise en charge des périphériques HID du client, sélectionnez « Désactivé ». Si l'option est « Activé » ou « Non configuré », la prise en charge des périphériques HID du client est autorisée.</string>

         <string id="Enable_Webrtc_Appshare">Activer le partage d'applications individuelles pour Microsoft Teams</string>

         <string id="Enable_Webrtc_Appshare_Desc">Bien que Microsoft Teams s'exécute en mode optimisé, cette option permet à l'utilisateur de partager une application individuelle. Pour désactiver le partage d'applications, sélectionnez « Désactivé ». Si l'option « Activé » ou « Non configuré » est cochée, le partage d'applications est autorisé.</string>

         <string id="Enable_Webrtc_Appshare_Givecontrol">Activer l'attribution du contrôle pour le partage d'applications individuelles de Microsoft Teams</string>

         <string id="Enable_Webrtc_Appshare_Givecontrol_Desc">Bien que Microsoft Teams s'exécute en mode optimisé, cette option permet à l'utilisateur de donner le contrôle d'une application individuelle partagée. Pour désactiver l'attribution du contrôle lors du partage d'applications individuelles, définissez cette stratégie sur « Désactivé ». Si l'option « Activé » ou « Non configuré » est activée, l'attribution du contrôle est autorisé lors du partage d'applications individuelles.</string>

         <string id="CustomBackgroundImages">Images d'arrière-plan personnalisées de Microsoft Teams</string>

         <string id="Enable_Background_Effects">Activer les effets d'arrière-plan pour Microsoft Teams</string>

         <string id="Enable_Background_Effects_Desc">Bien que Microsoft Teams s'exécute en mode optimisé, les utilisateurs peuvent sélectionner un arrière-plan virtuel pour les appels et les réunions. Pour désactiver la prise en charge des effets d'arrière-plan, sélectionnez Désactivé. Si l'option Activé ou Non configuré est sélectionnée, la prise en charge des effets d'arrière-plan est autorisée.</string>

         <string id="ForceEnableCustomBackgroundImages">Forcer l'activation ou la désactivation de la fonctionnalité d'images d'arrière-plan personnalisées pour Microsoft Teams</string>

         <string id="ForceEnableCustomBackgroundImagesDesc">Lorsque Microsoft Teams s'exécute en mode optimisé, les utilisateurs peuvent appliquer des images d'arrière-plan personnalisées lors des appels et des réunions. Pour désactiver la prise en charge des images d'arrière-plan personnalisées, sélectionnez « Désactivé ». Pour forcer les utilisateurs à utiliser uniquement des images d'arrière-plan personnalisées et empêcher l'application des images standard fournies dans l'interface utilisateur « effets d'arrière-plan » de Microsoft Teams, sélectionnez « Activé ». Si l'option « Non configuré » est définie, les utilisateurs peuvent basculer entre l'utilisation d'images d'arrière-plan personnalisées et les images d'interface utilisateur fournies par Microsoft Teams à leur propre discrétion.</string>

         <string id="CustomBackgroundImagesFolderPath">Spécifiez le dossier d'images d'arrière-plan personnalisées pour Microsoft Teams</string>

         <string id="CustomBackgroundImagesFolderPathDesc">Lorsque Microsoft Teams s'exécute en mode optimisé, les utilisateurs peuvent appliquer des images d'arrière-plan personnalisées sélectionnées dans un dossier d'images chargées par l'administrateur. Si vous sélectionnez Désactivé ou Non configuré, le dossier vers lequel les images doivent être chargées est C:\Program Files\Common Files\Omnissa\Remote Experience\x64\MSTeamsBackgroundImages. Pour utiliser un autre dossier, sélectionnez « Activé » et spécifiez le chemin d'accès au dossier dans la zone de texte du dossier d'images d'arrière-plan personnalisées, par exemple « C:\Users\<USER>\CustomBackgroundImagesFolder ».</string>

         <string id="CustomBackgroundDefaultImageName">Choisir une image d'arrière-plan personnalisée par défaut à appliquer en cas d'erreur de l'utilisateur</string>

         <string id="CustomBackgroundDefaultImageNameDesc">Spécifiez un nom d'image personnalisé par défaut à appliquer si l'utilisateur laisse la valeur de registre imageName vide ou saisit un nom d'image personnalisé non valide lorsque la fonctionnalité d'image d'arrière-plan personnalisée est activée.</string>

         <string id="Disable_Mirrored_Video">Désactiver l'aperçu automatique mis en miroir dans Microsoft Teams</string>

         <string id="Disable_Mirrored_Video_Desc">Par défaut, la vidéo avec aperçu automatique est mise en miroir pour Microsoft Teams en mode optimisé. Si vous définissez cette option, la vidéo mise en miroir est désactivée.</string>
         
         <string id="Enable_Webrtc_ProxyProbeUrl">Utilisez l'URL de sonde proxy personnalisée pour détecter le serveur proxy opérationnel.</string>

         <string id="Enable_Webrtc_ProxyProbeUrl_Desc">Lorsque plusieurs serveurs proxy sont configurés, spécifiez l'URL de sonde proxy personnalisée pour sonder le serveur proxy opérationnel et utilisez-la dans l'appel Microsoft Teams. Par exemple, https://teams.microsoft.com</string>

         <string id="Horizon_AppTap_Config">Configuration d'Horizon AppTap</string>

         <string id="ProcessIgnoreList">Processus à ignorer lors de la détection d'une session d'application vide</string>

         <string id="ProcessIgnoreList_Desc">Spécifie la liste des processus à ignorer lors de la détection des sessions d'application vides. Vous pouvez spécifier un nom de fichier de processus ou un chemin d'accès complet. Ces valeurs sont évaluées sans tenir compte de la casse. Les variables d'environnement ne sont pas autorisées dans les chemins d'accès. Les chemins d'accès réseau UNC sont autorisés (par exemple, \\Omnissa\temp\app.exe).</string>

         <string id="VDI_disconnect_time_till_logoff">Limite de durée de session déconnectée (VDI)</string>

         <string id="VDI_disconnect_time_till_logoff_Desc">Spécifie la durée après laquelle une session de poste de travail VDI déconnectée se ferme automatiquement.
Si « Jamais » est sélectionné, les sessions de poste de travail VDI déconnectées sur cette machine ne se ferment jamais. Si « Immédiatement » est sélectionné, les sessions déconnectées sont immédiatement fermées.

Un paramètre similaire existe dans Administrateur Horizon Connection Server. Il se trouve dans Paramètres du pool de postes de travail et s'appelle « Fermeture de session automatique après la déconnexion ». Si ce paramètre et le paramètre Administrateur Horizon Connection Server sont configurés, la valeur sélectionnée ici est prioritaire.
Par exemple, si vous sélectionnez « Jamais », une session déconnectée (sur cette machine) ne se fermera jamais, quel que soit le réglage du paramètre Administrateur Horizon Connection Server.</string>

         <string id="RDS_idle_time_till_disconnect">Durée d'inactivité RDS jusqu'à la déconnexion</string>

         <string id="RDS_idle_time_till_disconnect_Desc">Spécifie la durée après laquelle une session inactive des services Bureau à distance se déconnecte automatiquement.
Si le paramètre Jamais est sélectionné, les sessions des services Bureau à distance sur cette machine ne se déconnectent jamais.</string>

         <string id="RDS_disconnect_time_till_logoff">Durée de déconnexion RDS jusqu'à la fermeture de session</string>

         <string id="RDS_disconnect_time_till_logoff_Desc">Spécifie la durée après laquelle une session des services Bureau à distance déconnectée se ferme automatiquement.
Si le paramètre Jamais est sélectionné, les sessions des services Bureau à distance déconnectées sur cette machine ne se ferment jamais.</string>

         <string id="RDS_active_time_till_disconnect">Durée de connexion RDS jusqu'à la déconnexion</string>

         <string id="RDS_active_time_till_disconnect_Desc">Spécifiez la durée maximale pendant laquelle une session des services Bureau à distance peut être active avant d'être automatiquement déconnectée.
Si le paramètre Jamais est sélectionné, les sessions des services Bureau à distance sur cette machine ne se déconnectent jamais.</string>

         <string id="RDS_end_session_time_limit">Session de fin RDS lorsque la limite de temps est atteinte</string>

         <string id="RDS_end_session_time_limit_Desc">Spécifie s'il convient de mettre fin à une session des services Bureau à distance expirée plutôt que de la déconnecter.
Si le paramètre est défini, la session se termine (l'utilisateur est déconnecté et la session est supprimée du serveur) une fois que les limites de temps pour les sessions actives ou inactives ont été atteintes. Par défaut, les sessions des services Bureau à distance sont déconnectées après avoir atteint leurs limites de temps.</string>

         <string id="RDS_threshold_connecting_session">Seuil de sessions de connexion</string>

         <string id="RDS_threshold_connecting_session_Desc">Spécifie le nombre maximal de sessions pouvant se connecter simultanément à la machine RDSH, à l'exception des sessions de reconnexion.

Si cette option est activée, la valeur du seuil de sessions est initialement définie sur 20, mais doit être modifiée en fonction du cas d'utilisation. Si 0 est sélectionné, le seuil de sessions de connexion est désactivé.

Cette stratégie est désactivée par défaut. Si elle n'est pas configurée, le seuil de sessions de connexion est alors désactivé.</string>

         <string id="RDS_threshold_load_index">Seuil d'indice de charge</string>

         <string id="RDS_threshold_load_index_Desc">Spécifie l'indice de charge minimal auquel la machine RDSH commencera à refuser les ouvertures de session, à l'exception des sessions de reconnexion.

Si cette option est activée, la valeur du seuil de charge est initialement définie sur 0, mais doit être modifiée en fonction du cas d'utilisation. Si 0 est sélectionné, le seuil d'indice de charge est désactivé.

Cette stratégie est désactivée par défaut. Si la stratégie n'est pas configurée, le seuil d'indice de charge est alors désactivé.</string>

         <string id="Prewarm_disconnect_time_till_logoff">Limite de temps de la session de préchauffage</string>

         <string id="Prewarm_disconnect_time_till_logoff_Desc">Spécifie la durée après laquelle une session de préchauffage sera automatiquement fermée.</string>

         <string id="EnableUWPOnRDSH">Activer la prise en charge UWP sur les plates-formes RDSH</string>

         <string id="EnableUWPOnRDSH_Desc">Cette stratégie contrôle si les applications UWP peuvent être analysées et lancées sur des batteries de serveurs RDSH avec une version du système d'exploitation qui prend en charge les applications UWP. Cette stratégie ne s'applique pas aux plateformes de système d'exploitation de poste de travail, telles que l'accès distant à l'application VDI. Lorsqu'elles sont activées, les applications UWP peuvent être utilisées comme des applications hébergées depuis des batteries de serveurs RDSH. Vous devez redémarrer le service wsnm ou redémarrer le serveur RDSH pour que le GPO prenne effet. Pour en savoir plus sur les plates-formes prises en charge et déterminer si ce paramètre est activé ou désactivé par défaut, reportez-vous à la documentation d'Omnissa.</string>

        <string id="HandleLegalNoticeInWindow">Rediriger les messages de mentions légales en tant que fenêtre</string>

        <string id="HandleLegalNoticeInWindow_Desc">Lorsque ce paramètre est activé, cette stratégie redirige les mentions légales vers Horizon Client dans une fenêtre avec la taille spécifiée. La largeur et la hauteur de cette stratégie sont spécifiées en pixels. Pour les moniteurs DPI élevés, les tailles sont multipliées en fonction du DPI. Cette fonctionnalité est uniquement prise en charge pour les applications hébergées RDSH.
Cette stratégie est désactivée par défaut. Vous devez redémarrer le serveur RDSH et Horizon Client pour que le GPO s'applique.</string>

        <string id="TIME_NEVER">Jamais</string>

         <string id="TIME_1MIN">1 minute</string>

         <string id="TIME_5MIN">5 minutes</string>

         <string id="TIME_10MIN">10 minutes</string>

         <string id="TIME_15MIN">15 minutes</string>

         <string id="TIME_30MIN">30 minutes</string>

         <string id="TIME_1HR">1 heure</string>

         <string id="TIME_2HR">2 heures</string>

         <string id="TIME_3HR">3 heures</string>

         <string id="TIME_6HR">6 heures</string>

         <string id="TIME_8HR">8 heures</string>

         <string id="TIME_10HR">10 heures</string>

         <string id="TIME_12HR">12 heures</string>

         <string id="TIME_18HR">18 heures</string>

         <string id="TIME_1D">1 jour</string>

         <string id="TIME_2D">2 jours</string>

         <string id="TIME_3D">3 jours</string>

         <string id="TIME_4D">4 jours</string>

         <string id="TIME_5D">5 jours</string>

         <string id="TIME_1W">1 semaine</string>

         <string id="TIME_IMMEDIATELY">Immédiatement</string>

         <string id="EnableBatStatRedir">Activer la redirection de l'état de la batterie</string>

         <string id="EnableDisplayNetworkState">Activer l'affichage de l'état du réseau</string>
         <string id="EnableDisplayNetworkStateExplain">Ce paramètre permet de configurer l'affichage ou non des messages d'état du réseau sur l'interface utilisateur d'Horizon Client. Lorsque ce paramètre est activé, l'utilisateur final reçoit une notification de l'état du réseau si la connexion réseau est de mauvaise qualité. Lorsque ce paramètre est désactivé, l'utilisateur final ne reçoit aucune notification de l'état du réseau si la connexion réseau est de mauvaise qualité. Cette propriété est activée par défaut.</string>

         <string id="EnableBatStatRedir_Desc">Cette stratégie contrôle si la redirection de l'état de la batterie est activée. Lorsque cette stratégie n'est pas configurée, la redirection de l'état de la batterie est activée.</string>
         <string id="Horizon_WaterMark">Filigrane</string>
         <string id="Horizon_Watermark_Config">Configuration du filigrane</string>
         <string id="Desktop_Watermark_Configuration_Desc">Ce paramètre permet de configurer un filigrane afin qu'il s'affiche sur votre poste de travail virtuel. Dans la zone Texte, vous pouvez définir les éléments affichés dans le filigrane. Les options sont les suivantes :

%ViewClient_IP_Address%
%ViewClient_Broker_UserName%
%ViewClient_Broker_DomainName%
%COMPUTERNAME%
%USERDOMAIN%
%USERNAME%
%ViewClient_ConnectTime%   - Date en mois/jour/année
%ViewClient_ConnectTicks%  - Heure en heure:minute:seconde

Voici un exemple de « Texte » :
%USERDOMAIN%\%USERNAME%
%COMPUTERNAME% on %ViewClient_ConnectTime%
%ViewClient_IP_Address%

La limite de caractères du « Texte » est de 256 caractères. Il est limité à 1 024 caractères après le développement.

L'option Disposition de l'image spécifie la disposition du filigrane. Nous prenons en charge les options Mosaïque, Multiple et Centre. L'option Multiple place le filigrane au centre et dans chaque coin. Pour les sessions APP, ce paramètre est ignoré et la disposition est toujours Mosaïque.
L'option Rotation du texte permet de sélectionner un angle de rotation pour le texte du filigrane.
L'option Opacité permet de choisir la transparence du texte.
L'option Marge spécifie la distance entre le filigrane et le bord de l'écran du poste de travail virtuel. Elle s'applique uniquement à la disposition de la mosaïque.
L'option « Couleur du texte » spécifie la couleur du texte du filigrane utilisant des valeurs de couleur RVB séparées par des espaces en décimales et le contour du texte est rendu dans une couleur de contraste. Par défaut, le texte est rendu en blanc avec un contour noir.
L'option « Taille de police » spécifie la taille du texte en filigrane. La taille de police par défaut s'applique lorsque cette valeur est de 0.
Le paramètre Intervalle d'actualisation spécifie l'intervalle d'actualisation du filigrane en secondes. Lorsque vous spécifiez 0, cela désactive la mise à jour du filigrane. La valeur maximale est de 86 400 secondes (24 heures).
</string>
         <string id="Tile">Mosaïque</string>
         <string id="Multiple">Multiple</string>
         <string id="Center">Centre</string>
         <string id="TextColor">Couleur du texte</string>
         <string id="FontSize">Taille de police</string>
         <string id="RefreshInterval">Intervalle d'actualisation</string>
         <string id="BlockScreenCapture">Blocage des captures d'écran</string>
         <string id="BlockScreenCapture_Desc">Détermine si l'utilisateur final peut prendre des captures d'écran de son poste de travail virtuel ou de son application distante à partir de son point de terminaison. Ce paramètre ne peut être appliqué que sur Horizon Client pour Windows et Horizon Client pour Mac 2106 et versions ultérieures. La valeur par défaut est Désactivé, ce qui permet à l'utilisateur final de prendre des captures d'écran depuis son périphérique.

Activer : empêche les utilisateurs finaux de prendre des captures d'écran du poste de travail virtuel ou des applications virtuelles à partir de leurs périphériques Windows ou macOS.

Désactiver : autorise les utilisateurs finaux à prendre des captures d'écran à partir de leur point de terminaison.


« Autoriser l'enregistrement d'écran pour Horizon Mac Client » Détermine si l'utilisateur final peut effectuer un enregistrement d'écran de son poste de travail virtuel ou de son application distante à partir de son point de terminaison lorsque le GPO « Blocage des captures d'écran » est activé. Vous ne pouvez appliquer ce paramètre que sur Horizon Client 2309 pour Mac et versions ultérieures. La valeur par défaut est décochée, l'utilisateur final n'est donc pas autorisé à effectuer un enregistrement d'écran à partir de son périphérique.

Cochée : permet aux utilisateurs finaux d'effectuer un enregistrement d'écran du poste de travail virtuel ou des applications virtuelles à partir de leurs périphériques macOS.

Décochée : empêche les utilisateurs finaux de prendre l'enregistrement d'écran à partir de leurs périphériques macOS.</string>
         <string id="BlockThumbnailRepresentationWhenMinimized">Bloquer la représentation d'une miniature lorsqu'elle est réduite</string>
         <string id="BlockThumbnailRepresentationWhenMinimized_Desc">Détermine si le survol de la miniature du poste de travail distant affiche le contenu du poste de travail distant lorsque sa fenêtre est réduite.
Lorsque ce paramètre est activé, l'icône de l'application Horizon Client s'affiche au lieu du contenu du poste de travail distant pour la miniature de la fenêtre et l'aperçu en direct lorsque sa fenêtre est réduite.
Lorsqu'il est désactivé ou qu'il n'est pas configuré, le snapshot du dernier poste de travail distant avant d'être réduit s'affiche pour sa miniature de fenêtre et son aperçu en direct. Ce GPO ne prend effet que sur les points de terminaison Windows.</string>
         <string id="ScreenCaptureForMediaOffloaded">Capture d'écran pour la solution déchargée du support</string>
         <string id="ScreenCaptureForMediaOffloaded_Desc">Autorisez l'utilisateur final à prendre une capture d'écran pour le poste de travail de l'agent VDI lorsque la session multimédia est déchargée vers les points de terminaison.</string>
         <string id="AntiKeyLogger">Blocage des enregistreurs de frappe</string>
         <string id="AntiKeyLogger_Desc">Détermine si le point de terminaison chiffre la communication entre le clavier et Horizon Client pour éviter les programmes malveillants d'enregistrement des frappes sur le point de terminaison. La connexion initiale à Horizon Server est toujours protégée, quel que soit le paramètre GPO sur la machine virtuelle. Après l'authentification initiale, ce paramètre détermine si toutes les saisies sont chiffrées sur le point de terminaison. Ce paramètre ne peut être appliqué que sur Horizon Client 2111 pour Mac et Horizon Client 2203 pour Windows ou version ultérieure. La valeur par défaut est désactivée.

Activer : chiffrez toutes les frappes entre le clavier et Horizon Client.

Désactiver : les frappes sont communiquées normalement sur le point de terminaison.</string>
         <string id="BlockSendInput">Blocage des séquences de touches synthétiques</string>
         <string id="BlockSendInput_Desc">Détermine si le point de terminaison bloque les scripts qui automatisent les séquences de touches du point de terminaison dans une application ou un poste de travail virtuel. La connexion initiale à Horizon Server est toujours protégée, quel que soit le paramètre GPO sur la machine virtuelle. Après l'authentification initiale, ce paramètre détermine si toutes les séquences de touches synthétiques sur le point de terminaison sont bloquées. Vous ne pouvez appliquer ce paramètre que sur Horizon Client 2312 pour Windows ou version ultérieure. La valeur par défaut est désactivée.

Si l'option « Blocage des enregistreurs de frappe » n'est pas activée, ce paramètre n'a aucun effet.

Activer : bloquez toutes les séquences de touches synthétiques à partir du point de terminaison dans le poste de travail virtuel ou les applications virtuelles.

Désactiver : Horizon Client transfère les touches synthétiques comme d'habitude.</string>
         <string id="AllowFIDO2AuthenticatorAccess">Autoriser l'accès à l'authentificateur FIDO2</string>
         <string id="AllowFIDO2AuthenticatorAccess_Desc">Détermine si les applications du poste de travail distant peuvent accéder aux authentificateurs FIDO2 du point de terminaison. Lorsque ce paramètre est désactivé, les applications du poste de travail distant ne sont pas autorisées à accéder aux authentificateurs FIDO2 du point de terminaison. Lorsque ce paramètre est activé ou qu'il n'est pas configuré, les applications du poste de travail distant sont autorisées à accéder aux authentificateurs FIDO2 du point de terminaison.</string>
         <string id="FIDO2AllowList">Liste autorisée de FIDO2</string>
         <string id="FIDO2AllowList_Desc">Liste d'applications pouvant accéder aux authentificateurs FIDO2 du point de terminaison.

La syntaxe est la suivante :
   appname1.exe;appname2.exe

Lorsque ce paramètre n'est pas configuré ou qu'il est désactivé, la liste par défaut est utilisée. La liste par défaut est la suivante :
   chrome.exe;firefox.exe;msedge.exe</string>

         <string id="WaitForHybridJoin">Configurer l'attente de la jonction hybride</string>

         <string id="WaitForHybridJoin_Desc">Cet objet de stratégie de groupe (GPO) contrôle le comportement de l'agent par rapport au processus de jonction Microsoft Entra ID hybride. Il détermine si l'agent doit attendre ou non la fin du processus de jonction hybride avant de pouvoir traiter les demandes de poste de travail ou d'application.

Désactivé ou Non configuré : lorsque ce paramètre est désactivé ou non configuré, l'agent n'attend pas la fin du processus de jonction hybride. Cela signifie que l'agent peut commencer à traiter les demandes immédiatement, potentiellement avant l'intégration entière de la machine à Entra ID.

Activé : lorsque ce paramètre est activé, l'agent attend que la machine termine le processus de jonction hybride avec Entra ID. Ce n'est qu'une fois ce processus terminé que l'agent se marque comme étant DISPONIBLE, indiquant qu'il est prêt à traiter les demandes de poste de travail ou d'application.

L'activation de cette fonctionnalité est essentielle pour s'assurer que l'agent est entièrement intégré à Entra ID avant de commencer à traiter les demandes. Cette intégration est nécessaire pour les fonctionnalités telles que Single Sign-On (SSO) dans les ressources Azure/Office et pour reconnaître le périphérique dans Entra ID à des fins de gestion. Cependant, il est important de noter que l'activation de cette fonctionnalité peut entraîner un retard important dans la disponibilité des machines, car l'agent attend la fin du processus de jonction hybride.
         </string>

         <string id="IpPrefix">Configurer le sous-réseau qu'Horizon Agent utilise</string>

         <string id="IpPrefixDesc">Lorsque vous installez Horizon Agent sur une machine virtuelle qui possède plusieurs cartes réseau, vous devez configurer le sous-réseau qu'Horizon Agent utilise. Le sous-réseau détermine quelle adresse réseau est fournie par Horizon Agent au Serveur de connexion ou à l'instance de service de connexion pour les connexions de protocole client.

La syntaxe est :
   n.n.n.n/m

Dans cet exemple, n.n.n.n est le sous-réseau TCP/IP et m est le nombre de bits dans le masque de sous-réseau.

Exemple de valeur :
   ***********/21

Dans cet exemple, seules les adresses IP dans la plage comprise entre *********** et ************* sont acceptées pour être utilisées par Horizon Agent.
         </string>

      </stringTable>

      <presentationTable>
         <presentation id="MaxCollaboratorCount">
            <decimalTextBox refId="MaxCollaboratorCount_DB" defaultValue="5">Maximum</decimalTextBox>
         </presentation>

         <presentation id="CollaborationEmailInviteDelimiter">
            <textBox refId="CollaborationEmailInviteDelimiter_TB">
               <label>Séparateur entre les adresses électroniques</label>
            </textBox>
         </presentation>

         <presentation id="CollaborationServerURLs">
            <listBox refId="CollaborationServerURLs_list">Liste des URL et des noms des serveurs externes</listBox>
         </presentation>

         <presentation id="ConnectionTicketTimeout">
            <decimalTextBox refId="ConnectionTicketTimeout_DB" defaultValue="120">Délai d'expiration du ticket de connexion</decimalTextBox>
         </presentation>

         <presentation id="CredentialFilterExceptions">
            <textBox refId="CredentialFilterExceptions_TB">
               <label>Exceptions du filtre d'informations d'identification</label>
            </textBox>
         </presentation>
         <presentation id="RDPVcBridgeUnsupportedClients">
            <textBox refId="RDPVcBridgeUnsupportedClients_TB">
               <label>Clients RDPVcBridge non pris en charge</label>
            </textBox>
         </presentation>

         <presentation id="CommandsToRunOnConnect">
            <listBox refId="CommandsToRunOnConnect_list">Commandes</listBox>
         </presentation>

         <presentation id="CommandsToRunOnReconnect">
            <listBox refId="CommandsToRunOnReconnect_list">Commandes</listBox>
         </presentation>

         <presentation id="CommandsToRunOnDisconnect">
            <listBox refId="CommandsToRunOnDisconnect_list">Commandes</listBox>
         </presentation>

         <presentation id="SSO_retry_timeout">
            <decimalTextBox refId="SSO_retry_timeout_DB" defaultValue="5000">Délai de nouvelle tentative de l'authentification unique</decimalTextBox>
         </presentation>

         <presentation id="RDS_threshold_connecting_session">
            <decimalTextBox refId="RDS_threshold_connecting_session_DB" defaultValue="20">Valeur du seuil de sessions de connexion</decimalTextBox>
         </presentation>

         <presentation id="RDS_threshold_load_index">
            <decimalTextBox refId="RDS_threshold_load_index_DB" defaultValue="0">Valeur du seuil d'indice de charge</decimalTextBox>
         </presentation>

         <presentation id="Win10PhysicalAgentAudioOption">
            <dropdownList refId="Win10PhysicalAgentAudioOption_DDL" defaultItem="0">Option audio pour une machine de poste de travail distant physique Windows 10 à session unique</dropdownList>
         </presentation>

         <presentation id="WaitForLogoff">
            <decimalTextBox refId="WaitForLogoff_DB" defaultValue="10">Attendre le délai d'expiration de la fermeture de session</decimalTextBox>
         </presentation>

         <presentation id="Accept_SSL_encr_framework_channel">
            <dropdownList refId="Accept_SSL_encr_framework_channel_DDL" defaultItem="0">Accepter le canal d'infrastructure chiffré SSL</dropdownList>
         </presentation>

         <presentation id="Local_Reader_Name">
            <textBox refId="Local_Reader_Name_TB">
               <label>Nom du lecteur local</label>
            </textBox>
         </presentation>

         <presentation id="Cert_wait_timeout">
            <decimalTextBox refId="Cert_wait_timeout_DB" defaultValue="40">Délai d'attente de certificat</decimalTextBox>
         </presentation>

         <presentation id="Min_key_size">
            <decimalTextBox refId="Min_key_size_DB" defaultValue="1024">Taille de clé minimale</decimalTextBox>
         </presentation>

         <presentation id="All_key_sizes">
            <textBox refId="All_key_sizes_TB">
               <label>Toutes les tailles de clé</label>
                  <defaultValue>2048</defaultValue>
            </textBox>
         </presentation>

         <presentation id="Keys_to_precreate">
            <decimalTextBox refId="Keys_to_precreate_DB" defaultValue="5">Nombre de clés à créer au préalable</decimalTextBox>
         </presentation>

         <presentation id="Cert_min_validity">
            <decimalTextBox refId="Cert_min_validity_DB" defaultValue="10">Période de validité minimale requise pour un certificat</decimalTextBox>
         </presentation>

         <presentation id="WhfbCertificateAllowedApplicationsList">
            <multiTextBox refId="Whfb_Certificate_Allowed_Applications_list_TB">Liste des exécutables autorisés</multiTextBox>
         </presentation>

         <presentation id="Exclude_Vid_Pid">
            <textBox refId="Exclude_Vid_Pid_TB">
               <label>Exclure un périphérique Vid/Pid</label>
            </textBox>
         </presentation>

         <presentation id="Exclude_Vid_Pid_Rel">
            <textBox refId="Exclude_Vid_Pid_Rel_TB">
               <label>Exclure le périphérique Vid/Pid/Rel</label>
            </textBox>
         </presentation>

         <presentation id="Include_Vid_Pid">
            <textBox refId="Include_Vid_Pid_TB">
               <label>Inclure un périphérique Vid/Pid</label>
            </textBox>
         </presentation>

         <presentation id="Include_Vid_Pid_Rel">
            <textBox refId="Include_Vid_Pid_Rel_TB">
               <label>Inclure le périphérique Vid/Pid/Rel</label>
            </textBox>
         </presentation>

         <presentation id="Exclude_device_family">
            <textBox refId="Exclude_device_family_TB">
               <label>Exclure une famille de périphériques</label>
            </textBox>
         </presentation>

         <presentation id="Include_device_family">
            <textBox refId="Include_device_family_TB">
               <label>Inclure une famille de périphériques</label>
            </textBox>
         </presentation>

         <presentation id="HidOpt_Include_Vid_Pid">
            <textBox refId="HidOpt_Include_Vid_Pid_TB">
               <label>Inclure le périphérique VID/PID d'optimisation HID</label>
            </textBox>
         </presentation>

         <presentation id="Exclude_Auto_Vid_Pid">
            <textBox refId="Exclude_Auto_Vid_Pid_TB">
               <label>Exclure le périphérique VID/PID de connexion automatiquement</label>
            </textBox>
         </presentation>

         <presentation id="Exclude_auto_device_family">
            <textBox refId="Exclude_auto_device_family_TB">
               <label>Exclure la famille de périphériques de connexion automatiquement</label>
            </textBox>
         </presentation>

         <presentation id="Exclude_Vid_Pid_from_Split">
            <textBox refId="Exclude_Vid_Pid_from_Split_TB">
               <label>Exclure un périphérique Vid/Pid du fractionnement</label>
            </textBox>
         </presentation>

         <presentation id="Split_Vid_Pid_Device">
            <textBox refId="Split_Vid_Pid_Device_TB">
               <label>Fractionner un périphérique Vid/Pid</label>
            </textBox>
         </presentation>

         <presentation id="Allow_other_input_devices">
            <dropdownList refId="Allow_other_input_devices_DDL" defaultItem="0">Autoriser d'autres périphériques d'entrée</dropdownList>
         </presentation>

         <presentation id="Allow_HID_Bootable">
            <dropdownList refId="Allow_HID_Bootable_DDL" defaultItem="0">Autoriser les périphériques HID démarrables</dropdownList>
         </presentation>

         <presentation id="Allow_Audio_Input_devices">
            <dropdownList refId="Allow_Audio_Input_devices_DDL" defaultItem="0">Autoriser les périphériques d'entrée audio</dropdownList>
         </presentation>

         <presentation id="Allow_Audio_Output_devices">
            <dropdownList refId="Allow_Audio_Output_devices_DDL" defaultItem="0">Autoriser les périphériques de sortie audio</dropdownList>
         </presentation>

         <presentation id="Allow_keyboard_mouse">
            <dropdownList refId="Allow_keyboard_mouse_DDL" defaultItem="0">Autoriser les périphériques clavier et souris</dropdownList>
         </presentation>

         <presentation id="Allow_Video_Devices">
            <dropdownList refId="Allow_Video_Devices_DDL" defaultItem="0">Autoriser les périphériques vidéo</dropdownList>
         </presentation>

         <presentation id="Allow_Smart_Cards">
            <dropdownList refId="Allow_Smart_Cards_DDL" defaultItem="0">Autoriser les périphériques de carte à puce</dropdownList>
         </presentation>

         <presentation id="Allow_Auto_Device_Splitting">
            <dropdownList refId="Allow_Auto_Device_Splitting_DDL" defaultItem="0">Autoriser le fractionnement automatique de périphérique</dropdownList>
         </presentation>

         <presentation id="Accept_SSL_encr_framework_channel_2">
            <dropdownList refId="Accept_SSL_encr_framework_channel_2_DDL" defaultItem="0">Accepter le canal d'infrastructure chiffré SSL</dropdownList>
         </presentation>

         <presentation id="Default_proxy_server">
            <textBox refId="Default_proxy_server_TB">
               <label>Serveur proxy par défaut</label>
            </textBox>
         </presentation>

         <presentation id="Update_Java_Proxy">
            <dropdownList refId="Update_Java_Proxy_DDL" defaultItem="2">Définir le proxy pour l'applet Java</dropdownList>
         </presentation>

         <presentation id="HTML5MMRUrlList">
            <listBox refId="HTML5MMRUrlList_list">Liste d'URL pour activer la redirection multimédia HTML5 d'Horizon.</listBox>
         </presentation>

         <presentation id="HTML5MMRUrlBlockList">
            <listBox refId="HTML5MMRUrlBlockList_list">Liste d'URL pour exclure la redirection multimédia HTML5 d'Horizon.</listBox>
         </presentation>

         <presentation id="GeoRedirUrlList">
            <listBox refId="GeoRedirUrlList_list">Liste d'URL pour activer la fonctionnalité de redirection de géolocalisation d'Horizon.</listBox>
         </presentation>

         <presentation id="GeoRedirDistanceDelta">
            <textBox refId="GeoRedirDistanceDelta_value">
               <label>Distance minimale en mètres</label>
               <defaultValue>75</defaultValue>
            </textBox>
         </presentation>

         <presentation id="WebrtcProxyProbeUrl">
            <textBox refId="WebrtcProxyProbeUrl_value">
               <label>Utiliser l'URL pour sonder le serveur proxy pour les appels webrtc</label>
            </textBox>
         </presentation>


         <presentation id="BrowserRedirUrlList">
            <listBox refId="BrowserRedirUrlList_list">Liste d'URL pour activer la fonctionnalité de redirection d'Horizon Browser.</listBox>
         </presentation>

         <presentation id="EnhBrowserRedirUrlList">
            <listBox refId="EnhBrowserRedirUrlList_list">Liste d'URL pour activer la fonctionnalité de redirection améliorée d'Horizon Browser.</listBox>
         </presentation>

         <presentation id="BrowserRedirNavUrlList">
            <listBox refId="BrowserRedirNavUrlList_list">Liste d'URL pour activer la navigation de la fonctionnalité de redirection d'Horizon Browser.</listBox>
         </presentation>

         <presentation id="WebRTC_SDK_Web_App_Redir_Allow_List">
            <listBox refId="WebRTC_SDK_Web_App_Redir_Allow_List_list">Liste d'URL pour activer le SDK Horizon WebRTC pour la prise en charge des applications Web.</listBox>
         </presentation>

         <presentation id="SetForceNonOptimizedOptions">
            <checkBox refId="AUTO_DETECT_EXTERNAL_CONNECTIONS_CHKBOX">Détecter automatiquement les connexions externes</checkBox>
            <textBox refId="SetForceNonOptimizedEnvVar_TB">
               <label>Nom de la variable d'environnement :</label>
            </textBox>
         </presentation>

         <presentation id="UnityFilterRules_Filter">
            <listBox refId="UnityFilterRules_FilterList">Règles de filtre Unity</listBox>
         </presentation>

         <presentation id="Enable_Unity_Touch">
            <checkBox refId="Enabled_UWP_For_UnityTouch_CB" defaultChecked="true">Activer la prise en charge de l'application de plateforme Windows universelle (UWP) pour Unity Touch sur Windows 10.</checkBox>
         </presentation>

         <presentation id="ProcessIgnoreList">
            <multiTextBox refId="ProcessIgnoreList_TB">Processus à ignorer lors de la détection des sessions d'application vides</multiTextBox>
         </presentation>

         <presentation id="VDI_idle_time_till_disconnect">
            <dropdownList refId="VDI_idle_time_till_disconnect_minutes" noSort="true" defaultItem="0">Délai d'inactivité</dropdownList>
         </presentation>

         <presentation id="VDI_disconnect_time_till_logoff">
            <dropdownList refId="VDI_disconnect_time_till_logoff_minutes" noSort="true" defaultItem="0">Délai de déconnexion</dropdownList>
         </presentation>

         <presentation id="RDS_idle_time_till_disconnect">
            <dropdownList refId="RDS_idle_time_till_disconnect_milliseconds" noSort="true" defaultItem="0">Délai d'inactivité RDS</dropdownList>
         </presentation>

         <presentation id="RDS_disconnect_time_till_logoff">
            <dropdownList refId="RDS_disconnect_time_till_logoff_milliseconds" noSort="true" defaultItem="0">Délai de déconnexion RDS</dropdownList>
         </presentation>

         <presentation id="RDS_active_time_till_disconnect">
            <dropdownList refId="RDS_active_time_till_disconnect_milliseconds" noSort="true" defaultItem="0">Délai de connexion RDS</dropdownList>
         </presentation>

         <presentation id="Prewarm_disconnect_time_till_logoff">
            <dropdownList refId="Prewarm_disconnect_time_till_logoff_minutes" noSort="true" defaultItem="5">Délai d'expiration du préchauffage</dropdownList>
         </presentation>

         <presentation id ="watermark_configuration">
            <multiTextBox refId="Text">Texte</multiTextBox>
            <dropdownList refId="ImageFit" noSort="true" defaultItem="0">Disposition de l'image</dropdownList>
            <decimalTextBox refId="Rotation" defaultValue="45">Rotation du texte</decimalTextBox>
            <decimalTextBox refId="Opacity" defaultValue="50">Opacité</decimalTextBox>
            <decimalTextBox refId="Margin" defaultValue="50">Marge</decimalTextBox>
            <textBox refId="TextColor">
               <label>Couleur du texte</label>
               <defaultValue>255 255 255</defaultValue>
            </textBox>
            <decimalTextBox refId="FontSize" defaultValue="0">Taille de police</decimalTextBox>
            <decimalTextBox refId="RefreshInterval" defaultValue="0">Intervalle d'actualisation</decimalTextBox>
         </presentation>

        <presentation id="AppSignInWindow">
            <decimalTextBox refId="app_sign_in_window_width" defaultValue="800" spinStep="2">Largeur de la fenêtre des mentions légales : </decimalTextBox>
            <decimalTextBox refId="app_sign_in_window_height" defaultValue="600" spinStep="2">Hauteur de la fenêtre des mentions légales : </decimalTextBox>
        </presentation>
        <presentation id="Video_Cpu_Overuse_Threshold">
            <decimalTextBox refId="Video_Cpu_Overuse_Threshold_data" defaultValue="85">Seuil de surutilisation du CPU vidéo</decimalTextBox>
         </presentation>
         <presentation id="Webrtc_Recommended_AEC">
            <checkBox refId="Webrtc_Recommended_AEC_CB" defaultChecked="true"> Utiliser l'algorithme AEC recommandé </checkBox>
         </presentation>
         <presentation id="Force_Enable_Teams_Redir">
            <checkBox refId="Force_Enable_Teams_Redir_Value" defaultChecked="false"> Forcer l'optimisation de WebRTC côté client </checkBox>
         </presentation>
         <presentation id="Force_Enable_Electron_App_Redir">
            <checkBox refId="Force_Enable_Electron_App_Redir_Value" defaultChecked="false"> Forcer l'optimisation de WebRTC côté client </checkBox>
         </presentation>
         <presentation id="FIDO2AllowList">
            <textBox refId="FIDO2AllowList_TB">
               <label>Liste autorisée de FIDO2</label>
               <defaultValue>chrome.exe;firefox.exe;msedge.exe</defaultValue>
            </textBox>
         </presentation>
         <presentation id="AllowScreenRecording">
            <checkBox refId="AllowScreenRecording_CB" defaultChecked="false"> Autoriser l'enregistrement d'écran pour Horizon Mac Client </checkBox>
         </presentation>
         <presentation id="CustomBackgroundImagesFolderPath">
            <textBox refId="CustomBackgroundImagesFolder_Path">
               <label>Dossier d'images d'arrière-plan personnalisées</label>
            </textBox>
         </presentation>
         <presentation id="CustomBackgroundDefaultImageName">
            <textBox refId="CustomBackgroundDefaultImageName_Value">
               <label>Nom de l'image par défaut</label>
            </textBox>
         </presentation>
         <presentation id="EnableDisplayNetworkState">
            <decimalTextBox refId="NetworkWarningInterval_CB" defaultValue="5">Intervalle de temps d'affichage du message contextuel d'avertissement du réseau, en minutes. 60 minutes au maximum, 1 minute au minimum. La valeur par défaut est 5 minutes.</decimalTextBox>
         </presentation>
         <presentation id="IpPrefix" >
            <textBox refId="IpPrefixTextBox" >
               <label >Préfixe IP</label>
            </textBox>
         </presentation>
      </presentationTable>
   </resources>
</policyDefinitionResources>
