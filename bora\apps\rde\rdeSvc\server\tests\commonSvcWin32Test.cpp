/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * commonSvcWin32Test.cpp -
 *
 *    Unit Test for commonSvcWin.cpp
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <vector>

#include "shared/win32/stdafx.h"
#include "commonSvcWin32.h"
#include "commonSvcWin32UTMock.h"
#include "commonSvcMsg.h"
#include "fakeVdpService.h"
#include "rxUTLog.h"
#include "ScopeGuard.h"
#include "test.h"
#include "TabletModeController.h"
#include "utMock.h"

using namespace CORE;

using ::testing::_;
using ::testing::NiceMock;
using ::testing::Return;

static constexpr MXUser::Rank kBaseRank = 1000;
static constexpr auto kOneSecond = std::chrono::milliseconds(1000);
#define AGENT_POLICY_PATH L"HKLM\\" HORIZON_VDM_AGENT_REG_GPO_ROOT_W L"\\Configuration"
#define ALLOW_ARM_NO_ANTIKEYLOGGER                                                                 \
   L"Allow Connections from Horizon Client for Windows without Antikeylogger service if the "      \
   L"device is ARM-based"

class CommonSvcWin32UnitTest : public appremote::Test {
   void SetUp() override
   {
      VDP_SERVICE_QUERY_INTERFACE api = {0};
      void *channelHandle = NULL;
      if (!VDPService_ServerInit("HorizonRdeTest", &api, &channelHandle)) {
         Log("%s: Failed to initialize vdp service.\n", __FUNCTION__);
         FAIL() << "Failed to initialize vdp service\n";
      }
      SetAPIEntry(api);
   }

   void TearDown() override { VDPService_ServerExit(); }
};


/*
 *-----------------------------------------------------------------------------
 *
 * CommonSvcWin32UnitTest::CommonSvcWin32 --
 *
 *      Test CommonSvcWin32::CommonSvcWin32
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CommonSvcWin32UnitTest, WaitForUserLogon)
{
   std::shared_ptr<CommonSvcWin32UTMock> commonSvc = std::make_shared<CommonSvcWin32UTMock>();
   ASSERT_TRUE(commonSvc != nullptr);
   ASSERT_TRUE(SetEvent(commonSvc->mUserTokenAvilableEvent))
      << "Set mUserTokenAvilableEvent error: " << GetLastError() << std::endl;
   ASSERT_TRUE(commonSvc->WaitForUserLogon()) << "Wait for mUserTokenAvilableEvent failed\n";
   ResetEvent(commonSvc->mUserTokenAvilableEvent);
   ASSERT_TRUE(SetEvent(commonSvc->mHelperThreadStopEvent))
      << "Set HelperThreadStopEvent error: " << GetLastError() << std::endl;
   ASSERT_FALSE(commonSvc->WaitForUserLogon()) << "Wait for mHelperThreadStopEvent failed\n";
}


/*
 *-----------------------------------------------------------------------------
 *
 * CommonSvcWin32UnitTest::StartMessageFramework --
 *
 *      Test CommonSvcWin32::StartMessageFramework
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

typedef MessageFrameWork *(*FPStart)(MessageFrameWork::PCHANNELEXNOTIF, int, LPCTSTR, DWORD, int,
                                     int);

typedef MessageChannel *(MessageFrameWork::*FPConnectChannel)(MessageFrameWork::channelTypes,
                                                              LPCTSTR, LPCTSTR, LPCTSTR, LPCTSTR,
                                                              LPCTSTR, LPCTSTR, unsigned short,
                                                              MessageFrameWork::channelErrorTypes *,
                                                              PropertyBag *);

typedef void (MessageFrameWork::*FPPostMsg)(LPCTSTR, LPCTSTR, const CORE::PropertyBag &,
                                            CORE::MessageChannel *, CORE::MsgBinary *, bool, bool);

typedef MessageHandler::respType (MessageFrameWork::*FPSendMsg)(
   LPCTSTR, LPCTSTR, const CORE::PropertyBag &, CORE::PropertyBag &,
   CORE::MessageHandler::PRESPFRAGMENT, void *, CORE::MessageChannel *, DWORD, HANDLE,
   CORE::MsgBinary *, bool, CORE::MsgBinary *, CORE::tstr *, bool);

TEST_F(CommonSvcWin32UnitTest, StartMessageFramework)
{
   std::shared_ptr<CommonSvcWin32UTMock> commonSvc =
      std::make_shared<NiceMock<CommonSvcWin32UTMock>>();
   ASSERT_TRUE(commonSvc != nullptr);
   VMOCK((FPStart)&MessageFrameWork::Start)
      .ExpectCall(_, _, _, _, _, _)
      .WillOnce(Return((MessageFrameWork *)NULL))
      .WillRepeatedly(Return((MessageFrameWork *)0x12345678));
   ASSERT_FALSE(commonSvc->StartMessageFramework());
   ASSERT_TRUE(commonSvc->mMessageFrameworkStarted);
   ASSERT_FALSE(commonSvc->mMessageFrameworkChannel);


   VMOCK(&MessageFrameWork::Ready)
      .ExpectCall()
      .WillOnce(Return(false))
      .WillRepeatedly(Return(true));
   ASSERT_FALSE(commonSvc->StartMessageFramework());
   ASSERT_FALSE(commonSvc->mMessageFrameworkChannel);

   VMOCK((FPConnectChannel)&MessageFrameWork::ConnectChannel)
      .ExpectCall(_, _, _, _, _, _, _, _, _, _)
      .WillOnce(Return((MessageChannel *)NULL))
      .WillOnce(Return((MessageChannel *)0x12345678));
   ASSERT_FALSE(commonSvc->StartMessageFramework());
   ASSERT_FALSE(commonSvc->mMessageFrameworkChannel);


   /*
    * An not null MessageChannel pointer is returned,
    * so we need to mock MessageFrameWork::CloseChannel to avoid crash
    * in ~CommonSvcWin32().
    */
   ON_BLOCK_EXIT([&commonSvc]() {
      VMOCK(&MessageFrameWork::CloseChannel).ExpectCall(_).WillOnce([](MessageChannel *pChannel) {
         return;
      });
      commonSvc.reset();
   });

   ASSERT_TRUE(commonSvc->StartMessageFramework());
   ASSERT_EQ(commonSvc->mMessageFrameworkChannel, (MessageChannel *)0x12345678);
}


/*
 *-----------------------------------------------------------------------------
 *
 * CommonSvcWin32UnitTest::ProcessEnvironmentVarInfoCommand --
 *
 *      Test CommonSvcWin32::ProcessEnvironmentVarInfoCommand
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CommonSvcWin32UnitTest, ProcessEnvironmentVarInfoCommand)
{
   std::shared_ptr<CommonSvcWin32UTMock> commonSvc = std::make_shared<CommonSvcWin32UTMock>();
   ASSERT_TRUE(commonSvc != nullptr);
   commonSvc->ProcessEnvironmentVarInfoCommand(NULL);
   ASSERT_FALSE(commonSvc->mMessageFrameworkStarted) << "message channel started unexpectedly\n";


   VMOCK((FPStart)&MessageFrameWork::Start)
      .ExpectCall(_, _, _, _, _, _)
      .WillOnce(Return((MessageFrameWork *)0x12345678));
   VMOCK(&MessageFrameWork::Ready).ExpectCall().WillOnce(Return(true));
   VMOCK((FPConnectChannel)&MessageFrameWork::ConnectChannel)
      .ExpectCall(_, _, _, _, _, _, _, _, _, _)
      .WillOnce(Return((MessageChannel *)0x12345678));
   VMOCK((FPPostMsg)&MessageFrameWork::PostMsg).ExpectCall(_, _, _, _, _, _, _).Times(1);
   /*
    * An not null MessageChannel pointer is returned,
    * so we need to mock MessageFrameWork::CloseChannel to avoid crash
    * in ~CommonSvcWin32().
    */
   ON_BLOCK_EXIT([&commonSvc]() {
      VMOCK(&MessageFrameWork::CloseChannel).ExpectCall(_).WillOnce([](MessageChannel *pChannel) {
         return;
      });
      commonSvc.reset();
   });

   commonSvc->ProcessEnvironmentVarInfoCommand("a=b");
}


/*
 *-----------------------------------------------------------------------------
 *
 * CommonSvcWin32UnitTest::OnObjectStateChanged --
 *
 *      Test CommonSvcWin32::OnObjectStateChanged
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CommonSvcWin32UnitTest, OnObjectStateChanged)
{
   std::shared_ptr<CommonSvcWin32UTMock> commonSvc =
      std::make_shared<NiceMock<CommonSvcWin32UTMock>>();
   ASSERT_TRUE(commonSvc != nullptr);
   VDPService_SetChannelObjectState(COMMONSVC_OBJ_NAME, VDP_RPC_OBJ_CONNECTED, true);

   VDPService_SetChannelObjectState(COMMONSVC_OBJ_NAME, VDP_RPC_OBJ_DISCONNECTED, true);
}


/*
 *-----------------------------------------------------------------------------
 *
 * CommonSvcWin32UnitTest::SetTabletMode --
 *
 *      Test CommonSvcWin32::SetTabletMode
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CommonSvcWin32UnitTest, SetTabletMode)
{
   std::shared_ptr<CommonSvcWin32UTMock> commonSvc =
      std::make_shared<NiceMock<CommonSvcWin32UTMock>>();
   ASSERT_TRUE(commonSvc != nullptr);

   MXUser::Semaphore semaphore{__FUNCTION__, kBaseRank};
   VMOCK(&TabletModeController::SetTabletMode).ExpectCall(_).WillOnce([&semaphore](UINT mode) {
      semaphore.notify();
   });
   ASSERT_TRUE(commonSvc->GetUserToken());
   commonSvc->SetTabletMode();
   EXPECT_TRUE(semaphore.wait_for(5 * kOneSecond))
      << "TabletModeController::SetTabletMode is not called\n";
}


/*
 *-----------------------------------------------------------------------------
 *
 * CommonSvcWin32UnitTest::OnDisplayChange --
 *
 *      Test CommonSvcWin32::OnDisplayChange
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CommonSvcWin32UnitTest, OnDisplayChange)
{
   std::shared_ptr<CommonSvcWin32UTMock> commonSvc =
      std::make_shared<NiceMock<CommonSvcWin32UTMock>>();
   ASSERT_TRUE(commonSvc != nullptr);
   BOOL handled = TRUE;
   VMOCK(&DpiSyncServerWin32::OnDisplayChange).ExpectCall(_, _).Times(1);
   commonSvc->OnDisplayChange(0x1, 0x2, 0x3, handled);
}


/*
 *-----------------------------------------------------------------------------
 *
 * CommonSvcWin32UnitTest::OnTimer --
 *
 *      Test CommonSvcWin32::OnTimer
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CommonSvcWin32UnitTest, OnTimer)
{
   std::shared_ptr<CommonSvcWin32UTMock> commonSvc =
      std::make_shared<NiceMock<CommonSvcWin32UTMock>>();
   ASSERT_TRUE(commonSvc != nullptr);
   BOOL handled = TRUE;

#define BAT_STAT_RETRY_TIMER_ID 1001
#define TRAY_ICON_EXISTS_TIMER_ID 1002

   EXPECT_CALL(*commonSvc, ProcessBatStateCommand(_)).WillOnce([](BatteryStateCommand *command) {
      EXPECT_TRUE(command->commandType == BAT_STAT_CMD_STAT);
      return;
   });
   commonSvc->OnTimer(0x1, BAT_STAT_RETRY_TIMER_ID, 0x2, handled);

   VMOCK(&SystemTrayUtil::CheckIconsLaunchedByUser).ExpectCall(_).Times(1);
   commonSvc->OnTimer(0x1, TRAY_ICON_EXISTS_TIMER_ID, 0x2, handled);
}


/*
 *-----------------------------------------------------------------------------
 *
 * CommonSvcWin32UnitTest::OnDWMColorizationColorChanged --
 *
 *      Test CommonSvcWin32::OnDWMColorizationColorChanged
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CommonSvcWin32UnitTest, OnDWMColorizationColorChanged)
{
   std::shared_ptr<CommonSvcWin32UTMock> commonSvc =
      std::make_shared<NiceMock<CommonSvcWin32UTMock>>();
   ASSERT_TRUE(commonSvc != nullptr);
   BOOL handled = TRUE;
   EXPECT_CALL(*commonSvc, ProcessBatStateCommand(_)).Times(0);
   commonSvc->OnDWMColorizationColorChanged(0x1, 0x2, 0x3, handled);
   commonSvc->mIsBatTrayCreated = true;
   EXPECT_CALL(*commonSvc, ProcessBatStateCommand(_)).WillOnce([](BatteryStateCommand *command) {
      EXPECT_TRUE(command->commandType == BAT_STAT_CMD_STAT);
      return;
   });
   commonSvc->OnDWMColorizationColorChanged(0x1, 0x2, 0x3, handled);
}


/*
 *-----------------------------------------------------------------------------
 *
 * CommonSvcWin32UnitTest::FetchCertSSOUnlockReq --
 *
 *      Test CommonSvcWin32::FetchCertSSOUnlockReq
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CommonSvcWin32UnitTest, FetchCertSSOUnlockReq)
{
   std::shared_ptr<CommonSvcWin32UTMock> commonSvc =
      std::make_shared<NiceMock<CommonSvcWin32UTMock>>();
   ASSERT_TRUE(commonSvc != nullptr);

   VMOCK((FPStart)&MessageFrameWork::Start)
      .ExpectCall(_, _, _, _, _, _)
      .WillOnce(Return((MessageFrameWork *)0x12345678));
   VMOCK(&MessageFrameWork::Ready).ExpectCall().WillOnce(Return(true));
   VMOCK((FPConnectChannel)&MessageFrameWork::ConnectChannel)
      .ExpectCall(_, _, _, _, _, _, _, _, _, _)
      .WillOnce(Return((MessageChannel *)0x12345678));

   VMOCK((FPSendMsg)&MessageFrameWork::SendMsg)
      .ExpectCall(_, _, _, _, _, _, _, _, _, _, _, _, _, _)
      .WillOnce([](LPCTSTR queueName, LPCTSTR queueHint, const PropertyBag &messageBody,
                   PropertyBag &responseData,
                   MessageHandler::PRESPFRAGMENT responseFragmentCallback,
                   void *responseFragmentContext, MessageChannel *pChannel, DWORD timeout,
                   HANDLE hStopEvent, MsgBinary *bin, bool TakeBinaryOwnership, MsgBinary *binResp,
                   CORE::tstr *messageId_in, bool useOwnCredentials) {
         responseData.set(TEXT("sessionGuid"), TEXT("sessionGuid"));
         responseData.set(TEXT("ticket"), TEXT("ticket"));
         return MessageHandler::MsgOk;
      });
   VMOCK(&CommonSvcWin32::WaitForExitOrEvent)
      .ExpectCall(_)
      .WillOnce(Return(true))
      .WillOnce(Return(false)); // simulate thread exits

   EXPECT_CALL(*commonSvc, SendMsg(_, _, _, _, _))
      .WillOnce([](uint32 id, int32 status, const char *cookie, const char *msg, uint32 msgLen) {
         EXPECT_TRUE(id == CERTSSO_UNLOCK_MSG);
         return true;
      });

   /*
    * An not null MessageChannel pointer is returned,
    * so we need to mock MessageFrameWork::CloseChannel to avoid crash
    * in ~CommonSvcWin32().
    */
   ON_BLOCK_EXIT([&commonSvc]() {
      VMOCK(&MessageFrameWork::CloseChannel).ExpectCall(_).WillOnce([](MessageChannel *pChannel) {
         return;
      });
      commonSvc.reset();
   });

   commonSvc->FetchCertSSOUnlockReq();
}


/*
 *-----------------------------------------------------------------------------
 *
 * CommonSvcWin32UnitTest::OnInvoked --
 *
 *      Test CommonSvcWin32::OnInvoked
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CommonSvcWin32UnitTest, OnInvoked)
{
   std::shared_ptr<CommonSvcWin32UTMock> commonSvc =
      std::make_shared<::testing::NiceMock<CommonSvcWin32UTMock>>();
   ASSERT_TRUE(commonSvc != nullptr);

   // test ProcessDpiSyncCommand
   VMOCK(&DpiSyncServerWin32::OnDpiSyncCommand).ExpectCall(_).Times(1);
   DpiSyncCommand dpiSyncCommand = {DPI_SYNC_COMMAND_VERSION,
                                    {RDE_CHANNEL_DPI_SYNC_CLIENT_DISPLAY_INFO}};
   char *s = (char *)&dpiSyncCommand;
   VDPService_SendClientCmd(COMMONSVC_OBJ_NAME, std::string(s, s + sizeof(dpiSyncCommand)),
                            DPI_SYNC_MSG, true);

   // test ProcessDisplayCommand
   VMOCK(&DpiSyncServerWin32::OnDisplayCommand).ExpectCall(_).Times(1);
   VDPService_SendClientCmd(COMMONSVC_OBJ_NAME, "2", DISPLAY_MSG, true);

   // test ProcessTabletModeCommand
   VMOCK(&CommonSvcWin32::WaitForUserLogon).ExpectCall().Times(3).WillRepeatedly(Return(true));

   // test ProcessNetworkStateGPOCommand enable display
   MXUser::Semaphore semaphoreEnableDisplay{__FUNCTION__, kBaseRank};
   VMOCK(&CommonSvcWin32::CheckNetworkStateEnableDisplayAfterLogon)
      .ExpectCall()
      .WillOnce([&semaphoreEnableDisplay]() { semaphoreEnableDisplay.notify(); });

   NetworkStateGPOCommand networkStateGPOCommand = {NETWORK_STATE_GPO_CMD_REQUEST_ENABLE_DISPLAY};
   networkStateGPOCommand.data.enableDisplay = 0;

   s = (char *)&networkStateGPOCommand;
   VDPService_SendClientCmd(COMMONSVC_OBJ_NAME, std::string(s, s + sizeof(networkStateGPOCommand)),
                            NETWORK_STATE_GPO_MSG, true);
   EXPECT_TRUE(semaphoreEnableDisplay.wait_for(5 * kOneSecond));

   // test ProcessNetworkStateGPOCommand interval
   MXUser::Semaphore semaphoreInterval{__FUNCTION__, kBaseRank};
   VMOCK(&CommonSvcWin32::CheckNetworkStateIntervalAfterLogon)
      .ExpectCall()
      .WillOnce([&semaphoreInterval]() { semaphoreInterval.notify(); });
   networkStateGPOCommand = {NETWORK_STATE_GPO_CMD_REQUEST_INTERVAL};
   networkStateGPOCommand.data.interval = 0;

   s = (char *)&networkStateGPOCommand;
   VDPService_SendClientCmd(COMMONSVC_OBJ_NAME, std::string(s, s + sizeof(networkStateGPOCommand)),
                            NETWORK_STATE_GPO_MSG, true);
   EXPECT_TRUE(semaphoreInterval.wait_for(5 * kOneSecond));

   MXUser::Semaphore semaphore{__FUNCTION__, kBaseRank};
   VMOCK(&CommonSvcWin32::SetTabletMode).ExpectCall().WillOnce([&semaphore]() {
      semaphore.notify();
   });
   TabletModeCommand tabletModeCommand = {TABLET_MODE_COMMAND_SET, {0x1}};
   s = (char *)&tabletModeCommand;
   VDPService_SendClientCmd(COMMONSVC_OBJ_NAME, std::string(s, s + sizeof(tabletModeCommand)),
                            TABLET_MODE_MSG, true);
   EXPECT_TRUE(semaphore.wait_for(5 * kOneSecond))
      << "CommonSvcWin32::SetTabletMode is not called\n";
}


/*
 *-----------------------------------------------------------------------------
 *
 * CommonSvcWin32UnitTest::ProcessBatStateCommand --
 *
 *      Test CommonSvcWin32::ProcessBatStateCommand
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CommonSvcWin32UnitTest, ProcessBatStateCommand)
{
   std::shared_ptr<CommonSvcWin32UTMock> commonSvc =
      std::make_shared<::testing::NiceMock<CommonSvcWin32UTMock>>();
   ASSERT_TRUE(commonSvc != nullptr);

   commonSvc->mIsBatStatRedirEnabled = false;
   BatteryStateCommand batteryStateCommand = {BAT_STAT_CMD_VERSION};
   commonSvc->ProcessBatStateCommand(&batteryStateCommand);

   commonSvc->mIsBatStatRedirEnabled = true;
   commonSvc->ProcessBatStateCommand(&batteryStateCommand);

   // make sure commonSvc->mUserToken is valid
   ASSERT_TRUE(commonSvc->GetUserToken());

   bool acStates[] = {true, false};
   uint32 lifePercents[] = {0, 10, 20, 30, 40, 60, 80, 100};
   for (bool isACConnected : acStates) {
      for (uint32 batteryLifePercent : lifePercents) {
         batteryStateCommand = {BAT_STAT_CMD_STAT};
         batteryStateCommand.data.stat.isACConnected = isACConnected;
         batteryStateCommand.data.stat.batteryLifePercent = batteryLifePercent;
         VMOCK(&Shell_NotifyIcon).ExpectCall(_, _).WillOnce(Return(TRUE));
         commonSvc->ProcessBatStateCommand(&batteryStateCommand);

         EXPECT_TRUE(commonSvc->mIsBatTrayCreated);
         EXPECT_TRUE(commonSvc->mIsACConnected == isACConnected)
            << "mIsACConnected: " << commonSvc->mIsACConnected << "expect: " << isACConnected
            << std::endl;
         EXPECT_TRUE(commonSvc->mBatteryLifePercent == batteryLifePercent)
            << "mBatteryLifePercent: " << commonSvc->mBatteryLifePercent
            << "expect: " << batteryLifePercent << std::endl;
      }
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * CommonSvcWin32UnitTest::ProcessFeatureEnablementCommand --
 *
 *      Test CommonSvcWin32::ProcessFeatureEnablementCommand
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CommonSvcWin32UnitTest, ProcessFeatureEnablementCommand)
{
   std::shared_ptr<CommonSvcWin32UTMock> commonSvc =
      std::make_shared<::testing::NiceMock<CommonSvcWin32UTMock>>();
   ASSERT_TRUE(commonSvc != nullptr);

   FeatureEnablementCommand command = {FEATURE_ENABLEMENT_CMD_CAPACITY};
   command.data.capacity =
      BLOCK_SCREEN_CAPTURE_MASK | BLOCK_KEY_LOGGER_MASK | BLOCK_THUMBNAIL_REPRESENTATION_MASK;

   EXPECT_CALL(*commonSvc, SendMsg(_, _, _, _, _))
      .WillOnce([](uint32 id, int32 status, const char *cookie, const char *msg, uint32 msgLen) {
         EXPECT_TRUE(id == FEATURE_ENABLEMENT_MSG);
         return true;
      });
   EXPECT_CALL(*commonSvc, SendMsg(_, _))
      .WillOnce([](uint32 id, const std::vector<util::Variant> &values) {
         EXPECT_TRUE(id == FEATURE_OPTION_MSG);
         return true;
      });
   commonSvc->ProcessFeatureEnablementCommand(&command);
}


/*
 *-----------------------------------------------------------------------------
 *
 * CommonSvcWin32UnitTest::CheckNetworkStateEnableDisplayAfterLogon --
 *
 *      Test CommonSvcWin32::CheckNetworkStateEnableDisplayAfterLogon
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CommonSvcWin32UnitTest, CheckNetworkStateEnableDisplayAfterLogon)
{
   std::shared_ptr<CommonSvcWin32UTMock> commonSvc =
      std::make_shared<::testing::NiceMock<CommonSvcWin32UTMock>>();
   ASSERT_TRUE(commonSvc != nullptr);

   EXPECT_CALL(*commonSvc, SendMsg(_, _, _, _, _))
      .WillOnce([](uint32 id, int32 status, const char *cookie, const char *msg, uint32 msgLen) {
         EXPECT_TRUE(id == NETWORK_STATE_GPO_MSG);
         EXPECT_TRUE(msg);
         const NetworkStateGPOCommand *cmd = reinterpret_cast<const NetworkStateGPOCommand *>(msg);
         EXPECT_TRUE(cmd != nullptr);
         EXPECT_TRUE(cmd->commandType == NETWORK_STATE_GPO_CMD_RESPOND_ENABLE_DISPLAY);

         return true;
      });

   commonSvc->CheckNetworkStateEnableDisplayAfterLogon();
   testing::Mock::VerifyAndClearExpectations(commonSvc.get());
}


/*
 *-----------------------------------------------------------------------------
 *
 * CommonSvcWin32UnitTest::CheckNetworkStateIntervalAfterLogon --
 *
 *      Test CommonSvcWin32::CheckNetworkStateIntervalAfterLogon
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CommonSvcWin32UnitTest, CheckNetworkStateIntervalAfterLogon)
{
   std::shared_ptr<CommonSvcWin32UTMock> commonSvc =
      std::make_shared<::testing::NiceMock<CommonSvcWin32UTMock>>();
   ASSERT_TRUE(commonSvc != nullptr);

   EXPECT_CALL(*commonSvc, SendMsg(_, _, _, _, _))
      .WillOnce([](uint32 id, int32 status, const char *cookie, const char *msg, uint32 msgLen) {
         EXPECT_TRUE(id == NETWORK_STATE_GPO_MSG);
         EXPECT_TRUE(msg);
         const NetworkStateGPOCommand *cmd = reinterpret_cast<const NetworkStateGPOCommand *>(msg);
         EXPECT_TRUE(cmd != nullptr);
         EXPECT_TRUE(cmd->commandType == NETWORK_STATE_GPO_CMD_RESPOND_INTERVAL);
         return true;
      });

   commonSvc->CheckNetworkStateIntervalAfterLogon();
   testing::Mock::VerifyAndClearExpectations(commonSvc.get());
}


/*
 *-----------------------------------------------------------------------------
 *
 * CommonSvcWin32UnitTest::IsFeatureOptionsChanged --
 *
 *      Test CommonSvcWin32::IsFeatureOptionsChanged.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CommonSvcWin32UnitTest, IsFeatureOptionsChanged)
{
   std::shared_ptr<CommonSvcWin32UTMock> commonSvc =
      std::make_shared<::testing::NiceMock<CommonSvcWin32UTMock>>();
   ASSERT_TRUE(commonSvc != nullptr);

   {
      std::map<std::string, util::Variant> fakedeatureOptions;
      fakedeatureOptions[ALLOW_ARM_NO_ANTIKEYLOGGER_OPTION_KEY] = util::Variant((int32)(1));
      commonSvc->mFeatureOptions.clear();
      commonSvc->mFeatureOptions["fakedKey"] = util::Variant((int32)(1));
      EXPECT_TRUE(commonSvc->IsFeatureOptionsChanged(fakedeatureOptions));
   }
   {
      std::map<std::string, util::Variant> fakedeatureOptions;
      fakedeatureOptions[ALLOW_ARM_NO_ANTIKEYLOGGER_OPTION_KEY] = util::Variant((int32)(1));
      commonSvc->mFeatureOptions.clear();
      commonSvc->mFeatureOptions[ALLOW_ARM_NO_ANTIKEYLOGGER_OPTION_KEY] = util::Variant((int32)(1));
      EXPECT_FALSE(commonSvc->IsFeatureOptionsChanged(fakedeatureOptions));
   }
   {
      std::map<std::string, util::Variant> fakedeatureOptions;
      fakedeatureOptions["fakedKey"] = util::Variant((int32)(1));
      commonSvc->mFeatureOptions.clear();
      commonSvc->mFeatureOptions[ALLOW_ARM_NO_ANTIKEYLOGGER_OPTION_KEY] = util::Variant((int32)(1));
      EXPECT_TRUE(commonSvc->IsFeatureOptionsChanged(fakedeatureOptions));
   }
   {
      std::map<std::string, util::Variant> fakedeatureOptions;
      fakedeatureOptions[ALLOW_ARM_NO_ANTIKEYLOGGER_OPTION_KEY] = util::Variant((int32)(0));
      commonSvc->mFeatureOptions.clear();
      commonSvc->mFeatureOptions[ALLOW_ARM_NO_ANTIKEYLOGGER_OPTION_KEY] = util::Variant((int32)(1));
      EXPECT_TRUE(commonSvc->IsFeatureOptionsChanged(fakedeatureOptions));
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * CommonSvcWin32UnitTest::ReadMachineAppProtectionGPO --
 *
 *      Test CommonSvcWin32::ReadMachineAppProtectionGPO
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CommonSvcWin32UnitTest, ReadMachineAppProtectionGPO)
{
   std::shared_ptr<CommonSvcWin32UTMock> commonSvc =
      std::make_shared<::testing::NiceMock<CommonSvcWin32UTMock>>();
   ASSERT_TRUE(commonSvc != nullptr);
   typedef corestring<wchar_t> (*Fn)(const wchar_t *, const wchar_t *, bool);

   {
      uint64 featureEnablementStatus;
      std::map<std::string, util::Variant> featureOptions;
      EXPECT_FALSE(featureOptions.contains(ALLOW_ARM_NO_ANTIKEYLOGGER_OPTION_KEY));
      commonSvc->mClientFeatureCapacity = BLOCK_KEY_LOGGER_MASK;
      commonSvc->ReadMachineAppProtectionGPO(featureEnablementStatus, featureOptions);
      EXPECT_TRUE(featureOptions.contains(ALLOW_ARM_NO_ANTIKEYLOGGER_OPTION_KEY) &&
                  featureOptions[ALLOW_ARM_NO_ANTIKEYLOGGER_OPTION_KEY].lVal == 0);
   }
   {
      VMOCK((Fn)&wstr::readRegistry)
         .WillRepeatedly([](const wchar_t *path, const wchar_t *default, bool useWow6432) {
            corestring<wchar_t> keyPath = AGENT_POLICY_PATH L"\\" ALLOW_ARM_NO_ANTIKEYLOGGER;
            if (keyPath == path) {
               return L"1";
            } else {
               return L"-1";
            }
         });
      uint64 featureEnablementStatus;
      std::map<std::string, util::Variant> featureOptions;
      EXPECT_FALSE(featureOptions.contains(ALLOW_ARM_NO_ANTIKEYLOGGER_OPTION_KEY));
      commonSvc->mClientFeatureCapacity = BLOCK_KEY_LOGGER_MASK;
      commonSvc->ReadMachineAppProtectionGPO(featureEnablementStatus, featureOptions);
      EXPECT_TRUE(featureOptions.contains(ALLOW_ARM_NO_ANTIKEYLOGGER_OPTION_KEY) &&
                  featureOptions[ALLOW_ARM_NO_ANTIKEYLOGGER_OPTION_KEY].lVal == 1);
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * CommonSvcWin32UnitTest::ReadUserAppProtectionGPO --
 *
 *      Test CommonSvcWin32::ReadUserAppProtectionGPO
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CommonSvcWin32UnitTest, ReadUserAppProtectionGPO)
{
   std::shared_ptr<CommonSvcWin32UTMock> commonSvc =
      std::make_shared<::testing::NiceMock<CommonSvcWin32UTMock>>();
   ASSERT_TRUE(commonSvc != nullptr);
   typedef corestring<wchar_t> (*Fn)(HKEY, const wchar_t *, const wchar_t *);

   {
      for (const std::wstring &fakedValue : {std::wstring(L"0"), std::wstring(L"1")}) {
         uint64 featureEnablementStatus;
         std::map<std::string, util::Variant> featureOptions;
         EXPECT_FALSE(featureOptions.contains(ALLOW_ARM_NO_ANTIKEYLOGGER_OPTION_KEY));
         VMOCK(RegOpenKeyEx).WillRepeatedly(Return(ERROR_SUCCESS));
         VMOCK((Fn)&wstr::readRegistry)
            .WillRepeatedly([fakedValue](HKEY hKey, const wchar_t *name, const wchar_t *default) {
               corestring<wchar_t> keyPath = ALLOW_ARM_NO_ANTIKEYLOGGER;
               if (keyPath == name) {
                  return fakedValue.c_str();
               } else {
                  return L"-2";
               }
            });
         commonSvc->mClientFeatureCapacity = BLOCK_KEY_LOGGER_MASK;
         commonSvc->ReadUserAppProtectionGPO(featureEnablementStatus, featureOptions);
         EXPECT_TRUE(featureOptions.contains(ALLOW_ARM_NO_ANTIKEYLOGGER_OPTION_KEY) &&
                     featureOptions[ALLOW_ARM_NO_ANTIKEYLOGGER_OPTION_KEY].lVal ==
                        std::stol(fakedValue));
      }
   }
   {
      uint64 featureEnablementStatus;
      std::map<std::string, util::Variant> featureOptions;
      featureOptions[ALLOW_ARM_NO_ANTIKEYLOGGER_OPTION_KEY] = util::Variant((int32)(-1));
      VMOCK(RegOpenKeyEx).WillRepeatedly(Return(ERROR_SUCCESS));
      VMOCK((Fn)&wstr::readRegistry)
         .WillRepeatedly([](HKEY hKey, const wchar_t *name, const wchar_t *default) {
            corestring<wchar_t> keyPath = ALLOW_ARM_NO_ANTIKEYLOGGER;
            if (keyPath == name) {
               return L"-3";
            } else {
               return L"-2";
            }
         });
      commonSvc->mClientFeatureCapacity = BLOCK_KEY_LOGGER_MASK;
      commonSvc->ReadUserAppProtectionGPO(featureEnablementStatus, featureOptions);
      EXPECT_TRUE(featureOptions.contains(ALLOW_ARM_NO_ANTIKEYLOGGER_OPTION_KEY) &&
                  featureOptions[ALLOW_ARM_NO_ANTIKEYLOGGER_OPTION_KEY].lVal == -3);
   }
}