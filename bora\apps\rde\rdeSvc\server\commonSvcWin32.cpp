/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * commonSvcWin32.cpp --
 *
 *     CommonSvcWin32 class to send/receive various messages with
 *     vdpservice interface.
 *
 */

#include "shared/win32/stdafx.h"
#include "shared/commonSvcMsg.h"
#include "commonSvcWin32.h"
#include "resource.h"
#include "ScopeGuard.h"
#include "TabletModeController.h"

#include <bcrypt.h>
#include <limits.h>
#include <sigc++/bind.h>
#include <Strsafe.h>
#include "appUtil.h"
#include "ghIntegration.h"
#include "metroUtils.h"

extern "C" {
#include "impersonation.h"
#include "shared/util.h"
}

#include "log.h"
#include "rdsutils.h"
#include "str.h"
#include "winregistry.h"
#include "AppTapCommon.h"

#define ALL_THREAD_WAIT_TIME 300000 // Wait 5 minutes for all threads to finish

#define CERTSSO_UNLOCK_EVENT(sessionId)                                                            \
   tstr::printf(L"Global\\Omnissa.Horizon.RdeSvcCertSsoUnlockEvent.%u", sessionId)

#define BAT_TRAY_ID 101

#define AGENT_POLICY_PATH L"HKLM\\" HORIZON_VDM_AGENT_REG_GPO_ROOT_W L"\\Configuration"

#define AGENT_POLICY_SUFFIX HORIZON_VDM_AGENT_REG_GPO_ROOT_W L"\\Configuration"

#define LEGACY_AGENT_POLICY_PATH                                                                   \
   L"HKLM\\" LEGACY_HORIZON_VDM_AGENT_REG_GPO_ROOT_W L"\\Configuration"

#define LEGACY_AGENT_POLICY_SUFFIX LEGACY_HORIZON_VDM_AGENT_REG_GPO_ROOT_W L"\\Configuration"

#define BLOCK_SCREEN_CAPTURE_KEY L"Screen-capture Blocking"

#define ALLOW_SCREEN_RECORDING_KEY L"Allow Screen-recording"

#define ALLOW_ARM_NO_ANTIKEYLOGGER                                                                 \
   L"Allow Connections from Horizon Client for Windows without Antikeylogger service if the "      \
   L"device is ARM-based"

#define BLOCK_KEY_LOGGER_KEY L"Key Logger Blocking"

#define BLOCK_SEND_INPUT_KEY L"BlockSendInput"

#define BLOCK_THUMBNAIL_REPRESENTATION_KEY L"Thumbnail Representation Blocking"

#define ENABLE_NETWORK_STATE_DISPLAY L"EnableDisplayNetworkState"

#define NETWORK_STATE_WARNING_UI_INTERVAL L"NetworkStateWarningUIInterval"

#define USER_THEME_SUFFIX L"SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Themes\\Personalize"

#define USER_THEME_KEY L"SystemUsesLightTheme"

#define BAT_STAT_RETRY_TIMER_ID 1001
#define BAT_STAT_RETRY_DELAY_MS 1000

#define TRAY_ICON_EXISTS_TIMER_ID 1002
#define TRAY_ICON_EXISTS_DELAY_MS 5000

extern const WCHAR COMMONSVC_STOP_EVENT[] = L"Local\\CommonSvcWin32StopEvent";
extern void *CommonSvc_GetRegKeyWatcherInstance(void);

sigc::slot<bool> WaitForUserLogonSlot;


/*
 *-----------------------------------------------------------------------------
 *
 * CommonSvcWin32::CommonSvcWin32 --
 *
 *    Constructor.
 *
 * Results:
 *    None.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

CommonSvcWin32::CommonSvcWin32() :
   mDpiSyncServerWin32(this),
   mMessageFrameworkChannel(NULL),
   mMessageFrameworkStarted(false),
   mHelperThreadStopEvent(NULL),
   mUserTokenAvilableEvent(NULL),
   mUserToken(NULL),
   mTabletMode(0),
   mTabletModeController(NULL),
   mIsBatStatRedirEnabled(true),
   mIsBatTrayCreated(false),
   mIsACConnected(false),
   mAppProtectionUserGPOWatched(false),
   mAppProtectionMachineGPOWatched(false),
   mIconTimerEnabled(false),
   mBatteryLifePercent(100),
   mClientFeatureCapacity(0),
   mFeatureEnablementStatus(0),
   mIconBat(nullptr),
   mIconBatResourceId(0),
   mSystemTrayUtil(Debug_Level)
{
   Log("%s\n", __FUNCTION__);

   if ((CWindowImpl::Create(NULL, NULL, NULL, WS_POPUP)) == NULL) {
      ASSERT(FALSE);
      Log("%s: Failed to create helper window.\n", __FUNCTION__);
   }

   mHelperThreadStopEvent = CreateEvent(NULL, TRUE, FALSE, COMMONSVC_STOP_EVENT);
   if (mHelperThreadStopEvent == NULL) {
      Log("%s: Failed to create thread stop event, error = %u\n", __FUNCTION__, GetLastError());
   }

   mUserTokenAvilableEvent = CreateEvent(NULL, TRUE, FALSE, NULL);
   if (mUserTokenAvilableEvent == NULL) {
      Log("%s: Failed to create user token avilable event, error = %u\n", __FUNCTION__,
          GetLastError());
   }

   SetTabletModeSlot = sigc::mem_fun(this, &CommonSvcWin32::SetTabletMode);

   WaitForUserLogonSlot = sigc::mem_fun(this, &CommonSvcWin32::WaitForUserLogon);

   CheckFeatureEnablementAfterLogonSlot =
      sigc::mem_fun(this, &CommonSvcWin32::CheckFeatureEnablementAfterLogon);

   CheckNetworkStateEnableDisplayAfterLogonSlot =
      sigc::mem_fun(this, &CommonSvcWin32::CheckNetworkStateEnableDisplayAfterLogon);

   CheckNetworkStateIntervalAfterLogonSlot =
      sigc::mem_fun(this, &CommonSvcWin32::CheckNetworkStateIntervalAfterLogon);

#ifndef RDESERVERTEST
   GetCertSSOUnlockSlot = sigc::mem_fun(this, &CommonSvcWin32::FetchCertSSOUnlockReq);
   ExecuteAfterUserLogon(&GetCertSSOUnlockSlot);
#endif
   mFeatureEnablementStatus = 0;
   mFeatureEnablementStatus |= FEATURE_OPTION_MASK;

   if (!mAppProtectionWatcherHandler) {
      mAppProtectionWatcherHandler =
         static_cast<BaseCommonSvcRegKeyWatcherHandler *>(CommonSvc_GetRegKeyWatcherInstance());
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * CommonSvcWin32::~CommonSvcWin32 --
 *
 *    Destructor.
 *
 * Results:
 *    None.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

CommonSvcWin32::~CommonSvcWin32()
{
   Log("%s\n", __FUNCTION__);

   if (mAppProtectionWatcherHandler) {
      if (mAppProtectionUserGPOWatched) {
         mAppProtectionWatcherHandler->StopWatch(APP_PROTECTION_USER_GPO);
         mAppProtectionUserGPOWatched = false;
      }
      if (mAppProtectionMachineGPOWatched) {
         mAppProtectionWatcherHandler->StopWatch(APP_PROTECTION_MACHINE_GPO);
         mAppProtectionMachineGPOWatched = false;
      }
      mAppProtectionWatcherHandler = NULL;
   }

   // Tell the helper thread to exit, if it happened to be running
   BOOL result = SetEvent(mHelperThreadStopEvent);
   if (!result) {
      Log("%s: Failed to set thread stop event, error = %u\n", __FUNCTION__, GetLastError());
   }

   DWORD waitResult =
      WaitForMultipleObjects(mCommonSvcThreads.size(), mCommonSvcThreads.data(), TRUE, INFINITE);
   if (waitResult >= WAIT_OBJECT_0 && waitResult < (WAIT_OBJECT_0 + mCommonSvcThreads.size())) {
      Log("%s: All threads terminated as expected\n", __FUNCTION__);
   } else {
      Log("%s: Wait for all threads failed, waitResult = %u, error = %u\n", __FUNCTION__,
          waitResult, GetLastError());
   }

   if (mMessageFrameworkStarted) {
      if (mMessageFrameworkChannel) {
         MessageFrameWork::System()->CloseChannel(mMessageFrameworkChannel);
         mMessageFrameworkChannel = NULL;
      }
      MessageFrameWork::System()->Shutdown();
      mMessageFrameworkStarted = false;
   }

   for (int index = 0; index < mCommonSvcThreads.size(); index++) {
      if (mCommonSvcThreads[index] != NULL) {
         result = CloseHandle(mCommonSvcThreads[index]);
         if (!result) {
            Log("%s: Failed to close thread handle, handle = 0x%08x error = 0x%08x\n", __FUNCTION__,
                mCommonSvcThreads[index], GetLastError());
         }
         mCommonSvcThreads[index] = NULL;
      }
   }

   if (mHelperThreadStopEvent != NULL) {
      CloseHandle(mHelperThreadStopEvent);
      mHelperThreadStopEvent = NULL;
   }

   if (mUserTokenAvilableEvent != NULL) {
      CloseHandle(mUserTokenAvilableEvent);
      mUserTokenAvilableEvent = NULL;
   }

   if (mUserToken != NULL) {
      CloseHandle(mUserToken);
      mUserToken = NULL;
   }

   if (mTabletModeController) {
      delete mTabletModeController;
      mTabletModeController = NULL;
   }

   if (mIsBatTrayCreated) {
      NOTIFYICONDATA nid;
      nid.cbSize = sizeof(nid);
      nid.hWnd = m_hWnd;
      nid.uID = BAT_TRAY_ID;
      nid.uFlags = 0;
      Shell_NotifyIcon(NIM_DELETE, &nid);
      if (mIconBat) {
         DestroyIcon(mIconBat);
         mIconBat = nullptr;
      }
   }

   if (mIconTimerEnabled) {
      KillTimer(TRAY_ICON_EXISTS_TIMER_ID);
   }

   if (IsWindow()) {
      DestroyWindow();
   }

   Log("%s: End common rde service.\n", __FUNCTION__);
}


/*
 *-----------------------------------------------------------------------------
 *
 * CommonSvcWin32::WaitForUserLogon --
 *
 *    When user logon, mUserTokenAvilableEvent will be set. This function wait
 *    for mUserTokenAvilableEvent to aware the user is logon.
 *
 * Results:
 *    Returns true if user has logon or false otherwise.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

bool
CommonSvcWin32::WaitForUserLogon(void)
{
   // If SSO is disabled user needs to manually input username/pw to logon.
   // In such case we will keep waiting until user logon.
   HANDLE hEvents[] = {mUserTokenAvilableEvent, mHelperThreadStopEvent};
   DWORD result =
      WaitForMultipleObjects(sizeof(hEvents) / sizeof(hEvents[0]), hEvents, FALSE, INFINITE);
   switch (result) {
   case WAIT_OBJECT_0:
      Log("%s: Wait user token successfully.\n", __FUNCTION__);
      return true;
   case WAIT_OBJECT_0 + 1:
      Log("%s: Thread terminate.\n", __FUNCTION__);
      return false;
   default:
      Log("%s: Failed to wait user token\
             (waitCode=0x%08x, err=0x%08x)\n",
          __FUNCTION__, result, GetLastError());
      break;
   }
   return false;
}


/*
 *-----------------------------------------------------------------------------
 *
 * ExecuteInThreadProc --
 *
 *    Proc executed in a thread.
 *
 * Results:
 *    0
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

static DWORD WINAPI
ExecuteInThreadProc(LPVOID lpContext) // IN:
{
   if (!WaitForUserLogonSlot()) {
      Log("%s Failed to wait for user logon.\n", __FUNCTION__);
      return -1;
   }
   ExecuteCMDSlot *slot = reinterpret_cast<ExecuteCMDSlot *>(lpContext);
   (*slot)();
   return 0;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CommonSvcWin32::StartMessageFramework --
 *
 *    Starts mfw channel.
 *
 * Results:
 *    Returns true in case of success or false otherwise.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

bool
CommonSvcWin32::StartMessageFramework(void)
{
   mMessageFrameworkStarted = true;

   // Start mfw
   if (!MessageFrameWork::Start(NULL, 10 /*maxThreads*/, L"RdeSvc")) {
      Log("%s: MessageFrameWork::Start failed.\n", __FUNCTION__);
      return false;
   }

   if (!MessageFrameWork::Ready()) {
      Log("%s: MessageFrameWork::Ready failed.\n", __FUNCTION__);
      return false;
   }

   // Open shared memory channel to node manager
   mMessageFrameworkChannel =
      MessageFrameWork::System()->ConnectChannel(MessageFrameWork::SharedMemory);
   if (!mMessageFrameworkChannel) {
      Log("%s: Failed to connect mfw channel.\n", __FUNCTION__);
      return false;
   }

   return true;
}


/*
 *-----------------------------------------------------------------------------
 *
 *  CommonSvcWin32::ProcessEnvironmentVarInfoCommand --
 *
 *    Notification handler for environment var info messages received
 *    from View Client.
 *
 * Return value:
 *    None.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

void
CommonSvcWin32::ProcessEnvironmentVarInfoCommand(const char *pData)
{
   // Convert to utf16 from utf8
   utf16_t *buf = Unicode_GetAllocUTF16(pData);
   if (buf) {
      wchar_t *pos = wcschr(buf, L'=');
      if (pos != NULL) {
         wchar_t *envVarName = buf;
         wchar_t *envVarValue = pos + 1;
         *pos = 0;

         DWORD sid = -1;
         if (ProcessIdToSessionId(GetCurrentProcessId(), &sid)) {
            // Start MessageFramework if it's not started yet
            if (!mMessageFrameworkStarted) {
               StartMessageFramework();
            }

            if (mMessageFrameworkChannel) {
               // Create property bag with client env var name/value pair
               PropertyBag bag;
               bag.setInt(L"sessionId", sid);
               bag.set(envVarName, envVarValue);

               // Send client env variable info to wsnm plugin
               MessageFrameWork::System()->PostMsg(L"[]DesktopManager", L"UpdateEnvVars", bag);
            }
         } else {
            Log("%s: Unable to get session id, error = %d.\n", __FUNCTION__, GetLastError());
         }
      } else {
         Log("%s: Invalid env var buffer received: %S.\n", __FUNCTION__, buf);
      }
      free(buf);
   } else {
      Log("%s: Failed to convert env var '%s' buffer to utf16.\n", __FUNCTION__, pData);
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 *  CommonSvcWin32::OnObjectStateChanged --
 *
 *    Notification handler when object state is changed.
 *
 * Return value:
 *    None.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

void
CommonSvcWin32::OnObjectStateChanged()
{
   VDPRPC_ObjectState state = GetObjectState();
   mDpiSyncServerWin32.OnObjectStateChanged(state);
   switch (state) {
   case VDP_RPC_OBJ_CONNECTED: {
      Log("%s: vdp channel connected.\n", __FUNCTION__);
      std::string sessionType = GetSessionData("ViewClient_Launch_SessionType");

      if (stricmp(sessionType.c_str(), "DESKTOP")) {
         Log("%s: battery state redirection disabled for app session\n", __FUNCTION__);
         mIsBatStatRedirEnabled = false;
         mIconTimerEnabled = true;
      }

      if (mIconTimerEnabled && AppUtil_IsHorizonUWPEnabled()) {
         Log("%s: Setting timer for tray icon exit check\n", __FUNCTION__);
         SetTimer(TRAY_ICON_EXISTS_TIMER_ID, TRAY_ICON_EXISTS_DELAY_MS);
      }

      wstr enableBatStatRedirStr = wstr::readRegistryPolicyOverride(
         utils::regPath() + L"\\Agent\\Configuration\\EnableBatStatRedir", L"true");
      if (!enableBatStatRedirStr.comparei(L"false")) {
         Log("%s: battery state redirection disabled by policy\n", __FUNCTION__);
         mIsBatStatRedirEnabled = false;
      }
      if (mIsBatStatRedirEnabled) {
         SendServerBatStateVersion();
      }
      CreateGetUserTokenThread();
      NotifyRdsAadAuthConnState(true);
      break;
   }
   case VDP_RPC_OBJ_DISCONNECTED:
      Log("%s: vdp channel disconnected.\n", __FUNCTION__);
      NotifyRdsAadAuthConnState(false);
      break;

   default:
      break;
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 *  CommonSvcWin32::ProcessDpiSyncCommand --
 *
 *    Notification handler for DPI Sync messages received from View Client.
 *
 * Return value:
 *    None.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

void
CommonSvcWin32::ProcessDpiSyncCommand(DpiSyncCommand *command) // IN
{
   mDpiSyncServerWin32.OnDpiSyncCommand(command);
}


/*
 *-----------------------------------------------------------------------------
 *
 *  CommonSvcWin32::ProcessDisplayCommand --
 *
 *    Notification handler for Display messages received from View Client.
 *
 * Return value:
 *    None.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

void
CommonSvcWin32::ProcessDisplayCommand(DisplayCommand *command) // IN
{
   mDpiSyncServerWin32.OnDisplayCommand(command);
}


/*
 *-----------------------------------------------------------------------------
 *
 *  CommonSvcWin32::ExecuteAfterUserLogon --
 *
 *    Execute slot after user logon. If need to wait for user's logon,
 *    then wait in another thread.
 *
 * Return value:
 *    None.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

void
CommonSvcWin32::ExecuteAfterUserLogon(ExecuteCMDSlot *slot) // IN
{
   DWORD tid = 0;
   HANDLE h = CreateThread(NULL, 0, ExecuteInThreadProc, slot, 0, &tid);

   if (h == NULL) {
      Log("%s: Failed to create thread, error = %u\n", __FUNCTION__, GetLastError());
   } else {
      mCommonSvcThreads.push_back(h);
      Log("%s:Create a thread to execute. handle = 0x%08x, tid=%d .\n", __FUNCTION__, h, tid);
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * CommonSvcWin32::SetTabletMode --
 *
 *    Set tablet mode.
 *
 * Results:
 *    Set agent tablet mode to mTabletMode.
 *
 * Side effects:
 *    None
 *-----------------------------------------------------------------------------
 */

void
CommonSvcWin32::SetTabletMode()
{
   Log("%s\n", __FUNCTION__);

   CAccessToken userToken;
   userToken.Attach(mUserToken);

   if (!mTabletModeController) {
      mTabletModeController = new TabletModeController();
   }

   ImpersonatedSlot slot =
      sigc::bind(sigc::mem_fun(mTabletModeController, &TabletModeController::SetTabletMode),
                 sigc::ref(mTabletMode));

   Bool result = DoAsImpersonated(userToken, slot);
   userToken.Detach();
   if (!result) {
      Log("%s: Failed to set tablet mode for user\n", __FUNCTION__);
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 *  CommonSvcWin32::ProcessTabletModeCommand --
 *
 *    Notification handler for Tablet Mode messages received from View Client.
 *
 * Return value:
 *    None.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

void
CommonSvcWin32::ProcessTabletModeCommand(TabletModeCommand *command) // IN:
{
   switch (command->commandType) {
   case TABLET_MODE_COMMAND_SET: {
      mTabletMode = command->data.tabletMode;
      Log("%s: required tablet mode is = %u\n", __FUNCTION__, mTabletMode);
      ExecuteAfterUserLogon(&SetTabletModeSlot);
   } break;
   default:
      Log("%s: Unknown command type %u.\n", __FUNCTION__, command->commandType);
      break;
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * GetUserTokenThread --
 *
 *    Helper thread entry function to wait for user token to become available.
 *    Defers all the work to class member function.
 *
 * Results:
 *    Calls class member function to do actual work.
 *
 * Side effects:
 *    None
 *-----------------------------------------------------------------------------
 */

static DWORD WINAPI
GetUserTokenThread(LPVOID lpContext) // IN:
{
   Log("%s: Waiting for user token\n", __FUNCTION__);

   CommonSvcWin32 *pCommonSvcWin32 = reinterpret_cast<CommonSvcWin32 *>(lpContext);
   if (pCommonSvcWin32 == NULL) {
      Log("%s: Invalid thread function parameter: NULL\n", __FUNCTION__);
      return 0;
   }

   pCommonSvcWin32->GetUserToken();

   return 0;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CommonSvcWin32::GetUserToken --
 *
 *    Waits for user token to become available. Once user token becomes available,
 *    we use the token to query registry.
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *-----------------------------------------------------------------------------
 */

HANDLE
CommonSvcWin32::GetUserToken()
{
   DWORD waitResult = WaitForSingleObject(mHelperThreadStopEvent, 0);

   while (waitResult == WAIT_TIMEOUT && mUserToken == NULL) {
      mUserToken = RdeGetImpersonatedTokenAsLogonUser();
      if (mUserToken != NULL) {
         Log("%s:Get user token successfully.\n", __FUNCTION__);
         // Signal other threads wait for user logon
         BOOL result = SetEvent(mUserTokenAvilableEvent);
         if (!result) {
            Log("%s: Failed to set user token avilable event, error = %u\n", __FUNCTION__,
                GetLastError());
         }
      } else {
         waitResult = WaitForSingleObject(mHelperThreadStopEvent, 500);
      }
   }
   return mUserToken;
}


/*
 *-----------------------------------------------------------------------------
 *
 *  CommonSvcWin32::CreateGetUserTokenThread --
 *
 *    Wait for token to become available on a separate thread
 *
 * Return value:
 *    None.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

void
CommonSvcWin32::CreateGetUserTokenThread()
{
   Log("%s\n", __FUNCTION__);

   HANDLE helperThread = CreateThread(NULL, 0, GetUserTokenThread, this, 0, NULL);
   if (helperThread == NULL) {
      Log("%s: Failed to create helper thread, error = %u\n", __FUNCTION__, GetLastError());
   }

   mCommonSvcThreads.push_back(helperThread);
}


/*
 *----------------------------------------------------------------------------
 *
 * CommonSvcWin32::OnDisplayChange --
 *
 *    WM_DISPLAYCHANGE handler.
 *    When resolution is changed, the monitor Dpi will roll back to system.
 *    Ensures that adjust DPI when resolution changed, and make the dpi effective.
 *
 * Results:
 *    0.
 *
 * Side effects:
 *    None.
 *
 *----------------------------------------------------------------------------
 */

LRESULT
CommonSvcWin32::OnDisplayChange(UINT uMsg, WPARAM wParam, LPARAM lParam, BOOL &bHandled)
{
   static bool inDisplayChange = false;
   if (inDisplayChange) {
      Log("%s: ignore re-entered display change\n", __FUNCTION__);
      return 0;
   }
   inDisplayChange = true;
   Log("%s: Display changed\n", __FUNCTION__);
   mDpiSyncServerWin32.OnDisplayChange(HIWORD(lParam), LOWORD(lParam));
   inDisplayChange = false;
   return 0;
}


/*
 *----------------------------------------------------------------------------
 *
 * CommonSvcWin32::OnTimer --
 *
 *    WM_TIMER handler.
 *    A retry mechnism is implemented for the case failed to create system
 *    tray.
 *
 * Results:
 *    0.
 *
 * Side effects:
 *    None.
 *
 *----------------------------------------------------------------------------
 */

LRESULT
CommonSvcWin32::OnTimer(UINT uMsg,      // IN
                        WPARAM wParam,  // IN
                        LPARAM lParam,  // IN
                        BOOL &bHandled) // OUT
{
   if (wParam == BAT_STAT_RETRY_TIMER_ID) {
      KillTimer(BAT_STAT_RETRY_TIMER_ID);
      BatteryStateCommand cmd;
      cmd.commandType = BAT_STAT_CMD_STAT;
      cmd.data.stat.isACConnected = mIsACConnected;
      cmd.data.stat.batteryLifePercent = mBatteryLifePercent;
      ProcessBatStateCommand(&cmd);
      bHandled = TRUE;
   } else if (wParam == TRAY_ICON_EXISTS_TIMER_ID) {
      mSystemTrayUtil.CheckIconsLaunchedByUser(GHI_GetTrayIconHandles());
   }
   return 0;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CommonSvcWin32::OnDWMColorizationColorChanged --
 *
 *    Handler for the notification when the top-level DWM colorization color
 *    changes in Windows. Calls logic to determine whether the dark mode theme
 *    value has changed.
 *
 * Results:
 *    0.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

LRESULT
CommonSvcWin32::OnDWMColorizationColorChanged(UINT uMsg,      // IN:
                                              WPARAM wParam,  // IN:
                                              LPARAM lParam,  // IN:
                                              BOOL &bHandled) // IN/OUT:
{
   if (mIsBatTrayCreated) {
      BatteryStateCommand cmd;
      cmd.commandType = BAT_STAT_CMD_STAT;
      cmd.data.stat.isACConnected = mIsACConnected;
      cmd.data.stat.batteryLifePercent = mBatteryLifePercent;
      ProcessBatStateCommand(&cmd);
   }

   bHandled = FALSE;
   return 0;
}


/*
 *-----------------------------------------------------------------------------
 *
 *  CommonSvcWin32::InitEventForCertSSOUnlock --
 *
 *    Creates a named event in the Global namespace. The event is spaced via
 *    session id - making it unique for each session.
 *    We create it in the Global namespace as DesptopManager running in Node
 *    Manager process (as a service) will be triggering it
 *
 * Return value:
 *    HANDLE - event handle on success or NULL on failure.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

HANDLE
CommonSvcWin32::InitEventForCertSSOUnlock(DWORD sessionId) // IN
{
   HANDLE hRetval = NULL;
   SECURITY_ATTRIBUTES sAttr;
   SECURITY_DESCRIPTOR sDesc;
   tstr eventName = CERTSSO_UNLOCK_EVENT(sessionId);

   if (sessionId == 0) {
      Log("%s: Invalid sessionId\n", __FUNCTION__);
      goto exit;
   }

   ZeroMemory(&sDesc, sizeof(sDesc));
   ZeroMemory(&sAttr, sizeof(sAttr));

   if (!InitializeSecurityDescriptor(&sDesc, SECURITY_DESCRIPTOR_REVISION)) {
      Log("%s: InitializeSecurityDescriptor failed, Error = %s\n", __FUNCTION__,
          tstr::formatError()._mstr().c_str());
      goto exit;
   }

   if (!SetSecurityDescriptorDacl(&sDesc, TRUE, (PACL)NULL, FALSE)) {
      Log("%s: SetSecurityDescriptorDacl failed, Error = %s\n", __FUNCTION__,
          tstr::formatError()._mstr().c_str());
      goto exit;
   }

   sAttr.nLength = sizeof(sAttr);
   sAttr.lpSecurityDescriptor = &sDesc;
   sAttr.bInheritHandle = FALSE;

   hRetval = CreateEvent(&sAttr, FALSE, FALSE, eventName.c_str());
   if (hRetval == NULL) {
      Log("%s: Failed to create CertSSO unlock event (%s), Error = %s\n", __FUNCTION__,
          eventName.c_str(), tstr::formatError()._mstr().c_str());
   }

exit:
   return hRetval;
}


/*
 *-----------------------------------------------------------------------------
 *
 *  CommonSvcWin32::FetchCertSSOUnlockReq --
 *
 *    Helper function to query Desktop Manager the SessionGuid/Ticket for the
 *    session to be unlocked. This is passed to the Client
 *
 * Return value:
 *    None.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

void
CommonSvcWin32::FetchCertSSOUnlockReq()
{
   Log("%s\n", __FUNCTION__);

   HANDLE certSSOUnlockEventHnd = NULL;
   DWORD sessionId = 0;
   CAccessToken userToken;
   userToken.Attach(mUserToken);

   if (!ProcessIdToSessionId(GetCurrentProcessId(), &sessionId)) {
      Log("%s: Failed to retrieve session id, error = %u\n", __FUNCTION__, GetLastError());
      goto exit;
   } else {
      Log("%s: Current session id = %u\n", __FUNCTION__, sessionId);
   }

   certSSOUnlockEventHnd = InitEventForCertSSOUnlock(sessionId);
   if (!certSSOUnlockEventHnd) {
      goto exit;
   }

   // Start MessageFramework if it's not started yet
   if (!mMessageFrameworkStarted) {
      StartMessageFramework();
   }

   do {
      if (!WaitForExitOrEvent(certSSOUnlockEventHnd)) {
         Log("%s Failed to wait for CertSSO unlock or Thread exiting.\n", __FUNCTION__);
         goto exit;
      }

      if (!mMessageFrameworkChannel) {
         Log("%s Mfw not initialized.\n", __FUNCTION__);
         continue;
      }

      // Create property bag with client env var name/value pair
      PropertyBag bag;
      PropertyBag resp;

      bag.setInt(L"sessionId", sessionId);

      // Send client env variable info to wsnm plugin
      MessageHandler::respType result = MessageFrameWork::System()->SendMsg(
         L"[]DesktopManager", L"GetCertSsoUnlockReq", bag, resp);

      if (result == MessageHandler::MsgOk) {
         tstr sessionGuid = resp.get(TEXT("sessionGuid"), TEXT(""));
         tstr ticket = resp.get(TEXT("ticket"), TEXT(""));

         if (!sessionGuid.empty() && !ticket.empty()) {
            Log("%s Sending CertSSO Unlock request to Client, SessionGuid: %s, Ticket: %s\n",
                __FUNCTION__, sessionGuid._mstr().c_str(), ticket._mstr().c_str());
            SendCertSSOUnlockMsgToClient(sessionGuid, ticket);
         } else {
            if (sessionGuid.empty()) {
               Log("%s Failed as sessionGuid is empty.\n", __FUNCTION__);
            }
            if (ticket.empty()) {
               Log("%s Failed as ticket is empty.\n", __FUNCTION__);
            }
         }
      } else {
         Log("%s Failed to send GetCertSsoUnlockReq to node manager, Error: (%d) %s\n",
             __FUNCTION__, resp.getError(), resp.getErrorText()._mstr().c_str());
      }
   } while (true);

exit:
   if (certSSOUnlockEventHnd) {
      CloseHandle(certSSOUnlockEventHnd);
      certSSOUnlockEventHnd = NULL;
   }
   userToken.Detach();
}


/*
 *-----------------------------------------------------------------------------
 *
 *  CommonSvcWin32::OnInvoked --
 *
 * Return value:
 *    None.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

void
CommonSvcWin32::OnInvoked(void *contextHandle)
{
   if (HandleRdsAadAuthInvoked(contextHandle)) {
      return;
   }

   /*
    * Pass unhandled requests to base
    */
   CommonSvc::OnInvoked(contextHandle);
}


/*
 *-----------------------------------------------------------------------------
 *
 *  CommonSvcWin32::WaitForExitOrEvent --
 *
 *    Here we wait for an event trigger.
 *
 * Return value:
 *    true if CertSSO event is triggered.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

bool
CommonSvcWin32::WaitForExitOrEvent(HANDLE eventToWait) // IN
{
   HANDLE hEvents[] = {mHelperThreadStopEvent, eventToWait};
   DWORD result =
      WaitForMultipleObjects(sizeof(hEvents) / sizeof(hEvents[0]), hEvents, FALSE, INFINITE);

   switch (result) {
   case WAIT_OBJECT_0:
      Log("%s: ThreadStop event triggered.\n", __FUNCTION__);
      return false;
   case WAIT_OBJECT_0 + 1:
      Log("%s: Waited upon event triggered.\n", __FUNCTION__);
      return true;
   default:
      Log("%s: Failed to wait on events \
             (waitCode=0x%08x, %s)\n",
          __FUNCTION__, result, tstr::formatError()._mstr().c_str());
      break;
   }
   return false;
}


/*
 *-----------------------------------------------------------------------------
 *
 *  CommonSvcWin32::SendCertSSOUnlockMsgToClient --
 *
 *    Utility to relay the message to RdeClient.
 *
 * Return value:
 *    None.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

void
CommonSvcWin32::SendCertSSOUnlockMsgToClient(tstr &sessionGuid, // IN
                                             tstr &ticket)      // IN
{
   CertSSOCommand command = {0};
   command.commandType = CERTSSO_UNLOCK_COMMAND;

   char *sessionGUID = (char *)&(command.data.sessionInfo.sessionGUID[0]);
   char *ticketGUID = (char *)&(command.data.sessionInfo.ticketGUID[0]);

   int res = _snprintf_s(sessionGUID, sizeof(command.data.sessionInfo.sessionGUID), _TRUNCATE,
                         sessionGuid._mstr().c_str());
   if (res == -1) { // truncation occured
      Log("%s: CertSSO Unlock Command received invalid SessionGuid\n", __FUNCTION__);
      return;
   }

   res = _snprintf_s(ticketGUID, sizeof(command.data.sessionInfo.ticketGUID), _TRUNCATE,
                     ticket._mstr().c_str());
   if (res == -1) { // truncation occured
      Log("%s: CertSSO Unlock Command received invalid SessionGuid\n", __FUNCTION__);
      return;
   }

   bool result = SendMsg(CERTSSO_UNLOCK_MSG, 0, NULL, reinterpret_cast<const char *>(&command),
                         sizeof(command));
   if (!result) {
      Log("%s: Failed to send CertSSO Unlock\n", __FUNCTION__);
   } else {
      Log("%s: Sent CertSSO Unlock\n", __FUNCTION__);
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 *  CommonSvcWin32::ProcessBatStateCommand --
 *
 *    Notification handler for battery state messages received from Horizon
 *    Client.
 *
 * Return value:
 *    None.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

void
CommonSvcWin32::ProcessBatStateCommand(BatteryStateCommand *command) // IN
{
   if (!mIsBatStatRedirEnabled) {
      return;
   }
   switch (command->commandType) {
   case BAT_STAT_CMD_VERSION:
      Log("%s: receive battery state client version %u\n", __FUNCTION__, command->data.version);
      break;
   case BAT_STAT_CMD_STAT: {
      NOTIFYICONDATA nid;
      bool isACConnected = command->data.stat.isACConnected;
      uint32 batteryLifePercent = command->data.stat.batteryLifePercent;
      HINSTANCE hInst = _AtlBaseModule.GetResourceInstance();
      DWORD message = NIM_MODIFY;
      if (!mIsBatTrayCreated) {
         message = NIM_ADD;
         mIsBatTrayCreated = true;
      }
      nid.cbSize = sizeof(nid);
      nid.hWnd = m_hWnd;
      nid.uID = BAT_TRAY_ID;
      nid.uFlags = NIF_ICON | NIF_TIP;
      WCHAR formatBuf[ARRAYSIZE(nid.szTip)];
      if (isACConnected) {
         if (batteryLifePercent == 100) {
            LoadString(hInst, IDS_BAT_FULLY_CHARGED_TIP, nid.szTip, ARRAYSIZE(nid.szTip));
         } else {
            LoadString(hInst, IDS_BAT_CHARGING_TIP_FORMAT, formatBuf, ARRAYSIZE(nid.szTip));
            wsprintf(nid.szTip, formatBuf, batteryLifePercent);
         }
      } else {
         LoadString(hInst, IDS_BAT_TIP_FORMAT, formatBuf, ARRAYSIZE(nid.szTip));
         wsprintf(nid.szTip, formatBuf, batteryLifePercent);
      }

      bool isLightTheme = false;
      CAccessToken userToken;
      userToken.Attach(mUserToken);
      auto impersonated = userToken.ImpersonateLoggedOnUser();
      if (!impersonated) {
         Log("%s: Unable to impersonate as logged on user. Error: 0x%x.\n", __FUNCTION__,
             GetLastError());
         mIsBatTrayCreated = false;
         SetTimer(BAT_STAT_RETRY_TIMER_ID, BAT_STAT_RETRY_DELAY_MS);
         mIsACConnected = isACConnected;
         mBatteryLifePercent = batteryLifePercent;
         userToken.Detach();
         return;
      }

      HKEY hKeyUserRoot;
      auto status = RegOpenCurrentUser(KEY_READ, &hKeyUserRoot);
      if (status != ERROR_SUCCESS) {
         Log("%s: RegOpenCurrentUser failed, error: %d.", __FUNCTION__, status);
      } else {
         HKEY hKey;
         status = RegOpenKeyEx(hKeyUserRoot, USER_THEME_SUFFIX, 0, KEY_QUERY_VALUE, &hKey);
         if (status != ERROR_SUCCESS) {
            Log("%s: RegOpenKeyEx failed for user settings, error: %d.", __FUNCTION__, status);
         } else {
            isLightTheme = wstr::readRegistry(hKey, USER_THEME_KEY, L"0").toBool();
         }
         RegCloseKey(hKey);
      }
      RegCloseKey(hKeyUserRoot);

      userToken.Revert();
      userToken.Detach();

#define LOAD_ICON(_TYPE)                                                                           \
   int resourceId = IDI_BATTERY_STAT_##_TYPE;                                                      \
   if (IsMaximumWindowsVersion(7) || isLightTheme) {                                               \
      resourceId = IDI_BATTERY_STAT_##_TYPE##_DARK;                                                \
   }                                                                                               \
   if (resourceId != mIconBatResourceId || !mIconBat) {                                            \
      if (mIconBat) {                                                                              \
         DestroyIcon(mIconBat);                                                                    \
         mIconBat = nullptr;                                                                       \
      }                                                                                            \
      if (FAILED(LoadIconMetric(hInst, MAKEINTRESOURCE(resourceId), LIM_LARGE, &mIconBat))) {      \
         Log("%s: failed on LoadIconMetric for icon " #_TYPE, __FUNCTION__);                       \
         mIconBat = LoadIcon(hInst, MAKEINTRESOURCE(resourceId));                                  \
      }                                                                                            \
      mIconBatResourceId = resourceId;                                                             \
   }                                                                                               \
   nid.hIcon = mIconBat;
      if (isACConnected) {
         LOAD_ICON(CHARGE)
      } else if (batteryLifePercent == 100) {
         LOAD_ICON(FULL)
      } else if (batteryLifePercent >= 66) {
         LOAD_ICON(HIGH)
      } else if (batteryLifePercent >= 33) {
         LOAD_ICON(MEDIUM)
      } else if (batteryLifePercent >= 10) {
         LOAD_ICON(LOW)
      } else {
         LOAD_ICON(CRITICAL)
      }
#undef LOAD_ICON
      if (!isACConnected && mBatteryLifePercent >= 10 && batteryLifePercent < 10) {
         nid.uFlags |= NIF_INFO;
         nid.dwInfoFlags = NIIF_WARNING;
         LoadString(hInst, IDS_BAT_LOW_WARNING, nid.szInfo, sizeof(nid.szInfo) / sizeof(WCHAR));
         LoadString(hInst, IDS_BAT_LOW_WARNING_TITLE, nid.szInfoTitle,
                    sizeof(nid.szInfoTitle) / sizeof(WCHAR));
      }
      if (!Shell_NotifyIcon(message, &nid)) {
         /*
          * For the session just logged in, Shell_NotifyIcon may fail, we
          * use timer to create system tray later.
          *
          * When SSO is disabled and user never logon the desktop, Shell_NotifyIcon
          * will fail, then we will print the log all the time. Add this check to
          * avoid this issue.
          */
         if (mUserToken != NULL) {
            Log("%s: failed on Shell_NotifyIcon, GLE=0x%x\n", __FUNCTION__, GetLastError());
         }
         mIsBatTrayCreated = false;
         SetTimer(BAT_STAT_RETRY_TIMER_ID, BAT_STAT_RETRY_DELAY_MS);
      }
      mIsACConnected = isACConnected;
      mBatteryLifePercent = batteryLifePercent;
   } break;
   default:
      Log("%s: Unknown command type %u.\n", __FUNCTION__, command->commandType);
      break;
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 *  CommonSvcWin32::ProcessFeatureEnablementCommand --
 *
 *    Notification handler for feature enablement messages received from
 *    Horizon Client.
 *
 * Return value:
 *    None.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

void
CommonSvcWin32::ProcessFeatureEnablementCommand(FeatureEnablementCommand *command) // IN
{
   switch (command->commandType) {
   case FEATURE_ENABLEMENT_CMD_CAPACITY: {
      Log("%s: receive feature enablement capacity %u\n", __FUNCTION__, command->data.capacity);
      mClientFeatureCapacity = command->data.capacity;
      if (!mAppProtectionMachineGPOWatched) {
         mAppProtectionMachineGPOWatched = true;
         Log("%s: start to watch the app protection machine registry key.\n", __FUNCTION__);
         mAppProtectionWatcherHandler->StartWatch(m_hWnd, APP_PROTECTION_MACHINE_GPO);
      }
      /*
       * Check local machine GPO in case SSO is disabled;
       * we don't know when user will login; use local machine GPO first.
       */
      ReadMachineAppProtectionGPO(mFeatureEnablementStatus, mFeatureOptions);
      SendFeatureEnablementInfo(FEATURE_ENABLEMENT_CMD_STATUS, mFeatureEnablementStatus,
                                mFeatureOptions);

      // Check current user GPO.
      ExecuteAfterUserLogon(&CheckFeatureEnablementAfterLogonSlot);
      break;
   }
   default: {
      Log("%s: Unknown command type %u.\n", __FUNCTION__, command->commandType);
      break;
   }
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 *  CommonSvcWin32::CheckFeatureEnablementAfterLogon --
 *
 *    Check feature enablement status after logon.
 *    Current user is preferred with Local machine.
 *
 * Return value:
 *    True if succeed, false otherwise.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

void
CommonSvcWin32::CheckFeatureEnablementAfterLogon()
{
   /*
    * It will trigger OnAppProtectionRegistryChanged manually to reload user
    * GPO in watcher starting.
    */
   ::PostMessage(m_hWnd, APP_PROTECTION_USER_GPO_READY, 0, 0);
}


/*
 *-----------------------------------------------------------------------------
 *
 *  CommonSvcWin32::ProcessNetworkStateGPOCommand --
 *
 *    Notification handler for network state GPO received from Horizon Client.
 *
 * Return value:
 *    None.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

void
CommonSvcWin32::ProcessNetworkStateGPOCommand(NetworkStateGPOCommand *command) // IN
{
   switch (command->commandType) {
   case NETWORK_STATE_GPO_CMD_REQUEST_ENABLE_DISPLAY: {
      Log("%s: receive network state request enable display message %d\n", __FUNCTION__,
          command->data.enableDisplay);

      ExecuteAfterUserLogon(&CheckNetworkStateEnableDisplayAfterLogonSlot);
      break;
   }
   case NETWORK_STATE_GPO_CMD_REQUEST_INTERVAL: {
      Log("%s: receive network state interval message %u\n", __FUNCTION__, command->data.interval);

      ExecuteAfterUserLogon(&CheckNetworkStateIntervalAfterLogonSlot);
      break;
   }
   default: {
      Log("%s: Unknown command type %u.\n", __FUNCTION__, command->commandType);
      break;
   }
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 *  CommonSvcWin32::CheckNetworkStateEnableDisplayAfterLogon --
 *
 *    Check network state enable display policy value after logon.
 *    Though this policy value is only configured in Local machine, we intend
 *    to get this policy after logon.
 *
 * Return value:
 *    None.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

void
CommonSvcWin32::CheckNetworkStateEnableDisplayAfterLogon()
{
   CAccessToken userToken;
   userToken.Attach(RdeGetImpersonatedTokenAsLogonUser());
   if (userToken.GetHandle() == nullptr) {
      Log("%s: Failed to retrieve user token", __FUNCTION__);
      return;
   }

   auto impersonated = userToken.ImpersonateLoggedOnUser();
   if (!impersonated) {
      Log("%s: Unable to impersonate as logged on user. Error: 0x%x.\n", __FUNCTION__,
          GetLastError());
      userToken.Detach();
      return;
   }
   ON_BLOCK_EXIT([&]() {
      userToken.Revert();
      userToken.Detach();
   });

   bool enableDisplay = true;
   wstr enableDisplayStr = wstr::readRegistryPolicyOverride(
      utils::regPath() + L"\\Agent\\Configuration\\EnableDisplayNetworkState", L"true");
   if (!enableDisplayStr.comparei(L"false")) {
      enableDisplay = false;
   }

   Log("%s, the GPO of enable display network state warning UI is %d\n", __FUNCTION__,
       enableDisplay);
   SendNetworkStateGPOInfo(NETWORK_STATE_GPO_CMD_RESPOND_ENABLE_DISPLAY, enableDisplay);
}


/*
 *-----------------------------------------------------------------------------
 *
 *  CommonSvcWin32::CheckNetworkStateIntervalAfterLogon --
 *
 *    Check network state interval policy value after logon.
 *    Though this policy value is only configured in Local machine, we intend
 *    to get this policy after logon.
 *
 * Return value:
 *    None.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

void
CommonSvcWin32::CheckNetworkStateIntervalAfterLogon()
{
   CAccessToken userToken;
   userToken.Attach(RdeGetImpersonatedTokenAsLogonUser());
   if (userToken.GetHandle() == nullptr) {
      Log("%s: Failed to retrieve user token", __FUNCTION__);
      return;
   }

   auto impersonated = userToken.ImpersonateLoggedOnUser();
   if (!impersonated) {
      Log("%s: Unable to impersonate as logged on user. Error: 0x%x.\n", __FUNCTION__,
          GetLastError());
      userToken.Detach();
      return;
   }
   ON_BLOCK_EXIT([&]() {
      userToken.Revert();
      userToken.Detach();
   });

   wstr intervalStr = wstr::readRegistryPolicyOverride(
      utils::regPath() + L"\\Agent\\Configuration\\NetworkStateWarningUIInterval",
      AGENT_POLICY_PATH L"\\" NETWORK_STATE_WARNING_UI_INTERVAL, L"5");
   uint32 interval = intervalStr.toInt();
   Log("%s, the GPO of network state warning UI interval is %u\n", __FUNCTION__, interval);

   SendNetworkStateGPOInfo(NETWORK_STATE_GPO_CMD_RESPOND_INTERVAL, interval);
}


/*
 *-----------------------------------------------------------------------------
 *
 *  CommonSvcWin32::EnsureMessageFrameworkInitialized --
 *
 *    Utility function used by RdsAadAuth lib to ensure that MFW has been
 *    async. initialized by this class.
 *
 * Return value:
 *    None.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

bool
CommonSvcWin32::EnsureMessageFrameworkInitialized()
{
   if (!mMessageFrameworkStarted) {
      StartMessageFramework();
   }
   return true;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CommonSvcWin32::ReadMachineAppProtectionGPO --
 * CommonSvcWin32::ReadUserAppProtectionGPO --
 *
 *       Read machine/user app protection GPO
 *
 * Returns:
 *       None.
 *
 * Side effects:
 *       None.
 *
 *-----------------------------------------------------------------------------
 */

void
CommonSvcWin32::ReadMachineAppProtectionGPO(
   uint64 &featureEnablementStatus,                      // IN & OUT
   std::map<std::string, util::Variant> &featureOptions) // IN & OUT
{
   bool readLegacyGPO = wstr::fallbackToLegacyEnabled();
   if (mClientFeatureCapacity & BLOCK_SCREEN_CAPTURE_MASK) {
      int regValue =
         wstr::readRegistry(AGENT_POLICY_PATH L"\\" BLOCK_SCREEN_CAPTURE_KEY, L"-1").toInt();
      Log("%s, current machine block screen capture GPO %d\n", __FUNCTION__, regValue);
      bool readFromLegacy = false;
      if (regValue == -1 && readLegacyGPO) {
         readFromLegacy = true;
         regValue =
            wstr::readRegistry(LEGACY_AGENT_POLICY_PATH L"\\" BLOCK_SCREEN_CAPTURE_KEY, L"-1")
               .toInt();
         Log("%s, current machine legacy block screen capture GPO %d\n", __FUNCTION__, regValue);
      }
      if (regValue != -1 && regValue != 0) {
         featureEnablementStatus |= BLOCK_SCREEN_CAPTURE_MASK;
      }

      int32 allowScreenRecording =
         wstr::readRegistry(AGENT_POLICY_PATH L"\\" ALLOW_SCREEN_RECORDING_KEY, L"0").toInt();
      if (readFromLegacy) {
         allowScreenRecording =
            wstr::readRegistry(LEGACY_AGENT_POLICY_PATH L"\\" ALLOW_SCREEN_RECORDING_KEY, L"0")
               .toInt();
      }
      featureOptions[ALLOW_SCREEN_RECORDING_OPTION_KEY] = util::Variant(allowScreenRecording);
      Log("%s, value %d, current machine %s screen recording\n", __FUNCTION__, allowScreenRecording,
          featureOptions[ALLOW_SCREEN_RECORDING_OPTION_KEY].lVal ? "allows" : "disallows");
   }
   if (mClientFeatureCapacity & BLOCK_KEY_LOGGER_MASK) {
      int regValue =
         wstr::readRegistry(AGENT_POLICY_PATH L"\\" BLOCK_KEY_LOGGER_KEY, L"-1").toInt();
      Log("%s, current machine block key logger GPO %d\n", __FUNCTION__, regValue);
      if (regValue == -1 && readLegacyGPO) {
         regValue =
            wstr::readRegistry(LEGACY_AGENT_POLICY_PATH L"\\" BLOCK_KEY_LOGGER_KEY, L"-1").toInt();
         Log("%s, current machine legacy block key logger GPO %d\n", __FUNCTION__, regValue);
      }
      if (regValue != -1 && regValue != 0) {
         featureEnablementStatus |= BLOCK_KEY_LOGGER_MASK;
      }

      int32 allowArmNoAntiKeylogger =
         wstr::readRegistry(AGENT_POLICY_PATH L"\\" ALLOW_ARM_NO_ANTIKEYLOGGER, L"0").toInt();
      featureOptions[ALLOW_ARM_NO_ANTIKEYLOGGER_OPTION_KEY] =
         util::Variant(allowArmNoAntiKeylogger);
      Log("%s, value %d, current machine %s connection from windows ARM without"
          " AntiKeylogger.\n",
          __FUNCTION__, allowArmNoAntiKeylogger,
          featureOptions[ALLOW_ARM_NO_ANTIKEYLOGGER_OPTION_KEY].lVal ? "allows" : "disallows");
   }
   if (mClientFeatureCapacity & BLOCK_SEND_INPUT_MASK) {
      int regValue =
         wstr::readRegistry(AGENT_POLICY_PATH L"\\" BLOCK_SEND_INPUT_KEY, L"-1").toInt();
      Log("%s, current machine block SendInput() GPO %d\n", __FUNCTION__, regValue);
      if (regValue == -1 && readLegacyGPO) {
         regValue =
            wstr::readRegistry(LEGACY_AGENT_POLICY_PATH L"\\" BLOCK_SEND_INPUT_KEY, L"-1").toInt();
         Log("%s, current machine legacy block SendInput() GPO %d\n", __FUNCTION__, regValue);
      }
      if (regValue != -1 && regValue != 0) {
         featureEnablementStatus |= BLOCK_SEND_INPUT_MASK;
      }
   }
   if (mClientFeatureCapacity & BLOCK_THUMBNAIL_REPRESENTATION_MASK) {
      int regValue =
         wstr::readRegistry(AGENT_POLICY_PATH L"\\" BLOCK_THUMBNAIL_REPRESENTATION_KEY, L"-1")
            .toInt();
      Log("%s, current machine thumbnail representation blocking GPO %d\n", __FUNCTION__, regValue);
      if (regValue == -1 && readLegacyGPO) {
         regValue = wstr::readRegistry(
                       LEGACY_AGENT_POLICY_PATH L"\\" BLOCK_THUMBNAIL_REPRESENTATION_KEY, L"-1")
                       .toInt();
         Log("%s, current machine legacy thumbnail representation blocking "
             "GPO %d\n",
             __FUNCTION__, regValue);
      }
      if (regValue != -1 && regValue != 0) {
         featureEnablementStatus |= BLOCK_THUMBNAIL_REPRESENTATION_MASK;
      }
   }
}

void
CommonSvcWin32::ReadUserAppProtectionGPO(
   uint64 &featureEnablementStatus,                      // IN & OUT
   std::map<std::string, util::Variant> &featureOptions) // IN & OUT
{
   CAccessToken userToken;
   userToken.Attach(RdeGetImpersonatedTokenAsLogonUser());
   if (userToken.GetHandle() == nullptr) {
      Log("%s: Failed to retrieve user token", __FUNCTION__);
      return;
   }

   auto impersonated = userToken.ImpersonateLoggedOnUser();
   if (!impersonated) {
      Log("%s: Unable to impersonate as logged on user. Error: 0x%x.\n", __FUNCTION__,
          GetLastError());
      return;
   }
   ON_BLOCK_EXIT([&]() { userToken.Revert(); });

   HKEY hKeyUserRoot;
   auto status = RegOpenCurrentUser(KEY_READ, &hKeyUserRoot);
   if (status != ERROR_SUCCESS) {
      Log("%s: RegOpenCurrentUser failed, error: %d.", __FUNCTION__, status);
      return;
   }
   ON_BLOCK_EXIT(&RegCloseKey, hKeyUserRoot);

   HKEY hLegacyKey = NULL;
   status = RegOpenKeyEx(hKeyUserRoot, LEGACY_AGENT_POLICY_SUFFIX, 0, KEY_QUERY_VALUE, &hLegacyKey);
   bool legacyKeyAvailable = status == ERROR_SUCCESS && wstr::fallbackToLegacyEnabled();
   if (status != ERROR_SUCCESS) {
      Log("%s: RegOpenKeyEx failed for legacy user settings, error: %d.", __FUNCTION__, status);
   }
   ON_BLOCK_EXIT(&RegCloseKey, hLegacyKey);

   HKEY hKey = NULL;
   status = RegOpenKeyEx(hKeyUserRoot, AGENT_POLICY_SUFFIX, 0, KEY_QUERY_VALUE, &hKey);
   if (status != ERROR_SUCCESS) {
      if (!legacyKeyAvailable) {
         Log("%s: RegOpenKeyEx failed for user settings, error: %d.", __FUNCTION__, status);
         return;
      } else {
         Log("%s: RegOpenKeyEx failed for user settings, error: %d. "
             "Fallback to legacy user settings.",
             __FUNCTION__, status);
      }
   }
   ON_BLOCK_EXIT(&RegCloseKey, hKey);

   if (mClientFeatureCapacity & BLOCK_SCREEN_CAPTURE_MASK) {
      int regValue = wstr::readRegistry(hKey, BLOCK_SCREEN_CAPTURE_KEY, L"-1").toInt();
      Log("%s, current user block screen capture GPO %d\n", __FUNCTION__, regValue);
      bool readFromLegacy = false;
      if (regValue == -1 && legacyKeyAvailable) {
         readFromLegacy = true;
         regValue = wstr::readRegistry(hLegacyKey, BLOCK_SCREEN_CAPTURE_KEY, L"-1").toInt();
         Log("%s, current user legacy block screen capture GPO %d\n", __FUNCTION__, regValue);
      }
      if (regValue == 0) {
         featureEnablementStatus &= (~BLOCK_SCREEN_CAPTURE_MASK);
      } else if (regValue == 1) {
         featureEnablementStatus |= BLOCK_SCREEN_CAPTURE_MASK;
      }

      int32 allowScreenRecording =
         wstr::readRegistry(hKey, ALLOW_SCREEN_RECORDING_KEY, L"-1").toInt();
      if (readFromLegacy && legacyKeyAvailable) {
         allowScreenRecording =
            wstr::readRegistry(hLegacyKey, ALLOW_SCREEN_RECORDING_KEY, L"-1").toInt();
      }

      if (!featureOptions.contains(ALLOW_SCREEN_RECORDING_OPTION_KEY)) {
         featureOptions[ALLOW_SCREEN_RECORDING_OPTION_KEY] =
            util::Variant((int32)(allowScreenRecording == 1 ? 1 : 0));
      } else if (allowScreenRecording != -1) {
         featureOptions[ALLOW_SCREEN_RECORDING_OPTION_KEY].lVal = allowScreenRecording;
      }
      Log("%s, value %d, current user %s screen recording\n", __FUNCTION__, allowScreenRecording,
          allowScreenRecording == 1 ? "allows" : "disallows");
   }
   if (mClientFeatureCapacity & BLOCK_KEY_LOGGER_MASK) {
      int regValue = wstr::readRegistry(hKey, BLOCK_KEY_LOGGER_KEY, L"-1").toInt();
      Log("%s, current user block key logger GPO %d\n", __FUNCTION__, regValue);
      if (regValue == -1 && legacyKeyAvailable) {
         regValue = wstr::readRegistry(hLegacyKey, BLOCK_KEY_LOGGER_KEY, L"-1").toInt();
         Log("%s, current user legacy block key logger GPO %d\n", __FUNCTION__, regValue);
      }
      if (regValue == 0) {
         featureEnablementStatus &= (~BLOCK_KEY_LOGGER_MASK);
      } else if (regValue == 1) {
         featureEnablementStatus |= BLOCK_KEY_LOGGER_MASK;
      }

      int32 allowArmNoAntiKeylogger =
         wstr::readRegistry(hKey, ALLOW_ARM_NO_ANTIKEYLOGGER, L"-1").toInt();
      if (!featureOptions.contains(ALLOW_ARM_NO_ANTIKEYLOGGER_OPTION_KEY)) {
         featureOptions[ALLOW_ARM_NO_ANTIKEYLOGGER_OPTION_KEY] =
            util::Variant((int32)(allowArmNoAntiKeylogger == 1 ? 1 : 0));
      } else if (allowArmNoAntiKeylogger != -1) {
         featureOptions[ALLOW_ARM_NO_ANTIKEYLOGGER_OPTION_KEY].lVal = allowArmNoAntiKeylogger;
      }
      Log("%s, value %d, current user %s connection from windows ARM without "
          "AntiKeylogger.\n",
          __FUNCTION__, allowArmNoAntiKeylogger,
          allowArmNoAntiKeylogger == 1 ? "allows" : "disallows");
   }
   if (mClientFeatureCapacity & BLOCK_SEND_INPUT_MASK) {
      int regValue = wstr::readRegistry(hKey, BLOCK_SEND_INPUT_KEY, L"-1").toInt();
      Log("%s, current user block SendInput() GPO %d\n", __FUNCTION__, regValue);
      if (regValue == -1 && legacyKeyAvailable) {
         regValue = wstr::readRegistry(hLegacyKey, BLOCK_SEND_INPUT_KEY, L"-1").toInt();
         Log("%s, current user legacy block SendInput() GPO %d\n", __FUNCTION__, regValue);
      }
      if (regValue == 0) {
         featureEnablementStatus &= (~BLOCK_SEND_INPUT_MASK);
      } else if (regValue == 1) {
         featureEnablementStatus |= BLOCK_SEND_INPUT_MASK;
      }
   }
   if (mClientFeatureCapacity & BLOCK_THUMBNAIL_REPRESENTATION_MASK) {
      int regValue = wstr::readRegistry(hKey, BLOCK_THUMBNAIL_REPRESENTATION_KEY, L"-1").toInt();
      Log("%s, current user thumbnail representation blocking GPO %d\n", __FUNCTION__, regValue);
      if (regValue == -1 && legacyKeyAvailable) {
         regValue =
            wstr::readRegistry(hLegacyKey, BLOCK_THUMBNAIL_REPRESENTATION_KEY, L"-1").toInt();
         Log("%s, current user legacy thumbnail representation blocking GPO "
             "%d\n",
             __FUNCTION__, regValue);
      }
      if (regValue == 0) {
         featureEnablementStatus &= (~BLOCK_THUMBNAIL_REPRESENTATION_MASK);
      } else if (regValue == 1) {
         featureEnablementStatus |= BLOCK_THUMBNAIL_REPRESENTATION_MASK;
      }
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * CommonSvcWin32::IsFeatureOptionsChanged --
 *
 *       Whether the featureOptions ready from  registry is different comparing
 *       with the given one.
 *
 * Returns:
 *       False if is same, true otherwise.
 *
 * Side effects:
 *       None.
 *
 *-----------------------------------------------------------------------------
 */

bool
CommonSvcWin32::IsFeatureOptionsChanged(std::map<std::string, util::Variant> &featureOptions) // IN
{
   if (featureOptions.size() != mFeatureOptions.size()) {
      return true;
   }
   if ((featureOptions.contains(ALLOW_SCREEN_RECORDING_OPTION_KEY) &&
        mFeatureOptions.contains(ALLOW_SCREEN_RECORDING_OPTION_KEY) &&
        featureOptions[ALLOW_SCREEN_RECORDING_OPTION_KEY].lVal !=
           mFeatureOptions[ALLOW_SCREEN_RECORDING_OPTION_KEY].lVal) ||
       (featureOptions.contains(ALLOW_SCREEN_RECORDING_OPTION_KEY) ^
        mFeatureOptions.contains(ALLOW_SCREEN_RECORDING_OPTION_KEY))) {
      return true;
   }
   if ((featureOptions.contains(ALLOW_ARM_NO_ANTIKEYLOGGER_OPTION_KEY) &&
        mFeatureOptions.contains(ALLOW_ARM_NO_ANTIKEYLOGGER_OPTION_KEY) &&
        featureOptions[ALLOW_ARM_NO_ANTIKEYLOGGER_OPTION_KEY].lVal !=
           mFeatureOptions[ALLOW_ARM_NO_ANTIKEYLOGGER_OPTION_KEY].lVal) ||
       (featureOptions.contains(ALLOW_ARM_NO_ANTIKEYLOGGER_OPTION_KEY) ^
        mFeatureOptions.contains(ALLOW_ARM_NO_ANTIKEYLOGGER_OPTION_KEY))) {
      return true;
   }
   return false;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CommonSvcWin32::OnAppProtectionRegistryChanged --
 *
 *       The callback function when app protection registry changes.
 *
 * Returns:
 *       Always 0.
 *
 * Side effects:
 *       None.
 *
 *-----------------------------------------------------------------------------
 */

LRESULT
CommonSvcWin32::OnAppProtectionRegistryChanged(UINT uMsg,      // IN
                                               WPARAM wParam,  // IN
                                               LPARAM lParam,  // IN
                                               BOOL &bHandled) // IN
{
   uint64 featureEnablementStatus = 0;
   featureEnablementStatus |= FEATURE_OPTION_MASK;
   std::map<std::string, util::Variant> featureOptions;
   ReadMachineAppProtectionGPO(featureEnablementStatus, featureOptions);
   if (mAppProtectionUserGPOWatched) {
      // If user not logon yet, don't read the user GPO.
      ReadUserAppProtectionGPO(featureEnablementStatus, featureOptions);
   }
   if (featureEnablementStatus != mFeatureEnablementStatus ||
       IsFeatureOptionsChanged(featureOptions)) {
      Log("%s: status is changed %d, options are changed %d.\n", __FUNCTION__,
          featureEnablementStatus != mFeatureEnablementStatus ? 1 : 0,
          IsFeatureOptionsChanged(featureOptions) ? 1 : 0);
      mFeatureEnablementStatus = featureEnablementStatus;
      mFeatureOptions = featureOptions;
      SendFeatureEnablementInfo(FEATURE_ENABLEMENT_CMD_STATUS, mFeatureEnablementStatus,
                                mFeatureOptions);
   }
   return 0;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CommonSvcWin32::OnAppProtectionUserGPOReady --
 *
 *       The callback function when app protection user GPO is ready to watch.
 *
 * Returns:
 *       Always 0.
 *
 * Side effects:
 *       None.
 *
 *-----------------------------------------------------------------------------
 */

LRESULT
CommonSvcWin32::OnAppProtectionUserGPOReady(UINT uMsg,      // IN
                                            WPARAM wParam,  // IN
                                            LPARAM lParam,  // IN
                                            BOOL &bHandled) // IN
{
   if (mAppProtectionWatcherHandler != NULL && !mAppProtectionUserGPOWatched) {
      mAppProtectionUserGPOWatched = true;
      Log("%s: start to watch the app protection user registry key.\n", __FUNCTION__);
      mAppProtectionWatcherHandler->StartWatch(m_hWnd, APP_PROTECTION_USER_GPO);
   }
   return 0;
}