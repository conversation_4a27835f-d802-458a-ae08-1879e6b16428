# Copyright (c) Omnissa, LLC. All rights reserved.
# This product is protected by copyright and intellectual property laws in the
# United States and other countries as well as by international treaties.
# -- Omnissa Restricted

"""windows

Windows-specific packaging script for hccrt.

Maintainers: vdm-client@vmware.<NAME_EMAIL>.

"""

import os
import re
import vmware
import collections
from win32api import GetFileVersionInfo, LOWORD, HIWORD
from SCons.Script import Dir, File

artifactorySignedFileRoot = "horizon-cart-local/drivers/"

log = vmware.GetLogger("packaging")
productVersion = vmware.ProductVersionNumber()
yymmVersion = vmware.ProductVersionNumber("HorizonClientYYMM")
buildNum = vmware.BuildNumber()
env = vmware.pkg.stageEnv.Copy()
productBuildNum = vmware.ProductBuildNumber()

CODEQL_SCAN = vmware.LocalOpts.GetBool("CODEQL_SCAN", False)
CODE_COV = vmware.LocalOpts.GetBool("ENABLE_CODE_COV", False)

# Set up some paths used by MSI and staging code.
buildTypeRoot = os.path.join(vmware.DirAbsPath(vmware.BuildRoot()), vmware.BuildType())
installSourceRoot = os.path.join(buildTypeRoot, vmware.Product())
installSource32 = os.path.join(installSourceRoot, "x86")
installSource64 = os.path.join(installSourceRoot, "x64")
installSourceArm64 = os.path.join(installSourceRoot, "arm64")
installSourceArm64ec = os.path.join(installSourceRoot, "arm64ec")
installSourceNoarch = os.path.join(installSourceRoot, "noarch")
testE2EBaseDir = os.path.join(installSourceRoot, "tests")
testE2EBinaryDir32 = os.path.join(testE2EBaseDir, "testE2E")
testE2EBinaryDir64 = os.path.join(testE2EBinaryDir32, "x64")
autotestBaseDir = os.path.join(installSourceRoot, "autotest")
autotestBaseDir64 = os.path.join(autotestBaseDir, "x64")
unitTestScripts = os.path.join(installSourceRoot, "scripts")

pcoipSoftClientsRoot = vmware.GetGobuildComponent("pcoip-soft-clients")
fabulatechSerialPortRoot = os.path.join(
    vmware.GetConanComponent(env, "fabulatech_serialport").package_folder, "win"
)
fabulatechScannerRoot = os.path.join(
    vmware.GetConanComponent(env, "fabulatech_scanner").package_folder, "win"
)
openSSLRoot = vmware.GetConanComponent(env, "openssl").package_folder
eucTlmSdkRoot = vmware.GetGobuildComponent("euc_endpoint-telemetry-sdk")
msteamsapiRoot = vmware.GetGobuildComponent("msteamsapi")
teraLfsRoot = vmware.GetGobuildComponent("tera-lfs")
webrtcRoot = vmware.GetGobuildComponent("cart-webrtc")
windowsInstallkitRoot = vmware.GetGobuildComponent("windows-installkit")

# Installer info for the new modernized client.
installerName = "Omnissa-Horizon-Client-%s-%s-%s" % (
    yymmVersion,
    productVersion,
    buildNum,
)
msi32Name = "Omnissa-Horizon-Client-x86-%s-%s-%s" % (
    yymmVersion,
    productVersion,
    buildNum,
)
msi64Name = "Omnissa-Horizon-Client-x86_64-%s-%s-%s" % (
    yymmVersion,
    productVersion,
    buildNum,
)

libcdkTestDir = Dir("#horizonclient/view/openClient/lib/cdk/tests")
boraDCTDir = vmware.DirAbsPath("#bora/apps/horizonAgent/dct")
boraDCTTestDir = vmware.DirAbsPath("#bora/apps/horizonDCT/componentTest")
clientSdkRxApiTestDir = vmware.DirAbsPath(
    "#horizonclient/view/openClient/sdk/tests/rxApiTest"
)
boraMigrConfDir = vmware.DirAbsPath("#bora/apps/migration/config")

BetaFeature = collections.namedtuple(
    "BetaFeature", ["name", "wixCompilerFlags", "wixLightFlags"]
)

# Get the clientSDK environment per host (win64, win-arm64ec)
clientSdkEnvX64 = vmware.LookupEnv("clientSdkNetPrimitive-env", "win64")
clientSdkEnvArm64Ec = vmware.LookupEnv("clientSdkCommon-env", "win-arm64ec")

exportsHosts = {
    "win32": "x86",
    "win64": "x64",
    "win-arm64": "arm64",
    "win-arm64ec": "arm64ec",
}

rdeMsmNodes = []

# rtav/printredir msi nodes for component test
ciMsiNodes = {}

usbarbMsmNodes = {}


def GetVCRuntimeFiles(arch, buildType):
    """
    Get the VC Runtime files based on the arch and build type.

    arch: 'x86' or 'x64'.
    buildType: 'obj' or other types(such as 'release').

    ucrtbased.dll is needed by obj builds for both x86 and x64.
    """
    retFiles = []
    srcFiles = []
    rootDir = ""
    subDir = ""
    if buildType == "obj":
        rootDir = env["MSVC_NON_REDISTS_ROOT"]
        subDir = "Microsoft.VC140.DebugCRT"
        winDir = env["WINDOWS_NON_REDISTS_ROOT"]
        retFiles.append(
            os.path.join(winDir, "win", "ucrt", "dll", "winxp", arch, "ucrtbased.dll")
        )
        srcFiles = [
            "msvcp140d.dll",
            "vcruntime140d.dll",
        ]
        if arch == "x64":
            srcFiles.append("vcruntime140_1d.dll")
    else:
        rootDir = env["MSVC_REDISTS_ROOT"]
        subDir = "Microsoft.VC140.CRT"
        srcFiles = [
            "msvcp140.dll",
        ]
        if arch == "x64":
            srcFiles.append("vcruntime140_1.dll")

    fullPath = os.path.join(rootDir, "win", "dll", arch, subDir)
    for file in srcFiles:
        retFiles.append(os.path.join(fullPath, file))

    return retFiles


def nonrecursiveDirFilter(root, dirs):
    """
    Remove all subdirectories so we don't walk them. Should be passed into
    vmware.EnumerateSourceDirs or vmware.StageCopyDirectory for a nonrecursive
    enumeration.
    """
    del dirs[:]


def addDepsToZipSources(env, names, zipSources):
    """
    Add all dependencies which are specied by names and their pdb to zip
    """
    deps = []
    for name in names:
        dep = env[name]
        if isinstance(dep, list):
            deps += dep
        else:
            deps.append(dep)

    for dep in deps:
        depAbsPath = File(dep).abspath
        zipSources.append(os.path.split(depAbsPath))
        pdb = os.path.splitext(depAbsPath)[0] + ".pdb"
        if os.path.exists(pdb):
            zipSources.append(os.path.split(pdb))


def copyBinaryAndPdb(stageEnv, dest, src):
    """
    Takes a source binary or pdb and copies it to the destination.
    If the source is a binary, it also copies the pdb.
    Returns the copy nodes.

    This function automatically marks the copy nodes as not cacheable because
    it is currently only used to copy gobuild and toolchain dependencies, which
    can change often.
    """
    # Copy and sign the binary.
    nodes = stageEnv.FileCopy(dest, src)
    stageEnv.SignFile(nodes)

    # Copy the binary's pdb (if one exists).
    srcBasename = os.path.basename(src)
    srcRoot, srcExt = os.path.splitext(srcBasename)
    if srcExt != ".pdb":
        srcPdbBasename = srcRoot + ".pdb"
        srcPdbPath = os.path.join(os.path.dirname(src), srcPdbBasename)
        if os.path.isfile(srcPdbPath):
            destPdbPath = os.path.join(os.path.dirname(dest), srcPdbBasename)
            nodes += stageEnv.LinkCopy(destPdbPath, srcPdbPath)

    # See function comment above for why these are marked as not cacheable.
    stageEnv.NoCache(nodes)

    return nodes


def copyMsms(stageEnv, msmSrcDir, msmDestDir, archList):
    """
    Takes in the msm source directory and copies only those msms that
    has '-Client' in their name to the given destination directory.
    """
    msmCopyNodes = []
    for arch in archList:
        sourceDir = os.path.join(msmSrcDir, arch)
        msms = os.listdir(sourceDir)
        for msm in msms:
            name, ext = os.path.splitext(msm)
            if ext == ".msm" and "-Client-" in name:
                # It is a client msm. Now figure out dest name.
                nameComponents = name.split("-")
                destName = "Horizon_%s_%s.msm" % (nameComponents[1], nameComponents[-1])
                msmCopyNodes += stageEnv.LinkCopy(
                    os.path.join(msmDestDir, destName), os.path.join(sourceDir, msm)
                )

    # Save cache server space by not caching these copy nodes.
    stageEnv.NoCache(msmCopyNodes)

    return msmCopyNodes


def buildWincdkMSI(
    arch, msiName, installDir, additionalWixCompilerFlags={}, additionalWixLightFlags=[]
):
    """
    Builds the client MSI using the specified architecture.

    arch: 'x86' or 'x64'.
    installDir: Takes in the output directory for the msi
    additionalWixCompilerFlags: additional candle flags. Mostly used while
                                building the beta feature installer
    additionalWixLightFlags: additional light flags. Mostly used while
                                building the beta feature installer

    This function returns the resulting msi node.
    """
    wixEnv = stageEnv.Clone()

    wixDefines = {
        "PRODUCT": vmware.Product(),
        "INSTALLSOURCE_32": installSource32,
        "INSTALLSOURCE_64": installSource64,
        "INSTALLSOURCE_ARM64EC": installSourceArm64ec,
        "INSTALLSOURCE_NOARCH": installSourceNoarch,
        "INSTALLBORADCTDIR": boraDCTDir,
        "INSTALLBORAMIGRDIR": boraMigrConfDir,
        "PRODUCTNAME": "Omnissa Horizon Client",
        "MSIProductVersion": productVersion,
        "SRCROOT": vmware.DirAbsPath("#horizonclient"),
        "PRODUCT_BUILD_NUMBER": vmware.ProductBuildNumber(),
        "BUILD_NUMBER": buildNum,
        "CopyrightYears": wixEnv.ExtractMacro("vm_legal.h", "COPYRIGHT_YEARS"),
        "GOBUILD_WINDOWS_INSTALLKIT_ROOT": vmware.GetGobuildComponent(
            "windows-installkit"
        ),
        "GITHUB_EUC_TLM_SDK_ROOT": eucTlmSdkRoot,
        "CONAN_FABULATECH_COMMON_ROOT": os.path.join(
            vmware.GetConanComponent(env, "fabulatech_common").package_folder, "win"
        ),
        "CONAN_FABULATECH_SCANNER_ROOT": fabulatechScannerRoot,
        "CONAN_FABULATECH_SERIALPORT_ROOT": fabulatechSerialPortRoot,
        "CONAN_OPENSSL_ROOT": openSSLRoot,
        "MSMROOT": installDir,
        "TARGET_PLATFORM": arch,
        "SSL_VERSION": "3.0",
        "MSTeamsAPIDir": os.path.join(
            vmware.GetGobuildComponent("msteamsapi"), "win64_vc140", "lib"
        ),
        "WebRtcDir": os.path.join(webrtcRoot, "win64_vc140", "lib"),
        "CONAN_7ZIP_ROOT": Dir(
            vmware.GetConanBuildComponent(stageEnv, "7zip").env["PATH"]
        ).abspath,
        "OSL_TXT_FILE": vmware.FileAbsPath("#osl/hcwin/open_source_licenses.txt"),
    }
    # Add the additional wix compiler flags - this is for the beta feature
    wixDefines.update(additionalWixCompilerFlags)

    wixCandleFlags = [
        "-ext",
        "WixUIExtension",
        "-ext",
        "WixUtilExtension",
    ]
    wixLightFlags = [
        "-ext",
        "WixUIExtension",
        "-ext",
        "WixUtilExtension",
        "-sice:ICE03",
        "-sice:ICE09",
        "-sice:ICE12",
        "-sice:ICE26",
        "-sice:ICE27",
        "-sice:ICE30",
        "-sice:ICE61",
        "-sice:ICE64",
        "-sice:ICE69",  # Due to FabulaTech's scanner msm.
        "-sice:ICE79",
        "-sice:ICE80",
        "-sice:ICE82",
        "-sice:ICE83",
        "-sw1008",
        # Next two are due to consuming MSVC and VMware USB merge module.
        "-sw1055",
        "-sw1056",
    ]
    # Add the additional wix light flags - this is for the beta feature
    wixLightFlags.append(additionalWixLightFlags)

    wixEnv.Append(
        WIXDEFINES=wixDefines,
        WIXCANDLEFLAGS=wixCandleFlags,
        WIXLIGHTFLAGS=wixLightFlags,
        WIXLIGHTLOC=[
            "-cultures:en-us",
            "-loc",
            vmware.FileAbsPath(
                "#horizonclient/install/windows/" "client/l10n/1033.wxl"
            ),
        ],
    )

    # There are many wix source files, so it's easiest to just generate the
    # list dynamically by enumerating the directory.
    wixSourceDir = vmware.DirAbsPath("#horizonclient/install/windows/client")
    wixSources = [
        os.path.join(wixSourceDir, f)
        for f in os.listdir(wixSourceDir)
        if f.endswith(".wxs")
    ]
    wixObjNodes = []

    # The MSI directory must be a peer of the install directory because
    # the bootstrapper looks for it there.
    msiDir = Dir(os.path.join(installDir, arch))

    # Build each of the wix source files.
    for wixSource in wixSources:
        filename, ext = os.path.splitext(os.path.basename(wixSource))
        wixObjName = "%s-%s.wixobj" % (msiName, filename)
        wixObjNodes += wixEnv.Wixobj(msiDir.File(wixObjName), wixSource)

    # Create the msi. Pass WIX_EMITTER_PREPROCESS_WXS=True because
    # ViewClient.wxs uses variables and <?if?> conditions in the cabinet
    # <Media> logic and we need the preprocessor to sort it out.
    msiNode = wixEnv.Wixmsi(
        msiDir.File(msiName + ".msi"), wixObjNodes, WIX_EMITTER_PREPROCESS_WXS=True
    )

    # Embed all translations in the msi.
    langs = ["zh-tw", "de", "en", "es", "fr", "ja", "ko", "zh-cn"]
    wixEnv.EmbedTranslationMSI(
        msiNode,
        wixObjNodes,
        wixSourceDir,
        langs,
        ["#horizonclient/install/windows/client/l10n"],
    )

    # Sign the msi.
    wixEnv.SignFile(msiNode)

    return msiNode


def buildWincdkInstaller(
    stageEnv,
    dependencyNodes,
    installDir,
    clientInstallerName=installerName,
    clientMsi64Name=msi64Name,
):
    """Builds the Windows client installer (a.k.a. bootstrapper)

    This function owns the process of generating the full .exe installer
    bootstrapper using the msi files that were already generated.

    Arguments:
        - dependencyNodes: Any nodes that the bootstrapper build action should
                           depend upon.
          installDir: Installation directory where msi can be found
          clientInstallerName: Name of the client installer.
                               Default = Horizon client installer name
          clientMsi64Name: Name of the 64-bit msi.
                           Default - 64-bit Horizon client msi
    """
    env = stageEnv.Clone()
    env.LoadTool(["artifactory", "msvc-debug-runtime-installer"])

    resourceHackerExtractNode = env.FetchFromArtifactory(
        "horizon-cart-local/hcwin/ResourceHacker/ResourceHacker-4.4.zip",
        sha256="f7a4346aa7b1b450ab3ad3936a23d45ff7f2d9c1de4c2f59c786fc99bfe0bf40",
        extract=True,
    )
    resourceHackerFolder = resourceHackerExtractNode[0].abspath

    configType = "Debug" if vmware.BuildType() == "obj" else "Release"
    buildType = "Beta" if vmware.BuildType() == "beta" else "Nobeta"
    intDir = os.path.join(installDir, "intermediateOutput")

    wixRoot = env["WIX_ROOT"]
    dotnetRedistsComponent = vmware.GetConanComponent(env, "dotnet_redists")

    # Create a fake html5mmrClient.dll with size 0.
    # Only write it if it doesn't already exist, to avoid causing unnecessary
    # rebuilds due to SCons seeing that the file changed.
    def _TouchFile(target, source, env):
        with open(target[0].abspath, "w+"):
            pass

    dummyFileNode = env.Command(
        Dir(installSourceNoarch).File("html5mmrClient.dll"),
        [],
        vmware.ActionWithDisplay(_TouchFile, "Generating dummy html5mmrClient.dll"),
    )

    # Use a fake file object for the build step because in the following steps,
    # we will need to detach the engine, sign it, and then merge the
    # bootstrapper again. Both the build step and the merge step create the
    # same file (which is best to reduce storage space usage) but SCons doesn't
    # allow two nodes to produce the same target.
    buildTarget = File(os.path.join(installDir, clientInstallerName + ".exe"))
    buildSource = File(
        "#horizonclient/install/windows/ViewClientBA/" "ViewClientBA.sln"
    )
    buildOptions = {
        "Configuration": configType,
        "BuildType": buildType,
        "IsBuildOpt": vmware.BuildType() == "opt",
        "IntermediateOutputPath": "%s\\" % intDir,
        "OutputPath": installDir,
        "Platform": '"Any CPU"',
        "BootstrapperName": '"%s"' % clientInstallerName,
        "SRCROOT": vmware.DirAbsPath("#horizonclient"),
        "DEST": installDir,
        "MSBuildExtensionsPath32": wixRoot,
        "WixToolPath": wixRoot,
        "WixTargetsPath": os.path.join(wixRoot, "wix.targets"),
        "WixTasksPath": os.path.join(wixRoot, "WixTasks.dll"),
        "LuxTargetsPath": os.path.join(wixRoot, "Lux.targets"),
        "LuxTasksPath": os.path.join(wixRoot, "LuxTasks.dll"),
        "VIEWCLIENT_NAME": clientInstallerName,
        "VIEWCLIENT_NAME64": clientMsi64Name,
        "GITHUB_EUC_TLM_SDK_ROOT": eucTlmSdkRoot,
        "ETLM_INSTALLER_FULL_VERSION": getFileFullVersion(
            os.path.join(eucTlmSdkRoot, "Omnissa-Telemetry-Agent.exe")
        ),
        "VIEWCLIENT_VERSION": "%s.%s" % (productVersion, vmware.ProductBuildNumber()),
        "VIEWCLIENT_YYMM_VERSION": yymmVersion,
        "RESOURCE_HACKER_EXE": os.path.join(resourceHackerFolder, "ResourceHacker.exe"),
        "WIX_ROOT": wixRoot,
        "CONAN_DOTNET_REDISTS_ROOT": dotnetRedistsComponent.package_folder,
        "CONAN_DOTNET_REDISTS_VERSION": dotnetRedistsComponent.ref.version,
        "CONAN_MSVC_REDISTS_ROOT": env["MSVC_REDISTS_ROOT"],
        "CONAN_MSVC_DEBUG_RUNTIME_INSTALLER_ROOT": env[
            "MSVC_DEBUG_RUNTIME_INSTALLER_ROOT"
        ],
    }
    buildNode = env.MsBuild([buildTarget], [buildSource], options=buildOptions)
    env.Depends(
        buildNode, dependencyNodes + resourceHackerExtractNode + [dummyFileNode]
    )

    env["WIX_INSIGNIA"] = os.path.join(wixRoot, "insignia.exe")

    # Detach the engine from the bootstrapper and sign it.
    enginePath = os.path.join(installDir, configType, "engine.exe")
    env.AddPostAction(buildNode, "$WIX_INSIGNIA -ib $TARGET -o %s" % enginePath)

    if vmware.SignBinaries():
        # Note: we don't use env.SignFile() here because we want to sign a
        # different file than the action target. This isn't great practice
        # (generating a file unrelated to the build action) but the action
        # isn't cacheable anyway, so dependency enforcement doesn't fail.
        env.AddPostAction(buildNode, f'{stageEnv["SIGN_COMMAND_BASE"]} {enginePath}')

    # Merge the engine and the bootstrapper and sign it.
    env.AddPostAction(buildNode, "$WIX_INSIGNIA -ab %s $TARGET -o $TARGET" % enginePath)
    env.SignFile(buildNode)

    return buildNode


def getZipSourcesFromDir(dir, prefix="", suffix=""):
    """
    Retrieves an array containing information about sources from the specified
    directory that have the specified prefix(es) and/or suffix(es). Retrieves
    all sources if the parameters are not provided. This is retrieved in a
    format that is compatible with the Zip() function from the zip-builder
    vtool.

    To search for multiple prefixes/suffixes, pass a tuple of strings.
    """
    dirAbsPath = Dir(dir).abspath
    return [
        (dirAbsPath, f)
        for f in os.listdir(dirAbsPath)
        if f.startswith(prefix) and f.endswith(suffix)
    ]


def getZipSourcesFromNodeLookup(nodeNames, host, includeAllFiles=False):
    """
    Retrieves a list of zip sources that can be passed into env.Zip().

    :param nodeNames: List of node names to look up.
    :param host: Host of the node to use in the lookup.
    :param includeAllFiles: True if all files for this node (e.g. pdb, lib)
                            should be included.
    :return: List of tuples in a format compatible with env.Zip().
    """
    zipSources = []
    for nodeName in nodeNames:
        if includeAllFiles:
            nodes = vmware.pkg.LookupDeliverableNodes(nodeName, host)
        else:
            nodes = [vmware.LookupNode(nodeName, host)[0]]
        for node in nodes:
            zipSources.append((node.dir.abspath, node.name))
    return zipSources


def getZipSourcesFromNodeFileNames(allNodes, prefix="", suffix=""):
    """
    Retrieves a tuple containing the following:
        1. A list containing information about sources from the list of
           nodes that have the specified prefix(es) and/or suffix(es). This is
           retrieved in a format that is compatible with the Zip() function
           from the zip-builder vtool.
        2. A list containing the source nodes.

    This function differs from getZipSourcesFromNodeLookup in that it iterates
    over previously-generated nodes, whereas getZipSourcesFromNodeLookup
    looks up the nodes.

    To search for multiple prefixes/suffixes, pass a tuple of strings.
    """
    sources = []
    nodes = []
    for node in allNodes:
        if node.name.startswith(prefix) and node.name.endswith(suffix):
            # Avoid adding duplicates in case there are multiple actions
            # related to the specified node.
            source = (node.dir.abspath, node.name)
            if source not in sources:
                sources.append(source)
                nodes.append(node)
    return (sources, nodes)


def getFileFullVersion(filePath):
    """
    Get the full version of given file.
    :return: The full version of the file, or "0.0.0.0" if failed.
    """
    try:
        info = GetFileVersionInfo(filePath, "\\")
        ms = info["FileVersionMS"]
        ls = info["FileVersionLS"]
        return ".".join(
            [str(HIWORD(ms)), str(LOWORD(ms)), str(HIWORD(ls)), str(LOWORD(ls))]
        )
    except:
        return "0.0.0.0"


def publishGPOFiles(stageEnv, filesDir, zipDir):
    """Publishes all GPO files and a zip containing the GPO files.

    This function owns the process of collecting all GPO files and staging
    them to the destination directory in the format we would like to see
    in the Deliverables page.

    Often times the source folder structure and file names don't match what we
    want them staged as, so this function takes the approach of collecting all
    files in in filesDir and then zipping filesDir.
    """
    nodes = []

    pcoipGPOSrc = os.path.join(vmware.GetGobuildComponent("pcoip_soft_server"), "adm")

    # Collect all zip source from various directories. This maps from source to
    # subdirectory name; if the RHS value is None, use filesDir.
    # RHS is the subdir name and filefilter
    copySourceDirs = {
        "#horizonclient/view/gpo": None,
        "#horizonclient/view/gpo-l10n/horizon": None,
        os.path.join(pcoipSoftClientsRoot, "adm"): None,
        pcoipGPOSrc: None,
        "#bora/apps/rde/rtav/extras/GPO": None,
        "#bora/apps/rde/urlRedirection/gpo": None,
        "#bora/apps/rde/uncRedirection/gpo": None,
        "#bora/apps/printRedir/gpo": None,
        "#bora/apps/rde/perfTracker/admx": None,
        "#bora/apps/rde/tsdr/gpo": None,
        "#bora/apps/rde/mksvchan/gpo": None,
        "#horizonclient/view/openClient/win32/rc/en-US": ("en-US", None),
        "#bora/apps/horizonAgent/gpo": (
            None,
            lambda path, f: not f.startswith("ViewPMAutomation"),
        ),
    }
    copySourceFiles = [
        "#horizonclient/view/openClient/win32/rc/hzn_client.admx",
    ]

    # Put all zip sources up until now in the GPO publishing dir. The zip
    # sources after this point are dynamic and are not compatible with the
    # logic in this loop because StageCopyDirectory uses source dir globbing.
    for source, sourceInfo in copySourceDirs.items():
        if sourceInfo:
            subdirName = sourceInfo[0]
            filefilter = sourceInfo[1]
        else:
            subdirName = None
            filefilter = None

        if subdirName:
            destDir = os.path.join(filesDir, subdirName)
        else:
            destDir = filesDir

        if filefilter:
            publishNode = vmware.utils.StageCopyDirectory(
                source, destDir, stageEnv.LinkCopy, filefilter=filefilter
            )
        else:
            publishNode = vmware.utils.StageCopyDirectory(
                source, destDir, stageEnv.LinkCopy
            )
        nodes += publishNode

    for source in copySourceFiles:
        nodes += stageEnv.LinkCopy(os.path.join(filesDir, File(source).name), source)

    # Now an even more complicated case: files that we want to stage with a
    # different name! Do this by copying to an intermediate directory first.
    tempEnglishDir = os.path.join(filesDir, "en-US")
    nodes += stageEnv.LinkCopy(
        os.path.join(filesDir, "hzn_agent_scanner.admx"),
        os.path.join(fabulatechScannerRoot, "win64", "scanredirect.admx"),
    )
    nodes += stageEnv.LinkCopy(
        os.path.join(filesDir, "hzn_agent_serialport.admx"),
        os.path.join(fabulatechSerialPortRoot, "win64", "serialcom.admx"),
    )
    nodes += stageEnv.LinkCopy(
        os.path.join(tempEnglishDir, "hzn_agent_scanner.adml"),
        os.path.join(fabulatechScannerRoot, "win64", "en-US", "scanredirect.adml"),
    )
    nodes += stageEnv.LinkCopy(
        os.path.join(tempEnglishDir, "hzn_agent_serialport.adml"),
        os.path.join(fabulatechSerialPortRoot, "win64", "en-US", "serialcom.adml"),
    )

    # Printer redirection has a DLL file that should be put here
    for node in vmware.LookupNode("lbpSettingUI", "win32"):
        if node.abspath.endswith(".dll"):
            nodes += stageEnv.LinkCopy(Dir(filesDir).File(node.name), node)

    # Last step is to zip the filesDir into a file in zipDir.
    # Because this is marked as depending on other LinkCopy() calls, we mark
    # it as not cacheable.
    zipFileName = "Omnissa-Horizon-Extras-Bundle-%s-%s-%s.zip" % (
        yymmVersion,
        productVersion,
        buildNum,
    )
    zipNode = stageEnv.Command(
        Dir(zipDir).File(zipFileName),
        Dir(gpoDir).abspath,
        "cd $SOURCE && $ZIP -r -u ${TARGET.abspath} *",
    )
    stageEnv.Depends(zipNode, nodes)
    stageEnv.NoCacheIfBuildNumberInfoOverridden(zipNode)
    nodes += zipNode

    return nodes


def publishSDKZips(stageEnv, allNodes, destDir):
    """
    Publishes any SDKs zips to the SDK directory. Currently this only includes
    partner API SDK.
    """
    # First create the partner API SDK zip file.
    zipSources, dependencyNodes = getZipSourcesFromNodeFileNames(
        allNodes, "horizonClientController", ".dll"
    )

    # Get all files from the public dir.
    zipSources += getZipSourcesFromDir(
        "#horizonclient/view/openClient/lib/controllerAPI/public"
    )

    # Get the vmware packing headers.
    zipSources += getZipSourcesFromDir(
        vmware.DirAbsPath("#bora/public"), "vmware_pack_", ".h"
    )

    zipFileName = "Omnissa-Horizon-Client-Partner-Integration-SDK-%s-%s-%s.zip" % (
        yymmVersion,
        productVersion,
        buildNum,
    )
    zipFileNode = stageEnv.Zip(Dir(destDir).File(zipFileName), zipSources)
    stageEnv.NoCacheIfBuildNumberInfoOverridden(zipFileNode)
    stageEnv.Depends(zipFileNode, dependencyNodes)
    return zipFileNode


def stageFabulatechJsonFile(stageEnv):
    nodes = []
    if not vmware.ReleasePackagesDir():
        return nodes
    FabulatechInPublishDir = stageEnv.Dir(
        os.path.join(vmware.ReleasePackagesDir(), "fabulatech")
    )
    stageEnv.Execute(vmware.Mkdir(FabulatechInPublishDir))
    arch_folder = "win64"
    for item in ["fabulatech_common", "fabulatech_scanner", "fabulatech_serialport"]:
        conanCommonDir = vmware.GetConanComponent(stageEnv, item).package_folder
        jsonFileSrc = stageEnv.File(
            os.path.join(
                conanCommonDir,
                "win",
                arch_folder,
                "fabulatech_drop_components_version.json",
            )
        )
        jsonFileDst = FabulatechInPublishDir.File(f"{item}_version.json")
        jsonFileCopy = stageEnv.LinkCopy(jsonFileDst, jsonFileSrc)
        nodes += jsonFileCopy
    return nodes


def publishUSBClientDrivers(stageEnv):
    """
    Publishes USB Redirection driver to the drivers directory.
    """
    nodes = []
    targetNames = ["hznusb", "hznucmon"]
    publishDir = vmware.ReleasePackagesDir()
    for targetName in targetNames:
        if publishDir is not None:
            extensionSrc = os.path.join(
                vmware.DirAbsPath(vmware.BuildRoot()),
                vmware.BuildType(),
                "usbarb",
                "Drivers",
                targetName,
            )
            zipFile = File(os.path.join(publishDir, "drivers", targetName + ".zip"))
            zipNodes = stageEnv.ZipDir(zipFile, Dir(extensionSrc), ".")
            nodes += zipNodes
    return nodes


def stageDCTClientComponentTest(stageEnv):
    """
    Stage the binaries for DCT client Component Test.
    """
    nodes = []
    targetNames = ["rxDCTComponentTestClient"]
    DCTTestRoot = "#bora/apps/horizonDCT/componentTest"

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(publishDir, "tests")
        deliverableNodesNames = ["rxDCTComponentTestClient"]
        extraZipSources = []
        # Add the DCT test cases
        commonTestCasesDir = Dir(
            os.path.join(boraDCTTestDir, "testCases", "windowsCommon")
        )
        extraZipSources.append((commonTestCasesDir.abspath, "."))
        clientTestCasesDir = Dir(
            os.path.join(boraDCTTestDir, "testCases", "windowsClient")
        )
        extraZipSources.append((clientTestCasesDir.abspath, "."))

        # Add the test deployment config files
        deployConfigsDir = Dir(os.path.join(boraDCTTestDir, "deploymentConfigs"))
        extraZipSources.append((deployConfigsDir.abspath, "."))

        # Add the new modernized .exe file
        extraZipSources.append((Dir(installSource64).abspath, "Horizon.Client.UI.exe"))

        # Add DCT scripts and necessary binaries
        clientDCTDir = Dir("#horizonclient/view/openClient/dct/win32")
        clientDCTFiles = [
            "elevate.cmd",
            "DumpSetting.json",
            "support.bat",
        ]
        for file in clientDCTFiles:
            extraZipSources.append((clientDCTDir.abspath, file))

        dctWinClientScriptFiles = [
            File(
                os.path.join(DCTTestRoot, "unitTest/windowsClient/vdm-support-test.ps1")
            ),
        ]

        nodes += vmware.DirCopy(
            dctWinClientScriptFiles,
            Dir(os.path.join(DCTTestRoot, "unitTest/windowsClient/")),
            Dir(installSourceDir("win64")),
            stageEnv,
        )

        dctWinCommScriptFiles = [
            File(
                os.path.join(
                    DCTTestRoot, "unitTest/windowsCommon/vdm-unittest-common.ps1"
                )
            ),
        ]
        nodes += vmware.DirCopy(
            dctWinCommScriptFiles,
            Dir(os.path.join(DCTTestRoot, "unitTest/windowsCommon/")),
            Dir(installSourceDir("win64")),
            stageEnv,
        )

        stageEnv.SignFile(nodes)

        clientDCTPSFiles = [
            "elevate.ps1",
            "vdm-common.ps1",
            "vdm-debug.ps1",
            "vdm-help.ps1",
            "vdm-loglevel-common.ps1",
            "vdm-query.ps1",
            "vdm-support.ps1",
            "vdm-product-common.ps1",
            "vdm-support-common.ps1",
            "plugin-manager.ps1",
            "config-parser.ps1",
            "vdm-support-test.ps1",
            "vdm-unittest-common.ps1",
            "vdm-legacy-support.ps1",
        ]
        for file in clientDCTPSFiles:
            extraZipSources.append((installSource64, file))

        zipRoot = vmware.GetConanBuildComponent(stageEnv, "7zip").env["PATH"]
        # Add DCT deps 7za to test zip
        zip7zDir = Dir(zipRoot)
        extraZipSources.append((zip7zDir.abspath, "7za.exe"))
        extraZipSources.append((zip7zDir.abspath, "7za.dll"))
        extraZipSources.append((zip7zDir.abspath, "7zxa.dll"))

        extraZipSources.append((installSourceDir("win64"), "ws_diag.exe"))

        zipFilePath = os.path.join(publishDir, "win64", "rxDCTComponentTestClient.zip")
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetNames,
            "win64",
            deliverableNodesNames,
            extraZipSources,
            zipFilePath,
        )
        stageEnv.NoCacheIfBuildNumberInfoOverridden(zipNode)
        stageEnv.Depends(zipNode, nodes)
        if zipNode is not None:
            nodes += zipNode

    return nodes


def stageClientSdkRxApiTest(stageEnv):
    """
    Stage the binaries for client SDK Rx API Test.
    """
    nodes = []
    targetNames = ["clientSdkRxApiTest"]

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        sdkEnv = vmware.LookupEnv("clientSdkCommon-env", "win64")
        publishDir = os.path.join(publishDir, "tests")
        deliverableNodesNames = ["clientSdkRxApiTest"]
        extraZipSources = []
        extraZipSources.extend(getClientSdkRXApiTestCommonDeps())
        # Add the test deployment config files
        deployDir = Dir(os.path.join(clientSdkRxApiTestDir, "deploy"))
        extraZipSources.append((deployDir.abspath, "."))

        # package vmwude driver
        if vmware.BuildType() == "obj":
            driverBuildType = "Debug"
        else:
            driverBuildType = "Release"
        driverBuildDir = os.path.join(
            vmware.DirAbsPath(vmware.BuildRoot()),
            vmware.Product(),
            "vmwude",
            "Win10" + driverBuildType,
            "x64",
            "bin",
        )
        for fileType in [
            ".inf",
            ".cat",
            ".pdb",
            ".sys",
        ]:
            file = "vmwude" + fileType
            extraZipSources.append((driverBuildDir, file))

        addDepsToZipSources(sdkEnv, ["LIBICONV_REDIST"], extraZipSources)
        zipFilePath = os.path.join(publishDir, "win64", "clientSdkRxApiTest.zip")
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetNames,
            "win64",
            deliverableNodesNames,
            extraZipSources,
            zipFilePath,
        )
        stageEnv.NoCacheIfBuildNumberInfoOverridden(zipNode)
        stageEnv.Depends(zipNode, nodes)
        if zipNode is not None:
            nodes += zipNode
    return nodes


def stageClientSdkSCAuthTest(stageEnv):
    """
    Stage the binaries for client SDK Smart Card Authentication Test.
    """
    nodes = []
    targetNames = ["scAuthTest"]

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(publishDir, "tests")
        deliverableNodesNames = ["scAuthTest"]
        # dependent libs
        extraZipSources = []
        extraZipSources.extend(getClientSdkRXApiTestCommonDeps())

        # libcurl ,openssl and libiconv
        sdkEnv = vmware.LookupEnv("clientSdkCommon-env", "win64")
        libs = sdkEnv["CURL_REDIST"] + sdkEnv["OPENSSL_FIPS_REDIST"]
        for lib in libs:
            libFile = stageEnv.File(lib)
            extraZipSources.append((libFile.dir.abspath, libFile.name))
        addDepsToZipSources(sdkEnv, ["LIBICONV_REDIST"], extraZipSources)

        # Add the test deployment config files
        scTestDir = vmware.DirAbsPath(
            "#horizonclient/view/openClient/sdk/tests/scAuthTest"
        )
        deployDir = Dir(os.path.join(scTestDir, "deploy"))
        extraZipSources.append((deployDir.abspath, "."))

        zipFilePath = os.path.join(publishDir, "win64", "scAuthTest.zip")
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetNames,
            "win64",
            deliverableNodesNames,
            extraZipSources,
            zipFilePath,
        )
        stageEnv.NoCacheIfBuildNumberInfoOverridden(zipNode)
        stageEnv.Depends(zipNode, nodes)
        if zipNode is not None:
            nodes += zipNode
    return nodes


def stageClientSdkSCAuthUnitTest(stageEnv):
    """stageClientSdkSCAuthUnitTest

    Stage the test library for client SDK Smart Card Authentication Unit Test.

    """
    stageEnv.LoadTool("zip-3.0")
    nodes = []
    targetName = ["clientSdkSCAuthUT"]
    publishDir = vmware.ReleasePackagesDir()
    extraZipSources = []
    testDataSrc = "#horizonclient/view/openClient/sdk/tests/scAuthTest/unitTest/"
    extraZipSources.append((Dir(testDataSrc).abspath, "testCerts"))

    if publishDir is not None:
        extraZipSources.extend(getClientSdkRXApiTestCommonDeps())
        # libcurl and openssl lib
        sdkEnv = vmware.LookupEnv("clientSdkSCAuthUT-env", "win64")
        deps = [
            "CURL_REDIST",
            "OPENSSL_REDIST",
            "LIBICONV_REDIST",
            "GTEST_REDIST",
        ]
        addDepsToZipSources(sdkEnv, deps, extraZipSources)
        addDepsToZipSources(
            vmware.LookupEnv("omnissabaselib-env", host),
            ["LIBPNG_REDIST", "ZLIB_REDIST"],
            extraZipSources,
        )
        zipFilePath = File(
            os.path.join(publishDir, "tests", "win64", "clientSdkSCAuthUT.zip")
        )
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetName,
            "win64",
            targetName,
            extraZipSources,
            zipFilePath,
            excludedFileFormats=[".map"],
        )
        if zipNode is not None:
            nodes += zipNode
    return nodes


def stagePartnerAPItestPackage(stageEnv, allNodes, destDir):
    """
    Stage all files for PartnerAPItest.

    """
    partnerAPItestNodes = []
    for host, suffix in [
        ["win32", "32"],
        ["win64", "64"],
        ["win-arm64ec", "ARM64EC"],
    ]:
        partnerAPITestZipFileName = "controllerAPITests-%s-%s-%s-%s.zip" % (
            yymmVersion,
            productVersion,
            buildNum,
            suffix,
        )
        # partner api test
        partnerAPITestZipSources = []
        partnerAPITestZipDependencies = []
        for apiTest in [
            "controllerAPITest",
            "controllerAPIApiTest",
            "RemoteClientManager",
        ]:
            apiTestFileName = apiTest + suffix
            partnerAPITestSources, partnerAPITestDependencies = (
                getZipSourcesFromNodeFileNames(allNodes, apiTestFileName, "")
            )
            partnerAPITestZipSources += partnerAPITestSources
            partnerAPITestZipDependencies += partnerAPITestDependencies

        # gtest deps
        deps = vmware.LookupEnv("controllerAPIApiTest-env", host)["GTEST_REDIST"]
        for dep in deps:
            partnerAPITestZipSources.append(
                (os.path.dirname(os.path.abspath(str(dep))), os.path.basename(str(dep)))
            )

        partnerAPITestZipFile = stageEnv.Zip(
            Dir(destDir).File(partnerAPITestZipFileName), partnerAPITestZipSources
        )

        stageEnv.Depends(partnerAPITestZipFile, partnerAPITestZipDependencies)
        stageEnv.NoCacheIfBuildNumberInfoOverridden(partnerAPITestZipFile)

        partnerAPItestNodes.append(partnerAPITestZipFile)

    return partnerAPItestNodes


def stageClientUtPackage(stageEnv, destDir):
    """
    Stage all files for client unit test package.

    This function zips the dependent files in the source folders
    installSource32/64 to the client unit test node. It is zipped into the
    directory indicated by the input parameter destDir, which is actually
    ReleasePackagesDir()/tests/
    """

    unittestScripts = "#horizonclient/view/winClient/Tests/scripts"
    clientCopyNode = vmware.utils.StageCopyDirectory(
        stageEnv.Dir(unittestScripts).abspath,
        os.path.join(Dir(installSourceRoot).abspath, "scripts"),
        stageEnv.LinkCopy,
    )

    unitTestZipNodes = []
    scriptsDir = Dir(os.path.join(Dir(installSourceRoot).abspath, "scripts"))
    unitTestZipFileName = (
        "unittest-Omnissa-Horizon-Client-Windows-Intel-%s-%s-%s.zip"
        % (yymmVersion, productVersion, buildNum)
    )

    dirs = []
    commands = []
    dirs.append(scriptsDir)
    commands.append(
        vmware.ActionWithDisplay(
            "$CD %s && $ZIP -g -r ${TARGET.abspath} %s"
            % (Dir(installSourceRoot).abspath, "scripts"),
            "Zipping %s to ${TARGET.abspath}" % (scriptsDir.abspath),
        )
    )

    excludedZipTypes = "*.pdb *.map"
    dirs.append(Dir(installSourceDir("win64")))
    commands.append(
        vmware.ActionWithDisplay(
            "$CD %s && $ZIP -r -x %s -g ${TARGET.abspath} %s"
            % (
                stageEnv["ESCAPE"](vmware.DirAbsPath(installSourceRoot)),
                excludedZipTypes,
                "x64",
            ),
            "Zipping %s to ${TARGET.abspath}"
            % (stageEnv["ESCAPE"](installSourceDir("win64"))),
        )
    )

    includedZipFiles = "x64/*UnitTest.pdb"
    commands.append(
        vmware.ActionWithDisplay(
            "$CD %s && $ZIP -g ${TARGET.abspath} %s"
            % (
                stageEnv["ESCAPE"](vmware.DirAbsPath(installSourceRoot)),
                includedZipFiles,
            ),
            "Zipping %s to ${TARGET.abspath}" % (includedZipFiles),
        )
    )

    unitTestZipNodes += stageEnv.Command(
        Dir(destDir).File(unitTestZipFileName), dirs, commands
    )

    stageEnv.Depends(unitTestZipNodes, clientCopyNode)
    stageEnv.NoCache(unitTestZipNodes)

    return unitTestZipNodes


def stageHorizonRxTestModule(stageEnv, targetName, publishBinaries, testScripts, deps):
    """
    Stage the horizonrxtest modules.

    :param targetName: the name of the horizonrxtest modules.
    :param publishBinaries: the list of horizonrxtest binaries to lookup.
    :param testScripts: some scripts for running cases/deploy ...
    :param deps: list of the 3rd dependencies
    """
    nodes = []
    stageNodes = []

    # horizonrxtest binaries
    for componentNode in publishBinaries:
        binaryNodes = vmware.pkg.StageDeliverables(
            stageEnv,
            componentNode,
            "win64",
            os.path.join(vmware.pkg.stagePath, targetName),
            excludedFileFormats=[".pdb", ".map"],
        )
        stageNodes += binaryNodes

    # deploy, undeploy, run python
    stageDest = vmware.PathPrefixer(vmware.pkg.stagePath) / targetName
    for file in testScripts:
        filename = os.path.basename(file.abspath)
        stageNodes += stageEnv.FileCopy(Dir(stageDest).File(filename), file)
    utilitySrc = Dir("#bora/apps/horizonrxtest/componentTest/deploy/utility")
    utilityFiles = vmware.EnumerateSourceDir(utilitySrc)
    utilityDest = Dir(stageDest / "utility")
    stageNodes += vmware.DirCopy(utilityFiles, utilitySrc, utilityDest, stageEnv)

    # 3rd libs
    for lib in deps:
        f = stageEnv.File(lib)
        stageNodes += stageEnv.FileCopy(Dir(stageDest).File(f.name), f)

    # all stage nodes
    nodes += stageNodes

    # zip & publish
    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(publishDir, "tests", "horizonrxtest", "win64")
        zipDest = File(os.path.join(publishDir, "%s.zip" % targetName))
        zipNode = stageEnv.ZipDir(zipDest, Dir(stageDest), ".", "0644")
        stageEnv.Depends(zipNode, stageNodes)
        nodes += zipNode

    return nodes


def stageHorizonRxTestClient(stageEnv):
    """
    Stage the horizonrxtest package into horizonrxtestClient.zip.
    """
    targetName = "horizonrxtestClient"

    # horizonrxtest.exe, rxci_socket_vchan.dll, rxci_socket_mfw.dll
    publishBinaries = [
        "rxci_socket_vchan",
        "rxci_socket_mfw",
        "horizonrxtest",
    ]

    # deploy, undeploy, run python
    deploySrc = "#bora/apps/horizonrxtest/componentTest/deploy"
    fileruncases = Dir(deploySrc).File("horizonrxtest_run_cases.py")
    fileclient = Dir(deploySrc).File("horizonrxtest_client_win.ini")
    fileundeploy = Dir(deploySrc).File("horizonrxtest_undeploy.py")
    filedeploy = Dir(deploySrc).File("horizonrxtest_deploy.py")
    filehealthcheck = Dir(deploySrc).File("horizonrxtest_health_check.py")
    testScripts = [
        fileruncases,
        fileclient,
        fileundeploy,
        filedeploy,
        filehealthcheck,
    ]

    # 3rd libs
    buildEnv = vmware.LookupEnv("horizonrxtest-env", host)
    deps = (
        buildEnv["CURL_REDIST"]
        + buildEnv["ZLIB_REDIST"]
        + buildEnv["OPENSSL_REDIST"]
        + buildEnv["MSVC_RELEASE_CRT_REDIST"]
        + buildEnv["REDIST"]
    )

    return stageHorizonRxTestModule(
        stageEnv, targetName, publishBinaries, testScripts, deps
    )


def stageHorizonRxUt(stageEnv):
    """
    Stage the horizonrxut package into horizonrxut.zip.
    """
    stageNodes = []
    targetName = "horizonrxut"
    publishBinaries = ["horizonrxut", "rxSampleUnitTest"]

    # 3rd libs
    buildEnv = vmware.LookupEnv("horizonrxut-env", host)
    deps = buildEnv["MSVC_RELEASE_CRT_REDIST"] + buildEnv["REDIST"]
    if vmware.BuildType() == "obj":
        deps += buildEnv["MSVC_DEBUG_CRT_REDIST"]
        deps += buildEnv["WINDOWS_DEBUG_CRT_REDIST"]

    # Get run case and deployment python script
    scriptSrc = "#bora/apps/horizonrxtest/componentTest/deploy"
    runcasesScript = Dir(scriptSrc).File("horizonut_run_cases.py")
    deployScript = Dir(scriptSrc).File("horizonut_deploy.py")
    utSampleSrc = "#bora/apps/horizonrxtest/unitTest/sample"
    utExample = Dir(utSampleSrc).File("rxSampleUnitTest.json")
    testScripts = [
        runcasesScript,
        deployScript,
        utExample,
    ]

    # include rxSampleUnitTest.pdb for Windows
    if vmware.Host().IsWindows:
        nodes = vmware.pkg.LookupDeliverableNodes("rxSampleUnitTest", host)
        stageDest = vmware.PathPrefixer(vmware.pkg.stagePath) / targetName
        for node in nodes:
            if "rxSampleUnitTest.pdb" in node.name:
                stageNodes += stageEnv.FileCopy(Dir(stageDest).File(node.name), node)
                break

    stageNodes += stageHorizonRxTestModule(
        stageEnv, targetName, publishBinaries, testScripts, deps
    )
    return stageNodes


def stageScreenCaptureUnitTest(stageEnv):
    """ScreenCaptureUnitTest

    Stage the binaries for Screen Capture Unit Test.
    """
    nodes = []
    targetName = ["screenCaptureClientUt"]

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(vmware.DirAbsPath(publishDir), "tests")
        extraZipSources = []
        testEnv = vmware.LookupEnv("screenCaptureClientUt-env", host)
        deps = [
            "SIGC_REDIST",
            "GLIBMM_REDIST",
            "GETTEXT_REDIST",
            "GLIB_REDIST",
            "PCRE2_REDIST",
            "LIBPNG_REDIST",
            "ZLIB_REDIST",
            "GTEST_REDIST",
        ]
        addDepsToZipSources(testEnv, deps, extraZipSources)

        zipFilePath = os.path.join(publishDir, host, "screenCaptureRdeUnitTest.zip")
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetName,
            host,
            targetName,
            extraZipSources,
            zipFilePath,
            excludedFileFormats=[".map"],
        )
        if zipNode is not None:
            nodes += zipNode

    return nodes


def stageClipboardUnitTest(stageEnv):
    """stageClipboardUnitTest
    Stage the binaries for Clipboard Unit Test.
    """

    nodes = []
    targetName = ["clipboardUnitTest"]

    deliverableNodesNames = [
        "clipboardUnitTest",
        "omnissabaselib",
    ]

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(publishDir, "tests")

        extraZipSources = []
        host = "win64"

        testEnv = vmware.LookupEnv("clipboardUnitTest-env", host)
        for redist in testEnv["OPENSSL_REDIST"]:
            extraZipSources.append(os.path.split(redist))
        for lib in testEnv["ZLIB_REDIST"]:
            extraZipSources.append(
                (os.path.dirname(lib.abspath), os.path.basename(lib.abspath))
            )
        # Gather the list of all third party dependencies.
        deps = [
            "SIGC_REDIST",
            "GLIBMM_REDIST",
            "GETTEXT_REDIST",
            "GLIB_REDIST",
            "PCRE2_REDIST",
            "OPENSSL_FIPS_REDIST",
            "ZLIB_REDIST",
            "LIBICONV_REDIST",
            "LIBPNG_REDIST",
        ]
        addDepsToZipSources(testEnv, deps, extraZipSources)

        zipFilePath = os.path.join(publishDir, host, "clipboardUnitTest.zip")
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetName,
            host,
            deliverableNodesNames,
            extraZipSources,
            zipFilePath,
        )
        if zipNode is not None:
            nodes += zipNode

    return nodes


def stageClipboardClientComponentTest(stageEnv):
    """stageClipboardClientComponentTest
    Stage the binaries for Clipboard Component Test.
    """

    nodes = []
    targetName = ["mksvchanComponentTest"]

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(publishDir, "tests")
        deliverableNodesNames = [
            "fcpComponentTestVerifier",
            "mksvchanComponentTest",
            "mksvchanclient",
            "tsdrClient",
            "vdpservice",
            "omnissabaselib",
        ]

        extraZipSources = []
        extraZipSources.append(
            (
                stageEnv.Dir(
                    "#bora/apps/rde/mksvchan/tests/" "componenttest/clipboard"
                ).abspath,
                "mksvchan_client_win.ini",
            )
        )
        extraZipSources.append(
            (
                stageEnv.Dir(
                    "#bora/apps/rde/mksvchan/tests/componenttest/" "clipboard"
                ).abspath,
                "configuration",
            )
        )

        testDataSrc = (
            "#bora/apps/rde/mksvchan/tests/componenttest/" "clipboard/testData"
        )
        extraZipSources.append((Dir(testDataSrc).abspath, "."))

        host = "win64"
        # Gather the list of all third party dependencies to copy.
        testEnv = vmware.LookupEnv("mksvchanComponentTest-env", host)
        deps = [
            "SIGC_REDIST",
            "GLIBMM_REDIST",
            "GETTEXT_REDIST",
            "GLIB_REDIST",
            "PCRE2_REDIST",
            "OPENSSL_FIPS_REDIST",
            "ZLIB_REDIST",
            "LIBICONV_REDIST",
            "LIBPNG_REDIST",
        ]
        addDepsToZipSources(testEnv, deps, extraZipSources)

        zipFilePath = os.path.join(publishDir, host, "mksvchanComponentTestClient.zip")
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetName,
            host,
            deliverableNodesNames,
            extraZipSources,
            zipFilePath,
        )
        if zipNode is not None:
            nodes += zipNode

    return nodes


def stageFcpUnitTest(stageEnv):
    """stageFcpUnitTest
    Stage the binaries for FCP Unit Test.
    """

    nodes = []
    targetName = ["fcpUnitTest"]

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(publishDir, "tests")
        deliverableNodesNames = [
            "fcpUnitTest",
            "omnissabaselib",
        ]

        extraZipSources = []
        host = "win64"

        testEnv = vmware.LookupEnv("fcpUnitTest-env", host)
        for redist in testEnv["OPENSSL_REDIST"]:
            extraZipSources.append(os.path.split(redist))
        for lib in testEnv["ZLIB_REDIST"]:
            extraZipSources.append(
                (os.path.dirname(lib.abspath), os.path.basename(lib.abspath))
            )
        # Gather the list of all third party dependencies.
        deps = [
            "SIGC_REDIST",
            "GLIBMM_REDIST",
            "GETTEXT_REDIST",
            "GLIB_REDIST",
            "PCRE2_REDIST",
            "LIBICONV_REDIST",
            "LIBPNG_REDIST",
        ]
        addDepsToZipSources(testEnv, deps, extraZipSources)

        zipFilePath = os.path.join(publishDir, host, "fcpUnitTest.zip")
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetName,
            host,
            deliverableNodesNames,
            extraZipSources,
            zipFilePath,
            excludedFileFormats=[".map"],
        )
        if zipNode is not None:
            nodes += zipNode

    return nodes


def stageScannerRedirUnitTest(stageEnv):
    """stageScannerRedirUnitTest
    Stage the binaries for Scanner Redirection UT.
    """

    nodes = []
    targetName = ["scannerRedirUt", "scannerRedirUtTrayResource"]

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(publishDir, "tests")
        deliverableNodesNames = ["scannerRedirUt", "scannerRedirUtTrayResource"]
        host = "win64"
        extraZipSources = []
        testEnv = vmware.LookupEnv("scannerRedirUt-env", host)
        deps = [
            "GTEST_REDIST",
            "LIBPNG_REDIST",
            "ZLIB_REDIST",
        ]
        addDepsToZipSources(testEnv, deps, extraZipSources)

        zipFilePath = os.path.join(publishDir, host, "scannerRedirUt.zip")
        nodes += vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetName,
            host,
            deliverableNodesNames,
            extraZipSources,
            zipFilePath,
            excludedFileFormats=[".map"],
        )
    return nodes


def stageScannerRedirClientComponentTest(stageEnv):
    """stageScannerRedirClientComponentTest
    Stage the binaries for Scanner Redirection Client.
    """

    nodes = []

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is None:
        return nodes

    host = "win32"

    zipNodes = []
    zipNodes += vmware.LookupNode("scannerRedirClientApp", host)

    zipSources = []
    for zipNode in zipNodes:
        zipSources.append((zipNode.dir.abspath, zipNode.name))

    zipFile = os.path.join(publishDir, "tests", host, "scannerRedirClient32.zip")
    zipNode = stageEnv.Zip(zipFile, zipSources)
    nodes += zipNode

    host = "win64"

    zipNodes = []
    zipNodes += vmware.LookupNode("scannerRedirClient", host)
    zipNodes += vmware.LookupNode("scannerRedirClientApp", host)

    zipSources = []
    for zipNode in zipNodes:
        zipSources.append((zipNode.dir.abspath, zipNode.name))

    zipFile = os.path.join(publishDir, "tests", host, "scannerRedirClient.zip")
    zipNode = stageEnv.Zip(zipFile, zipSources)
    nodes += zipNode

    return nodes


def stageTsdrComponentClientTest(stageEnv):
    """stageTsdrComponentClientTest

    Stage the binaries for Tsdr Component Client Test.
    """

    nodes = []
    targetNames = ["tsdrComponentTest"]

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(vmware.DirAbsPath(publishDir), "tests")
        deliverableNodesNames = [
            "tsdrClient",
            "tsdrComponentTest",
            "vdpservice",
            "omnissabaselib",
        ]
        extraZipSources = []
        extraZipSources.append(
            (
                stageEnv.Dir("#bora/apps/rde/tsdr/tests/componenttest").abspath,
                "tsdr_client_win.ini",
            )
        )

        host = "win64"
        testEnv = vmware.LookupEnv("tsdrComponentTest-env", host)
        # Publish 3-part dependency
        # Gather the list of all dependencies to copy.
        deps = [
            "SIGC_REDIST",
            "GLIBMM_REDIST",
            "GETTEXT_REDIST",
            "GLIB_REDIST",
            "PCRE2_REDIST",
            "OPENSSL_FIPS_REDIST",
            "ZLIB_REDIST",
            "LIBICONV_REDIST",
            "LIBPNG_REDIST",
        ]
        addDepsToZipSources(testEnv, deps, extraZipSources)

        zipFilePath = os.path.join(publishDir, host, "tsdrComponentTestClient.zip")
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetNames,
            host,
            deliverableNodesNames,
            extraZipSources,
            zipFilePath,
        )
        if zipNode is not None:
            nodes += zipNode

    return nodes


def stageTsdrClientUnitTest(stageEnv):
    """stageTsdrClientUnitTest

    Stage the binaries for Tsdr Client Unit Test.
    """

    nodes = []
    targetName = ["tsdrClientUnitTest"]
    deliverableNodesNames = [
        "tsdrClientUnitTest",
        "omnissabaselib",
    ]

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(vmware.DirAbsPath(publishDir), "tests")
        extraZipSources = []

        host = "win64"
        testEnv = vmware.LookupEnv("tsdrClientUnitTest-env", host)

        # Publish 3-part dependency
        # Gather the list of all dependencies to copy.
        deps = [
            "SIGC_REDIST",
            "GLIB_REDIST",
            "GETTEXT_REDIST",
            "GLIBMM_REDIST",
            "PCRE2_REDIST",
            "OPENSSL_FIPS_REDIST",
            "ZLIB_REDIST",
            "LIBICONV_REDIST",
            "LIBPNG_REDIST",
        ]
        addDepsToZipSources(testEnv, deps, extraZipSources)
        zipFilePath = os.path.join(publishDir, host, "tsdrClientUnitTest.zip")
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetName,
            host,
            deliverableNodesNames,
            extraZipSources,
            zipFilePath,
            excludedFileFormats=[".map"],
        )
        if zipNode is not None:
            nodes += zipNode

    return nodes


def stageSdrUnitTest(stageEnv):
    """stageSdrUnitTest
    Stage the binaries for Storage Drive Redirection UT.
    """

    nodes = []
    targetName = ["sdrUt"]

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(publishDir, "tests")
        deliverableNodesNames = ["sdrUt"]
        host = "win64"
        extraZipSources = []
        testEnv = vmware.LookupEnv("sdrUt-env", host)
        deps = [
            "GTEST_REDIST",
        ]
        addDepsToZipSources(testEnv, deps, extraZipSources)
        zipFilePath = os.path.join(publishDir, host, "sdrUt.zip")
        nodes += vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetName,
            host,
            deliverableNodesNames,
            extraZipSources,
            zipFilePath,
            excludedFileFormats=[".map"],
        )
    return nodes


def stageSdrClientComponentTest(stageEnv):
    """Stage storage drive redirection CI test packages and binaries

    stageEnv specifies a stage environment object.
    """
    nodes = []

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is None:
        return nodes

    host = "win64"

    zipNodes = []
    zipNodes += vmware.LookupNode("sdrclienthost", host)
    zipNodes += vmware.LookupNode("sdrclient", host)
    zipNodes += vmware.LookupNode("omnissabaselib", host)

    # Publish CI scripts
    scriptsSrc = "#bora/apps/sdr/componentTest"
    zipNodes += [Dir(scriptsSrc)]
    toolsSrc = "#bora/apps/sdr/componentTest/tools"
    zipNodes += vmware.EnumerateSourceDir(toolsSrc)

    zipSources = []
    for zipNode in zipNodes:
        zipSources.append((zipNode.dir.abspath, zipNode.name))

    testEnv = vmware.LookupEnv("omnissabaselib-env", host)
    deps = [
        "OPENSSL_FIPS_REDIST",
        "ZLIB_REDIST",
        "LIBPNG_REDIST",
    ]
    addDepsToZipSources(testEnv, deps, zipSources)

    zipFile = os.path.join(publishDir, "tests", host, "sdrClientTest.zip")
    zipNode = stageEnv.Zip(zipFile, zipSources)
    nodes += zipNode

    return nodes


def stageTSMMRClientComponentTest(stageEnv):
    """stageTSMMRClientComponentTest

    Stage the binaries for tsmmr Component Test.
    Please note this feature is only support for Windows and Linux platform.
    """

    nodes = []
    targetNames = ["tsmmrComponentTest"]

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(publishDir, "tests")
        deliverableNodesNames = ["tsmmrComponentTest", "tsmmrClient", "vdpservice"]
        host = "win64"
        extraZipSources = []
        extraZipSources.append(
            (
                stageEnv.Dir(
                    "#bora/apps/rde/tsmmr/tests/componenttest/" "deployScript"
                ).abspath,
                "tsmmr_client_win.ini",
            )
        )

        zipFilePath = os.path.join(publishDir, host, "tsmmrComponentTestClient.zip")
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetNames,
            host,
            deliverableNodesNames,
            extraZipSources,
            zipFilePath,
        )
        if zipNode is not None:
            nodes += zipNode

    return nodes


def stageSignedDlls(stageEnv):
    """SignedDlls

    Stage the binaries for urlProtocolIntercept and uncRedirect

    """
    nodes = []
    releaseDir = vmware.ReleasePackagesDir()
    if releaseDir is not None:
        targetName = [
            "urlProtocolIntercept",
            "uncRedirection",
        ]
        extraZipSources = []
        excludedFileFormats = [".map"]
        archs = ["win64", "win-arm64ec"]

        for arch in archs:
            publishDir = os.path.join(releaseDir, "signedDlls", arch)
            zipFilePath = File(os.path.join(publishDir, "signedDlls.zip"))

            zipNode = vmware.pkg.CreateZipStageNode(
                stageEnv,
                targetName,
                arch,
                targetName,
                extraZipSources,
                zipFilePath,
                excludedFileFormats,
            )
            if zipNode is not None:
                nodes += zipNode
    return nodes


def publishTestZips(stageEnv, allNodes, destDir):
    """
    Publishes all testing-related zip files.

    This function uses entries from allNodes as the zip sources because those
    are already signed.

    """
    nodes = []

    # First publish the partner API test harness zip file.
    nodes += stagePartnerAPItestPackage(stageEnv, allNodes, destDir)

    # Next publish the testE2E zip file, containing various tests. People may
    # want to run these locally, so we copy them into a subdirectory of the
    # build directory and then zip that directory.
    testE2EZipFileName = "testE2E-VMware-view-client-Windows-%s-%s-%s.zip" % (
        yymmVersion,
        productVersion,
        buildNum,
    )
    zipNode = stageEnv.Command(
        Dir(destDir).File(testE2EZipFileName),
        Dir(testE2EBaseDir).abspath,
        "cd $SOURCE && " '$ZIP -r -u ${TARGET.abspath} * -x "*.pdb" -x "*.map"',
    )
    # The "del" command in the action makes this action not trackable and thus
    # not cacheable.
    stageEnv.NoCache(zipNode)
    # Avoid having to maintain a list of all testE2E dependencies by just
    # looking through all testE2E-related nodes in allNodes.
    stageEnv.Depends(zipNode, [node for node in allNodes if "testE2E" in node.abspath])
    nodes += zipNode

    # Next publish the autotest zip file
    autotestZipFileName = "autotest-VMware-Horizon-Client-Windows-%s-%s-%s.zip" % (
        yymmVersion,
        productVersion,
        buildNum,
    )
    autotestZipNode = stageEnv.Command(
        Dir(destDir).File(autotestZipFileName),
        Dir(autotestBaseDir).abspath,
        "cd $SOURCE && $ZIP -r -u ${TARGET.abspath} *",
    )
    stageEnv.NoCacheIfBuildNumberInfoOverridden(autotestZipFileName)
    # Avoid having to maintain a list of all autotest dependencies by just
    # looking through all autotest-related nodes in allNodes.
    stageEnv.Depends(
        autotestZipNode, [node for node in allNodes if "autotest" in node.abspath]
    )
    nodes += autotestZipNode

    # Publish the viewClient (remotemks) stub zip file. First we copy files
    # into an intermediate directory then we zip that directory.
    viewClientStubDir = stageEnv.Dir(installSource64).Dir("viewClientStub")
    viewClientStubStageNodes = []
    viewClientStubArchDir = viewClientStubDir.Dir("x64")
    for n in vmware.LookupNode("viewClientStub", "win64"):
        viewClientStubStageNodes += stageEnv.LinkCopy(
            viewClientStubArchDir.File(n.name), n
        )
    viewClientStubZipFileName = "horizon-protocol-stub-%s-%s-windows.zip" % (
        vmware.ProductVersionNumber(),
        vmware.BuildNumber(),
    )
    viewClientStubZipNode = stageEnv.ZipDir(
        Dir(destDir).File(viewClientStubZipFileName),
        viewClientStubDir.dir,
        viewClientStubDir.name,
    )
    stageEnv.Depends(viewClientStubZipNode, viewClientStubStageNodes)
    stageEnv.NoCacheIfBuildNumberInfoOverridden(viewClientStubZipNode)
    nodes += viewClientStubZipNode

    nodes += stageClientUtPackage(stageEnv, destDir)

    nodes += stageDCTClientComponentTest(stageEnv)
    nodes += stageClientSdkRxApiTest(stageEnv)
    nodes += stageClientSdkSCAuthTest(stageEnv)
    nodes += stageClientSdkSCAuthUnitTest(stageEnv)
    nodes += stageCrtboraScreenCaptureUnitTest(stageEnv)
    nodes += stageCrtboraDnDUnitTest(stageEnv)
    nodes += stageClientSdkRxApiUnitTest(stageEnv)
    nodes += stageRTAVUnitTest(stageEnv)
    nodes += stageRTAVClientComponentTest(stageEnv)
    nodes += stagePrintRedirComponentTest(stageEnv)
    nodes += stagePrintRedirUnitTest(stageEnv)
    nodes += stageClientFIDO2UnitTest(stageEnv)
    nodes += stageClientUSBUnitTest(stageEnv)
    nodes += stageFido2ClientComponentTest(stageEnv)
    nodes += stageUSBClientComponentTest(stageEnv)
    nodes += stageScreenCaptureUnitTest(stageEnv)
    nodes += stageClipboardUnitTest(stageEnv)
    nodes += stageClipboardClientComponentTest(stageEnv)
    nodes += stageFcpUnitTest(stageEnv)
    nodes += stageTlmRdpvcbridgeUnitTest(stageEnv)
    nodes += stageTlmRdpvcbridgeComponentTest(stageEnv)
    nodes += stageURLComponentTestClient(stageEnv)
    nodes += stageURLUnitTest(stageEnv)
    nodes += stageUNCUnitTest(stageEnv)
    nodes += stageScannerRedirUnitTest(stageEnv)
    nodes += stageScannerRedirClientComponentTest(stageEnv)
    nodes += stageTsdrComponentClientTest(stageEnv)
    nodes += stageTsdrClientUnitTest(stageEnv)
    nodes += stageSdrUnitTest(stageEnv)
    nodes += stageSdrClientComponentTest(stageEnv)
    nodes += stageTSMMRClientComponentTest(stageEnv)

    # Stage horizonrxtest modules
    nodes += stageHorizonRxTestClient(stageEnv)
    nodes += stageHorizonRxUt(stageEnv)
    return nodes


def publishCDSFiles(stageEnv, installerNode, publishDir, cdsDir):
    """
    Publishes all CDS-related files into the folder specified by cdsDir and
    also into a zip file in the folder specified by publishDir.
    installerNode is passed because it is included in the CDS payload.
    """
    stageEnv.LoadTool("cds-repo")

    # Initialize some variables that will be used later in this function.
    nodes = []
    cdsProductId = "viewcrt-windows"
    payloadMimeType = "omnissa/win-vmsetup-exe"
    earliestUpgradeVersion = "8.0.0"
    cdsCustomParams = {"marketVersion": yymmVersion}
    productDir = os.path.join(cdsDir, cdsProductId)
    payloadDir = os.path.join(productDir, productVersion, buildNum)
    metadataXmlGZPath = os.path.join(payloadDir, "metadata.xml.gz")
    tarName = "Omnissa-Horizon-Client-%s-%s-%s.exe.tar" % (
        yymmVersion,
        productVersion,
        buildNum,
    )
    tarPath = os.path.join(payloadDir, tarName)

    # Generate the CDS .tar file.
    bulletinNode = stageEnv.CDSBulletinMSI(
        File(tarPath),
        File(installerNode),
        payloadMimeType,
        cdsProductId,
        "all",
        productVersion,
        visibleTo=["%s,%s" % (cdsProductId, earliestUpgradeVersion)],
        customParams=cdsCustomParams,
    )
    stageEnv.Depends(bulletinNode, installerNode)
    nodes += bulletinNode

    # Generate the CDS metadata.
    metadataNode = stageEnv.CDSMetadata(
        metadataXmlGZPath,
        bulletinNode,
        "#horizonclient/install/windows/client/cds_resources/bulletins",
        "zh-tw,de,en,fr,ja,ko,zh-cn,es",
        secureDownload=True,
    )
    nodes += metadataNode

    # Generate the CDS repo.
    repoNode = stageEnv.CDSRepo(cdsDir, productDir)
    stageEnv.Depends(repoNode, nodes)
    nodes += repoNode

    # Zip it all together.
    zipFile = Dir(publishDir).File(
        "Omnissa-Horizon-Client-AutoUpdate-%s-%s-%s.zip"
        % (yymmVersion, productVersion, buildNum)
    )
    zipNode = stageEnv.Zip(
        zipFile, [(os.path.dirname(cdsDir), os.path.basename(cdsDir))]
    )
    stageEnv.NoCacheIfBuildNumberInfoOverridden(zipNode)
    stageEnv.Depends(zipNode, nodes)
    nodes += zipNode

    return nodes


def publishSdkNugetPackage(env, publishDir, packName, isPrimitive=False):
    """
    Packs and publishes the Nuget package of the .NET client sdk into the
    folder specified by publishDir.
    """
    srcPP = vmware.PathPrefixer("#horizonclient/view/openClient/sdk/semi-public/dotnet")
    packProj = srcPP.File("VMware.Horizon.Client.SDK.Pack.csproj")
    outputPath = vmware.DirAbsPath(vmware.phase.ComponentDir(packName))
    nupkgName = "%s.%s.nupkg" % (packName, pkgVer)
    packPath = vmware.FileAbsPath(outputPath, nupkgName)
    node = env.DotnetCli(
        "pack",
        [packPath],
        [packProj],
        "-c",
        "Release" if vmware.BuildType() == "release" else "Debug",
        "-o",
        outputPath,
        "/p:PackId=%s" % packName,
        "/p:IsPrimitive=%s" % isPrimitive,
        "/p:BaseIntermediateOutputPath=%s\\obj/" % outputPath,
        "/p:PackSrcPath=%s" % installSourceRoot,
        "/p:I18nPath=%s" % os.path.join(installSource32, "i18n"),
        "/p:PackageVersion=%s" % pkgVer,
    )

    env.Depends(node, vmware.EnumerateSourceDir(Dir(installSourceRoot)))
    env.Depends(node, packProj)
    env.Depends(node, winClientCopyNodes + dependencyCopyNodes)
    node += env.FileCopyEx(Dir(publishDir), packPath)
    env.NoCache(node)

    return node


def stageCrtboraAPITest(stageEnv):
    """stageCrtboraAPITest

    Stage the binaries for crtbora API Test.
    Please note currently it is done for the Windows platform.
    In the future we will add support for other platforms.
    """

    nodes = []
    targetName = "crtboraApiTest"
    vmware.PathPrefixer(vmware.pkg.stagePath) / targetName
    stageEnv.LoadTool("zip-3.0")

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        # Publish crtboraApiTest package
        zipSources = []

        # Gather the list of all dependencies to copy.
        testEnv = vmware.LookupEnv("crtboraApiTest", "win64")
        viewClientEnv = vmware.LookupEnv("viewClient", "win64")
        deps = [
            "SIGC_REDIST",
            "GLIBMM_REDIST",
            "GETTEXT_REDIST",
            "GLIB_REDIST",
            "PCRE2_REDIST",
            "OPENSSL_FIPS_REDIST",
            "GTEST_REDIST",
            "LIBICONV_REDIST",
        ] + (["SANITIZER_REDISTS"] if "SANITIZER_REDISTS" in testEnv else [])

        addDepsToZipSources(testEnv, deps, zipSources)
        addDepsToZipSources(viewClientEnv, ["LIBPNG_REDIST", "ZLIB_REDIST"], zipSources)

        # Zip vcruntime debug file
        if vmware.BuildType() == "obj":
            vdRunTimeDep = GetVCRuntimeFiles("x64", vmware.BuildType())
            for file in vdRunTimeDep:
                zipSources.append(os.path.split(file))

        # Zip omnissabase and omnissastring
        zipSources += getZipSourcesFromNodeLookup(
            ["omnissabaselib", "omnissastring", "hznprotect", "crtboraApiTest"],
            "win64",
            True,
        )

        zipSources.append((vmware.DirAbsPath("#bora/apps/crtbora/win32/tests/"), "res"))

        zipSources.append(
            (vmware.DirAbsPath("#bora/apps/crtbora/componentTest/appRemoting/"), "data")
        )

        zipSources.append(
            (
                vmware.DirAbsPath(
                    "#bora/apps/crtbora/componentTest/appRemoting/scripts"
                ),
                "crtboraApiTest.py",
            )
        )

        # Use crtboraApiTest.zip to adapt to the CI deploy script.
        zipFile = File(os.path.join(publishDir, "tests", "win64", "crtboraApiTest.zip"))
        zipNode = stageEnv.Zip(zipFile, zipSources)
        nodes += zipNode

        vmware.Alias("crtboraApiTest-stage", zipNode)

    return nodes


def stageRdeClientTest(stageEnv):
    """stageRdeClientTest
    Stage the bundle for windows rdeClient Test.
    """
    nodes = []
    targetName = "rdeClientTest"
    vmware.PathPrefixer(vmware.pkg.stagePath) / targetName
    stageEnv.LoadTool("zip-3.0")

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        # Publish rdeClientTest package
        zipSources = []
        sourceNodes = vmware.LookupNode(targetName, "win64")
        for node in sourceNodes:
            zipSources.append((node.dir.abspath, node.name))

        viewClientEnv = vmware.LookupEnv("viewClient", "win64")
        rdeClientTestEnv = vmware.LookupEnv("rdeClientTest", "win64")
        deps = (
            [viewClientEnv["ZLIB_REDIST"][0].abspath]
            + [viewClientEnv["LIBPNG_REDIST"][0].abspath]
            + rdeClientTestEnv["GTEST_REDIST"]
        )
        for dep in deps:
            # Copy the actual dependency dll or exe.
            zipSources.append(
                (os.path.dirname(os.path.abspath(str(dep))), os.path.basename(str(dep)))
            )
        appRemotingDir = vmware.DirAbsPath(
            "#bora/apps/crtbora/componentTest/appRemoting"
        )
        zipSources.append((appRemotingDir + "/scripts", "crtboraApiTest.py"))
        data = vmware.DirAbsPath(
            "#bora/apps/rde/rdeSvc/tests/componentTest/rdeClientTest"
        )
        zipSources.append((data, "data"))
        zipFile = File(os.path.join(publishDir, "tests", "win64", targetName + ".zip"))
        nodes += stageEnv.Zip(zipFile, zipSources)
    return nodes


def stageViewClientAPITest(stageEnv):
    """stageViewClientAPITest
    Stage the binaries for viewClient Api Test.
    """

    nodes = []
    targetName = "viewClientApiTest"

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is None:
        return nodes

    host = "win64"
    zipSources = []

    for node in vmware.pkg.LookupDeliverableNodes(targetName, host):
        zipSources.append((node.dir.abspath, node.name))

    viewClientApiTestEnv = vmware.LookupEnv(targetName, host)
    viewClientApiTestEnv.LoadTool("libssl")
    deps = (
        [viewClientApiTestEnv["ZLIB_REDIST"][0].abspath]
        + viewClientApiTestEnv["OPENSSL_FIPS_REDIST"]
        + viewClientApiTestEnv["GTEST_REDIST"]
        + viewClientApiTestEnv["LIBPNG_REDIST"]
    )

    for dep in deps:
        # Copy the actual dependency dll or exe.
        f = stageEnv.File(dep)
        zipSources.append((f.dir.abspath, f.name))

    zipFile = File(os.path.join(publishDir, "tests", host, targetName + ".zip"))
    nodes += stageEnv.Zip(zipFile, zipSources)

    return nodes


def stageViewClientGraphicsTest(stageEnv):
    """stageViewClientGraphicsTest
    Stage the binaries for viewClient Graphics Test.
    """

    nodes = []
    targetName = "viewClientGraphicsTest"

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is None:
        return nodes

    host = "win64"
    zipSources = []

    for node in vmware.pkg.LookupDeliverableNodes(targetName, host):
        zipSources.append((node.dir.abspath, node.name))

    viewClientGraphicsTestEnv = vmware.LookupEnv(targetName, host)
    viewClientGraphicsTestEnv.LoadTool("libssl")
    deps = (
        [viewClientGraphicsTestEnv["ZLIB_REDIST"][0].abspath]
        + viewClientGraphicsTestEnv["OPENSSL_FIPS_REDIST"]
        + viewClientGraphicsTestEnv["GTEST_REDIST"]
        + viewClientGraphicsTestEnv["LIBPNG_REDIST"]
    )

    for dep in deps:
        # Copy the actual dependency dll or exe.
        f = stageEnv.File(dep)
        zipSources.append((f.dir.abspath, f.name))

    zipFile = File(os.path.join(publishDir, "tests", host, targetName + ".zip"))
    nodes += stageEnv.Zip(zipFile, zipSources)

    return nodes


def stageAntiKeyloggerTest(stageEnv):
    """stageAntiKeyloggerTest

    Stage the binaries for antiKeylogger Test.
    Please note currently it is done for the Windows platform.
    """

    nodes = []
    targetName = "antiKeyloggerTest"
    vmware.PathPrefixer(vmware.pkg.stagePath) / targetName
    stageEnv.LoadTool("zip-3.0")

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        # Publish antiKeyloggerTest package
        zipSources = []
        zipSources.extend([(installSource64, "libpng16.dll")])

        # Gather the list of all dependencies to copy.
        testEnv = vmware.LookupEnv("antiKeyloggerTest", "win64")
        viewClientEnv = vmware.LookupEnv("viewClient", "win64")
        deps = [
            "SIGC_REDIST",
            "GLIBMM_REDIST",
            "GETTEXT_REDIST",
            "GLIB_REDIST",
            "PCRE2_REDIST",
            "OPENSSL_FIPS_REDIST",
            "GTEST_REDIST",
            "LIBICONV_REDIST",
        ]
        addDepsToZipSources(testEnv, deps, zipSources)
        addDepsToZipSources(viewClientEnv, ["ZLIB_REDIST", "LIBPNG_REDIST"], zipSources)

        # Zip omnissabase and omnissastring
        zipSources += getZipSourcesFromNodeLookup(
            [
                "omnissabaselib",
                "omnissastring",
                "hznprotect",
                "hznpclient",
                "sendInput",
                "antiKeyloggerTest",
            ],
            "win64",
        )

        # Pubish hznprotect driver files
        hznprotectDriverNodes = vmware.LookupNode("hznprotect", "win-kernel")
        for node in Flatten(hznprotectDriverNodes):
            zipSources.append((node.dir.abspath, node.name))

        # publish vmwkbsim driver files
        vmwkbdsimDriverNodes = vmware.LookupNode("vmwkbdsim", "win-kernel")
        for node in Flatten(vmwkbdsimDriverNodes):
            zipSources.append((node.dir.abspath, node.name))

        # Pubish hznprotect-stub driver files
        hznprotectstubDriverNodes = vmware.LookupNode("hznprotect-stub", "win-kernel")
        for node in Flatten(hznprotectstubDriverNodes):
            zipSources.append((node.dir.abspath, node.name))

        zipSources.append(
            (vmware.DirAbsPath("#bora/apps/crtbora/componentTest/appRemoting/"), "data")
        )
        zipSources.append(
            (
                vmware.DirAbsPath(
                    "#bora/apps/crtbora/componentTest/appRemoting/scripts"
                ),
                "antiKeyloggerTest.py",
            )
        )

        # Use antiKeyloggerTest.zip to adapt to the CI deploy script.
        zipFile = File(
            os.path.join(publishDir, "tests", "win64", "antiKeyloggerTest.zip")
        )
        zipNode = stageEnv.Zip(zipFile, zipSources)
        nodes += zipNode

    return nodes


def getRXComponentTestCommonDeps(host):
    commonDepsSources = []

    commonDepsSources += getZipSourcesFromNodeLookup(
        [
            "omnissabaselib",
            "omnissastring",
            "hznprotect",
        ],
        host,
    )

    testEnv = vmware.LookupEnv("libcrtbora", host)
    deps = [
        "SIGC_REDIST",
        "GLIBMM_REDIST",
        "GETTEXT_REDIST",
        "GLIB_REDIST",
        "PCRE2_REDIST",
        "LIBICONV_REDIST",
    ]
    addDepsToZipSources(testEnv, deps, commonDepsSources)
    return commonDepsSources


def getClientSdkRXApiTestCommonDeps():
    clientSdkTestDeps = [
        (installSource64, "rtavCliLib.dll"),
        (installSource64, "crtbora.dll"),
        (installSource64, "glib-2.0-0.dll"),
        (installSource64, "glibmm-2.68-1.dll"),
        (installSource64, "gmodule-2.0-0.dll"),
        (installSource64, "gobject-2.0-0.dll"),
        (installSource64, "gssapiLib.dll"),
        (installSource64, "gthread-2.0-0.dll"),
        (installSource64, "libxml2.dll"),
        (installSource64, "intl-8.dll"),
        (installSource64, "pcre2-8.dll"),
        (installSource64, "sigc-3.0.dll"),
        (installSource64, "omnissastring.dll"),
        (installSource64, "omnissabase.dll"),
        (installSource64, "hznprotect.dll"),
        (installSource64, "hznpclient.dll"),
        (installSource64, "zlib1.dll"),
        (installSource64, "libffi.dll"),
        (installSource64, "libpng16.dll"),
    ]
    return clientSdkTestDeps


def stageCrtboraRXDnDComponentTest(stageEnv):
    """stageCrtboraRXComponentTest

    Stage the test library for crtbora RX Component Test.

    """
    stageEnv.LoadTool("zip-3.0")
    nodes = []
    targetName = ["crtboraRXDnDComponentTest"]

    publishDir = vmware.ReleasePackagesDir()
    deliverableNodesNames = [
        "crtboraRXDnDComponentTest",
        "mksvchanclient",
        "tsdrClient",
        "vdpservice",
    ]
    extraZipSources = []
    extraZipSources.append(
        (
            stageEnv.Dir(
                "#bora/apps/crtbora/componentTest/" "remoteExperience"
            ).abspath,
            "crtboraRXComponentTest_client_win.ini",
        )
    )

    stageEnv.LoadTool(["artifactory"])
    testDataNode = stageEnv.FetchFromArtifactory(
        "horizon-cart-local/remote-experience/dnd/testFiles.zip",
        extract=True,
        extractTargetFiles=["testFiles/DnDTestFile.txt"],
    )
    testDataPath = os.path.dirname(os.path.dirname(testDataNode[0].abspath))
    extraZipSources.append((testDataPath, "."))

    if publishDir is not None:
        extraZipSources.extend(getRXComponentTestCommonDeps("win64"))

        addDepsToZipSources(
            vmware.LookupEnv("omnissabaselib-env", "win64"),
            ["LIBPNG_REDIST"],
            extraZipSources,
        )

        zipFilePath = File(
            os.path.join(
                publishDir, "tests", "win64", "crtboraRXDnDComponentTestClient.zip"
            )
        )
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetName,
            "win64",
            deliverableNodesNames,
            extraZipSources,
            zipFilePath,
            [".map"],
        )
        stageEnv.Depends(zipNode, testDataNode)

        if zipNode is not None:
            nodes += zipNode
    return nodes


def stageHtml5ClientTest(stageEnv):
    """stageHtml5ClientTest

    Stage the test binary for html5Client unit tests.

    """
    nodes = []
    targetName = ["html5mmrClientTest"]
    zipSources = []

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(publishDir, "tests")
        zipSources.extend(getRXComponentTestCommonDeps("win64"))

        msteamsDir = os.path.join(
            vmware.GetGobuildComponent("msteamsapi"), "win64_vc140"
        )
        zipSources.append((os.path.join(msteamsDir, "lib"), "Microsoft.SlimCV.VBM.dll"))

        # Gather the list of all dependencies to copy.
        deps = [
            "GTEST_REDIST",
            "OPENSSL_REDIST",
        ]
        addDepsToZipSources(
            vmware.LookupEnv("html5mmrClientTest-env", "win64"), deps, zipSources
        )

        zipFilePath = os.path.join(publishDir, "win64", "html5mmrClientTest.zip")
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetName,
            "win64",
            targetName,
            zipSources,
            zipFilePath,
            excludedFileFormats=[".map"],
        )
        if zipNode is not None:
            nodes += zipNode

    return nodes


def stageCrtboraRXFAComponentTest(stageEnv):
    """stageCrtboraRXFAComponentTest

    Stage the test library for crtbora RX FA Component Test.

    """
    stageEnv.LoadTool("zip-3.0")
    nodes = []
    targetName = ["crtboraRXFAComponentTest"]

    publishDir = vmware.ReleasePackagesDir()
    deliverableNodesNames = [
        "crtboraRXFAComponentTest",
        "appStub",
        "rxTestApp",
    ]
    extraZipSources = []
    extraZipSources.append(
        (
            stageEnv.Dir("#bora/apps/crtbora/componentTest/" "fileAssociation").abspath,
            "fileAssociation_win.ini",
        )
    )

    testDataSrc = "#bora/apps/crtbora/componentTest/fileAssociation/"
    extraZipSources.append((Dir(testDataSrc).abspath, "res"))

    if publishDir is not None:
        extraZipSources.extend(getRXComponentTestCommonDeps("win64"))

        addDepsToZipSources(
            vmware.LookupEnv("omnissabaselib-env", "win64"),
            ["LIBPNG_REDIST"],
            extraZipSources,
        )

        zipFilePath = File(
            os.path.join(publishDir, "tests", "win64", "crtboraRXFAComponentTest.zip")
        )
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetName,
            "win64",
            deliverableNodesNames,
            extraZipSources,
            zipFilePath,
        )
        if zipNode is not None:
            nodes += zipNode
    return nodes


def stageCrtboraScreenCaptureUnitTest(stageEnv):
    """stageCrtboraScreenCaptureUnitTest

    Stage the test library for crtbora screen capture Unit Test.

    """
    stageEnv.LoadTool("zip-3.0")
    nodes = []
    targetName = ["screenCaptureCrtboraUt"]

    publishDir = vmware.ReleasePackagesDir()
    extraZipSources = []

    if publishDir is not None:
        extraZipSources.extend(getRXComponentTestCommonDeps("win64"))

        zipFilePath = File(
            os.path.join(
                publishDir, "tests", "win64", "screenCaptureCrtboraUnitTest.zip"
            )
        )
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv, targetName, "win64", targetName, extraZipSources, zipFilePath
        )
        if zipNode is not None:
            nodes += zipNode
    return nodes


def stageCrtboraDnDUnitTest(stageEnv):
    """stageCrtboraDnDUnitTest

    Stage the test library for crtbora DnD Unit Test.

    """
    stageEnv.LoadTool("zip-3.0")
    nodes = []
    targetName = ["dndCrtboraUnitTest"]

    publishDir = vmware.ReleasePackagesDir()
    extraZipSources = []

    if publishDir is not None:
        extraZipSources.extend(getRXComponentTestCommonDeps("win64"))
        buildEnv = vmware.LookupEnv("dndCrtboraUnitTest-env", "win64")
        deps = [
            "OPENSSL_REDIST",
            "GTEST_REDIST",
            "LIBPNG_REDIST",
            "ZLIB_REDIST",
        ]
        addDepsToZipSources(buildEnv, deps, extraZipSources)

        zipFilePath = File(
            os.path.join(publishDir, "tests", "win64", "dndCrtboraUnitTest.zip")
        )
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetName,
            "win64",
            targetName,
            extraZipSources,
            zipFilePath,
            excludedFileFormats=[".map"],
        )
        if zipNode is not None:
            nodes += zipNode
    return nodes


def stageClientSdkRxApiUnitTest(stageEnv):
    """stageClientSdkRxApiUnitTest

    Stage the test library for client SDK RX API Unit Test.

    """
    stageEnv.LoadTool("zip-3.0")
    nodes = []
    targetName = ["clientSdkRxApiUT"]
    publishDir = vmware.ReleasePackagesDir()
    extraZipSources = []

    if publishDir is not None:
        deliverableNodesNames = [
            "clientSdkRxApiUT",
            "appStubUnitTest",
        ]
        extraZipSources.extend(getClientSdkRXApiTestCommonDeps())
        # libcurl and openssl lib
        sdkEnv = vmware.LookupEnv("clientSdkRxApiUT-env", "win64")
        deps = [
            "GTEST_REDIST",
            "CURL_REDIST",
            "OPENSSL_REDIST",
            "LIBICONV_REDIST",
        ]
        addDepsToZipSources(sdkEnv, deps, extraZipSources)
        addDepsToZipSources(
            vmware.LookupEnv("omnissabaselib-env", host),
            ["LIBPNG_REDIST", "ZLIB_REDIST"],
            extraZipSources,
        )

        zipFilePath = File(
            os.path.join(publishDir, "tests", "win64", "clientSdkRxApiUT.zip")
        )
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetName,
            "win64",
            deliverableNodesNames,
            extraZipSources,
            zipFilePath,
            excludedFileFormats=[".map"],
        )
        if zipNode is not None:
            nodes += zipNode
    return nodes


def stageRTAVClientComponentTest(stageEnv):
    """Stage RTAV CI test packages and binaries for client side

    stageEnv specifies a stage environment object.
    """
    nodes = []
    targetName = ["rtavTestNodeClient"]

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        testsSrc = "#bora/apps/rde/rtav/tests"
        deploySrc = testsSrc + "/testFramework/deploy/client"
        utilsSrc = testsSrc + "/testFramework/utils"
        rdpUtilsSrc = testsSrc + "/testFramework/rdpUtils"
        testSuiteSrc = testsSrc + "/testFramework/testSuite"
        testCasesSrc = testsSrc + "/testCases"

        publishDir = os.path.join(publishDir, "tests")
        deliverableNodesNames = [
            "pcoip_mfw",
            "rtavCliLib",
            "rtavSetting",
            "rtavTestNodeClient",
            "omnissabaselib",
        ]

        extraZipSources = [
            (Dir(deploySrc).abspath, "."),
            (Dir(rdpUtilsSrc).abspath, "."),
            (Dir(testCasesSrc).abspath, "."),
            (Dir(testSuiteSrc).abspath, "."),
            (Dir(utilsSrc).abspath, "."),
        ]

        testEnv = vmware.LookupEnv("rtavPlugin-env", "win64")
        deps = [
            "FFMPEG_AVCODEC_REDIST",
            "FFMPEG_AVUTIL_REDIST",
            "LIBWEBRTCRTAV_REDIST",
            "OPENSSL_FIPS_REDIST",
            "ZLIB_REDIST",
            "LIBPNG_REDIST",
        ]
        addDepsToZipSources(testEnv, deps, extraZipSources)

        msiNode = ciMsiNodes["RTAV"]
        if msiNode is not None:
            extraZipSources.append((msiNode[0].dir.abspath, msiNode[0].name))

        zipFilePath = os.path.join(publishDir, "win64", "rtavClientComponentTest.zip")
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetName,
            "win64",
            deliverableNodesNames,
            extraZipSources,
            zipFilePath,
        )
        if zipNode is not None:
            nodes += zipNode

    return nodes


def stageRTAVUnitTest(stageEnv):
    """stageRTAVUnitTest

    Stage the test library for RTAV Unit Test.

    """
    stageEnv.LoadTool("zip-3.0")
    nodes = []
    targetName = ["rtavUnitTest"]
    deliverableNodesNames = [
        "pcoip_mfw",
        "rtavUnitTest",
        "omnissabaselib",
    ]
    publishDir = vmware.ReleasePackagesDir()

    extraZipSources = []
    testEnv = vmware.LookupEnv("rtavPlugin-env", "win64")
    deps = [
        "OPENSSL_FIPS_REDIST",
        "ZLIB_REDIST",
        "LIBPNG_REDIST",
    ]
    addDepsToZipSources(testEnv, deps, extraZipSources)

    if publishDir is not None:
        zipFilePath = File(
            os.path.join(publishDir, "tests", "win64", "rtavUnitTest.zip")
        )
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetName,
            "win64",
            deliverableNodesNames,
            extraZipSources,
            zipFilePath,
            excludedFileFormats=[".map"],
        )
        if zipNode is not None:
            nodes += zipNode
    return nodes


def stagePrintRedirComponentTest(stageEnv):
    """Stage print redirection CI test packages and binaries
    stageEnv specifies a stage environment object.
    """
    zipNodes = []
    nodes = []
    targetName = "printRedirTest.zip"
    host = "win64"
    printCIDir = os.path.join(
        vmware.DirAbsPath(vmware.BuildRoot()), vmware.BuildType(), "printRedirTest"
    )
    scriptsSrc = "#bora/apps/printRedir/componentTest"
    msiNode = ciMsiNodes["PrintRedir"]
    if msiNode is not None:
        zipNodes += stageEnv.LinkCopy(
            os.path.join(printCIDir, msiNode[0].name), msiNode[0]
        )
    stageEnv.Depends(zipNodes, msiNode)
    zipNodes += vmware.LookupNode("prTestPrint", host)
    zipNodes += vmware.LookupNode("omnissabaselib", host)
    zipNodes += vmware.DirCopy(
        vmware.EnumerateSourceDir(scriptsSrc),
        Dir(scriptsSrc),
        Dir(printCIDir),
        stageEnv,
    )
    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(publishDir, "tests", host)
        zipSources = []
        buildEnv = vmware.LookupEnv("prclient-env", host)
        zipNodes += File(buildEnv["OPENSSL_FIPS_REDIST"])
        zipNodes += buildEnv["ZLIB_REDIST"]
        zipNodes += buildEnv["LIBPNG_REDIST"]
        for node in zipNodes:
            zipSources.append((node.dir.abspath, node.name))
        nodes += stageEnv.Zip(File(os.path.join(publishDir, targetName)), zipSources)
    return nodes


def stagePrintRedirUnitTest(stageEnv):
    """stagePrintRedirUnitTest
    Stage the binaries for printer redirection UT.
    """

    nodes = []
    targetName = ["printredirut"]

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(publishDir, "tests")
        deliverableNodesNames = ["printredirut"]
        host = "win64"
        extraZipSources = []

        buildEnv = vmware.LookupEnv("printHelper-env", host)
        deps = [
            buildEnv["REDISTRIBUTE_PDFIUM_DLL"],
        ]
        for lib in deps:
            extraZipSources.append(
                (os.path.dirname(lib.abspath), os.path.basename(lib.abspath))
            )
        buildEnv = vmware.LookupEnv("prclient-env", host)
        deps = buildEnv["ZLIB_REDIST"] + buildEnv["LIBPNG_REDIST"]
        for lib in deps:
            extraZipSources.append(
                (os.path.dirname(lib.abspath), os.path.basename(lib.abspath))
            )

        zipFilePath = os.path.join(publishDir, host, "printredirut.zip")
        nodes += vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetName,
            host,
            deliverableNodesNames,
            extraZipSources,
            zipFilePath,
            excludedFileFormats=[".map"],
        )
    return nodes


def stageFido2ClientComponentTest(stageEnv):
    """stageFido2ClientComponentTest
    Stage the binaries for FIDO2 Component Test.
    """
    nodes = []
    targetName = ["fido2ComponentTest"]
    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(publishDir, "tests")
        deliverableNodesNames = [
            "fido2ComponentTest",
            "fido2Client",
            "vdpservice",
        ]
        zipSources = []
        for filename in [
            "fido2_client_win.ini",
            "fido2Action.py",
            "fido2TcpClient.py",
            "fido2TcpServer.py",
            "fido2Utils.py",
        ]:
            zipSources.append(
                (
                    stageEnv.Dir(
                        "#bora/apps/rde/fido2/tests/componentTest/deploy"
                    ).abspath,
                    filename,
                )
            )

        zipFilePath = File(
            os.path.join(publishDir, "win64", "fido2ComponentTestClient.zip")
        )
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetName,
            "win64",
            deliverableNodesNames,
            zipSources,
            zipFilePath,
            excludedFileFormats=[".map"],
        )
        if zipNode is not None:
            nodes += zipNode
    return nodes


def stageClientFIDO2UnitTest(stageEnv):
    """stageClientFIDO2UnitTest

    Stage the unit test library for FIDO2 client components.

    """
    nodes = []
    targetName = ["fido2ClientUnitTest"]
    publishDir = vmware.ReleasePackagesDir()
    extraZipSources = []

    if publishDir is not None:
        zipFilePath = File(
            os.path.join(publishDir, "tests", "win64", "fido2ClientUnitTest.zip")
        )
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetName,
            "win64",
            targetName,
            extraZipSources,
            zipFilePath,
            excludedFileFormats=[".map"],
        )
        if zipNode is not None:
            nodes += zipNode
    return nodes


def stageTlmRdpvcbridgeUnitTest(stageEnv):
    """TlmRdpvcbridgeUnitTest

    Stage the binaries for tlm rdpvcbridge plugin Unit Test.
    """

    nodes = []
    targetName = ["tlm_rdpvcbridgeUT"]

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(vmware.DirAbsPath(publishDir), "tests")
        deliverableNodesNames = ["tlm_rdpvcbridgeUT"]

        extraZipSources = []
        zipFilePath = os.path.join(publishDir, "win64", "tlmRdpvcbridgeUnitTest.zip")
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetName,
            "win64",
            deliverableNodesNames,
            extraZipSources,
            zipFilePath,
            excludedFileFormats=[".map"],
        )
        if zipNode is not None:
            nodes += zipNode

    return nodes


def stageTlmRdpvcbridgeComponentTest(stageEnv):
    """TlmRdpvcbridgeComponentTest

    Stage the binaries for tlm rdpvcbridge plugin Component Test.
    """
    nodes = []
    targetName = ["tlmPluginCmpTestHost", "etlmapi"]

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(vmware.DirAbsPath(publishDir), "tests")
        deliverableNodesNames = ["tlm_rdpvcbridge", "tlmPluginCmpTestHost", "etlmapi"]

        extraZipSources = []
        extraZipSources.append(
            (
                stageEnv.Dir(
                    "#bora/apps/rde/tlm_rdpvcbridge/tests/componentTest/"
                    "deployscripts"
                ).abspath,
                "tlmplugin_client_win.ini",
            )
        )
        testDataSrc = "#bora/apps/rde/tlm_rdpvcbridge/tests/componentTest/"
        extraZipSources.append((Dir(testDataSrc).abspath, "testData"))

        zipFilePath = os.path.join(publishDir, "win64", "tlmPluginCompTest.zip")
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetName,
            "win64",
            deliverableNodesNames,
            extraZipSources,
            zipFilePath,
            excludedFileFormats=[".map"],
        )
        if zipNode is not None:
            nodes += zipNode

    return nodes


def stageURLComponentTestClient(stageEnv):
    """URLComponentTest

    Stage the binaries for URL Redirection Component Test.

    """

    nodes = []
    targetName = ["urlComponentTest"]

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(vmware.DirAbsPath(publishDir), "tests")
        deliverableNodesNames = [
            "urlComponentTest",
            "urlProtocolLaunchHelper",
            "urlProtocolIntercept",
            "browserPluginIE",
            "rxTestApp",
            "rdeCTServer",
        ]

        extraZipSources = []
        prefix = "win"
        for file in ["url_client_%s.ini" % prefix]:
            extraZipSources.append(
                (
                    stageEnv.Dir(
                        "#bora/apps/rde/urlRedirection/tests/" "componenttest/"
                    ).abspath,
                    file,
                )
            )
        # Publish urlComponentTest package
        # Gather the list of all dependencies to copy.
        zipFilePath = os.path.join(publishDir, "win64", "urlComponentTest.zip")
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetName,
            "win64",
            deliverableNodesNames,
            extraZipSources,
            zipFilePath,
        )
        if zipNode is not None:
            nodes += zipNode

    return nodes


def stageUNCUnitTest(stageEnv):
    """UNCUnitTest

    Stage the binaries for UNC Path Redirection Unit Test.

    """

    nodes = []
    targetName = ["uncSvcUnitTest", "uncNpUnitTest"]

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(vmware.DirAbsPath(publishDir), "tests")
        deliverableNodesNames = ["uncSvcUnitTest", "uncNpUnitTest"]

        extraZipSources = []
        zipFilePath = os.path.join(publishDir, "win64", "uncUnitTest.zip")
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetName,
            "win64",
            deliverableNodesNames,
            extraZipSources,
            zipFilePath,
            excludedFileFormats=[".map"],
        )
        if zipNode is not None:
            nodes += zipNode

    return nodes


def stageURLUnitTest(stageEnv):
    """URLUnitTest

    Stage the binaries for URL Redirection Unit Test.

    """

    nodes = []
    targetName = ["urlUnitTest"]

    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(vmware.DirAbsPath(publishDir), "tests")
        extraZipSources = []
        zipFilePath = os.path.join(publishDir, "win64", "urlUnitTest.zip")
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetName,
            "win64",
            targetName,
            extraZipSources,
            zipFilePath,
            excludedFileFormats=[".map"],
        )
        if zipNode is not None:
            nodes += zipNode

    return nodes


def stageUSBClientComponentTest(stageEnv):
    """stageUSBClientComponentTest

    Stage the binaries for usb Component Test.
    """

    nodes = []
    targetName = "usbTest"
    vmware.PathPrefixer(vmware.pkg.stagePath) / targetName

    deploySrc = "#bora/apps/viewusb/framework/usb/test/componenttest/deploy"
    testDataSrc = "#bora/apps/viewusb/framework/usb/test/componenttest/plugin/testData"
    publishDir = vmware.ReleasePackagesDir()
    zipSources = []

    if publishDir is not None:
        publishDir = os.path.join(publishDir, "tests")
        deliverableNodesNames = [
            "horizon-usbd-openssl",
            "usbRedirectionClient",
            "vdpservice",
            "usbArbitrator-podbin",
            "usbArbLib-podbin",
            "usbEnum-podbin",
            "usbEnumArb-podbin",
            "usbDeviceProperties-podbin",
            "usbStringPod-podbin",
        ]
        testNodes = vmware.pkg.LookupDeliverableNodes(targetName, "win64")
        for node in testNodes:
            if node.name.startswith(
                (
                    "usbComponentTest",
                    "libusbComponentTest",
                    "usbEngineTest",
                    "usbUdeTest",
                    "usbMmfwClientTest",
                    "usbMmfwServerTest",
                )
            ):
                zipSources.append((node.dir.abspath, node.name))

        usbarbMsiNodes = buildUsbarbMsi(stageEnv, usbarbMsmNodes)
        for n in usbarbMsiNodes:
            zipSources.append((n.dir.abspath, n.name))

        zipSources.append((stageEnv.Dir(deploySrc).abspath, "usb_run_cases.py"))
        zipSources.append((stageEnv.Dir(testDataSrc).abspath, "memdebug_config.json"))
        zipSources.append((stageEnv.Dir(deploySrc).abspath, "usb_client_win.ini"))
        zipSources.append((stageEnv.Dir(testDataSrc).abspath, "deviceUtils.ps1"))
        zipSources.append((stageEnv.Dir(testDataSrc).abspath, "regUtils.ps1"))
        # add usbarb components
        rdtNodes = vmware.pkg.LookupDeliverableNodes("remoteDeviceTool", "win64")
        rdtEnv = rdtNodes[0].get_env()
        for n in rdtNodes:
            zipSources.append((n.dir.abspath, n.name))
        for f in rdtEnv["OPENSSL_REDIST"]:
            name = os.path.basename(f)
            if not name.endswith(".dll"):
                continue
            zipSources.append((os.path.dirname(f), name))
            zipSources.append((os.path.dirname(f), re.sub("\\.dll$", ".pdb", name)))
        zipSources.append(
            ("#bora/tests/remoteDevice/remoteDeviceTool", "remoteDeviceTool.py")
        )
        # add ci drivers
        drivers = {
            "vmwude": "Win10",
            "vmwusbt": "Win10",
        }
        for driver, osFolder in drivers.items():
            if vmware.BuildType() == "obj":
                driverBuildType = "Debug"
            else:
                driverBuildType = "Release"
            driverBuildDir = os.path.join(
                vmware.DirAbsPath(vmware.BuildRoot()),
                vmware.Product(),
                driver,
                osFolder + driverBuildType,
                "x64",
                "bin",
            )
            for fileType in [
                ".inf",
                ".cat",
                ".pdb",
                ".sys",
            ]:
                file = driver + fileType
                zipSources.append((driverBuildDir, file))
        # add libxml library
        testEnv = vmware.LookupEnv("usbComponentTest-env", "win64")
        libxml = testEnv["REDISTRIBUTE_LIBXML2_DLL"].abspath
        zipSources.append(
            (os.path.dirname(os.path.abspath(libxml)), os.path.basename(libxml))
        )
        addDepsToZipSources(testEnv, ["LIBICONV_REDIST"], zipSources)

        # Use usbComponentTestClient.zip to adapt to the CI deploy script.
        zipFile = File(os.path.join(publishDir, "win64", "usbComponentTestClient.zip"))
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            [],
            "win64",
            deliverableNodesNames,
            zipSources,
            zipFile,
            excludedFileFormats=[".map"],
        )
        nodes += zipNode

    return nodes


def stageClientUSBUnitTest(stageEnv):
    """stageClientUSBUnitTest

    Stage the unit test library for USB client components.

    """
    nodes = []
    targetName = "usbTest"
    publishDir = vmware.ReleasePackagesDir()
    if publishDir is not None:
        publishDir = os.path.join(publishDir, "tests")
        deploySrc = "#bora/apps/viewusb/framework/usb/test/unitTestSet"
        deploySrc = stageEnv.Dir(deploySrc).abspath
        zipSources = [
            (deploySrc, "usb_ut_deploy.py"),
            (deploySrc, "usb_ut_run_all_cases.py"),
        ]
        zipSources.append((deploySrc, "usb_ut_win.ini"))

        testNodes = vmware.pkg.LookupDeliverableNodes(targetName, "win64")
        testLibs = [
            "usbMmfwUnitTest",
            "usbViewUsbLibUnitTest",
            "usbStringStoreUnitTest",
            "usbUrbTrxUnitTest",
            "usbDevConfigUnitTest",
            "usbDevFilterUnitTest",
            "usbUsbdLoaderUnitTest",
            "usbUsbdUnitTest",
            "usbRedirectionClientUnitTest",
        ]

        for node in testNodes:
            if node.name.startswith(tuple(testLibs)) and not node.name.endswith(
                (".lib", ".map")
            ):
                zipSources.append((node.dir.abspath, node.name))

        # other dependencies
        usbTestEnv = vmware.LookupEnv("usbComponentTest-env", "win64")
        for redist in usbTestEnv["OPENSSL_REDIST"]:
            comp = os.path.split(redist)
            zipSources.append((comp[0], comp[1]))

        usbTestEnv.LoadTool(
            [
                "libsigc",
                "libglibmm",
                "gettext",
                "libglib2",
                "libpcre2",
            ]
        )
        deps = [
            "SIGC_REDIST",
            "GLIBMM_REDIST",
            "GETTEXT_REDIST",
            "REDISTRIBUTE_LIBXML2_DLL",
            "GLIB_REDIST",
            "PCRE2_REDIST",
            "LIBICONV_REDIST",
        ]
        addDepsToZipSources(usbTestEnv, deps, zipSources)
        zipFile = File(os.path.join(publishDir, "win64", "usbUnitTestClient.zip"))
        zipNode = stageEnv.Zip(zipFile, zipSources)
        nodes += zipNode

    return nodes


def stageViewClient(stageEnv):
    """
    Stage the binaries for horizon-protocol.
    """
    stagePP = vmware.PathPrefixer(vmware.pkg.stagePath)
    viewClientPP = stagePP / "viewClient"
    nodes = []
    targetName = ["viewClient"]
    publishDir = vmware.ReleasePackagesDir()

    rmksEnv = vmware.LookupEnv("viewClient", "win64")
    rmksEnv.LoadTool("libxml")
    deps = [
        rmksEnv["FFMPEG_AVCODEC_REDIST"],
        rmksEnv["FFMPEG_AVUTIL_REDIST"],
        rmksEnv["FFMPEG_AVFORMAT_REDIST"],
        rmksEnv["ZLIB_REDIST"][0].abspath,
        rmksEnv["LIBPNG_REDIST"][0].abspath,
        rmksEnv["REDISTRIBUTE_LIBXML2_DLL"].abspath,
        rmksEnv["LIBX264_REDIST"],
    ] + rmksEnv["OPENSSL_FIPS_REDIST"]

    for dep in deps:
        nodes += stageEnv.LinkCopy(
            File(viewClientPP / "x64" / os.path.basename(dep)), os.path.abspath(dep)
        )
        pdb = os.path.abspath(os.path.splitext(dep)[0] + ".pdb")
        if os.path.exists(pdb):
            nodes += stageEnv.LinkCopy(
                File(viewClientPP / "x64" / os.path.basename(pdb)), pdb
            )

    for dep in ["viewClient", "hznprotect", "omnissabaselib"]:
        for node in vmware.pkg.LookupDeliverableNodes(dep, "win64"):
            nodes += stageEnv.LinkCopy(File(viewClientPP / "x64" / node.name), node)

    if publishDir is not None:
        extraZipSources = []
        deliverableNodesNames = ["viewClient"]

        for dep in deps:
            depDir = os.path.dirname(os.path.abspath(dep))
            extraZipSources.append((depDir, os.path.basename(dep)))
            pdb = os.path.splitext(dep)[0] + ".pdb"
            if os.path.exists(pdb):
                extraZipSources.append((depDir, os.path.basename(pdb)))

        extraZipSources += getZipSourcesFromNodeLookup(
            ["hznprotect", "omnissabaselib"], "win64"
        )

        zipFileName = "viewClient-%s-%s.zip" % (buildNum, "win64")
        zipFilePath = File(os.path.join(publishDir, "viewClient", zipFileName))
        zipNode = vmware.pkg.CreateZipStageNode(
            stageEnv,
            targetName,
            "win64",
            deliverableNodesNames,
            extraZipSources,
            zipFilePath,
            [".map"],
        )
        if zipNode is not None:
            nodes += zipNode
            stageEnv.NoCacheIfBuildNumberInfoOverridden(zipNode)

    return nodes


def generateClientSdkDocs(stageEnv, docOutputDir):
    """
    Generate the client SDK docs via doxygen.
    """
    target = "clientSdkDoc"
    projectName = '"Horizon Client SDK for Windows"'
    projectNumber = vmware.ProductVersionNumber()
    outputPath = docOutputDir.abspath
    doxyPreDef = "protected=private \\ internal=private"

    doxyConf = File(
        "#horizonclient/view/openClient/sdk/docs/windows/doxygen/" "config.txt"
    ).abspath
    doxyInput = [
        Dir("#horizonclient/view/openClient/sdk/docs/windows/doxygen").abspath,
    ] + [
        File("#horizonclient/view/openClient/sdk/semi-public/dotnet/" + f).abspath
        for f in [
            "HorizonAuthenticationParameters.h",
            "HorizonClient.h",
            "HorizonEvent.h",
            "HorizonFolderRedirectionSetting.h",
            "HorizonInsecureConnection.h",
            "HorizonLogger.h",
            "HorizonParameters.h",
            "HorizonRemoteHost.h",
            "HorizonShortcuts.h",
            "HorizonTypes.h",
            "IHorizonAudioOutDeviceInfo.h",
            "IHorizonAuthentication.h",
            "IHorizonCertificateError.h",
            "IHorizonClient.h",
            "IHorizonEntitlement.h",
            "IHorizonLogger.h",
            "IHorizonNetworkQualityState.h",
            "IHorizonPrinterInfo.h",
            "IHorizonRemoteContext.h",
            "IHorizonRtavDeviceInfo.h",
            "IHorizonRunningApplication.h",
            "IHorizonServer.h",
            "IHorizonSession.h",
            "IHorizonStorageDrive.h",
            "IHorizonUsbDevice.h",
        ]
    ]

    buildDir = os.path.join(vmware.BuildRoot(), "build", target)
    doxyVerFile = File(os.path.join(buildDir, "Doxyfile.ver")).abspath

    stageEnv.LoadTool(["doxygen"])

    doxyConfigSubstArgs = [
        ("@@HEADERPATH@@", " ".join(doxyInput)),
        ("@@DOXYOUT@@", outputPath),
        ("@@PROJ_NUMBER@@", projectNumber),
        ("@@PROJ_NAME@@", projectName),
        ("@@doxyPreDef@@", doxyPreDef),
        # ('@@DOT_PATH@@', stageEnv['DOXYGEN_DOTPATH']),
    ]

    doxyConf = stageEnv.TextSubst(
        doxyVerFile, doxyConf, TEXTSUBSTMODE=0o0664, TEXTSUBSTARGS=doxyConfigSubstArgs
    )

    stageEnv.NoCache(doxyVerFile)

    def filefilter(root, path):
        # This is the list of extensions that doxygen scans for.
        # Only the .h files would be scanned.
        exts = [
            ".h",
        ]
        return os.path.splitext(path)[1] in exts

    depDocFiles = []
    for item in doxyInput:
        if os.path.isdir(item):
            depDocFiles.append(vmware.EnumerateSourceDir(item, filefilter=filefilter))
        elif os.path.isfile(item):
            tempFiles = []
            tempFiles.append(item)
            depDocFiles.append(tempFiles)

    doxygenAction = vmware.ActionWithDisplay(
        "$DOXYGEN ${SOURCES[0].abspath}", "Generating Windows SDK doc"
    )
    doxygenNode = stageEnv.Command(
        outputPath, [doxyConf, depDocFiles], [doxygenAction, "$RM -f %s" % doxyVerFile]
    )

    # We can't predict the output yet so we can't cache it.
    stageEnv.NoCache(outputPath)

    return doxygenNode


def publishClientSdkDocs(stageEnv, docOutputDir, publishDir):
    """
    Publish the documents as a zip.
    """
    publishPP = vmware.PathPrefixer(publishDir)
    publishNodes = []
    docOutputFileName = "VMware.Horizon.Client.Windows.SDK.Doc.%s.zip" % (
        clientSdkBundleVer
    )
    docOutputZip = File(publishPP / docOutputFileName)
    stageEnv.ZipDir(docOutputZip, docOutputDir, ".")
    publishNodes = [docOutputZip]
    stageEnv.NoCache(docOutputZip)

    return publishNodes


def buildClientSdkSample(stageEnv):
    """
    Build the client SDK sample program.
    """
    sampleNodes = []
    srcPP = vmware.PathPrefixer("#horizonclient/view/openClient/sdk/sample/windows")
    outputDir = os.path.join(
        vmware.DirAbsPath(vmware.BuildRoot()), "build", "Horizon.Client.SDK.Sample"
    )
    configuration = "Debug" if vmware.BuildType() == "obj" else "Release"

    restoreNode = stageEnv.DotnetCli(
        "restore",
        [Dir(outputDir).File("Nuget.Config")],
        [srcPP.File("Horizon.Client.SDK.Sample.sln")],
        "--configfile",
        File("#horizonclient/view/winClient/Nuget.Config").abspath,
        "/p:BuildRoot=%s" % outputDir,
        "/p:Configuration=%s" % configuration,
    )

    sampleNodes += restoreNode

    buildNode = stageEnv.DotnetCli(
        "build",
        [Dir(outputDir).File("Horizon.Client.SDK.Sample.exe")],
        [srcPP.File("Horizon.Client.SDK.Sample.sln")],
        "--no-restore",
        "-c",
        configuration,
        "-o",
        outputDir,
        "/p:BuildRoot=%s" % outputDir,
        "/p:Configuration=%s" % configuration,
    )

    sampleNodes += buildNode
    stageEnv.Depends(buildNode, restoreNode)

    return sampleNodes


def copyClientSdkSampleSrc(stageEnv, publishDir):
    """
    Copy the source code of the client SDK sample program to
    the folder publishDir/sample/windows.
    When building the sample, it will copy and unzip the SDK nuget package
    into the subfolder ./samplePackage. As a result, we need filter out the
    source code files and copy them to the above folder.
    """

    sampleDestPath = os.path.join(publishDir, "sample/windows")
    srcPP = vmware.PathPrefixer("#horizonclient/view/openClient/sdk/sample/windows")

    srcDirs = [
        "Horizon.Client.SDK.Sample",
    ]
    srcFiles = [
        "Directory.Build.props",
        "Directory.Build.targets",
        "Horizon.Client.SDK.Sample.sln",
        "NuGet.Config",
        "SDK Sample Guide.txt",
    ]

    nodes = []
    # Copy all the files from those directories in srcDirs.
    for srcDir in srcDirs:
        nodes.append(
            vmware.utils.StageCopyDirectory(
                os.path.join(stageEnv.Dir(srcPP).abspath, srcDir),
                os.path.join(stageEnv.Dir(sampleDestPath).abspath, srcDir),
                stageEnv.LinkCopy,
            )
        )

    # Copy all the files in srcFiles.
    for srcFile in srcFiles:
        nodes.append(
            stageEnv.LinkCopy(
                stageEnv.File(os.path.join(sampleDestPath, srcFile)),
                os.path.join(stageEnv.Dir(srcPP).abspath, srcFile),
            )
        )

    return nodes


def stageLibcdkI18nFiles(folderName, moFileName):
    """
    Stage the LibCDK I18n files.
    folderName is the folder to hold those generted .mo files.
    moFileName is the generated .mo file name.
    The structure is: folderName/locale/LC_MESSAGES/moFileName.mo.
    """
    i18nFileNodes = []
    for locale in ["de", "es", "fr", "ja", "ko", "zh_CN", "zh_TW"]:
        srcFile = vmware.FileAbsPath("#horizonclient/view/openClient/po/%s.po" % locale)
        destFile = os.path.join(
            installSource32, folderName, locale, "LC_MESSAGES", "%s.mo" % moFileName
        )
        action = vmware.ActionWithDisplay(
            "$MSGFMT -o $TARGET $SOURCE", "Generating $TARGET"
        )
        i18nFileNodes += stageEnv.Command([File(destFile)], [File(srcFile)], action)

    return i18nFileNodes


def installSourceDir(host):
    """
    Get the install source directory by host.
    """
    installSourceMap = {
        "win32": installSource32,
        "win64": installSource64,
        "win-arm64": installSourceArm64,
        "win-arm64ec": installSourceArm64ec,
        "noarch": installSourceNoarch,
    }
    return installSourceMap[host]


def getDeps(host):
    """
    Get the dependencies by host.
    The host supports win64 and win-arm64ec.
    For the deps that don't have ec mode, will use x64 files instead.
    """
    clientSdkEnv = {"win64": clientSdkEnvX64, "win-arm64ec": clientSdkEnvArm64Ec}[host]

    deps = (
        [
            clientSdkEnv["REDISTRIBUTE_LIBXML2_DLL"],
        ]
        + clientSdkEnv["LIBICONV_REDIST"]
        + clientSdkEnv["CURL_REDIST"]
        + clientSdkEnv["GLIBMM_REDIST"]
        + clientSdkEnv["GETTEXT_REDIST"]
        + clientSdkEnv["SIGC_REDIST"]
        + clientSdkEnv["REDIST"]
        + clientSdkEnv["GLIB_REDIST"]
        + clientSdkEnv["OPENSSL_FIPS_REDIST"]
    )

    # Some components only have win64 binaries.
    crtboraX64Env = vmware.LookupEnv("libcrtbora", "win64")
    viewClientX64Env = vmware.LookupEnv("viewClient", "win64")
    printHelperEnv = vmware.LookupEnv("printHelper-env", "win64")
    rtavPluginEnv = vmware.LookupEnv("rtavPlugin-env", "win64")
    deps += (
        [
            viewClientX64Env["LIBX264_REDIST"],
            printHelperEnv["REDISTRIBUTE_PDFIUM_DLL"],
            rtavPluginEnv["LIBWEBRTCRTAV_REDIST"],
            os.path.join(pcoipSoftClientsRoot, "win64", "client", "pthreadVC2.dll"),
            os.path.join(pcoipSoftClientsRoot, "win64", "client", "libidn.dll"),
            viewClientX64Env["FFMPEG_AVCODEC_REDIST"],
            viewClientX64Env["FFMPEG_AVUTIL_REDIST"],
            viewClientX64Env["FFMPEG_AVFORMAT_REDIST"],
        ]
        + crtboraX64Env["PCRE2_REDIST"]
        + viewClientX64Env["ZLIB_REDIST"]
        + viewClientX64Env["LIBPNG_REDIST"]
        + GetVCRuntimeFiles("x64", vmware.BuildType())
    )

    # Test deps. Don't need on win-arm64ec host.
    if host == "win64":
        utNodes = vmware.pkg.LookupDeliverableNodes("horizonrxut", "win64")
        utBinaryPath = utNodes[0].abspath
        utEnv = vmware.LookupEnv("horizonrxut-env", host)
        deps += (
            [
                utBinaryPath,
            ]
            + utEnv["MSVC_RELEASE_CRT_REDIST"]
            + utEnv["REDIST"]
        )

    return deps


def buildRdeMsms(stageEnv, copyNodeInfo):
    """
    Builds and publishes all merge modules that are required by the consumers.
    """

    # Only x64 is required
    host = "win64"
    arch = "x64"

    # Maps from installer name to a tuple of:
    #   - Array of keys in "copyNodeInfo" that installer should depend upon.
    #   - Mode of the msm
    msmInfoMap = {
        "Html5MMR": ([], "Client"),
        "Mksvchan": (["mksvchanclient"], "Client"),
        "PrintRedir": (["prclient", "printHelper", "prvdpplugin"], "Client"),
        "RTAV": (["rtavCliLib", "rtavPlugin"], "Client"),
        "tlmvcbridge": (["tlm_rdpvcbridge"], "Common"),
        "TSDR": (["tsdrClient"], "Client"),
        "UsbRedirection": (
            [
                "usbRedirectionClient",
                "horizon-usbdloader",
                "horizon-usbd-openssl",
            ],
            "Client",
        ),
    }

    nodes = []

    # Build some dependent wixobj files that all merge modules will depend
    # upon. Currently this is only Directories.
    wixEnv = stageEnv.Clone()
    wixEnv.Append(
        WIXDEFINES={
            "MSIProductVersion": vmware.ProductVersionNumber(),
            "OBJDIR": vmware.BuildType(),
            "ARCH": arch,
            "WIXROOT": vmware.DirAbsPath("#bora/install/msi/msm"),
        },
        WIXCANDLEFLAGS=["-arch", arch, "-sw1086", "-wx"],
    )
    wixObjPath = os.path.join(
        buildTypeRoot, "apps", "rde-installers", "Directories-%s.wixobj" % arch
    )
    directoryWixObjNode = wixEnv.Wixobj(
        File(wixObjPath),
        "#bora/install/msi/msm/rdeIncludes/Directories.wxs",
    )

    # Now build each individual msm.
    for msm in msmInfoMap:
        msmTuple = msmInfoMap[msm]
        # Step 1: set up some initial variables, including the
        # parameters that we will send into candle.
        msmBuildDir = Dir(buildTypeRoot).Dir("apps").Dir(msm).Dir(arch)
        wixEnv = stageEnv.Clone()
        if msm == "Html5MMR":
            if vmware.BuildType() == "release":
                sourceDir = os.path.join(installSourceDir(host), "html5mmr", host)
            else:
                continue
        else:
            sourceDir = installSourceRoot
        mode = msmTuple[1]
        wixEnv.Append(
            WIXDEFINES={
                "MSIProductVersion": vmware.ProductVersionNumber(),
                "ARCH": arch,
                "WIXROOT": vmware.DirAbsPath("#bora/install/msi/msm"),
                "OBJDIR": vmware.BuildType(),
                "%s_BUILDDIR" % msm: sourceDir,
                "GOBUILD_WINDOWS_INSTALLKIT_ROOT": windowsInstallkitRoot,
                "MSM_TYPE": mode,
                "SRCROOT": vmware.DirAbsPath("#bora"),
            },
            WIXCANDLEFLAGS=[
                "-arch",
                arch,
                "-ext",
                "WixFirewallExtension",
                "-ext",
                "WixUIExtension",
                "-sw1086",
                "-sw1006",
                "-wx",
            ],
            WIXLIGHTFLAGS=[
                "-ext",
                "WixUIExtension",
                "-sw1072",
                "-wx",
                "-sice:ICE03",
                "-sice:ICE80",
                "-sice:ICE30",
                "-sice:ICE69",
            ],
            WIXLIGHTLOC=[
                "-cultures:en-us",
            ],
        )

        # Add any msm-specific definitions here.
        if msm == "RTAV":
            rtavPluginEnv = vmware.LookupEnv("rtavPlugin-env", host)
            x264Lib = os.path.basename(rtavPluginEnv["LIBX264_REDIST"])
            webrtcRTAVLib = os.path.basename(rtavPluginEnv["LIBWEBRTCRTAV_REDIST"])

            wixEnv.Append(
                WIXDEFINES={
                    "X264_LIB": x264Lib,
                    "WEBRTCRTAV_LIB": webrtcRTAVLib,
                }
            )

        # Stage all required nodes
        depNodes = []
        for dep in msmTuple[0]:
            depNodes += copyNodeInfo[(dep, host)]

        # USB Redirection requires some files in a different place
        # TODO: Change the wix definition so lines below can be removed
        if msm == "UsbRedirection":
            names = ["horizon-usbdloader", "horizon-usbd-openssl"]
            for name in names:
                for host in ["win64", "win-arm64ec"]:
                    for node in copyNodeInfo[(name, host)]:
                        if node.name not in [
                            "horiozn-usbdloader.exe",
                            "horizon-usbd-openssl.dll",
                        ]:
                            continue
                        depNodes += stageEnv.LinkCopy(
                            Dir(sourceDir)
                            .Dir("viewusb")
                            .Dir("build")
                            .Dir(exportsHosts[host])
                            .File(node.name),
                            node,
                        )

        wixFragmentSrc = "#bora/install/msi/msm/%s/%s.wxs" % (msm, mode)
        wixSrc = "#bora/install/msi/msm/%s/%s.wxs" % (msm, msm)

        # Step 2: build the msm-specific "fragment" wixobj.
        wixFragmentObjNode = wixEnv.Wixobj(
            msmBuildDir.File("%s-%sFragment-%s.wixobj" % (msm, mode, arch)),
            wixFragmentSrc,
        )
        Depends(wixFragmentObjNode, depNodes)

        # Step 3: build the msm-specific normal wixobj.
        wixObjNode = wixEnv.Wixobj(
            msmBuildDir.File("%s-%s-%s.wixobj" % (msm, mode, arch)), wixSrc
        )
        Depends(wixObjNode, depNodes)

        # Step 4: link the msm together using the "Directory" wixobj,
        # the msm-specific "fragment" wixobj, and the msm-specific
        # normal wixobj.
        wixName = "Horizon_%s_%s" % (msm, arch)
        if msm in ["Html5MMR"]:
            # Build msi
            wixMsmFile = msmBuildDir.File(wixName + ".msi")
            wixMsmNode = wixEnv.Wixmsi(
                wixMsmFile, [wixFragmentObjNode, wixObjNode, directoryWixObjNode]
            )
            wixEnv.SignFile(wixMsmNode)
        else:
            # Build msm
            wixMsmFile = msmBuildDir.File(wixName + ".msm")
            wixMsmNode = wixEnv.Wixmsm(
                wixMsmFile, [wixFragmentObjNode, wixObjNode, directoryWixObjNode]
            )
            rdeMsmNodes.append(wixMsmFile)

        nodes += wixMsmNode

        # build msi files used for CI test
        # consumers are written to expect 1.0.0 in the file name
        if msm in ["PrintRedir", "RTAV"]:
            msiName = "Horizon-%s-%s-1.0.0-%s" % (msm, mode, arch)
            tempMsmFile = msmBuildDir.File(msiName + ".msm")
            copyMsiNode = wixEnv.LinkCopy(tempMsmFile, wixMsmFile)
            ciMsiNodes[msm] = buildRdeMsi(
                wixEnv,
                copyMsiNode,
                msiName,
                msmBuildDir,
                msm,
                mode,
                arch,
                directoryWixObjNode,
            )
    return nodes


def buildRdeMsi(
    wixEnv,
    wixMsmNode,
    wixMsmName,
    wixBuildDir,
    moduleName,
    fragmentName,
    arch,
    wixObjDirNode,
):
    """Build the .msi nodes for module.

    wixEnv specifies the wix environment object.
    wixMsmNode specifies the .msm node generated for module.
    wixMsmName specifies the .msm file name(without .msm postfix).
    wixBuildDir specifies the intermediate build directory.
    moduleName specifies the module name.
    fragmentName specifies if it is Client/Agent fragment.
    arch specifies x86/x64.
    wixObjDirNode specifies the wixobj directory node.
    """
    nodes = []
    # This is necessary as the .msi generation depends on the .msm.
    vmware.RegisterNode(wixMsmNode, wixMsmName + ".msm")
    # Step 1: prepare the wix build env.
    nodeWixEnv = wixEnv.Clone()
    nodeWixEnv.Append(
        WIXDEFINES={
            "wixBuildDir": Dir(wixBuildDir).abspath,
            "fragmentName": fragmentName,
        },
        WIXLIGHTFLAGS=[
            "-sice:ICE61",  # This warning is generated if max version
            # is not defined. We dont want to define an
            # arbitary max version
            "-sice:ICE82",  # This warning is generated when Sequence
            # number is same for all actions.
            # We can safely ignore the warning.
        ],
    )

    # Step 2: build the msi-specific "fragment" wixobj.
    wixFragmentSrc = "#bora/install/msi/%s/components.wxs" % moduleName
    wixSrc = "#bora/install/msi/%s/%s.wxs" % (moduleName, moduleName)

    wixFragmentObjNode = nodeWixEnv.Wixobj(
        wixBuildDir.File(
            "%s-%sFragment-%s-msi.wixobj" % (moduleName, fragmentName, arch)
        ),
        wixFragmentSrc,
    )

    # Step 3: let the .msi fragment obj build depend on the .msm node.
    Depends(wixFragmentObjNode, wixMsmNode)

    # Step 4: build the msi-specific normal wixobj.
    wixObjNode = nodeWixEnv.Wixobj(
        wixBuildDir.File("%s-%s-%s-msi.wixobj" % (moduleName, fragmentName, arch)),
        wixSrc,
    )

    # Step 5: let the .msi obj build depend on the .msm node.
    Depends(wixObjNode, wixMsmNode)

    # Step 6: create the .msi node.
    wixMsiFile = wixBuildDir.File(wixMsmName + ".msi")
    wixMsiNode = nodeWixEnv.Wixmsi(
        wixMsiFile, [wixFragmentObjNode, wixObjNode, wixObjDirNode]
    )

    nodeWixEnv.SignFile(wixMsiNode)

    nodes += wixMsiNode

    return nodes


def stageUsbDriver(env, stageDir, cdfPath, drv, winVer, arch):
    arbStageNodes = []

    minimumWhqlOS = "6.1"
    updateDrvRes = vmware.LookupNode("updateDrvRes", host="win32")
    templateDriverVer = vmware.LookupNode("driverVer", host="win32")

    env.LoadTool("artifactory")
    extractTargetFiles = []
    for ext in [".cat", ".inf", ".sys", ".pdb"]:
        extractTargetFiles += ["{}Release/{}/bin/{}{}".format(winVer, arch, drv, ext)]
    folderId = "%s-%s" % (winVer, arch)

    registerNodeName = vmware.pkg.frozenFilesDetails[drv]["file"] + folderId
    frozenFilesExtractNode = env.FetchFromArtifactory(
        artifactorySignedFileRoot + vmware.pkg.frozenFilesDetails[drv]["file"],
        sha256=vmware.pkg.frozenFilesDetails[drv]["sha256"],
        extract=True,
        extractTargetFiles=extractTargetFiles,
        extractFolderSuffix=folderId,
        registerNodeName=registerNodeName,
    )
    frozenPath = os.path.dirname(frozenFilesExtractNode[0].abspath)

    dstDir = stageDir.Dir("Drivers").Dir(drv).Dir(winVer).Dir(arch)

    if vmware.BuildType() == "release" or vmware.BuildType() == "beta":
        useSigned = True if drv in vmware.pkg.frozenFilesDetails else False
    else:
        useSigned = False

    if useSigned:
        signedByMS = 1
        for ext in [".cat", ".inf", ".sys", ".pdb"]:
            f = drv + ext
            arbStageNodes += env.LinkCopy(dstDir.File(f), os.path.join(frozenPath, f))
            env.Depends(arbStageNodes, frozenFilesExtractNode)
    else:
        signedByMS = 0

        catInput = []

        # .sys and pdb file
        for n in vmware.LookupNode(drv + winVer + arch, host="win-kernel"):
            if n.abspath.endswith(drv + ".sys"):
                staged = env.FileCopy(dstDir.File(n.name), n)
                env.SignFile(staged)
                catInput += staged
            if n.abspath.endswith(drv + ".pdb"):
                staged = env.FileCopy(dstDir.File(n.name), n)

        # .cdf file
        cdfFile = drv + ".cdf"
        cdfSrc = File("#bora/modules/%s/%s" % (cdfPath, cdfFile))
        staged = env.LinkCopy(dstDir.File(cdfFile), cdfSrc)
        # env.SignFile(staged)
        catInput += staged

        # .inf file
        if drv == "hznusb":
            infSrc = "#bora/modules/hznusb/hznusb.inf.in"
            substArgs = {
                "Win10x64": [
                    (
                        "@@Win10 x64@@",
                        "%S_DeviceDesc1%=_Install1, USB\Vid_0e0f&Pid_ec01",
                    ),
                    ("@@Win10 arm64@@", "; not supported"),
                ],
                "Win10arm64": [
                    ("@@Win10 x64@@", "; not supported"),
                    (
                        "@@Win10 arm64@@",
                        "%S_DeviceDesc1%=_Install1, USB\Vid_0e0f&Pid_ec01",
                    ),
                ],
            }
        else:
            infSrc = "#bora/modules/hznucmon/hznucmon.inf.in"
            substArgs = {
                "Win10x64": [
                    ("@@Install@@", "[DefaultInstall.NT]"),
                    (
                        "@@Uninstall@@",
                        "[DefaultUninstall.NT]\r\n" "DelFiles = _Hznucmon.Files",
                    ),
                    ("@@Install Services@@", "[DefaultInstall.NT.Services]"),
                    (
                        "@@Uninstall Services@@",
                        "[DefaultUninstall.NT.Services]\r\n"
                        "DelService = %S_DriverName%,0x204"
                        "  ; SPSVCINST_DELETEEVENTLOGENTRY | SPSVCINST_STOPSERVICE",
                    ),
                    ("@@Service Binary@@", "ServiceBinary = %12%\%S_DriverName%.sys"),
                    ("@@Dest Dir@@", "DefaultDestDir = 12 ; DRIVER directory"),
                ],
                "Win10arm64": [
                    ("@@Install@@", "[DefaultInstall.NTarm64]"),
                    ("@@Uninstall@@", ""),
                    ("@@Install Services@@", "[DefaultInstall.NTarm64.Services]"),
                    ("@@Uninstall Services@@", ""),
                    ("@@Service Binary@@", "ServiceBinary = %13%\%S_DriverName%.sys"),
                    ("@@Dest Dir@@", "DefaultDestDir = 13 ; Driver Store Dir"),
                ],
            }
        catInput += env.TextSubst(
            dstDir.File(drv + ".inf"), infSrc, TEXTSUBSTARGS=substArgs[winVer + arch]
        )

        # .cat file
        makecat = '"' + env["MAKECAT"].abspath + '" -v -r'
        staged = env.Command(
            dstDir.File(drv + ".cat"),
            catInput,
            "$CD %s && %s %s" % (dstDir.abspath, makecat, cdfFile),
        )
        env.SignFile(staged)
        env.NoCache(staged)
        arbStageNodes += staged + catInput

    # ver.dll file
    driverInf = dstDir.File(drv + ".inf")
    driverVer = env.Command(
        dstDir.File(drv + "ver.dll"),
        [driverInf] + templateDriverVer + updateDrvRes,
        [
            Copy("$TARGET", str(templateDriverVer[0])),
            updateDrvRes[0].abspath
            + " "
            + minimumWhqlOS
            + " "
            + driverInf.abspath
            + " ${TARGET.abspath}",
        ],
    )
    env.SignFile(driverVer)

    for n in arbStageNodes:
        if not n.abspath.endswith(".cat"):
            continue
        env.Depends(n, driverInf)
        env.Depends(n, dstDir.File(drv + ".sys"))
        action = "%s -i %s -m %d -p 2:%s" % (
            env.subst("$CHECKDRIVER"),
            driverInf,
            signedByMS,
            {
                "Win10": "10.0",
            }[winVer],
        )
        env.AddPostAction(n, action)

    arbStageNodes += driverVer

    return arbStageNodes


def buildUsbarbMsm(stageEnv):
    nodes = []
    inputNodes = []
    msmEnv = stageEnv.Clone()

    msmEnv.LoadTool(
        [
            "digitalsign",
            "wix",
            "checkdriver",
        ]
    )

    sourceDir = Dir(buildTypeRoot).Dir("usbarb")

    # Stage binaries
    for host in ["win32", "win64"]:
        for name in ["usbArbitrator", "vnetlib-dll", "vnetlib-exe"]:
            src = vmware.LookupNode(name, host=host)[0]
            dst = sourceDir.Dir("32" if host == "win32" else "64").File(src.name)
            staged = msmEnv.FileCopy(dst, src)
            msmEnv.SignFile(staged)
            inputNodes += staged

    # Stage Drivers
    for winVer, archs in {
        "Win10": ["x64", "arm64"],
    }.items():
        for arch in archs:
            for cdfPath, drv in {
                "hznucmon": "hznucmon",
                "hznusb": "hznusb",
            }.items():
                inputNodes += stageUsbDriver(
                    msmEnv, sourceDir, cdfPath, drv, winVer, arch
                )

    """
    msmUSBDevicesInstUtil.dll always goes in the '32' directory even if it's
    for the 64 bit MSM.
    """
    instUtil = vmware.LookupNode("usbDevicesInstUtil", host="win64")[0]
    instUtilStaged = msmEnv.LinkCopy(sourceDir.Dir("32").File(instUtil.name), instUtil)
    msmEnv.SignFile(instUtilStaged)
    inputNodes += instUtilStaged

    ModuleId = "HorizonUSBDevices64"
    IsWin64 = "yes"
    WixObjFile = sourceDir.File("HorizonUSBDevices64.wixobj")
    WixMsmFile = sourceDir.File("HorizonUSBDevices64.msm")
    WixArch = "x64"

    WixSrcFile = "#bora/install/msi/msm/msmUSBDevices/HorizonUSBDevices.wxs"

    difxApiRedistParent = os.path.join(
        vmware.GetGobuildComponent("cayman_windows_toolset_win_redists_final"),
        "win/Program Files/Windows Kits/10/Redist/DIFx/DIFxAPI",
    )
    difxapiRedistX64 = os.path.join(difxApiRedistParent, "x64/")
    difxapiRedistX64Node = Dir(difxapiRedistX64)

    wixObj = msmEnv.Candle(
        target=WixObjFile,
        source=[WixSrcFile, difxapiRedistX64Node],
        options=" ".join(
            [
                "-arch",
                WixArch,
                "-v",
                "-dSOURCE2=" + vmware.DirAbsPath("%s/build" % vmware.BuildRoot()),
                "-dSOURCE=" + sourceDir.abspath,
                '-dDifxApiRedistDirX64="' + difxapiRedistX64 + '"',
                "-dDESKTOP_COMMONPATH="
                + vmware.DirAbsPath("#bora/install/desktop/windows/common"),
                "-dModuleId=" + ModuleId,
                "-dIsWin64=" + IsWin64,
            ]
        ),
    )

    msmEnv.Depends(wixObj, inputNodes)
    WixMsm = msmEnv.Light(
        target=WixMsmFile,
        source=wixObj,
        options=" ".join(
            [
                "-cultures:en-us",
                "-spdb",
                "-v",
            ]
        ),
    )
    cscript = os.path.join(os.environ.get("SystemRoot"), "system32", "cscript.exe")
    update64 = File("#bora/support/install/Update64bit.vbs")
    msmEnv.AddPostAction(WixMsm, "%s /nologo %s $TARGET" % (cscript, update64))
    usbarbMsmNodes["win64"] = WixMsm
    nodes += WixMsm

    return nodes


def buildUsbarbMsi(stageEnv, msmNodes):
    """Build the .msi for USB CI."""
    stageNodes = []
    stageDir = Dir(buildTypeRoot).Dir("usbarb").Dir("installer")

    # Get Version
    ver = vmware.ProductVersionNumber()

    # Stage msm
    for n in msmNodes["win64"]:
        stageNodes += stageEnv.LinkCopy(stageDir.Dir("64").File(n.name), n)

    # Build msi
    msiXmlNode = File("#bora/scons/package/usbarb/USBInstaller.wxs")
    msiWixObjNode = stageEnv.Candle(
        target=stageDir.File("USBInstaller.wixobj"),
        source=msiXmlNode,
        options="-dUSBVersion=%s" % (ver + "." + vmware.ProductBuildNumber()),
    )
    msiNode = stageEnv.Light(
        target=stageDir.File("USBInstaller.msi"),
        source=msiWixObjNode,
        options="-b %s" % stageDir.abspath,
    )
    stageEnv.Depends(msiNode, stageNodes)
    stageEnv.SignFile(msiNode)

    return msiNode


allNodes = []
stageEnv = vmware.pkg.stageEnv
stagePath = vmware.pkg.stagePath

# These variables are all related to the additional staging of pdb files.
# All x64 pdbs, dlls, and exes should be copied into pdbDir64.
# All arm64ec pdbs, dlls, and exes should be copied into pdbDirArm64ec.
# These copy nodes should be placed into
# pdbBundleSourceNodes (***not allNodes***) so that they are only copied if
# RELEASE_PACKAGES_DIR was provided and as a result, the zip code at the bottom
# of this file adds a dependency upon pdbBundleSourceNodes. If
# RELEASE_PACKAGES_DIR was not provided, pdbBundleSourceNodes will not be
# referenced and as a result, these copy nodes will not be executed.
pdbBundleSourceNodes = []
pdbDir = os.path.join(installSourceRoot, "pdb")
pdbDirName86 = "x86"
pdbDirName64 = "x64"
pdbDir86 = os.path.join(pdbDir, pdbDirName86)
pdbDir64 = os.path.join(pdbDir, pdbDirName64)
pdbDirNameArm64ec = "arm64ec"
pdbDirNameArm64 = "arm64"
pdbDirArm64ec = os.path.join(pdbDir, pdbDirNameArm64ec)
pdbDirArm64 = os.path.join(pdbDir, pdbDirNameArm64)

hostToPdbDirMap = {
    "win32": pdbDir86,
    "win64": pdbDir64,
    "win-arm64": pdbDirArm64,
    "win-arm64ec": pdbDirArm64ec,
}

# We don't stage anything to gobuild component exports right now, so we must
# create an empty directory for builds to succeed.
try:
    os.makedirs(vmware.DirAbsPath(stagePath))
except os.error:
    # This is usually thrown if the directory already exists.
    pass

stageEnv.LoadTool(
    [
        "linkcopy",
        "digitalsign",
        "dotnet",
        "wix",
        "zip-2.32",
        "zip-builder",
        "msbuild",
        "extract-macro",
        "horizonUtils",
    ]
)

nodeMap = {
    "clientSdkC": ["win64", "win-arm64ec"],
    "clientSdkCPrimitive": ["win64", "win-arm64ec"],
    "clientSdkNet": ["win64"],
    "controllerAPI": ["win32", "win64", "win-arm64ec"],
    "controllerAPIApiTest": ["win32", "win64", "win-arm64ec"],
    "controllerAPITest": ["win32", "win64", "win-arm64ec"],
    "remoteClientManager": ["win32", "win64", "win-arm64ec"],
    "gssapiLib": ["win64", "win-arm64ec"],
    "libcdkUnitTest": ["win64"],
    "clientSdkUnitTest": ["win64"],
    "libcrtbora": ["win64"],
    "sdr_hcSvcPlugin": ["win64", "win-arm64ec"],
    "ui_hcSvcPlugin": ["win64", "win-arm64ec"],
    "hznprotect_hcSvcPlugin": ["win64", "win-arm64ec"],
    "ui_hcSvcPluginUnitTest": ["win64"],
    "hznprotect": ["win64"],
    "hznpclient": ["win64"],
    "viewClient": ["win64"],
    "viewClientStub": ["win64"],
    "omnissabaselib": ["win64", "win-arm64ec"],
    "omnissastring": ["win64"],
    "winClient": ["win64"],
    "libinject": ["win64"],
    "InjectionDll": ["win64"],
    "antiKeyloggerTest": ["win64"],
    "vmUpdateLauncher": ["win64", "win-arm64ec"],
    "libcds": ["win64", "win-arm64ec"],
    "MessageFrameWork": ["win64", "win-arm64ec"],
    "whfbRedirection": ["win32", "win64", "win-arm64ec"],
    "ws_diag": ["win32", "win64"],
    "ws_dllhost": ["win32", "win64"],
    "scredir_vchanclient": ["win64"],
    "wsauth": ["win32", "win64", "win-arm64"],
    "ws_winauthDLL": ["win32", "win64"],
    "blastHcSvcPlugin": ["win64", "win-arm64ec"],
    "blastHcSvcWrapIPC": ["win64", "win-arm64ec"],
    "appStub": ["win64", "win-arm64ec"],
    "browserPluginIE": ["win32", "win64", "win-arm64ec"],
    "fido2Client": ["win64", "win-arm64ec"],
    "html5mmrClient": ["win64"],
    "html5mmrVideoPlayer": ["win64"],
    "hznClientCtrlVCPlugin": ["win64"],
    "mksvchanclient": ["win64", "win-arm64ec"],
    "hznMigrUtils": ["win64", "win-arm64ec"],
    "pcoip_mfw": ["win64"],
    "prclient": ["win64", "win-arm64ec"],
    "prClientUtil": ["win64", "win-arm64ec"],
    "prCommonWrapper": ["win64", "win-arm64ec"],
    "printHelper": ["win64", "win-arm64ec"],
    "printPreview": ["win64"],
    "prvdpplugin": ["win64", "win-arm64ec"],
    "rdeClient": ["win64", "win-arm64ec"],
    "rdpvcbridge": ["win64"],
    "rtavCliLib": ["win64", "win-arm64ec"],
    "rtavPlugin": ["win64", "win-arm64ec"],
    "scannerRedirClient": ["win64", "win-arm64ec"],
    "scannerRedirClientApp": ["win32", "win64", "win-arm64ec"],
    "sdrclient": ["win64", "win-arm64ec"],
    "sdrclientworker": ["win64", "win-arm64ec"],
    "teamsHelper": ["win64"],
    "tlm_rdpvcbridge": ["win64"],
    "tsdrClient": ["win64", "win-arm64ec"],
    "tsmmrClient": ["win64", "win-arm64ec"],
    "udpProxyLib": ["win64"],
    "uncRedirection": ["win32", "win64", "win-arm64ec"],
    "uncService": ["win32", "win64", "win-arm64ec"],
    "urlNativeMessageHost": ["win32", "win64", "win-arm64ec"],
    "urlProtocolLaunchHelper": ["win32", "win64", "win-arm64ec"],
    "urlProtocolIntercept": ["win32", "win32", "win64", "win-arm64ec"],
    "usbRedirectionClient": ["win64", "win-arm64ec"],
    "vdpservice": ["win64"],
    "horizon-usbdloader": ["win64", "win-arm64ec"],
    "horizon-usbd-openssl": ["win64", "win-arm64ec"],
    "usbArbitrator": ["win64"],
}

teraLfsSignedNodeInfo = {
    "uncRedirection": [
        "USE_SIGNED_UNCRD_PLUGIN",
        "%s/horizon/%s/Win8.1Release/%s/bin",
    ],
}

vmsoftSignedNodeInfo = {
    "urlProtocolIntercept": ["USE_SIGNED_URLFILTER_PLUGIN", "vmwurlfilter"],
    "uncRedirection": ["USE_SIGNED_UNCRD_PLUGIN", "vmwuncrd"],
}

copyFileMap = {
    "horizon-url-native-host-manifest.json": [
        File(
            "#bora/apps/rde/urlRedirection/urlNativeMessageHost/"
            "horizon-url-native-host-manifest.json"
        ),
        ["noarch"],
    ],
}

# List of signed files in Artifactory that are needed by hcwin installer
signedArtifactory = {
    "wsauth",
}

stageNodesFrozen = []

# Regiester Tera-LFS signed files
for nodeName, info in teraLfsSignedNodeInfo.items():
    frozenFlag = info[0]
    pathFmt = info[1]
    if vmware.LocalOpts.GetBool(frozenFlag, False):
        stageNodesFrozen.append(nodeName)
        for host in nodeMap[nodeName]:
            path = pathFmt % (teraLfsRoot, nodeName, exportsHosts[host])
            vmware.RegisterNode(
                vmware.EnumerateSourceDir(Dir(path)),
                "%s-frozen" % nodeName,
                host=host,
                space="build",
            )


# Register bora-vmsoft signed files
for nodeName, info in vmsoftSignedNodeInfo.items():
    frozenFlag = info[0]
    name = info[1]
    if vmware.LocalOpts.GetBool(frozenFlag, False):
        stageNodesFrozen.append(nodeName)
        for host in nodeMap[nodeName]:
            path = (
                "#bora-vmsoft/install/Windows/Frozen_Files/%s/"
                "Win8.1Release/%s/bin" % (name, exportsHosts[host])
            )
            vmware.RegisterNode(
                vmware.EnumerateSourceDir(
                    Dir(path), filefilter=lambda root, f: not f.endswith(".txt")
                ),
                "%s-frozen" % nodeName,
                host=host,
                space="build",
            )


# Register Artifactory signed files
for nodeName in signedArtifactory:
    if nodeName in vmware.pkg.frozenFilesDetails:
        stageNodesFrozen.append(nodeName)
        for host in nodeMap[nodeName]:
            stageEnv.LoadTool("artifactory")
            frozenFilesNode = stageEnv.FetchFromArtifactory(
                artifactorySignedFileRoot
                + vmware.pkg.frozenFilesDetails[nodeName]["file"],
                sha256=vmware.pkg.frozenFilesDetails[nodeName]["sha256"],
                extract=True,
                extractTargetFiles=["{}/{}.dll".format(exportsHosts[host], nodeName)],
                extractFolderSuffix=host,
                registerNodeName=vmware.pkg.frozenFilesDetails[nodeName]["file"] + host,
            )

            # Register the node with frozen suffix so that the lookup
            # will give the files from frozen directory
            regNode = vmware.RegisterNode(
                frozenFilesNode, "%s-frozen" % nodeName, host=host, space="build"
            )
            stageEnv.Depends(regNode, frozenFilesNode)


def StageDeliverables(stageEnv, name, host, stagePath, packaged=False, frozen=False):
    """
    Wrapper for vmware.pkg.StageDeliverables
    For frozen nodes, installs files to stagePath
    """
    stagedNodes = []
    if name in stageNodesFrozen:
        frozenNodes = vmware.pkg.LookupDeliverableNodes("%s-frozen" % name, host)
        # Stage frozen files to the stagePath
        stagedNodes = stageEnv.Install(stagePath, frozenNodes)
    else:
        # Stage nodes to the given stagePath
        stagedNodes = vmware.pkg.StageDeliverables(
            stageEnv, name, host, stagePath, packaged=packaged
        )

    return stagedNodes


copyNodeInfo = {}

for nodeName in nodeMap:
    for host in nodeMap[nodeName]:
        if nodeName == "html5mmrVideoPlayer" and vmware.BuildType() != "release":
            continue

        copyNodes = StageDeliverables(stageEnv, nodeName, host, installSourceDir(host))
        allNodes += copyNodes

        # Remember this info for later use in writing staging targets.
        copyNodeInfo[(nodeName, host)] = copyNodes

        # Pick the appropriate PDB staging dir.
        pdbDirPath = hostToPdbDirMap[host]

        for copyNode in copyNodes:
            copyNodeName = os.path.basename(copyNode.abspath)
            pdbBundleSourceNodes += stageEnv.LinkCopy(
                os.path.join(pdbDirPath, copyNodeName), copyNode
            )

if not CODEQL_SCAN:
    # Copy the winClient extra files to the buildtype/product source directory,
    # including the client language files, the dependent lib files from ModernWpf.
    winClientCopyNodes = []
    winClientEnv = vmware.LookupEnv("winClient-env", "win64")
    winClientRootDir = winClientEnv["WINCLIENT_ROOT"]
    winClientLangNodes = vmware.LookupNode("winClientLang", "win64")
    for node in winClientLangNodes:
        relpath = os.path.relpath(node.abspath, winClientRootDir.abspath)
        winClientCopyNodes += stageEnv.FileCopy(
            os.path.join(installSourceDir("win64"), relpath), node
        )

    stageEnv.SignFile(winClientCopyNodes)

    # Must use ijwhost.dll that is in .NET Core SDK.
    winClientCopyNodes += stageEnv.FileCopy(
        os.path.join(installSourceDir("win64"), "ijwhost.dll"),
        vmware.LookupEnv("clientSdkNetPrimitive-env", "win64")[
            "NETCOREAPPLIBPATH"
        ].File("ijwhost.dll"),
    )

    # Make all winClient dependencies together.
    winClientCopyNodes += copyNodeInfo[("winClient", "win64")]

    # Copy normal files
    for name, info in copyFileMap.items():
        for host in info[1]:
            winClientCopyNodes += stageEnv.FileCopy(
                Dir(installSourceDir(host)).File(name), info[0]
            )

    allNodes += winClientCopyNodes

# One exception is that we need to wait to stage controllerAPI until
# controllerAPITest is all done. See bug 2075728 for more details.
for host in nodeMap["controllerAPI"]:
    stageEnv.Depends(
        copyNodeInfo[("controllerAPI", host)], copyNodeInfo[("controllerAPITest", host)]
    )

testE2EStageNodes = []
# Copy all images for the modernized client.
imageSourceRootDir = Dir(
    "#horizonclient/view/winClient/" "VMware.Horizon.Client.UI/Assets"
).abspath
imageSources = [
    "application.ico",
    "desktop.ico",
    "server.ico",
    "windows365.ico",
]
for host in nodeMap["winClient"]:
    assetsDir = os.path.join(installSourceDir(host), "Assets")
    imageSourceNodes = []
    for imageSource in imageSources:
        imageSourceFullPath = os.path.join(imageSourceRootDir, imageSource)
        imageSourceNodes += stageEnv.LinkCopy(
            os.path.join(assetsDir, imageSource), imageSourceFullPath
        )
    allNodes += imageSourceNodes

scriptFiles = [
    File("#horizonclient/view/winClient/InstallLocalBuildSupport.psm1"),
    File("#horizonclient/view/winClient/InstallLocalBuildSupport.ps1"),
    File("#horizonclient/view/winClient/InstallLocalDebugBuildSupport.ps1"),
    File("#horizonclient/view/winClient/InstallLocalBuildSupport.bat"),
]

nodes = vmware.DirCopy(
    scriptFiles,
    Dir("#horizonclient/view/winClient/"),
    Dir(installSourceDir("win64")),
    stageEnv,
)
stageEnv.SignFile(nodes)
allNodes += nodes

dctClientScriptFiles = [
    File("#horizonclient/view/openClient/dct/win32/vdm-debug.ps1"),
    File("#horizonclient/view/openClient/dct/win32/vdm-help.ps1"),
    File("#horizonclient/view/openClient/dct/win32/vdm-product-common.ps1"),
    File("#horizonclient/view/openClient/dct/win32/vdm-query.ps1"),
    File("#horizonclient/view/openClient/dct/win32/vdm-support-common.ps1"),
    File("#horizonclient/view/openClient/dct/win32/vdm-support.ps1"),
    File("#horizonclient/view/openClient/dct/win32/elevate.ps1"),
    File("#horizonclient/view/openClient/dct/win32/vdm-legacy-support.ps1"),
]

nodes = vmware.DirCopy(
    dctClientScriptFiles,
    Dir("#horizonclient/view/openClient/dct/win32/"),
    Dir(installSourceDir("win64")),
    stageEnv,
)

dctCommonScriptFiles = [
    File("#bora/apps/horizonAgent/dct/vdm-common.ps1"),
    File("#bora/apps/horizonAgent/dct/vdm-loglevel-common.ps1"),
]

nodes += vmware.DirCopy(
    dctCommonScriptFiles,
    Dir("#bora/apps/horizonAgent/dct/"),
    Dir(installSourceDir("win64")),
    stageEnv,
)

dctpluginscripts = [
    File("#bora/apps/horizonAgent/dct/plugins/plugin-manager.ps1"),
    File("#bora/apps/horizonAgent/dct/plugins/config-parser.ps1"),
]

nodes += vmware.DirCopy(
    dctpluginscripts,
    Dir("#bora/apps/horizonAgent/dct/plugins/"),
    Dir(installSourceDir("win64")),
    stageEnv,
)

stageEnv.SignFile(nodes)
allNodes += nodes


if vmware.BuildType() == "release":
    html5mmrVideoPlayerStagePath = os.path.join(
        installSourceDir("win64"), "html5mmr", "win64"
    )
    html5mmrVideoPlayerEnv = vmware.LookupEnv("html5mmrVideoPlayer-env", host)
    # CEF is on an older compiler profile for now.
    conanCEF = vmware.GetConanComponent(env, "cef", forceCompiler="msvc17.9.6")
    if not conanCEF:
        raise vmware.ScriptError("cef is not in conan")
    cefFolder = os.path.join(conanCEF.package_folder, "cef")
    cefRuntimeDir = os.path.join(
        cefFolder, "win64_%s" % (html5mmrVideoPlayerEnv["VC_SUFFIX"]), "runtime"
    )
    nodes = vmware.DirCopy(
        vmware.EnumerateSourceDir(cefRuntimeDir),
        Dir(cefRuntimeDir),
        Dir(html5mmrVideoPlayerStagePath),
        stageEnv,
    )
    allNodes += nodes
    copyHtml5MmrVideoPlayer = stageEnv.FileCopy(
        os.path.join(html5mmrVideoPlayerStagePath, "HTML5VideoPlayer.exe"),
        os.path.join(installSourceDir("win64"), "HTML5VideoPlayer.exe"),
    )
    allNodes += copyHtml5MmrVideoPlayer

# Set up the list of binaries that we need to copy for the installer.
# Also include some dependencies so we stage their pdbs.
x64FipsDeps = clientSdkEnvX64["OPENSSL_FIPS_REDIST"]

wsauthDLLx64Path = os.path.join(installSourceDir("win64"), "wsauth.dll")

# Copy all binaries we need. The tuples we iterate over include:
#   1. The dependencies that we want to copy (including a pdb if one exists).
#   2. The destination directory to copy it to.
#   3. An additional directory to copy it to for pdb/dll/exe staging as part of
#      the PDB bundle
#   4. The testE2E directory to copy it to (optional).
dependencyCopyNodes = []
for infoTuple in [
    (getDeps("win64"), installSource64, pdbDir64, testE2EBinaryDir64),
    (
        x64FipsDeps,
        os.path.join(installSource64, "bin-fips"),
        os.path.join(pdbDir64, "bin-fips"),
        None,
    ),
    (getDeps("win-arm64ec"), installSourceArm64ec, pdbDirArm64ec, None),
    (
        x64FipsDeps,
        os.path.join(installSourceArm64ec, "bin-fips"),
        os.path.join(pdbDirArm64ec, "bin-fips"),
        None,
    ),
    ([wsauthDLLx64Path], pdbDirArm64ec, None, testE2EBinaryDir64),
]:
    deps = infoTuple[0]
    destDir = infoTuple[1]
    pdbDestDir = infoTuple[2]
    testE2EDestDir = infoTuple[3]
    for dep in deps:
        # Copy the binary and/or pdb.
        depFile = stageEnv.File(dep)

        destPath = os.path.join(destDir, depFile.name)
        copyNodes = copyBinaryAndPdb(stageEnv, destPath, depFile.abspath)
        dependencyCopyNodes += copyNodes

        # Also copy it to the pdb bundle directory and optionally to the
        # testE2E dir.
        for node in copyNodes:
            if pdbDestDir:
                pdbBundleSourceNodes += stageEnv.LinkCopy(
                    os.path.join(pdbDestDir, node.name), node.abspath
                )
            if testE2EDestDir:
                testE2EStageNodes += stageEnv.LinkCopy(
                    os.path.join(testE2EDestDir, node.name), node.abspath
                )

crtboraTestNode = vmware.LookupNode("crtboraTest", host="win64")
stageEnv.Depends(testE2EStageNodes, crtboraTestNode)
testE2EStageNodes += stageEnv.LinkCopy(
    os.path.join(testE2EBinaryDir64, "crtboraTest.dll"),
    os.path.join(
        vmware.LibraryDirectory("crtboraTest", host="win64"), "crtboraTest.dll"
    ),
)

testE2EStageNodes += stageEnv.LinkCopy(
    os.path.join(testE2EBinaryDir64, "crtbora.dll"),
    os.path.join(vmware.LibraryDirectory("crtbora", host="win64"), "crtbora.dll"),
)

testE2EStageNodes += stageEnv.LinkCopy(
    os.path.join(testE2EBinaryDir64, "horizon-protocol.exe"),
    os.path.join(
        vmware.BuildRoot(),
        "build",
        "horizon-protocol",
        vmware.BuildType(),
        "win64",
        "horizon-protocol.exe",
    ),
)

testE2EStageNodes += stageEnv.LinkCopy(
    os.path.join(testE2EBinaryDir64, "horizon-protocol.exe"),
    os.path.join(
        vmware.BuildRoot(),
        "build",
        "horizon-protocol",
        vmware.BuildType(),
        "win64",
        "horizon-protocol.exe",
    ),
)

# Copy this file using a different destination name.
dependencyCopyNodes += copyBinaryAndPdb(
    stageEnv,
    os.path.join(installSource64, "pcoip_client_win32.dll"),
    os.path.join(pcoipSoftClientsRoot, "win64", "client", "pcoip_client_win64.dll"),
)

dependencyCopyNodes += copyBinaryAndPdb(
    stageEnv,
    os.path.join(installSourceArm64ec, "pcoip_client_win32.dll"),
    os.path.join(pcoipSoftClientsRoot, "win64", "client", "pcoip_client_win64.dll"),
)

# Stage print preview language files
for node in vmware.LookupNode("printPreviewLang", host="win64"):
    src = node.abspath
    dst = os.path.join(
        installSource64, os.path.basename(os.path.dirname(src)), os.path.basename(src)
    )
    dependencyCopyNodes += copyBinaryAndPdb(stageEnv, dst, src)
if not CODEQL_SCAN:
    allNodes += dependencyCopyNodes

# Stage some dependency nodes in the testE2E binary dir.
for nodeName, host in [
    ("controllerAPI", "win32"),
    ("controllerAPIApiTest", "win32"),
    ("controllerAPITest", "win32"),
    ("remoteClientManager", "win32"),
    ("gssapiLib", "win64"),
]:
    # Avoid signing and staging the dlls/exes twice by operating on the
    # previous stage destination paths. This also allows us to avoid running
    # into bug 2075728 because it respects any delays in staging imposed
    # earlier on in this file.
    for node in copyNodeInfo[(nodeName, host)]:
        testE2EStageNodes += stageEnv.LinkCopy(
            os.path.join(testE2EBinaryDir32, node.name), node
        )

# Miscellaneous autotest dependencies.
autotestDeps = (
    [
        clientSdkEnvX64["REDISTRIBUTE_LIBXML2_DLL"].abspath,
        os.path.join(
            vmware.DirAbsPath("#horizonclient/view/openClient/win32"),
            "autoTest/installerTest/historyVersionsForUpgradeTest.json",
        ),
    ]
    + clientSdkEnvX64["LIBICONV_REDIST"]
    + clientSdkEnvX64["CURL_REDIST"]
    + clientSdkEnvX64["ZLIB_REDIST"]
    + clientSdkEnvX64["OPENSSL_FIPS_REDIST"]
    + vmware.LookupEnv("viewClientApiTest", "win64")["GTEST_REDIST"]
)

autotestStageNodes = []
for srcFile in autotestDeps:
    file = stageEnv.File(srcFile)
    autotestStageNodes += stageEnv.LinkCopy(
        os.path.join(autotestBaseDir, file.name), file
    )
if not CODEQL_SCAN:
    allNodes += autotestStageNodes

# Stage all autotest files into the autotest directory.
autotestNames = ["ciInstallerTest", "ciVirtualDevicesTest"]
for nodeName in autotestNames:
    testNodes = vmware.pkg.StageDeliverables(
        stageEnv, nodeName, "win64", autotestBaseDir
    )
    copyNodeInfo[(nodeName, "win64")] = testNodes
    if not CODEQL_SCAN:
        allNodes += testNodes

# Set up some targets for developers using node copy objects that we generated
# early in this file.
for nodeName, host in copyNodeInfo:
    copyNodes = copyNodeInfo[(nodeName, host)]

    if nodeName in autotestNames:
        copyNodes += autotestStageNodes
    else:
        copyNodes += dependencyCopyNodes

    if nodeName in ["controllerAPIApiTest", "controllerAPITest", "remoteClientManager"]:
        copyNodes += copyNodeInfo[("controllerAPI", host)]
    elif nodeName == "wincdk":
        copyNodes += copyNodeInfo[("gssapiLib", host)]
        copyNodes += imageSourceNodes

    vmware.Alias("%s-%s-stage" % (nodeName, host), copyNodes)

# Register the devenv alias used for Visual Studio integration.
allNodes += SConscript(
    "#bora/scons/package/hccrt/devenv.py",
    exports=[
        "autotestBaseDir",
        "installSource32",
        "installSource64",
        "installSourceArm64",
        "installSourceArm64ec",
        "testE2EBinaryDir32",
        "testE2EBinaryDir64",
    ],
)

installDir = os.path.join(installSourceRoot, installerName)

# Create localization .mo files.
allNodes += stageLibcdkI18nFiles("messages", "horizon-client")
allNodes += stageLibcdkI18nFiles("i18n", "horizon-client-sdk")

# We will move these files and remove ../ after viewdrivers has been
# flattened and removed.

msmNodes = []

# Build rde msms
msmNodes += buildRdeMsms(stageEnv, copyNodeInfo)

# Build usbarb msm
msmNodes += buildUsbarbMsm(stageEnv)

msmMap = {
    "../viewdrivers/msm/hznprotect": ["x64"],
    "../viewdrivers/msm/hznicpdr": ["x64"],
    "../viewdrivers/msm/vmwude": ["x64"],
    "../viewdrivers/msm/vmwusbt": ["x64"],
    "../horizonagent/msm/wsauth": ["x86", "x64"],
}

for msm, archs in msmMap.items():
    for arch in archs:
        binPath = vmware.DirAbsPath(os.path.join(installSourceRoot, arch))
        installerStagePath = installSourceRoot
        msmNodes += SConscript(
            [
                "%s.py" % msm,
            ],
            exports=["arch", "binPath", "installerStagePath"],
        )

Execute(vmware.Mkdir(installDir))

# Copy msms into installer staging dir
installerMsmNodes = []
for msmNode in msmNodes:
    installerMsmNodes += stageEnv.LinkCopy(Dir(installDir).File(msmNode.name), msmNode)
if not CODEQL_SCAN:
    allNodes += installerMsmNodes

    # Publish the Fabulatech drop components version json file
    allNodes += stageFabulatechJsonFile(stageEnv)

publishDir = vmware.ReleasePackagesDir()
if publishDir and not CODEQL_SCAN:
    # Now that everything is staged, try to build the msis and then the
    # installer. We only build them if RELEASE_PACKAGES_DIR was specified on
    # the command-line in order to save developers time.

    msiNodes = buildWincdkMSI("x64", msi64Name, installDir)
    stageEnv.Depends(msiNodes, installerMsmNodes)
    allNodes += msiNodes

    # wincdk installer combines the copied HTML5MMR msi with the wincdk MSI
    # into the bootstrapper, so we have to pass both as dependencies.
    installerBuildNode = buildWincdkInstaller(
        stageEnv, msiNodes + installerMsmNodes, installDir, installerName
    )
    allNodes += installerBuildNode

    gpoDir = os.path.join(publishDir, "gpo")
    sdkDir = os.path.join(publishDir, "sdk")
    testDir = os.path.join(publishDir, "tests")
    cdsDir = os.path.join(publishDir, "cds")

    # Stage the installers.
    installer = installerBuildNode[0].abspath
    installerCopyNode = stageEnv.LinkCopy(
        os.path.join(publishDir, os.path.basename(installer)), installer
    )
    allNodes += installerCopyNode

    # Publish the GPO template bundle.
    allNodes += publishGPOFiles(stageEnv, gpoDir, publishDir)

    # Publish the partner API bundle.
    allNodes += publishSDKZips(stageEnv, allNodes, sdkDir)

    # Publish the USB Redirection drivers
    allNodes += publishUSBClientDrivers(stageEnv)

    # Publish the signed dlls compile with arm64ec
    allNodes += stageSignedDlls(stageEnv)

    if not CODEQL_SCAN:
        # Publish all test harness zip files.
        allNodes += publishTestZips(stageEnv, allNodes, testDir)

    # Publish the CDS folder and zip file.
    # The main(modernized) installer will be used for CDS publish.
    allNodes += publishCDSFiles(stageEnv, installerCopyNode[0], publishDir, cdsDir)

    # Publish the PDB zip.
    pdbZipNode = stageEnv.Zip(
        Dir(publishDir).File(
            "Omnissa-Horizon-PDB-Bundle-%s-%s-%s.zip"
            % (yymmVersion, productVersion, buildNum)
        ),
        [
            (pdbDir, pdbDirName64),
            (pdbDir, pdbDirName86),
            (pdbDir, pdbDirNameArm64),
            (pdbDir, pdbDirNameArm64ec),
        ],
    )
    stageEnv.NoCacheIfBuildNumberInfoOverridden(pdbZipNode)
    stageEnv.Depends(pdbZipNode, pdbBundleSourceNodes)
    if not CODE_COV:
        allNodes += pdbZipNode

if not CODEQL_SCAN:
    allNodes += testE2EStageNodes

clientSdkBundleVer = (
    "%s.%s" % (productVersion, buildNum) if buildNum != "0" else productVersion
)
pkgVer = (
    "%s.%s" % (productVersion, productBuildNum)
    if productBuildNum != "0"
    else productVersion
)

if not CODEQL_SCAN:
    # Stage crtboraApiTest component test binaries
    allNodes += stageCrtboraAPITest(stageEnv)

    # Stage rdeClient component test binaries
    allNodes += stageRdeClientTest(stageEnv)

    # Stage viewClientAPITest component test binaries
    allNodes += stageViewClientAPITest(stageEnv)

    # Stage viewClientGraphicsTest component test binaries
    graphicsTestNode = stageViewClientGraphicsTest(stageEnv)
    vmware.Alias("viewClientGraphicsTest", graphicsTestNode)
    allNodes += graphicsTestNode

    # Stage crtboraRXDnDComponentTest test library
    allNodes += stageCrtboraRXDnDComponentTest(stageEnv)

    # Stage crtboraRXFAComponentTest test library
    allNodes += stageCrtboraRXFAComponentTest(stageEnv)

    # Stage antiKeyloggerTest test libraries
    allNodes += stageAntiKeyloggerTest(stageEnv)

    # Stage html5Client test binary
    allNodes += stageHtml5ClientTest(stageEnv)

    # Package code coverage data at last
    allNodes += vmware.pkg.StageCodeCoverageData(stageEnv, "hcwin", allNodes)

# Build the client SDK related parts.
if not CODE_COV and not CODEQL_SCAN:
    clientSdkBundleVer = (
        "%s.%s" % (productVersion, buildNum)
        if vmware.IsBuildNumberInfoOverridden()
        else productVersion
    )

    sdkNugetPackDir = vmware.DirAbsPath(vmware.BuildRoot(), "package")
    clientSdkNugetNode = publishSdkNugetPackage(
        stageEnv, sdkNugetPackDir, "Horizon.Client.SDK.Primitive", True
    )
    vmware.Alias("dotnet-sdk-nupkg", clientSdkNugetNode)


viewClientNodes = stageViewClient(stageEnv)
allNodes += viewClientNodes

if vmware.LocalOpts.GetString("BUILDKIND_FOR_VERSION_TAG_ONLY", None):
    # gobuild requires that we shut down all of our daemon processes
    # to avoid issues like bug 2839869.
    def ShutdownVBCS():
        import subprocess

        subprocess.check_call(
            [stageEnv["DOTNETBIN"], "build-server", "shutdown", "--vbcscompiler"]
        )

    vmware.AddExitFunc(ShutdownVBCS)

# hcwin builds use hcwin target and hccrt builds use hccrt target.
vmware.Alias("hcwin", allNodes)
vmware.Alias("hccrt", allNodes)

vmware.Alias("viewClient", viewClientNodes)
