/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

#include "cdkBasicHttpInt.h"
#include "cdkClientInfo.h"
#include "cdkConnection.h"
#include "cdkDebug.h"
#include "cdkMain.h"
#include "cdkSsl.h"
#include "cdkUrl.h"
#include "cdkUtil.h"

#ifdef _WIN32
#   include <winsock2.h>
#   include <ws2tcpip.h>
#   include <mswsock.h>
#else
#   include <errno.h>
#   include <sys/types.h>
#   include <sys/poll.h>
#   include <sys/socket.h>
#   include <sys/ioctl.h>
#   include <netdb.h>
#   include <netinet/in.h>
#   include <netinet/tcp.h>
#   include <unistd.h>
#endif
#include <openssl/pem.h>
#include <openssl/ssl.h>
#include <openssl/x509.h>
#include <stdlib.h>

#include <curl/easy.h>
#include <curl/multi.h>

#ifdef _WIN32
#   include <sys/timeb.h>
#else /* Linux */
#   include <sys/time.h>
#endif

#include <errno.h>
#include <string.h>
#if !defined(_WIN32) && !defined(N_PLAT_NLM)
#   include <strings.h> /* For strncasecmp */
#endif


#if !defined HAVE_CURLOPT_ADDRESS_SCOPE && LIBCURL_VERSION_NUM >= 0x071300
#   define HAVE_CURLOPT_ADDRESS_SCOPE 1
#endif
#if !defined HAVE_CURLOPT_CERTINFO && LIBCURL_VERSION_NUM >= 0x071301
#   define HAVE_CURLOPT_CERTINFO 1
#endif
#if !defined HAVE_CURLOPT_CONNECT_TO && LIBCURL_VERSION_NUM >= 0x073100 /* 7.49 */
#   define HAVE_CURLOPT_CONNECT_TO 1
#endif

/* #define for whether or not we support UDP */
#if defined HAVE_CURLOPT_CONNECT_TO
#   define USE_UDP_PROXY 1
#endif

#define DEFAULT_MAX_OUTSTANDING_REQUESTS ((size_t) - 1)
#define BASIC_HTTP_TIMEOUT_DATA ((void *)1)

#define BASIC_HTTP_ACTIVITY_CHECK_INTERVAL 2 /* 2 seconds */

/*The Curl connect timeout is a backup and will hopefully never fire.*/
#define DEFAULT_CONNECT_TIMEOUT 60UL  /* seconds */
#define MIN_CURL_CONNECT_TIMEOUT 90UL /* seconds */

#ifdef _WIN32
/* #define PATH_MAX 256 */
#   ifndef strcasecmp
#      define strcasecmp(_s1, _s2) g_ascii_strcasecmp((_s1), (_s2))
#   endif
#   ifndef strncasecmp
#      define strncasecmp(_s1, _s2, _n) g_ascii_strncasecmp((_s1), (_s2), (_n))
#   endif
#endif

#ifdef VIEW_EGLIB
#   define G_GINT64_FORMAT "ll"
#endif

/*
 * Return the length of the matching strings or 0 (zero) if not matching.
 * BUF may not be null terminated, so don't compare if BUF_LEN is too short.
 * STR must be null terminated and can be used in the length limited compare.
 */
#define STRNICMP_NON_TERM(STR, BUF, BUF_LEN)                                                       \
   (strlen(STR) <= BUF_LEN ? (strncasecmp(BUF, STR, strlen(STR)) == 0 ? strlen(STR) : 0) : 0)

#define HTTP_HEADER_CONTENT_LENGTH_STR "Content-Length: "
#define HTTP_HEADER_CONTENT_RANGE_STR "Content-Range: "
#define HTTP_HEADER_CONTENT_TYPE_STR "Content-Type: "
#define HTTP_HEADER_LAST_MODIFIED_STR "Last-Modified: "
#define HTTP_HEADER_ACCEPT_RANGES_STR "Accept-Ranges: "
#define HTTP_HEADER_DATE_STR "Date: "
#define HTTP_HEADER_RANGE_BYTES_STR "bytes "

#define MODULE "BasicHTTP: "

#define VMWARE_HTTPSPROXY "VMWARE_HTTPSPROXY"

typedef enum HttpHeaderComponent {
   HTTP_HEADER_COMP_UNKNOWN,
   HTTP_HEADER_COMP_CONTENT_LENGTH,
   HTTP_HEADER_COMP_CONTENT_RANGE,
   HTTP_HEADER_COMP_CONTENT_TYPE,
   HTTP_HEADER_COMP_LAST_MODIFIED,
   HTTP_HEADER_COMP_ACCEPT_RANGES,
   HTTP_HEADER_COMP_DATE,
   HTTP_HEADER_COMP_TERMINATOR,
   HTTP_HEADER_COMPONENTS_COUNT /* always last */
} HttpHeaderComponent;

struct CurlSocketState;

typedef struct CurlGlobalState {
   CURLM *curlMulti;
   struct CurlSocketState *socketList;
   gboolean useGlib;
   GHashTable *requests;
   GHashTable *peerCertCache;
   gboolean skipRemove;

   size_t maxOutstandingRequests;
   GQueue *pending;

   guint socketPollHandlerId;
   GHashTable *sessionHandles;
} CurlGlobalState;

typedef struct CurlSocketState {
   struct CurlSocketState *next;

   curl_socket_t sock;
   CURL *curl;
   int action;
   guint readHandlerId;
   guint writeHandlerId;
#ifdef __APPLE__
   guint rwHandlerId;
#endif
} CurlSocketState;

static const char *defaultUserAgent = "VMware-client";

static CurlSocketState *CdkBasicHttpFindSocket(curl_socket_t sock);

static CurlSocketState *CdkBasicHttpAddSocket(curl_socket_t sock, CURL *curl, int action);

static void CdkBasicHttpRemoveSocket(curl_socket_t sock);
#ifdef __APPLE__
static void CdkBasicHttpSetSocketKeepAlive(curl_socket_t sock);
#endif
static void CdkBasicHttpSetSocketState(CurlSocketState *socketState, curl_socket_t sock, CURL *curl,
                                       int action);

static void CdkBasicHttpPollAdd(CurlSocketState *socketState);

static void CdkBasicHttpPollRemove(CurlSocketState *socketState);

static gboolean CdkBasicHttpSocketPollCallback(gpointer clientData);

static void CdkBasicHttpSocketPollIoCallback(CdkFd fd, gpointer clientData);

static gboolean CdkBasicHttpConnectionTimeoutCallback(void *clientData);

static gboolean CdkBasicHttpInactivityTimeoutCallback(void *clientData);

static unsigned long *CdkBasicHttpStateToInactivityTimeout(CdkBasicHttpRequest *request,
                                                           CdkBasicHttpState state);

static void CdkBasicHttpGotActivity(CdkBasicHttpRequest *request);

static void CdkBasicHttpInitCurlMulti();

static void CdkBasicHttpInvokeCallback(CdkBasicHttpRequest *request);

static void CdkBasicHttpSetConnectionTimeout(CdkBasicHttpRequest *request);

static void CdkBasicHttpClearConnectionTimeout(CdkBasicHttpRequest *request);

static void CdkBasicHttpSetInactivityTimeout(CdkBasicHttpRequest *request);

static void CdkBasicHttpClearInactivityTimeout(CdkBasicHttpRequest *request);

static gboolean CdkBasicHttpIsSocketConnected(curl_socket_t socket);

static int CdkBasicHttpSocketCurlCallback(CURL *curl, curl_socket_t sock, int action,
                                          void *clientData, void *socketp); /* private socket */

static int CdkBasicHttpTimerCurlCallback(CURLM *multi, long timeoutMS, void *clientData);

static curl_socket_t CdkBasicHttpOpenSocketCurlCallback(void *clientp, curlsocktype purpose,
                                                        struct curl_sockaddr *address);

static int CdkBasicHttpDebugCurlCallback(CURL *curl, curl_infotype type, char *msg, size_t msgLen,
                                         void *clientData);

static gboolean CdkBasicHttpStartRequest(CdkBasicHttpRequest *request);
static void CdkBasicHttpFinishRequest(CdkBasicHttpRequest *request, CdkBasicHttpState finishState);

static size_t CdkBasicHttpHeaderCallback(void *buffer, size_t size, size_t nmemb, void *clientData);

static size_t CdkBasicHttpReadCallback(void *buffer, size_t size, size_t nmemb, void *clientData);

static size_t CdkBasicHttpWriteCallback(void *buffer, size_t size, size_t nmemb, void *clientData);

static curlioerr CdkBasicHttpIoctlCallback(CURL *handle, int cmd, void *clientData);

/* Consider these the proper ways to invoke CdkBasicHttpSource methods. */
static gssize CdkBasicHttpSourceRead(CdkBasicHttpSource *source, void *buffer, size_t size,
                                     size_t nmemb);

static gboolean CdkBasicHttpSourceRewind(CdkBasicHttpSource *source);

static size_t CdkBasicHttpSourceLength(CdkBasicHttpSource *source);

static gboolean CdkBasicHttpResumePollCallback(void *clientData);

void CdkBasicHttpRemoveResumePollCallback(CdkBasicHttpRequest *request);

extern void CdkBasicHttpBandwidthReset(CdkBandwidthStatistics *bwStat);

extern void CdkBasicHttpBandwidthUpdate(CdkBandwidthStatistics *bwStat, guint64 transferredBytes);

extern void CdkBasicHttpBandwidthSlideWindow(CdkBandwidthStatistics *bwStat);

extern gint64 CdkBasicHttpBandwidthGetDelay(CdkBasicHttpBandwidthGroup *group,
                                            CdkBasicHttpRequest *request,
                                            CdkBandwidthDirection direction);

static gboolean CdkStrUtil_GetNextInt64Token(gint64 *out, unsigned int *index, const char *str,
                                             const char *delimiters);

static gboolean CdkStrUtil_StrToInt64(gint64 *out, const char *str);

#if defined USE_UDP_PROXY
static gboolean CdkBasicHttpSetUdpProxyInfo(CdkBasicHttpRequest *request, const char *hostname,
                                            unsigned short port);

static void CdkBasicHttpUdpProxyErrorProc2(CdkUdpProxyErrorCode error, const char *remoteHost,
                                           void *context);
#endif

static void CdkBasicHttpAddDNSResolveEntry(CdkBasicHttpRequest *request);

static void CdkBasicHttpRemoveDNSResolveEntry(CdkBasicHttpRequest *request);

static CurlGlobalState *curlGlobalState = NULL;
static CdkBasicHttpCookieJar *defaultCookieJar = NULL;

static gboolean basicHttpTrace = 0;

/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttpSetSockOpts --
 *
 *       Adjust socket options.
 *
 * Results:
 *       0 on success, 1 on error.
 *
 * Side effects:
 *
 *-----------------------------------------------------------------------------
 */

static int
CdkBasicHttpSetSockOpts(void *clientp,        /* IN */
                        curl_socket_t sockfd, /* IN */
                        curlsocktype purpose) /* IN  */
{
   CdkBasicHttpRequest *request = (CdkBasicHttpRequest *)clientp;
   int val = 0;
   int sockRet = 0;

   ENTRY;

   g_return_val_if_fail(request, 1);

#ifdef _WIN32
   /*
    * There's a bug in WSA when using non-blocking sockets. If the send size is bigger
    * than the socket send buffer size, it will cause a delayed ACK.
    * The workaround is to ensure that the socket send buffer size is larger than the
    * max buffer that cURL will try to write.
    * See http://support.microsoft.com/kb/823764
    */

   val = CURL_MAX_WRITE_SIZE * 2 + 32;
   sockRet = setsockopt(sockfd, SOL_SOCKET, SO_SNDBUF, (const char *)&val, sizeof(val));
   if (sockRet != 0) {
      LERROR(0, ("CdkBasicHttpSetSockOpts: Failed to set send buffer size."));
      GOTO(0, exit);
   }
#endif

   /* Set TCP_NODELAY (disable Nagle) if the caller requested it. */
   if (request->flags & CDK_BASICHTTP_TCP_NODELAY) {
      val = 1;
      sockRet = setsockopt(sockfd, IPPROTO_TCP, TCP_NODELAY, (const char *)&val, sizeof(val));
      if (sockRet != 0) {
         LERROR(0, ("CdkBasicHttpSetSockOpts: Failed to set TCP_NODELAY."));
         GOTO(0, exit);
      }
   }
#ifdef __APPLE__
   CdkBasicHttpSetSocketKeepAlive(sockfd);
#endif
exit:
   /* Returning 1 indicates an error to curl. */
   RETURN(sockRet == 0 ? 0 : 1);
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttp_Init --
 *
 *
 * Results:
 *       None.
 *
 * Side effects:
 *
 *-----------------------------------------------------------------------------
 */

gboolean
CdkBasicHttp_Init(void)
{
   ENTRY;
   RETURN(CdkBasicHttp_InitEx(DEFAULT_MAX_OUTSTANDING_REQUESTS));
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttp_InitEx --
 *
 *
 * Results:
 *       None.
 *
 * Side effects:
 *
 *-----------------------------------------------------------------------------
 */

gboolean
CdkBasicHttp_InitEx(size_t maxOutstandingRequests) /* IN */
{
   gboolean success = TRUE;
   CURLcode code = CURLE_OK;
   char *traceVar = NULL;

   ENTRY;

#ifndef METRO
   traceVar = getenv("VMWARE_BASICHTTP_TRACE");
#else
   // [Metro] XXX TODO: We will write the trace flag in prefs or other ways.
   traceVar = "1";
#endif
   if ((traceVar && strcmp(traceVar, "0")) || CdkDebug_DebugLoggingIsEnabled()) {
      basicHttpTrace = 1;
   }

   if (NULL != curlGlobalState) {
      g_assert_not_reached();
   }

#if defined _WIN32 && !defined __MINGW32__
   code = curl_global_init(CURL_GLOBAL_WIN32);
#else
   code = curl_global_init(CURL_GLOBAL_ALL);
#endif

   if (CURLE_OK != code) {
      success = FALSE;
      GOTO(0, abort);
   }

   LINFO(0, ("Built using %s", OPENSSL_VERSION_TEXT));
   if (strncmp(curl_version(), "libcurl/" LIBCURL_VERSION, strlen("libcurl/" LIBCURL_VERSION))) {
      LINFO(0, ("Built using libcurl %s", LIBCURL_VERSION));
   }
   LINFO(0, ("Using %s", curl_version()));

   curlGlobalState = g_new0(CurlGlobalState, 1);
   CdkBasicHttpInitCurlMulti();
   curlGlobalState->useGlib = FALSE;
   if (NULL == curlGlobalState->curlMulti) {
      success = FALSE;
      GOTO(0, abort);
   }

   curlGlobalState->requests = g_hash_table_new(g_direct_hash, g_direct_equal);
   curlGlobalState->peerCertCache =
      g_hash_table_new_full(g_direct_hash, g_direct_equal, NULL, CdkUtil_FreePeerCertificates);
   curlGlobalState->skipRemove = FALSE;
   curlGlobalState->maxOutstandingRequests = maxOutstandingRequests;
   curlGlobalState->pending = g_queue_new();
   curlGlobalState->sessionHandles = g_hash_table_new(g_direct_hash, g_direct_equal);

abort:
   if (!success) {
      g_free(curlGlobalState);
      curlGlobalState = NULL;
   }

   RETURN(success);
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttp_Reset --
 *
 *      Shutdown and re-initialize BasicHttp.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      Cancels all pending requests.
 *
 *-----------------------------------------------------------------------------
 */

void
CdkBasicHttp_Reset(void)
{
   size_t maxOutstandingRequests;

   ENTRY;

   g_assert(curlGlobalState);

   maxOutstandingRequests = curlGlobalState->maxOutstandingRequests;

   CdkBasicHttp_Shutdown();
   CdkBasicHttp_InitEx(maxOutstandingRequests);

   EXIT;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttp_ResetConnection --
 *
 *      Reset the CURL Multi to ask CURL to close all opened socket
 *      file descriptors.
 *      The difference between this method and CdkBasicHttp_Reset is that,
 *      this API only resets the CURL Multi instance to reset sockets. The
 *      other one also resets the global state instance.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      All pending requests are discarded without any notification.
 *
 *-----------------------------------------------------------------------------
 */

void
CdkBasicHttp_ResetConnection(void)
{
   ENTRY;

   g_assert(curlGlobalState);
   /*
    * The old sessionHandles needs to be destroy here,
    * which reflects the removal of all curl handles
    * in the curlMulti instance.
    */
   g_hash_table_destroy(curlGlobalState->sessionHandles);
   curlGlobalState->sessionHandles = g_hash_table_new(g_direct_hash, g_direct_equal);
   curl_multi_cleanup(curlGlobalState->curlMulti);
   CdkBasicHttpInitCurlMulti();

   EXIT;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttp_SetSslCtxProc --
 *
 *      Sets the ssl context function for a given request.  This
 *      callback will be called after curl initializes all ssl
 *      options, but before the request is issued.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

void
CdkBasicHttp_SetSslCtxProc(CdkBasicHttpRequest *request,       /* IN */
                           CdkBasicHttpSslCtxProc *sslCtxProc) /* IN */
{
   ENTRY;
   request->sslCtxProc = sslCtxProc;
   EXIT;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttpRemoveFreeRequest --
 *
 *      Remove the connection for an outstanding request and then free
 *      the request.
 *
 * Results:
 *      Always 0.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

static int
CdkBasicHttpRemoveFreeRequest(CdkBasicHttpRequest *request, /* IN */
                              void *value,                  /* IN: Unused */
                              void *clientData)             /* IN: Unused */
{
   ENTRY;

   CdkBasicHttp_FreeRequest(request);

   RETURN(0);
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttp_Shutdown --
 *
 *
 * Results:
 *       None.
 *
 * Side effects:
 *
 *-----------------------------------------------------------------------------
 */

void
CdkBasicHttp_Shutdown(void)
{
   ENTRY;

   if (NULL != curlGlobalState) {
      curlGlobalState->skipRemove = TRUE;
      g_hash_table_foreach(curlGlobalState->requests, (GHFunc)CdkBasicHttpRemoveFreeRequest, NULL);
      g_hash_table_destroy(curlGlobalState->requests);
      curlGlobalState->requests = NULL;
      g_queue_foreach(curlGlobalState->pending, (GFunc)g_free, NULL);
      g_queue_free(curlGlobalState->pending);
      curlGlobalState->pending = NULL;
      g_hash_table_destroy(curlGlobalState->sessionHandles);
      curlGlobalState->sessionHandles = NULL;
   }

   if (NULL != defaultCookieJar) {
      CdkBasicHttp_FreeCookieJar(defaultCookieJar);
      defaultCookieJar = NULL;
   }

   if (NULL != curlGlobalState) {
      curl_multi_cleanup(curlGlobalState->curlMulti);
      curl_global_cleanup();
      g_free(curlGlobalState);
      curlGlobalState = NULL;
   }

   EXIT;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttp_CreateCookieJar --
 *
 *
 * Results:
 *       CdkBasicHttpCookieJar.
 *
 * Side effects:
 *
 *-----------------------------------------------------------------------------
 */

CdkBasicHttpCookieJar *
CdkBasicHttp_CreateCookieJar(void)
{
   CdkBasicHttpCookieJar *cookieJar;

   ENTRY;

   if (!curlGlobalState) {
      RETURN(NULL);
   }

   cookieJar = g_new0(CdkBasicHttpCookieJar, 1);
   cookieJar->curlShare = curl_share_init();
   curl_share_setopt(cookieJar->curlShare, CURLSHOPT_SHARE, CURL_LOCK_DATA_COOKIE);
   cookieJar->initialCookie = NULL;
   cookieJar->cookieFile = NULL;
   cookieJar->newSession = FALSE;

   RETURN(cookieJar);
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttp_CreateCookieFile --
 *
 *      Create a cookie jar based on a file.
 *      This function is disabled since the version 1.4.0
 *      See the comments below in the code section.
 *
 * Results:
 *      CdkBasicHttpCookieJar.
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

CdkBasicHttpCookieJar *
CdkBasicHttp_CreateCookieFile(const char *cookieFile)
{
   CdkBasicHttpCookieJar *cookieJar;

   ENTRY;

   /*
    * Using cookie file cache is no longer supported since
    * version 1.4.0.
    * The reason of this is related to a BUG:
    * VMW bug 821583
    * And the related information can be found here:
    * VMW review 304607
    *
    * Basically, sometimes the old session cookie cannot be
    * removed by resetting it via libcurl API on iOS platform.
    * And the session remains permanently, which caused that
    * the authentication step was skipped since then.
    */
   g_assert(FALSE);

   cookieJar = g_new0(CdkBasicHttpCookieJar, 1);
   cookieJar->curlShare = NULL;
   cookieJar->initialCookie = NULL;
   cookieJar->cookieFile = g_strdup(cookieFile);
   cookieJar->newSession = FALSE;

   RETURN(cookieJar);
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttp_SetInitialCookie --
 *
 *       Set the initial cookie for a cookie jar. This should only be called
 *       after the cookie Jar is created, and really should only be called
 *       before any requests have been made - the results will be confusing
 *       otherwise.
 *
 *       The cookie should be in either the "Set-Cookie:" format returned
 *       by an http server or netscape/mozilla cookie file format.
 *
 * Results:
 *       None.
 *
 * Side effects:
 *       None.
 *
 *-----------------------------------------------------------------------------
 */

void
CdkBasicHttp_SetInitialCookie(CdkBasicHttpCookieJar *cookieJar, /* IN */
                              const char *cookie)               /* IN */
{
   ENTRY;

   g_return_if_fail(NULL == cookieJar->initialCookie);

   cookieJar->initialCookie = g_strdup(cookie);

   EXIT;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttp_NewCookieSession --
 *
 *      New connections using this jar will start a new cookie session.
 *      If a cookie file is in use, only session-specific cookies are removed.
 *      If a cookie file is not in use, all cookies are removed.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

void
CdkBasicHttp_NewCookieSession(CdkBasicHttpCookieJar *cookieJar) /* IN */
{
   ENTRY;

   /*
    * The newSession variable only does anything in curl if we are using a
    * cookie file. If not using a cookie file, it is best to free the cookie
    * share object and create a new one.  See bug 754060 for more information.
    */
   if (cookieJar->cookieFile) {
      cookieJar->newSession = TRUE;
   } else {
      if (cookieJar->curlShare) {
         curl_share_setopt(cookieJar->curlShare, CURLSHOPT_UNSHARE, CURL_LOCK_DATA_COOKIE);
         curl_share_cleanup(cookieJar->curlShare);
      }
      cookieJar->curlShare = curl_share_init();
      curl_share_setopt(cookieJar->curlShare, CURLSHOPT_SHARE, CURL_LOCK_DATA_COOKIE);
   }

   EXIT;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttp_FreeCookieJar --
 *
 *
 * Results:
 *       None.
 *
 * Side effects:
 *
 *-----------------------------------------------------------------------------
 */

void
CdkBasicHttp_FreeCookieJar(CdkBasicHttpCookieJar *cookieJar) /* IN */
{
   ENTRY;

   if (NULL == cookieJar) {
      EXIT;
   }

   if (cookieJar->curlShare) {
      curl_share_setopt(cookieJar->curlShare, CURLSHOPT_UNSHARE, CURL_LOCK_DATA_COOKIE);
      curl_share_cleanup(cookieJar->curlShare);
   }
   g_free(cookieJar->initialCookie);
   g_free(cookieJar->cookieFile);
   g_free(cookieJar);

   EXIT;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttpSocketCurlCallback --
 *
 *
 * Results:
 *       None.
 *
 * Side effects:
 *
 *-----------------------------------------------------------------------------
 */

int
CdkBasicHttpSocketCurlCallback(CURL *curl,         /* IN */
                               curl_socket_t sock, /* IN */
                               int action,         /* IN */
                               void *clientData,   /* IN */
                               void *socketp)      /* IN */
{
   CurlSocketState *socketState;

   ENTRY;

   g_assert(NULL != curlGlobalState);

   if (CURL_POLL_REMOVE == action) {
      CdkBasicHttpRemoveSocket(sock);
   } else if (CURL_POLL_NONE != action) {
      socketState = CdkBasicHttpFindSocket(sock);

      if (NULL == socketState) {
         CdkBasicHttpAddSocket(sock, curl, action);
      } else {
         CdkBasicHttpSetSocketState(socketState, sock, curl, action);
      }
   }

   RETURN(0);
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttpTimerCurlCallback --
 *
 *      Callback function that libcurl calls when it wants us to adjust the
 *      timeout callback we're running on the poll loop. Curl uses this
 *      mechanism to implement timeouts on its http connections.
 *
 * Results:
 *      Always 0.
 *
 * Side effects:
 *      Old timer callback is always cleared and a new one might be registered.
 *
 *-----------------------------------------------------------------------------
 */

int
CdkBasicHttpTimerCurlCallback(CURLM *multi,     /* IN */
                              long timeoutMS,   /* IN */
                              void *clientData) /* IN */
{
   ENTRY;

   LDEBUG(0, ("%s: timeoutMS = %ld msec", __FUNCTION__, timeoutMS));

   CDK_MAIN_REMOVE(curlGlobalState->socketPollHandlerId);

   if (timeoutMS >= 0) {
      curlGlobalState->socketPollHandlerId =
         CdkMain_AddTimeout(timeoutMS, CdkBasicHttpSocketPollCallback, BASIC_HTTP_TIMEOUT_DATA);
   }

   RETURN(0);
}


/*
 *-----------------------------------------------------------------------------
 *
 *  CdkBasicHttpOpenSocketCurlCallback --
 *
 *     Callback function invoked by libcurl instead of the socket() call.
 *     We want to create the socket ourselves so that we can associate it with
 *     the CdkBasicHttpRequest.
 *
 *     This is needed to work around a bug in libcurl where the
 *     CURLOPT_CONNECTTIMEOUT option is not respected when using the multi interface.
 *     If a connection timeout is specified, we add a poll timer callback. When the
 *     timer expires, we check the state of the socket. If it is not in the connected
 *     state, we fail the request.
 *
 *     Presumably, libcurl will invoke connect() soon after creating the socket.
 *
 * Results:
 *     The file descriptor of the newly created socket on success, -1 on error.
 *
 * Side effects:
 *     None.
 *
 *-----------------------------------------------------------------------------
 */

curl_socket_t
CdkBasicHttpOpenSocketCurlCallback(void *clientp,                 /* IN */
                                   curlsocktype purpose,          /* IN */
                                   struct curl_sockaddr *address) /* IN */
{
   CdkBasicHttpRequest *req = (CdkBasicHttpRequest *)clientp;

   ENTRY;

   g_return_val_if_fail(req, 0);

   req->sock = socket(address->family, address->socktype, address->protocol);
   if (!CdkFd_IsValid(req->sock)) {
      LERROR(0, (MODULE "Failed to create socket of family: %d\ttype: %d\tprotocol: %d",
                 address->family, address->socktype, address->protocol));
      RETURN(CURL_SOCKET_BAD);
   }
   LDEBUG(0, (MODULE "%s: fd %d is created", __FUNCTION__, req->sock));

   RETURN(req->sock);
}


/*
 *-----------------------------------------------------------------------------
 *
 *  CdkBasicHttpDebugCurlCallback --
 *
 *     Callback function invoked by libcurl to log debug messages.
 *
 * Results:
 *     0.
 *
 * Side effects:
 *     None.
 *
 *-----------------------------------------------------------------------------
 */

int
CdkBasicHttpDebugCurlCallback(CURL *curl,         /* IN */
                              curl_infotype type, /* IN */
                              char *msg,          /* IN */
                              size_t msgLen,      /* IN */
                              void *clientData)   /* IN */
{
   const char *typeStr = NULL;
   CdkBasicHttpRequest *request = (CdkBasicHttpRequest *)clientData;

   g_return_val_if_fail(request, 0);
   g_return_val_if_fail(msg, 0);

   switch (type) {
   case CURLINFO_TEXT:
      typeStr = "TEXT";
      break;

   case CURLINFO_HEADER_IN:
      typeStr = "HEADER_IN";
      break;

   case CURLINFO_HEADER_OUT:
      typeStr = "HEADER_OUT";
      break;

   case CURLINFO_DATA_IN:
      typeStr = "DATA_IN";
      break;

   case CURLINFO_DATA_OUT:
      typeStr = "DATA_OUT";
      break;

   case CURLINFO_SSL_DATA_IN:
      typeStr = "SSL_DATA_IN";
      break;

   case CURLINFO_SSL_DATA_OUT:
      typeStr = "SSL_DATA_OUT";
      break;

   case CURLINFO_END:
      typeStr = "END";
      break;

   default:
      typeStr = "UNKNOWN";
      break;
   }

   switch (type) {
   /* These are the message types that should be human-readable text. */
   case CURLINFO_TEXT:
   case CURLINFO_HEADER_IN:
   case CURLINFO_HEADER_OUT: {
      /*
       * Avoid exposing JESSIONID by masking most bits of JSESSIONID as '*'.
       * For CURLINFO_TEXT, we will have:
       *    Added cookie JSESSIONID="3DE4***404E" for...
       * For CURLINFO_HEADER_IN and CURLINFO_HEADER_OUT, we will have:
       *    Cookie: JSESSIONID=6F37***CAE1;...
       */
      gboolean logged = FALSE;
      gchar *sessionStr = (type == CURLINFO_TEXT) ? "JSESSIONID=\"" : "JSESSIONID=";
      gchar *firstPart = g_strstr_len(msg, msgLen, sessionStr);
      gchar *lastPart = NULL;

      if (!firstPart) {
         // Do the same masking for ACCESSPOINTSESSIONID which comes from UAG
         sessionStr = (type == CURLINFO_TEXT) ? "ACCESSPOINTSESSIONID=\"" : "ACCESSPOINTSESSIONID=";
         firstPart = g_strstr_len(msg, msgLen, sessionStr);
      }

      if (firstPart) {
         firstPart += strlen(sessionStr);
         lastPart =
            g_strstr_len(firstPart, msgLen + msg - firstPart, (type == CURLINFO_TEXT) ? "\"" : ";");
      } else {
         const gchar *AuthorizationStr = "Authorization: Bearer ";
         firstPart = g_strstr_len(msg, msgLen, AuthorizationStr);
         if (firstPart) {
            firstPart += strlen(AuthorizationStr);
            lastPart = g_strstr_len(firstPart, msgLen + msg - firstPart, "\r\n");
         }
      }

      if (firstPart && lastPart > firstPart + 8) {
         firstPart += 4; /* keep first 4 characters. */
         lastPart -= 4;  /* keep last 4 characters as well. */
         LINFO(0, (MODULE "curl (%s) on request %p: %.*s***%.*s", typeStr, request,
                   (int)(firstPart - msg), msg, (int)(msgLen + msg - lastPart), lastPart));
         logged = TRUE;
      }

      if (!logged) {
         LINFO(0, (MODULE "curl (%s) on request %p: %.*s", typeStr, request, (int)msgLen, msg));
      }
   } break;

   /* Anything else is usually binary data. */
   case CURLINFO_DATA_IN:
   case CURLINFO_DATA_OUT:
   case CURLINFO_SSL_DATA_IN:
   case CURLINFO_SSL_DATA_OUT:
   default:
      LINFO(0, (MODULE "curl (%s) on request %p: %" G_GSIZE_FORMAT " bytes of data.", typeStr,
                request, msgLen));
      break;
   }

   /* Must return 0. */
   return 0;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttpFindSocket --
 *
 *
 * Results:
 *       None.
 *
 * Side effects:
 *
 *-----------------------------------------------------------------------------
 */

CurlSocketState *
CdkBasicHttpFindSocket(curl_socket_t sock) /* IN */
{
   CurlSocketState *socketState = NULL;

   ENTRY;

   g_assert(NULL != curlGlobalState);

   socketState = curlGlobalState->socketList;
   while (NULL != socketState) {
      if (sock == socketState->sock)
         break;

      socketState = socketState->next;
   }

   RETURN(socketState);
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttpAddSocket --
 *
 *
 * Results:
 *       None.
 *
 * Side effects:
 *
 *-----------------------------------------------------------------------------
 */

CurlSocketState *
CdkBasicHttpAddSocket(curl_socket_t sock, /* IN */
                      CURL *curl,         /* IN */
                      int action)         /* IN */
{
   CurlSocketState *socketState = NULL;
   CdkBasicHttpRequest *request = NULL;

   ENTRY;

   g_assert(NULL != curlGlobalState);
   g_return_val_if_fail(NULL == CdkBasicHttpFindSocket(sock), NULL);

   curl_easy_getinfo(curl, CURLINFO_PRIVATE, (void *)&request);
   if (request) {
      if (request->sock != CURL_SOCKET_BAD) {
         /*
          * It turns out it's totally normal for a request to get
          * assigned multiple sockets throughout its lifetime; if the
          * request is in CURLM_STATE_WAITRESOLVE, the fd we get will be
          * the dns socket, rather than the http socket. It's not clear
          * under what circumstances we could get a second, different
          * http socket, but if we do, we should clear our peer certs.
          *
          * Continue to assert that we don't, for now.
          *
          * See bug #750578.
          */
         g_assert(!request->peerCertificates);
         if (request->peerCertificates) {
            LDEBUG(0, ("Clearing peer certs %p for sock %d, changing to sock %d",
                       request->peerCertificates, (int)request->sock, (int)sock));
            sk_X509_pop_free(request->peerCertificates, X509_free);
            request->peerCertificates = NULL;
         }
      }
      request->sock = sock;
   }

   socketState = g_new0(CurlSocketState, 1);
   socketState->sock = sock;
   socketState->curl = curl;
   socketState->action = action;

   CdkBasicHttpPollAdd(socketState);

   socketState->next = curlGlobalState->socketList;
   curlGlobalState->socketList = socketState;

   RETURN(socketState);
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttpRemoveSocket --
 *
 *
 * Results:
 *       None.
 *
 * Side effects:
 *
 *-----------------------------------------------------------------------------
 */

void
CdkBasicHttpRemoveSocket(curl_socket_t sock) /* IN */
{
   CurlSocketState **socketState;
   CurlSocketState *socketStateToRemove = NULL;

   ENTRY;

   g_assert(NULL != curlGlobalState);

   socketState = &(curlGlobalState->socketList);
   while (NULL != *socketState) {
      if (sock != (*socketState)->sock) {
         socketState = &((*socketState)->next);
         continue;
      }

      socketStateToRemove = *socketState;
      *socketState = (*socketState)->next;

      CdkBasicHttpPollRemove(socketStateToRemove);
      g_free(socketStateToRemove);
   }

   EXIT;

} // CdkBasicHttpRemoveSocket


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttpSetSocketAlive --
 *
 *     Set the socket to keep alive.
 *
 * Results:
 *     None.
 *
 * Side effects:
 *     keepalive probe may be sent on this socket
 *-----------------------------------------------------------------------------
 */
#ifdef __APPLE__
void
CdkBasicHttpSetSocketKeepAlive(curl_socket_t sock) /* IN */
{
   ENTRY;
   /* active the keep alive */
   int keepalive = 1;
   /*
    * the interval between the last data packet sent (simple ACKs are not
    * considered data) and the first keepalive probe;
    */
   int keepidle = 30;
   /* the interval between subsequential keepalive probes */
   int keepinterval = 5;
   /*
    * the number of unacknowledged probes to send before considering the
    * connection dead and notifying the application layer
    */
   int keepcount = 3;

   setsockopt(sock, SOL_SOCKET, SO_KEEPALIVE, (void *)&keepalive, sizeof(keepalive));
   setsockopt(sock, IPPROTO_TCP, TCP_KEEPALIVE, (void *)&keepidle, sizeof(keepidle));
   setsockopt(sock, IPPROTO_TCP, TCP_KEEPINTVL, (void *)&keepinterval, sizeof(keepinterval));
   setsockopt(sock, IPPROTO_TCP, TCP_KEEPCNT, (void *)&keepcount, sizeof(keepcount));
   EXIT;
} // CdkBasicHttpSetSocketAlive
#endif


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttpSetSocketState --
 *
 *
 * Results:
 *       None.
 *
 * Side effects:
 *
 *-----------------------------------------------------------------------------
 */

void
CdkBasicHttpSetSocketState(CurlSocketState *socketState, /* IN */
                           curl_socket_t sock,           /* IN */
                           CURL *curl,                   /* IN */
                           int action)                   /* IN */
{
   ENTRY;

   g_return_if_fail(NULL != socketState);

   if ((socketState->sock != sock) || (socketState->curl != curl) ||
       (socketState->action != action)) {
      CdkBasicHttpPollRemove(socketState);
      socketState->sock = sock;
      socketState->curl = curl;
      socketState->action = action;
      CdkBasicHttpPollAdd(socketState);
   }

   EXIT;

} // CdkBasicHttpSetSocketState


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttpPollAdd --
 *
 *
 * Results:
 *       None.
 *
 * Side effects:
 *
 *-----------------------------------------------------------------------------
 */

void
CdkBasicHttpPollAdd(CurlSocketState *socketState) /* IN */
{
   ENTRY;
#ifdef __APPLE__
   static curl_socket_t preSock = -1;
#endif

   g_return_if_fail(NULL != socketState);
#ifdef __APPLE__
   /*
    * SR 1802670 CFSocket cannot support to create multiple CFSocketRef
    * on same native socket.
    */
   if ((CURL_POLL_IN & socketState->action) && (CURL_POLL_OUT & socketState->action)) {
      socketState->rwHandlerId =
         CdkMain_AddSocket(socketState->sock, CdkBasicHttpSocketPollIoCallback,
                           CDK_MAIN_POLL_FLAG_READ | CDK_MAIN_POLL_FLAG_WRITE, socketState);
   } else if (CURL_POLL_IN & socketState->action) {
      socketState->readHandlerId = CdkMain_AddSocket(
         socketState->sock, CdkBasicHttpSocketPollIoCallback, CDK_MAIN_POLL_FLAG_READ, socketState);
   } else if (CURL_POLL_OUT & socketState->action) {
      socketState->writeHandlerId =
         CdkMain_AddSocket(socketState->sock, CdkBasicHttpSocketPollIoCallback,
                           CDK_MAIN_POLL_FLAG_WRITE, socketState);
   }
#else
   if (CURL_POLL_IN & socketState->action) {
      socketState->readHandlerId = CdkMain_AddSocket(
         socketState->sock, CdkBasicHttpSocketPollIoCallback, CDK_MAIN_POLL_FLAG_READ, socketState);
   }
   if (CURL_POLL_OUT & socketState->action) {
      socketState->writeHandlerId =
         CdkMain_AddSocket(socketState->sock, CdkBasicHttpSocketPollIoCallback,
                           CDK_MAIN_POLL_FLAG_WRITE, socketState);
   }
#endif

   LDEBUG(0, ("%s: Adding poll with fd %d.", __FUNCTION__, socketState->sock));

#ifdef __APPLE__
   if (preSock != socketState->sock) {
      preSock = socketState->sock;
#else
   /* Only sets local address if the previous value is NULL */
   if (CdkUtil_GetLocalAddress() == NULL) {
#endif
      CdkUtil_SetLocalAddress(socketState->sock);
   }

   EXIT;

} // CdkBasicHttpPollAdd


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttpPollRemove --
 *
 *
 * Results:
 *       None.
 *
 * Side effects:
 *
 *-----------------------------------------------------------------------------
 */

void
CdkBasicHttpPollRemove(CurlSocketState *socketState) /* IN */
{
   ENTRY;

   g_return_if_fail(NULL != socketState);

   LDEBUG(0, ("%s: Removing poll with fd %d.", __FUNCTION__, socketState->sock));
#ifdef __APPLE__
   CDK_MAIN_REMOVE(socketState->rwHandlerId);
#endif
   CDK_MAIN_REMOVE(socketState->readHandlerId);
   CDK_MAIN_REMOVE(socketState->writeHandlerId);

   EXIT;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttpGetPeerCertificates --
 *
 *      Use Curl's peer certificate method to get the certificates
 *      seen with this request. If certificates aren't available, try
 *      to find them in our cache.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      Sets request->peerCertificates.
 *
 *-----------------------------------------------------------------------------
 */

static void
CdkBasicHttpGetPeerCertificates(CdkBasicHttpRequest *request) /* IN */
{
#if HAVE_CURLOPT_CERTINFO
   STACK_OF(X509) *sk = NULL;
   BIO *mem;
   X509 *x509;
   int i;
   struct curl_certinfo *certinfo = NULL;
   struct curl_slist *li;

   ENTRY;

   if (request->sock == CURL_SOCKET_BAD) {
      LDEBUG(0, ("No peer certificates available as there is no socket "
                 "associated with the request."));
      EXIT;
   }

   if (request->peerCertificates) {
      LDEBUG(0,
             ("Adding peer certs %p for sock %d", request->peerCertificates, (int)request->sock));
      g_hash_table_insert(curlGlobalState->peerCertCache, GINT_TO_POINTER(request->sock),
                          request->peerCertificates);
   } else if (!curl_easy_getinfo(request->curl, CURLINFO_CERTINFO, (void *)&certinfo) && certinfo &&
              certinfo->num_of_certs) {
      g_hash_table_remove(curlGlobalState->peerCertCache, GINT_TO_POINTER(request->sock));
      sk = sk_X509_new_null();
      for (i = 0; i < certinfo->num_of_certs; i++) {
         for (li = certinfo->certinfo[i]; li; li = li->next) {
            if (!strncmp("Cert:", li->data, 5)) {
               mem = BIO_new_mem_buf(li->data + 5, -1);
               x509 = PEM_read_bio_X509(mem, NULL, NULL, NULL);
               BIO_free(mem);
               if (!x509) {
                  sk_X509_pop_free(sk, X509_free);
                  EXIT;
               }
               sk_X509_push(sk, x509);
               break;
            }
         }
      }
      LDEBUG(0, ("Adding peer certs %p for sock %d", sk, (int)request->sock));
      g_hash_table_insert(curlGlobalState->peerCertCache, GINT_TO_POINTER(request->sock), sk);
   } else {
      sk = g_hash_table_lookup(curlGlobalState->peerCertCache, GINT_TO_POINTER(request->sock));
      LDEBUG(0, ("Found peer certs %p for sock %d", sk, (int)request->sock));
   }

   LDEBUG(0, ("Found %d peer certs for request %p", sk_X509_num(sk), request));

   request->peerCertificates = CdkUtil_DupPeerCertificates(sk);
#else
   ENTRY;
#endif
   EXIT;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttpCompleteRequestCallback --
 *
 *
 * Results:
 *       None.
 *
 * Side effects:
 *
 *-----------------------------------------------------------------------------
 */

static void
CdkBasicHttpCompleteRequestCallback(void *clientData) /* IN */
{
   CdkBasicHttpRequest *request;
   CdkBasicHttpResponse *response;
   CdkBasicHttpErrorCode errorCode;
   char *effectiveURL;

   ENTRY;

   g_return_if_fail(NULL != clientData);

   request = (CdkBasicHttpRequest *)clientData;
   response = g_new0(CdkBasicHttpResponse, 1);

   /* Do some cleanup and move to CDK_BASICHTTP_STATE_DONE. */
   CdkBasicHttpFinishRequest(request, CDK_BASICHTTP_STATE_DONE);

   if (!request->peerCertificates) {
      CdkBasicHttpGetPeerCertificates(request);
   }

   curl_easy_getinfo(request->curl, CURLINFO_RESPONSE_CODE, &response->responseCode);
   LDEBUG(0, ("%s: The HTTP response code: %d.", __FUNCTION__, (int)response->responseCode));

   if (CURLE_OK == curl_easy_getinfo(request->curl, CURLINFO_EFFECTIVE_URL, &effectiveURL)) {
      response->effectiveURL = g_strdup(effectiveURL);
   }

   /* First, check for CdkBasicHttp-generated errors. */
   if (request->errorCode != CDK_BASICHTTP_ERROR_NONE) {
      LERROR(0, ("%s: Found an error (%u) in the response.", __FUNCTION__, request->errorCode));
      errorCode = request->errorCode;
      switch (request->errorCode) {
      case CDK_BASICHTTP_ERROR_CONNECT_TIMEDOUT:
         response->responseCode = CDK_BASICHTTP_RESPONSE_SERVICEUNAVAILABLE;
         break;
      case CDK_BASICHTTP_ERROR_INACTIVITY_TIMEDOUT:
         break;
      }
   } else {
      LDEBUG(0, ("%s: The CURL result of the response: %d.", __FUNCTION__, request->result));
      /* Map error codes. */
      switch (request->result) {
         /* 1:1 mappings. */
      case CURLE_OK:
         errorCode = CDK_BASICHTTP_ERROR_NONE;
         break;
      case CURLE_UNSUPPORTED_PROTOCOL:
         errorCode = CDK_BASICHTTP_ERROR_UNSUPPORTED_PROTOCOL;
         break;
      case CURLE_URL_MALFORMAT:
         errorCode = CDK_BASICHTTP_ERROR_URL_MALFORMAT;
         break;
      case CURLE_COULDNT_RESOLVE_PROXY:
         errorCode = CDK_BASICHTTP_ERROR_COULDNT_RESOLVE_PROXY;
         break;
      case CURLE_COULDNT_RESOLVE_HOST:
         errorCode = CDK_BASICHTTP_ERROR_COULDNT_RESOLVE_HOST;
         break;
      case CURLE_COULDNT_CONNECT:
         errorCode = CDK_BASICHTTP_ERROR_COULDNT_CONNECT;
         break;
      case CURLE_HTTP_RETURNED_ERROR:
         errorCode = CDK_BASICHTTP_ERROR_HTTP_RETURNED_ERROR;
         break;
      case CURLE_OPERATION_TIMEDOUT:
         errorCode = CDK_BASICHTTP_ERROR_OPERATION_TIMEDOUT;
         break;
      case CURLE_SSL_CONNECT_ERROR:
         errorCode = CDK_BASICHTTP_ERROR_SSL_CONNECT_ERROR;
         break;
      case CURLE_TOO_MANY_REDIRECTS:
         errorCode = CDK_BASICHTTP_ERROR_TOO_MANY_REDIRECTS;
         break;
         /* n:1 mappings */
      case CURLE_WRITE_ERROR:
      case CURLE_READ_ERROR:
      case CURLE_SEND_ERROR:
      case CURLE_RECV_ERROR:
         errorCode = CDK_BASICHTTP_ERROR_TRANSFER;
         break;
      case CURLE_SSL_ENGINE_NOTFOUND:
      case CURLE_SSL_ENGINE_SETFAILED:
      case CURLE_SSL_CERTPROBLEM:
      case CURLE_SSL_CIPHER:
      case CURLE_SSL_CACERT:
      case CURLE_SSL_ENGINE_INITFAILED:
      case CURLE_SSL_CACERT_BADFILE:
      case CURLE_SSL_SHUTDOWN_FAILED:
         errorCode = CDK_BASICHTTP_ERROR_SSL_SECURITY;
         break;
      default:
         errorCode = CDK_BASICHTTP_ERROR_GENERIC;
         break;
      }
   }
   response->errorCode = errorCode;

   response->contentLength = request->receiveBuf->len;
   response->content = (char *)g_malloc0(response->contentLength + 1);
   if (response->contentLength > 0) {
      memcpy(response->content, request->receiveBuf->str, response->contentLength);
   }
   response->content[response->contentLength] = '\0';

// Will remove this pre-define when updating traceVar
#ifndef METRO
   if (basicHttpTrace) {
      LDEBUG(0, (MODULE "RECEIVED RECEIVED RECEIVED RECEIVED RECEIVED RECEIVED"));
      LDEBUG(0, ("  Content-Length: %u.", (unsigned int)response->contentLength));
   }
#endif

   (request->onSentProc)(request, response, request->clientData);

   /*
    * Don't use request after this point. Let's assume request has
    * been deleted by the callback.
    */

   EXIT;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttpProcessCURLMulti --
 *
 *
 * Results:
 *       None.
 *
 * Side effects:
 *       Completion notifications are queued up to run asynchronously.
 *
 *-----------------------------------------------------------------------------
 */

static void
CdkBasicHttpProcessCURLMulti(void)
{
   ENTRY;

   while (TRUE) {
      CURLMsg *msg;
      int msgsLeft;

      msg = curl_multi_info_read(curlGlobalState->curlMulti, &msgsLeft);
      if (NULL == msg) {
         break;
      }

      if (CURLMSG_DONE == msg->msg) {
         CURL *curl;
         CURLcode curlCode;
         CdkBasicHttpRequest *request = NULL;
         char *curlPrivate;

         /*
          * Save state as msg is unavailable after _multi_remove_handle.
          */
         curl = msg->easy_handle;
         curlCode = msg->data.result;
         if (NULL != curl &&
             g_hash_table_lookup_extended(curlGlobalState->sessionHandles, curl, NULL, NULL)) {
            if (CURLM_OK == curl_multi_remove_handle(curlGlobalState->curlMulti, curl)) {
               g_hash_table_remove(curlGlobalState->sessionHandles, curl);
               LDEBUG(0, ("%s: Remove session handle from Curl: %p", __FUNCTION__, curl));
            }
         }

         /*
          * Curl requires a char* for CURLINFO_PRIVATE, even though
          * the data type is actually something else.
          */
         curl_easy_getinfo(curl, CURLINFO_PRIVATE, &curlPrivate);
         request = (CdkBasicHttpRequest *)curlPrivate;

         if (NULL != request) {
            g_assert(curl == request->curl);

            if (NULL != request->cookieJar) {
               curl_easy_setopt(request->curl, CURLOPT_SHARE, NULL);
            }

            /*
             * Store easy error code to handle later.
             */
            request->result = curlCode;

            /*
             * If the request is in a bandwidth group, remove from it.
             */
            if (NULL != request->bwGroup) {
               CdkBasicHttp_RemoveRequestFromBandwidthGroup(request->bwGroup, request);
            }

            /*
             * We are done. Invoke the callback function.
             */
            CdkBasicHttpInvokeCallback(request);
         }
      }
   }

   EXIT;

} // CdkBasicHttpProcessCURLMulti


/*
 *-----------------------------------------------------------------------------
 *
 *    CdkBasicHttpSocketPollIoCallback --
 *
 *       Adapter to CdkBasicHttpSocketPollCallback, for situations where a
 *       callback is used both with and without an associated device.
 *
 * Results:
 *       None.
 *
 * Side effects:
 *       None.
 *
 *-----------------------------------------------------------------------------
 */

static void
CdkBasicHttpSocketPollIoCallback(CdkFd fd,            /* IN */
                                 gpointer clientData) /* IN */
{
   ENTRY;

   CdkBasicHttpSocketPollCallback(clientData);

   EXIT;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttpSocketPollCallback --
 *
 *
 * Results:
 *    FALSE in all cases.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

static gboolean
CdkBasicHttpSocketPollCallback(gpointer clientData) /* IN */
{
   CurlSocketState *socketState;
   CURLMcode curlMErr;
   curl_socket_t sock = 0;
   gboolean isTimeout;

   ENTRY;

   isTimeout = (clientData == BASIC_HTTP_TIMEOUT_DATA);
   if (isTimeout) {
      clientData = NULL;
   }

   socketState = (CurlSocketState *)clientData;
   if (socketState) {
      sock = socketState->sock;
   }

   g_assert(NULL != curlGlobalState);
   while (TRUE) {
      int runningHandles = 0;

      if (isTimeout) {
         LDEBUG(0, ("%s: passing CURL_SOCKET_TIMEOUT to curl", __FUNCTION__));
         curlMErr =
            curl_multi_socket(curlGlobalState->curlMulti, CURL_SOCKET_TIMEOUT, &runningHandles);
      } else if (socketState) {
         curlMErr = curl_multi_socket(curlGlobalState->curlMulti, sock, &runningHandles);
      } else {
         /*
          * Before calling curl_multi_socket_all, we need to process all
          * the pending curl multi results. Otherwise, one curl connection
          * could be assigned to more than one curl easy handles.
          *
          * There's a bug(?) in cUrl implementation up to 7.16.0 in that
          * the connection is returned to pool as soon as the request
          * becomes COMPLETED. However, it's not removed from easy multi
          * handle until curl_multi_remove_handle is called. If curl_multi
          * _socket_all is called when this happens, the same connection
          * could be	assigned to 2 curl easy handles which would cause mess
          * later on.
          */
         CdkBasicHttpProcessCURLMulti();
         curlMErr = curl_multi_socket_all(curlGlobalState->curlMulti, &runningHandles);
      }

      if (CURLM_CALL_MULTI_PERFORM != curlMErr) {
         /*
          * A CURL internal bug causes returning CURLM_BAD_SOCKET before
          * a curl handle is able to transit to the final complete state.
          *
          * It is timing related and the chance is exactly 1%. When this
          * happens, we need to redrive the curl handle using the
          * curl_multi_socket_all API. Hence we set socketState to NULL
          *
          * Note redrive using curl_multi_socket will not work as it could
          * not find the removed socket in hash and returns CURLM_BAD_SOCKET
          * before get a chance to finish the final state transition.
          */
         if (CURLM_BAD_SOCKET == curlMErr) {
            socketState = NULL;
            continue;
         }

         g_assert(CURLM_OK == curlMErr);

         break;
      }
   }

   CdkBasicHttpProcessCURLMulti();

   while (g_queue_get_length(curlGlobalState->pending) > 0 &&
          g_hash_table_size(curlGlobalState->requests) < curlGlobalState->maxOutstandingRequests) {
      CdkBasicHttpRequest *request =
         (CdkBasicHttpRequest *)g_queue_pop_head(curlGlobalState->pending);
      CdkBasicHttpStartRequest(request);
   }

   RETURN(FALSE);
}


/*
 *-----------------------------------------------------------------------------
 *
 *  CdkBasicHttpConnectionTimeoutCallback --
 *
 *     Poll callback invoked when the connection timeout for a request expires.
 *     Checks the state of the socket associated with the request. If the
 *     socket is not in the connected state, fails the request.
 *
 * Results:
 *     FALSE in all cases.
 *
 * Side effects:
 *     None.
 *
 *-----------------------------------------------------------------------------
 */

gboolean
CdkBasicHttpConnectionTimeoutCallback(void *clientData) /* IN */
{
   CdkBasicHttpRequest *request = (CdkBasicHttpRequest *)clientData;

   ENTRY;

   g_return_val_if_fail(request, TRUE);
   g_return_val_if_fail(request->connectTimeoutSec, TRUE);

   CdkBasicHttpClearConnectionTimeout(request);

   /*
    * Since we set CURLOPT_FRESH_CONNECT for requests that specify a non-zero connection
    * timeout, request->socket should have a valid value.
    */
   if (0 > request->sock) {
      LINFO(0, (MODULE "Unknown socket for request: %p. "
                       "Ignoring connection timeout.",
                request));
      g_assert(CdkFd_IsValid(request->sock));
   }

   LINFO(0, (MODULE "Connection timed out. Request: %p. Socket: %d", request, request->sock));
   /*
    * Skip requests whose socket has already been removed.
    */
   if (NULL == CdkBasicHttpFindSocket(request->sock)) {
      LINFO(0, (MODULE "Could not find socket for request: %p CURL: %p. "
                       "Ignoring connection timeout.",
                request, request->curl));
      RETURN(FALSE);
   }

   if (CdkBasicHttpIsSocketConnected(request->sock)) {
      LINFO(0, (MODULE "Connection timed out. Cancelling request %p.", request));
      CdkBasicHttp_CancelRequest(request);
   }

   /* Invoke the caller's callback function with an error code. */
   request->errorCode = CDK_BASICHTTP_ERROR_CONNECT_TIMEDOUT;
   CdkBasicHttpInvokeCallback(request);
   /* Don't use request after invoking the callback as the caller may have freed it */

   RETURN(FALSE);
}


/*
 *-----------------------------------------------------------------------------
 *
 *  CdkBasicHttpStateToInactivityTimeout --
 *
 *     Get a pointer to the inactivity timeout corresponding to a state, if
 *     any.
 *
 * Results:
 *     NULL on failure.
 *
 * Side effects:
 *     None.
 *
 *-----------------------------------------------------------------------------
 */

unsigned long *
CdkBasicHttpStateToInactivityTimeout(CdkBasicHttpRequest *request, /* IN */
                                     CdkBasicHttpState state)      /* IN */
{
   unsigned long *timeoutSec = NULL;

   ENTRY;

   g_return_val_if_fail(request != NULL, NULL);

   /* Inactivity timeouts are only valid for the following states. */
   switch (state) {
   case CDK_BASICHTTP_STATE_SENDING:
      timeoutSec = &request->sendInactivityTimeoutSec;
      break;

   case CDK_BASICHTTP_STATE_WAITING:
      timeoutSec = &request->waitInactivityTimeoutSec;
      break;

   case CDK_BASICHTTP_STATE_RECEIVING:
      timeoutSec = &request->recvInactivityTimeoutSec;
      break;
   }

   RETURN(timeoutSec);
}


/*
 *-----------------------------------------------------------------------------
 *
 *  CdkBasicHttpGotActivity --
 *
 *     Update the time of the most recent activity.
 *
 * Results:
 *     None.
 *
 * Side effects:
 *     None.
 *
 *-----------------------------------------------------------------------------
 */

void
CdkBasicHttpGotActivity(CdkBasicHttpRequest *request) /* IN */
{
   ENTRY;

   g_return_if_fail(request != NULL);

   CdkBasicHttpGetTimeOfDay(&request->lastActivityTime);

   EXIT;
}


/*
 *-----------------------------------------------------------------------------
 *
 *  CdkBasicHttpInactivityTimeoutCallback --
 *
 *     Poll callback invoked periodically to check for request activity.
 *
 * Results:
 *     TRUE in all cases.
 *
 * Side effects:
 *     None.
 *
 *-----------------------------------------------------------------------------
 */

gboolean
CdkBasicHttpInactivityTimeoutCallback(void *clientData) /* IN */
{
   CdkBasicHttpRequest *request = (CdkBasicHttpRequest *)clientData;
   gint64 currentTime = 0;
   gint64 elapsed = 0;
   unsigned long *timeoutSec = NULL;

   ENTRY;

   g_assert(request);
   g_assert(request->sendInactivityTimeoutSec != 0 || request->waitInactivityTimeoutSec != 0 ||
            request->recvInactivityTimeoutSec != 0);

   timeoutSec = CdkBasicHttpStateToInactivityTimeout(request, request->state);
   g_assert(timeoutSec != NULL);

   /* If we're paused, inactivity timeout doesn't apply. */
   if (request->pausedMask & CURLPAUSE_RECV || request->pausedMask & CURLPAUSE_SEND) {
      GOTO(0, exit);
   }

   CdkBasicHttpGetTimeOfDay(&currentTime);
   elapsed = currentTime - request->lastActivityTime;
   if (*timeoutSec != 0 && elapsed >= *timeoutSec * 1000 * (gint64)1000) {
      LINFO(0, (MODULE "Request %p in state %u timed out after %.3f seconds having "
                       "sent %" G_GINT64_FORMAT "u/%" G_GINT64_FORMAT
                       "u and received %" G_GINT64_FORMAT "u/%" G_GINT64_FORMAT "i bytes. "
                       "Cancelling request.",
                request, request->state, (float)elapsed / (1000 * 1000),
                request->statistics[CDK_BASICHTTP_UPLOAD].transferredBytes,
                (guint64)CdkBasicHttpSourceLength(request->body),
                request->statistics[CDK_BASICHTTP_DOWNLOAD].transferredBytes,
                request->recvContentInfo.expectedLength));

      CdkBasicHttp_CancelRequest(request);

      /* Invoke the caller's callback. */
      request->errorCode = CDK_BASICHTTP_ERROR_INACTIVITY_TIMEDOUT;
      CdkBasicHttpInvokeCallback(request);
      /* Caller likely freed the request, so don't use it anymore. */
      request = NULL;
   }

exit:
   RETURN(TRUE); /* MUST return true because we are a periodic handler */
}


/*
 *-----------------------------------------------------------------------------
 *
 *  CdkBasicHttpInitCurlMulti --
 *
 *     Create and initialize the CURL multi instance and the corresponding
 *     action callbacks. If the failed, the curlGlobalState->curlMulti
 *     is NULL.
 *
 * Results:
 *     None.
 *
 * Side effects:
 *     None.
 *
 *-----------------------------------------------------------------------------
 */

void
CdkBasicHttpInitCurlMulti()
{
   ENTRY;

   curlGlobalState->curlMulti = curl_multi_init();

   if (!curlGlobalState->curlMulti) {
      EXIT;
   }

   curl_multi_setopt(curlGlobalState->curlMulti, CURLMOPT_SOCKETFUNCTION,
                     CdkBasicHttpSocketCurlCallback);
   curl_multi_setopt(curlGlobalState->curlMulti, CURLMOPT_SOCKETDATA, NULL);
   curl_multi_setopt(curlGlobalState->curlMulti, CURLMOPT_TIMERFUNCTION,
                     CdkBasicHttpTimerCurlCallback);
   curl_multi_setopt(curlGlobalState->curlMulti, CURLMOPT_TIMERDATA, NULL);
   /* do no more than 8 connections per host */
   curl_multi_setopt(curlGlobalState->curlMulti, CURLMOPT_MAX_HOST_CONNECTIONS, 8L);

   EXIT;
}


/*
 *-----------------------------------------------------------------------------
 *
 *  CdkBasicHttpInvokeCallback --
 *
 *    Checks if the caller has specified a callback function to be invoked when the
 *    request completes. Schedules this callback function to be invoked on the poll
 *    thread immediately.
 *
 * Results:
 *     None.
 *
 * Side effects:
 *     None.
 *
 *-----------------------------------------------------------------------------
 */

void
CdkBasicHttpInvokeCallback(CdkBasicHttpRequest *request) /* IN */
{
   ENTRY;

   g_return_if_fail(request);

   if (NULL != request->onSentProc) {
      request->completeRequestHandlerId =
         CdkMain_AddIdle(CdkBasicHttpCompleteRequestCallback, request);
   }

   EXIT;
}


/*
 *-----------------------------------------------------------------------------
 *
 *  CdkBasicHttpSetConnectionTimeout --
 *
 *     Checks if a connection timeout has been specified for
 *     this request. Adds a poll timer callback for the connection timeout if it is
 *     specified (a value of 0 means no timeout).
 *
 * Results:
 *     None.
 *
 * Side effects:
 *     None.
 *
 *-----------------------------------------------------------------------------
 */

void
CdkBasicHttpSetConnectionTimeout(CdkBasicHttpRequest *request) /* IN */
{
   ENTRY;

   g_return_if_fail(request != NULL);

   /*
    * Set a poll callback to be invoked 'connection timeout' seconds from now.
    */
   if (0 != request->connectTimeoutSec) { /* 0 -> no connection timeout */
      request->timeoutHandlerId = CdkMain_AddTimeoutSeconds(
         request->connectTimeoutSec, CdkBasicHttpConnectionTimeoutCallback, request);
      /*
       * In order to check if the socket is in the connected state or not when the
       * connection timeout expires, we have to force libCurl to make a new connection.
       * See comments in CdkBasicHttp_SetConnectionTimeout.
       */
      CdkBasicHttp_SetFreshConnection(request);
   }

   EXIT;
}


/*
 *-----------------------------------------------------------------------------
 *
 *  CdkBasicHttpClearConnectionTimeout --
 *
 *     Removes the connection timer callback.
 *
 * Results:
 *     None.
 *
 * Side effects:
 *     None.
 *
 *-----------------------------------------------------------------------------
 */

void
CdkBasicHttpClearConnectionTimeout(CdkBasicHttpRequest *request) /* IN */
{
   ENTRY;

   g_return_if_fail(request != NULL);

   /* Remove the timer callback for connection timeouts */
   if (request->connectTimeoutSec) {
      CDK_MAIN_REMOVE(request->timeoutHandlerId);
      request->connectTimeoutSec = 0;
   }

   EXIT;
}


/*
 *-----------------------------------------------------------------------------
 *
 *  CdkBasicHttpSetInactivityTimeout --
 *
 *     Adds a poll timer callback for the inactivity timeout if it is
 *     specified (a value of 0 means no timeout). The callback will
 *     periodically check for request activity.
 *
 * Results:
 *     None.
 *
 * Side effects:
 *     None.
 *
 *-----------------------------------------------------------------------------
 */

void
CdkBasicHttpSetInactivityTimeout(CdkBasicHttpRequest *request) /* IN */
{
   ENTRY;

   g_return_if_fail(request != NULL);

   /* Set a periodic poll callback. */
   if (request->sendInactivityTimeoutSec != 0 || request->waitInactivityTimeoutSec != 0 ||
       request->recvInactivityTimeoutSec != 0) {

      request->inactivityTimeoutHandlerId = CdkMain_AddTimeoutSeconds(
         BASIC_HTTP_ACTIVITY_CHECK_INTERVAL, CdkBasicHttpInactivityTimeoutCallback, request);
   }

   EXIT;
}


/*
 *-----------------------------------------------------------------------------
 *
 *  CdkBasicHttpClearInactivityTimeout --
 *
 *     Removes the inactivity timer callback.
 *
 * Results:
 *     None.
 *
 * Side effects:
 *     None.
 *
 *-----------------------------------------------------------------------------
 */

void
CdkBasicHttpClearInactivityTimeout(CdkBasicHttpRequest *request) /* IN */
{
   ENTRY;

   if (request->sendInactivityTimeoutSec != 0 || request->waitInactivityTimeoutSec != 0 ||
       request->recvInactivityTimeoutSec != 0) {
      CDK_MAIN_REMOVE(request->inactivityTimeoutHandlerId);
      request->sendInactivityTimeoutSec = 0;
      request->waitInactivityTimeoutSec = 0;
      request->recvInactivityTimeoutSec = 0;
   }

   EXIT;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttp_SetFreshConnection --
 *
 *      Causes CURLOPT_FRESH_CONNECT to be set when sending the request.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      "Make the next transfer use a new (fresh) connection by
 *      force. If the connection cache is full before this connection,
 *      one of the existing connections will be closed as according to
 *      the selected or default policy."
 *
 *-----------------------------------------------------------------------------
 */

void
CdkBasicHttp_SetFreshConnection(CdkBasicHttpRequest *request) /* IN */
{
   ENTRY;

   if (request) {
      request->useFreshConnection = TRUE;
   }

   EXIT;
}


/*
 *-----------------------------------------------------------------------------
 *
 *  CdkBasicHttpIsSocketConnected --
 *
 *     Checks if the given socket is in the connected state.
 *
 * Results:
 *     true if the socket is in the connected state, false otherwise.
 *
 * Side effects:
 *     None.
 *
 *-----------------------------------------------------------------------------
 */

gboolean
CdkBasicHttpIsSocketConnected(curl_socket_t sock) /* IN */
{
   gboolean ret = FALSE;
   struct sockaddr peerAddr = {0};
   socklen_t peerAddrLen = sizeof peerAddr;
   int error;

   ENTRY;
   /*
    * Use getpeername() as a platform-independent way to check
    * if the socket is connected.
    */
   ret = (0 == getpeername(sock, &peerAddr, &peerAddrLen));
   error = errno; /* Store errno in a local var so we don't lose its value */

#ifdef _WIN32
   if (WSAENOTCONN == WSAGetLastError()) {
      LINFO(0, (MODULE "Socket %d not connected.", sock));
   }
#else
   if (ENOTCONN == error) {
      LINFO(0, (MODULE "Socket %d not connected.", sock));
   }
#endif

   RETURN(ret);
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttp_BuildHeaders --
 *
 *     Build the array for common http headers.
 *
 * Results:
 *     A string array for http headers.
 *
 * Side effects:
 *     The caller should free the memory if no longer needed.
 *
 *-----------------------------------------------------------------------------
 */

char **
CdkBasicHttp_BuildHeaders(const char *acceptLanguage, /* IN */
                          const char *authorization,  /* IN */
                          const char *contentType)    /* IN */
{
   GPtrArray *headers;

   ENTRY;

   headers = g_ptr_array_new();
   if (!cdk_str_empty0(acceptLanguage)) {
      g_ptr_array_add(headers, g_strdup_printf("Accept-Language: %s", acceptLanguage));
   }
   if (!cdk_str_empty0(contentType)) {
      g_ptr_array_add(headers, g_strdup_printf("Content-Type: %s", contentType));
   }
   if (!cdk_str_empty0(authorization)) {
      g_ptr_array_add(headers, g_strdup_printf("Authorization: Bearer %s", authorization));
   }
   g_ptr_array_add(headers, NULL);

   RETURN((char **)g_ptr_array_free(headers, FALSE));
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttp_CreateRequest --
 *
 *
 * Results:
 *       None.
 *
 * Side effects:
 *
 *-----------------------------------------------------------------------------
 */

CdkBasicHttpRequest *
CdkBasicHttp_CreateRequest(CdkConnection *conn,              /* IN */
                           const char *url,                  /* IN */
                           CdkBasicHttpMethod httpMethod,    /* IN */
                           char *const *const headers,       /* IN */
                           CdkBasicHttpCookieJar *cookieJar, /* IN */
                           const char *body)                 /* IN */
{
   ENTRY;
   RETURN(CdkBasicHttp_CreateRequestWithSSL(conn, url, httpMethod, headers, cookieJar, body));
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttp_CreateRequestWithSSL --
 *
 *
 * Results:
 *       None.
 *
 * Side effects:
 *
 *-----------------------------------------------------------------------------
 */

CdkBasicHttpRequest *
CdkBasicHttp_CreateRequestWithSSL(CdkConnection *conn,              /* IN */
                                  const char *url,                  /* IN */
                                  CdkBasicHttpMethod httpMethod,    /* IN */
                                  char *const *headers,             /* IN */
                                  CdkBasicHttpCookieJar *cookieJar, /* IN */
                                  const char *body)                 /* IN */
{
   CdkBasicHttpSource *sourceBody = CdkBasicHttp_AllocStringSource(body);
   CdkBasicHttpRequest *ret = NULL;

   ENTRY;

   ret = CdkBasicHttp_CreateRequestEx(conn, url, httpMethod, headers, cookieJar, sourceBody, 0);
   /* Need CdkBasicHttp_FreeRequest to free sourceBody. */
   ret->ownBody = TRUE;

   RETURN(ret);
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttp_CreateRequestEx --
 *
 *
 * Results:
 *       None.
 *
 * Side effects:
 *
 *-----------------------------------------------------------------------------
 */

CdkBasicHttpRequest *
CdkBasicHttp_CreateRequestEx(CdkConnection *conn,              /* IN */
                             const char *url,                  /* IN */
                             CdkBasicHttpMethod httpMethod,    /* IN */
                             char *const *headers,             /* IN */
                             CdkBasicHttpCookieJar *cookieJar, /* IN */
                             CdkBasicHttpSource *body,         /* IN */
                             guint32 flags)                    /* IN */
{
   CdkBasicHttpRequest *request = NULL;
   char *proxy = NULL;

   ENTRY;

   g_return_val_if_fail(NULL != conn, NULL);
   g_return_val_if_fail(NULL != CdkConnection_GetUrl(conn), NULL);
   g_return_val_if_fail(httpMethod >= CDK_BASICHTTP_METHOD_GET, NULL);
   g_return_val_if_fail(httpMethod < CDK_BASICHTTP_METHOD_UNKNOWN, NULL);

   if (CDK_BASICHTTP_DEFAULT_COOKIEJAR == cookieJar) {
      if (NULL == defaultCookieJar) {
         defaultCookieJar = CdkBasicHttp_CreateCookieJar();
      }
      cookieJar = defaultCookieJar;
   }

   request = (CdkBasicHttpRequest *)g_malloc0(sizeof *request);
   request->connection = conn;
   request->url = g_strdup(url);
   request->httpMethod = httpMethod;
   request->flags = flags;
   request->cookieJar = cookieJar;

   if (headers) {
      int i;
      for (i = 0; headers[i]; i++) {
         CdkBasicHttp_AppendRequestHeader(request, headers[i]);
      }
   }

   request->state = CDK_BASICHTTP_STATE_NOT_STARTED;
   request->body = body;
   request->receiveBuf = g_string_new(NULL);
   request->recvContentInfo.totalSize = CDK_BASICHTTP_UNKNOWN_SIZE;
   request->recvContentInfo.expectedLength = CDK_BASICHTTP_UNKNOWN_SIZE;
   request->recvContentInfo.rangeStart = 0;
   request->recvContentInfo.rangeEnd = CDK_BASICHTTP_UNKNOWN_SIZE;
   request->pausedMask = 0;
   request->authType = CDK_BASICHTTP_AUTHENTICATION_NONE;
   request->userNameAndPassword = NULL;
   request->userAgent = NULL;

#ifndef METRO
   proxy = getenv(VMWARE_HTTPSPROXY);
#endif

   CdkBasicHttp_SetProxy(request, proxy,
                         proxy ? CDK_BASICHTTP_PROXY_HTTP : CDK_BASICHTTP_PROXY_NONE);

   request->connectTimeoutSec = 0ul; // 0 -> no timeout

   /*
    * Since on Linux, libcurl could not detect the disconnected network, as a
    * workaround, we have to set the inactivity timeout when the request in
    * sending. if the timeout occurs, the request will be canceled. Without the
    * timeout, the request will hang in libcurl for a long time. Please see bug
    * 2996279.
    *
    * However the timeout may cause error to be promtped to end-users when some
    * unimportant xml message sending times out, especially on IOS client, it
    * cause client to disconnect from server, please see bug 3095835.
    */
   request->sendInactivityTimeoutSec = 0;

   request->waitInactivityTimeoutSec = 0ul; // 0 -> no timeout
   request->recvInactivityTimeoutSec = 0ul; // 0 -> no timeout
   request->lastActivityTime = 0;
   request->sock = CURL_SOCKET_BAD;
   request->errorCode = CDK_BASICHTTP_ERROR_NONE;
   request->connectTo = NULL;
   request->addResolveEntry = NULL;
   request->removeResolveEntry = NULL;

   RETURN(request);
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttp_AppendRequestHeader --
 *
 *       Append to the request header.
 *
 * Results:
 *       gbooleanean - TRUE on success, FALSE on failure.
 *
 * Side effects:
 *       On success, the header list returned will contain the header passed
 *       in, in addition to any previously appended headers.
 *       On failure, the entire header list will be retained, but the request
 *       will not succeed as the caller intends, and so, should be aborted.
 *
 *-----------------------------------------------------------------------------
 */

gboolean
CdkBasicHttp_AppendRequestHeader(CdkBasicHttpRequest *request, /* IN */
                                 const char *header)           /* IN */
{
   struct curl_slist *newList = NULL;

   ENTRY;

   if (!header || !request) {
      GOTO(0, exit);
   }

   newList = curl_slist_append(request->headerList, header);

   /*
    * If the above call succeeded, save the result header list.
    * If the above call failed, the previous header list is unchanged.
    */
   if (newList) {
      request->headerList = newList;
   } else {
      LERROR(0, (MODULE "AppendRequestHeader failed to append to the "
                        "request header. Insufficient memory."));
   }

exit:
   /*
    * Return the result. The result will be TRUE, successful, only if the
    * parameters passed in were valid and the new header was successfully
    * appended to the header list.
    */
   RETURN(newList != NULL);
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttp_AppendRangeRequestHeader --
 *
 *       Append "Range: bytes=<start>-<end>\r\n" to the request header.
 *
 * Results:
 *       gbooleanean - TRUE on success, FALSE on failure.
 *
 * Side effects:
 *       This will affect the range of the content processed by the request.
 *
 *-----------------------------------------------------------------------------
 */

gboolean
CdkBasicHttp_AppendRangeRequestHeader(CdkBasicHttpRequest *request, /* IN */
                                      gint64 start,                 /* IN */
                                      gint64 size)                  /* IN (OPT) */
{
   char temp[65];
   gint64 end = start + size - 1;
   int shouldAlwaysWork;
   gboolean rslt = FALSE;

   ENTRY;

   if (size > 0) {
      shouldAlwaysWork = g_snprintf(
         temp, 64, "Range:bytes=%" G_GINT64_FORMAT "d-%" G_GINT64_FORMAT "d", start, end);
   } else {
      shouldAlwaysWork = g_snprintf(temp, 64, "Range:bytes=%" G_GINT64_FORMAT "d-", start);
   }

   g_assert(shouldAlwaysWork >= 0);
   if (shouldAlwaysWork < 0) {
      LERROR(0, (MODULE "Formatting Range request header failed. Not expected."));
      GOTO(0, exit);
   }

   rslt = CdkBasicHttp_AppendRequestHeader(request, temp);
   if (!rslt) {
      LERROR(0, (MODULE "AppendRequestHeader failed. Not expected."));
   }

exit:
   RETURN(rslt);
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttp_SetRequestNameAndPassword --
 *
 *
 * Results:
 *       None.
 *
 * Side effects:
 *
 *-----------------------------------------------------------------------------
 */

void
CdkBasicHttp_SetRequestNameAndPassword(CdkBasicHttpRequest *request, /* IN */
                                       int authenticationType,       /* IN */
                                       const char *userName,         /* IN */
                                       const char *userPassword)     /* IN */
{
   ENTRY;

   g_return_if_fail(NULL == request);
   g_return_if_fail(authenticationType < CDK_BASICHTTP_AUTHENTICATION_NONE);

   request->authType = authenticationType;

   g_free(request->userNameAndPassword);
   request->userNameAndPassword = NULL;
   if ((NULL != userName) && (NULL != userPassword)) {
      request->userNameAndPassword = g_strdup_printf("%s:%s", userName, userPassword);
   }

   EXIT;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttp_SetUserAgent --
 *
 *       Sets the userAgent string for the HTTP request.
 *
 *
 * Results:
 *       None.
 *
 * Side effects:
 *       None.
 *
 *-----------------------------------------------------------------------------
 */

void
CdkBasicHttp_SetUserAgent(CdkBasicHttpRequest *request, /* IN */
                          const char *userAgent)        /* IN: New UserAgent string */
{
   ENTRY;

   g_return_if_fail(request != NULL);

   g_free(request->userAgent);
   request->userAgent = g_strdup(userAgent);

   EXIT;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttp_SetProxy --
 *
 *      Sets the proxy string for the HTTP request.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

void
CdkBasicHttp_SetProxy(CdkBasicHttpRequest *request,    /* IN */
                      const char *proxy,               /* IN */
                      CdkBasicHttpProxyType proxyType) /* IN */
{
   ENTRY;

   g_return_if_fail(request != NULL);
   if (proxyType != CDK_BASICHTTP_PROXY_NONE) {
      g_return_if_fail(proxy != NULL);
   }

   g_free(request->proxy);
   if (proxyType == CDK_BASICHTTP_PROXY_NONE) {
      /*
       * Setting the proxy string to "" (an empty string) will explicitly
       * disable the use of a proxy, even if there is an environment
       * variable set for it.
       *
       * NOTE: The value NULL means to accept curl's default behavior which
       * will search the *_proxy environment variables for one to use.
       */
      request->proxy = g_strdup("");
   } else {
      request->proxy = g_strdup(proxy);
   }
   request->proxyType = proxyType;

   EXIT;
}


/*
 *-----------------------------------------------------------------------------
 *
 *  CdkBasicHttp_SetConnectTimeout --
 *
 *     Sets the maximum time in seconds to allow connecting to the server to take.
 *     Once the connection has been made, this option is of no use.
 *     Set to 0 to disable connection timeout.
 *
 *     XXX: We would really like to use the CURLOPT_CONNECTTIMEOUT option to control
 *     the connection timeout. However, there is a bug (as of version 7.19.4 of libcurl)
 *     where this option is not respected when using the multi interface (as we do).
 *     So we add our own timeout instead. However, once the libcurl bug is fixed,
 *     we should use that instead.
 *
 * Results:
 *     None.
 *
 * Side effects:
 *     When a non-zero connection timeout is set, we have to force curl to create
 *     a new connection for this request (we set CURLOPT_FRESH_CONNECT for this request).
 *     Otherwise curl will keep the socket in the connected state so that it can be reused.
 *     If curl reuses the connection, we have no way of enforcing the connection timeout.
 *
 *-----------------------------------------------------------------------------
 */

void
CdkBasicHttp_SetConnectTimeout(CdkBasicHttpRequest *request, /* IN */
                               unsigned long seconds)        /* IN */
{
   ENTRY;

   g_return_if_fail(request != NULL);

   LDEBUG(0, ("%s: connectTimeoutSec set to %lu seconds", __FUNCTION__, seconds));
   request->connectTimeoutSec = seconds;

   EXIT;
}


/*
 *-----------------------------------------------------------------------------
 *
 *  CdkBasicHttp_SetInactivityTimeout --
 *
 *     Set the timeout in seconds for a request to be inactive before raising
 *     an error. Without using this, requests that are waiting for a response
 *     from a server that's no longer online could take a long time to time
 *     out.
 *
 * Results:
 *     None.
 *
 * Side effects:
 *     None.
 *
 *-----------------------------------------------------------------------------
 */

void
CdkBasicHttp_SetInactivityTimeout(CdkBasicHttpRequest *request, /* IN */
                                  CdkBasicHttpState state,      /* IN */
                                  unsigned long seconds)        /* IN */
{
   unsigned long *timeoutSec = NULL;

   ENTRY;

   g_return_if_fail(request);
   g_return_if_fail(request->state == CDK_BASICHTTP_STATE_NOT_STARTED);

   timeoutSec = CdkBasicHttpStateToInactivityTimeout(request, state);
   /*
    * Having a timeout doesn't make sense for all states. Hitting this g_assert
    * means the caller passed in a state which shouldn't have a timeout.
    */
   if (!timeoutSec) {
      g_assert_not_reached();
      EXIT;
   }

   *timeoutSec = seconds;

   EXIT;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttpSslCtxCb --
 *
 *      Callback from curl, after all of its ssl options have been
 *      set, before the connection has been made.  Pass the sslctx on
 *      to the caller, if it has set a callback.
 *
 * Results:
 *      OK
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

static CURLcode
CdkBasicHttpSslCtxCb(CURL *curl,   /* IN */
                     void *sslctx, /* IN */
                     void *parm)   /* IN */
{
   CdkBasicHttpRequest *request = parm;

   ENTRY;

   LDEBUG(0, ("Removing peer certificates for sock %d", (int)request->sock));
   g_hash_table_remove(curlGlobalState->peerCertCache, GINT_TO_POINTER(request->sock));
   if (request->sslCtxProc) {
      request->sslCtxProc(request, sslctx, request->clientData);
   }

   LDEBUG(0, ("Set SSL options before curl connection: Protocols"));
   CdkSsl_SetProtocolsInSSLContext(sslctx);

   LDEBUG(0, ("Set SSL options before curl connection: Cipher string"));
   if (!CdkSsl_SetCipherStringInSSLContext(sslctx)) {
      RETURN(CURLE_SSL_CIPHER);
   }

   LDEBUG(0, ("Set the supported signature algorithms for TLS v1.2"));
   const char *sigAlgs = CdkSsl_GetSignatureAlgorithms();
   if (NULL == sigAlgs && CdkSsl_GetConfSigAlgsForCert()) {
      /*
       * If signature algorithms isn't set by Client, we need to set the
       * predetermined algorithms for cert authentication so SHA256 is still
       * the default for TLSv1.2. This is for the consideration of backward
       * compatibility that the smart card middleware used by the customer may
       * not support SHA384 after NIAP feature adds support for SHA384. If the
       * customer wants to use SHA384, he must configure the signature
       * algorithm on Client.
       * In addition, to support smartcard & TLS1.2 on VADC. We must add more
       * signature algorithms extensions in Client Hello. If only using
       * "RSA+SHA256", the VADC SCHANNEL will reject the second SSL connection
       * Client Hello. The issue doesn't happen on a broker(Uses Java SSL).
       * Looks like this is a limitation of MS SCHANNEL.
       */
      sigAlgs = "RSA+SHA256:RSA+SHA1:ECDSA+SHA256:ECDSA+SHA1";
   }

   CdkSsl_SetSignatureAlgorithmsInSSLContext(sslctx, sigAlgs);
   /*
    * gConfSigAlgsForCert flag should not impact the other
    * SSL connetions. So we should clear the flag.
    */
   CdkSsl_SetConfSigAlgsForCert(FALSE);

   RETURN(CURLE_OK);
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttpStartRequest --
 *
 *
 * Results:
 *       A gboolean indicating whether the request has been successfully started
 *       or not.
 *
 * Side effects:
 *
 *-----------------------------------------------------------------------------
 */

static gboolean
CdkBasicHttpStartRequest(CdkBasicHttpRequest *request) /* IN */
{
   gboolean success = FALSE;
   CURLMcode curlMErr;
   char *locationIDCookie = NULL;
   char *cookieValue = NULL;

   unsigned long connectTimeoutSec = DEFAULT_CONNECT_TIMEOUT;
   CdkAddressType ipProtocol = CDK_ADDRESS_TYPE_IPV4;

   ENTRY;

#if TARGET_OS_IPHONE
   if (CdkConnection_IsBrokerUDPEnabled(request->connection) &&
       CdkConnection_GetUdpProxy(request->connection) != NULL && !CdkUtil_UDPProxyEnabled()) {
      CdkUtil_FreeUDPProxyInstance();
      CdkUtil_InitUDPProxy();
   }
#endif

   g_return_val_if_fail(NULL != request->connection, FALSE);
   g_return_val_if_fail(NULL != request->url, FALSE);
   g_return_val_if_fail(request->state == CDK_BASICHTTP_STATE_NOT_STARTED, FALSE);
   request->state = CDK_BASICHTTP_STATE_SENDING;

   request->curl = curl_easy_init();
   if (NULL == request->curl) {
      GOTO(0, abort);
   }

#ifdef HAVE_CURLOPT_ADDRESS_SCOPE
   if (CdkConnection_GetInterface(request->connection)) {
      curl_easy_setopt(request->curl, CURLOPT_INTERFACE,
                       CdkConnection_GetInterface(request->connection));
   } else if (CdkConnection_GetScope(request->connection) != -1) {
      curl_easy_setopt(request->curl, CURLOPT_ADDRESS_SCOPE,
                       CdkConnection_GetScope(request->connection));
   }
#endif

#if defined USE_UDP_PROXY
   if (!CdkBasicHttpSetUdpProxyInfo(request, CdkConnection_GetHostname(request->connection),
                                    CdkConnection_GetPort(request->connection))) {
      GOTO(0, abort);
   }
#endif

   /*
    * Providing a DNS resolve entry for the hostname if
    * UDPProxyPeerReachabilityCb provide a selected IP.
    */
   CdkBasicHttpAddDNSResolveEntry(request);

   curl_easy_setopt(request->curl, CURLOPT_URL, request->url);

   connectTimeoutSec =
      request->connectTimeoutSec ? request->connectTimeoutSec : DEFAULT_CONNECT_TIMEOUT;

   if (request->useFreshConnection) {
      curl_easy_setopt(request->curl, CURLOPT_FRESH_CONNECT, 1l);
      CdkBasicHttp_SetConnectTimeout(request, connectTimeoutSec);

      /* This option is only intended for the next connection. */
      request->useFreshConnection = FALSE;
   }

   /*
    * Note: We always set CURLOPT_SSL_VERIFYPEER to 0 and
    * CURLOPT_SSL_VERIFYHOST to 0. This is because we have our own custom
    * SSL certificate verification functionality, which is supplied by using
    * the CURLOPT_SSL_CTX_FUNCTION option. When evaluating an SSL certificate,
    * our function CdkRpc_SslCtxProc is triggered, which then sets:
    *    ctx->client_cert_cb = CdkRpc_ClientCertCb;
    *    ctx->verify_mode = SSL_VERIFY_PEER;
    *    ctx->app_verify_callback = CdkRpc_VerifyCb;
    *    ctx->app_verify_arg = request;
    * This then causes our function CdkRpc_VerifyCb to always be triggered and
    * then we apply our custom certificate verification logic based on the
    * certificate checking mode.
    */
   curl_easy_setopt(request->curl, CURLOPT_SSL_VERIFYPEER, (long)0);
   curl_easy_setopt(request->curl, CURLOPT_SSL_VERIFYHOST, (long)0);

   curl_easy_setopt(request->curl, CURLOPT_SSL_CTX_FUNCTION, CdkBasicHttpSslCtxCb);
   curl_easy_setopt(request->curl, CURLOPT_SSL_CTX_DATA, request);

   curl_easy_setopt(request->curl, CURLOPT_FOLLOWLOCATION, (long)1);
   curl_easy_setopt(request->curl, CURLOPT_POST301, (long)1);
   curl_easy_setopt(request->curl, CURLOPT_NOSIGNAL, (long)1);

   /*
    * If the preferred address is not NULL, it will be added into DNS cache.
    * Because the address already is the desired protocol version, it is not
    * needed to restrict the libcurl anymore. It will avoid the issue when the
    * uwpdproxy is using for IPv6 FQDN. The udpproxy is always listening
    * on 127.0.0.1. See bug #2781467.
    */
   if (CdkConnection_GetPreferredAddress(request->connection)) {
      LDEBUG(0, (MODULE "Server IP is not NULL, no need to restrict IP version."));
      curl_easy_setopt(request->curl, CURLOPT_IPRESOLVE, (long)CURL_IPRESOLVE_WHATEVER);
   } else {
      /*
       * Forces curl to ipv4 resolution if the addressing mode is IPv4-Only, while
       * ipv6 resolution for IPv6-Only mode, if client support IPv4 and IPv6 at the
       * same time, set option 'CURLOPT_IPRESOLVE' to 'CURL_IPRESOLVE_WHATEVER';
       * By default, 'CURL_IPRESOLVE_WHATEVER' will be used by curl to resolve
       * addresses to all IP versions that your system allows.
       */
      ipProtocol = CdkUtil_GetIpProtocolUsage();
      LDEBUG(0, ("Set IP resolve type before curl connection: %d", (int)ipProtocol));
      if (ipProtocol == CDK_ADDRESS_TYPE_IPV4) {
         curl_easy_setopt(request->curl, CURLOPT_IPRESOLVE, (long)CURL_IPRESOLVE_V4);
      } else if (ipProtocol == CDK_ADDRESS_TYPE_IPV6) {
         curl_easy_setopt(request->curl, CURLOPT_IPRESOLVE, (long)CURL_IPRESOLVE_V6);
      } else {
         curl_easy_setopt(request->curl, CURLOPT_IPRESOLVE, (long)CURL_IPRESOLVE_WHATEVER);
      }
   }

   /*
    * XXX: As of version 7.19.4 of curl, the CURLOPT_CONNECTTIMEOUT option is
    *      not respected when using the multi interface.  So we set our own
    *      connection timeout timer above for a fresh connection.
    *      See the comment in CdkBasicHttpOpenSocketCurlCallback.
    *
    * XXX: Since curl does not provide a separate error message for connect
    *      timeouts vs generic timeouts and our own connection timeout does,
    *      it's better for ours to provide the error message.  Therefore,
    *      a minimum of 10 seconds is added to CURLOPT_CONNECTTIMEOUT so
    *      that our timeout will (hopefully always) fire first.
    */
   curl_easy_setopt(request->curl, CURLOPT_CONNECTTIMEOUT,
                    connectTimeoutSec ? MAX(connectTimeoutSec + 10, MIN_CURL_CONNECT_TIMEOUT) : 0);

#ifdef _WIN32
   /*
    * Set a dummy random file, this is pretty much a no-op in libcurl
    * however, it triggers the libcurl to check if the random seed has enough
    * entropy and skips a lengthy rand_screen() if that is the case.
    */
   curl_easy_setopt(request->curl, CURLOPT_RANDOM_FILE, "");
#endif
   curl_easy_setopt(request->curl, CURLOPT_SOCKOPTFUNCTION, CdkBasicHttpSetSockOpts);
   curl_easy_setopt(request->curl, CURLOPT_SOCKOPTDATA, request);

   curl_easy_setopt(request->curl, CURLOPT_OPENSOCKETFUNCTION, CdkBasicHttpOpenSocketCurlCallback);
   curl_easy_setopt(request->curl, CURLOPT_OPENSOCKETDATA, request);

   if (request->flags & CDK_BASICHTTP_PROXY_TUNNEL) {
      curl_easy_setopt(request->curl, CURLOPT_HTTPPROXYTUNNEL, (long)1);
   }

   if ((CDK_BASICHTTP_AUTHENTICATION_NONE != request->authType) &&
       (NULL != request->userNameAndPassword)) {
      curl_easy_setopt(request->curl, CURLOPT_USERPWD, request->userNameAndPassword);
      switch (request->authType) {
      case CDK_BASICHTTP_AUTHENTICATION_BASIC:
         curl_easy_setopt(request->curl, CURLOPT_HTTPAUTH, (long)CURLAUTH_BASIC);
         break;
      case CDK_BASICHTTP_AUTHENTICATION_DIGEST:
         curl_easy_setopt(request->curl, CURLOPT_HTTPAUTH, (long)CURLAUTH_DIGEST);
         break;
      case CDK_BASICHTTP_AUTHENTICATION_NTLM:
         curl_easy_setopt(request->curl, CURLOPT_PROXYAUTH, (long)CURLAUTH_NTLM);
         break;
      case CDK_BASICHTTP_AUTHENTICATION_ANY:
      default:
         curl_easy_setopt(request->curl, CURLOPT_PROXYAUTH, (long)CURLAUTH_ANY);
         break;
      }
   } // Set the username/password.

   curl_easy_setopt(request->curl, CURLOPT_USERAGENT,
                    request->userAgent ? request->userAgent : defaultUserAgent);

   cookieValue = CdkClientInfo_GetLocationID();
   if (cookieValue) {
#if defined(VMX86_DEBUG) || !(TARGET_OS_IPHONE || defined(__ANDROID__))
      LDEBUG(0, ("location.id:%s", cookieValue));
#endif

      locationIDCookie = g_strdup_printf("com.omnissa.vdi.broker.location.id=%s", cookieValue);
      curl_easy_setopt(request->curl, CURLOPT_COOKIE, locationIDCookie);

      g_free(locationIDCookie);
      g_free(cookieValue);
   }

   if (NULL == request->cookieJar) {
      curl_easy_setopt(request->curl, CURLOPT_COOKIEFILE, "");
   } else {
      if (request->cookieJar->newSession) {
         curl_easy_setopt(request->curl, CURLOPT_COOKIESESSION, (long)1);
         request->cookieJar->newSession = FALSE;
      }
      if (NULL != request->cookieJar->curlShare) {
         curl_easy_setopt(request->curl, CURLOPT_SHARE, request->cookieJar->curlShare);
         curl_easy_setopt(request->curl, CURLOPT_COOKIEFILE, "");
      } else if (NULL != request->cookieJar->cookieFile) {
         curl_easy_setopt(request->curl, CURLOPT_COOKIEFILE, request->cookieJar->cookieFile);
         curl_easy_setopt(request->curl, CURLOPT_COOKIEJAR, request->cookieJar->cookieFile);
      } else {
         g_assert_not_reached();
      }

      /*
       * Curl can be so insane sometimes. You can share a cookie jar but you can't put
       * anything into it until you have an actual easy handle. So we have to store the
       * initial cookie until the first handle comes along, and then set it then.
       */
      if (NULL != request->cookieJar->initialCookie) {
         curl_easy_setopt(request->curl, CURLOPT_COOKIELIST, request->cookieJar->initialCookie);
         g_free(request->cookieJar->initialCookie);
         request->cookieJar->initialCookie = NULL;
      }
   }

   switch (request->proxyType) {
   case CDK_BASICHTTP_PROXY_NONE:
      break;
   case CDK_BASICHTTP_PROXY_HTTP:
      curl_easy_setopt(request->curl, CURLOPT_PROXYTYPE, CURLPROXY_HTTP);
      if (!(request->flags & CDK_BASICHTTP_PROXY_TUNNEL)) {
         /*
          * A bug in libcurl makes non-tunnel http proxy connections be reused
          * regardless of their remote hosts/ports. Some proxies such as
          * tinyproxy relay the communication on per-connection basis thus
          * will forward to the wrong host. CURLOPT_FRESH_CONNECT will force
          * libcurl to reconnect to the proxy instead of reusing the pooled
          * connections.
          *
          * This is the temporary workaround util libcurl is patched for
          * the bug in url.c: ConnectionExists function.
          */
         curl_easy_setopt(request->curl, CURLOPT_FRESH_CONNECT, 1l);
      }
      break;
   case CDK_BASICHTTP_PROXY_SOCKS4:
      curl_easy_setopt(request->curl, CURLOPT_PROXYTYPE, CURLPROXY_SOCKS4);
      break;
   default:
      g_assert_not_reached();
      break;
   }
   if (request->proxy != NULL) {
      curl_easy_setopt(request->curl, CURLOPT_PROXY, request->proxy);
#ifdef _WIN32
      char *proxyUserPwd = g_strdup_printf("%s:%s", "", "");
      curl_easy_setopt(request->curl, CURLOPT_PROXYUSERPWD, proxyUserPwd);
      curl_easy_setopt(request->curl, CURLOPT_PROXYAUTH, (long)CURLAUTH_ANYSAFE);
      g_free(proxyUserPwd);
#endif
   }

   if (basicHttpTrace) {
      curl_easy_setopt(request->curl, CURLOPT_VERBOSE, (long)1);
      curl_easy_setopt(request->curl, CURLOPT_DEBUGFUNCTION, CdkBasicHttpDebugCurlCallback);
      curl_easy_setopt(request->curl, CURLOPT_DEBUGDATA, request);
      if (CdkDebug_IsAllLogEnabled()) {
         curl_global_trace("all");
      }
   }

   switch (request->httpMethod) {
   case CDK_BASICHTTP_METHOD_GET:
      curl_easy_setopt(request->curl, CURLOPT_HTTPGET, (long)1);
      break;

   case CDK_BASICHTTP_METHOD_POST:
      curl_easy_setopt(request->curl, CURLOPT_POST, (long)1);
      /* Refer to bug 376040 before changing this to CURLOPT_POSTFIELDSIZE_LARGE. */
      curl_easy_setopt(request->curl, CURLOPT_POSTFIELDSIZE,
                       (long)CdkBasicHttpSourceLength(request->body));
      break;

   case CDK_BASICHTTP_METHOD_PUT:
      curl_easy_setopt(request->curl, CURLOPT_PUT, (long)1);
      break;

   case CDK_BASICHTTP_METHOD_DELETE:
      curl_easy_setopt(request->curl, CURLOPT_CUSTOMREQUEST, "DELETE");
      break;

   case CDK_BASICHTTP_METHOD_HEAD:
   default:
      // TODO: add later
      GOTO(0, abort);
   }

   if (NULL != request->headerList) {
      curl_easy_setopt(request->curl, CURLOPT_HTTPHEADER, request->headerList);
   }

   curl_easy_setopt(request->curl, CURLOPT_HEADERFUNCTION, CdkBasicHttpHeaderCallback);
   curl_easy_setopt(request->curl, CURLOPT_WRITEHEADER, request);

   curl_easy_setopt(request->curl, CURLOPT_READFUNCTION, CdkBasicHttpReadCallback);
   curl_easy_setopt(request->curl, CURLOPT_READDATA, request);

   curl_easy_setopt(request->curl, CURLOPT_WRITEFUNCTION, CdkBasicHttpWriteCallback);
   curl_easy_setopt(request->curl, CURLOPT_WRITEDATA, request);

   curl_easy_setopt(request->curl, CURLOPT_IOCTLFUNCTION, CdkBasicHttpIoctlCallback);
   curl_easy_setopt(request->curl, CURLOPT_IOCTLDATA, request);

#ifdef HAVE_CURLOPT_CERTINFO
   curl_easy_setopt(request->curl, CURLOPT_CERTINFO, 1L);
#endif

   curl_easy_setopt(request->curl, CURLOPT_PRIVATE, request);

   CdkBasicHttpSetConnectionTimeout(request);
   CdkBasicHttpGotActivity(request);
   CdkBasicHttpSetInactivityTimeout(request);

   g_hash_table_insert(curlGlobalState->requests, (void *)request, NULL);
   curlMErr = curl_multi_add_handle(curlGlobalState->curlMulti, request->curl);
   if (CURLM_OK != curlMErr) {
      GOTO(0, abort);
   } else {
      g_hash_table_insert(curlGlobalState->sessionHandles, request->curl, NULL);
      LDEBUG(0, ("%s: Add session handle in Curl: %p", __FUNCTION__, request->curl));
   }
// Will remove this pre-define when updating traceVar
#ifndef METRO
   if (basicHttpTrace) {
      LDEBUG(0, (MODULE "SENDING SENDING SENDING SENDING SENDING SENDING"));
      LDEBUG(0, ("  URL: %s", request->url));
   }
#endif

   CdkBasicHttpSocketPollCallback(NULL);

   success = TRUE;

abort:
   if (!success) {
      /*
       * If we didn't succeed, the callback won't be invoked, so we have to
       * clean up here. We also need to revert to CDK_BASICHTTP_STATE_NOT_STARTED.
       */
      CdkBasicHttpFinishRequest(request, CDK_BASICHTTP_STATE_NOT_STARTED);
   }
   RETURN(success);
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttpFinishRequest --
 *
 *       Clean up things created in CdkBasicHttpStartRequest and move to a
 *       finished state. This should be safe to call multiple times on one
 *       request.
 *
 * Results:
 *       None.
 *
 * Side effects:
 *
 *-----------------------------------------------------------------------------
 */

void
CdkBasicHttpFinishRequest(CdkBasicHttpRequest *request,  /* IN */
                          CdkBasicHttpState finishState) /* IN */
{
   ENTRY;

   g_return_if_fail(request);

   switch (finishState) {
   case CDK_BASICHTTP_STATE_NOT_STARTED:
      /* We finished because we failed to start. */
      break;

   case CDK_BASICHTTP_STATE_DONE:
      /* We finished by succeeding or failing. */
      break;

   default:
      /* Only the above states are valid finish states. */
      g_assert_not_reached();
   }

   request->state = finishState;

   /*
    * Warning: Make sure anything done in here can be safely done multiple
    * times on the same request.
    */

   CDK_MAIN_REMOVE(request->completeRequestHandlerId);

   CdkBasicHttpClearConnectionTimeout(request);
   CdkBasicHttpClearInactivityTimeout(request);

   EXIT;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttp_SendRequestEx --
 *
 *       The callback function onSentProc will be responsible for
 *       deleteing request and response.
 *
 * Results:
 *       Returns TRUE on success, FALSE on failure.
 *
 * Side effects:
 *
 *-----------------------------------------------------------------------------
 */

gboolean
CdkBasicHttp_SendRequestEx(CdkBasicHttpRequest *request,               /* IN */
                           CdkBasicHttpOptions options,                /* IN */
                           CdkBasicHttpProgressProc *sendProgressProc, /* IN/OPT */
                           CdkBasicHttpProgressProc *recvProgressProc, /* IN/OPT */
                           CdkBasicHttpOnSentProc *onSentProc,         /* IN */
                           void *clientData)                           /* IN */
{
   gboolean success = TRUE;

   ENTRY;

   g_assert(NULL != curlGlobalState);

   g_return_val_if_fail(NULL != request, FALSE);
   g_return_val_if_fail(NULL != onSentProc, FALSE);
   g_return_val_if_fail(request->state == CDK_BASICHTTP_STATE_NOT_STARTED, FALSE);
   g_return_val_if_fail(NULL == request->curl, FALSE);

   request->options = options;
   request->sendProgressProc = sendProgressProc;
   request->recvProgressProc = recvProgressProc;
   request->onSentProc = onSentProc;
   request->clientData = clientData;

   CdkBasicHttpGetTimeOfDay(&request->lastProgressCallbackTime);

   if (g_hash_table_size(curlGlobalState->requests) >= curlGlobalState->maxOutstandingRequests) {
      // Queue up request.
      g_queue_push_tail(curlGlobalState->pending, request);
   } else {
      success = CdkBasicHttpStartRequest(request);
   }

   RETURN(success);
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttp_SendRequest --
 *
 *       The callback function onSentProc will be responsible for
 *       deleteing request and response.
 *
 * Results:
 *       Returns TRUE on success, FALSE on failure.
 *
 * Side effects:
 *
 *-----------------------------------------------------------------------------
 */

gboolean
CdkBasicHttp_SendRequest(CdkBasicHttpRequest *request,       /* IN */
                         CdkBasicHttpOnSentProc *onSentProc, /* IN */
                         void *clientData)                   /* IN/OPT */
{
   ENTRY;

   RETURN(CdkBasicHttp_SendRequestEx(request, 0, NULL, NULL, onSentProc, clientData));
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttp_GetState --
 *
 *       Get the state of a request.
 *
 * Results:
 *       Returns TRUE on success, FALSE on failure.
 *
 * Side effects:
 *
 *-----------------------------------------------------------------------------
 */

gboolean
CdkBasicHttp_GetState(CdkBasicHttpRequest *request, /* IN */
                      CdkBasicHttpState *state)     /* OUT */
{
   gboolean success = FALSE;

   ENTRY;

   if (!request || !state) {
      GOTO(0, exit);
   }

   success = TRUE;

exit:
   if (success) {
      *state = request->state;
   }
   RETURN(success);
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttp_GetRequestUrl --
 *
 *       Get the url of the request.
 *
 * Results:
 *       The url of the request.
 *
 * Side effects:
 *       None.
 *
 *-----------------------------------------------------------------------------
 */

const char *
CdkBasicHttp_GetRequestUrl(CdkBasicHttpRequest *request) /* IN */
{
   ENTRY;
   g_return_val_if_fail(request, NULL);
   RETURN(request->url);
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttpPauseRequest --
 *
 *       Internal pause or resume the request.
 *
 * Results:
 *       Returns TRUE on success, FALSE on failure.
 *
 * Side effects:
 *       The desired callback function will not be called until unpaused.
 *
 *-----------------------------------------------------------------------------
 */

static gboolean
CdkBasicHttpPauseRequest(CdkBasicHttpRequest *request, /* IN */
                         int mask,                     /* IN */
                         gboolean pause)               /* IN */
{
   CURLcode rslt = CURLE_OK;
   int oldPausedMask = 0;

   ENTRY;

   if (NULL == request) {
      RETURN(FALSE);
   }

   /*
    * Remove the possible scheduled callback for bandwidth control.
    */
   CdkBasicHttpRemoveResumePollCallback(request);

   if (NULL != request->curl) {
      /*
       * Pre-emptively set the new pausedMask, which we'll roll back if
       * curl_easy_pause fails. This is necessary because curl_easy_pause
       * may call the read/write callbacks (CdkBasicHttpReadCallback and
       * CdkBasicHttpWriteCallback). Either of those may update pausedMask,
       * so updating pausedMask here AFTER calling curl_easy_pause would
       * overwrite those updates and cause our pause state to be out of
       * sync with curl's.
       */
      oldPausedMask = request->pausedMask;
      request->pausedMask = mask;

      rslt = curl_easy_pause(request->curl, mask);
      if (rslt == CURLE_OK) {
         if (!pause) {
            /*
             * Only call CdkBasicHttpSocketPollCallback if this is unpause.
             * Calling this function after pause seems to trigger a bug
             * in libcurl (7.19.4).
             *
             * The reason to call this function is after unpause, the
             * socket is still in its signalled state so no other socket
             * poll callback will be fired. Thus we need to call this
             * poll callback ourselves to jump start the action.
             */
            CdkBasicHttpSocketPollCallback(NULL);
         }
      } else {
         /* Roll back pausedMask since we failed. */
         request->pausedMask = oldPausedMask;
      }
   }

   /* Treat changed pause state as request activity. */
   CdkBasicHttpGotActivity(request);

   RETURN(rslt == CURLE_OK);
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttp_PauseRecvRequest --
 *
 *       Pause or resume the request. Success does not guarantee that the
 *       request is in the desired state upon returning. Use CdkBasicHttp_IsPaused
 *       to query the paused state.
 *
 * Results:
 *       Returns TRUE on success, FALSE on failure.
 *
 * Side effects:
 *       The write callback function will not be called until unpaused.
 *
 *-----------------------------------------------------------------------------
 */

gboolean
CdkBasicHttp_PauseRecvRequest(CdkBasicHttpRequest *request, /* IN */
                              gboolean pause)               /* IN */
{
   ENTRY;

   if (NULL != request && NULL != request->curl) {
      int mask =
         pause ? request->pausedMask | CURLPAUSE_RECV : request->pausedMask & (~CURLPAUSE_RECV);
      RETURN(CdkBasicHttpPauseRequest(request, mask, pause));
   }
   RETURN(FALSE);
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttp_PauseSendRequest --
 *
 *       Pause or resume the send request. Success does not guarantee that the
 *       request is in the desired state upon returning. Use CdkBasicHttp_IsPaused
 *       to query the paused state.
 *
 * Results:
 *       Returns TRUE on success, FALSE on failure.
 *
 * Side effects:
 *       The read callback function will not be called until unpaused.
 *
 *-----------------------------------------------------------------------------
 */

gboolean
CdkBasicHttp_PauseSendRequest(CdkBasicHttpRequest *request, /* IN */
                              gboolean pause)               /* IN */
{
   ENTRY;

   if (NULL != request && NULL != request->curl) {
      int mask =
         pause ? request->pausedMask | CURLPAUSE_SEND : request->pausedMask & (~CURLPAUSE_SEND);
      RETURN(CdkBasicHttpPauseRequest(request, mask, pause));
   }
   RETURN(FALSE);
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttp_IsPaused --
 *
 *       Check whether the send and/or receive side of the request is paused.
 *
 * Results:
 *       Returns TRUE on success, FALSE on failure. If successful and
 *       recvPaused and/or sendPaused are not NULL, they will be set to TRUE
 *       if the request is paused in that direction and FALSE otherwise.
 *
 * Side effects:
 *       None.
 *
 *-----------------------------------------------------------------------------
 */

gboolean
CdkBasicHttp_IsPaused(CdkBasicHttpRequest *request, /* IN */
                      gboolean *recvPaused,         /* OUT/OPT */
                      gboolean *sendPaused)         /* OUT/OPT */
{
   gboolean success = FALSE;

   ENTRY;

   if (NULL == request) {
      GOTO(0, exit);
   }

   if (recvPaused) {
      *recvPaused = (request->pausedMask & CURLPAUSE_RECV) ? TRUE : FALSE;
   }

   if (sendPaused) {
      *sendPaused = (request->pausedMask & CURLPAUSE_SEND) ? TRUE : FALSE;
   }

   success = TRUE;

exit:
   RETURN(success);
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttp_CancelRequest --
 *
 *
 * Results:
 *       None.
 *
 * Side effects:
 *
 *-----------------------------------------------------------------------------
 */

void
CdkBasicHttp_CancelRequest(CdkBasicHttpRequest *request) /* IN */
{
   ENTRY;

   g_assert(NULL != curlGlobalState);
   g_assert(request);

   if (NULL != request->curl &&
       g_hash_table_lookup_extended(curlGlobalState->sessionHandles, request->curl, NULL, NULL)) {
      if (CURLM_OK == curl_multi_remove_handle(curlGlobalState->curlMulti, request->curl)) {
         g_hash_table_remove(curlGlobalState->sessionHandles, request->curl);
         LDEBUG(0, ("%s: Remove session handle from Curl: %p", __FUNCTION__, request->curl));
      }
   }

   if (NULL != request->bwGroup) {
      CdkBasicHttp_RemoveRequestFromBandwidthGroup(request->bwGroup, request);
   }

   /*
    * We need to clean up here and go to CDK_BASICHTTP_STATE_DONE since we won't be
    * invoking the caller's callback.
    */
   CdkBasicHttpFinishRequest(request, CDK_BASICHTTP_STATE_DONE);

   EXIT;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttpParseContentRange --
 *
 *       Internal function that parses the Content-Range response header.
 *
 * Results:
 *       TRUE on success, FALSE on failure.
 *       On success, the output values are set to the values in the header.
 *       Note that the size output can be CDK_BASICHTTP_UNKNOWN_SIZE (-1).
 *
 * Side effects:
 *
 *-----------------------------------------------------------------------------
 */

static gboolean
CdkBasicHttpParseContentRange(char *headerCompValue,      /* IN */
                              size_t sizeHeaderCompValue, /* IN */
                              gint64 *start,              /* OUT */
                              gint64 *end,                /* OUT */
                              gint64 *size)               /* OUT */
{
   gboolean rslt = FALSE;
   gint64 contentRangeStart = CDK_BASICHTTP_UNKNOWN_SIZE;
   gint64 contentRangeEnd = CDK_BASICHTTP_UNKNOWN_SIZE;
   gint64 totalContentSize = CDK_BASICHTTP_UNKNOWN_SIZE;
   size_t lenBytesMatch = 0;
   unsigned int index = 0;

   ENTRY;

   g_return_val_if_fail(headerCompValue, FALSE);
   g_return_val_if_fail(start, FALSE);
   g_return_val_if_fail(end, FALSE);
   g_return_val_if_fail(size, FALSE);

   /*
    * Parse Content-Range: bytes <digits>-<digits>[/<digits>]
    * "Content-Range: " has already been parsed. First look for the
    * units value of "bytes ".
    */
   if ((lenBytesMatch = STRNICMP_NON_TERM(HTTP_HEADER_RANGE_BYTES_STR, headerCompValue,
                                          sizeHeaderCompValue)) == 0) {
      LERROR(0, (MODULE "Error parsing Content-Range. "
                        "Range-Type bytes expected."));
      /*
       * Bail with rslt of FALSE, defaulted to FALSE above. (FALSE is a failure.)
       */
      GOTO(0, exit);
   }

   /*
    * Now look for the start of the range. (Digits before the '-' separator.)
    */
   headerCompValue += lenBytesMatch;
   if (!CdkStrUtil_GetNextInt64Token(&contentRangeStart, &index, headerCompValue, "-") ||
       headerCompValue[index] != '-') {
      LERROR(0, (MODULE "Error parsing Content-Range. <digits>- expected."));
      /*
       *  Bail with rslt of FALSE, defaulted to FALSE above. (FALSE is a failure.)
       */
      GOTO(0, exit);
   }

   /*
    * Now look for the end of the range. (Digits after the '-' separator but before
    * the optional '/' seperator.)
    */
   index++;
   if (!CdkStrUtil_GetNextInt64Token(&contentRangeEnd, &index, headerCompValue, "/")) {
      LERROR(0, (MODULE "Error parsing Content-Range. "
                        "<digits>-<digits> expected."));
      /*
       *  Bail with rslt of FALSE, defaulted to FALSE above. (FALSE is a failure.)
       */
      GOTO(0, exit);
   }

   /*
    * If there was a '/' separator, look for the object size.
    */
   if (headerCompValue[index] == '/') {
      index++;
      if (!CdkStrUtil_StrToInt64(&totalContentSize, headerCompValue + index)) {
         LERROR(0, (MODULE "Error parsing Content-Range. "
                           "<digits>-<digits>/<digits> expected."));
         /*
          *  Bail with rslt of FALSE, defaulted to FALSE above. (FALSE is a failure.)
          */
         GOTO(0, exit);
      }
   }

   *start = contentRangeStart;
   *end = contentRangeEnd;
   *size = totalContentSize;
   rslt = TRUE;

exit:
   RETURN(rslt);
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttpHeaderCallback --
 *
 *        Process header lines. Called one header line at a time.
 *        Note: Header lines passed in are not null terminated.
 *        Also: Header lines passed in have a 0x0d, 0x0a, at the end.
 *
 * Results:
 *       Size of header data processed is returned.
 *
 * Side effects:
 *
 *-----------------------------------------------------------------------------
 */

size_t
CdkBasicHttpHeaderCallback(void *buffer,     /* IN */
                           size_t size,      /* IN */
                           size_t nmemb,     /* IN */
                           void *clientData) /* IN */
{
   CdkBasicHttpRequest *request = (CdkBasicHttpRequest *)clientData;
   char *headerData = (char *)buffer;
   size_t bufferSize = size * nmemb;
   size_t rslt = 0;
   HttpHeaderComponent headerComponent = HTTP_HEADER_COMP_UNKNOWN;
   size_t lenHdrMatch = 0;
   char *headerCompValue = NULL;
   size_t sizeHeaderCompValue = 0;

   ENTRY;

   g_return_val_if_fail(NULL != request, 0);

   if (bufferSize == 0) {
      LDEBUG(0, (MODULE "Header callback called with empty buffer. "
                        "Not expected. No harm. Nothing to do."));
      /*
       * Bail with rslt of 0, defaulted to 0 above. (0 is a failure.)
       */
      GOTO(0, exit);
   }

   /*
    * Convert the various header component strings into a switchable enum value.
    * Additional side effect is lenHdrMatch should be set to the length
    * of the leading header component string where the data for the component follows.
    * Keep in mind, the header data isn't guaranteed to be null terminated.
    */
   if ((lenHdrMatch = STRNICMP_NON_TERM(HTTP_HEADER_CONTENT_LENGTH_STR, headerData, bufferSize)) >
       0) {
      headerComponent = HTTP_HEADER_COMP_CONTENT_LENGTH;
   } else if ((lenHdrMatch =
                  STRNICMP_NON_TERM(HTTP_HEADER_CONTENT_RANGE_STR, headerData, bufferSize)) > 0) {
      headerComponent = HTTP_HEADER_COMP_CONTENT_RANGE;
   } else if ((lenHdrMatch =
                  STRNICMP_NON_TERM(HTTP_HEADER_CONTENT_TYPE_STR, headerData, bufferSize)) > 0) {
      headerComponent = HTTP_HEADER_COMP_CONTENT_TYPE;
   } else if ((lenHdrMatch =
                  STRNICMP_NON_TERM(HTTP_HEADER_LAST_MODIFIED_STR, headerData, bufferSize)) > 0) {
      headerComponent = HTTP_HEADER_COMP_LAST_MODIFIED;
   } else if ((lenHdrMatch =
                  STRNICMP_NON_TERM(HTTP_HEADER_ACCEPT_RANGES_STR, headerData, bufferSize)) > 0) {
      headerComponent = HTTP_HEADER_COMP_ACCEPT_RANGES;
   } else if ((lenHdrMatch = STRNICMP_NON_TERM(HTTP_HEADER_DATE_STR, headerData, bufferSize)) > 0) {
      headerComponent = HTTP_HEADER_COMP_DATE;
   } else if (bufferSize == 2 && headerData[0] == 0x0d && headerData[1] == 0x0a) {
      headerComponent = HTTP_HEADER_COMP_TERMINATOR;
   }

   /*
    * Put header component value data into "clean" usable shape.
    * Determine the location and size of the value data and trim traling
    * non-text. There should always be a trailing CRLF - 0x0d, 0x0a.
    * There is no null terminator guaranteed, so add one over the CRLF
    * instead of making a copy of the data in a dynamically sized
    * buffer. We'll use the fact that the incoming buffer is writable
    * and there should be a trailing CRLF that we can overwrite.
    * (libCurl is "giving" us this data to handle. It's writable by us.)
    */
   if (headerComponent == HTTP_HEADER_COMP_UNKNOWN) {
      /*
       * Null-terminate unknown headers but do not attempt to parse.
       */
      if (bufferSize > 2) {
         headerData[bufferSize - 2] = 0x00;
      } else {
         LERROR(0, (MODULE "Unexpected error null-terminating unknown header."));
         /*
          * Bail with rslt of 0, defaulted to 0 above. (0 is a failure.)
          */
         GOTO(0, exit);
      }
   } else if (headerComponent != HTTP_HEADER_COMP_TERMINATOR) {
      g_assert(bufferSize > lenHdrMatch);
      headerCompValue = headerData + lenHdrMatch;
      sizeHeaderCompValue = bufferSize - lenHdrMatch;
      if (sizeHeaderCompValue > 2 && headerCompValue[sizeHeaderCompValue - 1] == 0x0a &&
          headerCompValue[sizeHeaderCompValue - 2] == 0x0d) {
         sizeHeaderCompValue -= 2;
         headerCompValue[sizeHeaderCompValue] = 0;
      } else {
         LERROR(0, (MODULE "Unexpected error parsing header."));
         /*
          * Bail with rslt of 0, defaulted to 0 above. (0 is a failure.)
          */
         GOTO(0, exit);
      }
   }

   /*
    * Handle the various header components.
    */
   switch (headerComponent) {
   case HTTP_HEADER_COMP_CONTENT_LENGTH: {
      gint64 contentLength = CDK_BASICHTTP_UNKNOWN_SIZE;
      if (sizeHeaderCompValue == 0 || headerCompValue == NULL ||
          !CdkStrUtil_StrToInt64(&contentLength, headerCompValue)) {
         LERROR(0, (MODULE "Unexpected error parsing Content-Length."));
         /*
          * Bail with rslt of 0, defaulted to 0 above. (0 is a failure.)
          */
         break;
      }
      request->recvContentInfo.expectedLength = contentLength;
   }

      /*
       * Exit with rslt of bufferSize. (This is the normal success result.)
       */
      rslt = bufferSize;
      break;

   case HTTP_HEADER_COMP_CONTENT_RANGE: {
      gint64 contentRangeStart = CDK_BASICHTTP_UNKNOWN_SIZE;
      gint64 contentRangeEnd = CDK_BASICHTTP_UNKNOWN_SIZE;
      gint64 totalContentSize = CDK_BASICHTTP_UNKNOWN_SIZE;

      if (!CdkBasicHttpParseContentRange(headerCompValue, sizeHeaderCompValue, &contentRangeStart,
                                         &contentRangeEnd, &totalContentSize)) {
         LERROR(0, (MODULE "Parsing Content-Range header failed."));
         /*
          * Bail with rslt of 0, defaulted to 0 above. (0 is a failure.)
          */
         break;
      }

      request->recvContentInfo.totalSize = totalContentSize;
      request->recvContentInfo.rangeStart = contentRangeStart;
      request->recvContentInfo.rangeEnd = contentRangeEnd;
   }

      /*
       * Exit with rslt of bufferSize. (This is the normal success result.)
       */
      rslt = bufferSize;
      break;

   case HTTP_HEADER_COMP_UNKNOWN: {
      struct curl_slist *newList = NULL;
      newList = curl_slist_append(request->recvHeaderList, headerData);
      /* Keep header list unchanged if failed. */
      if (newList) {
         request->recvHeaderList = newList;
         request->numRecvHeaders++;
      } else {
         LERROR(0, (MODULE "failure to append to the receive header. "
                           "Insufficient memory."));
      }
   }
      /* Fall through */
   case HTTP_HEADER_COMP_CONTENT_TYPE:
   case HTTP_HEADER_COMP_LAST_MODIFIED:
   case HTTP_HEADER_COMP_ACCEPT_RANGES:
   case HTTP_HEADER_COMP_DATE:
   case HTTP_HEADER_COMP_TERMINATOR:
      /*
       * Just ignore header components we don't care about.
       * Exit with rslt of bufferSize. This is the normal successful result.
       */
      rslt = bufferSize;
      break;

   default:
      /*
       * The above unknown case should be the fallback default. We should never get here.
       * All possible enum headerComponent cases should be handled above.
       * This is just a defensive paranoia check for a bug.
       */
      g_assert_not_reached();
      /*
       * Bail with rslt of 0, defaulted to 0 above. (0 is a failure.)
       */
      break;
   }

exit:
   RETURN(rslt);
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttpReadCallback --
 *
 *
 * Results:
 *       The amount of data read is returned.
 *
 * Side effects:
 *
 *-----------------------------------------------------------------------------
 */

size_t
CdkBasicHttpReadCallback(void *buffer,     /* IN/OUT */
                         size_t size,      /* IN */
                         size_t nmemb,     /* IN */
                         void *clientData) /* IN */
{
   static const size_t readError = (size_t)-1;
   CdkBasicHttpRequest *request = (CdkBasicHttpRequest *)clientData;
   CdkBandwidthStatistics *bwStatistics;
   size_t ret;
   double uploaded;
   guint64 elapsed;
   gint64 currentTime;

   ENTRY;

   g_return_val_if_fail(NULL != request, 0);

   bwStatistics = &(request->statistics[CDK_BASICHTTP_UPLOAD]);

   curl_easy_getinfo(request->curl, CURLINFO_SIZE_UPLOAD, &uploaded);
   CdkBasicHttpBandwidthUpdate(bwStatistics, (guint64)uploaded);

   if (request->bwGroup && (request->bwGroup->limits[CDK_BASICHTTP_UPLOAD] > 0)) {
      gint64 delay;

      delay = CdkBasicHttpBandwidthGetDelay(request->bwGroup, request, CDK_BASICHTTP_UPLOAD);

      if (delay > 0) {
         request->resumePollHandlerId =
            CdkMain_AddTimeout((guint)(delay / 1000), CdkBasicHttpResumePollCallback, request);
         /*
          * Don't set request->pausedMask here. CdkBasicHttpResumePollCallback
          * will un-pause the transfer after delay timeout.
          */
         ret = CURL_READFUNC_PAUSE;
         GOTO(0, exit);
      }
   }

   CdkBasicHttpBandwidthSlideWindow(bwStatistics);
   currentTime = bwStatistics->lastTime;
   elapsed = (guint64)(currentTime - request->lastProgressCallbackTime);
   request->lastProgressCallbackTime = currentTime;
   CdkBasicHttpGotActivity(request);

   /*
    * Update the state before invoking the progress callback so that if the
    * application decides to query our state there, it's up to date.
    */
   if (bwStatistics->transferredBytes == CdkBasicHttpSourceLength(request->body)) {
      request->state = CDK_BASICHTTP_STATE_WAITING;
   } else {
      /*
       * It's necessary to make sure we go to CDK_BASICHTTP_STATE_SENDING in this
       * case because we weren't necessarily already in that state. We might
       * have gotten here after moving out of CDK_BASICHTTP_STATE_SENDING because
       * curl decided to resend the request, like in case of a redirect.
       *
       * This may also happen because sometimes curl asks for more data to send
       * even though we've already sent the entire body and the response may
       * have started arriving. We handle it fine because CdkBasicHttpSourceRead
       * below will return 0 and curl will know to stop, but it can look
       * strange to the caller. TODO: Try to shield the caller from this.
       *
       * As long as curl is asking us for data to send, and we haven't yet
       * sent all of it, we are in CDK_BASICHTTP_STATE_SENDING.
       */
      request->state = CDK_BASICHTTP_STATE_SENDING;
   }

   if (NULL != request->sendProgressProc) {
      gboolean success;

      success =
         (request->sendProgressProc)(request, 0, NULL, elapsed, bwStatistics->transferredBytes,
                                     bwStatistics->windowedRate, request->clientData);
      if (!success) {
         /*
          * Pause the transfer. The transfer must be resumed by calling
          * CdkBasicHttp_PauseSendRequest().
          */
         request->pausedMask |= CURLPAUSE_SEND;
         ret = CURL_READFUNC_PAUSE;
         GOTO(0, exit);
      }
   }

   ret = (size_t)CdkBasicHttpSourceRead(request->body, buffer, size, nmemb);

   if (readError == ret) {
      ret = CURL_READFUNC_ABORT;
      GOTO(0, exit);
   }

exit:
   RETURN(ret);
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttpWriteCallback --
 *
 *
 * Results:
 *       The amount of data written is returned.
 *
 * Side effects:
 *       Depending on the results returned by the external progress callback,
 *       the transfer could be paused or canceled.
 *
 *-----------------------------------------------------------------------------
 */

size_t
CdkBasicHttpWriteCallback(void *buffer,     /* IN */
                          size_t size,      /* IN */
                          size_t nmemb,     /* IN */
                          void *clientData) /* IN */
{
   CdkBasicHttpRequest *request = (CdkBasicHttpRequest *)clientData;
   CdkBandwidthStatistics *bwStatistics;
   size_t bufferSize = size * nmemb;
   size_t ret = 0;
   guint64 elapsed;
   gint64 currentTime;
   double downloaded;

   ENTRY;

   g_return_val_if_fail(NULL != request, 0);

   bwStatistics = &(request->statistics[CDK_BASICHTTP_DOWNLOAD]);

   curl_easy_getinfo(request->curl, CURLINFO_SIZE_DOWNLOAD, &downloaded);
   CdkBasicHttpBandwidthUpdate(bwStatistics, (guint64)downloaded);

   if (request->bwGroup && (request->bwGroup->limits[CDK_BASICHTTP_DOWNLOAD] > 0)) {
      gint64 delay;

      delay = CdkBasicHttpBandwidthGetDelay(request->bwGroup, request, CDK_BASICHTTP_DOWNLOAD);

      if (delay > 0) {
         request->resumePollHandlerId =
            CdkMain_AddTimeout((guint)(delay / 1000), CdkBasicHttpResumePollCallback, request);
         /*
          * Don't set request->pausedMask here. CdkBasicHttpResumePollCallback
          * will un-pause the transfer after delay timeout.
          */
         ret = CURL_WRITEFUNC_PAUSE;
         GOTO(0, exit);
      }
   }

   CdkBasicHttpBandwidthSlideWindow(&(request->statistics[CDK_BASICHTTP_DOWNLOAD]));
   currentTime = bwStatistics->lastTime;
   elapsed = (guint64)(currentTime - request->lastProgressCallbackTime);
   request->lastProgressCallbackTime = currentTime;
   CdkBasicHttpGotActivity(request);

   /*
    * Update the state before invoking the progress callback so that if the
    * application decides to query our state there, it's up to date.
    *
    * This side is a bit simpler than the send side. Any time curl is giving
    * us data that it received, we are in CDK_BASICHTTP_STATE_RECEIVING. We may
    * have started in CDK_BASICHTTP_STATE_WAITING, and we'll move to
    * CDK_BASICHTTP_STATE_DONE when curl tells us we're done.
    */
   request->state = CDK_BASICHTTP_STATE_RECEIVING;

   if (NULL != request->recvProgressProc) {
      gboolean success;

      success = (request->recvProgressProc)(request, bufferSize, buffer, elapsed,
                                            bwStatistics->transferredBytes,
                                            bwStatistics->windowedRate, request->clientData);
      if (!success) {
         /*
          * Pause the transfer. The transfer must be resumed by calling
          * CdkBasicHttp_PauseRecvRequest().
          */
         request->pausedMask |= CURLPAUSE_RECV;
         ret = CURL_WRITEFUNC_PAUSE;
         GOTO(0, exit);
      }
   }

   /*
    * If the caller set CDK_BASICHTTP_NO_RESPONSE_CONTENT, it means the caller
    * doesn't want to receive the response content from response->content.
    * Otherwise, append the partial result here into request->receiveBuf.
    */
   if (!(CDK_BASICHTTP_NO_RESPONSE_CONTENT & request->options)) {
      g_string_append_len(request->receiveBuf, buffer, bufferSize);
   }

   ret = bufferSize;

exit:
   RETURN(ret);
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttpIoctlCallback --
 *        Callback for curl ioctl.
 *
 * Results:
 *        Handles CURLIOCMD_NOP and CURLIOCMD_RESTARTREAD.
 *
 * Side effects:
 *
 *
 *-----------------------------------------------------------------------------
 */

curlioerr
CdkBasicHttpIoctlCallback(CURL *handle,     /* IN */
                          int cmd,          /* IN */
                          void *clientData) /* IN */
{
   CdkBasicHttpRequest *request = (CdkBasicHttpRequest *)clientData;
   curlioerr ret = CURLIOE_UNKNOWNCMD;

   ENTRY;

   switch (cmd) {
   case CURLIOCMD_NOP:
      ret = CURLIOE_OK;
      break;
   case CURLIOCMD_RESTARTREAD:
      if (CdkBasicHttpSourceRewind(request->body)) {
         CdkBasicHttpBandwidthReset(&(request->statistics[CDK_BASICHTTP_UPLOAD]));
         CdkBasicHttpBandwidthReset(&(request->statistics[CDK_BASICHTTP_DOWNLOAD]));
         ret = CURLIOE_OK;
      } else {
         ret = CURLIOE_FAILRESTART;
      }
      break;
   default:
      break;
   }

   RETURN(ret);
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttpResumePollCallback --
 *
 *       Callback to resume the transfer after it's been paused due to
 *       bandwidth control.
 *
 * Results:
 *       FALSE in all cases.
 *
 * Side effects:
 *
 *-----------------------------------------------------------------------------
 */

gboolean
CdkBasicHttpResumePollCallback(void *clientData) /* IN */
{
   CdkBasicHttpRequest *request;

   ENTRY;

   g_return_val_if_fail(NULL != clientData, FALSE);
   request = (CdkBasicHttpRequest *)clientData;

   curl_easy_pause(request->curl, request->pausedMask);

   /*
    * The socket is already in the signaled state.
    */
   CdkBasicHttpSocketPollCallback(NULL);

   RETURN(FALSE);
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttpRemoveResumePollCallback --
 *
 *       Remove CdkBasicHttpResumePollCallback from poll.
 *
 * Results:
 *       None.
 *
 * Side effects:
 *
 *-----------------------------------------------------------------------------
 */

void
CdkBasicHttpRemoveResumePollCallback(CdkBasicHttpRequest *request) /* IN */
{
   ENTRY;

   g_return_if_fail(NULL != request);
   g_return_if_fail(NULL == request->bwGroup);

   CDK_MAIN_REMOVE(request->resumePollHandlerId);

   EXIT;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttp_AllocSource --
 *       Create a new source.
 *
 * Results:
 *       A pointer to a source. Caller must call CdkBasicHttp_FreeSource.
 *
 * Side effects:
 *
 *-----------------------------------------------------------------------------
 */

CdkBasicHttpSource *
CdkBasicHttp_AllocSource(const CdkBasicHttpSourceOps *ops, /* IN */
                         void *privat)                     /* IN */
{
   CdkBasicHttpSource *ret = g_new0(CdkBasicHttpSource, 1);

   ENTRY;

   ret->ops = ops;
   ret->privat = privat;

   RETURN(ret);
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttp_FreeSource --
 *       Free a source.
 *
 * Results:
 *       None.
 *
 * Side effects:
 *
 *-----------------------------------------------------------------------------
 */

void
CdkBasicHttp_FreeSource(CdkBasicHttpSource *source) /* IN */
{
   ENTRY;

   if (source) {
      if (source->ops && source->ops->destructProc) {
         source->ops->destructProc(source->privat);
      }
      g_free(source);
   }

   EXIT;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttp_RewindSource --
 *       Rewind a source.
 *
 * Results:
 *       gbooleanean, TRUE on success.
 *
 * Side effects:
 *
 *-----------------------------------------------------------------------------
 */

gboolean
CdkBasicHttp_RewindSource(CdkBasicHttpSource *source) /* IN */
{
   gboolean success = FALSE;

   ENTRY;

   if (source) {
      success = CdkBasicHttpSourceRewind(source);
   }

   RETURN(success);
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttpSourceRead --
 *      Safely read from a source.
 *
 * Results:
 *      Length in bytes read on success, -1 on failure.
 *
 * Side effects:
 *
 *-----------------------------------------------------------------------------
 */

gssize
CdkBasicHttpSourceRead(CdkBasicHttpSource *source, /* IN */
                       void *buffer,               /* IN/OUT */
                       size_t size,                /* IN */
                       size_t nmemb)               /* IN */
{
   gssize ret = 0;

   ENTRY;

   g_return_val_if_fail(source != NULL, -1);
   g_return_val_if_fail(source->ops, -1);
   g_return_val_if_fail(source->ops->readProc, -1);

   ret = source->ops->readProc(source->privat, buffer, size, nmemb);

   /* Valid return values of a read method. */
   g_assert(-1 <= ret);
   RETURN(ret);
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttpSourceRewind --
 *      Safely rewind a source.
 *
 * Results:
 *      TRUE on success.
 *
 * Side effects:
 *
 *-----------------------------------------------------------------------------
 */

gboolean
CdkBasicHttpSourceRewind(CdkBasicHttpSource *source) /* IN */
{
   ENTRY;

   g_return_val_if_fail(source, FALSE);
   g_return_val_if_fail(source->ops, FALSE);
   g_return_val_if_fail(source->ops->rewindProc, FALSE);

   RETURN(source->ops->rewindProc(source->privat));
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttpSourceLength --
 *      Safely find the length of a source.
 *
 * Results:
 *      Length of the source.
 *
 * Side effects:
 *
 *-----------------------------------------------------------------------------
 */

size_t
CdkBasicHttpSourceLength(CdkBasicHttpSource *source) /* IN */
{
   ENTRY;

   g_return_val_if_fail(source, 0);
   g_return_val_if_fail(source->ops, 0);
   g_return_val_if_fail(source->ops->lengthProc, 0);

   RETURN(source->ops->lengthProc(source->privat));
}


/* CdkBasicHttpMemorySource declarations. */
static gssize CdkBasicHttpMemorySourceRead(void *privat, void *buffer, size_t size, size_t nmemb);
static gboolean CdkBasicHttpMemorySourceRewind(void *privat);
static size_t CdkBasicHttpMemorySourceLength(void *privat);
static void CdkBasicHttpMemorySourceDestruct(void *privat);

static CdkBasicHttpSourceOps CdkBasicHttpMemorySourceOps = {
   CdkBasicHttpMemorySourceRead, CdkBasicHttpMemorySourceRewind, CdkBasicHttpMemorySourceLength,
   CdkBasicHttpMemorySourceDestruct};

typedef struct CdkBasicHttpMemorySource CdkBasicHttpMemorySource;


/* CdkBasicHttpMemorySource implementation. */
struct CdkBasicHttpMemorySource {
   guint8 *data;
   size_t dataLen;
   CdkBasicHttpFreeProc *dataFreeProc;
   const guint8 *readPtr;
   size_t sizeLeft;
};


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttp_AllocMemorySource --
 *       Create a new memory source. If dataFreeProc is not NULL, the memory
 *       source will take ownership of the data passed and call dataFreeProc
 *       on it in its destructor. Otherwise, the memory source will make its
 *       own copy of the data.
 *
 * Results:
 *       A pointer to a memory source. Caller must call CdkBasicHttp_FreeSource.
 *
 * Side effects:
 *
 *-----------------------------------------------------------------------------
 */

CdkBasicHttpSource *
CdkBasicHttp_AllocMemorySource(guint8 *data,                       /* IN */
                               size_t dataLen,                     /* IN */
                               CdkBasicHttpFreeProc *dataFreeProc) /* IN */
{
   CdkBasicHttpMemorySource *source = g_new0(CdkBasicHttpMemorySource, 1);
   CdkBasicHttpSource *ret = NULL;

   ENTRY;

   source->dataFreeProc = dataFreeProc;
   if (dataFreeProc) {
      source->data = data;
   } else {
      source->data = g_malloc0(dataLen);
      memcpy(source->data, data, dataLen);
   }
   source->dataLen = dataLen;
   source->readPtr = source->data;
   source->sizeLeft = dataLen;

   ret = CdkBasicHttp_AllocSource(&CdkBasicHttpMemorySourceOps, source);
   RETURN(ret);
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttp_AllocStringSource --
 *       Create a new string memory source.
 *
 * Results:
 *       A pointer to a memory source. Caller must call CdkBasicHttp_FreeSource.
 *
 * Side effects:
 *
 *-----------------------------------------------------------------------------
 */

CdkBasicHttpSource *
CdkBasicHttp_AllocStringSource(const char *data) /* IN */
{
   ENTRY;
   RETURN(CdkBasicHttp_AllocMemorySource((guint8 *)data, data ? strlen(data) : 0, NULL));
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttpMemorySourceDestruct --
 *       Free a CdkBasicHttpMemorySource.
 *
 * Results:
 *       None.
 *
 * Side effects:
 *
 *-----------------------------------------------------------------------------
 */

void
CdkBasicHttpMemorySourceDestruct(void *privat) /* IN */
{
   CdkBasicHttpMemorySource *source = (CdkBasicHttpMemorySource *)privat;

   ENTRY;

   if (source) {
      if (source->data) {
         if (source->dataFreeProc) {
            source->dataFreeProc(source->data);
         } else {
            if (source->dataLen > 0) {
               memset(source->data, 0, source->dataLen);
            }
            g_free(source->data);
         }
      }
      g_free(source);
   }

   EXIT;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttpMemorySourceRead --
 *       Read from a memory source.
 *
 * Results:
 *       The amount of data read is returned.
 *
 * Side effects:
 *
 *-----------------------------------------------------------------------------
 */

gssize
CdkBasicHttpMemorySourceRead(void *privat, /* IN */
                             void *buffer, /* IN/OUT */
                             size_t size,  /* IN */
                             size_t nmemb) /* IN */
{
   CdkBasicHttpMemorySource *source;
   size_t bufferSize;
   size_t readSize = 0;

   ENTRY;

   source = (CdkBasicHttpMemorySource *)privat;
   g_return_val_if_fail(NULL != source, 0);

   bufferSize = size * nmemb;
   if (bufferSize < 1) {
      readSize = 0;
      GOTO(0, abort);
   }

   if (source->sizeLeft > 0) {
      if (source->sizeLeft < bufferSize) {
         bufferSize = source->sizeLeft;
      }
      memcpy(buffer, source->readPtr, bufferSize);
      source->readPtr += bufferSize;
      source->sizeLeft -= bufferSize;

      readSize = bufferSize;
      GOTO(0, abort);
   } else { // reset since curl may need to retry if the connection is broken.
      CdkBasicHttpMemorySourceRewind(source);
   }

abort:
   RETURN(readSize);
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttpMemorySourceRewind --
 *       Rewind a memory source.
 *
 * Results:
 *       TRUE on success.
 *
 * Side effects:
 *
 *-----------------------------------------------------------------------------
 */

gboolean
CdkBasicHttpMemorySourceRewind(void *privat) /* IN */
{
   CdkBasicHttpMemorySource *source;

   ENTRY;

   source = (CdkBasicHttpMemorySource *)privat;
   g_return_val_if_fail(NULL != source, FALSE);

   source->readPtr = source->data;
   source->sizeLeft = source->dataLen;

   RETURN(TRUE);
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttpMemorySourceLength --
 *       Length of a CdkBasicHttpMemorySource.
 *
 * Results:
 *       Length of memory source, -1 on failure.
 *
 * Side effects:
 *
 *-----------------------------------------------------------------------------
 */

size_t
CdkBasicHttpMemorySourceLength(void *privat) /* IN */
{
   CdkBasicHttpMemorySource *source;

   ENTRY;

   source = (CdkBasicHttpMemorySource *)privat;
   g_return_val_if_fail(NULL != source, -1);

   RETURN(source->dataLen);
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttpFreeRequestBody --
 *       Free the objects related to the body. This is to be called after
 *       curl_easy_cleanup.
 *
 * Results:
 *       None.
 *
 * Side effects:
 *
 *-----------------------------------------------------------------------------
 */

void
CdkBasicHttpFreeRequestBody(CdkBasicHttpRequest *request) /* IN */
{
   /* Caller is responsible for freeing the source. If ownBody is true, then
      CdkBasicHttp created the source. */

   ENTRY;

   if (request->ownBody) {
      CdkBasicHttp_FreeSource(request->body);
   }

   EXIT;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttp_FreeRequest --
 *
 *
 * Results:
 *       None.
 *
 * Side effects:
 *
 *-----------------------------------------------------------------------------
 */

void
CdkBasicHttp_FreeRequest(CdkBasicHttpRequest *request) /* IN */
{
   CdkUdpProxyInterface *udpProxy = NULL;
   ENTRY;

   if (NULL == request) {
      EXIT;
   }

   udpProxy = CdkConnection_GetUdpProxy(NULL);
   /*
    * If this was passed to the udpProxy as a context pointer, we need to
    * remove it, so we don't get it back later via some error callback.
    */
   if (udpProxy != NULL && udpProxy->updateContextFunc != NULL) {
      udpProxy->updateContextFunc(udpProxy, request, NULL);
   }

   CdkBasicHttpRemoveDNSResolveEntry(request);

   CdkBasicHttp_CancelRequest(request);

   curl_slist_free_all(request->headerList);
   curl_slist_free_all(request->recvHeaderList);
   curl_slist_free_all(request->connectTo);
   curl_slist_free_all(request->addResolveEntry);
   curl_slist_free_all(request->removeResolveEntry);
   g_string_free(request->receiveBuf, TRUE);
   g_free(request->url);
   g_free(request->userNameAndPassword);
   g_free(request->userAgent);
   g_free(request->proxy);
   if (NULL != request->curl) {
      curl_easy_cleanup(request->curl);
   }
   if (NULL != request->bwGroup) {
      CdkBasicHttp_RemoveRequestFromBandwidthGroup(request->bwGroup, request);
   }
   CdkBasicHttpFreeRequestBody(request);
   if (!curlGlobalState->skipRemove) {
      g_hash_table_remove(curlGlobalState->requests, (void *)request);
   }
   sk_X509_pop_free(request->peerCertificates, X509_free);
   g_free(request);

   EXIT;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttp_FreeResponse --
 *
 *
 * Results:
 *       None.
 *
 * Side effects:
 *
 *-----------------------------------------------------------------------------
 */

void
CdkBasicHttp_FreeResponse(CdkBasicHttpResponse *response) /* IN */
{
   ENTRY;

   if (NULL == response) {
      EXIT;
   }

   g_free(response->content);
   g_free(response->effectiveURL);
   g_free(response);

   EXIT;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttp_GetRecvContentInfo --
 *
 *       Get the receive content information for the request. This function
 *       can be called any time after request is created. But it will return
 *       useful information only after the header has been processed, for
 *       example, in the recvProgress callback.
 *
 * Results:
 *       None.
 *
 * Side effects:
 *
 *-----------------------------------------------------------------------------
 */

void
CdkBasicHttp_GetRecvContentInfo(CdkBasicHttpRequest *request,         /* IN */
                                CdkBasicHttpContentInfo *contentInfo) /* IN/OUT */
{
   ENTRY;

   g_return_if_fail(NULL != request);
   g_return_if_fail(NULL != contentInfo);

   *contentInfo = request->recvContentInfo;

   EXIT;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttp_GetNumResponseHeaders --
 *
 *       Get the number of unhandled headers in the response to a request.
 *       This can be called at any time but will not return accurate results
 *       until after the response has been fully obtained (eg: In the SentProc
 *       callback).
 *
 * Results:
 *       Number of unhandled headers.
 *
 * Side effects:
 *       None
 *
 *-----------------------------------------------------------------------------
 */

size_t
CdkBasicHttp_GetNumResponseHeaders(CdkBasicHttpRequest *request) /* IN */
{
   ENTRY;
   RETURN(request->numRecvHeaders);
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttp_GetResponseHeader --
 *
 *       Get a particular response header.
 *       This can be called at any time but will not return accurate results
 *       until after the response has been fully obtained (eg: In the SentProc
 *       callback).
 *
 * Results:
 *       The header as a string owned by CdkBasicHttp.
 *
 * Side effects:
 *       None
 *
 *-----------------------------------------------------------------------------
 */

const char *
CdkBasicHttp_GetResponseHeader(CdkBasicHttpRequest *request, /* IN */
                               size_t header)                /* IN */
{
   size_t i = 0;
   struct curl_slist *headerList = request->recvHeaderList;

   ENTRY;

   g_return_val_if_fail(header < request->numRecvHeaders, NULL);

   for (i = 0; i < header; i++) {
      headerList = headerList->next;
      g_assert(headerList);
   }

   RETURN(headerList->data);
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkStrUtil_GetNextToken --
 *
 *      Get the next token from a string after a given index w/o modifying the
 *      original string.
 *
 * Results:
 *      An allocated, NUL-terminated string containing the token. 'index' is
 *         updated to point after the returned token
 *      NULL if no tokens are left
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

static char *
CdkStrUtil_GetNextToken(unsigned int *index,    /* IN: Index to start at */
                        const char *str,        /* IN: String to parse */
                        const char *delimiters) /* IN: Chars separating tokens */
{
   unsigned int startIndex;
   unsigned int length;
   char *token;

   ENTRY;

   g_return_val_if_fail(index, NULL);
   g_return_val_if_fail(str, NULL);
   g_return_val_if_fail(delimiters, NULL);
   g_return_val_if_fail(*index <= strlen(str), NULL);

#define NOT_DELIMITER (strchr(delimiters, str[*index]) == NULL)

   /* Skip leading delimiters. */
   for (;; (*index)++) {
      if (str[*index] == '\0') {
         RETURN(NULL);
      }

      if (NOT_DELIMITER) {
         break;
      }
   }
   startIndex = *index;

   /*
    * Walk the string until we reach the end of it, or we find a
    * delimiter.
    */
   for ((*index)++; str[*index] != '\0' && NOT_DELIMITER; (*index)++) {
   }

#undef NOT_DELIMITER

   length = *index - startIndex;
   g_assert(length);
   token = (char *)g_malloc(length + 1 /* NUL */);
   g_assert(token);
   memcpy(token, str + startIndex, length);
   token[length] = '\0';

   RETURN(token);
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkStrUtil_GetNextInt64Token --
 *
 *      Acts like StrUtil_GetNextToken except it returns an gint64.
 *
 * Results:
 *      TRUE on a successful retrieval. FALSE otherwise.
 *      Token is stored in 'out', which is left undefined in the FALSE case.
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

static gboolean
CdkStrUtil_GetNextInt64Token(gint64 *out,            /* OUT: The output value */
                             unsigned int *index,    /* IN/OUT: Index to start at */
                             const char *str,        /* IN: String to parse */
                             const char *delimiters) /* IN: Chars separating tokens */
{
   char *resultStr;
   gboolean result;

   ENTRY;

   g_return_val_if_fail(out, FALSE);
   g_return_val_if_fail(index, FALSE);
   g_return_val_if_fail(str, FALSE);
   g_return_val_if_fail(delimiters, FALSE);

   resultStr = CdkStrUtil_GetNextToken(index, str, delimiters);
   result = resultStr ? CdkStrUtil_StrToInt64(out, resultStr) : FALSE;
   g_free(resultStr);

   RETURN(result);
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkStrUtil_StrToInt64 --
 *
 *      Convert a string into a 64bit integer.
 *
 * Results:
 *      TRUE if conversion was successful, FALSE otherwise.
 *      Value is stored in 'out', which is left undefined in the FALSE case.
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

static gboolean
CdkStrUtil_StrToInt64(gint64 *out,     /* OUT: The output value */
                      const char *str) /* IN: String to parse */
{
   char *ptr;

   ENTRY;

   g_return_val_if_fail(out, FALSE);
   g_return_val_if_fail(str, FALSE);

   errno = 0;

#if defined(_WIN32)
   *out = _strtoi64(str, &ptr, 0);
#elif defined(__FreeBSD__)
   *out = strtoq(str, &ptr, 0);
#elif defined(N_PLAT_NLM)
   /* Works for small values of str... */
   *out = (gint64)strtol(str, &ptr, 0);
#else
   *out = strtoll(str, &ptr, 0);
#endif

   RETURN(ptr[0] == '\0' && errno != ERANGE);
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttp_GetPeerCertificates --
 *
 *      Get the peer certificates sent by the server for a given
 *      request.  Uses cURL's API for getting certificate "info," and
 *      parses it into OpenSSL certificates.
 *
 * Results:
 *      STACK_OF(X509) *, to be freed with sk_X509_pop_free(,
 *      X509_free), or NULL.
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

void *
CdkBasicHttp_GetPeerCertificates(CdkBasicHttpRequest *request) // IN
{
   return CdkUtil_DupPeerCertificates(request->peerCertificates);
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttp_SetPeerCertificates --
 *
 *      Explicitly set the peer certificates seen with this request.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

void
CdkBasicHttp_SetPeerCertificates(CdkBasicHttpRequest *request, /* IN */
                                 gpointer peerCertificates)    /* IN */
{
   CdkUtil_FreePeerCertificates(request->peerCertificates);
   request->peerCertificates = CdkUtil_DupPeerCertificates(peerCertificates);
}


#ifdef METRO
/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttp_GetSessionCookie --
 *
 *      Get current session cookie from cookie jar.
 *
 * Results:
 *      Cookie string if succeed, otherwise NULL
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

char *
CdkBasicHttp_GetSessionCookie(CdkBasicHttpCookieJar *cookieJar) /* IN */
{
   struct curl_slist *cookies;
   struct curl_slist *cursor;
   char *sessionCookie = NULL;

   CURL *curl = curl_easy_init();
   if (curl && cookieJar) {
      curl_easy_setopt(curl, CURLOPT_SHARE, cookieJar->curlShare);

      CURLcode result = curl_easy_getinfo(curl, CURLINFO_COOKIELIST, &cookies);
      if (result == CURLE_OK) {
         gchar *sessionStr = "JSESSIONID";
         cursor = cookies;
         size_t len = strlen(cursor->data);
         while (cursor) {
            gchar *firstPart = g_strstr_len(cursor->data, len, sessionStr);
            if (firstPart) {
               sessionCookie = g_strdup(cursor->data);
               break;
            }
            cursor = cursor->next;
         }
         curl_slist_free_all(cookies);
      }
   }
   curl_easy_cleanup(curl);
   return sessionCookie;
}
#endif


#if defined USE_UDP_PROXY
/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttpSetUdpProxyInfo --
 *
 *      Check if UDP proxy is enabled & set the remote host info, then set the
 *      the curl option CURLOPT_CONNECT_TO to redirect the request to the
 *      local loopback connection.
 *      When UDP proxy is not enabled(TCP connection case), it's now required
 *      to udpate remote port and should be removed when Blast can optimize
 *      not having Client to pass port parameter to mks always.
 *
 * Results:
 *      TRUE if UDP proxy is not used, or successfully configured the remote
 *      host info in UDP proxy. FALSE on failure.
 *
 * Side effects:
 *      CURLOPT_CONNECT_TO is a new curl option available with version 7.49:
 *      https://curl.haxx.se/libcurl/c/CURLOPT_CONNECT_TO.html
 *
 *-----------------------------------------------------------------------------
 */

gboolean
CdkBasicHttpSetUdpProxyInfo(CdkBasicHttpRequest *request, // IN:
                            const char *hostname,         // IN:
                            unsigned short port)          // IN:
{
   gboolean remoteSetOK = FALSE;
   CdkConnection *conn = request->connection;
   CdkUdpProxyInterface *udpProxy = CdkConnection_GetUdpProxy(conn);

   g_return_val_if_fail(request != NULL, FALSE);
   g_return_val_if_fail(hostname != NULL, FALSE);

   if (CdkConnection_IsBrokerUDPEnabled(conn) && udpProxy != NULL) {
      /*
       * UdpProxy always listens on the IPv4 loopback address because this is an
       * internal address. TODO: UdpProxyLib should export this as a variable,
       * or return it during the init function.
       */
      const char *udpProxyLoopbackAddr = "127.0.0.1";
      char *connectToStr = g_strdup_printf("%s:%u:%s:%u", hostname, port, udpProxyLoopbackAddr,
                                           CdkConnection_GetLoopbackPort(NULL));

      if (connectToStr) {
         request->connectTo = curl_slist_append(NULL, connectToStr);
      }
      g_free(connectToStr);

      g_assert(curlGlobalState);
      g_assert(udpProxy->updateRemoteInfoFunc != NULL);
      remoteSetOK =
         udpProxy->updateRemoteInfoFunc(udpProxy, request, CdkConnection_GetPreferredAddress(conn),
                                        port, CdkBasicHttpUdpProxyErrorProc2);

      if (remoteSetOK) {
         LDEBUG(0, (MODULE "UDP Proxy remote host:%s port:%u",
                    CdkConnection_GetPreferredAddress(conn), port));
      } else {
         LERROR(0, (MODULE "Failed to set UDP Proxy info."));
         return FALSE;
      }

      LDEBUG(0, (MODULE "CURLOPT_CONNECT_TO string: %s", request->connectTo->data));
      curl_easy_setopt(request->curl, CURLOPT_CONNECT_TO, request->connectTo);

      // Do not use the external proxy for the loopback UDP proxy connection.
      if (request->proxyType != CDK_BASICHTTP_PROXY_NONE) {
         CdkBasicHttp_SetProxy(request, NULL, CDK_BASICHTTP_PROXY_NONE);
      }
   } else {
      /*
       * TODO: This should be removed after Blast can optimize
       * by removing the port parameter required to pass to mks always.
       */
      if (udpProxy != NULL) {
         g_assert(udpProxy->updateRemoteInfoFunc != NULL);
         remoteSetOK = udpProxy->updateRemoteInfoFunc(udpProxy, request, hostname, port,
                                                      CdkBasicHttpUdpProxyErrorProc2);
         if (remoteSetOK) {
            LDEBUG(0, (MODULE "Using TCP connection, "
                              "UDP Proxy remote host:%s port:%u",
                       hostname, port));
         } else {
            LERROR(0, (MODULE "Using TCP connection, "
                              "failed to set UDP Proxy info, but will ignore the failure"));
         }
      }
   }

   return TRUE;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttpUdpProxyErrorProc2 --
 *
 *      Callback function for the UDP Proxy connection.
 *      Nothing useful for now, but this will be used to manage the connection
 *      fallback eventually.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

void
CdkBasicHttpUdpProxyErrorProc2(CdkUdpProxyErrorCode error, // IN:
                               const char *remoteHost,     // IN:
                               void *context)              // IN:
{
   CdkBasicHttpRequest *request = (CdkBasicHttpRequest *)context;

   LDEBUG(0, (MODULE "UDP loopback proxy error:%d from:%s", error, remoteHost));

   if (request == NULL) {
      LDEBUG(0, ("%s: Request was null: error ignored", __FUNCTION__));
      return;
   }

   if (error == CDK_UDP_PROXY_CONNECT && request->errorCode == CDK_BASICHTTP_ERROR_NONE) {
      /*
       * UDP proxy is probably trying to connect to a TCP port.
       * Make up a connection error
       */
      request->result = CURLE_COULDNT_CONNECT;
      CdkBasicHttpCompleteRequestCallback(request);
   }
}
#endif


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttpAddDNSResolveEntry --
 *
 *      Add a DNS resolve entry for the hostname and Server IP mapping if
 *      Server IP is set, currently one request only allows adding one expected
 *      address entry which is not allowed to be changed.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

void
CdkBasicHttpAddDNSResolveEntry(CdkBasicHttpRequest *request) /* IN/OUT */
{
   char *nameResolveStr = NULL;
   CURLcode code = CURLE_OK;

   ENTRY;

   g_return_if_fail(request);
   g_return_if_fail(request->connection);

   if (!CdkConnection_GetPreferredAddress(request->connection) ||
       CdkConnection_GetHostnameType(request->connection) != CDK_ADDRESS_TYPE_NAME ||
       request->addResolveEntry) {
      LDEBUG(0, (MODULE "Server IP is NULL or hostname is not name or a DNS "
                        "resolve entry has been added."));
      EXIT;
   }

   nameResolveStr = g_strdup_printf("%s:%u:%s", CdkConnection_GetHostname(request->connection),
                                    CdkConnection_GetPort(request->connection),
                                    CdkConnection_GetPreferredAddress(request->connection));

   request->addResolveEntry = curl_slist_append(NULL, nameResolveStr);
   LDEBUG(0, (MODULE "CURLOPT_RESOLVE string %s", request->addResolveEntry->data));
   code = curl_easy_setopt(request->curl, CURLOPT_RESOLVE, request->addResolveEntry);
   if (code != CURLE_OK) {
      LERROR(0, (MODULE "Fail to add DNS resolve entry using string: %s", nameResolveStr));
   }

   g_free(nameResolveStr);

   EXIT;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CdkBasicHttpRemoveDNSResolveEntry --
 *
 *      Remove DNS resolve entry for the host+port pair from the DNS cache.
 *      Only remove it if Server IP is set, otherwise it may remove the resolve
 *      entry automatically added by libcurl in DNS cache.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

void
CdkBasicHttpRemoveDNSResolveEntry(CdkBasicHttpRequest *request) /* IN/OUT */
{
   char *nameResolveStr = NULL;
   CURLcode code = CURLE_OK;

   ENTRY;

   g_return_if_fail(request);
   g_return_if_fail(request->connection);

   if (!CdkConnection_GetPreferredAddress(request->connection) ||
       CdkConnection_GetHostnameType(request->connection) != CDK_ADDRESS_TYPE_NAME ||
       request->removeResolveEntry) {
      LDEBUG(0, (MODULE "Server IP is NULL or hostname is not name or the DNS "
                        "resolve entry has been removed."));
      EXIT;
   }

   nameResolveStr = g_strdup_printf("-%s:%u", CdkConnection_GetHostname(request->connection),
                                    CdkConnection_GetPort(request->connection));

   request->removeResolveEntry = curl_slist_append(NULL, nameResolveStr);
   code = curl_easy_setopt(request->curl, CURLOPT_RESOLVE, request->removeResolveEntry);
   if (code != CURLE_OK) {
      LERROR(0, (MODULE "Fail to remove DNS resolve entry using string: %s", nameResolveStr));
   }

   g_free(nameResolveStr);

   EXIT;
}
