/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */


#include <gtest/gtest.h>
#include "utMock.h"
#include "vncEncode.h"
#include "vncEncodeInt.h"

class VNCEncodeServerSetTopology : public ::testing::Test {};

/*
 * VNCEncodeServerSetTopology.ServerSetTopologyDeathTest
 *
 *     Check that we do blow up when trying to set a NULL topology.
 *
 */

#if defined(VMX86_BETA) || defined(VMX86_DEBUG)
TEST_F(VNCEncodeServerSetTopology, ServerSetTopologyDeathTest)
{
   VMOCK(&VNCLock_Acquire).Will([](VNCLock *lock, const char *owner) { return; });
   VMOCK(&VNCLock_Release).Will([](VNCLock *lock) { return; });
   VMOCK(&VNCEncodeAsync_InvalidateRegionEncoders).Will([](VNCEncoder *encoder) { return; });
   VMOCK(&VNCEncodeAsync_ResizeResubscribeRegionEncoders).Will([](VNCEncoder *encoder) { return; });
   VMOCK(&VNCEncodeCalculateBandwidthCap).Will([](VNCEncoder *encoder) { return; });
   VMOCK(&VNCEncodeNoteStateChange).Will([](VNCEncoder *encoder, uint32 dirty) { return; });

   VNCEncoder encoder = {0};

   encoder.lock = (VNCLock *)1;

   EXPECT_DEATH(VNCEncode_ServerSetTopology(&encoder, NULL, 0, NULL, NULL), "");
}
#endif


/*
 * VNCEncodeServerSetTopology.ServerSetTopologyZeroScreens
 *
 *     Check that we return early when trying to set zero screens with
 *     a screens ptr still available. The silenceEncoder case.
 *
 */

TEST_F(VNCEncodeServerSetTopology, ServerSetTopologyZeroScreens)
{
   VMOCK(&VNCLock_Acquire).Will([](VNCLock *lock, const char *owner) { return; });
   VMOCK(&VNCLock_Release).Will([](VNCLock *lock) { return; });
   VMOCK(&VNCEncodeAsync_InvalidateRegionEncoders).Will([](VNCEncoder *encoder) { return; });
   VMOCK(&VNCEncodeAsync_ResizeResubscribeRegionEncoders).Will([](VNCEncoder *encoder) { return; });
   VMOCK(&VNCEncodeCalculateBandwidthCap).Will([](VNCEncoder *encoder) { return; });
   VMOCK(&VNCEncodeNoteStateChange).Will([](VNCEncoder *encoder, uint32 dirty) { return; });

   VNCServerOSScreen screens[VNC_MAX_SCREENS] = {0};

   VNCEncoder encoder = {0};
   VNCServerOSCaptureType captureType;

   encoder.lock = (VNCLock *)1;

   VNCEncode_ServerSetTopology(&encoder, screens, 0, &captureType, NULL);

   EXPECT_EQ(captureType, VNC_SERVEROS_CAPTURE_TYPE_UNKNOWN);

   for (int i = 0; i < ARRAYSIZE(screens); i++) {
      EXPECT_EQ(encoder.screen[i].rect.top, 0);
      EXPECT_EQ(encoder.screen[i].rect.left, 0);
      EXPECT_EQ(encoder.screen[i].rect.bottom, 0);
      EXPECT_EQ(encoder.screen[i].rect.right, 0);
   }
}


/*
 * VNCEncodeServerSetTopology.ServerSetTopologyOneNullScreen
 *
 *     Check that we can set a zero sized one screen rect.
 *
 */

TEST_F(VNCEncodeServerSetTopology, ServerSetTopologyOneNullScreen)
{
   VMOCK(&VNCLock_Acquire).Will([](VNCLock *lock, const char *owner) { return; });
   VMOCK(&VNCLock_Release).Will([](VNCLock *lock) { return; });
   VMOCK(&VNCEncodeAsync_InvalidateRegionEncoders).Will([](VNCEncoder *encoder) { return; });
   VMOCK(&VNCEncodeAsync_ResizeResubscribeRegionEncoders).Will([](VNCEncoder *encoder) { return; });
   VMOCK(&VNCEncodeCalculateBandwidthCap).Will([](VNCEncoder *encoder) { return; });
   VMOCK(&VNCEncodeNoteStateChange).Will([](VNCEncoder *encoder, uint32 dirty) { return; });

   VNCServerOSScreen screens[VNC_MAX_SCREENS] = {0};

   VNCEncoder encoder = {0};

   encoder.lock = (VNCLock *)1;

   VNCEncode_ServerSetTopology(&encoder, screens, 1, NULL, NULL);

   for (int i = 0; i < ARRAYSIZE(screens); i++) {
      EXPECT_EQ(encoder.screen[i].rect.top, 0);
      EXPECT_EQ(encoder.screen[i].rect.left, 0);
      EXPECT_EQ(encoder.screen[i].rect.bottom, 0);
      EXPECT_EQ(encoder.screen[i].rect.right, 0);
   }

   EXPECT_EQ(encoder.numScreens, 1);
}


/*
 * VNCEncodeServerSetTopology.ServerSetTopologyOneScreen
 *
 *     Check that we can set a rect and normalize at 0,0.
 *
 */

TEST_F(VNCEncodeServerSetTopology, ServerSetTopologyOneScreen)
{
   VMOCK(&VNCLock_Acquire).Will([](VNCLock *lock, const char *owner) { return; });
   VMOCK(&VNCLock_Release).Will([](VNCLock *lock) { return; });
   VMOCK(&VNCEncodeAsync_InvalidateRegionEncoders).Will([](VNCEncoder *encoder) { return; });
   VMOCK(&VNCEncodeAsync_ResizeResubscribeRegionEncoders).Will([](VNCEncoder *encoder) { return; });
   VMOCK(&VNCEncodeCalculateBandwidthCap).Will([](VNCEncoder *encoder) { return; });
   VMOCK(&VNCEncodeNoteStateChange).Will([](VNCEncoder *encoder, uint32 dirty) { return; });

   VNCServerOSScreen screens[VNC_MAX_SCREENS] = {0};

   VNCEncoder encoder = {0};

   encoder.lock = (VNCLock *)1;

   Rect_SetXYWH(&screens[0].rect, 500, 600, 700, 800);

   VNCEncode_ServerSetTopology(&encoder, screens, 1, NULL, NULL);

   EXPECT_EQ(encoder.screen[0].rect.left, 0);
   EXPECT_EQ(encoder.screen[0].rect.top, 0);
   EXPECT_EQ(encoder.screen[0].rect.right, 700);
   EXPECT_EQ(encoder.screen[0].rect.bottom, 800);

   for (int i = 1; i < ARRAYSIZE(screens); i++) {
      EXPECT_EQ(encoder.screen[i].rect.left, 0);
      EXPECT_EQ(encoder.screen[i].rect.top, 0);
      EXPECT_EQ(encoder.screen[i].rect.right, 0);
      EXPECT_EQ(encoder.screen[i].rect.bottom, 0);
   }
}


/*
 * VNCEncodeServerSetTopology.ServerSetTopologyZeroScreenAfterOneScreen
 *
 *     Check that we can set a rect and then clear that rect as a topology
 *     change would do and ensure that our temporary screens becomes one.
 *
 */

TEST_F(VNCEncodeServerSetTopology, ServerSetTopologyZeroScreenAfterOneScreen)
{
   VMOCK(&VNCLock_Acquire).Will([](VNCLock *lock, const char *owner) { return; });
   VMOCK(&VNCLock_Release).Will([](VNCLock *lock) { return; });
   VMOCK(&VNCEncodeAsync_InvalidateRegionEncoders).Will([](VNCEncoder *encoder) { return; });
   VMOCK(&VNCEncodeAsync_ResizeResubscribeRegionEncoders).Will([](VNCEncoder *encoder) { return; });
   VMOCK(&VNCEncodeCalculateBandwidthCap).Will([](VNCEncoder *encoder) { return; });
   VMOCK(&VNCEncodeNoteStateChange).Will([](VNCEncoder *encoder, uint32 dirty) { return; });

   VNCServerOSScreen screens[VNC_MAX_SCREENS] = {0};

   VNCEncoder encoder = {0};

   encoder.lock = (VNCLock *)1;

   Rect_SetXYWH(&screens[0].rect, 500, 600, 700, 800);

   VNCEncode_ServerSetTopology(&encoder, screens, 1, NULL, NULL);

   EXPECT_EQ(encoder.screen[0].rect.left, 0);
   EXPECT_EQ(encoder.screen[0].rect.top, 0);
   EXPECT_EQ(encoder.screen[0].rect.right, 700);
   EXPECT_EQ(encoder.screen[0].rect.bottom, 800);

   for (int i = 1; i < ARRAYSIZE(screens); i++) {
      EXPECT_EQ(encoder.screen[i].rect.left, 0);
      EXPECT_EQ(encoder.screen[i].rect.top, 0);
      EXPECT_EQ(encoder.screen[i].rect.right, 0);
      EXPECT_EQ(encoder.screen[i].rect.bottom, 0);
   }

   VNCEncode_ServerSetTopology(&encoder, screens, 0, NULL, NULL);

   EXPECT_EQ(encoder.numScreens, 0);
   EXPECT_EQ(encoder.numTemporaryScreens, 1);
}


/*
 * VNCEncodeServerSetTopology.ServerSetTopologyInProgress
 *
 *     Check that a topology change is already in-progress and we
 *     don't modify the current topology.
 *
 */

TEST_F(VNCEncodeServerSetTopology, ServerSetTopologyInProgress)
{
   VMOCK(&VNCLock_Acquire).Will([](VNCLock *lock, const char *owner) { return; });
   VMOCK(&VNCLock_Release).Will([](VNCLock *lock) { return; });
   VMOCK(&VNCEncodeAsync_InvalidateRegionEncoders).Will([](VNCEncoder *encoder) { return; });
   VMOCK(&VNCEncodeAsync_ResizeResubscribeRegionEncoders).Will([](VNCEncoder *encoder) { return; });
   VMOCK(&VNCEncodeCalculateBandwidthCap).Will([](VNCEncoder *encoder) { return; });
   VMOCK(&VNCEncodeNoteStateChange).Will([](VNCEncoder *encoder, uint32 dirty) { return; });

   VNCServerOSScreen screens[VNC_MAX_SCREENS] = {0};

   VNCEncoder encoder = {0};

   encoder.lock = (VNCLock *)1;

   Rect_SetXYWH(&screens[0].rect, 500, 600, 700, 800);

   VNCEncode_ServerSetTopology(&encoder, screens, 1, NULL, NULL);

   EXPECT_EQ(encoder.screen[0].rect.left, 0);
   EXPECT_EQ(encoder.screen[0].rect.top, 0);
   EXPECT_EQ(encoder.screen[0].rect.right, 700);
   EXPECT_EQ(encoder.screen[0].rect.bottom, 800);

   for (int i = 1; i < ARRAYSIZE(screens); i++) {
      EXPECT_EQ(encoder.screen[i].rect.left, 0);
      EXPECT_EQ(encoder.screen[i].rect.top, 0);
      EXPECT_EQ(encoder.screen[i].rect.right, 0);
      EXPECT_EQ(encoder.screen[i].rect.bottom, 0);
   }

   VNCEncode_ServerSetTopology(&encoder, screens, 0, NULL, NULL);

   EXPECT_EQ(encoder.numScreens, 0);
   EXPECT_EQ(encoder.numTemporaryScreens, 1);

   VNCEncode_ServerSetTopology(&encoder, screens, 0, NULL, NULL);

   /* Check the same outcome as above */
   EXPECT_EQ(encoder.numScreens, 0);
   EXPECT_EQ(encoder.numTemporaryScreens, 1);
}


/*
 * VNCEncodeServerSetTopology.ServerSetTopologyOneScreenReactivated
 *
 *     Check that after a topology change we reactivate the screen.
 *
 */

TEST_F(VNCEncodeServerSetTopology, ServerSetTopologyOneScreenReactivated)
{
   VMOCK(&VNCLock_Acquire).Will([](VNCLock *lock, const char *owner) { return; });
   VMOCK(&VNCLock_Release).Will([](VNCLock *lock) { return; });
   VMOCK(&VNCEncodeAsync_InvalidateRegionEncoders).Will([](VNCEncoder *encoder) { return; });
   VMOCK(&VNCEncodeAsync_ResizeResubscribeRegionEncoders).Will([](VNCEncoder *encoder) { return; });
   VMOCK(&VNCEncodeCalculateBandwidthCap).Will([](VNCEncoder *encoder) { return; });
   VMOCK(&VNCEncodeNoteStateChange).Will([](VNCEncoder *encoder, uint32 dirty) { return; });

   VNCServerOSScreen screens[VNC_MAX_SCREENS] = {0};

   VNCEncoder encoder = {0};

   encoder.lock = (VNCLock *)1;

   Rect_SetXYWH(&screens[0].rect, 500, 600, 700, 800);

   VNCEncode_ServerSetTopology(&encoder, screens, 1, NULL, NULL);

   EXPECT_EQ(encoder.screen[0].rect.left, 0);
   EXPECT_EQ(encoder.screen[0].rect.top, 0);
   EXPECT_EQ(encoder.screen[0].rect.right, 700);
   EXPECT_EQ(encoder.screen[0].rect.bottom, 800);

   for (int i = 1; i < ARRAYSIZE(screens); i++) {
      EXPECT_EQ(encoder.screen[i].rect.left, 0);
      EXPECT_EQ(encoder.screen[i].rect.top, 0);
      EXPECT_EQ(encoder.screen[i].rect.right, 0);
      EXPECT_EQ(encoder.screen[i].rect.bottom, 0);
   }

   VNCEncode_ServerSetTopology(&encoder, screens, 0, NULL, NULL);

   EXPECT_EQ(encoder.numScreens, 0);
   EXPECT_EQ(encoder.numTemporaryScreens, 1);

   VNCEncode_ServerSetTopology(&encoder, screens, 1, NULL, NULL);

   EXPECT_EQ(encoder.screen[0].rect.left, 0);
   EXPECT_EQ(encoder.screen[0].rect.top, 0);
   EXPECT_EQ(encoder.screen[0].rect.right, 700);
   EXPECT_EQ(encoder.screen[0].rect.bottom, 800);

   for (int i = 1; i < ARRAYSIZE(screens); i++) {
      EXPECT_EQ(encoder.screen[i].rect.left, 0);
      EXPECT_EQ(encoder.screen[i].rect.top, 0);
      EXPECT_EQ(encoder.screen[i].rect.right, 0);
      EXPECT_EQ(encoder.screen[i].rect.bottom, 0);
   }
}
