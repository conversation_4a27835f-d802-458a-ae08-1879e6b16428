name: horizoncommon
run-name: >
  ${{ github.workflow }}
  ${{ github.event_name == 'workflow_dispatch' && inputs.buildtype ||
      (github.event_name == 'pull_request' && 'obj' || 'beta') }}
  ${{ github.event_name == 'pull_request' &&
      format(' - {0} (#{1})', github.event.pull_request.title, github.event.pull_request.number)
      || '' }}
concurrency:
  # This section ensure that multiple PR pushes will cancel superseded builds.
  # Builds on main, release/* and feature/* branches will not be canceled to
  # assist in root causing build breakages.
  group: ${{ github.workflow }}-${{
      (github.ref == 'refs/heads/main' ||
       startsWith(github.ref, 'refs/heads/feature/') ||
       startsWith(github.ref, 'refs/heads/release/')) &&
      github.run_id || github.ref
    }}-${{ inputs.buildtype }}
  cancel-in-progress: true
on:
  pull_request:
  push:
    branches:
      - 'main'
      - 'release/**'
      - 'feature/**'
    paths-ignore:
      - .github/RunnerResetConfig.json
      - .github/workflows/runner_app_config.yaml
      - .github/workflows/rx-devop-nightly-*.yaml
  workflow_dispatch:
    inputs:
      buildtype:
        type: choice
        description: Build type
        required: True
        default: beta
        options:
          - beta
          - obj
          - release
      conan_develop:
        type: boolean
        description: I am testing conan packages and need to enable the conan-develop remote
        required: True
        default: false
      conan_sandbox:
        type: boolean
        description: I am testing conan compiler upgrade and need to enable the conan-sandbox remote
        required: false
        default: false
env:
  BUILDTYPE: ${{ github.event_name == 'workflow_dispatch' && inputs.buildtype ||
                 (github.event_name == 'pull_request' && 'obj' || 'beta') }}
jobs:
  file-check:
    runs-on: [lnxbuild-gh, self-hosted]
    permissions:
      contents: read
      pull-requests: read
    outputs:
      enable-build: ${{ steps.filter.outputs.result }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          sparse-checkout: .github/workflow-filters.yaml
          sparse-checkout-cone-mode: false

      - name: Check if build should be run
        id: filter
        uses: euc-eng/filter-paths@v1
        with:
          filtersFile: .github/workflow-filters.yaml
          label: horizoncommon

  build-horizoncommon:
    needs: file-check
    if: ${{ needs.file-check.outputs.enable-build == 'true' }}
    strategy:
      matrix:
        label: [macbuildp-atl, lnxbuild-gh, winhzn-gh]
    runs-on:
      - ${{ matrix.label }}
      - self-hosted
      # MacOS needs the sip_disabled label too. Use a duplicate label "self-hosted"
      # for the else case because yaml seemingly doesn't offer any alternatives.
      - ${{ matrix.label == 'macbuildp-atl' && 'sip_disabled' || 'self-hosted' }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          lfs: true

      - name: Run SCons
        uses: ./.github/actions/scons
        with:
          buildtype: ${{ github.event_name == 'pull_request' && 'obj' || inputs.buildtype }}
          product: horizoncommon
          conanDevelop: ${{ inputs.conan_develop }}
          conanSandbox: ${{ inputs.conan_sandbox }}
          artifactoryApiToken: ${{ secrets.ARTIFACTORY_GITHUB_DELIVERABLES_API_TOKEN }}
          artifactoryUrl: ${{ vars.ARTIFACTORY_GITHUB_DELIVERABLES_URL }}
          artifactoryReadToken: ${{ secrets.ORG_OMNISSA_ART_READONLY_TOKEN }}
          artifactoryReadUser: ${{ vars.ORG_OMNISSA_ART_READONLY_USER }}
          azureSigningTenantId: ${{ vars.AZURE_BINARY_SIGNING_TENANT_ID }}
          azureSigningTestKeyCertName: ${{ vars.AZURE_BINARY_SIGNING_OFFICIAL_KEY_CERT_NAME }}
          azureSigningTestKeyClientId: ${{ vars.AZURE_BINARY_SIGNING_OFFICIAL_KEY_CLIENT_ID }}
          azureSigningTestKeySecret: ${{ secrets.AZURE_BINARY_SIGNING_OFFICIAL_KEY_CLIENT_SECRET }}
          azureSigningUrl: ${{ vars.AZURE_BINARY_SIGNING_OFFICIAL_KEY_URL }}
          # only generate compile_commands.json on windows for now
          extraParams: ${{ runner.os == 'Windows' &&
                           'COMPILE_DB=1 compiledb horizoncommon' || '' }} FOSSA_DEPS=1
        
      - name: Run Fossa
        if: ${{ inputs.buildtype == 'release' || github.event_name == 'pull_request' }}
        uses: ./.github/actions/fossa
        with:
          product: 'horizoncommon'
          fossa-api-key: ${{ secrets.ORG_OMNISSA_FOSSA_KEY }}
          omnissaArtifactoryToken: ${{ secrets.ORG_OMNISSA_ART_READONLY_TOKEN }}

  UT:
    needs: build-horizoncommon
    if: ${{ vars.ENABLE_UNIT_TEST_HOSTED_IN_MV == 'true' }}
    secrets: inherit
    uses: ./.github/workflows/horizoncommon_ut.yaml
    with:
      buildtype: ${{ github.event_name == 'pull_request' && 'obj' || inputs.buildtype }}

  sonar-upload-windows:
    needs:
      - build-horizoncommon
      - UT
    runs-on: [winhzn-gh, self-hosted]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Upload results to SonarQube
        uses: ./.github/actions/testframework/cart-sonar
        with:
          sonarProjectKey: cart-horizoncommon-windows
          productWorkflowJob: build-horizoncommon-winhzn-gh
          utJobs: cedar-tests-*
          artifactoryApiToken: ${{ secrets.ARTIFACTORY_GITHUB_DELIVERABLES_API_TOKEN }}
          artifactoryBaseUrl: ${{ vars.ARTIFACTORY_GITHUB_DELIVERABLES_URL }}
          artifactoryReadToken: ${{ secrets.ORG_OMNISSA_ART_READONLY_TOKEN }}
          artifactoryReadUser: ${{ vars.ORG_OMNISSA_ART_READONLY_USER }}
          languageMode: C++
          compileCommands: compile_commands.json
          sources: bora/apps/cedar
          sonarHostUrl: ${{ vars.ORG_EUC_SONAR_HOST }}
          sonarToken: ${{ secrets.ORG_EUC_SONAR_TOKEN }}
          conanDevelop: ${{ inputs.conan_develop }}
          conanSandbox: ${{ inputs.conan_sandbox }}

  horizoncommon-overall-status:
    needs:
      - build-horizoncommon
      - UT
      - sonar-upload-windows
    if: ${{ !cancelled() }}
    timeout-minutes: 10
    runs-on: [lnxbuild-gh, self-hosted]
    permissions:
      actions: write
      contents: read
      pull-requests: read
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Check overall workflow status
        uses: ./.github/actions/check-status
        with:
          workflowId: 'horizoncommon.yaml'
          jobs: ${{ toJson(needs) }}
          buildtype: ${{ inputs.buildtype }}
          slackWebhookUrl: ${{ secrets.CART_SLACK_WEBHOOK_URL }}
          slackBranches: ${{ vars.DAILY_BUILD_BRANCHES }}
