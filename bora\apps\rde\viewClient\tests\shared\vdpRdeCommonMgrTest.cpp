/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * vdpRdeCommonMgrTest.cpp -
 *
 *    Test functions of vdpRdeCommonMgr.c
 */

#include <gmock/gmock.h>
#include <gtest/gtest.h>
#include "utMock.h"

#include "shared/commonSvc_defines.h"
#include "vdpRdeCommonMgr.h"
#include "viewClient.h"
#include "viewControlMgr.h"

using testing::_;


/*
 *-----------------------------------------------------------------------------
 *
 * vdpRdeCommonMgrUnitTest
 *
 *      Test functions in vdpRdeCommonMgr.
 *
 *-----------------------------------------------------------------------------
 */

class vdpRdeCommonMgrUnitTest : public ::testing::Test {
public:
   static void SetUpTestCase();
   static void TearDownTestCase();

protected:
   virtual void SetUp() override;
   virtual void TearDown() override;
};

extern "C" {
void ViewClient_SetAllowArmNoAntiKeyloggerStatus(Bool enable);
Bool VDPRdeCommonMgr_OnNotificationWrapperForUT(void *context, const char *sourceToken,
                                                const void *cookie, const void *data);
}

/*
 *-----------------------------------------------------------------------------
 *
 * vdpRdeCommonMgrUnitTest::SetUpTestCase --
 *
 *      SetUp for entire test suite.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

void
vdpRdeCommonMgrUnitTest::SetUpTestCase()
{
   return;
}


/*
 *-----------------------------------------------------------------------------
 *
 * vdpRdeCommonMgrUnitTest::TearDownTestCase --
 *
 *      TearDown for entire test suite.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

void
vdpRdeCommonMgrUnitTest::TearDownTestCase()
{
   return;
}


/*
 *-----------------------------------------------------------------------------
 *
 * vdpRdeCommonMgrUnitTest::SetUp --
 *
 *      SetUp for each test case.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

void
vdpRdeCommonMgrUnitTest::SetUp()
{
   return;
}


/*
 *-----------------------------------------------------------------------------
 *
 * vdpRdeCommonMgrUnitTest::TearDown --
 *
 *      TearDown for each test case.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

void
vdpRdeCommonMgrUnitTest::TearDown()
{
   return;
}


/*
 *-----------------------------------------------------------------------------
 *
 * vdpRdeCommonMgrUnitTest::AllowArmNoAntiKeyloggerStatus --
 *
 *      Test API function related to AllowArmNoAntiKeyloggerStatus.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(vdpRdeCommonMgrUnitTest, AllowArmNoAntiKeyloggerStatus)
{
   for (uint64 status = 0; status <= 1; status++) {
      std::map<std::string, std::vector<uint8>> options;
      options["allowArmNoAntiKeylogger"] = {status ? (uint8)(1) : (uint8)(0)};
      std::vector<uint8> optionBuffer;
      for (auto option : options) {
         uint32 keySize = option.first.size();
         optionBuffer.insert(optionBuffer.end(), (uint8 *)&keySize,
                             (uint8 *)&keySize + sizeof keySize);
         optionBuffer.insert(optionBuffer.end(), (uint8 *)option.first.c_str(),
                             (uint8 *)option.first.c_str() + keySize);
         uint32 valSize = option.second.size();
         optionBuffer.insert(optionBuffer.end(), (uint8 *)&valSize,
                             (uint8 *)&valSize + sizeof valSize);
         optionBuffer.insert(optionBuffer.end(), (uint8 *)option.second.data(),
                             (uint8 *)option.second.data() + valSize);
      }

      uint8 featureEnableStatus = 1;
      RdeChannelMessage channelMsg{};
      channelMsg.msgSize = sizeof channelMsg + optionBuffer.size();
      channelMsg.msgType = RDE_SET_CHANNEL_MSG_TYPE(RDE_CHANNEL_APP_PROTECTION_MSG,
                                                    RDE_CHANNEL_BLOCK_KEY_LOGGER_ENABLE_MSG);

      std::vector<uint8> buffer;
      buffer.insert(buffer.end(), (uint8 *)&channelMsg,
                    (uint8 *)&channelMsg + sizeof channelMsg - sizeof(uint8));
      buffer.insert(buffer.end(), (uint8 *)&featureEnableStatus,
                    (uint8 *)&featureEnableStatus + sizeof featureEnableStatus);
      buffer.insert(buffer.end(), optionBuffer.begin(), optionBuffer.end());

      VMOCK(&ViewControlMgr_GHIUpdateFromGuest).ExpectCall(GHI_GUEST_RDE_COMMON_GENERIC, _, _);
      VMOCK(&ViewClient_SetAllowArmNoAntiKeyloggerStatus).ExpectCall(status ? TRUE : FALSE);

      VDPRdeCommonMgr_OnNotificationWrapperForUT(NULL, NULL, NULL, buffer.data());
   }
}