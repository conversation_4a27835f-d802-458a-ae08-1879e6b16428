/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * ioEngineTest.cpp
 *
 *    Test cases for ioEngine.cpp.
 */


#include <cstring>
#include "ioEngineTest.h"
#include "cache.h"
#include "convert.h"
#include "sdrNtdef.h"
#include "sdrTransport.h"
#include "SessionMgr.h"
#include "utSupportCommon.h"

void
IoEngineTestFixture::SetUp()
{
   IoEngine::Initialize();
   _sdrServer = std::make_shared<SDRServer>();
   _ovlWrapper = std::make_shared<OVL_WRAPPER>();
   _diskGeo = std::make_shared<DISK_GEOMETRY>();
   _sdrServer->Start();
   _sdrServer->InitAsyncFetchIfNotYet();
}

void
IoEngineTestFixture::TearDown()
{
   IoEngine::Uninitialize();
   _sdrServer->Stop();
}

TEST_F(IoEngineTestFixture, DriverQUERYRequestHandle)
{
   _request.resize(sizeof(DR_REQ_HEADER) + sizeof(DR_QUERY_DISK_REQ));
   ULONG64 queryDiskIoContext = 0x1234567887654321;
   ULONG diskId = 0x12345678;
   ULONG diskLetter = 'D';
   WCHAR devicePath[32] = LR"(\\.\D:)";
   SessionMgr::GetInstance().GetSessionById(1, true);
   DR_REQ_HEADER *reqHeader = (DR_REQ_HEADER *)_request.data();
   DR_QUERY_DISK_REQ *queryDiskReq = (DR_QUERY_DISK_REQ *)(reqHeader + 1);
   reqHeader->ioContext = queryDiskIoContext;
   reqHeader->major = DR_MJ_QUERY_DISK;
   reqHeader->minor = diskLetter;
   reqHeader->dataLength = sizeof(DR_QUERY_DISK_REQ);
   memcpy(queryDiskReq->devicePath, devicePath, sizeof(devicePath));
   queryDiskReq->diskId = diskId;

   _ovlWrapper->sdrServer = _sdrServer.get();
   _ovlWrapper->serverId = this;
   _ovlWrapper->sessionId = 1;
   _ovlWrapper->buffer = (char *)_request.data();

   Session *session = SessionMgr::GetInstance().GetSessionById(1);
   session->GetSDRServer()->_notifHandle = SDR_UT_FAKE_HANDLE_ID;

   BOOL succeeded = PostQueuedCompletionStatus(IoEngine::GetCompletionPort(),
                                               sizeof(DR_REQ_HEADER) + sizeof(DR_QUERY_DISK_REQ), 0,
                                               (LPOVERLAPPED)_ovlWrapper.get());
   if (!succeeded) {
      LOG_ERROR("Failed to call PostQueuedCompletionStatus, error %d.", GetLastError());
   }
   ASSERT_TRUE(succeeded);
}

TEST_F(IoEngineTestFixture, DriverREADRequestHandle)
{
   char diskContentr[] = "This is SDR read testing data.";
   _request.resize(sizeof(DR_REQ_HEADER) + sizeof(DR_RW_REQ));
   ULONG64 queryDiskIoContext = 0x1234567887654321;
   ULONG diskId = 0x12345678;
   ULONG diskLetter = 'D';
   WCHAR devicePath[32] = LR"(\\.\D:)";

   SessionMgr::GetInstance().GetSessionById(1, true);
   DR_REQ_HEADER *reqHeader = (DR_REQ_HEADER *)_request.data();
   _DR_RW_REQ *readDiskReq = (DR_RW_REQ *)(reqHeader + 1);
   reqHeader->ioContext = queryDiskIoContext;
   reqHeader->major = DR_MJ_READ;
   reqHeader->minor = diskLetter;
   reqHeader->dataLength = sizeof(DR_RW_REQ);

   readDiskReq->diskId = diskId;
   readDiskReq->byteOffset = 0;
   readDiskReq->length = sizeof(diskContentr);

   _ovlWrapper->sdrServer = _sdrServer.get();
   _ovlWrapper->serverId = this;
   _ovlWrapper->sessionId = 1;
   _ovlWrapper->buffer = (char *)_request.data();

   Session *session = SessionMgr::GetInstance().GetSessionById(1);
   session->GetSDRServer()->_notifHandle = SDR_UT_FAKE_HANDLE_ID;

   BOOL succeeded = PostQueuedCompletionStatus(IoEngine::GetCompletionPort(),
                                               sizeof(DR_REQ_HEADER) + sizeof(DR_RW_REQ), 0,
                                               (LPOVERLAPPED)_ovlWrapper.get());
   if (!succeeded) {
      LOG_ERROR("Failed to call PostQueuedCompletionStatus, error %d.", GetLastError());
   }
   ASSERT_TRUE(succeeded);
}

TEST_F(IoEngineTestFixture, DriverWRITERequestHandle)
{
   char writeContent[] = "This is SDR write testing data.";
   _request.resize(sizeof(DR_REQ_HEADER) + sizeof(DR_RW_REQ) + sizeof(writeContent));
   ULONG64 queryDiskIoContext = 0x123456788765432a;
   ULONG diskId = 0x12345678;
   ULONG diskLetter = 'D';
   WCHAR devicePath[32] = LR"(\\.\D:)";

   SessionMgr::GetInstance().GetSessionById(1, true);
   DR_REQ_HEADER *reqHeader = (DR_REQ_HEADER *)_request.data();
   _DR_RW_REQ *writeDiskReq = (DR_RW_REQ *)(reqHeader + 1);

   reqHeader->ioContext = queryDiskIoContext;
   reqHeader->major = DR_MJ_WRITE;
   reqHeader->minor = diskLetter;
   reqHeader->dataLength = sizeof(DR_RW_REQ) + sizeof(writeContent);

   writeDiskReq->diskId = diskId;
   writeDiskReq->byteOffset = 0;
   writeDiskReq->length = sizeof(writeContent);

   Session *session = SessionMgr::GetInstance().GetSessionById(1);
   session->GetSDRServer()->_notifHandle = SDR_UT_FAKE_HANDLE_ID;

   uint8 *reqData = (uint8 *)(writeDiskReq + 1);
   memcpy(reqData, writeContent, sizeof(writeContent));

   _ovlWrapper->sdrServer = _sdrServer.get();
   _ovlWrapper->serverId = this;
   _ovlWrapper->sessionId = 1;
   _ovlWrapper->buffer = (char *)_request.data();

   BOOL succeeded = PostQueuedCompletionStatus(IoEngine::GetCompletionPort(),
                                               sizeof(DR_REQ_HEADER) + sizeof(DR_RW_REQ), 0,
                                               (LPOVERLAPPED)_ovlWrapper.get());
   if (!succeeded) {
      LOG_ERROR("Failed to call PostQueuedCompletionStatus, error %d.", GetLastError());
   }
   ASSERT_TRUE(succeeded);
}

TEST_F(IoEngineTestFixture, DriverEmptyRequestHandle)
{
   char writeContent[] = "This is SDR write testing data.";
   _request.resize(sizeof(DR_REQ_HEADER) + sizeof(DR_RW_REQ) + sizeof(writeContent));
   ULONG64 queryDiskIoContext = 0x123456788765432a;
   ULONG diskId = 0x12345678;
   ULONG diskLetter = 'D';
   WCHAR devicePath[32] = LR"(\\.\D:)";

   SessionMgr::GetInstance().GetSessionById(1, true);
   DR_REQ_HEADER *reqHeader = (DR_REQ_HEADER *)_request.data();
   _DR_RW_REQ *writeDiskReq = (DR_RW_REQ *)(reqHeader + 1);

   reqHeader->ioContext = queryDiskIoContext;
   reqHeader->major = DR_MJ_WRITE;
   reqHeader->minor = diskLetter;
   reqHeader->dataLength = sizeof(DR_RW_REQ) + sizeof(writeContent);

   writeDiskReq->diskId = diskId;
   writeDiskReq->byteOffset = 0;
   writeDiskReq->length = sizeof(writeContent);

   uint8 *reqData = (uint8 *)(writeDiskReq + 1);
   memcpy(reqData, writeContent, sizeof(writeContent));

   _ovlWrapper->sdrServer = _sdrServer.get();
   _ovlWrapper->serverId = this;
   _ovlWrapper->sessionId = 1;
   _ovlWrapper->buffer = (char *)_request.data();

   BOOL succeeded = PostQueuedCompletionStatus(IoEngine::GetCompletionPort(), 0, 0,
                                               (LPOVERLAPPED)_ovlWrapper.get());
   if (!succeeded) {
      LOG_ERROR("Failed to call PostQueuedCompletionStatus, error %d.", GetLastError());
   }
   ASSERT_TRUE(succeeded);
}

/*
 * This test is disabled due to memory corruption. Fixing and re-enabling it is tracked in
 * https://omnissa.atlassian.net/browse/VCART-6669
 *
 * NOTE: This test can't be disabled using `DISABLED_` prefix because that causes the test
 * compilation to fail due to an inability to access private variable `_notifHandle`.
 */
#if 0
TEST_F(IoEngineTestFixture, OnClientResponseCacheHit)
{
   // Fake content data with 1024 size
   char readContent[1024] = {0};
   for (int i = 0; i < 1023; i++) {
      readContent[i] = 'A' + (i % 26);
   }
   readContent[1023] = '\0';
   ULONG64 queryDiskIoContext = 0x0000000000000001;
   *_diskGeo = {{.QuadPart = 100}, FixedMedia, 4, 256, 1024};
   DR_RW_REQ fakeRwReq = {.diskId = 1, .byteOffset = 0x1000, .length = 4096};
   bool writable = true;
   // Set cache size to 1024 to meet fake content data
   CacheSetChunkSize(1);
   CacheBioSet(queryDiskIoContext, fakeRwReq);
   SessionMgr::GetInstance().GetSessionById(1, true);
   size_t responseSize =
      sizeof(DWORD) + sizeof(DR_RSP_HEADER) + sizeof(DR_RW_RSP) + sizeof(readContent);
   void *response = malloc(responseSize);
   DWORD *sessionId = static_cast<DWORD *>(response);
   *sessionId = 1;
   DR_RSP_HEADER *rspHeader = reinterpret_cast<DR_RSP_HEADER *>(sessionId + 1);
   DR_RW_RSP *readDiskRsp = (DR_RW_RSP *)(rspHeader + 1);
   void *content = (void *)(readDiskRsp + 1);

   rspHeader->ioContext = queryDiskIoContext;
   rspHeader->ioStatus.Status = STATUS_SUCCESS;
   rspHeader->ioStatus.Information = 0;
   rspHeader->dataLength = sizeof(DR_QUERY_DISK_RSP);
   readDiskRsp->length = sizeof(readContent);
   memcpy(content, readContent, sizeof(readContent));

   _ovlWrapper->sdrServer = _sdrServer.get();
   _ovlWrapper->serverId = this;
   _ovlWrapper->sessionId = 1;
   _ovlWrapper->buffer = (char *)response;

   Session *session = SessionMgr::GetInstance().GetSessionById(1);
   session->GetSDRServer()->_notifHandle = SDR_UT_FAKE_HANDLE_ID;

   void *rspProto = nullptr;
   uint32 rspProtoSize = 0;
   Rsp2Proto((CHAR *)response + sizeof(DWORD), responseSize - sizeof(DWORD), &rspProto,
             &rspProtoSize, DR_MJ_QUERY_DISK);
   void *newMemory = malloc(rspProtoSize + sizeof(DWORD));
   DWORD session_Id = 1;
   // Copy the session ID to the beginning of the new memory block
   memcpy(newMemory, &session_Id, sizeof(DWORD));
   memcpy((char *)newMemory + sizeof(DWORD), rspProto, rspProtoSize);
   free(rspProto);
   TransStatus status =
      IoEngine::OnClientResponseReceived(newMemory, rspProtoSize + sizeof(DWORD), nullptr);

   EXPECT_EQ(status, TRANS_STATUS_SUCCESS);
}
#endif

TEST_F(IoEngineTestFixture, OnClientResponseCacheMiss)
{
   char readContent[1024] = {0};
   for (int i = 0; i < 1023; i++) {
      readContent[i] = 'A' + (i % 26);
   }
   readContent[1023] = '\0';
   ULONG64 queryDiskIoContext = 0x0000000000000001;
   *_diskGeo = {{.QuadPart = 100}, FixedMedia, 4, 256, 1024};
   DR_RW_REQ fakeRwReq = {.diskId = 1, .byteOffset = 0x1000, .length = 4096};
   bool writable = true;
   CacheBioSet(queryDiskIoContext, fakeRwReq);
   SessionMgr::GetInstance().GetSessionById(1, true);
   size_t responseSize =
      sizeof(DWORD) + sizeof(DR_RSP_HEADER) + sizeof(DR_RW_RSP) + sizeof(readContent);
   void *response = malloc(responseSize);
   DWORD *sessionId = static_cast<DWORD *>(response);
   *sessionId = 1;
   DR_RSP_HEADER *rspHeader = reinterpret_cast<DR_RSP_HEADER *>(sessionId + 1);
   DR_RW_RSP *readDiskRsp = (DR_RW_RSP *)(rspHeader + 1);
   void *content = (void *)(readDiskRsp + 1);

   rspHeader->ioContext = queryDiskIoContext;
   rspHeader->ioStatus.Status = STATUS_SUCCESS;
   rspHeader->ioStatus.Information = 0;
   rspHeader->dataLength = sizeof(DR_QUERY_DISK_RSP);
   readDiskRsp->length = sizeof(readContent);
   memcpy(content, readContent, sizeof(readContent));

   _ovlWrapper->sdrServer = _sdrServer.get();
   _ovlWrapper->serverId = this;
   _ovlWrapper->sessionId = 1;
   _ovlWrapper->buffer = (char *)response;

   Session *session = SessionMgr::GetInstance().GetSessionById(1);
   session->GetSDRServer()->_notifHandle = SDR_UT_FAKE_HANDLE_ID;

   void *rspProto = nullptr;
   uint32 rspProtoSize = 0;
   Rsp2Proto((CHAR *)response + sizeof(DWORD), responseSize - sizeof(DWORD), &rspProto,
             &rspProtoSize, DR_MJ_QUERY_DISK);
   void *newMemory = malloc(rspProtoSize + sizeof(DWORD));
   DWORD session_Id = 1; // Example session ID

   // Copy the session ID to the beginning of the new memory block
   memcpy(newMemory, &session_Id, sizeof(DWORD));
   memcpy((char *)newMemory + sizeof(DWORD), rspProto, rspProtoSize);
   free(rspProto);
   TransStatus status =
      IoEngine::OnClientResponseReceived(newMemory, rspProtoSize + sizeof(DWORD), nullptr);

   EXPECT_EQ(status, TRANS_STATUS_SUCCESS);
}