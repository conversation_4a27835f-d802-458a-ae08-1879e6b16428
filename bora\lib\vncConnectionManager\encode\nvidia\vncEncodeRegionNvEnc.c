/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 *
 * vncEncodeRegionNvEnc.c --
 *
 *      Encoding for whole frames with NVIDIA's HW H264 encoder
 *      using the NVENC API.
 *
 *      The video region encoders usually operate on full frames and
 *      identify unchanged regions at the macroblock level.
 */

#include "vncEncodeRegionInt.h"
#include "vncEncodeRegionUtil.h"
#include "vncUtilInt.h"

#if defined(NVENC_SDK_SUFFIX)
#   define _VNCENCODEREGION(s, f) VNCEncodeRegionNvEnc##s##_##f
#   define VNCENCODEREGION(s, f) _VNCENCODEREGION(s, f)
#else
#   define _VNCENCODEREGION(s, f) VNCEncodeRegionNvEnc_##f
#   define VNCENCODEREGION(s, f) _VNCENCODEREGION(s, f)
#endif

#include <float.h>
#include "config.h"
#include "vm_basic_types.h"
#include "dll_load_defs.h"
#include "err.h"
#include "posix.h"
#include "str.h"
#include "vncEncodeMP4Rect.h"
#include "cityHash.h"
#include "rasterCursor.h"
#include "vthread.h"
#include "userlock.h"
#include "hznPoll.h"
#include "vncAgeMap.h"
#include "vncFreqMap.h"
#include "vncHeatMap.h"

#if defined(_WIN32)
#   include <windows.h>
#   include <versionhelpers.h>
/*
 * Defining COBJMACROS allows access to COM pointer related macro
 * (IDXGIFactory1_EnumAdapters, ID3D11Device_Release,etc) that makes
 * code more readable.
 */
#   define COBJMACROS
#   include <d3d11.h>
#   include <dxgi.h>
#   include "nvapi.h"
#elif defined(__linux__)
/* Required for the Linux GL routines */
#   include "vncServerLinuxGL.h"
typedef void *HANDLE;
#endif

#include "nvml.h"
#include "nvEncodeAPI.h"

#if !defined(UNUSED)
#   define UNUSED(x) ((void)(x))
#endif

#define LGPFX "RegEnc-NvEnc: "

#define _PRIGUID "{%08x-%04x-%04x-%02x%02x%02x%02x%02x%02x%02x%02x}"

#define NV_ENC_FAILED(x) ((x) != NV_ENC_SUCCESS)
#define NV_ENC_SUCCEEDED(x) ((x) == NV_ENC_SUCCESS)

#define LEGACY_NVENCAPI_MAJOR_VERSION 8

#define H264_MIN_QP 0
#define H264_MAX_QP 51

#define AV1_MAX_QP 255
#define AV1_QP_SCALE (AV1_MAX_QP / H264_MAX_QP)

/* Define a sane floor for the encoder bandwidth, in bits per second. */
#define MIN_BITRATE_BITS_PER_SEC (64 * 1024)

/*
 * Global bandwidth cap for the encoder to accommodate most clients, as per the
 * Level 4.1 Main profile max video bit rate, in bits per second.
 */
#define MAX_BITRATE_BITS_PER_SEC (50 * 1024 * 1024)

#define QUALBOOSTDELAY_MS_MIN 10
#define QUALBOOSTDELAY_MS_MAX 5000

#include "vncEncodeManager.h"

#define CAPACITY_PERIOD_SEC 10

/* H.264 libvnc constants */
#define MBWIDTH 16 /* H.264 MacroBlock width */

/* Constants chosen through trial and error for MB cost estimation. */

#define MB_COST_SCALAR_MIN 1
#define MB_COST_SCALAR_QUAL 20
#define MB_COST_SCALAR_MAX 999

/* Bandwidth saving MODES */
typedef enum bwSavingMode {
   BWSAVING_OFF,
   BWSAVING_INTERACTIVE,
   BWSAVING_NON_INTERACTIVE
} bwSavingMode_t;

/* Input buffer macro for the encoder */
#define TOTAL_INPUT_RESOURCE 2
#define PRIMARY_INPUT_RESOURCE 0

/* Utility macros */
#define PERCENT(num, den) ((den) == 0 ? 100 : 100 * (num) / (den))

#define NVQUERY(nvenc, nvGUID, theCAPbit, capsVal)                                                 \
   do {                                                                                            \
      NV_ENC_CAPS_PARAM capsParam = {0};                                                           \
      NVENCSTATUS nvstat;                                                                          \
      capsParam.version = NV_ENC_CAPS_PARAM_VER;                                                   \
      capsParam.capsToQuery = theCAPbit;                                                           \
      nvstat = regEnc->encodeFns.nvEncGetEncodeCaps(nvenc, nvGUID, &capsParam, &capsVal);          \
      ASSERT(nvstat == NV_ENC_SUCCESS);                                                            \
      REGENCLG0("NVQUERY: %-40s %6u", #theCAPbit, val);                                            \
   } while (0);

#if defined(VMX86_DEVEL) || defined(VMX86_DEBUG)
#   define TIMESTAMP_ENABLE 1
#endif

#if TIMESTAMP_ENABLE
#   define NOTE_TIME(h) VNCEncodeRegionNoteTime(h)
#   define PRINT_TIMES(h, a) VNCEncodeRegionPrintTimes(h, a)
#else
#   define NOTE_TIME(h)
#   define PRINT_TIMES(h, a)
#endif

typedef struct VNCRegionEncoderNvEnc {
   struct VNCRegionEncoder base;

   HANDLE hNVEnc;

   NV_ENCODE_API_FUNCTION_LIST encodeFns;

   HANDLE pSharedHandle[TOTAL_INPUT_RESOURCE];

#ifdef _WIN32
   ID3D11Device *pD3d11Device;
   ID3D11DeviceContext *pD3d11DeviceContext;
   CRITICAL_SECTION csD3d11DeviceContext;
   ID3D11Texture2D *pD3d11Texture[TOTAL_INPUT_RESOURCE];
   ID3D11Texture2D *pD3d11TextureMappable[TOTAL_INPUT_RESOURCE];
   IDXGIKeyedMutex *pD3d11TextureKeyedMutex[TOTAL_INPUT_RESOURCE];
   int dxgiAdapterNum;
#endif
#ifdef __linux__
   vncServerLinuxGL *glCtx;
   void *createdContext;
#endif

   int frameNumber;
   uint32 encodeAsH264;

   uint8 maxQP;
   uint8 minQP;
   uint32 mbCostQual;
   int qualBoostDelayMs;

   /* bwSaving variables */
   Bool bwSavingEnabled;         /* BW saving state (ENABLED/DISABLED) */
   bwSavingMode_t bwSavingMode;  /* BW saving mode */
   Bool hotRegionActive;         /* Tracks hotregion bw saving state */
   int nvencBwTargetPercent;     /* Adjust the bw target [25-100]% */
   int nvencHotRegionMinFPS;     /* Min FPS to consider a region hot */
   int nvencHotRegionDeltaQP;    /* Positive QP to apply to hot regions */
   int nvencNonInteractiveMinQP; /* Min QP to apply when non-interactive */
   /*
    * vbvBufferSize corresponds to the maximum transmission delay, as per the
    * official Nvidia documentation NVENC_VideoEncoder_API_ProgGuide.pdf, it is
    * recommended to be in [500-3000] range.
    */
   int nvencVbvBufferSizeMSec;
   /*
    * QP change desired for the first phase of two step boosting process.
    * In the second phase QP would always be dropped to minQP.
    */
   int midBoostQPDelta;

   int maxDirtyFPS;     /* Max FPS of dirty regions */
   VNCBitmask *hotMask; /* Mask holding hot regions */
   VNCFreqMap *freqMap; /* Holds the changing frequency of dirty regions */

   /* Tracks the "age" (time since last change), per-macroblock. */
   VNCAgeMap *ageMap;
   /* Temp mask, tracks the last (accumulated) diff map. */
   VNCBitmask *dirtyMask;
   /* Tracks the macroblocks we think have achieved maximum quality. */
   VNCBitmask *maxQualMask;
   /* Tracks the macroblocks we think have achieved intermediate quality. */
   VNCBitmask *midQualMask;
   /* Temp mask, tracking what remains to boost in this pass. */
   VNCBitmask *toBoostMask;

   /*
    * We can either blindly use the NVENC internal rate control, or try to
    * control it by gathering diff maps and plumbing in QP deltas.
    */
   Bool useCustomRateControl;
   Bool useYUV444;
   Bool useHEVC;
   Bool useAV1;
   Bool useUnifiedD3DDevice;
   Bool useFullRangeAndBT7092020;

   int width;
   int height;
   int mbWidth;
   int mbHeight;
   int cuWidth;
   int cuHeight;
   Bool firstTime;
   Bool isFrameMaxQual;
   double lastBWupdate;
   double lastQualBoostTime;
   double lastQueryTime;
   int capacityCheckPeriodMs;
   int encFrameNum;
   int sendFrameNum;

   NV_ENC_INITIALIZE_PARAMS initParams;
   NV_ENC_PRESET_CONFIG presetConfig;
   NV_ENC_CONFIG config;
   NV_ENC_RC_PARAMS rcParamsNew;
   NV_ENC_PIC_PARAMS picParams;
   HANDLE inputRegHandle[TOTAL_INPUT_RESOURCE];
   HANDLE pBSBuffer;
   uint8 outAvgQP;
   uint32 frameSatd;
   int8 *qpDeltaMap;
   int8 *qpDeltaMapCTU;
   uint8 *qpMap;
   uint8 customMaxQP;
   uint8 lastMaxQP;
   Bool finalWipeFrame;
   int consecutiveEncodeSkips;
   int totalCUs;
   int totalMBs;
   int dirtyMBs;
   int lerpDirtyMBs;
   int midQualMBs;
   int refreshMBs;

   /* DEBUG */
   FILE *fhandleBitstream;
   FILE *fhandleRaw;
} VNCRegionEncoderNvEnc;


/* nvidiasdk API functions imported from .dll/.so file */
static struct {
   DLL_HANDLE dllHandleNvEncoder;
   DLL_HANDLE dllHandleNvml;

   NVENCSTATUS(NVENCAPI *NvEncodeAPICreateInstance)
   (NV_ENCODE_API_FUNCTION_LIST *functionList);

   nvmlReturn_t (*nvmlInit)(void);
   nvmlReturn_t (*nvmlShutdown)(void);
   const char *(*nvmlErrorString)(nvmlReturn_t result);
   nvmlReturn_t (*nvmlDeviceGetCount)(unsigned int *deviceCount);
   nvmlReturn_t (*nvmlDeviceGetHandleByIndex)(unsigned int index, nvmlDevice_t *device);
   nvmlReturn_t (*nvmlDeviceGetEncoderUtilization)(nvmlDevice_t device, unsigned int *utilization,
                                                   unsigned int *samplingPeriodUs);
   nvmlReturn_t (*nvmlDeviceGetEncoderCapacity)(nvmlDevice_t device,
                                                nvmlEncoderType_t encoderQueryType,
                                                unsigned int *encoderCapacity);
   nvmlReturn_t (*nvmlDeviceGetMemoryInfo)(nvmlDevice_t device, nvmlMemory_t *memory);
   nvmlReturn_t (*nvmlDeviceGetUtilizationRates)(nvmlDevice_t device,
                                                 nvmlUtilization_t *utilization);
   nvmlReturn_t (*nvmlDeviceGetProcessUtilization)(nvmlDevice_t device,
                                                   nvmlProcessUtilizationSample_t *utilization,
                                                   unsigned int *processCount,
                                                   unsigned long long lastSeenTimeStamp);
   nvmlReturn_t (*nvmlSystemGetProcessName)(unsigned int pid, char *name, unsigned int length);
   nvmlReturn_t (*nvmlDeviceGetGraphicsRunningProcesses)(nvmlDevice_t device,
                                                         unsigned int *infoCount,
                                                         nvmlProcessInfo_t *gInfos);
   nvmlReturn_t (*nvmlDeviceGetComputeRunningProcesses)(nvmlDevice_t device,
                                                        unsigned int *infoCount,
                                                        nvmlProcessInfo_t *cInfos);
   nvmlReturn_t (*nvmlSystemGetDriverVersion)(char *version, unsigned int length);
   nvmlReturn_t (*nvmlSystemGetNVMLVersion)(char *version, unsigned int length);
   nvmlReturn_t (*nvmlDeviceGetName)(nvmlDevice_t device, char *name, unsigned int length);
   nvmlReturn_t (*nvmlDeviceGetGridLicensableFeatures)(
      nvmlDevice_t device, nvmlGridLicensableFeatures_t *pGridLicensableFeatures);
   nvmlReturn_t (*nvmlDeviceGetVirtualizationMode)(nvmlDevice_t device,
                                                   nvmlGpuVirtualizationMode_t *mode);
} nvidiasdk;


/*
 * Track first allocation of H264 client.
 */
static int clientCount = 0;

static Bool nvmlInitialized = FALSE;
static int deviceCount = 0;
/*
 * Track whether caps have been queried and if so, what the results are.
 */
static Bool sCapsQueried = FALSE;
static VNCRegionEncoderNvEncCaps sCaps = {0};
static Bool h264SupportsCustomVbvBufferSize = FALSE;
static Bool hevcSupportsCustomVbvBufferSize = FALSE;
static Bool av1SupportsCustomVbvBufferSize = FALSE;

#define PCI_VENDOR_ID_NVIDIA (0x10de)

static int VNCEncodeRegionNvEncFindFreeHandleSlot(VNCRegionEncoderNvEnc *regEnc);
static int VNCEncodeRegionNvEncFindSharedHandle(VNCRegionEncoderNvEnc *regEnc,
                                                const VNCRegEncFrameState *frameState);
static void VNCEncodeRegionNvEncGetDeviceCount(void);

#include "vncEncodeRegionNvEncWindows.h"
#include "vncEncodeRegionNvEncLinux.h"

#define REGNVENC_DUMMY(id, dc)                                                                     \
   VNCRegionEncoderNvEnc *regEnc = Util_SafeCalloc(1, sizeof(*regEnc));                            \
   regEnc->base.config.encoderId = id;                                                             \
   regEnc->base.config.dynamicConfig = dc;

/*
 *----------------------------------------------------------------------------
 *
 * VNCEncodeRegionNvEncIsYUV444Requested --
 *
 *      Check if YUV 444 is requested.
 *
 * Results:
 *      TRUE if YUV 444 is requested.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

static Bool
VNCEncodeRegionNvEncIsYUV444Requested(VNCRegionEncoderNvEnc *regEnc)
{
   const VNCEncodeDynamicConfig *config = &regEnc->base.config.dynamicConfig;
   Bool yuv444Requested =
      (!regEnc->useHEVC && config->allowH264YUV444 &&
       regEnc->base.config.clientSupportsH264YUV444) ||
      (regEnc->useHEVC && config->allowHEVCYUV444 && regEnc->base.config.clientSupportsHEVCYUV444 &&
       (!regEnc->base.config.region.hdrEnabled || regEnc->base.config.clientSupportsHEVC10Bit444));

   return yuv444Requested;
}


/*
 *----------------------------------------------------------------------------
 *
 * VNCEncodeRegionNvEncIsYUV444Supported --
 *
 *      Check if YUV 444 is supported.
 *
 * Results:
 *      TRUE if YUV 444 is supported.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

static Bool
VNCEncodeRegionNvEncIsYUV444Supported(VNCRegionEncoderNvEnc *regEnc)
{
   return (regEnc->useHEVC ? sCaps.hevcSupportsYUV444 : sCaps.h264SupportsYUV444);
}


/*
 *----------------------------------------------------------------------------
 *
 * VNCEncodeRegionNvEncUnloadLibrary --
 *
 *      Unload the NVIDIA SDK library.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

static void
VNCEncodeRegionNvEncUnloadLibrary(void)
{
   VNCRegionEncoderNvEnc *regEnc = NULL; // for logging

   VNCEncodeManager_Lock();

   if (--clientCount == 0) {
      if (nvidiasdk.dllHandleNvEncoder) {
         REGENCLG0_ONCE("Unloading NVIDIA SDK shared library \"%s\"", NVENCODE_LIBRARY_NAME);
         CLOSE_LIB(nvidiasdk.dllHandleNvEncoder);
         nvidiasdk.dllHandleNvEncoder = NULL;
      }

      if (nvidiasdk.dllHandleNvml) {
         if (nvmlInitialized) {
            nvmlReturn_t ret = nvidiasdk.nvmlShutdown();
            if (ret != NVML_SUCCESS) {
               REGENCWARN("Failed to shutdown NVML (error %d). %s", ret,
                          nvidiasdk.nvmlErrorString(ret));
            }
            nvmlInitialized = FALSE;
         }

         REGENCLG0_ONCE("Unloading NVIDIA SDK shared library \"%s\"", NVML_LIBRARY_NAME);
         CLOSE_LIB(nvidiasdk.dllHandleNvml);
         nvidiasdk.dllHandleNvml = NULL;
      }
      ASSERT(nvmlInitialized == FALSE);
   }

   ASSERT(clientCount >= 0);

   VNCEncodeManager_Unlock();
}


/*
 *----------------------------------------------------------------------------
 *
 * VNCEncodeRegionNvEncGetFeaturecCodeString --
 *
 *      Return feature code string based on feature code number.
 *
 * Results:
 *      Feature code string.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

static const char *
VNCEncodeRegionNvEncGetFeatureCodeString(unsigned int featureCode) // IN
{
   switch (featureCode) {
   case 0:
      // Represents Bare Metal GPU.
      return "NVML_GPU_VIRTUALIZATION_MODE_NONE";
   case 1:
      // Device is associated with GPU-Passthorugh.
      return "NVML_GPU_VIRTUALIZATION_MODE_PASSTHROUGH";
   case 2:
      // Device is associated with vGPU inside virtual machine.
      return "NVML_GPU_VIRTUALIZATION_MODE_VGPU";
   case 3:
      // Device is associated with VGX hypervisor in vGPU mode.
      return "NVML_GPU_VIRTUALIZATION_MODE_HOST_VGPU";
   case 4:
      // Device is associated with VGX hypervisor in vSGA mode.
      return "NVML_GPU_VIRTUALIZATION_MODE_HOST_VSGA";
   default:
      return "Unknown feature code";
   }
}


/*
 *----------------------------------------------------------------------------
 *
 * VNCEncodeRegionNvEncLoadLibrary --
 *
 *      Load the NVIDIA SDK library.
 *
 * Results:
 *      TRUE if successful.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

static Bool
VNCEncodeRegionNvEncLoadLibrary(void)
{
   VNCRegionEncoderNvEnc *regEnc = NULL; // for logging
   char *nvmlLibPath;
   char driverVersion[80] = {0};
   char nvmlVersion[80] = {0};
   char deviceName[64] = {0};
   int i;
   nvmlReturn_t ret;

   VNCEncodeManager_Lock();

   if (++clientCount > 1) {
      ASSERT(nvidiasdk.dllHandleNvEncoder != NULL);
      VNCEncodeManager_Unlock();
      return TRUE;
   }

   /* Load the NVEncode library. */
   ASSERT(nvidiasdk.dllHandleNvEncoder == NULL);
   nvidiasdk.dllHandleNvEncoder = OPEN_LIB(NVENCODE_LIBRARY_NAME);
   if (!nvidiasdk.dllHandleNvEncoder) {
      REGENCLG0_ONCE("Library \"%s\" is unavailable (%s)", NVENCODE_LIBRARY_NAME,
                     DYNAMIC_LOAD_ERROR_STRING);
      goto exit;
   }

   REGENCLG0_ONCE("Loaded NVIDIA SDK shared library \"%s\"", NVENCODE_LIBRARY_NAME);
   DLSYM(NvEncodeAPICreateInstance, nvidiasdk.dllHandleNvEncoder);


   /* Load the nvml library. */
   ASSERT(nvidiasdk.dllHandleNvml == NULL);
   /*
    * Bug 2131392:
    * nvml.dll is not in the default location (%SYSTEMROOT%\system32).
    */
#ifdef WIN32
   nvmlLibPath = VNCEncodeRegionNvEncGetNvmlAbsolutePath();
   if (nvmlLibPath != NULL) {
      nvidiasdk.dllHandleNvml = OPEN_LIB(nvmlLibPath);
      free(nvmlLibPath);
   }
   if (nvidiasdk.dllHandleNvml == NULL) {
      nvidiasdk.dllHandleNvml = OPEN_LIB(NVML_LIBRARY_NAME);
   }
#else
   nvmlLibPath = NVML_LIBRARY_NAME;
   nvidiasdk.dllHandleNvml = OPEN_LIB(nvmlLibPath);
#endif
   if (!nvidiasdk.dllHandleNvml) {
      REGENCLG0_ONCE("Library \"%s\" is unavailable (%s)", NVML_LIBRARY_NAME,
                     DYNAMIC_LOAD_ERROR_STRING);
      goto exit;
   }

   REGENCLG0_ONCE("Loaded NVIDIA SDK shared library \"%s\"", NVML_LIBRARY_NAME);
   DLSYM_NOFAIL(nvmlInit, nvidiasdk.dllHandleNvml);
   DLSYM_NOFAIL(nvmlShutdown, nvidiasdk.dllHandleNvml);
   DLSYM_NOFAIL(nvmlErrorString, nvidiasdk.dllHandleNvml);
   DLSYM_NOFAIL(nvmlDeviceGetCount, nvidiasdk.dllHandleNvml);
   DLSYM_NOFAIL(nvmlDeviceGetHandleByIndex, nvidiasdk.dllHandleNvml);
   DLSYM_NOFAIL(nvmlDeviceGetEncoderUtilization, nvidiasdk.dllHandleNvml);
   DLSYM_NOFAIL(nvmlDeviceGetEncoderCapacity, nvidiasdk.dllHandleNvml);
   DLSYM_NOFAIL(nvmlDeviceGetMemoryInfo, nvidiasdk.dllHandleNvml);
   DLSYM_NOFAIL(nvmlDeviceGetUtilizationRates, nvidiasdk.dllHandleNvml);
   DLSYM_NOFAIL(nvmlDeviceGetProcessUtilization, nvidiasdk.dllHandleNvml);
   DLSYM_NOFAIL(nvmlSystemGetProcessName, nvidiasdk.dllHandleNvml);
   DLSYM_NOFAIL(nvmlDeviceGetGraphicsRunningProcesses, nvidiasdk.dllHandleNvml);
   DLSYM_NOFAIL(nvmlDeviceGetComputeRunningProcesses, nvidiasdk.dllHandleNvml);
   DLSYM_NOFAIL(nvmlSystemGetDriverVersion, nvidiasdk.dllHandleNvml);
   DLSYM_NOFAIL(nvmlSystemGetNVMLVersion, nvidiasdk.dllHandleNvml);
   DLSYM_NOFAIL(nvmlDeviceGetName, nvidiasdk.dllHandleNvml);
   DLSYM_NOFAIL(nvmlDeviceGetGridLicensableFeatures, nvidiasdk.dllHandleNvml);
   DLSYM_NOFAIL(nvmlDeviceGetVirtualizationMode, nvidiasdk.dllHandleNvml);

   if (!nvidiasdk.nvmlInit || !nvidiasdk.nvmlShutdown || !nvidiasdk.nvmlErrorString ||
       !nvidiasdk.nvmlDeviceGetCount || !nvidiasdk.nvmlDeviceGetHandleByIndex ||
       !nvidiasdk.nvmlDeviceGetEncoderUtilization || !nvidiasdk.nvmlDeviceGetEncoderCapacity ||
       !nvidiasdk.nvmlDeviceGetMemoryInfo || !nvidiasdk.nvmlDeviceGetUtilizationRates ||
       !nvidiasdk.nvmlSystemGetProcessName || !nvidiasdk.nvmlDeviceGetGraphicsRunningProcesses ||
       !nvidiasdk.nvmlDeviceGetComputeRunningProcesses || !nvidiasdk.nvmlSystemGetDriverVersion ||
       !nvidiasdk.nvmlSystemGetNVMLVersion || !nvidiasdk.nvmlDeviceGetName ||
       !nvidiasdk.nvmlDeviceGetGridLicensableFeatures ||
       !nvidiasdk.nvmlDeviceGetVirtualizationMode) {
      REGENCWARN_ONCE("Some NVIDIA nvml functions unavailable, unloading");
      goto exit;
   }

   ret = nvidiasdk.nvmlInit();
   if (ret != NVML_SUCCESS) {
      REGENCWARN_ONCE("%s: Failed to initialize NVML (error %d). %s", __FUNCTION__, ret,
                      nvidiasdk.nvmlErrorString(ret));
      if (ret == NVML_ERROR_DRIVER_NOT_LOADED) {
         /*
          * Linux: If the linux kernel is updated, the nvidia driver fails to
          *        load since it was compiled against a different kernel.
          *        Reinstalling the driver usually solves the issue.
          */
         REGENCWARN_ONCE("Failed to communicate with the NVIDIA driver. "
                         "Check if the NVIDIA driver is running using "
                         "nvidia-smi and reinstall the driver if necessary.");
      }
      CLOSE_LIB(nvidiasdk.dllHandleNvml);
      goto exit;
   }
   nvmlInitialized = TRUE;

   /* Query Driver version */
   ret = nvidiasdk.nvmlSystemGetDriverVersion(driverVersion, sizeof driverVersion);
   if (ret != NVML_SUCCESS) {
      REGENCWARN_ONCE("%s: Failed to query driver version (error %d). %s", __FUNCTION__, ret,
                      nvidiasdk.nvmlErrorString(ret));
      goto exit;
   }

   /* Query NVML version */
   ret = nvidiasdk.nvmlSystemGetNVMLVersion(nvmlVersion, sizeof nvmlVersion);
   if (ret != NVML_SUCCESS) {
      REGENCWARN_ONCE("%s: Failed to query NVML version (error %d). %s", __FUNCTION__, ret,
                      nvidiasdk.nvmlErrorString(ret));
      goto exit;
   }
   REGENCLG0_ONCE("%s: Driver version: %s - NVML version: %s", __FUNCTION__, driverVersion,
                  nvmlVersion);

   VNCEncodeRegionNvEncGetDeviceCount();
   if (deviceCount <= 0) {
      goto exit;
   }

   for (i = 0; i < deviceCount; i++) {
      nvmlDevice_t device = {0};
      nvmlGridLicensableFeatures_t gridLicenses;

      ret = nvidiasdk.nvmlDeviceGetHandleByIndex(i, &device);
      if (ret != NVML_SUCCESS) {
         REGENCWARN_ONCE("%s: Failed to get device index for %d (error %d). %s", __FUNCTION__, i,
                         ret, nvidiasdk.nvmlErrorString(ret));
         if (ret == NVML_ERROR_NO_PERMISSION) {
            REGENCWARN_ONCE("%s: insufficient permission to acquire device "
                            "%d handle, skipping",
                            __FUNCTION__, i);
            goto exit;
         }
         REGENCWARN_ONCE("%s: error getting device %d handle", __FUNCTION__, i);
         goto exit;
      }

      /* Query device name/profile */
      ret = nvidiasdk.nvmlDeviceGetName(device, deviceName, sizeof deviceName);
      if (ret != NVML_SUCCESS) {
         REGENCWARN_ONCE("%s: Failed to get device name (error %d). %s", __FUNCTION__, ret,
                         nvidiasdk.nvmlErrorString(ret));
         goto exit;
      }
      REGENCLG0_ONCE("%s: NVIDIA %s", __FUNCTION__, deviceName);

      /* Check license status */
      memset(&gridLicenses, 0, sizeof(gridLicenses));
      ret = nvidiasdk.nvmlDeviceGetGridLicensableFeatures(device, &gridLicenses);
      if (ret != NVML_SUCCESS) {
         REGENCWARN("%s: Failed to get license information (error %d). %s", __FUNCTION__, ret,
                    nvidiasdk.nvmlErrorString(ret));
         goto exit;
      } else if (gridLicenses.isGridLicenseSupported) {
         int j;

         for (j = 0; j < gridLicenses.licensableFeaturesCount; j++) {
            Bool unlicensed = gridLicenses.gridLicensableFeatures[j].featureState == 0;
            unsigned int featureCode = gridLicenses.gridLicensableFeatures[j].featureCode;
            REGENCLG0("Feature: %s Code (%d): %s State: %s",
                      gridLicenses.gridLicensableFeatures[j].licenseInfo, featureCode,
                      VNCEncodeRegionNvEncGetFeatureCodeString(featureCode),
                      unlicensed ? "Unlicensed" : "Licensed");
         }
      }

      nvmlGpuVirtualizationMode_t virtMode;
      ret = nvidiasdk.nvmlDeviceGetVirtualizationMode(device, &virtMode);
      if (ret != NVML_SUCCESS) {
         REGENCWARN_ONCE("%s: Failed to get virtualization mode (error %d). %s", __FUNCTION__, ret,
                         nvidiasdk.nvmlErrorString(ret));
      } else {
         switch (virtMode) {
         case NVML_GPU_VIRTUALIZATION_MODE_NONE:
            REGENCLG0_ONCE("Bare metal GPU (NVML_GPU_VIRTUALIZATION_MODE_NONE)");
            break;
         case NVML_GPU_VIRTUALIZATION_MODE_PASSTHROUGH:
            REGENCLG0_ONCE("PCI Passthrough GPU (NVML_GPU_VIRTUALIZATION_MODE_PASSTHROUGH)");
            break;
         case NVML_GPU_VIRTUALIZATION_MODE_VGPU:
            REGENCLG0_ONCE("vGPU (NVML_GPU_VIRTUALIZATION_MODE_VGPU)");
            break;
         default:
            REGENCLG0_ONCE("vSGA/vDGA GPU from hypervisor context??");
            break;
         }
      }
   }

   VNCEncodeManager_Unlock();
   return TRUE;

exit:
   VNCEncodeRegionNvEncUnloadLibrary();
   VNCEncodeManager_Unlock();

   return FALSE;
}


/*
 *----------------------------------------------------------------------------
 *
 * NvEncQueryCap.
 *
 *      Query NVIDIA GPU cap
 *
 * Results:
 *      Integer value, returns 0 if failed.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

static int
NvEncQueryCap(NV_ENCODE_API_FUNCTION_LIST encodeFns, // IN
              HANDLE nvenc,                          // IN
              GUID guid,                             // IN
              NV_ENC_CAPS capBit)                    // IN
{
   VNCRegionEncoderNvEnc *regEnc = NULL; // for logging
   NV_ENC_CAPS_PARAM capsParam = {0};
   NVENCSTATUS status;
   int capsVal = 0;

   capsParam.version = NV_ENC_CAPS_PARAM_VER;
   capsParam.capsToQuery = capBit;
   status = encodeFns.nvEncGetEncodeCaps(nvenc, guid, &capsParam, &capsVal);
   if (status != NV_ENC_SUCCESS) {
      REGENCLG0("%s: Failed to get encode caps %d (error %d).", __FUNCTION__, capBit, status);
      return 0;
   }

   return capsVal;
}


/*
 *----------------------------------------------------------------------------
 *
 * VNCEncodeRegionNvEncGetDeviceCount --
 *
 *      Query the number of devices.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      Updates global variable "deviceCount".
 *
 *----------------------------------------------------------------------------
 */

static void
VNCEncodeRegionNvEncGetDeviceCount(void)
{
   VNCRegionEncoderNvEnc *regEnc = NULL; // for logging
   nvmlReturn_t ret;

   ASSERT(nvmlInitialized);

   if (deviceCount > 0) {
      return;
   }

   ret = nvidiasdk.nvmlDeviceGetCount(&deviceCount);
   if (ret != NVML_SUCCESS) {
      REGENCWARN("%s: Failed to get device count (error %d). %s", __FUNCTION__, ret,
                 nvidiasdk.nvmlErrorString(ret));
      deviceCount = 0;
   }

   if (deviceCount <= 0) {
      REGENCLG0("%s: No GPU device detected.", __FUNCTION__);
   } else {
      REGENCLG0("%s: detected %u devices.", __FUNCTION__, deviceCount);
   }
}


/*
 *----------------------------------------------------------------------------
 *
 * VNCEncodeRegionNvEncGPUMemoryInfo --
 *
 *      Query and log usage per process.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

static void
VNCEncodeRegionNvEncGPUMemoryInfo(int screenNum, // IN
                                  char *title)   // IN
{
   Bool emitMemLog = Config_GetBool(FALSE, "RemoteDisplay.emitNvidiaMemLog");
   unsigned long long procMemUsed = 0;
   int i;

   if (!emitMemLog) {
      return;
   }

   Log("Checking GPU Memory %s, Encoder %d", title, screenNum);

   for (i = 0; i < deviceCount; i++) {
      nvmlProcessUtilizationSample_t *nvmlProcUtil = NULL;
      nvmlProcessInfo_t *gInfos = NULL;
      nvmlProcessInfo_t *cInfos = NULL;
      nvmlReturn_t ret;
      nvmlDevice_t device = {0};
      nvmlMemory_t memory = {0};
      uint32 processCount = 0;
      uint32 gInfoCount = 0;
      uint32 cInfoCount = 0;
      int idx;

      ret = nvidiasdk.nvmlDeviceGetHandleByIndex(i, &device);
      if (ret != NVML_SUCCESS) {
         Warning("%s: nvmlDeviceGetHandleByIndex failed (error %d). %s", __FUNCTION__, ret,
                 nvidiasdk.nvmlErrorString(ret));
         if (ret == NVML_ERROR_NO_PERMISSION) {
            Warning("%s: insufficient permission to acquire device "
                    "%d handle, skipping",
                    __FUNCTION__, i);
            goto exit;
         }
         Warning("%s: error getting device %d handle", __FUNCTION__, i);
         goto exit;
      }

      ret = nvidiasdk.nvmlDeviceGetMemoryInfo(device, &memory);
      if (ret != NVML_SUCCESS) {
         Warning("%s: Failed to get memory info (error %d). %s", __FUNCTION__, ret,
                 nvidiasdk.nvmlErrorString(ret));
         goto exit;
      }
      float memoryUsedPercentage = 0;

      if (memory.total != 0) {
         memoryUsedPercentage = (((float)memory.used / (float)memory.total)) * 100.0;
      }

      /*
       * As per SDK instructions, call once to obtain processCount
       * and to know how much to allocate and then a second time
       * to retrieve the process info.
       */
      /* 2 step - nvmlDeviceGetProcessUtilization */
      ret = nvidiasdk.nvmlDeviceGetProcessUtilization(device, NULL, &processCount, 0);
      if (ret != NVML_SUCCESS && ret != NVML_ERROR_INSUFFICIENT_SIZE) {
         Warning("%s: Failed to get process utilization 1/2 "
                 "(error %d). %s",
                 __FUNCTION__, ret, nvidiasdk.nvmlErrorString(ret));
         goto exit;
      }
      nvmlProcUtil = Util_SafeCalloc(1, processCount * sizeof(nvmlProcessUtilizationSample_t));
      ret = nvidiasdk.nvmlDeviceGetProcessUtilization(device, nvmlProcUtil, &processCount, 0);
      if (ret != NVML_SUCCESS) {
         Warning("%s: Failed to get process utilization 2/2 "
                 "(error %d). %s",
                 __FUNCTION__, ret, nvidiasdk.nvmlErrorString(ret));
         goto exit;
      }

      /* 2 step - nvmlDeviceGetGraphicsRunningProcesses */
      ret = nvidiasdk.nvmlDeviceGetGraphicsRunningProcesses(device, &gInfoCount, NULL);
      if (ret != NVML_SUCCESS && ret != NVML_ERROR_INSUFFICIENT_SIZE) {
         Warning("%s: Failed to get graphic processes info 1/2 "
                 "(error %d). %s",
                 __FUNCTION__, ret, nvidiasdk.nvmlErrorString(ret));
         goto exit;
      }
      gInfos = Util_SafeCalloc(1, gInfoCount * sizeof(nvmlProcessInfo_t));
      ret = nvidiasdk.nvmlDeviceGetGraphicsRunningProcesses(device, &gInfoCount, gInfos);
      if (ret != NVML_SUCCESS) {
         Warning("%s: Failed to get graphic processes info 2/2 "
                 "(error %d). %s",
                 __FUNCTION__, ret, nvidiasdk.nvmlErrorString(ret));
         goto exit;
      }

      /* 2 step - nvmlDeviceGetComputeRunningProcesses */
      ret = nvidiasdk.nvmlDeviceGetComputeRunningProcesses(device, &cInfoCount, NULL);
      if (ret != NVML_SUCCESS && ret != NVML_ERROR_INSUFFICIENT_SIZE) {
         Warning("%s: Failed to get compute processes info 1/2 "
                 "(error %d). %s",
                 __FUNCTION__, ret, nvidiasdk.nvmlErrorString(ret));
         goto exit;
      }
      cInfos = Util_SafeCalloc(1, cInfoCount * sizeof(nvmlProcessInfo_t));
      ret = nvidiasdk.nvmlDeviceGetComputeRunningProcesses(device, &cInfoCount, cInfos);
      if (ret != NVML_SUCCESS) {
         Warning("%s: Failed to get compute processes info 2/2 "
                 "(error %d). %s",
                 __FUNCTION__, ret, nvidiasdk.nvmlErrorString(ret));
         goto exit;
      }

      /* Log all GPU processes found and their used resources */
      for (idx = 0; idx < processCount; idx++) {
#define PROCNAMELEN 128
#define MAXPROCNAME 40
         char processName[PROCNAMELEN] = {0};
         int stringlen, stringoffset = 0;
         unsigned long long procMemGraphicsBytes = 0;
         unsigned long long procMemComputeBytes = 0;
         unsigned long long procMemBytes = 0;
         int j;
         char processType[3] = {0};

         /*
          * Remove dummy PID 0 process that does
          * NOT show up in nvidia-smi listing.
          */
         if (nvmlProcUtil[idx].pid == 0) {
            continue;
         }

         /* Find memory usage for the matching PID.
          *
          * Note, appears to be.....
          * that the Compute value can sometimes be slightly higher
          * than the Graphics value. We'll find the MAX later.
          */
         for (j = 0; j < gInfoCount; j++) {
            if (nvmlProcUtil[idx].pid == gInfos[j].pid) {
               procMemGraphicsBytes = gInfos[j].usedGpuMemory;
               processType[0] = ' ';
               processType[1] = 'G';
            }
         }
         for (j = 0; j < cInfoCount; j++) {
            if (nvmlProcUtil[idx].pid == cInfos[j].pid) {
               procMemComputeBytes = cInfos[j].usedGpuMemory;
               processType[0] = 'C';
            }
         }

         procMemBytes = MAX(procMemGraphicsBytes, procMemComputeBytes);

         procMemUsed += procMemBytes;

         ret = nvidiasdk.nvmlSystemGetProcessName(nvmlProcUtil[idx].pid, processName,
                                                  (sizeof processName) - 1);
         /* If process path is too long then truncate the start */
         stringlen = strlen(processName);
         if (stringlen > MAXPROCNAME) {
            stringoffset = stringlen - MAXPROCNAME;
         }
         Log("%s:\tPID: %6u Type: %-4s Proc: %-40s Memory: %llu MiB - "
             "3D/ComputeUtil: %3u FBMemUtil: %3u EncUtil: %3u "
             "DecUtil: %3u",
             __FUNCTION__, nvmlProcUtil[idx].pid, processType, processName + stringoffset,
             BYTES_2_MBYTES(procMemBytes), nvmlProcUtil[idx].smUtil, nvmlProcUtil[idx].memUtil,
             nvmlProcUtil[idx].encUtil, nvmlProcUtil[idx].decUtil);
      }

      Log("GPU Memory - Total (MiB): %llu Free: %llu Used: %llu ProcUsed: %llu "
          "Percentage used %.2f%%",
          BYTES_2_MBYTES(memory.total), BYTES_2_MBYTES(memory.free), BYTES_2_MBYTES(memory.used),
          BYTES_2_MBYTES(procMemUsed), memoryUsedPercentage);

   exit:
      free(cInfos);
      cInfos = NULL;
      free(gInfos);
      gInfos = NULL;
      free(nvmlProcUtil);
      nvmlProcUtil = NULL;
   }
}


/*
 *----------------------------------------------------------------------------
 *
 * VNCEncodeRegionNvEncDoesGPUHaveCapacity --
 *
 *      Return whether there is enough encoder capacity on the GPU, by probing
 *      the NVIDIA driver for information capacity and utilization.
 *
 *      XXX: NVML does not currently expose AV1 encoder utilization.
 *
 * Results:
 *      TRUE if there is enough capacity to start an encoder or the driver is
 *      not present; FALSE otherwise.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

static Bool
VNCEncodeRegionNvEncDoesGPUHaveCapacity(VNCRegionEncoderNvEnc *regEnc) // IN
{
   Bool success = FALSE;
   int i;

   ASSERT(nvidiasdk.dllHandleNvml);
   ASSERT(nvmlInitialized);

   VNCEncodeRegionNvEncGPUMemoryInfo(regEnc->base.config.screenNum, "HaveCapacityCheck");

   for (i = 0; i < deviceCount; i++) {
      nvmlReturn_t ret;
      nvmlDevice_t device = {0};
      nvmlUtilization_t nvmlUtilization = {0};
      uint32 utilization;
      uint32 h264Capacity;
      uint32 hevcCapacity;
      uint32 samplePeriodUs;

      ret = nvidiasdk.nvmlDeviceGetHandleByIndex(i, &device);
      if (ret != NVML_SUCCESS) {
         REGENCWARN("%s: nvmlDeviceGetHandleByIndex failed (error %d). %s", __FUNCTION__, ret,
                    nvidiasdk.nvmlErrorString(ret));
         if (ret == NVML_ERROR_NO_PERMISSION) {
            REGENCWARN("%s: insufficient permission to acquire device "
                       "%d handle, skipping",
                       __FUNCTION__, i);
            continue;
         }
         REGENCWARN("%s: error getting device %d handle", __FUNCTION__, i);
         goto exit;
      }

      ret = nvidiasdk.nvmlDeviceGetEncoderUtilization(device, &utilization, &samplePeriodUs);
      if (ret != NVML_SUCCESS) {
         REGENCWARN("%s: Failed to get encoder utilization (error %d). %s", __FUNCTION__, ret,
                    nvidiasdk.nvmlErrorString(ret));
         goto exit;
      }

      ret = nvidiasdk.nvmlDeviceGetUtilizationRates(device, &nvmlUtilization);
      if (ret != NVML_SUCCESS) {
         REGENCWARN("%s: Failed to get utilization rates (error %d). %s", __FUNCTION__, ret,
                    nvidiasdk.nvmlErrorString(ret));
         goto exit;
      }

      ret = nvidiasdk.nvmlDeviceGetEncoderCapacity(device, NVML_ENCODER_QUERY_H264, &h264Capacity);
      if (ret != NVML_SUCCESS) {
         REGENCWARN("%s: Failed to get H.264 encoder capacity (error %d). %s", __FUNCTION__, ret,
                    nvidiasdk.nvmlErrorString(ret));
         goto exit;
      }

      ret = nvidiasdk.nvmlDeviceGetEncoderCapacity(device, NVML_ENCODER_QUERY_HEVC, &hevcCapacity);
      if (ret != NVML_SUCCESS) {
         REGENCWARN("%s: Failed to get HEVC encoder capacity (error %d). %s", __FUNCTION__, ret,
                    nvidiasdk.nvmlErrorString(ret));
         goto exit;
      }

      REGENCLG0("%s: device %d - "
                "Utilization Rates - GPU: %3u%% Memory: %3u%% Encoder: %3u%%",
                __FUNCTION__, i, nvmlUtilization.gpu, nvmlUtilization.memory, utilization);
      REGENCLOG(9, "Capacity: H.264 %3u%%, HEVC %3u%%", h264Capacity, hevcCapacity);
   }

   /*
    * XXX: Always return TRUE for now.
    * Investigate what the driver returns to condition what tests we make.
    */
   success = TRUE;

exit:
   return success;
}


/*
 *----------------------------------------------------------------------------
 *
 * VNCEncodeRegionNvEncPrintEncodeCaps --
 *
 *      Print HW encoder capabilities.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */
static void
VNCEncodeRegionNvEncPrintEncodeCaps(VNCRegionEncoderNvEnc *regEnc, HANDLE hNVEnc)
{
   static Bool capsQueried = FALSE;

   ASSERT(regEnc); // Check nvEncGetEncodeCaps

   if (capsQueried == FALSE && regEnc->base.config.dynamicConfig.vncEncodeLogLevel >= 9) {
      GUID guid[] = {
#if NVENCAPI_MAJOR_VERSION > 8
         NV_ENC_CODEC_AV1_GUID,
#endif
         NV_ENC_CODEC_HEVC_GUID,
         NV_ENC_CODEC_H264_GUID,
      };
      const char *codec[] = {"AV1", "HEVC", "H264"};
      int val, i;

      capsQueried = TRUE;

      /* Query All encoder caps */
      for (i = 0; i < ARRAYSIZE(guid); i++) {
         uint32_t numProfiles = 0;
         uint32_t actualNumProfiles = 0;
         GUID *profiles = NULL;
         int j;
         NVENCSTATUS status;

         status = regEnc->encodeFns.nvEncGetEncodePresetCount(hNVEnc, guid[i], &numProfiles);
         if (!NV_ENC_SUCCEEDED(status)) {
            REGENCWARN("nvEncGetEncodePresetCount failed for %s (%d), skip", codec[i], status);
            continue;
         }
         profiles = Util_SafeMalloc(numProfiles * sizeof(*profiles));
         ASSERT(NV_ENC_SUCCEEDED(regEnc->encodeFns.nvEncGetEncodePresetGUIDs(
            hNVEnc, guid[i], profiles, numProfiles, &actualNumProfiles)));
         ASSERT(actualNumProfiles <= numProfiles);
         REGENCLG0("NVQUERY for %s", codec[i]);

         for (j = 0; j < actualNumProfiles; ++j) {
            const GUID *g = &profiles[j];
            Log("NVQUERY: preset GUID " _PRIGUID " supported.", g->Data1, g->Data2, g->Data3,
                g->Data4[0], g->Data4[1], g->Data4[2], g->Data4[3], g->Data4[4], g->Data4[5],
                g->Data4[6], g->Data4[7]);
         }
         free(profiles);

         NVQUERY(hNVEnc, guid[i], NV_ENC_CAPS_NUM_MAX_BFRAMES, val);
         NVQUERY(hNVEnc, guid[i], NV_ENC_CAPS_SUPPORTED_RATECONTROL_MODES, val);
         NVQUERY(hNVEnc, guid[i], NV_ENC_CAPS_SUPPORT_FIELD_ENCODING, val);
         NVQUERY(hNVEnc, guid[i], NV_ENC_CAPS_SUPPORT_MONOCHROME, val);
         NVQUERY(hNVEnc, guid[i], NV_ENC_CAPS_SUPPORT_FMO, val);
         NVQUERY(hNVEnc, guid[i], NV_ENC_CAPS_SUPPORT_QPELMV, val);
         NVQUERY(hNVEnc, guid[i], NV_ENC_CAPS_SUPPORT_BDIRECT_MODE, val);
         NVQUERY(hNVEnc, guid[i], NV_ENC_CAPS_SUPPORT_CABAC, val);
         NVQUERY(hNVEnc, guid[i], NV_ENC_CAPS_SUPPORT_ADAPTIVE_TRANSFORM, val);
#if NVENCAPI_MAJOR_VERSION > 8
         NVQUERY(hNVEnc, guid[i], NV_ENC_CAPS_SUPPORT_STEREO_MVC, val);
#endif
         NVQUERY(hNVEnc, guid[i], NV_ENC_CAPS_NUM_MAX_TEMPORAL_LAYERS, val);
         NVQUERY(hNVEnc, guid[i], NV_ENC_CAPS_SUPPORT_HIERARCHICAL_PFRAMES, val);
         NVQUERY(hNVEnc, guid[i], NV_ENC_CAPS_SUPPORT_HIERARCHICAL_BFRAMES, val);
         NVQUERY(hNVEnc, guid[i], NV_ENC_CAPS_LEVEL_MAX, val);
         NVQUERY(hNVEnc, guid[i], NV_ENC_CAPS_LEVEL_MIN, val);
         NVQUERY(hNVEnc, guid[i], NV_ENC_CAPS_SEPARATE_COLOUR_PLANE, val);
         NVQUERY(hNVEnc, guid[i], NV_ENC_CAPS_WIDTH_MAX, val);
         NVQUERY(hNVEnc, guid[i], NV_ENC_CAPS_HEIGHT_MAX, val);
         NVQUERY(hNVEnc, guid[i], NV_ENC_CAPS_SUPPORT_TEMPORAL_SVC, val);
         NVQUERY(hNVEnc, guid[i], NV_ENC_CAPS_SUPPORT_DYN_RES_CHANGE, val);
         NVQUERY(hNVEnc, guid[i], NV_ENC_CAPS_SUPPORT_DYN_BITRATE_CHANGE, val);
         NVQUERY(hNVEnc, guid[i], NV_ENC_CAPS_SUPPORT_DYN_FORCE_CONSTQP, val);
         NVQUERY(hNVEnc, guid[i], NV_ENC_CAPS_SUPPORT_DYN_RCMODE_CHANGE, val);
         NVQUERY(hNVEnc, guid[i], NV_ENC_CAPS_SUPPORT_SUBFRAME_READBACK, val);
         NVQUERY(hNVEnc, guid[i], NV_ENC_CAPS_SUPPORT_CONSTRAINED_ENCODING, val);
         NVQUERY(hNVEnc, guid[i], NV_ENC_CAPS_SUPPORT_INTRA_REFRESH, val);
         NVQUERY(hNVEnc, guid[i], NV_ENC_CAPS_SUPPORT_CUSTOM_VBV_BUF_SIZE, val);
         NVQUERY(hNVEnc, guid[i], NV_ENC_CAPS_SUPPORT_DYNAMIC_SLICE_MODE, val);
         NVQUERY(hNVEnc, guid[i], NV_ENC_CAPS_SUPPORT_REF_PIC_INVALIDATION, val);
         NVQUERY(hNVEnc, guid[i], NV_ENC_CAPS_PREPROC_SUPPORT, val);
         NVQUERY(hNVEnc, guid[i], NV_ENC_CAPS_ASYNC_ENCODE_SUPPORT, val);
         NVQUERY(hNVEnc, guid[i], NV_ENC_CAPS_MB_NUM_MAX, val);
         NVQUERY(hNVEnc, guid[i], NV_ENC_CAPS_MB_PER_SEC_MAX, val);
         NVQUERY(hNVEnc, guid[i], NV_ENC_CAPS_SUPPORT_YUV444_ENCODE, val);
         NVQUERY(hNVEnc, guid[i], NV_ENC_CAPS_SUPPORT_LOSSLESS_ENCODE, val);
         NVQUERY(hNVEnc, guid[i], NV_ENC_CAPS_SUPPORT_SAO, val);
         NVQUERY(hNVEnc, guid[i], NV_ENC_CAPS_SUPPORT_MEONLY_MODE, val);
         NVQUERY(hNVEnc, guid[i], NV_ENC_CAPS_SUPPORT_LOOKAHEAD, val);
         NVQUERY(hNVEnc, guid[i], NV_ENC_CAPS_SUPPORT_TEMPORAL_AQ, val);
         NVQUERY(hNVEnc, guid[i], NV_ENC_CAPS_SUPPORT_10BIT_ENCODE, val);
         NVQUERY(hNVEnc, guid[i], NV_ENC_CAPS_NUM_MAX_LTR_FRAMES, val);
         NVQUERY(hNVEnc, guid[i], NV_ENC_CAPS_SUPPORT_WEIGHTED_PREDICTION, val);
         NVQUERY(hNVEnc, guid[i], NV_ENC_CAPS_DYNAMIC_QUERY_ENCODER_CAPACITY, val);
         NVQUERY(hNVEnc, guid[i], NV_ENC_CAPS_SUPPORT_BFRAME_REF_MODE, val);
         NVQUERY(hNVEnc, guid[i], NV_ENC_CAPS_SUPPORT_EMPHASIS_LEVEL_MAP, val);
#if NVENCAPI_MAJOR_VERSION > 8
         NVQUERY(hNVEnc, guid[i], NV_ENC_CAPS_WIDTH_MIN, val);
         NVQUERY(hNVEnc, guid[i], NV_ENC_CAPS_HEIGHT_MIN, val);
         NVQUERY(hNVEnc, guid[i], NV_ENC_CAPS_SUPPORT_MULTIPLE_REF_FRAMES, val);
         NVQUERY(hNVEnc, guid[i], NV_ENC_CAPS_SUPPORT_ALPHA_LAYER_ENCODING, val);
         NVQUERY(hNVEnc, guid[i], NV_ENC_CAPS_NUM_ENCODER_ENGINES, val);
         NVQUERY(hNVEnc, guid[i], NV_ENC_CAPS_SINGLE_SLICE_INTRA_REFRESH, val);
#endif
      }
   }
}

/*
 *----------------------------------------------------------------------------
 *
 * VNCEncodeRegionNvEncSetBandwidth --
 *
 *      Updates the hardware encoder bandwidth settings in the rate control with
 *      the current bandwidth estimate.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *----------------------------------------------------------------------------
 */

static void
VNCEncodeRegionNvEncSetBandwidth(VNCRegionEncoderNvEnc *regEnc, // IN
                                 uint32 bandwidth,              // IN
                                 NV_ENC_RC_PARAMS *rcParams)    // OUT
{
   if (regEnc == NULL) {
      return;
   }

   /* Define a minimum bitrate */
   unsigned int bitrate = HZN_CLAMP(8 * bandwidth * regEnc->nvencBwTargetPercent / 100,
                                    MIN_BITRATE_BITS_PER_SEC, MAX_BITRATE_BITS_PER_SEC);
   REGENCLOG(9, "Bandwidth target(kbps) - Original: %d Capped: %d - bwRatio: %d%%",
             bandwidth * 8 / 1024, bitrate / 1024, 100 * regEnc->nvencBwTargetPercent / 100);

   rcParams->averageBitRate = (uint32)(1.0 * bitrate);
   rcParams->maxBitRate = (uint32)(1.2 * bitrate);

   if (regEnc->nvencVbvBufferSizeMSec) {
      if (regEnc->useAV1 && !av1SupportsCustomVbvBufferSize) {
         REGENCLOG(9, "Custom vbvBufferSize not supported in NvEnc AV1, "
                      "falling back to default value");
      } else if (regEnc->useHEVC && !hevcSupportsCustomVbvBufferSize) {
         REGENCLOG(9, "Custom vbvBufferSize not supported in NvEnc HEVC, "
                      "falling back to default value");
      } else if (!h264SupportsCustomVbvBufferSize) {
         REGENCLOG(9, "Custom vbvBufferSize not supported in NvEnc H264, "
                      "falling back to default value");
      } else {
         rcParams->vbvBufferSize =
            (uint32)(rcParams->averageBitRate * regEnc->nvencVbvBufferSizeMSec / 1000);
      }
   }
}


/*
 *----------------------------------------------------------------------------
 *
 * VNCEncodeRegionNvEncUpdateRC --
 *
 *      Update the rate control parameters for the encoder, and apply them if
 *      they changed.
 *
 * Results:
 *      Returns TRUE if successful, FALSE otherwise.
 *
 * Side effects:
 *      Subsequent frames might have a different quality level.
 *
 *----------------------------------------------------------------------------
 */

static Bool
VNCEncodeRegionNvEncUpdateRC(VNCRegionEncoderNvEnc *regEnc) // IN
{
   if (regEnc == NULL) {
      return FALSE;
   }

   const NV_ENC_RC_PARAMS *rcParamsNew = &regEnc->rcParamsNew;
   const NV_ENC_INITIALIZE_PARAMS *initParams = &regEnc->initParams;
   NV_ENC_RC_PARAMS *rcParamsCurrent;

   ASSERT(rcParamsNew);
   ASSERT(initParams);

   rcParamsCurrent = &initParams->encodeConfig->rcParams;

   /* Check which parameters have changed */
   if (0) {
#define PARAMCOMPARE(p1)                                                                           \
   do {                                                                                            \
      if (rcParamsCurrent->p1 != rcParamsNew->p1) {                                                \
         REGENCWARN("PARAMCOMPARE: %-40s - p1: %6u -> p2: %6u", #p1, rcParamsCurrent->p1,          \
                    rcParamsNew->p1);                                                              \
      }                                                                                            \
   } while (0);

      PARAMCOMPARE(version);
      PARAMCOMPARE(rateControlMode);
      PARAMCOMPARE(constQP.qpInterP);
      PARAMCOMPARE(constQP.qpInterB);
      PARAMCOMPARE(constQP.qpIntra);
      PARAMCOMPARE(averageBitRate);
      PARAMCOMPARE(maxBitRate);
      PARAMCOMPARE(vbvBufferSize);
      PARAMCOMPARE(vbvInitialDelay);
      PARAMCOMPARE(enableMinQP);
      PARAMCOMPARE(enableMaxQP);
      PARAMCOMPARE(enableInitialRCQP);
      PARAMCOMPARE(enableAQ);
      PARAMCOMPARE(enableLookahead);
      PARAMCOMPARE(disableIadapt);
      PARAMCOMPARE(disableBadapt);
      PARAMCOMPARE(enableTemporalAQ);
      PARAMCOMPARE(zeroReorderDelay);
      PARAMCOMPARE(enableNonRefP);
      PARAMCOMPARE(strictGOPTarget);
      PARAMCOMPARE(aqStrength);
      PARAMCOMPARE(reservedBitFields);
      PARAMCOMPARE(minQP.qpInterP);
      PARAMCOMPARE(minQP.qpInterB);
      PARAMCOMPARE(minQP.qpIntra);
      PARAMCOMPARE(maxQP.qpInterP);
      PARAMCOMPARE(maxQP.qpInterB);
      PARAMCOMPARE(maxQP.qpIntra);
      PARAMCOMPARE(initialRCQP.qpInterP);
      PARAMCOMPARE(initialRCQP.qpInterB);
      PARAMCOMPARE(initialRCQP.qpIntra);
      PARAMCOMPARE(temporallayerIdxMask);
      PARAMCOMPARE(targetQuality);
      PARAMCOMPARE(targetQualityLSB);
      PARAMCOMPARE(lookaheadDepth);
      PARAMCOMPARE(qpMapMode);
   }
   /*
    * If rate control params have changed, reconfigure the encoder with the
    * new ones.
    */
   if (memcmp(rcParamsNew, rcParamsCurrent, sizeof *rcParamsNew) != 0) {
      NV_ENC_RECONFIGURE_PARAMS params = {0};
      NVENCSTATUS status;

      REGENCLOG(6,
                "Updating RC, "
                "max bw %u kb/s -> %u kb/s "
                "avg bw %u kb/s -> %u kb/s",
                rcParamsCurrent->maxBitRate / 1024, rcParamsNew->maxBitRate / 1024,
                rcParamsCurrent->averageBitRate / 1024, rcParamsNew->averageBitRate / 1024);

      REGENCLOG(6, "Updating maxQP:%u -> %u", rcParamsCurrent->maxQP.qpInterP,
                rcParamsNew->maxQP.qpInterP);

      /*
       * qpInterP is the only one that really matters since there are no
       * B frames in this stream and there's only the first I frame.
       *
       * QPs should be within the range of [0-51] for H264 and HEVC, or
       * [0-255] for AV1, but unlikely to ever be 0 intentionally (more
       * likely a incorrect memset)
       */
      ASSERT(rcParamsNew->maxQP.qpInterP > 0);
      ASSERT(rcParamsNew->minQP.qpInterP > 0);
      if (regEnc->useAV1) {
         ASSERT(rcParamsNew->maxQP.qpInterP <= AV1_MAX_QP);
         ASSERT(rcParamsNew->minQP.qpInterP <= AV1_MAX_QP);
      } else {
         ASSERT(rcParamsNew->maxQP.qpInterP <= H264_MAX_QP);
         ASSERT(rcParamsNew->minQP.qpInterP <= H264_MAX_QP);
      }

      /*
       * Two steps:
       * - copy updated RC params to the initialize params we maintain.
       * - copy those initialize params to the temp reconfigure params.
       */
      params.version = NV_ENC_RECONFIGURE_PARAMS_VER;
      memcpy(rcParamsCurrent, rcParamsNew, sizeof *rcParamsNew);
      memcpy(&params.reInitEncodeParams, initParams, sizeof *initParams);

      status = regEnc->encodeFns.nvEncReconfigureEncoder(regEnc->hNVEnc, &params);
      if (status != NV_ENC_SUCCESS) {
         REGENCWARN("%s: encoder reconfigure failed (error %d / 0x%x)", __FUNCTION__, status,
                    status);
         return FALSE;
      }
   }
   return TRUE;
}


/*
 *----------------------------------------------------------------------------
 *
 * VNCEncodeRegionNvEncMBCost2 --
 *
 *      Compute an estimation of the encoding cost, in bytes, of a macroblock
 *      going from QPs qp0 to qp1, as part of an inactive region we are
 *      boosting the quality of.
 *
 *      Note: this function's constants are derived experimentally, and will
 *      not be completely accurate.
 *
 * Results:
 *      The estimated cost in bytes.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

static int
VNCEncodeRegionNvEncMBCost2(int qp0,     // IN
                            int qp1,     // IN
                            uint32 cost, // IN
                            Bool useAV1) // IN
{
   int divider = MAX(qp0, 1);
   int qpmin = CONFIG_H264_QP_MIN;
   int qpmax = useAV1 ? AV1_MAX_QP : CONFIG_H264_QP_MAX;
   ASSERT(qpmin <= qp0);
   ASSERT(qp0 <= qpmax);
   ASSERT(qpmin <= qp1);
   ASSERT(qp1 <= qpmax);
   ASSERT(qp1 <= qp0);
   UNUSED(qpmax);
   UNUSED(qpmin);

   return cost * (qp0 - qp1) / divider;
}


/*
 *----------------------------------------------------------------------------
 *
 * VNCEncodeRegionNvEncUpdateQPMap --
 *
 *      Update the QP delta and value maps, decreasing quality in active screen
 *      regions whilst boosting quality in inactive regions.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

static void
VNCEncodeRegionNvEncUpdateQPMap(VNCRegionEncoderNvEnc *regEnc, // IN/OUT
                                int byteBudget,                // IN
                                double now)                    // IN
{
   int estimatedBytesUsed = 0;
   int mbsPerLine = CEILING(regEnc->width, MBWIDTH);
   int minBoostMBs = 0;
   int iter;
   int mbx;
   int mby;
   int len;
   int i;

   /* Reset number of changed MBs counter. */
   regEnc->refreshMBs = 0;
   regEnc->midQualMBs = 0;

   /*
    * Establish a reasonable number of blocks to boost.
    * Start with 1/8th region; scale based on network and on-screen activity;
    * and floor to 1/8 region.
    */
   minBoostMBs = regEnc->totalMBs / 8;
   minBoostMBs = minBoostMBs * ((double)byteBudget / 12500.0);
   if (regEnc->dirtyMBs > 0) {
      minBoostMBs = minBoostMBs / 2;
   }
   minBoostMBs = MAX(minBoostMBs, regEnc->totalMBs / 8);

   /*
    * First, record the dirty blocks as having a QP of maxQP. Do not set a QP
    * delta - the Nvidia RC should pick a value approximating maxQP for
    * macroblocks with changed image data.
    */
   memset(regEnc->qpDeltaMap, 0, regEnc->totalMBs);
   iter = -1;
   while (VNCBitmask_IterateBlockIndexSpan(regEnc->dirtyMask, &iter, &mbx, &mby, &len)) {
      int mbOffset = mby * mbsPerLine + mbx;
      memset(regEnc->qpMap + mbOffset, regEnc->maxQP, len);
   }

   /*
    * Limit quality when the session goes into non-interactive mode OR
    * has hot-regions in interactive mode.
    */
   if (regEnc->bwSavingEnabled) {
      int qp = regEnc->minQP;
      bwSavingMode_t lastModeBwSaving = regEnc->bwSavingMode;

      regEnc->bwSavingMode = BWSAVING_OFF;
      /* Check for bandwidth saving scenarios */
      if (!VNCEncodeManager_InteractiveMode(VNCUtil_SystemTime(), 5.0)) {
         /* Non-Interactive Mode - bw saving algorithm */
         qp = regEnc->nvencNonInteractiveMinQP;
         regEnc->bwSavingMode = BWSAVING_NON_INTERACTIVE;
      } else if (regEnc->outAvgQP <= 14 && regEnc->nvencHotRegionMinFPS > 0 &&
                 regEnc->nvencHotRegionDeltaQP > 0) {
         /* Interactive Mode - bw saving algorithm for hotregions */
         VNCBitmask *hotAndDirtyMask = VNCBitmask_CreateSimilar(regEnc->hotMask);
         VNCBitmask_Copy(hotAndDirtyMask, regEnc->hotMask);
         VNCBitmask_Intersect(hotAndDirtyMask, regEnc->dirtyMask);

         regEnc->bwSavingMode = BWSAVING_INTERACTIVE;

         /* Set positive deltaQP for hot regions */
         iter = -1;
         while (VNCBitmask_IterateBlockIndexSpan(hotAndDirtyMask, &iter, &mbx, &mby, &len)) {
            int mbOffset = mby * mbsPerLine + mbx;
            memset(regEnc->qpDeltaMap + mbOffset, regEnc->nvencHotRegionDeltaQP, len);
         }
         VNCBitmask_Destroy(hotAndDirtyMask);
      }

      regEnc->rcParamsNew.minQP.qpInterP = qp;
      regEnc->rcParamsNew.minQP.qpInterB = qp;
      regEnc->rcParamsNew.minQP.qpIntra = qp;

      if (lastModeBwSaving != regEnc->bwSavingMode) {
         REGENC_RLOG(1, "bwsaving Mode %d", regEnc->bwSavingMode);
      }
   }

   /*
    * Next, if enough time has passed since the last quality boost, for the
    * blocks that are stale enough, reduce their QP to the configured minimum
    * to achieve maximum quality.
    */
   if ((now - regEnc->lastQualBoostTime) * 1000.0 >= regEnc->qualBoostDelayMs) {
      /*
       * If 2-step boosting is enabled, then do midQP for MBs not at
       * mid-quality level
       */
      if (regEnc->midBoostQPDelta != 0) {
         VNCBitmask *midBoostMask = VNCBitmask_CreateCopy(regEnc->toBoostMask);
         VNCBitmask_Subtract(midBoostMask, regEnc->midQualMask);

         iter = -1;
         while (VNCBitmask_IterateBlockIndexSpan(midBoostMask, &iter, &mbx, &mby, &len)) {
            int mbOffset = mby * mbsPerLine + mbx;
            for (i = 0; i < len; i++, mbOffset++) {
               uint8 qp = regEnc->qpMap[mbOffset];

               regEnc->qpDeltaMap[mbOffset] = regEnc->midBoostQPDelta;
               regEnc->qpMap[mbOffset] = (int)qp + regEnc->midBoostQPDelta;
               estimatedBytesUsed += VNCEncodeRegionNvEncMBCost2(
                  qp, qp + regEnc->midBoostQPDelta, regEnc->mbCostQual, regEnc->useAV1);
               regEnc->midQualMBs++;
               if (estimatedBytesUsed >= byteBudget &&
                   regEnc->midQualMBs + regEnc->refreshMBs >= minBoostMBs) {
                  VNCBitmask_SetBlockSpan(regEnc->midQualMask, mbx, mby, i + 1);
                  VNCBitmask_Destroy(midBoostMask);
                  goto out;
               }
            }
            VNCBitmask_SetBlockSpan(regEnc->midQualMask, mbx, mby, len);
         }
         VNCBitmask_Destroy(midBoostMask);
      }

      /*
       * Try for the best quality using minQP for - all toBoost MBs (if 2-step
       * boosting is disabled) or MBs already at mid-quality level (if 2-step
       * boosting enabled)
       */
      VNCBitmask *maxBoostMask = VNCBitmask_CreateCopy(regEnc->toBoostMask);
      if (regEnc->midBoostQPDelta != 0) {
         VNCBitmask_Intersect(maxBoostMask, regEnc->midQualMask);
      }

      iter = -1;
      while (VNCBitmask_IterateBlockIndexSpan(maxBoostMask, &iter, &mbx, &mby, &len)) {
         int mbOffset = mby * mbsPerLine + mbx;
         for (i = 0; i < len; i++, mbOffset++) {
            uint8 qp = regEnc->qpMap[mbOffset];

            if (qp <= regEnc->minQP) {
               continue;
            }
            regEnc->qpDeltaMap[mbOffset] = (int)regEnc->minQP - (int)qp;
            regEnc->qpMap[mbOffset] = regEnc->minQP;
            estimatedBytesUsed +=
               VNCEncodeRegionNvEncMBCost2(qp, regEnc->minQP, regEnc->mbCostQual, regEnc->useAV1);
            regEnc->refreshMBs++;
            if (estimatedBytesUsed >= byteBudget &&
                regEnc->midQualMBs + regEnc->refreshMBs >= minBoostMBs) {
               VNCBitmask_SetBlockSpan(regEnc->maxQualMask, mbx, mby, i + 1);
               VNCBitmask_ClearBlockSpan(regEnc->midQualMask, mbx, mby, i + 1);
               VNCBitmask_Destroy(maxBoostMask);
               goto out;
            }
         }
         VNCBitmask_SetBlockSpan(regEnc->maxQualMask, mbx, mby, len);
         VNCBitmask_ClearBlockSpan(regEnc->midQualMask, mbx, mby, len);
      }
      VNCBitmask_Destroy(maxBoostMask);

   out:
      VNCBitmask_Subtract(regEnc->toBoostMask, regEnc->maxQualMask);
      if (regEnc->refreshMBs > 0) {
         regEnc->lastQualBoostTime = now;
      }
   }

   /* No block should be marked at both mid and max levels of quality*/
   if (vmx86_devel && regEnc->midBoostQPDelta != 0) {
      ASSERT(!VNCBitmask_IsIntersecting(regEnc->maxQualMask, regEnc->midQualMask));
   }

   /* If we are at the highest quality level then there cannot be dirtyMBs */
   if (vmx86_devel && VNCBitmask_PopCount(regEnc->maxQualMask) == regEnc->totalMBs) {
      ASSERT(regEnc->dirtyMBs == 0);
   }

   /*
    * Last final wipe at highest quality if:
    * - regEnc skipped encoding more than N times
    * OR
    * - no dirty MBs to encode
    * - last reported avg QP by NVENC was == minQP
    * - last maxQP set was != minQP
    */
   if ((regEnc->consecutiveEncodeSkips > 100 && regEnc->isFrameMaxQual == FALSE) ||
       /* It's the ideal solution fails on multimon due to VNCencoder bug */
       /* (VNCBitmask_PopCount(regEnc->maxQualMask) == regEnc->totalMBs) || */
       (regEnc->dirtyMBs == 0 && regEnc->outAvgQP == regEnc->minQP &&
        regEnc->lastMaxQP != regEnc->minQP)) {
      REGENC_RLOG(6, "Final wipe frame");
      REGENC_RLOG(7,
                  "consecutiveEncodeSkips: %d maxQuality: %d "
                  "dirtyMBs: %d outAvgQP: %2d lastMaxQP: %2d minQP: %2d",
                  regEnc->consecutiveEncodeSkips,
                  VNCBitmask_PopCount(regEnc->maxQualMask) == regEnc->totalMBs, regEnc->dirtyMBs,
                  regEnc->outAvgQP, regEnc->lastMaxQP, regEnc->minQP);

      regEnc->finalWipeFrame = TRUE;
      regEnc->customMaxQP = regEnc->minQP;
      regEnc->rcParamsNew.maxQP.qpInterP = regEnc->customMaxQP;
      regEnc->rcParamsNew.maxQP.qpInterB = regEnc->customMaxQP;
      regEnc->rcParamsNew.maxQP.qpIntra = regEnc->customMaxQP;

      regEnc->rcParamsNew.minQP.qpInterP = regEnc->minQP;
      regEnc->rcParamsNew.minQP.qpInterB = regEnc->minQP;
      regEnc->rcParamsNew.minQP.qpIntra = regEnc->minQP;
   }

   /*
    * Dirty map is in 16x16 CU format.
    * - H264 can use it as-is.
    * - HEVC needs it downsampled to a mask with 32x32 CU.
    * - AV1 needs it downsampled to a mask with 64x64 CU.
    */
   if ((regEnc->useAV1 || regEnc->useHEVC) && regEnc->useCustomRateControl) {
      int scale = CEILING(regEnc->mbWidth, regEnc->cuWidth);
      int x, y;

      memset(regEnc->qpDeltaMapCTU, 0, regEnc->totalCUs);
      for (y = 0; y < regEnc->mbHeight; y++) {
         for (x = 0; x < regEnc->mbWidth; x++) {
            int idx16 = x + y * regEnc->mbWidth;
            int idxCTU = (x / scale) + (y / scale) * regEnc->cuWidth;
            ASSERT(idxCTU <= regEnc->totalCUs);

            /* Only use positive deltaQPs in CU if no other MB is negative */
            if (regEnc->qpDeltaMap[idx16] > 0 && regEnc->qpDeltaMapCTU[idxCTU] == 0) {
               regEnc->qpDeltaMapCTU[idxCTU] =
                  MAX(regEnc->qpDeltaMapCTU[idxCTU], regEnc->qpDeltaMap[idx16]);
            } else {
               regEnc->qpDeltaMapCTU[idxCTU] =
                  MIN(regEnc->qpDeltaMapCTU[idxCTU], regEnc->qpDeltaMap[idx16]);
            }
         }
      }
   }

   /* Debugging of deltaQPmaps */
   if (0) {
      if (regEnc->qpDeltaMap != NULL) {
         VNCEncodeRegionPrintMatrix("qpDeltaMap", regEnc->qpDeltaMap, regEnc->mbWidth,
                                    regEnc->mbHeight);
      }

      if (regEnc->qpDeltaMapCTU != NULL) {
         VNCEncodeRegionPrintMatrix("qpDeltaMapCTU", regEnc->qpDeltaMapCTU, regEnc->cuWidth,
                                    regEnc->cuHeight);
      }
   }
}


/*
 *----------------------------------------------------------------------------
 *
 * VNCEncodeRegionNvEncUpdateMaps --
 *
 *    Updates:
 *    - the dirtyMask with the contents of the accumulated diff map
 *    - the age map, and exports blocks older than 1 second to the "old" mask
 *    - the "to boost" mask, defined as the "old" mask minus what has already
 *      been boosted (either just for this sweep, or fully to maximum quality).
 *
 * Results:
 *    None.
 *
 * Side effects:
 *    Might clear the "to boost" mask to start the next sweep.
 *
 *----------------------------------------------------------------------------
 */

static void
VNCEncodeRegionNvEncUpdateMaps(VNCRegionEncoderNvEnc *regEnc) // IN/OUT
{
   double ages[] = {2.0, 1.0, 0.5, 0.25, 0.1};
   int ageIndex = 0;

   VNCBitmask_Subtract(regEnc->maxQualMask, regEnc->dirtyMask);

   VNCAgeMap_SetTime(regEnc->ageMap, VNCUtil_SystemTime());
   VNCAgeMap_TouchBitmask(regEnc->ageMap, regEnc->dirtyMask);

   do {
      /*
       * Go through successively younger age cut-offs, in order to first boost
       * the oldest regions of the screen and finish with the most recent.
       */
      VNCAgeMap_GetBitmaskOlder(regEnc->ageMap, ages[ageIndex++], regEnc->toBoostMask);
      VNCBitmask_Subtract(regEnc->toBoostMask, regEnc->maxQualMask);
   } while (VNCBitmask_IsEmpty(regEnc->toBoostMask) && ageIndex < ARRAYSIZE(ages));
}


/*
 *----------------------------------------------------------------------------
 *
 * VNCEncodeRegionNvEncRect --
 *
 *      Send h264 network abstraction layer units across our VNC
 *      connection.  Currently bundling all NALs into a single
 *      rectangle.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      Expands the encode buffer, might allocate memory.
 *
 *----------------------------------------------------------------------------
 */

static void
VNCEncodeRegionNvEncRect(BoxPtr dstRect,    // IN
                         VNCEncodeBuf *buf, // IN/OUT
                         uint16 opcode,     // IN
                         uint16 streamId,   // IN
                         Bool isH264YUV444, // IN
                         const void *data,  // IN
                         int dataSize)      // IN
{
   VNCH264Rect *rect = VNCEncodeBufRect(buf, sizeof *rect + dataSize);

   /*
    * The destination rectangle is currently always full-screen,
    * although the encoding allows more flexibilty than this.
    */
   if (isH264YUV444) {
      VNCEncodeRectHeader(&rect->header, dstRect, VNCH264YUV444RectEnc);
   } else {
      VNCEncodeRectHeader(&rect->header, dstRect, VNCH264RectEnc);
   }

   rect->opcode = htons(opcode);
   rect->streamId = htons(streamId);
   rect->dataLength = htonl(dataSize);

   /*
    * Copy the payload into the buffer
    */
   memcpy(rect + 1, data, dataSize);
}


/*
 *----------------------------------------------------------------------------
 *
 * VNCEncodeRegionNvEncDumpBitstream --
 *
 *      Dump compressed Elementary Stream.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      Generates HUGE files.
 *
 *----------------------------------------------------------------------------
 */

static void
VNCEncodeRegionNvEncDumpBitstream(VNCRegionEncoderNvEnc *regEnc,  // IN / OUT
                                  const unsigned char *bitstream, // IN
                                  int frameSize)                  // IN
{
   VERIFY(vmx86_debug);

   if (regEnc == NULL) {
      return;
   }

   if (regEnc->fhandleBitstream) {
      /*
       * Log the data to a file, which can be played with ffplay,
       * mplayer, etc.
       */
      if (fwrite(bitstream, frameSize, 1, regEnc->fhandleBitstream) == 0) {
         REGENCWARN("Write to bitstream dump file failed");
         VNCEncodeRegionCloseFile(&regEnc->fhandleBitstream);
      }
   }
}


/*
 *----------------------------------------------------------------------------
 *
 * VNCEncodeRegionNvEncSendNals --
 *
 *      Sends the encoded h264 payload for packetization
 *      and transmission.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      Expands the encode buffer, might allocate memory.
 *
 *----------------------------------------------------------------------------
 */

static void
VNCEncodeRegionNvEncSendNals(VNCRegionEncoderNvEnc *regEnc, // IN
                             VNCEncodeBuf *buf,             // IN/OUT
                             const void *hwh264nal,         // IN
                             int nalCount,                  // IN
                             int totalSize)                 // IN
{
   int opcode = regEnc->useAV1 ? VNC_AV1_DATA : (regEnc->useHEVC ? VNC_HEVC_DATA : VNC_H264_DATA);
   const VMRect *regionRect = &regEnc->base.config.region.rect;
   BoxRec rect;

   if (regEnc->firstTime) {
      opcode = regEnc->useAV1 ? VNC_AV1_RESET_STREAM
                              : (regEnc->useHEVC ? VNC_HEVC_RESET_STREAM : VNC_H264_RESET_STREAM);
      regEnc->firstTime = FALSE;
   }

   RECT_SETVMRECT(&rect, regionRect);

   /*
    * Pack the NALs into our EncodeBuf with an appropriate VNC
    * rectangle header.
    */
   if (regEnc->encodeAsH264) {
      VNCEncodeRegionNvEncRect(&rect, buf, opcode, regEnc->base.config.screenNum, regEnc->useYUV444,
                               hwh264nal, totalSize);
   } else {
      ASSERT(BitVector_Test(regEnc->base.config.caps, VNCH264MP4RectCap));
      VNCEncodeMP4Rect(&rect, buf, opcode, regEnc->base.config.screenNum, hwh264nal, totalSize,
                       nalCount, regEnc->frameNumber);
   }

   regEnc->frameNumber++;
}


/*
 *----------------------------------------------------------------------------
 *
 * VNCEncodeRegionNvEncGetHandleInt --
 *
 *      Helper function for loading API function table and opening an NvEnc
 *      encode session.
 *
 * Results:
 *      The encode session handle if successful; NULL otherwise.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

static HANDLE
VNCEncodeRegionNvEncGetHandleInt(VNCRegionEncoderNvEnc *regEnc,    // IN
                                 NV_ENCODE_API_FUNCTION_LIST *fns) // IN/OUT
{
   NV_ENC_OPEN_ENCODE_SESSION_EX_PARAMS openParams;
   HANDLE hNVEnc;
   NVENCSTATUS status;
   void *devicePtr = NULL;

   ASSERT(fns);

   if (regEnc == NULL) {
      return NULL;
   }
#if defined(_WIN32)
   devicePtr = regEnc->pD3d11Device;
#endif

   fns->version = NV_ENCODE_API_FUNCTION_LIST_VER;

   REGENC_RLOG(9, "Creating NvEnc instance");
   status = nvidiasdk.NvEncodeAPICreateInstance(fns);
   if (NV_ENC_FAILED(status)) {
      REGENCWARN("%s: NvEncodeAPICreateInstance failed (%d)", __FUNCTION__, status);
      return NULL;
   }

   memset(&openParams, 0, sizeof(openParams));
   openParams.version = NV_ENC_OPEN_ENCODE_SESSION_EX_PARAMS_VER;
   openParams.apiVersion = NVENCAPI_VERSION;
   openParams.device = devicePtr;
#ifdef _WIN32
   openParams.deviceType = NV_ENC_DEVICE_TYPE_DIRECTX;
#else
   openParams.deviceType = NV_ENC_DEVICE_TYPE_OPENGL;
#endif

   REGENC_RLOG(9, "Opening NvEnc encode session");
   status = fns->nvEncOpenEncodeSessionEx(&openParams, &hNVEnc);
   if (NV_ENC_FAILED(status) || hNVEnc == NULL) {
      REGENCWARN("%s: Failed to open Encode session (%d / 0x%x)", __FUNCTION__, status, status);
      return NULL;
   }
   return hNVEnc;
}


/*
 *----------------------------------------------------------------------------
 *
 * VNCEncodeRegionNvEncGetHandle --
 *
 *      Create and setup NvENC object.
 *
 * Results:
 *      Returns a pointer to the NvENC object if successful or NULL otherwise.
 *
 * Side effects:
 *      None
 *
 *----------------------------------------------------------------------------
 */

static void *
VNCEncodeRegionNvEncGetHandle(VNCRegionEncoderNvEnc *regEnc, // IN
                              int width,                     // IN
                              int height)                    // IN
{
   if (regEnc == NULL) {
      return NULL;
   }

   const VNCEncodeDynamicConfig *config = &regEnc->base.config.dynamicConfig;
   HANDLE hNVEnc = NULL;
   NV_ENC_CREATE_BITSTREAM_BUFFER stAllocBitstream;
   NV_ENC_CONFIG_H264 *h264Config = &regEnc->config.encodeCodecConfig.h264Config;
   NV_ENC_CONFIG_HEVC *hevcConfig = &regEnc->config.encodeCodecConfig.hevcConfig;
#if NVENCAPI_MAJOR_VERSION > 8
   NV_ENC_CONFIG_AV1 *av1Config = &regEnc->config.encodeCodecConfig.av1Config;
#endif
   GUID presetGuid = {0};
   const char *presetString = NULL;
   NV_ENC_QP minQP = {regEnc->minQP, regEnc->minQP, regEnc->minQP};
   NV_ENC_QP maxQP = {regEnc->maxQP, regEnc->maxQP, regEnc->maxQP};
   NVENCSTATUS status;
   GUID encodeGuid;
   Bool isHdr = (regEnc->useAV1 || regEnc->useHEVC) && regEnc->base.config.region.hdrEnabled;
   Bool clientSupportsH264MultiReferences = regEnc->base.config.clientSupportsH264MultiReferences;
   int maxRefFrames =
      clientSupportsH264MultiReferences ? HZN_CLAMP(config->nvencMaxReferenceFrames, 0, 16) : 1;
   ASSERT(sCapsQueried == TRUE);
   int multiPass = Config_GetLong(-1, "RemoteDisplay.nvencMultiPass");
   int rcMode = Config_GetLong(-1, "RemoteDisplay.nvencRCMode");
   int codingMode = Config_GetLong(-1, "RemoteDisplay.nvencH264CodingMode");
   int tuning = Config_GetLong(-1, "RemoteDisplay.nvencTuning");
   int fullRange;
   int colorMatrix;
   int colorPrimaries;
   int transferCharacteristics;

   if (config->nvencUseLegacyEncoderPreset) {
#if (NVENCAPI_MAJOR_VERSION > 8)
      presetGuid = NV_ENC_PRESET_P2_GUID;
      presetString = "NV_ENC_PRESET_P2_GUID";
   } else {
      presetGuid = NV_ENC_PRESET_P1_GUID;
      presetString = "NV_ENC_PRESET_P1_GUID";
#else
      presetGuid = NV_ENC_PRESET_LOW_LATENCY_DEFAULT_GUID;
      presetString = "NV_ENC_PRESET_LOW_LATENCY_DEFAULT_GUID";
   } else {
      presetGuid = NV_ENC_PRESET_LOW_LATENCY_HP_GUID;
      presetString = "NV_ENC_PRESET_LOW_LATENCY_HP_GUID";
#endif
   }

   if (!(hNVEnc = VNCEncodeRegionNvEncGetHandleInt(regEnc, &regEnc->encodeFns))) {
      return NULL;
   }
   REGENC_RLOG(1,
               "Using:\n"
               "- encoder handle %p.\n"
               "- encode size %ux%u.\n"
               "- frame rate %u/%u.\n"
               "- RC mode %d.\n"
               "- multi-pass mode %d.\n"
               "- entropy coding mode %d.\n"
               "- tuning mode %d.",
               hNVEnc, width, height, regEnc->base.config.dynamicConfig.maxFPS, 1, rcMode,
               multiPass, codingMode, tuning);

#if NVENCAPI_MAJOR_VERSION > 8
   if (regEnc->useAV1) {
      encodeGuid = NV_ENC_CODEC_AV1_GUID;
   } else
#endif
      if (regEnc->useHEVC) {
      encodeGuid = NV_ENC_CODEC_HEVC_GUID;
   } else {
      encodeGuid = NV_ENC_CODEC_H264_GUID;
   }

   VNCEncodeRegionNvEncPrintEncodeCaps(regEnc, hNVEnc);

   /* Fetch a preset config for use as a template. */
   memset(&regEnc->initParams, 0, sizeof(regEnc->initParams));
   regEnc->initParams.version = NV_ENC_INITIALIZE_PARAMS_VER;
   regEnc->initParams.encodeGUID = encodeGuid;
   regEnc->initParams.presetGUID = presetGuid;
   regEnc->initParams.encodeWidth = width;
   regEnc->initParams.encodeHeight = height;
#if NVENCAPI_MAJOR_VERSION > 8
   regEnc->initParams.tuningInfo = NV_ENC_TUNING_INFO_LOW_LATENCY;
   if (tuning > -1) {
      regEnc->initParams.tuningInfo =
         HZN_CLAMP(tuning, NV_ENC_TUNING_INFO_HIGH_QUALITY, NV_ENC_TUNING_INFO_LOSSLESS);
      REGENC_RLOG(1, "Set NV_ENC_INITIALIZE_PARAMS::tuningInfo to %d.",
                  regEnc->initParams.tuningInfo);
   }
#endif
   regEnc->initParams.darWidth = width;
   regEnc->initParams.darHeight = height;
   regEnc->initParams.maxEncodeWidth = width;
   regEnc->initParams.maxEncodeHeight = height;
   regEnc->initParams.frameRateDen = 1;
   regEnc->initParams.frameRateNum = regEnc->base.config.dynamicConfig.maxFPS;
   regEnc->initParams.enablePTD = 1;

   regEnc->presetConfig.version = NV_ENC_PRESET_CONFIG_VER;
   regEnc->presetConfig.presetCfg.version = NV_ENC_CONFIG_VER;

#if NVENCAPI_MAJOR_VERSION > 8
   status = regEnc->encodeFns.nvEncGetEncodePresetConfigEx(
      hNVEnc, encodeGuid, presetGuid, regEnc->initParams.tuningInfo, &regEnc->presetConfig);
#else
   status = regEnc->encodeFns.nvEncGetEncodePresetConfig(hNVEnc, encodeGuid, presetGuid,
                                                         &regEnc->presetConfig);
#endif
   if (status != NV_ENC_SUCCESS) {
      REGENCWARN("%s: encoder preset fetch failed (error %d / 0x%x)", __FUNCTION__, status, status);
      goto fail;
   }

   /* Copy the preset into the actual config. */
   memcpy(&regEnc->config, &regEnc->presetConfig.presetCfg, sizeof(NV_ENC_CONFIG));

   /* Modify the parameters which interest us. */
   if (regEnc->useAV1) {         // AV1, nothing currently
   } else if (regEnc->useHEVC) { // HEVC
      if (regEnc->useYUV444) {
         regEnc->config.profileGUID = NV_ENC_HEVC_PROFILE_FREXT_GUID;
      } else if (regEnc->base.config.region.hdrEnabled) {
         regEnc->config.profileGUID = NV_ENC_HEVC_PROFILE_MAIN10_GUID;
      } else {
         regEnc->config.profileGUID = NV_ENC_HEVC_PROFILE_MAIN_GUID;
      }
   } else { // H264
      regEnc->config.profileGUID =
         regEnc->useYUV444 ? NV_ENC_H264_PROFILE_HIGH_444_GUID : NV_ENC_H264_PROFILE_BASELINE_GUID;
      if (codingMode > -1) {
         ASSERT(h264Config != NULL);
         h264Config->entropyCodingMode =
            HZN_CLAMP(codingMode, NV_ENC_H264_ENTROPY_CODING_MODE_AUTOSELECT,
                      NV_ENC_H264_ENTROPY_CODING_MODE_CAVLC);
         REGENC_RLOG(1, "Set NV_ENC_CONFIG_H264::entropyCodingMode to %d.",
                     h264Config->entropyCodingMode);
      }
   }
   regEnc->config.rcParams.version = NV_ENC_RC_PARAMS_VER;
   regEnc->config.rcParams.enableMinQP = 1;
   regEnc->config.rcParams.minQP = minQP;
   regEnc->config.rcParams.enableMaxQP = 1;
   regEnc->config.rcParams.maxQP = maxQP;
   regEnc->config.rcParams.qpMapMode =
      regEnc->useCustomRateControl ? NV_ENC_QP_MAP_DELTA : NV_ENC_QP_MAP_DISABLED;

   regEnc->config.rcParams.rateControlMode = NV_ENC_PARAMS_RC_VBR;
   if (rcMode > -1) {
      regEnc->config.rcParams.rateControlMode =
         HZN_CLAMP(rcMode, NV_ENC_PARAMS_RC_CONSTQP, NV_ENC_PARAMS_RC_CBR);
      REGENC_RLOG(1, "Set NV_ENC_INITIALIZE_PARAMS::rateControlMode to %d.",
                  regEnc->config.rcParams.rateControlMode);
   }
#if NVENCAPI_MAJOR_VERSION > 8
   if (multiPass > -1) {
      regEnc->config.rcParams.multiPass =
         HZN_CLAMP(multiPass, NV_ENC_MULTI_PASS_DISABLED, NV_ENC_TWO_PASS_FULL_RESOLUTION);
      REGENC_RLOG(1, "Set NV_ENC_RC_PARAMS::multiPass to %d.", regEnc->config.rcParams.multiPass);
   }
#endif

   regEnc->initParams.encodeConfig = &regEnc->config;
   VNCEncodeRegionNvEncSetBandwidth(regEnc, config->maxBandwidth, &regEnc->config.rcParams);

   memcpy(&regEnc->rcParamsNew, &regEnc->config.rcParams, sizeof regEnc->config.rcParams);

#define NVENC_SET_CODEC_CONFIG_45(key, value)                                                      \
   do {                                                                                            \
      if (regEnc->useAV1) {                                                                        \
      } else if (regEnc->useHEVC)                                                                  \
         hevcConfig->key = (value);                                                                \
      else                                                                                         \
         h264Config->key = (value);                                                                \
   } while (FALSE)

#define NVENC_SET_CODEC_VUICONFIG_45(key, value)                                                   \
   do {                                                                                            \
      if (regEnc->useAV1) {                                                                        \
      } else if (regEnc->useHEVC)                                                                  \
         hevcConfig->hevcVUIParameters.key = (value);                                              \
      else                                                                                         \
         h264Config->h264VUIParameters.key = (value);                                              \
   } while (FALSE)

#if NVENCAPI_MAJOR_VERSION > 8
#   define NVENC_SET_CODEC_CONFIG_451(key, value)                                                  \
      do {                                                                                         \
         if (regEnc->useAV1)                                                                       \
            av1Config->key = (value);                                                              \
         else if (regEnc->useHEVC)                                                                 \
            hevcConfig->key = (value);                                                             \
         else                                                                                      \
            h264Config->key = (value);                                                             \
      } while (FALSE)
#else
#   define NVENC_SET_CODEC_CONFIG_451(key, value) NVENC_SET_CODEC_CONFIG_45(key, value)
#endif

   NVENC_SET_CODEC_CONFIG_45(sliceMode, 3);
   NVENC_SET_CODEC_CONFIG_45(sliceModeData,
                             regEnc->base.config.clientSupportsH264MultiSlice ? 4 : 1);

   if (config->h264keyframeInterval != 0) {
      /* keyframe interval should be between 10 seconds and 1 hour at 30FPS */
      uint32_t idrPeriod = HZN_CLAMP(config->h264keyframeInterval, 10 * 30, 3600 * 30);
      NVENC_SET_CODEC_CONFIG_451(idrPeriod, idrPeriod);
      REGENC_RLOG(1, "IDR interval: %u.\n", idrPeriod);
   } else {
      NVENC_SET_CODEC_CONFIG_451(idrPeriod, NVENC_INFINITE_GOPLENGTH);
   }
   /*
    * Bug 2393714:
    *
    * Our internal parsers used in the legacy MKS can only handle 1 reference
    * frame.
    */
#if NVENCAPI_MAJOR_VERSION > 8
   if (regEnc->useAV1) {
      av1Config->maxNumRefFramesInDPB = maxRefFrames;
   } else
#endif
      if (regEnc->useHEVC) {
      hevcConfig->maxNumRefFramesInDPB = maxRefFrames;
   } else {
      h264Config->maxNumRefFrames = maxRefFrames;

      if (regEnc->base.config.dynamicConfig.h264disableDeblockingFilter) {
         h264Config->disableDeblockingFilterIDC = 1;
         REGENC_RLOG(1, "Disabling deblocking filter.");
      }
   }

   REGENC_RLOG(1, "MultiRefClientSupport: %d maxRefFrames: %d", clientSupportsH264MultiReferences,
               maxRefFrames);

   /*
    * Set the bitstreamRestriction flag in VUI parameters. Without this flag,
    * the HTML connection with chrome - H264 decoding was experiencing delayed
    * frames/freeze when using hardware acceleration. This flag indicates
    * chrome that stream can be decoded and the pictures emitted immdediately.
    * With Google: https://bugs.chromium.org/p/chromium/issues/detail?id=1355705
    */
   NVENC_SET_CODEC_VUICONFIG_45(bitstreamRestrictionFlag, 1);
   /*
    * Setup the appropriate color settings.
    * - if configured, use BT.709 for SDR and BT.2020 for HDR. Use full range luma/chroma.
    * - otherwise, use BT.601 for SDR and BT.2020 for HDR. Use limited range luma/chroma.
    */
   NVENC_SET_CODEC_VUICONFIG_45(videoSignalTypePresentFlag, 1);
   NVENC_SET_CODEC_VUICONFIG_45(videoFormat, H2645_VIDEO_FORMAT_UNKNOWN);
   if (regEnc->useFullRangeAndBT7092020) {
      fullRange = 1;
      colorPrimaries = isHdr ? H2645_COLOR_MATRIX_BT2020NCL : H2645_COLOR_MATRIX_BT709;
      colorMatrix = isHdr ? H2645_COLOR_MATRIX_BT2020NCL : H2645_COLOR_MATRIX_BT709;
      transferCharacteristics =
         isHdr ? H2645_TRANSFER_CHARACTERISTICS_BT2020 : H2645_TRANSFER_CHARACTERISTICS_BT709;
   } else {
      fullRange = 0;
      colorMatrix = isHdr ? H2645_COLOR_MATRIX_BT2020NCL : H2645_COLOR_MATRIX_BT601;
      colorPrimaries = isHdr ? H2645_COLOR_PRIMARIES_BT2020 : H2645_COLOR_PRIMARIES_BT601;
      transferCharacteristics =
         isHdr ? H2645_TRANSFER_CHARACTERISTICS_BT2020 : H2645_TRANSFER_CHARACTERISTICS_BT601;
   }
#if NVENCAPI_MAJOR_VERSION > 8
   if (regEnc->useAV1) {
      av1Config->colorRange = fullRange;
      av1Config->colorPrimaries = colorPrimaries;
      av1Config->transferCharacteristics = transferCharacteristics;
      av1Config->matrixCoefficients = colorMatrix;
   } else
#endif
   {
      NVENC_SET_CODEC_VUICONFIG_45(videoFullRangeFlag, fullRange);
      NVENC_SET_CODEC_VUICONFIG_45(colourDescriptionPresentFlag, 1);
      NVENC_SET_CODEC_VUICONFIG_45(colourMatrix, colorMatrix);
      NVENC_SET_CODEC_VUICONFIG_45(colourPrimaries, colorPrimaries);
      NVENC_SET_CODEC_VUICONFIG_45(transferCharacteristics, transferCharacteristics);
   }

   if (regEnc->useYUV444) {
      /* No chroma fiddling needed in YUV 4:4:4 mode (lossless chroma). */
      NVENC_SET_CODEC_CONFIG_451(chromaFormatIDC, 3);
   } else {
      NVENC_SET_CODEC_CONFIG_451(chromaFormatIDC, 1);
   }
#undef NVENC_SET_CODEC_CONFIG_451
#undef NVENC_SET_CODEC_VUICONFIG_45
#undef NVENC_SET_CODEC_CONFIG_45

   if (isHdr) {
      if (regEnc->useHEVC) {
         hevcConfig->pixelBitDepthMinus8 = 2;
      } else if (regEnc->useAV1) {
#if NVENCAPI_MAJOR_VERSION > 8
         av1Config->pixelBitDepthMinus8 = 2;
#endif
      }
   }

   {
      GUID g = regEnc->initParams.encodeGUID;
      REGENC_RLOG(1, "Using encode GUID " _PRIGUID ".", g.Data1, g.Data2, g.Data3, g.Data4[0],
                  g.Data4[1], g.Data4[2], g.Data4[3], g.Data4[4], g.Data4[5], g.Data4[6],
                  g.Data4[7]);
      g = regEnc->initParams.presetGUID;
      REGENC_RLOG(1, "Using preset GUID " _PRIGUID ".", g.Data1, g.Data2, g.Data3, g.Data4[0],
                  g.Data4[1], g.Data4[2], g.Data4[3], g.Data4[4], g.Data4[5], g.Data4[6],
                  g.Data4[7]);
   }

   /* Initialize the encoder using the config. */
   status = regEnc->encodeFns.nvEncInitializeEncoder(hNVEnc, &regEnc->initParams);
   if (status != NV_ENC_SUCCESS) {
      REGENCWARN("%s: encoder initialization failed (error %d / 0x%x)", __FUNCTION__, status,
                 status);
      goto fail;
   }

   /* Allocate the output bitstream buffer. */
   memset(&stAllocBitstream, 0, sizeof(stAllocBitstream));
   stAllocBitstream.version = NV_ENC_CREATE_BITSTREAM_BUFFER_VER;
   status = regEnc->encodeFns.nvEncCreateBitstreamBuffer(hNVEnc, &stAllocBitstream);
   if (status != NV_ENC_SUCCESS) {
      REGENCWARN("%s: Failed to allocate output buffers (%d / 0x%x)", __FUNCTION__, status, status);
      goto fail;
   }

   regEnc->pBSBuffer = stAllocBitstream.bitstreamBuffer;

   /*
    * As a final step, log which color space is being used, as that information
    * is not included in the region encoder name string.
    */
   {
#ifdef _WIN32
      const char *strUnifiedD3D =
         (regEnc->useUnifiedD3DDevice ? " with unified D3D" : " without unified D3D");
#else
      const char *strUnifiedD3D = "";
#endif
      REGENCLG0("%s: using region encoder %s %d-bit color space%s."
                " Encoder preset profile %s. Using SDK %d.%d.",
                __FUNCTION__, VNCRegionEncoder_GetTypeName(regEnc->base.type), isHdr ? 10 : 8,
                strUnifiedD3D, presetString, NVENCAPI_MAJOR_VERSION, NVENCAPI_MINOR_VERSION);
   }

   return hNVEnc;

fail:
   status = regEnc->encodeFns.nvEncDestroyEncoder(hNVEnc);
   if (NV_ENC_FAILED(status)) {
      REGENCWARN("%s - Failed to destroy the NVIDIA HW encoder. Error: %d", __FUNCTION__, status);
   }
   return NULL;
}


/*
 *----------------------------------------------------------------------------
 *
 * VNCEncodeRegionNvEncCreate --
 *
 *      Create the region encoder's NvENC object.
 *
 * Results:
 *      Returns TRUE if successful, FALSE otherwise.
 *
 * Side effects:
 *      None
 *
 *----------------------------------------------------------------------------
 */

static VNCError
VNCEncodeRegionNvEncCreate(VNCRegionEncoderNvEnc *regEnc,         // IN
                           const VNCRegEncFrameState *frameState) // IN
{
   const VMRect *rect = &regEnc->base.config.region.rect;
   VNCError error = VNC_SUCCESS;
   int i;

   /* UnifiedD3DDevice is a Windows-only feature. */
   if (!regEnc->useUnifiedD3DDevice || vmx86_linux) {
      error = VNCEncodeRegionNvEncCreateOS(regEnc, frameState->adapterId);
      if (error != VNC_SUCCESS) {
         REGENCWARN("%s: VNCEncodeRegionNvEncCreateOS() Failed", __FUNCTION__);
         goto exit;
      }
   }

#ifdef _WIN32
   if (regEnc->useUnifiedD3DDevice) {
      ID3D11Texture2D_GetDevice((ID3D11Texture2D *)frameState->texture, &regEnc->pD3d11Device);
   }
#endif

   /* Create the encoder device for the i-th screen. */
   regEnc->hNVEnc = VNCEncodeRegionNvEncGetHandle(regEnc, Rect_WH(rect));
   if (!regEnc->hNVEnc) {
      error = VNC_ERROR_HW_ENCODE_FAILED;
      REGENCWARN("%s: Failed to create the NVIDIA NVENC HW encoder", __FUNCTION__);
      goto exit;
   }

   regEnc->totalMBs = CEILING(Rect_Width(rect), MBWIDTH) * CEILING(Rect_Height(rect), MBWIDTH);
   ASSERT(regEnc->totalMBs > 0);

   if (regEnc->useCustomRateControl) {
      regEnc->qpDeltaMap = Util_SafeCalloc(1, regEnc->totalMBs);
      regEnc->qpMap = Util_SafeMalloc(regEnc->totalMBs);
      for (i = 0; i < regEnc->totalMBs; i++) {
         regEnc->qpMap[i] = regEnc->base.config.dynamicConfig.qpLevel1H264;
      }
   }

   /* Set the H.264 per frame encoding parameter defaults. */
   regEnc->picParams.version = NV_ENC_PIC_PARAMS_VER;
   regEnc->picParams.inputBuffer = 0;
   regEnc->picParams.bufferFmt = 0;
   regEnc->picParams.inputWidth = Rect_Width(rect);
   regEnc->picParams.inputHeight = Rect_Height(rect);
   if (regEnc->useCustomRateControl) {
      if (regEnc->useAV1) {
         /* NVENC AV1 SuperBlock/CTU size is 64x64 */
         regEnc->cuWidth = CEILING(Rect_Width(rect), 64);
         regEnc->cuHeight = CEILING(Rect_Height(rect), 64);
         regEnc->totalCUs = regEnc->cuWidth * regEnc->cuHeight;
         regEnc->qpDeltaMapCTU = Util_SafeCalloc(1, regEnc->totalCUs);
         regEnc->picParams.qpDeltaMap = regEnc->qpDeltaMapCTU;
         regEnc->picParams.qpDeltaMapSize = regEnc->totalCUs;
      } else if (regEnc->useHEVC) {
         /* NVENC HEVC CTU size is 32x32 */
         regEnc->cuWidth = CEILING(Rect_Width(rect), 32);
         regEnc->cuHeight = CEILING(Rect_Height(rect), 32);
         regEnc->totalCUs = regEnc->cuWidth * regEnc->cuHeight;
         regEnc->qpDeltaMapCTU = Util_SafeCalloc(1, regEnc->totalCUs);
         regEnc->picParams.qpDeltaMap = regEnc->qpDeltaMapCTU;
         regEnc->picParams.qpDeltaMapSize = regEnc->totalCUs;
      } else {
         /* H264 block size is 16x16 */
         regEnc->picParams.qpDeltaMap = regEnc->qpDeltaMap;
         regEnc->picParams.qpDeltaMapSize = regEnc->totalMBs;
      }
   }
   regEnc->picParams.outputBitstream = regEnc->pBSBuffer;
   regEnc->picParams.completionEvent = NULL;
   regEnc->picParams.pictureStruct = NV_ENC_PIC_STRUCT_FRAME;
   regEnc->picParams.encodePicFlags = 0;
   regEnc->picParams.inputTimeStamp = 0;
   regEnc->picParams.inputDuration = 0;

exit:
   return error;
}


/*
 *----------------------------------------------------------------------------
 *
 * VNCEncodeRegionNvEncCloseHandles --
 *
 *      Unregisters all input resources, destroys all textures, and
 *      empties the list of shared handles.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

static void
VNCEncodeRegionNvEncCloseHandles(VNCRegionEncoderNvEnc *regEnc)
{
   NVENCSTATUS status;
   int i;

   if (regEnc == NULL) {
      return;
   }

#ifdef __linux__
   /*
    * If the glCtx hasn't been created, due to NvFBC capture shutting down,
    * then there shouldn't be any valid input surfaces, so ASSERT if we find
    * surfaces without any glCtx.
    */
   if (regEnc->glCtx == NULL) {
      for (i = 0; i < ARRAYSIZE(regEnc->inputRegHandle); i++) {
         ASSERT(regEnc->inputRegHandle[i] == NULL);
      }

      return;
   }
#endif

   for (i = 0; i < ARRAYSIZE(regEnc->inputRegHandle); i++) {
      if (regEnc->inputRegHandle[i]) {
         status =
            regEnc->encodeFns.nvEncUnregisterResource(regEnc->hNVEnc, regEnc->inputRegHandle[i]);
         if (NV_ENC_FAILED(status)) {
            REGENCWARN("%s: nvEncUnregisterResource failed with error %d", __FUNCTION__, status);
         }
         regEnc->inputRegHandle[i] = NULL;
      }
#ifdef _WIN32
      if (regEnc->pD3d11Texture[i]) {
         ID3D11Texture2D_Release(regEnc->pD3d11Texture[i]);
         regEnc->pD3d11Texture[i] = NULL;
      }
      if (regEnc->pD3d11TextureKeyedMutex[i]) {
         IDXGIKeyedMutex_Release(regEnc->pD3d11TextureKeyedMutex[i]);
         regEnc->pD3d11TextureKeyedMutex[i] = NULL;
      }
      if (regEnc->pD3d11TextureMappable[i]) {
         ID3D11Texture2D_Release(regEnc->pD3d11TextureMappable[i]);
         regEnc->pD3d11TextureMappable[i] = NULL;
      }
      ASSERT_ON_COMPILE(ARRAYSIZE(regEnc->inputRegHandle) == ARRAYSIZE(regEnc->pD3d11Texture));
      ASSERT_ON_COMPILE(ARRAYSIZE(regEnc->inputRegHandle) ==
                        ARRAYSIZE(regEnc->pD3d11TextureKeyedMutex));
      ASSERT_ON_COMPILE(ARRAYSIZE(regEnc->inputRegHandle) ==
                        ARRAYSIZE(regEnc->pD3d11TextureMappable));
#endif
      regEnc->pSharedHandle[i] = NULL;
      ASSERT_ON_COMPILE(ARRAYSIZE(regEnc->inputRegHandle) == ARRAYSIZE(regEnc->pSharedHandle));
   }
}


/*
 *----------------------------------------------------------------------------
 *
 * VNCEncodeRegionNvEncSetMaxQP --
 *
 *      Calculate and set maxQP based on frame byteBudget and dirty region size.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      Expands the encode buffer, might allocate memory.
 *
 *----------------------------------------------------------------------------
 */

static void
VNCEncodeRegionNvEncSetMaxQP(VNCRegionEncoderNvEnc *regEnc, // IN
                             int byteBudget)                // IN
{
   if (regEnc == NULL) {
      return;
   }
   /*
    * Use a QP estimator model to define maxQP based on the bytebudget
    * and dirty region size of the current frame. Don't use this estimator
    * without a minimum cap because setting this maxQP very lower will also
    * trigger any other lower quality MBs on the frame to be updated
    * all at once which can potentially generate a frame that exceeds the
    * planned bytebudget.
    *
    * Empirical model:
    *    Byte cost of an MB - Measured using a bitstream analyser.
    *      H264Logo TextMB
    * QP36      125     94
    * QP28      200    150
    * QP24      238    188
    * QP18      250    250
    * QP 8      450    375
    *
    * Approximated Formula:
    * MBbyteCost = -10*QP + 450
    * Approximate frame cost
    * FrameByteCost = dirtyMBs*(-10*QP + 450)
    * QP = (FrameByteCost/dirtyMBs)/-10 + 45
    */

   // Careful mixing signed with unsigned to avoid casting errors.
   int bytesPerMB = byteBudget / (regEnc->lerpDirtyMBs + 1);
   int scale = regEnc->useAV1 ? AV1_QP_SCALE : 1;
   int offset = 45 * scale;
   int divider = -10 * scale;
   uint8 estimatedQP = HZN_CLAMP(offset + (bytesPerMB / divider), regEnc->minQP, regEnc->maxQP);

   if (regEnc->outAvgQP - regEnc->minQP < 10) {
      regEnc->customMaxQP = MAX(regEnc->minQP, estimatedQP);
   } else {
      regEnc->customMaxQP = MAX(24, estimatedQP);
   }

   REGENC_RLOG(9,
               "%s - estimatedQP: %u customMaxQP: %d "
               "bytesPerMB: %d byteBudget: %8d dirtyMB: %d",
               __FUNCTION__, estimatedQP, regEnc->customMaxQP, bytesPerMB, byteBudget,
               regEnc->dirtyMBs);

   regEnc->rcParamsNew.maxQP.qpInterP = regEnc->customMaxQP;
   regEnc->rcParamsNew.maxQP.qpInterB = regEnc->customMaxQP;
   regEnc->rcParamsNew.maxQP.qpIntra = regEnc->customMaxQP;
}


/*
 *----------------------------------------------------------------------------
 *
 * VNCEncodeRegionNvEncFindFreeHandleSlot --
 *
 *      Find the first free slot in the shared handle array.
 *
 * Results:
 *      The slot index, or -1 if there is no free slot.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

static int
VNCEncodeRegionNvEncFindFreeHandleSlot(VNCRegionEncoderNvEnc *regEnc) // IN
{
   int texIdx;
#ifdef _WIN32
   if (regEnc->useUnifiedD3DDevice) {
      for (texIdx = 0; texIdx < ARRAYSIZE(regEnc->pD3d11Texture); ++texIdx) {
         if (regEnc->pD3d11Texture[texIdx] == NULL) {
            return texIdx;
         }
      }

      return -1;
   }
#endif

   for (texIdx = 0; texIdx < ARRAYSIZE(regEnc->pSharedHandle); ++texIdx) {
      if (regEnc->pSharedHandle[texIdx] == NULL) {
         return texIdx;
      }
   }

   return -1;
}


/*
 *----------------------------------------------------------------------------
 *
 * VNCEncodeRegionNvEncFindSharedHandle --
 *
 *      Match the given handle in the shared handle array.
 *
 * Results:
 *      The slot index, or -1 if not found.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

static int
VNCEncodeRegionNvEncFindSharedHandle(VNCRegionEncoderNvEnc *regEnc,         // IN
                                     const VNCRegEncFrameState *frameState) // IN
{
   int texIdx;
#ifdef _WIN32
   if (regEnc->useUnifiedD3DDevice) {
      for (texIdx = 0; texIdx < ARRAYSIZE(regEnc->pD3d11Texture); ++texIdx) {
         if (regEnc->pD3d11Texture[texIdx] == frameState->texture) {
            return texIdx;
         }
      }

      return -1;
   }
#endif

   for (texIdx = 0; texIdx < ARRAYSIZE(regEnc->pSharedHandle); ++texIdx) {
      if (regEnc->pSharedHandle[texIdx] == frameState->sharedHandle) {
         return texIdx;
      }
   }

   return -1;
}


/*
 *----------------------------------------------------------------------------
 *
 * VNCEncodeRegionNvEncUpdateFrequencyMap --
 *
 *      Updates the frequency map and hotmask.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

static void
VNCEncodeRegionNvEncUpdateFrequencyMap(VNCRegionEncoderNvEnc *regEnc,         // IN/OUT
                                       const VNCRegEncFrameState *frameState) // IN
{
   if (regEnc->nvencHotRegionMinFPS <= 0 || regEnc->nvencHotRegionDeltaQP <= 0) {
      return;
   }

   /* Update Frequency Map */
   if (frameState->freqMap) {
      VNCFreqMap_Copy(regEnc->freqMap, frameState->freqMap);
   } else {
      VNCFreqMap_NoteTime(regEnc->freqMap, frameState->pumpTime);
      if (regEnc->dirtyMBs > 0) {
         VNCFreqMap_NoteDirty(regEnc->freqMap, regEnc->dirtyMask);
      }
   }

   VNCFreqMap_GetBitmaskAboveFPS(regEnc->freqMap, regEnc->nvencHotRegionMinFPS, regEnc->hotMask);
   regEnc->maxDirtyFPS = VNCFreqMap_GetMaxFPS(regEnc->freqMap);

   // VNCBitmask_Debug(regEnc->hotMask, "freqMap");
}


/*
 *----------------------------------------------------------------------------
 *
 * VNCEncodeRegionNvEncPerformEncode --
 *
 *      Encode the given adapter's screen contents as an H.264 frame using the
 *      NvEncode API. Also maybe update the bandwidth and quality levels.
 *
 * Results:
 *      Returns VNCError code.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

static VNCError
VNCEncodeRegionNvEncPerformEncode(VNCRegionEncoderNvEnc *regEnc,         // IN
                                  VNCEncodeBuf *buf,                     // IN/OUT
                                  int texIdx,                            // IN
                                  const VNCRegEncFrameState *frameState) // IN
{
#if TIMESTAMP_ENABLE
   VNCRegEncProfiler prof = {0};
#endif
   if (regEnc == NULL) {
      return VNC_ERROR_GENERIC;
   }

   HANDLE enc = regEnc->hNVEnc;
   NV_ENC_MAP_INPUT_RESOURCE mapParam;
   NV_ENC_LOCK_BITSTREAM lockBitstreamData = {0};
   NVENCSTATUS status;
   VNCError error = VNC_SUCCESS;
   double now = VNCUtil_SystemTime();
   unsigned char *outputBuffer = NULL;
   int frameSize;

   regEnc->finalWipeFrame = FALSE;
   regEnc->dirtyMBs = VNCBitmask_PopCount(regEnc->dirtyMask);
   regEnc->lerpDirtyMBs = LERP(0.5, regEnc->lerpDirtyMBs, regEnc->dirtyMBs);
   if (regEnc->dirtyMBs > 0) {
      regEnc->isFrameMaxQual = FALSE;
   }
   REGENC_RLOG(9, "dirtyMBs: %5d lerpDirtyMBs: %5d", regEnc->dirtyMBs, regEnc->lerpDirtyMBs);

   /* Remove new dirtyMBs from MBs previously marked at mid-level quality*/
   VNCBitmask_Subtract(regEnc->midQualMask, regEnc->dirtyMask);

   VNCEncodeRegionNvEncUpdateFrequencyMap(regEnc, frameState);

   if (regEnc->useCustomRateControl) {
      if (!regEnc->firstTime) {
         /* Update the boost mask, using the latest dirty mask and age map. */
         VNCEncodeRegionNvEncUpdateMaps(regEnc);

         /* Estimate maxQP based on dirty region size and frame byte budget */
         VNCEncodeRegionNvEncSetMaxQP(regEnc, frameState->byteBudget);

         /* Update the QP deltas using this information. */
         VNCEncodeRegionNvEncUpdateQPMap(regEnc, frameState->byteBudget, now);

         /* Apply the update rate control parameters for the next encoded frame. */
         if (!VNCEncodeRegionNvEncUpdateRC(regEnc)) {
            error = VNC_ERROR_HW_ENCODE_FAILED;
            goto end;
         }
         NOTE_TIME(&prof);
      } else {
         regEnc->dirtyMBs = regEnc->totalMBs;
      }

      if (regEnc->dirtyMBs + regEnc->midQualMBs + regEnc->refreshMBs == 0 &&
          regEnc->finalWipeFrame != TRUE) {
         regEnc->consecutiveEncodeSkips++;
         REGENC_RLOG(6,
                     "No dirty, mid-quality or refresh blocks, "
                     "skipping encode count: %d",
                     regEnc->consecutiveEncodeSkips);

         error = VNC_ERROR_RETRY_ENCODE;
         goto end;
      }
   }
   REGENC_RLOG(6, "dirtyMBs: %5d midQualMBs: %5d refreshMBs: %5d", regEnc->dirtyMBs,
               regEnc->midQualMBs, regEnc->refreshMBs);
   REGENC_RLOG(6, "Setting maxQP as: %u", regEnc->customMaxQP);


#ifdef _WIN32
   /* Acquire the keyed mutex before using the input texture */
   if (!VNCEncodeRegionNvEncAcquireSync(regEnc, texIdx)) {
      error = VNC_ERROR_HW_ENCODE_FAILED;
      goto end;
   }
#endif
   NOTE_TIME(&prof);

   do {
      /* Setup input resource map for the encoder. */
      memset(&mapParam, 0, sizeof(mapParam));
      mapParam.version = NV_ENC_MAP_INPUT_RESOURCE_VER;
      mapParam.registeredResource = regEnc->inputRegHandle[texIdx];
      status = regEnc->encodeFns.nvEncMapInputResource(enc, &mapParam);
      NOTE_TIME(&prof);
      if (status != NV_ENC_SUCCESS) {
         REGENCWARN("%s: Failed to map resource with encoder (%d / 0x%x)", __FUNCTION__, status,
                    status);
         error = VNC_ERROR_HW_ENCODE_FAILED;
         break;
      }

      regEnc->picParams.inputBuffer = mapParam.mappedResource;
      regEnc->picParams.bufferFmt = mapParam.mappedBufferFmt;

      /* Now we can encode the grabbed frame! */
      status = regEnc->encodeFns.nvEncEncodePicture(enc, &regEnc->picParams);
      NOTE_TIME(&prof);
      if (status != NV_ENC_SUCCESS) {
         REGENCWARN("%s: encoder picture encode failed (error %d / 0x%x)", __FUNCTION__, status,
                    status);

         status = regEnc->encodeFns.nvEncUnmapInputResource(enc, mapParam.mappedResource);
         if (status != NV_ENC_SUCCESS) {
            REGENCWARN("%s: encoder input resource unmap failed (%d / 0x%x)", __FUNCTION__, status,
                       status);
         }
         error = VNC_ERROR_HW_ENCODE_FAILED;
         break;
      }

      /* Lock the bitstream data so we can copy it. */
      lockBitstreamData.version = NV_ENC_LOCK_BITSTREAM_VER;
      lockBitstreamData.outputBitstream = regEnc->pBSBuffer;
      lockBitstreamData.doNotWait = FALSE;

      status = regEnc->encodeFns.nvEncLockBitstream(enc, &lockBitstreamData);
      NOTE_TIME(&prof);
      if (status != NV_ENC_SUCCESS) {
         REGENCWARN("%s: encoder bitstream lock failed (%d / 0x%x)", __FUNCTION__, status, status);
         error = VNC_ERROR_HW_ENCODE_FAILED;
         break;
      }
      regEnc->outAvgQP = lockBitstreamData.frameAvgQP;
      regEnc->frameSatd = lockBitstreamData.frameSatd;

      /* Copy the NVIDIA bitstream to a local buffer so that we unlock it */
      frameSize = lockBitstreamData.bitstreamSizeInBytes;
      outputBuffer = Util_SafeMalloc(frameSize);
      memcpy(outputBuffer, lockBitstreamData.bitstreamBufferPtr, frameSize);

      regEnc->encFrameNum++;

      /* Finally, unlock the bitstream data. */
      status = regEnc->encodeFns.nvEncUnlockBitstream(enc, regEnc->pBSBuffer);
      NOTE_TIME(&prof);
      if (status != NV_ENC_SUCCESS) {
         REGENCWARN("%s: encoder bitstream unlock failed (%d / 0x%x)", __FUNCTION__, status,
                    status);
         error = VNC_ERROR_HW_ENCODE_FAILED;
         break;
      }

      /* Unmap the resource now that the bitstream has been unlocked. */
      status = regEnc->encodeFns.nvEncUnmapInputResource(enc, mapParam.mappedResource);
      NOTE_TIME(&prof);
      if (status != NV_ENC_SUCCESS) {
         REGENCWARN("%s: encoder input resource unmap failed (%d / 0x%x)", __FUNCTION__, status,
                    status);
         error = VNC_ERROR_HW_ENCODE_FAILED;
         break;
      }
   } while (0);

   WIN32_ONLY(VNCEncodeRegionNvEncReleaseSync(regEnc, texIdx));
   NOTE_TIME(&prof);
   if (error != VNC_SUCCESS) {
      goto end;
   }

   /*
    * End condition: frame average QP reported by NvEnc is equal to minQP.
    */
   regEnc->lastMaxQP = regEnc->customMaxQP;
   regEnc->isFrameMaxQual =
      (regEnc->customMaxQP <= regEnc->minQP && regEnc->outAvgQP <= regEnc->minQP);
   if (regEnc->isFrameMaxQual && regEnc->useCustomRateControl) {
      /*
       * If maxQP = minQP, then qpDeltaMap should be all set to 0.
       */
      if (0 && vmx86_devel && regEnc->qpDeltaMap) {
         int i;
         for (i = 0; i < regEnc->totalMBs; i++) {
            if (regEnc->qpDeltaMap[i] != 0) {
               REGENCWARN("Found non-zero qpDeltaMap at position: %d qp: %d", i,
                          regEnc->qpDeltaMap[i]);

               /*
                * VNCEncodeRegionPrintMatrix expects uint but qpDeltaMap
                * has signed integers. Read any value above 128 as negative
                * until we find a way to print signed and unsigned values
                * with fix width so that we can visualize the numbers aligned.
                */
               VNCEncodeRegionPrintMatrix("qpDeltaMap", // Print QPMap
                                          regEnc->qpDeltaMap, regEnc->mbWidth, regEnc->mbHeight);
            }
         }
      }

      REGENC_RLOG(5, "Max quality achieved, going idle. Avg. QP: %2d", regEnc->outAvgQP);
   }

   if (0 && regEnc->qpDeltaMap) {
      VNCEncodeRegionPrintMatrix("qpDeltaMap", regEnc->qpDeltaMap, regEnc->mbWidth,
                                 regEnc->mbHeight);
   }

   REGENC_RLOG(2,
               "Screen: %d Frame: %4d frameId: %d size: %7d budget: %7d Used: %4d%% "
               "dirtyMBs: %6d midQualMBs: %6d refreshMBs: %6d avgQP: %2u "
               "maxQP: %2u SATD: %8u EncodeTime: %3dms",
               regEnc->base.config.screenNum, regEnc->encFrameNum, frameState->frameId, frameSize,
               frameState->byteBudget, PERCENT(frameSize, frameState->byteBudget), regEnc->dirtyMBs,
               regEnc->midQualMBs, regEnc->refreshMBs, regEnc->outAvgQP, regEnc->customMaxQP,
               regEnc->frameSatd, (int)(1000 * (VNCUtil_SystemTime() - now)));

   if (regEnc->base.config.dynamicConfig.vncEncodeLogLevel >= 5) {
      PRINT_TIMES(&prof, NULL);
   }

   /* Package the NAL into a VNC message and put it on the wire. */
   VNCEncodeRegionNvEncSendNals(regEnc, buf, outputBuffer, 1, frameSize);

   /* Assert that no frames were forgotten or sent twice. */
   regEnc->sendFrameNum++;
   ASSERT(regEnc->sendFrameNum == regEnc->encFrameNum);

   if (vmx86_debug) {
      /* Dump H264 compressed bitstream if enabled */
      VNCEncodeRegionNvEncDumpBitstream(regEnc, outputBuffer, frameSize);
      /* Dump uncompressed screen contents if enabled */
      VNCEncodeRegionNvEncDumpARGB(regEnc, texIdx);
   }

   regEnc->consecutiveEncodeSkips = 0;

end:
   free(outputBuffer);

   return error;
}


/*
 *----------------------------------------------------------------------------
 *
 * VNCEncodeRegionNvEnc_Encode --
 *
 *      Sends any new H.264 frame(s) which were encoded in hardware using the
 *      NvFBC and NvEncode APIs.
 *
 * Results:
 *      VNC_SUCCESS or VNC_ERROR_*.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

static VNCError
VNCEncodeRegionNvEnc_Encode(VNCRegionEncoder *base,                // IN/OUT
                            const VNCRegEncFrameState *frameState, // IN
                            VNCEncodeBuf *buf)                     // IN/OUT
{
   VNCRegionEncoderNvEnc *regEnc = (VNCRegionEncoderNvEnc *)base;
   int texIdx = 0;
   VNCError error = VNC_SUCCESS;

   if (regEnc == NULL) {
      return VNC_ERROR_GENERIC;
   }

   regEnc->base.encodeState = REGENC_FB_DIRTY;

   VNCBitmask_Union(regEnc->dirtyMask, frameState->dirtyMask);

   /*
    * Update the NVIDIA NVENC HW Encoder bitrate every second
    * after the first 5 seconds of the connection
    */
   if (regEnc->lastBWupdate + 1 < frameState->pumpTime ||
       regEnc->base.config.startTime + 5 > frameState->pumpTime) {
      VNCEncodeRegionNvEncSetBandwidth(regEnc, frameState->bandwidth, &regEnc->rcParamsNew);
      regEnc->lastBWupdate = frameState->pumpTime;
   }

   /* Create NVENC encoder if it has not yet been done. */
   if (regEnc->hNVEnc == NULL) {
#ifdef _WIN32
      if (regEnc->base.config.staticConfig.allowCaptureEncodeUnifiedD3DDevice &&
          frameState->supportCaptureEncodeUnifiedD3DDevice &&
          regEnc->base.config.connectionType == VNC_CONNECTION_TYPE_PRIMARY) {
         /*
          * For primary sessions, it is optimal to use unified D3D device since
          * the synchronization of access to the shared texture can be removed.
          */
         regEnc->useUnifiedD3DDevice = TRUE;
      }
#else
      regEnc->useUnifiedD3DDevice = FALSE;
#endif

      /*
       * Dump GPU memory info before encoder creation.
       */
      VNCEncodeRegionNvEncGPUMemoryInfo(regEnc->base.config.screenNum, "BeforeEncoderCreation");

      error = VNCEncodeRegionNvEncCreate(regEnc, frameState);
      if (error != VNC_SUCCESS) {
         REGENCWARN("%s: Failed to create Nvidia encoder", __FUNCTION__);
         goto end;
      }

      /*
       * Dump GPU memory info after encoder creation.
       */
      VNCEncodeRegionNvEncGPUMemoryInfo(regEnc->base.config.screenNum, "AfterEncoderCreation");
   }

#ifdef __linux__
   /*
    * We need to make the GL context current to this thread
    * as there's no guarantees the async thread is the same
    * one each time.
    *
    * Further down, we unbind the context.
    */
   if (!VNCServerLinux_MakeGLCurrent(regEnc->glCtx, TRUE)) {
      error = VNC_ERROR_HW_ENCODE_FAILED;
      goto end;
   }
#endif

   /* Find index of texture associated with shared handle - maybe create it. */
   texIdx = VNCEncodeRegionNvEncFindSharedHandle(regEnc, frameState);
   if (texIdx < 0 && !VNCEncodeRegionNvEncRegisterTexture(regEnc, frameState, &texIdx)) {
      REGENCWARN("%s: Failed to register texture", __FUNCTION__);
      error = VNC_ERROR_HW_ENCODE_FAILED;
      goto end;
   }

   /* Ensure GPU is not too loaded to pursue NvENC session. */
   if (regEnc->capacityCheckPeriodMs > 0 &&
       frameState->pumpTime - regEnc->lastQueryTime > regEnc->capacityCheckPeriodMs / 1000.0) {
      regEnc->lastQueryTime = frameState->pumpTime;
      if (nvidiasdk.dllHandleNvml && base->config.dynamicConfig.allowNvidiaCapacityCheck &&
          !VNCEncodeRegionNvEncDoesGPUHaveCapacity(regEnc)) {
         REGENCWARN("%s: Insufficient encode capacity", __FUNCTION__);
         error = VNC_ERROR_HW_ENCODE_FAILED;
         goto end;
      }
   }

#ifdef _WIN32
   ENTER_CRITICAL_SECTION(regEnc)
#endif

   /* Perform a synchronous encode. */
   error = VNCEncodeRegionNvEncPerformEncode(regEnc, buf, texIdx, frameState);
   if (error != VNC_SUCCESS) {
      if (error != VNC_ERROR_RETRY_ENCODE) {
         REGENCWARN("%s: VNCEncodeRegionNvEncPerformEncode failed", __FUNCTION__);
      }
      goto end;
   }

   VNCBitmask_Clear(regEnc->dirtyMask);

end:
#ifdef __linux__
   VNCServerLinux_MakeGLCurrent(regEnc->glCtx, FALSE);
#endif
   if (error == VNC_ERROR_RETRY_ENCODE || error == VNC_SUCCESS) {
      if (regEnc->isFrameMaxQual) {
         regEnc->base.encodeState = REGENC_IDLE;
      } else {
         regEnc->base.encodeState = REGENC_FB_BOOSTING;
         regEnc->base.nextBoostTime = 0;
      }
   }

#ifdef _WIN32
   LEAVE_CRITICAL_SECTION(regEnc)
#endif

   return error;
}


/*
 *----------------------------------------------------------------------------
 *
 * VNCEncodeRegionNvEncDestroyEncoder --
 *
 *      Destroy the region encoder's NvENC object and associated resources for
 *      the given region.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None
 *
 *----------------------------------------------------------------------------
 */

static void
VNCEncodeRegionNvEncDestroyEncoder(VNCRegionEncoderNvEnc *regEnc) // IN
{
   NVENCSTATUS status;

   if (regEnc == NULL) {
      return;
   }

#ifdef __linux__
   if (regEnc->glCtx && !VNCServerLinux_MakeGLCurrent(regEnc->glCtx, TRUE)) {
      REGENCWARN("%s: Failed to make GL context %p current for "
                 "resource destruction",
                 __FUNCTION__, regEnc->glCtx);
   }
#endif

   VNCEncodeRegionNvEncCloseHandles(regEnc);

   if (regEnc->pBSBuffer) {
      status = regEnc->encodeFns.nvEncDestroyBitstreamBuffer(regEnc->hNVEnc, regEnc->pBSBuffer);
      if (NV_ENC_FAILED(status)) {
         REGENCWARN("%s - Failed to destroy the bitstream buffer. Error: %d", __FUNCTION__, status);
      }
      regEnc->pBSBuffer = NULL;
   }

   if (regEnc->hNVEnc) {
      status = regEnc->encodeFns.nvEncDestroyEncoder(regEnc->hNVEnc);
      if (NV_ENC_FAILED(status)) {
         REGENCWARN("%s - Failed to destroy the NVIDIA HW encoder. Error: %d", __FUNCTION__,
                    status);
      }
      regEnc->hNVEnc = NULL;
   }

   if (regEnc->qpDeltaMap) {
      free(regEnc->qpDeltaMap);
      regEnc->qpDeltaMap = NULL;
   }

   if (regEnc->qpMap) {
      free(regEnc->qpMap);
      regEnc->qpMap = NULL;
   }

   if (regEnc->qpDeltaMapCTU) {
      free(regEnc->qpDeltaMapCTU);
      regEnc->qpDeltaMapCTU = NULL;
   }

#ifdef __linux__
   VNCServerLinux_MakeGLCurrent(regEnc->glCtx, FALSE);
#endif

   VNCEncodeRegionNvEncDestroyOS(regEnc);
}


/*
 *----------------------------------------------------------------------------
 *
 * VNCEncodeRegionNvEnc_Destroy --
 *
 *      Destroy the encoder.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      Frees memory.
 *
 *----------------------------------------------------------------------------
 */

static void
VNCEncodeRegionNvEnc_Destroy(VNCRegionEncoder *base) // IN
{
   VNCRegionEncoderNvEnc *regEnc = (VNCRegionEncoderNvEnc *)base;

   if (regEnc != NULL) {
      REGENC_RLOG(5, "Final frame avg. QP: %d", regEnc->outAvgQP);

      VNCEncodeRegionNvEncDestroyEncoder(regEnc);

      /* will lose any delayed frames */
      VNCEncodeRegionCloseFile(&regEnc->fhandleBitstream);

      /* will lose any delayed frames */
      VNCEncodeRegionCloseFile(&regEnc->fhandleRaw);

      if (!regEnc->encodeAsH264) {
         VNCEncodeStopMP4DebugOutput();
      }

      VNCBitmask_Destroy(regEnc->maxQualMask);
      regEnc->maxQualMask = NULL;

      VNCBitmask_Destroy(regEnc->midQualMask);
      regEnc->midQualMask = NULL;

      VNCBitmask_Destroy(regEnc->toBoostMask);
      regEnc->toBoostMask = NULL;

      VNCBitmask_Destroy(regEnc->dirtyMask);
      regEnc->dirtyMask = NULL;

      VNCAgeMap_Destroy(regEnc->ageMap);
      regEnc->ageMap = NULL;

      VNCBitmask_Destroy(regEnc->hotMask);
      regEnc->hotMask = NULL;

      if (regEnc->freqMap) {
         VNCFreqMap_Destroy(regEnc->freqMap);
         regEnc->freqMap = NULL;
      }

      free(regEnc);
   }
   VNCEncodeRegionNvEncUnloadLibrary();
}


/*
 *----------------------------------------------------------------------------
 *
 * VNCEncodeRegionNvEnc_Create --
 *
 *      Create the harwdare encoder.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      Allocates memory.
 *
 *----------------------------------------------------------------------------
 */

VNCRegionEncoder *
VNCENCODEREGION(NVENC_SDK_SUFFIX, Create)(const VNCRegEncConfig *regEncConfig, // IN
                                          VNCEncodeCodecType codecType)        // IN
{
   Bool useAV1 = codecType == VNCENCODE_CODEC_TYPE_AV1;
   Bool useHEVC = codecType == VNCENCODE_CODEC_TYPE_HEVC;
   const VNCEncodeDynamicConfig *config = &regEncConfig->dynamicConfig;
   uint32 encodeAsH264 = BitVector_Test(regEncConfig->caps, VNCH264RectCap);
   uint32 encodeAsMP4 = BitVector_Test(regEncConfig->caps, VNCH264MP4RectCap) && config->allowMP4;
   struct VNCRegionEncoderNvEnc *regEnc = NULL;
   uint32 width = Rect_Width(&regEncConfig->region.rect);
   uint32 height = Rect_Height(&regEncConfig->region.rect);

   /* First, try to load the shared libraries; abort immediately if this fails. */
   if (!VNCEncodeRegionNvEncLoadLibrary()) {
      return NULL;
   }

   /* Abort if the GPU encoder utilization is too high. */
   if (nvidiasdk.dllHandleNvml && config->allowNvidiaCapacityCheck &&
       !VNCEncodeRegionNvEncDoesGPUHaveCapacity(regEnc)) {
      REGENCWARN("Insufficient encoder capacity remaining on the GPU, "
                 "falling back to software encoder.");
      goto fail;
   } else {
      VNCEncodeRegionNvEncGPUMemoryInfo(regEncConfig->screenNum, "AtEncoderInitialization");
   }

   /*
    * Abort if:
    * - width or height is odd, and we did not explicitly enable support of
    *   odd resolutions.
    * - the resolution is 0 x 0.
    */
   if ((!config->allowOddDimensionsH264 && (width & 1 || height & 1)) || width == 0 ||
       height == 0) {
      REGENCWARN("Unsupported resolution %dx%d.", width, height);
      goto fail;
   }

   /*
    * Check for max supported resolution
    */
   if (useAV1) {
      if (width > sCaps.av1MaxWidth || height > sCaps.av1MaxHeight) {
         REGENCWARN("Frame size (%dx%d pixels) > AV1 max (%dx%d pixels)", width, height,
                    sCaps.av1MaxWidth, sCaps.av1MaxHeight);
         goto fail;
      }
   } else if (useHEVC) {
      if (width > sCaps.hevcMaxWidth || height > sCaps.hevcMaxHeight) {
         REGENCWARN("Frame size (%dx%d pixels) > HEVC max (%dx%d pixels)", width, height,
                    sCaps.hevcMaxWidth, sCaps.hevcMaxHeight);
         goto fail;
      }
   } else {
      if (width > sCaps.h264MaxWidth || height > sCaps.h264MaxHeight) {
         REGENCWARN("Frame size (%dx%d pixels) > H264 max (%dx%d pixels)", width, height,
                    sCaps.h264MaxWidth, sCaps.h264MaxHeight);
         goto fail;
      }
   }

   if (!encodeAsH264 && !encodeAsMP4) {
      REGENCWARN("H.264 decoding cap unsupported by the client.");
      goto fail;
   }

   regEnc = Util_SafeCalloc(1, sizeof *regEnc);

   REGENC_RLOG(9, "Attempting to create NvEnc RegEnc with %s:%s:%s",
               useAV1    ? "AV1"
               : useHEVC ? "HEVC"
                         : "H264",
               VNCEncodeRegionNvEncIsYUV444Requested(regEnc) ? "444" : "420",
               (regEncConfig->region.hdrEnabled && sCaps.hevcSupports10Bit) ? "HDR" : "");

   VNCRegionEncoderInit(&regEnc->base, regEncConfig, VNC_SERVEROS_CAPTURE_TYPE_HW,
                        regEncConfig->region.hdrEnabled ? VNC_SERVEROS_PIXEL_FORMAT_10BIT_BGRX
                                                        : VNC_SERVEROS_PIXEL_FORMAT_8BIT_BGRX);

   regEnc->useHEVC = useHEVC;
   regEnc->useAV1 = useAV1;
   regEnc->useYUV444 = VNCEncodeRegionNvEncIsYUV444Requested(regEnc) &&
                       VNCEncodeRegionNvEncIsYUV444Supported(regEnc);

   if (useAV1) {
      if (regEncConfig->region.hdrEnabled && sCaps.av1Supports10Bit) {
         regEnc->base.type = regEnc->useYUV444 ? VNC_REGION_ENCODER_NVENC_HW_AV1_HDR_444
                                               : VNC_REGION_ENCODER_NVENC_HW_AV1_HDR_420;
      } else {
         regEnc->base.type = regEnc->useYUV444 ? VNC_REGION_ENCODER_NVENC_HW_AV1_444
                                               : VNC_REGION_ENCODER_NVENC_HW_AV1_420;
      }
   } else if (useHEVC) {
      if (regEncConfig->region.hdrEnabled && sCaps.hevcSupports10Bit) {
         regEnc->base.type = regEnc->useYUV444 ? VNC_REGION_ENCODER_NVENC_HW_HEVC_HDR_444
                                               : VNC_REGION_ENCODER_NVENC_HW_HEVC_HDR_420;
      } else {
         regEnc->base.type = regEnc->useYUV444 ? VNC_REGION_ENCODER_NVENC_HW_HEVC_444
                                               : VNC_REGION_ENCODER_NVENC_HW_HEVC_420;
      }
   } else {
      regEnc->base.type = regEnc->useYUV444 ? VNC_REGION_ENCODER_NVENC_HW_H264_444
                                            : VNC_REGION_ENCODER_NVENC_HW_H264_420;
   }

   regEnc->base.allowMultipleInflightFrames = TRUE;
   regEnc->base.needsDeflateCMFHeaders = FALSE;
   regEnc->base.encode = VNCEncodeRegionNvEnc_Encode;
   regEnc->base.destroy = VNCEncodeRegionNvEnc_Destroy;

   regEnc->frameNumber = 0;
   regEnc->encodeAsH264 = encodeAsH264;

   regEnc->useCustomRateControl = config->enableNvidiaH264CustomRC;
   REGENC_RLOG(9, "%s custom RC for hardware encoder",
               regEnc->useCustomRateControl ? "Using" : "NOT using");

   regEnc->nvencBwTargetPercent = HZN_CLAMP(config->nvencBwTargetPercent, 25, 100);
   if (regEnc->nvencBwTargetPercent != 100) {
      REGENCLG0("Bandwidth Target is: %d%%", regEnc->nvencBwTargetPercent);
   }

   regEnc->nvencHotRegionMinFPS = HZN_CLAMP(config->nvencHotRegionMinFPS, 0, 60);
   regEnc->midBoostQPDelta = config->nvidiaMidBoostQPDelta;
   regEnc->maxQP = config->qpmaxH264;
   regEnc->minQP = HZN_CLAMP(config->qpminH264, CONFIG_H264_QP_MIN, regEnc->maxQP);
   regEnc->nvencHotRegionDeltaQP = HZN_CLAMP(config->nvencHotRegionDeltaQP, 0, CONFIG_H264_QP_MAX);
   regEnc->nvencNonInteractiveMinQP =
      HZN_CLAMP(config->nvencNonInteractiveMinQP, regEnc->minQP, regEnc->maxQP);
   if (regEnc->useAV1) {
      regEnc->maxQP *= AV1_QP_SCALE;
      regEnc->minQP *= AV1_QP_SCALE;
      regEnc->midBoostQPDelta *= AV1_QP_SCALE;
      regEnc->nvencHotRegionDeltaQP *= AV1_QP_SCALE;
      regEnc->nvencNonInteractiveMinQP *= AV1_QP_SCALE;
      REGENC_RLOG(0, "qpmax=%u, qpmin=%u will be scaled for AV1.", config->qpmaxH264,
                  config->qpminH264);
   }
   REGENC_RLOG(0, "qpmax:%u qpmin:%u", regEnc->maxQP, regEnc->minQP);
   regEnc->nvencVbvBufferSizeMSec = config->nvidiaVBVBufferSizeMS;
   regEnc->bwSavingEnabled = config->enableNvidiaBwSaving;

   regEnc->qualBoostDelayMs =
      HZN_CLAMP(config->encQualBoostDelayMs, QUALBOOSTDELAY_MS_MIN, QUALBOOSTDELAY_MS_MAX);
   REGENC_RLOG(9, "Quality boost delay: %d ms", regEnc->qualBoostDelayMs);

   regEnc->mbCostQual = config->mbCostQualH264;
   if (regEnc->mbCostQual < MB_COST_SCALAR_MIN || regEnc->mbCostQual > MB_COST_SCALAR_MAX) {
      regEnc->mbCostQual = MB_COST_SCALAR_QUAL;
   }
   REGENC_RLOG(9, "MB quality cost: %d", regEnc->mbCostQual);

   regEnc->ageMap = VNCAgeMap_Create(width, height);
   ASSERT(regEnc->ageMap != NULL);
   regEnc->dirtyMask = VNCBitmask_Create(width, height, MBWIDTH);
   ASSERT(regEnc->dirtyMask != NULL);
   regEnc->toBoostMask = VNCBitmask_CreateSimilar(regEnc->dirtyMask);
   ASSERT(regEnc->toBoostMask != NULL);
   regEnc->maxQualMask = VNCBitmask_CreateSimilar(regEnc->dirtyMask);
   ASSERT(regEnc->maxQualMask != NULL);
   regEnc->midQualMask = VNCBitmask_CreateSimilar(regEnc->dirtyMask);
   ASSERT(regEnc->midQualMask != NULL);
   regEnc->hotMask = VNCBitmask_CreateSimilar(regEnc->dirtyMask);
   ASSERT(regEnc->hotMask != NULL);
   regEnc->freqMap = VNCFreqMap_Create(width, height, VNCUtil_SystemTime());
   ASSERT(regEnc->freqMap != NULL);
   regEnc->firstTime = TRUE;
   regEnc->width = Rect_Width(&regEncConfig->region.rect);
   regEnc->height = Rect_Height(&regEncConfig->region.rect);
   regEnc->mbWidth = CEILING(Rect_Width(&regEncConfig->region.rect), MBWIDTH);
   regEnc->mbHeight = CEILING(Rect_Height(&regEncConfig->region.rect), MBWIDTH);
   regEnc->lastQualBoostTime = VNCUtil_SystemTime();
   regEnc->capacityCheckPeriodMs = HZN_CLAMP(config->encCapacityCheckWaitPeriodMs, 0, 60000);
   regEnc->customMaxQP = regEnc->maxQP;

   regEnc->useFullRangeAndBT7092020 = config->enableExplicitColorInfo &&
                                      regEncConfig->clientSupportsExplicitColorInfo &&
                                      (regEnc->useYUV444 || config->forceExplicitColorInfo);
   if (regEnc->useFullRangeAndBT7092020) {
      REGENCLG0("Using full range YUV and BT.%d matrix.",
                regEnc->base.config.region.hdrEnabled ? 2020 : 709);
   } else {
      REGENCLG0("Using limited range YUV and BT.%d matrix.",
                regEnc->base.config.region.hdrEnabled ? 2020 : 601);
   }

   /* Dump compressed and/or uncompressed frames if requested. */
   if (config->dumpH264) {
      REGENCWARN("Dumping NvEnc bitstream.");
      char *fname = Str_Asprintf(NULL, FILE_DUMP_PREFIX "Session-%d-%d-%dx%d_%s.%s",
                                 regEncConfig->encoderId, regEncConfig->screenNum, regEnc->width,
                                 regEnc->height, regEnc->useYUV444 ? "I444" : "I420",
                                 useAV1    ? "av1"
                                 : useHEVC ? "h265"
                                           : "h264");
      VNCEncodeRegionOpenFile(&regEnc->fhandleBitstream, fname);
      free(fname);
   }

   if (config->dumpRaw) {
      REGENCWARN("Dumping BGRA uncompressed surface contents.");
      char *fname = Str_Asprintf(NULL, FILE_DUMP_PREFIX "Session-%d-%d-%dx%d_source.bgra",
                                 regEncConfig->encoderId, regEncConfig->screenNum, regEnc->width,
                                 regEnc->height);

      VNCEncodeRegionOpenFile(&regEnc->fhandleRaw, fname);
      free(fname);
   }

   if (!regEnc->encodeAsH264) {
      VNCEncodeStartMP4DebugOutput(regEncConfig->encoderId);
   }
   return &regEnc->base;

fail:
   VNCEncodeRegionNvEnc_Destroy(regEnc ? &regEnc->base : NULL);

   return NULL;
}


/*
 *----------------------------------------------------------------------------
 *
 * NvEncPrintPresetConfig --
 *
 *      Utility function to log codec members for codec/preset/tuning combination.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      Allocates memory.
 *
 *----------------------------------------------------------------------------
 */

static void
NvEncPrintPresetConfig(VNCRegionEncoderNvEnc *regEnc,    // IN
                       void *handle,                     // IN
                       NV_ENCODE_API_FUNCTION_LIST *fns, // IN
                       GUID codecGuid,                   // IN
                       GUID presetGuid,                  // IN
                       const char *presetString,         // IN
                       unsigned int tuning,              // IN
                       const char *tuningString)         // IN
{
   NV_ENC_PRESET_CONFIG presetCfg = {0};
   const GUID h264Guid = NV_ENC_CODEC_H264_GUID;
   const GUID hevcGuid = NV_ENC_CODEC_HEVC_GUID;
   Bool isH264 = memcmp(&codecGuid, &h264Guid, sizeof(h264Guid)) == 0;
   Bool isHEVC = memcmp(&codecGuid, &hevcGuid, sizeof(hevcGuid)) == 0;
   const char *codecString = isH264 ? "H264" : isHEVC ? "HEVC" : "AV1";
   NVENCSTATUS status;

#if !defined(VMX86_DEBUG)
   return;
#endif

   presetCfg.version = NV_ENC_PRESET_CONFIG_VER;
   presetCfg.presetCfg.version = NV_ENC_CONFIG_VER;
#if NVENCAPI_MAJOR_VERSION > 8
   status = fns->nvEncGetEncodePresetConfigEx(handle, codecGuid, presetGuid, tuning, &presetCfg);
#else
   status = fns->nvEncGetEncodePresetConfig(handle, codecGuid, presetGuid, &presetCfg);
#endif
   if (NV_ENC_FAILED(status)) {
      REGENC_RLOG(6, "nvEncGetEncodePresetConfigEx(%s, %s, %s) failed: %d.", codecString,
                  presetString, tuningString, status);
      return;
   }
   REGENC_RLOG(6, "NvEnc: %s: tuning %s: preset %s. preset config ->", codecString, tuningString,
               presetString);
#define LOG_CONFIG(x) REGENC_RLOG(6, "   config.%s = %d", #x, presetCfg.presetCfg.x)
#define LOG_CONFIG_QP(x)                                                                           \
   REGENC_RLOG(6, "   config.%s = QP { interP %u, interB %u, intra %u }", #x,                      \
               presetCfg.presetCfg.x.qpInterP, presetCfg.presetCfg.x.qpInterB,                     \
               presetCfg.presetCfg.x.qpIntra)
   LOG_CONFIG(gopLength);
   LOG_CONFIG(frameIntervalP);
   LOG_CONFIG(mvPrecision);

   LOG_CONFIG(rcParams.rateControlMode);
   LOG_CONFIG(rcParams.averageBitRate);
   LOG_CONFIG(rcParams.maxBitRate);
   LOG_CONFIG(rcParams.vbvBufferSize);
   LOG_CONFIG(rcParams.enableInitialRCQP);
   LOG_CONFIG(rcParams.enableMinQP);
   LOG_CONFIG(rcParams.enableMaxQP);
   LOG_CONFIG(rcParams.enableAQ);
   LOG_CONFIG(rcParams.enableLookahead);
   LOG_CONFIG(rcParams.enableTemporalAQ);
#if NVENCAPI_MAJOR_VERSION > 8
   LOG_CONFIG(rcParams.zeroReorderDelay);
#endif
   LOG_CONFIG(rcParams.enableNonRefP);
   LOG_CONFIG(rcParams.strictGOPTarget);
   LOG_CONFIG(rcParams.aqStrength);
   LOG_CONFIG_QP(rcParams.minQP);
   LOG_CONFIG_QP(rcParams.maxQP);
   LOG_CONFIG_QP(rcParams.initialRCQP);
#if NVENCAPI_MAJOR_VERSION > 8
   LOG_CONFIG(rcParams.lowDelayKeyFrameScale);
   LOG_CONFIG(rcParams.multiPass);
#endif

   if (isH264) {
      LOG_CONFIG(encodeCodecConfig.h264Config.enableConstrainedEncoding);
      LOG_CONFIG(encodeCodecConfig.h264Config.useConstrainedIntraPred);
#if NVENCAPI_MAJOR_VERSION > 8
      LOG_CONFIG(encodeCodecConfig.h264Config.singleSliceIntraRefresh);
#endif
      LOG_CONFIG(encodeCodecConfig.h264Config.enableLTR);
      LOG_CONFIG(encodeCodecConfig.h264Config.idrPeriod);
      LOG_CONFIG(encodeCodecConfig.h264Config.maxNumRefFrames);
      LOG_CONFIG(encodeCodecConfig.h264Config.entropyCodingMode);
      LOG_CONFIG(encodeCodecConfig.h264Config.intraRefreshPeriod);
      LOG_CONFIG(encodeCodecConfig.h264Config.intraRefreshCnt);
      LOG_CONFIG(encodeCodecConfig.h264Config.sliceMode);
      LOG_CONFIG(encodeCodecConfig.h264Config.sliceModeData);
   } else if (isHEVC) {
      LOG_CONFIG(encodeCodecConfig.hevcConfig.level);
      LOG_CONFIG(encodeCodecConfig.hevcConfig.tier);
      LOG_CONFIG(encodeCodecConfig.hevcConfig.minCUSize);
      LOG_CONFIG(encodeCodecConfig.hevcConfig.maxCUSize);
      LOG_CONFIG(encodeCodecConfig.hevcConfig.useConstrainedIntraPred);
      LOG_CONFIG(encodeCodecConfig.hevcConfig.disableDeblockAcrossSliceBoundary);
      LOG_CONFIG(encodeCodecConfig.hevcConfig.enableLTR);
      LOG_CONFIG(encodeCodecConfig.hevcConfig.enableIntraRefresh);
#if NVENCAPI_MAJOR_VERSION > 8
      LOG_CONFIG(encodeCodecConfig.hevcConfig.enableConstrainedEncoding);
#endif
      LOG_CONFIG(encodeCodecConfig.hevcConfig.idrPeriod);
      LOG_CONFIG(encodeCodecConfig.hevcConfig.intraRefreshPeriod);
      LOG_CONFIG(encodeCodecConfig.hevcConfig.intraRefreshCnt);
      LOG_CONFIG(encodeCodecConfig.hevcConfig.maxNumRefFramesInDPB);
      LOG_CONFIG(encodeCodecConfig.hevcConfig.sliceMode);
      LOG_CONFIG(encodeCodecConfig.hevcConfig.sliceModeData);
   } else {
#if NVENCAPI_MAJOR_VERSION > 8
      LOG_CONFIG(encodeCodecConfig.av1Config.level);
      LOG_CONFIG(encodeCodecConfig.av1Config.tier);
      LOG_CONFIG(encodeCodecConfig.av1Config.minPartSize);
      LOG_CONFIG(encodeCodecConfig.av1Config.maxPartSize);
      LOG_CONFIG(encodeCodecConfig.av1Config.outputAnnexBFormat);
      LOG_CONFIG(encodeCodecConfig.av1Config.enableFrameIdNumbers);
      LOG_CONFIG(encodeCodecConfig.av1Config.idrPeriod);
      LOG_CONFIG(encodeCodecConfig.av1Config.intraRefreshPeriod);
      LOG_CONFIG(encodeCodecConfig.av1Config.maxNumRefFramesInDPB);
      LOG_CONFIG(encodeCodecConfig.av1Config.colorPrimaries);
      LOG_CONFIG(encodeCodecConfig.av1Config.transferCharacteristics);
      LOG_CONFIG(encodeCodecConfig.av1Config.matrixCoefficients);
      LOG_CONFIG(encodeCodecConfig.av1Config.colorRange);
      LOG_CONFIG(encodeCodecConfig.av1Config.chromaSamplePosition);
#endif
   }
#undef LOG_CONFIG_QP
#undef LOG_CONFIG
}


/*
 *----------------------------------------------------------------------------
 *
 * VNCEncodeRegionNvEnc_QueryCaps --
 *
 *      Queries the capabilities of the nvidia encoder.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      Allocates memory.
 *
 *----------------------------------------------------------------------------
 */

Bool
VNCENCODEREGION(NVENC_SDK_SUFFIX, QueryCaps)(const VNCRegEncConfig *config,   // IN
                                             VNCRegionEncoderNvEncCaps *caps) // OUT
{
   NV_ENCODE_API_FUNCTION_LIST encodeFns = {0};
   HANDLE handle = NULL;

   if (Config_GetBool(FALSE, "RemoteDisplay.nvencUseLegacySDK") &&
       NVENCAPI_MAJOR_VERSION > LEGACY_NVENCAPI_MAJOR_VERSION) {
      return FALSE;
   }

   REGNVENC_DUMMY(config->encoderId, config->dynamicConfig);

   ASSERT(caps != NULL);

   if (!sCapsQueried) {
      if (!VNCEncodeRegionNvEncLoadLibrary()) {
         free(regEnc);
         return FALSE;
      }

      if (VNCEncodeRegionNvEncCreateOS(regEnc, NULL) != VNC_SUCCESS) {
         return FALSE;
      }

      if (!(handle = VNCEncodeRegionNvEncGetHandleInt(regEnc, &encodeFns))) {
         REGENCWARN("%s: Failed to obtain NvEnc encode session handle", __FUNCTION__);
         goto fail;
      }

      REGENC_RLOG(9, "Querying NvEnc caps");

      // H264
      sCaps.h264MaxWidth =
         NvEncQueryCap(encodeFns, handle, NV_ENC_CODEC_H264_GUID, NV_ENC_CAPS_WIDTH_MAX);
      if (sCaps.h264MaxWidth > 0) {
         sCaps.h264MaxHeight =
            NvEncQueryCap(encodeFns, handle, NV_ENC_CODEC_H264_GUID, NV_ENC_CAPS_HEIGHT_MAX);
         h264SupportsCustomVbvBufferSize = NvEncQueryCap(encodeFns, handle, NV_ENC_CODEC_H264_GUID,
                                                         NV_ENC_CAPS_SUPPORT_CUSTOM_VBV_BUF_SIZE);
         sCaps.h264SupportsYUV444 = NvEncQueryCap(encodeFns, handle, NV_ENC_CODEC_H264_GUID,
                                                  NV_ENC_CAPS_SUPPORT_YUV444_ENCODE);
         sCaps.h264Supports10Bit = NvEncQueryCap(encodeFns, handle, NV_ENC_CODEC_H264_GUID,
                                                 NV_ENC_CAPS_SUPPORT_10BIT_ENCODE);
      }

      // HEVC
      sCaps.hevcMaxWidth =
         NvEncQueryCap(encodeFns, handle, NV_ENC_CODEC_HEVC_GUID, NV_ENC_CAPS_WIDTH_MAX);
      if (sCaps.hevcMaxWidth > 0) {
         sCaps.hevcMaxHeight =
            NvEncQueryCap(encodeFns, handle, NV_ENC_CODEC_HEVC_GUID, NV_ENC_CAPS_HEIGHT_MAX);
         hevcSupportsCustomVbvBufferSize = NvEncQueryCap(encodeFns, handle, NV_ENC_CODEC_HEVC_GUID,
                                                         NV_ENC_CAPS_SUPPORT_CUSTOM_VBV_BUF_SIZE);
         sCaps.hevcSupportsYUV444 = NvEncQueryCap(encodeFns, handle, NV_ENC_CODEC_HEVC_GUID,
                                                  NV_ENC_CAPS_SUPPORT_YUV444_ENCODE);
         sCaps.hevcSupports10Bit = NvEncQueryCap(encodeFns, handle, NV_ENC_CODEC_HEVC_GUID,
                                                 NV_ENC_CAPS_SUPPORT_10BIT_ENCODE);
      }

      // AV1
#if NVENCAPI_MAJOR_VERSION > 8
      sCaps.av1MaxWidth =
         NvEncQueryCap(encodeFns, handle, NV_ENC_CODEC_AV1_GUID, NV_ENC_CAPS_WIDTH_MAX);
      if (sCaps.av1MaxWidth > 0) {
         sCaps.av1MaxHeight =
            NvEncQueryCap(encodeFns, handle, NV_ENC_CODEC_AV1_GUID, NV_ENC_CAPS_HEIGHT_MAX);
         av1SupportsCustomVbvBufferSize = NvEncQueryCap(encodeFns, handle, NV_ENC_CODEC_AV1_GUID,
                                                        NV_ENC_CAPS_SUPPORT_CUSTOM_VBV_BUF_SIZE);
         sCaps.av1SupportsYUV444 = NvEncQueryCap(encodeFns, handle, NV_ENC_CODEC_AV1_GUID,
                                                 NV_ENC_CAPS_SUPPORT_YUV444_ENCODE);
         sCaps.av1Supports10Bit = NvEncQueryCap(encodeFns, handle, NV_ENC_CODEC_AV1_GUID,
                                                NV_ENC_CAPS_SUPPORT_10BIT_ENCODE);
      }
#endif

      REGENCLG0(
         "GPU caps - %s%s%s%s%s%s - "
         "H264 max (%dx%d pixels) HEVC max (%dx%d pixels) AV1 max (%dx%d pixels)",
         sCaps.h264SupportsYUV444 ? "H264-4:4:4 " : "",
         sCaps.hevcSupportsYUV444 ? "HEVC-4:4:4 " : "", sCaps.av1SupportsYUV444 ? "AV1-4:4:4 " : "",
         sCaps.h264Supports10Bit ? "H264-10bit " : "", sCaps.hevcSupports10Bit ? "HEVC-10bit " : "",
         sCaps.av1Supports10Bit ? "AV1-10bit " : "", sCaps.h264MaxWidth, sCaps.h264MaxHeight,
         sCaps.hevcMaxWidth, sCaps.hevcMaxHeight, sCaps.av1MaxWidth, sCaps.av1MaxHeight);

#define QUERY_PRESET(codec, preset, tuning)                                                        \
   NvEncPrintPresetConfig(regEnc, handle, &encodeFns, codec, preset, #preset, tuning, #tuning)
#if NVENCAPI_MAJOR_VERSION > 8
#   define LOW_LATENCY_P1_PRESET NV_ENC_PRESET_P1_GUID
#   define LOW_LATENCY_P2_PRESET NV_ENC_PRESET_P2_GUID
#   define LOW_LATENCY_ULL_TUNING NV_ENC_TUNING_INFO_ULTRA_LOW_LATENCY
#   define LOW_LATENCY_LL_TUNING NV_ENC_TUNING_INFO_LOW_LATENCY
#else
#   define LOW_LATENCY_P1_PRESET NV_ENC_PRESET_LOW_LATENCY_HP_GUID
#   define LOW_LATENCY_P2_PRESET NV_ENC_PRESET_LOW_LATENCY_DEFAULT_GUID
#   define LOW_LATENCY_ULL_TUNING 0
#   define LOW_LATENCY_LL_TUNING 0
#endif

      if (sCaps.h264MaxWidth > 0) {
         QUERY_PRESET(NV_ENC_CODEC_H264_GUID, LOW_LATENCY_P1_PRESET, LOW_LATENCY_ULL_TUNING);
         QUERY_PRESET(NV_ENC_CODEC_H264_GUID, LOW_LATENCY_P1_PRESET, LOW_LATENCY_LL_TUNING);
         QUERY_PRESET(NV_ENC_CODEC_H264_GUID, LOW_LATENCY_P2_PRESET, LOW_LATENCY_ULL_TUNING);
         QUERY_PRESET(NV_ENC_CODEC_H264_GUID, LOW_LATENCY_P2_PRESET, LOW_LATENCY_LL_TUNING);
      }

      if (sCaps.hevcMaxWidth > 0) {
         QUERY_PRESET(NV_ENC_CODEC_HEVC_GUID, LOW_LATENCY_P1_PRESET, LOW_LATENCY_ULL_TUNING);
         QUERY_PRESET(NV_ENC_CODEC_HEVC_GUID, LOW_LATENCY_P1_PRESET, LOW_LATENCY_LL_TUNING);
         QUERY_PRESET(NV_ENC_CODEC_HEVC_GUID, LOW_LATENCY_P2_PRESET, LOW_LATENCY_ULL_TUNING);
         QUERY_PRESET(NV_ENC_CODEC_HEVC_GUID, LOW_LATENCY_P2_PRESET, LOW_LATENCY_LL_TUNING);
      }

#if NVENCAPI_MAJOR_VERSION > 8
      if (sCaps.av1MaxWidth > 0) {
         QUERY_PRESET(NV_ENC_CODEC_AV1_GUID, LOW_LATENCY_P1_PRESET, LOW_LATENCY_ULL_TUNING);
         QUERY_PRESET(NV_ENC_CODEC_AV1_GUID, LOW_LATENCY_P1_PRESET, LOW_LATENCY_LL_TUNING);
         QUERY_PRESET(NV_ENC_CODEC_AV1_GUID, LOW_LATENCY_P2_PRESET, LOW_LATENCY_ULL_TUNING);
         QUERY_PRESET(NV_ENC_CODEC_AV1_GUID, LOW_LATENCY_P2_PRESET, LOW_LATENCY_LL_TUNING);
      }
#endif

#undef LOW_LATENCY_P1_PRESET
#undef LOW_LATENCY_P2_PRESET
#undef LOW_LATENCY_ULL_TUNING
#undef LOW_LATENCY_LL_TUNING
#undef QUERY_PRESET

      sCapsQueried = TRUE;

   fail:
      if (handle && encodeFns.nvEncDestroyEncoder) {
         NVENCSTATUS status = encodeFns.nvEncDestroyEncoder(handle);
         if (NV_ENC_FAILED(status)) {
            REGENCWARN("%s - Failed to destroy the NVIDIA HW encoder. "
                       "Error: %d",
                       __FUNCTION__, status);
         }
      }
      VNCEncodeRegionNvEncDestroyOS(regEnc);
      VNCEncodeRegionNvEncUnloadLibrary();
   }
   free(regEnc);

   *caps = sCaps;

   return sCapsQueried;
}
