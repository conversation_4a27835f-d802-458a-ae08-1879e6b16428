# Copyright (c) Omnissa, LLC. All rights reserved.
# This product is protected by copyright and intellectual property laws in the
# United States and other countries as well as by international treaties.
# -- Omnissa Restricted

import os
import sys

# Access peer file scons_bootstrap.py.
sys.path.append(os.path.dirname(__file__))
from scons_bootstrap import ConanSConsBootstrap


class ConanHcMac(ConanSConsBootstrap):
    settings = "os", "arch", "build_type"

    def requirements(self):
        super().requirements()

        # Append required packages instead of inserting them, unless you know what you do
        self.requires("fmt/10.2.1")
        self.requires("jsoncpp/1.9.5")
        self.requires("libcbor/0.11.0")
        self.requires("libfido2/1.13.0")
        self.requires("nlohmann_json/3.11.2")
        self.requires("opus/1.4")
        self.requires("libcurl/8.10.0", options={"shared": True})
        self.requires("libgettext/0.22", options={ "shared" : True})
        self.requires("libsigcpp/3.6.0", options={ "shared" : True})
        self.requires("pcre2/10.42", options={ "shared" : True})
        self.requires("glib/2.84.1", options={ "shared" : True})
        self.requires("glibmm/2.78.1", options={ "shared" : True})
        self.requires("gtest/1.14.0", options={"shared": True})
        self.requires("ocmock/3.9.1")
        self.requires("openssl/3.0.16")
        self.requires("openssl_fips_validated/3.0.9")
        self.requires("ogg/1.3.2")
        self.requires("speex/1.2rc2")
        self.requires("speexdsp/1.2rc3")
        self.requires("theora/1.1.1")
        self.requires("icu/74.2")
        self.requires("sse2neon/1.7.0")
        self.requires("libjpeg-turbo/3.0.1")
        self.requires("libyuv/1882")
        self.requires("abseil/20240116.2")
        self.requires("protobuf/3.25.3")
        self.requires("protobuf-c/1.5.0")
        self.requires("snappy/1.1.7")
        self.requires("imgui/1.88")
        self.requires("implot/0.14")
        self.requires("fastlz/0.1.0")
        self.requires("lldpd/1.0.17")
        self.requires("cairo/1.18.0", options={"shared": True})
        self.requires("zlib/1.3.1")
        self.requires("libpng/1.6.48")
        self.requires("pixman/0.44.2", options={"shared": True})
        self.requires("boost/1.86.0")
        self.requires("libx264/164.20220124", options={"shared": True})
        self.requires("ffmpeg/7.0.1", options={"shared": True})
        self.requires("cunit/2.1-3")

    def build_requirements(self):
        super().build_requirements()

        self.tool_requires("7zip/24.09")
        self.tool_requires("dotnet_sdk/8.0.100")
        self.tool_requires("doxygen/1.9.4")
        self.tool_requires("gettext/0.22.5")
        self.tool_requires("icu/74.2")
        self.tool_requires("protobuf/3.25.3")
        self.tool_requires("protobuf-c/1.5.0")
