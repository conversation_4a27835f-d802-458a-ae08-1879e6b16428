# Copyright (c) Omnissa, LLC. All rights reserved.
# This product is protected by copyright and intellectual property laws in the
# United States and other countries as well as by international treaties.
# -- Omnissa Restricted

import os
import sys

# Access peer file scons_bootstrap.py.
sys.path.append(os.path.dirname(__file__))
from scons_bootstrap import ConanSConsBootstrap


class ConanHcLin(ConanSConsBootstrap):
    settings = "os", "arch", "build_type"

    def requirements(self):
        super().requirements()

        # Append required packages instead of inserting them, unless you know what you do
        self.requires("glibc/2.17")
        self.requires("zlib/1.3.1", options={"shared": True})
        self.requires("fabulatech_common/250409")
        self.requires("fabulatech_scanner/250409")
        self.requires("fabulatech_serialport/250409")
        self.requires("fmt/10.2.1")
        self.requires("jsoncpp/1.9.5")
        self.requires("nlohmann_json/3.11.2")
        self.requires("opus/1.4")
        self.requires("abseil/20240116.2")
        self.requires("protobuf/3.25.3")
        self.requires("protobuf-c/1.5.0")
        if self.settings.arch == "x86_64":
            self.requires("gstreamer/1.24.10", options={"shared": True})
            self.requires("gst-plugins-base/1.19.2", options={"shared": True})
            self.requires("libfuse/2.9.9", options={"shared": True})
            if self.settings.build_type == "Release":
                self.requires("cef_lin/134.0.6998.44")
        self.requires("libgcrypt/1.10.3", options={"shared": True})
        self.requires("libxml2/2.13.8", options={"shared": True})
        self.requires("libcurl/8.10.0", options={"shared": True})
        self.requires("xorg-proto/2023.2")
        self.requires("libepoxy/1.5.10", options={"shared": True})
        self.requires("libx11/1.8.7", options={"shared": True})
        self.requires("libxkbfile/1.1.3", options={"shared": True})
        self.requires("libxi/1.8.1", options={"shared": True})
        self.requires("libxcomposite/0.4.6", options={"shared": True})
        self.requires("libxscrnsaver/1.2.4", options={"shared": True})
        self.requires("libxdmcp/1.1.4", options={"shared": True})
        self.requires("libxau/1.0.11", options={"shared": True})
        self.requires("libxcb/1.16", options={"shared": True})
        self.requires("libxdamage/1.1.6", options={"shared": True})
        self.requires("libxext/1.3.6", options={"shared": True})
        self.requires("libxfixes/6.0.1", options={"shared": True})
        self.requires("libxtst/1.2.4", options={"shared": True})
        self.requires("libxcursor/1.2.2", options={"shared": True})
        self.requires("libxinerama/1.1.5", options={"shared": True})
        self.requires("libxrandr/1.5.4", options={"shared": True})
        self.requires("libxrender/0.9.11", options={"shared": True})
        self.requires("mesa/23.3.6", options={"shared": True})
        self.requires("freetype/2.13.2", options={"shared": True})
        self.requires("openssl/3.0.16")
        self.requires("openssl_fips_validated/3.0.9")
        self.requires("harfbuzz/8.3.0", options={"shared": True})
        self.requires("ogg/1.3.2")
        self.requires("speex/1.2rc2")
        self.requires("speexdsp/1.2rc3")
        self.requires("theora/1.1.1")
        self.requires("v4l-utils/1.20.0", options={"shared": True})
        self.requires("icu/74.2")
        self.requires("snappy/1.1.7")
        self.requires("libsndfile/1.2.2", options={"shared": True})
        if self.settings.arch == "armv7hf":
            self.requires("sse2neon/1.7.0")
            self.requires("ffmpeg/7.0.1", options={"shared": True})
            self.requires("raspberrypi/20231020");

        self.requires("libjpeg-turbo/3.0.1")
        self.requires("libgpg-error/1.36", options={"shared": True})
        self.requires("gtest/1.17.0", options={"shared": True})
        self.requires("libyuv/1882")
        self.requires("cups/2.3.6")
        self.requires("imgui/1.88")
        self.requires("implot/0.14")
        self.requires("file/5.44")
        self.requires("fastlz/0.1.0")
        self.requires("pulseaudio/17.0", options={"shared": True})
        self.requires("libudev/248.13", options={"shared": True})
        self.requires("glib/2.84.1", options={"shared": True})
        self.requires("glibmm/2.44.0", options={"shared": True})
        self.requires("pcre2/10.42", options={"shared": True})
        self.requires("libffi/3.4.4", options={"shared": True})
        self.requires("gdk-pixbuf-xlib/2.40.2", options={"shared": True})
        self.requires("gdk-pixbuf/2.42.10", options={"shared": True})
        self.requires("dbus/1.15.8", options={"shared": True})
        self.requires("expat/2.7.1", options={"shared": True})
        self.requires("cairo/1.18.0", options={"shared": True})
        self.requires("cairomm/1.10.0", options={"shared": True})
        self.requires("libpng/1.6.48", options={"shared": True})
        self.requires("pixman/0.44.2", options={"shared": True})
        self.requires("at-spi2-core/2.51.0", options={"shared" : True})
        self.requires("boost/1.86.0")
        self.requires("p11-kit/0.23.22", options={"shared": True})
        self.requires("libgcr/4.3.0", options={"shared": True})
        self.requires("pango/1.50.7", options={"shared": True})
        self.requires("pangomm/2.34.0", options={"shared": True})
        self.requires("gtk/3.24.42", options={"shared": True})
        self.requires("gtkmm/3.10.1", options={"shared": True})
        self.requires("libsigcpp/2.10.8", options={"shared": True})
        self.requires("libcbor/0.11.0")
        self.requires("libfido2/1.13.0")
        self.requires("libvdpau/1.5", options={"shared": True})

        isx86_64 = self.settings.arch == "x86_64"
        if isx86_64:
            self.requires("libx264/164.20220124", options={"shared": True})
            self.requires("ffmpeg-vdpau/7.0.1", options={"with_vdpau": True, "shared": True})
            self.requires("ffmpeg/7.0.1", options={"shared": True})
            self.requires("gnome-menus/3.36", options={"shared": True})
        self.requires("libiconv/1.17", options={"shared": True})
        self.requires("fontconfig/2.15.0", options={"shared": True})
        self.requires("atkmm/2.22.7", options={"shared": True})
        self.requires("cunit/2.1-3")
        self.requires("libxcvt/0.1.2")
        self.requires("util_linux/2.39.3", options={"shared": True})

    def build_requirements(self):
        super().build_requirements()

        self.tool_requires("7zip/24.09")
        self.tool_requires("m4/1.4.19")
        self.tool_requires("autoconf/2.71")
        self.tool_requires("automake/1.16.5")
        self.tool_requires("doxygen/1.9.4")
        self.tool_requires("dotnet_sdk/8.0.100")
        self.tool_requires("dpkg/1.22.4")
        self.tool_requires("libmagic/5.45")
        self.tool_requires("tar/1.35")
        self.tool_requires("rpm/4.19.1")
        self.tool_requires("protobuf/3.25.3")
        self.tool_requires("protobuf-c/1.5.0")
        self.tool_requires("gawk/5.3.0")
        self.tool_requires("icu/74.2")
        self.tool_requires("gnupg/2.2.42")
        self.tool_requires("findutils/4.10.0")
        self.tool_requires("coreutils/9.4")
        self.tool_requires("sed/4.9")
        self.tool_requires("glib/<host_version>")
        self.tool_requires("gdk-pixbuf/<host_version>")
        self.tool_requires("gettext/0.22.5")
        self.tool_requires("patchelf/0.17.2")
