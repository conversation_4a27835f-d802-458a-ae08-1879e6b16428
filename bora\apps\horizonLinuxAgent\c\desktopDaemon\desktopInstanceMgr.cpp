/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * desktopInstanceMgr.cpp --
 */


#include "desktopInstance.h"
#include <fstream>
#include <inttypes.h>
#include <memory>
#include <signal.h>
#include <string>
#include <sys/stat.h>
#include <sys/types.h>
#include <thread>
#include <unistd.h>

#include "commonUtils.h"
#include "desktopDCommon.h"
#include "desktopInstanceMgr.h"
#include "desktopUtils.h"
#include "ipc.h"
#include "ipcMsgDispatcher.h"
#include "lock.h"
#include <vector>


namespace Desktop {

DesktopInstanceMgr *DesktopInstanceMgr::gDesktopMgr = NULL;


/*
 *----------------------------------------------------------------------------
 *
 * DesktopInstanceMgr::DesktopInstanceMgr --
 *
 *   DesktopInstanceMgr constructor.
 *
 * Results:
 *   None.
 *
 * Side effects:
 *   None.
 *
 *----------------------------------------------------------------------------
 */

DesktopInstanceMgr::DesktopInstanceMgr() : appScanner("appScanner")
{
   mDpyMgrRestarted = false;
   mDesktopsLock = MXUser_CreateRecLock("DesktopMgr", RANK_application);
   DesktopUtils::InitDisplayTable();
}


/*
 *----------------------------------------------------------------------------
 *
 * DesktopInstanceMgr::~DesktopInstanceMgr --
 *
 *   DesktopInstanceMgr destructor.
 *
 * Results:
 *   None.
 *
 * Side effects:
 *   None.
 *
 *----------------------------------------------------------------------------
 */

DesktopInstanceMgr::~DesktopInstanceMgr()
{
   MXUser_DestroyRecLock(mDesktopsLock);
   mDesktopsLock = NULL;

   if (gDesktopMgr) {
      delete gDesktopMgr;
      gDesktopMgr = NULL;
   }
}


/*
 *----------------------------------------------------------------------------
 *
 * DesktopInstanceMgr::getDesktop --
 *
 *   Get the desktop instance by desktop ID.
 *
 * Results:
 *   The shared pointer of the desktop instance if found, otherwise nullptr.
 *
 * Side effects:
 *   None.
 *
 *----------------------------------------------------------------------------
 */

std::shared_ptr<DesktopInstance>
DesktopInstanceMgr::getDesktop(uint64 desktopId) // IN
{
   LinuxAgent::lock _(mDesktopsLock);

   DesktopInstanceMap::iterator iter;
   iter = mDesktops.find(desktopId);
   if (iter == mDesktops.end()) {
      return nullptr;
   } else {
      return iter->second;
   }
}


/*
 *----------------------------------------------------------------------------
 *
 * DesktopInstanceMgr::getPrelaunchedDesktop --
 *
 *   Get the a prelauched desktop instance.
 *
 * Results:
 *   The shared pointer of the desktop instance if found, otherwise nullptr.
 *
 * Side effects:
 *   None.
 *
 *----------------------------------------------------------------------------
 */

std::shared_ptr<DesktopInstance>
DesktopInstanceMgr::getPrelaunchedDesktop()
{
   LinuxAgent::lock _(mDesktopsLock);

   if (mPrelaunchedDesktops.empty()) {
      return nullptr;
   } else {
      return mPrelaunchedDesktops.front();
   }
}


/*
 *----------------------------------------------------------------------------
 *
 * DesktopInstanceMgr::getConsoleDesktop --
 *
 *   Get the console desktop.
 *
 * Results:
 *   The shared pointer of the console desktop instance if found,
 *   otherwise nullptr.
 *
 * Side effects:
 *   None.
 *
 *----------------------------------------------------------------------------
 */

std::shared_ptr<DesktopInstance>
DesktopInstanceMgr::getConsoleDesktop()
{
   LinuxAgent::lock _(mDesktopsLock);

   for (const auto &d : mDesktops) {
      if (d.second->getDesktopType() == Desktop::CONSOLE) {
         return d.second;
      }
   }

   return nullptr;
}


/*
 *----------------------------------------------------------------------------
 *
 * DesktopInstanceMgr::getDesktop --
 *
 *   Get the desktop instance by display number.
 *
 * Results:
 *   The shared pointer of the desktop instance if found, otherwise nullptr.
 *
 * Side effects:
 *   None.
 *
 *----------------------------------------------------------------------------
 */

std::shared_ptr<DesktopInstance>
DesktopInstanceMgr::getDesktop(const std::string &display) // IN
{
   LinuxAgent::lock _(mDesktopsLock);

   for (const auto &it : mDesktops) {
      auto desktop = it.second;
      if (display == desktop->getXDisplay().substr(1)) {
         return desktop;
      }
   }

   return nullptr;
}


/*
 *----------------------------------------------------------------------------
 *
 * DesktopInstanceMgr::addDesktop --
 *
 *   Add a desktop instance to the map.
 *
 * Results:
 *   false if already exists, otherwise put in the map and return true.
 *
 * Side effects:
 *   None.
 *
 *----------------------------------------------------------------------------
 */

bool
DesktopInstanceMgr::addDesktop(std::shared_ptr<DesktopInstance> desktop) // IN
{
   assert(desktop != NULL);

   LinuxAgent::lock _(mDesktopsLock);

   DesktopInstanceMap::iterator iter;
   iter = mDesktops.find(desktop->getDesktopId());
   if (iter != mDesktops.end()) {
      Log_Error("%s: desktop %" FMT64 "u already exist.\n", __FUNCTION__, desktop->getDesktopId());
      return false;
   }

   mDesktops[desktop->getDesktopId()] = desktop;
   return true;
}


/*
 *----------------------------------------------------------------------------
 *
 * DesktopInstanceMgr::removeDesktop --
 *
 *   Remove a desktop instance from the map.
 *
 * Results:
 *   None.
 *
 * Side effects:
 *   None.
 *
 *----------------------------------------------------------------------------
 */

void
DesktopInstanceMgr::removeDesktop(uint64 desktopId) // IN
{
   LinuxAgent::lock _(mDesktopsLock);

   mDesktops.erase(desktopId);
}


/*
 *----------------------------------------------------------------------------
 *
 * DesktopInstanceMgr::addPrelaunchedDesktop --
 *
 *   Add a prelaunched desktop instance to the map.
 *
 * Results:
 *   None.
 *
 * Side effects:
 *   None.
 *
 *----------------------------------------------------------------------------
 */

void
DesktopInstanceMgr::addPrelaunchedDesktop(std::shared_ptr<DesktopInstance> desktop) // IN
{
   assert(desktop != NULL);

   LinuxAgent::lock _(mDesktopsLock);

   mPrelaunchedDesktops.push_back(desktop);
}


/*
 *----------------------------------------------------------------------------
 *
 * DesktopInstanceMgr::removePrelaunchedDesktop --
 *
 *   Remove a prelaunched desktop instance from the map.
 *
 * Results:
 *   None.
 *
 * Side effects:
 *   None.
 *
 *----------------------------------------------------------------------------
 */

void
DesktopInstanceMgr::removePrelaunchedDesktop(std::shared_ptr<DesktopInstance> desktop) // IN
{
   LinuxAgent::lock _(mDesktopsLock);

   mPrelaunchedDesktops.remove(desktop);
}


/*
 *----------------------------------------------------------------------------
 *
 * DesktopInstanceMgr::clearPrelaunchedDesktop --
 *
 *   Clear the prelaunched desktop instance that is in stopped status.
 *
 * Results:
 *   None.
 *
 * Side effects:
 *   None.
 *
 *----------------------------------------------------------------------------
 */

void
DesktopInstanceMgr::clearPrelaunchedDesktop()
{
   LinuxAgent::lock _(mDesktopsLock);
   mPrelaunchedDesktops.remove_if(
      [](std::shared_ptr<DesktopInstance> d) { return d && d->getStatus() == STOPPED; });
}


/*
 *----------------------------------------------------------------------------
 *
 * DesktopInstanceMgr::prepareDesktop --
 *
 *   Prepare desktop for StartSession request or automatic prelauching.
 *
 * Results:
 *   true on success, false on fail.
 *
 * Side effects:
 *   None.
 *
 *----------------------------------------------------------------------------
 */

bool
DesktopInstanceMgr::prepareDesktop(uint64 desktopId,                 // IN
                                   const std::string &path,          // IN
                                   std::vector<std::string> &params, // IN
                                   DesktopAttributeMap *attrs)       // IN
{
   using namespace Desktop;
   LinuxAgent::lock _(mDesktopsLock);
   int maxSessionNumber = DesktopUtils::IsVDIMode() ? 1 : DesktopUtils::GetSessionMaxCount();
   DesktopType type = DesktopType::NORMAL;
   std::string dpiSync = "";
   std::string clientDpi = "";

   // Parse desktop attributes
   if (attrs != nullptr) {
      DesktopAttributeMap::iterator it;
      it = attrs->find("DPISYNC");
      if (it != attrs->end()) {
         dpiSync = it->second;
      }

      it = attrs->find("CLIENTDPI");
      if (it != attrs->end()) {
         clientDpi = it->second;
      }
   }

   if (desktopId == 0) { // Prelaunched desktop
      if (!DesktopUtils::IsHznGreeterEnabled()) {
         type = DesktopType::XDMCP;
      }
      // Check session buffer number.
      if ((int)mPrelaunchedDesktops.size() >= DesktopUtils::GetSessionBufferCount()) {
         Log_Info("%s: Already reach session buffer count, no need to prelauch more.\n",
                  __FUNCTION__);
         return false;
      }

      // Check max session number.
      if ((int)(mPrelaunchedDesktops.size() + mDesktops.size()) >= maxSessionNumber) {
         Log_Info("%s:Already reach session max count, no need to prelauch more.\n", __FUNCTION__);
         return false;
      }
   } else { // Got request to start a new desktop
      // Fail if the corresponding desktop already exists.
      if (getDesktop(desktopId)) {
         Log_Error("%s: Fail to prepare desktop,"
                   " desktop %" FMT64 "u has existed.\n",
                   __FUNCTION__, desktopId);
         return false;
      }

      // In VDI mode, if Console desktop exsits, attach it.
      if (DesktopUtils::IsVDIMode()) {
         auto console = getConsoleDesktop();
         auto nDesktop = mDesktops.size();

         if (nDesktop == 1 && console && console->getDesktopId() == static_cast<uint64>(-1)) {
            // Only unmapped console destop exist,
            // map it with the given ID and notify it is ready.
            Log_Info("%s: Umapped console desktop found, map it to ID: %" FMT64 "u\n", __FUNCTION__,
                     desktopId);

            removeDesktop(console->getDesktopId());
            console->setDesktopId(desktopId);
            CommonUtils::SetDpiSync(console->getXDisplay(), clientDpi, dpiSync);
            CommonUtils::SetDesktopId(console->getXDisplay(), std::to_string(desktopId));
            addDesktop(console);

            console->notifyDesktopReady();

            return true;
         } else if (nDesktop != 0) {
            Log_Error("%s: In VDI mode, "
                      "creating an additional desktop(%" FMT64 "u) is forbidden.\n",
                      __FUNCTION__, desktopId);

            return false;
         }
      }

      /* New desktop request reaches here, check if it is about to
       * exceed max sesison account.
       */
      if ((int)mDesktops.size() >= maxSessionNumber) {
         Log_Error("%s: Already reach session max count, failed to start a new "
                   "desktop: %" FMT64 "u.\n",
                   __FUNCTION__, desktopId);
         return false;
      }
   }

   std::shared_ptr<DesktopInstance> desktop = std::make_shared<DesktopInstance>(desktopId, type);
   if (!desktop || desktop->getDispNum() == -1) {
      Log_Error("Failed to create desktop: %" FMT64 "u.\n", desktopId);
      return false;
   }

   if (desktopId > 0) {
      if (!addDesktop(desktop)) {
         return false;
      }
   } else {
      addPrelaunchedDesktop(desktop);
   }

   CommonUtils::SetDpiSync(desktop->getXDisplay(), clientDpi, dpiSync);
   // Desktop Worker starts here.
   if (desktop->start(path, params)) {
      if (desktopId > 0) {
         CommonUtils::SetDesktopId(desktop->getXDisplay(), std::to_string(desktopId));
      }
   } else {
      if (desktopId > 0) {
         removeDesktop(desktopId);
      } else {
         removePrelaunchedDesktop(desktop);
      }

      return false;
   }

   return true;
}


/*
 *----------------------------------------------------------------------------
 *
 * DesktopInstanceMgr::prepareDesktop --
 *
 *   A wrapper function to construct parameters.
 *
 * Results:
 *   true on success, false on fail.
 *
 * Side effects:
 *   None.
 *
 *----------------------------------------------------------------------------
 */

bool
DesktopInstanceMgr::prepareDesktop(uint64 desktopId,           // IN
                                   bool isPrelaunch,           // IN
                                   std::string ssoType,        // IN
                                   DesktopAttributeMap *attrs) // IN
{
   std::vector<std::string> params;
   if (!Desktop::DesktopUtils::IsVDIMode()) {
      params.push_back("-m");
   }

   if (!Desktop::DesktopUtils::IsHznGreeterEnabled()) {
      params.push_back("-g");
   }

   if (isPrelaunch) {
      params.push_back("-c");
   }

   if (!ssoType.empty()) {
      params.push_back("-r");
      params.push_back(ssoType);
   }

   return prepareDesktop(desktopId, XSERVER_PATH, params, attrs);
}

/*
 *----------------------------------------------------------------------------
 *
 * DesktopInstanceMgr::PrelaunchDesktop --
 *
 *   Prelauch a dekstop and schedule a next round if it succeeds this time.
 *
 * Results:
 *   None.
 *
 * Side effects:
 *   None.
 *
 *----------------------------------------------------------------------------
 */

void
DesktopInstanceMgr::PrelaunchDesktop(void *mgr) // IN
{
   bool ssoEnabled = Desktop::DesktopUtils::IsSsoEnabled();
   bool hznGreeterEnabled = Desktop::DesktopUtils::IsHznGreeterEnabled();
   bool vdiMode = Desktop::DesktopUtils::IsVDIMode();
   if (ssoEnabled || (hznGreeterEnabled && vdiMode)) {
      Log_Info("%s: no need to prelaunch desktop as SSO %s, hznGreeter %s, "
               "vdiMode %s.\n",
               __FUNCTION__, ssoEnabled ? "enabled" : "disabled",
               hznGreeterEnabled ? "enabled" : "disabled", vdiMode ? "true" : "false");
      return;
   }

   if (!mgr) {
      Log_Warning("%s: Invalid desktop mamanger.\n", __FUNCTION__);
      return;
   }

   DesktopInstanceMgr *manager = static_cast<DesktopInstanceMgr *>(mgr);
   if (manager->prepareDesktop(0, true)) {
      manager->schedulePrelaunchDesktop();
   }
}


/*
 *----------------------------------------------------------------------------
 *
 * DesktopInstanceMgr::schedulePrelaunchDesktop --
 *
 *   Schedule to prelauch a desktop.
 *
 * Results:
 *   None.
 *
 * Side effects:
 *   None.
 *
 *----------------------------------------------------------------------------
 */

void
DesktopInstanceMgr::schedulePrelaunchDesktop()
{
   DesktopUtils::PollCallback(&DesktopInstanceMgr::PrelaunchDesktop, this);
}


/*
 *----------------------------------------------------------------------------
 *
 * DesktopInstanceMgr::destroyDesktop --
 *
 *   Destroy the desktop instance.
 *
 * Results:
 *   true for success, otherwise false.
 *
 * Side effects:
 *   None.
 *
 *----------------------------------------------------------------------------
 */

bool
DesktopInstanceMgr::destroyDesktop(uint64 desktopId) // IN
{
   std::shared_ptr<DesktopInstance> desktop = getDesktop(desktopId);
   if (!desktop) {
      Log_Info("%s: desktop %" FMT64 "u doesn't exist.\n", __FUNCTION__, desktopId);
      return false;
   }

   if (desktop->getStatus() == STOPPED) {
      Log_Info("%s: desktop %" FMT64 "u already stopped, destroy it.\n", __FUNCTION__, desktopId);
      removeDesktop(desktopId);
      IpcMsgDispatcher::NotifyDesktopDestroyed(desktopId);
      CommonUtils::RemoveDesktopId(desktop->getXDisplay(), std::to_string(desktopId));
      return true;
   } else if (desktop->getStatus() != STOPPING) {
      Log_Info("%s: desktop %" FMT64 "u is still running, stop it.\n", __FUNCTION__, desktopId);
      desktop->stop(true);
   }

   return true;
}


/*
 *----------------------------------------------------------------------------
 *
 * DesktopInstanceMgr::notifyDesktopReady --
 *
 *   Notify view agent when the desktop is ready.
 *
 * Results:
 *   None.
 *
 * Side effects:
 *   None.
 *
 *----------------------------------------------------------------------------
 */

void
DesktopInstanceMgr::notifyDesktopReady(const std::string &display) // IN
{
   auto desktop = getDesktop(display);
   if (desktop) {
      desktop->notifyDesktopReady();
   }
}


/*
 *----------------------------------------------------------------------------
 *
 * DesktopInstanceMgr::notifyDesktopFailed --
 *
 *   Destroy desktop when greeter invalid.
 *
 * Results:
 *   None.
 *
 * Side effects:
 *   None.
 *
 *----------------------------------------------------------------------------
 */

void
DesktopInstanceMgr::notifyDesktopFailed(const std::string &display) // IN
{
   auto desktop = getDesktop(display);
   if (desktop) {
      destroyDesktop(desktop->getDesktopId());
   }
}


/*
 *----------------------------------------------------------------------------
 *
 * DesktopInstanceMgr::startProgram --
 *
 *  Start process task handling. Only to run appScanner for now.
 *
 * Results:
 *   true for success, otherwise false.
 *
 * Side effects:
 *   None.
 *
 *----------------------------------------------------------------------------
 */

bool
DesktopInstanceMgr::startProgram(uint64 desktopId,                 // IN
                                 uint64 connectionId,              // IN
                                 const std::string &name,          // IN
                                 const std::string &path,          // IN
                                 std::vector<std::string> &params, // IN
                                 bool isMonitor)                   // IN
{
   if (name != "appScanner") {
      Log_Info("%s: Invalid process %s to be started.\n", __FUNCTION__, name.c_str());
      return false;
   }

   if (appScanner.isRunning()) {
      Log_Debug("%s: appScanner is running , return.\n", __FUNCTION__);
      return true;
   }

   std::vector<std::string> envs;
   return appScanner.start(path, params, envs);
}


/*
 *----------------------------------------------------------------------------
 *
 * DesktopInstanceMgr::actionLogLevel --
 *
 *  Pass set loglevel request to desktop instances.
 *
 * Results:
 *   True for success, otherwise false.
 *
 * Side effects:
 *   None.
 *
 *----------------------------------------------------------------------------
 */

bool
DesktopInstanceMgr::actionLogLevel(const std::string &sRequest) // IN
{
   bool ret = true;
   LinuxAgent::lock _(mDesktopsLock);

   for (const auto &d : mDesktops) {
      if (!d.second->actionLogLevel(sRequest)) {
         Log_Error("%s: Failed to actionLogLevel at desktop %" FMT64 "u\n", __FUNCTION__, d.first);
         ret = false;
      }
   }

   return ret;
}


/*
 *----------------------------------------------------------------------------
 *
 * DesktopInstanceMgr::actionProgram--
 *
 *  Pass start/stop process request to the desktop instance.
 *
 * Results:
 *   True for success, otherwise false.
 *
 * Side effects:
 *   None.
 *
 *----------------------------------------------------------------------------
 */

bool
DesktopInstanceMgr::actionProgram(uint64 desktopId,            // IN
                                  const std::string &sRequest) // IN
{
   auto desktop = getDesktop(desktopId);
   if (desktop == nullptr) {
      Log_Info("%s: desktop %" FMT64 "u doesn't exist.\n", __FUNCTION__, desktopId);
      return false;
   }

   return desktop->actionProgram(sRequest);
}


/*
 *----------------------------------------------------------------------------
 *
 * DesktopInstanceMgr::handleSso --
 *
 *  Pass SSO info to the desktop instance.
 *
 * Results:
 *   True for success, otherwise false.
 *
 * Side effects:
 *   None.
 *
 *----------------------------------------------------------------------------
 */

bool
DesktopInstanceMgr::handleSso(uint64 desktopId,            // IN
                              const std::string &sRequest) // IN
{
   boost::property_tree::ptree pt;
   std::istringstream iss(sRequest);
   std::string username = "";
   std::string password = "";
   std::string ssoType = "";
   std::string dpiSync = "";
   std::string clientDpi = "";
   std::shared_ptr<DesktopInstance> desktop = nullptr;

   try {
      boost::property_tree::json_parser::read_json(iss, pt);
      username = pt.get<std::string>("Request.SSO.username", "");
      password = pt.get<std::string>("Request.SSO.password", "");
      ssoType = pt.get<std::string>("Request.SSO.type", "");
      dpiSync = pt.get<std::string>("Request.DESKTOP.dpisync", "");
      clientDpi = pt.get<std::string>("Request.DESKTOP.clientdpi", "");
      Log_Info("%s: username: %s, SSO type: %s\n", __FUNCTION__, username.c_str(), ssoType.c_str());
   } catch (const boost::property_tree::json_parser::json_parser_error &e) {
      Log_Error("%s: Parse json error, cause: %s\n", __FUNCTION__, e.message().c_str());
      return false;
   } catch (const std::exception &e) {
      Log_Error("%s: Parse error, cause: %s\n", __FUNCTION__, e.what());
      return false;
   }

   desktop = getDesktop(desktopId);
   if (desktop) {
      return desktop->actionProgram(sRequest);
   } else {
      const std::string TRUESSO = "truesso";
      const std::string SMARTCARD = "smartcard";
      const std::string PASSWORD = "password";
      bool hasSSO = Desktop::DesktopUtils::IsSsoEnabled() &&
                    (ssoType == TRUESSO || ssoType == SMARTCARD || ssoType == PASSWORD);
      bool passCred = Desktop::DesktopUtils::IsSsoEnabled() && ssoType == PASSWORD;
      DesktopAttributeMap attrs;
      if (!dpiSync.empty() && !clientDpi.empty()) {
         attrs.insert({"DPISYNC", dpiSync});
         attrs.insert({"CLIENTDPI", clientDpi});
      }

      if (!hasSSO) {
         desktop = getPrelaunchedDesktop();
         if (desktop && desktop->getStatus() == RUNNING) {
            LinuxAgent::lock _(mDesktopsLock);
            Log_Info("A prelaunched desktop found.\n");
            desktop->setDesktopId(desktopId);
            CommonUtils::SetDpiSync(desktop->getXDisplay(), clientDpi, dpiSync);
            CommonUtils::SetDesktopId(desktop->getXDisplay(), std::to_string(desktopId));
            addDesktop(desktop);
            removePrelaunchedDesktop(desktop);
            desktop->notifyDesktopReady();

            // A prelaunched desktop is consumed, prelaunch a new one if need.
            schedulePrelaunchDesktop();
            return true;
         } else {
            Log_Info("No prelaunched desktop found, launch one.\n");
            return prepareDesktop(desktopId, false, "", &attrs);
         }
      } else {
         if (prepareDesktop(desktopId, false, ssoType, &attrs)) {
            desktop = getDesktop(desktopId);
            if (desktop == nullptr) {
               Log_Info("%s: desktop %" FMT64 "u doesn't exist.\n", __FUNCTION__, desktopId);
               return false;
            }

            // Only password-sso credential comes from StartSession request.
            if (passCred) {
               return desktop->actionProgram(sRequest);
            } else {
               return true;
            }
         }
      }
   }

   return false;
}


/*
 *----------------------------------------------------------------------------
 *
 * DesktopInstanceMgr::OnDesktopReadyCb --
 *
 *   When desktop is ready, report it to view agent via IPC.
 *
 * Results:
 *   None.
 *
 * Side effects:
 *   None.
 *
 *----------------------------------------------------------------------------
 */

void
DesktopInstanceMgr::OnDesktopReadyCb(void *data) // IN
{
   uint64 desktopId = (uint64)data;
   DesktopInstanceMgr *manager = GetDesktopInstanceMgr();
   std::shared_ptr<DesktopInstance> desktop = manager->getDesktop(desktopId);
   if (desktop == NULL) {
      Log_Error("%s: The desktop %" FMT64 "u doesn't exist.\n", __FUNCTION__, desktopId);
      return;
   }

   std::string magicstr = CommonUtils::GetDesktopMagic();
   std::string ipcToken = CommonUtils::GenerateDesktopToken(std::to_string(desktopId), magicstr);

   IpcMsgDispatcher::NotifyDesktopReady(desktopId, desktop->getDesktopType(),
                                        desktop->getXDisplay(), desktop->getXAuthority(), ipcToken,
                                        desktop->getUserName());
}


/*
 *----------------------------------------------------------------------------
 *
 * DesktopInstanceMgr::OnDesktopErrorCb --
 *
 *   The function called when error occurs.
 *
 * Results:
 *   None.
 *
 * Side effects:
 *   None.
 *
 *----------------------------------------------------------------------------
 */

void
DesktopInstanceMgr::OnDesktopErrorCb(void *data) // IN
{
   uint64 desktopId = (uint64)data;
   DesktopInstanceMgr *manager = GetDesktopInstanceMgr();
   if (desktopId == 0) { // Prelaunched desktop
      manager->clearPrelaunchedDesktop();
   }

   std::shared_ptr<DesktopInstance> desktop = manager->getDesktop(desktopId);
   if (desktop != nullptr) {
      std::string display = desktop->getXDisplay();
      std::string userName = desktop->getUserName();
      manager->destroyDesktop(desktopId);
      std::thread(&DesktopInstanceMgr::postDesktopDestroyed, display, userName).detach();
   } else {
      Log_Warning("%s: The desktop %" FMT64 "u already destroyed.\n", __FUNCTION__, desktopId);
   }
}


/*
 *----------------------------------------------------------------------------
 *
 * DesktopInstanceMgr::postDesktopDestroyed --
 *
 *   Clean the rudimental system processes, sessions and files after
 *   desktop was destroyed.
 *
 * Results:
 *   None.
 *
 * Side effects:
 *   None.
 *
 *----------------------------------------------------------------------------
 */

void
DesktopInstanceMgr::postDesktopDestroyed(const std::string display,  // IN
                                         const std::string userName) // IN
{
   // Recycle X display from an escaped X Server.
   DesktopUtils::RecycleXDisplay();

   // Check whether the display manager avalid for XDMCP.
   DesktopInstanceMgr::checkDisplayManager();
   std::string params;

   if (!display.empty()) {
      params += " -d " + display;
   }

   if (!userName.empty()) {
      params += " -u " + userName;
   }
   std::string command = Desktop::DESKTOPD_POST_DESKTOP_LOGOUT_SCRIPT + params;
   CommonUtils::ExecShell(command);

   // Schedule to prelaunch XDMCP desktop if need.
   DesktopInstanceMgr::GetDesktopInstanceMgr()->schedulePrelaunchDesktop();
}


/*
 *----------------------------------------------------------------------------
 *
 * DesktopInstanceMgr::checkDisplayManager --
 *
 *   Check whether XDMCP is enabled for system display manager in case
 *   hznGreeter is disabled.
 *   If not, we should restart the display manager.
 *
 * Results:
 *   None.
 *
 * Side effects:
 *   The DesktopDaemon will exit after restart display manager.
 *
 *----------------------------------------------------------------------------
 */

void
DesktopInstanceMgr::checkDisplayManager()
{
   int result = 0;
   DesktopInstanceMgr *manager = GetDesktopInstanceMgr();

   assert(manager != NULL);

   if (DesktopUtils::IsHznGreeterEnabled()) {
      return;
   }

   result = system("/usr/lib/omnissa/viewagent/bin/XdmcpIsReady.sh");
   if (WIFEXITED(result) && WEXITSTATUS(result) == 1) {
      bool expected = false;
      if (manager->mDpyMgrRestarted.compare_exchange_strong(expected, true)) {
         Log_Info("%s: Begin to restart desktop manager.\n", __FUNCTION__);
         CommonUtils::ExecShell(Desktop::DESKTOPD_RESET_DISPLAY_MANAGER_SCRIPT);
         Log_Info("%s: End to restart desktop manager.\n", __FUNCTION__);

         Log_Info("%s: DesktopDaemon exit since restart desktop manager.\n", __FUNCTION__);
         unregisterIpcService();
      } else {
         Log_Info("DesktopDaemon already restarted display manager.\n");
      }
   }
}

} // namespace Desktop
