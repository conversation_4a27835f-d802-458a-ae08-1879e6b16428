[ req ]
default_bits       = 4096
prompt             = no
default_md         = sha256
distinguished_name = dn
req_extensions     = v3_intermediate_ca

[ dn ]
CN = Omnissa Revoke Intermediate A

[ v3_intermediate_ca ]
subjectKeyIdentifier=hash
basicConstraints = critical,CA:true,pathlen:0
keyUsage = critical, digitalSignature, cRLSign, keyCertSign
crlDistributionPoints = URI:http://horizon-core-agent-crl2.com:8886/hzagent-2.crl
