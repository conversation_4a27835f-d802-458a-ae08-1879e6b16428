/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

#include "stdafx.h"

#include "utilWinHttpRequest.h"
#include "utilCertificates.h"


#define ROOT_CERT_STORE_NAME "Root"


/*
 *--------------------------------------------------------------------------
 *
 * WinhttpTestEnvironment --
 *
 *    Set up required to get all the tests to run properly.
 *
 *    As of this writing, most of these tests require:
 *       1. Clear the CRL cache of the system (before and after the tests run)
 *       2. Modifications to the hosts file to redirect requests to localhost
 *       3. A server to be running on localhost that can respond to requests
 *       4. Install one of the tests certificates in the root certificate store
 *
 *    There are more requirements for individual tests, but those are taken care of by the
 *    individual tests themselves.
 *
 *--------------------------------------------------------------------------
 */

class WinhttpTestEnvironment : public Environment {
public:
   WinhttpTestEnvironment() : mServerProcess(), mFileHandle(NULL) {}
   ~WinhttpTestEnvironment() {}

   void SetUp() override
   {
      ClearCRLCache(L"startup");

      WinFile hostsFile(GetHostsFilePath());
      mHostsBackupContents = hostsFile.ReadFileIntoString(false);
      if (mHostsBackupContents.empty()) {
         GTEST_LOG_(WARNING) << "Unable to read hosts file";
      }
      hostsFile.WriteStringToFile(mHostsBackupContents + mHostsFileEntries, false);

      SSL_Init(NULL, NULL, NULL);

#ifndef DONT_START_SERVER
      memset(&mServerProcess, 0, sizeof(mServerProcess));

      std::wstring startServerCmd(L"python -u http-server.py");
      auto serverLogPath = mWorkingDir + L"\\server.log";
      ProcUtil::LaunchProcessWithStdOut(startServerCmd, serverLogPath, mServerProcess, mFileHandle,
                                        false);
#endif
      /*
       * WinHTTP has a limitation where it won't perform a CRL request if the root is not in the
       * root certificate store. So we've no choice but to add a test certificate to the root
       * certificate store.
       */
      std::wstring crlRootPath = mWorkingDir + L"\\revoke-root.crt";
      WinFile crlRootFile(crlRootPath);
      auto crlRootPEM = crlRootFile.ReadFileIntoString();
      WinCertImplementation winCertImpl;
      auto certStore = winCertImpl.GetCertificateStore(ROOT_CERT_STORE_NAME);
      std::string empty;
      if (!winCertImpl.ImportCertificateToStore(certStore, crlRootPEM, mRootFriendlyName, empty,
                                                empty)) {
         SYSMSG_FUNC(Error, L"Failed to import certificate needed for testing into the root store");
      }
   }

   void TearDown() override
   {
      WinCertImplementation winCertImpl;
      auto certStore = winCertImpl.GetCertificateStore(ROOT_CERT_STORE_NAME);
      winCertImpl.DeleteCertificates(certStore, mRootFriendlyName);

#ifndef DONT_START_SERVER
      ProcUtil::CloseProcess(mServerProcess);
      if (mFileHandle != NULL) {
         CloseHandle(mFileHandle);
      }
#endif
      SSL_Exit();

      WinFile hostsFile(GetHostsFilePath());
      if (!hostsFile.WriteStringToFile(mHostsBackupContents, false)) {
         GTEST_LOG_(WARNING) << "Unable to restore hosts file";
      }

      ClearCRLCache(L"teardown");
   }

protected:
   /*
    * ClearCRLCache --
    *
    *    Clear the CRL cache. This is needed to ensure that the CRL is re-fetched.
    *
    * Results:
    *    True if the process started successfully, false otherwise.
    *
    * Side effects:
    *    None.
    *
    */

   bool ClearCRLCache(const std::wstring &testName)
   {
      // Clear the CRL cache (NOTE, this should be done at the start and END of the test)
      std::wstring clearCrlCmd(L"certutil -urlcache crl delete");
      auto clearCrlStdOutPath = mWorkingDir + L"\\" + testName + L"-clearcrl.log";
      return ProcUtil::LaunchProcessWithStdOutAndWait(clearCrlCmd, clearCrlStdOutPath, false,
                                                      10000);
   }


   /*
    * GetHostsFilePath --
    *
    *    Gets the hosts file path.
    *
    * Results:
    *    The file path
    *
    * Side effects:
    *    None.
    *
    */

   std::wstring GetHostsFilePath()
   {
      wchar_t hostsPath[MAX_PATH];
      if (GetSystemWindowsDirectoryW(hostsPath, MAX_PATH) == 0) {
         SYSMSG_FUNC(Error, L"Failed to get system directory");
         return L"";
      }
      std::wstring hostsFilePath = std::wstring(hostsPath) + L"\\System32\\drivers\\etc\\hosts";
      return hostsFilePath;
   }

private:
   PROCESS_INFORMATION mServerProcess;
   HANDLE mFileHandle;
   std::wstring mWorkingDir = std::filesystem::current_path().wstring();
   std::string mRootFriendlyName = "Omnissa Horizon Root Test CA";

   std::string mHostsFileEntries = "\n"
                                   "127.0.0.1 horizon-core-agent-test.com\n"
                                   "127.0.0.1 horizon-core-agent-exp.com\n"
                                   "127.0.0.1 horizon-core-agent-crl1.com\n"
                                   "127.0.0.1 horizon-core-agent-crl2.com\n"
                                   "127.0.0.1 horizon-core-agent-crl3.com\n"
                                   "127.0.0.1 hzagent-leafA.com\n"
                                   "127.0.0.1 hzagent-leafB.com\n"
                                   "127.0.0.1 hzagent-leafC.com\n";

   std::string mHostsBackupContents;
};


int
main(int argc, char *argv[])
{
   ::InitGoogleTest(&argc, argv);
   ::InitGoogleMock(&argc, argv);
   AddGlobalTestEnvironment(new WinhttpTestEnvironment());
   return RUN_ALL_TESTS();
}
