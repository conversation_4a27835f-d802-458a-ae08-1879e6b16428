import http.server
import socketserver
import socket
import select
import urllib.request

class Proxy(http.server.BaseHTTPRequestHandler):
    def do_CONNECT(self):
        # Parse host and port
        host, sep, port = self.path.partition(':')
        port = int(port) if port else 443
        try:
            # Connect to the target server
            with socket.create_connection((host, port)) as remote:
                self.send_response(200, "Connection Established")
                self.end_headers()
                # Tunnel data between client and remote
                conns = [self.connection, remote]
                while True:
                    rlist, _, _ = select.select(conns, [], [], 0.1)
                    if self.connection in rlist:
                        data = self.connection.recv(8192)
                        if not data:
                            break
                        remote.sendall(data)
                    if remote in rlist:
                        data = remote.recv(8192)
                        if not data:
                            break
                        self.connection.sendall(data)
        except (ConnectionResetError, BrokenPipeError):
            # Client closed the connection, just exit quietly
            return
        except Exception as e:
            self.send_error(502, f"CONNECT error: {e}")

    def do_GET(self):
        try:
            url = self.path
            if not url.startswith('http'):
                url = f'http://{self.headers["Host"]}{self.path}'
            print(f"Proxying GET to: {url}")
            req = urllib.request.Request(url, headers=self.headers)
            with urllib.request.urlopen(req) as res:
                self.send_response(res.status)
                for key, value in res.getheaders():
                    self.send_header(key, value)
                self.end_headers()
                self.wfile.write(res.read())
        except Exception as e:
            self.send_error(502, f"Proxy error: {e}")

    def do_POST(self):
        try:
            url = self.path
            if not url.startswith('http'):
                url = f'http://{self.headers["Host"]}{self.path}'
            print(f"Proxying POST to: {url}")
            content_length = int(self.headers.get('Content-Length', 0))
            post_data = self.rfile.read(content_length)
            req = urllib.request.Request(url, data=post_data, headers=self.headers, method='POST')
            with urllib.request.urlopen(req) as res:
                self.send_response(res.status)
                for key, value in res.getheaders():
                    self.send_header(key, value)
                self.end_headers()
                self.wfile.write(res.read())
        except Exception as e:
            self.send_error(502, f"Proxy error: {e}")

if __name__ == "__main__":
    PORT = 8889
    with socketserver.ThreadingTCPServer(("", PORT), Proxy) as httpd:
        print(f"Serving proxy (HTTP and HTTPS) on port {PORT}")
        httpd.serve_forever()