name: hclin
run-name: >
  ${{ github.workflow }}
  ${{ github.event_name == 'workflow_dispatch' && inputs.buildtype ||
      (github.event_name == 'pull_request' && 'obj' || 'beta') }}
  ${{ github.event_name == 'pull_request' &&
      format(' - {0} (#{1})', github.event.pull_request.title, github.event.pull_request.number)
      || '' }}
concurrency:
  # This section ensure that multiple PR pushes will cancel superseded builds.
  # Builds on main, release/* and feature/* branches will not be canceled to
  # assist in root causing build breakages.
  group: ${{ github.workflow }}-${{
      (github.ref == 'refs/heads/main' ||
       startsWith(github.ref, 'refs/heads/feature/') ||
       startsWith(github.ref, 'refs/heads/release/')) &&
      github.run_id || github.ref
    }}-${{ inputs.buildtype }}
  cancel-in-progress: true
on:
  pull_request:
  push:
    branches:
      - 'main'
      - 'release/**'
      - 'feature/**'
    paths-ignore:
      - .github/RunnerResetConfig.json
      - .github/workflows/runner_app_config.yaml
      - .github/workflows/rx-devop-nightly-*.yaml
  workflow_dispatch:
    inputs:
      buildtype:
        type: choice
        description: Build type
        required: True
        default: beta
        options:
          - beta
          - obj
          - opt
          - release
      conan_develop:
        type: boolean
        description: I am testing conan packages and need to enable the conan-develop remote
        required: True
        default: false
      conan_sandbox:
        type: boolean
        description: I am testing conan compiler upgrade and need to enable the conan-sandbox remote
        required: false
        default: false

env:
  BUILDTYPE: ${{ github.event_name == 'workflow_dispatch' && inputs.buildtype ||
                 (github.event_name == 'pull_request' && 'obj' || 'beta') }}

jobs:
  file-check:
    runs-on: [lnxbuild-gh, self-hosted]
    permissions:
      contents: read
      pull-requests: read
    outputs:
      enable-build: ${{ steps.filter.outputs.result }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          sparse-checkout: .github/workflow-filters.yaml
          sparse-checkout-cone-mode: false

      - name: Check if build should be run
        id: filter
        uses: euc-eng/filter-paths@v1
        with:
          filtersFile: .github/workflow-filters.yaml
          label: hclin

  build-hclin:
    needs: file-check
    if: ${{ needs.file-check.outputs.enable-build == 'true' }}
    permissions:
      actions: read
      contents: read
      pull-requests: read
    runs-on: [lnxbuild-gh, self-hosted]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          lfs: true

      - name: Run SCons
        uses: ./.github/actions/scons
        with:
          buildtype: ${{ github.event_name == 'pull_request' && 'obj' || inputs.buildtype }}
          product: hccrt
          extraParams: >
            FOSSA_DEPS=1 COMPILE_DB=1 compiledb hccrt
            ${{ github.event_name == 'pull_request' && 'ENABLE_CODE_COV=1' || '' }}
          conanDevelop: ${{ inputs.conan_develop }}
          conanSandbox: ${{ inputs.conan_sandbox }}
          artifactoryApiToken: ${{ secrets.ARTIFACTORY_GITHUB_DELIVERABLES_API_TOKEN }}
          artifactoryUrl: ${{ vars.ARTIFACTORY_GITHUB_DELIVERABLES_URL }}
          linSigningKeyId: ${{ vars.ORG_OMNISSA_LINUX_GPG_SIGNING_KEY_ID }}
          linSigningKeyPath: ${{ vars.ORG_OMNISSA_LINUX_GPG_SIGNING_DIR }}
          linSigningKeyPassword: ${{ secrets.ORG_OMNISSA_LINUX_GPG_SIGNING_KEY_PASSWORD }}
          artifactoryReadToken: ${{ secrets.ORG_OMNISSA_ART_READONLY_TOKEN }}
          artifactoryReadUser: ${{ vars.ORG_OMNISSA_ART_READONLY_USER }}

      - name: Run Fossa
        if: ${{ inputs.buildtype == 'release' || github.event_name == 'pull_request' }}
        uses: ./.github/actions/fossa
        with:
          product: 'hclin'
          fossa-api-key: ${{ secrets.ORG_OMNISSA_FOSSA_KEY }}
          omnissaArtifactoryToken: ${{ secrets.ORG_OMNISSA_ART_READONLY_TOKEN }}

  UT:
    needs: build-hclin
    if: ${{ vars.ENABLE_UNIT_TEST_HOSTED_IN_BJ_LIN == 'true' }}
    secrets: inherit
    uses: ./.github/workflows/hclin_ut.yaml
    with:
      buildtype: ${{ github.event_name == 'pull_request' && 'obj' || inputs.buildtype }}
      useGcov: ${{ github.event_name == 'pull_request' }}

  sonar-upload:
    runs-on: [lnxbuild-gh, self-hosted]
    needs:
      - build-hclin
      - UT
    # Disable sonar because sonar can't work with remote cache enabled on hclin now.
    if: false
    # we want to run sonar if dependent test jobs are successful or skipped, but
    # there's no function for skipped() so we use !failure() and !cancelled()
    #if: ${{ !failure() && !cancelled() &&
    #        needs.build-hclin.result == 'success' }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Upload results to SonarQube
        uses: ./.github/actions/testframework/cart-sonar
        with:
          product: hccrt
          sonarProjectKey: cart-hclin
          productWorkflowJob: build-hclin
          utJobs: 'ar-*,s4d-*,client-*,rx-*,vd-*'
          artifactoryApiToken: ${{ secrets.ARTIFACTORY_GITHUB_DELIVERABLES_API_TOKEN }}
          artifactoryBaseUrl: ${{ vars.ARTIFACTORY_GITHUB_DELIVERABLES_URL }}
          artifactoryReadToken: ${{ secrets.ORG_OMNISSA_ART_READONLY_TOKEN }}
          artifactoryReadUser: ${{ vars.ORG_OMNISSA_ART_READONLY_USER }}
          languageMode: C++
          compileCommands: compile_commands.json
          sources: >
            bora/apps/crtbora/common,
            bora/apps/crtbora/linux,
            bora/apps/docker,
            bora/apps/lib/cui,
            bora/apps/lib/dui,
            bora/apps/lib/lui/unity,
            bora/apps/lib/mksCtrlxx,
            bora/apps/lib/objc/vmAppKit,
            bora/apps/lib/objc/vmFoundation,
            bora/apps/lib/hznpclient,
            bora/apps/lib/hznprotect,
            bora/apps/lib/wui/unity,
            bora/apps/printRedir/apiHelper,
            bora/apps/printRedir/common,
            bora/apps/printRedir/LBP,
            bora/apps/printRedir/libs,
            bora/apps/printRedir/print,
            bora/apps/printRedir/redirectionClient,
            bora/apps/printRedir/redirectionService,
            bora/apps/printRedir/transport,
            bora/apps/rde/apptap/src,
            bora/apps/rde/fido2/client,
            bora/apps/rde/fido2/common,
            bora/apps/rde/hznime,
            bora/apps/rde/mksvchan,
            bora/apps/rde/rdeSvc/client/base,
            bora/apps/rde/rdeSvc/client/linux,
            bora/apps/rde/rdeSvc/client/posix,
            bora/apps/rde/rdeSvc/shared,
            bora/apps/rde/rds/audio,
            bora/apps/rde/rds/util,
            bora/apps/rde/rtav/apps/viewMMDevRedir,
            bora/apps/rde/rtav/driver,
            bora/apps/rde/rtav/libs,
            bora/apps/rde/scredirvchan/client,
            bora/apps/rde/sdoSensor/sdoDriver,
            bora/apps/rde/tsdr,
            bora/apps/rde/unityPlugin,
            bora/apps/rde/unityShell,
            bora/apps/rde/urlRedirection,
            bora/apps/rde/usbRedirection/client,
            bora/apps/rde/usbRedirection/common,
            bora/apps/rde/viewClient,
            bora/apps/viewusb/framework/usb,
            bora/lib/kbdlayoutid,
            bora/modules/hznprotect,
            bora/modules/hznprotect-stub,
            bora-vmsoft/hznbus,
            bora-vmsoft/hznflstor,
            bora-vmsoft/hznufhid,
            bora-vmsoft/hznvaudioin,
            bora-vmsoft/hznvhub,
            bora-vmsoft/lib/appUtil,
            bora-vmsoft/lib/caretUtil,
            bora-vmsoft/lib/ghIntegration,
            bora-vmsoft/lib/metroUtils,
            horizonclient/view/linuxClient,
            horizonclient/view/openClient/lib/cdk,
            horizonclient/view/openClient/lib/controllerAPI,
            horizonclient/view/openClient/lib/httpListener,
            horizonclient/view/openClient/lib/libview,
            horizonclient/view/openClient/lib/udpProxyImpl,
            horizonclient/view/openClient/sdk/internal,
            horizonclient/view/openClient/sdk/semi-public,
            horizonclient/view/openClient/tunnel,
          exclusions: 'horizonclient/view/openClient/lib/cdk/android'
          buildtype: ${{ env.BUILDTYPE }}
          sonarHostUrl: ${{ vars.ORG_EUC_SONAR_HOST }}
          sonarToken: ${{ secrets.ORG_EUC_SONAR_TOKEN }}
          conanDevelop: ${{ inputs.conan_develop }}
          conanSandbox: ${{ inputs.conan_sandbox }}

  hclin-overall-status:
    needs:
      - build-hclin
      - UT
      - sonar-upload
    if: ${{ !cancelled() }}
    timeout-minutes: 10
    runs-on: [lnxbuild-gh, self-hosted]
    permissions:
      actions: write
      contents: read
      pull-requests: read
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Check overall workflow status
        uses: ./.github/actions/check-status
        with:
          workflowId: 'hclin.yaml'
          jobs: ${{ toJson(needs) }}
          buildtype: ${{ inputs.buildtype }}
          slackWebhookUrl: ${{ secrets.CART_SLACK_WEBHOOK_URL }}
          dashboardUrl: ${{ vars.CART_BUILD_DASHBOARD }}
          slackBranches: ${{ vars.DAILY_BUILD_BRANCHES }}
