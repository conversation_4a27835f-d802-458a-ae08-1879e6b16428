#!/usr/bin/env python3

# Copyright (c) Omnissa, LLC. All rights reserved.
# This product is protected by copyright and intellectual property laws in the
# United States and other countries as well as by international treaties.
# -- Omnissa Restricted

import functools
import logging
import os
import platform
import shutil
import subprocess
from pathlib import Path
from time import localtime, sleep, strftime

def get_logger(name="router"):
    """Create a logger and return it
    """
    logger = logging.getLogger(name)
    logger.setLevel(logging.INFO)

    # Check if directory log is exist, create it if not
    logPath = os.getcwd() + '/log'
    if not os.path.exists(logPath):
        try:
            os.makedirs(logPath)
        except Exception:
            # Cannot output any error before log module initialized
            pass

    log_path = os.path.abspath("log/{}.log".format(name))

    for h in logger.handlers:
        if log_path == h.baseFilename:
            return logger

    # Create the logging file handler
    fh = logging.FileHandler(log_path)

    fmt = '%(asctime)s - %(levelname)s - %(message)s'
    formatter = logging.Formatter(fmt)
    fh.setFormatter(formatter)

    # Add handler to logger object
    logger.addHandler(fh)
    return logger


logger = get_logger()

def GetCmdOutput(cmd, printOut=True, cwd=None):
    logger.debug('GetCmdOutput: %s' % cmd)
    if cwd is None:
        output, err = subprocess.Popen(cmd,
                                       stdout=subprocess.PIPE,
                                       stderr=subprocess.PIPE,
                                       shell=True).communicate()
    else:
        output, err = subprocess.Popen(cmd,
                                       cwd=cwd,
                                       stdout=subprocess.PIPE,
                                       stderr=subprocess.PIPE,
                                       shell=True).communicate()
    if printOut:
        print('GetCmdOutput: ')
        print('-' * 40)
        print(cmd)
        print(output.decode(errors='replace'))
        print(err.decode(errors='replace'))
        print('-' * 40)
    if err.decode(errors='replace') == '':
        ret = output.decode(errors='replace')
    else:
        ret = err.decode(errors='replace')

    return ret

def exception(function):
    """Exception decorator
    """
    @functools.wraps(function)
    def wrapper(*args, **kwargs):
        try:
            return function(*args, **kwargs)
        except Exception as e:
            # log the exception
            logger.exception("Exception %s happens at %s" %
                             (str(e), function.__name__))

    return wrapper

def getDisplay(user):
    """Get the X display of the user.
    """
    if not user:
        logger.error('User is required.')
        return None

    extract_dpy = "PROCPS_USERLEN=32 PROCPS_FROMLEN=32 w -h {u} | " + \
                  "grep desktopWorker | awk '{{print substr($3, 2)}}'"

    output = GetCmdOutput(extract_dpy.format(u=user), False).strip()
    if not output or len(output) > 3:
        cmd = "PROCPS_USERLEN=32 PROCPS_FROMLEN=32 w -h %s" % user
        logger.error(GetCmdOutput(cmd, True).strip())

    return output[-3:] if len(output) > 3 else output

def getDesktopId(display):
    """Get desktop id for given display.
    """
    desktop_id = -1
    if not display:
        logger.error('Display is required')
        return desktop_id

    file = "/var/omnissa/hzn_desktop/hzn_desktopid." + display
    try:
        with open(file, 'r') as f:
            for line in f:
                if "DESKTOPID=" in line:
                    content = line.strip()
                    desktop_id = content[content.index('=') + 1:]
                    break
    except Exception:
        logger.error('Exception in getDesktopId')
        return desktop_id

    return desktop_id

def screenshot(user=None, output='.', display=None):
    """Dump the screenshot for the given display/user to the output.

    Prerequisite
    ----------
    Command xwd and convert must be installed from x11-apps and imagemagick.

    Parameters
    ----------
    display: The X display name or number
    user:    username
    output:  Screenshot's output directory or filename

    Returns:
    ----------
    None
    """

    # Log the parameters
    logger.info('{f}{a}'.format(f=screenshot.__name__, a=locals()))

    # Extract the X display via user if display not given
    if not display:
        display = getDisplay(user)

    if not display:
        logger.error('Failed to extract display.')
        return

    # X display verification
    display = display if display.startswith(':') else ':{}'.format(display)
    xauth = '/var/omnissa/viewagent/xauth/.xauth{}'.format(display)
    if not os.path.exists(xauth):
        logger.error("There's no XAUTH for {}.".format(display))
        return

    # Check the output path
    output = os.path.abspath(output)
    if os.path.isdir(output):
        output = os.path.join(
            output, '{u}{d}@{t}.jpg'.format(u=user if user else '',
                                            d=display,
                                            t=strftime("%m-%d_%H-%M-%S",
                                                       localtime())))

    # Screenshot and convert to jpeg
    cmd = 'XAUTHORITY={a} xwd -display {d} -root -nobdrs | convert xwd:- jpg:- > {o}'
    GetCmdOutput(cmd.format(a=xauth, d=display, o=output))

def controlDesktop(username, action):
    """Lock/Unlock/Logout desktop
       action: lock, unlock, logout
    """
    command = "common/scripts/actions.sh -u %s -a %s" % (username, action)
    os.system(command)

def getLockStatus(username):
    """Get desktop lock status
    """
    content = ""
    file = "/var/omnissa/viewagent/screen/screenLockStatus:{}".format(
        getDisplay(username))
    try:
        with open(file, 'r') as f:
            content = f.read().strip()
    except Exception as e:
        logger.error("Exception: {}".format(e))
        logger.error("Failed to get lock status from {}".format(file))

    return "locked" if content == "screen locked" else "unlocked"

def startXServer(display=':105'):
    """Start a display by calling StartXServer.sh
    """
    xauthdir = "/var/omnissa/viewagent/xauth/"
    createTestDirIfNotExist(xauthdir)
    xauthpath = xauthdir + ".xauth" + display
    startXServerCmd = "XAUTHORITY=" + xauthpath + \
                      " /usr/lib/omnissa/viewagent/bin/StartXServer.sh -d " + display

    startXServer_process = subprocess.Popen(startXServerCmd,
                             shell=True,
                             stdout=subprocess.PIPE,
                             stderr=subprocess.PIPE,
                             close_fds=True,
                             preexec_fn=os.setsid)
    sleep(2)
    return startXServer_process

def stopXorg(display):
    """kill a xorg process by finding xorg process with given display
    """
    command = "ps -ef |grep Xorg | " + \
              "grep " + display + \
              " | grep -v grep | awk '{print $2}'"
    xorg_pid = runCommand(command)

    if not xorg_pid == '':
        xorg_pid = xorg_pid.replace('\n', ' ')
        command = 'kill -9 ' + xorg_pid + ' > /dev/null 2>&1'
        runCommand(command)
        sleep(1)

def stopXServer(display=':105'):
    """Stop a display by finding StartXServer.sh process with given display
       Killing the process and all of its descendants
    """
    command = "ps -ef |grep StartXServer | " + \
              "grep " + display + \
              " | grep -v grep | awk '{print $2}'"
    xorg_pid = runCommand(command)

    if not xorg_pid == '':
        xorg_pid = xorg_pid.replace('\n', ' ')
        command = 'kill -9 -' + xorg_pid + ' > /dev/null 2>&1'
        runCommand(command)
        sleep(2)

    stopXorg(display)

def runCommand(command):
    run = os.popen(command)
    result = run.read()
    run.close()
    return result

# Get output error and returncode for a cmd
def GetOutput(cmd, printOut=True, cwd=None):
    logger.debug('GetOutput: %s' % cmd)
    if cwd is None:
        p = subprocess.Popen(cmd,
                             stdout=subprocess.PIPE,
                             stderr=subprocess.PIPE,
                             shell=True)
    else:
        p = subprocess.Popen(cmd,
                             cwd=cwd,
                             stdout=subprocess.PIPE,
                             stderr=subprocess.PIPE,
                             shell=True)
    output, err = p.communicate()
    p.wait()
    if printOut:
        print('GetOutput: ')
        print('-' * 40)
        print(cmd)
        print(output.decode(errors='replace'))
        print(err.decode(errors='replace'))
        print('-' * 40)
        print(p.returncode)

    return output.decode(errors='replace'), err.decode(errors='replace'), p.returncode

def createTestDirIfNotExist(dirpath):
    targetPath = Path(dirpath)
    if not targetPath.exists():
        targetPath.mkdir(parents=True, exist_ok=True)
        logger.debug("Create dir: %s" % dirpath)
        return True
    return False

def removeTestDir(dirpath):
    try:
        shutil.rmtree(dirpath)
        logger.debug("Remove dir: %s" % dirpath)
    except OSError as e:
        logger.error("Failed to remove: %s, error: %s" %(dirpath, e.strerror))

def removeTestFile(filepath):
    try:
        if os.path.isfile(filepath):
            os.remove(filepath)
    except OSError as e:
        logger.error("Failed to delete: %s, error: %s" %(filepath, e.strerror))

def waitUntilAgentActive(timeout):
    """
    Wait untill service viewagent is active
    @Input:
    -timeout, wait time in seconds
    """
    cmd = 'systemctl is-active viewagent'
    for x in range(timeout + 1):
        out, err, rc = GetOutput(cmd, printOut=False)
        out = out.strip()
        if (rc == 0 and out == 'active'):
            return True
        sleep(1)
    return False
