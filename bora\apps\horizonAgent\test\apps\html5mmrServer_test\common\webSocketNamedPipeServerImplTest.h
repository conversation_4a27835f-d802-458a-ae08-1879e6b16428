/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

#pragma once

class WebSocketNamedPipeServerImplTest : public TestBase {
public:
   static void SetUpTestCase();
   static void TearDownTestCase();

protected:
   std::shared_ptr<MockWebSocketNamedPipeServerImpl> mockNamedPipeServer;
   std::string mockPipeName = "testPipeName";
   std::string mockServerName = "testServer";
   DWORD mockProcessId = 1111;
   DWORD mockSessionId = 2222;
   void SetUp() override;
   void TearDown() override;
};
