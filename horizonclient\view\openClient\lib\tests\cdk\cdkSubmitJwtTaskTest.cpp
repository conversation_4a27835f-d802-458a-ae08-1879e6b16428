/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * cdkSubmitJwtTaskTest.cpp --
 *
 *      Implement unit test cases to verify functions of CdkSubmitJwtTask.
 */

#include "cdkAuthenticationTask.h"
#include "cdkPromptAuthInfoTask.h"
#include "cdkSubmitAuthInfoTask.h"
#include "cdkSubmitJwtTask.h"
#include "cdkSubmitJwtTaskTest.h"
#include "cdkXml.h"
#include "rxUTLog.h"
#include <iostream>


#define TEST_JWT_TOKEN "tokenxxxxxx"


/*
 *-----------------------------------------------------------------------------
 *
 * CdkSubmitJwtTaskTest::SetUp --
 *
 *      SetUp for the each test case.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

void
CdkSubmitJwtTaskTest::SetUp()
{
   UTConsoleLog("%s", __FUNCTION__);

   mRootTask = CdkTaskFixture::CreateRootTask();
   mSubmitJwtTask = std::make_shared<CdkTaskFixture>();
   mSubmitJwtTask->Create(mRootTask.get(), nullptr, CDK_TASK_SUBMIT_JWT, 0, nullptr);
}


/*
 *-----------------------------------------------------------------------------
 *
 * @test CdkSubmitJwtTaskTest::SetParamsTest
 *
 *      Test CdkSubmitJwtTask_SetParams, it should make the prompt task to be
 *      be done.
 *
 * Results:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CdkSubmitJwtTaskTest, SetParamsTest)
{
   ASSERT_TRUE(mSubmitJwtTask != nullptr);
   CdkSubmitAuthInfoTask_SetParams(mSubmitJwtTask->GetTask(), nullptr, "https://test.broker.com",
                                   nullptr);
   CdkTask *promptTask =
      CdkTask_FindTask(mRootTask->GetTask(), CDK_TASK_PROMPT_AUTH_INFO, 0, nullptr);

   ASSERT_TRUE(promptTask != NULL);
   EXPECT_TRUE(promptTask->state == CDK_TASK_STATE_DONE);
}


/*
 *-----------------------------------------------------------------------------
 *
 * @test CdkSubmitJwtTaskTest::CreateRequestTest
 *
 *      Test CdkSubmitJwtTask_CreateRequest, it should include the expected JWT
 *      token in the request.
 *
 * Results:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CdkSubmitJwtTaskTest, CreateRequestTest)
{
   ASSERT_TRUE(mSubmitJwtTask != nullptr);

   // Create authentication task to hold the JWT token
   CdkTask *authTask =
      CdkTask_FindOrRequestTask(mRootTask->GetTask(), CDK_TASK_AUTHENTICATION, NULL, 0, nullptr);
   ASSERT_TRUE(authTask != NULL);

   // Set the JWT token on the authentication task
   CdkTask_SetString(authTask, AUTH_HOME_SITE_REDIRECT_TOKEN, TEST_JWT_TOKEN);

   // Create prompt task and set its state to done
   CdkTask *promptTask =
      CdkTask_FindOrRequestTask(mRootTask->GetTask(), CDK_TASK_PROMPT_AUTH_INFO, NULL, 0, nullptr);
   ASSERT_TRUE(promptTask != NULL);
   promptTask->state = CDK_TASK_STATE_DONE;

   // Generate XML request
   auto request = CdkRpcTask_CreateRequest(mSubmitJwtTask->GetTask());
   ASSERT_TRUE(request != NULL);

   // Verify the root element is "do-submit-authentication"
   ASSERT_STREQ((const char *)request->name, "do-submit-authentication");

   // Get screen element which is a child of the root element
   auto screen = CdkXml_GetChild(request, "screen");
   ASSERT_TRUE(screen != NULL);

   // Validate the screen name is "jwt"
   auto authType = CdkXml_GetChildString(screen, "name");
   ASSERT_TRUE(authType != NULL);
   EXPECT_STREQ(authType, "jwt");

   // Validate the parameters section
   auto params = CdkXml_GetChild(screen, "params");
   ASSERT_TRUE(params != NULL);

   // Validate the token parameter
   auto token = CdkXml_GetParamString(params, "token");
   ASSERT_TRUE(token != NULL);
   EXPECT_STREQ(token, TEST_JWT_TOKEN);

   // Verify that the token was removed from the auth task
   EXPECT_TRUE(CdkTask_GetString(authTask, AUTH_HOME_SITE_REDIRECT_TOKEN) == nullptr);

   // Free the XML request
   xmlFreeNode(request);
   // CdkTask_Unref will take care of releasing authTask and promptTask
}
