<?xml version="1.0" encoding="UTF-8"?>

<Include>
   <Dialog Id="GoldenImage" Width="374" Height="266" NoMinimize="yes" Title="[ProductName]">
      <Control Id="Next" Type="PushButton" X="230" Y="243" Width="66" Height="17" Text="!(loc.IDS_NEXT)" Default="yes" />
      <Control Id="Cancel" Type="PushButton" X="301" Y="243" Width="66" Height="17" Cancel="yes" Text="!(loc.IDS_CANCEL)" />
      <Control Id="Banner" Type="Bitmap" X="0" Y="0" Width="374" Height="44" TabSkip="yes" Text="dialogBanner" />
      <Control Id="BannerLine" Type="Line" X="0" Y="44" Width="374" Height="0" />
      <Control Id="DlgDesc" Type="Text" X="21" Y="23" Width="292" Height="25" Transparent="yes" Text="!(loc.GoldenImage_DlgDesc)" />
      <Control Id="DlgLine" Type="Line" X="4" Y="234" Width="366" Height="0" />
      <Control Id="DlgTitle" Type="Text" X="13" Y="6" Width="292" Height="25" Transparent="yes" Text="!(loc.GoldenImage_DlgTitle)" />
      <Control Id="GoldenImageCheckBox" Type="CheckBox" X="20" Y="65" Width="300" Height="12" Property="GOLDEN_IMAGE_INSTALL" CheckBoxValue="1" Text="!(loc.GoldenImage_CheckBoxText)" />
      <Control Id="Back" Type="PushButton" X="164" Y="243" Width="66" Height="17" Text="!(loc.IDS_BACK)" />
   </Dialog>
</Include>
