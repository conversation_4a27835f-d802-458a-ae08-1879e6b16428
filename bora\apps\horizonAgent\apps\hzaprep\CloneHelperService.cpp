/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * Clone Helper Service
 */

#include "stdafx.h"
#include "CloneHelperService.h"

static CloneHelperService *gTheCloneService = NULL;

/*
 *-----------------------------------------------------------------------------
 *
 * initCloneHelperService --
 *
 *    Creates the clone helper service.
 *
 * Results:
 *    True on success.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */
bool
initCloneHelperService()
{
   if (gTheCloneService == NULL) {
      gTheCloneService = new CloneHelperService;
      if (!gTheCloneService->startEx2(TEXT("CloneHelper"), TEXT("The Clone Helper Queue"),
                                      gTheCloneService->createOperationInstance,
                                      gTheCloneService)) {
         delete gTheCloneService;
         gTheCloneService = NULL;
      }
   }
   if (gTheCloneService != NULL) {
      SYSMSG_FUNC(Debug, L"CloneHelper service started");
   } else {
      SYSMSG_FUNC(Error, L"Failed to start CloneHelper service");
   }
   return (gTheCloneService != NULL);
}

/*
 *-----------------------------------------------------------------------------
 *
 * CloneHelperService::prepClone --
 *
 *    Process prepare clone mfw message.
 *
 * Results:
 *    Respond to mfw client with an Ack on success.  Else respond with error message
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */
bool
CloneHelperService::prepClone(PropertyBag &params, PropertyBag &response)
{
   SYSMSG_FUNC(Debug, L"PrepClone msg received by Clone Helper service");
   wstr errResponse = L"Failed to process prepare clone message";
   wstr sigData;
   wstr poolID = mSvcPrep.readRegPoolID();
   if (poolID.empty()) {
      wstr errMsg = L"PoolID is not available";
      SYSMSG_FUNC(Error, L"%s", errMsg);
      response.setError(L"Invalid internal template image");
      return false;
   }
   params.add(PAR_POOL_ID, poolID);
   sigData.append(poolID);

   wstr adSrvName = params.get(PAR_AD_SERVER_NAME, L""), adSite = params.get(PAR_AD_SITE, L"");
   if (adSrvName.empty() || adSite.empty()) {
      wstr errMsg = L"Missing required information in params";
      SYSMSG_FUNC(Error, L"%s", errMsg);
      response.setError(errMsg);
      return false;
   }
   sigData.append(adSrvName);
   sigData.append(adSite);

   wstr hostName = mSvcPrep.getHostName();
   if (hostName.empty()) {
      wstr errMsg = L"Failed to discover machine name";
      SYSMSG_FUNC(Error, L"%s", errMsg);
      response.setError(errResponse);
      return false;
   }
   params.add(PAR_HOST_NAME, hostName);
   sigData.append(hostName);

   wstr macAddr = mSvcPrep.getMacAddr();
   if (macAddr.empty()) {
      wstr errMsg(TEXT("Failed to discover MAC address"));
      SYSMSG_FUNC(Error, TEXT("%s"), errMsg);
      response.setError(errResponse);
      return false;
   }
   params.add(PAR_HOST_MAC_ADDR, macAddr);
   sigData.append(macAddr);

   MsgBinary nonce;
   if (!mSvcPrep.generateNonce(VAL_NONCE_SIZE, nonce)) {
      wstr errMsg = L"Failed to generate nonce";
      SYSMSG_FUNC(Error, L"%s", errMsg);
      response.setError(errResponse);
      return false;
   }
   params.addBinary(PAR_NONCE, nonce.pBinary, nonce.sBinary);
   sigData.setBinary(nonce.pBinary, nonce.sBinary);
   // Get PHs public key to sign the data
   MsgBinary phsPubKey, phsPoPBlob;
   if (!mSvcPrep.getPublicKey(KEYPAR_PH_SIGN, sigData, phsPubKey, phsPoPBlob)) {
      /*
       * If the KEYPAR_PH_SIGN keypair is not available, it means Golden Image is not completed.
       * Respond with error
       */
      wstr errMsg;
      errMsg << L"Failed to retrieve PHs public key while processing " << HINT_CLONE_HELPER;
      SYSMSG_FUNC(Error, L"%s", errMsg);
      response.setError(errMsg);
      return false;
   }
   params.addBinary(PAR_SIGNATURE, nonce.pBinary, nonce.sBinary);

   vwstr srvFQDNs = mSvcPrep.readRegServerFQDNs();
   if (srvFQDNs.empty()) {
      wstr errMsg = L"Server FQDNs are not availble in the registry";
      SYSMSG_FUNC(Error, L"%s", errMsg);
      response.setError(errResponse);
      return false;
   }

   bool result;
   wstr ack;
   for (vtstrit it = srvFQDNs.begin(); it != srvFQDNs.end(); it++) {
      wstr fqdn = it->trim();
      result =
         mSvcPrep.connectAndSend(fqdn, QUEUE_AGENT_PAIRING, HINT_PREP_CLONE, params, response);
      if (!result || !(response.getErrorText(L"")).empty()) {
         wstr errMsg;
         errMsg << L"Couldn't connect and send " << HINT_PREP_CLONE << L" msg to server: " << fqdn;
         SYSMSG_FUNC(Warn, L"%s with error: %s", errMsg, response.getErrorText());
         response.clear();
      } else {
         ack = response.get(PAR_RESULT_TEXT, L"");
         SYSMSG_FUNC(Info, "Result text returned by the hint %s is %s", PAR_RESULT_TEXT, ack);
         MsgBinary resultSig;
         resultSig.owned = false;
         response.getBinary(PAR_RESULT_SIG, &resultSig.pBinary, &resultSig.sBinary);
         SYSMSG_FUNC(Info, "Size of Ack signature from the hint %s is %d", PAR_RESULT_TEXT,
                     resultSig.sBinary);
         // TODO: verify signation PAR_RESULT_TEXT value + nonce
         if (!ack.empty() && resultSig.sBinary > 0) {
            // successfully received an ack for HINT_PREP_CLONE msg
            SYSMSG_FUNC(Info, L"Successfully updated AD server name and AD site for the VM: %s",
                        hostName);
            break;
         }
      }
   }

   if (ack.empty()) {
      wstr errMsg;
      errMsg << L"Failed to updated AD server name and AD site for the VM(" << hostName
             << L") to any of the servers: " << srvFQDNs.toString(L",");
      SYSMSG_FUNC(Error, L"%s", errMsg);
      response.setError(errResponse);
      return false;
   }
   return true;
}
