/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * vdpRdeCommonMgr.c --
 *
 */

#include "str.h"
#include "strutil.h"
#include "vdpMksVchan.h"
#include "vdpNotificationApi.h"
#include "vdpObserverNameDefs.h"
#include "vdpRdeCommonMgr.h"
#include "viewClient.h"
#include "viewClientOS.h"
#include "viewControlMgr.h"
#include "rdeChannelMsg.h"

static struct {
   Bool active;
   VDPService_ObserverId pluginObserverId;
} rdeCommonMgr;

typedef struct FeatureOption {
   char *optionKey;
   char *optionValue;
   struct FeatureOption *next;
} FeatureOption;

static void VDPRdeCommonMgrCleanup(void);

static Bool VDPRdeCommonMgrOnPluginNotification(void *context, const char *sourceToken,
                                                const void *cookie, const void *data);
static FeatureOption *VDPRdeCommonMgr_ParseFeatureOptions(const RdeChannelMessage *msg);
static void VDPRdeCommonMgr_FreeFeatureOptions(FeatureOption *options);


/*
 *----------------------------------------------------------------------------
 *
 * VDPRdeCommonMgr_Init --
 *
 *    Initialize the rdeCommonMgr.
 *
 * Results:
 *      TRUE if successful. FALSE, otherwise.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

Bool
VDPRdeCommonMgr_Init(void)
{
   FUNC_ENTRY;

   memset(&rdeCommonMgr, 0x00, sizeof(rdeCommonMgr));

   if (!VDPNotification_Init()) {
      goto exit;
   }

   rdeCommonMgr.pluginObserverId = VDPNotification_RegisterObserver(
      RDE_COMMON_GENERIC_NOTIFICATION, NULL, VDPRdeCommonMgrOnPluginNotification);
   if (rdeCommonMgr.pluginObserverId == VDPOBSERVER_INVALID_ID) {
      goto exit;
   }

   rdeCommonMgr.active = TRUE;

exit:
   FUNC_EXIT;

   if (!rdeCommonMgr.active) {
      // Cleanup
      VDPRdeCommonMgrCleanup();
      return FALSE;
   }
   return TRUE;
}


/*
 *----------------------------------------------------------------------------
 *
 * VDPRdeCommonMgrCleanup --
 *
 *      Clean up the rdeCommonMgr.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

static void
VDPRdeCommonMgrCleanup(void)
{
   ASSERT(!rdeCommonMgr.active);

   if (rdeCommonMgr.pluginObserverId != VDPOBSERVER_INVALID_ID) {
      VDPNotification_UnregisterObserver(rdeCommonMgr.pluginObserverId);
      rdeCommonMgr.pluginObserverId = VDPOBSERVER_INVALID_ID;
   }

   VDPNotification_Exit();

   memset(&rdeCommonMgr, 0x00, sizeof(rdeCommonMgr));
}


/*
 *----------------------------------------------------------------------------
 *
 *  VDPRdeCommonMgr_Exit --
 *
 *      Clean up the state for the rdeCommonMgr.
 *
 * Results:
 *      TRUE if successful. FALSE, otherwise.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

Bool
VDPRdeCommonMgr_Exit(void)
{
   FUNC_ENTRY;

   if (rdeCommonMgr.active) {
      rdeCommonMgr.active = FALSE;
      VDPRdeCommonMgrCleanup();
   }

   FUNC_EXIT;
   return TRUE;
}


/*
 *----------------------------------------------------------------------------
 *
 *  VDPRdeCommonMgrOnPluginNotification --
 *
 *    Handler for the plugin notification sent through VDPService.
 *
 * Results:
 *    TRUE if successful. FALSE, otherwise.
 *
 * Side effects:
 *    None.
 *
 *----------------------------------------------------------------------------
 */

Bool
VDPRdeCommonMgrOnPluginNotification(void *context,           // IN
                                    const char *sourceToken, // IN
                                    const void *cookie,      // IN
                                    const void *data)        // IN
{
   uint32 *size = (uint32 *)data;
   RdeChannelMessage *pMsg = (RdeChannelMessage *)data;
   uint32 msgType = pMsg->msgType;
   Bool enable = FALSE;

   Log("%s: data size is %d.\n", __FUNCTION__, *size);
   switch (RDE_GET_CHANNEL_MSG_TYPE(msgType)) {

   case RDE_CHANNEL_APP_PROTECTION_MSG: {

      switch (RDE_GET_CHANNEL_INTERNAL_MSG_TYPE(msgType)) {

      case RDE_CHANNEL_BLOCK_KEY_LOGGER_ENABLE_MSG: {
         enable = *pMsg->payload;
         Log("%s: set block keylogger to %s.\n", __FUNCTION__, enable ? "enabled" : "disabled");
         ViewClient_SetBlockKeyLoggerStatus(enable);
         FeatureOption *header = VDPRdeCommonMgr_ParseFeatureOptions(data);
         FeatureOption *cur = header;
         while (cur != NULL) {
            if (cur->optionKey != NULL &&
                Str_Strcmp(cur->optionKey, "allowArmNoAntiKeylogger") == 0) {
               Bool allowArmNoAntiKeyloggerEnabled = *(uint8 *)(cur->optionValue);
               Log("%s, update allow connection from windows ARM without "
                   "AntiKeylogger to %d.\n",
                   __FUNCTION__, allowArmNoAntiKeyloggerEnabled ? 1 : 0);
               ViewClient_SetAllowArmNoAntiKeyloggerStatus(allowArmNoAntiKeyloggerEnabled);
               break;
            }
            cur = cur->next;
         }
         VDPRdeCommonMgr_FreeFeatureOptions(header);
         header = NULL;
      } break;

      case RDE_CHANNEL_BLOCK_SEND_INPUT_ENABLE_MSG: {
         enable = *pMsg->payload;
         Log("%s: set block SendInput() to %s.\n", __FUNCTION__, enable ? "enabled" : "disabled");
         ViewClient_SetBlockSendInputStatus(enable);
      } break;

      default:
         break;
      } // switch (RDE_GET_CHANNEL_INTERNAL_MSG_TYPE(msgType))

   } break;

   default:
      break;
   } // switch (RDE_GET_CHANNEL_MSG_TYPE(msgType))

   ViewControlMgr_GHIUpdateFromGuest(GHI_GUEST_RDE_COMMON_GENERIC, data, *size);

   return TRUE;
}


/*
 *----------------------------------------------------------------------------
 *
 *  VDPRdeCommonMgr_GHIRequestReceived --
 *
 *      A request has been received from the UI.
 *
 * Results:
 *      True if this message is handled; False otherwise.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

Bool
VDPRdeCommonMgr_GHIRequestReceived(GHIChannelType channel, // IN
                                   uint32 msgId,           // IN
                                   const char *msgName,    // IN: NULL-terminated
                                   const uint8 *msgData,   // IN
                                   uint32 msgDataLen)      // IN
{
   Bool status = FALSE;
   if (channel != GHI_CHANNEL_VIEW_REMOTE_RDE_COMMON) {
      return status;
   }
   if (Str_Strcmp(msgName, GHI_RDE_COMMON_GENERIC_CMD) == 0) {
      status = VDPNotification_Broadcast(RDE_COMMON_GENERIC_CMD, "", msgData);
   } else if (Str_Strcmp(msgName, GHI_RDE_COMMON_SET_IME_ENABLED_CMD) == 0) {
      Bool isIMEEnabled;

      isIMEEnabled = msgData ? !!*msgData : FALSE;
      ViewClient_IMESetEnabled(isIMEEnabled);
      status = TRUE;
   } else if (Str_Strcmp(msgName, GHI_RDE_COMMON_SET_IME_HOST_KEYS_CMD) == 0) {
      ViewClientOS_IMEAddFilterKeys(msgData, msgDataLen);
      status = TRUE;
   }
   ViewControlMgr_GHIResponseFromGuest(
      channel, msgId, status ? GHI_REQUEST_SUCCESS_OK : GHI_REQUEST_SUCCESS_ERROR, NULL, 0);
   return status;
}


/*
 *----------------------------------------------------------------------------
 *
 *  VDPRdeCommonMgr_ParseFeatureOptions --
 *
 *      Parse feature options by the given info.
 *
 * Results:
 *      Feature options.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

FeatureOption *
VDPRdeCommonMgr_ParseFeatureOptions(const RdeChannelMessage *pMsg) // IN
{
   if (pMsg == NULL || RDE_GET_CHANNEL_MSG_TYPE(pMsg->msgType) != RDE_CHANNEL_APP_PROTECTION_MSG) {
      return NULL;
   }

   FeatureOption *header = NULL;
   FeatureOption *tail = NULL;
   uint32 offset = 0;
   uint32 payloadSize = pMsg->msgSize - sizeof(RdeChannelMessage) + sizeof(uint8);
   offset++;
   while (offset < payloadSize) {
      uint32 keySize = 0;
      ASSERT(offset + sizeof(uint32) <= payloadSize);
      memcpy(&keySize, pMsg->payload + offset, sizeof(uint32));
      offset += sizeof(keySize);

      FeatureOption *curOption = Util_SafeMalloc(sizeof(FeatureOption));
      curOption->optionKey = Util_SafeMalloc(keySize + 1);
      ASSERT(offset + keySize <= payloadSize);
      memcpy(curOption->optionKey, (char *)pMsg->payload + offset, keySize);
      curOption->optionKey[keySize] = '\0';
      offset += keySize;

      uint32 valSize = 0;
      ASSERT(offset + sizeof(uint32) <= payloadSize);
      memcpy(&valSize, pMsg->payload + offset, sizeof(uint32));
      offset += sizeof(valSize);

      curOption->optionValue = Util_SafeMalloc(valSize);
      ASSERT(offset + valSize <= payloadSize);
      memcpy(curOption->optionValue, pMsg->payload + offset, valSize);
      offset += valSize;

      curOption->next = NULL;
      if (header == NULL) {
         header = curOption;
      }
      if (tail != NULL) {
         tail->next = curOption;
         tail = tail->next;
      } else {
         tail = curOption;
      }
   }
   return header;
}


/*
 *----------------------------------------------------------------------------
 *
 *  VDPRdeCommonMgr_FreeFeatureOptions --
 *
 *      Free the given feature options.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

void
VDPRdeCommonMgr_FreeFeatureOptions(FeatureOption *header) // IN
{
   FeatureOption *cur = header;
   while (cur != NULL) {
      FeatureOption *next = cur->next;
      free(cur->optionKey);
      free(cur->optionValue);
      free(cur);
      cur = next;
   }
}


#ifdef RDE_CLIENT_UNIT_TEST
/*
 *----------------------------------------------------------------------------
 *
 *  VDPRdeCommonMgr_OnNotificationWrapperForUT --
 *
 *      Wrapper of VDPRdeCommonMgrOnPluginNotification for unit test.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

Bool
VDPRdeCommonMgr_OnNotificationWrapperForUT(void *context,           // IN
                                           const char *sourceToken, // IN
                                           const void *cookie,      // IN
                                           const void *data)        // IN
{
   return VDPRdeCommonMgrOnPluginNotification(context, sourceToken, cookie, data);
}

#endif