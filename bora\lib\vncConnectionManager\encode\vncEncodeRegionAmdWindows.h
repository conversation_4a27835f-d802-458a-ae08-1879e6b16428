/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 *
 * vncEncodeRegionAmdlWindows.h --
 *
 *      Windows-specific functions for the Amd HW encoder.
 */

#if defined(_WIN32)

#   define AMF_LIBRARY_NAME "amfrt64.dll"

#   define DLSYM(name, dllHandle)                                                                  \
      do {                                                                                         \
         FARPROC *fnPtr = (FARPROC *)&amdsdk.name;                                                 \
         *fnPtr = GET_SYM(dllHandle, #name);                                                       \
         if (*fnPtr == NULL) {                                                                     \
            REGENCWARN("GetProcAddress: Failed to resolve %s: %d", #name, GetLastError());         \
            goto exit;                                                                             \
         }                                                                                         \
      } while (0)
#   define DLSYM_NOFAIL(name, dllHandle)                                                           \
      do {                                                                                         \
         FARPROC *fnPtr = (FARPROC *)&amdsdk.name;                                                 \
         *fnPtr = GET_SYM(dllHandle, #name);                                                       \
         if (*fnPtr == NULL) {                                                                     \
            REGENCWARN("GetProcAddress: Failed to resolve %s: %d", #name, GetLastError());         \
         }                                                                                         \
      } while (0)

#   define ENTER_CRITICAL_SECTION(a)                                                               \
      if ((a)->pD3d11DeviceContext) {                                                              \
         EnterCriticalSection(&(a)->csD3d11DeviceContext);                                         \
      }

#   define LEAVE_CRITICAL_SECTION(a)                                                               \
      if ((a)->pD3d11DeviceContext) {                                                              \
         LeaveCriticalSection(&(a)->csD3d11DeviceContext);                                         \
      }

#   define FILE_DUMP_PREFIX

#   include <d3d11_1.h>

void VNCEncodeRegionAmdCloseHandles(VNCRegionEncoderAmd *regEnc);


/*
 *-----------------------------------------------------------------------------
 *
 * VNCEncodeRegionAmdRegisterTexture --
 *
 *      Create a new D3D11 texture asssociated with the given shared handle.
 *
 * Results:
 *      Returns TRUE if successful, FALSE otherwise.
 *
 * Side effects:
 *      An additional handle slot will be occupied.
 *
 *-----------------------------------------------------------------------------
 */

static Bool
VNCEncodeRegionAmdRegisterTexture(VNCRegionEncoderAmd *regEnc,           // IN
                                  const VNCRegEncFrameState *frameState, // IN
                                  int *allocTexIdx)                      // OUT
{
   Bool success = FALSE;
   HRESULT result;
   IID iidD3D11Texture2D = IID_ID3D11Texture2D;
   IID iidDXGIKeyedMutex = IID_IDXGIKeyedMutex;
   IID iidD3D11Device1 = IID_ID3D11Device1;
   ID3D11Device1 *pDevice1 = NULL;
   void *resourceToRegister = NULL;
   int texIdx;

   /* Find a free slot for this handle. */
   texIdx = VNCEncodeRegionAmdFindFreeHandleSlot(regEnc);

   /*
    * If there is no available slot for this handle, take it as a hint that the
    * pixel providers have been invalidated and replace the old handles with
    * the new incoming ones (bug 2270135).
    */
   if (texIdx < 0) {
      REGENC_RLOG(3, "out of input resources, freeing old textures/handles");
      VNCEncodeRegionAmdCloseHandles(regEnc);
      texIdx = VNCEncodeRegionAmdFindFreeHandleSlot(regEnc);
   }
   VERIFY(texIdx >= 0 && texIdx < TOTAL_INPUT_RESOURCE);

   regEnc->pSharedHandle[texIdx] = (HANDLE)frameState->sharedHandle;

   *allocTexIdx = texIdx;

   if (regEnc->pD3d11Device) {
      D3D11_TEXTURE2D_DESC desc = {0};
      if (regEnc->useUnifiedD3DDevice) {
         REGENC_RLOG(3, "new shared texture %p (slot %d)", frameState->texture, texIdx);
         if (FAILED(result = ID3D11Texture2D_QueryInterface(
                       (ID3D11Texture2D *)frameState->texture, &iidD3D11Texture2D,
                       (void **)&regEnc->pD3d11Texture[texIdx]))) {
            REGENCWARN("%s: Failed to query D3D11 texture (%08x) (device %p)", __FUNCTION__, result,
                       regEnc->pD3d11Device);
            goto exit;
         }
      } else {
         REGENC_RLOG(3, "new shared handle %p (slot %d)", frameState->sharedHandle, texIdx);

         if (FAILED(result = ID3D11Device_QueryInterface(regEnc->pD3d11Device, &iidD3D11Device1,
                                                         (void **)&pDevice1))) {
            REGENCWARN("%s: Failed to query ID3D11Device1 (%08x) (device %p)", __FUNCTION__, result,
                       regEnc->pD3d11Device);
            goto exit;
         }

         if (FAILED(result = ID3D11Device1_OpenSharedResource1(
                       pDevice1, regEnc->pSharedHandle[texIdx], &iidD3D11Texture2D,
                       (void **)&regEnc->pD3d11Texture[texIdx]))) {
            REGENCWARN("%s: OpenSharedResource1 failed (%08x)"
                       " (device %p, handle %p)",
                       __FUNCTION__, result, regEnc->pD3d11Device, regEnc->pSharedHandle[texIdx]);
            goto exit;
         }
      }

      /*
       * Check if the current texture support keyed mutex. If it does it means we
       * need to acquire sync prior to accessing the texture.
       */
      if (FAILED(result = ID3D11Texture2D_QueryInterface(
                    regEnc->pD3d11Texture[texIdx], &iidDXGIKeyedMutex,
                    (void **)&regEnc->pD3d11TextureKeyedMutex[texIdx]))) {
         REGENCWARN_ONCE("%s: The D3D11 texture does not support keyed mutex (%08x)"
                         " (device %p, handle %p)",
                         __FUNCTION__, result, regEnc->pD3d11Device, regEnc->pSharedHandle[texIdx]);
      } else {
         REGENCWARN_ONCE("%s: The D3D11 texture does support keyed mutex (%08x)"
                         " (device %p, handle %p)",
                         __FUNCTION__, result, regEnc->pD3d11Device, regEnc->pSharedHandle[texIdx]);
      }

      resourceToRegister = regEnc->pD3d11Texture[texIdx];
      ID3D11Texture2D_GetDesc(regEnc->pD3d11Texture[texIdx], &desc);
      ASSERT(desc.Format == DXGI_FORMAT_B8G8R8A8_UNORM ||
             desc.Format == DXGI_FORMAT_R10G10B10A2_UNORM);

      if (desc.Format == DXGI_FORMAT_R10G10B10A2_UNORM) {
         REGENC_RLOG(1, "%s: Texture format ARGB10", __FUNCTION__);
      } else {
         REGENC_RLOG(1, "%s: Texture format ARGB", __FUNCTION__);
      }

      if (regEnc->fhandleRaw) {
         desc.Usage = D3D11_USAGE_STAGING;
         desc.BindFlags = 0;
         desc.CPUAccessFlags = D3D11_CPU_ACCESS_READ;
         desc.MiscFlags = 0;
         if (FAILED(result =
                       ID3D11Device_CreateTexture2D(regEnc->pD3d11Device, &desc, NULL,
                                                    &regEnc->pD3d11TextureMappable[texIdx]))) {
            REGENCWARN("%s: CreateTexture2D for staging texture failed (%08x)", __FUNCTION__,
                       result);
            goto exit;
         }
      }
   } else {
      return FALSE;
   }
   regEnc->inputRegHandle[texIdx] = resourceToRegister;
   success = TRUE;

exit:
   if (pDevice1 != NULL) {
      ID3D11Device1_Release(pDevice1);
   }

   if (success == FALSE) {
      VNCEncodeRegionAmdCloseHandles(regEnc);
   }

   return success;
}


/*
 *-----------------------------------------------------------------------------
 *
 * VNCEncodeRegionAmdGetDxgiAdapter --
 *
 *      Returns the Amd DXGI adapter, retrieved from its LUID.
 *      If the LUID pointer is NULL, skip that check and return the first
 *      matching Amd adapter.
 *
 * Results:
 *      A pointer to the correct IDXGIAdapter if there was a match;
 *      NULL otherwise.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

static IDXGIAdapter *
VNCEncodeRegionAmdGetDxgiAdapter(VNCRegionEncoderAmd *regEnc, // IN
                                 const LUID *adapterId)       // IN, optional
{
   IDXGIFactory1 *pDxgiFactory1 = NULL;
   IID iidDxgiFactory1 = IID_IDXGIFactory1;
   IDXGIAdapter *adapter = NULL;
   uint32 adapterIndex = 0;
   HRESULT result;

   REGENC_RLOG(9, "%s: Creating DXGIFactory1 interface", __FUNCTION__);
   if (FAILED(result = CreateDXGIFactory1(&iidDxgiFactory1, (void **)(&pDxgiFactory1)))) {
      REGENCWARN("%s: CreateDXGIFactory failed (%08x)", __FUNCTION__, result);
      return NULL;
   }

   while (IDXGIFactory1_EnumAdapters(pDxgiFactory1, adapterIndex, &adapter) !=
          DXGI_ERROR_NOT_FOUND) {
      DXGI_ADAPTER_DESC adapterDesc = {0};

      REGENC_RLOG(9, "%s: Processing current adapter %d", __FUNCTION__, adapterIndex);
      /*
       * Iterate through all Amd adapters and find the one with a matching
       * LUID.
       */
      if (IDXGIAdapter_GetDesc(adapter, &adapterDesc) == S_OK &&
          adapterDesc.VendorId == PCI_VENDOR_ID_AMD) {
         if (adapterId == NULL || !memcmp(adapterId, &adapterDesc.AdapterLuid, sizeof(LUID))) {
            goto exit;
         }
      }
      adapterIndex++;
      IDXGIAdapter_Release(adapter);
   }

exit:
   IDXGIFactory1_Release(pDxgiFactory1);
   return adapter;
}


/*
 *-----------------------------------------------------------------------------
 *
 * VNCEncodeRegionAmdCreateD3D11Device --
 *
 *      Create a new D3D11 device associated with an Amd adapter, optionally
 *      associated to a specific adapter LUID if provided.
 *      If a valid pointer is passed, a derived D3D device context will also
 *      be output. If pointer for critical section is valid when d3d context
 *      is requested a critical section is initialized.
 *
 * Results:
 *      A pointer to a valid ID3D11Device if successful; NULL otherwise.
 *
 * Side effects:
 *      deviceContext pointer may be written to.
 *
 *-----------------------------------------------------------------------------
 */

static void *
VNCEncodeRegionAmdCreateD3D11Device(VNCRegionEncoderAmd *regEnc,           // IN
                                    const LUID *adapterId,                 // IN/OPT
                                    ID3D11DeviceContext **ppDeviceContext, // OUT/OPT
                                    CRITICAL_SECTION *cs)                  // OUT/OPT
{
   HRESULT result;
   ID3D11Device *pD3d11Device = NULL;
   IDXGIAdapter *pDxgiAdapter = NULL;
   UINT flags = 0;
   const D3D_FEATURE_LEVEL FeatureLevels[] = {D3D_FEATURE_LEVEL_11_1, D3D_FEATURE_LEVEL_11_0,
                                              D3D_FEATURE_LEVEL_10_1, D3D_FEATURE_LEVEL_10_0,
                                              D3D_FEATURE_LEVEL_9_1};
   const UINT uNumFeatureLevels = ARRAYSIZE(FeatureLevels);

   REGENC_RLOG(9, "%s: Query Amd Dxgi Adapter", __FUNCTION__);
   pDxgiAdapter = VNCEncodeRegionAmdGetDxgiAdapter(regEnc, adapterId);
   if (pDxgiAdapter == NULL) {
      REGENCWARN("%s: Failed to find an Amd DXGI adapter with output "
                 "resolution matching desired adapter LUID",
                 __FUNCTION__);
      return NULL;
   }

   if (vmx86_devel) {
      flags = D3D11_CREATE_DEVICE_DEBUG;
   }

   REGENC_RLOG(9, "%s: Create D3D11 device for AMD encoder", __FUNCTION__);
   result = D3D11CreateDevice(pDxgiAdapter, D3D_DRIVER_TYPE_UNKNOWN, NULL, flags, FeatureLevels,
                              uNumFeatureLevels, D3D11_SDK_VERSION, &pD3d11Device, NULL, NULL);
   if (FAILED(result)) {
      REGENCWARN("%s: D3D11CreateDevice failed (%08x)", __FUNCTION__, result);
      goto exitDX11;
   }

   if (ppDeviceContext) {
      VERIFY(vmx86_debug);
      ID3D11Device_GetImmediateContext(pD3d11Device, ppDeviceContext);
      if (*ppDeviceContext == NULL) {
         REGENCWARN("%s: null device context returned", __FUNCTION__);
         ID3D11Device_Release(pD3d11Device);
         pD3d11Device = NULL;
         goto exitDX11;
      }

      if (cs) {
         InitializeCriticalSection(cs);
      }
   }

exitDX11:
   IDXGIAdapter_Release(pDxgiAdapter);

   return pD3d11Device;
}


/*
 *-----------------------------------------------------------------------------
 *
 * VNCEncodeRegionAmdDestroyD3D11Device --
 *
 *      Destroy the passed in D3D11 device pointer. If the deviceContext
 *      pointer is non-null, destroy that too. Critical section is expected to
 *      be valid if there is a device context and would be freed.
 *
 * Results:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

static void
VNCEncodeRegionAmdDestroyD3D11Device(ID3D11Device *device,               // IN
                                     ID3D11DeviceContext *deviceContext, // IN/OPT
                                     CRITICAL_SECTION *cs)               // IN/OPT
{
   /*
    * In a development build, report all the remaining live objects (must
    * attach the visual studio debugger to see this).
    */
   if (vmx86_devel && IsDebuggerPresent()) {
      HRESULT hr;
      ID3D11Debug *debug = NULL;
      ID3D11InfoQueue *infoQueue = NULL;
      IID iidD3D11Debug = IID_ID3D11Debug;
      IID iidD3D11InfoQueue = IID_ID3D11InfoQueue;
      hr = ID3D11Device_QueryInterface(device, &iidD3D11Debug, &debug);
      if (SUCCEEDED(hr)) {
         hr = ID3D11Device_QueryInterface(device, &iidD3D11InfoQueue, &infoQueue);
         if (SUCCEEDED(hr)) {
            ID3D11InfoQueue_AddApplicationMessage(infoQueue, D3D11_MESSAGE_SEVERITY_WARNING,
                                                  __FUNCTION__ ":");
            ID3D11Debug_ReportLiveDeviceObjects(debug, D3D11_RLDO_DETAIL);
            ID3D11InfoQueue_Release(infoQueue);
         }
         ID3D11Debug_Release(debug);
      }
   }

   ID3D11Device_Release(device);
   if (deviceContext) {
      EnterCriticalSection(cs);
      ID3D11DeviceContext_Release(deviceContext);
      LeaveCriticalSection(cs);
      DeleteCriticalSection(cs);
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * VNCEncodeRegionAmdCreateOS --
 *
 *      Create the OS specific region encoder properties.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

static VNCError
VNCEncodeRegionAmdCreateOS(VNCRegionEncoderAmd *regEnc, // IN
                           void *adapterId)             // IN
{
   /*
    * Create the D3D device for the i-th screen.
    */
   ID3D11DeviceContext **ppDeviceContext = (vmx86_debug) ? &regEnc->pD3d11DeviceContext : NULL;
   CRITICAL_SECTION *cs = (vmx86_debug) ? &regEnc->csD3d11DeviceContext : NULL;

   regEnc->pD3d11Device =
      VNCEncodeRegionAmdCreateD3D11Device(regEnc, (const LUID *)adapterId, ppDeviceContext, cs);
   if (!regEnc->pD3d11Device) {
      return VNC_ERROR_HW_ENCODE_FAILED;
   }

   return VNC_SUCCESS;
}


/*
 *-----------------------------------------------------------------------------
 *
 * VNCEncodeRegionIntelDestroyOS --
 *
 *      Destroy the OS specific region encoder properties.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

static void
VNCEncodeRegionAmdDestroyOS(VNCRegionEncoderAmd *regEnc) // IN
{
   if (regEnc->pD3d11Device) {
      VNCEncodeRegionAmdDestroyD3D11Device(regEnc->pD3d11Device, regEnc->pD3d11DeviceContext,
                                           &regEnc->csD3d11DeviceContext);
      regEnc->pD3d11DeviceContext = NULL;
      regEnc->pD3d11Device = NULL;
   }
}


/*
 *-----------------------------------------------------------------------------
 * VNCEncodeRegionAmdDumpARGB --
 *
 *      Dump uncompressed surface contents.
 *
 *      NOTE: Will fill the disk at the following rate:
 *      - 1080p@30Hz: 237 MB/s
 *      - 2160p@30Hz: 949 MB/s
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      Generates EVEN BIGGER files.
 *
 *-----------------------------------------------------------------------------
 */

static void
VNCEncodeRegionAmdDumpARGB(VNCRegionEncoderAmd *regEnc, // IN
                           int textureIdx)              // IN
{
   VERIFY(vmx86_debug);
   if (regEnc && regEnc->fhandleRaw) {
      HRESULT result;
      size_t bufferSize = 0;
      uint8 *buffer = NULL;

      if (regEnc->pD3d11DeviceContext) {
         /*
          * In DirectX 11, we need to explicitly copy the input texture to a
          * mappable one, then map the latter.
          */
         D3D11_MAPPED_SUBRESOURCE mappedSubRes = {0};

         ENTER_CRITICAL_SECTION(regEnc)
         ID3D11DeviceContext_CopyResource(
            regEnc->pD3d11DeviceContext,
            (ID3D11Resource *)regEnc->pD3d11TextureMappable[textureIdx],
            (ID3D11Resource *)regEnc->pD3d11Texture[textureIdx]);
         result =
            ID3D11DeviceContext_Map(regEnc->pD3d11DeviceContext,
                                    (ID3D11Resource *)regEnc->pD3d11TextureMappable[textureIdx], 0,
                                    D3D11_MAP_READ, 0, &mappedSubRes);
         if (FAILED(result)) {
            REGENCWARN("%s: Failed to map D3D11 texture for read-back (0x%08x)", __FUNCTION__,
                       result);
            LEAVE_CRITICAL_SECTION(regEnc);
            return;
         }
         bufferSize = mappedSubRes.RowPitch * regEnc->height;
         buffer = Util_SafeMalloc(bufferSize);
         memcpy(buffer, mappedSubRes.pData, bufferSize);
         ID3D11DeviceContext_Unmap(regEnc->pD3d11DeviceContext,
                                   (ID3D11Resource *)regEnc->pD3d11TextureMappable[textureIdx], 0);
         LEAVE_CRITICAL_SECTION(regEnc)
      }

      /*
       * Log the data to a file, which can be played with a specialized image
       * viewer.
       */
      if (buffer && fwrite(buffer, bufferSize, 1, regEnc->fhandleRaw) == 0) {
         REGENCWARN("Write to bitstream dump file failed");
         VNCEncodeRegionCloseFile(&regEnc->fhandleRaw);
      }
      free(buffer);
   }
}


#endif
