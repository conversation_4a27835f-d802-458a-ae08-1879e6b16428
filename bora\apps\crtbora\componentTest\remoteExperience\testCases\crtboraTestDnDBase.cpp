/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * crtboraTestDnDBase.cpp --
 *
 *      crtbora base testing cases of DnD.
 */


#include <algorithm>

#include "crtboraTestDnDBase.h"
#include "crtboraUtils.h"
#include "preference.h"
#include "utilities/dndTestUtils.h"

std::map<TestDataType, const char *> gDataTypeMap = {
   {DnD_Text, "Text"},     {DnD_RTF, "RTF"},
   {DnD_Image, "Image"},   {DnD_File, "File"},
   {DnD_Folder, "Folder"}, {DnD_MultiFileFolder, "MultiFileFolder"},
   {DnD_HTML, "HTML"},     {DnD_FileContent, "FileContent"}};

// Add setting in new registry path
const char *const Add_DnD_Size_Control_FMT =
   "reg add \"HKEY_LOCAL_MACHINE\\" HORIZON_VDM_REG_GPO_ROOT_A "\\Agent\\Drag and Drop\" "
   "/v DnDSize /t REG_DWORD /d %d /f /reg:64";

// Delete setting in new registry path
const char *const Del_DnD_Size_Control_FMT =
   "reg delete \"HKEY_LOCAL_MACHINE\\" HORIZON_VDM_REG_GPO_ROOT_A "\\Agent\\Drag and Drop\" "
   "/v DnDSize /f";

/*
 *-----------------------------------------------------------------------------
 *
 * DnDConfigData::StrToDataType --
 *
 *      Convert string of data type name to enum
 *
 * Results:
 *      TestDataType value.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

TestDataType
DnDConfigData::StrToDataType(const char *typeStr)
{
   for (auto iter = gDataTypeMap.begin(); iter != gDataTypeMap.end(); ++iter) {
      if (strcmp(iter->second, typeStr) == 0) {
         return iter->first;
      }
   }
   return DnD_Unknown;
}


/*
 *-----------------------------------------------------------------------------
 *
 * DnDConfigData::StrToDirection --
 *
 *      Convert string of data type name to enum
 *
 * Results:
 *      DnDDirection value.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

DnDDirection
DnDConfigData::StrToDirection(const char *typeStr)
{
   if (strcmp(typeStr, "AgentToClient") == 0) {
      return AgentToClient;
   } else {
      return ClientToAgent;
   }
}

std::vector<DnDConfigData> GeneratorEvaluationTest::param_value_vector;
std::vector<DnDConfigData> GeneratorEvaluationTest::param_vector_app;

const char *const DnD_Config_Common_File = "DnD_Test_Config_File_Common.json";
const char *const DnD_Config_Common_App_File = "DnD_Test_Config_File_App_Common.json";

#if defined(_WIN32)
const char *const DnD_Config_File = "DnD_Test_Config_File_Win.json";
const char *const DnD_Config_App_File = "DnD_Test_Config_File_App_Win.json";
#elif __APPLE__
const char *const DnD_Config_File = "DnD_Test_Config_File_Mac.json";
const char *const DnD_Config_App_File = "DnD_Test_Config_File_App_Mac.json";
#endif


/*
 *-----------------------------------------------------------------------------
 *
 * RunParamCases --
 *
 *      This is the dll function entrance and if you want to write a test
 *      component with value-parameteried testing, you should follow write
 *      below function in your project.
 *
 *-----------------------------------------------------------------------------
 */

extern "C" RX_CI_PLUGIN_API int
RunParamCases(const char *param,                      // IN
              SessionManagementInterface *sessionMgr, // IN
              FrameworkInterface *framework,          // IN
              int argc,                               // IN
              char **argv)                            // IN
{
   TestBase::SetFramework(framework);
   AutoTestBase::SetSessionManager(sessionMgr);

   RxTestClientTargetInfo targetInfo;
   /*
    * This flag should be initialized to 0 by default.
    * The RunAllTests may check this flag to determine how to run
    * all tests.
    */
   targetInfo.flags = 0;
   TestCasesTargetInfo(targetInfo);

   bool needsInitLog = !(targetInfo.flags & RXTEST_CLIENT_LOG_INITIALIZED);

   /*
    * If component is compiled with test case to one lib for testing, such as
    * crtbora, then the log will be initialized insdie component product
    * code and no needs to initialize log here.
    */
   if (needsInitLog) {
      // log init
      Preference_Init();
      LogOutput *output =
         Log_InitWithCustom(Log_CfgInterface(), &AutoTestBase::LogCustomMsgFuncImpl, HZN_LOG_INFO);
      ConsoleLog("Logging test case\n");
   }

   ComponentTestDnDBase::AddConfigDataFromFile(DnD_Config_Common_File);
   ComponentTestDnDBase::AddConfigDataFromFile(DnD_Config_File);

   if (ViewEnvConfig::getInstance().IsRDSHAgent()) {
      // Only fetch app test data and run app case in RDSH
      ComponentTestDnDBase::AddConfigDataFromFile(DnD_Config_Common_App_File);
      ComponentTestDnDBase::AddConfigDataFromFile(DnD_Config_App_File);
   }

   ::testing::InitGoogleTest(&argc, argv);
   int exitCode = AutoTestBase::RunAllTests(targetInfo.flags);

   if (needsInitLog) {
      // log exit
      Log_Exit();
      Preference_Exit();
   }

   return exitCode;
}


/*
 *----------------------------------------------------------------------------
 *
 * ComponentTestDnDBase::AddConfigDataFromFile --
 *
 *    Add test cases config data according to the content of the config file.
 *
 * Results:
 *    None.
 *
 * Side effects:
 *    None.
 *
 *----------------------------------------------------------------------------
 */

void
ComponentTestDnDBase::AddConfigDataFromFile(const char *configFile) // IN
{
   bool isAppData = (strstr(configFile, "App") != nullptr);

   // Read and parse json file for parameterized test
   const char *configChar = GetTextFileContent(configFile);
   cJSON *config = cJSON_Parse(configChar);
   if (config) {
      for (int i = 0; i < cJSON_GetArraySize(config); i++) {
         cJSON *item = cJSON_GetArrayItem(config, i);
         /*
          * NamedPipeCmd is only needed for Agent to Client drag test, so set
          * a empty namedPipeCmd for the Client to Agent case for the
          * NamedPipeCmd item does not exist in the json file.
          */
         DnDConfigData configData(cJSON_GetObjectItem(item, "DragType")->valuestring,
                                  cJSON_GetObjectItem(item, "Direction")->valuestring,
                                  (cJSON_GetObjectItem(item, "NamedPipeCmd")
                                      ? cJSON_GetObjectItem(item, "NamedPipeCmd")->valuestring
                                      : ""),
                                  cJSON_GetObjectItem(item, "DataFile")->valuestring,
                                  cJSON_GetObjectItem(item, "ExpectedResult")->valuestring,
                                  cJSON_GetObjectItem(item, "CaseName")->valuestring);
         if (isAppData) {
            GeneratorEvaluationTest::add_param_app(configData);
         } else {
            GeneratorEvaluationTest::add_param_value(configData);
         }
      }
      cJSON_Delete(config);
   }
   if (configChar) {
      delete[] configChar;
   }
}


/*
 *----------------------------------------------------------------------------
 *
 * ComponentTestDnDBase::PerformDnDTest --
 *
 *    Execute a common DnD operation.
 *
 * Results:
 *    true if DnD is successful, false otherwise.
 *
 * Side effects:
 *    None.
 *
 *----------------------------------------------------------------------------
 */

bool
ComponentTestDnDBase::PerformDnDTest()
{
   DnDConfigData cData = GetParam();
   bool isFile = ((cData.mType == DnD_File) || (cData.mType == DnD_Folder) ||
                  (cData.mType == DnD_MultiFileFolder));
   if (!CheckCasePreCondition(isFile)) {
      ConsoleLog("Pre-condition of the case is not ready.");
      return false;
   }

   std::clock_t startTime, endTime;
   startTime = clock();

   SetDnDState(State_CapsRecv);

   bool ret = false;
   if (cData.mDirection == ClientToAgent) {
      DeleteDnDResultFile();
      int dataSize = 0;
      const char *testData = GetTestDataFromFile(cData, dataSize);
      if (testData == nullptr) {
         ConsoleLog("Failed to get test data from file");
         return false;
      }

      ConsoleLog("Invoke dragging %s to Agent, dataSize = %d", gDataTypeMap[cData.mType], dataSize);
      ret = mDnDTestManager->DragToAgent(cData.mType, testData, dataSize);
      delete[] testData;
   } else {
      ConsoleLog("Invoke dragging to Client with command: %s.", cData.mNamedPipeCmd.c_str());
      ret = mDnDTestManager->DragToClient(cData.mNamedPipeCmd);
   }

   if (ret) {
      ret = VerifyDnDResult(cData.mDirection, cData.mType, cData.mExpectedResult);
   } else {
      ConsoleLog("Drag and Drop fails");
   }
   endTime = clock();
   ConsoleLog("Dragging total Time : %.3fs.", (double)(endTime - startTime) / CLOCKS_PER_SEC);
   return ret;
}


/*
 *----------------------------------------------------------------------------
 *
 * ComponentTestDnDBase::GetTestDataFromFile --
 *
 *    Read test data from data file based on the input configuration object.
 *
 * Results:
 *    Test data
 *
 * Side effects:
 *    Caller needs to release the data after use.
 *
 *----------------------------------------------------------------------------
 */

const char *
ComponentTestDnDBase::GetTestDataFromFile(const DnDConfigData &cData, // IN
                                          int &dataSize)              // OUT
{
   char *dataContent = NULL;
   switch (cData.mType) {
   case DnD_Text:
   case DnD_RTF:
   case DnD_HTML:
      dataContent = GetTextFileContent(cData.mDataFile.c_str());
      dataSize = strlen(dataContent) + 1;
      break;

   case DnD_Image:
      dataContent = GetImage(cData.mDataFile.c_str(), dataSize);
      break;
   case DnD_FileContent:
      dataContent = GetBinaryFileContent(cData.mDataFile.c_str(), dataSize);
      break;

   case DnD_File:
   case DnD_Folder:
   case DnD_MultiFileFolder: {
      /*
       * For file/folder, the test data is the file/folder name itself instead
       * of the file content
       */
      dataSize = cData.mDataFile.size() + 1;
      std::string fileList = cData.mDataFile;
#if defined(_WIN32)
      // Windows needs multiple file/folder separated by '\0', while mac needs '|'
      replace(fileList.begin(), fileList.end(), '|', '\0');
#endif
      dataContent = new char[dataSize];
      memset(dataContent, 0, dataSize);
      memcpy(&dataContent[0], fileList.c_str(), dataSize - 1);
      break;
   }
   default:
      break;
   }

   return dataContent;
}


/*
 *-----------------------------------------------------------------------------
 *
 * ComponentTestDnDRemoteApp::SetUpTestCase --
 * ComponentTestDnDRemoteApp::TearDownTestCase --
 *
 *      Set up and tear donw for test suite
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

void
ComponentTestDnDRemoteApp::SetUpTestCase()
{
   // Set DnD size control to 5 megabytes
   SetDnDSizeRegistry(5);
   mIsAppConn = true;
   ComponentTestDnDBase::SetUpTestCase();
}

void
ComponentTestDnDRemoteApp::TearDownTestCase()
{
   RemoveDnDSizeRegistry();
   ComponentTestDnDBase::TearDownTestCase();
   mIsAppConn = false;
}


/*
 *----------------------------------------------------------------------------
 *
 * ComponentTestDnDRemoteApp::SetDnDSizeRegistry --
 *
 *    Remote set DnD size registry in Agent, the default size unit is megabyte
 *
 * Results:
 *    Return true if succeeds, false otherwise.
 *
 * Side effects:
 *    None.
 *
 *----------------------------------------------------------------------------
 */

bool
ComponentTestDnDRemoteApp::SetDnDSizeRegistry(unsigned int value) // IN
{
   char cmdline[256] = "";
   snprintf(cmdline, sizeof(cmdline), Add_DnD_Size_Control_FMT, value);
   ConsoleLog("Set DnD size registry cmd is: \"%s\"", cmdline);
   return RemoteAdminExecuteBATSync(cmdline);
}


/*
 *----------------------------------------------------------------------------
 *
 * ComponentTestDnDRemoteApp::RemoveDnDSizeRegistry --
 *
 *    Remote DnD size registry in Agent.
 *
 * Results:
 *    Return true if succeeds, false otherwise.
 *
 * Side effects:
 *    None.
 *
 *----------------------------------------------------------------------------
 */

bool
ComponentTestDnDRemoteApp::RemoveDnDSizeRegistry() // IN
{
   char cmdline[256] = "";
   snprintf(cmdline, sizeof(cmdline), Del_DnD_Size_Control_FMT);
   ConsoleLog("Remove DnD size registry cmd is: \"%s\"", cmdline);
   return RemoteAdminExecuteBATSync(cmdline);
}


/*
 *-----------------------------------------------------------------------------
 *
 * ComponentTestDnDBase::Common --
 *
 *      Test case: drag and drop.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

TEST_P(ComponentTestDnDBase, Common)
{
   const ::testing::TestInfo *const test_info =
      ::testing::UnitTest::GetInstance()->current_test_info();
   Log("Enter test case: %s.\n", test_info->name());

   EXPECT_TRUE(PerformDnDTest());

   Log("Exit test case: %s.\n", test_info->name());
}

// Instantiate parameter generators
INSTANTIATE_TEST_SUITE_P(Common, ComponentTestDnDBase,
                         testing::ValuesIn(GeneratorEvaluationTest::param_value_test()),
                         CustomerPrameterNameFucntion);


/*
 *-----------------------------------------------------------------------------
 *
 * ComponentTestDnDRemoteApp::Common --
 *
 *      Test case: drag and drop for remote app.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

TEST_P(ComponentTestDnDRemoteApp, Common)
{
   const ::testing::TestInfo *const test_info =
      ::testing::UnitTest::GetInstance()->current_test_info();
   Log("Enter test case: %s.\n", test_info->name());

   EXPECT_TRUE(PerformDnDTest());

   Log("Exit test case: %s.\n", test_info->name());
}

// Instantiate parameter generators
INSTANTIATE_TEST_SUITE_P(TestDnD, ComponentTestDnDRemoteApp,
                         testing::ValuesIn(GeneratorEvaluationTest::param_value_app()),
                         CustomerPrameterNameFucntion);
GTEST_ALLOW_UNINSTANTIATED_PARAMETERIZED_TEST(ComponentTestDnDRemoteApp);
