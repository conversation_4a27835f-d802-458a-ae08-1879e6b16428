# Copyright (c) Omnissa, LLC. All rights reserved.
# This product is protected by copyright and intellectual property laws in the
# United States and other countries as well as by international treaties.
# -- Omnissa Restricted

"""benev

Staging for the benev test environment.

Maintainers:
    <EMAIL>
    <EMAIL>
"""

import vmware
import os

stageEnv = vmware.pkg.stageEnv.Copy()
stageEnv.LoadTool("linkcopy")

isWindows = vmware.Host().IsWindows()


# Stage files
def StageFiles(stageEnv, benevPP, benevEnv, host):
    stageNodes = []

    # Stage benevPeer
    benevPeerPath = Dir(
        benevPP / "blastSocketPeer" / vmware.pkg.stageHosts[host]
    ).abspath

    for lib in benevEnv["OPENSSL_REDIST"]:
        dstFile = os.path.join(benevPeerPath, os.path.basename(lib))
        stageNodes += stageEnv.FileCopy(dstFile, lib)

    stageNodes += vmware.pkg.StageDeliverables(
        stageEnv, "benevPeer", host, benevPeerPath
    )

    return stageNodes


# Stage Node files
def StageNodeFiles(stageEnv, benevPP):
    stageNodes = []

    # Stage nodeServer files
    nodeServerFiles = [
        "#bora/apps/rde/blast/benev/blastSocketPeer/nodejs/dockerBenev.js",
        "#bora/apps/rde/blast/benev/blastSocketPeer/nodejs/library.js",
        "#bora/apps/rde/blast/benev/blastSocketPeer/nodejs/netdata.js",
        "#bora/apps/rde/blast/benev/blastSocketPeer/nodejs/nodeServer.js",
        "#bora/apps/rde/blast/benev/blastSocketPeer/nodejs/package.json",
        "#bora/apps/rde/blast/benev/blastSocketPeer/nodejs/README.txt",
        "#bora/apps/rde/blast/benev/blastSocketPeer/nodejs/reporterConfig.json",
        "#bora/apps/rde/blast/benev/blastSocketPeer/nodejs/runNodeServer.js",
        "#bora/apps/rde/blast/benev/blastSocketPeer/nodejs/runTests.py",
        "#bora/apps/rde/blast/benev/blastSocketPeer/nodejs/Set.reg",
        "#bora/apps/rde/blast/benev/blastSocketPeer/nodejs/systemConfig.js",
        "#bora/apps/rde/blast/benev/blastSocketPeer/nodejs/test.js",
        "#bora/apps/rde/blast/benev/blastSocketPeer/nodejs/utils.js",
        "#bora/apps/rde/blast/benev/blastSocketPeer/nodejs/launchTopology.js",
        "#bora/apps/rde/blast/benev/blastSocketPeer/nodejs/traffic.json",
        "#bora/apps/rde/blast/benev/blastSocketPeer/nodejs/Unset.reg",
        "#bora/apps/rde/blast/benev/blastSocketPeer/nodejs/SetVvcAPITests.reg",
        "#bora/apps/rde/blast/benev/blastSocketPeer/nodejs/UnsetVvcAPITests.reg",
        "#bora/apps/rde/blast/benev/blastSocketPeer/nodejs/SetRawChannelType.reg",
        "#bora/apps/rde/blast/benev/blastSocketPeer/nodejs/syncLogs-linux-launch.sh",
        "#bora/apps/rde/blast/benev/blastSocketPeer/nodejs/syncLogs-linux.sh",
    ]
    nodejsPath = Dir(benevPP / "nodejs").abspath
    for file in nodeServerFiles:
        dstFile = os.path.join(nodejsPath, File(file).name)
        stageNodes += stageEnv.FileCopy(dstFile, file)

    # Stage topology files
    topologyFiles = [
        "#bora/apps/rde/blast/benev/blastSocketPeer/nodejs/topologies/topology.json",
        "#bora/apps/rde/blast/benev/blastSocketPeer/nodejs/topologies/goodTopology.json",
        "#bora/apps/rde/blast/benev/blastSocketPeer/nodejs/topologies/lossyTopology.json",
        "#bora/apps/rde/blast/benev/blastSocketPeer/nodejs/topologies/bsgTopology.json",
        "#bora/apps/rde/blast/benev/blastSocketPeer/nodejs/topologies/stableAgentTopology.json",
        "#bora/apps/rde/blast/benev/blastSocketPeer/nodejs/topologies/stableClientTopology.json",
        "#bora/apps/rde/blast/benev/blastSocketPeer/nodejs/topologies/README.txt",
    ]
    topologiesPath = Dir(benevPP / "nodejs" / "topologies").abspath
    for file in topologyFiles:
        dstFile = os.path.join(topologiesPath, File(file).name)
        stageNodes += stageEnv.FileCopy(dstFile, file)

    # Stage docker files
    dockerFiles = [
        "#bora/apps/rde/blast/benev/blastSocketPeer/nodejs/dockerfiles/benev-ubuntu",
        "#bora/apps/rde/blast/benev/blastSocketPeer/nodejs/dockerfiles/benev-windows",
        "#bora/apps/rde/blast/benev/blastSocketPeer/nodejs/dockerfiles/ncs",
        "#bora/apps/rde/blast/benev/blastSocketPeer/nodejs/dockerfiles/bsg-linux",
    ]
    dockerPath = Dir(benevPP / "nodejs" / "dockerfiles").abspath
    for file in dockerFiles:
        dstFile = os.path.join(dockerPath, File(file).name)
        stageNodes += stageEnv.FileCopy(dstFile, file)

    # Stage topology tool files
    topologyToolFiles = [
        "#bora/apps/asyncSocketProxy/bandwidth-capping-proxy.js",
        "#bora/apps/asyncSocketProxy/send-rate-limiter.js",
    ]
    for file in topologyToolFiles:
        dstFile = os.path.join(nodejsPath, File(file).name)
        stageNodes += stageEnv.FileCopy(dstFile, file)

    # Stage SSL certificates and CRL file
    sslCrlFiles = [
        "#bora/apps/rde/blast/benev/blastSocketPeer/nodejs/sslcrlfiles/BenevSslCert.pfx",
        "#bora/apps/rde/blast/benev/blastSocketPeer/nodejs/sslcrlfiles/BenevSslCertRevoked.pfx",
        "#bora/apps/rde/blast/benev/blastSocketPeer/nodejs/sslcrlfiles/revoke.crl",
        "#bora/apps/rde/blast/benev/blastSocketPeer/nodejs/sslcrlfiles/root.crt",
        "#bora/apps/rde/blast/benev/blastSocketPeer/nodejs/sslcrlfiles/intermediate.crt",
    ]
    sslCrlPath = Dir(benevPP / "nodejs" / "sslcrlfiles").abspath
    for file in sslCrlFiles:
        dstFile = os.path.join(sslCrlPath, File(file).name)
        stageNodes += stageEnv.FileCopy(dstFile, file)

    # stage memLeakDetector files
    memLeakDetectorFiles = [
        "#bora/apps/rde/blast/benev/blastSocketPeer/memLeakDetector/memleakdetector.py",
        "#bora/apps/rde/blast/benev/blastSocketPeer/memLeakDetector/vvcObjects.txt",
    ]
    memLeakDetectorPath = Dir(benevPP / "memLeakDetector").abspath
    for file in memLeakDetectorFiles:
        dstFile = os.path.join(memLeakDetectorPath, File(file).name)
        stageNodes += stageEnv.FileCopy(dstFile, file)

    return stageNodes


# Stage benev
stagePP = vmware.PathPrefixer(vmware.pkg.stagePath)
benevPP = stagePP / "benev"
stageNodes = []
for host in vmware.pkg.stageHosts:
    benevEnv = vmware.LookupEnv("benevPeer-env", host)
    stageNodes += StageFiles(stageEnv, benevPP, benevEnv, host)

stageNodes += StageNodeFiles(stageEnv, benevPP)
vmware.Alias("benev", stageNodes)
vmware.RegisterNode(stageNodes, "benev-stage-nodes")
