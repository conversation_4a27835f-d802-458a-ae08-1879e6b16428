﻿<?xml version="1.0" encoding="utf-8"?>

<WixLocalization Culture="en-us" Codepage="1252" xmlns="http://schemas.microsoft.com/wix/2006/localization">
   <String Id="LANGID">1033</String>

   <!-- Installshield Strings -->
   <String Id="IDS_COMPLUS_PROGRESSTEXT_COST">Costing COM+ application: [1]</String>
   <String Id="IDS_COMPLUS_PROGRESSTEXT_INSTALL">Installing COM+ application: [1]</String>
   <String Id="IDS_COMPLUS_PROGRESSTEXT_UNINSTALL">Uninstalling COM+ application: [1]</String>
   <String Id="IDS_PROGMSG_IIS_CREATEAPPPOOL">Creating application pool %s</String>
   <String Id="IDS_PROGMSG_IIS_CREATEAPPPOOLS">Creating application Pools...</String>
   <String Id="IDS_PROGMSG_IIS_CREATEVROOT">Creating IIS virtual directory %s</String>
   <String Id="IDS_PROGMSG_IIS_CREATEVROOTS">Creating IIS virtual directories...</String>
   <String Id="IDS_PROGMSG_IIS_CREATEWEBSERVICEEXTENSION">Creating web service extension</String>
   <String Id="IDS_PROGMSG_IIS_CREATEWEBSERVICEEXTENSIONS">Creating web service extensions...</String>
   <String Id="IDS_PROGMSG_IIS_EXTRACT">Extracting information for IIS virtual directories...</String>
   <String Id="IDS_PROGMSG_IIS_EXTRACTDONE">Extracted information for IIS virtual directories...</String>
   <String Id="IDS_PROGMSG_IIS_REMOVEAPPPOOL">Removing application pool</String>
   <String Id="IDS_PROGMSG_IIS_REMOVEAPPPOOLS">Removing application pools...</String>
   <String Id="IDS_PROGMSG_IIS_REMOVESITE">Removing web site at port %d</String>
   <String Id="IDS_PROGMSG_IIS_REMOVEVROOT">Removing IIS virtual directory %s</String>
   <String Id="IDS_PROGMSG_IIS_REMOVEVROOTS">Removing IIS virtual directories...</String>
   <String Id="IDS_PROGMSG_IIS_REMOVEWEBSERVICEEXTENSION">Removing web service extension</String>
   <String Id="IDS_PROGMSG_IIS_REMOVEWEBSERVICEEXTENSIONS">Removing web service extensions...</String>
   <String Id="IDS_PROGMSG_IIS_ROLLBACKAPPPOOLS">Rolling back application pools...</String>
   <String Id="IDS_PROGMSG_IIS_ROLLBACKVROOTS">Rolling back virtual directory and web site changes...</String>
   <String Id="IDS_PROGMSG_IIS_ROLLBACKWEBSERVICEEXTENSIONS">Rolling back web service extensions...</String>
   <String Id="IDS_PROGMSG_XML_COSTING">Costing XML files...</String>
   <String Id="IDS_PROGMSG_XML_CREATE_FILE">Creating XML file %s...</String>
   <String Id="IDS_PROGMSG_XML_FILES">Performing XML file changes...</String>
   <String Id="IDS_PROGMSG_XML_REMOVE_FILE">Removing XML file %s...</String>
   <String Id="IDS_PROGMSG_XML_ROLLBACK_FILES">Rolling back XML file changes...</String>
   <String Id="IDS_PROGMSG_XML_UPDATE_FILE">Updating XML file %s...</String>


   <!-- LaunchCondition Error Messages -->
   <String Id="MINIMUM_REQUIRED_OS">This product can only be installed on Windows 10, Windows Server 2016 or newer OSes.</String>
   <String Id="DENY_INSTALL_DOMAIN_CONTROLLER">This product can not be installed on a domain controller.</String>
   <String Id="NEED_ADMIN">You need administrator privileges to install/uninstall this software.</String>


   <!-- Feature Table -->
   <String Id="FEATURE_NAME_CORE">Core</String>
   <String Id="FEATURE_DESC_CORE">[ProductName] core functionality</String>
   <String Id="FEATURE_NAME_CORRETTO">Corretto</String>
   <String Id="FEATURE_DESC_CORRETTO">[ProductName] core functionality with the Corretto JDK distribution</String>
   <String Id="FEATURE_NAME_BELLSOFT">Bellsoft</String>
   <String Id="FEATURE_DESC_BELLSOFT">[ProductName] core functionality with the Bellsoft JDK distribution</String>
   <String Id="FEATURE_NAME_RDSH3D">3D RDSH</String>
   <String Id="FEATURE_DESC_RDSH3D">This feature enables hardware 3D acceleration in RDSH and Physical PC sessions.</String>
   <String Id="FEATURE_NAME_CLIENTDRIVEREDIRECTION">Client Drive Redirection</String>
   <String Id="FEATURE_DESC_CLIENTDRIVEREDIRECTION">Allow Horizon Clients to share local drives with remote desktops and applications. If not installed, copy/paste and drag and drop files and folders features will be disabled.</String>
   <String Id="FEATURE_NAME_NGVC">Instant Clone Agent</String>
   <String Id="FEATURE_DESC_NGVC">Instant Clone Agent should only be installed on a virtual machine running on VMware vSphere 7.0 or later.</String>
   <String Id="FEATURE_DESC_PCOIP_PHYSICAL">This feature installs the PCoIP server components onto your desktop.</String>
   <String Id="FEATURE_NAME_RTAV">Real-Time Audio-Video</String>
   <String Id="FEATURE_DESC_RTAV">Real-Time Audio-Video enables users to redirect locally connected audio and video peripherals back to the remote desktop for use.</String>
   <String Id="FEATURE_NAME_VMWPRINT">Horizon Integrated Printing</String>
   <String Id="FEATURE_DESC_VMWPRINT">Horizon Integrated Printing Redirection.</String>

   <String Id="FEATURE_NAME_SCANNERREDIRECTION">Scanner Redirection</String>
   <String Id="FEATURE_DESC_SCANNERREDIRECTION">Enables the Scanner Redirection feature.</String>
   <String Id="FEATURE_NAME_SERIALPORTREDIRECTION">Serial Port Redirection</String>
   <String Id="FEATURE_DESC_SERIALPORTREDIRECTION">Enables the Serial Port Redirection feature.</String>
   <String Id="FEATURE_NAME_SMARTCARD">Smartcard Redirection</String>
   <String Id="FEATURE_DESC_SMARTCARD">Enables the Smartcard Redirection feature.</String>
   <String Id="FEATURE_NAME_TSMMR">TSMMR</String>
   <String Id="FEATURE_DESC_TSMMR">Terminal Services Multimedia Redirection.</String>
   <String Id="FEATURE_NAME_URLREDIRECTION">URL Content Redirection</String>
   <String Id="FEATURE_DESC_URLREDIRECTION">Redirects URL content from a server session to a client device and vice versa.</String>
   <String Id="FEATURE_NAME_UNCREDIRECTION">UNC Path Redirection</String>
   <String Id="FEATURE_DESC_UNCREDIRECTION">Redirects UNC Path from a server session to a client device and vice versa.</String>
   <String Id="FEATURE_NAME_USB">USB Redirection</String>
   <String Id="FEATURE_DESC_USB">USB Redirection. Refer to the "Deploying USB Devices in a Secure Horizon Environment" document for guidance on using USB redirection securely.</String>
   <String Id="FEATURE_NAME_HZNVAUDIO">Horizon Audio</String>
   <String Id="FEATURE_DESC_HZNVAUDIO">Horizon virtual Audio driver</String>
   <String Id="FEATURE_NAME_HTML5MMR">HTML5 Multimedia Redirection</String>
   <String Id="FEATURE_DESC_HTML5MMR">Enables redirection of HTML5 Multimedia</String>
   <String Id="FEATURE_NAME_GEOREDIR">Geolocation Redirection</String>
   <String Id="FEATURE_DESC_GEOREDIR">Enables redirection of client's geolocation to the remote desktop</String>
   <String Id="FEATURE_NAME_SDOSENSOR">SDO Sensor Redirection</String>
   <String Id="FEATURE_DESC_SDOSENSOR">Enables Simple Device Orientation(SDO) Sensor Redirection feature, reports device orientation changes to remote desktop.</String>
   <String Id="FEATURE_NAME_STORAGEDRIVE">Storage Drive Redirection</String>
   <String Id="FEATURE_DESC_STORAGEDRIVE">Enables redirection of client's storage drive to the remote desktop.</String>
   <String Id="FEATURE_NAME_PERFTRACKER">Horizon Performance Tracker</String>
   <String Id="FEATURE_DESC_PERFTRACKER">Enables Horizon Performance Tracker</String>
   <String Id="FEATURE_NAME_HYBRIDLOGON">Hybrid Logon</String>
   <String Id="FEATURE_DESC_HYBRIDLOGON">Enables Hybrid logon which allows an unauthenticated user access to network resources without the need to enter credentials.</String>
   <String Id="FEATURE_NAME_HELPDESK">Help Desk Plugin for Horizon Agent</String>
   <String Id="FEATURE_DESC_HELPDESK">Help Desk Plugin for Horizon Agent.</String>

   <!-- Control Panel Strings -->
   <String Id="Url">https://www.omnissa.com/</String>

   <!-- Firewall Strings -->
   <String Id="BlastUDPFirewallExceptionName">Omnissa Horizon Blast UDP Traffic Exception</String>

   <!-- UI Dialog Strings -->
   <String Id="IDS__DisplayName_Custom">Custom</String>
   <String Id="IDS__DisplayName_Minimal">Minimal</String>
   <String Id="IDS__DisplayName_Typical">Typical</String>
   <String Id="INTEL_UNS_DESC">Provides Intel User Notification services.</String>
   <String Id="IDS_LicenseAcceptance">By installing you agree to the</String>
   <String Id="IDS_GeneralTerms">General Terms</String>
   <String Id="IDS_CANCEL">Cancel</String>
   <String Id="IDS_CANCEL2">&amp;Cancel</String>
   <String Id="IDS_OK">OK</String>
   <String Id="IDS_BACK">&lt; &amp;Back</String>
   <String Id="IDS_NEXT">&amp;Next &gt;</String>
   <String Id="IDS_FINISH">&amp;Finish</String>
   <String Id="IDS__IsCancelDlg_No">&amp;No</String>
   <String Id="IDS__IsCancelDlg_Yes">&amp;Yes</String>
   <String Id="IDS__IsAdminInstallBrowse_LookIn">&amp;Look in:</String>
   <String Id="IDS__IsAdminInstallBrowse_UpOneLevel">Up one level</String>
   <String Id="IDS__IsAdminInstallBrowse_BrowseDestination">Browse to the destination folder.</String>
   <String Id="IDS__IsAdminInstallBrowse_ChangeDestination">{&amp;MSSansBold8}Change Current Destination Folder</String>
   <String Id="IDS__IsAdminInstallBrowse_CreateFolder">Create new folder</String>
   <String Id="IDS__IsAdminInstallBrowse_FolderName">&amp;Folder name:</String>
   <String Id="IDS__IsAdminInstallPoint_Install">&amp;Install</String>
   <String Id="IDS__IsAdminInstallPoint_SpecifyNetworkLocation">Specify a network location for the server image of the product.</String>
   <String Id="IDS__IsAdminInstallPoint_EnterNetworkLocation">Enter the network location or click Change to browse to a location.  Click Install to create a server image of [ProductName] at the specified network location or click Cancel to exit the wizard.</String>
   <String Id="IDS__IsAdminInstallPoint_NetworkLocationFormatted">{&amp;MSSansBold8}Network Location</String>
   <String Id="IDS__IsAdminInstallPoint_NetworkLocation">&amp;Network location:</String>
   <String Id="IDS__IsAdminInstallPoint_Change">&amp;Change...</String>
   <String Id="IDS__IsAdminInstallPointWelcome_Wizard">{&amp;TahomaBold10}Welcome to the installer for [ProductName]</String>
   <String Id="IDS__IsAdminInstallPointWelcome_ServerImage">The installer will create a server image of [ProductName] at a specified network location. To continue, click Next.</String>
   <String Id="ProductVersion">{&amp;Arial9}Product version: [ProductVersionString]</String>
   <String Id="IDS__IsCancelDlg_ConfirmCancel">Are you sure you want to cancel [ProductName] installation?</String>
   <String Id="IDS__IsInstallRolesConfirmDlg_Message">The installer will install required roles to the operating system. Click OK to continue.</String>
   <String Id="ConnectionServer_TitleDesc">Enter the Horizon Connection Server that this machine will connect to.</String>
   <String Id="ConnectionServer_Title">{&amp;MSSansBold8}Register with Horizon Connection Server</String>
   <String Id="ConnectionServer_Text">Enter the server name of a Horizon Connection Server (standard or replica instance) and administrator login credentials to register this machine with the Horizon Connection Server.</String>
   <String Id="ConnectionServer_ServerNote">(hostname or IP address)</String>
   <String Id="ConnectionServerLogin_Text1">&amp;Authenticate as the currently logged on user</String>
   <String Id="ConnectionServerLogin_Text2">Specify administrator &amp;credentials</String>
   <String Id="ConnectionServerLogin_Title">Authentication:</String>
   <String Id="ConnectionServer_Username">&amp;Username:</String>
   <String Id="ConnectionServer_UsernameNote">(Domain\User)</String>
   <String Id="ConnectionServer_Password">&amp;Password:</String>
   <String Id="IDS__IsCustomSelectionDlg_SelectFeatures">Select the program features you want installed.</String>
   <String Id="IDS__IsCustomSelectionDlg_ClickFeatureIcon">Click on an icon in the list below to change how a feature is installed.</String>
   <String Id="IDS__IsCustomSelectionDlg_CustomSetup">{&amp;MSSansBold8}Custom Setup</String>
   <String Id="IDS__IsCustomSelectionDlg_Change">&amp;Change...</String>
   <String Id="IDS__IsCustomSelectionDlg_FeatureDescription">Feature Description</String>
   <String Id="IDS__IsCustomSelectionDlg_InstallTo">Install to:</String>
   <String Id="IDS__IsCustomSelectionDlg_MultilineDescription">Multiline description of the currently selected item</String>
   <String Id="IDS__IsCustomSelectionDlg_FeaturePath">&lt;selected feature path&gt;</String>
   <String Id="IDS__IsCustomSelectionDlg_FeatureSize">Feature size</String>
   <String Id="IDS__IsCustomSelectionDlg_Help">&amp;Help</String>
   <String Id="IDS__IsCustomSelectionDlg_Space">&amp;Space</String>
   <String Id="IDS_SetupTips_CustomSetupDescription">Custom Setup allows you to selectively install program features.</String>
   <String Id="IDS_SetupTips_CustomSetup">{&amp;MSSansBold8}Custom Setup Tips</String>
   <String Id="IDS_SetupTips_WillNotBeInstalled">Will not be installed.</String>
   <String Id="IDS_SetupTips_Advertise">Will be installed on first use. (Available only if the feature supports this option.)</String>
   <String Id="IDS_SetupTips_InstallState">This install state means the feature...</String>
   <String Id="IDS_SetupTips_AllInstalledLocal">Will be completely installed to the local hard drive.</String>
   <String Id="IDS_SetupTips_IconInstallState">The icon next to the feature name indicates the install state of the feature. Click the icon to drop down the install state menu for each feature.</String>
   <String Id="IDS_SetupTips_Network">Will be installed to run from the network. (Available only if the feature supports this option.)</String>
   <String Id="IDS_SetupTips_SubFeaturesInstalledLocal">Will have some subfeatures installed to the local hard drive. (Available only if the feature has subfeatures.)</String>
   <String Id="DesktopConfig_Subtitle">The following information is used to configure the Horizon Desktop feature</String>
   <String Id="DesktopConfig_Title">{&amp;MSSansBold8}Desktop OS Configuration</String>
   <String Id="DesktopConfig_Text">Select the mode for the [ProductName] on this OS:</String>
   <String Id="DesktopConfig_RDSHMode">The required Remote Desktop Session Host (RDSH) role is not installed on this operating system.

Click Next to install the required roles/features. The operating system will have to be restarted when it is complete.

After restart, the [ProductName] installer will have to be re-launched to continue installing it in RDS mode.</String>
   <String Id="DesktopConfig_DesktopMode">This agent will be configured in Desktop VDI Mode.</String>
   <String Id="DesktopConfig_InstallingRolesSuccess">Installing the required operating system roles/features was successful.
Please restart the operating system and re-launch [ProductName] installer.</String>
   <String Id="DesktopConfig_InstallingRolesFail">Error: The installer could not install the required operating system roles/features!</String>
   <String Id="IDS__IsDesktopConfigDlg_RDSMode">RDS Mode</String>
   <String Id="IDS__IsDesktopConfigDlg_DesktopMode">Desktop Mode</String>
   <String Id="InstallRolesConfirm_InstallingRoles">Please wait while the required roles/features are being configured on this system...</String>
   <String Id="IDS__IsFeatureDetailsDlg_DiskSpaceRequirements">{&amp;MSSansBold8}Disk Space Requirements</String>
   <String Id="IDS__IsFeatureDetailsDlg_SpaceRequired">The disk space required for the installation of the selected features.</String>
   <String Id="IDS__IsFeatureDetailsDlg_VolumesTooSmall">The highlighted volumes do not have enough disk space available for the currently selected features. You can remove files from the highlighted volumes, choose to install fewer features onto local drives, or select different destination drives.</String>
   <String Id="IDS__IsFilesInUse_Retry">&amp;Retry</String>
   <String Id="IDS__IsFilesInUse_Ignore">&amp;Ignore</String>
   <String Id="IDS__IsFilesInUse_Exit">&amp;Exit</String>
   <String Id="IDS__IsFilesInUse_FilesInUse">{&amp;MSSansBold8}Files in Use</String>
   <String Id="IDS__IsFilesInUse_FilesInUseMessage">Some files that need to be updated are currently in use.</String>
   <String Id="IDS__IsFilesInUse_ApplicationsUsingFiles">The following applications are using files that need to be updated by this setup. Close these applications and click Retry to continue.

Note: If you see [ProductName] in the following list, please click Ignore to continue.</String>
   <String Id="IDS__IsBrowseFolderDlg_LookIn">&amp;Look in:</String>
   <String Id="IDS__IsBrowseFolderDlg_UpOneLevel">Up one level</String>
   <String Id="IDS__IsBrowseFolderDlg_BrowseDestFolder">Browse to the destination folder.</String>
   <String Id="IDS__IsBrowseFolderDlg_ChangeCurrentFolder">{&amp;MSSansBold8}Change Current Destination Folder</String>
   <String Id="IDS__IsBrowseFolderDlg_CreateFolder">Create new folder</String>
   <String Id="IDS__IsBrowseFolderDlg_FolderName">&amp;Folder name:</String>
   <String Id="IDS__IsWelcomeDlg_WelcomeProductName">{&amp;TahomaBold10}Welcome to the Installation Wizard for [ProductName]</String>
   <String Id="IDS__IsWelcomeDlg_InstallProductName">The installation wizard will install [ProductName] on your computer. To continue, click Next.</String>
   <String Id="InstallWelcome_UpgradeLine1">The installation wizard will upgrade [ProductName] on your computer. To continue, click Next.</String>
   <String Id="IDS__IsWelcomeDlg_WarningCopyright">Copyright © [CopyrightYears] Omnissa. All rights reserved. This product is protected by copyright and intellectual property laws in the United States and other countries as well as by international treaties. &quot;Omnissa&quot; refers to Omnissa, LLC, Omnissa International Unlimited Company, and/or their subsidiaries.</String>
   <String Id="IpProtocolConfig_DlgDesc">Select the communication protocol</String>
   <String Id="IpProtocolConfig_DlgTitle">{&amp;MSSansBold8}Network protocol configuration</String>
   <String Id="GoldenImage_DlgDesc">Select whether this machine will be used as a Golden Image</String>
   <String Id="GoldenImage_DlgTitle">{&amp;MSSansBold8}Golden Image Selection</String>
   <String Id="GoldenImage_CheckBoxText">This machine will be used as a Golden Image</String>
   <String Id="ConnectionServer_IpText">Specify the protocol to be used to configure this Horizon Agent instance:</String>
   <String Id="ConnectionServer_IPv4Desc">This agent will be configured to choose the IPv4 protocol for establishing all connections.</String>
   <String Id="ConnectionServer_IPv6Desc">This agent will be configured to choose the IPv6 protocol for establishing all connections.</String>
   <String Id="ConnectionServer_Dual4Desc">This agent will be configured to support mixed IP modes preferring IPv4 protocol for establishing all connections.</String>
   <String Id="ConnectionServer_Dual6Desc">This agent will be configured to support mixed IP modes preferring IPv6 protocol for establishing all connections.</String>
   <String Id="IpProtocolConfig_FipsText">Specify whether or not to install this product with FIPS Compliant Cryptography.</String>
   <String Id="IpProtocolConfig_FipsDisabledDesc">This agent instance will operate without FIPS compliance.</String>
   <String Id="IpProtocolConfig_FipsEnabledDesc">This agent instance will be configured for FIPS compliant cryptography.</String>
   <String Id="FipsConfig_Disabled">Disabled</String>
   <String Id="FipsConfig_Enabled">Enabled</String>
   <String Id="IDS__AgreeToLicense_0">I &amp;do not accept the General Terms</String>
   <String Id="IDS__AgreeToLicense_1">I &amp;accept the General Terms</String>
   <String Id="IDS__IsLicenseDlg_ReadLicenseAgreement">Please read the following General Terms carefully.</String>
   <String Id="IDS__IsLicenseDlg_LicenseAgreement">{&amp;MSSansBold8}General Terms</String>
   <String Id="IDS__IsMaintenanceDlg_Modify">{&amp;MSSansBold8}&amp;Modify</String>
   <String Id="IDS__IsMaintenanceDlg_Repair">{&amp;MSSansBold8}Re&amp;pair</String>
   <String Id="IDS__IsMaintenanceDlg_Remove">{&amp;MSSansBold8}&amp;Remove</String>
   <String Id="IDS__IsMaintenanceDlg_MaitenanceOptions">Modify, repair, or remove the program.</String>
   <String Id="IDS__IsMaintenanceDlg_ProgramMaintenance">{&amp;MSSansBold8}Program Maintenance</String>
   <String Id="IDS__IsMaintenanceDlg_ModifyMessage">Allows users to change which features are installed.</String>
   <String Id="IDS__IsMaintenanceDlg_RepairMessage">Repair installation errors in the program. This option fixes missing or corrupt files, shortcuts, and registry entries.</String>
   <String Id="IDS__IsMaintenanceDlg_RemoveProductName">Remove [ProductName] from your computer.</String>
   <String Id="IDS__IsMaintenanceWelcome_WizardWelcome">{&amp;TahomaBold10}Welcome to the installer for [ProductName]</String>
   <String Id="IDS__IsMaintenanceWelcome_MaintenanceOptionsDescription">The installer will allow you to modify, repair, or remove [ProductName]. To continue, click Next.</String>
   <String Id="IDS_PRODUCTNAME_INSTALLSHIELD">[ProductName] - Installation Wizard</String>
   <String Id="IDS__IsMsiRMFilesInUse_CloseRestart">Automatically close and attempt to restart applications.</String>
   <String Id="IDS__IsMsiRMFilesInUse_RebootAfter">Do not close applications. (A reboot will be required.)</String>
   <String Id="IDS__IsMsiRMFilesInUse_ApplicationsUsingFiles">The following applications are using files that need to be updated by this setup.</String>
   <String Id="IDS__IsDiskSpaceDlg_OutOfDiskSpace">{&amp;MSSansBold8}Out of Disk Space</String>
   <String Id="IDS__IsDiskSpaceDlg_DiskSpace">Disk space required for the installation exceeds available disk space.</String>
   <String Id="IDS__IsDiskSpaceDlg_HighlightedVolumes">The highlighted volumes do not have enough disk space available for the currently selected features. You can remove files from the highlighted volumes, or cancel the installation.</String>
   <String Id="RdpChoice_EnableRdp">&amp;Enable the Remote Desktop capability on this computer</String>
   <String Id="RdpChoice_NoRdp">&amp;Do not enable the Remote Desktop capability on this computer</String>
   <String Id="RdpConfig_Subtitle">The following information is used to configure the Remote Desktop feature</String>
   <String Id="RdpConfig_Title">{&amp;MSSansBold8}Remote Desktop Protocol Configuration</String>
   <String Id="RdpConfig_Text">[ProductName] requires the Remote Desktop support to be turned on. Firewall exceptions will be added for the RDP port [RDP_PORT_NUMBER] and the View Framework channel [FRAMEWORK_CHANNEL_PORT]. What would you like to do?</String>
   <String Id="IDS__IsVerifyReadyDlg_Install">&amp;Install</String>
   <String Id="IDS__IsVerifyReadyDlg_WizardReady">The wizard is ready to begin installation.</String>
   <String Id="ReadyToInstall_RdshNote">NOTE: The RDS role is not enabled on this OS. [ProductName] will support only single desktop connections.</String>
   <String Id="IDS__IsVerifyReadyDlg_ClickInstall">Click Install to begin the installation or Cancel to exit the wizard.</String>
   <String Id="IDS__IsVerifyReadyDlg_ReadyRepair">{&amp;MSSansBold8}Ready to Repair the Program</String>
   <String Id="IDS__IsVerifyReadyDlg_ReadyInstall">{&amp;MSSansBold8}Ready to Install the Program</String>
   <String Id="ReadyToInstall_InstallDir">[ProductName] will be installed in:

[INSTALLDIR]</String>
   <String Id="ReadyToInstall_MsgSanPolicy_NGVC">NOTE: The VDS SAN policy will be set to &quot;Online All&quot; as required by the Instant Clone Agent (NGVC) feature.</String>
   <String Id="IDS__IsVerifyRemoveAllDlg_Remove">&amp;Remove</String>
   <String Id="IDS__IsVerifyRemoveAllDlg_ChoseRemoveProgram">You have chosen to remove the program from your system.</String>
   <String Id="IDS__IsVerifyRemoveAllDlg_ClickRemove">Click Remove to remove [ProductName] from your computer. After removal, this program will no longer be available for use.</String>
   <String Id="IDS__IsVerifyRemoveAllDlg_ClickBack">If you want to review or change any settings, click Back.</String>
   <String Id="IDS__IsVerifyRemoveAllDlg_RemoveProgram">{&amp;MSSansBold8}Remove the Program</String>
   <String Id="IDS__IsFatalError_NotModified">Your system has not been modified. To complete installation at another time, please run setup again.</String>
   <String Id="IDS__IsFatalError_ClickFinish">Click Finish to exit the wizard.</String>
   <String Id="IDS__IsFatalError_KeepOrRestore">You can either keep any existing installed elements on your system to continue this installation at a later time or you can restore your system to its original state prior to the installation.</String>
   <String Id="IDS__IsFatalError_RestoreOrContinueLater">Click Restore or Continue Later to exit the wizard.</String>
   <String Id="IDS__IsFatalError_WizardCompleted">{&amp;TahomaBold10}Installer Completed</String>
   <String Id="IDS__IsFatalError_WizardInterrupted">The wizard was interrupted before [ProductName] could be completely installed.</String>
   <String Id="IDS__IsFatalError_UninstallWizardInterrupted">The wizard was interrupted before [ProductName] could be completely uninstalled.</String>
   <String Id="IDS__IsExitDialog_WizardCompleted">{&amp;TahomaBold10}Installer Completed</String>
   <String Id="IDS__IsExitDialog_InstallSuccess">The installer has successfully installed [ProductName]. Click Finish to exit the wizard.</String>
   <String Id="IDS__IsExitDialog_UninstallSuccess">The installer has successfully uninstalled [ProductName]. Click Finish to exit the wizard.</String>
   <String Id="IDS__IsExitDialog_InstallingRolesSuccess">The installer has successfully configured the operating system with the roles/features required for installing [ProductName] in RDS Mode.

Click Finish to exit the wizard.</String>
   <String Id="IDS__IsErrorDlg_InstallerInfo">[ProductName] Installer Information</String>
   <String Id="IDS__IsErrorDlg_Abort">&amp;Abort</String>
   <String Id="IDS__IsErrorDlg_Yes">&amp;Yes</String>
   <String Id="IDS__IsErrorDlg_No">&amp;No</String>
   <String Id="IDS__IsErrorDlg_Ignore">&amp;Ignore</String>
   <String Id="IDS__IsErrorDlg_OK">&amp;OK</String>
   <String Id="IDS__IsErrorDlg_Retry">&amp;Retry</String>
   <String Id="IDS__IsInitDlg_WelcomeWizard">{&amp;TahomaBold10}Welcome to the installer for [ProductName]</String>
   <String Id="IDS__IsInitDlg_PreparingWizard">[ProductName] Setup is preparing the installer which will guide you through the program setup process.  Please wait.</String>
   <String Id="IDS__IsUserExit_NotModified">Your system has not been modified. To install this program at a later time, please run the installation again.</String>
   <String Id="IDS__IsUserExit_ClickFinish">Click Finish to exit the wizard.</String>
   <String Id="IDS__IsUserExit_KeepOrRestore">You can either keep any existing installed elements on your system to continue this installation at a later time or you can restore your system to its original state prior to the installation.</String>
   <String Id="IDS__IsUserExit_RestoreOrContinue">Click Restore or Continue Later to exit the wizard.</String>
   <String Id="IDS__IsUserExit_WizardCompleted">{&amp;TahomaBold10}Installer Completed</String>
   <String Id="IDS__IsUserExit_WizardInterrupted">The wizard was interrupted before [ProductName] could be completely installed.</String>
   <String Id="IDS__IsUserExit_UninstallWizardInterrupted">The wizard was interrupted before [ProductName] could be completely uninstalled.</String>
   <String Id="IDS__IsProgressDlg_UninstallingFeatures2">The program features you selected are being installed.</String>
   <String Id="IDS__IsProgressDlg_UninstallingFeatures">The program features you selected are being uninstalled.</String>
   <String Id="IDS__IsProgressDlg_WaitUninstall2">Please wait while the installer installs [ProductName]. This may take several minutes.</String>
   <String Id="IDS__IsProgressDlg_WaitUninstall">Please wait while the installer uninstalls [ProductName]. This may take several minutes.</String>
   <String Id="IDS__IsProgressDlg_InstallingProductName">{&amp;MSSansBold8}Installing [ProductName]</String>
   <String Id="IDS__IsProgressDlg_Uninstalling">{&amp;MSSansBold8}Uninstalling [ProductName]</String>
   <String Id="IDS__IsProgressDlg_SecHidden">(Hidden for now)Sec.</String>
   <String Id="IDS__IsProgressDlg_Status">Status:</String>
   <String Id="IDS__IsProgressDlg_Hidden">(Hidden for now)</String>
   <String Id="IDS__IsProgressDlg_HiddenTimeRemaining">(Hidden for now)Estimated time remaining:</String>
   <String Id="IDS__IsProgressDlg_ProgressDone">Progress done</String>
   <String Id="IDS__IsResumeDlg_Resuming">{&amp;TahomaBold10}Resuming the installer for [ProductName]</String>
   <String Id="IDS__IsResumeDlg_ResumeSuspended">The installer will complete the suspended installation of [ProductName] on your computer. To continue, click Next.</String>
   <String Id="IDS__IsResumeDlg_WizardResume">The installer will complete the installation of [ProductName] on your computer. To continue, click Next.</String>


   <!-- Error Strings -->
   <String Id="MsgWSWCInstalled">Unable to continue the installation because an incompatible version of the Horizon Client is already installed on this computer.

To continue installing the [ProductName] uninstall the Horizon Client and then re-run this installer.</String>
   <String Id="MsgClientRunning">Agent installation cannot continue. An active Horizon Client session has been detected.</String>
   <String Id="MsgDowngradeDetected">The installer has detected that a newer version of [ProductName] is already installed.</String>
   <String Id="MsgManualUninstallRequired">This installer cannot perform an upgrade over the existing product installation. Please uninstall the existing product before continuing with this installation.</String>
   <String Id="MsgMustReboot">The system must be rebooted before installation can continue.</String>
   <String Id="MsgServerInstalled">Unable to continue the installation because the Horizon Connection Server is already installed on this computer.

To continue installing the [ProductName] uninstall the Connection Server and then re-run this installer.</String>
   <String Id="MsgUnsupportedOldVersion">Unable to install because an older unsupported version of this product is already installed. Please uninstall it and reboot the system before installing this product.</String>
   <String Id="MsgUrlRedirectionInstalled">You are attempting to install [ProductName] with URL redirection enabled, but URL redirection is already enabled by the Horizon Client. This is not supported, if you continue the Agent will be installed without URL redirection.  You must uninstall the Client and install the Agent first to achieve URL redirection in Agent mode.</String>
   <String Id="MsgUNCRedirectionInstalled">You are attempting to install [ProductName] with UNC redirection enabled, but UNC redirection is already enabled by the Horizon Client. This is not supported, if you continue the Agent will be installed without UNC redirection.  You must uninstall the Client and install the Agent first to achieve UNC redirection in Agent mode.</String>
   <String Id="MsgVdmLoopbackIp">An error occurred attempting to establish the 'localhost' IP address. Make sure you have selected an IP protocol, and that the protocol is installed on this machine.</String>
   <String Id="MsgWindowsUpdateInProgress">A Windows Update is currently in progress. Please complete the Windows Update and reboot the system before installing the Horizon Agent.</String>
   <String Id="MsgWindowsUpdateAndRestartPending">The system must be updated and rebooted before installation can continue.</String>
   <String Id="NoRepairAllowed">An active Horizon Session is in progress. [ProductName] repair can not continue.</String>
   <String Id="MsgInstallationAbortifSVIInstalled">This installer cannot perform an upgrade over the existing product installation. The Horizon View Composer feature is no longer supported from version 8.1. To install this build first uninstall the previous build.</String>
   <String Id="SettingsFileInvalid">Failed to parse installer settings file: "[SETTINGS_FILE]"

Error at line [SettingsFileErrorLine].</String>


   <!-- Action Text Strings -->
   <String Id="ActionText_RdpConfig">Performing RDP configuration</String>
   <String Id="ConfigUserInit">Registering UserInit process: wssm.exe</String>
   <String Id="IDS_ACTIONTEXT_1">[1]</String>
   <String Id="IDS_ACTIONTEXT_1b">[1]</String>
   <String Id="IDS_ACTIONTEXT_1c">[1]</String>
   <String Id="IDS_ACTIONTEXT_1d">[1]</String>
   <String Id="IDS_ACTIONTEXT_Advertising">Advertising application</String>
   <String Id="IDS_ACTIONTEXT_AllocatingRegistry">Allocating registry space</String>
   <String Id="IDS_ACTIONTEXT_AppCommandLine">Application: [1], Command line: [2]</String>
   <String Id="IDS_ACTIONTEXT_AppId">AppId: [1]{{, AppType: [2]}}</String>
   <String Id="IDS_ACTIONTEXT_AppIdAppTypeRSN">AppId: [1]{{, AppType: [2], Users: [3], RSN: [4]}}</String>
   <String Id="IDS_ACTIONTEXT_Application">Application: [1]</String>
   <String Id="IDS_ACTIONTEXT_BindingExes">Binding executables</String>
   <String Id="IDS_ACTIONTEXT_ClassId">Class ID: [1]</String>
   <String Id="IDS_ACTIONTEXT_ClsID">Class ID: [1]</String>
   <String Id="IDS_ACTIONTEXT_ComponentIDQualifier">Component ID: [1], Qualifier: [2]</String>
   <String Id="IDS_ACTIONTEXT_ComponentIdQualifier2">Component ID: [1], Qualifier: [2]</String>
   <String Id="IDS_ACTIONTEXT_ComputingSpace">Computing space requirements</String>
   <String Id="IDS_ACTIONTEXT_ComputingSpace2">Computing space requirements</String>
   <String Id="IDS_ACTIONTEXT_ComputingSpace3">Computing space requirements</String>
   <String Id="IDS_ACTIONTEXT_ContentTypeExtension">MIME Content Type: [1], Extension: [2]</String>
   <String Id="IDS_ACTIONTEXT_ContentTypeExtension2">MIME Content Type: [1], Extension: [2]</String>
   <String Id="IDS_ACTIONTEXT_CopyingNetworkFiles">Copying files to the network</String>
   <String Id="IDS_ACTIONTEXT_CopyingNewFiles">Copying new files</String>
   <String Id="IDS_ACTIONTEXT_CreatingDuplicate">Creating duplicate files</String>
   <String Id="IDS_ACTIONTEXT_CreatingFolders">Creating folders</String>
   <String Id="IDS_ACTIONTEXT_CreatingShortcuts">Creating shortcuts</String>
   <String Id="IDS_ACTIONTEXT_DeletingServices">Deleting services</String>
   <String Id="IDS_ACTIONTEXT_EnvironmentStrings">Updating environment strings</String>
   <String Id="IDS_ACTIONTEXT_EvaluateLaunchConditions">Evaluating launch conditions</String>
   <String Id="IDS_ACTIONTEXT_Extension">Extension: [1]</String>
   <String Id="IDS_ACTIONTEXT_Extension2">Extension: [1]</String>
   <String Id="IDS_ACTIONTEXT_Feature">Feature: [1]</String>
   <String Id="IDS_ACTIONTEXT_FeatureColon">Feature: [1]</String>
   <String Id="IDS_ACTIONTEXT_File">File: [1]</String>
   <String Id="IDS_ACTIONTEXT_File2">File: [1]</String>
   <String Id="IDS_ACTIONTEXT_FileDependencies">File: [1],  Dependencies: [2]</String>
   <String Id="IDS_ACTIONTEXT_FileDir">File: [1], Directory: [9]</String>
   <String Id="IDS_ACTIONTEXT_FileDir2">File: [1], Directory: [9]</String>
   <String Id="IDS_ACTIONTEXT_FileDir3">File: [1], Directory: [9]</String>
   <String Id="IDS_ACTIONTEXT_FileDirSize">File: [1], Directory: [9], Size: [6]</String>
   <String Id="IDS_ACTIONTEXT_FileDirSize2">File: [1],  Directory: [9],  Size: [6]</String>
   <String Id="IDS_ACTIONTEXT_FileDirSize3">File: [1],  Directory: [9],  Size: [6]</String>
   <String Id="IDS_ACTIONTEXT_FileDirSize4">File: [1],  Directory: [2],  Size: [3]</String>
   <String Id="IDS_ACTIONTEXT_FileDirectorySize">File: [1],  Directory: [9],  Size: [6]</String>
   <String Id="IDS_ACTIONTEXT_FileFolder">File: [1], Folder: [2]</String>
   <String Id="IDS_ACTIONTEXT_FileFolder2">File: [1], Folder: [2]</String>
   <String Id="IDS_ACTIONTEXT_FileSectionKeyValue">File: [1],  Section: [2],  Key: [3], Value: [4]</String>
   <String Id="IDS_ACTIONTEXT_FileSectionKeyValue2">File: [1],  Section: [2],  Key: [3], Value: [4]</String>
   <String Id="IDS_ACTIONTEXT_Folder">Folder: [1]</String>
   <String Id="IDS_ACTIONTEXT_Folder1">Folder: [1]</String>
   <String Id="IDS_ACTIONTEXT_Font">Font: [1]</String>
   <String Id="IDS_ACTIONTEXT_Font2">Font: [1]</String>
   <String Id="IDS_ACTIONTEXT_FoundApp">Found application: [1]</String>
   <String Id="IDS_ACTIONTEXT_FreeSpace">Free space: [1]</String>
   <String Id="IDS_ACTIONTEXT_GeneratingScript">Generating script operations for action:</String>
   <String Id="IDS_ACTIONTEXT_InitializeODBCDirs">Initializing ODBC directories</String>
   <String Id="IDS_ACTIONTEXT_InstallODBC">Installing ODBC components</String>
   <String Id="IDS_ACTIONTEXT_InstallServices">Installing new services</String>
   <String Id="IDS_ACTIONTEXT_InstallingSystemCatalog">Installing system catalog</String>
   <String Id="IDS_ACTIONTEXT_KeyName">Key: [1], Name: [2]</String>
   <String Id="IDS_ACTIONTEXT_KeyNameValue">Key: [1], Name: [2], Value: [3]</String>
   <String Id="IDS_ACTIONTEXT_LibId">LibID: [1]</String>
   <String Id="IDS_ACTIONTEXT_Libid2">LibID: [1]</String>
   <String Id="IDS_ACTIONTEXT_MigratingFeatureStates">Migrating feature states from related applications</String>
   <String Id="IDS_ACTIONTEXT_MovingFiles">Moving files</String>
   <String Id="IDS_ACTIONTEXT_NameValueAction">Name: [1], Value: [2], Action [3]</String>
   <String Id="IDS_ACTIONTEXT_NameValueAction2">Name: [1], Value: [2], Action [3]</String>
   <String Id="IDS_ACTIONTEXT_PatchingFiles">Patching files</String>
   <String Id="IDS_ACTIONTEXT_ProgID">ProgID: [1]</String>
   <String Id="IDS_ACTIONTEXT_ProgID2">ProgID: [1]</String>
   <String Id="IDS_ACTIONTEXT_PropertySignature">Property: [1], Signature: [2]</String>
   <String Id="IDS_ACTIONTEXT_PublishProductFeatures">Publishing product features</String>
   <String Id="IDS_ACTIONTEXT_PublishProductInfo">Publishing product information</String>
   <String Id="IDS_ACTIONTEXT_PublishingQualifiedComponents">Publishing qualified components</String>
   <String Id="IDS_ACTIONTEXT_RegUser">Registering user</String>
   <String Id="IDS_ACTIONTEXT_RegisterClassServer">Registering class servers</String>
   <String Id="IDS_ACTIONTEXT_RegisterExtensionServers">Registering extension servers</String>
   <String Id="IDS_ACTIONTEXT_RegisterFonts">Registering fonts</String>
   <String Id="IDS_ACTIONTEXT_RegisterMimeInfo">Registering MIME info</String>
   <String Id="IDS_ACTIONTEXT_RegisterTypeLibs">Registering type libraries</String>
   <String Id="IDS_ACTIONTEXT_RegisteringComPlus">Registering COM+ Applications and Components</String>
   <String Id="IDS_ACTIONTEXT_RegisteringModules">Registering modules</String>
   <String Id="IDS_ACTIONTEXT_RegisteringProduct">Registering product</String>
   <String Id="IDS_ACTIONTEXT_RegisteringProgIdentifiers">Registering program identifiers</String>
   <String Id="IDS_ACTIONTEXT_RemoveApps">Removing applications</String>
   <String Id="IDS_ACTIONTEXT_RemovingBackup">Removing backup files</String>
   <String Id="IDS_ACTIONTEXT_RemovingDuplicates">Removing duplicated files</String>
   <String Id="IDS_ACTIONTEXT_RemovingFiles">Removing files</String>
   <String Id="IDS_ACTIONTEXT_RemovingFolders">Removing folders</String>
   <String Id="IDS_ACTIONTEXT_RemovingIni">Removing INI file entries</String>
   <String Id="IDS_ACTIONTEXT_RemovingMoved">Removing moved files</String>
   <String Id="IDS_ACTIONTEXT_RemovingODBC">Removing ODBC components</String>
   <String Id="IDS_ACTIONTEXT_RemovingRegistry">Removing system registry values</String>
   <String Id="IDS_ACTIONTEXT_RemovingShortcuts">Removing shortcuts</String>
   <String Id="IDS_ACTIONTEXT_RollingBack">Rolling back action:</String>
   <String Id="IDS_ACTIONTEXT_SearchForRelated">Searching for related applications</String>
   <String Id="IDS_ACTIONTEXT_SearchInstalled">Searching for installed applications</String>
   <String Id="IDS_ACTIONTEXT_SearchingQualifyingProducts">Searching for qualifying products</String>
   <String Id="IDS_ACTIONTEXT_ServerConfig">Configuring Horizon Connection Server</String>
   <String Id="IDS_ACTIONTEXT_Service">Service: [1]</String>
   <String Id="IDS_ACTIONTEXT_Service2">Service: [2]</String>
   <String Id="IDS_ACTIONTEXT_Service3">Service: [1]</String>
   <String Id="IDS_ACTIONTEXT_Service4">Service: [1]</String>
   <String Id="IDS_ACTIONTEXT_Shortcut">Shortcut: [1]</String>
   <String Id="IDS_ACTIONTEXT_Shortcut1">Shortcut: [1]</String>
   <String Id="IDS_ACTIONTEXT_StartingServices">Starting services</String>
   <String Id="IDS_ACTIONTEXT_StoppingServices">Stopping services</String>
   <String Id="IDS_ACTIONTEXT_UnpublishProductFeatures">Unpublishing product features</String>
   <String Id="IDS_ACTIONTEXT_UnpublishQualified">Unpublishing Qualified Components</String>
   <String Id="IDS_ACTIONTEXT_UnpublishingProductInfo">Unpublishing product information</String>
   <String Id="IDS_ACTIONTEXT_UnregTypeLibs">Unregistering type libraries</String>
   <String Id="IDS_ACTIONTEXT_UnregisterClassServers">Unregister class servers</String>
   <String Id="IDS_ACTIONTEXT_UnregisterExtensionServers">Unregistering extension servers</String>
   <String Id="IDS_ACTIONTEXT_UnregisterModules">Unregistering modules</String>
   <String Id="IDS_ACTIONTEXT_UnregisteringComPlus">Unregistering COM+ Applications and Components</String>
   <String Id="IDS_ACTIONTEXT_UnregisteringFonts">Unregistering fonts</String>
   <String Id="IDS_ACTIONTEXT_UnregisteringMimeInfo">Unregistering MIME info</String>
   <String Id="IDS_ACTIONTEXT_UnregisteringProgramIds">Unregistering program identifiers</String>
   <String Id="IDS_ACTIONTEXT_UpdateComponentRegistration">Updating component registration</String>
   <String Id="IDS_ACTIONTEXT_UpdateEnvironmentStrings">Updating environment strings</String>
   <String Id="IDS_ACTIONTEXT_Validating">Validating install</String>
   <String Id="IDS_ACTIONTEXT_BlastUDPInstall">Setting up UDP communication settings</String>
   <String Id="IDS_ACTIONTEXT_BlastUDPUninstall">Cleaning up UDP communication settings</String>
   <String Id="IDS_ACTIONTEXT_WritingINI">Writing INI file values</String>
   <String Id="IDS_ACTIONTEXT_WritingRegistry">Writing system registry values</String>
   <String Id="UnconfigUserInit">Unregistering UserInit process: wssm.exe</String>
   <String Id="VM_WaitForpairing_ProgressText">Waiting for agent pairing to complete...</String>

   <!-- UIText Strings -->
   <String Id="IDS_UITEXT_Available">Available</String>
   <String Id="IDS_UITEXT_Bytes">bytes</String>
   <String Id="IDS_UITEXT_CompilingFeaturesCost">Compiling cost for this feature...</String>
   <String Id="IDS_UITEXT_Differences">Differences</String>
   <String Id="IDS_UITEXT_DiskSize">Disk Size</String>
   <String Id="IDS_UITEXT_FeatureCompletelyRemoved">This feature will be completely removed.</String>
   <String Id="IDS_UITEXT_FeatureContinueNetwork">This feature will continue to be run from the network</String>
   <String Id="IDS_UITEXT_FeatureFreeSpace">This feature frees up [1] on your hard drive.</String>
   <String Id="IDS_UITEXT_FeatureInstalledCD">This feature, and all subfeatures, will be installed to run from the CD.</String>
   <String Id="IDS_UITEXT_FeatureInstalledCD2">This feature will be installed to run from CD.</String>
   <String Id="IDS_UITEXT_FeatureInstalledLocal">This feature, and all subfeatures, will be installed on local hard drive.</String>
   <String Id="IDS_UITEXT_FeatureInstalledLocal2">This feature will be installed on local hard drive.</String>
   <String Id="IDS_UITEXT_FeatureInstalledNetwork">This feature, and all subfeatures, will be installed to run from the network.</String>
   <String Id="IDS_UITEXT_FeatureInstalledNetwork2">This feature will be installed to run from network.</String>
   <String Id="IDS_UITEXT_FeatureInstalledRequired">Will be installed when required.</String>
   <String Id="IDS_UITEXT_FeatureInstalledWhenRequired">This feature will be set to be installed when required.</String>
   <String Id="IDS_UITEXT_FeatureInstalledWhenRequired2">This feature will be installed when required.</String>
   <String Id="IDS_UITEXT_FeatureLocal">This feature will be installed on the local hard drive.</String>
   <String Id="IDS_UITEXT_FeatureLocal2">This feature will be installed on your local hard drive.</String>
   <String Id="IDS_UITEXT_FeatureNetwork">This feature will be installed to run from the network.</String>
   <String Id="IDS_UITEXT_FeatureNetwork2">This feature will be available to run from the network.</String>
   <String Id="IDS_UITEXT_FeatureNotAvailable">This feature will not be available.</String>
   <String Id="IDS_UITEXT_FeatureOnCD">This feature will be installed to run from CD.</String>
   <String Id="IDS_UITEXT_FeatureOnCD2">This feature will be available to run from CD.</String>
   <String Id="IDS_UITEXT_FeatureRemainLocal">This feature will remain on your local hard drive.</String>
   <String Id="IDS_UITEXT_FeatureRemoveNetwork">This feature will be removed from your local hard drive, but will be still available to run from the network.</String>
   <String Id="IDS_UITEXT_FeatureRemovedCD">This feature will be removed from your local hard drive but will still be available to run from CD.</String>
   <String Id="IDS_UITEXT_FeatureRemovedUnlessRequired">This feature will be removed from your local hard drive but will be set to be installed when required.</String>
   <String Id="IDS_UITEXT_FeatureRequiredSpace">This feature requires [1] on your hard drive.</String>
   <String Id="IDS_UITEXT_FeatureRunFromCD">This feature will continue to be run from the CD</String>
   <String Id="IDS_UITEXT_FeatureSpaceFree">This feature frees up [1] on your hard drive. It has [2] of [3] subfeatures selected. The subfeatures free up [4] on your hard drive.</String>
   <String Id="IDS_UITEXT_FeatureSpaceFree2">This feature frees up [1] on your hard drive. It has [2] of [3] subfeatures selected. The subfeatures require [4] on your hard drive.</String>
   <String Id="IDS_UITEXT_FeatureSpaceFree3">This feature requires [1] on your hard drive. It has [2] of [3] subfeatures selected. The subfeatures free up [4] on your hard drive.</String>
   <String Id="IDS_UITEXT_FeatureSpaceFree4">This feature requires [1] on your hard drive. It has [2] of [3] subfeatures selected. The subfeatures require [4] on your hard drive.</String>
   <String Id="IDS_UITEXT_FeatureUnavailable">This feature will become unavailable.</String>
   <String Id="IDS_UITEXT_FeatureUninstallNoNetwork">This feature will be uninstalled completely, and you won't be able to run it from the network.</String>
   <String Id="IDS_UITEXT_FeatureWasCD">This feature was run from the CD but will be set to be installed when required.</String>
   <String Id="IDS_UITEXT_FeatureWasCDLocal">This feature was run from the CD but will be installed on the local hard drive.</String>
   <String Id="IDS_UITEXT_FeatureWasOnNetworkInstalled">This feature was run from the network but will be installed when required.</String>
   <String Id="IDS_UITEXT_FeatureWasOnNetworkLocal">This feature was run from the network but will be installed on the local hard drive.</String>
   <String Id="IDS_UITEXT_FeatureWillBeUninstalled">This feature will be uninstalled completely, and you won't be able to run it from CD.</String>
   <String Id="IDS_UITEXT_Folder">Fldr|New Folder</String>
   <String Id="IDS_UITEXT_GB">GB</String>
   <String Id="IDS_UITEXT_KB">KB</String>
   <String Id="IDS_UITEXT_MB">MB</String>
   <String Id="IDS_UITEXT_Required">Required</String>
   <String Id="IDS_UITEXT_TimeRemaining">Time remaining: {[1] min }[2] sec</String>
   <String Id="IDS_UITEXT_Volume">Volume</String>


   <!-- Error Table Strings -->
   <String Id="IDS_ERROR_0">{{Fatal error: }}</String>
   <String Id="IDS_ERROR_1">Error [1].</String>
   <String Id="IDS_ERROR_2">Warning [1].</String>
   <String Id="IDS_ERROR_4">Info [1].</String>
   <String Id="IDS_ERROR_5">Internal Error [1]. [2]{, [3]}{, [4]}</String>
   <String Id="IDS_ERROR_7">{{Disk full: }}</String>
   <String Id="IDS_ERROR_8">Action [Time]: [1]. [2]</String>
   <String Id="IDS_ERROR_9">[ProductName]</String>
   <String Id="IDS_ERROR_10">{[2]}{, [3]}{, [4]}</String>
   <String Id="IDS_ERROR_11">Message type: [1], Argument: [2]</String>
   <String Id="IDS_ERROR_12">=== Logging started: [Date]  [Time] ===</String>
   <String Id="IDS_ERROR_13">=== Logging stopped: [Date]  [Time] ===</String>
   <String Id="IDS_ERROR_14">Action start [Time]: [1].</String>
   <String Id="IDS_ERROR_15">Action ended [Time]: [1]. Return value [2].</String>
   <String Id="IDS_ERROR_16">Time remaining: {[1] minutes }{[2] seconds}</String>
   <String Id="IDS_ERROR_17">Out of memory. Shut down other applications before retrying.</String>
   <String Id="IDS_ERROR_18">Installer is no longer responding.</String>
   <String Id="IDS_ERROR_19">Installer terminated prematurely.</String>
   <String Id="IDS_ERROR_20">Please wait while Windows configures [ProductName]</String>
   <String Id="IDS_ERROR_21">Gathering required information...</String>
   <String Id="IDS_ERROR_22">Removing older versions of this application</String>
   <String Id="IDS_ERROR_23">Preparing to remove older versions of this application</String>
   <String Id="IDS_ERROR_32">{[ProductName] }Setup completed successfully.</String>
   <String Id="IDS_ERROR_33">{[ProductName] }Setup failed.</String>
   <String Id="IDS_ERROR_1101">Error reading from file: [2]. {{ System error [3].}}  Verify that the file exists and that you can access it.</String>
   <String Id="IDS_ERROR_1301">Cannot create the file [3].  A directory with this name already exists.  Cancel the installation and try installing to a different location.</String>
   <String Id="IDS_ERROR_1302">Please insert the disk: [2]</String>
   <String Id="IDS_ERROR_1303">The installer has insufficient privileges to access this directory: [2].  The installation cannot continue.  Log on as an administrator or contact your system administrator.</String>
   <String Id="IDS_ERROR_1304">Error writing to file [2].  Verify that you have access to that directory.</String>
   <String Id="IDS_ERROR_1305">Error reading from file [2].  Verify that the file exists and that you can access it.</String>
   <String Id="IDS_ERROR_1306">Another application has exclusive access to the file [2].  Please shut down all other applications, then click Retry.</String>
   <String Id="IDS_ERROR_1307">There is not enough disk space to install the file [2].  Free some disk space and click Retry, or click Cancel to exit.</String>
   <String Id="IDS_ERROR_1308">Source file not found: [2].  Verify that the file exists and that you can access it.</String>
   <String Id="IDS_ERROR_1309">Error reading from file: [3]. {{ System error [2].}}  Verify that the file exists and that you can access it.</String>
   <String Id="IDS_ERROR_1310">Error writing to file: [3]. {{ System error [2].}}  Verify that you have access to that directory.</String>
   <String Id="IDS_ERROR_1311">Source file not found{{(cabinet)}}: [2].  Verify that the file exists and that you can access it.</String>
   <String Id="IDS_ERROR_1312">Cannot create the directory [2].  A file with this name already exists.  Please rename or remove the file and click Retry, or click Cancel to exit.</String>
   <String Id="IDS_ERROR_1313">The volume [2] is currently unavailable.  Please select another.</String>
   <String Id="IDS_ERROR_1314">The specified path [2] is unavailable.</String>
   <String Id="IDS_ERROR_1315">Unable to write to the specified folder [2].</String>
   <String Id="IDS_ERROR_1316">A network error occurred while attempting to read from the file [2]</String>
   <String Id="IDS_ERROR_1317">An error occurred while attempting to create the directory [2]</String>
   <String Id="IDS_ERROR_1318">A network error occurred while attempting to create the directory [2]</String>
   <String Id="IDS_ERROR_1319">A network error occurred while attempting to open the source file cabinet [2].</String>
   <String Id="IDS_ERROR_1320">The specified path is too long [2].</String>
   <String Id="IDS_ERROR_1321">The Installer has insufficient privileges to modify the file [2].</String>
   <String Id="IDS_ERROR_1322">A portion of the path [2] exceeds the length allowed by the system.</String>
   <String Id="IDS_ERROR_1323">The path [2] contains words that are not valid in folders.</String>
   <String Id="IDS_ERROR_1324">The path [2] contains an invalid character.</String>
   <String Id="IDS_ERROR_1325">[2] is not a valid short file name.</String>
   <String Id="IDS_ERROR_1326">Error getting file security: [3] GetLastError: [2]</String>
   <String Id="IDS_ERROR_1327">Invalid Drive: [2]</String>
   <String Id="IDS_ERROR_1328">Error applying patch to file [2].  It has probably been updated by other means, and can no longer be modified by this patch.  For more information contact your patch vendor.  {{System Error: [3]}}</String>
   <String Id="IDS_ERROR_1329">A file that is required cannot be installed because the cabinet file [2] is not digitally signed. This may indicate that the cabinet file is corrupt.</String>
   <String Id="IDS_ERROR_1330">A file that is required cannot be installed because the cabinet file [2] has an invalid digital signature. This may indicate that the cabinet file is corrupt.{ Error [3] was returned by WinVerifyTrust.}</String>
   <String Id="IDS_ERROR_1331">Failed to correctly copy [2] file: CRC error.</String>
   <String Id="IDS_ERROR_1332">Failed to correctly patch [2] file: CRC error.</String>
   <String Id="IDS_ERROR_1333">Failed to correctly patch [2] file: CRC error.</String>
   <String Id="IDS_ERROR_1334">The file '[2]' cannot be installed because the file cannot be found in cabinet file '[3]'. This could indicate a network error, an error reading from the CD-ROM, or a problem with this package.</String>
   <String Id="IDS_ERROR_1335">The cabinet file '[2]' required for this installation is corrupt and cannot be used. This could indicate a network error, an error reading from the CD-ROM, or a problem with this package.</String>
   <String Id="IDS_ERROR_1336">There was an error creating a temporary file that is needed to complete this installation. Folder: [3]. System error code: [2]</String>
   <String Id="IDS_ERROR_1401">Could not create key [2]. {{ System error [3].}}  Verify that you have sufficient access to that key, or contact your support personnel.</String>
   <String Id="IDS_ERROR_1402">Could not open key: [2]. {{ System error [3].}}  Verify that you have sufficient access to that key, or contact your support personnel.</String>
   <String Id="IDS_ERROR_1403">Could not delete value [2] from key [3]. {{ System error [4].}}  Verify that you have sufficient access to that key, or contact your support personnel.</String>
   <String Id="IDS_ERROR_1404">Could not delete key [2]. {{ System error [3].}}  Verify that you have sufficient access to that key, or contact your support personnel.</String>
   <String Id="IDS_ERROR_1405">Could not read value [2] from key [3]. {{ System error [4].}}  Verify that you have sufficient access to that key, or contact your support personnel.</String>
   <String Id="IDS_ERROR_1406">Could not write value [2] to key [3]. {{ System error [4].}}  Verify that you have sufficient access to that key, or contact your support personnel.</String>
   <String Id="IDS_ERROR_1407">Could not get value names for key [2]. {{ System error [3].}}  Verify that you have sufficient access to that key, or contact your support personnel.</String>
   <String Id="IDS_ERROR_1408">Could not get sub key names for key [2]. {{ System error [3].}}  Verify that you have sufficient access to that key, or contact your support personnel.</String>
   <String Id="IDS_ERROR_1409">Could not read security information for key [2]. {{ System error [3].}}  Verify that you have sufficient access to that key, or contact your support personnel.</String>
   <String Id="IDS_ERROR_1410">Could not increase the available registry space. [2] KB of free registry space is required for the installation of this application.</String>
   <String Id="IDS_ERROR_1500">Another installation is in progress. You must complete that installation before continuing this one.</String>
   <String Id="IDS_ERROR_1501">Error accessing secured data. Please make sure the Windows Installer is configured properly and try the installation again.</String>
   <String Id="IDS_ERROR_1502">User [2] has previously initiated an installation for product [3].  That user will need to run that installation again before using that product.  Your current installation will now continue.</String>
   <String Id="IDS_ERROR_1503">User [2] has previously initiated an installation for product [3].  That user will need to run that installation again before using that product.</String>
   <String Id="IDS_ERROR_1601">Out of disk space -- Volume: '[2]'; required space: [3] KB; available space: [4] KB.  Free some disk space and retry.</String>
   <String Id="IDS_ERROR_1602">Are you sure you want to cancel?</String>
   <String Id="IDS_ERROR_1603">The file [2][3] is being held in use{ by the following process: Name: [4], ID: [5], Window Title: [6]}.  Close that application and retry.</String>
   <String Id="IDS_ERROR_1604">The product [2] is already installed, preventing the installation of this product.  The two products are incompatible.</String>
   <String Id="IDS_ERROR_1605">Out of disk space -- Volume: [2]; required space: [3] KB; available space: [4] KB.  If rollback is disabled, enough space is available. Click Abort to quit, Retry to check available disk space again, or Ignore to continue without rollback.</String>
   <String Id="IDS_ERROR_1606">Could not access network location [2].</String>
   <String Id="IDS_ERROR_1607">The following applications should be closed before continuing the installation:</String>
   <String Id="IDS_ERROR_1608">Could not find any previously installed compliant products on the machine for installing this product.</String>
   <String Id="IDS_ERROR_1609">An error occurred while applying security settings. [2] is not a valid user or group. This could be a problem with the package, or a problem connecting to a domain controller on the network. Check your network connection and click Retry, or Cancel to end the install. Unable to locate the user's SID, system error [3]</String>
   <String Id="IDS_ERROR_1651">Admin user failed to apply patch for a per-user managed or a per-machine application which is in advertise state.</String>
   <String Id="IDS_ERROR_1701">The key [2] is not valid.  Verify that you entered the correct key.</String>
   <String Id="IDS_ERROR_1702">The installer must restart your system before configuration of [2] can continue.  Click Yes to restart now or No if you plan to restart later.</String>
   <String Id="IDS_ERROR_1703">You must restart your system for the configuration changes made to [2] to take effect. Click Yes to restart now or No if you plan to restart later.</String>
   <String Id="IDS_ERROR_1704">An installation for [2] is currently suspended.  You must undo the changes made by that installation to continue.  Do you want to undo those changes?</String>
   <String Id="IDS_ERROR_1705">A previous installation for this product is in progress.  You must undo the changes made by that installation to continue.  Do you want to undo those changes?</String>
   <String Id="IDS_ERROR_1706">No valid source could be found for product [2].  The Windows Installer cannot continue.</String>
   <String Id="IDS_ERROR_1707">Installation operation completed successfully.</String>
   <String Id="IDS_ERROR_1708">Installation operation failed.</String>
   <String Id="IDS_ERROR_1709">Product: [2] -- [3]</String>
   <String Id="IDS_ERROR_1710">You may either restore your computer to its previous state or continue the installation later. Would you like to restore?</String>
   <String Id="IDS_ERROR_1711">An error occurred while writing installation information to disk.  Check to make sure enough disk space is available, and click Retry, or Cancel to end the installation.</String>
   <String Id="IDS_ERROR_1712">One or more of the files required to restore your computer to its previous state could not be found.  Restoration will not be possible.</String>
   <String Id="IDS_ERROR_1713">[2] cannot install one of its required products. Contact your technical support group.  {{System Error: [3].}}</String>
   <String Id="IDS_ERROR_1714">The older version of [2] cannot be removed.  Contact your technical support group.  {{System Error [3].}}</String>
   <String Id="IDS_ERROR_1715">Installed [2].</String>
   <String Id="IDS_ERROR_1716">Configured [2].</String>
   <String Id="IDS_ERROR_1717">Removed [2].</String>
   <String Id="IDS_ERROR_1718">File [2] was rejected by digital signature policy.</String>
   <String Id="IDS_ERROR_1719">Windows Installer service could not be accessed. Contact your support personnel to verify that it is properly registered and enabled.</String>
   <String Id="IDS_ERROR_1720">There is a problem with this Windows Installer package. A script required for this install to complete could not be run. Contact your support personnel or package vendor. Custom action [2] script error [3], [4]: [5] Line [6], Column [7], [8]</String>
   <String Id="IDS_ERROR_1721">There is a problem with this Windows Installer package. A program required for this install to complete could not be run. Contact your support personnel or package vendor. Action: [2], location: [3], command: [4]</String>
   <String Id="IDS_ERROR_1722">There is a problem with this Windows Installer package. A program run as part of the setup did not finish as expected. Contact your support personnel or package vendor. Action [2], location: [3], command: [4]</String>
   <String Id="IDS_ERROR_1723">There is a problem with this Windows Installer package. A DLL required for this install to complete could not be run. Contact your support personnel or package vendor. Action [2], entry: [3], library: [4]</String>
   <String Id="IDS_ERROR_1724">Removal completed successfully.</String>
   <String Id="IDS_ERROR_1725">Removal failed.</String>
   <String Id="IDS_ERROR_1726">Advertisement completed successfully.</String>
   <String Id="IDS_ERROR_1727">Advertisement failed.</String>
   <String Id="IDS_ERROR_1728">Configuration completed successfully.</String>
   <String Id="IDS_ERROR_1729">Configuration failed.</String>
   <String Id="IDS_ERROR_1730">You must be an Administrator to remove this application. To remove this application, you can log on as an administrator, or contact your technical support group for assistance.</String>
   <String Id="IDS_ERROR_1731">The source installation package for the product [2] is out of sync with the client package. Try the installation again using a valid copy of the installation package '[3]'.</String>
   <String Id="IDS_ERROR_1732">In order to complete the installation of [2], you must restart the computer. Other users are currently logged on to this computer, and restarting may cause them to lose their work. Do you want to restart now?</String>
   <String Id="IDS_ERROR_1801">The path [2] is not valid.  Please specify a valid path.</String>
   <String Id="IDS_ERROR_1802">Out of memory. Shut down other applications before retrying.</String>
   <String Id="IDS_ERROR_1803">There is no disk in drive [2]. Please insert one and click Retry, or click Cancel to go back to the previously selected volume.</String>
   <String Id="IDS_ERROR_1804">There is no disk in drive [2]. Please insert one and click Retry, or click Cancel to return to the browse dialog and select a different volume.</String>
   <String Id="IDS_ERROR_1805">The folder [2] does not exist.  Please enter a path to an existing folder.</String>
   <String Id="IDS_ERROR_1806">You have insufficient privileges to read this folder.</String>
   <String Id="IDS_ERROR_1807">A valid destination folder for the installation could not be determined.</String>
   <String Id="IDS_ERROR_1901">Error attempting to read from the source installation database: [2].</String>
   <String Id="IDS_ERROR_1902">Scheduling reboot operation: Renaming file [2] to [3]. Must reboot to complete operation.</String>
   <String Id="IDS_ERROR_1903">Scheduling reboot operation: Deleting file [2]. Must reboot to complete operation.</String>
   <String Id="IDS_ERROR_1904">Module [2] failed to register.  HRESULT [3].  Contact your support personnel.</String>
   <String Id="IDS_ERROR_1905">Module [2] failed to unregister.  HRESULT [3].  Contact your support personnel.</String>
   <String Id="IDS_ERROR_1906">Failed to cache package [2]. Error: [3]. Contact your support personnel.</String>
   <String Id="IDS_ERROR_1907">Could not register font [2].  Verify that you have sufficient permissions to install fonts, and that the system supports this font.</String>
   <String Id="IDS_ERROR_1908">Could not unregister font [2]. Verify that you have sufficient permissions to remove fonts.</String>
   <String Id="IDS_ERROR_1909">Could not create shortcut [2]. Verify that the destination folder exists and that you can access it.</String>
   <String Id="IDS_ERROR_1910">Could not remove shortcut [2]. Verify that the shortcut file exists and that you can access it.</String>
   <String Id="IDS_ERROR_1911">Could not register type library for file [2].  Contact your support personnel.</String>
   <String Id="IDS_ERROR_1912">Could not unregister type library for file [2].  Contact your support personnel.</String>
   <String Id="IDS_ERROR_1913">Could not update the INI file [2][3].  Verify that the file exists and that you can access it.</String>
   <String Id="IDS_ERROR_1914">Could not schedule file [2] to replace file [3] on reboot.  Verify that you have write permissions to file [3].</String>
   <String Id="IDS_ERROR_1915">Error removing ODBC driver manager, ODBC error [2]: [3]. Contact your support personnel.</String>
   <String Id="IDS_ERROR_1916">Error installing ODBC driver manager, ODBC error [2]: [3]. Contact your support personnel.</String>
   <String Id="IDS_ERROR_1917">Error removing ODBC driver [4], ODBC error [2]: [3]. Verify that you have sufficient privileges to remove ODBC drivers.</String>
   <String Id="IDS_ERROR_1918">Error installing ODBC driver [4], ODBC error [2]: [3]. Verify that the file [4] exists and that you can access it.</String>
   <String Id="IDS_ERROR_1919">Error configuring ODBC data source [4], ODBC error [2]: [3]. Verify that the file [4] exists and that you can access it.</String>
   <String Id="IDS_ERROR_1920">Service [2] ([3]) failed to start.  Verify that you have sufficient privileges to start system services.</String>
   <String Id="IDS_ERROR_1921">Service [2] ([3]) could not be stopped.  Verify that you have sufficient privileges to stop system services.</String>
   <String Id="IDS_ERROR_1922">Service [2] ([3]) could not be deleted.  Verify that you have sufficient privileges to remove system services.</String>
   <String Id="IDS_ERROR_1923">Service [2] ([3]) could not be installed.  Verify that you have sufficient privileges to install system services.</String>
   <String Id="IDS_ERROR_1924">Could not update environment variable [2].  Verify that you have sufficient privileges to modify environment variables.</String>
   <String Id="IDS_ERROR_1925">You do not have sufficient privileges to complete this installation for all users of the machine.  Log on as an administrator and then retry this installation.</String>
   <String Id="IDS_ERROR_1926">Could not set file security for file [3]. Error: [2].  Verify that you have sufficient privileges to modify the security permissions for this file.</String>
   <String Id="IDS_ERROR_1927">Component Services (COM+ 1.0) are not installed on this computer.  This installation requires Component Services in order to complete successfully.  Component Services are available on Windows 2000.</String>
   <String Id="IDS_ERROR_1928">Error registering COM+ application.  Contact your support personnel for more information.</String>
   <String Id="IDS_ERROR_1929">Error unregistering COM+ application.  Contact your support personnel for more information.</String>
   <String Id="IDS_ERROR_1930">The description for service '[2]' ([3]) could not be changed.</String>
   <String Id="IDS_ERROR_1931">The Windows Installer service cannot update the system file [2] because the file is protected by Windows.  You may need to update your operating system for this program to work correctly. {{Package version: [3], OS Protected version: [4]}}</String>
   <String Id="IDS_ERROR_1932">The Windows Installer service cannot update the protected Windows file [2]. {{Package version: [3], OS Protected version: [4], SFP Error: [5]}}</String>
   <String Id="IDS_ERROR_1933">The Windows Installer service cannot update one or more protected Windows files. SFP Error: [2]. List of protected files: [3]</String>
   <String Id="IDS_ERROR_1934">User installations are disabled via policy on the machine.</String>
   <String Id="IDS_ERROR_1935">An error occurred during the installation of assembly component [2]. HRESULT: [3]. {{assembly interface: [4], function: [5], assembly name: [6]}}</String>
   <String Id="IDS_ERROR_1936">An error occurred during the installation of assembly '[6]'. The assembly is not strongly named or is not signed with the minimal key length. HRESULT: [3]. {{assembly interface: [4], function: [5], component: [2]}}</String>
   <String Id="IDS_ERROR_1937">An error occurred during the installation of assembly '[6]'. The signature or catalog could not be verified or is not valid. HRESULT: [3]. {{assembly interface: [4], function: [5], component: [2]}}</String>
   <String Id="IDS_ERROR_1938">An error occurred during the installation of assembly '[6]'. One or more modules of the assembly could not be found. HRESULT: [3]. {{assembly interface: [4], function: [5], component: [2]}}</String>
   <String Id="IDS_ERROR_2101">Shortcuts not supported by the operating system.</String>
   <String Id="IDS_ERROR_2102">Invalid .ini action: [2]</String>
   <String Id="IDS_ERROR_2103">Could not resolve path for shell folder [2].</String>
   <String Id="IDS_ERROR_2104">Writing .ini file: [3]: System error: [2].</String>
   <String Id="IDS_ERROR_2105">Shortcut Creation [3] Failed. System error: [2].</String>
   <String Id="IDS_ERROR_2106">Shortcut Deletion [3] Failed. System error: [2].</String>
   <String Id="IDS_ERROR_2107">Error [3] registering type library [2].</String>
   <String Id="IDS_ERROR_2108">Error [3] unregistering type library [2].</String>
   <String Id="IDS_ERROR_2109">Section missing for .ini action.</String>
   <String Id="IDS_ERROR_2110">Key missing for .ini action.</String>
   <String Id="IDS_ERROR_2111">Detection of running applications failed, could not get performance data. Registered operation returned : [2].</String>
   <String Id="IDS_ERROR_2112">Detection of running applications failed, could not get performance index. Registered operation returned : [2].</String>
   <String Id="IDS_ERROR_2113">Detection of running applications failed.</String>
   <String Id="IDS_ERROR_2200">Database: [2]. Database object creation failed, mode = [3].</String>
   <String Id="IDS_ERROR_2201">Database: [2]. Initialization failed, out of memory.</String>
   <String Id="IDS_ERROR_2202">Database: [2]. Data access failed, out of memory.</String>
   <String Id="IDS_ERROR_2203">Database: [2]. Cannot open database file. System error [3].</String>
   <String Id="IDS_ERROR_2204">Database: [2]. Table already exists: [3].</String>
   <String Id="IDS_ERROR_2205">Database: [2]. Table does not exist: [3].</String>
   <String Id="IDS_ERROR_2206">Database: [2]. Table could not be dropped: [3].</String>
   <String Id="IDS_ERROR_2207">Database: [2]. Intent violation.</String>
   <String Id="IDS_ERROR_2208">Database: [2]. Insufficient parameters for Execute.</String>
   <String Id="IDS_ERROR_2209">Database: [2]. Cursor in invalid state.</String>
   <String Id="IDS_ERROR_2210">Database: [2]. Invalid update data type in column [3].</String>
   <String Id="IDS_ERROR_2211">Database: [2]. Could not create database table [3].</String>
   <String Id="IDS_ERROR_2212">Database: [2]. Database not in writable state.</String>
   <String Id="IDS_ERROR_2213">Database: [2]. Error saving database tables.</String>
   <String Id="IDS_ERROR_2214">Database: [2]. Error writing export file: [3].</String>
   <String Id="IDS_ERROR_2215">Database: [2]. Cannot open import file: [3].</String>
   <String Id="IDS_ERROR_2216">Database: [2]. Import file format error: [3], Line [4].</String>
   <String Id="IDS_ERROR_2217">Database: [2]. Wrong state to CreateOutputDatabase [3].</String>
   <String Id="IDS_ERROR_2218">Database: [2]. Table name not supplied.</String>
   <String Id="IDS_ERROR_2219">Database: [2]. Invalid Installer database format.</String>
   <String Id="IDS_ERROR_2220">Database: [2]. Invalid row/field data.</String>
   <String Id="IDS_ERROR_2221">Database: [2]. Code page conflict in import file: [3].</String>
   <String Id="IDS_ERROR_2222">Database: [2]. Transform or merge code page [3] differs from database code page [4].</String>
   <String Id="IDS_ERROR_2223">Database: [2]. Databases are the same. No transform generated.</String>
   <String Id="IDS_ERROR_2224">Database: [2]. GenerateTransform: Database corrupt. Table: [3].</String>
   <String Id="IDS_ERROR_2225">Database: [2]. Transform: Cannot transform a temporary table. Table: [3].</String>
   <String Id="IDS_ERROR_2226">Database: [2]. Transform failed.</String>
   <String Id="IDS_ERROR_2227">Database: [2]. Invalid identifier '[3]' in SQL query: [4].</String>
   <String Id="IDS_ERROR_2228">Database: [2]. Unknown table '[3]' in SQL query: [4].</String>
   <String Id="IDS_ERROR_2229">Database: [2]. Could not load table '[3]' in SQL query: [4].</String>
   <String Id="IDS_ERROR_2230">Database: [2]. Repeated table '[3]' in SQL query: [4].</String>
   <String Id="IDS_ERROR_2231">Database: [2]. Missing ')' in SQL query: [3].</String>
   <String Id="IDS_ERROR_2232">Database: [2]. Unexpected token '[3]' in SQL query: [4].</String>
   <String Id="IDS_ERROR_2233">Database: [2]. No columns in SELECT clause in SQL query: [3].</String>
   <String Id="IDS_ERROR_2234">Database: [2]. No columns in ORDER BY clause in SQL query: [3].</String>
   <String Id="IDS_ERROR_2235">Database: [2]. Column '[3]' not present or ambiguous in SQL query: [4].</String>
   <String Id="IDS_ERROR_2236">Database: [2]. Invalid operator '[3]' in SQL query: [4].</String>
   <String Id="IDS_ERROR_2237">Database: [2]. Invalid or missing query string: [3].</String>
   <String Id="IDS_ERROR_2238">Database: [2]. Missing FROM clause in SQL query: [3].</String>
   <String Id="IDS_ERROR_2239">Database: [2]. Insufficient values in INSERT SQL statement.</String>
   <String Id="IDS_ERROR_2240">Database: [2]. Missing update columns in UPDATE SQL statement.</String>
   <String Id="IDS_ERROR_2241">Database: [2]. Missing insert columns in INSERT SQL statement.</String>
   <String Id="IDS_ERROR_2242">Database: [2]. Column '[3]' repeated.</String>
   <String Id="IDS_ERROR_2243">Database: [2]. No primary columns defined for table creation.</String>
   <String Id="IDS_ERROR_2244">Database: [2]. Invalid type specifier '[3]' in SQL query [4].</String>
   <String Id="IDS_ERROR_2245">IStorage::Stat failed with error [3].</String>
   <String Id="IDS_ERROR_2246">Database: [2]. Invalid Installer transform format.</String>
   <String Id="IDS_ERROR_2247">Database: [2] Transform stream read/write failure.</String>
   <String Id="IDS_ERROR_2248">Database: [2] GenerateTransform/Merge: Column type in base table does not match reference table. Table: [3] Col #: [4].</String>
   <String Id="IDS_ERROR_2249">Database: [2] GenerateTransform: More columns in base table than in reference table. Table: [3].</String>
   <String Id="IDS_ERROR_2250">Database: [2] Transform: Cannot add existing row. Table: [3].</String>
   <String Id="IDS_ERROR_2251">Database: [2] Transform: Cannot delete row that does not exist. Table: [3].</String>
   <String Id="IDS_ERROR_2252">Database: [2] Transform: Cannot add existing table. Table: [3].</String>
   <String Id="IDS_ERROR_2253">Database: [2] Transform: Cannot delete table that does not exist. Table: [3].</String>
   <String Id="IDS_ERROR_2254">Database: [2] Transform: Cannot update row that does not exist. Table: [3].</String>
   <String Id="IDS_ERROR_2255">Database: [2] Transform: Column with this name already exists. Table: [3] Col: [4].</String>
   <String Id="IDS_ERROR_2256">Database: [2] GenerateTransform/Merge: Number of primary keys in base table does not match reference table. Table: [3].</String>
   <String Id="IDS_ERROR_2257">Database: [2]. Intent to modify read only table: [3].</String>
   <String Id="IDS_ERROR_2258">Database: [2]. Type mismatch in parameter: [3].</String>
   <String Id="IDS_ERROR_2259">Database: [2] Table(s) Update failed</String>
   <String Id="IDS_ERROR_2260">Storage CopyTo failed. System error: [3].</String>
   <String Id="IDS_ERROR_2261">Could not remove stream [2]. System error: [3].</String>
   <String Id="IDS_ERROR_2262">Stream does not exist: [2]. System error: [3].</String>
   <String Id="IDS_ERROR_2263">Could not open stream [2]. System error: [3].</String>
   <String Id="IDS_ERROR_2264">Could not remove stream [2]. System error: [3].</String>
   <String Id="IDS_ERROR_2265">Could not commit storage. System error: [3].</String>
   <String Id="IDS_ERROR_2266">Could not rollback storage. System error: [3].</String>
   <String Id="IDS_ERROR_2267">Could not delete storage [2]. System error: [3].</String>
   <String Id="IDS_ERROR_2268">Database: [2]. Merge: There were merge conflicts reported in [3] tables.</String>
   <String Id="IDS_ERROR_2269">Database: [2]. Merge: The column count differed in the '[3]' table of the two databases.</String>
   <String Id="IDS_ERROR_2270">Database: [2]. GenerateTransform/Merge: Column name in base table does not match reference table. Table: [3] Col #: [4].</String>
   <String Id="IDS_ERROR_2271">SummaryInformation write for transform failed.</String>
   <String Id="IDS_ERROR_2272">Database: [2]. MergeDatabase will not write any changes because the database is open read-only.</String>
   <String Id="IDS_ERROR_2273">Database: [2]. MergeDatabase: A reference to the base database was passed as the reference database.</String>
   <String Id="IDS_ERROR_2274">Database: [2]. MergeDatabase: Unable to write errors to Error table. Could be due to a non-nullable column in a predefined Error table.</String>
   <String Id="IDS_ERROR_2275">Database: [2]. Specified Modify [3] operation invalid for table joins.</String>
   <String Id="IDS_ERROR_2276">Database: [2]. Code page [3] not supported by the system.</String>
   <String Id="IDS_ERROR_2277">Database: [2]. Failed to save table [3].</String>
   <String Id="IDS_ERROR_2278">Database: [2]. Exceeded number of expressions limit of 32 in WHERE clause of SQL query: [3].</String>
   <String Id="IDS_ERROR_2279">Database: [2] Transform: Too many columns in base table [3].</String>
   <String Id="IDS_ERROR_2280">Database: [2]. Could not create column [3] for table [4].</String>
   <String Id="IDS_ERROR_2281">Could not rename stream [2]. System error: [3].</String>
   <String Id="IDS_ERROR_2282">Stream name invalid [2].</String>
   <String Id="IDS_ERROR_2302">Patch notify: [2] bytes patched to far.</String>
   <String Id="IDS_ERROR_2303">Error getting volume info. GetLastError: [2].</String>
   <String Id="IDS_ERROR_2304">Error getting disk free space. GetLastError: [2]. Volume: [3].</String>
   <String Id="IDS_ERROR_2305">Error waiting for patch thread. GetLastError: [2].</String>
   <String Id="IDS_ERROR_2306">Could not create thread for patch application. GetLastError: [2].</String>
   <String Id="IDS_ERROR_2307">Source file key name is null.</String>
   <String Id="IDS_ERROR_2308">Destination file name is null.</String>
   <String Id="IDS_ERROR_2309">Attempting to patch file [2] when patch already in progress.</String>
   <String Id="IDS_ERROR_2310">Attempting to continue patch when no patch is in progress.</String>
   <String Id="IDS_ERROR_2315">Missing path separator: [2].</String>
   <String Id="IDS_ERROR_2318">File does not exist: [2].</String>
   <String Id="IDS_ERROR_2319">Error setting file attribute: [3] GetLastError: [2].</String>
   <String Id="IDS_ERROR_2320">File not writable: [2].</String>
   <String Id="IDS_ERROR_2321">Error creating file: [2].</String>
   <String Id="IDS_ERROR_2322">User canceled.</String>
   <String Id="IDS_ERROR_2323">Invalid file attribute.</String>
   <String Id="IDS_ERROR_2324">Could not open file: [3] GetLastError: [2].</String>
   <String Id="IDS_ERROR_2325">Could not get file time for file: [3] GetLastError: [2].</String>
   <String Id="IDS_ERROR_2326">Error in FileToDosDateTime.</String>
   <String Id="IDS_ERROR_2327">Could not remove directory: [3] GetLastError: [2].</String>
   <String Id="IDS_ERROR_2328">Error getting file version info for file: [2].</String>
   <String Id="IDS_ERROR_2329">Error deleting file: [3]. GetLastError: [2].</String>
   <String Id="IDS_ERROR_2330">Error getting file attributes: [3]. GetLastError: [2].</String>
   <String Id="IDS_ERROR_2331">Error loading library [2] or finding entry point [3].</String>
   <String Id="IDS_ERROR_2332">Error getting file attributes. GetLastError: [2].</String>
   <String Id="IDS_ERROR_2333">Error setting file attributes. GetLastError: [2].</String>
   <String Id="IDS_ERROR_2334">Error converting file time to local time for file: [3]. GetLastError: [2].</String>
   <String Id="IDS_ERROR_2335">Path: [2] is not a parent of [3].</String>
   <String Id="IDS_ERROR_2336">Error creating temp file on path: [3]. GetLastError: [2].</String>
   <String Id="IDS_ERROR_2337">Could not close file: [3] GetLastError: [2].</String>
   <String Id="IDS_ERROR_2338">Could not update resource for file: [3] GetLastError: [2].</String>
   <String Id="IDS_ERROR_2339">Could not set file time for file: [3] GetLastError: [2].</String>
   <String Id="IDS_ERROR_2340">Could not update resource for file: [3], Missing resource.</String>
   <String Id="IDS_ERROR_2341">Could not update resource for file: [3], Resource too large.</String>
   <String Id="IDS_ERROR_2342">Could not update resource for file: [3] GetLastError: [2].</String>
   <String Id="IDS_ERROR_2343">Specified path is empty.</String>
   <String Id="IDS_ERROR_2344">Could not find required file IMAGEHLP.DLL to validate file:[2].</String>
   <String Id="IDS_ERROR_2345">[2]: File does not contain a valid checksum value.</String>
   <String Id="IDS_ERROR_2347">User ignore.</String>
   <String Id="IDS_ERROR_2348">Error attempting to read from cabinet stream.</String>
   <String Id="IDS_ERROR_2349">Copy resumed with different info.</String>
   <String Id="IDS_ERROR_2350">FDI server error</String>
   <String Id="IDS_ERROR_2351">File key '[2]' not found in cabinet '[3]'. The installation cannot continue.</String>
   <String Id="IDS_ERROR_2352">Could not initialize cabinet file server. The required file 'CABINET.DLL' may be missing.</String>
   <String Id="IDS_ERROR_2353">Not a cabinet.</String>
   <String Id="IDS_ERROR_2354">Cannot handle cabinet.</String>
   <String Id="IDS_ERROR_2355">Corrupt cabinet.</String>
   <String Id="IDS_ERROR_2356">Could not locate cabinet in stream: [2].</String>
   <String Id="IDS_ERROR_2357">Cannot set attributes.</String>
   <String Id="IDS_ERROR_2358">Error determining whether file is in-use: [3]. GetLastError: [2].</String>
   <String Id="IDS_ERROR_2359">Unable to create the target file - file may be in use.</String>
   <String Id="IDS_ERROR_2360">Progress tick.</String>
   <String Id="IDS_ERROR_2361">Need next cabinet.</String>
   <String Id="IDS_ERROR_2362">Folder not found: [2].</String>
   <String Id="IDS_ERROR_2363">Could not enumerate subfolders for folder: [2].</String>
   <String Id="IDS_ERROR_2364">Bad enumeration constant in CreateCopier call.</String>
   <String Id="IDS_ERROR_2365">Could not BindImage exe file [2].</String>
   <String Id="IDS_ERROR_2366">User failure.</String>
   <String Id="IDS_ERROR_2367">User abort.</String>
   <String Id="IDS_ERROR_2368">Failed to get network resource information. Error [2], network path [3]. Extended error: network provider [5], error code [4], error description [6].</String>
   <String Id="IDS_ERROR_2370">Invalid CRC checksum value for [2] file.{ Its header says [3] for checksum, its computed value is [4].}</String>
   <String Id="IDS_ERROR_2371">Could not apply patch to file [2]. GetLastError: [3].</String>
   <String Id="IDS_ERROR_2372">Patch file [2] is corrupt or of an invalid format. Attempting to patch file [3]. GetLastError: [4].</String>
   <String Id="IDS_ERROR_2373">File [2] is not a valid patch file.</String>
   <String Id="IDS_ERROR_2374">File [2] is not a valid destination file for patch file [3].</String>
   <String Id="IDS_ERROR_2375">Unknown patching error: [2].</String>
   <String Id="IDS_ERROR_2376">Cabinet not found.</String>
   <String Id="IDS_ERROR_2379">Error opening file for read: [3] GetLastError: [2].</String>
   <String Id="IDS_ERROR_2380">Error opening file for write: [3]. GetLastError: [2].</String>
   <String Id="IDS_ERROR_2381">Directory does not exist: [2].</String>
   <String Id="IDS_ERROR_2382">Drive not ready: [2].</String>
   <String Id="IDS_ERROR_2401">64-bit registry operation attempted on 32-bit operating system for key [2].</String>
   <String Id="IDS_ERROR_2402">Out of memory.</String>
   <String Id="IDS_ERROR_2501">Could not create rollback script enumerator.</String>
   <String Id="IDS_ERROR_2502">Called InstallFinalize when no install in progress.</String>
   <String Id="IDS_ERROR_2503">Called RunScript when not marked in progress.</String>
   <String Id="IDS_ERROR_2601">Invalid value for property [2]: '[3]'</String>
   <String Id="IDS_ERROR_2602">The [2] table entry '[3]' has no associated entry in the Media table.</String>
   <String Id="IDS_ERROR_2603">Duplicate table name [2].</String>
   <String Id="IDS_ERROR_2604">[2] Property undefined.</String>
   <String Id="IDS_ERROR_2605">Could not find server [2] in [3] or [4].</String>
   <String Id="IDS_ERROR_2606">Value of property [2] is not a valid full path: '[3]'.</String>
   <String Id="IDS_ERROR_2607">Media table not found or empty (required for installation of files).</String>
   <String Id="IDS_ERROR_2608">Could not create security descriptor for object. Error: '[2]'.</String>
   <String Id="IDS_ERROR_2609">Attempt to migrate product settings before initialization.</String>
   <String Id="IDS_ERROR_2611">The file [2] is marked as compressed, but the associated media entry does not specify a cabinet.</String>
   <String Id="IDS_ERROR_2612">Stream not found in '[2]' column. Primary key: '[3]'.</String>
   <String Id="IDS_ERROR_2613">RemoveExistingProducts action sequenced incorrectly.</String>
   <String Id="IDS_ERROR_2614">Could not access IStorage object from installation package.</String>
   <String Id="IDS_ERROR_2615">Skipped unregistration of Module [2] due to source resolution failure.</String>
   <String Id="IDS_ERROR_2616">Companion file [2] parent missing.</String>
   <String Id="IDS_ERROR_2617">Shared component [2] not found in Component table.</String>
   <String Id="IDS_ERROR_2618">Isolated application component [2] not found in Component table.</String>
   <String Id="IDS_ERROR_2619">Isolated components [2], [3] not part of same feature.</String>
   <String Id="IDS_ERROR_2620">Key file of isolated application component [2] not in File table.</String>
   <String Id="IDS_ERROR_2621">Resource DLL or Resource ID information for shortcut [2] set incorrectly.</String>
   <String Id="IDS_ERROR_2701">The depth of a feature exceeds the acceptable tree depth of [2] levels.</String>
   <String Id="IDS_ERROR_2702">A Feature table record ([2]) references a non-existent parent in the Attributes field.</String>
   <String Id="IDS_ERROR_2703">Property name for root source path not defined: [2]</String>
   <String Id="IDS_ERROR_2704">Root directory property undefined: [2]</String>
   <String Id="IDS_ERROR_2705">Invalid table: [2]; Could not be linked as tree.</String>
   <String Id="IDS_ERROR_2706">Source paths not created. No path exists for entry [2] in Directory table.</String>
   <String Id="IDS_ERROR_2707">Target paths not created. No path exists for entry [2] in Directory table.</String>
   <String Id="IDS_ERROR_2708">No entries found in the file table.</String>
   <String Id="IDS_ERROR_2709">The specified Component name ('[2]') not found in Component table.</String>
   <String Id="IDS_ERROR_2710">The requested 'Select' state is illegal for this Component.</String>
   <String Id="IDS_ERROR_2711">The specified Feature name ('[2]') not found in Feature table.</String>
   <String Id="IDS_ERROR_2712">Invalid return from modeless dialog: [3], in action [2].</String>
   <String Id="IDS_ERROR_2713">Null value in a non-nullable column ('[2]' in '[3]' column of the '[4]' table.</String>
   <String Id="IDS_ERROR_2714">Invalid value for default folder name: [2].</String>
   <String Id="IDS_ERROR_2715">The specified File key ('[2]') not found in the File table.</String>
   <String Id="IDS_ERROR_2716">Could not create a random subcomponent name for component '[2]'.</String>
   <String Id="IDS_ERROR_2717">Bad action condition or error calling custom action '[2]'.</String>
   <String Id="IDS_ERROR_2718">Missing package name for product code '[2]'.</String>
   <String Id="IDS_ERROR_2719">Neither UNC nor drive letter path found in source '[2]'.</String>
   <String Id="IDS_ERROR_2720">Error opening source list key. Error: '[2]'</String>
   <String Id="IDS_ERROR_2721">Custom action [2] not found in Binary table stream.</String>
   <String Id="IDS_ERROR_2722">Custom action [2] not found in File table.</String>
   <String Id="IDS_ERROR_2723">Custom action [2] specifies unsupported type.</String>
   <String Id="IDS_ERROR_2724">The volume label '[2]' on the media you're running from does not match the label '[3]' given in the Media table. This is allowed only if you have only 1 entry in your Media table.</String>
   <String Id="IDS_ERROR_2725">Invalid database tables</String>
   <String Id="IDS_ERROR_2726">Action not found: [2].</String>
   <String Id="IDS_ERROR_2727">The directory entry '[2]' does not exist in the Directory table.</String>
   <String Id="IDS_ERROR_2728">Table definition error: [2]</String>
   <String Id="IDS_ERROR_2729">Install engine not initialized.</String>
   <String Id="IDS_ERROR_2730">Bad value in database. Table: '[2]'; Primary key: '[3]'; Column: '[4]'</String>
   <String Id="IDS_ERROR_2731">Selection Manager not initialized.</String>
   <String Id="IDS_ERROR_2732">Directory Manager not initialized.</String>
   <String Id="IDS_ERROR_2733">Bad foreign key ('[2]') in '[3]' column of the '[4]' table.</String>
   <String Id="IDS_ERROR_2734">Invalid reinstall mode character.</String>
   <String Id="IDS_ERROR_2735">Custom action '[2]' has caused an unhandled exception and has been stopped. This may be the result of an internal error in the custom action, such as an access violation.</String>
   <String Id="IDS_ERROR_2736">Generation of custom action temp file failed: [2].</String>
   <String Id="IDS_ERROR_2737">Could not access custom action [2], entry [3], library [4]</String>
   <String Id="IDS_ERROR_2738">Could not access VBScript run time for custom action [2].</String>
   <String Id="IDS_ERROR_2739">Could not access JavaScript run time for custom action [2].</String>
   <String Id="IDS_ERROR_2740">Custom action [2] script error [3], [4]: [5] Line [6], Column [7], [8].</String>
   <String Id="IDS_ERROR_2741">Configuration information for product [2] is corrupt. Invalid info: [2].</String>
   <String Id="IDS_ERROR_2742">Marshaling to Server failed: [2].</String>
   <String Id="IDS_ERROR_2743">Could not execute custom action [2], location: [3], command: [4].</String>
   <String Id="IDS_ERROR_2744">EXE failed called by custom action [2], location: [3], command: [4].</String>
   <String Id="IDS_ERROR_2745">Transform [2] invalid for package [3]. Expected language [4], found language [5].</String>
   <String Id="IDS_ERROR_2746">Transform [2] invalid for package [3]. Expected product [4], found product [5].</String>
   <String Id="IDS_ERROR_2747">Transform [2] invalid for package [3]. Expected product version &lt; [4], found product version [5].</String>
   <String Id="IDS_ERROR_2748">Transform [2] invalid for package [3]. Expected product version &lt;= [4], found product version [5].</String>
   <String Id="IDS_ERROR_2749">Transform [2] invalid for package [3]. Expected product version == [4], found product version [5].</String>
   <String Id="IDS_ERROR_2750">Transform [2] invalid for package [3]. Expected product version &gt;= [4], found product version [5].</String>
   <String Id="IDS_ERROR_2751">Transform [2] invalid for package [3]. Expected product version &gt; [4], found product version [5].</String>
   <String Id="IDS_ERROR_2752">Could not open transform [2] stored as child storage of package [4].</String>
   <String Id="IDS_ERROR_2753">The File '[2]' is not marked for installation.</String>
   <String Id="IDS_ERROR_2754">The File '[2]' is not a valid patch file.</String>
   <String Id="IDS_ERROR_2755">Server returned unexpected error [2] attempting to install package [3].</String>
   <String Id="IDS_ERROR_2756">The property '[2]' was used as a directory property in one or more tables, but no value was ever assigned.</String>
   <String Id="IDS_ERROR_2757">Could not create summary info for transform [2].</String>
   <String Id="IDS_ERROR_2758">Transform [2] does not contain an MSI version.</String>
   <String Id="IDS_ERROR_2759">Transform [2] version [3] incompatible with engine; Min: [4], Max: [5].</String>
   <String Id="IDS_ERROR_2760">Transform [2] invalid for package [3]. Expected upgrade code [4], found [5].</String>
   <String Id="IDS_ERROR_2761">Cannot begin transaction. Global mutex not properly initialized.</String>
   <String Id="IDS_ERROR_2762">Cannot write script record. Transaction not started.</String>
   <String Id="IDS_ERROR_2763">Cannot run script. Transaction not started.</String>
   <String Id="IDS_ERROR_2765">Assembly name missing from AssemblyName table : Component: [4].</String>
   <String Id="IDS_ERROR_2766">The file [2] is an invalid MSI storage file.</String>
   <String Id="IDS_ERROR_2767">No more data{ while enumerating [2]}.</String>
   <String Id="IDS_ERROR_2768">Transform in patch package is invalid.</String>
   <String Id="IDS_ERROR_2769">Custom Action [2] did not close [3] MSIHANDLEs.</String>
   <String Id="IDS_ERROR_2770">Cached folder [2] not defined in internal cache folder table.</String>
   <String Id="IDS_ERROR_2771">Upgrade of feature [2] has a missing component.</String>
   <String Id="IDS_ERROR_2772">New upgrade feature [2] must be a leaf feature.</String>
   <String Id="IDS_ERROR_2801">Unknown Message -- Type [2]. No action is taken.</String>
   <String Id="IDS_ERROR_2802">No publisher is found for the event [2].</String>
   <String Id="IDS_ERROR_2803">Dialog View did not find a record for the dialog [2].</String>
   <String Id="IDS_ERROR_2804">On activation of the control [3] on dialog [2] CMsiDialog failed to evaluate the condition [3].</String>
   <String Id="IDS_ERROR_2806">The dialog [2] failed to evaluate the condition [3].</String>
   <String Id="IDS_ERROR_2807">The action [2] is not recognized.</String>
   <String Id="IDS_ERROR_2808">Default button is ill-defined on dialog [2].</String>
   <String Id="IDS_ERROR_2809">On the dialog [2] the next control pointers do not form a cycle. There is a pointer from [3] to [4], but there is no further pointer.</String>
   <String Id="IDS_ERROR_2810">On the dialog [2] the next control pointers do not form a cycle. There is a pointer from both [3] and [5] to [4].</String>
   <String Id="IDS_ERROR_2811">On dialog [2] control [3] has to take focus, but it is unable to do so.</String>
   <String Id="IDS_ERROR_2812">The event [2] is not recognized.</String>
   <String Id="IDS_ERROR_2813">The EndDialog event was called with the argument [2], but the dialog has a parent.</String>
   <String Id="IDS_ERROR_2814">On the dialog [2] the control [3] names a nonexistent control [4] as the next control.</String>
   <String Id="IDS_ERROR_2815">ControlCondition table has a row without condition for the dialog [2].</String>
   <String Id="IDS_ERROR_2816">The EventMapping table refers to an invalid control [4] on dialog [2] for the event [3].</String>
   <String Id="IDS_ERROR_2817">The event [2] failed to set the attribute for the control [4] on dialog [3].</String>
   <String Id="IDS_ERROR_2818">In the ControlEvent table EndDialog has an unrecognized argument [2].</String>
   <String Id="IDS_ERROR_2819">Control [3] on dialog [2] needs a property linked to it.</String>
   <String Id="IDS_ERROR_2820">Attempted to initialize an already initialized handler.</String>
   <String Id="IDS_ERROR_2821">Attempted to initialize an already initialized dialog: [2].</String>
   <String Id="IDS_ERROR_2822">No other method can be called on dialog [2] until all the controls are added.</String>
   <String Id="IDS_ERROR_2823">Attempted to initialize an already initialized control: [3] on dialog [2].</String>
   <String Id="IDS_ERROR_2824">The dialog attribute [3] needs a record of at least [2] field(s).</String>
   <String Id="IDS_ERROR_2825">The control attribute [3] needs a record of at least [2] field(s).</String>
   <String Id="IDS_ERROR_2826">Control [3] on dialog [2] extends beyond the boundaries of the dialog [4] by [5] pixels.</String>
   <String Id="IDS_ERROR_2827">The button [4] on the radio button group [3] on dialog [2] extends beyond the boundaries of the group [5] by [6] pixels.</String>
   <String Id="IDS_ERROR_2828">Tried to remove control [3] from dialog [2], but the control is not part of the dialog.</String>
   <String Id="IDS_ERROR_2829">Attempt to use an uninitialized dialog.</String>
   <String Id="IDS_ERROR_2830">Attempt to use an uninitialized control on dialog [2].</String>
   <String Id="IDS_ERROR_2831">The control [3] on dialog [2] does not support [5] the attribute [4].</String>
   <String Id="IDS_ERROR_2832">The dialog [2] does not support the attribute [3].</String>
   <String Id="IDS_ERROR_2833">Control [4] on dialog [3] ignored the message [2].</String>
   <String Id="IDS_ERROR_2834">The next pointers on the dialog [2] do not form a single loop.</String>
   <String Id="IDS_ERROR_2835">The control [2] was not found on dialog [3].</String>
   <String Id="IDS_ERROR_2836">The control [3] on the dialog [2] cannot take focus.</String>
   <String Id="IDS_ERROR_2837">The control [3] on dialog [2] wants the winproc to return [4].</String>
   <String Id="IDS_ERROR_2838">The item [2] in the selection table has itself as a parent.</String>
   <String Id="IDS_ERROR_2839">Setting the property [2] failed.</String>
   <String Id="IDS_ERROR_2840">Error dialog name mismatch.</String>
   <String Id="IDS_ERROR_2841">No OK button was found on the error dialog.</String>
   <String Id="IDS_ERROR_2842">No text field was found on the error dialog.</String>
   <String Id="IDS_ERROR_2843">The ErrorString attribute is not supported for standard dialogs.</String>
   <String Id="IDS_ERROR_2844">Cannot execute an error dialog if the Errorstring is not set.</String>
   <String Id="IDS_ERROR_2845">The total width of the buttons exceeds the size of the error dialog.</String>
   <String Id="IDS_ERROR_2846">SetFocus did not find the required control on the error dialog.</String>
   <String Id="IDS_ERROR_2847">The control [3] on dialog [2] has both the icon and the bitmap style set.</String>
   <String Id="IDS_ERROR_2848">Tried to set control [3] as the default button on dialog [2], but the control does not exist.</String>
   <String Id="IDS_ERROR_2849">The control [3] on dialog [2] is of a type, that cannot be integer valued.</String>
   <String Id="IDS_ERROR_2850">Unrecognized volume type.</String>
   <String Id="IDS_ERROR_2851">The data for the icon [2] is not valid.</String>
   <String Id="IDS_ERROR_2852">At least one control has to be added to dialog [2] before it is used.</String>
   <String Id="IDS_ERROR_2853">Dialog [2] is a modeless dialog. The execute method should not be called on it.</String>
   <String Id="IDS_ERROR_2854">On the dialog [2] the control [3] is designated as first active control, but there is no such control.</String>
   <String Id="IDS_ERROR_2855">The radio button group [3] on dialog [2] has fewer than 2 buttons.</String>
   <String Id="IDS_ERROR_2856">Creating a second copy of the dialog [2].</String>
   <String Id="IDS_ERROR_2857">The directory [2] is mentioned in the selection table but not found.</String>
   <String Id="IDS_ERROR_2858">The data for the bitmap [2] is not valid.</String>
   <String Id="IDS_ERROR_2859">Test error message.</String>
   <String Id="IDS_ERROR_2860">Cancel button is ill-defined on dialog [2].</String>
   <String Id="IDS_ERROR_2861">The next pointers for the radio buttons on dialog [2] control [3] do not form a cycle.</String>
   <String Id="IDS_ERROR_2862">The attributes for the control [3] on dialog [2] do not define a valid icon size. Setting the size to 16.</String>
   <String Id="IDS_ERROR_2863">The control [3] on dialog [2] needs the icon [4] in size [5]x[5], but that size is not available. Loading the first available size.</String>
   <String Id="IDS_ERROR_2864">The control [3] on dialog [2] received a browse event, but there is no configurable directory for the present selection. Likely cause: browse button is not authored correctly.</String>
   <String Id="IDS_ERROR_2865">Control [3] on billboard [2] extends beyond the boundaries of the billboard [4] by [5] pixels.</String>
   <String Id="IDS_ERROR_2866">The dialog [2] is not allowed to return the argument [3].</String>
   <String Id="IDS_ERROR_2867">The error dialog property is not set.</String>
   <String Id="IDS_ERROR_2868">The error dialog [2] does not have the error style bit set.</String>
   <String Id="IDS_ERROR_2869">The dialog [2] has the error style bit set, but is not an error dialog.</String>
   <String Id="IDS_ERROR_2870">The help string [4] for control [3] on dialog [2] does not contain the separator character.</String>
   <String Id="IDS_ERROR_2871">The [2] table is out of date: [3].</String>
   <String Id="IDS_ERROR_2872">The argument of the CheckPath control event on dialog [2] is invalid.</String>
   <String Id="IDS_ERROR_2873">On the dialog [2] the control [3] has an invalid string length limit: [4].</String>
   <String Id="IDS_ERROR_2874">Changing the text font to [2] failed.</String>
   <String Id="IDS_ERROR_2875">Changing the text color to [2] failed.</String>
   <String Id="IDS_ERROR_2876">The control [3] on dialog [2] had to truncate the string: [4].</String>
   <String Id="IDS_ERROR_2877">The binary data [2] was not found</String>
   <String Id="IDS_ERROR_2878">On the dialog [2] the control [3] has a possible value: [4]. This is an invalid or duplicate value.</String>
   <String Id="IDS_ERROR_2879">The control [3] on dialog [2] cannot parse the mask string: [4].</String>
   <String Id="IDS_ERROR_2880">Do not perform the remaining control events.</String>
   <String Id="IDS_ERROR_2881">CMsiHandler initialization failed.</String>
   <String Id="IDS_ERROR_2882">Dialog window class registration failed.</String>
   <String Id="IDS_ERROR_2883">CreateNewDialog failed for the dialog [2].</String>
   <String Id="IDS_ERROR_2884">Failed to create a window for the dialog [2].</String>
   <String Id="IDS_ERROR_2885">Failed to create the control [3] on the dialog [2].</String>
   <String Id="IDS_ERROR_2886">Creating the [2] table failed.</String>
   <String Id="IDS_ERROR_2887">Creating a cursor to the [2] table failed.</String>
   <String Id="IDS_ERROR_2888">Executing the [2] view failed.</String>
   <String Id="IDS_ERROR_2889">Creating the window for the control [3] on dialog [2] failed.</String>
   <String Id="IDS_ERROR_2890">The handler failed in creating an initialized dialog.</String>
   <String Id="IDS_ERROR_2891">Failed to destroy window for dialog [2].</String>
   <String Id="IDS_ERROR_2892">[2] is an integer only control, [3] is not a valid integer value.</String>
   <String Id="IDS_ERROR_2893">The control [3] on dialog [2] can accept property values that are at most [5] characters long. The value [4] exceeds this limit, and has been truncated.</String>
   <String Id="IDS_ERROR_2894">Loading RICHED20.DLL failed. GetLastError() returned: [2].</String>
   <String Id="IDS_ERROR_2895">Freeing RICHED20.DLL failed. GetLastError() returned: [2].</String>
   <String Id="IDS_ERROR_2896">Executing action [2] failed.</String>
   <String Id="IDS_ERROR_2897">Failed to create any [2] font on this system.</String>
   <String Id="IDS_ERROR_2898">For [2] textstyle, the system created a '[3]' font, in [4] character set.</String>
   <String Id="IDS_ERROR_2899">Failed to create [2] textstyle. GetLastError() returned: [3].</String>
   <String Id="IDS_ERROR_2901">Invalid parameter to operation [2]: Parameter [3].</String>
   <String Id="IDS_ERROR_2902">Operation [2] called out of sequence.</String>
   <String Id="IDS_ERROR_2903">The file [2] is missing.</String>
   <String Id="IDS_ERROR_2904">Could not BindImage file [2].</String>
   <String Id="IDS_ERROR_2905">Could not read record from script file [2].</String>
   <String Id="IDS_ERROR_2906">Missing header in script file [2].</String>
   <String Id="IDS_ERROR_2907">Could not create secure security descriptor. Error: [2].</String>
   <String Id="IDS_ERROR_2908">Could not register component [2].</String>
   <String Id="IDS_ERROR_2909">Could not unregister component [2].</String>
   <String Id="IDS_ERROR_2910">Could not determine user's security ID.</String>
   <String Id="IDS_ERROR_2911">Could not remove the folder [2].</String>
   <String Id="IDS_ERROR_2912">Could not schedule file [2] for removal on restart.</String>
   <String Id="IDS_ERROR_2919">No cabinet specified for compressed file: [2].</String>
   <String Id="IDS_ERROR_2920">Source directory not specified for file [2].</String>
   <String Id="IDS_ERROR_2924">Script [2] version unsupported. Script version: [3], minimum version: [4], maximum version: [5].</String>
   <String Id="IDS_ERROR_2927">ShellFolder id [2] is invalid.</String>
   <String Id="IDS_ERROR_2928">Exceeded maximum number of sources. Skipping source '[2]'.</String>
   <String Id="IDS_ERROR_2929">Could not determine publishing root. Error: [2].</String>
   <String Id="IDS_ERROR_2932">Could not create file [2] from script data. Error: [3].</String>
   <String Id="IDS_ERROR_2933">Could not initialize rollback script [2].</String>
   <String Id="IDS_ERROR_2934">Could not secure transform [2]. Error [3].</String>
   <String Id="IDS_ERROR_2935">Could not unsecure transform [2]. Error [3].</String>
   <String Id="IDS_ERROR_2936">Could not find transform [2].</String>
   <String Id="IDS_ERROR_2937">Windows Installer cannot install a system file protection catalog. Catalog: [2], Error: [3].</String>
   <String Id="IDS_ERROR_2938">Windows Installer cannot retrieve a system file protection catalog from the cache. Catalog: [2], Error: [3].</String>
   <String Id="IDS_ERROR_2939">Windows Installer cannot delete a system file protection catalog from the cache. Catalog: [2], Error: [3].</String>
   <String Id="IDS_ERROR_2940">Directory Manager not supplied for source resolution.</String>
   <String Id="IDS_ERROR_2941">Unable to compute the CRC for file [2].</String>
   <String Id="IDS_ERROR_2942">BindImage action has not been executed on [2] file.</String>
   <String Id="IDS_ERROR_2943">This version of Windows does not support deploying 64-bit packages. The script [2] is for a 64-bit package.</String>
   <String Id="IDS_ERROR_2944">GetProductAssignmentType failed.</String>
   <String Id="IDS_ERROR_2945">Installation of ComPlus App [2] failed with error [3].</String>
   <String Id="IDS_ERROR_3001">The patches in this list contain incorrect sequencing information: [2][3][4][5][6][7][8][9][10][11][12][13][14][15][16].</String>
   <String Id="IDS_ERROR_3002">Patch [2] contains invalid sequencing information. </String>
   <String Id="IDS_ERROR_25032">The installer failed to install the LSI driver.</String>
   <String Id="IDS_ERROR_25520">Failed to create security descriptor for [3]\[4], system error: [2]</String>
   <String Id="IDS_ERROR_25521">Failed to set security descriptor on object [3], system error: [2]</String>
   <String Id="IDS_ERROR_25522">Unknown Object Type [3], system error: [2]</String>
   <String Id="IDS_ERROR_27500">This setup requires Internet Information Server 4.0 or higher for configuring IIS Virtual Roots. Please make sure that you have IIS 4.0 or higher.</String>
   <String Id="IDS_ERROR_27501">This setup requires Administrator privileges for configuring IIS Virtual Roots.</String>
   <String Id="IDS_ERROR_27502">Could not connect to [2] '[3]'. [4]</String>
   <String Id="IDS_ERROR_27503">Error retrieving version string from [2] '[3]'. [4]</String>
   <String Id="IDS_ERROR_27504">SQL version requirements not met: [3]. This installation requires [2] [4] or later.</String>
   <String Id="IDS_ERROR_27505">Could not open SQL script file [2].</String>
   <String Id="IDS_ERROR_27506">Error executing SQL script [2]. Line [3]. [4]</String>
   <String Id="IDS_ERROR_27507">Connection or browsing for database servers requires that MDAC be installed.</String>
   <String Id="IDS_ERROR_27508">Error installing COM+ application [2]. [3]</String>
   <String Id="IDS_ERROR_27509">Error uninstalling COM+ application [2]. [3]</String>
   <String Id="IDS_ERROR_27510">Error installing COM+ application [2].  Could not load Microsoft(R) .NET class libraries. Registering .NET serviced components requires that Microsoft(R) .NET Framework be installed.</String>
   <String Id="IDS_ERROR_27511">Could not execute SQL script file [2]. Connection not open: [3]</String>
   <String Id="IDS_ERROR_27512">Error beginning transactions for [2] '[3]'. Database [4]. [5]</String>
   <String Id="IDS_ERROR_27513">Error committing transactions for [2] '[3]'. Database [4]. [5]</String>
   <String Id="IDS_ERROR_27514">This installation requires a Microsoft SQL Server. The specified server '[3]' is a Microsoft SQL Server Desktop Engine (MSDE).</String>
   <String Id="IDS_ERROR_27515">Error retrieving schema version from [2] '[3]'. Database: '[4]'. [5]</String>
   <String Id="IDS_ERROR_27516">Error writing schema version to [2] '[3]'. Database: '[4]'. [5]</String>
   <String Id="IDS_ERROR_27517">This installation requires Administrator privileges for installing COM+ applications. Log on as an administrator and then retry this installation.</String>
   <String Id="IDS_ERROR_27518">The COM+ application "[2]" is configured to run as an NT service; this requires COM+ 1.5 or later on the system. Since your system has COM+ 1.0, this application will not be installed.</String>
   <String Id="IDS_ERROR_27519">Error updating XML file [2]. [3]</String>
   <String Id="IDS_ERROR_27520">Error opening XML file [2]. [3]</String>
   <String Id="IDS_ERROR_27521">This setup requires MSXML 3.0 or higher for configuring XML files. Please make sure that you have version 3.0 or higher.</String>
   <String Id="IDS_ERROR_27522">Error creating XML file [2]. [3]</String>
   <String Id="IDS_ERROR_27523">Error loading servers.</String>
   <String Id="IDS_ERROR_27524">Error loading NetApi32.DLL. The ISNetApi.dll needs to have NetApi32.DLL properly loaded and requires an NT based operating system.</String>
   <String Id="IDS_ERROR_27525">Server not found. Verify that the specified server exists. The server name can not be empty.</String>
   <String Id="IDS_ERROR_27526">Unspecified error from ISNetApi.dll.</String>
   <String Id="IDS_ERROR_27527">The buffer is too small.</String>
   <String Id="IDS_ERROR_27528">Access denied. Check administrative rights.</String>
   <String Id="IDS_ERROR_27529">Invalid computer.</String>
   <String Id="IDS_ERROR_27530">Undefined switch case.</String>
   <String Id="IDS_ERROR_27531">Unhandled exception.</String>
   <String Id="IDS_ERROR_27532">Invalid user name for this server or domain.</String>
   <String Id="IDS_ERROR_27533">The case-sensitive passwords do not match.</String>
   <String Id="IDS_ERROR_27534">The list is empty.</String>
   <String Id="IDS_ERROR_27535">Access violation.</String>
   <String Id="IDS_ERROR_27536">Error getting group.</String>
   <String Id="IDS_ERROR_27537">Error adding user to group. Verify that the group exists for this domain or server.</String>
   <String Id="IDS_ERROR_27538">Error creating user.</String>
   <String Id="IDS_ERROR_27539">ERROR_NETAPI_ERROR_NOT_PRIMARY returned from NetAPI.</String>
   <String Id="IDS_ERROR_27540">The specified user already exists.</String>
   <String Id="IDS_ERROR_27541">The specified group already exists.</String>
   <String Id="IDS_ERROR_27542">Invalid password. Verify that the password is in accordance with your network password policy.</String>
   <String Id="IDS_ERROR_27543">Invalid name.</String>
   <String Id="IDS_ERROR_27544">Invalid group.</String>
   <String Id="IDS_ERROR_27545">The user name can not be empty and must be in the format DOMAIN\Username.</String>
   <String Id="IDS_ERROR_27546">Error loading or creating INI file in the user TEMP directory.</String>
   <String Id="IDS_ERROR_27547">ISNetAPI.dll is not loaded or there was an error loading the dll. This dll needs to be loaded for this operation. Verify that the dll is in the SUPPORTDIR directory.</String>
   <String Id="IDS_ERROR_27548">Error deleting INI file containing new user information from the user's TEMP directory.</String>
   <String Id="IDS_ERROR_27549">Error getting the primary domain controller (PDC).</String>
   <String Id="IDS_ERROR_27550">Every field must have a value in order to create a user.</String>
   <String Id="IDS_ERROR_27551">ODBC driver for [2] not found. This is required to connect to [2] database servers.</String>
   <String Id="IDS_ERROR_27552">Error creating database [4]. Server: [2] [3]. [5]</String>
   <String Id="IDS_ERROR_27553">Error connecting to database [4]. Server: [2] [3]. [5]</String>
   <String Id="IDS_ERROR_27554">Error attempting to open connection [2]. No valid database metadata associated with this connection.</String>
   <String Id="IDS_ERROR_28030">The installer failed to install the USB driver. To ensure a successful installation, please restart your machine and relaunch this installer.</String>
   <String Id="IDS_ERROR_28033">Unable to connect to %s. TCP connection failure on TCP port %d. Please check the server name entered.</String>
   <String Id="IDS_ERROR_28034">Unable to connect to LDAP at %s. Please check the server name entered.</String>
   <String Id="IDS_ERROR_28035">Invalid credentials. Please enter an administrator username and password for the specified server.</String>
   <String Id="IDS_ERROR_28036">Failed to bind to LDAP at %s. %s.</String>
   <String Id="IDS_ERROR_28037">Failed to update LDAP at %s. Access Denied. Please enter an administrator username and password with sufficient access rights on the specified server.</String>
   <String Id="IDS_ERROR_28038">Failed to update LDAP at %s. Schema violation. It is possible that the specified server is running an old software version and will need to be upgraded to support this agent installation.</String>
   <String Id="IDS_ERROR_28039">Failed to update LDAP at %s. %s.</String>
   <String Id="IDS_ERROR_28040">Invalid username format.</String>
   <String Id="IDS_ERROR_28041">This agent is already registered at %s. Do you want to continue this installation and update the existing registration information?</String>
   <String Id="IDS_ERROR_28042">This agent is registered multiple times at %s. Using the Horizon Administrator, please remove the entries for this machine and then attempt this installation again.</String>
   <String Id="IDS_ERROR_28045">If specifying administrator credentials you must input a username and password.</String>
   <String Id="IDS_ERROR_28046">Failed to obtain critical security information from LDAP at %s. It is possible that the specified server is running an old software version and will need to be upgraded to support this agent installation.</String>
   <String Id="IDS_ERROR_28053">A DLL failed to register. Please see the most recent %TEMP%\vminst*.log file for details.</String>
   <String Id="IDS_ERROR_28060">Error installing the Intel HECI Device Driver.</String>
   <String Id="IDS_ERROR_28062">An Agent for this computer is already registered at %s as a Virtual Machine. Either change the computername of this computer, or remove the Virtual Machine entry with Horizon Administrator on the Connection Server and retry this installation.</String>
   <String Id="IDS_ERROR_28065">The installer failed to install the Smartcard Redirector driver.</String>
   <String Id="IDS_ERROR_28089">The Connection Server %s is not running a sufficient software version to fully support this Agent. You should upgrade your Connection Servers first before continuing. If you continue anyway you will need to reinstall this Agent later to get full RDS Host functionality. Do you want to continue this installation?</String>
   <String Id="IDS_ERROR_28090">Setup has detected non-default registry values:

HKLM\SYSTEM\CurrentControlSet\Control\TerminalServer\WinStations\RDP-Tcp\MaxInstanceCount

HKLM\SYSTEM\CurrentControlSet\Control\TerminalServer\WinStations\Horizon-PCOIP\MaxInstanceCount

Since Horizon manages session limits, having these registry settings will cause unexpected behavior.</String>
   <String Id="IDS_ERROR_28092">The installer failed to install the Horizon Virtual Webcam Driver. To ensure a successful installation, please restart your machine and relaunch this installer.</String>
   <String Id="IDS_ERROR_28096">Please refer to the "Deploying USB Devices in a Secure Horizon Environment" document for guidance on using USB redirection securely.</String>
   <String Id="IDS_ERROR_28100">You are installing Omnissa Horizon Agent with some of the installer checks disabled. This is not recommended and will result in an unsupported installation, please cancel and re-run the installer with checks enabled.</String>
   <String Id="IDS_ERROR_28109">VC%d.%d nonredist is not installed on the machine. Horizon Agent installation may fail if you continue. You can download VC%d.%d nonredist installers from msvc_debug_runtime_installer Conan build.</String>
   <String Id="IDS_ERROR_28110">The Virtualization pack for Skype for Business requires .NET Framework 4.0 or newer to be present before installing this feature. Install the .NET Framework and re-launch the installer to install this feature.</String>
   <String Id="IDS_ERROR_28111">Please add or remove a feature to continue.</String>
   <String Id="IDS_ERROR_28113">During a modify installation,the "Instant Clone Agent" (NGVC) feature can not be changed. If you would like to add or remove a cloning feature please uninstall and reinstall the agent.</String>
   <String Id="IDS_ERROR_28114">You must have administrative privileges to modify or repair this installation. You can also run the following command from an elevated command prompt:

msiexec.exe /i [DATABASE]</String>
   <String Id="IDS_ERROR_28115">You must have administrative privileges to install this patch. You may need to run the following command from an elevated command prompt:

msiexec.exe /p [PATCH]</String>

   <String Id="IDS_ERROR_28116">Print Spooler service is not running. Horizon Integrated Printing feature may not be installed.</String>

   <!-- L10n properties for merge module services -->
   <String Id="IDS_PCOIPSG_DISPLAY_NAME">Omnissa Horizon PCoIP Security Gateway</String>
   <String Id="IDS_PCOIPSG_DESCRIPTION">Provides PCoIP gateway services.</String>

   <String Id="IDS_WSNM_SERVICE_DISPLAY_NAME">Omnissa Horizon Agent</String>
   <String Id="WsnmServiceDescription">Provides Horizon agent services.</String>

   <String Id="IDS_WSSH_SERVICE_DISPLAY_NAME">Omnissa Horizon Script Host</String>
   <String Id="IDS_WSSH_SERVICE_DESCRIPTION">Provides script host services.</String>

   <String Id="IDS_VMLM_SERVICE_DISPLAY_NAME">Omnissa Horizon Logon Monitor</String>
   <String Id="IDS_VMLM_SERVICE_DESCRIPTION">Provides logon monitoring services.</String>

   <String Id="IDS_HZMON_SERVICE_DISPLAY_NAME">Omnissa Horizon Monitoring Service</String>
   <String Id="IDS_HZMON_SERVICE_DESCRIPTION">Provides monitoring services.</String>

   <String Id="IDS_VMWRXG_SERVICE_DISPLAY_NAME">Omnissa Horizon Remote Experience Service</String>
   <String Id="IDS_VMWRXG_SERVICE_DESCRIPTION">Provides remote experience generic services.</String>

   <String Id="IDS_AUTORESTART">Automatically restart system on successful completion</String>

   <String Id="IDS_AUTORESTART_MODIFY">Automatically restart system on successful completion if necessary</String>

</WixLocalization>
