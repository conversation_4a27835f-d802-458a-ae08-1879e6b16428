/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

#include <d3d11.h>

class MockD3D11Device;

class MockD3D11DeviceContext : public ID3D11DeviceContext {
public:
   MockD3D11DeviceContext(MockD3D11Device *device);
   ~MockD3D11DeviceContext();

   ULONG STDMETHODCALLTYPE AddRef() override;
   ULONG STDMETHODCALLTYPE Release() override;
   HRESULT STDMETHODCALLTYPE QueryInterface(REFIID riid, void **ppvObject) override;
   void STDMETHODCALLTYPE GetDevice(ID3D11Device **ppDevice) override;

   HRESULT STDMETHODCALLTYPE GetPrivateData(REFGUID, UINT *, void *) override { return E_NOTIMPL; }
   HRESULT STDMETHODCALLTYPE SetPrivateData(REFGUID, UINT, const void *) override
   {
      return E_NOTIMPL;
   }
   HRESULT STDMETHODCALLTYPE SetPrivateDataInterface(REFGUID, const IUnknown *) override
   {
      return E_NOTIMPL;
   }

   // ID3D11DeviceContext (all pure virtuals stubbed)
   void STDMETHODCALLTYPE VSSetConstantBuffers(UINT, UINT, ID3D11Buffer *const *) override {}
   void STDMETHODCALLTYPE PSSetShaderResources(UINT, UINT,
                                               ID3D11ShaderResourceView *const *) override
   {}
   void STDMETHODCALLTYPE PSSetShader(ID3D11PixelShader *, ID3D11ClassInstance *const *,
                                      UINT) override
   {}
   void STDMETHODCALLTYPE PSSetSamplers(UINT, UINT, ID3D11SamplerState *const *) override {}
   void STDMETHODCALLTYPE VSSetShader(ID3D11VertexShader *, ID3D11ClassInstance *const *,
                                      UINT) override
   {}
   void STDMETHODCALLTYPE DrawIndexed(UINT, UINT, INT) override {}
   void STDMETHODCALLTYPE Draw(UINT, UINT) override {}
   HRESULT STDMETHODCALLTYPE Map(ID3D11Resource *, UINT, D3D11_MAP, UINT,
                                 D3D11_MAPPED_SUBRESOURCE *) override
   {
      return E_NOTIMPL;
   }
   void STDMETHODCALLTYPE Unmap(ID3D11Resource *, UINT) override {}
   void STDMETHODCALLTYPE PSSetConstantBuffers(UINT, UINT, ID3D11Buffer *const *) override {}
   void STDMETHODCALLTYPE IASetInputLayout(ID3D11InputLayout *) override {}
   void STDMETHODCALLTYPE IASetVertexBuffers(UINT, UINT, ID3D11Buffer *const *, const UINT *,
                                             const UINT *) override
   {}
   void STDMETHODCALLTYPE IASetIndexBuffer(ID3D11Buffer *, DXGI_FORMAT, UINT) override {}
   void STDMETHODCALLTYPE DrawIndexedInstanced(UINT, UINT, UINT, INT, UINT) override {}
   void STDMETHODCALLTYPE DrawInstanced(UINT, UINT, UINT, UINT) override {}
   void STDMETHODCALLTYPE GSSetConstantBuffers(UINT, UINT, ID3D11Buffer *const *) override {}
   void STDMETHODCALLTYPE GSSetShader(ID3D11GeometryShader *, ID3D11ClassInstance *const *,
                                      UINT) override
   {}
   void STDMETHODCALLTYPE IASetPrimitiveTopology(D3D11_PRIMITIVE_TOPOLOGY) override {}
   void STDMETHODCALLTYPE VSSetShaderResources(UINT, UINT,
                                               ID3D11ShaderResourceView *const *) override
   {}
   void STDMETHODCALLTYPE VSSetSamplers(UINT, UINT, ID3D11SamplerState *const *) override {}
   void STDMETHODCALLTYPE Begin(ID3D11Asynchronous *) override {}
   void STDMETHODCALLTYPE End(ID3D11Asynchronous *) override {}
   HRESULT STDMETHODCALLTYPE GetData(ID3D11Asynchronous *, void *, UINT, UINT) override
   {
      return E_NOTIMPL;
   }
   void STDMETHODCALLTYPE SetPredication(ID3D11Predicate *, BOOL) override {}
   void STDMETHODCALLTYPE GSSetShaderResources(UINT, UINT,
                                               ID3D11ShaderResourceView *const *) override
   {}
   void STDMETHODCALLTYPE GSSetSamplers(UINT, UINT, ID3D11SamplerState *const *) override {}
   void STDMETHODCALLTYPE OMSetRenderTargets(UINT, ID3D11RenderTargetView *const *,
                                             ID3D11DepthStencilView *) override
   {}
   void STDMETHODCALLTYPE OMSetRenderTargetsAndUnorderedAccessViews(
      UINT, ID3D11RenderTargetView *const *, ID3D11DepthStencilView *, UINT, UINT,
      ID3D11UnorderedAccessView *const *, const UINT *) override
   {}
   void STDMETHODCALLTYPE OMSetBlendState(ID3D11BlendState *, const FLOAT[4], UINT) override {}
   void STDMETHODCALLTYPE OMSetDepthStencilState(ID3D11DepthStencilState *, UINT) override {}
   void STDMETHODCALLTYPE SOSetTargets(UINT, ID3D11Buffer *const *, const UINT *) override {}
   void STDMETHODCALLTYPE DrawAuto() override {}
   void STDMETHODCALLTYPE DrawIndexedInstancedIndirect(ID3D11Buffer *, UINT) override {}
   void STDMETHODCALLTYPE DrawInstancedIndirect(ID3D11Buffer *, UINT) override {}
   void STDMETHODCALLTYPE Dispatch(UINT, UINT, UINT) override {}
   void STDMETHODCALLTYPE DispatchIndirect(ID3D11Buffer *, UINT) override {}
   void STDMETHODCALLTYPE RSSetState(ID3D11RasterizerState *) override {}
   void STDMETHODCALLTYPE RSSetViewports(UINT, const D3D11_VIEWPORT *) override {}
   void STDMETHODCALLTYPE RSSetScissorRects(UINT, const D3D11_RECT *) override {}
   void STDMETHODCALLTYPE CopySubresourceRegion(ID3D11Resource *, UINT, UINT, UINT, UINT,
                                                ID3D11Resource *, UINT, const D3D11_BOX *) override
   {}
   void STDMETHODCALLTYPE CopyResource(ID3D11Resource *, ID3D11Resource *) override {}
   void STDMETHODCALLTYPE UpdateSubresource(ID3D11Resource *, UINT, const D3D11_BOX *, const void *,
                                            UINT, UINT) override
   {}
   void STDMETHODCALLTYPE CopyStructureCount(ID3D11Buffer *, UINT,
                                             ID3D11UnorderedAccessView *) override
   {}
   void STDMETHODCALLTYPE ClearRenderTargetView(ID3D11RenderTargetView *, const FLOAT[4]) override
   {}
   void STDMETHODCALLTYPE ClearUnorderedAccessViewUint(ID3D11UnorderedAccessView *,
                                                       const UINT[4]) override
   {}
   void STDMETHODCALLTYPE ClearUnorderedAccessViewFloat(ID3D11UnorderedAccessView *,
                                                        const FLOAT[4]) override
   {}
   void STDMETHODCALLTYPE ClearDepthStencilView(ID3D11DepthStencilView *, UINT, FLOAT,
                                                UINT8) override
   {}
   void STDMETHODCALLTYPE GenerateMips(ID3D11ShaderResourceView *) override {}
   void STDMETHODCALLTYPE SetResourceMinLOD(ID3D11Resource *, FLOAT) override {}
   FLOAT STDMETHODCALLTYPE GetResourceMinLOD(ID3D11Resource *) override { return 0.0f; }
   void STDMETHODCALLTYPE ResolveSubresource(ID3D11Resource *, UINT, ID3D11Resource *, UINT,
                                             DXGI_FORMAT) override
   {}
   void STDMETHODCALLTYPE ExecuteCommandList(ID3D11CommandList *, BOOL) override {}
   void STDMETHODCALLTYPE HSSetShaderResources(UINT, UINT,
                                               ID3D11ShaderResourceView *const *) override
   {}
   void STDMETHODCALLTYPE HSSetShader(ID3D11HullShader *, ID3D11ClassInstance *const *,
                                      UINT) override
   {}
   void STDMETHODCALLTYPE HSSetSamplers(UINT, UINT, ID3D11SamplerState *const *) override {}
   void STDMETHODCALLTYPE HSSetConstantBuffers(UINT, UINT, ID3D11Buffer *const *) override {}
   void STDMETHODCALLTYPE DSSetShaderResources(UINT, UINT,
                                               ID3D11ShaderResourceView *const *) override
   {}
   void STDMETHODCALLTYPE DSSetShader(ID3D11DomainShader *, ID3D11ClassInstance *const *,
                                      UINT) override
   {}
   void STDMETHODCALLTYPE DSSetSamplers(UINT, UINT, ID3D11SamplerState *const *) override {}
   void STDMETHODCALLTYPE DSSetConstantBuffers(UINT, UINT, ID3D11Buffer *const *) override {}
   void STDMETHODCALLTYPE CSSetShaderResources(UINT, UINT,
                                               ID3D11ShaderResourceView *const *) override
   {}
   void STDMETHODCALLTYPE CSSetUnorderedAccessViews(UINT, UINT, ID3D11UnorderedAccessView *const *,
                                                    const UINT *) override
   {}
   void STDMETHODCALLTYPE CSSetShader(ID3D11ComputeShader *, ID3D11ClassInstance *const *,
                                      UINT) override
   {}
   void STDMETHODCALLTYPE CSSetSamplers(UINT, UINT, ID3D11SamplerState *const *) override {}
   void STDMETHODCALLTYPE CSSetConstantBuffers(UINT, UINT, ID3D11Buffer *const *) override {}
   void STDMETHODCALLTYPE VSGetConstantBuffers(UINT, UINT, ID3D11Buffer **) override {}
   void STDMETHODCALLTYPE PSGetShaderResources(UINT, UINT, ID3D11ShaderResourceView **) override {}
   void STDMETHODCALLTYPE PSGetShader(ID3D11PixelShader **, ID3D11ClassInstance **, UINT *) override
   {}
   void STDMETHODCALLTYPE PSGetSamplers(UINT, UINT, ID3D11SamplerState **) override {}
   void STDMETHODCALLTYPE VSGetShader(ID3D11VertexShader **, ID3D11ClassInstance **,
                                      UINT *) override
   {}
   void STDMETHODCALLTYPE PSGetConstantBuffers(UINT, UINT, ID3D11Buffer **) override {}
   void STDMETHODCALLTYPE IAGetInputLayout(ID3D11InputLayout **) override {}
   void STDMETHODCALLTYPE IAGetVertexBuffers(UINT, UINT, ID3D11Buffer **, UINT *, UINT *) override
   {}
   void STDMETHODCALLTYPE IAGetIndexBuffer(ID3D11Buffer **, DXGI_FORMAT *, UINT *) override {}
   void STDMETHODCALLTYPE GSGetConstantBuffers(UINT, UINT, ID3D11Buffer **) override {}
   void STDMETHODCALLTYPE GSGetShader(ID3D11GeometryShader **, ID3D11ClassInstance **,
                                      UINT *) override
   {}
   void STDMETHODCALLTYPE IAGetPrimitiveTopology(D3D11_PRIMITIVE_TOPOLOGY *) override {}
   void STDMETHODCALLTYPE VSGetShaderResources(UINT, UINT, ID3D11ShaderResourceView **) override {}
   void STDMETHODCALLTYPE VSGetSamplers(UINT, UINT, ID3D11SamplerState **) override {}
   void STDMETHODCALLTYPE GetPredication(ID3D11Predicate **, BOOL *) override {}
   void STDMETHODCALLTYPE GSGetShaderResources(UINT, UINT, ID3D11ShaderResourceView **) override {}
   void STDMETHODCALLTYPE GSGetSamplers(UINT, UINT, ID3D11SamplerState **) override {}
   void STDMETHODCALLTYPE OMGetRenderTargets(UINT, ID3D11RenderTargetView **,
                                             ID3D11DepthStencilView **) override
   {}
   void STDMETHODCALLTYPE OMGetRenderTargetsAndUnorderedAccessViews(
      UINT, ID3D11RenderTargetView **, ID3D11DepthStencilView **, UINT, UINT,
      ID3D11UnorderedAccessView **) override
   {}
   void STDMETHODCALLTYPE OMGetBlendState(ID3D11BlendState **, FLOAT[4], UINT *) override {}
   void STDMETHODCALLTYPE OMGetDepthStencilState(ID3D11DepthStencilState **, UINT *) override {}
   void STDMETHODCALLTYPE SOGetTargets(UINT, ID3D11Buffer **) override {}
   void STDMETHODCALLTYPE RSGetState(ID3D11RasterizerState **) override {}
   void STDMETHODCALLTYPE RSGetViewports(UINT *, D3D11_VIEWPORT *) override {}
   void STDMETHODCALLTYPE RSGetScissorRects(UINT *, D3D11_RECT *) override {}
   void STDMETHODCALLTYPE HSGetShaderResources(UINT, UINT, ID3D11ShaderResourceView **) override {}
   void STDMETHODCALLTYPE HSGetShader(ID3D11HullShader **, ID3D11ClassInstance **, UINT *) override
   {}
   void STDMETHODCALLTYPE HSGetSamplers(UINT, UINT, ID3D11SamplerState **) override {}
   void STDMETHODCALLTYPE HSGetConstantBuffers(UINT, UINT, ID3D11Buffer **) override {}
   void STDMETHODCALLTYPE DSGetShaderResources(UINT, UINT, ID3D11ShaderResourceView **) override {}
   void STDMETHODCALLTYPE DSGetShader(ID3D11DomainShader **, ID3D11ClassInstance **,
                                      UINT *) override
   {}
   void STDMETHODCALLTYPE DSGetSamplers(UINT, UINT, ID3D11SamplerState **) override {}
   void STDMETHODCALLTYPE DSGetConstantBuffers(UINT, UINT, ID3D11Buffer **) override {}
   void STDMETHODCALLTYPE CSGetShaderResources(UINT, UINT, ID3D11ShaderResourceView **) override {}
   void STDMETHODCALLTYPE CSGetUnorderedAccessViews(UINT, UINT,
                                                    ID3D11UnorderedAccessView **) override
   {}
   void STDMETHODCALLTYPE CSGetShader(ID3D11ComputeShader **, ID3D11ClassInstance **,
                                      UINT *) override
   {}
   void STDMETHODCALLTYPE CSGetSamplers(UINT, UINT, ID3D11SamplerState **) override {}
   void STDMETHODCALLTYPE CSGetConstantBuffers(UINT, UINT, ID3D11Buffer **) override {}
   void STDMETHODCALLTYPE ClearState() override {}
   void STDMETHODCALLTYPE Flush() override {}
   D3D11_DEVICE_CONTEXT_TYPE STDMETHODCALLTYPE GetType() override
   {
      return D3D11_DEVICE_CONTEXT_IMMEDIATE;
   }
   UINT STDMETHODCALLTYPE GetContextFlags() override { return 0; }
   HRESULT STDMETHODCALLTYPE FinishCommandList(BOOL, ID3D11CommandList **) override
   {
      return E_NOTIMPL;
   }

private:
   ULONG m_refCount = 1;
   MockD3D11Device *m_device;
};
