/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * vvcTestMock.cpp --
 *
 *      Defines to support mock tests.
 */

#pragma once

#include "testBase.h"

#ifdef _MSC_VER
/*
 * Suppress MSVC warning about non-standard zero-length array
 * 'PerfCounterEntryExtBase' defined in 'PerfCounterEntry', which is an
 * issue in C++ for copy constructors and not relevant for vvc tests.
 */
#   pragma warning(disable : 4200)

/*
 * Suppress MSVC warning about constant comparision in EXPECT_EQ().
 * Rule C6326: Potential comparison of a constant with another constant.
 */
#   pragma warning(disable : 6326)
#endif


class TestVvc : public TestBase {
public:
   static VvcInstanceHandle instanceHandle;
   static VvcSessionHandle sessionHandle;

   static void SetUpTestCase();
   static void TearDownTestCase();

   template<typename T>
   static void LogResultToConsole(std::string_view testCasse, const T &expected, const T &actual,
                                  bool addLineSeparator = true);

protected:
   void SetUp() override;
   void TearDown() override;

private:
   static void InitLog(std::string_view testName, std::string_view logLevel = "debug");

   static bool InitLogImpl(std::string_view testName);

   static void OuputLineSeparator(std::ostream &stream = std::cout, uint32 length = 80,
                                  char character = '=');
};


/*
 * Redfined from vvcPerfCounters.c
 */
typedef struct _PerfCounterEntry {
   PerfCounterValueDataType counterDatatype;
   double lastUpdate;
   VvcPerfValue value;
   PerfCounterEntryExtBase ext[0];
} PerfCounterEntry;


#ifdef __cplusplus
extern "C" {
#endif

extern VvcInstance *gInstanceMain;

#ifdef __cplusplus
}
#endif
