{"version": "0.5", "requires": ["zlib/1.3.1#070fdb998838959a825710509689296b%1733494270.942", "theora/1.1.1#80a4cffeef63baec0828b3e9012f0e6f%1715834616.288", "sse2neon/1.7.0#1ef6b4b578e8d52e9fd2f047f574eab5%1729949215.449", "speexdsp/1.2rc3#39d2a177a6d38971a8b0c5ad3189ba70%1712027434.492", "speex/1.2rc2#fe17dcfabdac9e92c508805b71d2ffa1%1712027433.216", "snappy/1.1.7#72841864bdd1c7f79ff4ba47980761e7%1713251799.36", "pcsc-lite-android/1.8.11#8e0586a46b4b35641b2353b210260905%1729669455.694", "opus/1.4#ff4e5a955840682b271bb7fe2c29ace6%1713283597.769", "openssl_fips_validated/3.0.9#2af1ca5cc524fa3775353719b750e440%1740295376.965", "openssl/3.0.16#09fdc1e695600e5f11185e9d5aee6aae%1740295373.964", "ogg/1.3.2#8bd0bd36c461da588578ae7715f0bc0f%1713283595.762", "nlohmann_json/3.11.2#66de7b3253d039ae75ae40a0b2050bcd%1709206230.076", "libyuv/1882#2f016ac1d9f331bea900cf5a04aac7e5%1741363798.278", "libxml2/2.13.8#4ad022515986ec53d7db24fa831fa57a%1747381344.498", "libpng/1.6.48#4af49de4b86d319c4c64d0be65e71774%1747755034.838", "libjpeg-turbo/3.0.1#0c3ec26496747234ae68a3055636c006%1721007067.804", "libcurl/8.10.0#fbfed1ad02dcc0432775363377a10303%1744204106.236", "icu/74.2#df0a1df80ef1e8db3db270f57414918b%1711025915.669", "gtest/1.17.0#b81d3ff53b1af0ef3d23373a0a923c98%1748940935.811", "eglib/2.4.0#4f12221ab68e68427fbe4cee6b90ee59%1718189595.671"], "build_requires": ["zlib/1.3.1#070fdb998838959a825710509689296b%1733494270.942", "xapian-core/1.4.19#a44dbb1d01de1eb9a9f95f2fd25427c5%1721007068.813", "util_linux/2.39.3#c69f594b4712cfa4e05995e37c2268c5%1740648977.648", "pkgconf/2.1.0#0177a5313f23035f337f78f6cfdcdcba%1719990004.814", "perl/5.38.0#e5f3f95ef101e56de5a37edfcdd472ee%1726298679.552", "ninja/1.11.1#f73fb14f87f6343107224de5c8734c0d%1704462561.271", "nasm/2.16.01#32bb0d9a9c2f9a786bfc501ab9589eb4%1745586475.269", "meson/1.3.1#ba55deb2839bcf58e99bee7eb5e7d496%1747979472.099", "make/4.4.1#572a6685bf5343b63f81a1e0c57c7b75%1745586474.861", "m4/1.4.19#265fb80b0415aef2ff3b0177569e7629%1711337655.59", "libtool/2.4.7#9bea50bfb7323892ff95c79fe29d47c7%1711678421.691", "libiconv/1.17#055c1d6f948ec3843b03b945360c6b3e%1718780206.714", "icu/74.2#df0a1df80ef1e8db3db270f57414918b%1711025915.669", "gradle/7.4#732d94d4fad4456c8936a3e770ed1f57%1709380854.777", "gnu-config/cci.20210814#7778c4be47471550efcfab6245e8fcc4%1720447612.743", "glibc/2.17#f31867240555f126de18ce7b95466545%1747919660.68", "gettext/0.22.5#13aeb9d05f4fa4a7d916ed95164dc781%1744999949.049", "gcc/12.1#e1828956db1056558e7f6230e2d34011%1712665137.798", "flex/2.6.4#9e15d97730c7c05d7673701644956217%1706781133.048", "doxygen/1.9.4#461b479e74052d4a5bb41dcf63b8cb5d%1708396102.071", "coreutils/9.4#11ee9fce4bf8fe11fc3c21171cdd8e3a%1740648976.192", "cmake/3.27.1#ff5aa246eafce398974ebaf4c0a6ae95%1712665136.894", "bison/3.7.6#4b33a752621345789d5a7bd0b7e3969d%1745586472.911", "binutils/2.38#eb567393d48d74b3ec336c3ec49621a7%1712665136.79", "automake/1.16.5#61211678c2d683d8a58f506ddcf63285%1711647072.926", "autoconf/2.71#e65e58d3eb5c70a6cc7ea1f1a22c2c41%1711347438.585", "android_sdk/34.0.0#bc16c66ad576d5626dbfedf8a188b6c5%1721372916.465", "android_ndk/r26b#0cb123bcb6af141356bac36a4a556e9f%1712665136.574", "7zip/24.09#753051c21fbb8c1eafe0747959080f1a%1734348912.1"], "python_requires": [], "config_requires": []}