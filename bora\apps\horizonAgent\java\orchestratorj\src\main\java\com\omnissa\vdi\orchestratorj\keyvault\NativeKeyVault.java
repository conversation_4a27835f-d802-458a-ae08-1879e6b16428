/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */
package com.omnissa.vdi.orchestratorj.keyvault;

import java.math.BigInteger;
import java.nio.ByteBuffer;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.UnrecoverableKeyException;
import java.security.cert.Certificate;
import java.security.cert.X509Certificate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.crypto.SecretKey;
import javax.security.auth.x500.X500Principal;

import com.omnissa.vdi.commonutils.Signature;
import com.omnissa.vdi.logger.Logger;
import com.omnissa.vdi.mfwj.MessageHandler;
import com.omnissa.vdi.mfwj.PropertyBag;
import com.omnissa.vdi.mfwj.binaryResp;
import com.omnissa.vdi.orchestratorj.keyvault.KeyVaultTransform.HashAlgorithmName;

/**
 * Java frontend for native KeyVault implementation, which uses the message
 * framework.
 */
public class NativeKeyVault implements KeyVault {

    private static final Logger log = Logger.getLogger(NativeKeyVault.class);

    private static final String ACCEPT_SELF_SIGNED_CERTS = "acceptSelfSignedCerts";

    private static final String INCLUDE_EXPIRED_CERTS = "includeExpiredCerts";

    private final static String ACTION_PERFORMED = "actionPerformed";

    private static final String ADVANCE_DAYS = "advanceDays";

    private final static String ALGORITHM = "algorithm";

    private final static String BINARY_IS_CERT = "binaryIsCert";

    private static final String BINDATA_SIGNED = "binDataSigned";

    private static final String BINDATA_TO_SIGN = "binDataToSign";

    private final static String CERT_ERROR_CODE = "endErrorCode";

    private final static String CERT_INFO_CODE = "endInfoCode";

    private final static String CERT_INVALID_REASONS = "endEntityReasons";

    private final static String CERT_SELF_SIGNED = "selfSigned";

    private final static String CERT_FILE_FORMAT = "certificateFileFormat";

    private final static String CHAIN_INVALID_REASONS = "chainReasons";

    private final static String CHAIN_ERROR_CODE = "chainErrorCode";

    private final static String CHAIN_INFO_CODE = "chainInfoCode";

    private final static String CHAIN_POLICY_CODE = "policyErrorCode";

    private final static String CONTEXT = "context";

    private final static String COUNT = "count";

    private final static String COUNT_ONLY = "countOnly";

    private final static String DELETED = "Deleted";

    private final static String DERIVE_FROM_KEY = "deriveFromKey";

    private static final String ENCIPHER_NAME = "encipherName";

    private static final String EX_VALIDATION = "extendedValidation";

    private static final String FRIENDLY_NAME = "friendlyName";

    private final static String GENERATION = "gen";

    private final static String HASH_ALGORITHM = "hashAlgorithm";

    private final static String HOST_NAME = "hostName";

    private final static String ID = "id";

    private final static String INTENDED_USE = "intendedUse";

    private final static String ISSUER_NAME = "issuerName";

    private final static String ITERATIONS = "iterations";

    private final static String KEY_LENGTH = "keyLength";

    private final static String LENGTH = "length";

    private final static String LIFETIME = "lifeTime";

    private final static String NAME = "name";

    private final static String NAMES = "names";

    private final static String NEW_FRIENDLY_NAME = "newFriendlyName";

    // Use for certificates
    private final static String OVERWRITE_EXISTING = "overwriteExisting";

    // Use for keys
    private final static String ALLOW_OVERWRITE = "allowOverwrite";

    private final static String OVERWRITTEN = "OverWritten";

    private static final String PASSWORD = "password";

    private static final String PERSIST = "persist";

    private final static String QUEUE_NAME = "[local]KeyVault";

    private final static String REKEY = "rekey";

    private final static String REMOVE_CERTIFICATE = "removeCert";

    private final static String RESULT = "result";

    private final static String SALT = "salt";

    private final static String SALT_LENGTH = "saltLength";

    private final static String SAN_INDEX = "sanIndex";

    private static final String SELECTION_MODE = "selectionMode";

    private static final String STORE_NAME = "storeName";

    private static final String SUB_ALT_NAMES = "subjectAltNames";

    private static final String TRANSFORM = "transform";

    private static final String TRUSTED = "trusted";

    private static final String WEAK_CRYPTO_REASON = "WeakCryptoReason";

    private static final String XF_ALGID = "XF_ALGID";

    private static final int MAX_KV_RETRY = 30;

    private static final int WIN_ERR_KEY_NOT_EXPORTABLE = 0x8009000B;

    private static final int WIN_ERR_CERT_SERVICE_UNAVAILABLE = 0x80090020;

    private static final Map<ChainSelectionMode, Integer> modeToInt = new HashMap<>();
    static {
        modeToInt.put(ChainSelectionMode.FirstValid, 0);
        modeToInt.put(ChainSelectionMode.LatestExpiry, 1);
        modeToInt.put(ChainSelectionMode.EarliestStart, 2);
    }

    private enum KeyVaultError {
        KVErrOk(0), KVErrFailed(30000), KVErrLocked(30001), KvErrRootKeyMissing(
                30002);

        int errcode;

        KeyVaultError(final int errcode) {
            this.errcode = errcode;
        }
    }

    /**
     * Call KeyVault. If result is MsgError and system is reported busy, or
     * result is MsgSystemBusy, then retry. We keep this up for MAX_KV_RETRY
     * tries and then bail out.
     *
     * If an error is returned, the response bag may be modified to maximise
     * available info.
     *
     * @param method
     *            name of the KeyVault method to call
     * @param params
     *            parameter bag for the call
     * @param response
     *            response data bag
     * @return true if we succeed on some attempt
     * @throws KeyVaultException
     *             if there is a problem accessing KeyVault
     */
    private static boolean kvCallWithRetry(String method, PropertyBag params,
            PropertyBag response) throws KeyVaultException {
        return kvCallWithRetry(method, params, response, null, null);
    }

    /**
     * Call KeyVault. If result is MsgError and system is reported busy, or
     * result is MsgSystemBusy, then retry. We keep this up for MAX_KV_RETRY
     * tries and then bail out.
     *
     * If an error is returned, the response bag may be modified to maximise
     * available info.
     *
     * @param method
     *            name of the KeyVault method to call
     * @param params
     *            parameter bag for the call
     * @param response
     *            response data bag
     * @param binaryIn
     *            optional binary data attached to params
     * @param binaryOut
     *            optional binary data attached to response
     * @return true if we succeed on some attempt
     * @throws KeyVaultException
     *             if there is a problem accessing KeyVault
     */
    private static boolean kvCallWithRetry(String method, PropertyBag params,
            PropertyBag response, ByteBuffer binaryIn, binaryResp binaryOut)
            throws KeyVaultException {
        int result = 0, retry;

        for (retry = 0; retry < MAX_KV_RETRY; ++retry) {
            if ((binaryIn == null) && (binaryOut == null)) {
                result = MessageHandler.SendMsg(QUEUE_NAME, method, params,
                        response);
            } else {
                result = MessageHandler.SendMsg(QUEUE_NAME, method, params,
                        response, binaryIn, binaryOut);
            }
            if (result == MessageHandler.MsgOk) {
                if (retry > 0) {
                    log.debug("KeyVault call '{}' succeeded on attempt {}",
                            method, ++retry);
                }
                return true;
            }

            // failed
            if ((result == MessageHandler.MsgSystemBusy)
                    || ((result == MessageHandler.MsgError) && (response
                            .getError() == KeyVaultError.KVErrLocked.errcode))) {
                /*
                 * KeyVault is busy, wait a sec and try again.
                 */
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException ignore) {
                    // don't care
                }
                response.clear();
                continue;
            }

            // some other problem
            if (result == MessageHandler.MsgError) {
                /*
                 * The call was executed but processing was unsuccessful, break
                 * out of the loop to return false.
                 */
                break;
            }

            /*
             * We've hit a problem with the KeyVault service or with mfw, throw
             * an appropriate exception. Note that MsgSystemBusy is handled
             * above.
             */
            switch (result) {
            case MessageHandler.MsgRoutingFailed:
            case MessageHandler.MsgNoQueueHandler:
            case MessageHandler.MsgShuttingDown:
            case MessageHandler.MsgQueuePaused:
                throw new KeyVaultUnavailableException(
                        response.formatError(result));
            default:
                throw new KeyVaultException(response.formatError(result));
            }
        }

        if (retry == MAX_KV_RETRY) {
            /*
             * We have been unable to execute the call even after lots of
             * retries. Bail out with a service unavailability exception, using
             * the result of the last attempt as context.
             */
            throw new KeyVaultUnavailableException(
                    response.formatError(result));
        }

        /*
         * The call was executed and returned a failure.
         */

        if (retry > 0) {
            log.debug("KeyVault call '{}' failed on attempt {}", method,
                    ++retry);
        }

        /*
         * Maximise error intelligence before returning (this is essentially
         * what formatError() does but here we are modifying the response bag
         * because we are not making the result code available to the caller).
         */
        String eText = "Error: ";
        int e = response.getError();
        String t = response.getErrorText();
        if (e == 0) {
            eText += result;
            response.setError(result, t != null ? t : eText);
        } else if (t == null) {
            eText += e;
            response.setError(e, eText);
        }
        return false;
    }

    @Override
    public int addContext(int length, String name) throws KeyVaultException {
        PropertyBag body = new PropertyBag();
        PropertyBag response = new PropertyBag();
        body.addInt(LENGTH, length);
        body.add(NAME, name);
        if (!kvCallWithRetry("addContext", body, response)) {
            throw new KeyVaultException(
                    "add operational context: " + response.getErrorText());
        }

        return response.getInt(GENERATION, 0);
    }

    @Override
    public int addKey(int length, String name) throws KeyVaultException {
        return addKey(length, name, null);
    }

    @Override
    public int addKey(int length, String name, String master)
            throws KeyVaultException {
        PropertyBag request = new PropertyBag();
        PropertyBag response = new PropertyBag();
        request.addInt(LENGTH, length);
        request.add(NAME, name);
        if (master != null) {
            request.add(ENCIPHER_NAME, master);
            request.addBool(PERSIST, true);
        }

        if (!kvCallWithRetry("addKey", request, response)) {
            throw new KeyVaultException("add key: " + response.getErrorText());
        }

        return response.getInt(GENERATION, 0);
    }

    @Override
    public int addKeyPair(int length, String name) throws KeyVaultException {
        return addKeyPair(length, name, null);
    }

    @Override
    public int addKeyPair(int length, String name, String master)
            throws KeyVaultException {
        PropertyBag request = new PropertyBag();
        PropertyBag response = new PropertyBag();
        request.addInt(LENGTH, length);
        request.add(NAME, name);
        if (master != null) {
            request.add(ENCIPHER_NAME, master);
            request.addBool(PERSIST, true);
        }

        if (!kvCallWithRetry("addKeyPair", request, response)) {
            throw new KeyVaultException(
                    "add keypair: " + response.getErrorText());
        }

        return response.getInt(GENERATION, 0);
    }

    @Override
    public int addMaster(String name) throws KeyVaultException {
        PropertyBag body = new PropertyBag();
        PropertyBag response = new PropertyBag();
        body.add(NAME, name);
        if (!kvCallWithRetry("addMaster", body, response)) {
            throw new KeyVaultException(
                    "add master key: " + response.getErrorText());
        }

        return response.getInt(GENERATION, 0);
    }

    @Override
    public Certificate addSelfSignedCertificate(String name,
            Signature.Algorithm signatureAlgorithm, int keyLength, int lifetime,
            String friendlyName, String storeName, boolean overwriteExisting)
            throws KeyVaultException {
        PropertyBag request = new PropertyBag();
        PropertyBag response = new PropertyBag();
        binaryResp binaryResponse = new binaryResp();

        if (log.isTraceEnabled()) {
            log.trace("Creating self-signed certificate: name=" + name
                    + ", friendlyName=" + friendlyName + ", algorithm="
                    + signatureAlgorithm + ", storeName=" + storeName
                    + ", overwriteExisting=" + overwriteExisting);
        }

        request.add(NAME, name);
        request.add(ALGORITHM, signatureAlgorithm.oid);
        request.addInt(KEY_LENGTH, keyLength);
        request.addInt(LIFETIME, lifetime);
        request.add(FRIENDLY_NAME, friendlyName);
        request.addBool(OVERWRITE_EXISTING, overwriteExisting);

        if (null != storeName) {
            request.add(STORE_NAME, storeName);
        }

        try {
            boolean ok = kvCallWithRetry("AddSelfSignedCertificate", request,
                    response, (ByteBuffer) null, binaryResponse);
            if (!ok) {
                switch (response.getInt("ErrorCode", 0)) {
                case WIN_ERR_CERT_SERVICE_UNAVAILABLE:
                    throw new KeyVaultUnavailableException(
                            "Add self-signed certificate: "
                                    + response.getErrorText());
                default:
                    throw new KeyVaultException("Add self-signed certificate: "
                            + response.getErrorText());
                }
            }

            if (binaryResponse.isEmpty()) {
                throw new KeyVaultException(
                        "Add self-signed certificate: Unexpectedly returned without a cert");
            }

            return KeyVaultBinaryUtils
                    .bytesToCertificate(binaryResponse.getByteBuffer());
        } finally {
            binaryResponse.free();
        }
    }

    @Override
    public int clear() throws KeyVaultException {
        PropertyBag body = new PropertyBag();
        PropertyBag response = new PropertyBag();
        if (!kvCallWithRetry("clear", body, response)) {
            throw new KeyVaultException("clear: " + response.getErrorText());
        }
        return response.getInt(COUNT, 0);
    }

    @Override
    public byte[] decipher(KeyName cipherKey, byte[] data)
            throws KeyVaultException {
        return decipher(cipherKey, data, null);
    }

    @Override
    public byte[] decipher(KeyName cipherKey, byte[] data,
            KeyVaultTransform transform) throws KeyVaultException {
        PropertyBag request = new PropertyBag();
        PropertyBag response = new PropertyBag();
        ByteBuffer binaryIn = KeyVaultBinaryUtils.getDirectByteBuffer(data);
        binaryResp binaryOut = new binaryResp();
        cipherKey.addToBag(request, NAME);
        if (transform != null) {
            request.addBag(TRANSFORM, transform.toPropertyBag());
        }

        boolean ok = kvCallWithRetry("decipher", request, response, binaryIn,
                binaryOut);
        if (!ok) {
            binaryOut.free();
            throw new KeyVaultException("decipher: " + response.getErrorText());
        }

        return binaryOut.consumeByteArray();
    }

    @Override
    public byte[] decipherWithDerivedKey(KeyName master, HashAlgorithm hashalg,
            int iterations, byte[] salt, byte[] data) throws KeyVaultException {
        return decipherWithDerivedKey(master, hashalg, iterations, salt, data,
                null);
    }

    @Override
    public byte[] decipherWithDerivedKey(KeyName master, HashAlgorithm hashalg,
            int iterations, byte[] salt, byte[] data,
            KeyVaultTransform transform) throws KeyVaultException {
        PropertyBag request = new PropertyBag();
        PropertyBag response = new PropertyBag();
        ByteBuffer binaryIn = KeyVaultBinaryUtils.getDirectByteBuffer(data);
        binaryResp binaryOut = new binaryResp();
        master.addToBag(request, NAME);
        request.add(HASH_ALGORITHM, hashalg.name());
        request.addInt(ITERATIONS, iterations);
        request.addBinary(SALT, salt);
        if (transform != null) {
            request.addBag(TRANSFORM, transform.toPropertyBag());
        }

        boolean ok = kvCallWithRetry("decipherWithDerivedKey", request,
                response, binaryIn, binaryOut);
        if (!ok) {
            binaryOut.free();
            throw new KeyVaultException(
                    "decipherWithDerivedKey: " + response.getErrorText());
        }

        return binaryOut.consumeByteArray();
    }

    @Override
    public int deriveKey(String name, KeyName master, KeyName context)
            throws KeyVaultException {
        PropertyBag request = new PropertyBag();
        PropertyBag response = new PropertyBag();
        request.add(NAME, name);
        master.addToBag(request, DERIVE_FROM_KEY);
        context.addToBag(request, CONTEXT);

        if (!kvCallWithRetry("deriveKey", request, response)) {
            throw new KeyVaultException(
                    "derive operational key: " + response.getErrorText());
        }

        return response.getInt(GENERATION, 0);
    }

    @Override
    public byte[] encipher(String cipherKey, byte[] data)
            throws KeyVaultException {
        return encipher(cipherKey, data, null);
    }

    @Override
    public byte[] encipher(String cipherKey, byte[] data,
            KeyVaultTransform transform) throws KeyVaultException {
        PropertyBag request = new PropertyBag();
        PropertyBag response = new PropertyBag();
        ByteBuffer binaryIn = KeyVaultBinaryUtils.getDirectByteBuffer(data);
        binaryResp binaryOut = new binaryResp();
        request.add(NAME, cipherKey);
        if (transform != null) {
            request.addBag(TRANSFORM, transform.toPropertyBag());
        }

        boolean ok = kvCallWithRetry("encipher", request, response, binaryIn,
                binaryOut);
        if (!ok) {
            binaryOut.free();
            throw new KeyVaultException("encipher: " + response.getErrorText());
        }

        if ((transform != null) && response.isBag(TRANSFORM)) {
            // response includes an updated transform bag
            transform.updateFromPropertyBag(response.getBag(TRANSFORM));
        }

        return binaryOut.consumeByteArray();
    }

    @Override
    public byte[] encipherWithDerivedKey(KeyName master, HashAlgorithm hashalg,
            int iterations, byte[] salt, byte[] data) throws KeyVaultException {
        return encipherWithDerivedKey(master, hashalg, iterations, salt, data,
                null);
    }

    @Override
    public byte[] encipherWithDerivedKey(KeyName master, HashAlgorithm hashalg,
            int iterations, byte[] salt, byte[] data,
            KeyVaultTransform transform) throws KeyVaultException {
        PropertyBag request = new PropertyBag();
        PropertyBag response = new PropertyBag();
        ByteBuffer binaryIn = KeyVaultBinaryUtils.getDirectByteBuffer(data);
        binaryResp binaryOut = new binaryResp();
        master.addToBag(request, NAME);
        request.addInt(SALT_LENGTH, salt.length);
        request.add(HASH_ALGORITHM, hashalg.name());
        request.addInt(ITERATIONS, iterations);
        if (transform != null) {
            request.addBag(TRANSFORM, transform.toPropertyBag());
        }

        try {
            boolean ok = kvCallWithRetry("encipherWithDerivedKey", request,
                    response, binaryIn, binaryOut);
            if (!ok) {
                throw new KeyVaultException(
                        "encipherWithDerivedKey: " + response.getErrorText());
            }
            byte[] returnedSalt = response.getBinaryByteArray(SALT);
            System.arraycopy(returnedSalt, 0, salt, 0, salt.length);
            return binaryOut.getByteArray();
        } finally {
            binaryOut.free();
        }
    }

    @Override
    public boolean exists(KeyName keyname) throws KeyVaultException {
        PropertyBag request = new PropertyBag();
        PropertyBag response = new PropertyBag();
        keyname.addToBag(request, NAME);

        return kvCallWithRetry("exist", request, response);
    }

    @Override
    public byte[] exportCertificates(String friendlyName, String storeName,
            KeyName master, HashAlgorithm hashalg, int iterations)
            throws KeyVaultException {
        return exportCertificates(friendlyName, storeName, null, null, master,
                hashalg, iterations);
    }

    @Override
    public byte[] exportCertificates(String friendlyName, String storeName,
            String hostName, String[] intendedUse, KeyName master,
            HashAlgorithm hashalg, int iterations) throws KeyVaultException {
        PropertyBag request = new PropertyBag();
        PropertyBag response = new PropertyBag();
        binaryResp binaryOut = new binaryResp();
        request.add(FRIENDLY_NAME, friendlyName);

        if (null != storeName) {
            request.add(STORE_NAME, storeName);
        }

        if (null != hostName) {
            request.add(HOST_NAME, hostName);
        }

        if (null != intendedUse) {
            for (String use : intendedUse) {
                request.add(INTENDED_USE, use);
            }
            request.addBool(EX_VALIDATION, true);
        }

        master.addToBag(request, NAME);
        request.add(HASH_ALGORITHM, hashalg.name());
        request.addInt(ITERATIONS, iterations);

        boolean ok = kvCallWithRetry("exportCertificates", request, response,
                (ByteBuffer) null, binaryOut);
        if (!ok) {
            binaryOut.free();
            throw new KeyVaultException(
                    "export certificates: " + response.getErrorText());
        }

        return binaryOut.consumeByteArray();
    }

    @Override
    public int importCertificates(byte[] certificateBlob)
            throws KeyVaultException {
        return importCertificates(certificateBlob, false);
    }

    @Override
    public int importCertificates(byte[] certificateBlob,
            boolean allowOverwrite) throws KeyVaultException {
        PropertyBag request = new PropertyBag();
        PropertyBag response = new PropertyBag();
        ByteBuffer binaryIn = KeyVaultBinaryUtils
                .getDirectByteBuffer(certificateBlob);

        if (allowOverwrite) {
            request.addBool(OVERWRITE_EXISTING, true);
        }

        boolean ok = kvCallWithRetry("importCertificates", request, response,
                binaryIn, null);
        if (!ok) {
            throw new KeyVaultException(
                    "import certificates: " + response.getErrorText());
        }
        return response.getInt(COUNT, 0);
    }

    @Override
    public int importEncodedCertificates(byte[] certificateFileImage,
            String friendlyName, String storeName, byte[] password,
            CertificateFileFormat certificateFileFormat, String hostname,
            String[] intendedUse) throws KeyVaultException {
        PropertyBag request = new PropertyBag();
        PropertyBag response = new PropertyBag();
        ByteBuffer binaryIn = KeyVaultBinaryUtils
                .getDirectByteBuffer(certificateFileImage);

        if (null == friendlyName) {
            throw new KeyVaultException("Friendly Name is NULL");
        }
        if (null == certificateFileFormat) {
            throw new KeyVaultException("Incorrect certificateFileFormat");
        }
        log.trace(
                "Importing CA-signed certificate: friendlyName= {}, storeName= {}, certificateFormat= {}, intendedUse:{}",
                friendlyName, storeName, certificateFileFormat.name(),
                intendedUse);
        if (null != storeName) {
            request.add(STORE_NAME, storeName);
        }
        if ((certificateFileFormat == CertificateFileFormat.pfx)
                && (null != password)) {
            request.addBinary(PASSWORD, password);
        }
        if (null != hostname) {
            request.add(HOST_NAME, hostname);
        }
        if (null != intendedUse) {
            for (String use : intendedUse) {
                request.add(INTENDED_USE, use);
            }
        }
        request.add(FRIENDLY_NAME, friendlyName);
        request.add(CERT_FILE_FORMAT, certificateFileFormat.name());

        boolean ok = kvCallWithRetry("importEncodedCertificates", request,
                response, binaryIn, null);
        if (!ok) {
            throw new KeyVaultException(
                    "importEncodedCertificates: " + response.getErrorText());
        }
        if (!("OK".equalsIgnoreCase(response.get(RESULT, null)))) {
            throw new KeyVaultCertificateException(
                    generateCertificateValidationResults(response, null,
                            hostname));
        }
        return response.getInt(COUNT, 0);
    }

    @Override
    public void finish() throws KeyVaultException {
        PropertyBag request = new PropertyBag();
        PropertyBag response = new PropertyBag();

        if (!kvCallWithRetry("finish", request, response)) {
            throw new KeyVaultException("finish: " + response.getErrorText());
        }
    }

    @Override
    public byte[] generatePKCS10(String subjectDN, int keySize, int lifeTime,
            Signature.Algorithm signatureAlgorithm,
            List<String> subjectAltNames, String storeName)
            throws KeyVaultException {
        PropertyBag request = new PropertyBag();
        PropertyBag response = new PropertyBag();
        binaryResp binaryOut = new binaryResp();

        request.add(NAME, subjectDN);
        if (keySize != 0) {
            request.addInt(KEY_LENGTH, keySize);
        }
        if (null != storeName) {
            request.add(STORE_NAME, storeName);
        }
        if (lifeTime != 0) {
            request.addInt(LIFETIME, lifeTime);
        }
        request.add(ALGORITHM, signatureAlgorithm.oid);
        if (subjectAltNames != null) {
            PropertyBag san = new PropertyBag();
            for (String altName : subjectAltNames) {
                san.add("", altName);
            }
            if (san.size() > 0) {
                request.addBag(SUB_ALT_NAMES, san);
            }
        }
        boolean ok = kvCallWithRetry("generatePKCS10", request, response, null,
                binaryOut);
        if (!ok) {
            binaryOut.free();
            throw new KeyVaultException(
                    "generatePKCS10: " + response.getErrorText());
        }
        return binaryOut.consumeByteArray();
    }

    @Override
    public Certificate[] getCertificateChain(String friendlyName)
            throws KeyVaultException {
        return getCertificateChain(friendlyName, null);
    }

    @Override
    public Certificate[] getCertificateChain(String friendlyName,
            String storeName) throws KeyVaultException {
        return getCertificateChain(friendlyName, storeName, null);
    }

    @Override
    public Certificate[] getCertificateChain(String friendlyName,
            String storeName, boolean acceptSelfSignedCerts)
            throws KeyVaultException {
        return getCertificateChain(friendlyName, storeName, null, null, null,
                acceptSelfSignedCerts);
    }

    @Override
    public Certificate[] getCertificateChain(String friendlyName,
            String storeName, ChainSelectionMode mode)
            throws KeyVaultException {
        return getCertificateChain(friendlyName, storeName, null, null, mode);
    }

    @Override
    public Certificate[] getCertificateChain(String friendlyName,
            String storeName, String hostName, String[] intendedUse,
            ChainSelectionMode mode) throws KeyVaultException {
        return getCertificateChain(friendlyName, storeName, hostName,
                intendedUse, mode, false);
    }

    @Override
    public Certificate[] getCertificateChain(String friendlyName,
            String storeName, String hostName, String[] intendedUse,
            ChainSelectionMode mode, boolean acceptSelfSignedCerts)
            throws KeyVaultException {
        PropertyBag request = new PropertyBag();
        PropertyBag response = new PropertyBag();
        binaryResp binaryResponse = new binaryResp();
        request.add(FRIENDLY_NAME, friendlyName);

        if (null != storeName) {
            request.add(STORE_NAME, storeName);
        }

        if (null != hostName) {
            request.add(HOST_NAME, hostName);
        }

        if (null != intendedUse) {
            for (String use : intendedUse) {
                request.add(INTENDED_USE, use);
            }
            request.addBool(EX_VALIDATION, true);
        }

        if (null != mode) {
            request.addInt(SELECTION_MODE, modeToInt.get(mode));
        }

        if (acceptSelfSignedCerts) {
            request.addBool(ACCEPT_SELF_SIGNED_CERTS, true);
        }

        X509Certificate[] chain;
        try {
            boolean ok = kvCallWithRetry("getCertificateChain", request,
                    response, (ByteBuffer) null, binaryResponse);
            if (!ok) {
                throw new KeyVaultException(
                        "get certificate chain: " + response.getErrorText());
            }

            chain = KeyVaultBinaryUtils.removeRootFromChain(KeyVaultBinaryUtils
                    .bytesToCertificateChain(binaryResponse.getByteBuffer()));
        } finally {
            binaryResponse.free();
        }
        if (chain == null) {
            /*
             * This shouldn't happen, but the "contract" for this method is that
             * it throws KeyVaultException if it does.
             */
            throw new KeyVaultException("get certificate chain: empty");
        }
        return chain;
    }

    @Override
    public CertificateStoreCounts getCertificateCounts(String friendlyName)
            throws KeyVaultException {
        return getCertificateCounts(friendlyName, null);
    }

    @Override
    public CertificateStoreCounts getCertificateCounts(String friendlyName,
            String storeName) throws KeyVaultException {
        return getCertificateCounts(friendlyName, storeName, null, null);
    }

    @Override
    public CertificateStoreCounts getCertificateCounts(String friendlyName,
            String storeName, String hostName, String[] intendedUse)
            throws KeyVaultException {

        PropertyBag request = new PropertyBag();
        PropertyBag response = new PropertyBag();
        request.add(FRIENDLY_NAME, friendlyName);

        if (null != storeName) {
            request.add(STORE_NAME, storeName);
        }

        if (null != hostName) {
            request.add(HOST_NAME, hostName);
        }

        if (null != intendedUse) {
            for (String use : intendedUse) {
                request.add(INTENDED_USE, use);
            }
            request.addBool(EX_VALIDATION, true);
        }

        request.addBool(COUNT_ONLY, true);

        if (!kvCallWithRetry("getCertificateChain", request, response)) {
            throw new KeyVaultException(
                    "get certificate counts: " + response.getErrorText());
        }

        return new CertificateStoreCounts(response.getInt("certsChecked", 0),
                response.getInt("certsValid", 0),
                response.getInt("certsTimeInvalid", 0));
    }

    @Override
    public String getCertificateHost(X509Certificate certificate)
            throws KeyVaultException {
        return getCertificateHost(certificate, null);
    }

    @Override
    public String getCertificateHost(X509Certificate certificate,
            String storeName) throws KeyVaultException {
        return getCertificateHost(certificate, storeName, 0);
    }

    @Override
    public String getCertificateHost(X509Certificate certificate,
            String storeName, int sanIndex) throws KeyVaultException {
        PropertyBag request = new PropertyBag();
        PropertyBag response = new PropertyBag();

        ByteBuffer blob = KeyVaultBinaryUtils
                .renderAsCertificateBlob(certificate);
        request.addBool(BINARY_IS_CERT, true);

        if (null != storeName) {
            request.add(STORE_NAME, storeName);
        }

        if (0 != sanIndex) {
            request.addInt(SAN_INDEX, sanIndex);
        }

        if (!kvCallWithRetry("getCertificateHost", request, response, blob,
                null)) {
            throw new KeyVaultException("Unable to get certificate hostname: "
                    + response.getErrorText());
        }

        return response.get("hostname", null);
    }

    /**
     * Sends the message over the framework, converting the result to a
     * PrivateKey.
     *
     * @param request
     *            PropertyBag in
     * @param binaryIn
     *            Binary data in
     * @return The private key
     * @throws UnrecoverableKeyException
     *             If the private key is not exportable
     * @throws KeyVaultException
     *             If there's an error talking to keyvault
     * @throws NoSuchAlgorithmException
     *             If the data cannot be converted to a private key
     */
    private PrivateKey getCertificateKey(PropertyBag request,
            ByteBuffer binaryIn) throws UnrecoverableKeyException,
            KeyVaultException, NoSuchAlgorithmException {
        // raw output is only used internally
        PropertyBag response = new PropertyBag();
        binaryResp binaryOut = new binaryResp();

        try {
            boolean ok = kvCallWithRetry("getCertificateKey", request, response,
                    binaryIn, binaryOut);
            if (!ok) {
                switch (response.getInt("ErrorCode", 0)) {
                case WIN_ERR_KEY_NOT_EXPORTABLE:
                    throw new UnrecoverableKeyException(
                            "Private key is not exportable");
                default:
                    throw new KeyVaultException(
                            "Unable to get certificate private key: "
                                    + response.getErrorText());
                }
            }

            return KeyVaultBinaryUtils
                    .bytesToPrivateKey(binaryOut.getByteBuffer());
        } finally {
            binaryOut.free();
        }
    }

    /**
     * This method variant is DEPRECATED in favour of the provided-certificate
     * variant. The problem with this variant is that Java and Windows may not
     * produce the same issuer string, and hence the private key may not be
     * found.
     */
    @Deprecated
    @Override
    public PrivateKey getCertificateKey(X500Principal issuer,
            BigInteger serialNumber, String storeName) throws KeyVaultException,
            NoSuchAlgorithmException, UnrecoverableKeyException {
        PropertyBag request = new PropertyBag();

        if (null != issuer) {
            request.add(ISSUER_NAME, issuer.getName("RFC1779"));
        }

        ByteBuffer serial = null;
        if (null != serialNumber) {
            serial = KeyVaultBinaryUtils.renderAsSerialNumber(serialNumber);
        }

        if (null != storeName) {
            request.add(STORE_NAME, storeName);
        }

        if (log.isTraceEnabled()) {
            StringBuilder sb = new StringBuilder("Requesting certificate key");
            if (null != issuer) {
                sb.append(", issuer: ").append(request.get(ISSUER_NAME, ""));
            }
            if (null != serial) {
                BigInteger bi = KeyVaultBinaryUtils.createBigInteger(serial,
                        serial.remaining());
                serial.rewind();
                sb.append(", serial: ").append(bi);
                // take opportunity to do a sanity check (should never fail)
                if (!bi.equals(serialNumber)) {
                    sb.append(" (MISMATCH)");
                }
            }
            if (null != storeName) {
                sb.append(", store: ").append(request.get(STORE_NAME, ""));
            }
            log.trace(sb.toString());
        }

        return getCertificateKey(request, serial);
    }

    @Override
    public PrivateKey getCertificateKey(X509Certificate certificate)
            throws KeyVaultException, NoSuchAlgorithmException,
            UnrecoverableKeyException {
        return getCertificateKey(certificate, null);
    }

    @Override
    public PrivateKey getCertificateKey(X509Certificate certificate,
            String storeName) throws KeyVaultException,
            NoSuchAlgorithmException, UnrecoverableKeyException {
        return getCertificateKey(certificate, storeName, false);
    }

    @Override
    public PrivateKey getCertificateKey(X509Certificate certificate,
            String storeName, boolean acceptSelfSignedCerts)
            throws KeyVaultException, NoSuchAlgorithmException,
            UnrecoverableKeyException {
        PropertyBag request = new PropertyBag();

        ByteBuffer blob = KeyVaultBinaryUtils
                .renderAsCertificateBlob(certificate);
        request.addBool(BINARY_IS_CERT, true);

        if (null != storeName) {
            request.add(STORE_NAME, storeName);
        }

        if (acceptSelfSignedCerts) {
            request.addBool(ACCEPT_SELF_SIGNED_CERTS, true);
        }
        return getCertificateKey(request, blob);
    }

    @Override
    public byte[] getData(KeyName name) throws KeyVaultException {
        PropertyBag request = new PropertyBag();
        PropertyBag response = new PropertyBag();
        name.addToBag(request, NAME);
        request.addBool("decipher", true);
        binaryResp binaryOut = new binaryResp();

        boolean ok = kvCallWithRetry("getData", request, response,
                (ByteBuffer) null, binaryOut);
        if (!ok) {
            binaryOut.free();
            throw new KeyVaultException(
                    "get application data: " + response.getErrorText());
        }

        return binaryOut.consumeByteArray();
    }

    @Override
    public byte[] getKey(String name, KeyName encipherName,
            KeyVaultTransform transform) throws KeyVaultException {
        PropertyBag request = new PropertyBag();
        PropertyBag response = new PropertyBag();
        binaryResp binaryOut = new binaryResp();

        request.add(NAME, name);
        encipherName.addToBag(request, ENCIPHER_NAME);

        if (transform != null) {
            if (transform.getIV() == null) {
                transform.setIV();
            }
            request.addBag(TRANSFORM, transform.toPropertyBag());
        }

        boolean ok = kvCallWithRetry("getKey", request, response,
                (ByteBuffer) null, binaryOut);
        if (!ok) {
            binaryOut.free();
            throw new KeyVaultException("getKey: " + response.getErrorText());
        }

        return binaryOut.consumeByteArray();
    }

    @Override
    public void getPublic(PublicKeyWithProof pkp)
            throws KeyVaultException, NoSuchAlgorithmException {
        PropertyBag request = new PropertyBag();
        PropertyBag response = new PropertyBag();
        binaryResp binaryOut = new binaryResp();
        pkp.addToBag(request);
        try {
            if (!kvCallWithRetry("getPublic", request, response,
                    (ByteBuffer) null, binaryOut)) {
                throw new KeyVaultException(
                        "get public: " + response.getErrorText());
            }
            pkp.setKey(binaryOut);
        } finally {
            binaryOut.free();
        }
        pkp.getName().setGen(response.getInt(GENERATION, 0));
        if (response.contains(BINDATA_SIGNED)) {
            pkp.setProof(response.getBinaryByteArray(BINDATA_SIGNED));
        }
    }

    @Override
    public PublicKey getPublicKey(KeyName name)
            throws KeyVaultException, NoSuchAlgorithmException {
        PropertyBag request = new PropertyBag();
        PropertyBag response = new PropertyBag();
        binaryResp binaryOut = new binaryResp();
        name.addToBag(request, NAME);
        try {
            if (!kvCallWithRetry("getPublic", request, response,
                    (ByteBuffer) null, binaryOut)) {
                throw new KeyVaultException(
                        "get public key: " + response.getErrorText());
            }
            name.setGen(response.getInt(GENERATION, 0));
            return KeyVaultBinaryUtils
                    .bytesToPublicKey(binaryOut.getByteBuffer());
        } finally {
            binaryOut.free();
        }
    }

    @Override
    public long getCRC32(byte[] data) throws KeyVaultException {
        PropertyBag request = new PropertyBag();
        PropertyBag response = new PropertyBag();
        ByteBuffer binaryIn = KeyVaultBinaryUtils.getDirectByteBuffer(data);

        boolean ok = kvCallWithRetry("getCRC32", request, response, binaryIn,
                null);
        if (!ok) {
            throw new KeyVaultException("getCRC32: " + response.getErrorText());
        }
        return response.getLong("crc", 0L);
    }

    @Override
    public byte[] getRandom(int numBytes) throws KeyVaultException {
        return getRandom(numBytes, null);
    }

    @Override
    public byte[] getRandom(int numBytes, String algorithm)
            throws KeyVaultException {
        return getRandom(numBytes, null, algorithm);
    }

    @Override
    public void getRandom(byte[] seed) throws KeyVaultException {
        getRandom(seed, null);
    }

    @Override
    public void getRandom(byte[] seed, String algorithm)
            throws KeyVaultException {
        getRandom(0, seed, algorithm);
    }

    /**
     * Get a sequence of random bytes of a chosen length, or the same length as
     * an optional sequence of seed bytes. If a seed is provided, it is
     * overwritten with the generated random bytes.
     *
     * @param numBytes
     *            the number of random bytes to return (ignored if seed is
     *            non-null)
     * @param seed
     *            an auxiliary seed which is set before generating random bytes
     *            (may be null if numBytes &gt; 0)
     * @param algorithm
     *            the random number generation algorithm to use (if null, the
     *            system default is used)
     * @return the generated sequence of random bytes
     * @throws KeyVaultException
     *             If there's an error talking to keyvault
     */
    private byte[] getRandom(int numBytes, byte[] seed, String algorithm)
            throws KeyVaultException {
        PropertyBag request = new PropertyBag();
        PropertyBag response = new PropertyBag();
        if ((seed == null) && (numBytes > 0)) {
            request.addInt(LENGTH, numBytes);
        }
        if (algorithm != null) {
            request.add(ALGORITHM, algorithm);
        }
        ByteBuffer binaryIn = seed == null ? null
                : KeyVaultBinaryUtils.getDirectByteBuffer(seed);
        binaryResp binaryOut = new binaryResp();

        boolean ok = kvCallWithRetry("getRandom", request, response, binaryIn,
                binaryOut);
        if (!ok) {
            binaryOut.free();
            throw new KeyVaultException(
                    "get random: " + response.getErrorText());
        }

        if (binaryOut.isEmpty()) {
            binaryOut.free();
            throw new KeyVaultException("get random: no response");
        }

        byte[] rand = binaryOut.consumeByteArray();
        if (seed != null) {
            if (rand.length < seed.length) {
                throw new KeyVaultException("get random: short response");
            }
            for (int i = 0; i < seed.length; ++i) {
                seed[i] = rand[i];
            }
        }

        return rand;
    }

    @Override
    public void setRandomSeed(byte[] seed) throws KeyVaultException {
        setRandomSeed(seed, null);
    }

    @Override
    public void setRandomSeed(byte[] seed, String algorithm)
            throws KeyVaultException {
        PropertyBag request = new PropertyBag();
        PropertyBag response = new PropertyBag();
        if (algorithm != null) {
            request.add(ALGORITHM, algorithm);
        }
        ByteBuffer binaryIn = KeyVaultBinaryUtils.getDirectByteBuffer(seed);

        boolean ok = kvCallWithRetry("setRandomSeed", request, response,
                binaryIn, null);
        if (!ok) {
            throw new KeyVaultException(
                    "set random seed: " + response.getErrorText());
        }
    }

    @Override
    public boolean init(boolean rekey) throws KeyVaultException {
        PropertyBag body = new PropertyBag();
        PropertyBag response = new PropertyBag();
        body.addBool(REKEY, rekey);

        return kvCallWithRetry("init", body, response);
    }

    @Override
    public int latest(KeyName specifier) throws KeyVaultException {
        PropertyBag request = new PropertyBag();
        PropertyBag response = new PropertyBag();
        specifier.addToBag(request, NAME);

        if (!kvCallWithRetry("latest", request, response)) {
            throw new KeyVaultException("latest: " + response.getErrorText());
        }
        return response.getInt(GENERATION, 0);
    }

    @Override
    public String[] list(KeyName specifier) throws KeyVaultException {
        PropertyBag request = new PropertyBag();
        PropertyBag response = new PropertyBag();
        specifier.addToBag(request, NAME);

        if (!kvCallWithRetry("list", request, response)) {
            throw new KeyVaultException("list: " + response.getErrorText());
        }
        int count = response.getInt(COUNT, 0);
        String names = response.get(NAMES, "");
        String[] list = names.split(", ");
        if (list.length != count) {
            log.debug("list mismatch due to splitter usage");
        }
        return list;
    }

    @Override
    public String id(KeyName name, String salt) throws KeyVaultException {
        PropertyBag request = new PropertyBag();
        PropertyBag response = new PropertyBag();
        name.addToBag(request, NAME);
        request.add(SALT, salt);

        if (!kvCallWithRetry("id", request, response)) {
            throw new KeyVaultException("id: " + response.getErrorText());
        }

        return response.get(ID, "");
    }

    @Override
    public Map<String, String> info(KeyName specifier)
            throws KeyVaultException {
        PropertyBag request = new PropertyBag();
        PropertyBag response = new PropertyBag();
        specifier.addToBag(request, NAME);

        if (!kvCallWithRetry("info", request, response)) {
            throw new KeyVaultException("info: " + response.getErrorText());
        }

        return response.toStringMap(true);
    }

    @Override
    public int putData(byte[] data, String name, String opKey)
            throws KeyVaultException {
        return putData(data, name, opKey, null);
    }

    @Override
    public int putData(byte[] data, String name, String opKey,
            KeyVaultTransform transform) throws KeyVaultException {
        PropertyBag request = new PropertyBag();
        PropertyBag response = new PropertyBag();
        ByteBuffer binaryIn = KeyVaultBinaryUtils.getDirectByteBuffer(data);
        request.add(NAME, name);
        request.add(ENCIPHER_NAME, opKey);
        if (transform != null) {
            if (transform.getIV() == null) {
                transform.setIV();
            }
            if (transform.getSalt() == null) {
                transform.setSalt();
            }
            request.addBag(TRANSFORM, transform.toPropertyBag());
        }

        boolean ok = kvCallWithRetry("putData", request, response, binaryIn,
                null);
        if (!ok) {
            throw new KeyVaultException(
                    "put application data: " + response.getErrorText());
        }

        return response.getInt(GENERATION, 0);
    }

    @Override
    public boolean putKey(byte[] keyBlob, boolean allowOverwrite)
            throws KeyVaultException {
        PropertyBag request = new PropertyBag();
        PropertyBag response = new PropertyBag();
        ByteBuffer binaryIn = KeyVaultBinaryUtils.getDirectByteBuffer(keyBlob);

        if (allowOverwrite) {
            request.addBool(ALLOW_OVERWRITE, true);
        }

        boolean ok = kvCallWithRetry("putKey", request, response, binaryIn,
                null);
        if (!ok) {
            throw new KeyVaultException("putKey: " + response.getErrorText());
        }
        return ok;
    }

    @Override
    public int removeData(String name) throws KeyVaultException {
        PropertyBag request = new PropertyBag();
        PropertyBag response = new PropertyBag();
        request.add(NAME, name);

        if (!kvCallWithRetry("remove", request, response)) {
            throw new KeyVaultException(
                    "removeData: " + response.getErrorText());
        }

        return response.getInt(COUNT, 0);
    }

    @Override
    public int removeKey(String name) throws KeyVaultException {
        PropertyBag request = new PropertyBag();
        PropertyBag response = new PropertyBag();
        request.add(NAME, name);
        request.addBool("checkDependents", true);

        if (!kvCallWithRetry("remove", request, response)) {
            throw new KeyVaultException(
                    "remove key: " + response.getErrorText());
        }

        return response.getInt(COUNT, 0);
    }

    @Override
    public byte[] signHash(String name, byte[] data,
            KeyVaultTransform transform) throws KeyVaultException {
        PropertyBag request = new PropertyBag();
        PropertyBag response = new PropertyBag();
        request.add(NAME, name);
        request.addBinary(BINDATA_TO_SIGN, data);
        if (transform != null) {
            request.addBag(TRANSFORM, transform.toPropertyBag());
        }

        boolean ok = kvCallWithRetry("signHash", request, response);

        if (!ok) {
            throw new KeyVaultException("signHash: " + response.getErrorText());
        }

        return response.getBinaryByteArray(BINDATA_SIGNED);
    }

    @Override
    public byte[] generateSignature(String name, byte[] data,
            KeyVaultTransform transform) throws KeyVaultException {
        PropertyBag request = new PropertyBag();
        PropertyBag response = new PropertyBag();
        request.add(NAME, name);
        request.addBinary(BINDATA_TO_SIGN, data);
        if (transform != null) {
            request.addBag(TRANSFORM, transform.toPropertyBag());
        }

        boolean ok = kvCallWithRetry("generateSignature", request, response);

        if (!ok) {
            throw new KeyVaultException("generateSignature: " + response.getErrorText());
        }

        return response.getBinaryByteArray(BINDATA_SIGNED);
    }

    @Override
    public void verifySignature(KeyName name, byte[] data, byte[] signature,
            KeyVaultTransform transform) throws KeyVaultException {
        PropertyBag request = new PropertyBag();
        PropertyBag response = new PropertyBag();
        name.addToBag(request, NAME);
        request.addBinary(BINDATA_TO_SIGN, data);
        request.addBinary(BINDATA_SIGNED, signature);
        if (transform != null) {
            request.addBag(TRANSFORM, transform.toPropertyBag());
        }

        if (!kvCallWithRetry("verifySignature", request, response)) {
            throw new KeyVaultException(
                    "verifySignature: " + response.getErrorText());
        }
    }

    /*
     * ---------------------- Certificate Management ----------------------
     */

    @Override
    public Certificate[] getEndEntityCertificates(String friendlyName)
            throws KeyVaultException {
        return getEndEntityCertificates(friendlyName, null);
    }

    @Override
    public Certificate[] getEndEntityCertificates(String friendlyName,
            String storeName) throws KeyVaultException {
        return getEndEntityCertificates(friendlyName, storeName, null, null);
    }

    @Override
    public Certificate[] getEndEntityCertificates(String friendlyName,
            String storeName, String hostName, String[] intendedUse)
            throws KeyVaultException {
        return getEndEntityCertificates(friendlyName, storeName, hostName,
                intendedUse, false);
    }

    @Override
    public Certificate[] getEndEntityCertificates(String friendlyName,
            String storeName, String hostName, String[] intendedUse,
            boolean acceptSelfSignedCerts) throws KeyVaultException {
        return getEndEntityCertificates(friendlyName, storeName, hostName,
                intendedUse, acceptSelfSignedCerts, false);
    }

    @Override
    public Certificate[] getEndEntityCertificates(String friendlyName,
            String storeName, String hostName, String[] intendedUse,
            boolean acceptSelfSignedCerts, boolean includeExpiredCerts)
            throws KeyVaultException {
        PropertyBag request = new PropertyBag();
        PropertyBag response = new PropertyBag();
        binaryResp binaryResponse = new binaryResp();

        request.add(FRIENDLY_NAME, friendlyName);

        if (null != storeName) {
            request.add(STORE_NAME, storeName);
        }

        if (null != hostName) {
            request.add(HOST_NAME, hostName);
        }

        if (null != intendedUse) {
            for (String use : intendedUse) {
                request.add(INTENDED_USE, use);
            }
            request.addBool(EX_VALIDATION, true);
        }
        if (acceptSelfSignedCerts) {
            request.addBool(ACCEPT_SELF_SIGNED_CERTS, true);
        }
        if (includeExpiredCerts) {
            request.addBool(INCLUDE_EXPIRED_CERTS, true);
        }
        X509Certificate[] certs;
        try {
            boolean ok = kvCallWithRetry("getEndEntityCertificates", request,
                    response, (ByteBuffer) null, binaryResponse);
            if (!ok) {
                throw new KeyVaultException(
                        "getEndEntityCertificates: " + response.getErrorText());
            }

            certs = KeyVaultBinaryUtils
                    .bytesToCertificateChain(binaryResponse.getByteBuffer());
        } finally {
            binaryResponse.free();
        }

        if (certs == null) {
            /*
             * The "contract" for this method is that it returns an empty array
             * if no certificates are available.
             */
            certs = new X509Certificate[0];
        }

        return certs;
    }

    @Override
    public void purgeInvalidCertificates(String friendlyName, String storeName)
            throws KeyVaultException {
        purgeInvalidCertificates(friendlyName, storeName, 0);
    }

    @Override
    public void purgeInvalidCertificates(String friendlyName, String storeName,
            int advanceDays) throws KeyVaultException {
        purgeInvalidCertificates(friendlyName, storeName, null, null,
                advanceDays);
    }

    @Override
    public void purgeInvalidCertificates(String friendlyName, String storeName,
            String hostName, String[] intendedUse, int advanceDays)
            throws KeyVaultException {
        purgeInvalidCertificates(friendlyName, storeName, hostName, intendedUse,
                advanceDays, false);
    }

    @Override
    public void purgeInvalidCertificates(String friendlyName, String storeName,
            String hostName, String[] intendedUse, int advanceDays,
            boolean acceptSelfSignedCerts) throws KeyVaultException {
        PropertyBag request = new PropertyBag();
        PropertyBag response = new PropertyBag();

        request.add(FRIENDLY_NAME, friendlyName);

        if (null != storeName) {
            request.add(STORE_NAME, storeName);
        }

        if (null != hostName) {
            request.add(HOST_NAME, hostName);
        }

        if (null != intendedUse) {
            for (String use : intendedUse) {
                request.add(INTENDED_USE, use);
            }
            request.addBool(EX_VALIDATION, true);
        }

        if (advanceDays > 0) {
            request.addInt(ADVANCE_DAYS, advanceDays);
        }

        if (acceptSelfSignedCerts) {
            request.addBool(ACCEPT_SELF_SIGNED_CERTS, true);
        }

        if (!kvCallWithRetry("purgeInvalidCertificates", request, response)) {
            throw new KeyVaultException(
                    "purgeInvalidCertificates: " + response.getErrorText());
        }
    }

    @Deprecated
    @Override
    public void removeCertificate(X500Principal issuer, BigInteger serialNumber,
            String storeName) throws KeyVaultException {
        PropertyBag request = new PropertyBag();
        PropertyBag response = new PropertyBag();

        if (null != issuer) {
            request.add(ISSUER_NAME, issuer.getName(X500Principal.RFC1779));
        }

        ByteBuffer serial = null;
        if (null != serialNumber) {
            serial = KeyVaultBinaryUtils.renderAsSerialNumber(serialNumber);
        }

        if (null != storeName) {
            request.add(STORE_NAME, storeName);
        }

        if (log.isTraceEnabled()) {
            log.trace("Requesting remove certificate: issuer="
                    + request.get(ISSUER_NAME, "") + ", store="
                    + request.get(STORE_NAME, ""));
        }

        boolean ok = kvCallWithRetry("removeCertificate", request, response,
                serial, null);
        if (!ok) {
            throw new KeyVaultException(
                    "removeCertificate: " + response.getErrorText());
        }
    }

    @Override
    public void removeCertificate(X509Certificate certificate, String storeName)
            throws KeyVaultException {
        PropertyBag request = new PropertyBag();
        PropertyBag response = new PropertyBag();

        ByteBuffer blob = KeyVaultBinaryUtils
                .renderAsCertificateBlob(certificate);
        request.addBool(BINARY_IS_CERT, true);

        if (null != storeName) {
            request.add(STORE_NAME, storeName);
        }

        boolean ok = kvCallWithRetry("removeCertificate", request, response,
                blob, null);
        if (!ok) {
            throw new KeyVaultException(
                    "removeCertificate: " + response.getErrorText());
        }
    }

    @Override
    public void setFriendlyName(X500Principal issuer, BigInteger serialNumber,
            String storeName, String newFriendlyName) throws KeyVaultException {
        PropertyBag request = new PropertyBag();
        PropertyBag response = new PropertyBag();

        request.add(NEW_FRIENDLY_NAME, newFriendlyName);

        if (null != issuer) {
            request.add(ISSUER_NAME, issuer.getName("RFC1779"));
        }

        ByteBuffer serial = null;
        if (null != serialNumber) {
            serial = KeyVaultBinaryUtils.renderAsSerialNumber(serialNumber);
        }

        if (null != storeName) {
            request.add(STORE_NAME, storeName);
        }

        if (log.isTraceEnabled()) {
            log.trace("Requesting friendly name change: issuer="
                    + request.get(ISSUER_NAME, "") + ", store="
                    + request.get(STORE_NAME, ""));
        }

        boolean ok = kvCallWithRetry("setFriendlyName", request, response,
                serial, null);
        if (!ok) {
            throw new KeyVaultException(
                    "setFriendlyName: " + response.getErrorText());
        }
    }

    @Override
    public void setFriendlyName(X509Certificate certificate, String storeName,
            String newFriendlyName) throws KeyVaultException {
        PropertyBag request = new PropertyBag();
        PropertyBag response = new PropertyBag();

        request.add(NEW_FRIENDLY_NAME, newFriendlyName);
        ByteBuffer blob = KeyVaultBinaryUtils
                .renderAsCertificateBlob(certificate);
        request.addBool(BINARY_IS_CERT, true);

        if (null != storeName) {
            request.add(STORE_NAME, storeName);
        }

        if (!kvCallWithRetry("setFriendlyName", request, response, blob,
                null)) {
            throw new KeyVaultException(
                    "setFriendlyName: " + response.getErrorText());
        }
    }

    @Override
    public CertificateValidationResults validateCertificateChain(
            X509Certificate[] chain, String subject) throws KeyVaultException {
        PropertyBag request = new PropertyBag();
        PropertyBag response = new PropertyBag();
        ByteBuffer blob = KeyVaultBinaryUtils.renderAsCertificateBlob(chain);
        request.add(HOST_NAME, subject);

        boolean ok = kvCallWithRetry("validateCertificateChain", request,
                response, blob, null);

        if (log.isTraceEnabled()) {
            log.trace("validateCertificateChain response: " + response);
        }

        if (!ok) {
            throw new KeyVaultException(
                    "validateCertificateChain: " + response.getErrorText());
        }

        return generateCertificateValidationResults(response, chain, subject);
    }

    private CertificateValidationResults generateCertificateValidationResults(
            PropertyBag result, X509Certificate[] chain, String subject) {
        return new CertificateValidationResults(chain, subject, true,
                result.get(RESULT, null),
                result.get(CERT_INVALID_REASONS, null),
                result.get(CHAIN_INVALID_REASONS, null),
                result.getInt(CERT_ERROR_CODE, 0),
                result.getInt(CERT_INFO_CODE, 0),
                result.getInt(CHAIN_ERROR_CODE, 0),
                result.getInt(CHAIN_INFO_CODE, 0),
                result.getInt(CHAIN_POLICY_CODE, 0),
                result.getBool(CERT_SELF_SIGNED, false),
                result.get(WEAK_CRYPTO_REASON, null));
    }

    /*
     * Public method to generate a validation result to order, for testing
     * purposes only. Note that there is no way to specify a subject name,
     * normally considered mandatory, and the generatedByKeyVault flag is
     * hard-wired to be false. Otherwise, the input is directly reflected in the
     * output.
     */
    public CertificateValidationResults generateCertificateValidationResults(
            X509Certificate[] chain, String result, String certReasons,
            String chainReasons, int certError, int certInfo, int chainError,
            int chainInfo, int chainPolicy, boolean selfSigned) {

        return new CertificateValidationResults(chain, null, false, result,
                certReasons, chainReasons, certError, certInfo, chainError,
                chainInfo, chainPolicy, selfSigned, null);
    }

    @Override
    public int putPublic(PublicKeyWithProof pkp) throws KeyVaultException {
        PropertyBag request = new PropertyBag();
        PropertyBag response = new PropertyBag();
        ByteBuffer binaryIn = pkp.getPreparedKey();
        pkp.addToBag(request);
        boolean ok = kvCallWithRetry("putPublic", request, response, binaryIn,
                null);
        if (!ok) {
            throw new KeyVaultException(
                    "put public: " + response.getErrorText());
        }
        return response.getInt(GENERATION, 0);
    }

    @Override
    @Deprecated
    public int putPublic(byte[] pubKey, byte[] signature, String keyName,
            String dataToSign, HashAlgorithmName hashAlgoName)
            throws KeyVaultException {
        try {
            return putPublic(new PublicKeyWithProof(pubKey, signature,
                    KeyName.fromString(keyName), dataToSign, hashAlgoName));
        } catch (IllegalArgumentException | NoSuchAlgorithmException e) {
            throw new KeyVaultException(e);
        }
    }

    @Override
    public int putPublicKey(byte[] pubKey, String keyName)
            throws KeyVaultException {
        return putPublic(
                new PublicKeyWithProof(pubKey, KeyName.fromString(keyName)));
    }

    @Override
    public byte[] wrapKey(SecretKey key, KeyName wrappingKey)
            throws KeyVaultException {
        PropertyBag request = new PropertyBag();
        PropertyBag response = new PropertyBag();
        ByteBuffer binaryIn = KeyVaultBinaryUtils
                .getDirectByteBuffer(key.getEncoded());
        binaryResp binaryOut = new binaryResp();
        if (!wrappingKey.addToBag(request, NAME)) {
            throw new KeyVaultException("Invalid wrappingKey specifier");
        }
        /*
         * Encrypting data with a keypair is identical to key wrapping with no
         * padding.
         */
        boolean ok = kvCallWithRetry("encipher", request, response, binaryIn,
                binaryOut);
        if (!ok) {
            binaryOut.free();
            throw new KeyVaultException("encipher: " + response.getErrorText());
        }
        return binaryOut.consumeByteArray();
    }

    @Override
    public SecretKey unwrapKey(byte[] wrappedKey, KeyName wrappingKey)
            throws KeyVaultException {
        PropertyBag request = new PropertyBag();
        PropertyBag response = new PropertyBag();
        ByteBuffer binaryIn = KeyVaultBinaryUtils
                .getDirectByteBuffer(wrappedKey);
        binaryResp binaryOut = new binaryResp();
        if (!wrappingKey.addToBag(request, NAME)) {
            throw new KeyVaultException("Invalid wrappingKey specifier");
        }
        try {
            /*
             * Decrypting data with a keypair is identical to key unwrapping
             * with no padding.
             */
            boolean ok = kvCallWithRetry("decipher", request, response,
                    binaryIn, binaryOut);
            if (!ok) {
                throw new KeyVaultException(
                        "decipher: " + response.getErrorText());
            }
            String alg = KeyVaultTransform.EncryptionName.AES.name();
            return KeyVaultBinaryUtils
                    .bytesToSecretKey(binaryOut.getByteBuffer(), alg);
        } finally {
            binaryOut.free();
        }
    }
}
