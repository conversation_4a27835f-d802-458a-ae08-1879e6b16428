# Copyright (c) Omnissa, LLC. All rights reserved.
# This product is protected by copyright and intellectual property laws in the
# United States and other countries as well as by international treaties.
# -- Omnissa Restricted

"""vvcUnitTest: vvclib Unit Tests

Unit tests for vvclib using Horizon UT framework.
https://confluence.eng.vmware.com/pages/viewpage.action?pageId=1496002224

This framework uses Horizonrxut app to load and run tests defined in this
test module (vvcUnitTest.dll/.so).

The older UT framwork for vvclib is Boost based (vvclibTest.sc).

Maintainers:
    Horizon Blast Networking team

"""

import vmware

isWindows = vmware.Host().IsWindows()

NODE_NAME = "vvcUnitTest"

target = "vvcUnitTest"
fileDesc = "Unit Tests for vvclib"

env = vmware.DefaultEnvironment()

env.LoadTool(["libssl"])

env.LoadTool("gtest", linkDynamic=True)

env.Append(
    CPPDEFINES={
        "GTEST_LINKED_AS_SHARED_LIBRARY": None,
        "VVC_UNIT_TEST": None,
    },
    CPPPATH=[
        "#bora/apps/horizonrxtest/unitTest/public",
        "#bora/apps/horizonrxtest/unitTest/lib",
    ],
)

# rxUnitTestLib.py should be added before real test component scons
rxUnitTestLibNodeName = "rxUnitTestLib"
rxUnitTestLibNode = vmware.LookupNode(rxUnitTestLibNodeName, host=env.Host().Name())

rxTestLibDir = rxUnitTestLibNode[0].dir.abspath
rxTestLibName = rxUnitTestLibNodeName
if isWindows:
    # Linking on Windows requires the .lib extension.
    rxTestLibName += ".lib"

env.Append(
    STATICLIBPATH={rxTestLibName: rxTestLibDir},
    STATICLIBS=[rxTestLibName],
    LIBPATH=[rxTestLibDir],
)

env.Append(
    CPPPATH=[
        "#bora/lib/public",
        "#bora/public",
        "#bora/lib/vvclib",
    ],
)

if isWindows:
    env.LoadTool(
        [
            "msvcrt",
            "atlmfc",
            "horizonUtils",
        ]
    )
    env.Append(
        CPPDEFINES={
            "UNICODE": None,
            "_UNICODE": None,
        },
        CCFLAGS=[
            # Disable Inline Function Expansion, so that we can
            # hook/mock under Release compilation
            "/Ob0",
            "-bigobj",
        ],
        SHLIBFLAGS=[
            # Disable MSVC's ICF (Identical Code Folding) optimization
            # to prevent inline hook issues under Release compilation
            "/OPT:NOICF",
        ],
        LIBS=[
            "kernel32.lib",
            "user32.lib",
            "advapi32.lib",
            "gdi32.lib",
            "ole32.lib",
            "ws2_32.lib",
            "Shell32.lib",
            "bcrypt.lib",
            "crypt32.lib",
            "OleAut32.lib",
            "iphlpapi.lib",
        ],
    )
elif vmware.Host().IsLinux():
    env.Append(
        CCFLAGS=[
            "-fPIC",
            "-fno-inline",
        ],
        LIBS=[
            "dl",
        ],
    )

e = vmware.Executable(NODE_NAME, env=env)
e.addStaticLibs(
    "vmlibs",
    [
        "asyncsocket",
        "config",
        "coreDump",
        "crypto",
        "dict",
        "err",
        "file",
        "keyLocator",
        "keySafe",
        "lock",
        "log",
        "mempool",
        "misc",
        "hashMap",
        "panic",
        "panicDefault",
        "poll",
        "pollDefault",
        "productState",
        "rbtree",
        "sig",
        "slab",
        "ssl",
        "sslRemap",
        "string",
        "thread",
        "udpfec",
        "unicode",
        "user",
        "uuid",
    ],
)

if isWindows:
    env.AddRdeVersionInfo(e, target + ".dll", fileDesc)
    e.addStaticLibs(
        "vmlibs",
        [
            "win32auth",
            "wmi",
        ],
    )

e.addSubdirs(
    [
        "lib/vvclib/unittest",
        "lib/vvclib",
    ]
)

node = e.createSharedLibraryNode()
Depends(node, rxUnitTestLibNode)

vmware.RegisterEnv("%s-env" % NODE_NAME, env)
vmware.RegisterNode(node, NODE_NAME)
vmware.Alias("%s-build" % NODE_NAME, node)
