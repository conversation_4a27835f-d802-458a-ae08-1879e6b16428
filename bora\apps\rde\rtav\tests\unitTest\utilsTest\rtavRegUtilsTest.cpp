/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * rtavRegUtilsTest.cpp --
 *
 *     Unit test for rtav/libs/utils/RegUtils.cpp
 */


#include "stdafx.h"
#include "RegUtils.h"
#include "rtavTestCommon.h"
#include "rtavRegUtilsTest.h"
#include "rxUTLog.h"
#include "RegUtils.h"

#include <future>


#define MMDR_TEST_REGISTRY_1 _T("regTest1")
#define MMDR_TEST_REGISTRY_2 _T("regTest2")

MockRegNotifyCBFn *rtavRegUtilsTest::gMockRegNotifyCBFn = nullptr;

/*
 *-----------------------------------------------------------------------------
 *
 * rtavRegUtilsTest::SetUpTestCase --
 *
 *     SetUp for the entire test suite.
 *
 * Results:
 *     None.
 *
 * Side effects:
 *     None.
 *
 *-----------------------------------------------------------------------------
 */

void
rtavRegUtilsTest::SetUpTestCase()
{
   UTConsoleLog("%s", __FUNCTION__);
}


/*
 *-----------------------------------------------------------------------------
 *
 * rtavRegUtilsTest::TearDownTestCase --
 *
 *     TearDown for the entire test suite.
 *
 * Results:
 *     None.
 *
 * Side effects:
 *     None.
 *
 *-----------------------------------------------------------------------------
 */

void
rtavRegUtilsTest::TearDownTestCase()
{
   UTConsoleLog("%s", __FUNCTION__);
}


/*
 *-----------------------------------------------------------------------------
 *
 * rtavRegUtilsTest::SetUp --
 *
 *     SetUp for the each test case.
 *
 * Results:
 *     None.
 *
 * Side effects:
 *     None.
 *
 *-----------------------------------------------------------------------------
 */

void
rtavRegUtilsTest::SetUp()
{
   UTConsoleLog("%s", __FUNCTION__);

   RegUtils::DeleteRegistryValue(REGKEY_HKLM, REGKEY_MMDR, MMDR_TEST_REGISTRY_1);
   RegUtils::DeleteRegistryValue(REGKEY_HKLM, REGKEY_MMDR, MMDR_TEST_REGISTRY_2);

   RegUtils::SetDWORD(REGKEY_HKLM, REGKEY_MMDR, MMDR_TEST_REGISTRY_1, 1);
   RegUtils::SetDWORD(REGKEY_HKLM, REGKEY_MMDR, MMDR_TEST_REGISTRY_2, 2);

   gMockRegNotifyCBFn = new MockRegNotifyCBFn();
   EXPECT_TRUE(gMockRegNotifyCBFn != nullptr);
}


/*
 *-----------------------------------------------------------------------------
 *
 * rtavRegUtilsTest::TearDown --
 *
 *     TearDown for the each test case.
 *
 * Results:
 *     None.
 *
 * Side effects:
 *     None.
 *
 *-----------------------------------------------------------------------------
 */

void
rtavRegUtilsTest::TearDown()
{
   UTConsoleLog("%s", __FUNCTION__);

   if (gMockRegNotifyCBFn) {
      delete gMockRegNotifyCBFn;
      gMockRegNotifyCBFn = nullptr;
   }

   RegUtils::DeleteRegistryValue(REGKEY_HKLM, REGKEY_MMDR, MMDR_TEST_REGISTRY_1);
   RegUtils::DeleteRegistryValue(REGKEY_HKLM, REGKEY_MMDR, MMDR_TEST_REGISTRY_2);
}


/*
 *-----------------------------------------------------------------------------
 *
 * rtavRegUtilsTest::RegisterNotifCB --
 *
 *     Test cases to verify RegisterNotifCB()
 *
 * Results:
 *     None.
 *
 * Side effects:
 *     None.
 *
 *-----------------------------------------------------------------------------
 */

/*
 * This test is disabled due to it being broken. Fixing and re-enabling it is tracked in
 * https://omnissa.atlassian.net/browse/VCART-6154
 */

TEST_F(rtavRegUtilsTest, DISABLED_RegisterNotifCB)
{
   RegUtils regUtils;
   regUtils.Init(_T("RegUtils::RegisterNotifCB Unit Test"));

   std::promise<void> p;

   std::vector<const TCHAR *> regSubKeys{MMDR_TEST_REGISTRY_1};
   regUtils.RegisterNotifCB(HKEY_LOCAL_MACHINE, regSubKeys, RegChangedNotifyCB, this);

   EXPECT_CALL(*gMockRegNotifyCBFn, regNotifyCBFn(_, _))
      .WillOnce([&p](void *userData, std::vector<StringDef> &changedKeys) {
         p.set_value();
         return true;
      });

   RegUtils::SetDWORD(REGKEY_HKLM, REGKEY_MMDR, MMDR_TEST_REGISTRY_1, 2);

   p.get_future().get();
}


/*
 *-----------------------------------------------------------------------------
 *
 * rtavRegUtilsTest::RegisterNotif --
 *
 *     Test cases to verify RegisterNotif()
 *
 * Results:
 *     None.
 *
 * Side effects:
 *     None.
 *
 *-----------------------------------------------------------------------------
 */

/*
 * This test is disabled due to it being broken. Fixing and re-enabling it is tracked in
 * https://omnissa.atlassian.net/browse/VCART-6154
 */

TEST_F(rtavRegUtilsTest, DISABLED_RegisterNotif)
{
   RegUtils regUtils;
   regUtils.Init(_T("RegUtils::RegisterNotif Unit Test"));

   HANDLE event = CreateEvent(NULL, FALSE, FALSE, NULL);
   EXPECT_TRUE(event != NULL);

   std::vector<const TCHAR *> regSubKeys{MMDR_TEST_REGISTRY_1, MMDR_TEST_REGISTRY_2};
   regUtils.RegisterNotif(HKEY_LOCAL_MACHINE, regSubKeys, event);

   RegUtils::SetDWORD(REGKEY_HKLM, REGKEY_MMDR, MMDR_TEST_REGISTRY_1, 3);
   RegUtils::SetDWORD(REGKEY_HKLM, REGKEY_MMDR, MMDR_TEST_REGISTRY_2, 4);

   /*
    * This test used to use INFINITE which would hang on failure. Instead, time out on failure.
    * We should expect callbacks to happen quickly, so 10 seconds should be more than enough.
    */
   DWORD ret = WaitForSingleObject(event, 10000);
   EXPECT_EQ(ret, WAIT_OBJECT_0);
}


/*
 *-----------------------------------------------------------------------------
 *
 * rtavRegUtilsTest::WorkingWebcams --
 *
 *     Test cases to verify WorkingWebcams registry key
 *
 * Results:
 *     None.
 *
 * Side effects:
 *     None.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(rtavRegUtilsTest, WorkingWebcam)
{
   StringDef path = REGKEY_MMDR;
   path += _T("\\WorkingWebcams\\webcam");
   LONG errorCode = RegDeleteKeyExW(HKEY_LOCAL_MACHINE, path.c_str(), KEY_WOW64_64KEY, 0);
   EXPECT_FALSE(RegUtils::CheckPath(HKEY_LOCAL_MACHINE, path));
   EXPECT_TRUE(RegUtils::SetWorkingWebcam(_T("webcam")));
   EXPECT_TRUE(RegUtils::CheckPath(HKEY_LOCAL_MACHINE, path));
   EXPECT_TRUE(RegUtils::RemoveWorkingWebcam(_T("webcam")));
   EXPECT_FALSE(RegUtils::CheckPath(HKEY_LOCAL_MACHINE, path));
}


/*
 *-----------------------------------------------------------------------------
 *
 * rtavRegUtilsTest::WorkingMicrophones --
 *
 *     Test cases to verify WorkingMicrophones registry key
 *
 * Results:
 *     None.
 *
 * Side effects:
 *     None.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(rtavRegUtilsTest, WorkingMicrophones)
{
   StringDef path = REGKEY_MMDR;
   path += _T("\\WorkingMicrophones\\microphone");
   LONG errorCode = RegDeleteKeyExW(HKEY_LOCAL_MACHINE, path.c_str(), KEY_WOW64_64KEY, 0);
   EXPECT_FALSE(RegUtils::CheckPath(HKEY_LOCAL_MACHINE, path));
   EXPECT_TRUE(RegUtils::SetWorkingMicrophone(_T("microphone")));
   EXPECT_TRUE(RegUtils::CheckPath(HKEY_LOCAL_MACHINE, path));
   EXPECT_TRUE(RegUtils::RemoveWorkingMicrophone(_T("microphone")));
   EXPECT_FALSE(RegUtils::CheckPath(HKEY_LOCAL_MACHINE, path));
}