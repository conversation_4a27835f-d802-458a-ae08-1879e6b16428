/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * vvcSessionManager.c --
 *
 *      Interface for VVC Session Management code.
 */

#include "asyncsocket.h"
#include "asyncBweSocket.h"
#include "blastSocketInt.h"
#include "fecAsyncSocket.h"
#include "fecSocketOptions.h"
#include "log.h"
#include "util.h"
#include "strutil.h"
#include "horizon.h"
#include "../vvclib/vvclibInt.h"
#include "../vvclib/vvcDebug.h"
#include "blastSocket.h"
#include "blastSocketUtil.h"

/* gcc needs special syntax to handle zero-length variadic arguments */
#if defined(_MSC_VER)
#   define VVCSESSIONMGRLOG(fmt, ...) Log("[VVCSessionManager] %s: " fmt, __FUNCTION__, __VA_ARGS__)
#else
#   define VVCSESSIONMGRLOG(fmt, ...)                                                              \
      Log("[VVCSessionManager] %s: " fmt, __FUNCTION__, ##__VA_ARGS__)
#endif

static const char *const GATEWAYED_SECRET_MARKER = "/r/";
static const char *const DIRECT_SECRET_MARKER = "/d/";
static const char *const GET_REQUEST_STR = "GET";

#ifdef _WIN32
// VVC perf provider GUID - {03F677B6-F526-4FEF-A4A2-483579AAFD26}
#   define VVC_PERF_PROVIDER_GUID                                                                  \
      {0x03F677B6, 0xF526, 0x4fef, 0xA4, 0xA2, 0x48, 0x35, 0x79, 0xAA, 0xFD, 0x26}
const GUID vvcPerfProviderGuid = VVC_PERF_PROVIDER_GUID;

// VVC perf counter GUID {585093FF-6923-4F94-8FB2-349C56E664EF}
#   define VVC_PERF_COUNTER_SET_GUID                                                               \
      {0x585093FF, 0x6923, 0x4f94, 0x8F, 0xB2, 0x34, 0x9C, 0x56, 0xE6, 0x64, 0xEF}
const GUID vvcPerfCounterSetGuid = VVC_PERF_COUNTER_SET_GUID;
#endif

typedef struct VvcSessionEventsCbData {
   void *clientData;
   int32 sessionId;
   BlastSocketVvcSessionWrapper *sessionWrapper;
} VvcSessionEventsCbData;

/*
 * We use this struct to get a list of sessionWrapper objects that matches the
 * "vvcSessionId" field when iterating the sessionMap via HashMap_Iterate().
 */
typedef struct BlastSocketSessionMapIterateCbData {
   int32 vvcSessionId; // IN
   DblLnkLst_Links sessionWrapperList;
} BlastSocketSessionMapIterateCbData;


// BlastSocketVvcSessionMgr::sessionMap Functions.

static Bool BlastSocketExistsSessionMapEntry(BlastSocketContext *blastSocketCtx, const char *vAuth);

static Bool BlastSocketGetSessionMapEntry(BlastSocketContext *blastSocketCtx, const char *vAuth,
                                          BlastSocketVvcSessionWrapper **sessionWrapper);

static Bool BlastSocketAddSessionMapEntry(BlastSocketContext *blastSocketCtx, const char *vAuth,
                                          BlastSocketVvcSessionWrapper *sessionWrapper);

static Bool BlastSocketRemoveSessionMapEntry(BlastSocketContext *blastSocketCtx, const char *vAuth);

// BlastSocketSessionWrapper Functions.

static BlastSocketVvcSessionWrapper *BlastSocketCreateSessionWrapper(
   int32 vvcSessionId, VvcSessionId vvcSessionIdHandle, VvcSessionHandle sessionHandle,
   EFIGUID smConnectionId, Bool negotiatedNCEnabled, Bool negotiatedBENITEnabled, const char *vAuth,
   BlastSocketContext *blastSocketCtx);

static void BlastSocketDestroySessionWrapper(BlastSocketVvcSessionWrapper *sessionWrapper);

// VvcSessionEvents Callbacks

static void BlastSocketVvcSessionOnErrorCb(VvcSessionHandle sessionHandle, VvcStatus status,
                                           void *clientData);

static void BlastSocketVvcSessionOnEstablishedCb(VvcSessionHandle sessionHandle, void *clientData);

static void BlastSocketVvcSessionOnDestroyCb(VvcSessionHandle sessionHandle, void *clientData);

static void BlastSocketVvcSessionOnCloseCb(VvcSessionHandle vvcSessionHandle, uint32 userMetadata,
                                           void *clientData);

// Vvc Hub Start/Stop Functions

static Bool BlastSocketStartVvcHub(BlastSocketVvcSessionWrapper *sessionWrapper);

static void BlastSocketStopVvcHub(VvcSessionId vvcSessionIdHandle, VvcSessionHandle sessionHandle,
                                  int32 vvcSessionId);

#ifdef VVCHUB_SUPPORT
static void BlastSocketHubLibCallback(void *context, Bool isError, Bool isConnected,
                                      unsigned long vvcSessionId, unsigned long processId);
#endif

// VvcSessionId generation API and Callback

static Bool BlastSocketGenerateVvcSessionId(BlastSocketContext *blastSocketContext,
                                            const char *vAuth, int32 *vvcSessionId,
                                            int32 *shadowInstanceId);

static Bool BlastSocketGetVvcSessionID(const int32 wtsSessionId, const int32 shadowInstanceId,
                                       int32 *vvcSessionId);

static Bool BlastSocketGenerateUniqueSessionId(int32 *shadowInstanceId, int32 *vvcSessionId);

static Bool BlastSocketGetPlatformSessionId(int32 *wtsSessionId);

static Bool BlastSocketPushAsockToVvcSession(BlastSocketVvcSessionWrapper *sessionWrapper,
                                             VvcSessionHandle vvcSessionHandle, AsyncSocket *asock,
                                             Bool isControlAsock, Bool isEndToEndConnection,
                                             Bool blastSocketThreadEnabled, uint32 nonce,
                                             uint32 serialNo);

static Bool BlastSocketPushTransportSwitchPolicyToVvcSession(BlastSocketContext *blastSocketCtx,
                                                             VvcSessionHandle vvcSessionHandle);

static Bool BlastSocketPushQoSPolicyToVvcSession(BlastSocketContext *blastSocketCtx,
                                                 VvcSessionHandle vvcSessionHandle);

static Bool BlastSocketPushPeerAllowListToVvc(const char *peerFeatureAllowList);

static void BlastSocketMapIterCbGetMatchingSessionWrappers(void *key, void *data, void *userData);

static Bool BlastSocketIsNewVvcSessionNeeded(BlastSocketContext *blastSocketCtx, int32 vvcSessionId,
                                             const char *vAuth, VvcSessionHandle *vvcSessionHandle);

static Bool BlastSocketExistsWSPeerConfigMapEntry(BlastSocketContext *blastSocketCtx,
                                                  const char *vAuth);

static Bool BlastSocketGetWSPeerConfigMapEntry(BlastSocketContext *blastSocketCtx,
                                               const char *vAuth,
                                               BlastSocketWSPeerConfig **wsPeerConfig);

static Bool BlastSocketAddWSPeerConfigMapEntry(BlastSocketContext *blastSocketCtx,
                                               const char *vAuth,
                                               BlastSocketWSPeerConfig *wsPeerConfig);

static Bool BlastSocketRemoveWSPeerConfigMapEntry(BlastSocketContext *blastSocketCtx,
                                                  const char *vAuth);

static Bool BlastSocketHandleNetworkFailure(BlastSocketContext *blastSocketCtx, const char *vAuth);

static VvcStatus BlastSocketSetVvcMinMaxBw(BlastSocketContext *blastSocketCtx, const char *vAuth,
                                           double minimumRate, double maximumRate,
                                           double maxBandwidthBurstMsec);

/*
 *----------------------------------------------------------------------------
 *
 * VvcPollCallbackCb --
 *
 *      The callback invoked by VVC schedule a callback on poll thread
 *      with pollClass set to POLL_CS_MAIN
 *
 * Results:
 *      VvcStatus.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

static VvcStatus
VvcPollCallbackCb(VvcInstancePollCb callback, // IN
                  void *clientData,           // IN
                  Bool periodic,              // IN
                  uint32 timeoutUs)           // IN
{
   HorizonStatus status = Poll_Callback(POLL_CS_MAIN, periodic ? POLL_FLAG_PERIODIC : 0, callback,
                                        clientData, POLL_REALTIME, timeoutUs, NULL);

   return status == HORIZON_STATUS_SUCCESS ? VVC_STATUS_SUCCESS : VVC_STATUS_ERROR;
}


/*
 *----------------------------------------------------------------------------
 *
 * VvcPollRemoveCb --
 *
 *      The callback invoked by VVC to remove a scheduled callback on
 *      poll loop with poll class set to POLL_CS_MAIN
 *
 * Results:
 *      VvcStatus.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

static VvcStatus
VvcPollRemoveCb(VvcInstancePollCb callback, // IN
                void *clientData,           // IN
                Bool periodic)              // IN
{
   return Poll_CallbackRemove(POLL_CS_MAIN, periodic ? POLL_FLAG_PERIODIC : 0, callback, clientData,
                              POLL_REALTIME)
             ? VVC_STATUS_SUCCESS
             : VVC_STATUS_ERROR;
}

/*
 *----------------------------------------------------------------------------
 *
 * VvcNetPollCallbackCb --
 *
 *      The callback invoked by VVC schedule a callback on poll thread
 *      with pollClass set to POLL_DEFAULT_CS_NET
 *
 * Results:
 *      VvcStatus.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

VvcStatus
VvcNetPollCallbackCb(VvcInstancePollCb callback, // IN
                     void *clientData,           // IN
                     Bool periodic,              // IN
                     uint32 timeoutUs)           // IN
{
   HorizonStatus status = Poll_Callback(POLL_DEFAULT_CS_NET, periodic ? POLL_FLAG_PERIODIC : 0,
                                        callback, clientData, POLL_REALTIME, timeoutUs, NULL);

   return status == HORIZON_STATUS_SUCCESS ? VVC_STATUS_SUCCESS : VVC_STATUS_ERROR;
}


/*
 *----------------------------------------------------------------------------
 *
 * VvcNetPollRemoveCb --
 *
 *      The callback invoked by VVC to remove a scheduled callback on
 *      poll loop with poll class set to POLL_DEFAULT_CS_NET
 *
 * Results:
 *      VvcStatus.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

VvcStatus
VvcNetPollRemoveCb(VvcInstancePollCb callback, // IN
                   void *clientData,           // IN
                   Bool periodic)              // IN
{
   return Poll_CallbackRemove(POLL_DEFAULT_CS_NET, periodic ? POLL_FLAG_PERIODIC : 0, callback,
                              clientData, POLL_REALTIME)
             ? VVC_STATUS_SUCCESS
             : VVC_STATUS_ERROR;
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketGetWSVAuth --
 *
 *      Retrieve the websocket URI and parse it for the vauth parameter.
 *      Allocate a new string and return the parameter in it. Copied from
 *      bora\mks\main\mksRemoteMgr.c (MKSRemoteMgrGetWebsocketVauthParam)
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      Allocates memory.
 *
 *----------------------------------------------------------------------------
 */

static char *
BlastSocketGetWSVAuth(AsyncSocket *asock) // IN
{
   const char *uri;
   const char *vauthKey = "vauth=";
   const char *start, *end;
   char *value;

   uri = AsyncSocket_GetWebSocketURI(asock);
   if (uri == NULL) {
      return NULL;
   }

   /*
    * Find "vauth="
    */
   start = stristr(uri, vauthKey);
   if (start == NULL) {
      return NULL;
   }

   /*
    * Skip over "vauth=":
    */
   start += strlen(vauthKey);

   /*
    * Find the start of the next parameter, or the end of the string:
    */
   end = strstr(start, "&");
   if (end == NULL) {
      end = start + strlen(start);
   }
   if (end - start > 1024) {
      return NULL;
   }

   /*
    * Allocate new storage, copy the parameter out and null-terminate
    * it:
    */
   value = Util_SafeMalloc(end - start + 1);
   memcpy(value, start, end - start);
   value[end - start] = '\0';
   return value;
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketGetRouteSpecifier --
 *
 *      Retrieve the websocket URI and parse it for the route token.
 *      Allocate a new string and return the parameter in it.
 *
 * Results:
 *      Route token if parsed successfully, NULL otherwise.
 *
 * Side effects:
 *      Allocates memory.
 *
 *----------------------------------------------------------------------------
 */

static char *
BlastSocketGetRouteSpecifier(AsyncSocket *asock) // IN
{
   const char *uri;
   const char *start, *end;
   char *value;

   uri = AsyncSocket_GetWebSocketURI(asock);
   if (uri == NULL) {
      return NULL;
   }

   /*
    * Find "/d/" or "/r"/
    */
   start = stristr(uri, DIRECT_SECRET_MARKER);
   if (start == NULL) {
      start = stristr(uri, GATEWAYED_SECRET_MARKER);
      if (start == NULL) {
         return NULL;
      } else {
         /*
          * Skip over "/r/"
          */
         start += strlen(GATEWAYED_SECRET_MARKER);
      }
   } else {
      /*
       * Skip over "/d/"
       */
      start += strlen(DIRECT_SECRET_MARKER);
   }

   /*
    * Find the start of the next parameter, or the end of the string:
    */
   end = strstr(start, "/");
   if (end == NULL) {
      end = start + strlen(start);
   }
   if (end - start > 1024) {
      return NULL;
   }

   /*
    * Allocate new storage, copy the parameter out and null-terminate
    * it:
    */
   value = Util_SafeMalloc(end - start + 1);
   memcpy(value, start, end - start);
   value[end - start] = '\0';
   return value;
}


/*
 *----------------------------------------------------------------------
 *
 * BlastSocketGetReconnectCookieFromURI --
 *
 *      Resolve the cookie from the url
 *
 * Results:
 *      If there is a parameter named "session", then return the value;
 *      otherwise, return NULL.
 *
 * Side effects:
 *      Allocates memory.
 *
 *----------------------------------------------------------------------
 */

static char *
BlastSocketGetReconnectCookieFromURI(const char *uri) // IN
{
   const char *sessionKey = "session=";
   char *value = NULL;
   const char *start, *end;

   if (uri == NULL) {
      return NULL;
   }

   // find "session="
   start = stristr(uri, sessionKey);
   if (start == NULL) {
      return NULL;
   }

   // skip over "session="
   start += strlen(sessionKey);

   // find the start of the next parameter, or the end of the string
   end = strstr(start, "&");
   if (end == NULL) {
      end = start + strlen(start);
   }
   if (end - start > 1024) {
      return NULL;
   }

   /*
    * Allocate new storage, copy the parameter out and null-terminate it.
    */
   value = Util_SafeMalloc(end - start + 1);
   memcpy(value, start, end - start);
   value[end - start] = '\0';
   return value;
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketProcessConnInfoTLVs
 *
 *    Processes the ConnInfo related TLVs (known) received.
 *
 *    If a known connection TLV can't be properly parsed, further
 *    processing is terminated and FALSE is returned.
 *
 *    Caller should initialize blastConnInfo before calling.
 *
 * Results:
 *    blastConnInfo contains successfully parsed connection info TLV values.
 *    returns TRUE on success and FALSE otherwise.
 *
 * Side effects:
 *    None.
 *
 *----------------------------------------------------------------------------
 */

static Bool
BlastSocketProcessConnInfoTLVs(AsyncSocket *asock,                 // IN
                               char *buf,                          // IN
                               int bufLen,                         // IN
                               BlastConnectionInfo *blastConnInfo) // OUT
{
   uint8 val8 = 0;
   uint32 val32 = 0;


   // extract connection usermode value
   val8 = 0;
   if (!BlastSetup_GetVal8_FromMinorTLV(asock, buf, bufLen, BLAST_SETUP_MTYPE_USERMODE, &val8)) {
      VVCSESSIONMGRLOG("Could not extract blast conn info usermode value.");
      return FALSE;
   }

   if (val8 < BLAST_CONN_USER_MODE_MAX) {
      blastConnInfo->usermode = (BlastConnectionUserMode)(val8);
      VVCSESSIONMGRLOG("Connection user mode: %s",
                       BlastSetup_ConnInfoGetUserModeStr(blastConnInfo->usermode));
   } else {
      VVCSESSIONMGRLOG("invalid blast connection user mode %d.", val8);
      /* continue processing */
   }

   // extract broker connection protocol info
   val8 = 0;
   if (!BlastSetup_GetVal8_FromMinorTLV(asock, buf, bufLen, BLAST_SETUP_MTYPE_BROKER_PROTOCOL,
                                        &val8)) {
      VVCSESSIONMGRLOG("error extracting broker connection protocol info.");
      return FALSE;
   }

   val8 &= BLAST_CONN_PROTO_ID_MASK;

   if (val8 < BLAST_CONN_PROTO_MAX) {
      blastConnInfo->brokerProtocol = (BlastConnectionProtocolID)(val8);
      VVCSESSIONMGRLOG("Broker protocol: %s",
                       (blastConnInfo->brokerProtocol == BLAST_CONN_PROTO_TCP) ? "TCP" : "UDP");
   } else {
      VVCSESSIONMGRLOG("invalid blast broker protocol %d.", val8);
      /* continue processing */
   }

   // extract primary connection protocol info
   val8 = 0;
   if (!BlastSetup_GetVal8_FromMinorTLV(asock, buf, bufLen, BLAST_SETUP_MTYPE_PRIMARY_PROTOCOL,
                                        &val8)) {
      VVCSESSIONMGRLOG("error extracting primary connection protocol info.");
      return FALSE;
   }

   val8 &= BLAST_CONN_PROTO_ID_MASK;

   if (val8 < BLAST_CONN_PROTO_MAX) {
      blastConnInfo->primaryProtocol = (BlastConnectionProtocolID)(val8);
      VVCSESSIONMGRLOG("Primary protocol: %s",
                       (blastConnInfo->primaryProtocol == BLAST_CONN_PROTO_TCP) ? "TCP" : "UDP");
   } else {
      VVCSESSIONMGRLOG("invalid blast primary protocol %d.", val8);
      /* continue processing */
   }

   // extract secondary connection protocol info
   val8 = 0;
   if (!BlastSetup_GetVal8_FromMinorTLV(asock, buf, bufLen, BLAST_SETUP_MTYPE_SECONDARY_PROTOCOL,
                                        &val8)) {
      VVCSESSIONMGRLOG("error extracting secondary connection protocol info.");
      return FALSE;
   }

   val8 &= BLAST_CONN_PROTO_ID_MASK;

   if (val8 < BLAST_CONN_PROTO_MAX) {
      blastConnInfo->secondaryProtocol = (BlastConnectionProtocolID)(val8);
      VVCSESSIONMGRLOG("Secondary protocol: %s",
                       (blastConnInfo->secondaryProtocol == BLAST_CONN_PROTO_TCP) ? "TCP" : "UDP");
   } else {
      VVCSESSIONMGRLOG("invalid blast secondary protocol %d.", val8);
      /* continue processing */
   }

   // extract the connection generation ID
   val32 = 0;
   if (!BlastSetup_GetVal32_FromMinorTLV(asock, buf, bufLen, BLAST_SETUP_MTYPE_GENERATION_ID,
                                         &val32)) {
      VVCSESSIONMGRLOG("error extracting blast generation id info.");
      return FALSE;
   }

   blastConnInfo->generationID = val32;
   VVCSESSIONMGRLOG("Generation ID: %x", blastConnInfo->generationID);

   return TRUE;
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketParamsError
 *
 *    Error callback to handle socket errors while the Blast Setup Params
 *    are being fetched.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

static void
BlastSocketParamsError(int error,          // IN
                       AsyncSocket *asock, // IN
                       void *clientData)   // IN
{
   ASSERT(clientData != NULL);
   AsyncSocket_Close(asock);
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketCompleteGetParamsCB
 *
 *    Callback from the setup module once the setup data is available.
 *
 *    The serialNo param is only applicable for VVC raw channel TCP connections.
 *
 * Results:
 *    None.
 *
 * Side effects:
 *      cookieOut will be freed as memory is allocated in heap from
 *      authentication callback.
 *
 *----------------------------------------------------------------------------
 */

void
BlastSocketCompleteGetParamsCB(AsyncSocket *asock, // IN
                               void *buf,          // IN
                               int len,            // IN
                               Bool status,        // IN
                               void *clientData,   // IN
                               int serialNo)       // IN
{
   char *vAuth;
   char *cookie;
   char *route;
   char *subproto;
   char *cookieOut = NULL;
   BlastConnectionInfo blastConnInfo;
   char *valueBuf;
   int valueLen;
   int32 vvcSessionId;
   int lockCount = 0;

   VvcSessionHandle vvcSessionHandle;
   BlastSocketVvcSessionWrapper *sessionWrapper;
   Bool ok = FALSE;
   Bool asockIsTCP;

   BlastSocketContext *blastSocketContext = clientData;

   ASSERT(clientData != NULL);
   if (status == FALSE) {
      VVCSESSIONMGRLOG("Could not get Blast Setup params, close the socket");
      AsyncSocket_Close(asock);
      return;
   }

   VVCSESSIONMGRLOG("Got %d bytes of setup data.", len);

   // Get the vAuth
   if (FALSE == BlastSetup_GetValueFromTLV(asock, (char *)buf, len, BLAST_SETUP_TYPE_VAUTH,
                                           &valueLen, &valueBuf)) {
      VVCSESSIONMGRLOG("Could not extract vAuth.");
      AsyncSocket_Close(asock);
      return;
   }
   vAuth = valueBuf;

   // Get the Cookie
   if (FALSE == BlastSetup_GetValueFromTLV(asock, (char *)buf, len, BLAST_SETUP_TYPE_COOKIE,
                                           &valueLen, &valueBuf)) {
      VVCSESSIONMGRLOG("Could not extract Cookie.");
      AsyncSocket_Close(asock);
      return;
   }
   cookie = valueBuf;

   // Get the Route Token
   if (FALSE == BlastSetup_GetValueFromTLV(asock, (char *)buf, len, BLAST_SETUP_TYPE_ROUTE,
                                           &valueLen, &valueBuf)) {
      VVCSESSIONMGRLOG("Could not extract Route Specifier.");
      AsyncSocket_Close(asock);
      return;
   }
   route = valueBuf;

   // Get the Subprotocol
   if (FALSE == BlastSetup_GetValueFromTLV(asock, (char *)buf, len, BLAST_SETUP_TYPE_SUBPROTOCOL,
                                           &valueLen, &valueBuf)) {
      VVCSESSIONMGRLOG("Could not extract Subprotocol.");
      AsyncSocket_Close(asock);
      return;
   }
   subproto = valueBuf;

   // Process the connection info
   memset(&blastConnInfo, 0, sizeof(blastConnInfo));
   if (FALSE == BlastSocketProcessConnInfoTLVs(asock, (char *)buf, len, &blastConnInfo)) {
      VVCSESSIONMGRLOG("Could not get connection protocol info.");
      AsyncSocket_Close(asock);
      return;
   }

   // Authenticate
   VVCSESSIONMGRLOG("Start authentication");

   cookieOut = BlastSocketProcessAuthRequest(vAuth, route, cookie, blastSocketContext, FALSE);

   if (cookieOut == NULL) {
      // Auth failed
      AsyncSocket_Close(asock);
      return;
   }

   free(cookieOut);

   VVCSESSIONMGRLOG("Done with authentication, now push %s asock to VvcSession",
                    FECAsyncSocket_IsFecSocket(asock) ? "BEAT" : "TCP");

   /*
    * Authentication successful.
    * Now retrieve the VvcSession based on vAuth and then Push the BEAT
    * Asock to that VvcSession.
    * Then call the original accept callback.
    */

   /* Release BlastSocketLock before calling into VVC; also before acquiring
    * sessionmap exclusive lock to preserve the lock ordering
    */
   while (BlastSocket_IsLocked()) {
      BlastSocket_Unlock();
      lockCount++;
   }
   ASSERT(lockCount <= 2);

   LOCK_SESSIONMAP(blastSocketContext);

   ok = BlastSocketGetSessionMapEntry(blastSocketContext, vAuth, &sessionWrapper);

   if (!ok) {
      VVCSESSIONMGRLOG("Entry for vAuth:%" MASK_TOKEN " is not present in "
                       "SessionMap",
                       vAuth);
      UNLOCK_SESSIONMAP(blastSocketContext);
      AsyncSocket_Close(asock);
      return;
   }

   VVCSESSIONMGRLOG("Entry for vAuth:%" MASK_TOKEN " is present in SessionMap"
                    ", vvcSessionId: %d, vvcSessionHandle: %p, vAuth:%" MASK_TOKEN
                    ", Now pushing asock : %p to the VvcSession. ",
                    vAuth, sessionWrapper->vvcSessionId, sessionWrapper->sessionHandle,
                    sessionWrapper->vAuth, asock);

   vvcSessionHandle = sessionWrapper->sessionHandle;
   vvcSessionId = sessionWrapper->vvcSessionId;

   UNLOCK_SESSIONMAP(blastSocketContext);

   /*
    * Not ideal to use serialNo to identify TCP socket. Add an asyncsocket API
    * if needed in future.
    */
   asockIsTCP = (serialNo != 0);

   // Wrap TCP socket in asyncBWE for raw channels.
   if (asockIsTCP) {
      AsyncBweSocketOptionsStatic opts = AsyncBweSocketOptionsStatic_CreateDefault();
      opts.keepAliveInterval = TRANSPORT_KEEPALIVE_TIMEOUT_SEC / 2;
      opts.keepAliveTimeout = TRANSPORT_KEEPALIVE_TIMEOUT_SEC;
      AsyncSocket *asockBwe = AsyncSocket_BweWrap(asock, &opts);
      if (!asockBwe) {
         VVCSESSIONMGRLOG("AsyncBweWrap on asyncsocket %d for raw channel failed",
                          AsyncSocket_GetID(asock));
         AsyncSocket_Close(asock);
         return;
      }
      VVCSESSIONMGRLOG("AsyncBweWrap asyncsocket %d with asyncsocket %d for raw "
                       "channel",
                       AsyncSocket_GetID(asock), AsyncSocket_GetID(asockBwe));
      AsyncSocket_SetCloseOptions(asockBwe, ASYNCSOCKET_FLUSH_TIMEOUT_MSEC, NULL);
      AsyncSocket_UseNodelay(asockBwe, TRUE);
      asock = asockBwe;
   }

   ok = BlastSocketPushAsockToVvcSession(sessionWrapper, vvcSessionHandle, asock, FALSE, TRUE,
                                         blastSocketContext->params.blastSocketThreadEnabled, 0,
                                         serialNo);

   while (lockCount--) {
      BlastSocket_Lock();
   }

   if (!ok) {
      VVCSESSIONMGRLOG("Failed to push %s asock: %p to VvcSession: %p", asockIsTCP ? "TCP" : "BEAT",
                       asock, vvcSessionHandle);
      AsyncSocket_Close(asock);
      return;
   }

   VVCSESSIONMGRLOG("Pushed %s asock: %p to VvcSession: %p", asockIsTCP ? "TCP" : "BEAT", asock,
                    vvcSessionHandle);

   // log secondary (BEAT) connection, not logged for raw (TCP) connection
   if (!asockIsTCP) {
      VVCSESSIONMGRLOG("Data Connection:UDP");
   }

   // Save blastConnectionInfo so we can query it later
   sessionWrapper->blastConnInfo = blastConnInfo;

   VVCSESSIONMGRLOG("End Accept callback");
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketGetWebSocketPathFromUri --
 *
 *      Parse the websocket request uri via secret marker if have
 *
 * Results:
 *      Websocket path
 *
 *----------------------------------------------------------------------------
 */

static char *
BlastSocketGetWebSocketPathFromUri(const char *uri)
{
   const char *start = NULL;
   const char *pathStart = NULL;
   const char *paramsStart = NULL;
   char *urlPath = NULL;
   char *routeSpecifierStart = NULL;
   char *routeSpecifierEnd = NULL;
   char *root = NULL;
   size_t length;

   if (uri == NULL) {
      return NULL;
   }

   start = strstr(uri, "://");
   if (start != NULL) {
      start += 3;
      pathStart = strstr(start, "/");
      if (pathStart == NULL) {
         pathStart = start;
      }
   } else {
      pathStart = uri;
   }

   if (pathStart == NULL || *pathStart == '\0') {
      free(urlPath);
      return NULL;
   }

   paramsStart = strstr(pathStart, "?");
   if (paramsStart != NULL) {
      length = paramsStart - pathStart;
   } else {
      length = strlen(pathStart);
   }

   urlPath = Util_SafeMalloc(length + 1);
   memcpy(urlPath, pathStart, length);
   urlPath[length] = '\0';

   if ((routeSpecifierStart = strstr(urlPath, GATEWAYED_SECRET_MARKER)) != NULL) {
      routeSpecifierEnd = strstr(routeSpecifierStart + strlen(GATEWAYED_SECRET_MARKER), "/");
   } else if ((routeSpecifierStart = strstr(urlPath, DIRECT_SECRET_MARKER)) != NULL) {
      routeSpecifierEnd = strstr(routeSpecifierStart + strlen(DIRECT_SECRET_MARKER), "/");
   } else {
      free(urlPath);
      return NULL;
   }

   if (routeSpecifierEnd != NULL) {
      *(routeSpecifierEnd + 1) = '\0';
   }
   root = Util_SafeStrdup(routeSpecifierStart);
   free(urlPath);
   return root;
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketGetRouteTokenFromReq --
 *
 *      Parse for the websocket URI and then parse it for the route token.
 *      Allocate a new string and return the parameter in it.
 *
 * Results:
 *      Route token if parsed successfully, NULL otherwise.
 *
 * Side effects:
 *      Allocates memory. Caller is responsible for freeing it.
 *
 *----------------------------------------------------------------------------
 */

static char *
BlastSocketGetRouteTokenFromReq(const char *httpRequest)
{
   /*
    * Parse httpRequest for URI and then parse for route.
    * We need to parse the URI first incase the "/d/" or "/r/" markers are
    * somehow missing in the URI which may lead to a false positive from the
    * remainder of the request. Format:
    * GET /[dr]/<route-specifier>/?vauth=<vauth>[&...] HTTP/[version]
    *
    * XXX: This function is used to find route specifier in Websocket URL so
    * that it can be sent as a response to the rid request by client.
    * This should eventually be removed when we generate (store and validate)
    * a completely different route specifier for BEAT later.
    */
   const char *start = Str_Strnstr(httpRequest, GET_REQUEST_STR, MAX_HTTP_BUF_SIZE);
   const char *end;
   char *uriPath, *uriPathEnd;
   size_t uriPathLen;
   char *value;

   if (start == NULL) {
      return NULL;
   }

   start += strlen(GET_REQUEST_STR);

   while (*start == ' ') {
      start++;
   }

   end = start;
   // We only want the path part.  Ignore params, if any.
   while (*end != '?' && *end != '\r' && *end != '\n' && *end != ' ' && *end != '\0') {
      end++;
   }

   uriPathLen = end - start;
   uriPath = Util_SafeMalloc(uriPathLen + 1);
   memcpy(uriPath, start, uriPathLen);
   uriPathEnd = &uriPath[uriPathLen];
   *uriPathEnd = '\0';

   // Find one of the markers in the path
   start = stristr(uriPath, DIRECT_SECRET_MARKER);
   if (start != NULL) {
      start += strlen(DIRECT_SECRET_MARKER);
   } else {
      start = stristr(uriPath, GATEWAYED_SECRET_MARKER);
      if (start != NULL) {
         start += strlen(GATEWAYED_SECRET_MARKER);
      } else {
         free(uriPath);
         return NULL;
      }
   }

   // The route spec goes from start to a '/' or end-of-path
   end = Str_Strnstr(start, "/", uriPathEnd - start);
   if (end == NULL) {
      end = uriPathEnd;
   }
   if (end - start > 1024) {
      free(uriPath);
      return NULL;
   }

   value = Util_SafeMalloc(end - start + 1);
   memcpy(value, start, end - start);
   value[end - start] = '\0';

   free(uriPath);
   return value;
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketGetPairValueByKey --
 *
 *      Parse a string and return the value if the key is found
 *      The input string should follow format "KEY1=VALUE1,KEY2=,KEY3=VALUE3"
 *
 * Results:
 *      Newly allocated string containing the value if found,
 *      NULL otherwise.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

static char *
BlastSocketGetPairValueByKey(const char *str, // IN
                             const char *key) // IN
{
   char *pch = NULL;
   char *cache = NULL;
   char *begin = NULL;
   char *value = NULL;
   size_t len;

   if (str == NULL) {
      return NULL;
   }

   len = strlen(str) + 1;
   cache = Util_SafeMalloc(len);
   Str_Strcpy(cache, str, len);

   begin = strstr(cache, key);

   if (begin != NULL) {
      pch = strtok(begin, ","); // get first token from string
      if (pch != NULL) {
         pch = strtok(pch, "="); // get key
         if (pch != NULL) {
            pch = strtok(NULL, "="); // get value
            if (pch) {               // found the value
               len = strlen(pch) + 1;
               value = Util_SafeMalloc(len);
               Str_Strcpy(value, pch, len);
               free(cache);
               return value;
            }
         }
      }
   }

   free(cache);
   return NULL;
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketChooseSubProtocol --
 *
 *      Given a list of subprotocols supported by the client, we need to pick
 *      one that the Agent will support.
 *
 * Results:
 *      Newly allocated string containing the selected subprotocol,
 *      Otherwise NULL.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

static char *
BlastSocketChooseSubProtocol(const char *clientList) // IN
{
   char *cache = NULL;
   size_t len;
   char *client = NULL;
   char *server = NULL;
   char *finalSubprotocol = NULL;

   /*
    * Server supported secondary subprotocols. If we want to add a new
    * secondary subprotocol, add it to below array.
    * Format: "udpv2/...", delimiter '/' is required among protocols
    */
   char serverSubprotocols[] = "udpv2";
   ASSERT(clientList != NULL);
   VVCSESSIONMGRLOG("ClientList: %s", clientList);

   /*
    * clientList is a list of subprotocols that the client supports. Parse this
    * list and check each one. We assume that the list is in priority order.
    * That is, the first subprotocol in the list is the one the client prefers
    * the most.
    */
   len = strlen(clientList) + 1;
   cache = Util_SafeMalloc(len);
   Str_Strcpy(cache, clientList, len);
   server = strtok(serverSubprotocols, "/");

   while (server != NULL) {
      client = strtok(cache, "/");
      while (client != NULL) {
         if (strcmp(client, server) == 0) {
            // match
            len = strlen(server) + 1;
            finalSubprotocol = Util_SafeMalloc(len);
            Str_Strcpy(finalSubprotocol, server, len);
            free(cache);
            return finalSubprotocol;
         }
         client = strtok(NULL, "/");
      }
      server = strtok(NULL, "/");
   }

   free(cache);
   return NULL;
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketBuildUDPResponse --
 *
 *      This function will be called if server has enabled UDP on its end.
 *      Check if client has send flags to indicate that UDP is being used.
 *      If so, then add the relevant headers to the websocket upgrade response.
 *      The only way udp will be enabled is if it is set in BlastSocket
 *      on the server, AND if UDP headers are found in the websocket request.
 *      These conditions together indicate that UDP is supported by both server
 *      and client.
 *
 * Results:
 *      Returns TRUE if UDP Response Headers are present, FALSE otherwise.
 *
 * Side effects:
 *      If the client has sent a flag asking for UDP, then we insert UDP
 *      meta-data into the websocket response and set udpHeaderPresent to
 *      TRUE. Otherwise the websocket response is not altered and
 *      udpHeadersPresent is set to FALSE.
 *
 *----------------------------------------------------------------------------
 */

static Bool
BlastSocketBuildUDPResponse(const char *httpRequest,               // IN
                            char **httpResponse,                   // IN/OUT
                            void *clientData,                      // IN
                            BlastSocketWSPeerConfig *wsPeerConfig) // OUT
{
   char *udpE2EValue = NULL;
   char *udpHopValue = NULL;
   char *udpE2EOldValue = NULL;
   char *udpHopOldValue = NULL;
   char *e2eValueStr = NULL;
   char *hopValueStr = NULL;
   char *value = NULL;
   char *finalSubprotocol = NULL;
   Bool e2eEnabled = FALSE;
   Bool hopEnabled = FALSE;
   Bool versionMatch = FALSE;
   Bool udpHeadersPresent = FALSE;
   Bool negotiatedBENIT = FALSE;
   Bool beatRidEnabled = FALSE;
   BlastSocketContext *blastSocketContext = clientData;
   Bool ret = FALSE;

   ASSERT(httpRequest != NULL);
   ASSERT(blastSocketContext != NULL);
   ASSERT(wsPeerConfig != NULL);

   /*
    * X-Blast-UDP-E2E-Req is the end to end request (from the client).
    * It contains "enable" and "versions".
    * X-Blast-UDP-Hop-Req is the hop request. It is sent by the prior hop.
    * If there is no BSG, then this is directly whatever the client sends. If
    * there is a BSG, then the BSG will change the header contents sent by the
    * client, so that the server sees whatever the BSG has inserted. This only
    * has a single value of "enable"
    */
   udpE2EValue = AsyncSocket_WebSocketGetHttpHeader(httpRequest, UDP_E2E_REQ_HEADER_STRING);
   udpHopValue = AsyncSocket_WebSocketGetHttpHeader(httpRequest, UDP_HOP_REQ_HEADER_STRING);

   if (blastSocketContext->params.legacyBlastWSProtocolsAllowed) {
      udpE2EOldValue =
         AsyncSocket_WebSocketGetHttpHeader(httpRequest, OLD_UDP_E2E_REQ_HEADER_STRING);
      udpHopOldValue =
         AsyncSocket_WebSocketGetHttpHeader(httpRequest, OLD_UDP_HOP_REQ_HEADER_STRING);
   }

   if ((udpE2EValue == NULL && udpE2EOldValue == NULL) ||
       (udpHopValue == NULL && udpHopOldValue == NULL)) {
      free(udpE2EValue);
      free(udpHopValue);
      free(udpE2EOldValue);
      free(udpHopOldValue);
      VVCSESSIONMGRLOG("Empty / Missing UDP headers, nothing to do.");
      return ret;
   }

   VVCSESSIONMGRLOG("UDP Headers found in the WSUPgradeRequest.");

   /*
    * Do not log UDP and Blast E2E and Hop headers verbatim to the log.
    * Uncomment below "#if 0" to enable logging headers to the file for
    * debugging.
    */
#if 0
   VVCSESSIONMGRLOG("UDP Headers:-> "
                    "E2E Req Value: %s, Hop Req Value: %s\n"
                    "E2E ReqOld Value: %s, Hop ReqOld Value: %s",
                    udpE2EValue ? udpE2EValue : "null",
                    udpHopValue ? udpHopValue : "null",
                    udpE2EOldValue ? udpE2EOldValue : "null",
                    udpHopOldValue ? udpHopOldValue : "null");
#endif

   if (udpE2EOldValue) {
      wsPeerConfig->isLegacyHeaderSeen = TRUE;
      if (udpE2EValue && strcmp(udpE2EOldValue, udpE2EValue)) {
         VVCSESSIONMGRLOG("UDP Headers:-> E2E Req: %s "
                          "differs from E2E ReqOld(using): %s",
                          udpE2EValue, udpE2EOldValue);
      }
      free(udpE2EValue);
      udpE2EValue = udpE2EOldValue;
      udpE2EOldValue = NULL;
   }

   if (udpHopOldValue) {
      wsPeerConfig->isLegacyHeaderSeen = TRUE;
      if (udpHopValue && strcmp(udpHopOldValue, udpHopValue)) {
         VVCSESSIONMGRLOG("UDP Headers:-> Hop Req: %s "
                          "differs from Hop ReqOld(using): %s",
                          udpHopValue, udpHopOldValue);
      }
      free(udpHopValue);
      udpHopValue = udpHopOldValue;
      udpHopOldValue = NULL;
   }

   /*
    * Parse the E2E header and figure out whether UDP is enabled, and what
    * version the client is requesting. We only support one version right
    * now, so validate that the version sent by the client is what we support.
    */

   // If the key is 'enable', make sure the value is 1
   value = BlastSocketGetPairValueByKey(udpE2EValue, ENABLE_VALUE);
   if (value != NULL) {
      if (strcmp(value, ENABLE) == 0) {
         e2eEnabled = TRUE;
      }
      free(value);
   }

   /*
    * If the key is 'secondarysubprotocol', then the value is a list
    * of secondary subprotocols that are supported by the client.
    * Select one from this list and return that in finalSubprotocol.
    * If we are unable to pick a value to use, the function below
    * will return false and we would not use UDP.
    */
   value = BlastSocketGetPairValueByKey(udpE2EValue, SECONDARY_SUBPROTOCOLS_VALUE);
   if (value != NULL) {
      finalSubprotocol = BlastSocketChooseSubProtocol(value);
      if (finalSubprotocol != NULL) {
         VVCSESSIONMGRLOG("Final Chosen Subprotocol: %s", finalSubprotocol);
         versionMatch = TRUE;
      }
      free(value);
   }

   /*
    * Check if BENIT was offered by the Client.
    * If yes, then calculate negotiated BENIT value and send it in response.
    */
   value = BlastSocketGetPairValueByKey(udpE2EValue, BENIT_VALUE);
   if (value != NULL) {
      if (strcmp(value, BENIT_ENABLED) == 0) {
         wsPeerConfig->isWSPeerBENITEnabled = TRUE;
         negotiatedBENIT = wsPeerConfig->isWSPeerBENITEnabled &&
                           blastSocketContext->params.localNetworkIntelligenceEnabled;
      } else {
         wsPeerConfig->isWSPeerBENITEnabled = FALSE;
      }
      free(value);
   }
   VVCSESSIONMGRLOG("BENIT offered by peer: %s, negotiatedBENIT: %s.",
                    wsPeerConfig->isWSPeerBENITEnabled ? "Yes" : "No",
                    negotiatedBENIT ? BENIT_ENABLED : BENIT_DISABLED);

   /*
    * Note: QoSPolicy is communicated by the Blast E2E and Hop Headers and not
    * via UDP E2E and Hop Headers,
    */

   // If the key is 'enable', make sure the value is 1
   value = BlastSocketGetPairValueByKey(udpHopValue, ENABLE_VALUE);
   if (value != NULL) {
      if (strcmp(value, ENABLE) == 0) {
         hopEnabled = TRUE;
      }
      free(value);
   }

   // Check if Route Replacement is enabled.
   if (blastSocketContext->params.beatRouteReplacementEnabled) {
      // Check if rid id present and value is 1
      value = BlastSocketGetPairValueByKey(udpHopValue, BEAT_RID_VALUE);
      if (value != NULL) {
         if (strcmp(value, BEAT_RID_ENABLED) == 0) {
            beatRidEnabled = TRUE;
         }
         free(value);
      }
   }

   udpHeadersPresent = e2eEnabled && versionMatch && hopEnabled;

   if (udpHeadersPresent) {
      char *auxiliaryFlowAttributes = NULL;
      char *beatRidAttribute = NULL;

      VVCSESSIONMGRLOG("[BEAT] UDP is enabled by server and client, send UDP "
                       "meta-data in the upgrade header response");

      if (blastSocketContext->params.udpAuxiliaryFlowsEnabled) {
         /*
          * In principle some or all of the auxiliary flow attributes
          * we place in the Hop-Rsp header can be different from the
          * corresponding main flow attributes, but for now we send the
          * auxiliary settings as copies of the main flow values.
          */
         auxiliaryFlowAttributes =
            Str_SafeAsprintf(NULL,
                             ",%s%08x"
                             ",%s%s"
                             ",%s%d",
                             AF_LABEL_VALUE, blastSocketContext->udpFecPort, AF_IP_VALUE,
                             blastSocketContext->params.serverIp, AF_PORT_VALUE,
                             blastSocketContext->params.serverUdpExternalPort);
      }

      // Insert route specifier in X-Blast-UDP-E2E-Rsp if requested.
      if (beatRidEnabled) {
         char *route = NULL;

         VVCSESSIONMGRLOG("Route token requested. Adding Route in E2E resp.");
         route = BlastSocketGetRouteTokenFromReq(httpRequest);
         if (route != NULL) {
            beatRidAttribute = Str_SafeAsprintf(NULL, ",%s%s", BEAT_RID_VALUE, route);
            free(route);
         } else {
            VVCSESSIONMGRLOG("Failed to get route from URI, not adding route"
                             " in E2E resp.");
         }
      }

      /*
       * Set UDP Http Response Headers
       * X-Blast-UDP-E2E-Rsp is the end to end response (from the server).
       * It contains "thumbprint", "port", and "status".
       * X-Blast-UDP-Hop-Rsp is the hop response. It is sent to the next
       * hop. If there is no BSG, then this is directly received by the client.
       * If there is a BSG, then the BSG will change the header contents sent
       * by the server, so that the client sees whatever the BSG has inserted.
       * This contains "IP", "Port", "enable", and "label".
       */

      e2eValueStr =
         Str_SafeAsprintf(NULL,
                          "%s%s,%s%s,"
                          "%s%d,%s%s,%s%s,%s%s%s",
                          THUMBPRINT_VALUE, blastSocketContext->params.thumbprint,
                          THUMBPRINT256_VALUE, blastSocketContext->params.thumbprint256, PORT_VALUE,
                          blastSocketContext->params.serverUdpExternalPort, STATUS_VALUE, STATUS,
                          SECONDARY_SUBPROTOCOLS_VALUE, finalSubprotocol, BENIT_VALUE,
                          negotiatedBENIT ? BENIT_ENABLED : BENIT_DISABLED,
                          (NULL != beatRidAttribute) ? beatRidAttribute : "");

      hopValueStr = Str_SafeAsprintf(
         NULL, "%s%s,%s%d,%s%s,%s%08x%s", IP_VALUE, blastSocketContext->params.serverIp, PORT_VALUE,
         blastSocketContext->params.serverUdpExternalPort, ENABLE_VALUE, ENABLE, LABEL_VALUE,
         blastSocketContext->udpFecPort,
         (NULL != auxiliaryFlowAttributes ? auxiliaryFlowAttributes : ""));

      if (wsPeerConfig->isLegacyHeaderSeen &&
          blastSocketContext->params.legacyBlastWSProtocolsAllowed) {
         *httpResponse = Str_SafeAsprintf(
            NULL,
            "%s %s\r\n"
            "%s %s\r\n"
            "%s %s\r\n"
            "%s %s\r\n",
            UDP_E2E_RSP_HEADER_STRING, e2eValueStr, OLD_UDP_E2E_RSP_HEADER_STRING, e2eValueStr,
            UDP_HOP_RSP_HEADER_STRING, hopValueStr, OLD_UDP_HOP_RSP_HEADER_STRING, hopValueStr);
      } else {
         *httpResponse = Str_SafeAsprintf(NULL,
                                          "%s %s\r\n"
                                          "%s %s\r\n",
                                          UDP_E2E_RSP_HEADER_STRING, e2eValueStr,
                                          UDP_HOP_RSP_HEADER_STRING, hopValueStr);
      }
      free(e2eValueStr);
      free(hopValueStr);
      free(auxiliaryFlowAttributes);
      free(beatRidAttribute);
      ret = TRUE;
   }

   free(udpE2EValue);
   free(udpHopValue);
   free(udpE2EOldValue);
   free(udpHopOldValue);
   free(finalSubprotocol);
   VVCSESSIONMGRLOG("Handled UDP related parts of the websocket upgrade, "
                    "UDP Response Headers present: %s",
                    ret ? "Yes" : "No");
   return ret;
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketBuildBlastWSResponse --
 *
 *      This function builds the WSResponse for WSUpgradeRequest.
 *
 *      1. If UDP is enabled then:
 *      Use helper func BlastSocketBuildUDPResponse() to build UDP Responses.
 *
 *      2. In all cases (irrespective of UDP Enabled or Not) do:
 *      If Blast Headers are found in the httpRequest, then build
 *      response for those Blast Headers (regardless of UDP Enabled or not).
 *
 *      3. Process both new and old Blast Request headers, if present. Though
 *      client sets same value for both the new and old Blast request headers,
 *      a BSG en route running a older version would not have processed the new
 *      hop header(simply passed it through). Hence, new hop header could
 *      differ from the old which would have been processed by BSG. So, use the
 *      old hop header values, if present. Note that if BSG was running a new
 *      version it would ensure that both old and new header values are same
 *      for the headers it generates.
 *
 *      4. Generate new response headers always, but generate old response
 *      headers only if old request headers are received:
 *         a. When both old and new request headers are received send both old
 *            and new response headers as the connection may be going through
 *            an older version of BSG which would forward both request headers
 *            upstream but would only forward old response headers downsteam
 *         b. When old request headers are deprecated in future, we may be
 *            configured for and/or receiving only new request headers in which
 *            case send only new response headers
 *
 * Results:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

static void
BlastSocketBuildBlastWSResponse(const char *httpRequest,               // IN
                                char **httpResponse,                   // IN/OUT
                                void *clientData,                      // IN
                                BlastSocketWSPeerConfig *wsPeerConfig) // OUT
{
   BlastSocketContext *blastSocketContext = clientData;
   char *blastE2EValue = NULL;
   char *blastHopValue = NULL;
   char *blastFeaturesValue = NULL;
   char *blastE2EOldValue = NULL;
   char *blastHopOldValue = NULL;
   char *blastFeaturesOldValue = NULL;
   char *e2eValueStr = NULL;
   char *hopValueStr = NULL;
   char *blastResponse = NULL;
   char *udpResponse = NULL;
   char *vvcRawChannelsStr = NULL;
   Bool udpHeadersPresent = FALSE;
   Bool blastHeadersPresent = FALSE;

   char *aOutV4QoSPolicyParamsStr = NULL;
   char *cOutV4QoSPolicyParamsStr = NULL;
   char *bUpV4QoSPolicyParamsStr = NULL;
   char *bDownV4QoSPolicyParamsStr = NULL;

   char *aOutV6QoSPolicyParamsStr = NULL;
   char *cOutV6QoSPolicyParamsStr = NULL;
   char *bUpV6QoSPolicyParamsStr = NULL;
   char *bDownV6QoSPolicyParamsStr = NULL;

   char *mptVersionStr = NULL;
   char *peerMptVersionStr = NULL;
   uint8 peerMptVersion = VVC_MPT_VERSION_MIN;

   char *deferredAcksParamsStr = NULL;

   Bool generateTcpBweVersion = FALSE;
   char *tcpBweVersionStr = NULL;
   char *peerTcpBweVersionStr = NULL;
   uint32 peerTcpBweVersion = ASYNC_BWE_SOCKET_VERSION_NONE;

   ASSERT(httpRequest != NULL);
   ASSERT(blastSocketContext != NULL);
   ASSERT(wsPeerConfig != NULL);

   wsPeerConfig->isLegacyHeaderSeen = FALSE;

   /*
    * X-Blast-E2E-Req is the end to end request (from the client).
    * X-Blast-UDP-Hop-Req is the hop request. It is sent by the prior hop.
    * Both these headers only contain the QoS values (for now).
    */

   blastE2EValue = AsyncSocket_WebSocketGetHttpHeader(httpRequest, BLAST_E2E_REQ_HEADER_STRING);
   blastHopValue = AsyncSocket_WebSocketGetHttpHeader(httpRequest, BLAST_HOP_REQ_HEADER_STRING);
   if (blastSocketContext->params.legacyBlastWSProtocolsAllowed) {
      blastE2EOldValue =
         AsyncSocket_WebSocketGetHttpHeader(httpRequest, OLD_BLAST_E2E_REQ_HEADER_STRING);
      blastHopOldValue =
         AsyncSocket_WebSocketGetHttpHeader(httpRequest, OLD_BLAST_HOP_REQ_HEADER_STRING);
   }

   if ((blastE2EValue == NULL && blastE2EOldValue == NULL) ||
       (blastHopValue == NULL && blastHopOldValue == NULL)) {
      free(blastE2EValue);
      free(blastHopValue);
      blastE2EValue = NULL;
      blastHopValue = NULL;
      free(blastE2EOldValue);
      free(blastHopOldValue);
      blastE2EOldValue = NULL;
      blastHopOldValue = NULL;

      VVCSESSIONMGRLOG("Empty / Missing Blast E2E and Hop headers");
      blastHeadersPresent = FALSE;
      wsPeerConfig->negotiatedTcpBweVersion = ASYNC_BWE_SOCKET_VERSION_NONE;
   } else {
      VVCSESSIONMGRLOG("Blast Headers (E2E and Hop) found in the "
                       "WSUPgradeRequest.");

      /*
       * Do not log UDP and Blast E2E and Hop headers verbatim to the log.
       * Uncomment below "#if 0" to enable logging headers to the file for
       * debugging.
       */
#if 0
      VVCSESSIONMGRLOG("Blast Headers:-> "
                       "E2E Req Value: %s, Hop Req Value: %s\n"
                       "E2E ReqOld Value: %s, Hop ReqOld Value: %s",
                       blastE2EValue ? blastE2EValue : "null",
                       blastHopValue ? blastHopValue : "null",
                       blastE2EOldValue ? blastE2EOldValue : "null",
                       blastHopOldValue ? blastHopOldValue : "null");
#endif

      /*
       * For compatibility, use old header values, if present(see comment above)
       */
      if (blastE2EOldValue) {
         wsPeerConfig->isLegacyHeaderSeen = TRUE;
         if (blastE2EValue && strcmp(blastE2EOldValue, blastE2EValue)) {
            VVCSESSIONMGRLOG("Blast Headers:-> E2E Req: %s differs from "
                             "E2E ReqOld(using): %s ",
                             blastE2EValue, blastE2EOldValue);
         }
         free(blastE2EValue);
         blastE2EValue = blastE2EOldValue;
         blastE2EOldValue = NULL;
      }

      if (blastHopOldValue) {
         wsPeerConfig->isLegacyHeaderSeen = TRUE;
         if (blastHopValue && strcmp(blastHopOldValue, blastHopValue)) {
            VVCSESSIONMGRLOG("Blast Headers:-> Hop Req: %s differs from "
                             "Hop ReqOld(using): %s ",
                             blastHopValue, blastHopOldValue);
         }
         free(blastHopValue);
         blastHopValue = blastHopOldValue;
         blastHopOldValue = NULL;
      }

      // Build response for Blast E2E and Hop Headers now
      blastHeadersPresent = TRUE;

      /*
       * Build QoSPolicyParamsStr and include it in httpResponse.
       *
       * QoSPolicy is communicated as below:
       * 1. Blast E2E Header response: COut (TCP v4 & v6 and UDP v4 & v6)
       * 2. Blast Hop Header response: BUp & BDown (TCP v4 & v6 and UDP v4 & v6)
       *
       * Note: Client will ignore the COut values that agent puts in Blast E2E
       * httpResponse if the client has its values locally configured.
       */

      // Get AOut and COut TCPv4 and UDPv4 and put it in httpResponse
      BlastSocketGetAOutV4QoSPolicyParamsStr(&blastSocketContext->qosPolicyParams,
                                             &aOutV4QoSPolicyParamsStr);
      BlastSocketGetCOutV4QoSPolicyParamsStr(&blastSocketContext->qosPolicyParams,
                                             &cOutV4QoSPolicyParamsStr);

      // Get BUp and BDown TCPv4 and UDPv4 and put it in httpResponse
      BlastSocketGetBUpV4QoSPolicyParamsStr(&blastSocketContext->qosPolicyParams,
                                            &bUpV4QoSPolicyParamsStr);
      BlastSocketGetBDownV4QoSPolicyParamsStr(&blastSocketContext->qosPolicyParams,
                                              &bDownV4QoSPolicyParamsStr);

      // Get AOut and COut TCPv6 and UDPv6 and put it in httpResponse
      BlastSocketGetAOutV6QoSPolicyParamsStr(&blastSocketContext->qosPolicyParams,
                                             &aOutV6QoSPolicyParamsStr);
      BlastSocketGetCOutV6QoSPolicyParamsStr(&blastSocketContext->qosPolicyParams,
                                             &cOutV6QoSPolicyParamsStr);

      // Get BUp and BDown TCPv6 and UDPv6 and put it in httpResponse
      BlastSocketGetBUpV6QoSPolicyParamsStr(&blastSocketContext->qosPolicyParams,
                                            &bUpV6QoSPolicyParamsStr);
      BlastSocketGetBDownV6QoSPolicyParamsStr(&blastSocketContext->qosPolicyParams,
                                              &bDownV6QoSPolicyParamsStr);

      BlastSocketGetMptVersionStr(wsPeerConfig->localMptVersion, &mptVersionStr);

      BlastSocketGetVvcDeferredAcksParamsStr(&blastSocketContext->deferredAcksParams,
                                             &deferredAcksParamsStr);

      VVCSESSIONMGRLOG("Sending QoSPolicy via Blast Headers: "
                       "QoSPolicy in Blast E2E: %s,%s "
                       "QoSPolicy in Blast Hop: %s,%s,%s,%s ",
                       cOutV4QoSPolicyParamsStr, cOutV6QoSPolicyParamsStr, bUpV4QoSPolicyParamsStr,
                       bDownV4QoSPolicyParamsStr, bUpV6QoSPolicyParamsStr,
                       bDownV6QoSPolicyParamsStr);

      VVCSESSIONMGRLOG("Sending Mpt version via Blast Headers: "
                       "MptVersion in Blast E2E: %s ",
                       mptVersionStr);

      VVCSESSIONMGRLOG("Sending Vvc Deferred Acks Params via Blast Headers: "
                       "Vvc DeferredAcksParams in Blast E2E: %s ",
                       deferredAcksParamsStr);

      /* Determine if we need to send TCP BWE version in the Blast response */
      if (blastSocketContext->params.bweEnabled &&
          wsPeerConfig->localTcpBweVersion != ASYNC_BWE_SOCKET_VERSION_NONE) {

         /* Extract the peer's TCP BWE version */
         peerTcpBweVersionStr = BlastSocketGetPairValueByKey(blastE2EValue, TCP_BWE_VERSION_VALUE);

         if (!peerTcpBweVersionStr) {
            VVCSESSIONMGRLOG("No value found for %s", TCP_BWE_VERSION_VALUE);
         } else {
            if (!StrUtil_StrToUint(&peerTcpBweVersion, peerTcpBweVersionStr)) {
               VVCSESSIONMGRLOG("Error extracting value from %s", TCP_BWE_VERSION_VALUE);
               peerTcpBweVersion = ASYNC_BWE_SOCKET_VERSION_NONE;
            }
            free(peerTcpBweVersionStr);
         }

         wsPeerConfig->negotiatedTcpBweVersion = BlastSocketGetNegotiatedTcpBweVersion(
            wsPeerConfig->localTcpBweVersion, peerTcpBweVersion);

         /* Send our TCP BWE version only if we got one from peer */
         if (peerTcpBweVersion != ASYNC_BWE_SOCKET_VERSION_NONE) {
            generateTcpBweVersion = TRUE;

            VVCSESSIONMGRLOG("[Blast E2E Headers] Local tcpBweVersion: %u, "
                             "Negotiated tcpBweVersion: %u",
                             wsPeerConfig->localTcpBweVersion,
                             wsPeerConfig->negotiatedTcpBweVersion);
         }
      } else {
         wsPeerConfig->negotiatedTcpBweVersion = ASYNC_BWE_SOCKET_VERSION_NONE;
      }

      if (generateTcpBweVersion) {
         BlastSocketGetTcpBweVersionStr(wsPeerConfig->localTcpBweVersion, &tcpBweVersionStr);

         VVCSESSIONMGRLOG("Sending tcp BWE version via Blast Headers: "
                          "tcpBweVersion in Blast E2E: %s ",
                          tcpBweVersionStr);

         e2eValueStr = Str_SafeAsprintf(NULL, "%s,%s,%s,%s,%s", cOutV4QoSPolicyParamsStr,
                                        cOutV6QoSPolicyParamsStr, mptVersionStr,
                                        deferredAcksParamsStr, tcpBweVersionStr);
         free(tcpBweVersionStr);
      } else {
         e2eValueStr =
            Str_SafeAsprintf(NULL, "%s,%s,%s,%s", cOutV4QoSPolicyParamsStr,
                             cOutV6QoSPolicyParamsStr, mptVersionStr, deferredAcksParamsStr);
      }

      // Determine if we need to send Raw Channel support in the response
      vvcRawChannelsStr = Str_SafeAsprintf(NULL, "%s%s", VVC_RAW_CHANNELS_VALUE, ENABLE);
      if (strstr(blastHopValue, vvcRawChannelsStr)) {
         VVCSESSIONMGRLOG("Sending VVC raw channels support in Blast Hop header: %s ",
                          vvcRawChannelsStr);
         hopValueStr = Str_SafeAsprintf(NULL, "%s,%s,%s,%s,%s", bUpV4QoSPolicyParamsStr,
                                        bDownV4QoSPolicyParamsStr, bUpV6QoSPolicyParamsStr,
                                        bDownV6QoSPolicyParamsStr, vvcRawChannelsStr);
      } else {
         hopValueStr = Str_SafeAsprintf(NULL, "%s,%s,%s,%s", bUpV4QoSPolicyParamsStr,
                                        bDownV4QoSPolicyParamsStr, bUpV6QoSPolicyParamsStr,
                                        bDownV6QoSPolicyParamsStr);
      }

      if (wsPeerConfig->isLegacyHeaderSeen &&
          blastSocketContext->params.legacyBlastWSProtocolsAllowed) {
         blastResponse = Str_SafeAsprintf(
            NULL,
            "%s %s\r\n"
            "%s %s\r\n"
            "%s %s\r\n"
            "%s %s\r\n",
            BLAST_E2E_RSP_HEADER_STRING, e2eValueStr, OLD_BLAST_E2E_RSP_HEADER_STRING, e2eValueStr,
            BLAST_HOP_RSP_HEADER_STRING, hopValueStr, OLD_BLAST_HOP_RSP_HEADER_STRING, hopValueStr);
      } else {
         blastResponse = Str_SafeAsprintf(NULL,
                                          "%s %s\r\n"
                                          "%s %s\r\n",
                                          BLAST_E2E_RSP_HEADER_STRING, e2eValueStr,
                                          BLAST_HOP_RSP_HEADER_STRING, hopValueStr);
      }
      free(e2eValueStr);
      e2eValueStr = NULL;
      free(hopValueStr);
      hopValueStr = NULL;
      free(vvcRawChannelsStr);

      peerMptVersionStr = BlastSocketGetPairValueByKey(blastE2EValue, MPT_VERSION_VALUE);
      if (!peerMptVersionStr) {
         VVCSESSIONMGRLOG("No value found for %s", MPT_VERSION_VALUE);
      } else {
         peerMptVersion = atoi(peerMptVersionStr);
         free(peerMptVersionStr);
      }

      wsPeerConfig->negotiatedMptVersion =
         BlastSocketGetNegotiatedMptVersion(wsPeerConfig->localMptVersion, peerMptVersion);

      VVCSESSIONMGRLOG("[Blast E2E Headers] Local MPTVersion: %d, "
                       "Negotiated MPTVersion: %d",
                       wsPeerConfig->localMptVersion, wsPeerConfig->negotiatedMptVersion);
   }

   // X-Blast-Features-Req holds the peer's feature allow list.
   blastFeaturesValue =
      AsyncSocket_WebSocketGetHttpHeader(httpRequest, BLAST_FEATURES_REQ_HEADER_STRING);
   if (blastSocketContext->params.legacyBlastWSProtocolsAllowed) {
      blastFeaturesOldValue =
         AsyncSocket_WebSocketGetHttpHeader(httpRequest, OLD_BLAST_FEATURES_REQ_HEADER_STRING);
   }

   if (blastFeaturesOldValue) {
      wsPeerConfig->isLegacyHeaderSeen = TRUE;
      if (blastFeaturesValue && strcmp(blastFeaturesOldValue, blastFeaturesValue)) {
         VVCSESSIONMGRLOG("Blast Headers:-> Features: %s differs from "
                          "FeaturesOld(using): %s ",
                          blastFeaturesValue, blastFeaturesOldValue);
      }
      free(blastFeaturesValue);
      blastFeaturesValue = blastFeaturesOldValue;
      blastFeaturesOldValue = NULL;
   }

   if (blastFeaturesValue != NULL) {
      char *tempBlastResponse = NULL;

      // Process blastFeatures list here first, then send response
      wsPeerConfig->peerFeatureAllowList = strdup(blastFeaturesValue);
      VVCSESSIONMGRLOG("Received Feature Allow List in Blast Headers: %s",
                       wsPeerConfig->peerFeatureAllowList);
      tempBlastResponse = blastResponse;
      if (wsPeerConfig->isLegacyHeaderSeen &&
          blastSocketContext->params.legacyBlastWSProtocolsAllowed) {
         blastResponse = Str_SafeAsprintf(NULL,
                                          "%s%s %s\r\n"
                                          "%s %s\r\n",
                                          tempBlastResponse, BLAST_FEATURES_RSP_HEADER_STRING,
                                          blastFeaturesValue, OLD_BLAST_FEATURES_RSP_HEADER_STRING,
                                          blastFeaturesValue);
      } else {
         blastResponse = Str_SafeAsprintf(NULL, "%s%s %s\r\n", tempBlastResponse,
                                          BLAST_FEATURES_RSP_HEADER_STRING, blastFeaturesValue);
      }
      free(tempBlastResponse);
      tempBlastResponse = NULL;
   } else {
      VVCSESSIONMGRLOG("Missing Feature Allow List in Blast Headers");
   }

   if (blastSocketContext->params.udpEnabled) {
      udpHeadersPresent =
         BlastSocketBuildUDPResponse(httpRequest, &udpResponse, blastSocketContext, wsPeerConfig);
   }

   /*
    * (1) Blast Headers and UDP Headers both present in the httpRequest - send
    *     responses for both in httpResponse.
    *
    * (2) Blast Header is found in httpRequest but not udpHeaders - send
    *     response for Blast Header only.
    *
    * (3) Blast Header is Not found in httpRequest but udpHeaders are found -
    *     send response for UDP Header only.
    *
    * (4) Do not send any httpResponse if both Blast and UDP Headers are not
    *     found.
    */

   if (blastHeadersPresent && udpHeadersPresent) {
      *httpResponse = Str_SafeAsprintf(NULL, "%s%s", udpResponse, blastResponse);
   } else if (blastHeadersPresent && !udpHeadersPresent) {
      // use blastResponse buffer directly, do not free it before returning
      *httpResponse = blastResponse;
      blastResponse = NULL;
   } else if (!blastHeadersPresent && udpHeadersPresent) {
      // use udpResponse buffer directly, do not free it before returning
      *httpResponse = udpResponse;
      udpResponse = NULL;
   } else {
      VVCSESSIONMGRLOG("Blast & UDP Headers both not found in WSUpgradeReq, "
                       "Not putting any Response.");
   }

   free(aOutV4QoSPolicyParamsStr);
   free(cOutV4QoSPolicyParamsStr);
   free(bUpV4QoSPolicyParamsStr);
   free(bDownV4QoSPolicyParamsStr);
   free(aOutV6QoSPolicyParamsStr);
   free(cOutV6QoSPolicyParamsStr);
   free(bUpV6QoSPolicyParamsStr);
   free(bDownV6QoSPolicyParamsStr);
   free(mptVersionStr);
   free(deferredAcksParamsStr);
   free(udpResponse);
   free(blastResponse);
   free(blastHopValue);
   free(blastE2EValue);
   free(blastFeaturesValue);
   free(blastHopOldValue);
   free(blastE2EOldValue);
   free(blastFeaturesOldValue);

   VVCSESSIONMGRLOG("Handled Blast related parts of the websocket upgrade");
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketHandleUpgradeRequest --
 *
 *      Handle websocket http upgrade request. Authentication will be done
 *      firstly, if authenticated, a newly cookie string will be generated,
 *      and then parse the uri and set the cookie into http header to be sent
 *      as upgrade response.
 *
 * Results:
 *      Return ASOCKERR_SUCCESS when sccess, ASOCKERR_GENERIC otherwise.
 *
 * Side effects:
 *      cookieOut will be freed as memory is allocated in heap from
 *      authentication callback.
 *
 *      Caller is responsible for freeing outputed httpResponse if it's
 *      not NULL.
 *
 *----------------------------------------------------------------------------
 */

int
BlastSocketHandleUpgradeRequest(AsyncSocket *asock,      // IN
                                void *clientData,        // IN
                                const char *httpRequest, // IN
                                char **httpResponse)     // IN/OUT
{
   BlastSocketContext *blastSocketContext = clientData;
   int ret;
   void *cbFuncClientData;
   char *path = NULL;
   char *vAuth = NULL;
   char *route = NULL;
   char *cookie = NULL;
   char *cookieOut = NULL;
   BlastSocketWSPeerConfig *wsPeerConfig = NULL;
   /*
    * Today, this path is always for control asock only. If this were to
    * change, the logic to determine if this a control asock needs to be used
    * to set the below flag.
    */
   Bool isControlAsock = TRUE;

   ASSERT(clientData != NULL);

   VVCSESSIONMGRLOG("Handle websocket upgrade request.");

   ret = ASOCKERR_GENERIC;
   cbFuncClientData = blastSocketContext->callbacks.cbFuncClientData;
   vAuth = BlastSocketGetWSVAuth(asock);
   route = BlastSocketGetRouteSpecifier(asock);

   if (NULL == vAuth || NULL == route) {
      VVCSESSIONMGRLOG("Failed to get authentication params from URI.");
      goto exit;
   }

   cookie = BlastSocketGetReconnectCookieFromURI(AsyncSocket_GetWebSocketURI(asock));

   cookieOut =
      BlastSocketProcessAuthRequest(vAuth, route, cookie, blastSocketContext, isControlAsock);

   if (cookieOut == NULL) {
      VVCSESSIONMGRLOG("Authentication failed.");
      goto exit;
   }

   path = BlastSocketGetWebSocketPathFromUri(AsyncSocket_GetWebSocketURI(asock));
   if (path == NULL) {
      VVCSESSIONMGRLOG("Failed to get Path from websocket request.");
      goto exit;
   }

   ret = AsyncSocket_SetWebSocketCookie(asock, cbFuncClientData, path, cookieOut);

   wsPeerConfig = (BlastSocketWSPeerConfig *)Util_SafeCalloc(1, sizeof *wsPeerConfig);
   ASSERT(wsPeerConfig);

   memset(wsPeerConfig, 0, sizeof *wsPeerConfig);
   wsPeerConfig->vAuth = strdup(vAuth);
   wsPeerConfig->localMptVersion =
      blastSocketContext->params.localNCEnabled ? VVC_MPT_VERSION_MAX : VVC_MPT_VERSION_MIN;
   wsPeerConfig->blastSocketContext = blastSocketContext;
   wsPeerConfig->peerFeatureAllowList = NULL;
   wsPeerConfig->localTcpBweVersion = blastSocketContext->params.bweEnabled
                                         ? ASYNC_BWE_SOCKET_VERSION_DEFAULT
                                         : ASYNC_BWE_SOCKET_VERSION_NONE;

   BlastSocketBuildBlastWSResponse(httpRequest, httpResponse, blastSocketContext, wsPeerConfig);

   /*
    * Put the BlastSocketWSPeerConfig into into wsPeerConfigMap.
    * Subsequently in PrimaryWSConnectCb, "consume" this WSPeerConfig.
    */

   LOCK_WSPEERCONFIGMAP(blastSocketContext);
   BlastSocketAddWSPeerConfigMapEntry(blastSocketContext, vAuth, wsPeerConfig);
   UNLOCK_WSPEERCONFIGMAP(blastSocketContext);

exit:
   free(cookie);
   free(cookieOut);
   free(path);
   free(vAuth);
   free(route);
   return ret;
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketProcessWebSocketConnection --
 *
 *      If required, finish doing VVC setup. Fire the callback to the
 *      application.
 *
 * Results:
 *      Return TRUE if success.
 *
 *----------------------------------------------------------------------------
 */

static Bool
BlastSocketProcessWebSocketConnection(AsyncSocket *asock,  // IN
                                      void *clientData,    // IN
                                      int32 *vvcSessionID) // OUT
{
   char *vAuth;
   void *cbFuncClientData;
   int32 vvcSessionId = -1;
   int32 shadowInstanceId = -1;
   BlastSocketContext *blastSocketContext = clientData;
   BlastSocketWSPeerConfig wsPeerConfig;
   ASSERT(clientData != NULL);
   ASSERT(vvcSessionID != NULL);

   vAuth = NULL;
   cbFuncClientData = blastSocketContext->callbacks.cbFuncClientData;
   VVCSESSIONMGRLOG("BlastSocketProcessWebSocketConnection: Start.");

   vAuth = BlastSocketGetWSVAuth(asock);

   // Get the WSPeerConfig for given vAuth and pass that over to SetupSession.
   wsPeerConfig = BlastSocketConsumeWSPeerConfig(blastSocketContext, vAuth);

   if (wsPeerConfig.negotiatedTcpBweVersion != ASYNC_BWE_SOCKET_VERSION_NONE) {
      AsyncSocket *asockWrap;
      AsyncBweSocketOptionsStatic opts = AsyncBweSocketOptionsStatic_CreateDefault();
      /*
       * Since BWE socket keepalives are based on send inactivity, it
       * is sufficient to set keepalive interval to be half of keepalive
       * timeout.
       */
      opts.keepAliveInterval = TRANSPORT_KEEPALIVE_TIMEOUT_SEC / 2;
      opts.keepAliveTimeout = TRANSPORT_KEEPALIVE_TIMEOUT_SEC;
      opts.version = wsPeerConfig.negotiatedTcpBweVersion;
      asockWrap = AsyncSocket_BweWrap(asock, &opts);
      if (asockWrap != NULL) {
         VVCSESSIONMGRLOG("Start: Bwe wrap asock %p to %p", asock, asockWrap);
         /* set any socket options on wrapped socket */
         AsyncSocket_SetCloseOptions(asockWrap, ASYNCSOCKET_FLUSH_TIMEOUT_MSEC, NULL);
         AsyncSocket_UseNodelay(asockWrap, TRUE);
         asock = asockWrap;
      } else {
         // Bwe Wrap failed
         VVCSESSIONMGRLOG("Start: Bwe wrap failed asock %p", asock);
         free(vAuth);
         return FALSE;
      }
   }

   if (FALSE == BlastSocketSetupSession(asock, FALSE, blastSocketContext, vAuth, wsPeerConfig,
                                        &vvcSessionId, &shadowInstanceId)) {
      VVCSESSIONMGRLOG("Failed to Setup VvcSession.");
      free(vAuth);
      return FALSE;
   }
   VVCSESSIONMGRLOG("Done with Setup Session with "
                    "VvcSessionId: %d, shadowInstanceId: %d, "
                    "vAuth:%" MASK_TOKEN ".",
                    vvcSessionId, shadowInstanceId, vAuth);

   *vvcSessionID = vvcSessionId;
   free(vAuth);
   return TRUE;
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocket_SubprotocolImpliesNC --
 *
 *      Decide whether the given subprotocol string indicates that
 *      Network Continuity should be activated within the VVC instance
 *      that terminates the websocket connection.
 *
 * Results:
 *      TRUE if the subprotocol indicates NC use, FALSE otherwise
 *
 *----------------------------------------------------------------------------
 */

Bool
BlastSocket_SubprotocolImpliesNC(const char *subprotocol) // IN
{
   ASSERT(NULL != subprotocol);

   return ((0 == strcmp(BLAST_SOCKET_WEBSOCKET_SUBPROTOCOL_VVC_WITH_CONTINUITY, subprotocol)) ||
           (0 == strcmp(BLAST_SOCKET_WEBSOCKET_SUBPROTOCOL_VVC_CONTINUITY_LEGACY, subprotocol)));
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocket_IsNegotiatedNCEnabled --
 *
 *      Returns if the VvcSession corresponding to given vAuth has
 *      negotiatedNCEnabled or not
 *
 * Results:
 *      TRUE if negotiatedNCEnabled, FALSE otherwise
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

Bool
BlastSocket_IsNegotiatedNCEnabled(BlastSocketContext *blastSocketCtx, // IN
                                  const char *vAuth)                  // IN
{
   return BlastSocketIsNegotiatedNCEnabled(blastSocketCtx, vAuth);
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocket_IsNegotiatedBENITEnabled --
 *
 *      Returns if the VvcSession corresponding to given vAuth has
 *      negotiatedBENITEnabled or not
 *
 * Results:
 *      TRUE if negotiatedBENITEnabled, FALSE otherwise
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

Bool
BlastSocket_IsNegotiatedBENITEnabled(BlastSocketContext *blastSocketCtx, // IN
                                     const char *vAuth)                  // IN
{
   return BlastSocketIsNegotiatedBENITEnabled(blastSocketCtx, vAuth);
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocket_SetVvcBlastSessionMgrCallbacks --
 *
 *      Set BlastSessionMgr callbacks in VVCLIB
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

void
BlastSocket_SetVvcBlastSessionMgrCallbacks(const BlastSessionMgrCallbacks *cb) // IN
{
   VVCLIB_SetBlastSessionMgrCallbacks(cb);
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocket_GetVvcQualityIndicators --
 *
 *      Retrieve network stats and evaluate network quality stats
 *
 * Results:
 *      TRUE if success, FALSE otherwise.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

Bool
BlastSocket_GetVvcQualityIndicators(const BlastSocketContext *blastSocketCtx, int32 vvcSessionId,
                                    VvcNetworkIndicatorStats *networkStats)
{
   Bool ret = FALSE;

   ASSERT(blastSocketCtx);
   ASSERT(networkStats);

   if (0 == blastSocketCtx->params.networkQualityStatsConfig.networkStatsCheckEnabled) {
      VVCSESSIONMGRLOG("VVC network quality indicators are disabled.");
      goto exit;
   }

   {
      VvcStatus status;
      VvcSessionInfo vvcSessionInfo;
      size_t infoLen = sizeof(VvcSessionInfo);

      status = VVCLIB_GetInfo(VvcInfoSessionInfo, 0, &vvcSessionId, sizeof(vvcSessionId),
                              &vvcSessionInfo, &infoLen);
      if (status != VVC_STATUS_SUCCESS) {
         VVCSESSIONMGRLOG("Failed to call VVCLIB_GetInfo for vvcSessionId: %d", vvcSessionId);
         goto exit;
      }

      if (vvcSessionInfo.state != VvcSessionEstablished) {
         VVCSESSIONMGRLOG("Unexpected VVC session state: %s",
                          VvcDebugSessionStateToString(vvcSessionInfo.state));
         goto exit;
      }

      {
         BlastSocketNetworkStats networkQualityStats = {0};

         networkQualityStats.config = blastSocketCtx->params.networkQualityStatsConfig;

         /* Evaluate and fill in network stats structure if an update is needed */
         if (BlastSocketNetworkStatsDetect_EvaluateNetworkStats(&vvcSessionInfo,
                                                                &networkQualityStats)) {
            *networkStats = networkQualityStats.vvcNetworkStats;

            VVCSESSIONMGRLOG(
               "Backend: %s. qualityState: %s. qualityScore: %d. "
               "indicatorCount: %d. rtt: %.2fms (score: %d), "
               "pkloss: %.2f%% (score: %d), rttv: %.2fms (score: %d)",
               VvcDebugDumpNetworkTransportTypeToString(networkStats->localActiveTransportType),
               VvcDebugDumpNetworkQualityStateToString(networkStats->qualityState),
               networkQualityStats.lastQualityScore, networkQualityStats.indicatorCount,
               networkStats->indicators[VVC_NETWORK_INDICATOR_RTT],
               networkQualityStats.indicatorScore[VVC_NETWORK_INDICATOR_RTT],
               networkStats->indicators[VVC_NETWORK_INDICATOR_PACKETLOSS],
               networkQualityStats.indicatorScore[VVC_NETWORK_INDICATOR_PACKETLOSS],
               networkStats->indicators[VVC_NETWORK_INDICATOR_RTTV],
               networkQualityStats.indicatorScore[VVC_NETWORK_INDICATOR_RTTV]);

            ret = TRUE;
         }
      }
   }

exit:
   return ret;
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketAcceptConnection --
 *
 *      Gets the AsyncSocket. Process the socket if TCP. Else if UDP, and also
 *      more recently with TCP raw channels, we need to get additional data
 *      from the client.
 *
 * Results:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

void
BlastSocketAcceptConnection(Bool isWebSocket,   // IN
                            AsyncSocket *asock, // IN
                            void *clientData,   // IN
                            int serialNo)       // IN
{
   int32 vvcSessionID;
   ASSERT(clientData != NULL);

   VVCSESSIONMGRLOG("Start: isWebSocket %d", isWebSocket);

   if (isWebSocket) {
      /*
       * Always invoke BlastSocketProcessWebSocketConnection().
       * It will setup VvcSession and put an entry in sessionMap.
       *
       * For BEAT, the acceptFn to the application will be fired
       * from BlastSocketCompleteGetParamsCB.
       * For TCP (fallback and udpDisabled case), the acceptFn to
       * the application will be fired from BlastSocketDataSockActivatedCb.
       */

      if (BlastSocketProcessWebSocketConnection(asock, clientData, &vvcSessionID) != TRUE) {
         VVCSESSIONMGRLOG("Error on processing websocket connection. "
                          "Closing websocket - %p",
                          asock);
         AsyncSocket_Close(asock);
         return;
      }

   } else {
      /*
       * For UDP FEC and TCP raw connections, the Authentication information is
       * obtained from the client.
       */
      VVCSESSIONMGRLOG("Not WebSocket, invoke GetParams for TLV exchange");
      AsyncSocket_SetErrorFn(asock, BlastSocketParamsError, clientData);
      BlastSetup_GetParams(asock, BlastSocketCompleteGetParamsCB, clientData, serialNo);
   }
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketInitVvc --
 *
 *      Initialize Main VVC Instance. Also enables VVC perf counters
 *      based on blastSocket params.
 *
 * Results:
 *      VvcStatus
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

VvcStatus
BlastSocketInitVvc(BlastSocketContext *blastSocketCtx)
{
   uint32 flags = 0;
   VvcStatus vvcStatus;
   VvcInstanceBackend instanceBe;

   ASSERT(blastSocketCtx);

   if (blastSocketCtx->instanceHandle) {
      VVCSESSIONMGRLOG("Already initialized VVCInstance:%p.", blastSocketCtx->instanceHandle);
      return VVC_STATUS_SUCCESS;
   }

   flags |= VVC_INSTANCE_MAIN | VVC_INSTANCE_DEFER_EVENT_DISPATCH_THREAD |
            VVC_INSTANCE_DEFER_SEND_DISPATCH_THREAD | VVC_INSTANCE_WAIT_FOR_EVENT_THREAD_EXIT;

   if (blastSocketCtx->params.isVvcPluginLoaderNeeded) {
      flags |= VVC_INSTANCE_ENABLE_LOADER;
   }

   if (blastSocketCtx->params.perfMonEnabled) {
      flags |= VVC_INSTANCE_ENABLE_PERF_COUNTERS;
   }

   if (blastSocketCtx->params.blastSocketThreadEnabled) {
      instanceBe.pollCallback = VvcNetPollCallbackCb;
      instanceBe.pollRemove = VvcNetPollRemoveCb;
   } else {
      instanceBe.pollCallback = VvcPollCallbackCb;
      instanceBe.pollRemove = VvcPollRemoveCb;
   }

#ifdef _WIN32
   instanceBe.perfProviderGuid = vvcPerfProviderGuid;
   instanceBe.perfCounterGuid = vvcPerfCounterSetGuid;
#endif
   vvcStatus = VVCLIB_Init(flags, NULL, "main", &instanceBe, 0, &blastSocketCtx->instanceHandle);
   if (!VVC_SUCCESS(vvcStatus)) {
      VVCSESSIONMGRLOG("Failed to initialize VVCInstance, VVCStatus:%d.", vvcStatus);
   } else {
      VVCSESSIONMGRLOG("Initialized VVCInstance:%p.", blastSocketCtx->instanceHandle);
   }

   return vvcStatus;
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketUninitVvc --
 *
 *      Un-initialize Main VVC Instance.
 *
 *  Results:
 *
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

VvcStatus
BlastSocketUninitVvc(BlastSocketContext *blastSocketCtx)
{
   VvcStatus vvcStatus;

   ASSERT(blastSocketCtx);

   if (!blastSocketCtx->instanceHandle) {
      VVCSESSIONMGRLOG("VVCInstance held by BlastSocket already "
                       "un-initialized.");
      return VVC_STATUS_SUCCESS;
   }

   vvcStatus = VVCLIB_Uninit(blastSocketCtx->instanceHandle);

   if (!VVC_SUCCESS(vvcStatus)) {
      VVCSESSIONMGRLOG("Failed to un-initialize VVCInstance, VVCStatus:%d.", vvcStatus);
   } else {
      VVCSESSIONMGRLOG("Un-initialized VVCInstance:%p.", blastSocketCtx->instanceHandle);
      blastSocketCtx->instanceHandle = NULL;
   }

   return vvcStatus;
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketStartVvcSession --
 *
 *      Do VVCLIB_OpenSession. Use errorCb & destroyCb given by application.
 *      Do VVCLIB_StartSession.
 *
 * Results:
 *      VvcStatus indicating if VvcSession was setup or not.
 *      Also, the OUT param VvcSessionHandle will be pointing to created
 *      VvcSession.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

VvcStatus
BlastSocketStartVvcSession(BlastSocketContext *blastSocketCtx, // IN
                           int32 vvcSessionId,                 // IN
                           EFIGUID smConnectionId,             // IN
                           const char *vAuth,                  // IN
                           VvcTransptBackend *transportBe,     // IN
                           AsyncSocket *asock,                 // IN
                           Bool isEndToEndConnection,          // IN
                           Bool negotiatedNCEnabled,           // IN
                           Bool negotiatedBENITEnabled,        // IN
                           uint8 negotiatedMptVersion,         // IN
                           uint32 negotiatedTcpBweVersion,     // IN
                           const char *peerFeatureAllowList)   // IN
{
   VvcStatus status = VVC_STATUS_SUCCESS;
   VvcSessionHandle vvcSessionHandle;
   VvcSessionEvents vvcSessionEvents;
   VvcSessionParams vvcSessionParams;
   VvcSessionEventsCbData *cbData;
   VvcInstanceHandle instanceHandle;
   BlastSocketVvcSessionWrapper *sessionWrapper = NULL;
   Bool ok = FALSE;
   Bool isNewVvcSessionNeeded = TRUE;
   VvcSessionId vvcSessionIdHandle = NULL;
   Bool haveSessionRef = FALSE;
   int vAuthCtrlAsockClaimCount = 0;

   uint32 nonce = 0;
   int lockCount = 0;

   ASSERT(blastSocketCtx);

   /* Release BlastSocketLock before calling any VVC APIs; also before
    * acquiring sessionmap exclusive lock to preserve the lock ordering.
    *
    * Reverse connection is known to have the BlastSocketLock locked
    * more than once recursively.
    */
   while (BlastSocket_IsLocked()) {
      BlastSocket_Unlock();
      lockCount++;
   }
   ASSERT(lockCount > 0 && lockCount <= 2);

   /*
    * Check for any existing VVCSession for this windows session
    * and if found any close the session before starting a new one.
    *
    * How are old VNCSession objects linked to an existing VVC
    * session destroyed ?
    * Closing a VVCSession will trigger a VvcMksOnChannelClose() which
    * calls VNCSession:Close(), which releases references and destroys
    * the VNCSession.
    * XXX: The above dependency Should be removed.
    *
    * How are old VvcSessions destroyed ?
    * VVCLIB_CloseSession() does not destroy session, it just puts
    * session in closing state and queues events to be triggered for all
    * listeners and channels to be closed and destroyed. Once the
    * VvcSession ref count reaches zero is when it will be destroyed.
    */
   instanceHandle = blastSocketCtx->instanceHandle;

   isNewVvcSessionNeeded =
      BlastSocketIsNewVvcSessionNeeded(blastSocketCtx, vvcSessionId, vAuth, &vvcSessionHandle);

   /*
    * Steps:
    *    1. Do VVCLIB_OpenSession()
    *    2. Create BlastSocketVvcSessionWrapper and add it to SessionMap
    *    3. Set the Min and Max Bw read from Blast config
    *       -- this requires an entry to be present in SessionMap
    *    4. Push Switch Policy to VvcSession - if applicable.
    *    5. Do VVCLIB_StartSession()
    *    6. Start VvcHub - if applicable.
    */

   if (isNewVvcSessionNeeded) {

      // Do VVCLIB_OpenSession()

      VVCSESSIONMGRLOG("Opening VVCSession using VVCSessionID:%d, "
                       "vAuth:%" MASK_TOKEN ", negotiatedNCEnabled: %s, "
                       "negotiatedBENITEnabled: %s, "
                       "negotiatedMPTVersion: %d.",
                       vvcSessionId, vAuth, negotiatedNCEnabled == TRUE ? "Yes" : "No",
                       negotiatedBENITEnabled == TRUE ? "Yes" : "No", negotiatedMptVersion);

      // Callback clientData structure is used for VvcSessionEventOnErrorCb
      cbData = Util_SafeCalloc(1, sizeof *cbData);
      cbData->clientData = (void *)blastSocketCtx;
      cbData->sessionId = vvcSessionId;
      cbData->sessionWrapper = NULL;

      memset(&vvcSessionEvents, 0, sizeof vvcSessionEvents);
      vvcSessionEvents.onEstablished = BlastSocketVvcSessionOnEstablishedCb;
      vvcSessionEvents.onError = BlastSocketVvcSessionOnErrorCb;
      vvcSessionEvents.onDestroy = BlastSocketVvcSessionOnDestroyCb;
      vvcSessionEvents.onClose = BlastSocketVvcSessionOnCloseCb;

      memset(&vvcSessionParams, 0, sizeof vvcSessionParams);
      vvcSessionParams.enableVVCCloseSeq = blastSocketCtx->params.localVVCCloseSeqEnabled;
      vvcSessionParams.enableVVCPauseResume = blastSocketCtx->params.localVVCPauseResumeEnabled;
      vvcSessionParams.enableVVCQoSPolicy = blastSocketCtx->params.localVVCQoSPolicyEnabled;
      vvcSessionParams.deferredAcksParams =
         Util_SafeCalloc(1, sizeof *vvcSessionParams.deferredAcksParams);
      *vvcSessionParams.deferredAcksParams = blastSocketCtx->deferredAcksParams;
      vvcSessionParams.enableVVCBatching = blastSocketCtx->params.localVVCBatchingEnabled;
      vvcSessionParams.disableVvcRawChannels = blastSocketCtx->params.disableVvcRawChannels;

      /*
       * Enable (degenerate) Multi-Protocol
       * When the Multi-Protocol flag is set, at least one AsyncSocket must be
       * pushed to VVC.
       */
      transportBe->flags |= VVC_TRANSPORT_BE_ENABLE_MULTI_PROTOCOL;

      /*
       * Set Network Continuity flag of transportBe if required.
       */
      if (negotiatedNCEnabled) {
         transportBe->flags |= VVC_TRANSPORT_BE_ENABLE_NW_CONTINUITY;
         vvcSessionParams.mptVersion =
            negotiatedMptVersion ? negotiatedMptVersion : VVC_MPT_VERSION_DEFAULT;

         /*
          * If NC is enabled, Set Network Intelligence flag of transportBe
          * if required.
          */
         if (negotiatedBENITEnabled) {
            transportBe->flags |= VVC_TRANSPORT_BE_ENABLE_NW_INTELLIGENCE;
         }
      }
      status = VVCLIB_OpenSession(instanceHandle, transportBe, vvcSessionId,
                                  0, // UNUSED
                                  vvcSessionParams, &vvcSessionEvents,
                                  (void *)(cbData), // clientData
                                  &vvcSessionHandle);

      free(vvcSessionParams.deferredAcksParams);

      if (!VVC_SUCCESS(status)) {
         VVCSESSIONMGRLOG("Unable to open VVCSession "
                          "with VVCSessionID:%d, VVCStatus:%d.",
                          vvcSessionId, status);
         goto out;
      }

      status = VVCLIB_AddRefSession(vvcSessionHandle);
      if (!VVC_SUCCESS(status)) {
         VVCSESSIONMGRLOG("Session with sessionId: %d, "
                          "sessionHandle: 0x%p does not exist",
                          vvcSessionId, vvcSessionHandle);
         goto out;
      }

      haveSessionRef = TRUE;

      vvcSessionIdHandle = VVCLIB_GetVvcSessionId(vvcSessionHandle);
      if (!vvcSessionIdHandle) {
         status = VVC_STATUS_NOT_FOUND;
         goto out;
      }

      VVCSESSIONMGRLOG("Opened VVCSession:%p with VVCSessionID:%d."
                       "VVCSessionIdHandle: %p",
                       vvcSessionHandle, vvcSessionId, vvcSessionIdHandle);

      // Create BlastSocketVvcSessionWrapper and add it to SessionMap

      sessionWrapper = BlastSocketCreateSessionWrapper(
         vvcSessionId, vvcSessionIdHandle, vvcSessionHandle, smConnectionId, negotiatedNCEnabled,
         negotiatedBENITEnabled, vAuth, blastSocketCtx);

      LOCK_SESSIONMAP(blastSocketCtx);

      cbData->sessionWrapper = sessionWrapper;

      ok = BlastSocketAddSessionMapEntry(blastSocketCtx, vAuth, sessionWrapper);

      if (!ok) {
         VVCSESSIONMGRLOG("SessionMap_AddEntry Failed for vAuth:%" MASK_TOKEN ", sessionId: %d",
                          vAuth, vvcSessionId);

         UNLOCK_SESSIONMAP(blastSocketCtx);
         status = VVC_STATUS_ERROR;
         goto out;
      }

      // Set the Min and Max Bw now
      status = BlastSocketSetVvcMinMaxBw(blastSocketCtx, vAuth, blastSocketCtx->params.minimumRate,
                                         blastSocketCtx->params.maximumRate,
                                         blastSocketCtx->params.maxBandwidthBurstMsec);

      if (!VVC_SUCCESS(status)) {
         UNLOCK_SESSIONMAP(blastSocketCtx);
         VVCSESSIONMGRLOG("Failed to set MinMax Bw, status: %d", status);
         goto out;
      }

      if (negotiatedBENITEnabled) {

         ok = BlastSocketPushTransportSwitchPolicyToVvcSession(blastSocketCtx, vvcSessionHandle);

         if (!ok) {
            VVCSESSIONMGRLOG("Failed to push Transport Switch Policy Params "
                             "for vAuth:%" MASK_TOKEN ", sessionId: %d",
                             vAuth, vvcSessionId);
            UNLOCK_SESSIONMAP(blastSocketCtx);
            status = VVC_STATUS_ERROR;
            goto out;
         } else {
            VVCSESSIONMGRLOG("Pushed Transport Switch Policy Params to "
                             "VvcSession for vAuth:%" MASK_TOKEN ", "
                             "sessionId: %d",
                             vAuth, vvcSessionId);
         }
      }

      if (blastSocketCtx->params.localVVCQoSPolicyEnabled) {
         ok = BlastSocketPushQoSPolicyToVvcSession(blastSocketCtx, vvcSessionHandle);

         if (!ok) {
            VVCSESSIONMGRLOG("Failed to push QoS Policy Params "
                             "for vAuth:%" MASK_TOKEN ", sessionId: %d",
                             vAuth, vvcSessionId);
            UNLOCK_SESSIONMAP(blastSocketCtx);
            status = VVC_STATUS_ERROR;
            goto out;
         } else {
            VVCSESSIONMGRLOG("Pushed QoS Policy Params to "
                             "VvcSession for vAuth:%" MASK_TOKEN ", "
                             "sessionId: %d",
                             vAuth, vvcSessionId);
         }
      }

      UNLOCK_SESSIONMAP(blastSocketCtx);

   } else {
      // isNewVvcSessionNeeded is FALSE
      LOCK_SESSIONMAP(blastSocketCtx);

      if (FALSE == BlastSocketGetSessionMapEntry(blastSocketCtx, vAuth, &sessionWrapper)) {
         VVCSESSIONMGRLOG("isNewVvcSessionNeeded is FALSE but couldn't fetch "
                          "the sessionWrapper");
         UNLOCK_SESSIONMAP(blastSocketCtx);
         status = VVC_STATUS_ERROR;
         goto out;
      }
      UNLOCK_SESSIONMAP(blastSocketCtx);
   }

   // Update Network Perf Counter using vAuthCtrlAsockClaimCount
   vAuthCtrlAsockClaimCount = BlastSocketGetViewAuthCtrlAsockClaimCount(vAuth, blastSocketCtx);
   if (vAuthCtrlAsockClaimCount > 0) {
      sessionWrapper->reconnectCount = vAuthCtrlAsockClaimCount - 1;
      if (vAuthCtrlAsockClaimCount > 1) {
         VVCSESSIONMGRLOG("Network Recovery detected for vAuth: %" MASK_TOKEN
                          " and sessionId: %d. Session Reconnect Counter: %d",
                          vAuth, vvcSessionId, sessionWrapper->reconnectCount);
      } else {
         VVCSESSIONMGRLOG("New Connection for vAuth: %" MASK_TOKEN " and "
                          "sessionId: %d. Session Reconnect Counter: %d",
                          vAuth, vvcSessionId, sessionWrapper->reconnectCount);
      }
   } else {
      VVCSESSIONMGRLOG("Did not set Session Reconnect Counter. vAuth: %" MASK_TOKEN
                       ", sessionId: %d, "
                       "vAuthCtrlSockClaimCount: %d",
                       vAuth, vvcSessionId, vAuthCtrlAsockClaimCount);
   }

#ifdef _WIN32
   nonce = BlastSocketGenerateWorkerNonce(
      vAuth, AsyncSocket_GetPort(blastSocketCtx->webSocketListenSocket),
      BlastSocketGetCurrentProcessId());
#endif

   if (!BlastSocketIsShadowSession(vAuth, blastSocketCtx)) {
      ok = BlastSocketPushPeerAllowListToVvc(peerFeatureAllowList);

      if (!ok) {
         VVCSESSIONMGRLOG("Failed to push peer Feature Allow List");
         status = VVC_STATUS_ERROR;
      }
   } else {
      VVCSESSIONMGRLOG("Not pushing Peer Allow List for Shadow Session");
   }

   /*
    * Save the negotiated tcpBweVersion for potential raw channel connect later
    */
   sessionWrapper->negotiatedTcpBweVersion = negotiatedTcpBweVersion;

   /*
    * The Asock that gets pushed to Vvc via BlastSocketStartVvcSession()
    * will be treated as "Control" Socket always.
    *
    * If Asock is pushed from elsewhere (from BlastSocketCompleteGetParamsCB
    * for example), then it will be pushed as a non-Control Socket.
    * Vvc in turn will decide which socket to mark as "Data" depending on
    * which one it sees the data traffic on.
    */

   ok = BlastSocketPushAsockToVvcSession(sessionWrapper, vvcSessionHandle, asock, TRUE,
                                         isEndToEndConnection,
                                         blastSocketCtx->params.blastSocketThreadEnabled, nonce, 0);

   if (!ok) {
      VVCSESSIONMGRLOG("Failed to Push Asock: %p to VvcSession: %p", asock, vvcSessionHandle);
      status = VVC_STATUS_ERROR;
      goto out;
   }

   VVCSESSIONMGRLOG("Pushed Asock: %p to VvcSession: %p", asock, vvcSessionHandle);

   if (isNewVvcSessionNeeded) {
      Bool skipVvcHubStart = FALSE;

      /*
       * For Linux VDI, because of missing support for RX features
       * in collab (shadow) sessions, we need to disable VVC Hub.
       * TODO: Remove this workaroud when Linux VDI and Collab team
       *       adds the missing support.
       */
#ifndef _WIN32
      skipVvcHubStart = BlastSocketIsShadowSession(vAuth, blastSocketCtx);
#endif

      // Start the Session now
      status = VVCLIB_StartSession(vvcSessionHandle);

      if (!VVC_SUCCESS(status)) {
         VVCSESSIONMGRLOG("Unable to start "
                          "VVCSession:%p with VVCSessionID:%d, VVCStatus:%d.",
                          vvcSessionHandle, vvcSessionId, status);
         goto out;
      }

      VVCSESSIONMGRLOG("Started VVCSession:%p with "
                       "VVCSessionID:%d, VVCSessionIdHandle: %p",
                       vvcSessionHandle, vvcSessionId, vvcSessionIdHandle);

      if (!skipVvcHubStart) {
         VVCSESSIONMGRLOG("Triggering BlastSocketStartVvcHub() ... ");
         BlastSocketStartVvcHub(sessionWrapper);
      }

   } // isNewVvcSessionNeeded

out:
   if (haveSessionRef) {
      VVCLIB_ReleaseSession(vvcSessionHandle);
   }

   /* Relock BlastSocketLock */
   while (lockCount--) {
      BlastSocket_Lock();
   }

   return status;
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketStopVvcSession --
 *
 *      If a Network failure has happened and Session Persistence has been
 *      negotiated (only via a BlastSocketParams flag for now), then
 *      preserve the session and do nothing.
 *      Otherwise take all of the following actions:
 *      Just do VVCLIB_CloseSession if forceClose = false.
 *      Otherwise do CancelSafeSendIO as well.
 *      Remove the entry from BlastSocketContext::sessionMap freeing up
 *      BlastSocketVvcSessionWrapper.
 *      The actual VvcSession object though will be cleaned up by VVCLIB
 *      when this session's sendTree is completely purged.
 *
 * Results:
 *      Return VvcStatus.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

VvcStatus
BlastSocketStopVvcSession(BlastSocketContext *blastSocketCtx, // IN
                          int32 vvcSessionId,                 // IN
                          const char *vAuth,                  // IN
                          VDPConnectionResult reason)         // IN
{
   BlastSocketVvcSessionWrapper *sessionWrapper = NULL;
   VvcSessionHandle sessionHandle;
   VvcSessionId vvcSessionIdHandle;
   Bool vvcHubActive = FALSE;
   int32 sessionId = VVC_CURRENT_SESSION;
   Bool ok = FALSE;
   char *cookie = NULL;
   Bool isNegotiatedNCEnabled = FALSE;
   Bool isVvcCloseSeqEnabled = FALSE;
   Bool isNetworkFailure = FALSE;
   VvcSessionCloseParams closeParams;

   if (!blastSocketCtx) {
      VVCSESSIONMGRLOG("Invalid BlastSocketContext for VVCSessionID: %d, "
                       "vAuth:%" MASK_TOKEN ".Failed to stop vvc session.",
                       vvcSessionId, vAuth ? vAuth : "");
      return VVC_STATUS_INVALID_ARGS;
   }

   ASSERT(blastSocketCtx->sessionMap);

   /*
    * Steps:
    *    1. Make sure that SessionMap has the entry for this Session.
    *    2. Check if the reason for stop is a network failure.
            Do not stop VVC session and return if NC is negotiated and there
            is network failure, else go to next step.
    *    3. Extract & save the sessionHandle if (1) is TRUE
    *    4. Remove the sessionWrapper entry from SessionMap
    *    5. Destroy the sessionWrapper
    *    6. Close the VvcHub if applicable.
    *    7. Close the VvcSession. Optionally do CancelIO if forceClose config
    *       is true.
    *
    * Note: Steps 1, 2, 3, 4 & 5 require SessionMap to be locked.
    */

   LOCK_SESSIONMAP(blastSocketCtx);

   ok = BlastSocketGetSessionMapEntry(blastSocketCtx, vAuth, &sessionWrapper);

   if (!ok) {
      VVCSESSIONMGRLOG("Entry for VVCSessionID: %d, vAuth:%" MASK_TOKEN
                       " is not present in SessionMap",
                       vvcSessionId, vAuth);

      UNLOCK_SESSIONMAP(blastSocketCtx);
      return VVC_STATUS_ERROR;
   }

   isNegotiatedNCEnabled = sessionWrapper->negotiatedNCEnabled;

   isNetworkFailure = sessionWrapper->isNetworkFailure && (reason == VDPCONNECT_CONNECTION_LOST ||
                                                           reason == VDPCONNECT_NETWORK_FAILURE);

   if (isNegotiatedNCEnabled && isNetworkFailure) {
      VVCSESSIONMGRLOG("Network Failure, "
                       "Not closing VvcSession (VvcSessionID:%d). ",
                       vvcSessionId);
      UNLOCK_SESSIONMAP(blastSocketCtx);
      return VVC_STATUS_SUCCESS;
   }

   VVCSESSIONMGRLOG("BlastSocketStopVvcSession: Session with "
                    "sessionId: %d about to be Stopped, isNetworkFailure: %s.",
                    vvcSessionId, isNetworkFailure == TRUE ? "Yes" : "No");

   sessionHandle = sessionWrapper->sessionHandle;
   sessionId = sessionWrapper->vvcSessionId;
   vvcSessionIdHandle = sessionWrapper->vvcSessionIdHandle;
   isVvcCloseSeqEnabled = sessionWrapper->isVvcCloseSeqEnabled;
   vvcHubActive = sessionWrapper->isVvcHubActive;

   memset(&closeParams, 0, sizeof closeParams);
   if (isVvcCloseSeqEnabled == FALSE || reason == VDPCONNECT_TIMEOUT ||
       reason == VDPCONNECT_INVALID || reason == VDPCONNECT_FAILURE || isNetworkFailure) {
      closeParams.closeReason = reason;
      closeParams.closeConfig = VVC_CLOSE_CONFIG_FORCE_CLOSE;
   } else {
      closeParams.closeReason = reason;
      closeParams.closeConfig = VVC_CLOSE_CONFIG_FLUSH_AND_SEND_MSG;
   }

   if (closeParams.closeConfig == VVC_CLOSE_CONFIG_FORCE_CLOSE) {
      /*
       * vAuth gets invalidated by BlastSocket_InvalidateAuth() below,
       * so save a copy for removing the session map entry.
       */
      char *vAuthCopy = Util_SafeStrdup(vAuth);

      // Drop cookie
      cookie = BlastSocketGetCookieFromViewAuthToken(vAuth, blastSocketCtx);
      if (cookie) {
         BlastSocketDropCookie(cookie, blastSocketCtx);
      }

      /*
       * In NR 1.0 cases, cookie is dropped, new VVC session and sessionMap
       * entry is created, but make sure vAuth is not invalidated.
       */
      if (isNetworkFailure == FALSE) {

         // Reset state in session wrapper as well.
         sessionWrapper->isNetworkFailure = FALSE;
         BlastSocket_InvalidateAuth(vAuth, blastSocketCtx, reason);
      }

      ok = BlastSocketRemoveSessionMapEntry(blastSocketCtx, vAuthCopy);

      if (!ok) {
         VVCSESSIONMGRLOG("SessionMap_RemoveEntry Failed for vAuth:%" MASK_TOKEN ".", vAuthCopy);

         free(vAuthCopy);
         UNLOCK_SESSIONMAP(blastSocketCtx);
         return VVC_STATUS_ERROR;
      }

      free(vAuthCopy);
   }

   if (vvcHubActive) {
      sessionWrapper->isVvcHubActive = FALSE;
      sessionWrapper->deActivateVvcHub = FALSE;
      UNLOCK_SESSIONMAP(blastSocketCtx);
      /*
       * XXX: Investigate if BlastSocketStopVvcHub(and the subsequent
       *      VVC_HubUninit() that it calls can be done without to having to
       *      unlock sessionMap.
       */
      VVCSESSIONMGRLOG("Triggering BlastSocketStopVvcHub() ... ");
      BlastSocketStopVvcHub(vvcSessionIdHandle, sessionHandle, sessionId);
   } else {
      sessionWrapper->deActivateVvcHub = TRUE;
      UNLOCK_SESSIONMAP(blastSocketCtx);
   }

   // Close the VvcSession

   if (closeParams.closeConfig == VVC_CLOSE_CONFIG_FORCE_CLOSE) {
      if (VVC_STATUS_SUCCESS != VVCLIB_AddRefSession(sessionHandle)) {
         VVCSESSIONMGRLOG("BlastSocketStopVvcSession: Session with "
                          "sessionId: %d, sessionHandle: 0x%p does not exist",
                          vvcSessionId, sessionHandle);
         return VVC_STATUS_ERROR;
      }
   }

   VVCLIB_CloseSession(sessionHandle, &closeParams);

   if (closeParams.closeConfig == VVC_CLOSE_CONFIG_FORCE_CLOSE) {
      // Force cancel transport async IO
      VVCLIB_SessionTransportCancelIo(sessionHandle, VVC_SESSION_TP_CANCEL_SENDS |
                                                        VVC_SESSION_TP_CANCEL_RECV |
                                                        VVC_SESSION_TP_CANCEL_FORCE);

      VVCLIB_ReleaseSession(sessionHandle);
   }

   VVCSESSIONMGRLOG("BlastSocketStopVvcSession: Session with "
                    "sessionId: %d Stopped",
                    vvcSessionId);

   return VVC_STATUS_SUCCESS;
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketSetVvcMinMaxBw --
 *
 *      Set MinMax Bw for the VvcSession
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

VvcStatus
BlastSocketSetVvcMinMaxBw(BlastSocketContext *blastSocketCtx, // IN
                          const char *vAuth,                  // IN
                          double minimumRate,                 // IN
                          double maximumRate,                 // IN
                          double maxBandwidthBurstMsec)       // IN
{
   VvcStatus status;
   BlastSocketVvcSessionWrapper *sessionWrapper;
   VvcSessionHandle sessionHandle;
   Bool ok = FALSE;

   ASSERT(blastSocketCtx);
   ASSERT(blastSocketCtx->sessionMap);
   ASSERT(ISLOCKED_SESSIONMAP(blastSocketCtx));

   ok = BlastSocketGetSessionMapEntry(blastSocketCtx, vAuth, &sessionWrapper);

   if (!ok) {
      VVCSESSIONMGRLOG("Entry for vAuth:%" MASK_TOKEN " is not present in "
                       "SessionMap",
                       vAuth);

      return VVC_STATUS_ERROR;
   }

   sessionHandle = sessionWrapper->sessionHandle;

   status = VVCLIB_SetSessionConfig(sessionHandle, VVC_CONFIG_MIN_BANDWIDTH_RATE, &minimumRate,
                                    sizeof(minimumRate));

   if (!VVC_SUCCESS(status)) {
      VVCSESSIONMGRLOG("Unable to set minimum "
                       "bandwidth rate, VVCStatus:%d.",
                       status);
      return status;
   }

   status = VVCLIB_SetSessionConfig(sessionHandle, VVC_CONFIG_MAX_BANDWIDTH_RATE, &maximumRate,
                                    sizeof(maximumRate));

   if (!VVC_SUCCESS(status)) {
      VVCSESSIONMGRLOG("Unable to set maximum "
                       "bandwidth rate, VVCStatus:%d.",
                       status);
      return status;
   }

   status = VVCLIB_SetSessionConfig(sessionHandle, VVC_CONFIG_MAX_BANDWIDTH_BURST_MSEC,
                                    &maxBandwidthBurstMsec, sizeof(maxBandwidthBurstMsec));

   if (!VVC_SUCCESS(status)) {
      VVCSESSIONMGRLOG("Unable to set maximum bandwidth burst interval, "
                       "VVCStatus:%d.",
                       status);
      return status;
   }

   return VVC_STATUS_SUCCESS;
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketGetVvcSessionIdFromVAuth --
 *
 *      Given a vAuth, get the vvcSessionId for the vvcSession corresponding
 *      to that vAuth
 *
 * Results:
 *      Returns TRUE if VvcSessionId is found, FALSE otherwise.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

Bool
BlastSocketGetVvcSessionIdFromVAuth(BlastSocketContext *blastSocketCtx, // IN
                                    const char *vAuth,                  // IN
                                    int32 *vvcSessionId)                // OUT
{
   BlastSocketVvcSessionWrapper *sessionWrapper;
   Bool ok = FALSE;
   Bool isSessionMapLocked = FALSE;

   ASSERT(blastSocketCtx);
   ASSERT(blastSocketCtx->sessionMap);
   ASSERT(vAuth);

   if (!vvcSessionId) {
      VVCSESSIONMGRLOG("Unable to get sessionId, invalid args");
      return ok;
   }

   isSessionMapLocked = ISLOCKED_SESSIONMAP(blastSocketCtx);

   if (!isSessionMapLocked) {
      LOCK_SESSIONMAP(blastSocketCtx);
   }

   ok = BlastSocketGetSessionMapEntry(blastSocketCtx, vAuth, &sessionWrapper);

   if (ok) {
      *vvcSessionId = sessionWrapper->vvcSessionId;
   }

   if (!isSessionMapLocked) {
      UNLOCK_SESSIONMAP(blastSocketCtx);
   }

   if (!ok) {
      VVCSESSIONMGRLOG("SessionMap does not have entry for vAuth:%" MASK_TOKEN ".", vAuth);
      return ok;
   }

   VVCSESSIONMGRLOG("Returning vvcSessionId: %d for vAuth:%" MASK_TOKEN ".", *vvcSessionId, vAuth);

   return ok;
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketIsNegotiatedNCEnabled --
 *
 *      Given a vAuth, return if the corresponding VvcSession has Network
 *      Continuity Enabled or not/
 *
 * Results:
 *      Returns TRUE if negotiatedNCEnabled is TRUE, otherwise FALSE.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

Bool
BlastSocketIsNegotiatedNCEnabled(BlastSocketContext *blastSocketCtx, // IN
                                 const char *vAuth)                  // IN
{
   BlastSocketVvcSessionWrapper *sessionWrapper;
   Bool ok = FALSE;
   Bool isSessionMapLocked = FALSE;
   Bool negotiatedNCEnabled = FALSE;

   ASSERT(blastSocketCtx);
   ASSERT(blastSocketCtx->sessionMap);
   ASSERT(vAuth);

   isSessionMapLocked = ISLOCKED_SESSIONMAP(blastSocketCtx);

   if (!isSessionMapLocked) {
      LOCK_SESSIONMAP(blastSocketCtx);
   }

   ok = BlastSocketGetSessionMapEntry(blastSocketCtx, vAuth, &sessionWrapper);

   if (ok) {
      negotiatedNCEnabled = sessionWrapper->negotiatedNCEnabled;
   }

   if (!isSessionMapLocked) {
      UNLOCK_SESSIONMAP(blastSocketCtx);
   }

   if (!ok) {
      VVCSESSIONMGRLOG("SessionMap does not have entry for vAuth:%" MASK_TOKEN ".", vAuth);
      return ok;
   }

   VVCSESSIONMGRLOG("For vAuth:%" MASK_TOKEN ", negotiatedNCEnabled: %s", vAuth,
                    negotiatedNCEnabled ? "Yes" : "No");

   return negotiatedNCEnabled;
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketIsNetworkFailure --
 *
 *      Given a vAuth, return if the corresponding VvcSession has Network
 *      Failure.
 *
 * Results:
 *      Returns TRUE if there is a network failure, otherwise FALSE.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

Bool
BlastSocketIsNetworkFailure(BlastSocketContext *blastSocketCtx, const char *vAuth)
{
   BlastSocketVvcSessionWrapper *sessionWrapper;
   Bool ok = FALSE;
   Bool isSessionMapLocked = FALSE;
   Bool isNetworkFailure = FALSE;

   ASSERT(blastSocketCtx);
   ASSERT(blastSocketCtx->sessionMap);
   ASSERT(vAuth);

   isSessionMapLocked = ISLOCKED_SESSIONMAP(blastSocketCtx);

   if (!isSessionMapLocked) {
      LOCK_SESSIONMAP(blastSocketCtx);
   }

   ok = BlastSocketGetSessionMapEntry(blastSocketCtx, vAuth, &sessionWrapper);

   if (ok) {
      isNetworkFailure = sessionWrapper->isNetworkFailure;
   }

   if (!isSessionMapLocked) {
      UNLOCK_SESSIONMAP(blastSocketCtx);
   }

   if (!ok) {
      VVCSESSIONMGRLOG("SessionMap does not have entry for vAuth:%" MASK_TOKEN ".", vAuth);
      return ok;
   }

   VVCSESSIONMGRLOG("For vAuth:%" MASK_TOKEN ", isNetworkFailure: %s", vAuth,
                    isNetworkFailure ? "Yes" : "No");

   return isNetworkFailure;
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketIsNegotiatedBENITEnabled --
 *
 *      Given a vAuth, return if the corresponding VvcSession has Network
 *      Intelligence (aka BENIT) Enabled or not.
 *
 * Results:
 *      Returns TRUE if negotiatedBENITEnabled is TRUE, otherwise FALSE.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

Bool
BlastSocketIsNegotiatedBENITEnabled(BlastSocketContext *blastSocketCtx, // IN
                                    const char *vAuth)                  // IN
{
   BlastSocketVvcSessionWrapper *sessionWrapper;
   Bool ok = FALSE;
   Bool isSessionMapLocked = FALSE;
   Bool negotiatedBENITEnabled = FALSE;

   ASSERT(blastSocketCtx);
   ASSERT(blastSocketCtx->sessionMap);
   ASSERT(vAuth);

   isSessionMapLocked = ISLOCKED_SESSIONMAP(blastSocketCtx);

   if (!isSessionMapLocked) {
      LOCK_SESSIONMAP(blastSocketCtx);
   }

   ok = BlastSocketGetSessionMapEntry(blastSocketCtx, vAuth, &sessionWrapper);

   if (ok) {
      negotiatedBENITEnabled = sessionWrapper->negotiatedBENITEnabled;
   }

   if (!isSessionMapLocked) {
      UNLOCK_SESSIONMAP(blastSocketCtx);
   }

   if (!ok) {
      VVCSESSIONMGRLOG("SessionMap does not have entry for vAuth:%" MASK_TOKEN ".", vAuth);
      return ok;
   }

   VVCSESSIONMGRLOG("For vAuth:%" MASK_TOKEN ", negotiatedBENITEnabled: %s", vAuth,
                    negotiatedBENITEnabled ? "Yes" : "No");

   return negotiatedBENITEnabled;
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketExistsSessionMapEntry --
 *
 *      Check if a (vAuth, sessionWrapper) pair exists in
 *      BlastSocketContext::sessionMap.
 *      BlastSocketContext::lock must be held before calling into this
 *      function.
 *
 * Results:
 *      Return false if entry not present, otherwise return true.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

Bool
BlastSocketExistsSessionMapEntry(BlastSocketContext *blastSocketCtx, // IN
                                 const char *vAuth)                  // IN
{
   BlastSocketVvcSessionWrapper **thisSessionWrapper;

   ASSERT(blastSocketCtx);
   ASSERT(ISLOCKED_SESSIONMAP(blastSocketCtx));
   ASSERT(blastSocketCtx->sessionMap);

   thisSessionWrapper =
      (BlastSocketVvcSessionWrapper **)HashMap_Get(blastSocketCtx->sessionMap, (void *)(vAuth));

   if (!thisSessionWrapper || !*thisSessionWrapper) {
      VVCSESSIONMGRLOG("SessionMap does not have entry for vAuth:%" MASK_TOKEN ".", vAuth);
      return FALSE;
   }

   VVCSESSIONMGRLOG("SessionMap does have entry for "
                    "vAuth:%" MASK_TOKEN ", "
                    "sessionWrapper->vAuth:%" MASK_TOKEN,
                    vAuth, (*thisSessionWrapper)->vAuth);

   return TRUE;
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketGetSessionMapEntry --
 *
 *      Check if a (vAuth, sessionWrapper) pair exists in
 *      BlastSocketVvSessionMgr::sessionMap.
 *      BlastSocketVvSessionMgr::lock must be held before calling into this
 *      Function.
 *
 * Results:
 *      Return false if entry not present, otherwise return true.
 *      If the OUT param "sessionWrapper" is Non-NULL, then it will
 *      hold the sessionWrapper at the hashMap entry upon exiting.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

Bool
BlastSocketGetSessionMapEntry(BlastSocketContext *blastSocketCtx,            // IN
                              const char *vAuth,                             // IN
                              BlastSocketVvcSessionWrapper **sessionWrapper) // OUT
{
   BlastSocketVvcSessionWrapper **thisSessionWrapper;

   ASSERT(blastSocketCtx);
   ASSERT(ISLOCKED_SESSIONMAP(blastSocketCtx));
   ASSERT(blastSocketCtx->sessionMap);

   thisSessionWrapper =
      (BlastSocketVvcSessionWrapper **)HashMap_Get(blastSocketCtx->sessionMap, (void *)(vAuth));

   if (!thisSessionWrapper || !*thisSessionWrapper) {
      VVCSESSIONMGRLOG("SessionMap does not have entry for vAuth:%" MASK_TOKEN ".", vAuth);
      return FALSE;
   }

   VVCSESSIONMGRLOG("SessionMap does have entry for "
                    "vAuth:%" MASK_TOKEN ", "
                    "sessionWrapper->vAuth:%" MASK_TOKEN ".",
                    vAuth, (*thisSessionWrapper)->vAuth);

   if (sessionWrapper) {
      *sessionWrapper = *thisSessionWrapper;
   } else {
      VVCSESSIONMGRLOG("Invalid args - can not return the retrieved "
                       "SessionMap entry.");
      return FALSE;
   }

   return TRUE;
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketAddSessionMapEntry --
 *
 *      Add a (vAuth, sessionWrapper) pair to
 *      BlastSocketContext::sessionMap.
 *      BlastSocketContext::sessionMapLock must be held before calling into
 *      this function.
 *
 *      XXX: If an entry for this vAuth exists, then return
 *           failure for now. This should change for Multi-Protocol later.
 *
 * Results:
 *      Return false if error or entry could not be added,
 *      Otherwise Put the entry and return true.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

Bool
BlastSocketAddSessionMapEntry(BlastSocketContext *blastSocketCtx,           // IN
                              const char *vAuth,                            // IN
                              BlastSocketVvcSessionWrapper *sessionWrapper) // IN
{
   Bool ok = FALSE;

   ASSERT(blastSocketCtx);
   ASSERT(ISLOCKED_SESSIONMAP(blastSocketCtx));
   ASSERT(blastSocketCtx->sessionMap);

   if (!sessionWrapper) {
      VVCSESSIONMGRLOG("Invalid args.");
      return FALSE;
   }

   if (BlastSocketExistsSessionMapEntry(blastSocketCtx, vAuth)) {

      VVCSESSIONMGRLOG("Hash Collision ! "
                       "Entry for vAuth:%" MASK_TOKEN " already exists.",
                       vAuth);
      return FALSE;
   }

   ok = HashMap_Put(blastSocketCtx->sessionMap, (void *)(vAuth), (void *)(&sessionWrapper));

   if (!ok) {
      VVCSESSIONMGRLOG("Failed to Put entry into sessionMap for "
                       "vAuth:%" MASK_TOKEN ", "
                       "sessionWrapper->vAuth:%" MASK_TOKEN ".",
                       vAuth, sessionWrapper->vAuth);
      return FALSE;
   }

   VVCSESSIONMGRLOG("Entry added to sessionMap for "
                    "vAuth:%" MASK_TOKEN ", "
                    "sessionWrapper->vAuth:%" MASK_TOKEN ", "
                    "Session count is now: %d",
                    vAuth, sessionWrapper->vAuth, HashMap_Count(blastSocketCtx->sessionMap));

   return TRUE;
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketRemoveSessionMapEntry --
 *
 *      Remove a (vAuth, sessionWrapper) pair from
 *      BlastSocketVvSessionMgr::sessionMap.
 *      BlastSocketVvSessionMgr::sessionMapLock must be held before calling into
 *      Function.
 *
 * Results:
 *      Return false if error or entry could not be added,
 *      Otherwise Put the entry and return true.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

Bool
BlastSocketRemoveSessionMapEntry(BlastSocketContext *blastSocketCtx, // IN
                                 const char *vAuth)                  // IN
{
   Bool ok = FALSE;

   ASSERT(blastSocketCtx);
   ASSERT(ISLOCKED_SESSIONMAP(blastSocketCtx));
   ASSERT(blastSocketCtx->sessionMap);

   ok = HashMap_Remove(blastSocketCtx->sessionMap, (void *)(vAuth));

   if (!ok) {
      VVCSESSIONMGRLOG("Entry for vAuth:%" MASK_TOKEN " was not present "
                       "in the SessionMap",
                       vAuth);
      return FALSE;
   }

   VVCSESSIONMGRLOG("Entry for vAuth:%" MASK_TOKEN " removed from "
                    "SessionMap, Session count is now: %d",
                    vAuth, HashMap_Count(blastSocketCtx->sessionMap));

   return TRUE;
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketCreateSessionWrapper --
 *
 *      Allocate and fill up a BlastSocketVvcSessionWrapper struct
 *
 * Results:
 *      Return newly created BlastSocketVvcSessionWrapper
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

BlastSocketVvcSessionWrapper *
BlastSocketCreateSessionWrapper(int32 vvcSessionId,                 // IN
                                VvcSessionId vvcSessionIdHandle,    // IN
                                VvcSessionHandle sessionHandle,     // IN
                                EFIGUID smConnectionId,             // IN
                                Bool negotiatedNCEnabled,           // IN
                                Bool negotiatedBENITEnabled,        // IN
                                const char *vAuth,                  // IN
                                BlastSocketContext *blastSocketCtx) // IN
{
   BlastSocketVvcSessionWrapper *sessionWrapper;

   sessionWrapper = (BlastSocketVvcSessionWrapper *)Util_SafeCalloc(1, sizeof *sessionWrapper);

   ASSERT(sessionWrapper);

   sessionWrapper->vvcSessionId = vvcSessionId;
   sessionWrapper->smConnectionId = smConnectionId;
   sessionWrapper->sessionHandle = sessionHandle;
   sessionWrapper->negotiatedNCEnabled = negotiatedNCEnabled;
   sessionWrapper->negotiatedBENITEnabled = negotiatedBENITEnabled;
   sessionWrapper->vAuth = strdup(vAuth);
   sessionWrapper->blastSocketCtx = blastSocketCtx;
   sessionWrapper->vvcSessionIdHandle = vvcSessionIdHandle;
   sessionWrapper->isNetworkFailure = FALSE;
   sessionWrapper->isVvcHubActive = FALSE;
   sessionWrapper->deActivateVvcHub = FALSE;

   // Update BlastConnectionInfo with reasonable defaults
   sessionWrapper->blastConnInfo.usermode = BLAST_CONN_USER_MODE_UNKNOWN;
   sessionWrapper->blastConnInfo.brokerProtocol = BLAST_CONN_PROTO_UNKNOWN;
   sessionWrapper->blastConnInfo.primaryProtocol = BLAST_CONN_PROTO_TCP;
   sessionWrapper->blastConnInfo.secondaryProtocol = BLAST_CONN_PROTO_UNKNOWN;
   sessionWrapper->blastConnInfo.generationID = 0;

   DblLnkLst_Init(&sessionWrapper->link);

   return sessionWrapper;
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketDestroySessionWrapper --
 *
 *      Free up a BlastSocketVvcSessionWrapper struct
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

void
BlastSocketDestroySessionWrapper(BlastSocketVvcSessionWrapper *sessionWrapper) // IN
{
   if (!sessionWrapper) {
      return;
   }

   ASSERT(!sessionWrapper->isVvcHubActive);

   free(sessionWrapper->vAuth);
   free(sessionWrapper);

   VVCSESSIONMGRLOG("BlastSocketVvcSessionWrapperDestroy done.");
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketVvcSessionOnErrorCb --
 *
 *      The Session Error Cb is provided by the application for now.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

static void
BlastSocketVvcSessionOnErrorCb(VvcSessionHandle sessionHandle, // IN
                               VvcStatus status,               // IN
                               void *clientData)               // IN
{
   BlastSocketCallbacks *callbacks;
   BlastSocketContext *blastSocketContext;
   BlastSocketVvcSessionWrapper *sessionWrapper;
   VvcSessionEventsCbData *cbData = clientData;
   char *vAuth = NULL;
   int32 vvcSessionId = VVC_INVALID_SESSIONID;

   ASSERT(clientData != NULL);

   blastSocketContext = (BlastSocketContext *)cbData->clientData;

   ASSERT(!ISLOCKED_SESSIONMAP(blastSocketContext));

   sessionWrapper = (BlastSocketVvcSessionWrapper *)cbData->sessionWrapper;
   callbacks = &blastSocketContext->callbacks;

   LOCK_SESSIONMAP(blastSocketContext);
   vAuth = Util_SafeStrdup(sessionWrapper->vAuth);
   vvcSessionId = sessionWrapper->vvcSessionId;
   UNLOCK_SESSIONMAP(blastSocketContext);

   VVCSESSIONMGRLOG("SessionEventErrorCb for VvcSession with handle: %p", sessionHandle);

   VVCSESSIONMGRLOG("Session Error callback, status: %d, vvcSessionId:%d", status,
                    cbData->sessionId);

   if (callbacks->socketCloseCb) {
      callbacks->socketCloseCb(vAuth, cbData->sessionId, TRUE, VDPCONNECT_INVALID,
                               callbacks->cbFuncClientData);
   }

   BlastSocketStopVvcSession(blastSocketContext, vvcSessionId, vAuth, VDPCONNECT_INVALID);
   free(vAuth);
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketVvcSessionOnEstablishedCb --
 *
 *      Session Established Cb.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

static void
BlastSocketVvcSessionOnEstablishedCb(VvcSessionHandle sessionHandle, // IN
                                     void *clientData)               // IN
{
   VvcSessionEventsCbData *cbData = clientData;
   BlastSocketVvcSessionWrapper *sessionWrapper = cbData->sessionWrapper;
   BlastSocketContext *blastSocketContext = NULL;
   void *cbFuncClientData = NULL;
   char *vAuth = NULL;
   char *cookie = NULL;
   int32 sessionId = VVC_INVALID_SESSIONID;
   ASSERT(clientData != NULL);

   VVCSESSIONMGRLOG("SessionEstablishedCb for VvcSession with handle: %p, "
                    "SessionWrapper: %p",
                    sessionHandle, sessionWrapper);

   LOCK_SESSIONMAP(sessionWrapper->blastSocketCtx);
   sessionWrapper->isVvcCloseSeqEnabled =
      VVCLIB_GetSessionIsVVCCloseSeqEnabled(sessionWrapper->vvcSessionId);
   sessionWrapper->negotiatedVvcPauseResumeEnabled =
      VVCLIB_GetSessionIsVVCPauseResumeEnabled(sessionWrapper->vvcSessionId);

   blastSocketContext = sessionWrapper->blastSocketCtx;
   vAuth = sessionWrapper->vAuth;
   sessionId = sessionWrapper->vvcSessionId;
   ASSERT(blastSocketContext);
   cbFuncClientData = blastSocketContext->callbacks.cbFuncClientData;

   cookie = BlastSocketGetCookieFromViewAuthToken(vAuth, blastSocketContext);
   UNLOCK_SESSIONMAP(sessionWrapper->blastSocketCtx);

   if (!cookie) {
      VVCSESSIONMGRLOG("Failed to get cookie for vAuth:%" MASK_TOKEN " Not "
                       "triggering acceptFn.",
                       (NULL != vAuth) ? vAuth : "<null>");
      return;
   }

   if (blastSocketContext->callbacks.acceptFn) {
      VVCSESSIONMGRLOG("VVC Session Established, triggering acceptFn.");
      if (FALSE == blastSocketContext->callbacks.acceptFn(vAuth, sessionId, cbFuncClientData)) {
         VVCSESSIONMGRLOG("Accept callback failed, Stop vvc session.");
         /*
          * Failure of AcceptCB means that VNC plugin failed to start.
          * We stop the vvc session  in this case with reason as network failure
          * and wait for the 2 min timer to close the connection.
          */
         BlastSocketStopVvcSession(blastSocketContext, sessionId, vAuth,
                                   VDPCONNECT_NETWORK_FAILURE);
         return;
      }
   }
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketVvcSessionOnDestroyCb --
 *
 *      Session Destroy Cb.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

static void
BlastSocketVvcSessionOnDestroyCb(VvcSessionHandle sessionHandle, // IN
                                 void *clientData)               // IN
{
   VvcSessionEventsCbData *cbData = clientData;
   ASSERT(clientData != NULL);

   VVCSESSIONMGRLOG("SessionDestroyCb for VvcSession with handle: %p, "
                    "SessionWrapper: %p",
                    sessionHandle, cbData->sessionWrapper);

   // Destroy the BlastSocketVvcSessionWrapper as well
   BlastSocketDestroySessionWrapper(cbData->sessionWrapper);
   free(cbData);
   cbData = NULL;
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketHandleNetworkFailure --
 *
 *      Network error handler for a connection corresponding to given vAuth.
 *
 * Results:
 *      TRUE is session exists and stopVvcSession succeeds.
 *      FALSE if no relevant session found.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

Bool
BlastSocketHandleNetworkFailure(BlastSocketContext *blastSocketCtx, // IN
                                const char *vAuth)                  // IN
{
   BlastSocketVvcSessionWrapper *sessionWrapper;
   Bool ok = FALSE;

   ASSERT(blastSocketCtx);
   ASSERT(vAuth);
   ASSERT(blastSocketCtx->sessionMap);
   ASSERT(!ISLOCKED_SESSIONMAP(blastSocketCtx));

   LOCK_SESSIONMAP(blastSocketCtx);

   ok = BlastSocketGetSessionMapEntry(blastSocketCtx, vAuth, &sessionWrapper);

   if (!ok) {
      VVCSESSIONMGRLOG("SessionMap does not have entry for vAuth:%" MASK_TOKEN ".", vAuth);
      UNLOCK_SESSIONMAP(blastSocketCtx);
   } else if (sessionWrapper->isNetworkFailure) {
      VVCSESSIONMGRLOG("Network failure already handled for vAuth:%" MASK_TOKEN ".", vAuth);
      UNLOCK_SESSIONMAP(blastSocketCtx);
   } else {

      char *vAuthTmp = Util_SafeStrdup(sessionWrapper->vAuth);
      const char *cookie = BlastSocketGetCookieFromViewAuthToken(vAuthTmp, blastSocketCtx);
      VDPConnectionResult closeReason = VDPCONNECT_NETWORK_FAILURE;
      Bool negotiatedNCEnabled = sessionWrapper->negotiatedNCEnabled;
      Bool negotiatedVvcPauseResumeEnabled = sessionWrapper->negotiatedVvcPauseResumeEnabled;
      Bool doNotNotifySocketCloseCb = negotiatedNCEnabled && negotiatedVvcPauseResumeEnabled;
      int32 sessionID = sessionWrapper->vvcSessionId;
      VvcStatus status = VVC_STATUS_ERROR;

      sessionWrapper->isNetworkFailure = TRUE;
      UNLOCK_SESSIONMAP(blastSocketCtx);

      if (cookie) {
         /*
          * Session interruption count will always be greater by 1 of the
          * session reconnect count, when a interruption happens. Ex: If the
          * session reconnect count is 2 and a session interruption occurs,
          * the session interruption count will be 3.
          */
         VVCSESSIONMGRLOG("cookie is present for session: %d, session "
                          "interruption detected. vAuth: %" MASK_TOKEN ", "
                          "Number of Session interrupts: %d",
                          sessionID, vAuthTmp, sessionWrapper->reconnectCount + 1);
         BlastSocketDropCookie(cookie, blastSocketCtx);
      } else {
         /*
          * Close session which receives a socket error
          * and has its vAuth invalidated already.
          * This prevents the session from indefinite wait to complete
          * vvc session close sequence.
          */
         VVCSESSIONMGRLOG("Close the session: %d as cookie is invalid. "
                          "vAuth: %" MASK_TOKEN ".",
                          sessionID, vAuthTmp);
         closeReason = VDPCONNECT_INVALID;
      }

      // Force-Close/Abort the ncDeclined channels
      VVCLIB_CloseNCDeclinedChannels(sessionWrapper->sessionHandle, VvcCloseChannelAbort);

      if (doNotNotifySocketCloseCb) {
         // Do not notify application of the socketCloseCb
         VVCSESSIONMGRLOG("Do not notify Application of the socketCloseCb, "
                          "sessionID: %d.",
                          sessionID);
      } else {
         // Call user's socket close callback
         if (blastSocketCtx->callbacks.socketCloseCb) {
            blastSocketCtx->callbacks.socketCloseCb(vAuthTmp, sessionID, TRUE, closeReason,
                                                    blastSocketCtx->callbacks.cbFuncClientData);
         }
      }

      status = BlastSocketStopVvcSession(blastSocketCtx, sessionID, vAuthTmp, closeReason);
      ok = (status == VVC_STATUS_SUCCESS);
      free(vAuthTmp);
   }

   return ok;
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketVvcSessionOnSocketErrorCb --
 *
 *      Socket error callback.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

void
BlastSocketVvcSessionOnSocketErrorCb(int error,          // IN
                                     AsyncSocket *asock, // IN
                                     int32 sessionID,    // IN
                                     void *clientData)   // IN
{
   BlastSocketContext *blastSocketContext;
   BlastSocketVvcSessionWrapper *sessionWrapper = clientData;
   const char *vAuth = NULL;
   ASSERT(sessionWrapper);

   blastSocketContext = sessionWrapper->blastSocketCtx;

   ASSERT(blastSocketContext);
   ASSERT(!ISLOCKED_SESSIONMAP(blastSocketContext));

   VVCSESSIONMGRLOG("Socket error: %d for session: %d", error, sessionID);

   LOCK_SESSIONMAP(blastSocketContext);
   vAuth = sessionWrapper->vAuth;
   UNLOCK_SESSIONMAP(blastSocketContext);

   BlastSocketHandleNetworkFailure(blastSocketContext, vAuth);
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketVvcSessionOnCloseCb --
 *
 *      Callback from VVC after receiving VVC Close Msg. Fire the user provided
 *      session close callback.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

static void
BlastSocketVvcSessionOnCloseCb(VvcSessionHandle vvcSessionHandle, uint32 userMetadata,
                               void *clientData)
{
   BlastSocketCallbacks *callbacks;
   BlastSocketContext *blastSocketContext;
   BlastSocketVvcSessionWrapper *sessionWrapper;
   VvcSessionEventsCbData *cbData = clientData;
   char *vAuth = NULL;
   int32 vvcSessionId = VVC_INVALID_SESSIONID;

   ASSERT(clientData != NULL);

   blastSocketContext = (BlastSocketContext *)cbData->clientData;

   ASSERT(!ISLOCKED_SESSIONMAP(blastSocketContext));

   callbacks = &blastSocketContext->callbacks;
   sessionWrapper = (BlastSocketVvcSessionWrapper *)cbData->sessionWrapper;

   LOCK_SESSIONMAP(blastSocketContext);
   vAuth = Util_SafeStrdup(sessionWrapper->vAuth);
   vvcSessionId = sessionWrapper->vvcSessionId;
   UNLOCK_SESSIONMAP(blastSocketContext);

   VVCSESSIONMGRLOG("VvcSessionCloseCb for session: %p, closeReason: %d", vvcSessionHandle,
                    userMetadata);

   if (callbacks->socketCloseCb) {
      callbacks->socketCloseCb(vAuth, vvcSessionId, FALSE, userMetadata,
                               callbacks->cbFuncClientData);
   }

   /*
    * Force Close the session now.
    * This based on reason being set to VDPCONNECT_INVALID.
    */
   BlastSocketStopVvcSession(blastSocketContext, vvcSessionId, vAuth, VDPCONNECT_INVALID);
   free(vAuth);
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketStartVvcHub --
 *
 *      Initialize VvcHub library for out of process communication with VVC
 *      extensions
 *
 * Results:
 *      Return TRUE is Vvc Hub started, FALSE otherwise.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

Bool
BlastSocketStartVvcHub(BlastSocketVvcSessionWrapper *sessionWrapper) // IN
{

#ifdef VVCHUB_SUPPORT

   // TODO : vvclib should expose an internal interface to return the latest
   // VvcIntf. That way it wont be necessary to update this file when we
   // introduce a new version of VvcIntf

   VvcIntfV10 vvcIntf;
   VvcIntfVer ver;
   VvcStatus vvcStatus;

   uint64 startTime = 0;
   uint64 endTime = 0;

   ver.major = VVC_MAJOR_VER_1;
   ver.minor = VVC_MINOR_VER_0;
   vvcIntf.size = sizeof(vvcIntf);

   vvcStatus = VVCLIB_GetIntf(VVC_PLUGIN_ID_CORE_PROTOCOL, &ver, (VvcIntf)&vvcIntf);

   if (VVC_SUCCESS(vvcStatus)) {
      startTime = NS_TO_MS(Hostinfo_SystemTimerNS());
      /*
       * Check if BlastSocketStopVvcSession() was called even prior
       * to calling VVC_HubInit(). Refer to bug 2631104 for details.
       */
      LOCK_SESSIONMAP(sessionWrapper->blastSocketCtx);
      if (sessionWrapper->deActivateVvcHub) {
         sessionWrapper->deActivateVvcHub = FALSE;
         UNLOCK_SESSIONMAP(sessionWrapper->blastSocketCtx);
         return FALSE;
      }
      UNLOCK_SESSIONMAP(sessionWrapper->blastSocketCtx);

      if (VVC_HubInit(&vvcIntf, sessionWrapper->vvcSessionIdHandle, sessionWrapper->smConnectionId,
                      sessionWrapper->vAuth, &BlastSocketHubLibCallback, sessionWrapper)) {
         endTime = NS_TO_MS(Hostinfo_SystemTimerNS());
         VVCSESSIONMGRLOG("For VvcSession with sessionId: %d, "
                          "Initializing VVCHub succeeded after %" FMT64 "u ms",
                          sessionWrapper->vvcSessionId, endTime - startTime);
         /*
          * Deferred processing of BlastSocketStopVvcHub() if BlastSocketStopVvcSession()
          * is called before VVC_HubInit() is completed. Refer to bug 2631104.
          */
         LOCK_SESSIONMAP(sessionWrapper->blastSocketCtx);
         if (sessionWrapper->deActivateVvcHub) {
            sessionWrapper->deActivateVvcHub = FALSE;
            ASSERT(sessionWrapper->isVvcHubActive == FALSE);
            UNLOCK_SESSIONMAP(sessionWrapper->blastSocketCtx);
            BlastSocketStopVvcHub(sessionWrapper->vvcSessionIdHandle, sessionWrapper->sessionHandle,
                                  sessionWrapper->vvcSessionId);
            return FALSE;
         } else {
            sessionWrapper->isVvcHubActive = TRUE;
            UNLOCK_SESSIONMAP(sessionWrapper->blastSocketCtx);
            return TRUE;
         }
      } else {
         endTime = NS_TO_MS(Hostinfo_SystemTimerNS());
         VVCSESSIONMGRLOG("For VvcSession with sessionId: %d, "
                          "Initializing VVCHub Failed after %" FMT64 "u ms",
                          sessionWrapper->vvcSessionId, endTime - startTime);
         return FALSE;
      }
   } else {
      VVCSESSIONMGRLOG("For VvcSession with sessionId: %d, "
                       "Failed to get VVCInterface, VVCStatus:%d ",
                       sessionWrapper->vvcSessionId, vvcStatus);

      return FALSE;
   }

#endif

   return TRUE;
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketStopVvcHub --
 *
 *      Free up VvcHub
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

void
BlastSocketStopVvcHub(VvcSessionId vvcSessionIdHandle, // IN
                      VvcSessionHandle sessionHandle,  // IN
                      int32 vvcSessionId)              // IN
{

#ifdef VVCHUB_SUPPORT

   uint64 startTime = 0;
   uint64 endTime = 0;

   VVCSESSIONMGRLOG("VvcSession with sessionId: %d, sessionHandle: 0x%p, "
                    "vvcSessionIdHandle: 0x%p, Closing VVCHub library.",
                    vvcSessionId, sessionHandle, vvcSessionIdHandle);

   // Take a reference on the "main" vvcSession whose Hub is to be Uninitialized
   if (VVC_STATUS_SUCCESS != VVCLIB_AddRefSession(sessionHandle)) {
      VVCSESSIONMGRLOG("VvcSession with sessionId: %d, "
                       "sessionHandle: 0x%p does not exist",
                       vvcSessionId, sessionHandle);
      return;
   }

   startTime = NS_TO_MS(Hostinfo_SystemTimerNS());
   VVC_HubUninit(vvcSessionIdHandle);
   endTime = NS_TO_MS(Hostinfo_SystemTimerNS());

   // Release the reference on the "main" vvcSession taken for HubUninit
   VVCLIB_ReleaseSession(sessionHandle);

   VVCSESSIONMGRLOG("VvcSession with sessionId: %d, "
                    "Closing VVCHub library completed in %" FMT64 "u ms",
                    vvcSessionId, endTime - startTime);
#endif
}


#ifdef VVCHUB_SUPPORT

/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketHubLibCallback --
 *
 *      Callback for VVCHub lib
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

static void
BlastSocketHubLibCallback(void *context,           // IN
                          Bool isError,            // IN
                          Bool isConnected,        // IN
                          unsigned long sessionId, // IN
                          unsigned long processId) // IN
{
   /*
    *  Bug 2671204 - BlastSocketHubLibCallback could be launched from MFW
    *                dispatch thread (nodeGoingDown) which has no guarantee
    *                for the validality of BlastSocketVvcSessionWrapper as
    *                session and sessionWrapper objects could already be
    *                destroyed from the main thread when this callback is called.
    *
    *                Instead we will log hub's vvcSessionIdHandle and the sessionWrapper
    *                pointer at the beginning of VVC_HubUninit() and vvcSessionIdHandle
    *                in BlastSocketStopVvcHub() so that the sessionId can be
    *                found from the log in BlastSocketStopVvcHub().
    */
   VVCSESSIONMGRLOG("For VvcSession with sessionWrapper: 0x%p, "
                    "received %s notification from VVCHub with "
                    "%s error for NodeWTSSessionID:%lu, NodeProcessID:%lu.",
                    context, isConnected ? "connect" : "disconnect", isError ? " " : "no",
                    sessionId, processId);
}

#endif

/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketSetupSession --
 *
 *      Higher level API that does the Setting up of a VVC Session.
 *      This function does following:
 *
 *      1. Generate VvcSessionId and shadowInstanceId.
 *      2. Initialize transportBe structure.
 *      3. Invoke BlastSocketStartVvcSession()
 *
 * Results:
 *      Return TRUE if successful.
 *
 * Side effects:
 *      BlastSocketContext::nextShadowInstanceId might be modified.
 *
 *----------------------------------------------------------------------------
 */

Bool
BlastSocketSetupSession(AsyncSocket *asock,                     // IN
                        Bool isEndToEndConnection,              // IN
                        BlastSocketContext *blastSocketContext, // IN
                        const char *vAuth,                      // IN
                        BlastSocketWSPeerConfig wsPeerConfig,   // IN
                        int32 *vvcSessionId,                    // OUT
                        int32 *shadowInstanceId)                // OUT
{
   VvcStatus status;
   VvcTransptBackend transportBe;
   Bool isBwe = FALSE;
   Bool negotiatedNCEnabled = FALSE;
   Bool negotiatedBENITEnabled = FALSE;
   const char *protocol = AsyncSocket_GetWebSocketProtocol(asock);
   EFIGUID smConnectionId;
   char *peerFeatureAllowList = NULL;

   if (NULL == protocol) {
      VVCSESSIONMGRLOG("Failed to obtain subprotocol list");
   } else {
      isBwe = (wsPeerConfig.negotiatedTcpBweVersion != ASYNC_BWE_SOCKET_VERSION_NONE);
      negotiatedNCEnabled = BlastSocket_SubprotocolImpliesNC(protocol);
   }

   negotiatedBENITEnabled = wsPeerConfig.isWSPeerBENITEnabled &&
                            blastSocketContext->params.localNetworkIntelligenceEnabled;

   VVCSESSIONMGRLOG("Network Intelligence is %sNegotiated.", negotiatedBENITEnabled ? "" : "Not ");

   // Retrieve Session Monitor Connection ID associated with vAuth
   memset(&smConnectionId, 0, sizeof smConnectionId);
   if (!BlastSocketGetSmConnectionIdFromViewAuthToken(vAuth, blastSocketContext, &smConnectionId)) {
      VVCSESSIONMGRLOG("Failed to retrieve smConnectionId");
   }

   // Step 1. Generate VvcSessionId and ShadowInstanceId

   if (!BlastSocketGenerateVvcSessionId(blastSocketContext, vAuth, vvcSessionId,
                                        shadowInstanceId)) {
      // Failed to generate vvcSessionId
      VVCSESSIONMGRLOG("Failed to generate VvcSessionId");
      return FALSE;
   }
   VVCSESSIONMGRLOG("Done with SessionId generation, "
                    "VvcSessionId: %d, shadowInstanceId: %d",
                    *vvcSessionId, *shadowInstanceId);

   VVCSESSIONMGRLOG("isBwe: %d isEndToEndConnection: %d "
                    "negotiatedNCEnabled: %d",
                    isBwe, isEndToEndConnection, negotiatedNCEnabled);

   isEndToEndConnection = isEndToEndConnection || isBwe;

   // Step 2: Initialize the transportBe structure
   memset(&transportBe, 0, sizeof transportBe);
   transportBe.flags = VVC_TRANSPORT_BE_ENABLE_FORCE_CANCEL_SAFE_IO | VVC_TRANSPORT_BE_SERVER |
                       (isEndToEndConnection ? VVC_TRANSPORT_BE_DISABLE_BANDWIDTH_DETECTION : 0);

   // Check if peer feature allow list needs to be populated
   if (NULL != wsPeerConfig.peerFeatureAllowList) {
      peerFeatureAllowList = wsPeerConfig.peerFeatureAllowList;
      wsPeerConfig.peerFeatureAllowList = NULL;
   }

   VVCSESSIONMGRLOG("Starting BlastSession with vvcSessionID:%d, "
                    "shadowInstanceID:%d",
                    *vvcSessionId, *shadowInstanceId);

   // 3. Invoke BlastSocketStartVvcSession().
   status =
      BlastSocketStartVvcSession(blastSocketContext, *vvcSessionId, smConnectionId, vAuth,
                                 &transportBe, asock, isEndToEndConnection, negotiatedNCEnabled,
                                 negotiatedBENITEnabled, wsPeerConfig.negotiatedMptVersion,
                                 wsPeerConfig.negotiatedTcpBweVersion, peerFeatureAllowList);

   free(peerFeatureAllowList);
   peerFeatureAllowList = NULL;

   if (status != VVC_STATUS_SUCCESS) {
      VVCSESSIONMGRLOG("Unable to Start Vvc Session, status:%d", status);
      return FALSE;
   }

   return TRUE;
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketGenerateVvcSessionId --
 *
 *      Generate VvcSessionId and shadowInstanceId.
 *      Both will be given to acceptFn.
 *
 * Results:
 *      Return TRUE if successful.
 *
 * Side effects:
 *      The minimumRate and maximumRate will be configured
 *
 *----------------------------------------------------------------------------
 */

Bool
BlastSocketGenerateVvcSessionId(BlastSocketContext *blastSocketContext, // IN
                                const char *vAuth,                      // IN
                                int32 *vvcSessionId,                    // OUT
                                int32 *shadowInstanceId)                // OUT
{
   Bool isShadowSession = FALSE;
   int shadowIdRetryCount = 0; /* Only for shadow id generation retry */

   VVCSESSIONMGRLOG("BlastSocketGenerateVvcSessionId START");

   ASSERT(blastSocketContext != NULL);

   /*
    *   Generate a unique SessionId which will be used to setup VvcSession.
    *   Returning a non-zero value for shadowInstanceId indicates that it is
    *   going to be a shadow Session.
    */

   if (!vvcSessionId || !shadowInstanceId) {
      VVCSESSIONMGRLOG("Invalid args, exiting.");
      return FALSE;
   }

   /*
    * Step 1. Calculate shadowInstanceId based on return value.
    */
   isShadowSession = BlastSocketIsShadowSession(vAuth, blastSocketContext);

   do {
      if (isShadowSession) {
         *shadowInstanceId = blastSocketContext->nextShadowInstanceId;
         blastSocketContext->nextShadowInstanceId++;
         if (*shadowInstanceId > MAX_SHADOW_INSTANCE_ID) {
            VVCSESSIONMGRLOG("ShadowInstanceId:%d is out of the supported "
                             "range, maxShadowInstanceId:%d, rolling over.",
                             *shadowInstanceId, MAX_SHADOW_INSTANCE_ID);
            *shadowInstanceId = *shadowInstanceId % MAX_SHADOW_INSTANCE_ID;
         }
      } else {
         *shadowInstanceId = PRIMARY_CONNECTION_SHADOW_INSTANCE_ID;
      }

      VVCSESSIONMGRLOG("ShadowInstanceId: %d, IsShadowSession: %s", *shadowInstanceId,
                       isShadowSession ? "Yes" : "No");

      /*
       * Step 2. Generate Unique SessionId based on a combination of WtsSessionId
       *         and shadowInstanceId.
       */

      if (!BlastSocketGenerateUniqueSessionId(shadowInstanceId, vvcSessionId)) {
         VVCSESSIONMGRLOG("Failed to generate VvcSessionId.");
         *vvcSessionId = -1;
         return FALSE;
      }

      /*
       * TODO: This should be removed once VVC returns a session id rather than
       * BlastSocket requiring a session id generation.
       */
      if (isShadowSession) {
         VvcSessionHandle session;
         VvcInstanceHandle vvcInstanceHandle = blastSocketContext->instanceHandle;

         /* Release BlastSocketLock before calling any VVC APIs */
         ASSERT(BlastSocket_IsLocked());
         BlastSocket_Unlock();

         session = VVCLIB_GetSessionHandle(vvcInstanceHandle, *vvcSessionId);
         if (session == NULL) {
            BlastSocket_Lock();
            /*
             * The generated shadow vvc session id is not used yet, so it's valid
             * to return.
             */
            return TRUE;
         } else {
            VVCLIB_ReleaseSession(session);
            BlastSocket_Lock();

            shadowIdRetryCount++;
            if (shadowIdRetryCount < MAX_SHADOW_INSTANCE_ID) {
               VVCSESSIONMGRLOG("The ShadowInstanceId:%d has been taken, "
                                "retrying count:%d",
                                *shadowInstanceId, shadowIdRetryCount);
            } else {
               VVCSESSIONMGRLOG("All %d shadow instance ids are not available.",
                                MAX_SHADOW_INSTANCE_ID);
               return FALSE;
            }
         }
      } else {
         /*
          * Returns TRUE for primary session.
          */
         return TRUE;
      }
   } while (TRUE);

   return TRUE;
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketGetVvcSessionID --
 *
 *   Generate VvcSessionID from WTSSessionID & ShadowInstanceID.
 *
 *   VvcSessionId is int32 (as defined by VVC library):
 *      VvcSessionId = ShadowInstanceId (8bit) | WTSSessionId (16bit)
 *   The highest 7bit (1 bit for sign number) is used by VVCSession's
 *   SHADOW SESSION macro (which will get removed).
 *   When that happens, we can rearrange for the encoding, e.g.
 *      VVCSessionID = ShadowInstanceID (7bit) | WTSSessionID (24bit)
 *
 * Results:
 *   True if successful, otherwise false.
 *
 * Side effect:
 *   None.
 *
 *----------------------------------------------------------------------------
 */

Bool
BlastSocketGetVvcSessionID(const int32 wtsSessionId,     // IN
                           const int32 shadowInstanceId, // IN
                           int32 *vvcSessionId)          // OUT
{
   ASSERT(vvcSessionId);

   *vvcSessionId = -1;

   if (shadowInstanceId == PRIMARY_CONNECTION_SHADOW_INSTANCE_ID) {
      *vvcSessionId = wtsSessionId;
      return TRUE;
   }

   if (wtsSessionId > MAX_WTS_SESSION_ID) {
      VVCSESSIONMGRLOG("WTSSessionId:%d is out of "
                       "the supported range for shadow session, "
                       "maxWTSSessionId:%u.",
                       wtsSessionId, MAX_WTS_SESSION_ID);
      return FALSE;
   }

   *vvcSessionId = (int32)wtsSessionId | (shadowInstanceId << 16);
   return TRUE;
}


/*
 *----------------------------------------------------------------------
 *
 * BlastSocketGenerateUniqueSessionId --
 *
 *    A function that encapsulates generating a sessionId to be used for
 *    setting up VVC Session. The sessionId returned from this function
 *    could be a "shadowSessionId"
 *    vvcSessionId will be "-1" in case of failure.
 *
 * Results:
 *    True if SessionId was generated, false otherwise.
 *
 * Side Effects:
 *    None.
 *
 *----------------------------------------------------------------------
 */

Bool
BlastSocketGenerateUniqueSessionId(int32 *shadowInstanceId, // IN
                                   int32 *vvcSessionId)     // OUT
{
   int32 wtsSessionId;

   if (!vvcSessionId || !shadowInstanceId) {
      VVCSESSIONMGRLOG("Invalid args.");
      return FALSE;
   }

   *vvcSessionId = -1;

   /*
    * VVCLIB_OpenSession uses sessionId to uniquely identify a VvcSession
    * associated to a given VvcInstance.
    *
    * Within the same VVC instance, there can't be multiple VvcSessions
    * having the same sessionId, which poses issues for shadow
    * session, where a single window session may have several clients
    * connected (hence, VVC sessions).
    *
    * For Out of Proc, VVC assumes that the sessionId is WTSSessionId, but
    * otherwise VvcSession::sessionId can be any int32 identifier.
    *
    * So, the temporary workaround is to "encode" both VVCInstanceID
    * and WTSSessionID into VVCSessionID argument for VVCLIB_OpenSession.
    * See GetVVCSessionID()
    */

   if (!BlastSocketGetPlatformSessionId(&wtsSessionId)) {
      VVCSESSIONMGRLOG("Could not get WTSSessionID.");
      return FALSE;
   }
   if (!BlastSocketGetVvcSessionID(wtsSessionId, *shadowInstanceId, vvcSessionId)) {
      VVCSESSIONMGRLOG("Error generating VVCSessionId from "
                       "WTSSessionId:%d and ShadowInstanceId:%d.",
                       wtsSessionId, *shadowInstanceId);
      return FALSE;
   }
   VVCSESSIONMGRLOG("Using VVCSessionId:%d(%x) "
                    "for WTSSessionId:%d and ShadowInstanceId:%d.",
                    *vvcSessionId, *vvcSessionId, wtsSessionId, *shadowInstanceId);

   return TRUE;
}


/*
 *----------------------------------------------------------------------
 *
 * BlastSocketGetPlatformSessionId --
 *
 *    Get the platform specific SessionId.
 *    WTS SessionId for Windows and getpgrp() for Linux.
 *
 * Results:
 *    True if SessionId was retrieved, false otherwise.
 *
 * Side Effects:
 *    None.
 *
 *----------------------------------------------------------------------
 */

Bool
BlastSocketGetPlatformSessionId(int32 *wtsSessionId) // OUT
{
#ifdef _WIN32
   DWORD sId;
   if (ProcessIdToSessionId(GetCurrentProcessId(), &sId)) {
      *wtsSessionId = (int32)sId;
      return TRUE;
   } else {
      return FALSE;
   }
#else
   /*
    * The process group ID is constant and unique during the process
    * lifetime. Only its lower 16 bits are kept to avoid overlapping
    * shadowSessionId.
    * References: Bug #3011028, Reviewboard #1791534, #1895728
    */
   *wtsSessionId = getpgrp() & MAX_WTS_SESSION_ID;
   return TRUE;
#endif
}


/*
 *----------------------------------------------------------------------
 *
 * BlastSocketDataSockActivatedCb --
 *
 *    This callback will be triggered by Vvc when a specific AsockBe gets
 *    marked as DataSocket.
 *    The last argument tells us if the Control AsockBe is also the Data
 *    AsockBe. In such case, we know that fallback of BEAT to TCP has
 *    triggered and we invoke acceptFn to appblast.
 *
 * Results:
 *    None.
 *
 * Side Effects:
 *    None.
 *
 *----------------------------------------------------------------------
 */

static void
BlastSocketDataSockActivatedCb(AsyncSocket *asock, // IN
                               int32 sessionID,    // IN
                               Bool isReconnect,   // IN
                               Bool isCtrlSock,    // IN
                               void *clientData)   // IN
{
   char *vAuth;
   char *cookie;
   void *cbFuncClientData;
   BlastSocketContext *blastSocketContext;
   BlastSocketVvcSessionWrapper *sessionWrapper = clientData;
   ASSERT(sessionWrapper);

   blastSocketContext = sessionWrapper->blastSocketCtx;
   ASSERT(blastSocketContext);

   VVCSESSIONMGRLOG("Asock %p is now Data Socket for VvcSessionId: %d, "
                    "isControlAsock: %s, isReconnect: %s.",
                    asock, sessionID, isCtrlSock == TRUE ? "Yes" : "No",
                    isReconnect == TRUE ? "Yes" : "No");

   VVCSESSIONMGRLOG("Data Connection:%s", isCtrlSock == TRUE ? "TCP" : "UDP");

   LOCK_SESSIONMAP(blastSocketContext);
   sessionWrapper->isNetworkFailure = FALSE;
   vAuth = sessionWrapper->vAuth;
   cbFuncClientData = blastSocketContext->callbacks.cbFuncClientData;
   cookie = BlastSocketGetCookieFromViewAuthToken(vAuth, blastSocketContext);
   UNLOCK_SESSIONMAP(blastSocketContext);

   if (!cookie) {
      VVCSESSIONMGRLOG("Failed to get cookie for vAuth:%" MASK_TOKEN " Not "
                       "triggering acceptFn.",
                       (NULL != vAuth) ? vAuth : "<null>");
      return;
   }

   if (FALSE == BlastSocketGrabCookie(cookie, blastSocketContext)) {
      VVCSESSIONMGRLOG("Grab cookie failed. Not triggering acceptFn.");
      /*
       * Failure to GrabCookie will result in VNC plugin not being started
       * and wait for the 2 min timer to close the connection.
       */
      return;
   }

   if (isReconnect) {

      if (sessionWrapper->negotiatedNCEnabled && sessionWrapper->negotiatedVvcPauseResumeEnabled) {
         // Do not notify application of the acceptCb
         VVCSESSIONMGRLOG("Do not notify of the Application about the "
                          "dataSockActivatedCb on Reconnect, sessionID: %d.",
                          sessionID);
      } else {
         if (blastSocketContext->callbacks.acceptFn) {
            VVCSESSIONMGRLOG("Data Socket activated, triggering acceptFn.");
            if (FALSE ==
                blastSocketContext->callbacks.acceptFn(vAuth, sessionID, cbFuncClientData)) {
               VVCSESSIONMGRLOG("Accept callback failed, Drop cookie.");
               /*
                * Failure of AcceptCB means that VNC plugin failed to start.
                * We drop the cookie in this case and wait for the 2 min timer
                * to close the connection.
                */
               BlastSocketDropCookie(cookie, blastSocketContext);
               return;
            }
         }
      }
   }
}


/*
 *----------------------------------------------------------------------
 *
 * BlastSocketGetSslContext --
 *
 *    Callback from VVC to get the SSL context originally passed to
 *    BlastSocket_Start and shared across all incoming client connections.
 *
 * Results:
 *    Saved SSL context pointer.
 *
 * Side Effects:
 *    None.
 *
 *----------------------------------------------------------------------
 */

void *
BlastSocketGetSslContext(void *clientData) // IN
{
   BlastSocketContext *blastSocketContext;
   BlastSocketVvcSessionWrapper *sessionWrapper = clientData;
   ASSERT(sessionWrapper);

   blastSocketContext = sessionWrapper->blastSocketCtx;
   ASSERT(blastSocketContext);

   return blastSocketContext->params.sslCtx;
}


/*
 *----------------------------------------------------------------------
 *
 * BlastSocketPushAsockToVvcSession --
 *
 *    Push the AsyncSocket to VvcSession
 *
 * Results:
 *    True if AsyncSocket was successfully given to VvcSession, False
 *    otherwise.
 *
 * Side Effects:
 *    None.
 *
 *----------------------------------------------------------------------
 */

Bool
BlastSocketPushAsockToVvcSession(BlastSocketVvcSessionWrapper *sessionWrapper, // IN
                                 VvcSessionHandle vvcSessionHandle,            // IN
                                 AsyncSocket *asock,                           // IN
                                 Bool isControlAsock,                          // IN
                                 Bool isEndToEndConnection,                    // IN
                                 Bool blastSocketThreadEnabled,                // IN
                                 uint32 nonce,                                 // IN
                                 uint32 serialNo)                              // IN
{
   VvcAsockBackend asockBe;
   VvcStatus status = VVC_STATUS_SUCCESS;

   ASSERT(sessionWrapper);
   ASSERT(vvcSessionHandle);
   ASSERT(asock);
   ASSERT(sessionWrapper->blastSocketCtx);

   // Build up asockBe and do VVCLIB_AddAsockBackend()
   memset(&asockBe, 0, sizeof asockBe);
   asockBe.asock = asock;
   asockBe.asockLock = BlastSocket_GetLock();
   asockBe.isEndToEndConnection = isEndToEndConnection;
   asockBe.isControlAsock = isControlAsock;
   asockBe.errorCb = BlastSocketVvcSessionOnSocketErrorCb;
   asockBe.errorCbClientData = sessionWrapper;
   asockBe.dataSockActivatedCb = BlastSocketDataSockActivatedCb;
   asockBe.dataSockActivatedCbData = sessionWrapper;
   asockBe.verifySetupMsgCb = (void *)BlastSetup_GetParams;
   asockBe.verifySetupMsgCbCb = BlastSocketCompleteGetParamsCB;
   asockBe.verifySetupMsgCbCbData = sessionWrapper->blastSocketCtx;
   asockBe.getSslCtxCb = BlastSocketGetSslContext;
   asockBe.getSslCtxCbData = sessionWrapper;
   if (isControlAsock) {
      asockBe.reconnectCount = sessionWrapper->reconnectCount;
      asockBe.negotiatedTcpBweVersion = sessionWrapper->negotiatedTcpBweVersion;
   }
   asockBe.workerNonce = nonce;
   asockBe.serialNo = serialNo;
   asockBe.blastSocketThreadEnabled = blastSocketThreadEnabled;

   status = VVCLIB_AddAsockBackend(vvcSessionHandle, &asockBe);
   if (!VVC_SUCCESS(status)) {
      VVCSESSIONMGRLOG("Failed to Add AsyncSocket to VVC, status: %d", status);
   }

   return status == VVC_STATUS_SUCCESS;
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketMapIterCbGetMatchingSessionWrappers --
 *
 *      Callback function for HashMap_Iterate().
 *      Stop the VVC Session if the passed in sessionId matches with
 *      hash map sessionId entry.
 *
 * Results:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

void
BlastSocketMapIterCbGetMatchingSessionWrappers(void *key,      // IN
                                               void *data,     // IN
                                               void *userData) // IN/OUT
{
   BlastSocketVvcSessionWrapper *sessionWrapper;
   BlastSocketSessionMapIterateCbData *cbData = userData;

   ASSERT(data != NULL);
   ASSERT(userData != NULL);

   sessionWrapper = *(BlastSocketVvcSessionWrapper **)data;

   ASSERT(sessionWrapper != NULL);

   if (sessionWrapper->vvcSessionId == cbData->vvcSessionId) {
      DblLnkLst_LinkLast(&sessionWrapper->link, &cbData->sessionWrapperList);
   }
}


/*
 *----------------------------------------------------------------------
 *
 * BlastSocketIsNewVvcSessionNeeded --
 *
 *    Check if a VvcSession already exists for the (VvcInstance, sessionId)
 *    combination.
 *    If VvcSession Persistence across network recovery is supported, then
 *    use this VvcSession as-is.
 *    Otherwise, clean up the VvcSession.
 *
 * Results:
 *    True if caller needs to setup a new VvcSession, False otherwise.
 *
 * Side Effects:
 *    If VvcSession Persistence across network recovery is not supported,
 *    then as a side-effect, the old VvcSession will be cleaned up.
 *
 *----------------------------------------------------------------------
 */

Bool
BlastSocketIsNewVvcSessionNeeded(BlastSocketContext *blastSocketCtx, // IN
                                 int32 vvcSessionId,                 // IN
                                 const char *vAuth,                  // IN
                                 VvcSessionHandle *vvcSessionHandle) // OUT
{
   Bool newVvcSessionRequired = TRUE;
   Bool isNetworkRecovery = FALSE;
   int vAuthClaimCount = 0;
   VvcInstanceHandle vvcInstanceHandle = NULL;
   VvcSessionHandle sessionHandle = NULL;

   ASSERT(blastSocketCtx != NULL);
   ASSERT(vAuth != NULL);

   vAuthClaimCount = BlastSocketGetViewAuthTokenClaimCount(vAuth, blastSocketCtx);

   /*
    * NOTE: Below check needs to be removed if VVC session
    *       creation is moved to HandleNewAuthRequest.
    *       Currently VVC session is created after handling WS upgrade request.
    */
   if (0 == vAuthClaimCount) {
      VVCSESSIONMGRLOG("Invalid vAuth:%" MASK_TOKEN ".", vAuth);
      return FALSE;
   }

   isNetworkRecovery = (vAuthClaimCount > 1) ? TRUE : FALSE;

   if (isNetworkRecovery) {

      if (!BlastSocketIsNetworkFailure(blastSocketCtx, vAuth)) {
         /*
          * A network recovery is received for a connection for the given
          * vAuth and we haven't so far seen a network failure from the
          * networking stack.
          * Before stopping VVC session we notify the application layer that
          * this session is being closed due to an error because of lost
          * connection. See Bug 1996506 and Bug 2291611 for more details.
          */
         VVCSESSIONMGRLOG("Network recovery seen for vAuth:%" MASK_TOKEN ". "
                          "before handling any network failure.",
                          vAuth);
         BlastSocketHandleNetworkFailure(blastSocketCtx, vAuth);
      }
      VVCSESSIONMGRLOG("Network Recovery: Checking if new vvc session is "
                       "needed for sessionid: %d, vAuth:%" MASK_TOKEN ".",
                       vvcSessionId, vAuth);
   }

   /*
    * Do SessionMap lookup first when NC is enabled.
    * With NC Disabled, iterate the SessionMap only if VVCLIB_GetSessionHandle()
    * succeeds.
    *
    * Why: to support NC for Shadow Sessions.
    * 1. For Shadow Sessions, upon reconnect the shadowInstanceId is bumped
    *    by "1" - causing the VvcSessionId to be different.
    * 2. This won't cause issue when NC is disabled because VvcSession is
    *    always newly created.
    * 3. With NC Enabled, the SessionMap will have a mapping for the shadow
    *    session's vAuth. We should get the persisted VvcSession from there
    *    and not create a new one.
    *
    * Once we find the persisted session, we fire dataSocketActivatedCb
    * with the persisted session's sessionId - so the subsequent listeners and
    * channels work (with correct VvcSessionId).
    *
    * XXX: An additional (and slightly invasive) fix should be done to Not
    *      bump up shadowInstanceId if connect request for a persisted
    *      Shadow Session comes by.
    */

   /*
    * XXX: In addition to checking a local BlastSocketParam flag,
    *      the decision to do VvcSession Persistence should also
    *      depend on a WebSocket header field negotiation.
    */
   if (BlastSocketIsNegotiatedNCEnabled(blastSocketCtx, vAuth) && isNetworkRecovery) {
      // VvcSession Persistence is supported
      BlastSocketVvcSessionWrapper *sessionWrapper;
      Bool ok = FALSE;

      /*
       * In addition to checking VVCLIB_GetSessionHandle(), also ensure
       * that BlastSocketContext::sessionMap has a mapping for this session.
       * If not, then we need to setup a new VvcSession.
       */
      LOCK_SESSIONMAP(blastSocketCtx);

      ok = BlastSocketGetSessionMapEntry(blastSocketCtx, vAuth, &sessionWrapper);

      if (!ok) {
         VVCSESSIONMGRLOG("Entry for sessionId: %d, vAuth:%" MASK_TOKEN
                          " is not present in SessionMap.",
                          vvcSessionId, vAuth);
         *vvcSessionHandle = NULL;
      } else {

         sessionHandle = sessionWrapper->sessionHandle;
         VVCSESSIONMGRLOG("Found existing Vvc Session: %p with "
                          "VVCSessionID: %d, vAuth:%" MASK_TOKEN ".",
                          sessionHandle, vvcSessionId, vAuth);
         newVvcSessionRequired = FALSE;
         *vvcSessionHandle = sessionHandle;
      }

      UNLOCK_SESSIONMAP(blastSocketCtx);
   } else {

      /*
       * We can end up here in 2 situations
       * 1. New vvc session for a new connection request.
       * 2. NR 1.0 (Network recovery for web-client or non-NC Clients).
       */

      vvcInstanceHandle = blastSocketCtx->instanceHandle;
      sessionHandle = VVCLIB_GetSessionHandle(vvcInstanceHandle, vvcSessionId);

      if (sessionHandle) {

         BlastSocketSessionMapIterateCbData *cbData;
         DblLnkLst_Links *curr;

         VVCSESSIONMGRLOG("Found existing VVCSession: %p, Check SessionMap if "
                          "entry needs to be deleted and close VVCSession.",
                          sessionHandle);

         cbData = Util_SafeCalloc(1, sizeof *cbData);

         cbData->vvcSessionId = vvcSessionId;
         DblLnkLst_Init(&cbData->sessionWrapperList);

         /*
          * Get the list of sessions in sessionMap matching this vvcSessionId
          * and stop them. We cannot do this during the iteration because
          * sessionMapLock should not be held while calling
          * BlastSocketStopVvcSession.
          */
         LOCK_SESSIONMAP(blastSocketCtx);
         HashMap_Iterate(blastSocketCtx->sessionMap, BlastSocketMapIterCbGetMatchingSessionWrappers,
                         FALSE, (void *)cbData);
         UNLOCK_SESSIONMAP(blastSocketCtx);

         /* Do StopVvcSession for the matched session. */
         DblLnkLst_ForEach(curr, &cbData->sessionWrapperList)
         {
            BlastSocketVvcSessionWrapper *sessionWrapper =
               DblLnkLst_Container(curr, BlastSocketVvcSessionWrapper, link);
            const char *vAuthTmp = sessionWrapper->vAuth;
            BlastSocketCallbacks *callbacks = &blastSocketCtx->callbacks;
            VDPConnectionResult stopSessionReason = VDPCONNECT_INVALID;

            ASSERT(sessionWrapper->vvcSessionId == vvcSessionId);
            ASSERT(sessionWrapper->blastSocketCtx == blastSocketCtx);

            VVCSESSIONMGRLOG("Found existing VVCSession with "
                             "VVCSessionID:%d, vAuth:%" MASK_TOKEN ", closing VVCSession.",
                             vvcSessionId, (NULL != vAuthTmp) ? vAuthTmp : "<null>");

            if (callbacks->socketCloseCb) {
               callbacks->socketCloseCb(vAuthTmp, vvcSessionId, TRUE, stopSessionReason,
                                        callbacks->cbFuncClientData);
            }

            BlastSocketStopVvcSession(blastSocketCtx, vvcSessionId, vAuthTmp, stopSessionReason);
         }

         VVCLIB_ReleaseSession(sessionHandle);
         sessionHandle = NULL;
         *vvcSessionHandle = NULL;
         free(cbData);
      } else {
         VVCSESSIONMGRLOG("VvcSession Not Found for vvcSessionId: %d and "
                          "newVvcSessionRequired: %s. ",
                          vvcSessionId, newVvcSessionRequired == TRUE ? "Yes" : "No");
      }
   }

   return newVvcSessionRequired;
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocket_StopVvcSession --
 *
 *      Teardown a VvcSession
 *
 * Results:
 *      Return VvcStatus.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

VvcStatus
BlastSocket_StopVvcSession(BlastSocketContext *blastSocketCtx, // IN
                           int32 vvcSessionId,                 // IN
                           const char *vAuth,                  // IN
                           VDPConnectionResult reason)         // IN
{
   return BlastSocketStopVvcSession(blastSocketCtx, vvcSessionId, vAuth, reason);
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocket_SetVvcMinMaxBw --
 *
 *      Set MinMax Bw for the VvcSession
 *
 * Results:
 *      Return VVC_STATUS_SUCCESS if successful.
 *
 * Side effects:
 *      The minimumRate and maximumRate will be configured
 *
 *----------------------------------------------------------------------------
 */

VvcStatus
BlastSocket_SetVvcMinMaxBw(BlastSocketContext *blastSocketCtx, // IN
                           const char *vAuth,                  // IN
                           double minimumRate,                 // IN
                           double maximumRate,                 // IN
                           double maxBandwidthBurstMsec)       // IN
{
   VvcStatus status = VVC_STATUS_SUCCESS;

   ASSERT(blastSocketCtx != NULL);

   LOCK_SESSIONMAP(blastSocketCtx);

   if (vAuth != NULL) {
      /* Set values for existing vAuth session, if any */
      status = BlastSocketSetVvcMinMaxBw(blastSocketCtx, vAuth, minimumRate, maximumRate,
                                         maxBandwidthBurstMsec);
   } else {
      /* Change will take effect for new VVC sessions */
      blastSocketCtx->params.minimumRate = minimumRate;
      blastSocketCtx->params.maximumRate = maximumRate;
      blastSocketCtx->params.maxBandwidthBurstMsec = maxBandwidthBurstMsec;
   }

   UNLOCK_SESSIONMAP(blastSocketCtx);

   return status;
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocket_GetBlastConnectionInfo --
 *
 *      Query the contents of the BlastConnectionInfo structure
 *
 * Results:
 *      If the structure could be obtained, then the connInfo argument
 *      will be updated, otherwise it will be left unchanged.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

void
BlastSocket_GetBlastConnectionInfo(BlastSocketContext *ctx,       // IN
                                   const char *vAuth,             // IN
                                   BlastConnectionInfo *connInfo) // OUT
{
   BlastSocketVvcSessionWrapper *sessionWrapper;

   if (connInfo == NULL) {
      VVCSESSIONMGRLOG("connInfo is NULL, nothing done.");
      return;
   }

   LOCK_SESSIONMAP(ctx);

   if (FALSE == BlastSocketGetSessionMapEntry(ctx, vAuth, &sessionWrapper)) {
      VVCSESSIONMGRLOG("Entry for vAuth:%" MASK_TOKEN " is not present in "
                       "SessionMap",
                       vAuth);
      UNLOCK_SESSIONMAP(ctx);
      return;
   }

   VVCSESSIONMGRLOG("Entry for vAuth:%" MASK_TOKEN " is present in SessionMap"
                    ", vvcSessionId: %d, vvcSessionHandle: %p",
                    vAuth, sessionWrapper->vvcSessionId, sessionWrapper->sessionHandle);

   *connInfo = sessionWrapper->blastConnInfo;

   UNLOCK_SESSIONMAP(ctx);
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketSetTransportSwitchPolicy --
 *
 *      Records the transport switch policy params applicable for a given
 *      session (identified by vAuth).
 *
 * Results:
 *      Returns TRUE if the transport switch policy params are recorded, FALSE
 *      otherwise.
 *
 *----------------------------------------------------------------------------
 */

Bool
BlastSocketSetTransportSwitchPolicy(BlastSocketContext *blastSocketCtx,                   // IN
                                    const char *vAuth,                                    // IN
                                    const BlastSocketTransportSwitchPolicyParams *params) // IN
{
   ASSERT(blastSocketCtx != NULL);
   ASSERT(vAuth != NULL);
   ASSERT(params != NULL);

   ASSERT(!ISLOCKED_SESSIONMAP(blastSocketCtx));
   LOCK_SESSIONMAP(blastSocketCtx);

   /*
    * TODO: The same switchPolicyParams get applied to all VvcSessions for now.
    *       We could maintain a Map (vAuth, switchPolicyParams) if we want
    *       to apply separate switchPolicyParams for each VvcSession.
    */
   blastSocketCtx->switchPolicyParams = *params;

   UNLOCK_SESSIONMAP(blastSocketCtx);

   VVCSESSIONMGRLOG("TransportSwitchPolicyParams Set for vAuth:%" MASK_TOKEN "", vAuth);
   return TRUE;
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketPushTransportSwitchPolicyToVvcSession --
 *
 *      Push the Transport Switch Policy Params to VvcSession.
 *
 * Results:
 *      Returns TRUE if the transport switch policy params were pushed
 *       to VvcSession, FALSE otherwise.
 *
 *----------------------------------------------------------------------------
 */

Bool
BlastSocketPushTransportSwitchPolicyToVvcSession(BlastSocketContext *blastSocketCtx, // IN
                                                 VvcSessionHandle vvcSessionHandle)  // IN
{
   VvcDataTransportSwitchPolicyParams params;

   ASSERT(blastSocketCtx != NULL);
   ASSERT(ISLOCKED_SESSIONMAP(blastSocketCtx));

   params.transportSwitchCbPeriodMS = blastSocketCtx->switchPolicyParams.transportSwitchCbPeriodMS;

   // Copy-over BEAT to TCP Switch Trigger Parameters
   params.beatToTcpBwKbps = blastSocketCtx->switchPolicyParams.beatToTcpBwKbps;
   params.beatToTcpPktLossPercentage =
      blastSocketCtx->switchPolicyParams.beatToTcpPktLossPercentage;
   params.beatToTcpRttMS = blastSocketCtx->switchPolicyParams.beatToTcpRttMS;
   params.beatToTcpRttVarPercentage = blastSocketCtx->switchPolicyParams.beatToTcpRttVarPercentage;

   // Copy-over TCP to BEAT Switch Trigger Parameters
   params.tcpToBeatBwKbps = blastSocketCtx->switchPolicyParams.tcpToBeatBwKbps;
   params.tcpToBeatRttVarPercentage = blastSocketCtx->switchPolicyParams.tcpToBeatRttVarPercentage;
   params.tcpToBeatRttMS = blastSocketCtx->switchPolicyParams.tcpToBeatRttMS;

   // Copy over other Miscellaneous Switch Policy Params
   params.beatToTcpThreshold = blastSocketCtx->switchPolicyParams.beatToTcpThreshold;
   params.tcpToBeatThreshold = blastSocketCtx->switchPolicyParams.tcpToBeatThreshold;

   params.isSwitchingAlwaysEnabled = blastSocketCtx->switchPolicyParams.isSwitchingAlwaysEnabled;
   params.switchCountMax = blastSocketCtx->switchPolicyParams.switchCountMax;

   // Use VVCLIB_SetSessionSwitchPolicy() to push it to VvcSession
   if (VVCLIB_SetTransportSwitchPolicy(vvcSessionHandle, params) != VVC_STATUS_SUCCESS) {

      VVCSESSIONMGRLOG("VVCLIB_SetTransportSwitchPolicy() Failed for "
                       "vvcSessionHandle: %p",
                       vvcSessionHandle);
      return FALSE;
   }

   VVCSESSIONMGRLOG("VVCLIB_SetTransportSwitchPolicy() Succeeded for "
                    "vvcSessionHandle: %p",
                    vvcSessionHandle);

   // Since switchPolicyParams are pushed to VvcSession, reset in blastSocketCtx
   memset(&blastSocketCtx->switchPolicyParams, 0, sizeof(blastSocketCtx->switchPolicyParams));

   return TRUE;
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketSetVvcQoSPolicy --
 *
 *      Records the Vvc QoS policy params.
 *
 * Results:
 *      Returns TRUE if the transport QoS policy params are recorded, FALSE
 *      otherwise.
 *
 *----------------------------------------------------------------------------
 */

Bool
BlastSocketSetVvcQoSPolicy(BlastSocketContext *blastSocketCtx, // IN
                           const char *vAuth,                  // IN
                           const VvcQoSPolicyParams *params)   // IN
{
   Bool ok = FALSE;
   ASSERT(blastSocketCtx != NULL);
   ASSERT(vAuth != NULL);
   ASSERT(params != NULL);

   ASSERT(!ISLOCKED_SESSIONMAP(blastSocketCtx));
   LOCK_SESSIONMAP(blastSocketCtx);

   ASSERT(blastSocketCtx->params.localVVCQoSPolicyEnabled == TRUE);

   if (BlastSocketValidateQoSPolicy(&blastSocketCtx->qosPolicyParams)) {
      ok = TRUE;
      blastSocketCtx->qosPolicyParams = *params;
      VVCSESSIONMGRLOG("QoSPolicyParams Accepted by BlastSockets "
                       "for vAuth:%" MASK_TOKEN "",
                       vAuth);
      /*
       * XXX: The same QoSPolicyParams get applied to all collab VvcSessions.
       *      This is expected since same user registry hive is shared by
       *      primary and collab sessions and there could be a single QoS Policy
       *      for now.
       */
   } else {
      VVCSESSIONMGRLOG("QoSPolicyParams Not Accepted by BlastSockets "
                       "for vAuth:%" MASK_TOKEN "",
                       vAuth);
   }

   UNLOCK_SESSIONMAP(blastSocketCtx);
   return ok;
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketSetVvcDeferredAcksParams --
 *
 *      Records the VvcDeferredAcksParams into blastSocketCtx.
 *
 * Results:
 *      Returns TRUE if the params are recorded, FALSE otherwise.
 *
 *----------------------------------------------------------------------------
 */

Bool
BlastSocketSetVvcDeferredAcksParams(BlastSocketContext *blastSocketCtx,  // IN
                                    const char *vAuth,                   // IN
                                    const VvcDeferredAcksParams *params) // IN
{
   ASSERT(blastSocketCtx != NULL);
   ASSERT(vAuth != NULL);
   ASSERT(params != NULL);

   ASSERT(!ISLOCKED_SESSIONMAP(blastSocketCtx));
   LOCK_SESSIONMAP(blastSocketCtx);

   blastSocketCtx->deferredAcksParams = *params;
   VVCSESSIONMGRLOG("VVC Deferred Acks Params Accepted by BlastSockets "
                    "for vAuth:%" MASK_TOKEN "",
                    vAuth);
   /*
    * XXX: The same VvcDeferredAcksParams get applied to all collab VvcSessions.
    *      This is expected since same user registry hive is shared by
    *      primary and collab sessions and there could be a single DeferredAcks
    *      param values for now.
    */

   UNLOCK_SESSIONMAP(blastSocketCtx);
   return TRUE;
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketPushQoSPolicyToVvcSession --
 *
 *      Push the QoS Policy Params to VvcSession.
 *
 * Results:
 *      Returns TRUE if the QoS policy params were pushed
 *      to VvcSession, FALSE otherwise.
 *
 *----------------------------------------------------------------------------
 */

Bool
BlastSocketPushQoSPolicyToVvcSession(BlastSocketContext *blastSocketCtx, // IN
                                     VvcSessionHandle vvcSessionHandle)  // IN
{
   ASSERT(blastSocketCtx != NULL);
   ASSERT(ISLOCKED_SESSIONMAP(blastSocketCtx));
   ASSERT(blastSocketCtx->params.localVVCQoSPolicyEnabled);

   // Use VVCLIB_SetQoSPolicy() to push it to VvcSession
   if (VVCLIB_SetQoSPolicy(vvcSessionHandle, blastSocketCtx->qosPolicyParams) !=
       VVC_STATUS_SUCCESS) {

      VVCSESSIONMGRLOG("VVCLIB_SetQoSPolicy() Failed for vvcSessionHandle: %p", vvcSessionHandle);
      return FALSE;
   }

   VVCSESSIONMGRLOG("VVCLIB_SetQoSPolicy() Succeeded for vvcSessionHandle: %p", vvcSessionHandle);

   return TRUE;
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketPushPeerAllowListToVvc --
 *
 *      Push the Peer's Feature Allow List to VVC.
 *
 * Results:
 *      Returns TRUE if the list was pushed to VVC, FALSE otherwise.
 *
 *----------------------------------------------------------------------------
 */

Bool
BlastSocketPushPeerAllowListToVvc(const char *peerFeatureAllowList)
{
   if (VVCLIB_UpdatePeerAllowList(peerFeatureAllowList) != VVC_STATUS_SUCCESS) {
      VVCSESSIONMGRLOG("VVCLIB_UpdatePeerAllowList() Failed for list: %s", peerFeatureAllowList);
      return FALSE;
   }
   return TRUE;
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketConsumeWSPeerConfig --
 *
 *      Check if a (vAuth, wsPeerConfig) pair exists in
 *      BlastSocketContext::wsPeerConfigMap.
 *      If yes, then retrieve it and Remove it from the wsPeerConfigMap.
 *      BlastSocketContext::wsPeerConfigLock must not be held before calling
 *      into this function.
 *
 * Results:
 *      Return false if entry not present, otherwise return true.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

BlastSocketWSPeerConfig
BlastSocketConsumeWSPeerConfig(BlastSocketContext *blastSocketCtx, // IN
                               const char *vAuth)                  // IN
{
   BlastSocketWSPeerConfig *thisWSPeerConfig, retWSPeerConfig;

   ASSERT(blastSocketCtx);
   ASSERT(!ISLOCKED_WSPEERCONFIGMAP(blastSocketCtx));
   ASSERT(blastSocketCtx->wsPeerConfigMap);

   LOCK_WSPEERCONFIGMAP(blastSocketCtx);

   if (BlastSocketGetWSPeerConfigMapEntry(blastSocketCtx, vAuth, &thisWSPeerConfig)) {

      // Save the retrieved WSPeerConfig & Remove and free the MapEntry.
      retWSPeerConfig = *thisWSPeerConfig;

      BlastSocketRemoveWSPeerConfigMapEntry(blastSocketCtx, vAuth);

      // Free up the Map Entry
      free(thisWSPeerConfig->vAuth);
      free(thisWSPeerConfig);
   } else {
      memset(&retWSPeerConfig, 0, sizeof retWSPeerConfig);
   }

   UNLOCK_WSPEERCONFIGMAP(blastSocketCtx);
   return retWSPeerConfig;
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketExistsWSPeerConfigMapEntry --
 *
 *      Check if a (vAuth, wsPeerConfig) pair exists in
 *      BlastSocketContext::wsPeerConfigMap.
 *      BlastSocketContext::wsPeerConfigLock must be held before calling into
 *      this function.
 *
 * Results:
 *      Return false if entry not present, otherwise return true.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

Bool
BlastSocketExistsWSPeerConfigMapEntry(BlastSocketContext *blastSocketCtx, // IN
                                      const char *vAuth)                  // IN
{
   BlastSocketWSPeerConfig **thisWSPeerConfig;

   ASSERT(blastSocketCtx);
   ASSERT(ISLOCKED_WSPEERCONFIGMAP(blastSocketCtx));
   ASSERT(blastSocketCtx->wsPeerConfigMap);

   thisWSPeerConfig =
      (BlastSocketWSPeerConfig **)HashMap_Get(blastSocketCtx->wsPeerConfigMap, (void *)(vAuth));

   if (!thisWSPeerConfig || !*thisWSPeerConfig) {
      VVCSESSIONMGRLOG("WSPeerConfigMap does not have entry for "
                       "vAuth:%" MASK_TOKEN ".",
                       vAuth);
      return FALSE;
   }

   VVCSESSIONMGRLOG("WSPeerConfigMap does have entry for "
                    "vAuth:%" MASK_TOKEN ", "
                    "wsPeerConfig->vAuth:%" MASK_TOKEN,
                    vAuth, (*thisWSPeerConfig)->vAuth);

   return TRUE;
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketGetWSPeerConfigMapEntry --
 *
 *      Check if a (vAuth, wsPeerConfig) pair exists in
 *      BlastSocketVvSessionMgr::wsPeerConfigMap.
 *      BlastSocketVvSessionMgr::wsPeerConfigMapLock must be held before calling
 *      into this function.
 *
 * Results:
 *      Return false if entry not present, otherwise return true.
 *      If the OUT param "wsPeerConfig" is Non-NULL, then it will
 *      hold the wsPeerConfig at the hashMap entry upon exiting.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

Bool
BlastSocketGetWSPeerConfigMapEntry(BlastSocketContext *blastSocketCtx,     // IN
                                   const char *vAuth,                      // IN
                                   BlastSocketWSPeerConfig **wsPeerConfig) // OUT
{
   BlastSocketWSPeerConfig **thisWSPeerConfig;

   ASSERT(blastSocketCtx);
   ASSERT(ISLOCKED_WSPEERCONFIGMAP(blastSocketCtx));
   ASSERT(blastSocketCtx->wsPeerConfigMap);

   thisWSPeerConfig =
      (BlastSocketWSPeerConfig **)HashMap_Get(blastSocketCtx->wsPeerConfigMap, (void *)(vAuth));

   if (!thisWSPeerConfig || !*thisWSPeerConfig) {
      VVCSESSIONMGRLOG("WSPeerConfigMap does not have entry for vAuth:%" MASK_TOKEN ".", vAuth);
      return FALSE;
   }

   VVCSESSIONMGRLOG("WSPeerConfigMap does have entry for "
                    "vAuth:%" MASK_TOKEN ", "
                    "wsPeerConfig->vAuth:%" MASK_TOKEN ".",
                    vAuth, (*thisWSPeerConfig)->vAuth);

   if (wsPeerConfig) {
      *wsPeerConfig = *thisWSPeerConfig;
   } else {
      VVCSESSIONMGRLOG("Invalid args - can not return the retrieved "
                       "WSPeerConfigMap entry.");
      return FALSE;
   }

   return TRUE;
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketAddWSPeerConfigMapEntry --
 *
 *      Add a (vAuth, wsPeerConfig) pair to
 *      BlastSocketContext::WSPeerConfigMap.
 *      BlastSocketContext::WSPeerConfigMapLock must be held before calling into
 *      this function.
 *
 * Results:
 *      Return false if error or entry could not be added,
 *      Otherwise Put the entry and return true.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

Bool
BlastSocketAddWSPeerConfigMapEntry(BlastSocketContext *blastSocketCtx,    // IN
                                   const char *vAuth,                     // IN
                                   BlastSocketWSPeerConfig *wsPeerConfig) // IN
{
   Bool ok = FALSE;

   ASSERT(blastSocketCtx);
   ASSERT(ISLOCKED_WSPEERCONFIGMAP(blastSocketCtx));
   ASSERT(blastSocketCtx->wsPeerConfigMap);

   if (!wsPeerConfig) {
      VVCSESSIONMGRLOG("Invalid args.");
      return FALSE;
   }

   if (BlastSocketExistsWSPeerConfigMapEntry(blastSocketCtx, vAuth)) {

      VVCSESSIONMGRLOG("Hash Collision ! "
                       "Entry for vAuth:%" MASK_TOKEN " already exists.",
                       vAuth);
      return FALSE;
   }

   ok = HashMap_Put(blastSocketCtx->wsPeerConfigMap, (void *)(vAuth), (void *)(&wsPeerConfig));

   if (!ok) {
      VVCSESSIONMGRLOG("Failed to Put entry into wsPeerConfigMap for "
                       "vAuth:%" MASK_TOKEN ", "
                       "wsPeerConfig->vAuth:%" MASK_TOKEN ".",
                       vAuth, wsPeerConfig->vAuth);
      return FALSE;
   }

   VVCSESSIONMGRLOG("Entry added to wsPeerConfigMap for "
                    "vAuth:%" MASK_TOKEN ", "
                    "wsPeerConfig->vAuth:%" MASK_TOKEN ", "
                    "WSPeerConfigMap entry count is now: %d",
                    vAuth, wsPeerConfig->vAuth, HashMap_Count(blastSocketCtx->wsPeerConfigMap));

   return TRUE;
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketRemoveWSPeerConfigMapEntry --
 *
 *      Remove a (vAuth, wsPeerConfig) pair from
 *      BlastSocketContext::wsPeerConfigMap.
 *      BlastSocketContext::wsPeerConfigMapLock must be held before calling into
 *      Function.
 *
 * Results:
 *      Return false if error or entry could not be removed,
 *      Otherwise Remove the entry and return true.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

Bool
BlastSocketRemoveWSPeerConfigMapEntry(BlastSocketContext *blastSocketCtx, // IN
                                      const char *vAuth)                  // IN
{
   Bool ok = FALSE;

   ASSERT(blastSocketCtx);
   ASSERT(ISLOCKED_WSPEERCONFIGMAP(blastSocketCtx));
   ASSERT(blastSocketCtx->wsPeerConfigMap);

   ok = HashMap_Remove(blastSocketCtx->wsPeerConfigMap, (void *)(vAuth));

   if (!ok) {
      VVCSESSIONMGRLOG("Entry for vAuth:%" MASK_TOKEN " was not present "
                       "in the WSPeerConfigMapMap",
                       vAuth);
      return FALSE;
   }

   VVCSESSIONMGRLOG("Entry for vAuth:%" MASK_TOKEN " removed from "
                    "WSPeerConfigMap, Entry count is now: %d",
                    vAuth, HashMap_Count(blastSocketCtx->wsPeerConfigMap));

   return TRUE;
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketLockSessionMap --
 * BlastSocketUnlockSessionMap --
 * BlastSocketIsLockedSessionMap --
 *
 *      Lock, Unlock and IsLocked for BlastSocketContext::sessionMapLock.
 *
 *      XXX: SessionMapLock should not be exposed outside of vvcSessionManager.c
 *           These APIs are being added because a work-around is needed till
 *           the global lock in VncSessionMgr is removed and we use ranked locks
 *
 *           Currently, we need to drop SessionMapLock before invoking
 *           cookieDeleteCb in BlastSocketRemoveCookie() - because AppBlast
 *           layer can call BlastSocket_StopVvcSession() with global lock held.
 *           Since blastSocketAuthMgr does not have access to SessionMapLock,
 *           these APIs are being added.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      SessionMapLock is acquired/released.
 *
 *----------------------------------------------------------------------------
 */

void
BlastSocketLockSessionMap(BlastSocketContext *blastSocketCtx) // IN
{
   ASSERT(blastSocketCtx);
   LOCK_SESSIONMAP(blastSocketCtx);
}

void
BlastSocketUnlockSessionMap(BlastSocketContext *blastSocketCtx) // IN
{
   ASSERT(blastSocketCtx);
   UNLOCK_SESSIONMAP(blastSocketCtx);
}

Bool
BlastSocketIsLockedSessionMap(BlastSocketContext *blastSocketCtx) // IN
{
   ASSERT(blastSocketCtx);
   return ISLOCKED_SESSIONMAP(blastSocketCtx);
}
