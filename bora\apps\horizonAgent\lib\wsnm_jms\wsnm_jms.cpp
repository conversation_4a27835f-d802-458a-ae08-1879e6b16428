/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */


/* */

/* wsnm_jms.cpp */

/* */


/* The reason for this exe is that the java vm is not well behaved. */

/* It crashed the wsnm when trying to unload the wsnm_jmsbridge.dll. */

/* We skip all shutdown of java here, we just exit when needed. */

#include "stdafx.h"
#include <consoleTrace.h>

#include "events.h"
#include "javacommon.h"
#include "svireg.h"
#include "utilWinInet.h"
#include "vdm_plugins.h"
#include "sessionConstants.h"
#include "daasConstants.h"
#include "securitymanager.h"


#define PLUGIN_NAME L"JMSBridge"
#define PLUGIN_DESC L"Bridge between the JMS system and the Orchestrator"

#define UNMANAGED_AGENT L"0"
#define MANAGED_AGENT L"1"


/* the node manager channel */
MessageChannel *l_channel = NULL;

AddressCache *l_addressCache = NULL;

#include "jmsHzMonConsumer.h"
JmsHzMonConsumer g_HzMonConsumer;

static void
ExitWsnmJms(UINT exitCode)
{
   if (g_HzMonConsumer.IsHzMonActive()) {
      if (g_HzMonConsumer.UnloadHzMonApi()) {
         sysmsg(Info, L"Unloaded HzMonApi");
      } else {
         sysmsg(Error, L"Failed to unload HzMonApi.");
      }
   }
   Sleep(exitCode ? 500 : 10);
   coreref::CheckForCleanShutdown(false);
   ExitProcess(exitCode);
}


/*
 *-----------------------------------------------------------------------------
 *
 * javaExitHandler --
 *
 *      Callback from ws_java_bridge when the JVM exits unexpectedly.
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

void __cdecl javaExitHandler(jint shutdownCode)
{
   sysmsg(Fatal, L"The JVM has exited with code %d", shutdownCode);
   ExitWsnmJms(shutdownCode);
}

/*
 *-----------------------------------------------------------------------------
 *
 * EventTypeString --
 *
 *      Convert an HzMon event type to string equivalent.
 *
 * Results:
 *    Event type as a string
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

static tstr
EventTypeString(int type)
{
#define CASE_ENUM_TOSTRING(type)                                                                   \
   case type:                                                                                      \
      return _T(#type)

   switch (type) {
      CASE_ENUM_TOSTRING(AGENT_STARTUP);
      CASE_ENUM_TOSTRING(AGENT_SHUTDOWN);
      CASE_ENUM_TOSTRING(AGENT_RESUME);
      CASE_ENUM_TOSTRING(AGENT_RECONFIGURED);
      CASE_ENUM_TOSTRING(AGENT_CONNECTED);
   default:
      return tstr::printf(_T("AGENT_UNKNOWN_%d"), type);
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * sendHzMonInfo --
 *
 *      Send an HzMon info event.
 *
 * Results:
 *    None
 *
 * Side effects:
 *    HzMon info event is sent to HzMon service.
 *
 *-----------------------------------------------------------------------------
 */

void
sendHzMonInfo(int type, PropertyBag &eventBag)
{
   if (!g_HzMonConsumer.IsHzMonActive()) {
      return;
   }

   // Ensure event type is in bag
   if (!eventBag.contains(L"type")) {
      eventBag.addInt(L"type", type);
   }
   if (!eventBag.contains(L"typeStr")) {
      eventBag.add(L"typeStr", EventTypeString(type));
   }

   g_HzMonConsumer.sendHzMonInfo(eventBag);
}


/*
 *-----------------------------------------------------------------------------
 *
 * sendHzMonError --
 *
 *      Send an HzMon error event.
 *
 * Results:
 *    None
 *
 * Side effects:
 *    HzMon error event is sent to HzMon service.
 *
 *-----------------------------------------------------------------------------
 */

void
sendHzMonError(LPCTSTR errorMsg)
{
   if (!g_HzMonConsumer.IsHzMonActive()) {
      return;
   }

   PropertyBag errorBag;
   errorBag.add(L"ErrorMsg", errorMsg);
   g_HzMonConsumer.sendHzMonError(errorBag);
}

void
sendHzMonError(PropertyBag &errorBag)
{
   if (!g_HzMonConsumer.IsHzMonActive()) {
      return;
   }

   g_HzMonConsumer.sendHzMonError(errorBag);
}


#include "javabridge.h"

/*
 *-----------------------------------------------------------------------------
 *
 * isViewVCManaged --
 *
 *      Is this instance managed by the View broker using VC.
 *
 * Results:
 *    For DaaS: Always return false.
 *    For View: Return true if the 'managed' NM flag is set.
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

bool
isViewVCManaged()
{
   static bool vcManaged =
      wstr::readRegistry(utils::regPath() + L"\\Node Manager\\Managed", MANAGED_AGENT).toBool();
   static bool daasAgent =
      wstr::readRegistry(utils::regPath() + L"\\Node Manager\\DaaSAgent", L"0").toBool();
   return vcManaged && !daasAgent;
}


/*
 *-----------------------------------------------------------------------------
 *
 * closeConsole --
 *
 *      Closes the console. Used in testing
 *
 * Results:
        None
 *
 * Side effects:
 *      Closes the console that this process is running in. This prompts
 *      wsnm_jms.exe to receive a CTRL_CLOSE_EVENT from Windows which will
 *      essentially close this process.
 *
 *-----------------------------------------------------------------------------
 */
#ifdef WSNM_JMS_TEST

void
closeConsole()
{
   HWND consoleWindow = GetConsoleWindow();
   PostMessage(consoleWindow, WM_CLOSE, 0, 0);
}

#endif

/* the Java bridge Orchestrator queue */

class JavaBridgeQueue : public WorkItem {
   /*
    *-----------------------------------------------------------------------------
    *
    * MessageHandler --
    *
    *      MessageHandler
    *
    * Results:
    *    None
    *
    * Side effects:
    *    None
    *
    *-----------------------------------------------------------------------------
    */

public:
   bool MessageHandler(CORE::wstr &hint, PropertyBag &body, PropertyBag &response)
   {
      bool fOk = false;

      if (_wcsicmp(hint, L"Restart") == 0) {
         /* Bug 257967 - Agent not resilient to suspend/resume, let's just exit
          * and let the jmsbridge reload us */

         sysmsg(Debug, L"Got a Restart message, exit to be reloaded");
         PropertyBag body, resp;
         body.set(L"RestartWithParam", L"Resume");
         theBridge.Shutdown(body, resp);
         ExitWsnmJms(0);

      } else if (_wcsicmp(hint, L"Shutdown") == 0) {
         sysmsg(Info, L"java bridge exe stopped by shutdown command");
         theBridge.Shutdown(body, response);
         ExitWsnmJms(0);

      } else if (_wcsicmp(hint, L"ShutdownNotify") == 0) {
         sysmsg(Info, L"java bridge exe notified of shutdown, will terminate");
         theBridge.Shutdown(body, response);
         ExitWsnmJms(0);

      } else if (_wcsicmp(hint, L"GetCurrentServerDn") == 0) {
         response.add(L"SERVERDN", theBridge.m_configBase->get(L"Server DN"));
         fOk = true;

      } else if (_wcsicmp(hint, L"GoAway") == 0) {
         // From wsnm_jmsbridge
         l_channel = NULL;

         if (!theBridge.m_stop) {
            sysmsg(Info, L"JavaBridge queue server un-installed");
            theBridge.Shutdown(body, response);
         }

         ExitWsnmJms(0);

      } else if (_wcsicmp(hint, L"PublishSessionMessage") == 0) {
         if (body.getBool(L"PORTAL", false)) {
            // send our session messages out as-is
            theBridge.PublishJMSMessage(body.get(L"EVENT", L"Unknown"), body.getBag(L"BODY"));
         } else {
            /*
             * We don't send session events for non-portal sessions but we do
             * want the broker to know about our new session information. Send
             * an async update.
             */
            sysmsg(Trace, L"Non-portal-session state change, trigger async update");
            MessageFrameWork::System()->PostMsg(QUEUE_DESKTOPMANAGER, L"SendSessionReport",
                                                PropertyBag(), l_channel);
         }
         fOk = true;
      } else if (_wcsicmp(hint, L"PublishAsyncNotification") == 0) {
         theBridge.PublishAsyncNotification(body);
         fOk = true;
      } else if (_wcsicmp(hint, L"PublishTopicMessage") == 0) {
         if (!body.get(PROP_TOPIC, L"").size()) {
            response.setError(ERROR_INVALID_PARAMETER, L"Missing required topic parameter");
         } else {
            fOk = theBridge.PublishTopicMessage(body);
         }
      } else if (_wcsicmp(hint, L"SendHzMonGuestInfo") == 0) {
         // GuestInfo "OK" is sent when Java connects successfully. Convert that
         // to an AGENT_CONNECTED HzMon event.
         int errorCode = body.getInt(L"ErrorCode", -1);
         if (errorCode == 0) {
            sendHzMonInfo(AGENT_CONNECTED, body);
         } else {
            sendHzMonError(body);
         }
         fOk = true;
      } else if (_wcsicmp(hint, CMD_LOOKUP_ACCOUNT_INFO) == 0) {
         theBridge.SendNotificationToBroker(hint, body);
         fOk = true;
      } else if (_wcsicmp(hint, DAAS_COLLECTOR) == 0) {
         theBridge.SendNotificationToBroker(hint, body);
         fOk = true;
#ifdef WSNM_JMS_TEST
      } else if (_wcsicmp(hint, L"CloseConsole") == 0) {
         closeConsole();
         fOk = true;
#endif
      } else {
         response.setError(ERROR_CALL_NOT_IMPLEMENTED);
      }

      return fOk;
   }


   /*
    *-----------------------------------------------------------------------------
    *
    * BroadcastMsg --
    *
    *      BroadcastMsg
    *
    * Results:
    *    None
    *
    * Side effects:
    *    None
    *
    *-----------------------------------------------------------------------------
    */

   void BroadcastMsg(wstr &eventClass, wstr &eventName, PropertyBag &MessageBody)
   {
      if ((eventClass.comparei(L"Reconfigure") == 0) &&
          (eventName.comparei(QUEUE_CONFIGMGR) == 0)) {

         /* Bug 491280 - we shouldn't tear down JMS if relevant settings are
          * unchanged as that gives a window where the machine is unavailable.
          * Instead, attempt a reconfigure and if that can't complete then
          * reload the process as before.
          */
         bool restartNeeded = !theBridge.Reconfigure();
         bool containsCommonConfig = MessageBody.getBool(L"CommonConfigSet", false);

         sysmsg(Debug,
                L"Got a Reconfigure message, check to be reloaded "
                L"(restartedNeeded=%d, containsCommonConfig=%d)",
                restartNeeded, containsCommonConfig);

         /* Setup, populate and send AGENT_RECONFIGURED event */
         PropertyBag eventBag;
         eventBag.setInt(L"type", AGENT_RECONFIGURED);

         PropertyBag attrBag;
         attrBag.set(PROP_MACHINE_ID,
                     ldapUtils::nameFromDn(theBridge.m_configBase->get(L"Server DN")));
         attrBag.set(PROP_POOL_ID,
                     ldapUtils::nameFromDn(theBridge.m_configBase->get(L"Server Pool DN")));
         attrBag.set(PROP_MACHINE_NAME, theBridge.m_netbiosName);

         eventBag.setBag(L"attributes", attrBag);

         if (Orchestrator::System()->SendMsg(L"EventLoggerService", L"SendEvent", eventBag,
                                             PropertyBag()) != MessageHandler::MsgOk) {
            sysmsg(Warn, L"Could not send reconfigure event");
         }
         sendHzMonInfo(AGENT_RECONFIGURED, eventBag);

         if (restartNeeded) {
            sysmsg(Debug, L"Need to reload the JMS process");
            PropertyBag body, resp;
            body.set(L"RestartWithParam", L"Reconfigure");
            theBridge.Shutdown(body, resp);
            ExitWsnmJms(0);
         }

         bool isManaged = isViewVCManaged();

         // Managed agents with no changes to the common config are done
         if (isManaged && !containsCommonConfig) {
            return;
         }

         /* Bug 346446: Unmanaged (and daas) agents need to acknowledge the
          * Reconfigure command with RECONFIGUREDONE. If the Agent received a
          * Reconfigure message with common config parameters, that needs to be
          * acknowledged too.
          */
         sysmsg(Debug, L"Preparing RECONFIGUREDONE message");

         PropertyBag commonConfigBag;
         MessageFrameWork::System()->SendMsg(QUEUE_CONFIGMGR, L"GetCommonConfig", PropertyBag(),
                                             commonConfigBag, 0, 0, l_channel);

         if (!isManaged) {
            PropertyBag unmanagedConfig;
            MessageFrameWork::System()->SendMsg(QUEUE_CONFIGMGR, L"GetUnmanagedConfig",
                                                PropertyBag(), unmanagedConfig, 0, 0, l_channel);
            commonConfigBag.merge(&unmanagedConfig);
         }

         commonConfigBag.set(L"VCMANAGED", isManaged ? MANAGED_AGENT : UNMANAGED_AGENT);
         wstr reconfigDone = L"RECONFIGUREDONE";
         theBridge.PublishJMSMessage(reconfigDone, commonConfigBag);
      }
   }


   /*
    *-----------------------------------------------------------------------------
    *
    * StateHandler --
    *
    *      StateHandler
    *
    * Results:
    *    None
    *
    * Side effects:
    *    None
    *
    *-----------------------------------------------------------------------------
    */

   void StateHandler(int state)
   {
      if (state == Paused) {
         theBridge.PauseJms();
      }

      if (state == Resumed) {
         theBridge.ResumeJms();
      }
   }
};

/*
 *-----------------------------------------------------------------------------
 *
 * createJavaBridgeQueue --
 *
 *      createJavaBridgeQueue
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

static WorkItem *
createJavaBridgeQueue()
{
   return new JavaBridgeQueue;
}


/*
 *-----------------------------------------------------------------------------
 *
 * ConsoleHandlerRoutine --
 *
 *      ConsoleHandlerRoutine
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

BOOL NTAPI
ConsoleHandlerRoutine(DWORD dwCtrlType)
{
   wstr eventStr;
   switch (dwCtrlType) {
   case CTRL_C_EVENT:
      eventStr = L"CTRL_C_EVENT";
      break;
   case CTRL_CLOSE_EVENT:
      eventStr = L"CTRL_CLOSE_EVENT";
      break;
   default:
      break;
   }


   switch (dwCtrlType) {
   case CTRL_C_EVENT:
   case CTRL_CLOSE_EVENT: {
      sysmsg(Info,
             L"The wsnm_jms process is terminating upon receipt of a "
             L"%s signal.",
             eventStr.c_str());
      PropertyBag bag;
      bag.setBool(L"KeepJmsbridge", true);
      theBridge.Shutdown(bag, PropertyBag());
      ExitWsnmJms(0);
   }

   case CTRL_SHUTDOWN_EVENT: {
      /* We might not be notified any other way so here we should shut the JMS
       * bridge down */
      theBridge.Shutdown(PropertyBag(), PropertyBag());
      return TRUE;
   }

   case CTRL_LOGOFF_EVENT: {
      /* This is what needs to be overridden as it is set by the java vm */
      return TRUE;
   }
   }
   return FALSE;
}


/*
 *-----------------------------------------------------------------------------
 *
 * JavaBridgeReader --
 *
 *      JavaBridgeReader
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

void
JavaBridgeReader(void *args)
{
   theBridge.ReaderThread(*((HANDLE *)args));
   ExitWsnmJms(0);
}


/*
 *-----------------------------------------------------------------------------
 *
 * Init_JavaBridge --
 *
 *      Init_JavaBridge
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

bool
Init_JavaBridge(wstr &param)
{
   theBridge.setState(JmsState::JMS_STATE_INITIALIZING);

   if (g_HzMonConsumer.isConsumerActive()) {
      // Start hzMon monitoring
      DWORD hzMonLogLevel;
      std::string hzMonLog;
      g_HzMonConsumer.LoadHzMonApi("Consumer_wsnm_jms", hzMonLogLevel, hzMonLog);
      sysmsg((SysMessageType)hzMonLogLevel, wstr::to_wstr(hzMonLog.c_str()));

      // Enable callbacks to gather metrics every minute
      g_HzMonConsumer.EnableMetricCallback();
   }

   try {
      if (!theBridge.Startup()) {
         return false;
      }

      /* Change the console handler setup by javavm to stop abnormal
       * terminations */
      SetConsoleCtrlHandler(ConsoleHandlerRoutine, TRUE);

   } catch (...) {
      sendHzMonError(L"Exception starting Java bridge");
      return false;
   }

   try {
      Orchestrator::System()->AddWorker(createJavaBridgeQueue, PLUGIN_NAME, PLUGIN_DESC);
      PropertyBag bag;
      bag.set(L"Name", PLUGIN_NAME);
      bag.set(L"Description", PLUGIN_DESC);

      if (Orchestrator::System()->SendMsg(ORCHESTRATOR_SYSTEM_QUEUE, L"AddWorker", bag,
                                          PropertyBag(), 0, 0,
                                          l_channel) != MessageHandler::MsgOk) {
         sysmsg(Error, L"JavaBridge exe failed to install its remote queue");
         sendHzMonError(L"JavaBridge exe failed to install its remote queue");
         return false;
      }

      sysmsg(Info, L"JavaBridge queue server installed");
   } catch (...) {
      sysmsg(Error, L"JavaBridge exe failed to install its queue");
      sendHzMonError(L"JavaBridge exe failed to install its queue");
      return false;
   }

   /* BUG 230336 - Vista Desktop with Suspend/Power Off power policy applied is
    * not getting launched (launch stuck). The DesktopManager calls
    * winauth::getTrustedDomains before publishing its queue.
    *
    * This may take a long time if we have "dead" domains in the AD trust web,
    * during which the broker gets the error NoQueueHandler when trying to
    * access the DesktopManager after the JMS STARTUP
    */
   sysmsg(Debug, L"Waiting for %s queue...", QUEUE_DESKTOPMANAGER);
   PropertyBag bag;
   bag.set(L"QueueName", QUEUE_DESKTOPMANAGER);
   bag.setInt(L"Timeout", 30 * 60 * 1000);
   Orchestrator::System()->SendMsg(ORCHESTRATOR_SYSTEM_QUEUE, L"IsWorkerInstalled", bag,
                                   PropertyBag(), 0, 0, l_channel);
   sysmsg(Debug, L"Finished waiting for %s queue", QUEUE_DESKTOPMANAGER);

   if (theBridge.m_agentConfig->get(L"Broker").empty()) {
      sysmsg(Info, L"Waiting for broker configuration");
      theBridge.setState(JmsState::JMS_STATE_WAITING_CONFIG);
      while (theBridge.m_agentConfig->get(L"Broker").empty()) {
         if (theBridge.waitOnStop(5000)) {
            sysmsg(Info, L"Stopping, no longer waiting for configuration");
            return false;
         }
      }
   }

   // Bug 539699: Need to wait for customization state from tools.
   if (theBridge.m_configBase->get(TEXT(CI_SYSPREP)).toBool() &&
       utils::isServiceInstalled(L"VMTools", true)) {

      theBridge.setState(JmsState::JMS_STATE_CUSTOMIZING);

      sysmsg(Debug, L"Machine needs to be sysprepped by VMTools");
      bool logged = false;

      bool timeoutForRescheduleLogged = false;
      int timeoutForReschedule =
         theBridge.m_agentConfig->getInt(L"CustomizationRescheduledStallTimeout");
      DWORD startTimeoutCount = GetTickCount();
      sysmsg(Trace, L"Sysprep reschedule timeout value: %d", timeoutForReschedule);

      while (true) {

         int customizationInProgress =
            wstr::readRegistry(L"HKLM\\Software\\VMware, Inc.\\"
                               L"Guest Customization\\CustomizationInProgress",
                               L"-1")
               .toInt();
         int rescheduled = wstr::readRegistry(L"HKLM\\Software\\VMware, Inc.\\"
                                              L"Guest Customization\\Rescheduled",
                                              L"0")
                              .toInt();

#ifdef _WIN64
         // Fall back to checking value in 32-bit registry
         if (customizationInProgress == -1) {
            HKEY hKey = NULL;
            if (RegOpenKeyExW(HKEY_LOCAL_MACHINE, L"Software\\VMware, Inc.\\Guest Customization",
                              0L, KEY_QUERY_VALUE | KEY_WOW64_32KEY, &hKey) == ERROR_SUCCESS) {
               customizationInProgress =
                  wstr::readRegistry(hKey, L"CustomizationInProgress", L"-1").toInt();
               rescheduled = wstr::readRegistry(hKey, L"Rescheduled", L"0").toInt();
               RegCloseKey(hKey);
            }
         }
#endif

         sysmsg(Trace, L"CustomizationInProgress is %d", customizationInProgress);

         if (customizationInProgress == 0) {
            sysmsg(Debug, L"Customization not in progress");
            if (logged) {
               sysmsg(Info, L"Customization completed, continuing");
            }
            break;
         }

         if (!logged) {
            sysmsg(Info, L"Waiting for customization to complete");
            logged = true;
         }

         if (rescheduled && (timeoutForReschedule > 0)) {
            DWORD maxTicks = (DWORD)timeoutForReschedule * 1000;
            if ((GetTickCount() - startTimeoutCount) > maxTicks) {
               bool fixAttempted =
                  theBridge.m_agentConfig->getBool(L"CustomizationRescheduledFixAttempted");
               if (!fixAttempted) {
                  sysmsg(Warn, L"Sysprep stall timeout hit, requesting reboot");
                  /*
                   * Mark as fix attempted to stop future attempts (this is one
                   * time only), post a reboot request and carry on, we will
                   * sit in waitOnStop below while it is actioned.
                   */
                  wstr regValue(L"true");
                  regValue.writeRegistry(
                     utils::regPath() +
                     L"\\Agent\\Configuration\\CustomizationRescheduledFixAttempted");
                  MessageFrameWork::System()->PostMsg(QUEUE_DESKTOPMANAGER, L"Reboot",
                                                      PropertyBag(), l_channel);
               } else {
                  if (!timeoutForRescheduleLogged) {
                     timeoutForRescheduleLogged = true;
                     sysmsg(Warn, L"Sysprep stall timeout hit, fix already attempted");
                  }
                  sysmsg(Debug, L"Sysprep stall timeout hit, fix already attempted");
               }
            }
         }

         if (theBridge.waitOnStop(5000)) {
            sysmsg(Debug, L"Stop event while waiting for customization");
            if (logged) {
               sysmsg(Info, L"Stopping, no longer waiting for customization");
            }
            return false;
         }
      }
   }

   /*
    * If the machine needs to be rebooted and the NotifyVdmStatusValue is set
    * to CustomizationPostCheckpointing then send the CUSTOMIZATIION to the
    * Broker.
    */
   if (SviReg::customizationInfoNeeded()) {
      wstr custStr = L"postSnapshotting";
      theBridge.Customization(custStr);
   }
   // Wait for SVI state if component installed
   NotifyVdmStatusValue vdmState;
   SvmPolicyState svmState;
   LONG timeWaited = SviReg::getState(vdmState, svmState, -1);

   /*
    * The first time we're reported as ready we know we need a checkpoint
    * operation, so we use the existing volatile NeedReboot key the agent
    * already checks for. If anything is set then the agent will report itself
    * as unavailable with state AGENT_ERR_NEED_REBOOT.
    *
    * TODO: We can use the vdmState directly in wsnm_desktop and avoid this
    */
   if (vdmState == WaitForCheckpointing) {
      wstr needRebootValue = L"HKLM\\Software\\Omnissa\\NeedReboot\\Components";

      wstr rebootComponents = wstr::readRegistry(needRebootValue, L"");
      if (rebootComponents.find(L"checkpointing") == (size_t)-1) {
         rebootComponents.insert(0, L"checkpointing;");
         utils::writeRegistryVolatileString(needRebootValue, rebootComponents, false);
      }
      sysmsg(Debug, TEXT("Setting need reboot flag to enforce checkpoint: %s"), rebootComponents);
   }

   /*
    * JavaBridgeReader thread is responsible for initing Main and
    * running mainObject.Start(...). Will set the event when this has
    * been completed.
    */
   HANDLE hevent = CreateEvent(0, TRUE, FALSE, 0);
   theBridge.setState(JmsState::JMS_STATE_WAITING_JAVA);

   if (!corerun::run("JavaBridgeReader", JavaBridgeReader, &hevent)) {
      sendHzMonError(L"Unable to create Java Bridge Reader");
      return false;
   }

   // Allow the reader thread init timeout to be overridden.
   DWORD readerInitTimeout =
      wstr::readRegistry(wstr::printf(L"%s\\Node Manager\\JmsReaderInitTimeout", utils::regPath()),
                         L"180000")
         .toInt();

   /* wait for reader init to complete before accepting the java vm to be up */
   HANDLE events[] = {hevent, theBridge.m_stopEvent};
   DWORD waitResult = WaitForMultipleObjects(2, events, FALSE, readerInitTimeout);
   if (waitResult != WAIT_OBJECT_0) {
      // hevent not set
      wstr msg;
      if (theBridge.m_stop) {
         msg = L"aborted, process stopping";
      } else if (waitResult == WAIT_TIMEOUT) {
         msg = L"timed out";
      } else {
         msg = wstr::printf(L"returned %u", waitResult);
      }
      sysmsg(Error, L"JavaBridge reader thread init: %s", msg.p());
      sendHzMonError(L"JavaBridge reader init failed");
      return false;
   }

   // Register JMS as the message transport so that it will receive notify messages
   sysmsg(Debug, L"Registering JMS bridge with %s queue", QUEUE_DESKTOPMANAGER);
   PropertyBag notifyBag;
   notifyBag.add(L"QUEUE", QUEUE_JMSBRIDGE);
   bool ok = MessageFrameWork::MsgOk ==
             MessageFrameWork::System()->SendMsg(QUEUE_DESKTOPMANAGER,
                                                 L"NotifyQueueRegisterMessageTransport", notifyBag,
                                                 PropertyBag(), 0, 0, l_channel);
   sysmsg(Trace, L"%s registered with %s queue", ok ? L"Successfully" : L"Unsuccessfully",
          QUEUE_DESKTOPMANAGER);
   if (!ok) {
      sysmsg(Error, L"Unable to register JMS message transport queue, aborting");
      sendHzMonError(L"Unable to register JMS message transport queue");
      return false;
   }

   wstr startMessage = L"STARTUP";
   PropertyBag messageProps;


   /* For VC-managed VMs, we send STARTUP message both for a startup and
    * reconfigure. For unmanaged machines, we will send STARTUP when invoked
    * without parameters, and RECONFIGUREDONE if invoked with parameter
    * "Reconfigure". For unmanaged machines, STARTUP and RECONFIGUREDONE
    * messages also contain configuration information as string properties.
    */

   bool attachUnmanagedConfig = false;
   bool attachCommonConfig = false;
   if (param.size()) {
      sysmsg(Trace, L"JavaBridge started with param = %s", param);

      if (_wcsnicmp(param, L"Resume", 6) == 0) {
         startMessage = L"RESUME";
      } else if (_wcsnicmp(param, L"Reconfigure", 11) == 0) {
         attachCommonConfig = true;
         /* For unmanaged View agents, and all DaaS agents, send a
          * RECONFIGUREDONE message over JMS. For managed View agents
          * we keep the existing behaviour which is to send the STARTUP
          * message
          */
         if (!isViewVCManaged()) {
            startMessage = L"RECONFIGUREDONE";
            attachUnmanagedConfig = true;
         }
      }
   }
   /* invoked without params */
   else {
      attachCommonConfig = true;
      if (!isViewVCManaged()) {
         attachUnmanagedConfig = true;
      }
   }

   if (attachUnmanagedConfig) {
      PropertyBag unmanagedConfig;

      MessageFrameWork::System()->SendMsg(QUEUE_CONFIGMGR, L"GetUnmanagedConfig", PropertyBag(),
                                          unmanagedConfig, 0, 0, l_channel);

      messageProps.merge(&unmanagedConfig, true);
   }

   if (attachCommonConfig) {
      PropertyBag commonConfigBag;
      MessageFrameWork::System()->SendMsg(QUEUE_CONFIGMGR, L"GetCommonConfig", PropertyBag(),
                                          commonConfigBag, 0, 0, l_channel);
      messageProps.merge(&commonConfigBag);
   }

   if (attachCommonConfig || attachUnmanagedConfig) {
      messageProps.set(L"VCMANAGED", isViewVCManaged() ? MANAGED_AGENT : UNMANAGED_AGENT);
   }

   SviReg::insert(messageProps, svmState, timeWaited);
   SviReg::setNgvcCheckpointingState(messageProps, vdmState);

   sysmsg(Info, L"View customization state %d (in %ld seconds)", svmState, timeWaited);

   // Send the event
   PropertyBag eventBag;
   int type = -1;
   if (_wcsicmp(startMessage, L"STARTUP") == 0) {
      type = AGENT_STARTUP;
   } else if (_wcsicmp(startMessage, L"RESUME") == 0) {
      type = AGENT_RESUME;
   } else if (_wcsicmp(startMessage, L"RECONFIGUREDONE") == 0) {
      sysmsg(Debug, L"StartMessage type RECONFIGUREDONE received.");
   } else {
      sysmsg(Warn, L"Unknown startMessage event type %s", startMessage);
   }

   // get rdsh load balancing settings from the broker on agent startup or resume
   coreosver ver;
   if (ver.isTerminalServer() && (type == AGENT_STARTUP || type == AGENT_RESUME)) {
      messageProps.add(L"GETLBSETTINGS", L"true");
   }

   if (type == AGENT_STARTUP) {
      PropertyBag versionBag;

      MessageFrameWork::System()->SendMsg(QUEUE_DESKTOPMANAGER, L"GetAgentVersion", PropertyBag(),
                                          versionBag, 0, 0, l_channel);

      messageProps.set(L"AGENTVERSION", versionBag.get(L"Version", L""));
   }

   if (type != -1) {
      eventBag.setInt(L"type", type);

      PropertyBag resp;
      if (Orchestrator::System()->SendMsg(QUEUE_DESKTOPMANAGER, L"SendEvent", eventBag, resp, 0, 0,
                                          l_channel) != MessageHandler::MsgOk) {
         sysmsg(Warn, L"Unable to send message (%s): %s", eventBag.flattenForDisplay(),
                resp.flattenForDisplay());
      }
      sendHzMonInfo(type, eventBag);

      /*
       * Bug 1599499 - SSO fails for child domain and trust domain
       * users when logging into instant clones on child and trust domains.
       *
       * The domain join workflow for NGVC is different. The internal
       * template is joined to the domain and rebooted. However the
       * clone only has its name and machine password updated,
       * after this there is no reboot.
       *
       * This leads to wsnm_desktop discovering the primary domain before customization
       * has updated the machine password. In turn this leads to DsGetDcName
       * failures during trusted domain enumeration.
       */
      if (vdmState == CustomizationSucceeded && SviReg::isNGVCAgentManaged()) {
         PropertyBag updateDomainsBag;
         sysmsg(Info, L"Sending UpdateDomains message");

         MessageFrameWork::System()->PostMsg(QUEUE_DESKTOPMANAGER, L"UpdateDomains",
                                             updateDomainsBag, l_channel);
      }
   }

   theBridge.PublishJMSMessage(startMessage, messageProps);

   /*
    * In case of Nutanix setup, wsnm_jms.exe needs to set the
    * NotifyVdmStatusValue to CustomizationPostCheckpointing, so that after the
    * snapshot is done, and the agent is rebooted, the NotifyVdmStatusValue
    * reflects correct state of the machine. Also after that, the STARTUP
    * message will go with the flag SVICHECKPOINTSUPPORTED set to 0.
    */
   if (vdmState == WaitForCheckpointing) {
      SviReg::setNotifyVdmStatusValue(CustomizationPostCheckpointing);
   }

   /* Bug 520731: If we are asked to restart while waiting for SVI quickprep to
    * complete, then we need to ensure that we always send a STARTUP message
    * instead of RESUME the first time. After we've sent a message out, tell
    * the bridge that we have sent a STARTUP.
    */
   if (l_channel) {
      PropertyBag bag;
      bag.add(L"SERVERDN", theBridge.m_configBase->get(L"Server DN"));
      MessageFrameWork::System()->PostMsg(L"JMSBridgeLoader", L"StartupSent", bag, l_channel);
   }

   theBridge.storeNetBiosNameForShutdown();

   return true;
}


/*
 *-----------------------------------------------------------------------------
 *
 * AbortChannel --
 *
 *      AbortChannel
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

void
AbortChannel(Orchestrator::ChannelType type, Orchestrator::ChannelEvent theEvent,
             MessageChannel *pChannel)
{
   if (theEvent == Orchestrator::Closed) {
      l_channel = NULL;

      if (!theBridge.m_stop) {
         sysmsg(Trace, L"java bridge exe stopped by channel close.");
         theBridge.Shutdown(PropertyBag(), PropertyBag());
      }

      ExitWsnmJms(0);
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * getJmsMetrics --
 *
 *    Read JMS metrics from Java bridge.
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

void
getJmsMetrics(wstr &state, int &outgoingQueueSize, int &incomingQueueSize)
{
   theBridge.getJmsMetrics(state, outgoingQueueSize, incomingQueueSize);
}


/*
 *-----------------------------------------------------------------------------
 *
 * mymain --
 *
 *      mymain
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

void
mymain(wstr &param)
{
   corerunnable::setThreadName("Main Thread");
   SysMessageModule(0);

   InstallUnhandledExceptionFilter();
   _InstallExceptionFilter();

   /*
    * We have a bug in Oracle's java net.dll that may cause deadlock.
    * Here we pre initialize the javabridge without any threads created
    * so that we can load the needed dll's without thread contention.
    */
   if (!theBridge.Pre_Startup()) {
      ExitWsnmJms(0);
   }

   consoleTrace cons(true);

   corerunnable::setThreadName("Main Thread");

   /*
    * Bug 548281 - Runtime Error in wsnm_jms when uninstalling.
    * We get a runtime abort call and we don't know why (we do know that
    * wsnm_jms does call ExitProcess and I beleive that the runtime gets
    * itself in a twist somewhere in it's exit handling).
    * Here we suppress the error messages when abort is called.
    */
   _set_abort_behavior(0, -1);

   /*
    * We need to set our shutdown to be after the SCM to make sure we don't get
    * terminated before the SCM has had a chance to shut us down normally. The
    * SCM is at 0x1E0.
    */
   SetProcessShutdownParameters(0x1D0, 0);


   /* clear the process feedback cursor */
   MSG Msg;

   if (PostThreadMessage(GetCurrentThreadId(), WM_USER, 0, 0)) {
      GetMessage(&Msg, 0, 0, 0);
   }

   CoInitializeEx(0, COINIT_MULTITHREADED);


   /* TODO: Massage the DLL path to ensure that the Java JNI DLLs can be found. */

   coreevent theEvent = coreevent("framework::wsnm_jms.exe");

   if (theEvent.existedBefore) {
      sysmsg(Warn, L"Java bridge exe is already running.");
   } else {
      MessageFrameWork::Start(MessageFrameWork::Client, AbortChannel, 1);
      MessageFrameWork::Ready();

      l_addressCache = new AddressCache;
      l_channel = MessageFrameWork::System()->ConnectChannel(MessageFrameWork::SharedMemChannel);

      bool isolatedJmsBridge =
         wstr::readRegistry(utils::regPath() + L"\\Node Manager\\JVM\\IsolatedJmsBridge", L"false")
            .toBool();
      if (isolatedJmsBridge) {
         sysmsg(Debug, L"Skipping wsnm_jmsbridge plugin check, isolated");
      }

      if ((l_channel) &&
          (isolatedJmsBridge || (MessageFrameWork::System()->SendMsg(
                                    MESSAGE_FRAMEWORK_SYSTEM_QUEUE, L"IsPluginLoaded",
                                    PropertyBag(L"FileName=wsnm_jmsbridge", NULL), PropertyBag(), 0,
                                    0, l_channel) == MessageHandler::MsgOk))) {
         if (Init_JavaBridge(param)) {
            sysmsg(Info, L"java bridge exe started ok.");


            /* hang around until killed */
            HANDLE handle = CreateEvent(0, TRUE, FALSE, 0);
            WaitForSingleObject(handle, INFINITE);
         }
      } else {
         sysmsg(Info, L"java bridge exe started when java bridge dll is not running.");
         /*
          * TODO: Process exits before logging is up to date. Don't want to
          * wait for threads below in case it causes behaviour change, so just
          * include a slight delay for now.
          */
         Sleep(500);
      }
   }

   if (l_addressCache) {
      delete l_addressCache;
   }

   ExitWsnmJms(0);
}


/* NOTE: you can build this exe either as a Windows app or as a console app */

/*      the console mode is to be able to run it in foreground to get trace
 * messages */


/* The program entry for build with /SUBSYSTEM:WINDOWS */

int APIENTRY
wWinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPTSTR lpCmdLine, int nCmdShow)
{
   mymain(wstr());
   return 0;
}


/* The program entry for build with /SUBSYSTEM:CONSOLE */

int
main(int argc, char **argv)
{
   wstr param;

   if (argc >= 2) {
      param = mstr(*(argv + 1))._wstr();
   }

   mymain(param);
   return 0;
}
