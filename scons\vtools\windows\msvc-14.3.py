# Copyright (c) Omnissa, LLC. All rights reserved.
# This product is protected by copyright and intellectual property laws in the
# United States and other countries as well as by international treaties.
# -- Omnissa Restricted

"""Visual Studio 2022 and Windows SDK 10 vTool

Configure the Visual Studio 2022 / MSVC 14.3 / cl.exe 19.x compiler and
related tools. Also use the Windows 10 SDK.

"""

import os
import platform
import vmware
import vmware.visualstudio
import vtools.common
import SCons.Script


def generate(env):

    log = vmware.GetLogger("vtools")

    # In msvc14 C++11 is always-on.
    env["USE_CPP11"] = True

    # Set tool version and target frame work version based on targetted msvc
    # compiler version. This allows a single CS proj to compile in multiple
    # version of compiler.
    # Note: MSBuild switched to "Current" as the TOOLS_VERSION with VS2019.
    env["TOOLS_VERSION"] = "Current"
    env["TARGET_FRAMEWORK_VERSION"] = "v4.7"

    # We must use vmware.RequestedBuildType() because
    # this may run before env.BuildType() has been setup.
    buildType = vmware.RequestedBuildType()

    if "windows-host" not in env["VTOOLS"]:
        raise vmware.ScriptError("Load windows-host instead of msvc-XXX")

    # Note: we expect this specific alias (see the getMsvcVersion definition
    # in windows-host.py for more info).
    if vmware.GetConanBuildComponent(env, "msvc_desktop") is None:
        raise vmware.ScriptError(
            "VS2022 builds are only supported in SCons " "using conan msvc_desktop."
        )

    log.info("Using Visual Studio 2022 from cayman")

    # Use Visual Studio from Cayman.
    baseDir = os.path.join(
        vmware.GetConanBuildComponent(env, "msvc_desktop").package_folder, "win"
    )

    buildToolsRoot = os.path.join(baseDir, "VS", "2022", "BuildTools")
    msbuildBinPath = os.path.join(
        buildToolsRoot,
        "Msbuild",
        "Current",
        "Bin",
        "amd64" if platform.architecture()[0] == "64bit" else "",
    )
    referencePath = os.path.join(
        buildToolsRoot, "Common7", "IDE", "ReferenceAssemblies", "v4.0"
    )
    frameworkParentPath = os.path.join(
        baseDir, "Reference Assemblies", "Microsoft", "Framework", ".NETFramework"
    )
    frameworkSubdirs = os.listdir(frameworkParentPath)
    frameworkSubdir = sorted([d for d in frameworkSubdirs], reverse=True)[0]
    frameworkPath = os.path.join(frameworkParentPath, frameworkSubdir)
    frameworkVersion = frameworkSubdir.replace("v", "")
    frameworkPathMap = {}
    for d in frameworkSubdirs:
        frameworkPathMap[d] = os.path.join(frameworkParentPath, d)
    env["FRAMEWORKPATHMAP"] = frameworkPathMap

    msvcParent = os.path.join(buildToolsRoot, "VC", "Tools", "MSVC")
    msvcDirs = [
        dir
        for dir in os.listdir(msvcParent)
        if os.path.isdir(os.path.join(msvcParent, dir)) and dir.startswith("14")
    ]
    if len(msvcDirs) == 0:
        raise vmware.ScriptError(
            "Unable to find the VC tools " "directory in %s." % msvcParent
        )
    elif len(msvcDirs) > 1:
        raise vmware.ScriptError(
            "Unexpectedly found multiple VC directories "
            "in %s: %s." % (msvcParent, msvcDirs)
        )

    msvcVersion = msvcDirs[0]  # The dir name also is the version of msvc.
    msvcDir = os.path.join(msvcParent, msvcDirs[0])
    msvcRedistDir = vmware.visualstudio.GetMsvcRedistDir(env, buildType)
    winRedistDir = vmware.visualstudio.GetWindowsRedistDir(env, buildType)
    msmDir = vmware.visualstudio.GetMsvcRedistMsmDir(env, buildType)

    adkDir = os.path.join(
        baseDir,
        "Windows Kits",
        "10",
        "Assessment and Deployment Kit",
        "Deployment Tools",
        "SDKs",
        "DismApi",
    )
    if os.path.isdir(adkDir):
        env.Replace(ADKROOT=env.Dir(adkDir))

    env.Replace(
        VCINSTALLDIR=env.Dir(os.path.join(buildToolsRoot, "VC")),
        MSVCROOT=env.Dir(msvcDir),
        SDKROOT=env.Dir(os.path.join(baseDir, "Windows Kits", "10")),
        MSMROOT=env.Dir(msmDir),
        MSBUILDBINPATH=env.Dir(msbuildBinPath),
        REFERENCEPATH=env.Dir(referencePath),
        FRAMEWORKPATHOVERRIDE=env.Dir(frameworkPath),
        CHECKFRAMEWORK=env.Dir(baseDir).Dir("BuildEnv").File("CheckFramework.exe"),
    )

    env.AppendENVPath(
        "MSBuildFrameworkToolsPath", vmware.visualstudio.FindNetFramework45Plus()
    )
    env.AppendENVPath("BuildLabSetupFilesRoot", baseDir)

    isHost64Bit = env.Host().Is64Bit()
    isHostARM64 = env.Host().IsARM64()
    isHostARM64EC = env.Host().IsARM64EC()
    if isHostARM64 or isHostARM64EC:
        arch = adkArch = "arm64"
        sdkBinArch = "x64"
    elif isHost64Bit:
        arch = sdkBinArch = "x64"
        adkArch = "amd64"
    else:
        arch = adkArch = sdkBinArch = "x86"

    for candidate in ["Professional", "BuildTools"]:
        checkPidPath = os.path.join(
            baseDir, "VS", "2022", candidate, "Common7", "IDE", "CheckPid.exe"
        )
        if os.path.isfile(checkPidPath):
            # We are getting Visual Studio from Cayman and each individual
            # developer needs to license their dev machine.
            vmware.visualstudio.TestLicensing(checkPidPath)
            break
    else:
        log.warning(
            "Unable to find CheckPid.exe in msvc_desktop from Visual Studio 2022."
        )

    # Redistributables are only available from Conan.
    vmware.visualstudio.AddCRTVarsToEnv(env)
    if buildType == "obj":
        vmware.visualstudio.AddCRTNonRedistsVarsToEnv(env)

    # On a clean host we need some extra directories in the PATH to access
    # some additional dlls that compile-time binaries (e.g. the vc14 protobuf
    # emitter) will need to run. On obj builds examples include ucrtbased.dll,
    # vcruntime140d.dll, and msvcp140d.dll. On non-obj builds examples include
    # vcruntime140.dll and msvcp140.dll.
    if buildType == "obj":
        vmware.visualstudio.AddMsvcNonRedistsToPath(env)
    else:
        vmware.visualstudio.AddMsvcRedistsToPath(env)

    msvcRoot = env["MSVCROOT"].abspath
    sdkRoot = env["SDKROOT"].abspath
    msmRoot = env["MSMROOT"].abspath
    msvcRedistRoot = msvcRedistDir.abspath
    winRedistRoot = winRedistDir.abspath
    msbuildBinPath = env["MSBUILDBINPATH"].abspath
    adkRoot = env["ADKROOT"].abspath if "ADKROOT" in env else ""

    log.info("msvc-14.3: MSVCROOT=%s" % msvcRoot)
    log.info("msvc-14.3: SDKROOT=%s" % sdkRoot)
    log.info("msvc-14.3: MSMROOT=%s" % msmRoot)
    log.info("msvc-14.3: MSBUILDBINPATH=%s" % msbuildBinPath)
    if adkRoot:
        log.info("msvc-14.3: ADKROOT=%s" % adkRoot)

    # The version of the SDK can be found in a few different places, but this
    # script finds it by looking at the include directory.
    incdirs = os.listdir(os.path.join(sdkRoot, "include"))
    sdkVersion = sorted([d for d in incdirs if d.startswith("10.")], reverse=True)[0]

    ### Load VTools

    env.LoadTool("pdb")
    # msvc-common needs to be loaded after pdb
    env.LoadTool("msvc-common")
    # For bora\scons\scripts\cl-wrap.py
    env.LoadTool("python-2.7")

    ### Setup Env
    # Our actual build machines are always 64-bit so we use HostX64 directory
    # for our tools to get the extra address space. env.Host().Is64Bit() is
    # checking the desired build output architecture, not the build host.
    # Build using the x64 compiler for x64 build machines.
    # Build using the x86 cross-compiler for x64 build machines.
    sdkBinPath = os.path.join(sdkRoot, "bin", sdkVersion, sdkBinArch)
    msvcBinRoot = env.Dir(os.path.join(msvcRoot, "bin", "HostX64", arch))

    # Needed for mspdbcore.dll.
    env.AppendENVPath("PATH", os.path.join(msvcRoot, "bin", "HostX64", "x64"))

    # Needed in case modules need to add more subfolders to CPPPATH and
    # LIBPATH (e.g. km).
    env["SDKLIBROOT"] = env["SDKROOT"].Dir("lib").Dir(sdkVersion)
    env["SDKBINROOT"] = env["SDKROOT"].Dir("bin").Dir(sdkVersion)
    env["SDKINCLUDEROOT"] = env["SDKROOT"].Dir("include").Dir(sdkVersion)
    if adkRoot:
        env["ADKLIBROOT"] = env["ADKROOT"].Dir("lib").Dir(adkArch)
        env["ADKINCLUDEROOT"] = env["ADKROOT"].Dir("include")

    # Parse the MSVC version based on the version subdirectory we found
    # earlier. For example, U0 is 14.30.30705. We want to parse this into the
    # tuple (14, 2, 0).
    log.info("MSVC version string is %s." % msvcVersion)
    msvcVersionList = msvcVersion.split(".")
    if len(msvcVersionList) < 2:
        raise vmware.ScriptError(
            "Unable to parse MSVC version string %s." % msvcVersion
        )
    msvcVersionTuple = (
        int(msvcVersionList[0]),
        int(msvcVersionList[1]) // 10,
        int(msvcVersionList[1]) % 10,
    )

    # v143 actually applies to 14.3 and 14.4 because Microsoft is onto .10 releases
    # and on, so this check allows for both.
    if msvcVersionTuple[0] != 14 or msvcVersionTuple[1] not in [3, 4]:
        raise vmware.ScriptError(
            "Parsing VS2022 version %s resulted in a "
            "version that was not 14.3 or 14.4: %s." % (msvcVersion, msvcVersionTuple)
        )

    env.Replace(
        MSVC_VERSION="msvc-14.3",
        MSVC_VERSION_TUPLE=msvcVersionTuple,
        # VS2022 is still providing mfc libraries with '140' in the name
        # instead of '143', so keep MFC_VERSION at '140'.
        MFC_VERSION="140",
        # It is best to leave the VC_SUFFIX as 'vc140' because cayman builds
        # will continue to produce builds with a suffix 'vc140' for vc14 builds
        # regardless of the exact 14.x version.
        VC_SUFFIX="vc140",
        SDKBINPATH=env.Dir(sdkBinPath),
        MSVCBINROOT=env.Dir(msvcBinRoot),
        MSBUILD="${_file(MSBUILDBIN, __env__)}",
        MSBUILDBIN=os.path.join(msbuildBinPath, "MSBuild.exe"),
        # TODO: older msvc VTools defined MSCOMMONROOT. However that no longer
        # makes sense as the file layouts have changed in Windows Kit v10.
        # Resolving usage of MSCOMMONROOT is a work in progress.
        USING_SDK60=False,
        USING_SDK60_OR_LATER=True,
        USING_SDK61=False,
        USING_SDK61_OR_LATER=True,
        USING_SDK70=False,
        USING_SDK70_OR_LATER=True,
        USING_SDK71=False,
        USING_SDK71_OR_LATER=True,
        USING_SDK80=False,
        USING_SDK80_OR_LATER=True,
        USING_SDK81=False,
        USING_SDK81_OR_LATER=True,
        USING_SDK10=True,
        USING_SDK10_OR_LATER=True,
        # Use warning level 3 for warning 4062 (unhandled enums in `switch`
        # statements). See bug 1760971.
        SWITCH_ENUM_WARNING_OPT="-w34062",
        WindowsSDKVersion=sdkVersion,
    )

    ### Setup options

    cc_globalopts = ["-nologo"]
    arflags = ["-nologo"]
    if isHostARM64EC:
        arflags = ["/machine:arm64ec"]

    # Note that unlike earlier msvc VTools we do not add -map and -mapinfo.
    linkflags = ["-nologo"]

    # Generate debug information. Implies -incremental.
    linkflags += ["-debug"]

    # Optionally support verbose output to logging to help debug defaultlib
    # issues. Goes well paired with DISPLAY_COMMAND_OUTPUT=True.
    if vmware.LocalOpts.GetBool("VERBOSE_LINK", False):
        linkflags += ["-verbose"]

    # Security flags which should not be disabled:
    #    1. ASLR / layout randomization. This is useful to make exploits more
    #       difficult due to the base address being randomized.
    #    2. Control flow guard. This is useful particularly for processes
    #       running as SYSTEM or with elevated permissions because it opts into
    #       a feature available in later versions of Windows where if an active
    #       memory corruption exploit is detected, the application is terminated
    #       rather than allowed to proceed.
    #    3. Spectre mitigations (optional but on by default). This tells the
    #       compiler to insert instructions to mitigate certain Spectre
    #       security vulnerabilities.
    linkflags += [
        "-DynamicBase",
        "/guard:cf",
    ]
    cc_globalopts += [
        "/guard:cf",
    ]

    spectreEnabled = vmware.LocalOpts.GetBool("ENABLE_SPECTRE_MITIGATIONS", True)
    if spectreEnabled:
        cc_globalopts.append("/Qspectre")

    if vmware.UseMsvcStaticAnalyzer():
        cc_globalopts += [
            # Enable static code analysis
            "/analyze",
            # Disable static code analysis warnings being treated as errors,
            # even when /WX is enabled for normal compiler warnings
            "/analyze:WX-",
            # Log code analysis output to a file with name ${TARGET}.sarif
            "/analyze:log${TARGET}.sarif",
        ]
        # Exclude gobuild components and toolchain from static analysis
        env.AppendENVPath(
            "CAEXCLUDEPATH",
            [
                vmware.DirAbsPath(vmware.ComponentsRoot()),
            ],
        )

    sanitizers = vmware.UseSanitizers()
    for sanitizer in sanitizers:
        sanitizerCcOpts = {
            "address": [
                # Flag to enable address sanitizer.
                "-fsanitize=address",
                # Disable C5072 because some components don't emit debug information, including
                # compiler-generated midl files. More investigation is needed there.
                "/wd5072",
                # Disable annotations as recommended in
                # https://learn.microsoft.com/en-us/cpp/sanitizers/error-container-overflow?view=msvc-170
                # until we have Conan builds with asan enabled and have confirmed that all other
                # static libraries we use have asan enabled.
                "-D_DISABLE_VECTOR_ANNOTATION",
                "-D_DISABLE_STRING_ANNOTATION",
            ],
        }
        if sanitizer in sanitizerCcOpts:
            cc_globalopts += sanitizerCcOpts[sanitizer]
        else:
            log.info(
                "Unknown sanitizer %s. Only the following are supported: %s"
                % (sanitizer, list(sanitizerCcOpts.keys()))
            )

    if isHostARM64EC:
        linkflags.extend(["/machine:arm64ec"])
        cc_globalopts += ["/arm64EC"]
    elif isHostARM64:
        linkflags.extend(["-machine:arm64"])
    elif isHost64Bit:
        linkflags.extend(["-machine:X64"])
    else:
        linkflags.extend(["-machine:X86"])

    # Enable RTTI.
    cc_globalopts += ["-GR"]

    # Use the "asynchronous" exception model. Bug 1386356 suggests that we
    # switch to the "synchronous" model (-EHsc).
    cc_globalopts += ["-EHa"]

    # Compile only. Do not link.
    cc_globalopts += ["-c"]

    # Use MSPDBSRV.EXE to serialize .pdb writes.
    cc_globalopts += ["-FS"]

    # Warning level 3.
    cc_globalopts += ["-W3"]

    # Treat warnings as errors.
    cc_globalopts += ["-WX"]

    if buildType == "obj":
        cc_globalopts += ["-D_DEBUG"]

        # Enable intrinsic function generation.
        cc_globalopts += ["-Oi"]

        # Disable optimization to aid debuggability.
        cc_globalopts += ["-Od"]

    else:
        cc_globalopts += ["-DNDEBUG"]

        # Optimize for speed
        cc_globalopts += ["-O2"]

        # Re-enable stack frame generation. -Oy- disables the -Oy "omit stack
        # frame" optimization implied by -O2.
        cc_globalopts += ["-Oy-"]

        # Remove unreferenced objects at link time. Implies -incremental:no.
        linkflags += ["-opt:ref"]

    defines = dict()

    # Give us access to all Windows 7 APIs/defines
    defines.update(NTDDI_VERSION=vmware.NTDDI_WIN7)
    defines.update(_WIN32_WINNT=vmware.WINNT_WIN7)

    # Bug 1338656: Workaround warning C4996 generated by 10A SDK for deprecated
    # winsock functions in asio headers, bora/lib/misc, etc.
    #
    # Example warnings:
    # warning C4996: 'WSASocketA': Use WSASocketW() instead
    # warning C4996: 'WSAAddressToStringA': Use WSAAddressToStringW() instead
    # warning C4996: 'WSAStringToAddressA': Use WSAStringToAddressW() instead
    # warning C4996: 'gethostbyaddr': Use getnameinfo() or GetNameInfoW()
    #                instead
    # warning C4996: 'gethostbyname': Use getaddrinfo() or GetAddrInfoW()
    #                instead
    # warning C4996: 'inet_addr': Use inet_pton() or InetPton() instead
    defines.update(_WINSOCK_DEPRECATED_NO_WARNINGS=None)

    # Define this or stuff will fail to load at runtime < Windows 7
    defines.update(PSAPI_VERSION="1")
    rootNETFXSDKPath = os.path.join(
        baseDir, "Windows Kits", "NETFXSDK", frameworkVersion
    )

    # Add some libraries that may have separate Spectre-mitigated directories
    # available.
    msvcLibPaths = [
        os.path.join(msvcRoot, "lib"),
        os.path.join(msvcRedistRoot, "lib"),
    ]
    for libPath in msvcLibPaths:
        spectreLibPath = os.path.join(libPath, "spectre")
        if spectreEnabled and os.path.isdir(spectreLibPath):
            libPath = spectreLibPath
        env.Append(LIBPATH=[os.path.join(libPath, arch)])

    # Now add libraries that don't have separate Spectre-mitigated directories.
    env.Append(
        LIBPATH=[
            os.path.join(winRedistRoot, "ucrt", "lib", "winxp", arch),
            os.path.join(sdkRoot, "lib", sdkVersion, "um", arch),
            os.path.join(sdkRoot, "lib", sdkVersion, "ucrt", arch),
            os.path.join(rootNETFXSDKPath, "lib", "um", arch),
        ]
    )

    if "address" in vmware.UseSanitizers():
        # Microsoft does not provide libvcasan.lib in the Spectre folders. If
        # asan is requested, we are on a new enough version of MSVC that the
        # `if spectreEnabled` check above would fire, so we need to separately
        # add the base directories to LIBPATH.
        for libPath in msvcLibPaths:
            env.Append(LIBPATH=[os.path.join(libPath, arch)])

    umLibPath = "$SDKROOT\\lib\\%s\\um\\%s" % (sdkVersion, arch)

    # Insider preview SDK headers/lib will be saved to environment if it exist.
    # only ULM and installer need this insider preview headers/lib so far, add
    # them to build include/lib path by modify module scons scripts.
    # ref bora\scons\modules\vminstut64.py
    insiderPreviewHdrs = sorted(
        [d for d in incdirs if d.startswith("InsiderPreview_10")], reverse=True
    )
    if insiderPreviewHdrs and isHost64Bit and not isHostARM64:
        insiderIncPath = os.path.join(sdkRoot, "include", insiderPreviewHdrs[0], "um")
        insiderLibPath = os.path.join(
            sdkRoot, "lib", insiderPreviewHdrs[0], "um", "x64"
        )

        if os.path.isdir(insiderIncPath):
            if not os.path.isdir(insiderLibPath):
                raise vmware.ScriptError(
                    "Parsing Windows 10 insider Kits error, the insider "
                    "preview header path %s does not match with the lib "
                    "path %s." % (insiderIncPath, insiderLibPath)
                )
        env.Replace(
            NEWEST_PREVIEW_SDKINCLUDEROOT=env.Dir(insiderIncPath),
            NEWEST_PREVIEW_SDKLIBROOT=env.Dir(insiderLibPath),
        )

    env.Append(
        CPPDEFINES=defines,
        CCFLAGS=cc_globalopts,
        CXXFLAGS=["$PCH_CXXFLAGS"],
        CPPPATH=[
            env.Dir("$SDKROOT\\include\\%s\\um" % sdkVersion),
            env.Dir("$SDKROOT\\include\\%s\\shared" % sdkVersion),
            env.Dir("$SDKROOT\\include\\%s\\ucrt" % sdkVersion),
            env.Dir("$SDKROOT\\include\\%s\\cppwinrt" % sdkVersion),
            env.Dir("$SDKROOT\\include\\%s\\winrt" % sdkVersion),
            env.Dir("%s\\include\\um" % rootNETFXSDKPath),
            env.Dir("$MSVCROOT\\include"),
            env.Dir(umLibPath),  # So source files can easily find tlb paths
        ],
        LINKFLAGS=linkflags,
        SHLIBFLAGS=linkflags + "$IMPLIBFLAGS -DLL".split(" "),
        ARFLAGS=arflags,
    )

    if adkRoot:
        env.Append(
            CPPPATH=["$ADKROOT\\include"],
            LIBPATH=[os.path.join(adkRoot, "lib", adkArch)],
        )

    # In VS2019, there is one header and one library that we need that is
    # present in an old version of the SDK: difxapi.[h,lib] in 10.0.17763.0.
    for incdir in incdirs:
        if incdir != sdkVersion and not incdir.startswith("InsiderPreview_10"):
            extraUmIncDir = os.path.join(sdkRoot, "include", incdir, "um")
            if os.path.isdir(extraUmIncDir):
                env.Append(CPPPATH=[extraUmIncDir])
            extraUmLibDir = os.path.join(sdkRoot, "lib", incdir, "um", arch)
            if os.path.isdir(extraUmLibDir):
                env.Append(LIBPATH=[extraUmLibDir])

    ### Setup build tools

    if isHostARM64:
        # for arm64 it is armasm64.exe
        masmBinName = "armasm64.exe"
    elif isHost64Bit:
        # It is named ml64.exe for x64.
        masmBinName = "ml64.exe"
    else:
        masmBinName = "ml.exe"

    binsDict = {
        "CCBIN": os.path.join("$MSVCBINROOT", "cl.exe"),
        "CXXBIN": os.path.join("$MSVCBINROOT", "cl.exe"),
        "LD": os.path.join("$MSVCBINROOT", "link.exe"),
        "AR": os.path.join("$MSVCBINROOT", "lib.exe"),
        "DUMP": os.path.join("$MSVCBINROOT", "dumpbin.exe"),
        "RC": os.path.join("$SDKBINPATH", "rc.exe"),
        "MASM": os.path.join("$MSVCBINROOT", masmBinName),
        "MC": os.path.join("$SDKBINPATH", "mc.exe"),
        "MSITRAN": os.path.join("$SDKBINPATH", "msitran.exe"),
        "MT": os.path.join("$SDKBINPATH", "mt.exe"),
        "NMAKE": os.path.join("$MSVCROOT", "bin", "nmake.exe"),
        "MAKECAT": os.path.join("$SDKBINPATH", "makecat.exe"),
    }
    binsFileDict = {}
    for key, val in binsDict.items():
        binsFileDict[key] = env.File(val)
    env.Replace(**binsFileDict)

    ### Setup masmAction
    if isHostARM64:
        masmOpts = "/nologo /errorReport:queue /guard:ehcont /machine ARM64"
    elif isHost64Bit:
        masmOpts = "/c /nologo /w"
    else:
        masmOpts = "/c /nologo /coff"
    masmPath = " ".join(
        [
            "-I$SDKROOT\\include\\%s\\shared" % sdkVersion,
            "-I$SDKROOT\\include\\%s\\um" % sdkVersion,
            "-I$SDKROOT\\include\\%s\\ucrt" % sdkVersion,
        ]
    )

    if isHostARM64:
        masm = " ".join(
            [
                "$MASM",
                masmOpts,
                masmPath,
                "${_join(INCLUDE_FUNC(CPPPATH, __env__))}",
                "-Fo",
                "$TARGET",
                "$SOURCE",
            ]
        )
    else:
        masm = " ".join(
            [
                "$MASM",
                masmOpts,
                masmPath,
                "${_join(INCLUDE_FUNC(CPPPATH, __env__))}",
                "$_define",
                "-Fo",
                "$TARGET",
                "$SOURCE",
            ]
        )

    display = vtools.common.displayFunction(long=masm, short="Assembling $TARGET")
    act = SCons.Action.Action(masm, strfunction=display)
    vtools.common.SetData(env, "masmAction", act)
