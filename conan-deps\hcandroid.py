# Copyright (c) Omnissa, LLC. All rights reserved.
# This product is protected by copyright and intellectual property laws in the
# United States and other countries as well as by international treaties.
# -- Omnissa Restricted

import os
import sys

# Access peer file scons_bootstrap.py.
sys.path.append(os.path.dirname(__file__))
from scons_bootstrap import ConanSConsBootstrap


class ConanHcAndroid(ConanSConsBootstrap):
    settings = "os", "arch", "build_type"

    def requirements(self):
        super().requirements()

        # Append required packages instead of inserting them, unless you know what you do
        isARM = self.settings.arch in ["armv7", "armv8"]
        self.requires("nlohmann_json/3.11.2")
        self.requires("opus/1.4")
        self.requires("pcsc-lite-android/1.8.11")
        self.requires("libxml2/2.13.8", options={"iconv": False})
        self.requires("openssl/3.0.16")
        self.requires("openssl_fips_validated/3.0.9")
        self.requires("ogg/1.3.2")
        self.requires("speex/1.2rc2")
        self.requires("speexdsp/1.2rc3")
        self.requires("theora/1.1.1")
        self.requires("icu/74.2")
        self.requires("libcurl/8.10.0")
        self.requires("snappy/1.1.7")
        if isARM:
            self.requires("sse2neon/1.7.0")
        self.requires("libjpeg-turbo/3.0.1")
        self.requires("libyuv/1882")
        self.requires("gtest/1.17.0")
        self.requires("eglib/2.4.0")
        self.requires("zlib/1.3.1")
        self.requires("libpng/1.6.48")

    def build_requirements(self):
        super().build_requirements()

        self.tool_requires("android_sdk/34.0.0")
        self.tool_requires("7zip/24.09")
        self.tool_requires("doxygen/1.9.4")
        self.tool_requires("gradle/7.4")
        self.tool_requires("icu/74.2")
        self.tool_requires("coreutils/9.4")
        self.tool_requires("gettext/0.22.5")
