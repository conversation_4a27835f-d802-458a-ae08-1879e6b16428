/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

#include "stdafx.h"

#include <boost/lexical_cast.hpp>
#include <boost/stacktrace.hpp>
#include <common/i18n/StringConverter.h>
#include <core/util/GuestInfo.h>


extern "C" {
#include "rpcout.h"
}

namespace i18n = svmga::common::i18n;

using namespace svmga::core::util;

GuestInfo::GuestInfo() : _cpi(CustomizationPersistentInfo::GetInstance()) {}

bool
GuestInfo::IsGuestInfoVmStateSet(const char *VmState)
{
   std::string strOut;

   if (VmState == NULL) {
      return false;
   }

   if (GetValue(VmState, strOut)) {
      SYSMSG_FUNC(Debug, _T("Value of %S: %S"), VmState, strOut.c_str());
      if (!strOut.empty() && strOut.front() == 49) {
         return true;
      }
   }

   return false;
}

bool
GuestInfo::Run(const char *szCommand, char **szResult, size_t *len)
{
   if (_cpi->IsUniversalPrep()) {
      // Should not be calling into this vsphere backdoor.
      SYSMSG_FUNC(Error, _T("Not allowed callstack: %S"), boost::stacktrace::stacktrace());
      return false;
   }

   std::string cmd;

   if (szCommand == NULL || szCommand[0] == '\0') {
      return false;
   }

   cmd.assign(szCommand);

   if (!RpcOut_SendOneRaw((void *)cmd.c_str(), cmd.length() + sizeof(char), szResult, len)) {
      SYSMSG_FUNC(Error, _T("Failed Running %S"), cmd.c_str());
      return false;
   }

   return true;
}

bool
GuestInfo::GetValue(const char *szValueName, char **szResult, size_t *len)
{
   if (_cpi->IsUniversalPrep()) {
      std::wstring entry;
      entry.assign(i18n::StringConverter::Utf8ToWString(szValueName));
      std::wstring value = _cpi->GetGuestInfoValue(entry);

      std::string result;
      result.assign(i18n::StringConverter::ToUtf8String(value));
      *szResult = new char[result.length() + 1];
      (*szResult)[result.length()] = '\0';
      result.copy(*szResult, result.length());

      if (len != NULL) {
         *len = result.length();
      }
      SYSMSG_FUNC(Debug, _T("Get GuestInfo equivalent regkey entry/value pair: %ws = %ws"),
                  entry.c_str(), value.c_str());
      // Don't delete szResult. ~ctor by caller.
      return true;
   }

   std::string cmd;

   if (szResult == nullptr || szValueName == nullptr || szValueName[0] == '\0') {
      return false;
   }

   cmd.assign("info-get guestinfo.");
   cmd.append(szValueName);

   if (len != NULL) {
      *len = 0;
   }

   *szResult = NULL;
   if (!Run(cmd.c_str(), szResult, len) || *szResult == NULL) {
      SYSMSG_FUNC(Debug, _T("Failed getting %S"), cmd.c_str());
      return false;
   }

   return true;
}

bool
GuestInfo::GetValue(const char *szValueName, std::string &strResult)
{
   bool bResult = false;
   char *szResult = NULL;

   if (szValueName == nullptr || szValueName[0] == '\0') {
      return false;
   }

   bResult = GetValue(szValueName, &szResult, NULL);
   if (bResult) {
      strResult.assign(szResult);
   }

   Free(&szResult);

   return bResult;
}

bool
GuestInfo::GetValue(const char *szValueName, std::wstring &strResult)
{
   bool bResult = false;
   std::string strResultA;

   bResult = GetValue(szValueName, strResultA);
   if (bResult) {
      strResult.assign(i18n::StringConverter::Utf8ToWString(strResultA));
   }

   return bResult;
}

bool
GuestInfo::SetValue(const char *szValueName, char *szValue)
{
   if (_cpi->IsUniversalPrep()) {
      std::wstring entry;
      entry.assign(i18n::StringConverter::Utf8ToWString(szValueName));

      std::wstring value;
      value.assign(i18n::StringConverter::Utf8ToWString(szValue));
      _cpi->SetGuestInfoValue(entry, value);

      SYSMSG_FUNC(Debug, _T("Set GuestInfo equivalent regkey entry/value pair: %ws = %ws"),
                  entry.c_str(), value.c_str());

      return true;
   }

   bool bResult = false;
   std::string cmd;

   if (szValue == nullptr || szValueName == nullptr || szValueName[0] == '\0') {
      return false;
   }

   cmd.assign("info-set guestinfo.");
   cmd.append(szValueName);
   cmd.append(" ");
   cmd.append(szValue);

   bResult = Run(cmd.c_str(), NULL, NULL);
   if (!bResult) {
      SYSMSG_FUNC(Debug, _T("Failed setting %S"), cmd.c_str());
   }

   return bResult;
}

bool
GuestInfo::SetValue(const char *szValueName, LONGLONG llValue)
{
   size_t len = 0;
   std::string strValue;
   std::string cmd;

   try {
      strValue.assign(boost::lexical_cast<std::string>(llValue));
   } catch (const boost::bad_lexical_cast &e) {
      SYSMSG_FUNC(Error, _T("Exception In lexical_cast Setting Value: %S, %S"), szValueName,
                  e.what());
      return 0;
   }

   return SetValue(szValueName, (char *)strValue.c_str());
}

bool
GuestInfo::Log(const char *szLog)
{
   if (_cpi->IsUniversalPrep()) {
      SYSMSG_FUNC(Debug, _T("GuestInfo equivalent log: %S"), szLog);
      return true;
   }

   bool bResult = false;
   std::string cmd;

   if (szLog == nullptr) {
      return false;
   }

   cmd.assign("log '");
   cmd.append(szLog);
   cmd.append("'");

   bResult = Run(cmd.c_str(), NULL, NULL);
   if (!bResult) {
      SYSMSG_FUNC(Debug, _T("Failed to run %S"), cmd.c_str());
   }

   return bResult;
}

std::string
GuestInfo::InsertMachineType(const char *szBefore, const char *szAfter)
{
   std::string strValue;
   MachineType *mt = MachineType::GetInstance();

   if (szAfter == NULL || szAfter[0] == '\0') {
      return strValue;
   }

   if (szBefore != NULL && szBefore[0] != '\0') {
      strValue.assign(szBefore);
      strValue.append(".");
   }

   strValue.append(mt->GetMachineTypeString());
   strValue.append(".");
   strValue.append(szAfter);

   return strValue;
}

void
GuestInfo::Free(char **szValue)
{
   if (szValue != NULL && *szValue != NULL) {
      free(*szValue);
      *szValue = NULL;
   }
}


#if SVMGA_ENABLE_UNIT_TESTS

bool
GuestInfo::Test()
{
   bool bResult = false;

   bResult = TestInsertMachineType();
   if (bResult == false) {
      SYSMSG_FUNC(Error, "TestInsertMachineType Failed");
      return false;
   }

   bResult = TestSetValue();
   if (bResult == false) {
      SYSMSG_FUNC(Error, "TestSetValue Failed");
      return false;
   }

   bResult = TestGetValue();
   if (bResult == false) {
      SYSMSG_FUNC(Error, "TestGetValue Failed");
      return false;
   }

   bResult = TestRun();
   if (bResult == false) {
      SYSMSG_FUNC(Error, "TestRun Failed");
      return false;
   }

   bResult = TestIsGuestInfoVmStateSet();
   if (bResult == false) {
      SYSMSG_FUNC(Error, "TestIsGuestInfoVmStateSet Failed");
      return false;
   }

   return true;
}

bool
GuestInfo::TestInsertMachineType()
{
   char szBefore[64] = {'\0'};
   char szAfter[64] = {'\0'};
   std::string strResult;

   //
   // Negative Tests
   //

   //
   // szAfter == NULL
   //
   strcpy(szBefore, "Before");
   strResult = InsertMachineType(szBefore, NULL);
   if (strResult.empty() == false) {
      SYSMSG_FUNC(Error, "szAfter NULL Test Failed");
      return false;
   }

   //
   // szAfter Empty
   //
   strcpy(szBefore, "Before");
   ZeroMemory(szAfter, 64);
   strResult = InsertMachineType(szBefore, szAfter);
   if (strResult.empty() == false) {
      SYSMSG_FUNC(Error, "szAfter Empty Test Failed");
      return false;
   }

   //
   // Positive Tests
   //

   //
   // szBefore == NULL
   //
   strcpy(szAfter, "After");
   strResult = InsertMachineType(NULL, szAfter);
   if (strResult.empty() == true) {
      SYSMSG_FUNC(Error, "szBefore NULL Test Failed");
      return false;
   }

   //
   // szBefore empty
   //
   ZeroMemory(szBefore, 64);
   strcpy(szAfter, "After");
   strResult = InsertMachineType(szBefore, szAfter);
   if (strResult.empty() == true) {
      SYSMSG_FUNC(Error, "szBefore Empty Test Failed");
      return false;
   }

   //
   // Both populated
   //
   strcpy(szBefore, "Before");
   strcpy(szAfter, "After");
   strResult = InsertMachineType(szBefore, szAfter);
   if (strResult.empty() == true) {
      SYSMSG_FUNC(Error, "Correct Arguments Failed");
      return false;
   }

   return true;
}

bool
GuestInfo::TestSetValue()
{
   bool bResult = false;
   std::string strResult;

   //
   // Negative Tests (String)
   //

   //
   // Value name NULL
   //
   bResult = SetValue(NULL, "Data");
   if (bResult == true) {
      SYSMSG_FUNC(Error, "Value Name NULL Test Failed");
      return false;
   }

   //
   // Vallue name empty
   //
   bResult = SetValue("", "Data");
   if (bResult == true) {
      SYSMSG_FUNC(Error, "Value Name Empty Test Failed");
      return false;
   }

   //
   // Value is NULL
   //
   bResult = SetValue("TestStringValue", (char *)NULL);
   if (bResult == true) {
      SYSMSG_FUNC(Error, "Value NULL Test Failed");
      return false;
   }

   //
   // Positive Tests (String)
   //

   //
   // Value Empty
   //
   bResult = SetValue("TestStringValue", "");
   if (bResult == false) {
      SYSMSG_FUNC(Error, "Value Empty Test Failed");
      return false;
   }

   //
   // Normal Use Case
   //
   bResult = SetValue("TestStringValue", "Data");
   if (bResult == false) {
      SYSMSG_FUNC(Error, "Positive Test Failed");
      return false;
   }

   //
   // Verify value was set correctly
   //
   if (!GetValue("TestStringValue", strResult)) {
      SYSMSG_FUNC(Error, "Positive Test Value Was Not Set");
      return false;
   }

   if (strResult.compare("Data") != 0) {
      SYSMSG_FUNC(Error, "Positive Test Value Not Correct");
      return false;
   }

   //
   // Value Empty (Repeat)
   //
   bResult = SetValue("TestStringValue", "");
   if (bResult == false) {
      SYSMSG_FUNC(Error, "Value Empty (Repeat) Test Failed");
      return false;
   }


   //
   // Negative Tests (Numeric)
   //

   //
   // Value Name NULL (Numeric)
   //
   bResult = SetValue(NULL, 100);
   if (bResult == true) {
      SYSMSG_FUNC(Error, "Value Name NULL (Numeric) Test Failed");
      return false;
   }

   //
   // Value Name Empty (Numeric)
   //
   bResult = SetValue("", 100);
   if (bResult == true) {
      SYSMSG_FUNC(Error, "Value Name Empty (Numeric) Test Failed");
      return false;
   }

   //
   // Positive Tests (Numeric)
   //

   //
   // Normal Use Case
   //
   bResult = SetValue("TestNumericValue", 100);
   if (bResult == false) {
      SYSMSG_FUNC(Error, "Positive Test (Numeric) Failed");
      return false;
   }

   //
   // Verify value was set correctly
   //
   if (!GetValue("TestNumericValue", strResult)) {
      SYSMSG_FUNC(Error, "Positive Test Value (Numeric) Was Not Set");
      return false;
   }

   if (strResult.compare("100") != 0) {
      SYSMSG_FUNC(Error, "Positive Test (Numeric) Value Not Correct");
      return false;
   }

   //
   // Negative Value
   //
   bResult = SetValue("TestNumericValue", -1);
   if (bResult == false) {
      SYSMSG_FUNC(Error, "Negative (Numeric) Test  Failed");
      return false;
   }

   //
   // Verify value was set correctly
   //
   if (!GetValue("TestNumericValue", strResult)) {
      SYSMSG_FUNC(Error, "Negative Value (Numeric) Was Not Set");
      return false;
   }

   if (strResult.compare("-1") != 0) {
      SYSMSG_FUNC(Error, "Negative Value Test (Numeric) Value Not Correct");
      return false;
   }

   //
   // Zero (Numeric)
   //
   bResult = SetValue("TestNumericValue", (LONGLONG)0);
   if (bResult == false) {
      SYSMSG_FUNC(Error, "Zero (Numeric) Test Failed");
      return false;
   }

   //
   // Verify value was set correctly
   //
   if (!GetValue("TestNumericValue", strResult)) {
      SYSMSG_FUNC(Error, "Zero (Numeric) Test Value Was Not Set");
      return false;
   }

   if (strResult.compare("0") != 0) {
      SYSMSG_FUNC(Error, "Zero (Numeric) Test Value Not Correct");
      return false;
   }

   return true;
}

bool
GuestInfo::TestGetValue()
{
   bool bResult = false;
   std::string strResult;
   size_t len = 0;
   char *szResult = NULL;

   //
   // Init TestNumericValue
   //
   bResult = SetValue("TestNumericValue", 12345678);
   if (bResult == false) {
      SYSMSG_FUNC(Error, "Failed Initializing TestNumericValue");
      return false;
   }

   //
   // Negative Tests (String)
   //

   //
   // Value name NULL
   //
   bResult = GetValue(NULL, strResult);
   if (bResult == true) {
      SYSMSG_FUNC(Error, "Value Name NULL Test Failed");
      return false;
   }

   //
   // Value Name Empty
   //
   bResult = GetValue("", strResult);
   if (bResult == true) {
      SYSMSG_FUNC(Error, "Value Name Empty Test Failed");
      return false;
   }

   //
   // Positive Tests (String)
   //

   //
   // Normal Use Case
   //
   bResult = GetValue("TestNumericValue", strResult);
   if (bResult == false) {
      SYSMSG_FUNC(Error, "Positive Test Failed");
      return false;
   }

   //
   // Negative Tests (Numeric)
   //

   //
   // Value Name NULL
   //
   bResult = GetValue(NULL, &szResult, &len);
   if (bResult == true) {
      SYSMSG_FUNC(Error, "Value Name NULL (3 Arg) Test Failed");
      return false;
   }

   //
   // Value Name Empty
   //
   bResult = GetValue("", &szResult, &len);
   if (bResult == true) {
      SYSMSG_FUNC(Error, "Value Name (3 Arg) Test Failed");
      return false;
   }

   //
   // szResult == NULL
   //
   bResult = GetValue("TestNumericValue", NULL, &len);
   if (bResult == true) {
      SYSMSG_FUNC(Error, "szResult NULL (3 Arg) Test Failed");
      return false;
   }

   //
   // Positive Tests (Numeric)
   //

   //
   // len == NULL
   // len can be NULL, expect true.
   //
   bResult = GetValue("TestNumericValue", &szResult, NULL);
   if (bResult == false) {
      SYSMSG_FUNC(Error, "Len NULL (3 Arg) Test Failed");
      return false;
   }

   //
   // Normal Use Case
   //
   bResult = GetValue("TestNumericValue", &szResult, &len);
   if (bResult == false) {
      SYSMSG_FUNC(Error, "Positive (3 Arg) Test Failed");
      return false;
   }

   return true;
}

bool
GuestInfo::TestRun()
{
   bool bResult = false;
   char szCommand[128] = {'\0'};
   char *szResult = NULL;
   size_t len = 0;

   //
   // Negative Tests
   //

   //
   // Command == NULL
   //
   bResult = Run(NULL, &szResult, &len);
   if (bResult == true) {
      SYSMSG_FUNC(Error, "Command NULL Test Failed");
      return false;
   }

   //
   // Command Empty
   //
   bResult = Run(szCommand, &szResult, &len);
   if (bResult == true) {
      SYSMSG_FUNC(Error, "Command Empty Test Failed");
      return false;
   }

   //
   // Positive Tests
   //

   //
   // szResult == NULL
   // szResult can be NULL. Expect true.
   //
   strcpy_s(szCommand, 128, "info-set guestinfo.TestRunValue The Value");
   bResult = Run(szCommand, NULL, &len);
   if (bResult == false) {
      SYSMSG_FUNC(Error, "szResult NULL Test Failed");
      return false;
   }

   //
   // len == NULL
   // len can be null, expect true;
   //
   bResult = Run(szCommand, &szResult, NULL);
   if (bResult == false) {
      SYSMSG_FUNC(Error, "len NULL Test Failed");
      return false;
   }

   //
   // Normal Use Case
   //
   bResult = Run(szCommand, &szResult, &len);
   if (bResult == false) {
      SYSMSG_FUNC(Error, "Positive Test Failed");
      return false;
   }

   return true;
}

bool
GuestInfo::TestIsGuestInfoVmStateSet()
{
   bool bResult = false;
   char VmState[128] = {'\0'};

   //
   // VmState == NULL
   //
   bResult = IsGuestInfoVmStateSet(NULL);
   if (bResult == true) {
      SYSMSG_FUNC(Error, "VmState NULL Test Failed");
      return false;
   }

   //
   // VmState Empty
   //
   bResult = IsGuestInfoVmStateSet(VmState);
   if (bResult == true) {
      SYSMSG_FUNC(Error, "VmState Empty Test Failed");
      return false;
   }

   //
   // VmState Not Set
   //
   strcpy_s(VmState, 128, "TestRunValueNotSet");
   bResult = IsGuestInfoVmStateSet(VmState);
   if (bResult == true) {
      SYSMSG_FUNC(Error, "VmState Not Set Test Failed");
      return false;
   }

   //
   // VmState Set to 0
   //
   SetValue("TestRunValue", (LONGLONG)0);
   strcpy_s(VmState, 128, "TestRunValue");
   bResult = IsGuestInfoVmStateSet(VmState);
   if (bResult == true) {
      SYSMSG_FUNC(Error, "VmState Set (0) Test Failed");
      return false;
   }

   //
   // VmState Set to 1
   //
   SetValue("TestRunValue", 1);
   strcpy_s(VmState, 128, "TestRunValue");
   bResult = IsGuestInfoVmStateSet(VmState);
   if (bResult == false) {
      SYSMSG_FUNC(Error, "VmState Set (1) Test Failed");
      return false;
   }

   return true;
}

#endif