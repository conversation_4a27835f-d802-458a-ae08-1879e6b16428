name: 'UT Jobs trigger'
description: 'Run ut job'
author: '<PERSON><PERSON><PERSON><PERSON>'


inputs:
  runNumber:
    description: 'workflow run number'
    required: true
  runAttempt:
    description: 'workflow run attempt'
    required: true
  testFilePaths:
    description: 'test files to download'
    required: true
  deployCommand:
    description: 'test deploy command'
    required: false
  testCommand:
    description: 'test run command'
    required: true
  uploadLogs:
    description: 'space-delimited list of additional file patterns to upload'
    required: false
  productWorkflow:
    description: 'product worflow'
    required: true
  productWorkflowJob:
    description: 'product workflow job'
    required: true
  artifactoryBaseUrl:
    description: 'artifactoryBaseUrl'
    required: true
  artifactoryApiToken:
    description: 'artifactoryApiToken'
    required: false
  buildtype:
    description: 'build type'
    required: true
    default: 'beta'
  enablecc:
    description: 'flag for if enable cc' 
    required: false
    default: 'true'
  modules:
    description: 'comma-delimited list of modules to pass to opencppcoverage'
    required: false
  pdbPath:
    description: 'pdb path needed for opencppcoverage'
    required: false
  reportDbUri:
    description: 'DB URI for report upload'
    required: true
  reportDbName:
    description: 'DB name for report upload'
    required: true
  reportDbCollection:
    description: 'Collection name of report DB'
    required: true
  useGcov:
    description: 'Is this a code coverage build. This flag is for linux and macOS only'
    required: false
    default: 'false'

runs:
  using: "composite"
  steps:
    # -------------------------------------------------------------------------
    # Set up env
    # Only the Linux cart-bj ut runners need to install python
    # TODO: pre-install python on these runners
    - name: Setup python
      if: runner.os == 'Linux'
      uses: actions/setup-python@v5
      with:
        python-version: '3.13'

    - name: Check for python vs python3
      run: |
        if command -v python &> /dev/null; then
          echo "Using 'python' as binary"
          echo "PYTHON_COMMAND=python" >> $GITHUB_ENV
        elif command -v python3 &> /dev/null; then
          echo "Using 'python3' as binary"
          echo "PYTHON_COMMAND=python3" >> $GITHUB_ENV
        else
          echo "Fatal error: 'python' or 'python3' not found in PATH"
          exit 1
        fi
      shell: bash
 
    - name: Disable code coverage by default
      run: |
        echo "NEEDCOVERAGE=false" >> $GITHUB_ENV
      shell: bash

    - name: Set code coverage env - Windows
      if: ${{ inputs.enablecc != 'false' && runner.os == 'Windows' }}
      run: |
        echo "NEEDCOVERAGE=true" >> $GITHUB_ENV
      shell: bash

    - name: Set code coverage env - Unix
      if: ${{ inputs.enablecc != 'false' && runner.os != 'Windows' && inputs.useGcov == 'true'}}
      run: |
        echo "NEEDCOVERAGE=true" >> $GITHUB_ENV
        GCOV_PREFIX=${{ github.workspace }}/gcov-gcda
        mkdir $GCOV_PREFIX
        echo "GCOV_PREFIX=$GCOV_PREFIX" >> $GITHUB_ENV
        echo "GCOV_PREFIX_STRIP=6" >> $GITHUB_ENV
      shell: bash

    - name: Download and unzip UT
      uses: euc-eng/artifactory-download@v1
      with:
        unzipFiles: true
        workflow: ${{ inputs.productWorkflow }}
        runNumber: ${{ inputs.runNumber }}
        runAttempt: ${{ inputs.runAttempt }}
        job: ${{ inputs.productWorkflowJob }}
        artifactoryBaseUrl: ${{ inputs.artifactoryBaseUrl }}
        artifactoryApiToken: ${{ inputs.artifactoryApiToken }}
        filePaths: ${{ inputs.testFilePaths }}

    - name: Deploy
      if: inputs.deployCommand != ''
      run: ${{ inputs.deployCommand }}
      shell: bash
  
    # OpenCppCoverage setup must be done after the unzip step because it 
    # needs the pdb files to be present in the working directory
    - name: Set OpenCppCoverage arguments
      id: opencpp-setup
      if: ${{ runner.os == 'Windows' && env.NEEDCOVERAGE == 'true' }}
      run: >
        ${{ env.PYTHON_COMMAND }} .github/actions/testframework/common/setup_opencpp.py
        --pdb "${{ inputs.pdbPath }}"
        --modules "${{ inputs.modules }}"
        --sources "${{ github.workspace }}"
        --excluded_sources "${{ github.workspace }}/bora/build"
        --output "${{ github.workspace }}/${{ github.job }}_cov.xml"
      shell: bash

    # -------------------------------------------------------------------------
    # Run tests
    - name: Run UT - OpencppCoverage - Windows
      if: ${{ runner.os == 'Windows' && env.NEEDCOVERAGE == 'true' }}
      run: >
        ${{ steps.opencpp-setup.outputs.opencpp-args }}
        ${{ inputs.testCommand }}
      shell: cmd

    - name: Run UT
      if: ${{ runner.os != 'Windows' || env.NEEDCOVERAGE == 'false' }}
      run: ${{ inputs.testCommand }}
      shell: bash

    # -------------------------------------------------------------------------
    # Collect and upload results
    - name: Publish test report
      if: ${{ !cancelled() }}
      uses: euc-eng/junit-test-reporter@main
      with:
        testResults: '*est*.xml'
        failOnNoReport: 'false'
        
    - name: Upload Test Report
      if: ${{ !cancelled() }}
      uses: euc-eng/test-reporter-backend-upload@v3
      with:
        testResults: '*est*.xml'
        app_version: '${{ github.ref_name }}'
        mongo_uri: ${{ inputs.reportDbUri }}
        db_name: ${{ inputs.reportDbName }}
        collection_name: ${{ inputs.reportDbCollection }}
        reportOnlyFailures: false
      env:
       ADDITIONAL_DATA: |
          {
            "job": "${{ github.job }}",
            "matrix": ${{ toJson(matrix) }},
            "buildtype": "${{ inputs.buildtype }}"
          }

    - name: Fold CC data - Unix
      if: ${{ !cancelled() && runner.os != 'Windows' && env.NEEDCOVERAGE == 'true' }}
      run: |
        if [ -d "${{ env.GCOV_PREFIX }}/bora/build" ]; then
          tar -C ${{ env.GCOV_PREFIX }}/bora/build -czvf ./${{ github.job }}_gcda.tar.gz .
        else
          echo "Directory ${{ env.GCOV_PREFIX }}/bora/build does not exist. Skipping tar creation."
        fi
      shell: bash
  
    - name: Copy log/test/cov results to artifacts dir
      if: ${{ !cancelled() }}
      run: |
        mkdir artifacts
        FILE_PATTERNS=("*.log" "*est*.xml" "*cov.xml" "*_gcda.tar.gz")
        for pattern in "${FILE_PATTERNS[@]}"; do
          find . -maxdepth 1 -name "$pattern" -exec cp {} artifacts/ \;
        done
      shell: bash

    - name: Upload log/test/cov results to Artifactory
      if: ${{ !cancelled() }}
      uses: euc-eng/artifactory-upload@v1
      with:
        artifactoryApiToken: ${{ inputs.artifactoryApiToken }}
        artifactoryBaseUrl: ${{ inputs.artifactoryBaseUrl }}
        sourcePath: artifacts

    - name: Upload specific logs to Artifactory
      if: ${{ !cancelled() && inputs.uploadLogs != '' }}
      uses: euc-eng/artifactory-upload@v1
      with:
        artifactoryApiToken: ${{ inputs.artifactoryApiToken }}
        artifactoryBaseUrl: ${{ inputs.artifactoryBaseUrl }}
        sourcePath: ${{ inputs.uploadLogs }}

    # -------------------------------------------------------------------------
    # Clean up env
    - name: Fix file permissions on Linux runner
      if: ${{ always() && runner.os == 'Linux' }}
      run: |
        sudo chmod -R u+rwX,g+rwX,o+rwX .
      shell: bash

    - name: Clean up uploaded logs
      if: ${{ always() && inputs.uploadLogs != '' }}
      run: |
        rm -rf ${{ inputs.uploadLogs }}
      shell: bash
