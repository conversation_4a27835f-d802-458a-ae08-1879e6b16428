/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*!
 * @file Service.h --
 * The file to include to manage a Windows Service.
 */

#pragma once

#include "stdafx.h"

#include <boost/shared_array.hpp>
#include <boost/thread.hpp>
#include <common/dispatcher/EventWrapperWin32.h>
#include <common/dispatcher/TimerWrapperWin32.h>
#include <common/dispatcher/Waitable.h>
#include <common/dispatcher/WaitableManager.h>
#include <common/windows/handle/Handle.h>

namespace svmga {
namespace common {
namespace windows {
namespace service {

namespace handle = svmga::common::windows::handle;
namespace dispatcher = svmga::common::dispatcher;
namespace coreutil = svmga::core::util;

struct NOTIFY_CONTEXT {
   std::wstring Name;
   SC_HANDLE hService;
   SC_HANDLE hScm;
   VOID *pService;
};

#define FlagOn(_F, _SF) ((_F) & (_SF))
#define SetFlag(_F, _SF) ((_F) |= (_SF))
#define ClearFlag(_F, _SF) ((_F) &= ~(_SF))

#define NOTIFY_MASK                                                                                \
   SERVICE_NOTIFY_STOPPED | SERVICE_NOTIFY_RUNNING | SERVICE_NOTIFY_PAUSED |                       \
      SERVICE_NOTIFY_STOP_PENDING | SERVICE_NOTIFY_START_PENDING | SERVICE_NOTIFY_PAUSE_PENDING |  \
      SERVICE_NOTIFY_CONTINUE_PENDING

#define SERVICE_NOTIFY_NONE 0x0
#define SERVICE_NOTIFY_RESTART 0x1

#define SERVICE_NOTIFY_DEFAULT_RESTART_DELAY 10000
#define SERVICE_NOTIFY_DEFAULT_MAX_RESTARTS 3
#define SERVICE_NOTIFY_DEFAULT_RESTART_DELAY 10000

#define SERVICE_DEFAULT_RETRY_DELAY 10000
#define SERVICE_DEFAULT_MAX_RETRIES 3

#define SERVICE_NO_RETRIES -1
#define SERVICE_NO_DELAY -1
#define SERVICE_GET_DELAY 0
#define SERVICE_GET_RETRIES 0

#define DHCP_SERVICE_NAME _T("dhcp")
#define NETLOGON_SERVICE_NAME _T("netlogon")
#define W32TIME_SERVICE_NAME _T("w32time")
#define HORIZON_AGENT_SERVICE_NAME _T("wsnm")

class Scm;
class Service;
typedef boost::shared_ptr<Service> ServicePtr;

/*!
 * @class Service --
 * This class provides methods that can be used to manipulate and
 * query about a Windows Service.
 */
class Service {

public:
   /*!
    * Constructor opens a handle to the Service specified by the name
    * passed in as arguement.
    *
    * @param scm Reference to the Scm class that will be used to open
    *            handle to the service.
    * @param serviceName String representing name of the service (e.g.
    * "netlogon"). List of service names are under registry key
    *                    HKLM\System\CurrentControlSet\Services
    *
    */
   Service(const Scm &scm, const std::wstring &serviceName, HRESULT &err);

   /*!
    * Returns the current status of this service.
    *
    */
   DWORD GetServiceStatus(SERVICE_STATUS *pStatus) const;

   /*!
    * Returns the configuration parameters of this service.
    *
    */
   boost::shared_array<QUERY_SERVICE_CONFIG> GetServiceConfig(DWORD &err) const;

   /*!
    * Remove a dependency upon service passed in as <c>serviceName</c>. After
    * this method is completed successfully, this service can be started
    * independent of service specified by <c>serviceName</c>.
    */
   DWORD RemoveDependency(const std::wstring &serviceName) const;

   HRESULT Start(LONG lMaxRetries, LONG lDelay);
   HRESULT Stop(LONG lMaxRetries, LONG lDelay);
   HRESULT Restart(const Scm &scm, LONG lMaxRetries, LONG lDelay);
   HRESULT QueryStatus(const Scm &scm, SERVICE_STATUS *pStatus);
   HRESULT SetStartType(const Scm &scm, DWORD dwStartType);
   HRESULT GetStartType(const Scm &scm, DWORD &dwStartType);

   std::wstring GetName() { return _serviceName; }

   HRESULT StartServiceNotify();
   HRESULT StopServiceNotify();
   HRESULT SetNotifyFlag(DWORD dwFlag);

private:
   //
   // Control Methods
   //
   HRESULT Start(const Scm &scm);
   DWORD Stop(const Scm &scm);
   VOID GetServiceParameters(LONG lMaxRetries, LONG lDelay);

   //
   // Notification Methods
   //
   HRESULT StartMonitor();
   static VOID CALLBACK ServiceNotifyCallback(PVOID pParameter);
   static VOID ServiceNotifyWorker(void *p);
   static VOID ServiceNotifyQueueWorker(void *p);
   VOID QueueServiceNotifyEvent(SERVICE_NOTIFY *pNotify);
   VOID NotifyIncrementRestartCount() { _notifyRestartCount++; }
   DWORD NotifyGetRestartCount() { return _notifyRestartCount; }
   DWORD NotifyGetStartDelay() { return _notifyStartDelay; }
   DWORD NotifyGetMaxRestarts() { return _notifyMaxRestarts; }

private:
   handle::ServiceHandleWrapper _serviceHandle;
   handle::ServiceHandleWrapper _scm;
   std::wstring _serviceName;
   SERVICE_NOTIFY _Notify;
   NOTIFY_CONTEXT _NotifyContext;
   boost::shared_ptr<boost::thread> _notifyWorker;
   boost::shared_ptr<boost::thread> _queueWorker;
   dispatcher::EventWrapperWin32Ptr _stopEvent;
   dispatcher::EventWrapperWin32Ptr _queueEvent;
   dispatcher::WaitableManagerPtr _waitableMgr;
   bool _bServiceNotifyStarted;
   HANDLE _hStopEvent;
   DWORD _dwNotifyFlags;
   DWORD _notifyRestartCount;
   DWORD _notifyMaxRestarts;
   DWORD _notifyStartDelay;
   DWORD _defaultDelay;
   DWORD _defaultRetries;
   DWORD _maxRetries;
   DWORD _retryDelay;
   std::list<SERVICE_NOTIFY *> _queue;

   coreutil::CustomizationPersistentInfo *_cpi;
};

}; // namespace service
}; // namespace windows
}; // namespace common
}; // namespace svmga
