/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * commonSvc_defines.h --
 *
 */

#ifndef COMMONSVC_DEFINES_H
#define COMMONSVC_DEFINES_H

#ifdef __cplusplus
extern "C" {
#endif

#include "vdprpc_defines.h"
#include "rdeChannelMsg.h"

#define ALLOW_SCREEN_RECORDING_OPTION_KEY "allowScreenRecording"
#define ALLOW_ARM_NO_ANTIKEYLOGGER_OPTION_KEY "allowArmNoAntiKeylogger"
#define COMMONSVC_OBJ_NAME "CommonSvcObject"
#define CERTSSO_GUID_SIZE 128

const uint32 DPI_SYNC_VERSION = 1;

const uint32 BAT_STAT_VERSION = 1;

const uint64 BLOCK_SCREEN_CAPTURE_MASK = 1 << 0;
const uint64 BLOCK_KEY_LOGGER_MASK = 1 << 1;
const uint64 BLOCK_THUMBNAIL_REPRESENTATION_MASK = 1 << 2;
const uint64 BLOCK_SEND_INPUT_MASK = 1 << 3;
const uint64 FEATURE_OPTION_MASK = ((uint64)1 << 63);


typedef enum {
   CLIENT_COMMON_PLUGIN_MSG,
   ENVIRONMENT_VAR_INFO_MSG,
   DPI_SYNC_MSG,
   TABLET_MODE_MSG,
   CERTSSO_UNLOCK_MSG,
   BATTERY_STATE_MSG,
   DISPLAY_MSG,
   FEATURE_ENABLEMENT_MSG,
   FEATURE_OPTION_MSG,
   RDSAADAUTH_MSG,
   NETWORK_STATE_GPO_MSG,
} CommonSvcCommandType;

typedef struct CommonSvcCommandHeader {
   uint32 command;
   uint32 reserved;
} CommonSvcCommandHeader;

typedef struct CommonSvcCommand {
   CommonSvcCommandHeader header;
   VDP_RPC_VARIANT *params;
   uint32 paramCount;
} CommonSvcCommand;

typedef enum {
   DPI_SYNC_COMMAND_NONE,
   DPI_SYNC_COMMAND_VERSION,
   DPI_SYNC_COMMAND_DPI,
   DPI_SYNC_COMMAND_TYPE_NUM,
} DpiSyncCommandType;

typedef struct DpiSyncCommand {
   DpiSyncCommandType commandType;
   union {
      uint32 version;
      uint32 systemDpi;
   } data;
} DpiSyncCommand;

typedef enum {
   RDSAADAUTH_AUTHENTICATE_COMMAND,
} RdsAadAuthCommandType;

#define RDSAADAUTH_VERSION (1)

#pragma pack(push, 1)
typedef struct RdsAadAuthCommandHeader {
   uint16 commandType;
   uint16 version;
   uint32 reserved;
   uint64 requestId;
} RdsAadAuthCommandHeader;

typedef struct RdsAadAuthResponseHeader {
   uint16 commandType;
   uint16 version;
   uint32 statusCode;
   uint64 requestId;
} RdsAadAuthResponseHeader;

typedef struct RdsAadAuthCommand {
   RdsAadAuthCommandHeader header;
   // uint8 deviceId[ ANYSIZE_ARRAY ];
   // uint8 deviceNonce[ ANYSIZE_ARARY ];
} RdsAadAuthCommand;
#pragma pack(pop)

typedef enum { TABLET_MODE_COMMAND_NONE, TABLET_MODE_COMMAND_SET } TabletModeCommandType;

typedef struct TabletModeCommand {
   TabletModeCommandType commandType;
   union {
      uint32 tabletMode;
   } data;
} TabletModeCommand;

typedef enum {
   CERTSSO_UNLOCK_COMMAND,
} CertSSOCommandType;

typedef struct CertSSOCommand {
   CertSSOCommandType commandType;
   union {
      struct {
         // null terminated char arrays
         uint8 sessionGUID[CERTSSO_GUID_SIZE];
         uint8 ticketGUID[CERTSSO_GUID_SIZE];
      } sessionInfo;
   } data;
} CertSSOCommand;

typedef enum { BAT_STAT_CMD_VERSION, BAT_STAT_CMD_STAT } BatteryStateCommandType;

typedef struct BatteryStateCommand {
   BatteryStateCommandType commandType;
   union {
      uint32 version;
      struct {
         uint32 isACConnected;
         uint32 batteryLifePercent;
      } stat;
   } data;
} BatteryStateCommand;

typedef enum {
   DISPLAY_COMMAND_INFO,
   DISPLAY_COMMAND_TYPE_NUM,
} DisplayCommandType;

#pragma pack(push, 1)
typedef struct DisplayCommand {
   DisplayCommandType commandType;
   union {
      RdeChannelDisplaysInfo displaysInfo;
   } data;
} DisplayCommand;
#pragma pack(pop)

typedef enum {
   FEATURE_ENABLEMENT_CMD_NONE,
   FEATURE_ENABLEMENT_CMD_CAPACITY,
   FEATURE_ENABLEMENT_CMD_STATUS,
} FeatureEnablementCommandType;

typedef struct FeatureEnablementCommand {
   FeatureEnablementCommandType commandType;
   union {
      uint64 capacity;
      uint64 status;
   } data;
} FeatureEnablementCommand;

typedef enum {
   NETWORK_STATE_GPO_CMD_NONE,
   NETWORK_STATE_GPO_CMD_REQUEST_ENABLE_DISPLAY,
   NETWORK_STATE_GPO_CMD_RESPOND_ENABLE_DISPLAY,
   NETWORK_STATE_GPO_CMD_REQUEST_INTERVAL,
   NETWORK_STATE_GPO_CMD_RESPOND_INTERVAL,
} NetworkStateGPOCommandType;

typedef struct NetworkStateGPOCommand {
   NetworkStateGPOCommandType commandType;
   union {
      bool enableDisplay;
      uint32 interval;
   } data;
} NetworkStateGPOCommand;

#ifdef __cplusplus
} // extern "C"
#endif

#endif
