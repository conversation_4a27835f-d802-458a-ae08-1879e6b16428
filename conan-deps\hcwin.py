# Copyright (c) Omnissa, LLC. All rights reserved.
# This product is protected by copyright and intellectual property laws in the
# United States and other countries as well as by international treaties.
# -- Omnissa Restricted

import os
import sys

# Access peer file scons_bootstrap.py.
sys.path.append(os.path.dirname(__file__))
from scons_bootstrap import ConanSConsBootstrap


class ConanHcWin(ConanSConsBootstrap):
    settings = "os", "arch", "compiler", "build_type"

    def requirements(self):
        super().requirements()

        isDynamic = self.settings.compiler.get_safe("runtime") == "dynamic"
        isRelease = self.settings.build_type == "Release"
        isx86_64 = self.settings.arch == "x86_64"
        isx86 = self.settings.arch == "x86"

        # We use the older compiler profile only for cef because it needs an older Windows SDK.
        # The rest of the code is compiled with the latest compiler profile.
        if self.settings.compiler.version == "193":
            if isDynamic and (isRelease and isx86_64):
                self.requires("cef/134.0.6998.44")
            return

        # Append required packages instead of inserting them, unless you know what you do
        self.requires("zlib/1.3.1", options={"shared": True})
        self.requires("fabulatech_common/250409")
        self.requires("fabulatech_scanner/250409")
        self.requires("fabulatech_serialport/250409")
        self.requires("fast_float/3.4.0")
        self.requires("fmt/10.2.1")
        self.requires("jsoncpp/1.9.5")
        self.requires("nlohmann_json/3.11.2")
        self.requires("abseil/20240116.2")
        self.requires("protobuf/3.25.3")
        self.requires("protobuf-c/1.5.0")
        self.requires("uriparser/0.9.8")
        self.requires("snappy/1.1.7")
        self.requires("libxml2/2.13.8", options={"shared": True})
        if self.settings.arch != 'arm64ec':
            self.requires("libcurl/8.10.0", options={"shared": True})
        # For those don't support arm64ec arch.
        if self.settings.arch in ["x86", "x86_64", 'armv8']:
            self.requires("openssl/3.0.16")
            self.requires("openssl_fips_validated/3.0.9")
        self.requires("pdfium/95.0.4629", options={"shared": True})
        if isx86_64:
            self.requires("libx264/164.20220124", options={"shared": True})
            self.requires("ffmpeg-windows/7.0.1", options={"with_mfx": True, "shared": True})
        if isx86_64 and isDynamic:
            self.requires("presentmon/2.3.0")
        if isDynamic:
            self.requires("opus/1.4")
            self.requires("ogg/1.3.2")
            self.requires("speex/1.2rc2")
            self.requires("speexdsp/1.2rc3")
            self.requires("theora/1.1.1")
            self.requires("imgui/1.88")
            self.requires("implot/0.14")
            self.requires("gtest/1.17.0", options={"shared": True})
        self.requires("libgettext/0.22", options={"shared": True})
        self.requires("libsigcpp/3.6.0", options={"shared": True})
        self.requires("pcre2/10.42", options={"shared": True})
        if isDynamic and (isx86 or isx86_64):
            self.requires("glib/2.84.1", options={"shared": True})
            self.requires("glibmm/2.78.1", options={"shared": True})
        if self.settings.arch == "armv8":
            self.requires("sse2neon/1.7.0")
        self.requires("libjpeg-turbo/3.0.1")
        self.requires("libyuv/1882")
        self.requires("dotnet_redists/8.0.10")
        if isx86 or isx86_64:
            self.requires("rescle/1.0.11", options={"shared": False})
            self.requires("nvidia_sdk_12.0/12.0")
            self.requires("nvidia_sdk_13.0/13.0")
        self.requires("fastlz/0.1.0")
        if self.settings.build_type == "Debug":
            self.requires("msvc_debug_runtime_installer/vc140-redists-latest")
        self.requires("libpng/1.6.48", options={"shared": True})
        self.requires("libiconv/1.17", options={"shared": True})
        self.requires("cunit/2.1-3", options={"shared": False})
        self.requires("tomlplusplus/3.1.0")
        self.requires("wtl/10.0.10320")

        if self.settings.arch == 'armv8' and not isDynamic:
            # For some reason boost is stuck on 1.85 for static armv8 builds
            self.requires("boost/1.85.0")
        elif self.settings.arch != 'arm64ec':
            self.requires("boost/1.86.0")

    def build_requirements(self):
        super().build_requirements()

        self.tool_requires("dotnet_sdk/8.0.404")
        self.tool_requires("doxygen/1.9.4")
        self.tool_requires("protobuf/3.25.3")
        self.tool_requires("protobuf-c/1.5.0")
        self.tool_requires("7zip/24.09")
        self.tool_requires("wix_toolset/3.11")
        self.tool_requires("squishcoco/7.1.0")
        self.tool_requires("squishcoco_dotnet/7.1.0")
        self.tool_requires("vmware-mswdk-exts/1.0.14")
        self.tool_requires("nvidia_cuda/12.8")
