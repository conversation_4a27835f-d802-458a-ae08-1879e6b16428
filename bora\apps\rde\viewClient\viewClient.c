/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * viewClient.c --
 *
 *      viewClient startup code.
 */

#ifdef _WIN32
#   include <winsock2.h>
#   include <io.h>
#   include <process.h>
#   include <dbghelp.h>
#   include <direct.h>
#   include "getoptwin32.h"
#   include "win32Util.h"
#   include "windowsu.h"
#   include "hznprotect.h"
#   include "whfbRedirection.h"
#   include "horizonPaths.h"
#else
#   include <errno.h>
#   include <sys/socket.h>
#   include <getopt.h>
#   include <sys/resource.h>
#endif

#ifdef __linux__
#   include <signal.h>
#   include <sys/syscall.h>
#endif

#include <stdlib.h>
#include <stdio.h>
#include <fcntl.h>
#include <ctype.h>
#include <openssl/ssl.h>

#include "asyncProxySocket.h"
#include "blastConnectionType.h"
#include "clamped.h"
#include "config.h"
#include "coreDump.h"
#include "dictionary.h"
#include "dynarray.h"
#include "file.h"
#include "hostinfo.h"
#include "keyboardMapping.h"
#include "location.h"
#include "log.h"
#include "msg.h"
#include "mutexRank.h"
#include "panic.h"
#include "preference.h"
#include "productState.h"
#include "sig.h"
#include "url.h"
#include "util.h"
#if defined(__APPLE__) || defined(__linux__)
#   include "vdpFido2RedirMgr.h"
#endif
#include "vdpMksVchan.h"
#include "vdpOverlayHost.h"
#include "vdpPluginHost.h"
#include "vdpPluginScreen.h"
#include "vdpGeoRedirMgrCommon.h"
#include "vdpHtml5RedirMgr.h"
#include "vdpOverlayHost.h"
#include "vdpRdeCommon.h"
#include "vdpRdeCommonMgr.h"
#include "vdpScreenCaptureMgr.h"
#include "vdpSdr.h"
#include "vdpSharedFolderMgr.h"
#include "vdpTarget.h"
#include "vdpTargetInfo.h"
#include "vdpUnityViewControl.h"
#include "vdpUsbRedirection.h"
#include "vdpVMObserver.h"
#include "viewClient.h"
#include "viewClientLock.h"
#include "viewClientLog.h"
#include "viewClientOS.h"
#include "viewControlMgr.h"
#include "viewInput.h"
#include "viewScreenManager.h"
#include "vm_version.h"
#include "vmclientrmks.h"
#include "vmlocale.h"
#include "vncClient.h"
#include "vncClientInt.h"
#include "vncPoll.h"
#include "vncThread.h"
#include "vthread.h"
#include "vm_basic_defs.h"

#define LOGLEVEL_MODULE viewClient
#include "loglevel_user.h"

#define LGPFX "VIEWCLIENT: "

// Set the default max dump count to 128.
#define DEFAULT_MAX_DUMP_COUNT 128

/*
 * Expose static functions only for UT to call
 * because GTest can't test static function.
 */
#ifdef VIEWCLIENTTEST
#   define STATIC
#else
#   define STATIC static
#endif

typedef enum ViewClientBenchmarkMode {
   BENCHMARK_NONE = 0,
   BENCHMARK_RENDER_PLAYER,
} ViewClientBenchmarkMode;

#ifdef __linux__
/*
 * Including X11 header will cause conflicts.
 * Here we only declare the function we need.
 */
extern int XInitThreads(void);

extern void SetXErrorHandler();
#endif

/*
 * Local data
 */
typedef enum {
   MKS_ROLE_TARGET_VSPHERE = 0,
   MKS_ROLE_TARGET_VDP,
   MKS_ROLE_TARGET_GENERIC_VNC,
} MKSRoleTarget;

static struct {
   MKSDisplayProtocol displayProtocol;
   MKSRoleTarget target;

   Bool vdpPluginInitialized;
   char vdpTarget[VDPPLUGIN_TARGET_MAX_LEN + 1];

   const char *host;
   int port;
   Bool useUdpFec;
   Bool rawTCPSocket;

   char *locale;
   const char *logFileName;
   unsigned int logId;
   unsigned int logMaxFiles;

   char *configOverlayFile;

   volatile Bool exitRequest;

   Bool connected;
   Bool osInitialized;

   const char *programName;
   const char *cmdLineArgs;
   int pipeFd;

   void *sslCtx;
   Bool sslRequired;
   Bool sslVerifyCerts;
   SSLVerifyParam sslVerifyParam;

   Bool debug;
   Bool debuggerAttached;

   char httpProxy[WEBSOCKET_URL_MAX];
   char vcdPath[WEBSOCKET_URL_MAX];
   char *routeSpecifier;
   char *connCookies;

   int errorCode;

   MXUserEvent *viewControlInitialized;
#ifdef __linux__
   Bool generateCoreDumpInCurrentProcess;
#endif
   Bool enableSplitMKSWindow;

   Bool isIMEEnabled;

   Bool isAppSession;

   Bool isAgentInstalled;
   Bool isBlockKeyLoggerEnabled;
   Bool isBlockSendInputEnabled;
   Bool isAllowArmNoAntiKeyloggerEnabled;

   ViewClientBenchmarkMode benchmarkMode;
   Bool onlyProbeMode;

   ViewClientVideoDecoderCaps decodeCaps;

   Bool isRealMultimon;
} viewClient;

const ViewModifierKeys mksModKeys[] = {VIEW_MOD_KEYS_DATA};

ViewClientConfig viewConfig;

/*
 * Local functions
 */
static void ViewClientBenchmark(void *clientData);
static void ViewClientExitWithUsage(void);
static void ViewClientParseOptions(int argc, char *argv[]);
static void ViewClientProtocolPowerOn(void);
static void ViewClientProtocolPowerOff(void);
static void ViewClientConnectedCb(void *data);
Bool ViewClient_ConnectBlast(const char *targetURL, const char *thumbprint,
                             SSLThumbprintType tpType, const char *httpProxy,
                             int reconnectAttemptCount);

#ifdef VIEWCLIENTTEST
/*
 *----------------------------------------------------------------------
 *
 * ViewClientTest_SetSessionType --
 *
 *      Set session type.
 *
 * Results:
 *      None.
 *
 *----------------------------------------------------------------------
 */

void
ViewClientTest_SetSessionType(Bool isAppSession)
{
   viewClient.isAppSession = isAppSession;
}


/*
 *----------------------------------------------------------------------
 *
 * ViewClientTest_SetProtocol --
 *
 *      Set current display Protocol.
 *
 * Results:
 *      None.
 *
 *----------------------------------------------------------------------
 */

void
ViewClientTest_SetProtocol(MKSDisplayProtocol protocol)
{
   viewClient.displayProtocol = protocol;
}


/*
 *----------------------------------------------------------------------
 *
 * ViewClientTest_SetTarget --
 *
 *      Set target.
 *
 * Results:
 *      None.
 *
 *----------------------------------------------------------------------
 */

void
ViewClientTest_SetTarget(int target)
{
   viewClient.target = target;
}
#endif


/*
 *----------------------------------------------------------------------
 *
 * ViewClient_Init --
 *
 *      Initialize view client.
 *
 * Results:
 *      None.
 *
 *----------------------------------------------------------------------
 */

void
ViewClient_Init(void)
{
   memset(&viewClient, 0, sizeof(viewClient));
   viewClient.benchmarkMode = BENCHMARK_NONE;
}


/*
 *----------------------------------------------------------------------
 *
 * ViewClient_GetProtocol --
 *
 *      Returns the current display Protocol.
 *
 * Results:
 *      The display protocol.
 *
 *----------------------------------------------------------------------
 */

MKSDisplayProtocol
ViewClient_GetProtocol(void)
{
   return viewClient.displayProtocol;
}


/*
 *----------------------------------------------------------------------
 *
 * ViewClient_IsBlastSession --
 * ViewClient_IsVDPSession --
 * ViewClient_IsViewSession --
 * ViewClient_IsGenericVNCSession --
 *
 *      Returns the current session type.
 *
 * Results:
 *      TRUE or FALSE.
 *
 *----------------------------------------------------------------------
 */

Bool
ViewClient_IsBlastSession(void)
{
   return (viewClient.target == MKS_ROLE_TARGET_VDP &&
           viewClient.displayProtocol == MKS_DISPLAYPROTOCOL_VNC);
}

Bool
ViewClient_IsVDPSession(void)
{
   return (viewClient.target == MKS_ROLE_TARGET_VDP &&
           viewClient.displayProtocol == MKS_DISPLAYPROTOCOL_VDP);
}

Bool
ViewClient_IsViewSession(void)
{
   return (ViewClient_IsBlastSession() || ViewClient_IsVDPSession());
}

Bool
ViewClient_IsGenericVNCSession(void)
{
   Bool isVNC = viewClient.target == MKS_ROLE_TARGET_GENERIC_VNC;

   ASSERT(IMPLIES(isVNC, viewClient.displayProtocol == MKS_DISPLAYPROTOCOL_VNC));
   return isVNC;
}


/*
 *----------------------------------------------------------------------
 *
 * ViewClient_IsUIConnected --
 *
 *      Returns if connection to the UI is established.
 *
 * Results:
 *      TRUE or FALSE.
 *
 *----------------------------------------------------------------------
 */

Bool
ViewClient_IsUIConnected(void)
{
   return viewClient.cmdLineArgs != NULL;
}


/*
 *----------------------------------------------------------------------
 *
 * ViewClient_IsConnected --
 *
 *      Returns if the blast session is connected.
 *
 * Results:
 *      TRUE or FALSE.
 *
 *----------------------------------------------------------------------
 */

Bool
ViewClient_IsConnected(void)
{
   return viewClient.connected;
}


/*
 *----------------------------------------------------------------------
 *
 * ViewClient_IsPlayerMode --
 *
 *      Returns if the client in the video player mode.
 *
 * Results:
 *      TRUE or FALSE.
 *
 *----------------------------------------------------------------------
 */

Bool
ViewClient_IsPlayerMode(void)
{
   return viewClient.benchmarkMode == BENCHMARK_RENDER_PLAYER;
}


/*
 *----------------------------------------------------------------------
 *
 * ViewClient_IsExiting --
 *
 *      Is the client exiting ?
 *
 * Results:
 *      TRUE or FALSE
 *
 *----------------------------------------------------------------------
 */

Bool
ViewClient_IsExiting(void)
{
   return viewClient.exitRequest;
}


/*
 *----------------------------------------------------------------------
 *
 * ViewClient_IsBenchmarkMode --
 *
 *      Is the client operating in benchmark mode.
 *
 * Results:
 *      TRUE if yes, FALSE if no.
 *
 *----------------------------------------------------------------------
 */

Bool
ViewClient_IsBenchmarkMode(void)
{
   return viewClient.benchmarkMode != BENCHMARK_NONE;
}


/*
 *----------------------------------------------------------------------
 *
 * ViewClient_RequestExit --
 *
 *      Requests that the client now exit.
 *
 * Results:
 *      None.
 *
 *----------------------------------------------------------------------
 */

void
ViewClient_RequestExit(void)
{
   viewClient.exitRequest = TRUE;

   if (viewClient.benchmarkMode != BENCHMARK_NONE) {
      Warning("Benchmark aborted\n");
      exit(1);
   }
}


/*
 *----------------------------------------------------------------------
 *
 * ViewClientMayDeleteLog --
 *
 *      This is a callback used by Log_BoundNumFiles(), which deletes
 *      old logfiles, to detect if a file is "owned" by this application.
 *
 *      If this callback returns TRUE, Log_BoundNumFiles() is permitted to
 *      delete the logfile if its PID does not match a running executable.
 *
 *      The "owned" term is flexible - it can mean created by this
 *      application (generic) or by a specific instance of this
 *      application. The definition used here is the former - all files
 *      created by this code taking the logId path below.
 *
 *      This function detects file names of the form:
 *
 *      horizon-<parent-pid>-protocol-<pid>.log
 *
 *      The <parent-pid> may vary but <pid> will always be the PID of an
 *      instance of this program.
 *
 *      See PR 338361 comment #82 for more information.
 *
 * Results:
 *      TRUE  The file may be deleted     (if "pid" does not match a
 *                                         currently running executable)
 *      FALSE The file may not be deleted (was not created by this app)
 *
 * Side effects:
 *      None
 *
 *----------------------------------------------------------------------
 */

static Bool
ViewClientMayDeleteLog(void *userData,       // IN: Unused
                       const char *fileName, // IN:
                       uint32 *pid)          // OUT:
{
   uint32 unused;

   ASSERT(fileName != NULL);

   /*
    * XXX: This function is likely not used by Horizon client.
    *
    * (and also incorrectly chooses all log files but the ones with the
    * horizon-<parent-pid>-protocol-<pid>.log pattern as candidates for
    * deletion! see LogDefaultMayDeleteFunc() for an example of correct usage.)
    */
   return sscanf(fileName, PRODUCT_GENERIC_HORIZON_BRIEF_NAME_LOWER "-%u-protocol-%u.log", &unused,
                 pid) != 2;
}


/*
 *-----------------------------------------------------------------------------
 *
 * ViewClientInitLog --
 *
 *      Initialize logging.
 *
 * Results:
 *      TRUE on success, otherwise FALSE.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

static Bool
ViewClientInitLog(const char *logFileName, // IN:
                  unsigned int logId)      // IN:
{
   char *suffix = NULL;
   LogOutput *output = NULL;
   Dictionary *dict = Dictionary_Create();

   ASSERT(dict);

   if (logFileName) {
      /*
       * Use an explicit log file name. This name will handle rotation
       * using the standard fixed name rotation policies.
       */

      Dict_SetString(dict, logFileName, "log.fileName");
   } else {
      /*
       * Create log files using the automatic file creation abilities of the
       * Log Facility. The log files created this way all end with "-PID.log"
       * where the PID is that the program creating the log file (this code).
       *
       * See PR 338361 comment #82 for more information.
       *
       * Filenames generated here look like "horizon-protocol-<pid>.log"
       * (and possibly "horizon-<parent-pid>-protocol-<pid>.log").
       */
      const char *suffixBase = viewClient.onlyProbeMode ? "mksprobe" : "protocol";

      if (logId) {
         suffix = Str_Asprintf(NULL, "%u-%s", logId, suffixBase);
      } else {
         suffix = Str_Asprintf(NULL, "%s", suffixBase);
      }
      if (!viewClient.logMaxFiles) {
         viewClient.logMaxFiles = 50;
      }
      Dict_SetString(dict, "remotemks.log.fileName", "log.config");
      Dict_SetString(dict, suffix, "log.suffix");
      // -1 because 'old' files number doesn't include 'current' file
      Dict_SetLong(dict, viewClient.logMaxFiles - 1, "log.keepOld");
   }

   /*
    * Remove any previous Log Facility outputs. Direct the Log Facility
    * the output specified here.
    */

   Log_Exit();

   output = Log_InitWithFile("remotemks", dict, Log_CfgInterface(), FALSE);

   Dictionary_Free(dict);

   if (output == NULL) {
      Warning("Unable to proceed without a log file.\n");
   } else {
      /*
       * Bound the number of log files that result from this application.
       *
       * The Log Facility will discard files only when the number of log
       * files exceeds keepOld (set above); only the oldest files over
       * the threshold whose PID is not active will be deleted.
       */

      if (logId) {
         /*
          * Log files with a "horizon-%u-protocol" basename
          * (e.g. horizon-<parent-pid>-protocol-<pid>.log) will confuse
          * Log_BoundNumFiles() because it is not a fixed string.
          * So use a callback function to select the logfiles which we created.
          */

         Log_BoundNumFiles(output, ViewClientMayDeleteLog, suffix);
      } else {
         /*
          * Log file names with a fixed basename (e.g. horizon-protocol-PID.log)
          * work with Log_BoundNumFiles()'s default old-logfile detection logic.
          */

         Log_BoundNumFiles(output, NULL, NULL);
      }

      if (vmx86_debug || vmx86_devel) {
         Warning("BLAST Client: Logging to %s\n", Log_GetOutputFileName(output));
      }
   }

   free(suffix);

   return (output != NULL);
}


/*
 *-----------------------------------------------------------------------------
 *
 * ViewClientAddPrefEntry --
 *
 *    Sets a config value in the preference file.
 *
 * Results:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

static void
ViewClientAddPrefEntry(const char *name,  // IN
                       const char *value, // IN
                       Bool encrypt,      // IN/UNUSED
                       int i,             // IN/UNUSED
                       void *prv)         // IN/UNUSED
{
   char *tempStr = Str_Asprintf(NULL, "%s=%s", name, value);
   Preference_SetFromString(tempStr, TRUE);
   free(tempStr);
}


/*
 *-----------------------------------------------------------------------------
 *
 * ViewClientLoadConfigOverlay --
 *
 *    Load the config overlay file.
 *
 * Results:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

static void
ViewClientLoadConfigOverlay(void)
{
   Dictionary *dict;
   Bool success;

   if (viewClient.configOverlayFile == NULL) {
      return;
   }

   dict = Dictionary_Create();
   success = Dictionary_Load(dict, viewClient.configOverlayFile);
   if (!success) {
      Panic("Failed to load config overlay: %s\n", viewClient.configOverlayFile);
   }

   Dictionary_Iterate(dict, ViewClientAddPrefEntry, NULL, FALSE);
   Dictionary_Free(dict);
}


/*
 *-----------------------------------------------------------------------------
 *
 * Panic --
 *
 *      Panic, possibly core dump
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

void
Panic(const char *fmt, // IN:
      ...)             // IN:
{
   va_list args;
   char buf[1024];
   static int count = 0;

   /* Catch double panic if Ungrab causes another. */
   if (!MXUser_InPanic()) {
      MXUser_SetInPanic();

      /*
       * We call directly to the OS layer here to forcibly ungrab, and
       * restore the keyboard/mouse settings.
       */
      if (viewClient.osInitialized) {
         ViewClientOS_Ungrab(TRUE);
      }
   }

   va_start(args, fmt);
   Str_Vsnprintf(buf, sizeof(buf), fmt, args);
   va_end(args);

   /*
    * Write the message to stderr first, so there's always
    * some sort of record.
    * Don't try to do anything fancy, since this is before
    * panic loop detection.  In particular, try not to call
    * any of our functions (that may call Panic()).
    */

   fputs(buf, stderr);
#ifdef _WIN32
   /*
    * This would nominally be Win32U_OutputDebugString.  However,
    * OutputDebugString is unusual in that the W version converts
    * to local encoding and calls the A version.
    *
    * Since any such conversion is risky (read: can Panic) and
    * we haven't yet hit the loop detection, we will conservatively
    * dump UTF-8 via the A version.
    */

   OutputDebugStringA(buf);
#endif

   /*
    * Notify the debugger if there is one.
    */

   Panic_BreakOnPanic();

   /*
    * Make sure Panic gets logged
    */
   Log_DisableThrottling();

   /*
    * Panic loop detection:
    *    first time - do the whole report and shutdown sequence
    *    second time - log safely and exit
    *    third time - log unsafely and exit
    *    beyond third time - just exit
    *
    * Second Panic indicates problem while core dumping.  Keep log locking
    *    active - we would prefer unjumbled log files.
    * Third Panic indicates problem while logging.  Turn off log locking
    *    because we really need the message now.
    */

   switch (count++) {
   case 0:
      break;
   case 2:
      Log_SkipLocking(TRUE);
      HZN_FALLTHROUGH();
   case 1:
      Log_Level(HZN_LOG_PANIC, "PANIC: %s", buf);
      Log_Level(HZN_LOG_PANIC, "%s", "Panic loop\n");
      Util_Backtrace(0);
      HZN_FALLTHROUGH();
   default:
      fprintf(stderr, "Panic loop\n");
      Util_ExitProcessAbruptly(1);
      NOT_REACHED();
   }

#ifdef _WIN32
   /*
    * Output again, in a way that we hope localizes correctly.  Since
    * we are converting, this can Panic, so it must run after loop
    * detection.
    */

   Win32U_OutputDebugString(buf);
#endif

   /*
    * The normal panic path:
    *    ASSERT(count == 1);
    */

   /*
    * Log panic information, and make sure we don't remove
    * the log file on exit.
    */

   Log_Level(HZN_LOG_PANIC, "PANIC: %s\n", buf);

   /*
    * Do the debugging steps early before we have a chance to double
    * panic. Coredump before backtrace because coredump is far more
    * likely to succeed when heap space is low.
    */

   Panic_DumpGuiResources();

   if (Panic_GetCoreDumpOnPanic()
#ifdef __linux__
       && !viewClient.generateCoreDumpInCurrentProcess
#endif
   ) {
      CoreDump_CoreDump();
   }

   CoreDump_LogModules(TRUE);
   Util_Backtrace(0);
#ifdef __linux__
   if (Panic_GetCoreDumpOnPanic() && viewClient.generateCoreDumpInCurrentProcess) {
      sigset_t mask, oldMask;
      struct sigaction sa, oldSA;
      /*
       * Generate duplicated logs for modules to avoid uncompleted callstack.
       */
      CoreDump_LogModules(TRUE);
      memset(&sa, 0, sizeof sa);
      sa.sa_handler = SIG_DFL;
      sigaction(SIGABRT, &sa, &oldSA);
      sigemptyset(&mask);
      sigaddset(&mask, SIGABRT);
      pthread_sigmask(SIG_UNBLOCK, &mask, &oldMask);
      syscall(SYS_tkill, syscall(SYS_gettid), SIGABRT);
   }
#endif
   Panic_LoopOnPanic();

   Log("Exiting\n");
   exit(-1);
   NOT_REACHED();
}


/*
 *----------------------------------------------------------------------
 *
 * ViewClientPollFire --
 *
 *      Helper function for poll callbacks.
 *
 * Results:
 *      None.
 *
 *----------------------------------------------------------------------
 */

void
ViewClientPollFire(PollerFunction func, // IN
                   void *funcData,      // IN
                   void *wrapperData)   // IN
{
   ASSERT(wrapperData == NULL);

   func(funcData);
}


/*
 *----------------------------------------------------------------------
 *
 * ViewClient_IsAgentInstalled --
 *
 *      Get whether Horizon Agent is installed on this host.
 *
 * Results:
 *      True if installed, false otherwise.
 *
 *----------------------------------------------------------------------
 */

Bool
ViewClient_IsAgentInstalled()
{
#ifdef _WIN32
   static Atomic_uint32 checked;

   if (!Atomic_Read32(&checked)) {
      LONG lResult;
      HKEY hKey = NULL;

      lResult = RegOpenKeyExW(HKEY_LOCAL_MACHINE, L"System\\CurrentControlSet\\Services\\WSNM", 0,
                              KEY_READ, &hKey);
      if (lResult == ERROR_SUCCESS) {
         viewClient.isAgentInstalled = TRUE;
         RegCloseKey(hKey);
      }

      Atomic_Write32(&checked, TRUE);
   } // checked == FALSE
#endif // _WIN32

   return viewClient.isAgentInstalled;
}


/*
 *----------------------------------------------------------------------
 *
 * ViewClient_SetBlockKeyLoggerStatus --
 *
 *      Set block keylogger enable / disable status from commonSvc.
 *
 * Results:
 *      None.
 *
 *----------------------------------------------------------------------
 */

void
ViewClient_SetBlockKeyLoggerStatus(Bool enable) // IN
{
   viewClient.isBlockKeyLoggerEnabled = enable;
}


/*
 *----------------------------------------------------------------------
 *
 * ViewClient_IsBlockKeyLoggerEnabled --
 *
 *      Get whether block keylogger is enabled.
 *
 * Results:
 *      True if enabled, false otherwise.
 *
 *----------------------------------------------------------------------
 */

Bool
ViewClient_IsBlockKeyLoggerEnabled()
{
#ifdef _WIN32
   /*
    * TODO: Please revert HznProtect_IsAMD64 and
    * isAllowArmNoAntiKeyloggerEnabledbelow once anti-keylogger in arm
    * is implemented in windows.
    */
   if (viewClient.isBlockKeyLoggerEnabled && !HznProtect_IsAMD64() &&
       viewClient.isAllowArmNoAntiKeyloggerEnabled) {
      return FALSE;
   } else {
      return viewClient.isBlockKeyLoggerEnabled;
   }
#else
   return viewClient.isBlockKeyLoggerEnabled;
#endif
}


/*
 *----------------------------------------------------------------------
 *
 * ViewClient_SetAllowArmNoAntiKeyloggerStatus --
 *
 *      Set whether allow arm without anti-keylogger from commonSvc.
 *
 * Results:
 *      None.
 *
 *----------------------------------------------------------------------
 */

void
ViewClient_SetAllowArmNoAntiKeyloggerStatus(Bool enable) // IN
{
   viewClient.isAllowArmNoAntiKeyloggerEnabled = enable;
}


/*
 *----------------------------------------------------------------------
 *
 * ViewClient_SetBlockSendInputStatus --
 *
 *      Set block SendInput() enable / disable status from commonSvc.
 *
 * Results:
 *      None.
 *
 *----------------------------------------------------------------------
 */

void
ViewClient_SetBlockSendInputStatus(Bool enable) // IN
{
   viewClient.isBlockSendInputEnabled = enable;
}


/*
 *----------------------------------------------------------------------
 *
 * ViewClient_IsBlockSendInputEnabled --
 *
 *      Get whether block SendInput() is enabled.
 *
 * Results:
 *      True if enabled, false otherwise.
 *
 *----------------------------------------------------------------------
 */

Bool
ViewClient_IsBlockSendInputEnabled()
{
#ifdef _WIN32
   /*
    * TODO: Please revert HznProtect_IsAMD64 and
    * isAllowArmNoAntiKeyloggerEnabledbelow once anti-keylogger in arm
    * is implemented in windows.
    */
   if (viewClient.isBlockSendInputEnabled && !HznProtect_IsAMD64() &&
       viewClient.isAllowArmNoAntiKeyloggerEnabled) {
      return FALSE;
   } else {
      return viewClient.isBlockSendInputEnabled;
   }
#else
   return viewClient.isBlockSendInputEnabled;
#endif
}


/*
 *----------------------------------------------------------------------
 *
 * ViewClientParseOptions
 *
 *      Process command line arguments
 *
 * Results:
 *      None
 *
 *----------------------------------------------------------------------
 */

static void
ViewClientParseOptions(int argc,     // IN
                       char *argv[]) // IN
{
   /*
    * IDs for options that have only a --long form.  These must be outside the
    * representable range for char.
    */
   enum {
      // Wait for a command with VNC connection info
      OPT_CONNECT_VNC = UCHAR_MAX + 1,
      // Run in decode probing mode
      OPT_PROBE_DECODE_HW,
   };

   static const struct option options[] = {// config variable
                                           {"set", required_argument, NULL, 's'},
                                           // Remote console connection... redundant.
                                           {NULL, no_argument, NULL, 'R'},
                                           // Logfile name
                                           {"log", required_argument, NULL, 'L'},
                                           // Log id (parent process id)
                                           {"parent-pid", required_argument, NULL, 'l'},
                                           // viewControl arguments
                                           {NULL, required_argument, NULL, '@'},
                                           // Pipe handle to receive MKS server cnx
                                           {NULL, required_argument, NULL, 'H'},
                                           // Pipe will receive View Desktop target info
                                           {"view", no_argument, NULL, 'V'},
                                           // Pipe will receive Websocket target info
                                           {"websocket", no_argument, NULL, 'W'},
                                           // Don't use SSL
                                           {"no-ssl", no_argument, NULL, 'I'},
                                           // UI Locale
                                           {"locale", required_argument, NULL, 'E'},
                                           // Remote display protocol
                                           {"protocol", required_argument, NULL, 'P'},
                                           // disable user preferences
                                           {"no-prefs", no_argument, NULL, 'D'},
                                           // Whether to verify SSL certs: {"on", "off"}
                                           {"ssl-checks", required_argument, NULL, 'C'},
#ifdef VMX86_DEVEL
                                           // hostname for VNC connection
                                           {"host", required_argument, NULL, 'h'},
                                           // port for VNC connection
                                           {"port", required_argument, NULL, 'p'},
                                           // websocketURL
                                           {"weburl", required_argument, NULL, 'w'},
                                           // use udpfec
                                           {"udpfec", no_argument, NULL, 'f'},
                                           // use raw tcp rather than bwe for tcp sockets
                                           {"raw-tcp", no_argument, NULL, 'r'},
                                           {"benchmark", required_argument, NULL, 'b'},
#endif
                                           // ProductState
                                           {NULL, required_argument, NULL, '#'},
                                           // Max number of log files
                                           {"max-logs", required_argument, NULL, 'm'},
                                           {"wait-for-vnc", no_argument, NULL, OPT_CONNECT_VNC},
                                           {"probe-only", no_argument, NULL, OPT_PROBE_DECODE_HW},
                                           {0}};

   viewClient.programName = argv[0];
   viewClient.sslRequired = TRUE;
   viewClient.sslVerifyCerts = TRUE;
   viewClient.logMaxFiles = 50;
   viewClient.displayProtocol = MKS_DISPLAYPROTOCOL_INVALID;

   /*
    * In debug/devel builds only, log the input arguments
    */
   if (vmx86_debug || vmx86_devel) {
      int i;
      void *acc = Log_BufBegin();

      Log(LGPFX "Command line arguments:");
      for (i = 0; i < argc; i++) {
         Log_BufAppend(acc, "%s ", argv[i]);
      }
      Log_BufEndLevel(acc, HZN_LOG_INFO);
   }

   while (TRUE) {
      int opt = Util_GetOpt(argc, argv, options, UTIL_NONOPT_STOP, FALSE);

      if (opt == -1) {
         break;
      }

      switch (opt) {
      case 's':
         Preference_SetFromString(optarg, TRUE);
         break;
      case 'R':
         // Redundant.
         break;
      case 'L':
         ASSERT(optarg != NULL);
         viewClient.logFileName = optarg;
         break;
      case 'l':
         ASSERT(optarg != NULL);
         viewClient.logId = strtoul(optarg, (char **)NULL, 0);
         break;
      case '@':
         viewClient.cmdLineArgs = optarg;
         break;
      case 'H': {
         /*
          * Read the FD/Handle of the pipe on which to receive either the:
          * - socket to the server MKS, or
          * - the View Desktop target (host/port/token)
          */
         char *p;

         ASSERT(optarg != NULL);
         viewClient.pipeFd = strtol(optarg, &p, 0);
         if (*p) {
            ViewClientExitWithUsage();
         }
#if _WIN32
         /*
          * Check if the handle is good
          */
         {
            DWORD dwFlags;

            if (!GetHandleInformation((HANDLE)(intptr_t)viewClient.pipeFd, &dwFlags)) {
               char buf[0x400];

               snprintf(buf, sizeof(buf), "Bad Handle 0x%x, %s", viewClient.pipeFd,
                        Err_ErrString());
               Win32U_MessageBox(NULL, buf, "Invalid Handle", MB_OK | MB_ICONSTOP);
               ViewClientExitWithUsage();
            }
         }
#else
         if (fcntl(viewClient.pipeFd, F_GETFL) < 0) {
            ViewClientExitWithUsage();
         }
#endif
         break;
      }
      case 'V':
         viewClient.target = MKS_ROLE_TARGET_VDP;
         break;
      case 'I':
         viewClient.sslRequired = FALSE;
         break;
      case 'E':
         free(viewClient.locale);
         viewClient.locale = Util_SafeStrdup(optarg);
         break;
      case 'P':
         ASSERT(optarg != NULL);
         viewClient.displayProtocol = strtol(optarg, (char **)NULL, 0);
         break;
      case 'D':
         // See bug 76204.
         // Obsolete. User preferences are always ignored in remotemks.
         break;
      case 'C':
         ASSERT(optarg != NULL);
         if (Str_Strcasecmp(optarg, "on") == 0) {
            viewClient.sslVerifyCerts = TRUE;
         } else if (Str_Strcasecmp(optarg, "off") == 0) {
            viewClient.sslVerifyCerts = FALSE;
         }
         break;
#ifdef VMX86_DEVEL
      case 'h':
         viewClient.target = MKS_ROLE_TARGET_GENERIC_VNC;
         viewClient.displayProtocol = MKS_DISPLAYPROTOCOL_VNC;
         viewClient.host = optarg;
         break;
      case 'p': {
         char *p;

         viewClient.port = strtol(optarg, &p, 0);
         if (*p != '\0') {
            Warning("Bad Port Number: %s\n", optarg);
            ViewClientExitWithUsage();
            NOT_REACHED();
         }
         break;
      }
      case 'w':
         memset(viewClient.vdpTarget, 0, sizeof viewClient.vdpTarget);
         Str_Strcpy(viewClient.vdpTarget, optarg, sizeof viewClient.vdpTarget);
         viewClient.target = MKS_ROLE_TARGET_VDP;
         viewClient.displayProtocol = MKS_DISPLAYPROTOCOL_VNC;
         break;
      case 'f':
         viewClient.useUdpFec = TRUE;
         break;
      case 'b':
         /* Ordering is important for string length check */
         if (Str_Strncmp(optarg, "videoPlayer", 12) == 0) {
            viewClient.benchmarkMode = BENCHMARK_RENDER_PLAYER;
         }
         viewClient.displayProtocol = MKS_DISPLAYPROTOCOL_VNC;
         viewClient.target = MKS_ROLE_TARGET_GENERIC_VNC;
         break;
#endif
      case '#':
         ProductState_Deserialize(optarg);
         break;
      case 'm':
         ASSERT(optarg != NULL);
         viewClient.logMaxFiles = strtoul(optarg, (char **)NULL, 0);
         break;
      case 'r':
         viewClient.rawTCPSocket = TRUE;
         break;
      case OPT_CONNECT_VNC:
         viewClient.sslRequired = FALSE;
         viewClient.target = MKS_ROLE_TARGET_VDP;
         viewClient.displayProtocol = MKS_DISPLAYPROTOCOL_VNC;
         break;
      case OPT_PROBE_DECODE_HW:
         viewClient.onlyProbeMode = TRUE;
         viewClient.displayProtocol = MKS_DISPLAYPROTOCOL_VNC;
         viewClient.target = MKS_ROLE_TARGET_GENERIC_VNC;
#ifndef VMX86_RELEASE
         {
            char *testProbe = Preference_GetString("off", "viewClient.testProbe");

            if (Str_Strequal(testProbe, "off")) {
               free(testProbe);
            } else {
               // test with fake probe output
               if (Str_Strequal(testProbe, "crashbefore")) {
                  Panic("testProbe is panicking before outputting decodeCaps!");
               }

               printf("viewClient.decodeCaps.hevc.yuv420.d8bit.maxWidth=7680\n"
                      "viewClient.decodeCaps.hevc.yuv420.d8bit.maxHeight=4320\n"
                      "viewClient.decodeCaps.hevc.yuv420.d8bit.supportsI601=TRUE\n"
                      "viewClient.decodeCaps.hevc.yuv420.d8bit.supportsH709=TRUE\n"
                      "viewClient.decodeCaps.hevc.yuv420.d8bit.supportsF709=TRUE\n");
               fflush(stdout);

               if (Str_Strequal(testProbe, "5sec")) {
                  Util_Sleep(5);
               } else if (Str_Strequal(testProbe, "15sec")) {
                  Util_Sleep(15);
               } else if (Str_Strequal(testProbe, "35sec")) {
                  Util_Sleep(35);
               } else if (Str_Strequal(testProbe, "crash")) {
                  Panic("testProbe is panicking after outputting decodeCaps!");
               } else if (Str_Strequal(testProbe, "hang")) {
                  while (1) {
                     Util_Sleep(1);
                  }
               }
               free(testProbe);
               exit(1);
            }
         }
#endif
         break;
      case '?':
         /* getopt has already printed an error message */
         ViewClientExitWithUsage();
         break;
      default:
         NOT_REACHED();
      }
   }

   if (!viewClient.onlyProbeMode) {
      if (vmx86_debug || vmx86_devel) {
         Warning("Running in devel mode, allowing all options.\n");
      } else if (!viewClient.cmdLineArgs) {
         /*
          * Require the -@ option.
          * Not applicable in probing-only mode.
          */
         ViewClientExitWithUsage();
      }

      if (viewClient.displayProtocol == MKS_DISPLAYPROTOCOL_INVALID) {
         /*
          * Require the -P option.
          * Not applicable in probing-only mode.
          */
         ViewClientExitWithUsage();
      }
   }
}

/*-----------------------------------------------------------------------------
 *
 * ViewClientExitWithUsage --
 *
 *      Complain and bail.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      Process Armageddon.
 *
 *-----------------------------------------------------------------------------
 */

void
ViewClientExitWithUsage(void)
{
   if (vmx86_debug || vmx86_devel) {
      Warning("%s:\n", viewClient.programName);
      Warning("  -?, --help    - print this message\n");
      Warning("  -s <str>      - set config variable\n");
      Warning("  -h <host>     - set server hostname or IP\n");
      Warning("  -p <port>     - set server port\n");
      Warning("  -f            - use udpfec connection\n");
      Warning("  -r            - use raw tcp connection (for encoder dumps)\n");
      Warning("  -b <mode>     - set benchmark mode (render, renderFPS, videoPlayer)\n");
      Warning("  --probe-only  - run in decode caps probing mode\n");
      Warning("\n");
   } else {
      Msg_Post(
         MSG_ERROR,
         MSGID(mksMain.dontRunDirectly) "%s: this executable should not be invoked directly.\n",
         viewClient.programName);
   }
   exit(1);
}


/*
 *----------------------------------------------------------------------------
 *
 * ViewClientRecvVDPTargetFromPipe --
 *
 *      Receive the (PCoIP or Blast) VDP target info from the View client UI.
 *      Currently this is "ipaddr:port;authtoken".
 *
 * Results:
 *      TRUE on success, FALSE on failure.
 *      On success, the info is copied to mksRoleViewRemote.vdpTarget.
 *
 * Side effects:
 *      Receives target (blocking) on local pipe.
 *
 *----------------------------------------------------------------------------
 */

Bool
ViewClientRecvVDPTargetFromPipe(void)
{
#ifdef _WIN32
   VDP_TARGETINFO targetInfo = {0};
   uint32 argSize = 0;
   char *argBuffer = NULL;
   VDP_ARGS *args = NULL;
   uint32 total = 0;
   uint32 bytesIO = 0;
   uint32 available = 0;
   uint32 response = TRUE;
   HANDLE recvSocketHandle = (HANDLE)(intptr_t)viewClient.pipeFd;
   ViewControlAuthToken token = {0};

   // Read VDPTargetInfo.
   if (!ReadFile(recvSocketHandle, &targetInfo.target, sizeof targetInfo, &bytesIO, NULL)) {
      Warning(LGPFX "Couldn't read recvSocketHandle: %s\n", Err_ErrString());
      response = FALSE;
      goto Exit;
   }
   total += bytesIO;

   // Read vdpArgs length.
   if (!ReadFile(recvSocketHandle, &argSize, sizeof argSize, &bytesIO, NULL)) {
      Warning(LGPFX "Couldn't read vdpArgs length from the "
                    "recvSocketHandle: %s\n",
              Err_ErrString());
      response = FALSE;
      goto Exit;
   }
   total += bytesIO;

   // Set a safe upper bound of 1 MiB to the length we read in.
   if (argSize > MBYTES_2_BYTES(1)) {
      Warning(LGPFX "Read in potentially invalid vdpArgs (length %u) "
                    "which exceeds maximum\n",
              argSize);
      response = FALSE;
      goto Exit;
   }

   // Read vdpArgs.
   if (argSize != 0) {
      argBuffer = malloc(argSize);
      if (!argBuffer || !ReadFile(recvSocketHandle, argBuffer, argSize, &bytesIO, NULL)) {
         Warning(LGPFX "Couldn't read vdpArgs from the recvSocketHandle: %s\n", Err_ErrString());
         response = FALSE;
         goto Exit;
      }
      total += bytesIO;

      // Decode vdpArgs
      args = VDPTargetInfo_DecodeVDPArgs(argBuffer, argSize);

      VDPPluginHost_SetVdpArgs(args);
   }

   memset(viewClient.vdpTarget, 0, sizeof viewClient.vdpTarget);
   Str_Strncpy(viewClient.vdpTarget, sizeof viewClient.vdpTarget, targetInfo.target,
               sizeof targetInfo.target - 1);

   if (!WriteFile(recvSocketHandle, &response, sizeof(response), &total, NULL)) {
      Warning(LGPFX "Couldn't write response to recvSocketHandle: %s\n", Err_ErrString());
      response = FALSE;
      goto Exit;
   }

Exit:
   free(argBuffer);
   return response;
#else
   Bool response = FALSE;
   VDP_TARGETINFO targetInfo = {{0}};
   uint32 argSize = 0;
   char *argBuffer = NULL;
   VDP_ARGS *args = NULL;
   int err = 0;

   // Read targetInfo
   err = recv(viewClient.pipeFd, &targetInfo, sizeof targetInfo, 0);
   if (err < 0 || err != sizeof targetInfo) {
      Warning(LGPFX "%s: Couldn't receive %zu bytes for targetInfo from "
                    "socket: %d %s. Received bytes = %d\n",
              __FUNCTION__, sizeof targetInfo, Err_Errno(), Err_ErrString(), err);
      goto Exit;
   }
   // Read vdpArgs size
   err = recv(viewClient.pipeFd, &argSize, sizeof argSize, 0);
   if (err < 0 || err != sizeof argSize) {
      Warning(LGPFX "%s: Couldn't receive %zu bytes for argSize from "
                    "socket: %d %s. Received bytes = %d\n",
              __FUNCTION__, sizeof argSize, Err_Errno(), Err_ErrString(), err);
      goto Exit;
   }
   // Set a safe upper bound of 1 MiB to the length we read in.
   if (argSize > MBYTES_2_BYTES(1)) {
      Warning(LGPFX "Read in potentially invalid vdpArgs (length %u) "
                    "which exceeds maximum\n",
              argSize);
      goto Exit;
   }
   // Read vdpArgs
   if (argSize > 0) {
      argBuffer = Util_SafeMalloc(argSize);
      err = recv(viewClient.pipeFd, argBuffer, argSize, 0);
      if (err < 0 || err != argSize) {
         Warning(LGPFX "%s: Couldn't receive %d bytes for vdpArgs from "
                       "socket: %d %s. Received bytes = %d\n",
                 __FUNCTION__, argSize, Err_Errno(), Err_ErrString(), err);
         goto Exit;
      }
      // Decode vdpArgs
      args = VDPTargetInfo_DecodeVDPArgs(argBuffer, argSize);

      VDPPluginHost_SetVdpArgs(args);
   }

   if (*targetInfo.target) {
      memset(viewClient.vdpTarget, 0, sizeof viewClient.vdpTarget);
      Str_Strncpy(viewClient.vdpTarget, sizeof viewClient.vdpTarget, targetInfo.target,
                  sizeof targetInfo.target - 1);
   }
   response = TRUE;
Exit:
   free(argBuffer);
   return response;
#endif
}


/*
 *----------------------------------------------------------------------
 *
 * ViewClientConnectedCb --
 *
 *    Callback from vnc decoder once connection has been established
 *    and initial server header has been received.  Notify the status
 *    to UI via viewControl.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      See above.
 *
 *----------------------------------------------------------------------
 */

void
ViewClientConnectedCb(void *data)
{
   ViewClientLock_Lock();

   free(viewClient.connCookies);
   viewClient.connCookies = Util_SafeStrdup(data);
   viewClient.connected = TRUE;

   if (ViewClient_IsUIConnected()) {
      ViewControlMgr_NotifyUIRMKSConnected(TRUE);
      ViewControlMgr_NotifyUIRMKSCanReconnect(TRUE);
   }

   ViewScreenManager_SendResolutionRequest(-1, NULL, TRUE);
   ViewClientLock_Unlock();
}


/*
 *----------------------------------------------------------------------------
 *
 * ViewClient_Disconnected --
 *
 *      Called by client implementations with an error code to let us know
 *      that we lost our MKS server connection.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      Handles the situation appropriately.
 *
 *----------------------------------------------------------------------------
 */

void
ViewClient_Disconnected(int errorCode) // IN
{
   Log(LGPFX "Disconnected from server.\n");

   viewClient.connected = FALSE;
   viewClient.errorCode = errorCode;

   if (viewConfig.enableSupportMode) {
      sleep(viewConfig.supportModeDelaySeconds);
   }


   if (ViewClient_IsUIConnected()) {
      ViewControlMgr_NotifyUIRMKSConnected(FALSE);
      if (ViewControlMgr_GetAttemptReconnect()) {
         Log(LGPFX "Deferring exit due to pending reconnect notification.\n");
         return;
      }
   }

   if (errorCode) {
      Log(LGPFX "Disconnected from server with error code %d.\n", errorCode);
   }

   ViewClient_RequestExit();
}


/*
 *----------------------------------------------------------------------------
 *
 * ViewClient_RetryConnection --
 *
 *      Called via viewControl when a connection is in need of reconnecting.
 *
 * Results:
 *      TRUE or FALSE on reconnect success/failure.
 *
 *----------------------------------------------------------------------------
 */

Bool
ViewClient_RetryConnection(int reconnectAttemptCount) // IN
{
   Bool vncSuccess = FALSE;

   /* duplicate code from viewClient.c */
   if (ViewClient_IsBlastSession()) {
      char *vdpThumbprint;
      char *targetURL;
      SSLThumbprintType tpType;
      char *proxy = NULL;

      vdpThumbprint = VDPTarget_GetPreferredThumbprint(viewClient.vdpTarget, &tpType);

      ASSERT(SSL_THUMBPRINT_SHA1 == tpType || SSL_THUMBPRINT_SHA256 == tpType ||
             SSL_THUMBPRINT_SHA384 == tpType || SSL_THUMBPRINT_SHA512 == tpType);

      if (vmx86_devel && Config_GetBool(FALSE, "RemoteDisplay.disableBlastSSL")) {
         viewClient.sslRequired = FALSE;
      }

      targetURL = VDPTarget_GetWebSocketURL(viewClient.vdpTarget, viewClient.sslRequired);

      VDPPluginHost_GetHttpProxy(&proxy);

      ViewClientLock_Lock();
      vncSuccess =
         ViewClient_ConnectBlast(targetURL, vdpThumbprint, tpType, proxy, reconnectAttemptCount);

      free(vdpThumbprint);
      free(targetURL);
      free(proxy);

      if (!vncSuccess) {
         ViewControlMgr_SetViewConnectionResult(VDPCONNECT_FAILURE);
         ViewClient_Disconnected(1);
      }
      ViewClientLock_Unlock();
   }

   return vncSuccess;
}


/*
 *----------------------------------------------------------------------------
 *
 * ViewClient_RetryProtocolRedirectConnection --
 *
 *      Doing the reconnection flow after receving refresed token from UI.
 *
 * Results:
 *      None
 *
 *----------------------------------------------------------------------------
 */
void
ViewClient_RetryProtocolRedirectConnection(const char *target)
{
   Bool success = FALSE;
   if (ViewClient_IsBlastSession()) {

      // Update new vdpTarget info
      ASSERT(target != NULL);
      memset(viewClient.vdpTarget, 0, sizeof(viewClient.vdpTarget));
      Str_Strcpy(viewClient.vdpTarget, target, sizeof(viewClient.vdpTarget));

      Log("%s: New vdpTarget is received for reconnection.", __FUNCTION__);
      // -1: Network continuity with freshed "j" token in <token>
      success = ViewClient_RetryConnection(-1);

      if (!success) {
         Log("%s: Reconnection failed:%s.", __FUNCTION__, viewClient.vdpTarget);
      }
   }
}


/*
 *----------------------------------------------------------------------
 *
 * ViewClientProtocolPowerOn --
 *
 *    Do any connection-specific setup during PowerOn.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      See above.
 *
 *----------------------------------------------------------------------
 */

void
ViewClientProtocolPowerOn(void)
{
   ASSERT(ViewClient_IsBlastSession() || ViewClient_IsVDPSession() ||
          ViewClient_IsGenericVNCSession());

   if (ViewClient_IsBlastSession() || ViewClient_IsVDPSession()) {
      if (!VDPUnityViewControl_Init()) {
         Warning(LGPFX "Initialize VDPUnityViewControl failed.\n");
      }
      if (!VDPSharedFolderMgr_Init()) {
         Warning(LGPFX "Initialize VDPSharedFolderMgrViewControl failed.\n");
      }
      if (!VDPRdeCommonMgr_Init()) {
         Warning(LGPFX "Initialize VDPDPISyncMgr failed.\n");
      }
      if (!VDPMksVchan_Init()) {
         Warning(LGPFX "Initialize VDMksVchan failed.\n");
      }
      if (!VDPRdeCommon_Init()) {
         Warning(LGPFX "Initialize VDPRdeCommon failed.\n");
      }
      if (!VDPUsbRedirection_Init()) {
         Warning(LGPFX "Initialize VDPUsbRedirection failed.\n");
      }
      if (!VDPHtml5RedirMgr_Init()) {
         Warning(LGPFX "Initialize VDPHtml5RedirMgr failed.\n");
      }
      if (!VDPScreenCaptureMgr_Init()) {
         Warning(LGPFX "Initialize VDPScreenCaptureMgr failed.\n");
      }
      if (!VDPSdr_Init()) {
         Warning(LGPFX "Initialize VDPSdr failed.\n");
      }
#ifdef _WIN32
      if (!WhfbRedirection_Init()) {
         Warning(LGPFX "Initialize WhfbRedirection failed.\n");
      }
#elif defined(__APPLE__) || defined(__linux__)
      if (!VDPFido2RedirMgr_Init()) {
         Warning(LGPFX "Initialize VDPFido2RedirMgr failed.\n");
      }
#endif
      if (!VDPVMObserver_Init()) {
         Warning(LGPFX "Initialize VDPVMObserver failed.\n");
      }

      if (!VDPGeoRedirMgrCommon_Init()) {
         Warning(LGPFX "Initialize VDPGeoRedirMgrCommon failed.\n");
      }
   }

   /*
    * Protocol specific initialization and receipt of socket.
    */
   if (ViewClient_IsBlastSession()) {
      char *connectionUserModeStr = NULL;

      /* Initialize the VDP Overlay interface */
      if (!VDPOverlayHost_Init()) {
         Warning(LGPFX "Failed to initialize overlay interface\n");
      }

      if (ViewClient_IsUIConnected()) {
         /* Will connect with Blast agent (e.g. running in guest VM) */
         if (!ViewClientRecvVDPTargetFromPipe()) {
            Panic(LGPFX "Failed to retrieve VDP target from pipe. \n");
         }
      }

      /*
       * If configured to probe decodeCaps from the Horizon Client UI, then
       * initialize bora configs with the probed decodeCaps here. They will
       * be read when VNCClient_PowerOn() calls ViewClient_InitFFmpeg().
       */

      if (Str_Strcmp(viewConfig.decodeCapsProbingMode, "legacy") != 0 &&
          Str_Strcmp(viewConfig.decodeCapsProbingMode, "off") != 0 &&
          VDPPluginHost_GetDecodeCapsProbeResults()) {
         Preference_Log();
      }

      /*
       * Initialize the connection type for websocket & data connection,
       * based on the user mode received from UI side configuration.
       */

      if (!VDPPluginHost_GetConnectionUserMode(&connectionUserModeStr)) {
         Log(LGPFX "Unable to get connection user mode.\n");
      }

      BlastConnection_InitConnectionTypes(connectionUserModeStr);
      free(connectionUserModeStr);
   } else if (ViewClient_IsVDPSession()) {
      viewClient.vdpPluginInitialized = VDPPluginHost_Init();
      if (!viewClient.vdpPluginInitialized) {
         /*
          * User specified VDP protocol explicitly but we failed to load a VDP
          * plugin. This should never happen.
          */
         Panic(LGPFX "Failed to load VDP plugin.\n");
      }

      /*
       * Initialize the VDP Overlay interface
       */
      if (!VDPOverlayHost_Init()) {
         Warning(LGPFX "Failed to initialize overlay interface\n");
      }

      if (!ViewClientRecvVDPTargetFromPipe()) {
         Panic(LGPFX "Failed to retrieve VDP target from pipe.\n");
      }
   } else if (ViewClient_IsGenericVNCSession()) {
      /*
       * Do nothing; we'll wait for a command with the VNC connection
       * information.
       */
   } else {
      /*
       * Command line specified an unknown protocol.
       */
      Panic(LGPFX "Invalid protocol specified.\n");
   }

   if (ViewClient_IsBlastSession() || ViewClient_IsGenericVNCSession()) {
      Log(LGPFX "Powering on VNC Client\n");
      if (!VNCClient_PowerOn()) {
         Panic(LGPFX "Failed to power on the VNC client.\n");
      }
   }

   if (viewClient.benchmarkMode != BENCHMARK_NONE || viewClient.onlyProbeMode) {
      /*
       * Do nothing; benchmark and probing modes do not interact with a server.
       */
   } else if (ViewClient_IsBlastSession()) {
      Bool vncSuccess;
      VDPLocaleList localeList = {0};
      char *targetURL;
      char *vdpThumbprint = NULL;
      SSLThumbprintType tpType = SSL_THUMBPRINT_SHA1;
      char *proxy = NULL;

      vdpThumbprint = VDPTarget_GetPreferredThumbprint(viewClient.vdpTarget, &tpType);

      if (vmx86_devel && Config_GetBool(FALSE, "RemoteDisplay.disableBlastSSL")) {
         viewClient.sslRequired = FALSE;
      }

      targetURL = VDPTarget_GetWebSocketURL(viewClient.vdpTarget, viewClient.sslRequired);

      VDPPluginHost_GetHttpProxy(&proxy);

      ViewClientLock_Lock();
      vncSuccess = ViewClient_ConnectBlast(targetURL, vdpThumbprint, tpType, proxy, 0);

      free(vdpThumbprint);
      free(targetURL);
      free(proxy);

      if (!vncSuccess) {
         Log(LGPFX "Failed to connect to VNC client: vdpTarget\n");
         ViewClient_Disconnected(1);
      }
      ViewClientLock_Unlock();

      if (!Config_GetBool(TRUE, "mks.keyboard.enableVDPLocaleSync")) {
         Log(LGPFX "Keyboard locale sync is disabled\n");
         return;
      }
      // Retrieves keyboard locale list and then sets it to VNCClient.
      VDPPluginHost_GetKeyboardLocaleList(&localeList);

      VNCClient_SetKeyboardLocaleList(&localeList);
   } else if (ViewClient_IsVDPSession()) {
#ifdef __APPLE__
      VDPLocaleList localeList = {0};
#endif
      Log(LGPFX "Powering on VDP Plugin\n");
      ViewControlMgr_NotifyUIRMKSCanReconnect(FALSE);

      VDPPluginHost_Connect(viewClient.vdpTarget);

      Util_Zero(viewClient.vdpTarget, sizeof viewClient.vdpTarget);
#ifdef __APPLE__
      /*
       * Mac OS gives warning while getting keyboard locale in non-main thread, so we get locale
       * here and save it for later.
       */
      if (!Config_GetBool(TRUE, "mks.keyboard.enableVDPLocaleSync")) {
         Log(LGPFX "Keyboard locale sync is disabled\n");
         return;
      }
      VDPPluginHost_GetKeyboardLocaleList(&localeList);
      VDPPluginHost_SetKeyboardLocaleList(&localeList);
#endif
   } else if (ViewClient_IsGenericVNCSession()) {
      Bool success;

      ViewClientLock_Lock();
      if (viewClient.useUdpFec) {
         success = VNCClient_ConnectUDPFEC(viewClient.host, viewClient.port);
      } else {
         success = VNCClient_ConnectVNC(viewClient.host, viewClient.port, !viewClient.rawTCPSocket);
      }
      ViewClientLock_Unlock();
      if (!success) {
         Panic("Failed to connect to VNC server at TCP %s:%d.\n", viewClient.host, viewClient.port);
      }

      ViewClientConnectedCb(NULL);
   }
}


/*
 *----------------------------------------------------------------------
 *
 * ViewClientProtocolPowerOff --
 *
 *    Do any connection-specific tear-down during PowerOff.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      See above.
 *
 *----------------------------------------------------------------------
 */

void
ViewClientProtocolPowerOff(void)
{
   ASSERT(ViewClient_IsBlastSession() || ViewClient_IsVDPSession() ||
          ViewClient_IsGenericVNCSession());

   if (ViewClient_IsBlastSession() || ViewClient_IsVDPSession()) {
      VDPSharedFolderMgr_Exit();
      VDPRdeCommonMgr_Exit();
      VDPMksVchan_Exit();
      VDPUsbRedirection_Exit();
      VDPHtml5RedirMgr_Exit();
      VDPScreenCaptureMgr_Exit();
      VDPSdr_Exit();
#ifdef _WIN32
      WhfbRedirection_Exit();
#elif defined(__APPLE__) || defined(__linux__)
      VDPFido2RedirMgr_Exit();
#endif

      if (ViewClient_IsBlastSession()) {
         Log(LGPFX "Powering off Blast\n");
         VNCClient_RequestBlastToDisconnect(VDPCONNECT_USER_INIT_DISCONNECT);
         VNCClientView_Stop();
      } else if (ViewClient_IsVDPSession()) {
         Log(LGPFX "Powering off VDP Plugin\n");
         VDPPluginHost_Disconnect();
         VDPPluginHost_Exit();
      }

      /*
       * The VDPUnityViewControl is the only client for the vdp Unity plugin and
       * should wait for all client threads to terminate to avoid invalidating
       * the interface pointer referenced by the unitySvc.
       */
      VDPUnityViewControl_Exit();

      VDPRdeCommon_Exit();
      VDPOverlayHost_Exit();
      VDPVMObserver_Exit();
      VDPGeoRedirMgrCommon_Exit();
   }

   if (ViewClient_IsBlastSession() || ViewClient_IsGenericVNCSession()) {
      Log(LGPFX "Powering off VNC Client\n");
      ViewClientLock_Lock();
      VNCClient_PowerOff();
      ViewClientLock_Unlock();
   }

   free(viewClient.connCookies);
   viewClient.connCookies = NULL;
   free(viewClient.routeSpecifier);
   viewClient.routeSpecifier = NULL;
}


/*
 *----------------------------------------------------------------------
 *
 * ViewClientApplySSLOption --
 *
 *      Apply the SSL option. If the SSL option in the
 *      options list, set this option. Otherwise unset
 *      this option.
 *
 * Results:
 *      None.
 *
 *----------------------------------------------------------------------
 */

static void
ViewClientApplySSLOption(void *sslCtx,              // IN/OUT
                         long sslOptions,           // IN
                         long sslOption,            // IN
                         Bool enable,               // IN
                         const char *sslOptionName) // IN
{
   SSL_CTX *ctx = sslCtx;
   Bool flag;

   ASSERT(ctx);
   ASSERT(sslOptionName);

   if (sslOptions & sslOption) {
      SSL_CTX_set_options(ctx, sslOption);
      flag = enable;
   } else {
      SSL_CTX_clear_options(ctx, sslOption);
      flag = !enable;
   }

   if (flag) {
      Log(LGPFX "%s is Enabled\n", sslOptionName);
   } else {
      Log(LGPFX "%s is Disabled\n", sslOptionName);
   }
}


/*
 *----------------------------------------------------------------------------
 *
 * ViewClientParseSigAlgList --
 *
 *      Parse a null-terminated signature algorithm list in the format accepted
 *      by SSL_CTX_set1_sigalgs_list(): "ECDSA+SHA256:RSA+SHA256:..."
 *      and save the corresponding OpenSSL NIDs into sslVerifyParams, so they
 *      can be cross-checked with the incoming server certificate's signature
 *      algorithm. This server cert verification was introduced for NIAP/CSfC
 *      compliance.
 *
 *      This parsing function fails at first format error or unrecognized name
 *      in the input string and salvages all crpto+hash pairs prior to it.
 *
 * Results:
 *      Number of valid signature algorithm NIDs stored in the array.
 *
 *----------------------------------------------------------------------------
 */

static void
ViewClientParseSigAlgList(char *signatureList,            // IN
                          SSLVerifyParam *sslVerifyParam, // OUT
                          int *sigAlgCount)               // OUT
{
   char *sig, *hash, *token, *prevToken;
   int sigalgs_count, length, sigLen, hashLen, nid, i;

   ASSERT(signatureList != NULL);

   sig = prevToken = token = signatureList;
   sigalgs_count = sigLen = hashLen = 0;

   length = strlen(signatureList);

   // Translagte signatureList to sigalg NIDs for validating peer certificate
   while (1) {
      if (sig - signatureList < length) {
         hash = Str_Strchr(sig, '+');
         if (hash) {
            sigLen = hash - sig;
            // advance beyond the "+" for hash name
            if (++hash - signatureList < length) {
               prevToken = token;
               token = Str_Strchr(hash, ':');
               hashLen = token ? token - hash : strlen(hash);
            }
         }
      }

      if (sigLen == 0 || hashLen == 0) {
         // unexpected format error, truncate and return
         Warning("%s: Invalid format, truncating signature list %s at offset %d", __FUNCTION__,
                 signatureList, token ? (int)(token - signatureList) : length);
         if (token) {
            *token = '\0';
         }
         break;
      }

      if (strncasecmp(sig, "RSA", sigLen) == 0 && strlen("RSA") == sigLen) {
         if (strncasecmp(hash, "SHA224", hashLen) == 0 && strlen("SHA224") == hashLen) {
            nid = NID_sha224WithRSAEncryption;
         } else if (strncasecmp(hash, "SHA256", hashLen) == 0 && strlen("SHA256") == hashLen) {
            nid = NID_sha256WithRSAEncryption;
         } else if (strncasecmp(hash, "SHA384", hashLen) == 0 && strlen("SHA384") == hashLen) {
            nid = NID_sha384WithRSAEncryption;
         } else if (strncasecmp(hash, "SHA512", hashLen) == 0 && strlen("SHA512") == hashLen) {
            nid = NID_sha512WithRSAEncryption;
         } else {
            Warning("%s: Unrecognizable hash %s with crypto %s, truncating "
                    "signature list %s at offset %d",
                    __FUNCTION__, hash, sig, signatureList, (int)(prevToken - signatureList));
            *prevToken = '\0';
            break;
         }
      } else if (strncasecmp(sig, "DSA", sigLen) == 0 && strlen("DSA") == sigLen) {
         /*
          * DSA is generally not preferred to RSA and ECDSA, but we still parse
          * for it since the remainder of the signatureList could contain a
          * more useful entry; and because SSLCheckKeySizeAndAlgorithm()
          * mentions it.
          */
         if (strncasecmp(hash, "SHA224", hashLen) == 0 && strlen("SHA224") == hashLen) {
            nid = NID_dsa_with_SHA224;
         } else if (strncasecmp(hash, "SHA256", hashLen) == 0 && strlen("SHA256") == hashLen) {
            nid = NID_dsa_with_SHA256;
         } else {
            Warning("%s: Unrecognizable hash %s with crypto %s, truncating "
                    "signature list %s at offset %d",
                    __FUNCTION__, hash, sig, signatureList, (int)(prevToken - signatureList));
            *prevToken = '\0';
            break;
         }
      } else if (strncasecmp(sig, "ECDSA", sigLen) == 0 && strlen("ECDSA") == sigLen) {
         if (strncasecmp(hash, "SHA224", hashLen) == 0 && strlen("SHA224") == hashLen) {
            nid = NID_ecdsa_with_SHA224;
         } else if (strncasecmp(hash, "SHA256", hashLen) == 0 && strlen("SHA256") == hashLen) {
            nid = NID_ecdsa_with_SHA256;
         } else if (strncasecmp(hash, "SHA384", hashLen) == 0 && strlen("SHA384") == hashLen) {
            nid = NID_ecdsa_with_SHA384;
         } else if (strncasecmp(hash, "SHA512", hashLen) == 0 && strlen("SHA512") == hashLen) {
            nid = NID_ecdsa_with_SHA512;
         } else {
            Warning("%s: Unrecognizable hash %s with crypto %s, truncating "
                    "signature list %s at offset %d",
                    __FUNCTION__, hash, sig, signatureList, (int)(prevToken - signatureList));
            *prevToken = '\0';
            break;
         }
      } else {
         Warning("%s: Unrecognizable crypto %s, truncating signature list %s "
                 "at offset %d",
                 __FUNCTION__, sig, signatureList, (int)(prevToken - signatureList));
         *prevToken = '\0';
         break;
      }

      sslVerifyParam->sigalgs_list_nids[sigalgs_count++] = nid;

      if (!token) {
         // signature list parsed fully
         break;
      } else if (sigalgs_count == MAX_SIGALGO_EXT_ENTRIES) {
         // cannot accommodate more algorithms, so truncate signature list
         Warning("%s: Truncating signature list %s at offset %d", __FUNCTION__, signatureList,
                 (int)(token - signatureList));
         *token = '\0';
         break;
      }

      sigLen = hashLen = 0;
      sig = token + 1;
   }

   if (vmx86_debug || vmx86_devel) {
      Log(LGPFX "Saved %d signature algorithm NIDs:", sigalgs_count);
      for (i = 0; i < sigalgs_count; i++) {
         Log(LGPFX "%d ", sslVerifyParam->sigalgs_list_nids[i]);
      }
   }

   *sigAlgCount = sigalgs_count;
}


/*
 *-----------------------------------------------------------------------------
 *
 * ViewClient_SetRealMultimon --
 *
 *      Debug mode when real multimon is selected.
 *
 * Results:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

void
ViewClient_SetRealMultimon(Bool enable)
{
   if (!vmx86_release) {
      viewClient.isRealMultimon = enable;
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * ViewClient_IsRealMultimon --
 *
 *      Debug mode when real multimon is selected.
 *
 * Results:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

Bool
ViewClient_IsRealMultimon(void)
{
   if (vmx86_release) {
      return FALSE;
   }

   return viewClient.isRealMultimon;
}


/*
 *-----------------------------------------------------------------------------
 *
 * ViewClient_IsMultiScreenEnabled --
 *
 *      Is the multi screen config enabled.
 *
 * Results:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

Bool
ViewClient_IsMultiScreenEnabled(void)
{
   if (vmx86_release) {
      return FALSE;
   }

   return viewConfig.numScreenConfigs > 0;
}


/*
 *-----------------------------------------------------------------------------
 *
 * ViewClient_IsMultiWindowEnabled --
 *
 *      Is the multi window config enabled.
 *
 * Results:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

Bool
ViewClient_IsMultiWindowEnabled(void)
{
   if (vmx86_release) {
      return FALSE;
   }

   return viewConfig.numWindowConfigs > 0;
}


/*
 *----------------------------------------------------------------------------
 *
 * ViewClientParseScreenWindowOverrides --
 *
 *      Retrieve window/screen configuration overrides.
 *
 * Results:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

static void
ViewClientParseScreenWindowOverrides(void)
{
   int i, j;
   int numScreenConfigs;
   int numWindowConfigs;
   ViewClientDisplays *initialMonitors;
   int numInitialMonitors;

   /*
    * Don't attempt multi-window mode in app sessions.
    */
   if (ViewClient_IsAppSession()) {
      return;
   }

   ViewClientOS_GetPhysicalTopology(&initialMonitors, &numInitialMonitors, TRUE, FALSE);

   numScreenConfigs = 0;
   while (numScreenConfigs < VIEW_MAX_OVERRIDE_CONFIG) {
      char *screenConfig = NULL;
      char *token;
      char *copy;
      char *tmp = NULL;
      int count = 0;
      int numInitialScreens;

      /* Get the next config */
      screenConfig = Config_GetString(NULL, "viewClient.screenConfig%d", numScreenConfigs);
      if (!screenConfig) {
         break;
      }

      /*
       * If the screenConfig starts with +, then we take chop up the physical
       * topology into screens, such as +3x2 will create 3 wide and 2 tall.
       *
       * If the screenConfig is -all, then we go into fullscreen mode on all
       * screens.
       *
       * If the screenConfig is -1,-3, then we go into fullscreen mode on
       * the selected physical monitors.
       *
       * If the screenConfig is x,y,w,h, then we create a screen at that position
       * and width/height, which can be comma seperated for more screens such
       * as 0,0,1920,1080,1920,0,1920,1080 will create a side-by-side 1920x1080
       * screen config.
       *
       */
      if (!Str_Strncmp(screenConfig, "+", 1)) {
         int w, h;
         int x = 0;
         int y = 0;

         if (2 != sscanf(screenConfig, "+%ix%i", &w, &h)) {
            Panic("Failed to parse screenConfig%d\n", numScreenConfigs);
         }

         numInitialScreens = w * h;
         numInitialScreens = HZN_CLAMP(numInitialScreens, 1, VNC_MAX_SCREENS);

         for (i = 0; i < h && count < VNC_MAX_SCREENS; i++) {
            x = 0;
            for (j = 0; j < w && count < VNC_MAX_SCREENS; j++) {
               Rect_SetXYWH(&viewConfig.screenOverrides[numScreenConfigs].screenRect[count], x, y,
                            Rect_Width(&initialMonitors[0].screenRect) / w,
                            Rect_Height(&initialMonitors[0].screenRect) / h);
               viewConfig.screenOverrides[numScreenConfigs].screenDpi[count] = 96;
               x += Rect_Width(&initialMonitors[0].screenRect) / w;
               count++;
            }
            y += Rect_Height(&initialMonitors[0].screenRect) / h;
         }

         viewConfig.screenOverrides[numScreenConfigs].fullScreenConfig = FALSE;
      } else if (!Str_Strncmp(screenConfig, "-all", 4)) {
         for (i = 0; i < numInitialMonitors && count < VNC_MAX_SCREENS; i++) {
            viewConfig.screenOverrides[numScreenConfigs].screenRect[count] =
               initialMonitors[i].screenRect;
            viewConfig.screenOverrides[numScreenConfigs].screenDpi[count] = 96;
            count++;
         }

         viewConfig.screenOverrides[numScreenConfigs].fullScreenConfig = TRUE;

         numInitialScreens = numInitialMonitors;
      } else if (!Str_Strncmp(screenConfig, "-", 1)) {
         int *monitors = NULL;
         int numMonitors = 0;
         int iter;
         copy = Util_SafeStrdup(screenConfig);
         token = strtok_r(copy, ",", &tmp);
         while (token != NULL) {
            monitors = Util_SafeRealloc(monitors, sizeof(*monitors) * (numMonitors + 1));
            monitors[numMonitors] = -atoi(token) - 1;

            if (monitors[numMonitors] >= numInitialMonitors) {
               Panic("Invalid monitor specified, use range 1...%d on screenConfig%d\n",
                     numInitialMonitors, numScreenConfigs);
            }

            numMonitors++;

            token = strtok_r(NULL, ",", &tmp);
         }
         free(copy);

         iter = 0;
         for (i = 0; i < numInitialMonitors && count < VNC_MAX_SCREENS; i++) {
            if (monitors[iter] == i) {
               viewConfig.screenOverrides[numScreenConfigs].screenRect[count] =
                  initialMonitors[i].screenRect;
               viewConfig.screenOverrides[numScreenConfigs].screenDpi[count] = 96;
               iter++;
               count++;
            }
         }

         free(monitors);

         viewConfig.screenOverrides[numScreenConfigs].fullScreenConfig = TRUE;

         numInitialScreens = numMonitors;
      } else {
         copy = Util_SafeStrdup(screenConfig);
         token = strtok_r(copy, ",", &tmp);
         while (token != NULL) {
            int t[4];
            for (i = 0; i < ARRAYSIZE(t); i++) {
               if (token) {
                  t[i] = atoi(token);
                  token = strtok_r(NULL, ",", &tmp);
               } else {
                  Panic("Failed to parse screenConfig%d\n", numScreenConfigs);
               }
            }
            Rect_SetXYWH(&viewConfig.screenOverrides[numScreenConfigs].screenRect[count], t[0],
                         t[1], t[2], t[3]);
            viewConfig.screenOverrides[numScreenConfigs].screenDpi[count] = 96;
            count++;
            if (count == VNC_MAX_SCREENS) {
               Warning("screenConfig%d has too many screens, clamping to 16\n", numScreenConfigs);
               break;
            }
         }
         numInitialScreens = count;
         free(copy);
         viewConfig.screenOverrides[numScreenConfigs].fullScreenConfig = FALSE;
      }

      free(screenConfig);

      for (i = 0; i < numInitialScreens; i++) {
         if (Rect_IsEmpty(&viewConfig.screenOverrides[numScreenConfigs].screenRect[i])) {
            Panic("Screen of screenConfig%d, is empty\n", numScreenConfigs);
         }
      }

      ASSERT(numInitialScreens <= VNC_MAX_SCREENS);
      viewConfig.screenOverrides[numScreenConfigs].numScreens = numInitialScreens;

      numScreenConfigs++;
   }

   free(initialMonitors);

   viewConfig.currentScreenConfig = 0;
   viewConfig.numScreenConfigs = numScreenConfigs;

   for (j = 0; j < numScreenConfigs; j++) {
      Warning("Screen Config %d Fullscreen:%s\n", j,
              viewConfig.screenOverrides[j].fullScreenConfig ? "Yes" : "No");
      for (i = 0; i < viewConfig.screenOverrides[j].numScreens; i++) {
         Warning("\tScreen %d: %dx%d @ %dx%d DPI:%d\n", i,
                 Rect_XYWH(&viewConfig.screenOverrides[j].screenRect[i]),
                 viewConfig.screenOverrides[j].screenDpi[i]);
      }
   }

   /*
    * The windowConfig must always be 8 parameters which specifies the source
    * rectangle in agent coordinates, and then followed by the destination rectangle
    * on the client device. For example, 0,0,1920,1080,0,0,800,600 will track agent
    * pixels at 1920x1080 @ 0x0, and display it on the client in a window of
    * the size 800x600.
    *
    * If the windowConfig first parameter starts with -, then we take that as a signal to track
    * a specific screen within that window. Such as -1 will track screen 0. Note
    * that screen tracking starts at 0, -2 will track screen 1. For example,
    * -1,0,0,0,0,0,800,600 will track screen 0 and display it in a window of size 800x600.
    * -2,0,0,0,0,0,1024,768 will track screen 1 and display it in a window of size 1024x768.
    *
    * If the windowConfig fifth parameter starts with -, then we take that as a signal to
    * create a window as large as the tracking screen rather than any specific size. For example,
    * -1,0,0,0,-1,0,0,0 will track screen 0 and create a window of the same size.
    * -2,0,0,0,-1,0,0,0 will track screen 1 and create a window of the same size.
    *  NOTE: That the fifth parameter just needs to be negative and the number does not
    *  correlate to any particular parameter.
    */
   numWindowConfigs = 0;
   while (numWindowConfigs < VIEW_MAX_OVERRIDE_CONFIG) {
      VMRect *windowSrcRect = &viewConfig.windowOverrides[numWindowConfigs].windowSrcRect;
      VMRect *windowDstRect = &viewConfig.windowOverrides[numWindowConfigs].windowDstRect;
      char *windowConfig = NULL;
      char *token;
      char *copy;
      char *tmp = NULL;
      Bool swap = FALSE;

      windowConfig = Config_GetString(NULL, "viewClient.windowConfig%d", numWindowConfigs);
      if (!windowConfig) {
         break;
      }

      copy = Util_SafeStrdup(windowConfig);
      token = strtok_r(copy, ",", &tmp);
      while (token != NULL) {
         int t[4];
         for (i = 0; i < 4; i++) {
            if (token) {
               t[i] = atoi(token);
               token = strtok_r(NULL, ",", &tmp);
            } else {
               Panic("Failed to parse windowConfig%d\n", numWindowConfigs);
            }
         }
         if (swap) {
            Rect_ClampedSetXYWH(windowDstRect, t[0], t[1], t[2], t[3]);
            swap = FALSE;
            break;
         } else {
            Rect_ClampedSetXYWH(windowSrcRect, t[0], t[1], t[2], t[3]);
            swap = TRUE;
         }
      }
      free(copy);

      if (Rect_X(windowSrcRect) < 0 || Rect_Y(windowSrcRect) < 0) {
         viewConfig.windowOverrides[numWindowConfigs].trackScreen =
            MAX(0, -Rect_X(windowSrcRect) - 1);
         Rect_SetXYWH(windowSrcRect, 0, 0, Rect_WH(windowSrcRect));
      } else if (Rect_IsEmpty(windowSrcRect)) {
         Panic("Source Window of windowConfig%d, is empty\n", numWindowConfigs);
      }

      if (Rect_X(windowDstRect) < 0 || Rect_Y(windowDstRect) < 0) {
         viewConfig.windowOverrides[numWindowConfigs].trackScreenForWindowSize = TRUE;
         Rect_SetXYWH(windowDstRect, 0, 0, Rect_WH(windowDstRect));
      } else {
         if (Rect_IsEmpty(windowDstRect)) {
            Panic("Destination Window of windowConfig%d, is empty\n", numWindowConfigs);
         }

         if (Rect_Width(windowDstRect) < 640 || Rect_Height(windowDstRect) < 480) {
            Panic("Please specify a sane dstRect. (minimum 640x480)");
         }
      }

      free(windowConfig);

      numWindowConfigs++;
   }

   viewConfig.numWindowConfigs = numWindowConfigs;

   for (j = 0; j < numWindowConfigs; j++) {
      Warning("Window Config %d\n", j);
      Warning("\tWindow %d: SRC: %dx%d @ %dx%d DST: %dx%d @ %dx%d\n", j,
              Rect_XYWH(&viewConfig.windowOverrides[j].windowSrcRect),
              Rect_XYWH(&viewConfig.windowOverrides[j].windowDstRect));
   }
}


/*
 *----------------------------------------------------------------------------
 *
 * ViewClientIsProtocolSupported --
 *
 *      Sanity check on sslDisabledProtocols to make sure all enabled
 *      protocols passed in from UI are supported.
 *
 * Results:
 *      TRUE if no unsupported protocols are found.
 *
 *----------------------------------------------------------------------------
 */

static Bool
ViewClientIsProtocolSupported(uint64 sslDisabledProtocols) // IN
{
   static const struct {
      long protocol;
      const char *name;
   } unSupportedProtocols[] = {{SSL_OP_NO_SSLv3, "SSLv3"}, {SSL_OP_NO_TLSv1, "TLS 1.0"}};

   static const uint8 unSupportedProtocolsNum =
      sizeof unSupportedProtocols / sizeof unSupportedProtocols[0];

#define IS_PROTOCOL_ENABLED(protocol) !((sslDisabledProtocols) & (protocol))

   int idx;
   for (idx = 0; idx < unSupportedProtocolsNum; idx++) {
      if (IS_PROTOCOL_ENABLED(unSupportedProtocols[idx].protocol)) {
         Warning(LGPFX "%s protocol is enabled but NOT supported.\n",
                 unSupportedProtocols[idx].name);
         return FALSE;
      }
   }

#undef IS_PROTOCOL_ENABLED

   return TRUE;
}


/*
 *----------------------------------------------------------------------------
 *
 * ViewClientApplySSLSettings --
 *
 *      Apply any needed SSL settings.
 *
 * Results:
 *      TRUE if the settings were applied correctly.
 *
 *----------------------------------------------------------------------------
 */

STATIC Bool
ViewClientApplySSLSettings(void) // IN
{
   if (ViewClient_IsBlastSession()) {
      SSL_CTX *sslCtx =
         (viewClient.sslCtx != NULL) ? viewClient.sslCtx : SSL_DefaultClientContext();

      const BLAST_SSL_SETTINGS *sslSettings = VDPPluginHost_GetSSLSettings();

      if (sslSettings != NULL) {
         if (!ViewClientIsProtocolSupported(sslSettings->sslDisabledProtocols)) {
            return FALSE;
         }

         ViewClientApplySSLOption(sslCtx, sslSettings->sslDisabledProtocols, SSL_OP_NO_SSLv3, FALSE,
                                  "SSLv3");
         ViewClientApplySSLOption(sslCtx, sslSettings->sslDisabledProtocols, SSL_OP_NO_TLSv1, FALSE,
                                  "TLSv1");
         ViewClientApplySSLOption(sslCtx, sslSettings->sslDisabledProtocols, SSL_OP_NO_TLSv1_1,
                                  FALSE, "TLSv1_1");

#define IS_TLS_PROTOCOL_ENABLED(sslctx, protocol)                                                  \
   (0 == (SSL_CTX_get_options((sslCtx)) & (protocol)))

         /*
          * TLS 1.1 is disabled by default in OpenSSL 3, so we need to set
          * the security level to 0 to enable using it with OpenSSL 3.
          * Note that if FIPS mode is enabled TLS 1.1 will be disallowed
          * and added in sslSettings->sslDisabledProtocols regardless so
          * we won't call SSL_CTX_set_security_level.
          */
         if (IS_TLS_PROTOCOL_ENABLED(sslCtx, SSL_OP_NO_TLSv1_1)) {
            SSL_CTX_set_security_level(sslCtx, 0);
            Log(LGPFX "Enabling TLS 1.1 per configuration with OpenSSL 3.\n");
         } else if (SSL_IsFIPSMode()) {
            Log(LGPFX "TLS 1.1 is disabled in FIPS mode.\n");
         } else {
            Log(LGPFX "TLS 1.1 is disabled per configuration.\n");
         }

         ViewClientApplySSLOption(sslCtx, sslSettings->sslDisabledProtocols, SSL_OP_NO_TLSv1_2,
                                  FALSE, "TLSv1_2");

         if (IS_TLS_PROTOCOL_ENABLED(sslCtx, SSL_OP_NO_TLSv1_2) ||
             IS_TLS_PROTOCOL_ENABLED(sslCtx, SSL_OP_NO_TLSv1_1)) {
            if (sslSettings->sslCipherString != NULL) {
               Log(LGPFX "TLS 1.1/1.2: Applying alternate TLS cipher list: %s\n",
                   sslSettings->sslCipherString);
               if (!SSL_CTX_set_cipher_list(sslCtx, sslSettings->sslCipherString)) {
                  Warning(LGPFX "TLS 1.1/1.2: Unable to use any of the alternate "
                                "TLS ciphers!\n");
                  return FALSE;
               }
            }
         }

         ViewClientApplySSLOption(sslCtx, sslSettings->sslDisabledProtocols, SSL_OP_NO_TLSv1_3,
                                  FALSE, "TLSv1_3");

         if (IS_TLS_PROTOCOL_ENABLED(sslCtx, SSL_OP_NO_TLSv1_3)) {
            if (SSL_IsFIPSMode()) {
               Log(LGPFX "TLS 1.3 is enabled in FIPS mode per configuration.\n");
            }
            if (sslSettings->sslCipherSuites != NULL) {
               Log(LGPFX "TLS 1.3: Applying alternate TLS cipher suites: %s\n",
                   sslSettings->sslCipherSuites);
               if (!SSL_CTX_set_ciphersuites(sslCtx, sslSettings->sslCipherSuites)) {
                  Warning(LGPFX "TLS 1.3: Unable to use any of the alternate "
                                "TLS cipher suites!\n");
                  return FALSE;
               }
            }
         }

#undef IS_TLS_PROTOCOL_ENABLED

         // Honor signature list if provided
         if (sslSettings->sslSignatureAlgorithms != NULL) {
            int sigalgs_count;
            ViewClientParseSigAlgList(sslSettings->sslSignatureAlgorithms,
                                      &viewClient.sslVerifyParam, &sigalgs_count);
            if (sigalgs_count == 0) {
               Warning(LGPFX "unable to apply SSL signature algorithms: %s",
                       sslSettings->sslSignatureAlgorithms);
               return FALSE;
            }

            Log(LGPFX "applying alternate SSL signature algorithms: %s\n",
                sslSettings->sslSignatureAlgorithms);
            viewClient.sslVerifyParam.sigalgs_count = sigalgs_count;
            if (!SSL_CTX_set1_sigalgs_list(sslCtx, sslSettings->sslSignatureAlgorithms)) {
               Warning(LGPFX "unable to apply alternate SSL "
                             "signature algorithms!\n");
               return FALSE;
            }

            /*
             * strictTLSVerify is opt-in flag for server cert verificaton. It
             * was introduced for NIAP/CSfC compliance, but can be used outside
             * of security clearance also if so desired.
             * Coverage will extend over time, starting off with TLS signature
             * algorithms.
             */
            viewClient.sslVerifyParam.strictTLSVerify = TRUE;
         }

         // Use the specified curves for ECDHE ciphers
         if (sslSettings->sslSupportedGroups != NULL) {
            Log(LGPFX "applying alternate SSL supported groups (curves): %s\n",
                sslSettings->sslSupportedGroups);
            if (!SSL_CTX_set1_curves_list(sslCtx, sslSettings->sslSupportedGroups)) {
               Warning(LGPFX "unable to apply alternate SSL "
                             "supported groups (curves)!\n");
               return FALSE;
            }
         }
      }
   }
   return TRUE;
}


/*
 *----------------------------------------------------------------------------
 *
 * ViewClientConfigSslCrlCache --
 *
 *      Read registry settings and call into SSL_InitCrlCache() to enable,
 *      disable and configure SSL CRL cache. Refer to DPM-11468 for details.
 *
 * Results:
 *      * If 'EnableSslCrlCache' is set to 'false' (case insensitive), SSL
 *        CRL cache will be disabled.
 *
 *      * If the folder specified in 'SslCrlCachePath' doesn't exist or can
 *        not be created, SSL CRL cache will be disabled.
 *
 *      * If SSL_InitCrlCache() fails, SSL CRL cache will be disabled.
 *
 *      * If this function runs on platforms other than Windows, SSL CRL
 *        cache will be disabled.
 *
 * Side effects:
 *      * For ViewClient, this feature is supported on Windows and Android
 *        only. Therefore this function has only Windows implementation.
 *        Android implementation will be in its own source file separately.
 *
 *      * SSL CRL cache only works when PKI Verification is chosen for Blast
 *        connection and Certificate Revocation Check is enabled.
 *
 *----------------------------------------------------------------------------
 */

static void
ViewClientConfigSslCrlCache(void)
{
#ifdef _WIN32
   const char *regKeyPath = HORIZON_VDM_REG_GPO_ROOT_A "\\Client\\Security";

   HRESULT result;
   HKEY hRegKey = NULL;
   char crlCacheRegBuf[MAX_PATH] = {'\0'};
   DWORD crlCacheRegBufSize;
   char crlCachePath[MAX_PATH] = {'\0'};
   int crlCachePathLen = sizeof crlCachePath;
   DWORD crlCacheUpdatePeriodHours;

   result = Win32U_RegOpenKeyEx(HKEY_LOCAL_MACHINE, regKeyPath, 0, KEY_READ, &hRegKey);
   if (result != ERROR_SUCCESS || hRegKey == NULL) {
      Warning(LGPFX "Failed to open reg key for SSL CRL cache. "
                    "[hr = 0x%x]. SSL CRL cache is disabled.\n",
              result);
      return;
   }

   /*
    * Check if kill switch is set. SSL CRL cache won't be enabled
    * until SSL_InitCrlCache() is called successfully.
    */
   crlCacheRegBufSize = sizeof crlCacheRegBuf;
   result =
      Win32U_RegQueryStringValue(hRegKey, "EnableSslCrlCache", crlCacheRegBuf, &crlCacheRegBufSize);
   if (result == ERROR_SUCCESS && 0 == Str_Strcasecmp(crlCacheRegBuf, "false")) {
      RegCloseKey(hRegKey);
      hRegKey = NULL;
      Log(LGPFX "SSL CRL cache is disabled from registry.\n");
      return;
   }

   // Check if registry setting for SslCrlCachePath is set
   crlCacheRegBufSize = sizeof crlCacheRegBuf;
   result =
      Win32U_RegQueryStringValue(hRegKey, "SslCrlCachePath", crlCacheRegBuf, &crlCacheRegBufSize);
   if (result == ERROR_SUCCESS && crlCacheRegBuf[0] != '\0') {
      Str_Strcpy(crlCachePath, crlCacheRegBuf, crlCachePathLen);
   } else {
      Log(LGPFX "SslCrlCachePath is not configured. "
                "Will use default value.\n");
   }

   /*
    * Check if 'SslCrlCacheUpdatePeriodHours' is set, by default
    * it's 23 hours, meaning the CRL file will need to be downloaded
    * and updated in file cache within a day since the last access of
    * the CRL file by Blast connection. This value is in line with
    * BSG's CRL cache update setting.
    */
   result = Win32U_RegQueryDWORDValue(hRegKey, "SslCrlCacheUpdatePeriodHours",
                                      &crlCacheUpdatePeriodHours);
   if (result != ERROR_SUCCESS) {
      crlCacheUpdatePeriodHours = 23;
      Log(LGPFX "SslCrlCacheUpdatePeriodHours is not configured. "
                "Will use default value %d.\n",
          crlCacheUpdatePeriodHours);
   }

   RegCloseKey(hRegKey);
   hRegKey = NULL;

   /*
    * If no registry settings found for 'SslCrlCachePath', we will use
    * current user's TEMP folder as the root path and create a subfolder
    * 'sslcrlcache' under it. Then use the subfolder as 'SslCrlCachePath'.
    */
   if (crlCachePath[0] == '\0') {
      char *tmpDir = File_GetSafeTmpDir(FALSE);
      if (tmpDir != NULL) {
         char *tmpCrlCachePath = Str_SafeAsprintf(NULL, "%s\\sslcrlcache", tmpDir);
         Str_Strcpy(crlCachePath, tmpCrlCachePath, crlCachePathLen);
         free(tmpCrlCachePath);
         tmpCrlCachePath = NULL;
         free(tmpDir);
         tmpDir = NULL;
      }
   }

   if (!File_IsDirectory(crlCachePath) && !File_CreateDirectory(crlCachePath)) {
      Warning(LGPFX "Failed to create folder [%s] for SSL CRL cache. %s\n", crlCachePath,
              Err_ErrString());
      crlCachePath[0] = '\0';
   }

   if (crlCachePath[0] == '\0') {
      Log(LGPFX "SslCrlCachePath is not set. SSL CRL cache is disabled.\n");
      return;
   }

   Log(LGPFX "SslCrlCachePath: %s, SslCrlCacheUpdatePeriodHours: %d\n", crlCachePath,
       crlCacheUpdatePeriodHours);

   // Enable and configure SSL CRL cache
   if (!SSL_InitCrlCache(crlCachePath, crlCacheUpdatePeriodHours)) {
      Warning(LGPFX "Failed to initialize SSL CRL cache. "
                    "SSL CRL cache is disabled.\n");
      return;
   }

   Log(LGPFX "SSL CRL cache is enabled.\n");
#else
   Log(LGPFX "SSL CRL cache is disabled.\n");
#endif
}


/*
 *----------------------------------------------------------------------
 *
 * ViewClientSetupCertificateCheck_Helper --
 *
 *      Configure the actual ThumbPrint / Certificate checks.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------
 */

typedef enum TP_CERT_CHECK { TP_CHECK = 1 << 0, CERT_CHECK = 1 << 1 } TP_CERT_CHECK;

static void
ViewClientSetupCertificateCheck_Helper(TP_CERT_CHECK certCheck,  // IN
                                       const char *thumbprint,   // IN
                                       SSLThumbprintType tpType) // IN
{
   if (certCheck & TP_CHECK) {
      Log(LGPFX "Expected thumbprint for remote display: %s\n", thumbprint ? thumbprint : "NULL");

      if (thumbprint) {
         viewClient.sslVerifyParam.tpType = tpType;
         Str_Strcpy(viewClient.sslVerifyParam.expectedThumbprintString, thumbprint,
                    sizeof(viewClient.sslVerifyParam.expectedThumbprintString));
      }
   }

   if (certCheck & CERT_CHECK) {
      char hostName[VDP_MAX_HOST_NAME + 1] = {0};
      uint32 fqdnSize = sizeof hostName;

      if (VDPPluginHost_GetTargetFQDN(hostName, &fqdnSize) && fqdnSize > 0) {
         // Use FQDN for SSL certificate hostname match if FQDN is provided
         Str_Strcpy(viewClient.sslVerifyParam.hostName, hostName,
                    sizeof viewClient.sslVerifyParam.hostName);
      }

      // Certificate revocation check flag will be passed in from client UI
      viewClient.sslVerifyParam.checkRevocations = VDPPluginHost_GetStrictCertRevocationCheck();

      if (viewClient.sslVerifyParam.checkRevocations) {
         /*
          * Enable revocation check for Blast NIAP certification
          * on Windows and Android platform.
          */
         viewClient.sslVerifyParam.revocationCheckWindowsAndriod = TRUE;

         // Add optional CRL checks for Blast NIAP certification
         viewClient.sslVerifyParam.optionalCrlCheck = TRUE;

         // Set max CRL size to 20MB (same as Android setting)
         viewClient.sslVerifyParam.crlMaxResponseSize = 20 * 1024 * 1024;

         // Set CRL check to the most strict mode for NIAP certification
         viewClient.sslVerifyParam.requireCrls = TRUE;
         viewClient.sslVerifyParam.crlMode = SSL_VERIFY_CRL_MODE_STRICT;

         // Enable and configure SSL CRL cache
         ViewClientConfigSslCrlCache();
      }

      Log(LGPFX "StrictCertRevocationCheck = %d\n", viewClient.sslVerifyParam.checkRevocations);
   }
}


/*
 *----------------------------------------------------------------------
 *
 * ViewClientSetupCertificateCheck --
 *
 *      Setup thumbprint and/or certificate checks base on
 *      certCheckMode passed in from client UI through VDP args.
 *
 * Results:
 *      True if succeeds, false otherwise.
 *
 * Side effects:
 *      DPM-8513
 *
 *----------------------------------------------------------------------
 */

static Bool
ViewClientSetupCertificateCheck(const char *thumbprint,   // IN
                                SSLThumbprintType tpType) // IN
{
   VDPCertCheckMode certCheckMode = VDPPluginHost_GetCertificateCheckMode();

   Log(LGPFX "CertificateCheckMode = %d\n", certCheckMode);

   switch (certCheckMode) {
   // Thumbprint check only. Existing behavior.
   case VDP_CERTCHECK_THUMBPRINT: // [[fallthrough]]
   /*
      Thumbprint check first, if fails do certificate verification.
      Existing behavior.
    */
   case VDP_CERTCHECK_THUMBPRINT_OR_PKI: {
      TP_CERT_CHECK certCheck = TP_CHECK;

      viewClient.sslVerifyParam.unverifiedPeerAction =
         (certCheckMode == VDP_CERTCHECK_THUMBPRINT
             ? SSL_UNVERIFIED_REQUIRE_THUMBPRINT_MATCH
             : SSL_UNVERIFIED_REQUIRE_THUMBPRINT_OR_CERTIFICATE);

      if (certCheckMode == VDP_CERTCHECK_THUMBPRINT_OR_PKI) {
         certCheck |= CERT_CHECK;
      }

      ViewClientSetupCertificateCheck_Helper(certCheck, thumbprint, tpType);
      break;
   }

   // Strict certificate verification only. New behavior for DPM-8513.
   case VDP_CERTCHECK_PKI: // [[fallthrough]]
   /*
    * Both thumbprint and strict certificate verification.
    * New behavior for DPM-8513.
    */
   case VDP_CERTCHECK_THUMBPRINT_AND_PKI: {
      TP_CERT_CHECK certCheck = CERT_CHECK;

      viewClient.sslVerifyParam.unverifiedPeerAction =
         (certCheckMode == VDP_CERTCHECK_PKI ? SSL_UNVERIFIED_REQUIRE_CERTIFICATE_CHAIN
                                             : SSL_UNVERIFIED_REQUIRE_THUMBPRINT_AND_CERTIFICATE);

      // Block all workarounds
      viewClient.sslVerifyCerts = TRUE;
      viewConfig.permitUnverifiedWebSocketSSL = FALSE;

      // Explicitly enforce global option to verify peer certificate
      SSL_SetVerifySSLCertificates(SSL_VERIFY_CERTIFICATES_ON);

      if (certCheckMode == VDP_CERTCHECK_THUMBPRINT_AND_PKI) {
         certCheck |= TP_CHECK;
      }

      ViewClientSetupCertificateCheck_Helper(certCheck, thumbprint, tpType);
      break;
   }

   default:
      Warning(LGPFX "Invalid certificate check mode.\n");
      return FALSE;
   }

   /*
    * Allow unverified proceed for exisitng workarounds and
    * when security level is set to VDP_SECURITY_NONE.
    */
   if (VDP_SECURITY_NONE == VDPPluginHost_GetSecurityLevel() ||
       viewConfig.permitUnverifiedWebSocketSSL || viewClient.sslVerifyCerts == FALSE) {
      viewClient.sslVerifyParam.unverifiedPeerAction = SSL_UNVERIFIED_PROCEED;
   }

   return TRUE;
}


/*
 *----------------------------------------------------------------------
 *
 * ViewClient_ConnectBlast --
 *
 *      Perform the SSL and VNCClient actions necessary to establish
 *      a websocket connection. This is used for initial conections.
 *
 *      int reconnectAttemptCount
 *      0: initial connection
 *      > 0: Network continuty with origianl <token>
 *      -1: Network continuity with freshed "j" token in <token>
 *
 * Results:
 *      True if succeeds, false otherwise.
 *
 * Side effects:
 *      Starts a VNC session.
 *
 *----------------------------------------------------------------------
 */

Bool
ViewClient_ConnectBlast(const char *targetURL,     // IN
                        const char *thumbprint,    // IN
                        SSLThumbprintType tpType,  // IN
                        const char *httpProxy,     // IN
                        int reconnectAttemptCount) // IN
{
   char *targetURLFinal = NULL;
   char *targetURLTemp = NULL;
   Bool vncSuccess = FALSE;
   int i = 0;
   char *token;
   const char slash[] = "/";
   uint16 udpPort = 0;

   memset(&viewClient.sslVerifyParam, 0, sizeof(SSLVerifyParam));

   if (!ViewClientApplySSLSettings()) {
      goto fail;
   }

#ifdef VMX86_DEVEL
   if (viewConfig.expectedThumbprintOverride) {
      thumbprint = viewConfig.expectedThumbprintOverride;
      Warning(LGPFX "Expected thumbprint was read from config file: %s\n", thumbprint);
   }
#endif

   /*
    * Get the route specifier from the URL.
    * The URL format is:
    * wss://<ip>:<port>/[dr]/<route-specifier>/?vauth=<vauth-token> or
    * wss://<ip>:<port>/j/<jwt>/?vauth=<vauth-token>
    */

   targetURLTemp = Util_SafeStrdup(targetURL);
   for (i = 0, token = strtok(targetURLTemp, slash); i < 3; i++) {
      token = strtok(NULL, slash);
   }

   if (!reconnectAttemptCount) {
      targetURLFinal = Util_SafeStrdup(targetURL);
      Log(LGPFX "Extracting and saving the route specifier.\n");
      /*
       * Needed for reconnection attempts.
       */
      VERIFY(token);
      VERIFY(viewClient.routeSpecifier == NULL);
      viewClient.routeSpecifier = Util_SafeStrdup(token);
   } else {
      char *p;

      Log(LGPFX "Attempting to reconnect with retry count = %d\n", reconnectAttemptCount);
      if (token == NULL) {
         Warning(LGPFX "Route specifier token is NULL.\n");
         goto fail;
      }
      if (viewClient.connCookies == NULL) {
         Warning(LGPFX "Reconnect cookie is NULL. Reconnect not possible.\n");
         goto fail;
      }
      if (reconnectAttemptCount > 0) {
         /* Network Continuity with original ext_token ("r" token) */
         if (strlen(token) != strlen(viewClient.routeSpecifier)) {
            Warning(LGPFX "Route specifier is not of expected length.\n");
            goto fail;
         }
         /* Reconstruct the URL with route specifier and cookie. */
         p = strstr(targetURL, token);
         VERIFY(p);
         strncpy(p, viewClient.routeSpecifier, strlen(viewClient.routeSpecifier));
      }

      /* Prepare the final target URL with cookie. */
      targetURLFinal = Str_Asprintf(NULL, "%s&%s", targetURL, viewClient.connCookies);
   }

   if (!ViewClientSetupCertificateCheck(thumbprint, tpType)) {
      Warning(LGPFX "Setup certificate checks failed.\n");
      goto fail;
   }

   if (!VDPTarget_GetHostAndPort(viewClient.vdpTarget, NULL, &udpPort)) {
      goto fail;
   }
   vncSuccess = VNCClient_ConnectBlast(targetURLFinal, &viewClient.sslVerifyParam, httpProxy,
                                       ViewClientConnectedCb, viewClient.connCookies,
                                       (reconnectAttemptCount != 0), udpPort);

fail:
   free(targetURLFinal);
   free(targetURLTemp);

   return vncSuccess;
}


/*
 *----------------------------------------------------------------------
 *
 * ViewClient_RequestBlastToDisconnect --
 *
 *      Request Blast server to disconnect.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      Server may close connection.
 *
 *----------------------------------------------------------------------
 */

void
ViewClient_RequestBlastToDisconnect(VDPConnectionResult vdpResult) // IN
{
   VNCClient_RequestBlastToDisconnect(vdpResult);
}


/*
 *----------------------------------------------------------------------------
 *
 * ViewClientCreateConfig --
 *
 *      Retrieve local config.
 *
 * Results:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

static void
ViewClientCreateConfig(void)
{
   uint32 mouseMoveMaxLatencyMsec;

   viewConfig.onlyProbeMode = viewClient.onlyProbeMode;

   viewConfig.nodelay = Config_GetBool(TRUE, "RemoteDisplay.nodelay");

   viewConfig.defaultTypematicDelayUS =
      Config_GetLong(500000, "mks.RemoteDisplay.defaultTypematicDelayUS");
   viewConfig.defaultTypematicPeriodUS =
      Config_GetLong(33333, "mks.RemoteDisplay.defaultTypematicPeriodUS");
   viewConfig.enableHardwareVideo = Config_GetBool(TRUE, "mks.enableHardwareVideo");
   viewConfig.disableTypematic = Config_GetBool(FALSE, "mks.disableTypematic");
   viewConfig.allowClientPenEventRedirection = Config_GetBool(TRUE, "mks.enablePen");
   viewConfig.penEventRedirectionOptimizedMode = Config_GetBool(FALSE, "mks.penOptimizeMode");
   viewConfig.suppressNumlocks = Config_GetBool(FALSE, "mks.keyboard.suppressNumlocks");
   viewConfig.enableHotkeyNumlockBinding =
      Config_GetBool(FALSE, "mks.keyboard.enableHotkeyNumlockBinding");
   viewConfig.loadClipboardPlugin = Config_GetBool(FALSE, "RemoteDisplay.loadClipboardPlugin");
   viewConfig.enableUDP = Config_GetBool(FALSE, "RemoteDisplay.enableUDP");
   viewConfig.enableBlastNetworkRecovery =
      Config_GetBool(TRUE, "RemoteDisplay.enableBlastNetworkRecovery");
   viewConfig.enableBlastNetworkContinuity =
      Config_GetBool(TRUE, "RemoteDisplay.enableBlastNetworkContinuity");
   viewConfig.enableBlastNetworkIntelligence =
      Config_GetBool(TRUE, "RemoteDisplay.enableBlastNetworkIntelligence");
   viewConfig.enableBlastNetworkWaitForBEAT =
      Config_GetBool(FALSE, "RemoteDisplay.enableBlastNetworkWaitForBEAT");
   viewConfig.enableBlastNetworkVVCPauseResume =
      Config_GetBool(TRUE, "RemoteDisplay.enableBlastNetworkVVCPauseResume");
   viewConfig.enableBlastNetworkThread =
      Config_GetBool(TRUE, "RemoteDisplay.enableBlastNetworkThread");
   viewConfig.enableBlastNetworkVVCQoSPolicy =
      Config_GetBool(TRUE, "RemoteDisplay.enableBlastNetworkVVCQoSPolicy");
   viewConfig.qosPolicyDscpCOutTCPv4 = Config_GetLong(-1, "RemoteDisplay.qosPolicyDscpCOutTCPv4");
   viewConfig.qosPolicyDscpCOutUDPv4 = Config_GetLong(-1, "RemoteDisplay.qosPolicyDscpCOutUDPv4");
   viewConfig.qosPolicyDscpCOutTCPv6 = Config_GetLong(-1, "RemoteDisplay.qosPolicyDscpCOutTCPv6");
   viewConfig.qosPolicyDscpCOutUDPv6 = Config_GetLong(-1, "RemoteDisplay.qosPolicyDscpCOutUDPv6");
   viewConfig.enableBlastNetworkVVCDeferredAcks =
      Config_GetBool(TRUE, "RemoteDisplay.enableBlastNetworkVVCDeferredAcks");
   viewConfig.vvcMptAckQuietPeriod = Config_GetLong(5000, "RemoteDisplay.vvcMptAckQuietPeriod");
   viewConfig.vvcMptAckUnackedBytes = Config_GetLong(32768, "RemoteDisplay.vvcMptAckUnackedBytes");
   viewConfig.vvcMptAckSeqGap = Config_GetLong(4, "RemoteDisplay.vvcMptAckSeqGap");
   viewConfig.enableBlastNetworkVVCBatching =
      Config_GetBool(TRUE, "RemoteDisplay.enableBlastNetworkVVCBatching");
   viewConfig.enableBlastNetworkTcpBwe =
      Config_GetBool(TRUE, "RemoteDisplay.enableBlastNetworkTcpBwe");
   viewConfig.enableBeatRouteReplacement =
      Config_GetBool(TRUE, "RemoteDisplay.enableBeatRouteReplacement");
   viewConfig.allowBlastNetworkLegacyWSProtocols =
      Config_GetBool(TRUE, "RemoteDisplay.allowBlastNetworkLegacyWSProtocols");

   viewConfig.udpFecMaxLowLevelPacketSize =
      Config_GetLong(0, "RemoteDisplay.udpFecMaxLowLevelPacketSize");
   viewConfig.allowHZNHeartbeatReconnect =
      Config_GetTriState(-1, "RemoteDisplay.allowHZNHeartbeatReconnect");
   viewConfig.showCursorSpot = Config_GetBool(TRUE, "RemoteDisplay.showCursorSpot");
   viewConfig.showHiddenCursorSpot = Config_GetBool(FALSE, "RemoteDisplay.showHiddenCursorSpot");
   viewConfig.disableAudioRecovery = Config_GetBool(FALSE, "RemoteDisplay.disableAudioRecovery");

   /*
    * The following bora configs default to FALSE and are usually set
    * by crtbora on the remotemks command line, forwarding user settings
    * from the client UI.
    *
    * For --probe-only mode we set these to TRUE in order to probe
    * these configurations.
    */
   viewConfig.allowClientH264 =
      Config_GetBool(viewClient.onlyProbeMode, "RemoteDisplay.allowClientH264");
   viewConfig.allowClientH264YUV444 =
      Config_GetBool(viewClient.onlyProbeMode, "RemoteDisplay.allowClientH264YUV444");
   viewConfig.allowClientHEVC =
      Config_GetBool(viewClient.onlyProbeMode, "RemoteDisplay.allowClientHEVC");
   viewConfig.allowClientHDR =
      Config_GetBool(viewClient.onlyProbeMode, "RemoteDisplay.allowClientHDR");

   /*
    * AV1 only enabled on Windows and Linux clients at this stage.
    */
   if (!vmx86_apple) {
      viewConfig.allowClientAV1 =
         Config_GetBool(viewClient.onlyProbeMode, "RemoteDisplay.allowClientAV1");
   } else {
      viewConfig.allowClientAV1 = FALSE;
   }

   viewConfig.enableExplicitColorInfo =
      Config_GetBool(TRUE, "RemoteDisplay.enableExplicitColorInfo");
   viewConfig.allowClientHEVCYUV444 = Config_GetBool(TRUE, "RemoteDisplay.allowClientHEVCYUV444");
   viewConfig.allowClientHEVCYUV444_10BIT =
      Config_GetBool(TRUE, "RemoteDisplay.allowHEVCYUV444_10BIT");
   /*
    * AV1 4:4:4 not currently supported anywhere, so fuse off.
    */
   viewConfig.allowClientAV1YUV444 = FALSE;

   /*
    * Max latency being 0 implies the mouse coalescing is disabled,
    * otherwise clamp the value between 1 and 50.
    */
   mouseMoveMaxLatencyMsec = Config_GetLong(0, "RemoteDisplay.mouseMoveMaxLatencyMsec");
   viewConfig.mouseMoveMaxLatencyMsec =
      mouseMoveMaxLatencyMsec != 0 ? HZN_CLAMP(mouseMoveMaxLatencyMsec, 1, 50) : 0;

   viewConfig.allowClientCursorWarping = Config_GetBool(TRUE, "RemoteDisplay.allowCursorWarping");
   viewConfig.forceClientHDR = Config_GetBool(FALSE, "RemoteDisplay.forceClientHDR");
   viewConfig.enableDecoderWatermark =
      Config_GetBool(FALSE, "RemoteDisplay.enableDecoderWatermark");
   viewConfig.permitUnverifiedWebSocketSSL =
      Config_GetBool(FALSE, "RemoteDisplay.permitUnverifiedWebSocketSSL");
   viewConfig.expectedThumbprintOverride =
      Config_GetString(NULL, "RemoteDisplay.expectedThumbprintOverride");
   viewConfig.allowCursorEventsOnLowLatencyChannel =
      Config_GetBool(TRUE, "RemoteDisplay.allowCursorEventsOnLowLatencyChannel");
   viewConfig.vaSyncSurfaceRetryCount =
      Config_GetLong(5000, "RemoteDisplay.vaSyncSurfaceRetryCount");

   viewConfig.forceOnAllowForcedRelativeMouse =
      Config_GetBool(FALSE, "RemoteDisplay.forceOnAllowForcedRelativeMouse");

   viewConfig.enableAsyncDecode = Config_GetBool(TRUE, "RemoteDisplay.enableAsyncDecode");

   viewConfig.asyncThreadsPerCore = Config_GetDouble(0.5, "RemoteDisplay.asyncThreadsPerCore");
   viewConfig.asyncThreadsMin = Config_GetLong(1, "RemoteDisplay.asyncThreadsMin");
   viewConfig.asyncThreadsMax = Config_GetLong(8, "RemoteDisplay.asyncThreadsMax");
   viewConfig.asyncThreadsOverride = Config_GetLong(0, "RemoteDisplay.asyncThreadsOverride");

   viewConfig.allowMultipleAudioOut = Config_GetBool(TRUE, "RemoteDisplay.allowMultipleAudioOut");
   viewClient.enableSplitMKSWindow = Config_GetBool(FALSE, "RemoteDisplay.splitMksWindow");
   viewConfig.allowDynamicAudioOut = Config_GetBool(TRUE, "RemoteDisplay.allowDynamicAudioOut");
   viewConfig.allowSoundlibAudioQueueStatus =
      Config_GetBool(TRUE, "RemoteDisplay.allowSoundlibAudioQueueStatus");

   viewConfig.enablePollRestartSocket =
      Config_GetBool(TRUE, "RemoteDisplay.enablePollRestartSocket");

   viewConfig.dumpPresentTimes = Config_GetBool(FALSE, "viewClient.dumpPresentTimes");
   viewConfig.dumpPresentIntervalMS = Config_GetLong(0, "viewClient.dumpPresentIntervalMS");

   viewConfig.dumpWindowYUV = Config_GetBool(FALSE, "viewClient.dumpWindowYUV");

   viewConfig.decodeCapsProbingMode =
      Config_GetString(VDP_DEFAULT_DECODECAPS_PROBING_MODE, "viewClient.decodeCapsProbingMode");

   if (!ViewClient_IsUIConnected() && Str_Strcmp(viewConfig.decodeCapsProbingMode, "legacy") != 0 &&
       Str_Strcmp(viewConfig.decodeCapsProbingMode, "off") != 0) {
      /*
       * Standalone remotemks can only choose "legacy" or "off" since it
       * will not receive probe results from Horizon Client UI.
       */
      free(viewConfig.decodeCapsProbingMode);
      viewConfig.decodeCapsProbingMode = Util_SafeStrdup("legacy");
   }

   if (!viewClient.onlyProbeMode) {
      Log(LGPFX "decodeCaps probing mode: %s%s", viewConfig.decodeCapsProbingMode,
          ViewClient_IsUIConnected() ? "" : " (standalone client)");
   }

   viewConfig.rLogLevel = Config_GetLong(0, "viewClient.rLogLevel");

   viewConfig.enableSupportMode = Config_GetBool(FALSE, "viewClient.enableSupportMode");
   viewConfig.supportModeDelaySeconds =
      HZN_CLAMP(Config_GetLong(3, "viewClient.supportModeDelaySeconds"), 0, 30);
}


/*
 *----------------------------------------------------------------------------
 *
 * ViewClientDestroyConfig --
 *
 *      Frees any memory allocated by ViewClientCreateConfig.
 *
 * Results:
 *      None.
 *
 *----------------------------------------------------------------------------
 */
static void
ViewClientDestroyConfig(void)
{
   free(viewConfig.expectedThumbprintOverride);
   viewConfig.expectedThumbprintOverride = 0;
   free(viewConfig.decodeCapsProbingMode);
   viewConfig.decodeCapsProbingMode = NULL;
}


/*
 *----------------------------------------------------------------------------
 *
 * ViewClient_ViewControlConnected --
 *
 *      Called when the ViewControl is connected or disconnected.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

void
ViewClient_ViewControlConnected(Bool connected) // IN
{
   ASSERT(ViewClientLock_IsLocked());

   MXUser_SignalEvent(viewClient.viewControlInitialized);

   Log("ViewControl is %sonnected.\n", connected ? "C" : "Disc");

   if (!connected) {
      viewClient.exitRequest = TRUE;
   }
}


/*
 *----------------------------------------------------------------------------
 *
 * ViewClient_SigHandler --
 *
 *      Signal catcher for linux and exits the process.
 *
 * Results:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

#ifndef _WIN32
void
ViewClient_SigHandler(int s,                           // IN:
                      siginfo_t *info,                 // IN:
                      ucontext_t *u, void *clientData) // IN:
{
   Warning("Signal %d received, exiting.\n", s);

   ViewClient_RequestExit();
}
#endif


/*
 *----------------------------------------------------------------------
 *
 * ViewClient_GetDumpSettings --
 *
 *      Prepare dump options by reading the GPO settings.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------
 */

#ifdef WIN32
void
ViewClient_GetDumpSettings(DWORD *dumpCount,             // OUT
                           MINIDUMP_TYPE *coreDumpFlags) // OUT
{
   HKEY hRegKey = NULL;
   char dumpTypeBuf[256] = {0};
   DWORD dumpTypeSize = sizeof(dumpTypeBuf);
   const char *regKeyPath = HORIZON_VDM_REG_GPO_ROOT_A "\\Dump";
   Bool isMiniDumpSet = FALSE;
   *coreDumpFlags =
      (MINIDUMP_TYPE)(MiniDumpWithFullMemory | MiniDumpWithFullMemoryInfo | MiniDumpWithHandleData |
                      MiniDumpWithUnloadedModules | MiniDumpWithThreadInfo);

   if (Win32U_RegOpenKeyEx(HKEY_LOCAL_MACHINE, regKeyPath, 0, KEY_READ, &hRegKey) !=
       ERROR_SUCCESS) {
      Log("%s: failed to open reg key of dump settings.\n", __FUNCTION__);
      return;
   }

   Log("%s: succeeded to open reg key of dump settings.\n", __FUNCTION__);

   if (Win32U_RegQueryDWORDValue(hRegKey, "GlobalMaxDumpCount", dumpCount) != ERROR_SUCCESS) {
      Log("%s: failed to read max dump count setting.\n", __FUNCTION__);
   }

   if (Win32U_RegQueryStringValue(hRegKey, "GlobalDumpType", dumpTypeBuf, &dumpTypeSize) !=
       ERROR_SUCCESS) {
      Log("%s: failed to read dump type setting.\n", __FUNCTION__);
   }
   isMiniDumpSet = Str_Strncasecmp(dumpTypeBuf, "mini", strlen("mini")) == 0;
   if (isMiniDumpSet) {
      *coreDumpFlags = (MINIDUMP_TYPE)(MiniDumpWithDataSegs | MiniDumpWithHandleData);
   }

   Log("%s: Dump count is set to %lu and dump type is set to %d.\n", __FUNCTION__, *dumpCount,
       *coreDumpFlags);

   RegCloseKey(hRegKey);
}
#endif


/*
 *----------------------------------------------------------------------
 *
 * ViewClient_VerifyCurrentDirectory --
 *
 *    DLL loading can be sensitive to the currrent directory; in
 *    particular, the openssl3 effort has had problems with DLLs
 *    not loading because the current directory is not what we
 *    expect it to be (it should be the same folder as where the
 *    remotemks is located).
 *
 *    The first call to the function will log the current directory
 *    while subsequent calls will only log a messge if the current
 *    directory has changed.
 *
 * Results:
 *    None.
 *
 * Side effects:
 *    None.
 *
 *----------------------------------------------------------------------
 */

void
ViewClient_VerifyCurrentDirectory()
{
#ifdef WIN32
   static WCHAR sCurrentDir[MAX_PATH] = L"";
   WCHAR curDir[ARRAYSIZE(sCurrentDir)] = L"";

   /*
    * On the first call, log the current directory
    */
   if (sCurrentDir[0] == L'\0') {
      GetCurrentDirectoryW(ARRAYSIZE(sCurrentDir), sCurrentDir);
      Log("Current directory is %ls\n", sCurrentDir);
      return;
   }

   /*
    * On subsequent calls, check if the current directory has changed
    */
   GetCurrentDirectoryW(ARRAYSIZE(curDir), curDir);
   if (wcsicmp(sCurrentDir, curDir) != 0) {
      wcscpy_s(sCurrentDir, ARRAYSIZE(sCurrentDir), curDir);
      Log("Current directory changed to %ls\n", sCurrentDir);
   }
#endif
}


/*
 *----------------------------------------------------------------------
 *
 * main --
 *
 *      Main routine for the viewClient.
 *
 * Results:
 *      Process exit status.
 *
 * Side effects:
 *      See above.
 *
 *----------------------------------------------------------------------
 */
#ifndef VIEWCLIENTTEST
#   ifdef WIN32
int APIENTRY
wWinMain(HINSTANCE hInstance,     // IN
         HINSTANCE hPrevInstance, // IN
         LPWSTR lpCmdLine,        // IN
         int nCmdShow)            // IN
#   else
int
main(int argc,     // IN
     char *argv[]) // IN
#   endif
{
   void *osThreadPool = NULL;
   PollOptions pollOpts = {0};

#   ifdef WIN32
   char **uargv;
   char **argv;
   int argc;
   WSADATA wsaData;
   WORD versionRequested = MAKEWORD(2, 0);
   BOOL res;
   DWORD dumpCount;
   MINIDUMP_TYPE coreDumpFlags = 0;

   res = W32Util_EnableSafePathSearching();
   VERIFY(res);

   Unicode_InitW(__argc, __wargv, NULL, &uargv, NULL);

   /*
    * Initialize Preferences.
    */
   Preference_Init();

   CoreDump_SetUnhandledExceptionFilter();
   CoreDump_InstallExtraExceptionHandlers();

   /* allow thin client vendors to disable dmp files if disk space is limited */
   dumpCount = Config_GetLong(DEFAULT_MAX_DUMP_COUNT, "viewClient.globalMaxDumpCount");

   /* this setting can be overridden via Group Policy */
   ViewClient_GetDumpSettings(&dumpCount, &coreDumpFlags);
   CoreDump_SetMaxDumpCount(dumpCount);

   if (WSAStartup(versionRequested, &wsaData) != 0) {
      return 1;
   }

   argc = __argc;
   argv = uargv;
#   else

#      ifdef __linux__
   if (!Sig_EarlyInit()) {
      return 1;
   }

   if (XInitThreads() == 0) {
      fprintf(stderr, "XInitThreads() failed\n");
      return 1;
   }

   SetXErrorHandler();
#      endif

   Unicode_Init(argc, &argv, NULL);

   /*
    * Initialize Preferences.
    */
   Preference_Init();

#      ifdef __APPLE__
   Location_Init("horizon-protocol");
#      endif
#   endif

   ViewClient_Init();

   VThread_Init("main");

   /*
    * Do this here so messages are localized as early as possible.
    * We use the default locale now, but may change it later.
    * -- edward
    */
   Log_SetStderrLevel(0, HZN_LOG_WARNING);
   viewClient.locale = Locale_GetUserLanguage();
   Msg_SetLocale(viewClient.locale, PRODUCT_EXECUTABLE "-mks");

   /*
    * Initialize Poll.
    */
   pollOpts.fireWrapperFn = &ViewClientPollFire;
   pollOpts.locked = TRUE;
   VNCPoll_Init(&pollOpts);

   /*
    * Parse options. Requires Preference module, to support '-s'
    * (Preference_SetFromString()).
    */

   ViewClientParseOptions(argc, argv);
   ViewClient_VerifyCurrentDirectory();

   Panic_Init();

   /*
    * [VCART-1122] Prevent popping up a "unrecoverable error"
    * modal dialog if --probe-only mode crashes.
    *
    * Probing may crash in third-party libraries while probing decoding
    * capabilities.
    *
    * Users generally do not need to be aware of such crashes, as the
    * probing results help the Horizon client avoid the same crashes.
    */
   if (viewClient.onlyProbeMode) {
      Panic_SetPanicMsgPost(FALSE);
   }


#   ifdef _WIN32
   /*
    * Set the flags based on GPO setting. This needs to be
    * called after Panic_Init to get GPO setting has the
    * highest priority.
    */
   Panic_SetCoreDumpFlags(coreDumpFlags);

   /*
    * [VCART-1122] Disable dmp files for --probe-only mode by default.
    *
    * Since users generally do not need to be aware of crashes in third-party
    * libraries while probing, also disable dmp files for such crashes.
    *
    * This also avoids filling limited disk space on thin clients like HP T730.
    */
   if (viewClient.onlyProbeMode && dumpCount == DEFAULT_MAX_DUMP_COUNT) {
      CoreDump_SetMaxDumpCount(0);
   }

   /*
    * Pop up console window in obj builds only.
    *
    * Also do not pop up the console when Horizon Client UI launches
    * "horizon-protocol --probe-only", since the console window will
    * intercept stdout and block the Horizon Client from receiving it.
    */
#      ifdef VMX86_LOG // obj builds only
   if (!viewClient.onlyProbeMode) {
      if (Config_GetBool(TRUE, "viewClient.enableWin32Console")) {
         W32Util_InitStdioConsole();
         SetConsoleTextAttribute(GetStdHandle(STD_OUTPUT_HANDLE),
                                 FOREGROUND_RED | FOREGROUND_GREEN | FOREGROUND_INTENSITY);
      }
   }
#      endif
#   endif

   if (!ViewClientInitLog(viewClient.logFileName, viewClient.logId)) {
      Panic(LGPFX "Unable to initialize logging.\n");
   }

   ViewClientLoadConfigOverlay();
   Preference_Log();

#   ifdef __APPLE__
#      ifdef __aarch64__
   Log(LGPFX "Running ARM64 binary");
#      endif
#      ifdef __x86_64__
   Log(LGPFX "Running x86_64 binary");
#      endif
#   endif

#   ifndef _WIN32
   if (Preference_GetBool(vmx86_devel || vmx86_debug, "mks.coredump.unlimited")) {
      struct rlimit rlim = {RLIM_INFINITY, RLIM_INFINITY};
      int error;

      error = setrlimit(RLIMIT_CORE, &rlim);
      if (error != 0) {
         Log(LGPFX "Error setting core-dump limit: %d\n", error);
      }
   }
#   endif

#   ifdef _WIN32
   if (HznProtect_OpenDriver()) {
      Log(LGPFX "Horizon Client Protect driver is ready\n");
   } else {
      DWORD err = GetLastError();
      Log(LGPFX "Horizon Client Protect driver failed: %d\n", err);
   }
#   endif

   if (viewClient.benchmarkMode == BENCHMARK_NONE) {
      Bool enableFIPSMode;

      SSL_FIPSInstall();

      enableFIPSMode = Preference_GetBool(FALSE, "mks.enableFIPSMode");

      /*
       * SSL_EnableFIPSMode only set internal flag for SSL_Init
       * to call FIPS_mode_set(1) after openssl APIs are loaded
       * and before ssl library is initialized.
       */

      if (enableFIPSMode) {
         SSL_EnableFIPSMode();
      }
#   ifdef __APPLE__
      SSL_Init(Preference_GetPathName, Location_Get("sslDir"), CONFIG_HORIZONSSLDIR);
#   else
      SSL_Init(Preference_GetPathName, DEFAULT_SSLLIBDIR, CONFIG_HORIZONSSLDIR);
#   endif

      if (enableFIPSMode) {
         if (SSL_IsFIPSMode()) {
            Log(LGPFX "View is running in FIPS mode.\n");
         } else {
            Panic(LGPFX "Failed to set View in FIPS mode.\n");
         }
      }
   }

#   ifdef __linux__
   viewClient.generateCoreDumpInCurrentProcess =
      Preference_GetBool(TRUE, "mks.generateCoreDumpInCurrentProcess");
#   endif
   /*
    * Set locale a second time to pick up command-line override,
    * the correct message dictionary via "libdir" (on Linux),
    * and to log errors.
    * -- edward
    */

   {
#   ifdef _WIN32
      char *modulePath = Win32U_GetModuleFileName(NULL);
      char *path = NULL;

      /*
       *  Reacquire locale after processing the '#' "ProductState" parameter.
       */
      viewClient.locale = Locale_GetUserLanguage();

      File_GetPathName(modulePath, &path, NULL);
      Msg_SetLocaleEx(viewClient.locale, PRODUCT_EXECUTABLE, path);
      free(path);
      free(modulePath);
#   else
      Msg_SetLocale(viewClient.locale, PRODUCT_EXECUTABLE);
#   endif
   }

   free(viewClient.locale);
   viewClient.locale = NULL;

   Sig_Init();

#   ifndef _WIN32
   if (!ViewClient_IsUIConnected()) {
      Sig_Callback(SIGINT, SIG_SAFE, ViewClient_SigHandler, NULL);
      Sig_Callback(SIGHUP, SIG_SAFE, ViewClient_SigHandler, NULL);
   }
#   endif

   /*
    * Check if we are running in AppMode or DesktopMode.
    */
   viewClient.isAppSession = FALSE;
   if (ViewClient_IsUIConnected()) {
      char *args = Preference_GetString(NULL, "mks.vdp.plugin.args");
      char *tmp = strtok(args, ",");

      while ((tmp = strtok(NULL, ","))) {
         if (!strcasecmp(tmp, "isAppSession")) {
            tmp = strtok(NULL, ",");

            if (!strcasecmp(tmp, "TRUE")) {
               viewClient.isAppSession = TRUE;
               Log(LGPFX "Session is running in AppMode\n");
            } else {
               Log(LGPFX "Session is running in DesktopMode\n");
            }

            break;
         }
      }

      free(args);
   }

   ViewClientCreateConfig();

   ViewClientLock_Init();

   viewClient.exitRequest = FALSE;

   ViewClientLock_Lock();

   KeyboardMapping_Init();

   ViewScreenManager_Init();

   if (Config_GetBool(TRUE, "viewClient.enableOSThreadPool")) {
      VNCThreadPoolConfig osThreadPoolConfig;

      memset(&osThreadPoolConfig, 0, sizeof(osThreadPoolConfig));
      osThreadPoolConfig.name = "viewClientOS";
      osThreadPoolConfig.asyncThreadsMin = 1;
      osThreadPoolConfig.asyncThreadsMax = 16;
      osThreadPoolConfig.asyncThreadsPerCore = Config_GetDouble(0.5, "viewClient.OSThreadsPerCore");
      osThreadPoolConfig.asyncThreadsOverride = Config_GetLong(0, "viewClient.OSThreadsOverride");
      osThreadPool = VNCThreadPool_Init(&osThreadPoolConfig);
   }

   ViewClientOS_Init(osThreadPool);

   ViewClientParseScreenWindowOverrides();

   viewClient.osInitialized = TRUE;

   ViewClient_CursorInit();

   ViewClient_MouseInit();

   ViewClient_KeyboardInit();

   ViewClient_ReplayInit();

   ViewClient_ImguiInit();

   ViewScreenManager_PowerOn();

   ViewClientLog_PowerOn();

   ViewControlMgr_Init();

   if (ViewClient_IsUIConnected()) {
      viewClient.viewControlInitialized = MXUser_CreateEvent(NULL, 0);

      if (!ViewControlMgr_PublishConnection(viewClient.cmdLineArgs)) {
         Panic("Failed to publish connection to ViewControl for given args: %s\n",
               viewClient.cmdLineArgs);
         goto failure;
      }

      ViewControlMgr_PowerOn();

      ViewClientLock_Unlock();

      while (!viewClient.exitRequest && !MXUser_TryWaitEvent(viewClient.viewControlInitialized)) {
         Warning("Waiting for ViewControl to connect\n");

         /*
          * Enter poll, waiting for connection.
          */
         Poll_Loop(FALSE, (Bool *)&viewClient.exitRequest, POLL_CLASS_MAIN);
      }

      ViewClientLock_Lock();

      if (viewClient.exitRequest) {
         Warning("ViewControl failed to connect, aborting.\n");
         goto failure;
      }
   }

   VDPPluginScreen_PowerOn();

   ViewClientProtocolPowerOn();

   ViewClientLock_Unlock();

   if (viewClient.onlyProbeMode) {
      viewClient.exitRequest = TRUE;
   }

   VNCPoll_Callback(ViewClientBenchmark);

   while (!viewClient.exitRequest) {
      ViewClientOS_Loop(VIEW_ID_INVALID);
      /*
       * Enter poll, waiting for termination.
       */
      Poll_Loop(FALSE, (Bool *)&viewClient.exitRequest, POLL_CLASS_MAIN);
   }

   /* Ungrab now. */
   ViewScreenManager_Ungrab(TRUE);

   /*
    * We don't hold the viewClient lock here due to poweroff requirements that
    * callbacks are fired which may require the viewClient lock.
    */
   ViewClientProtocolPowerOff();

   ViewClientLock_Lock();

   VDPPluginScreen_PowerOff();

   if (ViewClient_IsUIConnected()) {

   failure:
      ViewControlMgr_PowerOff();

      MXUser_DestroyEvent(viewClient.viewControlInitialized);
   }

   ViewControlMgr_Exit();

   ViewClientLog_PowerOff();

   ViewScreenManager_PowerOff();

   ViewClientLock_Unlock();

   ViewClient_CursorExit();

   ViewClient_MouseExit();

   ViewClient_KeyboardExit();

   ViewClient_ReplayExit();

   ViewClient_ImguiExit();

   ViewClientOS_Exit();

   VNCThreadPool_Exit(osThreadPool);

   viewClient.osInitialized = FALSE;

   ViewScreenManager_Exit();

   ViewClientLock_Exit();

   ViewClientDestroyConfig();

   Sig_Exit();

   SSL_Exit();

   VNCPoll_Exit();

   URL_Destroy();

#   ifdef _WIN32
   HznProtect_CloseDriver();
   ViewClient_VerifyCurrentDirectory();
#   endif

   Msg_Exit();

   Preference_Exit();

   Unicode_Shutdown(argc, argv, NULL);

   Log_Exit();

   Err_Exit();

   return viewClient.errorCode;
}
#endif


/*
 *----------------------------------------------------------------------
 *
 * ViewClient_IsEnabledSplitMKSWindows --
 *
 *      Get whether feature 'split mks windows' is enabled.
 *
 * Results:
 *      True if enabled, false otherwise.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------
 */

Bool
ViewClient_IsEnabledSplitMKSWindows(void)
{
   return viewClient.enableSplitMKSWindow;
}


/*
 *----------------------------------------------------------------------
 *
 * ViewClient_SetDisplayTopology --
 *
 *      Set display topology for protocol.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------
 */

void
ViewClient_SetDisplayTopology(void *clientData, uint32 cookie, const uint8 *buf, uint32 bufSize)
{
   ViewControlDisplayRPCRequest *topologies = (ViewControlDisplayRPCRequest *)buf;
   uint32 i;

   ASSERT(buf != NULL);

   ViewClientLock_Lock();
   if (topologies->numScreens == 0 || topologies->numScreens > VNC_MAX_SCREENS) {
      Log("%s: screen count is incorrect.\n", __FUNCTION__);
      return;
   }
   if (bufSize != sizeof(ViewControlDisplayRPCRequest)) {
      Log("%s: buffer size %d isn't same with ViewControlDisplayRPCRequest %zu\n", __FUNCTION__,
          bufSize, sizeof(ViewControlDisplayRPCRequest));
      return;
   }

   /*
    * Override the incoming topology with config.
    */
   if (ViewClient_IsMultiScreenEnabled() &&
       viewConfig.screenOverrides[viewConfig.currentScreenConfig].numScreens > 0) {
      for (i = 0; i < viewConfig.screenOverrides[viewConfig.currentScreenConfig].numScreens; i++) {
         VMRect *screenRect =
            &viewConfig.screenOverrides[viewConfig.currentScreenConfig].screenRect[i];
         topologies->screens[i].xRoot = Rect_Left(screenRect);
         topologies->screens[i].yRoot = Rect_Top(screenRect);
         topologies->screens[i].width = Rect_Width(screenRect);
         topologies->screens[i].height = Rect_Height(screenRect);
         topologies->screens[i].dpi =
            viewConfig.screenOverrides[viewConfig.currentScreenConfig].screenDpi[i];
         /* no need to set refreshRate or HDR here */
         Warning("Client Overriding screen %d - topology:(%d, %d, %d, %d)\n", i,
                 Rect_LTRB(screenRect));
      }
      topologies->numScreens =
         viewConfig.screenOverrides[viewConfig.currentScreenConfig].numScreens;
   }

   switch (ViewClient_GetProtocol()) {
   case MKS_DISPLAYPROTOCOL_VDP: {
      MKSDisplayTopologyPacket *extents =
         Util_SafeCalloc(topologies->numScreens, sizeof(MKSDisplayTopologyPacket));
      for (i = 0; i < topologies->numScreens; i++) {
         extents[i].left = topologies->screens[i].xRoot;
         extents[i].top = topologies->screens[i].yRoot;
         extents[i].right = (topologies->screens[i].xRoot + topologies->screens[i].width);
         extents[i].bottom = (topologies->screens[i].yRoot + topologies->screens[i].height);
         extents[i].dpi = topologies->screens[i].dpi;
      }

      VDPPluginHost_SetVDPDisplayTopology(extents, topologies->numScreens);
      free(extents);
      break;
   }
   case MKS_DISPLAYPROTOCOL_VNC: {
      VNCScreenRect *screenRects = Util_SafeCalloc(topologies->numScreens, sizeof *screenRects);
      for (i = 0; i < topologies->numScreens; i++) {
         ASSERT(topologies->screens[i].width > 0);
         ASSERT(topologies->screens[i].height > 0);

         screenRects[i].x = topologies->screens[i].xRoot;
         screenRects[i].y = topologies->screens[i].yRoot;
         screenRects[i].width = topologies->screens[i].width;
         screenRects[i].height = topologies->screens[i].height;
         screenRects[i].dpi = topologies->screens[i].dpi;
         Log(LGPFX "UI Requesting screen %d (%d %d %d %d)", i, screenRects[i].x, screenRects[i].y,
             screenRects[i].width, screenRects[i].height);
      }
      ViewScreenManager_SendResolutionRequest(topologies->numScreens, screenRects, FALSE);
      ViewScreenManager_UpdateScreenTopology(TRUE);
      free(screenRects);
      break;
   }
   }

   ViewClientLock_Unlock();
}


/*
 *----------------------------------------------------------------------
 *
 * ViewClient_SendAudioOutDevicesRequest --
 *
 *      Called when audio playback devices get added or removed.
 *
 * Results:
 *      None.
 *
 *----------------------------------------------------------------------
 */

void
ViewClient_SendAudioOutDevicesRequest(uint16 numDevs,                     // IN
                                      VNCAudioOutputDeviceUniqueId *devs) // IN
{
   VNCClient_SendAudioOutDevicesRequest(numDevs, devs);
}


/*
 *----------------------------------------------------------------------
 *
 * ViewClient_ReinitializeAudioOutDevices --
 *
 *      Re-initialize audio playback device backends.
 *      Called when audio playback devices get added or removed.
 *
 * Results:
 *      None.
 *
 *----------------------------------------------------------------------
 */

void
ViewClient_ReinitializeAudioOutDevices(uint16 numDevs,                     // IN
                                       VNCAudioOutputDeviceUniqueId *devs) // IN
{
   ViewClientLock_Lock();
   VNCClient_ReinitializeAudioOutDevices(numDevs, devs);
   ViewClientLock_Unlock();
}


/*
 *----------------------------------------------------------------------
 *
 * ViewClient_IsDynamicAudioOutEnabled --
 *
 *      Determines whether dynamic multi-audio is enabled.
 *
 * Results:
 *      True if dynamic multi-audio is enabled, False otherwise.
 *
 *----------------------------------------------------------------------
 */

Bool
ViewClient_IsDynamicAudioOutEnabled()
{
   return VNCClient_IsDynamicAudioOutEnabled();
}


/*
 *-----------------------------------------------------------------------------
 *
 * ViewClient_IMESetEnabled --
 *
 *       Set desktop local IME enable/disable status from client UI settings.
 *
 * Results:
 *       None.
 *
 * Side effects:
 *       None.
 *
 *-----------------------------------------------------------------------------
 */

void
ViewClient_IMESetEnabled(Bool enabled) // IN
{
   viewClient.isIMEEnabled = enabled;
}


/*
 *----------------------------------------------------------------------
 *
 * ViewClient_IsIMEEnabled --
 *
 *      Return if local IME is enabled.
 *
 * Results:
 *      TRUE or FALSE.
 *
 *----------------------------------------------------------------------
 */

Bool
ViewClient_IsIMEEnabled(void)
{
   return viewClient.isIMEEnabled;
}


/*
 *----------------------------------------------------------------------
 *
 * ViewClient_IsAppSession --
 *
 *      Return if we are running in AppSession mode or Desktop mode.
 *
 * Results:
 *      TRUE or FALSE.
 *
 *----------------------------------------------------------------------
 */

Bool
ViewClient_IsAppSession(void)
{
   return viewClient.isAppSession;
}


/*
 *----------------------------------------------------------------------
 *
 * ViewClientBenchmark --
 *
 *    Various benchmark modes for the viewClient.
 *
 * Results:
 *    None.
 *
 *----------------------------------------------------------------------
 */

static void
ViewClientBenchmark(void *clientData)
{
   if (viewClient.onlyProbeMode || viewClient.benchmarkMode == BENCHMARK_NONE) {
      return;
   }

   if (!ViewClientOS_PrepareBenchmark()) {
      Panic("Failed to prepare the benchmark\n");
   }

   if (viewClient.benchmarkMode == BENCHMARK_RENDER_PLAYER) {
      Bool pass = ViewClient_FFmpegPlayer();
      Warning("Player Benchmark : %s\n", pass ? "PASSED" : "FAILED");
   } else {
      NOT_IMPLEMENTED();
   }

   viewClient.exitRequest = TRUE;
}


/*
 *----------------------------------------------------------------------
 *
 * Rect_ScaleRect --
 *
 *    Will round left & top down and right & bottom
 *    up. For irregular overlays, when two clip rects touch one
 *    another, the result scaled rects will have one line of pixels
 *    overlap (e.g., The bottom line of the top and the top line
 *    of the bottom).
 *    Here, we scale the rect manually using Rect_ScalePoint to
 *    ensure the rounding is consistent.
 *
 * Results:
 *    None.
 *----------------------------------------------------------------------
 */

void
Rect_ScaleRectConsistent(VMRect *src, VMRect *dst, VMRect *inRect, VMRect *outRect)
{
   VMPoint topLeft, bottomRight;

   topLeft.x = (inRect)->left;
   topLeft.y = (inRect)->top;
   bottomRight.x = (inRect)->right;
   bottomRight.y = (inRect)->bottom;
   Rect_ScalePoint((src), (dst), &topLeft, &topLeft, FALSE);
   Rect_ScalePoint((src), (dst), &bottomRight, &bottomRight, FALSE);
   Rect_Set((outRect), topLeft.x, topLeft.y, bottomRight.x, bottomRight.y);
}


/*
 *----------------------------------------------------------------------
 *
 * ViewClient_GetVideoDecoderCaps --
 *
 *    Returns a pointer to the client's decoder caps.
 *
 *----------------------------------------------------------------------
 */

ViewClientVideoDecoderCaps *
ViewClient_GetVideoDecoderCaps(void)
{
   return &viewClient.decodeCaps;
}
