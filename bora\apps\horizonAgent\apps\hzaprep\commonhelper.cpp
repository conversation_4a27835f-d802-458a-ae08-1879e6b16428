/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * CommonHelper.cpp
 *
 *      Class that implements some of the functionality common among different
 *      hzaprep modes.
 *
 */

#include "stdafx.h"
#include "commonhelper.h"

hzaprepMode CommonHelper::mode = none_mode;
bool g_bShutdown = false;
HANDLE g_hShutdown = NULL;

/*
 *----------------------------------------------------------------------------
 *
 * channelCallback --
 *
 *      Callback from message framework reporting about a change in the
 *      channel state.
 *
 * Returns:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */
void
channelCallback(MessageFrameWork::channelTypes type, bool incoming, bool local, LPCTSTR remoteName,
                bool opened, MessageChannel *pChannel)
{
   SYSMSG_FUNC(Debug, L"Channel is %s.", opened ? L"opened" : L"closed");
}


/*
 *----------------------------------------------------------------------------
 *
 * CommonHelper::CommonHelper --
 *
 *      Constructor.
 *
 * Returns:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */
CommonHelper::CommonHelper() : mMFWInitialized(false) {}


/*
 *----------------------------------------------------------------------------
 *
 * CommonHelper::~CommonHelper --
 *
 *      Destructor.
 *
 * Returns:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */
CommonHelper::~CommonHelper()
{
   shutdownMFW();
}


/*
 *----------------------------------------------------------------------------
 *
 * CommonHelper::getInstance --
 *
 *      Gets the single instance
 *
 * Returns:
 *      The reference to the single instance.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */
CommonHelper &
CommonHelper::getInstance()
{
   static CommonHelper singleton;
   return singleton;
}


/*
 *----------------------------------------------------------------------------
 *
 * CommonHelper::initMFW --
 *
 *      Initializes MFW
 *
 * Returns:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */
bool
CommonHelper::initMFW(bool isServer)
{
   SysMessageModule(0);

   SYSMSG_FUNC(Trace, L"Initializing the MFW");
   std::lock_guard guard(mMFWInitMutex);

   if (mMFWInitialized) {
      SYSMSG_FUNC(Debug, L"MessageFramework is already started.");
      return true;
   }

   MessageFrameWork *mfw = NULL;

   if (!isServer) {
      mfw = MessageFrameWork::Start();
   } else {
      DWORD flags = MessageFrameWork::Listener_SharedMemory;
      /*
       * TODO: Need to determine the appropriate values for number of threads,
       * dispatchQueueTreshold and sharedQueueTreshold.
       */
      mfw = MessageFrameWork::Start(channelCallback, 50, L"hzaprep", flags, 5000, 1000);
   }

   if (!mfw) {
      SYSMSG_FUNC(Error, L"Failed to start MessageFramework");
      return false;
   }

   if (mfw->Ready()) {
      mMFWInitialized = true;
      return true;
   }

   return false;
}


/*
 *----------------------------------------------------------------------------
 *
 * CommonHelper::shutdownMFW --
 *
 *      Shuts down MFW.
 *
 * Returns:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */
void
CommonHelper::shutdownMFW()
{
   SYSMSG_FUNC(Trace, L"Shutting down the MFW");
   bool doShutdown = false;
   {
      std::lock_guard guard(mMFWInitMutex);
      doShutdown = mMFWInitialized;
      mMFWInitialized = false;
   }

   if (doShutdown) {
      /* Shutdown the threading system */
      MessageFrameWork::Shutdown();
   }
   return;
}


/*
 *----------------------------------------------------------------------------
 *
 * CommonHelper::isMFWInitialized --
 *
 *      Returns the value of flag indicating the status of MessageFrameWork
 *
 * Returns:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */
bool
CommonHelper::isMFWInitialized()
{
   std::lock_guard guard(mMFWInitMutex);
   return mMFWInitialized;
}
