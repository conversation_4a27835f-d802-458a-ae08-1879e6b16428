# Copyright (c) Omnissa, LLC. All rights reserved.
# This product is protected by copyright and intellectual property laws in the
# United States and other countries as well as by international treaties.
# -- Omnissa Restricted

"""vmware Config

This module maintains global state about the two types of build
configuration parameters we support: global options and local options.

GLOBAL OPTIONS:
Global options are those declared using the builtin scons mechanism in
scons/init.py. Each parameter has a API to set and access the value (such as
SetBuildRoot() and BuildRoot()). The first half of this file contains such
functions.

LOCAL OPTIONS:
Usable on-the-fly and developer-centric.  Local options are specified the
same way as global options, but are strictly forbidden in official builds.
Useful for group or individual tweaks.  e.g. NEW_FEATURE=1

NOTE: you MUST check the existence of a local option with either
(key in vmware.LocalOpts) or vmware.LocalOpts.has_key(key)
before using its value.  This is by design; official, supported
builds can never depend on the value of a local option, and
thus should be caught at the existence check.

Optionally, the vmware.LocalOpts.get() method takes a default
value argument; usage of that default argument is safe regardless
of whether local options are enabled or not because it results in
a well-defined value.

All of the functions from this module are imported into the vmware module
namespace, so none of them are accessed directly, and in fact, none should
be accessed directly.

"""

import inspect
import logging
import os
import re
import subprocess
import sys
import vmware
from pathlib import Path
from vmware.llvm import (
    SetUseClang,
    SetUseClangSanitizer,
    SetUseClangStaticAnalyzer,
    SetUseClangXray,
    SetUseClangCoverage,
    SetUseClangTidyPlugin,
    SetClangPlugins,
)
import SCons
from SCons.Script import Dir, SConscript

log = vmware.GetLogger("main")
conanLog = vmware.GetLogger("conan")

# global to cache set of command line options specified by the user/Local.sc
env_options = None

PYPI_VIRTUAL_SOURCE = (
    "https://artifactory.air-watch.com/artifactory/" "api/pypi/uem-pypi-virtual/simple"
)

# The list of functions that the vmware module imports from this one.
# Python uses this variable to figure out which names to import.
__all__ = [
    "SetBuildRoot",
    "BuildRoot",
    "SetVerbose",
    "Verbose",
    "SetDumpNodesOnly",
    "DumpNodesOnly",
    "IsBuildNumberInfoOverridden",
    "SetBuildNumber",
    "BuildNumber",
    "SetChangeNumber",
    "ChangeNumber",
    "SetProductBuildNumber",
    "ProductBuildNumber",
    "SetNumProcessors",
    "NumProcessors",
    "NumLocalProcessors",
    "ReleasePackagesDir",
    "BuildLogDir",
    "SetBuildLogDir",
    "BuildErrorSummary",
    "SetBuildErrorSummary",
    "PackageHints",
    "SetPackageHints",
    "SetReleasePackagesDir",
    "SetGitBranchName",
    "GitBranchName",
    "SetGitHash",
    "GitHash",
    "GitShortHash",
    "IsDevelopmentBranch",
    "SetBuildingForAppleAppStore",
    "BuildingForAppleAppStore",
    "SetBuildingForAppleSandbox",
    "BuildingForAppleSandbox",
    "GobuildComponentRoot",
    "GobuildGetComponentRoots",
    "GobuildHasComponentRoot",
    "SetGobuildHosttype",
    "GobuildHosttype",
    "BuildInfoDir",
    "DependencyEnforcementEnabled",
    "RemoteCacheFetchEnabled",
    "RemoteCachePushEnabled",
    "RemoteCacheUrlDefault",
    "RemoteCachingSupported",
    "CompilationDatabaseEnabled",
    "SetCompilationDatabaseEnabled",
    "LockDir",
    "PackageRoot",
    "ComponentsRoot",
    "IsFromGobuildCacheDir",
    "GobuildComponentsFromCommandLine",
    "AndroidCxxRuntime",
    "WindowsSystem",
    "GetEnvOpts",
    "SetUseNewGlib",
    "UseNewGlib",
    "SetColoredLogging",
    "ColoredLogging",
    "SetDetailedBuildStats",
    "DetailedBuildStats",
    "GetGobuildCacheDirComparisonPrefix",
    "UseSanitizers",
    "UseMsvcStaticAnalyzer",
    "SetBuildingForAppleUniversal",
    "BuildingForAppleUniversal",
    "ConanRoot",
    "GetConanProfile",
    "GetConanComponent",
    "GetConanBuildComponent",
    "GetConanPackageRootFromPath",
    "GetConanPackageRoot",
    "GetConanUniversalPackageRoot",
    "GetVenvRoot",
]


###############################################
# Get or set the GOBUILD_HOSTTYPE option

_gobuildHosttype = None


def SetGobuildHosttype(val):
    global _gobuildHosttype
    _gobuildHosttype = val


def GobuildHosttype():
    return _gobuildHosttype


def GetEnvOpts():
    """
    Return the cached env_options.  Most code should really
    use one of the helper functions defined in this file.
    """
    return env_options


def _GetProductDependencyName():
    """
    In most cases the product name maps to the required dependencies,
    however for the hccrt product we have separate dependencies per
    build target which is chosen by which build host we run on.
    """
    name = vmware.GetRequestedProduct()
    if name == "hccrt":
        if vmware.BuildHostIsWindows():
            name = "hcwin"
        elif vmware.BuildHostIsLinux():
            if GobuildHosttype() == "linux-centos8-1-fw":
                name = "hcandroid"
            else:
                name = "hclin"
        elif GobuildHosttype() == "macosx-monterey-dvm-fw":
            name = "hcios"
        else:
            name = "hcmac"
    return name


def InstallPypiFromAirwatchArtifactory():
    """
    Use sys.executable to ensure that you will call the same pip associated
    with the current runtime.
    The officially recommended way to install packages from a script is by
    calling pip's command-line interface via a subprocess.
    """
    requirements = os.path.join(
        vmware.DirAbsPath("#"), "github-deps", "requirements.txt"
    )
    subprocess.check_call(
        [
            sys.executable,
            "-m",
            "pip",
            "install",
            "--upgrade",
            "-U",
            "-i",
            PYPI_VIRTUAL_SOURCE,
            "--extra-index-url",
            "https://pypi.org/simple",
            "-r",
            requirements,
        ]
    )


def StringToIntTuple(s):
    """
    Convert a string of period-separated integers to a tuple of integers.
    If conversion fails for any part, uses 0 as the default value.
    """

    def safe_int(x, default=0):
        try:
            return int(x)
        except (ValueError, TypeError):
            return default

    return tuple(map(lambda x: safe_int(x), s.split(".")))


def CheckPypiDependencies():
    """
    Check whether the PYPI libraries are installed or not.
    """
    try:
        from migration_tools import version as migration_version  # noqa: F401
        from artifactory_download import version as artifactory_version  # noqa: F401

        if StringToIntTuple(migration_version.__version__) < (1, 0, 2):
            raise ImportError("Updating migration_tools to version >= 1.0.2")

        if StringToIntTuple(artifactory_version.__version__) < (1, 1, 14):
            raise ImportError("Updating artifactory_download to version >= 1.1.14")
    except (ImportError, AttributeError):
        log.info(
            "One or more dependency libraries are not already "
            "installed. Installing now..."
        )
        InstallPypiFromAirwatchArtifactory()


def RunGobuildBootstrapScript(buildtype):
    """
    Runs the bootstrap script with the appropriate JSON file.
    """

    from migration_tools import bootstrap

    bootstrapPath = inspect.getfile(bootstrap)
    jsonName = _GetProductDependencyName()
    jsonPath = os.path.join(vmware.DirAbsPath("#"), "gobuild-deps", f"{jsonName}.json")

    g = {}
    dgcKwArgs = {}
    if vmware.LocalOpts.GetBool("LOW_BANDWIDTH", False):
        dgcKwArgs["worker_count"] = 1

    shadowRoot = os.path.join(vmware.DirAbsPath(PackageRoot()), "COMPONENTS-SHADOW")
    gobuildEnv = bootstrap.download_gobuild_components(
        jsonPath,
        buildtype,
        bootstrapPath,
        components_root=vmware.DirAbsPath(ComponentsRoot()),
        shadow_root=shadowRoot,
        **dgcKwArgs,
    )
    for key, val in gobuildEnv.items():
        os.environ[key] = val


def RunGitHubBootstrapScript(buildtype):
    """
    Runs the artifactory_download script with the appropriate JSON file.
    """

    from artifactory_download import bootstrap

    jsonName = _GetProductDependencyName()
    jsonPath = vmware.FileAbsPath(f"#github-deps/{jsonName}.json")

    if not os.path.isfile(jsonPath):
        return False

    gobuildEnv = bootstrap.download_github_components(
        jsonPath,
        buildtype,
        "",
        False,
        components_root=vmware.DirAbsPath(ComponentsRoot()),
    )

    for key, val in gobuildEnv.items():
        os.environ[key] = val
    return True


def ConanRoot():
    """Get the path to conan home."""
    return os.environ["CONAN_HOME"]


_CONAN_COMPONENTS = {}  # _CONAN_COMPONENTS[product][profile]
_CONAN_PROFILES = {}


def _GetConanProfileForEnv(env, universal=True, forceArch=None, forceCompiler=False):
    # This is similar to CaymanProductDirectory
    host = env.Host()
    if host.IsWindows():
        platform = "windows"
    elif host.IsLinux():
        platform = "linux"
    elif host.IsMac():
        platform = "macos"
    elif host.IsiPhoneOS():
        platform = "ios"
    elif host.IsiPhoneSimulator():
        platform = "iossim"
    elif host.IsAndroid():
        platform = "android"
    elif host.IsGeneric() or host.IsEmscripten():
        # generic and emscripten can be built on any host, so it makes sense
        # to use build host here
        if vmware.BuildHostIsWindows():
            platform = "windows"
        elif vmware.BuildHostIsLinux():
            platform = "linux"
        elif vmware.BuildHostIsMac():
            platform = "macos"
        else:
            vmware.ScriptError("Unknown platform")
    else:
        raise vmware.ScriptError(f"Unknown platform: {str(host)}")

    # Compiler
    if forceCompiler:
        compiler = forceCompiler
    elif host.IsWindows():
        compiler = "msvc17.12.5"
    elif host.IsLinux():
        compiler = "gcc12.1"
    elif host.IsMac():
        compiler = "xcode16.2"
    elif host.IsiPhoneOS() or host.IsiPhoneSimulator():
        compiler = "xcode16.2"
    elif host.IsAndroid():
        compiler = "ndk26b"
    elif host.IsGeneric() or host.IsEmscripten():
        # generic and emscripten can be built on any host, so it makes sense
        # to use build host here
        if vmware.BuildHostIsWindows():
            compiler = "msvc17.12.5"
        elif vmware.BuildHostIsLinux():
            compiler = "gcc12.1"
        elif vmware.BuildHostIsMac():
            compiler = "xcode16.2"
        else:
            vmware.ScriptError("Unknown compiler")
    else:
        raise vmware.ScriptError("Unknown compiler")

    # Arch
    if forceArch:
        arch = forceArch
    elif host.IsMac() and universal:
        arch = "universal"
    elif host.IsARM64EC():
        arch = "arm64ec"
    elif host.IsARM64():
        arch = "armv8"
    elif host.IsHardFloatABI():
        arch = "armv7hf"
    elif host.IsARM():
        arch = "armv7"
    elif host.Is64Bit():
        arch = "x86_64"
    elif host.IsGeneric() or host.IsEmscripten():
        arch = "x86_64"
        if vmware.BuildHostIsArm():
            arch = "armv8"
    elif host.Arch() == "x86" or host.Arch() == "android-x86":
        arch = "x86"
    else:
        raise vmware.ScriptError("Unknown architecture")

    profile = f"{platform}-{compiler}-{arch}"
    if "msvcrt-static" in env["VTOOLS"]:
        profile += "-static"

    return profile


def _MapBuildTypeToConan(buildtype):
    return "Debug" if buildtype == "obj" else "Release"


def _LoadConanProfile(profile):
    from conan.internal.api.profile.profile_loader import ProfileLoader

    loader = ProfileLoader(ConanRoot())
    conanBuildType = _MapBuildTypeToConan(vmware.RequestedBuildType())
    _CONAN_PROFILES[profile] = loader.from_cli_args(
        profiles=[profile],
        settings=["build_type=%s" % conanBuildType],
        options={},
        conf=None,
        cwd=None,
    )


def _LoadConanComponentsForProfile(product, profile, path=None):
    if not path:
        path = os.path.join(
            vmware.DirAbsPath(PackageRoot()),
            "CONAN",
            product,
            _MapBuildTypeToConan(vmware.RequestedBuildType()),
            profile,
            "SConscript_conandeps",
        )
    if not os.path.exists(path):
        return
    (conan_build_deps, conan_host_deps) = SConscript(path)
    if product not in _CONAN_COMPONENTS:
        _CONAN_COMPONENTS[product] = {}
    _CONAN_COMPONENTS[product][profile] = {
        "build": conan_build_deps,
        "host": conan_host_deps,
    }

    # Allow developers to pass `LOGGING=conan=debug` to see information about the Conan
    # components.
    if conanLog.isEnabledFor(logging.DEBUG):
        for phase, deps in _CONAN_COMPONENTS[product][profile].items():
            for name, dep in deps.items():
                conanLog.debug(
                    f"{product} {profile} {name} ({phase}): Package folder is "
                    + f"{dep.package_folder} and env has:\n\t"
                    + "\n\t".join(f"{k}={v}" for k, v in dep.env.items() if v)
                )


def GetConanPackageRootFromPath(path):
    # Find the package folder containing a path
    path = Path(path).resolve()
    conan_home = Path(ConanRoot()).resolve()
    if conan_home not in path.parents:
        return None
    # Packages are in <conan_home>/p/<package id>/p/... so we must
    # take path relative to the conan home and then strip the first
    # 3 directories from the path
    p = path.relative_to(conan_home)
    return str(conan_home / Path(*p.parts[:3]))


def GetConanProfile(env):
    profile = _GetConanProfileForEnv(env, universal=False)
    if profile not in _CONAN_PROFILES:
        _LoadConanProfile(profile)
    return _CONAN_PROFILES.get(profile, None)


def GetConanComponent(
    env,
    component,
    missingOk=False,
    allowArm64ecFallbackToX64=False,
    forceArch=None,
    product=None,
    forceCompiler=None,
):
    product = product if product else _GetProductDependencyName()
    profile = _GetConanProfileForEnv(
        env, forceArch=forceArch, forceCompiler=forceCompiler
    )
    if product not in _CONAN_COMPONENTS or profile not in _CONAN_COMPONENTS[product]:
        _LoadConanComponentsForProfile(product, profile)
    data = (
        _CONAN_COMPONENTS.get(product, {})
        .get(profile, {})
        .get("host", {})
        .get(component, None)
    )

    if not data and allowArm64ecFallbackToX64 and env.Host().IsARM64EC():
        data = GetConanComponent(
            env,
            component,
            missingOk=missingOk,
            forceArch="x86_64",
            product=product,
            forceCompiler=forceCompiler,
        )
        if data:
            log.warning(
                f"No arm64ec conan build for {component}, falling back to x86_64."
            )

    if not data and not missingOk:
        raise vmware.ScriptError(
            f"Unable to find Conan component `{component}` "
            f"for profile `{profile}` and product `{product}`"
        )
    return data


def GetConanBuildComponent(
    env, component, missingOk=False, product=None, forceArch=None, forceCompiler=None
):
    product = product if product else _GetProductDependencyName()
    profile = _GetConanProfileForEnv(
        env, forceArch=forceArch, forceCompiler=forceCompiler
    )
    if product not in _CONAN_COMPONENTS or profile not in _CONAN_COMPONENTS[product]:
        # If this product profile does not exist, then try using the first available profile.
        # In theory the majority of build tools brought in by build_requires will not be host
        # profile specific, so this should be a good fallback.
        # e.g. this path can happen when building for ios where GetConanBuildComponent is called
        # with the build env of mac64 which means _GetConanProfileForEnv returns a mac profile
        # macos-xcode-universal but ios based product only has ios-xcode-* profiles.
        if not _LoadConanComponentsForProfile(product, profile):
            profile = next(iter(_CONAN_COMPONENTS.get(product, {}).keys()), None)

    data = (
        _CONAN_COMPONENTS.get(product, {})
        .get(profile, {})
        .get("build", {})
        .get(component, None)
    )

    if not data and not missingOk:
        raise vmware.ScriptError(
            f"Unable to find Conan component `{component}` " f"for profile `{profile}`"
        )
    return data


def _ConanfilePath(product):
    return os.path.join(vmware.DirAbsPath("#"), "conan-deps", f"{product}.py")


def GetConanPackageRoot():
    return os.path.join(vmware.DirAbsPath(PackageRoot()), "CONAN")


def GetConanUniversalPackageRoot():
    return os.path.join(vmware.DirAbsPath(PackageRoot()), "CONAN-UNIVERSAL")


def GetVenvRoot():
    return os.path.join(vmware.DirAbsPath(BuildRoot()), "pyvenv")


def RunConanInstall(product, buildtype, buildMissing, fossaDeps=False):
    if product == "conan":
        return

    conanfilePath = _ConanfilePath(product)
    conanBuildType = _MapBuildTypeToConan(buildtype)
    conanDevelop = vmware.LocalOpts.GetBool("CONAN_DEVELOP", False)
    conanSandbox = vmware.LocalOpts.GetBool("CONAN_SANDBOX", False)
    fossa_dep_folder = vmware.DirAbsPath("#") if fossaDeps else None

    import conan_deps

    output_paths_by_profile = conan_deps.conan_install(
        conanfilePath,
        conanBuildType,
        GetConanPackageRoot(),
        generators=["EucSConsDeps"],
        build_missing=buildMissing,
        conan_develop=conanDevelop,
        verbose=Verbose(),
        config_folder=os.path.join(vmware.DirAbsPath("#"), "conan-deps", "config"),
        venv_root=GetVenvRoot(),
        conan_home=ConanRoot(),
        deploy_root=GetConanUniversalPackageRoot(),
        fossa_dep_folder=fossa_dep_folder,
        conan_sandbox=conanSandbox,
    )
    for profile, path in output_paths_by_profile.items():
        _LoadConanComponentsForProfile(product, profile, path / "SConscript_conandeps")


def InitConfigOptions(env_opts):
    global env_options
    env_options = env_opts

    # Detect the 'dump nodes only' mode
    SetDumpNodesOnly(env_opts["DUMPNODESONLY"])

    # Set the build number.
    buildnum = env_opts.get("BUILD_NUMBER") or os.environ.get(  # command line
        "BUILD_NUMBER"
    )  # environment variable
    if buildnum:
        SetBuildNumber(buildnum)

    # Set the product build number
    productbuildnum = env_opts.get("PRODUCT_BUILD_NUMBER")
    if productbuildnum:
        SetProductBuildNumber(productbuildnum)

    log.info("config: BUILD_NUMBER=%s" % BuildNumber())
    log.info("config: PRODUCT_BUILD_NUMBER=%s" % ProductBuildNumber())

    # Setup the default build root.
    buildRootOverridden = True
    if "BUILDROOT" not in env_opts:
        if vmware.LocalOpts.GetBool("AUTO_BUILDROOT", False):
            aboveTreeRoot = os.path.abspath(
                os.path.join(vmware.SourceTree(), os.path.pardir)
            )
            env_opts["BUILDROOT"] = vmware.utils.GetAutoBuildrootPath(
                aboveTreeRoot, env_opts["PRODUCT"]
            )
        else:
            env_opts["BUILDROOT"] = os.path.join(vmware.SourceTree(), "build")
            buildRootOverridden = False

    # Set the JDK alias
    vmware.SetRequestedJdkAlias(env_opts.get("JDK"))

    # Set the buildroot path with a little massaging if necessary
    SetBuildRoot(vmware.PoundPrefixRelativePath(env_opts["BUILDROOT"]))
    # Set a flag saying whether the default build root was overridden.
    SetBuildRootOverridden(buildRootOverridden)

    # Set up other global state from command line
    SetVerbose(env_opts["VERBOSE"])

    # turn on implicit-cache unless FULL_SCANNING is set
    if not env_opts["FULL_SCANNING"]:
        log.info("Enabling SCons implicit dependency caching")
        SCons.Script.SetOption("implicit_cache", 1)

    SetReleasePackagesDir(env_opts.get("RELEASE_PACKAGES_DIR", None))

    # Product build logs directory
    #
    # Precedence:
    #   1. Command-line (BUILDLOG_DIR).
    #   2. MBS environment variable (VMW_PRODUCT_LOG_DIRECTORY; available only
    #      to native compilations when building in the farm).
    #   3. Gobuild environment variable (BUILDLOG_DIR; available to any build
    #      launched by Gobuild).
    #   4. Default ($BUILDROOT/logs).
    logdir = (
        env_opts.get("BUILDLOG_DIR")
        or os.environ.get("BUILDLOG_DIR")
        or os.path.join(BuildRoot(), "logs")
    )
    SetBuildLogDir(logdir)

    SetBuildErrorSummary(env_opts.get("BUILD_ERROR_SUMMARY", True))

    # Handle processor counting
    if SCons.Script.GetOption("num_jobs") != 1:
        print("SCons: -j option ignored, use NUM_CPU instead")

    SetNumProcessors(env_opts["NUM_CPU"])

    # If using iscons, load the interactive mode now
    if vmware.BuildHostIsLinux() or vmware.BuildHostIsWindows():
        if vmware.LocalOpts.GetBool("ISCONS", False):
            from vmware import iscons

            iscons.LoadRemoteInteractiveMode()
        elif vmware.LocalOpts.GetBool("ISCONS_ONLY", False):
            raise vmware.PanicError(
                "ISCONS_ONLY enabled, but not running " "through iscons"
            )

    # Set the branch that this build is happening on
    headFile = Path(vmware.DirAbsPath("#")) / ".git" / "HEAD"
    with headFile.open("r") as line:
        ref = line.read().replace("\n", "")
    if ref.find("ref") == -1:
        branchName = "detached"
    else:
        branchName = ref.replace("ref: refs/heads/", "")
    SetGitBranchName(env_opts.get("BRANCH_NAME", branchName))

    # Set the change that this build is happening on (GitHub builds only).
    gitSha = os.getenv("GITHUB_SHA")
    SetGitHash(gitSha if gitSha else "unknown-sha")

    SetColoredLogging(env_opts.get("COLORED_LOGGING", False))

    SetDetailedBuildStats(env_opts.get("ENABLE_BUILD_STATS", False))

    # Set the components root directory.
    SetComponentsRoot(
        env_opts.get(
            "COMPONENTS_ROOT",
            # For compatibility, support the variable used by make, too
            vmware.LocalOpts.GetString(
                "GOBUILD_LOCALCACHE_DIR",
                os.getenv(
                    "GOBUILD_LOCALCACHE_DIR", os.path.join(PackageRoot(), "COMPONENTS")
                ),
            ),
        )
    )

    # Set the gobuild hosttype. This must be before RunGobuildBootstrapScript().
    SetGobuildHosttype(env_opts.get("GOBUILD_HOSTTYPE", None))

    # Verifying Prerequisites for Artifactory Download
    CheckPypiDependencies()

    # To minimize churn in gobuild.py, run the bootstrap script now.
    RunGobuildBootstrapScript(env_opts["BUILDTYPE"])

    RunGitHubBootstrapScript(env_opts["BUILDTYPE"])

    # Setting to generate fossa-deps.yml or not
    fossaDeps = vmware.LocalOpts.GetBool("FOSSA_DEPS", False)

    RunConanInstall(
        _GetProductDependencyName(),
        env_opts["BUILDTYPE"],
        env_opts["CONAN_BUILD_MISSING"],
        fossaDeps,
    )

    conanExtraProducts = vmware.GetProductDefinition().conanExtraProducts

    # This is ugly, but can be a workaround to avoid hcandroid install those
    # useless deps.
    if _GetProductDependencyName() == "hclin":
        conanExtraProducts = [
            "libva-1",
            "libva-2",
            "ffmpeg-vaapi",
            "ffmpeg-vaapi27",
            "ffmpeg-vaapi2",
        ]

    for conanExtraProduct in conanExtraProducts:
        RunConanInstall(
            conanExtraProduct,
            env_opts["BUILDTYPE"],
            env_opts["CONAN_BUILD_MISSING"],
            fossaDeps,
        )

    # Gobuild component options
    _ParseGoBuildComponentsEnvVars()
    _ParseGoBuildComponentsOption(env_opts.get("GOBUILD_COMPONENTS", ""))

    SetWindowsSystem(env_opts.get("WINDOWS_SYSTEM", None))

    if "PRODUCT" in env_opts:
        config = vmware.GetProductDefinition().GetConfigDefault
    else:
        config = lambda name, value: value

    # Attempt to use the option from the command line first, otherwise, fall back
    # to the per-product default, if that isn't set, fall back to False.
    SetUseNewGlib(env_opts.get("USE_NEW_GLIB", config("USE_NEW_GLIB", False)))

    SetUseClang(env_opts.get("USE_CLANG", config("USE_CLANG", False)))
    SetUseClangCoverage(
        env_opts.get("USE_CLANG_COVERAGE", config("USE_CLANG_COVERAGE", False))
    )
    SetUseClangSanitizer(env_opts.get("USE_CLANG_SANITIZER", None))
    SetUseClangStaticAnalyzer(env_opts.get("USE_CLANG_STATIC_ANALYZER", False))
    SetUseClangXray(env_opts.get("USE_CLANG_XRAY", False))
    SetUseClangTidyPlugin(env_opts.get("USE_CLANG_TIDY", None))
    SetClangPlugins(env_opts.get("CLANG_PLUGINS", ""))
    SetAndroidCxxRuntime(
        env_opts.get("ANDROID_CXX_RUNTIME", config("ANDROID_CXX_RUNTIME", None))
    )
    _ParseUseSanitizers(env_opts.get("USE_SANITIZERS", config("USE_SANITIZERS", "")))

    def ActLikeLocalOptString(opt, defaultValue=None):
        # Prefer env over Local.sc over product definition. Either way, behave like
        # it was a local option. If defaultValue is set, it should be a string type
        value = env_opts.get(
            opt, vmware.LocalOpts.GetString(opt, config(opt, defaultValue))
        )
        # Checking for not None specifically allows empty string and false-y
        # non-string options to exist
        if value is not None:
            vmware.LocalOpts[opt] = value
            log.info("Setting %s=%s", opt, value)

    ActLikeLocalOptString("DEPCOP")
    ActLikeLocalOptString("REMOTE_CACHE_FETCH")
    ActLikeLocalOptString("REMOTE_CACHE_PUSH")
    ActLikeLocalOptString("REMOTE_CACHE_RESTRICTIONS")
    ActLikeLocalOptString("VFS")
    ActLikeLocalOptString("COMPILE_DB")
    ActLikeLocalOptString("USE_MSVC_STATIC_ANALYZER")
    ActLikeLocalOptString("SIGN_BINARIES")

    # Sets glibc-2.28 as the default unless otherwise overridden
    # Note: If changing this default you should also change
    # bora/support/gobuild/targets/esx.py default values.
    ActLikeLocalOptString("USE_GLIBC_228", "False")

    # Note: If changing this default you should also change
    # bora/support/gobuild/targets/esx.py default values.
    ActLikeLocalOptString("USE_PYTHON311", "True")

    ActLikeLocalOptString("IOFVP_VASA_UPGRADE", "False")

    # If the option is not set on the command line or in Local.sc,
    # but it is set by the product,
    # then do as if it was set on the command line or in Local.sc.
    opts = [
        "USE_GLIBC_217",
        "USE_GCC6",
        "USE_GCC9",
        "USE_BIOS",
        "ENABLE_SPECTRE_MITIGATIONS",
        "GOBUILD_FILTER_EXCEPTIONS",
        "USE_CSTORE_SYSTIME",
        "USE_PYTHON311",
    ]
    for opt in opts:
        if (
            env_opts.get(opt, None) is None
            and vmware.LocalOpts.GetBool(opt, None) is None
            and config(opt, None) is not None
        ):
            vmware.LocalOpts[opt] = config(opt, None)

    # Remote caching and dependency enforcement settings may change
    # whether some settings are enabled (e.g. precompiled headers or action
    # implicit dependency scanning). We could put logic throughout the VMware
    # codebase calling vmware.LocalOpts.GetBool(), but it is more easily
    # supportable to set it aside in a function.
    depcopValue = ShouldEnableDepcop(vmware.LocalOpts.GetBool("DEPCOP", False))
    SetDependencyEnforcementEnabled(depcopValue)

    # The REMOTE_CACHE_FETCH LocalOpt is not _declared_ as a bool, so None can be
    # returned. This allows us to differentiate between off (passing False and
    # definitely not wanting remote caching) and not set (passing nothing and
    # having SCons do a ping check to determine whether to auto-enable it).
    fetchTriState = vmware.LocalOpts.GetBool("REMOTE_CACHE_FETCH", None)
    SetRemoteCacheFetchEnabled(ShouldEnableRemoteCacheFetch(fetchTriState))

    # REMOTE_CACHE_PUSH is simpler, as it is off unless REMOTE_CACHE_PUSH=True is
    # specified.
    SetRemoteCachePushEnabled(vmware.LocalOpts.GetBool("REMOTE_CACHE_PUSH", False))

    SetCompilationDatabaseEnabled(vmware.LocalOpts.GetBool("COMPILE_DB", False))

    SetUseMsvcStaticAnalyzer(
        vmware.LocalOpts.GetBool("USE_MSVC_STATIC_ANALYZER", False)
    )

    intOpts = ["USE_GCC"]
    for opt in intOpts:
        if (
            env_opts.get(opt, None) is None
            and vmware.LocalOpts.GetInt(opt, 0) == 0
            and config(opt, None) is not None
        ):
            vmware.LocalOpts[opt] = config(opt, None)

    def _GetOptFromEnv_Local_Config(opt):
        """Retrieve value from env_opts (cmdline), localopts (cmdline+local.sc), and
        config (product definition/configDefaults)
        """
        return env_opts.get(opt, vmware.LocalOpts.GetBool(opt, config(opt, False)))

    SetBuildingForAppleAppStore(
        _GetOptFromEnv_Local_Config("BUILD_FOR_APPLE_APP_STORE")
    )
    SetBuildingForAppleSandbox(_GetOptFromEnv_Local_Config("USE_APPLE_SANDBOX"))
    SetBuildingForAppleUniversal(_GetOptFromEnv_Local_Config("APPLE_UNIVERSAL"))


###########################################################
# Keeping track of the global build root.
#
_buildroot = ""


def SetBuildRoot(path):
    """Sets the global build root path.

    Argument should be a scons-style path.
    """
    global _buildroot
    _buildroot = path


def BuildRoot():
    """Retrieves the global build root path."""
    global _buildroot
    return _buildroot


_buildrootOverridden = False


def BuildRootOverridden():
    return _buildrootOverridden


def SetBuildRootOverridden(overridden):
    global _buildrootOverridden
    _buildrootOverridden = overridden


# This build generates files in the following locations:
# 'BUILDROOT'                  := master directory
#     vmware.BuildRoot()
# 'BUILDROOT/.sconsign.dblite' := "staleness" database
#     (unnamed)
# 'BUILDROOT/info'             := published information about build,
#     vmware.BuildInfoDir()    used to export tree to another platform
# 'BUILDROOT/lock'             := lock files
#     vmware.LockDir()         used for build thread synchronization
# 'BUILDROOT/bora'             := "built" pieces of the tree,
#     (unnamed)                internal representations
# 'BUILDROOT/package'          := "runnable" pieces of the tree
#     vmware.PackageRoot()


def BuildInfoDir():
    return os.path.join(BuildRoot(), "info")


def LockDir():
    return os.path.join(BuildRoot(), "lock")


def PackageRoot():
    return os.path.join(BuildRoot(), "package")


_componentsRoot = None


def SetComponentsRoot(path):
    """Set the path where all components are stored."""
    global _componentsRoot
    _componentsRoot = path


def ComponentsRoot():
    """Get the path where all components are stored."""
    return _componentsRoot


gobuildCacheDirComparisonPrefix = None


def IsFromGobuildCacheDir(normalizedPath):
    """
    Does a best-guess check as to whether this path comes from a gobuild cache.
    It needs to be efficient so it can't use GetGobuildComponentInfoFromPath
    and avoids doing case normalization.

    This will not detect the case where a dependency comes from an overridden
    component that is not a child of the gobuild cache directory (e.g.
    GOBUILD_COMPONENTS=cayman_foo=C:\foo), so if you need more accuracy and
    don't require as good of performance, use GetGobuildComponentInfoFromPath
    instead.

    :param path: Absolute path of file to check. Path should be case-normalized
                 and have the OS-appropriate slashes; caller can use
                 os.path.normpath if the path could have the wrong slashes.
    :return: True if it's from the gobuild cache, False otherwise.
    """
    global gobuildCacheDirComparisonPrefix

    if gobuildCacheDirComparisonPrefix is None:
        gobuildCacheDirComparisonPrefix = os.path.normcase(
            vmware.DirAbsPath(ComponentsRoot())
        )

    return normalizedPath.startswith(gobuildCacheDirComparisonPrefix)


def GetGobuildCacheDirComparisonPrefix():
    """For diagnostic purposes only"""
    return gobuildCacheDirComparisonPrefix


###########################################################
# Global state about verbosity of output
#

_verbose = False


def SetVerbose(v):
    """Sets the global verbosity state.

    v: a boolean value that should be true if verbose output is desired.

    This may be deprecated at some point in favor of using the logging module.
    """
    global _verbose
    _verbose = v


def Verbose():
    """Retrieves the global verbosity state (boolean)"""
    global _verbose
    return _verbose


##########################################################
# Enable colored logging

_coloredLogging = False


def ColoredLogging():
    global _coloredLogging
    return _coloredLogging


def SetColoredLogging(val):
    global _coloredLogging
    _coloredLogging = val


##########################################################
# Enable detailed build stats output at build completion

_detailedBuildStats = False


def DetailedBuildStats():
    global _detailedBuildStats
    return _detailedBuildStats


def SetDetailedBuildStats(val):
    global _detailedBuildStats
    _detailedBuildStats = val


#######################################################
# State for "dump nodes only" mode
#

_dumpNodesOnly = False


def SetDumpNodesOnly(b):
    global _dumpNodesOnly
    _dumpNodesOnly = b


def DumpNodesOnly():
    return _dumpNodesOnly


########################################################
# Common functionality across build number and product build number.
#


def GetDefaultBuildNumber():
    """
    Returns the default build number. Not currently exported for use by other
    modules.

    This function returns a single zero to match what tinderbox uses. This
    allows us to get cache hits for local builds when tinderbox is enabled as a
    cache producer.
    """
    return "0"


def IsBuildNumberInfoOverridden():
    """
    Returns True if a non-default build number or product build number is in
    use. Returns False otherwise. These are commonly specified through the
    PRODUCT_NUMBER and PRODUCT_BUILD_NUMBER parameters.
    """
    defaultBuildNumber = GetDefaultBuildNumber()
    return (
        vmware.BuildNumber() != defaultBuildNumber
        or vmware.ProductBuildNumber() != defaultBuildNumber
    )


########################################################
# Build number
#

_buildnum = GetDefaultBuildNumber()


def SetBuildNumber(num):
    global _buildnum
    _buildnum = num


def BuildNumber():
    return _buildnum


########################################################
# Change number
#

_changeset = vmware.LocalOpts.GetChangeNumber(defaultval=0)


def SetChangeNumber(num):
    global _changeset
    _changeset = str(num)


def ChangeNumber():
    global _changeset
    if _changeset == 0:
        _changeset = vmware.utils.GetP5ChangesHint()
    return str(_changeset)


########################################################
# Product build number
#

_prodbuildnum = GetDefaultBuildNumber()


def SetProductBuildNumber(num):
    global _prodbuildnum
    _prodbuildnum = num


def ProductBuildNumber():
    return _prodbuildnum


###################################
# Configuration for number of CPU's

_numLocalCpus = vmware.CountLocalProcessors()


def SetNumProcessors(numLocalCpus):
    """
    Sets the number of local and remote processors.
    This information is used when creating forkers for SCons.
    """
    global _numLocalCpus

    # handle local CPUs first
    numLocalCpus = int(numLocalCpus)
    if numLocalCpus == 0:
        numLocalCpus = vmware.CountLocalProcessors()
    elif numLocalCpus > vmware.CountLocalProcessors():
        log.warn(
            "NUM_CPU exceeds maximum local processors %d > %d"
            % (numLocalCpus, vmware.CountLocalProcessors())
        )

    log.info("setting number of local CPUs to %d" % numLocalCpus)
    _numLocalCpus = numLocalCpus
    SCons.Script.SetOption("num_jobs", numLocalCpus)


def NumProcessors():
    """
    Return the total number of CPUs that this build will use
    """
    return _numLocalCpus


def NumLocalProcessors():
    """
    Return the number of local CPUs that this build will use
    """
    return _numLocalCpus


#####
# State for packaging hints to cut down packaging script execution for specific
# targets enumerated in bora/scons/modules/fast-targets.py

_packageHints = set()


def PackageHints():
    return _packageHints


def SetPackageHints(hints):
    global _packageHints
    _packageHints = hints


#####
# State and accessors for the release package export directory
#
#  The publish directory is where build deliverables are copied at the end of
#  the build. It is controlled by the RELEASE_PACKAGES_DIR environment
#  variable, which is typically set by Gobuild (for SB/OB builds).
#
#  When RELEASE_PACKAGES_DIR is not set (developers builds), the default
#  may be set by init.py to ^/build/root/<PRODUCT>/<BLDTYPE>/publish for
#  some products (for example 'esx') or left as None.
#
#  Note: if a default is set, remote caching may be enabled for inputs from
#        that folder.
#
#  "build dir" vs. "publish dir"
#
#   Build directory
#     Where to store intermediary build files which don't have a vocation to be
#     used after the build, including .o/.a's, and any file/binary that is
#     further packaged into a TGZ, VIB, ISO, ... A fairly accurate way to look
#     at the build directory is to think of it as temporary storage that is
#     wiped out at the end of the build.
#
#   Publish directory
#     Where to copy the final build deliverables (.iso, .vib, ...), as well as
#     all files that are required to test and debug these deliverables. The
#     publish directory is accessible in buildweb under the 'deliverable' tab.

_releasePackagesDir = None


def SetReleasePackagesDir(directory):
    global _releasePackagesDir
    if directory:
        _releasePackagesDir = vmware.PoundPrefixRelativePath(directory)

        # Avoid build issues by creating the directory if it doesn't exist.
        # This also helps GitHub builds have the remote cache log directory
        # created before SCons tries to access it.
        rpdAbsPath = vmware.DirAbsPath(_releasePackagesDir)
        if not os.path.isdir(rpdAbsPath):
            os.makedirs(rpdAbsPath)
    else:
        _releasePackagesDir = directory


def ReleasePackagesDir():
    return _releasePackagesDir


#####
# State and accessors for the build logs directory

_buildLogDir = None


def SetBuildLogDir(directory):
    global _buildLogDir
    if directory:
        _buildLogDir = vmware.PoundPrefixRelativePath(directory)
    else:
        _buildLogDir = directory


def BuildLogDir():
    return _buildLogDir


_buildErrorSummary = True


def SetBuildErrorSummary(enabled):
    global _buildErrorSummary
    _buildErrorSummary = enabled


def BuildErrorSummary():
    return _buildErrorSummary


##############################################
# Gets or sets the branch this build is running on

_branchName = False


def SetGitBranchName(b):
    global _branchName
    _branchName = b


def GitBranchName():
    return _branchName


##############################################
# Gets or sets the git sha this build is running on
# This is only currently set on GitHub builds

_gitHash = None
_gitShortHash = None


def SetGitHash(sha):
    global _gitHash, _gitShortHash
    _gitHash = sha

    # Note: this is a more future-proof version of the short hash. As of March 2025,
    # the short hash for cart is 10 characters long, so for now we'll use 11 to be safe.
    _gitShortHash = sha[:11] if sha else None


def GitHash():
    return _gitHash


def GitShortHash():
    """
    Fetches the short version of the Git hash. As described in SetGitHash(), this is
    longer than the current short hash to future-proof against changes in the length
    of the hash, given that we use this to uniquely identify build source hashes.
    """
    return _gitShortHash


##############################################
# Determime if we are in a development branch.
# As the naming convention of branch is not
# yet defined consider main, topic, feature
# and detached branch as development branch.
# Every other branch is consider release
# so we do not accidently leaked out development
# code or binaries.


def IsDevelopmentBranch():
    if (
        _branchName == "main"
        or _branchName.startswith("topic")
        or _branchName.startswith("feature")
        or _branchName == "detached"
    ):
        return True

    return False


#############################################
# Get and Set Apple App Store and Sandbox options

_buildingForAppleAppStore = None


def SetBuildingForAppleAppStore(value):
    global _buildingForAppleAppStore
    _buildingForAppleAppStore = value


def BuildingForAppleAppStore():
    return _buildingForAppleAppStore


_buildingForAppleSandbox = None


def SetBuildingForAppleSandbox(value):
    global _buildingForAppleSandbox

    if BuildingForAppleAppStore() and not value:
        raise vmware.ConfigError("Building for Apple's App Store requires sandboxing.")
    _buildingForAppleSandbox = value


def BuildingForAppleSandbox():
    return _buildingForAppleSandbox


#############################################
# Gets, Sets, or parses the location to gobuild components

_goBuildComponents = {}


# Environment variables bypass Windows command line length restrictions
def _ParseGoBuildComponentsEnvVars():
    # Same GOBUILD_X_ROOT convention used by our Make, Maven, Bazel, etc.
    # builds.
    pattern = re.compile("^GOBUILD_(.*)_ROOT$")

    for k, v in os.environ.items():
        match = pattern.match(k)
        if match and v:
            compName = match.group(1)
            SetGobuildComponentRoot(compName.lower().replace("-", "_"), v)


def _ParseGoBuildComponentsOption(option):
    if not option:
        return

    # option is a comma separated list of comp_name=comp_root pairs
    for compspec in option.split(","):
        try:
            compName, compRoot = compspec.split("=", 1)

            if compRoot:
                SetGobuildComponentRoot(
                    compName.lower().replace("-", "_"), vmware.DirAbsPath(compRoot)
                )
        except ValueError:
            raise vmware.ConfigError(
                "Invalid string %s in option GOBUILD_COMPONENTS" % compspec
            )


def SetGobuildComponentRoot(component, root):
    global _goBuildComponents
    _goBuildComponents[component] = root


def GobuildComponentRoot(component):
    if component not in _goBuildComponents:
        raise vmware.PanicError("Unknown component: %s" % component)
    return _goBuildComponents[component]


def GobuildHasComponentRoot(component):
    return component in _goBuildComponents


def GobuildGetComponentRoots():
    return _goBuildComponents.copy()


def GobuildComponentsFromCommandLine():
    """
    Return the list of components that were given on the command line through
    the GOBUILD_COMPONENTS argument.
    """
    return _goBuildComponents.keys()


###############################################
# Dependency enforcement and remote caching utility functions

_dependencyEnforcementEnabled = None


def SetDependencyEnforcementEnabled(enabled):
    global _dependencyEnforcementEnabled
    _dependencyEnforcementEnabled = enabled


def DependencyEnforcementEnabled():
    return _dependencyEnforcementEnabled


def RemoteCacheUrlDefault():
    return "http://bazel-remote.build.omnissa.com:8080"


_remoteCacheFetchEnabled = None


def SetRemoteCacheFetchEnabled(enabled):
    global _remoteCacheFetchEnabled
    _remoteCacheFetchEnabled = enabled


def RemoteCacheFetchEnabled():
    return _remoteCacheFetchEnabled


_remoteCachePushEnabled = None


def SetRemoteCachePushEnabled(enabled):
    global _remoteCachePushEnabled
    _remoteCachePushEnabled = enabled


def RemoteCachePushEnabled():
    return _remoteCachePushEnabled


def RemoteCachingSupported():
    """
    Returns True if remote caching is supported for the current product on the
    current platform or if a developer set REMOTE_CACHE_RESTRICTIONS=False.
    """
    if not vmware.LocalOpts.GetBool("REMOTE_CACHE_RESTRICTIONS", True):
        return True
    elif vmware.BuildHostIsWindows():
        return vmware.GetRequestedProduct() in [
            "appblastlibs",
            "hccrt",
            "horizonagent",
            "horizoncommon",
        ]
    elif vmware.BuildHostIsLinux():
        return vmware.GetRequestedProduct() in [
            "appblastlibs",
            "hccrt",
            "horizoncommon",
            "horizonlinuxagent",
        ]
    elif vmware.BuildHostIsMac():
        return vmware.GetRequestedProduct() in [
            "hccrt",
            "horizoncommon",
        ]
    else:
        return False


###############################################
# Gets or sets whether compile_db support is enabled.

_compilationDatabaseEnabled = False


def CompilationDatabaseEnabled():
    return _compilationDatabaseEnabled


def SetCompilationDatabaseEnabled(enabled):
    global _compilationDatabaseEnabled
    _compilationDatabaseEnabled = enabled


###############################################
# Gets or sets list of compiler sanitizers to use

_useSanitizers = []


def _ParseUseSanitizers(option):
    SetUseSanitizers(list(filter(lambda x: x != "", option.split(","))))


def SetUseSanitizers(val):
    global _useSanitizers
    _useSanitizers = val


def UseSanitizers():
    return _useSanitizers


###############################################
# Gets or sets usage of MSVC /analyze option

_useMsvcStaticAnalyzer = None


def SetUseMsvcStaticAnalyzer(value):
    global _useMsvcStaticAnalyzer
    _useMsvcStaticAnalyzer = value


def UseMsvcStaticAnalyzer():
    return _useMsvcStaticAnalyzer


###############################################
# Gets or sets Android C++ runtime library

_AndroidCxxRuntime = None


def SetAndroidCxxRuntime(value):
    global _AndroidCxxRuntime
    _AndroidCxxRuntime = value


def AndroidCxxRuntime():
    return _AndroidCxxRuntime


###############################################
# Gets or sets the Window's %SYSTEM% variable.

_windowsSystem = None


def SetWindowsSystem(path):
    global _windowsSystem
    if not path:
        if vmware.BuildHostIsWindows():
            path = os.environ.get("SYSTEM", r"C:\Windows")
        else:
            path = ""
    _windowsSystem = path


def WindowsSystem():
    return _windowsSystem


###############################################
# Gets or sets the "should build against a new major version of Glib" value.

_useNewGlib = None


def _ParseBoolInput(value):
    if type(value) == bool:
        return value

    if type(value) == type(""):
        if value.lower() == "false" or value == "0":
            return False

        if value.lower() == "true" or value == "1":
            return True


def SetUseNewGlib(value):
    """Sets the "should use a new Glib" value."""
    global _useNewGlib
    _useNewGlib = _ParseBoolInput(value)

    if _useNewGlib is None:
        raise vmware.ScriptError("Unknown value of USE_NEW_GLIB = %s" % str(value))
    if _useNewGlib:
        log.info("Using new Glib")
    else:
        log.info("Using old Glib")


def UseNewGlib():
    """Retrieves the "should use a new Glib" value."""
    return _useNewGlib


# Whether to build Apple universal bundle
# True means universal build,
# False means individual native build.
_buildingForAppleUniversal = False


def SetBuildingForAppleUniversal(value):
    global _buildingForAppleUniversal
    _buildingForAppleUniversal = value


def BuildingForAppleUniversal():
    return _buildingForAppleUniversal


def ShouldEnableDepcop(depcopValue):
    """
    :param depcopValue: True if DEPCOP=TRUE was specified, False otherwise.

    :return: True if depcop should be enabled, False otherwise.
    """
    if depcopValue and os.environ.get("COVSCAN"):
        log.info("Depcop: disabled due to COVSCAN running")
        depcopValue = False

    return depcopValue


def ShouldEnableRemoteCacheFetch(fetchTriState):
    """
    For official/sandbox/tinderbox builds (BUILDKIND_FOR_VERSION_TAG_ONLY is
    set to a non-empty string in those), this function also returns False if:
      1. Remote caching wasn't explicitly enabled via REMOTE_CACHE_FETCH=True.
      2. GOBUILD_REMOTE_CACHE_MODE=disabled, which happens when
         target.GetOptions contains build.remotecache.supported-protocols *and*
         either:
           a. --remotecache=disabled was passed to gobuild, or
           b. the policy for the type of build kind disables caching, or
           c. the list of supported protocols is empty.

    For local/DBC builds, this function returns True or False if
    REMOTE_CACHE_FETCH is explicitly set, and otherwise returns False if:
      1. The product doesn't support remote caching (see RemoteCachingSupported
         for details)
      2. BuildRoot is overridden.
      3. It's python2.
      4. Probing indicates the cache is responding too slowly.

    w.r.t. probing, this function performs a few network requests to the remote
    cache server to determine whether to turn on remote caching. In an ideal
    world, we would be able to do accurate bandwidth estimation to figure out
    whether the throughput to the remote cache server is sufficient to enable
    remote caching. That is beyond the scope of what we can do here so we
    settle for a pretty basic solution - we issue a few requests in parallel
    for empty /cas/ entries on the remote cache server. If the requests all
    succeed and the roundtrip time for those requests averages under a certain
    threshold, probing is considered to have succeeded.

    :param fetchTriState: True or False if REMOTE_CACHE_FETCH is specified,
                          None if REMOTE_CACHE_FETCH was unspecified.

    :return: True if remote cache fetch should be enabled, False otherwise.
    """
    if fetchTriState is False:
        # Explicitly disabled.
        return False
    elif vmware.LocalOpts.GetString("BUILDKIND_FOR_VERSION_TAG_ONLY", ""):
        # Gobuild case. This case is different because we make the target
        # explicitly opt in (REMOTE_CACHE_FETCH=True), and also allow Gobuild
        # to override it (GOBUILD_REMOTE_CACHE_MODE=disabled).
        #
        # Note: this code path is unreachable until we audit all usage of
        # BUILDKIND_FOR_VERSION_TAG_ONLY, because GitHub does not provide that.
        return False
    else:
        # Non-Gobuild case
        if fetchTriState:
            # If REMOTE_CACHE_FETCH is specified, respect that.
            return True
        elif not RemoteCachingSupported():
            # No need to log anything in this case.
            return False
        elif BuildRootOverridden():
            log.info(
                "RemoteCache: not enabling; build root directory is " "overridden."
            )
            return False

    try:
        from concurrent.futures import ThreadPoolExecutor
        import datetime
        import queue
        import urllib.request
    except ImportError:
        # Likely Python 2, which doesn't have urllib.request. Return False.
        return False

    # This empty CAS blob is always available on the server for testing
    # https://github.com/buchgr/bazel-remote#useful-endpoints
    TEST_CAS = "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"
    # Average request must take <= 200ms to enable caching. Currently we see
    # results in the range of 350-550ms for developers not in North America.
    THRESHOLD = 200
    REQUESTS = 5  # 5 requests
    TIMEOUT = 500  # Let requests timeout after 500ms
    FAILURE_CODE = -1

    server = SCons.Script.GetOption("remote_cache_url")
    if not server:
        server = RemoteCacheUrlDefault()
    url = "%s/cas/%s" % (server.rstrip("/"), TEST_CAS)

    q = queue.Queue(0)

    def _SendRemoteCacheRequest():
        """
        Performs one request to the remote cache server. If the request is
        successful, puts the elapsed time in milliseconds in the queue.
        Otherwise, puts FAILURE_CODE in the queue.
        """
        try:
            before = datetime.datetime.now()
            r = urllib.request.urlopen(url, timeout=TIMEOUT / 1000)
            delta = datetime.datetime.now() - before
            ms = delta.total_seconds() * 1000
            if r.status < 400 or r.status >= 600:
                q.put(ms)
                return
        except:
            pass

        q.put(FAILURE_CODE)

    with ThreadPoolExecutor(max_workers=REQUESTS) as executor:
        # Issue the specified number of requests to the executor.
        for i in range(REQUESTS):
            executor.submit(_SendRemoteCacheRequest)
        # Retrieve the same number of results and store in `times`.
        times = []
        for i in range(REQUESTS):
            times.append(q.get())

    if FAILURE_CODE in times:
        log.info(
            "RemoteCache: not enabling; one or more requests to the server " "failed."
        )
        return False
    else:
        average = sum(times) / len(times)
        result = average <= THRESHOLD
        log.info(
            "RemoteCache: %senabling; the average request time to the "
            "server was %dms (must be at most %dms).",
            "" if result else "not ",
            average,
            THRESHOLD,
        )
        return result
