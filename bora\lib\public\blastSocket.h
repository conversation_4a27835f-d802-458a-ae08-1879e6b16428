/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

#ifndef BLAST_SOCKET_H
#define BLAST_SOCKET_H

#include "asyncsocket.h"
#include "blastSetup.h"
#include "fecAsyncSocket.h"
#include "fecSocketOptions.h"
#include "vdpPlugin.h"
#include "vvclib.h"
#include "ssl.h"
#include "uuid.h"

#if defined(__cplusplus)
extern "C" {
#endif

// Below #defines are used to generate the Shadow Session Id.
#define MAX_WTS_SESSION_ID ((1 << 16) - 1) // 16 bit

#define MAX_SHADOW_INSTANCE_ID ((1 << 8) - 1) // 8 bit
#define PRIMARY_CONNECTION_SHADOW_INSTANCE_ID 0
#
// Websocket subprotocol names
#define BLAST_SOCKET_WEBSOCKET_SUBPROTOCOL_VVC_WITH_CONTINUITY "blast-vvc2"
#define BLAST_SOCKET_WEBSOCKET_SUBPROTOCOL_VVC_NO_CONTINUITY "blast-vvc"

// Legacy Websocket subprotocol names
#define BLAST_SOCKET_WEBSOCKET_SUBPROTOCOL_VVC_CONTINUITY_LEGACY "vvc2.vmware.com"
#define BLAST_SOCKET_WEBSOCKET_SUBPROTOCOL_VVC_LEGACY "vvc.vmware.com"
#define BLAST_SOCKET_WEBSOCKET_SUBPROTOCOL_VVC_OLD_LEGACY "vmware-vvc"

// TLS ALPN string prefix
#define BLAST_SOCKET_TLS_ALPN_PREFIX "blast-vvc-"
#define BLAST_SOCKET_TLS_ALPN_SUFFIX_LEN 5
#define BLAST_SOCKET_TLS_ALPN_SFX_ENCODELEN 8

#define ASYNCSOCKET_FLUSH_TIMEOUT_MSEC 2000

// Space for IPv6 (<=39 chars + %link suffix + [] wrapper + \0) + headroom
#define BLASTSOCKET_IPADDRESS_STRING_SIZE 64

// VVC's default BW Burst Interval, same as VVC_MAX_BANDWIDTH_BURST_MSEC_DEFAULT
#define BLASTSOCKET_MAX_BANDWIDTH_BURST_MSEC_DEFAULT 1000 /* msec */
#define BLASTSOCKET_MAX_BANDWIDTH_BURST_MSEC_MAX 30000    /* msec */

/*
 * Configuration settings for network stats detection
 */
typedef struct BlastSocketNetworkStatsConfig {
   /* Kill switch. Set to 0 to disable the feature */
   uint32 networkStatsCheckEnabled;

   /* Poll Callback Time Lapse Seconds. Default to 15. */
   uint32 detectCbPeriodSec;

   /* Config whether to add RTTV MS to RTT MS. Default to 0. */
   uint32 addRttvToRtt;

   /*
    * Quality Score Range use to define Quality State.
    */
   uint16 qualityScoreTcpThresholdGood, qualityScoreTcpThresholdPoor;
   uint16 qualityScoreBeatThresholdGood, qualityScoreBeatThresholdPoor;

   /*
    * TCP and BEAT Network Indicator High and Low boundaries.
    * See blastSocketClientNetStatsDetect.h for default values.
    *
    * beatRttMSExtreme and beatPkLossPercentExtreme are used as sole
    * indicators to trigger POOR state without going through normal
    * calculating process.
    */
   double tcpRttMSLow, tcpRttMSHigh;
   double beatRttMSLow, beatRttMSHigh, beatRttMSExtreme, beatPkLossPercentLow,
      beatPkLossPercentHigh, beatPkLossPercentExtreme, beatRttvMSLow, beatRttvMSHigh;

   /*
    * Weight for TCP and BEAT Network Indicators
    */
   uint32 tcpRttMSWeight, beatRttMSWeight, beatPkLossPercentWeight, beatRttvMSWeight;

} BlastSocketNetworkStatsConfig;

typedef struct BlastSocketParams {
   Bool udpEnabled;
   Bool udpSSLEnabled;
   Bool bweEnabled;
   Bool localNCEnabled;
   Bool localNetworkIntelligenceEnabled;
   Bool localVVCCloseSeqEnabled;
   Bool localVVCPauseResumeEnabled;
   Bool localVVCQoSPolicyEnabled;
   Bool localVVCBatchingEnabled;
   Bool legacyBlastWSProtocolsAllowed;
   Bool udpAuxiliaryFlowsEnabled;
   Bool perfMonEnabled;
   Bool blastSocketThreadEnabled;
   Bool isVvcPluginLoaderNeeded;
   Bool beatRouteReplacementEnabled;
   int maxLowLevelPacketSizeBytes;
   double minimumRate; /* bytesPerSecond */
   double maximumRate; /* bytesPerSecond */
   double maxBandwidthBurstMsec;

   int serverUdpExternalPort;
   char thumbprint[SSL_V_THUMBPRINT_STRING_MAX_SIZE];
   char thumbprint256[SSL_V_THUMBPRINT_STRING_MAX_SIZE];
   char thumbprint384[SSL_V_THUMBPRINT_STRING_MAX_SIZE];
   char thumbprint512[SSL_V_THUMBPRINT_STRING_MAX_SIZE];
   char serverIp[BLASTSOCKET_IPADDRESS_STRING_SIZE];
   // listenIp is not used yet, but is coming Real Soon
   char listenIp[BLASTSOCKET_IPADDRESS_STRING_SIZE];

   /*
    * The AOutUDP Dscp values are read and pushed by the application to
    * BlastSocket_Start() - which then pushes to FECAsyncSocket_Listen() via
    * fecStaticOpts. This is to ensure UDPFEC Connection setup and SSL packets
    * are put on wire with dscp value set.
    */
   int dscpAOutUDPv4;
   int dscpAOutUDPv6;

   int maxCookieIdleTimeSec;
   int cookieCleanupIntervalMS;

   void *sslCtx;

   BlastSocketNetworkStatsConfig networkQualityStatsConfig;

   Bool disableVvcRawChannels;

} BlastSocketParams;

typedef struct BlastUDPParams {
   char *udpE2EReqValue;
   char *udpE2EReqOldValue;
   char *udpHopReqValue;
   char *udpHopReqOldValue;
   char *connInfo;
   char *connInfoOld;
   char *udpE2ERespValue;
   char *udpE2ERespOldValue;
   char *udpHopRespValue;
   char *udpHopRespOldValue;
} BlastUDPParams;

typedef struct BlastWSParams {
   BlastUDPParams *udpParams;

   /*
    * The below headers are applicable for the Blast Protocol and are not
    * tied to a specific transport.
    */
   char *blastE2EReqValue;
   char *blastE2EReqOldValue;
   char *blastHopReqValue;
   char *blastHopReqOldValue;
   char *blastE2ERespValue;
   char *blastE2ERespOldValue;
   char *blastHopRespValue;
   char *blastHopRespOldValue;
   char *blastFeaturesReqValue;
   char *blastFeaturesReqOldValue;
   char *blastFeaturesRespValue;
   char *blastFeaturesRespOldValue;
} BlastWSParams;


/*
 * Connection type is also defined in libCdk for Horizon (View) client.
 * This should match the cdkConnection definition.
 *
 * Note: This struct definition used to be in asyncProxySocket.h
 */

typedef enum ConnectionType {
   CONNECTION_TYPE_TCP_ONLY = 0,
   CONNECTION_TYPE_UDP_ONLY,
   CONNECTION_TYPE_MIXED_MODE,
   CONNECTION_TYPE_LEGACY, // UDP v1 implementation
   CONNECTION_TYPE_COUNT,  // must alway be the last item
} ConnectionType;

typedef struct BlastSocketContext BlastSocketContext;
typedef struct BlastSocketConnectContext BlastSocketConnectContext;

// Callback functions that must be provided by the application
typedef Bool (*BlastSocketAcceptCb)(const char *vauth, int32 vvcSessionID, void *clientData);
typedef void (*BlastSocketCloseCb)(const char *vauth, int32 sessionID, Bool isError,
                                   VDPConnectionResult closeReason, void *clientData);

/* Blast session started (vAuth was redeemed) */
typedef void (*BlastSessionStartedCb)(const char *vAuth, void *clientData);

/* Blast session and/or vAuth invalidated */
typedef void (*BlastSessionInvalidatedCb)(const char *vAuth, VDPConnectionResult reason,
                                          void *clientData);

typedef BlastWSParams *(*BlastSocketPrepareWSUpgradeRequest)(void *clientData);

typedef void (*BlastSocketProcessWSUpgradeResponse)(const char *httpResponse, void *clientData);

Bool BlastSocket_IsBlastSocketThreadEnabled(BlastSocketContext *blastSocketContext);

/*
 * Entry-point functions to be used by the application.
 */
BlastSocketContext *BlastSocket_Start(
   void *sslCtx, Bool useSSL, const char *webSocketAddr, int *webSocketPort, int *udpFecPort,
   int numUdpFecPorts, const BlastSocketParams *blastSocketParams, BlastSocketAcceptCb acceptFn,
   BlastSocketCloseCb socketCloseCb, BlastSessionStartedCb sessionStartedCb,
   BlastSessionInvalidatedCb sessionInvalidatedCb, void *clientData);

void BlastSocket_Stop(BlastSocketContext *blastSocketContext);

AsyncSocket *BlastSocket_ConnectWebsocket(
   const char *url, struct _SSLVerifyParam *sslVerifyParam, const char *proxyStr,
   const char *cookies, const char *protocols[], BlastSocketPrepareWSUpgradeRequest prepareFn,
   BlastSocketProcessWSUpgradeResponse processFn, AsyncSocketConnectFn connectFn, void *clientData,
   AsyncSocketConnectFlags flags, AsyncSocketPollParams *pollParams, int *error);

AsyncSocket *BlastSocket_ConnectWebsocketWithFd(
   int tcpSocketFd, const char *url, const char *fqdn, struct _SSLVerifyParam *sslVerifyParam,
   const char *proxyStr, const char *cookies, const char *protocols[],
   BlastSocketPrepareWSUpgradeRequest prepareFn, BlastSocketProcessWSUpgradeResponse processFn,
   AsyncSocketConnectFn connectFn, void *clientData, AsyncSocketConnectFlags flags,
   AsyncSocketPollParams *pollParams, int *error);

Bool BlastSocket_InvalidateAuth(const char *vAuth, BlastSocketContext *blastSocketCtx,
                                VDPConnectionResult reason);

int BlastSocket_StartReverseConnection(BlastSocketContext *blastSocketCtx, const char *reverseHost,
                                       unsigned int reversePort, AsyncSocketConnectFn onConnectCb,
                                       AsyncSocketErrorFn errorFn, void *clientData,
                                       AsyncSocketConnectFlags connectFlags,
                                       AsyncSocket **reverseAsock);

AsyncSocket *BlastSocket_UpgradeReverseConnection(AsyncSocket *asock, const char *protocols[],
                                                  BlastSocketContext *blastSocketCtx, Bool useSSL,
                                                  void *sslCtx, int *error);

/*
 * Functions related to VvcSessionStart/Stop.
 */
VvcStatus BlastSocket_StopVvcSession(BlastSocketContext *blastSocketCtx, int32 vvcSessionId,
                                     const char *vAuth, VDPConnectionResult reason);

VvcStatus BlastSocket_SetVvcMinMaxBw(BlastSocketContext *blastSocketCtx, const char *vAuth,
                                     double minimumRate, double maximumRate,
                                     double maxBandwidthBurstMsec);

void BlastSocketCompleteGetParamsCB(AsyncSocket *asock, void *buf, int len, Bool status,
                                    void *clientData, int serialNo);

/*
 * Functions related to Authentication.
 */
Bool BlastSocket_HandleNewAuthRequest(Bool isShadowSession, BlastSocketContext *blastSocketCtx,
                                      EFIGUID *smConnectionId, char **vAuth);


void BlastSocket_TossExpiredCookies(Bool forceExpire, BlastSocketContext *blastSocketCtx,
                                    VDPConnectionResult reason);

Bool BlastSocket_SaveAuthToken(const char *vAuth, void *clientData, Bool isShadowSession);


char *BlastSocket_GetCookieFromViewAuthToken(const char *vAuth, BlastSocketContext *blastSocketCtx);
/*
 * Function to query the Connection information
 */
void BlastSocket_GetBlastConnectionInfo(BlastSocketContext *blastSocketCtx, const char *vAuth,
                                        BlastConnectionInfo *blastConnInfo);

/*
 * Functions related to Websocket (sub)protocol management
 */
Bool BlastSocket_SubprotocolImpliesNC(const char *subprotocol);

/*
 * Function to let consumers know if Network Continuity is enabled
 * for a VvcSession tied to a given vAuth.
 */
Bool BlastSocket_IsNegotiatedNCEnabled(BlastSocketContext *blastSocketCtx, const char *vAuth);

/*
 * Function to let consumers know if Network Intelligence is enabled
 * for a VvcSession tied to a given vAuth.
 */
Bool BlastSocket_IsNegotiatedBENITEnabled(BlastSocketContext *blastSocketCtx, const char *vAuth);

/*
 * Function to set BlastSessionMgr callbacks in VVCLIB
 */
void BlastSocket_SetVvcBlastSessionMgrCallbacks(const BlastSessionMgrCallbacks *cb);

/*
 * Function to retrieve network stats and evaluate network quality from stats
 */
Bool BlastSocket_GetVvcQualityIndicators(const BlastSocketContext *blastSocketCtx,
                                         int32 vvcSessionId,
                                         VvcNetworkIndicatorStats *networkStats);

/*
 * struct BlastSocketTransportSwitchPolicyParams:
 *    Values of all struct members are read from the VMware Blast\Config
 *    registry hive and pushed by the AppBlast Layer via API -
 *    "BlastSocket_SetTransportSwitchPolicy()"
 */

typedef struct BlastSocketTransportSwitchPolicyParams {

   double transportSwitchCbPeriodMS;

   double beatToTcpBwKbps;
   double beatToTcpPktLossPercentage;
   double beatToTcpRttMS;
   double beatToTcpRttVarPercentage;

   double tcpToBeatBwKbps;
   double tcpToBeatRttVarPercentage;
   double tcpToBeatRttMS;

   uint32 beatToTcpThreshold;
   uint32 tcpToBeatThreshold;

   Bool isSwitchingAlwaysEnabled;
   int32 switchCountMax;

} BlastSocketTransportSwitchPolicyParams;

/*
 * The below API is used by AppBlast layer to push Switch Policy to BlastSockets
 */
Bool BlastSocket_SetTransportSwitchPolicy(BlastSocketContext *blastSocketCtx, const char *vAuth,
                                          const BlastSocketTransportSwitchPolicyParams *params);

/*
 * Why SetVvcQoSPolicParams is a separate API than SetSwitchPolicyParams:
 *    With BENIT Enabled: QoSPolicyParams will be sent by agent to client.
 *    With BENIT Disabled: QoSPolicy will still get applied locally.
 *
 *    Since QoSPolicy should get applied even with BENIT disabled, define
 *    a separate API for clear separation.
 */

/*
 * The below API is used by AppBlast layer to push QoS Policy to BlastSockets
 * after each connect-disconnect.
 * The MKS/vncClient pushed the QoS Policy via BlastSocket_Connect().
 */
Bool BlastSocket_SetVvcQoSPolicy(BlastSocketContext *blastSocketCtx, const char *vAuth,
                                 const VvcQoSPolicyParams *params);

/*
 * The below API is used by AppBlast layer to push VVC Deferred Acks Params
 * to BlastSockets after each connect-disconnect.
 * BlastSockets layer will then push it to Vvc via VVCLIB_OpenSession()
 *
 * A separate API is needed here because:
 * - pushing via BlastSocket_Start() means any change will require LogOff to
 *   take effect.
 */
Bool BlastSocket_SetVvcDeferredAcksParams(BlastSocketContext *blastSocketCtx, const char *vAuth,
                                          const VvcDeferredAcksParams *params);

/*
 * MKS/Client-side will be given a BlastSocketClientParams struct via
 * ConnectCb.
 *
 * isWebSocket: specifies if BEAT or TCP connection has been setup.
 * isVvcBwe: is Bwe enabled or not
 * isNCEnabled: After negotiating with Agent, the final value if
 *              NetworkContinuity is to-be enabled or not.
 * cookie: This will be saved by MKS/Client-side and will be used in doing
 *         reconnects following a network teardown.
 */

typedef struct BlastSocketClientParams {
   Bool isWebSocket;
   Bool isVvcBwe;
   Bool isNCEnabled;
   char *cookie;
} BlastSocketClientParams;

typedef struct BlastSocketClientProxyHostParams {
   /*
    * hostname represents the proxy host, this is usually the localhost,
    * could be something else in the future
    */
   char *hostname;
   /*
    * destPort is port on the remote host that the proxy should connect
    * to: this is usually passed from the View Client UI.
    */
   uint16 destPort;
   /*
    * type is the primary WebSocket killswitch/override value
    */
   ConnectionType type;
   /*
    * brokerConnectionUDPUsed - useful in BENIT to decide if we should do
    * tunnel primary upfront or not.
    */
   Bool brokerConnectionUDPUsed;
} BlastSocketClientProxyHostParams;

/*
 * The ConnectCb function pointer prototype used by
 * BlastSocket_Connect() API.
 */

typedef void (*BlastSocketClientConnectCb)(int32 vvcSessionId, BlastSocketClientParams params,
                                           void *clientData);

/*
 * The CloseCb function pointer prototype used by
 * BlastSocket_Connect() API.
 * Note that the prototype differs from the Agent-side counterpart in that
 * vAuth is needed by the application on Agent-side & not needed on Client-Side.
 */

typedef void (*BlastSocketClientCloseCb)(int32 vvcSessionID, Bool isError,
                                         VDPConnectionResult closeReason, void *clientData);

/*
 * The notifyNetworkStatsCb function pointer prototype used by
 * BlastSocket_Connect() API.
 */

typedef void (*BlastSocketClientNotifyNetworkStatsCb)(int32 vvcSessionID,
                                                      VvcNetworkIndicatorStats stats,
                                                      void *clientData);


/*
 * BlastSocketClientConnectionParams:
 *    struct to hold all the parameters required to make a Blast connection
 */

typedef struct BlastSocketClientConnectionParams {

   const char *url;
   uint16 secondaryPort;
   struct _SSLVerifyParam *sslVerifyParam;
   const char *httpProxy;
   const char *fqdn;
   const char *cookies;
   AsyncSocketConnectFlags flags;
   BlastSocketClientProxyHostParams *websocketInfo;
   FECSocketOptionsStatic *fecSocketStaticOpts;
   Bool isTCPOnlyMode;
   Bool isReconnect;
   Bool localNetworkContinuityEnabled;
   Bool localNetworkIntelligenceEnabled;
   Bool localNetworkWaitForBEATNeeded;
   Bool localVVCCloseSeqEnabled;
   Bool isVvcPluginLoaderNeeded;
   Bool localVVCPauseResumeEnabled;
   Bool localVVCQoSPolicyEnabled;
   Bool isReconnectInternalEnabled;
   VvcQoSPolicyParams qosPolicyParams;
   VvcDeferredAcksParams deferredAcksParams;
   Bool localVVCBatchingEnabled;
   Bool localTcpBweEnabled;
   Bool beatRouteReplacementEnabled;
   Bool legacyBlastWSProtocolsAllowed;
   int32 platformSessionId;
} BlastSocketClientConnectionParams;

/*
 * BlastSocketClientConnectionCallbacks:
 *
 *    Struct to hold all the callback pointers required to be given to
 *    BlastSocket_Connect() API.
 */

typedef struct BlastSocketClientConnectionCallbacks {

   BlastSocketClientConnectCb socketConnectCb;
   BlastSocketClientCloseCb socketCloseCb;

   BlastSocketClientNotifyNetworkStatsCb notifyNetworkStatsCb;

   void *clientData;

} BlastSocketClientConnectionCallbacks;

/*
 * BlastSocketClientInitParams:
 *    struct to hold all the parameters required initialize BlastSocket on Client
 */

typedef struct BlastSocketClientInitParams {

   Bool blastSocketThreadEnabled;
   Bool blastHcSvcWrapIPCNeeded;

} BlastSocketClientInitParams;


/*
 * Desktop Clients & Mobile Clients do BlastSocket_Init() & Uninit() once.
 *
 * One difference between Desktop Clients & Mobile Clients is that Mobile
 * Clients will use BlastSocket_Init() & BlastSocket_Uninit()
 * to do VVCLIB_Init() & VVCLIB_Uninit() at the start and exit Client App.
 * Whereas in case of Desktop Clients, VVCLIB_Init() & VVCLIB_Uninit() is
 * not done as part of BlastSocket_Init() and BlastSocket_Uninit().
 */

void BlastSocket_Init(BlastSocketClientInitParams params);

void BlastSocket_Uninit(void);

/*
 * BlastSocket_Connect():
 *
 * Function used by MKS/Client-Side to establish Blast Connection in "Typical"
 * mode. This API handles WebSocket Upgrade to FECSocket & also Fallback to
 * TCP if BEAT connection does not go through.
 *
 * XXX: TODO: Remove the OUT asock parameter of this API.
 *
 *  - The vncClient code expects an asock value to be filled after
 *    the call the Connect API. (ProxyConnect and/or WSConnect).
 *  - We want to to have just a 1:1 replacement of _ConnectProxySocket()
 *    with BlastSocket_Connect() without having to change vncClient much.
 *
 *    So, return a vvcSessionId, but also have an OUT
 *    parameter so that vncClient's asock can be filled up.
 *
 *    This should be cleaned up - presumably after vncClient is split
 *    into a Blast and non-Blast code-paths.
 */

int32 BlastSocket_Connect(BlastSocketClientConnectionParams params,
                          BlastSocketClientConnectionCallbacks callbacks, int *error);

/*
 * BlastSocket_Close():
 * The "Close" counterpart of BlastSocket_Connect() API.
 */

int BlastSocket_Close(int32 vvcSessionId, VDPConnectionResult closeReason);

// Query API to know about Reconnects at Blast Sockets
Bool BlastSocket_IsReconnectInternalEnabled(int32 vvcSessionId);

VDPConnectionResult BlastSocket_GetCloseReason(int32 vvcSessionId);

Bool BlastSocket_WaitForBEATNeeded(int32 vvcSessionId);

void BlastSocketNetworkQualityStatsDetect_LoadConfig(
   BlastSocketNetworkStatsConfig *networkQualityStatsConfig);

/*
 * BlastSocketClient_AuxiliaryFlowInfo exposes information
 * gleaned from a Hop-Rsp header during the establishment of
 * the Blast Primary Websocket.
 * This information may be used to construct an Auxiliary Flow
 * over BEAT to the Blast peer.
 *
 * Note: This was previously defined in lib/public/asyncProxySocket.h
 */

typedef struct {

   /*
    * afioLabel is a BEAT transit label to be used as a template
    * to derive a label for an AF connection.  Transit labels are
    * opaque 4-byte items but, following existing practice, the
    * label is encoded here as a uint32 in host byte order.
    *
    * afioDest is a nul-terminated string containing the upstream
    * destination IP address or hostname for an AF connection.  It
    * is sized to contain a maximal-length DNS FQDN.
    *
    * afioPort is the upstream destination UDP port number for an
    * AF connection, in host byte order.
    *
    * afioHMACKeyBuf is a byte array containing the HMAC key to be
    * applied to traffic sent to the upstream destination.
    *
    * afioHMACKeySize is the count of valid bytes in afioHMACKeyBuf.
    * An afioHMACKeySize of 0 indicates that no HMAC should be
    * generated in AF traffic to the upstream destination.
    *
    * afioHMACAlgo is a nul-terminated string containing the name
    * of the hash algorithm to be applied to generate an HMAC on
    * traffic sent to the upstream destination.  Algorithm names
    * are expressed in OpenSSL syntax, i.e. short strings like
    * "sha1" or "sha256".  This field is generously sized to hold
    * any likely name.
    */

   uint32 afioLabel;         // BEAT transit label in host byte order
   char afioDest[257];       // string: IP or hostname + '\0'
   uint16 afioPort;          // in host byte order, 0 for invalid
   unsigned afioHMACKeySize; // count of valid bytes in afio_HMACKey
   uint8 afioHMACKeyBuf[FEC_MAX_HMAC_KEY_SIZE];
   // byte array
   char afioHMACAlgo[33]; // string: name + '\0', eg "sha256"

} BlastSocketClient_AuxiliaryFlowInfo;

#if defined(__cplusplus)
} // extern "C"
#endif

#endif // defined BLAST_SOCKET_H
