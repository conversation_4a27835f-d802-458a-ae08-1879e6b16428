/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * vdpRdeCommonMgr.h --
 *
 */


#pragma once

#include "vm_basic_types.h"

#include <ghIntegrationCommon.h>

#ifdef __cplusplus
extern "C" {
#endif

Bool VDPRdeCommonMgr_Init(void);
Bool VDPRdeCommonMgr_Exit(void);
Bool VDPRdeCommonMgr_Broadcast(const char *name, const void *cookie, const void *data);
Bool VDPRdeCommonMgr_GHIRequestReceived(GHIChannelType channel, uint32 msgId, const char *msgName,
                                        const uint8 *msgData, uint32 msgDataLen);

#ifdef RDE_CLIENT_UNIT_TEST
Bool VDPRdeCommonMgr_OnNotificationWrapperForUT(void *context, const char *sourceToken,
                                                const void *cookie, const void *data);
#endif

#define FUNC_ENTRY Log("%s: enter\n", __FUNCTION__)
#define FUNC_EXIT Log("%s: exit\n", __FUNCTION__)

#ifdef __cplusplus
}
#endif