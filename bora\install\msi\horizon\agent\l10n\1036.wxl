﻿<?xml version="1.0" encoding="utf-8"?>

<WixLocalization Culture="fr-fr" Codepage="1252" xmlns="http://schemas.microsoft.com/wix/2006/localization">
   <String Id="LANGID">1036</String>

   <!-- Installshield Strings -->
   <String Id="IDS_COMPLUS_PROGRESSTEXT_COST">Calcul de l'espace requis pour l'application COM+ : [1]</String>
   <String Id="IDS_COMPLUS_PROGRESSTEXT_INSTALL">Installation de l'application COM+ : [1]</String>
   <String Id="IDS_COMPLUS_PROGRESSTEXT_UNINSTALL">Désinstallation de l'application COM+ : [1]</String>
   <String Id="IDS_PROGMSG_IIS_CREATEAPPPOOL">Création du pool d'applications %s</String>
   <String Id="IDS_PROGMSG_IIS_CREATEAPPPOOLS">Création de pools d'applications en cours…</String>
   <String Id="IDS_PROGMSG_IIS_CREATEVROOT">Création du répertoire virtuel IIS %s</String>
   <String Id="IDS_PROGMSG_IIS_CREATEVROOTS">Création de répertoires virtuels IIS en cours…</String>
   <String Id="IDS_PROGMSG_IIS_CREATEWEBSERVICEEXTENSION">Création d'une extension de service Web</String>
   <String Id="IDS_PROGMSG_IIS_CREATEWEBSERVICEEXTENSIONS">Création d'extensions de service Web en cours…</String>
   <String Id="IDS_PROGMSG_IIS_EXTRACT">Extraction des informations pour les répertoires virtuels IIS en cours…</String>
   <String Id="IDS_PROGMSG_IIS_EXTRACTDONE">Informations extraites pour les répertoires virtuels IIS…</String>
   <String Id="IDS_PROGMSG_IIS_REMOVEAPPPOOL">Suppression du pool d'applications</String>
   <String Id="IDS_PROGMSG_IIS_REMOVEAPPPOOLS">Suppression des pools d'applications en cours…</String>
   <String Id="IDS_PROGMSG_IIS_REMOVESITE">Suppression du site Web sur le port %d</String>
   <String Id="IDS_PROGMSG_IIS_REMOVEVROOT">Suppression du répertoire virtuel IIS %s</String>
   <String Id="IDS_PROGMSG_IIS_REMOVEVROOTS">Suppression des répertoires virtuels IIS en cours…</String>
   <String Id="IDS_PROGMSG_IIS_REMOVEWEBSERVICEEXTENSION">Suppression d'une extension de service Web</String>
   <String Id="IDS_PROGMSG_IIS_REMOVEWEBSERVICEEXTENSIONS">Suppression des extensions de service Web en cours…</String>
   <String Id="IDS_PROGMSG_IIS_ROLLBACKAPPPOOLS">Restauration des pools d'applications en cours…</String>
   <String Id="IDS_PROGMSG_IIS_ROLLBACKVROOTS">Restauration des modifications du répertoire virtuel et du site Web en cours…</String>
   <String Id="IDS_PROGMSG_IIS_ROLLBACKWEBSERVICEEXTENSIONS">Restauration des extensions de service Web en cours…</String>
   <String Id="IDS_PROGMSG_XML_COSTING">Évaluation des fichiers XML en cours…</String>
   <String Id="IDS_PROGMSG_XML_CREATE_FILE">Création du fichier XML %s en cours...</String>
   <String Id="IDS_PROGMSG_XML_FILES">Exécution des modifications du fichier XML en cours…</String>
   <String Id="IDS_PROGMSG_XML_REMOVE_FILE">Suppression du fichier XML %s en cours...</String>
   <String Id="IDS_PROGMSG_XML_ROLLBACK_FILES">Restauration des modifications du fichier XML en cours…</String>
   <String Id="IDS_PROGMSG_XML_UPDATE_FILE">Mise à jour du fichier XML %s en cours...</String>


   <!-- LaunchCondition Error Messages -->
   <String Id="MINIMUM_REQUIRED_OS">Vous ne pouvez installer ce produit que sur les systèmes d'exploitation Windows 10, Windows Server 2016 ou une version ultérieure.</String>
   <String Id="DENY_INSTALL_DOMAIN_CONTROLLER">Ce produit ne peut pas être installé sur un contrôleur de domaine.</String>
   <String Id="NEED_ADMIN">Vous devez disposer des privilèges Administrateur pour installer/désinstaller ce logiciel.</String>


   <!-- Feature Table -->
   <String Id="FEATURE_NAME_CORE">Cœur</String>
   <String Id="FEATURE_DESC_CORE">Fonctionnalité principale de [ProductName]</String>
   <String Id="FEATURE_NAME_CORRETTO">Corretto</String>
   <String Id="FEATURE_DESC_CORRETTO">Fonctionnalité principale de[ProductName] avec la distribution Corretto JDK</String>
   <String Id="FEATURE_NAME_BELLSOFT">Bellsoft</String>
   <String Id="FEATURE_DESC_BELLSOFT">Fonctionnalité principale de[ProductName] avec la distribution Bellsoft JDK</String>
   <String Id="FEATURE_NAME_RDSH3D">RDSH 3D</String>
   <String Id="FEATURE_DESC_RDSH3D">Cette fonctionnalité permet l'accélération 3D matérielle dans les sessions RDSH et PC physique.</String>
   <String Id="FEATURE_NAME_CLIENTDRIVEREDIRECTION">Redirection du lecteur client</String>
   <String Id="FEATURE_DESC_CLIENTDRIVEREDIRECTION">Autorisez les clients Horizon Client à partager des lecteurs locaux avec des applications et des postes de travail distants. Si l'installation n'a pas été effectuée, les fonctions copier/coller et glisser-déposer de fichiers et de dossiers seront désactivées.</String>
   <String Id="FEATURE_NAME_NGVC">Instant Clone Agent</String>
   <String Id="FEATURE_DESC_NGVC">Instant Clone Agent ne doit être installé que sur une machine virtuelle s'exécutant sur VMware vSphere 7.0 ou version ultérieure.</String>
   <String Id="FEATURE_DESC_PCOIP_PHYSICAL">Cette fonctionnalité installe les composants du serveur PCoIP sur votre poste de travail.</String>
   <String Id="FEATURE_NAME_RTAV">Audio/Vidéo en temps réel</String>
   <String Id="FEATURE_DESC_RTAV">La fonctionnalité Audio/Vidéo en temps réel permet aux utilisateurs de rediriger des périphériques audio et vidéo connectés localement vers le poste de travail distant pour les utiliser.</String>
   <String Id="FEATURE_NAME_VMWPRINT">Horizon Integrated Printing</String>
   <String Id="FEATURE_DESC_VMWPRINT">Redirection d'Horizon Integrated Printing.</String>

   <String Id="FEATURE_NAME_SCANNERREDIRECTION">Redirection de scanner</String>
   <String Id="FEATURE_DESC_SCANNERREDIRECTION">Active la fonctionnalité Redirection de scanner.</String>
   <String Id="FEATURE_NAME_SERIALPORTREDIRECTION">Redirection de port série</String>
   <String Id="FEATURE_DESC_SERIALPORTREDIRECTION">Active la fonctionnalité Redirection de port série.</String>
   <String Id="FEATURE_NAME_SMARTCARD">Redirection de carte à puce</String>
   <String Id="FEATURE_DESC_SMARTCARD">Active la fonctionnalité Redirection de carte à puce.</String>
   <String Id="FEATURE_NAME_TSMMR">TSMMR</String>
   <String Id="FEATURE_DESC_TSMMR">Redirection multimédia des services Terminal Server.</String>
   <String Id="FEATURE_NAME_URLREDIRECTION">Redirection de contenu URL</String>
   <String Id="FEATURE_DESC_URLREDIRECTION">Redirige le contenu URL depuis une session de serveur vers un périphérique client et inversement.</String>
   <String Id="FEATURE_NAME_UNCREDIRECTION">Redirection de chemin UNC</String>
   <String Id="FEATURE_DESC_UNCREDIRECTION">Redirige le chemin UNC d'une session de serveur vers un périphérique client et vice versa.</String>
   <String Id="FEATURE_NAME_USB">Redirection USB</String>
   <String Id="FEATURE_DESC_USB">Refer to the "Deploying USB Devices in a Secure Horizon Environment" document for guidance on using USB redirection securely.</String>
   <String Id="FEATURE_NAME_HZNVAUDIO">Audio Horizon</String>
   <String Id="FEATURE_DESC_HZNVAUDIO">Pilote audio virtuel Horizon</String>
   <String Id="FEATURE_NAME_HTML5MMR">Redirection multimédia HTML5</String>
   <String Id="FEATURE_DESC_HTML5MMR">Permet la redirection de multimédia HTML5</String>
   <String Id="FEATURE_NAME_GEOREDIR">Redirection de géolocalisation</String>
   <String Id="FEATURE_DESC_GEOREDIR">Permet la redirection de géolocalisation du client vers le poste de travail distant</String>
   <String Id="FEATURE_NAME_SDOSENSOR">Redirection du capteur d'orientation de périphérique simple</String>
   <String Id="FEATURE_DESC_SDOSENSOR">Active la fonctionnalité de redirection du capteur d'orientation de périphérique simple, signale les modifications de l'orientation de périphérique au poste de travail distant.</String>
   <String Id="FEATURE_NAME_STORAGEDRIVE">Redirection de lecteur de stockage</String>
   <String Id="FEATURE_DESC_STORAGEDRIVE">Active la redirection du lecteur de stockage du client vers le poste de travail distant.</String>
   <String Id="FEATURE_NAME_PERFTRACKER">Horizon Performance Tracker</String>
   <String Id="FEATURE_DESC_PERFTRACKER">Active Horizon Performance Tracker</String>
   <String Id="FEATURE_NAME_HYBRIDLOGON">Ouverture de session hybride</String>
   <String Id="FEATURE_DESC_HYBRIDLOGON">Active l'ouverture de session hybride qui permet à un utilisateur non authentifié d'accéder aux ressources réseau sans devoir entrer d'informations d'identification.</String>
   <String Id="FEATURE_NAME_HELPDESK">Plug-in du service d'assistance d'Horizon Agent</String>
   <String Id="FEATURE_DESC_HELPDESK">Plug-in du service d'assistance d'Horizon Agent.</String>

   <!-- Control Panel Strings -->
   <String Id="Url">https://www.omnissa.com/</String>

   <!-- Firewall Strings -->
   <String Id="BlastUDPFirewallExceptionName">Exception de trafic UDP Omnissa Horizon Blast</String>

   <!-- UI Dialog Strings -->
   <String Id="IDS__DisplayName_Custom">Personnalisé</String>
   <String Id="IDS__DisplayName_Minimal">Minimal</String>
   <String Id="IDS__DisplayName_Typical">Par défaut</String>
   <String Id="INTEL_UNS_DESC">Fournit les services de notification des utilisateurs Intel.</String>
   <String Id="IDS_LicenseAcceptance">En procédant à l'installation, vous acceptez les</String>
   <String Id="IDS_GeneralTerms">Conditions générales</String>
   <String Id="IDS_CANCEL">Annuler</String>
   <String Id="IDS_CANCEL2">&amp;Annuler</String>
   <String Id="IDS_OK">OK</String>
   <String Id="IDS_BACK">&lt; &amp;Précédent</String>
   <String Id="IDS_NEXT">&amp;Suivant &gt;</String>
   <String Id="IDS_FINISH">&amp;Terminer</String>
   <String Id="IDS__IsCancelDlg_No">&amp;Non</String>
   <String Id="IDS__IsCancelDlg_Yes">&amp;Oui</String>
   <String Id="IDS__IsAdminInstallBrowse_LookIn">&amp;Regarder dans :</String>
   <String Id="IDS__IsAdminInstallBrowse_UpOneLevel">Dossier parent</String>
   <String Id="IDS__IsAdminInstallBrowse_BrowseDestination">Sélectionnez le dossier de destination.</String>
   <String Id="IDS__IsAdminInstallBrowse_ChangeDestination">{&amp;MSSansBold8}Modification du dossier de destination actuel</String>
   <String Id="IDS__IsAdminInstallBrowse_CreateFolder">Créer un nouveau dossier</String>
   <String Id="IDS__IsAdminInstallBrowse_FolderName">&amp;Nom du dossier :</String>
   <String Id="IDS__IsAdminInstallPoint_Install">&amp;Installer</String>
   <String Id="IDS__IsAdminInstallPoint_SpecifyNetworkLocation">Spécifiez un emplacement réseau pour l'image serveur du produit.</String>
   <String Id="IDS__IsAdminInstallPoint_EnterNetworkLocation">Entrez l'emplacement réseau ou cliquez sur Modifier pour accéder à un emplacement. Cliquez sur Installer pour créer une image serveur de [ProductName] à l'emplacement réseau spécifié ou sur Annuler pour quitter l'assistant.</String>
   <String Id="IDS__IsAdminInstallPoint_NetworkLocationFormatted">{&amp;MSSansBold8}Emplacement réseau</String>
   <String Id="IDS__IsAdminInstallPoint_NetworkLocation">&amp;Emplacement réseau :</String>
   <String Id="IDS__IsAdminInstallPoint_Change">&amp;Modifier...</String>
   <String Id="IDS__IsAdminInstallPointWelcome_Wizard">{&amp;TahomaBold10}Bienvenue dans le programme d'installation pour [ProductName]</String>
   <String Id="IDS__IsAdminInstallPointWelcome_ServerImage">Le programme d'installation va créer une image serveur de [ProductName] à un emplacement réseau spécifié. Pour continuer, cliquez sur Suivant.</String>
   <String Id="ProductVersion">{&amp;Arial9}Version du produit : [ProductVersionString]</String>
   <String Id="IDS__IsCancelDlg_ConfirmCancel">Voulez-vous vraiment annuler l'installation de [ProductName] ?</String>
   <String Id="IDS__IsInstallRolesConfirmDlg_Message">Le programme d'installation va installer les rôles requis pour le système d'exploitation. Cliquez sur OK pour continuer.</String>
   <String Id="ConnectionServer_TitleDesc">Entrez l'Horizon Connection Server auquel cette machine se connecte.</String>
   <String Id="ConnectionServer_Title">{&amp;MSSansBold8}Enregistrer dans l'Horizon Connection Server</String>
   <String Id="ConnectionServer_Text">Entrez le nom d'Horizon Connection Server (instance standard ou de réplica) et les informations d'identification de connexion d'administrateur pour enregistrer cette machine dans l'Horizon Connection Server.</String>
   <String Id="ConnectionServer_ServerNote">(nom d'hôte ou adresse IP)</String>
   <String Id="ConnectionServerLogin_Text1">S'&amp;authentifier en tant qu'utilisateur actuellement connecté</String>
   <String Id="ConnectionServerLogin_Text2">Spé&amp;cifier les informations d'identification de l'administrateur</String>
   <String Id="ConnectionServerLogin_Title">Authentification :</String>
   <String Id="ConnectionServer_Username">&amp;Nom d'utilisateur :</String>
   <String Id="ConnectionServer_UsernameNote">(Domaine\Utilisateur)</String>
   <String Id="ConnectionServer_Password">&amp;Mot de passe :</String>
   <String Id="IDS__IsCustomSelectionDlg_SelectFeatures">Sélectionnez les fonctions du programme que vous voulez installer.</String>
   <String Id="IDS__IsCustomSelectionDlg_ClickFeatureIcon">Pour modifier le type d'installation d'une fonction, cliquez sur l'icône correspondante dans la liste ci-dessous.</String>
   <String Id="IDS__IsCustomSelectionDlg_CustomSetup">{&amp;MSSansBold8}Installation personnalisée</String>
   <String Id="IDS__IsCustomSelectionDlg_Change">&amp;Modifier...</String>
   <String Id="IDS__IsCustomSelectionDlg_FeatureDescription">Description de la fonction</String>
   <String Id="IDS__IsCustomSelectionDlg_InstallTo">Installer dans :</String>
   <String Id="IDS__IsCustomSelectionDlg_MultilineDescription">Description sur plusieurs lignes de l'élément sélectionné</String>
   <String Id="IDS__IsCustomSelectionDlg_FeaturePath">&lt;selected feature path&gt;</String>
   <String Id="IDS__IsCustomSelectionDlg_FeatureSize">Taille de la fonction</String>
   <String Id="IDS__IsCustomSelectionDlg_Help">&amp;Aide</String>
   <String Id="IDS__IsCustomSelectionDlg_Space">&amp;Espace</String>
   <String Id="IDS_SetupTips_CustomSetupDescription">L'installation personnalisée vous permet d'installer les fonctions de votre choix.</String>
   <String Id="IDS_SetupTips_CustomSetup">{&amp;MSSansBold8}Conseils pour l'installation personnalisée</String>
   <String Id="IDS_SetupTips_WillNotBeInstalled">Ne sera pas installé.</String>
   <String Id="IDS_SetupTips_Advertise">Sera installé lors de la première utilisation. (Disponible uniquement si la fonction prend en charge cette option.)</String>
   <String Id="IDS_SetupTips_InstallState">L'état de l'installation indique que la fonction...</String>
   <String Id="IDS_SetupTips_AllInstalledLocal">Sera entièrement installé sur le disque dur local.</String>
   <String Id="IDS_SetupTips_IconInstallState">L'icône située à côté du nom de la fonction indique l'état d'installation de celle-ci. Cliquez sur l'icône pour afficher le menu déroulant de l'état d'installation de chaque fonction.</String>
   <String Id="IDS_SetupTips_Network">Sera installé pour une exécution à partir du réseau. (Disponible uniquement si la fonction prend en charge cette option.)</String>
   <String Id="IDS_SetupTips_SubFeaturesInstalledLocal">Certaines sous-fonctions seront installées sur le disque dur local. (Disponible uniquement si la fonction comporte des sous-fonctions.)</String>
   <String Id="DesktopConfig_Subtitle">Les informations suivantes permettent de configurer la fonctionnalité de poste de travail Horizon</String>
   <String Id="DesktopConfig_Title">{&amp;MSSansBold8}Configuration du système d'exploitation du poste de travail</String>
   <String Id="DesktopConfig_Text">Sélectionnez le mode de [ProductName] sur ce système d'exploitation :</String>
   <String Id="DesktopConfig_RDSHMode">Le rôle d'hôte de session Bureau à distance (RDSH) requis n'est pas installé sur ce système d'exploitation.

Cliquez sur Suivant pour installer les rôles/fonctionnalités requis. Le système d'exploitation devra être redémarré une fois terminé.

Après le redémarrage, le programme d'installation de [ProductName] devra être relancé pour continuer à l'installer en mode RDS.</String>
   <String Id="DesktopConfig_DesktopMode">Cet agent sera configuré en mode VDI de poste de travail.</String>
   <String Id="DesktopConfig_InstallingRolesSuccess">L'installation des rôles/fonctionnalités de système d'exploitation requis a réussi.
Redémarrez le système d'exploitation et relancez le programme d'installation de [ProductName].</String>
   <String Id="DesktopConfig_InstallingRolesFail">Erreur : le programme d'installation n'a pas pu installer les rôles/fonctionnalités de système d'exploitation requis.</String>
   <String Id="IDS__IsDesktopConfigDlg_RDSMode">Mode RDS</String>
   <String Id="IDS__IsDesktopConfigDlg_DesktopMode">Mode de poste de travail</String>
   <String Id="InstallRolesConfirm_InstallingRoles">Patientez pendant que les rôles/fonctionnalités requis sont configurés sur ce système…</String>
   <String Id="IDS__IsFeatureDetailsDlg_DiskSpaceRequirements">{&amp;MSSansBold8}Espace disque requis</String>
   <String Id="IDS__IsFeatureDetailsDlg_SpaceRequired">Espace disque requis pour l'installation des fonctions sélectionnées.</String>
   <String Id="IDS__IsFeatureDetailsDlg_VolumesTooSmall">L'espace disque des volumes mis en surbrillance est insuffisant pour permettre l'installation des fonctions actuellement sélectionnées. Vous pouvez supprimer des fichiers sur les volumes mis en surbrillance, installer moins de fonctions sur les lecteurs locaux ou sélectionner d'autres lecteurs de destination.</String>
   <String Id="IDS__IsFilesInUse_Retry">&amp;Réessayer</String>
   <String Id="IDS__IsFilesInUse_Ignore">&amp;Ignorer</String>
   <String Id="IDS__IsFilesInUse_Exit">&amp;Quitter</String>
   <String Id="IDS__IsFilesInUse_FilesInUse">{&amp;MSSansBold8}Fichiers utilisés</String>
   <String Id="IDS__IsFilesInUse_FilesInUseMessage">Certains fichiers devant être mis à jour sont en cours d'utilisation.</String>
   <String Id="IDS__IsFilesInUse_ApplicationsUsingFiles">Les applications suivantes utilisent des fichiers qui doivent être mis à jour par cette installation. Fermez ces applications et cliquez sur Réessayer pour continuer.

Remarque : si vous voyez [ProductName] dans la liste suivante, cliquez sur Ignorer pour continuer.</String>
   <String Id="IDS__IsBrowseFolderDlg_LookIn">&amp;Regarder dans :</String>
   <String Id="IDS__IsBrowseFolderDlg_UpOneLevel">Dossier parent</String>
   <String Id="IDS__IsBrowseFolderDlg_BrowseDestFolder">Sélectionnez le dossier de destination.</String>
   <String Id="IDS__IsBrowseFolderDlg_ChangeCurrentFolder">{&amp;MSSansBold8}Modification du dossier de destination actuel</String>
   <String Id="IDS__IsBrowseFolderDlg_CreateFolder">Créer un nouveau dossier</String>
   <String Id="IDS__IsBrowseFolderDlg_FolderName">&amp;Nom du dossier :</String>
   <String Id="IDS__IsWelcomeDlg_WelcomeProductName">{&amp;TahomaBold10}Bienvenue dans l'assistant d'installation de [ProductName]</String>
   <String Id="IDS__IsWelcomeDlg_InstallProductName">L'assistant d'installation va installer [ProductName] sur votre ordinateur. Pour continuer, cliquez sur Suivant.</String>
   <String Id="InstallWelcome_UpgradeLine1">L'assistant d'installation va mettre à niveau [ProductName] sur votre ordinateur. Pour continuer, cliquez sur Suivant.</String>
   <String Id="IDS__IsWelcomeDlg_WarningCopyright">Copyright © [CopyrightYears] Omnissa. Tous droits réservés. Ce produit est protégé par des lois sur le droit d'auteur et la propriété intellectuelle aux États-Unis et dans d'autres pays, ainsi que par des traités internationaux. « Omnissa » fait référence à Omnissa, LLC, Omnissa International Unlimited Company et/ou à leurs filiales.</String>
   <String Id="IpProtocolConfig_DlgDesc">Sélectionner le protocole de communication</String>
   <String Id="IpProtocolConfig_DlgTitle">{&amp;MSSansBold8}Configuration du protocole réseau</String>
   <String Id="GoldenImage_DlgDesc">Select whether this machine will be used as a Golden Image</String>
   <String Id="GoldenImage_DlgTitle">{&amp;MSSansBold8}Golden Image Selection</String>
   <String Id="GoldenImage_CheckBoxText">This machine will be used as a Golden Image</String>
   <String Id="ConnectionServer_IpText">Spécifiez le protocole à utiliser pour configurer cette instance d'Horizon Agent :</String>
   <String Id="ConnectionServer_IPv4Desc">Cet agent sera configuré afin de choisir le protocole IPv4 pour l'établissement de toutes les connexions.</String>
   <String Id="ConnectionServer_IPv6Desc">Cet agent sera configuré afin de choisir le protocole IPv6 pour l'établissement de toutes les connexions.</String>
   <String Id="ConnectionServer_Dual4Desc">Cet agent sera configuré afin de prendre en charge des modes IP mixtes en préférant le protocole IPv4 pour l'établissement de toutes les connexions.</String>
   <String Id="ConnectionServer_Dual6Desc">Cet agent sera configuré afin de prendre en charge des modes IP mixtes en préférant le protocole IPv6 pour l'établissement de toutes les connexions.</String>
   <String Id="IpProtocolConfig_FipsText">Spécifiez si ce produit doit être installé ou non avec le chiffrement conforme à FIPS.</String>
   <String Id="IpProtocolConfig_FipsDisabledDesc">Cette instance d'agent fonctionnera sans conformité FIPS.</String>
   <String Id="IpProtocolConfig_FipsEnabledDesc">Cette instance d'agent sera configurée pour la cryptographie conforme à FIPS.</String>
   <String Id="FipsConfig_Disabled">Désactivé</String>
   <String Id="FipsConfig_Enabled">Activé</String>
   <String Id="IDS__AgreeToLicense_0">Je n'&amp;accepte pas les conditions générales</String>
   <String Id="IDS__AgreeToLicense_1">J'&amp;accepte les conditions générales</String>
   <String Id="IDS__IsLicenseDlg_ReadLicenseAgreement">Lisez attentivement les conditions générales suivantes.</String>
   <String Id="IDS__IsLicenseDlg_LicenseAgreement">{&amp;MSSansBold8}Conditions générales</String>
   <String Id="IDS__IsMaintenanceDlg_Modify">{&amp;MSSansBold8}&amp;Modifier</String>
   <String Id="IDS__IsMaintenanceDlg_Repair">{&amp;MSSansBold8}&amp;Réparer</String>
   <String Id="IDS__IsMaintenanceDlg_Remove">{&amp;MSSansBold8}S&amp;upprimer</String>
   <String Id="IDS__IsMaintenanceDlg_MaitenanceOptions">Modifiez, réparez ou supprimez le programme.</String>
   <String Id="IDS__IsMaintenanceDlg_ProgramMaintenance">{&amp;MSSansBold8}Maintenance du programme</String>
   <String Id="IDS__IsMaintenanceDlg_ModifyMessage">Permet aux utilisateurs de modifier les fonctionnalités installées.</String>
   <String Id="IDS__IsMaintenanceDlg_RepairMessage">Réparez les erreurs d'installation du programme. Cette option répare les fichiers, raccourcis ou entrées de registre endommagés ou manquants.</String>
   <String Id="IDS__IsMaintenanceDlg_RemoveProductName">Supprimez [ProductName] de votre ordinateur.</String>
   <String Id="IDS__IsMaintenanceWelcome_WizardWelcome">{&amp;TahomaBold10}Bienvenue dans le programme d'installation pour [ProductName]</String>
   <String Id="IDS__IsMaintenanceWelcome_MaintenanceOptionsDescription">Le programme d'installation permet de modifier, de réparer ou de supprimer [ProductName]. Pour continuer, cliquez sur Suivant.</String>
   <String Id="IDS_PRODUCTNAME_INSTALLSHIELD">Assistant d'installation de [ProductName]</String>
   <String Id="IDS__IsMsiRMFilesInUse_CloseRestart">Fermez automatiquement les applications et essayez de les relancer.</String>
   <String Id="IDS__IsMsiRMFilesInUse_RebootAfter">Ne fermez pas les applications. (Un redémarrage sera nécessaire.)</String>
   <String Id="IDS__IsMsiRMFilesInUse_ApplicationsUsingFiles">Les applications suivantes utilisent des fichiers qui doivent être mis à jour par ce programme d'installation.</String>
   <String Id="IDS__IsDiskSpaceDlg_OutOfDiskSpace">{&amp;MSSansBold8}Espace disque insuffisant</String>
   <String Id="IDS__IsDiskSpaceDlg_DiskSpace">L'espace disque disponible est insuffisant pour l'installation.</String>
   <String Id="IDS__IsDiskSpaceDlg_HighlightedVolumes">L'espace disque disponible sur les volumes en surbrillance est insuffisant pour les fonctions sélectionnées. Vous pouvez supprimer des fichiers des volumes en surbrillance, ou annuler l'installation.</String>
   <String Id="RdpChoice_EnableRdp">&amp;Activer la fonction de poste de travail à distance sur cet ordinateur</String>
   <String Id="RdpChoice_NoRdp">&amp;Ne pas activer la fonction de poste de travail à distance sur ordinateur</String>
   <String Id="RdpConfig_Subtitle">Les informations suivantes servent à configurer la fonction de poste de travail à distance</String>
   <String Id="RdpConfig_Title">{&amp;MSSansBold8}Configuration du protocole de poste de travail à distance</String>
   <String Id="RdpConfig_Text">[ProductName] requiert que la prise en charge du poste de travail à distance soit activée. Des exceptions de pare-feu seront ajoutées pour le port RDP [RDP_PORT_NUMBER] et pour le canal View Framework [FRAMEWORK_CHANNEL_PORT]. Que souhaitez-vous faire ?</String>
   <String Id="IDS__IsVerifyReadyDlg_Install">&amp;Installer</String>
   <String Id="IDS__IsVerifyReadyDlg_WizardReady">L'assistant est prêt à démarrer l'installation.</String>
   <String Id="ReadyToInstall_RdshNote">REMARQUE : le rôle RDS n'est pas activé sur ce système d'exploitation. [ProductName] ne prend en charge que les connexions de poste de travail uniques.</String>
   <String Id="IDS__IsVerifyReadyDlg_ClickInstall">Cliquez sur Installer pour démarrer l'installation ou sur Annuler pour quitter l'assistant.</String>
   <String Id="IDS__IsVerifyReadyDlg_ReadyRepair">{&amp;MSSansBold8}Prêt à réparer le programme</String>
   <String Id="IDS__IsVerifyReadyDlg_ReadyInstall">{&amp;MSSansBold8}Prêt à installer le programme</String>
   <String Id="ReadyToInstall_InstallDir">[ProductName] sera installé dans :

[INSTALLDIR]</String>
   <String Id="ReadyToInstall_MsgSanPolicy_NGVC">REMARQUE : la stratégie VDS SAN sera définie sur « Mettre en ligne tous les disques » comme l'exige la fonctionnalité Instant Clone Agent (NGVC).</String>
   <String Id="IDS__IsVerifyRemoveAllDlg_Remove">&amp;Supprimer</String>
   <String Id="IDS__IsVerifyRemoveAllDlg_ChoseRemoveProgram">Vous avez choisi de supprimer le programme de votre système.</String>
   <String Id="IDS__IsVerifyRemoveAllDlg_ClickRemove">Cliquez sur Supprimer pour supprimer [ProductName] de votre ordinateur. Après la suppression, ce programme ne sera plus disponible.</String>
   <String Id="IDS__IsVerifyRemoveAllDlg_ClickBack">Si vous voulez vérifier ou modifier un paramètre, cliquez sur Précédent.</String>
   <String Id="IDS__IsVerifyRemoveAllDlg_RemoveProgram">{&amp;MSSansBold8}Suppression du programme</String>
   <String Id="IDS__IsFatalError_NotModified">Votre système n'a pas été modifié. Pour terminer l'installation ultérieurement, exécutez le programme d'installation à nouveau.</String>
   <String Id="IDS__IsFatalError_ClickFinish">Cliquez sur Terminer pour quitter l'assistant.</String>
   <String Id="IDS__IsFatalError_KeepOrRestore">Vous pouvez conserver les éléments installés sur votre système pour continuer cette installation ultérieurement ou restaurer votre système dans son état initial précédant l'installation.</String>
   <String Id="IDS__IsFatalError_RestoreOrContinueLater">Cliquez sur Restaurer ou Continuer ultérieurement pour quitter l'assistant.</String>
   <String Id="IDS__IsFatalError_WizardCompleted">{&amp;TahomaBold10}Fin du programme d'installation</String>
   <String Id="IDS__IsFatalError_WizardInterrupted">L'assistant a été interrompu avant la fin de l'installation de [ProductName].</String>
   <String Id="IDS__IsFatalError_UninstallWizardInterrupted">L'assistant a été interrompu avant la fin de la désinstallation de [ProductName].</String>
   <String Id="IDS__IsExitDialog_WizardCompleted">{&amp;TahomaBold10}Fin du programme d'installation</String>
   <String Id="IDS__IsExitDialog_InstallSuccess">Le programme d'installation a correctement installé [ProductName]. Cliquez sur Terminer pour quitter l'assistant.</String>
   <String Id="IDS__IsExitDialog_UninstallSuccess">Le programme d'installation a correctement désinstallé [ProductName]. Cliquez sur Terminer pour quitter l'assistant.</String>
   <String Id="IDS__IsExitDialog_InstallingRolesSuccess">Le programme d'installation a correctement configuré le système d'exploitation avec les rôles/fonctionnalités requis pour l'installation de [ProductName] en mode RDS.

Cliquez sur Terminer pour quitter l'assistant.</String>
   <String Id="IDS__IsErrorDlg_InstallerInfo">Informations du programme d'installation [ProductName]</String>
   <String Id="IDS__IsErrorDlg_Abort">&amp;Abandonner</String>
   <String Id="IDS__IsErrorDlg_Yes">&amp;Oui</String>
   <String Id="IDS__IsErrorDlg_No">&amp;Non</String>
   <String Id="IDS__IsErrorDlg_Ignore">&amp;Ignorer</String>
   <String Id="IDS__IsErrorDlg_OK">&amp;OK</String>
   <String Id="IDS__IsErrorDlg_Retry">&amp;Réessayer</String>
   <String Id="IDS__IsInitDlg_WelcomeWizard">{&amp;TahomaBold10}Bienvenue dans le programme d'installation pour [ProductName]</String>
   <String Id="IDS__IsInitDlg_PreparingWizard">L'installation de [ProductName] prépare le programme d'installation qui va vous guider tout au long du processus d'installation du programme. Veuillez patienter.</String>
   <String Id="IDS__IsUserExit_NotModified">Votre système n'a pas été modifié. Pour installer ce programme ultérieurement, réexécutez le programme d'installation.</String>
   <String Id="IDS__IsUserExit_ClickFinish">Cliquez sur Terminer pour quitter l'assistant.</String>
   <String Id="IDS__IsUserExit_KeepOrRestore">Vous pouvez conserver les éléments installés sur votre système pour continuer cette installation ultérieurement ou restaurer votre système dans son état initial précédant l'installation.</String>
   <String Id="IDS__IsUserExit_RestoreOrContinue">Cliquez sur Restaurer ou Continuer ultérieurement pour quitter l'assistant.</String>
   <String Id="IDS__IsUserExit_WizardCompleted">{&amp;TahomaBold10}Fin du programme d'installation</String>
   <String Id="IDS__IsUserExit_WizardInterrupted">L'assistant a été interrompu avant la fin de l'installation de [ProductName].</String>
   <String Id="IDS__IsUserExit_UninstallWizardInterrupted">L'assistant a été interrompu avant la fin de la désinstallation de [ProductName].</String>
   <String Id="IDS__IsProgressDlg_UninstallingFeatures2">Les fonctions de programme que vous avez sélectionnées sont en cours d'installation.</String>
   <String Id="IDS__IsProgressDlg_UninstallingFeatures">Les fonctions du programme que vous avez sélectionnées sont en cours de désinstallation.</String>
   <String Id="IDS__IsProgressDlg_WaitUninstall2">Patientez pendant que le programme d'installation installe [ProductName]. Cela peut prendre quelques minutes.</String>
   <String Id="IDS__IsProgressDlg_WaitUninstall">Patientez pendant que le programme d'installation désinstalle [ProductName]. Cela peut prendre quelques minutes.</String>
   <String Id="IDS__IsProgressDlg_InstallingProductName">{&amp;MSSansBold8}Installation de [ProductName]</String>
   <String Id="IDS__IsProgressDlg_Uninstalling">{&amp;MSSansBold8}Désinstallation de [ProductName]</String>
   <String Id="IDS__IsProgressDlg_SecHidden">(Masqué pour l'instant)S.</String>
   <String Id="IDS__IsProgressDlg_Status">État :</String>
   <String Id="IDS__IsProgressDlg_Hidden">(Masqué pour l'instant)</String>
   <String Id="IDS__IsProgressDlg_HiddenTimeRemaining">(Masqué pour l'instant)Temps restant estimé :</String>
   <String Id="IDS__IsProgressDlg_ProgressDone">État d'avancement</String>
   <String Id="IDS__IsResumeDlg_Resuming">{&amp;TahomaBold10}Reprise du programme d'installation pour [ProductName]</String>
   <String Id="IDS__IsResumeDlg_ResumeSuspended">Le programme d'installation va terminer l'installation interrompue de [ProductName] sur votre ordinateur. Pour continuer, cliquez sur Suivant.</String>
   <String Id="IDS__IsResumeDlg_WizardResume">Le programme d'installation va terminer l'installation de [ProductName] sur votre ordinateur. Pour continuer, cliquez sur Suivant.</String>


   <!-- Error Strings -->
   <String Id="MsgWSWCInstalled">Impossible de continuer l'installation, car une version incompatible d'Horizon Client est déjà installée sur cet ordinateur. Pour continuer l'installation d'[ProductName], désinstallez Horizon Client et réexécutez ce programme d'installation.</String>
   <String Id="MsgClientRunning">L'installation de l'agent ne peut pas continuer. Une session active d'Horizon Client a été détectée.</String>
   <String Id="MsgDowngradeDetected">Le programme d'installation a détecté qu'une version plus récente de [ProductName] était déjà installée.</String>
   <String Id="MsgManualUninstallRequired">Ce programme d'installation ne peut pas effectuer une mise à niveau par-dessus l'installation existante du produit. Désinstallez le produit existant avant de poursuivre cette installation.</String>
   <String Id="MsgMustReboot">Vous devez redémarrer le système avant de pouvoir poursuivre l'installation.</String>
   <String Id="MsgServerInstalled">Impossible de continuer l'installation, car Horizon Connection Server est déjà installé sur cet ordinateur. Pour continuer l'installation d'[ProductName], désinstallez le Serveur de connexion et réexécutez ce programme d'installation.</String>
   <String Id="MsgUnsupportedOldVersion">Installation impossible parce qu'une version plus ancienne non pris en charge de ce produit est déjà installée. Désinstallez-la et redémarrez le système avant d'installer ce produit.</String>
   <String Id="MsgUrlRedirectionInstalled">Vous tentez d'installer [ProductName] avec la redirection URL activée, mais la redirection URL est déjà activée par Horizon Client. Cette fonctionnalité n'est pas prise en charge. Si vous continuez, l'agent sera installé sans la redirection URL. Vous devez d'abord désinstaller le client et installer l'agent pour utiliser la redirection d'URL en mode Agent.</String>
   <String Id="MsgUNCRedirectionInstalled">Vous tentez d'installer [ProductName] avec la redirection UNC activée, mais celle-ci est déjà activée par Horizon Client. Cette fonctionnalité n'est pas prise en charge. Si vous continuez, l'agent sera installé sans la redirection UNC. Vous devez d'abord désinstaller le client et installer l'agent pour utiliser la redirection UNC en mode Agent.</String>
   <String Id="MsgVdmLoopbackIp">Une erreur s'est produite lors d'une tentative d'établissement de l'adresse IP 'localhost'. Assurez-vous d'avoir sélectionné un protocole IP et que ce dernier est installé sur cet ordinateur.</String>
   <String Id="MsgWindowsUpdateInProgress">Une instance de Windows Update est en cours d'exécution. Terminez-la et redémarrez le système avant d'installer Horizon Agent.</String>
   <String Id="MsgWindowsUpdateAndRestartPending">Vous devez mettre à jour et redémarrer le système pour poursuivre l'installation.</String>
   <String Id="NoRepairAllowed">Une session active d'Horizon est en cours. La réparation de [ProductName] ne peut pas continuer.</String>
   <String Id="MsgInstallationAbortifSVIInstalled">Ce programme d'installation ne peut pas effectuer une mise à niveau sur l'installation de produit existante. La fonctionnalité Horizon View Composer n'est plus prise en charge à partir de la version 8.1. Pour installer cette build, désinstallez d'abord la build précédente.</String>
   <String Id="SettingsFileInvalid">Échec de l'analyse du fichier de paramètres du programme d'installation : « [SETTINGS_FILE] »

Erreur à la ligne [SettingsFileErrorLine].</String>


   <!-- Action Text Strings -->
   <String Id="ActionText_RdpConfig">Exécution de la configuration RDP</String>
   <String Id="ConfigUserInit">Inscription du processus UserInit : wssm.exe</String>
   <String Id="IDS_ACTIONTEXT_1">[1]</String>
   <String Id="IDS_ACTIONTEXT_1b">[1]</String>
   <String Id="IDS_ACTIONTEXT_1c">[1]</String>
   <String Id="IDS_ACTIONTEXT_1d">[1]</String>
   <String Id="IDS_ACTIONTEXT_Advertising">Publication d'informations sur l'application</String>
   <String Id="IDS_ACTIONTEXT_AllocatingRegistry">Allocation d'espace dans le registre</String>
   <String Id="IDS_ACTIONTEXT_AppCommandLine">Application : [1], Ligne de commande : [2]</String>
   <String Id="IDS_ACTIONTEXT_AppId">Identificateur de l'application : [1]{{, Type d'application [2]}}</String>
   <String Id="IDS_ACTIONTEXT_AppIdAppTypeRSN">Identificateur de l'application : [1]{{, Type d'application [2], Utilisateurs [3], RSN [4]}}</String>
   <String Id="IDS_ACTIONTEXT_Application">Application : [1]</String>
   <String Id="IDS_ACTIONTEXT_BindingExes">Liaison des exécutables</String>
   <String Id="IDS_ACTIONTEXT_ClassId">ID de classe : [1]</String>
   <String Id="IDS_ACTIONTEXT_ClsID">ID de classe : [1]</String>
   <String Id="IDS_ACTIONTEXT_ComponentIDQualifier">ID du composant : [1], Qualificateur : [2]</String>
   <String Id="IDS_ACTIONTEXT_ComponentIdQualifier2">ID du composant : [1], Qualificateur : [2]</String>
   <String Id="IDS_ACTIONTEXT_ComputingSpace">Calcul de l'espace nécessaire</String>
   <String Id="IDS_ACTIONTEXT_ComputingSpace2">Calcul de l'espace nécessaire</String>
   <String Id="IDS_ACTIONTEXT_ComputingSpace3">Calcul de l'espace nécessaire</String>
   <String Id="IDS_ACTIONTEXT_ContentTypeExtension">Type de contenu MIME : [1], Extension : [2]</String>
   <String Id="IDS_ACTIONTEXT_ContentTypeExtension2">Type de contenu MIME : [1], Extension : [2]</String>
   <String Id="IDS_ACTIONTEXT_CopyingNetworkFiles">Copie de fichiers sur le réseau</String>
   <String Id="IDS_ACTIONTEXT_CopyingNewFiles">Copie de nouveaux fichiers</String>
   <String Id="IDS_ACTIONTEXT_CreatingDuplicate">Création de fichiers en double</String>
   <String Id="IDS_ACTIONTEXT_CreatingFolders">Création de dossiers</String>
   <String Id="IDS_ACTIONTEXT_CreatingShortcuts">Création de raccourcis</String>
   <String Id="IDS_ACTIONTEXT_DeletingServices">Suppression de services</String>
   <String Id="IDS_ACTIONTEXT_EnvironmentStrings">Mise à jour des chaînes d'environnement</String>
   <String Id="IDS_ACTIONTEXT_EvaluateLaunchConditions">Évaluation des conditions de lancement</String>
   <String Id="IDS_ACTIONTEXT_Extension">Extension : [1]</String>
   <String Id="IDS_ACTIONTEXT_Extension2">Extension : [1]</String>
   <String Id="IDS_ACTIONTEXT_Feature">Fonction : [1]</String>
   <String Id="IDS_ACTIONTEXT_FeatureColon">Fonction : [1]</String>
   <String Id="IDS_ACTIONTEXT_File">Fichier : [1]</String>
   <String Id="IDS_ACTIONTEXT_File2">Fichier : [1]</String>
   <String Id="IDS_ACTIONTEXT_FileDependencies">Fichier : [1],  Dépendances : [2]</String>
   <String Id="IDS_ACTIONTEXT_FileDir">Fichier : [1], Répertoire : [9]</String>
   <String Id="IDS_ACTIONTEXT_FileDir2">Fichier : [1], Répertoire : [9]</String>
   <String Id="IDS_ACTIONTEXT_FileDir3">Fichier : [1], Répertoire : [9]</String>
   <String Id="IDS_ACTIONTEXT_FileDirSize">Fichier : [1], Répertoire : [9], Taille : [6]</String>
   <String Id="IDS_ACTIONTEXT_FileDirSize2">Fichier : [1], Répertoire : [9], Taille : [6]</String>
   <String Id="IDS_ACTIONTEXT_FileDirSize3">Fichier : [1], Répertoire : [9], Taille : [6]</String>
   <String Id="IDS_ACTIONTEXT_FileDirSize4">Fichier : [1], Répertoire : [2], Taille : [3]</String>
   <String Id="IDS_ACTIONTEXT_FileDirectorySize">Fichier : [1], Répertoire : [9], Taille : [6]</String>
   <String Id="IDS_ACTIONTEXT_FileFolder">Fichier : [1], Dossier : [2]</String>
   <String Id="IDS_ACTIONTEXT_FileFolder2">Fichier : [1], Dossier : [2]</String>
   <String Id="IDS_ACTIONTEXT_FileSectionKeyValue">Fichier : [1], Section : [2], Clé : [3], Valeur : [4]</String>
   <String Id="IDS_ACTIONTEXT_FileSectionKeyValue2">Fichier : [1], Section : [2], Clé : [3], Valeur : [4]</String>
   <String Id="IDS_ACTIONTEXT_Folder">Dossier : [1]</String>
   <String Id="IDS_ACTIONTEXT_Folder1">Dossier : [1]</String>
   <String Id="IDS_ACTIONTEXT_Font">Police : [1]</String>
   <String Id="IDS_ACTIONTEXT_Font2">Police : [1]</String>
   <String Id="IDS_ACTIONTEXT_FoundApp">Application trouvée : [1]</String>
   <String Id="IDS_ACTIONTEXT_FreeSpace">Espace libre : [1]</String>
   <String Id="IDS_ACTIONTEXT_GeneratingScript">Génération de scripts pour l'action :</String>
   <String Id="IDS_ACTIONTEXT_InitializeODBCDirs">Initialisation des répertoires ODBC</String>
   <String Id="IDS_ACTIONTEXT_InstallODBC">Installation des composants ODBC</String>
   <String Id="IDS_ACTIONTEXT_InstallServices">Installation de nouveaux services</String>
   <String Id="IDS_ACTIONTEXT_InstallingSystemCatalog">Installation du catalogue système</String>
   <String Id="IDS_ACTIONTEXT_KeyName">Clé : [1], Nom : [2]</String>
   <String Id="IDS_ACTIONTEXT_KeyNameValue">Clé : [1], Nom : [2], Valeur : [3]</String>
   <String Id="IDS_ACTIONTEXT_LibId">Identificateur de la bibliothèque : [1]</String>
   <String Id="IDS_ACTIONTEXT_Libid2">Identificateur de la bibliothèque : [1]</String>
   <String Id="IDS_ACTIONTEXT_MigratingFeatureStates">Migration des états des fonctions à partir des applications associées</String>
   <String Id="IDS_ACTIONTEXT_MovingFiles">Déplacement des fichiers</String>
   <String Id="IDS_ACTIONTEXT_NameValueAction">Nom : [1], Valeur : [2], Action [3]</String>
   <String Id="IDS_ACTIONTEXT_NameValueAction2">Nom : [1], Valeur : [2], Action [3]</String>
   <String Id="IDS_ACTIONTEXT_PatchingFiles">Exécution du correctif sur les fichiers</String>
   <String Id="IDS_ACTIONTEXT_ProgID">Identificateur du programme : [1]</String>
   <String Id="IDS_ACTIONTEXT_ProgID2">Identificateur du programme : [1]</String>
   <String Id="IDS_ACTIONTEXT_PropertySignature">Propriété : [1], Signature : [2]</String>
   <String Id="IDS_ACTIONTEXT_PublishProductFeatures">Publication des fonctions du produit</String>
   <String Id="IDS_ACTIONTEXT_PublishProductInfo">Publication des informations sur le produit</String>
   <String Id="IDS_ACTIONTEXT_PublishingQualifiedComponents">Publication des composants qualifiés</String>
   <String Id="IDS_ACTIONTEXT_RegUser">Inscription de l'utilisateur</String>
   <String Id="IDS_ACTIONTEXT_RegisterClassServer">Inscription des serveurs de classe</String>
   <String Id="IDS_ACTIONTEXT_RegisterExtensionServers">Inscription des serveurs d'extension</String>
   <String Id="IDS_ACTIONTEXT_RegisterFonts">Inscription des polices</String>
   <String Id="IDS_ACTIONTEXT_RegisterMimeInfo">Inscription des informations MIME</String>
   <String Id="IDS_ACTIONTEXT_RegisterTypeLibs">Inscription des bibliothèques de types</String>
   <String Id="IDS_ACTIONTEXT_RegisteringComPlus">Inscription des applications et composants COM+</String>
   <String Id="IDS_ACTIONTEXT_RegisteringModules">Inscription des modules</String>
   <String Id="IDS_ACTIONTEXT_RegisteringProduct">Inscription du produit</String>
   <String Id="IDS_ACTIONTEXT_RegisteringProgIdentifiers">Inscription des identificateurs de programmes</String>
   <String Id="IDS_ACTIONTEXT_RemoveApps">Suppression d'applications</String>
   <String Id="IDS_ACTIONTEXT_RemovingBackup">Suppression des fichiers de sauvegarde</String>
   <String Id="IDS_ACTIONTEXT_RemovingDuplicates">Suppression des fichiers en double</String>
   <String Id="IDS_ACTIONTEXT_RemovingFiles">Suppression de fichiers</String>
   <String Id="IDS_ACTIONTEXT_RemovingFolders">Suppression de dossiers</String>
   <String Id="IDS_ACTIONTEXT_RemovingIni">Suppression des entrées de fichier INI</String>
   <String Id="IDS_ACTIONTEXT_RemovingMoved">Suppression des fichiers déplacés</String>
   <String Id="IDS_ACTIONTEXT_RemovingODBC">Suppression des composants ODBC</String>
   <String Id="IDS_ACTIONTEXT_RemovingRegistry">Suppression des valeurs du registre système</String>
   <String Id="IDS_ACTIONTEXT_RemovingShortcuts">Suppression des raccourcis</String>
   <String Id="IDS_ACTIONTEXT_RollingBack">Restauration de l'action :</String>
   <String Id="IDS_ACTIONTEXT_SearchForRelated">Recherche d'applications associées</String>
   <String Id="IDS_ACTIONTEXT_SearchInstalled">Recherche des applications installées</String>
   <String Id="IDS_ACTIONTEXT_SearchingQualifyingProducts">Recherche des produits qualifiants</String>
   <String Id="IDS_ACTIONTEXT_ServerConfig">Configuration d'Horizon Connection Server</String>
   <String Id="IDS_ACTIONTEXT_Service">Service : [1]</String>
   <String Id="IDS_ACTIONTEXT_Service2">Service : [2]</String>
   <String Id="IDS_ACTIONTEXT_Service3">Service : [1]</String>
   <String Id="IDS_ACTIONTEXT_Service4">Service : [1]</String>
   <String Id="IDS_ACTIONTEXT_Shortcut">Raccourci : [1]</String>
   <String Id="IDS_ACTIONTEXT_Shortcut1">Raccourci : [1]</String>
   <String Id="IDS_ACTIONTEXT_StartingServices">Démarrage des services</String>
   <String Id="IDS_ACTIONTEXT_StoppingServices">Arrêt des services</String>
   <String Id="IDS_ACTIONTEXT_UnpublishProductFeatures">Annulation de la publication des fonctions du produit</String>
   <String Id="IDS_ACTIONTEXT_UnpublishQualified">Annulation de la publication des composants qualifiés</String>
   <String Id="IDS_ACTIONTEXT_UnpublishingProductInfo">Annulation de la publication des informations sur le produit</String>
   <String Id="IDS_ACTIONTEXT_UnregTypeLibs">Annulation de l'inscription des bibliothèques de types</String>
   <String Id="IDS_ACTIONTEXT_UnregisterClassServers">Annulation de l'inscription des serveurs de classe</String>
   <String Id="IDS_ACTIONTEXT_UnregisterExtensionServers">Annulation de l'inscription des serveurs d'extension</String>
   <String Id="IDS_ACTIONTEXT_UnregisterModules">Annulation de l'inscription des modules</String>
   <String Id="IDS_ACTIONTEXT_UnregisteringComPlus">Annulation de l'inscription des applications et composants COM+</String>
   <String Id="IDS_ACTIONTEXT_UnregisteringFonts">Annulation de l'inscription des polices</String>
   <String Id="IDS_ACTIONTEXT_UnregisteringMimeInfo">Annulation de l'inscription des informations MIME</String>
   <String Id="IDS_ACTIONTEXT_UnregisteringProgramIds">Annulation de l'inscription des identificateurs de programmes</String>
   <String Id="IDS_ACTIONTEXT_UpdateComponentRegistration">Mise à jour de l'inscription des composants</String>
   <String Id="IDS_ACTIONTEXT_UpdateEnvironmentStrings">Mise à jour des chaînes d'environnement</String>
   <String Id="IDS_ACTIONTEXT_Validating">Validation d'installation en cours</String>
   <String Id="IDS_ACTIONTEXT_BlastUDPInstall">Configuration des paramètres de communication UDP</String>
   <String Id="IDS_ACTIONTEXT_BlastUDPUninstall">Nettoyage des paramètres de communication UDP</String>
   <String Id="IDS_ACTIONTEXT_WritingINI">Écriture des valeurs des fichiers INI</String>
   <String Id="IDS_ACTIONTEXT_WritingRegistry">Écriture des valeurs de registre système</String>
   <String Id="UnconfigUserInit">Annulation de l'inscription du processus UserInit : wssm.exe</String>
   <String Id="VM_WaitForpairing_ProgressText">Waiting for agent pairing to complete...</String>

   <!-- UIText Strings -->
   <String Id="IDS_UITEXT_Available">Disponible</String>
   <String Id="IDS_UITEXT_Bytes">octets</String>
   <String Id="IDS_UITEXT_CompilingFeaturesCost">Calcul de l'espace nécessaire pour cette fonction en cours…</String>
   <String Id="IDS_UITEXT_Differences">Différences</String>
   <String Id="IDS_UITEXT_DiskSize">Taille du disque</String>
   <String Id="IDS_UITEXT_FeatureCompletelyRemoved">Cette fonction sera entièrement supprimée.</String>
   <String Id="IDS_UITEXT_FeatureContinueNetwork">Cette fonction sera toujours exécutée depuis le réseau.</String>
   <String Id="IDS_UITEXT_FeatureFreeSpace">Cette fonction libère [1] sur votre disque dur.</String>
   <String Id="IDS_UITEXT_FeatureInstalledCD">Cette fonction et toutes ses sous-fonctions seront installées pour être exécutées à partir du CD.</String>
   <String Id="IDS_UITEXT_FeatureInstalledCD2">Cette fonction sera installée pour être exécutée à partir du CD.</String>
   <String Id="IDS_UITEXT_FeatureInstalledLocal">Fonction et sous-fonctions installées sur disque dur local.</String>
   <String Id="IDS_UITEXT_FeatureInstalledLocal2">Fonction installée sur disque dur local.</String>
   <String Id="IDS_UITEXT_FeatureInstalledNetwork">Cette fonction et toutes ses sous-fonctions seront installées pour être exécutées depuis le réseau.</String>
   <String Id="IDS_UITEXT_FeatureInstalledNetwork2">Cette fonction sera installée pour être exécutée à partir du réseau.</String>
   <String Id="IDS_UITEXT_FeatureInstalledRequired">Sera installé à la demande.</String>
   <String Id="IDS_UITEXT_FeatureInstalledWhenRequired">Cette fonction sera configurée pour être installée à la demande.</String>
   <String Id="IDS_UITEXT_FeatureInstalledWhenRequired2">Cette fonction sera installée à la demande.</String>
   <String Id="IDS_UITEXT_FeatureLocal">Cette fonction sera installée sur le disque dur local.</String>
   <String Id="IDS_UITEXT_FeatureLocal2">Cette fonction sera installée sur votre disque dur local.</String>
   <String Id="IDS_UITEXT_FeatureNetwork">Cette fonction sera installée pour être exécutée à partir du réseau.</String>
   <String Id="IDS_UITEXT_FeatureNetwork2">Cette fonction sera disponible pour être exécutée à partir du réseau.</String>
   <String Id="IDS_UITEXT_FeatureNotAvailable">Fonction indisponible.</String>
   <String Id="IDS_UITEXT_FeatureOnCD">Cette fonction sera installée pour être exécutée à partir du CD.</String>
   <String Id="IDS_UITEXT_FeatureOnCD2">Cette fonction sera disponible pour être exécutée à partir du CD.</String>
   <String Id="IDS_UITEXT_FeatureRemainLocal">Cette fonction sera conservée sur votre disque dur local.</String>
   <String Id="IDS_UITEXT_FeatureRemoveNetwork">Cette fonction sera supprimée de votre disque dur local, mais pourra encore être exécutée à partir du réseau.</String>
   <String Id="IDS_UITEXT_FeatureRemovedCD">Cette fonction sera supprimée de votre disque dur local mais pourra toujours être exécutée depuis votre CD.</String>
   <String Id="IDS_UITEXT_FeatureRemovedUnlessRequired">Cette fonction sera supprimée de votre disque dur local mais sera configurée pour être installée à la demande.</String>
   <String Id="IDS_UITEXT_FeatureRequiredSpace">Cette fonction requiert [1] sur votre disque dur.</String>
   <String Id="IDS_UITEXT_FeatureRunFromCD">Cette fonction sera toujours exécutée depuis le CD</String>
   <String Id="IDS_UITEXT_FeatureSpaceFree">Cette fonction libère [1] sur votre disque dur. [2] sous-fonctions sur [3] sont sélectionnées. Les sous-fonctions libèrent [4] sur votre disque dur.</String>
   <String Id="IDS_UITEXT_FeatureSpaceFree2">Cette fonction libère [1] sur votre disque dur. [2] sous-fonctions sur [3] sont sélectionnées. Les sous-fonctions nécessitent la présence de [4] sur votre disque dur.</String>
   <String Id="IDS_UITEXT_FeatureSpaceFree3">Cette fonction nécessite la présence de [1] sur votre disque dur. [2] sous-fonctions sur [3] sont sélectionnées. Les sous-fonctions libèrent [4] sur votre disque dur.</String>
   <String Id="IDS_UITEXT_FeatureSpaceFree4">Cette fonction nécessite la présence de [1] sur votre disque dur. [2] sous-fonctions sur [3] sont sélectionnées. Les sous-fonctions nécessitent la présence de [4] sur votre disque dur.</String>
   <String Id="IDS_UITEXT_FeatureUnavailable">Cette fonction ne sera plus disponible.</String>
   <String Id="IDS_UITEXT_FeatureUninstallNoNetwork">Cette fonction sera entièrement désinstallée et ne pourra plus être exécutée à partir du réseau.</String>
   <String Id="IDS_UITEXT_FeatureWasCD">Cette fonction était exécutée à partir du CD mais va être configurée pour être installée à la demande.</String>
   <String Id="IDS_UITEXT_FeatureWasCDLocal">Cette fonction était exécutée à partir du CD mais va être installée sur le disque dur local.</String>
   <String Id="IDS_UITEXT_FeatureWasOnNetworkInstalled">Cette fonction était exécutée à partir du réseau mais sera installée à la demande.</String>
   <String Id="IDS_UITEXT_FeatureWasOnNetworkLocal">Cette fonction était exécutée à partir du réseau mais sera installée sur le disque dur local.</String>
   <String Id="IDS_UITEXT_FeatureWillBeUninstalled">Cette fonction sera entièrement désinstallée et ne pourra plus être exécutée à partir du CD.</String>
   <String Id="IDS_UITEXT_Folder">Fldr|Nouveau dossier</String>
   <String Id="IDS_UITEXT_GB">Go</String>
   <String Id="IDS_UITEXT_KB">Ko</String>
   <String Id="IDS_UITEXT_MB">Mo</String>
   <String Id="IDS_UITEXT_Required">Requis</String>
   <String Id="IDS_UITEXT_TimeRemaining">Temps restant : {[1] min }[2] sec</String>
   <String Id="IDS_UITEXT_Volume">Volume</String>


   <!-- Error Table Strings -->
   <String Id="IDS_ERROR_0">{{Erreur fatale : }}</String>
   <String Id="IDS_ERROR_1">Erreur [1].</String>
   <String Id="IDS_ERROR_2">Avertissement [1].</String>
   <String Id="IDS_ERROR_4">Info [1].</String>
   <String Id="IDS_ERROR_5">Erreur interne [1]. [2]{, [3]}{, [4]}</String>
   <String Id="IDS_ERROR_7">{{Disque plein :}}</String>
   <String Id="IDS_ERROR_8">Action [Time] : [1]. [2]</String>
   <String Id="IDS_ERROR_9">[ProductName]</String>
   <String Id="IDS_ERROR_10">{[2]}{, [3]}{, [4]}</String>
   <String Id="IDS_ERROR_11">Type de message : [1], Argument : [2]</String>
   <String Id="IDS_ERROR_12">=== Début de l'écriture dans le journal : [Date] [Time] ===</String>
   <String Id="IDS_ERROR_13">=== Fin de l'écriture dans le journal : [Date] [Time] ===</String>
   <String Id="IDS_ERROR_14">Début de l'action [Time] : [1].</String>
   <String Id="IDS_ERROR_15">Fin de l'action [Time] : [1]. Valeur renvoyée [2].</String>
   <String Id="IDS_ERROR_16">Temps restant : {[1] minutes }{[2] secondes}</String>
   <String Id="IDS_ERROR_17">Mémoire insuffisante. Fermez les autres applications avant de réessayer.</String>
   <String Id="IDS_ERROR_18">Le programme d'installation ne répond plus.</String>
   <String Id="IDS_ERROR_19">Le programme d'installation s'est arrêté prématurément.</String>
   <String Id="IDS_ERROR_20">Patientez pendant que Windows configure [ProductName]</String>
   <String Id="IDS_ERROR_21">Collecte des informations nécessaires en cours…</String>
   <String Id="IDS_ERROR_22">Suppression des versions antérieures de cette application</String>
   <String Id="IDS_ERROR_23">Préparation de la suppression des versions antérieures de cette application</String>
   <String Id="IDS_ERROR_32">Installation de {[ProductName] } terminée.</String>
   <String Id="IDS_ERROR_33">Échec de l'installation de {[ProductName] }.</String>
   <String Id="IDS_ERROR_1101">Erreur lors de la lecture du fichier [2]. {{ Erreur système [3].}} Vérifiez que ce fichier existe et que vous êtes autorisé à y accéder.</String>
   <String Id="IDS_ERROR_1301">Impossible de créer le fichier [3]. Un répertoire avec le même nom existe déjà. Annulez la procédure et recommencez l'installation à un autre emplacement.</String>
   <String Id="IDS_ERROR_1302">Insérez le disque : [2]</String>
   <String Id="IDS_ERROR_1303">Le programme d'installation ne dispose pas des privilèges suffisants pour accéder au répertoire : [2]. Impossible de poursuivre l'installation. Ouvrez une session en tant qu'administrateur ou contactez votre administrateur système.</String>
   <String Id="IDS_ERROR_1304">Erreur lors de l'écriture dans le fichier [2]. Vérifiez que vous êtes autorisé à accéder à ce répertoire.</String>
   <String Id="IDS_ERROR_1305">Erreur lors de la lecture du fichier [2]. Vérifiez que ce fichier existe et que vous êtes autorisé à y accéder.</String>
   <String Id="IDS_ERROR_1306">Le fichier [2] est ouvert en mode exclusif dans une autre application. Fermez toutes les autres applications, puis cliquez sur Réessayer.</String>
   <String Id="IDS_ERROR_1307">L'espace disque est insuffisant pour installer ce fichier [2]. Libérez de l'espace disque et cliquez sur Réessayer, ou cliquez sur Annuler pour quitter.</String>
   <String Id="IDS_ERROR_1308">Fichier source introuvable [2]. Vérifiez que le fichier existe et que vous êtes autorisé à y accéder.</String>
   <String Id="IDS_ERROR_1309">Erreur lors de la lecture du fichier [3]. {{ Erreur système [2].}} Vérifiez que ce fichier existe et que vous êtes autorisé à y accéder.</String>
   <String Id="IDS_ERROR_1310">Erreur d'écriture dans le fichier [3]. {{ Erreur système [2].}} Vérifiez que vous êtes autorisé à accéder à ce répertoire.</String>
   <String Id="IDS_ERROR_1311">Fichier source introuvable{{(cabinet)}} : [2]. Vérifiez que ce fichier existe et que vous êtes autorisé à y accéder.</String>
   <String Id="IDS_ERROR_1312">Impossible de créer le répertoire [2]. Un fichier du même nom existe déjà. Renommez le fichier ou supprimez-le, puis cliquez sur Réessayer ou cliquez sur Annuler pour quitter.</String>
   <String Id="IDS_ERROR_1313">Le volume [2] n'est pas disponible actuellement. Sélectionnez-en un autre.</String>
   <String Id="IDS_ERROR_1314">Le chemin d'accès spécifié [2] n'est pas disponible.</String>
   <String Id="IDS_ERROR_1315">Impossible d'écrire dans le dossier spécifié [2].</String>
   <String Id="IDS_ERROR_1316">Une erreur réseau s'est produite pendant la tentative de lecture du fichier [2]</String>
   <String Id="IDS_ERROR_1317">Une erreur s'est produite lors de la tentative de création du répertoire [2]</String>
   <String Id="IDS_ERROR_1318">Une erreur réseau s'est produite lors de la tentative de création du répertoire [2]</String>
   <String Id="IDS_ERROR_1319">Une erreur réseau s'est produite lors de la tentative d'ouverture du fichier CAB source [2].</String>
   <String Id="IDS_ERROR_1320">Le chemin d'accès spécifié est trop long [2].</String>
   <String Id="IDS_ERROR_1321">Le programme d'installation ne dispose pas des privilèges suffisants pour modifier le fichier [2].</String>
   <String Id="IDS_ERROR_1322">Une partie du chemin d'accès [2] dépasse la longueur autorisée par le système.</String>
   <String Id="IDS_ERROR_1323">Le chemin d'accès [2] contient des mots qui ne sont pas valides dans les dossiers.</String>
   <String Id="IDS_ERROR_1324">Le chemin d'accès [2] contient un caractère non valide.</String>
   <String Id="IDS_ERROR_1325">[2] n'est pas un nom de fichier court valide.</String>
   <String Id="IDS_ERROR_1326">Erreur lors de l'obtention des informations sur la sécurité du fichier : [3] GetLastError : [2]</String>
   <String Id="IDS_ERROR_1327">Lecteur non valide : [2]</String>
   <String Id="IDS_ERROR_1328">Erreur lors de l'application du correctif au fichier [2]. Celui-ci a probablement été mis à jour par d'autres moyens et ne peut plus être modifié par ce correctif. Pour plus d'informations, consultez le fournisseur de votre correctif. {{Erreur système : [3]}}</String>
   <String Id="IDS_ERROR_1329">Un fichier requis ne peut pas être installé car le fichier CAB [2] n'est pas signé numériquement. Cela peut indiquer que le fichier CAB est endommagé.</String>
   <String Id="IDS_ERROR_1330">Un fichier requis ne peut pas être installé car le fichier CAB [2] contient une signature numérique non valide. Cela peut indiquer que le fichier CAB est endommagé.{L'erreur [3] a été renvoyée par WinVerifyTrust.}</String>
   <String Id="IDS_ERROR_1331">Impossible de copier le fichier [2] : erreur CRC.</String>
   <String Id="IDS_ERROR_1332">Impossible d'appliquer un correctif au fichier [2] : erreur CRC.</String>
   <String Id="IDS_ERROR_1333">Impossible d'appliquer un correctif au fichier [2] : erreur CRC.</String>
   <String Id="IDS_ERROR_1334">Le fichier '[2]' ne peut pas être installé car il est introuvable dans le fichier CAB '[3]'. Cela peut indiquer une erreur réseau, une erreur de lecture du CD-ROM ou un problème inhérent au package.</String>
   <String Id="IDS_ERROR_1335">Le fichier CAB '[2]' requis pour cette installation est endommagé et inutilisable. Cela peut indiquer une erreur réseau, une erreur de lecture du CD-ROM ou un problème inhérent au package.</String>
   <String Id="IDS_ERROR_1336">Une erreur s'est produite lors de la création d'un fichier temporaire nécessaire à l'installation. Dossier : [3]. Code d'erreur système : [2]</String>
   <String Id="IDS_ERROR_1401">Impossible de créer la clé [2]. {{ Erreur système [3].}} Vérifiez que vous disposez des droits suffisants pour cette clé ou contactez votre service de support technique.</String>
   <String Id="IDS_ERROR_1402">Impossible d'ouvrir la clé [2]. {{ Erreur système [3].}} Vérifiez que vous disposez des droits suffisants pour cette clé ou contactez votre service de support technique.</String>
   <String Id="IDS_ERROR_1403">Impossible de supprimer la valeur [2] de la clé [3]. {{ Erreur système [4].}} Vérifiez que vous disposez des droits suffisants pour cette clé ou contactez votre service de support technique.</String>
   <String Id="IDS_ERROR_1404">Impossible de supprimer la clé [2]. {{ Erreur système [3].}} Vérifiez que vous disposez des droits suffisants pour cette clé ou contactez votre service de support technique.</String>
   <String Id="IDS_ERROR_1405">Impossible de lire la valeur [2] de la clé [3]. {{ Erreur système [4].}} Vérifiez que vous disposez des droits suffisants pour cette clé ou contactez votre service de support technique.</String>
   <String Id="IDS_ERROR_1406">Impossible d'écrire la valeur [2] dans la clé [3]. {{ Erreur système [4].}} Vérifiez que vous disposez des droits suffisants pour cette clé ou contactez votre service de support technique.</String>
   <String Id="IDS_ERROR_1407">Impossible d'obtenir le nom des valeurs de la clé [2]. {{ Erreur système [3].}} Vérifiez que vous disposez des droits suffisants pour cette clé ou contactez votre service de support technique.</String>
   <String Id="IDS_ERROR_1408">Impossible d'obtenir le nom des sous-clés de la clé [2]. {{ Erreur système [3].}} Vérifiez que vous disposez des droits suffisants pour cette clé ou contactez votre service de support technique.</String>
   <String Id="IDS_ERROR_1409">Impossible de lire les informations sur la sécurité de la clé [2]. {{ Erreur système [3].}} Vérifiez que vous disposez des droits suffisants pour cette clé ou contactez votre service de support technique.</String>
   <String Id="IDS_ERROR_1410">Impossible d'augmenter l'espace disponible dans le registre. [2] Ko d'espace doivent être libres dans le registre pour permettre l'installation de cette application.</String>
   <String Id="IDS_ERROR_1500">Une autre installation est en cours d'exécution. Vous devez la terminer avant de poursuivre cette installation.</String>
   <String Id="IDS_ERROR_1501">Erreur lors de l'accès aux données sécurisées. Vérifiez que Windows Installer est correctement configuré, puis recommencez l'installation.</String>
   <String Id="IDS_ERROR_1502">L'utilisateur [2] a déjà lancé l'installation du produit [3]. Il devra réexécuter cette installation avant de pouvoir utiliser le produit. L'installation en cours va se poursuivre.</String>
   <String Id="IDS_ERROR_1503">L'utilisateur [2] a déjà lancé l'installation du produit [3]. Il devra réexécuter cette installation avant de pouvoir utiliser le produit.</String>
   <String Id="IDS_ERROR_1601">Espace disque insuffisant -- Volume : '[2]' ; Espace requis : [3] Ko ; Espace disponible : [4] Ko. Libérez de l'espace disque et réessayez.</String>
   <String Id="IDS_ERROR_1602">Voulez-vous vraiment annuler ?</String>
   <String Id="IDS_ERROR_1603">Le fichier [2][3] est actuellement utilisé{ par le processus suivant : Nom : [4], Identificateur : [5], Titre de la fenêtre : [6]}. Fermez cette application, puis réessayez.</String>
   <String Id="IDS_ERROR_1604">Le produit [2] est déjà installé. Impossible d'installer ce produit car il est incompatible avec celui déjà installé.</String>
   <String Id="IDS_ERROR_1605">Espace disque insuffisant -- Volume : [2] ; Espace requis : [3] Ko ; Espace disponible : [4] Ko. Si la fonction de restauration est désactivée, l'espace disponible est suffisant. Cliquez sur Abandonner pour quitter, sur Réessayer pour vérifier à nouveau la quantité d'espace disponible ou sur Ignorer pour continuer sans la fonction de restauration.</String>
   <String Id="IDS_ERROR_1606">Impossible d'accéder à l'emplacement réseau [2].</String>
   <String Id="IDS_ERROR_1607">Vous devez fermer les applications suivantes avant de poursuivre l'installation :</String>
   <String Id="IDS_ERROR_1608">Impossible de trouver sur cet ordinateur un produit compatible pour permettre l'installation de ce produit.</String>
   <String Id="IDS_ERROR_1609">Une erreur s'est produite lors de l'application des paramètres de sécurité. [2] n'est pas un utilisateur ou un groupe valide. Cela indique peut-être une erreur au niveau du package ou de la connexion à un contrôleur de domaine sur le réseau. Vérifiez votre connexion réseau et cliquez sur Réessayer ou sur Annuler pour mettre fin à l'installation. Impossible de localiser le SID de l'utilisateur, erreur système [3]</String>
   <String Id="IDS_ERROR_1651">L'utilisateur Admin n'a pas pu appliquer le correctif pour une application gérée par utilisateur ou par ordinateur, et qui est à l'état de publication.</String>
   <String Id="IDS_ERROR_1701">La clé [2] n'est pas valide. Vérifiez que vous avez entré la clé correcte.</String>
   <String Id="IDS_ERROR_1702">Le programme d'installation doit redémarrer votre système pour continuer la configuration de [2]. Cliquez sur Oui pour redémarrer maintenant, ou sur Non pour redémarrer ultérieurement.</String>
   <String Id="IDS_ERROR_1703">Vous devez redémarrer votre système pour que les modifications apportées à la configuration de [2] prennent effet. Cliquez sur Oui pour redémarrer maintenant, ou sur Non pour redémarrer ultérieurement.</String>
   <String Id="IDS_ERROR_1704">Une installation de [2] est actuellement suspendue. Vous devez annuler les modifications effectuées par cette installation pour continuer. Voulez-vous annuler ces modifications ?</String>
   <String Id="IDS_ERROR_1705">Une installation précédente de ce produit est en cours. Vous devez annuler les modifications effectuées par cette installation pour continuer. Voulez-vous annuler ces modifications ?</String>
   <String Id="IDS_ERROR_1706">Aucune source valide n'a été trouvée pour le produit [2]. Impossible de poursuivre l'exécution de Windows Installer.</String>
   <String Id="IDS_ERROR_1707">Installation terminée.</String>
   <String Id="IDS_ERROR_1708">L'installation a échoué.</String>
   <String Id="IDS_ERROR_1709">Produit : [2] -- [3]</String>
   <String Id="IDS_ERROR_1710">Vous pouvez restaurer l'état antérieur de votre ordinateur ou poursuivre l'installation ultérieurement. Voulez-vous restaurer votre ordinateur ?</String>
   <String Id="IDS_ERROR_1711">Une erreur est survenue lors de l'écriture sur le disque des informations concernant l'installation. Vérifiez que l'espace disponible sur le disque est suffisant et cliquez sur Réessayer, ou cliquez sur Annuler pour mettre fin à l'installation.</String>
   <String Id="IDS_ERROR_1712">Impossible de trouver un ou plusieurs des fichiers nécessaires pour restaurer l'état antérieur de votre ordinateur. Impossible de procéder à la restauration.</String>
   <String Id="IDS_ERROR_1713">[2] ne peut pas installer l'un des produits requis. Contactez votre groupe de support technique. {{Erreur système [3].}}</String>
   <String Id="IDS_ERROR_1714">Impossible de supprimer la version antérieure de [2]. Contactez votre groupe de support technique. {{Erreur système [3].}}</String>
   <String Id="IDS_ERROR_1715">A installé [2].</String>
   <String Id="IDS_ERROR_1716">A configuré [2].</String>
   <String Id="IDS_ERROR_1717">A supprimé [2].</String>
   <String Id="IDS_ERROR_1718">Le fichier [2] a été rejeté par la stratégie de signature numérique.</String>
   <String Id="IDS_ERROR_1719">Accès au service Windows Installer impossible. Contactez votre service de support technique pour vérifier qu'il est correctement inscrit et activé.</String>
   <String Id="IDS_ERROR_1720">Il y a un problème au niveau du package Windows Installer. Un script nécessaire à l'installation n'a pas pu être exécuté. Contactez votre service de support technique ou le fournisseur du package. Action personnalisée [2] erreur de script [3], [4] : [5] ligne [6], colonne [7], [8]</String>
   <String Id="IDS_ERROR_1721">Il y a un problème au niveau de ce package Windows Installer. Un programme nécessaire à l'installation n'a pas pu être exécuté. Contactez votre service de support technique ou le fournisseur du package. Action : [2], emplacement : [3], commande : [4]</String>
   <String Id="IDS_ERROR_1722">Il y a un problème au niveau de ce package Windows Installer. Un programme nécessaire à l'installation ne s'est pas correctement exécuté. Contactez votre service de support technique ou le fournisseur du package. Action [2], emplacement : [3], commande : [4]</String>
   <String Id="IDS_ERROR_1723">Il y a un problème au niveau de ce package Windows Installer. Une DLL nécessaire à l'installation n'a pas pu être exécutée. Contactez votre service de support technique ou le fournisseur du package. Action [2], entrée : [3], bibliothèque : [4]</String>
   <String Id="IDS_ERROR_1724">Suppression effectuée.</String>
   <String Id="IDS_ERROR_1725">Échec de la suppression.</String>
   <String Id="IDS_ERROR_1726">Publication effectuée.</String>
   <String Id="IDS_ERROR_1727">Échec de la publication.</String>
   <String Id="IDS_ERROR_1728">Configuration effectuée.</String>
   <String Id="IDS_ERROR_1729">Échec de la configuration.</String>
   <String Id="IDS_ERROR_1730">Vous devez être administrateur pour supprimer cette application. Pour la supprimer, ouvrez une session en tant qu'administrateur ou contactez votre service de support technique pour obtenir de l'aide.</String>
   <String Id="IDS_ERROR_1731">Le package d'installation source du produit [2] n'est pas synchronisé avec le package client. Recommencez l'installation en utilisant une copie valide du package d'installation '[3]'.</String>
   <String Id="IDS_ERROR_1732">Vous devez redémarrer l'ordinateur afin de terminer l'installation de [2]. D'autres utilisateurs sont actuellement connectés à cet ordinateur et un redémarrage pourrait causer la perte de leurs données. Voulez-vous redémarrer maintenant ?</String>
   <String Id="IDS_ERROR_1801">Le chemin d'accès [2] n'est pas valide. Spécifiez un chemin d'accès valide.</String>
   <String Id="IDS_ERROR_1802">Mémoire insuffisante. Fermez les autres applications avant de réessayer.</String>
   <String Id="IDS_ERROR_1803">Le lecteur [2] est vide. Insérez un disque et cliquez sur Réessayer, ou cliquez sur Annuler pour revenir au volume sélectionné précédemment.</String>
   <String Id="IDS_ERROR_1804">Le lecteur [2] est vide. Insérez un disque et cliquez sur Réessayer, ou cliquez sur Annuler pour revenir dans la boîte de dialogue Parcourir et sélectionner un autre volume.</String>
   <String Id="IDS_ERROR_1805">Le dossier [2] n'existe pas. Entrez un chemin d'accès à un dossier existant.</String>
   <String Id="IDS_ERROR_1806">Vous ne disposez pas des privilèges suffisants pour lire le contenu de ce dossier.</String>
   <String Id="IDS_ERROR_1807">Impossible de trouver un dossier de destination valide pour l'installation.</String>
   <String Id="IDS_ERROR_1901">Erreur pendant la tentative de lecture de la base de données d'installation source : [2].</String>
   <String Id="IDS_ERROR_1902">Planification du redémarrage : le fichier [2] est renommé [3]. Pour terminer l'opération, votre système doit être redémarré.</String>
   <String Id="IDS_ERROR_1903">Planification du redémarrage : suppression du fichier [2]. Pour terminer l'opération, votre système doit être redémarré.</String>
   <String Id="IDS_ERROR_1904">Impossible d'inscrire le module [2]. HRESULT [3]. Contactez votre service de support technique.</String>
   <String Id="IDS_ERROR_1905">Impossible d'annuler l'inscription du module [2]. HRESULT [3]. Contactez votre service de support technique.</String>
   <String Id="IDS_ERROR_1906">Impossible de mettre en cache le lot [2]. Erreur : [3]. Contactez votre service de support technique.</String>
   <String Id="IDS_ERROR_1907">Impossible d'inscrire la police [2]. Vérifiez que vous disposez des autorisations nécessaires pour l'installation des polices et que le système prend en charge cette police.</String>
   <String Id="IDS_ERROR_1908">Impossible d'annuler l'inscription de la police [2]. Vérifiez que les autorisations dont vous disposez sont suffisantes pour supprimer des polices.</String>
   <String Id="IDS_ERROR_1909">Impossible de créer le raccourci [2]. Vérifiez que le dossier de destination existe et que vous êtes autorisé à y accéder.</String>
   <String Id="IDS_ERROR_1910">Impossible de supprimer le raccourci [2]. Vérifiez que le fichier de raccourcis existe et que vous êtes autorisé à y accéder.</String>
   <String Id="IDS_ERROR_1911">Impossible d'inscrire la bibliothèque de types pour le fichier [2]. Contactez votre service de support technique.</String>
   <String Id="IDS_ERROR_1912">Impossible d'annuler l'inscription de la bibliothèque de types pour le fichier [2]. Contactez votre service de support technique.</String>
   <String Id="IDS_ERROR_1913">Impossible de mettre à jour le fichier INI [2][3]. Vérifiez que ce fichier existe et que vous êtes autorisé à y accéder.</String>
   <String Id="IDS_ERROR_1914">Impossible de planifier le remplacement du fichier [2] par le fichier [3] au redémarrage. Vérifiez que vous disposez des autorisations d'accès en écriture au fichier [3].</String>
   <String Id="IDS_ERROR_1915">Erreur lors de la suppression du gestionnaire de pilotes ODBC. Erreur ODBC [2] : [3]. Contactez votre service de support technique.</String>
   <String Id="IDS_ERROR_1916">Erreur lors de l'installation du gestionnaire de pilotes ODBC. Erreur ODBC [2] : [3]. Contactez votre service de support technique.</String>
   <String Id="IDS_ERROR_1917">Erreur lors de la suppression du pilote ODBC [4]. Erreur ODBC [2] : [3]. Vérifiez que vous disposez des privilèges suffisants pour supprimer des pilotes ODBC.</String>
   <String Id="IDS_ERROR_1918">Erreur lors de l'installation du pilote ODBC [4]. Erreur ODBC [2] : [3]. Vérifiez que le fichier [4] existe et que vous êtes autorisé à y accéder.</String>
   <String Id="IDS_ERROR_1919">Erreur lors de la configuration de la source de données ODBC [4]. Erreur ODBC [2] : [3]. Vérifiez que le fichier [4] existe et que vous êtes autorisé à y accéder.</String>
   <String Id="IDS_ERROR_1920">Échec du démarrage du service [2] ([3]). Vérifiez que vous disposez des privilèges suffisants pour démarrer les services système.</String>
   <String Id="IDS_ERROR_1921">Impossible d'arrêter le service [2] ([3]). Vérifiez que vous disposez des privilèges suffisants pour arrêter les services système.</String>
   <String Id="IDS_ERROR_1922">Impossible de supprimer le service [2] ([3]). Vérifiez que vous disposez des privilèges suffisants pour supprimer les services système.</String>
   <String Id="IDS_ERROR_1923">Impossible d'installer le service [2] ([3]). Vérifiez que vous disposez des privilèges suffisants pour installer des services système.</String>
   <String Id="IDS_ERROR_1924">Impossible de mettre à jour la variable d'environnement [2]. Vérifiez que vous disposez des privilèges suffisants pour modifier les variables d'environnement.</String>
   <String Id="IDS_ERROR_1925">Vous ne disposez pas des privilèges suffisants pour exécuter cette installation pour tous les utilisateurs de cet ordinateur. Ouvrez une session en tant qu'administrateur, puis réessayez d'exécuter cette installation.</String>
   <String Id="IDS_ERROR_1926">Impossible de définir la sécurité du fichier [3]. Erreur : [2]. Vérifiez que vous disposez des privilèges suffisants pour modifier les autorisations de sécurité pour ce fichier.</String>
   <String Id="IDS_ERROR_1927">Les services de composants (COM+ 1.0) ne sont pas installés sur cet ordinateur.  Cette installation requiert les services de composants pour s'exécuter correctement.  Les services de composants sont disponibles sous Windows 2000.</String>
   <String Id="IDS_ERROR_1928">Erreur lors de l'inscription de l'application COM+. Contactez votre service de support technique pour obtenir plus d'informations.</String>
   <String Id="IDS_ERROR_1929">Erreur lors de la désinscription de l'application COM+. Contactez votre service de support technique pour obtenir plus d'informations.</String>
   <String Id="IDS_ERROR_1930">La description du service '[2]' ([3]) n'a pas pu être modifiée.</String>
   <String Id="IDS_ERROR_1931">Le service Windows Installer ne peut pas mettre à jour le fichier système [2], car ce dernier est protégé par Windows. Vous devrez peut-être mettre à jour votre système d'exploitation pour que ce programme fonctionne correctement. {{Version de package [3], Version protégée du système d'exploitation [4]}}</String>
   <String Id="IDS_ERROR_1932">Le service Windows Installer ne peut pas mettre à jour le fichier Windows protégé [2]. {{Version de package [3], Version protégée du système d'exploitation [4], Erreur SFP [5]}}</String>
   <String Id="IDS_ERROR_1933">Le service Windows Installer ne peut pas mettre à jour un ou plusieurs fichiers Windows protégés. Erreur SFP : [2]. Liste des fichiers protégés : [3]</String>
   <String Id="IDS_ERROR_1934">Les installations utilisateur sont désactivées via la stratégie de l'ordinateur.</String>
   <String Id="IDS_ERROR_1935">Une erreur s'est produite lors de l'installation du composant d'assembly [2]. HRESULT : [3]. {{interface de l'assembly [4], fonction [5], nom de l'assembly [6]}}</String>
   <String Id="IDS_ERROR_1936">Une erreur s'est produite pendant l'installation de l'assembly '[6]'. L'assembly n'a pas un nom fort et n'est pas signé avec une longueur de clé minimale. HRESULT : [3]. {{interface de l'assembly [4], fonction [5], composant [2]}}</String>
   <String Id="IDS_ERROR_1937">Une erreur s'est produite pendant l'installation de l'assembly '[6]'. La signature ou le catalogue n'a pas pu être vérifié ou n'est pas valide. HRESULT : [3]. {{interface de l'assembly [4], fonction [5], composant [2]}}</String>
   <String Id="IDS_ERROR_1938">Une erreur s'est produite pendant l'installation de l'assembly '[6]'. Un ou plusieurs modules de l'assembly sont introuvables. HRESULT : [3]. {{interface de l'assembly [4], fonction [5], composant [2]}}</String>
   <String Id="IDS_ERROR_2101">Les raccourcis ne sont pas pris en charge par le système d'exploitation.</String>
   <String Id="IDS_ERROR_2102">Action .ini non valide : [2]</String>
   <String Id="IDS_ERROR_2103">Impossible de résoudre le chemin d'accès au dossier shell [2].</String>
   <String Id="IDS_ERROR_2104">Écriture du fichier .ini : [3] : Erreur système : [2].</String>
   <String Id="IDS_ERROR_2105">Échec de création du raccourci [3]. Erreur système : [2].</String>
   <String Id="IDS_ERROR_2106">Échec de suppression du raccourci [3]. Erreur système : [2].</String>
   <String Id="IDS_ERROR_2107">Erreur [3] lors de l'inscription de la bibliothèque de types [2].</String>
   <String Id="IDS_ERROR_2108">Erreur [3] lors de l'annulation de l'inscription de la bibliothèque de types [2].</String>
   <String Id="IDS_ERROR_2109">Section manquante pour l'action .ini.</String>
   <String Id="IDS_ERROR_2110">Clé manquante pour l'action .ini.</String>
   <String Id="IDS_ERROR_2111">Échec de détection des applications en cours d'exécution, impossible d'obtenir les données de performance. Opération inscrite renvoyée : [2].</String>
   <String Id="IDS_ERROR_2112">Échec de détection des applications en cours d'exécution, impossible d'obtenir l'index de performance. Opération inscrite renvoyée : [2].</String>
   <String Id="IDS_ERROR_2113">Échec de détection des applications en cours d'exécution.</String>
   <String Id="IDS_ERROR_2200">Base de données : [2]. Échec de création de l'objet de base de données, mode = [3].</String>
   <String Id="IDS_ERROR_2201">Base de données : [2]. Échec de l'initialisation, mémoire insuffisante.</String>
   <String Id="IDS_ERROR_2202">Base de données : [2]. Échec d'accès aux données, mémoire insuffisante.</String>
   <String Id="IDS_ERROR_2203">Base de données : [2]. Impossible d'ouvrir le fichier de base de données. Erreur système [3].</String>
   <String Id="IDS_ERROR_2204">Base de données : [2]. La table existe déjà : [3].</String>
   <String Id="IDS_ERROR_2205">Base de données : [2]. La table n'existe pas : [3].</String>
   <String Id="IDS_ERROR_2206">Base de données : [2]. Impossible de supprimer la table : [3].</String>
   <String Id="IDS_ERROR_2207">Base de données : [2]. Violation d'intention.</String>
   <String Id="IDS_ERROR_2208">Base de données : [2]. Paramètres insuffisants pour l'exécution.</String>
   <String Id="IDS_ERROR_2209">Base de données : [2]. État du curseur non valide.</String>
   <String Id="IDS_ERROR_2210">Base de données : [2]. Type de données de mise à jour non valide dans la colonne [3].</String>
   <String Id="IDS_ERROR_2211">Base de données : [2]. Impossible de créer la table de base de données [3].</String>
   <String Id="IDS_ERROR_2212">Base de données : [2]. La base de données n'est pas accessible en écriture.</String>
   <String Id="IDS_ERROR_2213">Base de données : [2]. Erreur lors de l'enregistrement des tables de la base de données.</String>
   <String Id="IDS_ERROR_2214">Base de données : [2]. Erreur lors de l'écriture du fichier d'exportation : [3].</String>
   <String Id="IDS_ERROR_2215">Base de données : [2]. Impossible d'ouvrir le fichier d'importation : [3].</String>
   <String Id="IDS_ERROR_2216">Base de données : [2]. Erreur de format du fichier d'importation : [3], ligne [4].</String>
   <String Id="IDS_ERROR_2217">Base de données : [2]. État incorrect pour CreateOutputDatabase [3].</String>
   <String Id="IDS_ERROR_2218">Base de données : [2]. Nom de table non fourni.</String>
   <String Id="IDS_ERROR_2219">Base de données : [2]. Format de base de données du programme d'installation non valide.</String>
   <String Id="IDS_ERROR_2220">Base de données : [2]. Données de champ/ligne non valides.</String>
   <String Id="IDS_ERROR_2221">Base de données : [2]. Conflit de pages de codes dans le fichier d'importation : [3].</String>
   <String Id="IDS_ERROR_2222">Base de données : [2]. La page de codes de transformation ou de fusion [3] est différente de la page de code de la base de données [4].</String>
   <String Id="IDS_ERROR_2223">Base de données : [2]. Les bases de données sont identiques. Aucune transformation générée.</String>
   <String Id="IDS_ERROR_2224">Base de données : [2]. GenerateTransform : base de données endommagée. Table : [3].</String>
   <String Id="IDS_ERROR_2225">Base de données : [2]. Transformation : impossible de transformer une table temporaire. Table : [3].</String>
   <String Id="IDS_ERROR_2226">Base de données : [2]. Échec de la transformation.</String>
   <String Id="IDS_ERROR_2227">Base de données : [2]. Identificateur '[3]' non valide dans la requête SQL : [4].</String>
   <String Id="IDS_ERROR_2228">Base de données : [2]. Table '[3]' inconnue dans la requête SQL : [4].</String>
   <String Id="IDS_ERROR_2229">Base de données : [2]. Impossible de charger la table '[3]' dans la requête SQL  : [4].</String>
   <String Id="IDS_ERROR_2230">Base de données : [2]. Table '[3]' répétée dans la requête SQL : [4].</String>
   <String Id="IDS_ERROR_2231">Base de données : [2]. ')' manquant dans la requête SQL : [3].</String>
   <String Id="IDS_ERROR_2232">Base de données : [2]. Jeton '[3]' inattendu dans la requête SQL : [4].</String>
   <String Id="IDS_ERROR_2233">Base de données : [2]. Aucune colonne dans la clause SELECT de la requête SQL : [3].</String>
   <String Id="IDS_ERROR_2234">Base de données : [2]. Aucune colonne dans la clause ORDER BY de la requête SQL : [3].</String>
   <String Id="IDS_ERROR_2235">Base de données : [2]. Colonne '[3]' absente ou ambiguë dans la requête SQL : [4].</String>
   <String Id="IDS_ERROR_2236">Base de données : [2]. Opérateur '[3]' non valide dans la requête SQL : [4].</String>
   <String Id="IDS_ERROR_2237">Base de données : [2]. Chaîne de requête non valide ou manquante : [3].</String>
   <String Id="IDS_ERROR_2238">Base de données : [2]. Clause FROM manquante dans la requête SQL : [3].</String>
   <String Id="IDS_ERROR_2239">Base de données : [2]. Valeurs insuffisantes dans l'instruction INSERT SQL.</String>
   <String Id="IDS_ERROR_2240">Base de données : [2]. Colonnes de mise à jour manquantes dans l'instruction UPDATE SQL.</String>
   <String Id="IDS_ERROR_2241">Base de données : [2]. Colonnes d'insertion manquantes dans l'instruction INSERT SQL.</String>
   <String Id="IDS_ERROR_2242">Base de données : [2]. Colonne '[3]' répétée.</String>
   <String Id="IDS_ERROR_2243">Base de données : [2]. Aucune colonne principale définie pour la création de la table.</String>
   <String Id="IDS_ERROR_2244">Base de données : [2]. Spécificateur de type '[3]' incorrect dans la requête SQL [4].</String>
   <String Id="IDS_ERROR_2245">Échec de IStorage::Stat avec l'erreur [3].</String>
   <String Id="IDS_ERROR_2246">Base de données : [2]. Format de transformation du programme d'installation non valide.</String>
   <String Id="IDS_ERROR_2247">Base de données : [2] Échec de lecture/écriture du flux de transformation.</String>
   <String Id="IDS_ERROR_2248">Base de données : [2] GenerateTransform/Merge : le type de colonne de la table de base ne correspond pas à celui de la table de référence. Table : [3] Colonne n° : [4].</String>
   <String Id="IDS_ERROR_2249">Base de données : [2] GenerateTransform : nombre de colonnes dans la table de base supérieur à celui de la table de référence. Table : [3].</String>
   <String Id="IDS_ERROR_2250">Base de données : [2] Transformation : impossible d'ajouter la ligne existante. Table : [3].</String>
   <String Id="IDS_ERROR_2251">Base de données : [2] Transformation : impossible de supprimer une ligne qui n'existe pas. Table : [3].</String>
   <String Id="IDS_ERROR_2252">Base de données : [2] Transformation : impossible d'ajouter la table existante. Table : [3].</String>
   <String Id="IDS_ERROR_2253">Base de données : [2] Transformation : impossible de supprimer une table qui n'existe pas. Table : [3].</String>
   <String Id="IDS_ERROR_2254">Base de données : [2] Transformation : impossible de mettre à jour une ligne qui n'existe pas. Table : [3].</String>
   <String Id="IDS_ERROR_2255">Base de données : [2] Transformation : une colonne portant ce nom existe déjà. Table : [3] Colonne : [4].</String>
   <String Id="IDS_ERROR_2256">Base de données : [2] GenerateTransform/Merge : le nombre de clés primaires de la table de la base ne correspond pas à celui de la table de référence. Table : [3].</String>
   <String Id="IDS_ERROR_2257">Base de données : [2]. Intention de modification de la table en lecture seule : [3].</String>
   <String Id="IDS_ERROR_2258">Base de données : [2]. Incompatibilité de type dans le paramètre : [3].</String>
   <String Id="IDS_ERROR_2259">Base de données : [2] Échec de mise à jour de la ou des tables</String>
   <String Id="IDS_ERROR_2260">Échec du stockage Storage CopyTo. Erreur système : [3].</String>
   <String Id="IDS_ERROR_2261">Impossible de supprimer le flux [2]. Erreur système : [3].</String>
   <String Id="IDS_ERROR_2262">Le flux n'existe pas : [2]. Erreur système : [3].</String>
   <String Id="IDS_ERROR_2263">Impossible d'ouvrir le flux [2]. Erreur système : [3].</String>
   <String Id="IDS_ERROR_2264">Impossible de supprimer le flux [2]. Erreur système : [3].</String>
   <String Id="IDS_ERROR_2265">Impossible de valider le stockage. Erreur système : [3].</String>
   <String Id="IDS_ERROR_2266">Impossible de restaurer le stockage. Erreur système : [3].</String>
   <String Id="IDS_ERROR_2267">Impossible de supprimer le stockage [2]. Erreur système : [3].</String>
   <String Id="IDS_ERROR_2268">Base de données : [2]. Fusion : des conflits de fusion ont été signalés dans [3] tables.</String>
   <String Id="IDS_ERROR_2269">Base de données : [2]. Fusion : le nombre de colonnes est différent dans la table '[3]' des deux bases de données.</String>
   <String Id="IDS_ERROR_2270">Base de données : [2]. GenerateTransformMerge : un nom de colonne dans la table de base ne correspond pas à celui de la table de référence. Table : [3] Colonne n° : [4].</String>
   <String Id="IDS_ERROR_2271">Échec d'écriture de SummaryInformation pour la transformation.</String>
   <String Id="IDS_ERROR_2272">Base de données : [2]. MergeDatabase n'écrira aucune modification car la base de données est ouverte en lecture seule.</String>
   <String Id="IDS_ERROR_2273">Base de données : [2]. MergeDatabase : une référence à la base de données de base a été transmise en tant que base de données de référence.</String>
   <String Id="IDS_ERROR_2274">Base de données : [2]. MergeDatabase : impossible d'écrire les erreurs dans la table d'erreurs. Cela peut être dû à une colonne n'acceptant pas les valeurs Null dans une table d'erreurs prédéfinie.</String>
   <String Id="IDS_ERROR_2275">Base de données : [2]. Opération de modification [3] non valide pour les jointures de tables.</String>
   <String Id="IDS_ERROR_2276">Base de données : [2]. Page de codes [3] non prise en charge par le système.</String>
   <String Id="IDS_ERROR_2277">Base de données : [2]. Échec d'enregistrement de la table [3].</String>
   <String Id="IDS_ERROR_2278">Base de données : [2]. Le nombre maximal d'expressions (32) a été dépassé dans la clause WHERE de la requête SQL : [3].</String>
   <String Id="IDS_ERROR_2279">Base de données : [2] Transformation : trop de colonnes dans la table de base [3].</String>
   <String Id="IDS_ERROR_2280">Base de données : [2]. Impossible de créer la colonne [3] pour la table [4].</String>
   <String Id="IDS_ERROR_2281">Impossible de renommer le flux [2]. Erreur système : [3].</String>
   <String Id="IDS_ERROR_2282">Nom de flux [2] non valide.</String>
   <String Id="IDS_ERROR_2302">Notification de correctif : [2] octets corrigés jusqu'à présent.</String>
   <String Id="IDS_ERROR_2303">Erreur lors de l'obtention des informations de volume. GetLastError : [2].</String>
   <String Id="IDS_ERROR_2304">Erreur lors de l'obtention de l'espace disque disponible. GetLastError : [2]. Volume : [3].</String>
   <String Id="IDS_ERROR_2305">Erreur lors de l'attente du thread du correctif. GetLastError : [2].</String>
   <String Id="IDS_ERROR_2306">Impossible de créer le thread pour l'application du correctif. GetLastError : [2].</String>
   <String Id="IDS_ERROR_2307">Le nom de clé du fichier source est Null.</String>
   <String Id="IDS_ERROR_2308">Le nom du fichier de destination est Null.</String>
   <String Id="IDS_ERROR_2309">Tentative d'application du correctif au fichier [2] alors qu'un correctif est déjà en cours.</String>
   <String Id="IDS_ERROR_2310">Tentative de poursuite de l'application du correctif alors qu'aucun correctif n'est en cours.</String>
   <String Id="IDS_ERROR_2315">Séparateur de chemin manquant : [2].</String>
   <String Id="IDS_ERROR_2318">Le fichier n'existe pas : [2].</String>
   <String Id="IDS_ERROR_2319">Erreur lors de la définition d'un attribut de fichier : [3] GetLastError : [2].</String>
   <String Id="IDS_ERROR_2320">Fichier non accessible en écriture : [2].</String>
   <String Id="IDS_ERROR_2321">Erreur lors de la création du fichier : [2].</String>
   <String Id="IDS_ERROR_2322">Utilisateur annulé.</String>
   <String Id="IDS_ERROR_2323">Attribut de fichier non valide.</String>
   <String Id="IDS_ERROR_2324">Impossible d'ouvrir le fichier : [3] GetLastError : [2].</String>
   <String Id="IDS_ERROR_2325">Impossible d'obtenir l'heure du fichier : [3] GetLastError : [2].</String>
   <String Id="IDS_ERROR_2326">Erreur dans FileToDosDateTime.</String>
   <String Id="IDS_ERROR_2327">Impossible de supprimer le répertoire : [3] GetLastError : [2].</String>
   <String Id="IDS_ERROR_2328">Erreur lors de l'obtention des informations de version du fichier : [2].</String>
   <String Id="IDS_ERROR_2329">Erreur lors de la suppression du fichier : [3]. GetLastError : [2].</String>
   <String Id="IDS_ERROR_2330">Erreur lors de l'obtention des attributs de fichier : [3]. GetLastError : [2].</String>
   <String Id="IDS_ERROR_2331">Erreur lors du chargement de la bibliothèque [2] ou lors de la recherche du point d'entrée [3].</String>
   <String Id="IDS_ERROR_2332">Erreur lors de l'obtention des attributs de fichier. GetLastError : [2].</String>
   <String Id="IDS_ERROR_2333">Erreur lors de la définition des attributs de fichier. GetLastError : [2].</String>
   <String Id="IDS_ERROR_2334">Erreur lors de la conversion de l'heure de fichier en heure locale pour le fichier : [3]. GetLastError : [2].</String>
   <String Id="IDS_ERROR_2335">Chemin d'accès : [2] n'est pas un parent de [3].</String>
   <String Id="IDS_ERROR_2336">Erreur lors de la création d'un fichier temporaire dans le chemin d'accès : [3]. GetLastError : [2].</String>
   <String Id="IDS_ERROR_2337">Impossible de fermer le fichier : [3] GetLastError : [2].</String>
   <String Id="IDS_ERROR_2338">Impossible de mettre à jour la ressource pour le fichier : [3] GetLastError : [2].</String>
   <String Id="IDS_ERROR_2339">Impossible de définir l'heure du fichier : [3] GetLastError : [2].</String>
   <String Id="IDS_ERROR_2340">Impossible de mettre à jour la ressource pour le fichier : [3], ressource manquante.</String>
   <String Id="IDS_ERROR_2341">Impossible de mettre à jour la ressource pour le fichier : [3], ressource trop volumineuse.</String>
   <String Id="IDS_ERROR_2342">Impossible de mettre à jour la ressource pour le fichier : [3] GetLastError : [2].</String>
   <String Id="IDS_ERROR_2343">Le chemin d'accès spécifié est vide.</String>
   <String Id="IDS_ERROR_2344">Impossible de trouver le fichier IMAGEHLP.DLL nécessaire pour valider le fichier : [2].</String>
   <String Id="IDS_ERROR_2345">[2] : le fichier ne contient pas de valeur de somme de contrôle valide.</String>
   <String Id="IDS_ERROR_2347">Utilisateur ignoré.</String>
   <String Id="IDS_ERROR_2348">Erreur lors de la tentative de lecture du flux du fichier CAB.</String>
   <String Id="IDS_ERROR_2349">Reprise de la copie avec des informations différentes.</String>
   <String Id="IDS_ERROR_2350">Erreur du serveur FDI</String>
   <String Id="IDS_ERROR_2351">Clé de fichier '[2]' introuvable dans le fichier CAB '[3]'. Impossible de poursuivre l'installation.</String>
   <String Id="IDS_ERROR_2352">Impossible d'initialiser le serveur de fichiers CAB. Le fichier 'CABINET.DLL' requis est peut-être manquant.</String>
   <String Id="IDS_ERROR_2353">Il ne s'agit pas d'un fichier CAB.</String>
   <String Id="IDS_ERROR_2354">Impossible de gérer le fichier CAB.</String>
   <String Id="IDS_ERROR_2355">Fichier CAB endommagé.</String>
   <String Id="IDS_ERROR_2356">Impossible de localiser le fichier CAB dans le flux : [2].</String>
   <String Id="IDS_ERROR_2357">Impossible de définir des attributs.</String>
   <String Id="IDS_ERROR_2358">Erreur lors de la détermination de l'utilisation du fichier : [3]. GetLastError : [2].</String>
   <String Id="IDS_ERROR_2359">Impossible de créer le fichier cible - il est peut-être en cours d'utilisation.</String>
   <String Id="IDS_ERROR_2360">Marque de progression.</String>
   <String Id="IDS_ERROR_2361">Fichier CAB suivant requis.</String>
   <String Id="IDS_ERROR_2362">Dossier introuvable : [2].</String>
   <String Id="IDS_ERROR_2363">Impossible d'énumérer les sous-dossiers du dossier : [2].</String>
   <String Id="IDS_ERROR_2364">Constante d'énumération erronée dans l'appel CreateCopier.</String>
   <String Id="IDS_ERROR_2365">Impossible d'effectuer une action BindImage sur le fichier exécutable [2].</String>
   <String Id="IDS_ERROR_2366">Échec de l'utilisateur.</String>
   <String Id="IDS_ERROR_2367">Abandon de l'utilisateur.</String>
   <String Id="IDS_ERROR_2368">Échec d'obtention des informations de ressources réseau. Erreur [2], chemin d'accès réseau [3]. Erreur étendue : fournisseur réseau [5], code d'erreur [4], description de l'erreur [6].</String>
   <String Id="IDS_ERROR_2370">Valeur de somme de contrôle CRC non valide pour le fichier [2].{Son en-tête indique une somme de contrôle de [3] et sa valeur calculée est [4].}</String>
   <String Id="IDS_ERROR_2371">Impossible d'appliquer le correctif au fichier [2]. GetLastError : [3].</String>
   <String Id="IDS_ERROR_2372">Le fichier correctif [2] est endommagé ou son format n'est pas valide. Tentative d'application du correctif au fichier [3]. GetLastError : [4].</String>
   <String Id="IDS_ERROR_2373">Le fichier [2] n'est pas un fichier correctif valide.</String>
   <String Id="IDS_ERROR_2374">Le fichier [2] n'est pas un fichier de destination valide pour le fichier correctif [3].</String>
   <String Id="IDS_ERROR_2375">Erreur d'application de correctif inconnue : [2].</String>
   <String Id="IDS_ERROR_2376">Fichier CAB introuvable.</String>
   <String Id="IDS_ERROR_2379">Erreur lors de l'ouverture du fichier pour lecture : [3] GetLastError : [2].</String>
   <String Id="IDS_ERROR_2380">Erreur lors de l'ouverture du fichier pour écriture : [3]. GetLastError : [2].</String>
   <String Id="IDS_ERROR_2381">Le répertoire n'existe pas : [2].</String>
   <String Id="IDS_ERROR_2382">Lecteur non prêt : [2].</String>
   <String Id="IDS_ERROR_2401">Tentative d'exécution d'une opération de Registre 64 bits sur un système d'exploitation 32 bits pour la clé [2].</String>
   <String Id="IDS_ERROR_2402">Mémoire insuffisante.</String>
   <String Id="IDS_ERROR_2501">Impossible de créer un énumérateur de script de restauration.</String>
   <String Id="IDS_ERROR_2502">Appel d'InstallFinalize alors qu'aucune installation n'est en cours.</String>
   <String Id="IDS_ERROR_2503">Appel de RunScript alors qu'il n'est pas signalé comme étant en cours de progression.</String>
   <String Id="IDS_ERROR_2601">Valeur non valide pour la propriété [2] : '[3]'</String>
   <String Id="IDS_ERROR_2602">L'entrée '[3]' de la table [2] n'est associée à aucune entrée dans la table des médias.</String>
   <String Id="IDS_ERROR_2603">Nom de table [2] en double.</String>
   <String Id="IDS_ERROR_2604">Propriété [2] non définie.</String>
   <String Id="IDS_ERROR_2605">Impossible de trouver le serveur [2] dans [3] ou [4].</String>
   <String Id="IDS_ERROR_2606">La valeur de la propriété [2] n'est pas un chemin d'accès complet valide : '[3]'.</String>
   <String Id="IDS_ERROR_2607">Table des médias introuvable ou vide (nécessaire pour l'installation des fichiers).</String>
   <String Id="IDS_ERROR_2608">Impossible de créer un descripteur de sécurité pour l'objet. Erreur : '[2]'.</String>
   <String Id="IDS_ERROR_2609">Tentative de migration des paramètres du produit avant l'initialisation.</String>
   <String Id="IDS_ERROR_2611">Le fichier [2] est marqué comme étant compressé, mais l'entrée de média associée ne spécifie pas de fichier CAB.</String>
   <String Id="IDS_ERROR_2612">Flux introuvable dans la colonne '[2]'. Clé primaire : '[3]'.</String>
   <String Id="IDS_ERROR_2613">Action RemoveExistingProducts incorrectement mise en séquence.</String>
   <String Id="IDS_ERROR_2614">Impossible d'accéder à l'objet IStorage à partir du package d'installation.</String>
   <String Id="IDS_ERROR_2615">Annulation de l'inscription du module [2] ignorée en raison d'un échec de résolution de la source.</String>
   <String Id="IDS_ERROR_2616">Parent du fichier compagnon [2] manquant.</String>
   <String Id="IDS_ERROR_2617">Composant partagé [2] introuvable dans la table des composants.</String>
   <String Id="IDS_ERROR_2618">Composant d'application isolé [2] introuvable dans la table des composants.</String>
   <String Id="IDS_ERROR_2619">Les composants isolés [2] et [3] ne font pas partie de la même fonctionnalité.</String>
   <String Id="IDS_ERROR_2620">Fichier clé du composant d'application isolé [2] introuvable dans la table des fichiers.</String>
   <String Id="IDS_ERROR_2621">Informations sur la DLL ou l'ID de ressource pour le raccourci [2] incorrectement définies.</String>
   <String Id="IDS_ERROR_2701">La profondeur d'une fonctionnalité dépasse la profondeur d'arborescence acceptable de [2] niveaux.</String>
   <String Id="IDS_ERROR_2702">Un enregistrement de la table des fonctionnalités ([2]) fait référence à un parent inexistant dans le champ Attributs.</String>
   <String Id="IDS_ERROR_2703">Nom de propriété du chemin source racine non défini : [2]</String>
   <String Id="IDS_ERROR_2704">Propriété du répertoire racine non définie : [2]</String>
   <String Id="IDS_ERROR_2705">Table non valide : [2] ; impossible de la lier en tant qu'arborescence.</String>
   <String Id="IDS_ERROR_2706">Chemins d'accès sources non créés. Il n'existe aucun chemin d'accès pour l'entrée [2] dans la table des répertoires.</String>
   <String Id="IDS_ERROR_2707">Chemins d'accès cibles non créés. Il n'existe aucun chemin d'accès pour l'entrée [2] dans la table des répertoires.</String>
   <String Id="IDS_ERROR_2708">Aucune entrée trouvée dans la table des fichiers.</String>
   <String Id="IDS_ERROR_2709">Nom de composant spécifié ('[2]') introuvable dans la table des composants.</String>
   <String Id="IDS_ERROR_2710">L'état 'Sélectionner' demandé n'est pas autorisé pour ce composant.</String>
   <String Id="IDS_ERROR_2711">Nom de fonctionnalité spécifié ('[2]') introuvable dans la table des fonctionnalités.</String>
   <String Id="IDS_ERROR_2712">Renvoi non valide de la boîte de dialogue non modale : [3], dans l'action [2].</String>
   <String Id="IDS_ERROR_2713">Valeur Null dans une colonne n'acceptant pas les valeurs Null ('[2]' dans la colonne '[3]' de la table '[4]'.</String>
   <String Id="IDS_ERROR_2714">Valeur non valide pour le nom de dossier par défaut : [2].</String>
   <String Id="IDS_ERROR_2715">Fichier de clé spécifié ('[2]') introuvable dans la table des fichiers.</String>
   <String Id="IDS_ERROR_2716">Impossible de créer un nom de sous-composant aléatoire pour le composant '[2]'.</String>
   <String Id="IDS_ERROR_2717">Condition d'action incorrecte ou erreur lors de l'appel de l'action personnalisée '[2]'.</String>
   <String Id="IDS_ERROR_2718">Nom de package manquant pour le code produit '[2]'.</String>
   <String Id="IDS_ERROR_2719">Chemin d'accès UNC ou chemin d'accès à la lettre du lecteur introuvable dans la source '[2]'.</String>
   <String Id="IDS_ERROR_2720">Erreur lors de l'ouverture de la clé de liste de sources. Erreur : '[2]'</String>
   <String Id="IDS_ERROR_2721">Action personnalisée [2] introuvable dans le flux des tables binaires.</String>
   <String Id="IDS_ERROR_2722">Action personnalisée [2] introuvable dans la table des fichiers.</String>
   <String Id="IDS_ERROR_2723">L'action personnalisée [2] spécifie un type non pris en charge.</String>
   <String Id="IDS_ERROR_2724">Le nom de volume '[2]' du média à partir duquel vous exécutez l'application ne correspond pas au nom '[3]' indiqué dans la table des médias. Ce cas de figure est uniquement autorisé si la table des médias ne contient qu'une seule entrée.</String>
   <String Id="IDS_ERROR_2725">Tables de base de données non valides</String>
   <String Id="IDS_ERROR_2726">Action introuvable : [2].</String>
   <String Id="IDS_ERROR_2727">L'entrée de répertoire '[2]' n'existe pas dans la table des répertoires.</String>
   <String Id="IDS_ERROR_2728">Erreur de définition de la table : [2]</String>
   <String Id="IDS_ERROR_2729">Moteur d'installation non initialisé.</String>
   <String Id="IDS_ERROR_2730">Valeur incorrecte dans la base de données. Table : '[2]' ; clé primaire : '[3]' ; colonne : '[4]'</String>
   <String Id="IDS_ERROR_2731">Gestionnaire de sélection non initialisé.</String>
   <String Id="IDS_ERROR_2732">Gestionnaire d'annuaires non initialisé.</String>
   <String Id="IDS_ERROR_2733">Clé étrangère ('[2]') incorrecte dans la colonne '[3]' de la table '[4]'.</String>
   <String Id="IDS_ERROR_2734">Caractère de mode de réinstallation non valide.</String>
   <String Id="IDS_ERROR_2735">L'action personnalisée '[2]' a provoqué une exception non gérée et a été arrêtée. Ceci peut être dû à une erreur interne de l'action personnalisée, telle qu'une violation d'accès.</String>
   <String Id="IDS_ERROR_2736">Échec de génération du fichier temporaire de l'action personnalisée : [2].</String>
   <String Id="IDS_ERROR_2737">Impossible d'accéder à l'action personnalisée [2], entrée [3], bibliothèque [4]</String>
   <String Id="IDS_ERROR_2738">Impossible d'accéder à l'exécution VBScript pour l'action personnalisée [2].</String>
   <String Id="IDS_ERROR_2739">Impossible d'accéder à l'exécution JavaScript pour l'action personnalisée [2].</String>
   <String Id="IDS_ERROR_2740">Action personnalisée [2], erreur de script [3], [4] : [5] ligne [6], colonne [7], [8].</String>
   <String Id="IDS_ERROR_2741">Les informations de configuration du produit [2] sont endommagées. Informations non valides : [2].</String>
   <String Id="IDS_ERROR_2742">Échec du marshaling sur le serveur : [2].</String>
   <String Id="IDS_ERROR_2743">Impossible d'exécuter l'action personnalisée [2], emplacement : [3], commande : [4].</String>
   <String Id="IDS_ERROR_2744">Échec de l'EXE appelé par l'action personnalisée [2], emplacement : [3], commande : [4].</String>
   <String Id="IDS_ERROR_2745">Transformation [2] non valide pour le package [3]. Langue attendue [4], langue trouvée [5].</String>
   <String Id="IDS_ERROR_2746">Transformation [2] non valide pour le package [3]. Produit attendu [4], produit trouvé [5].</String>
   <String Id="IDS_ERROR_2747">Transformation [2] non valide pour le package [3]. Version de produit attendue &lt; [4], version de produit trouvée [5].</String>
   <String Id="IDS_ERROR_2748">Transformation [2] non valide pour le package [3]. Version de produit attendue &lt;= [4], version de produit trouvée [5].</String>
   <String Id="IDS_ERROR_2749">Transformation [2] non valide pour le package [3]. Version de produit attendue == [4], version de produit trouvée [5].</String>
   <String Id="IDS_ERROR_2750">Transformation [2] non valide pour le package [3]. Version de produit attendue &gt;= [4], version de produit trouvée [5].</String>
   <String Id="IDS_ERROR_2751">Transformation [2] non valide pour le package [3]. Version de produit attendue &gt; [4], version de produit trouvée [5].</String>
   <String Id="IDS_ERROR_2752">Impossible d'ouvrir la transformation [2] stockée en tant que stockage enfant du package [4].</String>
   <String Id="IDS_ERROR_2753">Le fichier « [2] » n'est pas marqué pour installation.</String>
   <String Id="IDS_ERROR_2754">Le fichier « [2] » n'est pas un fichier correctif valide.</String>
   <String Id="IDS_ERROR_2755">Le serveur a renvoyé une erreur inattendue [2] lors de la tentative d'installation du package [3].</String>
   <String Id="IDS_ERROR_2756">La propriété '[2]' a été utilisée en tant que propriété de répertoire dans une ou plusieurs tables, mais aucune valeur ne lui a été assignée.</String>
   <String Id="IDS_ERROR_2757">Impossible de créer les informations récapitulatives de la transformation [2].</String>
   <String Id="IDS_ERROR_2758">La transformation [2] ne contient pas de version MSI.</String>
   <String Id="IDS_ERROR_2759">La version [3] de la transformation [2] est incompatible avec le moteur ; min : [4], max : [5].</String>
   <String Id="IDS_ERROR_2760">Transformation [2] non valide pour le package [3]. Code de mise à niveau attendu [4], trouvé [5].</String>
   <String Id="IDS_ERROR_2761">Impossible de démarrer la transaction. Le mutex global n'a pas été correctement initialisé.</String>
   <String Id="IDS_ERROR_2762">Impossible d'écrire l'enregistrement de script. La transaction n'a pas commencé.</String>
   <String Id="IDS_ERROR_2763">Impossible d'exécuter le script. La transaction n'a pas commencé.</String>
   <String Id="IDS_ERROR_2765">Nom d'assembly manquant dans la table AssemblyName : composant : [4].</String>
   <String Id="IDS_ERROR_2766">Le fichier [2] est un fichier de stockage MSI non valide.</String>
   <String Id="IDS_ERROR_2767">Aucune donnée disponible {lors de l'énumération de [2]}.</String>
   <String Id="IDS_ERROR_2768">La transformation en package correctif n'est pas valide.</String>
   <String Id="IDS_ERROR_2769">L'action personnalisée [2] n'a pas fermé [3] MSIHANDLEs.</String>
   <String Id="IDS_ERROR_2770">Dossier mis en cache [2] non défini dans la table de dossiers du cache interne.</String>
   <String Id="IDS_ERROR_2771">Un composant de la mise à niveau de la fonctionnalité [2] est manquant.</String>
   <String Id="IDS_ERROR_2772">La nouvelle fonctionnalité de mise à niveau [2] doit être une fonctionnalité feuille.</String>
   <String Id="IDS_ERROR_2801">Message inconnu -- Type [2]. Aucune action effectuée.</String>
   <String Id="IDS_ERROR_2802">Aucun éditeur trouvé pour l'événement [2].</String>
   <String Id="IDS_ERROR_2803">La vue de boîte de dialogue n'a pas trouvé d'enregistrement pour la boîte de dialogue [2].</String>
   <String Id="IDS_ERROR_2804">Lors de l'activation du contrôle [3] de la boîte de dialogue [2], CMsiDialog n'a pas pu évaluer la condition [3].</String>
   <String Id="IDS_ERROR_2806">La boîte de dialogue [2] n'a pas pu évaluer la condition [3].</String>
   <String Id="IDS_ERROR_2807">L'action [2] n'est pas reconnue.</String>
   <String Id="IDS_ERROR_2808">Le bouton par défaut est mal défini dans la boîte de dialogue [2].</String>
   <String Id="IDS_ERROR_2809">Dans la boîte de dialogue [2], les pointeurs de contrôle suivants ne forment pas un cycle. Il existe un pointeur de [3] à [4], mais il n'en existe pas d'autres.</String>
   <String Id="IDS_ERROR_2810">Dans la boîte de dialogue [2], les pointeurs de contrôle suivants ne forment pas de cycle. Il existe un pointeur de [3] et [5] à [4].</String>
   <String Id="IDS_ERROR_2811">Dans la boîte de dialogue [2], le contrôle [3] doit s'activer, mais il n'y parvient pas.</String>
   <String Id="IDS_ERROR_2812">L'événement [2] n'est pas reconnu.</String>
   <String Id="IDS_ERROR_2813">L'événement EndDialog a été appelé avec l'argument [2], mais la boîte de dialogue a un parent.</String>
   <String Id="IDS_ERROR_2814">Dans la boîte de dialogue [2], le contrôle [3] nomme un contrôle [4] inexistant comme contrôle suivant.</String>
   <String Id="IDS_ERROR_2815">La table ControlCondition contient une ligne sans condition pour la boîte de dialogue [2].</String>
   <String Id="IDS_ERROR_2816">La table EventMapping fait référence à un contrôle non valide [4] dans la boîte de dialogue [2] pour l'événement [3].</String>
   <String Id="IDS_ERROR_2817">L'événement [2] n'a pas pu définir l'attribut du contrôle [4] dans la boîte de dialogue [3].</String>
   <String Id="IDS_ERROR_2818">Dans la table ControlEvent, EndDialog contient un argument [2] non reconnu.</String>
   <String Id="IDS_ERROR_2819">Le contrôle [3] de la boîte de dialogue [2] doit être lié à une propriété.</String>
   <String Id="IDS_ERROR_2820">Tentative d'initialisation d'un gestionnaire déjà initialisé.</String>
   <String Id="IDS_ERROR_2821">Tentative d'initialisation d'une boîte de dialogue déjà initialisée : [2].</String>
   <String Id="IDS_ERROR_2822">Aucune autre méthode ne peut être appelée dans la boîte de dialogue [2] tant que tous les contrôles n'ont pas été ajoutés.</String>
   <String Id="IDS_ERROR_2823">Tentative d'initialisation d'un contrôle déjà initialisé : [3] dans la boîte de dialogue [2].</String>
   <String Id="IDS_ERROR_2824">L'attribut de boîte de dialogue [3] requiert un enregistrement d'au moins [2] champ(s).</String>
   <String Id="IDS_ERROR_2825">L'attribut de contrôle [3] requiert un enregistrement d'au moins [2] champ(s).</String>
   <String Id="IDS_ERROR_2826">Le contrôle [3] de la boîte de dialogue [2] dépasse les limites de la boîte de dialogue [4] de [5] pixels.</String>
   <String Id="IDS_ERROR_2827">La case [4] du groupe de cases d'option [3] de la boîte de dialogue [2] dépasse les limites du groupe [5] de [6] pixels.</String>
   <String Id="IDS_ERROR_2828">Tentative de suppression du contrôle [3] de la boîte de dialogue [2], mais il ne fait pas partie de cette boîte de dialogue.</String>
   <String Id="IDS_ERROR_2829">Tentative d'utilisation d'une boîte de dialogue non initialisée.</String>
   <String Id="IDS_ERROR_2830">Tentative d'utilisation d'un contrôle non initialisé dans la boîte de dialogue [2].</String>
   <String Id="IDS_ERROR_2831">Le contrôle [3] de la boîte de dialogue [2] ne prend pas en charge [5] l'attribut [4].</String>
   <String Id="IDS_ERROR_2832">La boîte de dialogue [2] ne prend pas en charge l'attribut [3].</String>
   <String Id="IDS_ERROR_2833">Le contrôle [4] de la boîte de dialogue [3] a ignoré le message [2].</String>
   <String Id="IDS_ERROR_2834">Les pointeurs suivants de la boîte de dialogue [2] ne forment pas une boucle unique.</String>
   <String Id="IDS_ERROR_2835">Le contrôle [2] est introuvable dans la boîte de dialogue [3].</String>
   <String Id="IDS_ERROR_2836">Le contrôle [3] de la boîte de dialogue [2] ne peut pas s'activer.</String>
   <String Id="IDS_ERROR_2837">Le contrôle [3] de la boîte de dialogue [2] attend que winproc renvoie [4].</String>
   <String Id="IDS_ERROR_2838">L'élément [2] de la table de sélection est son propre parent.</String>
   <String Id="IDS_ERROR_2839">Échec de définition de la propriété [2].</String>
   <String Id="IDS_ERROR_2840">Erreur liée à un problème de correspondance du nom de boîte de dialogue.</String>
   <String Id="IDS_ERROR_2841">Bouton OK introuvable dans la boîte de dialogue d'erreur.</String>
   <String Id="IDS_ERROR_2842">Champ de texte introuvable dans la boîte de dialogue d'erreur.</String>
   <String Id="IDS_ERROR_2843">L'attribut ErrorString n'est pas pris en charge pour les boîtes de dialogue standard.</String>
   <String Id="IDS_ERROR_2844">Impossible d'exécuter une boîte de dialogue d'erreur si Errorstring n'est pas défini.</String>
   <String Id="IDS_ERROR_2845">La largeur totale des boutons dépasse la taille de la boîte de dialogue d'erreur.</String>
   <String Id="IDS_ERROR_2846">SetFocus n'a pas trouvé le contrôle requis dans la boîte de dialogue d'erreur.</String>
   <String Id="IDS_ERROR_2847">Le contrôle [3] de la boîte de dialogue [2] a à la fois un style d'icône et de bitmap définis.</String>
   <String Id="IDS_ERROR_2848">Tentative de définition du contrôle [3] comme bouton par défaut dans la boîte de dialogue [2], mais ce contrôle n'existe pas.</String>
   <String Id="IDS_ERROR_2849">Le type de contrôle [3] de la boîte de dialogue [2] n'accepte pas de nombres entiers.</String>
   <String Id="IDS_ERROR_2850">Type de volume non reconnu.</String>
   <String Id="IDS_ERROR_2851">Les données de l'icône [2] ne sont pas valides.</String>
   <String Id="IDS_ERROR_2852">Au moins un contrôle doit être ajouté à la boîte de dialogue [2] pour pouvoir être utilisé.</String>
   <String Id="IDS_ERROR_2853">La boîte de dialogue [2] est non modale. La méthode d'exécution ne doit pas être appelée sur cette boîte de dialogue.</String>
   <String Id="IDS_ERROR_2854">Dans la boîte de dialogue [2], le contrôle [3] est désigné comme premier contrôle actif, mais ce dernier n'existe pas.</String>
   <String Id="IDS_ERROR_2855">Le groupe de cases d'option [3] de la boîte de dialogue [2] contient moins de 2 cases.</String>
   <String Id="IDS_ERROR_2856">Création d'une deuxième copie de la boîte de dialogue [2].</String>
   <String Id="IDS_ERROR_2857">Le répertoire [2] est mentionné dans la table de sélection, mais est introuvable.</String>
   <String Id="IDS_ERROR_2858">Les données du bitmap [2] ne sont pas valides.</String>
   <String Id="IDS_ERROR_2859">Message d'erreur test.</String>
   <String Id="IDS_ERROR_2860">Le bouton Annuler est mal défini dans la boîte de dialogue [2].</String>
   <String Id="IDS_ERROR_2861">Les pointeurs suivants des cases d'option du contrôle [3] de la boîte de dialogue [2] ne forment pas de cycle.</String>
   <String Id="IDS_ERROR_2862">Les attributs du contrôle [3] de la boîte de dialogue [2] ne définissent pas une taille d'icône valide. Définition de la taille à 16.</String>
   <String Id="IDS_ERROR_2863">Le contrôle [3] de la boîte de dialogue [2] requiert l'icône [4] de taille [5]x[5], mais cette taille n'est pas disponible. Chargement de la première taille disponible.</String>
   <String Id="IDS_ERROR_2864">Le contrôle [3] de la boîte de dialogue [2] a reçu un événement de navigation, mais il n'existe pas de répertoire configurable pour la sélection actuelle. Cause probable : le bouton Parcourir n'a pas été créé correctement.</String>
   <String Id="IDS_ERROR_2865">Le contrôle [3] du panneau d'affichage [2] dépasse les limites du panneau d'affichage [4] de [5] pixels.</String>
   <String Id="IDS_ERROR_2866">La boîte de dialogue [2] n'est pas autorisée à renvoyer l'argument [3].</String>
   <String Id="IDS_ERROR_2867">La propriété de la boîte de dialogue d'erreur n'est pas définie.</String>
   <String Id="IDS_ERROR_2868">Le bit de style d'erreur n'est pas défini pour la boîte de dialogue d'erreur [2].</String>
   <String Id="IDS_ERROR_2869">Le bit de style d'erreur est défini pour la boîte de dialogue [2], mais cette dernière n'est pas une boîte de dialogue d'erreur.</String>
   <String Id="IDS_ERROR_2870">La chaîne d'aide [4] du contrôle [3] de la boîte de dialogue [2] ne contient pas de caractère de séparation.</String>
   <String Id="IDS_ERROR_2871">La table [2] est périmée : [3].</String>
   <String Id="IDS_ERROR_2872">L'argument de l'événement de contrôle CheckPath dans la boîte de dialogue [2] n'est pas valide.</String>
   <String Id="IDS_ERROR_2873">Dans la boîte de dialogue [2], la longueur de la chaîne de contrôle [3] n'est pas valide : [4].</String>
   <String Id="IDS_ERROR_2874">Échec du remplacement de la police du texte par la police [2].</String>
   <String Id="IDS_ERROR_2875">Échec du remplacement de la couleur du texte par la couleur [2].</String>
   <String Id="IDS_ERROR_2876">Le contrôle [3] de la boîte de dialogue [2] a dû tronquer la chaîne : [4].</String>
   <String Id="IDS_ERROR_2877">Données binaires [2] introuvables</String>
   <String Id="IDS_ERROR_2878">Dans la boîte de dialogue [2], le contrôle [3] a pour valeur possible : [4]. Il s'agit d'une valeur non valide ou en double.</String>
   <String Id="IDS_ERROR_2879">Le contrôle [3] de la boîte de dialogue [2] ne peut pas analyser la chaîne de masque : [4].</String>
   <String Id="IDS_ERROR_2880">N'exécutez pas les événements de contrôle restants.</String>
   <String Id="IDS_ERROR_2881">Échec d'initialisation de CMsiHandler.</String>
   <String Id="IDS_ERROR_2882">Échec d'inscription de la classe de fenêtre de boîte de dialogue.</String>
   <String Id="IDS_ERROR_2883">Échec de CreateNewDialog pour la boîte de dialogue [2].</String>
   <String Id="IDS_ERROR_2884">Échec de création d'une fenêtre pour la boîte de dialogue [2].</String>
   <String Id="IDS_ERROR_2885">Échec de création du contrôle [3] dans la boîte de dialogue [2].</String>
   <String Id="IDS_ERROR_2886">Échec de création de la table [2].</String>
   <String Id="IDS_ERROR_2887">Échec de création d'un curseur dans la table [2].</String>
   <String Id="IDS_ERROR_2888">Échec d'exécution de la vue [2].</String>
   <String Id="IDS_ERROR_2889">Échec de création de la fenêtre pour le contrôle [3] dans la boîte de dialogue [2].</String>
   <String Id="IDS_ERROR_2890">Échec du gestionnaire lors de la création d'une boîte de dialogue initialisée.</String>
   <String Id="IDS_ERROR_2891">Échec de suppression de la fenêtre pour la boîte de dialogue [2].</String>
   <String Id="IDS_ERROR_2892">[2] est un contrôle d'entier uniquement, [3] n'est pas un entier valide.</String>
   <String Id="IDS_ERROR_2893">Le contrôle [3] de la boîte de dialogue [2] peut accepter des valeurs de propriétés de [5] caractères maximum. La valeur [4] dépasse la limite et a été tronquée.</String>
   <String Id="IDS_ERROR_2894">Échec de chargement de RICHED20.DLL. GetLastError() a renvoyé : [2].</String>
   <String Id="IDS_ERROR_2895">Échec de libération de RICHED20.DLL. GetLastError() a renvoyé : [2].</String>
   <String Id="IDS_ERROR_2896">Échec d'exécution de l'action [2].</String>
   <String Id="IDS_ERROR_2897">Échec de création d'une police [2] sur ce système.</String>
   <String Id="IDS_ERROR_2898">Pour le style de texte [2], le système a créé la police '[3]' dans le jeu de caractères [4].</String>
   <String Id="IDS_ERROR_2899">Échec de création du style de texte [2]. GetLastError() a renvoyé : [3].</String>
   <String Id="IDS_ERROR_2901">Paramètre non valide pour l'opération [2] : paramètre [3].</String>
   <String Id="IDS_ERROR_2902">Opération [2] appelée hors séquence.</String>
   <String Id="IDS_ERROR_2903">Le fichier [2] est manquant.</String>
   <String Id="IDS_ERROR_2904">Impossible d'exécuter l'action BindImage sur le fichier [2].</String>
   <String Id="IDS_ERROR_2905">Impossible de lire l'enregistrement à partir du fichier de script [2].</String>
   <String Id="IDS_ERROR_2906">En-tête manquant dans le fichier de script [2].</String>
   <String Id="IDS_ERROR_2907">Impossible de créer un descripteur de sécurité sécurisé. Erreur : [2].</String>
   <String Id="IDS_ERROR_2908">Impossible d'inscrire le composant [2].</String>
   <String Id="IDS_ERROR_2909">Impossible d'annuler l'inscription du composant [2].</String>
   <String Id="IDS_ERROR_2910">Impossible de déterminer l'ID de sécurité de l'utilisateur.</String>
   <String Id="IDS_ERROR_2911">Impossible de supprimer le dossier [2].</String>
   <String Id="IDS_ERROR_2912">Impossible de planifier la suppression du fichier [2] lors du redémarrage.</String>
   <String Id="IDS_ERROR_2919">Aucun fichier CAB spécifié pour le fichier compressé : [2].</String>
   <String Id="IDS_ERROR_2920">Répertoire source non spécifié pour le fichier [2].</String>
   <String Id="IDS_ERROR_2924">Version de script [2] non prise en charge. Version de script : [3], version minimale : [4], version maximale : [5].</String>
   <String Id="IDS_ERROR_2927">L'ID ShellFolder [2] n'est pas valide.</String>
   <String Id="IDS_ERROR_2928">Nombre maximal de sources dépassé. Source '[2]' ignorée.</String>
   <String Id="IDS_ERROR_2929">Impossible de déterminer la racine de publication. Erreur : [2].</String>
   <String Id="IDS_ERROR_2932">Impossible de créer le fichier [2] à partir des données de script. Erreur : [3].</String>
   <String Id="IDS_ERROR_2933">Impossible d'initialiser le script de restauration [2].</String>
   <String Id="IDS_ERROR_2934">Impossible de sécuriser la transformation [2]. Erreur [3].</String>
   <String Id="IDS_ERROR_2935">Impossible de supprimer la sécurité de la transformation [2]. Erreur [3].</String>
   <String Id="IDS_ERROR_2936">Transformation [2] introuvable.</String>
   <String Id="IDS_ERROR_2937">Windows Installer ne peut pas installer un catalogue de protection de fichiers système. Catalogue : [2], erreur : [3].</String>
   <String Id="IDS_ERROR_2938">Windows Installer ne peut pas extraire un catalogue de protection de fichiers système du cache. Catalogue : [2], erreur : [3].</String>
   <String Id="IDS_ERROR_2939">Windows Installer ne peut pas supprimer un catalogue de protection de fichiers système du cache. Catalogue : [2], erreur : [3].</String>
   <String Id="IDS_ERROR_2940">Gestionnaire d'annuaires non fourni pour la résolution de la source.</String>
   <String Id="IDS_ERROR_2941">Impossible de calculer le CRC du fichier [2].</String>
   <String Id="IDS_ERROR_2942">L'action BindImage n'a pas été exécutée sur le fichier [2].</String>
   <String Id="IDS_ERROR_2943">Cette version de Windows ne prend pas en charge le déploiement de packages 64 bits. Le script [2] est conçu pour un package 64 bits.</String>
   <String Id="IDS_ERROR_2944">Échec de GetProductAssignmentType.</String>
   <String Id="IDS_ERROR_2945">Échec d'installation de ComPlus App [2] avec l'erreur [3].</String>
   <String Id="IDS_ERROR_3001">Les correctifs figurant dans cette liste contiennent des informations de séquencement incorrectes : [2][3][4][5][6][7][8][9][10][11][12][13][14][15][16].</String>
   <String Id="IDS_ERROR_3002">Le correctif [2] contient des informations de séquencement non valides. </String>
   <String Id="IDS_ERROR_25032">Le programme d'installation n'a pas pu installer le pilote LSI.</String>
   <String Id="IDS_ERROR_25520">Échec de la création du descripteur de sécurité pour [3]\[4], erreur système : [2]</String>
   <String Id="IDS_ERROR_25521">Échec de la définition du descripteur de sécurité sur l'objet [3], erreur système : [2]</String>
   <String Id="IDS_ERROR_25522">Type d'objet inconnu [3], erreur système : [2]</String>
   <String Id="IDS_ERROR_27500">Cette installation nécessite Internet Information Server 4.0 ou une version supérieure pour la configuration des racines virtuelles IIS. Vérifiez que vous disposez au minimum de IIS 4.0.</String>
   <String Id="IDS_ERROR_27501">Cette installation nécessite les privilèges Administrateur pour la configuration des racines virtuelles IIS.</String>
   <String Id="IDS_ERROR_27502">Impossible de se connecter à [2] '[3]'. [4]</String>
   <String Id="IDS_ERROR_27503">Erreur lors de la récupération de la chaîne de version à partir de [2] '[3]'. [4]</String>
   <String Id="IDS_ERROR_27504">Les conditions requises pour la version de SQL ne sont pas satisfaites : [3]. Cette installation requiert [2] [4] ou une version supérieure.</String>
   <String Id="IDS_ERROR_27505">Impossible d'ouvrir le fichier de script SQL [2].</String>
   <String Id="IDS_ERROR_27506">Erreur lors de l'exécution du script SQL [2]. Ligne [3]. [4]</String>
   <String Id="IDS_ERROR_27507">La recherche ou la connexion à des serveurs de base de données nécessite l'installation de MDAC.</String>
   <String Id="IDS_ERROR_27508">Erreur lors de l'installation de l'application COM+ [2]. [3]</String>
   <String Id="IDS_ERROR_27509">Erreur lors de la désinstallation de l'application COM+ [2]. [3]</String>
   <String Id="IDS_ERROR_27510">Erreur lors de l'installation de l'application COM+ [2]. Impossible de charger les bibliothèques de classes Microsoft(R) .NET. L'inscription des composants .NET pris en charge nécessite l'installation de Microsoft(R) .NET Framework.</String>
   <String Id="IDS_ERROR_27511">Impossible d'exécuter le fichier de script SQL [2]. Connexion non ouverte : [3]</String>
   <String Id="IDS_ERROR_27512">Erreur au début des transactions pour [2] '[3]'. Base de données [4]. [5]</String>
   <String Id="IDS_ERROR_27513">Erreur lors de la validation des transactions pour [2] '[3]'. Base de données [4]. [5]</String>
   <String Id="IDS_ERROR_27514">Cette installation requiert Microsoft SQL Server. Le serveur spécifié '[3]' est un serveur MSDE (Microsoft SQL Server Desktop Engine).</String>
   <String Id="IDS_ERROR_27515">Erreur lors de l'extraction de la version de schéma de [2] '[3]'. Base de données : '[4]'. [5]</String>
   <String Id="IDS_ERROR_27516">Erreur lors de l'écriture de la version de schéma dans [2] '[3]'. Base de données : '[4]'. [5]</String>
   <String Id="IDS_ERROR_27517">Cette installation requiert des privilèges Administrateur pour l'installation d'applications COM+. Ouvrez une session en tant qu'administrateur, puis réessayez cette installation.</String>
   <String Id="IDS_ERROR_27518">L'application COM+ « [2] » est configurée pour s'exécuter en tant que service NT ; ceci requiert COM+ 1.5 ou supérieur sur le système. Comme votre système dispose de COM+ 1.0, cette application ne sera pas installée.</String>
   <String Id="IDS_ERROR_27519">Erreur lors de la mise à jour du fichier XML [2]. [3]</String>
   <String Id="IDS_ERROR_27520">Erreur lors de l'ouverture du fichier XML [2]. [3]</String>
   <String Id="IDS_ERROR_27521">Cette installation nécessite MSXML 3.0 ou version supérieure pour la configuration des fichiers XML. Assurez-vous que vous disposez de la version 3.0 ou supérieure.</String>
   <String Id="IDS_ERROR_27522">Erreur lors de la création du fichier XML [2]. [3]</String>
   <String Id="IDS_ERROR_27523">Erreur lors du chargement des serveurs.</String>
   <String Id="IDS_ERROR_27524">Erreur lors du chargement de NetApi32.DLL. ISNetApi.dll requiert que NetApi32.DLL soit correctement chargé et qu'un système d'exploitation Windows NT soit utilisé.</String>
   <String Id="IDS_ERROR_27525">Serveur introuvable. Assurez-vous que le serveur spécifié existe. Le nom de serveur ne peut pas être vide.</String>
   <String Id="IDS_ERROR_27526">Erreur non spécifiée de ISNetApi.dll.</String>
   <String Id="IDS_ERROR_27527">La mémoire tampon est insuffisante.</String>
   <String Id="IDS_ERROR_27528">Accès refusé. Vérifiez les droits d'administration.</String>
   <String Id="IDS_ERROR_27529">Ordinateur non valide.</String>
   <String Id="IDS_ERROR_27530">Cas de commutateur indéfini.</String>
   <String Id="IDS_ERROR_27531">Exception non traitée.</String>
   <String Id="IDS_ERROR_27532">Nom d'utilisateur non valide pour ce serveur ou ce domaine.</String>
   <String Id="IDS_ERROR_27533">Les mots de passe respectant la casse ne correspondent pas.</String>
   <String Id="IDS_ERROR_27534">La liste est vide.</String>
   <String Id="IDS_ERROR_27535">Violation d'accès.</String>
   <String Id="IDS_ERROR_27536">Erreur lors de l'obtention du groupe.</String>
   <String Id="IDS_ERROR_27537">Erreur lors de l'ajout de l'utilisateur au groupe. Vérifiez que le groupe existe pour ce domaine ou ce serveur.</String>
   <String Id="IDS_ERROR_27538">Erreur lors de la création de l'utilisateur.</String>
   <String Id="IDS_ERROR_27539">ERROR_NETAPI_ERROR_NOT_PRIMARY renvoyée par NetAPI.</String>
   <String Id="IDS_ERROR_27540">L'utilisateur spécifié existe déjà.</String>
   <String Id="IDS_ERROR_27541">Le groupe spécifié existe déjà.</String>
   <String Id="IDS_ERROR_27542">Mot de passe non valide. Vérifiez que le mot de passe respecte votre stratégie de mots de passe réseau.</String>
   <String Id="IDS_ERROR_27543">Nom non valide.</String>
   <String Id="IDS_ERROR_27544">Groupe non valide.</String>
   <String Id="IDS_ERROR_27545">Le nom d'utilisateur ne peut pas être vide et il doit avoir le format DOMAINE\Nom_utilisateur.</String>
   <String Id="IDS_ERROR_27546">Erreur lors du chargement ou de la création du fichier INI dans le répertoire temporaire de l'utilisateur.</String>
   <String Id="IDS_ERROR_27547">ISNetAPI.dll n'est pas chargé ou une erreur s'est produite lors de son chargement. Cette DLL doit être chargée pour l'opération. Vérifiez qu'elle se trouve dans le répertoire SUPPORTDIR.</String>
   <String Id="IDS_ERROR_27548">Une erreur s'est produite lors de la suppression du fichier INI contenant les informations du nouvel utilisateur dans le répertoire temporaire de l'utilisateur.</String>
   <String Id="IDS_ERROR_27549">Erreur lors de l'obtention du contrôleur principal du domaine (PDC).</String>
   <String Id="IDS_ERROR_27550">Pour créer un utilisateur, chaque champ doit contenir une valeur.</String>
   <String Id="IDS_ERROR_27551">Pilote ODBC de [2] introuvable. Ce pilote est requis pour la connexion aux serveurs de base de données [2].</String>
   <String Id="IDS_ERROR_27552">Erreur lors de la création de la base de données [4]. Serveur : [2] [3]. [5]</String>
   <String Id="IDS_ERROR_27553">Erreur lors de la connexion à la base de données [4]. Serveur : [2] [3]. [5]</String>
   <String Id="IDS_ERROR_27554">Erreur lors de la tentative d'ouverture de la connexion [2]. Aucune métadonnée de base de données valide associée à cette connexion.</String>
   <String Id="IDS_ERROR_28030">Le programme d'installation n'a pas pu installer le pilote USB. Pour garantir la réussite de l'installation, redémarrez votre ordinateur et relancez le programme d'installation.</String>
   <String Id="IDS_ERROR_28033">Connexion à %s impossible. Erreur de connexion TCP au port TCP %d. Vérifiez le nom de serveur saisi.</String>
   <String Id="IDS_ERROR_28034">Connexion à LDAP impossible sur %s. Vérifiez le nom du serveur saisi.</String>
   <String Id="IDS_ERROR_28035">Informations d'identification non valides. Saisissez un nom et un mot de passe d'administrateur pour le serveur spécifié.</String>
   <String Id="IDS_ERROR_28036">Liaison impossible au LDAP sur %s. %s.</String>
   <String Id="IDS_ERROR_28037">Échec de la mise à jour de LDAP sur %s. Accès refusé. Saisissez un nom et un mot de passe d'administrateur disposant de privilèges suffisants sur le serveur spécifié.</String>
   <String Id="IDS_ERROR_28038">Échec de la mise à jour de LDAP sur %s. Violation du schéma. Il est possible que le serveur spécifié exécute une ancienne version du logiciel et doive être mis à niveau pour prendre en charge cette installation d'agent.</String>
   <String Id="IDS_ERROR_28039">Échec de la mise à jour de LDAP sur %s. %s.</String>
   <String Id="IDS_ERROR_28040">Format de nom d'utilisateur non valide.</String>
   <String Id="IDS_ERROR_28041">Cet agent est déjà inscrit sur %s. Voulez-vous continuer l'installation et mettre à jour les informations d'inscription existantes ?</String>
   <String Id="IDS_ERROR_28042">Cet agent est inscrit plusieurs fois sur %s. À l'aide d'Horizon Administrator, supprimez les entrées de cette machine, puis retentez cette installation.</String>
   <String Id="IDS_ERROR_28045">Si vous spécifiez des informations d'identification d'administrateur, vous devez saisir un nom d'utilisateur et un mot de passe.</String>
   <String Id="IDS_ERROR_28046">Échec de l'obtention d'informations de sécurité critiques du LDAP sur %s. Il est possible que le serveur spécifié exécute une ancienne version et doive être mis à niveau pour prendre en charge cette installation d'agent.</String>
   <String Id="IDS_ERROR_28053">Échec d'inscription d'une DLL. Pour plus d'informations, consultez le fichier %TEMP%\vminst*.log le plus récent.</String>
   <String Id="IDS_ERROR_28060">Erreur lors de l'installation du pilote de périphérique Intel HECI.</String>
   <String Id="IDS_ERROR_28062">Un agent pour cet ordinateur est déjà inscrit sur %s en tant que machine virtuelle. Modifiez le nom de cet ordinateur ou supprimez l'entrée de la machine virtuelle à l'aide d'Horizon Administrator sur le Serveur de connexion et retentez l'installation.</String>
   <String Id="IDS_ERROR_28065">Le programme d'installation n'a pas pu installer le pilote de Smartcard Redirector.</String>
   <String Id="IDS_ERROR_28089">Le serveur de connexion %s n'exécute pas une version logicielle suffisante pour prendre cet agent entièrement en charge. Vous devez d'abord mettre à niveau vos serveurs de connexion avant de continuer. Si vous continuez quand même, vous devrez réinstaller cet agent ultérieurement pour obtenir la fonction d'hôte RDS complète. Souhaitez-vous continuer cette installation ?</String>
   <String Id="IDS_ERROR_28090">Le programme d'installation a détecté des valeurs de registre non définies par défaut :

HKLM\SYSTEM\CurrentControlSet\Control\TerminalServer\WinStations\RDP-Tcp\MaxInstanceCount

HKLM\SYSTEM\CurrentControlSet\Control\TerminalServer\WinStations\Horizon-PCOIP\MaxInstanceCount

Étant donné qu'Horizon gère les limites de session, la présence de ces paramètres de registre génère un comportement inattendu.</String>
   <String Id="IDS_ERROR_28092">Le programme d'installation n'a pas pu installer le pilote de webcam virtuel Horizon. Pour garantir la réussite de l'installation, redémarrez votre machine et relancez ce programme d'installation.</String>
   <String Id="IDS_ERROR_28096">Please refer to the "Deploying USB Devices in a Secure Horizon Environment" document for guidance on using USB redirection securely.</String>
   <String Id="IDS_ERROR_28100">Vous installez Omnissa Horizon Agent alors que certaines vérifications du programme d'installation sont désactivées. Cela n'est pas recommandé et produira une installation non prise en charge. Annulez et relancez le programme d'installation en activant les vérifications au préalable.</String>
   <String Id="IDS_ERROR_28109">VC%d.%d nonredist n'est pas installé sur la machine. L'installation d'Horizon Agent peut échouer si vous continuez. Vous pouvez télécharger les programmes d'installation VC%d.%d nonredist depuis la build de Conan msvc_debug_runtime_installer.</String>
   <String Id="IDS_ERROR_28110">Le pack de virtualisation pour Skype Entreprise requiert que .NET Framework 4.0 ou version ultérieure soit présent avant d'installer cette fonctionnalité. Installez .NET Framework et relancez le programme d'installation pour installer cette fonctionnalité.</String>
   <String Id="IDS_ERROR_28111">Ajoutez ou supprimez une fonctionnalité pour continuer.</String>
   <String Id="IDS_ERROR_28113">Lors d'une installation à modifier, vous ne pouvez pas modifier la fonctionnalité « Instant Clone Agent » (NGVC). Si vous souhaitez ajouter ou supprimer une fonctionnalité de clonage, désinstallez l'agent, puis réinstallez-le.</String>
   <String Id="IDS_ERROR_28114">Vous devez disposer des privilèges d'administration pour modifier ou réparer cette installation. Vous pouvez également exécuter la commande suivante à partir d'une invite de commande avec élévation de privilèges :

msiexec.exe /i [DATABASE]</String>
   <String Id="IDS_ERROR_28115">Vous devez disposer des privilèges d'administration pour installer ce correctif. Vous devrez peut-être exécuter la commande suivante à partir d'une invite de commandes avec élévation de privilèges :

msiexec.exe /p [PATCH]</String>

   <String Id="IDS_ERROR_28116">Le service Spouleur d'impression n'est pas en cours d'exécution. Impossible d'installer la fonctionnalité Horizon Integrated Printing.</String>

   <!-- L10n properties for merge module services -->
   <String Id="IDS_PCOIPSG_DISPLAY_NAME">Passerelle de sécurité PCoIP d'Omnissa Horizon</String>
   <String Id="IDS_PCOIPSG_DESCRIPTION">Fournit des services de passerelle PCoIP.</String>

   <String Id="IDS_WSNM_SERVICE_DISPLAY_NAME">Omnissa Horizon Agent</String>
   <String Id="WsnmServiceDescription">Fournit les services Horizon Agent.</String>

   <String Id="IDS_WSSH_SERVICE_DISPLAY_NAME">Hôte de script Omnissa Horizon</String>
   <String Id="IDS_WSSH_SERVICE_DESCRIPTION">Fournit les services d'hôte de script.</String>

   <String Id="IDS_VMLM_SERVICE_DISPLAY_NAME">Moniteur d'ouverture de session Omnissa Horizon</String>
   <String Id="IDS_VMLM_SERVICE_DESCRIPTION">Fournit des services de surveillance des ouvertures de session.</String>

   <String Id="IDS_HZMON_SERVICE_DISPLAY_NAME">Omnissa Horizon Monitoring Service</String>
   <String Id="IDS_HZMON_SERVICE_DESCRIPTION">Fournit des services de surveillance.</String>

   <String Id="IDS_VMWRXG_SERVICE_DISPLAY_NAME">Service d'expérience à distance Omnissa Horizon</String>
   <String Id="IDS_VMWRXG_SERVICE_DESCRIPTION">Fournit des services génériques d'expérience à distance.</String>

   <String Id="IDS_AUTORESTART">Redémarrage automatique du système une fois l'opération réussie</String>

   <String Id="IDS_AUTORESTART_MODIFY">Redémarrage automatique du système une fois l'opération réussie, si nécessaire</String>

</WixLocalization>
