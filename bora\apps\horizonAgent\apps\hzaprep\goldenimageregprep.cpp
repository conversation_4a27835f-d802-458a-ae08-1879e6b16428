/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */


/* ----------------------------------------------
 * goldenimageregprep.cpp
 * ---------------------------------------------- */

#include "stdafx.h"
#include "goldenimageregprep.h"


/*
 *----------------------------------------------------------------------------
 *
 * GoldenImageRegPrep::GoldenImageRegPrep --
 *
 *      Constructor
 *
 * Returns:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

GoldenImageRegPrep::GoldenImageRegPrep() : mRegistration(false) {}


/*
 *----------------------------------------------------------------------------
 *
 * GoldenImageRegPrep::~GoldenImageRegPrep --
 *
 *      Destructor
 *
 * Returns:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

GoldenImageRegPrep::~GoldenImageRegPrep()
{
   /*
    * If the mRegistration flag is not set to true then remove the created
    * master key and pairing helper keypair as we are exiting here and
    * the registration has failed.
    */
   if (!mRegistration) {
      // Delete the keypair and master key.
      if (!remove(mPHsKeyPairName)) {
         SYSMSG_FUNC(Error, L"Couldn't delete keypair %s", mPHsKeyPairName);
      }
      if (!remove(mMasterKeyName)) {
         SYSMSG_FUNC(Error, L"Couldn't delete master key %s", mMasterKeyName);
      }
   }
}


/*
 *----------------------------------------------------------------------------
 *
 * GoldenImageRegPrep::RegisterGoldenImage --
 *
 *      Prepares input parameters and sends RegisterGoldenImage message to the
 *      broker (FQDN provided in function parameter).
 *
 * Returns:
 *      true if golden image registration is successful otherwise false.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

bool
GoldenImageRegPrep::registerGoldenImage(wstr brokerFQDN)
{
   MsgBinary nonce, signature, pubKey;
   wstr vmGuid;

   // Create master key (persistent) and pairing helper key pair.
   bool result = createPersistentKeyPair(mPHsKeyPairName, VAL_KEY_LENGTH);

   if (!result) {
      SYSMSG_FUNC(Error, L"Pairing helper signing keypair creation failed.");
      return false;
   }

   // Generate the Nonce
   if (!generateNonce(VAL_NONCE_SIZE, nonce)) {
      SYSMSG_FUNC(Error, L"Nonce creation failed.");
      return false;
   }

   // Fetch the VM GUID
   if (!getVmGuid(vmGuid)) {
      SYSMSG_FUNC(Error, L"VM Guid couldn't be retrieved.");
      return false;
   }

   /*
    * Retrieve the public key and the PoP blob from keyVault.
    * The sigdata will be formed by concatenating the nonce and VM guid and
    * then hashing the concatented data (binary + string) to create HASH1.
    * TODO: At present only concatenation is done and hashing will be added
    * later.
    */
   wstr sigData;
   sigData.setBinary(nonce.pBinary, nonce.sBinary);
   sigData.append(vmGuid);

   // Get the public key from KeyVault.
   if (!getPublicKey(mPHsKeyPairName, sigData, pubKey, signature)) {
      SYSMSG_FUNC(Error,
                  L"Couldn't retrieve public key corresponding to "
                  L"%s keypair from KeyVault.",
                  mPHsKeyPairName);
      return false;
   }

   // TODO: Hash HASH1 with PHs public key and PoP blob to produce HASH2.
   // TODO: Sign HASH2 with the pre-pairing keypair to produce REG_SIG.

   PropertyBag request, response;
   request.add(PAR_GOLDEN_IMAGE_GUID, vmGuid); // String
   request.addBinary(PAR_PHS_PUBLIC_KEY, pubKey.pBinary, pubKey.sBinary);
   request.addBinary(PAR_NONCE, nonce.pBinary, nonce.sBinary);

   // Copy PoP blob generated while retrieving public key.
   request.addBinary(PAR_PHS_POP_BLOB, signature.pBinary, signature.sBinary);
   /*
    * TODO: Signing needs to be done.
    * We are not signing yet, so for now copying the PoP blob as the
    * signature so that PropertyBag has all the agreed upon values.
    */
   request.addBinary(PAR_SIGNATURE, signature.pBinary, signature.sBinary);

   if (!connectAndSend(brokerFQDN, QUEUE_AGENT_PAIRING, HINT_GI_REGISTRATION, request, response)) {
      SYSMSG_FUNC(Error,
                  L"Failed to register as a golden image. Error: (%d) "
                  L"%s",
                  response.getError(), response.getErrorText());
      return false;
   }

   /*
    * TODO 1: Hash the received text with the nonce.
    * TODO 2: Verify the BROKER_SIG using the broker public key and the hash
    * (this assures the broker identity and message authenticity, and
    * protects against replay)
    * At present if the broker successfully processes the RegisterGoldenImage
    * message it will send in response a binary item "resultSig" which must be
    * the same as the originally supplied nonce, and a string "resultText"
    * with contents "Ack". Both the binary data and the string need to be
    * verified.
    */

   void *data = NULL;
   size_t dataLen = 0;
   response.getBinary(PAR_RESULT_SIG, &data, &dataLen);
   wstr resultText = response.get(PAR_RESULT_TEXT, L"");

   if (!data || !dataLen) {
      SYSMSG_FUNC(Error, L"Signature data is missing.");
      return false;
   }

   MsgBinary resultSig;
   resultSig.set(data, dataLen, true);
   if (!resultSig.compare(nonce)) {
      SYSMSG_FUNC(Error, L"Mismatched signature data received.");
      return false;
   }

   if (resultText.empty()) {
      SYSMSG_FUNC(Error, L"Acknowledgement text is missing.");
      return false;
   }

   if (resultText.compare(ACKSTR)) {
      SYSMSG_FUNC(Error, L"Mismatched acknowledgement text received");
      return false;
   }

   mRegistration = true;
   SYSMSG_FUNC(Debug, L"Successfully registered the machine as golden image.");

   return true;
}
