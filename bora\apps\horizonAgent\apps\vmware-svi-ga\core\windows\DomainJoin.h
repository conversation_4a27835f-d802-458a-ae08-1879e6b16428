/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

#pragma once

#include <common/util/DomainServices.h>
#include <core/util/CustomizationState.h>
#include <core/windows/Network.h>

#define NETSETUP_JOIN_WITH_NEW_NAME 0x00000400

#define MAX_PWDCHANGE_RETRIES 5
#define MAX_PWDCHANGE_IPV6_DELAY 20000
#define MAX_TRUSTVERIFY_RETRIES 5
#define DEFAULT_TRUSTVERIFY_DELAY 1000
#define DEFAULT_TRUSTVERIFY_IPV6_DELAY 5000
#define MAX_DOMAIN_JOIN_RETRIES 10
#define DEFAULT_DOMAIN_JOIN_REBOOT_DELAY 10000

#define DOMAIN_JOIN_RETRIES_VALUE_NAME _T("DomainJoinRetries")
#define TRUST_VERIFY_RETRIES_VALUE_NAME _T("TrustVerifyRetries")
#define TRUST_VERIFY_DELAY_VALUE_NAME _T("TrustVerifyDelay")
#define PWD_CHANGE_RETRIES_VALUE_NAME _T("PwdChangeRetries")
#define PWD_CHANGE_IPV6_DELAY_VALUE_NAME _T("PwdChangeIpv6Delay")
#define SVM_NGA_UPN_NAME _T("NameUserPrincipal")
#define CURRENT_DC_VALUE_NAME _T("CurrentDC")

#define DOMAIN_JOIN_REBOOT_DELAY_VALUE_NAME _T("DomainJoinRebootDelay")

#define MACHINE_PWD_VALUE_NAME "machinePasswd"
#define HOSTNAME_VALUE_NAME "hostName"
#define PREFERRED_DC_VALUE_NAME "preferredDC"
#define PREFERRED_SITE_VALUE_NAME "preferredSite"
#define FQDN_VALUE_NAME "primed.fqdn"
#define MACHINE_PWD_CHECKSUM_VALUE_NAME "machinePasswdChecksum"

namespace svmga {
namespace core {
namespace windows {

namespace coreutil = svmga::core::util;
namespace commutil = svmga::common::util;

class DomainJoin {
public:
   DomainJoin();
   virtual ~DomainJoin() {}

   //
   // Get/Set values in guest-info for server communication
   //
   virtual std::wstring GetDomainName();
   virtual std::wstring GetHostName();
   virtual std::wstring GetPreferredDC();
   virtual std::wstring GetPreferredSite();

   virtual bool SetPreferredDC();
   virtual bool SetPreferredSite();

   virtual HRESULT RenameMachine();

   //
   // Domain Join
   //
   virtual bool JoinCloneToDomain(bool bClearPwd = true);
   bool JoinCloneToDomainWithNetJoinDomain();
   virtual HRESULT JoinCloneToDomainSysprep();
   HRESULT JoinMachineToDomain(bool bClearPwd = true);

   //
   // Machine Password
   //
   virtual bool ChangeMachinePassword(bool bOverride);
   virtual bool SecureMachinePwdInfo();
   virtual bool ClearSecuredMachinePwdInfo();
   virtual void ClearMachinePwdGuestInfoValues();
   virtual bool BackupMachinePassword();
   virtual bool RestoreMachinePassword();

   //
   // UPN
   //
   virtual HRESULT UpdateUPN();
   virtual void LogUPN();

   //
   // Trust Verification
   //
   virtual bool VerifyTrust(int iTries = MAX_TRUSTVERIFY_RETRIES);
   virtual bool VerifyTrustEx(bool bChangePwd = true);
   virtual bool VerifyTrustWithPreferredDc(std::wstring dcName,
                                           int iTries = MAX_TRUSTVERIFY_RETRIES);

   // Error Diagnostics
   virtual std::string GetErrorDiagnostics(std::string strErrorType);
   virtual std::wstring DiagnoseTrustVerifyErrors();

protected:
   //
   // Host name
   //
   virtual bool ValidateHostname(const char *hostname);

   //
   // Domain Join
   //
   virtual DWORD JoinMachineToDomainEx(std::wstring strDomain, LPCWSTR szMachinePwd);

   // Save and retrieve DC used for Domain Join in registry
   bool SaveRegDomainJoinDCName(std::wstring dcName);
   std::wstring RetrieveRegDomainJoinDCName();

   //
   // Domain Trust
   //
   std::wstring LogTrustFlags(DWORD dwFlags);

   //
   // Data Obfuscation
   //
   bool ObfuscateHicData(const unsigned char *pData, size_t &dataSize, char **szObfuscated,
                         size_t &obfuscatedLen);
   bool UnobfuscatedHicData(const char *pszObfuscatedData, unsigned char **unobfuscatedData,
                            size_t &unobfuscatedSize);
   char *CalculateChecksum(VOID *szData, DWORD dwSizeInBytes);

   //
   // Machine Password
   //
   bool RequestMachinePasswordChange();

   bool GetUnobfuscatedMachinePwd(void **pszMcPwd, size_t &size, bool bOutWideChar, bool bRawMcPwd,
                                  bool bClearPwd = true);

   bool ValidateMachinePassword(VOID *szMachinePwd, size_t pdwLen, bool *pbSkipped);

   // Set/Get Machine password and checksum (from guest-info/secure registry)
   size_t GetMachinePwd(char **pszMcPwd);
   void SecureFreeMachinePwd(char **pszMcPwd, size_t &pwdLen);

   size_t GetMachinePwdChksum(char **pszMcPwdChksum);
   void SecureFreeMachinePwdChksum(char **pszMcPwdChksum, size_t &chksumLen);

   void ClearMachinePassword();
   void ClearMachinePasswordChecksum();

   //
   // Utility
   //
   void secureFree(void **pData, size_t &size);
   void printBinary(void *pData, size_t &size);

private:
   coreutil::CustomizationPersistentInfo *_cpi;
   coreutil::Support *_Support;
   coreutil::MachineType *_MachineType;
   coreutil::System *_System;
   coreutil::Registry *_Registry;
   coreutil::CustomizationState *_CustState;
   Network *_Network;

   std::auto_ptr<coreutil::GuestInfo> _GuestInfo;
   std::auto_ptr<commutil::DomainServices> _DomainServices;

   int _domainJoinRetries;
   std::wstring _domainName;
   std::wstring _hostName;
};

} // namespace windows
} // namespace core
} // namespace svmga