/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * Template Helper Service
 */

#include "stdafx.h"
#include "TemplateHelperService.h"

static TemplateHelperService *gTheTemplateService = nullptr;

/*
 *-----------------------------------------------------------------------------
 *
 * initTemplateHelperService --
 *
 *    Creates the template helper service.
 *
 * Results:
 *    True on success.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */
bool
initTemplateHelperService()
{
   if (gTheTemplateService == NULL) {
      gTheTemplateService = new TemplateHelperService;
      if (!gTheTemplateService->startEx2(TEXT("TemplateHelper"), TEXT("The Template Helper Queue"),
                                         gTheTemplateService->createOperationInstance,
                                         gTheTemplateService)) {
         delete gTheTemplateService;
         gTheTemplateService = NULL;
      }
   }
   if (gTheTemplateService != NULL) {
      SYSMSG_FUNC(Debug, L"TemplateHelperService service started");
   } else {
      SYSMSG_FUNC(Error, L"Failed to start TemplateHelperService service");
   }
   return (gTheTemplateService != NULL);
}

/*
 *-----------------------------------------------------------------------------
 *
 * TemplateHelperService::prepInternalTemplate --
 *
 *    Process prepare internal template mfw message.
 *
 * Results:
 *    Add poolID to registery (\Agent\Configuration\PoolID) on success.  Else respond with error
 * message
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */
bool
TemplateHelperService::prepInternalTemplate(PropertyBag &params, PropertyBag &response)
{
   // TODO: change below log msg from Debug to Trace
   SYSMSG_FUNC(Info, L"prepInternalTemplate msg received by TemplateHelper service");
   wstr errResponse = L"Failed to process prepare internal template message";
   MsgBinary icPubKey, icPoPBlob, icNonce;
   icPubKey.owned = false;
   icPoPBlob.owned = false;
   icNonce.owned = false;

   bool result;
   wstr sigData;
   // If needed attributes not received respond with error
   if (!params.contains(PAR_ICE_PUBLIC_KEY) || !params.contains(PAR_ICE_POP_BLOB) ||
       !params.contains(PAR_NONCE)) {
      wstr errMsg = L"Missing required information in params";
      SYSMSG_FUNC(Error, L"%s", errMsg);
      // response.setError(errMsg);
      //  return true;

      /*
       * TODO: Currently no data received as part of the message,
       * hence create all required data set for temporary use
       * remove temp ICe key generation when the mfw client starts sending all information
       */
      mSvcPrep.generateNonce(VAL_NONCE_SIZE, icNonce);
      sigData.setBinary(icNonce.pBinary, icNonce.sBinary);
      if (!mSvcPrep.getPublicKey(KEYPAR_IC_ENCR, sigData, icPubKey, icPoPBlob)) {
         result = mSvcPrep.createPersistentKeyPair(KEYPAR_IC_ENCR, VAL_KEY_LENGTH);
         if (!result) {
            wstr errMsg = L"Failed to generate ICs keypair";
            SYSMSG_FUNC(Error, L"%s", errMsg);
            response.setError(errMsg);
            return false;
         }
         if (!mSvcPrep.getPublicKey(KEYPAR_IC_ENCR, sigData, icPubKey, icPoPBlob)) {
            wstr errMsg = L"Failed to retrieve ICs public key";
            SYSMSG_FUNC(Error, L"%s", errMsg);
            response.setError(errMsg);
            return false;
         }
      }
   }
   params.addBinary(PAR_ICE_PUBLIC_KEY, icPubKey.pBinary, icPubKey.sBinary);
   params.addBinary(PAR_ICE_POP_BLOB, icPoPBlob.pBinary, icPoPBlob.sBinary);
   params.addBinary(PAR_NONCE, icNonce.pBinary, icNonce.sBinary);

   // if poolID is present means, the VM is already marked as an Internal Template
   if (!(mSvcPrep.readRegPoolID().empty())) {
      wstr errMsg = L"The VM is already marked as an Internal Template";
      SYSMSG_FUNC(Error, L"%s", errMsg);
      response.setError(L"Invalid golden image");
      return false;
   }

   wstr vmGuid;
   result = mSvcPrep.getVmGuid(vmGuid);
   if (!result) {
      wstr errMsg = L"Failed to retrieve VM GUID from registry";
      SYSMSG_FUNC(Error, L"%s", errMsg);
      response.setError(errResponse);
      return false;
   }
   params.add(PAR_GOLDEN_IMAGE_GUID, vmGuid);

   wstr hostMAC;
   hostMAC = mSvcPrep.getMacAddr();
   if (hostMAC.empty()) {
      wstr errMsg = L"Failed to retrieve host MAC address";
      SYSMSG_FUNC(Error, L"%s", errMsg);
      response.setError(errResponse);
      return false;
   }
   params.add(PAR_HOST_MAC_ADDR, hostMAC);

   vwstr srvFQDNs = mSvcPrep.readRegServerFQDNs();
   if (srvFQDNs.empty()) {
      wstr errMsg = L"Server FQDNs are not availble in the registry";
      SYSMSG_FUNC(Error, L"%s", errMsg);
      response.setError(errResponse);
      return false;
   }

   result = mSvcPrep.createPersistentKeyPair(KEYPAR_GI_ENCR, VAL_KEY_LENGTH);
   if (!result) {
      wstr errMsg = L"Failed to generate GIe key pair";
      SYSMSG_FUNC(Error, L"%s", errMsg);
      response.setError(errResponse);
      return false;
   }
   /*
    * Retrieve the GIe public key and the PoP blob from keyVault.
    * The sigdata will be formed by concatenating the VM guid and nonce
    * then hashing the concatenated data (binary + string) to create HASH2.
    * TODO: At present only concatenation is done and hashing will be added later.
    */
   // TODO: Hashes HASH2 with ICe PoP blob, GIe PoP blob to produce HASH3.
   // GUID + nonce for sig data
   sigData.append(vmGuid);
   sigData.append(hostMAC);
   sigData.setBinary(icNonce.pBinary, icNonce.sBinary);

   MsgBinary giePubKey, giePoPBlob;
   // Get the GIe public key from the KeyVault.
   if (!mSvcPrep.getPublicKey(KEYPAR_GI_ENCR, sigData, giePubKey, giePoPBlob)) {
      wstr errMsg = L"Couldn't retrieve GIe public key from KeyVault";
      SYSMSG_FUNC(Error, L"%s", errMsg);
      response.setError(errResponse);
      return false;
   }
   params.addBinary(PAR_GIE_PUBLIC_KEY, giePubKey.pBinary, giePubKey.sBinary);
   params.addBinary(PAR_GIE_POP_BLOB, giePoPBlob.pBinary, giePoPBlob.sBinary);

   MsgBinary phsPubKey, phsPoPBlob;
   // Get PHs public key to sign the HASH3
   if (!mSvcPrep.getPublicKey(KEYPAR_PH_SIGN, sigData, phsPubKey, phsPoPBlob)) {
      // If the KEYPAR_PH_SIGN keypair not available means Golden Image is not registered.
      wstr errMsg;
      errMsg << L"Failed to retrieve PHs public key while processing " << HINT_IT_HELPER;
      SYSMSG_FUNC(Error, L"%s", errMsg);
      response.setError(errResponse);
      return false;
   }

   // TODO: sign HASH3 with PHs public key
   params.addBinary(PAR_SIGNATURE, phsPubKey.pBinary, phsPubKey.sBinary);

   /*
    * iterate over server FQDNs and send msg to get poolID.
    * Stop on success
    */
   wstr poolId;
   for (vtstrit it = srvFQDNs.begin(); it != srvFQDNs.end(); it++) {
      wstr fqdn = it->trim();
      /*
       * When a machine is created from the Nutanix template,
       * it has to be prepared for further use by installing Nutanix guest tools
       * Installing tools take a long time. Anywhere from 3 min and more depending on computing and
       * IO performance So, if connectAndSend succeeds, retry for 20 times with increasing sleep in
       * every iteration t(n) = t(n-1) + (t(n-1)/4) Start sleep with 12 secs and at 20th retry it
       * would be 713 secs aggregatively in 20 retries, wait is about an hour
       */
      int sleepInterval = 12;
      for (int i = 0; i < VAL_RETRIES; i++) {
         sleep(sleepInterval * VAL_MILLISEC_PER_SEC);
         result = mSvcPrep.connectAndSend(fqdn, QUEUE_AGENT_PAIRING, HINT_PREP_TEMPLATE, params,
                                          response);
         if (!result) {
            wstr errMsg;
            errMsg << L"Couldn't connect and send msg " << HINT_PREP_TEMPLATE << L" to server: "
                   << fqdn;
            SYSMSG_FUNC(Warn, L"%s with error: ", errMsg, response.getErrorText());
            response.clear();
         } else {
            poolId = response.get(PAR_POOL_ID, L"");
            if (poolId.empty()) {
               wstr errMsg;
               errMsg << L"Couldn't get poolID from server: " << fqdn;
               SYSMSG_FUNC(Warn, L"%s", errMsg);
               response.clear();
               sleepInterval = sleepInterval + static_cast<int>(sleepInterval / 4);
            } else {
               break;
            }
         }
      }
      // break outer for loop if poolId is not empty
      if (!poolId.empty()) {
         break;
      }
   }
   if (poolId.empty()) {
      wstr errMsg;
      errMsg << L"Faild to register the Internal Template associated to VMGUID (" << vmGuid
             << L") to any servers (" << srvFQDNs.toString(L",") << L")";
      SYSMSG_FUNC(Error, L"%s", errMsg);
      response.setError(errResponse);
      return false;
   }
   mSvcPrep.writeRegPoolID(poolId);
   // TODO: respond with an acknowledgement with ack sign
   response.add(PAR_RESULT_TEXT, ACKSTR);
   return true;
}
