/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */
#pragma once

class wutil {
public:
   /*
    * wutil::ServerReceivedRequest --
    *
    *    Determines if the simple python server received a request for the provided file name.
    */

   static bool ServerReceivedRequest(const std::wstring &logPath, const std::string &fileName)
   {
      std::string logContents;
      READ_FILE_INTO_STRING(logPath, logContents, false);
      std::string expectedCrlContent = "GET /" + fileName;
      return logContents.find(expectedCrlContent) != std::string::npos;
   }
};