/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/**
 * @file CustomizationPersistentInfo.h --
 * Defines the guest customization related persistent information.
 */

#pragma once

#include "InstantClonesErrors.h"

#include <common/windows/singleton/Singleton.h >

namespace svmga {
namespace core {
namespace util {

/*!
 * Provides all guest customiation related persistent information.
 * This class hides how the information are persisted but rather serves
 * as the information provider for guest customization needs.
 */
class CustomizationPersistentInfo : public Singleton<CustomizationPersistentInfo> {

public:
   virtual void FlushKeys() = 0;

   virtual bool IsUniversalPrep() = 0;

   // Horizon Agent Functions
   virtual bool IsPoolIdSet() = 0;
   virtual std::wstring GetOperationType() = 0;
   virtual bool IsMachinePwdSet() = 0;
   virtual void ClearMachinePwd() = 0;

   //
   // Fork Functions
   //
   virtual bool IsVmForked() = 0;
   virtual void MarkVmForked() = 0;
   virtual void ClearVmForked() = 0;

   //
   // Sysprep Functions
   //
   virtual bool IsSysprepCompleted() = 0;
   virtual void MarkSysprepCompleted() = 0;
   virtual bool IsSysprepGeneralizeCompleted() = 0;
   virtual void MarkSysprepGeneralizeCompleted() = 0;
   virtual bool IsSysprepSpecializeCompleted() = 0;
   virtual bool IsSysprepOobeCompleted() = 0;
   virtual void MarkSysprepSpecializeCompleted() = 0;
   virtual void MarkSysprepOobeCompleted() = 0;
   virtual bool IsFastRefreshCompleted() = 0;
   virtual void MarkFastRefreshCompleted() = 0;

   //
   // Domain Join Functions
   //
   virtual bool IsTemplateDomainJoined() = 0;
   virtual bool IsCloneDomainJoined() = 0;
   virtual bool IsCloneDomainJoinedWithNetJoinDomain() = 0;
   virtual void MarkTemplateDomainJoined() = 0;
   virtual void MarkCloneDomainJoined() = 0;
   virtual void MarkCloneDomainJoinedWithNetJoinDomain() = 0;
   virtual void SetDomainJoinRebootCount(int iCount) = 0;
   virtual int GetDomainJoinRebootCount() = 0;

   virtual bool IsPreVerifyTrustRebootEnabled() = 0;
   virtual void IncrementPreVerifyTrustReboots() = 0;
   virtual int GetPreVerifyTrustReboots() = 0;
   virtual int GetMaxPreVerifyTrustReboots() = 0;

   virtual bool IsVerifyTrustFailedRebootEnabled() = 0;
   virtual void IncrementVerifyTrustFailedReboots() = 0;
   virtual int GetVerifyTrustFailedReboots() = 0;
   virtual int GetMaxVerifyTrustFailedReboots() = 0;

   virtual bool IsNetJoinDomainEnabled() = 0;
   virtual void IncrementNetJoinDomainAttempts() = 0;
   virtual int GetNetJoinDomainAttempts() = 0;
   virtual int GetMaxNetJoinDomainAttempts() = 0;

   virtual bool SkipCloneprepDomainJoin() = 0;

   virtual DWORD GetPostNetlogonStartDelay() = 0;

   //
   // Machine Password Functions
   //
   virtual bool IsTemplateMachinePasswordChanged() = 0;
   virtual bool IsCloneMachinePasswordChanged() = 0;
   virtual void MarkTemplateMachinePasswordChanged() = 0;
   virtual void MarkCloneMachinePasswordChanged() = 0;
   virtual bool IsTemplateMachinePwdChangeEnabled() = 0;
   virtual bool IsCloneMachinePwdChangeEnabled() = 0;

   virtual bool IsSecureMachinePwdInfoDisabled() = 0;
   virtual bool IsMachinePwdInfoSecured() = 0;
   virtual bool MarkMachinePwdInfoSecured() = 0;
   virtual bool ClearSecuredMachinePwdInfo() = 0;

   //
   // Rename Functions
   //
   virtual bool IsTemplateRenamed() = 0;
   virtual bool IsParentRenamed() = 0;
   virtual bool IsCloneRenamed() = 0;

   virtual void MarkTemplateRenamed() = 0;
   virtual void MarkParentRenamed() = 0;
   virtual void MarkCloneRenamed() = 0;

   //
   // Reboot Functions
   virtual bool IsCloneRebooted() = 0;
   virtual bool IsTemplateRebooted() = 0;
   virtual bool IsReplicaRebooted() = 0;

   virtual void MarkCloneRebooted() = 0;
   virtual void MarkTemplateRebooted() = 0;
   virtual void MarkReplicaRebooted() = 0;

   //
   // Shutdown Functions
   //
   virtual bool IsTemplateShutdownNeeded() = 0;
   virtual void MarkTemplateShutdownNeeded() = 0;
   virtual void MarkTemplateShutdownDone() = 0;

   virtual bool IsCloneShutdownNeeded() = 0;
   virtual void MarkCloneShutdownNeeded() = 0;
   virtual void MarkCloneShutdownDone() = 0;

   //
   // Mac Address Functions
   //
   virtual bool IsMacAddressReset() = 0;
   virtual void MarkMacAddressReset() = 0;
   virtual std::wstring GetGoldenImageMacAddress() = 0;
   virtual void MarkGoldenImageMacAddress(const std::wstring &value) = 0;

   //
   // IP Functions
   //
   virtual int GetIpReleaseOnShutdownState() = 0;
   virtual bool IsIpRenewed() = 0;
   virtual bool MarkIpRenewed() = 0;
   virtual bool ClearIpRenewed() = 0;

   //
   // IPv6 Enabled
   //
   virtual bool IsIPv6SupportEnabled() = 0;

   //
   // DHCP Fix
   //
   virtual bool DisableDhcpService() = 0;

   //
   // GPUpdate Functions
   //
   virtual bool IsGPUpdateEnabledOnClone() = 0;
   virtual bool IsGPUpdateEnabledOnIT() = 0;

   //
   // Script Functions
   //
   virtual bool IsPostCustScriptDisabled() = 0;
   virtual bool AreScriptsSecured() = 0;
   virtual void MarkScriptsSecured() = 0;
   virtual bool IsPostCustScriptSecured() = 0;
   virtual bool IsPreShutdownScriptSecured() = 0;
   virtual void MarkPostCustScriptSecured() = 0;
   virtual void MarkPreShutdownScriptSecured() = 0;
   virtual bool IsPostCustScriptConfigured() = 0;
   virtual bool IsPreShutdownScriptConfigured() = 0;
   virtual void MarkPostCustScriptConfigured() = 0;
   virtual void MarkPreShutdownScriptConfigured() = 0;
   virtual bool IsLogScriptOutputEnabled() = 0;

   //
   // License Functions
   //
   virtual bool IsLicenseActivationEnabled(bool bSysprep) = 0;
   virtual bool IsLicenseActivated() = 0;
   virtual void MarkLicenseActivated() = 0;
   virtual bool IsLicenseRearmEnabled() = 0;
   virtual bool IsLicenseRearmCompleted() = 0;
   virtual void MarkLicenseRearmCompleted() = 0;

   //
   // Template Customization Functions
   //
   virtual bool IsTemplateCustomizationNeeded() = 0;
   virtual void MarkTemplateCustomizationNeeded() = 0;
   virtual void MarkTemplateCustomizationDone() = 0;
   virtual bool IsTemplateCustomizationDone() = 0;
   virtual bool IsITDomainJoinFinished() = 0;
   virtual void SetITDomainJoinFinished() = 0;
   virtual bool IsITCustomizationStartTimeSet() = 0;
   virtual void MarkITCustomizationStartTimeSet() = 0;

   //
   // Clone Customization Functions
   //
   virtual bool IsCloneCustomizationCompleted() = 0;
   virtual void MarkCloneCustomizationCompleted() = 0;

   //
   // Replica Customization Functions
   //
   virtual bool IsReplicaCustomizationCompleted() = 0;
   virtual void MarkReplicaCustomizationCompleted() = 0;

   //
   // Pre/Post Intregration Values
   //
   virtual bool PreIntegrationValuesParsed() = 0;
   virtual void MarkPreIntegrationValuesParsed() = 0;
   virtual bool PostIntegrationValuesParsed() = 0;
   virtual void MarkPostIntegrationValuesParsed() = 0;

   //
   // Profile Redirection
   //
   virtual bool AreProfilesRedirected() = 0;
   virtual void MarkProfilesRedirected() = 0;

   //
   // Persistent Disk Ready
   //
   virtual bool ArePersistentDisksReady() = 0;
   virtual void MarkPersistentDisksReady() = 0;

   //
   // NL Flags
   //
   virtual bool IsNLFlagEnabled() = 0;

   //
   // Service Notify Values
   //
   virtual bool IsServiceNotifyEnabled() = 0;
   virtual DWORD ServiceNotifyGetMaxRestarts(DWORD dwDefault) = 0;
   virtual DWORD ServiceNotifyGetStartDelay(DWORD dwDefault) = 0;

   virtual DWORD ServiceGetMaxRetries(DWORD dwDefault) = 0;
   virtual DWORD ServiceGetRetryDelay(DWORD dwDefault) = 0;

   virtual DWORD GetServiceShutdownDelay(DWORD dwDefault) = 0;

   //
   // All guestInfo for vmx backdoor we move to regkey
   //
   virtual std::wstring GetGuestInfoValue(const std::wstring &entry) = 0;
   virtual void SetGuestInfoValue(const std::wstring &entry, const std::wstring &value) = 0;

   /*!
    * Set customization result with NotifyVdmStatusValue indicating a success or
    * a failure and in case of a failure, the detail error code is specified
    * thru SvmPolicyState. Note that this call may be invoked multiple times
    * because agent may continue to execute other things even though a previous
    * error is hit. So there could be multiple errors occur down the road. When
    * 'force' is set to false which is the default case, the logic should be
    * that: if a previous error code has been set, any latter error code should
    * not overwrite it. This way will preserve the very original error.
    *
    * @param psState [in] detail error code in case vsValue is:
    * CustomizationFailed or "0" in case vsValue is: CustomizationSucceeded.
    * @param vsValue [in] status value to notify VDM agent.
    *                     Can either be: CustomizationFailed or
    * CustomizationSucceeded.
    * @param force [in] If this is set to false - the default case, see above
    *                   description; if this is set to true, it will simply
    * overwrite the policy state value and NotifyVdmStatusValue accordingly.
    */
   virtual void SetCustomizationResult(SvmPolicyState psState, NotifyVdmStatusValue vsValue,
                                       bool force = false) = 0;

   static void Terminate();
   static CustomizationPersistentInfo *GetInstance();

protected:
   static CustomizationPersistentInfo *ManageInstance(bool bCreate);
};

} // namespace util
} // namespace core
} // namespace svmga
