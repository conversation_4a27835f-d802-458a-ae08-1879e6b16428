/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

#include <windows.h>
#define SECURITY_WIN32
#include <security.h>
#include <winsock2.h>
#include <ws2tcpip.h>
#include <wtsapi32.h>
#include <MessageFrameWork.h>
#include <memory>
using namespace CORE;

#include <gmock/gmock.h>
#include <gtest/gtest.h>

#define SECURITY_WIN32
#include <filesystem>
#include <iostream>
#include <map>
#include <memory>
#include <stdio.h>
#include <string>
#include <thread>

#include <ssl.h>

#include "json/json.h"

#include "cedar/windows/resource_security.h"

using namespace testing;

#include "gtest-utils.h"
#include "utilFile.h"

// Use do-while to prevent redefinition errors
#define READ_FILE_INTO_STRING(filePath, stringName, binary)                                        \
   do {                                                                                            \
      WinFile myfile(filePath);                                                                    \
      stringName = myfile.ReadFileIntoString(binary);                                              \
   } while (0);

#include "TestLaunchProcess.h"

#define START_LEAF_SERVER(startLeafCmd) START_SERVER(startLeafCmd, L"server")


#define START_SERVER(startLeafCmd, suffix)                                                         \
   PROCESS_INFORMATION serverProcess = {0};                                                        \
   HANDLE hFileHandle = NULL;                                                                      \
   auto serverLogPath = mWorkingDir + L"\\" + testName + L"-" suffix L".log";                      \
   ASSERT_TRUE(ProcUtil::LaunchProcessWithStdOut(startLeafCmd, serverLogPath, serverProcess,       \
                                                 hFileHandle, false));

#define STOP_SERVER()                                                                              \
   ProcUtil::CloseProcess(serverProcess);                                                          \
   if (hFileHandle != NULL) {                                                                      \
      CloseHandle(hFileHandle);                                                                    \
   }