/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * testDependency.cc -
 *
 *    Initialize and uninitialize Poll and VThread for crtbora test
 */

#include "hostinfo.h"
#include "hznPoll.h"

extern "C" {
#include "file.h"
#include "vthread.h"
}

namespace crt {
namespace common {
namespace test {

/*
 *-----------------------------------------------------------------------------
 *
 * InitTestDependency
 *
 *      Minimal function to init Poll and VThread.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

void
InitTestDependency()
{
   VThread_Init("tcui");
#if defined(__APPLE__)
   Poll_InitCF();
#elif defined(__linux__)
   Poll_InitGtk();
#else
   PollOptions pollOpts = {0};
   pollOpts.windowsMsgThread = VThread_CurID();
   Poll_InitDefaultEx(&pollOpts);
#endif
}


/*
 *-----------------------------------------------------------------------------
 *
 * UninitializeTestDependency
 *
 *      Uninitialize Poll and VThread for the test.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

void
UninitializeTestDependency()
{
   Poll_Exit();
}

} // namespace test
} // namespace common
} // namespace crt
