/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */
#include "wsnmCommonIncludes.h"

#include <iomanip>
#include <memory>
#include <sstream>
#include <string>
#include <filesystem>

#include "bcrypt.h"
#include "winhttp.h"

#include "utilCertificates.h"
#include "utilCorestring.h"
#include "utilWinHttpRequest.h"
#include "utilHash.h"


#define NT_SUCCESS(Status) (((NTSTATUS)(Status)) >= 0)

constexpr wchar_t *HTTP_USER_AGENT = L"VMAgent";


namespace {


/*
 * getThumbprints()
 *
 *   Given a CERT_CONTEXT, the SHA-256 thumbprints are retrieved.
 *     Format example:
 *       83:7E:88:A9:13:02:DC:A9:44:EE:EC:3A:92:C3:...:B0:C3:C1:A0:47:DB
 */

std::string getThumbprints(PCCERT_CONTEXT context);


/*
 * split()
 *
 *   Splits a string. TODO: Fix corestring's split and remove this
 */

std::vector<CORE::wstr> split(const wstr &str, wchar_t splitChar);
}; // namespace


/*
 *-----------------------------------------------------------------------------
 *
 * WinHttpRequest --
 *
 *    Default constructor
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

WinHttpRequest::WinHttpRequest() :
   m_action(),
   m_fullURL(),
   m_hostName(),
   m_urlPath(),
   m_extraInfo(),
   m_proxyServerList(),
   m_proxyBypass(),
   m_proxyUser(),
   m_proxyPw(),
   m_proxyPACURL(),
   m_body(),
   m_uploadFileName(),
   m_headers(),
   m_queryParams(),
   m_responseCode(0),
   m_responseHeaders(),
   m_totalDataToSend(0),
   m_certStore(NULL),
   m_onlyUseProvidedStore(false),
   m_IEProxySettings(),
   m_ignoreAllCertErrors(false),
   m_ignoreWrongHostError(false),
   m_enableCertRevocation(false),
   m_serverCertVerified(false),
   m_CAThumbprints(),
   m_hSession(),
   m_hConnect(),
   m_hRequest(),
   m_response(),
   m_SyncEvent(),
   m_cancelEvent(),
   m_canceledRequest(NULL),
   m_requestComplete(false),
   m_handleLock(),
   m_uploadStream(),
   m_dataSent(0),
   m_uploadBuffer()
{}


/*
 *-----------------------------------------------------------------------------
 *
 * WinHttpRequest --
 *
 *    Overloaded constructor. Initializes with headers, query parameters and
 *      SSL settings.
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

WinHttpRequest::WinHttpRequest(bool ignoreAllCertErrors, bool enableCertRevocation,
                               std::map<std::string, std::string> headers,
                               std::multimap<std::string, std::string> queryParams) :
   m_action(),
   m_fullURL(),
   m_hostName(),
   m_urlPath(),
   m_extraInfo(),
   m_proxyServerList(),
   m_proxyBypass(),
   m_proxyUser(),
   m_proxyPw(),
   m_proxyPACURL(),
   m_body(),
   m_uploadFileName(),
   m_headers(headers),
   m_queryParams(queryParams),
   m_responseCode(0),
   m_responseHeaders(),
   m_totalDataToSend(0),
   m_certStore(NULL),
   m_onlyUseProvidedStore(false),
   m_IEProxySettings(),
   m_ignoreAllCertErrors(ignoreAllCertErrors),
   m_ignoreWrongHostError(false),
   m_enableCertRevocation(enableCertRevocation),
   m_CAThumbprints(),
   m_hSession(),
   m_hConnect(),
   m_hRequest(),
   m_response(),
   m_SyncEvent(),
   m_cancelEvent(),
   m_canceledRequest(NULL),
   m_requestComplete(false),
   m_handleLock(),
   m_uploadStream(),
   m_dataSent(0),
   m_uploadBuffer()
{}


/*
 *-----------------------------------------------------------------------------
 *
 * ~WinHttpRequest --
 *
 *    Destructor
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

WinHttpRequest::~WinHttpRequest() {}


/*
 *-----------------------------------------------------------------------------
 *
 * getSync --
 *
 *    Perform a synchronous GET request
 *
 * Results:
 *    true if successfully performed request and received response. false
 *      otherwise.
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

bool
WinHttpRequest::getSync(const std::string &url, std::string &response)
{
   m_response.clear();
   bool result = performSync(ACTION::GET, url);
   response = m_response;
   return result && requestSucceeded();
}


/*
 *-----------------------------------------------------------------------------
 *
 * postSync --
 *
 *    Perform a synchronous POST request
 *
 * Results:
 *    true if successfully performed request and received response. false
 *      otherwise.
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

bool
WinHttpRequest::postSync(HttpRequest::CONTENT_TYPE contentType, const std::string &url,
                         const std::string &body, std::string &response)
{
   AddContentTypeHeader(contentType);
   bool result = performSync(ACTION::POST, url, body);
   response = m_response;
   return result && requestSucceeded();
}


/*
 *-----------------------------------------------------------------------------
 *
 * putSync --
 *
 *    Perform a synchronous PUT request
 *
 * Results:
 *    true if successfully performed request and received response. false
 *      otherwise.
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

bool
WinHttpRequest::putSync(HttpRequest::CONTENT_TYPE contentType, const std::string &url,
                        const std::string &body, std::string &response)
{
   AddContentTypeHeader(contentType);
   bool result = performSync(ACTION::PUT, url, body);
   response = m_response;
   return result && requestSucceeded();
}


/*
 *-----------------------------------------------------------------------------
 *
 * putSyncFile --
 *
 *    Perform a synchronous PUT request, where the contents of the request are
 *    contained in a file.
 *
 * Results:
 *    true if successfully performed request and received response. false
 *      otherwise.
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

bool
WinHttpRequest::putSyncFile(HttpRequest::CONTENT_TYPE contentType, const std::string &url,
                            const std::string &filename, std::string &response)
{
   AddContentTypeHeader(contentType);
   bool result = performSync(ACTION::PUT, url, std::string(), filename);
   response = m_response;
   return result && requestSucceeded();
}


/*
 *-----------------------------------------------------------------------------
 *
 * deleteSync --
 *
 *    Perform a synchronous DELETE request
 *
 * Results:
 *    true if successfully performed request and received response. false
 *      otherwise.
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

bool
WinHttpRequest::deleteSync(const std::string &url, std::string &response)
{
   std::string body;
   bool result = performSync(ACTION::Delete, url, body);
   response = m_response;
   return result && requestSucceeded();
}


/*
 *-----------------------------------------------------------------------------
 *
 * getAsync --
 *
 *    Perform an asynchronous GET request.  Once the request is complete, the
 *    callback will be invoked. The callback will be invoked even if the request
 *    was unsuccessful. For example, if a timeout occurs, the callback will be
 *    invoked. The only way the callback is not invoked is if the request is
 *    explicitly canceled using cancelRequest. If you want to delete the
 *    HttpRequest object inside the callback, use WinHttpRequest->Release().
 *    Invoking the destructor directly will cause a crash.
 *
 * Results:
 *    true if successfully started the request. false otherwise.
 *
 * Side effects:
 *    Once the request is complete, the callback will be invoked. The callback
 *    will be invoked even if the request was unsuccessful.
 *
 *-----------------------------------------------------------------------------
 */

bool
WinHttpRequest::getAsync(const std::string &url, std::function<void(HttpRequest *, std::string)> cb)
{
   return performAsync(WinHttpRequest::ACTION::GET, url, cb);
}


/*
 *-----------------------------------------------------------------------------
 *
 * postAsync --
 *
 *    Perform an asynchronous POST request. Once the request is complete, the
 *    callback will be invoked. The callback will be invoked even if the request
 *    was unsuccessful. For example, if a timeout occurs, the callback will be
 *    invoked. The only way the callback is not invoked is if the request is
 *    explicitly canceled using cancelRequest. If you want to delete the
 *    HttpRequest object inside the callback, use WinHttpRequest->Release().
 *    Invoking the destructor directly will cause a crash.
 *
 * Results:
 *    true if successfully started the request. false otherwise.
 *
 * Side effects:
 *    Once the request is complete, the callback will be invoked. The callback
 *    will be invoked even if the request was unsuccessful.
 *
 *-----------------------------------------------------------------------------
 */

bool
WinHttpRequest::postAsync(HttpRequest::CONTENT_TYPE contentType, const std::string &url,
                          const std::string &body,
                          std::function<void(HttpRequest *, std::string)> cb)
{
   AddContentTypeHeader(contentType);
   m_body = std::vector<char>(body.begin(), body.end());
   return performAsync(WinHttpRequest::ACTION::POST, url, cb);
}


/*
 *-----------------------------------------------------------------------------
 *
 * putAsync --
 *
 *    Perform an asynchronous PUT request.
 *
 * Results:
 *    true if successfully started the request. false otherwise.
 *
 * Side effects:
 *    Once the request is complete, the callback will be invoked. The callback
 *    will be invoked even if the request was unsuccessful.
 *
 *-----------------------------------------------------------------------------
 */

bool
WinHttpRequest::putAsync(HttpRequest::CONTENT_TYPE contentType, const std::string &url,
                         const std::string &body,
                         std::function<void(HttpRequest *, std::string)> cb)
{
   AddContentTypeHeader(contentType);
   m_body = std::vector<char>(body.begin(), body.end());
   return performAsync(WinHttpRequest::ACTION::PUT, url, cb);
}

bool
WinHttpRequest::putAsync(HttpRequest::CONTENT_TYPE contentType, const std::string &url,
                         const std::vector<char> &body, size_t bodySize,
                         std::function<void(HttpRequest *, std::string)> cb)
{
   AddContentTypeHeader(contentType);
   if (body.size() < bodySize) {
      SYSMSG_FUNC(Warn, L"Size of outgoing buffer is smaller than the given length");
      m_body = std::vector<char>(body.begin(), body.end());
   } else {
      m_body = std::vector<char>(body.begin(), body.begin() + bodySize);
   }
   return performAsync(WinHttpRequest::ACTION::PUT, url, cb);
}


/*
 *-----------------------------------------------------------------------------
 *
 * putAsyncFile --
 *
 *    Perform an asynchronous PUT request where the contents of the request are
 *    contained within a file.
 *
 * Results:
 *    true if successfully started the request. false otherwise.
 *
 * Side effects:
 *    Once the request is complete, the callback will be invoked. The callback
 *    will be invoked even if the request was unsuccessful.
 *
 *-----------------------------------------------------------------------------
 */

bool
WinHttpRequest::putAsyncFile(HttpRequest::CONTENT_TYPE contentType, const std::string &url,
                             const std::string &filename,
                             std::function<void(HttpRequest *, std::string)> cb)
{
   AddContentTypeHeader(contentType);
   m_uploadFileName = filename;
   return performAsync(WinHttpRequest::ACTION::PUT, url, cb);
}


/*
 *-----------------------------------------------------------------------------
 *
 * performAsync --
 *
 *    Perform an asynchronous request. Contains the majority of the WinHTTP
 *    logic.
 *
 * Results:
 *    true if successfully started the request. false otherwise.
 *
 * Side effects:
 *    Once the request is complete, the callback will be invoked. The callback
 *    will be invoked even if the request was unsuccessful.
 *
 *-----------------------------------------------------------------------------
 */

bool
WinHttpRequest::performAsync(ACTION action, const std::string &url,
                             std::function<void(HttpRequest *, std::string)> callback)
{
   if (!callback) {
      SYSMSG_FUNC(Error, L"Invalid callback parameter");
      return false;
   }

   if (url.empty()) {
      SYSMSG_FUNC(Error, L"Empty url");
      return false;
   }

   URL_COMPONENTS components;
   /* Parse the URL received */
   memset(&components, 0, sizeof(components));
   components.dwStructSize = sizeof(components);
   components.dwSchemeLength = 1;
   components.dwHostNameLength = 1;
   components.dwUrlPathLength = 1;
   components.dwExtraInfoLength = 1;

   m_fullURL = wstr::to_wstr(url.c_str());
   if (!WinHttpCrackUrl(m_fullURL.c_str(), static_cast<DWORD>(m_fullURL.length()), 0,
                        &components)) {
      SYSMSG_FUNC(Error, L"Unable to parse url");
      return false;
   }

#ifndef ALLOW_HTTP
   if (components.nScheme != INTERNET_SCHEME_HTTPS) {
      SYSMSG_FUNC(Error, L"Only https is supported");
      return false;
   }
#endif

   std::lock_guard<std::recursive_mutex> lock(m_handleLock);

   this->m_callback = callback;

   wstr proxyPAC = getPACURL();

   // If we already know the proxy address and there's no PAC file to worry
   // about, then we can safely skip the proxy discovery logic
   bool skipProxyDiscovery = !m_proxyServerList.empty() && proxyPAC.empty();

   if (skipProxyDiscovery) {
      m_hSession.reset(WinHttpOpen(
         HTTP_USER_AGENT, WINHTTP_ACCESS_TYPE_NAMED_PROXY, m_proxyServerList.p(),
         m_proxyBypass.empty() ? WINHTTP_NO_PROXY_BYPASS : m_proxyBypass.p(), WINHTTP_FLAG_ASYNC));
   } else {
      // Open a session handle to begin the proxy discovery process
      m_hSession.reset(WinHttpOpen(HTTP_USER_AGENT, WINHTTP_ACCESS_TYPE_AUTOMATIC_PROXY,
                                   WINHTTP_NO_PROXY_NAME, WINHTTP_NO_PROXY_BYPASS,
                                   WINHTTP_FLAG_ASYNC));
   }

   if (!m_hSession) {
      SYSMSG_FUNC(Error, L"WinHttpOpen failed: %s", wstr::formatError());
      return false;
   }

   if (!skipProxyDiscovery) {
      NetProxyConfig winProxyInfo;
      if (getProxyURL(proxyPAC, winProxyInfo)) {
         // Reopen the session with the proxy info that we found
         m_hSession.reset(
            WinHttpOpen(HTTP_USER_AGENT, WINHTTP_ACCESS_TYPE_NAMED_PROXY, winProxyInfo.proxy.p(),
                        winProxyInfo.proxyBypass.empty() ? WINHTTP_NO_PROXY_BYPASS
                                                         : winProxyInfo.proxyBypass.p(),
                        WINHTTP_FLAG_ASYNC));
         if (!m_hSession) {
            SYSMSG_FUNC(Error, L"WinHttpOpen (proxy) failed: %s", wstr::formatError());
            return false;
         }
      }
   }

   // Set the timeouts
   if (!WinHttpSetTimeouts(m_hSession.get(),
                           static_cast<int>(m_ResolveTimeoutRatio * m_TotalTimeout_ms),
                           static_cast<int>(m_ConnectTimeoutRatio * m_TotalTimeout_ms),
                           static_cast<int>(m_SendTimeoutRatio * m_TotalTimeout_ms),
                           static_cast<int>(m_ReceiveTimeoutRatio * m_TotalTimeout_ms))) {
      DWORD err = GetLastError();
      SYSMSG_FUNC(Error, L"WinHttpSetTimeouts failed: 0x%x", err);
      return false;
   }

   // Set the callbacks
   DWORD registerCb = WINHTTP_CALLBACK_FLAG_ALL_COMPLETIONS | WINHTTP_CALLBACK_FLAG_HANDLES |
                      WINHTTP_CALLBACK_FLAG_SECURE_FAILURE;

   if (!m_CAThumbprints.empty()) {
      registerCb |= WINHTTP_CALLBACK_STATUS_SENDING_REQUEST;
   }

   WINHTTP_STATUS_CALLBACK cbStatus =
      ::WinHttpSetStatusCallback(m_hSession.get(), &WinHttpCallback, registerCb, 0);

   if (cbStatus == WINHTTP_INVALID_STATUS_CALLBACK) {
      SYSMSG_FUNC(Error, L"Failed to set status callback");
      return false;
   }

   m_hostName = wstr(components.lpszHostName, static_cast<size_t>(components.dwHostNameLength));

   m_hConnect.reset(WinHttpConnect(m_hSession.get(), m_hostName.c_str(), components.nPort, 0));
   if (!m_hConnect) {
      DWORD err = GetLastError();
      SYSMSG_FUNC(Error, L"WinHttpConnect failed: 0x%x", err);
      return false;
   }

   // Merge query parameters passed explicitly with those found in the URL
   if (components.dwExtraInfoLength) {
      wstr extraInfo =
         wstr(components.lpszExtraInfo, static_cast<size_t>(components.dwExtraInfoLength));
      importQueryParams(extraInfo);
   }

   // Generate the query string from the multimap
   std::string query;
   size_t queryLen = 0;
   getQueryParamStr(query, queryLen);

   if (queryLen) {
      m_extraInfo = wstr::to_wstr(query.c_str(), queryLen);
   }

   m_urlPath = wstr(components.lpszUrlPath, static_cast<size_t>(components.dwUrlPathLength));

   // Append the query to the url path
   wstr objectName = m_urlPath + m_extraInfo;

   switch (action) {
   case GET:
      m_action = L"GET";
      break;
   case POST:
      m_action = L"POST";
      break;
   case PUT:
      m_action = L"PUT";
      break;
   case Delete:
      m_action = L"DELETE";
      break;
   }

#ifdef ALLOW_HTTP
   DWORD flags = WINHTTP_FLAG_REFRESH;
   if (components.nScheme == INTERNET_SCHEME_HTTPS) {
      flags |= WINHTTP_FLAG_SECURE;
   }
#else
   DWORD flags = WINHTTP_FLAG_REFRESH | WINHTTP_FLAG_SECURE;
#endif

   m_hRequest.reset(WinHttpOpenRequest(m_hConnect.get(), m_action.c_str(), objectName.c_str(),
                                       NULL, /* Using Http/1.1 */
                                       WINHTTP_NO_REFERER, WINHTTP_DEFAULT_ACCEPT_TYPES, flags));
   if (!m_hRequest) {
      DWORD err = GetLastError();
      SYSMSG_FUNC(Error, L"WinHttpOpenRequest failed: 0x%x", err);
      return false;
   }

   // Enable/disable security features
   DWORD securityFlags = 0;
   if (m_ignoreWrongHostError) {
      securityFlags |= SECURITY_FLAG_IGNORE_CERT_CN_INVALID;
   }

   if (m_ignoreAllCertErrors) {
      securityFlags |= SECURITY_FLAG_IGNORE_ALL_CERT_ERRORS;
   } else if (!m_CAThumbprints.empty()) {
      // The unknown CA will get verified later
      securityFlags |= SECURITY_FLAG_IGNORE_UNKNOWN_CA;
   } else if (m_enableCertRevocation) {
      DWORD dwFlags = WINHTTP_ENABLE_SSL_REVOCATION;
      if (!WinHttpSetOption(m_hRequest.get(), WINHTTP_OPTION_ENABLE_FEATURE, &dwFlags,
                            sizeof(dwFlags))) {
         DWORD err = GetLastError();
         SYSMSG_FUNC(Error, L"WinHttpSetOption failed to enable revocation: 0x%x", err);
         // Unable to enforce revocation, so must fail
         return false;
      }
   }

   if (securityFlags) {
      if (!WinHttpSetOption(m_hRequest.get(), WINHTTP_OPTION_SECURITY_FLAGS, &securityFlags,
                            sizeof(securityFlags))) {
         DWORD err = GetLastError();
         SYSMSG_FUNC(Warn, L"WinHttpSetOptions failed: 0x%x", err);
         // Try going through with the request anyway
      }
   }

   // Set the proxy credentials if we need to
   if (!m_proxyUser.empty() && !m_proxyPw.empty()) {
      if (!WinHttpSetOption(m_hRequest.get(), WINHTTP_OPTION_PROXY_USERNAME, m_proxyUser.p_upd(),
                            static_cast<DWORD>(m_proxyUser.s()))) {
         DWORD err = GetLastError();
         SYSMSG_FUNC(Error, L"Unable to set proxy user: 0x%x %s", err);
         return false;
      }
      if (!WinHttpSetOption(m_hRequest.get(), WINHTTP_OPTION_PROXY_PASSWORD, m_proxyPw.p_upd(),
                            static_cast<DWORD>(m_proxyPw.s()))) {
         DWORD err = GetLastError();
         SYSMSG_FUNC(Error, L"Unable to set proxy pw: 0x%x %s", err);
         return false;
      }
   }

   // Handle the body
   LPVOID reqDataPtr = WINHTTP_NO_REQUEST_DATA;

   if (action == WinHttpRequest::ACTION::POST || action == WinHttpRequest::ACTION::PUT) {

      if (!m_uploadFileName.empty()) {
         m_body.clear();
         m_uploadStream = std::ifstream(m_uploadFileName, std::ifstream::binary);
         std::filesystem::path p(m_uploadFileName);
         m_totalDataToSend = std::filesystem::file_size(p);
         // Not sending any data right away
         m_dataSent = 0;
         // Set up the upload buffer
         m_uploadBuffer.resize(MAX_FILE_CHUNK_SIZE_BYTES);
      } else if (m_body.size()) {
         reqDataPtr = (LPVOID)m_body.data();
         m_totalDataToSend = m_body.size();
         // For small requests such as this, we're sending all the data
         // right away
         m_dataSent = m_totalDataToSend;
      }

      if (m_totalDataToSend > MAX_REQUEST_SIZE_BYTES) {
         SYSMSG_FUNC(Error, L"Request data size is too large");
         return false;
      }
   }

   // Handle the headers
   finalizeHeaders();

   LPCWSTR headerPtr = WINHTTP_NO_ADDITIONAL_HEADERS;
   size_t headerLen = 0;
   std::string header;
   getHeaderStr(header, headerLen);

   wstr headerWstr;
   if (headerLen != 0) {
      headerWstr = wstr::to_wstr(header.c_str(), headerLen);
      headerLen = headerWstr.length();
      headerPtr = headerWstr.c_str();
   }

   if (!WinHttpSendRequest(m_hRequest.get(), headerPtr, static_cast<DWORD>(headerLen), reqDataPtr,
                           static_cast<DWORD>(m_dataSent), static_cast<DWORD>(m_totalDataToSend),
                           reinterpret_cast<DWORD_PTR>(this))) {
      DWORD err = GetLastError();
      SYSMSG_FUNC(Error, L"WinHttpSendRequest failed: 0x%x", err);
      return false;
   }

   return true;
}


/*
 *-----------------------------------------------------------------------------
 *
 * performSync --
 *
 *    Perform a synchronous request. Uses the asynchronous logic and wraps it
 *    with events to mimic a synchronous request
 *
 * Results:
 *    true if successfully started the request. false otherwise.
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

bool
WinHttpRequest::performSync(ACTION action, const std::string &url, const std::string &body,
                            const std::string &filename)
{
   m_SyncEvent.reset(CreateEvent(0, FALSE, FALSE, 0));
   if (!m_SyncEvent) {
      SYSMSG_FUNC(Error, L"Unable to create synchronizing event");
      return false;
   }

   // We either upload from disk or from memory, not both. And it's fine to
   // not upload any data (for GET requests)
   if (!filename.empty()) {
      m_uploadFileName = filename;
   } else if (!body.empty()) {
      m_body = std::vector<char>(body.begin(), body.end());
   }

   bool result = performAsync(action, url, &Synchronize);
   if (!result) {
      SYSMSG_FUNC(Error, L"Failed to perform synchronous request");
      return false;
   }

   // The sync event will get set when the request is complete or when the
   // request is canceled (which usually happens when we're stopping)
   if (WaitForSingleObject(m_SyncEvent.get(), m_TotalTimeout_ms) != WAIT_OBJECT_0) {
      SYSMSG_FUNC(Error, L"Waiting for http request failed");
      return false;
   }
   return true;
}


/*
 *-----------------------------------------------------------------------------
 *
 * importQueryParams --
 *
 *    Parses the query string and imports the parameters into the internal
 *    query multimap
 *
 * Results:
 *    None
 *
 * Side effects:
 *    The internal query multimap is updated with the query parameters found in
 *    the query string
 *
 *-----------------------------------------------------------------------------
 */

void
WinHttpRequest::importQueryParams(wstr queryString)
{
   if (queryString.empty()) {
      // There is nothing to parse
      return;
   }

   // Remove the '?'/'#' at the beginning
   queryString.pop_front();

   // We can't use splitMap because duplicates are allowed in query strings
   std::vector<CORE::wstr> params = ::split(queryString, L'&');

   for (std::vector<CORE::wstr>::const_iterator it = params.begin(); it != params.end(); ++it) {
      auto map = ::split(*it, L'=');
      if (map.size() == 2) {
         m_queryParams.insert(std::make_pair<std::string, std::string>(
            std::string(wstr::to_mstr(map[0].c_str()).uriDecode().c_str()),
            std::string(wstr::to_mstr(map[1].c_str()).uriDecode().c_str())));
      }
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * ignoreAllCertErrors --
 *
 *    Indicate that certificate errors should be ignored. This must be called
 *      before performing an HTTP request
 *
 * Results:
 *    None
 *
 * Side effects:
 *    Sets a flag that tells the class to ignore certificate errors
 *
 *-----------------------------------------------------------------------------
 */

void
WinHttpRequest::ignoreAllCertErrors()
{
   m_ignoreAllCertErrors = true;
}


/*
 *-----------------------------------------------------------------------------
 *
 * ignoreWrongHostCertError --
 *
 *    Indicate that any mismatch between the certificate CN/SAN and the hostname
 *       should be ignored. Must be called before kicking off the request
 *
 * Results:
 *    None
 *
 * Side effects:
 *    Sets a flag that tells the class to ignore wrong host errors
 *
 *-----------------------------------------------------------------------------
 */

void
WinHttpRequest::ignoreWrongHostCertError()
{
   m_ignoreWrongHostError = true;
}


/*
 *-----------------------------------------------------------------------------
 *
 * buildChainUsingStore --
 *
 *    Specify which certificate store to use when attempting to build a
 *       certificate chain
 *
 * Results:
 *    None
 *
 * Side effects:
 *    Sets the certificate store to use when building a certificate chain
 *
 *-----------------------------------------------------------------------------
 */

void
WinHttpRequest::buildChainUsingStore(void *certStore)
{
   m_certStore = certStore;
}


/*
 *-----------------------------------------------------------------------------
 *
 * enableCertRevocation --
 *
 *    Indicate that certificate revocation checks must be made. This must be
 *      called before performing an HTTP request
 *
 * Results:
 *    None
 *
 * Side effects:
 *    Sets a flag in WinHttp to enable certificate revocation.
 *
 *-----------------------------------------------------------------------------
 */

void
WinHttpRequest::enableCertRevocation()
{
   m_enableCertRevocation = true;
}


/*
 *-----------------------------------------------------------------------------
 *
 * setHeaders --
 *
 *    Set the HTTP headers to use in the request. Must be called before
 *      performing the HTTP request
 *
 * Results:
 *    None
 *
 * Side effects:
 *    Sets the internal header member
 *
 *-----------------------------------------------------------------------------
 */

void
WinHttpRequest::setHeaders(const std::map<std::string, std::string> &headers)
{
   m_headers = headers;
}


/*
 *-----------------------------------------------------------------------------
 *
 * setQueryParams --
 *
 *    Set the query parameters to use in the request. Must be called before
 *      performing the HTTP request
 *
 * Results:
 *    None
 *
 * Side effects:
 *    Sets the internal query parameter member
 *
 *-----------------------------------------------------------------------------
 */

void
WinHttpRequest::setQueryParams(const std::multimap<std::string, std::string> &queryParams)
{
   m_queryParams = queryParams;
}


/*
 *-----------------------------------------------------------------------------
 *
 * setThumbprints --
 *
 *    Set the SSL thumbprints of a trusted certificate.
 *
 * Results:
 *    None
 *
 * Side effects:
 *    Sets the internal thumbprints member
 *
 *-----------------------------------------------------------------------------
 */

void
WinHttpRequest::setCAThumbprint(const std::string &thumbprint)
{
   m_CAThumbprints.push_back(thumbprint);
}


/*
 *-----------------------------------------------------------------------------
 *
 * setCATrustStore --
 *
 *    Set the Certificate Authority trust store in PEM format.
 *
 * Results:
 *    None
 *
 * Side effects:
 *    Sets the internal thumbprints member
 *
 *-----------------------------------------------------------------------------
 */

void
WinHttpRequest::setCATrustStore(const std::string &CAStore)
{
   m_CAThumbprints = WinCertImplementation::GetThumbprints(CAStore);
}

void
WinHttpRequest::setCATrustStore(const std::vector<std::string> &CAStoreThumbprints)
{
   m_CAThumbprints = CAStoreThumbprints;
}


/*
 *-----------------------------------------------------------------------------
 *
 * setTimeout --
 *
 *    Set the total maximum timeout for the request in seconds. Note that if a
 *    request times out, the callback will be triggered.
 *
 * Results:
 *    None
 *
 * Side effects:
 *    Sets the internal timeout member
 *
 *-----------------------------------------------------------------------------
 */

void
WinHttpRequest::setTimeout(unsigned int timeout_s)
{
   m_TotalTimeout_ms = timeout_s * 1000u;
}


/*
 *-----------------------------------------------------------------------------
 *
 * setProxyServerList --
 *
 *    Set the proxy server list. Multiple proxy servers must be delimited by
 *    a semicolon
 *
 * Results:
 *    None
 *
 * Side effects:
 *    Sets the internal proxy member
 *
 *-----------------------------------------------------------------------------
 */

void
WinHttpRequest::setProxyServerList(const std::string &proxyList)
{
   m_proxyServerList = util::corestr::stdStrToWstr(proxyList);
}


/*
 *-----------------------------------------------------------------------------
 *
 * setProxyBypass --
 *
 *    Set the proxy bypass
 *
 * Results:
 *    None
 *
 * Side effects:
 *    Sets the internal proxy member
 *
 *-----------------------------------------------------------------------------
 */

void
WinHttpRequest::setProxyBypass(const std::string &proxyBypass)
{
   m_proxyBypass = util::corestr::stdStrToWstr(proxyBypass);
}


/*
 *-----------------------------------------------------------------------------
 *
 * setProxyCredentials --
 *
 *    Set the proxy server's credentials
 *
 * Results:
 *    None
 *
 * Side effects:
 *    Sets the internal proxy credentials
 *
 *-----------------------------------------------------------------------------
 */

void
WinHttpRequest::setProxyCredentials(const std::wstring &username, const std::wstring &password)
{
   m_proxyUser = username;
   m_proxyPw ^= password;
}


/*
 *-----------------------------------------------------------------------------
 *
 * setProxyPACURL --
 *
 *    Set the proxy PAC file
 *
 * Results:
 *    None
 *
 * Side effects:
 *    Sets the internal proxy pac url
 *
 *-----------------------------------------------------------------------------
 */

void
WinHttpRequest::setProxyPACURL(const std::string &proxyPACURL)
{
   m_proxyPACURL = util::corestr::stdStrToWstr(proxyPACURL);
}


/*
 *-----------------------------------------------------------------------------
 *
 * cancelRequest --
 *
 *    Cancels an ongoing request. No-op if the request already completed. The
 *    callback registered will not be invoked. This is to guarantee that once
 *    this function returns, this object can be destroyed. This behavior also
 *    prevents the calling thread from being held up; it's expected that this
 *    function will be called when the program is shutting down.
 *
 * Results:
 *    True if the request was cancelled successfully. False otherwise. If a
 *    request is cancelled before it has been made, it is considered a success.
 *    If a request has already been completed, then 'canceling' it is also
 *    considered a success.
 *
 * Side effects:
 *    The callback set will not be invoked. This object will be safe to delete
 *    when this function returns.
 *
 *-----------------------------------------------------------------------------
 */

bool
WinHttpRequest::cancelRequest()
{
   {
      std::lock_guard<std::recursive_mutex> lock(m_handleLock);
      // Can't cancel a request that hasn't been made
      if (!m_hRequest || !m_hConnect || !m_hSession) {
         return true;
      }

      // Create the cancel event
      m_cancelEvent.reset(CreateEvent(NULL, FALSE, FALSE, NULL));
      if (!m_cancelEvent) {
         return false;
      }

      m_canceledRequest = m_hRequest.get();
      m_hRequest.reset();
      m_hConnect.reset();
      m_hSession.reset();
   }

   // Can't cancel a request that has already completed
   if (m_requestComplete) {
      return true;
   }

   if (WAIT_OBJECT_0 != WaitForSingleObject(m_cancelEvent.get(), 5000)) {
      SYSMSG_FUNC(Trace, L"Failed to wait for cancel event");
      return false;
   }

   return true;
}


/*
 *-----------------------------------------------------------------------------
 *
 * getResponseCode --
 *
 *    Returns the response code received
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

unsigned int
WinHttpRequest::getResponseCode()
{
   if (!m_requestComplete) {
      SYSMSG_FUNC(Warn, L"Request did not complete");
   }
   return m_responseCode;
}


/*
 *-----------------------------------------------------------------------------
 *
 * forceCertStoreChain --
 *
 *    The certificate chain for the server must be built using the system store.
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

void
WinHttpRequest::forceCertStoreChain()
{
   m_onlyUseProvidedStore = true;
}


/*
 *-----------------------------------------------------------------------------
 *
 * getResponseHeaders --
 *
 *    Returns the headers received in the response
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

std::string
WinHttpRequest::getResponseHeaders()
{
   if (!m_requestComplete) {
      SYSMSG_FUNC(Warn, L"Request did not complete");
   }
   return std::string(mstr::to_mstr(m_responseHeaders).c_str());
}


/*
 *-----------------------------------------------------------------------------
 *
 * finalizeHeaders --
 *
 *    Give an opportunity for headers to be added/modified before the request
 *    is sent.
 *
 * Results:
 *    The server response
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

void
WinHttpRequest::finalizeHeaders()
{
   return;
}


/*
 *-----------------------------------------------------------------------------
 *
 * requestSucceeded --
 *
 *    Determine if the request was successful
 *
 * Results:
 *    True if the request succeeded, false otherwise
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

bool
WinHttpRequest::requestSucceeded()
{
   return (m_responseCode < 300 && m_responseCode >= 200);
}


/*
 *-----------------------------------------------------------------------------
 *
 * CloseHandles --
 *
 *    Close the WinHttp handles. Necessary to prevent any callbacks from
 *    triggering and to ensure that this object can be deleted safely. As we
 *    pass this object as the context parameter to WinHTTP, this object must
 *    outlive all WinHTTP calls and is only safe to delete once the
 *    *HANDLE_CLOSING code is received.
 *
 * Results:
 *    None
 *
 * Side effects:
 *    The WinHTTP callback will be triggered with
 *    WINHTTP_CALLBACK_STATUS_HANDLE_CLOSING as the code.
 *
 *-----------------------------------------------------------------------------
 */

void
WinHttpRequest::CloseHandles()
{
   std::lock_guard<std::recursive_mutex> lock(m_handleLock);

   m_hRequest.reset();
   m_hConnect.reset();
   m_hSession.reset();
}


/*
 *-----------------------------------------------------------------------------
 *
 * AddContentTypeHeader --
 *
 *    Adds the "Content-Type" header to the request.
 *
 * Results:
 *    None
 *
 * Side effects:
 *    The header is added to the m_headers member
 *
 *-----------------------------------------------------------------------------
 */

void
WinHttpRequest::AddContentTypeHeader(HttpRequest::CONTENT_TYPE contentType)
{
   if (contentType == HttpRequest::CONTENT_TYPE::NONE) {
      return;
   }

   const static std::string contentTypeToStr[HttpRequest::CONTENT_TYPE_SIZE] = {
      "",
      "text/css",
      "text/csv",
      "text/html",
      "application/json",
      "text/plain",
      "text/xml",
      "application/octet-stream",
      "application/zip"};
   m_headers["Content-Type"] = contentTypeToStr[contentType];
}


/*
 *-----------------------------------------------------------------------------
 *
 * getHeaderStr --
 *
 *    Retrieve the header string and length
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

void
WinHttpRequest::getHeaderStr(std::string &header, size_t &headerLen) const
{
   header.clear();
   headerLen = 0;

   if (m_headers.empty()) {
      return;
   }

   for (auto &headerPair : m_headers) {
      header.append(headerPair.first + ":" + headerPair.second + "\n");
   }
   headerLen = header.length();
}


/*
 *-----------------------------------------------------------------------------
 *
 * getQueryParamStr --
 *
 *    Retrieve the query parameter string and length
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

void
WinHttpRequest::getQueryParamStr(std::string &query, size_t &queryParamLen) const
{
   query.clear();
   queryParamLen = 0;

   if (m_queryParams.empty()) {
      return;
   }

   query.assign("?");

   for (auto &queryParam : m_queryParams) {
      mstr param = mstr::printf("%s=%s&", HttpRequest::uriEncode(queryParam.first).c_str(),
                                HttpRequest::uriEncode(queryParam.second).c_str());
      query.append(param.c_str());
   }

   query.pop_back();
   queryParamLen = query.length();
}


/*
 *-----------------------------------------------------------------------------
 *
 * SendRequestComplete --
 *
 *    Contains WinHttp logic that should follow a successful WinHttpSendRequest.
 *    This method handles verifying thumbprints and asks for the response.
 *
 * Results:
 *    True if successful, false otherwise
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

bool
WinHttpRequest::SendRequestComplete()
{
   if (m_dataSent == m_totalDataToSend) {
      return ReceiveResponse();
   }
   // We still have data to send
   return UploadData();
}


/*
 *-----------------------------------------------------------------------------
 *
 * ReceiveResponse --
 *
 *    Called when we are ready to receive a response from the server. Prompts
 *    WinHTTP to wait for a response, which we then get notified at a later time
 *
 * Results:
 *    True if successful, false otherwise
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

bool
WinHttpRequest::ReceiveResponse()
{
   std::lock_guard<std::recursive_mutex> lock(m_handleLock);

   if (!m_hRequest) {
      SYSMSG_FUNC(Error, L"Request handle doesn't exist");
      return false;
   }

   if (!WinHttpReceiveResponse(m_hRequest.get(), 0)) {
      DWORD err = GetLastError();
      SYSMSG_FUNC(Error, L"WinHttpReceiveResponse failed: 0x%x %s", err, wstr::errorText(err));
      return false;
   }

   return true;
}


/*
 *-----------------------------------------------------------------------------
 *
 * UploadData --
 *
 *    Writes data to the HTTP request when performing a file upload.
 *
 * Results:
 *    True if successful, false otherwise
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

bool
WinHttpRequest::UploadData()
{
   std::lock_guard<std::recursive_mutex> lock(m_handleLock);

   if (!m_hRequest) {
      SYSMSG_FUNC(Error, L"Request handle doesn't exist");
      return false;
   }

   if (!m_uploadStream.good()) {
      SYSMSG_FUNC(Error, L"File stream is invalid");
      return false;
   }

   m_uploadStream.read(m_uploadBuffer.data(), m_uploadBuffer.size());
   std::streamsize count = m_uploadStream.gcount();

   BOOL writeResult =
      WinHttpWriteData(m_hRequest.get(), m_uploadBuffer.data(), static_cast<DWORD>(count), NULL);
   if (!writeResult) {
      DWORD lastError = GetLastError();
      SYSMSG_FUNC(Error, L"WinHttpWritedata failed: 0x%x %s", lastError,
                  wstr::errorText(lastError));
      return false;
   }

   return true;
}


/*
 *-----------------------------------------------------------------------------
 *
 * WriteComplete --
 *
 *    Handles the WINHTTP_CALLBACK_STATUS_WRITE_COMPLETE notification.
 *    Determines whether more data should be sent or to complete the request.
 *
 * Results:
 *    True if successful, false otherwise
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

bool
WinHttpRequest::WriteComplete(DWORD bytesWritten)
{
   m_dataSent += static_cast<unsigned long long>(bytesWritten);

   if (m_dataSent == m_totalDataToSend) {
      // Close up the stream now
      m_uploadStream.close();
      return ReceiveResponse();
   }
   // There's still more data to send
   return UploadData();
}


/*
 *-----------------------------------------------------------------------------
 *
 * DataAvailable --
 *
 *    Handles the WINHTTP_CALLBACK_STATUS_DATA_AVAILABLE notification.
 *    Creates a buffer and requests the WinHTTP library to write the contents
 *    of the data to it.
 *
 * Results:
 *    True if successful, false otherwise
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

bool
WinHttpRequest::DataAvailable(DWORD len)
{
   if (len == 0) {
      SYSMSG_FUNC(Debug, L"No data left to read");
      return true;
   }

   std::lock_guard<std::recursive_mutex> lock(m_handleLock);

   if (!m_hRequest) {
      SYSMSG_FUNC(Error, L"Request handle doesn't exist");
      return false;
   }

   // Create a buffer to store the data
   auto buffer = new char[len + 1];
   if (!buffer) {
      SYSMSG_FUNC(Error, L"Out of memory");
      return false;
   }

   // Tell WinHTTP to store the data in our buffer. This does not happen
   // immediately, the callback will be invoked when the data transfer is done.
   // WinHTTP takes ownership of the buffer until then.
   if (!WinHttpReadData(m_hRequest.get(), static_cast<LPVOID>(buffer), len, NULL)) {
      DWORD err = GetLastError();
      SYSMSG_FUNC(Error, L"Failed to read data response, error 0x%x %s", err, wstr::errorText(err));
      delete[] buffer;
      return false;
   }

   return true;
}


/*
 *-----------------------------------------------------------------------------
 *
 * DataComplete --
 *
 *    Handles the WINHTTP_CALLBACK_STATUS_READ_COMPLETE notification.
 *    Stores the data received from the WinHTTP library and frees the buffer
 *    created during the DataAvailable call.
 *
 * Results:
 *    True if successful, false otherwise
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

bool
WinHttpRequest::DataComplete(LPSTR data, DWORD len)
{
   if (data && len > 0) {
      m_response.append(data, len);
   }
   delete[] data;

   return QueryData();
}


/*
 *-----------------------------------------------------------------------------
 *
 * CheckHeaders --
 *
 *    Handles the WINHTTP_CALLBACK_STATUS_HEADERS_AVAILABLE notification.
 *    Provides the opportunity to check the response headers, if applicable.
 *
 * Results:
 *    True if successful, false otherwise
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

bool
WinHttpRequest::CheckHeaders()
{
   // Report the status code
   DWORD dwStatusCode = 0;
   DWORD dwStatusSize = sizeof(dwStatusCode);

   std::lock_guard<std::recursive_mutex> lock(m_handleLock);

   if (!m_hRequest) {
      SYSMSG_FUNC(Error, L"Request handle doesn't exist");
      return false;
   }

   // Report the status code
   if (WinHttpQueryHeaders(m_hRequest.get(), WINHTTP_QUERY_STATUS_CODE | WINHTTP_QUERY_FLAG_NUMBER,
                           WINHTTP_HEADER_NAME_BY_INDEX, &dwStatusCode, &dwStatusSize,
                           WINHTTP_NO_HEADER_INDEX)) {
      SYSMSG_FUNC(Debug, L"HTTP status code received: %lu", dwStatusCode);
      m_responseCode = static_cast<unsigned int>(dwStatusCode);
   } else {
      DWORD lastErr = GetLastError();
      SYSMSG_FUNC(Error, L"Unable to get status code. Error: 0x%x %s", lastErr,
                  wstr::errorText(lastErr));
      // Try to get the data anyway
   }

   // Try to get the size of the headers
   DWORD headerSize;
   BOOL result =
      WinHttpQueryHeaders(m_hRequest.get(), WINHTTP_QUERY_RAW_HEADERS_CRLF,
                          WINHTTP_HEADER_NAME_BY_INDEX, NULL, &headerSize, WINHTTP_NO_HEADER_INDEX);

   if (!result && GetLastError() == ERROR_INSUFFICIENT_BUFFER) {
      unique_heapalloc headerBuffer(static_cast<PBYTE>(HeapAlloc(GetProcessHeap(), 0, headerSize)));
      if (headerBuffer) {
         result = WinHttpQueryHeaders(m_hRequest.get(), WINHTTP_QUERY_RAW_HEADERS_CRLF,
                                      WINHTTP_HEADER_NAME_BY_INDEX, headerBuffer.get(), &headerSize,
                                      WINHTTP_NO_HEADER_INDEX);
         if (result) {
            m_responseHeaders =
               wstr(reinterpret_cast<wchar_t *>(headerBuffer.get()), headerSize / sizeof(wchar_t));
         } else {
            SYSMSG_FUNC(Error, L"Unable to get all headers. Error: %s", wstr::formatError());
         }
      } else {
         SYSMSG_FUNC(Error, L"Out of memory");
      }
   }

   return QueryData();
}


/*
 *-----------------------------------------------------------------------------
 *
 * QueryData --
 *
 *    Asks the WinHTTP library if data is available for this request.
 *
 * Results:
 *    True if successful, false otherwise
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

bool
WinHttpRequest::QueryData()
{
   std::lock_guard<std::recursive_mutex> lock(m_handleLock);

   if (!m_hRequest) {
      SYSMSG_FUNC(Error, L"Request handle doesn't exist");
      return false;
   }

   if (!WinHttpQueryDataAvailable(m_hRequest.get(), NULL)) {
      DWORD err = GetLastError();
      SYSMSG_FUNC(Error, L"Unable to query data available, error 0x%x %s", err,
                  wstr::errorText(err));
      return false;
   }
   return true;
}


/*
 *-----------------------------------------------------------------------------
 *
 * SendingRequest --
 *
 *    This method is called just before the WinHttp client sends the request.
 *    The API only calls this method when we relax the HTTPS requirements,
 *    giving us the opportunity to verify the server's certificate ourselves.
 *    So, this is the ideal place to do our own certificate validation before
 *    we send any sensitive data to the server.
 *
 *    This method may be called twice in the same request, for POST requests
 *    due to one call to WinHttpSendRequest and a subsequent call call to
 *    WinHttpWriteData. However, the server certificates only need to be
 *    verified once per request, so we will use a flag to indicate that
 *    the certificates are only validated once.
 *
 * Results:
 *    True if successful, false otherwise
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

bool
WinHttpRequest::SendingRequest()
{
   std::lock_guard<std::recursive_mutex> lock(m_handleLock);

   // Skip verification as the certificates have already been verified
   if (m_serverCertVerified) {
      SYSMSG_FUNC(Trace, L"Skip as server certificate chain has already been verified");
      return true;
   }

   if (!m_hRequest) {
      SYSMSG_FUNC(Error, L"Request handle doesn't exist");
      return false;
   }

   // Don't bother checking the certificate
   if (m_ignoreAllCertErrors) {
      return true;
   }

   // Get the certificate chain
   unique_cert_chain_context certChainCtx(GetCertChain());

   if (!certChainCtx) {
      SYSMSG_FUNC(Error, L"Unable to obtain certificate chain");
      return false;
   }

   // Verify the chain. We do not relax any SSL verification yet. First see
   // if the server's certificate meets all the criteria.
   DWORD verifyCertErr = 0;
   if (!WinCertImplementation::VerifyCertChain(certChainCtx.get(), m_hostName, false, false,
                                               verifyCertErr)) {
      SYSMSG_FUNC(Error, L"Initial VerifyCertChain failed");
      return false;
   }

   // We only entertain two errors: Unknown CA and CN mismatch
   if (verifyCertErr != ERROR_SUCCESS && verifyCertErr != CERT_E_UNTRUSTEDROOT &&
       verifyCertErr != CERT_E_CN_NO_MATCH) {
      SYSMSG_FUNC(Debug, L"Unable to verify cert chain. Error: 0x%x", verifyCertErr);
      return false;
   }

   /* If there are multiple errors with the certificate, some errors take
      precedence over others. UNTRUSTEDROOT takes precedence over CN_NO_MATCH
      (i.e. if the certificate has both an untrusted root and the CN doesn't
      match, then the error that CertVerifyCertificateChainPolicy reports is
      UNTRUSTEDROOT). This means if we get an UNTRUSTEDROOT error, we will
      need to do another check with unknown CA allowed.
    */
   bool foundUntrustedRoot = verifyCertErr == CERT_E_UNTRUSTEDROOT;
   bool foundWrongHost = verifyCertErr == CERT_E_CN_NO_MATCH;
   // Try to fish out a CN/SAN mismatch error
   if (foundUntrustedRoot) {
      // Disable the CA check and try again
      verifyCertErr = 0;
      if (!WinCertImplementation::VerifyCertChain(certChainCtx.get(), m_hostName, true, false,
                                                  verifyCertErr)) {
         SYSMSG_FUNC(Error, L"VerifyCertChain with untrustedroot ignored failed");
         return false;
      }

      // We fished out another error that we didn't expect
      if (verifyCertErr != CERT_E_CN_NO_MATCH && verifyCertErr != ERROR_SUCCESS) {
         SYSMSG_FUNC(Debug, L"Unable to verify cert chain with CA check disabled. Error: 0x%x",
                     verifyCertErr);
         return false;
      }
      foundWrongHost = verifyCertErr == CERT_E_CN_NO_MATCH;
   }

   // Fail out if we found a wrong host and it's not ignorable
   if (foundWrongHost) {
      SYSMSG_FUNC(Debug, L"Found a CN/SAN mismatch");
      if (!m_ignoreWrongHostError) {
         return false;
      }
   }

   // Next, check cert revocation (if certificate revocation checking is enabled)
   if (m_enableCertRevocation) {
      // NOTE: CRL checking will fail if the server's root certificate is not in the root cert store
      // TODO: Do a manual CRL check instead
      unique_cert_context endCertCtx;
      DWORD cbSize = sizeof(PCCERT_CONTEXT);
      if (!WinHttpQueryOption(m_hRequest.get(), WINHTTP_OPTION_SERVER_CERT_CONTEXT,
                              endCertCtx.put(), &cbSize)) {
         DWORD err = GetLastError();
         SYSMSG_FUNC(Error, L"Unable to query cert ctx, error 0x%x %s", err,
                     wstr::formatError(err));
         return false;
      }

      DWORD revocationCheck;
      DWORD revocationInfo;
      if (!WinCertImplementation::VerifyCertRevocation(certChainCtx.get(), endCertCtx.get(),
                                                       revocationCheck, revocationInfo)) {
         SYSMSG_FUNC(Debug, L"Failed to make revocation check");
         return false;
      }

      if (revocationCheck != CERT_TRUST_NO_ERROR) {
         SYSMSG_FUNC(Debug, L"Revocation check failed. Status: 0x%x Info: 0x%x", revocationCheck,
                     revocationInfo);
         return false;
      }
   }

   /*
    * Finally, determine if we can trust the certificate using the thumbprints we have.
    * But, if we already trust the certificate, we can exit early.
    */
   if (!foundUntrustedRoot) {
      m_serverCertVerified = true;
      return true;
   }

   if (m_CAThumbprints.empty()) {
      SYSMSG_FUNC(Debug, L"No trusted thumbprints provided.");
      return false;
   }

   auto serverCerts = WinCertImplementation::ExtractCertificatesFromChain(certChainCtx.get());
   if (serverCerts.empty()) {
      SYSMSG_FUNC(Debug, L"Failed to extract individual certs from chain.");
      return false;
   }

   std::vector<std::string> tps;

   HashGenerator hashGen;
   hashGen.SetAlgorithm(HashGenerator::ALGORITHM::SHA256, false);
   for (const auto &serverCert : serverCerts) {
      std::string tp = WinCertImplementation::GetThumbprint(serverCert.get(), &hashGen);
      tps.push_back(tp);

      SYSMSG_FUNC(Trace, L"Server Thumbprint: %S", tp.c_str());
   }

   bool matchedTp = false;

   for (const auto &trustedTp : m_CAThumbprints) {
      CORE::mstr trustedTpMstr(trustedTp.c_str());
      for (const auto &serverTp : tps) {
         if (trustedTpMstr.compare(serverTp.c_str(), serverTp.size()) == 0) {
            SYSMSG_FUNC(Trace, L"Matched thumbprint: %S", trustedTpMstr);
            matchedTp = true;
            break;
         }
      }
      if (matchedTp) {
         break;
      }
   }

   if (!matchedTp) {
      SYSMSG_FUNC(Debug, L"Thumbprints did not match.");
      return false;
   }

   m_serverCertVerified = true;
   return true;
}


/*
 *-----------------------------------------------------------------------------
 *
 * RequestCallback --
 *
 *    The main WinHTTP callback handler. Routes notifications to the correct
 *    methods
 *
 * Results:
 *    None
 *
 * Side effects:
 *    If any method fails, the stored callback is invoked right away.
 *
 *-----------------------------------------------------------------------------
 */

void
WinHttpRequest::RequestCallback(HINTERNET handle, DWORD code, void *info, DWORD length)
{
   bool result = true;
   switch (code) {
   case WINHTTP_CALLBACK_STATUS_DATA_AVAILABLE: {
      DWORD len = *static_cast<LPDWORD>(info);
      if (len == 0) {
         // Done, close the handles.
         m_requestComplete = true;
         CloseHandles();
         return;
      }
      result = DataAvailable(len);
      break;
   }
   case WINHTTP_CALLBACK_STATUS_HEADERS_AVAILABLE:
      result = CheckHeaders();
      break;
   case WINHTTP_CALLBACK_STATUS_READ_COMPLETE:
      result = DataComplete(static_cast<LPSTR>(info), length);
      break;
   case WINHTTP_CALLBACK_STATUS_SENDREQUEST_COMPLETE:
      result = SendRequestComplete();
      break;
   case WINHTTP_CALLBACK_STATUS_WRITE_COMPLETE: {
      DWORD *bytesWritten = static_cast<DWORD *>(info);
      result = WriteComplete(*bytesWritten);
      break;
   }
   case WINHTTP_CALLBACK_STATUS_REQUEST_ERROR: {
      wstr failedCall;
      WINHTTP_ASYNC_RESULT *war = static_cast<WINHTTP_ASYNC_RESULT *>(info);
      switch (war->dwResult) {
      case API_RECEIVE_RESPONSE:
         failedCall = L"WinHttpReceiveResponse";
         break;
      case API_QUERY_DATA_AVAILABLE:
         failedCall = L"WinHttpQueryDataAvailable";
         break;
      case API_READ_DATA:
         failedCall = L"WinHttpReadData";
         break;
      case API_WRITE_DATA:
         failedCall = L"WinHttpWriteData";
         break;
      case API_SEND_REQUEST:
         failedCall = L"WinHttpSendRequest";
         break;
      default:
         failedCall = tstr(L"unknown code: ") + tstr(war->dwResult);
      }
      auto winhttpErrorTxt = mstr::errorText(war->dwError, 0, FORMAT_MESSAGE_FROM_HMODULE,
                                             GetModuleHandle(L"winhttp.dll"));
      SYSMSG_FUNC(Error, L"%s failed: 0x%x (%S)", failedCall, war->dwError, winhttpErrorTxt);
      if (war->dwError != ERROR_WINHTTP_OPERATION_CANCELLED) {
         // Don't treat a canceled request as a failure. Canceled requests
         // should not trigger a callback.
         result = false;
      }
      break;
   }
   case WINHTTP_CALLBACK_STATUS_HANDLE_CREATED:
      // Do nothing
      break;
   case WINHTTP_CALLBACK_STATUS_HANDLE_CLOSING: {
      // Did we cancel the request?
      if (m_canceledRequest == handle) {
         if (m_SyncEvent) {
            // We ourselves are waiting for the request (we're in synchronous
            // mode). So signal the request thread.
            SetEvent(m_SyncEvent.get());
         }

         // Signal the thread that canceled the request
         SetEvent(m_cancelEvent.get());
         return;
      }

      if (m_requestComplete && m_callback) {
         if (m_SyncEvent) {
            // We're in synchronous mode. We control the callback and lifetime
            // of this object. So, there's no need to do reference logic.
            // When we perform a synchronous request, the object is most likely
            // placed on the stack. The object can go out of scope during the
            // callback and the destructor would be called with a RefCount of 2.
            // This causes a crash.
            m_callback(this, m_response);
         } else {
            // Guard against being deleted while in the callback. Not doing so
            // causes a crash with gflags page heap mode on.
            this->AddRef();
            m_callback(this, m_response);
            this->Release();
         }
         return;
      }
      break;
   }
   case WINHTTP_CALLBACK_STATUS_SENDING_REQUEST: {
      result = SendingRequest();
      break;
   }
   case WINHTTP_CALLBACK_STATUS_REQUEST_SENT:
      // Do nothing
      break;
   case WINHTTP_CALLBACK_FLAG_SECURE_FAILURE: {
      DWORD secureErrorFlag = *static_cast<LPDWORD>(info);
      if (secureErrorFlag & WINHTTP_CALLBACK_STATUS_FLAG_CERT_REV_FAILED) {
         SYSMSG_FUNC(Debug, L"Certificate revocation check failed");
      }
      if (secureErrorFlag & WINHTTP_CALLBACK_STATUS_FLAG_INVALID_CERT) {
         SYSMSG_FUNC(Debug, L"Certificate is invalid");
      }
      if (secureErrorFlag & WINHTTP_CALLBACK_STATUS_FLAG_CERT_REVOKED) {
         SYSMSG_FUNC(Debug, L"Certificate has been revoked");
      }
      if (secureErrorFlag & WINHTTP_CALLBACK_STATUS_FLAG_INVALID_CA) {
         SYSMSG_FUNC(Debug, L"Certificate Authority is unrecognized");
      }
      if (secureErrorFlag & WINHTTP_CALLBACK_STATUS_FLAG_CERT_CN_INVALID) {
         SYSMSG_FUNC(Debug, L"Certificate Common Name is incorrect");
      }
      if (secureErrorFlag & WINHTTP_CALLBACK_STATUS_FLAG_CERT_DATE_INVALID) {
         SYSMSG_FUNC(Debug, L"Certificate is expired");
      }
      if (secureErrorFlag & WINHTTP_CALLBACK_STATUS_FLAG_SECURITY_CHANNEL_ERROR) {
         SYSMSG_FUNC(Debug, L"Internal ssl library error");
      }
      if (secureErrorFlag & WINHTTP_CALLBACK_STATUS_FLAG_CERT_WRONG_USAGE) {
         SYSMSG_FUNC(Debug, L"Server's certificate has wrong usage");
      }
      break;
   }
   default:
      SYSMSG_FUNC(Debug, L"Unable to handle callback type: %d", code);
      break;
   }

   if (!result) {
      m_requestComplete = true;
      CloseHandles();
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * WinHttpCallback --
 *
 *    The static WinHTTP callback handler.
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

void CALLBACK
WinHttpRequest::WinHttpCallback(HINTERNET handle, DWORD_PTR context, DWORD code, void *info,
                                DWORD length)
{
   if (context == NULL) {
      /* The connect and session handles do not have a context associated with
         them. Ignore when these handles are closed
       */
      if (code != WINHTTP_CALLBACK_STATUS_HANDLE_CLOSING &&
          code != WINHTTP_CALLBACK_STATUS_HANDLE_CREATED) {
         SYSMSG_FUNC(Warn, L"Context is null. Internet status: %d", code);
      }
      return;
   }

   WinHttpRequest *winReq = reinterpret_cast<WinHttpRequest *>(context);

   if (!winReq) {
      SYSMSG_FUNC(Error, L"Failed to retrieve context");
      return;
   }

   winReq->RequestCallback(handle, code, info, length);
}


/*
 *-----------------------------------------------------------------------------
 *
 * Synchronize --
 *
 *    A static callback used for the synchronous calls.
 *    Allows mimicking synchronous behavior.
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

void
WinHttpRequest::Synchronize(HttpRequest *req, std::string resp)
{
   WinHttpRequest *winReq = dynamic_cast<WinHttpRequest *>(req);
   if (!winReq || !winReq->m_SyncEvent) {
      SYSMSG_FUNC(Error, L"Unable to signal sync event");
      return;
   }
   SetEvent(winReq->m_SyncEvent.get());
}


/*
 *-----------------------------------------------------------------------------
 *
 * GetCertChain --
 *
 *    Retrieve the certificate chain of the server.
 *
 *    There were some limitations with WinHTTP on older OSes, so we try to get
 *    the chain from 3 different sources in this order:
 *       1. WinHTTP itself
 *       2. An OpenSSL connection
 *       3. The Windows certificate store
 *
 *    The Windows cert store is the least reliable option. When it retrieves
 *    certificates, it guesses what the chain may be based on the CN of the
 *    issuers (it doesn't actually check the signatures). This can be
 *    problematic if there are multiple certificates with the same CN in the
 *    store (see VCART-5560). We keep this option around as it doesn't hurt
 *    to have more fallbacks.
 *
 *    OpenSSL is the most reliable option, but we don't want to open another
 *    connection if we don't have to.
 *
 * Results:
 *    The certificate chain
 *
 * Side effects:
 *    May open another connection with OpenSSL to the server.
 *
 *-----------------------------------------------------------------------------
 */

PCCERT_CHAIN_CONTEXT
WinHttpRequest::GetCertChain()
{
   if (m_certStore == NULL && m_onlyUseProvidedStore) {
      SYSMSG_FUNC(Debug, L"Unable to build certificate chain: missing store");
      return NULL;
   }

   PCCERT_CHAIN_CONTEXT certChainCtx = NULL;
   DWORD cbSize = sizeof(PCCERT_CHAIN_CONTEXT);

   // Try to get the chain directly from WinHttp. This will fail on older OSes.
   if (!m_onlyUseProvidedStore) {
      // This option is only supported on Windows 10 2004 and above
      if (WinHttpQueryOption(m_hRequest.get(), WINHTTP_OPTION_SERVER_CERT_CHAIN_CONTEXT,
                             &certChainCtx, &cbSize)) {
         return certChainCtx;
      }

      DWORD lastErr = GetLastError();
      if (lastErr != ERROR_INVALID_PARAMETER && lastErr != ERROR_WINHTTP_INVALID_OPTION) {
         SYSMSG_FUNC(Error, L"Unable to query cert chain ctx, error 0x%x %s", lastErr,
                     wstr::errorText(lastErr));
         return NULL;
      }
   }

   unique_cert_context certCtx;
   cbSize = sizeof(PCCERT_CONTEXT);
   if (!WinHttpQueryOption(m_hRequest.get(), WINHTTP_OPTION_SERVER_CERT_CONTEXT, certCtx.put(),
                           &cbSize)) {
      DWORD lastErr = GetLastError();
      SYSMSG_FUNC(Error, L"Unable to query cert ctx, error 0x%x %s", lastErr,
                  wstr::errorText(lastErr));
      return NULL;
   }

#ifdef WINHTTP_NO_OPENSSL
   SYSMSG_FUNC(Trace, L"Skipping retrieving the certificate chain via OpenSSL.");
#else
   if (!m_onlyUseProvidedStore) {
      SYSMSG_FUNC(Debug, L"Retrieving the server certificate chain via OpenSSL.");
      auto certchain = m_sslHandler.GetCertChainCtx(m_fullURL, certCtx.get());
      if (certchain) {
         return certchain;
      }
   }
#endif

   SYSMSG_FUNC(Debug, L"Building the certificate chain using the system store");

   CERT_CHAIN_PARA chainPara;
   memset(&chainPara, 0, sizeof(CERT_CHAIN_PARA));
   chainPara.cbSize = sizeof(CERT_CHAIN_PARA);
   chainPara.RequestedUsage.dwType = USAGE_MATCH_TYPE_AND;
   chainPara.RequestedUsage.Usage.cUsageIdentifier = 0;
   chainPara.RequestedUsage.Usage.rgpszUsageIdentifier = NULL;
   if (!CertGetCertificateChain(NULL, certCtx.get(), NULL, m_certStore, &chainPara, 0, NULL,
                                &certChainCtx)) {
      DWORD lastErr = GetLastError();
      SYSMSG_FUNC(Error, L"CertGetCertificateChain Error: 0x%x %s", lastErr,
                  wstr::errorText(lastErr));
      return NULL;
   }

   // Return cert chain if built successfully using the system cert store
   if (WinCertImplementation::GetNumCertsInChainCtx(certChainCtx) > 1) {
      return certChainCtx;
   }
   return NULL;
}


/*
 *-----------------------------------------------------------------------------
 *
 * getPACURL --
 *
 *    Retrieve the PAC url
 *
 * Results:
 *    The PACURL
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

wstr
WinHttpRequest::getPACURL()
{
   // Priority order: 1. Passed in value 2. auto-detected value 3. registry
   if (!m_proxyPACURL.empty()) {
      return m_proxyPACURL;
   }

   DWORD autoDetectFlags = WINHTTP_AUTO_DETECT_TYPE_DHCP | WINHTTP_AUTO_DETECT_TYPE_DNS_A;
   LPWSTR autoDetectPAC;
   if (WinHttpDetectAutoProxyConfigUrl(autoDetectFlags, &autoDetectPAC)) {
      unique_lpwstr autoDetectPACDel(autoDetectPAC);
      return CORE::wstr(autoDetectPAC);
   }

   m_IEProxySettings.init();
   return m_IEProxySettings.mProxyConfig.PACURL;
}


/*
 *-----------------------------------------------------------------------------
 *
 * getProxyURL --
 *
 *    Retrieve the proxy url. Must be run after the session handle is open.
 *
 * Results:
 *    True if a proxy was found. False otherwise
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

bool
WinHttpRequest::getProxyURL(const wstr &PACURL, NetProxyConfig &proxyConfig)
{
   // Try to get the proxy server using the PAC file
   if (!PACURL.empty() && m_hSession) {
      WINHTTP_AUTOPROXY_OPTIONS autoProxyOptions;
      WINHTTP_PROXY_INFO proxyInfo;
      memset(&autoProxyOptions, 0, sizeof(autoProxyOptions));
      memset(&proxyInfo, 0, sizeof(proxyInfo));

      autoProxyOptions.dwFlags = WINHTTP_AUTOPROXY_CONFIG_URL;
      autoProxyOptions.lpszAutoConfigUrl = PACURL.p();
      autoProxyOptions.fAutoLogonIfChallenged = TRUE;

      // Note: This method caches its results for faster future access
      if (WinHttpGetProxyForUrl(m_hSession.get(), m_fullURL.p(), &autoProxyOptions, &proxyInfo)) {
         unique_lpwstr proxyUrl(proxyInfo.lpszProxy);
         unique_lpwstr proxyBypass(proxyInfo.lpszProxyBypass);

         SYSMSG_FUNC(Trace, L"Ran PAC file. Proxy access type: %d", proxyInfo.dwAccessType);

         if (proxyInfo.dwAccessType == WINHTTP_ACCESS_TYPE_NAMED_PROXY) {
            proxyConfig.proxy = proxyUrl.get();
            proxyConfig.proxyBypass = proxyBypass.get();
            return true;
         }
      } else {
         DWORD err = GetLastError();
         SYSMSG_FUNC(Debug, L"Unable to find proxy with given pac file: 0x%x", err);
      }
   }

   /* PAC lookup failed. Fall back to the given proxy server list if it was
    * passed in. This could occur if we received both a PACURL and a proxy
    * server, but the PACURL ended up being faulty
    */
   if (!m_proxyServerList.empty()) {
      SYSMSG_FUNC(Debug, L"Falling back to provided proxy server");
      proxyConfig.proxy = m_proxyServerList;
      proxyConfig.proxyBypass = m_proxyBypass;
      return true;
   }

   // Finally, try searching the registry.
   m_IEProxySettings.init();
   if (!m_IEProxySettings.mAutoDetect && !m_IEProxySettings.mProxyConfig.proxy.empty()) {
      proxyConfig.proxy = m_IEProxySettings.mProxyConfig.proxy;
      proxyConfig.proxyBypass = m_IEProxySettings.mProxyConfig.proxyBypass;
      return true;
   }

   return false;
}


namespace {


/*
 *-----------------------------------------------------------------------------
 *
 * getThumbprints --
 *
 *    Generates thumbprints from the given CERT_CONTEXT
 *
 * Results:
 *    SSL thumbprints, in uppercase hex.
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

std::string
getThumbprints(PCCERT_CONTEXT context)
{
   mstr hash;

   HashGenerator hashGen;
   hashGen.SetAlgorithm(HashGenerator::ALGORITHM::SHA256, false);

   bool success =
      hashGen.GenerateHash(reinterpret_cast<char *>(context->pbCertEncoded), context->cbCertEncoded,
                           nullptr, 0, HashGenerator::OUTPUTFMT::HEX_UPPER_WITH_DELIM, hash);

   if (!success) {
      SYSMSG_FUNC(Error, L"Unable to generate hash");
      return "";
   }

   return std::string(hash.c_str());
}


std::vector<CORE::wstr>
split(const wstr &str, wchar_t splitChar)
{
   std::vector<CORE::wstr> res;

   size_t offset = 0;
   size_t found = 0;
   while ((found = str.find(splitChar, offset)) != (size_t)-1) {
      wstr sub = str.substr(offset, found - offset);
      if (sub.size()) {
         res.push_back(sub);
      }
      offset = found + 1;
   }
   if (offset < str.size()) {
      res.push_back(str.substr(offset));
   }
   return res;
}
}; // namespace


/*
 *-----------------------------------------------------------------------------
 *
 * WinHttpRequestFactory::GetHttpRequestClient --
 *
 *    Creates WinHttpRequest objects
 *
 * Results:
 *    An WinHttpRequest object.
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

std::shared_ptr<HttpRequest>
WinHttpRequestFactory::GetHttpRequestClient()
{
   return std::shared_ptr<HttpRequest>(new WinHttpRequest,
                                       [](WinHttpRequest *req) { req->Release(); });
}
