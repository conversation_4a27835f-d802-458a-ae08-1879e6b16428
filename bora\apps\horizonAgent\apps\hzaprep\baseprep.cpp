/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */


/* ----------------------------------------------
 * baseprep.cpp
 * ---------------------------------------------- */

#include "stdafx.h"
#include "baseprep.h"
#include <ws2tcpip.h>
#include "horizonPaths.h"
#include <Ws2ipdef.h>
#include "utilWinInet.h"

#define MAX_BROKER_RETRIES 10
#define IP_CHECK_RETRIES 150

BasePrep::BasePrep() {}

BasePrep::~BasePrep() {}


/*
 *----------------------------------------------------------------------------
 *
 * BasePrep::createPersistentKeyPair --
 *
 *      Creates persistent keypair in KeyVault.
 *
 * Returns:
 *      true if successful otherwise false
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */
bool
BasePrep::createPersistentKeyPair(wstr keypairName, int keyLength)
{
   if (keypairName.empty()) {
      SYSMSG_FUNC(Error, L"Empty keypair name string.");
      return false;
   }

   // 1. Check for existence of the master key, if not present then create it.
   kvCallResult result;
   PropertyBag params, response;
   params.add(PAR_NAME, mMasterKeyName.c_str());
   result = kvCallWithRetry(HINT_LATEST, params, response);

   if (result == kvCallBroken) {
      // Shouldn't move forward with anymore KeyVault requests.
      SYSMSG_FUNC(Error, L"Failed to execute the KeyVault operation.");
      return false;
   }

   if (result != kvCallSuccess) {
      params.addInt(PAR_LENGTH, keyLength);
      result = kvCallWithRetry(HINT_ADD_MASTER, params, response);

      if (result == kvCallBroken) {
         SYSMSG_FUNC(Debug, L"Failed to execute the KeyVault operation.");
         return false;
      }
      if (result != kvCallSuccess) {
         SYSMSG_FUNC(Error, L"Cannot generate master key: %s", response.getErrorText());
         return false;
      }
   }

   int gen = response.getInt(PAR_GEN, 0);
   if (gen == 0) { // Should not happen.
      SYSMSG_FUNC(Error, L"Master key is still not generated.");
      return false;
   }

   params.clear();
   response.clear();

   // 2. Create persistent keypair.
   params.addInt(PAR_LENGTH, keyLength);
   params.addInt(PAR_ENCIPHERGEN, gen);
   params.add(PAR_NAME, keypairName);
   params.addBool(PAR_PERSIST, true);
   params.add(PAR_ENCIPHER_NAME, mMasterKeyName);

   result = kvCallWithRetry(HINT_ADD_KEYPAIR, params, response);

   if (result == kvCallBroken) {
      SYSMSG_FUNC(Debug, L"Failed to execute the KeyVault operation.");
      return false;
   }

   if (result != kvCallSuccess) {
      SYSMSG_FUNC(Error, L"Cannot generate key pair: %s", response.getErrorText());
      return false;
   }

   return true;
}

/*
 *----------------------------------------------------------------------------
 *
 * BasePrep::remove --
 *
 *      Removes keypair in KeyVault.
 *
 * Returns:
 *      true if successful otherwise false
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */
bool
BasePrep::remove(wstr keyPairName)
{
   PropertyBag params, response;

   if (!keyPairName.size()) {
      SYSMSG_FUNC(Error, L"Empty keypair name");
      return false;
   }

   params.add(PAR_NAME, keyPairName);
   params.addBool(PAR_CHECKDEPENDENTS, true);

   kvCallResult result = kvCallWithRetry(HINT_REMOVE, params, response);
   if (result == kvCallBroken) {
      SYSMSG_FUNC(Debug, L"Failed to execute the KeyVault operation.");
      return false;
   }

   if (result != kvCallSuccess) {
      SYSMSG_FUNC(Error, L"Failed to remove key with name %s: %s", keyPairName,
                  response.getErrorText());
      return false;
   }

   return true;
}

/*
 *----------------------------------------------------------------------------
 *
 * BasePrep::signHash --
 *
 *      signhash
 *
 * Returns:
 *      true if successful otherwise false
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */
bool
BasePrep::signHash(wstr keyName, MsgBinary &sigData)
{
   // TODO: implement by sending 'signHash' mfw msg to keyvault
   wstr dummySign = L"dummy signed hash";
   sigData.set(dummySign.p_upd(), dummySign.size());
   return true;
}

/*
 *----------------------------------------------------------------------------
 *
 * BasePrep::keyExists --
 *
 *      Look for a keyName exists in keyvalult
 *
 * Returns:
 *      true if key exists otherwise false
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */
bool
BasePrep::keyExists(wstr keyName)
{
   PropertyBag params, response;
   params.add(PAR_NAME, keyName.c_str());
   kvCallResult result = kvCallWithRetry(HINT_LATEST, params, response);
   if (result == kvCallBroken) {
      SYSMSG_FUNC(Debug, L"Failed to execute the KeyVault operation for %s", HINT_LATEST);
      return false;
   }

   if (result != kvCallSuccess) {
      SYSMSG_FUNC(Error, L"Failed to check %s key exists with error: %s (%d)", keyName,
                  response.getErrorText(), result);
      return false;
   }

   wstr error = response.getErrorText(L"");
   if (!error.empty() || (response.getError() != -1)) {
      SYSMSG_FUNC(Error, L"Key %s check failed with error: %s", keyName, response.getErrorText());
      return false;
   }

   return true;
}

/*
 *----------------------------------------------------------------------------
 *
 * BasePrep::generateNonce --
 *
 *      Generates a nonce of specified length
 *
 * Returns:
 *      true if successful otherwise false
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */
bool
BasePrep::generateNonce(int length,           // IN
                        MsgBinary &msgBinary) // OUT
{
   PropertyBag params, response;
   params.addInt(PAR_LENGTH, length);
   kvCallResult result = kvCallWithRetry(HINT_GET_RANDOM, params, response, 0, &msgBinary);
   if (result == kvCallBroken) {
      SYSMSG_FUNC(Debug, L"Failed to execute the KeyVault operation.");
      return false;
   }

   if (result != kvCallSuccess) {
      SYSMSG_FUNC(Error, L"Failed to generate nonce: %s", response.getErrorText());
      return false;
   }

   return true;
}

/*
 *----------------------------------------------------------------------------
 *
 * BasePrep::getVmGuid --
 *
 *      Retrieves the VM guid present in the registry entry
 *      HKLM\Software\Omnissa\Horizon\Node Manager\Server DN
 *
 * Returns:
 *      true if successful otherwise false
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */
bool
BasePrep::getVmGuid(wstr &vmGuid)
{
   wstr serverDn = wstr::readRegistry(utils::regPath() + L"\\Node Manager\\Server DN", L"");
   if (!serverDn.size()) {
      SYSMSG_FUNC(Error, L"Server DN is not set in the registry.");
      return false;
   }
   vmGuid = CORE::ldapUtils::nameFromDn(serverDn);

   if (!vmGuid.size()) {
      SYSMSG_FUNC(Error, L"Empty VM Guid in Server DN.");
      return false;
   }

   return true;
}

/*
 *----------------------------------------------------------------------------
 *
 * BasePrep::getPublicKey --
 *
 *      Retrieves the public key part of the keypair from KeyVault, along with
 *      signature (if signature data is provided)
 *
 * Returns:
 *      true if successful otherwise false
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */
bool
BasePrep::getPublicKey(wstr keyName,         // IN
                       wstr sigData,         // IN
                       MsgBinary &pubKey,    // OUT
                       MsgBinary &signature) // OUT
{
   PropertyBag params, response, transform;

   if (!keyName.size()) {
      SYSMSG_FUNC(Error, L"Empty keypair name");
      return false;
   }
   params.add(PAR_NAME, keyName);

   if (sigData.size()) {
      params.addBinary(PAR_BINDATATOSIGN, sigData.p_upd(), sigData.s());
   }

   // Add the transform bag:
   transform.add(PAR_TRANSFORM_ALGID, VAL_SHA256_ALGORITHM);
   params.addBag(PAR_TRANSFORM, transform);

   kvCallResult result = kvCallWithRetry(HINT_GET_PUBKEY, params, response, 0, &pubKey);

   if (result != kvCallSuccess) {
      SYSMSG_FUNC(Error, L"Cannot get public key:%s", response.getErrorText());
      return false;
   }

   // Retrieve PoP blob if originally supplied with sigdata.
   if (sigData.size()) {
      void *data = NULL;
      size_t dataLen = 0;
      response.getBinary(PAR_BINDATASIGNED, &data, &dataLen);
      signature.set(data, dataLen, true);
   }

   return true;
}

/*
 *----------------------------------------------------------------------------
 *
 * BasePrep::connectAndSend --
 *
 *      Connects to the provided broker via the Framework channel and sends it
 *      the specified message.
 *
 * Returns:
 *      true if successful otherwise false
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */
bool
BasePrep::connectAndSend(const wstr &brokerFQDN, const wstr &queueName, const wstr &messageHint,
                         const PropertyBag &request, PropertyBag &response)
{
   bool ret = false;

   if (brokerFQDN.empty()) {
      SYSMSG_FUNC(Error, L"Empty broker FQDN provided.");
      return ret;
   }

   if (queueName.empty()) {
      SYSMSG_FUNC(Error, L"Empty queue name provided.");
      return ret;
   }

   if (messageHint.empty()) {
      SYSMSG_FUNC(Error, L"Empty message hint provided.");
      return ret;
   }

   MessageChannel *channel =
      MessageFrameWork::System()->ConnectChannel(MessageFrameWork::Socket, NULL, NULL, brokerFQDN);

   if (!channel) {
      SYSMSG_FUNC(Error, L"ConnectChannel failed!");
      return ret;
   }

   MessageHandler::respType result = MessageHandler::MsgOk;

   for (int retry = 0; retry < MAX_BROKER_RETRIES; ++retry) {
      result = MessageFrameWork::System()->SendMsg(queueName, messageHint, request, response, 0, 0,
                                                   channel);
      if (result == MessageHandler::MsgOk) {
         ret = true;
         SYSMSG_FUNC(Trace, L"SendMsg succeeded on attempt %d.", ++retry);
         break;
      }

      if (result == MessageHandler::MsgSystemBusy) {
         SYSMSG_FUNC(Trace, L"SendMsg failed: %s (%s)", response.getErrorText(),
                     MessageHandler::respTypeName(result));
         Sleep(1000);
         response.clear();
         continue;
      }

      /*
       * The result is neither MsgOk nor MsgSystemBusy, so let's get out of the
       * loop.
       */
      break;
   }

   if (!ret) {
      SYSMSG_FUNC(Error, L"SendMsg failed: %s (%s)", response.getErrorText(),
                  MessageHandler::respTypeName(result));
   }

   MessageFrameWork::System()->CloseChannel(channel);

   return ret;
}

/*
 *----------------------------------------------------------------------------
 *
 * BasePrep::kvCallWithRetry --
 *
 *      Sends message to KeyVault queue, retries it once in case if MFW is busy
 *      or if KeyVault is locked. Retry is done only once because on the machine
 *      a single instance of hzaprep.exe will be running at a time, the Horizon
 *      Agent will be disabled during this time so the number of MFW\KeyVault
 *      operations shoudln't be enough to overwhelm the MFW and KeyVault.
 *
 * Returns:
 *      kvCallSuccess in case of successful call to KeyVault, kvCallFailure if
 *      there was error and kvCallBroken in case we are not able to get the
 *      message processed by KeyVault.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */
BasePrep::kvCallResult
BasePrep::kvCallWithRetry(const wstr &hint, const PropertyBag &params, PropertyBag &response,
                          MsgBinary *bin, MsgBinary *binResp)
{
   MessageHandler::respType result;
   int retry;

   for (retry = 0; retry < MAX_KV_RETRIES; ++retry) {
      result = MessageFrameWork::System()->SendMsg(QUEUE_KEYVAULT, hint, params, response, 0, 0, 0,
                                                   (DWORD)-1, 0, bin, true, binResp);
      if (result == MessageHandler::MsgOk) {
         SYSMSG_FUNC(Debug, L"KeyVault call '%s' succeeded on attempt %d", hint, ++retry);
         return kvCallSuccess;
      }

      // Not executed due to MFW being busy or KeyVault being locked.
      if (result == MessageHandler::MsgSystemBusy ||
          (result == MessageHandler::MsgError &&
           response.getError() == KeyVaultError::KVErrLocked)) {
         SYSMSG_FUNC(Trace, L"KeyVault call '%s' not executed: %s (%s)", hint,
                     response.getErrorText(), MessageHandler::respTypeName(result));
         /*
          * KeyVault is busy, wait a sec and try again.
          */
         Sleep(1000);
         response.clear();
         continue;
      }

      /*
       * Break out of the loop because it is some other error and there is no
       * need to retry.
       */
      break;
   }

   if (result != MessageHandler::MsgError || response.getError() == KeyVaultError::KVErrLocked) {
      SYSMSG_FUNC(Error, L"KeyVault call '%s' could not be executed: %s (%s)", hint,
                  response.getErrorText(), MessageHandler::respTypeName(result));
      return kvCallBroken;
   }

   /*
    * Log some information in case result was MessageHandler::MsgError and
    * KeyVault is not locked.
    */
   if (retry) {
      SYSMSG_FUNC(Debug, L"KeyVault call '%s' returned %s (%s) on attempt %d", hint,
                  response.getErrorText(), MessageHandler::respTypeName(result), ++retry);
   } else {
      SYSMSG_FUNC(Debug, L"KeyVault call '%s' failed: %s (%s)", hint, response.getErrorText(),
                  MessageHandler::respTypeName(result));
   }

   return kvCallFailure;
}


/*
 *----------------------------------------------------------------------------
 *
 * BasePrep::getHostName --
 *
 *    get the extended computer name
 *
 * Results:
 *    returns ComputerNameDnsFullyQualified
 *
 * Side effects:
 *    None.
 *
 *----------------------------------------------------------------------------
 */
wstr
BasePrep::getHostName()
{
   // Get FQDN of the agent machine.
   wstr hostname = wstr::getComputerName();
   SYSMSG_FUNC(Trace, TEXT("Agent hostname '%s' "), hostname);
   return hostname;
}

/*
 *----------------------------------------------------------------------------
 *
 * BasePrep::getMacAddr --
 *
 *    retrieve MAC address(es) from adapters
 *
 * Results:
 *    MAC address if successful. Otherwise, empty string.
 *
 * Side effects:
 *    None.
 *
 *----------------------------------------------------------------------------
 */
wstr
BasePrep::getMacAddr()
{
   if (mMacAddrs.empty()) {
      // Fetch only mac addresses, no IP
      if (!FetchNetworkData(false)) {
         SYSMSG_FUNC(Debug, L"FetchNetworkData failed");
      }

      if (mMacAddrs.empty()) {
         SYSMSG_FUNC(Error, L"MAC address is still empty after fetch");
      }
   }

   return mMacAddrs;
}


/*
 *----------------------------------------------------------------------------
 *
 * BasePrep::readRegServerFQDNs --
 *
 *      Retrieves the Broker FQDNs present in the registry entry
 *      HKLM\Software\Omnissa\Horizon\Agent\Configuration\Broker
 *
 * Returns:
 *      returns a list of broker FQDNs
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */
vwstr
BasePrep::readRegServerFQDNs()
{
   vwstr srvFQDNs;
   wstr fqdns = wstr::readRegistry(utils::regPath() + REG_SRVS_FQDNS, L"");
   if (!(fqdns.empty())) {
      srvFQDNs = fqdns.split(VAL_SRV_FQDNS_DELIMETER);
   }

   return srvFQDNs;
}


/*
 *----------------------------------------------------------------------------
 *
 * BasePrep::writeRegPoolID --
 *
 *    Write PoolID from registry (\Agent\Configuration\PoolID).
 *
 * Results:
 *    returns true on success.
 *
 * Side effects:
 *    None.
 *
 *----------------------------------------------------------------------------
 */
bool
BasePrep::writeRegPoolID(const wstr &poolID)
{
   return poolID.writeRegistry(utils::regPath() + REG_POOL_ID, REG_SZ);
}


/*
 *----------------------------------------------------------------------------
 *
 * BasePrep::readRegPoolID --
 *
 *    Read PoolID from registry (\Agent\Configuration\PoolID).
 *
 * Results:
 *    returns PoolID value on success else empty string
 *
 * Side effects:
 *    None.
 *
 *----------------------------------------------------------------------------
 */
wstr
BasePrep::readRegPoolID()
{
   return wstr::readRegistry(utils::regPath() + REG_POOL_ID, L"");
}

/*
 *----------------------------------------------------------------------------
 *
 * BasePrep::FetchNetworkData --
 *
 *    Fetch the Mac & IP addresses so that latest data is available to iterate
 *    through especially when looking for IP address. This is useful when the
 *    machine was earlier assigned a link-local address and then gets assigned
 *    proper IP address.
 *
 * Results:
 *
 * Side effects:
 *    None.
 *
 *----------------------------------------------------------------------------
 */

bool
BasePrep::FetchNetworkData(bool getIp /*= true*/)
{
   SOCKADDR_IN sockaddr4 = {};
   SOCKADDR_IN6 sockaddr6 = {};
   wstr macAddrIPv4, macAddrIPv6;

   // remove any existing values
   mMacAddrs.clear();
   mIpAddr4.clear();
   mIpAddr6.clear();

   // we will get sockets and mac addresses with this one call
   if (!(util::WinInet::GetInterfaceAddresses(nullptr, &sockaddr4, &sockaddr6, nullptr,
                                              &macAddrIPv4, nullptr, &macAddrIPv6, nullptr))) {
      SYSMSG_FUNC(Error, TEXT("util::WinInet::GetInterfaceAddresses failed"));
      return false;
   }
   SYSMSG_FUNC(Debug, TEXT("IPv4 mac address: %s IPv6 mac address: %s "), macAddrIPv4.p(),
               macAddrIPv6.p());

   if (!macAddrIPv4.empty()) {
      mMacAddrs.append(macAddrIPv4);
   }
   if (!macAddrIPv6.empty()) {
      if (!mMacAddrs.empty()) {
         mMacAddrs.append(L";"); // Add separator before appending
      }
      mMacAddrs.append(macAddrIPv6);
   }

   // Get IP addresses using the sockets we already fetched
   if (getIp) {
      auto fetchIp = [&](PSOCKADDR addr, wstr &ipAddr) -> bool {
         wstr ip;
         int result = util::WinInet::GetIpAddress(addr, ip);
         if (result != 0) {
            SYSMSG_FUNC(Error, TEXT("util::WinInet::GetIpAddress failed, Error %d"), result);
            return false;
         }
         if (!ip.empty()) {
            SYSMSG_FUNC(Debug, TEXT("Resolved IP address: %s"), ip);
            ipAddr.append(ip);
         }
         return true;
      };

      bool ipFound = fetchIp(reinterpret_cast<PSOCKADDR>(&sockaddr4), mIpAddr4) ||
                     fetchIp(reinterpret_cast<PSOCKADDR>(&sockaddr6), mIpAddr6);
      return ipFound;
   }

   return true;
}

/*
 *-------------------------------------------------------------------------------
 *
 * BasePrep::IsLocalLink --
 *
 *    Checks if the given IP address is a link-local address.
 *
 * Returns:
 *    true if the IP address is link-local, otherwise false.
 *
 * Side effects:
 *    None.
 *
 *-------------------------------------------------------------------------------
 */
bool
BasePrep::IsLocalLink(const wstr &ipAddr)
{
   // Check for IPv4 link-local prefix "169.254"
   if (ipAddr.find(L"169.254") == 0) {
      return true;
   }

   // Check for IPv6 link-local range "fe80::/10"
   if (ipAddr.size() >= 4 && tolower(ipAddr[0]) == 'f' && tolower(ipAddr[1]) == 'e') {
      char nibble = tolower(ipAddr[2]);
      if (nibble >= '8' && nibble <= 'b') {
         return true;
      }
   }

   return false;
}


/*
 *----------------------------------------------------------------------------
 *
 * BasePrep::getIPAddr --
 *
 *    util method to retrieve the machine's IP address
 *
 * Results:
 *    IP Address in string form if successful, otherwise empty string.
 *
 * Side effects:
 *    None.
 *
 *----------------------------------------------------------------------------
 */

wstr
BasePrep::getIPAddr()
{
   /*
    * When DHCP service is started, it has been observed that it takes
    * at least a couple of retries to get the IP address.
    */
   int retry = 0;

   while (retry < IP_CHECK_RETRIES) {
      if (!FetchNetworkData()) {
         SYSMSG_FUNC(Debug, L"FetchNetworkData failed, retry %d", retry);
      }

      // Check if mIpAddr4 is valid
      if (!mIpAddr4.empty() && !IsLocalLink(mIpAddr4)) {
         SYSMSG_FUNC(Debug, L"Found valid IPv4 address: %s", mIpAddr4);
         return mIpAddr4;
      }

      // fallback to mIpAddr6
      if (!mIpAddr6.empty() && !IsLocalLink(mIpAddr6)) {
         SYSMSG_FUNC(Debug, L"Found valid IPv6 address: %s", mIpAddr6);
         return mIpAddr6;
      }

      SYSMSG_FUNC(Debug, L"Invalid or link-local IP address: %s, %s", mIpAddr4, mIpAddr6);
      Sleep(VAL_SLEEP_IP_CHECK);
      retry++;
   }

   SYSMSG_FUNC(Error, L"Failed to obtain a valid IP address");
   return L"";
}
