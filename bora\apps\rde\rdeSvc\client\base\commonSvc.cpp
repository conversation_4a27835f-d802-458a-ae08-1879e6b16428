/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * commonSvc.cpp --
 *
 *     CommonSvc class for sending/receiving various messages to/from rde server.
 *
 */

#include "shared/rdeSvc_defines.h"
#include "shared/commonSvcMsg.h"
#include "commonSvc.h"
#include "dpiSyncClient.h"
#include "rdsAadAuthClient.h"

#include "str.h"
#include "util.h"

#include <vdpObserverNameDefs.h>

extern VDPRdeCommonClient_Interface &GetRdeCommonClientInterface();


/*
 *-----------------------------------------------------------------------------
 *
 * CommonSvc::CommonSvc --
 *
 *    Constructor.
 *
 * Return value:
 *    None.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

CommonSvc::CommonSvc() : mDpiSyncClient(this), mRdsAadAuthClient(this)
{
   Log("%s: Start CommonSvc.\n", __FUNCTION__);

   mVdpRdeCommonClientId = INVALID_RDECOMMON_CLIENT_ID;

   mVdpRdeCommonClientSink.version = VDPRDECOMMONCLIENT_SINK_V1;
   mVdpRdeCommonClientSink.v1.RdeCommonClientSink_Send = OnVdpClientSendMsg;
   mClientGenericObserverId =
      GetObserverInterface().v1.RegisterObserver(RDE_COMMON_GENERIC_CMD, this, OnClientGenericMsg);
#if defined(__APPLE__) || defined(_WIN32)
   mFeatureEnableStatus = 0;
   mFeatureEnablementCapacity = 0;
   mFeatureEnablementCapacity |= BLOCK_SCREEN_CAPTURE_MASK;
   mFeatureEnablementCapacity |= BLOCK_KEY_LOGGER_MASK;
#endif
#if defined(_WIN32)
   mFeatureEnablementCapacity |= BLOCK_THUMBNAIL_REPRESENTATION_MASK;
   mFeatureEnablementCapacity |= BLOCK_SEND_INPUT_MASK;
#endif
}


/*
 *-----------------------------------------------------------------------------
 *
 * CommonSvc::~CommonSvc --
 *
 *    Destructor.
 *
 * Return value:
 *    None.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

CommonSvc::~CommonSvc()
{
   if (IsValidRdeCommonClient(mVdpRdeCommonClientId)) {
      VDPRdeCommonClient_Interface &vdpRdeCommonClient = GetRdeCommonClientInterface();
      if (!vdpRdeCommonClient.v1.RdeCommonClient_Destroy(mVdpRdeCommonClientId)) {
         Log("%s: Failed to destroy vdp rde common client instance.\n", __FUNCTION__);
      }
   }
   if (mClientGenericObserverId != VDPOBSERVER_INVALID_ID) {
      GetObserverInterface().v1.UnregisterObserver(mClientGenericObserverId);
      mClientGenericObserverId = VDPOBSERVER_INVALID_ID;
   }

   Log("%s: End CommonSvc.\n", __FUNCTION__);
}


/*
 *-----------------------------------------------------------------------------
 *
 * CommonSvc::Init --
 *
 *    Initializes CommonSvc object.
 *
 * Results:
 *    True if successful or false otherwise.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

bool
CommonSvc::Init(void)
{
   if (!CreateObject(COMMONSVC_OBJ_NAME, VDP_RPC_OBJ_CONFIG_INVOKE_ALLOW_ANY_THREAD)) {
      Log("%s: Failed to create object.\n", __FUNCTION__);
      return false;
   }

   VDPRdeCommonClient_Interface &vdpRdeCommonClient = GetRdeCommonClientInterface();
   if (!vdpRdeCommonClient.v1.RdeCommonClient_Create(this, &mVdpRdeCommonClientSink,
                                                     &mVdpRdeCommonClientId)) {
      Log("%s: Failed to create vdp rde common client instance.\n", __FUNCTION__);
      return false;
   }

   return true;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CommonSvc::SendMsg --
 *
 *    Sends a message to the server.
 *
 * Return value:
 *    true if successful or false otherwise.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

bool
CommonSvc::SendMsg(uint32 id,          // IN: message id
                   const char *cookie, // IN (OPT): client data passed with client request
                   const char *msg,    // IN
                   uint32 msgLen)      // IN
{
   bool success = false;
   uint32 invokeId = 0;
   util::ChannelContextHandle context;

   if (GetObjectState() == VDP_RPC_OBJ_DISCONNECTED) {
      Log("%s: ignore msg due to object disconnected\n", __FUNCTION__);
      return false;
   }

   if (!msg) {
      Log("%s: Invalid message.\n", __FUNCTION__);
      return false;
   }

   if (!CreateContext(&context.mHandle)) {
      Log("%s: Failed to create context object.\n", __FUNCTION__);
      return false;
   }

   CommonSvcCommand cmd = {{0}};
   CommonSvcCommand_Build(&cmd, id, 0, cookie ? cookie : "", msg, msgLen);
   /*
    * That flag will prevent vdp service to send ACK response for every
    * message and it should improve vdp service performance.
    */
   util::Variant value(TRUE);
   context.SetOps(VDP_RPC_CHANNEL_CONTEXT_OPT_POST, &value);

   if (!CommonSvcCommand_Encode(context.mHandle, &cmd)) {
      Log("%s: Failed to encode context object.\n", __FUNCTION__);
      goto exit;
   }

#if !defined(RDESVC_REDACT_LOG) // defined currently for release build type
   Log("%s: Sending message: len=%d.\n", __FUNCTION__, msgLen);
#endif

   if (!Invoke(context.mHandle, &invokeId)) {
      Log("%s: Failed to invoke context object.\n", __FUNCTION__);
      goto exit;
   }

   success = true;

exit:
   if (!success) {
      DestroyContext(context.mHandle);
      context.mHandle = NULL;
   }

   CommonSvcCommand_Clear(&cmd);
   return success;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CommonSvc::OnInvoked --
 *
 *    Receives message from the server and forward it
 *    through vdp service interface to remote mks.
 *
 * Return value:
 *    None.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

void
CommonSvc::OnInvoked(void *contextHandle) // IN
{
   util::ChannelContextHandle context(contextHandle);
   CommonSvcCommand cmd = {{0}};

   if (!CommonSvcCommand_Decode(contextHandle, &cmd)) {
      Log("%s: Failed to decode context object.\n", __FUNCTION__);
      return;
   }

   uint32 command = context.GetCommand();
   switch (command) {
   case DPI_SYNC_MSG: {
      VDP_RPC_BLOB *blob = &(cmd.params[0].blobVal);
      if (!blob || !blob->blobData || blob->size < sizeof(DpiSyncCommand)) {
         Log("%s: Invalid dpi version message from the client.\n", __FUNCTION__);
         CommonSvcCommand_Clear(&cmd);
         return;
      }

      ProcessDpiSyncCommand(reinterpret_cast<DpiSyncCommand *>(blob->blobData));
   } break;
   case CERTSSO_UNLOCK_MSG: {
      VDP_RPC_BLOB *blob = &(cmd.params[0].blobVal);
      if (!blob || !blob->blobData || blob->size < sizeof(CertSSOCommand)) {
         Log("%s: Invalid CertSSO Unlock message from the guest.\n", __FUNCTION__);
         CommonSvcCommand_Clear(&cmd);
         return;
      }

      ProcessCertSSOUnlockCommand(reinterpret_cast<CertSSOCommand *>(blob->blobData));
   } break;
   case RDSAADAUTH_MSG: {
      bool isValid = false;
      if (cmd.params[0].vt == VDP_RPC_VT_BLOB) {
         VDP_RPC_BLOB *blob = &(cmd.params[0].blobVal);
         if (blob && blob->blobData) {
            mRdsAadAuthClient.ProcessAuthCommand((unsigned char *)blob->blobData, blob->size);
            isValid = true;
         }
      }
      if (!isValid) {
         Log("%s: Invalid RDSAADAUTH message from the view agent.\n", __FUNCTION__);
      }
   } break;

#if defined(__linux__) || defined(_WIN32)
   case BATTERY_STATE_MSG: {
      VDP_RPC_BLOB *blob = &(cmd.params[0].blobVal);
      if (!blob || !blob->blobData || blob->size < sizeof(BatteryStateCommand)) {
         Log("%s: Invalid bat sync version message from the client.\n", __FUNCTION__);
         CommonSvcCommand_Clear(&cmd);
         return;
      }
      BatteryStateCommand *command = reinterpret_cast<BatteryStateCommand *>(blob->blobData);
      if (command) {
         switch (command->commandType) {
         case BAT_STAT_CMD_VERSION:
            if (batStateVersionSig) {
               batStateVersionSig(command->data.version);
            }
            break;
         default:
            Log("%s: Unhandled Battery state command %d.\n", __FUNCTION__, command->commandType);
            break;
         }
      }
   } break;
#endif
   case DISPLAY_MSG: {
      VDP_RPC_BLOB *blob = &(cmd.params[0].blobVal);
      if (!blob || !blob->blobData) {
         Log("%s: Invalid display message from the client.\n", __FUNCTION__);
         CommonSvcCommand_Clear(&cmd);
         return;
      }
      ProcessDisplayCommand(reinterpret_cast<DisplayCommand *>(blob->blobData));
   } break;
#if defined(__APPLE__) || defined(_WIN32)
   case FEATURE_ENABLEMENT_MSG: {
      VDP_RPC_BLOB *blob = &(cmd.params[0].blobVal);
      if (!blob || !blob->blobData || blob->size < sizeof(FeatureEnablementCommand)) {
         Log("%s: Received invalid feature enablement msg from server.\n", __FUNCTION__);
         CommonSvcCommand_Clear(&cmd);
         return;
      }
      FeatureEnablementCommand *command =
         reinterpret_cast<FeatureEnablementCommand *>(blob->blobData);
      switch (command->commandType) {
      case FEATURE_ENABLEMENT_CMD_STATUS:
         Log("%s: feature enablement status %llu\n", __FUNCTION__,
             (long long unsigned int)command->data.status);
         mFeatureEnableStatus = command->data.status;
         if (!(mFeatureEnableStatus & FEATURE_OPTION_MASK)) {
            ProcessFeatureEnablementCommand();
         } else {
            Log("%s: wait for feature option\n", __FUNCTION__);
         }
         break;
      default:
         Log("%s: Unhandled feature enablement command %d.\n", __FUNCTION__, command->commandType);
         break;
      }
   } break;
   case FEATURE_OPTION_MSG: {
      Log("%s: received feature option msg.\n", __FUNCTION__);
      mFeatureOptions = ParseFeatureOptions(&context);
      ProcessFeatureEnablementCommand();
   } break;
#endif
   case NETWORK_STATE_GPO_MSG: {
      Log("%s: received network state msg.\n", __FUNCTION__);
      VDP_RPC_BLOB *blob = &(cmd.params[0].blobVal);
      if (!blob || !blob->blobData || blob->size < sizeof(NetworkStateGPOCommand)) {
         Log("%s: Received invalid network state msg from server.\n", __FUNCTION__);
         CommonSvcCommand_Clear(&cmd);
         return;
      }
      NetworkStateGPOCommand *command = reinterpret_cast<NetworkStateGPOCommand *>(blob->blobData);

      switch (command->commandType) {
      case NETWORK_STATE_GPO_CMD_RESPOND_ENABLE_DISPLAY:
         Log("%s: network state enable display is %d\n", __FUNCTION__,
             (int)command->data.enableDisplay);
         ProcessNetworkStateEnableDisplayCommand(command->data.enableDisplay);
         break;
      case NETWORK_STATE_GPO_CMD_RESPOND_INTERVAL:
         Log("%s: network state interval is %u\n", __FUNCTION__, (uint32)command->data.interval);
         ProcessNetworkStateIntervalCommand(command->data.interval);
         break;
      default:
         Log("%s: Unhandled network state command %d.\n", __FUNCTION__, command->commandType);
         break;
      }

      break;
   }
   default:
      Log("%s: Unknown command %d.\n", __FUNCTION__, command);
      break;
   }

   CommonSvcCommand_Clear(&cmd);
}


/*
 *-----------------------------------------------------------------------------
 *
 * CommonSvc::OnVdpClientSendMsg --
 *
 *    Called from VDP service when a message has to be sent to rde server.
 *
 * Return value:
 *    TRUE if successful or FALSE otherwise.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

Bool
CommonSvc::OnVdpClientSendMsg(void *data,         // IN
                              const char *cookie, // IN
                              const char *msg,    // IN
                              int32 msgLen)       // IN
{
   ASSERT(data);
   CommonSvc *pCommonSvc = (CommonSvc *)data;

   return pCommonSvc->SendMsg(CLIENT_COMMON_PLUGIN_MSG, cookie, msg, msgLen);
}


/*
 *-----------------------------------------------------------------------------
 *
 * CommonSvc::OnClientGenericMsg --
 *
 *    Called from mks for generic client message via vdpservice notification.
 *
 * Return value:
 *    TRUE if successful or FALSE otherwise.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

Bool
CommonSvc::OnClientGenericMsg(void *pluginContext,     // IN
                              const char *sourceToken, // IN
                              const void *cookie,      // IN
                              const void *data)        // IN
{
   CommonSvc *pCommonSvc = (CommonSvc *)pluginContext;
   return pCommonSvc->ProcessClientGenericMsg(sourceToken, cookie, data);
}


/*
 *-----------------------------------------------------------------------------
 *
 *  CommonSvc::OnObjectStateChanged --
 *
 *    Notification handler when object state is changed.
 *
 * Return value:
 *    None.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

void
CommonSvc::OnObjectStateChanged()
{
   VDPRPC_ObjectState state = GetObjectState();
   switch (state) {
   case VDP_RPC_OBJ_CONNECTED:
      Log("%s: vdp channel connected.\n", __FUNCTION__);
#if defined(__APPLE__) || defined(_WIN32)
      SendClientFeatureEnablementCapacity();
#endif
      RequestNetworkStateEnableDisplayGPO();
      RequestNetworkStateIntervalGPO();
      break;

   case VDP_RPC_OBJ_DISCONNECTED:
      Log("%s: vdp channel disconnected.\n", __FUNCTION__);
      break;

   default:
      break;
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * CommonSvc::ProcessDpiSyncCommand --
 *
 *    Notification handler for DPI Sync messages received from View Agent.
 *
 * Return value:
 *    None.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

void
CommonSvc::ProcessDpiSyncCommand(DpiSyncCommand *command) // IN:
{
   mDpiSyncClient.ProcessDpiSyncCommand(command);
}


/*
 *-----------------------------------------------------------------------------
 *
 * CommonSvc::ProcessDisplayCommand --
 *
 *    Notification handler for Display messages received from View Agent.
 *
 * Return value:
 *    None.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

void
CommonSvc::ProcessDisplayCommand(DisplayCommand *command) // IN:
{
   mDpiSyncClient.ProcessDisplayCommand(command);
}


#if TARGET_OS_IPHONE
/*
 *-----------------------------------------------------------------------------
 *
 *  CommonSvc::SendTabletModeToAgent --
 *
 *    Sends the new tablet mode value to View Agent.
 *
 * Return value:
 *    None.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

void
CommonSvc::SendTabletModeToAgent(uint32 tabletMode) // IN:
{
   Log("%s: send tablet mode %u to agent\n", __FUNCTION__, tabletMode);
   TabletModeCommand command = {TABLET_MODE_COMMAND_SET, {tabletMode}};
   bool sendResult =
      SendMsg(TABLET_MODE_MSG, NULL, reinterpret_cast<const char *>(&command), sizeof(command));
   if (!sendResult) {
      Log("%s: Failed to send tablet mode information\n", __FUNCTION__);
   }
}
#endif


/*
 *-----------------------------------------------------------------------------
 *
 *  CommonSvc::ProcessCertSSOUnlockCommand --
 *
 *    Processes CertSSO related commands from the guest.
 *
 * Return value:
 *    None.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

void
CommonSvc::ProcessCertSSOUnlockCommand(CertSSOCommand *command) // IN
{
   switch (command->commandType) {
   case CERTSSO_UNLOCK_COMMAND: {
      const char *srcSessionGuid = (const char *)&(command->data.sessionInfo.sessionGUID[0]);
      const char *srcTicketGuid = (const char *)&(command->data.sessionInfo.ticketGUID[0]);

      Log("%s: CertSSO Unlock session = %s, ticket = %s\n", __FUNCTION__, srcSessionGuid,
          srcTicketGuid);

      if (IsValidRdeCommonClient(mVdpRdeCommonClientId)) {
         VDPRdeCommonClient_Interface &vdpRdeCommonClient = GetRdeCommonClientInterface();
         if (vdpRdeCommonClient.version < VDPRDECOMMON_INTERFACE_V2) {
            Log("%s: VdpRdeCommonClient version: %u, does not support CertSSO Unlock\n",
                __FUNCTION__, vdpRdeCommonClient.version);
            break;
         }

         if (!vdpRdeCommonClient.v2.RdeCommonClient_CertSSOUnlock(mVdpRdeCommonClientId,
                                                                  srcSessionGuid, srcTicketGuid)) {
            Log("%s: Failed to send CertSSO Unlock request to VdpClient.\n", __FUNCTION__);
         }
      } else {
         Log("%s: Invalid VdpRdeCommonClientId.\n", __FUNCTION__);
      }
   } break;

   default:
      Log("%s: Unknown command type %u.\n", __FUNCTION__, command->commandType);
      break;
   }
}


#if defined(__linux__) || defined(_WIN32)
/*
 *-----------------------------------------------------------------------------
 *
 *  CommonSvc::SetBatStateVersionCallback --
 *
 *    Set callback for battery state redir agent plugins version.
 *
 * Return value:
 *    None.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

void
CommonSvc::SetBatStateVersionCallback(std::function<void(uint32)> callback) // IN
{
   batStateVersionSig = callback;
}


/*
 *-----------------------------------------------------------------------------
 *
 *  CommonSvc::SendClientBatStatVersion --
 *
 *    Send battery state redir client plugin version.
 *
 * Return value:
 *    None.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

void
CommonSvc::SendClientBatStatVersion(uint32 version) // IN
{
   BatteryStateCommand command = {BAT_STAT_CMD_VERSION};
   command.data.version = version;
   bool result =
      SendMsg(BATTERY_STATE_MSG, NULL, reinterpret_cast<const char *>(&command), sizeof(command));
   if (!result) {
      Log("%s: Failed to send version information\n", __FUNCTION__);
   } else {
      Log("%s: Sent Client Battery version = %u\n", __FUNCTION__, command.data.version);
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 *  CommonSvc::SendClientBetStatInfo --
 *
 *    Send battery state.
 *
 * Return value:
 *    None.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

void
CommonSvc::SendClientBatStatInfo(bool acConnected,          // IN
                                 uint32 batteryLifePercent) // IN
{
   BatteryStateCommand command = {BAT_STAT_CMD_STAT};
   command.data.stat.isACConnected = acConnected;
   command.data.stat.batteryLifePercent = batteryLifePercent;
   bool result =
      SendMsg(BATTERY_STATE_MSG, NULL, reinterpret_cast<const char *>(&command), sizeof(command));
   if (!result) {
      Log("%s: Failed to send battery information\n", __FUNCTION__);
   } else {
      Log("%s: Sent Client Battery information. IsAcConnected %d "
          "betteryLifePercent %d\n",
          __FUNCTION__, command.data.stat.isACConnected, command.data.stat.batteryLifePercent);
   }
}
#endif


/*
 *-----------------------------------------------------------------------------
 *
 *  CommonSvc::ProcessClientGenericMsg --
 *
 *    Notification handler for generic client message.
 *
 * Return value:
 *    None.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

Bool
CommonSvc::ProcessClientGenericMsg(const char *sourceToken, // IN
                                   const void *cookie,      // IN
                                   const void *data)        // IN
{
   const RdeChannelMessage *message = reinterpret_cast<const RdeChannelMessage *>(data);
   uint32 msgType = message->msgType;
   switch (RDE_GET_CHANNEL_MSG_TYPE(msgType)) {
   case RDE_CHANNEL_DISPLAY_MSG:
      return mDpiSyncClient.ProcessRdeCommonDisplayMsg(sourceToken, cookie, data);
   case RDE_CHANNEL_RDSAADAUTH_MSG:
      return mRdsAadAuthClient.ProcessRdeCommonAuthMsg(sourceToken, cookie, data);
   default:
      break;
   }
   return FALSE;
}


/*
 *-----------------------------------------------------------------------------
 *
 *  CommonSvc::NotifyRdeCommonVDPObserver --
 *
 *    send Notification to VDPService.
 *
 * Return value:
 *    TRUE if successful, FALSE otherwise.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

Bool
CommonSvc::NotifyRdeCommonVDPObserver(const char *cookie, // IN
                                      const void *msg)    // IN
{
   return GetObserverInterface().v1.Broadcast(RDE_COMMON_GENERIC_NOTIFICATION, cookie, msg);
}


#if defined(__APPLE__) || defined(_WIN32)
/*
 *-----------------------------------------------------------------------------
 *
 *  CommonSvc::SendClientFeatureEnablementCapacity --
 *
 *    Send the list of features that are supported on the client.
 *
 * Return value:
 *    None.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

void
CommonSvc::SendClientFeatureEnablementCapacity() // IN
{
   FeatureEnablementCommand command = {FEATURE_ENABLEMENT_CMD_CAPACITY};
   command.data.capacity = mFeatureEnablementCapacity;
   bool result = SendMsg(FEATURE_ENABLEMENT_MSG, NULL, reinterpret_cast<const char *>(&command),
                         sizeof(command));
   Log("%s: Sent client feature capacity = %llu %s\n", __FUNCTION__,
       (long long unsigned int)command.data.capacity, result ? "SUCCESS" : "FAIL");
}


/*
 *-----------------------------------------------------------------------------
 *
 *  CommonSvc::ProcessFeatureEnablementCommand --
 *
 *    Notification handler for feature messages received from View Agent.
 *
 * Return value:
 *    None.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

void
CommonSvc::ProcessFeatureEnablementCommand()
{
   std::map<std::string, std::vector<uint8>> options;
   if (mFeatureEnablementCapacity & BLOCK_SCREEN_CAPTURE_MASK) {
      if (mFeatureOptions.contains(ALLOW_SCREEN_RECORDING_OPTION_KEY)) {
         options[ALLOW_SCREEN_RECORDING_OPTION_KEY] =
            mFeatureOptions[ALLOW_SCREEN_RECORDING_OPTION_KEY];
      }
      NotifyFeatureEnablement(mFeatureEnableStatus, BLOCK_SCREEN_CAPTURE_MASK, options,
                              RDE_CHANNEL_APP_PROTECTION_MSG,
                              RDE_CHANNEL_BLOCK_SCREEN_CAPTURE_ENABLE_MSG);
   }
   if (mFeatureEnablementCapacity & BLOCK_KEY_LOGGER_MASK) {
      if (mFeatureOptions.contains(ALLOW_ARM_NO_ANTIKEYLOGGER_OPTION_KEY)) {
         options[ALLOW_ARM_NO_ANTIKEYLOGGER_OPTION_KEY] =
            mFeatureOptions[ALLOW_ARM_NO_ANTIKEYLOGGER_OPTION_KEY];
      }
      NotifyFeatureEnablement(mFeatureEnableStatus, BLOCK_KEY_LOGGER_MASK, options,
                              RDE_CHANNEL_APP_PROTECTION_MSG,
                              RDE_CHANNEL_BLOCK_KEY_LOGGER_ENABLE_MSG);
   }
   if (mFeatureEnablementCapacity & BLOCK_SEND_INPUT_MASK) {
      NotifyFeatureEnablement(mFeatureEnableStatus, BLOCK_SEND_INPUT_MASK, options,
                              RDE_CHANNEL_APP_PROTECTION_MSG,
                              RDE_CHANNEL_BLOCK_SEND_INPUT_ENABLE_MSG);
   }
   if (mFeatureEnablementCapacity & BLOCK_THUMBNAIL_REPRESENTATION_MASK) {
      NotifyFeatureEnablement(mFeatureEnableStatus, BLOCK_THUMBNAIL_REPRESENTATION_MASK, options,
                              RDE_CHANNEL_APP_PROTECTION_MSG,
                              RDE_CHANNEL_BLOCK_THUMBNAIL_REPRESENTATION_ENABLE_MSG);
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 *  CommonSvc::NotifyFeatureEnablement --
 *
 *    Notification handler for specific feature enablement messages with
 *    option from View Agent.
 *
 * Return value:
 *    None.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

void
CommonSvc::NotifyFeatureEnablement(uint64 status,                                     // IN
                                   uint64 featureMask,                                // IN
                                   std::map<std::string, std::vector<uint8>> options, // IN
                                   RdeChannelMessageType channelType,                 // IN
                                   int msgType)                                       // IN
{
   std::vector<uint8> optionBuffer;
   for (auto option : options) {
      uint32 keySize = option.first.size();
      optionBuffer.insert(optionBuffer.end(), (uint8 *)&keySize,
                          (uint8 *)&keySize + sizeof keySize);
      optionBuffer.insert(optionBuffer.end(), (uint8 *)option.first.c_str(),
                          (uint8 *)option.first.c_str() + keySize);
      uint32 valSize = option.second.size();
      optionBuffer.insert(optionBuffer.end(), (uint8 *)&valSize,
                          (uint8 *)&valSize + sizeof valSize);
      optionBuffer.insert(optionBuffer.end(), (uint8 *)option.second.data(),
                          (uint8 *)option.second.data() + valSize);
   }

   uint8 featureEnableStatus = !!(status & featureMask);
   RdeChannelMessage channelMsg{};
   channelMsg.msgSize = sizeof channelMsg + optionBuffer.size();
   channelMsg.msgType = RDE_SET_CHANNEL_MSG_TYPE(channelType, msgType);

   std::vector<uint8> buffer;
   buffer.insert(buffer.end(), (uint8 *)&channelMsg,
                 (uint8 *)&channelMsg + sizeof channelMsg - sizeof(uint8));
   buffer.insert(buffer.end(), (uint8 *)&featureEnableStatus,
                 (uint8 *)&featureEnableStatus + sizeof featureEnableStatus);
   buffer.insert(buffer.end(), optionBuffer.begin(), optionBuffer.end());

   bool res = NotifyRdeCommonVDPObserver(nullptr, buffer.data());
   Log("%s: notify UI for feature mask %llu %s\n", __FUNCTION__,
       (long long unsigned int)featureMask, res ? "SUCCESS" : "FAILED");
}


/*
 *-----------------------------------------------------------------------------
 *
 *  CommonSvc::ParseFeatureOptions --
 *
 *    Parse feature options by the info from agent.
 *
 * Return value:
 *    Feature options.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

std::map<std::string, std::vector<uint8>>
CommonSvc::ParseFeatureOptions(util::ChannelContextHandle *context) // IN
{
   std::map<std::string, std::vector<uint8>> result;
   if (context == NULL) {
      return result;
   }
   for (uint32 i = 0; i + 1 < context->GetParamCount(); i += 2) {
      util::Variant key;
      util::Variant val;
      context->GetParam(i, &key);
      context->GetParam(i + 1, &val);
      if (key.vt == VDP_RPC_VT_LPSTR) {
         std::string optionKey = key.strVal;
         std::vector<uint8> optionVal;
         switch (val.vt) {
         case VDP_RPC_VT_I1: {
            Log("%s: get char option\n", __FUNCTION__);
            optionVal.resize(sizeof(char));
            memcpy(optionVal.data(), &(val.cVal), sizeof(char));
            break;
         }
         case VDP_RPC_VT_I2: {
            Log("%s: get short option\n", __FUNCTION__);
            optionVal.resize(sizeof(short));
            memcpy(optionVal.data(), &(val.iVal), sizeof(short));
            break;
         }
         case VDP_RPC_VT_UI2: {
            Log("%s: get unsigned short option\n", __FUNCTION__);
            optionVal.resize(sizeof(unsigned short));
            memcpy(optionVal.data(), &(val.uiVal), sizeof(unsigned short));
            break;
         }
         case VDP_RPC_VT_I4: {
            Log("%s: get int32 option\n", __FUNCTION__);
            optionVal.resize(sizeof(int32));
            memcpy(optionVal.data(), &(val.lVal), sizeof(int32));
            break;
         }
         case VDP_RPC_VT_UI4: {
            Log("%s: get uint32 option\n", __FUNCTION__);
            optionVal.resize(sizeof(uint32));
            memcpy(optionVal.data(), &(val.ulVal), sizeof(uint32));
            break;
         }
         case VDP_RPC_VT_I8: {
            Log("%s: get int64 option\n", __FUNCTION__);
            optionVal.resize(sizeof(int64));
            memcpy(optionVal.data(), &(val.llVal), sizeof(int64));
            break;
         }
         case VDP_RPC_VT_R4: {
            Log("%s: get float option\n", __FUNCTION__);
            optionVal.resize(sizeof(float));
            memcpy(optionVal.data(), &(val.fVal), sizeof(float));
            break;
         }
         case VDP_RPC_VT_R8: {
            Log("%s: get double option\n", __FUNCTION__);
            optionVal.resize(sizeof(double));
            memcpy(optionVal.data(), &(val.dVal), sizeof(double));
            break;
         }
         case VDP_RPC_VT_LPSTR: {
            Log("%s: get str option\n", __FUNCTION__);
            optionVal.resize(strlen(val.strVal));
            memcpy(optionVal.data(), val.strVal, strlen(val.strVal));
            break;
         }
         case VDP_RPC_VT_BLOB: {
            Log("%s: get blob option\n", __FUNCTION__);
            optionVal.resize(val.blobVal.size);
            memcpy(optionVal.data(), val.blobVal.blobData, val.blobVal.size);
            break;
         }
         default: {
            Log("%s, unknow type %d\n", __FUNCTION__, val.vt);
         }
         }
         result[optionKey] = optionVal;
         Log("%s: add key %s.\n", __FUNCTION__, optionKey.c_str());
      }
   }
   return result;
}

#endif


/*
 *-----------------------------------------------------------------------------
 *
 *  CommonSvc::RequestNetworkStateEnableDisplayGPO --
 *
 *    Sends network state enable display policy request to the server side.
 *
 * Return value:
 *    None.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

void
CommonSvc::RequestNetworkStateEnableDisplayGPO()
{
   NetworkStateGPOCommand command = {NETWORK_STATE_GPO_CMD_REQUEST_ENABLE_DISPLAY};
   command.data.enableDisplay = 0;
   bool result = SendMsg(NETWORK_STATE_GPO_MSG, NULL, reinterpret_cast<const char *>(&command),
                         sizeof(command));
   Log("%s: Sent network state enable display state command = %d %s\n", __FUNCTION__,
       command.data.enableDisplay, result ? "SUCCESS" : "FAIL");
}


/*
 *-----------------------------------------------------------------------------
 *
 *  CommonSvc::RequestNetworkStateIntervalGPO --
 *
 *    Sends network state time interval policy request to the server side.
 *
 * Return value:
 *    None.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

void
CommonSvc::RequestNetworkStateIntervalGPO()
{
   NetworkStateGPOCommand command = {NETWORK_STATE_GPO_CMD_REQUEST_INTERVAL};
   command.data.interval = 0;
   bool result = SendMsg(NETWORK_STATE_GPO_MSG, NULL, reinterpret_cast<const char *>(&command),
                         sizeof(command));
   Log("%s: Sent network state interval state command = %u %s\n", __FUNCTION__,
       command.data.interval, result ? "SUCCESS" : "FAIL");
}


/*
 *-----------------------------------------------------------------------------
 *
 *  CommonSvc::ProcessNetworkStateEnableDisplayCommand --
 *
 *    Processes network state enable display policy command.
 *
 * Return value:
 *    None.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

void
CommonSvc::ProcessNetworkStateEnableDisplayCommand(bool enableDisplay) // IN
{
   const int msgHeaderSize = sizeof(RdeChannelMessage) - sizeof(uint8);
   RdeChannelMessage channelMsg{};
   channelMsg.msgSize = msgHeaderSize + sizeof(bool);
   channelMsg.msgType = RDE_SET_CHANNEL_MSG_TYPE(RDE_CHANNEL_NETWORK_STATE_SETTING_MSG,
                                                 RDE_CHANNEL_NETWORK_STATE_ENABLE_DISPLAY_MSG);
   bool *pValue = (bool *)channelMsg.payload;
   *pValue = enableDisplay;
   Log("%s: enableDisplay is %d, *pValue is %d \n", __FUNCTION__, enableDisplay, *pValue);
   if (!NotifyRdeCommonVDPObserver(NULL, &channelMsg)) {
      Log("%s: Failed to send display info to rmks.\n", __FUNCTION__);
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 *  CommonSvc::ProcessNetworkStateIntervalCommand --
 *
 *    Processes network state interval policy command.
 *
 * Return value:
 *    None.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

void
CommonSvc::ProcessNetworkStateIntervalCommand(uint32 interval) // IN
{
   const int msgHeaderSize = sizeof(RdeChannelMessage) - sizeof(uint8);
   int mallocSize = msgHeaderSize + sizeof(uint32);
   RdeChannelMessage *pRdeChannelMsg = (RdeChannelMessage *)malloc(mallocSize);
   if (pRdeChannelMsg == nullptr) {
      Log("%s: malloc size %d fail\n", __FUNCTION__, mallocSize);
      return;
   }

   pRdeChannelMsg->msgSize = mallocSize;
   pRdeChannelMsg->msgType = RDE_SET_CHANNEL_MSG_TYPE(RDE_CHANNEL_NETWORK_STATE_SETTING_MSG,
                                                      RDE_CHANNEL_NETWORK_STATE_INTERVAL_MSG);
   uint32 *pValue = reinterpret_cast<uint32 *>(pRdeChannelMsg->payload);
   *pValue = interval;
   Log("%s: interval is %u, *pValue is %u \n", __FUNCTION__, interval, *pValue);
   if (!NotifyRdeCommonVDPObserver(NULL, pRdeChannelMsg)) {
      Log("%s: Failed to send display info to rmks.\n", __FUNCTION__);
   }
   free(pRdeChannelMsg);
}