/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

package com.omnissa.vdi.agent.messageserver;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.security.KeyPair;
import java.security.PrivateKey;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.jms.Destination;
import javax.jms.ExceptionListener;
import javax.jms.JMSException;
import javax.jms.Message;
import javax.jms.Session;
import javax.jms.TopicConnection;
import javax.jms.TopicConnectionFactory;
import javax.jms.TopicSession;

import com.omnissa.vdi.logger.Logger;
import org.apache.commons.lang3.StringUtils;

import com.swiftmq.jms.SwiftMQConnectionFactory;
import com.omnissa.vdi.agent.messageserver.JmsMetrics.JmsState;
import com.omnissa.vdi.commonutils.Thumbprint;
import com.omnissa.vdi.messagesecurity.Identity;
import com.omnissa.vdi.messagesecurity.MessageSecurityEncryptionKey;
import com.omnissa.vdi.messagesecurity.MessageSecurityException;
import com.omnissa.vdi.messagesecurity.WrappedMessageSecurityHandler;
import com.omnissa.vdi.messagesecurity.swiftmq.BrokerUpdateUtility;
import com.omnissa.vdi.ssl.CertificateManager;
import com.omnissa.vdi.ssl.CertificateManagerException;
import com.omnissa.vdi.ssl.CertificateManagerUnavailableException;

/**
 * There are 3 types of connection to the JMS that the agent requires: A
 * subscriber, on which commands are accepted A publisher, on which responses to
 * commands are sent A publisher, on which events are sent to the desktop
 * controller.
 *
 * Previous incarnations of this code would end up with 3 separate TCP/IP
 * connections. Now we're standardising on SwiftMQ, we optimize this into having
 * just one "connection" to JMS and it is the JMS Manager that has this. To
 * multithread the JMS, a different session is required for each of the 3 uses
 * above. We have explicit retrieval mechanisms for each of the 3 types (as that
 * makes the client coding easier when dealing with failovers).
 *
 * The properties support object is used to read the list of connection brokers
 * and property files are no longer used at all in the Agent on Windows.
 *
 * December 2008 - Scalability enhancements. SwiftMQ supports a feature called
 * hierarchical topics. This allows: Dynamic registration of topics that don't
 * need to be in the config file Topics of the form X.Y.Z Subscribers to the
 * topic X.Y.Z will receive messages sent to X, X.Y, and X.Y.Z Sends are
 * synchronized on the root topic (i.e. X above) So the new structure will be to
 * use the following topics: PoolDn.ServerDn sessiontopic Alongside the existing
 * WtsTopic. Selectors will no longer be set apart from on WtsTopic where they
 * will remain for backwards compatibility. Moving the system to using a
 * callback approach to message delivery should allow the majority of the code
 * to remain the same. desktopcontrol is used as before, and also will support
 * having occasional "update" messages sent to allow the trackers to remove the
 * requirement to poll.
 *
 * <AUTHOR>
 *
 */
public class JmsManager implements ExceptionListener, JmsConnectionManager {
    private static final Logger log = Logger.getLogger(JmsManager.class);

    /**
     * Connected flag - modified by synchronised methods
     */
    private boolean connected = false;

    private boolean reconnecting = false;

    /**
     * Active connection broker
     */
    private String activeRouter;

    /**
     * Current topic connection
     */
    private TopicConnection topicConnection;

    private final String id;

    private final List<JmsConnectionUser> connectionUsers;

    private final ThreadPoolManager threadPoolMgr;

    private JmsManager(String id, Main mainObject,
            ThreadPoolManager threadPoolMgr) {
        this.mainObject = mainObject;
        this.id = id != null ? id : "agent";
        connectionUsers = new ArrayList<>();
        this.threadPoolMgr = threadPoolMgr;
    }

    /**
     * Back reference to main for notifications
     */
    Main mainObject;

    private static final int STANDARD_JMS_PORT = 4001;

    private static final int SECURE_JMS_PORT = 4002;

    /**
     * Retrieve the singleton instance of the JMS Manager
     *
     * @param id The thread id to use in logging statements
     * @param mainObject The main object from Main
     * @param threadPoolMgr The manager of the thread pool
     * @return the JMS Manager
     */
    public static JmsManager createInstance(String id, Main mainObject,
            ThreadPoolManager threadPoolMgr) {
        JmsManager manager = new JmsManager(id, mainObject, threadPoolMgr);
        return manager;
    }

    /**
     * Release the main object to stop these being circular
     */
    public void releaseMain() {
        mainObject = null;
    }

    /**
     * Used by some of the poll loops to check if they should exit
     *
     * @return connected
     */
    public boolean isConnected() {
        return connected;
    }

    public boolean isReconnecting() {
        return reconnecting;
    }

    /**
     * Set the connection status, returned by isConnected(). As a catch-all
     * solution to bug #384700, we now track our connection attempts within Main
     * so that we can restart the process if we appear to be stuck.
     *
     * @param value
     *            The connected state
     */
    public void setConnected(boolean value) {
        connected = value;
        mainObject.changeConnectedStatus(value);
    }

    /**
     * Get the list of brokers to connect to.
     *
     * @return A randomized list of servers, possibly empty.
     *
     */
    protected List<String> getBrokerList() {
        List<String> brokers = new ArrayList<>(getConfig().getBrokers());
        Collections.shuffle(brokers);
        return brokers;
    }


    // PoC only: This won't be needed outside of PoC, just using it to trigger
    // broker into sending secondary key
    private synchronized boolean requestGoldenImageKey() {
        AgentJmsConfig config = getConfig();

        log.debug("PoC only: Sending CHANGEID to request golden image key...");

        // Send CHANGEID with REQUEST_KEY set to true
        try {
            TopicSession session = createTopicSession(false,
                    Session.AUTO_ACKNOWLEDGE);

            final long DEFAULT_CHANGEID_MESSAGE_TTL_IN_MILLSECONDS = TimeUnit.SECONDS
                    .toMillis(10);
            final long DEFAULT_CHANGEID_RESPONSE_TIMEOUT_IN_MILLSECONDS = TimeUnit.SECONDS
                    .toMillis(15);

            BrokerUpdateUtility updater = new BrokerUpdateUtility(session,
                    "desktopcontrol", "CHANGEID", "CHANGEIDREPLY",
                    getAgentMessageSecurityHandler(),
                    config.getBrokers().size(), // Don't need response from all
                    // brokers? Just 1 will do?
                    DEFAULT_CHANGEID_RESPONSE_TIMEOUT_IN_MILLSECONDS,
                    DEFAULT_CHANGEID_MESSAGE_TTL_IN_MILLSECONDS);

            updater.addParameterToSend("SERVERDN", config.getServerDn());
            updater.addParameterToSend("SERVERPOOLDN",
                    config.getServerPoolDn());
            updater.addParameterToSend("ACTIVEBROKER", activeRouter);

            // This is the value that the broker uses to know this is a request
            // for the golden image key for PoC only
            updater.addParameterToSend("REQUEST_KEY", "true");

            updater.updateOverJms(new BrokerUpdateUtility.LoopChecker() {
                @Override
                public boolean isRunning() {
                    return !mainObject.areWeStopping();
                }
            });

            if (updater.okResponseReceived()) {
                log.info(
                        "PoC only: Received response to CHANGEID key request message");

                return true;
            }
        } catch (Exception e) {
            log.debug(
                    "PoC only: Unable to trigger golden image key via JMS server "
                    + activeRouter, e);
        }
        return false;
    }

    /**
     * Make connection to broker to enable identity change. Based on doConnect()
     * but much simplified as it only needs to make basic connection on
     * unsecured port.
     *
     * @throws IllegalStateException
     *             when not in valid state to connect
     */
    private synchronized void doChangeIdentityConnect()
            throws IllegalStateException {

        if (mainObject == null || mainObject.areWeStopping()) {
            throw new IllegalStateException("stopping");
        }

        // We don't want to report as connected while changing identity
        setConnected(false);

        while (true) {

            List<String> brokerList = getBrokerList();

            for (String broker : brokerList) {
                activeRouter = broker;
                log.debug("doChangeIdentityConnect: Using connection broker {}",
                        activeRouter);

                try {
                    Map<String, String> props = createJmsPropertiesMap(
                            activeRouter, false, false, false);

                    TopicConnectionFactory topicConnectionFactory = (TopicConnectionFactory) SwiftMQConnectionFactory
                            .create(props);
                    topicConnection = topicConnectionFactory
                            .createTopicConnection();
                    topicConnection.setExceptionListener(this);
                    topicConnection.start();

                    mainObject.setJmsState(
                            JmsMetrics.JmsState.JMS_STATE_REQUEST_IDENTITY);
                    return;

                } catch (InterruptedException e) {
                    if (log.isDebugEnabled()) {
                        log.debug(
                                "doChangeIdentityConnect: Interrupted while connecting to JMS server {}",
                                activeRouter, e);
                    }
                } catch (Exception e) {
                    logConnectException(e);
                }

                if (mainObject == null || mainObject.areWeStopping()) {
                    // don't continue through the broker list
                    break;
                }
            }

            if (mainObject == null || mainObject.areWeStopping()) {
                throw new IllegalStateException("stopping");
            }

            try {
                Thread.sleep(15000);
            } catch (InterruptedException e) {
                if (brokerList.isEmpty()) {
                    throw new RuntimeException("No brokers defined");
                }
                break;
            }
        }
    }

    private void logConnectException(Exception e) {
        /*
         * Under debugging situations this may be useful, but in production, all
         * we want to do is keep trying to connect to a broker to get service
         * resumed without generating tons of logging.
         */
        if (log.isDebugEnabled()) {
            if (e instanceof UnknownHostException) {
                log.debug("Unable to resolve hostname for " + activeRouter);
            } else if (e instanceof JMSException) {
                JMSException je = (JMSException) e;
                if (je.getLinkedException() != null) {
                    log.debug("Unable to connect to JMS server " + activeRouter,
                            je.getLinkedException());
                } else {
                    log.debug("Unable to connect to JMS server " + activeRouter,
                            e);
                }
            } else {
                log.debug("Unable to connect to JMS server " + activeRouter, e);
            }
        }
    }

    /**
     * Change the identity of the agent. Requests a new identity from the broker
     * using the golden image key to sign the request. Only change identity if
     * the MAC address has changed (indicating we have been cloned). Check for
     * MAC change is done in confirmMachineIdentity and if decide need to change
     * then requireNewIdentity flag is set.
     *
     * @return true if identity changed.
     */
    private synchronized boolean changeIdentity() {
        AgentJmsConfig config = getConfig();

        // Check for other reasons we can't change identity
        if (config.isManaged()) {
            // Only unmanaged agents can change identity
            log.debug("Managed agent, can't change identity");
            return false;
        }

        if (config.isDaaSAgent()) {
            // Not supported on DaaS
            log.debug("DaaS agent, can't change identity");
            return false;
        }

        /*
         * Is there a golden image key or a RePairing key that we can use?
         */
        KeyPair signingKeyPair = config.getGoldenImageKeypair();
        if (signingKeyPair == null) {
            log.warn("No signing key available so can't change identity");
            return false;
        }

        log.debug("Changing identity");

        // Retrieve private key for signing the change ID message.
        PrivateKey signer = null;
        signer = signingKeyPair.getPrivate();
        if (signer == null) {
            log.error("Invalid golden image or RPs private key");
            return false;
        }

        // Connect over unsecure port. Keep trying until successful.
        doChangeIdentityConnect();
        while (mainObject
                .getJmsState() != JmsState.JMS_STATE_REQUEST_IDENTITY) {
            log.debug("Unable to connect to change identity, keep trying");
            try {
                Thread.sleep(15000);
            } catch (InterruptedException e) {
                log.debug("Interrupted");
                throw new IllegalStateException("interrupted");
            }
            doChangeIdentityConnect();
        }

        // Send CHANGEID
        try {
            AgentMessageSecurityHandler handler = new AgentMessageSecurityHandler(
                    false);
            handler.configure(config.getMsMode(), config.getIdentity(), signer,
                    config.getBrokerPublicKey(), null, config);

            TopicSession session = createTopicSession(false,
                    Session.AUTO_ACKNOWLEDGE);
            WrappedMessageSecurityHandler wrappedHandler = new WrappedMessageSecurityHandler(
                    handler);

            final long DEFAULT_CHANGEID_MESSAGE_TTL_IN_MILLSECONDS = TimeUnit.SECONDS
                    .toMillis(10);
            final long DEFAULT_CHANGEID_RESPONSE_TIMEOUT_IN_MILLSECONDS = TimeUnit.SECONDS
                    .toMillis(15);

            BrokerUpdateUtility updater = new BrokerUpdateUtility(session,
                    "desktopcontrol", "CHANGEID", "CHANGEIDREPLY",
                    wrappedHandler, config.getBrokers().size(),
                    DEFAULT_CHANGEID_RESPONSE_TIMEOUT_IN_MILLSECONDS,
                    DEFAULT_CHANGEID_MESSAGE_TTL_IN_MILLSECONDS);

            updater.addParameterToSend("SERVERDN", config.getServerDn());
            updater.addParameterToSend("SERVERPOOLDN",
                    config.getServerPoolDn());
            updater.addParameterToSend("ACTIVEBROKER", activeRouter);
            updater.addParameterToSend("FQDN", config.getServerDnsName());

            updater.addParameterToReceive("AGENT_IDENTITY");
            updater.addParameterToReceive("AGENT_PUBLIC_KEY");
            updater.addParameterToReceive("AGENT_PRIVATE_KEY");
            updater.addParameterToReceive("AGENT_SERVER_DN");
            updater.addParameterToReceive("AGENT_SERVER_POOL_DN");
            updater.addParameterToReceive("BROKERS");

            updater.updateOverJms(new BrokerUpdateUtility.LoopChecker() {
                @Override
                public boolean isRunning() {
                    return !mainObject.areWeStopping();
                }
            });

            if (!updater.okResponseReceived()) {
                log.debug("Unable to get successful response to CHANGEID");
                return false;
            }

            log.trace("Processing response to CHANGEID message");

            String agentIdentity = updater
                    .getReceivedParameter("AGENT_IDENTITY");
            String agentPublicKey = updater
                    .getReceivedParameter("AGENT_PUBLIC_KEY");
            String agentPrivateKey = updater
                    .getReceivedParameter("AGENT_PRIVATE_KEY");
            String agentServerDn = updater
                    .getReceivedParameter("AGENT_SERVER_DN");
            String agentServerPoolDn = updater
                    .getReceivedParameter("AGENT_SERVER_POOL_DN");
            String brokers = updater
                    .getReceivedParameter("BROKERS");

            log.debug("agentIdentity is {}", agentIdentity);
            log.debug("agentPublicKey is {}", agentPublicKey);
            log.debug("agentServerDn is {}", agentServerDn);
            log.debug("agentServerPoolDn is {}", agentServerPoolDn);
            log.debug("specified broker(s) are {}", brokers);

            if (StringUtils.isBlank(agentIdentity)
                    || StringUtils.isBlank(agentPublicKey)
                    || StringUtils.isBlank(agentPrivateKey)
                    || StringUtils.isBlank(agentServerDn)
                    || StringUtils.isBlank(agentServerPoolDn)
                    || StringUtils.isBlank(brokers)) {
                log.error("Invalid identity change data, ignoring");
                return false;
            }

            // Store new identity
            config.setNewIdentity(agentIdentity, agentServerDn,
                    agentServerPoolDn, agentPublicKey,
                    agentPrivateKey);

            // Save the brokers list to registry
            config.setBrokers(brokers);

            log.info("Successfully changed agent identity to {}",
                    agentIdentity);

            // Tear down connection used to change identity
            topicConnection.stop();
            topicConnection.setExceptionListener(null);
            topicConnection.close();
            topicConnection = null;

            // Reinitialise configuration using new identity
            config.reinit();

            return true;

        } catch (MessageSecurityException e) {
            log.debug("Exception trying to configure handler", e);
        } catch (Exception e) {
            log.debug(
                    "Unable to change identity with JMS server " + activeRouter,
                    e);
        }
        return false;
    }

    /**
     * Connects to the router. After this call returns, it is reasonable to
     * expect that all the relevant member variables will contain the right
     * thing. We need to guard against some of the threads doing a teardown
     * whilst another thread is trying to connect. So, we count the number of
     * connects, and need to go through the same number of teardowns before we
     * attempt a fresh connect. As we presume that there will always be 3
     * connect attempts, using a cyclic barrier to synchronize all 3 before
     * attempting a connect, and then synchronizing the internal connect
     * function as one would normally
     *
     * @throws IllegalStateException
     *             If the service is stopping
     */
    public synchronized void connect() throws IllegalStateException {

        doConnect();

        // This is only for PoC!!
        // Reuse CHANGEID message to send request for the broker to push down
        // the golden image pairing keys. This will eventually be done purely
        // from the broker side but as we don't have that at the moment we need
        // a way of getting the broker to create and send the keys to use
        // when changing ID.
        // To use this trigger, manually set triggerGoldenImage to true in
        // registry and restart the agent.
        AgentJmsConfig config = getConfig();
        if (config.triggerGoldenImage()) {
            log.debug("PoC only: Sending CHANGEID trigger to broker");
            requestGoldenImageKey();
            config.setTriggerGoldenImage(false);
        }
    }

    private synchronized void doConnect() throws IllegalStateException {

        AgentJmsConfig config = getConfig();

        /*
         * Change identity if required (unmanaged agent in 3rd party
         * provisioning platform e.g. AWS designated as golden image)
         */
        if (config.requireNewIdentity()) {
            changeIdentity();
        }

        /*
         * The first one through should flip this to true so the waiters can
         * just return as the connection will be valid.
         */
        if (isConnected()) {
            return;
        }

        if (mainObject == null || mainObject.areWeStopping()) {
            throw new IllegalStateException("stopping");
        }

        /*
         * Check if the RePairing key request flag is set in the registry,
         * there is no existing RePairing keypair and the machine is not
         * paired.
         * This flag is set by the installer when machine gets registered as
         * a golden image via hzaprep.exe, and once pairing is done then the
         * installer deletes this flag.
         * Generally only the flag value should be checked, but checking for key
         * also is useful as there may be a rare scenario in whick this keypair
         * might already exist e.g. Installation with GOLDEN_IMAGE_INSTALL=1
         * was started, machine got registered as a golden image, flag was
         * created and set to 1, RePairing keypair got generated here but for
         * some reason pairing didn't finish and installer rolled back.Now
         * if the installation is tried again with GOLDEN_IMAGE_INSTALL=1,
         * the keypair will be present and shoudln't be recreated.
         * Also check if the machine is paired, in the case of an agent upgrade
         * it is likely that the machine is already paired, it is not expected
         * that the flag will be set in that case, but it is still safer to
         * check for pairing. In paired state a new keypair shouldn't be
         * created because broker will never become aware of it and this will
         * impact clone's re-pairing.
         */
        KeyPair rePairKeyPair;
        if (config.shouldCreateRePairingKeyPair()
            && config.getRePairingKey() == null && !config.isPaired()) {
            log.debug("Creating RPs keypair.");
            rePairKeyPair = config.createRePairingKeyPair();
        }

        /*
         * If we fail to connect on the JMS bus we will try the notify the
         * broker via the backdoor
         *
         * Flag used to track whether the guestinfo is set or not
         */
        GuestInfoErrorState errorState = null;

        /*
         * bug 1359795, a failure to connect during provisioning/customisation
         * will result in the VM being marked as being in an error state
         * permanently. It's possible on badly configured infrastructure for the
         * initial connect to fail so the flag below is used to prevent error
         * reporting the first time an error occurs
         */
        boolean reportErrorState = false;
        boolean warningLogged = false;
        boolean jmsThumbprintExchangeRequired = false;

        String resolvedAddress = "";

        while (true) {

            /*
             * Keep setting connect state the loop so the kill timer doesn't
             * expire (see bug 384700)
             */
            setConnected(false);

            List<String> brokerList = getBrokerList();

            for (String broker : brokerList) {
                resolvedAddress = "";

                activeRouter = broker;
                log.debug("Using connection broker " + activeRouter);
                setConnected(false);

                try {
                    /*
                     * Lookup the broker directly to detect DNS problems, and
                     * also to report the resolved IP. This will throw an
                     * UnknownHostException if the address can't be resolved.
                     */

                    InetAddress addresses[] = InetAddress
                            .getAllByName(activeRouter);

                    /*
                     * use agent identity as username
                     */
                    Identity identity = config.getIdentity();

                    /*
                     * create SwiftMQ SSL Certificate
                     */
                    List<Thumbprint> tps = createCertificateAndGetThumbprints(identity, config.isDaaSAgent());

                    /*
                     * Set agent thumbprint in registry for DaaS.
                     */
                    if (config.isDaaSAgent()) {
                        storeAgentThumbprints(tps);
                    }

                    resolvedAddress = addresses[0].toString();

                    Map<String, String> props = createJmsPropertiesMap(
                            activeRouter, false, config.isDaaSAgent(),
                            config.isDaaSLegacyPairingMode());

                    TopicConnectionFactory topicConnectionFactory =
                            (TopicConnectionFactory) SwiftMQConnectionFactory
                                    .create(props);

                    if (!(config.isDaaSAgent()
                            && config.isDaaSLegacyPairingMode())) {

                        /*
                         * For backwards compatibility with older brokers, use
                         * default anonymous account for initial connection which
                         * will be used for pairing.
                         */

                        topicConnection = topicConnectionFactory
                                .createTopicConnection();

                        topicConnection.setExceptionListener(this);
                        topicConnection.start();

                        mainObject.setJmsState(
                                JmsMetrics.JmsState.JMS_STATE_PAIRING);

                        /*
                         * May need to securely pair before configuring sessions
                         */
                        AgentMessageSecurityHandler handler = mainObject
                                .getAgentMessageSecurityHandler();
                        config.pairOverJms(mainObject, topicConnection, tps,
                                jmsThumbprintExchangeRequired, activeRouter);
                        config.configureMessageSecurity(handler);

                        mainObject.setJmsState(
                                JmsMetrics.JmsState.JMS_STATE_CONNECTING);

                        // Tear down anonymous connection
                        topicConnection.stop();
                        topicConnection.setExceptionListener(null);
                        topicConnection.close();
                        topicConnection = null;

                        /*
                         * determine if we are connecting to a broker which support
                         * SSL connections by checking for the presence of the
                         * broker's SSL certificate thumbprint. If it's there, we
                         * need to reconnect on the SSL port
                         */
                        String thumbprint =
                                config.getBrokerSSLCertificateThumbprint();
                        if (thumbprint != null && !thumbprint.trim()
                                .isEmpty()) {
                            log.debug("Re-connecting using secure port {}",
                                    SECURE_JMS_PORT);
                            props = createJmsPropertiesMap(activeRouter, true,
                                    config.isDaaSAgent(),
                                    config.isDaaSLegacyPairingMode());
                        } else {
                            log.debug("Re-connecting using standard port {}",
                                    STANDARD_JMS_PORT);
                        }
                        topicConnectionFactory =
                                (TopicConnectionFactory) SwiftMQConnectionFactory
                                        .create(props);

                        log.debug("JMS identity is {}", identity);
                        topicConnection = topicConnectionFactory
                                .createTopicConnection(identity.toString(),
                                        null);
                        topicConnection.setExceptionListener(this);
                        topicConnection.start();
                    } else {
                        // Need to pair before creating first connection...
                        mainObject.setJmsState(JmsMetrics.JmsState.JMS_STATE_PAIRING);

                        config.pairOverJms(mainObject, topicConnection, tps,
                                jmsThumbprintExchangeRequired, activeRouter);

                        mainObject.setJmsState(JmsMetrics.JmsState.JMS_STATE_CONNECTING);

                        /*
                         * Username required for non-anonymous connections on
                         * secure port. FileBasedSSLSocketFactory uses the
                         * username to lookup the correct key store for VLS and
                         * Linux standalone agent.
                         */
                        topicConnection = topicConnectionFactory
                                .createTopicConnection(identity.toString(), null);

                        topicConnection.setExceptionListener(this);
                        topicConnection.start();

                        if (log.isDebugEnabled()) {
                            log.debug("using port " + SECURE_JMS_PORT + " for DaaS");
                        }
                    }

                    setupUsers();

                    setConnected(true);
                    if (config.isManaged()) {
                        GuestInfoErrorState.setErrorState("OK",
                                GuestInfoErrorState.AGENT_NO_ERROR,
                                activeRouter, resolvedAddress);
                    }
                    return;

                } catch (InterruptedException e) {
                    if (log.isDebugEnabled()) {
                        log.debug("Interrupted while connecting to JMS server "
                                + activeRouter, e);
                    }
                    tearDown(true);
                } catch (Exception e) {
                    logConnectException(e);

                    /*
                     * It isn't clear this is the right thing to do, as we may
                     * want to report which brokers we can't get to. However, we
                     * don't want lots of VMs setting guestinfo every 15 seconds
                     * if a failure occurs
                     */
                    if (errorState == null && reportErrorState) {
                        errorState = new GuestInfoErrorState(e.toString(),
                                GuestInfoErrorState.AGENT_ERROR_NOT_CONNECTED,
                                activeRouter, resolvedAddress);
                    }

                    tearDown(true);
                }

                if (mainObject == null || mainObject.areWeStopping()) {
                    // don't continue through the broker list
                    break;
                }
            }

            if (mainObject == null || mainObject.areWeStopping()) {
                throw new IllegalStateException("stopping");
            }

            /*
             * We get here if there are no brokers defined, or connections to
             * all brokers in the list failed. Sleep before trying the list
             * again.
             */
            if (!brokerList.isEmpty() && !warningLogged) {
                log.warn("Unable to connect to any listed host."
                        + " The agent will continue to retry: " + brokerList);
                if (errorState != null && reportErrorState) {
                    errorState.publish();
                    warningLogged = true;
                } else if (!reportErrorState) {
                    log.debug("Will not report initial error");
                    reportErrorState = true;
                }
            }

            /*
             * one reason why we failed to connect could be that the JMS SSL
             * certificate thumbprints are no longer in sync. To ensure that
             * they are, on the next pass ensure pairing is performed again with
             * the current private key which will also perform the thumbprint
             * exchange.
             */
            if (!brokerList.isEmpty() && jmsThumbprintExchangeRequired == false) {
                log.debug("Will attempt JMS Thumbprint exchange");
                jmsThumbprintExchangeRequired = true;
            }

            try {
                Thread.sleep(15000);
            } catch (InterruptedException e) {
                if (brokerList.isEmpty()) {
                    throw new RuntimeException("No brokers defined");
                }
                break;
            }
        }
    }

    private void storeAgentThumbprints(List<Thumbprint> tps) {
        String tpReg = null;
        for (Thumbprint tp : tps) {
            if (tpReg == null) {
                tpReg = tp.toString();
            } else {
                tpReg +=  ";" + tp.toString();
            }
        }
        if (tpReg != null) {
            getConfig().setAgentSSLCertificateThumbprint(tpReg);
        }
    }

    /**
     * creates a certificate if one doesn't exist and returns the thumbprints
     *
     * @param identity The identity to look up
     * @param isDaasAgent Whether this is a DaaS agent
     * @return A list of thumbprints
     * @throws Exception If an error occurred with the certificates
     */
    private List<Thumbprint> createCertificateAndGetThumbprints(
            Identity identity, boolean isDaasAgent) throws Exception {

        int certificateGenerationAttemptsRemaining = 10;

        CertificateManager cm = getConfig().getCertificateManager();

        while (true) {
            if (mainObject == null || mainObject.areWeStopping()) {
                throw new IllegalStateException("stopping");
            }

            try {
                return cm.getCertificateThumbprints(identity.toString(), true);
            } catch (CertificateManagerUnavailableException e) {
                if (certificateGenerationAttemptsRemaining > 0) {
                    certificateGenerationAttemptsRemaining--;
                    log.error("Failed to create Certificate: " + e.getMessage());
                    if (log.isDebugEnabled()) {
                        log.debug(
                                "Failed to create Certificate" + e.getMessage()
                                        + ". Sleeping before next attempt", e);
                    }

                    try {
                        Thread.sleep(30000);
                    } catch (InterruptedException ie) {
                        log.debug("Interrupted");
                    }

                } else {
                    log.error("Failed to create Certificate" + e.getMessage());
                    if (log.isDebugEnabled()) {
                        log.debug(
                                "Failed to create Certificate" + e.getMessage(),
                                e);
                    }
                    throw new Exception("Failed to create Certificate", e);
                }
            } catch (CertificateManagerException e) {
                log.error("Failed to create Certificate" + e.getMessage());
                if (log.isDebugEnabled()) {
                    log.debug("Failed to create Certificate" + e.getMessage(),
                            e);
                }
                throw new Exception("Failed to create Certificate", e);
            }
        }
    }

    private void initUsers() {
        if (!connectionUsers.isEmpty()) {
            return;
        }

        connectionUsers.add(new EventPublishingManager(this));
        if (getConfig().isDaaSAgent()) {
            connectionUsers.add(new DaasMessageManager(this, mainObject.getMessageHandler()));
            connectionUsers.add(new DaasMessageResponder(this));
            connectionUsers.add(new DaasPublishingManager(this));
            if (getConfig().isDaaSLegacyPairingMode()) {
                connectionUsers.add(new DaasAgentConfigMonitor(this));
            }
        } else {
            connectionUsers.add(new TopicMessageManager(this, mainObject.getMessageHandler()));
            connectionUsers.add(new TopicMessageResponder(this));
            connectionUsers.add(new DesktopControlPublishingManager(this));
            /*
             * UBI-424: Commenting this out to prevent the creation of the long-dated Agent
             * certificate to address a timing issue. If both the regular and long-dated
             * certificates are generated within the same second, then after pairing
             * whenever the agent restarts the long-dated one is picked up for connecting
             * to the SSL port and the broker rejects it since it only knows the
             * regular-dated certificate.
             * TODO: The proper fix (tracked in UBI-489) is to generate the long-dated
             * certificate on the agent only when the current certificate is within the
             * configured refresh window. For more details please refer to
             * https://omnissa.atlassian.net/browse/UBI-489.
             */
            //connectionUsers.add(new AgentCertificateGeneration(this));
        }

        connectionUsers.add(new TimingProfilerPublishingManager(this));
        connectionUsers.add(new AsyncSessionReporter(this, mainObject.getReportGenerator()));
        connectionUsers.add(new CmsPublishingManager(this));
    }

    private void setupUsers() throws JMSException {
        initUsers();

        for (JmsConnectionUser user : connectionUsers) {
            user.setup();
        }
        for (JmsConnectionUser user : connectionUsers) {
            user.setReady(true);
        }
    }

    private void teardownUsers() throws IllegalStateException {
        for (JmsConnectionUser user : connectionUsers) {
            if (user.isReady()) {
                user.setReady(false);
            }
        }
        for (JmsConnectionUser user : connectionUsers) {
            try {
                user.teardown();
            } catch (JMSException e) {
                log.error("Exception while tearing down connection user: "
                        + e.getMessage());
                log.debug("Exception during teardown: " + e, e);
            }
        }
    }

    private void finishUsers() throws IllegalStateException {
        for (JmsConnectionUser user : connectionUsers) {
            try {
                user.finished();
            } catch (JMSException e) {
                log.error("Exception while finishing connection user: "
                        + e.getMessage());
                log.debug("Exception during finished: " + e, e);
            }
        }
    }
    
    public static Map<String, String> createJmsPropertiesMap(String broker,
            boolean useSecurePort, boolean isDaaSAgent, boolean isDaasLegacyPairingMode) {
        Map<String, String> props = new HashMap<String, String>();
        if (useSecurePort || isDaasLegacyPairingMode) {
            if (Main.isSimulated()) {
                props.put(SwiftMQConnectionFactory.SOCKETFACTORY,
                        "com.omnissa.vdi.messagesecurity.swiftmq.FileBasedSSLSocketFactory");
            } else if (isDaaSAgent) {
                props.put(SwiftMQConnectionFactory.SOCKETFACTORY,
                        "com.omnissa.vdi.agent.messageserver.DaasSSLSocketFactory");
            } else {
                props.put(SwiftMQConnectionFactory.SOCKETFACTORY,
                        "com.omnissa.vdi.agent.messageserver.AgentSSLSocketFactory");
            }
            props.put(SwiftMQConnectionFactory.PORT, "" + SECURE_JMS_PORT);
        } else {
            props.put(SwiftMQConnectionFactory.SOCKETFACTORY,
                    "com.swiftmq.net.PlainSocketFactory");
            props.put(SwiftMQConnectionFactory.PORT, "" + STANDARD_JMS_PORT);
        }
        props.put(SwiftMQConnectionFactory.HOSTNAME, broker);
        props.put(SwiftMQConnectionFactory.KEEPALIVEINTERVAL, "20000");
        props.put(SwiftMQConnectionFactory.JMS_TTL, "1800000");
        // SwiftMQ scalability recommendations
        props.put(SwiftMQConnectionFactory.SMQP_PRODUCER_REPLY_INTERVAL, "2");
        props.put(SwiftMQConnectionFactory.SMQP_CONSUMER_CACHE_SIZE, "2");
        props.put(SwiftMQConnectionFactory.INPUT_BUFFER_SIZE, "4192");
        props.put(SwiftMQConnectionFactory.OUTPUT_BUFFER_SIZE, "4192");
        return props;
    }

    @Override
    public void onException(JMSException arg0) {
        if (log.isDebugEnabled()) {
            log.debug("Exception in JmsManager-" + getId(), arg0);
        }
        needReconnect();
    }

    public synchronized void tearDown(boolean ignoreConnectedState) {
        if (ignoreConnectedState || isConnected()) {

            // Interrupt all threads- this is implemented for items
            // waiting on a non JMS lock

            setConnected(false);

            teardownUsers();

            try {
                if (topicConnection != null) {
                    topicConnection.stop();
                }
            } catch (Exception E) {
                log.trace(E);
            }

            try {
                if (topicConnection != null) {
                    topicConnection.setExceptionListener(null);
                }
            } catch (Exception E) {
                log.trace(E);
            }

            try {
                if (topicConnection != null) {
                    topicConnection.close();
                    topicConnection = null;
                }
            } catch (Exception E) {
                log.trace(E);
            }
        }
    }

    public synchronized void finished() {
        finishUsers();
    }

    @Override
    public TopicSession createTopicSession(boolean transacted,
            int acknowledgeMode) throws JMSException {
        return topicConnection.createTopicSession(transacted, acknowledgeMode);
    }

    @Override
    public Map<String, Destination> getDestinationMap() {
        return mainObject.destinationMap;
    }

    @Override
    public Map<String, MessageSecurityEncryptionKey> getEncryptionMap() {
        return mainObject.encryptionMap;
    }

    @Override
    public synchronized void needReconnect() {
        log.debug("Reconnect request signalled");

        if (!isConnected()) {
            log.debug("Reconnect request ignored, connection not yet complete");
            return;
        }
        if (reconnecting) {
            log.debug("Reconnect request ignored, already in progress");
            return;
        }
        reconnecting = true;

        Thread connectThread = new Thread("ReconnectTask-" + getId()) {
            @Override
            public void run() {
                log.debug("Reconnect firing");
                try {
                    tearDown(false);
                    if (mainObject != null && !mainObject.areWeStopping()) {
                        connect();
                    }
                } finally {
                    reconnecting = false;
                }
            }
        };
        connectThread.start();
    }

    @Override
    public String getActiveRouter() {
        return activeRouter;
    }

    @Override
    public ThreadPoolManager getThreadPoolManager() {
        return threadPoolMgr;
    }

    @Override
    public AgentMessageSecurityHandler getAgentMessageSecurityHandler() {
        return mainObject.getAgentMessageSecurityHandler();
    }

    @Override
    public AgentJmsConfig getConfig() {
        return mainObject.agentConfig;
    }

    @Override
    public String getId() {
        return id;
    }

    @Override
    public void addHeadersToOutgoingMessage(Message message) throws JMSException {
        mainObject.addAgentIdentityToMessage(message);
        mainObject.addCapabilitiesToMessage(message);
        mainObject.addSeqNoToMessage(message);
        mainObject.addActiveConnectionBrokerToMessage(message);
    }

    /*
     * TODO : The below is a temporary accessor until further cleanup work can
     * be done.
     */

    @Override
    @SuppressWarnings("unchecked")
    public <T> T getJmsConnectionUser(Class<T> clazz) {
        for (JmsConnectionUser user : connectionUsers) {
            if (user.getClass() == clazz) {
                return (T) user;
            }
        }
        return null;
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T> List<T> getJmsConnectionUsers(Class<T> baseClass) {
        List<T> users = new ArrayList<>();
        for (JmsConnectionUser user : connectionUsers) {
            if (baseClass.isAssignableFrom(user.getClass())) {
                users.add((T) user);
            }
        }
        return users;
    }
}
