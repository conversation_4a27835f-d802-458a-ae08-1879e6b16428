# Copyright (c) Omnissa, LLC. All rights reserved.
# This product is protected by copyright and intellectual property laws in the
# United States and other countries as well as by international treaties.
# -- Omnissa Restricted

import os
import sys

# Access peer file scons_bootstrap.py.
sys.path.append(os.path.dirname(__file__))
from scons_bootstrap import ConanSConsBootstrap


class ConanAppblastlibs(ConanSConsBootstrap):
    settings = "os", "arch", "build_type", "compiler"

    def requirements(self):
        super().requirements()

        # Append required packages instead of inserting them, unless you know what you do
        isLinux = self.settings.os == "Linux"
        isWindows = self.settings.os == "Windows"
        isArmv8 = self.settings.arch == "armv8"
        isArmv7hf = self.settings.arch == "armv7hf"
        isx86 = self.settings.arch == "x86"
        isx86_64 = self.settings.arch == "x86_64"
        isDynamic = self.settings.compiler.get_safe("runtime") == "dynamic"

        self.requires("zlib/1.3.1", options={"shared": True})
        self.requires("amf_sdk/1.4.30")
        self.requires("fast_float/3.4.0")
        self.requires("fmt/10.2.1")
        self.requires("jsoncpp/1.9.5")
        self.requires("nlohmann_json/3.11.2")
        self.requires("onevpl/2023.1.0")
        self.requires("abseil/20240116.2")
        self.requires("protobuf/3.25.3")
        self.requires("protobuf-c/1.5.0")
        self.requires("opus/1.4")
        self.requires("libxml2/2.13.8", options={"shared": True})
        self.requires("openssl/3.0.16")
        self.requires("openssl_fips_validated/3.0.9")

        if isWindows:
            if isx86 or isx86_64:
                self.requires("rescle/1.0.11", options={"shared": False})
            self.requires("uriparser/0.9.8")
            if self.settings.arch != 'arm64ec':
                self.requires("boost/1.86.0")

        if isLinux:
            self.requires("glibc/2.17")

        if self.settings.arch != 'arm64ec':
            self.requires("libcurl/8.10.0", options={"shared": True})

        isx86_64 = self.settings.arch == "x86_64"
        isDynamic = self.settings.compiler.get_safe("runtime") == "dynamic"
        if isx86_64:
            self.requires("libx264/164.20220124", options={"shared": True})
            if isDynamic:
                self.requires("log4cxx/0.11.0", options={"shared" : True})
        if isDynamic or isLinux:
            self.requires("libx265/3.4", options={"shared" : True})
        if isx86_64 and isLinux:
            self.requires("keyutils/1.5.5")
            self.requires("xorg-proto/2023.2")
            self.requires("libx11/1.8.7", options={"shared" : True})
            self.requires("libxkbfile/1.1.3", options={"shared" : True})
            self.requires("libxi/1.8.1", options={"shared" : True})
            self.requires("libxcomposite/0.4.6", options={"shared" : True})
            self.requires("libxscrnsaver/1.2.4", options={"shared" : True})
            self.requires("libxdmcp/1.1.4", options={"shared" : True})
            self.requires("libxau/1.0.11", options={"shared" : True})
            self.requires("libxcb/1.16", options={"shared" : True})
            self.requires("libxdamage/1.1.6", options={"shared" : True})
            self.requires("libxext/1.3.6", options={"shared" : True})
            self.requires("libxfixes/6.0.1", options={"shared" : True})
            self.requires("libxtst/1.2.4", options={"shared" : True})
            self.requires("libxcursor/1.2.2", options={"shared" : True})
            self.requires("libxinerama/1.1.5", options={"shared" : True})
            self.requires("libxrandr/1.5.4", options={"shared" : True})
            self.requires("libxrender/0.9.11", options={"shared" : True})
            self.requires("mesa/23.3.6", options={"shared" : True})
            self.requires("pulseaudio/17.0", options={"shared" : True})
            self.requires("boost/1.86.0")
            self.requires("libxcvt/0.1.2")

        self.requires("libgettext/0.22", options={"shared": True})
        self.requires("libsigcpp/3.6.0", options={"shared": True})
        self.requires("pcre2/10.42", options={"shared": True})
        if not isWindows or isDynamic:
            self.requires("glib/2.84.1", options={"shared": True})
            self.requires("ogg/1.3.2")
            self.requires("speexdsp/1.2rc3")
            # Need require gtest before libyuv.
            self.requires("gtest/1.17.0", options={"shared": True})
        self.requires("libjpeg-turbo/3.0.1")
        self.requires("libyuv/1882")
        self.requires("fastlz/0.1.0")
        if isWindows:
            self.requires("intel_mediasdk/22.5.4", options={"shared": True})
            self.requires("ffmpeg-windows/7.0.1", options={"with_mfx": True, "shared": True})
        else:
            self.requires("ffmpeg/7.0.1", options={"shared": True})
            if isLinux and isx86_64:
                self.requires("ffmpeg-vdpau/7.0.1", options={"with_vdpau": True, "shared": True})
        self.requires("libpng/1.6.48", options={"shared": True})
        self.requires("libiconv/1.17", options={"shared": True})
        self.requires("tomlplusplus/3.1.0")

        if (isWindows or isLinux) and not isArmv8:
            self.requires("nvidia_sdk_13.0/13.0")
            self.requires("nvidia_sdk_12.0/12.0")
            self.requires("nvidia_sdk_8.1/8.1")

        if isWindows:
            self.requires("wtl/10.0.10320")

    def build_requirements(self):
        super().build_requirements()

        self.tool_requires("7zip/24.09")
        self.tool_requires("doxygen/1.9.4")
        if self.settings.os == "Linux":
            self.tool_requires("coreutils/9.4")
            self.tool_requires("findutils/4.10.0")
        self.tool_requires("protobuf/3.25.3")
        self.tool_requires("protobuf-c/1.5.0")
        self.tool_requires("rust/1.83.0")
        if self.settings.os == "Windows":
            self.tool_requires("wix_toolset/3.11")
            self.tool_requires("vmware-mswdk-exts/1.0.14")
            self.tool_requires("squishcoco/7.1.0")
