/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

#include "hznReflectUtils.h"
#include "hznReflectEnvironment.h"
#include <getoptwin32.h>


void
InitializeLogger()
{
   LoggerPtr logger = Logger::Create("DefaultLogger", "hznReflectTest", true);
   LogWriterPtr writer = logger->CreateWriter(LOG_WRITER_FILE);
   if (!writer) {
      return;
   }
   logger->SetLevel(LOGGER_LOG_DEBUG);
   writer->SetBufferingEnabled(false);
   logger->Start();
}
/*
 * Google Test Main Method
 */

int
main(int argc, char *argv[])
{
   int opt;
   bool noDriver = false;
   uint32 sleepMs = 0;

   struct option long_options[] = {{"no-driver", no_argument, nullptr, 'n'},
                                   {"keep-reg", no_argument, nullptr, 'k'},
                                   {"sleep", required_argument, nullptr, 's'},
                                   {nullptr, 0, nullptr, 0}};

   CommandLineArgs args;

   while ((opt = getopt_long(argc, argv, "nks:", long_options, nullptr)) != -1) {
      switch (opt) {
      case 'n': {
         args.noDriver = true;
         break;
      }
      case 'k': {
         args.keepReg = true;
         break;
      }
      case 's': {
         args.sleepMs = atoi(optarg);
         break;
      }
      default:
         std::cerr << "Usage: " << argv[0] << " [--local] [--sleep <ms>]" << std::endl;
      }
   }

   ::InitGoogleTest(&argc, argv);
   ::InitGoogleMock(&argc, argv);
   InitializeLogger();
   ::AddGlobalTestEnvironment(new HznReflectEnvironment(&args));
   int exitCode = RUN_ALL_TESTS();
   Logger::ShutDown();
   return exitCode;
}