/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * cui/mks/mks.cc --
 *
 *     Implementation of the MKS class.
 */


#include <sstream>
#include <sigc++/sigc++.h>
#include <sigc++2to3.h>
#ifdef _WIN32
#   include <process.h>
#   include <winsock2.h>
#   include <ws2tcpip.h>
#else
#   include <netdb.h>
#   include <sys/types.h>
#   include <sys/socket.h>
#   include <arpa/inet.h>
#endif

#include "cui/core/slotutils.hh"
#include "cui/core/text.hh"
#include "cui/mks/hotKey.hh"
#include "cui/mks/mks.hh"
#include "cui/mks/mksScreenMgrMKSControl.hh"
#include "cui/mks/mksScreenView.hh"

#include "asyncsocket.h"
#include "keycodes.h"
#include "log.h"
#include "msg.h"
#include "posix.h"
#include "preference.h"
#include "productState.h"
#include "vm_assert.h"
#include "random.h"

#define LOGLEVEL_MODULE cui
#include "loglevel_user.h"

#define LOGPFX "CUIMKS: "


namespace cui {


#ifdef VMX86_DEVEL
// XXX: turn this way down for debugging so we'll see the connecting screen
#   define CONNECTING_NOTIFICATION_TIMEOUT_MS 250
#else
#   define CONNECTING_NOTIFICATION_TIMEOUT_MS 3250
#endif


static MKSControlLibGamingMouseMode GamingMouseModeToMKSControl(GamingMouseMode gamingMouseMode);

static ViewControlKeyBindModState MKSGetViewControlKeyBindModState(cui::Modifiers::KeyState state,
                                                                   const char *rightKeyName,
                                                                   const char *leftKeyName);


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::NewVNCMKS --
 *
 *      Constructs a new cui::MKS object suitable for connecting to a VNC
 *      server.
 *
 * Results:
 *      Returns the allocated cui::MKS object.
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

/* static */
MKS *
MKS::NewVNCMKS(const utf::string &remoteMKSPath,    // IN
               const StringVec &extraRemoteMKSArgs) // IN
{
   return new MKS(new mksctrl::MKSControlClientBase(), remoteMKSPath, extraRemoteMKSArgs);
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::MKS --
 *
 *      Constructor of a MKS.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

MKS::MKS(mksctrl::MKSControlClientBase *viewControl, // IN: VM's viewControl client.
                                                     //     Takes ownership.
         const utf::string &mksClientFilePath,       // IN/OPT: Path to the remoteMKS
                                                     //         executable.
         const StringVec &mksClientExtraArgs,        // IN/OPT: Extra args to remoteMKS
                                                     //         executable.
         MKSWindowMgr *windowMgr)                    // IN/OPT: MKSWindowMgr to handle
                                                     //         window events.
   :
   displayName(),
   detaching(),
   error(),
   canFullscreen("canFullscreen"),
   canGrab("canGrab"),
   canScreenshot("canScreenshot"),
   canSendCtrlAltDel("canSendCtrlAltDel"),
   allowKeyboardUngrab(true),
   grabOnKeyPress(false),
   grabOnMouseClick(false),
   grabOnMouseEnter(false),
   ungrabOnMouseLeave(false),
   ungrabIfObscured(false),
   hideCursorOnUngrab(false),
   gamingMouseMode(MOUSE_AUTOMATIC),
   hostShortcutsAreEnabled(false),
   allowLocalForRemote(false),
   httpsTunnel(0),
   mksKeys(),
   useDebugMKS(false),
   useIMEInput(false),
   syncGuestNumLock(false),
   syncGuestCapsLock(false),
   syncGuestScrollLock(false),
   blockScreenCaptureEnabled(false),
   allowScreenRecordingEnabled(false),
   allowArmNoAntiKeyloggerEnabled(false),
   blockKeyLoggerEnabled(false),
   blockSendInputEnabled(false),
   blockThumbnailRepresentationEnabled(false),
   networkWarningUIEnableDisplay(false),
   networkWarningUIShowingInterval(0),
   mMKSControlClient(viewControl),
   mksControlConnectionBroken(),
   mWindowGroupID(MKS_WINDOW_GROUP_ID_NONE),
   mScreenWindows(),
   mAttached(false),
   mMksControlPipeName(),
   mMksToken(),
   mMKSWindowMgr(),
   numLockEnabled(false),
   capsLockEnabled(false),
   scrollLockEnabled(false),
   cursorConstrained(false),
   mPending(false),
   mConnectingNotification(false),
   mCompleted(),
   mMaxGuestScreenWidth(640),
   mMaxGuestScreenHeight(480),
   mMaxGuestNumDisplays(1),
   mMaxBoundingBoxMemoryBytes(mMaxGuestScreenWidth * mMaxGuestScreenHeight * mMaxGuestNumDisplays *
                              4),
   mOwner(),
   mHookedKeys(),
   mksPrefsChanged(),
   mUngrabLocked(false),
   mVNCLastError(0),
   mMKSScreenMgr(),
   mMKSScreenView(),
   mParentPID(0),
   mMksClientFilePath(mksClientFilePath),
   mMksClientExtraArgs(mksClientExtraArgs),
   mIsMKSStalled(false),
   mWantToAttach(false),
   mRemoteConnSessionHost(""),
   mRemoteConnSessionVMSpecifer(""),
   mDisallowedThumbprintTypes(GetDisallowedThumbprintTypes())
{
   Log(LOGPFX "cui::MKS (%p)\n", this);

   ASSERT(mMKSControlClient.get() != nullptr);
   mMKSControlClient->connectionBroken.connect(
      sigc::mem_fun(this, &MKS::OnMKSControlConnectionBroken));
   mMKSControlClient->connectionInitialized.connect(mksInitialized.make_slot());
   mMKSControlClient->networkQualityStateUpdated.connect(networkQualityStateUpdated.make_slot());

   mOwner = Format("%x.%p", getpid(), this);

   mMKSScreenMgr.reset(new MKSScreenMgrMKSControl(viewControl));

   ASSERT(windowMgr != nullptr);

   mMKSWindowMgr.reset(windowMgr);

   mMKSWindowMgr->SetMKS(this);

   sigc::slot<void> grabState = grabStateChanged.make_slot();
   mMKSControlClient->grabState.changed.connect(grabState);
   grabState();

   sigc::slot<void> hotKeyPrefix = hotKeyPrefixChanged.make_slot();
   mMKSControlClient->hotKeyPrefix.changed.connect(hotKeyPrefix);
   hotKeyPrefix();

   /*
    * TODO: This is deprecated and we need to move to
    * keyboard/keyBindings/notifyUIEvent (keyBindingsNotifyUIEventChanged and
    * OnNotifyUIEvent).
    */
   mMKSControlClient->hookedKeyPressed.connect(sigc::mem_fun(this, &MKS::OnHookedKeyPressed));

   sigc::slot<void> attemptDnDUngrabSlot = attemptDnDUngrab.make_slot();
   mMKSControlClient->attemptDnDUngrab.connect(attemptDnDUngrabSlot);

   sigc::slot<void> dndUngrabSlot = onGHDnDUngrabChanged.make_slot();
   mMKSControlClient->GHDnDUngrab.changed.connect(dndUngrabSlot);

   mksPrefsChanged.connect(sigc::mem_fun(this, &MKS::ApplyPrefs));
   sigc::slot<void> mksPrefsSlot = mksPrefsChanged.make_slot();
   allowKeyboardUngrab.changed.connect(mksPrefsSlot);
   hostShortcutsAreEnabled.changed.connect(mksPrefsSlot);
   grabOnMouseClick.changed.connect(mksPrefsSlot);
   grabOnKeyPress.changed.connect(mksPrefsSlot);
   grabOnMouseEnter.changed.connect(mksPrefsSlot);
   ungrabOnMouseLeave.changed.connect(mksPrefsSlot);
   ungrabIfObscured.changed.connect(mksPrefsSlot);
   hideCursorOnUngrab.changed.connect(mksPrefsSlot);
   gamingMouseMode.changed.connect(mksPrefsSlot);
   syncGuestNumLock.changed.connect(mksPrefsSlot);
   syncGuestCapsLock.changed.connect(mksPrefsSlot);
   syncGuestScrollLock.changed.connect(mksPrefsSlot);

   mksKeys.changed.connect(sigc::mem_fun(this, &MKS::ApplyHotkeys));

   mWindowGroupID = mMKSWindowMgr->CreateMKSWindowGroup();

   ScreenWindow screenWindow;

   screenWindow.type = cui::ScreenWindow::MAIN_UI;
   screenWindow.hostMonitorRect = mMKSScreenMgr->GetVirtualBoundingBox();
   screenWindow.screen = mMKSScreenMgr->GetScreenWithID(cui::MKS_SCREEN_ID_NONE);

   mScreenWindows.push_back(screenWindow);

   canFullscreen.AddTest(sigc::mem_fun(this, &MKS::TestPresent), presentChanged,
                         "Is the MKS present");
   canGrab.AddTest(sigc::mem_fun(this, &MKS::TestPresent), presentChanged, "Is the MKS present");
   canScreenshot.AddTest(sigc::mem_fun(this, &MKS::TestPresent), presentChanged,
                         "Is the MKS present");
   canSendCtrlAltDel.AddTest(sigc::mem_fun(this, &MKS::TestPresent), presentChanged,
                             "Is the MKS present");

   canGrab.AddTest(sigc::mem_fun(this, &MKS::TestUngrabUnlocked), ungrabLockedChanged,
                   "Is ungrab unlocked");

   shouldUISendRawEvents.Bind(mMKSControlClient->shouldUISendRawEvents.changed,
                              mMKSControlClient->shouldUISendRawEvents.MakeGetterSlot());

   mMKSControlClient->vncServerErrorChanged.connect(
      sigc::mem_fun(this, &MKS::OnVNCServerErrorChanged));

   mMKSControlClient->ledStateChanged.connect(sigc::mem_fun(this, &MKS::OnKeyboardLEDStateChanged));

   mMKSControlClient->cursorConstrainedChanged.connect(
      sigc::mem_fun(this, &MKS::OnCursorConstrainedChanged));

   static_cast<MKSScreenMgrMKSControl *>(mMKSScreenMgr.get())->Init(this);
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::~MKS --
 *
 *      Destructor of a MKS.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

MKS::~MKS()
{
   Log(LOGPFX "Destroy cui::MKS (%p)\n", this);

   EmitDestroying();
   // We may or may not be in connecting process (mPending is true).
   // In either way, we just want to release the connection resource.

   // Cancel all the queued attach actions and ingore the pending action.
   mCompleted.clear();
   SetPending(false);
   Detach();

   mMKSWindowMgr->DestroyMKSWindowGroup(mWindowGroupID);

   Log(LOGPFX "Destroying cui::MKS (%p) is done.\n", this);
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::Detach --
 *
 *      Make sure a MKS is detached and will not automatically re-attach.
 *
 *      XXX This API must become asynchronous --hpreg.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::Detach()
{
   SetAttached(false);
   detaching.emit();
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::GetMKSControlClientBase --
 *
 * Results:
 *      Returns the owned MKSControlClientBase object of this MKS.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

mksctrl::MKSControlClientBase *
MKS::GetMKSControlClient() const
{
   return mMKSControlClient.get();
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::OnSetAttachedCompleted --
 *
 *      Completion handler (i.e. code that is common to both the success and
 *      error handlers) for the pending SetAttached() operation.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::OnSetAttachedCompleted()
{
   Log(LOGPFX "cui::MKS::OnSetAttachedCompleted (%p)\n", this);

   /* The pending SetAttached() operation is no longer pending. */
   SetPending(false);

   /*
    * Fire the next queued call to SetAttached(), if any.
    */
   if (mCompleted.size()) {
      sigc::slot<void> onCompleted = mCompleted.begin();
      mCompleted.pop_front();
      onCompleted();
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::OnSetAttachedCompletedError --
 *
 *      If any error is encountered in connecting process, this function is
 *      called to revert to the original state.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      The connecting process is complete.
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::OnSetAttachedCompletedError(void)
{
   OnSetAttachedCompletedDone();
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::OnSetAttachedCompletedDone --
 *
 *      After connecting or disconnecting succeeds, this function is called
 *      to do some initialization work.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      Connection / disconnection is done.
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::OnSetAttachedCompletedDone(void)
{
   mAttached ? OnMKSDisconnected() : OnMKSConnected();

   OnSetAttachedCompleted();
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::OnMKSConnected --
 *
 *      Called when the MKS is connected. Setting the correct state and
 *      initializing.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      emit presentChanged.
 *      emit capsChanged.
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::OnMKSConnected()
{
   ASSERT(!mAttached);

   /*
    * When the remote MKS connects to everything except a legacy IPC based
    * server (ESX 2.x), it supports seamless reconnection. For IPC based
    * servers, it will never be set and this callback will harmlessly never
    * be signalled.
    */

   sigc::slot<void> onCanReconnect = sigc::mem_fun(this, &MKS::OnCanReconnectChanged);

   if (mMKSControlClient->canReconnect == true) {
      onCanReconnect();
   } else {
      mMKSControlClient->remoteCanReconnect.connect(onCanReconnect);
   }

   /*
    * Register the callback after ViewControl has been attached.
    * This prevents calling the callback a second time for
    * an old event when reconnecting cui::MKS to a running VM.
    */

   mMKSControlClient->notifyUIUID.connect(keyBindingsNotifyUIEventChanged.make_slot());

   mAttached = true;
   presentChanged.emit();
   capsChanged.emit();

   ApplyHotkeys();
   ApplyHookedKeys();

   /*
    * viewControl values init
    */
   ApplyPrefs();
   SetUngrabLocked(false);
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::OnMKSDisconnected --
 *
 *      Called when the MKS is disconnected. Setting the correct state and
 *      uninitializing.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      emit presentChanged.
 *      emit capsChanged.
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::OnMKSDisconnected()
{
   ASSERT(mAttached);
   mAttached = false;

   presentChanged.emit();
   capsChanged.emit();
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::OnSetAttachedDoneMKSControl --
 *
 *      Called when viewControl connection is succeeded.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::OnSetAttachedDoneMKSControl(DoneSlot onDone) // IN
{
   ASSERT(!mAttached);
   Log_Verbose(LOGPFX "cui::MKS::OnSetAttachedDoneMKSControl (%p): %s\n", this,
               mAttached ? "detached" : "attached");

   OnSetAttachedCompletedDone();
   onDone();
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::OnSetAttachedErrorMKSControl --
 *
 *      Called when some error reported from viewControl connection.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::OnSetAttachedErrorMKSControl(bool cancelled,    // IN
                                  const Error &err,  // IN
                                  AbortSlot onAbort) // IN
{
   Log(LOGPFX "cui::MKS::OnSetAttachedErrorMKSControl (%p): %s\n", this, err.what());

   /*
    * If the MKS stalls, we will reconnect when it resumes.
    * See MKS::OnMKSStalled.
    *
    * If there are queued connection/disconnection requests, ignore the
    * current error.
    */
   if (!mIsMKSStalled && mCompleted.empty()) {
      // xxx: TODO. Make the err parameter more accurate and user-friendly.
      utf::string errStr = Format(
         GetLocalString(mAttached ? MSGID(cui.mks.detach) "Failed to disconnect from the MKS: %s"
                                  : MSGID(cui.mks.attach) "Unable to connect to the MKS: %s")
            .c_str(),
         err.what());

      error.emit(errStr);
   }

   OnSetAttachedCompletedError();
   onAbort(cancelled, err);
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::SetConnectingNotification --
 *
 *      Sets mConnectingNotification to the new value, emitting
 *      connectingNotificationChanged if it changed.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::SetConnectingNotification(bool connecting) // IN
{
   if (connecting == mConnectingNotification) {
      return;
   }

   mConnectingNotification = connecting;
   connectingNotificationChanged.emit();
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::ConnectMKS --
 *
 *      Try to connect to MKS. Connect to viewControl.
 *      The remote-mks startup process returns the name of a channel that
 *      viewControl client should connect to.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::ConnectMKS(void)
{
   NOT_IMPLEMENTED();
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::DisconnectMKS --
 *
 *      Disconnect MKS and process the next command in mCompleted queue.
 *      This function should not be public and just for
 *      internal use. It cannot be called in the interval of connecting.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::DisconnectMKS(void)
{
   DisconnectMKSControl();
   OnSetAttachedCompletedDone();
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::ConnectMKSControl --
 *
 *      Try to create the viewControl channel.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::ConnectMKSControl(AbortSlot onAbort, // IN
                       DoneSlot onDone)   // IN
{
   ConnectMKSServer(mMksControlPipeName, &mMksToken, onAbort, onDone);
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::DisconnectMKSControl --
 *
 *      Destroy the viewControl channel.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::DisconnectMKSControl(void)
{
   mMKSControlClient->Disconnect();
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::SetAttached --
 *
 *      Set the "attached" property of a MKS.
 *
 *      For remote MKS, we do need to manually connect to MKS (Get ticket, then
 *      launch remote-mks).
 *
 *      When viewControl is introduced, in order to make it compatible to the old
 *      code, we have to still use this function as the start point too. Please
 *      don't let the function name confuse you. We do use this function to
 *      CONNECT mks.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::SetAttached(bool attached) // IN
{
   Log(LOGPFX "cui::MKS::SetAttached (%p): %s\n", this, attached ? "attach" : "detach");

   // Don't attach when destroying the object.
   ASSERT(!(IsDestroying() && attached));
   if (IsDestroying() && attached) {
      return;
   }

   if (mPending) {
      if (!attached) {
         CancelConnecting();
      }

      /*
       * Schedule a retry of this last call to SetAttached() when the currently
       * pending SetAttached() operation completes.
       */
      mCompleted.connect(sigc::bind(sigc::mem_fun(this, &MKS::SetAttached), attached));
      return;
   }

   /*
    * There is now a pending SetAttached() operation, which is intended to flip
    * the value of 'mAttached'.
    */
   SetPending(true);

   if (mAttached == attached) {
      OnSetAttachedCompleted();
      return;
   }

   mWantToAttach = attached;
   attached ? ConnectMKS() : DisconnectMKS();
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::CancelConnecting --
 *
 *      Cancel it if in connecting process.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::CancelConnecting()
{
   mMKSControlClient->CancelConnecting();
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::GetPresent --
 *
 *      Whether we have been connected to MKS.
 *
 * Results:
 *      True if connected, false otherwise.
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

bool
MKS::GetPresent(void) const
{
   return mAttached;
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::IsMKSInitialized --
 *
 *    Whether the MKS has been connected and
 *    all the initial MKS states have been received.
 *
 * Results:
 *      true if MKS states has been initialized and false otherwise.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

bool
MKS::IsMKSInitialized() const
{
   return mMKSControlClient->GetConnectionState() == mksctrl::MKSControlClientBase::CON_INITIALIZED;
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::OnMKSControlConnectionBroken --
 *
 *      Callback for the viewControl connection is broken.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::OnMKSControlConnectionBroken(void)
{
   Log(LOGPFX "On viewControl connection broken (%p)\n", this);

   mksControlConnectionBroken.emit();

   if (!mAttached) {
      Warning(LOGPFX "cui::MKS::OnMKSControlConnectionBroken: "
                     "viewControl connection is broken but MKS is not attached (%p).\n",
              this);
      return;
   }

   // Disconnect viewControl.
   SetAttached(false);

   Log(LOGPFX "On viewControl connection broken done (%p).\n", this);
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::OnCanReconnectChanged --
 *
 *      Callback when property mMKSControlClient->remoteCanReconnect
 *      gets changed.
 *
 *      Report our ability to handle reconnection and listen for the loss of
 *      connection event.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::OnCanReconnectChanged(void)
{
   /*
    * This node changes values in exactly three instances.
    * 1. From unset to true for VNC MKS connections.
    * 2. From unset to false for IPC MKS connections.
    * 3. From set to unset when disconnecting from the MKS. When this occurs,
    *    we've already stopped listening and won't see the event.
    */
   if (mMKSControlClient->canReconnect == true) {
      mMKSControlClient->SetAttemptReConnect(true);

      sigc::slot<void> onConnectedChanged(sigc::mem_fun(this, &MKS::OnConnectedChanged));

      mMKSControlClient->isRemoteConnected.connect(onConnectedChanged);
      onConnectedChanged();
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::OnConnectedChanged --
 *
 *      Callback when property mMKSControlClient->isRemoteConnected
 *      gets changed.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::OnConnectedChanged(void)
{
   Log_Verbose(LOGPFX "cui::MKS::OnConnectedChanged (%p)\n", this);
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::OnConnectingNotificationTimeout --
 *
 *      Callback for when the timeout for notifying the user of re-connection
 *      taking too long is hit.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      If reconnection fails, a retry followed by full detach/attach cycle
 *      will be forced.
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::OnConnectingNotificationTimeout()
{
   SetConnectingNotification(true);
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::SetResolution --
 *
 *      Ask VMware tools to switch the guest to a given virtual screen resolution.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::SetResolution(int width,         // IN
                   int height,        // IN
                   AbortSlot onAbort, // IN
                   DoneSlot onDone)   // IN
{
   if (!mAttached) {
      cui::Cancel(onAbort);
      return;
   }

   mMKSControlClient->SetResolution(width, height, onAbort, onDone);
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::GetGuestTopologyLimits --
 *
 *      Retrieve the limits of the guest topology. They do not vary
 *      with the scaling constraint.
 *
 * Results:
 *      Four values: The max width of any one screen, the max height of any
 *      one screen, the max number of displays, and the maximum amount of display
 *      memory that can be used for the bounding box of the topology.
 *      Calculations that use that last value should assume 4 bytes per pixel.
 *
 *      hasBoundingBoxMemRestriction: Whether the size of the bounding box need
 *      fit in the maxScreenMemoryBytes. The restriction was dropped in HWv14.
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::GetGuestTopologyLimits(uint32 &maxScreenWidth,             // OUT
                            uint32 &maxScreenHeight,            // OUT
                            uint32 &maxNumDisplays,             // OUT
                            uint32 &maxScreenMemoryBytes,       // OUT
                            Bool &hasBoundingBoxMemRestriction) // OUT
   const
{
   const auto &limits = mMKSControlClient->GetGuestTopologyLimits();
   maxScreenWidth = limits.maxScreenWidth;
   maxScreenHeight = limits.maxScreenHeight;
   maxNumDisplays = limits.maxNumDisplays;
   maxScreenMemoryBytes = limits.maxScreenMemoryBytes;
   hasBoundingBoxMemRestriction = limits.hasBoundingBoxMemRestriction;
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::GetGrabState --
 *
 *      Retrieve the grab state of the MKS.
 *
 * Results:
 *      One of UNGRAB_HARD, UNGRAB_SOFT, or GRAB.
 *      Notice that GRAB_MOTION is an event clients send to the MKS,
 *      not a value they ever should get back.
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

MKS::GrabState
MKS::GetGrabState(void) const
{
   if (!mAttached) {
      return UNGRAB_HARD;
   }
   LOG(10, LOGPFX "cui::MKS::GetGrabState using viewControl\n");
   switch (mMKSControlClient->GetGrabState()) {
   case mksctrl::GrabState::GRABBED_CONSOLE:
   case mksctrl::GrabState::GRABBED_UNITY:
      return GRAB;
   case mksctrl::GrabState::UNGRABBED_HARD:
      return UNGRAB_HARD;
   case mksctrl::GrabState::UNGRABBED_MOTION:
      return UNGRAB_SOFT;
   case mksctrl::GrabState::INVALID:
      /*
       * viewControl is ready, but the grab state has not
       * been read from MKS yet.
       */
      return UNGRAB_HARD;
   }
   NOT_IMPLEMENTED();
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::SetGrabState --
 *
 *      Set the grab state of the MKS.
 *
 *      Callers that are aware of BUM should not use this function to set
 *      the state to GRAB or GRAB_MOTION; they should use SendGrabRequest
 *      instead. The exception to that is unity windows, which must continue
 *      using this function to grab.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::SetGrabState(GrabState grabState, // IN:
                  AbortSlot onAbort,   // IN/OPT:
                  DoneSlot onDone)     // IN/OPT:
{
   if (!mAttached) {
      cui::Cancel(onAbort);
      return;
   }
   LOG(10, LOGPFX "cui::MKS::SetGrabState using viewControl (state: %d)\n", grabState);
   /*
    * XXX TODO: Change this method to handle only ungrabs, and update
    * callers to use SendGrabRequest() for grab or motion grabs.
    * Also change the function name to SendUngrabRequest?
    */
   switch (grabState) {
   case UNGRAB_HARD:
      mMKSControlClient->HardUngrab(onAbort, onDone);
      return;
   case UNGRAB_SOFT:
      mMKSControlClient->MotionUngrab(onAbort, onDone);
      return;
   case GRAB:
      mMKSControlClient->RequestGrab(VIEWCONTROL_INVALID_ID, onAbort, onDone);
      return;
   case GRAB_MOTION:
      mMKSControlClient->RequestMotionGrab(VIEWCONTROL_INVALID_ID, onAbort, onDone);
      return;
   }
   NOT_IMPLEMENTED();
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::SendGrabRequest --
 *
 *      Issues a grab request for a specific MKS window.  (Pass window
 *      ID 0 if you used the legacy SetViewVal() to create an MKS
 *      window.)
 *
 *      If motionGrab is true, then the grab is treated as the result
 *      of moving the mouse into the window.  If the VM was previously
 *      hard-ungrabbed, then a motionGrab will not grab input.
 *
 *      Unlike SetGrabState(), this method allows you to specify a
 *      specific MKS window ID to grab into.  The mouse will smoothly
 *      transition between all MKS windows in the same window group
 *      as the specified window.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      If grabbing is allowed, input is grabbed in the specified window.
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::SendGrabRequest(MKSWindowID windowID, // IN
                     bool motionGrab,      // IN
                     AbortSlot onAbort,    // IN/OPT
                     DoneSlot onDone)      // IN/OPT
{
   if (!mAttached) {
      cui::Cancel(onAbort);
      return;
   }
   LOG(10,
       LOGPFX "cui::MKS::SendGrabRequest using viewControl "
              "(window: %d, bMotion: %d)\n",
       windowID, motionGrab);
   if (motionGrab) {
      mMKSControlClient->RequestMotionGrab(windowID, onAbort, onDone);
   } else {
      mMKSControlClient->RequestGrab(windowID, onAbort, onDone);
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::SetParentPID --
 *
 *      Set the "parent PID" that is passed in to the external MKS processes.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::SetParentPID(int parentPID) // IN
{
   mParentPID = parentPID;
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::ApplyHotkeys --
 *
 *      Apply the hotkey mask to the MKS, but only if the MKS is
 *      attached - it doesn't do anything to apply them earlier.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::ApplyHotkeys(void)
{
   if (!mAttached) {
      return;
   }

   ViewControlHotKey hotKey;

   hotKey.allModState.control = MKSGetViewControlKeyBindModState(
      mksKeys->ctrl, "pref.hotkey.rightControl", "pref.hotkey.leftControl");
   hotKey.allModState.shift = MKSGetViewControlKeyBindModState(
      mksKeys->shift, "pref.hotkey.rightShift", "pref.hotkey.leftShift");
   hotKey.allModState.alt =
      MKSGetViewControlKeyBindModState(mksKeys->alt, "pref.hotkey.rightAlt", "pref.hotkey.leftAlt");
   hotKey.allModState.gui =
      MKSGetViewControlKeyBindModState(mksKeys->gui, "pref.hotkey.rightGUI", "pref.hotkey.leftGUI");
   hotKey.disableHotkeyDelete = Preference_GetBool(FALSE, "pref.hotkey.disableHotkeyDelete");
   hotKey.disableHotkeyInsert = Preference_GetBool(FALSE, "pref.hotkey.disableHotkeyInsert");
   hotKey.disableHotkeyEscape = Preference_GetBool(FALSE, "pref.hotkey.disableHotkeyEscape");
   hotKey.disableExitFullScreenOnHotkeyRelease =
      Preference_GetBool(FALSE, "pref.hotkey.disableExitFullScreenOnHotkeyRelease");

   mMKSControlClient->SetHotkey(&hotKey);
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKSGetViewControlKeyBindModState --
 *
 *      Helper function to map the ui::Modifiers::KeyState
 *      to ViewControlKeyBindModState before sending to MKS
 *
 * Results:
 *      returns mapped ui::Modifiers::KeyState to ViewControlKeyBindModState
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

static ViewControlKeyBindModState
MKSGetViewControlKeyBindModState(cui::Modifiers::KeyState state, // IN
                                 const char *rightKeyName,       // IN
                                 const char *leftKeyName)        // IN
{
   if (cui::Modifiers::UP == state) {
      return VIEWCONTROL_KEYBIND_MOD_BOTH_UP;
   } else if (cui::Modifiers::DOWN == state) {
      return VIEWCONTROL_KEYBIND_MOD_BOTH_DOWN;
   } else if (cui::Modifiers::EITHER == state) {
      int32 rightKey = Preference_GetTriState(MKS_HOTKEY_STATE_DONT_CARE, rightKeyName);
      int32 leftKey = Preference_GetTriState(MKS_HOTKEY_STATE_DONT_CARE, leftKeyName);

      if (MKS_HOTKEY_STATE_DOWN == leftKey && MKS_HOTKEY_STATE_UP == rightKey) {
         return VIEWCONTROL_KEYBIND_MOD_LEFT_DOWN_RIGHT_UP;
      } else if (MKS_HOTKEY_STATE_DOWN == leftKey && MKS_HOTKEY_STATE_DONT_CARE == rightKey) {
         return VIEWCONTROL_KEYBIND_MOD_LEFT_DOWN_RIGHT_DONTCARE;
      } else if (MKS_HOTKEY_STATE_DONT_CARE == leftKey && MKS_HOTKEY_STATE_UP == rightKey) {
         return VIEWCONTROL_KEYBIND_MOD_LEFT_DONTCARE_RIGHT_UP;
      } else if (MKS_HOTKEY_STATE_UP == leftKey && MKS_HOTKEY_STATE_DOWN == rightKey) {
         return VIEWCONTROL_KEYBIND_MOD_RIGHT_DOWN_LEFT_UP;
      } else if (MKS_HOTKEY_STATE_DONT_CARE == leftKey && MKS_HOTKEY_STATE_DOWN == rightKey) {
         return VIEWCONTROL_KEYBIND_MOD_RIGHT_DOWN_LEFT_DONTCARE;
      } else if (MKS_HOTKEY_STATE_UP == leftKey && MKS_HOTKEY_STATE_DONT_CARE == rightKey) {
         return VIEWCONTROL_KEYBIND_MOD_RIGHT_DONTCARE_LEFT_UP;
      }
   }
   return VIEWCONTROL_KEYBIND_MOD_INVALID;
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::SetHookedKeys --
 *
 *      Set the list of hooked keys that the MKS should notify us of rather
 *      than passing to the guest. The list is of the form of a ';' delimited
 *      string where each element is of the form "0x%x,0x%x" where the first
 *      part is the key scancode and the second is the modifier mask. Pass '0xffff'
 *      for the modifier mask if you want to use the current MKS hotkey mask.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::SetHookedKeys(const std::vector<HotKey> &keyList) // IN:
{
   if (mHookedKeys == keyList) {
      return;
   }

   mHookedKeys = keyList;
   ApplyHookedKeys();
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::ApplyHookedKeys --
 *
 *      Apply the set of hooked keys to the MKS, but only if the MKS is
 *      attached - it doesn't do anything to apply them earlier.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 * @deprecated:
 *      TODO: This is deprecated and we need to move to keyboard/keyBindings.
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::ApplyHookedKeys(void)
{
   if (mAttached) {
      utf::string hookedKeyStr;
      const char *delimiter = "";
      for (std::vector<HotKey>::const_iterator i = mHookedKeys.begin(); i != mHookedKeys.end();
           ++i) {
         if (!i->empty()) {
            hookedKeyStr += cui::Format("%s0x%04x,0x%04x", delimiter, i->vkey, i->modifiers);
            delimiter = ";";
         }
      }

      mMKSControlClient->ApplyHookedKeys(hookedKeyStr);
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::OnHookedKeyPressed --
 *
 *      viewControl callback for when a hooked key is pressed. This marshalls
 *      the key and emits a conventional signal for it. Note that we check that
 *      there is actually a value as we'll be notified when the value is
 *      cleared as well.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 * @deprecated:
 *      TODO: This is deprecated and we need to move to
 *      keyboard/keyBindings/notifyUIEvent (keyBindingsNotifyUIEventChanged and
 *      OnNotifyUIEvent).
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::OnHookedKeyPressed(void)
{
   const utf::string pressed = mMKSControlClient->hookedKeyPressedStr;
   if (!pressed.empty()) {
      int vkey = 0;
      int modifiers = 0;
      if (sscanf(pressed.c_str(), "%i,%i", &vkey, &modifiers) == 2) {
         hookedKeyPressed.emit(HotKey(vkey, modifiers));
      }
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::GetHotKeyPrefix --
 *
 *      Get the current hotkey prefix being used by the MKS.
 *
 * Results:
 *      Hotkey prefix (e.g. "Ctrl+Alt")
 *
 * Side effects:
 *      None
 *
 * @deprecated
 *      The UI constructs this and sends it to the MKS via /mks/hotkey and then
 *      turns around and asks the MKS to translate it and return it as text.
 *      We need to remove this from the MKS and do it from within the UI.
 *
 *-----------------------------------------------------------------------------
 */

utf::string
MKS::GetHotKeyPrefix(void) const
{
   const char *prefixString = mMKSControlClient->hotKeyPrefix;
   utf::string hotKeyPrefix = prefixString;

   // Return minus trailing "+"

   return !hotKeyPrefix.empty() ? hotKeyPrefix.substr(0, hotKeyPrefix.length() - 1) : "";
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::GetUngrabLocked --
 *
 *      Retrieve the "releaseEvent" property of an MKS.
 *
 * Results:
 *      True if locked, false otherwise.
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

bool
MKS::GetUngrabLocked(void) const
{
   if (!mAttached) {
      return false;
   }
   LOG(10, LOGPFX "cui::MKS::GetUngrabLocked using viewControl\n");
   return mMKSControlClient->IsClientReleasingGrab();
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::SetUngrabLocked --
 *
 *      Hold or release the ungrab lock. While you hold the ungrab lock
 *      the MKS is guaranteed to be in windowed, ungrabbed mode.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      Emits capsChanged signal.
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::SetUngrabLocked(bool ungrabLocked) // IN:
{
   if (!mAttached || mUngrabLocked == ungrabLocked) {
      return;
   }
   LOG(10, LOGPFX "cui::MKS::SetUngrabLocked using viewControl\n");
   mUngrabLocked = ungrabLocked;
   if (mUngrabLocked) {
      mMKSControlClient->ReleaseGrab(AbortSlot(),
                                     sigc::mem_fun(this, &MKS::OnSetUngrabLockedFinish));
   } else {
      mMKSControlClient->UnreleaseGrab(AbortSlot(),
                                       sigc::mem_fun(this, &MKS::OnSetUngrabLockedFinish));
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::OnSetUngrabLockedFinish --
 *
 *      Called when the 'release' command has been received and processed by the MKS.
 *      See cui::MKS::SetUngrabLocked.
 *      If only reference count changed, which means no command is sent to MKS, this
 *      callback is called instantly with grabStateChanged equals false.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::OnSetUngrabLockedFinish(
   bool grabStateChanged) // IN: whether MKS is really informed, or only reference count is changed.
{
   if (grabStateChanged) {
      OnUngrabLockChanged();
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::OnUngrabLockChanged --
 *
 *      callback when grabStateChanged.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      Emits the ungrabLockedChanged and capsChanged signals.
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::OnUngrabLockChanged()
{
   ungrabLockedChanged.emit();
   capsChanged.emit();
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::TestPresent --
 *
 *      Test if the MKS is present.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

bool
MKS::TestPresent(Capability::FailureReasonList &list) // IN/OUT
{
   if (!GetPresent()) {
      MKSFailureReason *reason =
         new MKSFailureReason("mks: not present", MKSFailureReason::ERR_NOT_PRESENT);
      list.push_back(reason);
      return false;
   }
   return true;
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::TestUngrabUnlocked --
 *
 *      Test against ungrab being locked.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

bool
MKS::TestUngrabUnlocked(Capability::FailureReasonList &list) // IN/OUT
{
   if (GetUngrabLocked()) {
      MKSFailureReason *reason =
         new MKSFailureReason("mks: ungrab locked", MKSFailureReason::ERR_UNGRAB_LOCKED);
      list.push_back(reason);
      return false;
   }
   return true;
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::SendCtrlAltDel --
 *
 *      Send Ctrl+Alt+Del to the virtual machine through the MKS.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::SendCtrlAltDel()
{
   SendKeyEvent(MKS_MOD_ALT | MKS_MOD_CONTROL, VSCAN_DELETE);
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::GetVNCLastError --
 *
 *      Get the last error for VNC Server.
 *
 * Results:
 *      Error message if there is an error, "" otherwise.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

utf::string
MKS::GetVNCLastError() const
{
   if (mVNCLastError != 0) {
      return GetLocalString(AsyncSocket_MsgError(mVNCLastError));
   }

   return "";
}


/*
 *----------------------------------------------------------------------------
 *
 * cui::MKS::SetAllowDragMotionUngrab --
 *
 *      Tell the MKS whether to allow button down motion ungrab. This is used
 *      by dnd when it determines there is a g->h dnd operation.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

void
MKS::SetAllowDragMotionUngrab(bool allow) // IN
{
   mMKSControlClient->SetAllowButtonDownMotionUngrab(allow);
}


/*
 *----------------------------------------------------------------------------
 *
 * cui::MKS::GetAttemptDnDUngrab --
 *
 *      Gets the x/y coordinates of where the mouse attempted to ungrab with
 *      the button down.
 *
 * Results:
 *      The x/y coordinates corresponding to the ungrab position.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

void
MKS::GetAttemptDnDUngrab(int &x, // OUT
                         int &y) // OUT
   const
{
   x = mMKSControlClient->ghDndUngrabCoordinates.x;
   y = mMKSControlClient->ghDndUngrabCoordinates.y;
}


/*
 *----------------------------------------------------------------------------
 *
 * cui::MKS::GetGHDnDUngrab --
 *
 *      Gets X11's notion of ungrab to allow dnd to start.
 *
 * Results:
 *      true if the mks has ungrabed X, false otherwise.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

bool
MKS::GetGHDnDUngrab() const
{
   return mMKSControlClient->GHDnDUngrab;
}


/*
 *----------------------------------------------------------------------------
 *
 * cui::MKS::GetKeyboardLEDState --
 *
 *      Gets keyboard LED state from the guest.
 *
 * Results:
 *      Whether various LED states are enabled.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

void
MKS::GetKeyboardLEDState(bool &numLockEnabled,    // OUT: whether the NumLock LED is on
                         bool &capsLockEnabled,   // OUT: whether the CapsLock LED is on
                         bool &scrollLockEnabled) // OUT: whether the ScrollLock LED is on
   const
{
   numLockEnabled = this->numLockEnabled;
   capsLockEnabled = this->capsLockEnabled;
   scrollLockEnabled = this->scrollLockEnabled;
}


/*
 *----------------------------------------------------------------------------
 *
 * cui::MKS::OnKeyboardLEDStateChanged --
 *
 *      Gets called when keyboardLEDStateInitialized is changed. The
 *      keyboardLEDStateInitialized is set to true in every LED state change
 *      callback from MKS.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

void
MKS::OnKeyboardLEDStateChanged(bool numLockEnabled,    // IN
                               bool capsLockEnabled,   // IN
                               bool scrollLockEnabled) // IN
{
   this->numLockEnabled = numLockEnabled;
   this->capsLockEnabled = capsLockEnabled;
   this->scrollLockEnabled = scrollLockEnabled;

   keyboardLEDStateInitialized = true;
   keyboardLEDStateChanged.emit();
}


/*
 *----------------------------------------------------------------------------
 *
 * cui::MKS::GetCursorConstrained --
 *
 *      Gets whether UI needs to enable/disable cursor constraining.
 *
 * Result:
 *      true if UI needs to enable cursor constraining, false otherwise.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

bool
MKS::GetCursorConstrained(void) const
{
   return cursorConstrained;
}


/*
 *----------------------------------------------------------------------------
 *
 * cui::MKS::OnCursorConstrainedChanged --
 *
 *      Gets called when cursorConstrainedChanged is changed.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

void
MKS::OnCursorConstrainedChanged(bool cursorConstrained) // IN
{
   this->cursorConstrained = cursorConstrained;

   cursorConstrainedChanged.emit();
}


/*
 *----------------------------------------------------------------------------
 *
 * cui::MKS::ClearGHDnDUngrab --
 *
 *      Clears the X11 ungrab notification flag.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

void
MKS::ClearGHDnDUngrab()
{
   mMKSControlClient->GHDnDUngrab = false;
}


/*
 *----------------------------------------------------------------------------
 *
 * cui::MKS::GetHostShortcutsAreEnabled --
 *
 *      Retrieves the "prefs/hostShortcutsAreEnabled" runtime setting.
 *      For the global user preference, see cui::Prefs.
 *
 * Results:
 *      true if shortcuts are enabled, false otherwise.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

bool
MKS::GetHostShortcutsAreEnabled(void) const
{
   return hostShortcutsAreEnabled;
}


/*
 *----------------------------------------------------------------------------
 *
 * cui::MKS::SetHostShortcutsAreEnabled --
 *
 *      Sets the "prefs/hostShortcutsAreEnabled" runtime setting.
 *      For the global user preference, see cui::Prefs.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

void
MKS::SetHostShortcutsAreEnabled(bool enabled) // IN:
{
   if (GetHostShortcutsAreEnabled() != enabled) {
      hostShortcutsAreEnabled = enabled;
      mMKSControlClient->SetPreference(VIEWCONTROL_PREF_MACOS_HOST_SHORTCUTS_ARE_ENABLED, enabled);
   }
}


/*
 *----------------------------------------------------------------------------
 *
 * cui::MKS::GetPending --
 *
 *      Check if the mks is pending.
 *
 * Results:
 *      true if is in pending. false, otherwise.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

bool
MKS::GetPending(void) const
{
   return mPending;
}


/*
 *----------------------------------------------------------------------------
 *
 * cui::MKS::SetPending --
 *
 *      Set the current pending state.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

void
MKS::SetPending(bool pending) // IN:
{
   if (mPending != pending) {
      mPending = pending;
      pendingChanged.emit();
   }
}


/*
 *----------------------------------------------------------------------------
 *
 * cui::MKS::GetConnectingNotification --
 *
 *      Check whether we need to notify the user that we are reconnecting.
 *
 * Results:
 *      true if we should notify the user. false, otherwise.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

bool
MKS::GetConnectingNotification(void) const
{
   return mConnectingNotification;
}


/*
 *----------------------------------------------------------------------------
 *
 * mapToViewControlLEDBindState --
 *
 *      Helper function to map MKS::KeyBinding::LEDState to
 *      respective ViewControlLEDBindState.
 *
 * Results:
 *      returns mapped LEDBindState.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

ViewControlLEDBindState
mapToViewControlLEDBindState(MKS::KeyBinding::LEDState state)
{
   switch (state) {
   case MKS::KeyBinding::LED_DONT_CARE:
      return VIEWCONTROL_LEDBIND_DONTCARE;
   case MKS::KeyBinding::LED_ON:
      return VIEWCONTROL_LEDBIND_ON;
   case MKS::KeyBinding::LED_OFF:
      return VIEWCONTROL_LEDBIND_OFF;
   default:
      return VIEWCONTROL_LEDBIND_INVALID;
   }
}


/*
 *----------------------------------------------------------------------------
 *
 * mapToViewControlInputDeviceType --
 *
 *      Helper function to map MKS::TriggerDevice to
 *      respective ViewControlInputDeviceType.
 *
 * Results:
 *      returns mapped InputDeviceType.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

ViewControlInputDeviceType
mapToViewControlInputDeviceType(MKS::TriggerDevice td)
{
   switch (td) {
   case MKS::TRIGGER_DEVICE_KEYBOARD:
      return VIEWCONTROL_IDT_KEYBOARD;
   case MKS::TRIGGER_DEVICE_MOUSE:
      return VIEWCONTROL_IDT_MOUSE;
   default:
      return VIEWCONTROL_IDT_INVALID;
   }
}


/*
 *----------------------------------------------------------------------------
 *
 * MKSGetViewControlKeyBindModState --
 *
 *      Helper function to map MKS::KeyBinding::ModifierState to
 *      respective ViewControlKeyBindModState.
 *
 * Results:
 *      returns mapped KeyBindModState.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

ViewControlKeyBindModState
MKSGetViewControlKeyBindModState(MKS::KeyBinding::ModifierState state)
{
   switch (state) {
   case MKS::KeyBinding::BOTH_UP:
      return VIEWCONTROL_KEYBIND_MOD_BOTH_UP;
   case MKS::KeyBinding::ONE_DOWN_OTHER_DONT_CARE:
      return VIEWCONTROL_KEYBIND_MOD_ONE_DOWN_OTHER_DONTCARE;
   case MKS::KeyBinding::BOTH_DONT_CARE:
      return VIEWCONTROL_KEYBIND_MOD_BOTH_DONTCARE;
   case MKS::KeyBinding::LEFT_DOWN_RIGHT_UP:
      return VIEWCONTROL_KEYBIND_MOD_LEFT_DOWN_RIGHT_UP;
   case MKS::KeyBinding::RIGHT_DOWN_LEFT_UP:
      return VIEWCONTROL_KEYBIND_MOD_RIGHT_DOWN_LEFT_UP;
   case MKS::KeyBinding::LEFT_DOWN_RIGHT_DONT_CARE:
      return VIEWCONTROL_KEYBIND_MOD_LEFT_DOWN_RIGHT_DONTCARE;
   case MKS::KeyBinding::RIGHT_DOWN_LEFT_DONT_CARE:
      return VIEWCONTROL_KEYBIND_MOD_RIGHT_DOWN_LEFT_DONTCARE;
   case MKS::KeyBinding::BOTH_DOWN:
      return VIEWCONTROL_KEYBIND_MOD_BOTH_DOWN;
   case MKS::KeyBinding::ONE_DOWN_OTHER_UP:
      return VIEWCONTROL_KEYBIND_MOD_ONE_DOWN_OTHER_UP;
   case MKS::KeyBinding::ONE_DONT_CARE_OTHER_UP:
      return VIEWCONTROL_KEYBIND_MOD_ONE_UP_OTHER_DONTCARE;
   case MKS::KeyBinding::LEFT_DONT_CARE_RIGHT_UP:
      return VIEWCONTROL_KEYBIND_MOD_LEFT_DONTCARE_RIGHT_UP;
   case MKS::KeyBinding::RIGHT_DONT_CARE_LEFT_UP:
      return VIEWCONTROL_KEYBIND_MOD_RIGHT_DONTCARE_LEFT_UP;
   default:
      return VIEWCONTROL_KEYBIND_MOD_INVALID;
   }
}


/*
 *----------------------------------------------------------------------------
 *
 * cui::MKS::SetKeyBindings --
 *
 *      Sends the given key bindings to the MKS.
 *      This function should not be run unless the MKS is present.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

void
MKS::SetKeyBindings(const std::vector<KeyBinding> &keyBindings) // IN
{
   if (!GetPresent()) {
      Warning(LOGPFX "cui::MKS::SetKeyBindings: the MKS is not present so trying to "
                     "set keybindings will not actually work.\n");
   }

   std::vector<ViewControlInputMapping> mappings;
   mappings.reserve(keyBindings.size());

   for (std::vector<KeyBinding>::const_iterator it = keyBindings.begin(); it != keyBindings.end();
        it++) {
      ViewControlInputMapping aMapping;
      // mCtx[path + "keyBinding/useHotkeyMods/"] =
      // it->sourceHostKeys.useHotkeyMods;
      switch (it->action.type) {
      case KeyBinding::SEND_KEY:
         aMapping.type = VIEWCONTROL_IMT_SEND_KEYS;
         aMapping.sendKeys.trigger.triggerDevice =
            mapToViewControlInputDeviceType(it->sourceHostKeys.triggerDevice);
         aMapping.sendKeys.trigger.modBinding.control =
            MKSGetViewControlKeyBindModState(it->sourceHostKeys.controlKeyState);
         aMapping.sendKeys.trigger.modBinding.alt =
            MKSGetViewControlKeyBindModState(it->sourceHostKeys.altKeyState);
         aMapping.sendKeys.trigger.modBinding.shift =
            MKSGetViewControlKeyBindModState(it->sourceHostKeys.shiftKeyState);
         aMapping.sendKeys.trigger.modBinding.gui =
            MKSGetViewControlKeyBindModState(it->sourceHostKeys.guiKeyState);

         aMapping.sendKeys.trigger.ledBinding.numLock =
            mapToViewControlLEDBindState(it->sourceHostKeys.numLockState);
         aMapping.sendKeys.trigger.ledBinding.capsLock =
            mapToViewControlLEDBindState(it->sourceHostKeys.capsLockState);
         aMapping.sendKeys.trigger.ledBinding.scrollLock =
            mapToViewControlLEDBindState(it->sourceHostKeys.scrollLockState);

         aMapping.sendKeys.allowDelayMods = it->sourceHostKeys.allowDelayMods;
         aMapping.sendKeys.trigger.triggerKey.usagePage = it->sourceHostKeys.usbHid.usagePage;
         aMapping.sendKeys.trigger.triggerKey.usageCode = it->sourceHostKeys.usbHid.usageCode;

         aMapping.sendKeys.modResult.lControl = it->action.sendGuestKeys.leftControlKey;
         aMapping.sendKeys.modResult.rControl = it->action.sendGuestKeys.rightControlKey;
         aMapping.sendKeys.modResult.lAlt = it->action.sendGuestKeys.leftAltKey;
         aMapping.sendKeys.modResult.rAlt = it->action.sendGuestKeys.rightAltKey;
         aMapping.sendKeys.modResult.lShift = it->action.sendGuestKeys.leftShiftKey;
         aMapping.sendKeys.modResult.rShift = it->action.sendGuestKeys.rightShiftKey;
         aMapping.sendKeys.modResult.lGui = it->action.sendGuestKeys.leftGUIKey;
         aMapping.sendKeys.modResult.rGui = it->action.sendGuestKeys.rightGUIKey;
         aMapping.sendKeys.resultKey.usageCode = it->action.sendGuestKeys.usbHid.usageCode;
         aMapping.sendKeys.resultKey.usagePage = it->action.sendGuestKeys.usbHid.usagePage;
         break;
      case KeyBinding::NOTIFY_UI:
         aMapping.type = VIEWCONTROL_IMT_CALLBACK;

         aMapping.callback.trigger.triggerDevice =
            mapToViewControlInputDeviceType(it->sourceHostKeys.triggerDevice);
         aMapping.callback.trigger.modBinding.control =
            MKSGetViewControlKeyBindModState(it->sourceHostKeys.controlKeyState);
         aMapping.callback.trigger.modBinding.alt =
            MKSGetViewControlKeyBindModState(it->sourceHostKeys.altKeyState);
         aMapping.callback.trigger.modBinding.shift =
            MKSGetViewControlKeyBindModState(it->sourceHostKeys.shiftKeyState);
         aMapping.callback.trigger.modBinding.gui =
            MKSGetViewControlKeyBindModState(it->sourceHostKeys.guiKeyState);

         aMapping.callback.trigger.ledBinding.numLock =
            mapToViewControlLEDBindState(it->sourceHostKeys.numLockState);
         aMapping.callback.trigger.ledBinding.capsLock =
            mapToViewControlLEDBindState(it->sourceHostKeys.capsLockState);
         aMapping.callback.trigger.ledBinding.scrollLock =
            mapToViewControlLEDBindState(it->sourceHostKeys.scrollLockState);

         aMapping.callback.allowDelayMods = it->sourceHostKeys.allowDelayMods;
         aMapping.callback.trigger.triggerKey.usagePage = it->sourceHostKeys.usbHid.usagePage;
         aMapping.callback.trigger.triggerKey.usageCode = it->sourceHostKeys.usbHid.usageCode;

         aMapping.callback.callbackUID = it->action.notifyUI;
         break;
      case KeyBinding::SET_QUOTE:
         aMapping.type = VIEWCONTROL_IMT_SET_QUOTE;
         aMapping.setQuote.modBinding.control =
            MKSGetViewControlKeyBindModState(it->sourceHostKeys.controlKeyState);
         aMapping.setQuote.modBinding.alt =
            MKSGetViewControlKeyBindModState(it->sourceHostKeys.altKeyState);
         aMapping.setQuote.modBinding.shift =
            MKSGetViewControlKeyBindModState(it->sourceHostKeys.shiftKeyState);
         aMapping.setQuote.modBinding.gui =
            MKSGetViewControlKeyBindModState(it->sourceHostKeys.guiKeyState);

         aMapping.setQuote.triggerKey.usagePage = it->sourceHostKeys.usbHid.usagePage;
         aMapping.setQuote.triggerKey.usageCode = it->sourceHostKeys.usbHid.usageCode;

         break;
      }

      mappings.push_back(aMapping);
   }

   mMKSControlClient->SetInputMappings(mappings.data(), mappings.size());
}


/*
 *----------------------------------------------------------------------------
 *
 * cui::MKS::SetOneToOneKeyBindings --
 *
 *      Sends the given one to one key bindings to the MKS.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

void
MKS::SetOneToOneKeyBindings(const std::vector<OneToOneKeyBinding> &keyBindings) // IN
{

   std::vector<ViewControlInputMapping> mappings;
   mappings.reserve(keyBindings.size());

   for (std::vector<OneToOneKeyBinding>::const_iterator it = keyBindings.begin();
        it != keyBindings.end(); it++) {
      ViewControlInputMapping aMapping;
      aMapping.type = VIEWCONTROL_IMT_ONE_TO_ONE;
      aMapping.oneToOne.device = mapToViewControlInputDeviceType(it->triggerDevice);
      aMapping.oneToOne.fromKey.usagePage = it->fromKey.usagePage;
      aMapping.oneToOne.fromKey.usageCode = it->fromKey.usageCode;
      aMapping.oneToOne.toKey.usagePage = it->toKey.usagePage;
      aMapping.oneToOne.toKey.usageCode = it->toKey.usageCode;

      mappings.push_back(aMapping);
   }

   mMKSControlClient->SetInputMappings(mappings.data(), mappings.size());
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::SendUnicodeCodePoint --
 *
 *      Sends the unicode code point to the MKS.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

void
MKS::SendUnicodeCodePoint(unsigned int unicodeCodePoint, // IN
                          uint64 timeStamp,              // IN
                          AbortSlot onAbort,             // IN
                          DoneSlot onDone)               // IN
{
   mMKSControlClient->SendUnicodeCodePoint(unicodeCodePoint, timeStamp);
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::SendKey --
 *
 *      Send a key through the "Keyboard Of Last Resort" (or "KBLR").
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

void
MKS::SendKey(HIDUsage usbHid,   // IN
             bool isKeyDown,    // IN
             AbortSlot onAbort, // IN
             DoneSlot onDone)   // IN
{
   mMKSControlClient->SendKey(usbHid, isKeyDown, onAbort, onDone);
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::SendKeyEvent --
 *
 *      Sends the given key and modifiers to the MKS.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

void
MKS::SendKeyEvent(MKSModifierState modState, // IN
                  VScancode scancode,        // IN
                  AbortSlot onAbort,         // IN
                  DoneSlot onDone)           // IN
{
   HIDUsage hidKey = KeyboardMapping_VScanToHID(scancode);
   mMKSControlClient->SendKeyEventWithModifiers(hidKey, modState, onAbort, onDone);
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::InjectKeys --
 *
 *      Sends the given sequence of characters, as if the user had pressed
 *      and released each key. This method does not affect the VM's grab state.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

void
MKS::InjectKeys(const utf::string &sequence, // IN
                AbortSlot onAbort,           // IN/OPT
                DoneSlot onDone)             // IN/OPT
{
   if (sequence.empty()) {
      onDone();
      return;
   }

   mMKSControlClient->SendTextAsKeySequence(sequence, onAbort, onDone);
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::SendMacCGEvent --
 *
 *    Sends a macOS CGEvent to the MKS. This will be used in scenarios where the
 *    MKS is unable to create an event tap and will need to rely on the UI to
 *    send events to it, such as when the UI is running from an App Sandbox.
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::SendMacCGEvent(const uint8 *cgEventBytes, // IN
                    uint32 cgEventLength,      // IN
                    int eventPid,              // IN
                    AbortSlot onAbort,         // IN/OPT
                    DoneSlot onDone)           // IN/OPT
{
   mMKSControlClient->SendMacCGEvent(cgEventBytes, cgEventLength, eventPid, onAbort, onDone);
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::SendUnityMouseEvent --
 *
 *    Sends a mouse input event to the guest, for use in unity mode.
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::SendUnityMouseEvent(int x,              // IN
                         int y,              // IN
                         int wheelDistance,  // IN
                         int horizDistance,  // IN
                         bool leftBtnDown,   // IN
                         bool rightBtnDown,  // IN
                         bool middleBtnDown, // IN
                         bool btn4Down,      // IN
                         bool btn5Down,      // IN
                         AbortSlot onAbort,  // IN/OPT
                         DoneSlot onDone)    // IN/OPT
{
   mMKSControlClient->SendUnityMouseEvent(x, y, wheelDistance, horizDistance, leftBtnDown,
                                          rightBtnDown, middleBtnDown, btn4Down, btn5Down, onAbort,
                                          onDone);
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::SendUnityMouseEvent --
 *
 *    Sends a mouse input event to the guest, for use in unity mode.
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::SendUnityMouseEvent(const UnityMouseEvent &mouseEvent, // IN
                         AbortSlot onAbort,                 // IN/OPT
                         DoneSlot onDone)                   // IN/OPT
{
   SendUnityMouseEvent(mouseEvent.x, mouseEvent.y, mouseEvent.vertWheelDistance,
                       mouseEvent.horzWheelDistance, mouseEvent.leftBtnDown,
                       mouseEvent.rightBtnDown, mouseEvent.middleBtnDown, mouseEvent.btn4Down,
                       mouseEvent.btn5Down);
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::SendUnityTouchEvent --
 *
 *    Sends a touch input event to the guest, for use in unity mode.
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::SendUnityTouchEvent(const UnityTouchEvent &touchEvent, // IN
                         AbortSlot onAbort,                 // IN/OPT
                         DoneSlot onDone)                   // IN/OPT
{
   if (touchEvent.points.empty()) {
      // No touch input to send.
      return;
   }
   mMKSControlClient->SendUnityTouchEvent(touchEvent.points);
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::KeyBinding::KeyBinding --
 *
 *      Default constructor for the KeyBinding object.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

MKS::KeyBinding::KeyBinding()
{
   memset(this, 0, sizeof *this);
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::OneToOneKeyBinding::OneToOneKeyBinding --
 *
 *      Default constructor for the OneToOneKeyBinding object.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

MKS::OneToOneKeyBinding::OneToOneKeyBinding()
{
   memset(this, 0, sizeof *this);
}


/*
 *----------------------------------------------------------------------------
 *
 * cui::MKS::SetEatKeys --
 *
 *      This controls which guest modifier keys should be never sent
 *      individually to the guest.  This can be used for example to
 *      prevent the start menu to appearing while in Unity, even when
 *      pressing and releasing the key mapped to the windows key, by
 *      setting lGui and rGui to be eaten.  This does not prevent key
 *      combination such as window-r from being sent to the guest.
 *
 *      This key eating happens to guest keys not host keys meaning
 *      these keys are detected _after_ one-to-one remapping have
 *      taken place unlike normal key delaying which happens before
 *      one-to-one remapping.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

void
MKS::SetEatKeys(bool lControl, // IN
                bool rControl, // IN
                bool lAlt,     // IN
                bool rAlt,     // IN
                bool lShift,   // IN
                bool rShift,   // IN
                bool lGui,     // IN
                bool rGui)     // IN
{
   mMKSControlClient->SetEatKeys(lControl, rControl, lAlt, rAlt, lShift, rShift, lGui, rGui);
}


/*
 *----------------------------------------------------------------------------
 *
 * cui::MKS::SetAllowGrabInUnity --
 *
 *      This allows or disallows the UI to grab while in Unity mode.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

void
MKS::SetAllowGrabInUnity(bool value) // IN
{
   mMKSControlClient->SetAllowGrabInUnity(value);
}


/*
 *----------------------------------------------------------------------------
 *
 * cui::MKS::GetAllowGrabInUnity --
 *
 *      Checks if the UI is allowed to grab in Unity mode.
 *
 * Results:
 *      True if the UI is allowed to grab while in Unity mode, false otherwise.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

bool
MKS::GetAllowGrabInUnity()
{
   return mMKSControlClient->useUnityGrab;
}


/*
 *----------------------------------------------------------------------------
 *
 * cui::MKS::SetUnityHostCursorScale --
 *
 *      Sets the scale to use for the host cursor while in Unity. For example,
 *      specify 2.0 to resize the cursor bitmap to double the original size,
 *      or 0.5 to resize it to half the original size.
 *
 *      This is needed when Unity windows are being scaled on the host side.
 *      The MKS only tracks the grabbed window in non-Unity, so it can't
 *      automatically determine the cursor scale to use while in Unity.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *----------------------------------------------------------------------------
 */

void
MKS::SetUnityHostCursorScale(double value) // IN
{
   mMKSControlClient->SetUnityHostCursorScale(value);
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::SetMouseInMKSWindow --
 *
 *      This command is used by the Fusion UI to tell the MKS
 *      when the mouse is no longer inside any of the windows
 *      in the currently grabbed window group.  The MKS uses
 *      this information to implement obscured ungrab while the
 *      MKS is in absolute mode.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

void
MKS::SetMouseInMKSWindow(bool inWindow,     // IN
                         AbortSlot onAbort, // IN
                         DoneSlot onDone)   // IN
{
   return mMKSControlClient->SetMouseInMKSWindow(inWindow);
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::SetDelayGUIKeyUp --
 *
 *      Defines if the MKS should delay all GUI key up events, and if
 *      so for how long in milliseconds. This is used on Fusion when
 *      "Enable MacOS keyboard shortcuts" is selected in order to
 *      prevent the start menu from appearing in the guest when
 *      Command+Tabbing away from an MKS-drawn window.
 *
 *      When the UI sends the GUI key down event to the MKS, the MKS does not
 *      send that event to the guest immediately. The MKS will delay the GUI key
 *      down infinitely until something interesting happens. "Interesting"
 *      things include:
 *
 *         1) A GUI key up event is sent to the MKS; the MKS delays sending it by
 *            delayGUIKeyUp milliseconds; that time expires and the UI hasn't
 *            done anything to stop the MKS from sending the GUI key to the
 *            guest.
 *         2) Pressing other keys on the keyboard.
 *         3) Pressing a mouse button.
 *
 *      The delayGUIKeyUp timer starts when the MKS processes the GUI key up
 *      event that was sent from the UI.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

void
MKS::SetDelayGUIKeyUp(int milliseconds) // IN
{
   if (mAttached) {
      mMKSControlClient->SetGUIKeyUpDelay(milliseconds);
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::DropDelayedModifierKeys --
 *
 *      Ask the MKS to drop any modifiers that have been sent to the MKS, but
 *      not yet sent to the guest.
 *
 *      This is used by Fusion, in combination with delayGUIKeyUp (see
 *      MKS::SetDelayGUIKeyUp()), to try to help with the race conditions that
 *      happen around the user hitting Command+Tab, for instance. Depending on
 *      the scenario, the UI gets a GUI key down and up, and we have no
 *      choice but to send both events to the MKS. It is only after this that we
 *      find out that in a very short period of time following these events, we
 *      see another event that means that we should not send the GUI key down
 *      and up to the guest.
 *
 *      See https://wiki.eng.vmware.com/MKS:_Command/Windows_Key_Problems for
 *      more details.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

void
MKS::DropDelayedModifierKeys(AbortSlot onAbort, // IN/OPT
                             DoneSlot onDone)   // IN/OPT

{
   mMKSControlClient->DropDelayedModKeys(onAbort, onDone);
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::ReleaseAllKeys --
 *
 *      Ask the MKS to release any pressed keys.
 *
 *      This should reset the pressed keyboard state in the host and guest code
 *      and restore our state to no keys being pressed. This also happens when
 *      the MKS ungrabs, but this method provides a finer grain of control, and
 *      allows us to restore keyboard state without having to ungrab, which has
 *      other side effects.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

void
MKS::ReleaseAllKeys(AbortSlot onAbort, // IN/OPT
                    DoneSlot onDone)   // IN/OPT

{
   mMKSControlClient->ReleaseAllKeys();
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::SetCursorGrabAllowed --
 *
 *      By default the MKS automatically sets the host cursor when we're
 *      grabbed. The "setCursorGrabAllowed" command allows us to disable
 *      this behavior so that the UI can control the host cursor.
 *
 *      Currently this is used to change the host cursor back to an arrow
 *      when the mouse moves outside of a Unity window. Once bug 450598 is
 *      fixed this will no longer be necessary. (Bug #450598 has been closed
 *      as not a bug... what do we do now?)
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

void
MKS::SetCursorGrabAllowed(bool allowed,      // IN
                          AbortSlot onAbort, // IN/OPT
                          DoneSlot onDone)   // IN/OPT
{
   mMKSControlClient->SetCursorGrabAllowed(allowed);
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::SetMainMKSScreenView --
 *
 *      Sets the default MKSScreenView used by this cui::MKS.  Normally this
 *      is not necessary because derived classes usually should override
 *      GetMainMKSScreenView instead.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

void
MKS::SetMainMKSScreenView(MKSScreenView *mksScreenView) // IN: Takes ownership.
{
   mMKSScreenView.reset(mksScreenView);
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::ConnectLocalMKSServer --
 * cui::MKS::ConnectMKSServer --
 * cui::MKS::ConnectVNC --
 *
 *      Connects the viewControl 'client' to the viewControl 'server' in the MKS.
 *      This is asynchronous; the mAttached field will be set when the
 *      connection is made.
 *
 *      * ConnectLocalMKSServer is for local host listening on designate port number.
 *      * ConnectMKSServer connects to the mks process listening on a
 *        named pipe for Windows or a Unix domain socket for other platforms.
 *      * ConnectVNC connects to a generic VNC server.  Spawns the remote MKS
 *        and then requests that it connect to a specified VNC server.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::ConnectLocalMKSServer(int port,                      // IN
                           MKSControlLibAuthToken *token, // IN
                           AbortSlot onAbort,             // IN
                           DoneSlot onDone)               // IN
{
   ASSERT(mMKSControlClient.get() != NULL);

   mMKSControlClient->ConnectToLocalHost(
      port, token, sigc::bind(sigc::mem_fun(this, &MKS::OnMKSControlConnectAbort), onAbort),
      sigc::bind(sigc::mem_fun(this, &MKS::OnMKSControlConnectDone), onDone));
}

void
MKS::ConnectMKSServer(const utf::string &mksPipeName, // IN
                      MKSControlLibAuthToken *token,  // IN/OPT
                      AbortSlot onAbort,              // IN
                      DoneSlot onDone)                // IN
{
   ASSERT(mMKSControlClient.get() != NULL);

   mMKSControlClient->ConnectToMKS(
      mksPipeName, token, sigc::bind(sigc::mem_fun(this, &MKS::OnMKSControlConnectAbort), onAbort),
      sigc::bind(sigc::mem_fun(this, &MKS::OnMKSControlConnectDone), onDone));
}

void
MKS::ConnectVNC(const utf::string &hostname,     // IN
                unsigned int port,               // IN
                const EncryptedString &password, // IN
                AbortSlot onAbort,               // IN/OPT
                DoneSlot onConnectDone)          // IN/OPT
{
   Panic(LOGPFX "vm ticketing is not expected with this product: %s\n", ProductState_GetName());
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::OnMKSStalled --
 *
 *      The MKS thread may stop running its normal poll loop, which means that
 *      the MKS will stop processing new events and the UI will fail to connect
 *      to the MKS.
 *      When this happens, OnMKSStalled should be called to inform the
 *      MKS object so that it can try to reconnect when the MKS
 *      resumes from stalling.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::OnMKSStalled(bool stalled) // IN
{
   mIsMKSStalled = stalled;
   if (!mIsMKSStalled) {
      SetAttached(mWantToAttach);
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::OnMKSControlConnectDone --
 *
 *      Invoked when viewControl successfully connects. This will set an
 *      internal state flag, apply preferences and other configurations,
 *      and then call the additional "done" slot.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::OnMKSControlConnectDone(DoneSlot onDone) // IN
{
   LOG(5, LOGPFX "cui::MKS::OnMKSControlConnectDone: MKS connected\n");
   onDone();
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::OnMKSControlConnectAbort --
 *
 *      Invoked when viewControl fails to connect. This will set an
 *      internal state flag, and then call the additional "abort" slot.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::OnMKSControlConnectAbort(bool canceled,      // IN
                              const Error &error, // IN
                              AbortSlot onAbort)  // IN
{
   Warning(LOGPFX "cui::MKS::OnMKSControlConnectAbort: "
                  "MKS failed to connect (%s).\n",
           error.what());
   onAbort(canceled, error);
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::ApplyPrefs --
 *
 *      Apply all MKS Preferences, including bool types, GamingMouseMode.
 *      Should be called when the viewControl client is first connected to server.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::ApplyPrefs(void)
{
   if (mAttached) {
      LOG(10, LOGPFX "cui::MKS::ApplyPrefs using viewControl\n");
      mMKSControlClient->SetPreference(VIEWCONTROL_PREF_ALLOW_KEYBOARD_UNGRAB, allowKeyboardUngrab)
         ->SetPreference(VIEWCONTROL_PREF_ALLOW_MOTION_GRAB, grabOnMouseEnter)
         ->SetPreference(VIEWCONTROL_PREF_UNGRAB_IF_OBSCURED, ungrabIfObscured)
         ->SetPreference(VIEWCONTROL_PREF_ALLOW_GRAB_ON_KEY_PRESS, grabOnKeyPress)
         ->SetPreference(VIEWCONTROL_PREF_ALLOW_GRAB_ON_MOUSE_CLICK, grabOnMouseClick)
         ->SetPreference(VIEWCONTROL_PREF_HIDE_CURSOR_ON_UNGRAB, hideCursorOnUngrab)
         ->SetPreference(VIEWCONTROL_PREF_ALLOW_MOTION_UNGRAB, ungrabOnMouseLeave)
         ->SetGamingMouseMode(GamingMouseModeToMKSControl(gamingMouseMode));
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * GamingMouseModeToMKSControl --
 *
 *      Map the old GamingMouseMode enum type to MKSControlLibGamingMouseMode
 *      for viewControl.
 *
 * Results:
 *      Corresponding MKSControlLibGamingMouseMode value.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

static MKSControlLibGamingMouseMode
GamingMouseModeToMKSControl(GamingMouseMode gamingMouseMode) // IN
{
   switch (gamingMouseMode) {
   case MOUSE_AUTOMATIC:
      return VIEWCONTROL_MOUSE_DYNAMIC;
   case MOUSE_RELATIVE:
      return VIEWCONTROL_MOUSE_GAMING;
   case MOUSE_ABSOLUTE:
      return VIEWCONTROL_MOUSE_ABSOLUTE;
   }
   NOT_IMPLEMENTED();
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::GetWin32MKSIntegrityLevel --
 *
 *    On Windows hosts, MKS processes running at different integrity levels
 *    can not work together, because the mouse / keyboard hook functions in
 *    MKS processes are restricted by the UIPI mechanism.
 *
 * Results:
 *      Whether the integrity level value is valid or not.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

bool
MKS::GetWin32MKSIntegrityLevel(uint32 &levelOut) // OUT
   const
{
   return mMKSControlClient->GetWin32MKSIntegrityLevel(levelOut);
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::OnVNCServerErrorChanged --
 *
 *    Called when the VNC server error has changed.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::OnVNCServerErrorChanged(int asyncSocketError) // IN
{
   if (mVNCLastError != asyncSocketError) {
      mVNCLastError = asyncSocketError;
      vncLastErrorChanged.emit();
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::SetVNCServerSetting --
 *
 *    Set VNC server settings.
 *
 * Results:
 *      True is the message is sent, FALSE otherwise.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

bool
MKS::SetVNCServerSetting(Bool enabled,      // IN
                         uint16 port,       // IN
                         const uint32 *key, // IN
                         uint32 keyLen)     // IN
{
   if (mAttached) {
      mVNCLastError = 0;
      mMKSControlClient->EnableVNCServer(enabled, port, key, keyLen);
      vncSettingChanged.emit(enabled, port, key, keyLen);
      return true;
   }
   return false;
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::SetMacOSEventTapUIPID --
 *
 *    Set the pid of UI process.
 *
 * Results:
 *    True is the message is sent, FALSE otherwise.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

bool
MKS::SetMacOSEventTapUIPID(uint32 pid) // IN
{
   if (mAttached) {
      mMKSControlClient->SetMacOSEventTapUIPID(pid);
      return true;
   }
   return false;
}


/*
 *-----------------------------------------------------------------------------
 *
 * cui::MKS::GetDisallowedThumbprintTypes --
 *
 *    Get the disallowed thumbprint types. The default list should be changed
 *    with care, in order to maintain backwards compatibility with ESX.
 *
 * Results:
 *    returns a vector of thumbprint types.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

std::vector<utf::string>
MKS::GetDisallowedThumbprintTypes() const
{
   // TODO add "sha1" to default when SHA256_Mode FSS is enabled in ESX.
   utf::string types = utf::CopyAndFree(Preference_GetString("", "pref.disallowedThumbprintTypes"));
   return types.split(",");
}


} /* namespace cui */
