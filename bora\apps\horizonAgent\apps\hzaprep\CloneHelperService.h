/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * Clone Helper Service
 * ICAgent interacts with this service.
 */

#pragma once

#include "serviceprep.h"
#include <mfwService.h>


class CloneHelperService : public service {
public:
   /*
    * cloneServiceOperation inner class
    * This is the external command interface
    */
   class cloneServiceOperation : public serviceOperation {
   private:
      CloneHelperService *mTheCloneSvc;

   public:
      cloneServiceOperation(CloneHelperService *serviceInstance) { mTheCloneSvc = serviceInstance; }

      MFW_MESSAGE_MAP_BEGIN_TRACE(CloneHelperService::cloneServiceOperation)
      MFW_OPERATION_BOOL(HINT_CLONE_HELPER)
      mTheCloneSvc->prepClone(MFW_PARAMS(), MFW_RESP());
      MFW_MESSAGE_MAP_END()
   };

   static WorkItem *createOperationInstance(void *serviceInstance)
   {
      return new CloneHelperService::cloneServiceOperation(
         reinterpret_cast<CloneHelperService *>(serviceInstance));
   }

private:
   ServicePrep mSvcPrep;

   bool prepClone(PropertyBag &params, PropertyBag &response);
};
