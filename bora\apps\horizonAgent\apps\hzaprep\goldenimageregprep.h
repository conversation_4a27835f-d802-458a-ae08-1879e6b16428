/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * goldenimageregprep.h
 *
 *      This class implements functions that are specific to golden image
 *      registration.
 *
 */


#pragma once
#include "baseprep.h"

#define KEYPAR_PH_SIGN L"PHs"

class GoldenImageRegPrep : public BasePrep {
public:
   GoldenImageRegPrep();
   ~GoldenImageRegPrep();
   bool registerGoldenImage(const wstr &brokerFQDN);

private:
   const wstr mPHsKeyPairName = KEYPAR_PH_SIGN;
   bool mRegistration;
};
