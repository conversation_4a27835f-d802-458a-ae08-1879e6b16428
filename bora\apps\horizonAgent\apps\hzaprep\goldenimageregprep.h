/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * goldenimageregprep.h
 *
 *      This class implements functions that are specific to golden image
 *      registration.
 *
 */


#pragma once
#include "baseprep.h"

class GoldenImageRegPrep : public BasePrep {
public:
   GoldenImageRegPrep();
   ~GoldenImageRegPrep();
   bool registerGoldenImage(wstr brokerFQDN);

private:
   const wstr mPHsKeyPairName = L"PHs";
   bool mRegistration;
};
