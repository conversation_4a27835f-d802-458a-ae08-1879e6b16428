# Copyright (c) Omnissa, LLC. All rights reserved.
# This product is protected by copyright and intellectual property laws in the
# United States and other countries as well as by international treaties.
# -- Omnissa Restricted

import vmware
import os

Import("env_opts")

vncName = "viewClientGraphicsTest"

env = vmware.LookupEnv("viewClient")

env["VIEWCLIENTTEST"] = True

##### Link with googletest #####
#
# Normally gtest is linked dynamically by products that use it.
# However, this test will be run on Jenkins and static linkage
# simplifies the run process, removing the reliance on looking
# in cayman directories for gtest.dll which change every so often.
env.LoadTool("gtest", linkDynamic=True)

env.Append(
    CPPDEFINES={
        "VIEWCLIENTTEST": None,
    },
    CPPPATH=[
        "#bora/apps/horizonrxtest/unitTest/lib",
        "#bora/public",
        "#bora/lib/public",
        "#bora/lib/vnc",
        "#bora/apps/rde/viewClient",
        "#bora/apps/rde/viewClient/win32",
    ],
)

# vncLibs for all platforms
vncLibs = [
    "asyncsocket",
    "blastSockets",
    "blastCodec",
    "bufferRegion",
    "cityhash",
    "config",
    "connect",
    "coreDump",
    "crypto",
    "d3des",
    "dict",
    "dynxdr",
    "err",
    "esArray",
    "file",
    "guestRpc",
    "hashMap",
    "hstree",
    "image",
    "keyboard",
    "lfqueue",
    "lock",
    "log",
    "mempool",
    "misc",
    "panic",
    "poll",
    "pollDefault",
    "productState",
    "raster",
    "rbtree",
    "rectangle",
    "region",
    "runtime",
    "sig",
    "slab",
    "sound",
    "soundlib",
    "ssl",
    "string",
    "sync",
    "thread",
    "udpfec",
    "udpProxy",
    "unicode",
    "unityWindowTracker",
    "url",
    "user",
    "version",
    "uuid",
    "vdplib",
    "viewControl",
    "vnc",
    "vvclib",
    "win32tsf",
]

if vmware.Host().IsWindows():
    vncLibs += [
        "kbdlayoutid",
        "registrywin32",
        "trapapi",
        "win32auth",
        "wmi",
    ]
    env.LoadTool(
        [
            "msvcrt",
            "presentmon",
        ]
    )
    env.Append(
        CCFLAGS=[
            # Disable Inline Function Expansion, so that we can
            # hook/mock under Release compilation
            "/Ob0",
            "-bigobj",
        ],
        SHLIBFLAGS=[
            # Disable MSVC's ICF (Identical Code Folding) optimization
            # to prevent inline hook issues under Release compilation
            "/OPT:NOICF",
        ],
        LIBS=[
            "advapi32.lib",
            "kernel32.lib",
            "ole32.lib",
            "oleaut32.lib",
            "psapi.lib",
            "shell32.lib",
            "user32.lib",
            "Dbghelp.lib",
        ],
        LINKFLAGS=[
            "/SUBSYSTEM:CONSOLE",
        ],
    )

e = vmware.Executable(vncName, env=env)
e.addStaticLibs("vmlibs", vncLibs)
e.addGlobalStaticLibs(
    [
        "vncReplay",
    ]
)

e.addSubdirs(
    [
        "apps/rde/viewClient",
        "apps/rde/viewClient/win32",
        "apps/rde/viewClient/tests/win32",
        "apps/lib/hznprotect",
        "lib/decoder",
    ],
    tree="bora",
)

# rxUnitTestLib.py should be added before real test component scons
rxUnitTestLibNodeName = "rxUnitTestLib"
rxUnitTestLibNode = vmware.LookupNode(rxUnitTestLibNodeName, host=env.Host().Name())

rxTestLibDir = rxUnitTestLibNode[0].dir.abspath
rxTestLibName = rxUnitTestLibNodeName
if vmware.Host().IsWindows():
    # Linking on Windows requires the .lib extension.
    rxTestLibName += ".lib"

env.Append(
    STATICLIBPATH={rxTestLibName: rxTestLibDir},
    STATICLIBS=[rxTestLibName],
    LIBPATH=[rxTestLibDir],
)

node = e.createProgramNode()
vmware.RegisterNode(node, vncName)
vmware.RegisterEnv(vncName, e.GetEnv())
vmware.Alias(vncName + "-build", node)
