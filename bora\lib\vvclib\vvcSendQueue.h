/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */


/*
 * vvcSendQueue.h
 *
 * View Virtual Channel queue and prioritorisation
 *
 */


#ifndef _VVCQUEUE_H
#define _VVCQUEUE_H


#include "vvclibInt.h"

#ifdef __cplusplus
extern "C" {
#endif


#define VVC_PSEUDO_PACKET_LEN_DEFAULT 128
#define VVC_PSEUDO_PACKET_LEN_CFG_KEY "pseudoPacketLength"

#define VVC_PSEUDO_PACKET_LEN_HIGH_BW_DEFAULT 1024
#define VVC_PSEUDO_PACKET_LEN_HIGH_BW_CFG_KEY "pseudoPacketLengthHighBw"


#define VVC_MAX_REMAINING_MSG_LENGTH_CFG_KEY "maxRemainingMsgLength"
#define VVC_MAX_REMAINING_MSG_LENGTH_DEFAULT 0


#define VVC_FIXED_TIMER_RES_CFG_KEY "fixedTimerRes"
#define VVC_FIXED_TIMER_RES_DEFAULT 0

#define VVC_MIN_TIMER_RES_CFG_KEY "minTimerRes"
#define VVC_MIN_TIMER_RES_DEFAULT_AGENT 0
#define VVC_MIN_TIMER_RES_DEFAULT_CLIENT 10

#define REQUIRED_PROBE_MESSAGE_RATE_MULTIPLIER 4

#define VVC_CACHED_BW_EXPIRY_SEC 2

// Type of Control Message to Queue
typedef enum {
   VvcDefaultCtrlMsg,
   VvcBweCtrlMsg,
   VvcSessionCloseCtrlMsg

} VvcCtrlMsgTypeToQueue;

Bool VvcQueueMessage(VvcChannel *channel, uint8 *buf, size_t len, Bool ctrlMsg,
                     VvcCtrlMsgTypeToQueue ctrlMsgType, void *msgClientData, uint32 *msgId);

Bool VvcQueueMessageToSendTree(VvcMsg *msg);

Bool VvcQueueChannelMessagesToSendTree(VvcChannel *channel);

void VvcPurgeSendQueue(VvcChannel *channel);

void VvcDispatchSendQueues(VvcSession *session, VvcDispatchSendTrigger trigger);

void VvcDoDispatchSendQueues(VvcSession *session, VvcDispatchSendTrigger trigger);

void VvcDeferredDispatchSendQueues(void *clientData);

void VvcDeferredDispatchSendQueuesPeriodic(void *clientData);

Bool VvcInitDispatchSendPoll(VvcSession *session);

void VvcUninitDispatchSendPoll(VvcSession *session);

void VvcStopDispatchSendPoll(VvcSession *session);

void VvcDispatchSendPollWake(VvcSession *session, VvcDispatchSendPollCmd cmd);

void VvcPurgeSendTree(VvcSession *session);

VvcMsg *VvcPeekNextMessage(VvcSession *session);

void VvcPurgeMptSendQueue(VvcChannel *channel);

int VvcSendMPTAcks(VvcSession *session);

void VvcDispatchInflightMessagesSendCb(VvcSession *session);

double VvcGetRawChannelMaxBw(VvcSession *session, double nowMS, VvcNetworkStats *rawNetworkStats);

#ifdef __cplusplus
}
#endif

#endif // _VVCQUEUE_H
