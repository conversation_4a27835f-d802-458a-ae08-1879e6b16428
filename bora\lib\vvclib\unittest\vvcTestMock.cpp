/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * vvcTestMock.cpp --
 *
 *      VVC unit test module using vMocks, to be run and loaded using Horizon
 *      UT framework.
 *
 *      Since udpfec is also linked in vvclib, it is possible to mock and
 *      test udpfec library in this test module.
 *
 *      To build and run the tests please visit the page
 *      https://omnissa.atlassian.net/wiki/spaces/BNDT/pages/75629237/UT-Networking
 */

#include "utMock.h"
#include "dictionary.h"
#include "hashMap.h"
#include "log.h"
#include "preference.h"
#include "util.h"
#include "userlock.h"
#include "vdpPlugin.h"
#include "vvclibInt.h"
#include "vvcPerfPriv.h"
#include "vvcTestMock.h"

using namespace testing;

VvcInstanceHandle TestVvc::instanceHandle;


/*
 *-----------------------------------------------------------------------------
 *
 * TestVvc::SetUpTestCase --
 *
 *      SetUp for the entire test suite.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

void
TestVvc::SetUpTestCase()
{
   // Search for any user preference or config files
   Preference_Init();

   InitLog("TestVvc");

   Preference_Log();

   char instanceName[] = "TestVvcUTMain";
   VvcInstanceBackend instanceBe{nullptr};

   /*
    * Initialize vvclib to get a valid gInstanceMain for the tests.
    *
    * Note: Currently this new vvc initialization is shared among all test
    *       cases. If a new initialization of vvc is needed for a specific
    *       test case we will need to refactor the code to accomplish both.
    */
   VVCLIB_Init(VVC_INSTANCE_MAIN, nullptr, instanceName, &instanceBe, nullptr, &instanceHandle);
}


/*
 *-----------------------------------------------------------------------------
 *
 * TestVvc::TearDownTestCase --
 *
 *      TearDown for the entire test suite.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

void
TestVvc::TearDownTestCase()
{
   VVCLIB_Uninit(instanceHandle);
}


/*
 *-----------------------------------------------------------------------------
 *
 * TestVvc::OuputLineSeparator --
 *
 *      Ouput a line separator on std::cout to separate test cases.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

void
TestVvc::OuputLineSeparator(std::ostream &stream /*=std::cout*/, uint32 length /*=80*/,
                            char character /*='='*/)
{
   stream << std::string(length, character) << std::endl;
}


/*
 *-----------------------------------------------------------------------------
 *
 * TestVvc::SetUp --
 *
 *      SetUp for the each test case.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

void
TestVvc::SetUp()
{
   OuputLineSeparator();
}


/*
 *-----------------------------------------------------------------------------
 *
 * TestVvc::TearDown --
 *
 *      TearDown for the each test case.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

void
TestVvc::TearDown()
{
   OuputLineSeparator();
}


/*
 *-----------------------------------------------------------------------------
 *
 * TestVvc::InitLogImpl --
 *
 *      Implementation of initializing vvc log.
 *
 * Results:
 *      true on success, otherwise false.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

bool
TestVvc::InitLogImpl(std::string_view testName)
{
   Dictionary *dict = Dictionary_Create();

   if (!dict) {
      printf("Failed to create dictionary.\n");
      return false;
   }

   /*
    * Create log files using the automatic file creation abilities of the
    * Log Facility. The log files created this way all end with "-PID.log"
    * where the PID is that the program creating the log file (this code).
    */
   char suffix[16];
   std::string base{"UT-"};

   base += testName.data();
   Str_Sprintf(suffix, sizeof suffix, base.c_str());

   Dict_SetString(dict, (base + ".log.fileName").c_str(), "log.config");
   Dict_SetString(dict, suffix, "log.suffix");

   static const int maxLogFiles = 50;
   Dict_SetLong(dict, maxLogFiles, "log.keepOld");

   /*
    * Remove any previous Log Facility outputs. Direct the Log Facility
    * the output specified here.
    */
   Log_Exit();

   LogOutput *output = Log_InitWithFile(base.c_str(), dict, Log_CfgInterface(), TRUE);
   Dictionary_Free(dict);

   if (!output) {
      printf("Unable to proceed without a log file.\n");
   } else {
      Log("%s: Logging to %s\n", base.c_str(), Log_GetOutputFileName(output));
   }

   return (output != nullptr);
}


/*
 *-----------------------------------------------------------------------------
 *
 * TestVvc::InitLog --
 *
 *      Initialize vvc log and set default log level.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

void
TestVvc::InitLog(std::string_view testName, std::string_view logLevel /*="debug"*/)
{
   if (InitLogImpl(testName)) {
      VvcSetLogLevel(const_cast<char *>(logLevel.data()));
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * TestVvc::LogResultToConsole --
 *
 *      Log test results on std:cerr where vvc error log is printed.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

template<typename T>
void
TestVvc::LogResultToConsole(std::string_view testCase, const T &expected, const T &actual,
                            bool addLineSeparator /*=true*/)
{
   std::cerr << testCase << " - Expected: " << expected << ", Actual: " << actual << std::endl;

   if (addLineSeparator) {
      OuputLineSeparator(std::cerr);
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * TestSetPerfCounter --
 *
 * Test for set/get of vvc perf rate counters.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(TestVvc, TestSetPerfCounter)
{
   PerfCounterEntry *pce;
   ModulePerfCountersDb *moduleEntry;
   VvclibPerfError ret;
   PerfDbHandle handle;
   double start, end, bw;
   double valueDouble = 0.0;

   // minimal init of moduleEntry
   moduleEntry = (ModulePerfCountersDb *)Util_SafeCalloc(1, sizeof(*moduleEntry));
   moduleEntry->moduleDbRWLock = MXUser_CreateRWLock("vvcPerfCountersDbModuleLock", RANK_LEAF);

   pce = (PerfCounterEntry *)Util_SafeCalloc(1, sizeof(*pce) + sizeof(PerfCounterEntryExtRate64));
   PerfCountersInitValueForType(VALUE_TYPE_DOUBLE, &valueDouble, EXT_RATE_UINT64, pce);

   // dummy inits as vmocks will ignore these params
   handle = 0xC001;

   VMOCK(PerfCountersGetModuleFromModuleMap).OnCall(_, _).WillByDefault(moduleEntry);

   VMOCK(HashMap_Get).OnCall(_, _).WillByDefault(&pce);

   EXPECT_EQ(HashMap_Get(0, 0), &pce);

   /*
    * Bandwidth counters like VvcPerfChannelOutBw maintain moving average of
    * values sampled at constant intervals.
    * VVC perf maintains history of 4 samples, so set 4 values (bits sent) with
    * timestamps, then get the bandwidth rate and verify it is valid for the
    * number of bytes transferred since the oldest sample.
    */
   ret = VvcPerfCountersSetRateValueUint64(gInstanceMain, handle, VvcPerfChannelOutBw, 1200 * 8,
                                           PerfCountersSystemTime());
   start = PerfCountersSystemTime();

   Util_Sleep(1);
   ret = VvcPerfCountersSetRateValueUint64(gInstanceMain, handle, VvcPerfChannelOutBw, 200 * 8,
                                           PerfCountersSystemTime());
   Util_Sleep(1);
   ret = VvcPerfCountersSetRateValueUint64(gInstanceMain, handle, VvcPerfChannelOutBw, 400 * 8,
                                           PerfCountersSystemTime());
   Util_Sleep(1);
   ret = VvcPerfCountersSetRateValueUint64(gInstanceMain, handle, VvcPerfChannelOutBw, 500 * 8,
                                           PerfCountersSystemTime());

   end = PerfCountersSystemTime();
   bw = 1100 * 8 / (end - start);

   ret = VVCLIB_PerfCountersGetValueDouble(handle, VvcPerfChannelOutBw, &valueDouble);

   printf("Outbound bandwidth %lf kbps\n", valueDouble);

   // Verify value fetched from perfDB meets the expected b/w
   EXPECT_LT(fabs(bw - valueDouble), 0.05);

   // cleanup
   free(pce);
   MXUser_DestroyRWLock(moduleEntry->moduleDbRWLock);
   free(moduleEntry);
}


/*
 *-----------------------------------------------------------------------------
 *
 * TestStopAllSessions --
 *
 * Test VVCLIB API - VVCLIB_StopAllSessions.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(TestVvc, TestStopAllSessions)
{
   BlastSessionMgrCallbacks cb{.stopAllSessions = [](int) -> size_t { return 1; }};

   VVCLIB_SetBlastSessionMgrCallbacks(&cb);

   VvcStatus status{VVC_STATUS_ERROR};
   size_t numSessionStopped;

   // Test a successful case
   VDPConnectionResult vdpConnectionResult{VDPCONNECT_DISCONNECT_CRITICAL_PROCESS_TERMINATED};

   EXPECT_EQ(status = VVCLIB_StopAllSessions(vdpConnectionResult, &numSessionStopped),
             VVC_STATUS_SUCCESS);
   LogResultToConsole<VvcStatus>("Test success case", VVC_STATUS_SUCCESS, status);

   // Test invalid vdpConnectionResult
   VDPConnectionResult vdpConnectionResultBak = vdpConnectionResult;
   vdpConnectionResult = VDPCONNECT_NETWORK_UNREACHABLE;
   EXPECT_EQ(status = VVCLIB_StopAllSessions(vdpConnectionResult, &numSessionStopped),
             VVC_STATUS_INVALID_ARGS);
   vdpConnectionResult = vdpConnectionResultBak;
   LogResultToConsole<VvcStatus>("Test invalid vdpConnectionResult", VVC_STATUS_INVALID_ARGS,
                                 status);

   // Test gInstanceMain being nullptr
   VvcInstance *gInstanceMainBak = gInstanceMain;
   gInstanceMain = nullptr;
   EXPECT_EQ(status = VVCLIB_StopAllSessions(vdpConnectionResult, &numSessionStopped),
             VVC_STATUS_INVALID_STATE);
   gInstanceMain = gInstanceMainBak;
   LogResultToConsole<VvcStatus>("Test gInstanceMain being nullptr", VVC_STATUS_INVALID_STATE,
                                 status);

   // Test an invalid role for gInstanceMain
   gInstanceMain->flags |= VVC_INSTANCE_ROLE_CLIENT;
   EXPECT_EQ(status = VVCLIB_StopAllSessions(vdpConnectionResult, &numSessionStopped),
             VVC_STATUS_ERROR);
   gInstanceMain->flags &= ~VVC_INSTANCE_ROLE_CLIENT;
   LogResultToConsole<VvcStatus>("Test invalid role for gInstanceMain", VVC_STATUS_ERROR, status);

   // Test callback 'stopAllSessions' is not set
   BlastSessionMgr_StopAllSessionsCb stopAllSessionCb = cb.stopAllSessions;
   cb.stopAllSessions = nullptr;
   VVCLIB_SetBlastSessionMgrCallbacks(&cb);
   EXPECT_EQ(status = VVCLIB_StopAllSessions(vdpConnectionResult, &numSessionStopped),
             VVC_STATUS_ERROR);
   cb.stopAllSessions = stopAllSessionCb;
   LogResultToConsole<VvcStatus>("Test callback 'stopAllSessions' is not set", VVC_STATUS_ERROR,
                                 status, false);
}
