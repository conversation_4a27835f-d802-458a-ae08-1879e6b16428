/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

#include "stdafx.h"

#include <atomic>
#include <thread>
#include <chrono>

#include "utilWinHttpRequest.h"
#include "TestLaunchProcess.h"

#include "util.h"


static std::atomic<bool> g_should_wait_for_crl{false};

/*
 * LocalhostTest.cpp
 *
 *    Test cases that require a server to be running on localhost.
 *
 */

class LocalhostTest : public ::testing::Test {
public:
   LocalhostTest() {};

   ~LocalhostTest() {};

   void SetUp() {}

   void TearDown() {}

protected:
   std::string mServerEndpoint = "https://horizon-core-agent-test.com:4443";
   std::string mWrongHostnameEndpoint = "https://127.0.0.1:4443";
   std::string mRootThumbprint = "EC:0C:62:A2:CB:38:5F:0D:96:11:D5:AC:7C:54:75:8A:F2:5B:E1:63:88:"
                                 "71:52:9A:4E:90:F0:8C:BF:0E:2D:95";
   std::string mInterThumbprint = "45:77:31:0E:C8:4E:0A:96:66:15:07:04:5C:01:DB:E3:5B:4D:CD:7A:CC:"
                                  "FD:D3:09:F0:9D:71:96:37:50:39:53";
   std::string mLeafThumbprint = "07:FB:E9:CF:50:D0:20:82:77:05:FB:69:FE:7E:CB:DB:93:F1:B5:69:85:"
                                 "49:CB:37:D8:5B:55:A2:B5:08:62:C8";
   std::string mWrongThumbprint1 = "AC:0C:62:A2:CB:38:5F:0D:96:11:D5:AC:7C:54:75:8A:F2:5B:E1:63:88:"
                                   "81:52:9A:4E:90:F0:8C:BF:0E:2D:95";
   std::string mWrongThumbprint2 = "BC:0C:62:A2:CB:38:5F:0D:96:11:D5:AC:7C:54:75:8A:F2:5B:E1:63:88:"
                                   "91:52:9A:4E:90:F0:8C:BF:0E:2D:95";

   std::string mExpiredServerEndpoint = "https://horizon-core-agent-exp.com:1443";

   std::string mLeafAEndpoint = "https://hzagent-leafA.com:5443";
   std::wstring mStartLeafAServer =
      LR"(python -u http-server.py --port=5443 --cert="leafA.crt" --key="leafA.key")";
   std::string mLeafAEndpointLocalhost = "https://127.0.0.1:5443";

   std::string mLeafBEndpoint = "https://hzagent-leafB.com:3443";
   std::wstring mStartLeafBServer =
      LR"(python -u http-server.py --port=3443 --cert="leafB.crt" --key="leafB.key")";

   std::string mLeafCEndpoint = "https://hzagent-leafC.com:2443";
   std::wstring mStartLeafCServer =
      LR"(python -u http-server.py --port=2443 --cert="leafC.crt" --key="leafC.key")";

   std::wstring mStartCrlServer1 = LR"(python -u -m http.server 8887)";
   std::wstring mStartCrlServer2 = LR"(python -u -m http.server 8886)";
   std::wstring mStartCrlServer3 = LR"(python -u -m http.server 8885)";

   std::string mExpiredEndpoint = "https://horizon-core-agent-exp.com:1443";
   std::wstring mStartExpiredServer =
      LR"(python -u http-server.py --port=1443 --cert="horizon-core-agent-exp.crt" --key="horizon-core-agent-test.key")";

   std::wstring mWorkingDir = std::filesystem::current_path().wstring();


   /*
    * CRLServer*ReceivedRequest --
    *
    *    WinHTTP has a behavior where it caches all the CRL requests it receives in-memory.
    *    Clearing the crl cache via certutil only takes effect when the process restarts.
    *    With that in mind, we only need to check if the CRL server was reached once in the entirety
    *    of a test run.
    *
    *    But we also need to support the ability to run a single test and still verify that the
    *    CRL server is hit.
    */
   bool CRLServer1ReceivedRequest(const std::wstring &crl1LogPath)
   {
      static bool mReceivedCrl1 = false;
      if (mReceivedCrl1) {
         return true;
      }

      mReceivedCrl1 = wutil::ServerReceivedRequest(crl1LogPath, "hzagent-1.crl");
      return mReceivedCrl1;
   }

   bool CRLServer2ReceivedRequest(const std::wstring &crl2LogPath)
   {
      static bool mReceivedCrl2 = false;
      if (mReceivedCrl2) {
         return mReceivedCrl2;
      }

      mReceivedCrl2 = wutil::ServerReceivedRequest(crl2LogPath, "hzagent-2.crl");
      return mReceivedCrl2;
   }

   bool CRLServer3ReceivedRequest(const std::wstring &crl3LogPath)
   {
      static bool mReceivedCrl3 = false;
      if (mReceivedCrl3) {
         return true;
      }

      mReceivedCrl3 = wutil::ServerReceivedRequest(crl3LogPath, "hzagent-3.crl");
      return mReceivedCrl3;
   }


   /*
    * WaitForCRLCacheIfNeeded
    *
    *    Windows has a CRL cache. If WinHTTP fails to get the CRL, it will cache the failure for a
    *    time that is specified by the following registry key:
    *
    * HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Cryptography\OID\EncodingType
    * 0\CertDllCreateCertificateChainEngine\Config\ChainUrlRetrievalTimeoutMilliseconds
    *
    *    Most machines won't have this key set, so the default is 15 seconds.
    *    We're waiting for 16 seconds to add a buffer as to avoid any timing issues.
    */

   void WaitForCRLCacheIfNeeded()
   {
      if (!g_should_wait_for_crl) {
         return;
      }

      GTEST_LOG_(INFO) << "Waiting 16 seconds for CRL cache to expire...";
      std::this_thread::sleep_for(std::chrono::seconds(16));
      g_should_wait_for_crl = false;
   }
};


#define START_CRL_SERVER1()                                                                        \
   PROCESS_INFORMATION crl1Process = {0};                                                          \
   HANDLE hCrl1File = NULL;                                                                        \
   auto crl1LogPath = mWorkingDir + L"\\" + testName + L"-crl1.log";                               \
   ASSERT_TRUE(ProcUtil::LaunchProcessWithStdOut(mStartCrlServer1, crl1LogPath, crl1Process,       \
                                                 hCrl1File, false));


#define START_CRL_SERVER2()                                                                        \
   PROCESS_INFORMATION crl2Process = {0};                                                          \
   HANDLE hCrl2File = NULL;                                                                        \
   auto crl2LogPath = mWorkingDir + L"\\" + testName + L"-crl2.log";                               \
   ASSERT_TRUE(ProcUtil::LaunchProcessWithStdOut(mStartCrlServer2, crl2LogPath, crl2Process,       \
                                                 hCrl2File, false));

#define START_CRL_SERVER3()                                                                        \
   PROCESS_INFORMATION crl3Process = {0};                                                          \
   HANDLE hCrl3File = NULL;                                                                        \
   auto crl3LogPath = mWorkingDir + L"\\" + testName + L"-crl3.log";                               \
   ASSERT_TRUE(ProcUtil::LaunchProcessWithStdOut(mStartCrlServer3, crl3LogPath, crl3Process,       \
                                                 hCrl3File, false));


#define STOP_CRL_SERVER1()                                                                         \
   ProcUtil::CloseProcess(crl1Process);                                                            \
   if (hCrl1File != NULL) {                                                                        \
      CloseHandle(hCrl1File);                                                                      \
   }


#define STOP_CRL_SERVER2()                                                                         \
   ProcUtil::CloseProcess(crl2Process);                                                            \
   if (hCrl2File != NULL) {                                                                        \
      CloseHandle(hCrl2File);                                                                      \
   }

#define STOP_CRL_SERVER3()                                                                         \
   ProcUtil::CloseProcess(crl3Process);                                                            \
   if (hCrl3File != NULL) {                                                                        \
      CloseHandle(hCrl3File);                                                                      \
   }


/*
 * LocalhostTest::testUnknownCA --
 *
 *    The localhost server is using a self-signed certificate. This test verifies that the request
 *    will fail because the CA is not trusted.
 */

TEST_F(LocalhostTest, testUnknownCA)
{
   std::string getUrl = mServerEndpoint;
   std::string getResponse;

   WinHttpRequest req;
   EXPECT_FALSE(req.getSync(getUrl, getResponse));
   EXPECT_TRUE(getResponse.empty());
}


/*
 * LocalhostTest::testHostnameMismatch --
 *
 *    The localhost server's certificate does not match the hostname. This test verifies that the
 *    request will fail, even if we trust the root CA.
 */

TEST_F(LocalhostTest, testHostnameMismatch)
{
   std::string getUrl = mWrongHostnameEndpoint + "/ping";

   std::string getResponse;
   WinHttpRequest req;
   req.setCAThumbprint(mRootThumbprint);
   EXPECT_FALSE(req.getSync(getUrl, getResponse));
   EXPECT_TRUE(getResponse.empty());
}


/*
 * LocalhostTest::testHostnameMismatchIgnore --
 *
 *    The localhost server's certificate does not match the hostname. This test verifies that the
 *    request will succeed if we trust the CA and configure it to ignore hostname mismatches.
 */

TEST_F(LocalhostTest, testHostnameMismatchIgnore)
{
   std::string getUrl = mWrongHostnameEndpoint + "/ping";

   std::string getResponse;
   WinHttpRequest req;
   req.setCAThumbprint(mRootThumbprint);
   req.ignoreWrongHostCertError();
   EXPECT_TRUE(req.getSync(getUrl, getResponse));
   EXPECT_FALSE(getResponse.empty());
}


/*
 * LocalhostTest::testIntermediateTp --
 *
 *    The localhost server's certificate is using a self-signed certificate. This test verifies that
 *    the request will succeed if we trust the intermediate CA.
 */

TEST_F(LocalhostTest, testIntermediateTp)
{
   std::string getUrl = mServerEndpoint + "/ping";

   std::string getResponse;
   WinHttpRequest req;
   req.setCAThumbprint(mInterThumbprint);
   EXPECT_TRUE(req.getSync(getUrl, getResponse));
   EXPECT_FALSE(getResponse.empty());
}


/*
 * LocalhostTest::testLeafTp --
 *
 *    The localhost server's certificate is using a self-signed certificate. This test verifies that
 *    the request will succeed if we trust the leaf certificate.
 */

TEST_F(LocalhostTest, testLeafTp)
{
   std::string getUrl = mServerEndpoint + "/ping";

   std::string getResponse;
   WinHttpRequest req;
   req.setCAThumbprint(mLeafThumbprint);
   EXPECT_TRUE(req.getSync(getUrl, getResponse));
   EXPECT_FALSE(getResponse.empty());
}


/*
 * LocalhostTest::testIgnoreAllCertErrors --
 *
 *    Ignore all certificate errors. The request should succeed.
 */

TEST_F(LocalhostTest, testIgnoreAllCertErrors)
{
   std::string getUrl = mWrongHostnameEndpoint + "/ping";
   std::string getResponse;

   WinHttpRequest req;
   req.ignoreAllCertErrors();
   EXPECT_TRUE(req.getSync(getUrl, getResponse));
}


/*
 * LocalhostTest::testUntrustedRootFailMismatch --
 *
 *    Give the wrong thumbprint to the request. The request should fail.
 */

TEST_F(LocalhostTest, testUntrustedRootFailMismatch)
{
   std::string getUrl = mServerEndpoint + "/ping";

   std::string getResponse;
   WinHttpRequest req;
   req.setCAThumbprint(mWrongThumbprint1);
   EXPECT_FALSE(req.getSync(getUrl, getResponse));
}


/*
 * LocalhostTest::testUntrustedRootFailMismatch --
 *
 *    Give the wrong thumbprints to the request. The request should fail.
 */

TEST_F(LocalhostTest, testUntrustedRootMultipleTpFail)
{
   std::string getUrl = mServerEndpoint + "/ping";
   std::string getResponse;

   std::vector<std::string> caTrustStore{mWrongThumbprint1, mWrongThumbprint2};

   WinHttpRequest req;
   req.setCATrustStore(caTrustStore);
   EXPECT_TRUE(!req.getSync(getUrl, getResponse));
}


/*
 * LocalhostTest::testUntrustedRootMultipleTpSucceed1 --
 *
 *    Give one wrong thumbprint and one correct thumbprint to the request. The request should
 *    succeed.
 */

TEST_F(LocalhostTest, testUntrustedRootMultipleTpSucceed1)
{
   std::string getUrl = mServerEndpoint + "/ping";
   std::string getResponse;

   std::vector<std::string> caTrustStore{mRootThumbprint, mWrongThumbprint1};

   WinHttpRequest req;
   req.setCATrustStore(caTrustStore);
   EXPECT_TRUE(req.getSync(getUrl, getResponse));
   EXPECT_STREQ(getResponse.c_str(), "pong");
}


/*
 * LocalhostTest::testUntrustedRootMultipleTpSucceed2 --
 *
 *    Give one wrong thumbprint and one correct thumbprint to the request. The request should
 *    succeed.
 */

TEST_F(LocalhostTest, testUntrustedRootMultipleTpSucceed2)
{
   std::string getUrl = mServerEndpoint + "/ping";
   std::string getResponse;

   std::vector<std::string> caTrustStore{mWrongThumbprint2, mRootThumbprint};

   WinHttpRequest req;
   req.setCATrustStore(caTrustStore);
   EXPECT_TRUE(req.getSync(getUrl, getResponse));
   EXPECT_STREQ(getResponse.c_str(), "pong");
}


/*
 * LocalhostTest::testUntrustedRootMultiplePEMFail --
 *
 *    Give a CA trust store in PEM format. The store should contain multiple CA's.
 *    None of the CA's will be the one that signed the server's certificate. The request should
 *    fail.
 */

TEST_F(LocalhostTest, testUntrustedRootMultiplePEMFail)
{
   std::string getUrl = mServerEndpoint + "/ping";
   std::string getResponse;

   std::string localhostPEM;
   std::wstring localhostRoot = mWorkingDir + L"\\mosquitto_broker_ca.crt";
   READ_FILE_INTO_STRING(localhostRoot, localhostPEM, false);
   std::string omnissaPEM;
   std::wstring omnissaRoot = mWorkingDir + L"\\omnissa.crt";
   READ_FILE_INTO_STRING(omnissaRoot, omnissaPEM, false);

   std::string caTrustStore = localhostPEM + omnissaPEM;

   WinHttpRequest req;
   req.setCATrustStore(caTrustStore);
   EXPECT_FALSE(req.getSync(getUrl, getResponse));
}


/*
 * LocalhostTest::testUntrustedRootMultiplePEMSucceed1 --
 *
 *    Give a CA trust store in PEM format. The store should contain multiple CA's.
 *    One of the CA's will be the one that signed the server's certificate. The request should
 *    succeed.
 */

TEST_F(LocalhostTest, testUntrustedRootMultiplePEMSucceed1)
{
   std::string getUrl = mServerEndpoint + "/ping";
   std::string getResponse;

   std::string omnissaPEM;
   std::wstring omnissaRoot = mWorkingDir + L"\\omnissa.crt";
   READ_FILE_INTO_STRING(omnissaRoot, omnissaPEM, false);
   std::string horizonPEM;
   std::wstring horizonRoot = mWorkingDir + L"\\horizon-root.crt";
   READ_FILE_INTO_STRING(horizonRoot, horizonPEM, false);

   std::string caTrustStore = horizonPEM + omnissaPEM;

   WinHttpRequest req;
   req.setCATrustStore(caTrustStore);
   EXPECT_TRUE(req.getSync(getUrl, getResponse));
   EXPECT_STREQ(getResponse.c_str(), "pong");
}


/*
 * LocalhostTest::testUntrustedRootMultiplePEMSucceed1 --
 *
 *    Give a CA trust store in PEM format. The store should contain multiple CA's.
 *    One of the CA's will be the one that signed the server's certificate. The request should
 *    succeed.
 */

TEST_F(LocalhostTest, testUntrustedRootMultiplePEMSucceed2)
{
   std::string getUrl = mServerEndpoint + "/ping";
   std::string getResponse;

   std::string omnissaPEM;
   std::wstring omnissaRoot = mWorkingDir + L"\\omnissa.crt";
   READ_FILE_INTO_STRING(omnissaRoot, omnissaPEM, false);
   std::string horizonPEM;
   std::wstring horizonRoot = mWorkingDir + L"\\horizon-root.crt";
   READ_FILE_INTO_STRING(horizonRoot, horizonPEM, false);

   std::string caTrustStore = omnissaPEM + horizonPEM;

   WinHttpRequest req;
   req.setCATrustStore(caTrustStore);
   EXPECT_TRUE(req.getSync(getUrl, getResponse));
   EXPECT_STREQ(getResponse.c_str(), "pong");
}


/*
 * LocalhostTest::testQueryParamsUrl --
 *
 *    Tests that the server is receiving all our query parameters correctly.
 *    NOTE: The server is designed to just echo the query parameters back to us.
 */

TEST_F(LocalhostTest, testQueryParamsUrl)
{
   std::string getUrl = mServerEndpoint + "/query?myquery=myans&myq=mya";
   std::string getResponse;

   WinHttpRequest req;
   req.setCAThumbprint(mRootThumbprint);
   EXPECT_TRUE(req.getSync(getUrl, getResponse));
   EXPECT_TRUE(!getResponse.empty());
   Json::Reader reader;
   Json::Value emptyObj = Json::objectValue;
   Json::Value root;
   EXPECT_TRUE(reader.parse(getResponse, root));
   ASSERT_TRUE(!root.empty());
   EXPECT_TRUE(root.isMember("myquery"));
   EXPECT_TRUE(root["myquery"] == "myans");
   EXPECT_TRUE(root.isMember("myq"));
   EXPECT_TRUE(root["myq"] == "mya");
}


/*
 * LocalhostTest::testQueryMerge --
 *
 *    Test that when we provide query parameters in a URL and explicitly, the query parameters are
 *    merged properly.
 *    NOTE: The server is designed to just echo the query parameters back to us.
 */

TEST_F(LocalhostTest, testQueryMerge)
{
   std::string getUrl = mServerEndpoint + "/query?myquery=myans&myq=mya";
   std::string getResponse;

   std::multimap<std::string, std::string> queryParams = {{"hello", "world"}, {"lorem", "ipsum"}};

   WinHttpRequest req;
   req.setCAThumbprint(mRootThumbprint);
   req.setQueryParams(queryParams);
   EXPECT_TRUE(req.getSync(getUrl, getResponse));
   EXPECT_TRUE(!getResponse.empty());
   Json::Reader reader;
   Json::Value emptyObj = Json::objectValue;
   Json::Value root;
   EXPECT_TRUE(reader.parse(getResponse, root));
   ASSERT_TRUE(!root.empty());
   EXPECT_TRUE(root.isMember("myquery"));
   EXPECT_TRUE(root["myquery"] == "myans");
   EXPECT_TRUE(root.isMember("myq"));
   EXPECT_TRUE(root["myq"] == "mya");
   for (const auto &[query, val] : queryParams) {
      EXPECT_TRUE(root.isMember(query));
      EXPECT_TRUE(root[query] == val);
   }
}


/*
 * LocalhostTest::testComplexQuery --
 *
 *    Test that we can send complex query parameters to the server and receive them back.
 *    "complex" meaning that they contain special characters.
 */

TEST_F(LocalhostTest, testComplexQuery)
{
   std::string getUrl = mServerEndpoint + "/query?myquery=myans&myq=mya";
   std::string getResponse;

   std::multimap<std::string, std::string> queryParams = {{"he llo==", "world??"},
                                                          {"s!@it*d", "t(m^m)b$t"},
                                                          {",./;'[]\\`-=", "<>?:\"{}|~!@#$%^&*()_"},
                                                          {"redundant", "argument"},
                                                          {"redundant", "otherargument"}};

   WinHttpRequest req;
   req.setCAThumbprint(mRootThumbprint);
   req.setQueryParams(queryParams);
   EXPECT_TRUE(req.getSync(getUrl, getResponse));
   Json::Reader reader;
   Json::Value emptyObj = Json::objectValue;
   Json::Value root;
   EXPECT_TRUE(reader.parse(getResponse, root));
   ASSERT_TRUE(!root.empty());
   for (auto &queryParam : queryParams) {
      EXPECT_TRUE(root.isMember(queryParam.first));
      auto paramIterPair = queryParams.equal_range(queryParam.first);
      for (auto foundIt = paramIterPair.first; foundIt != paramIterPair.second; ++foundIt) {
         std::string value = foundIt->second;
         Json::Value &obj = root[queryParam.first];
         if (obj.isArray()) {
            bool foundValue = false;
            for (int i = 0; static_cast<unsigned int>(i) < obj.size(); ++i) {
               if (value == obj[i].asString()) {
                  foundValue = true;
                  break;
               }
            }
            EXPECT_TRUE(foundValue);
         } else {
            EXPECT_TRUE(value == obj.asString());
         }
      }
   }
}


/*
 * LocalhostTest::testHeaders --
 *
 *    Test that we can send the appropriate headers to the server.
 *    NOTE: The server is designed to just echo the headers back to us.
 */

TEST_F(LocalhostTest, testHeaders)
{
   std::string getUrl = mServerEndpoint + "/headers";
   std::string getResponse;
   // Header keys usually start with uppercase, lowercase only afterwards
   std::map<std::string, std::string> headers = {{"Myheader", "myvalue"},
                                                 {"Myotherheader", "myothervalue"}};

   WinHttpRequest req;
   req.setCAThumbprint(mRootThumbprint);
   req.setHeaders(headers);
   EXPECT_TRUE(req.getSync(getUrl, getResponse));
   Json::Reader reader;
   Json::Value emptyObj = Json::objectValue;
   Json::Value root;
   EXPECT_TRUE(reader.parse(getResponse, root));
   ASSERT_TRUE(!root.empty());
   for (auto &pair : headers) {
      EXPECT_TRUE(root.isMember(pair.first));
      EXPECT_TRUE(root[pair.first] == pair.second);
   }
}


/*
 * LocalhostTest::testPost --
 *
 *    Test that we can POST to the server.
 *    NOTE: The server is designed to return the same body back to us.
 */

TEST_F(LocalhostTest, testPost)
{
   // httpbin/post returns the exact same body back to us if successful
   std::string postUrl = mServerEndpoint + "/post";
   std::string body = "messagebody";
   std::string postResponse;

   WinHttpRequest req;
   req.setCAThumbprint(mRootThumbprint);
   EXPECT_TRUE(req.postSync(HttpRequest::CONTENT_TYPE::PLAIN, postUrl, body, postResponse));
   EXPECT_TRUE(body == postResponse);
}


/*
 * LocalhostTest::testAsyncPost --
 *
 *    Test that we can perform a POST to the server. Tests the asyncrhonous version.
 *    NOTE: The server is designed to return the same body back to us.
 */

TEST_F(LocalhostTest, testAsyncPost)
{
   std::string postUrl = mServerEndpoint + "/post";

   cedar::windows::unique_event asyncPostEvent(CreateEvent(0, FALSE, FALSE, 0));
   ASSERT_TRUE(!!asyncPostEvent);

   std::string body = "messagebody";
   std::string resp;

   WinHttpRequest req;
   req.ignoreAllCertErrors();
   bool res = req.postAsync(HttpRequest::CONTENT_TYPE::JSON, postUrl, body,
                            [&asyncPostEvent, &resp](HttpRequest *req, std::string postResponse) {
                               UNREFERENCED_PARAMETER(req);
                               resp = postResponse;
                               SetEvent(asyncPostEvent.get());
                            });
   EXPECT_TRUE(res);
   EXPECT_EQ(WAIT_OBJECT_0, WaitForSingleObject(asyncPostEvent.get(), 30000));
   EXPECT_TRUE(!resp.empty());
   EXPECT_TRUE(body == resp);
}

/*
 * LocalhostTest::testAsyncPut --
 *
 *    Test that we can perform a PUT to the server. Test an asyncrhonous request.
 *    NOTE: The server is designed to return the same body back to us.
 */

TEST_F(LocalhostTest, testAsyncPut)
{
   std::string putUrl = mServerEndpoint + "/put";
   std::string bodyStr = "messagebody";
   std::vector<char> body(bodyStr.begin(), bodyStr.end());

   cedar::windows::unique_event asyncPutEvent(CreateEvent(0, FALSE, FALSE, 0));
   ASSERT_TRUE(!!asyncPutEvent);

   std::string putResponse;

   WinHttpRequest *req = new WinHttpRequest();
   req->ignoreAllCertErrors();
   EXPECT_TRUE(
      req->putAsync(HttpRequest::CONTENT_TYPE::JSON, putUrl, body, body.size(),
                    [&asyncPutEvent, &putResponse](HttpRequest *req, std::string response) {
                       putResponse = response;
                       SetEvent(asyncPutEvent.get());
                       auto winReq = dynamic_cast<WinHttpRequest *>(req);
                       if (winReq) {
                          winReq->Release();
                       }
                    }));

   EXPECT_TRUE(WaitForSingleObject(asyncPutEvent.get(), 5000) == WAIT_OBJECT_0);

   EXPECT_TRUE(bodyStr == putResponse);
}


/*
 * LocalhostTest::testPutFileSync --
 *
 *    Test that we can perform a PUT to the server from a file.
 *    NOTE: The server is designed to return the same body back to us.
 */

TEST_F(LocalhostTest, testPutFileSync)
{
   std::string putUrl = mServerEndpoint + "/put";

   std::string fileContents = "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc justo "
                              "erat, accumsan at sem a, porttitor venenatis mauris. Proin "
                              "imperdiet.";

   auto testName = gutils::GetTestName();
   auto fileName = mWorkingDir + L"\\" + testName + L".txt";
   WinFile file(fileName);
   ASSERT_TRUE(file.WriteStringToFile(fileContents));

   std::string fileNameA(wstr::to_mstr(fileName.c_str()).c_str());

   std::string putResponse;

   WinHttpRequest req;
   req.ignoreAllCertErrors();
   EXPECT_TRUE(
      req.putSyncFile(HttpRequest::CONTENT_TYPE::PLAIN, putUrl, fileNameA.c_str(), putResponse));
   EXPECT_TRUE(fileContents == putResponse);
}


/*
 * LocalhostTest::testPutFileAsync --
 *
 *    Test that we can perform a PUT to the server from a file. Tests the asyncrhonous version.
 *    NOTE: The server is designed to return the same body back to us.
 */

TEST_F(LocalhostTest, testPutFileAsync)
{
   std::string putUrl = mServerEndpoint + "/put";

   std::string fileContents = "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc justo "
                              "erat, accumsan at sem a, porttitor venenatis mauris. Proin "
                              "imperdiet.";

   auto testName = gutils::GetTestName();
   auto fileName = mWorkingDir + L"\\" + testName + L".txt";
   WinFile file(fileName);
   ASSERT_TRUE(file.WriteStringToFile(fileContents));

   std::string fileNameA(wstr::to_mstr(fileName.c_str()).c_str());

   cedar::windows::unique_event asyncPutEvent(CreateEvent(0, FALSE, FALSE, 0));
   ASSERT_TRUE(!!asyncPutEvent);
   std::string putResponse;

   WinHttpRequest *req = new WinHttpRequest();
   req->ignoreAllCertErrors();
   EXPECT_TRUE(
      req->putAsyncFile(HttpRequest::CONTENT_TYPE::PLAIN, putUrl, fileNameA.c_str(),
                        [&asyncPutEvent, &putResponse](HttpRequest *req, std::string resp) {
                           putResponse = resp;
                           SetEvent(asyncPutEvent.get());
                           auto winReq = dynamic_cast<WinHttpRequest *>(req);
                           if (winReq) {
                              winReq->Release();
                           }
                        }));
   EXPECT_TRUE(WaitForSingleObject(asyncPutEvent.get(), 5000) == WAIT_OBJECT_0);

   EXPECT_TRUE(fileContents == putResponse);
}


/*
 * LocalhostTest::testDelete --
 *
 *    Test that we can perform a DELETE to the server. Test an asyncrhonous request.
 */

TEST_F(LocalhostTest, testDelete)
{
   std::string deleteUrl = mServerEndpoint + "/delete";

   std::string response;

   WinHttpRequest req;
   req.ignoreAllCertErrors();
   EXPECT_TRUE(req.deleteSync(deleteUrl, response));
   EXPECT_TRUE(req.getResponseCode() == 200);
}


/*
 * LocalhostTest::testSyncHeap --
 *
 *    Test that we can safely delete this object immediately after completion.
 */

TEST_F(LocalhostTest, testSyncHeap)
{
   std::string postUrl = mServerEndpoint + "/ping";
   std::string body = "messagebody";
   std::string getResponse;

   WinHttpRequest *req = new WinHttpRequest();
   req->ignoreAllCertErrors();
   EXPECT_TRUE(req->getSync(postUrl, getResponse));
   req->Release();
   req = nullptr;
   EXPECT_TRUE(!getResponse.empty());
   EXPECT_STREQ(getResponse.c_str(), "pong");
}


/*
 * LocalhostTest::testResponseHeaders --
 *
 *    Test that we receive the response headers correctly.
 *    NOTE: The server is designed to convert query parameters to headers and return them back to
 *    us.
 */

TEST_F(LocalhostTest, testResponseHeaders)
{
   std::string headerUrl = mServerEndpoint + "/querytoheaders";

   std::multimap<std::string, std::string> query = {{"qwerty", "abcd1234"}, {"zxcvbn", "09876"}};

   std::string response;

   WinHttpRequest req;
   req.ignoreAllCertErrors();
   req.setQueryParams(query);
   EXPECT_TRUE(req.getSync(headerUrl, response));
   std::string responseHeaders = req.getResponseHeaders();

   for (auto const &[queryparam, queryval] : query) {
      EXPECT_TRUE(responseHeaders.find(queryparam) != std::string::npos);
      EXPECT_TRUE(responseHeaders.find(queryval) != std::string::npos);
   }
}


/*
 * LocalhostTest::testTimeoutSuccess --
 *
 *    Test that even if a request takes a long time to complete, we can still get a response if
 *    we wait long enough.
 */

TEST_F(LocalhostTest, testTimeoutSuccess)
{
   std::string getUrl = mServerEndpoint + "/delay?seconds=9";
   std::string response;

   WinHttpRequest req;
   req.ignoreAllCertErrors();
   req.setTimeout(150);
   ULONGLONG startTime = GetTickCount64();
   EXPECT_TRUE(req.getSync(getUrl, response));
   ULONGLONG endTime = GetTickCount64();
   EXPECT_TRUE(!response.empty());
   ULONGLONG diffTime = endTime - startTime;
   EXPECT_GE(diffTime, 9000ull);
}


/*
 * LocalhostTest::testAsyncCancel --
 *
 *    Test that we can cancel an asynchronous request.
 */

TEST_F(LocalhostTest, testAsyncCancel)
{
   std::string getUrl = mServerEndpoint + "/delay?seconds=5";

   bool callbackTriggered = false;
   WinHttpRequest req;
   req.ignoreAllCertErrors();
   std::string resp;
   EXPECT_TRUE(
      req.getAsync(getUrl, [&callbackTriggered, &resp](HttpRequest *req, std::string getResponse) {
         UNREFERENCED_PARAMETER(req);
         resp = getResponse;
         // When a request is canceled, it should not trigger the callback
         callbackTriggered = true;
      }));
   Sleep(1000);
   // Ensure that the cancel request does not end due to timeout
   EXPECT_TRUE(req.cancelRequest());
   Sleep(5000);
   EXPECT_TRUE(resp.empty());
   EXPECT_FALSE(callbackTriggered);
}


/*
 * LocalhostTest::testSyncCancel --
 *
 *    Test that we can cancel a synchronous request.
 */

TEST_F(LocalhostTest, testSyncCancel)
{
   std::string getUrl = mServerEndpoint + "/delay?seconds=5";
   std::string getResponse;
   bool cancelResult = false;

   WinHttpRequest req;
   req.ignoreAllCertErrors();
   ULONGLONG startTime = GetTickCount64();
   std::thread cancelThread = std::thread([&req, &cancelResult]() {
      Sleep(1000);
      cancelResult = req.cancelRequest();
   });
   EXPECT_FALSE(req.getSync(getUrl, getResponse));
   ULONGLONG endTime = GetTickCount64();
   cancelThread.join();
   ULONGLONG diffTime = endTime - startTime;
   EXPECT_LE(diffTime, 4000ull);
   EXPECT_TRUE(cancelResult);
   // Give the server time to finish its wait (so the next test doesn't fail)
   Sleep(5000);
}


/*
 * LocalhostTest::testAsyncTimeout --
 *
 *    Test that we can set timeout values on the request. An asynchronous request should always
 *    trigger the callback.
 */

TEST_F(LocalhostTest, testAsyncTimeout)
{
   std::string getUrl = mServerEndpoint + "/delay?seconds=5";

   bool callbackTriggered = false;
   cedar::windows::unique_event asyncGetEvent(CreateEvent(0, FALSE, FALSE, 0));
   ASSERT_TRUE(!!asyncGetEvent);

   std::string resp;
   WinHttpRequest req;
   req.ignoreAllCertErrors();
   req.setTimeout(2);
   EXPECT_TRUE(req.getAsync(getUrl, [&callbackTriggered, &asyncGetEvent,
                                     &resp](HttpRequest *req, std::string getResponse) {
      UNREFERENCED_PARAMETER(req);
      resp = getResponse;
      callbackTriggered = true;
      SetEvent(asyncGetEvent.get());
   }));
   EXPECT_EQ(WAIT_OBJECT_0, WaitForSingleObject(asyncGetEvent.get(), INFINITE));
   EXPECT_TRUE(callbackTriggered);
   EXPECT_TRUE(resp.empty());
   // Give the server time to finish its wait (so the next test doesn't fail)
   Sleep(5000);
}


/*
 * LocalhostTest::testSyncTimeout --
 *
 *    Test that we can set timeout values on the request.
 */

TEST_F(LocalhostTest, testSyncTimeout)
{
   std::string getUrl = mServerEndpoint + "/delay?seconds=5";
   std::string response;

   WinHttpRequest req;
   req.ignoreAllCertErrors();
   req.setTimeout(2);
   ULONGLONG startTime = GetTickCount64();
   EXPECT_FALSE(req.getSync(getUrl, response));
   ULONGLONG endTime = GetTickCount64();
   ULONGLONG diffTime = endTime - startTime;
   EXPECT_LE(diffTime, 5000ull);
   // Give the server time to finish its wait (so the next test doesn't fail)
   Sleep(5000);
}


/*
 * LocalhostTest::testCertStore_NoStore --
 *
 *    Try to build the server's certificate using the given store. The store is NULL, so the request
 *    should fail.
 */

TEST_F(LocalhostTest, testCertStore_NoStore)
{
   std::string getUrl = mServerEndpoint + "/ping";

   std::string getResponse;
   WinHttpRequest nullStoreReq;
   nullStoreReq.buildChainUsingStore(NULL);
   nullStoreReq.forceCertStoreChain();
   EXPECT_FALSE(nullStoreReq.getSync(getUrl, getResponse));
}


/*
 * LocalhostTest::testCertStore_Success --
 *
 *    Try to build the server's certificate using the given store. The store is valid and the
 *    thumbprint will be correct, so the request should succeed.
 */

TEST_F(LocalhostTest, testCertStore_Success)
{
   std::string getUrl = mServerEndpoint + "/ping";

   CertificateStoreType store =
      cedar::windows::unique_cert_store(CertOpenStore(CERT_STORE_PROV_MEMORY, 0, NULL, 0, NULL));
   const auto &storeRef = std::get<cedar::windows::unique_cert_store>(store);
   ASSERT_TRUE(!!storeRef);

   const std::string empty;

   std::string certChainStr;
   std::wstring certChainPath = mWorkingDir + L"\\horizon-core-agent-test.crt";
   READ_FILE_INTO_STRING(certChainPath, certChainStr, false);

   WinCertImplementation winCert;
   ASSERT_TRUE(winCert.ImportCertificateToStore(store, certChainStr, empty, empty, empty));

   std::string getResponse;
   WinHttpRequest req;
   req.buildChainUsingStore(storeRef.get());
   req.setCAThumbprint(mRootThumbprint);
   req.forceCertStoreChain();
   EXPECT_TRUE(req.getSync(getUrl, getResponse));
}


/*
 * LocalhostTest::testCertStore_TpMismatch --
 *
 *    Try to build the server's certificate using the given store. The store is valid but the
 *    thumbprint will be wrong, so the request should fail.
 */

TEST_F(LocalhostTest, testCertStore_TpMismatch)
{
   std::string getUrl = mServerEndpoint + "/ping";

   CertificateStoreType store =
      cedar::windows::unique_cert_store(CertOpenStore(CERT_STORE_PROV_MEMORY, 0, NULL, 0, NULL));
   const auto &storeRef = std::get<cedar::windows::unique_cert_store>(store);
   ASSERT_TRUE(!!storeRef);

   const std::string empty;

   std::string certChainStr;
   std::wstring certChainPath = mWorkingDir + L"\\horizon-core-agent-test.crt";
   READ_FILE_INTO_STRING(certChainPath, certChainStr, false);

   WinCertImplementation winCert;
   ASSERT_TRUE(winCert.ImportCertificateToStore(store, certChainStr, empty, empty, empty));

   std::string getResponse;
   WinHttpRequest req;
   req.buildChainUsingStore(storeRef.get());
   req.setCAThumbprint(mWrongThumbprint2);
   req.forceCertStoreChain();
   EXPECT_FALSE(req.getSync(getUrl, getResponse));
}


/*
 * LocalhostTest::Revocation_NoCRL --
 *
 *    Enable the certificate revocation check. Start a server that has CRL distribution points
 *    in its certificate. All CRL distribution points will be inaccessible. Ensure that the request
 *    fails.
 *
 *    Try again with the certificate revocation check disabled. The request should succeed.
 */

TEST_F(LocalhostTest, Revocation_NoCRL)
{
   WaitForCRLCacheIfNeeded();

   auto testName = gutils::GetTestName();

   // Start the server that has a CRL
   START_LEAF_SERVER(mStartLeafAServer);

   std::string getUrl = mLeafAEndpoint + "/ping";
   std::string getResponse;
   WinHttpRequest req;
   req.enableCertRevocation();
   EXPECT_FALSE(req.getSync(getUrl, getResponse));
   EXPECT_TRUE(getResponse.empty());

   // Verify that if we relax the certificate revocation check, our request can succeed
   getResponse.clear();
   WinHttpRequest req2;
   EXPECT_TRUE(req2.getSync(getUrl, getResponse));
   EXPECT_STREQ(getResponse.c_str(), "pong");

   STOP_SERVER();

   /*
    * This test expects the CRL request to fail. So the next test case should wait for the CRL cache
    * to refresh.
    */
   g_should_wait_for_crl = true;
}

/*
 * LocalhostTest::Revocation_OneMissingCRL --
 *
 *    Enable the certificate revocation check. Start a server that has a CRL distribution point
 *    in its certificate. One CRL distribution point will be inaccessible. Ensure that the request
 *    fails.
 */

TEST_F(LocalhostTest, Revocation_OneMissingCRL)
{
   WaitForCRLCacheIfNeeded();

   auto testName = gutils::GetTestName();

   START_CRL_SERVER1();
   START_LEAF_SERVER(mStartLeafAServer);

   std::string getUrl = mLeafAEndpoint + "/ping";
   std::string getResponse;
   WinHttpRequest req;
   req.enableCertRevocation();
   EXPECT_FALSE(req.getSync(getUrl, getResponse));
   EXPECT_TRUE(getResponse.empty());

   STOP_SERVER();
   STOP_CRL_SERVER1();

   EXPECT_TRUE(CRLServer1ReceivedRequest(crl1LogPath));

   /*
    * This test expects the CRL request to fail. So the next test case should wait for the CRL cache
    * to refresh.
    */
   g_should_wait_for_crl = true;
}


/*
 * LocalhostTest::Revocation_LeafA --
 *
 *    Enable the certificate revocation check. Start a server that has a CRL distribution point
 *    in its certificate. Start the CRL servers. Ensure that the request succeeds.
 *
 *                      +----------------------------------------+
 *                      |               Root CA                  |
 *                      |    horizon-core-agent-crl1.com:8887    |
 *                      +----------------------------------------+
 *                               /                    \
 *                              /                      \
 *     +----------------------------------------+     +--------------------------------------------+
 *     |          Intermediate CA A             |     |        Intermediate CA B (Revoked)         |
 *     |    horizon-core-agent-crl2.com:8886    |     |      horizon-core-agent-crl3.com:8885      |
 *     +----------------------------------------+     +--------------------------------------------+
 *                /                    \                              \
 *               /                      \                              \
 *  +--------------------------+  +------------------------------+  +--------------------------+
 *  |      Leaf A (Good)       |  |      Leaf B (Revoked)        |  |        Leaf C            |
 *  |  hzagent-leafA.com:5443  |  |    hzagent-leafB.com:3443    |  |  hzagent-leafC.com:2443  |
 *  +--------------------------+  +------------------------------+  +--------------------------+
 *
 */

TEST_F(LocalhostTest, Revocation_LeafA)
{
   WaitForCRLCacheIfNeeded();

   auto testName = gutils::GetTestName();

   // Start the CRL servers
   START_CRL_SERVER1();
   START_CRL_SERVER2();

   // Start the actual server
   START_LEAF_SERVER(mStartLeafAServer);

   std::string getUrl = mLeafAEndpoint + "/ping";
   std::string getResponse;
   WinHttpRequest req;
   req.enableCertRevocation();
   EXPECT_TRUE(req.getSync(getUrl, getResponse));
   EXPECT_STREQ(getResponse.c_str(), "pong");

   STOP_SERVER();
   STOP_CRL_SERVER2();
   STOP_CRL_SERVER1();

   // Ensure that the CRL servers have the correct logs
   EXPECT_TRUE(CRLServer1ReceivedRequest(crl1LogPath));
   EXPECT_TRUE(CRLServer2ReceivedRequest(crl2LogPath));
}


/*
 * LocalhostTest::Revocation_LeafB --
 *
 *    The leaf cert is revoked. Enable all the crl distribution points.
 *    Ensures that the request fails.
 *
 *    (see above for the diagram)
 */

TEST_F(LocalhostTest, Revocation_LeafB)
{
   WaitForCRLCacheIfNeeded();

   auto testName = gutils::GetTestName();

   // Start the CRL servers
   START_CRL_SERVER1();
   START_CRL_SERVER2();

   // Start the actual server
   START_LEAF_SERVER(mStartLeafBServer);

   std::string getUrl = mLeafBEndpoint + "/ping";
   std::string getResponse;
   WinHttpRequest req;
   req.enableCertRevocation();
   EXPECT_FALSE(req.getSync(getUrl, getResponse));
   EXPECT_TRUE(getResponse.empty());

   STOP_SERVER();
   STOP_CRL_SERVER2();
   STOP_CRL_SERVER1();

   // Ensure that the CRL servers have the correct logs
   EXPECT_TRUE(CRLServer1ReceivedRequest(crl1LogPath));
   EXPECT_TRUE(CRLServer2ReceivedRequest(crl2LogPath));
}


/*
 * LocalhostTest::Revocation_LeafC --
 *
 *    The intermediate cert is revoked. Enable all the crl distribution points.
 *    Ensures that the request fails.
 *
 *    (see above for the diagram)
 */

TEST_F(LocalhostTest, Revocation_LeafC)
{
   WaitForCRLCacheIfNeeded();

   auto testName = gutils::GetTestName();

   // Start the CRL servers
   START_CRL_SERVER1();
   START_CRL_SERVER3();

   // Start the actual server
   START_LEAF_SERVER(mStartLeafCServer);

   std::string getUrl = mLeafCEndpoint + "/ping";
   std::string getResponse;
   WinHttpRequest req;
   req.enableCertRevocation();
   EXPECT_FALSE(req.getSync(getUrl, getResponse));
   EXPECT_TRUE(getResponse.empty());

   STOP_SERVER();
   STOP_CRL_SERVER3();
   STOP_CRL_SERVER1();

   // Ensure that the CRL servers have the correct logs
   EXPECT_TRUE(CRLServer1ReceivedRequest(crl1LogPath));
   /*
    * NOTE: Windows checks the root first. Since the root has revoked the intermediate then the
    * intermediate CRL will not be checked.
    */
   EXPECT_FALSE(CRLServer3ReceivedRequest(crl3LogPath));
}


/*
 * LocalhostTest::Revocation_TPOK --
 *
 *    When the user provides a thumbprint, WinHttpRequest adds a hook to WinHTTP right before it
 *    sends data to the server. This hook is used to check the certificate chain (to attempt to
 *    match the provided thumbprint).
 *
 *    But, this results in us having to do much of the validation ourselves.
 *    So ensure that our validation is working as intended.
 */

TEST_F(LocalhostTest, Revocation_TPOK)
{
   WaitForCRLCacheIfNeeded();

   auto testName = gutils::GetTestName();

   // Start the CRL servers
   START_CRL_SERVER1();
   START_CRL_SERVER2();

   // Start the actual server
   START_LEAF_SERVER(mStartLeafAServer);

   std::string getUrl = mLeafAEndpoint + "/ping";
   std::string getResponse;
   WinHttpRequest req;
   req.setCAThumbprint(mWrongThumbprint1);
   req.enableCertRevocation();
   EXPECT_TRUE(req.getSync(getUrl, getResponse));
   EXPECT_STREQ(getResponse.c_str(), "pong");

   STOP_SERVER();
   STOP_CRL_SERVER2();
   STOP_CRL_SERVER1();

   // Ensure that the CRL servers have the correct logs
   EXPECT_TRUE(CRLServer1ReceivedRequest(crl1LogPath));
   EXPECT_TRUE(CRLServer2ReceivedRequest(crl2LogPath));
}


/*
 * LocalhostTest::Revocation_TPWrongHostname --
 *
 *    Similar to Revocation_TPOK, but using a wrong hostname.
 */

TEST_F(LocalhostTest, Revocation_TPWrongHostname)
{
   WaitForCRLCacheIfNeeded();

   auto testName = gutils::GetTestName();

   // Start the CRL servers
   START_CRL_SERVER1();
   START_CRL_SERVER2();

   // Start the actual server
   START_LEAF_SERVER(mStartLeafAServer);

   std::string getUrl = mLeafAEndpointLocalhost + "/ping";
   std::string getResponse;
   WinHttpRequest req;
   req.setCAThumbprint(mWrongThumbprint1);
   req.ignoreWrongHostCertError();
   req.enableCertRevocation();
   EXPECT_TRUE(req.getSync(getUrl, getResponse));
   EXPECT_STREQ(getResponse.c_str(), "pong");

   getResponse.clear();
   WinHttpRequest req2;
   req2.setCAThumbprint(mWrongThumbprint1);
   req2.enableCertRevocation();
   EXPECT_FALSE(req2.getSync(getUrl, getResponse));
   EXPECT_TRUE(getResponse.empty());

   STOP_SERVER();
   STOP_CRL_SERVER2();
   STOP_CRL_SERVER1();

   // Ensure that the CRL servers have the correct logs
   EXPECT_TRUE(CRLServer1ReceivedRequest(crl1LogPath));
   EXPECT_TRUE(CRLServer2ReceivedRequest(crl2LogPath));
}


/*
 * LocalhostTest::Revocation_TPFail --
 *
 *    Similar to Revocation_TPOK, but the server's certificate is revoked. So the request should
 *    fail.
 */

TEST_F(LocalhostTest, Revocation_TPFail)
{
   WaitForCRLCacheIfNeeded();

   auto testName = gutils::GetTestName();

   // Start the CRL servers
   START_CRL_SERVER1();
   START_CRL_SERVER2();

   // Start the actual server
   START_LEAF_SERVER(mStartLeafBServer);

   std::string getUrl = mLeafBEndpoint + "/ping";
   std::string getResponse;
   WinHttpRequest req;
   req.setCAThumbprint(mWrongThumbprint1);
   req.enableCertRevocation();
   EXPECT_FALSE(req.getSync(getUrl, getResponse));
   EXPECT_TRUE(getResponse.empty());

   STOP_SERVER();
   STOP_CRL_SERVER2();
   STOP_CRL_SERVER1();

   // Ensure that the CRL servers have the correct logs
   EXPECT_TRUE(CRLServer1ReceivedRequest(crl1LogPath));
   EXPECT_TRUE(CRLServer2ReceivedRequest(crl2LogPath));
}


/*
 * LocalhostTest::ExpiredCertificate --
 *
 *    Attempt to make a connection to a server that has an expired certificate. The request should
 *    fail.
 */

TEST_F(LocalhostTest, ExpiredCertificate)
{
   auto testName = gutils::GetTestName();

   START_LEAF_SERVER(mStartExpiredServer);

   std::string getUrl = mExpiredEndpoint + "/ping";
   std::string getResponse;
   WinHttpRequest req;
   req.setCAThumbprint(mRootThumbprint);
   EXPECT_FALSE(req.getSync(getUrl, getResponse));
   EXPECT_TRUE(getResponse.empty());

   STOP_SERVER();
}


/*
 * LocalhostTest::AsyncGet --
 *
 *    Tests that a valid URL returns true and a non-empty response. Tests the async version of the
 *    WinHttpRequest object.
 */

TEST_F(LocalhostTest, AsyncGet)
{
   std::string getUrl = mServerEndpoint + "/ping";

   cedar::windows::unique_event asyncGetEvent(CreateEvent(0, FALSE, FALSE, 0));

   WinHttpRequest req;
   std::string resp;
   req.ignoreAllCertErrors();

   bool res =
      req.getAsync(getUrl, [&asyncGetEvent, &resp](HttpRequest *req, std::string getResponse) {
         UNREFERENCED_PARAMETER(req);
         resp = getResponse;
         SetEvent(asyncGetEvent.get());
      });
   EXPECT_TRUE(res);
   EXPECT_EQ(WAIT_OBJECT_0, WaitForSingleObject(asyncGetEvent.get(), 30000));

   EXPECT_TRUE(!resp.empty());
   EXPECT_EQ(req.getResponseCode(), 200);
   EXPECT_STREQ(resp.c_str(), "pong");
}


/*
 * LocalhostTest::ReleaseInCallback --
 *
 *    Tests that a valid URL returns true and a non-empty response. Tests the async version of the
 *    WinHttpRequest object. Additionally tests that the WinHttpRequest object can be released in
 *    the callback.
 */

TEST_F(LocalhostTest, ReleaseInCallback)
{
   std::string getUrl = mServerEndpoint + "/ping";

   cedar::windows::unique_event asyncGetEvent(CreateEvent(0, FALSE, FALSE, 0));
   ASSERT_TRUE(!!asyncGetEvent);

   unsigned int responseCode = 0;
   std::string resp;
   {
      WinHttpRequest *req = new WinHttpRequest();
      req->ignoreAllCertErrors();
      bool res = req->getAsync(
         getUrl, [&asyncGetEvent, &responseCode, &resp](HttpRequest *req, std::string getResponse) {
            UNREFERENCED_PARAMETER(req);
            UNREFERENCED_PARAMETER(getResponse);
            WinHttpRequest *winReq = dynamic_cast<WinHttpRequest *>(req);
            responseCode = winReq->getResponseCode();
            resp = getResponse;

            winReq->Release();

            SetEvent(asyncGetEvent.get());
         });
      EXPECT_TRUE(res);
   }
   EXPECT_EQ(WAIT_OBJECT_0, WaitForSingleObject(asyncGetEvent.get(), 30000));
   EXPECT_TRUE(!resp.empty());
   EXPECT_EQ(responseCode, 200);
   EXPECT_STREQ(resp.c_str(), "pong");
}


/*
 * LocalhostTest::TrustedRootMismatchTp --
 *
 *    Give a bad thumbprint to the request. The request should succeed because the root is trusted.
 */

TEST_F(LocalhostTest, TrustedRootMismatchTp)
{
   auto testName = gutils::GetTestName();

   START_LEAF_SERVER(mStartLeafAServer);

   std::string getUrl = mLeafAEndpoint + "/ping";

   std::string someThumbprint = "BC:0C:62:A2:CB:38:5F:0D:96:11:D5:AC:7C:54:75:8A:F2:5B:E1:63:88:"
                                "91:52:9A:4E:90:F0:8C:BF:0E:2D:95";
   std::string getResponse;
   WinHttpRequest req;
   req.setCAThumbprint(someThumbprint);
   EXPECT_TRUE(req.getSync(getUrl, getResponse));
   EXPECT_STREQ(getResponse.c_str(), "pong");

   STOP_SERVER();
}
