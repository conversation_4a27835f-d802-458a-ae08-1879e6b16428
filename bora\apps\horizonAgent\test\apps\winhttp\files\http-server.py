# Generated by GitHub Copilot

import os
import json
from http.server import BaseHTTPRequestHandler, HTTPServer
from urllib.parse import urlparse, parse_qs
import ssl
import time
import argparse

class RequestHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        try:
            # Parse the URL and query parameters
            parsed_url = urlparse(self.path)
            if parsed_url.path == "/query":
                # Extract query parameters
                query_params = parse_qs(parsed_url.query)

                # Convert query parameters to a JSON-compatible format
                response_data = {key: value if len(value) > 1 else value[0] for key, value in query_params.items()}

                # Send response headers
                self.send_response(200)
                self.send_header("Content-Type", "application/json")
                self.end_headers()

                # Send JSON response
                self.wfile.write(json.dumps(response_data).encode("utf-8"))
            elif parsed_url.path == "/headers":
                # Extract headers and convert to a dict
                headers_dict = dict(self.headers)
                # Send response headers
                self.send_response(200)
                self.send_header("Content-Type", "application/json")
                self.end_headers()
                # Send JSON response
                self.wfile.write(json.dumps(headers_dict).encode("utf-8"))
            elif parsed_url.path == "/ping":
                self.send_response(200)
                self.send_header("Content-Type", "text/plain")
                self.end_headers()
                self.wfile.write(b"pong")
            elif parsed_url.path == "/querytoheaders":
                # Parse query parameters
                query_params = parse_qs(parsed_url.query)
                self.send_response(200)
                # Add each query parameter as a response header
                for key, values in query_params.items():
                    # Use the first value for each key
                    self.send_header(key, values[0] if values else "")
                self.end_headers()
            elif parsed_url.path == "/delay":
                # Default delay
                delay_seconds = 5
                # Parse query parameters
                query_params = parse_qs(parsed_url.query)
                if "seconds" in query_params:
                    try:
                        # Use the first value for 'seconds' and convert to int
                        delay_seconds = int(query_params["seconds"][0])
                    except (ValueError, IndexError):
                        pass  # Ignore invalid values and use default
                time.sleep(delay_seconds)
                self.send_response(200)
                self.end_headers()
                self.wfile.write(f"Delayed response for {delay_seconds} seconds".encode("utf-8"))
            else:
                # Handle other paths with a 404 response
                self.send_response(404)
                self.end_headers()
                self.wfile.write(b"Not Found")
        except (BrokenPipeError, ConnectionResetError, ssl.SSLEOFError):
            # Client disconnected before response could be sent; ignore
            pass

    def do_POST(self):
        try:
            parsed_url = urlparse(self.path)
            if parsed_url.path == "/post":
                # Get the length of the data
                content_length = int(self.headers.get('Content-Length', 0))
                # Read the POST data
                post_data = self.rfile.read(content_length) if content_length > 0 else b""
                # Send response headers
                self.send_response(200)
                self.send_header("Content-Type", self.headers.get("Content-Type", "application/octet-stream"))
                self.end_headers()
                # Echo the data back
                self.wfile.write(post_data)
            else:
                # Handle other paths with a 404 response
                self.send_response(403)
                self.end_headers()
                self.wfile.write(b"Forbidden")
        except (BrokenPipeError, ConnectionResetError, ssl.SSLEOFError):
            # Client disconnected before response could be sent; ignore
            pass

    def do_PUT(self):
        try:
            parsed_url = urlparse(self.path)
            if parsed_url.path == "/put":
                content_length = int(self.headers.get('Content-Length', 0))
                put_data = self.rfile.read(content_length) if content_length > 0 else b""
                self.send_response(200)
                self.send_header("Content-Type", self.headers.get("Content-Type", "application/octet-stream"))
                self.end_headers()
                self.wfile.write(put_data)
            else:
                self.send_response(404)
                self.end_headers()
                self.wfile.write(b"Not Found")
        except (BrokenPipeError, ConnectionResetError, ssl.SSLEOFError):
            # Client disconnected before response could be sent; ignore
            pass

    def do_DELETE(self):
        try:
            parsed_url = urlparse(self.path)
            if parsed_url.path == "/delete":
                self.send_response(200)
                self.end_headers()
            else:
                self.send_response(404)
                self.end_headers()
                self.wfile.write(b"Not Found")
        except (BrokenPipeError, ConnectionResetError, ssl.SSLEOFError):
            # Client disconnected before response could be sent; ignore
            pass

def run(server_class=HTTPServer, handler_class=RequestHandler, port=4443, certfile=None, keyfile=None):
    # Get the current working directory
    current_dir = os.getcwd()

    # Construct paths for the key and certificate files
    certfile_path = os.path.join(current_dir, certfile)
    keyfile_path = os.path.join(current_dir, keyfile)

    server_address = ("127.0.0.1", port)
    httpd = server_class(server_address, handler_class)

    # Create an SSL context
    ssl_context = ssl.SSLContext(ssl.PROTOCOL_TLS_SERVER)
    ssl_context.load_cert_chain(certfile=certfile_path, keyfile=keyfile_path)

    # Wrap the server socket with SSL
    httpd.socket = ssl_context.wrap_socket(httpd.socket, server_side=True)

    print(f"Starting HTTPS server on https://127.0.0.1:{port}")
    print(f"Using keyfile: {keyfile_path}")
    print(f"Using certfile: {certfile_path}")
    httpd.serve_forever()

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Simple HTTPS server for testing.")
    parser.add_argument("--port", type=int, default=4443, help="Port number to listen on (default: 4443)")
    parser.add_argument("--cert", type=str, default="horizon-core-agent-test.crt", help="Path to certificate file (default: horizon-core-agent-test.crt)")
    parser.add_argument("--key", type=str, default="horizon-core-agent-test.key", help="Path to private key file (default: horizon-core-agent-test.key)")
    args = parser.parse_args()

    run(port=args.port, certfile=args.cert, keyfile=args.key)