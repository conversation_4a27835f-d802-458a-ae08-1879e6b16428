/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * commonSvcWin32.h --
 *
 *     CommonSvcWin32 class to send/receive various messages with
 *     vdpservice interface.
 *
 */

#pragma once
#include "lib/util/objImpl.h"
#include "shared/commonSvc_defines.h"
#include "utilFunctions.h"

#ifdef RDESERVERTEST
#   include <gtest/gtest_prod.h>
#endif
#include "MessageFramework.h"
#include "dpiSync/dpiUtilsWin32.h"
#include "dpiSync/dpiSyncServerWin32.h"
#include "SystemTrayUtil.h"
#include "commonSvc.h"
#include "rdsAadAuthServer.h"
#include "baseCommonSvcRegKeyWatcherHandler.h"

using namespace CORE;
class TabletModeController;

class CommonSvcWin32 :
   public CommonSvc,
   public CWindowImpl<CommonSvcWin32>,
   public RdsAadAuthServer<CommonSvcWin32> {
public:
   HANDLE mUserTokenAvilableEvent;
   ExecuteCMDSlot SetTabletModeSlot;
   ExecuteCMDSlot GetCertSSOUnlockSlot;
   ExecuteCMDSlot CheckFeatureEnablementAfterLogonSlot;
   ExecuteCMDSlot CheckNetworkStateEnableDisplayAfterLogonSlot;
   ExecuteCMDSlot CheckNetworkStateIntervalAfterLogonSlot;

public:
   CommonSvcWin32();
   virtual ~CommonSvcWin32();

   virtual void OnObjectStateChanged();

   HANDLE GetUserToken();
   void SetTabletMode();
   void FetchCertSSOUnlockReq();
   void CheckFeatureEnablementAfterLogon();
   void CheckNetworkStateEnableDisplayAfterLogon();
   void CheckNetworkStateIntervalAfterLogon();

   // CertSSO Unlock functionality
   void SendCertSSOUnlockMsgToClient(tstr &sessionGuid, tstr &ticket);

   bool GetPathWithSessionID(wchar_t *path, size_t size, const wchar_t *basePath);
   void ExecuteAfterUserLogon(ExecuteCMDSlot *slot);

protected:
   bool StartMessageFramework();

   MessageChannel *mMessageFrameworkChannel;
   bool mMessageFrameworkStarted;

   BEGIN_MSG_MAP(CommonSvcWin32)
   MESSAGE_HANDLER(WM_DISPLAYCHANGE, OnDisplayChange);
   MESSAGE_HANDLER(WM_TIMER, OnTimer);
   MESSAGE_HANDLER(WM_DWMCOLORIZATIONCOLORCHANGED, OnDWMColorizationColorChanged);
   MESSAGE_HANDLER(APP_PROTECTION_GPO_CHANGED, OnAppProtectionRegistryChanged);
   MESSAGE_HANDLER(APP_PROTECTION_USER_GPO_READY, OnAppProtectionUserGPOReady);
   END_MSG_MAP()
   LRESULT OnDisplayChange(UINT uMsg, WPARAM wParam, LPARAM lParam, BOOL &bHandled);
   LRESULT OnTimer(UINT uMsg, WPARAM wParam, LPARAM lParam, BOOL &bHandled);
   LRESULT OnDWMColorizationColorChanged(UINT uMsg, WPARAM wParam, LPARAM lParam, BOOL &bHandled);
   LRESULT OnAppProtectionRegistryChanged(UINT uMsg, WPARAM wParam, LPARAM lParam, BOOL &bHandled);
   LRESULT OnAppProtectionUserGPOReady(UINT uMsg, WPARAM wParam, LPARAM lParam, BOOL &bHandled);

   virtual void ProcessDpiSyncCommand(DpiSyncCommand *command);
   virtual void ProcessDisplayCommand(DisplayCommand *command);
   virtual void ProcessTabletModeCommand(TabletModeCommand *command);
   virtual void ProcessBatStateCommand(BatteryStateCommand *command);
   virtual void ProcessEnvironmentVarInfoCommand(const char *pData);
   virtual void ProcessFeatureEnablementCommand(FeatureEnablementCommand *command);
   virtual void ProcessNetworkStateGPOCommand(NetworkStateGPOCommand *command);
   virtual void ReadMachineAppProtectionGPO(uint64 &featureEnablementStatus,
                                            std::map<std::string, util::Variant> &featureOptions);
   virtual void ReadUserAppProtectionGPO(uint64 &featureEnablementStatus,
                                         std::map<std::string, util::Variant> &featureOptions);
   virtual bool IsFeatureOptionsChanged(std::map<std::string, util::Variant> &featureOptions);
   virtual bool WaitForUserLogon(void);

private:
   // Impersonate
   void CreateGetUserTokenThread();

   // CertSSO
   HANDLE InitEventForCertSSOUnlock(DWORD sessionId);

   virtual bool EnsureMessageFrameworkInitialized();

   virtual void OnInvoked(void *contextHandle);

   // Utilites
   bool WaitForExitOrEvent(HANDLE eventToWait);

   // Tray Icons
   bool WasAppLaunchedByUser(HWND hwnd);

   // Variables to manage helper thread lifetime
   HANDLE mHelperThreadStopEvent;

   // Tablet Mode
   unsigned int mTabletMode;
   TabletModeController *mTabletModeController;

   // Events
   std::vector<HANDLE> mCommonSvcThreads;
   HANDLE mUserToken;

private:
   bool mIsBatStatRedirEnabled;
   bool mIconTimerEnabled;
   bool mIsBatTrayCreated;
   bool mIsACConnected;
   bool mAppProtectionUserGPOWatched;
   bool mAppProtectionMachineGPOWatched;
   uint32 mBatteryLifePercent;
   uint64 mClientFeatureCapacity;
   uint64 mFeatureEnablementStatus;
   std::map<std::string, util::Variant> mFeatureOptions;
   HICON mIconBat;
   int mIconBatResourceId;
   UINT mAppLauncherPid;
   std::map<uint64, bool> mAppLaunchedByUserMap;
   SystemTrayUtil mSystemTrayUtil;
   DpiSyncServerWin32 mDpiSyncServerWin32;
   BaseCommonSvcRegKeyWatcherHandler *mAppProtectionWatcherHandler = NULL;
#ifdef RDESERVERTEST
   FRIEND_TEST(CommonSvcWin32UnitTest, ProcessEnvironmentVarInfoCommand);
   FRIEND_TEST(CommonSvcWin32UnitTest, StartMessageFramework);
   FRIEND_TEST(CommonSvcWin32UnitTest, WaitForUserLogon);
   FRIEND_TEST(CommonSvcWin32UnitTest, OnDWMColorizationColorChanged);
   FRIEND_TEST(CommonSvcWin32UnitTest, FetchCertSSOUnlockReq);
   FRIEND_TEST(CommonSvcWin32UnitTest, ProcessBatStateCommand);
   FRIEND_TEST(CommonSvcWin32UnitTest, IsFeatureOptionsChanged);
   FRIEND_TEST(CommonSvcWin32UnitTest, ReadMachineAppProtectionGPO);
   FRIEND_TEST(CommonSvcWin32UnitTest, ReadUserAppProtectionGPO);
#endif
};
