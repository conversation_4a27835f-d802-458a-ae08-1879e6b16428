<h1>VMware Horizon Unity Architecture - Quick Guide</h1>
<h2>What is Unity Mode?</h2>
<p>Unity (seamless window) mode allows guest applications to appear as native host windows, removing the VM desktop border and making apps feel integrated with the host OS.</p>
<h2>Example: Opening notepad.exe in Unity Mode</h2>
<h3>1. User Action</h3>
<p>User double-clicks notepad.exe in Windows VM</p>
<h3>2. Guest Detection</h3>
<ul>
<li><strong>Tools Service</strong> (<code>vmtoolsd.exe</code>) detects new window creation</li>
<li><strong>Unity Hook</strong> (<code>unityHook.dll</code>) captures window properties:</li>
<li>Window handle, title, size, position</li>
<li>Window attributes (resizable, minimizable, etc.)</li>
</ul>
<h3>3. Guest-to-Host Communication</h3>
<p><code>Unity RPC Protocol (unity.x)
├── UNITY_WINDOW_NEW - Create new window
├── Window ID, type, attributes
└── Position, size, title data</code></p>
<h3>4. Host Processing</h3>
<ul>
<li><strong>Unity Manager</strong> (<code>unityMgr.cpp</code>) receives RPC</li>
<li>Creates <strong>Unity Window</strong> object (<code>unityWindow.cpp</code>)</li>
<li>Maps guest coordinates to host coordinates</li>
</ul>
<h3>5. Host Window Creation</h3>
<ul>
<li>Creates Win32 window with appropriate styles:
  <code>cpp
  dwStyles = WS_POPUP | WS_SYSMENU | WS_MINIMIZEBOX;
  dwExStyles = WS_EX_TOOLWINDOW;</code></li>
<li>Sets up MKS rendering region</li>
<li>Configures window decorations</li>
</ul>
<h3>6. Window Becomes Visible</h3>
<ul>
<li>Window appears on host desktop</li>
<li>Taskbar integration (if <code>WS_EX_APPWINDOW</code>)</li>
<li>Focus management synchronized</li>
</ul>
<h3>7. Ongoing Operations</h3>
<ul>
<li><strong>Input</strong>: Host events → Guest application</li>
<li><strong>Updates</strong>: Guest changes → Host window refresh</li>
<li><strong>Move/Resize</strong>: Bidirectional synchronization</li>
</ul>
<h3>8. Window Closure</h3>
<ul>
<li>Guest closes notepad → Unity RPC destroy</li>
<li>Host window cleanup and removal</li>
</ul>
<h2>Key Components</h2>
<p>| Component | Role |
|-----------|------|
| <code>vmtoolsd.exe</code> | Guest service managing Unity |
| <code>unityHook.dll</code> | Window event capture |
| <code>unityMgr.cpp</code> | Host-side window lifecycle |
| <code>unityWindow.cpp</code> | Individual window management |
| Unity RPC | Guest↔Host communication |</p>
<h2>Benefits</h2>
<ul>
<li><strong>Seamless Integration</strong>: Apps appear native to host</li>
<li><strong>Multi-Monitor</strong>: Windows can span host displays  </li>
<li><strong>Performance</strong>: Direct rendering without VM desktop</li>
<li><strong>User Experience</strong>: Native window management</li>
</ul>
<hr />
<p><em>VMware Horizon Unity Architecture - Internal Training Guide</em></p>