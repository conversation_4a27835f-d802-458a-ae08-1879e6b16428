/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

#pragma once

#include <core/windows/Customization.h>
#include <core/windows/DomainJoin.h>
#include <core/windows/Network.h>
#include <core/windows/Script.h>

//
// This define enables a few pieces of code that allow
// testing the agent sysprep code independent of the ngvc server.
// Some registry keys must be reset to 0 and sysprep
// invoked from the VC.  If this value is 0, the agent
// expects the ngvc server to initiate sysprep and generalize
// to run prior to clone booting.  If 1, generalize runs
// on the clone.
//
#define SYSPREP_TEST 0

#define SYSPREP_STATE_VALUE _T("ImageState")

#define SYSPREP_STATE_GENERALIZE_TO_OOBE _T("IMAGE_STATE_GENERALIZE_RESEAL_TO_OOBE")
#define SYSPREP_STATE_GENERALIZE_TO_AUDIT _T("IMAGE_STATE_GENERALIZE_RESEAL_TO_AUDIT")
#define SYSPREP_STATE_SPECIALIZE_TO_OOBE _T("IMAGE_STATE_SPECIALIZE_RESEAL_TO_OOBE")
#define SYSPREP_STATE_SPECIALIZE_TO_AUDIT _T("IMAGE_STATE_SPECIALIZE_RESEAL_TO_AUDIT")

#define SYSPREP_STATE_COMPLETE _T("IMAGE_STATE_COMPLETE")
#define SYSPREP_STATE_UNDEPLOYABLE _T("IMAGE_STATE_UNDEPLOYABLE")

#define SYSPREP_DEFAULT_REGISTRY_FLUSH_WAIT_TIME (ONE_MINUTE)
#define OPERATION_TYPE_MAX_WAIT_TIME (ONE_MINUTE * 5)
#define SYSPREP_DEFAULT_MAX_WAIT_TIME (ONE_MINUTE * 10)
#define SYSPREP_DEFAULT_SPECIALIZE_MAX_WAIT_TIME (ONE_MINUTE * 60)
#define SYSPREP_DEFAULT_OOBE_MAX_WAIT_TIME (ONE_MINUTE * 30)
#define SYSPREP_MAX_WAIT_TIME_VALUE_NAME _T("SysprepMaxWaitTime")

#define SYSPREP_XML_UPDATE_SCRIPT_PATH "SetSysprepXmlInterfaceId.ps1"

#define SETUP_COMMAND_LINE_VALUE_NAME _T("CmdLine")
#define SETUP_COMMAND_LINE_DEFAULT_VALUE _T("oobe\\windeploy.exe")

#define OPERATION_TYPE_VALUE_NAME _T("OPERATION_TYPE")

namespace svmga {
namespace core {
namespace windows {

namespace coreutil = svmga::core::util;
namespace windev = svmga::common::windows::device;

class Universalprep : public Customization {
public:
   Universalprep();

   bool CustomizeMaster();
   bool CustomizeIT();
   bool CustomizeReplica();
   bool CustomizeParent();
   bool CustomizeClone();

protected:
   bool CustomizeClonePreShutdown();
   bool CustomizeClonePostShutdown();

   bool CustomizeClonePostSnapshot();
   bool CustomizeClonePostRefresh();

   bool WaitForSysprep();
   bool WaitForGeneralize();
   bool WaitForOobe();

   bool WaitForPoolId();

private:
   bool VerifyUseSVI(DWORD expectedValue);

   AgentHelperWrapper *_ahu;
};

} // namespace windows
} // namespace core
} // namespace svmga