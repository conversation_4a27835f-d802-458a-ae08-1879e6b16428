#!/bin/sh

########################################################
# Copyright (c) Omnissa, LLC. All rights reserved.
# This product is protected by copyright and intellectual property laws in the
# United States and other countries as well as by international treaties.
# -- Omnissa Restricted
########################################################


initialise() {
   HOST="`hostname`"
   NAME="`date +$HOST-vdm-sdct-%Y%m%d-%H%M-agent`"
   FILES=""
   TMP_FILES=""
   TMP_DIR=/tmp/${NAME}
   mkdir ${TMP_DIR}
   grep "^OPEN_OS=true" $AGENT_CFG_FILE >>/dev/null 2>&1
   if [ "$?" = "0" ]; then
      OPEN_OS="true"
      echo "Current OS $DISTRO_ID is not fully supported"
   fi
}


finalise() {
   echo "Creating ${NAME}.tgz"
   printf "%s\n" $FILES > tempfilelist.txt
   tar  cfhz "${NAME}.tgz" -T tempfilelist.txt -C ${TMP_DIR} ${TMP_FILES} 2>/dev/null
   rm -fr ${TMP_DIR}
   rm -f tempfilelist.txt
}


gather_agent() {
   VIEWAGENT_PRODUCT=$VIEWAGENT_PATH/Product.txt
   VIEWAGENT_XORG_CONF=$VIEWAGENT_PATH/resources/X11/xorg.conf
   VIEWAGENT_LOG=/var/log/omnissa/viewagent*.log*
   HORIZON_LOG=`find /tmp/omnissa-* -regex '.*/horizon-.*log'`
   VIEWAGENT_LOG_EVENT=`find $VIEWAGENT_TMP_ROOT -regex '.*/logEvent'`
   SCREEN_LOCK_STATUS=`find $VIEWAGENT_TMP_ROOT -regex '.*/screenLockStatus.*'`
   VIEWAGENT_ENV_INFO=`find $VIEWAGENT_TMP_ROOT -regex '.*/envInfo'`
   AUTORANDR_CONF_FILE=$VIEWAGENT_TMP_ROOT/autorandr/consoleProfile/config
   AUTORANDR_SETUP_FILE=$VIEWAGENT_TMP_ROOT/autorandr/consoleProfile/setup
   UDEV_RULES=/etc/udev/rules.d/*.rules
   FILES="$FILES $VIEWAGENT_PRODUCT $VIEWAGENT_LOG $HORIZON_LOG"
   FILES="$FILES $VIEWAGENT_ENV_FILE $VIEWAGENT_LOG_EVENT"
   FILES="$FILES $SCREEN_LOCK_STATUS"
   FILES="$FILES $BORA_CFG_FILE $CUSTOM_CFG_FILE"
   FILES="$FILES $VIEWAGENT_ENV_INFO $VIEWAGENT_XORG_CONF"
   FILES="$FILES $AUTORANDR_CONF_FILE $AUTORANDR_SETUP_FILE"
   FILES="$FILES $UDEV_RULES"
   FILES="$FILES $AGENT_CFG_FILE"
   if grep "^VADCINST=true" $AGENT_CFG_FILE >/dev/null 2>&1; then
      FILES="$FILES /etc/omnissa/vadc/viewagent-vadc.conf"
   fi

   AGENT_CFG_DCT=viewagent-config.txt

   grep -v JMS_KEYSTORE_PASSWORD $AGENT_CFG_FILE | \
   grep -v "INSTALL_OPTIONS=.* -j " | \
   grep -v "INSTALL_OPTIONS=.* -p " \
      > ${TMP_DIR}/$AGENT_CFG_DCT
   echo "# JMS_KEYSTORE_PASSWORD stripped" >> ${TMP_DIR}/$AGENT_CFG_DCT 2>&1

   TMP_FILES="$TMP_FILES $AGENT_CFG_DCT"

   gather_agent_recording
   gather_agent_titan
}


gather_agent_recording() {
   if [ -d $HORIZON_PATH/horizonrecording ]; then
      HORIZON_RECORDING_PARING_DATA=/etc/omnissa/horizonrecording/pairingdata.json
      HORIZON_RECORDING_CONFIGURATION=/etc/omnissa/horizonrecording/recordingconfiguration.json
      HORIZON_RECORDING_LOG=/var/log/omnissa/horizonrecording/ServiceLogging*.log*
      FILES="$FILES $HORIZON_RECORDING_PARING_DATA $HORIZON_RECORDING_CONFIGURATION"
      FILES="$FILES $HORIZON_RECORDING_LOG"
   fi
}


gather_agent_titan() {
   TITAN_MQTT_CONFIG=/etc/omnissa/mqtt-config.txt
   if [ -f "$TITAN_MQTT_CONFIG" ]; then
      TITAN_WA_AGENT=/var/log/waagent.log
      FILES="$FILES $TITAN_MQTT_CONFIG $TITAN_WA_AGENT"

      TITAN_AUTHD_BROKER_CONF=/var/snap/authd-msentraid/current/broker.conf
      if [ -f "$TITAN_AUTHD_BROKER_CONF" ]; then
         TITAN_AUTHD_MSENTRAID_CONF=/etc/authd/brokers.d/msentraid.conf
         FILES="$FILES $TITAN_AUTHD_BROKER_CONF $TITAN_AUTHD_MSENTRAID_CONF"
      fi
   fi
}


gather_UI() {
   which pkaction >/dev/null 2>&1
   if [ "$?" = "0" ];then
      PKACTION=pkaction.txt
      pkaction > ${TMP_DIR}/${PKACTION} 2>&1
      TMP_FILES="$TMP_FILES $PKACTION"
   fi
}


gather_X() {
   XORG_LOG=/var/log/Xorg*
   XORG_CONF=/etc/X11/xorg.conf
   FILES="$FILES $XORG_LOG $XORG_CONF"
}


gather_network() {
   HOSTS=/etc/hosts
   RESOLV=/etc/resolv.conf
   NSSWITCH=/etc/nsswitch.conf

   FILES="$FILES $HOSTS $RESOLV $NSSWITCH"

   IFCONFIG=ifconfig.txt
   FIREWALL=iptables-save.txt
   NETSTAT=netstat.txt

   which ifconfig >/dev/null 2>&1
   if [ "$?" = "0" ]; then
      ifconfig -a > ${TMP_DIR}/${IFCONFIG} 2>&1
   else
      which ip >/dev/null 2>&1 && ip address > ${TMP_DIR}/${IFCONFIG} 2>&1
   fi
   iptables-save > ${TMP_DIR}/${FIREWALL} 2>&1
   which netstat >/dev/null 2>&1
   if [ "$?" = "0" ]; then
      netstat -an > ${TMP_DIR}/${NETSTAT} 2>&1
   else
      which ss >/dev/null 2>&1 && ss -an > ${TMP_DIR}/${NETSTAT} 2>&1
   fi

   TMP_FILES="$TMP_FILES $IFCONFIG $FIREWALL $NETSTAT"
}


gather_system() {
   case "$DISTRO_ID" in
   ${DISTRO_ID_ROCKY}|\
   ${DISTRO_ID_CENTOS}|\
   ${DISTRO_ID_RHEL_CLIENT}|\
   ${DISTRO_ID_RHEL_SERVER}|\
   ${DISTRO_ID_RHEL_WORKSTATION}|\
   ${DISTRO_ID_SUSE}|\
   ${DISTRO_ID_NEOKYLIN})
      SYSTEM_LOG="/var/log/messages*"
      ;;
   ${DISTRO_ID_UBUNTU}|\
   ${DISTRO_ID_DEBIAN}|\
   ${DISTRO_ID_KYLIN})
      SYSTEM_LOG="/var/log/syslog*"
      ;;
   esac

   #
   # Common files
   #
   BOOT_LOG=/var/log/boot*.log

   FILES="$FILES $BOOT_LOG $SYSTEM_LOG $PAM_PATH"

   HOSTNAME=hostname.txt
   DOMAIN=domain.txt
   PTREE=ptree.txt
   SYSTEM_UUID=system-uuid.txt
   JAVA_DCT=java-version.txt
   DMESG=dmesg.txt

   hostname > ${TMP_DIR}/${HOSTNAME} 2>&1
   domainname > ${TMP_DIR}/${DOMAIN} 2>&1
   dmidecode -s system-uuid > ${TMP_DIR}/${SYSTEM_UUID} 2>&1
   ps -auxf > ${TMP_DIR}/${PTREE} 2>&1
   dmesg > ${TMP_DIR}/${DMESG} 2>&1

   JAVA=$VIEWAGENT_PATH/jre/bin/java
   ${JAVA} -version > ${TMP_DIR}/${JAVA_DCT} 2>&1

   TMP_PROC_DIR=${TMP_DIR}/proc
   mkdir -p ${TMP_PROC_DIR}
   while read proc_file
   do
      cat /proc/${proc_file} > ${TMP_PROC_DIR}/${proc_file} 2>&1
      TMP_FILES="$TMP_FILES proc/${proc_file}"
   done << !!!
cpuinfo
meminfo
vmstat
loadavg
!!!

   #initctl list
   #service --status-all

   TMP_FILES="$TMP_FILES $HOSTNAME $DOMAIN $PTREE $SYSTEM_UUID $JAVA_DCT $DMESG"
}

gather_sso() {
   if [ -f "$VIEWAGENT_PATH/sso/pamAuth" ]; then
      SECURE_LOG="/var/log/secure*"
      KRB5_CONFIG="/etc/krb5.conf"
      SSSD_CONFIG="/etc/sssd/sssd.conf"
      SSSD_LOG="/var/log/sssd"
      FILES="$FILES $SECURE_LOG $KRB5_CONFIG $SSSD_CONFIG $SSSD_LOG"

      # Collect ca-cert information for truesso
      grep "^TRUESSOINST=true" /etc/omnissa/viewagent-config.txt > /dev/null 2>&1
      if [ "$?" = "0" ]; then
         SSSD_CERT_INFO=sssd_auth_ca_db.txt
         CA_CERT_INFO=ca_cert.txt
         CA_CERT_PATH=
         if [ -f "/etc/sssd/pki/sssd_auth_ca_db.pem" ]; then
            openssl crl2pkcs7 -nocrl -certfile /etc/sssd/pki/sssd_auth_ca_db.pem | \
               openssl pkcs7 -print_certs -noout -text > ${TMP_DIR}/${SSSD_CERT_INFO} 2>&1
         fi

         if [ -f "/etc/pki/ca-trust/source/anchors/ca_cert.pem" ]; then
            CA_CERT_PATH="/etc/pki/ca-trust/source/anchors/ca_cert.pem"
         elif [ -f "/etc/pam_pkcs11/cacerts/certificate.pem" ]; then
            CA_CERT_PATH="/etc/pam_pkcs11/cacerts/certificate.pem"
         fi
         if [ -n "$CA_CERT_PATH" ]; then
            openssl crl2pkcs7 -nocrl -certfile ${CA_CERT_PATH} | \
               openssl pkcs7 -print_certs -noout -text > ${TMP_DIR}/${CA_CERT_INFO} 2>&1
         fi

         TMP_FILES="$TMP_FILES $SSSD_CERT_INFO $CA_CERT_INFO"
      fi
   fi
}

gather_smc() {
   # Collect ca-cert information for smartcard redirection
   grep "^SCREDIRINST=true" /etc/omnissa/viewagent-config.txt > /dev/null 2>&1
   if [ "$?" = "0" ]; then
      SMCSSO_CONFIG="/etc/omnissa/viewagent-greeter.conf"
      FILES="$FILES $SMCSSO_CONFIG"

      SSSD_CERT_INFO=sssd_auth_ca_db.txt
      CA_CERT_INFO=ca_cert.txt
      CA_CERT_PATH=
      if [ -f "/etc/sssd/pki/sssd_auth_ca_db.pem" ]; then
         openssl crl2pkcs7 -nocrl -certfile /etc/sssd/pki/sssd_auth_ca_db.pem | \
            openssl pkcs7 -print_certs -noout -text > ${TMP_DIR}/${SSSD_CERT_INFO} 2>&1
      elif [ -f "/etc/pam_pkcs11/cacerts/certificate.pem" ]; then
         CA_CERT_PATH="/etc/pam_pkcs11/cacerts/certificate.pem"
	 openssl crl2pkcs7 -nocrl -certfile ${CA_CERT_PATH} | \
            openssl pkcs7 -print_certs -noout -text > ${TMP_DIR}/${CA_CERT_INFO} 2>&1
      elif [ -f "/etc/pam_pkcs11/nssdb" ]; then
	 certutil -L -d /etc/pam_pkcs11/nssdb > ${TMP_DIR}/${CA_CERT_INFO} 2>&1
      fi
         
      TMP_FILES="$TMP_FILES $SSSD_CERT_INFO $CA_CERT_INFO"
   fi
}

gather_authentication() {
   gather_sso
   gather_smc

   case "$DISTRO_ID" in
   ${DISTRO_ID_ROCKY}|\
   ${DISTRO_ID_CENTOS}|\
   ${DISTRO_ID_RHEL_CLIENT}|\
   ${DISTRO_ID_RHEL_SERVER}|\
   ${DISTRO_ID_RHEL_WORKSTATION}|\
   ${DISTRO_ID_SUSE}|\
   ${DISTRO_ID_NEOKYLIN})
      AUTH_LOG="/var/log/audit/audit.log*"
      DM_LOG="/var/log/gdm"
      ;;
   ${DISTRO_ID_UBUNTU}|\
   ${DISTRO_ID_DEBIAN})
      AUTH_LOG="/var/log/auth.log*"
      DM_LOG="/var/log/gdm3"
      ;;
   ${DISTRO_ID_KYLIN})
      AUTH_LOG="/var/log/auth.log*"
      DM_LOG="/var/log/lightdm"
      ;;
   *)
      if [ "$OPEN_OS" = "true" ]; then
         ls -U /var/log/audit/audit.log* >/dev/null 2>&1
         if [ "$?" = "0" ]; then
            AUTH_LOG="/var/log/audit/audit.log*"
         fi
         ls -U /var/log/auth.log* >/dev/null 2>&1
         if [ "$?" = "0" ]; then
            AUTH_LOG="$AUTH_LOG /var/log/auth.log*"
         fi
         if [ -d "/var/log/gdm" ]; then
            DM_LOG="/var/log/gdm"
         fi
         if [ -d "/var/log/lightdm" ]; then
            DM_LOG="$DM_LOG /var/log/lightdm"
         fi
      else
         echo "$DISTRO_ID not supported"
         exit
      fi
      ;;
   esac

   PAM_CONFIG="/etc/pam.d"

   FILES="$FILES $AUTH_LOG $DM_LOG $PAM_CONFIG"
}

gather_cups() {
   if [ -f "/etc/cups/cupsd.conf" ]; then
      CUPS_CONFIG="/etc/cups/cupsd.conf"
   fi
   if [ -f "/var/log/cups/error_log" ]; then
      CUPS_LOG="/var/log/cups/*"
   fi
   FILES="$FILES $CUPS_CONFIG $CUPS_LOG"
}

gather_coredumps() {
   case "$DISTRO_ID" in
   ${DISTRO_ID_ROCKY}|\
   ${DISTRO_ID_CENTOS}|\
   ${DISTRO_ID_RHEL_CLIENT}|\
   ${DISTRO_ID_RHEL_SERVER}|\
   ${DISTRO_ID_RHEL_WORKSTATION}|\
   ${DISTRO_ID_SUSE}|\
   ${DISTRO_ID_NEOKYLIN})
      COREFILES="$VIEWAGENT_PATH/core.*"
      ;;
   ${DISTRO_ID_UBUNTU}|\
   ${DISTRO_ID_DEBIAN}|\
   ${DISTRO_ID_KYLIN})
      #
      # Dependent upon the release / version of Ubuntu / apport, the following
      # has been tested on Ubuntu 14.04 LTS.
      #
      COREFILES="$VIEWAGENT_PATH/core"
      ;;
   *)
      if [ "$OPEN_OS" = "true" ]; then
         ls -U $VIEWAGENT_PATH/core* >/dev/null 2>&1
         if [ "$?" = "0" ]; then
            COREFILES="$VIEWAGENT_PATH/core*"
         fi
      else
         echo "$DISTRO_ID not supported"
         exit
      fi
      ;;
   esac

   FILES="$FILES $COREFILES"
}


gather_crashdumps() {
   #
   # On Ubuntu the apport service currently gathers in core dumps from
   # third party binaries, hence look for crashes relating to Linux Agent
   # comments.   Longer term this should be made more generic, i.e. disable
   # apport for Linux Agent binaries and support across all distributions via
   # core dumps.
   #

   case "$DISTRO_ID" in
   ${DISTRO_ID_ROCKY}|\
   ${DISTRO_ID_CENTOS}|\
   ${DISTRO_ID_RHEL_CLIENT}|\
   ${DISTRO_ID_RHEL_SERVER}|\
   ${DISTRO_ID_RHEL_WORKSTATION}|\
   ${DISTRO_ID_SUSE}|\
   ${DISTRO_ID_NEOKYLIN})
      ;;
   ${DISTRO_ID_UBUNTU}|\
   ${DISTRO_ID_DEBIAN}|\
   ${DISTRO_ID_KYLIN})
      CRASHFILES="/var/crash/_usr_lib_omnissa_viewagent*"
      ;;
   *)
      if [ "$OPEN_OS" = "true" ]; then
         ls -U /var/crash/_usr_lib_omnissa_viewagent* >/dev/null 2>&1
         if [ "$?" = "0" ]; then
            CRASHFILES="/var/crash/_usr_lib_omnissa_viewagent*"
         fi
      else
         echo "$DISTRO_ID not supported"
         exit
      fi
      ;;
   esac

   FILES="$FILES $CRASHFILES"
}

gather_gdm_conf () {
   test -d /etc/gdm && gdm_conf_dir=/etc/gdm
   test -d /etc/gdm3 && gdm_conf_dir=/etc/gdm3
   test -f ${gdm_conf_dir}/daemon.conf && FILES="$FILES ${gdm_conf_dir}/daemon.conf"
   test -f ${gdm_conf_dir}/custom.conf && FILES="$FILES ${gdm_conf_dir}/custom.conf"
}


#
# main
#

#
# Ensure script is executed as root.
#
if [ "`id -u`" != "0" ]; then
   echo "dct-debug.sh not run as superuser"
   exit
fi


#
# Include common constants and routines
#
. "`dirname $0`/commonlib.sh"


identify_distribution
configure_distribution_common
initialise
gather_system
gather_authentication
gather_network
gather_X
gather_UI
gather_agent
gather_coredumps
gather_crashdumps
gather_cups
gather_gdm_conf
finalise
