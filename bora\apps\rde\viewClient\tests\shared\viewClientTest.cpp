/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * ViewClientTest.cpp -
 *
 *    Test functions of viewClient.c
 */

#include <gmock/gmock.h>
#include <gtest/gtest.h>
#include "utMock.h"

#include "vdpPluginHost.h"
#include "vdpTarget.h"
#include "viewClient.h"
#include "vncClient.h"
#ifdef _WIN32
#   include "hznprotect.h"
#endif

extern "C" {
void ViewClientTest_SetProtocol(MKSDisplayProtocol protocol);
void ViewClientTest_SetTarget(int target);
Bool ViewClient_ConnectBlast(const char *targetURL, const char *thumbprint,
                             SSLThumbprintType tpType, const char *httpProxy,
                             int reconnectAttemptCount);
void ViewClient_RetryProtocolRedirectConnection(const char *target);
Bool ViewClientLock_IsLocked(void);
void ViewClientLock_Lock_func(const char *func);
void ViewClientLock_Unlock(void);

void ViewClient_SetBlockKeyLoggerStatus(Bool enable);
Bool ViewClient_IsBlockKeyLoggerEnabled();
void ViewClient_SetBlockSendInputStatus(Bool enable);
Bool ViewClient_IsBlockSendInputEnabled();
void ViewClient_SetAllowArmNoAntiKeyloggerStatus(Bool enable);

#ifdef _WIN32
BOOL HznProtect_IsAMD64(void);
#endif
}


/*
 *-----------------------------------------------------------------------------
 *
 * ViewClientUnitTest
 *
 *      Test functions in ViewClient.
 *
 *-----------------------------------------------------------------------------
 */

class ViewClientUnitTest : public ::testing::Test {
public:
   static void SetUpTestCase();
   static void TearDownTestCase();

protected:
   virtual void SetUp() override;
   virtual void TearDown() override;
};


/*
 *-----------------------------------------------------------------------------
 *
 * ViewClientUnitTest::SetUpTestCase --
 *
 *      SetUp for entire test suite.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

void
ViewClientUnitTest::SetUpTestCase()
{
   return;
}


/*
 *-----------------------------------------------------------------------------
 *
 * ViewClientUnitTest::TearDownTestCase --
 *
 *      TearDown for entire test suite.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

void
ViewClientUnitTest::TearDownTestCase()
{
   return;
}


/*
 *-----------------------------------------------------------------------------
 *
 * ViewClientUnitTest::SetUp --
 *
 *      SetUp for each test case.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

void
ViewClientUnitTest::SetUp()
{
   return;
}


/*
 *-----------------------------------------------------------------------------
 *
 * ViewClientUnitTest::TearDown --
 *
 *      TearDown for each test case.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

void
ViewClientUnitTest::TearDown()
{
   return;
}


/*
 *-----------------------------------------------------------------------------
 *
 * ViewClientUnitTest::RetryProtocolRedirectConnection --
 *
 *      Test API function ViewClient_RetryProtocolRedirectConnection.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(ViewClientUnitTest, RetryProtocolRedirectConnection)
{
   const char target[] = "20.252.62.178:8443;{\"a\":\"N95P18beYZV4ntjFMZpR85AMGsHTSuHu5d8NTgn7\", \
      \"j\":\"1EEAD33D-5CA6-488F-BCE1-0371F253966F\",\"thumbprint\":\"40 C0 23 E3 94 3D BD 98 48 34 7A BB F7 DC 0C 0D BE E7 60 DC\", \
      \"thumbprint256\":\"9F 02 8E 60 A1 24 1A DA AA D7 36 DE 32 2B E1 28 07 40 FD 65 93 EE 83 5B 14 D7 DD 73 A9 F4 4B 2F\"}";
   char targetUrlExpected[] = "ws://20.252.62.178:8443/j/1EEAD33D-5CA6-488F-BCE1-0371F253966F/"
                              "?vauth=N95P18beYZV4ntjFMZpR85AMGsHTSuHu5d8NTgn7";
   char thumprintExpect[] = "9F:02:8E:60:A1:24:1A:DA:AA:D7:36:DE:32:2B:E1:28:07:40:FD:65:93:EE:83:"
                            "5B:14:D7:DD:73:A9:F4:4B:2F";

   VMOCK(&ViewClientLock_IsLocked).Will(TRUE);
   VMOCK(&ViewClientLock_Lock_func).Will([](const char *func) { return; });
   VMOCK(&ViewClientLock_Unlock).WillOnce([](void) { return; });
   VMOCK(&ViewClient_IsBlastSession).WillRepeatedly(TRUE);
   VMOCK(&ViewClient_ConnectBlast)
      .WillOnce([&targetUrlExpected, &thumprintExpect](
                   const char *targetURL, const char *thumbprint, SSLThumbprintType tpType,
                   const char *httpProxy, int reconnectAttemptCount) {
         EXPECT_STREQ(targetURL, targetUrlExpected);
         EXPECT_STREQ(thumbprint, thumprintExpect);
         EXPECT_EQ(tpType, SSL_THUMBPRINT_SHA256);
         EXPECT_EQ(reconnectAttemptCount, -1);

         return TRUE;
      });

   ViewClient_RetryProtocolRedirectConnection(target);
}


/*
 *-----------------------------------------------------------------------------
 *
 * ViewClientUnitTest::IsBlockKeyLoggerEnabled --
 *
 *      Test API function ViewClient_IsBlockKeyLoggerEnabled.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(ViewClientUnitTest, IsBlockKeyLoggerEnabled)
{
   {
      ViewClient_SetBlockKeyLoggerStatus(TRUE);
      ViewClient_SetAllowArmNoAntiKeyloggerStatus(TRUE);
#ifdef _WIN32
      VMOCK(&HznProtect_IsAMD64).Will(FALSE);
      EXPECT_FALSE(ViewClient_IsBlockKeyLoggerEnabled());

      VMOCK(&HznProtect_IsAMD64).Will(TRUE);
      EXPECT_TRUE(ViewClient_IsBlockKeyLoggerEnabled());
#else
      EXPECT_TRUE(ViewClient_IsBlockKeyLoggerEnabled());
#endif
   }

   {
      ViewClient_SetBlockKeyLoggerStatus(TRUE);
      ViewClient_SetAllowArmNoAntiKeyloggerStatus(FALSE);
#ifdef _WIN32
      VMOCK(&HznProtect_IsAMD64).Will(FALSE);
      EXPECT_TRUE(ViewClient_IsBlockKeyLoggerEnabled());

      VMOCK(&HznProtect_IsAMD64).Will(TRUE);
      EXPECT_TRUE(ViewClient_IsBlockKeyLoggerEnabled());
#else
      EXPECT_TRUE(ViewClient_IsBlockKeyLoggerEnabled());
#endif
   }

   {
      ViewClient_SetBlockKeyLoggerStatus(FALSE);
      ViewClient_SetAllowArmNoAntiKeyloggerStatus(FALSE);
#ifdef _WIN32
      VMOCK(&HznProtect_IsAMD64).Will(FALSE);
      EXPECT_FALSE(ViewClient_IsBlockKeyLoggerEnabled());

      VMOCK(&HznProtect_IsAMD64).Will(TRUE);
      EXPECT_FALSE(ViewClient_IsBlockKeyLoggerEnabled());
#else
      EXPECT_FALSE(ViewClient_IsBlockKeyLoggerEnabled());
#endif
   }

   {
      ViewClient_SetBlockKeyLoggerStatus(FALSE);
      ViewClient_SetAllowArmNoAntiKeyloggerStatus(TRUE);
#ifdef _WIN32
      VMOCK(&HznProtect_IsAMD64).Will(FALSE);
      EXPECT_FALSE(ViewClient_IsBlockKeyLoggerEnabled());

      VMOCK(&HznProtect_IsAMD64).Will(TRUE);
      EXPECT_FALSE(ViewClient_IsBlockKeyLoggerEnabled());
#else
      EXPECT_FALSE(ViewClient_IsBlockKeyLoggerEnabled());
#endif
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * ViewClientUnitTest::IsBlockSendInputEnabled --
 *
 *      Test API function ViewClient_IsBlockSendInputEnabled.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(ViewClientUnitTest, IsBlockSendInputEnabled)
{
   {
      ViewClient_SetBlockSendInputStatus(TRUE);
      ViewClient_SetAllowArmNoAntiKeyloggerStatus(TRUE);
#ifdef _WIN32
      VMOCK(&HznProtect_IsAMD64).Will(FALSE);
      EXPECT_FALSE(ViewClient_IsBlockSendInputEnabled());

      VMOCK(&HznProtect_IsAMD64).Will(TRUE);
      EXPECT_TRUE(ViewClient_IsBlockSendInputEnabled());
#else
      EXPECT_TRUE(ViewClient_IsBlockSendInputEnabled());
#endif
   }

   {
      ViewClient_SetBlockSendInputStatus(TRUE);
      ViewClient_SetAllowArmNoAntiKeyloggerStatus(FALSE);
#ifdef _WIN32
      VMOCK(&HznProtect_IsAMD64).Will(FALSE);
      EXPECT_TRUE(ViewClient_IsBlockSendInputEnabled());

      VMOCK(&HznProtect_IsAMD64).Will(TRUE);
      EXPECT_TRUE(ViewClient_IsBlockSendInputEnabled());
#else
      EXPECT_TRUE(ViewClient_IsBlockSendInputEnabled());
#endif
   }

   {
      ViewClient_SetBlockSendInputStatus(FALSE);
      ViewClient_SetAllowArmNoAntiKeyloggerStatus(FALSE);
#ifdef _WIN32
      VMOCK(&HznProtect_IsAMD64).Will(FALSE);
      EXPECT_FALSE(ViewClient_IsBlockSendInputEnabled());

      VMOCK(&HznProtect_IsAMD64).Will(TRUE);
      EXPECT_FALSE(ViewClient_IsBlockSendInputEnabled());
#else
      EXPECT_FALSE(ViewClient_IsBlockSendInputEnabled());
#endif
   }

   {
      ViewClient_SetBlockSendInputStatus(FALSE);
      ViewClient_SetAllowArmNoAntiKeyloggerStatus(TRUE);
#ifdef _WIN32
      VMOCK(&HznProtect_IsAMD64).Will(FALSE);
      EXPECT_FALSE(ViewClient_IsBlockSendInputEnabled());

      VMOCK(&HznProtect_IsAMD64).Will(TRUE);
      EXPECT_FALSE(ViewClient_IsBlockSendInputEnabled());
#else
      EXPECT_FALSE(ViewClient_IsBlockSendInputEnabled());
#endif
   }
}