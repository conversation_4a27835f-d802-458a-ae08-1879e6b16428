/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * vvclib.h
 *
 * View Virtual Channel library
 *
 */


#ifndef _VVCLIB_H
#define _VVCLIB_H

#include "horizon.h"
#include "vm_atomic.h"
#include "vvc.h"
#include "vvcStatus.h"
#include "asyncsocket.h"
#include "hashMap.h"

#if defined(_WIN32)
#   include <Qos2.h>
#endif

#if defined(__cplusplus)
extern "C" {
#endif

/*
 * VvcPluginId - each client side plugin is assigned a unique id which is used
 *               track the resources that each plugin creates.  Code that uses
 *               VVC directly should should pass VVC_PLUGIN_ID_CORE_PROTOCOL
 *               as the plugin id.
 */
typedef uint32 VvcPluginId;
#define VVC_PLUGIN_ID_INVALID ((VvcPluginId)~0)
#define VVC_PLUGIN_ID_CORE_PROTOCOL ((VvcPluginId)0)


// VVC Instance poll callback function
typedef void (*VvcInstancePollCb)(void *clientData);


/*
 * VVC Instance backend interface
 */
typedef struct VvcInstanceBackend {
   /*
    * Add/remove a timer callback
    */
   VvcStatus (*pollCallback)(VvcInstancePollCb callback, void *clientData, Bool periodic,
                             uint32 timeoutUs);

   VvcStatus (*pollRemove)(VvcInstancePollCb callback, void *clientData, Bool periodic);

#ifdef _WIN32
   // Windows performance provider and counter GUIDs
   GUID perfProviderGuid;
   GUID perfCounterGuid;
#endif

} VvcInstanceBackend;


// VVC Transport backend flags
#define VVC_TRANSPORT_BE_DISABLE_BANDWIDTH_DETECTION 0x01
#define VVC_TRANSPORT_BE_SERVER 0x02
#define VVC_TRANSPORT_BE_ENABLE_FORCE_CANCEL_SAFE_IO 0x04
#define VVC_TRANSPORT_BE_ENABLE_MULTI_PROTOCOL 0x08
#define VVC_TRANSPORT_BE_ENABLE_NW_CONTINUITY 0x10
#define VVC_TRANSPORT_BE_ENABLE_NW_INTELLIGENCE 0x20


// Infinite max-flight
#define VVC_INFINITE_INFLIGHT 0

// MPT Version
#define VVC_MPT_V0 (0) // No MPT/NC
#define VVC_MPT_V1 (1) // MPT/NC
// V2 is skipped and is treated as equal to V1
#define VVC_MPT_V3 (3) // MPT with implicit/optional header

#define VVC_MPT_VERSION_DEFAULT VVC_MPT_V1
#define VVC_MPT_VERSION_MIN VVC_MPT_V0
#define VVC_MPT_VERSION_MAX VVC_MPT_V3

/*
 * Keepalive / inactivity intervals. If TRANSPORT_KEEPALIVE_INTERVAL_SEC
 * pass without receiving anything on the socket, we send a keepalive.
 * If TRANSPORT_KEEPALIVE_TIMEOUT_SEC pass without any activity,
 * we send a socket error. TRANSPORT_TIMEOUT_POLL_INTERVAL_SEC is how
 * often we check to see if we have a full inactivity interval without
 * traffic.
 * TRANSPORT_KEEPALIVE_INTERVAL_SEC is calculated based off of
 * the values of TRANSPORT_KEEPALIVE_TIMEOUT_SEC and
 * TRANSPORT_TIMEOUT_POLL_INTERVAL_SEC. We want
 * TRANSPORT_KEEPALIVE_INTERVAL_SEC to be as long as possible so that we
 * aren't sending unnecessary messages, but it also needs to be short
 * enough that we have a reasonable amount of time to send a keepalive and
 * receive an ack before the timeout fires.
 *
 * NOTE: Since BWE socket keepalives are based on send inactivity, it
 * is sufficient to set keepalive interval to be half of keepalive timeout.
 * This is taken care of at places where BWE socket wrap is performed.
 * BWE socket wrap would fail at run time if keepalive interval is set
 * to a non-zero value below 20s[Bug 2106237].
 *
 * TODO: Extend this to get interval values from config / the registry.
 */
#define TRANSPORT_KEEPALIVE_TIMEOUT_SEC 40
#define TRANSPORT_TIMEOUT_POLL_INTERVAL_SEC 5
#define TRANSPORT_KEEPALIVE_INTERVAL_SEC                                                           \
   (TRANSPORT_KEEPALIVE_TIMEOUT_SEC / 2 - TRANSPORT_TIMEOUT_POLL_INTERVAL_SEC - 1)

typedef struct VvcNetworkStats {
   double bandwidthBytesPerSecond;
   double rttSeconds;
   double rttVarianceSeconds;
   double packetLossPercent;
   uint32 queuedBytes;   /* unsent bytes in send queue */
   uint32 inflightBytes; /* bytes on the wire but not acknowledged */
   int remotePort;       /* port client uses, valid only on client-side */

} VvcNetworkStats;


/*
 * Identify the type of three Network Indicators
 */
typedef enum VvcNetworkIndicatorType {
   VVC_NETWORK_INDICATOR_RTT,
   VVC_NETWORK_INDICATOR_PACKETLOSS,
   VVC_NETWORK_INDICATOR_RTTV,

   VVC_NETWORK_INDICATOR_MAX

} VvcNetworkIndicatorType;


/*
 * Network Quality State to send to UI
 */
typedef enum VvcNetworkQualityState {
   /* Quality state when there are no backends */
   VVC_NETWORK_QUALITY_NONE,

   /* Score falls to 70 and above by default */
   VVC_NETWORK_QUALITY_GOOD,

   /* Score falls in between 40 and 70 by default */
   VVC_NETWORK_QUALITY_OK,

   /* Score falls to 40 and below by default */
   VVC_NETWORK_QUALITY_POOR

} VvcNetworkQualityState;


/*
 * Network Indicator Stats structure used to store
 * the data needed to send over to UI for update.
 * It's also used in viewControlTypes.h for UI update.
 */
typedef struct VvcNetworkIndicatorStats {
   /* Local Active Transport Type (NONE, TCP or UDP) */
   VvcSessionActiveTransportType localActiveTransportType;

   /*
    * Network Quality State based on pre-defined
    * range the calculated score falls into.
    */
   VvcNetworkQualityState qualityState;

   /* Store raw indicator data */
   double indicators[VVC_NETWORK_INDICATOR_MAX];

} VvcNetworkIndicatorStats;


/*
 * VVC Transport backend interface
 */
typedef struct VvcTransptBackend {
   void *clientData;

   /*
    * Send a buffer.  VvcSessionTransportSendComplete is called by the
    * by the transport provider when the send has completed
    */
   VvcStatus (*send)(uint8 *buf, size_t len, void *completionContext, void *clientData);

   /*
    * Receive a buffer.  VvcSessionTransportRecvComplete is called by the
    * by the transport provider when the recv has completed
    */
   VvcStatus (*recv)(uint8 *buf, size_t len, void *completionContext, void *clientData);

   /*
    * Close transport
    */
   VvcStatus (*close)(void *clientData);

   /*
    * Get network statistics from transport
    */
   VvcStatus (*getNetworkStats)(void *clientData, VvcNetworkStats *networkStats);

   /*
    * Get SNI hostname from transport
    */
   VvcStatus (*getSNIHostname)(void *clientData, const char **sniHostname);

   /*
    * Notify transport provider to cancel all recv operations
    */
   VvcStatus (*cancelRecv)(void *clientData);

   /*
    * TODO: optional stream and virtual channel callbacks for transports sctp
    * and pcoip vchan
    */

   // Flags field
   uint32 flags;

   // Max in-flight when bandwidth detection is disabled
   uint64 maxInFlight;

   /*
    * Get Auxiliary Flow information from transport
    */
   VvcStatus (*getAuxiliaryFlowInfo)(void *clientData, VvcAuxiliaryFlowInfo *auxFlowInfo,
                                     SSLVerifyParam *sslParams);

   VvcStatus (*getSetupMsg)(void *clientData, char **setupMsg, int *len);
   VvcStatus (*verifySetupMsg)(void *clientData, AsyncSocket *asock);
   void *(*getSslContext)(void *clientData);
   Bool (*tcpConnect)(void *clientData, unsigned char serialNo, void *cbData,
                      void *connectionCookie);
} VvcTransptBackend;


/*
 * The implementations of VvcAsockBackend functions are owned by VVC including
 * the errorCb.
 * However, an application will provide its errorCb. When to invoke that
 * will be decided by Multi Protocol logic inside Vvc.
 * See comments in vvcMultiAsockBackend.c for more details.
 */

typedef void (*VvcAsockBackendErrorCb)(int error, AsyncSocket *asock, int32 sessionID,
                                       void *clientData);

/*
 * Callback to notify the applications when a specific AsockBe gets
 * marked as DataSocket.
 *
 * The "Bool isCtrlSock" argument of this callback will let the applications
 * know if this Data AsockBe also happens to be the Control AsockBe or not.
 */

typedef void (*VvcAsockBackendDataSockActivatedCb)(AsyncSocket *asock, int32 sessionID,
                                                   Bool isReconnect, Bool isCtrlSock,
                                                   void *clientData);

typedef Bool (*VvcAsockBackendGetAuxFlowInfoCb)(void *clientData, void *valuePtr,
                                                socklen_t *outBufLen, SSLVerifyParam *sslParams);

typedef Bool (*VvcAsockBackendGetSetupMsgCb)(void *clientData, char **buf, int *bufLen);

typedef Bool (*VvcAsockBackendVerifySetupMsgCb)(AsyncSocket *asock, void *cb, void *getParamsCb,
                                                void *cbData);

typedef void *(*VvcAsockBackendGetSslContextCb)(void *clientData);

typedef Bool (*VvcAsockBackendTCPConnect)(void *clientData, void *cbData, void *connectionCookie,
                                          uint32 nonce, unsigned char serialNo);
/*
 * VvcAsockBackend structure
 *
 * When VvcTransportBackend::flags includes MULTIPROTOCOL, applications MUST
 * provide at least one AsyncSocket to VVC via VVCLIB_AddAsockBackend()
 */

typedef struct VvcRecvState VvcRecvState;

typedef struct VvcAsockBackend {
   AsyncSocket *asock;
   MXUserRecLock *asockLock;
   int asockID;

   VvcAsockBackendErrorCb errorCb;
   void *errorCbClientData;

   VvcAsockBackendDataSockActivatedCb dataSockActivatedCb;
   void *dataSockActivatedCbData;

   VvcAsockBackendGetAuxFlowInfoCb getAuxFlowInfoCb;
   void *getAuxFlowInfoCbData;

   VvcAsockBackendGetSetupMsgCb getSetupMsgCb;
   VvcAsockBackendVerifySetupMsgCb verifySetupMsgCb;
   void *verifySetupMsgCbCb; // BlastSocketCompleteGetParamsCB
   void *verifySetupMsgCbCbData;

   VvcAsockBackendGetSslContextCb getSslCtxCb;
   void *getSslCtxCbData;

   VvcAsockBackendTCPConnect tcpConnect;
   void *tcpConnectData;

   Bool isEndToEndConnection;

   Bool isControlAsock;
   Bool isDataAsock;

   Bool isVVCHeartbeatEnabled;

   // Timestamps in us, used for knowing when there's network traffic
   uint64 lastRecvTs;
   uint64 lastSendTs;

   // Session pointer
   VvcSessionHandle session;

   // Track receive state for this asock backend
   VvcRecvState *recvState;

   // MPTv3: Per-AsockBackend MsgSeq (RcvNxt and SndNxt)
   HashMap *channelMsgSeqMap;

   Bool isRemoved;

#if defined(_WIN32)
   HANDLE qwaveQoSHandle;
   QOS_FLOWID qwaveQoSFlowID;
#endif
   int remotePort; // portNum used to connect socket, not valid agent-side

   uint32 reconnectCount;

   Atomic_uint32 refCount;

   // server-side (worker) nonce used for raw channels
   uint32 workerNonce;
   uint32 serialNo;
   // client-side info for raw channels
   Bool isPrimaryTunneled;

   Bool blastSocketThreadEnabled;   // TRUE if net polling thread available
   uint32 negotiatedTcpBweVersion;  // negotiated TCP BWE version if control asock
   Atomic_uint32 errorCbInProgress; // > 0 if in error callback

   Bool negotiatedVvcRawChannels; // raw channels negotiated if client and control asock
} VvcAsockBackend;


// Instance event callbacks
typedef void(VVCFN_API *VvcInstanceOnDestroyCb)(VvcInstanceHandle instanceHandle, void *clientData);

// Session  events
typedef struct VvcInstanceEvents {
   VvcInstanceOnDestroyCb onDestroy;

} VvcInstanceEvents;


typedef struct VvcPerfCounters {
   /*
    * Session and channel counters
    *
    * NOTE: sendQueueBytes, ackBps, msgSchedulePeriodAvg, bandwidthAvg,
    *       maxInFlightAvg, bytesToSendAvg, inFlightBytesAvg, rttAvg are not
    *       recorded for channels
    */

   double inBps;
   uint64 inQueueBytes;

   double sendBps;
   uint64 sendQueueBytes;

   double ackBps;

   uint64 msgSchedulePeriodAvg;
   uint64 bandwidthAvg;
   uint64 maxInFlightAvg;
   uint64 bytesToSendAvg;
   uint64 inFlightBytesAvg;
   uint64 rttAvg;

} VvcPerfCounters;


// Session event callbacks
typedef void(VVCFN_API *VvcSessionOnEstablishedCb)(VvcSessionHandle sessionHandle,
                                                   void *clientData);

typedef void(VVCFN_API *VvcSessionOnErrorCb)(VvcSessionHandle sessionHandle, VvcStatus status,
                                             void *clientData);

typedef void(VVCFN_API *VvcSessionOnDestroyCb)(VvcSessionHandle sessionHandle, void *clientData);

typedef void(VVCFN_API *VvcSessionOnCloseCb)(VvcSessionHandle sessionHandle, uint32 userMetadata,
                                             void *clientData);

// Forward decl VvcDeferredAcksParams struct
typedef struct VvcDeferredAcksParams VvcDeferredAcksParams;

// Session  params
typedef struct VvcSessionParams {
   Bool enableVVCCloseSeq;
   Bool enableVVCPauseResume;
   Bool enableVVCQoSPolicy;
   uint8 mptVersion;
   VvcDeferredAcksParams *deferredAcksParams;
   Bool enableVVCBatching;
   Bool disableVvcRawChannels;

} VvcSessionParams;

/*
 * A config to decide which portions of the VVC close sequence
 * we should do.
 *   VVC_CLOSE_CONFIG_FORCE_CLOSE - Skip the entire VVC close
 *      sequence.
 *   VVC_CLOSE_CONFIG_FLUSH_ONLY - Make a best effort to flush all data
 *      but do not send the VVC close messages.
 *   VVC_CLOSE_CONFIG_FLUSH_AND_SEND_MSG - Flush all data and send
 *      the VVC close messages. This will guarantee all data is delivered.
 *
 */
typedef enum _VvcSessionCloseConfig {
   VVC_CLOSE_CONFIG_FORCE_CLOSE = 1,
   VVC_CLOSE_CONFIG_FLUSH_ONLY = 2,
   VVC_CLOSE_CONFIG_FLUSH_AND_SEND_MSG = 3,
} VvcSessionCloseConfig;

// Session close  params
typedef struct VvcSessionCloseParams {
   uint32 closeReason;
   VvcSessionCloseConfig closeConfig;

} VvcSessionCloseParams;

// Session  events
typedef struct VvcSessionEvents {
   VvcSessionOnEstablishedCb onEstablished;
   VvcSessionOnErrorCb onError;
   VvcSessionOnDestroyCb onDestroy;
   VvcSessionOnCloseCb onClose;

} VvcSessionEvents;

/*
 * VVC Session Config
 */
typedef enum _VvcSessionConfig {
   VVC_CONFIG_MIN_BANDWIDTH_RATE, /* bytesPerSecond */
   VVC_CONFIG_MAX_BANDWIDTH_RATE, /* bytesPerSecond */
   VVC_CONFIG_MAX_BANDWIDTH_BURST_MSEC,
} VvcSessionConfig;
/*
 * VVC Log levels
 */
typedef enum _VvcLogLevel {
   VVCLOG_UNKNOWN,
   VVCLOG_FATAL,
   VVCLOG_ERROR,
   VVCLOG_WARN,
   VVCLOG_INFO,
   VVCLOG_DEBUG,
   VVCLOG_TRACE,
   VVCLOG_VERBOSE,
   VVCLOG_ALL,
} VvcLogLevel;

typedef enum _VvcMemLogLevel {
   VVCMEMLOG_UNKNOWN,
   VVCMEMLOG_NONE,
   VVCMEMLOG_ALL,
} VvcMemLogLevel;

typedef enum _VvcPktTraceLevel {
   VVCPKTTRACE_UNKNOWN,
   VVCPKTTRACE_NONE,
   VVCPKTTRACE_ALL,
} VvcPktTraceLevel;

// VVC init flags
#define VVC_INSTANCE_MAIN 0x001
#define VVC_INSTANCE_DEFER_EVENT_DISPATCH 0x002
#define VVC_INSTANCE_DEFER_SEND_DISPATCH 0x004
#define VVC_INSTANCE_ENABLE_LOADER 0x008
#define VVC_INSTANCE_ENABLE_PERF_COUNTERS 0x010
#define VVC_INSTANCE_DEFER_EVENT_DISPATCH_THREAD 0x040
#define VVC_INSTANCE_DEFER_SEND_DISPATCH_THREAD 0x100
#define VVC_INSTANCE_WAIT_FOR_EVENT_THREAD_EXIT 0x200

// VVC session transport cancel flags
#define VVC_SESSION_TP_CANCEL_SENDS 0x001
#define VVC_SESSION_TP_CANCEL_RECV 0x002
#define VVC_SESSION_TP_CANCEL_FORCE 0x004

/*
 * Is this instance co-located with the client<>desktop transport endpoint.
 * INPROC is used if not set
 */
#define VVC_INSTANCE_LOCATION_OUTOFPROC 0x020
/*
 * Running on CLIENT or DESKTOP (which is the default when not set)
 */
#define VVC_INSTANCE_ROLE_CLIENT 0x080


// Host API decl's
VvcStatus VVCLIB_Init(uint32 flags, void *clientData, char *name, VvcInstanceBackend *instBe,
                      VvcInstanceEvents *events, VvcInstanceHandle *instHandle);

VvcStatus VVCLIB_Uninit(VvcInstanceHandle instHandle);

VvcStatus VVCLIB_CreateListenerInstance(VvcInstanceHandle instHandle, VvcPluginId pluginId,
                                        int32 sessionId, char *name, VvcListenerEvents *events,
                                        void *clientData, VvcListenerHandle *listenerHandle);

/*
 * TODO: Revisit transportBe->flags like MULTI_PROTOCOL, NW_CONTINUITY,
 *       NW_INTELLIGENCE and move to session->flags as they are more a
 *       session property than transport.
 */
VvcStatus VVCLIB_OpenSession(VvcInstanceHandle instHandle, VvcTransptBackend *transportBe,
                             int32 sessionId, uint32 flags, VvcSessionParams params,
                             VvcSessionEvents *events, void *clientData,
                             VvcSessionHandle *sessionHandle);

VvcStatus VVCLIB_StartSession(VvcSessionHandle sessionHandle);

VvcStatus VVCLIB_CloseSession(VvcSessionHandle sessionHandle, VvcSessionCloseParams *closeParams);

VvcStatus VVCLIB_AsockBackendErrorHandler(int error, AsyncSocket *asock,
                                          VvcSessionHandle sessionHandle);

VvcStatus VVCLIB_SetSessionEvents(VvcSessionHandle sessionHandle, VvcSessionEvents *events,
                                  void *clientData);

VvcStatus VVCLIB_SetSessionConfig(VvcSessionHandle sessionHandle, VvcSessionConfig configType,
                                  void *value, size_t valuelen);

int32 VVCLIB_GetPlatformIdFromVvcSessionId(VvcSessionId sessionId);

VvcSessionId VVCLIB_GetVvcSessionId(VvcSessionHandle sessionHandle);

VvcStatus VVCLIB_GetSessionId(VvcSessionHandle sessionHandle, int32 *sessionId);

VvcStatus VVCLIB_AddRefInstance(VvcInstanceHandle instanceHandle);

VvcStatus VVCLIB_ReleaseInstance(VvcInstanceHandle instanceHandle);

VvcStatus VVCLIB_AddRefSession(VvcSessionHandle sessionHandle);

VvcStatus VVCLIB_ReleaseSession(VvcSessionHandle sessionHandle);

VvcSessionHandle VVCLIB_GetSessionHandle(VvcInstanceHandle instHandle, int32 sessionId);

Bool VVCLIB_GetSessionIsVVCReconnectTokenEnabled(int32 sessionId);

Bool VVCLIB_GetSessionIsPluginReconnectTokenDisabled(int32 sessionId);

Bool VVCLIB_GetSessionIsVVCHeartbeatEnabled(int32 sessionId);

Bool VVCLIB_GetSessionIsVVCCloseSeqEnabled(int32 sessionId);

Bool VVCLIB_GetSessionIsVVCPauseResumeEnabled(int32 sessionId);

Bool VVCLIB_GetSessionIsNetworkContinuityEnabled(int32 sessionId);

VvcStatus VVCLIB_SessionTransportSendComplete(void *sccBatcher, VvcStatus status, uint8 *buf,
                                              size_t len);

VvcStatus VVCLIB_SessionTransportCancelSafeSendComplete(void *sccBatcher, VvcStatus status,
                                                        uint8 *buf, size_t len);

VvcStatus VVCLIB_SessionTransportRecvComplete(void *completionContext, VvcStatus status, uint8 *buf,
                                              size_t len, size_t bytesRecv);

VvcStatus VVCLIB_SessionTransportCancelIo(VvcSessionHandle sessionHandle, uint32 flags);

VvcStatus VVCLIB_AddAsockBackend(VvcSessionHandle sessionHandle, VvcAsockBackend *asockBackend);


/*
 * VvcDataTransportSwitchPolicyParams is pushed by BlastSockets to VvcSession.
 * These PolicyParams then get consumed into VvcSession::dataTransportSwitch
 * after negotiatedDoConcurrentTransports.
 */
typedef struct VvcDataTransportSwitchPolicyParams {

   double transportSwitchCbPeriodMS; // doSwitchPollCb schedule time in ms

   double beatToTcpBwKbps;            // switch to TCP if Bw higher than this
   double beatToTcpPktLossPercentage; // switch to TCP if less loss than this
   double beatToTcpRttMS;             // switch to TCP if less RTT than this
   double beatToTcpRttVarPercentage;  // switch to TCP if less rttVar than this

   double tcpToBeatBwKbps;           // switch to BEAT if Bw lesser than this
   double tcpToBeatRttVarPercentage; // switch to BEAT if rttVar more than this
   double tcpToBeatRttMS;            // switch to BEAT if Rtt more than this

   uint32 beatToTcpThreshold;
   uint32 tcpToBeatThreshold;

   Bool isSwitchingAlwaysEnabled; // switch every time regardless of nw params
   int32 switchCountMax;          // max num of switches allowed per session

} VvcDataTransportSwitchPolicyParams;

/*
 * The below API is effective only on Agent-Side.
 * The below API should be invoked on an Opened-but-not-started VvcSession
 * (similar to VVCLIB_AddAsockBackend()).
 */

VvcStatus VVCLIB_SetTransportSwitchPolicy(VvcSessionHandle sessionHandle,
                                          VvcDataTransportSwitchPolicyParams params);

/*
 * Why QoSPolicParams is a separate struct than SwitchPolicyParams:
 *    With BENIT Enabled: QoSPolicyParams are sent by agent to client.
 *    With BENIT Disabled: QoSPolicy will get applied locally at both ends.
 *
 *    Since QoSPolicy should get applied even with BENIT disabled, define
 *    a separate struct and an API.
 */

#define VVC_QOS_INVALID_VALUE -1
#define VVC_QOS_MIN_VALUE 0
#define VVC_QOS_MAX_VALUE 63 // 63 decimal = 0x3F hex i.e. the rightmost 6 bits
#define VVC_QOS_VERSION_1 1

/*
 * VvcQoSPolicyParams is pushed by BlastSockets to VvcSession.
 */
typedef struct VvcQoSPolicyParams {

   int32 version;

   struct {
      // IPv4 AOut TCP and UDP - Dscp values for traffic originating from agent
      int dscpAOutTCPv4;
      int dscpAOutUDPv4;
      // IPv4 COut TCP and UDP - Dscp values for traffic originating from client
      int dscpCOutTCPv4;
      int dscpCOutUDPv4;
      // IPv4 BUp TCP and UDP - Dscp values for upstream traffic from BSG
      int dscpBUpTCPv4;
      int dscpBUpUDPv4;
      // IPv4 BDown TCP and UDP - Dscp values for downstream traffic from BSG
      int dscpBDownTCPv4;
      int dscpBDownUDPv4;

      // IPv6 AOut TCP and UDP - Dscp values for traffic originating from agent
      int dscpAOutTCPv6;
      int dscpAOutUDPv6;
      // IPv6 COut TCP and UDP - Dscp values for traffic originating from client
      int dscpCOutTCPv6;
      int dscpCOutUDPv6;
      // IPv6 BUp TCP and UDP - Dscp values for upstream traffic from BSG
      int dscpBUpTCPv6;
      int dscpBUpUDPv6;
      // IPv6 BDown TCP and UDP - Dscp values for downstream traffic from BSG
      int dscpBDownTCPv6;
      int dscpBDownUDPv6;
   } v1;

} VvcQoSPolicyParams;

/*
 * The below API should be invoked on an Opened-but-not-started VvcSession
 * (similar to VVCLIB_AddAsockBackend()).
 */

VvcStatus VVCLIB_SetQoSPolicy(VvcSessionHandle sessionHandle, VvcQoSPolicyParams params);

#define VVC_ENABLE_DEFERRED_ACKS_DEFAULT (TRUE)
#define VVC_MPT_ACK_QUIET_PERIOD_DEFAULT (5000)
#define VVC_MPT_ACK_SEQ_GAP_DEFAULT (4)
#define VVC_MPT_ACK_SEQ_GAP_MAX (8 * 1024)
#define VVC_MPT_ACK_UNACKED_BYTES_DEFAULT (32 * 1024)

/*
 * VvcDeferredAcksParams is pushed by BlastSockets to VvcSession.
 *
 * Client-Side: BlastSocketClientConnectParams takes in a
 *              VvcDeferredAcksParams variable to BlastSocket_Connect().
 *
 * Agent-Side: In cookieCreateCb, worker will read & push VvcDeferredAcks to
 *             BlastSockets layer via API BlastSocket_SetDeferredAcksParams()
 *             BlastSockets layer then pushes it to VVC via VVCLIB_OpenSession()
 *             This ensures any change in VvcDeferredAckParams takes effect
 *             across disconnect-reconnects instead of needing a Login-Logoff.
 */

struct VvcDeferredAcksParams {

   Bool enableDeferredAcks;
   int32 mptAckQuietPeriod;
   int32 mptAckUnackedBytes;
   int32 mptAckSeqGap;
};

/*
 * Why RemoveAsockBackend API should not be exposed to applications:
 * 1. The "Multi-Protocol" logic is handled inside VVC and applications should
 *    not be aware of it. Applications will only AddAsockBackends to Vvc.
 * 2. Vvc will -
 *    - Communicate the SocketErrorCb to outside
 *    - Remove the AsockBackend on which errorCb was fired on.
 * 3. So in short, the "Remove AsockBackend" is implicitly & internally handled
 *    within Vvc.
 */

VvcStatus VVCLIB_GetSessionPerfCounters(VvcSessionHandle sessionHandle,
                                        VvcPerfCounters *perfCounters);

VvcSessionCloseReason VVCLIB_GetSessionCloseReason(VvcSessionHandle sessionHandle);

/* Public API for VVCLIB_SetSessionCloseReason() is not Needed */

VvcStatus VVCLIB_GetIntf(VvcPluginId pluginId, VvcIntfVer *verReqd, VvcIntf inft);


// Extension API decl's
VvcStatus VVCLIB_CreateListener(VvcPluginId pluginId, int32 sessionId, char *name,
                                VvcListenerEvents *events, void *clientData,
                                VvcListenerHandle *listenerHandle);

VvcStatus VVCLIB_CreateListenerV11(VvcPluginId pluginId, int32 sessionId, char *name,
                                   VvcListenerEvents *events, void *clientData,
                                   VvcListenerHandle *listenerHandle);

// VVCLIB_CreateListenerEx API is supported from VvcIntf 1.5 onwards.
VvcStatus VVCLIB_CreateListenerEx(VvcPluginId pluginId, VvcSessionId sessionId, char *name,
                                  VvcListenerEvents *events, void *clientData,
                                  VvcListenerHandle *listenerHandle);

VvcStatus VVCLIB_ActivateListener(VvcListenerHandle listenerHandle);

VvcStatus VVCLIB_CloseListener(VvcListenerHandle listenerHandle);

VvcStatus VVCLIB_AddRefListener(VvcListenerHandle listenerHandle);

VvcStatus VVCLIB_ReleaseListener(VvcListenerHandle listenerHandle);

VvcStatus VVCLIB_GetListenerSessionId(VvcListenerHandle listenerHandle, int32 *sessionId);

VvcStatus VVCLIB_GetListenerHandle(int32 sessionId, const char *name,
                                   VvcListenerHandle *vvcListenerHandleHandle);

VvcStatus VVCLIB_OpenChannel(VvcListenerHandle listenerHandle, char *name, void *connectionCookie,
                             VvcChannelEvents *events, uint32 priority, uint32 timeout,
                             uint32 flags, uint8 *initialData, size_t initialDataLen,
                             void *clientData, uint32 *channelId);

VvcStatus VVCLIB_AcceptChannel(void *connectionCookie, uint32 flags,
                               VvcChannelEvents *channelEvents, uint8 *initialData,
                               size_t initialDataLen, void *clientData, uint32 *channelId);

VvcStatus VVCLIB_ResumeChannel(VvcChannelHandle channelHandle, uint8 *initialData,
                               size_t initialDataLen);

VvcStatus VVCLIB_RejectChannel(void *connectionCookie, uint32 reserved, uint8 *initialData,
                               size_t initialDataLen);

VvcStatus VVCLIB_CloseChannelInt(VvcChannelHandle channelHandle, VvcCloseChannelReason reason);

VvcStatus VVCLIB_CloseChannel(VvcChannelHandle channelHandle, VvcCloseChannelReason reason);

VvcStatus VVCLIB_CloseNCDeclinedChannels(VvcSessionHandle sessionHandle,
                                         VvcCloseChannelReason reason);

VvcStatus VVCLIB_SetChannelEvents(VvcChannelHandle channelHandle, VvcChannelEvents *events,
                                  void *clientData);

VvcStatus VVCLIB_AddRefChannel(VvcChannelHandle channelHandle);

VvcStatus VVCLIB_ReleaseChannel(VvcChannelHandle channelHandle);

VvcStatus VVCLIB_GetChannelId(VvcChannelHandle channelHandle, uint32 *channelId);

VvcStatus VVCLIB_GetChannelName(VvcChannelHandle channelHandle, char **name, size_t *nameLen);

VvcStatus VVCLIB_GetChannelPriority(VvcChannelHandle channelHandle, uint32 *priority);

VvcStatus VVCLIB_GetChannelSessionId(VvcChannelHandle channelHandle, int32 *sessionId);

VvcStatus VVCLIB_GetChannelPerfCounters(VvcChannelHandle channelHandle,
                                        VvcPerfCounters *perfCounters);

VvcStatus VVCLIB_Send(VvcChannelHandle channelHandle, uint32 flags, uint32 reserved, uint8 *buf,
                      size_t len, void *msgClientData, uint32 *msgId);

VvcStatus VVCLIB_RecvComplete(VvcChannelHandle channelHandle, uint8 *buf);

VvcStatus VVCLIB_GetConnectSessionId(void *connectionCookie, int32 *sessionId);

VvcStatus VVCLIB_GetCurrentProcessSessionId(int32 *sessionId);

VvcStatus VVCLIB_GetInfo(VvcInfoType infoType, uint32 flags, void *param, size_t paramLen,
                         void *info, size_t *infoLen);

VvcStatus VVCLIB_GetInfoForInstance(VvcInstanceHandle handle, VvcInfoType infoType, uint32 flags,
                                    void *param, size_t paramLen, void *info, size_t *infoLen);

VvcStatus VVCLIB_RecvBuffer(VvcChannelHandle channelHandle, uint8 *buf, size_t len,
                            size_t recvMinimum);

VvcStatus VVCLIB_CancelRecvBuffer(VvcChannelHandle channelHandle);

VvcStatus VVCLIB_SetFeatureName(VvcListenerHandle listenerHandle, VvcChannelHandle channelHandle,
                                const char *featureName);

VvcStatus VVCLIB_OpenMsgChannel(VvcSessionId sessionId, VvcMsgChannelIdentity myIdentity,
                                VvcMsgChannelEvents *events, void *clientData,
                                VvcMsgChannelHandle *msgChannelHandle);

VvcStatus VVCLIB_CloseMsgChannel(VvcMsgChannelHandle msgChannelHandle);

VvcStatus VVCLIB_SendMsgChannel(VvcMsgChannelHandle msgChannelHandle, VvcMsgChannelGroupId groupId,
                                void *msg, size_t len);

VvcStatus VVCLIB_GetMsgChannelInfo(VvcSessionId sessionId, VvcMsgChannelGroupId vvcMsgChannelId,
                                   VvcMsgChannelInfo *info);

Bool VVCLIB_CompareSerializedIdToVvcSessionId(VvcSessionId sessionId, const char *serializedId);

Bool VVCLIB_IsLogLevelActive(VvcLogLevel logLevel);

VvcStatus VVCLIB_UpdatePeerAllowList(const char *peerFeatureAllowList);


// Performance counters related APIs --

typedef enum {
   /* Network performance counters for a session */
   VvcPerfTranBwUplink = 1,
   VvcPerfTranBwDownlink = 2,
   VvcPerfTranPktLossUplink = 3,
   VvcPerfTranPktLossDownlink = 4,
   VvcPerfTranRtt = 5,
   VvcPerfTranJitterUplink = 6,
   VvcPerfTranJitterDownlink = 7,
   VvcPerfTranSentBytes = 8,
   VvcPerfTranRecvedBytes = 9,
   VvcPerfTranSentPkts = 10,
   VvcPerfTranRecvedPkts = 11,
   VvcPerfTranTCPSentBytes = 12,
   VvcPerfTranTCPRecvedBytes = 13,
   VvcPerfTranUDPSentBytes = 14,
   VvcPerfTranUDPRecvedBytes = 15,
   VvcPerfTranReconnectCount = 16,

   /* Network performance counters for a channel */
   VvcPerfChannelSentBytes = 51,
   VvcPerfChannelRecvedBytes = 52,
   VvcPerfChannelSentPkts = 53,
   VvcPerfChannelRecvedPkts = 54,
   VvcPerfChannelOutBw = 55,
   VvcPerfChannelInBw = 56,
   VvcPerfChannelSendQTime = 57,


   /* VNC performance counters.
    *
    * TotalFrameCount / FrameRate is about the total number of frames sent which
    * includes both "dirty" frames and "quality boost" frames.
    *
    * Dirty frames are frame sends triggered by the content on the screen changing.
    * Quality boost frames are extra frames we send with spare bandwidth in order
    * to improve the image quality.
    */
   VvcPerfVncFrameRate = 101,
   VvcPerfVncPollRate = 102,
   VvcPerfVncFBCRate = 103,
   VvcPerfVncTotalFrameCount = 104,
   VvcPerfVncTotalPollCount = 105,
   VvcPerfVncTotalFbcCount = 106,
   VvcPerfVncDirtyFrameRate = 107,
   VvcPerfVncTotalDirtyFrameCount = 108,
   VvcPerfVncEncoderType = 109,

} PerfCounterType;


typedef enum {
   VVCLIB_PERF_SUCCESS = 0,
   VVCLIB_PERF_MODULE_EXISTS,
   VVCLIB_PERF_INVALID_DB,
   VVCLIB_PERF_INVALID_HANDLE,
   VVCLIB_PERF_INVALID_COUNTER,
   VVCLIB_PERF_INVALID_COUNTER_TYPE,
   VVCLIB_PERF_ERROR
} VvclibPerfError;

// Public APIs for perf counters database writers --

typedef uint32 PerfDbHandle;

// Module Type
typedef enum { VvcChannelConsumer = 0, VvcSessionUser = 1 } VvcPerfDbModuleType;


// Callback to get new values from the users of the perf counters database.

typedef void (*VvcPerfCountersDBGetValueCb)(PerfDbHandle handle, PerfCounterType ctr_type,
                                            void *currValue, uint64 timeDiffSinceLastUpdateMS,
                                            void *newValue, void *clientData, int *error);

VvclibPerfError VVCLIB_PerfCountersInit(VvcPerfDbModuleType moduleType, void *moduleVvcHandle,
                                        const char *moduleName,
                                        VvcPerfCountersDBGetValueCb callbackFn, void *clientData,
                                        PerfDbHandle *handle);
VvclibPerfError VVCLIB_PerfCountersUninit(PerfDbHandle handle);

VvclibPerfError VVCLIB_PerfCountersInitValueUint32(PerfDbHandle handle, PerfCounterType ctr_type,
                                                   uint32 value);
VvclibPerfError VVCLIB_PerfCountersInitValueUint64(PerfDbHandle handle, PerfCounterType ctr_type,
                                                   uint64 value);
VvclibPerfError VVCLIB_PerfCountersInitValueDouble(PerfDbHandle handle, PerfCounterType ctr_type,
                                                   double value);

VvclibPerfError VVCLIB_PerfCountersSetValueUint32(PerfDbHandle handle, PerfCounterType ctr_type,
                                                  uint32 value);
VvclibPerfError VVCLIB_PerfCountersSetValueUint64(PerfDbHandle handle, PerfCounterType ctr_type,
                                                  uint64 value);
VvclibPerfError VVCLIB_PerfCountersSetValueDouble(PerfDbHandle handle, PerfCounterType ctr_type,
                                                  double value);

VvclibPerfError VVCLIB_PerfCountersIncrementValueUint32(PerfDbHandle handle,
                                                        PerfCounterType ctr_type, uint32 value);
VvclibPerfError VVCLIB_PerfCountersIncrementValueUint64(PerfDbHandle handle,
                                                        PerfCounterType ctr_type, uint64 value);
VvclibPerfError VVCLIB_PerfCountersIncrementValueDouble(PerfDbHandle handle,
                                                        PerfCounterType ctr_type, double value);

VvclibPerfError VVCLIB_PerfCountersDecrementValueUint32(PerfDbHandle handle,
                                                        PerfCounterType ctr_type, uint32 value);
VvclibPerfError VVCLIB_PerfCountersDecrementValueUint64(PerfDbHandle handle,
                                                        PerfCounterType ctr_type, uint64 value);
VvclibPerfError VVCLIB_PerfCountersDecrementValueDouble(PerfDbHandle handle,
                                                        PerfCounterType ctr_type, double value);

VvclibPerfError VVCLIB_PerfCountersDelete(PerfDbHandle handle, PerfCounterType crt_type);

// Public APIs for perf counters database readers --
VvclibPerfError VVCLIB_PerfCountersGetHandle(const char *moduleName, PerfDbHandle *handle);

VvclibPerfError VVCLIB_PerfCountersGetValueUint32(PerfDbHandle handle, PerfCounterType ctr_type,
                                                  uint32 *value);
VvclibPerfError VVCLIB_PerfCountersGetValueUint64(PerfDbHandle handle, PerfCounterType ctr_type,
                                                  uint64 *value);
VvclibPerfError VVCLIB_PerfCountersGetValueDouble(PerfDbHandle handle, PerfCounterType ctr_type,
                                                  double *value);

// Public APIs for dynamically setting log levels
Bool VVCLIB_SetLogLevel(int32 sessionId, char *logLevel);
Bool VVCLIB_SetMemLogLevel(int32 sessionId, char *memLogLevel);

void VVCLIB_RawChanConnectCb(void *cbData, void *connectionCookie, AsyncSocket *asock);

typedef size_t (*BlastSessionMgr_StopAllSessionsCb)(int vdpConnectionResult);

// Structure for storing callbacks from BlastSessionMgr
typedef struct BlastSessionMgrCallbacks {

   BlastSessionMgr_StopAllSessionsCb stopAllSessions;

} BlastSessionMgrCallbacks;

// Public API for setting BlastSessionMgr callbacks in VVCLIB
void VVCLIB_SetBlastSessionMgrCallbacks(const BlastSessionMgrCallbacks *cb);

// Public API for calling 'stopAllSessions' callback from BlastSessionMgr
VvcStatus VVCLIB_StopAllSessions(int vdpConnectionResult, size_t *numSessionStopped);

#if defined(__cplusplus)
} // extern "C"
#endif

#endif // _VVCLIB_H
