/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */


#include "d3d11DeviceMock.h"
#include "d3d11DeviceContextMock.h"

MockD3D11DeviceContext::MockD3D11DeviceContext(MockD3D11Device *device) : m_device(device)
{
   if (m_device) {
      m_device->AddRef();
   }
}

MockD3D11DeviceContext::~MockD3D11DeviceContext()
{
   if (m_device) {
      m_device->Release();
   }
}

ULONG
MockD3D11DeviceContext::AddRef()
{
   return ++m_refCount;
}

ULONG
MockD3D11DeviceContext::Release()
{
   if (--m_refCount == 0) {
      delete this;
      return 0;
   }
   return m_refCount;
}

HRESULT
MockD3D11DeviceContext::QueryInterface(REFIID riid, void **ppvObject)
{
   if (riid == __uuidof(ID3D11DeviceContext) || riid == __uuidof(IUnknown)) {
      *ppvObject = static_cast<ID3D11DeviceContext *>(this);
      AddRef();
      return S_OK;
   }
   *ppvObject = nullptr;
   return E_NOINTERFACE;
}

void
MockD3D11DeviceContext::GetDevice(ID3D11Device **ppDevice)
{
   if (ppDevice && m_device) {
      *ppDevice = m_device;
      m_device->AddRef();
   }
}