<?xml version="1.0" encoding="utf-8"?>

<!--
*******************************************************************************
   Copyright (c) Omnissa, LLC. All rights reserved.
   This product is protected by copyright and intellectual property laws in the
   United States and other countries as well as by international treaties.
   - Omnissa Restricted

   CustomActions.wxi  (product: View Agent)

      WiX include for all custom actions included in this product.
*******************************************************************************
-->

<Include>

   <!-- Binary Files -->
   <Binary Id="vdmInstUtil.dll" SourceFile="$(var.GOBUILD_WINDOWS_INSTALLKIT_ROOT)\x64\vdmInstUtil.dll" />
   <Binary Id="driverInstUtil.dll" SourceFile="$(var.GOBUILD_WINDOWS_INSTALLKIT_ROOT)\x64\driverInstUtil.dll" />
   <Binary Id="_toolsinstutil.dll" SourceFile="$(var.ToolsInstDir)\toolsinstutil.dll" SuppressModularization="no" />
   <Binary Id="VMwareCustomActions.dll" SourceFile="$(var.GOBUILD_WINDOWS_INSTALLKIT_ROOT)\x64\VMwareCustomActions.dll" />
   <Binary Id="cacheMod.exe" SourceFile="$(var.GOBUILD_WINDOWS_INSTALLKIT_ROOT)\x86\cacheMod.exe" />
   <Binary Id="pcoip_perf_installer.dll" SourceFile="$(var.GOBUILD_PCOIP_SOFT_SERVER_ROOT)\win64\agent\pcoip_perf_installer64.dll" />

   <!-- Action Text -->
   <UI>
      <ProgressText Action="Advertise">!(loc.IDS_ACTIONTEXT_Advertising)</ProgressText>
      <ProgressText Action="AllocateRegistrySpace" Template="!(loc.IDS_ACTIONTEXT_FreeSpace)">!(loc.IDS_ACTIONTEXT_AllocatingRegistry)</ProgressText>
      <ProgressText Action="AppSearch" Template="!(loc.IDS_ACTIONTEXT_PropertySignature)">!(loc.IDS_ACTIONTEXT_SearchInstalled)</ProgressText>
      <ProgressText Action="BindImage" Template="!(loc.IDS_ACTIONTEXT_File)">!(loc.IDS_ACTIONTEXT_BindingExes)</ProgressText>
      <ProgressText Action="CCPSearch">!(loc.IDS_ACTIONTEXT_UnregisterModules)</ProgressText>
      <ProgressText Action="CostFinalize">!(loc.IDS_ACTIONTEXT_ComputingSpace3)</ProgressText>
      <ProgressText Action="CostInitialize">!(loc.IDS_ACTIONTEXT_ComputingSpace)</ProgressText>
      <ProgressText Action="CreateFolders" Template="!(loc.IDS_ACTIONTEXT_Folder)">!(loc.IDS_ACTIONTEXT_CreatingFolders)</ProgressText>
      <ProgressText Action="CreateShortcuts" Template="!(loc.IDS_ACTIONTEXT_Shortcut)">!(loc.IDS_ACTIONTEXT_CreatingShortcuts)</ProgressText>
      <ProgressText Action="DeleteServices" Template="!(loc.IDS_ACTIONTEXT_Service)">!(loc.IDS_ACTIONTEXT_DeletingServices)</ProgressText>
      <ProgressText Action="DuplicateFiles" Template="!(loc.IDS_ACTIONTEXT_FileDirectorySize)">!(loc.IDS_ACTIONTEXT_CreatingDuplicate)</ProgressText>
      <ProgressText Action="FileCost">!(loc.IDS_ACTIONTEXT_ComputingSpace2)</ProgressText>
      <ProgressText Action="FindRelatedProducts" Template="!(loc.IDS_ACTIONTEXT_FoundApp)">!(loc.IDS_ACTIONTEXT_SearchForRelated)</ProgressText>
      <ProgressText Action="GenerateScript" Template="!(loc.IDS_ACTIONTEXT_1)">!(loc.IDS_ACTIONTEXT_GeneratingScript)</ProgressText>
      <ProgressText Action="InstallAdminPackage" Template="!(loc.IDS_ACTIONTEXT_FileDirSize)">!(loc.IDS_ACTIONTEXT_CopyingNetworkFiles)</ProgressText>
      <ProgressText Action="InstallFiles" Template="!(loc.IDS_ACTIONTEXT_FileDirSize2)">!(loc.IDS_ACTIONTEXT_CopyingNewFiles)</ProgressText>
      <ProgressText Action="InstallODBC">!(loc.IDS_ACTIONTEXT_InstallODBC)</ProgressText>
      <ProgressText Action="InstallSFPCatalogFile" Template="!(loc.IDS_ACTIONTEXT_FileDependencies)">!(loc.IDS_ACTIONTEXT_InstallingSystemCatalog)</ProgressText>
      <ProgressText Action="InstallServices" Template="!(loc.IDS_ACTIONTEXT_Service2)">!(loc.IDS_ACTIONTEXT_InstallServices)</ProgressText>
      <ProgressText Action="InstallValidate">!(loc.IDS_ACTIONTEXT_Validating)</ProgressText>
      <ProgressText Action="LaunchConditions">!(loc.IDS_ACTIONTEXT_EvaluateLaunchConditions)</ProgressText>
      <ProgressText Action="MigrateFeatureStates" Template="!(loc.IDS_ACTIONTEXT_Application)">!(loc.IDS_ACTIONTEXT_MigratingFeatureStates)</ProgressText>
      <ProgressText Action="MoveFiles" Template="!(loc.IDS_ACTIONTEXT_FileDirSize3)">!(loc.IDS_ACTIONTEXT_MovingFiles)</ProgressText>
      <ProgressText Action="PatchFiles" Template="!(loc.IDS_ACTIONTEXT_FileDirSize4)">!(loc.IDS_ACTIONTEXT_PatchingFiles)</ProgressText>
      <ProgressText Action="ProcessComponents">!(loc.IDS_ACTIONTEXT_UpdateComponentRegistration)</ProgressText>
      <ProgressText Action="PublishComponents" Template="!(loc.IDS_ACTIONTEXT_ComponentIDQualifier)">!(loc.IDS_ACTIONTEXT_PublishingQualifiedComponents)</ProgressText>
      <ProgressText Action="PublishFeatures" Template="!(loc.IDS_ACTIONTEXT_FeatureColon)">!(loc.IDS_ACTIONTEXT_PublishProductFeatures)</ProgressText>
      <ProgressText Action="PublishProduct">!(loc.IDS_ACTIONTEXT_PublishProductInfo)</ProgressText>
      <ProgressText Action="RMCCPSearch">!(loc.IDS_ACTIONTEXT_SearchingQualifyingProducts)</ProgressText>
      <ProgressText Action="RegisterClassInfo" Template="!(loc.IDS_ACTIONTEXT_ClassId)">!(loc.IDS_ACTIONTEXT_RegisterClassServer)</ProgressText>
      <ProgressText Action="RegisterComPlus" Template="!(loc.IDS_ACTIONTEXT_AppIdAppTypeRSN)">!(loc.IDS_ACTIONTEXT_RegisteringComPlus)</ProgressText>
      <ProgressText Action="RegisterExtensionInfo" Template="!(loc.IDS_ACTIONTEXT_Extension2)">!(loc.IDS_ACTIONTEXT_RegisterExtensionServers)</ProgressText>
      <ProgressText Action="RegisterFonts" Template="!(loc.IDS_ACTIONTEXT_Font)">!(loc.IDS_ACTIONTEXT_RegisterFonts)</ProgressText>
      <ProgressText Action="RegisterMIMEInfo" Template="!(loc.IDS_ACTIONTEXT_ContentTypeExtension)">!(loc.IDS_ACTIONTEXT_RegisterMimeInfo)</ProgressText>
      <ProgressText Action="RegisterProduct" Template="!(loc.IDS_ACTIONTEXT_1b)">!(loc.IDS_ACTIONTEXT_RegisteringProduct)</ProgressText>
      <ProgressText Action="RegisterProgIdInfo" Template="!(loc.IDS_ACTIONTEXT_ProgID2)">!(loc.IDS_ACTIONTEXT_RegisteringProgIdentifiers)</ProgressText>
      <ProgressText Action="RegisterTypeLibraries" Template="!(loc.IDS_ACTIONTEXT_LibId)">!(loc.IDS_ACTIONTEXT_RegisterTypeLibs)</ProgressText>
      <ProgressText Action="RegisterUser" Template="!(loc.IDS_ACTIONTEXT_1c)">!(loc.IDS_ACTIONTEXT_RegUser)</ProgressText>
      <ProgressText Action="RemoveDuplicateFiles" Template="!(loc.IDS_ACTIONTEXT_FileDir)">!(loc.IDS_ACTIONTEXT_RemovingDuplicates)</ProgressText>
      <ProgressText Action="RemoveEnvironmentStrings" Template="!(loc.IDS_ACTIONTEXT_NameValueAction2)">!(loc.IDS_ACTIONTEXT_UpdateEnvironmentStrings)</ProgressText>
      <ProgressText Action="RemoveExistingProducts" Template="!(loc.IDS_ACTIONTEXT_AppCommandLine)">!(loc.IDS_ACTIONTEXT_RemoveApps)</ProgressText>
      <ProgressText Action="RemoveFiles" Template="!(loc.IDS_ACTIONTEXT_FileDir2)">!(loc.IDS_ACTIONTEXT_RemovingFiles)</ProgressText>
      <ProgressText Action="RemoveFolders" Template="!(loc.IDS_ACTIONTEXT_Folder1)">!(loc.IDS_ACTIONTEXT_RemovingFolders)</ProgressText>
      <ProgressText Action="RemoveIniValues" Template="!(loc.IDS_ACTIONTEXT_FileSectionKeyValue)">!(loc.IDS_ACTIONTEXT_RemovingIni)</ProgressText>
      <ProgressText Action="RemoveODBC">!(loc.IDS_ACTIONTEXT_RemovingODBC)</ProgressText>
      <ProgressText Action="RemoveRegistryValues" Template="!(loc.IDS_ACTIONTEXT_KeyName)">!(loc.IDS_ACTIONTEXT_RemovingRegistry)</ProgressText>
      <ProgressText Action="RemoveShortcuts" Template="!(loc.IDS_ACTIONTEXT_Shortcut1)">!(loc.IDS_ACTIONTEXT_RemovingShortcuts)</ProgressText>
      <ProgressText Action="Rollback" Template="!(loc.IDS_ACTIONTEXT_1d)">!(loc.IDS_ACTIONTEXT_RollingBack)</ProgressText>
      <ProgressText Action="RollbackCleanup" Template="!(loc.IDS_ACTIONTEXT_File2)">!(loc.IDS_ACTIONTEXT_RemovingBackup)</ProgressText>
      <ProgressText Action="SelfRegModules" Template="!(loc.IDS_ACTIONTEXT_FileFolder)">!(loc.IDS_ACTIONTEXT_RegisteringModules)</ProgressText>
      <ProgressText Action="SelfUnregModules" Template="!(loc.IDS_ACTIONTEXT_FileFolder2)">!(loc.IDS_ACTIONTEXT_UnregisterModules)</ProgressText>
      <ProgressText Action="SetODBCFolders">!(loc.IDS_ACTIONTEXT_InitializeODBCDirs)</ProgressText>
      <ProgressText Action="StartServices" Template="!(loc.IDS_ACTIONTEXT_Service3)">!(loc.IDS_ACTIONTEXT_StartingServices)</ProgressText>
      <ProgressText Action="StopServices" Template="!(loc.IDS_ACTIONTEXT_Service4)">!(loc.IDS_ACTIONTEXT_StoppingServices)</ProgressText>
      <ProgressText Action="UnmoveFiles" Template="!(loc.IDS_ACTIONTEXT_FileDir3)">!(loc.IDS_ACTIONTEXT_RemovingMoved)</ProgressText>
      <ProgressText Action="UnpublishComponents" Template="!(loc.IDS_ACTIONTEXT_ComponentIdQualifier2)">!(loc.IDS_ACTIONTEXT_UnpublishQualified)</ProgressText>
      <ProgressText Action="UnpublishFeatures" Template="!(loc.IDS_ACTIONTEXT_Feature)">!(loc.IDS_ACTIONTEXT_UnpublishProductFeatures)</ProgressText>
      <ProgressText Action="UnpublishProduct">!(loc.IDS_ACTIONTEXT_UnpublishingProductInfo)</ProgressText>
      <ProgressText Action="UnregisterClassInfo" Template="!(loc.IDS_ACTIONTEXT_ClsID)">!(loc.IDS_ACTIONTEXT_UnregisterClassServers)</ProgressText>
      <ProgressText Action="UnregisterComPlus" Template="!(loc.IDS_ACTIONTEXT_AppId)">!(loc.IDS_ACTIONTEXT_UnregisteringComPlus)</ProgressText>
      <ProgressText Action="UnregisterExtensionInfo" Template="!(loc.IDS_ACTIONTEXT_Extension)">!(loc.IDS_ACTIONTEXT_UnregisterExtensionServers)</ProgressText>
      <ProgressText Action="UnregisterFonts" Template="!(loc.IDS_ACTIONTEXT_Font2)">!(loc.IDS_ACTIONTEXT_UnregisteringFonts)</ProgressText>
      <ProgressText Action="UnregisterMIMEInfo" Template="!(loc.IDS_ACTIONTEXT_ContentTypeExtension2)">!(loc.IDS_ACTIONTEXT_UnregisteringMimeInfo)</ProgressText>
      <ProgressText Action="UnregisterProgIdInfo" Template="!(loc.IDS_ACTIONTEXT_ProgID)">!(loc.IDS_ACTIONTEXT_UnregisteringProgramIds)</ProgressText>
      <ProgressText Action="UnregisterTypeLibraries" Template="!(loc.IDS_ACTIONTEXT_Libid2)">!(loc.IDS_ACTIONTEXT_UnregTypeLibs)</ProgressText>
      <ProgressText Action="VM_AddLDAPMachineEntry">!(loc.IDS_ACTIONTEXT_ServerConfig)</ProgressText>
      <ProgressText Action="VM_BlastUDPReservePorts">!(loc.IDS_ACTIONTEXT_BlastUDPInstall)</ProgressText>
      <ProgressText Action="VM_BlastUDPReservePorts_RB">!(loc.IDS_ACTIONTEXT_BlastUDPUninstall)</ProgressText>
      <ProgressText Action="VM_BlastUDPReservePorts_SideChannel">!(loc.IDS_ACTIONTEXT_BlastUDPInstall)</ProgressText>
      <ProgressText Action="VM_BlastUDPReservePorts_SideChannel_RB">!(loc.IDS_ACTIONTEXT_BlastUDPUninstall)</ProgressText>
      <ProgressText Action="VM_BlastUDPReleasePorts">!(loc.IDS_ACTIONTEXT_BlastUDPUninstall)</ProgressText>
      <ProgressText Action="VM_BlastUDPReleasePorts_RB">!(loc.IDS_ACTIONTEXT_BlastUDPInstall)</ProgressText>
      <ProgressText Action="VM_BlastUDPReleasePorts_SideChannel">!(loc.IDS_ACTIONTEXT_BlastUDPUninstall)</ProgressText>
      <ProgressText Action="VM_BlastUDPReleasePorts_SideChannel_RB">!(loc.IDS_ACTIONTEXT_BlastUDPInstall)</ProgressText>
      <ProgressText Action="VM_CfgUserInit">!(loc.ConfigUserInit)</ProgressText>
      <ProgressText Action="VM_WaitForPairing">!(loc.VM_WaitForpairing_ProgressText)</ProgressText>
      <ProgressText Action="VM_UnCfgUserInit">!(loc.UnconfigUserInit)</ProgressText>
      <ProgressText Action="WriteEnvironmentStrings" Template="!(loc.IDS_ACTIONTEXT_NameValueAction)">!(loc.IDS_ACTIONTEXT_EnvironmentStrings)</ProgressText>
      <ProgressText Action="WriteIniValues" Template="!(loc.IDS_ACTIONTEXT_FileSectionKeyValue2)">!(loc.IDS_ACTIONTEXT_WritingINI)</ProgressText>
      <ProgressText Action="WriteRegistryValues" Template="!(loc.IDS_ACTIONTEXT_KeyNameValue)">!(loc.IDS_ACTIONTEXT_WritingRegistry)</ProgressText>
   </UI>


   <!-- Custom Actions -->

   <!-- These are utility properties that will help make some custom action
        conditions easier to understand -->
   <CustomAction Id="SetProperty_INSTALLING"
                 Property="INSTALLING"
                 Value="1" />

   <CustomAction Id="SetProperty_UNINSTALLING"
                 Property="UNINSTALLING"
                 Value="1" />

   <CustomAction Id="VM_SetProperty_REINSTALL"
                 Property="REINSTALL"
                 Value="[VDM_REINSTALL]" />

   <CustomAction Id="VM_SetProperty_REINSTALLMODE"
                 Property="REINSTALLMODE"
                 Value="[VDM_REINSTALLMODE]" />

   <CustomAction Id="VM_PopulateUpgradeTable"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMPopulateUpgradeTable"
                 Return="check"
                 Execute="immediate" />

   <CustomAction Id="VM_PopulateCustomActionDetails"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMPopulateCustomActionDetails"
                 Return="check"
                 Execute="immediate" />

   <CustomAction Id="VM_GetInstalledProductVersion_NEWPRODUCTFOUND_SD"
                 Property="VMGetInstalledProductVersion"
                 Value="[NEWPRODUCTFOUND]" />

   <CustomAction Id="VM_GetInstalledProductVersion_SAMEVERSIONDETECTED_SD"
                 Property="VMGetInstalledProductVersion"
                 Value="[SAMEVERSIONDETECTED]" />

   <CustomAction Id="VM_GetInstalledProductVersion"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMGetInstalledProductVersion"
                 Return="ignore"
                 Execute="immediate" />

   <CustomAction Id="SetARPINSTALLLOCATION"
                 Property="ARPINSTALLLOCATION"
                 Value="[INSTALLDIR]" />

   <CustomAction Id="SetVMWARE_AGENT_INSTALLPATH_CERT"
                 Property="VMWARE_AGENT_INSTALLPATH_CERT"
                 Value="[VMWARE_AGENT_INSTALLPATH]\cert\" />

   <CustomAction Id="VM_GetInstalledFeatureState_ThinPrint_SD"
                 Property="VMGetInstalledFeatureState"
                 Value="[OLDPRODUCTFOUND];ThinPrint" />

   <CustomAction Id="VM_GetInstalledFeatureState_ThinPrint"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMGetInstalledFeatureState"
                 Execute="immediate"
                 Return="check" />

   <CustomAction Id="VM_GetInstalledFeatureState_SVI_SD"
                 Property="VMGetInstalledFeatureState"
                 Value="[OLDPRODUCTFOUND];SVIAgent" />

   <CustomAction Id="VM_GetInstalledFeatureState_SVI"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMGetInstalledFeatureState"
                 Execute="immediate"
                 Return="check" />

   <CustomAction Id="VM_SetEnablePrintRedir"
                 Property="VMEnablePrintRedir"
                 Value="1" />

   <CustomAction Id="VM_EnablePrintRedir_SD"
                 Property="VMEnableFeature"
                 Value="$(var.VMWPrint_FeatureName)" />

   <CustomAction Id="VM_EnablePrintRedir"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMEnableFeature"
                 Execute="immediate"
                 Return="check" />

   <CustomAction Id="VM_SetIsAgentDCT"
                 Property="IS_AGENT_DCT.F6F7242F_9526_45AB_827C_CF4FA2CA9302"
                 Value="[IS_AGENT_DCT]" />

   <CustomAction Id="VM_AddRegInterceptEnabled32Wow"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMRegAppendDelimString"
                 Return="check"
                 Impersonate="no"
                 Execute="deferred" />

   <CustomAction Id="VM_AddRegInterceptEnabled32Wow_RB"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMRegAppendDelimString"
                 Return="check"
                 Execute="rollback" />

   <CustomAction Id="VM_AddRegInterceptEnabled32Wow_RB_SD"
                 Property="VM_AddRegInterceptEnabled32Wow_RB"
                 Value="[RdpVcBridgeRegPathWow];InterceptEnabled; ;1;true" />

   <CustomAction Id="VM_AddRegInterceptEnabled32Wow_SD"
                 Property="VM_AddRegInterceptEnabled32Wow"
                 Value="[RdpVcBridgeRegPathWow];InterceptEnabled; ;1;true" />

   <CustomAction Id="VM_AddRegInterceptEnabled"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMRegAppendDelimString"
                 Return="check"
                 Impersonate="no"
                 Execute="deferred" />

   <CustomAction Id="VM_AddRegInterceptEnabled_RB"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMRegRemoveDelimString"
                 Return="check"
                 Execute="rollback" />

   <CustomAction Id="VM_AddRegInterceptEnabled_RB_SD"
                 Property="VM_AddRegInterceptEnabled_RB"
                 Value="[RdpVcBridgeRegPath];InterceptEnabled; ;1;true" />

   <CustomAction Id="VM_AddRegInterceptEnabled_SD"
                 Property="VM_AddRegInterceptEnabled"
                 Value="[RdpVcBridgeRegPath];InterceptEnabled; ;1;true" />

   <CustomAction Id="VM_AddRegInterceptWhiteList32Wow"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMRegAppendDelimString"
                 Return="check"
                 Impersonate="no"
                 Execute="deferred" />

   <CustomAction Id="VM_AddRegInterceptWhiteList32Wow_RB"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMRegAppendDelimString"
                 Return="check"
                 Execute="rollback" />

   <CustomAction Id="VM_AddRegInterceptWhiteList32Wow_RB_SD"
                 Property="VM_AddRegInterceptWhiteList32Wow_RB"
                 Value="[RdpVcBridgeRegPathWow];Intercept-Lync; ;1;true" />

   <CustomAction Id="VM_AddRegInterceptWhiteList32Wow_SD"
                 Property="VM_AddRegInterceptWhiteList32Wow"
                 Value="[RdpVcBridgeRegPathWow];Intercept-Lync; ;1;true" />

   <CustomAction Id="VM_AddRegInterceptWhiteList"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMRegAppendDelimString"
                 Return="check"
                 Impersonate="no"
                 Execute="deferred" />

   <CustomAction Id="VM_RunEVDConfiguration_SD"
                 Property="VM_RunEVDConfiguration"
                 Value="[#EVDConfigurationAPI.dll]" />

   <CustomAction Id="VM_RunEVDConfiguration"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMRunEVDConfiguration"
                 Return="check"
                 Impersonate="no"
                 Execute="deferred" />

   <CustomAction Id="VM_AddRegInterceptWhiteList_RB"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMRegRemoveDelimString"
                 Return="check"
                 Execute="rollback" />

   <CustomAction Id="VM_AddRegInterceptWhiteList_RB_SD"
                 Property="VM_AddRegInterceptWhiteList_RB"
                 Value="[RdpVcBridgeRegPath];Intercept-Lync; ;1;true" />

   <CustomAction Id="VM_AddRegInterceptWhiteList_SD"
                 Property="VM_AddRegInterceptWhiteList"
                 Value="[RdpVcBridgeRegPath];Intercept-Lync; ;1;true" />

   <CustomAction Id="VM_AppSearch"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMAppSearch"
                 Return="check"
                 Execute="firstSequence" />

   <CustomAction Id="VM_CheckForClientSession"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMCheckForClientSession"
                 Return="check"
                 Execute="immediate" />

   <CustomAction Id="VM_CheckReboot"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMCheckReboot"
                 Return="check"
                 Execute="immediate" />

   <CustomAction Id="VM_CheckRebootForVMwareComponents"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMCheckRebootForVMwareComponents"
                 Return="check"
                 Execute="immediate" />

   <CustomAction Id="HZ_MigrateCertStore"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="HZMigrateCertStore"
                 Return="ignore"
                 Execute="deferred" />

   <CustomAction Id="VM_CheckRunningInVm"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMCheckRunningInVm"
                 Return="check"
                 Execute="firstSequence" />

   <CustomAction Id="VM_SetIsAzureManagedDeployment"
                 Property="IsAzureManagedDeployment"
                 Value="1" />

   <!-- "0" means virtual machine and "1" means physical machine -->
   <Property Id="VM_TYPE_REG" Value="0" />
   <CustomAction Id="VM_SetVM_TYPE_REG"
                 Property="VM_TYPE_REG"
                 Value="1" />

   <CustomAction Id="VM_DeleteOldLogs"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMDeleteOldLogs"
                 Return="ignore"
                 Impersonate="no"
                 Execute="deferred" />

   <CustomAction Id="VM_CheckWindowsUpdateProgress"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMCheckWindowsUpdateProgress"
                 Return="check"
                 Execute="immediate" />

   <CustomAction Id="VM_CheckWindowsUpdateAndRestartPending"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMCheckWindowsUpdateAndRestartPending"
                 Return="check"
                 Execute="immediate" />

   <CustomAction Id="VM_UpdateARPDisplayVersion_SD"
                 Property="VM_UpdateARPDisplayVersion"
                 Value="[ProductVersion].[BuildNumber] ([ProductVersionString])" />

   <CustomAction Id="VM_UpdateARPDisplayVersion"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMUpdateARPDisplayVersion"
                 Return="check"
                 Impersonate="no"
                 Execute="deferred" />

   <CustomAction Id="VM_CheckWindowsVersion"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMCheckWindowsVersion"
                 Return="check"
                 Execute="immediate" />

   <CustomAction Id="VM_CheckSupportedHznviddVersion"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMCheckSupportedHznviddVersion"
                 Return="check"
                 Execute="immediate" />

   <CustomAction Id="VM_CheckWindowsWVD"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMCheckWindowsWVD"
                 Return="check"
                 Execute="immediate" />

   <CustomAction Id="VM_IsTeraHostCardPresent"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMIsTeraHostCardPresent"
                 Return="check"
                 Execute="immediate" />

   <CustomAction Id="VM_ClearVMREBOOT"
                 Property="VMREBOOT"
                 Value=""
                 Execute="firstSequence" />

   <CustomAction Id="VM_CheckSpoolerServiceRunning"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMCheckSpoolerServiceRunning"
                 Return="check"
                 Execute="immediate" />

   <?define FrameworkOpenFirewallArgs=[VersionNT];advfirewall firewall add rule dir=in localport=32111 name="Omnissa Horizon Framework" protocol=TCP profile=any action=allow?>
   <?define FrameworkCloseFirewallArgs=[VersionNT];advfirewall firewall delete rule name="Omnissa Horizon Framework"?>
   <CustomAction Id="VM_CloseFrameworkChannel"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMCloseFirewall2"
                 Return="ignore"
                 Impersonate="no"
                 Execute="deferred" />

   <CustomAction Id="VM_CloseFrameworkChannel_RB"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMOpenFirewall2"
                 Return="ignore"
                 Impersonate="no"
                 Execute="rollback" />

   <CustomAction Id="VM_CloseFrameworkChannel_RB_SetData"
                 Property="VM_CloseFrameworkChannel_RB"
                 Value="$(var.FrameworkOpenFirewallArgs)" />

   <CustomAction Id="VM_CloseFrameworkChannel_SetData"
                 Property="VM_CloseFrameworkChannel"
                 Value="$(var.FrameworkCloseFirewallArgs)" />

   <?define PCoIPOpenFirewallTCPArgs=[VersionNT];advfirewall firewall add rule dir=in program="[TERADICI_PCOIP_SERVER_X64]pcoip_server_win32.exe" name="Omnissa PCoIP Server" protocol=TCP profile=any action=allow?>
   <?define PCoIPOpenFirewallUDPArgs=[VersionNT];advfirewall firewall add rule dir=in program="[TERADICI_PCOIP_SERVER_X64]pcoip_server_win32.exe" name="Omnissa PCoIP Server" protocol=UDP profile=any action=allow?>
   <?define PCoIPCloseFirewallArgs=[VersionNT];advfirewall firewall delete rule name="Omnissa PCoIP Server"?>
   <CustomAction Id="VM_ClosePCoIPFirewall"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMCloseFirewall2"
                 Return="ignore"
                 Impersonate="no"
                 Execute="deferred" />

   <CustomAction Id="VM_ClosePCoIPFirewall_TCP_RB"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMOpenFirewall2"
                 Return="ignore"
                 Impersonate="no"
                 Execute="rollback" />

   <CustomAction Id="VM_ClosePCoIPFirewall_UDP_RB"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMOpenFirewall2"
                 Return="ignore"
                 Impersonate="no"
                 Execute="rollback" />

   <CustomAction Id="VM_ClosePCoIPFirewall_TCP_RB_SD"
                 Property="VM_ClosePCoIPFirewall_TCP_RB"
                 Value="$(var.PCoIPOpenFirewallTCPArgs)" />

   <CustomAction Id="VM_ClosePCoIPFirewall_UDP_RB_SD"
                 Property="VM_ClosePCoIPFirewall_UDP_RB"
                 Value="$(var.PCoIPOpenFirewallUDPArgs)" />

   <CustomAction Id="VM_ClosePCoIPFirewall_SD"
                 Property="VM_ClosePCoIPFirewall"
                 Value="$(var.PCoIPCloseFirewallArgs)" />

   <CustomAction Id="VM_DeleteLogs"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMDeleteLogs"
                 Return="check"
                 Impersonate="no"
                 Execute="deferred" />

   <CustomAction Id="VM_DeleteLogs_SD"
                 Property="VM_DeleteLogs"
                 Value="[VDM_LOGS]" />

   <CustomAction Id="VM_SetNGVC_INSTALLING"
                 Property="NGVC_INSTALLING"
                 Value="1" />

   <CustomAction Id="VM_DisableStandbyMode"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMSwapRegistryDwordValues"
                 Return="check"
                 Impersonate="no"
                 Execute="deferred" />

   <CustomAction Id="VM_DisableStandbyMode_RB"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMSwapRegistryDwordValues"
                 Return="ignore"
                 Impersonate="no"
                 Execute="rollback" />

   <CustomAction Id="VM_DisableStandbyMode_RB_SD"
                 Property="VM_DisableStandbyMode_RB"
                 Value="-1;[PowerManagementPath];[PowerManagementPathBackup];Attributes;112" />

   <CustomAction Id="VM_DisableStandbyMode_SD"
                 Property="VM_DisableStandbyMode"
                 Value="1;[PowerManagementPath];[PowerManagementPathBackup];Attributes;112;#112" />

   <CustomAction Id="VM_EnableStandbyMode"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMSwapRegistryDwordValues"
                 Return="check"
                 Impersonate="no"
                 Execute="deferred" />

   <CustomAction Id="VM_EnableStandbyMode_RB"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMSwapRegistryDwordValues"
                 Return="ignore"
                 Impersonate="no"
                 Execute="rollback" />

   <CustomAction Id="VM_EnableStandbyMode_RB_SD"
                 Property="VM_EnableStandbyMode_RB"
                 Value="-1;[PowerManagementPath];[PowerManagementPathBackup];Attributes;112;[POWERMANAGEMENT_STATUS]" />

   <CustomAction Id="VM_EnableStandbyMode_SD"
                 Property="VM_EnableStandbyMode"
                 Value="1;[PowerManagementPath];[PowerManagementPathBackup];Attributes;112" />

   <CustomAction Id="VM_InstallWsnmWinlogonNotificationHandler"
                 BinaryKey="VMwareCustomActions.dll"
                 DllEntry="VMInstallWsnmWinlogonNotificationHandler"
                 Return="check"
                 Impersonate="no"
                 Execute="deferred" />

   <CustomAction Id="VM_InstallWsnmWinlogonNotificationHandlerUninstall"
                 BinaryKey="VMwareCustomActions.dll"
                 DllEntry="VMUninstallWsnmWinlogonNotificationHandler"
                 Return="check"
                 Impersonate="no"
                 Execute="deferred" />

   <CustomAction Id="VM_VMLMHandlersUninstall"
                 BinaryKey="VMwareCustomActions.dll"
                 DllEntry="VMUninstallVMLMHandlers"
                 Return="ignore"
                 Impersonate="no"
                 Execute="deferred" />

   <CustomAction Id="VM_MessageBox"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMMessageBox"
                 Return="check"
                 Execute="firstSequence" />

   <CustomAction Id="VM_MustRebootCheck"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMCheckReboot"
                 Return="check"
                 Execute="immediate" />

   <CustomAction Id="VM_OpenFrameworkChannel"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMOpenFirewall2"
                 Return="ignore"
                 Impersonate="no"
                 Execute="deferred" />

   <CustomAction Id="VM_OpenFrameworkChannel_RB"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMCloseFirewall2"
                 Return="ignore"
                 Impersonate="no"
                 Execute="rollback" />

   <CustomAction Id="VM_OpenFrameworkChannel_RB_SetData"
                 Property="VM_OpenFrameworkChannel_RB"
                 Value="$(var.FrameworkCloseFirewallArgs)" />

   <CustomAction Id="VM_OpenFrameworkChannel_SetData"
                 Property="VM_OpenFrameworkChannel"
                 Value="$(var.FrameworkOpenFirewallArgs)" />

   <CustomAction Id="VM_OpenPCoIPFirewall_TCP"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMOpenFirewall2"
                 Return="ignore"
                 Impersonate="no"
                 Execute="deferred" />

   <CustomAction Id="VM_OpenPCoIPFirewall_UDP"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMOpenFirewall2"
                 Return="ignore"
                 Impersonate="no"
                 Execute="deferred" />

   <CustomAction Id="VM_OpenPCoIPFirewall_RB"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMCloseFirewall2"
                 Return="ignore"
                 Impersonate="no"
                 Execute="rollback" />

   <CustomAction Id="VM_OpenPCoIPFirewall_RB_SD"
                 Property="VM_OpenPCoIPFirewall_RB"
                 Value="$(var.PCoIPCloseFirewallArgs)" />

   <CustomAction Id="VM_OpenPCoIPFirewall_TCP_SD"
                 Property="VM_OpenPCoIPFirewall_TCP"
                 Value="$(var.PCoIPOpenFirewallTCPArgs)" />

   <CustomAction Id="VM_OpenPCoIPFirewall_UDP_SD"
                 Property="VM_OpenPCoIPFirewall_UDP"
                 Value="$(var.PCoIPOpenFirewallUDPArgs)" />

   <CustomAction Id="VM_RegisterPcoipPerf64_SD"
                 Property="VM_RegisterPcoipPerf64"
                 Value="[TERADICI_PCOIP_SERVER_X64]pcoip_perf_provider64.dll" />

   <CustomAction Id="VM_RegisterPcoipPerf64"
                 BinaryKey="pcoip_perf_installer.dll"
                 DllEntry="PcoipPerfInstall"
                 Return="check"
                 Impersonate="no"
                 Execute="deferred" />

   <CustomAction Id="VM_RegisterPcoipPerf_RB"
                 BinaryKey="pcoip_perf_installer.dll"
                 DllEntry="PcoipPerfUninstall"
                 Return="ignore"
                 Impersonate="no"
                 Execute="rollback" />

   <CustomAction Id="VM_RemoveRegInterceptEnabled32Wow"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMRegRemoveDelimString"
                 Return="check"
                 Impersonate="no"
                 Execute="deferred" />

   <CustomAction Id="VM_RemoveRegInterceptEnabled32Wow_RB"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMRegAppendDelimString"
                 Return="check"
                 Execute="rollback" />

   <CustomAction Id="VM_RemoveRegInterceptEnabled32Wow_RB_SD"
                 Property="VM_RemoveRegInterceptEnabled32Wow_RB"
                 Value="[RdpVcBridgeRegPathWow];InterceptEnabled; ;1;true" />

   <CustomAction Id="VM_RemoveRegInterceptEnabled32Wow_SD"
                 Property="VM_RemoveRegInterceptEnabled32Wow"
                 Value="[RdpVcBridgeRegPathWow];InterceptEnabled; ;1;true" />

   <CustomAction Id="VM_RemoveRegInterceptEnabled"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMRegRemoveDelimString"
                 Return="check"
                 Impersonate="no"
                 Execute="deferred" />

   <CustomAction Id="VM_RemoveRegInterceptEnabled_RB"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMRegAppendDelimString"
                 Return="check"
                 Execute="rollback" />

   <CustomAction Id="VM_RemoveRegInterceptEnabled_RB_SD"
                 Property="VM_RemoveRegInterceptEnabled_RB"
                 Value="[RdpVcBridgeRegPath];InterceptEnabled; ;1;true" />

   <CustomAction Id="VM_RemoveRegInterceptEnabled_SD"
                 Property="VM_RemoveRegInterceptEnabled"
                 Value="[RdpVcBridgeRegPath];InterceptEnabled; ;1;true" />

   <CustomAction Id="VM_RemoveRegInterceptWhiteList32Wow"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMRegRemoveDelimString"
                 Return="check"
                 Impersonate="no"
                 Execute="deferred" />

   <CustomAction Id="VM_RemoveRegInterceptWhiteList32Wow_RB"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMRegAppendDelimString"
                 Return="check"
                 Execute="rollback" />

   <CustomAction Id="VM_RemoveRegInterceptWhiteList32Wow_RB_SD"
                 Property="VM_RemoveRegInterceptWhiteList32Wow_RB"
                 Value="[RdpVcBridgeRegPathWow];Intercept-Lync; ;1;true" />

   <CustomAction Id="VM_RemoveRegInterceptWhiteList32Wow_SD"
                 Property="VM_RemoveRegInterceptWhiteList32Wow"
                 Value="[RdpVcBridgeRegPathWow];Intercept-Lync; ;1;true" />

   <CustomAction Id="VM_RemoveRegInterceptWhiteList"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMRegRemoveDelimString"
                 Return="check"
                 Impersonate="no"
                 Execute="deferred" />

   <CustomAction Id="VM_RemoveRegInterceptWhiteList_RB"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMRegAppendDelimString"
                 Return="check"
                 Execute="rollback" />

   <CustomAction Id="VM_RemoveRegInterceptWhiteList_RB_SD"
                 Property="VM_RemoveRegInterceptWhiteList_RB"
                 Value="[RdpVcBridgeRegPath];Intercept-Lync; ;1;true" />

   <CustomAction Id="VM_RemoveRegInterceptWhiteList_SD"
                 Property="VM_RemoveRegInterceptWhiteList"
                 Value="[RdpVcBridgeRegPath];Intercept-Lync; ;1;true" />

   <CustomAction Id="VM_RemoveRegistryKeys"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMRemoveRegKeys"
                 Return="ignore"
                 Impersonate="no"
                 Execute="deferred" />

   <CustomAction Id="VM_RemoveRegistryKeys_SD"
                 Property="VM_RemoveRegistryKeys"
                 Value="HKLM:SOFTWARE\Omnissa\Horizon\Blast;HKLM:SOFTWARE\Omnissa\Horizon\Agent;HKLM:SOFTWARE\Omnissa\Horizon;HKLM:SOFTWARE\Omnissa\Horizon\RdpVcBridge;HKLM:SOFTWARE\Omnissa\Horizon\Instant Clone Agent\nga;HKLM:SOFTWARE\Omnissa\Horizon\Instant Clone Agent;HKLM:SOFTWARE\Wow6432Node\Omnissa\Horizon\RdpVcBridge;HKLM:Software\Omnissa\Horizon\Installer;HKLM:SOFTWARE\Omnissa\Horizon\SVGA DevTap" />

   <CustomAction Id="VM_SelfReg"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMSelfReg"
                 Return="ignore"
                 Impersonate="no"
                 Execute="commit" />

   <CustomAction Id="VM_SelfReg_RB"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMSelfUnreg"
                 Return="ignore"
                 Impersonate="no"
                 Execute="rollback" />

   <CustomAction Id="VM_SelfReg_RB_SD"
                 Property="VM_SelfReg_RB"
                 Value="[VM_SelfRegister]" />

   <CustomAction Id="VM_SelfReg_SD"
                 Property="VM_SelfReg"
                 Value="[VM_SelfRegister]" />

   <CustomAction Id="VM_SelfRegInit"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMSelfRegInit"
                 Return="ignore"
                 Execute="immediate" />

   <CustomAction Id="VM_SelfUnreg"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMSelfUnreg"
                 Return="ignore"
                 Impersonate="no"
                 Execute="deferred" />

   <CustomAction Id="VM_SelfUnreg_RB"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMSelfReg"
                 Return="ignore"
                 Impersonate="no"
                 Execute="rollback" />

   <CustomAction Id="VM_SelfUnreg_RB_SD"
                 Property="VM_SelfUnreg_RB"
                 Value="[VM_SelfUnregister]" />

   <CustomAction Id="VM_SelfUnreg_SD"
                 Property="VM_SelfUnreg"
                 Value="[VM_SelfUnregister]" />

   <CustomAction Id="VM_SetALLUSERS"
                 Property="ALLUSERS"
                 Value="1" />

   <CustomAction Id="VM_SetVersionNT_Win10"
                 Property="VersionNT"
                 Value="1000" />

   <CustomAction Id="VM_SetVersionNT64_Win10"
                 Property="VersionNT64"
                 Value="1000" />

   <!-- These properties are referenced by the call to VM_DeselectAgentFeaturesForIPv6 and  VM_DisableAgentFeaturesForIPv6 -->
   <Property Id="IPV6_FEATURE_DESELECTLIST"
             Value="SerialPortRedirection ScannerRedirection RTAV SmartCard" />

   <CustomAction Id="VM_DeselectAgentFeaturesForIPv6"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMDeselectAgentFeaturesForIPv6"
                 Return="check"
                 Execute="firstSequence" />

   <CustomAction Id="VM_SetForceDesktopAgent"
                 Property="VdmForceDesktopAgent"
                 Value="1" />

   <CustomAction Id="VM_SetIntelUnsDesc"
                 Property="INTEL_UNS_DESC.51BFA491_ECBD_4BB5_AA14_FDB94E755D13"
                 Value="[INTEL_UNS_DESC]" />

   <CustomAction Id="VM_SetOLDPRODUCTFOUND"
                 Property="OLDPRODUCTFOUND"
                 Value="1" />

   <CustomAction Id="VM_SetPcoipFeatureDesc"
                 Property="FEATURE_DESC_PCOIP"
                 Value="[FEATURE_DESC_PCOIP_PHYSICAL]" />

   <CustomAction Id="VM_SetPcoipUdpSizeThreshold"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMSetPcoipUdpSizeThreshold"
                 Return="check"
                 Execute="deferred" />

   <CustomAction Id="VM_SetPerfhostStartupType"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMSetServiceStartType"
                 Return="check"
                 Impersonate="no"
                 Execute="deferred" />

   <CustomAction Id="VM_SetPerfhostStartupType_RB"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMSetServiceStartType"
                 Return="ignore"
                 Impersonate="no"
                 Execute="rollback" />

   <CustomAction Id="VM_SetPerfhostStartupType_RB_SD"
                 Property="VM_SetPerfhostStartupType_RB"
                 Value="perfhost;SVC_STARTTYPE_INITIAL_PERFHOST" />

   <CustomAction Id="VM_SetPerfhostStartupType_SD"
                 Property="VM_SetPerfhostStartupType"
                 Value="perfhost;2" />

   <CustomAction Id="VM_SetSanPolicy"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMSetSanPolicy"
                 Return="check"
                 Impersonate="no"
                 Execute="deferred" />

   <CustomAction Id="VM_SetSanPolicy_RB"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMSetSanPolicy"
                 Return="ignore"
                 Impersonate="no"
                 Execute="rollback" />

   <CustomAction Id="VM_SetSanPolicy_RB_SD"
                 Property="VM_SetSanPolicy_RB"
                 Value="[VDS_SAN_POLICY]" />

   <CustomAction Id="VM_SetSanPolicy_SD"
                 Property="VM_SetSanPolicy"
                 Value="1" />

   <CustomAction Id="VM_SetToolsDir"
                 Property="VMWARE_TOOLS"
                 Value="[TOOLSDIR]" />

   <CustomAction Id="VM_SetToolsDir32"
                 Property="TOOLSDIR"
                 Value="[TOOLSDIR32]" />

   <CustomAction Id="VM_SetVCManaged"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMSetVCManaged"
                 Return="check"
                 Execute="firstSequence" />

   <!-- This property is not getting preserved across HCS on Azure upgrades,
        so this custom action is used only to preserve the broker/node address
        across agent upgrades on azure -->
   <CustomAction Id="VM_SetVDM_SERVER_NAME"
                 Property="VDM_SERVER_NAME"
                 Value="[AGENT_CONFIGURATION_BROKER_REG]" />

   <CustomAction Id="VM_SetVDM_VC_MANAGED_AGENT"
                 Property="VDM_VC_MANAGED_AGENT"
                 Value="0" />

   <CustomAction Id="VM_SetVDM_ConnectionServerRequired"
                 Property="VDM_ConnectionServerRequired"
                 Value="1" />

   <CustomAction Id="VM_SetVdmForceDesktopAgent"
                 Property="VdmForceDesktopAgent"
                 Value="[VDM_FORCE_DESKTOP_AGENT]"
                 Execute="firstSequence" />

   <CustomAction Id="VM_SetVDM_LOOPBACK_IP"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMSetVDM_LOOPBACK_IP_WithoutDualModeSupport"
                 Return="check"
                 Execute="immediate" />

   <CustomAction Id="VM_SetVMREBOOT"
                 Property="VMREBOOT"
                 Value="1" />

   <CustomAction Id="VM_SetREBOOTPROMPT"
                 Property="REBOOTPROMPT"
                 Value="Suppress" />

   <CustomAction Id="VM_SetServerOsValid"
                 Property="ServerOsValid"
                 Value="1" />

   <CustomAction Id="VM_SetVdmInstallerChecks"
                 Property="VDM_INSTALLER_CHECKS"
                 Value="[%VDM_INSTALLER_CHECKS]"
                 Execute="oncePerProcess" />

   <CustomAction Id="VM_SetVdmSkipBrokerRegistration"
                 Property="VDM_SKIP_BROKER_REGISTRATION"
                 Value="[%VDM_SKIP_BROKER_REGISTRATION]"
                 Execute="oncePerProcess" />

   <CustomAction Id="VM_SetWIX_ACCOUNT_ADMINISTRATORS_BLAST"
                 Property="WIX_ACCOUNT_ADMINISTRATORS.35BDBBE8_72E0_4EA1_AD92_DE934704FA87"
                 Value="[WIX_ACCOUNT_ADMINISTRATORS]" />

   <CustomAction Id="VM_SetWIX_ACCOUNT_USERS_BLAST"
                 Property="WIX_ACCOUNT_USERS.35BDBBE8_72E0_4EA1_AD92_DE934704FA87"
                 Value="[WIX_ACCOUNT_USERS]" />

   <CustomAction Id="VM_UnRegisterPcoipPerf"
                 BinaryKey="pcoip_perf_installer.dll"
                 DllEntry="PcoipPerfUninstall"
                 Return="check"
                 Impersonate="no"
                 Execute="deferred" />

   <CustomAction Id="VM_UnRegisterPcoipPerf64_RB_SD"
                 Property="VM_RegisterPcoipPerf64"
                 Value="[TERADICI_PCOIP_SERVER_X64]pcoip_perf_provider64.dll" />

   <CustomAction Id="VM_UnRegisterPcoipPerf64_RB"
                 BinaryKey="pcoip_perf_installer.dll"
                 DllEntry="PcoipPerfInstall"
                 Return="ignore"
                 Impersonate="no"
                 Execute="rollback" />

   <CustomAction Id="VM_ValidateConnectionServer"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMValidateConnectionServer"
                 Return="check"
                 Execute="immediate" />

   <CustomAction Id="VM_ValidateINSTALLDIR"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMValidateINSTALLDIR"
                 Return="check"
                 Execute="immediate" />

   <CustomAction Id="setAllUsersProfile2K"
                 Property="ALLUSERSPROFILE"
                 Value="[%ALLUSERSPROFILE]" />

   <CustomAction Id="setUserProfileNT"
                 Property="USERPROFILE"
                 Value="[%USERPROFILE]" />

   <CustomAction Id="VM_WriteFailedCustomActionDetailsToRegistry_RB"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMWriteFailedCustomActionDetailsToRegistry"
                 Return="ignore"
                 Impersonate="no"
                 Execute="rollback" />

   <CustomAction Id="VM_WriteFailedCustomActionDetailsToRegistry_RB_SD"
                 Property="VM_WriteFailedCustomActionDetailsToRegistry_RB"
                 Value="[MsiLogFileLocation];[VMCA_TABLE_CONTENTS]" />

   <Property Id="debugKeyPath" Value="SOFTWARE\Omnissa\Horizon\Debug\Agent" />
   <Property Id="debugKeyValue" Value="Rollback" />

   <CustomAction Id="VM_DebugRollback_SD"
                 Property="VM_DebugSimulateError"
                 Value="[debugKeyPath];[debugKeyValue];finalize" />

   <CustomAction Id="VM_DebugSimulateError"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMDebugSimulateError"
                 Return="check"
                 Impersonate="no"
                 Execute="deferred" />

   <!-- This action kicks off the url redirection 'helper' process after a succesful installation -->
   <CustomAction Id="VM_LaunchURLRedirectionHelper"
                 FileKey="horizon_url_protocol_launch_helper.exe"
                 ExeCommand=""
                 Execute="immediate"
                 Return="asyncNoWait" />

   <CustomAction Id="VM_SetUrlRedirectionWarnText"
                 Property="MessageBoxText"
                 Value="!(loc.MsgUrlRedirectionInstalled)" />

   <!-- end URL redirection actions -->

   <!-- This action kicks off the UNC redirection 'helper' process after a succesful installation -->
   <CustomAction Id="VM_LaunchUNCRedirectionService"
                 FileKey="uncRedirection_service_x86.exe"
                 ExeCommand=""
                 Execute="immediate"
                 Return="asyncNoWait" />

   <CustomAction Id="VM_SetUNCRedirectionWarnText"
                 Property="UNCMessageBoxText"
                 Value="!(loc.MsgUNCRedirectionInstalled)" />

   <!-- end UNC redirection actions -->

   <!-- This action is needed to help prevent upgrade failures from 7.3.2 -> 'newer' upgrades (bug 2015873) -->
   <CustomAction Id="VM_CacheMod_VM_RemoveNetworkContinuityRegValue"
                 BinaryKey="cacheMod.exe"
                 ExeCommand="[OLDPRODUCTFOUND] VM_RemoveNetworkContinuityRegValue"
                 Execute="immediate"
                 Return="ignore" />

   <!-- This action is needed to help prevent the following registry key
        from being removed at the end of a 7.x -> 8.x upgrade:
        [HKLM\Software\Omnissa\Horizon\Installer\Features_HorizonAgent] -->
   <CustomAction Id="VM_CacheMod_VM_RemoveFeatureStatesFromRegistry"
                 BinaryKey="cacheMod.exe"
                 ExeCommand="[HORIZON_7_AGENT_INSTALLED] VM_RemoveFeatureStatesFromRegistry"
                 Execute="immediate"
                 Return="ignore" />

   <!-- The following actions are needed to remove some Persona leftovers from 7.x -> 8.x upgrade -->

   <?define VP_PKGID="EDD77A6D_F039_46D3_8228_62AC9CE974A8" ?>

   <CustomAction Id="VM_SetOldVPProductCode_VPA"
                 Property="OLDVPPRODUCTCODE"
                 Value="[OLDPRODUCTFOUND]" />

   <CustomAction Id="VM_SetOldVPProductCode_VP"
                 Property="OLDVPPRODUCTCODE"
                 Value="[VPFOUND]" />

   <CustomAction Id="VM_CacheMod_UninstallRegistry.$(var.VP_PKGID)"
                 BinaryKey="cacheMod.exe"
                 ExeCommand="[OLDVPPRODUCTCODE] UninstallRegistry.$(var.VP_PKGID) $$_RTOLogonService.$(var.VP_PKGID)=2"
                 Execute="immediate"
                 Return="ignore" />

   <CustomAction Id="VM_CacheMod_UninstallRTOLogonDriver.$(var.VP_PKGID)"
                 BinaryKey="cacheMod.exe"
                 ExeCommand="[OLDVPPRODUCTCODE] UninstallRTOLogonDriver.$(var.VP_PKGID) $$_RTOLogon.sys.$(var.VP_PKGID)=2"
                 Execute="immediate"
                 Return="ignore" />

   <CustomAction Id="VM_CacheMod_UninstallRTOLogonService.$(var.VP_PKGID)"
                 BinaryKey="cacheMod.exe"
                 ExeCommand="[OLDVPPRODUCTCODE] UninstallRTOLogonService.$(var.VP_PKGID) $$_RTOLogonService.$(var.VP_PKGID)=2"
                 Execute="immediate"
                 Return="ignore" />

   <CustomAction Id="VM_CacheMod_UninstallWinlogonComp.$(var.VP_PKGID)"
                 BinaryKey="cacheMod.exe"
                 ExeCommand="[OLDVPPRODUCTCODE] UninstallWinlogonComp.$(var.VP_PKGID) &quot;$$_RTOLogonService.$(var.VP_PKGID)=2 And VersionNT &gt;= 600&quot;"
                 Execute="immediate"
                 Return="ignore" />

   <!-- For the View 7.0.2 release, Citibank has asked that we make sure all files are cleaned up
        after the View Agent uninstalls. These custom actions are authored as a quick-fix to remove
        any leftover files.
        XXX: This logic should be handled by the individual installer components, not the top-level
             View Agent installer code. This code 'will' introduce a bug if these drivers are ever
             shared with another installer someday. -->
   <CustomAction Id="VM_Remove_vmwvaudio_SD"
                 Property="VM_Remove_vmwvaudio"
                 Value="[SYS_DRIVERS]vmwvaudio.sys;0;TRUE" />

   <CustomAction Id="VM_Remove_vmwvaudio"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMRemoveFile"
                 Return="ignore"
                 Impersonate="no"
                 Execute="commit" />

   <CustomAction Id="VM_Remove_hznvaudioin_SD"
                 Property="VM_Remove_hznvaudioin"
                 Value="[SYS_DRIVERS]hznvaudioin.sys;0;TRUE" />

   <CustomAction Id="VM_Remove_hznvaudioin"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMRemoveFile"
                 Return="ignore"
                 Impersonate="no"
                 Execute="commit" />

   <CustomAction Id="VM_Remove_hznvwebcam_SD"
                 Property="VM_Remove_hznvwebcam"
                 Value="[SYS_DRIVERS]hznvwebcam.sys;0;TRUE" />

   <CustomAction Id="VM_Remove_hznvwebcam"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMRemoveFile"
                 Return="ignore"
                 Impersonate="no"
                 Execute="commit" />


   <!-- Blast UDP filter driver setup -->

   <!-- install -->
   <CustomAction Id="VM_BlastUDPReservePorts"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMBlastUDPReservePorts"
                 Return="check"
                 Impersonate="no"
                 Execute="deferred" />

   <CustomAction Id="VM_SetBlast_UDP_PORT_RANGE_RDSH"
                 Property="BLAST_UDP_PORT_RANGE"
                 Value="256" />

   <CustomAction Id="VM_SetBlast_UDP_PORT_RANGE_VDI"
                 Property="BLAST_UDP_PORT_RANGE"
                 Value="16" />

   <CustomAction Id="VM_BlastUDPReservePorts_SD"
                 Property="VM_BlastUDPReservePorts"
                 Value="[BLAST_UDP_STARTING_PORT];[BLAST_UDP_PORT_RANGE];!(loc.BlastUDPFirewallExceptionName)" />

   <CustomAction Id="VM_BlastUDPReservePorts_SideChannel"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMBlastUDPReservePorts_SideChannel"
                 Return="check"
                 Impersonate="no"
                 Execute="deferred" />

   <CustomAction Id="VM_BlastUDPReservePorts_SideChannel_SD"
                 Property="VM_BlastUDPReservePorts_SideChannel"
                 Value="[BLAST_UDP_STARTING_PORT];[BLAST_UDP_PORT_RANGE];!(loc.BlastUDPFirewallExceptionName);3;" />


   <!-- install-rollback -->
   <CustomAction Id="VM_BlastUDPReservePorts_RB"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMBlastUDPReleasePorts"
                 Return="ignore"
                 Impersonate="no"
                 Execute="rollback" />

   <CustomAction Id="VM_BlastUDPReservePorts_RB_SD"
                 Property="VM_BlastUDPReservePorts_RB"
                 Value="!(loc.BlastUDPFirewallExceptionName)" />

   <CustomAction Id="VM_BlastUDPReservePorts_SideChannel_RB"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMBlastUDPReleasePorts_SideChannel"
                 Return="ignore"
                 Impersonate="no"
                 Execute="rollback" />

   <CustomAction Id="VM_BlastUDPReservePorts_SideChannel_RB_SD"
                 Property="VM_BlastUDPReservePorts_SideChannel_RB"
                 Value="!(loc.BlastUDPFirewallExceptionName)" />

   <!-- uninstall -->
   <CustomAction Id="VM_BlastUDPReleasePorts"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMBlastUDPReleasePorts"
                 Return="ignore"
                 Impersonate="no"
                 Execute="deferred" />

   <CustomAction Id="VM_BlastUDPReleasePorts_SD"
                 Property="VM_BlastUDPReleasePorts"
                 Value="!(loc.BlastUDPFirewallExceptionName)" />

   <CustomAction Id="VM_BlastUDPReleasePorts_SideChannel"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMBlastUDPReleasePorts_SideChannel"
                 Return="ignore"
                 Impersonate="no"
                 Execute="deferred" />

   <CustomAction Id="VM_BlastUDPReleasePorts_SideChannel_SD"
                 Property="VM_BlastUDPReleasePorts_SideChannel"
                 Value="!(loc.BlastUDPFirewallExceptionName)" />

   <!-- uninstall-rollback -->
   <CustomAction Id="VM_BlastUDPReleasePorts_RB"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMBlastUDPReservePorts"
                 Return="ignore"
                 Impersonate="no"
                 Execute="rollback" />

   <CustomAction Id="VM_BlastUDPReleasePorts_RB_SD"
                 Property="VM_BlastUDPReleasePorts_RB"
                 Value="[BLAST_UDP_STARTING_PORT];[BLAST_UDP_PORT_RANGE];!(loc.BlastUDPFirewallExceptionName)" />

   <CustomAction Id="VM_BlastUDPReleasePorts_SideChannel_RB"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMBlastUDPReservePorts_SideChannel"
                 Return="ignore"
                 Impersonate="no"
                 Execute="rollback" />

   <CustomAction Id="VM_BlastUDPReleasePorts_SideChannel_RB_SD"
                 Property="VM_BlastUDPReleasePorts_SideChannel_RB"
                 Value="[BLAST_UDP_STARTING_PORT];[BLAST_UDP_PORT_RANGE];!(loc.BlastUDPFirewallExceptionName)" />


   <!-- Type 19, Error custom actions -->
   <CustomAction Id="VM_ErrWSWCInstalled"
                 Error="!(loc.MsgWSWCInstalled)" />

   <CustomAction Id="VM_ErrClientRunning"
                 Error="!(loc.MsgClientRunning)" />

   <CustomAction Id="VM_ErrManualUninstallRequired"
                 Error="!(loc.MsgManualUninstallRequired)" />

   <CustomAction Id="VM_ErrSVIFeatureInstalled"
                 Error="!(loc.MsgInstallationAbortifSVIInstalled)" />

   <CustomAction Id="VM_ErrServerInstalled"
                 Error="!(loc.MsgServerInstalled)" />

   <CustomAction Id="VM_ErrUnsupportedOldVersion"
                 Error="!(loc.MsgUnsupportedOldVersion)" />

   <CustomAction Id="VM_ErrVDM_LOOPBACK_IP"
                 Error="!(loc.MsgVdmLoopbackIp)" />

   <CustomAction Id="VM_ErrWindowsUpdateInProgress"
                 Error="!(loc.MsgWindowsUpdateInProgress)" />

   <CustomAction Id="VM_ErrWindowsUpdateAndRestartPending"
                 Error="!(loc.MsgWindowsUpdateAndRestartPending)" />

   <CustomAction Id="VM_ErrCloningFeaturesCanNotChange"
                 Error="28113" />

   <CustomAction Id="VM_AdminAccessRequired_ModifyRepair"
                 Error="28114" />

   <CustomAction Id="VM_AdminAccessRequired_Patch"
                 Error="28115" />

   <CustomAction Id="VM_MustReboot"
                 Error="!(loc.MsgMustReboot)" />

   <CustomAction Id="VM_NoRepairAllowed"
                 Error="!(loc.NoRepairAllowed)" />

   <!-- There are two downgrade error actions: one for displaying a localized error message,
        and one for logging an English string during silent installs -->
   <CustomAction Id="VM_ErrDowngradeDetectedUI"
                 Error="!(loc.MsgDowngradeDetected)" />

   <CustomAction Id="VM_ErrDowngradeDetectedSilent"
                 Error="The installer has detected that a newer version of [ProductName] is already installed." />

   <CustomAction Id="VM_InstallOSRolesAndFeatures"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMInstallOSRolesAndFeatures"
                 Return="check"
                 Execute="immediate" />

   <CustomAction Id="HZ_Check_AgentRegistryMigrated"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="HZCheckAgentRegistryMigrated"
                 Return="ignore"
                 Execute="firstSequence" />

   <CustomAction Id="HZ_CopyRegTree_Migration_Imm"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="HZCopyRegKeyTree"
                 Return="ignore"
                 Execute="firstSequence" />

   <CustomAction Id="HZ_CopyRegTree_Migration_Def"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="HZCopyRegKeyTree"
                 Return="ignore"
                 Impersonate="no"
                 Execute="deferred" />

   <!-- CustomActionData = HK*:Path::HK*:Path::excluded keys::excluded keys;HK*:Path::HK*:Path#overrideFlag -->
   <CustomAction Id="HZ_CopyRegTree_Migration_Def_SD"
                 Property="HZ_CopyRegTree_Migration_Def"
                 Value="HKLM:SOFTWARE\\VMware, Inc.\\VMware VDM\\KeyVaultCNG::HKLM:SOFTWARE\\Omnissa\\Horizon\\KeyVaultCNG::v4v_agent#1" />

   <CustomAction Id="VM_CheckVDMLogsDirOverride"
                 Property="VDM_LOGS"
                 Value="[REG_VDM_LOGS_OVERRIDE]"
                 Execute="immediate" />
   <CustomAction Id="VM_CreateVDMLogsDir"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMCreateVDMLogsDir"
                 Return="check"
                 Impersonate="no"
                 Execute="deferred" />
   <CustomAction Id="VM_CreateVDMLogsDir_SD"
                 Property="VM_CreateVDMLogsDir"
                 Value="[VDM_DIR]|[VDM_LOGS]" />

   <CustomAction Id="VMCreateUnityFiltersDir"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMCreateUnityFiltersDir"
                 Return="check"
                 Impersonate="no"
                 Execute="deferred" />
   <CustomAction Id="VMCreateUnityFiltersDir_SD"
                 Property="VMCreateUnityFiltersDir"
                 Value="[RDE]|[UNITY_FILTERS]" />

   <?define ForceDeleteFiles=1?>
   <CustomAction Id="VM_RemoveCachedInstallerFiles"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMRemoveDirectory"
                 Return="ignore"
                 Impersonate="yes"
                 Execute="deferred" />
   <CustomAction Id="VM_RemoveCachedInstallerFiles_SD"
                 Property="VM_RemoveCachedInstallerFiles"
                 Value="[COMMON_DIR]InstallerCache\[ProductName]\[ProductVersion]-[ProductCode];$(var.ForceDeleteFiles)" />

   <!-- This action helps ensure that some feature install state changed during a modify install -->
   <CustomAction Id="VM_ValidateRequestedFeatureChanges"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMValidateRequestedFeatureChanges"
                 Return="check"
                 Impersonate="yes"
                 Execute="immediate" />

   <!-- The Property "VDM_SERVER_PASSWORD" will not be populated by this action -->
   <Property Id="SettingsFilePasswords"
             Value="VDM_SERVER_PASSWORD" />
   <CustomAction Id="VM_ReadInstallerSettingsFile"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMReadInstallerSettingsFile"
                 Return="ignore"
                 Impersonate="no"
                 Execute="firstSequence" />
   <CustomAction Id="VM_ErrSettingsFileInvalid"
                 Error="!(loc.SettingsFileInvalid)" />

   <CustomAction Id="VM_VMUninstallHznusmWinlogonNotificationHandler"
                 BinaryKey="VMwareCustomActions.dll"
                 DllEntry="VMUninstallHznusmWinlogonNotificationHandler"
                 Return="check"
                 Impersonate="no"
                 Execute="deferred" />

   <!-- Hzaprep is used for agent pairing during Golden Image installation -->
   <CustomAction Id="VM_LaunchHzaprep"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMRun"
                 Return="check"
                 Impersonate="no"
                 Execute="deferred" />

   <CustomAction Id="VM_LaunchHzaprep_SD"
                 Property="VM_LaunchHzaprep"
                 Value="[#hzaprep.exe];/register [VDM_SERVER_NAME];0;1;" />

   <?define Hzaprep_RegKey="Software\Omnissa\Horizon\Node Manager"?>
   <?define Hzaprep_RegName="CreateRPsKey"?>
   <?define Hzaprep_RegValue="true"?>
   <?define Hzaprep_RegNative="0"?> <!-- 0=64-bit Hive, 1=32-bit Hive-->
   <?define Hzaprep_WriteRegistryCustomActionData=HKLM;$(var.Hzaprep_RegKey);$(var.Hzaprep_RegName);REG_SZ;$(var.Hzaprep_RegValue);$(var.Hzaprep_RegNative)?>
   <?define Hzaprep_RemoveRegistryCustomActionData=HKLM;$(var.Hzaprep_RegKey);$(var.Hzaprep_RegName);$(var.Hzaprep_RegNative)?>
   <CustomAction Id="VM_WriteRegistry_Hzaprep"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMWriteRegistry"
                 Return="check"
                 Impersonate="no"
                 Execute="deferred" />

   <CustomAction Id="VM_WriteRegistry_Hzaprep_SD"
                 Property="VM_WriteRegistry_Hzaprep"
                 Value="$(var.Hzaprep_WriteRegistryCustomActionData)" />

   <!-- Install-Rollback action -->
   <CustomAction Id="VM_WriteRegistry_Hzaprep_RB"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMRemoveRegistry"
                 Return="ignore"
                 Impersonate="no"
                 Execute="rollback" />

   <CustomAction Id="VM_WriteRegistry_Hzaprep_RB_SD"
                 Property="VM_WriteRegistry_Hzaprep_RB"
                 Value="$(var.Hzaprep_RemoveRegistryCustomActionData)" />

   <!-- Custom action to wait for pairing to be complete -->
   <Property Id="AGENT_PAIRING_WAIT_TIME" Value="120" /> <!-- in seconds -->
   <CustomAction Id="VM_WaitForPairing_SD"
                 Property="VM_WaitForPairing"
                 Value="[AGENT_PAIRING_WAIT_TIME]" />

   <CustomAction Id="VM_WaitForPairing"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMWaitForPairing"
                 Return="check"
                 Impersonate="no"
                 Execute="deferred" />
   
   <!-- Custom action to 'Disable' the wsnm service -->
   <!-- [UBI-308]: replace this with a custom action from vdmInstUtil.dll -->
   <CustomAction Id="VM_DisableWSNMService"
                 Directory="TARGETDIR"
                 ExeCommand="sc config wsnm start= disabled"
                 Return="check"
                 Execute="deferred"
                 Impersonate="no" />

   <!-- Custom actions to remove the Hzaprep reg key before installation finishes -->
   <CustomAction Id="VM_RemoveRegistry_Hzaprep"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMRemoveRegistry"
                 Return="ignore"
                 Impersonate="no"
                 Execute="deferred" />

   <CustomAction Id="VM_RemoveRegistry_Hzaprep_SD"
                 Property="VM_RemoveRegistry_Hzaprep"
                 Value="$(var.Hzaprep_RemoveRegistryCustomActionData)" />
</Include>
