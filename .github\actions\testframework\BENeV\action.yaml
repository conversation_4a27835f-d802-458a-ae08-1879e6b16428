name: 'Run BENeV'
description: 'Execute BENeV tests for appblastlibs'
author: 'VMware, Inc.'

inputs:
  runNumber:
    description: 'appblastlibs run number. Defaults to current run'
    required: false
  runAttempt:
    description: 'appblastlibs run attempt. Defaults to current run'
    required: false
  artifactoryApiToken:
    description: Artifactory API Token
    required: true
  artifactoryBaseUrl:
    description: Base URL for Artifactory
    required: true
  artifactoryReadToken:
    description: Token to use when reading from Omnissa Artifactory
    required: true
  useOpenCppCoverage:
    description: 'Generate code coverage report with OpenCppCoverage (Windows only)'
    required: false
    default: 'false'
  useGcov:
    description: 'Generate gcda files for gcov'
    required: false
    default: 'false'
  topology:
    description: Set this to `stableAgent` or `stableClient` for n-1 testing
    required: false
  enableRawChannel:
    description: Run BENeV with --enableRawChannel
    required: false
  reportDbUri:
    description: 'DB URI for report upload'
    required: true
  reportDbName:
    description: 'DB name for report upload'
    required: true
  reportDbCollection:
    description: 'Collection name of report DB'
    required: true
  buildtype:
    description: 'build type'
    required: true
    default: 'beta'

runs:
  using: "composite"
  steps:
    - name: Gcov setup
      if: runner.os != 'Windows' && inputs.useGcov == 'true'
      run: |
        GCOV_PREFIX="${{ github.workspace }}/gcov-gcda"
        mkdir "$GCOV_PREFIX"
        echo "GCOV_PREFIX=$GCOV_PREFIX" >> $GITHUB_ENV
        echo "GCOV_PREFIX_STRIP=6" >> $GITHUB_ENV
      shell: bash

    - name: Download BENeV
      uses: euc-eng/artifactory-download@v1
      with:
        filePaths: >
          benev/*/*,benev/*/*/*,test/x64/*,test/x64/vvcAsockPerfApp/*,
          test/x64/vvcProxyStub/*,test/x64/wiresharkDissector/*,x64/*,x64/*/*
        runNumber: ${{ inputs.runNumber }}
        runAttempt: ${{ inputs.runAttempt }}
        job: build-appblastlibs-${{ runner.os == 'Windows' && 'winhzn-gh' || 'lnxbuild-gh' }}
        artifactoryApiToken: ${{ inputs.artifactoryApiToken }}
        artifactoryBaseUrl: ${{ inputs.artifactoryBaseUrl }}
        workflow: appblastlibs

    - name: Download stable benevPeer
      if: inputs.topology != ''
      uses: euc-eng/artifactory-download@v1
      with:
        # Download the stable benevPeer from a known pre-rebranding change
        change: 69cae26132eeb8a4c475dedf359cc5b97de3dcbd
        filePaths: ${{ runner.os == 'Linux' && 'benev/blastSocketPeer/x64/*'
                       || 'benev/*/*,benev/*/*/*,test/x64/*,x64/*,x64/*/*' }}
        destinationDir: stableBenev
        job: build-appblastlibs-${{ runner.os == 'Windows' && 'winhzn-gh' || 'lnxbuild-gh' }}
        artifactoryApiToken: ${{ inputs.artifactoryApiToken }}
        artifactoryBaseUrl: ${{ inputs.artifactoryBaseUrl }}
        workflow: appblastlibs

    - name: Delete and Install Certificates
      if: ${{ runner.os == 'Windows' }}
      run: |
        certutil -delstore root "Test Root CA"
        certutil -delstore ca "Test Intermediate CA"
        certutil -addstore root ./benev/nodejs/sslcrlfiles/root.crt
        certutil -addstore ca ./benev/nodejs/sslcrlfiles/intermediate.crt
      shell: bash

    - name: Set OpenCppCoverage arguments
      id: opencpp-setup
      if: runner.os == 'Windows' && inputs.useOpenCppCoverage == 'true'
      run: >
        python .github/actions/testframework/common/setup_opencpp.py
        --modules benevPeer.exe
        --sources bora/lib
        --pdb benev/blastSocketPeer/x64/benevPeer.pdb
        --cover_children
        --output ${{ github.workspace }}/benevcov.xml
      shell: cmd

    - name: Run BENeV ( {{ runner.os }} )
      shell: bash
      run: >
        python ./.github/actions/testframework/BENeV/benev.py
        --execute_benev --benev_test_type stable
        --stable_benev_path stableBenev
        ${{ inputs.topology != '' && format('--topology {0}Topology.json', inputs.topology) || '' }}
        ${{ inputs.enableRawChannel != '' && '--enable_raw_channel' || '' }}
        ${{ (runner.os == 'Windows' && inputs.useOpenCppCoverage == 'true') &&
            format('--opencppcoverage_args "{0}"', steps.opencpp-setup.outputs.opencpp-args) || '' }}
      env:
        ARTIFACTORY_READ_TOKEN: ${{ inputs.artifactoryReadToken }}

    - name: Collect logs + analyze dumps
      if: ${{ !cancelled() }}
      run: >
        python
        ./.github/actions/testframework/common/post_build.py
        --post_always --test_framework benev
        --pipeline_sub_job_name artifacts-benev
      shell: bash

    - name: Copy test/cov results to artifacts dir
      if: ${{ !cancelled() }}
      run: |
        cp benev/nodejs/test-results.xml artifacts-benev/benev-tests.xml
        cp *cov.xml artifacts-benev || true
      shell: bash

    - name: Copy gcov reports to artifacts dir
      if: ${{ runner.os != 'Windows' && inputs.useGcov == 'true' }}
      run: tar -C "${{ env.GCOV_PREFIX }}/bora/build" -czvf artifacts-benev/benev_gcda.tar.gz .
      shell: bash

    - name: Publish test report
      if: ${{ !cancelled() }}
      uses: euc-eng/junit-test-reporter@main
      with:
        testResults: 'benev/nodejs/test-results.xml'

    - name: Upload Test Report
      if: ${{ !cancelled() }}
      uses: euc-eng/test-reporter-backend-upload@v3
      with:
        testResults: 'benev/nodejs/test-results.xml'
        app_version: '${{ github.ref_name }}'
        mongo_uri: ${{ inputs.reportDbUri }}
        db_name: ${{ inputs.reportDbName }}
        collection_name: ${{ inputs.reportDbCollection }}
        reportOnlyFailures: false
      env:
        ADDITIONAL_DATA: |
          {
            "job": "${{ github.job }}",
            "matrix": ${{ toJson(matrix) }},
            "buildtype": "${{ inputs.buildtype }}"
          }

    - name: Upload logs to Artifactory
      if: ${{ !cancelled() }}
      uses: euc-eng/artifactory-upload@v1
      with:
        artifactoryApiToken: ${{ inputs.artifactoryApiToken }}
        artifactoryBaseUrl: ${{ inputs.artifactoryBaseUrl }}
        sourcePath: artifacts-benev
