/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */
package com.omnissa.vdi.orchestratorj.keyvault;

import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.PublicKey;

import com.omnissa.vdi.mfwj.PropertyBag;
import com.omnissa.vdi.mfwj.binaryResp;
import com.omnissa.vdi.orchestratorj.keyvault.KeyVaultTransform.DigestAlgorithmName;
import com.omnissa.vdi.orchestratorj.keyvault.KeyVaultTransform.HashAlgorithmName;

/**
 * Data class to hold a public key and optionally its proof when putting a
 * public key to KeyVault or fetching a public key from KeyVault. When providing
 * or requesting a proof, this class also holds the hashed proof data and
 * specifies the hash algorithm. Also holds the key pair name, which is updated
 * with the key generation after a fetch operation.
 */
public class PublicKeyWithProof {
    private KeyName name;

    private byte[] key;

    private byte[] proof;

    private byte[] data;

    private HashAlgorithmName algo;

    private boolean trusted;

    /**
     * Build a trusted public key.
     *
     * @param key
     *            a raw (encoded) public key (can be null)
     * @param name
     *            a KeyName object containing name and generation information
     *
     * @throws IllegalArgumentException
     *             if either parameter is null
     */
    public PublicKeyWithProof(byte[] key, KeyName name)
            throws IllegalArgumentException {
        if (key == null || name == null) {
            throw new IllegalArgumentException("Missing parameter");
        }
        this.key = key;
        this.name = name;
        trusted = true;
    }

    /**
     * Build a request for a named public key with proof of private key
     * possession.
     *
     * @param name
     *            a KeyName object containing name and generation information
     * @param data
     *            a hash of material used as input to signature generation
     * @param algo
     *            the hash algorithm used to produce the data parameter
     *
     * @throws IllegalArgumentException
     *             if any parameter is null
     * @throws NoSuchAlgorithmException
     *             if algo has no Digest equivalent
     */
    public PublicKeyWithProof(KeyName name, String data, HashAlgorithmName algo)
            throws IllegalArgumentException, NoSuchAlgorithmException {
        if (name == null || data == null || algo == null) {
            throw new IllegalArgumentException("Missing parameter");
        }
        this.name = name;
        this.algo = algo;
        this.data = makeHash(data, algo);
    }

    /**
     * Build an object containing a named public key with proof of private key
     * possession.
     *
     * @param key
     *            a raw (encoded) public key (can be null)
     * @param proof
     *            a signature using data and algo to prove possession of the
     *            private key
     * @param name
     *            a KeyName object containing name and generation information
     * @param data
     *            a hash of material used as input to signature generation
     * @param algo
     *            the hash algorithm used to produce the data parameter
     *
     * @throws IllegalArgumentException
     *             if any parameter is null
     * @throws NoSuchAlgorithmException
     *             if algo has no Digest equivalent
     */
    public PublicKeyWithProof(byte[] key, byte[] proof, KeyName name,
            String data, HashAlgorithmName algo)
            throws IllegalArgumentException, NoSuchAlgorithmException {
        this(name, data, algo);
        if (key == null || proof == null) {
            throw new IllegalArgumentException("Missing key or proof");
        }
        this.key = key;
        this.proof = proof;
    }

    /**
     * Extend an existing property bag with defined values. May not include
     * proof if this is a request.
     *
     * @param bag
     *            the property bag to extend
     *
     * @throws IllegalArgumentException
     *             if bag is null
     */
    public void addToBag(PropertyBag bag) throws IllegalArgumentException {
        if (bag == null) {
            throw new IllegalArgumentException("Missing bag");
        }
        bag.add("name", name.getFullName());
        if (trusted) {
            bag.addBool("trusted", Boolean.TRUE);
            return;
        }
        if (proof != null) {
            bag.addBinary("binDataSigned", proof);
        }
        if (data != null) {
            bag.addBinary("binDataToSign", data);
        }
        if (algo != null) {
            PropertyBag transform = new PropertyBag();
            transform.add("XF_ALGID", algo.name());
            bag.addBag("transform", transform);
        }
    }

    /**
     * Return the public key if set.
     *
     * @return a public key object
     * @throws KeyVaultException
     *             if the key is not set or an error occurs
     */
    public PublicKey getKey() throws KeyVaultException {
        PublicKey pk;
        try {
            pk = makeKey(key);
        } catch (NoSuchAlgorithmException e) {
            throw new KeyVaultException("Invalid key");
        }
        if (pk == null) {
            throw new KeyVaultException("Unset");
        }
        return pk;
    }

    /**
     * Return the name of the keypair associated with the public key.
     *
     * @return a KeyName object containing name and generation information
     */
    public KeyName getName() {
        return name;
    }

    /**
     * Return a signature that can be used to prove possession of the private
     * key.
     *
     * @return the signature (can be null)
     */
    public byte[] getProof() {
        return proof;
    }

    /**
     * Return a hash of material used, or to be used, as input to signature
     * generation.
     *
     * @return the hash (can be null)
     */
    public byte[] getData() {
        return data;
    }

    /**
     * Return the algorithm used for the hashed material used as input to
     * signature generation.
     *
     * @return the algorithm (can be null)
     */
    public HashAlgorithmName getAlgo() {
        return algo;
    }

    /**
     * Is this object a request for a public key?
     *
     * @return true if it contains no key
     */
    public boolean isRequest() {
        return (key == null);
    }

    /**
     * Is this key trusted (that is, no proof provided)?
     *
     * @return true if trusted
     */
    public boolean isTrusted() {
        return trusted || (key != null && proof == null);
    }

    /* package-private */ ByteBuffer getPreparedKey() {
        if (key != null) {
            return KeyVaultBinaryUtils.getDirectByteBuffer(key);
        }
        return null;
    }

    /* package-private */ void setKey(binaryResp keydata) {
        try {
            key = makeKey(keydata.getByteBuffer()).getEncoded();
        } catch (NoSuchAlgorithmException ignore) {
        }
    }

    /* package-private */ void setProof(byte[] proof) throws KeyVaultException {
        this.proof = proof;
    }

    private static byte[] makeHash(String input, HashAlgorithmName algoId)
            throws NoSuchAlgorithmException {
        String name = DigestAlgorithmName.fromHash(algoId).name();
        MessageDigest digest = MessageDigest.getInstance(name);
        return digest.digest(input.getBytes(StandardCharsets.UTF_8));
    }

    private static PublicKey makeKey(byte[] encoded)
            throws NoSuchAlgorithmException {
        if (encoded == null) {
            return null;
        }
        return makeKey(ByteBuffer.wrap(encoded));
    }

    private static PublicKey makeKey(ByteBuffer encoded)
            throws NoSuchAlgorithmException {
        if (encoded == null) {
            return null;
        }
        return KeyVaultBinaryUtils.bytesToPublicKey(encoded);
    }
}
