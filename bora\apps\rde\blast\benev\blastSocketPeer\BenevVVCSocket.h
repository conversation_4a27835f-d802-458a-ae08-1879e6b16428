/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

#ifndef _BENEV_VVC_SOCKET_H
#define _BENEV_VVC_SOCKET_H


#include <string>

#include "horizon.h"
#include "userlock.h"

#include "vvclib.h"
#include "BenevSocket.h"


/*
 *----------------------------------------------------------------------
 *
 * class BenevVVCSocket --
 *
 *      Implementation of an asynchronous Benev VVC socket. A BenevVVCSocket
 *      maps to a VVC Channel.
 *
 *----------------------------------------------------------------------
 */

class BenevVVCSocket : public BenevSocket {
public:
   static BenevVVCSocket *connectTo(const std::string &channelName, const std::string &featureName,
                                    int32 sessionId, onConnect *connectCb,
                                    const void *connectCbData, onError *errorCb,
                                    const void *errorCbData,
                                    uint32 channelPriority = VVC_TRAFFIC_TYPE_REAL_TIME_2,
                                    uint32 channelFlags = 0);

   static BenevVVCSocket *acceptPeerOpenedChannel(
      VvcListenerHandle listener, int32 sessionId, const char *channelName, const char *featureName,
      void *connCookie, onConnect *connectCb, void *cbData, onError *errorCb,
      const void *errorCbData, uint32 channelPriority = VVC_TRAFFIC_TYPE_REAL_TIME_2,
      uint32 channelFlags = 0);

   ~BenevVVCSocket();

   bool recvData(void *buf, int bufLen, onRecv *recvCb, const void *cbData);
   bool sendData(const void *buf, int bufLen, onSent sentCb, const void *cbData);
   void closeSocket();
   bool getRawChannelStatus();

private:
   static const char *sClassName;

   /* Keep track a few send data */
   class SendCtx {
   public:
      SendCtx(const void *buf, int bufLen, onSent *cb, const void *cbData) :
         mSendBuf(buf),
         mSendBufLen(bufLen),
         mSentCb(cb),
         mSentCbData(cbData)
      {}
      const void *mSendBuf;
      unsigned mSendBufLen;
      onSent *mSentCb;
      const void *mSentCbData;
   };

   static VvcListenerHandle createVVCListenerForConnect(const std::string &channelName,
                                                        int32 sessionId);

   static void vvcSessionConnected(char *name, VvcListenerHandle listenerHandle,
                                   void *connectionCookie, uint32 connectionCaps, int32 sessionId,
                                   void *clientData);
   static void vvcPeerOpenedChannel(char *name, VvcListenerHandle listenerHandle,
                                    void *connectionCookie, uint32 connectionCaps, int32 sessionId,
                                    uint8 *initialData, size_t initialDataLen, void *clientData);
   static void vvcListenerClosed(VvcListenerHandle listenerHandle, void *clientData);

   static void channelOnOpenCb(VvcChannelHandle channel, VvcStatus status, uint8 *initialData,
                               size_t initialDataLen, void *clientData);
   static void channelOnCloseCb(VvcChannelHandle channel, VvcCloseChannelReason reason,
                                void *clientData);
   static void channelOnRecvCb(VvcChannelHandle channelHandle, uint32 flags, uint8 *buf, size_t len,
                               void *clientData);
   static void channelOnSendCompleteCb(VvcChannelHandle channelHandle, VvcStatus status, uint8 *buf,
                                       size_t len, void *clientData, void *msgClientData,
                                       uint32 msgId);

   BenevVVCSocket(VvcListenerHandle listener, int32 sessionId, const std::string &channelName,
                  std::string featureName, onConnect *connectCb, const void *connectCbData,
                  onError *errorCb, const void *errorCbData,
                  uint32 channelPriority = VVC_TRAFFIC_TYPE_REAL_TIME_2, uint32 channelFlags = 0);

   void channelOpened(VvcChannelHandle channel, uint8 *initialData, size_t initialDataLen);
   void dataReceived(uint8 *buf, size_t len);
   void dataSent(uint8 *buf, size_t len, SendCtx *sendCtx);
   void channelClosed(VvcChannelHandle channel, VvcCloseChannelReason reason);

   /*
    * A global list of listeners - this is to support wildcard channel names.
    */
   // XXX currently only one wildcard listener is supported
   static std::string sWildcardListenerName;
   static int sNextChannelNumber;
   static VvcListenerHandle sWildcardListener;

   VvcListenerHandle mListener;
   int32 mSessionId;
   const std::string mChannelName;
   const std::string mFeatureName;
   uint32 mChannelFlags;
   uint32 mChannelPriority;
   uint32 mChannelId;
   VvcChannelHandle mChannel;

   MXUserExclLock *mLock;

   onRecv *mRecvCb;
   const void *mRecvCbData;

   /* This is set to the buffer passed from the caller of recvData */
   uint8 *mRecvBuf;
   unsigned mRecvBufLen;

   bool mRawChannel;
};


#endif
