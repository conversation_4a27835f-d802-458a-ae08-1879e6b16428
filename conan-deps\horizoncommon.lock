{"version": "0.5", "requires": ["zlib/1.3.1#070fdb998838959a825710509689296b%1733494270.942", "wtl/10.0.10320#fc6020a5b8cc3f1179ec68615e42908c%1745248053.512", "util_linux/2.39.3#c69f594b4712cfa4e05995e37c2268c5%1740648977.648", "tomlplusplus/3.1.0#257300a00670bf514f6f1f322eeb9d89%1708022078.067", "snappy/1.1.7#72841864bdd1c7f79ff4ba47980761e7%1713251799.36", "rescle/1.0.11#933cc8db99892f9b9dcba12b0b914c4b%1714983006.974", "pcre2/10.42#0aa2ba6a3446486c65e60346d5a9fa55%1711692874.277", "openssl_fips_validated/3.0.9#2af1ca5cc524fa3775353719b750e440%1740295376.965", "openssl/3.0.16#09fdc1e695600e5f11185e9d5aee6aae%1740295373.964", "nlohmann_json/3.11.2#66de7b3253d039ae75ae40a0b2050bcd%1709206230.076", "libuv/1.48.0#f52fd11c73250433872a2a514c25fbb5%1717061021.896", "libiconv/1.17#055c1d6f948ec3843b03b945360c6b3e%1718780206.714", "libgettext/0.22#5ff9077ef9609e8eb31f9026256e82a0%1725475554.379", "libffi/3.4.4#c297536b60daec1ce4156c3628f874ff%1721007067.05", "jsoncpp/1.9.5#925804cc02bddd48441050a0344eed30%1708430492.248", "gtest/1.17.0#b81d3ff53b1af0ef3d23373a0a923c98%1748940935.811", "glibc/2.17#f31867240555f126de18ce7b95466545%1747919660.68", "glib/2.84.1#2730f913e60bcdbb23eeece7123bdc38%1747979467.326", "fmt/10.2.1#00dc4de2f60a8abb3a0f0b08d30ca771%1720109961.309", "fast_float/3.4.0#d8f4cbe6254820507f0cf0e99d97e151%1713275088.616", "boost/1.86.0#f70e151226aa0dbd5b1c5b1fd886823d%1730708381.876"], "build_requires": ["xcode/16.2#fb8b2a898ee2fdcc24e619f96fc19e8b%1745424608.976", "windows_redists/17.12.5-sdk26100.2454#e3a3800be17f1b0cd2f2b254abc70593%1741373308.755", "windows_non_redists/17.12.5-sdk26100.2454#7cfb36f1a4c31c9b1fc5deb4f6f7c21b%1741373308.289", "strawberryperl/5.38.0.1#8dbf355d802c6fd3c4f9979ba3a6c7c2%1700524960.205", "squishcoco/7.1.0#6104db4d172fbeaed1012c7c072d5175%1713334933.683", "pkgconf/2.1.0#0177a5313f23035f337f78f6cfdcdcba%1719990004.814", "perl/5.38.0#e5f3f95ef101e56de5a37edfcdd472ee%1726298679.552", "ninja/1.11.1#f73fb14f87f6343107224de5c8734c0d%1704462561.271", "nasm/2.16.01#32bb0d9a9c2f9a786bfc501ab9589eb4%1745586475.269", "msys2/cci.latest#0a6c87b74dc17768919ff9603807bbe6%1732531088.868", "msvc_redists/17.12.5#685b9a17a9dc413a961146d730c3d056%1741373307.555", "msvc_non_redists/17.12.5#c9d7dbe20c476283f27a0208869364de%1741373306.718", "msvc_desktop/17.12.5-sdk26100.2454#dd6d670bfb58009943a0d9fce0c35c3e%1741373278.899", "meson/1.4.1#723365dfd8e4ec2decc7ce18f83cac69%1747979472.412", "meson/1.3.1#ba55deb2839bcf58e99bee7eb5e7d496%1747979472.099", "make/4.4.1#572a6685bf5343b63f81a1e0c57c7b75%1745586474.861", "m4/1.4.19#265fb80b0415aef2ff3b0177569e7629%1711337655.59", "libiconv/1.17#055c1d6f948ec3843b03b945360c6b3e%1718780206.714", "glibc/2.17#f31867240555f126de18ce7b95466545%1747919660.68", "gettext/0.22.5#13aeb9d05f4fa4a7d916ed95164dc781%1744999949.049", "gcc/12.1#e1828956db1056558e7f6230e2d34011%1712665137.798", "findutils/4.10.0#8b22610de08d68872c61465c4ded5640%1744999948.937", "coreutils/9.4#11ee9fce4bf8fe11fc3c21171cdd8e3a%1740648976.192", "cmake/3.27.1#ff5aa246eafce398974ebaf4c0a6ae95%1712665136.894", "binutils/2.38#eb567393d48d74b3ec336c3ec49621a7%1712665136.79", "b2/5.2.1#559a98b1c2599d6d8c8e211e54c7cf9e%1730708381.119", "automake/1.16.5#61211678c2d683d8a58f506ddcf63285%1711647072.926", "autoconf/2.71#e65e58d3eb5c70a6cc7ea1f1a22c2c41%1711347438.585", "android_ndk/r26b#0cb123bcb6af141356bac36a4a556e9f%1712665136.574", "7zip/24.09#753051c21fbb8c1eafe0747959080f1a%1734348912.1"], "python_requires": ["windows_toolchain/1.0.0#5e2f7e972c57329ab1e490f0de6d0f59%1741373309.1"], "config_requires": []}