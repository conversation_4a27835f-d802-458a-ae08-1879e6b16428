/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * hookKeyboard.c  --
 *
 *      Windows keyboard hook.
 */

#include "winClient.h"
#include "winKeyboardMagic.h"
#include "hznprotect.h"
#include "config.h"


#define FlagOn(_F, _SF) ((_F) & (_SF))
#define BoolFlagOn(_F, _SF) (FlagOn((_F), (_SF)) ? 1 : 0)
#define SetFlag(_F, _SF) ((_F) |= (_SF))
#define ClearFlag(_F, _SF) ((_F) &= ~(_SF))


/*
 * Used to check the Windows Registry value.
 * HOOK_TIMEOUT_MS must match the value in the installer.
 */

#define HOOK_TIMEOUT_MS 5000

/*
 * Keys that we we'll automatically pop up on ungrab.
 */

static const struct {
   DWORD vkCode, scanCode;
} restoreKeys[] = {
   {VK_LSHIFT, VSCAN_LSHIFT},     {VK_RSHIFT, VSCAN_RSHIFT}, {VK_LCONTROL, VSCAN_LCONTROL},
   {VK_RCONTROL, VSCAN_RCONTROL}, {VK_LMENU, VSCAN_LALT},    {VK_RMENU, VSCAN_RALT},
   {VK_LWIN, VSCAN_LGUI},         {VK_RWIN, VSCAN_RGUI},     {'G', VSCAN_G},
};

typedef struct HookKeyboardKeyState {
   ViewModifierKeyState modifiers;

   Bool printScreen;
   Bool deleteKey;
   Bool numpadDelete;

   /*
    * Special caps lock tracking (taking injected LED presses, and
    * "shift as capslock off" into account).
    */
   Bool naturalCapsLock;
} HookKeyboardKeyState;

static struct {
   Bool isVistaOrAbove;
   VThreadID threadID;

   int savedLEDs;
   int currentLEDs;

   /*
    * "To turn off Caps Lock/Press the SHIFT key" option set on host?
    */
   Atomic_uint32 shiftCaps;

   Atomic_uint32 exiting;
   Bool ignoreWinLEnabled;
   Bool hostLockScreenDisabled;
   Bool respectGuestLED;
   Atomic_Bool syncNumLock;
   Atomic_Bool syncCapsLock;
   Atomic_Bool syncScrollLock;
} keyboard;

static struct {
   /*
    * The thread data should only be accessed by the hook thread.
    */

   /*
    * Track special handling for the Win+L sequence.
    */
   Bool trackWinL;

   /*
    * When Shift is used as the capslock-off key, the Windows keyboard handler
    * automatically generates capslock key presses in response to shift keys.
    * We need to let these through in order for capslock-off to work.
    * Specifically we need to allow through the first caps down/up sequence
    * that occurs after a shift keypress (which we generated) is seen by the
    * low-level keyboard hook routine.
    */
   Bool allowNextCaps;

   /*
    * Special handling of certain keys after a CAD sequence.
    */
   Bool trackKeysAfterCAD;

   /*
    * Track special handling after CAD is seen.
    */
   Bool inCADAftermath;

   /*
    * Track the state of various keys.
    */
   HookKeyboardKeyState state;

   VScancode prevTypematicKey; // used to detect special autorepeat
   DWORD lastEventVScanCode;
   DWORD lastEventFlags;
   Bool lastEventDown;
} thread;

/*
 * Local functions
 */
static void HookKeyboardThreadMain(void *clientData);
static void HookKeyboardCheckShiftCapsOff(void);
static void HookKeyboardCheckLockScreen(void);
static void HookKeyboardCheckHookTimeout(void);
static void HookKeyboardReleaseDownKeys(void);
static LRESULT CALLBACK HookKeyboardFunc(int nCode, WPARAM wParam, LPARAM lParam);
static void HookKeyboardToggleLED(int led);


/*
 *----------------------------------------------------------------------
 *
 * HookKeyboardPowerOn --
 *
 *      Power on.  Creates the hook thread.  The hook thread needs
 *      to run even when we're not grabbed to handle grab on
 *      keypress.
 *
 * Results:
 *      TRUE on success, FALSE on failure.
 *
 * Side effects:
 *      Device structure is allocated and registered.
 *      A VM index is allocated.
 *
 *----------------------------------------------------------------------
 */

Bool
HookKeyboardPowerOn(void)
{
   LOG(1, LGPFX "%s\n", __FUNCTION__);

   Util_Zero(&keyboard, sizeof keyboard);
   Util_Zero(&thread, sizeof thread);

   keyboard.isVistaOrAbove = (Hostinfo_OSVersion(0) >= 6);

   keyboard.respectGuestLED = Config_GetBool(FALSE, "mks.keyboard.respectGuestLED");
   LOG(3, LGPFX "keyboard.respectGuestLED: %d\n", keyboard.respectGuestLED);

   keyboard.ignoreWinLEnabled = Config_GetBool(FALSE, "mks.keyboard.ignoreWinLEnabled");
   LOG(3, LGPFX "keyboard.ignoreWinLEnabled: %d\n", keyboard.ignoreWinLEnabled);
   HookKeyboardCheckLockScreen();
   HookKeyboardCheckHookTimeout();

   return TRUE;
}


/*
 *----------------------------------------------------------------------
 *
 * HookKeyboardPowerOff --
 *
 *      Power off the keyboard hook.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      Kills the hook.
 *
 *----------------------------------------------------------------------
 */

void
HookKeyboardPowerOff(void)
{}


/*
 *----------------------------------------------------------------------
 *
 * HookKeyboardTogglesLED --
 *
 *      Toggles a host LED when low level hook is not enabled.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      Changes the host LEDs.
 *
 *----------------------------------------------------------------------
 */

void
HookKeyboardToggleLED(int led) // IN
{
   INPUT inputs[2] = {{
                         .type = INPUT_KEYBOARD,
                         .ki.dwFlags = KEYEVENTF_EXTENDEDKEY,
                         .ki.dwExtraInfo = INJECTED_KEY_MAGIC,
                      },
                      {
                         .type = INPUT_KEYBOARD,
                         .ki.dwFlags = KEYEVENTF_EXTENDEDKEY | KEYEVENTF_KEYUP,
                         .ki.dwExtraInfo = INJECTED_KEY_MAGIC,
                      }};

   switch (led) {

   case KBD_LED_SCROLL_LOCK: {
      inputs[0].ki.wVk = VK_SCROLL;
      inputs[0].ki.wScan = SCAN_SCROLL;

      inputs[1].ki.wVk = VK_SCROLL;
      inputs[1].ki.wScan = SCAN_SCROLL;
   } break;

   case KBD_LED_NUM_LOCK: {
      inputs[0].ki.wVk = VK_NUMLOCK;
      inputs[0].ki.wScan = SCAN_NUMLOCK;

      inputs[1].ki.wVk = VK_NUMLOCK;
      inputs[1].ki.wScan = SCAN_NUMLOCK;
   } break;

   case KBD_LED_CAPS_LOCK: {
      /*
       * If shift is the capslock-off key, we need to send a (left-)shift key
       * to turn off capslock (i.e. to toggle capslock when capslock is on).
       */

      int keyToSend = VK_CAPITAL;

      ASSERT(led == KBD_LED_CAPS_LOCK);
      if (Atomic_Read32(&keyboard.shiftCaps) && (keyboard.currentLEDs & 1 << KBD_LED_CAPS_LOCK)) {
         keyToSend = VK_SHIFT;
      }

      inputs[0].ki.wVk = keyToSend;
      inputs[0].ki.wScan = SCAN_CAPS;

      inputs[1].ki.wVk = keyToSend;
      inputs[1].ki.wScan = SCAN_CAPS;
   } break;

   default:
      NOT_IMPLEMENTED();

   } // switch (led)

   SendInput(2, inputs, sizeof(INPUT));
}


/*
 *----------------------------------------------------------------------
 *
 * ViewClientOS_SetLEDs --
 *
 *      Changes the host LEDs to match the specified parameter.
 *      If the config "mks.keyboard.respectGuestLED" is TRUE or the
 *      prefs to sync LED key is FALSE, this function set the LED per
 *      the guest's LED state, otherwise do nothing here.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      Changes the host LEDs.
 *
 *----------------------------------------------------------------------
 */

void
ViewClientOS_SetLEDs(unsigned int leds) // IN
{
   Bool expectedNumLock = FALSE;
   Bool expectedCapsLock = FALSE;
   Bool expectedScrollLock = FALSE;

   Bool syncNumLock = FALSE;
   Bool syncCapsLock = FALSE;
   Bool syncScrollLock = FALSE;

   ViewControlMgr_GetSyncLEDPrefs(&syncNumLock, &syncCapsLock, &syncScrollLock);

   Atomic_WriteBool(&keyboard.syncNumLock, syncNumLock);
   Atomic_WriteBool(&keyboard.syncCapsLock, syncCapsLock);
   Atomic_WriteBool(&keyboard.syncScrollLock, syncScrollLock);

   Bool hostNumLock = ((GetKeyState(VK_NUMLOCK) & 1) != 0);
   Bool hostCapsLock = ((GetKeyState(VK_CAPITAL) & 1) != 0);
   Bool hostScrollLock = ((GetKeyState(VK_SCROLL) & 1) != 0);

   Bool guestNumLock = ((leds & 1 << KBD_LED_NUM_LOCK) != 0);
   Bool guestCapsLock = ((leds & 1 << KBD_LED_CAPS_LOCK) != 0);
   Bool guestScrollLock = ((leds & 1 << KBD_LED_SCROLL_LOCK) != 0);

   keyboard.currentLEDs = (hostNumLock << KBD_LED_NUM_LOCK) | (hostCapsLock << KBD_LED_CAPS_LOCK) |
                          (hostScrollLock << KBD_LED_SCROLL_LOCK);
   if (keyboard.respectGuestLED) {
      expectedNumLock = guestNumLock;
      expectedCapsLock = guestCapsLock;
      expectedScrollLock = guestScrollLock;
   } else {
      expectedNumLock = syncNumLock ? hostNumLock : guestNumLock;
      expectedCapsLock = syncCapsLock ? hostCapsLock : guestCapsLock;
      expectedScrollLock = syncScrollLock ? hostScrollLock : guestScrollLock;
   }

   if (hostScrollLock != expectedScrollLock) {
      LOG(3, LGPFX "%s: toggle scroll lock to %d\n", __FUNCTION__, expectedScrollLock);
      HookKeyboardToggleLED(KBD_LED_SCROLL_LOCK);
   }

   if (hostNumLock != expectedNumLock) {
      LOG(3, LGPFX "%s: toggle num lock to %d\n", __FUNCTION__, expectedNumLock);
      HookKeyboardToggleLED(KBD_LED_NUM_LOCK);
   }

   if (hostCapsLock != expectedCapsLock) {
      LOG(3, LGPFX "%s: toggle caps lock to %d\n", __FUNCTION__, expectedCapsLock);
      HookKeyboardToggleLED(KBD_LED_CAPS_LOCK);
   }

   keyboard.currentLEDs = (expectedNumLock << KBD_LED_NUM_LOCK) |
                          (expectedCapsLock << KBD_LED_CAPS_LOCK) |
                          (expectedScrollLock << KBD_LED_SCROLL_LOCK);
}


/*
 *----------------------------------------------------------------------
 *
 * Win32_KeyboardGrab --
 *
 *      Install the keyboard hook.
 *
 *      In the normal case, we use a low-level keyboard hook,
 *      which is installed and handled by a thread (created here).
 *
 * Results:
 *      TRUE on success.
 *
 * Side effects:
 *      The keyboard hook is installed.
 *
 *----------------------------------------------------------------------
 */

Bool
Win32_KeyboardGrab(Bool reGrab) // IN
{
   MXUserSemaphore *threadSignal;
   Bool success;
   int i;

   LOG(2, LGPFX "%s\n", __FUNCTION__);

   if (reGrab) {
      return TRUE;
   }

   /*
    * Save LEDs
    */
   keyboard.currentLEDs = (GetKeyState(VK_SCROLL) & 1) << KBD_LED_SCROLL_LOCK |
                          (GetKeyState(VK_NUMLOCK) & 1) << KBD_LED_NUM_LOCK |
                          (GetKeyState(VK_CAPITAL) & 1) << KBD_LED_CAPS_LOCK;
   keyboard.savedLEDs = keyboard.currentLEDs;
   ViewControlMgr_HostLEDsAtGrab(keyboard.savedLEDs);

   /*
    * Get sync LED prefs
    */
   Bool syncNumLock = FALSE;
   Bool syncCapsLock = FALSE;
   Bool syncScrollLock = FALSE;
   ViewControlMgr_GetSyncLEDPrefs(&syncNumLock, &syncCapsLock, &syncScrollLock);
   Atomic_WriteBool(&keyboard.syncNumLock, syncNumLock);
   Atomic_WriteBool(&keyboard.syncCapsLock, syncCapsLock);
   Atomic_WriteBool(&keyboard.syncScrollLock, syncScrollLock);

   /*
    * Check shift-caps-off setting.
    */
   HookKeyboardCheckShiftCapsOff();

   /*
    * Release down keys on the host before grabbing
    */
   HookKeyboardReleaseDownKeys();

   Atomic_Write32(&keyboard.exiting, FALSE);

   /*
    * Thunk the HookKeyboardDoGrab call to the hook thread.
    */
   threadSignal = MXUser_CreateSemaphore("hookKeyboardThreadSignal", RANK_LEAF);

   VThread_CreateThread(HookKeyboardThreadMain, threadSignal, "keyboard", &keyboard.threadID);
   VERIFY(keyboard.threadID != VTHREAD_INVALID_ID);

   i = 0;
   success = FALSE;
   while (i < viewWin32.inputThreadRetries && !success) {
      success = MXUser_TimedDownSemaphore(threadSignal, viewWin32.inputThreadTimeout);
      i++;
   }

   if (!success) {
      Panic(LGPFX "Keyboard thread is not responding to grab request\n");
   }

   MXUser_DestroySemaphore(threadSignal);

   LOG(3, LGPFX "Done grabbing\n");

   return success;
}


/*
 *----------------------------------------------------------------------
 *
 * Win32_KeyboardUngrab --
 *
 *      Ungrab the host keyboard.
 *
 *      This must be safe to call during Panic and might come in with
 *      unknown locks if emergency is TRUE. See MKS_Release.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      Twiddled the host keyboard modifier keys so they don't
 *      appear stuck.
 *
 *----------------------------------------------------------------------
 */

void
Win32_KeyboardUngrab(Bool emergencyRelease) // IN
{
   LOG(2, LGPFX "%s\n", __FUNCTION__);

   if (keyboard.threadID != VTHREAD_INVALID_ID) {
      DWORD hostThreadID = VThread_GetHostThreadID(keyboard.threadID);

      Atomic_Write32(&keyboard.exiting, TRUE);
      PostThreadMessage(hostThreadID, WM_QUIT, 0, 0);

      keyboard.threadID = VTHREAD_INVALID_ID;
   }

   /*
    * Check shift-caps-off setting.
    */
   HookKeyboardCheckShiftCapsOff();

   /*
    * Restore host's LED state.
    */
   ViewControlMgr_RestoreLEDsAtUngrab(keyboard.savedLEDs);

   /*
    * Reset IME key filter state.
    */
   Win32_ResetIMEKeyFilter(FALSE);

   LOG(3, LGPFX "Done ungrabbing\n");
}


/*
 *----------------------------------------------------------------------
 *
 * HookKeyboardThreadMain --
 *
 *      The keyboard hook thread
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      Keys are handled via a keyboard hook.
 *
 *----------------------------------------------------------------------
 */

void
HookKeyboardThreadMain(void *clientData) // UNUSED
{
   MXUserSemaphore *threadSignal = clientData;
   VThreadID tid = VThread_CurID();
   HHOOK hHook;
   MSG msg;

   if (!SetThreadPriority(GetCurrentThread(), THREAD_PRIORITY_TIME_CRITICAL)) {
      Log("MKS HOOK failed to set thread priority: %s\n", Msg_ErrString());
   }

#define IS_KEY_DOWN_ASYNC(vk) BoolFlagOn(GetAsyncKeyState(vk), 0x8000)

   thread.state.modifiers.lControl = IS_KEY_DOWN_ASYNC(VK_LCONTROL);
   thread.state.modifiers.rControl = IS_KEY_DOWN_ASYNC(VK_RCONTROL);
   thread.state.modifiers.lAlt = IS_KEY_DOWN_ASYNC(VK_LMENU);
   thread.state.modifiers.rAlt = IS_KEY_DOWN_ASYNC(VK_RMENU);
   thread.state.modifiers.lShift = IS_KEY_DOWN_ASYNC(VK_LSHIFT);
   thread.state.modifiers.rShift = IS_KEY_DOWN_ASYNC(VK_RSHIFT);
   thread.state.modifiers.lGui = IS_KEY_DOWN_ASYNC(VK_LWIN);
   thread.state.modifiers.rGui = IS_KEY_DOWN_ASYNC(VK_RWIN);

   thread.state.printScreen = IS_KEY_DOWN_ASYNC(VK_SNAPSHOT);
   thread.state.deleteKey = IS_KEY_DOWN_ASYNC(VK_DELETE);
   thread.state.numpadDelete = IS_KEY_DOWN_ASYNC(VK_DECIMAL);

#undef IS_KEY_DOWN_ASYNC

   thread.state.naturalCapsLock = FALSE;
   thread.prevTypematicKey = VSCAN_NONE;
   thread.lastEventVScanCode = VSCAN_NONE;
   thread.lastEventFlags = 0;
   thread.lastEventDown = TRUE;

   /*
    * Install the keyboard hook procedure
    */
   LOG(2, "MKS HOOK Installing WH_KEYBOARD_LL\n");
   hHook = SetWindowsHookEx(WH_KEYBOARD_LL, HookKeyboardFunc, Win32U_GetModuleHandle(NULL), 0);
   if (!hHook) {
      Warning("MKS HOOK failed to install hook: %s\n", Msg_ErrString());
      goto exit;
   }

   LOG(2, "MKS HOOK keyboard thread starting\n");

   MXUser_UpSemaphore(threadSignal);

   while (!Atomic_Read32(&keyboard.exiting)) {
      if (MsgWaitForMultipleObjects(0, NULL, FALSE, 100, QS_ALLINPUT) == WAIT_OBJECT_0) {
         while (PeekMessage(&msg, 0, 0, 0, PM_REMOVE)) {
            DispatchMessage(&msg);
         }
      }
   }

   if (!UnhookWindowsHookEx(hHook)) {
      int errno;
      const char *errmsg;

      errno = Err_Errno();
      errmsg = Err_ErrString();

      switch (errno) {
      case ERROR_INVALID_HOOK_HANDLE:
         /*
          * The hook was probably already invalidated by Windows
          * (because we timed out).
          */
         break;
      default:
         Panic("MKS HOOK: UnhookWindowsHookEx failed with error: (%d) %s\n", errno, errmsg);
         break;
      }
   }

exit:
   VThread_DestroyThread(tid);
}


/*
 *----------------------------------------------------------------------
 *
 * HookKeyboardFunc --
 *
 *      The keyboard hook callback function, called when our hook is
 *      installed and there is keyboard input to process.
 *
 *      XXX: for mks.config.keyboardFilter=require, we may want to ignore
 *      any input that makes it here (which shouldn't happen).
 *
 * Results:
 *      1 to absorb the key.
 *      CallNextHookEx() to pass the key on.
 *
 * Side effects:
 *      A key is processed.
 *
 *----------------------------------------------------------------------
 */

LRESULT CALLBACK
HookKeyboardFunc(int nCode,     // IN
                 WPARAM wParam, // IN
                 LPARAM lParam) // IN
{
   Bool sendKeyToHost;
   Bool sendKeyToGuest;
   Bool sendExtraDownToGuest = FALSE;
   Bool sendExtraUpToGuest = FALSE;
   Bool sendReleaseAll = FALSE;
   Bool sendUnicodeToGuest = FALSE;

   KBDLLHOOKSTRUCT messageData;
   DWORD extraLParam;
   int vkCode;
   Bool down;
   Bool up;
   Bool extended;
   Bool injected;
   Bool special;
   Bool repeatedKey;
   VScancode vscancode;
   Bool buggyKeyUps;
   Bool anyLEDKey;
   Bool injectedLEDKey;
   Bool injectedIMEKey;
   Bool buggyWinUp;
   Bool ignoreWinL;
   Bool keyIsCAD;
   Bool duplicateUp;
   Bool buggyKeysAfterCAD;
   Bool isIMEEnabled;

   /*
    * Be a good citizen -- chain to next hook if action code is negative,
    * as MSDN documentation tells us to do.
    */

   if (!ViewScreenManager_IsGrabbed(NULL) || nCode < 0) {
      LOG(2, "MKS HOOK (hook) nCode %d\n", nCode);
      return CallNextHookEx(NULL, nCode, wParam, lParam);
   }

   /*
    * Extract key information from arguments.
    *
    * The 0x200 bit in scanCode seems to indicate system-generated
    * keys.  Specifically, the "Alt Gr" in the Swedish locale,
    * generates both the normal right Alt and a left control
    * with 0x200 set in the scanCode value.
    *
    * The only invalid code that we can get (after the masking)
    * is VSCAN_NONE.  We check for that and drop it.
    *
    * Copy KBDLLHOOKSTRUCT to avoid leaking the decrypted contents
    * to other applications via CallNextHookEx().
    */
   messageData = *(PKBDLLHOOKSTRUCT)lParam;
   extended = BoolFlagOn(messageData.flags, LLKHF_EXTENDED);
   injected = BoolFlagOn(messageData.flags, LLKHF_INJECTED);
   special = BoolFlagOn(messageData.scanCode, 0x200);
   extraLParam = (DWORD)messageData.dwExtraInfo;

   if (ViewClient_IsBlockKeyLoggerEnabled() && !ViewClient_IsAgentInstalled()) {
      /*
       * Previously, guest connections were only allowed if the client supported
       * anti-keylogger functionality, provided the corresponding GPO was enabled.
       *
       * However, after the patch at https://github.com/euc-vdi/cart/pull/7999,
       * this is no longer strictly true. Users can now enable the
       * AllowArmNoAntiKeylogger GPO option to permit connections from ARM
       * devices that do not support anti-keylogger.
       *
       * A potential issue arises when this GPO is later disabled within the VDI.
       * If the user provides keyboard input during the brief period when the GPO
       * is disabled but the update hasn't yet propagated to the client, the system
       * may encounter an ASSERT(HznProtect_IsDriverOpened()) failure and crash.
       * This happens because anti-keylogger support on ARM is not implemented.
       *
       * To prevent this, we now only assert on non-ARM platforms and bypass the
       * assertion on ARM.
       */
      ASSERT(HznProtect_IsDriverOpened() || !HznProtect_IsAMD64());

      if (!HznProtect_DecryptMessage(&messageData)) {
         LOG(0, LGPFX "(hook) failed to decrypt message: 0x%x\n", extraLParam);
         if (ViewClient_IsBlockSendInputEnabled()) {
            /*
             * Ignore all invalid (failed to decrypt) keystrokes
             * when block SendInput() is enabled.
             */
            return CallNextHookEx(NULL, nCode, wParam, lParam);
         }
      } else {
         extraLParam = (DWORD)messageData.dwExtraInfo;
      }
   } // if enable anti-keyLogger

   vkCode = messageData.vkCode;
   up = BoolFlagOn(messageData.flags, LLKHF_UP);
   down = up ? FALSE : TRUE;

   vscancode = messageData.scanCode & VSCAN_CODE_BITS;
   if (extended) {
      SetFlag(vscancode, VSCAN_PREFIX_BIT);
   }

   switch (vscancode) {
   case 0x136:
      vscancode = VSCAN_RSHIFT;
      break;
   case 0x045:
      vscancode = VSCAN_PAUSE;
      break;
   case 0x145:
      vscancode = VSCAN_NUMLOCK;
      break;
   case 0x13a:
      /*
       * This is a left-shift keypress we generated to turn off capslock.
       * (We know this because LSHIFT normally is not an extended key and
       * has vscancode 0x02a.)  In response we set the allowNextCaps flag
       * to remind ourselves not to zap the next caps key event; set the
       * flag when the fake-lshift goes down, clear it when it goes up.
       */
      if (Atomic_Read32(&keyboard.shiftCaps) && (vkCode == VK_LSHIFT)) {
         thread.allowNextCaps = down;
      }
      break;
   case VSCAN_SYSREQ:
      vscancode = VSCAN_PRINT;
      break;
   case 0x00:
   case 0x100:
      /*
       * We have a valid vkCode but no vscancode.  This was probably sent
       * from a key injector on the host, and we need to find a vscancode
       * to send it to the Guest.
       */
      vscancode = KeyboardMapping_VKeyToVScan(vkCode);
      break;
   }

   LOG(2,
       LGPFX "(hook) incoming: wp 0x%04x vkCode 0x%02x scanCode 0x%04x "
             "flags 0x%02x, (0x%03x %s)\n",
       wParam, vkCode, messageData.scanCode, messageData.flags, vscancode, down ? "down" : "up");

   isIMEEnabled = ViewClient_IsIMEEnabled();

   if (isIMEEnabled && vkCode == VK_KANA) {
      LOG(2, "MKS HOOK (hook) allowing VK_KANA for Japanese IME\n");
   } else if (vscancode == VSCAN_NONE) {
      LOG(2, "MKS HOOK (hook) dropping VSCAN_NONE\n");
      return 1;
   }


   /*
    * When Shift is used as the capslock-off key, Windows "eats" some keyboard
    * events upstream of our keyboard hook. Result is that if caps are on and
    * the user presses capslock, the only event we see is a capslock-keyup
    * event.
    */
   if (Atomic_Read32(&keyboard.shiftCaps)) {
      /*
       * Look for VK_CAPITAL keystrokes that are not "extended". These are
       * the "natural" keystrokes (as opposed to generated keystrokes that we
       * insert to set the capslock LED).
       */
      if ((vkCode == VK_CAPITAL) && !extended) {
         if (!thread.state.naturalCapsLock && up) {
            sendExtraDownToGuest = TRUE;
         }
         thread.state.naturalCapsLock = down;
      }
   }

   /*
    * Detect cases where Windows sends us the key up without the previous down.
    */
   if (up) {
      if (vscancode == VSCAN_HANGUL_EN || vscancode == VSCAN_HANJA_EN) {
         /*
          * Windows "eats" key down event for the hangul and hanja keys when
          * the active locale is English.
          */
         sendExtraDownToGuest = TRUE;
      } else if (vscancode == VSCAN_PRINT) {
         /*
          * When Left Shift, and Left Alt are down and we get a SysReq up,
          * we don't see the SysReq down.  SysReq maps to PrintScreen above.
          * For details see bug 544780.
          */
         if (thread.state.modifiers.lShift && thread.state.modifiers.lAlt &&
             !thread.state.printScreen) {
            sendExtraDownToGuest = TRUE;
         }
      }
   }


   /*
    * Special handling for Win+L sequence.
    *
    * On Vista and Win7 without the filter driver, the Win + L sequence seems
    * to get the Win key out of sync with the session, and it can stick on the
    * host.  See bug 472312.
    *
    * Sending an extra Win key up to the host seems to fix this.
    */
   buggyWinUp = FALSE;
   if (thread.trackWinL) {
      if (up && (vscancode == VSCAN_LGUI || vscancode == VSCAN_RGUI)) {
         buggyWinUp = TRUE;
         thread.trackWinL = FALSE;
      } else if (up && vscancode == VSCAN_L) {
         /*
          * Do nothing but wait for next invocation of our hook.
          * The L-up could come before the Win-Up.
          */
      } else {
         /*
          * Don't do anything and reset the flag.
          */
         thread.trackWinL = FALSE;
      }
   }

   ignoreWinL = FALSE;
   if (vscancode == VSCAN_L && down && thread.lastEventDown && keyboard.isVistaOrAbove) {
      if (thread.lastEventVScanCode == VSCAN_LGUI || thread.lastEventVScanCode == VSCAN_RGUI) {
         /*
          * Start the tracking.
          */
         thread.trackWinL = TRUE;
         if (keyboard.ignoreWinLEnabled && !keyboard.hostLockScreenDisabled &&
             !thread.state.modifiers.lAlt && !thread.state.modifiers.rAlt &&
             !thread.state.modifiers.rShift && !thread.state.modifiers.lShift &&
             !thread.state.modifiers.lControl && !thread.state.modifiers.rControl) {
            ignoreWinL = TRUE;
         }
      }
   }


   /*
    * Some Toshiba laptops have the bad habit of occasionally generating
    * multiple key-up events for the same keypress.  See bug 13856.
    * These duplicated events often but not always have the same timestamp.
    *
    * So, we won't forward a key-up event if the previous event was a
    * key-up event for the same key.
    */

   duplicateUp = FALSE;
   if (/*!mks.config.win32.skipDuplicateKeyUpDetection && */
       vscancode == thread.lastEventVScanCode && messageData.flags == thread.lastEventFlags &&
       !down && !thread.lastEventDown) {
      LOG(2, "MKS HOOK (hook) detected repeated up for 0x%03x\n", vscancode);
      duplicateUp = TRUE;
   } else {
      thread.lastEventVScanCode = vscancode;
      thread.lastEventFlags = messageData.flags;
      thread.lastEventDown = down;
   }


   /*
    * Some Japanese and Korean keyboards generate unusual sequences of up/downs
    * for the following keys.
    *
    * Sometimes we get both WM_KEYDOWN and WM_KEYUP when they are pressed
    * down or typematicing down, and sometimes they do not send any up at all.
    *
    * So, we pretend that each down is immediately released, and ignore all
    * key ups we receive for these keys.
    *
    * This makes it impossible to hold any of these keys in the guest, and
    * messes up our typematic detection.
    */

   buggyKeyUps = FALSE;

   switch (vkCode) {
   case VK_DBE_ALPHANUMERIC:
   case VK_DBE_KATAKANA:
   case VK_DBE_HIRAGANA:
   case VK_DBE_SBCSCHAR:
   case VK_DBE_DBCSCHAR:
   case VK_DBE_ROMAN:
   case VK_DBE_NOROMAN:
   case VK_KANJI: // Also takes care about VK_HANJA which has same vkCode
   case VK_HANGUL:
      buggyKeyUps = TRUE;
      break;
   }


   /*
    * Detect autorepeat (typematic).
    */
   repeatedKey = FALSE;
   if (special) {
      /*
       * Skip over special keys.
       *
       * When a special key is autorepeated, it gets between the real
       * repeated key and messes us up.
       */
   } else if (buggyKeyUps) {
      /*
       * Disable typematic filtering for buggy keys.
       */
      thread.prevTypematicKey = VSCAN_NONE;
   } else if (duplicateUp && !sendExtraDownToGuest && !sendExtraUpToGuest) {
      /*
       * If we have a duplicate up which is not otherwise obnoxious,
       * treat it as a repeated key, but don't do typematic detection on it.
       */
      repeatedKey = TRUE;
   } else {
      /*
       * Normal typematic detection.
       */
      if (down && (vscancode == thread.prevTypematicKey)) {
         repeatedKey = TRUE;
      }

      if (down) {
         /*
          * Save the next key that might typematic.
          */
         thread.prevTypematicKey = vscancode;
      } else if (vscancode == thread.prevTypematicKey) {
         /*
          * If we got an up for the previously typematicing key,
          * clear the typematic state.
          */
         thread.prevTypematicKey = VSCAN_NONE;
      }
   }

   ViewModifierKeyState prevModState = thread.state.modifiers;

   /*
    * Track host key state for certain keys.
    */
   switch (vscancode) {
   case VSCAN_LCONTROL:
      thread.state.modifiers.lControl = down;
      break;
   case VSCAN_RCONTROL:
      thread.state.modifiers.rControl = down;
      break;
   case VSCAN_LALT:
      thread.state.modifiers.lAlt = down;
      break;
   case VSCAN_RALT:
      thread.state.modifiers.rAlt = down;
      break;
   case VSCAN_LSHIFT:
      thread.state.modifiers.lShift = down;
      break;
   case VSCAN_RSHIFT:
      thread.state.modifiers.rShift = down;
      break;
   case VSCAN_LGUI:
      thread.state.modifiers.lGui = down;
      break;
   case VSCAN_RGUI:
      thread.state.modifiers.rGui = down;
      break;
   case VSCAN_PRINT:
      thread.state.printScreen = down;
      break;
   case VSCAN_DELETE:
      thread.state.deleteKey = down;
      break;
   case VSCAN_NUMPAD_DELETE:
      thread.state.numpadDelete = down;
      break;
   }

   /*
    * Calculate some helper values.
    */
   anyLEDKey = FALSE;
   if (vkCode == VK_CAPITAL || vkCode == VK_NUMLOCK || vkCode == VK_SCROLL) {
      anyLEDKey = TRUE;
   }

   injectedLEDKey = FALSE;
   injectedIMEKey = FALSE;
   if (injected) {
      /*
       * This is an LED key, with the same unusual extended value that we
       * use when injecting keys to toggle Caps Lock and Scroll.
       */
      if (vkCode == VK_CAPITAL && extended || vkCode == VK_SCROLL && extended) {
         injectedLEDKey = TRUE;
      }

      /*
       * This is an Numlock key, with extral info value that we use when
       * injecting keys to toggle Numlock.
       */
      if (extraLParam == INJECTED_KEY_MAGIC && vkCode == VK_NUMLOCK) {
         injectedLEDKey = TRUE;
      }

      if (extraLParam == INJECTED_IME_KEY) {
         injectedIMEKey = TRUE;
      }
   }

   keyIsCAD = FALSE;
   if (down && (vscancode == VSCAN_DELETE || vscancode == VSCAN_NUMPAD_DELETE) &&
       (thread.state.modifiers.lControl || thread.state.modifiers.rControl) &&
       (thread.state.modifiers.lAlt || thread.state.modifiers.rAlt) &&
       !(thread.state.modifiers.lShift || thread.state.modifiers.rShift)) {
      keyIsCAD = TRUE;
   }

   /*
    * If keys come in immediately after a CAD, Windows will sometimes send
    * us a key DOWN with no corresponding UP, which can cause the MKS to stick
    * keys.  We don't currently have a way to tell which key-downs are stuck
    * like this, so to avoid problems with this in customer scenarios that
    * we've seen, ignore the first DELETE or ENTER keydown after a CAD
    * sequence.  (See bugs 1237498 and 1265639.)
    */
   buggyKeysAfterCAD = FALSE;
   if (down && thread.trackKeysAfterCAD && thread.inCADAftermath) {
      Bool allModifiersUp = !(thread.state.modifiers.lControl || thread.state.modifiers.rControl) &&
                            !(thread.state.modifiers.lAlt || thread.state.modifiers.rAlt) &&
                            !(thread.state.modifiers.lShift || thread.state.modifiers.rShift);
      Bool deleteKey = (vscancode == VSCAN_DELETE || vscancode == VSCAN_NUMPAD_DELETE);
      Bool enterKey = (vscancode == VSCAN_ENTER || vscancode == VSCAN_NUMPAD_ENTER);

      if ((allModifiersUp && deleteKey) || enterKey) {
         buggyKeysAfterCAD = TRUE;

         /*
          * Stop tracking the buggy keys when the first key-down
          * comes in after a CAD sequence.
          */
         thread.trackKeysAfterCAD = FALSE;
      }
   }

   /*
    * Determine the final fate of the key.
    *
    * By default, the guest gets the key and the host does not.
    * (But, there are lots of special cases.)
    */
   sendKeyToGuest = TRUE;
   sendKeyToHost = FALSE;
   /*
    * Send all VK_PACKET message as unicode
    */
   if (vkCode == VK_PACKET) {
      sendKeyToGuest = FALSE;
      sendKeyToHost = FALSE;
      sendExtraDownToGuest = FALSE;
      sendExtraUpToGuest = FALSE;
      sendUnicodeToGuest = TRUE;
   } else if (vkCode == VK_CAPITAL && special && thread.allowNextCaps) {
      /*
       * This is a fake key we generated to turn off capslock.
       */
      sendKeyToGuest = FALSE;
      sendKeyToHost = TRUE;
   } else if (injectedLEDKey) {
      /*
       * This is probably a key we injected to toggle an LED.
       * (Although in theory something else could have injected it...)
       */
      sendKeyToGuest = FALSE;
      sendKeyToHost = TRUE;
   } else if (anyLEDKey && !keyboard.respectGuestLED) {
      /*
       * If the config "mks.keyboard.respectGuestLED" is TRUE or the
       * prefs to sync the LED key is FALSE, we do not set the LED
       * here and let the callback "ViewClientOS_SetLEDs" to set the
       * host's LED, otherwise, we will set the host LED here by
       * setting "sendKeyToHost" to TRUE.
       */
      switch (vkCode) {
      case VK_CAPITAL:
         sendKeyToHost = Atomic_ReadBool(&keyboard.syncCapsLock);
         break;
      case VK_NUMLOCK:
         sendKeyToHost = Atomic_ReadBool(&keyboard.syncNumLock);
         break;
      case VK_SCROLL:
         sendKeyToHost = Atomic_ReadBool(&keyboard.syncScrollLock);
         break;
      default:
         break;
      }
      sendKeyToGuest = TRUE;
   } else if ((!isIMEEnabled && repeatedKey) || special) {
      /*
       * Repeated keys and special keys are discarded.
       */
      sendKeyToHost = FALSE;
      sendKeyToGuest = FALSE;
      sendExtraDownToGuest = FALSE;
      sendExtraUpToGuest = FALSE;
   } else if (!isIMEEnabled && buggyKeyUps) {
      if (up) {
         /*
          * If the key has buggy ups, ignore all incoming ups.
          */
         sendKeyToHost = FALSE;
         sendKeyToGuest = FALSE;
         sendExtraUpToGuest = FALSE;
         sendExtraDownToGuest = FALSE;
      } else {
         /*
          * Treat downs for buggy keys as a press-release.
          */
         sendExtraUpToGuest = TRUE;
         sendKeyToHost = FALSE;
         sendKeyToGuest = TRUE;
      }
   } else if (keyIsCAD) {
      /*
       * Control-Alt-Delete handling.
       */

      if (isIMEEnabled) {
         Win32_ResetIMEKeyFilter(TRUE);
      }
      if (keyboard.isVistaOrAbove) {
         thread.inCADAftermath = TRUE;
      }

      /*
       * Release all keys in the guest when a CAD is detected.
       * Windows will not always send the key up events and it is otherwise
       * possible to get stuck keys in the guest.  Also, clear out
       * the modifier key state cache since all keys have now been
       * released.
       */
      sendReleaseAll = TRUE;
      thread.state.modifiers.lControl = FALSE;
      thread.state.modifiers.rControl = FALSE;
      thread.state.modifiers.lAlt = FALSE;
      thread.state.modifiers.rAlt = FALSE;
      thread.state.modifiers.lShift = FALSE;
      thread.state.modifiers.rShift = FALSE;
      thread.state.modifiers.lGui = FALSE;
      thread.state.modifiers.rGui = FALSE;

      /*
       * Track the key after a CAD sequence occurs.
       */
      thread.trackKeysAfterCAD = TRUE;
   } else if (buggyWinUp) {
      /*
       * Send the buggyWinUp to both host and guest.
       */
      ASSERT(up);
      sendKeyToHost = TRUE;
      sendKeyToGuest = TRUE;
   } else if (buggyKeysAfterCAD) {
      sendKeyToGuest = FALSE;
   } else if (injectedIMEKey) {
      sendKeyToHost = TRUE;
      sendKeyToGuest = FALSE;
   } else if (isIMEEnabled) {
      sendKeyToHost = FALSE;
      sendKeyToGuest = FALSE;

      if (vkCode == VK_KANA) {
         /*
          * The Japanese IME Kana Input mode will generate VK_KANA with empty
          * scancode and this key should go to host.
          */
         sendKeyToHost = TRUE;
      } else {
         /*
          * IME needs to handle buggy keys like VK_KANJI for Japanese IME and
          * repeated keys.
          */
         Win32_IMEFilterKey(KeyboardMapping_VScanToHID(vscancode), down, repeatedKey, prevModState,
                            thread.state.modifiers, &sendKeyToHost);
      }
   }


   /*
    * Special handling for keys that come in right after a CAD.
    */
   if (thread.inCADAftermath) {
      switch (vscancode) {
      case VSCAN_LCONTROL:
      case VSCAN_RCONTROL:
      case VSCAN_LALT:
      case VSCAN_RALT:
      case VSCAN_DELETE:
      case VSCAN_NUMPAD_DELETE:
      case VSCAN_ESCAPE:
         /*
          * Windows seems to muck with state below our hook when CAD is pressed,
          * and we'll sometimes stick keys in the host if we don't allow
          * (what seem like) superfluous ups through.
          */
         break;
      default:
         if (down) {
            /*
             * End the special states when the first non CAD-related key
             * comes in.
             */
            thread.inCADAftermath = FALSE;
            thread.trackKeysAfterCAD = FALSE;
         }
         break;
      }

      if (up && thread.inCADAftermath) {
         sendKeyToHost = TRUE;
      }
   }

   if (up && 0 /*mks.config.win32.forwardAllKeyUps */) {
      sendKeyToHost = TRUE;
   }

   /*
    * Filter out Win + L from sending to guest
    */
   if (ignoreWinL) {
      sendKeyToGuest = FALSE;
   }

   /*
    * Do it!!!
    */
   LOG(3,
       LGPFX "(hook) outgoing: vscan=%s(0x%03x) %s -> sendKeyToGuest=%d, "
             "sendKeyToHost=%d, %s%s%s\n",
       KeyboardMapping_GetVScanName(vscancode), vscancode, down ? "down" : "up", sendKeyToGuest,
       sendKeyToHost, sendExtraDownToGuest ? "sendExtraDownToGuest, " : "",
       sendExtraUpToGuest ? "sendExtraUpToGuest, " : "", sendReleaseAll ? "sendReleaseAll, " : "");

   if (sendExtraDownToGuest) {
      ASSERT(up);
      ViewClient_InsertKeyEvent(KeyboardMapping_VScanToHID(vscancode), TRUE);
   }

   if (sendKeyToGuest) {
      ViewClient_InsertKeyEvent(KeyboardMapping_VScanToHID(vscancode), down);
   }

   if (sendUnicodeToGuest && down) {
      ViewClient_InsertUnicodePoint(messageData.scanCode, GetTickCount());
   }

   if (sendExtraUpToGuest) {
      ASSERT(down);
      ViewClient_InsertKeyEvent(KeyboardMapping_VScanToHID(vscancode), FALSE);
   }

   if (sendReleaseAll) {
      ViewClient_ReleaseKeyboard();
   }

   if (sendKeyToHost) {
      return CallNextHookEx(NULL, nCode, wParam, lParam);
   } else {
      /*
       * Tell Windows we processed the event, so
       * it will not forward it to the next hook.
       */
      return TRUE;
   }
}


/*
 *----------------------------------------------------------------------
 *
 * HookKeyboardCheckShiftCapsOff --
 *
 *      Check the registry for the shift-to-turn-capslock-off feature.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------
 */
void
HookKeyboardCheckShiftCapsOff(void)
{
   HKEY key;
   DWORD data;
   DWORD dataLen = sizeof data;
   DWORD type;

   /*
    * Dead-simple: read the DWORD at HKCU\Keyboard Layout\Attributes, and
    * check bit 16 (if it's set the shift-as-capslock-off feature is enabled).
    */
   if (Win32U_RegOpenKeyEx(HKEY_CURRENT_USER, "Keyboard Layout", 0, KEY_QUERY_VALUE | KEY_SET_VALUE,
                           &key) == ERROR_SUCCESS) {
      DWORD error = Win32U_RegQueryValueEx(key, "Attributes", NULL, &type, (LPBYTE)&data, &dataLen);

      if ((error == ERROR_SUCCESS) && (type == REG_DWORD)) {
         Atomic_Write32(&keyboard.shiftCaps, BoolFlagOn(data, 0x10000));
      }
      RegCloseKey(key);
   }
}

/*
 *----------------------------------------------------------------------
 *
 * HookKeyboardCheckLockScreen --
 *
 *      Check the registry for "DisableLockWorkstation"
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------
 */
void
HookKeyboardCheckLockScreen(void)
{
   HKEY key;
   DWORD data;
   DWORD dataLen = sizeof data;
   DWORD type;
   if (Win32U_RegOpenKeyEx(HKEY_CURRENT_USER,
                           "Software\\Microsoft\\Windows\\CurrentVersion\\Policies\\System", 0,
                           KEY_QUERY_VALUE, &key) == ERROR_SUCCESS) {
      DWORD error = Win32U_RegQueryValueEx(key, "DisableLockWorkstation", NULL, &type,
                                           (LPBYTE)&data, &dataLen);
      if ((error == ERROR_SUCCESS) && (type == REG_DWORD)) {
         keyboard.hostLockScreenDisabled = (data == 1);
      }
      RegCloseKey(key);
   }
}


/*
 *----------------------------------------------------------------------
 *
 * HookKeyboardCheckHookTimeout --
 *
 *      Check the low-level hook timeout value in the registry
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      The timeout may be changed (in the registry).
 *
 *----------------------------------------------------------------------
 */

void
HookKeyboardCheckHookTimeout(void)
{
   HKEY key;
   DWORD data;
   DWORD dataLen = sizeof data;
   DWORD type;

   if (Win32U_RegOpenKeyEx(HKEY_CURRENT_USER, "Control Panel\\Desktop", 0, KEY_QUERY_VALUE, &key) ==
       ERROR_SUCCESS) {
      DWORD error = Win32U_RegQueryValueEx(key, "LowLevelHooksTimeout", NULL, &type,
                                           (unsigned char *)&data, &dataLen);

      if (error != ERROR_SUCCESS) {
         Log(LGPFX "RegQueryValueEx(LowLevelHooksTimeout) failed: %s (%d)\n",
             Msg_Errno2String(error), error);
      } else if (data < HOOK_TIMEOUT_MS) {
         Log(LGPFX "Keyboard hook timeout set below recommended value "
                   "(found=%d, recommended=%d)\n",
             data, HOOK_TIMEOUT_MS);
      }

      RegCloseKey(key);
   }
}


/*
 *----------------------------------------------------------------------
 *
 * HookKeyboardReleaseDownKeys --
 *
 *      Send all the restoreKeys that are currently down back up
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      Keys are sent up
 *
 *----------------------------------------------------------------------
 */

void
HookKeyboardReleaseDownKeys(void)
{
   UINT cInputs = 0;
   INPUT inputs[ARRAYSIZE(restoreKeys)];

   RtlZeroMemory(&inputs, sizeof inputs);

   for (SIZE_T i = 0; i < ARRAYSIZE(restoreKeys); i++) {
      DWORD vkCode = restoreKeys[i].vkCode;

      if (FlagOn(GetKeyState(vkCode), 0x8000)) {
         VScancode scanCode = restoreKeys[i].scanCode;
         DWORD flags = KEYEVENTF_KEYUP;

         if (FlagOn(scanCode, VSCAN_PREFIX_BIT)) {
            SetFlag(flags, KEYEVENTF_EXTENDEDKEY);
         }

         inputs[cInputs].type = INPUT_KEYBOARD;
         inputs[cInputs].ki.wVk = vkCode;
         inputs[cInputs].ki.wScan = scanCode & VSCAN_CODE_BITS;
         inputs[cInputs].ki.dwFlags = flags;

         cInputs++;
      }
   }

   if (cInputs > 0) {
      if (ViewClient_IsBlockKeyLoggerEnabled() && !ViewClient_IsAgentInstalled()) {
         HznProtect_SendInput(cInputs, inputs, sizeof(INPUT));
      } else {
         SendInput(cInputs, inputs, sizeof(INPUT));
      }
   }
}
