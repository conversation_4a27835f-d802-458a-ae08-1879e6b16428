# Copyright (c) Omnissa, LLC. All rights reserved.
# This product is protected by copyright and intellectual property laws in the
# United States and other countries as well as by international treaties.
# -- Omnissa Restricted

"""Omnissa Horizon Smartcard driver

This driver is being used by Horizon to support
SmartCard redirection for non RDP session. This driver
receives SmartCard IOCTL's from winscard on the guest.

Maintainer:
   <EMAIL>
"""

import os
import vmware
import time

driverName = "hznvscrd"
env = vmware.LookupEnv("wdk-driver3")
env.LoadTool(["digitalsign", "horizonUtils"])

log = vmware.GetLogger("main")
km = vmware.KernelModule.WindowsWDKDriver3(driverName)

# Wdf coinstaller version definitions
kmdfLibVersion = "1.09"
wdfCoInstallerLibVersion = "01009"

# Driver version definitions
buildYear = str(time.localtime()[0])
buildDate = str(time.strftime("%m/%d/%Y"))
driverVersionFilePath = File("#bora-vmsoft/hznvscrd/vscrd_version.h")

# Get the driver version from the file
driverVersion = env.GetDriverVersion(
    driverName, driverVersionFilePath, "HZNVSCRD_DRIVER_VERSION"
)

infCdfNodes = []
infCdfNodes += env.TextSubst(
    os.path.join(vmware.Host().ComponentBuildPath(driverName), "vscrdinf.txt"),
    File("#bora-vmsoft/hznvscrd/vscrdinf_template.txt"),
    TEXTSUBSTARGS=[
        (
            "__VERSION__",
            driverVersion,
        ),
        (
            "__DATE__",
            buildDate,
        ),
        (
            "__YEAR__",
            buildYear,
        ),
        ("__KMDFLIBVERSION__", kmdfLibVersion),
        ("__WDFCOINSTALLER_LIB_VERSION__", wdfCoInstallerLibVersion),
    ],
)
infCdfNodes += env.TextSubst(
    os.path.join(vmware.Host().ComponentBuildPath(driverName), "vscrdcdf.txt"),
    File("#bora-vmsoft/hznvscrd/vscrdcdf_template.txt"),
    TEXTSUBSTARGS=[
        (
            "__KMDFLIBFILEVERSION__",
            wdfCoInstallerLibVersion,
        ),
    ],
)
vmware.RegisterNode(infCdfNodes, "hznvscrdinf")

km.AddDirectoryGlob(os.path.join(vmware.BuildRoot(), "build", "version"), "*")
km.AddDirectoryGlob("#bora-vmsoft/hznvscrd", "*")
km.AddDirectoryGlob(vmware.Host().ComponentBuildPath(driverName))

variantsWin64 = [
    {
        "WDK_TARGET": "Win7/x64",
        "WDK_SUBTARGETS": ["bin"],
    },
    {
        "WDK_TARGET": "Win10/arm64",
        "WDK_SUBTARGETS": ["bin"],
    },
    {
        "WDK_TARGET": "Latest/x64",
        "WDK_SUBTARGETS": ["pfd", "sdv", "dvl"],
    },
]

binaries = [
    "hznvscrd.sys",
    "hznvscrd.pdb",
    "hznvscrd.inf",
    "hznvscrd.cat",
]

nodeWin64 = km.BuildNode(env, variantsWin64, binaries, srcRoot="#bora-vmsoft")

wdfCoInstallerx64Node = env.FileCopy(
    os.path.join(
        nodeWin64[0].dir.abspath, "WdfCoInstaller%s.dll" % wdfCoInstallerLibVersion
    ),
    os.path.join(
        env["WINDOWS_REDISTS_ROOT"],
        "win",
        "km",
        "wdf",
        "x64",
        "WdfCoInstaller%s.dll" % wdfCoInstallerLibVersion,
    ),
)

# Make each node build sequential.
# There is a race condition in accessing source globs
# when all variants nodes are built at the same time
# and it causes compilation errors.
env.Depends(nodeWin64, wdfCoInstallerx64Node)
env.Depends(nodeWin64, infCdfNodes)

nodeWin64 += wdfCoInstallerx64Node
vmware.RegisterNode(nodeWin64, "hznvscrdWin7x64")

# Sign each node
env.SignFile(nodeWin64)

nodes = [nodeWin64]

vmware.RegisterNode(nodes, "hznvscrd")
Alias("hznvscrd-build", nodes)
