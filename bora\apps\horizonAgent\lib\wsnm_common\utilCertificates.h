/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * The certificate storage and retrieval related functions.
 */
#pragma once
#include <Windows.h>
#include <variant>

#include <corestring.h>
#include "CertUtilityInterface.h"
#include "utilHash.h"


class WinCertImplementation : public CertUtilityInterface {
public:
   bool ImportCertificateToStore(const CertificateStoreType &storeName,
                                 const std::string &certToImport, const std::string &friendlyName,
                                 const std::string &privateKey,
                                 const std::string &privKeyContainerName,
                                 bool allCertsFriendlyName = false,
                                 std::vector<std::string> *thumbprints = nullptr) override;

   bool SetPrivateKeyForCertificateInStore(PCCERT_CONTEXT certContext,
                                           const std::string &privateKey,
                                           const std::string &containerName) override;

   bool ExportCertificateAndKeyFromStore(const CertificateStoreType &storeName,
                                         const std::string &certFriendlyName, std::string &pkcs_DER,
                                         std::string &commonName, std::string *caCrt = nullptr,
                                         std::string *rootCaCrt = nullptr,
                                         PCCERT_CONTEXT *pCertContext = nullptr) override;

   PCCERT_CONTEXT FindCertificate(const HCERTSTORE hStore, const std::string &friendlyName,
                                  std::string &commonName) override;

   PCCERT_CONTEXT FindCertificate(const CertificateStoreType &store,
                                  const std::string &friendlyName,
                                  std::string &commonName) override;

   std::vector<CertificateDetails> ListCertificates(const CertificateStoreType &store) override;

   bool RetrieveCACertificateChainFromStore(const std::string &certFriendlyName,
                                            const std::string &storeName,
                                            std::string &certRetrieved) override;

   bool DeleteCertificate(PCCERT_CONTEXT certContext) override;

   bool DeleteCertificateAndPrivateKey(PCCERT_CONTEXT certContext) override;

   void DeleteCertificates(const CertificateStoreType &store,
                           const std::string &friendlyName) override;

   std::map<std::string, std::string> DeleteCertificates(
      const CertificateStoreType &store, const std::vector<std::vector<BYTE>> &hashes) override;

   bool RenameCertificate(PCCERT_CONTEXT cert, const std::string &newFriendlyName) override;

   void RenameCertificates(const CertificateStoreType &store, const std::string &newFriendlyName,
                           const std::string &oldFriendlyName) override;

   bool GenerateKeyUsingWin32API(const std::string &containerName, std::string &encodedKeyOut,
                                 std::string &privKeyBlob) override;

   bool GenerateCSRUsingWin32API(
      IN const std::string &commonName, IN const std::string &containerName,
      OUT std::string &encodedCertRequest,
      IN OPTIONAL const std::string &organizationUnitName = std::string(),
      IN OPTIONAL const std::string &organizationName = std::string(),
      IN OPTIONAL const std::string &state = std::string(),
      IN OPTIONAL const std::string &country = std::string()) override;

   static HCERTSTORE OpenCertificateStore(const std::string &storeName);

   CertificateStoreType GetCertificateStore(const std::string &storeName) override;

   bool RetrieveSystemCertificates(std::vector<std::string> &certs) override;

   std::vector<cedar::windows::unique_cert_context> FindCertificates(
      const CertificateStoreType &store, const std::string &friendlyName) override;

   std::vector<cedar::windows::unique_cert_context> FindCertificates(
      const CertificateStoreType &store, const std::vector<std::string> &thumbprints,
      std::vector<std::vector<BYTE>> *notFoundTps = nullptr) override;

   std::vector<cedar::windows::unique_cert_context> FindCertificates(
      const CertificateStoreType &store, const std::vector<std::vector<BYTE>> &thumbprints,
      std::vector<std::vector<BYTE>> *notFoundTps = nullptr) override;

   std::vector<std::string> GetPEMCerts(const CertificateStoreType &storeName,
                                        const std::string &friendlyName) override;

   static bool GetCertIssuerName(PCCERT_CONTEXT context, std::string &issuerName);

   static bool GetCertSubjectName(PCCERT_CONTEXT context, std::string &subjectName);

   static bool GetCertValidFrom(PCCERT_CONTEXT context, std::string &validFrom);

   static bool GetCertValidTo(PCCERT_CONTEXT context, std::string &validTo);

   static bool GetCertSerialNumber(PCCERT_CONTEXT context, std::string &serialNumber);

   static bool GetCertFriendlyName(PCCERT_CONTEXT context, CORE::wstr &friendlyName);

   static int GetNumCertsInChainCtx(PCCERT_CHAIN_CONTEXT certChainCtx);

   static bool ConvertChainCtxToPEM(PCCERT_CHAIN_CONTEXT certChainCtx,
                                    std::vector<std::string> &certChainVec);

   std::vector<cedar::windows::unique_cert_context> RetrieveCACertificates(
      const CertificateStoreType &store, PCCERT_CONTEXT certContext) override;

   std::vector<cedar::windows::unique_cert_context> GetCertificateChain(
      const CertificateStoreType &store, PCCERT_CONTEXT certContext) override;

   static std::vector<cedar::windows::unique_cert_context> ExtractCertificatesFromChain(
      PCCERT_CHAIN_CONTEXT chertChainCtx, bool rootFirst = true);

   static std::string ConvertCertCtxToPEM(PCCERT_CONTEXT ctx);

   static PCCERT_CONTEXT ConvertPEMToCertCtx(const std::string &pem);

   bool BuildCertificateChain(const CertificateStoreType &store, PCCERT_CONTEXT certContext,
                              PCCERT_CHAIN_CONTEXT *chainContext) override;

   static bool ConvertChainCtxToPEM(PCCERT_CHAIN_CONTEXT certChainCtx, std::string &chainStr,
                                    bool skipClientCert);

   static bool ConvertRootCaCtxToPEM(PCCERT_CHAIN_CONTEXT certChainCtx, std::string &rootCaStr);

   static bool VerifyCertChain(PCCERT_CHAIN_CONTEXT certChainCtx, CORE::wstr &hostname,
                               bool ignoreUntrustedRoot, bool ignoreWrongHost, DWORD &errorCode);

   static bool VerifyCertRevocation(PCCERT_CHAIN_CONTEXT certChainCtx, PCCERT_CONTEXT endCertCtx,
                                    DWORD &chainTrustStatus, DWORD &chainTrustInfo);

   static std::vector<std::string> GetThumbprints(const std::string &pemEncodedCert);

   static std::string GetThumbprint(PCCERT_CONTEXT certContext,
                                    HashGenerator *hashGenerator = nullptr);

   static std::vector<BYTE> GetThumbprintBinary(PCCERT_CONTEXT certContext,
                                                HashGenerator *hashGenerator = nullptr);

   static PCCERT_CHAIN_CONTEXT ExtractLongestChain(const HCERTSTORE store);

   static PCCERT_CHAIN_CONTEXT ConvertPEMToCertChainCtx(const std::string &pem);

private:
   static bool RetrieveCACertificateChain(const HCERTSTORE store, PCCERT_CONTEXT certContext,
                                          std::string &caCrt);

   static bool RetrieveRootCACertificate(HCERTSTORE certStore, PCCERT_CONTEXT certContext,
                                         std::string &rootCaCrt);

   bool ImportCertificate(const HCERTSTORE store, const std::string &certToImport,
                          const std::string &friendlyName, const std::string &privateKey,
                          const std::string &privKeyContainerName, bool allCertsFriendlyName,
                          std::vector<std::string> *thumbprints);

   static bool OpenCertificateStore(const CertificateStoreType &store, HCERTSTORE &hStore,
                                    bool &shouldCloseStore);

   static bool BuildCertificateChain(const HCERTSTORE store, PCCERT_CONTEXT certContext,
                                     PCCERT_CHAIN_CONTEXT *chainContext);

   static CORE::mstr FileTimeToMstr(LPFILETIME fileTime);
};
