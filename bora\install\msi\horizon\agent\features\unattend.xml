<?xml version="1.0" encoding="utf-8"?>
<unattend xmlns="urn:schemas-microsoft-com:unattend">
    <settings pass="generalize">
        <component name="Microsoft-Windows-Security-SPP" 
                   processorArchitecture="amd64" 
                   publicKeyToken="31bf3856ad364e35" language="neutral" 
                   versionScope="nonSxS" 
                   xmlns:wcm="http://schemas.microsoft.com/WMIConfig/2002/State" 
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <SkipRearm>1</SkipRearm>
        </component>
    </settings>
    <settings pass="specialize">
        <component name="Microsoft-Windows-Deployment" 
                   processorArchitecture="amd64" 
                   publicKeyToken="31bf3856ad364e35" language="neutral" 
                   versionScope="nonSxS" 
                   xmlns:wcm="http://schemas.microsoft.com/WMIConfig/2002/State" 
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <RunSynchronous>
                <RunSynchronousCommand wcm:action="add">
                    <Order>1</Order>
                    <Path>net user Administrator /active:yes</Path>
                </RunSynchronousCommand>
            </RunSynchronous>
        </component>
        <component name="Microsoft-Windows-Shell-Setup" 
                   processorArchitecture="amd64" 
                   publicKeyToken="31bf3856ad364e35" language="neutral" 
                   versionScope="nonSxS" 
                   xmlns:wcm="http://schemas.microsoft.com/WMIConfig/2002/State" 
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <ComputerName>placeholder</ComputerName>
        </component>
        <component name="Microsoft-Windows-UnattendedJoin" 
                   processorArchitecture="amd64" 
                   publicKeyToken="31bf3856ad364e35" language="neutral" 
                   versionScope="nonSxS" 
                   xmlns:wcm="http://schemas.microsoft.com/WMIConfig/2002/State" 
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <Identification>
                <Credentials>
                    <Domain>placeholder</Domain>
                    <Password>placeholder</Password>
                    <Username>placeholder</Username>
                </Credentials>
                <JoinDomain>placeholder</JoinDomain>
                <UnsecureJoin>false</UnsecureJoin>
            </Identification>
        </component>
    </settings>
    <settings pass="oobeSystem">
        <component name="Microsoft-Windows-Shell-Setup" 
                   processorArchitecture="amd64" 
                   publicKeyToken="31bf3856ad364e35" language="neutral" 
                   versionScope="nonSxS" 
                   xmlns:wcm="http://schemas.microsoft.com/WMIConfig/2002/State" 
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <OOBE>
                <HideEULAPage>true</HideEULAPage>
                <HideLocalAccountScreen>true</HideLocalAccountScreen>
                <HideOEMRegistrationScreen>true</HideOEMRegistrationScreen>
                <HideOnlineAccountScreens>true</HideOnlineAccountScreens>
                <ProtectYourPC>3</ProtectYourPC>
                <UnattendEnableRetailDemo>false</UnattendEnableRetailDemo>
                <VMModeOptimizations>
                    <SkipAdministratorProfileRemoval>true</SkipAdministratorProfileRemoval>
                    <SkipNotifyUILanguageChange>true</SkipNotifyUILanguageChange>
                    <SkipWinREInitialization>true</SkipWinREInitialization>
                </VMModeOptimizations>
                <SkipMachineOOBE>true</SkipMachineOOBE>
                <SkipUserOOBE>true</SkipUserOOBE>
            </OOBE>
        </component>
    </settings>
</unattend>
