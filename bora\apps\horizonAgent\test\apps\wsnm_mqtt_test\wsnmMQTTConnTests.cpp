/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * wsnmMQTTConnTests.cpp
 *
 *      Handles the functional tests of the MQTT part
 */

#include "wsnmMQTTTestUtil.h"
#include "azureADMonitor.h"
#include "utMock.h"
#include "mocks/mockHttpRequest.h"
#include "mocks/mockCertImpl.h"
#include "mocks/mockDomainUtil.h"
#include "mocks/mockFile.h"
#include "mocks/mockDirectory.h"
#include "UEMHandler.h"
#include <restartMonitor.h>
#include <fstream>
#include <TlHelp32.h>
#include <filesystem>
#include <stdexcept>

#include "gmock-helpers.h"
#include "mfw-helpers.h"

#include "utilCorestring.h"
#include "utilFile.h"
#include "events.h"

HANDLE g_mPubEvent;
typedef void (MqttManager::*FPPublishDesktopStatus)(const wstr &, ArgType, const wstr &, bool);


class MQTTConnTests : public MqttTest, public MqttTestUtil {
public:
   MQTTConnTests() { KillActiveMosquittoProcess(); }

   ~MQTTConnTests() {}

   static void on_publish_success(void *context, MQTTAsync_successData5 *response);

   const unsigned int NUM_LARGE_MSG_ITERATIONS = 20;


   static void SetUpTestCase() { s_mockDomain = std::make_shared<MockDomain>(); }


   static void TearDownTestCase() { s_mockDomain.reset(); }


   inline static std::shared_ptr<MockDomain> s_mockDomain = nullptr;

protected:
   virtual void SetUp() {}


   virtual void TearDown()
   {
      TemplateHandler::GetInstance().ClearDomainInterface();
      MqttManager::GetInstance().ClearCertInterface();
      MqttManager::GetInstance().ClearHttpRequestFactory();
   }


   void SetPendingLCMSpecInRegistry()
   {
      SetRegistryNodeManager("mqtt_ShortTemplateId_pend", "2-TEST");
      SetRegistryNodeManager("mqtt_VmId_pend", "AGENT2-TEST");
      SetRegistryNodeManager("mqtt_EdgeId_pend", "TEMPLATE");
      SetRegistryNodeManager("mqtt_AgentIdentity_pend", mAgentIdentity.c_str());
      SetRegistryNodeManager("mqtt_Otp", "WmkzIzNb6nlBgCGAv3z");
      SetRegistryNodeManager("mqtt_OtpUrl", "xxxxxx");
   }

   void SetAWSInRegistry()
   {
      SetRegistryNodeManager("mqtt_aws_pair_version", "1.0");
      SetRegistryNodeManager("mqtt_aws_refresh_url", "xyz.com");
      SetRegistryNodeManager("mqtt_aws_refresh_token", "xabcdef");
      SetRegistryNodeManager("mqtt_aws_lcm_url", "abc.com");
      SetRegistryNodeManager("mqtt_aws_access_token", "myexpiredrefreshtoken");
   }

   void SetActiveCredentialsInRegistry()
   {
      SetRegistryNodeManager("mqtt_ShortTemplateId", "2-TEST");
      SetRegistryNodeManager("mqtt_VmId", "AGENT2-TEST");
      SetRegistryNodeManager("mqtt_EdgeId", "TEMPLATE");
      SetRegistryNodeManager("mqtt_AgentIdentity", mAgentIdentity.c_str());
      std::string mqttEndpoint = "ssl://" + mMQTTEndpointHost + ":" + mMQTTEndpointPort;
      SetRegistryNodeManager("mqtt_EndpointUrl", mqttEndpoint.c_str());
      SetRegistryNodeManager("mqtt_BootstrapTopic", "desktop/agent/bootstrap");
   }

   // Create process wrapper function - taken from hzMonServiceTes
   bool LaunchProcess(std::wstring cmdStr, PROCESS_INFORMATION &pi)
   {
      STARTUPINFO info = {0};
      info.cb = sizeof(info);
      LPWSTR cmd = const_cast<wchar_t *>(cmdStr.c_str());
      if (!CreateProcess(NULL, cmd, NULL, NULL, FALSE, NORMAL_PRIORITY_CLASS, NULL, NULL, &info,
                         &pi)) {
         SYSMSG_FUNC(Debug, "CreateProcess failed to launch the command: %s Error: %s", cmd,
                     wstr::formatError());
         return false;
      }
      return true;
   }


   bool isConnected() { return MqttManager::GetInstance().IsConnected(); }


   // Create start command for MQTT broker
   std::wstring MQTTStartCmd()
   {
      WriteMosquittoConfFile(L"mosquitto_omnissa.conf", "mosquitto_broker_ca.crt",
                             "mosquitto_broker_omnissa.crt", "mosquitto_broker_omnissa.key");
      std::wstring mqttBrokerCmdStr = L"\"" + mMosquittoDir + L"\\mosquitto.exe\" -c \"" +
                                      mConfigDir + L"\\mosquitto_omnissa.conf\" -v";
      return mqttBrokerCmdStr;
   }


   std::wstring MQTTStartCmdLegacy()
   {
      WriteMosquittoConfFile(L"mosquitto_localhost.conf", "mosquitto_broker_ca.crt",
                             "mosquitto_broker_localhost.crt", "mosquitto_broker_localhost.key");
      std::wstring mqttBrokerCmdStr = L"\"" + mMosquittoDir + L"\\mosquitto.exe\" -c \"" +
                                      mConfigDir + L"\\mosquitto_localhost.conf\" -v";
      return mqttBrokerCmdStr;
   }


   std::wstring MQTTPublishTopicMessage(const std::wstring &topic, const std::wstring &msg)
   {
      std::wstring formattedTopic = L"-t \"" + topic + L"\"";
      std::wstring formattedMsg = L"-m \"" + msg + L"\"";
      std::wstring mqttPubFullStr = mMQTTPubCmd + L" " + formattedTopic + L" " + formattedMsg +
                                    L" " + mMosquittoClientCertPaths;
      return mqttPubFullStr;
   }


   std::wstring MQTTSubscribeToTopic(const std::wstring &topic)
   {
      std::wstring formattedTopic = L"-t \"" + topic + L"\"";
      std::wstring mqttSubFullStr =
         mMQTTSubCmd + L" " + formattedTopic + L" " + mMosquittoClientCertPaths;
      return mqttSubFullStr;
   }


   std::wstring GetTopicName(AllTopics topic)
   {
      return MqttTestUtil::GetTopicName(topic, mAgentEdgeId, mAgentTemplateId, mAgentVmId);
   }


   void CreateTestEvent() { g_mPubEvent = CreateEvent(0, false, false, NULL); }


   /*
    *--------------------------------------------------------------------------
    *
    * GetExpectedLcmFetchPayload --
    *
    *    Gets the expected payload for a fetch lcm vmhub post call.
    *
    * Results:
    *    None
    *
    * Side effects:
    *    None.
    *
    *--------------------------------------------------------------------------
    */

   std::string GetExpectedLcmFetchPayload(const wstr &ip, const std::string &version)
   {
      static mstr s_computerName;
      if (s_computerName.empty()) {
         s_computerName = mstr::getComputerName();
      }

      const std::string expectedPayloadFormat = R"({)"
                                                R"("ip":"%s",)"
                                                R"("version":"%s",)"
                                                R"("vmId":"%s")"
                                                R"(})";
      return mstr::printf(expectedPayloadFormat.c_str(), wstr::to_mstr(ip).c_str(), version.c_str(),
                          s_computerName.c_str())
         .c_str();
   }


   /*
    *--------------------------------------------------------------------------
    *
    * SetupRedeemOTPMockLegacy --
    *
    *    Mocks OTP redemption for legacy certificates. Does not hook up the
    *    request.
    *
    * Results:
    *    None
    *
    * Side effects:
    *    None.
    *
    *--------------------------------------------------------------------------
    */

   void SetupRedeemOTPMockLegacy(std::shared_ptr<MockHttpRequest> mockReq)
   {
      std::string caCrt, clientCrt, mockResp;
      READ_FILE_INTO_STRING(mLegacyCACertPath, caCrt, false);
      READ_FILE_INTO_STRING(mLegacyClientCrtPath, clientCrt, false);
      mockResp = GenerateOtpRedemptionResponse(caCrt, clientCrt);

      EXPECT_CALL(*mockReq, postSync(_, _, _, _))
         .WillOnce(DoAll(SetArgReferee<3>(mockResp), Return(true)));
      EXPECT_CALL(*mockReq, enableCertRevocation()).Times(0);
      SetupMockHttpRequestProxy(mockReq);
   }

   std::wstring mMockPublish = mConfigDir + L"\\GetIconsResp.json";
   std::wstring mLegacyKeyBinPath = mConfigDir + L"\\encodedKey.bin";
   std::wstring mLegacyCSRBinPath = mConfigDir + L"\\encodedCSR.bin";
   std::wstring mLegacyPkcsPath = mConfigDir + L"\\DataBlob.bin";

   std::wstring mLegacyCACertPath = mConfigDir + L"\\ca.crt";
   std::wstring mLegacyClientCrtPath = mConfigDir + L"\\legacy-client.crt";
   std::wstring mOmnissaClientCrtPath = mConfigDir + L"\\omnissa-client.crt";
   std::wstring mOmnissaClientCACrtPath = mConfigDir + L"\\omnissa-client-ca.crt";
   std::wstring mOmnissaClientPkcsPath = mConfigDir + L"\\omnissa-client-pkcs.bin";
   std::wstring mOmnissaClientKeyBinPath = mConfigDir + L"\\omnissa-client-key.bin";
   std::wstring mOmnissaClientCSRBinPath = mConfigDir + L"\\omnissa-client-csr.bin";
   std::wstring mOmnissaCARootPath = mConfigDir + L"\\omnissa.crt";
   std::wstring mMosquittoClientKeyPath = mConfigDir + L"\\mosquitto_client.key";
   std::wstring mMosquittoClientCertPath = mConfigDir + L"\\mosquitto_client.crt";
   std::wstring mMosquittoClientCaPath = mConfigDir + L"\\mosquitto_broker_ca.crt";
   std::wstring mBootStrapPath = mConfigDir + L"\\BootStrapSample.json";
   std::wstring mSubTextPath = mConfigDir + L"\\Sub.txt";
   std::wstring mMosquittoOuputPath = mConfigDir + L"\\Mosquitto.log";
   std::wstring mHorizonClientCrtPath = mConfigDir + L"\\horizon-client.crt";
   std::wstring mHorizonClientCACrtPath = mConfigDir + L"\\horizon-client-ca.crt";
   std::wstring mHorizonClientPkcsPath = mConfigDir + L"\\horizon-client-pkcs.bin";
   std::wstring mHorizonClientKeyBinPath = mConfigDir + L"\\horizon-client-key.bin";
   std::wstring mHorizonClientCSRBinPath = mConfigDir + L"\\horizon-client-csr.bin";

   std::wstring mMQTTCollectMsg =
      LR"({\"interval\":60, \"SessionReportConfig\": {\"UNWANTEDFIELDS\": [\"one\",\"two\"]}})";
   std::wstring mCommandMsg =
      LR"({\"ID\":\"eb7c6e8f-c4b6-4659-bcbe-ada769e30e30\", \"COMMAND\": \"SomeCommand\"})";
   std::wstring mCommandMsg2 =
      LR"({\"ID\":\"40016ab0-0aa9-4f24-a840-f804b72f7f39\", \"COMMAND\": \"SomeCommand\"})";
   std::wstring mOtpTokenMsg = LR"({\"otp\":\"xabcdef\", \"vmHubUrl\":\"abc.com\"})";

   std::string mAgentIdentity = "TEMPLATE2-TEST_AGENT2-TEST";
   std::wstring mAgentEdgeId = L"TEMPLATE";
   std::wstring mAgentTemplateId = L"2-TEST";
   std::wstring mAgentVmId = L"AGENT2-TEST";
   std::wstring LCMSpecFolder = L"C:\\AzureData";
   std::wstring mLCMSpecFilePath = LCMSpecFolder + L"\\CustomData.bin";

   std::wstring mMQTTPubCmd =
      L"\"" + mMosquittoDir + L"\\mosquitto_pub.exe\" -h localhost -p 8883 --insecure -i pubclient";

   std::wstring mMQTTSubCmd =
      L"\"" + mMosquittoDir + L"\\mosquitto_sub.exe\" -h localhost -p 8883 --insecure -i subclient";

   std::wstring mMosquittoClientCertPaths = L"--cafile \"" + mMosquittoClientCaPath +
                                            L"\" --cert \"" + mMosquittoClientCertPath +
                                            L"\" --key \"" + mMosquittoClientKeyPath + L"\"";

   std::string mAwsOtpFileContents =
      R"({"token":"xabcdef","url":"abc.com","version":"1.0","aturl":"xyz.com"})";

   std::string mLcmSpecFileContents = R"({)"
                                      R"("vmId":"AGENT2-TEST",)"
                                      R"("shortTemplateId":"2-TEST",)"
                                      R"("vmHub":{)"
                                      R"("url":"https://vmhub.azurefd.net/vmhub/credentials",)"
                                      R"("oneTimePassword":"zxLxBy2B7lnzkofz7MiUxXGfGkMmOYCP",)"
                                      R"("sslCertThumbprint":"",)"
                                      R"("sslCertChain":"")"
                                      R"(},)"
                                      R"("edgeGateway":{)"
                                      R"("id":"TEMPLATE",)"
                                      R"("managementIp":"***********",)"
                                      R"("fqdn":"myfqdn.addr.net",)"
                                      R"("port":"9876")"
                                      R"(})"
                                      R"(})";

   std::string mRefreshTokenContents = R"({)"
                                       R"("id":"67bfbc3a1a11f237e0ef87a8",)"
                                       R"("orgId":"6d4a3a51-914e-4035-8153-6b133bb90b2b",)"
                                       R"("location":"US",)"
                                       R"("tokenId":"36d1973f-c1f5-4925-b657-f7d602a6ba8e",)"
                                       R"("token":"myrefreshtoken",)"
                                       R"("tokenType":"access_token",)"
                                       R"("expiresAt":"2025-02-27T01:44:06.546+00:00",)"
                                       R"("createdAt":"2025-02-27T01:14:06.546+00:00",)"
                                       R"("modifiedAt":null,)"
                                       R"("version":0)"
                                       R"(})";
};


static vmstr
AddThumbprintPEMEntries(std::map<std::string, std::string> &map, std::string pem)
{
   vmstr thumbprints;
   auto pems = CertUtilityInterface::ExtractPEMCertificates(pem);
   auto tps = WinCertImplementation::GetThumbprints(pem);
   if (pems.size() != tps.size()) {
      throw std::runtime_error("Missing some thumbprints");
   }
   for (size_t i = 0; i < pems.size(); ++i) {
      map.insert({tps[i], pems[i]});
      thumbprints.push_back(tps[i]);
   }
   return thumbprints;
}


void
MQTTConnTests::on_publish_success(void *context, MQTTAsync_successData5 *response)
{
   if (g_mPubEvent) {
      bool ab = SetEvent(g_mPubEvent);
      if (!ab) {
         SYSMSG_FUNC(Debug, L"Failed to set Event error: %d", GetLastError());
      }
   }
}


// Mocks OTP redemption for legacy certificates. Hooks it up to a factory
#define MOCK_VMHUB_OTP_REDEMPTION()                                                                \
   READ_FILE_INTO_STRING(mLegacyCACertPath, caCrt, false);                                         \
   READ_FILE_INTO_STRING(mLegacyClientCrtPath, clientCrt, false)                                   \
   mockResp = GenerateOtpRedemptionResponse(caCrt, clientCrt);                                     \
   auto mockHttpReqFactory = std::make_shared<MockHttpRequestFactory>();                           \
   auto mockHttp = std::make_shared<MockHttpRequest>();                                            \
   EXPECT_CALL(*mockHttpReqFactory, GetHttpRequestClient()).WillOnce(Return(mockHttp));            \
   EXPECT_CALL(*mockHttp, postSync(_, _, _, _))                                                    \
      .WillOnce(DoAll(SetArgReferee<3>(mockResp), Return(true)));                                  \
   EXPECT_CALL(*mockHttp, enableCertRevocation()).Times(0);                                        \
   SetupMockHttpRequestProxy(mockHttp);


#define MOCK_WINDOWS_CERT_IMPORT_FIND(n)                                                           \
   EXPECT_CALL(*mockCert__, ImportCertificateToStore(GMOCK_BLANK_ARG7))                            \
      .Times(n)                                                                                    \
      .WillRepeatedly([&thumbprintToPEMMap](const CertificateStoreType &,                          \
                                            const std::string &certToImport, const std::string &,  \
                                            const std::string &, const std::string &, bool,        \
                                            std::vector<std::string> *thumbprints) {               \
         auto pems = CertUtilityInterface::ExtractPEMCertificates(certToImport);                   \
         auto tps = WinCertImplementation::GetThumbprints(certToImport);                           \
         if (pems.size() != tps.size()) {                                                          \
            throw std::runtime_error("Missing some thumbprints");                                  \
         }                                                                                         \
         for (size_t i = 0; i < pems.size(); ++i) {                                                \
            thumbprintToPEMMap.insert({tps[i], pems[i]});                                          \
            if (thumbprints) {                                                                     \
               thumbprints->push_back(tps[i]);                                                     \
            }                                                                                      \
         }                                                                                         \
         return true;                                                                              \
      });                                                                                          \
   EXPECT_CALL(*mockCert__, FindCertificates(_, Matcher<const std::vector<std::string> &>(_), _))  \
      .WillRepeatedly([&thumbprintToPEMMap](const CertificateStoreType &,                          \
                                            const std::vector<std::string> &tps,                   \
                                            std::vector<std::vector<BYTE>> *) {                    \
         std::vector<cedar::windows::unique_cert_context> certCtxs;                                \
         for (const auto &tp : tps) {                                                              \
            auto pem = thumbprintToPEMMap[tp];                                                     \
            if (pem.empty()) {                                                                     \
               throw std::runtime_error("Thumbprint not found");                                   \
            }                                                                                      \
            cedar::windows::unique_cert_context certCtx(                                           \
               WinCertImplementation::ConvertPEMToCertCtx(pem));                                   \
            certCtxs.push_back(std::move(certCtx));                                                \
         }                                                                                         \
         return std::move(certCtxs);                                                               \
      });


#define MOCK_WINDOWS_CERT_API_LEGACY()                                                             \
   READ_FILE_INTO_STRING(mLegacyKeyBinPath, mockEncodedKey, true)                                  \
   READ_FILE_INTO_STRING(mLegacyCSRBinPath, mockEncodedCSR, true)                                  \
   READ_FILE_INTO_STRING(mLegacyPkcsPath, dataBlob, true)                                          \
   EXPECT_CALL(*mockCert__, GenerateKeyUsingWin32API(_, _, _))                                     \
      .WillOnce(DoAll(SetArgReferee<1>(mockEncodedKey), Return(true)));                            \
   EXPECT_CALL(*mockCert__, GenerateCSRUsingWin32API(GMOCK_BLANK_ARG7))                            \
      .WillOnce(DoAll(SetArgReferee<2>(mockEncodedCSR), Return(true)));                            \
   EXPECT_CALL(*mockCert__, ExportCertificateAndKeyFromStore(GMOCK_BLANK_ARG7))                    \
      .Times(2)                                                                                    \
      .WillRepeatedly(                                                                             \
         DoAll(SetArgReferee<2>(dataBlob), SetArgReferee<3>(mAgentIdentity), Return(true)));       \
   EXPECT_CALL(*mockCert__, RetrieveSystemCertificates(_)).WillOnce(Return(true));                 \
   EXPECT_CALL(*mockCert__, DeleteCertificates(_, Matcher<const std::string &>(                    \
                                                     StrEq(MQTT_CLIENT_CERT_FRIENDLY_NAME))))      \
      .WillOnce(Return());                                                                         \
   EXPECT_CALL(*mockCert__, GetCertificateStore(_)).WillRepeatedly([](auto &&...args) {            \
      CertificateStoreType store("My");                                                            \
      return store;                                                                                \
   });                                                                                             \
   MOCK_WINDOWS_CERT_IMPORT_FIND(2)


#define MOCK_WINDOWS_CERT_API(n)                                                                   \
   READ_FILE_INTO_STRING(mOmnissaClientCrtPath, clientCrt, false)                                  \
   READ_FILE_INTO_STRING(mOmnissaClientPkcsPath, pkcsBin, true)                                    \
   READ_FILE_INTO_STRING(mOmnissaClientCACrtPath, caCrt, false)                                    \
   auto caTps = AddThumbprintPEMEntries(thumbprintToPEMMap, caCrt);                                \
   auto clientTps = AddThumbprintPEMEntries(thumbprintToPEMMap, clientCrt);                        \
   SetRegistryNodeManager(MQTT_CA_THUMBPRINTS_REG, caTps);                                         \
   SetRegistryNodeManager(MQTT_CLIENT_THUMBPRINTS_REG, clientTps);                                 \
   EXPECT_CALL(*mockCert__, ExportCertificateAndKeyFromStore(GMOCK_BLANK_ARG7))                    \
      .Times(2)                                                                                    \
      .WillRepeatedly(                                                                             \
         DoAll(SetArgReferee<2>(pkcsBin), SetArgReferee<3>(mAgentIdentity), Return(true)));        \
   EXPECT_CALL(*mockCert__, RetrieveSystemCertificates(_)).WillOnce(Return(true));                 \
   EXPECT_CALL(*mockCert__, GetCertificateStore(_)).WillRepeatedly([](auto &&...args) {            \
      CertificateStoreType store("My");                                                            \
      return store;                                                                                \
   });                                                                                             \
   MOCK_WINDOWS_CERT_IMPORT_FIND(n)


/*
 * All customizations are associated with a registry key so we know
 * what conditions to check for. In case of domain join, we check that
 * condition only if AGENT_CONFIG_ONPREM_AD key is set. Since we set
 * LCM spec in registry, we need to have regular registry read calls happen
 */
#define MOCK_DOMAIN_INTF(n)                                                                        \
   MOCK_ON_PREM_AD_ATTEMPTED()                                                                     \
   EXPECT_CALL(*s_mockDomain, IsComputerInDomain(_))                                               \
      .Times(n)                                                                                    \
      .WillRepeatedly(DoAll(SetArgReferee<0>(true), Return(true)));                                \
   MOCK_UEM_NOT_INSTALLED()

#define MOCK_HYBRID_JOIN()                                                                         \
   VMOCK_V(hybridRegInterceptor, (FPreadRegistry) & wstr::readRegistry)                            \
      .WillRepeatedly(                                                                             \
         [&hybridRegInterceptor](const wchar_t *path, const wchar_t *defVal, bool useWow6432) {    \
            wstr regPath = path;                                                                   \
            if (regPath.findi(AGENT_CONFIG_WAIT_FOR_HYBRID_JOIN) != -1)                            \
               return wstr(L"1");                                                                  \
            return hybridRegInterceptor.CallRealFunc(path, defVal, useWow6432);                    \
         });                                                                                       \
   MOCK_UEM_NOT_INSTALLED()

#define WSNM_MQTT_SETUP(n)                                                                         \
   MOCK_VMHUB_OTP_REDEMPTION()                                                                     \
   MOCK_MFW_REGISTRATION(n)                                                                        \
   MOCK_DOMAIN_INTF(n)                                                                             \
   MOCK_WINDOWS_CERT_API_LEGACY()

#define WSNM_MQTT_SETUP_UEM()                                                                      \
   MOCK_VMHUB_OTP_REDEMPTION()                                                                     \
   MOCK_MFW_REGISTRATION(1)                                                                        \
   MOCK_UEM_INSTALLED()                                                                            \
   MOCK_WINDOWS_CERT_API_LEGACY()

#define WSNM_MQTT_SETUP_NO_BOOTSTRAP()                                                             \
   MOCK_MFW_REGISTRATION(1)                                                                        \
   MOCK_DOMAIN_INTF(1)                                                                             \
   MOCK_WINDOWS_CERT_API(0)

#define WSNM_MQTT_SETUP_HYBRID_JOIN()                                                              \
   MOCK_VMHUB_OTP_REDEMPTION()                                                                     \
   MOCK_MFW_REGISTRATION(1)                                                                        \
   MOCK_HYBRID_JOIN()                                                                              \
   MOCK_WINDOWS_CERT_API_LEGACY()


// Makes MqttManager registration cycles more deterministic.
#define SETUP_RETRY_INTERVALS()                                                                    \
   VMOCK_V(GetStartupMinWait, &mqttConfig::getStartupMinWaitInterval)                              \
      .ExpectCall()                                                                                \
      .WillRepeatedly(Return(1));                                                                  \
   VMOCK_V(GetStartupMaxWait, &mqttConfig::getStartupMaxWaitInterval)                              \
      .ExpectCall()                                                                                \
      .WillRepeatedly(Return(2));                                                                  \
   VMOCK_V(GetStartupMinJitter, &mqttConfig::getStartupMinJitter)                                  \
      .ExpectCall()                                                                                \
      .WillRepeatedly(Return(0));                                                                  \
   VMOCK_V(GetStartupMaxJitter, &mqttConfig::getStartupMaxJitter)                                  \
      .ExpectCall()                                                                                \
      .WillRepeatedly(Return(0));


// Skip the master image MAC address flow
#define SETUP_CLONE_FLOW(n, m)                                                                     \
   VMOCK_V(MockStoredMacAddressZeros, &mqttConfig::getStoredMACAddress)                            \
      .ExpectCall()                                                                                \
      .Times(n)                                                                                    \
      .WillRepeatedly(Return("00:00:00:00:00:00"));                                                \
   VMOCK_V(MockNicAddresses, &util::WinInet::GetAllNICMacAddresses)                                \
      .ExpectCall(_)                                                                               \
      .Times(m)                                                                                    \
      .WillRepeatedly([](std::vector<CORE::wstr> &allNICs) {                                       \
         allNICs.push_back(L"00:00:00:00:00:01");                                                  \
         allNICs.push_back(L"00:00:00:00:00:02");                                                  \
         return true;                                                                              \
      });


#define SETUP_VALID_AWS_FILE()                                                                     \
   bool awsOtpFileExists = true;                                                                   \
   std::shared_ptr<MockFile> azureFile, awsFile;                                                   \
   std::shared_ptr<MockFileFactory> fileFactory;                                                   \
   MqttManagerHookUpFile(azureFile, awsFile, fileFactory);                                         \
   EXPECT_CALL(*azureFile, FileExists()).WillRepeatedly(Return(false));                            \
   std::string awsOtpEncoded = mstr(mAwsOtpFileContents).base64Encode().c_str();                   \
   SetupValidFile(awsOtpFileExists, awsOtpEncoded, awsFile);


#define SETUP_NO_FILE_EXISTENCE()                                                                  \
   bool awsOtpFileExists = false;                                                                  \
   std::shared_ptr<MockFile> azureFile, awsFile;                                                   \
   std::shared_ptr<MockFileFactory> fileFactory;                                                   \
   MqttManagerHookUpFile(azureFile, awsFile, fileFactory);                                         \
   EXPECT_CALL(*azureFile, FileExists()).WillRepeatedly(Return(false));                            \
   EXPECT_CALL(*awsFile, FileExists()).WillRepeatedly(Return(false));


/*
 *-----------------------------------------------------------------------------
 *
 * MQTTConnTests::KillMosquittoTest
 *
 *    Tests the multiple reconnect scenario by killing the Mosquitto broker.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MQTTConnTests, KillMosquittoTest)
{
   std::wstring testName = GetTestName();
   std::wstring mosquittoLogPath = mConfigDir + L"\\" + testName + L".log";

   DeleteLCMSpecInRegistry();

   std::shared_ptr<MockCertImpl> mockCert__ = std::make_shared<MockCertImpl>();
   SetPendingLCMSpecInRegistry();

   // Set up the mock interface and dummy data needed for mocking
   std::string mockResp, mockEncodedKey, mockEncodedCSR, dataBlob, clientCrt, caCrt;
   std::map<std::string, std::string> thumbprintToPEMMap;

   // Since we will disconnect twice, we expect to see some calls 3 times
   WSNM_MQTT_SETUP(3);

   // Start the MQTT broker in another process
   PROCESS_INFORMATION mqttBrokerPI = {0};
   HANDLE mosquittoOutputFileHandle = NULL;
   EXPECT_TRUE(ProcUtil::LaunchProcessWithStdOut(MQTTStartCmdLegacy(), mosquittoLogPath,
                                                 mqttBrokerPI, mosquittoOutputFileHandle, false));
   Sleep(1000);

   StartApp();

   // Set the mock interfaces
   SETUP_NO_FILE_EXISTENCE();
   MqttManager::GetInstance().SetHttpRequestFactory(mockHttpReqFactory);
   MqttManager::GetInstance().SetCertInterface(mockCert__);
   TemplateHandler::GetInstance().SetDomainInterface(s_mockDomain);

   // Start the connection process
   EXPECT_TRUE(MqttManager::GetInstance().Start());
   Sleep(3000);

   EXPECT_TRUE(isConnected());

   // Stop the broker
   EXPECT_TRUE(CloseProcess(mqttBrokerPI));
   CloseHandle(mosquittoOutputFileHandle);
   mosquittoOutputFileHandle = NULL;

   Sleep(2000);

   // Restart the broker. Append broker logs to the already existing file.
   PROCESS_INFORMATION mqttBrokerRestartPI = {0};
   EXPECT_TRUE(ProcUtil::LaunchProcessWithStdOut(MQTTStartCmdLegacy(), mosquittoLogPath,
                                                 mqttBrokerRestartPI, mosquittoOutputFileHandle,
                                                 true));
   // Give the Agent time to automatically reconnect
   Sleep(20000);

   EXPECT_TRUE(isConnected());

   // Sleep to allow main app to write to MQTT broker
   Sleep(3000);

   // Stop the broker
   EXPECT_TRUE(CloseProcess(mqttBrokerRestartPI));
   CloseHandle(mosquittoOutputFileHandle);
   mosquittoOutputFileHandle = NULL;

   Sleep(1000);

   // Restart the broker
   PROCESS_INFORMATION mqttBrokerSecondRestartPI = {0};
   EXPECT_TRUE(ProcUtil::LaunchProcessWithStdOut(MQTTStartCmdLegacy(), mosquittoLogPath,
                                                 mqttBrokerSecondRestartPI,
                                                 mosquittoOutputFileHandle, true));
   Sleep(20000);

   EXPECT_TRUE(isConnected());

   // Cleanup
   EXPECT_TRUE(MqttManager::GetInstance().Stop());
   EXPECT_TRUE(CloseProcess(mqttBrokerSecondRestartPI));
   CloseHandle(mosquittoOutputFileHandle);

   // Parse the MQTT broker logs to verify that it received AVAILABLE msgs
   WinFile mosquittoLogFile(mosquittoLogPath);
   auto mosquittoLog = mosquittoLogFile.ReadFileIntoString();

   // 3 AVAILABLE, 1 UNAVAILABLE messages
   size_t pos = 0;
   EXPECT_TRUE(FindPublish(mosquittoLog, mAgentIdentity, pos));
   EXPECT_TRUE(FindPublish(mosquittoLog, mAgentIdentity, pos));
   EXPECT_TRUE(FindPublish(mosquittoLog, mAgentIdentity, pos));
   EXPECT_TRUE(FindPublish(mosquittoLog, mAgentIdentity, pos));
   EXPECT_FALSE(FindPublish(mosquittoLog, mAgentIdentity, pos));

   vmstr caTps = vmstr::readRegistry(
      "HKLM\\SOFTWARE\\Omnissa\\Horizon\\Node Manager\\" MQTT_CA_THUMBPRINTS_REG);
   EXPECT_EQ(caTps.size(), 1);
   vmstr clientTps = vmstr::readRegistry(
      "HKLM\\SOFTWARE\\Omnissa\\Horizon\\Node Manager\\" MQTT_CLIENT_THUMBPRINTS_REG);
   EXPECT_EQ(clientTps.size(), 2);

   StopApp();
   DeleteLCMSpecInRegistry();
   MqttManagerClearFileFactory();
}


/*
 *-----------------------------------------------------------------------------
 *
 * MQTTConnTests::BootstrapWithNoFile
 *
 *    A simple bootstrap case where the lcm spec file has already been parsed and placed in the
 *    registry. The mqtt plugin should resume bootstrapping from this state.
 *
 *    Additionally, mock that the machine has already been joined to a domain.
 *
 *    Validate that the MQTT connection is established and that appropriate messages are sent to
 *    Mosquitto.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MQTTConnTests, BootstrapWithNoFile)
{
   std::wstring testName = GetTestName();
   std::wstring mosquittoLogPath = mConfigDir + L"\\" + testName + L".log";

   std::shared_ptr<MockCertImpl> mockCert__ = std::make_shared<MockCertImpl>();
   SetPendingLCMSpecInRegistry();

   // Set up the mock interface and dummy data needed for mocking
   std::string mockResp, mockEncodedKey, mockEncodedCSR, dataBlob, clientCrt, caCrt;
   std::map<std::string, std::string> thumbprintToPEMMap;

   WSNM_MQTT_SETUP(1);

   PROCESS_INFORMATION pi;
   cedar::windows::unique_file fileHandle;
   EXPECT_TRUE(StartMosquittoBroker(
      testName, "mosquitto_broker_ca.crt", "mosquitto_broker_localhost.crt",
      "mosquitto_broker_localhost.key", mosquittoLogPath, pi, *fileHandle.put()));

   Sleep(2000);

   // Subscribe to the status topic
   std::wstring subscribeLogPath = mConfigDir + L"\\" + testName + L"-sub.log";
   PROCESS_INFORMATION subPi = {0};
   cedar::windows::unique_file subFileHandle;
   EXPECT_TRUE(SubscribeToTopic(subscribeLogPath, MqttTestUtil::AllTopics::STATUS, mAgentEdgeId,
                                mAgentTemplateId, mAgentVmId, pi, *subFileHandle.put()));

   Sleep(2000);

   StartApp();

   // Set the mock interfaces
   SETUP_NO_FILE_EXISTENCE();
   MqttManager::GetInstance().SetHttpRequestFactory(mockHttpReqFactory);
   MqttManager::GetInstance().SetCertInterface(mockCert__);
   TemplateHandler::GetInstance().SetDomainInterface(s_mockDomain);

   // Start the connection process
   EXPECT_TRUE(MqttManager::GetInstance().Start());
   Sleep(3000);

   EXPECT_TRUE(isConnected());

   EXPECT_TRUE(MqttManager::GetInstance().Stop());

   Sleep(3000);

   CloseProcess(subPi);
   CloseProcess(pi);

   CloseHandle(subFileHandle.release());

   std::vector<Json::Value> jsonMsgs;
   ParseJsonMsgs(subscribeLogPath, jsonMsgs);

   EXPECT_EQ(jsonMsgs.size(), 2);
   EXPECT_STREQ(jsonMsgs[0]["status"].asString().c_str(), "AVAILABLE");
   EXPECT_STREQ(jsonMsgs[1]["status"].asString().c_str(), "UNAVAILABLE");
   EXPECT_STREQ(jsonMsgs[1]["reason"].asString().c_str(), "SHUTDOWN");

   DeleteLCMSpecInRegistry();
   StopApp();
   MqttManagerClearFileFactory();
}


/*
 *-----------------------------------------------------------------------------
 *
 * MQTTConnTests::ThumbprintBootstrap
 *
 *    Test case: Simulates changing the Certificate Authority store from
 *    chain-only to chain+CA thumbprints. The mosquitto broker sends an OTP
 *    token message through to the Agent to kick off the transition.
 *
 *    Includes swapping the MQTT broker from localhost signed to Omnissa signed.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MQTTConnTests, ThumbprintBootstrap)
{
   std::shared_ptr<MockCertImpl> mockCert__ = std::make_shared<MockCertImpl>();
   SetActiveCredentialsInRegistry();

   // Set up the mock interface and dummy data needed for mocking
   std::string mockResp, dataBlob, legacyClientCertPEM;
   std::map<std::string, std::string> thumbprintToPEMMap;

   // Legacy certificates
   READ_FILE_INTO_STRING(mLegacyClientCrtPath, legacyClientCertPEM, false);
   READ_FILE_INTO_STRING(mLegacyPkcsPath, dataBlob, true);

   // Preload the client cert into our mocked store
   AddThumbprintPEMEntries(thumbprintToPEMMap, legacyClientCertPEM);

   // Omnissa certificates
   std::string omnCaCrt, omnClientCrt, omnPkcs, omnKeyBin, omnCSRBin;

   READ_FILE_INTO_STRING(mOmnissaClientCACrtPath, omnCaCrt, false);
   READ_FILE_INTO_STRING(mOmnissaClientCrtPath, omnClientCrt, false);
   READ_FILE_INTO_STRING(mOmnissaClientPkcsPath, omnPkcs, true);
   READ_FILE_INTO_STRING(mOmnissaClientKeyBinPath, omnKeyBin, true);
   READ_FILE_INTO_STRING(mOmnissaClientCSRBinPath, omnCSRBin, true);

   std::string omnHttpResponse = GenerateOtpRedemptionResponse(omnCaCrt, omnClientCrt);

   auto omnCaThumbprints = WinCertImplementation::GetThumbprints(omnCaCrt);

   // Mock MFW and Domain
   MOCK_MFW_REGISTRATION(3);
   MOCK_DOMAIN_INTF(3);

   SETUP_NO_FILE_EXISTENCE();

   // Events for refreshing the certificates, successful dry run, and swapping
   MOCK_EVENT_LOGGER(3);

   // Mock OTP redemption
   auto mockHttpReqFactory = std::make_shared<MockHttpRequestFactory>();
   auto mockHttpOmnissa = std::make_shared<MockHttpRequest>();

   EXPECT_CALL(*mockHttpReqFactory, GetHttpRequestClient()).WillOnce(Return(mockHttpOmnissa));

   EXPECT_CALL(*mockHttpOmnissa, postSync(_, _, _, _))
      .WillOnce(DoAll(SetArgReferee<3>(omnHttpResponse), Return(true)));
   EXPECT_CALL(*mockHttpOmnissa, enableCertRevocation()).Times(0);
   SetupMockHttpRequestProxy(mockHttpOmnissa);

   // Mock certificate store
   MOCK_WINDOWS_CERT_IMPORT_FIND(2);

   EXPECT_CALL(*mockCert__, GenerateKeyUsingWin32API(_, _, _))
      .WillOnce(DoAll(SetArgReferee<1>(omnKeyBin), Return(true)));
   EXPECT_CALL(*mockCert__, GenerateCSRUsingWin32API(GMOCK_BLANK_ARG7))
      .WillOnce(DoAll(SetArgReferee<2>(omnCSRBin), Return(true)));
   EXPECT_CALL(*mockCert__, ExportCertificateAndKeyFromStore(
                               _, StrEq(MQTT_CLIENT_CERT_FRIENDLY_NAME), GMOCK_BLANK_ARG5))
      .Times(4)
      .WillOnce(DoAll(SetArgReferee<2>(dataBlob), SetArgReferee<3>(mAgentIdentity), Return(true)))
      .WillOnce(DoAll(SetArgReferee<2>(dataBlob), SetArgReferee<3>(mAgentIdentity), Return(true)))
      .WillOnce(DoAll(SetArgReferee<2>(omnPkcs), SetArgReferee<3>(mAgentIdentity), Return(true)))
      .WillOnce(DoAll(SetArgReferee<2>(omnPkcs), SetArgReferee<3>(mAgentIdentity), Return(true)));
   EXPECT_CALL(*mockCert__, ExportCertificateAndKeyFromStore(
                               _, StrEq(MQTT_CLIENT_PENDING_CERT_FRIENDLY_NAME), GMOCK_BLANK_ARG5))
      .Times(2)
      .WillRepeatedly(
         DoAll(SetArgReferee<2>(omnPkcs), SetArgReferee<3>(mAgentIdentity), Return(true)));

   // Twice for each valid connection, once for the dryrun
   EXPECT_CALL(*mockCert__, RetrieveSystemCertificates(_)).Times(3).WillRepeatedly(Return(true));
   EXPECT_CALL(*mockCert__, DeleteCertificates(_, Matcher<const std::string &>(StrEq(
                                                     MQTT_CLIENT_PENDING_CERT_FRIENDLY_NAME))))
      .WillOnce(Return());
   EXPECT_CALL(*mockCert__, GetCertificateStore(_)).WillRepeatedly([](auto &&...args) {
      CertificateStoreType store("My");
      return store;
   });

   // Get the certificate based on the chain
   EXPECT_CALL(*mockCert__, GetCertificateChain(_, _))
      .Times(1)
      .WillRepeatedly([&legacyClientCertPEM](auto &&...args) {
         std::vector<std::string> pemCerts =
            CertUtilityInterface::ExtractPEMCertificates(legacyClientCertPEM);
         std::vector<cedar::windows::unique_cert_context> clientCertChain;
         for (const auto &pemCert : pemCerts) {
            cedar::windows::unique_cert_context certCtx(
               WinCertImplementation::ConvertPEMToCertCtx(pemCert));
            clientCertChain.push_back(std::move(certCtx));
         }
         return std::move(clientCertChain);
      });
   EXPECT_CALL(*mockCert__, RenameCertificate(_, StrEq(MQTT_CLIENT_CERT_FRIENDLY_NAME)))
      .WillOnce(Return(true));
   EXPECT_CALL(*mockCert__, DeleteCertificateAndPrivateKey(_)).WillOnce(Return(true));

   // Start the Legacy broker
   PROCESS_INFORMATION mqttBrokerPI = {0};
   HANDLE mosquittoOutputFileHandle = NULL;
   EXPECT_TRUE(ProcUtil::LaunchProcessWithStdOut(MQTTStartCmdLegacy(), mMosquittoOuputPath,
                                                 mqttBrokerPI, mosquittoOutputFileHandle));
   Sleep(1000);

   // Start the Event Logger subscriber
   PROCESS_INFORMATION eventLoggerPI = {0};
   HANDLE eventLoggerOutputFileHandle = NULL;
   auto subscribeCommand = MQTTSubscribeToTopic(GetTopicName(AllTopics::EVENT_LOGGER));
   EXPECT_TRUE(ProcUtil::LaunchProcessWithStdOut(subscribeCommand, mSubTextPath, eventLoggerPI,
                                                 eventLoggerOutputFileHandle));

   Sleep(1000);

   // Start mqttConfig
   StartApp();

   // Hook in the mock interfaces
   MqttManager::GetInstance().SetHttpRequestFactory(mockHttpReqFactory);
   MqttManager::GetInstance().SetCertInterface(mockCert__);
   TemplateHandler::GetInstance().SetDomainInterface(s_mockDomain);

   // Start MqttManager
   EXPECT_TRUE(MqttManager::GetInstance().Start());
   Sleep(5000);

   EXPECT_TRUE(isConnected());

   // Send OTP message. Force the cert swap.
   std::wstring otpTokenMsg =
      LR"({\"otp\":\"abc123\",\"vmHubUrl\":\"https://helloworld.com/vmhub/credentials\",\"forceCertSwap\":true})";

   EXPECT_TRUE(PublishMosquittoMessage(GetTopicName(AllTopics::OTP), otpTokenMsg));

   // Wait for wsnm_mqtt to reconnect.
   Sleep(5000);

   EXPECT_TRUE(isConnected());

   // Stop the subscriber
   CloseProcess(eventLoggerPI);
   CloseHandle(eventLoggerOutputFileHandle);
   // Swap the broker from being localhost signed to omnissa signed
   CloseProcess(mqttBrokerPI);
   CloseHandle(mosquittoOutputFileHandle);

   PROCESS_INFORMATION mqttBrokerOmnissaPI = {0};
   EXPECT_TRUE(LaunchProcess(MQTTStartCmd(), mqttBrokerOmnissaPI));

   Sleep(15000);

   EXPECT_TRUE(isConnected());

   EXPECT_TRUE(MqttManager::GetInstance().Stop());
   CloseProcess(mqttBrokerOmnissaPI);

   // Validate that we received all 3 events before the swap
   WinFile eventLoggerLogs(mSubTextPath);
   EXPECT_TRUE(eventLoggerLogs.FileExists());
   auto eventLoggerTxt = eventLoggerLogs.ReadFileIntoString();
   auto refreshedStr = mstr(AGENT_REFRESHED_CERTIFICATES);
   auto refreshEvent = eventLoggerTxt.find(refreshedStr.c_str());
   EXPECT_NE(refreshEvent, std::string::npos);
   auto dryRunStr = mstr(AGENT_CERT_SWAP_DRY_RUN_SUCCESS);
   auto dryRunEvent = eventLoggerTxt.find(dryRunStr.c_str(), refreshEvent);
   EXPECT_NE(dryRunEvent, std::string::npos);
   auto swapStr = mstr(AGENT_SWAPPED_CERTIFICATES);
   auto swapEvent = eventLoggerTxt.find(swapStr.c_str(), dryRunEvent);
   EXPECT_NE(swapEvent, std::string::npos);

   eventLoggerLogs.FileDelete();

   // Validate that the Agent performed a dry run before disconnecting
   std::string dryrunClientID = mAgentIdentity + "_dryrun";

   std::string brokerLog;
   READ_FILE_INTO_STRING(mMosquittoOuputPath, brokerLog, false);

   size_t pos = 0;
   EXPECT_TRUE(FindClientConnected(brokerLog, dryrunClientID, pos));
   EXPECT_TRUE(FindClientDisconnected(brokerLog, mAgentIdentity, pos));
   size_t mainClientDisconnectFound = pos;

   // Count the number of event logger publishes
   std::string eventLoggerTopicStr =
      wstr::to_mstr(GetTopicName(AllTopics::EVENT_LOGGER).c_str()).c_str();
   std::string eventLoggerFindStr = "'" + eventLoggerTopicStr + "'";

   int count = 0;
   pos = 0;
   size_t lastEvent = 0;
   while ((pos = brokerLog.find(eventLoggerFindStr, pos)) != std::string::npos) {
      count++;
      pos += eventLoggerFindStr.length();
      lastEvent = pos;
   }
   // Since we expect 3 messages, the broker should have 6 instances of this str
   EXPECT_EQ(count, 6);
   // Ensure that the last event log occurs before the disconnect
   EXPECT_TRUE(lastEvent < mainClientDisconnectFound);

   WinFile mosquittoLogFile(mMosquittoOuputPath);
   mosquittoLogFile.FileDelete();

   vmstr caTps = vmstr::readRegistry(
      "HKLM\\SOFTWARE\\Omnissa\\Horizon\\Node Manager\\" MQTT_CA_THUMBPRINTS_REG);
   EXPECT_EQ(caTps.size(), 2);
   vmstr clientTps = vmstr::readRegistry(
      "HKLM\\SOFTWARE\\Omnissa\\Horizon\\Node Manager\\" MQTT_CLIENT_THUMBPRINTS_REG);
   EXPECT_EQ(clientTps.size(), 2);

   DeleteLCMSpecInRegistry();
   MqttManagerClearFileFactory();
}


/*
 *-----------------------------------------------------------------------------
 *
 * MQTTConnTests::ForceCertSwapDryRunFailed
 *
 *    Test case: Simulates changing the Certificate Authority store from
 *    one CA thumbprint store to another. The mosquitto broker sends an OTP
 *    token message through to the Agent to kick off the transition. The
 *    Agent receives invalid credentials during this process.
 *
 *    The Agent will attempt to go from Omnissa-signed to Horizon-signed.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MQTTConnTests, ForceCertSwapDryRunFailed)
{
   // Debugging often leads to the registry values being stale
   DeleteLCMSpecInRegistry();

   std::shared_ptr<MockCertImpl> mockCert__ = std::make_shared<MockCertImpl>();
   SetActiveCredentialsInRegistry();

   // Omnissa certificates
   std::string omnCaCrt, omnClientCrt, omnPkcs;
   std::map<std::string, std::string> thumbprintToPEMMap;

   READ_FILE_INTO_STRING(mOmnissaClientCACrtPath, omnCaCrt, false);
   READ_FILE_INTO_STRING(mOmnissaClientCrtPath, omnClientCrt, false);
   READ_FILE_INTO_STRING(mOmnissaClientPkcsPath, omnPkcs, true);

   auto caTps = AddThumbprintPEMEntries(thumbprintToPEMMap, omnCaCrt);
   AddThumbprintPEMEntries(thumbprintToPEMMap, omnClientCrt);

   SetRegistryNodeManager(MQTT_CA_THUMBPRINTS_REG, caTps);

   std::string omnHttpResponse = GenerateOtpRedemptionResponse(omnCaCrt, omnClientCrt);

   auto omnCaThumbprints = WinCertImplementation::GetThumbprints(omnCaCrt);

   // Horizon certificates
   std::string hznCaCrt, hznClientCrt, hznPkcs, hznKeyBin, hznCSRBin;
   READ_FILE_INTO_STRING(mHorizonClientCACrtPath, hznCaCrt, false);
   READ_FILE_INTO_STRING(mHorizonClientCrtPath, hznClientCrt, false);
   READ_FILE_INTO_STRING(mHorizonClientPkcsPath, hznPkcs, true);
   READ_FILE_INTO_STRING(mHorizonClientKeyBinPath, hznKeyBin, true);
   READ_FILE_INTO_STRING(mHorizonClientCSRBinPath, hznCSRBin, true);

   std::string hznHttpResponse = GenerateOtpRedemptionResponse(hznCaCrt, hznClientCrt);

   auto hznCaThumbprints = WinCertImplementation::GetThumbprints(hznCaCrt);

   // Mock MFW and Domain
   MOCK_MFW_REGISTRATION(1);
   MOCK_DOMAIN_INTF(1);

   SETUP_NO_FILE_EXISTENCE();

   // Mock EventLogger-related calls
   MOCK_EVENT_LOGGER(2);

   // Mock OTP redemption
   auto mockHttpReqFactory = std::make_shared<MockHttpRequestFactory>();
   auto mockHttpHorizon = std::make_shared<MockHttpRequest>();

   EXPECT_CALL(*mockHttpReqFactory, GetHttpRequestClient()).WillOnce(Return(mockHttpHorizon));

   EXPECT_CALL(*mockHttpHorizon, postSync(_, _, _, _))
      .WillOnce(DoAll(SetArgReferee<3>(hznHttpResponse), Return(true)));
   EXPECT_CALL(*mockHttpHorizon, enableCertRevocation()).Times(0);
   SetupMockHttpRequestProxy(mockHttpHorizon);

   /*
    * Mock the cert store.
    * The first pass will bootstrap as an Omnissa-signed client.
    * The second pass will attempt to bootstrap as a Horizon-signed client.
    */
   MOCK_WINDOWS_CERT_IMPORT_FIND(2);

   EXPECT_CALL(*mockCert__, GenerateKeyUsingWin32API(_, _, _))
      .WillOnce(DoAll(SetArgReferee<1>(hznKeyBin), Return(true)));
   EXPECT_CALL(*mockCert__, GenerateCSRUsingWin32API(GMOCK_BLANK_ARG7))
      .WillOnce(DoAll(SetArgReferee<2>(hznCSRBin), Return(true)));
   /*
    * Currently these are called twice.
    * 1. During the storing operation, we validate that the storing was
    *    successful by validating that we can retrieve the data.
    * 2. During the connection operation, we go retrieve the private key.
    */
   EXPECT_CALL(*mockCert__, ExportCertificateAndKeyFromStore(
                               _, StrEq(MQTT_CLIENT_CERT_FRIENDLY_NAME), GMOCK_BLANK_ARG5))
      .Times(2)
      .WillRepeatedly(
         DoAll(SetArgReferee<2>(omnPkcs), SetArgReferee<3>(mAgentIdentity), Return(true)));
   EXPECT_CALL(*mockCert__, ExportCertificateAndKeyFromStore(
                               _, StrEq(MQTT_CLIENT_PENDING_CERT_FRIENDLY_NAME), GMOCK_BLANK_ARG5))
      .Times(2)
      .WillRepeatedly(
         DoAll(SetArgReferee<2>(hznPkcs), SetArgReferee<3>(mAgentIdentity), Return(true)));

   // One for the real connection, one for the connection test
   EXPECT_CALL(*mockCert__, RetrieveSystemCertificates(_)).Times(2).WillRepeatedly(Return(true));
   /*
    * Done during the storing process to ensure there aren't multiple certs with
    * the same friendly name in the store.
    *
    * After the dry run fails, we also expect the pending cert to be deleted.
    */
   EXPECT_CALL(*mockCert__, DeleteCertificates(_, Matcher<const std::string &>(StrEq(
                                                     MQTT_CLIENT_PENDING_CERT_FRIENDLY_NAME))))
      .Times(2)
      .WillRepeatedly(Return());
   // Just return a string when the Agent wants the store.
   EXPECT_CALL(*mockCert__, GetCertificateStore(_)).WillRepeatedly([](auto &&...args) {
      CertificateStoreType store("My");
      return store;
   });

   // Calls are the same as FindCertificates
   EXPECT_CALL(*mockCert__, GetCertificateChain(_, _))
      .Times(1)
      .WillRepeatedly([&omnClientCrt](auto &&...args) {
         std::vector<std::string> pemCerts =
            CertUtilityInterface::ExtractPEMCertificates(omnClientCrt);
         std::vector<cedar::windows::unique_cert_context> clientCertChain;
         for (const auto &pemCert : pemCerts) {
            cedar::windows::unique_cert_context certCtx(
               WinCertImplementation::ConvertPEMToCertCtx(pemCert));
            clientCertChain.push_back(std::move(certCtx));
         }
         return std::move(clientCertChain);
      });

   // Start the broker
   PROCESS_INFORMATION mqttBrokerPI = {0};
   HANDLE mosquittoOutputFileHandle = NULL;
   EXPECT_TRUE(ProcUtil::LaunchProcessWithStdOut(MQTTStartCmd(), mMosquittoOuputPath, mqttBrokerPI,
                                                 mosquittoOutputFileHandle));
   Sleep(1000);

   // Start the Event Logger subscriber
   PROCESS_INFORMATION eventLoggerPI = {0};
   HANDLE eventLoggerOutputFileHandle = NULL;
   auto subscribeCommand = MQTTSubscribeToTopic(GetTopicName(AllTopics::EVENT_LOGGER));
   EXPECT_TRUE(ProcUtil::LaunchProcessWithStdOut(subscribeCommand, mSubTextPath, eventLoggerPI,
                                                 eventLoggerOutputFileHandle));

   Sleep(1000);

   // Start mqttConfig
   StartApp();

   // Hook in the mock interfaces
   MqttManager::GetInstance().SetHttpRequestFactory(mockHttpReqFactory);
   MqttManager::GetInstance().SetCertInterface(mockCert__);
   TemplateHandler::GetInstance().SetDomainInterface(s_mockDomain);

   // Start MqttManager
   EXPECT_TRUE(MqttManager::GetInstance().Start());

   Sleep(5000);

   EXPECT_TRUE(isConnected());

   // Send OTP message. Force the cert swap.
   std::wstring otpTokenMsg =
      LR"({\"otp\":\"abc123\",\"vmHubUrl\":\"https://helloworld.com/vmhub/credentials\",\"forceCertSwap\":true})";

   EXPECT_TRUE(PublishMosquittoMessage(GetTopicName(AllTopics::OTP), otpTokenMsg));

   // Wait for wsnm_mqtt to perform the dry run
   Sleep(5000);

   EXPECT_TRUE(isConnected());

   PropertyBag sampleMsg;
   sampleMsg.set(L"Hello", L"World");
   MqttManager::GetInstance().SendSessionMessage(sampleMsg);

   // Wait for Mosquitto to receive the msg
   Sleep(3000);

   // Stop wsnm_mqtt
   EXPECT_TRUE(MqttManager::GetInstance().Stop());
   // Stop the subscriber
   CloseProcess(eventLoggerPI);
   CloseHandle(eventLoggerOutputFileHandle);
   // Stop the broker
   CloseProcess(mqttBrokerPI);
   CloseHandle(mosquittoOutputFileHandle);

   // Validate what happened
   WinFile eventLoggerLogs(mSubTextPath);
   EXPECT_TRUE(eventLoggerLogs.FileExists());
   auto eventLoggerTxt = eventLoggerLogs.ReadFileIntoString();
   auto refreshedStr = mstr(AGENT_REFRESHED_CERTIFICATES);
   auto refreshEvent = eventLoggerTxt.find(refreshedStr.c_str());
   EXPECT_NE(refreshEvent, std::string::npos);
   auto dryRunFailStr = mstr(AGENT_CERT_SWAP_DRY_RUN_FAILED);
   auto dryRunFailEvent = eventLoggerTxt.find(dryRunFailStr.c_str(), refreshEvent);
   EXPECT_NE(dryRunFailEvent, std::string::npos);

   eventLoggerLogs.FileDelete();

   WinFile brokerLogs(mMosquittoOuputPath);
   EXPECT_TRUE(brokerLogs.FileExists());
   auto brokerTxt = brokerLogs.ReadFileIntoString();
   EXPECT_TRUE(!brokerTxt.empty());
   // Ensure the initial connection succeeded
   std::string connectionAttemptStr("New connection from 127.0.0.1");
   auto initialClientConnected = brokerTxt.find(connectionAttemptStr);
   EXPECT_NE(initialClientConnected, std::string::npos);
   // Ensure that the dry run was attempted after the initial connection
   auto dryrunClientConnected = brokerTxt.find(connectionAttemptStr, initialClientConnected);
   EXPECT_NE(dryrunClientConnected, std::string::npos);
   // Ensure that the dry run failed
   std::string dryRunFailedStr("OpenSSL Error");
   auto dryRunFailed = brokerTxt.find(dryRunFailedStr);
   EXPECT_NE(dryRunFailed, std::string::npos);

   // Make sure the events came through
   std::string eventLoggerTopicStr =
      wstr::to_mstr(GetTopicName(AllTopics::EVENT_LOGGER).c_str()).c_str();
   auto refreshSuccessEvent = brokerTxt.find(eventLoggerTopicStr);
   EXPECT_NE(refreshSuccessEvent, std::string::npos);
   // Ensure the dry run event came after the dry run disconnected
   auto dryRunFailedEvent = brokerTxt.find(eventLoggerTopicStr, dryRunFailed);
   EXPECT_NE(dryRunFailedEvent, std::string::npos);
   // Ensure that the publish came through after a failed dry run
   std::string clientPublishStr("Received PUBLISH from " + mAgentIdentity);
   auto clientPublish = brokerTxt.find(clientPublishStr, dryrunClientConnected);
   // Ensure that there was no disconnection until after the dry run attempt
   std::string clientDisconnectedStr("Client " + mAgentIdentity + " disconnected.");
   auto clientDisconnected = brokerTxt.find(clientDisconnectedStr);
   EXPECT_NE(clientDisconnected, std::string::npos);
   EXPECT_TRUE(clientDisconnected > dryrunClientConnected);

   brokerLogs.FileDelete();

   // Ensure that the Agent cleaned up all remnants of the bad credentials
   vwstr pendingCATps;
   pendingCATps.readRegistry(MQTT_CONFIG_LOCATION + L"\\mqtt_PendingCAThumbprints");
   EXPECT_TRUE(pendingCATps.empty());

   wstr pendingEndpointUrl =
      wstr::readRegistry(MQTT_CONFIG_LOCATION + L"\\mqtt_PendingEndpointUrl", L"");
   EXPECT_TRUE(pendingEndpointUrl.empty());

   vmstr caTpsReg = vmstr::readRegistry(
      "HKLM\\SOFTWARE\\Omnissa\\Horizon\\Node Manager\\" MQTT_CA_THUMBPRINTS_REG);
   EXPECT_EQ(caTpsReg.size(), 2);
   vmstr clientTpsReg = vmstr::readRegistry(
      "HKLM\\SOFTWARE\\Omnissa\\Horizon\\Node Manager\\" MQTT_CLIENT_THUMBPRINTS_REG);
   EXPECT_EQ(clientTpsReg.size(), 2);

   DeleteLCMSpecInRegistry();
   MqttManagerClearFileFactory();
}


/*
 *-----------------------------------------------------------------------------
 *
 * MQTTConnTests::MQTTBrokerDisconnectTest
 *
 *    Test case: Tests the disconnect functionality of mqtt plugin.
 *       Creates a connection between mqtt plugin and broker, and checks
 *       if CONNECTED flag is set, and then closes the broker and checks
 *       the DISCONNECTED flag.
 *
 *    In addition, tests the non-bootstrap flow where all expected values
 *    already exist in the registry and certificate store.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MQTTConnTests, MQTTBrokerDisconnectTest)
{
   std::shared_ptr<MockCertImpl> mockCert__ = std::make_shared<MockCertImpl>();
   SetActiveCredentialsInRegistry();

   // Set up the mock interface and dummy data needed for mocking
   std::string pkcsBin, clientCrt, caCrt;
   std::map<std::string, std::string> thumbprintToPEMMap;

   WSNM_MQTT_SETUP_NO_BOOTSTRAP();

   // Start the MQTT broker in another process
   PROCESS_INFORMATION mqttBrokerPI = {0};
   EXPECT_TRUE(LaunchProcess(MQTTStartCmdLegacy(), mqttBrokerPI));
   Sleep(1000);

   StartApp();

   // Set the mock interfaces
   MqttManager::GetInstance().SetCertInterface(mockCert__);
   TemplateHandler::GetInstance().SetDomainInterface(s_mockDomain);

   // Start the connection process
   EXPECT_TRUE(MqttManager::GetInstance().Start());
   Sleep(3000);

   EXPECT_TRUE(CloseProcess(mqttBrokerPI));

   Sleep(3000);

   EXPECT_FALSE(isConnected());

   EXPECT_TRUE(MqttManager::GetInstance().Stop());
   DeleteLCMSpecInRegistry();
}


/*
 *-----------------------------------------------------------------------------
 *
 * MQTTConnTests::CertMonitorValidator
 *
 *    Test case: Ensure that cert monitor is validating the certificates,
 *       including both the active and pending certificates.
 *
 *    Additionally ensure that the pending client certificate thumbprints are
 *       populated appropriately in the registry (if they don't exist).
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MQTTConnTests, CertMonitorValidator)
{
   std::shared_ptr<MockCertImpl> mockCert__ = std::make_shared<MockCertImpl>();
   SetActiveCredentialsInRegistry();

   // Set up the mock interface and dummy data needed for mocking
   std::string pkcsBin, clientCrt, caCrt, pendingCrt;
   std::map<std::string, std::string> thumbprintToPEMMap;

   READ_FILE_INTO_STRING(mHorizonClientCrtPath, pendingCrt, false);

   WSNM_MQTT_SETUP_NO_BOOTSTRAP();

   EXPECT_CALL(*mockCert__, FindCertificate(Matcher<const CertificateStoreType &>(_),
                                            StrEq(MQTT_CLIENT_PENDING_CERT_FRIENDLY_NAME), _))
      .WillOnce([&pendingCrt](auto &&args...) {
         auto certChain = CertUtilityInterface::ExtractPEMCertificates(pendingCrt);
         return WinCertImplementation::ConvertPEMToCertCtx(certChain[0]);
      });
   EXPECT_CALL(*mockCert__, GetCertificateChain(_, _)).WillOnce([&pendingCrt](auto &&...args) {
      std::vector<std::string> pemCerts = CertUtilityInterface::ExtractPEMCertificates(pendingCrt);
      std::vector<cedar::windows::unique_cert_context> clientCertChain;
      for (const auto &pemCert : pemCerts) {
         cedar::windows::unique_cert_context certCtx(
            WinCertImplementation::ConvertPEMToCertCtx(pemCert));
         clientCertChain.push_back(std::move(certCtx));
      }
      return std::move(clientCertChain);
   });

   // Start the MQTT broker in another process
   PROCESS_INFORMATION mqttBrokerPI = {0};
   EXPECT_TRUE(LaunchProcess(MQTTStartCmdLegacy(), mqttBrokerPI));
   Sleep(1000);

   StartApp();

   // Set the mock interfaces
   MqttManager::GetInstance().SetCertInterface(mockCert__);
   TemplateHandler::GetInstance().SetDomainInterface(s_mockDomain);

   // Start the connection process
   EXPECT_TRUE(MqttManager::GetInstance().Start());

   Sleep(3000);

   EXPECT_TRUE(isConnected());

   EXPECT_TRUE(MqttManager::GetInstance().Stop());

   EXPECT_TRUE(CloseProcess(mqttBrokerPI));

   // Verify that cert monitor wrote the correct values to the registry
   vmstr caTpsReg = vmstr::readRegistry(
      "HKLM\\SOFTWARE\\Omnissa\\Horizon\\Node Manager\\" MQTT_PENDING_CLIENT_THUMBPRINTS_REG);
   EXPECT_EQ(caTpsReg.size(), 3);

   DeleteLCMSpecInRegistry();
}


/*
 *-----------------------------------------------------------------------------
 *  MQTTConnTests::MQTTBrokerReconnectTest
 *
 *    Tests the reconnect functionality of mqtt plugin. Creates a conn between
 *    mqtt plugin and broker, then close and start the broker again.
 *    Checks for the connection status and also checks that upon reconnection
 *    the plugin resubscribes to the topics.
 *
 * Results:
 *    true for success, false otherwise
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MQTTConnTests, MQTTBrokerReconnectTest)
{
   std::shared_ptr<MockCertImpl> mockCert__ = std::make_shared<MockCertImpl>();
   SetPendingLCMSpecInRegistry();

   // Set up the mock interface and dummy data needed for mocking
   std::string mockResp, mockEncodedKey, mockEncodedCSR, dataBlob, clientCrt, caCrt;
   std::map<std::string, std::string> thumbprintToPEMMap;

   WSNM_MQTT_SETUP(2);

   // Start the MQTT broker in another process
   PROCESS_INFORMATION mqttBrokerPI = {0};
   EXPECT_TRUE(LaunchProcess(MQTTStartCmdLegacy(), mqttBrokerPI));
   Sleep(1000);

   StartApp();

   // Set the mock interfaces
   SETUP_NO_FILE_EXISTENCE();
   MqttManager::GetInstance().SetHttpRequestFactory(mockHttpReqFactory);
   MqttManager::GetInstance().SetCertInterface(mockCert__);
   TemplateHandler::GetInstance().SetDomainInterface(s_mockDomain);

   // Start the connection process
   EXPECT_TRUE(MqttManager::GetInstance().Start());
   Sleep(3000);

   EXPECT_TRUE(isConnected());

   EXPECT_TRUE(CloseProcess(mqttBrokerPI));

   Sleep(2000);

   PROCESS_INFORMATION mqttBrokerRestartPI = {0};
   EXPECT_TRUE(LaunchProcess(MQTTStartCmdLegacy(), mqttBrokerRestartPI));
   Sleep(30000);

   EXPECT_TRUE(isConnected());

   EXPECT_TRUE(MqttManager::GetInstance().Stop());
   EXPECT_TRUE(CloseProcess(mqttBrokerRestartPI));
   DeleteLCMSpecInRegistry();
   MqttManagerClearFileFactory();
}


/*
 *-----------------------------------------------------------------------------
 *
 * MQTTConnTests::MQTTLargePublishTest
 *
 *    Test case: Tests PublishMessage API by sending large data to it.
 *
 * Results:
 *    TRUE if successful. FALSE, otherwise.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MQTTConnTests, MQTTLargePublishTest)
{
   std::shared_ptr<MockCertImpl> mockCert__ = std::make_shared<MockCertImpl>();
   SetPendingLCMSpecInRegistry();

   // Set up the mock interface and dummy data needed for mocking
   std::string mockResp, mockEncodedKey, mockEncodedCSR, dataBlob, clientCrt, caCrt;
   std::map<std::string, std::string> thumbprintToPEMMap;

   WSNM_MQTT_SETUP(1);

   // Start the MQTT broker in another process
   PROCESS_INFORMATION mqttBrokerPI = {0};
   EXPECT_TRUE(LaunchProcess(MQTTStartCmdLegacy(), mqttBrokerPI));
   Sleep(1000);

   StartApp();

   // Set the mock interfaces
   PROCESS_INFORMATION mqttSubPI = {0};
   HANDLE subOutputHandle = NULL;
   const std::wstring &startCollectMsg = MQTTSubscribeToTopic(GetTopicName(AllTopics::RESPONSE));

   EXPECT_TRUE(
      ProcUtil::LaunchProcessWithStdOut(startCollectMsg, mSubTextPath, mqttSubPI, subOutputHandle));

   SETUP_NO_FILE_EXISTENCE();
   MqttManager::GetInstance().SetHttpRequestFactory(mockHttpReqFactory);
   MqttManager::GetInstance().SetCertInterface(mockCert__);
   TemplateHandler::GetInstance().SetDomainInterface(s_mockDomain);

   // Start the connection process
   EXPECT_TRUE(MqttManager::GetInstance().Start());

   Sleep(3000);

   std::string data;
   Json::Value json;
   Json::Reader reader;

   // Create the global event
   CreateTestEvent();

   READ_FILE_INTO_STRING(mMockPublish, data, false);

   EXPECT_TRUE(reader.parse(data, json));

   // Adding loop to test https://reviewboard.eng.vmware.com/r/1818474/
   for (int i = 0; i < NUM_LARGE_MSG_ITERATIONS; i++) {
      MqttManager::GetInstance().PublishResponse(json, on_publish_success);

      EXPECT_EQ(WaitForSingleObject(g_mPubEvent, 8000), WAIT_OBJECT_0);
   }

   Sleep(3000);
   WinFile subTextFile(mSubTextPath);
   EXPECT_TRUE(subTextFile.FileExists());

   std::filesystem::path p(mSubTextPath.c_str());
   std::filesystem::path q(mMockPublish.c_str());

   /*we're using a range because the individual GetIconResp.json has a newline
    * and carriage return at the end of the file, but the concatenated Sub.txt
    * file only has a single newline separating each message*/
   int expectedTotal = (std::filesystem::file_size(q) * NUM_LARGE_MSG_ITERATIONS);
   EXPECT_THAT(std::filesystem::file_size(p), AllOf(Ge(expectedTotal - NUM_LARGE_MSG_ITERATIONS),
                                                    Le(expectedTotal + NUM_LARGE_MSG_ITERATIONS)));

   // Cleanup
   CloseProcess(mqttSubPI);
   CloseHandle(subOutputHandle);
   EXPECT_TRUE(MqttManager::GetInstance().Stop());
   EXPECT_TRUE(CloseProcess(mqttBrokerPI));
   subTextFile.FileDelete();

   if (g_mPubEvent) {
      CloseHandle(g_mPubEvent);
   }
   g_mPubEvent = NULL;
   DeleteLCMSpecInRegistry();
   MqttManagerClearFileFactory();
}


/*
 *-----------------------------------------------------------------------------
 *
 * MQTTConnTests::MQTTPubSubBootStrapTest
 *
 *    Test case: Tests BOOTSTRAP topic does receive message
 *       on successful subscription of topic.
 *       Creates a connection between plugin and broker, and through broker
 *       publishes a message on selected topic
 *
 * Results:
 *    TRUE if successful. FALSE, otherwise.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MQTTConnTests, MQTTPubSubBootStrapTest)
{
   std::shared_ptr<MockCertImpl> mockCert__ = std::make_shared<MockCertImpl>();
   SetPendingLCMSpecInRegistry();

   // Set up the mock interface and dummy data needed for mocking
   std::string mockResp, mockEncodedKey, mockEncodedCSR, dataBlob, clientCrt, caCrt;
   std::map<std::string, std::string> thumbprintToPEMMap;

   WSNM_MQTT_SETUP(1);

   // Start the MQTT broker in another process
   PROCESS_INFORMATION mqttBrokerPI = {0};
   EXPECT_TRUE(LaunchProcess(MQTTStartCmdLegacy(), mqttBrokerPI));
   Sleep(1000);

   StartApp();

   // Set the mock interfaces
   SETUP_NO_FILE_EXISTENCE();
   MqttManager::GetInstance().SetHttpRequestFactory(mockHttpReqFactory);
   MqttManager::GetInstance().SetCertInterface(mockCert__);
   TemplateHandler::GetInstance().SetDomainInterface(s_mockDomain);

   // Start the connection process
   EXPECT_TRUE(MqttManager::GetInstance().Start());

   Sleep(3000);

   EXPECT_TRUE(PublishMosquittoMessage(L"desktop/agent/bootstrap", mMQTTCollectMsg));

   Sleep(3000);

   wstr RegValue = wstr::readRegistry(MQTT_CONFIG_LOCATION + L"\\mqtt_UnwantedFields", L"");
   std::string regValue = util::corestr::wstrToStdStr(RegValue);

   EXPECT_STRCASEEQ(regValue.c_str(), "one,two");
   EXPECT_TRUE(MqttManager::GetInstance().Stop());
   EXPECT_TRUE(CloseProcess(mqttBrokerPI));

   StopApp();
   DeleteLCMSpecInRegistry();
   MqttManagerClearFileFactory();
}


/*
 *-----------------------------------------------------------------------------
 *
 * MQTTConnTests::MQTTPubSubCommandTest
 *
 *    Test case: Tests COMMANDS topic does receive message
 *       on successful subscription of topic.
 *       Creates a connection between plugin and broker, and through broker
 *       publishes a message on selected topic
 *
 * Results:
 *    TRUE if successful. FALSE, otherwise.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MQTTConnTests, MQTTPubSubCommandTest)
{
   std::shared_ptr<MockCertImpl> mockCert__ = std::make_shared<MockCertImpl>();
   SetPendingLCMSpecInRegistry();

   // Set up the mock interface and dummy data needed for mocking
   std::string mockResp, mockEncodedKey, mockEncodedCSR, dataBlob, clientCrt, caCrt;
   std::map<std::string, std::string> thumbprintToPEMMap;

   WSNM_MQTT_SETUP(1);

   VMOCK(&MessageFrameWork::SendAsyncMsg).ExpectCall(GMOCK_BLANK_ARG11).Times(AtLeast(1));

   // Start the MQTT broker in another process
   PROCESS_INFORMATION mqttBrokerPI = {0};
   EXPECT_TRUE(LaunchProcess(MQTTStartCmdLegacy(), mqttBrokerPI));
   Sleep(1000);

   StartApp();

   // Set the mock interfaces
   SETUP_NO_FILE_EXISTENCE();
   MqttManager::GetInstance().SetHttpRequestFactory(mockHttpReqFactory);
   MqttManager::GetInstance().SetCertInterface(mockCert__);
   TemplateHandler::GetInstance().SetDomainInterface(s_mockDomain);

   // Start the connection process
   EXPECT_TRUE(MqttManager::GetInstance().Start());
   Sleep(3000);

   EXPECT_TRUE(PublishMosquittoMessage(GetTopicName(AllTopics::COMMANDS), mCommandMsg));

   // Sleep to allow main app to write to MQTT broker
   Sleep(3000);

   EXPECT_TRUE(MqttManager::GetInstance().Stop());
   EXPECT_TRUE(CloseProcess(mqttBrokerPI));

   DeleteLCMSpecInRegistry();
   MqttManagerClearFileFactory();
}


/*
 *-----------------------------------------------------------------------------
 *
 * MQTTConnTests::MQTTUEMInitTest
 *
 *    Test case: Tests status topic does receive INIT
 *       on successful subscription of topic.
 *       Creates a connection between plugin and broker
 *
 * Results:
 *    TRUE if successful. FALSE, otherwise.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MQTTConnTests, MQTTUEMInitTest)
{
   std::shared_ptr<MockCertImpl> mockCert__ = std::make_shared<MockCertImpl>();
   SetPendingLCMSpecInRegistry();

   // Set up the mock interface and dummy data needed for mocking
   std::string mockResp, mockEncodedKey, mockEncodedCSR, dataBlob, clientCrt, caCrt;
   std::map<std::string, std::string> thumbprintToPEMMap;

   WSNM_MQTT_SETUP_UEM();

   bool foundInit = false;
   VMOCK_V(statusInterceptor, (FPPublishDesktopStatus)&MqttManager::PublishDesktopStatus)
      .WillRepeatedly([&statusInterceptor, &foundInit](const wstr &status, ArgType argType,
                                                       const wstr &arg, bool rel) {
         if (status == MQTT_STATUS_INITIALIZING) {
            foundInit = true;
         }
         statusInterceptor.CallRealFunc(status, argType, arg, rel);
      });

   // Start the MQTT broker in another process
   PROCESS_INFORMATION mqttBrokerPI = {0};
   EXPECT_TRUE(LaunchProcess(MQTTStartCmdLegacy(), mqttBrokerPI));
   Sleep(1000);

   StartApp();

   // Set the mock interfaces
   MqttManager::GetInstance().SetHttpRequestFactory(mockHttpReqFactory);
   MqttManager::GetInstance().SetCertInterface(mockCert__);
   TemplateHandler::GetInstance().SetDomainInterface(s_mockDomain);

   // Start the connection process
   EXPECT_TRUE(MqttManager::GetInstance().Start());
   Sleep(3000);

   EXPECT_TRUE(MqttManager::GetInstance().Stop());
   EXPECT_TRUE(foundInit);
   EXPECT_TRUE(CloseProcess(mqttBrokerPI));
   DeleteLCMSpecInRegistry();
}


/*
 *-----------------------------------------------------------------------------
 *
 * MQTTConnTests::HybridJoinAvailableOnBoot
 *
 *    Test case: Assume hybrid join was done and machine rebooted.
 *       Now agent should send available status.
 *       Creates a connection between plugin and broker
 *
 * Results:
 *    TRUE if successful. FALSE, otherwise.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MQTTConnTests, HybridJoinAvailableOnBoot)
{
   std::shared_ptr<MockCertImpl> mockCert__ = std::make_shared<MockCertImpl>();
   SetPendingLCMSpecInRegistry();

   // Set up the mock interface and dummy data needed for mocking
   std::string mockResp, mockEncodedKey, mockEncodedCSR, dataBlob, clientCrt, caCrt;
   std::map<std::string, std::string> thumbprintToPEMMap;

   WSNM_MQTT_SETUP_HYBRID_JOIN();
   EXPECT_CALL(*s_mockDomain, IsComputerInDomain(_))
      .WillOnce(DoAll(SetArgReferee<0>(true), Return(true)));

   // Set up mock needed for Azure AD join
   VMOCK(&AzureADMonitor::IsAzureADSupported).ExpectCall().Will(true);

   wstr fakeDeviceId(L"test-device-id");

   VMOCK(&AzureADMonitor::IsDeviceAzureADJoined)
      .WillOnce([fakeDeviceId](wstr &deviceId, wstr &errMsg) {
         deviceId = fakeDeviceId;
         return true;
      });

   bool foundAvailableStatus = false;
   VMOCK_V(statusInterceptor, (FPPublishDesktopStatus)&MqttManager::PublishDesktopStatus)
      .WillRepeatedly([&statusInterceptor, &foundAvailableStatus](
                         const wstr &status, ArgType argType, const wstr &arg, bool rel) {
         if (status == MQTT_STATUS_AVAILABLE) {
            foundAvailableStatus = true;
         }
         statusInterceptor.CallRealFunc(status, argType, arg, rel);
      });

   // Start the MQTT broker in another process
   PROCESS_INFORMATION mqttBrokerPI = {0};
   EXPECT_TRUE(LaunchProcess(MQTTStartCmdLegacy(), mqttBrokerPI));
   Sleep(1000);

   StartApp();

   // Set the mock interfaces
   MqttManager::GetInstance().SetHttpRequestFactory(mockHttpReqFactory);
   MqttManager::GetInstance().SetCertInterface(mockCert__);
   TemplateHandler::GetInstance().SetDomainInterface(s_mockDomain);

   // Start the connection process
   EXPECT_TRUE(MqttManager::GetInstance().Start());

   // Sleep to allow main app to write to MQTT broker
   Sleep(3000);
   EXPECT_TRUE(foundAvailableStatus);
   EXPECT_TRUE(MqttManager::GetInstance().Stop());
   EXPECT_TRUE(CloseProcess(mqttBrokerPI));
   DeleteLCMSpecInRegistry();
}

/*
 *-----------------------------------------------------------------------------
 *
 * MQTTConnTests::WaitForHybridJoinOnBoot
 *
 *    Test case: Assume hybrid join payload was received. Machine was
 *    rebooted but hybrid join wasn't complete. Agent should send
 *    WaitForHybridJoin status.
 *
 * Results:
 *    TRUE if successful. FALSE, otherwise.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MQTTConnTests, WaitForHybridJoinOnBoot)
{
   std::shared_ptr<MockCertImpl> mockCert__ = std::make_shared<MockCertImpl>();
   SetPendingLCMSpecInRegistry();

   // Set up the mock interface and dummy data needed for mocking
   std::string mockResp, mockEncodedKey, mockEncodedCSR, dataBlob, clientCrt, caCrt;
   std::map<std::string, std::string> thumbprintToPEMMap;

   WSNM_MQTT_SETUP_HYBRID_JOIN();

   EXPECT_CALL(*s_mockDomain, IsComputerInDomain(_))
      .WillOnce(DoAll(SetArgReferee<0>(true), Return(true)));

   // Set up mock needed for Azure AD join
   VMOCK(&AzureADMonitor::IsAzureADSupported).ExpectCall().Will(true);

   wstr fakeDeviceId(L"test-device-id");

   // Pretend device isn't joined to azure AD
   VMOCK(&AzureADMonitor::IsDeviceAzureADJoined)
      .WillOnce([fakeDeviceId](wstr &deviceId, wstr &errMsg) {
         deviceId = fakeDeviceId;
         return false;
      });

   bool foundWaitForHybridJoinStatus = false;
   VMOCK_V(statusInterceptor, (FPPublishDesktopStatus)&MqttManager::PublishDesktopStatus)
      .WillRepeatedly([&statusInterceptor, &foundWaitForHybridJoinStatus](
                         const wstr &status, ArgType argType, const wstr &arg, bool rel) {
         if (status == MQTT_STATUS_WAIT_FOR_HYBRID_JOIN) {
            foundWaitForHybridJoinStatus = true;
         }
         statusInterceptor.CallRealFunc(status, argType, arg, rel);
      });

   // Start the MQTT broker in another process
   PROCESS_INFORMATION mqttBrokerPI = {0};
   EXPECT_TRUE(LaunchProcess(MQTTStartCmdLegacy(), mqttBrokerPI));
   Sleep(1000);

   StartApp();

   // Set the mock interfaces
   MqttManager::GetInstance().SetHttpRequestFactory(mockHttpReqFactory);
   MqttManager::GetInstance().SetCertInterface(mockCert__);
   TemplateHandler::GetInstance().SetDomainInterface(s_mockDomain);

   // Start the connection process
   EXPECT_TRUE(MqttManager::GetInstance().Start());

   // Sleep to allow main app to write to MQTT broker
   Sleep(3000);
   EXPECT_TRUE(foundWaitForHybridJoinStatus);
   EXPECT_TRUE(MqttManager::GetInstance().Stop());
   EXPECT_TRUE(CloseProcess(mqttBrokerPI));
   DeleteLCMSpecInRegistry();
}


/*
 *-----------------------------------------------------------------------------
 *
 * MQTTConnTests::MQTTPubSubDuplicateCommandsTest
 *
 *    Test case: Tests COMMANDS topic filters out duplicate messages sent
 *    within a fixed time frame. Creates a connection between plugin and
 *    broker, and through broker publishes duplicate messages on topic.
 *
 * Results:
 *    TRUE if successful. FALSE, otherwise.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MQTTConnTests, MQTTPubSubDuplicateCommandsTest)
{
   std::shared_ptr<MockCertImpl> mockCert__ = std::make_shared<MockCertImpl>();
   SetPendingLCMSpecInRegistry();

   // Set up the mock interface and dummy data needed for mocking
   std::string mockResp, mockEncodedKey, mockEncodedCSR, dataBlob, clientCrt, caCrt;
   std::map<std::string, std::string> thumbprintToPEMMap;

   WSNM_MQTT_SETUP(1);

   /* Expect 2 commands to be sent, and 2 to be filtered out.
    * Still possible to send more than 2 messages if duplicates are
    * removed due to slow messages, thus only check for at least 2.
    */
   VMOCK(&MessageFrameWork::SendAsyncMsg).ExpectCall(GMOCK_BLANK_ARG11).Times(AtLeast(2));

   // Start the MQTT broker in another process
   PROCESS_INFORMATION mqttBrokerPI = {0};
   EXPECT_TRUE(LaunchProcess(MQTTStartCmdLegacy(), mqttBrokerPI));
   Sleep(1000);

   StartApp();

   // Set the mock interfaces
   SETUP_NO_FILE_EXISTENCE();
   MqttManager::GetInstance().SetHttpRequestFactory(mockHttpReqFactory);
   MqttManager::GetInstance().SetCertInterface(mockCert__);
   TemplateHandler::GetInstance().SetDomainInterface(s_mockDomain);

   // Start the connection process
   EXPECT_TRUE(MqttManager::GetInstance().Start());
   Sleep(3000);

   // Publish two unique messages, each of which are duplicated once
   EXPECT_TRUE(PublishMosquittoMessage(GetTopicName(AllTopics::COMMANDS), mCommandMsg));
   EXPECT_TRUE(PublishMosquittoMessage(GetTopicName(AllTopics::COMMANDS), mCommandMsg));

   EXPECT_TRUE(PublishMosquittoMessage(GetTopicName(AllTopics::COMMANDS), mCommandMsg2));
   EXPECT_TRUE(PublishMosquittoMessage(GetTopicName(AllTopics::COMMANDS), mCommandMsg2));

   // Sleep to allow main app to write to MQTT broker
   Sleep(3000);

   EXPECT_TRUE(MqttManager::GetInstance().Stop());
   EXPECT_TRUE(CloseProcess(mqttBrokerPI));

   StopApp();
   DeleteLCMSpecInRegistry();
   MqttManagerClearFileFactory();
}


/*
 *-----------------------------------------------------------------------------
 *
 * MQTTConnTests::MQTTRestartMonitorTest
 *
 *    Test case: Tests the RestartMonitor logic by checking the INIT message in
 *    the subscribed topic to which wsnm_mqtt publishes
 *
 * Results:
 *    TRUE if successful. FALSE, otherwise.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MQTTConnTests, MQTTRestartMonitorTest)
{
   std::shared_ptr<MockCertImpl> mockCert__ = std::make_shared<MockCertImpl>();
   SetPendingLCMSpecInRegistry();

   // Set up the mock interface and dummy data needed for mocking
   std::string mockResp, mockEncodedKey, mockEncodedCSR, dataBlob, clientCrt, caCrt;
   std::map<std::string, std::string> thumbprintToPEMMap;

   WSNM_MQTT_SETUP(1);

   // Start the MQTT broker in another process
   PROCESS_INFORMATION mqttBrokerPI = {0};
   EXPECT_TRUE(LaunchProcess(MQTTStartCmdLegacy(), mqttBrokerPI));
   Sleep(1000);

   StartApp();

   PROCESS_INFORMATION mqttSubPI = {0};
   const std::wstring &startCollectMsg = MQTTSubscribeToTopic(GetTopicName(AllTopics::STATUS));
   HANDLE subOutputHandle = NULL;

   EXPECT_TRUE(
      ProcUtil::LaunchProcessWithStdOut(startCollectMsg, mSubTextPath, mqttSubPI, subOutputHandle));

   SETUP_NO_FILE_EXISTENCE();
   MqttManager::GetInstance().SetHttpRequestFactory(mockHttpReqFactory);
   MqttManager::GetInstance().SetCertInterface(mockCert__);
   TemplateHandler::GetInstance().SetDomainInterface(s_mockDomain);
   // Start the connection process
   EXPECT_TRUE(MqttManager::GetInstance().Start());

   Sleep(3000);

   // Set the minimum interval value
   RestartMonitor::GetInstance().SetPollInterval(5);

   RestartMonitor::GetInstance().Start();

   // 5 sec pollInterval + 2 sec to write on file
   Sleep(7000);

   // close the process handle as we want to do operation on file
   CloseProcess(mqttSubPI);
   CloseHandle(subOutputHandle);

   RestartMonitor::GetInstance().Stop();

   EXPECT_TRUE(MqttManager::GetInstance().Stop());
   EXPECT_TRUE(CloseProcess(mqttBrokerPI));

   WinFile subTextFile(mSubTextPath);
   EXPECT_TRUE(subTextFile.FileExists());
   std::string subMsgs = subTextFile.ReadFileIntoString();
   EXPECT_TRUE(!subMsgs.empty());
   EXPECT_TRUE(subMsgs.find("INIT") != std::string::npos);
   subTextFile.FileDelete();

   StopApp();
   DeleteLCMSpecInRegistry();
   MqttManagerClearFileFactory();
}


/*
 *-----------------------------------------------------------------------------
 *
 * MQTTConnTests::AWSBootstrap
 *
 *    Test case: Tests bootstrapping logic for an AWS environment.
 *       This test case handles the happy path.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MQTTConnTests, AWSBootstrap)
{
   std::shared_ptr<MockCertImpl> mockCert__ = std::make_shared<MockCertImpl>();

   std::string mockEncodedKey, mockEncodedCSR, dataBlob, pkcsBin;
   std::map<std::string, std::string> thumbprintToPEMMap;

   DeleteLCMSpecInRegistry();
   StartApp();

   std::shared_ptr<MockFile> azureFile, awsFile;
   std::shared_ptr<MockFileFactory> fileFactory;
   MqttManagerHookUpFile(azureFile, awsFile, fileFactory);

   EXPECT_CALL(*azureFile, FileExists()).WillRepeatedly(Return(false));

   bool awsOtpFileExists = true;
   std::string awsOtpEncoded = mstr(mAwsOtpFileContents).base64Encode().c_str();
   SetupValidFile(awsOtpFileExists, awsOtpEncoded, awsFile);

   // There are 3 http calls that we need to mock in the AWS case
   std::shared_ptr<MockHttpRequest> mockGetRefreshToken, mockFetchLcm, mockRedeemOtp, fourthReq;
   std::shared_ptr<MockHttpRequestFactory> mockReqFactory;
   MqttManagerHookUpHttpRequests(3, mockGetRefreshToken, mockFetchLcm, mockRedeemOtp, fourthReq,
                                 mockReqFactory);

   // Set up the refresh token request
   std::string expectedRefreshUrl("xyz.com");
   std::string expectedRefreshPayload = R"({"refreshToken":"xabcdef"})";
   SetupHttpPostRequestMock(mockGetRefreshToken, expectedRefreshUrl, expectedRefreshPayload,
                            mRefreshTokenContents, {"", ""});

   // Set up the lcm spec fetch request
   std::string expectedLcmUrl("abc.com");
   std::string lcmSpecEncoded = mstr(mLcmSpecFileContents).base64Encode().c_str();

   wstr ipAddr(L"*******");
   VMOCK(&util::WinInet::GetIpAddressFiltered).ExpectCall(_, _).WillOnce(Return(ipAddr));
   auto expectedLcmPayload = GetExpectedLcmFetchPayload(ipAddr, "1.0");

   SetupHttpPostRequestMock(mockFetchLcm, expectedLcmUrl, expectedLcmPayload, lcmSpecEncoded,
                            {"Authorization", "Bearer myrefreshtoken"});

   // Set up the redeem otp request
   SetupRedeemOTPMockLegacy(mockRedeemOtp);

   // Mock that we're not in the master image
   // getStoredMACAddress will be called an extra time to "erase" its contents
   SETUP_CLONE_FLOW(2, 1);

   // Mock the rest
   MOCK_MFW_REGISTRATION(1);
   MOCK_DOMAIN_INTF(1);
   MOCK_WINDOWS_CERT_API_LEGACY();

   MqttManager::GetInstance().SetCertInterface(mockCert__);
   TemplateHandler::GetInstance().SetDomainInterface(s_mockDomain);

   // Start the broker
   PROCESS_INFORMATION mqttBrokerPI = {0};
   EXPECT_TRUE(LaunchProcess(MQTTStartCmdLegacy(), mqttBrokerPI));
   Sleep(1000);

   const std::wstring &startCollectMsg = MQTTSubscribeToTopic(GetTopicName(AllTopics::STATUS));

   PROCESS_INFORMATION mqttSubPI = {0};
   HANDLE statusOutputFileHandle = NULL;
   EXPECT_TRUE(ProcUtil::LaunchProcessWithStdOut(startCollectMsg, mSubTextPath, mqttSubPI,
                                                 statusOutputFileHandle));
   Sleep(1000);

   // Start MqttManager. Give it time to bootstrap
   MqttManager::GetInstance().Start();

   Sleep(1000 * 7);

   // Close the subscriber first so that we don't get the UNAVAILABLE message
   CloseProcess(mqttSubPI);
   CloseHandle(statusOutputFileHandle);
   Sleep(1000);

   // Shut everything down
   MqttManager::GetInstance().Stop();
   MqttManager::GetInstance().SetFileFactory(nullptr);
   StopApp();

   CloseProcess(mqttBrokerPI);

   // Check the registry
   auto token = wstr::readRegistry(MQTT_CONFIG_LOCATION + L"\\mqtt_aws_refresh_token", L"");
   EXPECT_TRUE(token.empty());
   auto awsUrl = wstr::readRegistry(MQTT_CONFIG_LOCATION + L"\\mqtt_aws_lcm_url", L"");
   EXPECT_TRUE(awsUrl.empty());
   auto rt = wstr::readRegistry(MQTT_CONFIG_LOCATION + L"\\mqtt_aws_access_token", L"");
   EXPECT_TRUE(rt.empty());
   auto rturl = wstr::readRegistry(MQTT_CONFIG_LOCATION + L"\\mqtt_aws_refresh_url", L"");
   EXPECT_TRUE(rturl.empty());
   auto pairVer = wstr::readRegistry(MQTT_CONFIG_LOCATION + L"\\mqtt_aws_pair_version", L"");
   EXPECT_TRUE(pairVer.empty());

   // Validate the test
   WinFile myfile(mSubTextPath);
   std::string text = myfile.ReadFileIntoString();
   EXPECT_TRUE(!text.empty());
   myfile.FileDelete();

   EXPECT_TRUE(text.find("AVAILABLE") != std::string::npos);

   DeleteLCMSpecInRegistry();
   MqttManagerClearFileFactory();
}


/*
 *-----------------------------------------------------------------------------
 *
 * MQTTConnTests::AWSBootstrapWaitFail
 *
 *    Test case: Have MqttManager stop before the file wait succeeds.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MQTTConnTests, AWSBootstrapWaitFail)
{
   DeleteLCMSpecInRegistry();
   StartApp();

   std::shared_ptr<MockFile> azureFile, awsFile;
   std::shared_ptr<MockFileFactory> fileFactory;
   MqttManagerHookUpFile(azureFile, awsFile, fileFactory);

   EXPECT_CALL(*azureFile, FileExists()).WillRepeatedly(Return(false));
   EXPECT_CALL(*awsFile, FileExists()).WillRepeatedly(Return(false));

   std::shared_ptr<MockDirectory> mockDir;
   std::shared_ptr<MockDirectoryFactory> dirFactory;
   MqttManagerHookUpDirectory(mockDir, dirFactory);

   EXPECT_CALL(*mockDir, WaitForFilesToExist(_, _))
      .WillOnce([](Unused, const IDirectory::EventContext &ctx) {
         WaitForSingleObject(ctx.shutdownEvent, INFINITE);
         return nullptr;
      });

   MqttManager::GetInstance().Start();

   Sleep(3000);

   EXPECT_FALSE(MqttManager::GetInstance().IsConnected());

   MqttManager::GetInstance().Stop();

   DeleteLCMSpecInRegistry();
   StopApp();
   MqttManagerClearFileFactory();
   MqttManagerClearDirectoryFactory();
}


/*
 *-----------------------------------------------------------------------------
 *
 * MQTTConnTests::AWSBootstrapFilePrivilegeFail
 *
 *    Test case: The AWS otp file does not have the required permissions.
 *    Verify that the file is not deleted.
 *    Verify the WaitForFilesToExist flow.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MQTTConnTests, AWSBootstrapFilePrivilegeFail)
{
   DeleteLCMSpecInRegistry();
   StartApp();

   bool awsOtpFileCreated = false;

   std::shared_ptr<MockFile> azureFile, awsFile;
   std::shared_ptr<MockFileFactory> fileFactory;
   MqttManagerHookUpFile(azureFile, awsFile, fileFactory);

   EXPECT_CALL(*azureFile, FileExists()).WillRepeatedly(Return(false));

   EXPECT_CALL(*awsFile, FileExists()).WillRepeatedly([&awsOtpFileCreated]() {
      return awsOtpFileCreated;
   });
   EXPECT_CALL(*awsFile, OwnedByHighestPrivilegedAccount()).WillOnce(Return(false));
   EXPECT_CALL(*awsFile, FileDelete()).Times(0);

   std::shared_ptr<MockDirectory> mockDir;
   std::shared_ptr<MockDirectoryFactory> dirFactory;
   MqttManagerHookUpDirectory(mockDir, dirFactory);

   EXPECT_CALL(*mockDir, WaitForFilesToExist(_, _)).WillOnce([&](Unused, Unused) {
      awsOtpFileCreated = true;
      return awsFile.get();
   });

   MqttManager::GetInstance().Start();

   Sleep(3000);

   EXPECT_FALSE(MqttManager::GetInstance().IsConnected());

   MqttManager::GetInstance().Stop();
   MqttManager::GetInstance().SetFileFactory(nullptr);

   DeleteLCMSpecInRegistry();
   StopApp();
   MqttManagerClearFileFactory();
   MqttManagerClearDirectoryFactory();
}


/*
 *-----------------------------------------------------------------------------
 *
 * MQTTConnTests::AWSBootstrapMacAddressEmpty
 *
 *    Test case: Test that the MQTT manager does a check on the MAC address.
 *    It should try to store the MAC address once and do two lookups.
 *    The second lookup should be a no-op, as the MAC address hasn't changed.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MQTTConnTests, AWSBootstrapMacAddressEmpty)
{
   DeleteLCMSpecInRegistry();
   StartApp();

   bool awsOtpFileExists = true;

   std::shared_ptr<MockFile> azureFile, awsFile;
   std::shared_ptr<MockFileFactory> fileFactory;
   MqttManagerHookUpFile(azureFile, awsFile, fileFactory);

   EXPECT_CALL(*azureFile, FileExists()).WillRepeatedly(Return(false));

   std::string awsOtpEncoded = mstr(mAwsOtpFileContents).base64Encode().c_str();

   SetupValidFile(awsOtpFileExists, awsOtpEncoded, awsFile);

   VMOCK_V(GetStoredMacAddrMock, &mqttConfig::getStoredMACAddress)
      .ExpectCall()
      .Times(2)
      .WillRepeatedly([&GetStoredMacAddrMock]() { return GetStoredMacAddrMock.CallRealFunc(); });

   VMOCK_V(SetStoredMacAddrMock, &mqttConfig::setStoredMACAddress)
      .ExpectCall(_)
      .WillOnce([&SetStoredMacAddrMock](const std::string &macaddr) {
         SetStoredMacAddrMock.CallRealFunc(macaddr);
      });

   // Reduce the time it takes to get another cycle started
   SETUP_RETRY_INTERVALS();

   MqttManager::GetInstance().Start();

   Sleep(2000);

   EXPECT_FALSE(MqttManager::GetInstance().IsConnected());

   MqttManager::GetInstance().Stop();
   MqttManager::GetInstance().SetFileFactory(nullptr);

   DeleteLCMSpecInRegistry();
   StopApp();
   MqttManagerClearFileFactory();
}


/*
 *-----------------------------------------------------------------------------
 *
 * MQTTConnTests::AWSBootstrapMacAddressInNICList
 *
 *    Test case: Simulate the mac address changing but remaining on the system.
 *    The Agent should do a no-op.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MQTTConnTests, AWSBootstrapMacAddressInNICList)
{
   DeleteLCMSpecInRegistry();
   StartApp();

   bool awsOtpFileExists = true;

   std::shared_ptr<MockFile> azureFile, awsFile;
   std::shared_ptr<MockFileFactory> fileFactory;
   MqttManagerHookUpFile(azureFile, awsFile, fileFactory);

   EXPECT_CALL(*azureFile, FileExists()).WillRepeatedly(Return(false));

   std::string awsOtpEncoded = mstr(mAwsOtpFileContents).base64Encode().c_str();

   SetupValidFile(awsOtpFileExists, awsOtpEncoded, awsFile);

   // Simulate the MAC address changing.
   VMOCK(&mqttConfig::getStoredMACAddress)
      .ExpectCall()
      .WillOnce(Return(""))
      .WillOnce(Return("00:00:00:00:00:00"));

   VMOCK(&mqttConfig::setStoredMACAddress).ExpectCall(_).Times(1);

   // But the MAC address still exists on the system
   VMOCK(&util::WinInet::GetAllNICMacAddresses)
      .ExpectCall(_)
      .WillOnce([](std::vector<CORE::wstr> &allNICs) {
         allNICs.push_back(L"00:00:00:00:00:00");
         allNICs.push_back(L"00:00:00:00:00:01");
         return true;
      });

   // Reduce the time it takes to get another cycle started
   SETUP_RETRY_INTERVALS();

   MqttManager::GetInstance().Start();

   Sleep(2000);

   EXPECT_FALSE(MqttManager::GetInstance().IsConnected());

   MqttManager::GetInstance().Stop();
   MqttManager::GetInstance().SetFileFactory(nullptr);

   DeleteLCMSpecInRegistry();
   StopApp();
   MqttManagerClearFileFactory();
}


/*
 *-----------------------------------------------------------------------------
 *
 * MQTTConnTests::AWSBootstrapGetRefreshTokenFail
 *
 *    Test case: Fail the AWS bootstrap process at the refresh token operation
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MQTTConnTests, AWSBootstrapGetRefreshTokenFail)
{
   DeleteLCMSpecInRegistry();
   StartApp();

   SETUP_VALID_AWS_FILE();

   // Simulate that the master image flow is complete
   SETUP_CLONE_FLOW(1, 1);

   std::shared_ptr<MockHttpRequest> mockGetRefreshToken, secondReq, thirdReq, fourthReq;
   std::shared_ptr<MockHttpRequestFactory> mockReqFactory;
   MqttManagerHookUpHttpRequests(1, mockGetRefreshToken, secondReq, thirdReq, fourthReq,
                                 mockReqFactory);

   std::string expectedRefreshUrl("xyz.com");
   std::string expectedRefreshPayload = R"({"refreshToken":"xabcdef"})";
   std::string refreshResponse = "badresponse";
   SetupHttpPostRequestMock(mockGetRefreshToken, expectedRefreshUrl, expectedRefreshPayload,
                            refreshResponse, {"", ""});

   MqttManager::GetInstance().Start();

   Sleep(3000);

   EXPECT_FALSE(MqttManager::GetInstance().IsConnected());

   MqttManager::GetInstance().Stop();
   MqttManager::GetInstance().SetFileFactory(nullptr);

   auto token = wstr::readRegistry(MQTT_CONFIG_LOCATION + L"\\mqtt_aws_refresh_token", L"");
   EXPECT_TRUE(token == wstr(L"xabcdef"));
   auto awsUrl = wstr::readRegistry(MQTT_CONFIG_LOCATION + L"\\mqtt_aws_lcm_url", L"");
   EXPECT_TRUE(awsUrl == wstr(L"abc.com"));
   auto rt = wstr::readRegistry(MQTT_CONFIG_LOCATION + L"\\mqtt_aws_access_token", L"");
   EXPECT_TRUE(rt.empty());
   auto rturl = wstr::readRegistry(MQTT_CONFIG_LOCATION + L"\\mqtt_aws_refresh_url", L"");
   EXPECT_TRUE(rturl == wstr(L"xyz.com"));
   auto pairVer = wstr::readRegistry(MQTT_CONFIG_LOCATION + L"\\mqtt_aws_pair_version", L"");
   EXPECT_TRUE(pairVer == wstr(L"1.0"));

   StopApp();
   MqttManagerClearFileFactory();
}


/*
 *-----------------------------------------------------------------------------
 *
 * MQTTConnTests::AWSBootstrapFetchLcmFailExpiredToken
 *
 *    Test case: Fail the AWS bootstrap process at the refresh token operation
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MQTTConnTests, AWSBootstrapFetchLcmFailExpiredToken)
{
   DeleteLCMSpecInRegistry();

   // Mock that the AWS file has already been loaded into the registry
   SetAWSInRegistry();
   StartApp();

   // Simulate that the master image flow is complete
   SETUP_CLONE_FLOW(3, 2);

   SETUP_NO_FILE_EXISTENCE();

   std::shared_ptr<MockHttpRequest> mockFetchLcmExpired, mockGetNewRefreshToken,
      mockFetchLcmSuccess, mockRedeemOTP;
   std::shared_ptr<MockHttpRequestFactory> mockReqFactory;
   MqttManagerHookUpHttpRequests(4, mockFetchLcmExpired, mockGetNewRefreshToken,
                                 mockFetchLcmSuccess, mockRedeemOTP, mockReqFactory);

   // Set up the failed lcm spec fetch request
   wstr ipAddr(L"*******");
   VMOCK(&util::WinInet::GetIpAddressFiltered)
      .ExpectCall(_, _)
      .Times(2)
      .WillRepeatedly(Return(ipAddr));
   std::string expectedLcmUrl("abc.com");
   auto expectedLcmPayload = GetExpectedLcmFetchPayload(ipAddr, "1.0");

   SetupHttpPostRequestMock(mockFetchLcmExpired, expectedLcmUrl, expectedLcmPayload, "",
                            {"Authorization", "Bearer myexpiredrefreshtoken"}, 401);

   // Agent should then try to refresh its token
   std::string expectedRefreshUrl("xyz.com");
   std::string expectedRefreshPayload = R"({"refreshToken":"xabcdef"})";
   SetupHttpPostRequestMock(mockGetNewRefreshToken, expectedRefreshUrl, expectedRefreshPayload,
                            mRefreshTokenContents, {"", ""});

   // The second LCM spec fetch should succeed
   std::string lcmSpecEncodedSuccess = mstr(mLcmSpecFileContents).base64Encode().c_str();
   SetupHttpPostRequestMock(mockFetchLcmSuccess, expectedLcmUrl, expectedLcmPayload,
                            lcmSpecEncodedSuccess, {"Authorization", "Bearer myrefreshtoken"});

   // Now the last step is to mock the OTP redemption
   SetupRedeemOTPMockLegacy(mockRedeemOTP);

   // Set up the Mosquitto broker
   // TODO: See if this can be macro'd
   std::string mockEncodedKey, mockEncodedCSR, dataBlob;
   std::shared_ptr<MockCertImpl> mockCert__ = std::make_shared<MockCertImpl>();
   std::map<std::string, std::string> thumbprintToPEMMap;

   MOCK_MFW_REGISTRATION(1);
   MOCK_DOMAIN_INTF(1);
   MOCK_WINDOWS_CERT_API_LEGACY();

   MqttManager::GetInstance().SetCertInterface(mockCert__);
   TemplateHandler::GetInstance().SetDomainInterface(s_mockDomain);

   // Start the broker
   PROCESS_INFORMATION mqttBrokerPI = {0};
   EXPECT_TRUE(LaunchProcess(MQTTStartCmdLegacy(), mqttBrokerPI));
   Sleep(1000);

   const std::wstring &startCollectMsg = MQTTSubscribeToTopic(GetTopicName(AllTopics::STATUS));

   PROCESS_INFORMATION mqttSubPI = {0};
   HANDLE statusOutputFileHandle = NULL;
   EXPECT_TRUE(ProcUtil::LaunchProcessWithStdOut(startCollectMsg, mSubTextPath, mqttSubPI,
                                                 statusOutputFileHandle));
   Sleep(1000);

   // Reduce the time it takes to get another cycle started
   SETUP_RETRY_INTERVALS();

   // Start MqttManager. Give it time to bootstrap
   MqttManager::GetInstance().Start();

   Sleep(1000 * 7);

   // Close the subscriber first so that we don't get the UNAVAILABLE message
   CloseProcess(mqttSubPI);
   CloseHandle(statusOutputFileHandle);
   Sleep(1000);

   // Shut everything down
   MqttManager::GetInstance().Stop();
   MqttManager::GetInstance().SetFileFactory(nullptr);
   StopApp();
   CloseProcess(mqttBrokerPI);

   // Verify that we connected
   WinFile myfile(mSubTextPath);
   std::string text = myfile.ReadFileIntoString();
   EXPECT_TRUE(!text.empty());
   myfile.FileDelete();

   EXPECT_TRUE(text.find("AVAILABLE") != std::string::npos);

   DeleteLCMSpecInRegistry();
   MqttManagerClearFileFactory();
}


/*
 *-----------------------------------------------------------------------------
 *
 * MQTTConnTests::AWSBootstrapFetchLcmSpecFail500
 *
 *    Test case: Fail the AWS bootstrap process at the LCM spec fetch operation.
 *    Fail with a 500 error. Ensure we get another request with the same
 *    refresh token.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(MQTTConnTests, AWSBootstrapFetchLcmSpecFail500)
{
   DeleteLCMSpecInRegistry();
   StartApp();

   SETUP_VALID_AWS_FILE();

   // Simulate that the master image flow is complete
   SETUP_CLONE_FLOW(2, 2);

   std::shared_ptr<MockHttpRequest> mockGetRefreshToken, mockFetchLcmFailOnce,
      mockFetchLcmFailTwice, fourthReq;
   std::shared_ptr<MockHttpRequestFactory> mockReqFactory;
   MqttManagerHookUpHttpRequests(3, mockGetRefreshToken, mockFetchLcmFailOnce,
                                 mockFetchLcmFailTwice, fourthReq, mockReqFactory);

   std::string expectedRefreshUrl("xyz.com");
   std::string expectedRefreshPayload = R"({"refreshToken":"xabcdef"})";
   SetupHttpPostRequestMock(mockGetRefreshToken, expectedRefreshUrl, expectedRefreshPayload,
                            mRefreshTokenContents, {"", ""});

   // Simulate a failed LCM spec fetch (500 error)
   std::string expectedUrl("abc.com");
   wstr ipAddr(L"*******");
   VMOCK(&util::WinInet::GetIpAddressFiltered)
      .ExpectCall(_, _)
      .Times(2)
      .WillRepeatedly(Return(ipAddr));
   auto expectedPayload = GetExpectedLcmFetchPayload(ipAddr, "1.0");

   SetupHttpPostRequestMock(mockFetchLcmFailOnce, expectedUrl, expectedPayload, "",
                            {"Authorization", "Bearer myrefreshtoken"}, 500);

   // Simulate a second failed LCM spec fetch
   SetupHttpPostRequestMock(mockFetchLcmFailTwice, expectedUrl, expectedPayload, "",
                            {"Authorization", "Bearer myrefreshtoken"}, 403);

   // Reduce the time it takes to get another cycle started
   SETUP_RETRY_INTERVALS();

   MqttManager::GetInstance().Start();

   Sleep(3000);

   EXPECT_FALSE(MqttManager::GetInstance().IsConnected());

   MqttManager::GetInstance().Stop();
   MqttManager::GetInstance().SetFileFactory(nullptr);

   DeleteLCMSpecInRegistry();
   StopApp();
   MqttManagerClearFileFactory();
}
