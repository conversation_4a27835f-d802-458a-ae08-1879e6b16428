/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * blastSocket.c --
 *
 *      Interface for Blast Socket module code.
 */

#include <stdlib.h>

#include "asyncsocket.h"
#include "asyncWebSocket.h"
#include "blastSocket.h"
#include "blastSocketInt.h"
#include "blastSocketUtil.h"
#include "fecAsyncSocket.h"
#include "fecSocketOptions.h"
#include "log.h"
#include "util.h"
#include "str.h"
#include "horizon.h"
#include "vthread.h"


/*
 * These are tables of acceptable WebSocket subprotocols for different
 * combinations of Legacy and Continuity configuration modes.  Subprotocols
 * within each table are sorted in preference order, starting with the
 * most-preferred subprotocol.
 *
 * Note that the deprecated _VVC_OLD has higher preference than _VVC.
 * This is a workaround for the behaviour of some remnant code in the
 * Blast Service, which drops the connection if the worker chooses the
 * _VVC subprotocol.  This issue does not affect customers.  It was
 * introduced during CART 18fq3 development and only applies to internal
 * Blast agent builds made during the CART 18fq3 development cycle up to
 * the end of July when the misbehaving remnant Service code was removed.
 * Even so, we don't want to force developers to have to upgrade all of
 * their agents as soon as client code using these tables lands in
 * official builds.
 *
 * VVC_OLD can be demoted below _VVC after a grace period of a couple of
 * weeks.  That should be enough time for developers to naturally move to
 * newer builds of the 18fq3 agent.
 */

static const char *subprotocols_websocket_continuity[] = {
   BLAST_SOCKET_WEBSOCKET_SUBPROTOCOL_VVC_WITH_CONTINUITY,
   BLAST_SOCKET_WEBSOCKET_SUBPROTOCOL_VVC_NO_CONTINUITY, NULL};

static const char *subprotocols_websocket[] = {BLAST_SOCKET_WEBSOCKET_SUBPROTOCOL_VVC_NO_CONTINUITY,
                                               NULL};

static const char *subprotocols_websocket_continuity_legacy[] = {
   BLAST_SOCKET_WEBSOCKET_SUBPROTOCOL_VVC_WITH_CONTINUITY,
   BLAST_SOCKET_WEBSOCKET_SUBPROTOCOL_VVC_CONTINUITY_LEGACY,
   BLAST_SOCKET_WEBSOCKET_SUBPROTOCOL_VVC_NO_CONTINUITY,
   BLAST_SOCKET_WEBSOCKET_SUBPROTOCOL_VVC_OLD_LEGACY,
   BLAST_SOCKET_WEBSOCKET_SUBPROTOCOL_VVC_LEGACY,
   NULL};

static const char *subprotocols_websocket_legacy[] = {
   BLAST_SOCKET_WEBSOCKET_SUBPROTOCOL_VVC_NO_CONTINUITY,
   BLAST_SOCKET_WEBSOCKET_SUBPROTOCOL_VVC_OLD_LEGACY, BLAST_SOCKET_WEBSOCKET_SUBPROTOCOL_VVC_LEGACY,
   NULL};

// Global variables to prevent BlastSockets from being stopped/started twice
static Atomic_Ptr gInitLockStorage;
MXUserExclLock *gInitLock;
static Bool isBlastSocketsInitialized;

/* gcc needs special syntax to handle zero-length variadic arguments */
#if defined(_MSC_VER)
#   define BLASTSOCKETLOG(fmt, ...) Log("[BlastSocket] %s: " fmt "\n", __FUNCTION__, __VA_ARGS__)
#else
#   define BLASTSOCKETLOG(fmt, ...) Log("[BlastSocket] %s: " fmt "\n", __FUNCTION__, ##__VA_ARGS__)
#endif

static void BlastSocketHandleListenError(Bool isWebSocket, int error, AsyncSocket *asock,
                                         void *clientData);
static void BlastSocketWSListenErrorCb(int error, AsyncSocket *asock, void *clientData);
static void BlastSocketUDPListenErrorCb(int error, AsyncSocket *asock, void *clientData);
static void BlastSocketWSConnectionAccepted(AsyncSocket *asock, void *clientData);
static void BlastSocketUDPConnectionAccepted(AsyncSocket *asock, void *clientData);
static int BlastSocketPrepareUpgradeRequest(AsyncSocket *asock, WebSocketHttpRequest *httpRequest,
                                            void *clientData);
static int BlastSocketProcessUpgradeResponse(AsyncSocket *asock, WebSocketHttpRequest *httpResponse,
                                             void *clientData);
static BlastSocketContext *BlastSocketStart(
   void *sslCtx, Bool useSSL, const char *webSocketAddr, int *webSocketPort, int *udpFecPort,
   int numUdpFecPorts, const BlastSocketParams *blastSocketParams, BlastSocketAcceptCb acceptFn,
   BlastSocketCloseCb socketCloseCb, BlastSessionStartedCb sessionStartedCb,
   BlastSessionInvalidatedCb sessionInvalidatedCb, void *clientData);

static int BlastSocketStartReverseConnection(BlastSocketContext *blastSocketContext,
                                             const char *reverseHost, unsigned int reversePort,
                                             AsyncSocketConnectFn onConnectCb,
                                             AsyncSocketErrorFn errorFn, void *clientdata,
                                             AsyncSocketConnectFlags connectFlags,
                                             AsyncSocket **reverseAsock);

void BlastSocketStopSessionFromSessionMapIterateCb(void *key, void *data, void *userData);
void BlastSocketStop(BlastSocketContext *blastSocketContext);

/*
 *-----------------------------------------------------------------------------
 *
 * BlastSocketHandleListenError --
 *
 *   Log errors on listen Asyncsocket and close the socket.
 *
 * Results:
 *   None.
 *
 *-----------------------------------------------------------------------------
 */

void
BlastSocketHandleListenError(Bool isWebSocket,   // IN
                             int error,          // IN
                             AsyncSocket *asock, // IN
                             void *clientData)   // IN
{
   BlastSocketContext *blastSocketContext = clientData;
   ASSERT(clientData != NULL);

   BLASTSOCKETLOG("Error:%d, isWebSocket:%d, asock:%p. Closing the socket.", error, isWebSocket,
                  asock);
   if (error == ASOCKERR_GENERIC) {
      BLASTSOCKETLOG("ASOCKERR_GENERIC, error:%d", AsyncSocket_GetGenericErrno(asock));
   }

   AsyncSocket_Close(asock);
   if (isWebSocket) {
      blastSocketContext->webSocketListenSocket = NULL;
   } else {
      blastSocketContext->udpFecListenSocket = NULL;
   }

   BLASTSOCKETLOG("asock %p closed successfully", asock);
}


/*
 *-----------------------------------------------------------------------------
 *
 * BlastSocketWSListenErrorCb --
 *
 *    Error callback to handle socket errors on websocket listen socket
 *
 * Results:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

void
BlastSocketWSListenErrorCb(int error,          // IN
                           AsyncSocket *asock, // IN
                           void *clientData)   // IN
{
   ASSERT(clientData != NULL);
   BlastSocketHandleListenError(TRUE, error, asock, clientData);
}


/*
 *-----------------------------------------------------------------------------
 *
 * BlastSocketUDPListenErrorCb --
 *
 *    Error callback to handle socket errors on UDPFEC listen socket
 *
 * Results:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

void
BlastSocketUDPListenErrorCb(int error,          // IN
                            AsyncSocket *asock, // IN
                            void *clientData)   // IN
{
   ASSERT(clientData != NULL);
   BlastSocketHandleListenError(FALSE, error, asock, clientData);
}


/*
 *-----------------------------------------------------------------------------
 *
 * BlastSocket_IsBlastSocketThreadEnabled --
 *
 *    TRUE if blastSocket thread is available for poll
 *
 * Results:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

Bool
BlastSocket_IsBlastSocketThreadEnabled(BlastSocketContext *blastSocketContext)
{
   return blastSocketContext->params.blastSocketThreadEnabled;
}


/*
 *-----------------------------------------------------------------------------
 *
 * BlastSocketConnectionAccepted --
 *
 *    Connection accepted callback. Send this accepted unauthenticated
 *    AsyncSocket to VVCSessionManager for further processing.
 *
 *    SerialNo was added for TCP raw connections and is not applicable (0) for
 *    primary TCP or secondary BEAT Blast connections.
 *
 * Results:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

void
BlastSocketConnectionAccepted(Bool isWebSocket,   // IN
                              AsyncSocket *asock, // IN
                              void *clientData,   // IN
                              int serialNo)       // IN
{
   BlastSocketContext *blastSocketContext = clientData;
   ASSERT(clientData != NULL);
   AsyncSocket_SetCloseOptions(asock, ASYNCSOCKET_FLUSH_TIMEOUT_MSEC, NULL);
   AsyncSocket_UseNodelay(asock, TRUE);
   BlastSocketAcceptConnection(isWebSocket, asock, blastSocketContext, serialNo);
}


/*
 *-----------------------------------------------------------------------------
 *
 * BlastSocketWSConnectionAccepted --
 *
 *    Notification when a new websocket connection is accepted.
 *
 * Results:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

void
BlastSocketWSConnectionAccepted(AsyncSocket *asock, // IN
                                void *clientData)   // IN
{
   ASSERT(clientData != NULL);
   BlastSocketConnectionAccepted(TRUE, asock, clientData, 0 /* NA */);
}


/*
 *-----------------------------------------------------------------------------
 *
 * BlastSocketUDPConnectionAccepted --
 *
 *    Notification when a new connection is accepted over UDPFEC.
 *
 * Results:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

void
BlastSocketUDPConnectionAccepted(AsyncSocket *asock, // IN
                                 void *clientData)   // IN
{
   ASSERT(clientData != NULL);
   BlastSocketConnectionAccepted(FALSE, asock, clientData, 0 /* NA */);
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketPrepareUpgradeRequest --
 *
 *      Add UDP headers to websocket http upgrade request. Make a user
 *      callback which will fill the necessary UDP E2E, Hop and Connection
 *      Info request headers
 *
 * Results:
 *      Return ASOCKERR_SUCCESS when success, Error otherwise.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

int
BlastSocketPrepareUpgradeRequest(AsyncSocket *asock, WebSocketHttpRequest *httpRequest,
                                 void *clientData)
{
   BlastSocketConnectContext *connectCtx = clientData;
   BlastWSParams *blastWSParams = NULL;
   BlastUDPParams *udpParams = NULL;
   Bool udpHeadersPresent = TRUE;

   if (NULL == connectCtx) {
      BLASTSOCKETLOG("NULL clientData. Failed to add UDP headers to WS Upgrade "
                     "request.");
      return ASOCKERR_INVAL;
   }

   if (NULL == connectCtx->prepareWSUpgradeRequestFn) {
      BLASTSOCKETLOG("No user callback set. Failed to add UDP headers to "
                     "WS Upgrade request.");
      return ASOCKERR_INVAL;
   }

   blastWSParams = connectCtx->prepareWSUpgradeRequestFn(connectCtx->clientData);

   if (NULL == blastWSParams) {
      BLASTSOCKETLOG("NULL BlastWSParams. Failed to add Blast headers to "
                     "WS Upgrade request.");
      return ASOCKERR_INVAL;
   }

   if (NULL == blastWSParams->udpParams) {
      BLASTSOCKETLOG("NULL UDPParams. Failed to add UDP headers to WS Upgrade "
                     "request.");
      udpHeadersPresent = FALSE;
   }

   if (udpHeadersPresent) {
      udpParams = blastWSParams->udpParams;

      if (udpParams->udpE2EReqValue) {
         WebSocketHttpRequestPrintf(httpRequest, "%s %s\r\n", UDP_E2E_REQ_HEADER_STRING,
                                    udpParams->udpE2EReqValue);
      }

      if (udpParams->udpE2EReqOldValue) {
         WebSocketHttpRequestPrintf(httpRequest, "%s %s\r\n", OLD_UDP_E2E_REQ_HEADER_STRING,
                                    udpParams->udpE2EReqOldValue);
      }

      if (udpParams->udpHopReqValue) {
         WebSocketHttpRequestPrintf(httpRequest, "%s %s\r\n", UDP_HOP_REQ_HEADER_STRING,
                                    udpParams->udpHopReqValue);
      }

      if (udpParams->udpHopReqOldValue) {
         WebSocketHttpRequestPrintf(httpRequest, "%s %s\r\n", OLD_UDP_HOP_REQ_HEADER_STRING,
                                    udpParams->udpHopReqOldValue);
      }

      if (udpParams->connInfo) {
         WebSocketHttpRequestPrintf(httpRequest, "%s %s\r\n", E2E_CLIENT_CONN_INFO_HEADER_STRING,
                                    udpParams->connInfo);
      }

      if (udpParams->connInfoOld) {
         WebSocketHttpRequestPrintf(httpRequest, "%s %s\r\n",
                                    OLD_E2E_CLIENT_CONN_INFO_HEADER_STRING, udpParams->connInfoOld);
      }
   }

   if (blastWSParams->blastE2EReqValue) {
      WebSocketHttpRequestPrintf(httpRequest, "%s %s\r\n", BLAST_E2E_REQ_HEADER_STRING,
                                 blastWSParams->blastE2EReqValue);
   }

   if (blastWSParams->blastE2EReqOldValue) {
      WebSocketHttpRequestPrintf(httpRequest, "%s %s\r\n", OLD_BLAST_E2E_REQ_HEADER_STRING,
                                 blastWSParams->blastE2EReqOldValue);
   }

   if (blastWSParams->blastHopReqValue) {
      WebSocketHttpRequestPrintf(httpRequest, "%s %s\r\n", BLAST_HOP_REQ_HEADER_STRING,
                                 blastWSParams->blastHopReqValue);
   }

   if (blastWSParams->blastHopReqOldValue) {
      WebSocketHttpRequestPrintf(httpRequest, "%s %s\r\n", OLD_BLAST_HOP_REQ_HEADER_STRING,
                                 blastWSParams->blastHopReqOldValue);
   }

   if (blastWSParams->blastFeaturesReqValue) {
      WebSocketHttpRequestPrintf(httpRequest, "%s %s\r\n", BLAST_FEATURES_REQ_HEADER_STRING,
                                 blastWSParams->blastFeaturesReqValue);
   }

   if (blastWSParams->blastFeaturesReqOldValue) {
      WebSocketHttpRequestPrintf(httpRequest, "%s %s\r\n", OLD_BLAST_FEATURES_REQ_HEADER_STRING,
                                 blastWSParams->blastFeaturesReqOldValue);
   }

   return ASOCKERR_SUCCESS;
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketProcessUpgradeResponse --
 *
 *      Process websocket http upgrade response. Make a user callback that
 *      will do the actual processing of http response buffer.
 *
 * Results:
 *      Return ASOCKERR_SUCCESS when success, Error otherwise.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

int
BlastSocketProcessUpgradeResponse(AsyncSocket *asock, WebSocketHttpRequest *httpResponse,
                                  void *clientData)
{
   BlastSocketConnectContext *connectCtx = clientData;

   ASSERT(NULL != httpResponse);
   if (NULL == connectCtx) {
      BLASTSOCKETLOG("NULL clientData. Failed to process UDP headers in WS "
                     "Upgrade response.");
      return ASOCKERR_INVAL;
   }

   if (NULL == connectCtx->processWSUpgradeResponseFn) {
      BLASTSOCKETLOG("No user callback set. Failed to process UDP headers in "
                     "WS Upgrade response.");
      return ASOCKERR_INVAL;
   }

   connectCtx->processWSUpgradeResponseFn(httpResponse->buf, connectCtx->clientData);

   return ASOCKERR_SUCCESS;
}


/*
 *----------------------------------------------------------------------------
 *
 * IPFamilyForAddress, IPAnyForFamily, IPAnyForAddress --
 *
 *      Helpers for categorising IP address strings.  There's probably a
 *      better place for these, or they might even already exist someplace,
 *      but I don't have time to research that.  TODO: Clean this up.
 *
 * Results:
 *      IPFamilyForAddress returns AF_INET or AF_INET6
 *
 *      IPAny... returns the appropriate "any" (aka "wildcard") address as
 *      a string
 *
 *----------------------------------------------------------------------------
 */


static int
IPFamilyForAddress(const char *addr)
{
   return (NULL == Str_Strchr(addr, ':')) ? AF_INET : AF_INET6;
}


static char *
IPAnyForFamily(int family)
{
   return (AF_INET6 == family) ? "::" : "0.0.0.0";
}


static char *
IPAnyForAddress(const char *addr)
{
   return IPAnyForFamily(IPFamilyForAddress(addr));
}


#ifdef BENEV
int HandleHandoffSocketCb(AsyncSocket *asock, // IN
                          void *clientData,   // IN
                          void *buf,          // IN
                          uint32 bufLen,      // IN
                          uint32 currentPos); // IN
#endif

/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketStart --
 *
 *      Initializes BlastSocket module. webSocketPort and udpFecPort are
 *      provided as input. If zero, then they will get updated to the final
 *      port number.
 *
 * Results:
 *      Valid Context on success, NULL context on failure.
 *
 *----------------------------------------------------------------------------
 */

BlastSocketContext *
BlastSocketStart(void *sslCtx,                                   // IN
                 Bool useSSL,                                    // IN
                 const char *webSocketAddr,                      // IN
                 int *webSocketPort,                             // IN/OUT
                 int *udpFecPort,                                // IN/OUT
                 int numUdpFecPorts,                             // IN
                 const BlastSocketParams *blastSocketParams,     // IN
                 BlastSocketAcceptCb acceptFn,                   // IN
                 BlastSocketCloseCb socketCloseCb,               // IN
                 BlastSessionStartedCb sessionStartedCb,         // IN
                 BlastSessionInvalidatedCb sessionInvalidatedCb, // IN
                 void *clientData)                               // IN
{
   AsyncSocketPollParams pollParams;
   const char **webSocketProtocolList = NULL;
   BlastSocketContext *blastSocketContext = Util_SafeCalloc(1, sizeof *blastSocketContext);
   BlastSocketParams *bsParams = NULL;
   AsyncSocket *asock = NULL;
   int outError = 0;

   gInitLock =
      MXUser_CreateSingletonExclLock(&gInitLockStorage, "blastSocketsInitLock", RANK_UNRANKED);
   MXUser_AcquireExclLock(gInitLock);
   if (isBlastSocketsInitialized) {
      BLASTSOCKETLOG("Error: BlastSocketStart has already been called.");
      goto fail;
   }

   VThread_Init("BlastSocket");
   ASSERT(blastSocketParams != NULL);
   blastSocketContext->params = *blastSocketParams; // save a copy
   bsParams = &blastSocketContext->params;

   if (bsParams->blastSocketThreadEnabled) {
      int cores = BlastSocketGetNumCores();
      if (cores <= 1) {
         bsParams->blastSocketThreadEnabled = FALSE;
         BLASTSOCKETLOG("BlastSocket thread not enabled due to %d cores.", cores);
      }
   }

   blastSocketContext->sessionMapLock =
      MXUser_CreateExclLock("BlastSocketContextSessionMapLock", RANK_UNRANKED);

   blastSocketContext->sessionMap = HashMap_AllocMap(
      MAX_VVC_SESSIONS_PER_SESSIONMGR, sizeof(char *), sizeof(BlastSocketVvcSessionWrapper *));

   // Save the callback function pointers.
   blastSocketContext->callbacks.acceptFn = acceptFn;
   blastSocketContext->callbacks.socketCloseCb = socketCloseCb;
   blastSocketContext->callbacks.sessionStartedCb = sessionStartedCb;
   blastSocketContext->callbacks.sessionInvalidatedCb = sessionInvalidatedCb;
   blastSocketContext->callbacks.cbFuncClientData = clientData;

   // initialize auth module.
   blastSocketContext->isAuthInitialized = FALSE;
   if (FALSE == BlastSocketInitAuthMgr(blastSocketContext)) {
      BLASTSOCKETLOG("Failed to initialize authentication module.");
      goto fail;
   }

   blastSocketContext->nextShadowInstanceId = 1;

   memset(&blastSocketContext->switchPolicyParams, 0,
          sizeof(blastSocketContext->switchPolicyParams));

   /*
    * Why QoSPolicParams is a separate struct than SwitchPolicyParams:
    *    With BENIT Enabled: QoSPolicyParams will be sent by agent to client.
    *    With BENIT Disabled: QoSPolicy will still get applied locally.
    *
    *    Since QoSPolicy should get applied even with BENIT disabled, define
    *    a separate struct and an API.
    */
   memset(&blastSocketContext->qosPolicyParams, VVC_QOS_INVALID_VALUE,
          sizeof(blastSocketContext->qosPolicyParams));

   memset(&blastSocketContext->deferredAcksParams, 0,
          sizeof(blastSocketContext->deferredAcksParams));

   blastSocketContext->wsPeerConfigMapLock =
      MXUser_CreateExclLock("BlastSocketContextWSPeerConfigMapLock", RANK_UNRANKED);

   blastSocketContext->wsPeerConfigMap = HashMap_AllocMap(
      MAX_VVC_SESSIONS_PER_SESSIONMGR, sizeof(char *), sizeof(BlastSocketWSPeerConfig *));

   BlastSocketLock_Init();

   pollParams.pollClass = bsParams->blastSocketThreadEnabled ? POLL_DEFAULT_CS_NET : POLL_CS_MAIN;
   pollParams.flags = 0;
   pollParams.lock = BlastSocket_GetLock();

   if (bsParams->blastSocketThreadEnabled) {
      BlastSocketThread_Init();
   }

   BlastSocketInitVvc(blastSocketContext);

   if (!bsParams->localNCEnabled) {
      BLASTSOCKETLOG("Network Continuity is locally disabled. Also "
                     "disable network intelligence, vvc close sequence and "
                     "vvc pause-resume.");
      bsParams->localNetworkIntelligenceEnabled = FALSE;
      bsParams->localVVCCloseSeqEnabled = FALSE;
      bsParams->localVVCPauseResumeEnabled = FALSE;
   }

   BLASTSOCKETLOG("Server IP is %s", '\0' == bsParams->serverIp[0] ? "empty" : bsParams->serverIp);
   BLASTSOCKETLOG("Bandwidth Estimation is %slocally enabled.", bsParams->bweEnabled ? "" : "not ");
   BLASTSOCKETLOG("Network Continuity is %slocally enabled.",
                  bsParams->localNCEnabled ? "" : "not ");
   BLASTSOCKETLOG("Network Intelligence is %slocally enabled.",
                  bsParams->localNetworkIntelligenceEnabled ? "" : "not ");
   BLASTSOCKETLOG("VVCPauseResume is %slocally enabled.",
                  bsParams->localVVCPauseResumeEnabled ? "" : "not ");
   BLASTSOCKETLOG("VVCQoSPolicy is %slocally enabled.",
                  bsParams->localVVCQoSPolicyEnabled ? "" : "not ");
   BLASTSOCKETLOG("VVCCloseSeq is %slocally enabled.",
                  bsParams->localVVCCloseSeqEnabled ? "" : "not ");
   BLASTSOCKETLOG("VVCBatching is %slocally enabled.",
                  bsParams->localVVCBatchingEnabled ? "" : "not ");
   BLASTSOCKETLOG("Legacy Blast Websocket Protocols are %sallowed.",
                  bsParams->legacyBlastWSProtocolsAllowed ? "" : "not ");

   if (bsParams->legacyBlastWSProtocolsAllowed) {
      if (bsParams->localNCEnabled) {
         webSocketProtocolList = subprotocols_websocket_continuity_legacy;
      } else {
         webSocketProtocolList = subprotocols_websocket_legacy;
      }
   } else {
      if (bsParams->localNCEnabled) {
         webSocketProtocolList = subprotocols_websocket_continuity;
      } else {
         webSocketProtocolList = subprotocols_websocket;
      }
   }

   BLASTSOCKETLOG("Listen for websocket connection, port %d.", *webSocketPort);

#ifndef BENEV
   asock = AsyncSocket_PrepareListenWebSocket(
      useSSL, webSocketProtocolList, BlastSocketWSConnectionAccepted, blastSocketContext,
      &pollParams, sslCtx, BlastSocketHandleUpgradeRequest, NULL, NULL);
#else
   {
      /*
       * Since Benev doesn't do cross-process socket handoff from service to worker
       * we have to simulate the socket handloff here so that the handoff callback
       * HandleHandoffSocketCb() will get called when ALPN is received.
       */
      int alpnPrefixLen = strlen(BLAST_SOCKET_TLS_ALPN_PREFIX);
#   define ALPN_PREFIX_ARRAY_LEN 16
      char alpn[ALPN_PREFIX_ARRAY_LEN] = {0};

      alpn[0] = alpnPrefixLen + BLAST_SOCKET_TLS_ALPN_SFX_ENCODELEN;
      memcpy(alpn + 1, BLAST_SOCKET_TLS_ALPN_PREFIX, alpnPrefixLen);

      // Pass sslCtx to HandleHandoffSocketCb through blastSocketContext->bsParams
      bsParams->sslCtx = sslCtx;

      asock = AsyncSocket_PrepareListenWebSocket(
         useSSL, webSocketProtocolList, BlastSocketWSConnectionAccepted, blastSocketContext,
         &pollParams, sslCtx, BlastSocketHandleUpgradeRequest, HandleHandoffSocketCb,
         alpn[0] != 0 ? alpn : NULL);
   }
#endif

   if (asock) {
      asock = AsyncSocket_RegisterListenWebSocket(asock, webSocketAddr, *webSocketPort, &pollParams,
                                                  &outError);
   }

   if (asock == NULL) {
      BLASTSOCKETLOG("Error listening on WebSocket Port %d, error: %d", *webSocketPort, outError);
      BlastSocketStop(blastSocketContext);
      MXUser_ReleaseExclLock(gInitLock);
      return NULL;
   }

   blastSocketContext->webSocketListenSocket = asock;

   // Flush the websocket close frame synchronously on socket close
   AsyncSocket_SetCloseOptions(blastSocketContext->webSocketListenSocket,
                               ASYNCSOCKET_FLUSH_TIMEOUT_MSEC, NULL);

   AsyncSocket_SetErrorFn(blastSocketContext->webSocketListenSocket, BlastSocketWSListenErrorCb,
                          blastSocketContext); // clientData

   if (0 == *webSocketPort) {
      *webSocketPort = AsyncSocket_GetPort(blastSocketContext->webSocketListenSocket);
      BLASTSOCKETLOG("Requested dynamic port, got WebSocket port:%d", *webSocketPort);
   }

   if (bsParams->udpEnabled) {
      /*
       * Listen for connections directly from the client if UDP is enabled.
       * Save the port number in case we need to send it to the service.
       */
      int outError;
      FECSocketOptionsStatic staticOpts;
      const char *listenIp;
      AsyncSocketPollParams fecPollParams;

      FECAsyncSocket_Init();

      fecPollParams.pollClass =
         bsParams->blastSocketThreadEnabled ? POLL_DEFAULT_CS_NET : POLL_CS_MAIN;
      fecPollParams.flags = 0;
      fecPollParams.lock = BlastSocket_GetLock();

      /*
       * Obtain set of default socket options; then set whatever we want to
       * override.
       */
      staticOpts = FECSocketOptionsStatic_CreateDefault();

      /*
       * Trigger keepalive at least half the expected transport keepalive
       * timeout period. But if the recommended FECSocket option keepalive
       * period is lower, use that value instead.
       */
      staticOpts.keepaliveTimeout =
         MIN(TRANSPORT_KEEPALIVE_TIMEOUT_SEC / 2, FEC_SOCKET_OPTIONS_KEEPALIVE_SECS);

      if (bsParams->maxLowLevelPacketSizeBytes != 0) {
         staticOpts.maxLowLevelPacketSize = (uint32)(bsParams->maxLowLevelPacketSizeBytes);
      }

      // Push AOutUDP Dscp values to FECAsyncSocket_Listen() via fecStaticOpts
      if (bsParams->localVVCQoSPolicyEnabled) {
         staticOpts.dscpOutV4 = bsParams->dscpAOutUDPv4;
         staticOpts.dscpOutV6 = bsParams->dscpAOutUDPv6;
      }

      staticOpts.externalPort = bsParams->serverUdpExternalPort;

      BLASTSOCKETLOG("SSL Ctx is %p", sslCtx);
      /*
       * Save sslCtx for future on-demand creation of server-side listening
       * sockets for VVC raw channels.
       */
      bsParams->sslCtx = sslCtx;

      if ('\0' == bsParams->serverIp[0]) {
         // For historical compatibility an empty serverIp gets IPv4-any
         listenIp = IPAnyForFamily(AF_INET);
         BLASTSOCKETLOG("Listen on empty serverIp, will use addr:%s.", listenIp);
      } else {
         // Listen on the "any" address of the appropriate addresss family
         listenIp = IPAnyForAddress(bsParams->serverIp);
         BLASTSOCKETLOG("Listen on addr:%s derived from serverIp:%s.", listenIp,
                        bsParams->serverIp);
      }

      /*
       * If numPorts is -1, then we listen only on the specified port.
       * Otherwise, listen on the entire range of ports.
       */
      BLASTSOCKETLOG("Listen for FEC connection at addr:%s port:%d "
                     "numPorts:%d.",
                     listenIp, *udpFecPort, numUdpFecPorts);

      if (bsParams->udpSSLEnabled == FALSE) {
         BLASTSOCKETLOG("SSL disabled for FEC connection.");
      }

      if (numUdpFecPorts == -1) {
         // Use listen API that accepts a single port value.
         blastSocketContext->udpFecListenSocket = FECAsyncSocket_Listen(
            listenIp, *udpFecPort, BlastSocketUDPConnectionAccepted,
            blastSocketContext, // clientData
            &fecPollParams, bsParams->udpSSLEnabled, sslCtx, &staticOpts, &outError);
         if (blastSocketContext->udpFecListenSocket == NULL) {
            BLASTSOCKETLOG("Error:%d listening on UDP FEC addr:%s port:%d", outError, listenIp,
                           *udpFecPort);
            /*
             * Instead of failing, set udpEnabled to False and allow the
             * worker to continue with just the TCP connection.
             */
            blastSocketContext->params.udpEnabled = FALSE;
            goto exit;
         }
      } else {
         // Use listen API that accepts a range of ports.
         blastSocketContext->udpFecListenSocket = FECAsyncSocket_ListenInPortRange(
            listenIp, *udpFecPort, numUdpFecPorts, BlastSocketUDPConnectionAccepted,
            blastSocketContext, // clientData
            &fecPollParams, bsParams->udpSSLEnabled, sslCtx, &staticOpts, &outError);
         if (blastSocketContext->udpFecListenSocket == NULL) {
            BLASTSOCKETLOG("Error:%d listening on UDP FEC addr:%s port:%d "
                           "numPorts:%d",
                           outError, listenIp, *udpFecPort, numUdpFecPorts);
            /*
             * Instead of failing, set udpEnabled to False and allow the
             * worker to continue with just the TCP connection.
             */
            blastSocketContext->params.udpEnabled = FALSE;
            goto exit;
         }
         *udpFecPort = AsyncSocket_GetPort(blastSocketContext->udpFecListenSocket);
         BLASTSOCKETLOG("Using Port Range API, actual UDP FEC port:%d", *udpFecPort);
      }

      // Flush the websocket close frame synchronously on socket close
      AsyncSocket_SetCloseOptions(blastSocketContext->udpFecListenSocket,
                                  ASYNCSOCKET_FLUSH_TIMEOUT_MSEC, NULL);
      AsyncSocket_SetErrorFn(blastSocketContext->udpFecListenSocket, BlastSocketUDPListenErrorCb,
                             blastSocketContext); // clientData

      // udpFecPort should never be 0 at this point
      ASSERT(0 != *udpFecPort);
   }

   blastSocketContext->udpFecPort = *udpFecPort;

exit:
   isBlastSocketsInitialized = TRUE;
   MXUser_ReleaseExclLock(gInitLock);

   return blastSocketContext;

fail:
   free(blastSocketContext);
   MXUser_ReleaseExclLock(gInitLock);
   return NULL;
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketStartReverseConnection --
 *
 *      Triggers AsyncSocket_Connect for the Reverse Connection Host and Port
 *      with the provided callbacks.
 *
 * Results:
 *      ASOCKERR_* with valid Asock on success, NULL on failure.
 *
 *----------------------------------------------------------------------------
 */

int
BlastSocketStartReverseConnection(BlastSocketContext *blastSocketCtx,   // IN
                                  const char *reverseHost,              // IN
                                  unsigned int reversePort,             // IN
                                  AsyncSocketConnectFn onConnectCb,     // IN
                                  AsyncSocketErrorFn errorFn,           // IN
                                  void *clientData,                     // IN
                                  AsyncSocketConnectFlags connectFlags, // IN
                                  AsyncSocket **reverseAsock)           // OUT
{
   int error = ASOCKERR_GENERIC;
   AsyncSocketPollParams pollParams;
   const Bool sendLowLatencyMode = TRUE;

   if (!isBlastSocketsInitialized) {
      BLASTSOCKETLOG("BlastSockets not initialized, couldn't start Reverse "
                     "Connection");
      return error;
   }

   pollParams.pollClass =
      BlastSocket_IsBlastSocketThreadEnabled(blastSocketCtx) ? POLL_DEFAULT_CS_NET : POLL_CS_MAIN;
   pollParams.flags = 0;
   pollParams.lock = BlastSocket_GetLock();

   BlastSocket_Lock();
   *reverseAsock = AsyncSocket_Connect(reverseHost, reversePort, onConnectCb, clientData,
                                       connectFlags, &pollParams, &error);

   if (*reverseAsock == NULL) {
      BLASTSOCKETLOG("Failed to start Reverse Connection for reverseAddress: "
                     "%s:%u. Error: %d",
                     reverseHost, reversePort, error);
      BlastSocket_Unlock();
      return error;
   }

   AsyncSocket_SetErrorFn(*reverseAsock, errorFn, clientData);

   error = AsyncSocket_SetCloseOptions(*reverseAsock, ASYNCSOCKET_FLUSH_TIMEOUT_MSEC, NULL);
   if (error != ASOCKERR_SUCCESS) {
      BLASTSOCKETLOG("Failed to set socket close flush timeout option for "
                     "asock(%d). Error: %d",
                     AsyncSocket_GetID(*reverseAsock), error);
   }

   error = AsyncSocket_SetOption(*reverseAsock, ASYNC_SOCKET_OPTS_LAYER_BASE,
                                 ASYNC_SOCKET_OPT_SEND_LOW_LATENCY_MODE, &sendLowLatencyMode,
                                 sizeof sendLowLatencyMode);
   if (error != ASOCKERR_SUCCESS) {
      BLASTSOCKETLOG("Failed to set Low Latency Mode socket option for "
                     "asock(%d). Error: %d",
                     AsyncSocket_GetID(*reverseAsock), error);
   }

   BLASTSOCKETLOG("Start Reverse Connection on %s:%u", reverseHost, reversePort);
   BlastSocket_Unlock();
   return ASOCKERR_SUCCESS;
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketStopSessionFromSessionMapIterateCb --
 *
 *      Callback function for HashMap_Iterate() when clearing the session
 *      map. Force stop the VVC Session found in the map entry
 *
 * Results:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

void
BlastSocketStopSessionFromSessionMapIterateCb(void *key,      // IN
                                              void *data,     // IN
                                              void *userData) // IN/OUT
{
   BlastSocketVvcSessionWrapper *sessionWrapper;
   BlastSocketContext *blastSocketCtx = userData;

   ASSERT(data != NULL);
   ASSERT(userData != NULL);

   sessionWrapper = *(BlastSocketVvcSessionWrapper **)data;

   ASSERT(sessionWrapper != NULL);

   BLASTSOCKETLOG("Stopping VVCSession:%d, vAuth:%" MASK_TOKEN " from session map.",
                  sessionWrapper->vvcSessionId, sessionWrapper->vAuth);

   BlastSocketStopVvcSession(blastSocketCtx, sessionWrapper->vvcSessionId, sessionWrapper->vAuth,
                             VDPCONNECT_INVALID);
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketClearSessionMap --
 *
 *      Iterates over the session Map and stops VVC Session if any session
 *      found in the map. Destroys the map and lock after ensuring map
 *      is cleared.
 *
 * Results:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

void
BlastSocketClearSessionMap(BlastSocketContext *blastSocketCtx)
{
   ASSERT(blastSocketCtx);
   ASSERT(blastSocketCtx->sessionMap);

   HashMap_Iterate(blastSocketCtx->sessionMap, BlastSocketStopSessionFromSessionMapIterateCb, FALSE,
                   blastSocketCtx);

   ASSERT(HashMap_Count(blastSocketCtx->sessionMap) == 0);
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketDestroySessionMap --
 *
 *      Destroy SessionMap and SessionMapLock.
 *
 * Results:
 *      Destroys sessionMap and sessionMapLock.
 *
 *----------------------------------------------------------------------------
 */

void
BlastSocketDestroySessionMap(BlastSocketContext *blastSocketCtx)
{
   ASSERT(blastSocketCtx);
   ASSERT(blastSocketCtx->sessionMap);

   if (blastSocketCtx->sessionMapLock) {
      MXUser_DestroyExclLock(blastSocketCtx->sessionMapLock);
      blastSocketCtx->sessionMapLock = NULL;
   }

   if (blastSocketCtx->sessionMap) {
      // The SessionMap should not have any entries at this point.
      HashMap_DestroyMap(blastSocketCtx->sessionMap);
      blastSocketCtx->sessionMap = NULL;
   }
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketRemoveFromWSPeerConfigMapIterateCb --
 *
 *      Callback function for HashMap_Iterate() when clearing the wsPeerConfig
 *      map. Remove the map entry.
 *
 * Results:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

void
BlastSocketRemoveFromWSPeerConfigMapIterateCb(void *key,      // IN
                                              void *data,     // IN
                                              void *userData) // IN/OUT
{
   BlastSocketWSPeerConfig *wsPeerConfig;
   BlastSocketContext *blastSocketCtx = userData;

   ASSERT(data != NULL);
   ASSERT(userData != NULL);

   wsPeerConfig = *(BlastSocketWSPeerConfig **)data;

   ASSERT(wsPeerConfig != NULL);

   BLASTSOCKETLOG("Removing entry for vAuth:%" MASK_TOKEN " from wsPeerConfig map.",
                  wsPeerConfig->vAuth);

   // Free peerFeatureAllowList as BSConsumeWSPeerConfig method cannot free it
   free(wsPeerConfig->peerFeatureAllowList);
   wsPeerConfig->peerFeatureAllowList = NULL;

   // ConsumeWSPeerConfig func below removes the entry from Map & frees it.
   BlastSocketConsumeWSPeerConfig(blastSocketCtx, wsPeerConfig->vAuth);
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketClearWSPeerConfigMap --
 *
 *      Iterates over the wsPeerConfigMap and remove entries if present.
 *      Destroys the map and lock after ensuring map is cleared.
 *
 * Results:
 *      Destroys wsPeerConfigMap and wsPeerConfigMapLock.
 *
 *----------------------------------------------------------------------------
 */

void
BlastSocketClearWSPeerConfigMap(BlastSocketContext *blastSocketCtx) // IN
{
   ASSERT(blastSocketCtx);
   ASSERT(blastSocketCtx->wsPeerConfigMap);

   HashMap_Iterate(blastSocketCtx->wsPeerConfigMap, BlastSocketRemoveFromWSPeerConfigMapIterateCb,
                   FALSE, blastSocketCtx);

   ASSERT(HashMap_Count(blastSocketCtx->wsPeerConfigMap) == 0);

   if (blastSocketCtx->wsPeerConfigMapLock) {
      MXUser_DestroyExclLock(blastSocketCtx->wsPeerConfigMapLock);
      blastSocketCtx->wsPeerConfigMapLock = NULL;
   }

   // The SessionMap should not have any entries at this point.
   HashMap_DestroyMap(blastSocketCtx->wsPeerConfigMap);
   blastSocketCtx->wsPeerConfigMap = NULL;
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketGetVDPReasonFromVvcReason --
 *
 *      Map a given VvcSessionCloseReason to a VDPConnectionResult
 *
 * Results:
 *      VDPConnectionResult for the given VvcSessionCloseReason
 *
 *----------------------------------------------------------------------------
 */

VDPConnectionResult
BlastSocketGetVDPReasonFromVvcReason(VvcSessionCloseReason vvcReason) // IN
{
   switch (vvcReason) {
   case VvcSessionCloseInvalid:
      return VDPCONNECT_INVALID;
   case VvcSessionCloseNormal:
      return VDPCONNECT_FAILURE;
   case VvcSessionCloseNetworkDisconnect:
      return VDPCONNECT_NETWORK_FAILURE;
   case VvcSessionClosePeerRejected:
      return VDPCONNECT_REJECTED;
   default:
      return VDPCONNECT_FAILURE;
   }
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketValidateQoSPolicyParam --
 *
 *      Validate the value of a QoSPolicy Parameter.
 *
 * Results:
 *      Return TRUE if a valid value, FALSE otherwise.
 *
 *----------------------------------------------------------------------------
 */

Bool
BlastSocketValidateQoSPolicyParam(int qosPolicyParamValue) // IN
{
   if (qosPolicyParamValue >= VVC_QOS_MIN_VALUE && qosPolicyParamValue <= VVC_QOS_MAX_VALUE) {
      return TRUE;
   } else if (qosPolicyParamValue == VVC_QOS_INVALID_VALUE) {
      return TRUE;
   } else {
      return FALSE;
   }
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketValidateQoSPolicy --
 *
 *      Validate the value of a each QoSPolicy Parameter.
 *
 * Results:
 *      Returns TRUE if QoSPolicy Validated, FALSE otherwise.
 *
 *----------------------------------------------------------------------------
 */

Bool
BlastSocketValidateQoSPolicy(VvcQoSPolicyParams *qosPolicyParams) // IN
{
   ASSERT(qosPolicyParams);

   // Validate Agent-Outgoing (AOut) & Client-Outgoing (COut) TCPv4 and UDPv4
   if (!BlastSocketValidateQoSPolicyParam(qosPolicyParams->v1.dscpAOutTCPv4)) {
      BLASTSOCKETLOG("Invalid value of v1.dscpAOutTCPv4: %d, "
                     "Can't apply to socket.",
                     qosPolicyParams->v1.dscpAOutTCPv4);
      return FALSE;
   }

   if (!BlastSocketValidateQoSPolicyParam(qosPolicyParams->v1.dscpAOutUDPv4)) {
      BLASTSOCKETLOG("Invalid value of v1.dscpAOutUDPv4: %d, "
                     "Can't apply to socket.",
                     qosPolicyParams->v1.dscpAOutUDPv4);
      return FALSE;
   }

   if (!BlastSocketValidateQoSPolicyParam(qosPolicyParams->v1.dscpCOutTCPv4)) {
      BLASTSOCKETLOG("Invalid value of v1.dscpCOutTCPv4: %d, "
                     "Can't apply to socket.",
                     qosPolicyParams->v1.dscpCOutTCPv4);
      return FALSE;
   }

   if (!BlastSocketValidateQoSPolicyParam(qosPolicyParams->v1.dscpCOutUDPv4)) {
      BLASTSOCKETLOG("Invalid value of v1.dscpCOutUDPv4: %d, "
                     "Can't apply to socket.",
                     qosPolicyParams->v1.dscpCOutUDPv4);
      return FALSE;
   }

   // Validate BSG-Upstream (BUp) & BSG-Downstream (BDown) TCPv4 and UDPv4
   if (!BlastSocketValidateQoSPolicyParam(qosPolicyParams->v1.dscpBUpTCPv4)) {
      BLASTSOCKETLOG("Invalid value of v1.dscpBUpTCPv4: %d, "
                     "Can't apply to socket.",
                     qosPolicyParams->v1.dscpBUpTCPv4);
      return FALSE;
   }

   if (!BlastSocketValidateQoSPolicyParam(qosPolicyParams->v1.dscpBUpUDPv4)) {
      BLASTSOCKETLOG("Invalid value of v1.dscpBUpUDPv4: %d, "
                     "Can't apply to socket.",
                     qosPolicyParams->v1.dscpBUpUDPv4);
      return FALSE;
   }

   if (!BlastSocketValidateQoSPolicyParam(qosPolicyParams->v1.dscpBDownTCPv4)) {
      BLASTSOCKETLOG("Invalid value of v1.dscpBDownTCPv4: %d, "
                     "Can't apply to socket.",
                     qosPolicyParams->v1.dscpBDownTCPv4);
      return FALSE;
   }

   if (!BlastSocketValidateQoSPolicyParam(qosPolicyParams->v1.dscpBDownUDPv4)) {
      BLASTSOCKETLOG("Invalid value of v1.dscpBDownUDPv4: %d, "
                     "Can't apply to socket.",
                     qosPolicyParams->v1.dscpBDownUDPv4);
      return FALSE;
   }

   // Validate Agent-Outgoing (AOut) & Client-Outgoing (COut) TCPv6 and UDPv6
   if (!BlastSocketValidateQoSPolicyParam(qosPolicyParams->v1.dscpAOutTCPv6)) {
      BLASTSOCKETLOG("Invalid value of v1.dscpAOutTCPv6: %d, "
                     "Can't apply to socket.",
                     qosPolicyParams->v1.dscpAOutTCPv6);
      return FALSE;
   }

   if (!BlastSocketValidateQoSPolicyParam(qosPolicyParams->v1.dscpAOutUDPv6)) {
      BLASTSOCKETLOG("Invalid value of v1.dscpAOutUDPv6: %d, "
                     "Can't apply to socket.",
                     qosPolicyParams->v1.dscpAOutUDPv6);
      return FALSE;
   }

   if (!BlastSocketValidateQoSPolicyParam(qosPolicyParams->v1.dscpCOutTCPv6)) {
      BLASTSOCKETLOG("Invalid value of v1.dscpCOutTCPv6: %d, "
                     "Can't apply to socket.",
                     qosPolicyParams->v1.dscpCOutTCPv6);
      return FALSE;
   }

   if (!BlastSocketValidateQoSPolicyParam(qosPolicyParams->v1.dscpCOutUDPv6)) {
      BLASTSOCKETLOG("Invalid value of v1.dscpCOutUDPv6: %d, "
                     "Can't apply to socket.",
                     qosPolicyParams->v1.dscpCOutUDPv6);
      return FALSE;
   }

   // Validate BSG-Upstream (BUp) & BSG-Downstream (BDown) TCPv6 and UDPv6
   if (!BlastSocketValidateQoSPolicyParam(qosPolicyParams->v1.dscpBUpTCPv6)) {
      BLASTSOCKETLOG("Invalid value of v1.dscpBUpTCPv6: %d, "
                     "Can't apply to socket.",
                     qosPolicyParams->v1.dscpBUpTCPv6);
      return FALSE;
   }

   if (!BlastSocketValidateQoSPolicyParam(qosPolicyParams->v1.dscpBUpUDPv6)) {
      BLASTSOCKETLOG("Invalid value of v1.dscpBUpUDPv6: %d, "
                     "Can't apply to socket.",
                     qosPolicyParams->v1.dscpBUpUDPv6);
      return FALSE;
   }

   if (!BlastSocketValidateQoSPolicyParam(qosPolicyParams->v1.dscpBDownTCPv6)) {
      BLASTSOCKETLOG("Invalid value of v1.dscpBDownTCPv6: %d, "
                     "Can't apply to socket.",
                     qosPolicyParams->v1.dscpBDownTCPv6);
      return FALSE;
   }

   if (!BlastSocketValidateQoSPolicyParam(qosPolicyParams->v1.dscpBDownUDPv6)) {
      BLASTSOCKETLOG("Invalid value of v1.dscpBDownUDPv6: %d, "
                     "Can't apply to socket.",
                     qosPolicyParams->v1.dscpBDownUDPv6);
      return FALSE;
   }

   return TRUE;
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketGetAOutV4QoSPolicyParamsStr --
 *
 *      Build a string in the format name=value for AOut (Agent-Outgoing) IPv4
 *      members of qosPolicyParams.
 *
 * Results:
 *      Return string representation of QoS Policy Params
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

void
BlastSocketGetAOutV4QoSPolicyParamsStr(VvcQoSPolicyParams *qosPolicyParams, // IN
                                       char **aOutV4QoSPolicyParamsStr)     // OUT
{
   ASSERT(qosPolicyParams);

   *aOutV4QoSPolicyParamsStr =
      Str_Asprintf(NULL, "%s%d,%s%d", DSCP_AOUT_TCPV4_VALUE, qosPolicyParams->v1.dscpAOutTCPv4,
                   DSCP_AOUT_UDPV4_VALUE, qosPolicyParams->v1.dscpAOutUDPv4);
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketGetCOutV4QoSPolicyParamsStr --
 *
 *      Build a string in the format name=value for COut (Client-Outgoing) IPv4
 *      members of qosPolicyParams.
 *
 * Results:
 *      Return string representation of QoS Policy Params
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

void
BlastSocketGetCOutV4QoSPolicyParamsStr(VvcQoSPolicyParams *qosPolicyParams, // IN
                                       char **cOutV4QoSPolicyParamsStr)     // OUT
{
   ASSERT(qosPolicyParams);

   *cOutV4QoSPolicyParamsStr =
      Str_Asprintf(NULL, "%s%d,%s%d", DSCP_COUT_TCPV4_VALUE, qosPolicyParams->v1.dscpCOutTCPv4,
                   DSCP_COUT_UDPV4_VALUE, qosPolicyParams->v1.dscpCOutUDPv4);
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketGetBUpV4QoSPolicyParamsStr --
 *
 *      Build a string in the format name=value for BUp (BSG-Upstream) IPv4
 *      members of qosPolicyParams.
 *
 * Results:
 *      Return string representation of QoS Policy Params
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

void
BlastSocketGetBUpV4QoSPolicyParamsStr(VvcQoSPolicyParams *qosPolicyParams, // IN
                                      char **bUpV4QoSPolicyParamsStr)      // OUT
{
   ASSERT(qosPolicyParams);

   *bUpV4QoSPolicyParamsStr =
      Str_Asprintf(NULL, "%s%d,%s%d", DSCP_BUP_TCPV4_VALUE, qosPolicyParams->v1.dscpBUpTCPv4,
                   DSCP_BUP_UDPV4_VALUE, qosPolicyParams->v1.dscpBUpUDPv4);
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketGetBDownV4QoSPolicyParamsStr --
 *
 *      Build a string in the format name=value for BDown (BSG-Downstream) IPv4
 *      members of qosPolicyParams.
 *
 * Results:
 *      Return string representation of QoS Policy Params
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

void
BlastSocketGetBDownV4QoSPolicyParamsStr(VvcQoSPolicyParams *qosPolicyParams, // IN
                                        char **bDownV4QoSPolicyParamsStr)    // OUT
{
   ASSERT(qosPolicyParams);

   *bDownV4QoSPolicyParamsStr =
      Str_Asprintf(NULL, "%s%d,%s%d", DSCP_BDOWN_TCPV4_VALUE, qosPolicyParams->v1.dscpBDownTCPv4,
                   DSCP_BDOWN_UDPV4_VALUE, qosPolicyParams->v1.dscpBDownUDPv4);
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketGetAOutV6QoSPolicyParamsStr --
 *
 *      Build a string in the format name=value for AOut (Agent-Outgoing) IPv6
 *      members of qosPolicyParams.
 *
 * Results:
 *      Return string representation of QoS Policy Params
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

void
BlastSocketGetAOutV6QoSPolicyParamsStr(VvcQoSPolicyParams *qosPolicyParams, // IN
                                       char **aOutV6QoSPolicyParamsStr)     // OUT
{
   ASSERT(qosPolicyParams);

   *aOutV6QoSPolicyParamsStr =
      Str_Asprintf(NULL, "%s%d,%s%d", DSCP_AOUT_TCPV6_VALUE, qosPolicyParams->v1.dscpAOutTCPv6,
                   DSCP_AOUT_UDPV6_VALUE, qosPolicyParams->v1.dscpAOutUDPv6);
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketGetCOutV6QoSPolicyParamsStr --
 *
 *      Build a string in the format name=value for COut (Client-Outgoing) IPv6
 *      members of qosPolicyParams.
 *
 * Results:
 *      Return string representation of QoS Policy Params
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

void
BlastSocketGetCOutV6QoSPolicyParamsStr(VvcQoSPolicyParams *qosPolicyParams, // IN
                                       char **cOutV6QoSPolicyParamsStr)     // OUT
{
   ASSERT(qosPolicyParams);

   *cOutV6QoSPolicyParamsStr =
      Str_Asprintf(NULL, "%s%d,%s%d", DSCP_COUT_TCPV6_VALUE, qosPolicyParams->v1.dscpCOutTCPv6,
                   DSCP_COUT_UDPV6_VALUE, qosPolicyParams->v1.dscpCOutUDPv6);
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketGetMptVersionStr --
 *
 *      Build a string in the format name=value for Multi-protocol version
 *
 * Results:
 *      Return string representation of mptVersion
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

void
BlastSocketGetMptVersionStr(const uint8 mptVersion,   // IN
                            char **cOutMptVersionStr) // OUT
{
   *cOutMptVersionStr = Str_Asprintf(NULL, "%s%d", MPT_VERSION_VALUE, mptVersion);
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketGettcpBweVersionStr --
 *
 *      Build a string in the format name=value for TCP BWE version
 *
 * Results:
 *      Return string representation of tcpBweVersion
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

void
BlastSocketGetTcpBweVersionStr(const uint32 tcpBweVersion,  // IN
                               char **cOutTcpBweVersionStr) // OUT
{
   ASSERT(cOutTcpBweVersionStr);
   *cOutTcpBweVersionStr = Str_Asprintf(NULL, "%s%u", TCP_BWE_VERSION_VALUE, tcpBweVersion);
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketGetVvcDeferredAcksParamsStr --
 *
 *      Build a string in the format name=value for VvcDeferredAcksParams
 *
 * Results:
 *      Return string representation of mptVersion
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

void
BlastSocketGetVvcDeferredAcksParamsStr(VvcDeferredAcksParams *deferredAcksParams, // IN
                                       char **deferredAcksParamsStr)              // OUT

{
   ASSERT(deferredAcksParamsStr);

   *deferredAcksParamsStr =
      Str_Asprintf(NULL, "%s%s,%s%d,%s%d,%s%d", VVC_ENABLE_DEFERREDACKS_VALUE,
                   deferredAcksParams->enableDeferredAcks ? "1" : "0", VVC_MPT_ACKQUIETPERIOD_VALUE,
                   deferredAcksParams->mptAckQuietPeriod, VVC_MPT_ACKUNACKEDBYTES_VALUE,
                   deferredAcksParams->mptAckUnackedBytes, VVC_MPT_ACKSEQGAP_VALUE,
                   deferredAcksParams->mptAckSeqGap);
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketGetBUpV6QoSPolicyParamsStr --
 *
 *      Build a string in the format name=value for BUp (BSG-Upstream) IPv6
 *      members of qosPolicyParams.
 *
 * Results:
 *      Return string representation of QoS Policy Params
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

void
BlastSocketGetBUpV6QoSPolicyParamsStr(VvcQoSPolicyParams *qosPolicyParams, // IN
                                      char **bUpV6QoSPolicyParamsStr)      // OUT
{
   ASSERT(qosPolicyParams);

   *bUpV6QoSPolicyParamsStr =
      Str_Asprintf(NULL, "%s%d,%s%d", DSCP_BUP_TCPV6_VALUE, qosPolicyParams->v1.dscpBUpTCPv6,
                   DSCP_BUP_UDPV6_VALUE, qosPolicyParams->v1.dscpBUpUDPv6);
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketGetBDownV6QoSPolicyParamsStr --
 *
 *      Build a string in the format name=value for BDown (BSG-Downstream) IPv6
 *      members of qosPolicyParams.
 *
 * Results:
 *      Return string representation of QoS Policy Params
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

void
BlastSocketGetBDownV6QoSPolicyParamsStr(VvcQoSPolicyParams *qosPolicyParams, // IN
                                        char **bDownV6QoSPolicyParamsStr)    // OUT
{
   ASSERT(qosPolicyParams);

   *bDownV6QoSPolicyParamsStr =
      Str_Asprintf(NULL, "%s%d,%s%d", DSCP_BDOWN_TCPV6_VALUE, qosPolicyParams->v1.dscpBDownTCPv6,
                   DSCP_BDOWN_UDPV6_VALUE, qosPolicyParams->v1.dscpBDownUDPv6);
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocket_SetTransportSwitchPolicy --
 *
 *      Records the transport switch policy params applicable for a given
 *      session (identified by vAuth).
 *
 * Results:
 *      Returns TRUE if the transport switch policy params are recorded, FALSE
 *      otherwise.
 *
 *----------------------------------------------------------------------------
 */

Bool
BlastSocket_SetTransportSwitchPolicy(BlastSocketContext *blastSocketCtx,                   // IN
                                     const char *vAuth,                                    // IN
                                     const BlastSocketTransportSwitchPolicyParams *params) // IN
{
   return BlastSocketSetTransportSwitchPolicy(blastSocketCtx, vAuth, params);
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocket_SetVvcQoSPolicy --
 *
 *      Records the Vvc QoS policy params.
 *
 * Results:
 *      Returns TRUE if the transport QoS policy params are recorded, FALSE
 *      otherwise.
 *
 *----------------------------------------------------------------------------
 */

Bool
BlastSocket_SetVvcQoSPolicy(BlastSocketContext *blastSocketCtx, // IN
                            const char *vAuth,                  // IN
                            const VvcQoSPolicyParams *params)   // IN
{
   return BlastSocketSetVvcQoSPolicy(blastSocketCtx, vAuth, params);
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocket_SetVvcDeferredAcksParams --
 *
 *      Records the Vvc Deferred Acks Params.
 *
 * Results:
 *      Returns TRUE if params are recorded, FALSE otherwise.
 *
 *----------------------------------------------------------------------------
 */

Bool
BlastSocket_SetVvcDeferredAcksParams(BlastSocketContext *blastSocketCtx,  // IN
                                     const char *vAuth,                   // IN
                                     const VvcDeferredAcksParams *params) // IN
{
   return BlastSocketSetVvcDeferredAcksParams(blastSocketCtx, vAuth, params);
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketConnectWebsocketInt --
 *
 *      This is the API function that provides a simple wrapper around
 *      AsyncSocketConnectWebSocket API.
 *      If the caller provides the optional websocket upgrade callback
 *      parameters, then this API will use them to allow preparing UDP
 *      specific headers in the upgrade request and processing the
 *      websocket upgrade response.
 *
 * Results:
 *      Valid AsyncSocket on success, NULL on failure.
 *
 *----------------------------------------------------------------------------
 */

AsyncSocket *
BlastSocketConnectWebSocketInt(int tcpSocketFd,                                       // IN
                               const char *url,                                       // IN
                               const char *fqdn,                                      // IN
                               struct _SSLVerifyParam *sslVerifyParam,                // IN
                               const char *proxyStr,                                  // IN
                               const char *cookies,                                   // IN
                               const char *protocols[],                               // IN
                               BlastSocketPrepareWSUpgradeRequest prepareRequestFn,   // IN/OPT
                               BlastSocketProcessWSUpgradeResponse processResponseFn, // IN/OPT
                               AsyncSocketConnectFn connectFn,                        // IN
                               void *clientData,                                      // IN
                               AsyncSocketConnectFlags flags,                         // IN
                               AsyncSocketPollParams *pollParams,                     // IN
                               int *error)                                            // OUT
{
   AsyncSocket *asock = NULL;
   BlastSocketConnectContext *blastSocketConnectContext = NULL;
   AsyncWebSocketUpgradeRequestFn webSocketPrepareRequestFn = NULL;
   AsyncWebSocketUpgradeResponseFn webSocketProcessResponseFn = NULL;
   void *webSocketUpgradeClientData = NULL;
   AsyncWebSocketConnectArgs args;

   ASSERT(NULL != clientData);

   if (prepareRequestFn && processResponseFn) {
      blastSocketConnectContext = Util_SafeCalloc(1, sizeof *blastSocketConnectContext);

      blastSocketConnectContext->prepareWSUpgradeRequestFn = prepareRequestFn;
      blastSocketConnectContext->processWSUpgradeResponseFn = processResponseFn;
      blastSocketConnectContext->clientData = clientData;

      webSocketPrepareRequestFn = BlastSocketPrepareUpgradeRequest;
      webSocketProcessResponseFn = BlastSocketProcessUpgradeResponse;
      webSocketUpgradeClientData = blastSocketConnectContext;
   }

   BLASTSOCKETLOG("Using AsyncSocketConnectWebSocket with "
                  "tcpSocketFd:%d, fqdn:%s",
                  tcpSocketFd, fqdn);

   AsyncSocketInitWebSocketConnectArgs(&args);
   args.url = url;
   args.tcpSocketFd = tcpSocketFd;
   args.fqdn = fqdn;
   args.sslVerifyParam = sslVerifyParam;
   args.proxyStr = proxyStr;
   args.cookies = cookies;
   args.protocols = protocols;
   args.connectFn = connectFn;
   args.clientData = clientData;
   args.flags = flags;
   args.pollParams = pollParams;
   args.prepareFn = webSocketPrepareRequestFn;
   args.processFn = webSocketProcessResponseFn;
   args.upgradeCbData = webSocketUpgradeClientData;
   args.outError = error;
   asock = AsyncSocketConnectWebSocketHelper(&args);

   if (NULL == asock) {
      BLASTSOCKETLOG("AsyncSocketConnectWebSocket Failed !!");
   }

   return asock;
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocket_ConnectWebsocket --
 *
 *      Invokes BlastSocketConnectWebSocketInt which is a wrapper around
 *      AsyncSocketConnectWebSocket API.
 *
 * Results:
 *      Valid AsyncSocket on success, NULL on failure.
 *
 *----------------------------------------------------------------------------
 */

AsyncSocket *
BlastSocket_ConnectWebsocket(const char *url,                                       // IN
                             struct _SSLVerifyParam *sslVerifyParam,                // IN
                             const char *proxyStr,                                  // IN
                             const char *cookies,                                   // IN
                             const char *protocols[],                               // IN
                             BlastSocketPrepareWSUpgradeRequest prepareRequestFn,   // IN/OPT
                             BlastSocketProcessWSUpgradeResponse processResponseFn, // IN/OPT
                             AsyncSocketConnectFn connectFn,                        // IN
                             void *clientData,                                      // IN
                             AsyncSocketConnectFlags flags,                         // IN
                             AsyncSocketPollParams *pollParams,                     // IN
                             int *error)                                            // OUT
{
   return BlastSocketConnectWebSocketInt(-1, url, NULL, sslVerifyParam, proxyStr, cookies,
                                         protocols, prepareRequestFn, processResponseFn, connectFn,
                                         clientData, flags, pollParams, error);
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocket_ConnectWebsocketWithFd --
 *
 *      The "WithFd" version of BlastSocket_ConnectWebSocket.
 *      See comments from BlastSocket_ConnectWebsocket() for more details
 *
 * Results:
 *      Valid AsyncSocket on success, NULL on failure.
 *
 *----------------------------------------------------------------------------
 */

AsyncSocket *
BlastSocket_ConnectWebsocketWithFd(int tcpSocketFd,                                       // IN
                                   const char *url,                                       // IN
                                   const char *fqdn,                                      // IN
                                   struct _SSLVerifyParam *sslVerifyParam,                // IN
                                   const char *proxyStr,                                  // IN
                                   const char *cookies,                                   // IN
                                   const char *protocols[],                               // IN
                                   BlastSocketPrepareWSUpgradeRequest prepareRequestFn,   // IN/OPT
                                   BlastSocketProcessWSUpgradeResponse processResponseFn, // IN/OPT
                                   AsyncSocketConnectFn connectFn,                        // IN
                                   void *clientData,                                      // IN
                                   AsyncSocketConnectFlags flags,                         // IN
                                   AsyncSocketPollParams *pollParams,                     // IN
                                   int *error)                                            // OUT
{
   return BlastSocketConnectWebSocketInt(tcpSocketFd, url, fqdn, sslVerifyParam, proxyStr, cookies,
                                         protocols, prepareRequestFn, processResponseFn, connectFn,
                                         clientData, flags, pollParams, error);
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocket_StartReverseConnection --
 *
 *      This is an API function that starts connecting on Reverse Connection.
 *
 * Results:
 *      Valid AsyncSocket on success, NULL on failure.
 *
 *----------------------------------------------------------------------------
 */

int
BlastSocket_StartReverseConnection(BlastSocketContext *blastSocketCtx,   // IN
                                   const char *reverseHost,              // IN
                                   unsigned int reversePort,             // IN
                                   AsyncSocketConnectFn onConnectCb,     // IN
                                   AsyncSocketErrorFn errorFn,           // IN
                                   void *clientdata,                     // IN
                                   AsyncSocketConnectFlags connectFlags, // IN
                                   AsyncSocket **reverseAsock)           // OUT
{
   return BlastSocketStartReverseConnection(blastSocketCtx, reverseHost, reversePort, onConnectCb,
                                            errorFn, clientdata, connectFlags, reverseAsock);
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocket_UpgradeReverseConnection --
 *
 *      This is the API function that enables Reverse Connection to upgrade
 *      an established Reverse Connection to a websocket.
 *
 * Results:
 *      Valid AsyncSocket on success, NULL on failure.
 *
 *----------------------------------------------------------------------------
 */

AsyncSocket *
BlastSocket_UpgradeReverseConnection(AsyncSocket *asock,                 // IN
                                     const char *protocols[],            // IN
                                     BlastSocketContext *blastSocketCtx, // IN
                                     Bool useSSL,                        // IN
                                     void *sslCtx,                       // IN
                                     int *error)                         // OUT
{
   VERIFY(useSSL);

   BLASTSOCKETLOG("Upgrading Reverse Connection to websocket");

   // Disable VVC raw channels if reverse connection is enabled
   blastSocketCtx->params.disableVvcRawChannels = TRUE;

   return AsyncSocket_UpgradeToWebSocket(asock, protocols, BlastSocketWSConnectionAccepted,
                                         blastSocketCtx, useSSL, sslCtx,
                                         BlastSocketHandleUpgradeRequest, error);
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocket_Start --
 *
 *      This is the API function that Starts the Blast Socket module, with
 *      a websocket listen with full HTTP handshake.
 *
 * Results:
 *      Valid Context on sucesss, NULL context on failure.
 *
 *----------------------------------------------------------------------------
 */

BlastSocketContext *
BlastSocket_Start(void *sslCtx,                                   // IN
                  Bool useSSL,                                    // IN
                  const char *webSocketAddr,                      // IN
                  int *webSocketPort,                             // IN/OUT
                  int *udpFecPort,                                // IN/OUT
                  int numUdpFecPorts,                             // IN
                  const BlastSocketParams *blastSocketParams,     // IN
                  BlastSocketAcceptCb acceptFn,                   // IN
                  BlastSocketCloseCb socketCloseCb,               // IN
                  BlastSessionStartedCb sessionStartedCb,         // IN
                  BlastSessionInvalidatedCb sessionInvalidatedCb, // IN
                  void *clientData)                               // IN
{
   return BlastSocketStart(sslCtx, useSSL, webSocketAddr, webSocketPort, udpFecPort, numUdpFecPorts,
                           blastSocketParams, acceptFn, socketCloseCb, sessionStartedCb,
                           sessionInvalidatedCb, clientData);
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocket_Stop --
 *
 *      Public API to call into BlastSocketStop.
 *
 * Results:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

void
BlastSocket_Stop(BlastSocketContext *blastSocketContext) // IN
{
   MXUser_AcquireExclLock(gInitLock);
   if (!isBlastSocketsInitialized) {
      BLASTSOCKETLOG("Error: Called BlastSocket_Stop when already "
                     "uninitialized.");
      MXUser_ReleaseExclLock(gInitLock);
      return;
   }

   BlastSocketStop(blastSocketContext);

   isBlastSocketsInitialized = FALSE;
   MXUser_ReleaseExclLock(gInitLock);
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketStop --
 *
 *      Stops the Blast Socket module, and releases the BlastSocketContext
 *      object that we previously allocated. Must be holding the gInitLock
 *      to enter.
 *
 * Results:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

void
BlastSocketStop(BlastSocketContext *blastSocketContext) // IN
{
   ASSERT(blastSocketContext != NULL);
   ASSERT(MXUser_IsCurThreadHoldingExclLock(gInitLock));

   /*
    * Clear SessionMap so that we call BlastSocket_StopVvcSession on
    * any existing sessions.
    */
   BlastSocketClearSessionMap(blastSocketContext);

   // Clear out the callback function pointers.
   blastSocketContext->callbacks.acceptFn = NULL;
   BLASTSOCKETLOG("Setting the Blastsocket callbacks data to null");
   blastSocketContext->callbacks.cbFuncClientData = NULL;

   /*
    * VVC needs to shutdown before the network poll loop is stopped.
    *
    * Vvclib is given n/w poll lib as its deferred procedure call backend, hence
    * it needs to stop using poll lib before it exits. Also vvclib needs a
    * chance to send a VVC_Stop to all the loaded extensions before the
    * MainThread exits.  If extensions are not instructed to stop then those
    * extensions can have threads which are not instructed to stop which will
    * prevent Blast Worker from exiting.
    */
   BlastSocketUninitVvc(blastSocketContext);

   if (BlastSocket_IsBlastSocketThreadEnabled(blastSocketContext)) {
      BlastSocketThread_Exit();
   }

   /*
    * Destroy WSPeerConfigMap after BlastSocket thread exits.
    */
   BlastSocketClearWSPeerConfigMap(blastSocketContext);

   // Release authentication resources.
   BlastSocketUninitAuthMgr(blastSocketContext);

   /*
    * "blastSocketContext" is given as clientData to VVC.
    * Destroy it only after the VVCLIB_Uninit() - which stops the Vvc's Event
    * Dispatch thread.
    */

   BlastSocketDestroySessionMap(blastSocketContext);

   FECAsyncSocket_Exit();
   BlastSocketLock_Exit();

   free(blastSocketContext);
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketGetNegotiatedMptVersion --
 *
 *      Get negotiated Mpt version number based on local and peer received
 *      values. Negotiated MptVersion is lowest of the two values or equal.
 *
 * Results:
 *      Negotiated MptVersion.
 *
 *----------------------------------------------------------------------------
 */

int
BlastSocketGetNegotiatedMptVersion(uint8 localMptVersion, // IN
                                   uint8 peerMptVersion)  // IN
{
   if (localMptVersion > peerMptVersion) {
      return peerMptVersion;
   } else {
      return localMptVersion;
   }
}


/*
 *----------------------------------------------------------------------------
 *
 * BlastSocketGetNegotiatedTcpBweVersion --
 *
 *      Get negotiated tcp BWE version number based on local and peer received
 *      values. Negotiated tcpBweVersion is lower of the two values or equal.
 *
 * Results:
 *      Negotiated tcpBweVersion.
 *
 *----------------------------------------------------------------------------
 */

int
BlastSocketGetNegotiatedTcpBweVersion(uint32 localTcpBweVersion, // IN
                                      uint32 peerTcpBweVersion)  // IN
{
   if (localTcpBweVersion > peerTcpBweVersion) {
      return peerTcpBweVersion;
   } else {
      return localTcpBweVersion;
   }
}
