/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/* global require: false, describe: false, beforeEach: false*/

"use strict";

var blastSocket = require('./library');
var parallel = require('mocha.parallel');
var _ = require('underscore');
const launchTopology = require('./launchTopology').launchTopology;
const cleanupTopology = require('./launchTopology').cleanupTopology;
const request = require('request');
const { waitUntilListening } = require('./utils');

var dashedLine = '--------------------------------------------------------';


/*
 * Start NodeServer on localhost and returns default options.
 * Default options are taken from runNodeServer and library modules.
 */
function getDefaultOpts() {
   // runNodeServer starts the nodeServer on localhost
   // and returns default options that we're interested in.
   const runNodeServer = require('./runNodeServer');
   let memLeakCheck = runNodeServer.nodeServerOptions.doMemLeakCheck;
   let removeAllContainers = runNodeServer.nodeServerOptions.removeAllContainers;
   if (topologyParams.doMemLeakCheck !== undefined) {
      memLeakCheck = topologyParams.doMemLeakCheck;
   }
   const retOpts = {
      agentBlastTcpPort: parseInt(nodes[agentIndex].blastTcpPort),
      clientBlastTcpPort: parseInt(nodes[clientIndex].blastTcpPort),
      agentBlastUdpPort: parseInt(nodes[agentIndex].blastUdpPort),
      clientBlastUdpPort: parseInt(nodes[clientIndex].blastUdpPort),
      agentRawBeatPort: parseInt(nodes[agentIndex].rawBeatPort),
      doMemLeakCheck: memLeakCheck,
      // Connect error codes are only thrown on windows clients
      doConnectErrorCheck: clientPlatform === 'win32',
      bandwidthCap: [0],
      wireBandwidthCap: [0],
      vvcAPITestPath: runNodeServer.nodeServerOptions.vvcAPITestPath,
      overwriteVmwareVvcDll: runNodeServer.nodeServerOptions.overwriteVmwareVvcDll,
      removeAllContainers: removeAllContainers,
      // Even when not connecting with SSL, the BSG requires the SSL Library
      alwaysLoadSSLLib: testOptions.bsg.enabled,
      branding: runNodeServer.nodeServerOptions.branding,
      enableRawChannel: runNodeServer.nodeServerOptions.enableRawChannel
   };
   if (retOpts.enableRawChannel) {
      retOpts.useSSL = true;
      retOpts.certFilePathName = require('process').cwd() +
                                             '/sslcrlfiles/BenevSslCert.pfx';
      retOpts.checkRevocation = false;
   }
   return retOpts;
}


/*
 * Get default options for Vvc API tests (in-proc and out-of-proc).
 * Don't run a memory leak check with Vvc API tests since they're currently
 * not set up to clean themselves up properly at the end.
 */
function getDefaultVvcAPITestOpts() {
   let options = getDefaultOpts();
   options.blastProtocol = 'BENIT';
   options.doMemLeakCheck = false;
   options.vvcAPIPort = 50132;
   return options;
}


/*
 * Get all the traffic patterns that match the tag from the file.
 * If no tag is provided, return all the patterns from the file.
 */
function getTrafficPatterns(fileName, tag) {
   let allPatterns = JSON.parse(fs.readFileSync(fileName)).tests;
   var taggedPatterns = [];

   if (tag === null) {
      return allPatterns;
   }

   _.each(allPatterns, (pattern) => {
      if (pattern.tags.indexOf(tag) > -1) {
         taggedPatterns.push(pattern);
      }
   });

   return taggedPatterns;
}

// Using options from topology file if it exists and is valid.
// If not, using default options.
const path = require('path');
const fs = require('fs');
const { expect } = require('chai');
const argv = require('optimist').argv;
const topologyFilePath = argv.topology;
var testOptions = {};
var topologyParams = {};

if (fs.existsSync(topologyFilePath)) {
   console.log("Topology file found.");
   var topology = JSON.parse(fs.readFileSync(topologyFilePath));
} else {
   console.log('No topology file found. Using defaults!');
   var topology = {
      "nodes": [
         {
            "type": "client",
            "name": "host-client",
            "platform": "host",
            "nsPort": "3000",
            "blastTcpPort": "53000",
            "connectTo": "host-agent"
         },
         {
            "type": "agent",
            "name": "host-agent",
            "platform": "host",
            "nsPort": "3000",
            "blastTcpPort": "53000",
            "rawBeatPort": "53100"
         }
      ],
      "params": {
         "doMemLeakCheck": true,
         "tags": [
            "standard"
         ]
      }
   };
}

var nodes = topology['nodes'] === undefined ? [] : topology['nodes'];
var agentIndex = -1;
var clientIndex = -1
var bsgIndex = -1;

nodes.forEach(function (item, index, array) {
   if (item.type === 'client') {
      clientIndex = index;
   } else if (item.type === 'agent') {
      agentIndex = index;
   } else if (item.type === 'bsg') {
      bsgIndex = index;
   } else if (item.type !== 'ncs') {
      console.log(`Unknown topology item type: ${item.type}`);
   }
});

if (agentIndex < 0 || clientIndex < 0) {
   console.log("Invalid topology file. Agent/Client details missing. Using defaults!");
} else {
   topologyParams = topology['params'];
   var agentPlatform = nodes[agentIndex].platform;
   var clientPlatform = nodes[clientIndex].platform;

   if (agentPlatform === 'host') {
      agentPlatform = process.platform;
   }

   if (clientPlatform === 'host') {
      clientPlatform = process.platform;
   }

   // Make UDP ports default to TCP ports if not specified
   if (nodes[agentIndex].blastUdpPort === undefined) {
      nodes[agentIndex].blastUdpPort = nodes[agentIndex].blastTcpPort;
   }

   if (nodes[clientIndex].blastUdpPort === undefined) {
      nodes[clientIndex].blastUdpPort = nodes[clientIndex].blastTcpPort;
   }

   testOptions = {
      agent: {
         blastTcpPort: nodes[agentIndex].blastTcpPort,
         blastUdpPort: nodes[agentIndex].blastUdpPort,
         rawBeatPort: nodes[agentIndex].rawBeatPort
      },
      client: {
         blastTcpPort: nodes[clientIndex].blastTcpPort,
         blastUdpPort: nodes[clientIndex].blastUdpPort
      },
      bsg: {
         enabled: bsgIndex !== -1
      }
   }

   blastSocket.initializePortsAndIpValues(testOptions);
}

/*
 * Returns a promise for when all the node servers are alive
 * nodeInfos is an array of the node infos returned by launchTopology
 */
function waitUntilAllNodeServersAreAlive(nodeInfos) {
   // Array of tuples [ip, port]
   let uniqueIpAndPortList = [];

   // Find all the node servers by looping through the node infos
   // Only look for unique node server ips and ports
   for (const nodeInfo of nodeInfos) {
      // Does this node have a node server
      if (nodeInfo.nsIp) {
         const ip = nodeInfo.nsIp;
         const port = nodeInfo.nsPort;

         // Is this a new node server
         const isUnique = uniqueIpAndPortList.find((element) =>
               element[0] === ip && element[1] === port) === undefined;

         // If this is a new node server, keep track of it
         if (isUnique) {
            uniqueIpAndPortList.push([ip, port]);
         }
      }
   }

   // This means that there are no node servers
   // If there are no node servers, then we cannot run tests
   expect(uniqueIpAndPortList.length).to.be.above(0);

   let listeningProms = uniqueIpAndPortList.map(([ip, port]) => {
      // Wait for the node server to become alive
      return waitUntilListening(`http://${ip}:${port}/alive/`, 2 * 60000, 500);
   });

   return Promise.all(listeningProms);
}

/*
 * Before All Tests:
 *    Run setup before all tests:
 *       Setup the topology
 */
before('Test Setup', function (done) {
   this.timeout(3 * 60 * 1000);

   new Promise((res) => {
      console.log("Launching topology");
      launchTopology(topology, getDefaultOpts().removeAllContainers).then((nodeInfos) => {
         testOptions.agent = JSON.parse(JSON.stringify(nodeInfos[agentIndex]));
         testOptions.client = JSON.parse(JSON.stringify(nodeInfos[clientIndex]));

         if (bsgIndex >= 0) {
            testOptions.bsg = JSON.parse(JSON.stringify(nodeInfos[bsgIndex]));
            testOptions.bsg.enabled = true;
         }

         blastSocket.initializePortsAndIpValues(testOptions);

         console.log('Waiting for node servers to become alive');
         waitUntilAllNodeServersAreAlive(nodeInfos).then(() => {
            console.log('Starting tests now.');
            res();
         }).catch(() => {
            expect.fail('Node servers never became alive');
         });
      });
   }).then(() => {
      console.log("Setting client configs");
      return blastSocket.primeConfig('client', getDefaultOpts());
   }).then(async () => {
      // Wait before setting server config.
      await new Promise((res) => setTimeout(res, 100));
      console.log("Setting server configs");
      return blastSocket.primeConfig('server', getDefaultOpts());
   }).then(done);

   console.log(dashedLine);
});


/*
 * Before Each Test:
 *    Print test name.
 *
 */
beforeEach('Test Start', function (done) {
   console.log(`------ Test enter: "${this.currentTest.parent.title}: ${this.currentTest.title}"\n`);
   done();
});

/*
 * After All Tests:
 *    Run cleanup after all tests:
 *       Remove containers if created.
 *
 * Called after failed tests too.
 */
after('Cleanup', function (done) {
   this.timeout(30 * 1000);
   Promise.all([blastSocket.clearConfig('client'), blastSocket.clearConfig('server')]).then(() => {
      console.log("Cleaning up docker containers.");
      cleanupTopology().then(() => {
         console.log('Exiting now.');
         done();
      });
   });
});


/*
 * After Each Test:
 *    Run cleanup after each test:
 *       Create visual space between tests.
 *
 * Called after failed tests too.
 */
afterEach('Test Cleanup', function (done) {
   this.timeout(2.5 * 1000)
   console.log(`\n------ Test exit: "${this.currentTest.parent.title}: ${this.currentTest.title}"`);
   console.log(dashedLine);
   // Add a 2 second delay between tests to allow teardown to finish
   setTimeout(done, 2000);
});


/*
 *-----------------------------------------------------------------------------
 * Client Initiated Disconnect:
 *
 *    Testing blastSocket when established connection is closed by client.
 *
 * Transport Protocols:
 *    [BENIT, TCP, BEAT]
 *
 *-----------------------------------------------------------------------------
 */

describe('Client Initiated Disconnect', function () {
   // Make sure the topology has the standard 1 agent-client pair setup (with an optional ncs)
   if (!topologyParams.tags.includes('standard')) {
      console.log(`Skipping Test: ${this.title} (Non-Standard Topology)`);
      return;
   }

   const protocols = ['TCP', 'BEAT', 'BENIT'];

   let options = getDefaultOpts();

   _.each(protocols, (proto) => {
      options.blastProtocol = proto;
      blastSocket.testClientInitiatedDisconnect(options);
   });
});

/*
 *-----------------------------------------------------------------------------
 * WAN Network Profile test
 *
 *    Testing data transfer integrity and collecting measurements over
 *    different WAN network profiles.
 *
 *-----------------------------------------------------------------------------
 */


describe('[MX] WAN Network Profile Tests', function () {
   // Make sure the topology has the standard 1 agent-client pair setup (with an optional ncs)
   if (!topologyParams.tags.includes('standard')) {
      console.log(`Skipping Test: ${this.title} (Non-Standard Topology)`);
      return;
   }

   const protocols = ['TCP', 'BEAT', 'BENIT'];
   let netProfiles = ['1244000 0 0 0'];//"BW(Bps) PacketLoss(%) RTT/Delay(ms) DelayJitter(ms)"
   let options = getDefaultOpts();

   options.doMemLeakCheck = false;
   options.netProfiles = netProfiles;
   options.openChanPeer = 'client';
   options.channelFlag = 0x0;
   options.wanIp = "**************";

   fs.unlink('ackCumulative.txt', (err) => {
      console.log("Deleting ackCumulative.txt... ", err);
   });

   _.each(protocols, (proto) => {
      options.blastProtocol = proto;
      blastSocket.testWAN(options);
   });
});



/*
 *-----------------------------------------------------------------------------
 * Server Initiated Disconnect:
 *
 *    Testing blastSocket when established connection is closed by server.
 *
 * Transport Protocols:
 *    [BENIT, TCP, BEAT]
 *
 *-----------------------------------------------------------------------------
 */

describe('Server Initiated Disconnect', function () {
   // Make sure the topology has the standard 1 agent-client pair setup (with an optional ncs)
   if (!topologyParams.tags.includes('standard')) {
      console.log(`Skipping Test: ${this.title} (Non-Standard Topology)`);
      return;
   }

   const protocols = ['TCP', 'BEAT', 'BENIT'];
   let options = getDefaultOpts();

   _.each(protocols, (proto) => {
      options.blastProtocol = proto;
      blastSocket.testServerInitiatedDisconnect(options);
   });
});


/*
 *-----------------------------------------------------------------------------
 * Disconnect From Server & Client At Same Time:
 *
 *    Testing blastSocket when both client and server try to close connection at
 *    same time when BENIT is used
 *
 * Transport Protocol:
 *       BENIT
 *
 *
 *-----------------------------------------------------------------------------
 */

describe('Disconnect from server and client at the same time', function () {
   // Make sure the topology has the standard 1 agent-client pair setup (with an optional ncs)
   if (!topologyParams.tags.includes('standard')) {
      console.log(`Skipping Test: ${this.title} (Non-Standard Topology)`);
      return;
   }

   const protocols = ['TCP', 'BEAT', 'BENIT'];
   let options = getDefaultOpts();

   _.each(protocols, (proto) => {
      options.blastProtocol = proto;
      blastSocket.testDisconnectFromClientAndServerAtSameTime(options);
   });
});


/*
 *-----------------------------------------------------------------------------
 * Connect using custom UDP port:
 *
 *    A connect-disconnect test that ensures that the client was able to
 *    connect using a requested UDP port.
 *    This test uses a bandwidth capping proxy between the client and agent to
 *    differentiate between cases where the client just took the server's UDP
 *    port (failure) and cases where the client correctly used the requested
 *    port and connected to the bandwidth capping proxy.
 *
 * Transport Protocol:
 *       BEAT
 *
 *
 *-----------------------------------------------------------------------------
 */

describe('Custom UDP Port', function () {
   // Make sure the topology has the standard 1 agent-client pair setup (with an optional ncs)
   if (!topologyParams.tags.includes('standard')) {
      console.log(`Skipping Test: ${this.title} (Non-Standard Topology)`);
      return;
   }

   let options = getDefaultOpts();
   // Increment the agent's blast ports to accommodate a bandwidth capper middlebox
   options.agentBlastTcpPort++;
   options.agentBlastUdpPort++;

   options.blastProtocol = 'BEAT';


   blastSocket.testCustomUdpPort(options);
});


/*
 *-----------------------------------------------------------------------------
 * Run the Vvc API tests for in-proc features
 *
 *    All Vvc API tests should pass.
 *
 *-----------------------------------------------------------------------------
 */

describe('In-proc Vvc API tests', function () {
   // The Vvc tests can only run on windows
   if (agentPlatform !== 'win32' || clientPlatform !== 'win32') {
      console.log(`Skipping Test: ${this.title} (Non-Windows Agent and/or Client)`);
      return;
   }

   // Make sure the topology has the standard 1 agent-client pair setup (with an optional ncs)
   if (!topologyParams.tags.includes('standard')) {
      console.log(`Skipping Test: ${this.title} (Non-Standard Topology)`);
      return;
   }

   let options = getDefaultVvcAPITestOpts();
   options.vvcAPITestsToRun = 'VVC_TEST_CHANNEL*/*P0';
   options.doOutOfProc = false;

   blastSocket.testVvcAPIs(options);
});


/*
 *-----------------------------------------------------------------------------
 * Run the Vvc API tests for out-of-proc features (Channel Test Cases)
 *
 *    All Vvc API channel tests should pass.
 *
 *-----------------------------------------------------------------------------
 */

describe('Out-of-proc Vvc API tests for channel', function () {
   // The Vvc tests can only run on windows
   if (agentPlatform !== 'win32' || clientPlatform !== 'win32') {
      console.log(`Skipping Test: ${this.title} (Non-Windows Agent and/or Client)`);
      return;
   }

   // Make sure the topology has the standard 1 agent-client pair setup (with an optional ncs)
   if (!topologyParams.tags.includes('standard')) {
      console.log(`Skipping Test: ${this.title} (Non-Standard Topology)`);
      return;
   }

   let options = getDefaultVvcAPITestOpts();
   options.vvcAPITestsToRun = 'VVC_TEST_CHANNEL*/*P0';
   options.doOutOfProc = true;

   blastSocket.testVvcAPIs(options);
});


/*
 *-----------------------------------------------------------------------------
 * Run the Vvc API tests for out-of-proc features (Listener Test Cases)
 *
 *    All Vvc API listener tests should pass.
 *
 *-----------------------------------------------------------------------------
 */

describe('Out-of-proc Vvc API tests for listener', function () {
   // The Vvc tests can only run on windows
   if (agentPlatform !== 'win32' || clientPlatform !== 'win32') {
      console.log(`Skipping Test: ${this.title} (Non-Windows Agent and/or Client)`);
      return;
   }

   // Make sure the topology has the standard 1 agent-client pair setup (with an optional ncs)
   if (!topologyParams.tags.includes('standard')) {
      console.log(`Skipping Test: ${this.title} (Non-Standard Topology)`);
      return;
   }

   let options = getDefaultVvcAPITestOpts();
   options.vvcAPITestsToRun = 'VVC_TEST_LISTENER*/*P0';
   options.doOutOfProc = true;

   blastSocket.testVvcAPIs(options);
});


/*
 *-----------------------------------------------------------------------------
 * Run Out-of-proc Vvc API test for Allow Channel List feature
 *
 *   Test should succeed in opening a channel because its name has been
 *   added to the registry allow list.
 *
 *-----------------------------------------------------------------------------
 */

describe('Out-of-proc Allow Channel List test - Allow', function () {
   // The Vvc tests can only run on windows
   if (agentPlatform !== 'win32' || clientPlatform !== 'win32') {
      console.log(`Skipping Test: ${this.title} (Non-Windows Agent and/or Client)`);
      return;
   }

   // Make sure the topology has the standard 1 agent-client pair setup (with an optional ncs)
   if (!topologyParams.tags.includes('standard')) {
      console.log(`Skipping Test: ${this.title} (Non-Standard Topology)`);
      return;
   }

   let options = getDefaultVvcAPITestOpts();
   options.vvcAPITestsToRun = 'VVC_TEST_CHANNEL*/Positive_ServerOpenChannelAllowList';
   options.doOutOfProc = true;
   options.doAllowChannelList = true;
   // Test case when channel is allowed
   options.allowChannel = true;
   blastSocket.testVvcAPIs(options);
});


/*
 *-----------------------------------------------------------------------------
 * Run Out-of-proc Vvc API test for Allow Channel List feature
 *
 *   Test should succeed in fail to open a channel because test case
 *   has changed the allowed channel name in registry.
 *
 *-----------------------------------------------------------------------------
 */

describe('Out-of-proc Allow Channel List test - Disallow', function () {
   // The Vvc tests can only run on windows
   if (agentPlatform !== 'win32' || clientPlatform !== 'win32') {
      console.log(`Skipping Test: ${this.title} (Non-Windows Agent and/or Client)`);
      return;
   }

   // Make sure the topology has the standard 1 agent-client pair setup (with an optional ncs)
   if (!topologyParams.tags.includes('standard')) {
      console.log(`Skipping Test: ${this.title} (Non-Standard Topology)`);
      return;
   }

   let options = getDefaultVvcAPITestOpts();
   options.vvcAPITestsToRun = 'VVC_TEST_CHANNEL*/Negative_ServerOpenChannelAllowList';
   options.doOutOfProc = true;
   options.doAllowChannelList = true;
   // Test case when channel is NOT allowed
   options.allowChannel = false;
   blastSocket.testVvcAPIs(options);
});


/*
 *-----------------------------------------------------------------------------
 * Traffic Generator test
 *
 *    Make sure data transferred over multiple proxies maintains integrity.
 *
 *-----------------------------------------------------------------------------
 */

describe('Traffic Generator Tests', function () {
   // Make sure the topology has the standard 1 agent-client pair setup (with an optional ncs)
   if (!topologyParams.tags.includes('standard')) {
      console.log(`Skipping Test: ${this.title} (Non-Standard Topology)`);
      return;
   }

   let trafficParams = getTrafficPatterns('traffic.json', "data transfer");
   let options = getDefaultOpts();
   _.each(trafficParams, (test) => {
      describe('Test with ' + test.description, function() {
         this.timeout(test.timeoutSec * 1000);

         options.patternDescription = test.description;
         options.trafficPatterns = test.patterns;
         // Generate test cases based on the linear combination of these options
         options.transportModes = ['TCP', 'BEAT', 'BENIT'];
         // Who opens the proxy channel?
         options.openChanPeers = ['client', 'server'];
         // VVC OpenChan channelFlags param. e.g.g 0x800 means DECLINE_NC
         options.channelFlags = [0x0, 0x800];

         blastSocket.trafficGeneratorTests(options);
      });
   });
});


/*
 *-----------------------------------------------------------------------------
 * Reconnect Count in Network Recovery:
 *
 *    Reconnection after losing connection with NC disabled should increment
 *    the reconnect counter, reconnect happens by Network Recovery.
 *
 *-----------------------------------------------------------------------------
 */

describe('Reconnect Count in Network Recovery', function() {
   // Make sure the topology has the standard 1 agent-client pair setup (with an optional ncs)
   if (!topologyParams.tags.includes('standard')) {
      console.log(`Skipping Test: ${this.title} (Non-Standard Topology)`);
      return;
   }

   const protocols = ['TCP', 'BEAT', 'BENIT'];
   let options = getDefaultOpts();
   // Increment the agent's blast ports to accommodate a bandwidth capper middlebox
   options.agentBlastTcpPort++;
   options.agentBlastUdpPort++;
   options.numDisconnects = 3;

   _.each(protocols, (proto) => {
      options.blastProtocol = proto;
      blastSocket.testReconnectCountNR(options);
   });
});


/*
 *-----------------------------------------------------------------------------
 * Network Continuity.
 *
 *    Losing connection during a transfer should allow for reconnection and the
 *    completion of the transfer
 *
 *-----------------------------------------------------------------------------
 */

describe('Network Continuity', function () {
   // The network continuity test isn't stable on linux
   // TODO: Make the test stable on linux
   if (agentPlatform === 'linux' || clientPlatform === 'linux') {
      console.log(`Skipping Test: ${this.title} (Linux Agent and/or Client)`);
      return;
   }

   // Make sure the topology has the standard 1 agent-client pair setup (with an optional ncs)
   if (!topologyParams.tags.includes('standard')) {
      console.log(`Skipping Test: ${this.title} (Non-Standard Topology)`);
      return;
   }

   let options = getDefaultOpts();
   // Increment the agent's blast ports to accommodate a bandwidth capper middlebox
   options.agentBlastTcpPort++;
   options.agentBlastUdpPort++;
   // Generate test cases based on the linear combination of these options
   const testCaseSpec = {
      pattern: 'Network Continuity',
      transportModes: ['TCP', 'BEAT', 'BENIT'],
      // Who opens the proxy channel?
      openChanPeers: ['client', 'server'],
      // VVC OpenChan channelFlags param.
      channelFlags: [0x0],
      // How long is network dropped for?
      networkDropTimeMs: [5000]
   };
   // Temp: temporarily disable BEAT/BENIT due to failures
   testCaseSpec.transportModes = ['TCP'];

   blastSocket.testNetworkContinuity(options, testCaseSpec);
});


/*
 *-----------------------------------------------------------------------------
 * Sending Bandwidth Cap.
 *
 *    If we set Benev.maxBandwidthKbps to a non-0 value, we expect the average
 *    bandwidth during a data transfer to be <= (2 * maxBandwidthKbps).
 *
 *-----------------------------------------------------------------------------
 */

describe('Sending Bandwidth Cap', function () {
   // Make sure the topology has the standard 1 agent-client pair setup (with an optional ncs)
   if (!topologyParams.tags.includes('standard')) {
      console.log(`Skipping Test: ${this.title} (Non-Standard Topology)`);
      return;
   }

   let patterns = getTrafficPatterns('traffic.json', "bandwidth cap");

   _.each(patterns, (test) => {
      let options = getDefaultOpts();
      options.patternDescription = test.description;
      // Generate test cases based on the linear combination of these options
      options.trafficPatterns = test.patterns;
      options.transportModes = ['TCP', 'BEAT', 'BENIT'];
      // Who opens the proxy channel and sends data? Only use server since
      // the client doesn't apply a send bandwidth cap
      options.openChanPeers = ['server'];
      // VVC OpenChan channelFlags param
      options.channelFlags = [0x800];
      // Send bandwidth cap in kilobits per second
      options.bandwidthCap = [500, 1000, 2000];

      blastSocket.testBandwidthCap(options);
   });
});


/*
 *-----------------------------------------------------------------------------
 * Minimum of Two Bandwidth Caps.
 *
 *    If we set Benev.maxBandwidthKbps to a non-0 value, and the wire bandwidth
 *    to a non -0 value, we expect the average bandwidth during a data transfer
 *    to be minimum of the two bandwidth caps.
 *
 *-----------------------------------------------------------------------------
 */

 describe('Minimum of Two Bandwidth Caps', function () {
   // Make sure the topology has the standard 1 agent-client pair setup (with an optional ncs)
   if (!topologyParams.tags.includes('standard')) {
      console.log(`Skipping Test: ${this.title} (Non-Standard Topology)`);
      return;
   }

   let patterns = getTrafficPatterns('traffic.json', "bandwidth cap");

   _.each(patterns, (test) => {
      let options = getDefaultOpts();
      // Increment the agent's blast ports to accommodate a bandwidth capper middlebox
      options.agentBlastTcpPort++;
      options.agentBlastUdpPort++;
      //Set the wire bandwidth cap in kbps
      options.wireBandwidthCap = ['700k'];
      //Currently no linear combination is tested
      options.trafficPatterns = test.patterns;
      //mode could be made to randomly choosen
      options.transportModes = ['BENIT'];
      //Only server can sets the bandwidth
      options.openChanPeers = ['server'];
      //VVC OpenChan channelFlags param
      options.channelFlags = [0x800];
      //Set the bandwidth cap in kbps
      options.bandwidthCap = [500, 2000];

      blastSocket.testWireBandwidthCap(options);
   });
});


/*
 *-----------------------------------------------------------------------------
 * Compare protocol speeds under various network conditions.
 *
 *-----------------------------------------------------------------------------
 */
describe('[Unstable] Test protocol speeds with various network conditions', function() {
   // Make sure the topology has the standard 1 agent-client par setup (with an optional ncs)
   if (!topologyParams.tags.includes('standard')) {
      console.log(`Skipping Test: ${this.title} (Non-Standard Topology)`);
      return;
   }

   const testCases = [
      {
         networkConditionTag: 'good',
         expectedBest: 'BENIT',
         expectedWorst: 'BEAT'
      },
      {
         networkConditionTag: 'lossy',
         expectedBest: 'BENIT',
         expectedWorst: 'TCP'
      },
      {
         networkConditionTag: 'lossy',
         expectedBest: 'BEAT',
         expectedWorst: 'TCP'
      }
   ];

   for (const testCase of testCases) {
      // Make sure the topology has the test case's network condition
      if (!topologyParams.tags.includes(testCase.networkConditionTag)) {
         continue;
      }

      let options = getDefaultOpts();
      options.expectedBest = testCase.expectedBest;

      const testCaseSpec = {
         pattern: `${testCase.expectedBest} vs. ${testCase.expectedWorst} - ` +
            `${testCase.networkConditionTag} network`,
         transportModes: [testCase.expectedBest, testCase.expectedWorst],
         // Who opens the proxy channel?
         openChanPeers: ['client', 'server'],
         // VVC OpenChan channelFlags param. e.g.g 0x800 means DECLINE_NC
         channelFlags: [0x0, 0x800]
      }

      blastSocket.compareProtocols(options, testCaseSpec);
   }
});


/*
 *-----------------------------------------------------------------------------
 * Test failed connections and ensure they throw the correct error code
 *
 *    These also test for bug 2292670 where the client hangs/crashes when
 *    failing to connect to the agent.
 *-----------------------------------------------------------------------------
 */
 describe('Test failed connections', function() {
   // Make sure the topology has the standard 1 agent-client par setup (with an optional ncs)
   if (!topologyParams.tags.includes('standard')) {
      console.log(`Skipping Test: ${this.title} (Non-Standard Topology)`);
      return;
   }

   // If the topology has a BSG, it changes the connection error codes
   // TODO: add tests specifically for BSG connection error codes
   if (testOptions.bsg.enabled) {
      console.log(`Skipping Test: ${this.title} (Topology includes BSG)`);
      return;
   }

   const testCases = [
      {
         // The .invalid insures that this hostname will not be resolvable
         ip: 'badhost.invalid',
         expectedError: 'VDPCONNECT_HOSTNAME_NOT_RESOLVABLE',
         timeoutSeconds: 20
      },
      {
         // This is an unused ip address reserved for future use
         ip: '240.0.0.0',
         expectedError: 'VDPCONNECT_NETWORK_UNREACHABLE',
         timeoutSeconds: 20
      },
      {
         // This is a reserved ip address that was formerly used for ipv6 to ipv4 relay
         ip: '***********',
         expectedError: 'VDPCONNECT_CONN_TIMEDOUT',
         timeoutSeconds: 80
      },
      {
         // There should be nothing listening on the port used to connect
         ip: '127.0.0.1',
         expectedError: 'VDPCONNECT_CONN_REFUSED',
         timeoutSeconds: 20
      }
   ];

   for (const testCase of testCases) {
      let options = getDefaultOpts();
      options.clientTargetIp = testCase.ip;
      options.expectedError = testCase.expectedError;
      options.timeoutSeconds = testCase.timeoutSeconds;

      if (options.doConnectErrorCheck) {
         blastSocket.testFailedConnection(options);
      }
   }
});


/*
 *-----------------------------------------------------------------------------
 *
 * CRL Cache Management for Certificate Revocation Check
 *
 *-----------------------------------------------------------------------------
 */

describe('CRL Cache Management', function () {
   // CRL Cache Management can only run on Windows
   if (agentPlatform !== 'win32' || clientPlatform !== 'win32') {
      console.log(`Skipping Test: ${this.title} (Non-Windows Agent and/or Client)`);
      return;
   }

   // Make sure the topology has the standard 1 agent-client pair setup (with an optional ncs)
   if (!topologyParams.tags.includes('standard')) {
      console.log(`Skipping Test: ${this.title} (Non-Standard Topology)`);
      return;
   }

   // The current BSG setup does not support an SSL connection
   if (topologyParams.tags.includes('bsg')) {
      console.log(`Skipping Test: ${this.title} (No support for BSG with SSL)`);
      return;
   }

   const testCases = [
      {
         // Test case without CRL cache
         enableCRLCache: false
      },
      {
         // Test case with CRL cache
         enableCRLCache: true
      }
   ];

   for (const testCase of testCases) {
      let options = getDefaultOpts();
      options.enableCRLCache = testCase.enableCRLCache;
      blastSocket.testCRLCache(options);
   }
});


/*
 *-----------------------------------------------------------------------------
 *
 * Test if SNI is set correctly at client and received by server when using SSL
 *
 *-----------------------------------------------------------------------------
 */

describe('SNI Hostname Test', function () {
   // SNI Hostname Test can only run on Windows
   if (agentPlatform !== 'win32' || clientPlatform !== 'win32') {
      console.log(`Skipping Test: ${this.title} (Non-Windows Agent and/or Client)`);
      return;
   }

   // Make sure the topology has the standard 1 agent-client pair setup (with an optional ncs)
   if (!topologyParams.tags.includes('standard')) {
      console.log(`Skipping Test: ${this.title} (Non-Standard Topology)`);
      return;
   }

   // The current BSG setup does not support an SSL connection
   if (topologyParams.tags.includes('bsg')) {
      console.log(`Skipping Test: ${this.title} (No support for BSG with SSL)`);
      return;
   }

   // Enable SSL test using a good certificate without revocation check
   let options = getDefaultOpts();
   options.useSSL = true;
   options.certFilePathName = require('process').cwd() + '/sslcrlfiles/BenevSslCert.pfx';
   options.checkRevocation = false;

   // Use local hostname as the SNI for testing purpose
   let localHostname = require("os").hostname();
   options.agentTargetIp = localHostname;
   options.clientTargetIp = localHostname;
   options.blastProtocol = 'BENIT';

   blastSocket.testSNIHostname(options);
});


/*
 *-----------------------------------------------------------------------------
 *
 * Run one in-proc Vvc API test case using SSL
 *
 *-----------------------------------------------------------------------------
 */

describe('In-proc Vvc API test using SSL', function () {
   // In-proc Vvc API test using SSL test can only run on Windows
   if (agentPlatform !== 'win32' || clientPlatform !== 'win32') {
      console.log(`Skipping Test: ${this.title} (Non-Windows Agent and/or Client)`);
      return;
   }

   // Make sure the topology has the standard 1 agent-client pair setup (with an optional ncs)
   if (!topologyParams.tags.includes('standard')) {
      console.log(`Skipping Test: ${this.title} (Non-Standard Topology)`);
      return;
   }

   // The current BSG setup does not support an SSL connection
   if (topologyParams.tags.includes('bsg')) {
      console.log(`Skipping Test: ${this.title} (No support for BSG with SSL)`);
      return;
   }

   // Only run one test case
   let options = getDefaultVvcAPITestOpts();
   options.vvcAPITestsToRun = 'VVC_TEST_CHANNEL*/Positive_ServerOpenChannel_P0';
   options.doOutOfProc = false;

   // Enable SSL mode for Benev
   options.useSSL = true;
   options.certFilePathName = require('process').cwd() + '/sslcrlfiles/BenevSslCert.pfx';
   options.checkRevocation = false;

   blastSocket.testVvcAPIs(options);
});


/*
 *-----------------------------------------------------------------------------
 * Ensure that the performance metrics reflect the expected values
 *
 *-----------------------------------------------------------------------------
 */
describe('[Unstable] Performance Metrics Accuracy Test', function() {
   // Make sure the topology has the standard 1 agent-client par setup (with an optional ncs)
   if (!topologyParams.tags.includes('standard')) {
      console.log(`Skipping Test: ${this.title} (Non-Standard Topology)`);
      return;
   }

   let options = getDefaultOpts();

   // Generate test cases based on the linear combination of these options
   const testCaseSpec = {
      transportModes: ['TCP', 'BEAT'],
      // Who opens the proxy channel?
      openChanPeers: ['client', 'server'],
      // VVC OpenChan channelFlags param.
      channelFlags: [0x0]
   };

   blastSocket.testPerformanceMetrics(options, testCaseSpec);
});


/*
 *-----------------------------------------------------------------------------
 * Test BSG connectivity error codes
 *
 *-----------------------------------------------------------------------------
 */
describe('[Unstable] Test BSG connectivity error codes', function () {
   this.timeout(5000);
   if (!testOptions.bsg.enabled) {
      console.log(`Skipping Test: ${this.title} (Topology needs to include BSG)`);
      return;
   }
   const testCases = [
      {
         // BSG Timeout
         ip: '127.0.0.1',
         expectedError: "VDPCONNECT_GATEWAY_TIMEOUT",
         timeoutSeconds: 50
      }
   ];

   for (const testCase of testCases) {
      let options = getDefaultOpts();
      options.clientTargetIp = testCase.ip;
      options.expectedError = testCase.expectedError;
      options.timeoutSeconds = testCase.timeoutSeconds;

      blastSocket.testBSGFailedConnections(options);
   }
});


/*
 *-----------------------------------------------------------------------------
 *
 * Test VVC BEAT raw channels established successfully
 *
 * This test uses Benev SSL test with VVC BEAT raw channels enabled and
 *
 * 1. Check if the raw channel BEAT connection is established successfully
 * 2. Run one VVC API test to confirm if the raw channel is working
 *
 *-----------------------------------------------------------------------------
 */

describe('VVC BEAT Raw Channels Test', function () {
   // Make sure the topology has the standard 1 agent-client pair setup (with an optional ncs)
   if (!topologyParams.tags.includes('standard')) {
      console.log(`Skipping Test: ${this.title} (Non-Standard Topology)`);
      return;
   }

   // The current BSG setup does not support an SSL connection
   if (topologyParams.tags.includes('bsg')) {
      console.log(`Skipping Test: ${this.title} (No support for BSG with SSL)`);
      return;
   }

   // Only run one VVC API test case
   let options = getDefaultVvcAPITestOpts();
   if (isNaN(options.agentRawBeatPort) || !options.enableRawChannel) {
      console.log(`Skipping Test: ${this.title} (No support for Raw Channel)`);
      return;
   }

   options.vvcAPITestsToRun = 'VVC_TEST_CHANNEL*/Positive_ServerOpenChannel_P0';
   options.doOutOfProc = false;

   // Enable SSL mode for Benev Windows Only
   if (process.platform === 'win32') {
      options.useSSL = true;
      options.certFilePathName = require('process').cwd() + '/sslcrlfiles/BenevSslCert.pfx';
      options.checkRevocation = false;
   }

   options.blastProtocol = 'BEAT';
   blastSocket.testRawChannel(options);
});


/*
 *-----------------------------------------------------------------------------
 *
 * Test VVC TCP raw channels established successfully
 *
 * This test uses Benev SSL test with VVC TCP raw channels enabled and
 *
 * 1. Check if the raw channel TCP connection is established successfully
 * 2. Run one VVC API test to confirm if the raw channel is working
 *
 *-----------------------------------------------------------------------------
 */

describe('VVC TCP Raw Channels Test', function () {
   // Make sure the topology has the standard 1 agent-client pair setup (with an optional ncs)
   if (!topologyParams.tags.includes('standard')) {
      console.log(`Skipping Test: ${this.title} (Non-Standard Topology)`);
      return;
   }

   // The current BSG setup does not support an SSL connection
   if (topologyParams.tags.includes('bsg')) {
      console.log(`Skipping Test: ${this.title} (No support for BSG with SSL)`);
      return;
   }

   // Only run one VVC API test case
   let options = getDefaultVvcAPITestOpts();
   if (isNaN(options.agentRawBeatPort) || !options.enableRawChannel) {
      console.log(`Skipping Test: ${this.title} (No support for Raw Channel)`);
      return;
   }

   options.vvcAPITestsToRun = 'VVC_TEST_CHANNEL*/Positive_ServerOpenChannel_P0';
   options.doOutOfProc = false;

   // Enable SSL mode for Benev Windows Only
   if (process.platform === 'win32') {
      options.useSSL = true;
      options.certFilePathName = require('process').cwd() + '/sslcrlfiles/BenevSslCert.pfx';
      options.checkRevocation = false;
   }

   options.blastProtocol = 'TCP';
   blastSocket.testRawChannel(options);
});


/*
 *-----------------------------------------------------------------------------
 *
 * Test VVC raw channels established successfully when BENIT is used
 *
 * This test uses Benev SSL test with VVC raw channels enabled and
 *
 * 1. Check if the raw channel TCP or BEAT connection is established successfully
 * 2. Run one VVC API test to confirm if the raw channel is working
 *
 *-----------------------------------------------------------------------------
 */

describe('VVC BENIT Raw Channels Test', function () {
   // Make sure the topology has the standard 1 agent-client pair setup (with an optional ncs)
   if (!topologyParams.tags.includes('standard')) {
      console.log(`Skipping Test: ${this.title} (Non-Standard Topology)`);
      return;
   }

   // The current BSG setup does not support an SSL connection
   if (topologyParams.tags.includes('bsg')) {
      console.log(`Skipping Test: ${this.title} (No support for BSG with SSL)`);
      return;
   }

   // Only run one VVC API test case
   let options = getDefaultVvcAPITestOpts();
   if (isNaN(options.agentRawBeatPort) || !options.enableRawChannel) {
      console.log(`Skipping Test: ${this.title} (No support for Raw Channel)`);
      return;
   }

   options.vvcAPITestsToRun = 'VVC_TEST_CHANNEL*/Positive_ServerOpenChannel_P0';
   options.doOutOfProc = false;

   // Enable SSL mode for Benev Windows Only
   if (process.platform === 'win32') {
      options.useSSL = true;
      options.certFilePathName = require('process').cwd() + '/sslcrlfiles/BenevSslCert.pfx';
      options.checkRevocation = false;
   }

   options.blastProtocol = 'BENIT';
   blastSocket.testRawChannel(options);
});