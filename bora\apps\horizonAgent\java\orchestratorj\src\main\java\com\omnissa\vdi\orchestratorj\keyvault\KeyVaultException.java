/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */
package com.omnissa.vdi.orchestratorj.keyvault;

public class KeyVaultException extends Exception {

    private static final long serialVersionUID = 1L;

    public KeyVaultException(String message) {
        super(message);
    }

    public KeyVaultException(Throwable cause) {
        super(cause);
    }
}
