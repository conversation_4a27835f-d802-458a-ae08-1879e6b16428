/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * BasePrep.h
 *
 *      This class implements functions that use some common MFW and KeyVault
 *      functionality, which is needed by all three modes of the helper tool.
 *
 */


#pragma once

#include <utilsLdap.h>
#include <KeyVaultPublic.h>

#define QUEUE_KEYVAULT L"KeyVault"
#define QUEUE_AGENT_PAIRING L"AgentPairingService"

#define HINT_ADD_KEYPAIR L"addkeypair"
#define HINT_ADD_MASTER L"addmaster"
#define HINT_GET_PUBKEY L"getpublic"
#define HINT_GET_RANDOM L"getrandom"
#define HINT_REMOVE L"remove"
#define HINT_LATEST L"latest"
#define HINT_EXISTS L"exist"
#define HINT_PREP_TEMPLATE L"PrepareTemplate"
#define HINT_PREP_CLONE L"FirstStartUpAfterClone"
#define HINT_IT_HELPER L"PrepInternalTemplate"
#define HINT_CLONE_HELPER L"PrepClone"
#define HINT_GI_REGISTRATION L"RegisterGoldenImage"
#define HINT_PREPARE_CLONE L"PrepareClone"

#define PAR_LENGTH L"length"
#define PAR_NAME L"name"
#define PAR_TRANSFORM L"transform"
#define PAR_TRANSFORM_ALGID L"KP_ALGID"
#define PAR_ENCIPHER_NAME L"enciphername"
#define PAR_GEN L"gen"
#define PAR_BINDATASIGNED L"bindatasigned"
#define PAR_BINDATATOSIGN L"bindatatosign"
#define PAR_CHECKDEPENDENTS L"checkdependents"
#define PAR_ENCIPHERGEN L"enciphergen"
#define PAR_PERSIST L"persist"

#define VAL_SHA256_ALGORITHM L"SHA256"
#define VAL_NONCE_SIZE 16
#define VAL_KEY_LENGTH 2048
#define VAL_TIMEOUT_MS 15000
#define VAL_SRV_FQDNS_DELIMETER L' '
#define VAL_RETRIES 20
#define VAL_MILLISEC_PER_SEC 1000
#define VAL_LINKLOCAL_ADDR L"169.254"
#define VAL_SLEEP_IP_CHECK 10000
#define VAL_LOCALHOST_IP_ADDR L"127.0.0.1"

#define ACKSTR L"Ack"
#define PAR_RESULT_TEXT L"resultText"
#define PAR_RESULT_SIG L"resultSig"

#define REG_SRVS_FQDNS L"\\Agent\\Configuration\\Broker"
#define REG_POOL_ID L"\\Agent\\Configuration\\PoolID"
#define MASTER_KEY_NAME L"HPm"
#define KEYPAR_PH_SIGN L"PHs"
#define KEYPAR_GI_ENCR L"GIe"
#define KEYPAR_IC_ENCR L"ICe"

#define PAR_POOL_ID L"pool-id"
#define PAR_GOLDEN_IMAGE_GUID L"golden-image-guid"
#define PAR_PHS_PUBLIC_KEY L"phs-public-key"
#define PAR_PHS_POP_BLOB L"phs-pop-blob"
#define PAR_HOST_NAME L"host-name"
#define PAR_HOST_MAC_ADDR L"host-mac-address"
#define PAR_NONCE L"nonce"
#define PAR_ICE_PUBLIC_KEY L"ice-public-key"
#define PAR_ICE_POP_BLOB L"ice-pop-blob"
#define PAR_GIE_PUBLIC_KEY L"gie-public-key"
#define PAR_GIE_POP_BLOB L"gie-pop-blob"
#define PAR_SIGNATURE L"signature"
#define PAR_AD_SERVER_NAME L"ad-server-name"
#define PAR_AD_SITE L"ad-site"

#define MAX_KV_RETRIES 1

class BasePrep {
public:
   enum kvCallResult { kvCallSuccess, kvCallFailure, kvCallBroken };
   BasePrep();
   virtual ~BasePrep();
   bool createPersistentKeyPair(wstr keypairName, int keyLength);
   bool getVmGuid(wstr &vmGuid);
   bool getPublicKey(wstr keyName, wstr sigData, MsgBinary &pubKey, MsgBinary &signature);
   bool remove(wstr keypairName);
   bool generateNonce(int length, MsgBinary &msgBinary);
   bool connectAndSend(const wstr &brokerFQDN, const wstr &queueName, wstr const &messageHint,
                       const PropertyBag &request, PropertyBag &response);
   kvCallResult kvCallWithRetry(const wstr &hint, const PropertyBag &params, PropertyBag &response,
                                MsgBinary *bin = NULL, MsgBinary *binResp = NULL);
   wstr getHostName();
   wstr getMacAddr();
   vwstr readRegServerFQDNs();
   bool writeRegPoolID(const wstr &poolID);
   wstr readRegPoolID();
   bool signHash(wstr keyName, MsgBinary &sigData);
   bool keyExists(wstr keyName);
   wstr getIPAddr();
   const wstr mMasterKeyName = MASTER_KEY_NAME;

private:
   wstr getIPv4AddressFromAdapters();
   void refreshAdaptersList();
   ULONG GetAdapters();
   PIP_ADAPTER_ADDRESSES mAdaptAddrs;
};
