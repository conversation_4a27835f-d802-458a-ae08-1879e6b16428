/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/* global require:false, module: false, describe: false, it: false, before: false,
   after: false, beforeEach: false, afterEach: false, setTimeout */

"use strict";


var chai = require('chai');
var should = chai.should();
var expect = chai.expect;
var assert = chai.assert;
var request = require('request');
var parallel = require('mocha.parallel');
var fs = require('fs');
var _ = require('underscore');
var netdata = require('./netdata.js');
var EventEmitter = require('events').EventEmitter;
var net = require('net');
const { waitMs, validateResponse } = require('./utils');
const { performance } = require('perf_hooks');

// The information regarding the agent and client
var BENEV_AGENT = {};
var BENEV_CLIENT = {};
// The information regarding the BSG (Blast Secure Gateway)
var BSG = {};
// The default TCP listening port of a Benev Proxy
const BENEV_PROXY_SERVER_LISTEN_PORT = 23457;
const BENEV_PROXY_CLIENT_LISTEN_PORT = 23456;
// The default TCP listening host of a Benev Proxy
const BENEV_PROXY_LISTEN_HOST = '0.0.0.0';
// The default test TCP server's (implemented in node) listening port
const BENEV_TEST_TCP_SERVER_PORT = 8484;
// The default test TCP server's listening host
const BENEV_TEST_TCP_SERVER_HOST = '0.0.0.0';
// Data String for Traffic Generator
var refDataStr = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
// Used to track what configs we've already set
var gConfigs = {};
// The port key passed to the BSG to be used to connect to a benev agent
const BENEV_PORT_KEY = '3b4a06d1-3349-4d3a-8f02-057e0873faa8';

// Some predefined data file sizes for consistency and repeatability
const BENEV_FILESIZE = {
   TINY   : 110,
   SMALL  : 11000,
   MEDIUM : 1100000,
   LARGE  : 110000000,
   GIANT  : 11000000000
};


var self = module.exports = {
   initializePortsAndIpValues: initializePortsAndIpValues,
   primeConfig: primeConfig,
   clearConfig: clearConfig,

   testGetStats: function (benevPeerMode) {
      it(`Should get stats from ${benevPeerMode}`, function (done) {
         request
            .get(`http://localhost:3000/${benevPeerMode}/get-stats`, function (err, res, body) {
               expect(res.body).to.be.not.null;
               console.log(res.body);
               done();
            });
      });
   },

   testWAN: function (options) {
      const desc = ('WAN Network Profile Tests');

      _.each(options.netProfiles, (netProfile) => {

         var fileToSend;
         const dnHost = BENEV_CLIENT.ip,
               dnPort = BENEV_PROXY_CLIENT_LISTEN_PORT,
               upHost = BENEV_AGENT.ip,
               upPort = BENEV_TEST_TCP_SERVER_PORT,
               hexChFlag = options.channelFlag.toString();

         describe(desc, function () {
            this.timeout(100 * 1000);

            before('Validating Node Servers are alive', (done) => {
               fileToSend = makeDataFile(BENEV_FILESIZE.MEDIUM);
               validateNodeServersAlive(done);
            });

            beforeEach('Starting Benev peers', (done) => {
               launchAndStartBenevPeers(options).then(() => {
                  const clientParams = {
                     'benevPeer': 'client',
                     'name': 'abc*',
                     'channel-flags': hexChFlag,
                     'enableRawChannel': options.enableRawChannel
                  };
                  _.extend(clientParams, getProxyParams('client',
                     options.openChanPeer,
                     dnHost,
                     dnPort,
                     upHost,
                     upPort,
                     0)
                  );
                  const serverParams = {
                     'benevPeer': 'server',
                     'name': 'abc*',
                     'channel-flags': hexChFlag,
                     'enableRawChannel': options.enableRawChannel
                  };
                  _.extend(serverParams, getProxyParams('server',
                     options.openChanPeer,
                     dnHost,
                     dnPort,
                     upHost,
                     upPort,
                     0)
                  ); 

                  const specs = [clientParams, serverParams];
                  startBenevProxy(clientParams);
                  startBenevProxy(serverParams);

                  console.log(`Setting network profile to ${netProfile}. ***** ***** ***** ***** *****`);
                  fs.appendFile('ackCumulative.txt', `${netProfile}::${options.blastProtocol}\n`, () => {});

                  var cmd = `ssh root@${options.wanIp} \"./set_net_profile.sh ${netProfile}\"`;
                  const{exec} = require('child_process');
                  exec(cmd, (err, stdout, stderr) => {
                     if (err || stderr){
                        console.log("ERROR: ", err);
                        console.log("STDERR: ", stderr);
                        console.log("SOMETHING WENT WRONG, using previous network profile");
                        done();
                     } else {
                        done();
                        console.log('stdout: ', stdout);
                     }
                  });
               });
            });

            afterEach('Stopping Benev peers', (done) => {
               console.log('Restoring default network profile. ***** ***** ***** ***** *****');
               var cmd = `ssh root@${options.wanIp} \"./set_net_profile.sh 1244000 0 0 0\"`;
               const{exec} = require('child_process');
               exec(cmd, (err, stdout, stderr) => {
                  if (err || stderr){
                     console.log(err);
                     console.log(stderr);
                     //TRY AGAIN
                  }
                  console.log('stdout: ', stdout);
                  killBenevPeer('client', () => {
                     killBenevPeer('server', () => {
                        checkForMemLeak(options, () => {
                           checkForConnectErrorCode(options, null, done);
                        });
                     });
                  });
               });
            });

            // Client: close channnel -> stop session
            function closeChannelAndStopClient(doneCb) {
               closeChannel('client', () => {
                  stopBenevPeer('client', () =>{
                     doneCb();
                  });
               });
             }

            it(`[${options.blastProtocol}] Should maintain data integrity over different WAN settings`, function (done) {
               launchNetcat('server', fileToSend, upPort, () => {
                  launchNetcat('client', fileToSend, dnPort, () => {
                     closeChannelAndStopClient(() => {
                        done();
                     });
                  });
               });
            });
         });
      });
   },

   testClientInitiatedDisconnect: function (opts) {
      const desc = ('Client-initiated disconnect');
      describe(desc, function () {
         // Default dump timeout seconds
         let dumpTimeoutSeconds = 15;

         /*
          * Set actual test timeout seconds.
          */
         this.timeout(getTestTimeout(dumpTimeoutSeconds) * 1000);

         // Test done flag. Used to prevent unnecessary timeout handler.
         let testDone = false;

         // Save a local copy
         let options = Object.assign({}, opts);

         before('Validating Node Servers are alive', (done) => {
            validateNodeServersAlive(done);
         });

         beforeEach('Starting Benev peers', function(done) {
            // Set an independent timeout for launching
            this.timeout(20 * 1000);
            launchBenevPeers(options).then(done);
         });

         afterEach('Stopping Benev peers', (done) => {
            // Set flag to prevent unnecessary timeout handler.
            testDone = true;

            killBenevPeer('client', () => {
               killBenevPeer('server', () => {
                  checkForMemLeak(options, () => {
                     checkForConnectErrorCode(options, null, done);
                  });
               });
            });
         });

         // Client: close channnel -> stop session
         function closeChannelAndStopClient(doneCb) {
            closeChannel('client', () => {
               stopBenevPeer('client', () =>{
                  doneCb();
               });
            });
         }

         it(`[${options.blastProtocol}] Close channel, stop, and disconnect should be ok`, function (done) {
            setTimeout(() => {
               if (testDone === false) {
                  console.log('\nClient-initiated disconnect failed with timeout!\n');
                  dumpBenevPeers();
               }
            }, dumpTimeoutSeconds * 1000);

            startBenevPeers(options).then(() => {
               // Set delay to allow channel create to finish
               setTimeout(() => {
                  closeChannelAndStopClient(() => {
                     done();
                  });
               }, 5000);
            })
            .catch(err => {
               done(err);
            });
         });
      });
   },

   testServerInitiatedDisconnect: function (opts) {
      const desc = ('Server-initiated disconnect');
      describe(desc, function () {
         // Default dump timeout seconds
         let dumpTimeoutSeconds = 15;

         /*
          * Set actual test timeout seconds.
          */
         this.timeout(getTestTimeout(dumpTimeoutSeconds) * 1000);

         // Test done flag. Used to prevent unnecessary timeout handler.
         let testDone = false;

         // Save a local copy
         let options = Object.assign({}, opts);

         before('Validating Node Servers are alive', (done) => {
            validateNodeServersAlive(done);
         });

         beforeEach('Starting Benev peers', function(done) {
            // Set an independent timeout for launching
            this.timeout(20 * 1000);
            launchBenevPeers(options).then(done);
         });

         afterEach(`Stopping Benev peers`, (done) => {
            // Set flag to prevent unnecessary timeout handler.
            testDone = true;

            killBenevPeer('client', () => {
               killBenevPeer('server', () => {
                  checkForMemLeak(options, () => {
                     checkForConnectErrorCode(options, null, done);
                  });
               });
            });
         });

         // Server: close channnel -> stop session
         function closeChannelAndStopServer(doneCb) {
            closeChannel('server', () => {
               stopBenevPeer('server', () =>{
                  doneCb();
               });
            });
         }

         it(`[${options.blastProtocol}] Close channel, stop, and disconnect should be ok`, function (done) {
            setTimeout(() => {
               if (testDone === false) {
                  console.log('\nServer-initiated disconnect failed with timeout!\n');
                  dumpBenevPeers();
               }
            }, dumpTimeoutSeconds * 1000);

            startBenevPeers(options).then(() => {
               // Set delay to allow channel create to finish
               setTimeout(() => {
                  closeChannelAndStopServer(() => {
                     done();
                  });
               }, 5000);
            })
            .catch(err => {
               done(err);
            });
         });
      });
   },

   testDisconnectFromClientAndServerAtSameTime: function (opts) {
      const desc = ('Simultaneous disconnect');
      describe(desc, function () {
         this.timeout(15 * 1000);

         // Save a local copy
         let options = Object.assign({}, opts);

         before('Validating Node Servers are alive', (done) => {
            validateNodeServersAlive(done);
         });

         beforeEach('Starting Benev peers', function(done) {
            // Set an independent timeout for launching
            this.timeout(20 * 1000);
            launchBenevPeers(options).then(done);
         });

         afterEach(`Stopping Benev peers`, (done) => {
            killBenevPeer('client', () => {
               killBenevPeer('server', () => {
                  checkForMemLeak(options, () => {
                     checkForConnectErrorCode(options, null, done);
                  });
               });
            });
         });

         const testDesc = ('Close channel, stop, and disconnect should be ok');
         function disconnectPeers(teardownOrder) {
            // p and q are client or server
            var [p, q] = teardownOrder;
            it(`[${options.blastProtocol}][${p}, ${q}] ${testDesc}`, function (done) {
               startBenevPeers(options).then(() => {
                  var doneCnt = 0;

                  function peerDone(cb) {
                     doneCnt++;
                     if (doneCnt == 2) {
                        cb();
                     }
                  }

                  function closeChannelPeerDone() {
                     peerDone(() => {
                        doneCnt = 0;
                        stopBenevPeer(p, _.partial(peerDone, done));
                        stopBenevPeer(q, _.partial(peerDone, done));
                     });
                  }

                  // Set delay to allow channel create to finish
                  setTimeout(() => {
                     closeChannel(p, closeChannelPeerDone);
                     closeChannel(q, closeChannelPeerDone);
                  }, 5000);
               })
               .catch(err => {
                  done(err);
               });
            });
         }

         disconnectPeers(['client', 'server']);
         disconnectPeers(['server', 'client']);
      });
   },

   testCustomUdpPort: function(opts) {
      describe('Custom Udp port', function () {
         // BENEV_AGENT.blastUdpPort is what everything else thinks the agent is listening on
         // opts.agentBlastUdpPort is what the agent is actually listening on
         // This forwards traffic attempting to connect to the agent to where it actually is
         const bwcapperArgs = {
            'listen-port': BENEV_AGENT.blastUdpPort,
            'forward-port': opts.agentBlastUdpPort,
         }

         let dumpTimeoutSeconds = 10;

         /*
          * Set actual test timeout seconds.
          */
         this.timeout(getTestTimeout(dumpTimeoutSeconds) * 1000);

         // Test done flag. Used to prevent unnecessary timeout handler.
         let testDone = false;

         before('Validating Node Servers are alive', (done) => {
            validateNodeServersAlive(done);
         });

         beforeEach(`Start benev peers with ${opts.blastProtocol} and ` +
                    `launch bandwidth capper`, (done) => {
            launchBWCapper(bwcapperArgs, () => {
               launchAndStartBenevPeers(opts).then(done);
            });
         });

         afterEach('Stop benev peers and kill bandwidth capper', (done) => {
            stopBenevPeers().then(() => {
               killBWCapper(() => {
                  testDone = true;
                  checkForConnectErrorCode(opts, null, done);
               });
            });
         });

         // Client: close channnel -> stop session
         function closeChannelAndStopClient(doneCb) {
            closeChannel('client', () => {
               stopBenevPeer('client', () =>{
                  doneCb();
               });
            });
         }


         it(`[${opts.blastProtocol}] Connect-disconnect with custom port`, function(done) {
            setTimeout(() => {
               if (testDone === false) {
                  console.log('\nSet custom UDP port test failed with timeout!\n');
                  dumpBenevPeers();
               }
            }, dumpTimeoutSeconds * 1000);

            getVvcStats('client', 'Check BEAT transport', (data) => {
               // Make sure the BEAT connection was successfully started
               expect(data['Local Transport']).to.equal(opts.blastProtocol,
                      `Wrong local transport. Expected: ${opts.blastProtocol}, ` +
                      `actual: ${data['Local Transport']}`);
               expect(data['Peer Transport']).to.equal(opts.blastProtocol,
                      `Wrong peer transport. Expected: ${opts.blastProtocol}, ` +
                      `actual: ${data['Peer Transport']}`);

               // Make sure the client used the requested port rather than the server's
               expect(parseInt(data['Remote Port'])).to.equal(opts.clientBlastUdpPort,
                      `Client didn't use custom port. ` +
                      `Expected: ${opts.clientBlastUdpPort}, ` +
                      `actual: ${data['Remote Port']}`);

               closeChannelAndStopClient(() => {
                  done();
               });
            });
         });
      });
   },

   trafficGeneratorTests: function (options) {
      runTGTests(options, 'Traffic Generator', 'Maintain data integrity')
   },

   testReconnectCountNR: function(options) {
      describe('Test Reconnect Count in NR', function () {
         const dropTimeMs = 5000;
         // TODO: Find a way to wait until the connection is actually back
         const waitTimeMs = 15000;
         const testTimeMs = (dropTimeMs + waitTimeMs + 5000) * // 4500ms buf
                                                      options.numDisconnects;

         this.timeout(testTimeMs + 30 * 1000); // additional 30s buffer

         // Save a local copy
         let opts = Object.assign({}, options);

         // BENEV_AGENT.blastTcpPort is what everything else thinks the agent is listening on
         // opts.agentBlastTcpPort is what the agent is actually listening on
         // This forwards traffic attempting to connect to the agent to where it actually is
         const bwcapperArgs = {
            'listen-port': BENEV_AGENT.blastTcpPort,
            'forward-port': opts.agentBlastTcpPort,
         }

         before('Disable Network Continuity', (done) => {
            validateNodeServersAlive(() => {
               writeConfig('server', 'w',
                           '{ "Benev.enableBlastNetworkContinuity": false }',
                           opts).then(done);
            });
         });

         // Network Continuity is enabled by default. Restore after test.
         after('Restore Network Continuity', (done) => {
            writeConfig('server', 'w',
                        '{ "Benev.enableBlastNetworkContinuity": true }',
                        opts).then(done);
         });

         beforeEach(`Start benev peers with ${opts.blastProtocol} and ` +
                    `launch bandwidth capper`, (done) => {
            launchBWCapper(bwcapperArgs, () => {
               launchAndStartBenevPeers(opts).then(done);
            });
         });

         afterEach('Stop benev peers and kill bandwidth capper', (done) => {
            stopBenevPeers().then(() => {
               killBWCapper(() => {
                  checkForConnectErrorCode(opts, null, done);
               });
            });
         });

         function runNRTest(remainingTimes, doneCb) {
            if (remainingTimes === 0) {
               doneCb();
            } else {
               const expectedCount = opts.numDisconnects - remainingTimes + 1;

               setTimeout(() => { // Interrupt connection after 500 ms
                  killBWCapper(() => {
                     setTimeout(() => { // Resume connection after dropTimeMs
                        launchBWCapper(bwcapperArgs, () => { // relaunch
                           setTimeout(() => { // Check after waitTimeMs
                              checkReconnectCount(expectedCount, (data) => {
                                 expect(data['NCEnabled']).to.equal('0');
                                 runNRTest(remainingTimes - 1, doneCb);
                              });
                           }, waitTimeMs);
                        });
                     }, dropTimeMs);
                  });
               }, 500);
            }
         }

         it(`[${opts.blastProtocol}] Reconnect Count in NR`, function(done) {
            runNRTest(opts.numDisconnects, done);
         });
      });
   },

   testNetworkContinuity: testNetworkContinuity,

   compareProtocols: compareProtocols,

   testVvcAPIs: function (options) {
      options.reset = 'true';

      let desc = 'Run ' + (options.doOutOfProc ? 'out-of-proc' : 'in-proc') +
                 ' Vvc API tests in ' + (options.useSSL ? '' : 'non-') + 'SSL mode';
      describe(desc, function () {
         // Default dump timeout seconds
         let dumpTimeoutSeconds = 90;

         /*
          * Set actual test timeout seconds.
          */
         this.timeout(getTestTimeout(dumpTimeoutSeconds) * 1000);

         // Test done flag. Used to prevent unnecessary timeout handler.
         let testDone = false;

         before('Validating Node Servers are alive', (done) => {
            validateNodeServersAlive((err) => {
               if (err) {
                  done(err);
               }
               // Set the Reg keys for Vvc API tests
               setVvcAPITestRegKeys(options).then(() => {
                  done();
               });
            });
         });

         after('Clearing reg keys', function (done) {
            // Unset Reg keys after tests
            this.timeout(10 * 1000);
            console.log("Clearing the Vvc API tests reg keys");
            unsetVvcAPITestRegKeys().then(() => {
               setTimeout(() => {
                  console.log('Vvc test cleanup is finished');
                  done();
               }, 300);
               console.log('Waiting for vvc test cleanup');
            });            
         });

         afterEach('Stopping Benev peers', (done) => {
            // Set flag to prevent unnecessary timeout handler.
            testDone = true;

            killBenevPeer('client', () => {
               killBenevPeer('server', () => {
                  checkForMemLeak(options, () => {
                     checkForConnectErrorCode(options, null, done);
                  });
               });
            });
         });


         // If we're running OOP tests, make sure the system is set up correctly
         function oopSystemSetup(options) {
            let command = 'setup-oop';
            let url = `${benevServerUrl('server')}/${command}`;

            return new Promise((resolve, reject) => {
               if (options.doOutOfProc === false) {
                  // As long as we don't do the setup, in-proc tests will run
                  resolve();
               } else {
                  request.get(url, (err, res) => {
                     validateResponse(err, res, command);
                     resolve();
                  });
               }
            });
         }

         it(`${this.parent.title}`, function (done) {
            setTimeout(() => {
               if (testDone === false) {
                  console.log('\nVvc API tests failed with timeout!\n');
                  dumpBenevPeers();
               }
            }, dumpTimeoutSeconds * 1000);

            oopSystemSetup(options).then(() => {
               setupVvcAPITests('client', options).then(() => {
                  setupVvcAPITests('server', options).then(() => {
                     launchAndStartBenevPeers(options).then(() => {
                        runVvcAPITests(options).then(done);
                     })
                     .catch(err => {
                        done(err);
                     });
                  });
               });
            });
         });
      });
   },

   testBandwidthCap: function(options) {
      describe('Send Bandwidth Cap', function () {
         this.timeout(40 * 1000);

         const testCaseDesc = 'Average sending bandwidth should be <= 2 * threshold';
          runTGTests(options, 'Bandwidth Cap', testCaseDesc);
      });
   },

   testWireBandwidthCap: function(options) {

      let opts = Object.assign({}, options); //Make a copy of the options
      _.each (options.wireBandwidthCap, (wireBWCap) => {
         // BENEV_AGENT.blastTcpPort is what everything else thinks the agent is listening on
         // opts.agentBlastTcpPort is what the agent is actually listening on
         // This forwards traffic attempting to connect to the agent to where it actually is
         const bwcapperArgs = {
            'listen-port' : BENEV_AGENT.blastTcpPort,
            'forward-port' : options.agentBlastTcpPort,
            'downstream-bw-cap' : wireBWCap,
            'upstream-bw-cap' : wireBWCap,
            };

      describe('Send Wire Bandwidth Cap', function () {
         this.timeout(100 * 1000);

         // Call bandwidth capper

         beforeEach('Start bandwidth capper before starting benev peers', (done) => {
            launchBWCapper(bwcapperArgs, () => {
               done();
            });
         });

         afterEach('Stop bandwidth capper', (done) => {
            killBWCapper(() => {
               checkForConnectErrorCode(options, null, done);
            });
         });

         //Run traffic generator tests
         const testCaseDesc = 'Throughput should be minimum of two bandwidth caps';
         runTGTests(options, 'Wire Bandwidth cap', testCaseDesc);
         });
      });
   },

   testFailedConnection: function(options) {
      describe('Client-only', function () {
         this.timeout(options.timeoutSeconds * 1000);

         it(`Should fail with ${options.expectedError} on an attempted connection ` +
                                 `to ${options.clientTargetIp}`, function (done) {
            // Start the client
            function startBenevClient(protocol, host, tcpPort, done) {
               const url = `start/?protocol=${protocol}&host=${host}&tcpPort=${tcpPort}`;
               nodeServerRequest(request.post, 'client', url, 'Start', (res, err) => {
                  done();
               });
            }

            // Check for memory leak is required
            function checkMemLeak(doneCb) {
               if (options.doMemLeakCheck) {
                  memLeakCheck('client', doneCb);
               } else {
                  doneCb();
               }
            }

            launchBenevPeer('client', null, () => {
               console.log('Client launched');
               startBenevClient(options.blastProtocol, options.clientTargetIp,
                                options.clientBlastTcpPort, () => {
                  killBenevPeer('client', () => {
                     console.log('Client killed');
                     checkMemLeak(() => {
                        checkForConnectErrorCode(options, options.expectedError, done);
                     });
                  });
               });
            });
         });
      });
   },

   testBSGFailedConnections: function (options) {
      const desc = ('Gateway Failed Connections');
      describe(desc, function () {
         // Default dump timeout seconds
         let dumpTimeoutSeconds = 10;

         function startBenevClient(protocol, host, tcpPort, done) {
            let url = `start/?protocol=${protocol}&host=${host}&tcpPort=${tcpPort}`;
            // Start the client
            function startBenevClientInternal() {
               nodeServerRequest(request.post, 'client', url, 'Start', (res, err) => {
                  done();
               });
            }

            const bsgParams = {
               agentIp: BSG.targetIp,
               agentPort: BSG.targetPort,
               portKey: BENEV_PORT_KEY
            };

            addBSGRoute(bsgParams, startBenevClientInternal);

         }

         // Test for bsg error codes
         it(`Stop benevClient, check for error codes`, function (done) {
            setTimeout(() => {
               console.log('Gateway failed connections timeout\n');
               dumpBenevPeers();
            }, dumpTimeoutSeconds * 1000);

            launchBenevPeer('client', null, () => {
               startBenevClient(options.blastProtocol, options.clientTargetIp,
                                   options.clientBlastTcpPort, () => {
                  killBenevPeer('client', () => {
                     console.log('Client killed');
                     checkForMemLeak(options, () => {
                        checkForConnectErrorCode(options, options.expectedError, done);
                     });
                  });
               });
            });
         });
      });
   },

   testCRLCache: function (options) {
      const desc = ('CRL Cache Management for Certificate Revocation Check');
      describe(desc, function () {
         before('Validating Node Servers are alive', (done) => {
            validateNodeServersAlive(done);
         });

         it('CRL Cache is ' + (options.enableCRLCache ? 'enabled' : 'disabled') +
            '. Should return connection error code VDPCONNECT_CONNECT_TLS', function (done) {
            this.timeout(30 * 1000);

            // Check for memory leak is required
            function checkMemLeak(doneCb) {
               if (options.doMemLeakCheck) {
                  memLeakCheck('client', doneCb);
               } else {
                  doneCb();
               }
            }

            function startBenevServerUsingSSL(protocol, host, tcpPort, done) {
               var reqUrl = (`${benevServerUrl('server')}/server/start/` +
                             `?protocol=${protocol}&host=${host}&tcpPort=${tcpPort}` +
                             `&useSSL=true&certFilePathName=` + require('process').cwd() +
                             `/sslcrlfiles/BenevSslCertRevoked.pfx`);

               request.post(reqUrl, function (err, res) {
                  if (done) {
                     done();
                  }
               });
            }

            function startBenevClientUsingSSL(protocol, host, tcpPort, done) {
               var reqUrl = (`${benevServerUrl('client')}/client/start/` +
                             `?protocol=${protocol}&host=${host}&tcpPort=${tcpPort}` +
                             `&useSSL=true&enableCrlCache=` +
                             (options.enableCRLCache ? "true" : "false"));

               request.post(reqUrl, function (err, res) {
                  if (done) {
                     let closeData = validateResponse(err, res, 'close');
                     expect(closeData).to.equal('client closed with error');
                     done();
                  }
               });
            }

            function startCertificateRevocationCheck(doneCb) {
               launchBenevPeer('server', null, () => {
                  startBenevServerUsingSSL(options.blastProtocol, BENEV_AGENT.targetIp,
                                           options.agentBlastTcpPort, () => {
                     launchBenevPeer('client', null, () => {
                        startBenevClientUsingSSL(options.blastProtocol, BENEV_CLIENT.targetIp,
                                                 options.clientBlastTcpPort, () => {
                           killBenevPeer('server', () => {
                              killBenevPeer('client', () => {
                                 checkMemLeak(() => {
                                    checkForConnectErrorCode(options, 'VDPCONNECT_CONNECT_TLS', doneCb);
                                 });
                              });
                           });
                        });
                     });
                  });
               });
            }

            /*
             * When CRL cache is disabled, CRL file will be downloaded from HTTP server.
             * When CRL cache is enabled, CRL file will be fetched from local file cache.
             */
            if (options.enableCRLCache == false) {
               launchHttpServer(() => {
                  startCertificateRevocationCheck(() => {
                     killHttpServer(done);
                  })
               });
            } else {
               startCertificateRevocationCheck(done);
            }
         });
      });
   },

   testSNIHostname: function (options) {
      describe('Test SNI hostname is set correctly', function () {
         let dumpTimeoutSeconds = 15;

         /*
          * Set actual test timeout seconds.
          */
         this.timeout(getTestTimeout(dumpTimeoutSeconds) * 1000);

         // Test done flag. Used to prevent unnecessary timeout handler.
         let testDone = false;

         before('Validating Node Servers are alive', (done) => {
            validateNodeServersAlive(done);
         });

         beforeEach('Start benev peers in SSL mode', (done) => {
            launchAndStartBenevPeers(options).then(done);
         });

         afterEach('Stop benev peers', (done) => {
            stopBenevPeers().then(() => {
               testDone = true;
               checkForConnectErrorCode(options, null, done);
            });
         });

         // Client: close channnel -> stop session
         function closeChannelAndStopClient(doneCb) {
            closeChannel('client', () => {
               stopBenevPeer('client', () =>{
                  doneCb();
               });
            });
         }

         it('Both client and server sockets should have the same SNI hostname', function (done) {
            setTimeout(() => {
               if (testDone === false) {
                  console.log('\nSet SNI hostname test failed with timeout!\n');
                  dumpBenevPeers();
               }
            }, dumpTimeoutSeconds * 1000);

            var clientSNIHostname = null, serverSNIHostname = null;

            // Check client and server 5 seconds later to make sure SSL handshake is complete
            setTimeout(() => {
               getVvcStats('client', 'Check SNI hostname', (data) => {
                  clientSNIHostname = data['SNI Hostname'];

                  getVvcStats('server', 'Check SNI hostname', (data) => {
                     serverSNIHostname = data['SNI Hostname'];

                     console.log(`Checking clientSNIHostname is ${clientSNIHostname} ` +
                                 `and serverSNIHostname is ${serverSNIHostname}`);

                     // Make sure both client and server SNI hostnames are set
                     expect(clientSNIHostname).to.be.not.null;
                     expect(serverSNIHostname).to.be.not.null;

                     /*
                      * Make sure server SNI hostname is the same as
                      * the client SNI hostname.
                      */
                     console.log(`Checking clientSNIHostname:${clientSNIHostname} ` +
                                 `is same as serverSNIHostname:${serverSNIHostname}`);

                     expect(clientSNIHostname).to.equal(serverSNIHostname,
                            `SNI hostnames don't match. ` +
                            `clientSNIHostname: ${clientSNIHostname}, ` +
                            `serverSNIHostname: ${serverSNIHostname}`);

                     closeChannelAndStopClient(() => {
                        done();
                     });
                  });
               });
            }, 5000);
         });
      })
   },

   testPerformanceMetrics(opts, testCaseSpec) {
      // The test server (target) options
      const tcpHost = BENEV_TEST_TCP_SERVER_HOST;
      const tcpPort = BENEV_TEST_TCP_SERVER_PORT;
      const targetOpts = {
         listenPort: tcpPort,
         listenAddr: tcpHost,
         setNoDelay: true,
         echoBack: false  // echo back data sent by a client
      };

      describe('Test Performance Metrics', function () {
         this.timeout(100*1000);

         let target = null;

         // free vars: target
         before('Starting TCP Target', (done) => {
            target = netdata.tcpTarget(targetOpts, () => {
               done();
            });
         });

         // free vars: target
         after('Stopping TCP Target', (done) => {
            target.stop(() => {
               done();
            });
         });

         const testDesc = `Performance metrics should reflect the amount of data sent`;

         const metricsmap = createMetricsMap();

         after('Writing metrics', function (done) {
            writeAndClearMetricsMap(metricsmap, testDesc);
            done();
         });

         const filesize = BENEV_FILESIZE.LARGE;

         const dataOpts = {
            xferFilename: makeDataFile(filesize),
            optionString: ``,
            peer: ``,
            metricsmap: metricsmap,
            echo: false
         };

         runTestThroughBenevProxiesAndBWCapper(opts, undefined, testDesc,
                                 testCaseSpec, (destInfo) => {
            // Move test details over to dataOpts.optionString
            dataOpts.optionString = destInfo.optionString;
            dataOpts.peer = destInfo.peer;
            // Transfer
            return new Promise((resolve, reject) => {
               let isTransferFinished = false;
               let transferEnd = 0;
               let transferStart = performance.now();

               streamData(destInfo, dataOpts).then(() => {
                  transferEnd = performance.now();
                  isTransferFinished = true;
               });

               // Keep a count of the total estimate for how much data has been
               // sent and received according to the bandwidth metrics
               let totalOut = 0;
               let totalIn = 0;

               // How many times have we read the bandwidth
               let totalReadings = 0;

               // Wait a few seconds to let the metrics stabilize
               setTimeout(() => {
                  // Check the metrics every second so that bandwidth can be
                  // collected and used to estimate bytes sent/received
                  let interval = setInterval(() => {
                     getPerformanceMetrics('Get Performance Metrics', (data) => {
                        let {bytesSent, bytesReceived, outBw, inBw} = data;

                        // The bandwidth metrics are in kbps, convert to bytes per
                        // second
                        totalOut += parseFloat(outBw) * (1000 / 8);
                        totalIn += parseFloat(inBw) * (1000 / 8);

                        totalReadings++;

                        if (isTransferFinished) {
                           // Get the percent difference between two numbers
                           function diff(origNum, newNum) {
                              return (newNum - origNum) / origNum;
                           }

                           let expectedBytes = filesize;
                           let expectedBW = expectedBytes / ((transferEnd - transferStart) / 1000);

                           let actualBytes;
                           let actualBW;

                           if (dataOpts.peer === 'client') {
                              actualBytes = bytesReceived;
                              actualBW = totalIn / totalReadings;
                           } else if (dataOpts.peer === 'server') {
                              actualBytes = bytesSent;
                              actualBW = totalOut / totalReadings;
                           } else {
                              expect.fail(`The open channel peer is "${dataOpts.peer}" and should only be client or server`);
                           }

                           let actualBytesDiff = diff(expectedBytes, actualBytes);
                           let actualBWDiff = diff(expectedBW, actualBW);

                           console.log('Performance metrics:');
                           console.log(`  Expected Bytes: ${expectedBytes}`);
                           console.log(`  Actual Bytes: ${actualBytes} (${actualBytesDiff * 100}%)`);
                           console.log(`  Expected Bandwidth: ${expectedBW}`);
                           console.log(`  Actual Bandwidth: ${actualBW} (${actualBWDiff * 100}%)`);

                           // TODO: Determine why this does not match exactly
                           expect(actualBytesDiff).to.be.within(-.1, .1);
                           // Allow up to a 10% difference for the bandiwdth
                           expect(actualBWDiff).to.be.within(-.1, .1);

                           clearInterval(interval);
                           resolve();
                        }
                     });
                  }, 1000);
               }, 3000);
            });
         });
      });
   },

   testRawChannel: function (options) {
      _.each ([true, false], (enableRawChannel) => {
         runRawChannelTest(options, enableRawChannel);
      });
   }
};


/*
 * Runs a test to check if raw channel is properly enabled or disabled.
 */
function runRawChannelTest(options, enableRawChannel) {
   let blastProtocol = options.blastProtocol;

   describe(`VVC ${blastProtocol} Raw Channel Test`, function () {
      // Default dump timeout seconds
      let dumpTimeoutSeconds = 30;

      /*
       * Set actual test timeout seconds.
       */
      this.timeout(getTestTimeout(dumpTimeoutSeconds) * 1000);

      // Test done flag. Used to prevent unnecessary timeout handler.
      let testDone = false;

      before('Validating Node Servers are alive', (done) => {
         validateNodeServersAlive(done);
      });

      after('Clearing reg keys', function (done) {
         console.log("Clearing the Raw channels tests reg keys");
         unsetVvcAPITestRegKeys().then(() => {
            setTimeout(() => {
               console.log('Raw channels test cleanup is finished');
               done();
            }, 300);
            console.log('Waiting for Raw channels test cleanup');
         });            
      });

      beforeEach('Set enable/disable raw channel flag', (done) => {
         options.enableRawChannel = enableRawChannel;
         setVvcAPITestRegKeys(options).then(() => {
            launchAndStartBenevPeers(options).then(done);
         });
      });

      afterEach('Stop benev peers', (done) => {
         stopBenevPeers().then(() => {
            testDone = true;
            checkForConnectErrorCode(options, null, done);
         });
      });

      it(`VVC ${blastProtocol} Raw Channel is ` + (enableRawChannel ? 'enabled' : 'disabled'), function (done) {
         setTimeout(() => {
            if (testDone === false) {
               console.log('\nRaw channels test failed with timeout!\n');
               dumpBenevPeers();
            }
         }, dumpTimeoutSeconds * 1000);

         function IsBEATRawChannelEnabled(netstat) {
            return netstat.length == 2 &&
                   netstat[0].indexOf('UDP') >= 0 &&
                   netstat[0].indexOf(options.agentBlastUdpPort) > 0 &&
                   netstat[1].indexOf('UDP') >= 0 &&
                   netstat[1].indexOf(options.agentRawBeatPort) > 0;
         }

         function IsTCPRawChannelEnabled(netstat) {
            return netstat.length == 3 &&
                   netstat[0].indexOf('TCP') >= 0 &&
                   netstat[0].indexOf(options.agentBlastTcpPort) > 0 &&
                   netstat[1].indexOf('TCP') >= 0 &&
                   netstat[1].indexOf(options.agentBlastTcpPort) > 0 &&
                   netstat[2].indexOf('TCP') >= 0 &&
                   netstat[2].indexOf(options.agentBlastTcpPort) > 0;
         }

         function isRawChannelEnabled(netstat) {
            switch(blastProtocol) {
               case 'BEAT':
                  return IsBEATRawChannelEnabled(netstat);
               case 'TCP':
                  return IsTCPRawChannelEnabled(netstat);
               default:
                 expect.fail(`isRawChannelEnabled: Unknown protocol ${protocol}`);
            }
         }

         function isBEATRawChannelDisabled(netstat) {
            return netstat.length == 1 &&
                   netstat[0].toUpperCase().indexOf('UDP') >= 0 &&
                   netstat[0].indexOf(options.agentBlastUdpPort) > 0;
         }

         function isTCPRawChannelDisabled(netstat) {
            if (process.platform === 'win32') {
               return netstat.length == 2 &&
                      netstat[0].toUpperCase().indexOf('TCP') >= 0 &&
                      netstat[0].indexOf(options.agentBlastTcpPort) > 0 &&
                      netstat[1].toUpperCase().indexOf('TCP') >= 0 &&
                      netstat[1].indexOf(options.agentBlastTcpPort) > 0;
            } else {
               return netstat.length == 1 &&
                      netstat[0].toUpperCase().indexOf('TCP') >= 0 &&
                      netstat[0].indexOf(options.agentBlastTcpPort) > 0;
            }
         }

         function isRawChannelDisabled(netstat) {
            switch(blastProtocol) {
               case 'BEAT':
                  return isBEATRawChannelDisabled(netstat);
               case 'TCP':
                  return isTCPRawChannelDisabled(netstat);
               default:
                  expect.fail(`isRawChannelDisabled: Unknown protocol ${protocol}`);
            }
         }

         function checkRawChannelStatusWindows() {
            let cmd = `wmic process where "name='benevPeer.exe' and commandline like '%-mode s%'" get processid /value`;
            const{exec} = require('child_process');

            exec(cmd, (err, stdout, stderr) => {
               if (err || stderr){
                  expect.fail(`Failed to run wmic: ${err}. ${stderr}.`);
               }

               const wmicOutput = stdout.toString().trim();
               const [name, pid] = wmicOutput.split('=');

               expect(name).to.equal('ProcessId', `Couldn't find ProcessId from wmic output ${wmicOutput}`);

               let netStatProtocol = blastProtocol;
               if (netStatProtocol == 'BEAT') {
                  netStatProtocol = 'UDP';
               }

               cmd = `netstat -abnop ` + netStatProtocol.toLowerCase() + ` | findstr ${pid}`; 
               exec(cmd, (err, stdout, stderr) => {
                  if (err || stderr){
                     expect.fail(`Failed to run netstat: ${err}. ${stderr}.`);
                  }

                  const netstat = stdout.toString().trim().split(/\r\n|\r|\n/);
                  console.log(`netstat=${netstat}, netstat.length=${netstat.length}`);

                  if (options.enableRawChannel) {
                     if (isRawChannelEnabled(netstat)) {
                        console.log(`${blastProtocol} raw channel found, port=` +
                                    (blastProtocol == 'BEAT' ? `${options.agentRawBeatPort}`
                                                             : `${options.agentBlastTcpPort}`));
                        setupVvcAPITests('client', options).then(() => {
                           setupVvcAPITests('server', options).then(() => {
                              runVvcAPITests(options).then(done);
                           });
                        });
                     } else {
                        expect.fail(`Failed to check if raw channel is enabled.`);
                     }
                  } else if (isRawChannelDisabled(netstat)) {
                     console.log(`Fallback to regular, port=${options.agentBlastUdpPort}`);
                     done();
                  } else {
                     expect.fail(`Failed to check if raw channel is disabled.`);
                  }
               });
            });
         }

         function checkRawChannelStatusLinux() {
            const{exec} = require('child_process');

            let netStatCmdParam;
            switch(blastProtocol) {
               case 'BEAT':
                  netStatCmdParam = '-unpl';
                  break;
               case 'TCP':
                  netStatCmdParam = '-tnpl';
                  break;
               default:
                  expect.fail(`checkRawChannelStatusLinux: Unknown protocol ${blastProtocol}`);
            }

            let rawChannelPortCmd = `netstat ${netStatCmdParam} | grep ` +
                                    (blastProtocol == 'BEAT' ? `${options.agentRawBeatPort}`
                                                             : `${options.agentBlastTcpPort}`);
            exec(rawChannelPortCmd, (err, stdout, stderr) => {
               /*
                * Raw channel isn't supported on Linux. So when query raw beat port (by default
                * 53100) it will show nothing but when query raw tcp port (by default 53000) there
                * will be one item as TCP uses the same port as regular channel for raw channel.
                */
               if (blastProtocol == 'BEAT') {
                  expect(stdout.toString().trim()).to.be.empty;
               }

               // Check regular port and make sure the fallback is working
               let regularChannelCmd = `netstat ${netStatCmdParam} | grep ` +
                                       (blastProtocol == 'BEAT' ? `${options.agentBlastUdpPort}`
                                                                : `${options.agentBlastTcpPort}`);
               exec(regularChannelCmd, (err, stdout, stderr) => {
                  const netstat = stdout.toString().trim().split(/\r\n|\r|\n/);
                  console.log(`netstat=${netstat}, netstat.length=${netstat.length}`);

                  if (isRawChannelDisabled(netstat)) {
                     console.log(`Linux: Fallback to regular, port=${options.agentBlastUdpPort}`);
                     done();
                  } else {
                     expect.fail(`Linux: Failed to check if raw channel is disabled.`);
                  }
               });
            });
         }

         function checkRawChannelStatus() {
            if (process.platform === 'win32') {
               checkRawChannelStatusWindows();
            } else {
               checkRawChannelStatusLinux();
            }
         }

         setTimeout(() => {
            if (blastProtocol == 'BENIT') {
               getVvcStats('server', 'Get Local Transport', (data) => {
                  blastProtocol = data['Local Transport'];
                  checkRawChannelStatus();
               });
            } else {
               checkRawChannelStatus();
            }
         }, 5000);
      });
   });
}


/*
 * Runs a test that makes sure network continuity is properly working.
 */
function testNetworkContinuity(opts, testCaseSpec) {
   // The test server (target) options
   const tcpHost = BENEV_TEST_TCP_SERVER_HOST;
   const tcpPort = BENEV_TEST_TCP_SERVER_PORT;
   const targetOpts = {
      listenPort: tcpPort,
      listenAddr: tcpHost,
      setNoDelay: true,
      echoBack: true  // echo back data sent by a client
   };

   // BENEV_AGENT.blastTcpPort is what everything else thinks the agent is listening on
   // opts.agentBlastTcpPort is what the agent is actually listening on
   // This forwards traffic attempting to connect to the agent to where it actually is
   const bwcapperArgs = {
      'listen-port': BENEV_AGENT.blastTcpPort,
      'forward-port': opts.agentBlastTcpPort,
   }

   describe('Test Network Continuity', function () {
      this.timeout(100*1000);

      var target = null;

      // free vars: target
      before('Starting TCP Target', (done) => {
         target = netdata.tcpTarget(targetOpts, () => {
            done();
         });
      });

      // free vars: target
      after('Stopping TCP Target', (done) => {
         target.stop(() => {
            done();
         });
      });

      let testDesc = testCaseSpec.pattern;

      const metricsmap = createMetricsMap();

      after('Writing metrics', function (done) {
         writeAndClearMetricsMap(metricsmap, testDesc);
         done();
      });

      const dataOpts = {
         // This (LARGE/8) file size is optimal for this test.
         // It allows for enough time to interrupt the connection.
         // It is also not so large that with a bandwidth cap it times out.
         xferFilename: makeDataFile(BENEV_FILESIZE.LARGE / 8),
         optionString: ``,
         peer: ``,
         metricsmap: metricsmap
      };

      _.each(testCaseSpec.networkDropTimeMs, (dropTimeMs) => {
         const testCaseDesc = `Data transfer should resume after a disconnect of ${dropTimeMs} milliseconds`;
         runTestThroughBenevProxiesAndBWCapper(opts, bwcapperArgs, testCaseDesc,
                                 testCaseSpec, (destInfo) => {
            var hadChanceToInterrupt = false;

            // Setup transfer interuption
            setTimeout(() => { // Interrupt connection after 1500 ms
               console.log('Start Interrupting Connection');
               killBWCapper(() => {
                  console.log('Finished Interruption Connection');
                  hadChanceToInterrupt = true;
                  setTimeout(() => { // Resume connection after a downtime
                     console.log('Resuming Connection - Relaunching Bandwidth Capper');
                     launchBWCapper(bwcapperArgs, () => { // The bandwidth capper has been relaunched
                        console.log('Connection Resumed');
                     });
                  }, dropTimeMs);
               });
            }, 1500);

            // Move test details over to dataOpts.optionString
            dataOpts.optionString = destInfo.optionString;
            dataOpts.peer = destInfo.peer;
            // Transfer
            return new Promise((resolve, reject) => {
               streamData(destInfo, dataOpts).then(() => {
                  console.log('Transfer finished');
                  expect(hadChanceToInterrupt).to.equal(true);

                  // Verify reconnect counter increased
                  checkReconnectCount(1, resolve);
               });
            });
         });
      });
   });
}


/*
 * Runs a test that compares the transfer speed of protocols.
 */
function compareProtocols (opts, testCaseSpec) {
   // The test server (target) options
   const tcpHost = BENEV_TEST_TCP_SERVER_HOST;
   const tcpPort = BENEV_TEST_TCP_SERVER_PORT;
   const targetOpts = {
      listenPort: tcpPort,
      listenAddr: tcpHost,
      setNoDelay: true,
      echoBack: true  // echo back data sent by a client
   };

   describe(`Compare time between protocols - ${testCaseSpec.pattern}` , function () {
      const testTimeout = 20 * 1000;
      const testNum =
         testCaseSpec.transportModes.length *
         testCaseSpec.openChanPeers.length *
         testCaseSpec.channelFlags.length;
      this.timeout(testTimeout * testNum);

      var target = null;

      // free vars: target
      before('Starting TCP Target', (done) => {
         target = netdata.tcpTarget(targetOpts, () => {
            done();
         });
      });

      let protocolTimes = {};

      _.each(testCaseSpec.transportModes, (protocol) => {
         protocolTimes[protocol] = 0;
      });

      // free vars: target
      after('Stopping TCP Target', (done) => {
         // Find the best time
         let bestTime = Number.MAX_VALUE;
         _.each(protocolTimes, (time, protocol) => {
            console.log(protocol + ' Protocol: ' + time);
            bestTime = Math.min(bestTime, time);
         });
         // Make sure the expected protocol had the fastest transfer time
         expect(bestTime).to.equal(protocolTimes[opts.expectedBest]);
         // If the test took a short amount of time it is likely that data throughput was not a large factor in the time taken
         // This would mean that you can't make a meaningful comparison between the protocols
         expect(bestTime).to.be.greaterThan(5000);

         target.stop(() => {
            done();
         });
      });

      let testDesc = testCaseSpec.pattern;

      const metricsmap = createMetricsMap();

      after('Writing metrics', function (done) {
         writeAndClearMetricsMap(metricsmap, testDesc);
         done();
      });

      const dataOpts = {
         xferFilename: makeDataFile(BENEV_FILESIZE.MEDIUM / 2),
         optionString: ``,
         peer: ``,
         metricsmap: metricsmap
      };

      const testCaseDesc = `Time data transfer`;
      it(testCaseDesc, function(done) {
         runBundledTestsThroughBenevProxies(opts, testCaseDesc,
            testCaseSpec, (destInfo) => {
               // Move test details over to dataOpts.optionString
               dataOpts.optionString = destInfo.optionString;
               dataOpts.peer = destInfo.peer;

               // Transfer
               return new Promise((resolve, reject) => {
                  console.log('Transfer started for a ' + destInfo.xportMode + ' test');
                  let startTime = Date.now();
                  streamData(destInfo, dataOpts).then(() => {
                     console.log('Transfer finished for a ' + destInfo.xportMode + ' test');
                     // TODO - Track performance for each individual case
                     protocolTimes[destInfo.xportMode] += Date.now() - startTime;

                     resolve();
                  });
               });
         }).then(() => {
            done();
         });
      });
   });
}


/*
 * Set up a Benev client/server connection and a pair of Benev proxies.
 * Run parameterized tests through the proxy connection one after another.
 * This should be used as a single test instead of many individual ones.
 *
 * XXX Note testFunc must return a promise.
 */
function runBundledTestsThroughBenevProxies(options, testCaseDesc, testCaseSpec, testFunc) {
   let tests = [];

   // Dynamicallly construct tests based on the combination of the above
   // parameters
   _.each(testCaseSpec.transportModes, (xportMode) => {
      let opts = Object.assign({}, options); // Make a copy of the options
      opts.blastProtocol = xportMode;

      function startTest() {
         console.log(`Start benev peers with ${xportMode}`);
         return launchAndStartBenevPeers(opts);
      }

      function endTest() {
         console.log('Stop benev peers');
         return stopBenevPeers().then(() => {
            return new Promise((res) => {
               checkForConnectErrorCode(opts, null, res);
            });
         });
      }

      _.each(testCaseSpec.openChanPeers, (openChanPeer) => {
         _.each(testCaseSpec.channelFlags, (chFlag) => {
            const testOpts = {
               xportMode: xportMode,
               openChanPeer: openChanPeer,
               chFlag: chFlag,
               enableRawChannel: opts.enableRawChannel
            };

            tests.push(() => {
               console.log(testCaseDesc + ' with ' +
               `(${testOpts.xportMode}, ${testOpts.openChanPeer}-open-channel, ` +
               `channelFlags 0x${testOpts.chFlag.toString(16)}) rawChannel` +
               `${testOpts.enableRawChannel}`);

               return startTest()
                  .then(() => runProxyTestCase(testOpts, testFunc))
                  .then(() => endTest());
            });
         });
      });
   });

   return new Promise((res) => {
      function runTests(index) {
         if (index >= tests.length) {
            res();
            return;
         }

         let test = tests[index];
         test().then(() => {
            runTests(index + 1);
         });
      }

      runTests(0);
   });
}


/*
 * Set up a Benev client/server connection, a pair of Benev proxies, and an optional bandwidth capper.
 * Run parameterized tests through the proxy connection and bandwidth capper.
 *
 * XXX Note testFunc must return a promise.
 */
function runTestThroughBenevProxiesAndBWCapper(options, bwcapperArgs, testCaseDesc, testCaseSpec, testFunc) {
   // Dynamicallly construct tests based on the combination of the above
   // parameters
   _.each(testCaseSpec.transportModes, (xportMode) => {
      let opts = Object.assign({}, options); // Make a copy of the options
      opts.blastProtocol = xportMode;

      describe('Run tests through a BENeV proxy', function () {
         // Bandwidth capper is optional, but it must launch before the benevPeers
         if (bwcapperArgs) {
            // Before/after depends on transport mode
            beforeEach(`Start benev peers with ${xportMode} and launch bandwidth capper`, (done) => {
               launchBWCapper(bwcapperArgs, () => {
                  launchAndStartBenevPeers(opts).then(done);
               })
            });

            afterEach('Stop benev peers and kill bandwidth capper', (done) => {
               stopBenevPeers().then(() => {
                  killBWCapper(() => {
                     checkForConnectErrorCode(opts, null, done);
                  });
               });
            });
         } else {
            // Before/after depends on transport mode
            beforeEach(`Start benev peers with ${xportMode}`, (done) => {
               launchAndStartBenevPeers(opts).then(done);
            });

            afterEach('Stop benev peers', (done) => {
               stopBenevPeers().then(() => {
                  checkForConnectErrorCode(opts, null, done);
               });
            });
         }

         _.each(testCaseSpec.openChanPeers, (openChanPeer) => {
            _.each(testCaseSpec.channelFlags, (chFlag) => {
               const testOpts = {
                  xportMode: xportMode,
                  openChanPeer: openChanPeer,
                  chFlag: chFlag,
                  enableRawChannel: opts.enableRawChannel
               };
               generateProxyTestCase(testCaseDesc, testOpts, testFunc);
            });
         });
      });
   });
}


/*
 * Generate a uniquely named test case for each of the combination of the
 * test options and record options to appropriate csv file
 *
 * XXX Note testFn must return a promise.
 */
function generateProxyTestCase(testCaseDesc, testOpts, testFn) {
   it(testCaseDesc + ' with ' +
      `(${testOpts.xportMode}, ${testOpts.openChanPeer}-open-channel, ` +
      `channelFlags 0x${testOpts.chFlag.toString(16)}) rawChannel ` +
      `${testOpts.enableRawChannel}`, function (done) {
         runProxyTestCase(testOpts, testFn).then(() => {
            done();
         });
      });
}

/*
 * Start Benev proxies and run test - Two cases:
 *
 * VVC client opens proxy channel: (openChanPeer === 'client')
 *    tcpClient ==>
 *        [TCP:dnHost:dnPort]:BenevClientProxy:VVC <==> VVC:BenevServerProxy:TCP ==>
 *    [TCP:upHost:upPort]:tcpTarget
 *
 * VVC server opens proxy channel: (openChanPeer === 'server')
 *    tcpClient ==>
 *        [TCP:dnHost:dnPort]:BenevServerProxy:VVC <==> VVC:BenevClientProxy:TCP ==>
 *    [TCP:upHost:upPort]:tcpTarget
 *
 * XXX Note testFn must return a promise.
 */
function runProxyTestCase(testOpts, testFn) {
   // downstream and upstream TCP connection params
   const dnHost = BENEV_PROXY_LISTEN_HOST,
         dnPort = testOpts.openChanPeer === 'server' ? BENEV_PROXY_SERVER_LISTEN_PORT : BENEV_PROXY_CLIENT_LISTEN_PORT;
   const upHost = testOpts.openChanPeer === 'server' ? BENEV_CLIENT.hostIp : BENEV_AGENT.hostIp,
         upPort = BENEV_TEST_TCP_SERVER_PORT;

   // BenevProxy requires the 'channel-flags' param to be in hex
   const hexChFlag = '0x' + testOpts.chFlag.toString(16);

   const clientParams = {
      'benevPeer': 'client',
      'name': 'BenevProxyChan*',
      'channel-flags': hexChFlag,  // "start-proxy" command takes hex string
      'enableRawChannel': testOpts.enableRawChannel
      // 'mode', 'host' and 'port' added below
   };
   _.extend(clientParams, getProxyParams('client',
                                         testOpts.openChanPeer,
                                         dnHost,
                                         dnPort,
                                         upHost,
                                         upPort,
                                         0)); // Bandwidth cap

   const serverParams = {
       'benevPeer': 'server',
       'name': 'benevProxyChan*',
       'channel-flags': hexChFlag,
       'enableRawChannel': testOpts.enableRawChannel
       // 'mode', 'host' and 'port' added below
   };
   _.extend(serverParams, getProxyParams('server',
                                         testOpts.openChanPeer,
                                         dnHost,
                                         dnPort,
                                         upHost,
                                         upPort,
                                         0)); // Bandwidth cap

   if (testOpts.featureName) {
      clientParams['feature-name'] = testOpts.featureName;
      serverParams['feature-name'] = testOpts.featureName;
   }

   // Start client and server proxies as promises and start streaming data
   // after the both of them are done (starting)
   const specs = [clientParams, serverParams];
   let startProxyProms = _.map(specs, (param) => startBenevProxy(param));

   // similarly both client and server proxies need to be stopped
   function stopProxies() {
      let proms = _.map(specs, (param) => stopBenevProxy(param));

      return Promise.all(proms);
   }

   const destInfo = {
      // where should the test client connect to?
      port: dnPort,
      host: dnHost,
      optionString: `${testOpts.xportMode};${testOpts.openChanPeer}-open-channel;` +
                    ((testOpts.chFlag.toString(16)=='0')? `accept-NC`:`decline-NC`),
      peer: testOpts.openChanPeer,
      xportMode: testOpts.xportMode
   };

   return Promise.all(startProxyProms)
      .then(() => testFn(destInfo))
      .then(() => stopProxies());
}

/*
 * Starts up a TCP client and target and then stream end-to-end data between
 * them, with the option to verify data integrity.
 */
function streamData(destInfo, dataOpts) {
   return new Promise((resolve) => {
      netdata.streamE2EData(destInfo, dataOpts, () => {
         resolve();
      });
   });
}

/*
 * A utility to create (if necessary) or reuse (if possible) a file of a
 * specified size containing a varying data pattern.  The given size is
 * rounded up to accommodate a whole number of fixed-length text lines,
 * and the file name incorporates that rounded-up size.
 *
 * Returns the name of the file, even if there are problems with the file.
 * (For example, the file might have an unexpected size, or perhaps we
 * were unable to create it.)  Let the test that tries to use the file
 * handle any such issues.
 */
function makeDataFile(size) {

   const lineEnd = '\n';
   const numDigits = Math.floor(1 + Math.log10(size));
   const zeroes = ''.padEnd(numDigits, '0');
   const lineLength = zeroes.length + lineEnd.length;
   const numLines = Math.floor((size + lineLength - 1) / lineLength);
   const actualSize = numLines * lineLength;
   const fname = 'benev-data-file-' + actualSize + '.txt';

   let stat;
   try {
      stat = fs.statSync(fname);
      if (stat.size === actualSize) {
         console.log('Using existing data file "' + fname + '"');
      } else {
         console.log('Warning: using existing data file "' + fname +
                     '" with unexpected size ' + stat.size +
                     ' (expected ' + actualSize + ')');
      }
   } catch (err) {
      // File does not exist, we'll just go ahead and create it
   }

   if (undefined === stat) {
      try {
         let fd = fs.openSync(fname, 'w', 0o660);
         for (let n = 0 ; n < numLines ; ++n) {
            fs.writeSync(fd, (zeroes + n).slice(-numDigits) + lineEnd);
         }
         fs.closeSync(fd);
         console.log('Created data file "' + fname + '"');
      } catch (err) {
         console.log(err);
      }
   }

   return fname;
}

/*
 * Creates a map holding the test metrics data from BENeV tests,
 * dividing them by server, client, and the recorded metric.
 *
 * Allows for easy storage (when collecting data in finishReceive
 * methods in netdata.js) and extraction of values (when writing
 * values to csv file in writeMapToFile of library.js)
 */
function createMetricsMap() {
   return {
      server: {
         avgRtt: {},
         totalTime: {}
      },
      client: {
         avgRtt: {},
         totalTime: {}
      }
   }
}

/*
 * Take in the metricsmap created as an argument and write out its contents
 * into a csv file after it has been populated by the tests. The pattern
 * argument is the test pattern description given from test.js and extracted
 * from the traffic.json file.
 *
 * Once completed, clear out the map to ensure accurate data is being
 * recorded each run.
 */
function writeAndClearMetricsMap(metricsmap, pattern) {
   //write out csv file for server data
   writeMapToFile(metricsmap.server, pattern + '(server)');
   //write out csv file for client data
   writeMapToFile(metricsmap.client, pattern + '(client)');
   //clear map contents here
   metricsmap.server = {};
   metricsmap.client = {};
}

/*
 * Traffic Generator Tests.
 * Set up a Benev client/server connection for each combination of parameters.
 * Create a pair of Benev proxies (Client and server) for each traffic pattern.
 * Run parameterized tests (file transfer and customized data) through the
 *    proxy connections.
 */
function runTGTests(options, testSuiteName, testCaseDesc) {
   // Reference char while sending/validating data for the first time.
   let refChar = refDataStr.slice(-1);
   let testDesc = options.patternDescription;

   const metricsmap = createMetricsMap();

   after('Writing metrics', function (done) {
      writeAndClearMetricsMap(metricsmap, testDesc);
      done();
   });

   // Dynamicallly construct tests based on the combination of the above
   // parameters
   _.each(options.bandwidthCap, (bandwidthCap) => {
      _.each(options.transportModes, (xportMode) => {
         const opts = {
            blastProtocol: xportMode,
            agentBlastTcpPort: options.agentBlastTcpPort,
            clientBlastTcpPort: options.clientBlastTcpPort,
            agentBlastUdpPort: options.agentBlastUdpPort,
            clientBlastUdpPort: options.clientBlastUdpPort,
            agentIp: BENEV_AGENT.ip,
            clientIp: BENEV_CLIENT.ip,
            alwaysLoadSSLLib: options.alwaysLoadSSLLib,
            useSSL: options.useSSL,
            certFilePathName: options.certFilePathName,
            checkRevocation: options.checkRevocation,
            enableRawChannel: false // Raw Channel only for proxy
         };

         describe(`${testSuiteName}`, function () {
            beforeEach(`Start benev peers with ${xportMode}`, (done) => {
               writeConfig('server', 'w', `{ "Benev.maxBandwidthKbps": ` +
                           `"${bandwidthCap}" }`, options).then(() => {
                  // Explicitly set raw type for Raw Channel proxy
                  setRawTypeRegKeys('server', opts.blastProtocol).then(() => {
                     launchAndStartBenevPeers(opts).then(() => {
                        done();
                     })
                     .catch(err => {
                        done(err);
                     });
                  });
               });
            });

            afterEach(`Stop benev peers`, (done) => {
               stopBenevPeers().then(() => {
                  checkForConnectErrorCode(options, null, done);
               });
            });

            _.each(options.openChanPeers, (openChanPeer) => {
               _.each(options.channelFlags, (chFlag) => {
                  const testOpts = {
                     trafficPatterns: options.trafficPatterns,
                     xportMode: xportMode,
                     openChanPeer: openChanPeer,
                     chFlag: chFlag,
                     bandwidthCap: bandwidthCap,
                     wireBandwidthCap: options.wireBandwidthCap,
                     metricsmap: metricsmap,
                     enableRawChannel: options.enableRawChannel
                  };
                  generateTGTestCase(testCaseDesc, testOpts);
               });
            });
         });
      });
   });

   // Generate a uniquely named test case for each combination of the options
   function generateTGTestCase(testCaseDesc, testOpts) {
      it(testCaseDesc + ' with ' +
         `(${testOpts.xportMode}, ${testOpts.openChanPeer}-open-channel, ` +
         `channelFlags 0x${testOpts.chFlag.toString(16)}, bandwidth cap ` +
         `${testOpts.bandwidthCap.toString()}Kb/s, ` + `wireBandwidthCap ` +
         `${testOpts.wireBandwidthCap.toString()}b/s) rawChannel ` +
         `${testOpts.enableRawChannel}`, function (done) {
         runTGTestCase(testOpts).then(() => {
            done();
         });
      });
   }

   // Callback for tcpTarget to validate received data and send ack for custom
   // data scenario. This is triggered after tcpTarget gets a connection.
   function tcpTargetCustomConnCb(sock, dataSize, ackSize) {
      // Number of acknowledged bytes
      let lastAckByteNum = 0;
      // Number of received bytes
      let recvByteNum = 0;
      // Data not yet acknowledged
      let unackedData = '';
      // Last character of data acknowledged
      let ackedLastChar = refChar;
      // Last character sent as acknowledgement
      let lastAckCharSent = refChar;
      // Number of acknowledgements sent
      let numOfAcksSent = 0;

      sock.setNoDelay(true);

      sock.on('data', (data) => {
         recvByteNum += data.length;
         unackedData += data;
         while (recvByteNum - lastAckByteNum >= dataSize) {
            // Validate and Send an ack
            if(validateData(unackedData, ackedLastChar, dataSize)) {
               const ackData = generateData(lastAckCharSent, ackSize);
               sock.write(ackData);
               lastAckCharSent = ackData.slice(-1);
               numOfAcksSent++;
            } else {
               console.log("Failed while validating data:\n",
                           "data: " + unackedData + "\n",
                           "ackedLastChar: " + ackedLastChar + "\n",
                           "dataSize:" + dataSize + "\n",
                           "This test will fail");
               should.fail();
            }
            lastAckByteNum += dataSize;
            ackedLastChar = unackedData.slice(dataSize - 1, dataSize);
            unackedData = unackedData.slice(dataSize);
         }
      });

      sock.on('error', (err) => {
         console.log('Got error on server socket:', err.message);
      });

      sock.on('close', () => {
         console.log(`****** Target server: Connection closed: ` +
                     `${recvByteNum} bytes received. ******`);
      });
   };

   /*
    * For each traffic pattern, do the following in parallel:
    * - Start tcpTarget listener (receiver) on the receiverPort
    * - Start client and server Benev proxies
    * - Create a TCP sender on senderPort and Start streaming file/custom data
    * - Stop Benev proxies and tcpTarget
    *
    * Create a promise for each of the above iterations and return a promise
    * of these promises.
    * 'testOpts' contains trafficParams with all port details for each proxy
    *
    * Data transfer flow when client sends data to server:
    * VVC client opens proxy channel: (openChanPeer === 'client')
    *    tcpServer ==> [TCP:dnHost:dnPort]:BenevClientProxy:VVC
    *       <==> VVC:BenevServerProxy:TCP ==> [TCP:upHost:upPort]:tcpTarget
    *
    */
   function runTGTestCase(testOpts) {
      let allPromisesList = [];
      let proxyCount = 0;

      _.each(testOpts.trafficPatterns, (proxyParams) => {
         proxyCount++;

         // downstream and upstream TCP connection params
         const dnHost = BENEV_PROXY_LISTEN_HOST,
               dnPort = testOpts.openChanPeer === 'server' ? proxyParams.senderPortServer : proxyParams.senderPortClient;
         const upHost = testOpts.openChanPeer === 'server' ? BENEV_CLIENT.hostIp : BENEV_AGENT.hostIp,
               upPort = proxyParams.receiverPort;

         // BenevProxy requires the 'channel-flags' param to be in hex
         const hexChFlag = '0x' + testOpts.chFlag.toString(16);

         // Create client and server BenevProxy params
         const clientParams = {
            'benevPeer': 'client',
            'name': proxyParams.proxyName + '*',
            'channel-flags': hexChFlag,  // "start-proxy" command takes hex str
            'enableRawChannel': (proxyCount <= 2) ? testOpts.enableRawChannel : false
            // 'mode', 'host' and 'port' added below
         };
         _.extend(clientParams,
                  getProxyParams('client',
                                 testOpts.openChanPeer,
                                 dnHost,
                                 dnPort,
                                 upHost,
                                 upPort,
                                 0)); // Bandwidth cap

         const serverParams = {
            'benevPeer': 'server',
            'name': proxyParams.proxyName + '*',
            'channel-flags': hexChFlag,
            'enableRawChannel': (proxyCount <= 1) ? testOpts.enableRawChannel : false
            // 'mode', 'host' and 'port' added below
         };
         _.extend(serverParams,
                  getProxyParams('server',
                                 testOpts.openChanPeer,
                                 dnHost,
                                 dnPort,
                                 upHost,
                                 upPort,
                                 testOpts.bandwidthCap));

         const specs = [clientParams, serverParams];

         function stopProxies() {
            let proms = _.map(specs, (param) => stopBenevProxy(param));
            return Promise.all(proms);
         }

         let tcpTargetOpts = {
            listenPort: upPort,
            listenAddr: BENEV_TEST_TCP_SERVER_HOST
         };

         // Sender needs destination info to stream data
         const destInfo = {
            port: dnPort,
            host: dnHost,
         };

         let trafficInfo = {
            proxyName: proxyParams.proxyName,
            optionString: `${testOpts.xportMode};${testOpts.openChanPeer}-open-channel;` +
                          ((testOpts.chFlag.toString(16)=='0')? `accept-NC`:`decline-NC`),
            peer: testOpts.openChanPeer,
            metricsmap: testOpts.metricsmap
         };

         let streamDataFunc;
         let tcpTargetFunc;
         if(proxyParams.fileTransfer) {
            // File transfer on this proxy
            let fileSize = BENEV_FILESIZE[proxyParams.fileSize.toUpperCase()];
            if(!fileSize) {
               console.log("Invalid json file. Invalid file size: " +
                           proxyParams.fileSize +
                           " for proxy: " + proxyParams.proxyName);
               should.fail();
            }
            trafficInfo.xferFilename = makeDataFile(fileSize);
            tcpTargetOpts.echoBack = true;

            // Call streamData for simple file transfer
            streamDataFunc = _.partial(streamData, destInfo, trafficInfo);
            // Use the default connectCb provided by tcpTarget
            tcpTargetFunc = _.partial(netdata.tcpTarget, tcpTargetOpts);
         } else {
            // Custom data on this proxy
            trafficInfo.packetSize = proxyParams.packetSize;
            trafficInfo.packetDelay = proxyParams.packetDelay;
            trafficInfo.noOfPackets = proxyParams.noOfPackets;
            trafficInfo.ack = proxyParams.ackTraffic;
            trafficInfo.refChar = refChar;

            // Call streamCustomData for custom data transfer
            streamDataFunc = _.partial(streamCustomData,
                                       destInfo, trafficInfo,
                                       generateData, validateData);
            // Use tcpTargetCustomConnCb instead of the the default
            // tcpTargetConnectCb which echoes back everything it receives.
            tcpTargetFunc = _.partial(netdata.tcpTarget, tcpTargetOpts, _,
                                       (sock) => {
                                          tcpTargetCustomConnCb(sock,
                                             trafficInfo.packetSize,
                                             trafficInfo.ack.packetSize);
                                       });
         }

         // Creating a new promise for each proxy and storing it
         allPromisesList.push(new Promise((resolve) => {
            // Start receiver tcpTarget
            let tcpTarget = tcpTargetFunc(() => {
               // Start client and server proxies as promises and start
               // streaming data after the both of them are done (starting)
               Promise.all(_.map(specs, (param) => startBenevProxy(param)))
                  .then(streamDataFunc) // Stream data (file or custom)
                  .then(stopProxies) // Stop Benev proxies
                  .then(() => tcpTarget.stop(resolve)); // Stop tcpTarget
            });
         }));
      });

      return Promise.all(allPromisesList);
   }

   // Starts up a TCP client to connect to tcpTarget and stream custom data
   // Returns a promise
   function streamCustomData(destInfo, dataOpts, generatorFunc, validatorFunc) {
      return new Promise((resolve) => {
         netdata.streamE2ECustomData(destInfo, dataOpts,
                                       generatorFunc, validatorFunc, () => {
            resolve();
         });
      });
   }

   // Generates data by looping over refDataStr
   function generateData(refChar, size) {
      let dataStr = refDataStr;
      let start = dataStr.indexOf(refChar) + 1;

      for (var i = 0; i < (size / dataStr.length) + 2; i++) {
         dataStr += dataStr;
      }

      return dataStr.slice(start, start + size);
   }

   // Validates data with refChar and refDataStr as references
   function validateData(data, refChar, size) {
      let dataStr = refDataStr;
      let start = dataStr.indexOf(refChar) + 1;

      for (var i = 0; i < (size / dataStr.length) + 2; i++) {
         dataStr += dataStr;
      }

      return data.slice(0, size) === dataStr.slice(start, start + size);
   }
}

/*
 * Convert data from the generated metricsmaps to string so that they
 * can be written to csv files. These csv files will be read by the
 * Jenkins plot plugin and display a line graph comparing different
 * builds and their test metrics' values.
 */
function writeMapToFile(metricsmap, filename) {
   // Key is test options, value is metric data (avg rtt or total transfer time)
   const metricKeys = Object.keys(metricsmap).map(metric => {
      let data = '';
      let tests = '';
      const optionkeys = Object.keys(metricsmap[metric]).map(testOptions => {
         tests += testOptions + ',';
         let sum = 0;
         const proxyKeys = Object.keys(metricsmap[metric][testOptions]).map(proxy => {
            sum += metricsmap[metric][testOptions][proxy];
         });
         // Get the average of the rtt values if there are multiple proxies
         let numKeys = Object.keys(metricsmap[metric][testOptions]).length;
         data += (sum/numKeys) + ',';
      });
      // Don't write the file if there was no data stored in map
      // (ex. 1 Channel - avg rtt)
      if (tests != '') {
         fs.writeFileSync(filename + `-${metric}.csv`,tests + '\r\n' + data);
         data = '';
         tests = '';
      }
   });
}

/*
 * Return proxy params based on open-channel-peer
 * Ex: if client opens channel, it's mode is tcp2vvc
 *    and server's mode is vvc2tcp
 */
function getProxyParams(peer, opChPeer, dnHost, dnPort, upHost, upPort, cap) {
   if (opChPeer === peer) {
      // peer opens channel so it starts a TCP server at the
      // downstream host:port and connects to VVC peer
      return {
         mode: 'tcp2vvc',
         host: dnHost,
         port: dnPort,
         bandwidthCap: cap
      };
   } else {
      // peer accepts channel so it connects to upstream TCP host:port
      return {
         mode: 'vvc2tcp',
         host: upHost,
         port: upPort,
         bandwidthCap: 0
      };
   }
};


/*
 * Validate Node Server is up.
 */
function validateNodeServer(peerMode, doneCb) {
   var reqUrl = `${benevServerUrl(peerMode)}/alive`;

   request.get(reqUrl, { timeout: 2000 }, function (err, res, body) {
      if (err) {
         console.log("Node Server is not up on: " + peerMode + ", " + err);
         expect.fail("Node Server is not up on: " + peerMode + ", " + err);
      } else {
         expect(res.statusCode).to.equal(200);
         expect(res.body).to.equal('node server alive');
      }
      doneCb(err);
   });
}

/*
 * Validate Both Agent and Client Node Server's are up.
 *
 * TODO: Remove now that test.js waits for node servers to become alive
 */
function validateNodeServersAlive(done) {
   validateNodeServer('server', (err) => {
      if (err) {
         done(err);
      } else {
         validateNodeServer('client', done);
      }
   });
}

/*
 * Launch the BenevPeer as a child process
 */
function launchBenevPeer(peerMode, options, done) {
   var reqUrl = `${benevServerUrl(peerMode)}/${peerMode}/launch/`;
   var optionStr = '?options=';

   if (peerMode === 'server') {
       optionStr += (options ? JSON.stringify(options) : '');
   }
   reqUrl += optionStr;

   request.post(reqUrl, function (err, res) {
      expect(res.statusCode).to.equal(200);
      expect(res.body).to.equal('launch');
      done(err);
   });
}


/*
 * Launch netcat as a child process
 */
function launchNetcat(peerMode, fileToSend, port, done) {
   console.log('Launching netcat in... ', peerMode);
   var reqUrl = `${benevServerUrl(peerMode)}/${peerMode}/launch-ncat/`;
   reqUrl += `${BENEV_AGENT.ip}/${BENEV_CLIENT.ip}/`;
   reqUrl += `${fileToSend}/`;
   reqUrl += `${port}/`;

   request.post(reqUrl, function (err, res) {
      // comment out if running test with many failures
      expect(res.statusCode).to.equal(200);
      console.log("COMPLETE ACK: ***** *****");
      // if continuing tests despite failures
      if (res.statusCode == 200 && peerMode == "client"){
         console.log("SUCCESS!");
         fs.appendFile("ackCumulative.txt", res.body, () => {
            fs.appendFile("ackCumulative.txt", '\n', () => {
               console.log("Writing ack to ackCumulative.txt...");
            });
         });
      }
      console.log(res.body);
      done();
   });
}


/*
 * Start the BenevPeer. For the server, this means start listening; for the
 * client, this means connect to the server.
 *
 * If we are starting the client and the BSG is being used, this also adds
 * a route to the BSG.
 */
function startBenevPeer(config, done) {
   function startBenevPeerInternal() {
      var reqUrl = (`${benevServerUrl(config.peerMode)}/${config.peerMode}/start/` +
                    `?protocol=${config.protocol}&host=${config.host}` +
                    `&tcpPort=${config.tcpPort}&udpPort=${config.udpPort}` +
                    `&useSSL=${config.useSSL}` +
                    `&certFilePathName=${config.certFilePathName}` +
                    `&checkRevocation=${config.checkRevocation}` +
                    `&alwaysLoadSSLLib=${config.alwaysLoadSSLLib}` +
                    `&enableRawChannel=${config.enableRawChannel}`);

      request.post(reqUrl, function (err, res) {
         if (done) {
            var res = validateResponse(err, res, "start", "BlastSocket_Start FAILED");
            if (res == ('BlastSocket_Start FAILED')) {
               done(1); // done with error
            } else {
               done();
            }
         } else {
            // ignore errors since the caller doesn't care about whether
            // this actually succeeded or not.
         }
      });
   }

   if (config.peerMode === 'server' && config.enableRawChannel) {
      setRawTypeRegKeys(config.peerMode, config.protocol)
         .then(startBenevPeerInternal);
   } else if (BSG.enabled && config.peerMode === 'client') {
   // Add a bsg route before we start the client if we need to
      const bsgParams = {
         agentIp: BSG.targetIp,
         agentPort: BSG.targetPort,
         portKey: BENEV_PORT_KEY
      };

      addBSGRoute(bsgParams, startBenevPeerInternal);
   } else {
      startBenevPeerInternal();
   }
}


/*
 * Stop BenevPeer - this means stopping the blastSocket.
 */
function stopBenevPeer(peerMode, done) {
   var reqUrl = `${benevServerUrl(peerMode)}/${peerMode}/close`;

   request.delete(reqUrl, (err, res, body) => {
      validateResponse(err, res, "close");
      done(err);
   });
}


/*
 * Kill BenevPeer - Tell the BenevPeer child process to exit.
 * If the BenevPeer is the client, remove the BSG route if there is one.
 */
function killBenevPeer(peerMode, done) {
   var reqUrl = `${benevServerUrl(peerMode)}/${peerMode}/exit`;

   request.delete(reqUrl, function (err, res, body) {
      expect(res.body).to.equal('exit');

      if (peerMode === 'client' && BSG.enabled) {
         const bsgParams = {
            portKey: BENEV_PORT_KEY
         };

         removeBSGRoute(bsgParams, done);
      } else {
         done();
      }
   });
}


/*
 * Dump BenevPeer process - Send command to dump BenevPeer process.
 */
function dumpBenevPeer(peerMode, done) {
   var reqUrl = `${benevServerUrl(peerMode)}/${peerMode}/dump`;

   request.delete(reqUrl, function (err, res, body) {
      expect(res.body).to.equal('dumpdone');
      done();
   });
}


/*
 * Launch bandwidth Capper - Launch the bandwidth capper
 */
function launchBWCapper(args, done) {
   if (args['listen-addr'] === undefined) {
      console.log('No listen-addr specified for bandwidth capper. Using default.');
      args['listen-addr'] = BENEV_AGENT.targetIp;
   }

   if (args['forward-addr'] === undefined) {
      console.log('No forward-addr specified for bandwidth capper. Using default.');
      args['forward-addr'] = BENEV_AGENT.targetIp;
   }

   let url = `${benevServerUrl('server')}/launch-bwcapper`;
   let paramStr = _.chain(args)
      .pairs()    // convert into pairs of [name, value] in an array
      .map((nvp) => {return [nvp[0], nvp[1]].join('=');}) // "name=value"
      .value()    //unwrap
      .join('&'); // "name1=value1&name2=value2..."
   let fullURL = url + (paramStr === '' ? '' : '?' + paramStr);
   request.post(fullURL, (err, res) => {
      validateResponse(err, res, "launch-bwcapper");
      // Give bandwidth capper time to startup
      setTimeout(done, 500);
   });
}


/*
 * Kill bandwidth Capper - Kill the bandwidth capper
 */
function killBWCapper(done) {
   var reqUrl = `${benevServerUrl('server')}/kill-bwcapper`;
   request.delete(reqUrl, (err, res) => {
      validateResponse(err, res, "kill-bwcapper");
      done();
   });
}


/*
 * Adds a route to the BSG (Blast Secure Gateway)
 */
function addBSGRoute(args, done) {
   let url = `${benevServerUrl('bsg')}/add-bsg-route`;
   let paramStr = _.chain(args)
      .pairs()    // convert into pairs of [name, value] in an array
      .map((nvp) => {return [nvp[0], nvp[1]].join('=');}) // "name=value"
      .value()    //unwrap
      .join('&'); // "name1=value1&name2=value2..."
   let fullURL = url + (paramStr === '' ? '' : '?' + paramStr);
   request.post(fullURL, (err, res) => {
      let routeToken = validateResponse(err, res, "add-bsg-route");

      // Pass the route token from the BSG to the client
      setRouteToken(routeToken, done);
   });
}


/*
 * Remove a route from the BSG (Blast Secure Gateway)
 */
function removeBSGRoute(args, done) {
   let url = `${benevServerUrl('bsg')}/remove-bsg-route`;
   let paramStr = _.chain(args)
      .pairs()    // convert into pairs of [name, value] in an array
      .map((nvp) => {return [nvp[0], nvp[1]].join('=');}) // "name=value"
      .value()    //unwrap
      .join('&'); // "name1=value1&name2=value2..."
   let fullURL = url + (paramStr === '' ? '' : '?' + paramStr);
   request.delete(fullURL, (err, res) => {
      validateResponse(err, res, "remove-bsg-route");
      done();
   });
}

/*
 * Set the client's route token
 */
function setRouteToken(routeToken, done) {
   const path = `set-route-token/?routeToken=${routeToken}`;
   const desc = 'Set the route token';
   nodeServerRequest(request.post, 'client', path, desc, (res, err) => {
      validateResponse(err, res, 'set-route-token');
      done();
   });
}


function closeChannel(peerMode, done) {
   var reqUrl = `${benevServerUrl(peerMode)}/${peerMode}/close-channel`;

   request.delete(reqUrl, function (err, res, body) {
      validateResponse(err, res, "close-channel");
      done();
   });
};


/*
 * Check BenevPeer logs for memory leaks.
 */
function memLeakCheck(peerMode, doneCb) {
   var reqUrl = `${benevServerUrl(peerMode)}/${peerMode}/memleakcheck`;
   const desc = (`Memory leak check on ${peerMode} will succeed ` +
                 `and return 0`);
   request.get(reqUrl, function (err, res, body) {
      expect(res.body).to.equal('Exited with code 0');
      doneCb();
   });
}

/*
 * Helper function to call memory leak checks
 */
function checkForMemLeak(options, doneCb) {
   if (options.doMemLeakCheck) {
      // check for mem leak in Benev Peers
      var doneCnt = 0;
      function peerDone(peer) {
         doneCnt++;
         if (doneCnt == 2) {
            doneCb();
         }
      }
      // Call mem leak check in Benev Peers
      memLeakCheck('client', _.partial(peerDone, 'client'));
      memLeakCheck('server', _.partial(peerDone, 'server'));
   } else {
      doneCb();
   }
}

/*
 * Helper function to call connection error code checks
 *
 * expectedCode is a string that will be compared with the connection error.
 * Set expectedCode to null if there should be no connection error.
 */
function checkForConnectErrorCode(options, expectedCode, doneCb) {
   if (options.doConnectErrorCheck) {
      const path = 'connect-error-check';
      const desc = 'Connect Error Check';
      nodeServerRequest(request.get, 'client', path, desc, (res, err) => {
         let data = validateResponse(err, res, path);
         expect(data).to.equal(expectedCode);
         doneCb();
      });
   } else {
      doneCb();
   }
}

/*
 * Launch both Benev client and server executables.
 */
function launchBenevPeers(opts) {
   return callAndPromise(_.partial(launchBenevPeer, 'server', opts))
      .then(() => callAndPromise(
         _.partial(launchBenevPeer, 'client', null)))
      .then(() => {
         return new Promise((resolve, reject) => {
            resolve();
         });
      });
}


/*
 * Start (establish a connection between) both Benev client and server.
 * This function calls the BenevPeersHelper for a maximum of 3 times before
 * failing to establish connection between Benev Peers.
 */
async function startBenevPeers(opts) {
   var i = 0;
   let maxRetry = 3;
   var pass = false;
   while (i < maxRetry) {
      // wait for to establish connection
      await startBenevPeersHelper(opts, i * 2)
         .then(() => {
            i = maxRetry; // to exit while loop
            pass = true;
         })
         .catch(err => {
            // Add delay between retries
            let timeout = Math.pow(2, i) * 100;
            console.log(`Could not bind to port, retrying with different port in ${timeout} ms`);
            waitMs(timeout);
            i++; // increment for retry
         });
   }
   return new Promise((resolve, reject) => {
      if (pass == true)
         resolve();
      else
         reject(new Error('startBenevPeers Failed'));
   })
}

/*
 * Start Benev server and client using provided port offset.
 * Port offset is used to retry connection with different ports.
 */
function startBenevPeersHelper(opts, portOffset) {
   var config = {};

   config.peerMode = 'server';
   config.protocol = opts.blastProtocol;
   config.host = (opts.agentTargetIp ? opts.agentTargetIp : BENEV_AGENT.targetIp);
   config.tcpPort = opts.agentBlastTcpPort + portOffset;
   config.udpPort = opts.agentBlastUdpPort;
   config.useSSL = opts.useSSL;
   config.certFilePathName = opts.certFilePathName;
   config.checkRevocation = null;
   config.alwaysLoadSSLLib = opts.alwaysLoadSSLLib;
   config.enableRawChannel = opts.enableRawChannel;

   return callAndPromise(_.partial(startBenevPeer, config)).then(() => {
            config.peerMode = 'client';
            config.protocol = opts.blastProtocol;
            config.host = (opts.clientTargetIp ? opts.clientTargetIp : BENEV_CLIENT.targetIp);
            config.tcpPort = opts.clientBlastTcpPort + portOffset;
            config.udpPort = opts.clientBlastUdpPort;
            config.useSSL = opts.useSSL;
            config.certFilePathName = null;
            config.checkRevocation = opts.checkRevocation;
            config.alwaysLoadSSLLib = opts.alwaysLoadSSLLib;
            config.enableRawChannel = null;

            return callAndPromise(_.partial(startBenevPeer, config)).then(() => {
                     return new Promise((resolve, reject) => {
                        // XXX When a BenevPeer indicates a successful "start", it is just
                        // saying that the BlastSocket connected (or started accepting
                        // connections in the case of the Benev Agent). But to start Benev
                        // Proxy, we need to be sure that the VVC session is
                        // established. Currently there's no notification from the BenevPeer
                        // for this event. So we will just have to wait a bit.
                        setTimeout(resolve, 200);
                     })
            })
         })
         .catch(err => {
            return new Promise((resolve, reject) => {
               return reject(err);
            })
         });
}


/*
 * Launches and starts both client and server.
 */
function launchAndStartBenevPeers(opts) {
   return new Promise((resolve, reject) => {
      launchBenevPeers(opts).then(() => {
         startBenevPeers(opts).then(() => {
            resolve();
         })
         .catch(err => {
            reject(err);
         });
      });
   });
}


/*
 * Stop both Benev client and server.
 */
function stopBenevPeers() {
   return callAndPromise(_.partial(stopBenevPeer, 'client'))
      .then(() => callAndPromise(_.partial(stopBenevPeer, 'server')))
      .then(() => callAndPromise(_.partial(killBenevPeer, 'client')))
      .then(() => callAndPromise(_.partial(killBenevPeer, 'server')));
}


/*
 * Sets up the config on a benev peer.
 */
function primeConfig(peerMode, defaultOpts) {
   var reqUrl = `${benevServerUrl(peerMode)}/${peerMode}/prime-config/` +
                `?defaultOpts=${JSON.stringify(defaultOpts)}`;

   return new Promise((resolve, reject) => {
      request.post(reqUrl, (err, res) => {
         validateResponse(err, res, "prime-config");
         resolve();
      });
   });
}

/*
 * Clears the config on a benev peer.
 */
function clearConfig(peerMode) {
   var reqUrl = `${benevServerUrl(peerMode)}/${peerMode}/clear-config/`;

   return new Promise((resolve, reject) => {
      request.post(reqUrl, (err, res) => {
         validateResponse(err, res, "clear-config");
         resolve();
      });
   });
}

/*
 * Write the given config strings to the BENeV config file
 */
function writeConfig(peerMode, mode, configs, options) {
   var configsObject = JSON.parse(configs);

   // No reason to write a config that we've already set
   var shouldWrite = false;
   _.mapObject(configsObject, function (value, key) {
      if (!gConfigs.hasOwnProperty(key) ||
          gConfigs[key] !== value) {
         shouldWrite = true;
      }
   });

   if (shouldWrite === false) {
      return new Promise((resolve, reject) => {
         resolve();
      });
   }

   var reqUrl = `${benevServerUrl(peerMode)}/${peerMode}/write-config/` +
                `?mode=${mode}&config=${configs}`;

   return new Promise((resolve, reject) => {
      request.post(reqUrl, (err, res) => {
         validateResponse(err, res, "write-config");
         // Update global config with new values
         Object.assign(gConfigs, configsObject);
         resolve();
      });
   });
}

/*
 * Sets up reg keys for the vvc api tests on a single beenv peer.
 */
function setVvcAPITestRegKeysPeer(peerMode, opts) {
   var reqUrl = `${benevServerUrl(peerMode)}/${peerMode}/set-vvc-api-test-reg-keys/` +
                `?opts=${JSON.stringify(opts)}`;

   return new Promise((resolve, reject) => {
      request.post(reqUrl, (err, res) => {
         validateResponse(err, res, "set-vvc-api-test-reg-keys");
         resolve();
      });
   });
}

/*
 * Sets up reg keys for the vvc api tests on all beenv peers.
 */
function setVvcAPITestRegKeys(opts) {
   return setVvcAPITestRegKeysPeer('server', opts).then(() => {
      setVvcAPITestRegKeysPeer('client', opts);
   });
}

/*
 * Cleans up the reg keys for the vvc api tests on a single benev peer.
 */
function unsetVvcAPITestRegKeysPeer(peerMode) {
   var reqUrl = `${benevServerUrl(peerMode)}/${peerMode}/unset-vvc-api-test-reg-keys/`;

   return new Promise((resolve, reject) => {
      request.post(reqUrl, (err, res) => {
         validateResponse(err, res, "unset-vvc-api-test-reg-keys");
         resolve();
      });
   });
}

/*
 * Cleans up the reg keys for the vvc api tests on all benev peers.
 */
function unsetVvcAPITestRegKeys(opts) {
   return Promise.all([
      unsetVvcAPITestRegKeysPeer('client'),
      unsetVvcAPITestRegKeysPeer('server')
   ]);
}

/*
 * Sets up reg key for the raw channel type.
 */
function setRawTypeRegKeys(peerMode, blastProtocol) {
   console.log('Setting Raw Channel Type reg key: ', blastProtocol);
   const reqUrl = `${benevServerUrl(peerMode)}/${peerMode}` +
                  `/set-raw-type-reg-keys/?blastProtocol=${blastProtocol}`;

   return new Promise((resolve, reject) => {
      request.post(reqUrl, (err, res) => {
         validateResponse(err, res, "set-raw-type-reg-keys");
         resolve();
      });
   });
}

/*
 * Start Benev Proxy for a BenevPeer. 'param' is a map of
 *     {
 *         'benevPeer': client|server,
 *         'mode': tcp2vvc|vvc2tcp,
 *         'name': channel-name,
 *         'channel-flags': OPEN_CHAN_FLAGS,
 *         'host': listening hostname,
 *         'port': listnening port number
 *     }
 */
function startBenevProxy(param) {
   if (param['enableRawChannel'] && param['mode'] === 'tcp2vvc'
                                 && param['benevPeer'] === 'server'
                                 && param['channel-flags'] === '0x800') {
      // Attempt raw channel only if server-open-channel and NC declined
      param['channel-flags'] = '0x1800';
      console.log('Requesting raw channel for proxy %s', param['name']);
   }
   delete param['enableRawChannel'];
   let url = `${benevServerUrl(param['benevPeer'])}/${param['benevPeer']}/start-proxy/`;
   let paramStr = _.chain(param)
       .omit('benevPeer')
       .pairs()   // convert into pairs of [name, value] in an array
       .map((nvp) => {return [nvp[0], nvp[1]].join('=');}) // "name=value"
       .value()  // unwrap
       .join('&'); // "name1=value1&name2=value2..."
   let fullUrl = url + (paramStr === '' ? '' : '?' + paramStr);

   return new Promise((resolve, reject) => {
      request.post(fullUrl, (err, res) => {
         validateResponse(err, res, "start-proxy");
         resolve();
      });
   });
}


/*
 * Stop Benev Proxy for a BenevPeer. 'benevProxyInfo' is a map of
 *     {
 *         'benevPeer': client|server,
 *         'name': channel-name
 *     }
 */
function stopBenevProxy(benevProxyInfo) {
   let url = `${benevServerUrl(benevProxyInfo['benevPeer'])}/` +
               `${benevProxyInfo['benevPeer']}/stop-proxy/`;
   // Only name is needed to stop the proxy as of now. Unpacking only name.
   let paramStr = _.chain(benevProxyInfo)
       .pick('name') // ignore everything except name
       .pairs()   // convert into pairs of [name, value] in an array
       .map((nvp) => {return [nvp[0], nvp[1]].join('=');}) // "name=value"
       .value();  // unwrap
   let fullUrl = url + (paramStr === '' ? '' : '?' + paramStr);

   return new Promise((resolve, reject) => {
      request.post(fullUrl, (err, res) => {
         let stopSummary = validateResponse(err, res, "stop-proxy");
         // Get and convert avgBw and cap to KB/s
         let avgBw = parseFloat(stopSummary.avgBw) / 1000;
         let bandwidthCap = benevProxyInfo["bandwidth-cap"] / 8;
         if (bandwidthCap > 0) {
            assert.isAtMost(avgBw, bandwidthCap * 2,
                            `Avg Bandwidth (${avgBw}) KB/s wasn't below ` +
                            `2 * threshold ${bandwidthCap} KB/s`);
            console.log(`Bandwidth cap is ${bandwidthCap} KB/s and avgBw ` +
                        `was ${avgBw} KB/s`);
         }
         if (stopSummary.rawChannel) {
            if (benevProxyInfo['channel-flags'] === '0x1800') {
               // Verify if raw channels were actually used.
               expect(stopSummary.rawChannel).to.equal('true');
               console.log('Raw channel status verified, expected: true, actual: %s',
                           stopSummary.rawChannel);
            } else {
               // Ensure raw channels was not used
               expect(stopSummary.rawChannel).to.equal('false');
               console.log('Raw channel status verified, expected: false, actual: %s',
                           stopSummary.rawChannel);
            }
         } else {
               console.log('Missing raw channels status');
         }
         resolve();
      });
   });
}


function logErrorAndFail(errMsg) {
   const errStr = desc + ' validation failed: ' + errMsg;
   console.log(errStr);
   should.fail(errStr);
}


function getVvcStats(peerMode, desc, doneCb) {
   nodeServerRequest(request.get, peerMode, 'get-stats', desc,
      (res, err) => {
         let data = validateResponse(err, res, 'get-stats');
         if (!data) {
            logErrorAndFail('No data received from \'get-stats\' request');
         }

         if (doneCb) {
            doneCb(data);
         }
      });
}


function getPerformanceMetrics(desc, doneCb) {
   nodeServerRequest(request.get, 'server', 'get-perf-metrics', desc,
      (res, err) => {
         let data = validateResponse(err, res, 'get-perf-metrics');
         if (!data) {
            logErrorAndFail('No data received from \'get-perf-metrics\' request');
         }

         if (doneCb) {
            doneCb(data);
         }
      });
}


function checkReconnectCount(expectedCount, doneCb) {
   getVvcStats('server', 'NC-get-stats',
      (data) => {
         const actualCount = parseInt(data['Reconnect Count']);

         console.log('Reconnect Count - Expected: ' + expectedCount +
                     ', Actual: ' + actualCount);

         if (expectedCount !== actualCount) {
            logErrorAndFail('Expected: ' + expectedCount + ', Actual: ' +
                            actualCount);
         }

         if (doneCb) {
            doneCb(data);
         }
      });
}


/*
 * Return the Benev Server's root URL.
 */
function benevServerUrl(peerMode) {
   if (peerMode === 'client') {
      return `http://${BENEV_CLIENT.nsIp}:${BENEV_CLIENT.nsPort}`;
   } else if (peerMode === 'server') {
      return `http://${BENEV_AGENT.nsIp}:${BENEV_AGENT.nsPort}`;
   } else if (peerMode === 'bsg') {
      return `http://${BSG.nsIp}:${BSG.nsPort}`;
   }
}


/*
 * Initialize the Ports and IPs of agent and client
 * Also initialize the blast port to be used.
 */
function initializePortsAndIpValues(options) {
   BENEV_AGENT = JSON.parse(JSON.stringify(options.agent));
   BENEV_CLIENT = JSON.parse(JSON.stringify(options.client));
   BSG = JSON.parse(JSON.stringify(options.bsg));
}


/*
 * Convert a xxxBenevPeer function into one that returns a promise
 */
function callAndPromise(fn) {
   return new Promise((resolve, reject) => {
      fn((err) => {
         if (err) {
            reject(err);
         } else {
            resolve();
         }
      });
   });
}


/*
 * Utility function to facilitate requests to node server.
 * doneCb is called with the response object for response validation.
 */
function nodeServerRequest(requestFn, peerMode, urlAddOn, desc, doneCb) {
   const errMsg = desc + ' - nodeServerRequest - Error';
   const reqUrl = `${benevServerUrl(peerMode)}/${peerMode}/` + urlAddOn;

   requestFn(reqUrl, (err, res) => {
      expect(err, errMsg).to.equal(null);
      expect(res.statusCode, errMsg).to.equal(200);
      if (doneCb) {
         doneCb(res, err);
      }
   });
}

/*
 * Utility function to dump Benev server and client processes.
 */
function dumpBenevPeers() {
   /*
    * Dump server first as it loads serverTestPlugin.dll
    * which gets all the VVC API test cases done.
    */
   dumpBenevPeer('server', () => {
      dumpBenevPeer('client', () => {
      });
   });
}

/*
 * Common function to get test timeout seconds from dump timeout.
 */
function getTestTimeout(dumpTimeoutSeconds) {
   /*
    * Take Benev process hang dumps 10 seconds earlier than the actual test
    * timeout. This value can be used for taking hang dumps for all tests.
    */
   const HANG_DUMP_TIMEOUT_DIFF_SECONDS = 10;

   /*
    * Test timeout is always longer than dump timeout to take into
    * account the grace period needed for ProcDump to take dumps.
    */
   return dumpTimeoutSeconds + HANG_DUMP_TIMEOUT_DIFF_SECONDS;
}


/*
 * Launch Python HTTP server to host CRL file for SSL CRL Cache.
 */
function launchHttpServer(doneCb) {
   let command = 'launchhttpserver';
   let url = `${benevServerUrl('server')}/${command}`;

   request.get(url, (err, res) => {
      validateResponse(err, res, command);
      doneCb();
   });
}


/*
 * Kill Python HTTP server
 */
function killHttpServer(doneCb) {
   let command = 'killhttpserver';
   let url = `${benevServerUrl('server')}/${command}`;

   request.delete(url, (err, res) => {
      validateResponse(err, res, command);
      doneCb();
   });
}

// Initial setup to run the Vvc API tests
function setupVvcAPITests(benevPeerMode, options) {
   // Client and agent need each other's IPs
   let peerIp = (benevPeerMode == 'client' ? BENEV_AGENT.ip : BENEV_CLIENT.ip);
   let urlAddOn = `setup-vvc-api-tests/?&host=${peerIp}&port=${options.vvcAPIPort}`;

   return new Promise((resolve, reject) => {
      let desc = 'setupVvcAPITests';

      nodeServerRequest(request.post, benevPeerMode, urlAddOn, desc,
         (res) => {
            expect(res.body, desc).to.equal('setup-vvc-api-tests');
            resolve();
         });
   });
}

// Trigger the Vvc API tests
function runVvcAPITests(options) {
   let urlAddOn = `run-vvc-api-tests/?tests=${options.vvcAPITestsToRun}` +
                  `&dooutofproc=${options.doOutOfProc}` +
                  `&doallowchannellist=${options.doAllowChannelList}` +
                  `&allowchannel=${options.allowChannel}`;

   return new Promise((resolve, reject) => {
      let expectedRes = 'vvcAPITest: exited with code 0 and signal null';
      let desc = 'runVvcAPITests';

      nodeServerRequest(request.post, 'server', urlAddOn, desc,
         (res) => {
            expect(res.body, desc).to.equal(expectedRes);
            resolve();
         });
   });
}