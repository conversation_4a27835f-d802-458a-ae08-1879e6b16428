/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

#include "wsnmCommonIncludes.h"
#include "utilCertificates.h"
#include "utilHash.h"
#include <assert.h>

#include <stdexcept>
#include <sstream>
#include <iomanip>


/*
 * The certificate storage and retrieval related functions.
 */


/*
 *----------------------------------------------------------------------
 *
 * FindCertificate --
 *
 *   Finds a certificate in the certificate store based on the friendly
 *   name provided. If commonName is not empty, then the certificate's
 *   common name must also match. If commonName is empty, only matches based
 *   on the friendly name.
 *
 * Results:
 *   Returns the PCCERT_CONTEXT if successful, otherwise NULL.
 *
 * Side Effects:
 *   Caller must free the PCCERT_CONTEXT.
 *
 *----------------------------------------------------------------------
 */

PCCERT_CONTEXT
WinCertImplementation::FindCertificate(const CertificateStoreType &store,
                                       const std::string &friendlyName,
                                       std::string &commonName) // IN/OUT
{
   HCERTSTORE hCertStore = nullptr;
   cedar::windows::unique_cert_store uCertStore;
   bool shouldCloseStore;

   if (!OpenCertificateStore(store, hCertStore, shouldCloseStore)) {
      return nullptr;
   }
   if (shouldCloseStore) {
      uCertStore.reset(hCertStore);
   }

   return FindCertificate(hCertStore, friendlyName, commonName);
}


PCCERT_CONTEXT
WinCertImplementation::FindCertificate(const HCERTSTORE hStore,         // IN
                                       const std::string &friendlyName, // IN
                                       std::string &commonName)         // IN/OUT
{
   PCCERT_CONTEXT certCtxt = nullptr;

   if (!hStore) {
      SYSMSG_FUNC(Error, L"NULL Certificate store provided.");
      return nullptr;
   }

   if (!friendlyName.size()) {
      SYSMSG_FUNC(Error, L"NULL Certificate name provided.");
      return nullptr;
   }

   wstr wFriendlyName(wstr::to_wstr(friendlyName.c_str()));
   DWORD ret = 0;

   while (certCtxt = CertEnumCertificatesInStore(hStore, certCtxt)) {
      wstr friendlyName;
      if (!GetCertFriendlyName(certCtxt, friendlyName)) {
         continue;
      }

      if (friendlyName == wFriendlyName) {
         if (certCtxt->dwCertEncodingType == X509_ASN_ENCODING) {
            SYSMSG_FUNC(Debug, L"Found matching certificate with friendly name=%s", wFriendlyName);

            // Extract the Common Name
            DWORD commonNameLength =
               CertGetNameStringA(certCtxt, CERT_NAME_ATTR_TYPE, 0, szOID_COMMON_NAME, NULL, 0);
            if (commonNameLength > 0) {
               mstr retCN;
               retCN.setSize(commonNameLength);

               CertGetNameStringA(certCtxt, CERT_NAME_ATTR_TYPE, 0, szOID_COMMON_NAME,
                                  retCN.p_upd(), commonNameLength);
               // Remove extraneous white space that CertGetNameString adds
               retCN.trimRight();

               if (commonName.empty()) {
                  commonName = std::string(retCN.c_str());
                  return certCtxt;
               } else if (retCN.comparei(commonName.c_str()) == 0) {
                  SYSMSG_FUNC(Debug, L"Found matching certificate with common name=%S",
                              commonName.c_str());
                  return certCtxt;
               } else {
                  SYSMSG_FUNC(Debug, L"Ignoring cert with CN mismatch: %S", retCN);
               }
            } else {
               SYSMSG_FUNC(Warn,
                           L"Unable to get the common name length for "
                           L"certificate with friendly name %s",
                           wFriendlyName);
               if (commonName.empty()) {
                  return certCtxt;
               }
            }
         } else {
            SYSMSG_FUNC(Warn, L"Unexpected cert encoding type=%d", certCtxt->dwCertEncodingType);
         }
      } else {
         SYSMSG_FUNC(Trace, L"Skipping certificate with name '%s'", friendlyName);
      }
   }

   SYSMSG_FUNC(Debug,
               L"Certificate with friendly name '%s' and common name "
               L"'%S' not found. err=%s",
               wFriendlyName, commonName.empty() ? "n/a" : commonName.c_str(), wstr::formatError());

   /*
    * Free the cert context as we will not be calling
    * CertEnumCertificatesInStore anymore, that API frees the
    * context sent as previous cert context.
    */
   if (certCtxt) {
      CertFreeCertificateContext(certCtxt);
      certCtxt = nullptr;
   }
   return certCtxt;
}


/*
 *----------------------------------------------------------------------
 *
 * ListCertificates --
 *
 *   Iterates through the certificates in the store and returns the details of
 *   each certificate.
 *
 * Results:
 *   A list of certificate detials
 *
 * Side Effects:
 *   None
 *
 *----------------------------------------------------------------------
 */

std::vector<CertUtilityInterface::CertificateDetails>
WinCertImplementation::ListCertificates(const CertificateStoreType &store)
{
   std::vector<CertUtilityInterface::CertificateDetails> certs;

   HCERTSTORE hCertStore;
   bool shouldCloseStore;
   cedar::windows::unique_cert_store uCertStore;

   if (!OpenCertificateStore(store, hCertStore, shouldCloseStore)) {
      return certs;
   }
   if (shouldCloseStore) {
      uCertStore.reset(hCertStore);
   }

   HashGenerator hashGen;
   hashGen.SetAlgorithm(HashGenerator::ALGORITHM::SHA256, false);

   PCCERT_CONTEXT certCtx = nullptr;
   while (certCtx = CertEnumCertificatesInStore(hCertStore, certCtx)) {
      CertificateDetails certDetails;
      std::string temp;
      if (GetCertSubjectName(certCtx, temp)) {
         certDetails.subjectName = temp;
      }
      if (GetCertSerialNumber(certCtx, temp)) {
         certDetails.serialNumber = temp;
      }
      if (GetCertIssuerName(certCtx, temp)) {
         certDetails.issuerName = temp;
      }
      if (GetCertValidFrom(certCtx, temp)) {
         certDetails.validFrom = temp;
      }
      if (GetCertValidTo(certCtx, temp)) {
         certDetails.validTo = temp;
      }
      wstr friendlyName;
      if (GetCertFriendlyName(certCtx, friendlyName)) {
         certDetails.friendlyName = wstr::to_mstr(friendlyName).c_str();
      } else {
         certDetails.friendlyName = "";
      }

      certDetails.thumbprint = GetThumbprint(certCtx, &hashGen);
      certs.push_back(certDetails);
   }
   return std::move(certs);
}


/*
 *----------------------------------------------------------------------
 *
 * ImportCertificateToStore --
 *
 *   Imports the provided PEM format certificate to certificate store,
 *   and associates the provided friendly name and private key with it.
 *   The private key and friendly name should only be provided for client
 *   certificate, they should not be provided for CA certificates.
 *
 *   If importing to a memory store, then store name, friendly name, and
 *   private key should not be provided. The provided memory store should
 *   be closed outside by the caller.
 *
 * Results:
 *   Returns true if succcessful, false otherwise.
 *
 * Side Effects:
 *   None.
 *
 *----------------------------------------------------------------------
 */

bool
WinCertImplementation::ImportCertificateToStore(const CertificateStoreType &store,       // IN
                                                const std::string &certToImport,         // IN
                                                const std::string &friendlyName,         // IN
                                                const std::string &privateKey,           // IN
                                                const std::string &privKeyContainerName, // IN
                                                bool allCertsFriendlyName,               // IN/OPT
                                                std::vector<std::string> *thumbprints)   // OUT/OPT
{
   if (!certToImport.size()) {
      SYSMSG_FUNC(Error, L"Empty certificate has been provided.");
      return false;
   }

   HCERTSTORE hCertStore = nullptr;
   cedar::windows::unique_cert_store uCertStore;
   bool shouldCloseStore;

   if (!OpenCertificateStore(store, hCertStore, shouldCloseStore)) {
      return false;
   }
   if (shouldCloseStore) {
      uCertStore.reset(hCertStore);
   }

   return ImportCertificate(hCertStore, certToImport, friendlyName, privateKey,
                            privKeyContainerName, allCertsFriendlyName, thumbprints);
}


/*
 *----------------------------------------------------------------------
 *
 * SetPrivateKeyForCertificateInStore --
 *
 *   Associates the private key with the certificate.
 *
 * Results:
 *   Returns true if succcessful, false otherwise.
 *
 * Side Effects:
 *   None.
 *
 *----------------------------------------------------------------------
 */

bool
WinCertImplementation::SetPrivateKeyForCertificateInStore(PCCERT_CONTEXT certContext,       // IN
                                                          const std::string &privateKey,    // IN
                                                          const std::string &containerName) // IN
{
   bool ret = false;
   if (!certContext || !privateKey.size()) {
      SYSMSG_FUNC(Error, L"Invalid parameters provided");
      return ret;
   }

   wchar_t *wContainerName = nullptr;
   NCryptBuffer cryptBuffers[1] = {0};
   NCryptBufferDesc keyParameters;
   NCRYPT_PROV_HANDLE hProv = 0;
   NCRYPT_KEY_HANDLE hKey;

   // Convert the container to wide.
   wstr wideContainerName = wstr::to_wstr(containerName.c_str());
   wContainerName = (wchar_t *)wideContainerName.c_str();

   // Must give the key a name in order to store permanently.
   cryptBuffers[0].BufferType = NCRYPTBUFFER_PKCS_KEY_NAME;
   cryptBuffers[0].pvBuffer = (LPWSTR)wContainerName;
   cryptBuffers[0].cbBuffer = (DWORD)(wcslen(wContainerName) + 1) * sizeof(wchar_t);

   keyParameters.ulVersion = NCRYPTBUFFER_VERSION;
   keyParameters.pBuffers = cryptBuffers;
   keyParameters.cBuffers = 1;

   DWORD importFlags = NCRYPT_SILENT_FLAG | NCRYPT_OVERWRITE_KEY_FLAG;

   // It's a computer key, not a user key.
   importFlags |= NCRYPT_MACHINE_KEY_FLAG;

   // Defer key finalize so we can set exportable property.
   importFlags |= NCRYPT_DO_NOT_FINALIZE_FLAG;
   SECURITY_STATUS st = ERROR_SUCCESS;
   st = NCryptOpenStorageProvider(&hProv, MS_KEY_STORAGE_PROVIDER, 0);

   if (st != ERROR_SUCCESS) {
      SYSMSG_FUNC(Error, L"NCryptOpenStorageProvider failed, returned=%d", st);
      return ret;
   }

   st = NCryptImportKey(hProv, 0, LEGACY_RSAPRIVATE_BLOB, &keyParameters, &hKey,
                        (PBYTE)privateKey.c_str(), (DWORD)privateKey.size(), importFlags);
   if (st != ERROR_SUCCESS) {
      SYSMSG_FUNC(Error, L"NCryptImportKey failed, returned=%d", st);
      goto cleanup;
   }

   // Allow plaintext export of key.
   DWORD allowExport = NCRYPT_ALLOW_PLAINTEXT_EXPORT_FLAG;
   st = NCryptSetProperty(hKey, NCRYPT_EXPORT_POLICY_PROPERTY, (PBYTE)&allowExport,
                          sizeof(allowExport), 0);

   if (st != ERROR_SUCCESS) {
      SYSMSG_FUNC(Error, L"NCryptSetProperty failed, returned=%d", st);
      goto cleanup;
   }

   // Finalize key (no more changes)
   DWORD finalizeFlags = NCRYPT_SILENT_FLAG | NCRYPT_WRITE_KEY_TO_LEGACY_STORE_FLAG;
   st = NCryptFinalizeKey(hKey, finalizeFlags);
   if (st != ERROR_SUCCESS) {
      SYSMSG_FUNC(Error, L"NCryptFinalizeKey failed, returned=%d", st);
      goto cleanup;
   }

   /*
    * Add the key to the cert.
    */

   CRYPT_KEY_PROV_INFO info;
   memset(&info, 0, sizeof(info));
   info.pwszContainerName = (LPWSTR)wContainerName;
   info.pwszProvName = MS_KEY_STORAGE_PROVIDER;
   info.dwProvType = 0; // CNG Keystorage provider

   info.dwFlags = CERT_SET_KEY_PROV_HANDLE_PROP_ID | CERT_SET_KEY_CONTEXT_PROP_ID |
                  NCRYPT_MACHINE_KEY_FLAG | NCRYPT_SILENT_FLAG;
   info.dwKeySpec = AT_KEYEXCHANGE;

   if (!CertSetCertificateContextProperty(certContext, CERT_KEY_PROV_INFO_PROP_ID, 0, &info)) {
      SYSMSG_FUNC(Error, L"CertSetCertificateContextProperty failed. Error = %d.", GetLastError());
      goto cleanup;
   }

   ret = true;

cleanup:
   if (hProv)
      NCryptFreeObject(hProv);
   if (hKey)
      NCryptFreeObject(hKey);

   return ret;
}


/*
 *-----------------------------------------------------------------------------
 *
 * OpenCertificateStore
 *
 *    Open a certificate store of the given name.
 *
 * Results:
 *    cert store if successful. NULL, otherwise.
 *
 * Side effects:
 *    cert store needs to be freed by the caller.
 *
 *-----------------------------------------------------------------------------
 */

HCERTSTORE
WinCertImplementation::OpenCertificateStore(const std::string &storeName)
{
   HCERTSTORE hCertStore = nullptr;

   if (storeName.empty()) {
      SYSMSG_FUNC(Error, "Certificate store name not provided.");
      return hCertStore;
   }

   hCertStore = CertOpenStore(CERT_STORE_PROV_SYSTEM_A, X509_ASN_ENCODING | PKCS_7_ASN_ENCODING,
                              NULL, CERT_SYSTEM_STORE_LOCAL_MACHINE, storeName.c_str());
   if (hCertStore == NULL) {
      SYSMSG_FUNC(Error, L"Unable to open cert store error: %s", wstr::formatError());
   }

   return hCertStore;
}


/*
 *-----------------------------------------------------------------------------
 *
 * GetCertificateStore
 *
 *    Open a certificate store of the given name. Returns it in the form of a
 *    CertificateStoreType. This allows callers of this interface to reuse the
 *    same store for all operations.
 *
 * Results:
 *    cert store
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

CertificateStoreType
WinCertImplementation::GetCertificateStore(const std::string &storeName)
{
   cedar::windows::unique_cert_store certStore(OpenCertificateStore(storeName));
   return std::move(certStore);
}


/*
 *-----------------------------------------------------------------------------
 *
 * WinCertImplementation::RetrieveSystemCertificates
 *
 *    Get the certificates installed in the root certificate store.
 *
 * Results:
 *    True if successful, false otherwise.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

bool
WinCertImplementation::RetrieveSystemCertificates(std::vector<std::string> &certificates)
{
   cedar::windows::unique_cert_store certStore(OpenCertificateStore("Root"));
   if (!certStore) {
      SYSMSG_FUNC(Error, L"Unable to open root certificate store");
      return false;
   }

   PCCERT_CONTEXT certCtx = nullptr;
   while (certCtx = CertEnumCertificatesInStore(certStore.get(), certCtx)) {
      auto pemCert = ConvertCertCtxToPEM(certCtx);
      if (pemCert.empty()) {
         continue;
      }

      certificates.push_back(pemCert);
   }
   return certificates.size();
}


/*
 *----------------------------------------------------------------------
 *
 * WinCertImplementation::FindCertificates --
 *
 *   Opens cert store with given storeName. Finds all certificates
 *   with given friendly name.
 *
 * Results:
 *   Returns vector of PCCERT_CONTEXT certificates.
 *
 * Side Effects:
 *   None
 *
 *----------------------------------------------------------------------
 */

std::vector<cedar::windows::unique_cert_context>
WinCertImplementation::FindCertificates(const CertificateStoreType &store,
                                        const std::string &friendlyName)
{
   std::vector<cedar::windows::unique_cert_context> certs;

   if (!friendlyName.size()) {
      SYSMSG_FUNC(Error, "Friendly Name not provided.");
      return std::move(certs);
   }

   HCERTSTORE hCertStore;
   bool shouldCloseStore;
   cedar::windows::unique_cert_store uCertStore;

   if (!OpenCertificateStore(store, hCertStore, shouldCloseStore)) {
      return std::move(certs);
   }
   if (shouldCloseStore) {
      uCertStore.reset(hCertStore);
   }

   wstr wFriendlyName(mstr::to_wstr(friendlyName.c_str()));
   PCCERT_CONTEXT certCtx = nullptr;
   PCCERT_CONTEXT dupCtx = nullptr;
   while (certCtx = CertEnumCertificatesInStore(hCertStore, certCtx)) {
      wstr friendlyName;
      if (!GetCertFriendlyName(certCtx, friendlyName)) {
         continue;
      }

      if (wFriendlyName != friendlyName) {
         continue;
      }

      dupCtx = CertDuplicateCertificateContext(certCtx);

      certs.push_back(cedar::windows::unique_cert_context(dupCtx));
   }

   return std::move(certs);
}


/*
 *----------------------------------------------------------------------
 *
 * WinCertImplementation::FindCertificates --
 *
 *   Opens cert store with given storeName. Finds all certificates
 *   with given thumbprints.
 *
 *   Retain the order in which the thumbprints are given.
 *
 * Results:
 *   Returns a list of certificates.
 *
 * Side Effects:
 *   None
 *
 *----------------------------------------------------------------------
 */

std::vector<cedar::windows::unique_cert_context>
WinCertImplementation::FindCertificates(const CertificateStoreType &store,
                                        const std::vector<std::string> &thumbprints,
                                        std::vector<std::vector<BYTE>> *notFoundTps)
{
   std::vector<cedar::windows::unique_cert_context> certs;

   /*
    * Convert the thumbprints into binary form for a couple of reasons:
    *    1. Avoid case sensitivity
    *    2. Avoid having to convert every tp in the cert store to a hex string
    */

   std::vector<std::vector<BYTE>> searchTps =
      CertUtilityInterface::ThumbprintsToByteVectors(thumbprints);

   if (searchTps.size() != thumbprints.size()) {
      SYSMSG_FUNC(Warn, L"Not all hashes were valid.");
   } else if (searchTps.size() == 0) {
      SYSMSG_FUNC(Error, L"No hashes to look for.");
      return certs;
   }

   return FindCertificates(store, searchTps, notFoundTps);
}


std::vector<cedar::windows::unique_cert_context>
WinCertImplementation::FindCertificates(const CertificateStoreType &store,
                                        const std::vector<std::vector<BYTE>> &thumbprints,
                                        std::vector<std::vector<BYTE>> *notFoundTps)
{
   std::vector<cedar::windows::unique_cert_context> certs;

   if (thumbprints.empty()) {
      return std::move(certs);
   }

   HCERTSTORE hCertStore;
   bool shouldCloseStore;
   cedar::windows::unique_cert_store uCertStore;

   if (!OpenCertificateStore(store, hCertStore, shouldCloseStore)) {
      return std::move(certs);
   }
   if (shouldCloseStore) {
      uCertStore.reset(hCertStore);
   }

   certs.resize(thumbprints.size());
   std::vector<bool> foundFlags(thumbprints.size(), false);
   unsigned int certsFoundNum = 0;

   // Unfortunately CertFindCertificateInStore only supports SHA1
   PCCERT_CONTEXT certCtx = nullptr;
   PCCERT_CONTEXT dupCtx = nullptr;
   HashGenerator hashGen;
   hashGen.SetAlgorithm(HashGenerator::ALGORITHM::SHA256, false);

   while ((certCtx = CertEnumCertificatesInStore(hCertStore, certCtx)) &&
          certsFoundNum < thumbprints.size()) {
      auto certCtxTp = WinCertImplementation::GetThumbprintBinary(certCtx, &hashGen);

      for (size_t tpIndex = 0; tpIndex < thumbprints.size(); ++tpIndex) {
         if (thumbprints[tpIndex] == certCtxTp) {
            certsFoundNum++;
            SYSMSG_FUNC(Trace, L"Found cert with thumbprint: %S",
                        HashGenerator::FormatHash(certCtxTp));
            dupCtx = CertDuplicateCertificateContext(certCtx);
            certs[tpIndex] = cedar::windows::unique_cert_context(dupCtx);
            foundFlags[tpIndex] = true;
            break;
         }
      }
   }

   if (certCtx != NULL) {
      CertFreeCertificateContext(certCtx);
      certCtx = nullptr;
   }

   if (certsFoundNum != thumbprints.size()) {
      // We risk nullptr dereferences if we return empty contexts
      SYSMSG_FUNC(Warn,
                  L"Not all certificates with given thumbprints found. "
                  L"Expected: %zu. Actual: %u",
                  thumbprints.size(), certsFoundNum);

      certs.erase(
         std::remove_if(certs.begin(), certs.end(),
                        [](const cedar::windows::unique_cert_context &ctx) { return !ctx; }),
         certs.end());
   }

   if (notFoundTps) {
      for (size_t i = 0; i < thumbprints.size(); ++i) {
         if (!foundFlags[i]) {
            notFoundTps->push_back(thumbprints[i]);
         }
      }
   }

   return std::move(certs);
}


/*
 *----------------------------------------------------------------------
 *
 * GetPEMCert --
 *
 *   Opens cert store with given storeName. Finds all certificates
 *   with given friendly name. Converts them to PEM format.
 *
 * Results:
 *   Returns vector of PEM encoded certificates else empty vector
 *
 * Side Effects:
 *   None
 *
 *----------------------------------------------------------------------
 */

std::vector<std::string>
WinCertImplementation::GetPEMCerts(const CertificateStoreType &storeName, // IN
                                   const std::string &friendlyName)       // IN
{
   std::vector<std::string> pemCerts;

   auto certs = FindCertificates(storeName, friendlyName);

   for (auto &cert : certs) {
      auto pemCert = ConvertCertCtxToPEM(cert.get());
      if (pemCert.empty()) {
         continue;
      }
      pemCerts.insert(pemCerts.begin(), pemCert);
   }

   return pemCerts;
}


/*
 *-----------------------------------------------------------------------------
 *
 * ExportCertificateAndKeyFromStore
 *
 *    Retrieve pfx formated key data consisting of both certificate and private
 *    key (PKCS#12). Optionally, retrieves the CA certificate and the
 *    certificate context.
 *
 * Results:
 *    true if successful. false, otherwise.
 *
 * Side effects:
 *    pCertContext needs to be freed by the caller.
 *
 *-----------------------------------------------------------------------------
 */

bool
WinCertImplementation::ExportCertificateAndKeyFromStore(const CertificateStoreType &store,   // IN
                                                        const std::string &certFriendlyName, // IN
                                                        std::string &dataBlob,               // OUT
                                                        std::string &commonName,      // IN/OUT
                                                        std::string *caCrt,           // OUT/OPT
                                                        std::string *rootCaCrt,       // OUT/OPT
                                                        PCCERT_CONTEXT *pCertContext) // OUT/OPT
{
   HCERTSTORE hCertStore;
   bool shouldCloseStore;
   cedar::windows::unique_cert_store uCertStore;

   if (!OpenCertificateStore(store, hCertStore, shouldCloseStore)) {
      return false;
   }
   if (shouldCloseStore) {
      uCertStore.reset(hCertStore);
   }

   cedar::windows::unique_cert_context cert(
      FindCertificate(hCertStore, certFriendlyName, commonName));

   if (!cert) {
      SYSMSG_FUNC(Error, L"Unable to find the certificate with friendly name=%S.",
                  certFriendlyName.c_str());
      return false;
   }

   // Create a memory store
   cedar::windows::unique_cert_store memoryStore(
      CertOpenStore(CERT_STORE_PROV_MEMORY, 0, NULL, 0, NULL));
   if (!memoryStore) {
      SYSMSG_FUNC(Error, L"Cannot create memory store. Error = %s.", wstr::formatError());
      return false;
   }

   if (!CertAddCertificateContextToStore(memoryStore.get(), cert.get(), CERT_STORE_ADD_USE_EXISTING,
                                         NULL)) {
      SYSMSG_FUNC(Error, L"Failed to add to the memory store. Error = %s.", wstr::formatError());
      return false;
   }

   CRYPT_DATA_BLOB cryptDataBlob = {0};
   if (!PFXExportCertStoreEx(memoryStore.get(), &cryptDataBlob, NULL, NULL,
                             EXPORT_PRIVATE_KEYS | REPORT_NOT_ABLE_TO_EXPORT_PRIVATE_KEY |
                                REPORT_NO_PRIVATE_KEY)) {
      SYSMSG_FUNC(Error, L"Failed to get the pkcs12 data size. Error = %s.", wstr::formatError());
      return false;
   }

   if (cryptDataBlob.cbData == 0) {
      SYSMSG_FUNC(Error, L"CRYPT_DATA_BLOB size is unexpectedly 0.");
      return false;
   }

   // Allocate the buffer.
   std::vector<BYTE> cryptDataBuffer(cryptDataBlob.cbData, 0);
   cryptDataBlob.pbData = &cryptDataBuffer[0];
   if (!PFXExportCertStoreEx(memoryStore.get(), &cryptDataBlob, NULL, NULL,
                             EXPORT_PRIVATE_KEYS | REPORT_NOT_ABLE_TO_EXPORT_PRIVATE_KEY |
                                REPORT_NO_PRIVATE_KEY)) {
      SYSMSG_FUNC(Error, L"Failed to export the pkcs12 data. Error = %s.", wstr::formatError());
      return false;
   }

   dataBlob.assign((char *)cryptDataBlob.pbData, cryptDataBlob.cbData);

   // Retrieve CA certificate chain, given that caCrt is supplied as param.
   if (caCrt && !RetrieveCACertificateChain(hCertStore, cert.get(), *caCrt)) {
      SYSMSG_FUNC(Error, L"Failed to retrieve CA certificate chain.");
      return false;
   }

   // Retrieve CA root certificate, given that rootCaCrt is supplied as param.
   if (rootCaCrt && !RetrieveRootCACertificate(hCertStore, cert.get(), *rootCaCrt)) {
      SYSMSG_FUNC(Error, L"Fail to retrieve root CA certificate.");
      return false;
   }

   if (pCertContext) {
      *pCertContext = cert.release();
   }

   return true;
}


/*
 *-----------------------------------------------------------------------------
 *
 * RetrieveCACertificateChainFromStore
 *
 *    Retrieve the CA Certificate chain from the certificate store given
 *    the friendly name and cert stores are provided.
 *
 * Results:
 *    TRUE if successful. FALSE, otherwise.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

bool
WinCertImplementation::RetrieveCACertificateChainFromStore(
   const std::string &certFriendlyName, // IN
   const std::string &storeName,        // IN
   std::string &certRetrieved)          // OUT
{
   bool ret = false;
   cedar::windows::unique_cert_chain_context chain;
   CERT_CHAIN_PARA param = {0};

   if (!certFriendlyName.size()) {
      SYSMSG_FUNC(Error, L"Client certificate name not provided.");
      return false;
   }

   if (!storeName.size()) {
      SYSMSG_FUNC(Error, L"Certificate store name not provided.");
      return false;
   }

   // Open the cert store.
   cedar::windows::unique_cert_store hCertStore(OpenCertificateStore(storeName));
   if (!hCertStore) {
      SYSMSG_FUNC(Error, L"Unable to open cert store error = %d.", GetLastError());
      return false;
   }

   // First find the cert context of the client certificate.
   std::string empty;
   cedar::windows::unique_cert_context clientCertCtxt(
      FindCertificate(hCertStore.get(), certFriendlyName, empty));
   if (!clientCertCtxt) {
      SYSMSG_FUNC(Error, L"Unable to get the CERT_CONTEXT of the stored client certificate.");
      return false;
   }

   return RetrieveCACertificateChain(hCertStore.get(), clientCertCtxt.get(), certRetrieved);
}


/*
 *-----------------------------------------------------------------------------
 *
 * DeleteCertificate
 *
 *    Delete the certificate context.
 *
 * Results:
 *    TRUE if successful. FALSE, otherwise.
 *
 * Side effects:
 *    Certificate is removed from store. Context freed if successful.
 *
 *-----------------------------------------------------------------------------
 */

bool
WinCertImplementation::DeleteCertificate(PCCERT_CONTEXT certContext)
{
   if (certContext == nullptr) {
      return false;
   }

   // CertDeleteCertificateFromStore frees context if successful
   BOOL result = CertDeleteCertificateFromStore(certContext);
   if (!result) {
      SYSMSG_FUNC(Error, L"CertDeleteCertificateFromStore failed %s.", wstr::formatError());
      return false;
   }
   return true;
}


/*
 *-----------------------------------------------------------------------------
 *
 * DeleteCertificateAndPrivateKey
 *
 *    Delete the certificate context along with the associated private key.
 *    The private key deletion is done as a best effort, it will not affect
 *    the deletion of the cert itself. This is because a certificate may
 *    not have a private key associated with it.
 *
 * Results:
 *    True if the certificate deletion succeeded (the certContext was freed as
 *    well). False otherwise.
 *
 * Side effects:
 *    PCCERT_CONTEXT will be freed if the function returns true.
 *
 *-----------------------------------------------------------------------------
 */

bool
WinCertImplementation::DeleteCertificateAndPrivateKey(PCCERT_CONTEXT certContext)
{
   if (certContext == nullptr) {
      return false;
   }

   // Delete its key container
   HCRYPTPROV_OR_NCRYPT_KEY_HANDLE handle = NULL;
   DWORD keySpec = 0;
   BOOL freeHandle = FALSE;
   if (CryptAcquireCertificatePrivateKey(
          certContext, CRYPT_ACQUIRE_SILENT_FLAG | CRYPT_ACQUIRE_ONLY_NCRYPT_KEY_FLAG, NULL,
          &handle, &keySpec, &freeHandle)) {
      if (NCryptDeleteKey(handle, 0) != ERROR_SUCCESS) {
         // NCryptDeleteKey frees handle only if successful
         if (freeHandle && keySpec == CERT_NCRYPT_KEY_SPEC) {
            NCryptFreeObject(handle);
         } else if (freeHandle) {
            CryptReleaseContext(handle, 0);
         }
      }
   } else {
      // Unlikely to happen, but log then fall through to delete cert anyway
      SYSMSG_FUNC(Debug, L"Failed to acquire private key handle. Error: %s", wstr::formatError());
   }

   // Delete the cert itself
   if (!CertDeleteCertificateFromStore(certContext)) {
      SYSMSG_FUNC(Error, L"CertDeleteCertificateFromStore failed with error=%s.",
                  wstr::formatError());
      return false;
   }
   return true;
}


/*
 *-----------------------------------------------------------------------------
 *
 * DeleteCertificates
 *
 *    Delete the certificates with a given friendly name.
 *
 * Results:
 *    None
 *
 * Side effects:
 *    Certificates are removed from store.
 *
 *-----------------------------------------------------------------------------
 */

void
WinCertImplementation::DeleteCertificates(const CertificateStoreType &store,
                                          const std::string &friendlyName)
{
   auto certs = FindCertificates(store, friendlyName);

   for (auto &cert : certs) {
      if (!DeleteCertificateAndPrivateKey(cert.get())) {
         SYSMSG_FUNC(Warn, L"Unable to delete the certificate with friendly name %S.",
                     friendlyName.c_str());
         continue;
      }

      // DeleteCertificateAndPrivateKey releases the memory for us
      cert.release();

      SYSMSG_FUNC(Debug, L"Deleted certificate with friendly name %S", friendlyName.c_str());
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * DeleteCertificates
 *
 *    Delete the certificates with given thumbprints.
 *
 * Results:
 *    Returns a map of thumbprints and their deletion status.
 *
 * Side effects:
 *    Certificates are removed from store.
 *
 *-----------------------------------------------------------------------------
 */

std::map<std::string, std::string>
WinCertImplementation::DeleteCertificates(const CertificateStoreType &store,
                                          const std::vector<std::vector<BYTE>> &hashes)
{
   std::map<std::string, std::string> deleteResult;

   std::vector<std::vector<BYTE>> notFoundTps;
   auto certs = FindCertificates(store, hashes, &notFoundTps);

   HashGenerator hashGen;
   hashGen.SetAlgorithm(HashGenerator::ALGORITHM::SHA256, false);

   for (auto &cert : certs) {
      std::string hashStr = WinCertImplementation::GetThumbprint(cert.get(), &hashGen);

      if (!DeleteCertificateAndPrivateKey(cert.get())) {
         SYSMSG_FUNC(Warn, L"Unable to delete the certificate with thumbprint %S.",
                     hashStr.c_str());
         deleteResult[hashStr] = "Failed";
         continue;
      }
      // DeleteCertificateAndPrivateKey releases the memory for us
      cert.release();
      deleteResult[hashStr] = "Deleted";
   }

   for (const auto &tp : notFoundTps) {
      std::string hashStr = HashGenerator::FormatHash(tp).c_str();
      deleteResult[hashStr] = "Not Found";
   }

   return deleteResult;
}


/*
 *-----------------------------------------------------------------------------
 *
 * RenameCertificate
 *
 *    Rename the certificate to a new friendly name.
 *
 * Results:
 *    None
 *
 * Side effects:
 *    Certificate is renamed in the store.
 *
 *-----------------------------------------------------------------------------
 */

bool
WinCertImplementation::RenameCertificate(PCCERT_CONTEXT cert, const std::string &newFriendlyName)
{
   wstr wideFriendlyName = wstr::to_wstr(newFriendlyName.c_str());
   CERT_NAME_BLOB friendlyNameBlob;
   friendlyNameBlob.cbData = (DWORD)(wideFriendlyName.size() + 1) * sizeof(wchar_t);
   friendlyNameBlob.pbData = (BYTE *)(wideFriendlyName.c_str());

   if (!CertSetCertificateContextProperty(cert, CERT_FRIENDLY_NAME_PROP_ID, 0, &friendlyNameBlob)) {
      SYSMSG_FUNC(Error,
                  L"CertSetCertificateContextProperty failed "
                  L"while setting friendly name, error=%s",
                  wstr::formatError());
      return false;
   }
   return true;
}


/*
 *-----------------------------------------------------------------------------
 *
 * RenameCertificates
 *
 *    Rename the certificates with a given friendly name to another given
 *    friendly name.
 *
 * Results:
 *    None
 *
 * Side effects:
 *    Certificates are renamed in the store.
 *
 *-----------------------------------------------------------------------------
 */

void
WinCertImplementation::RenameCertificates(const CertificateStoreType &store,
                                          const std::string &newFriendlyName,
                                          const std::string &oldFriendlyName)
{
   if (!newFriendlyName.size()) {
      SYSMSG_FUNC(Error, "New Friendly Name not provided.");
      return;
   }
   if (!oldFriendlyName.size()) {
      SYSMSG_FUNC(Error, "Old Friendly Name not provided.");
      return;
   }
   wstr wNewFriendlyName(mstr::to_wstr(newFriendlyName.c_str()));

   auto certificates = FindCertificates(store, oldFriendlyName);

   CERT_NAME_BLOB newFriendlyNameBlob;
   newFriendlyNameBlob.cbData = static_cast<DWORD>(wNewFriendlyName.size() + 1) * sizeof(wchar_t);
   newFriendlyNameBlob.pbData = (BYTE *)(wNewFriendlyName.c_str());

   for (auto &cert : certificates) {
      if (!CertSetCertificateContextProperty(cert.get(), CERT_FRIENDLY_NAME_PROP_ID, 0,
                                             &newFriendlyNameBlob)) {
         SYSMSG_FUNC(Error,
                     L"CertSetCertificateContextProperty failed "
                     L"while setting friendly name, error=%s",
                     wstr::formatError());
         continue;
      }
   }
}


/*
 *------------------------------------------------------------------------------
 *
 * GenerateRSAKeyPairUsingWin32API --
 *
 *    Generates the RSA key pair using Win32 API.
 *    Also, captures the private key PRIVATEKEYBLOB format, this can directly be
 *    used for associating the private key with the certificate in the cert
 *    store.
 *
 * Results:
 *    Returns true if successful, false otherwise.
 *
 * Side effects:
 *    None.
 *------------------------------------------------------------------------------
 */

bool
WinCertImplementation::GenerateKeyUsingWin32API(const std::string &containerName, // IN
                                                std::string &encodedKeyOut,       // OUT
                                                std::string &privKeyBlob)         // OUT
{
   wchar_t *wContainerName = nullptr;
   static const DWORD CERT_RSA_KEYSIZE = 2048;
   HCRYPTPROV_OR_NCRYPT_KEY_HANDLE hCryptProv = NULL;
   DWORD keyLen, encodedKeyLen = 0;
   unsigned char *blob = nullptr;
   unsigned char *encoded = nullptr;
   bool result = false;

   if (!containerName.size()) {
      SYSMSG_FUNC(Error, L"Empty container named provided.");
      return false;
   }

   // Make the container name wide.
   wstr wideContainerName = wstr::to_wstr(containerName.c_str());
   wContainerName = (wchar_t *)wideContainerName.c_str();
   SECURITY_STATUS st = ERROR_SUCCESS;
   NCRYPT_KEY_HANDLE hNCryptKey = NULL;
   DWORD exportPolicy = NCRYPT_ALLOW_EXPORT_FLAG | NCRYPT_ALLOW_PLAINTEXT_EXPORT_FLAG;
   DWORD keyLength = 2048;
   BYTE tempBuffer[2048] = {0};

   st = NCryptOpenStorageProvider(&hCryptProv, MS_KEY_STORAGE_PROVIDER, 0);
   if (st != ERROR_SUCCESS) {
      SYSMSG_FUNC(Debug, L"NCryptOpenStorageProvider failed, returned = %d", st);
      goto out;
   }

   st =
      NCryptCreatePersistedKey(hCryptProv, &hNCryptKey, NCRYPT_RSA_ALGORITHM, wContainerName,
                               AT_KEYEXCHANGE, NCRYPT_MACHINE_KEY_FLAG | NCRYPT_OVERWRITE_KEY_FLAG);

   if (st != ERROR_SUCCESS) {
      SYSMSG_FUNC(Debug, L"NCryptCreatePersistedKey failed, returned = %d.", st);
      goto out;
   }

   st = NCryptSetProperty(hNCryptKey, NCRYPT_LENGTH_PROPERTY, (PBYTE)&CERT_RSA_KEYSIZE,
                          sizeof(DWORD), NCRYPT_PERSIST_FLAG | NCRYPT_SILENT_FLAG);

   if (st != ERROR_SUCCESS) {
      SYSMSG_FUNC(Debug, L"NCryptSetProperty failed, while setting size, returned = %d.", st);
      goto out;
   }
   st = NCryptSetProperty(hNCryptKey, NCRYPT_EXPORT_POLICY_PROPERTY, (PBYTE)&exportPolicy,
                          static_cast<DWORD>(sizeof(DWORD)),
                          NCRYPT_PERSIST_FLAG | NCRYPT_SILENT_FLAG);

   if (st != ERROR_SUCCESS) {
      SYSMSG_FUNC(Debug,
                  L"NCryptSetProperty failed, while setting export "
                  L"policy, returned = %d.",
                  st);
      goto out;
   }

   st = NCryptFinalizeKey(hNCryptKey, NCRYPT_WRITE_KEY_TO_LEGACY_STORE_FLAG | NCRYPT_SILENT_FLAG);

   if (st != ERROR_SUCCESS) {
      SYSMSG_FUNC(Debug, L"NCryptFinalizeKey failed, returned = %d.", st);
      goto out;
   }

   st = NCryptExportKey(hNCryptKey, 0, LEGACY_RSAPRIVATE_BLOB, NULL, NULL, 0, &keyLen,
                        NCRYPT_SILENT_FLAG);

   if (st != ERROR_SUCCESS) {
      SYSMSG_FUNC(Debug, L"NCryptExportKey failed, returned = %d.", st);
      goto out;
   } else {
      SYSMSG_FUNC(Debug, L"NCryptExportKey succeeded, size required=%d.", keyLen);
   }

   if (keyLen == 0) {
      SYSMSG_FUNC(Error,
                  L"NCryptExportKey returned invalid length %d. "
                  L"Key is valid.",
                  keyLen);
      goto out;
   }

   blob = new (std::nothrow) unsigned char[keyLen];

   if (!blob) {
      SYSMSG_FUNC(Error, L"Memory allocation failed!");
      goto out;
   }
   st = NCryptExportKey(hNCryptKey, 0, LEGACY_RSAPRIVATE_BLOB, NULL, (PBYTE)blob, keyLen, &keyLen,
                        NCRYPT_SILENT_FLAG);

   if (st != ERROR_SUCCESS) {
      SYSMSG_FUNC(Debug,
                  L"NCryptExportKey failed after creating buffer space,"
                  L" returned = %d, size required=%d",
                  st, keyLen);
      goto out;
   }

   /*
    * Capture the private key blob for use later while saving the
    * certificate and private key in the certificate store.
    * The private key blob being copied here can contain null characters
    * so we need to have a reliable way of copying the entire content of the
    * blob and std::string constructur provides this facility.
    */
   privKeyBlob.assign((const char *)blob, keyLen);

   if (!CryptEncodeObject(X509_ASN_ENCODING | PKCS_7_ASN_ENCODING, PKCS_RSA_PRIVATE_KEY, blob, NULL,
                          &encodedKeyLen)) {
      SYSMSG_FUNC(Error, L"CryptEncodeObject failed to get length, error:0x%08lx.", GetLastError());
      goto out;
   }

   encoded = new unsigned char[encodedKeyLen];
   if (!CryptEncodeObject(X509_ASN_ENCODING | PKCS_7_ASN_ENCODING, PKCS_RSA_PRIVATE_KEY, blob,
                          encoded, &encodedKeyLen)) {
      SYSMSG_FUNC(Error, "CryptEncodeObject failed, error:0x%08lx.", GetLastError());
      goto out;
   }

   encodedKeyOut.assign((const char *)encoded, encodedKeyLen);
   result = true;

out:
   if (encoded) {
      std::memset(encoded, 0, encodedKeyLen);
      delete[] encoded;
      encoded = nullptr;
   }
   if (blob) {
      std::memset(blob, 0, keyLen);
      delete[] blob;
      blob = nullptr;
   }

   if (hNCryptKey) {
      NCryptFreeObject(hNCryptKey);
   }
   if (hCryptProv) {
      NCryptFreeObject(hCryptProv);
   }

   return result;
}


/*
 *------------------------------------------------------------------------------
 *
 * GenerateCSRUsingWin32API --
 *
 *    Generates the Certificate Signing Request using Win32 API.
 *
 * Results:
 *    Returns true if successful, false otherwise.
 *
 * Side effects:
 *    None.
 *------------------------------------------------------------------------------
 */

bool
WinCertImplementation::GenerateCSRUsingWin32API(IN const std::string &commonName,
                                                IN const std::string &containerName,
                                                OUT std::string &encodedCertRequest,
                                                IN OPTIONAL const std::string &organizationUnitName,
                                                IN OPTIONAL const std::string &organizationName,
                                                IN OPTIONAL const std::string &state,
                                                IN OPTIONAL const std::string &country)
{
   bool ret = false;
   BYTE *pbNameEncoded = nullptr;
   CERT_PUBLIC_KEY_INFO *pbPublicKeyInfo = nullptr;
   BYTE *pbSignedEncodedCertReq = nullptr;
   CERT_NAME_BLOB SubjNameBlob;
   CERT_REQUEST_INFO CertReqInfo;
   CERT_NAME_INFO Name;
   wchar_t *wContainerName = nullptr;
   wchar_t *wCommonName = nullptr;
   wstr wideCommonName;
   std::vector<CERT_RDN_ATTR> rgNameAttr;

   if (!commonName.size()) {
      SYSMSG_FUNC(Error, L"Empty common name provided.");
      return false;
   }

   if (!containerName.size()) {
      SYSMSG_FUNC(Error, L"Empty container named provided.");
      return false;
   }

   // Make the container name wide.
   wstr wideContainerName = wstr::to_wstr(containerName.c_str());
   wContainerName = (wchar_t *)wideContainerName.c_str();

   NCRYPT_PROV_HANDLE hNCryptProvHandle = NULL;
   SECURITY_STATUS st = ERROR_SUCCESS;
   NCRYPT_KEY_HANDLE hNCryptKey = NULL;
   st = NCryptOpenStorageProvider(&hNCryptProvHandle, MS_KEY_STORAGE_PROVIDER, 0);
   if (st != ERROR_SUCCESS) {
      SYSMSG_FUNC(Debug, L"NCryptOpenStorageProvider failed, returned = %d", st);
      goto out;
   }

   st = NCryptOpenKey(hNCryptProvHandle, &hNCryptKey, wContainerName, AT_KEYEXCHANGE,
                      NCRYPT_MACHINE_KEY_FLAG | NCRYPT_SILENT_FLAG);

   if (st != ERROR_SUCCESS) {
      SYSMSG_FUNC(Debug, L"NCryptOpenKey failed, returned = %d", st);
      goto out_context;
   }


   DWORD cbPublicKeyInfo;
   if (!CryptExportPublicKeyInfo(hNCryptKey, AT_KEYEXCHANGE,
                                 X509_ASN_ENCODING | PKCS_7_ASN_ENCODING, NULL, &cbPublicKeyInfo)) {
      SYSMSG_FUNC(Error,
                  L"CryptExportPublicKeyInfo Failed, "
                  "error:0x%08x.",
                  GetLastError());
      goto out_key;
   }

   pbPublicKeyInfo = (CERT_PUBLIC_KEY_INFO *)malloc(cbPublicKeyInfo);
   if (!pbPublicKeyInfo) {
      SYSMSG_FUNC(Error, L"Failed to allocate memory for public key");
      goto out_key;
   }

   if (!CryptExportPublicKeyInfo(hNCryptKey, AT_KEYEXCHANGE,
                                 X509_ASN_ENCODING | PKCS_7_ASN_ENCODING, pbPublicKeyInfo,
                                 &cbPublicKeyInfo)) {
      SYSMSG_FUNC(Error,
                  L"CryptExportPublicKeyInfo Failed, "
                  "error:0x%08x.",
                  GetLastError());
      goto out_pubkey;
   }

   // Convert common name to wide, before dumping it to log file.
   SYSMSG_FUNC(Debug, L"Using common name = %s", (wstr::to_wstr(commonName.c_str())).c_str());

   rgNameAttr.push_back({(LPSTR)szOID_COMMON_NAME, CERT_RDN_PRINTABLE_STRING,
                         (DWORD)commonName.size(), (BYTE *)commonName.c_str()});
   if (!organizationUnitName.empty()) {
      rgNameAttr.push_back({(LPSTR)szOID_ORGANIZATIONAL_UNIT_NAME, CERT_RDN_PRINTABLE_STRING,
                            (DWORD)organizationUnitName.size(),
                            (BYTE *)organizationUnitName.c_str()});
   }
   if (!organizationName.empty()) {
      rgNameAttr.push_back({(LPSTR)szOID_ORGANIZATION_NAME, CERT_RDN_PRINTABLE_STRING,
                            (DWORD)organizationName.size(), (BYTE *)organizationName.c_str()});
   }
   if (!state.empty()) {
      rgNameAttr.push_back({(LPSTR)szOID_STATE_OR_PROVINCE_NAME, CERT_RDN_PRINTABLE_STRING,
                            (DWORD)state.size(), (BYTE *)state.c_str()});
   }
   if (!country.empty()) {
      rgNameAttr.push_back({(LPSTR)szOID_COUNTRY_NAME, CERT_RDN_PRINTABLE_STRING,
                            (DWORD)country.size(), (BYTE *)country.c_str()});
   }

   CERT_RDN rgRDN[] = {static_cast<DWORD>(rgNameAttr.size()), &rgNameAttr[0]};

   Name.cRDN = 1;
   Name.rgRDN = rgRDN;

   DWORD cbNameEncoded;
   if (!CryptEncodeObject(X509_ASN_ENCODING | PKCS_7_ASN_ENCODING, X509_NAME, &Name, NULL,
                          &cbNameEncoded)) {
      SYSMSG_FUNC(Error, L"CryptEncodeObject failed to get length, error:0x%08lx.", GetLastError());
      goto out_pubkey;
   }

   if (!(pbNameEncoded = (BYTE *)malloc(cbNameEncoded))) {
      SYSMSG_FUNC(Error, L"Failed to allocate memory for certificate name");
      goto out_pubkey;
   }

   if (!CryptEncodeObject(X509_ASN_ENCODING | PKCS_7_ASN_ENCODING, X509_NAME, &Name, pbNameEncoded,
                          &cbNameEncoded)) {
      SYSMSG_FUNC(Error, L"CryptEncodeObject failed to get length, error:0x%08lx.", GetLastError());
      goto out_pbname;
   }

   // TODO : Look into key usage extensions
   SubjNameBlob.cbData = cbNameEncoded;
   SubjNameBlob.pbData = pbNameEncoded;
   CertReqInfo.cAttribute = 0;
   CertReqInfo.rgAttribute = NULL;
   CertReqInfo.dwVersion = CERT_REQUEST_V1;
   CertReqInfo.Subject = SubjNameBlob;
   CertReqInfo.SubjectPublicKeyInfo = *pbPublicKeyInfo;

   CRYPT_ALGORITHM_IDENTIFIER SigAlg;
   CRYPT_OBJID_BLOB Parameters;
   memset(&Parameters, 0, sizeof(Parameters));
   SigAlg.pszObjId = (LPSTR)szOID_RSA_SHA256RSA;
   SigAlg.Parameters = Parameters;

   DWORD cbEncodedCertReqSize;
   if (!CryptSignAndEncodeCertificate(hNCryptKey, AT_KEYEXCHANGE,
                                      X509_ASN_ENCODING | PKCS_7_ASN_ENCODING,
                                      X509_CERT_REQUEST_TO_BE_SIGNED, &CertReqInfo, &SigAlg, NULL,
                                      NULL, &cbEncodedCertReqSize)) {
      SYSMSG_FUNC(Error, L"CryptSignAndEncodeCertificate failed %x", GetLastError());
      goto out_pbname;
   }

   pbSignedEncodedCertReq = (BYTE *)malloc(cbEncodedCertReqSize);
   if (!pbSignedEncodedCertReq) {
      SYSMSG_FUNC(Error, L"Failed to allocate memory for signed certificate");
      goto out_pbname;
   }

   if (!CryptSignAndEncodeCertificate(hNCryptKey, AT_KEYEXCHANGE,
                                      X509_ASN_ENCODING | PKCS_7_ASN_ENCODING,
                                      X509_CERT_REQUEST_TO_BE_SIGNED, &CertReqInfo, &SigAlg, NULL,
                                      pbSignedEncodedCertReq, &cbEncodedCertReqSize)) {
      SYSMSG_FUNC(Error, L"CryptSignAndEncodeCertificate failed %x", GetLastError());
      goto out_pbcertreq;
   }

   encodedCertRequest.assign((const char *)pbSignedEncodedCertReq, cbEncodedCertReqSize);
   ret = true;

out_pbcertreq:
   if (pbSignedEncodedCertReq) {
      free(pbSignedEncodedCertReq);
   }
out_pbname:
   if (pbNameEncoded) {
      free(pbNameEncoded);
   }
out_pubkey:
   if (pbPublicKeyInfo) {
      free(pbPublicKeyInfo);
   }
out_key:
   if (hNCryptKey) {
      NCryptFreeObject(hNCryptKey);
   }
out_context:
   NCryptFreeObject(hNCryptProvHandle);
out:
   return ret;
}


/*
 *----------------------------------------------------------------------
 *
 * ImportCertificate --
 *
 *   Imports the provided PEM format certificate to certificate store,
 *   and associates the provided friendly name and private key with it.
 *   The PEM format certificate is first converted to binary format and
 *   then stored.
 *
 *   The private key should only be provided for client
 *   certificate, they should not be provided for CA certificates.
 *   By default, friendly name is assigned only to client certificate,
 *   unless allCertsFriendlyName is set to true. In that case, all certs
 *   in the chain will be assigned same friendly name.
 *
 * Results:
 *   Returns true if succcessful, false otherwise.
 *
 * Side Effects:
 *   None.
 *
 *----------------------------------------------------------------------
 */

bool
WinCertImplementation::ImportCertificate(const HCERTSTORE hStore,                 // IN
                                         const std::string &certToImport,         // IN
                                         const std::string &friendlyName,         // IN
                                         const std::string &privateKey,           // IN
                                         const std::string &privKeyContainerName, // IN
                                         bool allCertsFriendlyName,               // IN
                                         std::vector<std::string> *thumbprints)   // OUT
{
   /*
    * Parse the certificate content as there can be multiple certificates in
    * case of certificate chain for CA trust store.
    */
   auto pemCerts = CertUtilityInterface::ExtractPEMCertificates(certToImport);

   // If there is a certificate chain, only the first certificate in the chain
   // should be given the private key. It doesn't make sense
   // to attach the private key of the client to the root Certificate Authority.
   HashGenerator hashGen;

   bool firstCert = true;
   for (const auto &pemCert : pemCerts) {
      cedar::windows::unique_cert_context ctx(ConvertPEMToCertCtx(pemCert));
      if (!ctx) {
         return false;
      }
      // Get the thumbprint
      if (thumbprints) {
         hashGen.SetAlgorithm(HashGenerator::ALGORITHM::SHA256, false);
         auto tp = GetThumbprint(ctx.get(), &hashGen);
         if (tp.empty()) {
            SYSMSG_FUNC(Error, L"Unable to calculate thumbprint for certificate.");
            return false;
         }
         thumbprints->push_back(tp);
      }

      // Assign friendly name to cert
      if (friendlyName.size() && (firstCert || allCertsFriendlyName)) {
         SYSMSG_FUNC(Debug, L"friendlyName is present so will be set");
         if (!RenameCertificate(ctx.get(), friendlyName)) {
            return false;
         }
      }
      // Add private key to the cert context, if it has been provided
      if (privateKey.size() && firstCert) {
         if (!SetPrivateKeyForCertificateInStore(ctx.get(), privateKey, privKeyContainerName)) {
            return false;
         }
      }

      if (!CertAddCertificateContextToStore(hStore, ctx.get(), CERT_STORE_ADD_REPLACE_EXISTING,
                                            NULL)) {
         SYSMSG_FUNC(Error, L"Failed to add certificate to store, error = %s", wstr::formatError());
         return false;
      }

      firstCert = false;
   }

   return true;
}


/*
 *-----------------------------------------------------------------------------
 *
 * GetCertIssuerName
 *
 *    Retrieve the issuer name for a certificate.
 *
 * Results:
 *    Returns true if it's able to retrieve the certificate issuer's name.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

bool
WinCertImplementation::GetCertIssuerName(PCCERT_CONTEXT context,  // IN
                                         std::string &issuerName) // OUT
{
   if (!context)
      return false;

   DWORD issuerNameLen = CertNameToStrA(context->dwCertEncodingType, &context->pCertInfo->Issuer,
                                        CERT_SIMPLE_NAME_STR, NULL, 0);

   if (issuerNameLen > 0) {
      std::vector<char> issuerBuf(issuerNameLen, '\0');
      CertNameToStrA(context->dwCertEncodingType, &context->pCertInfo->Issuer, CERT_SIMPLE_NAME_STR,
                     &issuerBuf[0], issuerNameLen);
      issuerName = &issuerBuf[0];
   }
   return true;
}


/*
 *-----------------------------------------------------------------------------
 *
 * GetCertSubjectName
 *
 *    Retrieve the subject name for a certificate.
 *
 * Results:
 *    Returns true if it's able to retrieve the certificate subject name.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

bool
WinCertImplementation::GetCertSubjectName(PCCERT_CONTEXT context, std::string &subjectName)
{
   if (!context) {
      return false;
   }

   DWORD subjectNameLen = CertNameToStrA(context->dwCertEncodingType, &context->pCertInfo->Subject,
                                         CERT_SIMPLE_NAME_STR, NULL, 0);

   if (subjectNameLen > 0) {
      std::vector<char> subjectBuf(subjectNameLen, '\0');
      CertNameToStrA(context->dwCertEncodingType, &context->pCertInfo->Subject,
                     CERT_SIMPLE_NAME_STR, &subjectBuf[0], subjectNameLen);
      subjectName = &subjectBuf[0];
   }
   return true;
}


/*
 *-----------------------------------------------------------------------------
 *
 * GetCertValidFrom
 *
 *    Retrieve the date in string format when the certificate is valid from.
 *
 * Results:
 *    True if the certificate is valid from date is retrieved. False otherwise
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

bool
WinCertImplementation::GetCertValidFrom(PCCERT_CONTEXT context, std::string &validFrom)
{
   if (!context || !context->pCertInfo) {
      return false;
   }
   auto notBeforeFileTime = context->pCertInfo->NotBefore;

   validFrom = FileTimeToMstr(&notBeforeFileTime).c_str();
   return true;
}


/*
 *-----------------------------------------------------------------------------
 *
 * GetCertValidTo
 *
 *    Retrieve the date in string format when the certificate is valid to.
 *
 * Results:
 *    True if the certificate is valid to date is retrieved. False otherwise
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

bool
WinCertImplementation::GetCertValidTo(PCCERT_CONTEXT context, std::string &validTo)
{
   if (!context || !context->pCertInfo) {
      return false;
   }
   auto notAfterFileTime = context->pCertInfo->NotAfter;

   validTo = FileTimeToMstr(&notAfterFileTime).c_str();
   return true;
}


/*
 *-----------------------------------------------------------------------------
 *
 * GetCertSerialNumber
 *
 *    Retrieve the serial number of the certificate
 *
 * Results:
 *    True if the serial number is retrieved. False otherwise
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

bool
WinCertImplementation::GetCertSerialNumber(PCCERT_CONTEXT context, std::string &serialNumber)
{
   if (!context || !context->pCertInfo) {
      return false;
   }

   auto serialNumBlob = context->pCertInfo->SerialNumber;
   std::ostringstream oss;
   for (DWORD i = 0; i < serialNumBlob.cbData; i++) {
      oss << std::hex << std::setw(2) << std::setfill('0') << (int)serialNumBlob.pbData[i];
   }
   serialNumber = oss.str();
   return true;
}


/*
 *-----------------------------------------------------------------------------
 *
 * GetCertFriendlyName
 *
 *    Retrieve the friendly name of the certificate
 *
 * Results:
 *    True if the friendly name is retrieved. False otherwise
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

bool
WinCertImplementation::GetCertFriendlyName(PCCERT_CONTEXT context, CORE::wstr &friendlyName)
{
   if (!context) {
      return false;
   }

   auto certSz = CertGetNameString(context, CERT_NAME_FRIENDLY_DISPLAY_TYPE, 0, 0, NULL, 0);
   if (certSz == 1) {
      SYSMSG_FUNC(Warn, L"CertGetNameString (size) failed.");
      return false;
   }

   friendlyName.resize(certSz);

   certSz = CertGetNameString(context, CERT_NAME_FRIENDLY_DISPLAY_TYPE, 0, 0, friendlyName.p_upd(),
                              certSz);
   if (certSz == 1) {
      SYSMSG_FUNC(Warn, L"CertGetNameString failed.");
      return false;
   }
   return true;
}


/*
 *------------------------------------------------------------------------------
 *
 * GetNumCertsInChainCtx --
 *
 *    Gets the number of certificates in a chain context (the length of the
 *    certificate chain).
 *
 * Results:
 *    Number of certificates in chain
 *
 * Side effects:
 *    None.
 *------------------------------------------------------------------------------
 */

int
WinCertImplementation::GetNumCertsInChainCtx(PCCERT_CHAIN_CONTEXT certChainCtx)
{
   if (!certChainCtx) {
      return 0;
   }

   /*
    * Parse the first simple chain and get its length. Multiple simple chains
    * are possible, but we do not need to handle this case.
    * TODO: Handle multiple simple chains.
    */
   return certChainCtx->rgpChain[0]->cElement;
}


/*
 *------------------------------------------------------------------------------
 *
 * ConvertChainCtxToPEM --
 *
 *    Converts a certificate chain into PEM format. Returns the chain in a
 *    vector. The 0th element is the root certificate, the last element is the
 *    client certificate.
 *
 * Results:
 *    Returns true if successful, false otherwise.
 *
 * Side effects:
 *    None.
 *------------------------------------------------------------------------------
 */

bool
WinCertImplementation::ConvertChainCtxToPEM(PCCERT_CHAIN_CONTEXT certChainCtx,
                                            std::vector<std::string> &certChainVec)
{
   certChainVec.clear();
   /*
    * TODO: Add some verification/validation logic to take care of negative
    * cases when one of the licenses in the chain is deleted etc.
    * e.g. if let's say there are three certificates in the chain, client
    * certificate, intermediate root certificate and root certificate, now
    * if root certificate is deleted later on, CertGetCertificateChain will
    * return true with and provide simple chain with 2 certificates, but that
    * would be invalid as root is not present.
    */
   // Parse the first simple chain to get the certificates.
   SYSMSG_FUNC(Debug, L"Number of simple chains in the chain context=%d", certChainCtx->cChain);

   /*
    * It is possible that there can be multiple simple chains in the chain
    * context, though it doesn't happen in our case at present.
    * TODO: Handle multiple simple chains.
    */
   if (certChainCtx->cChain > 0) {
      DWORD i = 0;
      SYSMSG_FUNC(Debug, L"Number of certificates in simple chain %d is %d.", i,
                  certChainCtx->rgpChain[i]->cElement);
      for (DWORD j = 0; j < certChainCtx->rgpChain[i]->cElement; j++) {
         std::string subjectName;
         std::string issuerName;
         GetCertSubjectName(certChainCtx->rgpChain[i]->rgpElement[j]->pCertContext, subjectName);
         GetCertIssuerName(certChainCtx->rgpChain[i]->rgpElement[j]->pCertContext, issuerName);
         SYSMSG_FUNC(Debug, L"Found subject=%s; Found issuer=%s",
                     CORE::mstr::to_wstr(subjectName.c_str()),
                     CORE::mstr::to_wstr(issuerName.c_str()));

         // Convert the certificate to PEM format.
         DWORD certSizeInBytes = 0;
         PCCERT_CONTEXT cert = certChainCtx->rgpChain[i]->rgpElement[j]->pCertContext;

         auto pemCert = ConvertCertCtxToPEM(cert);
         if (pemCert.empty()) {
            return false;
         }
         certChainVec.insert(certChainVec.begin(), pemCert);
      }
   }
   return true;
}


/*
 *------------------------------------------------------------------------------
 *
 * RetrieveCACertificates --
 *
 *    Given a leaf certificate, return the certificate chain up to its root.
 *    Excludes the client certificate.
 *
 * Results:
 *    A list of certificates that are part of the chain. The last certificate
 *    being the Root certificate and the first the leaf certificate.
 *
 * Side effects:
 *    None.
 *------------------------------------------------------------------------------
 */

std::vector<cedar::windows::unique_cert_context>
WinCertImplementation::RetrieveCACertificates(const CertificateStoreType &store,
                                              PCCERT_CONTEXT certContext)
{
   std::vector<cedar::windows::unique_cert_context> certs = GetCertificateChain(store, certContext);

   // Remove the leaf cert from the chain
   if (certs.size()) {
      certs.erase(certs.begin());
   }

   return std::move(certs);
}


/*
 *------------------------------------------------------------------------------
 *
 * GetCertificateChain --
 *
 *    Given a leaf certificate, return the certificate chain up to its root.
 *
 * Results:
 *    A list of certificates that are part of the chain. The first certificate
 *    being the leaf certificate and the last being the root certificate.
 *
 * Side effects:
 *    None.
 *------------------------------------------------------------------------------
 */

std::vector<cedar::windows::unique_cert_context>
WinCertImplementation::GetCertificateChain(const CertificateStoreType &store,
                                           PCCERT_CONTEXT certContext)
{
   std::vector<cedar::windows::unique_cert_context> certs;

   cedar::windows::unique_cert_chain_context chain;
   if (!BuildCertificateChain(store, certContext, chain.put())) {
      SYSMSG_FUNC(Error, L"Failed to build certificate chain");
      return std::move(certs);
   }

   certs = ExtractCertificatesFromChain(chain.get(), false);

   return std::move(certs);
}


/*
 *------------------------------------------------------------------------------
 *
 * ExtractCertificatesFromChain --
 *
 *    Given a certificate chain, extract the list of certificates in the chain.
 *
 * Results:
 *    A list of certificates that are part of the chain.
 *
 * Side effects:
 *    None.
 *------------------------------------------------------------------------------
 */

std::vector<cedar::windows::unique_cert_context>
WinCertImplementation::ExtractCertificatesFromChain(PCCERT_CHAIN_CONTEXT certChainCtx,
                                                    bool rootFirst)
{
   std::vector<cedar::windows::unique_cert_context> certs;

   if (certChainCtx->cChain < 1) {
      return std::move(certs);
   }

   DWORD i = 0;
   PCCERT_CONTEXT dupCtx;

   for (DWORD j = 0; j < certChainCtx->rgpChain[i]->cElement; j++) {

      dupCtx =
         CertDuplicateCertificateContext(certChainCtx->rgpChain[i]->rgpElement[j]->pCertContext);

      if (rootFirst) {
         certs.insert(certs.begin(), cedar::windows::unique_cert_context(dupCtx));
      } else {
         certs.push_back(cedar::windows::unique_cert_context(dupCtx));
      }
   }
   return std::move(certs);
}


/*
 *------------------------------------------------------------------------------
 *
 * ConvertCertCtxToPEM --
 *
 *    Given a certificate context (PCCERT_CONTEXT), convert it to a PEM string.
 *
 * Results:
 *    The given certificate in PEM format.
 *
 * Side effects:
 *    None.
 *------------------------------------------------------------------------------
 */

std::string
WinCertImplementation::ConvertCertCtxToPEM(PCCERT_CONTEXT ctx)
{
   if (ctx == nullptr) {
      return "";
   }

   DWORD certSizeInBytes = 0;
   if (!CryptBinaryToStringA(ctx->pbCertEncoded, ctx->cbCertEncoded,
                             CRYPT_STRING_BASE64HEADER | CRYPT_STRING_NOCR, NULL,
                             &certSizeInBytes)) {
      SYSMSG_FUNC(Error, L"CryptBinaryToString failed. Error = %s", wstr::formatError());
      return "";
   }

   std::vector<char> certBuffer;
   certBuffer.resize(certSizeInBytes);

   if (!CryptBinaryToStringA(ctx->pbCertEncoded, ctx->cbCertEncoded,
                             CRYPT_STRING_BASE64HEADER | CRYPT_STRING_NOCR, &certBuffer[0],
                             &certSizeInBytes)) {
      SYSMSG_FUNC(Error, L"CryptBinaryToString failed. Error = %s", wstr::formatError());
      return "";
   }
   return std::string(&certBuffer[0], certSizeInBytes);
}


/*
 *------------------------------------------------------------------------------
 *
 * ConvertPEMToCertCtx --
 *
 *    Given a certificate (in PEM format), convert it to a certificate context
 *    (PCCERT_CONTEXT).
 *
 * Results:
 *    The given certificate in binary format (PCCERT_CONTEXT).
 *
 * Side effects:
 *    None.
 *------------------------------------------------------------------------------
 */

PCCERT_CONTEXT
WinCertImplementation::ConvertPEMToCertCtx(const std::string &pem)
{
   if (pem.empty()) {
      return nullptr;
   }

   DWORD certDecodedLength;
   if (!CryptStringToBinaryA(pem.c_str(), static_cast<DWORD>(pem.length()), CRYPT_STRING_BASE64_ANY,
                             nullptr, &certDecodedLength, nullptr, nullptr)) {
      SYSMSG_FUNC(Warn, L"Unable to decode certificate (size). Error = %s", wstr::formatError());
      return nullptr;
   }

   std::vector<BYTE> certDecoded(certDecodedLength, 0);
   if (!CryptStringToBinaryA(pem.c_str(), static_cast<DWORD>(pem.length()), CRYPT_STRING_BASE64_ANY,
                             &certDecoded[0], &certDecodedLength, nullptr, nullptr)) {
      SYSMSG_FUNC(Warn, L"Unable to decode certificate. Error = %s", wstr::formatError());
      return nullptr;
   }

   // Create a CERT_CONTEXT
   PCCERT_CONTEXT pCertContext = CertCreateCertificateContext(
      X509_ASN_ENCODING | PKCS_7_ASN_ENCODING, &certDecoded[0], certDecodedLength);
   if (!pCertContext) {
      SYSMSG_FUNC(Warn, L"CertCreateCertificateContext failed. Error = %s", wstr::formatError());
      return nullptr;
   }
   return pCertContext;
}


/*
 *------------------------------------------------------------------------------
 *
 * ConvertChainCtxToPEM --
 *
 *    Converts a certificate chain into PEM format. Returns the chain as a
 *    string. Optionally leaves out the client certificate.
 *
 * Results:
 *    Returns true if successful, false otherwise.
 *
 * Side effects:
 *    None.
 *------------------------------------------------------------------------------
 */

bool
WinCertImplementation::ConvertChainCtxToPEM(PCCERT_CHAIN_CONTEXT certChainCtx,
                                            std::string &chainStr, bool skipClientCert)
{
   std::vector<std::string> certs;
   if (!ConvertChainCtxToPEM(certChainCtx, certs)) {
      return false;
   }

   if (skipClientCert) {
      certs.pop_back();
   }

   for (auto it = certs.cbegin(); it != certs.cend(); ++it) {
      chainStr.append(*it);
   }
   return true;
}


/*
 *------------------------------------------------------------------------------
 *
 * ConvertRootCaCtxToPEM --
 *
 *    Converts a certificate chain which contains the root CA certificate into
 *    PEM format. Returns the root CA cert, which is the certificate at the
 *    0 index.
 *
 * Results:
 *    Returns true if successful, false otherwise.
 *
 * Side effects:
 *    None.
 *------------------------------------------------------------------------------
 */

bool
WinCertImplementation::ConvertRootCaCtxToPEM(PCCERT_CHAIN_CONTEXT certChainCtx,
                                             std::string &rootCaStr)
{
   std::vector<std::string> certs;
   if (!ConvertChainCtxToPEM(certChainCtx, certs)) {
      return false;
   }

   rootCaStr = certs.front();
   return true;
}


/*
 *------------------------------------------------------------------------------
 *
 * BuildCertificateChain --
 *
 *    Builds the CA certificate chain starting from the given client certificate
 *    and using the certificates in the system cert store.
 *
 * Results:
 *    Returns true if successful, false otherwise.
 *
 * Side effects:
 *    None.
 *------------------------------------------------------------------------------
 */

bool
WinCertImplementation::BuildCertificateChain(const HCERTSTORE store, PCCERT_CONTEXT certContext,
                                             PCCERT_CHAIN_CONTEXT *chainContext)
{
   assert(store);
   assert(certContext);
   assert(chainContext);

   // Find the certificate chain and get the CA certificates in one blob.
   CERT_CHAIN_PARA param = {0};
   param.cbSize = sizeof(CERT_CHAIN_PARA);
   param.RequestedUsage.dwType = USAGE_MATCH_TYPE_AND;
   param.RequestedUsage.Usage.cUsageIdentifier = 0;
   param.RequestedUsage.Usage.rgpszUsageIdentifier = NULL;
   if (!CertGetCertificateChain(NULL, certContext, NULL, store, &param, 0, NULL, chainContext)) {
      SYSMSG_FUNC(Error, L"CertGetCertificateChain failed, with last error=%s",
                  wstr::formatError());
      return false;
   }

   return true;
}


bool
WinCertImplementation::BuildCertificateChain(const CertificateStoreType &store,
                                             PCCERT_CONTEXT certContext,
                                             PCCERT_CHAIN_CONTEXT *chainContext)
{
   HCERTSTORE hCertStore = nullptr;
   cedar::windows::unique_cert_store uCertStore;
   bool shouldCloseStore;

   if (!OpenCertificateStore(store, hCertStore, shouldCloseStore)) {
      return false;
   }
   if (shouldCloseStore) {
      uCertStore.reset(hCertStore);
   }

   if (!certContext) {
      SYSMSG_FUNC(Error, L"Client certificate context not provided.");
      return false;
   }
   if (!chainContext) {
      SYSMSG_FUNC(Error, L"Client certificate context chain not provided.");
      return false;
   }

   return BuildCertificateChain(hCertStore, certContext, chainContext);
}


/*
 *------------------------------------------------------------------------------
 *
 * RetrieveCACertificateChain --
 *
 *    Retrieve the CA certificate chain given the client certificate context and
 *    the opened certificate store.
 *
 * Results:
 *    Returns true if successful, false otherwise.
 *
 * Side effects:
 *    None.
 *------------------------------------------------------------------------------
 */

bool
WinCertImplementation::RetrieveCACertificateChain(const HCERTSTORE store,
                                                  PCCERT_CONTEXT certContext, std::string &caCrt)
{
   cedar::windows::unique_cert_chain_context chain;
   if (!BuildCertificateChain(store, certContext, chain.put())) {
      SYSMSG_FUNC(Error, L"Failed to build certificate chain");
      return false;
   }

   return ConvertChainCtxToPEM(chain.get(), caCrt, true);
}


/*
 *------------------------------------------------------------------------------
 *
 * RetrieveRootCACertificate --
 *
 *    Retrieve the root CA certificate given the client certificate context and
 *    the opened certificate store.
 *
 * Results:
 *    Returns true if successful, false otherwise.
 *
 * Side effects:
 *    None.
 *------------------------------------------------------------------------------
 */

bool
WinCertImplementation::RetrieveRootCACertificate(HCERTSTORE certStore, PCCERT_CONTEXT certContext,
                                                 std::string &rootCaCrt)
{
   cedar::windows::unique_cert_chain_context chain;
   if (!BuildCertificateChain(certStore, certContext, chain.put())) {
      SYSMSG_FUNC(Error, L"Failed to build certificate chain");
      return false;
   }

   return ConvertRootCaCtxToPEM(chain.get(), rootCaCrt);
}


/*
 *------------------------------------------------------------------------------
 *
 * VerifyCertChain --
 *
 *    Attempts to validate a certificate chain context. Optionally ignores some
 *    certificate errors if desired.
 *
 * Results:
 *    Returns true if successful, false otherwise.
 *
 * Side effects:
 *    None.
 *------------------------------------------------------------------------------
 */

bool
WinCertImplementation::VerifyCertChain(PCCERT_CHAIN_CONTEXT certChainCtx, CORE::wstr &hostname,
                                       bool ignoreUntrustedRoot, bool ignoreWrongHost,
                                       DWORD &errorCode)
{
   if (certChainCtx == NULL) {
      return false;
   }

   HTTPSPolicyCallbackData polHttps;
   memset(&polHttps, 0, sizeof(HTTPSPolicyCallbackData));
   polHttps.cbStruct = sizeof(HTTPSPolicyCallbackData);
   polHttps.dwAuthType = AUTHTYPE_SERVER;
   polHttps.pwszServerName = hostname.p_upd();

   CERT_CHAIN_POLICY_PARA policyParam;
   memset(&policyParam, 0, sizeof(CERT_CHAIN_POLICY_PARA));
   policyParam.cbSize = sizeof(CERT_CHAIN_POLICY_PARA);
   policyParam.pvExtraPolicyPara = &polHttps;
   if (ignoreUntrustedRoot) {
      policyParam.dwFlags |= CERT_CHAIN_POLICY_ALLOW_UNKNOWN_CA_FLAG;
   }
   if (ignoreWrongHost) {
      policyParam.dwFlags |= CERT_CHAIN_POLICY_IGNORE_INVALID_NAME_FLAG;
   }

   CERT_CHAIN_POLICY_STATUS policyStatus;
   memset(&policyStatus, 0, sizeof(CERT_CHAIN_POLICY_STATUS));
   policyStatus.cbSize = sizeof(CERT_CHAIN_POLICY_STATUS);

   if (!CertVerifyCertificateChainPolicy(CERT_CHAIN_POLICY_SSL, certChainCtx, &policyParam,
                                         &policyStatus)) {
      SYSMSG_FUNC(Error, L"CertVerifyCertificateChainPolicy Error: %s", tstr::formatError());
      return false;
   }
   errorCode = policyStatus.dwError;
   return true;
}


/*
 *------------------------------------------------------------------------------
 *
 * VerifyCertRevocation --
 *
 *    Attempts to determine if a certificate chain has been revoked.
 *
 * Results:
 *    Returns true if successful, false otherwise.
 *
 * Side effects:
 *    None.
 *------------------------------------------------------------------------------
 */

bool
WinCertImplementation::VerifyCertRevocation(PCCERT_CHAIN_CONTEXT certChainCtx,
                                            PCCERT_CONTEXT endCertCtx, DWORD &chainTrustStatus,
                                            DWORD &chainTrustInfo)
{
   if (endCertCtx == NULL) {
      return false;
   }

   CertificateStoreType store =
      cedar::windows::unique_cert_store(CertOpenStore(CERT_STORE_PROV_MEMORY, 0, NULL, 0, NULL));
   const auto &storeRef = std::get<cedar::windows::unique_cert_store>(store);

   auto certificatesToImport = ExtractCertificatesFromChain(certChainCtx);

   for (const auto &cert : certificatesToImport) {
      if (!CertAddCertificateContextToStore(storeRef.get(), cert.get(),
                                            CERT_STORE_ADD_REPLACE_EXISTING, NULL)) {
         SYSMSG_FUNC(Error, L"Failed to add certificate to store, error = %s", wstr::formatError());
         return false;
      }
   }

   static LPSTR rgszUsages[] = {szOID_PKIX_KP_SERVER_AUTH, szOID_SERVER_GATED_CRYPTO,
                                szOID_SGC_NETSCAPE};
   static DWORD cUsages = sizeof(rgszUsages) / sizeof(LPSTR);

   CERT_CHAIN_PARA ChainPara;
   memset(&ChainPara, 0, sizeof(ChainPara));
   ChainPara.cbSize = sizeof(ChainPara);
   ChainPara.RequestedUsage.dwType = USAGE_MATCH_TYPE_OR;
   ChainPara.RequestedUsage.Usage.cUsageIdentifier = cUsages;
   ChainPara.RequestedUsage.Usage.rgpszUsageIdentifier = rgszUsages;
   DWORD flags = CERT_CHAIN_CACHE_END_CERT | CERT_CHAIN_REVOCATION_CHECK_CHAIN;

   cedar::windows::unique_cert_chain_context revChainCtx;
   if (!CertGetCertificateChain(NULL, endCertCtx, NULL, storeRef.get(), &ChainPara, flags, 0,
                                revChainCtx.put())) {
      DWORD err = GetLastError();
      SYSMSG_FUNC(Debug, L"CertGetCertificateChain with revocation failed: 0x%x %s", err,
                  CORE::wstr::errorText(err));
      return false;
   }

   if (!revChainCtx || !revChainCtx->rgpChain) {
      SYSMSG_FUNC(Debug, L"Unable to find first simple chain");
      return false;
   }

   chainTrustStatus = revChainCtx->rgpChain[0]->TrustStatus.dwErrorStatus;
   chainTrustInfo = revChainCtx->rgpChain[0]->TrustStatus.dwInfoStatus;

   return true;
}


/*
 *------------------------------------------------------------------------------
 *
 * GetThumbprints --
 *
 *    Given a string with certificates in a PEM format, retrieve the thumbprint
 *    of every single certificate.
 *
 * Results:
 *    A list of thumbprints formed from the given certificates.
 *
 * Side effects:
 *    None.
 *------------------------------------------------------------------------------
 */

std::vector<std::string>
WinCertImplementation::GetThumbprints(const std::string &pemEncodedCert)
{
   std::vector<std::string> tps;

   auto certs = CertUtilityInterface::ExtractPEMCertificates(pemEncodedCert);

   HashGenerator hashGen;
   hashGen.SetAlgorithm(HashGenerator::ALGORITHM::SHA256, false);

   for (const auto &cert : certs) {
      cedar::windows::unique_cert_context certCtx(ConvertPEMToCertCtx(cert));
      if (!certCtx) {
         continue;
      }

      auto hash = GetThumbprint(certCtx.get(), &hashGen);
      if (hash.empty()) {
         SYSMSG_FUNC(Warn, L"Unable to generate hash for certificate:\n%S", cert.c_str());
         continue;
      }
      tps.push_back(hash.c_str());
   }
   return tps;
}


/*
 *------------------------------------------------------------------------------
 *
 * GetThumbprintBinary --
 *
 *    Given a certificate in PCCERT_CONTEXT format, retrieve the thumbprint
 *    in binary form.
 *
 * Results:
 *    A byte vector containing the thumbprint.
 *
 * Side effects:
 *    None.
 *------------------------------------------------------------------------------
 */

std::vector<BYTE>
WinCertImplementation::GetThumbprintBinary(PCCERT_CONTEXT pCertContext,
                                           HashGenerator *hashGenerator)
{
   assert(pCertContext);

   std::vector<BYTE> hash;

   auto generateHash = [&](auto &generator) {
      if (!generator.GenerateHash(pCertContext->pbCertEncoded, pCertContext->cbCertEncoded, nullptr,
                                  0, hash)) {
         SYSMSG_FUNC(Warn, L"GenerateHash failed.");
      }
   };

   if (hashGenerator) {
      generateHash(*hashGenerator);
   } else {
      HashGenerator hashGen;
      hashGen.SetAlgorithm(HashGenerator::SHA256, false);
      generateHash(hashGen);
   }
   return hash;
}


/*
 *------------------------------------------------------------------------------
 *
 * GetThumbprint --
 *
 *    Given a certificate context (PCCERT_CONTEXT), generate the thumbprint for
 *    it. Optionally pass in a HashGenerator object for performance.
 *
 * Results:
 *    A thumbprint string.
 *
 * Side effects:
 *    None.
 *------------------------------------------------------------------------------
 */

std::string
WinCertImplementation::GetThumbprint(PCCERT_CONTEXT pCertContext, HashGenerator *hashGenerator)
{
   assert(pCertContext);

   std::vector<BYTE> hash = GetThumbprintBinary(pCertContext, hashGenerator);

   if (hash.empty()) {
      return "";
   }

   CORE::mstr formattedHash;
   HashGenerator::FormatHash(hash, HashGenerator::HEX_UPPER_WITH_DELIM, formattedHash);
   return formattedHash.c_str();
}


/*
 *------------------------------------------------------------------------------
 *
 * OpenCertificateStore --
 *
 *    Given a CertificateStoreType, open the certificate store and extract
 *    HCERTSTORE. When CertificateStoreType holds a
 *    cedar::windows::unique_cert_store, then it does nearly nothing.
 *    When CertificateStoreType holds a string, then it opens the store,
 *    returns the newly-opened HCERTSTORE, and indicates that the caller should
 *    close the store.
 *
 * Results:
 *    True if the store was opened successfully, false otherwise.
 *
 * Side effects:
 *    None.
 *------------------------------------------------------------------------------
 */

bool
WinCertImplementation::OpenCertificateStore(const CertificateStoreType &store, HCERTSTORE &hStore,
                                            bool &shouldCloseStore)
{
   if (std::holds_alternative<std::string>(store)) {
      const std::string &storeName = std::get<std::string>(store);

      hStore = OpenCertificateStore(storeName);
      if (hStore == NULL) {
         SYSMSG_FUNC(Error, L"OpenCertificateStore failed.");
         return false;
      }
      shouldCloseStore = true;
   } else {
      const auto &storeTemp = std::get<cedar::windows::unique_cert_store>(store);
      if (!storeTemp) {
         SYSMSG_FUNC(Error, "Certificate store invalid.");
         return false;
      }
      hStore = storeTemp.get();
      shouldCloseStore = false;
   }
   return true;
}


/*
 *------------------------------------------------------------------------------
 *
 * ExtractLongestChain --
 *
 *    Given a certificate store, find the longest certificate chain.
 *    This is particularly useful when dealing with PEM certificates, where
 *    the order can be reversed or jumbled.
 *
 * Results:
 *    The longest chain found in the store. NULL if there's an error.
 *
 * Side effects:
 *    None.
 *------------------------------------------------------------------------------
 */

PCCERT_CHAIN_CONTEXT
WinCertImplementation::ExtractLongestChain(const HCERTSTORE hCertStore)
{
   // Iterate through all certificates in the store
   PCCERT_CONTEXT pCertContext = nullptr;
   std::vector<PCCERT_CHAIN_CONTEXT> chains;

   // Loop through all certificates in the store
   while ((pCertContext = CertFindCertificateInStore(
              hCertStore, X509_ASN_ENCODING, 0, CERT_FIND_ANY, nullptr, pCertContext)) != nullptr) {

      // Chain parameters
      CERT_CHAIN_PARA chainPara = {};
      memset(&chainPara, 0, sizeof(CERT_CHAIN_PARA));
      chainPara.cbSize = sizeof(CERT_CHAIN_PARA);
      chainPara.RequestedUsage.dwType = USAGE_MATCH_TYPE_AND;
      chainPara.RequestedUsage.Usage.cUsageIdentifier = 0;
      chainPara.RequestedUsage.Usage.rgpszUsageIdentifier = NULL;

      // Attempt to get the certificate chain for this certificate
      PCCERT_CHAIN_CONTEXT pChainContext = nullptr;
      BOOL success = CertGetCertificateChain(NULL,         // Use default store
                                             pCertContext, // Start with the current certificate
                                             nullptr,      // No time context, use current time
                                             hCertStore, // The store for intermediate certificates
                                             &chainPara, // Chain parameters
                                             0,
                                             nullptr,         // Default settings
                                             &pChainContext); // The resulting chain context

      // If the chain was successfully built, store it
      if (pChainContext != nullptr) {
         if (success) {
            chains.push_back(pChainContext);
         }
      }
   }

   // Now pick the longest chain
   size_t maxChainLength = 0;
   auto longestChain = chains.begin();
   PCCERT_CHAIN_CONTEXT longestChainCtx = nullptr;

   for (auto it = chains.begin(); it != chains.end(); ++it) {
      if ((*it)->cChain < 1) {
         continue;
      }
      if ((*it)->rgpChain[0]->cElement > maxChainLength) {
         maxChainLength = (*it)->rgpChain[0]->cElement;
         longestChain = it;
         longestChainCtx = *it;
      }
   }

   // Remove the longest chain from the list
   chains.erase(longestChain);

   // Free all remaining chains in the list
   for (auto &chain : chains) {
      CertFreeCertificateChain(chain);
   }

   return longestChainCtx;
}


/*
 *------------------------------------------------------------------------------
 *
 * ConvertPEMToCertChainCtx --
 *
 *    Given a PEM certificate, convert it into a PCCERT_CHAIN_CONTEXT object.
 *    If the PEM certificate contains multiple certificate chains, this method
 *    will return the longest one.
 *
 * Results:
 *    The longest chain found in the PEM certificate. NULL if there's an error.
 *
 * Side effects:
 *    None.
 *------------------------------------------------------------------------------
 */

PCCERT_CHAIN_CONTEXT
WinCertImplementation::ConvertPEMToCertChainCtx(const std::string &pem)
{
   HCERTSTORE hMemoryStore = CertOpenStore(CERT_STORE_PROV_MEMORY, 0, NULL, 0, NULL);
   if (!hMemoryStore) {
      SYSMSG_FUNC(Error, L"Failed to open cert memory store store with error: %s",
                  wstr::formatError());
      return nullptr;
   }

   CertificateStoreType store = cedar::windows::unique_cert_store(hMemoryStore);

   const std::string empty;
   WinCertImplementation winImpl;
   if (!winImpl.ImportCertificateToStore(store, pem, empty, empty, empty)) {
      SYSMSG_FUNC(Error, L"Failed to import to certificate to store");
      return nullptr;
   }

   return ExtractLongestChain(hMemoryStore);
}


/*
 *------------------------------------------------------------------------------
 *
 * FileTimeToMstr --
 *
 *    Given a LPFILETIME, convert it to a human-readable string.
 *
 * Results:
 *    A human readable string representing the date and time.
 *
 * Side effects:
 *    None.
 *------------------------------------------------------------------------------
 */

mstr
WinCertImplementation::FileTimeToMstr(LPFILETIME fileTime)
{
   SYSTEMTIME systemTime;
   FileTimeToSystemTime(fileTime, &systemTime);
   return mstr::printf("%04d-%02d-%02d,%02d:%02d:%02d", systemTime.wYear, systemTime.wMonth,
                       systemTime.wDay, systemTime.wHour, systemTime.wMinute, systemTime.wSecond);
}
