# Copyright (c) Omnissa, LLC. All rights reserved.
# This product is protected by copyright and intellectual property laws in the
# United States and other countries as well as by international treaties.
# -- Omnissa Restricted

"""hzaprepTest: hzaprep unit tests

Written using Gtest/Gmock.

Maintainers: <EMAIL>
"""

import vmware

NODE_NAME = "hzaprepTest"

env = vmware.DefaultEnvironment()

env.LoadTool("gtest", linkDynamic=True)
env.LoadTool(
    [
        "horizonUtils",
        "msvcrt",
        "atlmfc",
        "fmtlib",
    ]
)
env.LoadTool("mfw", linkStatic=False, linkLocal=True)

env.Append(
    LINKFLAGS=[
        "-subsystem:console",
    ],
    CPPDEFINES={
        "UNIT_TEST_ENV": None,
        "UNICODE": None,
        "_UNICODE": None,
    },
    CPPPATH=[
        "#bora/public",
        "#bora/lib/public",
        "#bora/apps/horizonrxtest/unitTest/public",
        "#bora/apps/horizonrxtest/unitTest/lib",
        "#bora/apps/horizonagent/test/include",
        "#bora/apps/horizonAgent/include",
        "#bora/apps/horizonAgent/apps/hzaprep",
        "#bora/apps/horizonAgent/test/apps/hzaprepTest",
        "#bora/apps/horizonCommon/include/mfw",
        "#bora/apps/horizonCommon/lib/mfw/keyvault",
        "#bora/apps/horizonAgent/lib/wsnm_common",
    ],
    CCFLAGS=[
        # Disable Inline Function Expansion, so that we can
        # hook/mock under Release compilation
        "/Ob0",
        # A large number of test cases necessitates this flag
        "/bigobj",
    ],
    SHLIBFLAGS=[
        # Disable MSVC's ICF (Identical Code Folding) optimization
        # to prevent inline hook issues under Release compilation
        "/OPT:NOICF",
    ],
)

env["AGENT_TARGET"] = NODE_NAME

e = vmware.Executable(NODE_NAME, env=env)
e.GetEnv().AddRdeVersionInfo(e, NODE_NAME + ".exe", "hzaPrep Testing Framework")

e.addSubdirs(
    [
        "apps/horizonAgent/apps/hzaprep",
        "apps/horizonAgent/test/apps/hzaprepTest",
    ]
)

e.addGlobalStaticLibs(
    [
        "omnissastring-static",
        "rxUnitTestLib",
    ]
)

e.addStaticLibs(
    "vmlibs",
    [
        "string",
        "rdsutils",
    ],
)

node = e.createProgramNode()
vmware.RegisterNode(node, NODE_NAME)
vmware.RegisterEnv("%s-env" % NODE_NAME, env)
vmware.Alias("%s-build" % NODE_NAME, node)
