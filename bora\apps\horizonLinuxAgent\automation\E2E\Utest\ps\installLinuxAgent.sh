#!/bin/bash

# Copyright (c) Omnissa, LLC. All rights reserved.
# This product is protected by copyright and intellectual property laws in the
# United States and other countries as well as by international treaties.
# -- Omnissa Restricted

# Download and install Linux agent

number=""
product_uri=""
broker=""
domain=""
workDir=/root/install_tmp
instLog=/root/install_tmp/install.log
tmpoutputDir="/tmp/crtbuild"
rpmpkg="no"
product="horizonlinuxagent"
branch="main"
installpkg=""
buildtype="beta"
buildFlag="no"
removeCfg="no"
install="yes"
prepare="yes"
smartcard="no"
vhcirebuild="no"
webcam="no"
ipv6="no"
multisession="no"
collabuidependencycheck="no"
managed="yes"
broker="**********"
domain="linuxcart.com"
user="tachyut"
password="VMware123"
registeredName=""
cdr="yes"
fips="no"
vadc="no"
usb="no"
clipboard="yes"
printer="yes"
audioin="no"
T="no"
S="yes"
vhciPatchFile=""
v4l2PatchFile=""
gh_org="euc-vdi"
gh_repo="cart"
product_brand="Omnissa"
agent_prefix="Omnissa-horizonagent-linux-x86_64"

########################################################################
#Functions                                                             #
########################################################################
function Usage()
{
cat <<EOF
--------------------------------------------------------------
Usage: ${0##*/} [Options]

Options:
   -n|--number build number     Input the build number.
   -N|--registered-name         registered machine name
   -s|--sanbox yes or no        install sandbox build
   -r|--remove yes or no        remove config files from /etc/omnissa/
   -B|--branch branch name      such as main
   -I|--install yes or no       do installation work
   --prepare yes or no          do all prepare work before install
   -m|--smartcard yes or no     install smartcard
   -F|--cdr yes or no           install cdr
   -f|--fips yes or no          install fips
   -U|--usb yes or no           install usb
   -P yes or no                 install printer
   -C yes or no                 install clipboard
   --rpm yes or no              install with rpm package
   --webcam yes or no           install with webcam
   --ipv6 yes or no             install with ipv6
   -u|--user                    user for registration
   -b|--broker                  broker IP address
   -d|--domain                  domain name
   -p|--password                password
   -a|--install yes or no       install audioin
   -T yes or no                 install with TrueSSO
   -S yes or no                 install with SSO
   -V|--vadc yes or no          install with vadc
   --multiple-session           install with multiple-session enabled
   --installpkg                 install package, both tarball and rpm
   --product                    "horizonlinuxagent/horizonlinuxagent_codecov"
   Examples:
   ./installLinuxagent.sh -n 2333560 -d lxd.vdi -b *************
   ./installLinuxagent.sh -B main -M no -b ************* -d lxd.vdi -u zyc1 -N zyc-r7ms --multiple-session

Notes:
   1. Use domain admin domain\administrator to register
   2. Standard password will be used for domain admin password
   3. hostname will be used for registered name on broker
   4. self signed CN will use hostname + domain
   5. JMS SSL keystore password will use "vmware"
   6. downloaded build and install log are located under /root/install_temp
   7. Run as root
--------------------------------------------------------------
EOF
}

function Prepare()
{
    echo Prepare
    echo "Log file $instLog"
    rm -rf $workDir
    mkdir $workDir
    rm -rf $tmpoutputDir
    mkdir -p $tmpoutputDir
    echo "start">$instLog
}

function GetLatestBuild()
{
   echo GetLatestBuild
   local tmpoutput="$tmpoutputDir/output.txt"
   local artifactory_api_url="https://artifactory.air-watch.com/artifactory/api"
   local final_url="$artifactory_api_url/search/prop?repos=github-publish-local&repository=$gh_org/$gh_repo&workflow=$product&branch=$branch&buildtype=$buildtype"
   # for eg
   # https://artifactory.air-watch.com/artifactory/api/search/prop?repos=github-publish-local&repository=euc-vdi/cart&workflow=horizonlinuxagent
   echo "artifactory_api_url final_url: $final_url"
   curl --location "$final_url" -o "$tmpoutput"
   # get the latest build uri
   product_uri=$(cat $tmpoutput | python3 -c \
"
import sys, json, requests
search_results = json.load(sys.stdin)['results']
try:
    build_url = ''
    if search_results:
        index = 0
        latest_run_num = 0
        for i in range(len(search_results)):
            uri = search_results[i]['uri']
            run_num = int(uri.split('/')[-3])
            if run_num > latest_run_num:
                latest_run_num = run_num
                return_uri = uri
    print(return_uri)
except:
    print('ERROR')
"
)
   temp="${product_uri%\"}"
   product_uri="${temp#\"}"
   # for eg https://artifactory.air-watch.com:443/artifactory/api/storage/github-publish-local/euc-vdi/cart/horizonlinuxagent/15628/build-horizonlinuxagent/1

   echo "artifactory_api_url: $product_uri?properties"
   curl --location "$product_uri?properties" -o "$tmpoutput"
   # get github build number
   number=$(cat $tmpoutput | python3 -c \
"
import sys, json
try:
   properties = json.load(sys.stdin)['properties']
   if properties:
      run_id = properties['run_id'][0]
      print (run_id)
   else:
      print ('')
except:
   print ('')
"
)
   temp="${number%\"}"
   number="${temp#\"}"


   echo "number=$number"

   if [ $number -eq 0 ]; then
      echo $resDelimiter
      echo "Incorrect product, branch and build type combination."
      echo "Or no succeeded build for this combination."
      echo "Product: $product"
      echo "Branch: $branch"
      echo "Build Type: $buildtype"
      echo $resDelimiter
      exit 1
   fi
}

function DownloadBuild()
{
   echo DownloadBuild
   cd $workDir

   buildXml=$(wget -q -O- $product_uri)
   if [ "$rpmpkg" == "no" ]; then
       if [[ $branch == *"713"* ]]; then
           echo "over cart-21fq1-713 branch"
           buildURL_x64=$(echo $buildXml | sed "s/.*\(http.*\.*Omnissa-horizonagent-linux-x86_64-[^-]*-[0-9]*.tar.gz\).*/\1/")
       elif [[ $branch == *"710"* ]]; then
           echo "over cart-20fq3-710 branch"
           buildURL_x64=$(echo $buildXml | sed "s/.*\(http.*\.*Omnissa-horizonagent-linux-x86_64-[^-]*-[0-9]*.tar.gz\).*/\1/")
       else
           buildURL_x64_name=$(echo $buildXml | sed "s/.*\($agent_prefix-2[^-]*-[^-]*-[0-9]*.tar.gz\).*/\1/")
           buildURL_x64="$product_uri/$buildURL_x64_name"
       fi
       downloadUriXml=$(wget -q -O- $buildURL_x64)
       downloadUri=$(echo $downloadUriXml |  sed -n 's/.*"downloadUri"\s*:\s*"\([^"]*\)".*/\1/p') 
       #wget https://artifactory.air-watch.com:443/artifactory/github-publish-local/euc-vdi/cart/horizonlinuxagent/15566/build-horizonlinuxagent/1/Omnissa-horizonagent-linux-x86_64-2412-8.14.0-13022678574.tar.gz

       echo "build downlod uri $downloadUri"
       # for eg: running wget https://artifactory.air-watch.com:443/artifactory/github-publish-local/euc-vdi/cart/horizonlinuxagent/15628/build-horizonlinuxagent/1/Omnissa-horizonagent-linux-x86_64-2412-8.14.0-13043516261.tar.gz
       wget $downloadUri >> $instLog 2>&1
   else
       echo "Starting RPM build download..."

       # Extract the RPM URL from the build XML
       buildURL_rpm_name=$(echo "$buildXml" | sed -n "s/.*\(Omnissa-horizonagent-linux-2[^-]*.*\.rpm\).*/\1/p")
       buildURL_rpm="$product_uri/$buildURL_rpm_name"

       # Log the extracted RPM URL
       echo "Extracted RPM URL: $buildURL_rpm"

       downloadUriXml=$(wget -q -O- $buildURL_rpm)
       downloadUri=$(echo $downloadUriXml |  sed -n 's/.*"downloadUri"\s*:\s*"\([^"]*\)".*/\1/p') 

       echo "build downlod uri $downloadUri"
       wget $downloadUri >> $instLog 2>&1

   fi
   if [ "$vadc" == "yes" ]; then
       buildURL_x64_vadc=$(echo $buildXml | sed "s/.*\(http.*\.*Omnissa-horizonagent-linux-vadc-x86_64-2[^-]*-[^-]*-[0-9]*.tar.gz\).*/\1/")
       echo $buildURL_x64_vadc
       wget $buildURL_x64_vadc >> $instLog 2>&1
   fi
}

function GetBuildInfo()
{
   echo GetBuildInfo
   local tmpoutput="$tmpoutputDir/output.txt"
   local artifactory_api_url="https://artifactory.air-watch.com/artifactory/api"
   local final_url="$artifactory_api_url/search/prop?run_id=$number"
   echo "artifactory_api_url: $final_url"
   # for eg https://artifactory.air-watch.com/artifactory/api/search/prop?run_id=12996613892
   curl --location "$final_url" -o "$tmpoutput"
   # get product uri
   product_uri=$(cat $tmpoutput | python3 -c \
"
import sys, json
try:
   search_results = json.load(sys.stdin)['results']
   if search_results:
      run_results = []
      for r in search_results:
         if 'build-horizonlinuxagent' in r['uri']:
            run_results.append(r)
      uri = run_results[-1]['uri']
      print (uri)
   else:
      print ('')
except:
   print ('')
"
)
   temp="${product_uri%\"}"
   product_uri="${temp#\"}"

   echo "artifactory_api_url: $product_uri?properties"
}

function Uninstall()
{
    echo Uninstall
    echo "sudo /usr/lib/omnissa/viewagent/bin/uninstall_viewagent.sh"
    sudo /usr/lib/omnissa/viewagent/bin/uninstall_viewagent.sh
    if [ "$removeCfg" = "yes" ];then
    echo "sudo rm -rf /etc/omnissa/*"
    sudo rm -rf /etc/omnissa/*
    fi
    echo "sudo rm -rf /tmp/omnissa-*"
    sudo rm -rf /tmp/omnissa-*
    echo "sudo rm -rf /var/log/omnissa/*"
    sudo rm -rf /var/log/omnissa/*
    echo "sudo rm -rf /var/crash/*"
    sudo rm -rf /var/crash/*
}
function ExtractBuild()
{
    echo ExtractBuild
    cd $workDir
    localtarball=$(ls $agent_prefix-*.tar.gz)
    echo $localtarball
    extractedbuild=${localtarball%.tar.gz}
    echo $extractedbuild
    rm -rf $extractedbuild
    rm -rf $number
    echo "tar -xzvf $localtarball"
    tar -xzvf $localtarball >> $instLog 2>&1
    echo "mv $extractedbuild $number"
    mv $extractedbuild $number
}
function ExtractVadc()
{
    echo $resDelimiter
    echo ExtractVadc
    cd $workDir
    localtarball=$(ls Omnissa-horizonagent-linux-vadc-x86_64-2*.tar.gz)
    echo $localtarball
    extractedbuild=${localtarball%.tar.gz}
    echo $extractedbuild
    rm -rf $extractedbuild
    rm -rf vadc
    echo "tar -xzvf $localtarball"
    tar -xzvf $localtarball >> $instLog 2>&1
    echo "mv $extractedbuild vadc"
    mv $extractedbuild vadc
}
function ApplyVhciPatch()
{
    echo $resDelimiter
    echo "Apply VHCI Patch ..."
    echo $vhciPatchFile
    local vhciTar='/root/vhci-hcd-1.15.tar.bz2'
    local vhciInstallDir='/root/vhci-hcd-1.15'
    rm -rf ${vhciInstallDir}
    tar jxvf $vhciTar -C /root/.
    cd $vhciInstallDir

    if [[ ( "$sysDist" = "SLES15" ) || ( "$sysDist" = "SLES12" ) || ( "$sysDist" = "SLED12" ) ||
        ( "$sysDist" = "SLED15" ) || ( "$sysDist" = "debian10" ) || ( "$sysDist" = "debian11" ) ||
        ( "$sysDist" = "debian12" ) ]]; then
        mkdir -p linux/$(echo $(uname -r) | cut -d '-' -f 1)/drivers/usb/core
        cp /lib/modules/$(uname -r)/source/include/linux/usb/hcd.h linux/$(echo $(uname -r) | cut -d '-' -f 1)/drivers/usb/core
    fi

    cd $vhciInstallDir
    patch -p1 < $vhciPatchFile
    echo "compile & install vhci ..."
    make clean && make && make install
}
function ApplyV4L2Patch()
{
    echo $resDelimiter
    echo "Apply V4L2 loopback Patch ..."
    echo $v4l2PatchFile
    local v4l2Tar='/root/v0.12.5.tar.gz'
    local v4l2InstallDir='/root/v4l2loopback-0.12.5'
    rm -rf ${v4l2InstallDir}
    tar zxvf $v4l2Tar -C /root/.
    cd $v4l2InstallDir

    patch -p1 < $v4l2PatchFile
    echo "compile & install v4l2 loopback ..."
    make clean && make && make install
    echo "install utilities ..."
    make install-utils
    echo "install dependencies ..."
    depmod -A
    if [ "$sysDist" = "Ubuntu2204" -o "$sysDist" = "Ubuntu2404" ]; then
        # remove default v4l2loopback driver on ubuntu 2204 or later
        rmmod v4l2loopback
        local depCfg="/etc/depmod.d/ubuntu.conf"
        if ! grep -q v4l2 $depCfg; then
            echo "override v4l2loopback * extra" >> $depCfg
        fi
        depmod -a
    fi

}
function InstallBuild()
{
    echo InstallBuild
    cmd=""
    if [ "$rpmpkg" == "no" ]; then
        cd $workDir/$number
        cmd="sudo ./install_viewagent.sh -A yes"
    else
        cmd="sudo /usr/lib/omnissa/viewagent/bin/viewSetup.sh -A yes"
    fi
    if [ "$smartcard" == "yes" ]; then
        cmd=$cmd" -m "$smartcard
    fi
    if [ "$cdr" == "no" ]; then
        cmd=$cmd" -F "$cdr
    fi
    if [ "$clipboard" == "no" ]; then
        cmd=$cmd" -C "$clipboard
    fi
    if [ "$printer" == "no" ]; then
        cmd=$cmd" -P "$printer
    fi
    if [ "$usb" == "yes" ]; then
        cmd=$cmd" -U "$usb
    fi
    if [ "$fips" == "yes" ]; then
        cmd=$cmd" -f "$fips
    fi
    if [ "$audioin" == "yes" ]; then
        cmd=$cmd" -a "$audioin
    fi
    if [ "$T" == "yes" ]; then
        cmd=$cmd" -T "$T
    fi
    if [ "$S" == "no" ]; then
        cmd=$cmd" -S "$S
    fi
    if [ "$V" == "yes" ]; then
        vadc="yes"
    fi
    if [ "$managed" == "no" ]; then
        cmd=$cmd" -M "$managed
        cmd=$cmd" -b "$broker
        cmd=$cmd" -d "$domain
        cmd=$cmd" -u "$user
        cmd=$cmd" -p "$password
    fi
    if [ "$multisession" == "yes" ]; then
        cmd=$cmd" --multiple-session"
    fi
    if [ "$collabuidependencycheck" == "yes" ]; then
        cmd=$cmd" --collaboration-dependency-check"
    fi
    if [ "$webcam" == "yes" ]; then
        cmd=$cmd" --webcam"
    fi
    if [ "$ipv6" == "yes" ]; then
        cmd=$cmd" --ipv6"
    fi
    echo $resDelimiter
    echo $cmd
    sudo $cmd
}

function InstallRPM()
{
    echo $resDelimiter
    echo "Starting InstallRPM"
    cd "$workDir" || { echo "Error: Failed to change directory to $workDir"; exit 1; }

    # Find the RPM file
    localrpm=$(ls Omnissa-horizonagent-linux-*.rpm 2>/dev/null)
    if [ -z "$localrpm" ]; then
        echo "Error: No RPM file found in $workDir after waiting $maxWaitTime seconds."
        exit 1
    fi

    echo "Found RPM file: $localrpm"

    # Verify the file exists
    if [ ! -f "$localrpm" ]; then
        echo "Error: RPM file $localrpm not found."
        exit 1
    fi

    # Install the RPM
    local cmd="rpm -ivh $localrpm"
    echo "Executing: $cmd"
    $cmd
    if [ $? -ne 0 ]; then
        echo "Error: Failed to install the RPM package."
        exit 1
    fi

    echo "RPM package installed successfully."
}

function InstallVadc()
{
    echo $resDelimiter
    echo InstallVadc
    cd $workDir/vadc
    cmd="sudo ./install_vadc.sh -A yes"
    echo $cmd
    $cmd
}

function SetCupsCfg()
{
    grep "GCOV_PREFIX" /etc/cups/cups-files.conf >/dev/null 2>&1 && \
         grep "GCOV_PREFIX_STRIP" /etc/cups/cups-files.conf >/dev/null 2>&1
    if [ "$?" != "0" ]; then
        echo "SetEnv GCOV_PREFIX /PathToCodeCoverageData" >> /etc/cups/cups-files.conf
        echo "SetEnv GCOV_PREFIX_STRIP 4" >> /etc/cups/cups-files.conf
        echo "PassEnv GCOV_PREFIX GCOV_PREFIX_STRIP" >> /etc/cups/cups-files.conf
    fi
}

function SetCupsPolicy() {
    local tmpDir=/tmp/selinux_cupsbackend.tmp
    rm -rf $tmpDir
    local moduleName=my-cup
    local policyContent=$tmpDir/$moduleName.te
    local binaryModule=$tmpDir/$moduleName.mod
    local modulePackage=$tmpDir/$moduleName.pp

    # allow cups-hzn to generate cc data from Redhat 8.5.
    mkdir -p $tmpDir
    chmod -R 700 $tmpDir
    echo "module $moduleName 1.0;" >> $policyContent
    echo "require {" >> $policyContent
    echo "type root_t;" >> $policyContent
    echo "type default_t;" >> $policyContent
    echo "type cupsd_t;" >> $policyContent
    echo "class dir { add_name create write };" >> $policyContent
    echo "class file { create lock open read write };" >> $policyContent
    echo "}" >> $policyContent
    echo "allow cupsd_t default_t:dir { add_name create write };" >> $policyContent
    echo "allow cupsd_t default_t:file { create lock open read write };" >> $policyContent
    echo "allow cupsd_t root_t:dir write;" >> $policyContent
    checkmodule -M -m $policyContent -o $binaryModule > /dev/null 2>&1
    semodule_package -m $binaryModule -o $modulePackage > /dev/null 2>&1
    semodule -i $modulePackage > /dev/null 2>&1
    rm -rf $tmpDir
}
resDelimiter="################################################################################"

########################################################################
# Main                                                                  #
########################################################################

# Show usage
# Usage

#======================================================================
#===Get Arguments
#======================================================================
while [ $# -ne 0 ]; do
   arg=$1
   shift
   case $arg in
   -n|--number)
      number="$1"
      shift
      ;;
   -s|--sandbox)
       buildFlag="$1"
       shift
      ;;
   -r|--remove)
       removeCfg="$1"
       shift
      ;;
   -B|--branch)
       branch="$1"
       shift
      ;;
   -I|--install)
       install="$1"
       shift
       ;;
   --prepare)
       prepare="$1"
       shift
       ;;
   -c|--buildtype)
       buildtype="$1"
       shift
       ;;
   -m|--smartcard)
      smartcard="$1"
      shift
      ;;
   -F|--cdr)
      cdr="$1"
      shift
      ;;
   -f|--fips)
      fips="$1"
      shift
      ;;
   -U|--usb)
      usb="$1"
      shift
      ;;
   -C)
      clipboard="$1"
      shift
      ;;
   -P)
      printer="$1"
      shift
      ;;
   -a|--audioin)
      audioin="$1"
      shift
      ;;
   -T)
      T="$1"
      shift
      ;;
   -V|--vadc)
      vadc="$1"
      shift
      ;;
   --installpkg)
      installpkg="$1"
      shift
      ;;
   --vhcirebuild)
      vhcirebuild="$1"
      shift
      ;;
   --webcam)
      webcam="$1"
      shift
      ;;
   --ipv6)
      ipv6="$1"
      shift
      ;;
   --multisession)
      multisession="$1"
      shift
      ;;
   --collaboration-dependency-check)
      collabuidependencycheck="$1"
      shift
      ;;
   -N|--registered-name)
      registeredName="$1"
      shift
      ;;
   -M)
      managed="$1"
      shift
      ;;
   -b|--broker)
      broker="$1"
      shift
      ;;
   -d|--domain)
      domain="$1"
      shift
      ;;
   -u|--user)
      user="$1"
      shift
      ;;
   -p|--password)
      password="$1"
      shift
      ;;
   --rpm)
      rpmpkg="$1"
      shift
      ;;
   --product)
      product="$1"
      shift
      ;;
   *)
      echo "wrong cmdline options."
      Usage
      exit 1
      ;;
   esac
done

if [ -z $installpkg ]; then
    if [ -z $number ]; then
        echo build number is not specified. Use latest build
    fi
else
    number=$(echo $installpkg | grep -Po '\d{6,}')
fi

# check for ENV build number, e.g. when used with Jenkins
if [ -z $number ]; then
    if [ -n "$BUILDNOM" ]; then
        number=$BUILDNUM
    fi
fi

# Simple check of the system distribution.
if [ -e "/etc/centos-release" ] && grep -e "7.[0-9]" /etc/centos-release > /dev/null 2>&1 ; then
    sysDist="CentOS7"
elif [ -e "/etc/centos-release" ] && grep -e "8.[0-9]" /etc/redhat-release > /dev/null 2>&1 ; then
    sysDist="CentOS8"
elif [ -e "/etc/redhat-release" ] && grep -e "9.[0-9]" /etc/redhat-release > /dev/null 2>&1 ; then
    sysDist="RHEL9"
elif [ -e "/etc/redhat-release" ] && grep -e "8.[0-9]" /etc/redhat-release > /dev/null 2>&1 ; then
    sysDist="RHEL8"
elif [ -e "/etc/redhat-release" ] && grep -e "7.[0-9]" /etc/redhat-release > /dev/null 2>&1 ; then
    sysDist="RHEL7"
elif [ -e "/etc/debian_version" ] && grep -e "10.[0-9]" /etc/debian_version > /dev/null 2>&1 ; then
    sysDist="debian10"
elif [ -e "/etc/debian_version" ] && grep -e "11.[0-9]" /etc/debian_version > /dev/null 2>&1 ; then
    sysDist="debian11"
elif [ -e "/etc/debian_version" ] && grep -e "12.[0-10]" /etc/debian_version > /dev/null 2>&1 ; then
    sysDist="debian12"
elif grep -e "Red Hat" /etc/issue > /dev/null 2>&1; then
    sysDist="RHEL6"
elif grep -e "CentOS" /etc/issue > /dev/null 2>&1; then
    sysDist="CentOS6"
elif grep -e "24.04" /etc/os-release > /dev/null 2>&1; then
    sysDist="Ubuntu2404"
elif grep -e "Ubuntu 22" /etc/issue > /dev/null 2>&1; then
    sysDist="Ubuntu2204"
elif grep -e "Ubuntu 20" /etc/issue > /dev/null 2>&1; then
    sysDist="Ubuntu2004"
elif grep -e "Ubuntu 18" /etc/issue > /dev/null 2>&1; then
    sysDist="Ubuntu1804"
elif grep -e "SUSE.*Server 15" /etc/issue > /dev/null 2>&1; then
    sysDist="SLES15"
elif grep -e "SUSE.*Desktop 15" /etc/issue > /dev/null 2>&1; then
    sysDist="SLED15"
elif grep -e "SUSE.*Server 12" /etc/issue > /dev/null 2>&1; then
    sysDist="SLES12"
elif grep -e "SUSE.*Desktop 12" /etc/issue > /dev/null 2>&1; then
    sysDist="SLED12"
else
    echo "Not supported yet"
    exit -1
fi

# unset proxy to avoid download failure issue
unset https_proxy
unset http_proxy
Uninstall
if [ -z $installpkg ]; then
    Prepare
    if [ -z $number ]; then
        GetLatestBuild
    else
       GetBuildInfo
    fi
    DownloadBuild
else
    if echo $installpkg | grep rpm; then
        rpmpkg="yes"
    fi
fi
if [ "$rpmpkg" == "no" ]; then
    ExtractBuild
    vhciPatchFile=`ls /root/install_tmp/*/resources/vhci/patch/vhci.patch`
    v4l2PatchFile=`ls /root/install_tmp/*/resources/v4l2loopback/v4l2loopback.patch`
else
    InstallRPM
    vhciPatchFile="/usr/lib/omnissa/viewagent/resources/vhci/patch/vhci.patch"
    v4l2PatchFile="/usr/lib/omnissa/viewagent/resources/v4l2loopback/v4l2loopback.patch"
fi

if [ "$vhcirebuild" == "yes" -o "$usb" == "yes" ]; then
    ApplyVhciPatch
fi

if [ "$webcam" == "yes" ]; then
    ApplyV4L2Patch
fi

InstallBuild

if [ "$vadc" == "yes" ]; then
    ExtractVadc
    InstallVadc
fi

if [ "$product" == "horizonlinuxagent_codecov" ]; then
    # always start with new ENV
    rm -rf /PathToCodeCoverageData
    mkdir -p /PathToCodeCoverageData/bora/build/build
    chmod -R 777 /PathToCodeCoverageData
    if ! grep '^DefaultEnvironment=GCOV_PREFIX=\/PathToCodeCoverageData GCOV_PREFIX_STRIP=4' \
        /etc/systemd/system.conf > /dev/null 2>&1; then
        sed -i -e '$aDefaultEnvironment=GCOV_PREFIX=/PathToCodeCoverageData GCOV_PREFIX_STRIP=4' /etc/systemd/system.conf ;
    fi
    if [ "$sysDist" = "RHEL8" ]; then
       # set cups configuration for GCOV
       SetCupsCfg
       # set cups policy for selinux
       SetCupsPolicy
    fi
fi
