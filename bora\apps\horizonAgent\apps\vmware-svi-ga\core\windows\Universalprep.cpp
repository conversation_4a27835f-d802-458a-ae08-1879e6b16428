/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

#include "stdafx.h"

#include <core/windows/Universalprep.h>

using namespace svmga::common::util;
using namespace svmga::core::windows;
using namespace svmga::core::util;

enum OperationType { NewClone = 0, Refresh<PERSON>lone, ResyncClone, UnknownClone };

OperationType
GetOperationType(const std::wstring &operationType)
{
   if (operationType.compare(_T("NEW_CLONE")) == 0) {
      return OperationType::NewClone;
   } else if (operationType.compare(_T("REFRESH_CLONE")) == 0) {
      return OperationType::RefreshClone;
   } else if (operationType.compare(_T("RESYNC_CLONE")) == 0) {
      return OperationType::ResyncClone;
   }

   return OperationType::UnknownClone;
}

Universalprep::Universalprep()
{
   _ahu = coreutil::AgentHelperWrapper::GetInstance();
}

bool
Universalprep::CustomizeMaster()
{
   //
   // Verify "Use SVI" has already been set as 0 by the helper tool. Otherwise installation will
   // fail and revert since during first run since wsnm_jms will be waiting for the customization to
   // complete. Refer to following slack thread for more details:
   // https://omnissa.slack.com/archives/C081FAHF0JG/p1741642734151589
   //
   return VerifyUseSVI(0);
}

bool
Universalprep::CustomizeIT()
{
   bool bResult = false;
   std::wstring value;

   if (_cpi->IsTemplateCustomizationDone()) {
      SYSMSG_FUNC(Debug, _T("Template Customization Already Complete, Nothing To Do"));
      return true;
   }

   //
   // No vmfork so far. Perform customization for internal template.
   //
   SYSMSG_FUNC(Debug, _T("Template: Performing customization."));

   if (!_cpi->IsITCustomizationStartTimeSet()) {
      _Support->SetITCustomizationStartTime();
      // TODO: Revisit and correct the times. Now the total time assumes IT reboot only true for
      // cloneprep.
      _Support->SetITCustomizationTimeToReboot();
      _cpi->MarkITCustomizationStartTimeSet();
   }

   //
   // Notify server the current status of it customization
   //
   _CustState->SetCustomizationState(CUSTOMIZATION_STATE_RUNNING, SVM_PS_SUCCESS,
                                     NGVC_CATEGORY_CUSTOMIZATION, NGVC_INFO_CUSTOMIZATION_RUNNING,
                                     ERROR_SUCCESS);

   //
   // Create AgentIntegration Key
   //
   _AgentIntegration->CreateAgentIntegrationKey();

   //
   // Ask the helper tool to finish its job for internal template
   //
   if (!_ahu->LaunchTemplateHelper()) {
      SYSMSG_FUNC(Error, _T("Failed to launch agent helper in service mode for internal template"));
      goto Cleanup;
   }

   // Verify the poolID, which the helper tool should have set.
   if (!_cpi->IsPoolIdSet()) {
      // Don't quit, since there might be a latency.
      SYSMSG_FUNC(Warn, _T("PoolID was not set by the helper tool, or there was latency"));
   } else {
      SYSMSG_FUNC(Debug, _T("PoolID was successfully set by the helper tool"));
   }

   //
   // Sysprep generalize happen in a blocking thread
   //
   if (!_ahu->SysprepGeneralize()) {
      SYSMSG_FUNC(Error, _T("Failed to run sysprep generalize in the blocking process"));
      goto Cleanup;
   } else {
      SYSMSG_FUNC(Debug,
                  _T("Sysprep generalization finished successfully in the blocking process"));
   }

   // https://omnissa.atlassian.net/browse/UBI-360
   // Even sysprep thread exited with 0, we observed rare race condition in registry flush:
   // Lazy registry flush by sysprep & Universalprep caused write to CmdLine at about the same time,
   // then the value is blank (neither written is committed). So wait for the value is there then we
   // proceed.
   //
   if (!WaitForGeneralize()) {
      SYSMSG_FUNC(
         Error,
         _T("Sysprep generalization registry flush failed to update ImageState and/or CmdLine"));
      goto Cleanup;
   }

   SYSMSG_FUNC(
      Debug, _T("Sysprep generalization registry flush succeeded updating ImageState and CmdLine"));
   _cpi->MarkSysprepGeneralizeCompleted();

   // Here comes the real interception:
   value = _ahu->GetSysprepInterceptionString();
   _Registry->SetValue(SETUP_COMMAND_LINE_VALUE_NAME, value, RegType::SystemSetup);
   SYSMSG_FUNC(Debug, _T("Interception string was set as: %ws"), value.c_str());

   //
   // Set IP address to be released on shutdown
   //
   if (!_Network->ReleaseIpOnShutdown()) {
      SYSMSG_FUNC(Warn, _T("Failed to set IP address to be released on shutdown"));
      // Don't quit
   }

   // Clone's initial state, shutdown is required for snapshotting
   _cpi->MarkCloneShutdownNeeded();

   //
   // Mark template customization done
   //
   _cpi->MarkTemplateCustomizationDone();
   SYSMSG_FUNC(Debug, _T("Template: Marked Template Customization Done"));

   //
   // Notify server the final status of IT customization
   //
   _CustState->SetCustomizationState(CUSTOMIZATION_STATE_SUCCESS, SVM_PS_SUCCESS,
                                     NGVC_CATEGORY_CUSTOMIZATION, NGVC_INFO_CUSTOMIZATION_SUCCEEDED,
                                     ERROR_SUCCESS);

   //
   // Shutdown the template
   //
   _cpi->MarkTemplateShutdownDone();
   SYSMSG_FUNC(Debug, _T("Template: Marked Template Shutdown Done"));

   SYSMSG_FUNC(Debug, _T("Total IT Customization Time: %d seconds"),
               _Support->GetTotalITCustomizationTime() / 1000);

   SYSMSG_FUNC(Debug, _T("Template: About To Shutdown"));

   _System->Shutdown();

   bResult = true;

Cleanup:
   return bResult;
}

bool
Universalprep::CustomizeReplica()
{
   return true;
}

bool
Universalprep::CustomizeParent()
{
   return true;
}

bool
Universalprep::CustomizeClone()
{
   HRESULT hr = S_OK;
   bool bResult = false;
   DWORD dwWait = 0;

   if (_cpi->IsCloneCustomizationCompleted()) {
      SYSMSG_FUNC(Debug, _T("Clone LCM indicator: User Reboot"));
      SYSMSG_FUNC(Debug,
                  _T("A user/hypervisor/os/broker reboot without involving reconfig. No op"));
      return true;
   }

   // Indicates wsnm_jms will be waiting for the clone customization to complete.
   VerifyUseSVI(1);

   // This means the 1st start of the clone
   // Actually the VERY 1st start is the chain: helper tool-> specialize -> windeploy.exe, which
   // restarts the clone For more details:
   // https://omnissa.atlassian.net/wiki/spaces/HAC/pages/483628589/Pairing+Steps+for+Nutanix+Provisioning+Workflow
   if (_cpi->IsCloneShutdownNeeded()) {
      _Support->SetCloneCustomizationStartTime();

      //
      // Wait for Sysprep to complete
      //
      bResult = WaitForSysprep();
      if (!bResult) {
         SYSMSG_FUNC(Error, _T("Sysprep Customization Failed"));
         _CustState->SetCustomizationState(CUSTOMIZATION_STATE_ERROR,
                                           SVM_PS_SYSPREP_TIMEOUT_FAILURE, NGVC_CATEGORY_SYSPREP,
                                           _CustState->GetDescription(SVM_PS_SYSPREP_FAILED), hr);
         SYSMSG_FUNC(Debug, _T("Total Clone Sysprep Customization Time: %dms"),
                     _Support->GetTotalCloneSysprepCustomizationTime(true));
         goto Cleanup;
      } else {
         if (_System->IsShutdownEventSet()) {
            SYSMSG_FUNC(Debug, _T("Shutdown Event Set, Exiting"));
            goto Cleanup;
         }
      }

      SYSMSG_FUNC(Debug, _T("Starting desktop customization"));

      //
      // Fetch domain and host name
      //
      std::wstring hostName = _DomainJoin->GetHostName();
      std::wstring domainName = _DomainJoin->GetDomainName();
      SYSMSG_FUNC(Debug, _T("Clone's machine fqdn retrieved: %ws\\%ws"), domainName.c_str(),
                  hostName.c_str());

      //
      // Fetch AD server name and site (will set to registry)
      //
      if (!_DomainJoin->SetPreferredDC()) {
         SYSMSG_FUNC(Error, _T("Failed to set preferred DC"));
         goto Cleanup;
      }

      if (!_DomainJoin->SetPreferredSite()) {
         SYSMSG_FUNC(Error, _T("Failed to set preferred site"));
         goto Cleanup;
      }

      //
      // Use the plain ad server name & site in registry , since the ones from _DomainJoin
      // will have add leading domain name (from vmx.extraConfig), which we don't have yet
      std::wstring adServerName;
      if (!_GuestInfo->GetValue(PREFERRED_DC_VALUE_NAME, adServerName)) {
         SYSMSG_FUNC(Error, _T("Failed to get preferred DC"));
         goto Cleanup;
      }

      std::wstring adSite;
      if (!_GuestInfo->GetValue(PREFERRED_SITE_VALUE_NAME, adSite)) {
         SYSMSG_FUNC(Error, _T("Failed to get preferred site"));
         goto Cleanup;
      }

      SYSMSG_FUNC(
         Debug,
         _T("Ad server name (without leading domain): %ws, site (without leading domain): %ws"),
         adServerName.c_str(), adSite.c_str());

      //
      // Disables netlogon, enable after snapshot
      //
      DWORD dwStatus = SERVICE_DISABLED;
      // hr = _System->ControlSvc(NETLOGON_SERVICE_NAME,
      //    NGVC_SERVICE_SET_START_TYPE, dwStatus);
      // if (FAILED(hr)) {
      //    SYSMSG_FUNC(Warn, _T("Failed to disable Netlogon service: 0x%X"), hr);
      // }
      // else {
      //    SYSMSG_FUNC(Debug, _T("Disabled Netlogon service"));
      // }

      //
      // Ask the helper tool to finish its job for clone
      //
      if (!_ahu->LaunchCloneHelper(adServerName, adSite)) {
         SYSMSG_FUNC(Error, _T("Failed to launch agent helper in service mode for clone"));
         goto Cleanup;
      }

      //
      // Removes the agent helper since it is no longer needed
      //
      if (!DeleteFile(_T(AGENT_HELPER_FULL_PATH))) {
         SYSMSG_FUNC(Warn, _T("Failed to remove agent helper: %S, Error %d"),
                     AGENT_HELPER_FULL_PATH, GetLastError());
         // Do not fail clone customization
      } else {
         SYSMSG_FUNC(Debug, _T("Successfully removed agent helper: %S"), AGENT_HELPER_FULL_PATH);
      }

      //
      // Enables and launches Horizon Agent
      //
      dwStatus = SERVICE_AUTO_START;
      hr = _System->ControlSvc(HORIZON_AGENT_SERVICE_NAME, NGVC_SERVICE_SET_START_TYPE, dwStatus);
      if (FAILED(hr)) {
         SYSMSG_FUNC(Warn, _T("Failed to enable Horizon Agent service: 0x%X"), hr);
      } else {
         SYSMSG_FUNC(Debug, _T("Enabled Horizon Agent service"));
      }

      hr = _System->ControlSvc(HORIZON_AGENT_SERVICE_NAME, NGVC_SERVICE_START, dwStatus);
      if (FAILED(hr)) {
         SYSMSG_FUNC(Warn, _T("Failed to start Horizon Agent service: 0x%X"), hr);
      } else {
         SYSMSG_FUNC(Debug, _T("Started Horizon Agent service"));
      }

      //
      // Perform pre-shutdown customization steps
      //
      bResult = CustomizeClonePreShutdown();
      if (!bResult) {
         SYSMSG_FUNC(Error, _T("CustomizeClonePreShutdown failed"));
         goto Cleanup;
      }
   } else {
      /*
       * 2nd start of the clone, no need to shutdown again
       * Perform post-shutdown customization steps
       * Reaching here, Horizon Agent should be started and running,
       * so wait for reconfig to be done if necessary. Upon finish,
       * we will know if the clone is new, refresh or resync.
       */

      std::wstring operationType = _cpi->GetOperationType();
      SYSMSG_FUNC(Debug, _T("The clone OperationType is: %ws"), operationType.c_str());

      if (operationType.empty()) {
         hr = _Registry->WaitForValueExists(OPERATION_TYPE_VALUE_NAME, OPERATION_TYPE_MAX_WAIT_TIME,
                                            RegType::HorizonAgent);
         if (FAILED(hr)) {
            SYSMSG_FUNC(Error, _T("Clone OperationType is not set in a timely manner. Please ")
                               _T("check Horizon Agent logs"));
            goto Cleanup;
         }

         // Now we can get the OperationType
         operationType = _cpi->GetOperationType();
         SYSMSG_FUNC(Debug, _T("After wait, the clone OperationType is: %ws"),
                     operationType.c_str());
      }

      OperationType opType = GetOperationType(operationType);

      switch (opType) {
      case OperationType::NewClone:
         // First restart after snapshotting
         SYSMSG_FUNC(Debug, _T("Clone LCM indicator: New Clone"));

         bResult = CustomizeClonePostSnapshot();
         if (!bResult) {
            SYSMSG_FUNC(Error, _T("CustomizeClonePostSnapshot Failed"));
            goto Cleanup;
         }
         break;

      case OperationType::RefreshClone:
         // First restart after fast refresh
         SYSMSG_FUNC(Debug, _T("Clone LCM indicator: Refresh Clone"));

         // Verify the password & machine name is reset
         if (!_cpi->IsMachinePwdSet()) {
            SYSMSG_FUNC(Error, _T("Machine password is not set post fast refresh"));
         }

         bResult = CustomizeClonePostRefresh();
         if (!bResult) {
            SYSMSG_FUNC(Error, _T("CustomizeClonePostRefresh Failed"));
            goto Cleanup;
         }
         break;

      case OperationType::ResyncClone:
         // First restart after resync
         SYSMSG_FUNC(Debug, _T("Clone LCM indicator: Resync Clone"));

         // TODO: M3/M4 work
         break;
      default:
         SYSMSG_FUNC(Error, _T("Unknown operation type (or not supported yet): %ws"),
                     operationType.c_str());
         goto Cleanup;
      }

      if (!CustomizeClonePostShutdown()) {
         SYSMSG_FUNC(Error, _T("CustomizeClonePostShutdown Failed"));
         goto Cleanup;
      }

      // Restart finished. Customization done
      _cpi->MarkCloneCustomizationCompleted();

      SYSMSG_FUNC(Debug, _T("Clone customization successfully completed"));
   }

   bResult = true;

Cleanup:
   return bResult;
}

bool
Universalprep::CustomizeClonePreShutdown()
{
   HRESULT hr = S_OK;
   bool bResult = false;
   bool bCustomized = false;

   //
   // Set pre-customization agent integration values
   //
   _AgentIntegration->ParseAgentIntegrationValues(SVM_INTEGRATION_FLAGS_PARSE_PRE_VALUES);

   //
   // Secure Scripts
   //
   bResult = _Script->SecureScripts(_CustType->IsFastRefresh());
   if (bResult == false) {
      SYSMSG_FUNC(Error, _T("Failed Securing Scripts"));
   } else {
      SYSMSG_FUNC(Debug, _T("SecureScripts Succeeded"));
   }

   //
   // Universalprep domain join is already enabled, rename and domain join was already
   // done by Microsoft Universalprep process. Save preferred DC and site to guest
   // info for server.
   //
   SYSMSG_FUNC(Debug, _T("Sysprep domain join enabled. Skipping agent domain join."));
   _cpi->MarkCloneRenamed();
   _cpi->MarkCloneDomainJoined();

   //
   // Transition to snapshotting customization state
   // This will also set WaitForCheckpointing state in registry for Horizon
   // Agent which should communicate with Horizon Server to shutdown the VM for
   // snapshot and then power on
   //
   _CustState->SetCustomizationState(CUSTOMIZATION_STATE_SNAPSHOTTING, SVM_PS_SUCCESS,
                                     NGVC_CATEGORY_CUSTOMIZATION, NGVC_INFO_CUSTOMIZATION_RUNNING,
                                     ERROR_SUCCESS);

   //
   // Mark pre-shutdown customization successful
   // Horizon agent will perform shutdown
   //
   _cpi->MarkCloneShutdownDone();
   bCustomized = true;

   SYSMSG_FUNC(Debug, _T("Clone Customization Time Before Shutdown: %dms"),
               _Support->GetTotalCloneICCustomizationTime(false));

   return bCustomized;
}

bool
Universalprep::CustomizeClonePostShutdown()
{
   NotifyViewAgent nva;
   bool bResult = false;
   std::string custState = "";
   SvmPolicyState fastRefreshStatus = SVM_PS_SUCCESS;
   NotifyVdmStatusValue vdmStatus = CustomizationNotStarted;

   //
   // Verify if clone is in right state and reset states if needed
   // HIC Server should set clone customization state to postSnapshotting
   // NGA should set Vdm Status to CustomizationPostCheckpointing
   //
   if (_CustState->GetCustomizationState(custState) &&
       custState.compare(CUSTOMIZATION_STATE_POST_SNAPSHOTTING) == 0 &&
       nva.GetVdmStatus(vdmStatus) && vdmStatus == CustomizationPostCheckpointing) {
      SYSMSG_FUNC(Debug, _T("Clone customization state is: %S"), custState.c_str());
   } else {
      SYSMSG_FUNC(Warn,
                  _T("Unexpected clone customization state (%S) or vdm status ")
                  _T("Value (%d). Resetting states."),
                  custState.c_str(), vdmStatus);
      _CustState->SetCustomizationState(CUSTOMIZATION_STATE_POST_SNAPSHOTTING, SVM_PS_SUCCESS,
                                        NGVC_CATEGORY_CUSTOMIZATION,
                                        NGVC_INFO_CUSTOMIZATION_RUNNING, ERROR_SUCCESS);
   }

   //
   // Verify Trust
   //
   // TODO: M2: Re-enable trust verification. Refer to:
   // https://omnissa.atlassian.net/browse/UBI-354
   if (false && !_DomainJoin->VerifyTrustEx(_CustType->IsFastRefresh())) {
      // Change password only if fast refresh
      SYSMSG_FUNC(Error, _T("VerifyTrustEx Failed"));

      _CustState->SetCustomizationState(CUSTOMIZATION_STATE_ERROR, SVM_PS_TRUST_VERIFICATION_FAILED,
                                        NGVC_CATEGORY_TRUST_VERIFY, E_FAIL);
      goto Cleanup;
   } else {
      SYSMSG_FUNC(Debug, _T("Trust Verification Succeeded"));
   }

   //
   // Update the UPN in the registry
   //
   _DomainJoin->UpdateUPN();

   //
   // Prepare Persistent Disks
   //
   if (!PreparePersistentDisks()) {
      SYSMSG_FUNC(Error, _T("PreparePersistentDisks Failed"));
      _CustState->SetCustomizationState(CUSTOMIZATION_STATE_ERROR, SVM_PS_PERSISTENT_DISKS_FAILED,
                                        NGVC_CATEGORY_PERSISTENT_DISKS, E_FAIL);
      goto Cleanup;
   }

   //
   // Complete Customization
   //
   if (!CompleteCustomization()) {
      SYSMSG_FUNC(Debug, _T("CompleteCustomization Failed in %dms"),
                  _Support->GetTotalCloneCustomizationTime());
      goto Cleanup;
   } else {
      SYSMSG_FUNC(Debug, _T("CompleteCustomization Succeeded in %dms"),
                  _Support->GetTotalCloneCustomizationTime());
   }

   // Customization post shutdown succeeded.
   _CustState->SetCustomizationState(CUSTOMIZATION_STATE_SUCCESS, SVM_PS_SUCCESS,
                                     NGVC_CATEGORY_CUSTOMIZATION, NGVC_INFO_CUSTOMIZATION_SUCCEEDED,
                                     ERROR_SUCCESS);
   bResult = true;

Cleanup:

   _Support->GetTotalCloneCustomizationTime();
   return bResult;
}

bool
Universalprep::WaitForGeneralize()
{
   HRESULT hr =
      _Registry->WaitForString(SYSPREP_STATE_VALUE, SYSPREP_STATE_GENERALIZE_TO_OOBE,
                               SYSPREP_DEFAULT_REGISTRY_FLUSH_WAIT_TIME, RegType::Sysprep);
   if (FAILED(hr)) {
      SYSMSG_FUNC(Error, _T("Failed to wait for generalize/ImageState regkey to be flushed"));
      return false;
   }

   hr = _Registry->WaitForString(SETUP_COMMAND_LINE_VALUE_NAME, SETUP_COMMAND_LINE_DEFAULT_VALUE,
                                 SYSPREP_DEFAULT_REGISTRY_FLUSH_WAIT_TIME, RegType::SystemSetup);
   if (FAILED(hr)) {
      SYSMSG_FUNC(Error, _T("Failed to wait for generalize/CmdLine regkey to be flushed"));
      return false;
   }

   return true;
}

bool
Universalprep::WaitForOobe()
{
   HRESULT hr = S_OK;
   bool bRet = false;
   bool bExists = false;
   DWORD dwWaitTime = SYSPREP_DEFAULT_OOBE_MAX_WAIT_TIME;
   std::wstring strValue;

   _Registry->GetValue(SYSPREP_MAX_WAIT_TIME_VALUE_NAME, SYSPREP_DEFAULT_OOBE_MAX_WAIT_TIME,
                       dwWaitTime, RegType::Service);

   SYSMSG_FUNC(Debug, _T("Calling WaitForStringValue (Oobe)"));

   // OOBE after snapshot might finish before service starts. Check registry
   // state before waiting for OOBE.
   _Registry->GetValue(SYSPREP_STATE_VALUE, strValue, RegType::Sysprep);
   if (strValue.compare(SYSPREP_STATE_COMPLETE) == 0) {
      SYSMSG_FUNC(Debug, _T("Found expected value for OOBE: %ws"), strValue.c_str());
   } else {
      hr = _Registry->WaitForString(SYSPREP_STATE_VALUE, SYSPREP_STATE_COMPLETE, dwWaitTime,
                                    bExists, _System->GetShutdownEvent(), RegType::Sysprep);
      if (!bExists) {
         if (SUCCEEDED(hr) && _System->IsShutdownEventSet()) {
            bRet = true;
         } else if (hr == HRESULT_FROM_WIN32(ERROR_TIMEOUT)) {
            SYSMSG_FUNC(Error, _T("Timed out waiting for Oobe"));
         } else {
            SYSMSG_FUNC(Error, _T("WaitForStringValue Failed: 0x%X"), hr);
         }
         goto Cleanup;
      }
   }

   SYSMSG_FUNC(Debug, _T("Oobe Completed"));
   _cpi->MarkSysprepOobeCompleted();
   bRet = true;

Cleanup:
   return bRet;
}

bool
Universalprep::WaitForSysprep()
{
   bool bResult = false;
   NotifyViewAgent nva;

   if (_cpi->IsSysprepCompleted()) {
      SYSMSG_FUNC(Debug, _T("Sysprep Customization Already Completed"));
      return true;
   }

   // TODO: M3 work: add tick count support for generalization + specialization

   //
   // Set clone Universalprep customization start time
   //
   _Support->SetCloneSysprepCustomizationStartTime();

   //
   // Notify view agent customization is in progress
   //
   nva.MarkCustomizationInProgress();

   // There are 3 parts of sysprep: 1) generalization; 2) specialization; 3)oobe

   // Reaching this point, please note that:
   // 1) sysprep generalization, no need to wait: done in Universalprep::CustomizeIT
   //    and it must have succeeded, otherwise the clone SHALL not be forked
   if (!_cpi->IsSysprepGeneralizeCompleted()) {
      SYSMSG_FUNC(Error, _T("Sysprep generalize should be completed before clone can be forked"));
      goto Cleanup;
   }

   // 2) sysprep specialization, no need to wait: done in helper tool in native running mode.
   //    but it could fail, so we need to check if the status is still post-specialize
   if (!_cpi->IsSysprepSpecializeCompleted()) {
      // See if agent helper didn't succeed the specialization (for instance, file read error,
      // credential retrieval error)
      std::wstring strValue;
      _Registry->GetValue(SYSPREP_STATE_VALUE, strValue, RegType::Sysprep);
      if (strValue.compare(SYSPREP_STATE_GENERALIZE_TO_OOBE) == 0) {
         SYSMSG_FUNC(Error, _T("Agent helper pre-specialize errored out. Clone is still in ")
                            _T("pre-specialization state"));
         goto Cleanup;
      } else {
         SYSMSG_FUNC(Debug, _T("Agent helper pre-specialize finished correctly. Specialization ")
                            _T("should have finished"));
         _cpi->MarkSysprepSpecializeCompleted();
      }
   }

   // 3) OOBE: could be done or not, depending on the case
   if (!_cpi->IsSysprepOobeCompleted()) {
      SYSMSG_FUNC(Debug, _T("Waiting For Universalprep OOBE"));

      //
      // Transition to OOBE State
      //
      _CustState->SetCustomizationState(CUSTOMIZATION_STATE_OOBE, SVM_PS_SUCCESS,
                                        NGVC_CATEGORY_CUSTOMIZATION,
                                        NGVC_INFO_CUSTOMIZATION_RUNNING, ERROR_SUCCESS);

      if (WaitForOobe()) {
         if (!_System->IsShutdownEventSet()) {

            SYSMSG_FUNC(Debug, _T("Universalprep Time at end of OOBE: %dms"),
                        _Support->GetTotalCloneSysprepCustomizationTime(false));

            _cpi->MarkSysprepCompleted();
         }
      } else {
         SYSMSG_FUNC(Error, _T("Failed Waiting For OOBE"));
         goto Cleanup;
      }
   }

   SYSMSG_FUNC(Debug, _T("Total Clone Sysprep Customization Time: %d seconds"),
               _Support->GetTotalCloneSysprepCustomizationTime(false) / 1000);

   bResult = true;

Cleanup:

   return bResult;
}

bool
Universalprep::CustomizeClonePostSnapshot()
{
   HRESULT hr = S_OK;

   //
   // Enables and starts netlogon.
   //
   DWORD dwStatus = SERVICE_AUTO_START;
   hr = _System->ControlSvc(NETLOGON_SERVICE_NAME, NGVC_SERVICE_SET_START_TYPE, dwStatus);
   if (FAILED(hr)) {
      SYSMSG_FUNC(Warn, _T("Failed to enable Netlogon service: 0x%X"), hr);
      return false;
   } else {
      SYSMSG_FUNC(Debug, _T("Enabled Netlogon service"));
   }

   hr = _System->ControlSvc(NETLOGON_SERVICE_NAME, NGVC_SERVICE_START, dwStatus);
   if (FAILED(hr)) {
      SYSMSG_FUNC(Warn, _T("Failed to start Netlogon service: 0x%X"), hr);
      return false;
   } else {
      SYSMSG_FUNC(Debug, _T("Started Netlogon service"));
   }

   return true;
}

bool
Universalprep::CustomizeClonePostRefresh()
{
   // TODO: M3 work

   //
   // Decrypts the machine password using KeyVault.
   //

   //
   // Sets the machine password.
   //

   //
   // Enables and starts Netlogon.
   //

   //
   // Removes the password from the registry.
   //

   return true;
}

bool
Universalprep::VerifyUseSVI(DWORD expectedValue)
{
   SYSMSG_FUNC(Debug, _T("VerifyUseSVI started. 'Use SVI' is expected to be: %d"), expectedValue);

   DWORD dwValue = 0;
   HRESULT hr = S_OK;
   hr = _Registry->GetValue(SVM_USE_SVI_VALUE_NAME, -1, dwValue, RegType::NodeManager);
   if (FAILED(hr)) {
      SYSMSG_FUNC(Error, L"Failed reading 'Use SVI': 0x%X", hr);
      return false;
   }
   if (dwValue != expectedValue) {
      SYSMSG_FUNC(Error, L"'Use SVI' is NOT set to expected value. Actual value is: %d", dwValue);
      return false;
   }

   SYSMSG_FUNC(Debug, _T("VerifyUseSVI finished successfully"));
   return true;
}