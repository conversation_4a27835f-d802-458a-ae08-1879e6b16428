/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */
/*
 * unityMgrTest.cc -
 *
 *    Unit Test of unityMgr.cc
 */


#include <gmock/gmock.h>
#include <gtest/gtest.h>
#include "common/tests/guestOpsMKSControlUTMock.hh"
#include "common/module.hh"
#include "cui/mks/mksScreenMgrMKSControl.hh"
#include "cui/unity/unityFailureReasons.hh"
#include "gcd.h"
#include "guest_os_common.h"
#include "msg.h"
#include "win32/tests/baseUnitTest.h"
#include "win32/tests/vmUTMock.h"
#include "win32/tests/mksUTMock.h"
#include "win32/tests/unityMgrUTMock.h"
#include "win32/tests/unityWindowUTMock.h"
#include "utMock.h"
#include "vm.h"
#include "vm_product.h"

using namespace crt::win32;

using ::testing::_;
using ::testing::AnyNumber;
using ::testing::NiceMock;

class CUIUnityMgrUnitTest : public BaseUnitTest {};

namespace cui {

static sigc::slot<void> gDelayedCallback;
static unsigned int gDelayedMs = 0;

/*
 * FakedOnScheduleCallback --
 *
 *      Faked delay callback.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

static sigc::connection
FakedOnScheduleCallback(sigc::slot<void> callback, // IN
                        unsigned int ms)           // IN
{
   gDelayedCallback = callback;
   gDelayedMs = ms;
   return sigc::connection(callback);
}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::TestOn --
 *
 *    Test cui::UnityMgr::On method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CUIUnityMgrUnitTest, TestOn)
{
   VMUTMock *vm =
      new NiceMock<VMUTMock>("target", "", crt::win32::CRT_GHI_MESSAGES_OVER_MKSCONTROL);
   vm->Init();
   auto mks = vm->GetMKS();
   EXPECT_TRUE(mks);
   UnityMgrUTMock *mgr = dynamic_cast<UnityMgrUTMock *>(vm->GetUnityMgr());

   // Test return when VM is already in unity
   {
      VMOCK(&cui::UnityMgr::IsOn).Will(true);
      EXPECT_CALL(*mgr, CreateUnityConnectionMock).Times(0);
      EXPECT_CALL(*mgr, SetUnityStateMock).Times(0);
      mgr->cui::UnityMgr::On();
      testing::Mock::VerifyAndClearExpectations(mgr);
      mgr->cui::UnityMgr::Off();
   }

   // Test guest can not create unity connection
   {
      EXPECT_CALL(*mgr, CreateUnityConnectionMock).WillRepeatedly([]() { return false; });
      EXPECT_CALL(*mgr, SetUnityStateMock).Times(0);
      mgr->cui::UnityMgr::On();
      testing::Mock::VerifyAndClearExpectations(mgr);
      mgr->cui::UnityMgr::Off();
   }

   // Test enterUnityStarting call UnityMgr::Off
   {
      EXPECT_CALL(*mgr, CreateUnityConnectionMock).WillRepeatedly([&mgr]() {
         mgr->cui::UnityMgr::Off();
         return true;
      });
      EXPECT_CALL(*mgr, SetUnityStateMock).Times(1);
      mgr->cui::UnityMgr::On();
      testing::Mock::VerifyAndClearExpectations(mgr);
      mgr->cui::UnityMgr::Off();
   }
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::TestPause --
 *
 *    Test cui::UnityMgr::Pause method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CUIUnityMgrUnitTest, TestPause)
{
   VMUTMock *vm =
      new NiceMock<VMUTMock>("target", "", crt::win32::CRT_GHI_MESSAGES_OVER_MKSCONTROL);
   vm->Init();
   auto mks = vm->GetMKS();
   EXPECT_TRUE(mks);
   UnityMgrUTMock *mgr = dynamic_cast<UnityMgrUTMock *>(vm->GetUnityMgr());

   // Test unity on->pause->pause
   {
      VMOCK(&cui::UnityMgr::IsOn).Will(true);
      mgr->cui::UnityMgr::Pause();
      VMOCK(&cui::UnityMgr::DestroyAllWindows).Times(0);
      mgr->cui::UnityMgr::Pause();
   }

   // Test unity on->pause
   {
      VMOCK(&cui::UnityMgr::IsOn).Will(true);
      mgr->cui::UnityMgr::Off();
      VMOCK(&cui::UnityMgr::DestroyAllWindows).Times(1);
      mgr->cui::UnityMgr::Pause();
   }
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::TestUnPause --
 *
 *    Test cui::UnityMgr::UnPause method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CUIUnityMgrUnitTest, TestUnPause)
{
   VMUTMock *vm =
      new NiceMock<VMUTMock>("target", "", crt::win32::CRT_GHI_MESSAGES_OVER_MKSCONTROL);
   vm->Init();
   auto mks = vm->GetMKS();
   EXPECT_TRUE(mks);
   UnityMgrUTMock *mgr = dynamic_cast<UnityMgrUTMock *>(vm->GetUnityMgr());

   // Test unity on->unpause
   {
      VMOCK(&cui::UnityMgr::IsOn).Will(true);
      EXPECT_CALL(*vm, OnUnityStateChangedMock).Times(0);
      mgr->cui::UnityMgr::Unpause();
      testing::Mock::VerifyAndClearExpectations(mgr);
      testing::Mock::VerifyAndClearExpectations(vm);
      mgr->cui::UnityMgr::Off();
   }

   // Test unity on->pause->unpase
   {
      VMOCK(&cui::UnityMgr::IsOn).Will(true);
      mgr->cui::UnityMgr::Pause();
      EXPECT_CALL(*vm, OnUnityStateChangedMock).Times(1);
      mgr->cui::UnityMgr::Unpause();
      testing::Mock::VerifyAndClearExpectations(mgr);
      testing::Mock::VerifyAndClearExpectations(vm);
      mgr->cui::UnityMgr::Off();
   }
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::FakeUnityOnReadyChanged --
 *
 *    FakeUnityOnReadyChanged method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

static void FakeUnityOnReadyChanged() {};


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::TestSetEnterUnityOnReady --
 *
 *    Test cui::UnityMgr::SetEnterUnityOnReady method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CUIUnityMgrUnitTest, TestSetEnterUnityOnReady)
{
   VMUTMock *vm =
      new NiceMock<VMUTMock>("target", "", crt::win32::CRT_GHI_MESSAGES_OVER_MKSCONTROL);
   vm->Init();
   auto mks = vm->GetMKS();
   EXPECT_TRUE(mks);
   UnityMgrUTMock *mgr = dynamic_cast<UnityMgrUTMock *>(vm->GetUnityMgr());
   mgr->enterUnityOnReadyChanged.connect(sigc::ptr_fun(&FakeUnityOnReadyChanged));

   // unity ready
   {
      VMOCK(&FakeUnityOnReadyChanged).Times(1);
      mgr->SetEnterUnityOnReady(true);
      EXPECT_TRUE(mgr->mEnterUnityOnReady);
      EXPECT_TRUE(mgr->GetEnterUnityOnReady());
   }

   // unity not ready
   {
      VMOCK(&FakeUnityOnReadyChanged).Times(1);
      mgr->SetEnterUnityOnReady(false);
      EXPECT_TRUE(!mgr->mEnterUnityOnReady);
      EXPECT_TRUE(!mgr->GetEnterUnityOnReady());
   }
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::TestOff --
 *
 *    Test cui::UnityMgr::Off method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CUIUnityMgrUnitTest, TestOff)
{
   VMUTMock *vm =
      new NiceMock<VMUTMock>("target", "", crt::win32::CRT_GHI_MESSAGES_OVER_MKSCONTROL);
   vm->Init();
   auto mks = vm->GetMKS();
   EXPECT_TRUE(mks);
   UnityMgrUTMock *mgr = dynamic_cast<UnityMgrUTMock *>(vm->GetUnityMgr());

   // Test unity off->off
   {
      VMOCK(&cui::UnityMgr::DestroyAllWindows).Times(0);
      mgr->cui::UnityMgr::Off();
   }

   // Test unity on->off
   {
      VMOCK(&cui::UnityMgr::IsOn).Will(true);
      VMOCK(&cui::UnityMgr::DestroyAllWindows).Times(1);
      mgr->cui::UnityMgr::Off();
      testing::Mock::VerifyAndClearExpectations(mgr);
   }
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::TestSendKeyPress --
 *
 *    Test cui::UnityMgr::SendKeyPress method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CUIUnityMgrUnitTest, TestSendKeyPress)
{
   VMUTMock *vm =
      new NiceMock<VMUTMock>("target", "", crt::win32::CRT_GHI_MESSAGES_OVER_MKSCONTROL);
   vm->Init();
   auto mks = dynamic_cast<crt::win32::MKSUTMock *>(vm->GetMKS());
   KeyboardMapping_Init();
   EXPECT_TRUE(mks);
   UnityMgrUTMock *mgr = dynamic_cast<UnityMgrUTMock *>(vm->GetUnityMgr());
   VScancode code = 0x1;
   std::string keySequence;
   EXPECT_CALL(*mgr, SendKeyEventMock(_))
      .Times(2)
      .WillRepeatedly([&keySequence, &code](const cui::UnityKeyEvent &keyEvent) {
         if (keyEvent.down && (keyEvent.key == code)) {
            keySequence += "keyDown";
         } else if (!keyEvent.down && (keyEvent.key == code)) {
            keySequence += "keyUp";
         } else {
            keySequence += "Unknown";
         }
      });
   mgr->SendKeyPress(code);
   EXPECT_TRUE(keySequence == "keyDownkeyUp");
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::TestSendUnicodeCodePoint --
 *
 *    Test cui::UnityMgr::SendUnicodeCodePoint method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CUIUnityMgrUnitTest, TestSendUnicodeCodePoint)
{
   VMUTMock *vm =
      new NiceMock<VMUTMock>("target", "", crt::win32::CRT_GHI_MESSAGES_OVER_MKSCONTROL);
   vm->Init();
   auto mks = dynamic_cast<crt::win32::MKSUTMock *>(vm->GetMKS());
   KeyboardMapping_Init();
   EXPECT_TRUE(mks);
   UnityMgrUTMock *mgr = dynamic_cast<UnityMgrUTMock *>(vm->GetUnityMgr());
   unsigned int unicodeCode = 0x55;
   bool unicodeMatched = false;
   {
      VMOCK(&cui::UnityMgr::IsOn).Will(true);
      VMOCK(&cui::MKS::SendUnicodeCodePoint)
         .WillOnce([&unicodeMatched, &unicodeCode](unsigned int unicodeCodePoint, uint64 timestamp,
                                                   cui::AbortSlot onAbort, cui::DoneSlot onDone) {
            unicodeMatched = (unicodeCodePoint == unicodeCode) && (!timestamp);
         });
      mgr->SendUnicodeCodePoint(unicodeCode, 0);
      EXPECT_TRUE(unicodeMatched);
   }
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::TestSendKeyEvent --
 *
 *    Test cui::UnityMgr::SendKeyEvent method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CUIUnityMgrUnitTest, TestSendKeyEvent)
{
   VMUTMock *vm =
      new NiceMock<VMUTMock>("target", "", crt::win32::CRT_GHI_MESSAGES_OVER_MKSCONTROL);
   vm->Init();
   auto mks = dynamic_cast<crt::win32::MKSUTMock *>(vm->GetMKS());
   KeyboardMapping_Init();
   EXPECT_TRUE(mks);
   UnityMgrUTMock *mgr = dynamic_cast<UnityMgrUTMock *>(vm->GetUnityMgr());

   // Test SendKeyEvent in grab pending state
   {
      VMOCK(&cui::UnityMgr::IsOn).Will(true);
      mgr->cui::UnityMgr::Ungrab();
      VMOCK((void(cui::UnityMgr::*)(void)) & cui::UnityMgr::Grab).Times(1);
      VMOCK(&cui::UnityMgr::IsGrabPending).Will(true);
      EXPECT_CALL(*mks, SendKeyMock).Times(0);
      mgr->cui::UnityMgr::SendKeyEvent(cui::UnityKeyEvent(0x30, false));
      EXPECT_TRUE(mgr->mQueuedInputEvents.size() == 1);
      cui::UnityKeyEvent *e = dynamic_cast<UnityKeyEvent *>(mgr->mQueuedInputEvents[0]);
      EXPECT_TRUE((e->key == 0x30) && (e->down == false));
      testing::Mock::VerifyAndClearExpectations(mks);
   }

   // Test SendKeyEvent in grab state
   {
      VMOCK(&cui::UnityMgr::IsOn).Will(true);
      VMOCK(&cui::UnityMgr::IsGrabPending).Will(false);
      bool keyMatched = false;
      EXPECT_CALL(*mks, SendKeyMock)
         .WillOnce([&mgr, &keyMatched](HIDUsage usbHid, bool isKeyDown, cui::AbortSlot onAbort,
                                       cui::DoneSlot onDone) {
            keyMatched = (isKeyDown == false) &&
                         (usbHid.usageCode == KeyboardMapping_VScanToHID(0x30).usageCode);
         });
      mgr->cui::UnityMgr::SendKeyEvent(cui::UnityKeyEvent(0x30, false));
      testing::Mock::VerifyAndClearExpectations(mks);
      EXPECT_TRUE(keyMatched);
   }
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::TestHandlePendingTopWindows --
 *
 *    Test cui::UnityMgr::HandlePendingTopWindows method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CUIUnityMgrUnitTest, TestHandlePendingTopWindows)
{
   VMUTMock *vm =
      new NiceMock<VMUTMock>("target", "", crt::win32::CRT_GHI_MESSAGES_OVER_MKSCONTROL);
   vm->Init();
   auto mks = dynamic_cast<crt::win32::MKSUTMock *>(vm->GetMKS());
   EXPECT_TRUE(mks);
   UnityMgrUTMock *mgr = dynamic_cast<UnityMgrUTMock *>(vm->GetUnityMgr());
   dispatch_sync(dispatch_get_main_queue(), [&mgr]() {
      mgr->UpdateUnityWindowAdd((UnityWindowId)0x123, "fakedWindowPath", "fakedExePath",
                                "fakedEntitlementId");
      mgr->UpdateUnityWindowAdd((UnityWindowId)0x456, "fakedWindowPath", "fakedExePath",
                                "fakedEntitlementId");

      // Add windows to ZOrder
      mgr->mWindowZOrder.push_back(0x456);
      mgr->mWindowZOrder.push_back(0x123);
   });

   // Test no pending windows to order front
   {
      mgr->mPendingTopWindows.clear();
      VMOCK(&cui::UnityMgr::ClearPendingTopWindows).Times(1);
      EXPECT_TRUE(!mgr->HandlePendingTopWindows());
   }

   // Test order window 0x123 to front and previous top window is 0x456
   {
      auto topWindow = dynamic_cast<UnityWindowUTMock *>(mgr->GetWindowById((UnityWindowId)0x123));
      auto previousTopWindow =
         dynamic_cast<UnityWindowUTMock *>(mgr->GetWindowById((UnityWindowId)0x456));
      previousTopWindow->isTopmost = true;
      mgr->mPendingTopWindows.insert(0x123);
      EXPECT_CALL(*topWindow, OrderFrontMock).Times(1);
      VMOCK(&cui::UnityMgr::ClearPendingTopWindows).Times(1);
      EXPECT_CALL(*previousTopWindow, OrderFrontMock).Times(0);
      EXPECT_TRUE(mgr->HandlePendingTopWindows());
   }

   // Test mWindowZOrder contains an invalid window id 0x110
   {
      mgr->mPendingTopWindows.clear();
      mgr->mPendingTopWindows.insert(0x123);
      mgr->mWindowZOrder.clear();
      mgr->mWindowZOrder.push_back(0x110);
      VMOCK(&cui::UnityMgr::ClearPendingTopWindows).Times(0);
      EXPECT_TRUE(!mgr->HandlePendingTopWindows());
   }

   // Test mPendingTopWindows contains an invalid window id 0x1
   {
      mgr->mPendingTopWindows.clear();
      mgr->mPendingTopWindows.insert(0x1);
      VMOCK(&cui::UnityMgr::ClearPendingTopWindows).Times(0);
      EXPECT_TRUE(!mgr->HandlePendingTopWindows());
   }

   dispatch_sync(dispatch_get_main_queue(), [&mgr]() {
      mgr->UpdateUnityWindowRemove((UnityWindowId)0x123);
      mgr->UpdateUnityWindowRemove((UnityWindowId)0x456);
   });
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::TestSendMouseEvent --
 *
 *    Test cui::UnityMgr::SendMouseEvent method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CUIUnityMgrUnitTest, TestSendMouseEvent)
{
   VMUTMock *vm =
      new NiceMock<VMUTMock>("target", "", crt::win32::CRT_GHI_MESSAGES_OVER_MKSCONTROL);
   vm->Init();
   auto mks = dynamic_cast<crt::win32::MKSUTMock *>(vm->GetMKS());
   KeyboardMapping_Init();
   EXPECT_TRUE(mks);
   UnityMgrUTMock *mgr = dynamic_cast<UnityMgrUTMock *>(vm->GetUnityMgr());
   cui::UnityMouseEvent mouseEvent;
   mouseEvent.x = 80;
   mouseEvent.y = 80;

   // Test SendMouseEvent in grab pending state
   {
      VMOCK(&cui::UnityMgr::IsOn).Will(true);
      mgr->cui::UnityMgr::Ungrab();
      EXPECT_CALL(*mks, SendUnityMouseEventMock).Times(0);
      VMOCK((void(cui::UnityMgr::*)(void)) & cui::UnityMgr::Grab).Times(1);
      VMOCK(&cui::UnityMgr::IsGrabPending).Will(true);
      mgr->cui::UnityMgr::SendMouseEvent(mouseEvent);
      EXPECT_TRUE(mgr->mQueuedInputEvents.size() == 1);
      cui::UnityMouseEvent *e = dynamic_cast<UnityMouseEvent *>(mgr->mQueuedInputEvents[0]);
      EXPECT_TRUE((e->x == mouseEvent.x) && (e->y == mouseEvent.y));
      testing::Mock::VerifyAndClearExpectations(mks);
   }

   // Test SendMouseEvent in grab state
   {
      VMOCK(&cui::UnityMgr::IsOn).Will(true);
      VMOCK(&cui::UnityMgr::IsGrabPending).Will(false);
      bool mouseMatched = false;
      EXPECT_CALL(*mks, SendUnityMouseEventMock)
         .WillOnce([&mgr, &mouseMatched](const cui::UnityMouseEvent &mouseEvent,
                                         cui::AbortSlot onAbort, cui::DoneSlot onDone) {
            mouseMatched = (mouseEvent.x == 80) && (mouseEvent.y == 80);
         });
      mgr->cui::UnityMgr::SendMouseEvent(mouseEvent);
      testing::Mock::VerifyAndClearExpectations(mks);
      EXPECT_TRUE(mouseMatched);
   }
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::TestSendTouchEvent --
 *
 *    Test cui::UnityMgr::SendTouchEvent method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CUIUnityMgrUnitTest, TestSendTouchEvent)
{
   VMUTMock *vm =
      new NiceMock<VMUTMock>("target", "", crt::win32::CRT_GHI_MESSAGES_OVER_MKSCONTROL);
   vm->Init();
   auto mks = dynamic_cast<crt::win32::MKSUTMock *>(vm->GetMKS());
   KeyboardMapping_Init();
   EXPECT_TRUE(mks);
   UnityMgrUTMock *mgr = dynamic_cast<UnityMgrUTMock *>(vm->GetUnityMgr());
   cui::UnityTouchPoint touchPoint;
   touchPoint.contactId = 1;
   touchPoint.flags = 1;
   touchPoint.x = 1;
   touchPoint.y = 1;
   cui::UnityTouchEvent e;
   e.points.push_back(touchPoint);

   // Test SendTouchEvent in grab pending state
   {
      VMOCK(&cui::UnityMgr::IsOn).Will(true);
      mgr->cui::UnityMgr::Ungrab();
      VMOCK(&cui::MKS::SendUnityTouchEvent).Times(0);
      VMOCK((void(cui::UnityMgr::*)(void)) & cui::UnityMgr::Grab).Times(1);
      VMOCK(&cui::UnityMgr::IsGrabPending).Will(true);
      mgr->cui::UnityMgr::SendTouchEvent(e);
      EXPECT_TRUE(mgr->mQueuedInputEvents.size() == 1);
      cui::UnityTouchEvent *e = dynamic_cast<UnityTouchEvent *>(mgr->mQueuedInputEvents[0]);
      EXPECT_TRUE(e->points.size() == 1);
      EXPECT_TRUE((e->points[0].x == 1) && (e->points[0].y == 1) && (e->points[0].flags == 1) &&
                  (e->points[0].contactId == 1));
      testing::Mock::VerifyAndClearExpectations(mks);
   }

   // Test SendKeyEvent in grab state
   {
      VMOCK(&cui::UnityMgr::IsOn).Will(true);
      VMOCK(&cui::UnityMgr::IsGrabPending).Will(false);
      bool touchMatched = false;
      VMOCK(&cui::MKS::SendUnityTouchEvent)
         .WillOnce([&mgr, &touchMatched](const cui::UnityTouchEvent &touchEvent,
                                         cui::AbortSlot onAbort, cui::DoneSlot onDone) {
            touchMatched = (touchEvent.points.size() == 1) && (touchEvent.points[0].x == 1) &&
                           (touchEvent.points[0].y == 1) && (touchEvent.points[0].contactId == 1) &&
                           (touchEvent.points[0].flags == 1);
         });
      mgr->cui::UnityMgr::SendTouchEvent(e);
      testing::Mock::VerifyAndClearExpectations(mks);
      EXPECT_TRUE(touchMatched);
   }
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::TestGrab --
 *
 *    Test cui::UnityMgr::Grab method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CUIUnityMgrUnitTest, TestGrab)
{
   VMUTMock *vm =
      new NiceMock<VMUTMock>("target", "", crt::win32::CRT_GHI_MESSAGES_OVER_MKSCONTROL);
   vm->Init();
   auto mks = dynamic_cast<crt::win32::MKSUTMock *>(vm->GetMKS());
   KeyboardMapping_Init();
   EXPECT_TRUE(mks);
   UnityMgrUTMock *mgr = dynamic_cast<UnityMgrUTMock *>(vm->GetUnityMgr());
   dispatch_sync(dispatch_get_main_queue(), [&mgr]() {
      mgr->UpdateUnityWindowAdd((UnityWindowId)0x123, "fakedWindowPath", "fakedExePath",
                                "fakedEntitlementId");
   });
   auto firstWindow = (mgr->GetWindowById((UnityWindowId)0x123));

   // Test Unity is already grabbed
   {
      VMOCK(&cui::UnityMgr::IsGrabbed).Will(true);
      EXPECT_CALL(*mks, SendGrabRequestMock).Times(0);
      mgr->cui::UnityMgr::Grab(cui::MKS::GRAB);
      testing::Mock::VerifyAndClearExpectations(mks);
   }

   // Test Product is PRODUCT_FUSION
   {
      VMOCK(&cui::UnityMgr::IsGrabbed).Will(false);
      VMOCK(&ProductState_GetProduct).Will(PRODUCT_FUSION);
      bool inWindowMatched = false;
      bool grabStateMatched = false;
      VMOCK(&cui::MKS::SetMouseInMKSWindow)
         .WillOnce([&inWindowMatched](bool inWindow, AbortSlot onAbort, DoneSlot onDone) {
            inWindowMatched = (inWindow == true);
         });
      EXPECT_CALL(*mks, SetGrabStateMock)
         .WillOnce(
            [&grabStateMatched](cui::MKS::GrabState grabState, AbortSlot onAbort, DoneSlot onDone) {
               grabStateMatched = (grabState == cui::MKS::GRAB);
            });
      mgr->cui::UnityMgr::Grab(cui::MKS::GRAB, firstWindow);
      testing::Mock::VerifyAndClearExpectations(mks);
      EXPECT_TRUE(grabStateMatched);
      EXPECT_TRUE(inWindowMatched);
   }

   // Test Unity is ungrabbed
   {
      VMOCK(&cui::UnityMgr::IsGrabbed).Will(false);
      VMOCK(&ProductState_GetProduct).Will(PRODUCT_VDM_CLIENT);
      bool grabRequestMatched = false;
      EXPECT_CALL(*mks, SendGrabRequestMock)
         .WillOnce([&grabRequestMatched, &firstWindow](cui::MKSWindowID windowID, bool motionGrab,
                                                       AbortSlot onAbort, DoneSlot onDone) {
            grabRequestMatched = (windowID == firstWindow->GetMKSWindowID() && motionGrab == false);
         });
      mgr->cui::UnityMgr::Grab(cui::MKS::GRAB, firstWindow);
      testing::Mock::VerifyAndClearExpectations(mks);
      EXPECT_TRUE(grabRequestMatched);
   }

   bool focusMatched = false;

   // Test UnityMgr::Grab(const UnityWindow* window,  unsigned int focusDelayMS)
   {
      EXPECT_CALL(*mgr, SetFocusedWindowMock)
         .WillOnce(
            [&focusMatched, &firstWindow](const cui::UnityWindow *window, unsigned int delayMS) {
               focusMatched = (window == firstWindow) && (delayMS == 1000);
               return true;
            });
      mgr->cui::UnityMgr::Grab(firstWindow, 1000);
      testing::Mock::VerifyAndClearExpectations(mgr);
      EXPECT_TRUE(focusMatched);
   }

   dispatch_sync(dispatch_get_main_queue(),
                 [&mgr]() { mgr->UpdateUnityWindowRemove((UnityWindowId)0x123); });
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::TestUngrab --
 *
 *    Test cui::UnityMgr::Ungrab method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CUIUnityMgrUnitTest, TestUngrab)
{
   VMUTMock *vm =
      new NiceMock<VMUTMock>("target", "", crt::win32::CRT_GHI_MESSAGES_OVER_MKSCONTROL);
   vm->Init();
   auto mks = dynamic_cast<crt::win32::MKSUTMock *>(vm->GetMKS());
   EXPECT_TRUE(mks);
   UnityMgrUTMock *mgr = dynamic_cast<UnityMgrUTMock *>(vm->GetUnityMgr());

   // Ungrabbed -> Ungrabbed
   {
      VMOCK(&cui::UnityMgr::IsUngrabbed).Will(true);
      EXPECT_CALL(*mks, SetGrabStateMock).Times(0);
      mgr->cui::UnityMgr::Ungrab(cui::MKS::UNGRAB_HARD, true);
      testing::Mock::VerifyAndClearExpectations(mks);
   }

   // Grabbed -> Ungrabbed && Unfocus guest unity window
   {
      bool grabStateMatched = false;
      VMOCK(&cui::UnityMgr::IsUngrabbed).Will(false);
      cui::AbortSlot SetGrabStateAbort;
      cui::DoneSlot SetGrabStateDone;
      EXPECT_CALL(*mks, SetGrabStateMock)
         .WillOnce([&grabStateMatched, &SetGrabStateAbort, &SetGrabStateDone](
                      cui::MKS::GrabState grabState, AbortSlot onAbort, DoneSlot onDone) {
            grabStateMatched = (grabState == cui::MKS::UNGRAB_HARD);
            SetGrabStateDone = onDone;
            SetGrabStateAbort = onAbort;
         });
      bool windowMatched = false;
      EXPECT_CALL(*mgr, SetFocusedWindowMock)
         .WillOnce([&windowMatched](const cui::UnityWindow *window, unsigned int delayMS) {
            windowMatched = (window == NULL);
            return true;
         });
      EXPECT_CALL(*mgr, ReleaseModifierKeysMock).Times(1);
      cui::ScheduleCallback = sigc::ptr_fun(&FakedOnScheduleCallback);
      mgr->cui::UnityMgr::Ungrab(cui::MKS::UNGRAB_HARD, true);
      testing::Mock::VerifyAndClearExpectations(mks);
      testing::Mock::VerifyAndClearExpectations(mgr);
      EXPECT_TRUE(gDelayedMs == cui::UnityMgr::MKS_GRABSTATE_CHANGE_TIMEOUT);
      EXPECT_TRUE(grabStateMatched);
      EXPECT_TRUE(mgr->mUnityGrabState == cui::UnityMgr::UNITY_UNGRAB_PENDING);
      EXPECT_TRUE(windowMatched);

      // Test OnDone callback
      mgr->mUnityGrabState = cui::UnityMgr::UNITY_UNGRAB_PENDING;
      SetGrabStateDone();
      EXPECT_TRUE(mgr->mUnityGrabState == cui::UnityMgr::UNITY_UNGRABBED);

      // Test OnAbort callback
      mgr->mUnityGrabState = cui::UnityMgr::UNITY_UNGRAB_PENDING;
      SetGrabStateAbort(
         false, Error(GetLocalString(MSGID(cui.vcloud.nullCnx) "Connection has been destroyed.")));
      EXPECT_TRUE(mgr->mUnityGrabState == cui::UnityMgr::UNITY_UNGRABBED);
   }

   // Grabbed -> Ungrabbed && Not unfocus guest unity window
   {
      bool grabStateMatched = false;
      VMOCK(&cui::UnityMgr::IsUngrabbed).Will(false);
      EXPECT_CALL(*mks, SetGrabStateMock)
         .WillOnce(
            [&grabStateMatched](cui::MKS::GrabState grabState, AbortSlot onAbort, DoneSlot onDone) {
               grabStateMatched = (grabState == cui::MKS::UNGRAB_HARD);
            });
      EXPECT_CALL(*mgr, SetFocusedWindowMock).Times(0);
      EXPECT_CALL(*mgr, ReleaseModifierKeysMock).Times(0);
      mgr->cui::UnityMgr::Ungrab(cui::MKS::UNGRAB_HARD, false);
      testing::Mock::VerifyAndClearExpectations(mks);
      testing::Mock::VerifyAndClearExpectations(mgr);
      EXPECT_TRUE(mgr->mUnityGrabState == cui::UnityMgr::UNITY_UNGRAB_PENDING);
      EXPECT_TRUE(grabStateMatched);
   }
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::TestOnMKSGrabStateChanged --
 *
 *    Test cui::UnityMgr::OnMKSGrabStateChanged method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CUIUnityMgrUnitTest, TestOnMKSGrabStateChanged)
{
   VMUTMock *vm =
      new NiceMock<VMUTMock>("target", "", crt::win32::CRT_GHI_MESSAGES_OVER_MKSCONTROL);
   vm->Init();
   auto mks = dynamic_cast<crt::win32::MKSUTMock *>(vm->GetMKS());
   KeyboardMapping_Init();
   EXPECT_TRUE(mks);
   UnityMgrUTMock *mgr = dynamic_cast<UnityMgrUTMock *>(vm->GetUnityMgr());
   {
      // Push a few pending input events to mQueuedInputEvents
      VMOCK(&cui::UnityMgr::IsOn).Will(true);
      VMOCK(&cui::UnityMgr::IsGrabPending).Will(true);
      cui::UnityMouseEvent mouseEvent;
      mouseEvent.x = 80;
      mouseEvent.y = 80;
      mgr->cui::UnityMgr::SendMouseEvent(mouseEvent);
      mgr->cui::UnityMgr::SendKeyEvent(cui::UnityKeyEvent(0x30, false));
      cui::UnityTouchPoint touchPoint;
      touchPoint.contactId = 1;
      touchPoint.flags = 1;
      touchPoint.x = 1;
      touchPoint.y = 1;
      cui::UnityTouchEvent e;
      e.points.push_back(touchPoint);
      mgr->cui::UnityMgr::SendTouchEvent(e);

      // Test Unity is not on and we do nothing
      {
         VMOCK(&cui::UnityMgr::IsOn).Will(false);
         EXPECT_CALL(*mgr, SendTouchEventMock).Times(0);
         EXPECT_CALL(*mgr, SendMouseEventMock).Times(0);
         EXPECT_CALL(*mgr, SendKeyEventMock).Times(0);
         EXPECT_CALL(*mgr, SetFocusedWindowMock).Times(0);
         mgr->cui::UnityMgr::OnMKSGrabStateChanged();
         testing::Mock::VerifyAndClearExpectations(mgr);
      }

      // Test Unity is grabbed and all pending events sent to guest
      {
         VMOCK(&cui::UnityMgr::IsOn).Will(true);
         EXPECT_CALL(*mks, GetGrabStateMock()).WillRepeatedly([]() { return cui::MKS::GRAB; });
         EXPECT_CALL(*mgr, SetFocusedWindowMock).Times(0);
         bool touchMatched = false;
         EXPECT_CALL(*mgr, SendTouchEventMock(_))
            .WillOnce([&touchMatched](const cui::UnityTouchEvent &touchEvent) {
               touchMatched = (touchEvent.points.size() == 1) && (touchEvent.points[0].x == 1) &&
                              (touchEvent.points[0].y == 1) &&
                              (touchEvent.points[0].contactId == 1) &&
                              (touchEvent.points[0].flags == 1);
               return;
            });
         bool mouseMatched = false;
         EXPECT_CALL(*mgr, SendMouseEventMock(_))
            .WillOnce([&mouseMatched](const cui::UnityMouseEvent &mouseEvent) {
               mouseMatched = (mouseEvent.x == 80) && (mouseEvent.y == 80);
               return;
            });
         bool keyMatched = false;
         EXPECT_CALL(*mgr, SendKeyEventMock(_))
            .WillOnce([&keyMatched](const cui::UnityKeyEvent &keyEvent) {
               keyMatched = (keyEvent.key == 0x30);
               return;
            });
         mgr->cui::UnityMgr::OnMKSGrabStateChanged();
         testing::Mock::VerifyAndClearExpectations(mgr);
         testing::Mock::VerifyAndClearExpectations(mks);
         EXPECT_TRUE(touchMatched);
         EXPECT_TRUE(mouseMatched);
         EXPECT_TRUE(keyMatched);
      }

      // Test Unity is ungrabbed and but there is a unfocus request pending
      {
         VMOCK(&cui::UnityMgr::IsOn).Will(true);
         VMOCK(&cui::UnityMgr::IsSetFocusedWindowPending).Will(true);
         EXPECT_CALL(*mks, GetGrabStateMock).WillRepeatedly([]() { return cui::MKS::UNGRAB_HARD; });
         bool windowMatched = false;
         EXPECT_CALL(*mgr, SetFocusedWindowMock)
            .WillOnce([&windowMatched](const cui::UnityWindow *window, unsigned int delayMS) {
               windowMatched = (window == NULL);
               return true;
            });
         mgr->cui::UnityMgr::OnMKSGrabStateChanged();
         testing::Mock::VerifyAndClearExpectations(mgr);
         testing::Mock::VerifyAndClearExpectations(mks);
         EXPECT_TRUE(windowMatched);
      }
   }

   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::FakeSetFocusedWindow --
 *
 *    FakeSetFocusedWindow method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

static void FakeSetFocusedWindow() {};


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::TestSetFocusedWindow --
 *
 *    Test cui::UnityMgr::SetFocusedWindow method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CUIUnityMgrUnitTest, TestSetFocusedWindow)
{
   VMUTMock *vm =
      new NiceMock<VMUTMock>("target", "", crt::win32::CRT_GHI_MESSAGES_OVER_MKSCONTROL);
   vm->Init();
   auto mks = dynamic_cast<crt::win32::MKSUTMock *>(vm->GetMKS());
   KeyboardMapping_Init();
   EXPECT_TRUE(mks);
   UnityMgrUTMock *mgr = dynamic_cast<UnityMgrUTMock *>(vm->GetUnityMgr());
   auto guestOps = dynamic_cast<crt::common::test::GuestOpsMKSControlUTMock *>(vm->GetGuestOps());

   // Test unity is off
   {
      // Set mDelaySetFocusedWindowCnx to connected
      sigc::signal<void, void> m_slot;
      if (!mgr->mDelaySetFocusedWindowCnx.connected()) {
         mgr->mDelaySetFocusedWindowCnx = m_slot.connect(sigc::ptr_fun(FakeSetFocusedWindow));
      }
      ASSERT_TRUE(mgr->mDelaySetFocusedWindowCnx.connected());
      VMOCK(&cui::UnityMgr::IsOn).Will(false);
      cui::ScheduleCallback = sigc::ptr_fun(&FakedOnScheduleCallback);
      VMOCK(&FakedOnScheduleCallback).Times(0);
      EXPECT_FALSE(mgr->cui::UnityMgr::SetFocusedWindow(0, 1));
   }

   // Test delay send focused window
   {
      VMOCK(&cui::UnityMgr::IsOn).Will(true);
      cui::ScheduleCallback = sigc::ptr_fun(&FakedOnScheduleCallback);
      VMOCK(&FakedOnScheduleCallback).Times(1);
      EXPECT_FALSE(mgr->cui::UnityMgr::SetFocusedWindow(0, 1));
   }

   // Test guest can not set focused window
   {
      std::string fakedMsg = "8=0 ";
      guestOps->OnGHIUpdateNotified(GHI_GUEST_CAP_FEATURES_VIEW_REMOTE,
                                    (const uint8 *)fakedMsg.data(), fakedMsg.size());
      VMOCK(&cui::UnityMgr::IsOn).Will(true);
      VMOCK(&cui::UnityMgr::HandleGuestWindowVisibility).Times(0);
      EXPECT_CALL(*guestOps, SetFocusedWindowMock).Times(0);
      EXPECT_FALSE(mgr->cui::UnityMgr::SetFocusedWindow(0));
   }

   // Test unfocus window
   {
      std::string fakedMsg = "8=1 ";
      guestOps->OnGHIUpdateNotified(GHI_GUEST_CAP_FEATURES_VIEW_REMOTE,
                                    (const uint8 *)fakedMsg.data(), fakedMsg.size());
      VMOCK(&cui::UnityMgr::IsOn).Will(true);
      bool unfocused = false;
      EXPECT_CALL(*guestOps, SetFocusedWindowMock(_, _, _))
         .WillOnce([&unfocused](const uint32 windowId, cui::AbortSlot onAbort,
                                cui::DoneSlot onDone) { unfocused = (windowId == 0); });
      EXPECT_TRUE(mgr->cui::UnityMgr::SetFocusedWindow(0));
      testing::Mock::VerifyAndClearExpectations(guestOps);
      EXPECT_TRUE(unfocused);
   }

   // Test focus window
   crt::win32::UnityWindowUTMock *window = NULL;
   dispatch_sync(dispatch_get_main_queue(), [&mgr, &window]() {
      mgr->UpdateUnityWindowAdd((UnityWindowId)0x123, "fakedWindowPath1", "fakedExePath1",
                                "fakedEntitlementId1");
      window = dynamic_cast<crt::win32::UnityWindowUTMock *>(mgr->AllUnityWindows()[0x123]);
   });

   // Test menu window should not be focused
   {
      VMOCK(&cui::UnityMgr::IsOn).Will(true);
      VMOCK_V(mymock, &cui::UnityWindow::IsWindowMenu).Will(true);
      EXPECT_CALL(*guestOps, SetFocusedWindowMock).Times(0);
      EXPECT_FALSE(mgr->cui::UnityMgr::SetFocusedWindow((const cui::UnityWindow *)window));
      testing::Mock::VerifyAndClearExpectations(guestOps);
   }

   // Test tool windows and transparent windows should not be focused
   {
      VMOCK(&cui::UnityMgr::IsOn).Will(true);
      window->windowType == UNITY_WINDOW_TYPE_MENU;
      window->SetAttribute(UNITY_WINDOW_ATTR_TRANSPARENT, true);
      EXPECT_CALL(*guestOps, SetFocusedWindowMock).Times(0);
      EXPECT_FALSE(mgr->cui::UnityMgr::SetFocusedWindow((const cui::UnityWindow *)window));
      testing::Mock::VerifyAndClearExpectations(guestOps);
   }

   // Test normal window should be focused
   {
      VMOCK(&cui::UnityMgr::IsOn).Will(true);
      VMOCK_V(mymock1, &cui::UnityWindow::IsWindowMenu).Will(false);
      VMOCK_V(mymock2, &cui::UnityWindow::GetAttribute).Will(false);
      bool focused = false;
      EXPECT_CALL(*guestOps, SetFocusedWindowMock(_, _, _))
         .WillOnce([&focused](const uint32 windowId, cui::AbortSlot onAbort, cui::DoneSlot onDone) {
            focused = (windowId == 0x123);
         });
      EXPECT_TRUE(mgr->cui::UnityMgr::SetFocusedWindow((const cui::UnityWindow *)window));
      testing::Mock::VerifyAndClearExpectations(guestOps);
      EXPECT_TRUE(focused);
   }
   dispatch_sync(dispatch_get_main_queue(),
                 [&mgr]() { mgr->UpdateUnityWindowRemove((UnityWindowId)0x123); });
   delete vm;
}

/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::FakedOnEnterUnitySucceeded --
 *
 *      Test FakedOnEnterUnitySucceeded method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

static void
FakedOnEnterUnitySucceeded()
{}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::FakedOnEnterUnityFailed --
 *
 *      Test FakedOnEnterUnityFailed method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

static void
FakedOnEnterUnityFailed()
{}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::TestOnEnterUnityCompleted --
 *
 *    Test cui::UnityMgr::OnEnterUnityCompleted method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CUIUnityMgrUnitTest, TestOnEnterUnityCompleted)
{
   VMUTMock *vm =
      new NiceMock<VMUTMock>("target", "", crt::win32::CRT_GHI_MESSAGES_OVER_MKSCONTROL);
   vm->Init();
   auto mks = dynamic_cast<crt::win32::MKSUTMock *>(vm->GetMKS());
   EXPECT_TRUE(mks);
   auto mgr = dynamic_cast<UnityMgrUTMock *>(vm->GetUnityMgr());
   mgr->enterUnitySucceeded.connect(sigc::ptr_fun(&FakedOnEnterUnitySucceeded));
   mgr->enterUnityFailed.connect(sigc::ptr_fun(&FakedOnEnterUnityFailed));

   // Test unity state is not pending
   {
      EXPECT_CALL(*mgr, GetUnityStateMock).WillRepeatedly([]() {
         return cui::UnityMgr::UNITY_STATE_ON;
      });
      VMOCK(&FakedOnEnterUnitySucceeded).Times(0);
      VMOCK(&FakedOnEnterUnityFailed).Times(0);
      mgr->OnEnterUnityCompleted(true);
      testing::Mock::VerifyAndClearExpectations(mgr);
   }
   EXPECT_TRUE(mgr->cui::UnityMgr::GetUnityState() == cui::UnityMgr::UNITY_STATE_OFF);

   // Test enter unity successfully
   {
      EXPECT_CALL(*mgr, GetUnityStateMock).WillRepeatedly([]() {
         return cui::UnityMgr::UNITY_STATE_ON_PENDING;
      });
      VMOCK(&FakedOnEnterUnitySucceeded).Times(1);
      VMOCK(&FakedOnEnterUnityFailed).Times(0);
      mgr->OnEnterUnityCompleted(true);
      testing::Mock::VerifyAndClearExpectations(mgr);
   }
   EXPECT_TRUE(mgr->cui::UnityMgr::GetUnityState() == cui::UnityMgr::UNITY_STATE_ON);

   // Test enter unity failed
   {
      EXPECT_CALL(*mgr, GetUnityStateMock).WillRepeatedly([]() {
         return cui::UnityMgr::UNITY_STATE_ON_PENDING;
      });
      VMOCK(&FakedOnEnterUnitySucceeded).Times(0);
      VMOCK(&FakedOnEnterUnityFailed).Times(1);
      mgr->OnEnterUnityCompleted(false);
      testing::Mock::VerifyAndClearExpectations(mgr);
   }
   EXPECT_TRUE(mgr->cui::UnityMgr::GetUnityState() == cui::UnityMgr::UNITY_STATE_OFF);
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::TestUpdateUnityFinalize --
 *
 *    Test cui::UnityMgr::UpdateUnityFinalize method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CUIUnityMgrUnitTest, TestUpdateUnityFinalize)
{
   VMOCK(&crt::common::GetVerboseLogging).Will(true);
   VMUTMock *vm =
      new NiceMock<VMUTMock>("target", "", crt::win32::CRT_GHI_MESSAGES_OVER_MKSCONTROL);
   vm->Init();
   auto mks = dynamic_cast<crt::win32::MKSUTMock *>(vm->GetMKS());
   EXPECT_TRUE(mks);
   UnityMgrUTMock *mgr = dynamic_cast<UnityMgrUTMock *>(vm->GetUnityMgr());

   // unity state is not pause, we should update all pending unity update
   {
      EXPECT_CALL(*mgr, SynchronizeWindowZOrderMock).Times(1);
      EXPECT_CALL(*mgr, GetUnityStateMock).WillRepeatedly([]() {
         return cui::UnityMgr::UNITY_STATE_ON_PENDING;
      });
      EXPECT_CALL(*mgr, OnEnterUnityCompletedMock).Times(1);
      UnityWindowId id[] = {UnityWindowId(0x123)};
      mgr->cui::UnityMgr::UpdateUnityZOrder(id, 0);
      mgr->cui::UnityMgr::UpdateUnityFinalize();
      testing::Mock::VerifyAndClearExpectations(mgr);
   }

   // unity state is pause, we should ignore all unity updates
   {
      VMOCK(&cui::UnityMgr::IsOn).Will(true);
      mgr->cui::UnityMgr::Pause();
      ASSERT_TRUE(mgr->cui::UnityMgr::IsPaused());
      mgr->UpdateUnityFinalize();
      mgr->cui::UnityMgr::Off();
   }
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::TestProcessNewWindowPath --
 *
 *    Test cui::UnityMgr::ProcessNewWindowPath method.
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CUIUnityMgrUnitTest, TestProcessNewWindowPath)
{
   VMOCK(&crt::common::GetVerboseLogging).Will(true);
   VMUTMock *vm =
      new NiceMock<VMUTMock>("target", "", crt::win32::CRT_GHI_MESSAGES_OVER_MKSCONTROL);
   vm->Init();
   auto mks = dynamic_cast<crt::win32::MKSUTMock *>(vm->GetMKS());
   EXPECT_TRUE(mks);
   UnityMgrUTMock *mgr = dynamic_cast<UnityMgrUTMock *>(vm->GetUnityMgr());
   auto guestOps = dynamic_cast<crt::common::test::GuestOpsMKSControlUTMock *>(vm->GetGuestOps());

   utf::string windowPath;
   utf::string execPath = "123";

   // Test windowPath is empty
   {
      bool windowMatched = false;
      EXPECT_CALL(*guestOps, GetUnityWindowPathMock(_, _, _))
         .WillOnce(
            [&windowMatched](uint32 windowID, cui::GuestOps::GetUnityWindowPathDoneSlot onDone,
                             cui::AbortSlot onAbort) { windowMatched = (windowID == 0x123); });
      VMOCK(&cui::UnityMgr::OnGetWindowPathDone).Times(0);
      mgr->ProcessNewWindowPath(0x123, windowPath, execPath);
      EXPECT_TRUE(windowMatched);
      testing::Mock::VerifyAndClearExpectations(guestOps);
   }

   windowPath = "123";
   execPath.clear();

   // Test execPath is empty
   {
      bool windowMatched = false;
      EXPECT_CALL(*guestOps, GetUnityWindowPathMock(_, _, _))
         .WillOnce(
            [&windowMatched](uint32 windowID, cui::GuestOps::GetUnityWindowPathDoneSlot onDone,
                             cui::AbortSlot onAbort) { windowMatched = (windowID == 0x123); });
      VMOCK(&cui::UnityMgr::OnGetWindowPathDone).Times(0);
      mgr->ProcessNewWindowPath(0x123, windowPath, execPath);
      EXPECT_TRUE(windowMatched);
      testing::Mock::VerifyAndClearExpectations(guestOps);
   }

   execPath.clear();
   windowPath.clear();
   execPath = "123";
   windowPath = "456";

   // Test windowPath and execPath already has value, no need to request path
   {
      bool windowMatched = false;
      EXPECT_CALL(*guestOps, GetUnityWindowPathMock).Times(0);
      VMOCK(&cui::UnityMgr::OnGetWindowPathDone)
         .WillOnce([&windowMatched](const utf::string &windowPath, const utf::string &execPath,
                                    uint32 windowID) {
            windowMatched = (windowID == 0x123) && (execPath.compare("123", false) == 0) &&
                            (windowPath.compare("456", false) == 0);
         });
      mgr->ProcessNewWindowPath(0x123, windowPath, execPath);
      testing::Mock::VerifyAndClearExpectations(guestOps);
      EXPECT_TRUE(windowMatched);
   }
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::TestOnGetWindowPathDone --
 *
 *    Test cui::UnityMgr::OnGetWindowPathDone method.
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CUIUnityMgrUnitTest, TestOnGetWindowPathDone)
{
   VMOCK(&crt::common::GetVerboseLogging).Will(true);
   VMUTMock *vm =
      new NiceMock<VMUTMock>("target", "", crt::win32::CRT_GHI_MESSAGES_OVER_MKSCONTROL);
   vm->Init();
   auto mks = dynamic_cast<crt::win32::MKSUTMock *>(vm->GetMKS());
   EXPECT_TRUE(mks);
   UnityMgrUTMock *mgr = dynamic_cast<UnityMgrUTMock *>(vm->GetUnityMgr());
   dispatch_sync(dispatch_get_main_queue(), [&mgr]() {
      mgr->UpdateUnityWindowAdd((UnityWindowId)0x123, "fakedWindowPath1", "fakedExePath1",
                                "fakedEntitlementId1");
   });
   auto window = dynamic_cast<crt::win32::UnityWindowUTMock *>(mgr->AllUnityWindows()[0x123]);
   utf::string execPath = "123";
   utf::string windowPath = "456";

   // Test window id 0x456 is invalid, do nothing
   {
      VMOCK(&cui::GuestAppMgr::GetGuestApp).Times(0);
      VMOCK(&cui::UnityMgr::OnGetWindowPathAbort).Times(0);
      mgr->OnGetWindowPathDone(windowPath, execPath, 0x456);
   }

   windowPath.clear();
   execPath = "123";

   // Test windowPath is empty, abort
   {
      bool windowAndErrorMatched = false;
      VMOCK(&cui::GuestAppMgr::GetGuestApp).Times(0);
      VMOCK(&cui::UnityMgr::OnGetWindowPathAbort)
         .WillOnce([&windowAndErrorMatched](bool cancelled, const cui::Error &e, uint32 windowID) {
            windowAndErrorMatched = (cancelled == false) &&
                                    ("Empty window path" == std::string(e.what())) &&
                                    (windowID == 0x123);
         });
      mgr->OnGetWindowPathDone(windowPath, execPath, 0x123);
      EXPECT_TRUE(windowAndErrorMatched);
   }


   windowPath = "456";
   execPath.clear();
   /*
    * Test Request Guest App Normally, but execPath is empty
    * then requested execPath == windowPath, then call
    * OnGetGuestAppDone, test guestAppMatched matched
    */
   {
      VMOCK(&cui::GuestApp::SetGuestAppMgr).Times(1);
      cui::GuestApp::KeyIDs keyIDs = {"123", "456", "789"};
      cui::GuestApp *guestApp = mgr->GetGuestAppMgr()->GetGuestAppFactory()->AddNewGuestApp(
         keyIDs, "notepad", cui::GuestApp::IconList(), "a", false, false, false);
      bool guestAppMatched = false;
      VMOCK_V(mymock, &cui::UnityWindow::SetGuestApp)
         .WillOnce([&guestAppMatched, &keyIDs, &mgr](cui::GuestApp *guestApp) {
            guestAppMatched = (guestApp->vmUUID.Get() == mgr->guestUUID.Get()) &&
                              (guestApp->GetExecPath() == keyIDs.execPath);
         });
      bool windowMatched = false;
      VMOCK(&cui::GuestAppMgr::GetGuestApp)
         .WillOnce([&windowMatched, &window, &mgr, &guestApp](
                      const utf::string &windowPath, const GuestApp::KeyIDs &keyIDs,
                      cui::GuestAppMgr::GetGuestAppDoneSlot onDone, cui::AbortSlot onAbort) {
            windowMatched = (windowPath.compare("456") == 0) && (keyIDs.execPath == windowPath) &&
                            (keyIDs.appEntitlementID == window->appEntitlementID) &&
                            (keyIDs.vmUUID == mgr->guestUUID.Get());
            onDone(guestApp);
         });
      VMOCK(&cui::UnityMgr::OnGetWindowPathAbort).Times(0);
      mgr->OnGetWindowPathDone(windowPath, execPath, 0x123);
      EXPECT_TRUE(windowMatched);
      EXPECT_TRUE(guestAppMatched);
   }

   windowPath = "456";
   execPath = "123";
   /*
    * Test Request Guest App Normally, execPath is not empty
    * then requested execPath == execPath, then call OnGetGuestAppAbort
    */
   {
      bool windowIdMatched = false;
      VMOCK(&cui::UnityMgr::SetDefaultGuestAppForWindow)
         .WillOnce([&windowIdMatched](uint32 windowID) { windowIdMatched = (windowID == 0x123); });
      bool windowMatched = false;
      VMOCK(&cui::GuestAppMgr::GetGuestApp)
         .WillOnce([&windowMatched, &window, &mgr, &execPath, &windowPath](
                      const utf::string &windowPath, const GuestApp::KeyIDs &keyIDs,
                      cui::GuestAppMgr::GetGuestAppDoneSlot onDone, cui::AbortSlot onAbort) {
            windowMatched = (windowPath.compare(windowPath) == 0) &&
                            (keyIDs.execPath == execPath) &&
                            (keyIDs.appEntitlementID == window->appEntitlementID) &&
                            (keyIDs.vmUUID == mgr->guestUUID.Get());
            onAbort(false, cui::Error("Error"));
         });
      VMOCK(&cui::UnityMgr::OnGetWindowPathAbort).Times(0);
      mgr->OnGetWindowPathDone(windowPath, execPath, 0x123);
      EXPECT_TRUE(windowMatched);
      EXPECT_TRUE(windowIdMatched);
   }

   dispatch_sync(dispatch_get_main_queue(),
                 [&mgr]() { mgr->UpdateUnityWindowRemove((UnityWindowId)0x123); });
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::FakeOnWindowOverlayIconAdded --
 *
 *    FakeOnWindowOverlayIconAdded method.
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

static void
FakeOnWindowOverlayIconAdded()
{}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::TestDestroyWindow --
 *
 *    Test cui::UnityMgr::DestroyWindow method.
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CUIUnityMgrUnitTest, TestDestroyWindow)
{
   VMOCK(&crt::common::GetVerboseLogging).Will(true);
   VMUTMock *vm =
      new NiceMock<VMUTMock>("target", "", crt::win32::CRT_GHI_MESSAGES_OVER_MKSCONTROL);
   vm->Init();
   auto mks = dynamic_cast<crt::win32::MKSUTMock *>(vm->GetMKS());
   EXPECT_TRUE(mks);
   UnityMgrUTMock *mgr = dynamic_cast<UnityMgrUTMock *>(vm->GetUnityMgr());
   dispatch_sync(dispatch_get_main_queue(), [&mgr]() {
      mgr->UpdateUnityWindowAdd((UnityWindowId)0x123, "fakedWindowPath1", "fakedExePath1",
                                "fakedEntitlementId1");
   });
   auto window = (mgr->AllUnityWindows()[0x123]);
   ASSERT_TRUE(!mgr->mWindowConnections.empty());

   /*
    *  Test window is cleaned up
    */
   {
      auto i = mgr->mWindows.begin();
      sigc::signal<void, void> m_slot;
      mgr->mPendingOverlayIconUpdate[i->first] =
         m_slot.connect(sigc::ptr_fun(FakeOnWindowOverlayIconAdded));
      mgr->mDraggingWindows[i->first] = window;
      mgr->mWindowZOrder.clear();
      mgr->mWindowZOrder.push_back(0x123);
      mgr->mZOrderChanged = false;
      bool idMatched = false;
      VMOCK(&cui::UnityMgr::RemoveWindowFromPendingTopWindows).WillOnce([&idMatched](uint32 id) {
         idMatched = (id == 0x123);
      });
      typedef void (*Fn2)(std::vector<sigc::connection> &);
      VMOCK((Fn2)&cui::ClearConnections).Times(1);
      VMOCK(&wui::UnityWindow::Cleanup).Times(1);
      mgr->DestroyWindow(i);
      EXPECT_TRUE(mgr->mPendingOverlayIconUpdate.empty());
      EXPECT_TRUE(idMatched);
      EXPECT_TRUE(mgr->mWindowZOrder.empty());
      EXPECT_TRUE(mgr->mZOrderChanged);
      EXPECT_TRUE(mgr->mDraggingWindows.empty());
      EXPECT_TRUE(mgr->mWindowConnections.empty());
      EXPECT_TRUE(mgr->mWindows.empty());
   }

   mgr->mWindows.insert(
      std::make_pair(0x123, dynamic_cast<crt::win32::UnityWindowUTMock *>(window)));
   dispatch_sync(dispatch_get_main_queue(),
                 [&mgr]() { mgr->UpdateUnityWindowRemove((UnityWindowId)0x123); });
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::TestOnGetGuestAppDone --
 *
 *    Test cui::UnityMgr::OnGetGuestAppDone method.
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CUIUnityMgrUnitTest, TestOnGetGuestAppDone)
{
   VMOCK(&crt::common::GetVerboseLogging).Will(true);
   VMUTMock *vm =
      new NiceMock<VMUTMock>("target", "", crt::win32::CRT_GHI_MESSAGES_OVER_MKSCONTROL);
   vm->Init();
   auto mks = dynamic_cast<crt::win32::MKSUTMock *>(vm->GetMKS());
   EXPECT_TRUE(mks);
   UnityMgrUTMock *mgr = dynamic_cast<UnityMgrUTMock *>(vm->GetUnityMgr());
   dispatch_sync(dispatch_get_main_queue(), [&mgr]() {
      mgr->UpdateUnityWindowAdd((UnityWindowId)0x123, "fakedWindowPath1", "fakedExePath1",
                                "fakedEntitlementId1");
   });
   auto window = dynamic_cast<crt::win32::UnityWindowUTMock *>(mgr->AllUnityWindows()[0x123]);

   {
      VMOCK(&cui::GuestApp::SetGuestAppMgr).Times(1);
      cui::GuestApp::KeyIDs keyIDs = {"123", "456", "789"};
      cui::GuestApp *guestApp = mgr->GetGuestAppMgr()->GetGuestAppFactory()->AddNewGuestApp(
         keyIDs, "notepad", cui::GuestApp::IconList(), "a", false, false, false);
      bool guestAppMatched = false;
      VMOCK_V(mymock, &cui::UnityWindow::SetGuestApp)
         .WillOnce([&guestAppMatched, &keyIDs, &mgr](cui::GuestApp *guestApp) {
            guestAppMatched = (guestApp->vmUUID.Get() == mgr->guestUUID.Get()) &&
                              (guestApp->GetExecPath() == keyIDs.execPath);
         });
      mgr->OnGetGuestAppDone(guestApp, 0x123);
      EXPECT_TRUE(guestAppMatched);
   }

   dispatch_sync(dispatch_get_main_queue(),
                 [&mgr]() { mgr->UpdateUnityWindowRemove((UnityWindowId)0x123); });
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::TestGetEnterUnityFailedMessage --
 *
 *    Test cui::UnityMgr::GetEnterUnityFailedMessage method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CUIUnityMgrUnitTest, TestGetEnterUnityFailedMessage)
{
   VMOCK(&crt::common::GetVerboseLogging).Will(true);
   VMUTMock *vm =
      new NiceMock<VMUTMock>("target", "", crt::win32::CRT_GHI_MESSAGES_OVER_MKSCONTROL);
   vm->Init();
   auto mks = dynamic_cast<crt::win32::MKSUTMock *>(vm->GetMKS());
   EXPECT_TRUE(mks);
   UnityMgrUTMock *mgr = dynamic_cast<UnityMgrUTMock *>(vm->GetUnityMgr());
   utf::string message;

   // Test unity failed with a reason
   {
      EXPECT_CALL(*mgr, GetUnityFailedReasonsMock).WillRepeatedly([]() {
         std::vector<utf::string> strings;
         utf::string str = "123";
         strings.push_back(str);
         return strings;
      });
      utf::string error = mgr->cui::UnityMgr::GetEnterUnityFailedMessage();
      utf::string format = GetLocalString(
         MSGID(cui.unity.failed.cantDoUnityReasons) "The virtual machine cannot enter Unity mode "
                                                    "because:\n\n%s\n");
      std::vector<utf::string> reasons;
      utf::string str = "123";
      reasons.push_back(str);
      message = Format(format.c_str(), ToString(reasons, "\n").c_str());
      EXPECT_TRUE(error.compare(message, false) == 0);
      testing::Mock::VerifyAndClearExpectations(mgr);
   }

   // Test unity failed without a reason
   {
      EXPECT_CALL(*mgr, GetUnityFailedReasonsMock).WillRepeatedly([]() {
         std::vector<utf::string> strings;
         return strings;
      });
      utf::string format = GetLocalString(MSGID(
         cui.unity.failed.genericNotSupported) "The virtual machine cannot enter Unity mode. "
                                               "Check that Unity is supported for this guest "
                                               "operating system and that the latest version of %s "
                                               "is installed.");
      message = Format(format.c_str(), VMWARE_TOOLS_SHORT_NAME);
      utf::string error = mgr->cui::UnityMgr::GetEnterUnityFailedMessage();
      EXPECT_TRUE(error.compare(message, false) == 0);
      testing::Mock::VerifyAndClearExpectations(mgr);
   }

   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::TestUpdateUnityWindowPosition --
 *
 *    Test cui::UnityMgr::UpdateUnityWindowPosition method.
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CUIUnityMgrUnitTest, TestUpdateUnityWindowPosition)
{
   VMOCK(&crt::common::GetVerboseLogging).Will(true);
   VMUTMock *vm =
      new NiceMock<VMUTMock>("target", "", crt::win32::CRT_GHI_MESSAGES_OVER_MKSCONTROL);
   vm->Init();
   auto mks = dynamic_cast<crt::win32::MKSUTMock *>(vm->GetMKS());
   EXPECT_TRUE(mks);
   UnityMgrUTMock *mgr = dynamic_cast<UnityMgrUTMock *>(vm->GetUnityMgr());
   dispatch_sync(dispatch_get_main_queue(), [&mgr]() {
      mgr->UpdateUnityWindowAdd((UnityWindowId)0x123, "fakedWindowPath1", "fakedExePath1",
                                "fakedEntitlementId1");
   });
   auto window = dynamic_cast<crt::win32::UnityWindowUTMock *>(mgr->AllUnityWindows()[0x123]);
   cui::Rect guestRect = cui::Rect::FromXYWH(0, 0, 1, 1);

   /*
    * Test 0x456 is invalid window, do nothing
    */
   {
      VMOCK(&cui::UnityMgr::UpdateDragMoveWindowRegion).Times(0);
      VMOCK(&cui::UnityWindow::SetDragging).Times(0);
      mgr->UpdateUnityWindowPosition(0x456, guestRect);
   }

   /*
    *  Test 0x123 is a new window, move window but do not SetDragging
    */
   {
      VMOCK(&cui::UnityMgr::UpdateDragMoveWindowRegion).Times(1);
      VMOCK(&cui::UnityWindow::SetDragging).Times(0);
      mgr->mNewWindows.clear();
      mgr->mNewWindows.push_back(0x123);
      mgr->UpdateUnityWindowPosition(0x123, guestRect);
      CRect rc;
      window->GetWindowRect(&rc);
      EXPECT_TRUE(rc.Width() == guestRect.Width());
      EXPECT_TRUE(rc.Height() == guestRect.Height());
      EXPECT_TRUE(rc.left == guestRect.left);
      EXPECT_TRUE(rc.top == guestRect.top);
   }

   /*
    *  Test 0x123 is not a new window, but miRegion is Empty,
    *  move window but do not SetDragging
    */
   {
      VMOCK(&cui::UnityMgr::UpdateDragMoveWindowRegion).Times(1);
      VMOCK(&miRegionNotEmpty).Will(false);
      VMOCK(&cui::UnityWindow::SetDragging).Times(0);
      mgr->mNewWindows.clear();
      mgr->UpdateUnityWindowPosition(0x123, guestRect);
      CRect rc;
      window->GetWindowRect(&rc);
      EXPECT_TRUE(rc.Width() == guestRect.Width());
      EXPECT_TRUE(rc.Height() == guestRect.Height());
      EXPECT_TRUE(rc.left == guestRect.left);
      EXPECT_TRUE(rc.top == guestRect.top);
   }

   /*
    *  Test 0x123 is not a new window and miRegion is empty,
    *  but is it already dragging,
    *  move window but do not SetDragging
    */
   {
      VMOCK(&cui::UnityMgr::UpdateDragMoveWindowRegion).Times(1);
      VMOCK(&cui::UnityWindow::IsDragging).Will(true);
      VMOCK(&miRegionNotEmpty).Will(true);
      mgr->mNewWindows.clear();
      VMOCK(&cui::UnityWindow::SetDragging).Times(0);
      mgr->UpdateUnityWindowPosition(0x123, guestRect);
      CRect rc;
      window->GetWindowRect(&rc);
      EXPECT_TRUE(rc.Width() == guestRect.Width());
      EXPECT_TRUE(rc.Height() == guestRect.Height());
      EXPECT_TRUE(rc.left == guestRect.left);
      EXPECT_TRUE(rc.top == guestRect.top);
   }

   /*
    * Test If this window isn't dragging and isn't
    * new Window and miRegion isn't empty
    * test window is moved and SetDragging is called
    */
   {
      VMOCK(&cui::UnityMgr::UpdateDragMoveWindowRegion).Times(1);
      VMOCK(&cui::UnityWindow::IsDragging).Will(false);
      VMOCK(&miRegionNotEmpty).Will(true);
      VMOCK(&cui::UnityWindow::SetDragging).Times(1);
      mgr->mNewWindows.clear();
      mgr->UpdateUnityWindowPosition(0x123, guestRect);
      CRect rc;
      window->GetWindowRect(&rc);
      EXPECT_TRUE(rc.Width() == guestRect.Width());
      EXPECT_TRUE(rc.Height() == guestRect.Height());
      EXPECT_TRUE(rc.left == guestRect.left);
      EXPECT_TRUE(rc.top == guestRect.top);
   }
   dispatch_sync(dispatch_get_main_queue(),
                 [&mgr]() { mgr->UpdateUnityWindowRemove((UnityWindowId)0x123); });
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::TestUpdateUnityWindowState --
 *
 *    Test cui::UnityMgr::UpdateUnityWindowState method.
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CUIUnityMgrUnitTest, TestUpdateUnityWindowState)
{
   VMOCK(&crt::common::GetVerboseLogging).Will(true);
   VMUTMock *vm =
      new NiceMock<VMUTMock>("target", "", crt::win32::CRT_GHI_MESSAGES_OVER_MKSCONTROL);
   vm->Init();
   auto mks = dynamic_cast<crt::win32::MKSUTMock *>(vm->GetMKS());
   EXPECT_TRUE(mks);
   UnityMgrUTMock *mgr = dynamic_cast<UnityMgrUTMock *>(vm->GetUnityMgr());
   dispatch_sync(dispatch_get_main_queue(), [&mgr]() {
      mgr->UpdateUnityWindowAdd((UnityWindowId)0x123, "fakedWindowPath1", "fakedExePath1",
                                "fakedEntitlementId1");
   });
   auto window = dynamic_cast<crt::win32::UnityWindowUTMock *>(mgr->AllUnityWindows()[0x123]);
   cui::ClearConnections(window->mConnections);
   window->mSetHostWindowAttributesCnx.disconnect();

   /*
    * Test target window attribute is "minimized and in focus and topmost"
    * all attributes are updated.
    */
   uint32 state =
      UNITY_WINDOW_STATE_MINIMIZED | UNITY_WINDOW_STATE_IN_FOCUS | UNITY_WINDOW_STATE_TOPMOST;
   {
      bool windowIdMatched = false;
      window->mAttributes[UnityWindowAttribute(UNITY_WINDOW_ATTR_FOCUSED)] = false;
      window->mAttributes[UnityWindowAttribute(UNITY_WINDOW_ATTR_MINIMIZED)] = false;
      window->mAttributes[UnityWindowAttribute(UNITY_WINDOW_ATTR_ALWAYS_ABOVE)] = false;
      VMOCK(&cui::UnityMgr::RemoveWindowFromPendingTopWindows)
         .WillOnce([&windowIdMatched](uint32 id) { windowIdMatched = (id == 0x123); });
      VMOCK_V(myMock, &cui::UnityWindow::SetAttribute)
         .Times(3)
         .WillRepeatedly([&mgr, &myMock](UnityWindowAttribute attr, bool value) {
            myMock.CallRealFunc(attr, value);
         });
      mgr->UpdateUnityWindowState(0x123, state);
      EXPECT_TRUE(mgr->GetWindowById(0x123)->GetAttribute(UNITY_WINDOW_ATTR_ALWAYS_ABOVE));
      EXPECT_TRUE(mgr->GetWindowById(0x123)->GetAttribute(UNITY_WINDOW_ATTR_MINIMIZED));
      EXPECT_TRUE(mgr->GetWindowById(0x123)->GetAttribute(UNITY_WINDOW_ATTR_FOCUSED));
   }

   /*
    * Test target window attribute is not minimized and not in focus and not topmost
    */
   state = 0;
   {
      VMOCK(&cui::UnityMgr::RemoveWindowFromPendingTopWindows).Times(0);
      window->mAttributes[UnityWindowAttribute(UNITY_WINDOW_ATTR_FOCUSED)] = true;
      window->mAttributes[UnityWindowAttribute(UNITY_WINDOW_ATTR_MINIMIZED)] = true;
      window->mAttributes[UnityWindowAttribute(UNITY_WINDOW_ATTR_ALWAYS_ABOVE)] = true;
      VMOCK_V(myMock, &cui::UnityWindow::SetAttribute)
         .Times(3)
         .WillRepeatedly([&mgr, &myMock](UnityWindowAttribute attr, bool value) {
            myMock.CallRealFunc(attr, value);
         });
      mgr->UpdateUnityWindowState(0x123, state);
      EXPECT_TRUE(!mgr->GetWindowById(0x123)->GetAttribute(UNITY_WINDOW_ATTR_ALWAYS_ABOVE));
      EXPECT_TRUE(!mgr->GetWindowById(0x123)->GetAttribute(UNITY_WINDOW_ATTR_MINIMIZED));
      EXPECT_TRUE(!mgr->GetWindowById(0x123)->GetAttribute(UNITY_WINDOW_ATTR_FOCUSED));
   }

   /*
    * Test target window attribute is not minimized and not focus and not topmost
    * but the window attribute is already updated to
    * not minimized and not focus and not topmost.
    * then SetAttribute is never called
    */
   {
      VMOCK(&cui::UnityMgr::RemoveWindowFromPendingTopWindows).Times(0);
      VMOCK(&cui::UnityWindow::GetAttribute).Will(false);
      int attrState = 0;
      VMOCK(&cui::UnityWindow::SetAttribute).Times(0);
      mgr->UpdateUnityWindowState(0x123, state);
   }
   dispatch_sync(dispatch_get_main_queue(),
                 [&mgr]() { mgr->UpdateUnityWindowRemove((UnityWindowId)0x123); });
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::TestDoesWindowIntersectOthers --
 *
 *    Test cui::UnityMgr::DoesWindowIntersectOthers method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CUIUnityMgrUnitTest, TestDoesWindowIntersectOthers)
{
   VMOCK(&crt::common::GetVerboseLogging).Will(true);
   VMUTMock *vm =
      new NiceMock<VMUTMock>("target", "", crt::win32::CRT_GHI_MESSAGES_OVER_MKSCONTROL);
   vm->Init();
   auto mks = dynamic_cast<crt::win32::MKSUTMock *>(vm->GetMKS());
   EXPECT_TRUE(mks);
   UnityMgrUTMock *mgr = dynamic_cast<UnityMgrUTMock *>(vm->GetUnityMgr());

   // Test window is null
   {
      EXPECT_FALSE(mgr->cui::UnityMgr::DoesWindowIntersectOthers(NULL));
   }
   crt::win32::UnityWindowUTMock *firstWindow = NULL;
   crt::win32::UnityWindowUTMock *secondWindow = NULL;
   dispatch_sync(dispatch_get_main_queue(), [&mgr, &firstWindow, &secondWindow]() {
      mgr->UpdateUnityWindowAdd((UnityWindowId)0x123, "fakedWindowPath1", "fakedExePath1",
                                "fakedEntitlementId1");
      firstWindow = dynamic_cast<crt::win32::UnityWindowUTMock *>(mgr->AllUnityWindows()[0x123]);
      cui::Rect guestRect = cui::Rect::FromXYWH(0, 0, 800, 600);
      firstWindow->Move(guestRect);
      mgr->UpdateUnityWindowAdd((UnityWindowId)0x456, "fakedWindowPath2", "fakedExePath2",
                                "fakedEntitlementId2");
      secondWindow = dynamic_cast<crt::win32::UnityWindowUTMock *>(mgr->AllUnityWindows()[0x456]);
      guestRect = cui::Rect::FromXYWH(400, 300, 800, 600);
      secondWindow->Move(guestRect);
   });

   // Test orderedWindows is targetwindow
   {
      VMOCK(&cui::UnityMgr::GetOrderedWindows).WillRepeatedly([&firstWindow]() {
         std::vector<cui::UnityWindow *> unityWindows;
         unityWindows.push_back(firstWindow);
         return unityWindows;
      });
      EXPECT_FALSE(mgr->cui::UnityMgr::DoesWindowIntersectOthers(firstWindow));
   }

   // Test orderedWindow interact with targetwindow
   {
      VMOCK(&cui::UnityMgr::GetOrderedWindows).WillRepeatedly([&secondWindow]() {
         std::vector<cui::UnityWindow *> unityWindows;
         unityWindows.push_back(secondWindow);
         return unityWindows;
      });
      EXPECT_TRUE(mgr->cui::UnityMgr::DoesWindowIntersectOthers(firstWindow));
   }
   dispatch_sync(dispatch_get_main_queue(), [&mgr]() {
      mgr->UpdateUnityWindowRemove((UnityWindowId)0x123);
      mgr->UpdateUnityWindowRemove((UnityWindowId)0x456);
   });

   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::TestSynchronizeWindowZOrder --
 *
 *    Test cui::UnityMgr::SynchronizeWindowZOrder method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CUIUnityMgrUnitTest, TestSynchronizeWindowZOrder)
{
   VMUTMock *vm =
      new NiceMock<VMUTMock>("target", "", crt::win32::CRT_GHI_MESSAGES_OVER_MKSCONTROL);
   vm->Init();
   auto mks = dynamic_cast<crt::win32::MKSUTMock *>(vm->GetMKS());
   EXPECT_TRUE(mks);
   UnityMgrUTMock *mgr = dynamic_cast<UnityMgrUTMock *>(vm->GetUnityMgr());
   dispatch_sync(dispatch_get_main_queue(), [&mgr]() {
      mgr->UpdateUnityWindowAdd((UnityWindowId)0x123, "fakedWindowPath1", "fakedExePath1",
                                "fakedEntitlementId1");
      mgr->UpdateUnityWindowAdd((UnityWindowId)0x456, "fakedWindowPath1", "fakedExePath1",
                                "fakedEntitlementId1");
   });
   auto firstWindow = dynamic_cast<crt::win32::UnityWindowUTMock *>(mgr->AllUnityWindows()[0x123]);
   auto secondWindow = dynamic_cast<crt::win32::UnityWindowUTMock *>(mgr->AllUnityWindows()[0x456]);
   mgr->mWindowZOrder.push_back(0x123);

   /*
    * Test window 0x123 has been removed before SynchronizeWindowZOrder,
    * then no need to synchronize window z-order
    */
   {
      mgr->mWindows.erase(mgr->mWindows.find(0x123));
      EXPECT_CALL(*firstWindow, IsInFrontOfWindowMock).Times(0);
      EXPECT_CALL(*mgr, OnSynchronizeZOrderDoneMock).Times(1);
      mgr->SynchronizeWindowZOrder();
      testing::Mock::VerifyAndClearExpectations(mgr);
      testing::Mock::VerifyAndClearExpectations(firstWindow);
   }

   /*
    * Test window 0x123 has been minimized,
    * then no need to synchronize window z-order
    */
   {
      mgr->mWindows.insert(std::make_pair(0x123, dynamic_cast<cui::UnityWindow *>(firstWindow)));
      EXPECT_CALL(*firstWindow, IsInFrontOfWindowMock).Times(0);
      EXPECT_CALL(*mgr, OnSynchronizeZOrderDoneMock).Times(1);
      firstWindow->isMinimized = true;
      mgr->SynchronizeWindowZOrder();
      testing::Mock::VerifyAndClearExpectations(mgr);
      testing::Mock::VerifyAndClearExpectations(firstWindow);
   }

   /*
    * Test window 0x123 is the first and only non-minimized window,
    * then no need to synchronize window z-order
    */
   {
      EXPECT_CALL(*firstWindow, IsInFrontOfWindowMock).Times(0);
      EXPECT_CALL(*mgr, OnSynchronizeZOrderDoneMock).Times(1);
      firstWindow->isMinimized = false;
      mgr->SynchronizeWindowZOrder();
      testing::Mock::VerifyAndClearExpectations(mgr);
      testing::Mock::VerifyAndClearExpectations(firstWindow);
   }


   mgr->mWindowZOrder.push_back(0x456);
   /*
    * Test first window is already in front of second window
    * then no need to call OrderInFrontOfWindow
    */
   {
      EXPECT_CALL(*firstWindow, IsInFrontOfWindowMock)
         .WillOnce([](const cui::UnityWindow &otherWindow) { return true; });
      EXPECT_CALL(*firstWindow, OrderInFrontOfWindowMock).Times(0);
      EXPECT_CALL(*mgr, OnSynchronizeZOrderDoneMock).Times(1);
      mgr->SynchronizeWindowZOrder();
      testing::Mock::VerifyAndClearExpectations(mgr);
      testing::Mock::VerifyAndClearExpectations(firstWindow);
   }

   /*
    * Test first window is not in front of second window
    * then need to call OrderInFrontOfWindow
    */
   {
      bool windowMatched = false;
      EXPECT_CALL(*firstWindow, IsInFrontOfWindowMock)
         .WillOnce([](const cui::UnityWindow &otherWindow) { return false; });
      EXPECT_CALL(*firstWindow, OrderInFrontOfWindowMock)
         .WillOnce([&secondWindow, &windowMatched](const cui::UnityWindow &otherWindow) {
            windowMatched = (otherWindow.GetId() == secondWindow->GetId());
         });
      EXPECT_CALL(*mgr, OnSynchronizeZOrderDoneMock).Times(1);
      mgr->SynchronizeWindowZOrder();
      EXPECT_TRUE(windowMatched);
      testing::Mock::VerifyAndClearExpectations(mgr);
      testing::Mock::VerifyAndClearExpectations(firstWindow);
   }

   dispatch_sync(dispatch_get_main_queue(), [&mgr]() {
      mgr->UpdateUnityWindowRemove((UnityWindowId)0x123);
      mgr->UpdateUnityWindowRemove((UnityWindowId)0x456);
   });
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::TestOrderNewWindowsOnscreen --
 *
 *    Test cui::UnityMgr::OrderNewWindowsOnscreen method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CUIUnityMgrUnitTest, TestOrderNewWindowsOnscreen)
{
   VMUTMock *vm =
      new NiceMock<VMUTMock>("target", "", crt::win32::CRT_GHI_MESSAGES_OVER_MKSCONTROL);
   vm->Init();
   auto mks = dynamic_cast<crt::win32::MKSUTMock *>(vm->GetMKS());
   EXPECT_TRUE(mks);
   UnityMgrUTMock *mgr = dynamic_cast<UnityMgrUTMock *>(vm->GetUnityMgr());
   dispatch_sync(dispatch_get_main_queue(), [&mgr]() {
      mgr->UpdateUnityWindowAdd((UnityWindowId)0x123, "fakedWindowPath1", "fakedExePath1",
                                "fakedEntitlementId1");
      mgr->UpdateUnityWindowAdd((UnityWindowId)0x456, "fakedWindowPath1", "fakedExePath1",
                                "fakedEntitlementId1");
   });
   auto firstWindow = dynamic_cast<crt::win32::UnityWindowUTMock *>(mgr->AllUnityWindows()[0x123]);
   auto secondWindow = dynamic_cast<crt::win32::UnityWindowUTMock *>(mgr->AllUnityWindows()[0x456]);

   // mNewWindows is empty, do nothing
   {
      mgr->mNewWindows.clear();
      EXPECT_CALL(*firstWindow, OrderFrontMock).Times(0);
      EXPECT_CALL(*secondWindow, OrderFrontMock).Times(0);
      EXPECT_TRUE(!mgr->OrderNewWindowsOnscreen());
      testing::Mock::VerifyAndClearExpectations(firstWindow);
      testing::Mock::VerifyAndClearExpectations(secondWindow);
      EXPECT_TRUE(mgr->mNewWindows.empty());
   }

   /*
    * mNewWindows has two windows 0x123, 0x456,
    * but 0x123 is removed from mWindows,
    * do nothing for 0x123
    */
   {
      mgr->mNewWindows.clear();
      mgr->mNewWindows.push_back(0x123);
      mgr->mNewWindows.push_back(0x456);
      mgr->mWindows.erase(mgr->mWindows.find(0x123));
      EXPECT_CALL(*firstWindow, OrderFrontMock).Times(0);
      EXPECT_CALL(*secondWindow, OrderFrontMock).Times(1);
      EXPECT_TRUE(mgr->OrderNewWindowsOnscreen());
      testing::Mock::VerifyAndClearExpectations(firstWindow);
      testing::Mock::VerifyAndClearExpectations(secondWindow);
      EXPECT_TRUE(mgr->mNewWindows.empty());
   }

   /*
    *  mNewWindows has two windows 0x123, 0x456,
    *  order those in front.
    */
   {
      mgr->mNewWindows.clear();
      mgr->mNewWindows.push_back(0x123);
      mgr->mNewWindows.push_back(0x456);
      mgr->mWindows.clear();
      mgr->mWindows.insert(std::make_pair(0x123, dynamic_cast<cui::UnityWindow *>(firstWindow)));
      mgr->mWindows.insert(std::make_pair(0x456, dynamic_cast<cui::UnityWindow *>(secondWindow)));
      firstWindow->isInitialUpdateDone = false;
      secondWindow->isInitialUpdateDone = false;
      EXPECT_CALL(*firstWindow, OrderFrontMock).Times(1);
      EXPECT_CALL(*secondWindow, OrderFrontMock).Times(1);
      EXPECT_TRUE(mgr->OrderNewWindowsOnscreen());
      EXPECT_TRUE(firstWindow->isInitialUpdateDone);
      EXPECT_TRUE(secondWindow->isInitialUpdateDone);
      testing::Mock::VerifyAndClearExpectations(firstWindow);
      testing::Mock::VerifyAndClearExpectations(secondWindow);
      EXPECT_TRUE(mgr->mNewWindows.empty());
   }

   dispatch_sync(dispatch_get_main_queue(), [&mgr]() {
      mgr->UpdateUnityWindowRemove((UnityWindowId)0x123);
      mgr->UpdateUnityWindowRemove((UnityWindowId)0x456);
   });
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::TestUpdateUnityWindowAttr --
 *
 *    Test cui::UnityMgr::UpdateUnityWindowAttr method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CUIUnityMgrUnitTest, TestUpdateUnityWindowAttr)
{
   VMUTMock *vm =
      new NiceMock<VMUTMock>("target", "", crt::win32::CRT_GHI_MESSAGES_OVER_MKSCONTROL);
   vm->Init();
   auto mks = dynamic_cast<crt::win32::MKSUTMock *>(vm->GetMKS());
   EXPECT_TRUE(mks);
   UnityMgrUTMock *mgr = dynamic_cast<UnityMgrUTMock *>(vm->GetUnityMgr());
   dispatch_sync(dispatch_get_main_queue(), [&mgr]() {
      mgr->UpdateUnityWindowAdd((UnityWindowId)0x123, "fakedWindowPath1", "fakedExePath1",
                                "fakedEntitlementId1");
   });
   auto window = dynamic_cast<crt::win32::UnityWindowUTMock *>(mgr->AllUnityWindows()[0x123]);
   /*
    * Test attrbute UNITY_WINDOW_ATTR_FOCUSED is set to 1
    */
   {
      mgr->UpdateUnityWindowAttr(0x123, UNITY_WINDOW_ATTR_FOCUSED, 1);
      EXPECT_TRUE(window->GetAttribute(UNITY_WINDOW_ATTR_FOCUSED));
   }
   /*
    * Test attrbute UNITY_WINDOW_ATTR_FOCUSED is set to 0
    */
   {
      mgr->UpdateUnityWindowAttr(0x123, UNITY_WINDOW_ATTR_FOCUSED, 0);
      EXPECT_TRUE(!window->GetAttribute(UNITY_WINDOW_ATTR_FOCUSED));
   }
   dispatch_sync(dispatch_get_main_queue(),
                 [&mgr]() { mgr->UpdateUnityWindowRemove((UnityWindowId)0x123); });
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::TestUpdateUnityWindowIcon --
 *
 *    Test cui::UnityMgr::UpdateUnityWindowIcon method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CUIUnityMgrUnitTest, TestUpdateUnityWindowIcon)
{
   VMUTMock *vm =
      new NiceMock<VMUTMock>("target", "", crt::win32::CRT_GHI_MESSAGES_OVER_MKSCONTROL);
   vm->Init();
   auto mks = dynamic_cast<crt::win32::MKSUTMock *>(vm->GetMKS());
   EXPECT_TRUE(mks);
   UnityMgrUTMock *mgr = dynamic_cast<UnityMgrUTMock *>(vm->GetUnityMgr());
   dispatch_sync(dispatch_get_main_queue(), [&mgr]() {
      mgr->UpdateUnityWindowAdd((UnityWindowId)0x123, "fakedWindowPath1", "fakedExePath1",
                                "fakedEntitlementId1");
   });
   /*
    * Test icon is set to correct type
    */
   {
      bool typeMatched = false;
      VMOCK(&cui::UnityWindow::SetIcon).WillOnce([&typeMatched](UnityIconType type) {
         typeMatched = (type == UNITY_ICON_TYPE_MAIN);
      });
      mgr->UpdateUnityWindowIcon(0x123, UNITY_ICON_TYPE_MAIN);
      EXPECT_TRUE(typeMatched);
   }
   dispatch_sync(dispatch_get_main_queue(),
                 [&mgr]() { mgr->UpdateUnityWindowRemove((UnityWindowId)0x123); });
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::TestUpdateUnityWindowDesktop --
 *
 *    Test cui::UnityMgr::UpdateUnityWindowDesktop method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CUIUnityMgrUnitTest, TestUpdateUnityWindowDesktop)
{
   VMUTMock *vm =
      new NiceMock<VMUTMock>("target", "", crt::win32::CRT_GHI_MESSAGES_OVER_MKSCONTROL);
   vm->Init();
   auto mks = dynamic_cast<crt::win32::MKSUTMock *>(vm->GetMKS());
   EXPECT_TRUE(mks);
   UnityMgrUTMock *mgr = dynamic_cast<UnityMgrUTMock *>(vm->GetUnityMgr());
   dispatch_sync(dispatch_get_main_queue(), [&mgr]() {
      mgr->UpdateUnityWindowAdd((UnityWindowId)0x123, "fakedWindowPath1", "fakedExePath1",
                                "fakedEntitlementId1");
   });
   /*
    * Test desktop window is sticky
    */
   auto window = dynamic_cast<crt::win32::UnityWindowUTMock *>(mgr->AllUnityWindows()[0x123]);
   {
      window->SetSticky(false);
      mgr->UpdateUnityWindowDesktop(0x123, -1);
      EXPECT_TRUE(window->IsSticky());
   }

   /*
    * Test desktop window is not sticky
    */
   {
      window->SetSticky(true);
      bool idMatched = false;
      VMOCK(&cui::UnityWindow::SetDesktop).WillOnce([&idMatched](UnityDesktopId desktopId) {
         idMatched = (desktopId == 0x456);
      });
      mgr->UpdateUnityWindowDesktop(0x123, 0x456);
      EXPECT_TRUE(!window->IsSticky());
      EXPECT_TRUE(idMatched);
   }
   dispatch_sync(dispatch_get_main_queue(),
                 [&mgr]() { mgr->UpdateUnityWindowRemove((UnityWindowId)0x123); });
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::TestUpdateUnityWindowTitlebarArea --
 *
 *    Test cui::UnityMgr::UpdateUnityWindowTitlebarArea method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CUIUnityMgrUnitTest, TestUpdateUnityWindowTitlebarArea)
{
   VMUTMock *vm =
      new NiceMock<VMUTMock>("target", "", crt::win32::CRT_GHI_MESSAGES_OVER_MKSCONTROL);
   vm->Init();
   auto mks = dynamic_cast<crt::win32::MKSUTMock *>(vm->GetMKS());
   EXPECT_TRUE(mks);
   UnityMgrUTMock *mgr = dynamic_cast<UnityMgrUTMock *>(vm->GetUnityMgr());
   dispatch_sync(dispatch_get_main_queue(), [&mgr]() {
      mgr->UpdateUnityWindowAdd((UnityWindowId)0x123, "fakedWindowPath1", "fakedExePath1",
                                "fakedEntitlementId1");
   });
   cui::Rect guestRect = cui::Rect::FromXYWH(0, 0, 1, 1);
   mgr->UpdateUnityWindowTitlebarArea(0x123, guestRect);
   auto window = dynamic_cast<crt::win32::UnityWindowUTMock *>(mgr->AllUnityWindows()[0x123]);
   EXPECT_TRUE(window->titlebarArea.Get() == guestRect);
   dispatch_sync(dispatch_get_main_queue(),
                 [&mgr]() { mgr->UpdateUnityWindowRemove((UnityWindowId)0x123); });
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::TestUpdateUnityWindowTitlebarButtonArea --
 *
 *    Test cui::UnityMgr::UpdateUnityWindowTitlebarButtonArea method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CUIUnityMgrUnitTest, TestUpdateUnityWindowTitlebarButtonArea)
{
   VMUTMock *vm =
      new NiceMock<VMUTMock>("target", "", crt::win32::CRT_GHI_MESSAGES_OVER_MKSCONTROL);
   vm->Init();
   auto mks = dynamic_cast<crt::win32::MKSUTMock *>(vm->GetMKS());
   EXPECT_TRUE(mks);
   UnityMgrUTMock *mgr = dynamic_cast<UnityMgrUTMock *>(vm->GetUnityMgr());
   dispatch_sync(dispatch_get_main_queue(), [&mgr]() {
      mgr->UpdateUnityWindowAdd((UnityWindowId)0x123, "fakedWindowPath1", "fakedExePath1",
                                "fakedEntitlementId1");
   });
   cui::Rect guestRect = cui::Rect::FromXYWH(0, 0, 1, 1);
   auto window = dynamic_cast<crt::win32::UnityWindowUTMock *>(mgr->AllUnityWindows()[0x123]);

   // Test titlebarbutton = UNITY_TITLEBAR_BUTTON_MAXIMIZE
   {
      bool guestRectMatched = false;
      EXPECT_CALL(*window, SetMaximizeButtonPositionMock)
         .WillOnce([&guestRectMatched, guestRect](const cui::Rect &guestButtonRect) {
            guestRectMatched = (guestButtonRect == guestRect);
         });
      mgr->UpdateUnityWindowTitlebarButtonArea(0x123, UNITY_TITLEBAR_BUTTON_MAXIMIZE, guestRect);
      testing::Mock::VerifyAndClearExpectations(mgr);
      EXPECT_TRUE(guestRectMatched);
   }

   // Test titlebarbutton != UNITY_TITLEBAR_BUTTON_MAXIMIZE
   {
      EXPECT_CALL(*window, SetMaximizeButtonPositionMock).Times(0);
      mgr->UpdateUnityWindowTitlebarButtonArea(0x123, UNITY_TITLEBAR_BUTTON_CLOSE, guestRect);
      testing::Mock::VerifyAndClearExpectations(mgr);
   }

   dispatch_sync(dispatch_get_main_queue(),
                 [&mgr]() { mgr->UpdateUnityWindowRemove((UnityWindowId)0x123); });
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::TestUpdateUnityWindowPrimaryWindow --
 *
 *    Test cui::UnityMgr::UpdateUnityWindowPrimaryWindow method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CUIUnityMgrUnitTest, TestUpdateUnityWindowPrimaryWindow)
{
   VMUTMock *vm =
      new NiceMock<VMUTMock>("target", "", crt::win32::CRT_GHI_MESSAGES_OVER_MKSCONTROL);
   vm->Init();
   auto mks = dynamic_cast<crt::win32::MKSUTMock *>(vm->GetMKS());
   EXPECT_TRUE(mks);
   UnityMgrUTMock *mgr = dynamic_cast<UnityMgrUTMock *>(vm->GetUnityMgr());
   dispatch_sync(dispatch_get_main_queue(), [&mgr]() {
      mgr->UpdateUnityWindowAdd((UnityWindowId)0x123, "fakedWindowPath1", "fakedExePath1",
                                "fakedEntitlementId1");
   });
   cui::Rect guestRect = cui::Rect::FromXYWH(0, 0, 1, 1);

   {
      mgr->UpdateUnityWindowPrimaryWindow(0x123, 0x456);
      auto window = dynamic_cast<crt::win32::UnityWindowUTMock *>(mgr->AllUnityWindows()[0x123]);
      EXPECT_TRUE(window->primaryWindowId.Get() == 0x456);
   }

   dispatch_sync(dispatch_get_main_queue(),
                 [&mgr]() { mgr->UpdateUnityWindowRemove((UnityWindowId)0x123); });
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::TestUpdateUnityWindowSecondaryWindows --
 *
 *    Test cui::UnityMgr::UpdateUnityWindowSecondaryWindows method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CUIUnityMgrUnitTest, TestUpdateUnityWindowSecondaryWindows)
{
   VMUTMock *vm =
      new NiceMock<VMUTMock>("target", "", crt::win32::CRT_GHI_MESSAGES_OVER_MKSCONTROL);
   vm->Init();
   auto mks = dynamic_cast<crt::win32::MKSUTMock *>(vm->GetMKS());
   EXPECT_TRUE(mks);
   UnityMgrUTMock *mgr = dynamic_cast<UnityMgrUTMock *>(vm->GetUnityMgr());
   dispatch_sync(dispatch_get_main_queue(), [&mgr]() {
      mgr->UpdateUnityWindowAdd((UnityWindowId)0x123, "fakedWindowPath1", "fakedExePath1",
                                "fakedEntitlementId1");
   });

   {
      UnityWindowId secondaryWindowIds[UNITY_MAX_SECONDARY_WINDOWS] = {0x456, 0x789, 0x001};
      mgr->UpdateUnityWindowSecondaryWindows(0x123, &secondaryWindowIds[0], 3);
      auto window = dynamic_cast<crt::win32::UnityWindowUTMock *>(mgr->AllUnityWindows()[0x123]);
      std::string windowIds;
      for (auto iter = window->secondaryWindowIds.Get().begin();
           iter != window->secondaryWindowIds.Get().end(); iter++) {
         windowIds += std::to_string(*iter);
      }
      std::string expectedIds =
         std::to_string(0x456) + std::to_string(0x789) + std::to_string(0x001);
      EXPECT_TRUE(windowIds == expectedIds);
   }

   dispatch_sync(dispatch_get_main_queue(),
                 [&mgr]() { mgr->UpdateUnityWindowRemove((UnityWindowId)0x123); });
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::TestUpdateUnityWindowResizingEdge --
 *
 *    Test cui::UnityMgr::UpdateUnityWindowResizingEdge method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CUIUnityMgrUnitTest, TestUpdateUnityWindowResizingEdge)
{
   VMUTMock *vm =
      new NiceMock<VMUTMock>("target", "", crt::win32::CRT_GHI_MESSAGES_OVER_MKSCONTROL);
   vm->Init();
   auto mks = dynamic_cast<crt::win32::MKSUTMock *>(vm->GetMKS());
   EXPECT_TRUE(mks);
   UnityMgrUTMock *mgr = dynamic_cast<UnityMgrUTMock *>(vm->GetUnityMgr());
   dispatch_sync(dispatch_get_main_queue(), [&mgr]() {
      mgr->UpdateUnityWindowAdd((UnityWindowId)0x123, "fakedWindowPath1", "fakedExePath1",
                                "fakedEntitlementId1");
   });

   {
      bool edgeMatched = false;
      VMOCK(&cui::UnityWindow::SetResizingEdge)
         .WillOnce([&edgeMatched](UnityResizingEdge edge, int minWidth, int minHeight, int maxWidth,
                                  int maxHeight) {
            edgeMatched = (edge == UNITY_RESIZING_EDGE_LEFT) && (minWidth == 10) &&
                          (minHeight == 11) && (maxWidth == 100) && (maxHeight == 101);
         });
      mgr->UpdateUnityWindowResizingEdge(0x123, UNITY_RESIZING_EDGE_LEFT, 10, 11, 100, 101);
      EXPECT_TRUE(edgeMatched);
   }

   dispatch_sync(dispatch_get_main_queue(),
                 [&mgr]() { mgr->UpdateUnityWindowRemove((UnityWindowId)0x123); });
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::TestWatchForAlwaysAboveWindows --
 *
 *    Test cui::UnityMgr::WatchForAlwaysAboveWindows method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CUIUnityMgrUnitTest, TestWatchForAlwaysAboveWindows)
{
   VMUTMock *vm =
      new NiceMock<VMUTMock>("target", "", crt::win32::CRT_GHI_MESSAGES_OVER_MKSCONTROL);
   vm->Init();
   auto mks = dynamic_cast<crt::win32::MKSUTMock *>(vm->GetMKS());
   EXPECT_TRUE(mks);
   UnityMgrUTMock *mgr = dynamic_cast<UnityMgrUTMock *>(vm->GetUnityMgr());
   dispatch_sync(dispatch_get_main_queue(), [&mgr]() {
      mgr->UpdateUnityWindowAdd((UnityWindowId)0x123, "fakedWindowPath1", "fakedExePath1",
                                "fakedEntitlementId1");
   });
   {
      mgr->mWatchingForAlwaysAboveWindows = false;
      gDelayedMs = 0;
      cui::ScheduleCallback = sigc::ptr_fun(&FakedOnScheduleCallback);
      mgr->WatchForAlwaysAboveWindows();
      EXPECT_TRUE(gDelayedMs == cui::UnityMgr::MKS_GRABSTATE_CHANGE_TIMEOUT);
      EXPECT_TRUE(mgr->mWatchingForAlwaysAboveWindows);
      mgr->OnWatchForAlwaysAboveWindowsTimeout();
      EXPECT_TRUE(!mgr->mWatchingForAlwaysAboveWindows);
   }

   dispatch_sync(dispatch_get_main_queue(),
                 [&mgr]() { mgr->UpdateUnityWindowRemove((UnityWindowId)0x123); });
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::TestToggleStartUI --
 *
 *    Test cui::UnityMgr::ToggleStartUI method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CUIUnityMgrUnitTest, TestToggleStartUI)
{
   VMUTMock *vm =
      new NiceMock<VMUTMock>("target", "", crt::win32::CRT_GHI_MESSAGES_OVER_MKSCONTROL);
   vm->Init();
   auto mks = dynamic_cast<crt::win32::MKSUTMock *>(vm->GetMKS());
   EXPECT_TRUE(mks);
   UnityMgrUTMock *mgr = dynamic_cast<UnityMgrUTMock *>(vm->GetUnityMgr());
   auto guestOps = dynamic_cast<crt::common::test::GuestOpsMKSControlUTMock *>(vm->GetGuestOps());
   dispatch_sync(dispatch_get_main_queue(), [&mgr]() {
      mgr->UpdateUnityWindowAdd((UnityWindowId)0x123, "fakedWindowPath1", "fakedExePath1",
                                "fakedEntitlementId1");
   });
   cui::Property<bool> fakeCanToggleStartUI;
   mgr->mGuestOps->canToggleStartUI.AddTest(fakeCanToggleStartUI);

   // Test guest can toggle start UI
   {
      fakeCanToggleStartUI = true;
      EXPECT_CALL(*guestOps, ToggleStartUIMock).Times(1);
      VMOCK(&cui::UnityMgr::SendKeyPress).Times(0);
      mgr->ToggleStartUI();
      testing::Mock::VerifyAndClearExpectations(guestOps);
   }

   // Test guest can not toggle start UI
   {
      fakeCanToggleStartUI = false;
      bool keyMatched = false;
      EXPECT_CALL(*guestOps, ToggleStartUIMock).Times(0);
      VMOCK(&cui::UnityMgr::SendKeyPress).WillOnce([&keyMatched](VScancode vscan) {
         keyMatched = (vscan == VSCAN_LGUI);
      });
      mgr->ToggleStartUI();
      testing::Mock::VerifyAndClearExpectations(guestOps);
      EXPECT_TRUE(keyMatched);
   }
   dispatch_sync(dispatch_get_main_queue(),
                 [&mgr]() { mgr->UpdateUnityWindowRemove((UnityWindowId)0x123); });
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::TestHandleGuestWindowVisibility --
 *
 *    Test cui::UnityMgr::HandleGuestWindowVisibility method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CUIUnityMgrUnitTest, TestHandleGuestWindowVisibility)
{
   VMUTMock *vm =
      new NiceMock<VMUTMock>("target", "", crt::win32::CRT_GHI_MESSAGES_OVER_MKSCONTROL);
   vm->Init();
   auto mks = dynamic_cast<crt::win32::MKSUTMock *>(vm->GetMKS());
   EXPECT_TRUE(mks);
   UnityMgrUTMock *mgr = dynamic_cast<UnityMgrUTMock *>(vm->GetUnityMgr());
   dispatch_sync(dispatch_get_main_queue(), [&mgr]() {
      mgr->UpdateUnityWindowAdd((UnityWindowId)0x123, "fakedWindowPath1", "fakedExePath1",
                                "fakedEntitlementId1");
      mgr->UpdateUnityWindowAdd((UnityWindowId)0x456, "fakedWindowPath2", "fakedExePath2",
                                "fakedEntitlementId2");
   });

   /*
    * Test hideMetroWhenUnfocused_rw = false,
    * do nothing
    */
   {
      mgr->hideMetroWhenUnfocused_rw = false;
      VMOCK(&GuestOS_GetOSIDByName).Times(0);
      VMOCK(&GuestOS_IsWinEightClient).Times(0);
      VMOCK(&GuestOS_IsWinTenClient).Times(0);
      VMOCK(&cui::UnityMgr::GetOrderedWindows).Times(0);
      mgr->HandleGuestWindowVisibility(NULL);
   }

   /*
    * Test os is not win8 and not win10
    * do nothing
    */
   {
      mgr->hideMetroWhenUnfocused_rw = true;
      VMOCK(&GuestOS_IsWinEightClient).Will(false);
      VMOCK(&GuestOS_IsWinEightServer).Will(false);
      VMOCK(&GuestOS_IsWinTenClient).Will(false);
      VMOCK(&GuestOS_IsWinTenServer).Will(false);
      VMOCK(&cui::UnityMgr::GetOrderedWindows).Times(0);
      mgr->HandleGuestWindowVisibility(NULL);
   }

   /*
    * Test guest os is win8,
    * test look up all the visible window in guest to find the
    * start screen or a fullscreen Metro app.
    * if the target immersive window exist, press WIN+D
    */
   auto firstwindow = (mgr->AllUnityWindows()[0x123]);
   auto secondwindow = (mgr->AllUnityWindows()[0x456]);
   firstwindow->isMinimized = false;
   secondwindow->isMinimized = false;
   firstwindow->windowType = UNITY_WINDOW_TYPE_METRO_OBSOLETE;
   secondwindow->windowType = UNITY_WINDOW_TYPE_NORMAL;
   {
      VMOCK(&GuestOS_IsWinEightClient).Will(true);
      VMOCK(&GuestOS_IsWinEightServer).Will(false);
      VMOCK(&GuestOS_IsWinTenClient).Will(false);
      VMOCK(&GuestOS_IsWinTenServer).Will(false);
      VMOCK(&cui::UnityMgr::GetOrderedWindows).WillOnce([&firstwindow, &secondwindow]() {
         std::vector<UnityWindow *> windowList;
         windowList.push_back(firstwindow);
         return windowList;
      });
      bool keyMatched = false;
      VMOCK(&cui::MKS::SendKeyEvent)
         .WillOnce([&keyMatched](MKSModifierState modState, VScancode scancode,
                                 cui::AbortSlot onAbort, cui::DoneSlot onDone) {
            keyMatched = (modState == MKS_MOD_GUI) && (scancode == VSCAN_D);
         });
      mgr->HandleGuestWindowVisibility(secondwindow);
      EXPECT_TRUE(keyMatched);
   }

   /*
    * Test guest os is win10,
    * test look up start menu.
    * if the target start menu window exist, press ESC
    */
   firstwindow->windowType = UNITY_WINDOW_TYPE_START_SCREEN;
   {
      VMOCK(&GuestOS_IsWinEightClient).Will(false);
      VMOCK(&GuestOS_IsWinEightServer).Will(false);
      VMOCK(&GuestOS_IsWinTenClient).Will(true);
      VMOCK(&GuestOS_IsWinTenServer).Will(false);
      VMOCK(&cui::UnityMgr::GetOrderedWindows).WillOnce([&firstwindow, &secondwindow]() {
         std::vector<UnityWindow *> windowList;
         windowList.push_back(firstwindow);
         windowList.push_back(secondwindow);
         return windowList;
      });
      bool keyMatched = false;
      VMOCK(&cui::MKS::SendKeyEvent)
         .WillOnce([&keyMatched](MKSModifierState modState, VScancode scancode,
                                 cui::AbortSlot onAbort, cui::DoneSlot onDone) {
            keyMatched = (modState == 0) && (scancode == VSCAN_ESCAPE);
         });
      mgr->HandleGuestWindowVisibility(NULL);
      EXPECT_TRUE(keyMatched);
   }

   /*
    *  Test windows is minimized, do not send key
    */
   firstwindow->isMinimized = true;
   secondwindow->isMinimized = true;
   {
      VMOCK(&GuestOS_IsWinEightClient).Will(false);
      VMOCK(&GuestOS_IsWinEightServer).Will(false);
      VMOCK(&GuestOS_IsWinTenClient).Will(true);
      VMOCK(&GuestOS_IsWinTenServer).Will(false);
      VMOCK(&cui::UnityMgr::GetOrderedWindows).WillOnce([&firstwindow, &secondwindow]() {
         std::vector<UnityWindow *> windowList;
         windowList.push_back(firstwindow);
         windowList.push_back(secondwindow);
         return windowList;
      });
      VMOCK(&cui::MKS::SendKeyEvent).Times(0);
      mgr->HandleGuestWindowVisibility(NULL);
   }
   dispatch_sync(dispatch_get_main_queue(), [&mgr]() {
      mgr->UpdateUnityWindowRemove((UnityWindowId)0x123);
      mgr->UpdateUnityWindowRemove((UnityWindowId)0x456);
   });
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::TestUpdateUnityBorderHitTestResult --
 *
 *    Test cui::UnityMgr::UpdateUnityBorderHitTestResult method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CUIUnityMgrUnitTest, TestUpdateUnityBorderHitTestResult)
{
   VMUTMock *vm =
      new NiceMock<VMUTMock>("target", "", crt::win32::CRT_GHI_MESSAGES_OVER_MKSCONTROL);
   vm->Init();
   auto mks = dynamic_cast<crt::win32::MKSUTMock *>(vm->GetMKS());
   EXPECT_TRUE(mks);
   UnityMgrUTMock *mgr = dynamic_cast<UnityMgrUTMock *>(vm->GetUnityMgr());
   dispatch_sync(dispatch_get_main_queue(), [&mgr]() {
      mgr->UpdateUnityWindowAdd((UnityWindowId)0x123, "fakedWindowPath1", "fakedExePath1",
                                "fakedEntitlementId1");
   });

   {
      bool hitMatched = false;
      VMOCK(&cui::UnityWindow::SetBorderHitTestResult)
         .WillOnce([&hitMatched](UnityBorderHitTestResult value) {
            hitMatched = (value == UNITY_BORDERHITTEST_RIGHT);
         });
      mgr->UpdateUnityBorderHitTestResult(0x123, UNITY_BORDERHITTEST_RIGHT);
      EXPECT_TRUE(hitMatched);
   }

   dispatch_sync(dispatch_get_main_queue(),
                 [&mgr]() { mgr->UpdateUnityWindowRemove((UnityWindowId)0x123); });
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::TestUpdateUnityResizingBorderSize --
 *
 *    Test cui::UnityMgr::UpdateUnityResizingBorderSize method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CUIUnityMgrUnitTest, TestUpdateUnityResizingBorderSize)
{
   VMUTMock *vm =
      new NiceMock<VMUTMock>("target", "", crt::win32::CRT_GHI_MESSAGES_OVER_MKSCONTROL);
   vm->Init();
   auto mks = dynamic_cast<crt::win32::MKSUTMock *>(vm->GetMKS());
   EXPECT_TRUE(mks);
   UnityMgrUTMock *mgr = dynamic_cast<UnityMgrUTMock *>(vm->GetUnityMgr());
   dispatch_sync(dispatch_get_main_queue(), [&mgr]() {
      mgr->UpdateUnityWindowAdd((UnityWindowId)0x123, "fakedWindowPath1", "fakedExePath1",
                                "fakedEntitlementId1");
   });

   {
      mgr->mResizingBorderSize.width = 0;
      mgr->mResizingBorderSize.height = 0;
      mgr->UpdateUnityResizingBorderSize(1, 2);
      EXPECT_TRUE(mgr->mResizingBorderSize.width == 1);
      EXPECT_TRUE(mgr->mResizingBorderSize.height == 2);
   }

   dispatch_sync(dispatch_get_main_queue(),
                 [&mgr]() { mgr->UpdateUnityWindowRemove((UnityWindowId)0x123); });
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::TestUnityWindowGuestAppChanged --
 *
 *    Test cui::UnityMgr::UnityWindowGuestAppChanged method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CUIUnityMgrUnitTest, TestUnityWindowGuestAppChanged)
{
   VMUTMock *vm =
      new NiceMock<VMUTMock>("target", "", crt::win32::CRT_GHI_MESSAGES_OVER_MKSCONTROL);
   vm->Init();
   auto mks = dynamic_cast<crt::win32::MKSUTMock *>(vm->GetMKS());
   EXPECT_TRUE(mks);
   UnityMgrUTMock *mgr = dynamic_cast<UnityMgrUTMock *>(vm->GetUnityMgr());
   dispatch_sync(dispatch_get_main_queue(), [&mgr]() {
      mgr->UpdateUnityWindowAdd((UnityWindowId)0x123, "fakedWindowPath1", "fakedExePath1",
                                "fakedEntitlementId1");
   });
   {
      bool idMatched = false;
      VMOCK(&cui::UnityMgr::ProcessNewWindowPath)
         .WillOnce(
            [&idMatched](uint32 id, const utf::string &windowPath, const utf::string &execPath) {
               idMatched = (id == 0x123) && (!windowPath.size()) && (!execPath.size());
            });
      mgr->UnityWindowGuestAppChanged(0x123);
      EXPECT_TRUE(idMatched);
   }
   dispatch_sync(dispatch_get_main_queue(),
                 [&mgr]() { mgr->UpdateUnityWindowRemove((UnityWindowId)0x123); });
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::TestUpdateUnityWindowRegion --
 *
 *    Test cui::UnityMgr::UpdateUnityWindowRegion method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CUIUnityMgrUnitTest, TestUpdateUnityWindowRegion)
{
   VMUTMock *vm =
      new NiceMock<VMUTMock>("target", "", crt::win32::CRT_GHI_MESSAGES_OVER_MKSCONTROL);
   vm->Init();
   auto mks = dynamic_cast<crt::win32::MKSUTMock *>(vm->GetMKS());
   EXPECT_TRUE(mks);
   UnityMgrUTMock *mgr = dynamic_cast<UnityMgrUTMock *>(vm->GetUnityMgr());
   dispatch_sync(dispatch_get_main_queue(), [&mgr]() {
      mgr->UpdateUnityWindowAdd((UnityWindowId)0x123, "fakedWindowPath1", "fakedExePath1",
                                "fakedEntitlementId1");
   });
   {
      RegionPtr region = miRegionCreate(NULL, 0);
      cui::Rect rect = cui::Rect::FromLTRB(1, 2, 3, 4);
      Region_UnionRectWithRegion(region, &rect);
      mgr->UpdateUnityWindowRegion(0x123, region);
      EXPECT_TRUE(miRegionsEqual(mgr->GetWindowById(0x123)->GetRegion(), region));
      miRegionDestroy(region);
   }
   dispatch_sync(dispatch_get_main_queue(),
                 [&mgr]() { mgr->UpdateUnityWindowRemove((UnityWindowId)0x123); });
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::TestUpdateUnityWindowTitle --
 *
 *    Test cui::UnityMgr::UpdateUnityWindowTitle method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CUIUnityMgrUnitTest, TestUpdateUnityWindowTitle)
{
   VMUTMock *vm =
      new NiceMock<VMUTMock>("target", "", crt::win32::CRT_GHI_MESSAGES_OVER_MKSCONTROL);
   vm->Init();
   auto mks = dynamic_cast<crt::win32::MKSUTMock *>(vm->GetMKS());
   EXPECT_TRUE(mks);
   UnityMgrUTMock *mgr = dynamic_cast<UnityMgrUTMock *>(vm->GetUnityMgr());
   dispatch_sync(dispatch_get_main_queue(), [&mgr]() {
      mgr->UpdateUnityWindowAdd((UnityWindowId)0x123, "fakedWindowPath1", "fakedExePath1",
                                "fakedEntitlementId1");
   });
   auto window = dynamic_cast<crt::win32::UnityWindowUTMock *>(mgr->AllUnityWindows()[0x123]);
   {
      mgr->UpdateUnityWindowTitle(0x123, "notepad");
      EXPECT_TRUE(!window->windowTitle.Get().compare("notepad"));
   }
   dispatch_sync(dispatch_get_main_queue(),
                 [&mgr]() { mgr->UpdateUnityWindowRemove((UnityWindowId)0x123); });
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::TestOrderWindowsToTop --
 *
 *    Test cui::UnityMgr::OrderWindowsToTop method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CUIUnityMgrUnitTest, TestOrderWindowsToTop)
{
   VMUTMock *vm =
      new NiceMock<VMUTMock>("target", "", crt::win32::CRT_GHI_MESSAGES_OVER_MKSCONTROL);
   vm->Init();
   auto mks = dynamic_cast<crt::win32::MKSUTMock *>(vm->GetMKS());
   EXPECT_TRUE(mks);
   UnityMgrUTMock *mgr = dynamic_cast<UnityMgrUTMock *>(vm->GetUnityMgr());
   dispatch_sync(dispatch_get_main_queue(), [&mgr]() {
      mgr->UpdateUnityWindowAdd((UnityWindowId)0x123, "fakedWindowPath1", "fakedExePath1",
                                "fakedEntitlementId1");
   });
   {
      cui::UnityMgr::UnityWindowList windows;
      windows.push_back(0x123);
      windows.push_back(0x456);
      std::string windowIds;
      VMOCK(&cui::UnityMgr::ClearPendingTopWindows).Times(1);
      VMOCK(&cui::UnityMgr::OrderWindowsToTopNoClear)
         .WillOnce([&windowIds](const cui::UnityMgr::UnityWindowList &windows) {
            for (auto i : windows) {
               windowIds += std::to_string(i);
            }
         });
      mgr->OrderWindowsToTop(windows);
      std::string expectedIds = std::to_string(0x123) + std::to_string(0x456);
      EXPECT_TRUE(windowIds == expectedIds);
   }
   dispatch_sync(dispatch_get_main_queue(),
                 [&mgr]() { mgr->UpdateUnityWindowRemove((UnityWindowId)0x123); });
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::TestOrderWindowsToTopNoClear --
 *
 *    Test cui::UnityMgr::OrderWindowsToTopNoClear method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CUIUnityMgrUnitTest, TestOrderWindowsToTopNoClear)
{
   VMUTMock *vm =
      new NiceMock<VMUTMock>("target", "", crt::win32::CRT_GHI_MESSAGES_OVER_MKSCONTROL);
   vm->Init();
   auto mks = dynamic_cast<crt::win32::MKSUTMock *>(vm->GetMKS());
   EXPECT_TRUE(mks);
   UnityMgrUTMock *mgr = dynamic_cast<UnityMgrUTMock *>(vm->GetUnityMgr());
   dispatch_sync(dispatch_get_main_queue(), [&mgr]() {
      mgr->UpdateUnityWindowAdd((UnityWindowId)0x123, "fakedWindowPath1", "fakedExePath1",
                                "fakedEntitlementId1");
   });

   // Test we can order the windows to the top immediately.
   {
      cui::UnityMgr::UnityWindowList targetWindows;
      targetWindows.push_back(0x123);
      targetWindows.push_back(0x456);
      VMOCK(&cui::UnityMgr::HandlePendingTopWindows).Will(true);
      std::string windowsMatched;
      VMOCK(&cui::UnityMgr::AddWindowToPendingTopWindows)
         .Times(2)
         .WillRepeatedly([&windowsMatched, &targetWindows](uint32 id) {
            if (std::find(targetWindows.begin(), targetWindows.end(), id) != targetWindows.end()) {
               windowsMatched += "true";
            }
         });
      VMOCK(&cui::UnityMgr::SynchronizeWindowZOrder).Times(1);
      VMOCK(&cui::GuestOpsMKSControl::OrderUnityWindowsToTop).Times(0);
      mgr->OrderWindowsToTopNoClear(targetWindows);
      EXPECT_TRUE(windowsMatched == "truetrue");
   }

   /*
    *  Test we can not order the windows to the top immediately
    *  ask the guest to do it
    */
   {
      cui::UnityMgr::UnityWindowList targetWindows;
      targetWindows.push_back(0x123);
      targetWindows.push_back(0x456);
      VMOCK(&cui::UnityMgr::HandlePendingTopWindows).Will(false);
      std::string windowsMatched1;
      VMOCK(&cui::UnityMgr::AddWindowToPendingTopWindows)
         .Times(2)
         .WillRepeatedly([&windowsMatched1, &targetWindows](uint32 id) {
            if (std::find(targetWindows.begin(), targetWindows.end(), id) != targetWindows.end()) {
               windowsMatched1 += "true";
            }
         });
      VMOCK(&cui::UnityMgr::SynchronizeWindowZOrder).Times(0);
      bool windowsMatched2 = false;
      VMOCK(&cui::GuestOpsMKSControl::OrderUnityWindowsToTop)
         .WillOnce([&windowsMatched2, &targetWindows](
                      const std::list<uint32> &windows, cui::AbortSlot onAbort,
                      cui::DoneSlot onDone) { windowsMatched2 = (windows == targetWindows); });
      mgr->OrderWindowsToTopNoClear(targetWindows);
      EXPECT_TRUE(windowsMatched1 == "truetrue");
      EXPECT_TRUE(windowsMatched2);
   }
   dispatch_sync(dispatch_get_main_queue(),
                 [&mgr]() { mgr->UpdateUnityWindowRemove((UnityWindowId)0x123); });
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::TestUpdateUnityZOrder --
 *
 *    Test cui::UnityMgr::UpdateUnityZOrder method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CUIUnityMgrUnitTest, TestUpdateUnityZOrder)
{
   VMUTMock *vm =
      new NiceMock<VMUTMock>("target", "", crt::win32::CRT_GHI_MESSAGES_OVER_MKSCONTROL);
   vm->Init();
   auto mks = dynamic_cast<crt::win32::MKSUTMock *>(vm->GetMKS());
   EXPECT_TRUE(mks);
   UnityMgrUTMock *mgr = dynamic_cast<UnityMgrUTMock *>(vm->GetUnityMgr());
   dispatch_sync(dispatch_get_main_queue(), [&mgr]() {
      mgr->UpdateUnityWindowAdd((UnityWindowId)0x123, "fakedWindowPath1", "fakedExePath1",
                                "fakedEntitlementId1");
      mgr->UpdateUnityWindowAdd((UnityWindowId)0x456, "fakedWindowPath1", "fakedExePath1",
                                "fakedEntitlementId1");
   });

   /*
    *  Test mWindowZOrder is updated, 0x123, 0x456 is valid,
    *  0x789 is invalid id.
    */
   {
      UnityWindowId ids[] = {0x123, 0x456, 0x789};
      int length = sizeof(ids) / sizeof(ids[0]);
      int i = 0;
      mgr->mZOrderChanged = false;
      mgr->UpdateUnityZOrder(ids, length);
      EXPECT_TRUE(mgr->mWindowZOrder.size());
      EXPECT_TRUE(length == mgr->mWindowZOrder.size());
      for (auto iter = mgr->mWindowZOrder.begin(); iter != mgr->mWindowZOrder.end(); iter++) {
         EXPECT_TRUE((*iter) == ids[i++]);
      }
      EXPECT_TRUE(mgr->mZOrderChanged);
   }
   dispatch_sync(dispatch_get_main_queue(), [&mgr]() {
      mgr->UpdateUnityWindowRemove((UnityWindowId)0x123);
      mgr->UpdateUnityWindowRemove((UnityWindowId)0x456);
   });
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::FakeOnUnityWindowRemoved --
 *
 *    FakeOnUnityWindowRemoved method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

static void
FakeOnUnityWindowRemoved(cui::WeakPtr<cui::UnityWindow> window)
{}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::TestDestroyAllWindows --
 *
 *    Test cui::UnityMgr::DestroyAllWindows method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CUIUnityMgrUnitTest, TestDestroyAllWindows)
{
   VMUTMock *vm =
      new NiceMock<VMUTMock>("target", "", crt::win32::CRT_GHI_MESSAGES_OVER_MKSCONTROL);
   vm->Init();
   auto mks = dynamic_cast<crt::win32::MKSUTMock *>(vm->GetMKS());
   EXPECT_TRUE(mks);
   UnityMgrUTMock *mgr = dynamic_cast<UnityMgrUTMock *>(vm->GetUnityMgr());
   dispatch_sync(dispatch_get_main_queue(), [&mgr]() {
      mgr->UpdateUnityWindowAdd((UnityWindowId)0x123, "fakedWindowPath1", "fakedExePath1",
                                "fakedEntitlementId1");
      mgr->UpdateUnityWindowAdd((UnityWindowId)0x456, "fakedWindowPath1", "fakedExePath1",
                                "fakedEntitlementId1");
   });
   auto firstWindow = dynamic_cast<crt::win32::UnityWindowUTMock *>(mgr->AllUnityWindows()[0x123]);
   auto secondWindow = dynamic_cast<crt::win32::UnityWindowUTMock *>(mgr->AllUnityWindows()[0x456]);
   {
      std::string flagMatched;
      std::string windowMatched;
      VMOCK(&cui::UnityMgr::DestroyWindow)
         .Times(2)
         .WillRepeatedly(
            [&mgr, &flagMatched, &windowMatched](cui::UnityMgr::UnityWindowMap::iterator &i1) {
               if (mgr->mStartToDestroyAllWindows) {
                  flagMatched += "flagCorrect";
               }
               windowMatched += std::to_string((i1->first));
               mgr->mWindows.erase(i1);
            });
      std::string flagMatched2;
      std::string windowMatched2;
      mgr->windowRemoved.connect(sigc::ptr_fun(FakeOnUnityWindowRemoved));
      VMOCK(&FakeOnUnityWindowRemoved)
         .Times(2)
         .WillRepeatedly(
            [&mgr, &flagMatched2, &windowMatched2](cui::WeakPtr<cui::UnityWindow> window) {
               if (mgr->mStartToDestroyAllWindows) {
                  flagMatched2 += "flagCorrect";
               }
               windowMatched2 += std::to_string(window->GetId());
            });
      VMOCK(&cui::UnityMgr::ClearPendingTopWindows).Times(1);
      mgr->DestroyAllWindows();
      EXPECT_TRUE(flagMatched == "flagCorrectflagCorrect");
      EXPECT_TRUE(windowMatched == std::to_string(0x123) + std::to_string(0x456));
      EXPECT_TRUE(flagMatched2 == "flagCorrectflagCorrect");
      EXPECT_TRUE(windowMatched2 == std::to_string(0x123) + std::to_string(0x456));
      EXPECT_TRUE(mgr->mWindows.empty());
      EXPECT_TRUE(mgr->mWindowZOrder.empty());
      EXPECT_TRUE(!mgr->mStartToDestroyAllWindows);
   }

   mgr->mWindows.insert(std::make_pair(0x123, firstWindow));
   mgr->mWindows.insert(std::make_pair(0x456, secondWindow));
   dispatch_sync(dispatch_get_main_queue(), [&mgr]() {
      mgr->UpdateUnityWindowRemove((UnityWindowId)0x123);
      mgr->UpdateUnityWindowRemove((UnityWindowId)0x456);
   });
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::TestGetOrderedWindows --
 *
 *    Test cui::UnityMgr::GetOrderedWindows method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CUIUnityMgrUnitTest, TestGetOrderedWindows)
{
   VMUTMock *vm =
      new NiceMock<VMUTMock>("target", "", crt::win32::CRT_GHI_MESSAGES_OVER_MKSCONTROL);
   vm->Init();
   auto mks = dynamic_cast<crt::win32::MKSUTMock *>(vm->GetMKS());
   EXPECT_TRUE(mks);
   UnityMgrUTMock *mgr = dynamic_cast<UnityMgrUTMock *>(vm->GetUnityMgr());
   dispatch_sync(dispatch_get_main_queue(), [&mgr]() {
      mgr->UpdateUnityWindowAdd((UnityWindowId)0x123, "fakedWindowPath1", "fakedExePath1",
                                "fakedEntitlementId1");
      mgr->UpdateUnityWindowAdd((UnityWindowId)0x456, "fakedWindowPath1", "fakedExePath1",
                                "fakedEntitlementId1");
   });
   auto firstWindow = dynamic_cast<crt::win32::UnityWindowUTMock *>(mgr->AllUnityWindows()[0x123]);
   auto secondWindow = dynamic_cast<crt::win32::UnityWindowUTMock *>(mgr->AllUnityWindows()[0x456]);
   {
      // Add windows to ZOrder
      mgr->mWindowZOrder.push_back(0x456);
      mgr->mWindowZOrder.push_back(0x123);
      std::vector<UnityWindow *> orderedWindows;
      orderedWindows = mgr->GetOrderedWindows();
      EXPECT_TRUE(orderedWindows[0] == secondWindow);
      EXPECT_TRUE(orderedWindows[1] == firstWindow);
   }
   dispatch_sync(dispatch_get_main_queue(), [&mgr]() {
      mgr->UpdateUnityWindowRemove((UnityWindowId)0x123);
      mgr->UpdateUnityWindowRemove((UnityWindowId)0x456);
   });
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::TestGetWindows --
 *
 *    Test cui::UnityMgr::GetWindows method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CUIUnityMgrUnitTest, TestGetWindows)
{
   VMUTMock *vm =
      new NiceMock<VMUTMock>("target", "", crt::win32::CRT_GHI_MESSAGES_OVER_MKSCONTROL);
   vm->Init();
   auto mks = dynamic_cast<crt::win32::MKSUTMock *>(vm->GetMKS());
   EXPECT_TRUE(mks);
   UnityMgrUTMock *mgr = dynamic_cast<UnityMgrUTMock *>(vm->GetUnityMgr());
   dispatch_sync(dispatch_get_main_queue(), [&mgr]() {
      mgr->UpdateUnityWindowAdd((UnityWindowId)0x123, "fakedWindowPath1", "fakedExePath1",
                                "fakedEntitlementId1");
      mgr->UpdateUnityWindowAdd((UnityWindowId)0x456, "fakedWindowPath1", "fakedExePath1",
                                "fakedEntitlementId1");
   });
   auto firstWindow = dynamic_cast<crt::win32::UnityWindowUTMock *>(mgr->AllUnityWindows()[0x123]);
   auto secondWindow = dynamic_cast<crt::win32::UnityWindowUTMock *>(mgr->AllUnityWindows()[0x456]);
   {
      std::vector<UnityWindow *> windows;
      windows = mgr->GetWindows();
      EXPECT_TRUE(windows[0] == firstWindow);
      EXPECT_TRUE(windows[1] == secondWindow);
   }
   dispatch_sync(dispatch_get_main_queue(), [&mgr]() {
      mgr->UpdateUnityWindowRemove((UnityWindowId)0x123);
      mgr->UpdateUnityWindowRemove((UnityWindowId)0x456);
   });
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::TestOnWindowOverlayIconAdded --
 *
 *    Test cui::UnityMgr::OnWindowOverlayIconAdded method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CUIUnityMgrUnitTest, TestOnWindowOverlayIconAdded)
{
   VMUTMock *vm =
      new NiceMock<VMUTMock>("target", "", crt::win32::CRT_GHI_MESSAGES_OVER_MKSCONTROL);
   vm->Init();
   auto mks = dynamic_cast<crt::win32::MKSUTMock *>(vm->GetMKS());
   EXPECT_TRUE(mks);
   UnityMgrUTMock *mgr = dynamic_cast<UnityMgrUTMock *>(vm->GetUnityMgr());
   utf::string expectedDescription = "nonono";
   cui::PNGData pngData;

   crt::win32::UnityWindowUTMock *firstWindow = NULL;
   dispatch_sync(dispatch_get_main_queue(), [&mgr, &firstWindow]() {
      mgr->UpdateUnityWindowAdd((UnityWindowId)0x123, "fakedWindowPath1", "fakedExePath1",
                                "fakedEntitlementId1");
      firstWindow = dynamic_cast<crt::win32::UnityWindowUTMock *>(mgr->AllUnityWindows()[0x123]);
   });

   // Test unity window 0x456 is not shown and we should delay updating the overlay icon
   {
      EXPECT_CALL(*firstWindow, OnWindowOverlayIconAddedMock).Times(0);
      cui::ScheduleCallback = sigc::ptr_fun(&FakedOnScheduleCallback);
      VMOCK(&FakedOnScheduleCallback).Times(1);
      mgr->cui::UnityMgr::OnWindowOverlayIconAdded(UnityWindowId(0x456), expectedDescription,
                                                   pngData, true);
      EXPECT_FALSE(mgr->cui::UnityMgr::mPendingOverlayIconUpdate.empty());
      testing::Mock::VerifyAndClearExpectations(firstWindow);
   }

   /*
    *  Test there is already a icon update timer we should clear the existed timer;
    *  Test the update is from the timer so we should do nothing when unity
    *  is not shown.
    */
   {
      EXPECT_CALL(*firstWindow, OnWindowOverlayIconAddedMock).Times(0);
      mgr->cui::UnityMgr::OnWindowOverlayIconAdded(UnityWindowId(0x456), expectedDescription,
                                                   pngData, false);
      EXPECT_TRUE(mgr->cui::UnityMgr::mPendingOverlayIconUpdate.empty());
      testing::Mock::VerifyAndClearExpectations(firstWindow);
   }

   // Test unity window is shown and we should updating the overlay icon
   {
      bool iconMatched = false;
      EXPECT_CALL(*firstWindow, OnWindowOverlayIconAddedMock)
         .WillOnce([&iconMatched, &expectedDescription](const utf::string &description,
                                                        const cui::PNGData &data) {
            iconMatched = (description.compare(expectedDescription) == 0) && data.empty();
         });
      mgr->cui::UnityMgr::OnWindowOverlayIconAdded(UnityWindowId(0x123), expectedDescription,
                                                   pngData, true);
      testing::Mock::VerifyAndClearExpectations(firstWindow);
      EXPECT_TRUE(iconMatched);
   }
   dispatch_sync(dispatch_get_main_queue(),
                 [&mgr]() { mgr->UpdateUnityWindowRemove((UnityWindowId)0x123); });
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::TestOnWindowOverlayIconDeleted --
 *
 *    Test cui::UnityMgr::OnWindowOverlayIconDeleted method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CUIUnityMgrUnitTest, TestOnWindowOverlayIconDeleted)
{
   VMUTMock *vm =
      new NiceMock<VMUTMock>("target", "", crt::win32::CRT_GHI_MESSAGES_OVER_MKSCONTROL);
   vm->Init();
   auto mks = dynamic_cast<crt::win32::MKSUTMock *>(vm->GetMKS());
   EXPECT_TRUE(mks);
   UnityMgrUTMock *mgr = dynamic_cast<UnityMgrUTMock *>(vm->GetUnityMgr());
   crt::win32::UnityWindowUTMock *firstWindow = NULL;
   dispatch_sync(dispatch_get_main_queue(), [&mgr, &firstWindow]() {
      mgr->UpdateUnityWindowAdd((UnityWindowId)0x123, "fakedWindowPath1", "fakedExePath1",
                                "fakedEntitlementId1");
      firstWindow = dynamic_cast<crt::win32::UnityWindowUTMock *>(mgr->AllUnityWindows()[0x123]);
   });

   // Test unity window is shown and we should delete the overlay icon
   {
      EXPECT_CALL(*firstWindow, OnWindowOverlayIconDeletedMock).Times(1);
      mgr->cui::UnityMgr::OnWindowOverlayIconDeleted((UnityWindowId)0x123);
      testing::Mock::VerifyAndClearExpectations(firstWindow);
   }

   // Test unity window is not shown and we should ignore the message
   {
      EXPECT_CALL(*firstWindow, OnWindowOverlayIconDeletedMock).Times(0);
      mgr->cui::UnityMgr::OnWindowOverlayIconDeleted((UnityWindowId)0x456);
      testing::Mock::VerifyAndClearExpectations(firstWindow);
   }
   dispatch_sync(dispatch_get_main_queue(),
                 [&mgr]() { mgr->UpdateUnityWindowRemove((UnityWindowId)0x123); });
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::TestUpdateUnityWindowCaretPosition --
 *
 *    Test cui::UnityMgr::UpdateUnityWindowCaretPosition method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CUIUnityMgrUnitTest, TestUpdateUnityWindowCaretPosition)
{
   VMUTMock *vm =
      new NiceMock<VMUTMock>("target", "", crt::win32::CRT_GHI_MESSAGES_OVER_MKSCONTROL);
   vm->Init();
   auto mks = dynamic_cast<crt::win32::MKSUTMock *>(vm->GetMKS());
   EXPECT_TRUE(mks);
   UnityMgrUTMock *mgr = dynamic_cast<UnityMgrUTMock *>(vm->GetUnityMgr());
   crt::win32::UnityWindowUTMock *firstWindow = NULL;
   dispatch_sync(dispatch_get_main_queue(), [&mgr, &firstWindow]() {
      mgr->UpdateUnityWindowAdd((UnityWindowId)0x123, "fakedWindowPath1", "fakedExePath1",
                                "fakedEntitlementId1");
      firstWindow = dynamic_cast<crt::win32::UnityWindowUTMock *>(mgr->AllUnityWindows()[0x123]);
   });

   // Test unity window existed and we should update
   {
      bool caretPositionMatched = false;
      VMOCK_V(mymock, &crt::win32::UnityWindow::SetCaretPosition)
         .WillOnce([&caretPositionMatched](int x, int y, int width, int height) {
            caretPositionMatched = (x == 1 && y == 2 && width == 3 && height == 4);
         });
      mgr->UpdateUnityWindowCaretPosition(UnityWindowId(0x123), 1, 2, 3, 4);
      EXPECT_TRUE(caretPositionMatched);
   }

   // Test unity window not existed and we should ignore
   {
      VMOCK_V(mymock, &crt::win32::UnityWindow::SetCaretPosition).Times(0);
      mgr->UpdateUnityWindowCaretPosition(UnityWindowId(0x456), 1, 2, 3, 4);
      testing::Mock::VerifyAndClearExpectations(firstWindow);
   }
   mgr->cui::UnityMgr::OnWindowDecorationsChanged();
   dispatch_sync(dispatch_get_main_queue(),
                 [&mgr]() { mgr->UpdateUnityWindowRemove((UnityWindowId)0x123); });
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::TestSetGuestResolutionScale --
 *
 *    Test cui::UnityMgr::SetGuestResolutionScale method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CUIUnityMgrUnitTest, TestSetGuestResolutionScale)
{
   VMUTMock *vm =
      new NiceMock<VMUTMock>("target", "", crt::win32::CRT_GHI_MESSAGES_OVER_MKSCONTROL);
   vm->Init();
   auto mks = dynamic_cast<crt::win32::MKSUTMock *>(vm->GetMKS());
   EXPECT_TRUE(mks);
   UnityMgrUTMock *mgr = dynamic_cast<UnityMgrUTMock *>(vm->GetUnityMgr());
   crt::win32::UnityWindowUTMock *firstWindow = NULL;
   dispatch_sync(dispatch_get_main_queue(), [&mgr]() {
      mgr->UpdateUnityWindowAdd((UnityWindowId)0x123, "fakedWindowPath1", "fakedExePath1",
                                "fakedEntitlementId1");
   });

   auto window = dynamic_cast<crt::win32::UnityWindowUTMock *>(mgr->AllUnityWindows()[0x123]);

   // Test scale factor of guest changed to 2.0
   {
      double originScale = mgr->mCoordConverter->GetGuestResolutionScale();
      VMOCK(&cui::UnityWindow::Move).Times(1);
      VMOCK(&cui::UnityWindow::SetRegion).Times(1);
      mgr->SetGuestResolutionScale(2.0);
      EXPECT_TRUE(mgr->mCoordConverter->GetGuestResolutionScale() == 2.0);
      EXPECT_TRUE(mgr->mCoordConverter->GetGuestResolutionScale() != originScale);
   }

   // Test mCoordConverter is NULL
   {
      mgr->mCoordConverter = NULL;
      VMOCK(&cui::UnityCoordinateConverting::GetGuestResolutionScale).Times(0);
      mgr->SetGuestResolutionScale(2.0);
   }


   dispatch_sync(dispatch_get_main_queue(),
                 [&mgr]() { mgr->UpdateUnityWindowRemove((UnityWindowId)0x123); });
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::TestOnWindowDraggingChanged --
 *
 *    Test cui::UnityMgr::OnWindowDraggingChanged method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CUIUnityMgrUnitTest, TestOnWindowDraggingChanged)
{
   VMUTMock *vm =
      new NiceMock<VMUTMock>("target", "", crt::win32::CRT_GHI_MESSAGES_OVER_MKSCONTROL);
   vm->Init();
   auto mks = dynamic_cast<crt::win32::MKSUTMock *>(vm->GetMKS());
   EXPECT_TRUE(mks);
   UnityMgrUTMock *mgr = dynamic_cast<UnityMgrUTMock *>(vm->GetUnityMgr());
   crt::win32::UnityWindowUTMock *firstWindow = NULL;
   crt::win32::UnityWindowUTMock *secondWindow = NULL;
   dispatch_sync(dispatch_get_main_queue(), [&mgr, &firstWindow, &secondWindow]() {
      mgr->UpdateUnityWindowAdd((UnityWindowId)0x123, "fakedWindowPath1", "fakedExePath1",
                                "fakedEntitlementId1");
      mgr->UpdateUnityWindowAdd((UnityWindowId)0x456, "fakedWindowPath2", "fakedExePath2",
                                "fakedEntitlementId2");
      firstWindow = dynamic_cast<crt::win32::UnityWindowUTMock *>(mgr->AllUnityWindows()[0x123]);
      secondWindow = dynamic_cast<crt::win32::UnityWindowUTMock *>(mgr->AllUnityWindows()[0x456]);
   });

   // Test when we clear the dragging windows, do nothing
   {
      mgr->mClearingDraggingWindows = true;
      VMOCK(&cui::UnityWindow::IsDragging).Times(0);
      mgr->OnWindowDraggingChanged(0x123);
   }

   // Test mwindows does not contain the dragging window
   {
      mgr->mClearingDraggingWindows = false;
      mgr->mWindows.erase(mgr->mWindows.find(0x123));
      VMOCK(&cui::UnityWindow::IsDragging).Times(0);
      mgr->OnWindowDraggingChanged(0x123);
   }

   // Test window is dragging, store the dragging state to mDraggingWindows
   {
      mgr->mClearingDraggingWindows = false;
      mgr->mWindows.insert(
         std::make_pair(0x123, dynamic_cast<crt::win32::UnityWindowUTMock *>(firstWindow)));
      VMOCK(&cui::UnityWindow::IsDragging).Will(true);
      mgr->mDraggingWindows.clear();
      VMOCK(&cui::UnityMgr::UpdateDragMoveWindowRegion).Times(1);
      mgr->OnWindowDraggingChanged(0x123);
      EXPECT_TRUE(mgr->mDraggingWindows[0x123] == firstWindow);
   }

   // Test window is not dragging, clear the mDraggingWindows
   {
      mgr->mClearingDraggingWindows = false;
      mgr->mWindows.insert(
         std::make_pair(0x123, dynamic_cast<crt::win32::UnityWindowUTMock *>(firstWindow)));
      mgr->mWindows.insert(
         std::make_pair(0x456, dynamic_cast<crt::win32::UnityWindowUTMock *>(secondWindow)));
      firstWindow->SetDragging(false);
      secondWindow->SetDragging(true);
      VMOCK(&cui::UnityMgr::UpdateDragMoveWindowRegion).Times(1);
      mgr->OnWindowDraggingChanged(0x123);
      EXPECT_TRUE(mgr->mDraggingWindows.empty());
      EXPECT_TRUE(!secondWindow->IsDragging());
   }


   dispatch_sync(dispatch_get_main_queue(), [&mgr]() {
      mgr->UpdateUnityWindowRemove((UnityWindowId)0x123);
      mgr->UpdateUnityWindowRemove((UnityWindowId)0x456);
   });
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::TestAddWindowToPendingTopWindows --
 *
 *    Test cui::UnityMgr::AddWindowToPendingTopWindows method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CUIUnityMgrUnitTest, TestAddWindowToPendingTopWindows)
{
   VMUTMock *vm =
      new NiceMock<VMUTMock>("target", "", crt::win32::CRT_GHI_MESSAGES_OVER_MKSCONTROL);
   vm->Init();
   auto mks = dynamic_cast<crt::win32::MKSUTMock *>(vm->GetMKS());
   EXPECT_TRUE(mks);
   UnityMgrUTMock *mgr = dynamic_cast<UnityMgrUTMock *>(vm->GetUnityMgr());
   crt::win32::UnityWindowUTMock *window = NULL;
   dispatch_sync(dispatch_get_main_queue(), [&mgr, &window]() {
      mgr->UpdateUnityWindowAdd((UnityWindowId)0x123, "fakedWindowPath1", "fakedExePath1",
                                "fakedEntitlementId1");
      window = dynamic_cast<crt::win32::UnityWindowUTMock *>(mgr->AllUnityWindows()[0x123]);
   });
   mgr->mPendingTopWindows.clear();
   mgr->AddWindowToPendingTopWindows(0x123);
   EXPECT_TRUE(mgr->mPendingTopWindows.contains(0x123));
   dispatch_sync(dispatch_get_main_queue(),
                 [&mgr]() { mgr->UpdateUnityWindowRemove((UnityWindowId)0x123); });
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::TestGetAllRunningApps --
 *
 *    Test cui::UnityMgr::GetAllRunningApps method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CUIUnityMgrUnitTest, TestGetAllRunningApps)
{
   VMUTMock *vm =
      new NiceMock<VMUTMock>("target", "", crt::win32::CRT_GHI_MESSAGES_OVER_MKSCONTROL);
   vm->Init();
   auto mks = dynamic_cast<crt::win32::MKSUTMock *>(vm->GetMKS());
   EXPECT_TRUE(mks);
   UnityMgrUTMock *mgr = dynamic_cast<UnityMgrUTMock *>(vm->GetUnityMgr());
   crt::win32::UnityWindowUTMock *window = NULL;
   dispatch_sync(dispatch_get_main_queue(), [&mgr, &window]() {
      mgr->UpdateUnityWindowAdd((UnityWindowId)0x123, "fakedWindowPath1", "fakedExePath1",
                                "fakedEntitlementId1");
      window = dynamic_cast<crt::win32::UnityWindowUTMock *>(mgr->AllUnityWindows()[0x123]);
   });
   mgr->mWindows.insert(
      std::make_pair(0x123, dynamic_cast<crt::win32::UnityWindowUTMock *>(window)));

   // Test GetGuestApps is NULL
   {
      EXPECT_TRUE(mgr->GetAllRunningApps().empty());
   }

   // Test GetGuestApps is not NULL, but the guestApp is default app
   cui::GuestApp::KeyIDs keyIDs = {"123", "456", "789"};
   cui::GuestApp *guestApp = mgr->GetGuestAppMgr()->GetGuestAppFactory()->AddNewGuestApp(
      keyIDs, "notepad", cui::GuestApp::IconList(), "a", false, false, false);
   window->SetGuestApp(guestApp);
   {
      VMOCK(&cui::GuestApp::IsDefaultGuestApp).Will(true);
      EXPECT_TRUE(mgr->GetAllRunningApps().empty());
   }

   // Test appList has been updated
   {
      VMOCK(&cui::GuestApp::IsDefaultGuestApp).Will(false);
      EXPECT_TRUE(mgr->GetAllRunningApps().size() == 1);
      EXPECT_TRUE(mgr->GetAllRunningApps().contains(guestApp));
   }
   dispatch_sync(dispatch_get_main_queue(),
                 [&mgr]() { mgr->UpdateUnityWindowRemove((UnityWindowId)0x123); });
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::TestOnHostOrderedWindowToTop --
 *
 *    Test cui::UnityMgr::OnHostOrderedWindowToTop method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CUIUnityMgrUnitTest, TestOnHostOrderedWindowToTop)
{
   VMUTMock *vm =
      new NiceMock<VMUTMock>("target", "", crt::win32::CRT_GHI_MESSAGES_OVER_MKSCONTROL);
   vm->Init();
   auto mks = dynamic_cast<crt::win32::MKSUTMock *>(vm->GetMKS());
   EXPECT_TRUE(mks);
   UnityMgrUTMock *mgr = dynamic_cast<UnityMgrUTMock *>(vm->GetUnityMgr());
   crt::win32::UnityWindowUTMock *window = NULL;
   dispatch_sync(dispatch_get_main_queue(), [&mgr, &window]() {
      mgr->UpdateUnityWindowAdd((UnityWindowId)0x123, "fakedWindowPath1", "fakedExePath1",
                                "fakedEntitlementId1");
      window = dynamic_cast<crt::win32::UnityWindowUTMock *>(mgr->AllUnityWindows()[0x123]);
      cui::UnityMgr::UnityWindowList list;
      list.push_back((UnityWindowId)0x11111111);
      list.push_back((UnityWindowId)0x22222222);
      window->secondaryWindowIds = list;
   });
   cui::UnityMgr::UnityWindowList windowList;
   windowList.push_back((UnityWindowId)0x123);
   windowList.push_back((UnityWindowId)0x11111111);
   windowList.push_back((UnityWindowId)0x22222222);
   mgr->mWindows.insert(
      std::make_pair(0x123, dynamic_cast<crt::win32::UnityWindowUTMock *>(window)));
   bool windowMatched = false;
   VMOCK(&cui::UnityMgr::OrderWindowsToTopNoClear)
      .WillOnce([&windowMatched, &windowList](const cui::UnityMgr::UnityWindowList &windows) {
         windowMatched = (windowList == windows);
      });
   mgr->OnHostOrderedWindowToTop(0x123);
   EXPECT_TRUE(windowMatched);
   dispatch_sync(dispatch_get_main_queue(),
                 [&mgr]() { mgr->UpdateUnityWindowRemove((UnityWindowId)0x123); });
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::TestGetUnityFailedReasons --
 *
 *    Test cui::UnityMgr::GetUnityFailedReasons method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CUIUnityMgrUnitTest, TestGetUnityFailedReasons)
{
   VMUTMock *vm =
      new NiceMock<VMUTMock>("target", "", crt::win32::CRT_GHI_MESSAGES_OVER_MKSCONTROL);
   vm->Init();
   auto mks = dynamic_cast<crt::win32::MKSUTMock *>(vm->GetMKS());
   EXPECT_TRUE(mks);
   UnityMgrUTMock *mgr = dynamic_cast<UnityMgrUTMock *>(vm->GetUnityMgr());
   crt::win32::UnityWindowUTMock *window = NULL;
   dispatch_sync(dispatch_get_main_queue(), [&mgr, &window]() {
      mgr->UpdateUnityWindowAdd((UnityWindowId)0x123, "fakedWindowPath1", "fakedExePath1",
                                "fakedEntitlementId1");
      window = dynamic_cast<crt::win32::UnityWindowUTMock *>(mgr->AllUnityWindows()[0x123]);
   });

   // Test unity failure reason is GUEST_IS_UNSUPPORTED_WINDOWS
   {
      cui::Capability::FailureReasonList failureReasons;
      const char *testId = "The guest is unsupported windows";
      cui::UnityGuestFailureReason::ErrorDetail detail =
         cui::UnityGuestFailureReason::GUEST_IS_UNSUPPORTED_WINDOWS;
      failureReasons.push_back(new cui::UnityGuestFailureReason(testId, detail));
      VMOCK(&cui::Capability::GetFailureReasons).Will(failureReasons);
      std::vector<utf::string> reasons = mgr->GetUnityFailedReasons();
      EXPECT_TRUE(reasons.size() == 1);
      EXPECT_TRUE(
         reasons[0].compare(
            GetLocalString(
               MSGID(cui.unity.failed
                        .unsupportedOn2000OrOlder) "- Unity in a Windows guest requires Windows XP "
                                                   "or later."),
            false) == 0);
   }

   // Test unity failure reason is GUEST_IS_LINUX
   {
      cui::Capability::FailureReasonList failureReasons;
      const char *testId = "The guest is linux";
      cui::UnityGuestFailureReason::ErrorDetail detail =
         cui::UnityGuestFailureReason::GUEST_IS_LINUX;
      failureReasons.push_back(new cui::UnityGuestFailureReason(testId, detail));
      VMOCK(&cui::Capability::GetFailureReasons).Will(failureReasons);
      std::vector<utf::string> reasons = mgr->GetUnityFailedReasons();
      EXPECT_TRUE(reasons.size() == 1);
      EXPECT_TRUE(
         reasons[0].compare(
            GetLocalString(MSGID(
               cui.unity.failed.unsupportedOnLinux) "- Unity is not supported in Linux guests."),
            false) == 0);
   }

   // Test unity failure reason is GUEST_IS_NOT_WINDOWS
   {
      cui::Capability::FailureReasonList failureReasons;
      const char *testId = "The guest is not Windows";
      cui::UnityGuestFailureReason::ErrorDetail detail =
         cui::UnityGuestFailureReason::GUEST_IS_NOT_WINDOWS;
      failureReasons.push_back(new cui::UnityGuestFailureReason(testId, detail));
      VMOCK(&cui::Capability::GetFailureReasons).Will(failureReasons);
      std::vector<utf::string> reasons = mgr->GetUnityFailedReasons();
      EXPECT_TRUE(reasons.size() == 1);
      EXPECT_TRUE(reasons[0].compare(
                     GetLocalString(MSGID(
                        cui.unity.failed
                           .unsupportedGuest) "- Unity is not supported in this guest operating "
                                              "system."),
                     false) == 0);
   }

   // Test guest support unity
   {
      utf::string format =
         LocalizedString(
            MSGID(cui.unity.failed.toolsOutdated) "- The guest operating system is running an "
                                                  "out-of-date version of %s.")
            .str();
      mgr->mExtraUnityFailedReasons.insert(Format(format.c_str(), VMWARE_TOOLS_SHORT_NAME));
      VMOCK_V(mymock, &cui::MKSScreenMgrMKSControl::HasFullscreenApp).Will(true);
      std::vector<utf::string> reasons = mgr->GetUnityFailedReasons();
      EXPECT_TRUE(reasons.size() == 3);
      EXPECT_TRUE(reasons[0].compare(Format(format.c_str(), VMWARE_TOOLS_SHORT_NAME), false) == 0);
      EXPECT_TRUE(
         reasons[1].compare(
            GetLocalString(MSGID(
               cui.unity.failed
                  .cantChangeGuestResolution) "- The guest operating system's resolution cannot "
                                              "be changed."),
            false) == 0);
      EXPECT_TRUE(reasons[2].compare(
                     GetLocalString(MSGID(
                        cui.unity.failed
                           .fullscreenApp) "- A full screen application is running in the guest."),
                     false) == 0);
   }
   dispatch_sync(dispatch_get_main_queue(),
                 [&mgr]() { mgr->UpdateUnityWindowRemove((UnityWindowId)0x123); });
   delete vm;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CUIUnityMgrUnitTest::TestOnHostMinimizedAndUnMinimizedWindow --
 *
 *    Test cui::UnityMgr::OnHostMinimizedWindow and
 *    cui::UnityMgr::OnHostUnminimizedWindow method.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CUIUnityMgrUnitTest, TestOnHostMinimizedAndUnMinimizedWindow)
{
   VMUTMock *vm =
      new NiceMock<VMUTMock>("target", "", crt::win32::CRT_GHI_MESSAGES_OVER_MKSCONTROL);
   vm->Init();
   auto mks = dynamic_cast<crt::win32::MKSUTMock *>(vm->GetMKS());
   EXPECT_TRUE(mks);
   UnityMgrUTMock *mgr = dynamic_cast<UnityMgrUTMock *>(vm->GetUnityMgr());
   auto guestOps = dynamic_cast<crt::common::test::GuestOpsMKSControlUTMock *>(vm->GetGuestOps());
   crt::win32::UnityWindowUTMock *window = NULL;
   dispatch_sync(dispatch_get_main_queue(), [&mgr, &window]() {
      mgr->UpdateUnityWindowAdd((UnityWindowId)0x123, "fakedWindowPath1", "fakedExePath1",
                                "fakedEntitlementId1");
      window = dynamic_cast<crt::win32::UnityWindowUTMock *>(mgr->AllUnityWindows()[0x123]);
   });

   mgr->mWindows.insert(
      std::make_pair(0x123, dynamic_cast<crt::win32::UnityWindowUTMock *>(window)));

   // Test OnHostMinimizedWindow
   {
      bool msgMatch = false;
      VMOCK_V(mymock, &cui::UnityWindow::SetHostWindowAttribute)
         .WillOnce([&msgMatch](ViewControlWindowAttr attr, bool value) {
            msgMatch = (attr == VIEWCONTROL_WA_MINIMIZED) && (value == true);
         });
      bool idMatched = false;
      EXPECT_CALL(*guestOps, MinimizeUnityWindowMock(_, _, _))
         .WillOnce([&idMatched](UnityWindowId windowID, cui::AbortSlot onAbort,
                                cui::DoneSlot onDone) { idMatched = (windowID == 0x123); });

      mgr->OnHostMinimizedWindow(0x123);
      testing::Mock::VerifyAndClearExpectations(guestOps);
      EXPECT_TRUE(msgMatch);
      EXPECT_TRUE(idMatched);
   }

   // Test OnHostUnminimizedWindow
   {
      bool msgMatch = false;
      VMOCK_V(mymock, &cui::UnityWindow::SetHostWindowAttribute)
         .WillOnce([&msgMatch](ViewControlWindowAttr attr, bool value) {
            msgMatch = (attr == VIEWCONTROL_WA_MINIMIZED) && (value == false);
         });
      bool idMatched = false;
      EXPECT_CALL(*guestOps, UnminimizeUnityWindowMock(_, _, _))
         .WillOnce([&idMatched](UnityWindowId windowID, cui::AbortSlot onAbort,
                                cui::DoneSlot onDone) { idMatched = (windowID == 0x123); });

      mgr->OnHostUnminimizedWindow(0x123);
      testing::Mock::VerifyAndClearExpectations(guestOps);
      EXPECT_TRUE(msgMatch);
      EXPECT_TRUE(idMatched);
   }

   dispatch_sync(dispatch_get_main_queue(),
                 [&mgr]() { mgr->UpdateUnityWindowRemove((UnityWindowId)0x123); });
   delete vm;
}

} // namespace cui