/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*!
 * @file ExistingRegKey.cpp --
 * Defines class to read, or write to existing registry key.
 */

#include "stdafx.h"

#include "common/i18n/StringConverter.h"
#include "common/windows/handle/Handle.h"
#include "common/windows/registry/ExistingRegKey.h"
#include "common/windows/registry/RegKeyBase.h"
#include "common/windows/registry/Registry.h"
#include <vector>

namespace handle = svmga::common::windows::handle;
namespace conv = svmga::common::i18n;

using namespace svmga::common::windows::registry;


/*!
 * Constructor.
 * @param parent IN: parent key, e.g. HKEY_LOCAL_MACHINE
 * @param path IN: key path relative to parent
 * @param samDesired IN: requested access permissions
 * @param hr OUT: return code
 */

ExistingRegKey::ExistingRegKey(HKEY parent, std::wstring path, REGSAM samDesired, HRESULT &hr) :
   RegKeyBase(path)
{
   hr = OpenKey(parent, path.c_str(), samDesired);
}

HRESULT
ExistingRegKey::OpenKey(HKEY parent, std::wstring path, REGSAM samDesired)
{
   HRESULT hr = S_OK;
   HKEY hSubKey = NULL;
   LONG err = ERROR_SUCCESS;

   err = RegOpenKeyEx(parent, path.c_str(), 0, samDesired, &hSubKey);
   if (err != ERROR_SUCCESS) {
      hr = HRESULT_FROM_WIN32(err);
      SYSMSG_FUNC(Error, _T("Failed Opening Key: %ws, 0x%X"), path.c_str(), hr);
   }

   _hKey = hSubKey;

   return hr;
}

/*!
 * Reads a single-string value from the key.
 * @param valName IN: the name of the value
 * @return the value of the string
 */
HRESULT
ExistingRegKey::ReadStringValue(const std::wstring &valName, std::wstring &value)
{
   HRESULT hr = S_OK;
   DWORD size = 0;
   DWORD type;

   // Get the required size for the value.
   LONG ret = ::RegQueryValueEx(_hKey, valName.c_str(), NULL, &type, NULL, &size);

   if (ret != ERROR_SUCCESS) {
      hr = HRESULT_FROM_WIN32(ret);
      SYSMSG_FUNC(Error, _T("Failed Getting Value Size: %ws, 0x%X"), valName.c_str(), hr);
      return hr;
   }

   if (type != REG_SZ && type != REG_EXPAND_SZ) {
      SYSMSG_FUNC(Error, _T("Unexpected Type: Value Name: %ws"), valName.c_str());
      return E_INVALIDARG;
   }

   std::vector<BYTE> buf(size);
   ret = ::RegQueryValueEx(_hKey, valName.c_str(), NULL, NULL, &buf[0], &size);
   if (ret != ERROR_SUCCESS) {
      hr = HRESULT_FROM_WIN32(ret);
      return hr;
   }

   value.assign((TCHAR *)&buf[0]);

   return hr;
}

/*!
 * Reads a multi-string value from the key.
 * @param valName IN: the name of the value
 * @return the value read from the registry.
 */

HRESULT
ExistingRegKey::ReadMultiStringValue(const std::wstring &valName, std::vector<std::wstring> value)
{
   HRESULT hr = S_OK;
   DWORD size = 0;
   DWORD type;
   size_t i = 0;

   // Get the required size for the value.
   LONG ret = ::RegQueryValueEx(_hKey, valName.c_str(), NULL, &type, NULL, &size);

   if (ret != ERROR_SUCCESS) {
      return HRESULT_FROM_WIN32(ret);
   }

   if (type != REG_MULTI_SZ) {
      return HRESULT_FROM_WIN32(ret);
   }

   std::vector<BYTE> buf(size);
   ret = ::RegQueryValueEx(_hKey, valName.c_str(), NULL, NULL, &buf[0], &size);

   if (ret != ERROR_SUCCESS) {
      return HRESULT_FROM_WIN32(hr);
   }

   while (i < size) {
      std::wstring strValue((TCHAR *)&buf[i]);
      value.push_back(strValue);
      i += (strValue.length() + 1) * sizeof(wchar_t);
   }

   return hr;
}

HRESULT
ExistingRegKey::WriteMultiStringValue(const std::wstring &valName, BYTE *value, const size_t len)
{
   DWORD ret = ERROR_SUCCESS;

   ret = RegSetValueExW(_hKey, valName.c_str(), 0, REG_MULTI_SZ, value, (DWORD)len);

   return HRESULT_FROM_WIN32(ret);
}

/*!
 * Reads a DWORD value from the key.
 * @param valName IN: the name of the value
 * @return the value
 */
HRESULT
ExistingRegKey::ReadDwordValue(const std::wstring &valName, DWORD &dwResult)
{
   HRESULT hr = S_OK;
   DWORD ret = 0;
   DWORD size = sizeof(ret);

   ret = ::RegQueryValueEx(_hKey, valName.c_str(), NULL, NULL, (LPBYTE)&dwResult, &size);

   if (ret != ERROR_SUCCESS) {
      hr = HRESULT_FROM_WIN32(ret);
      if (hr == HRESULT_FROM_WIN32(ERROR_FILE_NOT_FOUND)) {
         SYSMSG_FUNC(Warn, _T("%ws Does Not Exist"), valName.c_str());
      } else {
         SYSMSG_FUNC(Error, _T("Failed Querying Value: %ws, 0x%X"), valName.c_str(), hr);
      }
   }

   return hr;
}

/*!
 * Reads a DWORD value from the key.
 * @param valName IN: the name of the value
 * @param defVal IN: The default value to return it it does not exists.
 * @return the value
 */
HRESULT
ExistingRegKey::ReadDwordValue(const std::wstring &valName, DWORD defVal, DWORD &dwResult)
{
   dwResult = defVal;
   return ReadDwordValue(valName, dwResult);
}

/*!
 * Reads a DWORD value from the key.
 * @param valName IN: the name of the value
 * @return the value
 */
HRESULT
ExistingRegKey::ReadDwordValueEx(const std::wstring &valName, DWORD defVal, DWORD &dwValue)
{
   DWORD ret = ERROR_SUCCESS;
   DWORD size = sizeof(ret);

   dwValue = defVal;

   ret = ::RegQueryValueEx(_hKey, valName.c_str(), NULL, NULL, (LPBYTE)&dwValue, &size);
   return HRESULT_FROM_WIN32(ret);
}

/*!
 * Writes a single-string value to the key.
 * @param valName IN: the name of the value
 * @param type IN: the value's type, must be either REG_SZ or REG_EXPAND_SZ
 * @param value IN: the string value
 */
HRESULT
ExistingRegKey::WriteStringValue(const std::wstring &valName, DWORD type, const std::wstring &value)
{
   HRESULT hr = S_OK;

   LONG ret = ::RegSetValueEx(_hKey, valName.c_str(), 0, type, (CONST BYTE *)value.c_str(),
                              ((DWORD)value.length() + 1) * sizeof(TCHAR));
   if (ret != ERROR_SUCCESS) {
      hr = HRESULT_FROM_WIN32(ret);
      SYSMSG_FUNC(Error, _T("Failed Writing String Value: %ws, 0x%X"), valName.c_str(), hr);
   }

   return hr;
}

/*!
 * Writes a REG_DWORD value to the key.
 * @param valName IN: the name of the value
 * @param value IN: the DWORD value
 */
HRESULT
ExistingRegKey::WriteDwordValue(const std::wstring &valName, DWORD value)
{
   HRESULT hr = S_OK;

   LONG ret =
      ::RegSetValueEx(_hKey, valName.c_str(), 0, REG_DWORD, (CONST BYTE *)&value, sizeof(value));
   if (ret != ERROR_SUCCESS) {
      hr = HRESULT_FROM_WIN32(ret);
      SYSMSG_FUNC(Error, _T("Failed Writing DWORD Value: %ws, 0x%X"), valName.c_str(), hr);
   }

   return hr;
}

/*!
 * Checks to see if the value exists.
 */
bool
ExistingRegKey::ValueExists(const std::wstring &valName)
{
   LONG ret = ::RegQueryValueEx(_hKey, valName.c_str(), NULL, NULL, NULL, NULL);

   if (ret == ERROR_SUCCESS) {
      // Value exists
      return true;
   } else if (ret != ERROR_FILE_NOT_FOUND) {
      // Value not found is not error. Check other failures
      HRESULT hr = HRESULT_FROM_WIN32(ret);
      SYSMSG_FUNC(Error, _T("Failed querying value: %ws, error: 0x%X. Assuming not exists"),
                  valName.c_str(), hr);
   }

   return false;
}

/*!
 * Checks to see a sub key exists.
 */
HRESULT
ExistingRegKey::SubKeyExists(const std::wstring &subKeyName, bool &bExists)
{
   HRESULT hr = S_OK;

   ExistingRegKey subKey(*this, subKeyName, KEY_READ, hr);
   if (FAILED(hr)) {
      SYSMSG_FUNC(Error, L"Failed Opening SubKey: %ws, 0x%X", subKeyName.c_str(), hr);
      return hr;
   }

   if (subKey.operator HKEY() != NULL) {
      bExists = true;

   } else {
      bExists = false;
      hr = HRESULT_FROM_WIN32(ERROR_FILE_NOT_FOUND);
   }

   return hr;
}

HRESULT
ExistingRegKey::WaitForChange(bool &bResult, HANDLE hWaitEvent)
{
   HRESULT hr = S_OK;
   LSTATUS lr = ERROR_SUCCESS;

   //
   //  Request change notification
   //
   bResult = false;
   lr = RegNotifyChangeKeyValue(_hKey, TRUE, REG_NOTIFY_CHANGE_LAST_SET | REG_NOTIFY_CHANGE_NAME,
                                hWaitEvent, TRUE);
   if (lr != ERROR_SUCCESS) {
      hr = HRESULT_FROM_WIN32(lr);
      SYSMSG_FUNC(Error, _T("RegNotifyChangeKeyValue Failed: 0x%X"), hr);
      goto Cleanup;
   }

   bResult = true;

Cleanup:

   return hr;
}

HRESULT
ExistingRegKey::WaitForStringValue(const std::wstring &ValueName, std::wstring strExpectedValue,
                                   DWORD dwDuration, bool &bResult, HANDLE hShutdownEvent)
{
   HRESULT hr = S_OK;
   LSTATUS lr = ERROR_SUCCESS;
   DWORD dwWait = 0;
   std::wstring strValue;
   HANDLE hEvents[2] = {0};
   wchar_t szValue[MAX_PATH] = {_T('\0')};

   bResult = false;
   hEvents[0] = hShutdownEvent;

   //
   // Create handle to wait on
   //
   hEvents[1] = CreateEvent(NULL, TRUE, FALSE, NULL);
   if (hEvents[1] == NULL) {
      hr = HRESULT_FROM_WIN32(GetLastError());
      SYSMSG_FUNC(Debug, _T("Failed creating event: 0x%X"), hr);
      goto Cleanup;
   }

   while (1) {
      // Request change notification
      lr = RegNotifyChangeKeyValue(_hKey, TRUE, REG_NOTIFY_CHANGE_LAST_SET, hEvents[1], TRUE);
      if (lr != ERROR_SUCCESS) {
         hr = HRESULT_FROM_WIN32(lr);
         SYSMSG_FUNC(Debug, _T("RegNotifyChangeKeyValue Failed: 0x%X"), hr);
         goto Cleanup;
      }

      //
      // Wait for registry value change notification
      //
      dwWait = WaitForMultipleObjects(2, hEvents, FALSE, dwDuration);

      // If shutdown was called, exit
      if (dwWait == WAIT_OBJECT_0) {
         SYSMSG_FUNC(Debug, _T("Shutdown Event Set, Exiting"));
         goto Cleanup;
      }

      // Query the value after wait function returns
      hr = ReadStringValue(ValueName, strValue);
      if (strValue.compare(strExpectedValue) == 0) {
         SYSMSG_FUNC(Debug, _T("Found expected value: %ws"), strValue.c_str());
         break;
      }

      // If wait timed out, and value is still not changed, return error
      if (dwWait == WAIT_TIMEOUT) {
         hr = HRESULT_FROM_WIN32(ERROR_TIMEOUT);
         SYSMSG_FUNC(Debug,
                     _T("Timed out waiting for expected REG_SZ Value %ws. ")
                     _T("Last value was %ws. Duration: %dms."),
                     strExpectedValue.c_str(), strValue.c_str(), dwDuration);
         goto Cleanup;
      }

      // Otherwise continue to wait for desired value
      SYSMSG_FUNC(Debug, _T("Actual value: %ws Does Not Match Expected Value: %ws"),
                  strValue.c_str(), strExpectedValue.c_str());
   }

   bResult = true;

Cleanup:

   if (hEvents[1] != NULL) {
      CloseHandle(hEvents[1]);
      hEvents[1] = NULL;
   }

   return hr;
}

HRESULT
ExistingRegKey::WaitForDwordValue(const std::wstring &ValueName, DWORD dwExpectedValue,
                                  DWORD dwDuration, bool &bResult)
{
   HRESULT hr = S_OK;
   LSTATUS lr = ERROR_SUCCESS;
   DWORD dwWait = 0;
   DWORD dwValue = 0;
   HANDLE hRegEvent = NULL;

   bResult = false;

   //
   // Create handle to wait on
   //
   hRegEvent = CreateEvent(NULL, TRUE, FALSE, NULL);
   if (hRegEvent == NULL) {
      hr = HRESULT_FROM_WIN32(GetLastError());
      SYSMSG_FUNC(Debug, _T("Failed creating event: 0x%X"), hr);
      goto Cleanup;
   }

   while (1) {
      //
      // Request change notification
      //
      lr = RegNotifyChangeKeyValue(_hKey, TRUE, REG_NOTIFY_CHANGE_LAST_SET, hRegEvent, TRUE);
      if (lr != ERROR_SUCCESS) {
         hr = HRESULT_FROM_WIN32(lr);
         SYSMSG_FUNC(Debug, _T("RegNotifyChangeKeyValue Failed: 0x%X"), hr);
         break;
      } else {
         if ((dwWait = WaitForSingleObject(hRegEvent, dwDuration)) == WAIT_OBJECT_0) {
            //
            // Query the value;
            //
            hr = ReadDwordValue(ValueName, 0, dwValue);

            //
            // If the value matches or is 0, return true.
            //
            if (dwValue == 0) {
               SYSMSG_FUNC(Debug, _T("Actual value: %d. Exiting"), dwValue);
               bResult = true;
               goto Cleanup;
            } else if (dwValue == dwExpectedValue) {
               SYSMSG_FUNC(Debug, _T("Actual value: %d Matches Expected Value: %d"), dwValue,
                           dwExpectedValue);
               bResult = true;
               goto Cleanup;
            } else {
               SYSMSG_FUNC(Debug, _T("Actual value: %d Does Not Match Expected Value: %d"), dwValue,
                           dwExpectedValue);
               continue;
            }
         } else if (dwWait == WAIT_TIMEOUT) {
            hr = HRESULT_FROM_WIN32(ERROR_TIMEOUT);
            SYSMSG_FUNC(Debug,
                        _T("Timed out waiting for DWORD: Duration: %dms, ")
                        _T("Expected Value: %d, Value: %d"),
                        dwDuration, dwExpectedValue, dwValue);
            goto Cleanup;
         }
      }
   }

Cleanup:

   if (hRegEvent != NULL) {
      CloseHandle(hRegEvent);
      hRegEvent = NULL;
   }

   return hr;
}

/**
 * A simple wait, polls the desired regkey to be ready every second, without relying on
 * the regkey change notification, like the one in ExistingRegKey::WaitForStringValue(),
 * which will stop working correctly after sysprep generalization is done
 */
HRESULT
ExistingRegKey::WaitForStringValue(const std::wstring &valueName, const std::wstring &expectedValue,
                                   int totalWaitTimeInMs)
{
   std::wstring value;
   int waitTimeInMs = 1000;

   while (true) {
      HRESULT hr = ReadStringValue(valueName, value);

      if (SUCCEEDED(hr) && value.compare(expectedValue) == 0) {
         SYSMSG_FUNC(Debug, _T("WaitForStringValue: Found expected value: %ws=%ws"),
                     valueName.c_str(), expectedValue.c_str());
         return S_OK;
      }

      Sleep(waitTimeInMs);
      totalWaitTimeInMs -= waitTimeInMs;
      if (totalWaitTimeInMs <= 0) {
         break;
      }
   }

   SYSMSG_FUNC(Error, _T("Failed to wait for expected value: %ws=%ws. Final value is: %ws=%ws"),
               valueName.c_str(), expectedValue.c_str(), valueName.c_str(), value.c_str());

   return HRESULT_FROM_WIN32(ERROR_TIMEOUT);
}

/*
 * TODO: https://omnissa.atlassian.net/browse/UBI-402
 * The timeout for each single wait is set to the total timeout (dwDuration),
 * which might cause the wait to be longer than expected. Exit mechanism
 * has to be added to end the wait loop when total timeout is reached
 *
 * We need to fix this issue for ALL the wait functions in this file
 */
HRESULT
ExistingRegKey::WaitForValueExists(const std::wstring &valueName, DWORD dwDuration)
{
   HRESULT hr = S_OK;
   HANDLE hRegEvent = NULL;

   //
   // Create handle to wait on
   //
   hRegEvent = CreateEvent(NULL, TRUE, FALSE, NULL);
   if (hRegEvent == NULL) {
      hr = HRESULT_FROM_WIN32(GetLastError());
      SYSMSG_FUNC(Error, _T("Failed creating event: 0x%X"), hr);
      goto Cleanup;
   }

   while (1) {
      // Request change notification
      LSTATUS lr =
         RegNotifyChangeKeyValue(_hKey, TRUE, REG_NOTIFY_CHANGE_LAST_SET, hRegEvent, TRUE);
      if (lr != ERROR_SUCCESS) {
         hr = HRESULT_FROM_WIN32(lr);
         SYSMSG_FUNC(Error, _T("RegNotifyChangeKeyValue Failed: 0x%X"), hr);
         goto Cleanup;
      }

      DWORD dwWait = WaitForSingleObject(hRegEvent, dwDuration);

      if (dwWait == WAIT_OBJECT_0) {
         // Value exists so we can break
         if (ValueExists(valueName)) {
            SYSMSG_FUNC(Debug, _T("Value: %ws is just found. Exiting the wait loop"),
                        valueName.c_str());
            break;
         }
      } else if (dwWait == WAIT_TIMEOUT) {
         hr = HRESULT_FROM_WIN32(ERROR_TIMEOUT);
         SYSMSG_FUNC(Error, _T("Timed out waiting for value: %ws to be set"), valueName.c_str());
         goto Cleanup;
      }
   }

Cleanup:

   if (hRegEvent != NULL) {
      CloseHandle(hRegEvent);
      hRegEvent = NULL;
   }

   return hr;
}