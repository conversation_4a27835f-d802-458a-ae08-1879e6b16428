/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * cui/mks/mks.hh --
 *
 *     Manage connection to/disconnection from the MKS process.
 */

#ifndef CUI_MKS_HH
#define CUI_MKS_HH


#include <sigc++/trackable.h>
#include <sigc++/connection.h>

#include "cui/core/capability.hh"
#include "cui/core/deferredSignal.hh"
#include "cui/core/destroyNotifier.hh"
#include "cui/core/signal.hh"
#include "cui/core/weakPtr.hh"
#include "cui/mks/mksScreenWindow.hh"
#include "cui/mks/modifiers.hh"
#include "cui/mks/types.hh"
#include "mksCtrlxx/mksCtrl.hh"

#include "keyboardMapping.h"


namespace cui {


/* Forward-declare this here so we don't pull in all of cuiTypes.hh. */
class UnityTouchEvent;


class LIB_EXPORT MKS : public virtual DestroyNotifier {
public:
   /*
    * GRAB_MOTION is used only for SetGrabState.  GetGrabState will never
    * return it.
    */
   enum GrabState {
      UNGRAB_HARD,
      UNGRAB_SOFT,
      GRAB,
      GRAB_MOTION,
   };

   enum TriggerDevice { TRIGGER_DEVICE_KEYBOARD, TRIGGER_DEVICE_MOUSE };

   typedef cui::DoneSlot DoneSlot;
   typedef cui::AbortSlot AbortSlot;

   struct KeyBinding {
      KeyBinding();

      enum ModifierState {
         BOTH_UP,
         ONE_DOWN_OTHER_DONT_CARE,
         BOTH_DONT_CARE,
         LEFT_DOWN_RIGHT_UP,
         RIGHT_DOWN_LEFT_UP,
         LEFT_DOWN_RIGHT_DONT_CARE,
         RIGHT_DOWN_LEFT_DONT_CARE,
         BOTH_DOWN,
         ONE_DOWN_OTHER_UP,
         ONE_DONT_CARE_OTHER_UP,
         LEFT_DONT_CARE_RIGHT_UP,
         RIGHT_DONT_CARE_LEFT_UP
      };
      enum LEDState { LED_DONT_CARE, LED_ON, LED_OFF };

      struct {
         TriggerDevice triggerDevice;
         bool useHotkeyMods;

         ModifierState controlKeyState;
         ModifierState altKeyState;
         ModifierState shiftKeyState;
         ModifierState guiKeyState;

         LEDState numLockState;
         LEDState capsLockState;
         LEDState scrollLockState;

         bool allowDelayMods;

         HIDUsage usbHid;
      } sourceHostKeys;

      enum ActionType { SEND_KEY, NOTIFY_UI, SET_QUOTE };
      struct {
         ActionType type;

         union {
            struct {
               bool leftControlKey;
               bool rightControlKey;
               bool leftAltKey;
               bool rightAltKey;
               bool leftShiftKey;
               bool rightShiftKey;
               bool leftGUIKey;
               bool rightGUIKey;

               HIDUsage usbHid;
            } sendGuestKeys;
            int notifyUI;
         };
      } action;
   };

   struct OneToOneKeyBinding {
      OneToOneKeyBinding();

      TriggerDevice triggerDevice;
      HIDUsage fromKey;
      HIDUsage toKey;
   };

   static MKS *NewVNCMKS(const utf::string &remoteMKSPath, const StringVec &extraRemoteMKSArgs);

   MKS(mksctrl::MKSControlClientBase *viewControl, const utf::string &mksClientFilePath = "",
       const StringVec &mksClientExtraArgs = {}, MKSWindowMgr *windowMgr = NULL);
   virtual ~MKS();

   void Detach(void);

   mksctrl::MKSControlClientBase *GetMKSControlClient() const;

   void SetResolution(int width, int height, AbortSlot onAbort = AbortSlot(),
                      DoneSlot onDone = DoneSlot());

   void GetGuestTopologyLimits(uint32 &maxScreenWidth, uint32 &maxScreenHeight,
                               uint32 &maxNumDisplays, uint32 &maxScreenMemoryBytes,
                               Bool &hasBoundingBoxMemRestriction) const;

   // Preferences (for dui only)
   void SetDelayGUIKeyUp(int milliseconds);

   // Grab/ungrab
   sigc::signal<void> grabStateChanged;
   virtual GrabState GetGrabState(void) const;
   virtual void SetGrabState(GrabState grabState, AbortSlot onAbort = AbortSlot(),
                             DoneSlot onDone = DoneSlot());
   virtual void SendGrabRequest(MKSWindowID windowID, bool motionGrab,
                                AbortSlot onAbort = AbortSlot(), DoneSlot onDone = DoneSlot());
   void SetUngrabLocked(bool ungrabLocked);
   bool GetUngrabLocked(void) const;
   sigc::signal<void> ungrabLockedChanged;

   void SetParentPID(int parentPID);

   sigc::signal<void, const HotKey &> hookedKeyPressed;
   void SetHookedKeys(const std::vector<HotKey> &keyList);

   sigc::signal<void> hotKeyPrefixChanged;
   utf::string GetHotKeyPrefix(void) const;

   sigc::signal<void> presentChanged;
   virtual bool GetPresent(void) const;

   sigc::signal<void, const ViewControlNetworkQualityState *> networkQualityStateUpdated;

   /** Emits When connected and all the initial MKS states have been received. */
   sigc::signal<void> mksInitialized;
   bool IsMKSInitialized() const;

   // Deprecated signal (replaced by Capabilities):
   sigc::signal<void> capsChanged;

   virtual void SendCtrlAltDel();

   utf::string GetVNCLastError() const;
   sigc::signal<void> vncLastErrorChanged;

   sigc::signal<void, Bool, uint16, const uint32 *, uint32> vncSettingChanged;

   sigc::signal<void> mksWindowChanged;

   virtual void SetAllowDragMotionUngrab(bool allow);

   void GetAttemptDnDUngrab(int &x, int &y) const;
   sigc::signal<void> attemptDnDUngrab;

   bool GetGHDnDUngrab(void) const;
   void ClearGHDnDUngrab(void);
   sigc::signal<void> onGHDnDUngrabChanged;

   virtual void GetKeyboardLEDState(bool &numLockEnabled, bool &capsLockEnabled,
                                    bool &scrollLockEnabled) const;
   sigc::signal<void> keyboardLEDStateChanged;
   Property<bool> keyboardLEDStateInitialized;

   bool GetCursorConstrained() const;
   sigc::signal<void> cursorConstrainedChanged;

   virtual bool GetHostShortcutsAreEnabled() const;
   virtual void SetHostShortcutsAreEnabled(bool enabled);

   void SetPending(bool pending);
   bool GetPending(void) const;
   sigc::signal<void> pendingChanged;

   bool GetConnectingNotification(void) const;
   sigc::signal<void> connectingNotificationChanged;

   virtual void SetKeyBindings(const std::vector<KeyBinding> &keyBindings);
   sigc::signal<void, int> keyBindingsNotifyUIEventChanged;

   virtual void SetOneToOneKeyBindings(const std::vector<OneToOneKeyBinding> &keyBindings);

   void SendUnicodeCodePoint(unsigned int unicodeCodePoint, uint64 timestamp,
                             AbortSlot onAbort = AbortSlot(), DoneSlot onDone = DoneSlot());

   virtual void SendKey(HIDUsage usbHid, bool isKeyDown, AbortSlot onAbort = AbortSlot(),
                        DoneSlot onDone = DoneSlot());

   virtual void SendKeyEvent(MKSModifierState modState, VScancode scancode,
                             AbortSlot onAbort = AbortSlot(), DoneSlot onDone = DoneSlot());

   void InjectKeys(const utf::string &sequence, AbortSlot onAbort = AbortSlot(),
                   DoneSlot onDone = DoneSlot());

   void SendMacCGEvent(const uint8 *cgEventBytes, uint32 cgEventLength, int eventPid,
                       AbortSlot onAbort = AbortSlot(), DoneSlot onDone = DoneSlot());

   void SendUnityMouseEvent(int x, int y, int wheelDistance, int horizDistance, bool leftBtnDown,
                            bool rightBtnDown, bool middleBtnDown, bool btn4Down, bool btn5Down,
                            AbortSlot onAbort = AbortSlot(), DoneSlot onDone = DoneSlot());

   virtual void SendUnityMouseEvent(const UnityMouseEvent &mouseEvent,
                                    AbortSlot onAbort = AbortSlot(), DoneSlot onDone = DoneSlot());

   void SendUnityTouchEvent(const UnityTouchEvent &touchEvent, AbortSlot onAbort = AbortSlot(),
                            DoneSlot onDone = DoneSlot());

   virtual void SetEatKeys(bool lControl, bool rControl, bool lAlt, bool rAlt, bool lShift,
                           bool rShift, bool lGui, bool rGui);

   bool GetAllowGrabInUnity();

   void SetUnityHostCursorScale(double value);

   void SetAllowGrabInUnity(bool allowed);

   void SetMouseInMKSWindow(bool inWindow, AbortSlot onAbort = AbortSlot(),
                            DoneSlot onDone = DoneSlot());

   bool SetMacOSEventTapUIPID(uint32 pid);

   MKSWindowGroupID GetMKSWindowGroupID() const { return mWindowGroupID; }

   ScreenWindows &GetScreenWindows() { return mScreenWindows; }
   virtual void SetScreenWindows(const ScreenWindows &sw) { mScreenWindows = sw; }

   void DropDelayedModifierKeys(AbortSlot onAbort = AbortSlot(), DoneSlot onDone = DoneSlot());
   void ReleaseAllKeys(AbortSlot onAbort = AbortSlot(), DoneSlot onDone = DoneSlot());
   void SetCursorGrabAllowed(bool allowed, AbortSlot onAbort = AbortSlot(),
                             DoneSlot onDone = DoneSlot());

   virtual MKSScreenView *GetMainMKSScreenView() { return mMKSScreenView.get(); }
   void SetMainMKSScreenView(MKSScreenView *mksScreenView);

   sigc::signal<void, cui::MKSScreenView *> macosCursorMKSScreenViewChanged;

   virtual MKSScreenMgr *GetScreenMgr() const { return mMKSScreenMgr.get(); }
   MKSWindowMgr *GetWindowMgr() const { return mMKSWindowMgr.get(); }

   void ConnectLocalMKSServer(int port, MKSControlLibAuthToken *token,
                              AbortSlot onAbort = AbortSlot(), DoneSlot onDone = DoneSlot());
   void ConnectMKSServer(const utf::string &mksPipeName, MKSControlLibAuthToken *token,
                         AbortSlot onAbort = AbortSlot(), DoneSlot onDone = DoneSlot());
   void ConnectVNC(const utf::string &host, unsigned int port, const EncryptedString &password,
                   AbortSlot onAbort = AbortSlot(), DoneSlot onDone = DoneSlot());

   void CancelConnecting();

   void OnMKSStalled(bool stalled);

   bool GetWin32MKSIntegrityLevel(uint32 &levelOut) const;

   bool SetVNCServerSetting(Bool enabled, uint16 port, const uint32 *key, uint32 keyLen);

   std::vector<utf::string> GetDisallowedThumbprintTypes() const;

   Property<utf::string> displayName;

   /** Emits when the MKS is detaching. */
   sigc::signal<void> detaching;

   /** Emits when there's an error from the MKS or an operation. */
   sigc::signal<void, const utf::string &> error;

   /** Can this mks go fullscreen */
   Capability canFullscreen;

   /** Can this mks grab the UI */
   Capability canGrab;

   /** Can this mks take a screenshot */
   Capability canScreenshot;

   /** Can this mks send ctrl-alt-delete */
   Capability canSendCtrlAltDel;

   /*
    * Various preference properties that control behavior of the MKS.
    */

   /** Whether to allow ungrabbing via the keyboard. */
   Property<bool> allowKeyboardUngrab;

   /** Whether or not to grab when a key is pressed. */
   Property<bool> grabOnKeyPress;

   /** Whether or not to grab when the mouse is clicked. */
   Property<bool> grabOnMouseClick;

   /** Whether or not to grab when the mouse enters the MKS. */
   Property<bool> grabOnMouseEnter;

   /** Whether or not to ungrab when the mouse leaves the MKS. */
   Property<bool> ungrabOnMouseLeave;

   /** Whether or not to ungrab if the MKS is obscured. */
   Property<bool> ungrabIfObscured;

   /** Whether or not to hide the cursor when ungrabbed. */
   Property<bool> hideCursorOnUngrab;

   /** The MKS key modifiers. */
   Property<GamingMouseMode> gamingMouseMode;

   /** Whether to allow host shortcuts. */
   Property<bool> hostShortcutsAreEnabled;

   /*
    * Various preference properties End.
    */

   /** Whether to allow local MKS for remote VMs. */
   Property<bool> allowLocalForRemote;

   /** If non-zero, the port over which to tunnel remote MKS. */
   Property<unsigned int> httpsTunnel;

   /** The MKS key modifiers. */
   Property<Modifiers> mksKeys;

   /** Use the debug MKS. */
   Property<bool> useDebugMKS;

   /** Whether or not to use IME input text */
   Property<bool> useIMEInput;

   /** Whether the UI should send raw CGEvents (macOS only) */
   Property<bool> shouldUISendRawEvents;

   /** Whether or not to sync the guest numlock. */
   Property<bool> syncGuestNumLock;

   /** Whether or not to sync the guest capslock. */
   Property<bool> syncGuestCapsLock;

   /** Whether or not to sync the guest scrolllock. */
   Property<bool> syncGuestScrollLock;

   /** Whether or not to block screen capture. */
   Property<bool> blockScreenCaptureEnabled;

   /** Whether or not to allow screen recording. */
   Property<bool> allowScreenRecordingEnabled;

   /** Whether or not to allow connection from windows arm client with anti-keylogger launch failed.
    */
   Property<bool> allowArmNoAntiKeyloggerEnabled;

   /** Whether or not to block keylogger. */
   Property<bool> blockKeyLoggerEnabled;

   /** Whether or not to block SendInput(). */
   Property<bool> blockSendInputEnabled;

   /** Whether or not to block Icon Thumbnail representation when window is minimized. */
   Property<bool> blockThumbnailRepresentationEnabled;

   /** Whether or not to enable display of network warning UI. */
   Property<bool> networkWarningUIEnableDisplay;

   /** The network warning UI showing time interval. */
   Property<uint32> networkWarningUIShowingInterval;

protected:
   void SetConnectingNotification(bool connecting);
   void SetAttached(bool attached);

   virtual void ApplyPrefs(void);
   void ApplyHotkeys(void);

   virtual void OnMKSConnected();
   virtual void OnMKSDisconnected();
   void OnSetAttachedCompleted();

   void SetRemoteConnSessionHost(utf::string sessionHost) { mRemoteConnSessionHost = sessionHost; }
   void SetRemoteConnSessionVMSpecifer(utf::string sessionVMSpecifer)
   {
      mRemoteConnSessionVMSpecifer = sessionVMSpecifer;
   }

   std::unique_ptr<mksctrl::MKSControlClientBase> mMKSControlClient;

   /** Emits when the viewControl channel is broken. */
   sigc::signal<void> mksControlConnectionBroken;

   MKSWindowGroupID mWindowGroupID;
   ScreenWindows mScreenWindows;

   /* Are we currently attached to an MKS process? */
   bool mAttached;

   /* We need know the pipe name to connect to MKS */
   utf::string mMksControlPipeName;
   MKSControlLibAuthToken mMksToken;

   std::unique_ptr<MKSWindowMgr> mMKSWindowMgr;

private:
   // Disallow copying:
   MKS(const MKS &);
   MKS operator=(const MKS &);

   void OnSetAttachedCompletedError(void);
   void OnSetAttachedCompletedDone(void);

   void OnSetAttachedDoneMKSControl(DoneSlot onDone);
   void OnSetAttachedErrorMKSControl(bool cancelled, const Error &err, AbortSlot onAbort);

   void OnMKSControlConnectionBroken(void);
   void OnCanReconnectChanged(void);
   void OnConnectedChanged(void);
   void OnConnectingNotificationTimeout(void);
   void ApplyHookedKeys(void);
   void OnHookedKeyPressed(void);
   void OnNotifyUIEvent(void);

   bool TestPresent(Capability::FailureReasonList &list);
   bool TestUngrabUnlocked(Capability::FailureReasonList &list);

   void ConnectMKS(void);
   void DisconnectMKS(void);

   void ConnectMKSControl(AbortSlot onAbort, DoneSlot onDone);
   void DisconnectMKSControl(void);

   void OnMKSControlConnectDone(DoneSlot onDone);
   void OnMKSControlConnectAbort(bool cancelled, const Error &error, AbortSlot onAbort);

   // Grab/ungrab
   void OnSetUngrabLockedFinish(bool grabStateChanged);
   void OnUngrabLockChanged();

   utf::string GetRemoteConnSessionHost() { return mRemoteConnSessionHost; }
   utf::string GetRemoteConnSessionVMSpecifer() { return mRemoteConnSessionVMSpecifer; }

   // VNC
   void OnVNCServerErrorChanged(int asyncSocketError);

   // LED
   void OnKeyboardLEDStateChanged(bool numLockEnabled, bool capsLockEnabled,
                                  bool scrollLockEnabled);
   bool numLockEnabled;
   bool capsLockEnabled;
   bool scrollLockEnabled;

   // Cursor
   void OnCursorConstrainedChanged(bool cursorConstrained);

   bool cursorConstrained;

   /* Is there a pending SetAttached()? */
   bool mPending;

   /* Have we been trying to reconnect for a while? */
   bool mConnectingNotification;

   /*
    * A queue of calls to SetAttached() that are waiting for the current
    * call to complete.
    * The function slots() is removed in sigc++ 3.0. The Signal is used for
    * implementing the functions.
    */
   Signal mCompleted;

   mutable uint32 mMaxGuestScreenWidth;
   mutable uint32 mMaxGuestScreenHeight;
   mutable uint32 mMaxGuestNumDisplays;
   mutable uint32 mMaxBoundingBoxMemoryBytes;

   utf::string mOwner;
   std::vector<HotKey> mHookedKeys;

   // Preferences
   cui::DeferredSignal mksPrefsChanged;

   // Grab/ungrab
   bool mUngrabLocked;

   // VNC
   int mVNCLastError;

   std::unique_ptr<MKSScreenMgr> mMKSScreenMgr;
   std::unique_ptr<MKSScreenView> mMKSScreenView;

   int mParentPID;

   /* Remoting-related flags */
   utf::string mMksClientFilePath;
   StringVec mMksClientExtraArgs;

   bool mIsMKSStalled;
   bool mWantToAttach;

   /*
    * Remote connection only, currently only effective for
    * VIM and mks-only session.
    */
   utf::string mRemoteConnSessionHost;
   utf::string mRemoteConnSessionVMSpecifer;

   std::vector<utf::string> mDisallowedThumbprintTypes;
};

class LIB_EXPORT MKSFailureReason : public Capability::FailureReason {
public:
   enum ErrorDetail {
      ERR_NOT_PRESENT,
      ERR_UNGRAB_LOCKED,
   };

   typedef std::shared_ptr<const MKSFailureReason> Ptr;

   MKSFailureReason(const utf::string &testId, ErrorDetail detail) :
      Capability::FailureReason(testId),
      errorDetail(detail)
   {}
   MKSFailureReason *Clone() const { return new MKSFailureReason(*this); }

   ErrorDetail errorDetail;
};


} /* namespace cui */


#endif /* CUI_MKS_HH */
