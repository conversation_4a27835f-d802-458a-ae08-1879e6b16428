# Copyright (c) Omnissa, LLC. All rights reserved.
# This product is protected by copyright and intellectual property laws in the
# United States and other countries as well as by international treaties.
# -- Omnissa Restricted

"""Build file for agentPrepTool module

This file builds bora/apps/horizonAgent/apps/hzaprep (Windows only)
consumed by the horizonagent target

"""
import vmware

env = vmware.Host().DefaultEnvironment()
NODE_NAME = "hzaprep"

env.LoadTool(
    [
        "horizonUtils",
        "msvcrt-static",
        "atlmfc",
        "fmtlib",
    ]
)

env.LoadTool("mfw", linkStaticCRT=True)

env.Append(
    CPPDEFINES={
        "UNICODE": None,
        "_UNICODE": None,
    },
    CPPPATH=[
        "#bora/public",
        "#bora/apps/horizonAgent/include",
        "#bora/apps/horizonCommon/include/mfw",
        "#bora/apps/horizonCommon/lib/mfw/keyvault",
        "#bora/apps/horizonAgent/lib/wsnm_common",
    ],
)

e = vmware.Executable(NODE_NAME, env=env)

e.GetEnv().AddRdeVersionInfo(e, NODE_NAME + ".exe", "Horizon Agent Prep Tool")

e.addSubdirs(["apps/horizonAgent/apps/hzaprep"])


# Create EXE
node = e.createProgramNode()

vmware.RegisterNode(node, NODE_NAME)
vmware.RegisterEnv("%s-env" % NODE_NAME, env)
vmware.Alias("%s-build" % NODE_NAME, node)
