/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * Template Helper Service
 * ICAgent interacts with this service.
 */

#pragma once

#include "serviceprep.h"
#include <mfwService.h>


class TemplateHelperService : public service {
public:
   /*
    * templateServiceOperation inner class
    * This is the external command interface
    */
   class templateServiceOperation : public serviceOperation {
   private:
      TemplateHelperService *mTheTemplateSvc;

   public:
      templateServiceOperation(TemplateHelperService *serviceInstance)
      {
         mTheTemplateSvc = serviceInstance;
      }

      MFW_MESSAGE_MAP_BEGIN_TRACE(TemplateHelperService::templateServiceOperation)
      MFW_OPERATION_BOOL(HINT_IT_HELPER)
      mTheTemplateSvc->prepInternalTemplate(MFW_PARAMS(), MFW_RESP());
      MFW_MESSAGE_MAP_END()
   };

   static WorkItem *createOperationInstance(void *serviceInstance)
   {
      return new TemplateHelperService::templateServiceOperation(
         reinterpret_cast<TemplateHelperService *>(serviceInstance));
   }

private:
   ServicePrep mSvcPrep;

   bool prepInternalTemplate(PropertyBag &params, PropertyBag &response);
};
