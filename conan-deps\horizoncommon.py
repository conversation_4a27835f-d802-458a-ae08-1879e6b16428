# Copyright (c) Omnissa, LLC. All rights reserved.
# This product is protected by copyright and intellectual property laws in the
# United States and other countries as well as by international treaties.
# -- Omnissa Restricted

import os
import sys

# Access peer file scons_bootstrap.py.
sys.path.append(os.path.dirname(__file__))
from scons_bootstrap import ConanSConsBootstrap


class ConanHorizon<PERSON>ommon(ConanSConsBootstrap):
    settings = "os", "arch", "build_type", "compiler"

    def requirements(self):
        super().requirements()

        # Append required packages instead of inserting them, unless you know what you do
        self.requires("fast_float/3.4.0")
        self.requires("nlohmann_json/3.11.2")
        self.requires("snappy/1.1.7")
        if self.settings.os not in ["Android", "iOS"]:
            self.requires("gtest/1.17.0")

        if not (self.settings.os == 'Windows' and \
                self.settings.arch in ['arm64ec']):
            self.requires("openssl/3.0.16")
            if self.settings.os != "iOS":
                self.requires("openssl_fips_validated/3.0.9")
        if self.settings.os != "Android":
            self.requires("fmt/10.2.1")
            if self.settings.os != "iOS":
                self.requires("jsoncpp/1.9.5")

        if self.settings.os == "Windows":
            if self.settings.arch != 'arm64ec' and self.settings.compiler.get_safe("runtime") != "static":
                self.requires("zlib/1.3.1", options={"shared": True})
                self.requires("boost/1.86.0")
            self.requires("wtl/10.0.10320")

        if self.settings.os == "Windows" and self.settings.arch in ["x86", "x86_64"]:
            self.requires("rescle/1.0.11", options={"shared": False})

        if self.settings.os == "Linux":
            self.requires("glibc/2.17")
            self.requires("libuv/1.48.0")
            self.requires("util_linux/2.39.3", options={"shared": True})

        if self.settings.os in ["Macos", "Linux", "Windows"] and \
                self.settings.arch != "arm64ec" and \
                self.settings.compiler.get_safe("runtime") != "static":
            self.requires("pcre2/10.42", options={"shared": True})
            self.requires("glib/2.84.1", options={"shared": True})

        self.requires("tomlplusplus/3.1.0")

    def build_requirements(self):
        super().build_requirements()

        self.tool_requires("7zip/24.09")
        if self.settings.os == "Linux":
            self.tool_requires("coreutils/9.4")
        if self.settings.os == "Windows":
            self.tool_requires("squishcoco/7.1.0")
        else:
            self.tool_requires("findutils/4.10.0")
