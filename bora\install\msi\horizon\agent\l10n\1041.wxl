﻿<?xml version="1.0" encoding="utf-8"?>

<WixLocalization Culture="ja-jp" Codepage="932" xmlns="http://schemas.microsoft.com/wix/2006/localization">
   <String Id="LANGID">1041</String>

   <!-- Installshield Strings -->
   <String Id="IDS_COMPLUS_PROGRESSTEXT_COST">COM+ アプリケーションのコスト分析: [1]</String>
   <String Id="IDS_COMPLUS_PROGRESSTEXT_INSTALL">COM+ アプリケーションのインストール: [1]</String>
   <String Id="IDS_COMPLUS_PROGRESSTEXT_UNINSTALL">COM+ アプリケーションのアンインストール: [1]</String>
   <String Id="IDS_PROGMSG_IIS_CREATEAPPPOOL">アプリケーション プール %s の作成</String>
   <String Id="IDS_PROGMSG_IIS_CREATEAPPPOOLS">アプリケーション プールを作成しています...</String>
   <String Id="IDS_PROGMSG_IIS_CREATEVROOT">IIS 仮想ディレクトリ %s の作成</String>
   <String Id="IDS_PROGMSG_IIS_CREATEVROOTS">IIS 仮想ディレクトリを作成しています...</String>
   <String Id="IDS_PROGMSG_IIS_CREATEWEBSERVICEEXTENSION">Web サービス拡張の作成</String>
   <String Id="IDS_PROGMSG_IIS_CREATEWEBSERVICEEXTENSIONS">Web サービス拡張を作成しています...</String>
   <String Id="IDS_PROGMSG_IIS_EXTRACT">IIS 仮想ディレクトリの情報を抽出しています...</String>
   <String Id="IDS_PROGMSG_IIS_EXTRACTDONE">IIS 仮想ディレクトリ用に抽出された情報...</String>
   <String Id="IDS_PROGMSG_IIS_REMOVEAPPPOOL">アプリケーション プールの削除</String>
   <String Id="IDS_PROGMSG_IIS_REMOVEAPPPOOLS">アプリケーション プールを削除しています...</String>
   <String Id="IDS_PROGMSG_IIS_REMOVESITE">ポート %d で Web サイトを削除します</String>
   <String Id="IDS_PROGMSG_IIS_REMOVEVROOT">IIS 仮想ディレクトリ %s の削除</String>
   <String Id="IDS_PROGMSG_IIS_REMOVEVROOTS">IIS 仮想ディレクトリを削除しています...</String>
   <String Id="IDS_PROGMSG_IIS_REMOVEWEBSERVICEEXTENSION">Web サービス拡張の削除</String>
   <String Id="IDS_PROGMSG_IIS_REMOVEWEBSERVICEEXTENSIONS">Web サービス拡張を削除しています...</String>
   <String Id="IDS_PROGMSG_IIS_ROLLBACKAPPPOOLS">アプリケーション プールをロールバックしています...</String>
   <String Id="IDS_PROGMSG_IIS_ROLLBACKVROOTS">仮想ディレクトリ、および Web サイトの変更をロールバックしています...</String>
   <String Id="IDS_PROGMSG_IIS_ROLLBACKWEBSERVICEEXTENSIONS">Web サービス拡張をロールバックしています...</String>
   <String Id="IDS_PROGMSG_XML_COSTING">XML ファイルのコスト分析...</String>
   <String Id="IDS_PROGMSG_XML_CREATE_FILE">XML ファイル %s を作成しています...</String>
   <String Id="IDS_PROGMSG_XML_FILES">XML ファイルの変更を行っています...</String>
   <String Id="IDS_PROGMSG_XML_REMOVE_FILE">XML ファイル %s を削除しています...</String>
   <String Id="IDS_PROGMSG_XML_ROLLBACK_FILES">XML ファイルの変更をロールバックしています...</String>
   <String Id="IDS_PROGMSG_XML_UPDATE_FILE">XML ファイル %s を更新しています...</String>


   <!-- LaunchCondition Error Messages -->
   <String Id="MINIMUM_REQUIRED_OS">この製品は、Windows 10、Windows Server 2016 以降の OS にのみインストールできます。</String>
   <String Id="DENY_INSTALL_DOMAIN_CONTROLLER">この製品は、ドメイン コントローラにインストールできません。</String>
   <String Id="NEED_ADMIN">このソフトウェアのインストール/アンインストールには、管理者権限が必要です。</String>


   <!-- Feature Table -->
   <String Id="FEATURE_NAME_CORE">コア</String>
   <String Id="FEATURE_DESC_CORE">[ProductName] のコア機能</String>
   <String Id="FEATURE_NAME_CORRETTO">Corretto</String>
   <String Id="FEATURE_DESC_CORRETTO">Corretto JDK ディストリビューションの [ProductName] コア機能</String>
   <String Id="FEATURE_NAME_BELLSOFT">Bellsoft</String>
   <String Id="FEATURE_DESC_BELLSOFT">Bellsoft JDK ディストリビューションの [ProductName] コア機能</String>
   <String Id="FEATURE_NAME_RDSH3D">3D RDSH</String>
   <String Id="FEATURE_DESC_RDSH3D">この機能は RDSH セッションと物理 PC セッションのハードウェア 3D のアクセラレーションを有効にします。</String>
   <String Id="FEATURE_NAME_CLIENTDRIVEREDIRECTION">クライアント ドライブのリダイレクト</String>
   <String Id="FEATURE_DESC_CLIENTDRIVEREDIRECTION">Horizon Client で、リモート デスクトップおよびアプリケーションから共有のローカル ドライブを使用できるようにします。インストールされていない場合は、コピー アンド ペースト機能、ファイルとフォルダのドラッグ アンド ドロップ機能は無効になります。</String>
   <String Id="FEATURE_NAME_NGVC">Instant Clone Agent</String>
   <String Id="FEATURE_DESC_NGVC">Instant Clone Agent のインストールは、VMware vSphere 7.0 以降で実行している仮想マシンに限定してください。</String>
   <String Id="FEATURE_DESC_PCOIP_PHYSICAL">この機能は、PCoIP サーバ コンポーネントをデスクトップにインストールします。</String>
   <String Id="FEATURE_NAME_RTAV">リアルタイム オーディオ-ビデオ</String>
   <String Id="FEATURE_DESC_RTAV">リアルタイム オーディオ ビデオでは、ユーザーがローカルで接続されたオーディオおよびビデオ周辺機器をリモート デスクトップにリダイレクトして使用することができます。</String>
   <String Id="FEATURE_NAME_VMWPRINT">Horizon Integrated Printing</String>
   <String Id="FEATURE_DESC_VMWPRINT">Horizon Integrated Printing リダイレクト。</String>

   <String Id="FEATURE_NAME_SCANNERREDIRECTION">スキャナ リダイレクト</String>
   <String Id="FEATURE_DESC_SCANNERREDIRECTION">スキャナ リダイレクト機能を有効にします。</String>
   <String Id="FEATURE_NAME_SERIALPORTREDIRECTION">シリアル ポート リダイレクト</String>
   <String Id="FEATURE_DESC_SERIALPORTREDIRECTION">シリアル ポート リダイレクト機能を有効にします。</String>
   <String Id="FEATURE_NAME_SMARTCARD">Smartcard リダイレクト</String>
   <String Id="FEATURE_DESC_SMARTCARD">Smartcard リダイレクト機能を有効にします。</String>
   <String Id="FEATURE_NAME_TSMMR">TSMMR</String>
   <String Id="FEATURE_DESC_TSMMR">ターミナル サービスのマルチメディア リダイレクト。</String>
   <String Id="FEATURE_NAME_URLREDIRECTION">URL コンテンツ リダイレクト</String>
   <String Id="FEATURE_DESC_URLREDIRECTION">サーバ セッションの URL コンテンツをクライアント デバイスに、またはその逆にリダイレクトします。</String>
   <String Id="FEATURE_NAME_UNCREDIRECTION">UNC パス リダイレクト</String>
   <String Id="FEATURE_DESC_UNCREDIRECTION">UNC パスをサーバ セッションからクライアント デバイスに、またはその逆にリダイレクトします。</String>
   <String Id="FEATURE_NAME_USB">USB リダイレクト</String>
   <String Id="FEATURE_DESC_USB">USB リダイレクト。USB リダイレクトを安全に使用するために、Horizon セキュリティのドキュメントを参照してください。</String>
   <String Id="FEATURE_NAME_HZNVAUDIO">Horizon オーディオ</String>
   <String Id="FEATURE_DESC_HZNVAUDIO">Horizon 仮想オーディオ ドライバ</String>
   <String Id="FEATURE_NAME_HTML5MMR">HTML5 マルチメディア リダイレクト</String>
   <String Id="FEATURE_DESC_HTML5MMR">HTML5 マルチメディアのリダイレクトを有効にする</String>
   <String Id="FEATURE_NAME_GEOREDIR">位置情報リダイレクト</String>
   <String Id="FEATURE_DESC_GEOREDIR">クライアント位置情報のリモート デスクトップへのリダイレクトを有効にします。</String>
   <String Id="FEATURE_NAME_SDOSENSOR">SDO センサーのリダイレクト</String>
   <String Id="FEATURE_DESC_SDOSENSOR">Simple Device Orientation(SDO) センサーのリダイレクト機能を有効にして、デバイスの方向変更をリモートのデスクトップに報告します。</String>
   <String Id="FEATURE_NAME_STORAGEDRIVE">ストレージ ドライブのリダイレクト</String>
   <String Id="FEATURE_DESC_STORAGEDRIVE">クライアントのストレージ ドライブのリモート デスクトップへのリダイレクトを有効にします。</String>
   <String Id="FEATURE_NAME_PERFTRACKER">Horizon Performance Tracker</String>
   <String Id="FEATURE_DESC_PERFTRACKER">Horizon Performance Tracker を有効にします</String>
   <String Id="FEATURE_NAME_HYBRIDLOGON">ハイブリッド ログイン</String>
   <String Id="FEATURE_DESC_HYBRIDLOGON">ハイブリッド ログインを有効にします。有効にすると、非認証ユーザーが認証情報を入力せずにネットワーク リソースにアクセスできるようになります。</String>
   <String Id="FEATURE_NAME_HELPDESK">Horizon Agent のヘルプ デスク プラグイン</String>
   <String Id="FEATURE_DESC_HELPDESK">Horizon Agent のヘルプ デスク プラグイン。</String>

   <!-- Control Panel Strings -->
   <String Id="Url">https://www.omnissa.com/</String>

   <!-- Firewall Strings -->
   <String Id="BlastUDPFirewallExceptionName">Omnissa Horizon Blast UDP トラフィックの例外</String>

   <!-- UI Dialog Strings -->
   <String Id="IDS__DisplayName_Custom">カスタム</String>
   <String Id="IDS__DisplayName_Minimal">最小</String>
   <String Id="IDS__DisplayName_Typical">標準</String>
   <String Id="INTEL_UNS_DESC">Intel User Notification サービスを提供します。</String>
   <String Id="IDS_LicenseAcceptance">インストールすることで以下の書類に同意したことになります：</String>
   <String Id="IDS_GeneralTerms">一般条項</String>
   <String Id="IDS_CANCEL">キャンセル</String>
   <String Id="IDS_CANCEL2">キャンセル (&amp;C)</String>
   <String Id="IDS_OK">OK</String>
   <String Id="IDS_BACK">&lt; 戻る (&amp;B)</String>
   <String Id="IDS_NEXT">次へ (&amp;N) &gt;</String>
   <String Id="IDS_FINISH">完了 (&amp;F)</String>
   <String Id="IDS__IsCancelDlg_No">いいえ (&amp;N)</String>
   <String Id="IDS__IsCancelDlg_Yes">はい (&amp;Y)</String>
   <String Id="IDS__IsAdminInstallBrowse_LookIn">探す場所 (&amp;L):</String>
   <String Id="IDS__IsAdminInstallBrowse_UpOneLevel">1 つ上のレベルに移動</String>
   <String Id="IDS__IsAdminInstallBrowse_BrowseDestination">インストール先フォルダを参照してください。</String>
   <String Id="IDS__IsAdminInstallBrowse_ChangeDestination">{&amp;MSSansBold8}現在のインストール先フォルダの変更</String>
   <String Id="IDS__IsAdminInstallBrowse_CreateFolder">フォルダの新規作成</String>
   <String Id="IDS__IsAdminInstallBrowse_FolderName">フォルダ名前 (&amp;F):</String>
   <String Id="IDS__IsAdminInstallPoint_Install">インストール (&amp;I)</String>
   <String Id="IDS__IsAdminInstallPoint_SpecifyNetworkLocation">製品のサーバ イメージに対するネットワーク ロケーションを指定してください。</String>
   <String Id="IDS__IsAdminInstallPoint_EnterNetworkLocation">ネットワーク ロケーションを入力するか、あるいは「変更」をクリックしてロケーションを参照してください。「インストール」をクリックすると、指定したネットワーク ロケーションに [ProductName] のサーバ イメージを作成します。「キャンセル」をクリックすると、ウィザードを終了します。</String>
   <String Id="IDS__IsAdminInstallPoint_NetworkLocationFormatted">{&amp;MSSansBold8}ネットワーク ロケーション</String>
   <String Id="IDS__IsAdminInstallPoint_NetworkLocation">ネットワーク ロケーション (&amp;N):</String>
   <String Id="IDS__IsAdminInstallPoint_Change">変更 (&amp;C)...</String>
   <String Id="IDS__IsAdminInstallPointWelcome_Wizard">{&amp;TahomaBold10}[ProductName] 用のインストーラへようこそ</String>
   <String Id="IDS__IsAdminInstallPointWelcome_ServerImage">インストーラは、指定したネットワーク ロケーションに [ProductName] のサーバ イメージを作成します。続行するには、「次へ」をクリックしてください。</String>
   <String Id="ProductVersion">{&amp;Arial9}製品バージョン: [ProductVersionString]</String>
   <String Id="IDS__IsCancelDlg_ConfirmCancel">[ProductName] のインストールをキャンセルしてもよろしいですか。</String>
   <String Id="IDS__IsInstallRolesConfirmDlg_Message">インストーラがオペレーティング システムに必要なロールをインストールします。続行するには「OK」をクリックします。</String>
   <String Id="ConnectionServer_TitleDesc">このマシンが接続する Horizon Connection Server を入力してください。</String>
   <String Id="ConnectionServer_Title">{&amp;MSSansBold8}Horizon Connection Server に登録</String>
   <String Id="ConnectionServer_Text">このマシンを Horizon Connection Server に登録するには、Horizon Connection Server (標準またはレプリカ インスタンス) のサーバ名と管理者のログイン認証情報を入力してください。</String>
   <String Id="ConnectionServer_ServerNote">(ホスト名または IP アドレス)</String>
   <String Id="ConnectionServerLogin_Text1">現在ログオンしているユーザーとして認証する (&amp;A)</String>
   <String Id="ConnectionServerLogin_Text2">管理者の認証情報を指定する (&amp;C)</String>
   <String Id="ConnectionServerLogin_Title">認証:</String>
   <String Id="ConnectionServer_Username">ユーザー名 (&amp;U):</String>
   <String Id="ConnectionServer_UsernameNote">(ドメイン\ユーザー)</String>
   <String Id="ConnectionServer_Password">パスワード (&amp;P):</String>
   <String Id="IDS__IsCustomSelectionDlg_SelectFeatures">インストールするプログラムの機能を選択してください。</String>
   <String Id="IDS__IsCustomSelectionDlg_ClickFeatureIcon">下のリストにあるアイコンをクリックして、機能のインストール方法を変更してください。</String>
   <String Id="IDS__IsCustomSelectionDlg_CustomSetup">{&amp;MSSansBold8}カスタム セットアップ</String>
   <String Id="IDS__IsCustomSelectionDlg_Change">変更 (&amp;C)...</String>
   <String Id="IDS__IsCustomSelectionDlg_FeatureDescription">機能の説明</String>
   <String Id="IDS__IsCustomSelectionDlg_InstallTo">インストール先:</String>
   <String Id="IDS__IsCustomSelectionDlg_MultilineDescription">選択しているアイテムの説明</String>
   <String Id="IDS__IsCustomSelectionDlg_FeaturePath">&lt;selected feature path&gt;</String>
   <String Id="IDS__IsCustomSelectionDlg_FeatureSize">機能のサイズ</String>
   <String Id="IDS__IsCustomSelectionDlg_Help">ヘルプ (&amp;H)</String>
   <String Id="IDS__IsCustomSelectionDlg_Space">ディスク (&amp;S)</String>
   <String Id="IDS_SetupTips_CustomSetupDescription">カスタム セットアップを選択すると、必要なプログラム機能のみをインストールすることができます。</String>
   <String Id="IDS_SetupTips_CustomSetup">{&amp;MSSansBold8}カスタム セットアップ ティップ</String>
   <String Id="IDS_SetupTips_WillNotBeInstalled">インストールされません。</String>
   <String Id="IDS_SetupTips_Advertise">最初の使用時にインストールします。(機能がこのオプションをサポートする場合のみ有効です。)</String>
   <String Id="IDS_SetupTips_InstallState">各アイコンは、次のインストール状態を示します...</String>
   <String Id="IDS_SetupTips_AllInstalledLocal">ローカルのハード ディスク ドライブにすべての機能をインストールします。</String>
   <String Id="IDS_SetupTips_IconInstallState">機能名の隣にあるアイコンは、機能のインストール状態を示しています。アイコンをクリックして、各機能の「インストール状態」メニューをドロップダウンしてください。</String>
   <String Id="IDS_SetupTips_Network">ネットワークから実行するようにインストールします。(機能がこのオプションをサポートする場合のみ有効です。)</String>
   <String Id="IDS_SetupTips_SubFeaturesInstalledLocal">ローカルのハードディスク ドライブにサブ機能をインストールします。(機能に、サブ機能が含まれる場合のみ有効です。)</String>
   <String Id="DesktopConfig_Subtitle">Horizon デスクトップ機能の構成に、次の情報が使用されます</String>
   <String Id="DesktopConfig_Title">{&amp;MSSansBold8}デスクトップ OS 構成</String>
   <String Id="DesktopConfig_Text">この OS での [ProductName] 用のモードを選択:</String>
   <String Id="DesktopConfig_RDSHMode">このオペレーティング システムには、必要なリモート デスクトップ セッション ホスト (RDSH) のロールがインストールされていません。

「次へ」をクリックして、必要なロール/機能をインストールします。完了したら、オペレーティング システムを再起動する必要があります。

再起動後、RDS モードでのインストールを続行するために、[ProductName] のインストーラを再起動する必要があります。</String>
   <String Id="DesktopConfig_DesktopMode">このエージェントは、デスクトップ VDI モードで構成されます。</String>
   <String Id="DesktopConfig_InstallingRolesSuccess">必要なオペレーティング システムのロール/機能のインストールに成功しました。オペレーティング システムを再起動して、[ProductName] インストーラを起動し直してください。</String>
   <String Id="DesktopConfig_InstallingRolesFail">エラー: インストーラは、必要なオペレーティング システムのロール/機能をインストールできませんでした。</String>
   <String Id="IDS__IsDesktopConfigDlg_RDSMode">RDS モード</String>
   <String Id="IDS__IsDesktopConfigDlg_DesktopMode">デスクトップ モード</String>
   <String Id="InstallRolesConfirm_InstallingRoles">このシステムで必要なロール/機能を構成しています。しばらくお待ちください...</String>
   <String Id="IDS__IsFeatureDetailsDlg_DiskSpaceRequirements">{&amp;MSSansBold8}必要なディスク容量</String>
   <String Id="IDS__IsFeatureDetailsDlg_SpaceRequired">選択している機能をインストールするために必要なディスク容量です。</String>
   <String Id="IDS__IsFeatureDetailsDlg_VolumesTooSmall">現在のボリュームには、選択している機能をインストールするために十分なディスク容量がありません。ボリュームからファイルを削除してディスク容量を確保するか、ローカル ディスクにインストールする機能を削減してください。または、別のドライブを選択してください。</String>
   <String Id="IDS__IsFilesInUse_Retry">再試行 (&amp;R)</String>
   <String Id="IDS__IsFilesInUse_Ignore">無視 (&amp;I)</String>
   <String Id="IDS__IsFilesInUse_Exit">終了 (&amp;E)</String>
   <String Id="IDS__IsFilesInUse_FilesInUse">{&amp;MSSansBold8}使用中のファイル</String>
   <String Id="IDS__IsFilesInUse_FilesInUseMessage">更新が必要なファイルの一部が現在使用中です。</String>
   <String Id="IDS__IsFilesInUse_ApplicationsUsingFiles">次のアプリケーションが、このセットアップで更新する必要のあるファイルを使用しています。続行するには、これらのアプリケーションを閉じて「再試行」をクリックしてください。

注: 次のリストに [ProductName] が表示されている場合は、「無視」をクリックして続行してください。</String>
   <String Id="IDS__IsBrowseFolderDlg_LookIn">探す場所 (&amp;L):</String>
   <String Id="IDS__IsBrowseFolderDlg_UpOneLevel">1 つ上のレベルに移動</String>
   <String Id="IDS__IsBrowseFolderDlg_BrowseDestFolder">インストール先フォルダを参照してください。</String>
   <String Id="IDS__IsBrowseFolderDlg_ChangeCurrentFolder">{&amp;MSSansBold8}現在のインストール先フォルダの変更</String>
   <String Id="IDS__IsBrowseFolderDlg_CreateFolder">フォルダの新規作成</String>
   <String Id="IDS__IsBrowseFolderDlg_FolderName">フォルダ名前 (&amp;F):</String>
   <String Id="IDS__IsWelcomeDlg_WelcomeProductName">{&amp;TahomaBold10}[ProductName] インストール ウィザードにようこそ</String>
   <String Id="IDS__IsWelcomeDlg_InstallProductName">インストール ウィザードは、ご使用のコンピュータへ [ProductName] をインストールします。続行するには、「次へ」をクリックしてください。</String>
   <String Id="InstallWelcome_UpgradeLine1">インストール ウィザードは、ご使用のコンピュータの [ProductName] をアップグレードします。続行するには、「次へ」をクリックしてください。</String>
   <String Id="IDS__IsWelcomeDlg_WarningCopyright">Copyright (C) [CopyrightYears] Omnissa. All rights reserved. 本製品は、米国およびその他の地域における著作権および知的財産権法および国際条約により保護されています。「Omnissa」は、Omnissa, LLC、Omnissa International Unlimited Company、およびその子会社のいずれかまたは両方を指します。</String>
   <String Id="IpProtocolConfig_DlgDesc">通信プロトコルを選択します</String>
   <String Id="IpProtocolConfig_DlgTitle">{&amp;MSSansBold8}ネットワーク プロトコル構成</String>
   <String Id="GoldenImage_DlgDesc">Select whether this machine will be used as a Golden Image</String>
   <String Id="GoldenImage_DlgTitle">{&amp;MSSansBold8}Golden Image Selection</String>
   <String Id="GoldenImage_CheckBoxText">This machine will be used as a Golden Image</String>
   <String Id="ConnectionServer_IpText">この Horizon Agent インスタンスの構成に使用されるプロトコルを指定します。</String>
   <String Id="ConnectionServer_IPv4Desc">このエージェントは、すべての接続を確立するために IPv4 プロトコルを選択するように構成されます。</String>
   <String Id="ConnectionServer_IPv6Desc">このエージェントは、すべての接続を確立するために IPv6 プロトコルを選択するように構成されます。</String>
   <String Id="ConnectionServer_Dual4Desc">このエージェントは、混合 IP モードをサポートするよう構成され、接続の確立には IPv4 プロトコルが優先されます。</String>
   <String Id="ConnectionServer_Dual6Desc">このエージェントは、混合 IP モードをサポートするよう構成され、接続の確立には IPv6 プロトコルが優先されます。</String>
   <String Id="IpProtocolConfig_FipsText">FIPS 互換の暗号を使用してこの製品をインストールするかどうかを指定します。</String>
   <String Id="IpProtocolConfig_FipsDisabledDesc">このエージェント インスタンスは FIPS コンプライアンスなしに動作します。</String>
   <String Id="IpProtocolConfig_FipsEnabledDesc">このエージェント インスタンスは FIPS 準拠の暗号用に構成されます。</String>
   <String Id="FipsConfig_Disabled">無効</String>
   <String Id="FipsConfig_Enabled">有効</String>
   <String Id="IDS__AgreeToLicense_0">一般条項に同意しません (&amp;D)</String>
   <String Id="IDS__AgreeToLicense_1">一般条項に同意します (&amp;A)</String>
   <String Id="IDS__IsLicenseDlg_ReadLicenseAgreement">次の一般条項をよくお読みください。</String>
   <String Id="IDS__IsLicenseDlg_LicenseAgreement">{&amp;MSSansBold8}一般条項</String>
   <String Id="IDS__IsMaintenanceDlg_Modify">{&amp;MSSansBold8}変更 (&amp;M)</String>
   <String Id="IDS__IsMaintenanceDlg_Repair">{&amp;MSSansBold8}修復 (&amp;P)</String>
   <String Id="IDS__IsMaintenanceDlg_Remove">{&amp;MSSansBold8}削除 (&amp;R)</String>
   <String Id="IDS__IsMaintenanceDlg_MaitenanceOptions">プログラムを変更、修復、または削除します。</String>
   <String Id="IDS__IsMaintenanceDlg_ProgramMaintenance">{&amp;MSSansBold8}プログラムのメンテナンス</String>
   <String Id="IDS__IsMaintenanceDlg_ModifyMessage">インストールされている機能をユーザーが変更できるようにします。</String>
   <String Id="IDS__IsMaintenanceDlg_RepairMessage">プログラム中のインストール エラーを修復します。このオプションでは、失われたり壊れたりしたファイル、ショートカット、およびレジストリ エントリを修正することができます。</String>
   <String Id="IDS__IsMaintenanceDlg_RemoveProductName">コンピュータから [ProductName] を削除します。</String>
   <String Id="IDS__IsMaintenanceWelcome_WizardWelcome">{&amp;TahomaBold10}[ProductName] 用のインストーラへようこそ</String>
   <String Id="IDS__IsMaintenanceWelcome_MaintenanceOptionsDescription">インストーラを使用すると、[ProductName] の変更、修正、または削除を行うことができます。続行するには、「次へ」をクリックしてください。</String>
   <String Id="IDS_PRODUCTNAME_INSTALLSHIELD">[ProductName] - インストール ウィザード</String>
   <String Id="IDS__IsMsiRMFilesInUse_CloseRestart">自動的に閉じて、アプリケーションの再起動を試みます。</String>
   <String Id="IDS__IsMsiRMFilesInUse_RebootAfter">アプリケーションを閉じないでください。(再起動が必要です。)</String>
   <String Id="IDS__IsMsiRMFilesInUse_ApplicationsUsingFiles">以下のアプリケーションは、このセットアップで更新する必要のあるファイルを使用しています。</String>
   <String Id="IDS__IsDiskSpaceDlg_OutOfDiskSpace">{&amp;MSSansBold8}ディスク容量の不足</String>
   <String Id="IDS__IsDiskSpaceDlg_DiskSpace">インストールには、現在使用可能なディスク容量よりも多くのディスク容量が必要です。</String>
   <String Id="IDS__IsDiskSpaceDlg_HighlightedVolumes">強調表示されているボリュームには、選択している機能をインストールするために十分なディスク容量がありません。このボリュームからファイルを削除してディスク容量を確保するか、インストールをキャンセルしてください。</String>
   <String Id="RdpChoice_EnableRdp">このコンピュータでリモート デスクトップ機能を有効にする (&amp;E)</String>
   <String Id="RdpChoice_NoRdp">このコンピュータでリモート デスクトップ機能を有効にしない (&amp;D)</String>
   <String Id="RdpConfig_Subtitle">リモート デスクトップ機能の構成に、次の情報が使用されます</String>
   <String Id="RdpConfig_Title">{&amp;MSSansBold8}リモート デスクトップ プロトコル構成</String>
   <String Id="RdpConfig_Text">[ProductName] では、リモート デスクトップ サポートがオンになっている必要があります。RDP ポート [RDP_PORT_NUMBER] と View Framework チャネル [FRAMEWORK_CHANNEL_PORT] にファイアウォールの例外が追加されます。何を実行しますか。</String>
   <String Id="IDS__IsVerifyReadyDlg_Install">インストール (&amp;I)</String>
   <String Id="IDS__IsVerifyReadyDlg_WizardReady">ウィザードは、インストールを開始する準備ができました。</String>
   <String Id="ReadyToInstall_RdshNote">注: この OS では、RDS ロールが有効になっていません。[ProductName] では、1 台のデスクトップ接続のみがサポートされます。</String>
   <String Id="IDS__IsVerifyReadyDlg_ClickInstall">「インストール」をクリックして、インストールを開始してください。または、「キャンセル」をクリックして、ウィザードを終了します。</String>
   <String Id="IDS__IsVerifyReadyDlg_ReadyRepair">{&amp;MSSansBold8}プログラムの修復準備</String>
   <String Id="IDS__IsVerifyReadyDlg_ReadyInstall">{&amp;MSSansBold8}プログラムのインストール準備</String>
   <String Id="ReadyToInstall_InstallDir">[ProductName] は次の場所にインストールされます: 

[INSTALLDIR]</String>
   <String Id="ReadyToInstall_MsgSanPolicy_NGVC">注: VDS SAN ポリシーは、Instant Clone Agent (NGVC) 機能で要求されているとおり「すべてをオンライン」に設定されます。</String>
   <String Id="IDS__IsVerifyRemoveAllDlg_Remove">削除 (&amp;R)</String>
   <String Id="IDS__IsVerifyRemoveAllDlg_ChoseRemoveProgram">ご使用のシステムからプログラムを削除するオプションを選択しました。</String>
   <String Id="IDS__IsVerifyRemoveAllDlg_ClickRemove">「削除」をクリックして、コンピュータから [ProductName] を削除してください。削除を実行すると、このプログラムは、使用できなくなります。</String>
   <String Id="IDS__IsVerifyRemoveAllDlg_ClickBack">設定を確認したり変更する場合は、「戻る」をクリックします。</String>
   <String Id="IDS__IsVerifyRemoveAllDlg_RemoveProgram">{&amp;MSSansBold8}プログラムの削除</String>
   <String Id="IDS__IsFatalError_NotModified">システムの状態は変更されていません。改めてインストールする場合は、再度セットアップを実行してください。</String>
   <String Id="IDS__IsFatalError_ClickFinish">「完了」をクリックして、ウィザードを終了してください。</String>
   <String Id="IDS__IsFatalError_KeepOrRestore">システムにインストールされている既存の要素を保持して、後でインストールを続行するか、またはシステムをインストール前の状態に復元することができます。</String>
   <String Id="IDS__IsFatalError_RestoreOrContinueLater">「復元」、または「後で続行」をクリックしてウィザードを終了してください。</String>
   <String Id="IDS__IsFatalError_WizardCompleted">{&amp;TahomaBold10}インストーラの完了</String>
   <String Id="IDS__IsFatalError_WizardInterrupted">ウィザードは、[ProductName] のインストールを完了する前に中断されました。</String>
   <String Id="IDS__IsFatalError_UninstallWizardInterrupted">[ProductName] のアンインストールを完了する前にウィザードが中断されました。</String>
   <String Id="IDS__IsExitDialog_WizardCompleted">{&amp;TahomaBold10}インストーラの完了</String>
   <String Id="IDS__IsExitDialog_InstallSuccess">インストーラは、[ProductName] を正常にインストールしました。「完了」をクリックして、ウィザードを終了してください。</String>
   <String Id="IDS__IsExitDialog_UninstallSuccess">インストーラは、[ProductName] を正常にアンインストールしました。「完了」をクリックして、ウィザードを終了してください。</String>
   <String Id="IDS__IsExitDialog_InstallingRolesSuccess">インストーラは、RDS モードでの [ProductName] のインストールに必要なロール/機能を使用してオペレーティング システムを正常に構成しました。

「完了」をクリックしてウィザードを終了します。</String>
   <String Id="IDS__IsErrorDlg_InstallerInfo">[ProductName] インストーラ情報</String>
   <String Id="IDS__IsErrorDlg_Abort">中止 (&amp;A)</String>
   <String Id="IDS__IsErrorDlg_Yes">はい (&amp;Y)</String>
   <String Id="IDS__IsErrorDlg_No">いいえ (&amp;N)</String>
   <String Id="IDS__IsErrorDlg_Ignore">無視 (&amp;I)</String>
   <String Id="IDS__IsErrorDlg_OK">OK (&amp;O)</String>
   <String Id="IDS__IsErrorDlg_Retry">再試行 (&amp;R)</String>
   <String Id="IDS__IsInitDlg_WelcomeWizard">{&amp;TahomaBold10}[ProductName] 用のインストーラへようこそ</String>
   <String Id="IDS__IsInitDlg_PreparingWizard">[ProductName]セットアップは、プログラム セットアップの手順をご案内するインストーラを準備中です。しばらくお待ちください。</String>
   <String Id="IDS__IsUserExit_NotModified">システムの状態は変更されていません。改めてインストールする場合は、再度セットアップを実行してください。</String>
   <String Id="IDS__IsUserExit_ClickFinish">「完了」をクリックして、ウィザードを終了してください。</String>
   <String Id="IDS__IsUserExit_KeepOrRestore">システムにインストールされている既存の要素を保持して、後でインストールを続行するか、またはシステムをインストール前の状態に復元することができます。</String>
   <String Id="IDS__IsUserExit_RestoreOrContinue">「復元」、または「後で続行」をクリックしてウィザードを終了してください。</String>
   <String Id="IDS__IsUserExit_WizardCompleted">{&amp;TahomaBold10}インストーラの完了</String>
   <String Id="IDS__IsUserExit_WizardInterrupted">ウィザードは、[ProductName] のインストールを完了する前に中断されました。</String>
   <String Id="IDS__IsUserExit_UninstallWizardInterrupted">[ProductName] のアンインストールを完了する前にウィザードが中断されました。</String>
   <String Id="IDS__IsProgressDlg_UninstallingFeatures2">選択したプログラム機能をインストールしています。</String>
   <String Id="IDS__IsProgressDlg_UninstallingFeatures">選択したプログラム機能をアンインストールしています。</String>
   <String Id="IDS__IsProgressDlg_WaitUninstall2">インストーラが [ProductName] をインストールしている間、しばらくお待ちください。これには数分かかる場合があります。</String>
   <String Id="IDS__IsProgressDlg_WaitUninstall">インストーラが [ProductName] をアンインストールしている間、しばらくお待ちください。これには数分かかる場合があります。</String>
   <String Id="IDS__IsProgressDlg_InstallingProductName">{&amp;MSSansBold8}[ProductName] のインストール中</String>
   <String Id="IDS__IsProgressDlg_Uninstalling">{&amp;MSSansBold8}[ProductName] のアンインストール中</String>
   <String Id="IDS__IsProgressDlg_SecHidden">(現在非表示)秒</String>
   <String Id="IDS__IsProgressDlg_Status">ステータス:</String>
   <String Id="IDS__IsProgressDlg_Hidden">(現在非表示)</String>
   <String Id="IDS__IsProgressDlg_HiddenTimeRemaining">(現在非表示)推定残り時間:</String>
   <String Id="IDS__IsProgressDlg_ProgressDone">プログレスの完了</String>
   <String Id="IDS__IsResumeDlg_Resuming">{&amp;TahomaBold10}[ProductName] 用のインストーラを続行しています</String>
   <String Id="IDS__IsResumeDlg_ResumeSuspended">インストーラは、ご使用のコンピュータで中断していた [ProductName] のインストールを完了します。続行するには、「次へ」をクリックしてください。</String>
   <String Id="IDS__IsResumeDlg_WizardResume">インストーラは、ご使用のコンピュータへの [ProductName] のインストールを完了します。続行するには、「次へ」をクリックしてください。</String>


   <!-- Error Strings -->
   <String Id="MsgWSWCInstalled">このコンピュータには、互換性のないバージョンの Horizon Client がインストールされているため、インストールを続行できません。

[ProductName] のインストールを続行するには、Horizon Client をアンインストールしてから、このインストーラを再実行してください。</String>
   <String Id="MsgClientRunning">Agent のインストールを続行できません。アクティブな Horizon Client セッションが検出されました。</String>
   <String Id="MsgDowngradeDetected">インストーラは、[ProductName] の新しいバージョンがすでにインストールされていることを検出しました。</String>
   <String Id="MsgManualUninstallRequired">このインストーラは既存の製品インストールからのアップグレードを実行できません。既存の製品をアンインストールしてからインストールを続行してください。</String>
   <String Id="MsgMustReboot">インストールを続行する前に、システムを再起動する必要があります。</String>
   <String Id="MsgServerInstalled">このコンピュータに Horizon Connection Server がインストールされているため、インストールを続行できません。

[ProductName] のインストールを続行するには、Connection Server をアンインストールしてから、このインストーラを再実行してください。</String>
   <String Id="MsgUnsupportedOldVersion">この製品のサポートされていない古いバージョンがすでにインストールされているため、インストールできません。アンインストールし、システムを再起動してから、この製品をインストールしてください。</String>
   <String Id="MsgUrlRedirectionInstalled">URL リダイレクトを有効にして、[ProductName] をインストールしようとしていますが、Horizon Client によって URL リダイレクトはすでに有効になっています。URL リダイレクトを有効にせずにエージェントをそのままインストールすると、URL リダイレクトはサポートされません。エージェント モードで URL リダイレクトを可能にするには、最初に Client をアンインストールしてから、エージェントをインストールする必要があります。</String>
   <String Id="MsgUNCRedirectionInstalled">UNC リダイレクトを有効にして、[ProductName] をインストールしようとしていますが、Horizon Client によって UNC リダイレクトはすでに有効になっています。これはサポートされておらず、続行すると UNC リダイレクトなしでエージェントがインストールされます。エージェント モードで UNC リダイレクトを可能にするには、最初にクライアントをアンインストールしてから、エージェントをインストールする必要があります。</String>
   <String Id="MsgVdmLoopbackIp">「localhost」の IP アドレスの確立を試行したときにエラーが発生しました。IP プロトコルが選択されていることと、プロトコルがこのマシンにインストールされていることを確認してください。</String>
   <String Id="MsgWindowsUpdateInProgress">Windows Update が現在進行中です。Windows Update を完了させ、システムを再起動してから Horizon Agent をインストールしてください。</String>
   <String Id="MsgWindowsUpdateAndRestartPending">インストールを続行する前に、システムを更新して再起動する必要があります。</String>
   <String Id="NoRepairAllowed">アクティブな Horizon セッションが実行中です。[ProductName] の修復を続行できません。</String>
   <String Id="MsgInstallationAbortifSVIInstalled">このインストーラは、既存の製品インストールからのアップグレードを実行できません。Horizon View Composer 機能はバージョン 8.1 からサポート対象外になりました。このビルドをインストールする前に、以前のビルドをアンインストールする必要があります。</String>
   <String Id="SettingsFileInvalid">インストーラ設定ファイル「[SETTINGS_FILE]」の解析に失敗しました

行 [SettingsFileErrorLine] にエラーがあります。</String>


   <!-- Action Text Strings -->
   <String Id="ActionText_RdpConfig">RDP 構成を行っています</String>
   <String Id="ConfigUserInit">UserInit プロセス: wssm.exe を登録しています</String>
   <String Id="IDS_ACTIONTEXT_1">[1]</String>
   <String Id="IDS_ACTIONTEXT_1b">[1]</String>
   <String Id="IDS_ACTIONTEXT_1c">[1]</String>
   <String Id="IDS_ACTIONTEXT_1d">[1]</String>
   <String Id="IDS_ACTIONTEXT_Advertising">アプリケーションを設定しています</String>
   <String Id="IDS_ACTIONTEXT_AllocatingRegistry">レジストリ領域を割り当てています</String>
   <String Id="IDS_ACTIONTEXT_AppCommandLine">アプリケーション: [1]、コマンド ライン: [2]</String>
   <String Id="IDS_ACTIONTEXT_AppId">AppId: [1]{{、AppType: [2]}}</String>
   <String Id="IDS_ACTIONTEXT_AppIdAppTypeRSN">AppId: [1]{{、AppType: [2]、Users: [3]、RSN: [4]}}</String>
   <String Id="IDS_ACTIONTEXT_Application">アプリケーション: [1]</String>
   <String Id="IDS_ACTIONTEXT_BindingExes">実行ファイルをバインドしています</String>
   <String Id="IDS_ACTIONTEXT_ClassId">クラス ID: [1]</String>
   <String Id="IDS_ACTIONTEXT_ClsID">クラス ID: [1]</String>
   <String Id="IDS_ACTIONTEXT_ComponentIDQualifier">コンポーネント ID: [1]、修飾子: [2]</String>
   <String Id="IDS_ACTIONTEXT_ComponentIdQualifier2">コンポーネント ID: [1]、修飾子: [2]</String>
   <String Id="IDS_ACTIONTEXT_ComputingSpace">必要な領域を計算しています</String>
   <String Id="IDS_ACTIONTEXT_ComputingSpace2">必要な領域を計算しています</String>
   <String Id="IDS_ACTIONTEXT_ComputingSpace3">必要な領域を計算しています</String>
   <String Id="IDS_ACTIONTEXT_ContentTypeExtension">MIME コンテンツ タイプ: [1]、エクステンション: [2]</String>
   <String Id="IDS_ACTIONTEXT_ContentTypeExtension2">MIME コンテンツ タイプ: [1]、エクステンション: [2]</String>
   <String Id="IDS_ACTIONTEXT_CopyingNetworkFiles">ネットワークにファイルをコピーしています</String>
   <String Id="IDS_ACTIONTEXT_CopyingNewFiles">新しいファイルをコピーしています</String>
   <String Id="IDS_ACTIONTEXT_CreatingDuplicate">ファイルの複製を作成しています</String>
   <String Id="IDS_ACTIONTEXT_CreatingFolders">フォルダを作成しています</String>
   <String Id="IDS_ACTIONTEXT_CreatingShortcuts">ショートカットを作成しています</String>
   <String Id="IDS_ACTIONTEXT_DeletingServices">サービスを削除しています</String>
   <String Id="IDS_ACTIONTEXT_EnvironmentStrings">環境ストリングを更新しています</String>
   <String Id="IDS_ACTIONTEXT_EvaluateLaunchConditions">起動状態を検証しています</String>
   <String Id="IDS_ACTIONTEXT_Extension">エクステンション: [1]</String>
   <String Id="IDS_ACTIONTEXT_Extension2">エクステンション: [1]</String>
   <String Id="IDS_ACTIONTEXT_Feature">機能: [1]</String>
   <String Id="IDS_ACTIONTEXT_FeatureColon">機能: [1]</String>
   <String Id="IDS_ACTIONTEXT_File">ファイル: [1]</String>
   <String Id="IDS_ACTIONTEXT_File2">ファイル: [1]</String>
   <String Id="IDS_ACTIONTEXT_FileDependencies">ファイル: [1]、依存: [2]</String>
   <String Id="IDS_ACTIONTEXT_FileDir">ファイル: [1]、ディレクトリ: [9]</String>
   <String Id="IDS_ACTIONTEXT_FileDir2">ファイル: [1]、ディレクトリ: [9]</String>
   <String Id="IDS_ACTIONTEXT_FileDir3">ファイル: [1]、ディレクトリ: [9]</String>
   <String Id="IDS_ACTIONTEXT_FileDirSize">ファイル: [1]、ディレクトリ: [9]、サイズ: [6]</String>
   <String Id="IDS_ACTIONTEXT_FileDirSize2">ファイル: [1]、ディレクトリ: [9]、サイズ: [6]</String>
   <String Id="IDS_ACTIONTEXT_FileDirSize3">ファイル: [1]、ディレクトリ: [9]、サイズ: [6]</String>
   <String Id="IDS_ACTIONTEXT_FileDirSize4">ファイル: [1]、ディレクトリ: [2]、サイズ: [3]</String>
   <String Id="IDS_ACTIONTEXT_FileDirectorySize">ファイル: [1]、ディレクトリ: [9]、サイズ: [6]</String>
   <String Id="IDS_ACTIONTEXT_FileFolder">ファイル: [1]、フォルダ: [2]</String>
   <String Id="IDS_ACTIONTEXT_FileFolder2">ファイル: [1]、フォルダ: [2]</String>
   <String Id="IDS_ACTIONTEXT_FileSectionKeyValue">ファイル: [1]、セクション: [2]、キー: [3]、値: [4]</String>
   <String Id="IDS_ACTIONTEXT_FileSectionKeyValue2">ファイル: [1]、セクション: [2]、キー: [3]、値: [4]</String>
   <String Id="IDS_ACTIONTEXT_Folder">フォルダ: [1]</String>
   <String Id="IDS_ACTIONTEXT_Folder1">フォルダ: [1]</String>
   <String Id="IDS_ACTIONTEXT_Font">フォント: [1]</String>
   <String Id="IDS_ACTIONTEXT_Font2">フォント: [1]</String>
   <String Id="IDS_ACTIONTEXT_FoundApp">見つかったアプリケーション: [1]</String>
   <String Id="IDS_ACTIONTEXT_FreeSpace">空き領域: [1]</String>
   <String Id="IDS_ACTIONTEXT_GeneratingScript">アクションに対するスクリプト オペレーションを生成しています:</String>
   <String Id="IDS_ACTIONTEXT_InitializeODBCDirs">ODBC ディレクトリを初期化しています</String>
   <String Id="IDS_ACTIONTEXT_InstallODBC">ODBC コンポーネントをインストールしています</String>
   <String Id="IDS_ACTIONTEXT_InstallServices">新しいサービスをインストールしています</String>
   <String Id="IDS_ACTIONTEXT_InstallingSystemCatalog">システム カタログをインストールしています</String>
   <String Id="IDS_ACTIONTEXT_KeyName">キー: [1]、名前: [2]</String>
   <String Id="IDS_ACTIONTEXT_KeyNameValue">キー: [1]、名前: [2]、値: [3]</String>
   <String Id="IDS_ACTIONTEXT_LibId">LibID: [1]</String>
   <String Id="IDS_ACTIONTEXT_Libid2">LibID: [1]</String>
   <String Id="IDS_ACTIONTEXT_MigratingFeatureStates">関連のアプリケーションから機能の状態を移行しています</String>
   <String Id="IDS_ACTIONTEXT_MovingFiles">ファイルを移動しています</String>
   <String Id="IDS_ACTIONTEXT_NameValueAction">名前: [1]、値: [2]、アクション [3]</String>
   <String Id="IDS_ACTIONTEXT_NameValueAction2">名前: [1]、値: [2]、アクション [3]</String>
   <String Id="IDS_ACTIONTEXT_PatchingFiles">ファイルを修正しています</String>
   <String Id="IDS_ACTIONTEXT_ProgID">ProgID: [1]</String>
   <String Id="IDS_ACTIONTEXT_ProgID2">ProgID: [1]</String>
   <String Id="IDS_ACTIONTEXT_PropertySignature">プロパティ: [1]、署名: [2]</String>
   <String Id="IDS_ACTIONTEXT_PublishProductFeatures">製品機能を発行しています</String>
   <String Id="IDS_ACTIONTEXT_PublishProductInfo">製品情報を発行しています</String>
   <String Id="IDS_ACTIONTEXT_PublishingQualifiedComponents">正規のコンポーネントを発行しています</String>
   <String Id="IDS_ACTIONTEXT_RegUser">ユーザーを登録しています</String>
   <String Id="IDS_ACTIONTEXT_RegisterClassServer">クラス サーバを登録しています</String>
   <String Id="IDS_ACTIONTEXT_RegisterExtensionServers">エクステンション サーバを登録しています</String>
   <String Id="IDS_ACTIONTEXT_RegisterFonts">フォントを登録しています</String>
   <String Id="IDS_ACTIONTEXT_RegisterMimeInfo">MIME 情報を登録しています</String>
   <String Id="IDS_ACTIONTEXT_RegisterTypeLibs">タイプ ライブラリを登録しています。</String>
   <String Id="IDS_ACTIONTEXT_RegisteringComPlus">COM+ アプリケーションとコンポーネントを登録しています</String>
   <String Id="IDS_ACTIONTEXT_RegisteringModules">モジュールを登録しています</String>
   <String Id="IDS_ACTIONTEXT_RegisteringProduct">製品を登録しています</String>
   <String Id="IDS_ACTIONTEXT_RegisteringProgIdentifiers">プログラム識別子を登録しています</String>
   <String Id="IDS_ACTIONTEXT_RemoveApps">アプリケーションを削除しています</String>
   <String Id="IDS_ACTIONTEXT_RemovingBackup">バックアップ ファイルを削除しています</String>
   <String Id="IDS_ACTIONTEXT_RemovingDuplicates">重複しているファイルを削除しています</String>
   <String Id="IDS_ACTIONTEXT_RemovingFiles">ファイルを削除しています</String>
   <String Id="IDS_ACTIONTEXT_RemovingFolders">フォルダを削除しています</String>
   <String Id="IDS_ACTIONTEXT_RemovingIni">INI ファイルのエントリを削除しています</String>
   <String Id="IDS_ACTIONTEXT_RemovingMoved">移動したファイルを削除しています</String>
   <String Id="IDS_ACTIONTEXT_RemovingODBC">ODBC コンポーネントを削除しています</String>
   <String Id="IDS_ACTIONTEXT_RemovingRegistry">システム レジストリの値を削除しています</String>
   <String Id="IDS_ACTIONTEXT_RemovingShortcuts">ショートカットを削除しています</String>
   <String Id="IDS_ACTIONTEXT_RollingBack">アクションを元に戻しています:</String>
   <String Id="IDS_ACTIONTEXT_SearchForRelated">関連のアプリケーションを検索しています</String>
   <String Id="IDS_ACTIONTEXT_SearchInstalled">インストールされているアプリケーションを検索しています</String>
   <String Id="IDS_ACTIONTEXT_SearchingQualifyingProducts">正規の製品を検索しています</String>
   <String Id="IDS_ACTIONTEXT_ServerConfig">Horizon Connection Server を構成しています</String>
   <String Id="IDS_ACTIONTEXT_Service">サービス: [1]</String>
   <String Id="IDS_ACTIONTEXT_Service2">サービス: [2]</String>
   <String Id="IDS_ACTIONTEXT_Service3">サービス: [1]</String>
   <String Id="IDS_ACTIONTEXT_Service4">サービス: [1]</String>
   <String Id="IDS_ACTIONTEXT_Shortcut">ショートカット: [1]</String>
   <String Id="IDS_ACTIONTEXT_Shortcut1">ショートカット: [1]</String>
   <String Id="IDS_ACTIONTEXT_StartingServices">サービスを開始しています</String>
   <String Id="IDS_ACTIONTEXT_StoppingServices">サービスを停止しています</String>
   <String Id="IDS_ACTIONTEXT_UnpublishProductFeatures">製品機能の発行を停止しています</String>
   <String Id="IDS_ACTIONTEXT_UnpublishQualified">正規のコンポーネントの発行を停止しています</String>
   <String Id="IDS_ACTIONTEXT_UnpublishingProductInfo">製品情報の発行を停止しています</String>
   <String Id="IDS_ACTIONTEXT_UnregTypeLibs">タイプ ライブラリの登録を解除しています</String>
   <String Id="IDS_ACTIONTEXT_UnregisterClassServers">クラス サーバの登録を解除しています</String>
   <String Id="IDS_ACTIONTEXT_UnregisterExtensionServers">エクステンション サーバの登録を解除しています</String>
   <String Id="IDS_ACTIONTEXT_UnregisterModules">モジュールの登録を解除しています</String>
   <String Id="IDS_ACTIONTEXT_UnregisteringComPlus">COM+ アプリケーションとコンポーネントの登録を解除しています</String>
   <String Id="IDS_ACTIONTEXT_UnregisteringFonts">フォントの登録を解除しています</String>
   <String Id="IDS_ACTIONTEXT_UnregisteringMimeInfo">MIME 情報の登録を解除しています</String>
   <String Id="IDS_ACTIONTEXT_UnregisteringProgramIds">プログラム識別子の登録を解除しています</String>
   <String Id="IDS_ACTIONTEXT_UpdateComponentRegistration">コンポーネントの登録を更新しています</String>
   <String Id="IDS_ACTIONTEXT_UpdateEnvironmentStrings">環境ストリングを更新しています</String>
   <String Id="IDS_ACTIONTEXT_Validating">インストールを検証しています</String>
   <String Id="IDS_ACTIONTEXT_BlastUDPInstall">UDP 通信設定のセットアップ</String>
   <String Id="IDS_ACTIONTEXT_BlastUDPUninstall">UDP 通信設定のクリーンアップ</String>
   <String Id="IDS_ACTIONTEXT_WritingINI">INI ファイルの値を書き込んでいます</String>
   <String Id="IDS_ACTIONTEXT_WritingRegistry">システム レジストリの値を書き込んでいます</String>
   <String Id="UnconfigUserInit">UserInit プロセス: wssm.exe の登録を取り消しています</String>
   <String Id="VM_WaitForpairing_ProgressText">Waiting for agent pairing to complete...</String>

   <!-- UIText Strings -->
   <String Id="IDS_UITEXT_Available">使用可能</String>
   <String Id="IDS_UITEXT_Bytes">バイト</String>
   <String Id="IDS_UITEXT_CompilingFeaturesCost">この機能に対するコストをコンパイルしています...</String>
   <String Id="IDS_UITEXT_Differences">残り</String>
   <String Id="IDS_UITEXT_DiskSize">ディスク サイズ</String>
   <String Id="IDS_UITEXT_FeatureCompletelyRemoved">この機能をすべて削除します。</String>
   <String Id="IDS_UITEXT_FeatureContinueNetwork">この機能を継続してネットワークから実行するようにします。</String>
   <String Id="IDS_UITEXT_FeatureFreeSpace">この機能は、ハードディスク ドライブの [1] を開放します。</String>
   <String Id="IDS_UITEXT_FeatureInstalledCD">この機能、およびすべてのサブ機能を CD から実行するようにインストールします。</String>
   <String Id="IDS_UITEXT_FeatureInstalledCD2">この機能を CD から実行するようにインストールします。</String>
   <String Id="IDS_UITEXT_FeatureInstalledLocal">この機能と全サブ機能をローカルのハードディスク ドライブにインストールします。</String>
   <String Id="IDS_UITEXT_FeatureInstalledLocal2">この機能をローカルのハードディスク ドライブにインストールします。</String>
   <String Id="IDS_UITEXT_FeatureInstalledNetwork">この機能、およびすべてのサブ機能をネットワークから実行するようにインストールします。</String>
   <String Id="IDS_UITEXT_FeatureInstalledNetwork2">この機能をネットワークから実行するようにインストールします。</String>
   <String Id="IDS_UITEXT_FeatureInstalledRequired">要求があった場合に、インストールします。</String>
   <String Id="IDS_UITEXT_FeatureInstalledWhenRequired">要求があった場合に、この機能をインストールするようにセットします。</String>
   <String Id="IDS_UITEXT_FeatureInstalledWhenRequired2">要求があった場合に、この機能をインストールします。</String>
   <String Id="IDS_UITEXT_FeatureLocal">この機能をローカルのハードディスク ドライブにインストールします。</String>
   <String Id="IDS_UITEXT_FeatureLocal2">この機能をローカルのハードディスク ドライブにインストールします。</String>
   <String Id="IDS_UITEXT_FeatureNetwork">この機能をネットワークから実行するようにインストールします。</String>
   <String Id="IDS_UITEXT_FeatureNetwork2">この機能をネットワークから実行できるようにします。</String>
   <String Id="IDS_UITEXT_FeatureNotAvailable">この機能を使用できないようにします。</String>
   <String Id="IDS_UITEXT_FeatureOnCD">この機能を CD から実行するようにインストールします。</String>
   <String Id="IDS_UITEXT_FeatureOnCD2">この機能を CD から実行できるようにします。</String>
   <String Id="IDS_UITEXT_FeatureRemainLocal">この機能は、ローカルのハードディスク ドライブから削除されません。</String>
   <String Id="IDS_UITEXT_FeatureRemoveNetwork">この機能をローカルのハードディスク ドライブから削除しますが、ネットワークからは実行できるようにします。</String>
   <String Id="IDS_UITEXT_FeatureRemovedCD">この機能をローカルのハードディスク ドライブから削除しますが、CD からは実行できるようにします。</String>
   <String Id="IDS_UITEXT_FeatureRemovedUnlessRequired">この機能をローカルのハードディスク ドライブから削除して、要求があった場合にインストールするようにセットします。</String>
   <String Id="IDS_UITEXT_FeatureRequiredSpace">この機能をインストールするには、ハード ドライブに [1] が必要です。</String>
   <String Id="IDS_UITEXT_FeatureRunFromCD">この機能を継続して CD から実行するようにします。</String>
   <String Id="IDS_UITEXT_FeatureSpaceFree">この機能は、ハードディスク ドライブの [1] を開放します。[2] / [3] のサブ機能が選択されています。サブ機能は、[4] を開放します。</String>
   <String Id="IDS_UITEXT_FeatureSpaceFree2">この機能は、ハードディスク ドライブの [1] を開放します。[2] / [3] のサブ機能が選択されています。サブ機能には、ハード ドライブに [4] が必要です。</String>
   <String Id="IDS_UITEXT_FeatureSpaceFree3">この機能をインストールするには、ハード ドライブに [1] が必要です。[2] / [3] のサブ機能が選択されています。サブ機能は、[4] を開放します。</String>
   <String Id="IDS_UITEXT_FeatureSpaceFree4">この機能をインストールするには、ハード ドライブに [1] が必要です。[2] / [3] のサブ機能が選択されています。サブ機能には、ハード ドライブに [4] が必要です。</String>
   <String Id="IDS_UITEXT_FeatureUnavailable">この機能は、使用できないようになります。</String>
   <String Id="IDS_UITEXT_FeatureUninstallNoNetwork">この機能を完全にアンインストールします。ネットワークから実行できないようになります。</String>
   <String Id="IDS_UITEXT_FeatureWasCD">この機能は CD から実行されましたが、要求があった場合にインストールするようにセットします。</String>
   <String Id="IDS_UITEXT_FeatureWasCDLocal">この機能は CD から実行されましたが、ローカルのハードディスク ドライブにインストールします。</String>
   <String Id="IDS_UITEXT_FeatureWasOnNetworkInstalled">この機能はネットワークから実行されましたが、要求があった場合にインストールするようにセットします。</String>
   <String Id="IDS_UITEXT_FeatureWasOnNetworkLocal">この機能はネットワークから実行されましたが、ローカルのハードディスク ドライブにインストールします。</String>
   <String Id="IDS_UITEXT_FeatureWillBeUninstalled">この機能を完全にアンインストールします。CD から実行できないようになります。</String>
   <String Id="IDS_UITEXT_Folder">フォルダ|フォルダの新規作成</String>
   <String Id="IDS_UITEXT_GB">GB</String>
   <String Id="IDS_UITEXT_KB">KB</String>
   <String Id="IDS_UITEXT_MB">MB</String>
   <String Id="IDS_UITEXT_Required">必要なディスク容量</String>
   <String Id="IDS_UITEXT_TimeRemaining">残り時間: {[1] 分 }[2] 秒</String>
   <String Id="IDS_UITEXT_Volume">ボリューム</String>


   <!-- Error Table Strings -->
   <String Id="IDS_ERROR_0">{{致命的なエラー: }}</String>
   <String Id="IDS_ERROR_1">エラー [1]。</String>
   <String Id="IDS_ERROR_2">警告 [1]。</String>
   <String Id="IDS_ERROR_4">情報 [1]。</String>
   <String Id="IDS_ERROR_5">内部エラー [1]。 [2]{, [3]}{, [4]}</String>
   <String Id="IDS_ERROR_7">{{ディスクがいっぱいです: }}</String>
   <String Id="IDS_ERROR_8">アクション [Time]: [1]. [2]</String>
   <String Id="IDS_ERROR_9">[ProductName]</String>
   <String Id="IDS_ERROR_10">{[2]}{, [3]}{, [4]}</String>
   <String Id="IDS_ERROR_11">メッセージの種類: [1]、引数: [2]</String>
   <String Id="IDS_ERROR_12">=== ログ開始: [Date] [Time] ===</String>
   <String Id="IDS_ERROR_13">=== ログ終了: [Date] [Time] ===</String>
   <String Id="IDS_ERROR_14">アクション開始 [Time]: [1]。</String>
   <String Id="IDS_ERROR_15">アクション終了 [Time]: [1]。戻り値 [2]。</String>
   <String Id="IDS_ERROR_16">残り時間: {[1] 分 }{[2] 秒}</String>
   <String Id="IDS_ERROR_17">メモリ不足です。他のアプリケーションを閉じてから、再度実行してください。</String>
   <String Id="IDS_ERROR_18">インストーラから応答がありません。</String>
   <String Id="IDS_ERROR_19">インストーラは完了前に中断されました。</String>
   <String Id="IDS_ERROR_20">Windows に [ProductName] を設定しています。しばらくお待ちください。</String>
   <String Id="IDS_ERROR_21">必要な情報を集めています...</String>
   <String Id="IDS_ERROR_22">このアプリケーションの古いバージョンを削除しています</String>
   <String Id="IDS_ERROR_23">このアプリケーションの古いバージョンを削除する準備をしています</String>
   <String Id="IDS_ERROR_32">{[ProductName] の}セットアップが正常終了しました。</String>
   <String Id="IDS_ERROR_33">{[ProductName] の}セットアップに失敗しました。</String>
   <String Id="IDS_ERROR_1101">ファイル [2] の読み込みに失敗しました。{{ システム エラー [3]。}} 目的のファイルが存在し、このファイルへのアクセス権を持っているかどうかを確認してください。</String>
   <String Id="IDS_ERROR_1301">ファイル [3] を作成できません。この名前を持つディレクトリはすでに存在しています。インストールをキャンセルして、別の場所にインストールしてみてください。</String>
   <String Id="IDS_ERROR_1302">ディスク [2] を挿入してください。</String>
   <String Id="IDS_ERROR_1303">インストーラにはディレクトリ [2] にアクセスする権限がありません。インストールを継続できません。管理者としてログインするか、またはシステム管理者にお問い合わせください。</String>
   <String Id="IDS_ERROR_1304">ファイル [2] への書き込みに失敗しました。このディレクトリへのアクセス権があるかどうかを確認してください。</String>
   <String Id="IDS_ERROR_1305">ファイル [2] の読み込みに失敗しました。目的のファイルが存在し、このファイルへのアクセス権を持っているかどうかを確認してください。</String>
   <String Id="IDS_ERROR_1306">ファイル [2] は他のアプリケーションで排他的に使用されています。開いているアプリケーションをすべて閉じてから、「再試行」をクリックしてください。</String>
   <String Id="IDS_ERROR_1307">ファイル [2] をインストールするために、十分なディスク容量がありません。ディスク容量を開放してから、「再試行」をクリックしてください。終了する場合は、「キャンセル」をクリックします。</String>
   <String Id="IDS_ERROR_1308">ソース ファイル [2] が見つかりません。目的のファイルが存在し、このファイルへのアクセス権を持っているかどうかを確認してください。</String>
   <String Id="IDS_ERROR_1309">ファイル [3] の読み込みに失敗しました。{{ システム エラー [2]。}} 目的のファイルが存在し、このファイルへのアクセス権を持っているかどうかを確認してください。</String>
   <String Id="IDS_ERROR_1310">ファイル [3] への書き込みに失敗しました。{{ システム エラー [2]。}} このディレクトリへのアクセス権があるかどうかを確認してください。</String>
   <String Id="IDS_ERROR_1311">ソース ファイルが見つかりません {{(cabinet)}}: [2]。目的のファイルが存在し、このファイルへのアクセス権を持っているかどうかを確認してください。</String>
   <String Id="IDS_ERROR_1312">ディレクトリ [2] を作成できません。この名前を持つファイルがすでに存在しています。ファイルの名前を変更するか、ファイルを削除してから、「再試行」をクリックしてください。または「キャンセル」をクリックして、終了してください。</String>
   <String Id="IDS_ERROR_1313">ボリューム [2] は現在使用できません。別のボリュームを選択してください。</String>
   <String Id="IDS_ERROR_1314">指定されたパス [2] は使用できません。</String>
   <String Id="IDS_ERROR_1315">指定されたフォルダ [2] に書き込めません。</String>
   <String Id="IDS_ERROR_1316">ファイル [2] を読み込み中に、ネットワーク エラーが発生しました</String>
   <String Id="IDS_ERROR_1317">ディレクトリ [2] を作成中に、エラーが発生しました。</String>
   <String Id="IDS_ERROR_1318">ディレクトリ [2] を作成中に、ネットワーク エラーが発生しました。</String>
   <String Id="IDS_ERROR_1319">ソース ファイル キャビネット [2] を開いている途中で、ネットワーク エラーが発生しました。</String>
   <String Id="IDS_ERROR_1320">指定されたパスは長すぎます: [2]</String>
   <String Id="IDS_ERROR_1321">インストーラには、ファイル [2] を修正する権限がありません。</String>
   <String Id="IDS_ERROR_1322">パス [2] の一部がシステムで許容されている長さを超えています。</String>
   <String Id="IDS_ERROR_1323">パス [2] に使用されている単語の中に、フォルダでは使用できないものが含まれています。</String>
   <String Id="IDS_ERROR_1324">パス [2] に使用できない文字が含まれています。</String>
   <String Id="IDS_ERROR_1325">[2] はショート ファイル名としては正しくありません。</String>
   <String Id="IDS_ERROR_1326">ファイル セキュリティの取得に失敗しました: [3] GetLastError: [2]</String>
   <String Id="IDS_ERROR_1327">ドライブが正しくありません: [2]</String>
   <String Id="IDS_ERROR_1328">ファイル [2] にパッチを適用するときにエラーが発生しました。おそらく別の方法によって更新されたためです。このパッチで変更することはできません。詳細は、このパッチのベンダーにお問い合わせください。{{システム エラー: [3]}}</String>
   <String Id="IDS_ERROR_1329">キャビネット ファイル [2] がデジタル署名されていないため、必要なファイルをインストールできません。キャビネット ファイルが破損している可能性があります。</String>
   <String Id="IDS_ERROR_1330">キャビネット ファイル [2] のデジタル署名が無効なため、必要なファイルをインストールできません。キャビネット ファイルが破損している可能性があります。{エラー [3] が WinVerifyTrust から返されました。}</String>
   <String Id="IDS_ERROR_1331">[2] ファイルを正常にコピーできませんでした: CRC エラー。</String>
   <String Id="IDS_ERROR_1332">[2] ファイルを正常にパッチできませんでした: CRC エラー。</String>
   <String Id="IDS_ERROR_1333">[2] ファイルを正常にパッチできませんでした: CRC エラー。</String>
   <String Id="IDS_ERROR_1334">ファイル '[2]' がキャビネット ファイル '[3]' で見つからないため、このファイルをインストールできません。ネットワーク エラー、CD-ROM からの読み取りエラー、またはこのパッケージに関する問題の可能性があります。</String>
   <String Id="IDS_ERROR_1335">このインストールに必要なキャビネット ファイル '[2]' が破損していて、使用できません。ネットワーク エラー、CD-ROM からの読み取りエラー、またはこのパッケージに関する問題の可能性があります。</String>
   <String Id="IDS_ERROR_1336">このインストールを完了するために必要な一時ファイルの作成でエラーが発生しました。フォルダ: [3]。システム エラー コード: [2]</String>
   <String Id="IDS_ERROR_1401">キー [2] を作成できませんでした。{{ システム エラー [3]。}} そのキーへの必要なアクセス権を持っていることを確認するか、またはサポート担当者へお問い合わせください。</String>
   <String Id="IDS_ERROR_1402">キー [2] を開くことができませんでした。{{ システム エラー [3]。}} そのキーへの必要なアクセス権を持っていることを確認するか、またはサポート担当者へお問い合わせください。</String>
   <String Id="IDS_ERROR_1403">キー [3] から値 [2] を削除できませんでした。{{ システム エラー [4]。}} そのキーへの必要なアクセス権を持っていることを確認するか、またはサポート担当者へお問い合わせください。</String>
   <String Id="IDS_ERROR_1404">キー [2] を削除できませんでした。{{ システム エラー [3]。}} そのキーへの必要なアクセス権を持っていることを確認するか、またはサポート担当者へお問い合わせください。</String>
   <String Id="IDS_ERROR_1405">キー [3] から値 [2] を読み込めませんでした。{{ システム エラー [4]。}} そのキーへの必要なアクセス権を持っていることを確認するか、またはサポート担当者へお問い合わせください。</String>
   <String Id="IDS_ERROR_1406">値 [2] をキー [3] に書き込めませんでした。{{ システム エラー [4]。}} そのキーへの必要なアクセス権を持っていることを確認するか、またはサポート担当者へお問い合わせください。</String>
   <String Id="IDS_ERROR_1407">キー [2] に対する値の名前を得ることができませんでした。{{ システム エラー [3]。}} そのキーへの必要なアクセス権を持っていることを確認するか、またはサポート担当者へお問い合わせください。</String>
   <String Id="IDS_ERROR_1408">キー [2] に対するサブ キー名を得ることができませんでした。{{ システム エラー [3]。}} そのキーへの必要なアクセス権を持っていることを確認するか、またはサポート担当者へお問い合わせください。</String>
   <String Id="IDS_ERROR_1409">キー [2] に対するセキュリティ情報が読めませんでした。{{ システム エラー [3]。}} そのキーへの必要なアクセス権を持っていることを確認するか、またはサポート担当者へお問い合わせください。</String>
   <String Id="IDS_ERROR_1410">使用可能なレジストリ領域を増やせませんでした。このアプリケーションをインストールするには、[2]KB のレジストリ領域が必要です。</String>
   <String Id="IDS_ERROR_1500">別のインストールが現在行われています。別のインストールが完了しなければ、新たにインストールを行うことはできません。</String>
   <String Id="IDS_ERROR_1501">保護されているデータへのアクセスでエラーが発生しました。Windows Installer が正しく構成されていることを確認してから、再度インストールを実行してください。</String>
   <String Id="IDS_ERROR_1502">ユーザー [2] によって、すでに製品 [3] のインストールが開始されています。このユーザーは、この製品を使う前に再度インストールを実行する必要があります。現在のインストールはこのまま継続されます。</String>
   <String Id="IDS_ERROR_1503">ユーザー [2] によって、すでに [3] のインストールが開始されています。このユーザーは、この製品を使う前に再度インストールを実行する必要があります。</String>
   <String Id="IDS_ERROR_1601">ディスク容量が足りません ボリューム: [2]; 必要な領域: [3]KB; 使用可能な領域: [4]KB。ディスク容量を増やしてから、再度実行してください。</String>
   <String Id="IDS_ERROR_1602">キャンセルしますか。</String>
   <String Id="IDS_ERROR_1603">ファイル [2][3] は次のプロセスより使用されています。{プロセス名: [4]、プロセス ID: [5]、ウィンドウ タイトル: [6]}。このアプリケーションを終了してから、再度実行してください。</String>
   <String Id="IDS_ERROR_1604">[2] がすでにインストールされているため、この製品のインストールを妨げています。この 2 つの製品を 1 台のコンピュータに同時に存在させることはできません。</String>
   <String Id="IDS_ERROR_1605">ディスク容量が足りません -- ボリューム: [2]、必要な領域: [3] KB、空き領域: [4] KB。ロールバックが無効であれば、空き領域は十分です。「中断」をクリックして終了するか、「再試行」をクリックしてもう一度空き領域を確認するか、「無視」をクリックしてロールバックせずに続行します。</String>
   <String Id="IDS_ERROR_1606">ネットワーク ロケーション [2] へアクセスできませんでした。</String>
   <String Id="IDS_ERROR_1607">インストールを継続するには、次のアプリケーションを閉じる必要があります:</String>
   <String Id="IDS_ERROR_1608">このマシンにこの製品をインストールするのに、前もってインストールされていなければならない製品が見つかりませんでした。</String>
   <String Id="IDS_ERROR_1609">セキュリティ設定の適用中にエラーが発生しました。[2] は有効なユーザーまたはグループではありません。パッケージに関する問題、またはネットワーク上のドメイン コントローラへの接続に関する問題の可能性があります。ネットワーク接続を確認してから「再試行」をクリックするか、または「キャンセル」をクリックしてインストールを終了してください。ユーザーの SID が見つかりません、システム エラー [3]</String>
   <String Id="IDS_ERROR_1651">管理ユーザーは、ユーザーごとに管理された、またはマシンごとの、アドバタイズ状態にあるアプリケーションに、パッチを適用できませんでした。</String>
   <String Id="IDS_ERROR_1701">キー [2] は正しくありません。正しいキーが入力されているかどうかを確認してください。</String>
   <String Id="IDS_ERROR_1702">[2] の設定を継続する前に、システムを再起動する必要があります。すぐに再起動する場合は「はい」を、後で手動で再起動する場合は「いいえ」をクリックしてください。</String>
   <String Id="IDS_ERROR_1703">[2] に対する設定変更を有効にするには、システムを再起動する必要があります。すぐに再起動する場合は「はい」を、あとで手動で再起動する場合は「いいえ」をクリックしてください。</String>
   <String Id="IDS_ERROR_1704">[2] のインストールは現在中断されています。このまま継続するには、インストールによって行われた変更を取り消す必要があります。これらの変更を取り消しますか。</String>
   <String Id="IDS_ERROR_1705">この製品の前のインストールは現在進行中です。このまま継続するには、インストールによって行われた変更を取り消す必要があります。これらの変更を取り消しますか。</String>
   <String Id="IDS_ERROR_1706">製品 [2] に対する有効なソースが見つかりませんでした。Windows Installer を継続できません。</String>
   <String Id="IDS_ERROR_1707">インストールは正常に終了しました。</String>
   <String Id="IDS_ERROR_1708">インストールは失敗しました。</String>
   <String Id="IDS_ERROR_1709">製品: [2] -- [3]</String>
   <String Id="IDS_ERROR_1710">コンピュータを直前の状態に復元するか、後でインストールを継続することができます。復元しますか。</String>
   <String Id="IDS_ERROR_1711">インストール情報をディスクに書き込み中にエラーが発生しました。十分なディスク容量があることを確認してから、「再試行」をクリックして継続するか、または「キャンセル」をクリックしてインストールを中止してください。</String>
   <String Id="IDS_ERROR_1712">お使いのコンピュータを直前の状態に復元するために必要なファイルの一部が見つかりません。復元することはできません。</String>
   <String Id="IDS_ERROR_1713">[2] は要求された製品の 1 つをインストールできません。技術サポート グループにお問い合わせください。{{システム エラー: [3]。}}</String>
   <String Id="IDS_ERROR_1714">[2] の古いバージョンを削除することはできません。技術サポート グループにお問い合わせください。{{システム エラー: [3]。}}</String>
   <String Id="IDS_ERROR_1715">[2] をインストールしました。</String>
   <String Id="IDS_ERROR_1716">[2] を構成しました。</String>
   <String Id="IDS_ERROR_1717">[2] を削除しました。</String>
   <String Id="IDS_ERROR_1718">ファイル [2] はデジタル署名ポリシーによって拒否されました。</String>
   <String Id="IDS_ERROR_1719">Windows Installer サービスにアクセスできませんでした。サポート担当者に連絡し、サービスが適切に登録されて有効になっていることを確認してください。</String>
   <String Id="IDS_ERROR_1720">この Windows Installer パッケージには問題があります。このインストールの完了に必要なスクリプトを実行できませんでした。サポート担当者またはパッケージ ベンダーに問い合わせてください。カスタム アクション [2] のスクリプト エラー [3]、[4]: [5] 行 [6]、列 [7]、[8]</String>
   <String Id="IDS_ERROR_1721">この Windows Installer パッケージには問題があります。このインストールの完了に必要なプログラムを実行できませんでした。サポート担当者またはパッケージ ベンダーに問い合わせてください。アクション: [2]、場所: [3]、コマンド: [4]</String>
   <String Id="IDS_ERROR_1722">この Windows Installer パッケージには問題があります。セットアップの一部として実行されたプログラムが、意図したとおりに終了しませんでした。サポート担当者またはパッケージ ベンダーに問い合わせてください。アクション [2]、場所: [3]、コマンド: [4]</String>
   <String Id="IDS_ERROR_1723">この Windows Installer パッケージには問題があります。このインストールの完了に必要な DLL を実行できませんでした。サポート担当者またはパッケージ ベンダーに問い合わせてください。アクション [2]、エントリ: [3]、ライブラリ: [4]</String>
   <String Id="IDS_ERROR_1724">削除が正常に完了しました。</String>
   <String Id="IDS_ERROR_1725">削除が失敗しました。</String>
   <String Id="IDS_ERROR_1726">アドバタイズが正常に完了しました。</String>
   <String Id="IDS_ERROR_1727">アドバタイズが失敗しました。</String>
   <String Id="IDS_ERROR_1728">構成が正常に完了しました。</String>
   <String Id="IDS_ERROR_1729">構成が失敗しました。</String>
   <String Id="IDS_ERROR_1730">このアプリケーションを削除できるのは管理者だけです。このアプリケーションを削除するには、管理者としてログオンするか、または技術サポート グループに連絡してください。</String>
   <String Id="IDS_ERROR_1731">製品 [2] のソース インストール パッケージが、クライアント パッケージと同期していません。インストール パッケージ '[3]' の有効なコピーを使用してインストールを再度実行してください。</String>
   <String Id="IDS_ERROR_1732">[2] のインストールを完了するには、コンピュータを再起動する必要があります。現在他のユーザーがこのコンピュータにログオンしているので、再起動を行うと他のユーザーの作業結果が失われる可能性があります。今すぐ再起動しますか。</String>
   <String Id="IDS_ERROR_1801">パス [2] が正しくありません。正しいパスを指定してください。</String>
   <String Id="IDS_ERROR_1802">メモリ不足です。他のアプリケーションを閉じてから、再度実行してください。</String>
   <String Id="IDS_ERROR_1803">ドライブ [2] にはディスクがありません。ディスクを挿入してから「再試行」をクリックしてください。以前に選択されたボリュームに戻るには、「キャンセル」をクリックしてください。</String>
   <String Id="IDS_ERROR_1804">ドライブ [2] にはディスクがありません。ディスクを挿入してから「再試行」をクリックしてください。「検索」ダイアログ ボックスに戻って、別のボリュームを選択する場合は、「キャンセル」をクリックしてください。</String>
   <String Id="IDS_ERROR_1805">フォルダ [2] は存在しません。既存のフォルダへのパスを入力してください。</String>
   <String Id="IDS_ERROR_1806">このフォルダを読み込むには、アクセス権が不十分です。</String>
   <String Id="IDS_ERROR_1807">正しいインストール先のフォルダが確定できません。</String>
   <String Id="IDS_ERROR_1901">ソース インストール データベース [2] からの読み込みに失敗しました。</String>
   <String Id="IDS_ERROR_1902">再起動操作のスケジューリング: ファイル [2] の名前を [3] に変更。操作を完了するには、再起動しなければなりません。</String>
   <String Id="IDS_ERROR_1903">再起動操作のスケジューリング: ファイル [2] の削除。操作を完了するには、再起動しなければなりません。</String>
   <String Id="IDS_ERROR_1904">モジュール [2] の登録に失敗しました。HRESULT [3]。サポート担当者へお問い合わせください。</String>
   <String Id="IDS_ERROR_1905">モジュール [2] の登録解除に失敗しました。HRESULT [3]。サポート担当者へお問い合わせください。</String>
   <String Id="IDS_ERROR_1906">パッケージ [2] のキャッシュに失敗しました。エラー: [3]。サポート担当者へお問い合わせください。</String>
   <String Id="IDS_ERROR_1907">フォント [2] を登録できませんでした。フォントをインストールする権利があり、システムがこのフォントをサポートしていることを確認してください。</String>
   <String Id="IDS_ERROR_1908">フォント [2] の登録を取り消すことができませんでした。フォントを削除する権利があることを確認してください。</String>
   <String Id="IDS_ERROR_1909">ショートカット [2] を作成できませんでした。目的のフォルダが存在し、このフォルダへのアクセス権を持っているかどうかを確認してください。</String>
   <String Id="IDS_ERROR_1910">ショートカット [2] を削除できません。ショートカット ファイルが存在するか、またこのファイルへのアクセス権を持っているかどうかを確認してください。</String>
   <String Id="IDS_ERROR_1911">ファイル [2] にタイプ ライブラリを登録できませんでした。サポート担当者へお問い合わせください。</String>
   <String Id="IDS_ERROR_1912">ファイル [2] のタイプ ライブラリの登録を取り消すことができませんでした。サポート担当者へお問い合わせください。</String>
   <String Id="IDS_ERROR_1913">INI ファイル [2][3] を更新できませんでした。目的のファイルが存在し、このファイルへのアクセス権を持っているかどうかを確認してください。</String>
   <String Id="IDS_ERROR_1914">再起動するときに、ファイル [3] を [2] で置き換えるようにスケジュールすることができませんでした。ファイル [3] への書き込み権を持っていることを確認してください。</String>
   <String Id="IDS_ERROR_1915">ODBC ドライバ マネージャの削除でエラーが発生しました。ODBC エラー [2]: [3]。サポート担当者へお問い合わせください。</String>
   <String Id="IDS_ERROR_1916">ODBC ドライバ マネージャのインストールでエラーが発生しました。ODBC エラー [2]: [3]。サポート担当者へお問い合わせください。</String>
   <String Id="IDS_ERROR_1917">ODBC ドライバ [4] の削除でエラーが発生しました。ODBC エラー [2]: [3]。ODBC ドライバを削除する権限を持っていることを確認してください。</String>
   <String Id="IDS_ERROR_1918">ODBC ドライバ [4] のインストールでエラーが発生しました。ODBC エラー [2]: [3]。ファイル [4] が存在し、このファイルへのアクセス権を持っているかどうかを確認してください。</String>
   <String Id="IDS_ERROR_1919">ODBC データ ソース [4] の構成に失敗しました。ODBC エラー [2]: [3]。ファイル [4] が存在し、このファイルへのアクセス権を持っているかどうかを確認してください。</String>
   <String Id="IDS_ERROR_1920">サービス [2] ([3]) が開始できませんでした。システム サービスを開始する権限を持っていることを確認してください。</String>
   <String Id="IDS_ERROR_1921">サービス [2] ([3]) を停止できませんでした。システム サービスを停止する権限を持っていることを確認してください。</String>
   <String Id="IDS_ERROR_1922">サービス [2] ([3]) を削除できませんでした。システム サービスを削除する権限を持っていることを確認してください。</String>
   <String Id="IDS_ERROR_1923">サービス [2] ([3]) をインストールできませんでした。システム サービスをインストールする権限を持っていることを確認してください。</String>
   <String Id="IDS_ERROR_1924">環境変数 [2] を更新できませんでした。環境変数を修正する権限を持っていることを確認してください。</String>
   <String Id="IDS_ERROR_1925">アクセス権が不十分なため、このマシンのすべてのユーザーが使用できるようにアプリケーションをインストールすることはできません。管理者としてログインし、再度インストールしてください。</String>
   <String Id="IDS_ERROR_1926">ファイル [3] に対するファイル セキュリティを設定できませんでした。エラー: [2]。このファイルに対してセキュリティ許可を変更する権限を持っていることを確認してください。</String>
   <String Id="IDS_ERROR_1927">コンポーネント サービス (COM+ 1.0) は、このコンピュータにインストールされていません。インストールを完了するにはコンポーネント サービスが必要です。コンポーネント サービスは、Windows 2000 で利用できます。</String>
   <String Id="IDS_ERROR_1928">COM+ アプリケーションの登録エラーです。詳しくはサポート担当者へお問い合わせください。</String>
   <String Id="IDS_ERROR_1929">COM+ アプリケーションの登録解除のエラーです。詳しくはサポート担当者へお問い合わせください。</String>
   <String Id="IDS_ERROR_1930">サービス「[2]」([3]) の記述は変更できませんでした。</String>
   <String Id="IDS_ERROR_1931">Windows Installer サービスは、システム ファイル [2] を更新できません。このファイルは Windows によって保護されています。このプログラムを正しく動作させるには、オペレーティング システムをアップデートする必要がある可能性があります。{{パッケージ バージョン: [3]、OS 保護バージョン: [4]}}</String>
   <String Id="IDS_ERROR_1932">Windows Installer サービスでは、保護された Windows ファイル [2] を更新することはできません。{{パッケージ バージョン: [3]、OS 保護バージョン: [4]、SFP エラー: [5]}}</String>
   <String Id="IDS_ERROR_1933">Windows Installer サービスが、1 つまたは複数の保護された Windows ファイルを更新できません。SFP エラー: [2]。保護されているファイルのリスト: [3]</String>
   <String Id="IDS_ERROR_1934">マシンのポリシーにより、ユーザーのインストールが無効にされています。</String>
   <String Id="IDS_ERROR_1935">アセンブリ コンポーネント [2] のインストール中にエラーが発生しました。HRESULT: [3]。{{アセンブリ インターフェイス: [4]、関数: [5]、アセンブリ名: [6]}}</String>
   <String Id="IDS_ERROR_1936">アセンブリ '[6]' のインストール中にエラーが発生しました。アセンブリが、厳密に名前を指定されていないか、または最小のキーの長さで署名されていません。HRESULT: [3]。{{アセンブリ インターフェイス: [4]、関数: [5]、コンポーネント: [2]}}</String>
   <String Id="IDS_ERROR_1937">アセンブリ '[6]' のインストール中にエラーが発生しました。署名またはカタログが検証できないか、または無効です。HRESULT: [3]。{{アセンブリ インターフェイス: [4]、関数: [5]、コンポーネント: [2]}}</String>
   <String Id="IDS_ERROR_1938">アセンブリ '[6]' のインストール中にエラーが発生しました。アセンブリの 1 つ以上のモジュールが見つかりませんでした。HRESULT: [3]。{{アセンブリ インターフェイス: [4]、関数: [5]、コンポーネント: [2]}}</String>
   <String Id="IDS_ERROR_2101">ショートカットはオペレーティング システムでサポートされていません。</String>
   <String Id="IDS_ERROR_2102">無効な .ini アクション: [2]</String>
   <String Id="IDS_ERROR_2103">シェル フォルダ [2] のパスを解決できませんでした。</String>
   <String Id="IDS_ERROR_2104">.ini ファイルの書き込み中: [3]: システム エラー: [2]。</String>
   <String Id="IDS_ERROR_2105">ショートカットの作成 [3] が失敗しました。システム エラー: [2]。</String>
   <String Id="IDS_ERROR_2106">ショートカットの削除 [3] が失敗しました。システム エラー: [2]。</String>
   <String Id="IDS_ERROR_2107">タイプ ライブラリ [2] の登録中にエラー [3] が発生しました。</String>
   <String Id="IDS_ERROR_2108">タイプ ライブラリ [2] の登録解除中にエラー [3] が発生しました。</String>
   <String Id="IDS_ERROR_2109">.ini アクションのセクションがありません。</String>
   <String Id="IDS_ERROR_2110">.ini アクションのキーがありません。</String>
   <String Id="IDS_ERROR_2111">実行中のアプリケーションの検出に失敗し、パフォーマンス データを取得できませんでした。返された登録済み操作: [2]。</String>
   <String Id="IDS_ERROR_2112">実行中のアプリケーションの検出に失敗し、パフォーマンス インデックスを取得できませんでした。返された登録済み操作: [2]。</String>
   <String Id="IDS_ERROR_2113">実行中のアプリケーションの検出に失敗しました。</String>
   <String Id="IDS_ERROR_2200">データベース: [2]。データベース オブジェクトの作成が失敗しました。モード = [3]。</String>
   <String Id="IDS_ERROR_2201">データベース: [2]。メモリ不足のため、初期化に失敗しました。</String>
   <String Id="IDS_ERROR_2202">データベース: [2]。メモリ不足のため、データ アクセスに失敗しました。</String>
   <String Id="IDS_ERROR_2203">データベース: [2]。データベース ファイルを開くことができません。システム エラー [3]。</String>
   <String Id="IDS_ERROR_2204">データベース: [2]。テーブルはすでに存在します: [3]。</String>
   <String Id="IDS_ERROR_2205">データベース: [2]。テーブルが存在しません: [3]。</String>
   <String Id="IDS_ERROR_2206">データベース: [2]。テーブルをドロップできませんでした: [3]。</String>
   <String Id="IDS_ERROR_2207">データベース: [2]。インテントの違反です。</String>
   <String Id="IDS_ERROR_2208">データベース: [2]。Execute のパラメータが不十分です。</String>
   <String Id="IDS_ERROR_2209">データベース: [2]。カーソルの状態が無効です。</String>
   <String Id="IDS_ERROR_2210">データベース: [2]。列 [3] の更新データ型が無効です。</String>
   <String Id="IDS_ERROR_2211">データベース: [2]。データベース テーブル [3] を作成できませんでした。</String>
   <String Id="IDS_ERROR_2212">データベース: [2]。データベースが書き込み可能な状態ではありません。</String>
   <String Id="IDS_ERROR_2213">データベース: [2]。データベース テーブルの保存中にエラーが発生しました。</String>
   <String Id="IDS_ERROR_2214">データベース: [2]。エクスポート ファイルの書き込み中にエラーが発生しました: [3]。</String>
   <String Id="IDS_ERROR_2215">データベース: [2]。インポート ファイルを開くことができません: [3]。</String>
   <String Id="IDS_ERROR_2216">データベース: [2]。インポート ファイルの形式に誤りがあります: [3]、行 [4]。</String>
   <String Id="IDS_ERROR_2217">データベース: [2]。CreateOutputDatabase [3] に対する状態が正しくありません。</String>
   <String Id="IDS_ERROR_2218">データベース: [2]。テーブル名が指定されていません。</String>
   <String Id="IDS_ERROR_2219">データベース: [2]。インストーラのデータベース形式が無効です。</String>
   <String Id="IDS_ERROR_2220">データベース: [2]。行/フィールド データが無効です。</String>
   <String Id="IDS_ERROR_2221">データベース: [2]。インポート ファイルでコード ページが競合しています: [3]。</String>
   <String Id="IDS_ERROR_2222">データベース: [2]。変換またはマージのコード ページ [3] がデータベースのコード ページ [4] と異なります。</String>
   <String Id="IDS_ERROR_2223">データベース: [2]。データベースは同じです。変換は生成されません。</String>
   <String Id="IDS_ERROR_2224">データベース: [2]。GenerateTransform: データベースが破損しています。テーブル: [3]。</String>
   <String Id="IDS_ERROR_2225">データベース: [2]。Transform: 一時テーブルを変換できません。テーブル: [3]。</String>
   <String Id="IDS_ERROR_2226">データベース: [2]。変換が失敗しました。</String>
   <String Id="IDS_ERROR_2227">データベース: [2]。SQL クエリに無効な識別子 '[3]' があります: [4]。</String>
   <String Id="IDS_ERROR_2228">データベース: [2]。SQL クエリに不明なテーブル '[3]' があります: [4]。</String>
   <String Id="IDS_ERROR_2229">データベース: [2]。SQL クエリでテーブル '[3]' をロードできませんでした: [4]。</String>
   <String Id="IDS_ERROR_2230">データベース: [2]。SQL クエリでテーブル '[3]' が反復されています: [4]。</String>
   <String Id="IDS_ERROR_2231">データベース: [2]。SQL クエリに ')' がありません: [3]。</String>
   <String Id="IDS_ERROR_2232">データベース: [2]。SQL クエリに予期しないトークン '[3]' があります: [4]。</String>
   <String Id="IDS_ERROR_2233">データベース: [2]。SQL クエリの SELECT 句に列がありません: [3]。</String>
   <String Id="IDS_ERROR_2234">データベース: [2]。SQL クエリの ORDER BY 句に列がありません: [3]。</String>
   <String Id="IDS_ERROR_2235">データベース: [2]。SQL クエリの列 '[3]' が存在しないか、またはあいまいです: [4]。</String>
   <String Id="IDS_ERROR_2236">データベース: [2]。SQL クエリの演算子 '[3]' が無効です: [4]。</String>
   <String Id="IDS_ERROR_2237">データベース: [2]。クエリ文字列が無効か、またはありません: [3]。</String>
   <String Id="IDS_ERROR_2238">データベース: [2]。SQL クエリに FROM 句がありません: [3]。</String>
   <String Id="IDS_ERROR_2239">データベース: [2]。INSERT SQL ステートメントの値が不十分です。</String>
   <String Id="IDS_ERROR_2240">データベース: [2]。UPDATE SQL ステートメントに更新列がありません。</String>
   <String Id="IDS_ERROR_2241">データベース: [2]。INSERT SQL ステートメントに挿入列がありません。</String>
   <String Id="IDS_ERROR_2242">データベース: [2]。列 '[3]' が繰り返されています。</String>
   <String Id="IDS_ERROR_2243">データベース: [2]。テーブルの作成にプライマリ列が定義されていません。</String>
   <String Id="IDS_ERROR_2244">データベース: [2]。SQL クエリ [4] の型指定子 '[3]' が無効です。</String>
   <String Id="IDS_ERROR_2245">IStorage::Stat がエラー [3] で失敗しました。</String>
   <String Id="IDS_ERROR_2246">データベース: [2]。インストーラの変換形式が無効です。</String>
   <String Id="IDS_ERROR_2247">データベース: [2] 変換ストリームの読み取り/書き込みが失敗しました。</String>
   <String Id="IDS_ERROR_2248">データベース: [2] GenerateTransform/Merge: 基本テーブルの列の型が参照テーブルと一致しません。テーブル: [3]、列番号: [4]。</String>
   <String Id="IDS_ERROR_2249">データベース: [2] GenerateTransform: 基本テーブルに参照テーブルより多くの列があります。テーブル: [3]。</String>
   <String Id="IDS_ERROR_2250">データベース: [2] Transform: 既存の行は追加できません。テーブル: [3]。</String>
   <String Id="IDS_ERROR_2251">データベース: [2] Transform: 存在しない行は削除できません。テーブル: [3]。</String>
   <String Id="IDS_ERROR_2252">データベース: [2] Transform: 既存のテーブルは追加できません。テーブル: [3]。</String>
   <String Id="IDS_ERROR_2253">データベース: [2] Transform: 存在しないテーブルは削除できません。テーブル: [3]。</String>
   <String Id="IDS_ERROR_2254">データベース: [2] Transform: 存在しない行は更新できません。テーブル: [3]。</String>
   <String Id="IDS_ERROR_2255">データベース: [2] Transform: この名前の列はすでに存在します。テーブル: [3]、列: [4]。</String>
   <String Id="IDS_ERROR_2256">データベース: [2] GenerateTransform/Merge: 基本テーブルの主キーの数が参照テーブルと一致しません。テーブル: [3]。</String>
   <String Id="IDS_ERROR_2257">データベース: [2]。読み取り専用のテーブルを変更しようとしています: [3]。</String>
   <String Id="IDS_ERROR_2258">データベース: [2]。パラメータの型が一致しません: [3]。</String>
   <String Id="IDS_ERROR_2259">データベース: [2] テーブルの更新が失敗しました</String>
   <String Id="IDS_ERROR_2260">ストレージの CopyTo が失敗しました。システム エラー: [3]。</String>
   <String Id="IDS_ERROR_2261">ストリーム [2] を削除できませんでした。システム エラー: [3]。</String>
   <String Id="IDS_ERROR_2262">ストリームが存在しません: [2]。システム エラー: [3]。</String>
   <String Id="IDS_ERROR_2263">ストリーム [2] を開けませんでした。システム エラー: [3]。</String>
   <String Id="IDS_ERROR_2264">ストリーム [2] を削除できませんでした。システム エラー: [3]。</String>
   <String Id="IDS_ERROR_2265">ストレージをコミットできませんでした。システム エラー: [3]。</String>
   <String Id="IDS_ERROR_2266">ストレージをロールバックできませんでした。システム エラー: [3]。</String>
   <String Id="IDS_ERROR_2267">ストレージ [2] を削除できませんでした。システム エラー: [3]。</String>
   <String Id="IDS_ERROR_2268">データベース: [2]。Merge: [3] 個のテーブルでマージの競合が報告されました。</String>
   <String Id="IDS_ERROR_2269">データベース: [2]。Merge: 2 つのデータベースの '[3]' テーブルの列数が異なりました。</String>
   <String Id="IDS_ERROR_2270">データベース: [2]。GenerateTransform/Merge: 基本テーブルの列名が参照テーブルと一致しません。テーブル: [3]、列番号: [4]。</String>
   <String Id="IDS_ERROR_2271">変換の SummaryInformation の書き込みが失敗しました。</String>
   <String Id="IDS_ERROR_2272">データベース: [2]。データベースが読み取り専用で開かれているため、MergeDatabase は変更を書き込みません。</String>
   <String Id="IDS_ERROR_2273">データベース: [2]。MergeDatabase: 基本データベースに対する参照が、参照データベースとして渡されました。</String>
   <String Id="IDS_ERROR_2274">データベース: [2]。MergeDatabase: Error テーブルにエラーを書き込めません。定義済みの Error テーブルに Null が許容されない列がある可能性があります。</String>
   <String Id="IDS_ERROR_2275">データベース: [2]。指定された Modify [3] 操作はテーブルの結合に対して無効です。</String>
   <String Id="IDS_ERROR_2276">データベース: [2]。コード ページ [3] はシステムでサポートされていません。</String>
   <String Id="IDS_ERROR_2277">データベース: [2]。テーブル [3] を保存できませんでした。</String>
   <String Id="IDS_ERROR_2278">データベース: [2]。SQL クエリの WHERE 句での式の数の制限 32 を超えました: [3]。</String>
   <String Id="IDS_ERROR_2279">データベース: [2] Transform: 基本テーブル [3] の列数が多すぎます。</String>
   <String Id="IDS_ERROR_2280">データベース: [2]。列 [3] をテーブル [4] に作成できませんでした。</String>
   <String Id="IDS_ERROR_2281">ストリーム [2] の名前を変更できませんでした。システム エラー: [3]。</String>
   <String Id="IDS_ERROR_2282">ストリーム名が無効です [2]。</String>
   <String Id="IDS_ERROR_2302">パッチ通知: [2] バイトがパッチされました。</String>
   <String Id="IDS_ERROR_2303">ボリューム情報の取得中にエラーが発生しました。GetLastError: [2]。</String>
   <String Id="IDS_ERROR_2304">ディスク空き領域の取得中にエラーが発生しました。GetLastError: [2]。ボリューム: [3]。</String>
   <String Id="IDS_ERROR_2305">パッチ スレッドの待機中にエラーが発生しました。GetLastError: [2]。</String>
   <String Id="IDS_ERROR_2306">パッチ アプリケーション用のスレッドを作成できませんでした。GetLastError: [2]。</String>
   <String Id="IDS_ERROR_2307">ソース ファイルのキー名が null です。</String>
   <String Id="IDS_ERROR_2308">宛先ファイル名が null です。</String>
   <String Id="IDS_ERROR_2309">パッチがすでに実行中のときに、ファイル [2] のパッチを試みました。</String>
   <String Id="IDS_ERROR_2310">実行中のパッチがないときに、パッチの続行を試みました。</String>
   <String Id="IDS_ERROR_2315">パス区切り文字がありません: [2]。</String>
   <String Id="IDS_ERROR_2318">ファイルが存在しません: [2]。</String>
   <String Id="IDS_ERROR_2319">ファイルの属性の設定中にエラーが発生しました: [3] GetLastError: [2]。</String>
   <String Id="IDS_ERROR_2320">ファイルが書き込み可能ではありません: [2]。</String>
   <String Id="IDS_ERROR_2321">ファイルの作成中にエラーが発生しました: [2]。</String>
   <String Id="IDS_ERROR_2322">ユーザーがキャンセルしました。</String>
   <String Id="IDS_ERROR_2323">ファイル属性が無効です。</String>
   <String Id="IDS_ERROR_2324">ファイルを開けませんでした: [3] GetLastError: [2]。</String>
   <String Id="IDS_ERROR_2325">ファイルのファイル時刻を取得できませんでした: [3] GetLastError: [2]。</String>
   <String Id="IDS_ERROR_2326">FileToDosDateTime でエラーが発生しました。</String>
   <String Id="IDS_ERROR_2327">ディレクトリを削除できませんでした: [3] GetLastError: [2]。</String>
   <String Id="IDS_ERROR_2328">ファイルのバージョン情報の取得中にエラーが発生しました: [2]。</String>
   <String Id="IDS_ERROR_2329">ファイルの削除中にエラーが発生しました: [3]。GetLastError: [2]。</String>
   <String Id="IDS_ERROR_2330">ファイルの属性の取得中にエラーが発生しました: [3]。GetLastError: [2]。</String>
   <String Id="IDS_ERROR_2331">ライブラリ [2] のロード エラー、またはエントリ ポイント [3] の検索エラー。</String>
   <String Id="IDS_ERROR_2332">ファイルの属性の取得中にエラーが発生しました。GetLastError: [2]。</String>
   <String Id="IDS_ERROR_2333">ファイルの属性の設定中にエラーが発生しました。GetLastError: [2]。</String>
   <String Id="IDS_ERROR_2334">ファイルのファイル時刻をローカル時刻に変換中にエラーが発生しました: [3]。GetLastError: [2]。</String>
   <String Id="IDS_ERROR_2335">パス [2] は [3] の親ではありません。</String>
   <String Id="IDS_ERROR_2336">パス [3] で一時ファイルの作成中にエラーが発生しました。GetLastError: [2]。</String>
   <String Id="IDS_ERROR_2337">ファイル [3] を閉じることができませんでした。GetLastError: [2]。</String>
   <String Id="IDS_ERROR_2338">ファイル [3] のリソースを更新できませんでした。GetLastError: [2]。</String>
   <String Id="IDS_ERROR_2339">ファイル [3] のファイル時刻を設定できませんでした。GetLastError: [2]。</String>
   <String Id="IDS_ERROR_2340">ファイル [3] のリソースを更新できませんでした。リソースがありません。</String>
   <String Id="IDS_ERROR_2341">ファイル [3] のリソースを更新できませんでした。リソースが大きすぎます。</String>
   <String Id="IDS_ERROR_2342">ファイル [3] のリソースを更新できませんでした。GetLastError: [2]。</String>
   <String Id="IDS_ERROR_2343">指定されたパスが空です。</String>
   <String Id="IDS_ERROR_2344">ファイル [2] を検証するために必要なファイル IMAGEHLP.DLL が見つかりませんでした。</String>
   <String Id="IDS_ERROR_2345">[2]: ファイルに有効なチェックサム値が含まれません。</String>
   <String Id="IDS_ERROR_2347">ユーザーが無視しました。</String>
   <String Id="IDS_ERROR_2348">キャビネット ストリームからの読み取り中にエラーが発生しました。</String>
   <String Id="IDS_ERROR_2349">コピーが異なる情報でレジュームしました。</String>
   <String Id="IDS_ERROR_2350">FDI サーバ エラー</String>
   <String Id="IDS_ERROR_2351">ファイル キー '[2]' がキャビネット '[3]' に見つかりませんでした。インストールを続行できません。</String>
   <String Id="IDS_ERROR_2352">キャビネット ファイル サーバを初期化できませんでした。必要なファイル 'CABINET.DLL' がない可能性があります。</String>
   <String Id="IDS_ERROR_2353">キャビネットではありません。</String>
   <String Id="IDS_ERROR_2354">キャビネットを処理できません。</String>
   <String Id="IDS_ERROR_2355">キャビネットが破損しています。</String>
   <String Id="IDS_ERROR_2356">ストリーム [2] でキャビネットを特定できませんでした。</String>
   <String Id="IDS_ERROR_2357">属性を設定できません。</String>
   <String Id="IDS_ERROR_2358">ファイルが使用中かどうか判別できません: [3]。GetLastError: [2]。</String>
   <String Id="IDS_ERROR_2359">ターゲット ファイルを作成できません。ファイルが使用中の可能性があります。</String>
   <String Id="IDS_ERROR_2360">進行状況のティック。</String>
   <String Id="IDS_ERROR_2361">次のキャビネットが必要です。</String>
   <String Id="IDS_ERROR_2362">フォルダが見つかりませんでした: [2]。</String>
   <String Id="IDS_ERROR_2363">フォルダ [2] のサブフォルダを列挙できませんでした。</String>
   <String Id="IDS_ERROR_2364">CreateCopier の呼び出しの列挙定数が正しくありません。</String>
   <String Id="IDS_ERROR_2365">exe ファイル [2] の BindImage に失敗しました。</String>
   <String Id="IDS_ERROR_2366">ユーザーによる失敗です。</String>
   <String Id="IDS_ERROR_2367">ユーザーによる中止です。</String>
   <String Id="IDS_ERROR_2368">ネットワーク リソース情報を取得できませんでした。エラー [2]、ネットワーク パス [3]。拡張エラー: ネットワーク プロバイダ [5]、エラー コード [4]、エラー説明 [6]。</String>
   <String Id="IDS_ERROR_2370">[2] ファイルの CRC チェックサムが無効です。{ヘッダーで示されているチェックサムは [3] ですが、計算された値は [4] です。}</String>
   <String Id="IDS_ERROR_2371">パッチをファイル [2] に適用できませんでした。GetLastError: [3]。</String>
   <String Id="IDS_ERROR_2372">パッチ ファイル [2] は壊れているか、または無効な形式です。ファイル [3] のパッチを試みました。GetLastError: [4]。</String>
   <String Id="IDS_ERROR_2373">ファイル [2] は有効なパッチ ファイルではありません。</String>
   <String Id="IDS_ERROR_2374">ファイル [2] はパッチ ファイル [3] の有効な対象ファイルではありません。</String>
   <String Id="IDS_ERROR_2375">不明なパッチ エラー: [2]。</String>
   <String Id="IDS_ERROR_2376">キャビネットが見つかりません。</String>
   <String Id="IDS_ERROR_2379">ファイルを読み取り用に開こうとしてエラーが発生しました: [3]。GetLastError: [2]。</String>
   <String Id="IDS_ERROR_2380">ファイルを書き込み用に開こうとしてエラーが発生しました: [3]。GetLastError: [2]。</String>
   <String Id="IDS_ERROR_2381">ディレクトリが存在しません: [2]。</String>
   <String Id="IDS_ERROR_2382">ドライブの準備ができていません: [2]。</String>
   <String Id="IDS_ERROR_2401">64 ビットのレジストリ操作が、32 ビットのオペレーティング システムでキー [2] に対して試みられました。</String>
   <String Id="IDS_ERROR_2402">メモリが不足しています。</String>
   <String Id="IDS_ERROR_2501">ロールバック スクリプト列挙子を作成できませんでした。</String>
   <String Id="IDS_ERROR_2502">インストールが実行中ではないときに InstallFinalize を呼び出しました。</String>
   <String Id="IDS_ERROR_2503">進行中とマークされていないときに RunScript を呼び出しました。</String>
   <String Id="IDS_ERROR_2601">プロパティ [2] の値が無効です: '[3]'</String>
   <String Id="IDS_ERROR_2602">[2] テーブルのエントリ '[3]' には、Media テーブルに関連付けられたエントリがありません。</String>
   <String Id="IDS_ERROR_2603">テーブル名 [2] が重複しています。</String>
   <String Id="IDS_ERROR_2604">[2] プロパティが定義されていません。</String>
   <String Id="IDS_ERROR_2605">サーバ [2] が [3] または [4] に見つかりませんでした。</String>
   <String Id="IDS_ERROR_2606">プロパティ [2] の値は有効なフル パスではありません: '[3]'。</String>
   <String Id="IDS_ERROR_2607">Media テーブルが見つからないか、または空です (ファイルのインストールに必要です)。</String>
   <String Id="IDS_ERROR_2608">オブジェクトのセキュリティ記述子を作成できませんでした。エラー: '[2]'。</String>
   <String Id="IDS_ERROR_2609">初期化の前に製品設定の移行を試みました。</String>
   <String Id="IDS_ERROR_2611">ファイル [2] は圧縮に指定されていますが、関連付けられたメディア エントリでキャビネットが指定されていません。</String>
   <String Id="IDS_ERROR_2612">ストリームが '[2]' 列に見つかりません。主キー: '[3]'。</String>
   <String Id="IDS_ERROR_2613">RemoveExistingProducts アクションの順序が正しくありません。</String>
   <String Id="IDS_ERROR_2614">インストール パッケージから IStorage オブジェクトにアクセスできませんでした。</String>
   <String Id="IDS_ERROR_2615">ソースの解決が失敗したため、モジュール [2] の登録解除をスキップしました。</String>
   <String Id="IDS_ERROR_2616">コンパニオン ファイル [2] の親がありません。</String>
   <String Id="IDS_ERROR_2617">共有コンポーネント [2] が Component テーブルに見つかりません。</String>
   <String Id="IDS_ERROR_2618">分離されたアプリケーション コンポーネント [2] が Component テーブルに見つかりません。</String>
   <String Id="IDS_ERROR_2619">分離されたコンポーネント [2]、[3] は同じ機能の一部ではありません。</String>
   <String Id="IDS_ERROR_2620">分離されたアプリケーション コンポーネント [2] のキー ファイルが File テーブルにありません。</String>
   <String Id="IDS_ERROR_2621">ショートカット [2] のリソース DLL またはリソース ID の情報が正しくありません。</String>
   <String Id="IDS_ERROR_2701">機能の深さが、許容されるツリーの深さである [2] レベルを超えています。</String>
   <String Id="IDS_ERROR_2702">Feature テーブルのレコード ([2]) が、Attributes フィールドで存在しない親を参照しています。</String>
   <String Id="IDS_ERROR_2703">ルート ソース パスのプロパティ名が定義されていません: [2]</String>
   <String Id="IDS_ERROR_2704">ルート ディレクトリのプロパティが定義されていません: [2]</String>
   <String Id="IDS_ERROR_2705">無効なテーブル: [2]。ツリーとしてリンクできませんでした。</String>
   <String Id="IDS_ERROR_2706">ソース パスが作成されませんでした。Directory テーブルのエントリ [2] に対するパスが存在しません。</String>
   <String Id="IDS_ERROR_2707">ターゲット パスが作成されませんでした。Directory テーブルのエントリ [2] に対するパスが存在しません。</String>
   <String Id="IDS_ERROR_2708">ファイル テーブルにエントリが見つかりませんでした。</String>
   <String Id="IDS_ERROR_2709">指定されたコンポーネント名 ('[2]') が Component テーブルに見つかりません。</String>
   <String Id="IDS_ERROR_2710">要求された 'Select' 状態は、このコンポーネントに対しては無効です。</String>
   <String Id="IDS_ERROR_2711">指定された機能名 ('[2]') が Feature テーブルに見つかりません。</String>
   <String Id="IDS_ERROR_2712">モードレス ダイアログ: [3]、アクション [2] からの戻り値が無効です。</String>
   <String Id="IDS_ERROR_2713">Null が許容されない列に Null 値があります ('[4]' テーブルの '[3]' 列の '[2]')。</String>
   <String Id="IDS_ERROR_2714">デフォルト フォルダ名の値が無効です: [2]。</String>
   <String Id="IDS_ERROR_2715">指定されたファイル キー ('[2]') は File テーブルに見つかりません。</String>
   <String Id="IDS_ERROR_2716">コンポーネント '[2]' のランダムなサブコンポーネント名を作成できませんでした。</String>
   <String Id="IDS_ERROR_2717">アクション条件が正しくないか、またはカスタム アクション '[2]' の呼び出し中にエラーが発生しました。</String>
   <String Id="IDS_ERROR_2718">製品コード '[2]' に対するパッケージ名がありません。</String>
   <String Id="IDS_ERROR_2719">UNC パスまたはドライブ文字パスのどちらもソース '[2]' に見つかりません。</String>
   <String Id="IDS_ERROR_2720">ソース リスト キーを開くときにエラーが発生しました。エラー: '[2]'</String>
   <String Id="IDS_ERROR_2721">カスタム アクション [2] が Binary テーブル ストリームに見つかりません。</String>
   <String Id="IDS_ERROR_2722">カスタム アクション [2] が File テーブルに見つかりません。</String>
   <String Id="IDS_ERROR_2723">カスタム アクション [2] でサポートされていない型が指定されています。</String>
   <String Id="IDS_ERROR_2724">実行元のメディアのボリューム ラベル '[2]' が、Media テーブルで指定されているラベル '[3]' と一致しません。これは、Media テーブルのエントリが 1 つの場合にのみ許可されます。</String>
   <String Id="IDS_ERROR_2725">データベース テーブルが無効です</String>
   <String Id="IDS_ERROR_2726">アクションが見つかりません: [2]。</String>
   <String Id="IDS_ERROR_2727">ディレクトリ エントリ '[2]' が Directory テーブルに存在しません。</String>
   <String Id="IDS_ERROR_2728">テーブル定義のエラーです: [2]</String>
   <String Id="IDS_ERROR_2729">インストール エンジンが初期化されていません。</String>
   <String Id="IDS_ERROR_2730">データベース内の値が正しくありません。テーブル: '[2]'、主キー: '[3]'、列: '[4]'</String>
   <String Id="IDS_ERROR_2731">Selection Manager が初期化されていません。</String>
   <String Id="IDS_ERROR_2732">Directory Manager が初期化されていません。</String>
   <String Id="IDS_ERROR_2733">'[4]' テーブルの '[3]' 列の外部キー ('[2]') が正しくありません。</String>
   <String Id="IDS_ERROR_2734">再インストール モード文字が無効です。</String>
   <String Id="IDS_ERROR_2735">カスタム アクション '[2]' のために処理されない例外が発生し、アクションは停止しました。アクセス違反など、カスタム アクションの内部エラーが原因の可能性があります。</String>
   <String Id="IDS_ERROR_2736">カスタム アクションの一時ファイルの生成が失敗しました: [2]。</String>
   <String Id="IDS_ERROR_2737">カスタム アクション [2]、エントリ [3]、ライブラリ [4] にアクセスできませんでした</String>
   <String Id="IDS_ERROR_2738">カスタム アクション [2] の VBScript ランタイムにアクセスできませんでした。</String>
   <String Id="IDS_ERROR_2739">カスタム アクション [2] の JavaScript ランタイムにアクセスできませんでした。</String>
   <String Id="IDS_ERROR_2740">カスタム アクション [2] のスクリプト エラー [3]、[4]: [5] 行 [6]、列 [7]、[8]。</String>
   <String Id="IDS_ERROR_2741">製品 [2] の構成情報が破損しています。無効な情報: [2]。</String>
   <String Id="IDS_ERROR_2742">サーバへのマーシャリングが失敗しました: [2]。</String>
   <String Id="IDS_ERROR_2743">カスタム アクション [2]、場所: [3]、コマンド: [4] を実行できませんでした。</String>
   <String Id="IDS_ERROR_2744">カスタム アクション [2]、場所: [3]、コマンド: [4] によって呼び出された EXE が失敗しました。</String>
   <String Id="IDS_ERROR_2745">変換 [2] はパッケージ [3] に対して無効です。予期される言語は [4] で、見つかった言語は [5] です。</String>
   <String Id="IDS_ERROR_2746">変換 [2] はパッケージ [3] に対して無効です。予期される製品は [4] で、見つかった製品は [5] です。</String>
   <String Id="IDS_ERROR_2747">変換 [2] はパッケージ [3] に対して無効です。予期される製品バージョンは [4] より前で、見つかった製品バージョンは [5] です。</String>
   <String Id="IDS_ERROR_2748">変換 [2] はパッケージ [3] に対して無効です。予期される製品バージョンは [4] 以前で、見つかった製品バージョンは [5] です。</String>
   <String Id="IDS_ERROR_2749">変換 [2] はパッケージ [3] に対して無効です。予期される製品バージョンは [4] で、見つかった製品バージョンは [5] です。</String>
   <String Id="IDS_ERROR_2750">変換 [2] はパッケージ [3] に対して無効です。予期される製品バージョンは [4] 以降で、見つかった製品バージョンは [5] です。</String>
   <String Id="IDS_ERROR_2751">変換 [2] はパッケージ [3] に対して無効です。予期される製品バージョンは [4] より後で、見つかった製品バージョンは [5] です。</String>
   <String Id="IDS_ERROR_2752">パッケージ [4] の子ストレージとして格納されている変換 [2] を開けませんでした。</String>
   <String Id="IDS_ERROR_2753">ファイル '[2]' はインストール対象に指定されていません。</String>
   <String Id="IDS_ERROR_2754">ファイル '[2]' は有効なパッチ ファイルではありません。</String>
   <String Id="IDS_ERROR_2755">パッケージ [3] のインストール中に、サーバが予期しないエラー [2] を返しました。</String>
   <String Id="IDS_ERROR_2756">プロパティ '[2]' が 1 つ以上のテーブルでディレクトリ プロパティとして使用されていましたが、値が割り当てられていませんでした。</String>
   <String Id="IDS_ERROR_2757">変換 [2] のサマリ情報を作成できませんでした。</String>
   <String Id="IDS_ERROR_2758">変換 [2] には MSI のバージョンが含まれません。</String>
   <String Id="IDS_ERROR_2759">変換 [2] バージョン [3] はエンジンと互換性がありません。最小: [4]、最大: [5]。</String>
   <String Id="IDS_ERROR_2760">変換 [2] はパッケージ [3] に対して無効です。予期されるアップグレード コードは [4] ですが、見つかったコードは [5] です。</String>
   <String Id="IDS_ERROR_2761">トランザクションを開始できません。グローバル ミューテックスが正しく初期化されていません。</String>
   <String Id="IDS_ERROR_2762">スクリプト レコードを書き込めません。トランザクションが開始していません。</String>
   <String Id="IDS_ERROR_2763">スクリプトを実行できません。トランザクションが開始していません。</String>
   <String Id="IDS_ERROR_2765">アセンブリ名が AssemblyName テーブルにありません: コンポーネント: [4]。</String>
   <String Id="IDS_ERROR_2766">ファイル [2] は無効な MSI ストレージ ファイルです。</String>
   <String Id="IDS_ERROR_2767">{[2] の列挙に}これ以上データはありません。</String>
   <String Id="IDS_ERROR_2768">パッチ パッケージの変換が無効です。</String>
   <String Id="IDS_ERROR_2769">カスタム アクション [2] が [3] MSIHANDLE を閉じませんでした。</String>
   <String Id="IDS_ERROR_2770">キャッシュされたフォルダ [2] は、内部キャッシュ フォルダ テーブルで定義されていません。</String>
   <String Id="IDS_ERROR_2771">機能 [2] のアップグレードに足りないコンポーネントがあります。</String>
   <String Id="IDS_ERROR_2772">新しいアップグレード機能 [2] はリーフ機能である必要があります。</String>
   <String Id="IDS_ERROR_2801">不明メッセージ -- 型 [2]。アクションは実行されません。</String>
   <String Id="IDS_ERROR_2802">イベント [2] に対するパブリッシャが見つかりません。</String>
   <String Id="IDS_ERROR_2803">ダイアログ ビューでダイアログ [2] のレコードが見つかりませんでした。</String>
   <String Id="IDS_ERROR_2804">ダイアログ [2] のコントロール [3] のアクティブ化で、CMsiDialog が条件 [3] の評価に失敗しました。</String>
   <String Id="IDS_ERROR_2806">ダイアログ [2] は条件 [3] の評価に失敗しました。</String>
   <String Id="IDS_ERROR_2807">アクション [2] は認識されません。</String>
   <String Id="IDS_ERROR_2808">ダイアログ [2] でのデフォルト ボタンの定義が正しくありません。</String>
   <String Id="IDS_ERROR_2809">ダイアログ [2] で、次のコントロール ポインタがサイクルを形成していません。[3] から [4] へのポインタはありますが、それ以降のポインタがありません。</String>
   <String Id="IDS_ERROR_2810">ダイアログ [2] で、次のコントロール ポインタがサイクルを形成していません。[3] と [5] の両方から [4] へのポインタがあります。</String>
   <String Id="IDS_ERROR_2811">ダイアログ [2] でコントロール [3] がフォーカスを取得する必要がありますが、取得できません。</String>
   <String Id="IDS_ERROR_2812">イベント [2] は認識されません。</String>
   <String Id="IDS_ERROR_2813">EndDialog イベントが引数 [2] を指定して呼び出されましたが、ダイアログには親があります。</String>
   <String Id="IDS_ERROR_2814">ダイアログ [2] で、コントロール [3] が存在しないコントロール [4] を次のコントロールとして指定しています。</String>
   <String Id="IDS_ERROR_2815">ControlCondition テーブルには、ダイアログ [2] に対する条件のない行があります。</String>
   <String Id="IDS_ERROR_2816">EventMapping テーブルは、イベント [3] についてダイアログ [2] の無効なコントロール [4] を参照しています。</String>
   <String Id="IDS_ERROR_2817">イベント [2] でダイアログ [3] のコントロール [4] に対する属性の設定が失敗しました。</String>
   <String Id="IDS_ERROR_2818">ControlEvent テーブルで、EndDialog に認識できない引数 [2] があります。</String>
   <String Id="IDS_ERROR_2819">ダイアログ [2] のコントロール [3] には、リンクされたプロパティが必要です。</String>
   <String Id="IDS_ERROR_2820">すでに初期化されているハンドラの初期化を試みました。</String>
   <String Id="IDS_ERROR_2821">すでに初期化されているダイアログの初期化を試みました: [2]。</String>
   <String Id="IDS_ERROR_2822">すべてのコントロールが追加されるまで、ダイアログ [2] で他のメソッドを呼び出すことはできません。</String>
   <String Id="IDS_ERROR_2823">ダイアログ [2] ですでに初期化されているコントロール [3] の初期化を試みました。</String>
   <String Id="IDS_ERROR_2824">ダイアログの属性 [3] には、少なくとも [2] フィールドのレコードが必要です。</String>
   <String Id="IDS_ERROR_2825">コントロールの属性 [3] には、少なくとも [2] フィールドのレコードが必要です。</String>
   <String Id="IDS_ERROR_2826">ダイアログ [2] のコントロール [3] は、ダイアログ [4] の境界を [5] ピクセル越えています。</String>
   <String Id="IDS_ERROR_2827">ダイアログ [2] のラジオ ボタン グループ [3] のボタン [4] は、グループ [5] の境界を [6] ピクセル越えています。</String>
   <String Id="IDS_ERROR_2828">コントロール [3] をダイアログ [2] から削除しようとしましたが、コントロールはダイアログの一部ではありません。</String>
   <String Id="IDS_ERROR_2829">初期化されていないダイアログを使おうとしています。</String>
   <String Id="IDS_ERROR_2830">ダイアログ [2] の初期化されていないコントロールを使おうとしています。</String>
   <String Id="IDS_ERROR_2831">ダイアログ [2] のコントロール [3] は [5] の属性 [4] をサポートしていません。</String>
   <String Id="IDS_ERROR_2832">ダイアログ [2] は属性 [3] をサポートしていません。</String>
   <String Id="IDS_ERROR_2833">ダイアログ [3] のコントロール [4] はメッセージ [2] を無視しました。</String>
   <String Id="IDS_ERROR_2834">ダイアログ [2] の次のポインタは、単一のループを形成していません。</String>
   <String Id="IDS_ERROR_2835">コントロール [2] はダイアログ [3] に見つかりませんでした。</String>
   <String Id="IDS_ERROR_2836">ダイアログ [2] のコントロール [3] はフォーカスを取得できません。</String>
   <String Id="IDS_ERROR_2837">ダイアログ [2] のコントロール [3] は、winproc が [4] を戻すことを望んでいます。</String>
   <String Id="IDS_ERROR_2838">選択テーブルの項目 [2] は、それ自体を親として持っています。</String>
   <String Id="IDS_ERROR_2839">プロパティ [2] の設定が失敗しました。</String>
   <String Id="IDS_ERROR_2840">エラー ダイアログ名が一致しません。</String>
   <String Id="IDS_ERROR_2841">エラー ダイアログに「OK」ボタンがありませんでした。</String>
   <String Id="IDS_ERROR_2842">エラー ダイアログにテキスト フィールドがありませんでした。</String>
   <String Id="IDS_ERROR_2843">ErrorString 属性は標準ダイアログではサポートされていません。</String>
   <String Id="IDS_ERROR_2844">Errorstring が設定されていない場合、エラー ダイアログを実行できません。</String>
   <String Id="IDS_ERROR_2845">ボタンの幅の合計が、エラー ダイアログのサイズを超えています。</String>
   <String Id="IDS_ERROR_2846">SetFocus はエラー ダイアログで必要なコントロールを発見できませんでした。</String>
   <String Id="IDS_ERROR_2847">ダイアログ [2] のコントロール [3] には、アイコンとビットマップ スタイルの両方が設定されています。</String>
   <String Id="IDS_ERROR_2848">コントロール [3] をダイアログ [2] のデフォルト ボタンとして設定しようとしましたが、このコントロールは存在しません。</String>
   <String Id="IDS_ERROR_2849">ダイアログ [2] のコントロール [3] は、整数値を設定できない型です。</String>
   <String Id="IDS_ERROR_2850">認識できないボリューム タイプです。</String>
   <String Id="IDS_ERROR_2851">アイコン [2] のデータが無効です。</String>
   <String Id="IDS_ERROR_2852">使用する前に、少なくとも 1 つのコントロールをダイアログ [2] に追加する必要があります。</String>
   <String Id="IDS_ERROR_2853">ダイアログ [2] はモードレス ダイアログです。その上で実行メソッドを呼び出すことはできません。</String>
   <String Id="IDS_ERROR_2854">ダイアログ [2] では、コントロール [3] が最初にアクティブになるコントロールとして指定されていますが、そのようなコントロールはありません。</String>
   <String Id="IDS_ERROR_2855">ダイアログ [2] のラジオ ボタン グループ [3] には、2 個以上のボタンがありません。</String>
   <String Id="IDS_ERROR_2856">ダイアログ [2] の第 2 のコピーを作成しています。</String>
   <String Id="IDS_ERROR_2857">ディレクトリ [2] が選択テーブルに指定されていますが、見つかりません。</String>
   <String Id="IDS_ERROR_2858">ビットマップ [2] に対するデータが無効です。</String>
   <String Id="IDS_ERROR_2859">テスト エラー メッセージです。</String>
   <String Id="IDS_ERROR_2860">ダイアログ [2] でのキャンセル ボタンの定義が正しくありません。</String>
   <String Id="IDS_ERROR_2861">ダイアログ [2] コントロール [3] でのラジオ ボタンに対する次のポインタが、サイクルを形成していません。</String>
   <String Id="IDS_ERROR_2862">ダイアログ [2] のコントロール [3] の属性で、有効なアイコン サイズが定義されていません。サイズを 16 に設定します。</String>
   <String Id="IDS_ERROR_2863">ダイアログ [2] のコントロール [3] ではサイズ [5]x[5] のアイコン [4] が必要ですが、そのサイズは使用できません。使用可能な最初のサイズをロードします。</String>
   <String Id="IDS_ERROR_2864">ダイアログ [2] のコントロール [3] がブラウザ イベントを受信しましたが、現在の選択項目には構成可能なディレクトリがありません。ブラウザのボタンが正しく作成されていない可能性があります。</String>
   <String Id="IDS_ERROR_2865">ビルボード [2] のコントロール [3] は、ビルボード [4] の境界を [5] ピクセル越えています。</String>
   <String Id="IDS_ERROR_2866">ダイアログ [2] は、引数 [3] を返すことができません。</String>
   <String Id="IDS_ERROR_2867">エラー ダイアログのプロパティが設定されていません。</String>
   <String Id="IDS_ERROR_2868">エラー ダイアログ [2] ではエラー スタイル ビットが設定されていません。</String>
   <String Id="IDS_ERROR_2869">ダイアログ [2] はエラー スタイル ビットが設定されていますが、エラー ダイアログではありません。</String>
   <String Id="IDS_ERROR_2870">ダイアログ [2] のコントロール [3] のヘルプ文字列 [4] には、区切り文字が含まれません。</String>
   <String Id="IDS_ERROR_2871">[2] テーブルは古くなっています: [3]。</String>
   <String Id="IDS_ERROR_2872">ダイアログ [2] の CheckPath コントロール イベントの引数が無効です。</String>
   <String Id="IDS_ERROR_2873">ダイアログ [2] で、コントロール [3] の文字列長の制限が無効です: [4]。</String>
   <String Id="IDS_ERROR_2874">テキスト フォントの [2] への変更が失敗しました。</String>
   <String Id="IDS_ERROR_2875">テキスト色の [2] への変更が失敗しました。</String>
   <String Id="IDS_ERROR_2876">ダイアログ [2] のコントロール [3] は文字列を切り捨てる必要がありました: [4]。</String>
   <String Id="IDS_ERROR_2877">バイナリ データ [2] が見つかりませんでした</String>
   <String Id="IDS_ERROR_2878">ダイアログ [2] のコントロール [3] には値 [4] を設定できます。これは無効な値または重複する値です。</String>
   <String Id="IDS_ERROR_2879">ダイアログ [2] のコントロール [3] は、マスク文字列 [4] を解析できません。</String>
   <String Id="IDS_ERROR_2880">残りのコントロール イベントを実行しないでください。</String>
   <String Id="IDS_ERROR_2881">CMsiHandler の初期化が失敗しました。</String>
   <String Id="IDS_ERROR_2882">ダイアログ ウィンドウ クラスを登録できませんでした。</String>
   <String Id="IDS_ERROR_2883">ダイアログ [2] に対する CreateNewDialog が失敗しました。</String>
   <String Id="IDS_ERROR_2884">ダイアログ [2] のウィンドウの作成が失敗しました。</String>
   <String Id="IDS_ERROR_2885">ダイアログ [2] でコントロール [3] の作成が失敗しました。</String>
   <String Id="IDS_ERROR_2886">[2] テーブルの作成が失敗しました。</String>
   <String Id="IDS_ERROR_2887">[2] テーブルに対するカーソルの作成が失敗しました。</String>
   <String Id="IDS_ERROR_2888">[2] ビューの実行が失敗しました。</String>
   <String Id="IDS_ERROR_2889">ダイアログ [2] のコントロール [3] に対するウィンドウの作成が失敗しました。</String>
   <String Id="IDS_ERROR_2890">ハンドラが初期化されたダイアログの作成に失敗しました。</String>
   <String Id="IDS_ERROR_2891">ダイアログ [2] のウィンドウを破棄できませんでした。</String>
   <String Id="IDS_ERROR_2892">[2] は整数専用のコントロールです。[3] は有効な整数値ではありません。</String>
   <String Id="IDS_ERROR_2893">ダイアログ [2] のコントロール [3] は、最大で [5] 文字の長さのプロパティ値を受け付けることができます。値 [4] はこの制限を超えているため、切り捨てられました。</String>
   <String Id="IDS_ERROR_2894">RICHED20.DLL のロードが失敗しました。GetLastError() は [2] を返しました。</String>
   <String Id="IDS_ERROR_2895">RICHED20.DLL の解放が失敗しました。GetLastError() は [2] を返しました。</String>
   <String Id="IDS_ERROR_2896">アクション [2] の実行が失敗しました。</String>
   <String Id="IDS_ERROR_2897">このシステムでは [2] フォントを作成できませんでした。</String>
   <String Id="IDS_ERROR_2898">[2] テキスト スタイルに対し、システムは [4] 文字セットの '[3]' フォントを作成しました。</String>
   <String Id="IDS_ERROR_2899">[2] テキスト スタイルを作成できませんでした。GetLastError() は [3] を返しました。</String>
   <String Id="IDS_ERROR_2901">操作 [2] に対するパラメータが無効です。パラメータは [3] です。</String>
   <String Id="IDS_ERROR_2902">操作 [2] が正しくない順序で呼び出されました。</String>
   <String Id="IDS_ERROR_2903">ファイル [2] がありません。</String>
   <String Id="IDS_ERROR_2904">ファイル [2] の BindImage に失敗しました。</String>
   <String Id="IDS_ERROR_2905">スクリプト ファイル [2] からレコードを読み取ることができませんでした。</String>
   <String Id="IDS_ERROR_2906">スクリプト ファイル [2] にヘッダーがありません。</String>
   <String Id="IDS_ERROR_2907">安全なセキュリティ記述子を作成できませんでした。エラー: [2]。</String>
   <String Id="IDS_ERROR_2908">コンポーネント [2] を登録できませんでした。</String>
   <String Id="IDS_ERROR_2909">コンポーネント [2] を登録解除できませんでした。</String>
   <String Id="IDS_ERROR_2910">ユーザーのセキュリティ ID を判別できませんでした。</String>
   <String Id="IDS_ERROR_2911">フォルダ [2] を削除できませんでした。</String>
   <String Id="IDS_ERROR_2912">再起同時のファイル [2] の削除をスケジュールできませんでした。</String>
   <String Id="IDS_ERROR_2919">圧縮されたファイル [2] に対してキャビネットが指定されていません。</String>
   <String Id="IDS_ERROR_2920">ファイル [2] にソース ディレクトリが指定されていません。</String>
   <String Id="IDS_ERROR_2924">スクリプト [2] のバージョンがサポートされていません。スクリプトのバージョン: [3]、最低バージョン: [4]、最高バージョン: [5]。</String>
   <String Id="IDS_ERROR_2927">ShellFolder ID [2] が無効です。</String>
   <String Id="IDS_ERROR_2928">ソースの最大数を超えました。ソース '[2]' をスキップします。</String>
   <String Id="IDS_ERROR_2929">発行ルートを判別できませんでした。エラー: [2]。</String>
   <String Id="IDS_ERROR_2932">スクリプト データからファイル [2] を作成できませんでした。エラー: [3]。</String>
   <String Id="IDS_ERROR_2933">ロールバック スクリプト [2] を初期化できませんでした。</String>
   <String Id="IDS_ERROR_2934">変換 [2] をセキュリティで保護できませんでした。エラー: [3]。</String>
   <String Id="IDS_ERROR_2935">変換 [2] のセキュリティ保護を解除できませんでした。エラー: [3]。</String>
   <String Id="IDS_ERROR_2936">変換 [2] が見つかりませんでした。</String>
   <String Id="IDS_ERROR_2937">Windows Installer はシステム ファイル保護カタログをインストールできません。カタログ: [2]、エラー: [3]。</String>
   <String Id="IDS_ERROR_2938">Windows Installer はキャッシュからシステム ファイル保護カタログを取得できません。カタログ: [2]、エラー: [3]。</String>
   <String Id="IDS_ERROR_2939">Windows Installer はキャッシュからシステム ファイル保護カタログを削除できません。カタログ: [2]、エラー: [3]。</String>
   <String Id="IDS_ERROR_2940">Directory Manager がソース解決に提供されていません。</String>
   <String Id="IDS_ERROR_2941">ファイル [2] の CRC を計算できません。</String>
   <String Id="IDS_ERROR_2942">BindImage アクションが [2] ファイルで実行されていません。</String>
   <String Id="IDS_ERROR_2943">このバージョンの Windows は、64 ビット パッケージの展開をサポートしていません。スクリプト [2] は 64 ビット パッケージ用です。</String>
   <String Id="IDS_ERROR_2944">GetProductAssignmentType が失敗しました。</String>
   <String Id="IDS_ERROR_2945">ComPlus App [2] のインストールがエラー [3] で失敗しました。</String>
   <String Id="IDS_ERROR_3001">このリストのパッチに、正しくないシーケンス情報が含まれています: [2][3][4][5][6][7][8][9][10][11][12][13][14][15][16]。</String>
   <String Id="IDS_ERROR_3002">パッチ [2] に無効なシーケンス情報が含まれています。 </String>
   <String Id="IDS_ERROR_25032">インストーラは LSI ドライバをインストールできませんでした。</String>
   <String Id="IDS_ERROR_25520">[3]\[4] のセキュリティ記述子の作成に失敗しました。システム エラー: [2]</String>
   <String Id="IDS_ERROR_25521">オブジェクト [3] でセキュリティ記述子の設定に失敗しました。システム エラー: [2]</String>
   <String Id="IDS_ERROR_25522">不明なオブジェクト タイプ [3]、システム エラー: [2]</String>
   <String Id="IDS_ERROR_27500">このセットアップには、IIS 仮想ルートを構成するために Internet Information Server 4.0 以降が必要です。IIS 4.0 以降を使用していることを確認してください。</String>
   <String Id="IDS_ERROR_27501">このセットアップには、IIS 仮想ルートを構成するために Administrator (管理者) の権限が必要です。</String>
   <String Id="IDS_ERROR_27502">[2]「[3]」に接続できませんでした。 [4]</String>
   <String Id="IDS_ERROR_27503">[2]「[3]」のバージョン ストリング読み取りエラー。 [4]</String>
   <String Id="IDS_ERROR_27504">SQL バージョン要件が満たされていません: [3]。このインストールには [2] [4] 以降が必要です。</String>
   <String Id="IDS_ERROR_27505">SQL スクリプト ファイル [2] を開くことができませんでした。</String>
   <String Id="IDS_ERROR_27506">SQL スクリプト [2] の実行エラー。[3] 行目。 [4]</String>
   <String Id="IDS_ERROR_27507">データベースサーバを参照する、またはそれに接続するには MDAC のインストールが必要です。</String>
   <String Id="IDS_ERROR_27508">COM+ アプリケーション [2] のインストール中にエラーが発生しました。 [3]</String>
   <String Id="IDS_ERROR_27509">COM+ アプリケーション [2] のアンインストール中にエラーが発生しました。 [3]</String>
   <String Id="IDS_ERROR_27510">COM+ アプリケーション [2] のインストール中にエラーが発生しました。Microsoft(R) .NET クラス ライブラリをロードできませんでした。Microsoft(R) .NET サービス コンポーネントを登録するには、Microsoft(R) .NET Framework がインストールされている必要があります。</String>
   <String Id="IDS_ERROR_27511">SQL スクリプト ファイル [2] を実行できませんでした。接続を開けません: [3]</String>
   <String Id="IDS_ERROR_27512">[2] [3] のトランザクション開始中にエラーが発生しました。データベース [4]。 [5]</String>
   <String Id="IDS_ERROR_27513">[2] [3] のトランザクション実行中にエラーが発生しました。データベース [4]。 [5]</String>
   <String Id="IDS_ERROR_27514">このインストールには、Microsoft SQL Server が必要です。指定したサーバ [3] は Microsoft SQL Server Desktop Engine (MSDE) です。</String>
   <String Id="IDS_ERROR_27515">[2] [3] からスキーマ バージョンを取得中にエラーが発生しました。データベース: '[4]'. [5]</String>
   <String Id="IDS_ERROR_27516">[2] [3] へスキーマ バージョンを書き込み中にエラーが発生しました。データベース: '[4]'. [5]</String>
   <String Id="IDS_ERROR_27517">このインストールでは、COM+ アプリケーションのインストールを実行するために管理者権限が必要です。管理者としてログインし、再度インストールしてください。</String>
   <String Id="IDS_ERROR_27518">COM+ アプリケーション [2] は NT サービスとして実行するように構成されいるため、システム上に COM+ 1.5 以降が必要です。システムには COM+ 1.0 が存在するため、アプリケーションはインストールされません。</String>
   <String Id="IDS_ERROR_27519">XML ファイル [2] の更新中にエラーが発生しました。 [3]</String>
   <String Id="IDS_ERROR_27520">XML ファイル [2] を開く際にエラーが発生しました。 [3]</String>
   <String Id="IDS_ERROR_27521">このセットアップでは、XML ファイルを構成するために MSXML 3.0 以降が必要です。バージョン 3.0 以降があることを確認してください。</String>
   <String Id="IDS_ERROR_27522">XML ファイル [2] を作成中にエラーが発生しました。 [3]</String>
   <String Id="IDS_ERROR_27523">サーバのロード中にエラーが発生しました。</String>
   <String Id="IDS_ERROR_27524">NetApi32.DLL のロード時にエラーが発生しました。ISNetApi.dll には、正常にロードされた NetApi32.DLL が必要です。また、NT ベースのオペレーティング システムが必要です。</String>
   <String Id="IDS_ERROR_27525">サーバが見つかりません。指定したサーバが存在することを確認してください。サーバ名を空白にすることはできません。</String>
   <String Id="IDS_ERROR_27526">ISNetApi.dll からの不特定エラー。</String>
   <String Id="IDS_ERROR_27527">バッファが小さすぎます。</String>
   <String Id="IDS_ERROR_27528">アクセスが拒否されました。管理者権限を確認してください。</String>
   <String Id="IDS_ERROR_27529">無効なコンピュータ。</String>
   <String Id="IDS_ERROR_27530">未定義のスイッチ ケース</String>
   <String Id="IDS_ERROR_27531">処理されなかった例外。</String>
   <String Id="IDS_ERROR_27532">このサーバまたはドメイン用のユーザー名が無効です。</String>
   <String Id="IDS_ERROR_27533">大文字と小文字を区別するパスワードが一致しません。</String>
   <String Id="IDS_ERROR_27534">リストが空です。</String>
   <String Id="IDS_ERROR_27535">アクセス違反。</String>
   <String Id="IDS_ERROR_27536">グループの取得中にエラーが発生しました。</String>
   <String Id="IDS_ERROR_27537">ユーザーをグループへ追加中にエラーが発生しました。このドメインまたはサーバにグループが存在することを確認してください。</String>
   <String Id="IDS_ERROR_27538">ユーザー作成中にエラーが発生しました。</String>
   <String Id="IDS_ERROR_27539">ERROR_NETAPI_ERROR_NOT_PRIMARY が NetAPI から戻りました。</String>
   <String Id="IDS_ERROR_27540">指定したユーザーがすでに存在します。</String>
   <String Id="IDS_ERROR_27541">指定したグループがすでに存在します。</String>
   <String Id="IDS_ERROR_27542">パスワードが無効です。パスワードがネットワークのパスワード ポリシーに準拠していることを確認してください。</String>
   <String Id="IDS_ERROR_27543">無効な名前。</String>
   <String Id="IDS_ERROR_27544">無効なグループ。</String>
   <String Id="IDS_ERROR_27545">ユーザー名は空にできません。また、DOMAIN\Username の形式でなくてはなりません。</String>
   <String Id="IDS_ERROR_27546">ユーザー TEMP ディレクトリに INI ファイルをロード中または作成中にエラーが発生しました。</String>
   <String Id="IDS_ERROR_27547">ISNetAPI.dll がロードされていない、または dll のロード エラーが発生しました。処理を実行するには、この dll のロードが必要です。dll が SUPPORTDIR ディレクトリに存在することを確認してください。</String>
   <String Id="IDS_ERROR_27548">新規ユーザー情報を含む INI ファイルを、ユーザーの TEMP ディレクトリから削除する際にエラーが発生しました。</String>
   <String Id="IDS_ERROR_27549">プライマリ ドメイン コントローラ (PDC) の取得中にエラーが発生しました。</String>
   <String Id="IDS_ERROR_27550">ユーザーを作成するには、すべてのフィールドに値を入力してください。</String>
   <String Id="IDS_ERROR_27551">[2] の ODBC ドライバが見つかりません。これは [2] データベース サーバへの接続に必要です。</String>
   <String Id="IDS_ERROR_27552">データベース [4] の作成の際に、エラーが発生しました。サーバ: [2] [3]. [5]</String>
   <String Id="IDS_ERROR_27553">データベース [4] への接続の際に、エラーが発生しました。サーバ: [2] [3]. [5]</String>
   <String Id="IDS_ERROR_27554">接続 [2] を開こうとして、エラーが発生しました。この接続には有効なデータベース メタデータが関連付けられていません。</String>
   <String Id="IDS_ERROR_28030">インストーラが USB ドライバのインストールに失敗しました。正常にインストールするには、マシンを再起動してからこのインストーラを再起動してください。</String>
   <String Id="IDS_ERROR_28033">%s に接続できません。TCP ポート %d での TCP 接続に失敗しました。入力したサーバ名を確認してください。</String>
   <String Id="IDS_ERROR_28034">%s で LDAP に接続できません。入力したサーバ名を確認してください。</String>
   <String Id="IDS_ERROR_28035">認証情報が無効です。指定したサーバの管理者のユーザー名とパスワードを入力してください。</String>
   <String Id="IDS_ERROR_28036">%s で LDAP にバインドできませんでした。%s。</String>
   <String Id="IDS_ERROR_28037">%s で LDAP を更新できませんでした。アクセスが拒否されました。指定したサーバに必要なアクセス権を持つ管理者のユーザー名とパスワードを入力してください。</String>
   <String Id="IDS_ERROR_28038">%s で LDAP を更新できませんでした。スキーマ違反です。指定したサーバが実行しているソフトウェアのバージョンが古く、アップグレードしないとこのエージェントをインストールできない可能性があります。</String>
   <String Id="IDS_ERROR_28039">%s で LDAP を更新できませんでした。%s。</String>
   <String Id="IDS_ERROR_28040">ユーザー名の書式が無効です。</String>
   <String Id="IDS_ERROR_28041">このエージェントは %s ですでに登録されています。このインストールを続行して既存の登録情報を更新しますか。</String>
   <String Id="IDS_ERROR_28042">このエージェントは %s で複数回登録されています。Horizon Administrator を使用して、このマシン用のエントリを削除してから再度インストールしてみてください。</String>
   <String Id="IDS_ERROR_28045">管理者の認証情報を指定する場合は、ユーザー名とパスワードを入力する必要があります。</String>
   <String Id="IDS_ERROR_28046">%s で LDAP から重要なセキュリティ情報を取得できませんでした。指定したサーバで実行しているソフトウェアのバージョンが古く、アップグレードしないとこのエージェントをインストールできない可能性があります。</String>
   <String Id="IDS_ERROR_28053">DLL の登録に失敗しました。詳細については、最新の %TEMP%\vminst*.log ファイルを参照してください。</String>
   <String Id="IDS_ERROR_28060">Intel HECI デバイス ドライバのインストール中にエラーが発生しました。</String>
   <String Id="IDS_ERROR_28062">このコンピュータのエージェントは、すでに仮想マシンとして %s で登録されています。このコンピュータのコンピュータ名を変更するか、または Connection Server で Horizon Administrator を使用して仮想マシン エントリを削除してからこのインストールを再度実行してください。</String>
   <String Id="IDS_ERROR_28065">インストーラは Smartcard Redirector ドライバをインストールできませんでした。</String>
   <String Id="IDS_ERROR_28089">Connection Server %s で、このエージェントを完全にサポートできるソフトウェア バージョンが実行されていません。操作を続行する前に、まず Connection Server をアップグレードする必要があります。アップグレードせずに操作を続行する場合は、このエージェントを後で再インストールして完全な RDS ホスト機能を取得する必要があります。このインストールを続行しますか。</String>
   <String Id="IDS_ERROR_28090">セットアップでデフォルト以外のレジストリの値が検出されました。

HKLM\SYSTEM\CurrentControlSet\Control\TerminalServer\WinStations\RDP-Tcp\MaxInstanceCount

HKLM\SYSTEM\CurrentControlSet\Control\TerminalServer\WinStations\Horizon-PCOIP\MaxInstanceCount

Horizon ではセッション制限が管理されるため、これらのレジストリ設定により予期しない動作が発生する可能性があります。</String>
   <String Id="IDS_ERROR_28092">インストーラが Horizon 仮想 Web カメラ ドライバのインストールに失敗しました。正常にインストールするには、マシンを再起動してからこのインストーラを再起動してください。</String>
   <String Id="IDS_ERROR_28096">USB リダイレクトを安全に使用するために『Horizon セキュリティ』を参照してください。</String>
   <String Id="IDS_ERROR_28100">インストーラによる一部のチェックを無効にして Omnissa Horizon Agent をインストールしています。これは推奨されていないため、サポート対象外のインストールになります。キャンセルし、チェックを有効にしてから、インストーラを再度実行してください。</String>
   <String Id="IDS_ERROR_28109">VC%d.%d nonredist がマシンにインストールされていません。続行すると、Horizon Agent のインストールが失敗する可能性があります。VC%d.%d nonredist のインストーラは、msvc_debug_runtime_installer conan build からダウンロードできます。</String>
   <String Id="IDS_ERROR_28110">Virtualization Pack for Skype for Business では この機能をインストールする前に、.NET Framework 4.0 以降がインストールされている必要があります。この機能をインストールするには、.NET Framework をインストールしてから、インストーラを起動し直してください。</String>
   <String Id="IDS_ERROR_28111">続行するには、機能を追加または削除してください。</String>
   <String Id="IDS_ERROR_28113">修正インストールでは Instant Clone Agent (NGVC) の機能を変更できません。クローン作成機能を追加または削除する場合は、エージェントをアンインストールしてから再インストールしてください。</String>
   <String Id="IDS_ERROR_28114">このインストールを変更または修復するには、管理者権限が必要です。管理者権限のコマンド プロンプトから次のコマンドを実行することもできます。

msiexec.exe /i [DATABASE]</String>
   <String Id="IDS_ERROR_28115">このパッチをインストールするには、管理者権限が必要です。管理者権限のコマンド プロンプトから次のコマンドを実行する必要があります。

msiexec.exe /p [PATCH]</String>

   <String Id="IDS_ERROR_28116">印刷スプーラ サービスが実行されていません。Horizon Integrated Printing 機能がインストールされていない可能性があります。</String>

   <!-- L10n properties for merge module services -->
   <String Id="IDS_PCOIPSG_DISPLAY_NAME">Omnissa Horizon PCoIP Security Gateway</String>
   <String Id="IDS_PCOIPSG_DESCRIPTION">PCoIP ゲートウェイ サービスを提供します。</String>

   <String Id="IDS_WSNM_SERVICE_DISPLAY_NAME">Omnissa Horizon Agent</String>
   <String Id="WsnmServiceDescription">Horizon Agent サービスを提供します。</String>

   <String Id="IDS_WSSH_SERVICE_DISPLAY_NAME">Omnissa Horizon スクリプト ホスト</String>
   <String Id="IDS_WSSH_SERVICE_DESCRIPTION">スクリプト ホスト サービスを提供します。</String>

   <String Id="IDS_VMLM_SERVICE_DISPLAY_NAME">Omnissa Horizon Logon Monitor</String>
   <String Id="IDS_VMLM_SERVICE_DESCRIPTION">ログイン監視サービスを提供します。</String>

   <String Id="IDS_HZMON_SERVICE_DISPLAY_NAME">Omnissa Horizon Monitoring Service</String>
   <String Id="IDS_HZMON_SERVICE_DESCRIPTION">監視サービスを提供します。</String>

   <String Id="IDS_VMWRXG_SERVICE_DISPLAY_NAME">Omnissa Horizon Remote Experience Service</String>
   <String Id="IDS_VMWRXG_SERVICE_DESCRIPTION">リモート エクスペリエンス汎用サービスを提供します。</String>

   <String Id="IDS_AUTORESTART">正常に完了したら、システムを自動的に再起動します</String>

   <String Id="IDS_AUTORESTART_MODIFY">正常に完了したら、必要に応じてシステムを自動的に再起動します</String>

</WixLocalization>
