[ ca ]
default_ca	= CA_default		# The default ca section

[ CA_default ]
dir             = ./
crldir          = $dir/crl
crlnumber       = $dir/crlnumber
crl             = $crldir/crl.pem
#crl_extensions  = crl_ext
default_crl_days= 30
new_certs_dir = $dir/newcerts
database	= $dir/index.txt	# database index file.
default_md	= sha256		# use public key default MD
policy		= policy_match
serial		= $dir/serial 		# The current serial number

[ policy_match ]
countryName		= optional
stateOrProvinceName	= optional
organizationName	= optional
organizationalUnitName	= optional
commonName		= supplied
emailAddress		= optional

[ v3_ca ]
crlDistributionPoints = URI:http://horizon-core-agent-crl1.com:8887/hzagent-1.crl
subjectKeyIdentifier = hash
authorityKeyIdentifier = keyid:always,issuer:always
basicConstraints = critical,CA:true,pathlen:0
keyUsage = critical, digitalSignature, cRLSign, keyCertSign

#[ crl_ext ]
#authorityKeyIdentifier=keyid:always