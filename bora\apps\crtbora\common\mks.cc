/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * crtbora/common/mks.cc -
 *
 */

#include "common.hh"
#include "mks.hh"

#include "cui/core/core.hh"
#include "cui/core/slotutils.hh"
#include "cui/core/text.hh"
#include "cui/core/features.hh"
#ifdef _WIN32
#   include "hznprotect/hznprotect.h"
#endif
#include "module.hh"
#include "rmksProbeMgr.hh"
#include "vm.hh"

#include "vm_product.h"
#include "hznPoll.h"
#include "ScopeGuard.h"
#include "string.h"
#include "preference.h"
#include "productState.h"
#include "hostinfo.h"
#include "random.h"

extern "C" {
#include "vmclientrmks.h"
#include "vdpPlugin.h"
#include "vdpTargetInfo.h"
#include "vdplib.h"
#include "vm_basic_defs.h"

#ifndef _WIN32
#   include <sys/wait.h>
#endif
}

#ifdef _WIN32
#   define UINTPTR_NULL static_cast<uintptr_t>(0)
#else
#   define UINTPTR_NULL static_cast<uintptr_t>(-1)
#endif

#define MAX_RECONNECT_WAIT_TIME_MSEC 8000
#define RECONNECT_EXPIRE_TIME_MSEC 120000 // Reconnect expires in 2 mins.

#define RdeTopology2MKSTopology(monitor, displayInfo)                                              \
   (monitor).left = (displayInfo).rect.left;                                                       \
   (monitor).top = (displayInfo).rect.top;                                                         \
   (monitor).right = (displayInfo).rect.right;                                                     \
   (monitor).bottom = (displayInfo).rect.bottom;                                                   \
   (monitor).dpi = (displayInfo).monitorDPI;

bool
operator==(const VMRect &lhs, // IN:
           const VMRect &rhs) // IN:
{
   // Operator function required by std::find.
   return (lhs.left == rhs.left && lhs.right == rhs.right && lhs.top == rhs.top &&
           lhs.bottom == rhs.bottom);
}


bool
operator==(const RdeChannelDisplayInfo &lhs, // IN:
           const RdeChannelDisplayInfo &rhs) // IN:
{
   // Operator function required by std::find.
   return (lhs.rect == rhs.rect && lhs.bpp == rhs.bpp && lhs.isPrimary == rhs.isPrimary &&
           lhs.monitorDPI == rhs.monitorDPI);
}


namespace crt {
namespace common {


/*
 *-----------------------------------------------------------------------------
 *
 * MKS::MKS --
 *
 *      Constructor.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

MKS::MKS(VM *vm) : MKS(vm, vm->CreateViewControlClient()) {}
MKS::MKS(VM *vm,                                  // IN
         mksctrl::ViewControlClient *viewCtrlCli) // IN
   :
   cui::MKS(viewCtrlCli, "", {}, vm->CreateMKSWindowMgr(viewCtrlCli)),
   mVM(vm),
   mReconnectAttemptCount(0),
   mMKSProcessPID(UINTPTR_NULL),
   mVdpArgs(NULL),
   mConnectionState(csUnset),
   mProtocol(MKS_DISPLAYPROTOCOL_INVALID),
   mUsbdInstanceId(),
   mUsbdDevicesFilterStatus(false),
   mRemoteDpi(0),
   mRemoteDpiVersion(RDE_CHANNEL_DPI_SYNC_SERVER_DEFAULT)
{
   mMKSControlClient->isRemoteConnected.connect(
      sigc::mem_fun(this, &MKS::OnConnectionStateChanged));

   mMKSControlClient->ClientDisconnectRequestStateChanged.connect(
      sigc::mem_fun(this, &MKS::OnClientDisconnectRequestStateChanged));

   mMKSControlClient->remoteSupportsRelativeMouse.changed.connect(
      sigc::mem_fun(this, &MKS::OnRemoteSupportRelativeMouse));

   mMKSControlClient->isRemoteTabletAvailable.changed.connect(
      sigc::mem_fun(this, &MKS::OnRemoteTabletAvailable));


   /*
    * Handle guest to host MKS control messages.
    */
   mMKSControlClient->GetGuestToHostMessageSignal(GHI_CHANNEL_VIEW_REMOTE_RDE_COMMON)
      .connect(sigc::mem_fun(this, &MKS::OnRdeCommonUpdateNotified));

   mMKSControlClient->GetGuestToHostMessageSignal(GHI_CHANNEL_VIEW_USB_REDIRECTION)
      .connect(sigc::mem_fun(this, &MKS::OnUsbRedirectionUpdateNotified));

   mMKSControlClient->GetGuestToHostMessageSignal(GHI_CHANNEL_VIEW_FIDO2_REDIRECTION)
      .connect(sigc::mem_fun(this, &MKS::OnFido2RedirectionUpdateNotified));
   /*
    * MKS control value -> mUsbdInstanceId property -> usbdInstanceIdReceived
    * signal. In this way we can guarantee that the usbdInstanceIdReceived
    * signal is emitted only when the value is really changed, even if the MKS
    * control may signal more than once for the same value.
    */
   mUsbdInstanceId.changed.connect(usbdInstanceIdReceived.make_slot());

   GuestOps *guestOps = dynamic_cast<GuestOps *>(mVM->GetGuestOps());
   guestOps->canSetKeyboardState.changed.connect(sigc::mem_fun(this, &MKS::ApplyPrefs));

   /*
    * Override the various MKS defaults with the ones that we want.
    */
   cui::Modifiers viewModifiers;
   viewModifiers.ctrl = cui::Modifiers::DOWN;
   viewModifiers.shift = cui::Modifiers::UP;
   viewModifiers.alt = cui::Modifiers::DOWN;
   viewModifiers.gui = cui::Modifiers::UP;
   mksKeys = viewModifiers;

   allowKeyboardUngrab = true;
   gamingMouseMode = cui::MOUSE_ABSOLUTE;
   grabOnMouseClick = true;
   grabOnMouseEnter = true;
   hideCursorOnUngrab = true;
   ungrabIfObscured = true;
   ungrabOnMouseLeave = true;
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKS::~MKS --
 *
 *      Destructor.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

MKS::~MKS()
{
   VDPTargetInfo_FreeVDPArgs(mVdpArgs);
   mVdpArgs = NULL;
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKS::GetViewControlClient --
 *
 * Results:
 *      Returns the owned ViewControlClient object of this MKS.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

mksctrl::ViewControlClient *
MKS::GetViewControlClient() const
{
   /*
    * Notice that we created an mksctrl::ViewControlClient object when
    * we called the MKS ctor
    */
   return cui::checked_cast<mksctrl::ViewControlClient *>(GetMKSControlClient());
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKS::GetCmdLine --
 *      Construct command line options to pass to remotemks from
 *      mProperties passed down from the Horizon Client UI.
 *
 *      "properties" is the list of property names and corresponding
 *      bora config names.
 *
 *      "useBool" determines whether the properties are interpreted
 *      as String or Bool values.
 *
 *      Optionally removes Blast-only properties from "vdpPluginProperties"
 *      so that they do not clutter the "mks.vdp.plugin.args" bora config.
 *
 * Results:
 *      Command line options string.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

utf::string
MKS::GetCmdLine(const std::vector<PropertyToBoraConfig> &properties, // IN
                bool useBool,                                        // IN
                PropertyManager *vdpPluginProperties = NULL)         // IN/OUT/OPT
{
   utf::string cmdline;

   /*
    * Iterate through the list of properties with corresponding bora configs.
    */
   for (const auto &p : properties) {
      const char *property = p.first;
      const char *boraConfig = p.second;
      utf::string value;

      if (mProperties.Get(property, &value)) {
         /*
          * The Horizon Client UI has set this property, so pass this
          * value to remotemks as a bora config command line option.
          */

         if (!useBool) {
            if (value.empty()) {
               /* Ignore non-Bool property values that are empty strings */
               continue;
            }

            /*
             * Pass non-Bool values to remotemks as unchanged strings.
             */
            cmdline += cui::Format(" -s %s=%s", boraConfig, value.c_str());
         } else {
            /*
             * Pass Bool values as "TRUE" or "FALSE".  GetBool() treats
             * anything other than (case-insensitive) "TRUE" as false.
             */
            cmdline += cui::Format(" -s %s=%s", boraConfig,
                                   mProperties.GetBool(property) ? "TRUE" : "FALSE");
         }

         /*
          * Reduce remotemks command line clutter by removing properties
          * from mks.vdp.plugin.args which are only used by Blast.
          */
         if (vdpPluginProperties) {
            vdpPluginProperties->Remove(property);
         }
      }
   }

   return cmdline;
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKS::Connect --
 *
 *      Construct a command line to launch remoteMKS.
 *
 *      pluginName: This is the VDP plugin module that remoteMKS will use.
 *      E.g. On windows, it is always be "pcoip_client_win32" for PCoIP.
 *
 *      target: This field is retrieved through broker.
 *      Generally, it has the form of ip:port:tag:public_key.
 *      When running under the standalone mode, it should be always in form of
 *      ip:port.
 *
 *      Pass Horizon Client UI settings to remotemks as command line options.
 *
 * Results:
 *      true if successful.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

bool
MKS::Connect(const utf::string &pluginName, // IN
             const utf::string &target)     // IN
{
   /*
    * "ngp_client" is a special pluginName which indicates to use Blast
    * (aka. the "Next Generation (VNC) Protocol").  Blast is also not
    * implemented as a VDP plugin library, but rather compiled into remotemks.
    */

   mProtocol = pluginName == "ngp_client" ? MKS_DISPLAYPROTOCOL_VNC : MKS_DISPLAYPROTOCOL_VDP;

   utf::string cmdline = cui::Format("-P %d", mProtocol);

   /*
    * The following properties, which may get set by the Horizon Client UI,
    * are only used by Blast.  They are not used by the PCoIP VDP plugin or
    * the Remote Desktop Experience (RDE) virtual channel/VVC plugins.
    *
    * They enable/disable support in the Blast/VNC client for codecs and
    * features such as BlastCodec, H264, HEVC, and HDR by overriding
    * remotemks' bora configs on the remotemks command line.
    *
    * Remotemks' bora configs are not overridden for unset properties.
    */
   std::vector<PropertyToBoraConfig> blastBoolProperties{
      {"allowClientBlastCodec", "RemoteDisplay.allowClientBlastCodec"},
      {"allowClientH264", "RemoteDisplay.allowClientH264"},
      {"allowClientH264YUV444", "RemoteDisplay.allowClientH264YUV444"},
      {"allowClientHEVC", "RemoteDisplay.allowClientHEVC"},
      {"allowClientHEVCYUV444", "RemoteDisplay.allowClientHEVCYUV444"},
      {"allowClientHDR", "RemoteDisplay.allowClientHDR"},
      {"allowClientAV1", "RemoteDisplay.allowClientAV1"},
      {"AllowSplitMksWindow", "RemoteDisplay.splitMksWindow"},
      {"enableEventScroll", "mks.win32.enableEventScroll"},
      {"enableWindowOptimizations", "viewClient.enableWindowOptimizations"}};

   std::vector<PropertyToBoraConfig> blastStringProperties{
      {"decodeCapsProbingMode", "viewClient.decodeCapsProbingMode"},
      {"decoderImageCacheSize", "RemoteDisplay.updateCacheSizeKB"},
      {"MouseMoveMaxLatencyMsec", "RemoteDisplay.mouseMoveMaxLatencyMsec"}};

   /* The following properties might be used in PCoIP or RDE plugins too. */
   std::vector<PropertyToBoraConfig> moreBoolProperties{{"enableUDP", "RemoteDisplay.enableUDP"}};

   PropertyManager vdpPluginProperties(mProperties);
   cmdline += GetCmdLine(blastBoolProperties, /*isBool*/ true, &vdpPluginProperties);
   cmdline += GetCmdLine(blastStringProperties, /*isBool*/ false, &vdpPluginProperties);
   cmdline += GetCmdLine(moreBoolProperties, /*isBool*/ true);

   cmdline += cui::Format(" -s mks.vdp.plugin.name=%s"
                          " -s mks.vdp.plugin.args=%s"
                          " -s RemoteDisplay.loadClipboardPlugin=TRUE"
                          " -s mks.skipHookTimeoutCheck=TRUE",
                          pluginName.c_str(), vdpPluginProperties.ToString().c_str());

   if (ShouldEnableMonochromeCursorConversion()) {
      cmdline += " -s mks.win32.useMonochromeCursor=1";
   }

   /*
    * Check if FIPS is enabled.
    */
   if (mProperties.GetBool("EnableFIPSMode")) {
      cmdline += " -s mks.enableFIPSMode=TRUE";
   }

   if (!mUILanguage.empty()) {
      utf::string uiLang = cui::Format(" -E %s", mUILanguage.c_str());
      cmdline += uiLang;
   }

#ifdef __linux__
   // Apply pulse sound backend by default.
   cmdline += " -s sound.enablePulseAudio=TRUE";
#endif

   utf::string maxLogFiles = cui::Format(" -m %d", GetLogMaxFiles());
   cmdline += maxLogFiles;

   if (!CreateMKS(cmdline, target, mProtocol)) {
      return false;
   }
   return true;
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKS::Reconnect --
 *
 *      Send the new target info to rmks for session reconnection
 *
 * Results:
 *      true if successful.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

bool
MKS::Reconnect(const utf::string &target)
{
   if (mMKSControlClient != NULL) {
      mksctrl::ViewControlClient *viewCtrl =
         dynamic_cast<mksctrl::ViewControlClient *>(GetMKSControlClient());
      if (viewCtrl != nullptr) {
         viewCtrl->SendProtocolRedirectReconnectReq(target.c_str());
         mReconnectAttemptCount = 0;
         Log("%s: Send target info to rmks for reconnect (%p)\n", __FUNCTION__, this);
      } else {
         Log("%s: wrong ViewControlClient!\n", __FUNCTION__);
         return false;
      }
   } else {
      cui::Abort(cui::AbortSlot{});
   }

   return true;
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKS::SendCtrlAltDel --
 *
 *      Override of cui::MKS::SendCtrlAltDel.  We need to send Ctrl+Alt+Insert
 *      instead of Ctrl+Alt+Del (see bug 1078603).
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::SendCtrlAltDel()
{
   SendKeyEvent(MKS_MOD_ALT | MKS_MOD_CONTROL, VSCAN_INSERT);
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKS::SetMouseMode --
 *
 *      Set mouse mode.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::SetMouseMode(bool isRelativeMouseMode)
{
   gamingMouseMode = isRelativeMouseMode ? cui::MOUSE_RELATIVE : cui::MOUSE_ABSOLUTE;
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKS::SendCtrlV --
 *
 *      Send Ctrl+V.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::SendCtrlV()
{
   SendKeyEvent(MKS_MOD_CONTROL, VSCAN_V);
}


/*
 * MKS::GetEnableSplitMKSWindow --
 *
 *    Get whether split MKS Window feature is enalbed.
 *
 * Results:
 *    returns true if the feature is enabled, false otherwise.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

bool
MKS::GetEnableSplitMKSWindow()
{
   return mProperties.GetBool("AllowSplitMksWindow");
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKS::CreateMKS --
 *
 *      Launch remoteMKS and Connect ViewControl.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

bool
MKS::CreateMKS(const utf::string &cmdLine,  // IN
               const utf::string &target,   // IN
               MKSDisplayProtocol protocol) // IN/UNUSED
{
   std::string probeResults = crt::common::RmksProbeMgr::GetProbeResults();
   VDPTargetInfo_SetDecodeCapsProbeResults(mVdpArgs, probeResults.c_str());

   Bool ret;
   utf::string vmHash = mVM->GetVMHash();
   utf::string rmksPath = GetMKSBinaryPath();
   ViewControlAuthToken viewControlToken = {};

   /*
    * Set pref.crtbora.rmksPipeWaitTimeout = "XXXXX"
    * in standard preference lookup locations for example
    * C:\Users\<USER>\AppData\Roaming\VMware\config.ini
    */
   uint32 rmksPipeWaitTimeout =
      Preference_GetLong(RMKS_PIPE_WAIT_MS, "pref.crtbora.rmksPipeWaitTimeout");

   /*
    * Create a random token, which we will pass securely to the RemoteMKS
    * process. When we connect to the ViewControl server, it will expect this
    * same token.
    */
   if (!Random_Crypto(sizeof(viewControlToken.token), viewControlToken.token)) {
      Warning("ViewControl is enabled, but Random_Crypto failed\n");
      return false;
   }

   VDPLib_ConnectMKSArgs args = {rmksPath.c_str(),  cmdLine.c_str(), target.c_str(),
                                 mVdpArgs,          vmHash.c_str(),  rmksPipeWaitTimeout,
                                 &viewControlToken, UINTPTR_NULL,    ""};
   ret = VDPLib_ConnectMKS(&args);
   if (!ret) {
      Warning("VDPLib_CreateMKS failed.\n");
      return false;
   }

   if (args.viewCtrlPipeName[0] == '\0') {
      Warning("crt::common::MKS::CreateMKS: viewControl pipe name is empty.\n");
      return false;
   }

   /*
    * Pending state will be reset to false when OnSetAttachedCompleted
    * is called. It is used to avoid handling disconnect when connecting
    * is still in progress.
    */
   ASSERT(!GetPending());
   SetPending(true);

   mMKSProcessPID = args.mksPID;

   ConnectMKSServer(args.viewCtrlPipeName, &viewControlToken,
                    sigc::mem_fun(this, &MKS::OnMKSControlConnectAbort),
                    sigc::mem_fun(this, &MKS::OnMKSControlConnectDone));
   return true;
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKS::OnMKSControlConnectDone --
 *
 *      Callback when viewControl is connected.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::OnMKSControlConnectDone()
{
   Log("%s: viewControl is ready: doing some stuff\n", __FUNCTION__);
   ASSERT(!mMKSControlChannelCreated);
   mMKSControlChannelCreated =
      mksControlConnectionBroken.connect(sigc::mem_fun(this, &MKS::OnMKSControlCnxBroken));
   OnMKSChannelCreated();
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKS::OnMKSControlConnectAbort --
 *
 *      Callback when viewControl connecting is failed.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::OnMKSControlConnectAbort(bool cancelled,        // IN
                              const cui::Error &err) // IN
{
   Log("%s: viewControl connecting is failed: %s\n", __FUNCTION__, err.what());
   mMKSControlChannelCreated.disconnect();
   SetConnectionState(csDisconnected);
}


void
MKS::OnMKSControlCnxBroken()
{
   Log("%s: viewControl connection is broken.\n", __FUNCTION__);
   ASSERT(mMKSControlChannelCreated);

   SetAttached(false);

   mMKSControlChannelCreated.disconnect();
   SetConnectionState(csDisconnected);
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKS::OnReconnectExpireTimeout --
 *
 *      Callback when the reconnect expires.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::OnReconnectExpireTimeout()
{
   Log("%s: Unable to reconnect after %d seconds, disconnecting.\n", __FUNCTION__,
       RECONNECT_EXPIRE_TIME_MSEC / 1000);
   SetConnectionState(csDisconnected);
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKS::OnReconnectionWaitTimeout --
 *
 *      Callback when the reconnection backoff timer expires,
 *      causing a new (view) reconnection request to be sent to rmks.
 *      Using op/retryVDPConnection/in/reconnectAttemptCount as a signal
 *      to rmks to request for reconnection.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      Will send a signal to rmks to request a reconnection attempt.
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::OnReconnectionWaitTimeout()
{
   if (GetConnectionState() != csPending) {
      Log("%s: Connection state is not pending.\n", __FUNCTION__);
      return;
   }

   if (mMKSControlClient != NULL) {
      mMKSControlClient->SetVDPReconnectAttemptCount(mReconnectAttemptCount);
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKS::StartUsbd --
 *
 *      Tell the MKS to start usbd.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::StartUsbd()
{
   Log("%s: start usbd.\n", __FUNCTION__);
   SendGHIRequest(GHI_CHANNEL_VIEW_USB_REDIRECTION, GHI_HOST_USB_REDIRECTION_STARTUSBD_CMD, nullptr,
                  0, cui::AbortSlot{}, mksctrl::GHIResponseSlot{});
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKS::ProcessRdeCommonDisplayMsg --
 *
 *       Process RdeCommon Display Message when a GHI update is received in channel
 *       RDE_CHANNEL_DISPLAY_MSG.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::ProcessRdeCommonDisplayMsg(const uint8 *msg, // IN
                                uint32 msgLen)    // IN
{
   const RdeChannelMessage *pMsg = reinterpret_cast<const RdeChannelMessage *>(msg);
   uint32 msgType = pMsg->msgType;
   switch (RDE_GET_CHANNEL_INTERNAL_MSG_TYPE(msgType)) {
   case RDE_CHANNEL_DISPLAY_INFO_MSG: {
      RdeChannelDisplaysInfo *pRdeChannelDisplay = (RdeChannelDisplaysInfo *)(pMsg->payload);
      Log("%s: display info is received.\n", __FUNCTION__);
      if (pRdeChannelDisplay->count == 0) {
         Log("%s: received monitor count is 0.\n", __FUNCTION__);
         return;
      }
      if (mPendingDisplayInfos.size() != pRdeChannelDisplay->count) {
         Log("%s: display count is not identical\n", __FUNCTION__);
         return;
      }
      std::vector<RdeChannelDisplayInfo> displayInfos(pRdeChannelDisplay->displayInfos,
                                                      pRdeChannelDisplay->displayInfos +
                                                         pRdeChannelDisplay->count);
      if (!(displayInfos == mPendingDisplayInfos)) {
         Log("%s: display info is not identical\n", __FUNCTION__);
         return;
      }
      std::vector<MKSDisplayTopologyPacket> monitors;
      for (const auto &displayInfo : mPendingDisplayInfos) {
         MKSDisplayTopologyPacket monitor;
         RdeTopology2MKSTopology(monitor, displayInfo);
         monitors.push_back(monitor);
      }
      SetDisplayTopology(monitors);
   } break;
   default:
      break;
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKS::ProcessRdeCommonDpiSyncMsg --
 *
 *       Process RdeCommon DPI sync Message when a GHI update is received in channel
 *       RDE_CHANNEL_DPI_SYNC_MSG.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::ProcessRdeCommonDpiSyncMsg(const uint8 *msg, // IN
                                uint32 msgLen)    // IN
{
   const RdeChannelMessage *pMsg = reinterpret_cast<const RdeChannelMessage *>(msg);
   uint32 msgType = pMsg->msgType;
   switch (RDE_GET_CHANNEL_INTERNAL_MSG_TYPE(msgType)) {
   case RDE_CHANNEL_DPI_SYNC_VERSION_MSG: {
      RdeChannelDPISync *pRdeChannelDPISync = (RdeChannelDPISync *)(pMsg->payload);
      Log("%s: dpi version %d is received.\n", __FUNCTION__, pRdeChannelDPISync->data.version);
      mRemoteDpiVersion = pRdeChannelDPISync->data.version;
      if (mPendingDisplayInfos.size() == 0) {
         Log("%s: no pending topology to be set.\n", __FUNCTION__);
         return;
      }
      if (mRemoteDpiVersion != RDE_CHANNEL_DPI_SYNC_SERVER_DEFAULT) {
         SetDisplayTopology(mPendingDisplayInfos);
      }
   } break;
   default:
      break;
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKS::ProcessRdeCommonIMEMsg --
 *
 *       Process RdeCommon IME Message when a GHI update is received via
 *       RDE_CHANNEL_IME_MSG channel.
 *
 * Results:
 *       None.
 *
 * Side effects:
 *       None.
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::ProcessRdeCommonIMEMsg(const RdeChannelMessage *pMsg) // IN
{
   uint32 payloadSize = pMsg->msgSize - sizeof(RdeChannelMessage) + sizeof(uint8);
   switch (RDE_GET_CHANNEL_INTERNAL_MSG_TYPE(pMsg->msgType)) {
   case RDE_CHANNEL_IME_CARET_POSITION_MSG: {
      VMRect caretPosition = *(reinterpret_cast<const VMRect *>(pMsg->payload));
      VMPoint guestVirtualDesktopOrigin =
         *(reinterpret_cast<const VMPoint *>(pMsg->payload + sizeof(VMRect)));
      Rect_TranslateInv(&mCaretRect, &caretPosition, &guestVirtualDesktopOrigin);
      if (GetVerboseLogging()) {
         Log("%s: receive caret position %d %d %d %d, guestVirtualDesktopOrigin %d %d, "
             "translated caret position %d %d %d %d\n",
             __FUNCTION__, Rect_LTRB(&caretPosition), guestVirtualDesktopOrigin.x,
             guestVirtualDesktopOrigin.y, Rect_LTRB(&mCaretRect));
      }
      caretPositionReceived.emit(mCaretRect);
   } break;
   case RDE_CHANNEL_IME_DATA_MSG:
      ProcessIMEDataMessage((const char *)pMsg->payload, payloadSize);
      break;
   case RDE_CHANNEL_IME_CAPS_MSG: {
      mCanEnableIME = *pMsg->payload;
      mIMECaretImproved = *(pMsg->payload + sizeof(uint8));
      imeCanEnableReceived.emit(mCanEnableIME);
   } break;
   default:
      break;
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKS::ProcessRdeCommonAppProtectionMsg --
 *
 *       Process RdeCommon app protectino Message.
 *
 * Results:
 *       None.
 *
 * Side effects:
 *       None.
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::ProcessRdeCommonAppProtectionMsg(const RdeChannelMessage *pMsg) // IN
{
   std::map<std::string, std::vector<std::byte>> options;
   uint32 offset = 0;
   uint32 payloadSize = pMsg->msgSize - sizeof(RdeChannelMessage) + sizeof(uint8);
   offset++;
   while (offset < payloadSize) {
      uint32 keySize = 0;
      memcpy(&keySize, pMsg->payload + offset, sizeof(uint32));
      offset += sizeof(keySize);

      std::string optionKey((char *)pMsg->payload + offset, keySize);
      offset += keySize;

      uint32 valSize = 0;
      memcpy(&valSize, pMsg->payload + offset, sizeof(uint32));
      offset += sizeof(valSize);

      std::vector<std::byte> optionVal;
      optionVal.resize(valSize);
      memcpy(optionVal.data(), pMsg->payload + offset, valSize);
      offset += valSize;

      options[optionKey] = optionVal;
   }

   if (RDE_GET_CHANNEL_INTERNAL_MSG_TYPE(pMsg->msgType) ==
       RDE_CHANNEL_BLOCK_SCREEN_CAPTURE_ENABLE_MSG) {
      blockScreenCaptureEnabled = *pMsg->payload;
      if (options.contains("allowScreenRecording")) {
         Log("%s, update allow screen recording.\n", __FUNCTION__);
         allowScreenRecordingEnabled = *(uint8 *)(options["allowScreenRecording"].data());
      }
      Log("%s, blocking screen-capture changes to %d, allow screen-recording "
          "changes to %d\n",
          __FUNCTION__, blockScreenCaptureEnabled ? 1 : 0, allowScreenRecordingEnabled ? 1 : 0);
      if (allowScreenRecordingEnabled && !blockScreenCaptureEnabled) {
         Warning("%s: block screen recording could not allow screenshot.\n", __FUNCTION__);
         ASSERT(false);
      }
   } else if (RDE_GET_CHANNEL_INTERNAL_MSG_TYPE(pMsg->msgType) ==
              RDE_CHANNEL_BLOCK_KEY_LOGGER_ENABLE_MSG) {
      if (options.contains("allowArmNoAntiKeylogger")) {
         Log("%s, update allow connection from windows ARM without "
             "AntiKeylogger.\n",
             __FUNCTION__);
         allowArmNoAntiKeyloggerEnabled = *(uint8 *)(options["allowArmNoAntiKeylogger"].data());
      }
#ifdef _WIN32
      if (*pMsg->payload && allowArmNoAntiKeyloggerEnabled && !HznProtect_IsAMD64()) {
         blockKeyLoggerEnabled = false;
      } else {
#endif
         blockKeyLoggerEnabled = *pMsg->payload;
#ifdef _WIN32
      }
#endif
      Log("%s, blocking key-logger changes to %d, allow connection from windows "
          "ARM without AntiKeylogger changes to %d.\n",
          __FUNCTION__, blockKeyLoggerEnabled ? 1 : 0, allowArmNoAntiKeyloggerEnabled ? 1 : 0);
   } else if (RDE_GET_CHANNEL_INTERNAL_MSG_TYPE(pMsg->msgType) ==
              RDE_CHANNEL_BLOCK_SEND_INPUT_ENABLE_MSG) {
      Log("%s, blocking SendInput() changes to %d\n", __FUNCTION__, (int)*pMsg->payload);
#ifdef _WIN32
      if (*pMsg->payload && allowArmNoAntiKeyloggerEnabled && !HznProtect_IsAMD64()) {
         blockSendInputEnabled = false;
      } else {
#endif
         blockSendInputEnabled = *pMsg->payload;
#ifdef _WIN32
      }
#endif
   } else if (RDE_GET_CHANNEL_INTERNAL_MSG_TYPE(pMsg->msgType) ==
              RDE_CHANNEL_BLOCK_THUMBNAIL_REPRESENTATION_ENABLE_MSG) {
      Log("%s, blocking thumbnail representation changes to %d\n", __FUNCTION__,
          (int)*pMsg->payload);
      blockThumbnailRepresentationEnabled = *pMsg->payload;
   } else {
      Log("%s, not implemented type %d\n", __FUNCTION__,
          RDE_GET_CHANNEL_INTERNAL_MSG_TYPE(pMsg->msgType));
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKS::ProcessRdeCommonNetworkStateSettingMsg --
 *
 *       Process network state setting message, which includes
 *       RDE_CHANNEL_NETWORK_STATE_ENABLE_DISPLAY_MSG and
 *       RDE_CHANNEL_NETWORK_STATE_INTERVAL_MSG.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::ProcessRdeCommonNetworkStateSettingMsg(const RdeChannelMessage *pMsg) // IN
{
   switch (RDE_GET_CHANNEL_INTERNAL_MSG_TYPE(pMsg->msgType)) {
   case RDE_CHANNEL_NETWORK_STATE_ENABLE_DISPLAY_MSG: {
      networkWarningUIEnableDisplay = *(reinterpret_cast<const bool *>(pMsg->payload));
      Log("%s, get enable display value %d\n", __FUNCTION__, (bool)*pMsg->payload);
      networkStateEnableDisplaySettings.emit(networkWarningUIEnableDisplay);
   } break;
   case RDE_CHANNEL_NETWORK_STATE_INTERVAL_MSG: {
      networkWarningUIShowingInterval = *(reinterpret_cast<const uint32 *>(pMsg->payload));
      Log("%s, get interval value %u\n", __FUNCTION__, (uint32)*pMsg->payload);
      networkStateIntervalSettings.emit(networkWarningUIShowingInterval);
   } break;
   default:
      Log("%s, not implemented type %d\n", __FUNCTION__,
          RDE_GET_CHANNEL_INTERNAL_MSG_TYPE(pMsg->msgType));
      break;
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKS::OnRdeCommonUpdateNotified --
 *
 *       Callback when a GHI update is received in channel
 *       GHI_CHANNEL_VIEW_REMOTE_RDE_COMMON.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::OnRdeCommonUpdateNotified(GHIGuestToHostMessageType type, // IN
                               const uint8 *msg,               // IN
                               uint32 msgLen)                  // IN
{
   const RdeChannelMessage *pMsg = reinterpret_cast<const RdeChannelMessage *>(msg);
   uint32 msgType = pMsg->msgType;
   switch (type) {
   case GHI_GUEST_RDE_COMMON_HOST_SET_DPI:
      mRemoteDpi = *(reinterpret_cast<const int32 *>(msg));
      remoteDpiReceived.emit();
      break;
   case GHI_GUEST_RDE_COMMON_UNLOCK_DESKTOP:
      OnCertSSOUnlockRequestReceived(msg, msgLen);
      break;
   case GHI_GUEST_RDE_COMMON_CLIPBOARD_DATA_SENT_DONE:
      clipboardChangeCount = *(reinterpret_cast<const int *>(msg));
      clipboardDataSentDone.emit();
      break;
   case GHI_GUEST_RDE_COMMON_GENERIC:
      switch (RDE_GET_CHANNEL_MSG_TYPE(msgType)) {
      case RDE_CHANNEL_DPI_SYNC_MSG:
         ProcessRdeCommonDpiSyncMsg(msg, msgLen);
         break;
      case RDE_CHANNEL_DISPLAY_MSG:
         ProcessRdeCommonDisplayMsg(msg, msgLen);
         break;
      case RDE_CHANNEL_IME_MSG:
         ProcessRdeCommonIMEMsg(pMsg);
         break;
      case RDE_CHANNEL_APP_PROTECTION_MSG:
         ProcessRdeCommonAppProtectionMsg(pMsg);
         break;
      case RDE_CHANNEL_NETWORK_STATE_SETTING_MSG:
         ProcessRdeCommonNetworkStateSettingMsg(pMsg);
         break;
      default:
         break;
      }
      break;
   default:
      break;
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKS::OnUsbRedirectionUpdateNotified --
 *
 *       Callback when a GHI update is received in channel
 *       GHI_CHANNEL_VIEW_USB_REDIRECTION.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::OnUsbRedirectionUpdateNotified(GHIGuestToHostMessageType type, // IN
                                    const uint8 *msg,               // IN
                                    uint32 msgLen)                  // IN
{
   switch (type) {
   case GHI_GUEST_USB_REDIRECTION_USB_INSTANCE_ID:
      OnUsbdInstanceIdReceived(reinterpret_cast<const char *>(msg), msgLen);
      break;
   case GHI_GUEST_USB_REDIRECTION_DEVICES_FILTER_STATUS:
      OnUsbdDevicesFilterStatusChanged(!!*(reinterpret_cast<const Bool *>(msg)));
      break;
   default:
      break;
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKS::SendFido2Message --
 *
 *      Send Fido2 message to MKS.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::SendFido2Message(const char *ghiCmd, // IN
                      const char *param)  // IN
{
   ASSERT(ghiCmd != nullptr);
   utf::string fullCmd = cui::Format("%s:%s", ghiCmd, param);
   SendGHIRequest(
      GHI_CHANNEL_VIEW_FIDO2_REDIRECTION, ghiCmd, reinterpret_cast<const uint8 *>(fullCmd.c_str()),
      static_cast<uint32>(fullCmd.bytes()) + 1, cui::AbortSlot{}, mksctrl::GHIResponseSlot{});
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKS::OnFido2RedirectionUpdateNotified --
 *
 *       Callback when a GHI update is received in channel
 *       GHI_CHANNEL_VIEW_Fido2_REDIRECTION.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::OnFido2RedirectionUpdateNotified(GHIGuestToHostMessageType type, // IN
                                      const uint8 *msg,               // IN
                                      uint32 msgLen)                  // IN
{
   Log("%s: msg: (%s), msg len: %d, type: (%d).\n", __FUNCTION__, (const char *)msg, (int)msgLen,
       (int)type);
   switch (type) {
   case GHI_GUEST_FIDO2_REDIRECTION_WINDOW_REQUEST:
      mVM->fido2WndMsgReceived.emit(reinterpret_cast<const char *>(msg), msgLen);
      break;
   case GHI_GUEST_FIDO2_REDIRECTION_DEVICE_CHECK:
      mVM->fido2DevMsgReceived.emit(reinterpret_cast<const char *>(msg), msgLen);
      break;
   default:
      break;
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKS::OnConnectionStateChanged --
 *
 *      Callback when isRemoteConnected property
 *      gets changed.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::OnConnectionStateChanged(void)
{
   bool connected = mMKSControlClient->remoteConnected;

   if (connected == true) {
      SetConnectionState(csConnected);
      mReconnectAttemptCount = 0;
      mReconnectExpireTimeout.disconnect();
   } else if (mProtocol != MKS_DISPLAYPROTOCOL_VNC) {
      SetConnectionState(csDisconnected);
   } else {
      /*
       * If the connection state is not connected, the below logic decides
       * whether to request RMKS to retry for a connection based on the
       * following factors -
       * -> Whether the disconnect was due to network failure.
       * -> Maximum number of reconnect attempts.
       */
      VDPConnectionResult disconnectReason = (VDPConnectionResult)GetConnectionStateReason();

      if (disconnectReason != VDPCONNECT_NETWORK_FAILURE &&
          disconnectReason != VDPCONNECT_NETWORK_FAILURE_WITH_CONTINUITY) {
         Log("%s: remote mks set disconnect reason %s (%d).\n", __FUNCTION__,
             VDPConnectionResultToChar((VDPConnectionResult)disconnectReason), disconnectReason);
         SetConnectionState(csDisconnected);
      } else {
         SetConnectionState(csPending);

         if (mReconnectAttemptCount == 0) {
            if (!mReconnectExpireTimeout.connected()) {
               mReconnectExpireTimeout = cui::ScheduleCallback(
                  sigc::mem_fun(this, &MKS::OnReconnectExpireTimeout), RECONNECT_EXPIRE_TIME_MSEC);
            }

            // Request a new JWT for reconnection if Blast dynamic patch feature is enabled
            if (mProperties.GetBool("allowBlastDynamicPath")) {
               RequestProtocolRedirectReconnect();
               mReconnectAttemptCount++;
               return;
            }
         }

         mReconnectAttemptCount++;
         uint64 reconnectWaitMsec = ((uint64)1 << mReconnectAttemptCount) * 1000;
         if (reconnectWaitMsec > MAX_RECONNECT_WAIT_TIME_MSEC) {
            reconnectWaitMsec = MAX_RECONNECT_WAIT_TIME_MSEC;
         }

         // Request rmks for a reconnect after backoff period.
         cui::ScheduleCallback(sigc::mem_fun(this, &MKS::OnReconnectionWaitTimeout),
                               reconnectWaitMsec);

         Log("%s: remote mks set disconnect reason %d, so attempting to "
             "reconnect with retry count = %d and duration = %" FMT64 "u sec.\n",
             __FUNCTION__, disconnectReason, mReconnectAttemptCount, reconnectWaitMsec / 1000);
      }
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKS::OnClientDisconnectRequestStateChanged --
 *
 *      Callback when "remote/vdp/connectionResult/clientDisconnectRequestState"
 *      value changed.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::OnClientDisconnectRequestStateChanged(void)
{
   clientDisconnectRequestStateChanged.emit();
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKS::OnRemoteSupportRelativeMouse --
 *
 *      Callback when "remoteSupportRelativeMouse" value changed.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::OnRemoteSupportRelativeMouse(void)
{
   remoteSupportsRelativeMouseChanged.emit();
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKS::OnRemoteTabletAvailable --
 *
 *      Callback when "isRemoteTabletAvailable" value changed.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::OnRemoteTabletAvailable(void)
{
   remoteTabletAvailableChanged.emit();
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKS::OnUsbdInstanceIdReceived --
 *
 *      Callback when USB redirection usbd instance id value changed.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::OnUsbdInstanceIdReceived(const char *msg, // IN
                              uint32 msgLen)   // IN
{
   std::string newValue(msg, msgLen);
   Log("%s(): Received usbd instance id : %s (old value: %s)\n", __FUNCTION__, newValue.c_str(),
       mUsbdInstanceId.Get().c_str());
   mUsbdInstanceId = utf::string(newValue);
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKS::OnUsbdDevicesFilterStatusChanged --
 *
 *      Callback when USB redirection filter status value changed.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::OnUsbdDevicesFilterStatusChanged(bool status) // IN
{
   Log("%s(): Received usb redirection devices filter status : %s\n", __FUNCTION__,
       status ? "true" : "false");

   mUsbdDevicesFilterStatus = status;
   usbdDevicesFilterStatusChanged.emit();
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKS::GetUsbdDevicesFilterStatus --
 *
 *      Get the USB redirection filter status.
 *
 * Results:
 *      The value of USB redirection filter status.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

bool
MKS::GetUsbdDevicesFilterStatus()
{
   Log("%s: The usbd device filter status is %s.\n", __FUNCTION__,
       mUsbdDevicesFilterStatus ? "enabled" : "disabled");
   return mUsbdDevicesFilterStatus;
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKS::GetConnectionStateReason --
 *
 *      Get the VDPConnectionResult code.
 *
 * Results:
 *      VDPConnectionResult code to indicate the disconnection reason.
 *      Calling this function without any connection returns
 *      VDPCONNECT_RESULT_UNSPECIFIED.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

int
MKS::GetConnectionStateReason()
{
   /* Read the connectionResult/statusCode from MKS */
   int statusCode = mMKSControlClient->connectionResultStatusCode;

   if (statusCode == VDPCONNECT_INVALID) {
      Log("%s(): remote mks did not set a disconnect reason code, "
          "so assuming it crashed.\n",
          __FUNCTION__);
      /* View client should pop up an error as rmks might have crashed. */
      return VDPCONNECT_FAILURE;
   }

   Log("%s(): remote mks disconnect reason code is %d.\n", __FUNCTION__, statusCode);
   return statusCode;
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKS::GetConnectionState --
 * MKS::SetConnectionState --
 *      Get/Set the current connection state.
 *
 *      If you are setting csDisconnected state, please keep in mind that
 *      connectionStateChanged signal will be emitted in the set function.
 *      Given the connection has been broken, Some connected slots
 *      may then destroy this MKS object. So, please DONT visit
 *      any members of 'this' object after you call SetConnectionState(csDisconnected).
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

ConnectionState
MKS::GetConnectionState(void)
{
   return mConnectionState;
}

void
MKS::SetConnectionState(const ConnectionState cs) // IN
{
   Log("%s: MKS connection state changes from %d to %d.\n", __FUNCTION__, mConnectionState, cs);
   if (mConnectionState == cs) {
      return;
   }
   mConnectionState = cs;
   connectionStateChanged.emit();
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKS::RequestProtocolRedirectReconnect --
 *      Request to get the refreshed JWT and then do the reconnection of Blast
 *      session again.
 *
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */
void
MKS::RequestProtocolRedirectReconnect()
{
   Log("%s: request a new JWT for reconnection (%p).\n", __FUNCTION__, this);
   requestProtocolRedirectReconnect.emit();
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKS::SendGHIRequest --
 *
 *      Send a GHI/Unity message to the guest.
 *
 *      onDone/onAbort will be called when the guest's response to the request
 *      is received, or the request is failed to be sent, or the request times out.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::SendGHIRequest(GHIChannelType channel,          // IN
                    const char *msgName,             // IN
                    const uint8 *msgData,            // IN
                    uint32 msgDataLen,               // IN
                    cui::AbortSlot onAbort,          // IN
                    mksctrl::GHIResponseSlot onDone) // IN
{
   if (mMKSControlClient != NULL) {
      mMKSControlClient->SendGHIRequest(channel, msgName, msgData, msgDataLen, onAbort, onDone);
   } else {
      cui::Abort(onAbort);
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKS::GetClientDisconnectRequestState --
 * MKS::SetClientDisconnectRequestState --
 *      Get/Set the client initiated disconnect request status.
 *
 *      This is set to VDPDISCONNECTSTATE_REQUEST by the client to initiate a
 *      disconnect request.
 *
 *     This is set to VDPDISCONNECTSTATE_RESPONSE by rmks when it has a
 *     response for our request, retrieved with MKS::GetConnectionStateReason().
 *
 * Results:
 *      GetClientDisconnectRequestState returns the clientDisconnectRequestState
 *      SetClientDisconnectRequestState sets the disconnect status (True/False)
 *      in Horizon-Protocol
 *
 * Side effects:
 *      The server may close connection.
 *
 *-----------------------------------------------------------------------------
 */

int
MKS::GetClientDisconnectRequestState(void)
{
   if (mMKSControlClient != NULL) {
      return mMKSControlClient->ClientDisconnectRequestState;
   }

   return VDPDISCONNECTSTATE_NONE;
}


bool
MKS::SetClientDisconnectRequestState(VDPDisconnectState state,      // IN
                                     VDPConnectionResult vdpResult) // IN
{
   if (mMKSControlClient != NULL) {
      mMKSControlClient->SetClientDisconnectRequestState(state, vdpResult);
   }

   return true;
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKS::GetRemoteSupportRelativeMouse --
 *
 *      Check if the remoteRelativeMouse is supported
 *
 * Results:
 *      true if the remoteRelativeMouse is supported. false, otherwise.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

bool
MKS::GetRemoteSupportsRelativeMouse()
{
   if (mMKSControlClient != NULL) {
      return mMKSControlClient->remoteSupportsRelativeMouse;
   }

   return false; // since no value defined
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKS::GetRemoteTabletAvailable --
 *
 *      Check if the remoteTablet is available.
 *
 * Results:
 *      true if the remoteTablet is available. false, otherwise.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

bool
MKS::GetRemoteTabletAvailable()
{
   if (mMKSControlClient != NULL) {
      return mMKSControlClient->isRemoteTabletAvailable;
   }

   return false; // since no value defined
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKS::GetRemoteDpi --
 *
 *      Get system DPI on the remote operating system.
 *
 * Results:
 *      System DPI on the remote operating system.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

int
MKS::GetRemoteDpi()
{
   return mRemoteDpi;
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKS::GetUsbdInstanceId --
 *
 *      Get the USBD instance id string received from usbRedirection client plugin.
 *
 * Results:
 *      The received USBD instance id string.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

utf::string
MKS::GetUsbdInstanceId()
{
   Log("%s(): The usbd instance id is : %s \n", __FUNCTION__, mUsbdInstanceId.Get().c_str());
   return mUsbdInstanceId.Get();
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKS::SetProperty --
 *
 *      Set properties, which will be passed to the vchan plugins
 *      through pcoip_vchan_plugin_client_init.
 *
 * Results:
 *      true if successful. false, otherwise.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

bool
MKS::SetProperty(const utf::string &name,  // IN
                 const utf::string &value) // IN
{

   Log("%s(): \"%s\"=\"%s\"\n", __FUNCTION__, name.c_str(), value.c_str());
   mProperties.Add(name, value);
   return true;
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKS::SetUILang --
 *
 *      Initialize the UI language.
 *
 * Results:
 *      true if successful. false, otherwise.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

bool
MKS::SetUILang(const utf::string &UILang) // IN
{
   mUILanguage = UILang;
   return true;
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKS::SetVdpArgs --
 *
 *      Set VdpArgs.
 *
 * Results:
 *      true if successful. false, otherwise.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

bool
MKS::SetVdpArgs(uint32 securityLevel,                      // IN
                const utf::string &broker,                 // IN
                const utf::string &connectionUserMode,     // IN
                uint16 port,                               // IN
                bool brokerConnectionUDPUsed,              // IN
                uint16 udpProxyPort,                       // IN
                const utf::string &fqdn,                   // IN
                const utf::string &httpProxy,              // IN
                uint8 certCheckMode,                       // IN
                bool strictCertRevocationCheck,            // IN
                const std::vector<uint8> cert,             // IN
                const uint64 sslDisabledProtocols,         // IN
                const utf::string &sslCipherString,        // IN
                const utf::string &sslCipherSuites,        // IN
                const utf::string &sslSignatureAlgorithms, // IN
                const utf::string &sslSupportedGroups)     // IN
{
   VDPTargetInfo_FreeVDPArgs(mVdpArgs);
   BLAST_SSL_SETTINGS sslSettings = {0};
   sslSettings.sslDisabledProtocols = sslDisabledProtocols;
   sslSettings.sslCipherString = (char *)sslCipherString.c_str();
   sslSettings.sslCipherSuites = (char *)sslCipherSuites.c_str();
   sslSettings.sslSignatureAlgorithms = (char *)sslSignatureAlgorithms.c_str();
   sslSettings.sslSupportedGroups = (char *)sslSupportedGroups.c_str();

   mVdpArgs = VDPTargetInfo_AllocVDPArgs(
      securityLevel, broker.c_str(), connectionUserMode.c_str(), port, brokerConnectionUDPUsed,
      udpProxyPort, fqdn.c_str(), httpProxy.c_str(), certCheckMode, strictCertRevocationCheck,
      cert.size(), &cert[0], &sslSettings);

   return true;
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKS::SetDisplayTopology --
 *
 *      Set Display topology. Vector size, width/height and dpi of each monitor are
 *      already checked by caller to be non-zero.
 *
 * Results:
 *      true if successful. false, otherwise.
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

bool
MKS::SetDisplayTopology(const std::vector<MKSDisplayTopologyPacket> &monitors) // IN

{
   for (const auto &monitor : monitors) {
      Log("%s: monitor: (%d, %d, %d, %d) dpi %d\n", __FUNCTION__, monitor.left, monitor.top,
          monitor.right, monitor.bottom, monitor.dpi);
   }
   if (mMKSControlClient != NULL) {
      mksctrl::ViewControlClient *viewCtrl =
         dynamic_cast<mksctrl::ViewControlClient *>(GetMKSControlClient());
      if (viewCtrl != nullptr) {
         viewCtrl->SetDisplayTopology(monitors, cui::AbortSlot{}, cui::DoneSlot{});
      } else {
         Log("%s: wrong ViewControlClient!\n", __FUNCTION__);
      }
   } else {
      cui::Abort(cui::AbortSlot{});
   }
   return true;
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKS::SetDisplayTopology --
 *
 *      Set Display topology.
 *
 * Results:
 *      true if successful. false, otherwise.
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

bool
MKS::SetDisplayTopology(const std::vector<RdeChannelDisplayInfo> &displayInfos) // IN
{
   if (displayInfos.size() <= 0) {
      Log("%s: monitor size is incorrect!\n", __FUNCTION__);
      return false;
   }

   mPendingDisplayInfos = displayInfos;

   std::vector<MKSDisplayTopologyPacket> monitors;
   for (const auto &displayInfo : displayInfos) {
      MKSDisplayTopologyPacket monitor;
      RdeTopology2MKSTopology(monitor, displayInfo);
      monitors.push_back(monitor);
   }

   switch (mRemoteDpiVersion) {
   case RDE_CHANNEL_DPI_SYNC_SERVER_DEFAULT:
      SetDisplayTopology(monitors);
      break;
   /*
    * If dpi sync per monitor is supported for remote agent without IDD driver,
    * we both need send topology and display info to agent.
    */
   case RDE_CHANNEL_DPI_SYNC_NON_IDD_DRIVER:
      SetDisplayTopology(monitors);
      /*
       * If dpi sync per monitor is supported for remote agent with IDD driver,
       * we firstly send display info to agent, and delay sending topology until
       * receiving the message from agent.
       */
      HZN_FALLTHROUGH();
   case RDE_CHANNEL_DPI_SYNC_IDD_DRIVER: {
      const int packSize = sizeof(uint32);
      const int countSize = sizeof(uint32);
      const int msgHeaderSize = sizeof(RdeChannelMessage) - sizeof(uint8);
      int mallocSize =
         msgHeaderSize + countSize + sizeof(RdeChannelDisplayInfo) * displayInfos.size() + packSize;
      RdeChannelMessage *pRdeChannelMsg = (RdeChannelMessage *)malloc(mallocSize);
      if (pRdeChannelMsg == nullptr) {
         Log("%s: malloc size %d fail\n", __FUNCTION__, mallocSize);
         return false;
      }
      ON_BLOCK_EXIT(&free, pRdeChannelMsg);
      pRdeChannelMsg->msgSize = mallocSize;
      pRdeChannelMsg->msgType =
         RDE_SET_CHANNEL_MSG_TYPE(RDE_CHANNEL_DISPLAY_MSG, RDE_CHANNEL_DISPLAY_INFO_MSG);

      RdeChannelDisplaysInfo *pDisplaysInfo = (RdeChannelDisplaysInfo *)pRdeChannelMsg->payload;

      pDisplaysInfo->count = displayInfos.size();

      int i = 0;
      for (const auto &displayInfo : displayInfos) {
         pDisplaysInfo->displayInfos[i] = displayInfo;
         VMRect rect = displayInfo.rect;
         Log("%s: monitor: width:%d, height:%d, left:%d, top:%d, bpp:%d, isPrimary:%d, "
             "dpi:%d\n",
             __FUNCTION__, Rect_Width(&rect), Rect_Height(&rect), rect.left, rect.top,
             displayInfo.bpp, displayInfo.isPrimary, displayInfo.monitorDPI);
         i++;
      }
      SendGHIRequest(GHI_CHANNEL_VIEW_REMOTE_RDE_COMMON, GHI_RDE_COMMON_GENERIC_CMD,
                     (const uint8 *)pRdeChannelMsg, mallocSize, cui::AbortSlot{},
                     mksctrl::GHIResponseSlot{});
   } break;
   default:
      Log("%s: dpi version is incorrect!\n", __FUNCTION__);
      return false;
   }
   return true;
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKS::ApplyPrefs --
 *
 *      Apply all MKS Preferences, including bool types, GamingMouseMode.
 *      Should be called when the viewControl client is first connected to server.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::ApplyPrefs(void)
{
   cui::MKS::ApplyPrefs();
   if (mAttached) {
      GuestOps *guestOps = dynamic_cast<GuestOps *>(mVM->GetGuestOps());
      if (guestOps == nullptr) {
         Log("%s: wrong guestOps!\n", __FUNCTION__);
         return;
      }
      mMKSControlClient
         ->SetPreference(VIEWCONTROL_PREF_VIEW_SYNC_GUEST_NUMLOCK,
                         guestOps->canSetKeyboardState && syncGuestNumLock)
         ->SetPreference(VIEWCONTROL_PREF_VIEW_SYNC_GUEST_CAPSLOCK,
                         guestOps->canSetKeyboardState && syncGuestCapsLock)
         ->SetPreference(VIEWCONTROL_PREF_VIEW_SYNC_GUEST_SCROLLLOCK,
                         guestOps->canSetKeyboardState && syncGuestScrollLock);
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKS::EnableSound --
 *
 *      Requests the MKS to enable or disable sound from the protocol session.
 *
 * Results:
 *      true if succeeded, false otherwise.
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

bool
MKS::EnableSound(bool enabled) // IN: whether to enable sound
{
   if (GetVerboseLogging()) {
      Log("%s: %s sound.", __FUNCTION__, enabled ? "Enabling" : "Disabling");
   }

   if (mMKSControlClient != NULL) {
      mMKSControlClient->EnableSound(enabled);
   }

   return true;
}

/*
 *-----------------------------------------------------------------------------
 *
 * MKS::OnMKSChannelCreated --
 *
 *      Called when viewControl channel is created. When it is ready, we call
 *      OnMKSConnected to set the state to be connected and do some initialize work.
 *      If viewControl is not enabled, we should not check the viewControl
 *      connection state.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::OnMKSChannelCreated()
{
   if (mMKSControlChannelCreated) {
      OnMKSConnected();
      OnSetAttachedCompleted();
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * crt::common::MKS::OnCertSSOUnlockRequestReceived --
 *
 *      Handle the CertSSO unlock request from the plugin.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::OnCertSSOUnlockRequestReceived(const uint8 *msg, // IN
                                    uint32 msgLen)    // IN
{
   // Parse the two strings from the message
   utf::string unlockInfo;
   unlockInfo.append((const char *)msg, msgLen);
   cui::StringVec guids = unlockInfo.split(",");
   if (guids.size() != 2u) {
      Log("%s: Invalid unlock info, %s.\n", __FUNCTION__, unlockInfo.c_str());
      return;
   }

   // Send the message to client UI
   certSSOUnlockRequestReceived.emit(guids[0], guids[1]);
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKS::SetBufferWithoutAudio --
 *
 *      Handle the request to enable/disable buffer when there is no audio.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::SetBufferWithoutAudio(bool enable) // IN
{
   Log("%s: %s AV buffering without audio.\n", __FUNCTION__, enable ? "Enable" : "Disable");

   const char *data = enable ? "1" : "0";
   SendGHIRequest(GHI_CHANNEL_VIEW_PROTOCOL, GHI_SET_BUFFER_WITHOUT_AUDIO_CMD,
                  reinterpret_cast<const uint8 *>(data), static_cast<uint32>(strlen(data) + 1),
                  cui::AbortSlot{}, mksctrl::GHIResponseSlot{});
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKS::SetViewClientIMEEnabled --
 *
 *       Set IME enabled flag for RMKS so that the keyboard hook can start/stop
 *       filtering host IME keys.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::SetViewClientIMEEnabled(bool enabled) // IN
{
   uint8 imeEnabled = enabled ? 1 : 0;
   mMKSControlClient->SendGHIRequest(
      GHI_CHANNEL_VIEW_REMOTE_RDE_COMMON, GHI_RDE_COMMON_SET_IME_ENABLED_CMD, &imeEnabled,
      sizeof imeEnabled, cui::AbortSlot{}, mksctrl::GHIResponseSlot{});
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKS::SyncHostIMEKeys --
 *
 *       Sync host IME key combinations that needs to be filtered by RMKS
 *       keyboard hooks and send back to host.
 *
 * Returns:
 *       None.
 *
 * Side effects:
 *       None.
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::SyncHostIMEKeys(uint16 langId,                                                   // IN
                     const std::vector<std::pair<MKSModifierState, uint32>> &imeKeys) // IN
{
   std::vector<uint8> buffer;

   auto data = reinterpret_cast<const uint8 *>(&langId);
   buffer.insert(buffer.end(), data, data + sizeof langId);

   for (const auto &key : imeKeys) {
      data = reinterpret_cast<const uint8 *>(&key.first);
      buffer.insert(buffer.end(), data, data + sizeof key.first);

      data = reinterpret_cast<const uint8 *>(&key.second);
      buffer.insert(buffer.end(), data, data + sizeof key.second);
   }

   mMKSControlClient->SendGHIRequest(GHI_CHANNEL_VIEW_REMOTE_RDE_COMMON,
                                     GHI_RDE_COMMON_SET_IME_HOST_KEYS_CMD, buffer.data(),
                                     buffer.size(), cui::AbortSlot{}, mksctrl::GHIResponseSlot{});
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKS::SendIMECommand --
 *
 *       Build IME data and send to guest. The first byte is command and
 *       followed by optional data buffer.
 *
 * Returns:
 *       None.
 *
 * Side effects:
 *       None.
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::SendIMECommand(IMECommand cmd,   // IN
                    const void *data, // IN
                    uint32 size)      // IN
{
   RdeChannelMessage channelMsg{};
   channelMsg.msgSize = sizeof(channelMsg) - sizeof(uint8) + sizeof(cmd) + size;
   channelMsg.msgType = RDE_SET_CHANNEL_MSG_TYPE(RDE_CHANNEL_IME_MSG, RDE_CHANNEL_IME_DATA_MSG);

   std::vector<uint8> buffer;
   buffer.insert(buffer.end(), (uint8 *)&channelMsg,
                 (uint8 *)&channelMsg + sizeof(channelMsg) - sizeof(uint8));
   buffer.push_back(static_cast<uint8>(cmd));
   if (data && size) {
      buffer.insert(buffer.end(), (uint8 *)data, (uint8 *)data + size);
   }

   mMKSControlClient->SendGHIRequest(GHI_CHANNEL_VIEW_REMOTE_RDE_COMMON, GHI_RDE_COMMON_GENERIC_CMD,
                                     buffer.data(), buffer.size(), cui::AbortSlot{},
                                     mksctrl::GHIResponseSlot{});
}


/*
 *-----------------------------------------------------------------------------
 *
 * MKS::ProcessIMEDataMessage --
 *
 *       Process IME data message from Horizon IME.
 *
 * Returns:
 *       None.
 *
 * Side effects:
 *       None.
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::ProcessIMEDataMessage(const char *data, // IN
                           uint32 size)      // IN
{
   if (data && size) {
      auto cmd = static_cast<IMECommand>(*data);
      const char *payload = data + sizeof(IMECommand);

      if (cmd == IMECommand::IME_PIPE_CONNECTED) {
         mPipeConnected = *payload;
         if (!mPipeConnected) {
            Log("%s: reset caret position to zero\n", __FUNCTION__);
            Rect_SetEmpty(&mCaretRect);
         }
      }

      remoteIMECommandReceived.emit(data, size);
   }
}

/*
 *-----------------------------------------------------------------------------
 *
 * MKS::SetRemoteDpiVersion --
 *
 *       Set the remote dpi version for client.
 *
 * Returns:
 *       None.
 *
 * Side effects:
 *       None.
 *
 *-----------------------------------------------------------------------------
 */

void
MKS::SetRemoteDpiVersion(uint32 remoteDpiVersion)
{
   mRemoteDpiVersion = remoteDpiVersion;
}

} // namespace common
} // namespace crt
