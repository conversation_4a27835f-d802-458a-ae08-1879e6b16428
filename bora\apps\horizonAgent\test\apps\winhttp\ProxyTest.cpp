/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

#include "stdafx.h"

#include "utilWinHttpRequest.h"
#include "TestLaunchProcess.h"
#include "util.h"


/*
 * ProxyTest.cpp
 *
 *    Test cases that require a proxy server to be running on localhost.
 *
 */

class ProxyTest : public ::testing::Test {
public:
   ProxyTest() {};

   ~ProxyTest() {};

   void SetUp() {}

   void TearDown() {}

protected:
   /*
    *--------------------------------------------------------------------------
    *
    * StripProtocol --
    *
    *    Remove the protocol (http:// or https://) from a URL.
    *
    * Results:
    *    The hostname, port, path of the URL.
    *
    * Side effects:
    *    None.
    *
    *--------------------------------------------------------------------------
    */

   std::string StripProtocol(const std::string &url)
   {
      const std::string http = "http://";
      const std::string https = "https://";
      if (url.compare(0, http.length(), http) == 0) {
         return url.substr(http.length());
      } else if (url.compare(0, https.length(), https) == 0) {
         return url.substr(https.length());
      }
      return url;
   }


   /*
    *--------------------------------------------------------------------------
    *
    * ProxyServerReceivedRequest --
    *
    *    Determine if a request came to the proxy server.
    *
    * Results:
    *    True if a request came to the proxy server, false otherwise.
    *
    * Side effects:
    *    None.
    *
    *--------------------------------------------------------------------------
    */

   bool ProxyServerReceivedRequest(const std::wstring &logPath)
   {
      std::string proxyLogs;
      READ_FILE_INTO_STRING(logPath, proxyLogs, false);
      EXPECT_TRUE(!proxyLogs.empty());

      // Ensure that the proxy server received the request
      std::string hostname = StripProtocol(mServerEndpoint);
      std::string expectedLog = "CONNECT " + hostname;
      return proxyLogs.find(expectedLog) != std::string::npos;
   }


   /*
    * WinHTTP has a limitation where if you perform a request to localhost (or any variation of it
    * such as 127.0.0.1), then it will not use the proxy server (no matter the OS configuration).
    * Removing localhost from the proxy bypass list does not help.
    *
    * As a workaround, we need to make requests to a server that is not localhost. We do this by
    * modifying the hosts file.
    */
   std::string mServerEndpoint = "https://horizon-core-agent-test.com:4443";
   std::string mServerThumbprint = "EC:0C:62:A2:CB:38:5F:0D:96:11:D5:AC:7C:54:75:8A:F2:5B:E1:63:88:"
                                   "71:52:9A:4E:90:F0:8C:BF:0E:2D:95";

   std::string mProxyServer = "http://127.0.0.1:8889";
   std::wstring mStartProxy = L"python proxy.py";

   std::wstring mStartPACServer = LR"(python -u -m http.server 8884)";

   std::string mProxyPACContents =
      "function FindProxyForURL(url, host) {\n\treturn \"PROXY 127.0.0.1:8889\";\n}";

   std::wstring mWorkingDir = std::filesystem::current_path().wstring();
   std::string mWorkingDirA = std::filesystem::current_path().string();
};


#define START_PAC_SERVER()                                                                         \
   PROCESS_INFORMATION pacProcess = {0};                                                           \
   HANDLE hPacFile = NULL;                                                                         \
   auto pacLogPath = mWorkingDir + L"\\" + testName + L"-pac.log";                                 \
   ASSERT_TRUE(ProcUtil::LaunchProcessWithStdOut(mStartPACServer, pacLogPath, pacProcess,          \
                                                 hPacFile, false));

#define STOP_PAC_SERVER()                                                                          \
   ProcUtil::CloseProcess(pacProcess);                                                             \
   if (hPacFile != NULL) {                                                                         \
      CloseHandle(hPacFile);                                                                       \
   }


/*
 * WinHTTP has a limitation where it will cache the contents of a proxy PAC file in memory.
 * There is no known way to clear this cache.
 * As a workaround, we create a unique PAC file for each test case. To prevent subsequent runs from
 * failing, we also append the tick count to the PAC file name.
 */
#define GENERATE_UNIQUE_PAC_NAME()                                                                 \
   auto testNameA = gutils::GetTestNameA();                                                        \
   std::string proxyPacName = testNameA + "-" + std::to_string(GetTickCount64()) + ".pac";

#define CREATE_PAC_FILE()                                                                          \
   GENERATE_UNIQUE_PAC_NAME();                                                                     \
   WinFile pacFile(mWorkingDirA + "\\" + proxyPacName);                                            \
   ASSERT_TRUE(pacFile.WriteStringToFile(mProxyPACContents));

// These PAC files don't have anything interesting in them. So delete them to save space.
#define DELETE_PAC_FILE()                                                                          \
   if (pacFile.FileExists()) {                                                                     \
      pacFile.FileDelete();                                                                        \
   }


/*
 * ProxyTest::testProxy --
 *
 *    Test that we can perform a get to the server using a proxy.
 */

TEST_F(ProxyTest, testProxy)
{
   auto testName = gutils::GetTestName();

   std::string getUrl = mServerEndpoint + "/ping";
   std::string getResponse;

   START_SERVER(mStartProxy, L"proxy");

   WinHttpRequest req;
   req.ignoreAllCertErrors();
   req.setProxyServerList(mProxyServer);
   EXPECT_TRUE(req.getSync(getUrl, getResponse));
   EXPECT_STREQ(getResponse.c_str(), "pong");

   STOP_SERVER();

   EXPECT_TRUE(ProxyServerReceivedRequest(serverLogPath));
}


/*
 * ProxyTest::BadProxyServer --
 *
 *    Test that an invalid proxy server returns false and an empty response.
 */

TEST_F(ProxyTest, BadProxyServer)
{
   std::string getUrl = mServerEndpoint + "/ping";
   std::string getResponse;

   WinHttpRequest req;
   req.ignoreAllCertErrors();
   req.setProxyServerList("http://unknownproxy.com:8889");
   EXPECT_FALSE(req.getSync(getUrl, getResponse));
}


/*
 * ProxyTest::BadPACURL --
 *
 *    Provide an invalid PACURL. Expect that the client will make an attempt to get the proxy via
 *    PACURL. This attempt will fail. The client should fall back toa direct connection. That
 *    request should succeed.
 */

TEST_F(ProxyTest, BadPACURL)
{
   auto testName = gutils::GetTestName();

   std::string getUrl = mServerEndpoint + "/ping";
   std::string getResponse;

   GENERATE_UNIQUE_PAC_NAME();

   START_PAC_SERVER();

   WinHttpRequest req;
   req.ignoreAllCertErrors();
   req.setProxyPACURL("http://127.0.0.1:8884/" + proxyPacName);
   EXPECT_TRUE(req.getSync(getUrl, getResponse));
   EXPECT_STREQ(getResponse.c_str(), "pong");

   STOP_PAC_SERVER();

   EXPECT_TRUE(wutil::ServerReceivedRequest(pacLogPath, proxyPacName));
}


/*
 * ProxyTest::PacExists_NoProxy --
 *
 *    Provider a valid PAC file that contains a proxy server. That proxy server will be unreachable.
 *    Ensure the request fails.
 */

TEST_F(ProxyTest, PacExists_NoProxy)
{
   auto testName = gutils::GetTestName();

   std::string getUrl = mServerEndpoint + "/ping";
   std::string getResponse;

   CREATE_PAC_FILE();

   START_PAC_SERVER();

   WinHttpRequest req;
   req.ignoreAllCertErrors();
   req.setProxyPACURL("http://127.0.0.1:8884/" + proxyPacName);
   EXPECT_FALSE(req.getSync(getUrl, getResponse));

   STOP_PAC_SERVER();

   EXPECT_TRUE(wutil::ServerReceivedRequest(pacLogPath, proxyPacName));

   DELETE_PAC_FILE();
}


/*
 * ProxyTest::PacAndProxyOK --
 *
 *    Provide a valid PAC file that contains a proxy server. That proxy server will be available.
 *    Ensure the request succeeds.
 */

TEST_F(ProxyTest, PacAndProxyOK)
{
   auto testName = gutils::GetTestName();

   std::string getUrl = mServerEndpoint + "/ping";
   std::string getResponse;

   CREATE_PAC_FILE();

   START_SERVER(mStartProxy, L"proxy");
   START_PAC_SERVER();

   WinHttpRequest req;
   req.ignoreAllCertErrors();
   req.setProxyPACURL("http://127.0.0.1:8884/" + proxyPacName);
   EXPECT_TRUE(req.getSync(getUrl, getResponse));
   EXPECT_STREQ(getResponse.c_str(), "pong");

   STOP_PAC_SERVER();
   STOP_SERVER();

   EXPECT_TRUE(wutil::ServerReceivedRequest(pacLogPath, proxyPacName));
   EXPECT_TRUE(ProxyServerReceivedRequest(serverLogPath));

   DELETE_PAC_FILE();
}


/*
 * ProxyTest::GoodProxyBadPACURL --
 *
 *    Provider an invalid PAC URL. The proxy server will be valid.
 *    Ensure the request succeeds. The expectation is that we will fall back to the explicitly-
 *    provided proxy server.
 */

TEST_F(ProxyTest, GoodProxyBadPACURL)
{
   auto testName = gutils::GetTestName();

   std::string getUrl = mServerEndpoint + "/ping";
   std::string getResponse;

   START_SERVER(mStartProxy, L"proxy");

   WinHttpRequest req;
   req.setProxyPACURL("http://127.0.0.1:8884/unknownproxy.pac");
   req.setProxyServerList(mProxyServer);
   req.ignoreAllCertErrors();
   EXPECT_TRUE(req.getSync(getUrl, getResponse));
   EXPECT_STREQ(getResponse.c_str(), "pong");

   STOP_SERVER();

   EXPECT_TRUE(ProxyServerReceivedRequest(serverLogPath));
}


/*
 * ProxyTest::BadProxyGoodPACURL --
 *
 *    Provider a valid PAC URL. The proxy server will be invalid. Ensure the request succeeds.
 */

TEST_F(ProxyTest, BadProxyGoodPACURL)
{
   auto testName = gutils::GetTestName();

   std::string getUrl = mServerEndpoint + "/ping";
   std::string getResponse;

   CREATE_PAC_FILE();

   START_PAC_SERVER();
   START_SERVER(mStartProxy, L"proxy");

   WinHttpRequest req;
   req.setProxyPACURL("http://127.0.0.1:8884/" + proxyPacName);
   req.setProxyServerList("http://unknownproxy.com:8889");
   req.ignoreAllCertErrors();
   EXPECT_TRUE(req.getSync(getUrl, getResponse));
   EXPECT_STREQ(getResponse.c_str(), "pong");

   STOP_PAC_SERVER();
   STOP_SERVER();

   EXPECT_TRUE(wutil::ServerReceivedRequest(pacLogPath, proxyPacName));
   EXPECT_TRUE(ProxyServerReceivedRequest(serverLogPath));

   DELETE_PAC_FILE();
}


/*
 * ProxyTest::GoodProxyGoodPACURL --
 *
 *    Provider a valid PAC URL. The proxy server will be valid.
 *    Ensure the request succeeds. The expectation is that the proxy we determine via PAC URL will
 *    supercede the explicitly-provided proxy server.
 */

TEST_F(ProxyTest, GoodProxyGoodPACURL)
{
   auto testName = gutils::GetTestName();

   std::string getUrl = mServerEndpoint + "/ping";
   std::string getResponse;

   CREATE_PAC_FILE();

   START_PAC_SERVER();
   START_SERVER(mStartProxy, L"proxy");

   WinHttpRequest req;
   req.setProxyPACURL("http://127.0.0.1:8884/" + proxyPacName);
   req.setProxyServerList(mProxyServer);
   req.ignoreAllCertErrors();
   EXPECT_TRUE(req.getSync(getUrl, getResponse));
   EXPECT_STREQ(getResponse.c_str(), "pong");

   STOP_PAC_SERVER();
   STOP_SERVER();

   EXPECT_TRUE(wutil::ServerReceivedRequest(pacLogPath, proxyPacName));
   EXPECT_TRUE(ProxyServerReceivedRequest(serverLogPath));

   DELETE_PAC_FILE();
}
