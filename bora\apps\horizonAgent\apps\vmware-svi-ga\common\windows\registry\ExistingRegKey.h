/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*!
 * @file ExistingRegKey.h --
 * Defines class to read, or write to existing registry key.
 */

#pragma once

#include "common/windows/handle/Handle.h"
#include "common/windows/registry/RegKeyBase.h"
#include "common/windows/registry/Registry.h"
#include <vector>
#include <windows.h>

namespace svmga {
namespace common {
namespace windows {
namespace registry {

namespace handle = svmga::common::windows::handle;

class ExistingRegKey;
typedef boost::shared_ptr<ExistingRegKey> ExistingRegKeyPtr;

/*!
 * @class ExistingRegKey --
 * Represents an existing registry key.
 * Currently, compiler generate copy constructor, assignment operator
 * and destructor are good enough.
 */
class ExistingRegKey : public RegKeyBase {
public:
   /*!
    * Constructor.
    * @param parent IN: parent key, e.g. HKEY_LOCAL_MACHINE
    * @param path IN: key path relative to parent
    * @param samDesired IN: requested access permissions
    * @param hr OUT: return code
    */
   ExistingRegKey(HKEY parent, std::wstring path, REGSAM samDesired, HRESULT &hr);

   /*!
    * Reads a single-string value from the key.
    * @param valName IN: the name of the value
    * @return the value of the string
    */
   HRESULT ReadStringValue(const std::wstring &valName, std::wstring &value);

   /*!
    * Reads a multi-string value from the key.
    * @param valName IN: the name of the value
    * @return the value read from the registry.
    */
   HRESULT ReadMultiStringValue(const std::wstring &valName, std::vector<std::wstring> value);
   HRESULT WriteMultiStringValue(const std::wstring &valName, BYTE *value, const size_t len);

   /*!
    * Reads a DWORD value from the key.
    * @param valName IN: the name of the value
    * @return the value
    */
   HRESULT ReadDwordValue(const std::wstring &valName, DWORD &dwResult);

   /*!
    * Reads a DWORD value from the key.
    * @param valName IN: the name of the value
    * @param defVAl IN: default value to return if it does not exists.
    * @return the value
    */
   HRESULT ReadDwordValue(const std::wstring &valName, DWORD defVal, DWORD &dwResult);

   HRESULT ReadDwordValueEx(const std::wstring &valName, DWORD defVal, DWORD &dwValue);

   /*!
    * Writes a single-string value to the key.
    * @param valName IN: the name of the value
    * @param type IN: the value's type, must be either REG_SZ or REG_EXPAND_SZ
    * @param value IN: the string value
    */
   HRESULT WriteStringValue(const std::wstring &valName, DWORD type, const std::wstring &value);

   /*!
    * Writes a REG_DWORD value to the key.
    * @param valName IN: the name of the value
    * @param value IN: the DWORD value
    */
   HRESULT WriteDwordValue(const std::wstring &valName, DWORD value);

   /*!
    * Checks to see a value exists.
    */
   bool ValueExists(const std::wstring &valName);

   /*!
    * Checks to see if a sub key exists.
    */
   HRESULT SubKeyExists(const std::wstring &subKeyName, bool &bExists);

   HRESULT WaitForChange(bool &bResult, HANDLE hWaitEvent);

   HRESULT WaitForDwordValue(const std::wstring &ValueName, DWORD dwExpectedValue, DWORD dwDuration,
                             bool &bResult);

   HRESULT WaitForStringValue(const std::wstring &ValueName, std::wstring strExpectedValue,
                              DWORD dwDuration, bool &bResult, HANDLE hShutdownEvent);

   HRESULT WaitForStringValue(const std::wstring &valueName, const std::wstring &expectedValue,
                              int totalWaitTimeInMs);

   // Wait for a value to be set, but no need to check the value
   HRESULT WaitForValueExists(const std::wstring &ValueName, DWORD dwDuration);

private:
   HRESULT OpenKey(HKEY parent, std::wstring path, REGSAM samDesired);
};

} // namespace registry
} // namespace windows
} // namespace common
} // namespace svmga