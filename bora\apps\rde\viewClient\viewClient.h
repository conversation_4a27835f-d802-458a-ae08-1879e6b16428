/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * viewClient.h --
 */

#ifndef _VIEWCLIENT_H_
#define _VIEWCLIENT_H_

#include "rectangle.h"
#include "vdpPlugin.h"
#include "mksScreenTypes.h"
#include "ssl.h"
#include "viewInput.h"
#include "viewMouse.h"
#include "vm_basic_types.h"
#include "vmclientrmks.h"
#include "hzn_protocol.h"

#ifdef __cplusplus
extern "C" {
#endif

/*
 * VMTIMETYPExS_TO_MS --
 * VMTIMETYPExS_TO_SEC --
 *
 *    Converts VmTimeType ticks to other time values.
 */
#define VMTIMETYPEUS_TO_MS(t) ((double)(t) / 1000)
#define VMTIMETYPEUS_TO_SEC(t) ((double)(t) / 1000000)
#define VMTIMETYPENS_TO_MS(t) ((double)(t) / 1000000)
#define VMTIMETYPENS_TO_SEC(t) ((double)(t) / 1000000000)

/*
 * Optional verbose logging which gets compiled into release builds
 */
#define VIEWCLIENT_RLOG(level, ...)                                                                \
   do {                                                                                            \
      if (viewConfig.rLogLevel >= level) {                                                         \
         Log(__VA_ARGS__);                                                                         \
      }                                                                                            \
   } while (0)


#define VIEW_MAX_OVERRIDE_CONFIG 10
#define VIEW_ID_INVALID -1
#define VNC_MAX_SCREENS 16
#define FIRST_GROUP_ID 0
#define VIEW_ID_LAST_VALID 4095

/*
 * Watermark sizes.
 */
#define VIEW_WATERMARK_WIDTH 16
#define VIEW_WATERMARK_HEIGHT 16

/*
 * Window flags
 */
#define VIEW_IS_RENDERING (1 << 0)
#define VIEW_SKIP_PRESENTATION (1 << 1)
#define VIEW_IS_MINIMIZED (1 << 2)
#define VIEW_ONLY_BASELAYER (1 << 3)
#define VIEW_DSTRECT (1 << 4)

/*
 * Overlay indexes.
 */
#define VIEW_MAX_VDP_OVERLAYS 64
#define VIEW_MAX_CURSOR_OVERLAYS 2
#define VIEW_MAX_WIN_OVERLAYS 4
#define VIEW_MAX_BASE_LAYERS 64
enum {
   VIEW_OVERLAYIDX_VDP_START = 0,

   VIEW_OVERLAYIDX_BASE_LAYER_START = VIEW_MAX_VDP_OVERLAYS,

   VIEW_OVERLAYIDX_START = VIEW_OVERLAYIDX_BASE_LAYER_START + VIEW_MAX_BASE_LAYERS,

   VIEW_OVERLAYIDX_FPS_START = VIEW_OVERLAYIDX_START,

   VIEW_OVERLAYIDX_CURSOR_START = VIEW_OVERLAYIDX_FPS_START + VNC_MAX_SCREENS,

   VIEW_OVERLAYIDX_LOSSLESSINDICATOR_START =
      VIEW_OVERLAYIDX_CURSOR_START + VIEW_MAX_CURSOR_OVERLAYS,

   VIEW_OVERLAYIDX_WATERMARK_START = VIEW_OVERLAYIDX_LOSSLESSINDICATOR_START + VNC_MAX_SCREENS,

   VIEW_OVERLAYIDX_IMGUI_START = VIEW_OVERLAYIDX_WATERMARK_START + VNC_MAX_SCREENS,

   VIEW_OVERLAYIDX_PRESENT_MON_START = VIEW_OVERLAYIDX_IMGUI_START + VNC_MAX_SCREENS,

   VIEW_OVERLAYIDX_MAX = VIEW_OVERLAYIDX_PRESENT_MON_START + VNC_MAX_SCREENS,

   /*
    * Window overlays.
    */
   VIEW_OVERLAYIDX_WIN_START = VIEW_OVERLAYIDX_MAX,
   VIEW_OVERLAYIDX_LOSSLESS_WIN_START =
      VIEW_OVERLAYIDX_WIN_START + VIEW_MAX_WIN_OVERLAYS * (VIEW_ID_LAST_VALID + 1),
};

typedef struct {
   Bool loadClipboardPlugin;
   Bool forceOnAllowForcedRelativeMouse;
   Bool enableBlastNetworkContinuity;
   Bool enableBlastNetworkRecovery;
   Bool enableBlastNetworkIntelligence;
   Bool enableBlastNetworkWaitForBEAT;
   Bool enableBlastNetworkVVCPauseResume;
   Bool enableBlastNetworkVVCQoSPolicy;
   Bool enableBlastNetworkThread;
   Bool enableUDP;
   Bool allowClientH264;
   Bool allowClientHEVC;
   Bool allowClientAV1;
   Bool allowClientHDR;
   Bool forceClientHDR;
   Bool allowClientH264YUV444;
   Bool allowClientHEVCYUV444;
   Bool allowClientHEVCYUV444_10BIT;
   Bool allowClientAV1YUV444;
   Bool enableDecoderWatermark;
   Bool nodelay;
   Bool showCursorSpot;
   Bool showHiddenCursorSpot;
   Bool disableAudioRecovery;
   Bool disableTypematic;
   Bool enableHardwareVideo;
   Bool permitUnverifiedWebSocketSSL;
   char *expectedThumbprintOverride;

   int allowHZNHeartbeatReconnect;
   int qosPolicyDscpCOutTCPv4;
   int qosPolicyDscpCOutUDPv4;
   int qosPolicyDscpCOutTCPv6;
   int qosPolicyDscpCOutUDPv6;

   Bool enableBlastNetworkVVCDeferredAcks;
   int vvcMptAckQuietPeriod;
   int vvcMptAckUnackedBytes;
   int vvcMptAckSeqGap;

   Bool enableBlastNetworkVVCBatching;
   Bool enableBlastNetworkTcpBwe;

   Bool enableBeatRouteReplacement;
   Bool allowBlastNetworkLegacyWSProtocols;

   uint32 udpFecMaxLowLevelPacketSize;

   uint32 minTypematicPeriodUS;
   uint32 minTypematicDelayUS;
   uint32 defaultTypematicDelayUS;
   uint32 defaultTypematicPeriodUS;
   Bool suppressNumlocks;
   Bool enableHotkeyNumlockBinding;

   Bool allowClientCursorWarping;
   Bool allowCursorEventsOnLowLatencyChannel;
   uint32 mouseMoveMaxLatencyMsec;

   Bool allowClientPenEventRedirection;
   Bool penEventRedirectionOptimizedMode;

   Bool enableAsyncDecode;
   double asyncThreadsPerCore;
   uint32 asyncThreadsMin;
   uint32 asyncThreadsMax;
   uint32 asyncThreadsOverride;
   uint32 vaSyncSurfaceRetryCount;

   Bool allowMultipleAudioOut;
   Bool allowDynamicAudioOut;
   Bool allowSoundlibAudioQueueStatus;

   Bool enableExplicitColorInfo;

   Bool enablePollRestartSocket;

   Bool dumpPresentTimes;
   uint32 dumpPresentIntervalMS;

   Bool dumpWindowYUV;

   char *decodeCapsProbingMode;
   Bool onlyProbeMode;

   unsigned int rLogLevel;

   Bool enableSupportMode;
   unsigned int supportModeDelaySeconds;

   /*
    * Override screen configuration.
    */
   struct {
      VMRect screenRect[VNC_MAX_SCREENS];
      int screenDpi[VNC_MAX_SCREENS];
      int numScreens;
      Bool fullScreenConfig;
   } screenOverrides[VIEW_MAX_OVERRIDE_CONFIG];
   int currentScreenConfig;
   int numScreenConfigs;

   /*
    * Override window configuration.
    */
   struct {
      VMRect windowSrcRect;
      VMRect windowDstRect;
      int trackScreen;
      Bool trackScreenForWindowSize;
   } windowOverrides[VIEW_MAX_OVERRIDE_CONFIG];
   int numWindowConfigs;
} ViewClientConfig;

extern ViewClientConfig viewConfig;

typedef struct {
   Bool visible;
   VMPoint point;
   int screenId;
} ViewCursorPosition;

typedef struct {
   VMRect screenRect;
   unsigned int refreshRate;
   unsigned int dpi;
   Bool hdrCapable;
} ViewClientDisplays;

typedef struct {
   struct {
      struct ViewClientVideoCapsEntry {
         VMRect max;
         /*
          * 8-bit (SDR) mode codecs only use BT.601 and BT.709.
          * 10-bit (HDR) mode codecs only use BT.2020.
          */
         union {
            struct {
               Bool supportsI601;
               Bool supportsH709;
               Bool supportsF709;
            };
            struct {
               Bool supportsH2020;
               Bool supportsF2020;
            };
         };
      } d8bit, d10bit;
   } yuv420, yuv444;
} ViewClientVideoCodecCaps;

typedef struct {
   ViewClientVideoCodecCaps h264;
   ViewClientVideoCodecCaps hevc;
   ViewClientVideoCodecCaps av1;
} ViewClientVideoDecoderCaps;

/*
 * Decode probing functions.
 */

ViewClientVideoDecoderCaps *ViewClient_GetVideoDecoderCaps(void);

/*
 * Blast protocol/session related functions.
 */

Bool ViewClient_IsBlastSession(void);
Bool ViewClient_IsVDPSession(void);
Bool ViewClient_IsViewSession(void);
Bool ViewClient_IsGenericVNCSession(void);

void ViewClient_Disconnected(int errorCode);
Bool ViewClient_IsConnected(void);
MKSDisplayProtocol ViewClient_GetProtocol(void);
void ViewClient_RequestBlastToDisconnect(VDPConnectionResult vdpResult);
void ViewClient_RequestExit(void);
Bool ViewClient_IsExiting(void);
Bool ViewClient_RetryConnection(int reconnect);
void ViewClient_ViewControlConnected(Bool connected);
Bool ViewClient_AudioOutDevice(void);
void ViewClient_SendAudioOutDevicesRequest(uint16 numDevs, VNCAudioOutputDeviceUniqueId *devs);
void ViewClient_ReinitializeAudioOutDevices(uint16 numDevs, VNCAudioOutputDeviceUniqueId *devs);
Bool ViewClient_IsDynamicAudioOutEnabled(void);
Bool ViewClient_IsAppSession(void);
void ViewClient_RetryProtocolRedirectConnection(const char *target);


/*
 * Standalone mode functions.
 */
Bool ViewClient_IsFakeMultimon(void);
Bool ViewClient_IsRealMultimon(void);
void ViewClient_SetRealMultimon(Bool enable);
Bool ViewClient_IsUIConnected(void);
Bool ViewClient_IsPlayerMode(void);
Bool ViewClient_IsBenchmarkMode(void);

/*
 * Block keylogger functions.
 */
Bool ViewClient_IsAgentInstalled();
void ViewClient_SetBlockKeyLoggerStatus(Bool enable);
Bool ViewClient_IsBlockKeyLoggerEnabled();
void ViewClient_SetBlockSendInputStatus(Bool enable);
Bool ViewClient_IsBlockSendInputEnabled();
void ViewClient_SetAllowArmNoAntiKeyloggerStatus(Bool enable);

/*
 * Input functions.
 */
void ViewClient_InsertTouchEvent(ViewInputTouchPacket *touchDown, int touchCount);
void ViewClient_InsertPenEvents(ViewInputPenPointer *penPointers, uint32 penPointerCount,
                                int frameId);
void ViewClient_InsertMouseEvent(ViewMouseEvent *m);
void ViewClient_InsertKeyEvent(HIDUsage hid, Bool down);
void ViewClient_InsertKeyEventWithModifiers(HIDUsage hid, ViewModifierKeyState modState);
void ViewClient_InsertUnicodePoint(uint32 unicodeCodePoint, uint64 timestamp);
void ViewClient_ReleaseKeyboard(void);
Bool ViewClient_SendKeySequence(const char *sequence);
void ViewClient_SetLEDs(unsigned int leds);
void ViewClient_SetMouseMode(Bool isAbsolute);
void ViewClient_SetMouseCaps(ViewMouseCapabilities caps, ViewMouseMode hint);
void ViewClient_SetLEDSyncCaps(Bool supportKeyboardLEDRequest);
void ViewClient_SetTypematicInfo(uint32 delay, uint32 period);
void ViewClient_MouseInit(void);
void ViewClient_MouseExit(void);
void ViewClient_KeyboardInit(void);
void ViewClient_KeyboardExit(void);
void ViewClient_IMESetEnabled(Bool enabled);
Bool ViewClient_IsIMEEnabled(void);


/*
 * Cursor functions.
 */
void ViewClient_CursorInit(void);
void ViewClient_CursorExit(void);
void ViewClient_CursorGrab(VMPoint *point);
void ViewClient_CursorUngrab(void);
void ViewClient_SetCursorShape(const MKSCursor *shape);
void ViewClient_SetCursorVisibility(Bool visible);
double ViewClient_GetUnityCursorScale();
void ViewClient_SetUnityCursorScale(double scale);
void ViewClient_UpdateCursorPos(int x, int y);
void ViewClient_SetBlankCursor(void);
void ViewClient_UpdateCursorShape(void);
void ViewClient_GetCursorState(VMPoint *point, Bool *valid, Bool *local, Bool *swCursor,
                               Bool *visible);
void ViewClient_SetCursorPos(VMPoint point, int screenId);
void ViewClient_WarpCursorPos(VMPoint point);
void ViewClient_SetCursorSpot(Bool cursorSpot);
void ViewClient_NotifyRemoteWarp(void);
void ViewClient_ForceLossyCursor(Bool enabled);
void ViewClient_DrawCursorToWindow(int windowId);
Bool ViewClient_IsRelativeMouse(void);

Bool ViewClient_KeyHookNotifyUI(HIDUsage hid, ViewModifierKeyState modState);

/*
 * Replay functionality.
 */
void ViewClient_ReplayInit(void);
void ViewClient_ReplayExit(void);
void ViewClient_ReplayDebugMessage(uint32 opcode, const uint8_t *data, uint32 dataLen);
void ViewClient_ReplayFlushScreens(uint16 dirtyScreens);
Bool ViewClient_ReplayStartRecording(const char *path);
void ViewClient_ReplayStopRecording(void);
Bool ViewClient_ReplayIsRecording(void);
Bool ViewClient_ReplayStartComparing(const char *path);
void ViewClient_ReplayStopComparing(void);
Bool ViewClient_ReplayIsComparing(void);
Bool ViewClient_ReplayGetComparisonFrameStats(unsigned int screenId, int *frameId,
                                              double *framePsnr, double *frameMse, int64 *frameSse);
Bool ViewClient_ReplayGetComparisonSseBlocks(unsigned int screenId, int *width, int *height,
                                             int *blockSize, const int **sseBlocks);
const char *ViewClient_ReplayGetComparisonReplayPath(void);

/*
 * Imgui debug ui.
 */
void ViewClient_ImguiInit(void);
void ViewClient_ImguiExit(void);
Bool ViewClient_ImguiVisible();
Bool ViewClient_ImguiKeyEvent(HIDUsage hid, Bool down);
Bool ViewClient_ImguiMouseEvent(ViewMouseEvent *m);
Bool ViewClient_ImguiUpdateCursor();
void ViewClient_ImguiDebugMessage(uint32 opcode, const uint8_t *data, uint32 dataLen);

/*
 * Split MKS Window functions.
 */
Bool ViewClient_IsEnabledSplitMKSWindows(void);
void ViewClient_SetDisplayTopology(void *clientData, uint32 cookie, const uint8 *buf,
                                   uint32 bufSize);

Bool ViewClient_IsMultiScreenEnabled(void);
Bool ViewClient_IsMultiWindowEnabled(void);

/*
 * Rect helpers
 */

void Rect_ScaleRectConsistent(VMRect *src, VMRect *dst, VMRect *inRect, VMRect *outRect);
void ViewClient_SendTouchEvent(int touchCount, const ViewTouchPoint *touchPts,
                               ViewInputFlags flags);

void ViewClient_Init();

#ifdef __cplusplus
}
#endif

#endif
