/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

#ifdef _WIN32
#   include <winsock2.h>
#   include <MessageFrameWork.h>

using namespace CORE;

// for horizon-vvc2.dll
#   include <cedar/ipc/ipclib.h>
#   include <smWinAgentDefs.h>

#   include "BenevSSLManager.h"

using cedar::PlatformString;
using namespace cedar::ipc;
using namespace horizon::sm;
#endif

#include "base64.h"
#include "BenevAgent.h"
#include "BenevProxy.h"
#include "preference.h"
#include "config.h"

#define LGPFX "BenevAgent: "


/*
 *----------------------------------------------------------------------
 *
 * AgentVvcOnChannelOpen --
 *
 *      Callback function when VVC channel is opened.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      See above.
 *
 *----------------------------------------------------------------------
 */

void
AgentVvcOnChannelOpen(VvcChannelHandle channel, // IN
                      VvcStatus status,         // IN
                      uint8 *initialData,       // IN
                      size_t initialDataLen,    // IN
                      void *clientData)         // IN
{
   Log(LGPFX "Inside AgentVvcOnChannelOpen\n");
   BenevAgent *agent = (BenevAgent *)clientData;

   agent->vvcOnChannelOpen(channel, status, initialData, initialDataLen);
}


/*
 *----------------------------------------------------------------------
 *
 * AgentVvcOnChannelClose --
 *
 *      Callback function when VVC channel is closed.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      See above.
 *
 *----------------------------------------------------------------------
 */

void
AgentVvcOnChannelClose(VvcChannelHandle channelHandle, // IN
                       VvcCloseChannelReason reason,   // IN
                       void *clientData)               // IN
{
   Log(LGPFX "Inside AgentVvcOnChannelClose\n");

   BenevAgent *agent = (BenevAgent *)clientData;
   agent->vvcOnChannelClose(channelHandle, reason);
}


/*
 *----------------------------------------------------------------------
 *
 * AgentVvcChannelOnSendComplete --
 *
 *      Callback function for send complete on a vvc channel.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      See above.
 *
 *----------------------------------------------------------------------
 */

void
AgentVvcChannelOnSendComplete(VvcChannelHandle channel, // IN
                              VvcStatus status,         // IN
                              uint8 *buf,               // IN
                              size_t len,               // IN
                              void *clientData,         // IN
                              void *msgClientData,      // IN
                              uint32 msgId)             // IN
{
   Log(LGPFX "Inside AgentVvcChannelOnSendComplete\n");
}


/*
 *----------------------------------------------------------------------
 *
 * AgentVvcChannelOnDelivered --
 *
 *      Callback function for msg delivered to peer.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      See above.
 *
 *----------------------------------------------------------------------
 */

void
AgentVvcChannelOnDelivered(VvcChannelHandle channel, // IN
                           void *clientData,         // IN
                           void *msgClientData,      // IN
                           uint32 msgId)             // IN
{
   Log(LGPFX "Inside AgentVvcChannelOnDelivered\n");
}


/*
 *----------------------------------------------------------------------
 *
 * AgentVvcChannelOnRecv --
 *
 *      Callback function for data received on a channel.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      See above.
 *
 *----------------------------------------------------------------------
 */

void
AgentVvcChannelOnRecv(VvcChannelHandle channelHandle, // IN
                      uint32 flags,                   // IN
                      uint8 *buf,                     // IN
                      size_t len,                     // IN
                      void *clientData)               // IN
{
   BenevAgent *agent = (BenevAgent *)clientData;

   agent->vvcChannelOnRecv(channelHandle, buf, len);
}


/*
 *----------------------------------------------------------------------
 *
 * AgentVvcOnListenerConnectCb --
 *
 *      Callback function when listener is connected.
 *
 * Results:
 *       None
 *
 * Side effects:
 *      See above.
 *
 *----------------------------------------------------------------------
 */

void
AgentVvcOnListenerConnectCb(char *name,                       // IN
                            VvcListenerHandle listenerHandle, // IN
                            void *connectionCookie,           // IN
                            uint32 connectionCaps,            // IN
                            int32 sessionId,                  // IN
                            void *clientData)                 // IN
{
   Log(LGPFX "Inside vvcOnListenerConnectCb\n");

   BenevAgent *agent = (BenevAgent *)clientData;
   agent->vvcOnListenerConnectCb(name, listenerHandle, connectionCookie, connectionCaps, sessionId);
}


/*
 *----------------------------------------------------------------------
 *
 * AgentVvcOnListenerPeerOpenCb --
 *
 *      Callback function on a connection request for a listener.
 *
 * Results:
 *       None
 *
 * Side effects:
 *      See above.
 *
 *----------------------------------------------------------------------
 */

void
AgentVvcOnListenerPeerOpenCb(char *name,                       // IN
                             VvcListenerHandle listenerHandle, // IN
                             void *connectionCookie,           // IN
                             uint32 connectionCaps,            // IN
                             int32 sessionId,                  // IN
                             uint8 *initialData,               // IN
                             size_t initialDataLen,            // IN
                             void *clientData)                 // IN
{
   Log(LGPFX "Inside vvcOnListenerPeerOpenCb\n");
}


/*
 *----------------------------------------------------------------------
 *
 * AgentVvcOnListenerCloseCb --
 *
 *      Callback function when a listener is closed.
 *
 * Results:
 *       None
 *
 * Side effects:
 *      See above.
 *
 *----------------------------------------------------------------------
 */

void
AgentVvcOnListenerCloseCb(VvcListenerHandle listenerHandle, // IN
                          void *clientData)                 // IN
{
   Log(LGPFX "Inside vvcOnListenerCloseCb\n");
}


/*
 *----------------------------------------------------------------------
 *
 * BlastSocketConnectionOnAccept --
 *
 *      Callback function when a connection is accepted by BlastSocket
 *
 * Results:
 *       None
 *
 * Side effects:
 *      See above.
 *
 *----------------------------------------------------------------------
 */

Bool
BlastSocketConnectionOnAccept(const char *vauth,  // IN
                              int32 vvcSessionID, // IN
                              void *clientData)   // IN
{
   Log(LGPFX "Inside BlastSocketConnectionAcceptCB\n");

   BenevAgent *agent = (BenevAgent *)clientData;

   return agent->blastSocketConnectionAcceptCB(vauth, vvcSessionID);
}


/*
 *----------------------------------------------------------------------
 *
 * BenevAgent::BlastSocketConnectionOnClose --
 *
 *      Callback function after blast socket is closed once VVC Close
 *      sequence is complete.
 *
 * Results:
 *       None
 *
 * Side effects:
 *      See above.
 *
 *----------------------------------------------------------------------
 */

void
BenevAgent::BlastSocketConnectionOnClose(const char *vauth,               // IN
                                         int32 sessionId,                 // IN
                                         Bool isError,                    // IN
                                         VDPConnectionResult closeReason, // IN
                                         void *clientData)                // IN
{
   Log(LGPFX "Inside BlastSocketConnectionOnClose with reason - %d, "
             "isError - %d\n",
       closeReason, isError);
   BenevAgent *agent = (BenevAgent *)clientData;
   if (!agent) {
      Warning(LGPFX "Connection close callback is unexpectedly null\n");
      return;
   }
   agent->StopBlastSocketsAndVvc(closeReason);

   agent->mIsSocketClosed = true;
   if (agent->mCloseRequested) {
      std::map<std::string, std::string> stopSummary = {};
      stopSummary.insert({"msg", "agent closed successfully"});
      stopSummary.insert({"rawChannel", agent->mRawChannelStatus ? "true" : "false"});
      agent->GetController() << agent->MakeJsonResponse(agent->GetRequestId(), "close", "success",
                                                        stopSummary);
   }
}


/*
 *----------------------------------------------------------------------
 *
 * BlastSocketSessionStarted --
 *
 *      Callback function after cookie is created for a blast socket
 *      connection when first web socket upgrade request is handled.
 *
 * Results:
 *       None
 *
 * Side effects:
 *      See above.
 *
 *----------------------------------------------------------------------
 */

void
BlastSocketSessionStarted(const char *vAuth, // IN
                          void *clientData)  // IN
{
   Log(LGPFX "Inside BlastSocketSessionStarted\n");

   BenevAgent *agent = (BenevAgent *)clientData;

   agent->setTransportSwitchPolicy(vAuth);
   agent->setVvcDeferredAcksParams(vAuth);
}


/*
 *----------------------------------------------------------------------
 *
 * BenevAgent::blastSocketConnectionSocketError --
 *
 *      Handler for socket error on a blast socket connection.
 *
 * Results:
 *       None
 *
 * Side effects:
 *      See above.
 *
 *----------------------------------------------------------------------
 */

void
BenevAgent::setTransportSwitchPolicy(const char *vAuth) // IN
{
   BlastSocketTransportSwitchPolicyParams policy = mConfig.GetTransportSwitchPolicyFromConfig();

   BlastSocket_SetTransportSwitchPolicy(mAgentBlastSocketContext, vAuth, &policy);
}


/*
 *----------------------------------------------------------------------
 *
 * BenevAgent::setVvcDeferredAcksParams --
 *
 *      Set the VVC Deferred Acks parameters
 *
 * Results:
 *       None
 *
 * Side effects:
 *      See above.
 *
 *----------------------------------------------------------------------
 */

void
BenevAgent::setVvcDeferredAcksParams(const char *vAuth) // IN
{
   VvcDeferredAcksParams deferredAcksParams = {
      VVC_ENABLE_DEFERRED_ACKS_DEFAULT,  // enableDeferredAcks
      VVC_MPT_ACK_QUIET_PERIOD_DEFAULT,  // mptAckQuietPeriod
      VVC_MPT_ACK_UNACKED_BYTES_DEFAULT, // mptAckUnackedBytes
      VVC_MPT_ACK_SEQ_GAP_DEFAULT        // mptAckSeqGap
   };

   if (BlastSocket_SetVvcDeferredAcksParams(mAgentBlastSocketContext, vAuth, &deferredAcksParams)) {
      Log(LGPFX "Set Vvc DeferredAcks Parameters\n");
   } else {
      Warning(LGPFX "ERROR - Failed to set Vvc DeferredAcks Parameters\n");
   }
}


/*
 *----------------------------------------------------------------------
 *
 * BlastSocketSessionInvalidated --
 *
 *      Callback function after vAuth is deleted for a blast socket
 *      connection.
 *
 * Results:
 *       None
 *
 * Side effects:
 *      See above.
 *
 *----------------------------------------------------------------------
 */

void
BlastSocketSessionInvalidated(const char *vAuth,          // IN
                              VDPConnectionResult reason, // IN
                              void *clientData)           // IN
{
   Log(LGPFX "Inside BlastSocketSessionInvalidated\n");
}


/*
 *----------------------------------------------------------------------
 *
 * BenevAgent::blastSocketConnectionSocketError --
 *
 *      Handler for socket error on a blast socket connection.
 *
 * Results:
 *       None
 *
 * Side effects:
 *      See above.
 *
 *----------------------------------------------------------------------
 */

void
BenevAgent::blastSocketConnectionSocketError(int32 vvcSessionID) // IN
{
   Warning("Got view connection socket ERROR\n");
}


/*
 *----------------------------------------------------------------------
 *
 * BenevAgent::vvcOnChannelOpen --
 *
 *      Handler for vvc channel open callback.
 *
 * Results:
 *       None
 *
 * Side effects:
 *      See above.
 *
 *----------------------------------------------------------------------
 */

void
BenevAgent::vvcOnChannelOpen(VvcChannelHandle channel, // IN
                             VvcStatus status,         // IN
                             uint8 *initialData,       // IN
                             size_t initialDataLen)    // IN
{
   mChannel = channel;
   if (VVC_SUCCESS_RAW_CHAN_OPEN(status)) {
      mRawChannelStatus = true;
   }
}


/*
 *----------------------------------------------------------------------
 *
 * BenevAgent::vvcOnChannelClose --
 *
 *      Handler for vvc channel close callback.
 *
 * Results:
 *       None
 *
 * Side effects:
 *      See above.
 *
 *----------------------------------------------------------------------
 */

void
BenevAgent::vvcOnChannelClose(VvcChannelHandle channelHandle, // IN
                              VvcCloseChannelReason reason)   // IN
{
   Warning(LGPFX "VVC channel closed\n");
   mChannel = NULL;
}


/*
 *----------------------------------------------------------------------
 *
 * BenevAgent::vvcChannelOnRecv --
 *
 *      Handler for vvc channel recv callback.
 *
 * Results:
 *       None
 *
 * Side effects:
 *      See above.
 *
 *----------------------------------------------------------------------
 */

void
BenevAgent::vvcChannelOnRecv(VvcChannelHandle channelHandle, // IN
                             uint8 *buf,                     // IN
                             size_t len)                     // IN
{
   VVCLIB_RecvComplete(channelHandle, buf);
}


/*
 *----------------------------------------------------------------------
 *
 * BenevAgent::vvcOnListenerConnectCb --
 *
 *      Handler for vvc listener connect callback.
 *
 * Results:
 *       None
 *
 * Side effects:
 *      See above.
 *
 *----------------------------------------------------------------------
 */

void
BenevAgent::vvcOnListenerConnectCb(char *name,                       // IN
                                   VvcListenerHandle listenerHandle, // IN
                                   void *connectionCookie,           // IN
                                   uint32 connectionCaps,            // IN
                                   int32 sessionId)                  // IN
{
   VvcChannelEvents channelEvents;

   memset(&channelEvents, 0, sizeof channelEvents);
   channelEvents.onOpen = AgentVvcOnChannelOpen;
   channelEvents.onClose = AgentVvcOnChannelClose;
   channelEvents.onSendComplete = AgentVvcChannelOnSendComplete;
   channelEvents.onDelivered = AgentVvcChannelOnDelivered;
   channelEvents.onRecv = AgentVvcChannelOnRecv;

   /*
    * VVC_CHANNEL_EXTENSION_RECV_BUFFER is not a requirement for raw channels,
    * but it helps extend code coverage and mimics blast - mks on the server side.
    */
   uint32 flags = (mEnableRawChannel
                      ? VVC_CHANNEL_EXTENSION_RECV_BUFFER | VVC_CHANNEL_DECLINE_NC | VVC_CHANNEL_RAW
                      : 0);

   VvcStatus status = VVCLIB_OpenChannel(listenerHandle, name, connectionCookie, &channelEvents,
                                         VVC_TRAFFIC_TYPE_REAL_TIME_2, // PRIORITY
                                         -1,                           // TIMEOUT
                                         flags,                        // FLAGS
                                         NULL,                         // INITIAL DATA
                                         0,                            // INITIAL LEN
                                         (void *)this, &mChannelId);
   if (!VVC_SUCCESS(status)) {
      Warning(LGPFX "ERROR - Creation of channel failed\n");
   } else {
      Log(LGPFX "Creation of channel Succeeded\n");
   }
}


/*
 *----------------------------------------------------------------------
 *
 * BenevAgent::blastSocketConnectionAcceptCB --
 *
 *      Handler for blast socket connection accepted callback.
 *
 * Results:
 *       None
 *
 * Side effects:
 *       See above.
 *
 *----------------------------------------------------------------------
 */

Bool
BenevAgent::blastSocketConnectionAcceptCB(const char *vauth,  // IN
                                          int32 vvcSessionID) // IN
{
   mVvcSessionID = vvcSessionID;
   // Create a VVC listener
   VvcListenerEvents listenerEvents;
   memset(&listenerEvents, 0, sizeof listenerEvents);
   listenerEvents.onConnect = AgentVvcOnListenerConnectCb;
   listenerEvents.onPeerOpen = AgentVvcOnListenerPeerOpenCb;
   listenerEvents.onClose = AgentVvcOnListenerCloseCb;

   string listenerName = "Test-listener";
   VvcStatus status = VVCLIB_CreateListener(VVC_PLUGIN_ID_CORE_PROTOCOL, mVvcSessionID,
                                            (char *)listenerName.c_str(), //"Test-listener",
                                            &listenerEvents, (void *)this, &mListener);
   if (!VVC_SUCCESS(status)) {
      Warning(LGPFX "ERROR - VVCLIB_CreateListener failed\n");
   } else {
      Log(LGPFX "VVCLIB_CreateListener Succeeded with Test-listener\n");
   }

   // Activate the listener
   status = VVCLIB_ActivateListener(mListener);
   if (!VVC_SUCCESS(status)) {
      Warning(LGPFX "ERROR - failed to Activate the listener\n");
      return FALSE;
   } else {
      Log(LGPFX "Could activate listener\n");
   }

   return TRUE;
}


/*
 *----------------------------------------------------------------------
 *
 * BenevAgent::StopBlastSocketsAndVvc --
 *
 *      Close BlastSocket and call StopVvc()
 *
 * Results:
 *       None
 *
 * Side effects:
 *      See above.
 *
 *----------------------------------------------------------------------
 */

void
BenevAgent::StopBlastSocketsAndVvc(VDPConnectionResult closeReason)
{
   Log(LGPFX "Closing with reason: %d\n", closeReason);

   StopVvc();

   if (mVvcSessionID != VVC_INVALID_SESSIONID) {
      /*
       * BlastSocket_StopVvcSession() will do a graceful close of VvcSession.
       * As part of VVC Close Seq, vAuth will be invalidated during the
       * Force-Close part when flushing of messages is done.
       */
      BlastSocket_StopVvcSession(mAgentBlastSocketContext, mVvcSessionID, mVAuth.c_str(),
                                 closeReason);
      mVvcSessionID = VVC_INVALID_SESSIONID;
   }
}


/*
 *----------------------------------------------------------------------
 *
 * BenevAgent::CloseConnection --
 *
 *      Close down the agent side of the connection
 *
 * Results:
 *       None
 *
 * Side effects:
 *      See above.
 *
 *----------------------------------------------------------------------
 */

void
BenevAgent::CloseConnection(const string &reqId)
{
   Log(LGPFX "Closing the Agent as blast listener\n");
   StopBlastSocketsAndVvc(VDPCONNECT_SERVER_DISCONNECTED);
   Log(LGPFX "Closed the Agent as blast listener\n");

   std::map<std::string, std::string> stopSummary = {};
   stopSummary.insert({"msg", "agent closed successfully"});
   stopSummary.insert({"rawChannel", mRawChannelStatus ? "true" : "false"});
   if (mIsSocketClosed) {
      GetController() << MakeJsonResponse(reqId, "close", "success", stopSummary);
   } else {
      mRequestId = reqId;
      mCloseRequested = true;
   }
}


/*
 *----------------------------------------------------------------------
 *
 * BenevAgent::ExitConnection --
 *
 *      Stop listening for connections for Blast Socket Agent
 *
 * Results:
 *       None
 *
 * Side effects:
 *      See above.
 *
 *----------------------------------------------------------------------
 */

void
BenevAgent::ExitConnection(void)
{
   if (!mAgentBlastSocketContext) {
      Log(LGPFX "Exiting without Starting\n");
   } else {
      Log(LGPFX "Stopping Agent\n");
      BlastSocket_Stop(mAgentBlastSocketContext);
      Log(LGPFX "Stopped Agent\n");
   }

   if (mLoadedSSLLib) {
      mLoadedSSLLib = false;
      SSL_Exit();
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * HandleHandoffSocketCb
 *
 *    Callback from webSocket layer when the registered ALPN prefix is matched
 *    with the one in the TLS handshake (ClientHello) message.
 *
 *    This is to simulate the handoff connected socket from service to worker via
 *    ClientSocketFactory::HandoffConnectedSocketCb -> ServerAsyncSocket::ProcessWinSocketInfo.
 *
 *    Since Benev doesn't do cross-process socket handoff, this function just
 *    uses the existing socket to call into AsyncSocket_AcceptSSL and
 *    BlastSocketConnectionAccepted.
 *
 * Results:
 *    Success (0) or failure.
 *
 *-----------------------------------------------------------------------------
 */

extern "C" int
HandleHandoffSocketCb(AsyncSocket *asock, // IN
                      void *clientData,   // IN
                      void *buf,          // IN
                      uint32 bufLen,      // IN
                      uint32 currentPos)  // IN
{
   ASSERT(asock);
   ASSERT(clientData);
   ASSERT(buf);

   /*
    * Hardcode on-wire ALPN length, until need arises to pass it as a parameter
    * to this callback to accommodate more ALPN lengths.
    */
   int encodedALPNSuffixLen = BLAST_SOCKET_TLS_ALPN_SFX_ENCODELEN;

   ASSERT(bufLen >= currentPos + encodedALPNSuffixLen);

   if (bufLen < currentPos + encodedALPNSuffixLen) {
      Warning(LGPFX "ERROR - Invalid encoded ALPN suffix\n");
      return -1;
   }

   const char *alpnEncoded = (char *)buf + currentPos;
   size_t decodeLength = Base64_DecodedLength(alpnEncoded, encodedALPNSuffixLen);

   size_t alpnSuffixLen;
   uint8 *decodedAlpnSuffix = (uint8 *)Util_SafeCalloc(1, decodeLength);

   if (!Base64_Decode(alpnEncoded, decodedAlpnSuffix, decodeLength, &alpnSuffixLen)) {
      free(decodedAlpnSuffix);
      Warning(LGPFX "ERROR - Invalid ALPN suffix, unable to decode\n");
      return -1;
   }

   // ALPN suffix prior to encoding was nonce (4B) + serialNo (1B), extract them
   ASSERT(alpnSuffixLen == BLAST_SOCKET_TLS_ALPN_SUFFIX_LEN);

   uint32 nonce = ntohl(*(uint32 *)decodedAlpnSuffix);
   unsigned char serialNo = decodedAlpnSuffix[sizeof(nonce)];
   free(decodedAlpnSuffix);

   AsyncSocketPollParams pollParams = {0};

   pollParams.pollClass = POLL_CS_MAIN;
   pollParams.lock = MXUser_CreateRecLock("WorkerSocket", RANK_UNRANKED);

   int error = ASOCKERR_GENERIC;
   int fd = AsyncSocket_GetFd(asock);

   if ((asock = AsyncSocket_AttachToFd(fd, &pollParams, &error)) == NULL) {
      Warning(LGPFX "ERROR - Failed to wrap socket in asyncsocket, error: %s",
              AsyncSocket_Err2String(error));
#ifdef _WIN32
      closesocket(fd);
#else
      close(fd);
#endif
      return -1;
   }

   BlastSocketContext *blastSocketContext = static_cast<BlastSocketContext *>(clientData);

   if (!AsyncSocket_AcceptSSL(asock, blastSocketContext->params.sslCtx)) {
      Warning(LGPFX "ERROR - SSL Accept failed\n");
      AsyncSocket_Close(asock);
      return -1;
   }

   BlastSocketConnectionAccepted(FALSE, asock, blastSocketContext, serialNo);

   return 0;
}

/*
 *----------------------------------------------------------------------
 *
 * BenevAgent::StartConnection --
 *
 *      Start listening for connections for Blast Socket Agent
 *      based on appropriate listen params.
 *
 * Results:
 *       None
 *
 * Side effects:
 *       See above.
 *
 *----------------------------------------------------------------------
 */

void
BenevAgent::StartConnection(const StartConnectionParams &startParams)
{
   // Call BlastSocket_Start
   int tcpPort = startParams.tcpPort;
   int udpPort = startParams.udpPort;
   BlastSocketContext *agentBlastSocketContext;
   BlastSocketParams blastSocketParams;
   memset(&blastSocketParams, 0, sizeof blastSocketParams);

   if (udpPort == 0) {
      udpPort = tcpPort;
   }

#ifdef _WIN32
   char userName[UNLEN + 1];
   DWORD userMameLen = sizeof(userName);
   /*
    * In order to test new vvc2 hub/node BenevAgent needs
    * to run under SYSTEM account for ipc to work.
    */
   ::GetUserName(userName, &userMameLen);
   Log(LGPFX "Running under %s account\n", userName);

   if (0 == std::strcmp(userName, "SYSTEM")) {
      if (!AddConnectionToSessionMonitor()) {
         Warning(LGPFX "ERROR - AddConnectionToSessionMonitor FAILED\n");
         return;
      }
   }
#endif

   Log(LGPFX
       "StartConnection: protocol %s, tcpPort %d, udpPort %d, useSSL %d, enableRawChannel %d\n",
       startParams.protocol.c_str(), tcpPort, udpPort, startParams.useSSL,
       startParams.enableRawChannel);

   blastSocketParams.udpEnabled = TRUE;
   if (startParams.protocol == "TCP") {
      blastSocketParams.udpEnabled = FALSE;
   }

   mEnableRawChannel = startParams.enableRawChannel;

   blastSocketParams.bweEnabled = mConfig.enableBlastNetworkTcpBwe;
   blastSocketParams.localNCEnabled = mConfig.enableBlastNetworkContinuity;
   blastSocketParams.serverUdpExternalPort = udpPort;
   blastSocketParams.localVVCCloseSeqEnabled = mConfig.enableBlastNetworkVVCCloseSeq;
   blastSocketParams.localNetworkIntelligenceEnabled = mConfig.enableBlastNetworkIntelligence;
   if (startParams.protocol == "BEAT") {
      blastSocketParams.localNetworkIntelligenceEnabled = FALSE;
   }
   blastSocketParams.localVVCPauseResumeEnabled = mConfig.enableBlastNetworkVVCPauseResume;
   blastSocketParams.blastSocketThreadEnabled = mConfig.enableBlastNetworkThread;
   blastSocketParams.isVvcPluginLoaderNeeded = mConfig.enableBlastNetworkVvcPluginLoader;
   blastSocketParams.localVVCQoSPolicyEnabled = mConfig.enableBlastNetworkVVCQoSPolicy;
   blastSocketParams.localVVCBatchingEnabled = mConfig.enableBlastNetworkVVCBatching;
   blastSocketParams.legacyBlastWSProtocolsAllowed = mConfig.allowBlastNetworkLegacyWSProtocols;
   blastSocketParams.perfMonEnabled = TRUE;
   blastSocketParams.maxCookieIdleTimeSec = 120;
   blastSocketParams.cookieCleanupIntervalMS = 1000;
   // Convert Kilobits to bytes
   blastSocketParams.maximumRate = mConfig.maxBandwidthKbps * 1000 / 8;

   void *sslCtx = NULL;

#ifdef _WIN32
   if (startParams.useSSL && mSSLManager != nullptr) {
      sslCtx = mSSLManager->GetSSLContext();
      blastSocketParams.udpSSLEnabled = TRUE;
   }
#endif

   bool useSSL = (sslCtx != NULL);

   if (!useSSL && startParams.alwaysLoadSSLLib) {
      // We aren't connecting with SSL, but we still need the SSL library
      SSL_Init(NULL, DEFAULT_SSLLIBDIR, CONFIG_HORIZONSSLDIR);
      mLoadedSSLLib = true;
   }

   std::ostringstream outputLogStream;
   outputLogStream << "Benev agent starting in " << (useSSL ? "" : "non-") << "SSL mode." << endl;

   Log(LGPFX "%s", outputLogStream.str().c_str());
   cout << outputLogStream.str();

   agentBlastSocketContext = BlastSocket_Start(
      sslCtx, useSSL, startParams.host.c_str(), &tcpPort, &udpPort, -1, &blastSocketParams,
      BlastSocketConnectionOnAccept, BlastSocketConnectionOnClose, BlastSocketSessionStarted,
      BlastSocketSessionInvalidated, (void *)this);

   if (agentBlastSocketContext == NULL) {
      Warning(LGPFX "ERROR - BlastSocket_Start FAILED\n");
      mController << MakeJsonResponse(startParams.reqId, "start", "fail",
                                      "BlastSocket_Start FAILED");
      return;
   }

   mAgentBlastSocketContext = agentBlastSocketContext;

   Log(LGPFX "BlastSocket_Start SUCCESS. Listening port = %d\n", tcpPort);
   mController << MakeJsonResponse(startParams.reqId, "start", "success", "vauth: " + mVAuth);

   SetupVAuth(mVAuth);

   Log(LGPFX "Called BlastSocket_Start\n");
}


/*
 *----------------------------------------------------------------------
 *
 * BenevAgent::SetupVAuth --
 *
 *      Set or generate a vAuth. Meaning of "vauth" parameter:
 *
 *      "<generate>"         - generate a new vauth dynamically
 *      ""                   - use the default,
 *      valid length string  - override the default.
 *
 * Results:
 *       None
 *
 * Side effects:
 *      The vAuth is saved in BenevAgent::mVAuth
 *
 *----------------------------------------------------------------------
 */

void
BenevAgent::SetupVAuth(const string &vAuth)
{
   if (vAuth == "<generate>") {
      // The special value means to generate a vAuth token
      char *vAuthTmp = NULL;
      if (!BlastSocket_HandleNewAuthRequest(FALSE, // isShadowSession
                                            mAgentBlastSocketContext, NULL, &vAuthTmp)) {
         Warning(LGPFX "ERROR - BlastSocket_HandleNewAuthRequest FAILED\n");
         return;
      }

      mVAuth = vAuthTmp;
      cout << "Using vAuth=" << mVAuth << endl;
      return;
   }

   if (vAuth.empty()) {
      // Just the existing use mVAuth
   } else if (vAuth.size() == VAUTH_TOKEN_LEN) {
      mVAuth = vAuth;
   } else {
      Log(LGPFX "Invalid vAuth value: '%s'\n", vAuth.c_str());
      return;
   }

   // Call to set the vAuth token
   Bool ret = BlastSocket_SaveAuthToken(mVAuth.c_str(), mAgentBlastSocketContext, FALSE);
   if (ret) {
      Log(LGPFX "Set of vAuth token as: %s complete\n", mVAuth.c_str());
   } else {
      Warning(LGPFX "ERROR - Setting of vAuth token FAILED\n");
   }
}


/*
 *----------------------------------------------------------------------
 *
 * BenevAgent::Shutdown --
 *
 *      Shutdown MFW and clean up resources.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      No code using MFW can run after this call.
 *
 *----------------------------------------------------------------------
 */

void
BenevAgent::Shutdown()
{
#ifdef _WIN32
   MessageFrameWork::Shutdown();
   corerunnable::waitForAllThreads();
#endif
}


/*
 *----------------------------------------------------------------------
 *
 * BenevAgent::AddConnectionToSessionMonitor --
 *
 *      Add a new connection to Session Monitor.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------
 */
#ifdef _WIN32
bool
BenevAgent::AddConnectionToSessionMonitor()
{
   /*
    * Open Session Monitor, which needs to be kept alive
    * across the lifecycle of process in order to preserve
    * the connection to the underline usm driver service.
    */
   static SessionMonitor sm;
   unsigned int platformStatusCode;

   auto smStatus = sm.open(&platformStatusCode);
   if (smStatus != SmStatusCode::Success) {
      Warning(LGPFX "Failed to open Session Monitor, "
                    "platformStatusCode: %d, smStatus: %d\n",
              platformStatusCode, smStatus);
      return false;
   }

   // Enable queue events
   smStatus = sm.setEventQueueState(true);
   if (smStatus != SmStatusCode::Success) {
      Warning(LGPFX "Failed to set event queue state, smStatus: %d\n", smStatus);
      return false;
   }

   // Add a new connection to session monitor
   DWORD currSessionId = 0;
   if (!ProcessIdToSessionId(GetCurrentProcessId(), &currSessionId)) {
      Warning(LGPFX "Failed to get current session id.\n");
      return false;
   }

   GUID smConnectionId = {0};
   auto addTrx = make_shared<SmTrx>();
   auto addOp = addTrx->appendOp(SmOpType::Add, sm.getConnectionsColId(), SmColKey(smConnectionId));

   uint32_t winSessionId = currSessionId;
   auto acl = addOp->getAcl();
   acl->setAccessMask(OpAccessEveryoneRead);
   acl->addAllowProcessSessionId(winSessionId);

   auto addValues = addOp->getValues();
   addValues->set(SM_CONNECTION_STATE, (uint32_t)SmConnectionState::Connected);
   addValues->set(SM_PROTOCOL, (uint32_t)SmProtocol::Blast);
   addValues->set(SM_WIN_SESSION_ID, winSessionId);
   addValues->set(SM_SESSION_TYPE, (uint32_t)SmSessionType::Application);
   addValues->set(SM_USER_DN, CEDAR_S("cn=user1.dn=test,dn=local"));
   addValues->set(SM_BLAST_AUTH_TOKEN, CEDAR_S("qwertyuiopasdfghjklzxcvbnm1234567890qwer"));

   smStatus = sm.submitTrx(addTrx);
   if (smStatus != SmStatusCode::Success && smStatus != SmStatusCode::AlreadyExists) {
      Warning(LGPFX "Failed to add Connection Item, smStatus: %d\n", smStatus);
      return false;
   }

   Log(LGPFX "Successfully added a new connection to Session Monitor. "
             "smStatus: %d\n",
       smStatus);
   return true;
}
#endif


/*
 *----------------------------------------------------------------------
 *
 * BenevAgent::CreateSSLManager --
 *
 *      Create BenevAgentSSLManager to handle Benev Agent SSL
 *      initializations and configurations.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      Support Windows only for now.
 *
 *----------------------------------------------------------------------
 */

void
BenevAgent::CreateSSLManager(const std::string &certFilePathName /*= ""*/,
                             bool checkRevocation /*= false*/, bool enableCrlCache /*= false*/)
{
#ifdef _WIN32
   mSSLManager = std::make_unique<BenevAgentSSLManager>(certFilePathName);
#endif
}
