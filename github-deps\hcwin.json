{"pcoip-soft-clients": {"repository": "euc-ra/pcoip-soft-clients", "workflow": "pcoip-soft-clients", "branch": "main", "change": "80cf18f1a4594f6afa4eb84612e69532abbecd5c", "buildtypes": "release", "job": "build-pcoip-soft-clients-winhzn-gh", "unzipFiles": "True"}, "pcoip-soft-interface": {"repository": "euc-ra/pcoip-soft-server", "workflow": "pcoip-soft-interface", "job": "build-pcoip-soft-interface", "buildtypes": "release", "branch": "v4.1.0", "change": "db6bca551a9da828f5cb41dd9fece193da8d090c", "unzipFiles": "True"}, "pcoip-soft-server": {"repository": "euc-ra/pcoip-soft-server", "workflow": "pcoip-soft-server", "branch": "main", "change": "5379ce7e6e03f6ad167608333f5cb7fab4a3efc9", "job": "build-pcoip-soft-server-winhzn-gh", "unzipFiles": "True", "buildtypes": {"obj": "beta", "beta": "beta", "release": "release"}}, "cart-webrtc": {"repository": "euc-vdi/cart-webrtc", "workflow": "cart_webrtc", "branch": "main", "change": "09d0be2d5f94b1a5b3fedc21c4f16b4e7d11487a", "job": "build-cart-webrtc-win", "unzipFiles": "True", "buildtypes": {"beta": "beta", "obj": "obj", "opt": "release", "release": "release"}}, "windows-installkit": {"repository": "euc-vdi/windows-installkit", "workflow": "windows-installkit", "branch": "master", "change": "d31d2e8d45a991d8d221d095f2f4eb1210cbc621", "job": "build-windows-installkit", "buildtypes": {"beta": "beta", "obj": "obj", "opt": "beta", "release": "beta"}}, "euc_endpoint-telemetry-sdk": {"repository": "euc-uem/endpoint-telemetry-yukon", "workflow": "tlm-win", "branch": "main", "change": "4fe8d8b07d660602cb973584e28dfaa685a4b637", "job": "build", "unzipFiles": "False", "buildtypes": {"beta": "release", "obj": "obj", "opt": "release", "release": "release"}}, "msteamsapi": {"repository": "euc-eng/msteamsapi", "workflow": "m<PERSON><PERSON><PERSON><PERSON>", "branch": "master", "change": "3074e2bed7868ce54cd71517c81bfc7477a8cfc4", "job": "build-msteamsapi-winhzn-gh", "buildtypes": "release", "unzipFiles": "False"}}