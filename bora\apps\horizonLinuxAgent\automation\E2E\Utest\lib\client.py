# -*- coding: cp1252 -*-
# Copyright (c) Omnissa, LLC. All rights reserved.
# This product is protected by copyright and intellectual property laws in the
# United States and other countries as well as by international treaties.
# -- Omnissa Restricted
import subprocess, time, os, re, sys, socket, shlex
import lib.kits
import lib.seleniumlib
import lib.findPic as pic
import lib.audio as audio
import rpyc
from pathlib import Path
from PIL import Image
from datetime import datetime
from zipfile import ZipFile, ZIP_DEFLATED, ZIP_BZIP2, ZIP_LZMA
from threading import Thread
import shutil
import ast
import unittest
import platform
import mmap
import pyscreeze

kits = lib.kits.kits
cached_values = lib.kits.cached_values
seleniumlib = lib.seleniumlib.seleniumlib
isipv6 = lib.auto.isipv6

if 'darwin' in sys.platform:
    from Quartz import (
        CGWindowListCopyWindowInfo,
        kCGWindowListOptionOnScreenOnly,
        kCGNullWindowID
    )
    import plistlib
elif 'win32' in sys.platform:
    import winreg
    import win32gui
    import win32con
    import win32print
else:
    from .usbif import *

try:
    import pyautogui
    pyautogui.FAILSAFE=False
except:
    print('No pyautogui found')

class Client():
    DEFAULTDESKTOPSIZE='1024x768'
    def __init__(self):
        self.P = None
        self.tsharkThread = None
        self.remoteP = None
        self.cache = {}
        self.cache['connectTS'] = None
        self.smartcard = False
        self.borderSet = False
        self.windowBorderWidth = 0
        self.windowBorderHeight = 20
        self.hidDevs = []
        self.storageDevs = []
        self.ip = 'localhost'
        self.remote = False
        if 'darwin' in sys.platform:
            self.os = 'Mac'
        elif 'linux' in sys.platform:
            self.os = 'Linux'
        else:
            self.os = 'Windows'
        self.node = platform.node()
        self.nameMac       = 'Omnissa Horizon Client'
        self.bundleIdMac   = 'com.omnissa.horizon.client.mac'
        self.bundlePathMac = '/Applications/Omnissa Horizon Client.app/'

    def Update(self, ipAddr='localhost', broker='localhost', uag='localhost'):
        self.broker = broker
        self.uag = uag
        self.ip = ipAddr
        if self.ip in ('localhost', '127.0.0.1'):
            self.remote = False
        else:
            self.remote = True

    def __str__(self):
        return f"IP: {self.ip}, remote: {self.remote}"

    def connect(self):
        if self.remote:
            for _ in range(10):
                try:
                    conn = rpyc.classic.connect(self.ip)
                    rsys = conn.modules.sys
                    rplatform = conn.modules.platform
                    self.node = rplatform.node()
                    if 'darwin' in rsys.platform:
                        self.os = 'Mac'
                    elif 'linux' in rsys.platform:
                        self.os = 'Linux'
                    else:
                        self.os = 'Windows'
                    conn.close()
                    break
                except:
                    time.sleep(10)
                    continue

    def GetCmdOutput(self, cmd, printOut=True, shell=True, cwd=None, env=None):
        """
        Run the cmd and wait for finish on client,
        then ret the output

        @Input:
          cmd, command
          printOutput, True or False to print output
          cwd, working directory
          env, working environment
        """
        comment = 'GetCmdOutput: %s' %cmd
        kits.Comment(comment)
        if shell:
            output, err = subprocess.Popen(cmd,
                                           cwd = cwd,
                                           env = env,
                                           stdout = subprocess.PIPE,
                                           stderr = subprocess.PIPE,
                                           shell = True).communicate()
        else:
            cmd = shlex.split(cmd)
            output, err = subprocess.Popen(cmd,
                                           cwd = cwd,
                                           env = env,
                                           stdout = subprocess.PIPE,
                                           stderr = subprocess.PIPE).communicate()

        if printOut:
            print('GetCmdOutput: ', cmd)
            print(output, err)
        if err =='':
            return output.decode()
        else:
            return output.decode() + err.decode()

    def RunCmd(self, cmd, shell=True, cwd=None, env=None, timeout=60):
        """
        Run the cmd and do not wait for finish on client,
        just return the child process
        @Input:
          cwd, working directory
        """
        comment = 'RunCmd: ' + cmd
        kits.Comment(comment)
        if shell:
            subprocess.run(cmd,
                           cwd=cwd,
                           shell=True)
        else:
            cmd = shlex.split(cmd)
            subprocess.call(cmd)

    def __WinClient_Launch_autogui(self, *args, **kwargs):
        """
        make sure horizon-client.exe under $env:path
        """
        desktopSizes = {"small": "windowSmall",
                        "large": "windowLarge",
                        "full": "fullscreen",
                        "all": "multimonitor",
                        "640x480": "640x480",
                        "800x600": "800x600",
                        "1024x768": "1024x768"}

        cmd = 'horizon-client.exe '
        for arg in args:
            cmd = cmd + arg + ' '
        for i in list(kwargs.keys()):
            if i == "desktopSize":
                temp = i
                i = "desktopLayout"
                kwargs[i] = desktopSizes.get(kwargs[temp])
            if i == "protocol":
                temp = i
                i = "desktopProtocol"
                kwargs[i] = kwargs[temp]
            cmd = '%s -%s %s ' % (cmd, i, kwargs[i])
        print(('Launch Win Client: ', cmd))

        key = 'firstRun'
        if (key in self.cache) == False:
            self.cache[key] = True
        print(cmd)

        pyautogui.hotkey('winleft', 'r')
        kits.Wait(1)
        pyautogui.typewrite(cmd)
        pyautogui.typewrite(['enter'])

    def __WinClient_Launch(self, *args, **kwargs):
        """
        make sure horizon-client.exe under $env:path
        """
        CREATE_NEW_PROCESS_GROUP = 0x00000200
        DETACHED_PROCESS = 0x00000008
        flags = DETACHED_PROCESS | CREATE_NEW_PROCESS_GROUP
        desktopSizes = {"small": "windowSmall",
                        "large": "windowLarge",
                        "full": "fullscreen",
                        "all": "multimonitor",
                        "640x480": "640x480",
                        "800x600": "800x600",
                        "1024x768": "1024x768"}

        cmd = 'horizon-client.exe'
        for arg in args:
            cmd = cmd + arg + ' '
        for i in list(kwargs.keys()):
            if i == "desktopSize":
                temp = i
                i = "desktopLayout"
                kwargs[i] = desktopSizes.get(kwargs[temp])
            if i == "protocol":
                temp = i
                i = "desktopProtocol"
                kwargs[i] = kwargs[temp]
            cmd = '%s -%s %s ' % (cmd, i, kwargs[i])
        print(('Launch Win Client: ', cmd))

        key = 'firstRun'
        if (key in self.cache) == False:
            self.cache[key] = True

        # kits.Comment(cmd)
        cmd = shlex.split(cmd)
        subprocess.CREATE_NEW_CONSOLE
        self.cache['connectTS'] = time.strftime("%Y-%m-%dT%H:%M:%S", time.localtime())
        # kits.Comment(f"launch {self.os} client at {self.cache['connectTS']}")
        self.cache['connStart'] = time.time()
        self.P = subprocess.Popen(cmd, close_fds=True, creationflags=flags)

    def __WinClient_SetSecurityMode(self, modeValue = '0'):
        """
        "0", no check
        "1", warn
        "2", full checking
        """
        modePath = '"HKLM\Software\Policies\VMware, Inc.\VMware VDM\Client\Security"'
        modeKey = "certcheckmode"
        cmd = 'reg add %s /v %s /d %s /t REG_SZ /f' %(modePath, modeKey, modeValue)
        self.GetCmdOutput(cmd)

    def __WinClient_SetVChanTraceLevel(self):
        modePath = '"HKLM\Software\Omnissa\Horzion\Client\vvc"'
        # for vvc trace log level
        modeKey = "logLevel"
        modeValue = "trace"
        cmd = 'reg add %s /v %s /d %s /t REG_SZ /f' %(modePath, modeKey, modeValue)
        self.GetCmdOutput(cmd, shell=False)
        time.sleep(0.1)
        # for debug log level
        modeKey = "debugLogLevel"
        modeValue = "7"
        cmd = 'reg add %s /v %s /d %s /t REG_SZ /f' %(modePath, modeKey, modeValue)
        self.GetCmdOutput(cmd, shell=False)
        time.sleep(0.1)
        # for packet trace log level
        modeKey = "pktTraceLevel"
        modeValue = "all"
        cmd = 'reg add %s /v %s /d %s /t REG_SZ /f' %(modePath, modeKey, modeValue)
        self.GetCmdOutput(cmd, shell=False)
        time.sleep(0.1)
        # for memory log level
        modeKey = "memLogLevel"
        modeValue = "all"
        cmd = 'reg add %s /v %s /d %s /t REG_SZ /f' %(modePath, modeKey, modeValue)
        self.GetCmdOutput(cmd, shell=False)
        time.sleep(0.1)


    def SetDEMConfig(self, DEMNetworkPath, Item):
        if 'win32' in sys.platform:
            self.__WinClient_SetDEMCfg(DEMNetworkPath, Item)
        else:
            pass

    def __WinClient_SetDEMCfg(self, DEMNetworkPath, Item):
        import xml.etree.ElementTree as ET
        SubElement = ET.SubElement
        CfgPath = DEMNetworkPath + '\\general\\FlexRepository\\Horizon\\{}.xml'.format(Item)
        pref = os.path.normpath(CfgPath)
        if os.path.exists(pref):
            os.remove(pref)
        rootPrefs = (r'<?xml version="1.0" encoding="UTF-8"?><userEnvironmentSettings> </userEnvironmentSettings>')
        with open(pref, "w") as f:
            f.write(rootPrefs)
        time.sleep(1)
        tree = ET.parse(pref)
        root = tree.getroot()

        ClientIp = self.GetClientHostIP()
        IPrange = {}
        IPrange['a'] = IPrange['A'] = ClientIp.split('.')[0]
        IPrange['b'] = IPrange['B'] = ClientIp.split('.')[1]
        IPrange['c'] = IPrange['C'] = ClientIp.split('.')[2]
        IPrange['d'] = IPrange['D'] = ClientIp.split('.')[3]

        settingItem = {}
        osName = {}
        settingItem['driveRedirection'] = "1"
        settingItem['clipboard'] = "1"
        settingItem['usb'] = "1"
        settingItem['type'] = "horizon"
        osName['os'] = "linux"

        condition = SubElement(root, 'conditions')
        SubElement(condition, 'os', attrib=osName)
        SubElement(condition, 'tsip', attrib=IPrange)
        SubElement(root, 'setting', attrib=settingItem)
        prefContent = ET.dump(root)
        kits.Comment(prefContent)
        tree.write(pref)

    def __WinClient_SetCfgPref(self, udp=None, h264="true"):
        """
        """
        cdrFolder = "C:\\CDR"
        pdfFolder = "C:\\PDF"
        shutil.rmtree(cdrFolder, ignore_errors=True)
        shutil.rmtree(pdfFolder, ignore_errors=True)
        import xml.etree.ElementTree as ET
        SubElement = ET.SubElement
        rootPrefs = (r'<?xml version="1.0"?><Root> </Root>')
        prefPath = ''
        if not self.remote:
            prefPath = os.environ['appdata'] + '\\Omnissa\\Omnissa Horizon Client\\prefs.txt'
            if not os.path.exists(cdrFolder):
                os.makedirs(cdrFolder)
        else:
            prefPath = os.path.join(os.path.expanduser('~'), f'{os.getpid()}-prefs.txt')
        pref = os.path.normpath(prefPath)
        rootPrefs = (r'<?xml version="1.0"?><Root> </Root>')
        with open(pref, "w") as f:
            f.write(rootPrefs)
        time.sleep(1)
        tree = ET.parse(pref)
        root = tree.getroot()
        sharingAttrib = {}
        sharingItem = {}
        blastAttrib = {}
        securityAttrib = {}
        sharingAttrib['allowAccessRemovable'] = "false"
        sharingAttrib['shareHomeDirectory'] = "false"
        sharingAttrib['showPromptDlg'] = "false"
        sharingItem['from'] = "C:\CDR"
        sharingItem['to'] = "CDR"
        blastAttrib['allowClientBlastCodec'] = h264
        blastAttrib['allowClientH264'] = h264
        blastAttrib['allowClientH264YUV444'] = h264
        blastAttrib['allowClientHDR'] = h264
        blastAttrib['allowClientHEVC'] = h264
        blastAttrib['allowClientHDR'] = h264
        if udp:
            blastAttrib['ConnectionUserMode'] = udp
        shareList = SubElement(root, 'sharingList', attrib=sharingAttrib)
        SubElement(shareList, 'sharingItem', attrib=sharingItem)
        SubElement(root, 'BlastSettings', attrib=blastAttrib)
        securityAttrib['certCheckMode'] = "2"
        SubElement(root, 'SecurityMode', attrib=securityAttrib)
        prefContent = ET.dump(root)
        tree.write(pref)

        if self.remote:
            conn = rpyc.classic.connect(self.ip)
            ros  = conn.modules.os
            if not ros.path.exists(cdrFolder):
                ros.makedirs(cdrFolder)
            rprefPath = ros.environ['appdata'] + '\\Omnissa\\Omnissa Horizon Client\\prefs.txt'
            rprefPath = ros.path.normpath(rprefPath)
            if ros.path.exists(rprefPath):
                ros.remove(rprefPath)
            rpyc.utils.classic.upload(conn=conn, remotepath=rprefPath, localpath=pref)
            time.sleep(1)
            with conn.builtins.open(rprefPath) as f:
                content = f.read()
                print(f'content:{content}')
            os.remove(pref)
            conn.close()

    def __WinClient_GetDumps(self):
        vdmDumpDir = r"C:\ProgramData\VMware\VDM\Dumps"
        p1 = Path(vdmDumpDir)
        vdmDumps = list(p1.glob("*.dmp"))

        rmksTmpDir = os.path.join(os.environ['temp'])
        p2 = Path(rmksTmpDir)
        rmksDumps = list(p2.glob("vmware-*/*.dmp"))

        usbTmpDir = r"C:\Windows\Temp\vmware-SYSTEM"
        p3 = Path(usbTmpDir)
        usbDumps = []
        try:
            usbDumps = list(p3.glob("*.dmp"))
        except:
            pass

        viewTmpDir = os.path.join(os.environ['APPDATA'], 'Omnissa', 'Omnissa Horizon Client')
        p4 = Path(viewTmpDir)
        viewDumps = list(p4.glob("vmware-*/*.dmp"))

        mediaDumps = list(p2.glob("vmware-*/VMWMediaProvider/*.dmp"))

        return vdmDumps + rmksDumps + usbDumps + viewDumps + mediaDumps

    def __WinClient_CheckDumps(self, ts):
        dumps = self.__WinClient_GetDumps()
        for lf in dumps:
            mtime = os.path.getmtime(lf)
            # just ignore old dumps
            if mtime < ts:
                continue
            print(lf)
            kits.Warning("Found Dumps in Windows Client")
            dump_time = datetime.fromtimestamp(mtime).strftime('%Y-%m-%d %H:%M:%S')
            kits.Comment(f"dump files {lf}\n created on {dump_time}")

    def __WinClient_CheckPanicViaLog(self, ts):
        pattern = re.compile(b'panic:', re.I)
        tmpDir = os.path.join(os.environ['temp'], f'vmware-{os.getlogin()}')
        p = Path(tmpDir)
        logFile = list(p.glob('*.log'))
        for lf in logFile:
            mtime = os.path.getmtime(lf)
            # just ignore old log
            if mtime < ts:
                continue
            if os.stat(lf).st_size == 0:
                continue
            with open(lf, 'rb', 0) as f, mmap.mmap(f.fileno(), 0, access=mmap.ACCESS_READ) as s:
                if not pattern.findall(s):
                    continue
                kits.Log('client panic log in question', lf, delete=False)
                kits.Warning("Panic found in client log!")
                time.sleep(1)

    def __WinClient_GetMKSLog(self, ts):
        tmpDir = os.path.join(os.environ['temp'], f'vmware-{os.getlogin()}')
        p = Path(tmpDir)
        mksLogs = list(p.glob('*vmware-mks-*.log'))
        mksLogs.sort(key=lambda x: os.path.getctime(x))
        if not len(mksLogs):
            return
        for lf in mksLogs:
            ctime = os.path.getctime(lf)
            # just ignore old mks log
            if ctime < ts:
                continue
            # if larger than 1M, compress it first
            if os.path.getsize(lf) >> 20 > 1:
                compressedFile = f'{lf}.zip'
                if self.compress(lf, compressedFile):
                    kits.Log('client mks log ', compressedFile, delete=True)
                    kits.Comment("upload client compressed rmks log!")
            else:
                kits.Log('client mks log ', lf, delete=False)
                kits.Comment("upload client rmks log!")
            time.sleep(1)

    def __WinClient_NoneSVGADisplay(self):
        dc = os.path.join(os.getcwd(), 'ps', 'dc64cmd.exe')
        cmd = dc + ' -listmonitors'
        displays = []
        ret = subprocess.run(cmd, capture_output=True)
        if ret.returncode in ('0', 0):
            outs = ret.stdout.decode()
            monitors = outs.split('Monitor:')
            devices = [x.strip() for x in monitors if not 'VMware SVGA' in x]
            print(devices)
            p = re.compile('\S+DISPLAY\d+')
            for idx in devices:
                m = p.search(idx)
                if m:
                    displays.append(m.group())
        return sorted(displays)

    def __WinClient_ResetMonitor(self, tool=None):
        if not tool:
            return True
        if tool == 'vmtool':
            vmresexe = 'C:/Program Files/VMware/VMware Tools/VMWareResolutionSet.exe'
            if not os.path.exists(vmresexe):
                kits.Comment("VMwareResolutionSet.exe NOT Found!")
                return False
            vmresreset = f"{vmresexe} 0 1 , 0 0 1920 1200"
            ret = subprocess.call(vmresreset)
            if ret in ('0', 0):
                kits.Comment("VMwareResolutionSet Reset Succeeded !")
                time.sleep(5)
        elif tool == 'dc':
            dccmd = os.path.join(os.getcwd(), 'ps', 'dc64cmd.exe')
            if not os.path.exists(dccmd):
                kits.Comment("dc64cmd.exe NOT Found!")
                return False
            displays = self.__WinClient_NoneSVGADisplay()
            print(displays)
            if len(displays) < 4:
                print("Get Monitors failed")
                sys.exit(0)
            rdisplays = sorted(displays, reverse=True)
            for idx in rdisplays[:-1]:
                detachCmd = dccmd + f' -detach -monitor={idx}'
                subprocess.run(detachCmd)
                time.sleep(1)
            dc4k = dccmd + f' -monitor={rdisplays[-1]} -width=3840 -height=2160'
            subprocess.run(dc4k)
            time.sleep(1)


    def __WinClient_SetMultimon(self, primaryIdx=0, totalMonitors=4,
                                resList=['0+0+2048x1536', '2048+0+2048x1536',
                                         '4096+0+2048x1536', '6144+0+2048x1536']):
        vmresexe = 'C:/Program Files/VMware/VMware Tools/VMWareResolutionSet.exe'
        if len(resList) != totalMonitors:
            print("parameter Error between total Monitors and resolution List ")
            return False
        vmrescmd = f"{vmresexe} {primaryIdx} {totalMonitors}"
        mp = re.compile('''
                    (?P<pos>\d+\+\d+)           # position
                    \+
                    (?P<resolution>\d+x\d+)     # resolution
                    ''', re.X)
        for idx in resList:
            p = mp.search(idx)
            x, y = p.group('pos').split('+')
            w, h = p.group('resolution').split('x')
            vmrescmd = f"{vmrescmd} , {x} {y} {w} {h}"
        kits.Comment(vmrescmd)

        if self.remote:
            timeout = 60
            while timeout > 0:
                try:
                    conn = rpyc.classic.connect(self.ip)
                    ros = conn.modules.os
                    if not ros.path.exists(vmresexe):
                        kits.Comment("VMwareResolutionSet.exe NOT Found!")
                        return False
                    rsub = conn.modules.subprocess
                    ret = rsub.call(vmrescmd)
                    if ret in ('0', 0):
                        kits.Comment("VMwareResolutionSet Succeeded !")
                        conn.close()
                        break
                    else:
                        kits.Comment("VMwareResolutionSet Failed !")
                        time.sleep(5)
                        timeout =- 5
                        continue
                except Exception as e:
                    print("Exception", e)
                    time.sleep(10)
                    timeout -= 10
                    continue
        else:
            print("execute locally")
            if not os.path.exists(vmresexe):
                kits.Comment("VMwareResolutionSet.exe NOT Found!")
                return False
            ret = subprocess.run(vmrescmd)
            if ret in ('0', 0):
                kits.Comment("VMwareResolutionSet Succeeded !")
            time.sleep(5)

    def __WinClient_SetMultimon4K(self, primaryIdx=0, totalMonitors=4,
                                  resList=['0+0+3840x2160', '3840+0+3840x2160',
                                          '7680+0+3840x2160', '11520+0+3840x2160']):
        dccmd = os.path.join(os.getcwd(), 'ps', 'dc64cmd.exe')
        if not os.path.exists(dccmd):
            kits.Comment("dc64cmd.exe NOT Found!")
            return False
        displays = self.__WinClient_NoneSVGADisplay()
        mp = re.compile('''
                    (?P<pos>\d+\+\d+)           # position
                    \+
                    (?P<resolution>\d+x\d+)     # resolution
                    ''', re.X)

        primarySet = False
        for monitor, res in zip(displays, resList):
            p = mp.search(res)
            x, y = p.group('pos').split('+')
            w, h = p.group('resolution').split('x')
            cmd = dccmd + f' -monitor={monitor} -lx={x} -ty={y} -width={w}, -height={h}'
            if not primarySet:
                cmd += ' -primary'
                primarySet = True
            else:
                cmd += ' -secondary'
            ret = subprocess.run(cmd)
            if ret in ('0', 0):
                kits.Comment(f"{cmd} Succeeded !")
            else:
                kits.Comment(f"{cmd} Failed !")
            time.sleep(1)

    def __LinClient_SetPrefCfgValue(self, udp=None, h264=None):
        import configparser
        import io

        parser = configparser.RawConfigParser()
        parser.optionxform = str
        pref = os.path.expanduser("~/.omnissa/view-preferences")
        #pref = os.path.expanduser("~/.vmware/view-preferences")
        try:
            os.remove(pref)
        except FileNotFoundError:
            print("~/.omnissa/view-preferences not found")
        subprocess.run("sync && sleep 1", shell=True)
        # make sure view-preference exist
        if not os.path.exists(pref):
            open(pref, 'w').close()

        dumpySection = 'xxxx'
        with open(pref, 'r') as f:
            prefStr = '[%s]\n' % dumpySection + f.read()
        prefFp = io.StringIO(prefStr)
        parser.read_file(prefFp)
        parser.set(dumpySection, 'view.warnSslVersion', '"{}"'.format('FALSE'))
        parser.set(dumpySection, 'view.sslVerificationMode', '"{}"'.format('3'))
        parser.set(dumpySection, 'view.defaultLogLevel', '"{}"'.format('0'))
        parser.set(dumpySection, 'view.shareRemovableStorage', '"{}"'.format('FALSE'))
        parser.set(dumpySection, 'view.showSharingPromptDialog', '"{}"'.format('FALSE'))
        parser.set(dumpySection, 'view.usbAutoConnectAtStartUp', '"{}"'.format('TRUE'))
        parser.set(dumpySection, 'view.usbAutoConnectOnInsert', '"{}"'.format('TRUE'))

        cdrFolder = os.path.expanduser("~/CDR")
        pdfFolder = os.path.expanduser("~/PDF")
        shutil.rmtree(cdrFolder, ignore_errors=True)
        shutil.rmtree(pdfFolder, ignore_errors=True)
        if not os.path.exists(cdrFolder):
            os.makedirs(cdrFolder)
        parser.set(dumpySection, 'view.sharingFolders', '"{}"'.format(cdrFolder))
        if udp:
            parser.set(dumpySection, 'view.connectionUserMode', '"{}"'.format(udp))
        else:
            parser.set(dumpySection, 'view.connectionUserMode', '"0"')

        if h264:
            parser.set(dumpySection, 'view.enableH264', '"{}"'.format(h264))
            parser.set(dumpySection, 'view.enableHEVC', '"{}"'.format(h264))
        else:
            parser.set(dumpySection, 'view.enableH264', '"TRUE"')
            parser.set(dumpySection, 'view.enableHEVC', '"TRUE"')

        with open(pref, 'w') as f:
            parser.write(f)

        delCmd = "sed -i '/{}/d' {}".format(dumpySection, pref)
        self.GetCmdOutput(delCmd)

    def ClearSSHKey(self, agentIP='127.0.0.1'):
        if not 'win32' in sys.platform:
            clearCmd = 'ssh-keygen -R {}'.format(agentIP)
            self.GetCmdOutput(clearCmd)
        else:
            pass


    def __LinClient_CheckDumps(self):
        cwd = os.getcwd()
        cmd = f'ls -lh /var/crash/_usr_lib_vmware_view_* {cwd}/core* /tmp/core.*'
        ret = subprocess.getoutput(cmd)
        result = ret.lower().count('no such file') == 3
        logdir = '/tmp'
        if 'WORKSPACE' in os.environ:
            logdir = os.environ['WORKSPACE']
        if not result:
            kits.Warning('core dump and crash dump found in client')
            lines = ret.splitlines()
            dumps = [ y for y in lines if not 'no such file' in y.lower() ]
            for idx in dumps:
                kits.Comment(f"dump files {idx}")
                dumpFile = idx.split(' ')[-1]
                dumpName = os.path.split(dumpFile)[-1]
                tgtName = subprocess.getoutput(f'date +{dumpName}-"%Y-%m-%d-%H-%M-%S"')
                tgtDump = f'{logdir}/{tgtName}'
                kits.Comment(f"saved dump files {tgtDump}")
                subprocess.getoutput(f'sudo mv {dumpFile} {tgtDump}')
        # just be safe here
        subprocess.getoutput('sudo rm -f /var/crash/_usr_lib_vmware_view_*')
        subprocess.getoutput(f'sudo rm -f {cwd}/core*')
        return result


    def __LinClient_CollectLogs(self):
        vUserTmp = "/tmp/omnissa-{}".format(os.getlogin())
        if not os.path.exists(vUserTmp):
            return
        logzip = subprocess.getoutput('date +clientLog-"%Y-%m-%d-%H-%M".zip')
        logdir = '/tmp'
        if 'WORKSPACE' in os.environ:
            logdir = os.environ['WORKSPACE']
        kits.Comment(f"Client Log Directory: {logdir}")
        log = f"{logdir}/{logzip}"
        zipCmd = f"zip -r {log} /tmp/omnissa-qe /var/log/omnissa"
        subprocess.call(zipCmd, shell=True)
        time.sleep(1)
        kits.Log('client log bundle', log, delete=False)


    def __LinClient_ClearLogs(self):
        vUserTmp = Path("/tmp/omnissa-{}".format(os.getlogin()))
        cmd = "sudo rm -rf {}".format(str(vUserTmp))
        self.RunCmd(cmd, shell=True)
        cmd = "sudo rm -rf /var/log/omnissa/vmware-usb-*.log"
        self.RunCmd(cmd, shell=True)


    def __LinuxClient_Launch(self, *args, **kwargs):
        """
        Launch VMware View Linux client.

        @param args: a tuple from *args.
        @type args: tuple
        @kwargs: a dictionary from **args.
        @type kwargs: dict

        @return: pid if succeeded.
        @rtype: integer

        """
        cmd = 'horizon-client' + ' '
        for i in list(kwargs.keys()):
            if len(i) == 1:
                cmd = "%s -%s '%s' " % (cmd, i, kwargs[i])
            else:
                cmd = "%s --%s='%s' " % (cmd, i, kwargs[i])
        for i in args:
            cmd = '%s %s ' % (cmd, i)
        linuxClient_env = os.environ
        linuxClient_env['VMWARE_VIEW_DEBUG_LOGGING'] = 'TRUE'
        # 0 Log_All, 1 Log_Trace, 2 Log_Debug, 3 Log_Info 4 Log_Warn 5 Log_Error
        # 6 LogFatal
        print('Launch Linux Client: ', cmd)
        kits.Comment(cmd)
        self.cache['connectTS'] = time.strftime("%Y-%m-%dT%H:%M:%S", time.localtime())
        kits.Comment(f"launch {self.os} client at {self.cache['connectTS']}")
        self.cache['connStart'] = time.time()
        self.P = subprocess.Popen(shlex.split(cmd),
                                  env = linuxClient_env)

    def __LinuxClient_CheckPanicViaLog(self):
        searchPanicCmd = ' sudo grep -rl PANIC: '
        searchFile = ''
        ret = (False, '')
        vUserTmp = "/tmp/omnissa-{}".format(os.getlogin())
        vUSBDir = "/var/log/omnissa"
        ignoreError = " 2>/dev/null"
        ret = subprocess.getoutput(searchPanicCmd + vUserTmp + ignoreError)
        detected = bool(ret)
        if detected:
            kits.Warning("Panic found in client log!")
            print(ret)
            logs = ret.splitlines()
            for idx in logs:
                kits.Log('client panic log in question', idx, delete=False)
                time.sleep(1)
        ret = subprocess.getoutput(searchPanicCmd + vUSBDir + ignoreError)
        detected = bool(ret)
        if detected:
            print("Client: Panic found in USB LOG!")


    def __MacClient_SetSslOptions(self, disableTls12=True):
        hclist = os.path.expanduser("~/Library/Preferences/com.omnissa.horizon.client.mac.plist")
        cfgDict = {}
        cfgDict['autoConnectForSingleCert'] = 0
        cfgDict['kAutoCheckForUpdates'] = 0
        cfgDict['kAutoDownloadForUpdates'] = 0
        cfgDict['certificateVerificationMode'] = 3
        cfgDict['promptedUSBPrintingServicesInstall'] = 1
        cfgDict['promptSharingChecked'] = 1
        # magic number '167772160' for disable TLSv1.2 on Mac client
        if disableTls12:
            cfgDict['sslProtocolOptions'] = 167772160
        with open(hclist, 'wb') as fp:
            plistlib.dump(cfgDict, fp)

        time.sleep(2)
        cmd = "defaults read com.omnissa.horizon.client.mac"
        outs = subprocess.getoutput(cmd)
        kits.Comment(outs)

    def __MacClient_SetPrefCfgValue(self, udp=None, h264=True):

        hclist = os.path.expanduser("~/Library/Preferences/com.omnissa.horizon.client.mac.plist")
        cfgDict = {}
        cfgDict['autoConnectForSingleCert'] = 0
        cfgDict['kAutoCheckForUpdates'] = 0
        cfgDict['kAutoDownloadForUpdates'] = 0
        cfgDict['certificateVerificationMode'] = 3
        cfgDict['promptedUSBPrintingServicesInstall'] = 1
        cfgDict['promptSharingChecked'] = 1
        if h264:
            cfgDict['disableClientH264'] = 0
        else:
            cfgDict['disableClientH264'] = 1
        redirectFrom = os.path.expanduser("~/CDR")
        shutil.rmtree(redirectFrom, ignore_errors=True)
        if not os.path.exists(redirectFrom):
            os.makedirs(redirectFrom)

        cfgCDR = {}
        cfgCDR['redirectFrom'] = redirectFrom
        cfgCDR['redirectTo'] = "CDR"
        cfgCDR['redirectionStatus'] = 1
        cfgCDR['sharePermission'] = 2
        cfgCDR['shareTags'] = ['custom']
        cfgDict['SharedFolders'] = []
        cfgDict['SharedFolders'].append(cfgCDR)
        with open(hclist, 'wb') as fp:
            plistlib.dump(cfgDict, fp)

        time.sleep(2)
        cmd = "defaults read com.omnissa.horizon.client.mac"
        outs = subprocess.getoutput(cmd)
        kits.Comment(outs)

    def __MacClient_GetWindowInfo(self, windowTitle=None):
        if not windowTitle:
            return
        options = kCGWindowListOptionOnScreenOnly
        windowList = CGWindowListCopyWindowInfo(options, kCGNullWindowID)
        for window in windowList:
            wTitle = window.get('kCGWindowName', u'Unknown')
            if windowTitle == wTitle:
                b = window['kCGWindowBounds']
                p = int(window['kCGWindowOwnerPID'])
                x, y, width, height = map(int, (b['X'], b['Y'], b['Width'], b['Height']))
                return (True, p, x, y, width, height)
        else:
            return (False, 0, 0, 0, 0, 0)

    def __MacClient_Resize(self, poolName, desktopSize=DEFAULTDESKTOPSIZE, tryTimes=2):
        resizepy = os.path.join(os.getcwd(), 'lib', 'resize.py')
        cmd = '/usr/bin/python {} -d {} -s {}'.format(resizepy, poolName, desktopSize)
        comment = ''
        while tryTimes > 0:
            self.RunCmd(cmd, shell=True)
            time.sleep(1)
            ret, p, x, y, width, height = self.__MacClient_GetWindowInfo(poolName)
            if ret:
                comment = "pid:{}, pos:({},{}), size:{}x{}".format(p, x, y, width, height)
                print(comment)
                tryTimes -= 1
            else:
                comment = "Failed to resize Mac Client window via atomac"
                print(comment)
                unittest.TestCase.assertTrue(False, comment)
        kits.Comment(comment)

    def __MacClient_Launch(self, userName=None, password='vmware', serverURL=None, desktopName=None,
                           domainName=None, protocol='Blast', desktopSize='1024x768'):
        """
        Launch VMware View Mac client.

        """
        cmd1 = 'horizon-client://'
        uriStr = "{}@{}/{}?domainName={}&desktopProtocol={}&desktopLayout={}".format(userName,
                                                                                     serverURL,
                                                                                     desktopName,
                                                                                     domainName,
                                                                                     protocol,
                                                                                     desktopSize)
        cmd = 'open "{}"'.format(cmd1 + uriStr)
        launchpy = os.path.join(os.getcwd(), 'lib', 'resize.py')
        cmd = f"/usr/bin/python {launchpy} -l '{cmd}' -P {password}"
        print('Launch Mac Client: ', cmd)
        self.cache['connectTS'] = time.strftime("%Y-%m-%dT%H:%M:%S", time.localtime())
        kits.Comment(f"launch {self.os} client at {self.cache['connectTS']}")
        self.cache['connStart'] = time.time()
        self.P = subprocess.Popen(cmd, shell=True)

    def LaunchToDesktop(self, poolInfo, desktopSize=DEFAULTDESKTOPSIZE, h264=False, udp='mix', bypassUAG=True, expected=True):

        desktopName = poolInfo['pool']
        userName = f"{poolInfo['user']}@{poolInfo['domain']}.com"
        serverURL = self.broker
        if not bypassUAG:
            serverURL = self.uag

        # kits.Comment(f'########################- Start login on os:{self.os} from {self.node}({self.ip}) -################################')
        if 'win32' in sys.platform:
            self.__WinClient_SetSecurityMode()
            if os.environ.get('CDEBUG', False):
                self.__WinClient_SetVChanTraceLevel()
            if not h264:
                if udp == 'mix':
                    self.__WinClient_SetCfgPref(h264='false')
                elif udp == 'udp':
                    self.__WinClient_SetCfgPref(h264='false', udp='UDP-Only')
                else:
                    self.__WinClient_SetCfgPref(h264='false', udp='TCP-Only')
            else:
                if udp == 'mix':
                    self.__WinClient_SetCfgPref()
                elif udp == 'udp':
                    self.__WinClient_SetCfgPref(udp='UDP-Only')
                else:
                    self.__WinClient_SetCfgPref(udp='TCP-Only')

            if self.smartcard:
                self.__WinClient_Launch(serverURL=serverURL,
                                        domainName=poolInfo['domain'],
                                        desktopName=desktopName,
                                        protocol='Blast',
                                        desktopSize=desktopSize,
                                        connectUSBOnStartup=True,
                                        smartCardPIN='1234')
            else:
                self.__WinClient_Launch(serverURL=serverURL,
                                        userName=userName,
                                        password=poolInfo['password'],
                                        desktopName=desktopName,
                                        protocol='Blast',
                                        desktopSize=desktopSize,
                                        connectUSBOnStartup=True)

        elif 'linux' in sys.platform:
            self.__LinClient_ScreenSize()
            if not h264:
                if udp == 'mix':
                    self.__LinClient_SetPrefCfgValue(h264='FALSE')
                elif udp == 'udp':
                    self.__LinClient_SetPrefCfgValue(h264='FALSE', udp='2')
                else:
                    self.__LinClient_SetPrefCfgValue(h264='FALSE', udp='3')
            else:
                if udp == 'mix':
                    self.__LinClient_SetPrefCfgValue()
                elif udp == 'udp':
                    self.__LinClient_SetPrefCfgValue(udp='2')
                else:
                    self.__LinClient_SetPrefCfgValue(udp='3')

            self.__LinuxClient_Launch("-q",
                                      serverURL = serverURL,
                                      userName = poolInfo['user'],
                                      domainName = poolInfo['domain'],
                                      password = poolInfo['password'],
                                      desktopName = desktopName,
                                      protocol = 'BLAST',
                                      desktopSize = desktopSize)

        else:
            self.__MacClient_SetPrefCfgValue(h264=h264)
            self.__MacClient_Launch(serverURL = serverURL,
                                    userName = poolInfo['user'],
                                    password = poolInfo['password'],
                                    domainName = poolInfo['domain'],
                                    desktopName = desktopName,
                                    protocol = 'Blast',
                                    desktopSize = desktopSize)

        waitTime = 180
        while waitTime > 0:
            print(desktopName)
            ret = self.PoolWindowExists(desktopName)
            if not expected: # For different user login error case
                kits.Verify(expected, ret, 'Login Error')
                return True
            if not ret:
                time.sleep(0.5)
                waitTime -= 0.5
                comments = f" {userName} Failed to Connect from {self.os} Client after {180 - waitTime} seconds"
                print(comments)
                continue
            else:
                tconsumed = time.time() - self.cache['connStart']
                t = tconsumed < 300
                kits.Verify(t, True, f'client launch time consumed: {tconsumed} seconds, less than 300')
                return True
        else:
            self.UploadRMKSLog(cached_values.get('clientTS'))
            comments = f" {userName} Failed to Connect from {self.os} Client"
            print(comments)
            kits.Verify(False, True, comments, screenshot=True)
            return False

    def LaunchWithTLS12Disabled(self, poolInfo, desktopSize=DEFAULTDESKTOPSIZE):
        desktopName = poolInfo['pool']
        if 'darwin' == sys.platform:
            self.__MacClient_SetSslOptions()
            self.__MacClient_Launch(serverURL = self.broker,
                                    userName = poolInfo['user'],
                                    password = poolInfo['password'],
                                    domainName = poolInfo['domain'],
                                    desktopName = desktopName,
                                    protocol = 'Blast',
                                    desktopSize = desktopSize)
        else:
            return

        kits.Wait(10)
        waitTime = 90
        while waitTime > 0:
            if not self.PoolWindowExists(desktopName):
                time.sleep(15)
                waitTime -= 15
                comments = "Failed to Connect from {} Client after {} seconds".format(self.os, 90 - waitTime + 10)
                # kits.Comment(comments)
                continue
            else:
                break
        else:
            comments = "Failed to Connect from {} Client".format(self.os)
            # kits.Verify(False, True, comments, screenshot=True)
            unittest.TestCase.assertTrue(False, comments)

    def ManualLogin(self, guestOS='Ubuntu18', username=None, password=None):
        # manul login greeter window capture
        import lib.findPic as pic
        for idx in range(10):
            lpic = pic.GrabScreenLocal()
            kits.Screenshot('Manual login with local desktop greeter', lpic[0])
            for ld in lpic:
                if os.path.exists(ld):
                    os.remove(ld)
            greeterImage = 'greeter_ubt18.png'
            if guestOS in ('redhat7', 'redhat8'):
                greeterImage = 'greeter_rh7.png'
            greeter = os.path.join(os.getcwd(), 'images', greeterImage)
            print(greeter)

            # click greeter to login
            print("press ESC")
            pyautogui.press('esc')
            time.sleep(4)
            if bool(username):
                ifind = pyscreeze.locateCenterOnScreen(greeter, confidence=.6, grayscale=True)
                if ifind == None:
                    time.sleep(5)
                    kits.Warning(f'Failed to find greeter {idx + 1} times')
                    continue
                pyautogui.click(ifind)
                time.sleep(2)
                pyautogui.typewrite(username)
                time.sleep(2)
                pyautogui.typewrite(['enter'])
            pyautogui.click()
            time.sleep(2)
            pyautogui.typewrite(password)
            time.sleep(2)
            pyautogui.typewrite(['enter'])
            break
        else:
            kits.Verify(True, False, 'Failed to find greeter to input correct username.')

    def LaunchToApp(self, h264=False, udp='mix', appName='Terminal', user='qe', domain='LXD', password='vmware'):
        kits.Comment(f'########################- {user} Try to open {appName} on os:{self.os} from {self.node}({self.ip}) -################################')
        if 'win32' in sys.platform:
            self.__WinClient_SetSecurityMode()
            if os.environ.get('CDEBUG', False):
                self.__WinClient_SetVChanTraceLevel()
            if not h264:
                if udp == 'mix':
                    self.__WinClient_SetCfgPref(h264='false')
                elif udp == 'udp':
                    self.__WinClient_SetCfgPref(h264='false', udp='UDP-Only')
                else:
                    self.__WinClient_SetCfgPref(h264='false', udp='TCP-Only')
            else:
                if udp == 'mix':
                    self.__WinClient_SetCfgPref()
                elif udp == 'udp':
                    self.__WinClient_SetCfgPref(udp='UDP-Only')
                else:
                    self.__WinClient_SetCfgPref(udp='TCP-Only')

            self.__WinClient_Launch(serverURL=serverURL,
                                    userName=f"{user}@{domain}",
                                    password=poolInfo['password'],
                                    appName=appName,
                                    appProtocol='BLAST')

        elif 'linux' in sys.platform:
            self.__LinClient_ScreenSize()
            if not h264:
                if udp == 'mix':
                    self.__LinClient_SetPrefCfgValue(h264='FALSE')
                elif udp == 'udp':
                    self.__LinClient_SetPrefCfgValue(h264='FALSE', udp='2')
                else:
                    self.__LinClient_SetPrefCfgValue(h264='FALSE', udp='3')
            else:
                if udp == 'mix':
                    self.__LinClient_SetPrefCfgValue()
                elif udp == 'udp':
                    self.__LinClient_SetPrefCfgValue(udp='2')
                else:
                    self.__LinClient_SetPrefCfgValue(udp='3')

            self.__LinuxClient_Launch("-q",
                                      serverURL = serverURL,
                                      userName = f"{user}@{domain}",
                                      password = poolInfo['password'],
                                      applicationName = appName,
                                      protocol = 'BLAST')

        else:
            # TODO, add mac client support
            pass

        kits.Wait(10)
        waitTime = 180
        windowName = appName
        if appName == 'Terminal':
            windowName = f"{user}@"
        while waitTime > 0:
            ret = self.PoolWindowExists(windowName)
            if not ret:
                time.sleep(15)
                waitTime -= 15
                comments = "Failed to Connect from {} Client after {} seconds".format(self.os, 180 - waitTime + 10)
                kits.Comment(comments)
                continue
            else:
                return True
        else:
            comments = "Failed to Connect from {} Client".format(self.os)
            kits.Verify(False, True, comments, screenshot=True)
            unittest.TestCase.assertTrue(False, comments)
            return False

    def MoveDesktopToCenter(self, poolName):
        if 'win32' in sys.platform:
            self.__WinClient_GetClientBorder(poolName)
            self.__WinClient_MoveCenter(poolName)

        elif 'linux' in sys.platform:
            self.__LinClient_GetClientBorder(poolName)
            self.__LinClient_MoveCenter(poolName)
        else:
            pass

    def PoolWindowExists(self, poolName):
        if 'win32' in sys.platform:
            hwnd = pyautogui.getWindow(poolName)
            if not hwnd:
                return False
            else:
                hwnd.set_foreground()
        elif 'linux' in sys.platform:
            cmd = 'wmctrl -G -p -l | grep {}'.format(poolName)
            outs = self.GetCmdOutput(cmd)
            if not outs:
                return False
        else:
            ret, _, _, _, _, _ = self.__MacClient_GetWindowInfo(poolName)
            if not ret:
                return False

        return True

    def ResizeDesktop(self, poolName, desktopSize='1440x900'):
        clientTS = client.GetCurrentTS()
        kits.Comment(f"Client resize to {desktopSize} in {clientTS}")
        if 'win32' in sys.platform:
            self.__WinClient_Resize(poolName, desktopSize)
        elif 'linux' in sys.platform:
            self.__LinClient_Resize(poolName, desktopSize)
        else:
            self.__MacClient_Resize(poolName, desktopSize)

    def MaxmizeBrowser(self, executor='localhost'):
        seleniumlib.MaxmizeWebClient(executor=executor)

    def CenterBrowser(self, executor='localhost'):
        seleniumlib.CenterWebClient(executor=executor)

    def RefreshBrowser(self, executor='localhost'):
        seleniumlib.RefreshBrowser(executor=executor)

    def UngrabDesktop(self):
        w, _ = pyautogui.size()
        # avoid pyautogui fail-safe exception
        try:
            if 'darwin' in sys.platform:
                pyautogui.hotkey('command', 'ctrl')
            elif 'win32' in sys.platform:
                pyautogui.hotkey('alt', 'ctrl')
            elif 'linux' in sys.platform:
                cmd = 'wmctrl -a x-nautilus-desktop'
                self.RunCmd(cmd, shell=False)
        except Exception as e:
            print("Exception", e)
            pass

        x = int(w/2)
        if 'linux' in sys.platform:
            self.__LinClient_ScreenSize()
            x = int(int(self.cwidth)/2)
        # avoid menu bar on Ubuntu 1804
        try:
            pyautogui.click(x=x, y=40, duration=0.1)
            kits.Comment("click {},2".format(x))
        except Exception as e:
            print("Exception", e)
            pass

    def ActivateDesktop(self, poolName):
        if 'linux' in sys.platform:
            self.__LinClient_MoveCenter(poolName)
            self.__LinClient_Activate(poolName)
        elif 'win32' in sys.platform:
            self.__WinClient_MoveCenter(poolName)
            self.__WinClient_Activate(poolName)

        self.ClickCenter()

    def __LinClient_ScreenSize(self):
        monInfos = self.__LinClient_GetRes()
        if not len(monInfos):
            return False
        curRes = monInfos[0][1]
        kits.Comment('client screen resolution : {}'.format(curRes))
        self.cwidth, self.cheight = curRes.split('x')

    def __LinClient_Resize(self, poolName, desktopSize=DEFAULTDESKTOPSIZE):
        w, h = desktopSize.split('x')
        print(f"destop size: {desktopSize}")
        print(f"window border, width: {self.windowBorderWidth} height: {self.windowBorderHeight}")
        bw = int(self.windowBorderWidth) + int(w)
        bh = int(self.windowBorderHeight) + int(h)
        # make the desktop center
        x = int(int(self.cwidth) - bw) / 2
        y = int(int(self.cheight) - bh) / 2
        print(f"Geometry: {int(x)}, {int(y)}, {bw}, {bh}")
        cmd = 'wmctrl -r {} -e 0,{},{},{},{}'.format(poolName, int(x), int(y), bw, bh)
        self.RunCmd(cmd, shell=True)

    def __LinClient_MoveCenter(self, poolName):
        cmd = 'wmctrl -G -p -l | grep {}'.format(poolName)
        outs = self.GetCmdOutput(cmd)
        if not outs:
            return

        b =  outs.split()
        bw = int(b[5])
        bh = int(b[6])

        # make the desktop center
        x = int(int(self.cwidth) - int(bw)) / 2
        y = int(int(self.cheight) - int(bh)) / 2
        cmd = 'wmctrl -r {} -e 0,{},{},{},{}'.format(poolName, int(x), int(y), bw, bh)
        self.RunCmd(cmd, shell=True)

    def ClickCenter(self):
        size = [int(x) for x in pyautogui.size()]
        pos = [int(x/2) for x in size]
        # avoid pyautogui fail-safe exception
        try:
            pyautogui.click(pos[0], pos[1], duration=0.2)
            # kits.Comment("click {},{}".format(pos[0], pos[1]))
            time.sleep(1)
            pyautogui.click(pos[0], pos[1], duration=0.2, button='right')
            # kits.Comment("right click {},{}".format(pos[0], pos[1]))
        except Exception as e:
            print("Exception: pyautogui click: ", e)
            pass

    def RefreshWithF5(self):
        pyautogui.press('f5')
        kits.Comment("Refresh with F5")
        time.sleep(3)

    def compress(self, srcfile, tgtfile, compresslevel=9):
        fname, fext = os.path.splitext(tgtfile)
        if fext == '.zip':
            method = ZIP_DEFLATED
        elif fext == '.bz2':
            method = ZIP_BZIP2
        elif fext == '.xz':
            method = ZIP_LZMA
        else:
            print(f'{fext} Not Supported')
            return False

        with ZipFile(tgtfile, 'w', method, compresslevel=compresslevel) as zf:
            zf.write(tgtfile)
        return True

    def __LinClient_Activate(self, poolName):
        cmd = 'wmctrl -a {}'.format(poolName)
        self.RunCmd(cmd, shell=False)
        time.sleep(1)
        # make sure we are always on top
        cmd = 'wmctrl -r {} -b add,above'.format(poolName)
        self.RunCmd(cmd, shell=False)

    def __LinClient_GetRes(self):
        """
        Author: yanchaozhang
        Creation time: 2016-03-18

        Get the screen resolution of agent.
        return list like
        ['Virtual1', '1920x1200', 0, '1920,0', True]']
        here:
            Virtual1 is the monitor name in Linux VDI
            1920x1200 is the resolution
            0 indicate it is not in pivot mode
            1920,0 indicate the position
            True indicate that this is the primary monitor
        """
        cmd = "xrandr -q"
        outs = self.GetCmdOutput(cmd, shell=False)
        if re.search('xrandr: Failed to', outs):
            return self.__LinClient_GetXineramExt()

        ret = []
        mp = re.compile('''
                    (?P<name>^\S+?)             # name
                    \s
                    connected
                    \s
                    (?P<primary>primary\s)?     # primary monitor ?
                    ((?P<cresolution>\d+x\d+)   # current resolution
                    \+
                    (?P<pos>\d+\+\d+))          # position
                    \s
                    (?P<rotate>[^\(\s]+)?       # rotation
                    ''', re.M | re.X)

        for m in mp.finditer(outs):
            if m.group('cresolution'):
                item = []
                item.append(m.group('name'))
                item.append(m.group('cresolution'))
                rotate = 'normal'
                if m.group('rotate'):
                    rotate = m.group('rotate')
                item.append(rotate)
                position = m.group('pos').replace('+', ',')
                item.append(position)
                primary = False
                if m.group('primary'):
                    primary = True
                item.append(primary)
                ret.append(item)

        return ret

    def __LinClient_GetXineramExt(self):
        """
        Author: yanchaozhang
        Creation time: 2016-03-18
        Since xrandr get failed in RHEL/CentOS 6, we get display info from XINERAMA extention
        """

        cmd = "xdpyinfo -ext XINERAMA"
        outs = self.RunCmd(cmd=cmd, shell=True)

        ret = []
        mp1 = re.compile('''
                        (?:^XINERAMA.*?$)           # XINERAMA extention
                        (?P<heads>.+?)              # remaining resolutions
                        (?=^\w+|\Z)                 # another beginning or EOF
                        ''', re.S | re.M | re.X)

        mp2 = re.compile('''
                        (?:\s+)                     # spaces from beginning
                        (?P<head>[^:]+)             # head number
                        :
                        (?:\s+)                     # space
                        (?P<cresolution>\d+x\d+)    # current resolution
                        (?:\s+)                     # space
                        @                           # @ symbol
                        (?:\s+)                     # space
                        (?P<pos>\d+,\d+)            # position
                        ''', re.X)

        n = mp1.search(outs)
        if not n:
            return ret
        if n.group('heads'):
           heads = [x for x in n.group('heads').splitlines() if x]
           for h in heads:
              m = mp2.search(h)
              if m:
                  item = []
                  item.append(m.group('head'))
                  item.append(m.group('cresolution'))
                  item.append(0)
                  item.append(m.group('pos'))
                  item.append(False)
                  ret.append(item)

        return ret

    def __LinClient_GetClientBorder(self, poolName, desktopSize=DEFAULTDESKTOPSIZE):
        if self.borderSet:
            return
        time.sleep(5)
        cmd = 'wmctrl -G -p -l | grep {}'.format(poolName)
        outs = self.GetCmdOutput(cmd)
        w, h = 0, 0
        if outs:
            b =  outs.split()
            print(f'outs: {b}')
            w = int(b[5])
            h = int(b[6])
        else:
            w = 1024
            h = 793
        dw, dh = [int(x) for x in desktopSize.split('x')]
        kits.Comment("Setting window border")
        kits.Comment(f"{dw}, {dh}, {w}, {h}")
        self.windowBorderWidth = abs(w - dw)
        self.windowBorderHeight = abs(h - dh)
        self.borderSet = True

    def __WinClient_GetClientBorder(self, poolName, desktopSize=DEFAULTDESKTOPSIZE):
        hwnd = pyautogui.getWindow(poolName)
        if not hwnd:
            # win10 default value
            self.windowBorderWidth = 16
            self.windowBorderHeight = 62
        # Get desktop position, top left and bottom right
        x, y, w, h = hwnd.get_position()
        w -= x
        h -= y
        dw, dh = [int(x) for x in desktopSize.split('x')]
        self.windowBorderWidth = w - dw
        self.windowBorderHeight = h - dh

    def __WinClient_Resize(self, poolName, desktopSize='1366x768'):
        hwnd = pyautogui.getWindow(poolName)
        if not hwnd:
            return
        else:
            hwnd.set_foreground()
        width, height = pyautogui.size()

        wind = win32gui.GetForegroundWindow()
        if desktopSize != 'all':
            win32gui.ShowWindow(wind, win32con.SW_NORMAL)
            w, h = desktopSize.split('x')
            bw = int(self.windowBorderWidth) + int(w)
            bh = int(self.windowBorderHeight) + int(h)

            # make the desktop center
            hwnd.resize(bw, bh)
            topX = int((int(width) - int(bw)) / 2)
            topY = int((int(height) - int(bh)) / 2)
            time.sleep(1)
            hwnd.move(topX, topY)
        else:
            win32gui.ShowWindow(wind, win32con.SW_MAXIMIZE)


    def __WinClient_MoveCenter(self, poolName):
        width, height = pyautogui.size()
        hwnd = pyautogui.getWindow(poolName)
        if not hwnd:
            return
        x, y, w, h = hwnd.get_position()
        topX = int((int(width) - int(w - x)) / 2)
        topY = int((int(height) - int(h - y)) / 2)
        # kits.Comment("Move to Top left Position {},{}".format(topX, topY))
        hwnd.move(topX, topY)

    def __WinClient_Activate(self, poolName):
        hwnd = pyautogui.getWindow(poolName)
        if not hwnd:
            return
        hwnd.set_foreground()

    def __WinClient_GetRes(self, verbose=False):
        """
        Author: yanchaozhang
        Creation time: 2022-04-20

        Get the screen resolution of agent.
        in verbose mode, return list like:
        ['DISPLAY1', '3840x2160', '0,0', True']
        here:
            DISPLAY1 is the monitor name in windows client
            3840x2160 is the resolution
            0,0 indicate the position
            True indicate that this is the primary monitor
        in non-verbose mode, return list like:
        ['0+0+3840x2160']
        """
        ret = []
        from screeninfo import get_monitors
        monInfos = get_monitors()
        if not len(monInfos):
            return False
        for m in monInfos:
            if verbose:
                item = []
                item.append(m.name)
                item.append(f"{m.width}x{m.height}")
                item.append(f"{m.x},{m.y}")
                item.append(f"{m.is_primary}")
            else:
                item = f"{m.x}+{m.y}+{m.width}x{m.height}"
            ret.append(item)
        return ret

    def __client_Record(self, audioFile=None, recordIdx=lib.auto.clientData.get('recordSndIdx', 1), seconds=10):
        if not audioFile:
            kits.Comment("Please specify audio record file")
        start = datetime.now()
        ts = start.strftime('%Y-%m-%d-%H-%M-%S')
        kits.Comment(f'Record File: {audioFile}')
        print(f'Start Recording: {ts}')
        framerate = lib.auto.clientData.get('framerate', None)
        ad = audio.AudioInfo()
        audio.record(ad, audioFile, inp=recordIdx, seconds=seconds, framerate=framerate)
        print(f"End Recording: {datetime.now().strftime('%Y-%m-%d-%H-%M-%S')}")

    def __client_Play(self, audioFile=None, playIdx=lib.auto.clientData.get('playSndIdx', 1)):
        if not audioFile:
            kits.Comment("Please specify audio file")
        start = datetime.now()
        ts = start.strftime('%Y-%m-%d-%H-%M-%S')
        kits.Comment(f'Audio File: {audioFile}')
        print(f'Start Playing: {ts}')
        mi = audio.MusicInfo(audioFile)
        ad = audio.AudioInfo()
        audio.play(ad, mi, od=int(playIdx))
        print(f"End Playing: {datetime.now().strftime('%Y-%m-%d-%H-%M-%S')}")

    def __WinClient_StartPktCapture(self, interface, pkts):
        tshark = r"C:/Program Files/Wireshark/tshark.exe"
        if not os.path.exists(tshark):
            print('tshark not found!')
            return False
        blastPortFilter = 'port 443 or port 8443 or port 22443'
        cmd = f'"{tshark}" -i {interface} -w {pkts} -f "{blastPortFilter}"'
        print('start tshark ...')
        self.RunCmd(cmd)

    def __WinClient_StopPktCapture(self):
        cmd = 'taskkill /F /im tshark.exe'
        print('stop tshark ...')
        self.RunCmd(cmd)

    def HTMLAccess_MoveCenter(self):
        if 'win32' in sys.platform:
            width, height = pyautogui.size()
            hwnd = pyautogui.getWindow('Omnissa Horizon')
            if not hwnd:
                return
            hwnd.set_foreground()
            time.sleep(1)
            x, y, w, h = hwnd.get_position()
            topX = int((int(width) - int(w - x)) / 2)
            topY = int((int(height) - int(h - y)) / 2)
            kits.Comment("Move to Top left Position {},{}".format(topX, topY))
            hwnd.move(topX, topY)
        elif 'darwin' in sys.platform:
            self.CenterBrowser()

    def IsRemoteDesktopShownMss(self, remoteDesktopIP, clientHost='localhost', timeCput=True,
                                expectation=True, threshold=0.8, timeout=300, visualize=False,
                                selectivemul=False, backUp=False, bakPath=r'D:\Pic'):
        t = timeout
        rets = []
        while timeout > 0:
            rpic = []
            for idx in range(30):
                rpic = pic.GrabScreenRemote((remoteDesktopIP, None))
                if len(rpic):
                    # check if totally black
                    if pic.IsBlack(rpic[0]):
                        # save disk space
                        for idxp in rpic:
                            if os.path.exists(idxp):
                                os.remove(idxp)
                    else:
                        # let's save the first screenshot, and delete others
                        if len(rpic) > 1:
                            for idxp in rpic[1:]:
                                if os.path.exists(idxp):
                                    os.remove(idxp)
                        break
                else:
                    print(f'Failed to get agent screen from {remoteDesktopIP} in {idx + 1} times !')
                    time.sleep(1)
                    continue
                print(f'detect agent black screen from {remoteDesktopIP} in {idx + 1} times !')
                time.sleep(1)
                continue
            else:
                return False

            lpic = []
            for idx in range(180):
                if clientHost in ('localhost', '127.0.0.1'):
                    lpic = pic.GrabScreenLocal()
                else:
                    lpic = pic.GrabScreenRemote((clientHost, None))
                if len(lpic):
                    # check if totally black
                    if pic.IsBlack(lpic[0]):
                        for idxp in lpic:
                            if os.path.exists(idxp):
                                os.remove(idxp)
                    else:
                        # let's save the first screenshot, and delete others
                        if len(lpic) > 1:
                            for idxp in lpic[1:]:
                                if os.path.exists(idxp):
                                    os.remove(idxp)
                        break
                    print(f'detect client black screen in {idx + 1} times !')
                    time.sleep(1)
                    continue
                else:
                    print(f'Failed to get client screen in {idx + 1} times !')
                    time.sleep(1)
                    continue
            else:
                return False

            print('Client Desktop Numbers {}'.format(len(lpic[1:])))
            print(f"agent desktop : {rpic}")
            print(f"client desktop : {lpic}")

            rets.clear()
            clientImgsForUpload = []
            agentImgsForUpload = []
            if not selectivemul:
                try:
                    ret, _ = pic.FindPic(rpic[0], srcImages=lpic[:1], threshold=threshold,
                                         matchAlgoIdx=1, visualize=visualize)
                    print(f"{('not detected', 'detected')[ret]}, between client: {rpic[0]} and agent: {lpic[0]}")
                    self.cache['detectTS'] = time.time()
                    rets.append(ret)
                except Exception as e:
                    print("Exception", e)
                clientImgsForUpload.append(lpic[0])
                agentImgsForUpload.append(rpic[0])
            else:
                rpic.sort()
                lpic.sort()
                t = threshold
                for idx, (i, j) in enumerate(zip(rpic, lpic)):
                    if idx == 0:
                        t = 0.6 # due to manubar
                    else:
                        t = threshold
                    ret, _ = pic.FindPic(i, srcImages=[j], threshold=t, matchAlgoIdx=1)
                    self.cache['detectTS'] = time.time()
                    print(f"Retemote Desktop {('not detected', 'detected')[ret]}, between client: {i} and agent: {j}")
                    rets.append(ret)
                    clientImgsForUpload.append(j)
                    agentImgsForUpload.append(i)

            # racetrack failed to receive screen shot larger than 1M
            for i, j in zip(clientImgsForUpload, agentImgsForUpload):
                if os.path.getsize(i) >> 20 > 1:
                    img = Image.open(i)
                    w, h = img.size
                    r = 480 / float(h)
                    new_w = int(w * r)
                    # img2 = img.resize((new_w, 480), Image.ANTIALIAS)
                    img2 = img.resize((new_w, 480), Image.Resampling.LANCZOS)
                    fn, fe = os.path.splitext(i)
                    fname = f"{fn}__{new_w}x480{fe}"
                    print(fname)
                    img2.save(fname)
                    kits.Screenshot('client desktop', fname)
                    os.remove(fname)
                else:
                    kits.Screenshot('client desktop', i)
                if os.path.getsize(j) >> 20 > 1:
                    img = Image.open(j)
                    w, h = img.size
                    r = 480 / float(h)
                    new_w = int(w * r)
                    img2 = img.resize((new_w, 480), Image.Resampling.LANCZOS)
                    fn, fe = os.path.splitext(j)
                    fname = f"{fn}__{new_w}x480{fe}"
                    print(fname)
                    img2.save(fname)
                    kits.Screenshot('agent desktop', fname)
                    os.remove(fname)
                else:
                    kits.Screenshot('agent desktop', j)
                # virtual screen is enough in most case
                if not selectivemul:
                    break

            result = all(rets)
            print(f"display checking per monitor results {rets}")
            if not expectation:
                result = not any(rets)
            if backUp:
                for idx in lpic:
                    blpic = os.path.join(bakPath, os.path.split(idx)[-1])
                    print(blpic)
                    shutil.move(idx, blpic)
                for idx in rpic:
                    brpic = os.path.join(bakPath, os.path.split(idx)[-1])
                    print(brpic)
                    shutil.move(idx, brpic)
            else:
                for ld in lpic:
                    if os.path.exists(ld):
                        os.remove(ld)
                for rd in rpic:
                    if os.path.exists(rd):
                        os.remove(rd)

            if result:
                kits.Verify(result, expectation, 'Remote desktop %s is shown in client host %s' %(remoteDesktopIP, clientHost))
                if expectation and timeCput:
                    if 'detectTS' in self.cache and 'connStart' in self.cache:
                        tconsumed = self.cache['detectTS'] - self.cache['connStart']
                        t = tconsumed < 600
                        kits.Verify(t, True, f'desktop display time consumed: {tconsumed:.3f} seconds, less than 600')
                return True
            else:
                rets.clear()
                time.sleep(1)
                timeout -= 1
        else:
            kits.Verify(False, True, 'Remote desktop is not shown in client host within %s seconds' % t)
            return False

    def KillClient(self, check=False, timeout=30):
        if not self.P:
            return

        if 'win32' in sys.platform:
            cmd = 'horizon-client.exe --shutdown'
        else:
            cmd = 'killall horizon-client'

        self.RunCmd(cmd, shell=False)
        time.sleep(2)
        while self.P.poll() is None and timeout > 0:
            time.sleep(1)
            timeout -= 1
        if self.P and self.P.stdout:
            self.P.stdout.close()
        if self.P and self.P.stderr:
            self.P.stderr.close()
        if self.P:
            self.P.terminate()

        time.sleep(2)
        if 'win32' in sys.platform:
            viewExist = bool(len(self.CheckProcess()))
            if viewExist:
                os.system("taskkill /im horizon-client.exe")
                if check:
                    kits.Verify(True, viewExist, 'horizon-client quit successfully')
                else:
                    kits.Comment('horizon-client quit does not successfully')
            rmksExist = bool(len(self.CheckProcess(procName='horizon-protocol')))
            if rmksExist:
                os.system("taskkill /im horizon-protocol.exe")
                if check:
                    kits.Verify(True, rmksExist, 'horizon-protocol quit successfully')
                else:
                    kits.Comment('horizon-protocol does not quit successfully')
        else:
            # for linux, we should also kill crtbora
            if 'linux' in sys.platform:
                os.system("killall horizon-client-crtbora")
                time.sleep(2)
            os.system("killall horizon-client")
            time.sleep(2)
            os.system("killall horizon-protocol")
            time.sleep(2)
            if 'linux' in sys.platform:
                os.system("sudo killall thnuclnt ")
                time.sleep(2)

    def KillChomeBrowser(self, remote=False):
        if remote:
            conn = ''
            try:
                conn = rpyc.classic.connect(self.ip)
                rsys = conn.modules.sys
                ros = conn.modules.os
                if 'win32' in rsys.platform:
                    ros.system("taskkill /im chrome.exe")
                conn.close()
            except Exception as e:
                print("Exception", e)

        elif 'win32' in sys.platform:
            os.system("taskkill /im chrome.exe /F")

    def KillSafariBrowser(self):
        if 'darwin' in sys.platform:
            os.system("killall Safari")

    def KillFileExplorer(self):
        """
        Author: yanchaozhang
        Date: 2017-03-10
        Kill Nautilus
        @Example:  KillFileExplorer()
        @Input: None
        @Output: None
        """

        killCmd = 'killall nautilus '
        self.RunCmd(killCmd, shell=False)
        time.sleep(2)


    def ResetMonitor(self, tool=None):
        if 'Windows' == self.os:
            self.__WinClient_ResetMonitor(tool=tool)
        else:
            pass


    def SetMultimon(self, primaryIdx=0, totalMonitors=4, is4k=False,
                    resList=['0+0+2048x1536', '2048+0+2048x1536',
                             '4096+0+2048x1536', '6144+0+2048x1536']):
        if 'Windows' == self.os:
            if is4k:
                # retry to improve reliability
                for _ in range(5):
                    #commenting below code as dc64cmd.exe tool is not available.
                    # 4k resolution is also set using VMWareResolutionSet.exe.
                    # This will be reverted once we have dc64cmd.exe

                    #self.__WinClient_SetMultimon4K(primaryIdx=primaryIdx,
                    #                               totalMonitors=totalMonitors,
                    #                               resList=resList)
                    self.__WinClient_SetMultimon(primaryIdx=primaryIdx,
                                                 totalMonitors=totalMonitors,
                                                 resList=resList)

                    monInfos = self.__WinClient_GetRes()
                    print(f"client monitor info: {monInfos}")
                    if set(monInfos) == set(resList):
                        break
            else:
                self.__WinClient_SetMultimon(primaryIdx=primaryIdx,
                                             totalMonitors=totalMonitors,
                                             resList=resList)
        else:
            pass


    def RemoteClient_LaunchToDesktopRpyc(self, desktopName, desktopSize=DEFAULTDESKTOPSIZE, userName='zyc1', password='VMware123',
                                         domain='LXD', h264=False, udp='mix', bypassUAG=True, expected=True):
        serverURL = self.broker
        timeout = 90
        node = ''
        print(f"remote IP:{self.ip}")
        while timeout > 0:
            conn = ''
            try:
                conn = rpyc.classic.connect(self.ip)
            except Exception as e:
                print("Exception", e)
                time.sleep(10)
                timeout -= 10
                continue

            rsys = conn.modules.sys
            rsub = conn.modules.subprocess
            rplatform = conn.modules.platform
            node = rplatform.node()
            if 'win32' in rsys.platform:
                if not h264:
                    if udp == 'mix':
                        self.__WinClient_SetCfgPref(h264='false')
                    elif udp == 'udp':
                        self.__WinClient_SetCfgPref(h264='false', udp='UDP-Only')
                    else:
                        self.__WinClient_SetCfgPref(h264='false', udp='TCP-Only')
                else:
                    if udp == 'mix':
                        self.__WinClient_SetCfgPref()
                    elif udp == 'udp':
                        self.__WinClient_SetCfgPref(udp='UDP-Only')
                    else:
                        self.__WinClient_SetCfgPref(udp='TCP-Only')

                CREATE_NEW_PROCESS_GROUP = 0x00000200
                DETACHED_PROCESS = 0x00000008
                cmd = 'horizon-client.exe '
                print("Broker: "+serverURL+", user: "+userName+"@"+domain+", Desktop: "+desktopName+", PassworD: "+str(password))
                
                args = ['--serverURL=%s' % serverURL,
                        f'--userName={userName}',
                        '--password=%s' % password,
                        f'--desktopName={desktopName}.{domain}.com',
                        '--desktopProtocol=%s' % 'Blast',
                        '--desktopLayout=%s' % desktopSize,
                        f'--domainName={domain}.com'  ]
                args.insert(0, cmd)
                
                args = ' '.join(args)
                # kits.Comment(f"Launch cmd: {' '.join(args)}")
                flags = DETACHED_PROCESS | CREATE_NEW_PROCESS_GROUP
                rsub.CREATE_NEW_CONSOLE
                self.cache['connectTS'] = time.strftime("%Y-%m-%dT%H:%M:%S", time.localtime())
                # kits.Comment(f"launch {self.os} client at {self.cache['connectTS']}")
                self.cache['connStart'] = time.time()
                print(args)
                self.remoteP = rsub.Popen(args, close_fds=True, creationflags=flags)

            else:
                cmd = 'horizon-client'
                args = ["-q",
                       '--serverURL %s' % serverURL,
                        f'--userName={userName}@{domain}',
                        '--password=%s' % password,
                        '--desktopName %s' % desktopName,
                        '--protocol %s' % 'BLAST',
                        '--desktopSize %s' % desktopSize ]
                for i in args:
                    cmd = '%s %s' % (cmd, i)
                kits.Comment(cmd)
                self.cache['connectTS'] = time.strftime("%Y-%m-%dT%H:%M:%S", time.localtime())
                kits.Comment(f"launch {self.os} client at {self.cache['connectTS']}")
                self.cache['connStart'] = time.time()
                self.remoteP = rsub.Popen(cmd, shell=True)
            conn.close()
            break

        print(f'########################- {userName} Start login on os:{self.os} from {node}({self.ip}) -################################')

    def RemoteClient_LaunchToAppsRpyc(self, appName='Terminal', userName='qe', password='VMware123',
                                      domain='LXD', h264=True, udp='mix'):
        serverURL = self.broker
        timeout = 600
        node = ''
        print(f"remote IP:{self.ip}")
        windowName = appName
        if appName == 'Terminal':
            windowName = f"{userName}@rh95rdsh:~"
        while timeout > 0:
            conn = ''
            try:
                conn = rpyc.classic.connect(self.ip)
            except Exception as e:
                print("Exception", e)
                time.sleep(10)
                timeout -= 10
                continue

            rsys = conn.modules.sys
            rsub = conn.modules.subprocess
            rplatform = conn.modules.platform
            rgui = conn.modules.pyautogui
            rper = conn.modules.pyperclip
            rwin = conn.modules.pywinauto
            node = rplatform.node()
            if 'win32' in rsys.platform:
                if not h264:
                    if udp == 'mix':
                        self.__WinClient_SetCfgPref(h264='false')
                    elif udp == 'udp':
                        self.__WinClient_SetCfgPref(h264='false', udp='UDP-Only')
                    else:
                        self.__WinClient_SetCfgPref(h264='false', udp='TCP-Only')
                else:
                    if udp == 'mix':
                        self.__WinClient_SetCfgPref()
                    elif udp == 'udp':
                        self.__WinClient_SetCfgPref(udp='UDP-Only')
                    else:
                        self.__WinClient_SetCfgPref(udp='TCP-Only')

                CREATE_NEW_PROCESS_GROUP = 0x00000200
                DETACHED_PROCESS = 0x00000008
                cmd = 'horizon-client.exe '
                args = ['--serverURL=%s' % serverURL,
                        f'--userName={userName}@{domain}.com',
                        '--password=%s' % password,
                        '--appName=%s' % appName,
                        '--appProtocol=%s' % 'BLAST']
                args.insert(0, cmd)
                kits.Comment(f"Launch cmd: {' '.join(args)}")
                flags = DETACHED_PROCESS | CREATE_NEW_PROCESS_GROUP
                rsub.CREATE_NEW_CONSOLE
                self.cache['connectTS'] = time.strftime("%Y-%m-%dT%H:%M:%S", time.localtime())
                kits.Comment(f"launch {self.os} client at {self.cache['connectTS']}")
                self.cache['connStart'] = time.time()
                self.remoteP = rsub.Popen(args, close_fds=True, creationflags=flags)
            else:
                cmd = 'horizon-client'
                args = ["-q",
                       '--serverURL %s' % serverURL,
                        f'--userName={userName}@{domain}',
                        '--password=%s' % password,
                        '--applicationName %s' % appName,
                        '--protocol %s' % 'BLAST']
                for i in args:
                    cmd = '%s %s' % (cmd, i)
                kits.Comment(cmd)
                self.cache['connectTS'] = time.strftime("%Y-%m-%dT%H:%M:%S", time.localtime())
                kits.Comment(f"launch {self.os} client at {self.cache['connectTS']}")
                self.cache['connStart'] = time.time()
                self.remoteP = rsub.Popen(cmd, shell=True)

            # kits.Comment(f'########################- {userName} Open {appName} on os:{self.os} from {node}({self.ip}) -################################')
            waitTime = checkTime = 600
            while waitTime > 0:
                ret = rgui.getWindow(windowName)
                if not ret:
                    comments = f"Failed to Connect {appName} from {node} after {checkTime - waitTime} seconds"
                    print(comments)
                    time.sleep(15)
                    waitTime -= 15
                    continue
                else:
                    ret.set_foreground()
                    try:
                        rgui.click(960, 600, duration=0.2)
                    except Exception as e:
                        print("Exception", e)
                        pass

                    # minimize
                    ret.minimize()
                    # kits.Wait(10)
                    cpic = pic.GrabScreenRemote((self.ip, None))
                    # kits.Screenshot('minimize app window', cpic[0])
                    for cd in cpic:
                        if os.path.exists(cd):
                            os.remove(cd)
                    # restore
                    ret.restore()
                    # kits.Wait(10)
                    cpic = pic.GrabScreenRemote((self.ip, None))
                    # kits.Screenshot('restore app window', cpic[0])
                    for cd in cpic:
                        if os.path.exists(cd):
                            os.remove(cd)
                    # maximize
                    ret.maximize()
                    # kits.Wait(10)
                    cpic = pic.GrabScreenRemote((self.ip, None))
                    # kits.Screenshot('maximize app window', cpic[0])
                    for cd in cpic:
                        if os.path.exists(cd):
                            os.remove(cd)
                    rwin.keyboard.send_keys('{ENTER}')
                    cmd = 'ls -R ~/tsclient'
                    rper.copy(cmd)
                    time.sleep(2)
                    rwin.keyboard.send_keys('^+v')
                    # kits.Wait(1)
                    rwin.keyboard.send_keys('{ENTER}')
                    # kits.Wait(2)
                    cpic = pic.GrabScreenRemote((self.ip, None))
                    # kits.Screenshot('list CDR Folder', cpic[0])
                    conn.close()
                    return True
            else:
                comments = "Failed to Connect from {} Client".format(self.os)
                kits.Verify(False, True, comments, screenshot=False)
                conn.close()
                return False

    def RemoteClient_CheckDesktopOnRemoteClient(self, remoteClientIP, remoteAgentIP, remoteAgentPort,
                                                timeout=60, threshold=0.8, expectation=True):
        t = timeout
        rets = []
        while timeout > 0:
            apic = []
            for idx in range(5):
                apic = pic.GrabScreenRemote((remoteAgentIP, None), port=remoteAgentPort)
                if len(apic):
                    break
                else:
                    time.sleep(2)
                    continue
            cpic = []
            for idx in range(5):
                cpic = pic.GrabScreenRemote((remoteClientIP,None))
                if len(cpic):
                    break
                else:
                    time.sleep(2)
                    continue

            print('Client Desktop Numbers {}'.format(len(cpic[1:])))
            print(f"agent desktop : {apic}")
            print(f"client desktop : {cpic}")

            rets.clear()
            clientImgsForUpload = []
            agentImgsForUpload = []
            try:
                ret, _ = pic.FindPic(apic[0], srcImages=cpic, threshold=threshold, matchAlgoIdx=1)
                print(f"{('not detected', 'detected')[ret]}, between client: {cpic[0]} and agent: {apic[0]}")
                rets.append(ret)
            except Exception as e:
                print("Exception", e)

            clientImgsForUpload.append(cpic[0])
            agentImgsForUpload.append(apic[0])

            # racetrack failed to receive screen shot larger than 1M
            for i, j in zip(clientImgsForUpload, agentImgsForUpload):
                if os.path.getsize(i) >> 20 > 1:
                    img = Image.open(i)
                    w, h = img.size
                    r = 480 / float(h)
                    new_w = int(w * r)
                    img2 = img.resize((new_w, 480), Image.Resampling.LANCZOS)
                    fn, fe = os.path.splitext(i)
                    fname = f"{fn}__{new_w}x480{fe}"
                    print(fname)
                    img2.save(fname)
                    kits.Screenshot('client desktop', fname)
                    time.sleep(2)
                    os.remove(fname)
                else:
                    kits.Screenshot('client desktop', i)
                    time.sleep(2)
                if os.path.getsize(j) >> 20 > 1:
                    img = Image.open(j)
                    w, h = img.size
                    r = 480 / float(h)
                    new_w = int(w * r)
                    img2 = img.resize((new_w, 480), Image.Resampling.LANCZOS)
                    fn, fe = os.path.splitext(j)
                    fname = f"{fn}__{new_w}x480{fe}"
                    print(fname)
                    img2.save(fname)
                    kits.Screenshot('agent desktop', fname)
                    time.sleep(2)
                    os.remove(fname)
                else:
                    kits.Screenshot('agent desktop', j)
                    time.sleep(2)

            result = all(rets)
            print(f"display checking per monitor results {rets}")
            if not expectation:
                result = not any(rets)
            for cd in cpic:
                if os.path.exists(cd):
                    os.remove(cd)
            for ad in apic:
                if os.path.exists(ad):
                    os.remove(ad)

            if result:
                kits.Verify(result, expectation, 'Remote desktop %s is shown in client host %s' %(remoteAgentIP, remoteClientIP))
                return True
            else:
                rets.clear()
                time.sleep(10)
                timeout -= 10
        else:
            kits.Verify(False, True, f'Remote desktop {remoteAgentIP} is not shown in client host {remoteClientIP} within {t} seconds')
            return False

    def RemoteClient_IsRemoteDesktopShownRpyc(self, remoteDesktopIP):
        return self.IsRemoteDesktopShownMss(remoteDesktopIP, clientHost=self.ip)

    def RemoteClient_KillClient(self, check=False, timeout=30):
        conn = rpyc.classic.connect(self.ip, ipv6=isipv6)
        rsys = conn.modules.sys
        rsub = conn.modules.subprocess
        rsh  = conn.modules.shlex
        if 'win32' in rsys.platform:
            cmd = 'horizon-client.exe --shutdown'
        else:
            cmd = 'killall horizon-client'
        rsub.call(rsh.split(cmd))
        time.sleep(5)

        # safeguard
        if 'win32' in rsys.platform:
            # since "--shutdown" should handle the client quit procedure
            rsub.call(rsh.split("taskkill /im horizon-client.exe"))
            rsub.call(rsh.split("taskkill /im horizon-protocol.exe"))
            time.sleep(2)
        else:
            # for linux, we should also kill crtbora
            if 'linux' in rsys.platform:
                rsub.call(rsh.split("killall horizon-client-crtbora"))
                time.sleep(2)
                rsub.call(rsh.split("killall horizon-client"))
            time.sleep(2)
            rsub.call(rsh.split("killall horizon-protocol"))
            time.sleep(2)
            if 'linux' in rsys.platform:
                rsub.call(rsh.split("killall thnuclnt "))
                time.sleep(2)
        conn.close()

    def RemoteClient_KillChomeBrowser(self, check=False, timeout=30):
        conn = rpyc.classic.connect(self.ip, ipv6=isipv6)
        rsys = conn.modules.sys
        ros = conn.modules.os

        if 'win32' in rsys.platform:
            ros.system("taskkill /im chrome.exe")

        conn.close()

    def RemoteClient_KillFileExplorer(self, check=False, timeout=30):
        conn = rpyc.classic.connect(self.ip, ipv6=isipv6)
        rsys = conn.modules.sys

        if 'linux' in rsys.platform:
            killCmd = 'killall nautilus '

            rsub = conn.modules.subprocess
            rsh  = conn.modules.shlex

            rsub.call(rsh.split(killCmd))
            time.sleep(2)
        conn.close()

    def LaunchToDesktopWithWeb(self, pool, executor='localhost', user='zyc1', password='vmware',
                               domain='LXD', browser='chrome', bypassUAG=True):
        portalURL = 'https://%s' % self.broker
        if not bypassUAG:
            portalURL = 'https://%s' % self.uag
        if not domain.endswith('.com'):
            domain += '.com'
        userName = "%s@%s" % (user, domain.upper())
        desktopName = pool
        if 'darwin' in sys.platform:
            browser = 'safari'
        print(f'pool:{pool}, broker: {self.broker}, userName, {userName}')
        seleniumlib.LaunchBrowser(executor, browser)
        seleniumlib.NavigateToPortal(executor, portalURL)
        time.sleep(2)
        seleniumlib.LaunchWebClient(executor)
        time.sleep(5)
        seleniumlib.AuthWithAD(executor, userName, password)
        seleniumlib.ConnectToDesktop(executor, desktopName)
        kits.Comment(f'########################- {userName} on {browser}({executor}) -################################')

    def LaunchToDesktopWithWeb_CloseBrowser(self, executor='localhost'):
        try:
            seleniumlib.CloseBrowser(executor)
        except Exception as e:
            pass

    def VerifyDump(self, ts):
        comments = f"Check Client Panic since {ts}"
        print(comments)
        ts_old = datetime.strptime(ts, '%Y-%m-%d %H:%M:%S').timestamp()
        if 'win32' in sys.platform:
            self.__WinClient_CheckPanicViaLog(ts_old)
            self.__WinClient_CheckDumps(ts_old)
        elif 'linux' in sys.platform:
            self.__LinuxClient_CheckPanicViaLog()
            self.__LinClient_CheckDumps()
        else:
            print('not implemented')

    def UploadRMKSLog(self, ts):
        if not ts:
            ts = datetime.now()
        ts_old = datetime.strptime(ts, '%Y-%m-%d %H:%M:%S').timestamp()
        comments = f"Upload Client rmks log since {ts}"
        print(comments)
        if 'win32' in sys.platform:
            self.__WinClient_GetMKSLog(ts_old)

    def RecordAudio(self, audioFile):
        if not 'darwin' in sys.platform:
            return self.__client_Record(audioFile=audioFile)
        else:
            print('not implemented')

    def PlayAudio(self, audioFile):
        if not 'darwin' in sys.platform:
            return self.__client_Play(audioFile=audioFile)
        else:
            print('not implemented')

    def PktCapture(self, interface, pktfile):
        if 'win32' in sys.platform:
            return self.__WinClient_StartPktCapture(interface, pktfile)
        else:
            print('not implemented')


    def StartPktCapture(self, interface="Ethernet0", pktstore=lib.auto.clientData.get('pktstore', r'E:\pktstore')):
        if not os.path.exists(pktstore):
            os.makedirs(pktstore)
        now = datetime.now()
        ts = now.strftime('%Y-%m-%d-%H-%M-%S')
        pktname = f'{ts}.pcapng'
        pktfile = os.path.normpath(os.path.join(pktstore, pktname))
        kits.Comment(f'save Wireshark packets on {pktfile}')
        self.tsharkThread = Thread(target=self.PktCapture, args=(interface, pktfile))
        self.tsharkThread.start()

    def StopPktCapture(self):
        # process terminate is not guaranteed to kill tshark
        if 'win32' in sys.platform:
            self.__WinClient_StopPktCapture()

    def GetClientHostIP(self):
        return socket.gethostbyname(socket.gethostname())

    def CheckProcess(self, procName='horizon-client'):
        import psutil
        ls = []
        for p in psutil.process_iter():
            name_, exe, cmdlne = "", "", []
            try:
                name_ = p.name()
                cmdline = p.cmdline()
                exe = p.exe()
            except (psutil.AccessDenied, psutil.ZombieProcess):
                pass
            except psutil.NoSuchProcess:
                continue
            except Exception as e:
                print("Exception in get process name", e)
                continue
            if procName in name_ or procName in cmdline or procName in os.path.basename(exe):
                ls.append(procName)
        return ls

    def GetClientBuild(self):
        if 'win32' in sys.platform:
            return self.__WinClient_GetBuild()
        elif 'linux' in sys.platform:
            return self.__LinClient_GetBuild()
        elif 'darwin' in sys.platform:
            return self.__MacClient_GetBuild()
        else:
            pass

    def __WinClient_GetBuild(self):
        regClient = r"HKEY_LOCAL_MACHINE\SOFTWARE\WOW6432Node\Omnissa\Horizon\Client"
        regQuery = "BuildNumber"
        regQuery2 = "Version"
        number = 'Unknown'
        version = 'Unknown'
        try:
            number = self.__WinClient_RegValue(regClient, regQuery)
        except Exception as e:
            print("Exception", e)
        try:
            version = self.__WinClient_RegValue(regClient, regQuery2)
        except Exception as e:
            print("Exception", e)
        return "{}-{}".format(version, number)

    def __WinClient_RegValue(self, path, name="", start_key=None):
        if isinstance(path, str):
            path = path.split("\\")
        if start_key is None:
            start_key = getattr(winreg, path[0])
            return self.__WinClient_RegValue(path[1:], name, start_key)
        else:
            subkey = path.pop(0)
        with winreg.OpenKey(start_key, subkey) as handle:
            if path:
                return self.__WinClient_RegValue(path, name, handle)
            else:
                desc, i = None, 0
                while not desc or desc[0] != name:
                    desc = winreg.EnumValue(handle, i)
                    i += 1
                return desc[1]

    def __LinClient_GetBuild(self):
        build = 'unknown'
        cmd = "horizon-client --version 2>/dev/null | head -n 1 | grep -Eo '[0-9.]+'"
        outs = subprocess.getoutput(cmd)
        if outs:
            build = outs.replace('\n', '-')
        return build

    def __LinClient_GetUsbDevices(self):
        cmd = "lsusb | sort"
        devOuts = subprocess.Popen(cmd,
                                   shell=True,
                                   stdout=subprocess.PIPE,
                                   stderr=subprocess.PIPE).communicate()[0].decode()

        cmd = "lsusb -t"
        treeOuts = subprocess.Popen(cmd,
                                    shell=True,
                                    stdout=subprocess.PIPE,
                                    stderr=subprocess.PIPE).communicate()[0].decode()

        d = UsbInfo()
        allusbs = [UsbDev(x) for x in d.parseUsbDevs(devOuts)]
        infs = d.parseUsbTree(treeOuts)
        usbInfs = [UsbInf(x) for x in infs]
        [x.updateInterfaces(y) for y in usbInfs for x in allusbs]
        [x.update() for x in allusbs]
        nonHubDevs = [ x for x in allusbs if not any('hub' in y.usbclass for y in x.interfaces) ]
        return nonHubDevs

    def __LinClient_GetPrinters(self):
        cmd = 'lpstat -p'
        outs = self.GetCmdOutput(cmd, shell=True)
        print(f"Client Printers:\n{outs}")
        infos = re.findall(r'printer\s+(\S+)\s+is\s+(\S+)\.', outs)
        return infos

    def __WinClient_GetPrinters(self):
        infos = []
        infos.clear()
        for p in win32print.EnumPrinters(win32print.PRINTER_ENUM_LOCAL):
            print(p[2])
            infos.append(p[2])
        return infos

    def __WinClient_GetPdfText(self, pdfFile):
        import pytesseract
        from pdf2image import convert_from_path

        txts = ''
        # Store all the pages of the PDF in a variable
        pages = convert_from_path(pdfFile, 500)
        # Counter to store images of each page of PDF to image
        image_counter = 1
        # Iterate through all the pages stored above
        for page in pages:
            filename = "page_"+str(image_counter)+".jpg"
            # Save the image of the page in system
            page.save(filename, 'JPEG')
            # Increment the counter to update filename
            image_counter += 1
        filelimit = image_counter - 1
        # Iterate from 1 to total number of pages
        for i in range(1, filelimit + 1):
            filename = "page_"+str(i)+".jpg"
            # Recognize the text as string in image using pytesserct
            text = str(((pytesseract.image_to_string(Image.open(filename)))))
            txts += text

        p = Path(os.getcwd())
        page_imgs = list(p.glob("page_*.jpg"))
        print(page_imgs)
        for page in page_imgs:
            os.remove(page)
        return txts

    def __LinClient_GetPdfText(self, pdfFile):
        import fitz
        txts = ''
        with fitz.open(pdfFile) as docs:
            for page in docs:
                txts += page.get_text()
        return txts

    def GetPdfText(self, pdfFile):
        if 'linux' in sys.platform:
            return self.__LinClient_GetPdfText(pdfFile)
        elif 'win32' in sys.platform:
            return self.__WinClient_GetPdfText(pdfFile)
        else:
            return ''

    def GetUsbDevices(self, clientData):
        if not 'usbAutoDetect' in clientData:
            return
        if clientData['usbAutoDetect'].lower() == 'false':
            tmp = ast.literal_eval(clientData['storageDevs'])
            self.storageDevs = [list(map(hex, x)) for x in tmp]
            tmp = ast.literal_eval(clientData['hidDevs'])
            self.hidDevs = [list(map(hex, x)) for x in tmp]
        else:
            if 'linux' in sys.platform:
                allDevs = self.__LinClient_GetUsbDevices()
                allUsbs = [(x.vendor, x.vid, x.pid, x.interfaces[0].usbclass) for x in allDevs]
                # sled11 and rhel6 show HID but rhel7/8 show 'Human Interface Device'
                self.hidDevs = [ (x[1], x[2]) for x in allUsbs if 'hid' in x[3].lower()]
                self.hidDevs += [ (x[1], x[2]) for x in allUsbs if 'human' in x[3].lower()]
                self.storageDevs = [ (x[1], x[2]) for x in allUsbs if 'stor' in x[3].lower()]
            else:
                pass

    def GetPrinters(self):
        if 'linux' in sys.platform:
            return self.__LinClient_GetPrinters()
        elif 'win32' in sys.platform:
            return self.__WinClient_GetPrinters()
        else:
            pass

    def CleanPDFDir(self):
        pdfFolder = os.path.expanduser("~/PDF")
        if 'win32' in sys.platform:
            pdfFolder = "C:\\PDF"
        shutil.rmtree(pdfFolder, ignore_errors=True)

    def GetLatestPDF(self):
        pdfFolder = os.path.expanduser("~/PDF")
        if 'win32' in sys.platform:
            pdfFolder = "C:\\PDF"

        p = Path(pdfFolder)
        pdfFiles = list(p.glob("*.pdf"))
        print('-------')
        print(pdfFiles)
        if len(pdfFiles):
            return max(pdfFiles, key=os.path.getctime).as_posix()
        return None

    def __MacClient_GetBuild(self):
        build = 'unknown'
        cmd = 'defaults read /Applications/VMware\ Horizon\ Client.app/Contents/Info.plist CFBundleVersion'
        build = subprocess.getoutput(cmd)
        return build

    def GetCurrentTS(self):
        # follow the convention
        return time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())

    def SleepClientNetwork(self, sec=60):
        localScript = os.path.join(os.getcwd(), 'ps', 'SleepNetwork.ps1')
        cmd1 = 'C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\powershell.exe -ExecutionPolicy ByPass '
        cmd2 = '%s %s' % (localScript, sec)
        if self.remote:
            conn = rpyc.classic.connect(self.ip, ipv6=isipv6)
            ros = conn.modules.os
            if not ros.path.exists(localScript):
                kits.Comment("SleepNetwork.ps1 NOT Found!")
            rcall = rpyc.async_(conn.modules.subprocess.call, ipv6=isipv6)
            kits.Comment("Sleep Network {} seconds.".format(sec))
            rcall(cmd1 + cmd2, shell=True)
            conn.close()
        else:
            kits.Comment("Sleep Network {} seconds.".format(sec))
            if 'win32' in sys.platform:
                self.GetCmdOutput(cmd1+cmd2, shell=True)
            elif 'linux' in sys.platform:
                cmd = 'sudo ifconfig %s down'
                self.GetCmdOutput(cmd, shell=True)
                time.sleep(sec)
                cmd = 'sudo ifconfig %s up'
                self.GetCmdOutput(cmd, shell=True)
            else:
                pass

    def RemoteClient_LaunchShadowSessionRpyc(self, poolInfo, collabId=None,
                                             collaborator='lvl1', desktopSize=DEFAULTDESKTOPSIZE):

        conn = rpyc.classic.connect(self.ip, ipv6=isipv6)
        rsys = conn.modules.sys
        rsub = conn.modules.subprocess

        if 'win32' in rsys.platform:
            modePath = '"HKLM\Software\Policies\VMware, Inc.\VMware VDM\Client\Security"'
            modeValue = '0'
            modeKey = "certcheckmode"
            cmd = 'reg add %s /v %s /d %s /t REG_SZ /f' % (modePath, modeKey, modeValue)
            rsub.call(cmd)
            CREATE_NEW_PROCESS_GROUP = 0x00000200
            DETACHED_PROCESS = 0x00000008
            cmd = 'horizon-client.exe '
            args = ['--serverURL=%s' % self.broker,
                    '--userName=%s@%s' % (collaborator, poolInfo['domain']),
                    '--password=%s' % 'vmware',
                    '--desktopName=%s' % collabId,
                    '--desktopLayout=%s' % desktopSize
                    ]
            args.insert(0, cmd)
            cmdForPrint = ' '.join(map(str, args))
            print(args)
            print(cmdForPrint)
            kits.Comment(f"remote Windows client launch cmd:\n {cmdForPrint}")
            self.remoteP = rsub.Popen(args, creationflags=DETACHED_PROCESS | CREATE_NEW_PROCESS_GROUP)

        else:
            cmd = 'horizon-client'
            args = ["-q",
                    '--serverURL %s' % self.broker,
                    '--userName=%s@%s' % (collaborator, poolInfo['domain']),
                    '--password=%s' % 'vmware',
                    '--shadowSessionName=%s' % collabId,
                    '--desktopSize=%s' % desktopSize
                    ]

            for i in args:
                cmd = '%s %s' % (cmd, i)
            print(cmd)
            kits.Comment(f"remote Linux client launch cmd:\n {cmd}")
            self.remoteP = rsub.Popen(cmd, shell=True)
        conn.close()

    def LaunchShadowSessionWithWeb(self, poolInfo, executor='localhost',
                                   collabId=None, collaborator='lvl1',
                                   browser='chrome', bypassUAG=True):
        portalURL = 'https://%s' % self.broker
        if not bypassUAG:
            portalURL = 'https://%s' % self.uag
        userName = "%s@%s" % (collaborator, poolInfo['domain'])
        password = 'vmware'
        if 'darwin' in sys.platform:
            browser = 'safari'
        string = '/portal/webclient/index.html?collabSessionId=%s' % collabId
        portalURL += string
        seleniumlib.LaunchBrowser(executor, browser)
        seleniumlib.NavigateToPortal(executor, portalURL)
        seleniumlib.AuthWithAD(executor, userName, password)

    def LaunchShadowSessionByMethod(self, poolInfo, remoteDesktopIP, client_type='win',
                                   launchMethod='cmd', collabId=None, collaborator='lvl1', client_uri=None):
        # Ensure 'Fittoviewer' is DISABLED in each shadow client
        if client_type in ('win', 'linux'):
            if launchMethod == 'cmd':
                self.RemoteClient_LaunchShadowSessionRpyc(poolInfo, collabId, collaborator)
            else:
                self.RemoteClient_LaunchShadowSessionRpyc(poolInfo, client_uri, collaborator)
        elif client_type in 'html':
            self.LaunchShadowSessionWithWeb(poolInfo, self.ip, collabId, collaborator)
            kits.Wait(20)
        # we use this method since we always launch the client in predefined size
        self.IsRemoteDesktopShownMss(remoteDesktopIP, self.ip, threshold=0.6, timeCput=False)

    def KillShadowClient(self, client_type):
        if client_type == 'html':
            self.LaunchToDesktopWithWeb_CloseBrowser(self.ip)
        else:
            self.RemoteClient_KillClient()

    def ClearLogs(self):
        if 'linux' in sys.platform:
            self.__LinClient_CollectLogs()
            time.sleep(2)
            self.__LinClient_ClearLogs()



client = Client()
remoteclients = []
shadowclients = []
