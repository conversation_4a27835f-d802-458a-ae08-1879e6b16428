/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * vscrd_version.h --
 *
 *       Version definitions for hznvscrd driver.
 *
 * NOTES:
 * 1) vscrd driver version is in the format A.B.C.D where A remains
 *    constant, B is a branch number, C is an individual change and D remains
 *    0000.
 */

#ifndef _HZNVSCRD_VERSION_H_
#define _HZNVSCRD_VERSION_H_

// clang-format off

#define HZNVSCRD_DRIVER_VERSION           7.0.5.0
#define HZNVSCRD_DRIVER_VERSION_COMMAS    7,0,5.0
#define HZNVSCRD_DRIVER_VERSION_STRING    "7.0.5.0"

// clang-format on

#endif /* _VSCRD_VERSION_H_ */
