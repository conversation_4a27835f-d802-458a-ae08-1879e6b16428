/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * clipboardRedirectionTest.cpp --
 *
 *      Clipboard Redirection API component test cases.
 */


#include "ciStdafx.h"
#include "clipboardCommonUtils.h"
#include "clipboardRedirectionTest.h"

#define RTF_TEST_FILE "ClipboardTestRichText.rtf"
#define RTF_IMAGE_TEST_FILE "ClipboardTestRichTextImage.rtf"
#define IMAGE_TEST_FILE "ClipboardTestImage.png"
#define HTML_TEST_FILE "ClipboardTestHtmlFile.html"
#define PLAIN_TEXT_TEST_FILE "ClipboardTestPlainText.txt"
#define PLAIN_TEXT_TEST_FILE_UTF8 "ClipboardTestPlainText.utf8"
#define PLAIN_TEXT_TEST_FILE_999BYTES "ClipboardTestPlainText_999Bytes.txt"

#if defined(_WIN32)
#   define Clipboard_DataSize_Config_File "Clipboard_DataSize_Config_File_Win.json"
#elif __linux__
#   define Clipboard_DataSize_Config_File "Clipboard_DataSize_Config_File_Lin.json"
#elif __APPLE__
#   define Clipboard_DataSize_Config_File "Clipboard_DataSize_Config_File_Mac.json"
#endif

std::vector<ClipboardData> GeneratorEvaluationTest::param_value_vector;


/*
 *-----------------------------------------------------------------------------
 *
 * RunParamCases --
 *
 *      This is the dll function entrance and if you want to write a test
 *      component with value-parameteried testing, you should follow write
 *      below function in your project.
 *
 *-----------------------------------------------------------------------------
 */

extern "C" RX_CI_PLUGIN_API int
RunParamCases(const char *param,                      // IN
              SessionManagementInterface *sessionMgr, // IN
              FrameworkInterface *framework,          // IN
              int argc,                               // IN
              char **argv)                            // IN
{
   TestBase::SetFramework(framework);
   AutoTestBase::SetSessionManager(sessionMgr);

   // log init
   Preference_Init();
   LogOutput *output =
      Log_InitWithCustom(Log_CfgInterface(), &AutoTestBase::LogCustomMsgFuncImpl, HZN_LOG_DEBUG_10);

   // Read and parse json file for parameterized test
   std::string configChar;
   GetLocalPlainTextContent(Clipboard_DataSize_Config_File, configChar);

   char *configBuff = new char[configChar.size() + 1];
   memcpy(configBuff, configChar.c_str(), configChar.size() + 1);

   cJSON *config = cJSON_Parse(configBuff);
   if (config) {
      for (int i = 0; i < cJSON_GetArraySize(config); i++) {
         cJSON *item = cJSON_GetArrayItem(config, i);
         GeneratorEvaluationTest::add_param_value(
            ClipboardData((TestDataType)cJSON_GetObjectItem(item, "TestType")->valueint,
                          cJSON_GetObjectItem(item, "FileName")->valuestring,
                          cJSON_GetObjectItem(item, "CaseName")->valuestring));
      }
      cJSON_Delete(config);
   }

   delete[] configBuff;

   ::testing::InitGoogleTest(&argc, argv);

   int ret = RUN_ALL_TESTS();

   // log exit
   Log_Exit();
   Preference_Exit();

   return ret;
}


/*
 *-----------------------------------------------------------------------------
 *
 * ClipboardDataSize::SendClipboardTextData --
 *
 *      Test case: send local system clipboard plain text data of different
 *      sizes to server.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

TEST_P(ClipboardDataSize, SendClipboardTextData)
{
   ClipboardData data = GetParam();
   EXPECT_TRUE(SendLocalClipboardData(data.type, data.fileName));
}

// Instantiate parameter generators
INSTANTIATE_TEST_SUITE_P(ComponentTest_Clipboard, ClipboardDataSize,
                         testing::ValuesIn(GeneratorEvaluationTest::param_value_test()),
                         CustomerPrameterNameFucntion);


/*
 *-----------------------------------------------------------------------------
 *
 * ComponentTest_Clipboard_BasicFunctions::SendClipboardTextData --
 *
 *      Test case: send local system clipboard plain text data to server.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(ComponentTest_Clipboard_BasicFunctions, P0_SendClipboardTextData)
{
   EXPECT_TRUE(
      SendLocalClipboardData(ClipboardTestDataType_Text, PLAIN_TEXT_TEST_FILE, CheckAuditMessage));
}


/*
 *-----------------------------------------------------------------------------
 *
 * ComponentTest_Clipboard_BasicFunctions::RequestClipboardTextData --
 *
 *      Test case: request server system clipboard data to client.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(ComponentTest_Clipboard_BasicFunctions, P0_RequestClipboardTextData)
{
   EXPECT_TRUE(RequestRemoteClipboardData(ClipboardTestDataType_Text, PLAIN_TEXT_TEST_FILE,
                                          CheckAuditMessage));
}


#if defined(_WIN32) || __APPLE__
/*
 *-----------------------------------------------------------------------------
 *
 * ComponentTest_Clipboard_BasicFunctions::SendClipboardRTFData --
 *
 *      Test case: send local system clipboard RTF data to server.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(ComponentTest_Clipboard_BasicFunctions, P0_SendClipboardRTFData)
{
   EXPECT_TRUE(SendLocalClipboardData(ClipboardTestDataType_RTF, RTF_TEST_FILE, CheckAuditMessage));
}


/*
 *-----------------------------------------------------------------------------
 *
 * ComponentTest_Clipboard_BasicFunctions::RequestClipboardRTFData --
 *
 *      Test case: request server system clipboard RTF data to client.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(ComponentTest_Clipboard_BasicFunctions, P0_RequestClipboardRTFData)
{
   EXPECT_TRUE(
      RequestRemoteClipboardData(ClipboardTestDataType_RTF, RTF_TEST_FILE, CheckAuditMessage));
}


#   if defined(_WIN32)
/*
 *-----------------------------------------------------------------------------
 *
 * ComponentTest_Clipboard_BasicFunctions::SendClipboardHTMLData --
 *
 *      Test case: send local system clipboard HTML data to server.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(ComponentTest_Clipboard_BasicFunctions, P0_SendClipboardHTMLData)
{
   EXPECT_TRUE(
      SendLocalClipboardData(ClipboardTestDataType_HTML, HTML_TEST_FILE, CheckAuditMessage));
}


/*
 *-----------------------------------------------------------------------------
 *
 * ComponentTest_Clipboard_BasicFunctions::RequestClipboardHTMLData --
 *
 *      Test case: request server system clipboard HTML data to client.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(ComponentTest_Clipboard_BasicFunctions, P0_RequestClipboardHTMLData)
{
   EXPECT_TRUE(
      RequestRemoteClipboardData(ClipboardTestDataType_HTML, HTML_TEST_FILE, CheckAuditMessage));
}


/*
 *-----------------------------------------------------------------------------
 *
 * ComponentTest_Clipboard_BasicFunctions::SendClipboardImageAndTextData --
 *
 *      Test case: send local system clipboard image and text data to server.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(ComponentTest_Clipboard_BasicFunctions, P1_SendClipboardImageAndTextData)
{
   EXPECT_TRUE(SendLocalClipboardData(ClipboardTestDataType_RTF_Image, RTF_IMAGE_TEST_FILE));
}


/*
 *-----------------------------------------------------------------------------
 *
 * ComponentTest_Clipboard_BasicFunctions::RequestClipboardImageAndTextData --
 *
 *      Test case: request server system clipboard image and text data to client.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(ComponentTest_Clipboard_BasicFunctions, P1_RequestClipboardImageAndTextData)
{
   EXPECT_TRUE(RequestRemoteClipboardData(ClipboardTestDataType_RTF_Image, RTF_IMAGE_TEST_FILE));
}
#   endif // _WIN32


/*
 *-----------------------------------------------------------------------------
 *
 * ComponentTest_Clipboard_BasicFunctions::SendClipboardImageData --
 *
 *      Test case: send local system clipboard image data to server.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(ComponentTest_Clipboard_BasicFunctions, P0_SendClipboardImageData)
{
   EXPECT_TRUE(
      SendLocalClipboardData(ClipboardTestDataType_Image, IMAGE_TEST_FILE, CheckAuditMessage));
}


/*
 *-----------------------------------------------------------------------------
 *
 * ComponentTest_Clipboard_BasicFunctions::RequestClipboardImageData --
 *
 *      Test case: request server clipboard image data to client side.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(ComponentTest_Clipboard_BasicFunctions, P0_RequestClipboardImageData)
{
   EXPECT_TRUE(
      RequestRemoteClipboardData(ClipboardTestDataType_Image, IMAGE_TEST_FILE, CheckAuditMessage));
}
#endif // _WIN32 || __APPLE__


/*
 *-----------------------------------------------------------------------------
 *
 * ComponentTest_Clipboard_AgentGPO::EnabledClipboardClientToServer --
 *
 *      Test case: Enabled Clipboard Client to Server.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(ComponentTest_Clipboard_AgentGPO, P1_EnabledClipboardClientToServer)
{
   SetServerClipboardDirection(EnabledClipboardClientToServer);
   DisconnectAndReconnect();

   EXPECT_TRUE(SendLocalClipboardData(ClipboardTestDataType_Text));
   EXPECT_FALSE(RequestRemoteClipboardData(ClipboardTestDataType_Text));
}


/*
 *-----------------------------------------------------------------------------
 *
 * ComponentTest_Clipboard_AgentGPO::EnabledClipboardBothDirection --
 *
 *      Test case: Enabled Clipboard both redirection.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(ComponentTest_Clipboard_AgentGPO, P0_EnabledClipboardBothDirection)
{
   // Set Clipboard old registry key to an unexpected value
   SetServerClipboardDirection(EnabledClipboardClientToServer);
   // Set Clipboard new registry key to an expected value
   SetServerClipboardDirectionUnifiedKey(EnabledClipboardBothDirection);
   DisconnectAndReconnect();

   EXPECT_TRUE(SendLocalClipboardData(ClipboardTestDataType_Text));
   EXPECT_TRUE(RequestRemoteClipboardData(ClipboardTestDataType_Text));
}


/*
 *-----------------------------------------------------------------------------
 *
 * ComponentTest_Clipboard_AgentGPO::EnabledClipboardServerToClient --
 *
 *      Test case: Enabled Clipboard Server to Client.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */


// Temporarily Disabling the failing P1_EnabledClipboardServerToClient component test to restore
// pipeline stability. Need to fix it later.
// Ref. Jira Ticket: VCART-5202, TestRail TC ID: C47040
// Automation Level Marked as Manual on TestRail for now

TEST_F(ComponentTest_Clipboard_AgentGPO, DISABLED_P1_EnabledClipboardServerToClient)
{
   // Set Clipboard registry key of HKLM to an unexpected value
   SetServerClipboardDirection(EnabledClipboardClientToServer);
   // Set Clipboard registry key of HKCU to an expected value
   SetServerClipboardDirection(EnabledClipboardServerToClient, Registry_HKCU);
   DisconnectAndReconnect();

   EXPECT_FALSE(SendLocalClipboardData(ClipboardTestDataType_Text));
   EXPECT_TRUE(RequestRemoteClipboardData(ClipboardTestDataType_Text));
}


/*
 *-----------------------------------------------------------------------------
 *
 * ComponentTest_Clipboard_AgentGPO::DisabledClipboardBothDirection --
 *
 *      Test case: Disabled Clipboard both redirection.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(ComponentTest_Clipboard_AgentGPO, P0_DisabledClipboardBothDirection)
{
   // Set Clipboard old registry key to an unexpected value
   SetServerClipboardDirection(EnabledClipboardServerToClient);
   // Set Clipboard new registry key under HKLM to an unexpected value
   SetServerClipboardDirectionUnifiedKey(EnabledClipboardBothDirection);
   // Set Clipboard new registry key under HKCU to an expected value
   SetServerClipboardDirectionUnifiedKey(DisabledClipboardBothDirection, Registry_HKCU);
   DisconnectAndReconnect();

   EXPECT_FALSE(SendLocalClipboardData(ClipboardTestDataType_Text));
   EXPECT_FALSE(RequestRemoteClipboardData(ClipboardTestDataType_Text));
}


/*
 *-----------------------------------------------------------------------------
 *
 * ComponentTest_Clipboard_AgentGPO::SessionDisabledClipboardBothDirection --
 *
 *      Test case: Disabled Clipboard both redirection by session registry.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(ComponentTest_Clipboard_AgentGPO, P1_SessionDisabledClipboardBothDirection)
{
   // Set Clipboard new registry key under HKCU to an unexpected value
   SetServerClipboardDirectionUnifiedKey(EnabledClipboardClientToServer, Registry_HKCU);
   // Set Clipboard new session registry key under HKLM to an expected value
   SetServerClipboardDirectionSessionKey(DisabledClipboardBothDirection);

   EXPECT_FALSE(SendLocalClipboardData(ClipboardTestDataType_Text));
   EXPECT_FALSE(RequestRemoteClipboardData(ClipboardTestDataType_Text));
   ClearServerClipboardSessionKey();
}


/*
 *-----------------------------------------------------------------------------
 *
 * ComponentTest_Clipboard_AgentGPO::NotConfigClipboardDirection --
 *
 *      Test case: Not configure Clipboard direction.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(ComponentTest_Clipboard_AgentGPO, P0_NotConfigClipboardDirection)
{
   ClearServerClipboardSessionKey();
   ClearServerClipboardUnifiedKey();
   ClearServerClipboardRegKey();
   DisconnectAndReconnect();

   EXPECT_TRUE(SendLocalClipboardData(ClipboardTestDataType_Text));
   EXPECT_FALSE(RequestRemoteClipboardData(ClipboardTestDataType_Text));
}


#if defined(_WIN32) || __APPLE__
/*
 *-----------------------------------------------------------------------------
 *
 * ComponentTest_Clipboard_SizeLimit::SendRTFDataGreaterThanThreshold --
 *
 *      Test case: send local system clipboard RTF data greater than threshold to server.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(ComponentTest_Clipboard_SizeLimit, P1_SendRTFDataGreaterThanThreshold)
{
   EXPECT_FALSE(SendLocalClipboardData(ClipboardTestDataType_RTF_Image, RTF_IMAGE_TEST_FILE));
}


/*
 *-----------------------------------------------------------------------------
 *
 * ComponentTest_Clipboard_SizeLimit::SendImageDataGreaterThanThreshold --
 *
 *      Test case: send local system clipboard image data greater than threshold to server.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(ComponentTest_Clipboard_SizeLimit, P1_SendImageDataGreaterThanThreshold)
{
   EXPECT_FALSE(SendLocalClipboardData(ClipboardTestDataType_Image, IMAGE_TEST_FILE));
}
#endif // _WIN32 || __APPLE__


/*
 *-----------------------------------------------------------------------------
 *
 * ComponentTest_Clipboard_SizeLimit::SendTextDataClose2Threshold --
 *
 *      Test case: send local system clipboard text data close to threshold to
 *      server.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(ComponentTest_Clipboard_SizeLimit, P1_SendTextDataClose2Threshold)
{
   EXPECT_TRUE(
      SendLocalClipboardData(ClipboardTestDataType_Text_999Bytes, PLAIN_TEXT_TEST_FILE_999BYTES));
}


/*
 *-----------------------------------------------------------------------------
 *
 * ComponentTest_Clipboard_SizeLimit::SendUTF8DataGreaterThanThreshold --
 *
 *      Test case: send local system clipboard UTF-8 data greater than
 *      threshold to server.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(ComponentTest_Clipboard_SizeLimit, P1_SendUTF8DataGreaterThanThreshold)
{
   EXPECT_TRUE(SendLocalClipboardData(ClipboardTestDataType_UTF8, PLAIN_TEXT_TEST_FILE_UTF8));
}


/*
 *-----------------------------------------------------------------------------
 *
 * ComponentTest_Clipboard_SizeLimit_FineGrain::TextBelowLimitedSizeFromClientToServer --
 *
 *      Test case: Copy text below the limited size from Client to Server.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(ComponentTest_Clipboard_SizeLimit_FineGrain, P1_TextBelowLimitedSizeFromClientToServer)
{
   EXPECT_TRUE(SendLocalClipboardData(ClipboardTestDataType_Text));
}


/*
 *-----------------------------------------------------------------------------
 *
 * ComponentTest_Clipboard_SizeLimit_FineGrain::TextGreaterLimitedSizeFromServerToClient --
 *
 *      Test case: Copy text greater than limited size from Server to Client.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(ComponentTest_Clipboard_SizeLimit_FineGrain, P1_TextGreaterLimitedSizeFromServerToClient)
{
   EXPECT_FALSE(RequestRemoteClipboardData(ClipboardTestDataType_Text_999Bytes,
                                           PLAIN_TEXT_TEST_FILE_999BYTES));
}


#if defined(_WIN32) || __APPLE__
/*
 *-----------------------------------------------------------------------------
 *
 * ComponentTest_Clipboard_SizeLimit_FineGrain::RtfGreaterLimitedSizeFromClientToServer --
 *
 *      Test case: Copy rtf text greater than limited size from Client to Server.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(ComponentTest_Clipboard_SizeLimit_FineGrain, P1_RtfGreaterLimitedSizeFromClientToServer)
{
   EXPECT_FALSE(SendLocalClipboardData(ClipboardTestDataType_RTF_Image, RTF_IMAGE_TEST_FILE));
}


/*
 *-----------------------------------------------------------------------------
 *
 * ComponentTest_Clipboard_SizeLimit_FineGrain::RtfGreaterLimitedSizeFromServerToClient --
 *
 *      Test case: Copy rtf text greater than limited size from Server to Client.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(ComponentTest_Clipboard_SizeLimit_FineGrain, P1_RtfGreaterLimitedSizeFromServerToClient)
{
   EXPECT_FALSE(RequestRemoteClipboardData(ClipboardTestDataType_RTF_Image, RTF_IMAGE_TEST_FILE));
}
#endif // _WIN32 || __APPLE__


/*
 *-----------------------------------------------------------------------------
 *
 * ComponentTest_Clipboard_DataFilter::FilterTextDataIncoming --
 *
 *      Test case: filter text out of the incoming clipboard data.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(ComponentTest_Clipboard_DataFilter, P1_FilterTextDataIncoming)
{
   EXPECT_FALSE(SendLocalClipboardData(ClipboardTestDataType_Text));
}


/*
 *-----------------------------------------------------------------------------
 *
 * ComponentTest_Clipboard_DataFilter::FilterTextDataOutgoing --
 *
 *      Test case: filter text out of the outgoing clipboard data.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(ComponentTest_Clipboard_DataFilter, P1_FilterTextDataOutgoing)
{
   EXPECT_FALSE(RequestRemoteClipboardData(ClipboardTestDataType_Text));
}


#if defined(_WIN32) || __APPLE__
/*
 *-----------------------------------------------------------------------------
 *
 * ComponentTest_Clipboard_DataFilter::FilterRTFDataIncoming --
 *
 *      Test case: filter Rich Text Format data out of the incoming clipboard data.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(ComponentTest_Clipboard_DataFilter, P1_FilterRTFDataIncoming)
{
   EXPECT_FALSE(SendLocalClipboardData(ClipboardTestDataType_RTF, RTF_TEST_FILE));
}


/*
 *-----------------------------------------------------------------------------
 *
 * ComponentTest_Clipboard_DataFilter::FilterRTFDataOutgoing --
 *
 *      Test case: filter Rich Text Format data out of the outgoing clipboard data.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(ComponentTest_Clipboard_DataFilter, P1_FilterRTFDataOutgoing)
{
   EXPECT_FALSE(RequestRemoteClipboardData(ClipboardTestDataType_RTF, RTF_TEST_FILE));
}


/*
 *-----------------------------------------------------------------------------
 *
 * ComponentTest_Clipboard_DataFilter::FilterImageDataIncoming --
 *
 *      Test case: filter image out of the incoming clipboard data.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(ComponentTest_Clipboard_DataFilter, P1_FilterImageDataIncoming)
{
   EXPECT_FALSE(SendLocalClipboardData(ClipboardTestDataType_Image, IMAGE_TEST_FILE));
}


/*
 *-----------------------------------------------------------------------------
 *
 * ComponentTest_Clipboard_DataFilter::FilterImageDataOutgoing --
 *
 *      Test case: filter image out of the outgoing clipboard data.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(ComponentTest_Clipboard_DataFilter, P1_FilterImageDataOutgoing)
{
   EXPECT_FALSE(RequestRemoteClipboardData(ClipboardTestDataType_Image, IMAGE_TEST_FILE));
}


#   if defined(_WIN32)
/*
 *-----------------------------------------------------------------------------
 *
 * ComponentTest_Clipboard_DataFilter::FilterHTMLDataIncoming --
 *
 *      Test case: filter HTML data out of the incoming clipboard data.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(ComponentTest_Clipboard_DataFilter, P1_FilterHTMLDataIncoming)
{
   EXPECT_FALSE(SendLocalClipboardData(ClipboardTestDataType_HTML, HTML_TEST_FILE));
}


/*
 *-----------------------------------------------------------------------------
 *
 * ComponentTest_Clipboard_DataFilter::FilterHTMLDataOutgoing --
 *
 *      Test case: filter HTML data out of the outgoing clipboard data.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(ComponentTest_Clipboard_DataFilter, P1_FilterHTMLDataOutgoing)
{
   EXPECT_FALSE(RequestRemoteClipboardData(ClipboardTestDataType_HTML, HTML_TEST_FILE));
}
#   endif // _WIN32
#endif    // _WIN32 || __APPLE__