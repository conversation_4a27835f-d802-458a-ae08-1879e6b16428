# Copyright (c) Omnissa, LLC. All rights reserved.
# This product is protected by copyright and intellectual property laws in the
# United States and other countries as well as by international treaties.
# -- Omnissa Restricted

import argparse
from glob import glob
import logging
import os
import shutil
import subprocess
import sys
import winreg
try:
    import simplejson as json
except ImportError:
    import json


class BlastInTheBox():
    """A class for operations related to Blast in the Box tests."""

    def __init__(self):
        """BlastInTheBox constructor."""
        self.log = logging.getLogger(self.__class__.__name__)


    def trigger_bitb(self, bitb_protocol='BEAT', bitb_encoder_config='H264',
                     bitb_address_type='IPv4',
                     bitb_client_type='MockWinClient',
                     bitb_test_file='stable',
                     bitb_test_cases=None, bitb_test_loop=None,
                     media_file=None, blast_log_level='debug',
                     vvc_log_level_agent='debug',
                     vvc_log_level_client='debug', vvc_mem_log_level=None,
                     capture_rlog_level=None, set_prefer_legacy_driver_registry=False):
        """Sets up the environment and executes BitB."""
        self.__set_bitb_reg_keys(bitb_protocol, blast_log_level,
                                 vvc_log_level_agent, vvc_log_level_client,
                                 vvc_mem_log_level, capture_rlog_level,
                                 set_prefer_legacy_driver_registry)
        self.__execute_bitb(bitb_protocol, bitb_encoder_config,
                            bitb_address_type, bitb_client_type,
                            bitb_test_file, bitb_test_cases, bitb_test_loop,
                            media_file)


    def cleanup_env(self):
        """Cleans up the environment before and after each BitB job."""
        os.system('net stop VMBlast')

        if 'VMBlastS.exe' in subprocess.check_output('tasklist', shell=True, encoding="utf8"):
            self.log.warning('Blast Service is still running.')
            os.system('Taskkill /IM VMBlastS.exe /F')
        else:
            self.log.info('Blast Service is not running.')

        if 'VMBlastW.exe' in subprocess.check_output('tasklist', shell=True, encoding="utf8"):
            self.log.warning('Blast Worker is still running.')
            os.system('Taskkill /IM VMBlastW.exe /F')
        else:
            self.log.info('Blast Worker is not running.')

        if 'chrome.exe' in subprocess.check_output('tasklist', shell=True, encoding="utf8"):
            self.log.info('Terminate Chrome process.')
            os.system("wmic process where name='chrome.exe' delete")
        else:
            self.log.info('Chrome is not running.')

        if 'MockWinClient.exe' in subprocess.check_output('tasklist', shell=True, encoding="utf8"):
            self.log.info('Terminate MockWinClient process.')
            os.system("wmic process where name='MockWinClient.exe' delete")
        else:
            self.log.info('MockWinClient is not running.')

        if 'bitb.exe' in subprocess.check_output('tasklist', shell=True, encoding="utf8"):
            self.log.warning('bitb.exe is still running.')
            os.system("wmic process where name='bitb.exe' delete")
        else:
            self.log.info('bitb.exe is not running.')

        self.log.info('Setting default resolution @ 1920 x 1080')
        subprocess.call('"C:\\Program Files\\VMware\\VMware Tools\\' +
                        'VMwareResolutionSet.exe" 0 1 , 0 0 1920 1080',
                        shell=True)

        self.__delete_old_logs()


    def get_test_cases(self):
        """Get BitB stable test case list from json and print it to stdout."""
        test_names = []
        with open('bitb/testCases/stableTestCases.json') as f:
            data = json.load(f)

        for test_case in data['TestCases']:
            test_names.append(test_case['name'])

        print(json.dumps(test_names))


    def __delete_old_logs(self):
        """Creates Blast folder and deletes old log files."""
        if not os.path.exists('C:\\ProgramData\\Omnissa\\Horizon\\logs'):
            self.log.info('Horizon Blast Folder does not exist. Creating...')
            os.makedirs('C:\\ProgramData\\Omnissa\\Horizon\\logs')

        self.log.info('Listing existing files for troubleshooting if needed...')
        os.system('dir "C:\\ProgramData\\Omnissa\\Horizon\\logs\\"')

        self.log.info('Deleting VDM/Blast/BitB log files...')
        self.__delete_files('C:\\ProgramData\\Omnissa\\Horizon\\logs\\*')
        self.__delete_files('C:\\Users\\<USER>\\AppData\\Local\\' +
                            'Temp\\omnissa-Administrator\\*.dmp')
        self.__delete_files('C:\\ProgramData\\Horizon\\Dumps\\*.dmp')
        self.__delete_files('C:\\ProgramData\\Horizon\\Dumps\\diagnostic\\*.dmp')


    def __set_bitb_reg_keys(self, bitb_protocol, blast_log_level,
                            vvc_log_level_agent, vvc_log_level_client,
                            vvc_mem_log_level, capture_rlog_level,
                            set_prefer_legacy_driver_registry):
        """Sets test specific registry keys for BitB."""
        self.log.info('Clearing existing reg keys...')
        self.__delete_reg_key(winreg.HKEY_LOCAL_MACHINE,
                              self.__REG_KEY_BASE_OMN_POLICIES_PATH)
        self.__delete_reg_key(winreg.HKEY_LOCAL_MACHINE,
                              os.path.join(self.__REG_KEY_BASE_OMN_PATH,
                                           'Horizon\\Input DevTap'))

        self.log.info('Adding reg keys...')

        self.log.info(' -- Disable SSL')
        self.__add_reg_key('Horizon\\Blast\\Config',
                           'DisableSsl', '1')

        self.log.info(' -- Enabling Recording Session')
        self.__add_reg_key('Horizon\\Blast\\Config',
                           'RecordingSessionEnabled', '1')

        self.log.info(' -- Enabling Blast Codec')
        self.__add_reg_key('Horizon\\Blast\\Config',
                           'EncoderBlastCodecEnabled', '1')

        self.log.info(' -- Enabling Encoder RLogs')
        self.__add_reg_key('Horizon\\Blast\\Config',
                           'EncoderRLogLevel', '9')

        self.log.info(' -- Enabling Blast debug logs')
        self.__add_reg_key('Horizon\\Blast\\Config',
                           'LogLevel', blast_log_level)

        udp_option = '0' if bitb_protocol == 'TCP' else '1'
        self.log.info(' -- UdpEnabled is %s' % udp_option)
        self.__add_reg_key('Horizon\\Blast\\Config',
                           'UdpEnabled', udp_option)

        if capture_rlog_level != None:
            self.log.info(' -- Setting CaptureRLogLevel to %s' %
                           capture_rlog_level)
            self.__add_reg_key('Horizon\\Blast\\Config',
                               'CaptureRLogLevel', capture_rlog_level)

        self.log.info(' -- Setting topology wait time')
        self.__add_reg_key('Horizon\\Blast\\Config',
                           'TopologyRetrySuccessWaitMs', '1000')

        self.log.info(' -- Setting VVC loglevel for agent to %s' %
                       vvc_log_level_agent)
        self.__add_reg_key('Horizon\\Blast\\Config',
                           'logLevel', vvc_log_level_agent)

        self.log.info(' -- Setting VVC loglevel for client to %s' %
                       vvc_log_level_client)
        self.__add_reg_key('Horizon\\Client\\vvc',
                           'logLevel', vvc_log_level_client)

        if vvc_mem_log_level != None:
            self.log.info(' -- Setting memLogLevel to %s' % vvc_mem_log_level)
            self.__add_reg_key('Horizon\\Blast\\vvc',
                               'memLogLevel', vvc_mem_log_level)

        self.log.info(' -- Enabling Connection Manager Logs')
        self.__add_reg_key('Horizon\\Blast\\Config',
                           'VNCConnectionMgrStatsEnabled', '1')

        if set_prefer_legacy_driver_registry:
           self.log.info(' -- Enabling Prefer Legacy Driver registry key')
           self.__add_reg_key('Horizon\\Blast\\Config',
                              'PixelProviderPreferLegacyDriver', '0')

        self.log.info(' -- Setting SecurePipes')
        self.__add_reg_key('Horizon\\Blast\\Config', 'SecurePipes', '0')

        self.log.info(' -- Setting BlankScreenEnabled')
        self.__add_reg_key('Horizon\\Blast\\Config', 'BlankScreenEnabled', '0')

        self.log.info(' -- Setting BitB WER DumpPath')
        self.__add_reg_key('LocalDumps\\bitb.exe', 'DumpFolder',
                           'C:\\ProgramData\\Omnissa\\Horizon\\logs\\',
                           'SOFTWARE\\Microsoft\\Windows\\Windows Error Reporting')

        self.log.info(' -- Setting BitB WER DumpType to Full')
        self.__add_reg_key('LocalDumps\\bitb.exe', 'DumpType', '2',
                           'SOFTWARE\\Microsoft\\Windows\\Windows Error Reporting')

        self.log.info(' -- Setting Input Devtap Mock Mode')
        self.__add_reg_key('Horizon\\Input DevTap', 'MockMode', 1,
                           self.__REG_KEY_BASE_OMN_PATH, type_string=False)


    def __execute_bitb(self, bitb_protocol, bitb_encoder_config,
                       bitb_address_type, bitb_client_type, bitb_test_file,
                       bitb_test_cases, bitb_test_loop, media_file):
        """Executes BitB testcases."""
        if 'VMBlastS.exe' in subprocess.check_output('tasklist', shell=True, encoding="utf8"):
            self.log.info('Blast Service is running.')
        else:
            self.log.warning('Blast Service is not running.')

        test_case_option = ''
        if bitb_test_cases != None:
            test_case_option += ' -testCases %s' % bitb_test_cases
        if bitb_test_loop != None:
            test_case_option += ' -loop %s' % bitb_test_loop

        media_file_option = ''
        if media_file != None:
            media_file_option = '-playMediaFile %s' % media_file

        proc_dump_src = "C:\\ProcDump"
        proc_dump_dest = os.path.join(os.getcwd(), "ProcDump")
        self.log.info('Copying ProcDump src:%s, dest:%s' % (proc_dump_src,
                                                            proc_dump_dest))
        shutil.copytree(proc_dump_src, proc_dump_dest)

        subprocess.call('bitb.exe -nonsecureBlastConnection ' +
                        '-disableConsoleLogging ' +
                        '-clientType %s ' % bitb_client_type +
                        '-addressType %s ' % bitb_address_type +
                        '-protocol %s ' % bitb_protocol +
                        '-testFiles ".\\testCases\\' +
                        '%sTestCases.json" ' % bitb_test_file +
                        '-encoderConfig %s ' % bitb_encoder_config +
                        '-procDump %s\\procdump.exe ' % proc_dump_dest +
                        '%s ' % test_case_option +
                        '%s ' % media_file_option, shell=True,
                        cwd=os.path.join(os.getcwd(), 'bitb'))


    def __delete_files(self, file_pattern):
        """Deletes all files matching file_pattern."""
        for file in glob(file_pattern):
            try:
                os.remove(file)
            except Exception as e:
                self.log.error(str(e))


    def __add_reg_key(self, path, name, value, base_path = None,
                      type_string = True):
        """Adds a registry key."""

        if base_path is None:
            base_path = self.__REG_KEY_BASE_OMN_POLICIES_PATH

        full_path = os.path.join(base_path, path)

        if type_string:
            reg_type = winreg.REG_SZ
        else:
            reg_type = winreg.REG_DWORD

        with winreg.CreateKeyEx(winreg.HKEY_LOCAL_MACHINE,
                                full_path, 0, winreg.KEY_SET_VALUE) as key:
            winreg.SetValueEx(key, name, 0, reg_type, value)

    def __delete_reg_key(self, root_key, current_key):
        """Deletes a registry key and all of its subkeys."""
        try:
            with winreg.OpenKey(root_key, current_key, 0,
                    winreg.KEY_ALL_ACCESS) as key:
                subkey_count, _, _ = winreg.QueryInfoKey(key)
                for i in range(subkey_count):
                    child = winreg.EnumKey(key, 0)
                    self.__delete_reg_key(key, child)
                try:
                    winreg.DeleteKey(key, '')
                except Exception:
                    self.log.warning('Failed to delete key %s' % key)
        except Exception:
            self.log.warning('Failed to delete key %s\\%s' % (root_key, current_key))

    __REG_KEY_BASE_OMN_PATH = 'SOFTWARE\\Omnissa'
    __REG_KEY_BASE_OMN_POLICIES_PATH = 'SOFTWARE\\Policies\\Omnissa'


def main(argv):
    """
    main() for bitb.py, will be executed if this file is called
    directly.
    """
    parser = argparse.ArgumentParser()
    parser.add_argument('--trigger_bitb', action='store_true',
                        help='set up environment and start BitB tests')
    parser.add_argument('--cleanup_env', action='store_true',
                        help='clean up environment before and after BitB')
    parser.add_argument('--get_test_cases', action='store_true',
                        help='print list of BitB test cases to stdout')
    parser.add_argument('--bitb_protocol', choices=['BEAT', 'TCP'],
                        default='BEAT')
    parser.add_argument('--bitb_encoder_config',
                        choices=['H264', 'ADAPTIVE', 'BLAST_CODEC',
                                 'H264_444', 'HEVC', 'HEVC_444'],
                        default='H264')
    parser.add_argument('--bitb_address_type', choices=['IPv4', 'IPv6'],
                        default='IPv4')
    parser.add_argument('--bitb_client_type',
                        choices=['MockWinClient', 'MockWebClient'],
                        default='MockWinClient')
    parser.add_argument('--bitb_test_file',
                        choices=['stable', 'unstable', 'longRunning'],
                        default='stable')
    parser.add_argument('--bitb_test_cases')
    parser.add_argument('--bitb_test_loop')
    parser.add_argument('--media_file')
    parser.add_argument('--blast_log_level', default='debug')
    parser.add_argument('--vvc_log_level_agent', default='debug')
    parser.add_argument('--vvc_log_level_client', default='debug')
    parser.add_argument('--vvc_mem_log_level')
    parser.add_argument('--capture_rlog_level')
    parser.add_argument('--prefer_legacy_driver_registry', action='store_true',
                        help='set config to prefer to avoid VIDD driver')
    args = parser.parse_args()
    logging.basicConfig(level=logging.INFO)

    my_bitb = BlastInTheBox()
    if args.cleanup_env:
        my_bitb.cleanup_env()
    if args.trigger_bitb:
        my_bitb.trigger_bitb(args.bitb_protocol, args.bitb_encoder_config,
                             args.bitb_address_type, args.bitb_client_type,
                             args.bitb_test_file, args.bitb_test_cases,
                             args.bitb_test_loop, args.media_file,
                             args.blast_log_level, args.vvc_log_level_agent,
                             args.vvc_log_level_client,
                             args.vvc_mem_log_level, args.capture_rlog_level,
                             args.prefer_legacy_driver_registry)
    if args.get_test_cases:
        my_bitb.get_test_cases()


if __name__ == "__main__":
    main(sys.argv[1:])
