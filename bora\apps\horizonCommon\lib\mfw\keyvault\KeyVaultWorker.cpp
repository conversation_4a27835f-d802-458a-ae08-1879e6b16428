/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/** \file KeyVaultWorker.cpp
 *
 *  The builtin local process KeyVault service
 */

#include <stdafx.h>

#ifdef WIN32

#   include "OrchestratorInt.h"
#   include "KeyVault.h"
#   include "KeyVaultWorker.h"
#   include "KeyCollection.h"
#   include "KeyVaultCng.h"
#   include "KeyVaultShare.h"
#   include "KeyVaultThreadGate.h"

extern KeyVaultThreadGate *g_gate;

extern void (*__mfwKeyVaultWorkerCreate)();
static void
_mfwKeyVaultWorkerCreate()
{
   g_gate = new KeyVaultThreadGate(TEXT("KeyVault"));
   TRY_MEM_RETV(g_gate, );
   g_pMessageFrameWorkInt->AddWorker(TEXT("KeyVault"), TEXT("The Builtin Key Vault API Queue"),
                                     KeyVaultWorkerCreate, 0);
}
class keyvaultinit {
public:
   keyvaultinit() { __mfwKeyVaultWorkerCreate = _mfwKeyVaultWorkerCreate; }
};
static keyvaultinit _keyvaultinit;


#   define NEED_KEYNAME()                                                                          \
      if (!keyname.setName(params.get(KEYPAR_NAME, TEXT("")), false, false, &response)) {          \
         goto done;                                                                                \
      }                                                                                            \
      keyname.gen = 0;

#   define NEED_KEYNAME_ALLOW_NAMESPACE()                                                          \
      if (!keyname.setName(params.get(KEYPAR_NAME, TEXT("")), true, false, &response)) {           \
         goto done;                                                                                \
      }                                                                                            \
      keyname.gen = 0;

#   define NEED_KEYNAME_AND_GEN()                                                                  \
      keyname.gen = params.getInt(KEYPAR_GEN, 0);                                                  \
      if (!keyname.setName(params.get(KEYPAR_NAME, TEXT("")), false, false, &response)) {          \
         goto done;                                                                                \
      }

#   define NEED_KEYNAME_AND_GEN_ALLOW_NAMESPACE()                                                  \
      keyname.gen = params.getInt(KEYPAR_GEN, 0);                                                  \
      if (!keyname.setName(params.get(KEYPAR_NAME, TEXT("")), true, false, &response)) {           \
         goto done;                                                                                \
      }

#   define NEED_KEYNAME_AND_GEN_OR_SUPPLIED()                                                      \
      keyname.gen = params.getInt(KEYPAR_GEN, 0);                                                  \
      if (!keyname.setName(params.get(KEYPAR_NAME, TEXT("")), true, isSuppliedKey(params),         \
                           &response)) {                                                           \
         goto done;                                                                                \
      }

#   define NEED_KEYNAME_ENCIPHER()                                                                 \
      keynameEncipher.gen = params.getInt(KEYPAR_ENCIPHERGEN, 0);                                  \
      if (!keynameEncipher.setName(params.get(KEYPAR_ENCIPHERNAME, TEXT("")), false, false,        \
                                   &response)) {                                                   \
         goto done;                                                                                \
      }

#   define NEED_KEYNAME_ENCIPHER_ALLOW_NAMESPACE()                                                 \
      keynameEncipher.gen = params.getInt(KEYPAR_ENCIPHERGEN, 0);                                  \
      if (!keynameEncipher.setName(params.get(KEYPAR_ENCIPHERNAME, TEXT("")), true, false,         \
                                   &response)) {                                                   \
         goto done;                                                                                \
      }

#   define NEED_SIGNDATA_PARAMS()                                                                  \
      if (!signDataParams.Init(params, true)) {                                                    \
         response.setError(TEXT("Failed to initialize key hash params"));                          \
         goto done;                                                                                \
      }

#   define CHECK_SIGNDATA_PARAMS()                                                                 \
      if (!signDataParams.Init(params)) {                                                          \
         response.setError(TEXT("Signature data provided but ")                                    \
                              TEXT("transform bag or algorithm name is missing"));                 \
         goto done;                                                                                \
      }

#   define NEED_SIGNATURE()                                                                        \
      if (!signDataParams.Init(params, true, true)) {                                              \
         response.setError(TEXT("Signature or signature data is missing"));                        \
         goto done;                                                                                \
      }

#   define NEED_SIGNATURE_UNLESS_TRUSTED()                                                         \
      if (!signDataParams.Init(params, !trusted, !trusted)) {                                      \
         response.setError(TEXT("Signature or signature data is missing"));                        \
         goto done;                                                                                \
      }

extern KeyVaultShare *g_Shared;

extern corecritsec g_gateSync;
extern KeyVault *g_KeyVault;
extern bool g_extTraceLog;

static bool g_firstTime = true;

bool g_isRekeying = false;
HANDLE g_rekeyWaitEvent = NULL;


class KeyVaultWorker : public WorkItem {
private:
   bool inline isSuppliedKey(PropertyBag &params)
   {
      if (params.isBag(KEYPAR_TRANSFORM)) {
         PropertyBag *bagptr = params.getBagPtr(KEYPAR_TRANSFORM);
         return bagptr->isBinary(KEYPAR_TRANSFORM_XF_KEY);
      }
      return false;
   }

public:
   /*
    *--------------------------------------------------------------------------
    *
    * MessageHandler_doit
    *
    * Results:
    *   true if ok
    *
    *--------------------------------------------------------------------------
    */

   bool MessageHandler_doit(tstr &queueHint, PropertyBag &params, PropertyBag &response)
   {
      /*
       * The certificate validation is not related to the rest of the KeyVault
       * handling so we check that first
       */

      if (_tcsicmp(queueHint, TEXT("ValidateCertificateChain")) == 0) {
         DWORD start = GetTickCount();
         bool ok = ValidateCertificateChain(params, response, this);
         SYSMSG_FUNC(Debug, TEXT("ValidateCertificateChain ok=%u, msecs=%u"), ok,
                     GetTickCount() - start);
         return ok;
      } else if (_tcsicmp(queueHint, TEXT("GetCertificateChain")) == 0) {
         return GetCertificateChain(params, response, this);
      } else if (_tcsicmp(queueHint, TEXT("GetCertificateHost")) == 0) {
         return GetCertificateHost(params, response, this);
      } else if (_tcsicmp(queueHint, TEXT("GetCertificateKey")) == 0) {
         return GetCertificateKey(params, response, this);
      } else if (_tcsicmp(queueHint, TEXT("AddSelfSignedCertificate")) == 0) {
         return AddSelfSignedCertificate(params, response, this);
      } else if (_tcsicmp(queueHint, TEXT("getEndEntityCertificates")) == 0) {
         return getEndEntityCertificates(params, response, this);
      } else if (_tcsicmp(queueHint, TEXT("removeCertificate")) == 0) {
         return removeCertificate(params, response, this);
      } else if (_tcsicmp(queueHint, TEXT("setFriendlyName")) == 0) {
         return setFriendlyName(params, response, this);
      } else if (_tcsicmp(queueHint, TEXT("purgeInvalidCertificates")) == 0) {
         return purgeInvalidCertificates(params, response, this);
      } else if (_tcsicmp(queueHint, TEXT("importEncodedCertificates")) == 0) {
         return importEncodedCertificates(params, response, this);
      } else if (_tcsicmp(queueHint, TEXT("generatePKCS10")) == 0) {
         return GeneratePKCS10(params, response, this);
      }

      /*
       * We have a KeyVault command
       */

      KeyName keyname;
      KeyName keynameEncipher;

      /*
       * Check KeyVault requests that does not need the KeyVault class
       * (we don't want to initiate the KeyVault class if not needed)
       */
      {
         KeyVaultCng cng;
         if (_tcsicmp(queueHint, TEXT("getRandom")) == 0) {
            return cng.getRandom(params, response, this);
         } else if (_tcsicmp(queueHint, TEXT("setRandomSeed")) == 0) {
            return cng.setRandomSeed(params, response, this);
         } else if (_tcsicmp(queueHint, TEXT("getCRC32")) == 0) {
            return cng.getCRC32(response, this);
         } else if ((_tcsicmp(queueHint, TEXT("encipherWithDerivedKey")) == 0) ||
                    (_tcsicmp(queueHint, TEXT("decipherWithDerivedKey")) == 0)) {
            tstr name = params.get(KEYPAR_NAME, TEXT(""));
            if ((_tcsicmp(name, SECURITYMGRSSO_KEYNAME) == 0) ||
                (_tcsicmp(name, SECURITYMGRSESS_KEYNAME) == 0)) {
               keyname.gen = params.getInt(KEYPAR_GEN, 0);
               if (!keyname.setName(params.get(KEYPAR_NAME, TEXT("")), false, false, &response)) {
                  return false;
               }
               if (_tcsicmp(queueHint, TEXT("encipherWithDerivedKey")) == 0) {
                  return cng.encipherWithDerivedKey(keyname, params, response, this, NULL);
               }
               return cng.decipherWithDerivedKey(keyname, params, response, this, NULL);
            }
         } else if (_tcsicmp(queueHint, TEXT("exportCertificates")) == 0) {
            tstr name = params.get(KEYPAR_NAME, TEXT(""));
            if ((_tcsicmp(name, SECURITYMGRSSO_KEYNAME) == 0) ||
                (_tcsicmp(name, SECURITYMGRSESS_KEYNAME) == 0)) {
               keyname.gen = params.getInt(KEYPAR_GEN, 0);
               if (!keyname.setName(params.get(KEYPAR_NAME, TEXT("")), false, false, &response)) {
                  return false;
               }
               return exportCertificates(keyname, params, response, this);
            }
         }
      }

      // isRekeying is a non-blocking call not using the thread gate
      if (_tcsicmp(queueHint, TEXT("isRekeying")) == 0) {
         return isRekeying(params, response);
      }

      /*
       * Enter the KeyVault service thread gate.
       * We have now made this service multi-threaded for performance using CNG.
       * We use a thread gate here to still support commands like rekey which
       * need to run single-threaded. The thread gate allows us to frame a
       * command that need to be single-threaded by gate lock/unlock calls.
       * The gate also calls initiate and cleanup when needed.
       */

      KV_ENTER_THREAD_GATE(g_gate, response);

      bool fOk = false;

      /*
       * KeyCollection requests have their own KeyVault instance
       */

      if (_tcsnicmp(queueHint, TEXT("KeyColl_"), 8) == 0) {
         tstr tmp = queueHint.p() + 8;
         return KeyCollections::HandleMsg(tmp, params, response, &g_gateSync);
      }

      /*
       * Check which operation to perform
       */

      if (_tcsicmp(queueHint, TEXT("addContext")) == 0) {
         NEED_KEYNAME();
         fOk = g_KeyVault->addContext(params.getInt(KEYPAR_LENGTH, 0), keyname, response);
      } else

         if ((_tcsicmp(queueHint, TEXT("addKey")) == 0) ||
             (_tcsicmp(queueHint, TEXT("addKeyPair")) == 0)) {
         NEED_KEYNAME();
         bool persist = params.getBool(KEYPAR_PERSIST, false);
         if (persist) {
            NEED_KEYNAME_ENCIPHER();
         }
         fOk = g_KeyVault->addKey(_tcsicmp(queueHint, TEXT("addKeyPair")) == 0,
                                  params.getInt(KEYPAR_LENGTH, 0), keyname, keynameEncipher,
                                  persist, response);
      } else

         if (_tcsicmp(queueHint, TEXT("addMaster")) == 0) {
         NEED_KEYNAME();
         fOk = g_KeyVault->addMaster(keyname, response);
      } else

         if (_tcsicmp(queueHint, TEXT("clear")) == 0) {
         g_KeyVault->clear(response);
         fOk = true;
      } else

         if (_tcsicmp(queueHint, TEXT("decipher")) == 0) {
         NEED_KEYNAME_AND_GEN_OR_SUPPLIED();
         fOk = g_KeyVault->decipher(keyname, params, response, this);
      } else

         if (_tcsicmp(queueHint, TEXT("deriveKey")) == 0) {
         NEED_KEYNAME();
         KeyName master, context;
         if ((!master.setName(params.get(KEYPAR_DERIVEFROMKEY, TEXT("")), true, false,
                              &response)) ||
             (!context.setName(params.get(KEYPAR_CONTEXT, TEXT("")), true, false, &response))) {
            goto done;
         }
         fOk = g_KeyVault->deriveKey(keyname, master, context, response);
      } else

         if (_tcsicmp(queueHint, TEXT("encipher")) == 0) {
         NEED_KEYNAME_AND_GEN_OR_SUPPLIED();
         fOk = g_KeyVault->encipher(keyname, params, response, this);
      } else

         if (_tcsicmp(queueHint, TEXT("exist")) == 0) {
         NEED_KEYNAME_AND_GEN_ALLOW_NAMESPACE();
         if (keyname.isBuiltinSsoKey() || keyname.isBuiltinSessKey()) {
            fOk = keyname.nameSpace == namespace_none;
         } else if (!keyname.gen) {
            response.setError(TEXT("The generation does not exist"));
         } else {
            fOk = g_KeyVault->latest(keyname, response);
            response.remove(RESP_GEN);
         }
      } else

         if (_tcsicmp(queueHint, TEXT("export")) == 0) {
         NEED_KEYNAME();
         NEED_KEYNAME_ENCIPHER_ALLOW_NAMESPACE();
         PropertyBag transform = params.getBag(KEYPAR_TRANSFORM);
         bool exportAllOperationalKeyGens =
            params.getBool(TEXT("exportAllOperationalKeyGens"), false);
         fOk =
            g_KeyVault->keyExport(keyname, params.get(TEXT("type"), TYPE_MASTER), keynameEncipher,
                                  exportAllOperationalKeyGens, transform, response, this);
      } else

         if (_tcsicmp(queueHint, TEXT("exportCertificates")) == 0) {
         NEED_KEYNAME_ALLOW_NAMESPACE();
         fOk = exportCertificates(keyname, params, response, this);
      } else

         if (_tcsicmp(queueHint, TEXT("finish")) == 0) {
         fOk = true;
      } else

         if (_tcsicmp(queueHint, TEXT("getData")) == 0) {
         NEED_KEYNAME_AND_GEN();
         bool decipher = params.getBool(KEYPAR_DECIPHER, false);
         fOk = g_KeyVault->getData(keyname, decipher, response, this);
      } else

         if (_tcsicmp(queueHint, TEXT("getKey")) == 0) {
         NEED_KEYNAME_AND_GEN();
         NEED_KEYNAME_ENCIPHER_ALLOW_NAMESPACE();
         PropertyBag transform = params.getBag(KEYPAR_TRANSFORM);
         fOk = g_KeyVault->keyExport(keyname, TYPE_OPERATIONAL, keynameEncipher, false, transform,
                                     response, this);
      } else

         if (_tcsicmp(queueHint, TEXT("getKeyRaw")) == 0) {
         NEED_KEYNAME_AND_GEN();
         fOk = g_KeyVault->getKeyRaw(keyname, response, this);
      } else

         if (_tcsicmp(queueHint, TEXT("getPublic")) == 0) {
         SignDataInfo signDataParams;
         NEED_KEYNAME_AND_GEN();
         CHECK_SIGNDATA_PARAMS();
         fOk = g_KeyVault->getPublic(keyname, signDataParams, response, this);
      } else

         if (_tcsicmp(queueHint, TEXT("import")) == 0) {
         fOk = g_KeyVault->keyImport(params.get(TEXT("type"), TYPE_MASTER), params, response, this);
      } else

         if (_tcsicmp(queueHint, TEXT("importCertificates")) == 0) {
         fOk = importCertificates(params, response, this);
      } else

         if (_tcsicmp(queueHint, TEXT("info")) == 0) {
         NEED_KEYNAME_AND_GEN_ALLOW_NAMESPACE();
         fOk = g_KeyVault->info(keyname, response);
      } else

         if (_tcsicmp(queueHint, TEXT("init")) == 0) {
         fOk = true;
         if (params.getBool(TEXT("rekey"), false)) {
            g_gate->lock();
            fOk = g_KeyVault->rekey();
            g_gate->unlock();
         }
      } else

         if (_tcsicmp(queueHint, TEXT("latest")) == 0) {
         NEED_KEYNAME_ALLOW_NAMESPACE();
         fOk = g_KeyVault->latest(keyname, response);
      } else

         if (_tcsicmp(queueHint, TEXT("list")) == 0) {
         if (keyname.setName(params.get(KEYPAR_NAME, TEXT("")), true, true, &response)) {
            fOk = g_KeyVault->list(keyname, params, response);
         }
      } else

         if (_tcsicmp(queueHint, TEXT("putData")) == 0) {
         NEED_KEYNAME();
         NEED_KEYNAME_ENCIPHER();
         PropertyBag transform = params.getBag(KEYPAR_TRANSFORM);
         fOk = g_KeyVault->putData(keyname, keynameEncipher, transform, response, this);
      } else

         if (_tcsicmp(queueHint, TEXT("putKey")) == 0) {
         fOk = g_KeyVault->keyImport(TYPE_OPERATIONAL, params, response, this);
      } else

         if (_tcsicmp(queueHint, TEXT("putKeyRaw")) == 0) {
         NEED_KEYNAME();
         NEED_KEYNAME_ENCIPHER();
         bool persist = params.getBool(KEYPAR_PERSIST, false);
         fOk = g_KeyVault->putKeyRaw(keyname, keynameEncipher, persist, response, this);
      } else

         if (_tcsicmp(queueHint, TEXT("putPublic")) == 0) {
         NEED_KEYNAME();
         bool trusted = params.getBool(KEYPAR_TRUSTED, false);
         SignDataInfo signDataParams;
         NEED_SIGNATURE_UNLESS_TRUSTED();
         fOk = g_KeyVault->putPublic(keyname, trusted, signDataParams, response, this);
      } else

         if (_tcsicmp(queueHint, TEXT("rekey")) == 0) {
         g_gate->lock();
         fOk = g_KeyVault->rekey();
         g_gate->unlock();
      } else

         if (_tcsicmp(queueHint, TEXT("remove")) == 0) {
         NEED_KEYNAME();
         // we cannot remove keys that are shared by or to us
         if ((keyname.nameSpace != namespace_none) || (g_Shared->IsKeySharedByMe(keyname.name)) ||
             (g_Shared->IsKeySharedByMeCAPI(keyname.name))) {
            response.setError(TEXT("Remove of shared key is not allowed"));
         } else {
            DWORD keysToRetain = params.getInt(TEXT("keysToRetain"), 0);
            bool checkDependents = params.getBool(KEYPAR_CHECKDEPENDENTS, false);
            fOk = g_KeyVault->remove(keyname.name, response, checkDependents, keysToRetain);
         }
      } else

         if (_tcsicmp(queueHint, TEXT("signHash")) == 0) {
         SignDataInfo signDataParams;
         NEED_KEYNAME();
         NEED_SIGNDATA_PARAMS();
         fOk = g_KeyVault->signHash(keyname, signDataParams, response);
      } else

         if (_tcsicmp(queueHint, TEXT("signPKCS10")) == 0) {
         SignDataInfo signDataParams;
         NEED_KEYNAME();
         NEED_SIGNDATA_PARAMS();
         fOk = g_KeyVault->signPKCS10(keyname, signDataParams, response);
      } else

         if (_tcsicmp(queueHint, TEXT("generateSignature")) == 0) {
         SignDataInfo signDataParams;
         NEED_KEYNAME();
         NEED_SIGNDATA_PARAMS();
         fOk = g_KeyVault->generateSignature(keyname, signDataParams, response);
      } else

         if (_tcsicmp(queueHint, TEXT("verifySignature")) == 0) {
         SignDataInfo signDataParams;
         NEED_KEYNAME();
         NEED_SIGNDATA_PARAMS();
         fOk = g_KeyVault->verifySignature(keyname, signDataParams, response);
      } else

         if (_tcsicmp(queueHint, TEXT("encipherWithDerivedKey")) == 0) {
         NEED_KEYNAME_AND_GEN_ALLOW_NAMESPACE();
         fOk =
            g_KeyVault->m_cng.encipherWithDerivedKey(keyname, params, response, this, g_KeyVault);
      } else

         if (_tcsicmp(queueHint, TEXT("decipherWithDerivedKey")) == 0) {
         NEED_KEYNAME_AND_GEN_ALLOW_NAMESPACE();
         fOk =
            g_KeyVault->m_cng.decipherWithDerivedKey(keyname, params, response, this, g_KeyVault);
      } else

         if (_tcsicmp(queueHint, TEXT("share")) == 0) {
         NEED_KEYNAME();
         // the "ShareToExeNames" is a comma separated list of exe names
         tstr shareTo = params.get(TEXT("ShareToExeNames"), TEXT(""));
         fOk = g_Shared->Share(keyname, shareTo, response);
      } else

         if (_tcsicmp(queueHint, TEXT("isShared")) == 0) {
         NEED_KEYNAME();
         fOk =
            g_Shared->IsKeySharedByMe(keyname.name) || g_Shared->IsKeySharedByMeCAPI(keyname.name);
      } else

         if (_tcsicmp(queueHint, TEXT("unshare")) == 0) {
         NEED_KEYNAME_AND_GEN_ALLOW_NAMESPACE();
         fOk = g_Shared->Unshare(keyname, response);
      } else

         if (_tcsicmp(queueHint, TEXT("id")) == 0) {
         NEED_KEYNAME_AND_GEN_ALLOW_NAMESPACE();
         fOk = g_KeyVault->id(keyname, params, response, g_KeyVault);

      } else

         if (_tcsicmp(queueHint, TEXT("DeleteRootKey")) == 0) {
         if (tstr::readRegistry(utils::regPath() + TEXT("\\DeveloperMachine"), TEXT("false"))
                .toBool()) {
            g_KeyVault->deleteRootKey(params, response);
            fOk = true;
         }
      } else {
         response.setError(ERROR_CALL_NOT_IMPLEMENTED);
      }

   done:
      return fOk;
   }


   /*
    *--------------------------------------------------------------------------
    *
    * MessageHandler
    *
    *   The Key Vault WorkerItem MessageHandler implementation
    *
    * Results:
    *   true if ok
    *
    *--------------------------------------------------------------------------
    */

   bool MessageHandler(tstr &queueHint, PropertyBag &params, PropertyBag &response)
   {
      /*
       * KeyVault is local access only (local = current process)
       * To be able to test with wsnm_test while developing
       * I allow remote access for the debug version.
       */

#   ifndef _DEBUG
      if (!msgIsLocal()) {
         response.setError(TEXT("KeyVault remote access is not allowed"));
         return false;
      }
#   endif

      if (g_firstTime) {
         coresync sync(g_gateSync);
         if (g_firstTime) {
            g_firstTime = false;
            g_extTraceLog =
               tstr::readRegistry(utils::regPath() + TEXT("\\KeyVaultExtendedTraceLog"), TEXT(""))
                  .toBool();
         }
      }
#   ifdef MFW_DEVEL
      if (g_extTraceLog) {
         MsgBinary bin;
         GetBinaryRequestData(bin, false);
         SYSMSG_FUNC(Trace, TEXT("KEYVAULT: message IN  '%s', sbin=%u, params:\r\n%s"), queueHint,
                     bin.sBinary, params.flattenForDisplay());
      }
#   endif

      DWORD start = GetTickCount();
      bool fOk = MessageHandler_doit(queueHint, params, response);
      SYSMSG_FUNC(Debug, TEXT("KeyVault service got operation=%s, ok=%u, msecs=%u"), queueHint, fOk,
                  GetTickCount() - start);
      // ensure KeyVault failures have an error code
      if (!fOk && !response.getError()) {
         response.setError(KvErrFailed);
      }

#   ifdef MFW_DEVEL
      if (g_extTraceLog) {
         SYSMSG_FUNC(Trace, TEXT("KEYVAULT: message OUT  '%s'%s, response:\r\n%s"), queueHint,
                     fOk ? TEXT("") : TEXT(" FAILED"), response.flattenForDisplay());
      }
#   endif
      return fOk;
   }


   /*
    *--------------------------------------------------------------------------
    *
    * isRekeying
    *
    *   Check if rekey is in progress with an optional waitToStopSeconds
    *   for it to stop if started or pending
    *
    * Results:
    *   true if rekey is in progress
    *
    *--------------------------------------------------------------------------
    */
   bool isRekeying(PropertyBag &params, PropertyBag &response)
   {
      response.setBool(TEXT("rekeying"), false);
      if (g_isRekeying) {
         DWORD waitSeconds = params.getInt(TEXT("waitToStopSeconds"), false);
         if (waitSeconds) {
            if (!g_rekeyWaitEvent) {
               g_rekeyWaitEvent = CreateEvent(0, TRUE, FALSE, 0);
               if (!g_rekeyWaitEvent) {
                  SYSMSG_FUNC(Error, TEXT("KeyVault could not create event for isRekeying"));
                  return false;
               }
            }
            DWORD st = WaitForSingleObject(g_rekeyWaitEvent, waitSeconds * 1000);
            if (st != WAIT_TIMEOUT) {
               return true;
            }
         }
         response.setBool(TEXT("rekeying"), true);
      }
      return true;
   }
};


/*
 *--------------------------------------------------------------------------
 *
 * KeyVaultWorkerCleanup
 *
 *   The static Key Vault WorkerItem cleanup
 *
 * Results:
 *   none
 *
 *--------------------------------------------------------------------------
 */
void
KeyVaultWorkerCleanup(bool)
{
   if (g_gate) {
      g_gate->stop(false);
   }

   CertificateCleanup();
   KeyCollections::Shutdown();

   if (g_gate) {
      g_gate->Release();
      g_gate = NULL;
   }
}


/*
 *--------------------------------------------------------------------------
 *
 * KeyVaultWorkerCreate
 *
 *   The static Key Vault WorkerItem creator
 *
 * Results:
 *   a KeyVaultWorker instance
 *
 *--------------------------------------------------------------------------
 */

WorkItem *
KeyVaultWorkerCreate()
{
   return new KeyVaultWorker;
}


/*--------------------------------------------------------------------------
 *
 * getKeyVaultInst
 *
 *   Get the KeyVault main instance pointer
 *
 * Results:
 *   the KeyVault main instance pointer
 *
 *--------------------------------------------------------------------------
 */

KeyVault *
getKeyVaultInst()
{
   return g_KeyVault;
}


#endif // WIN32
