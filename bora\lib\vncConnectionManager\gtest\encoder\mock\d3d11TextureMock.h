/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

#include <d3d11.h>
#include <vector>

class MockD3D11Device;

class MockD3D11Texture2D : public ID3D11Texture2D {
public:
public:
   MockD3D11Texture2D(MockD3D11Device *device, const D3D11_TEXTURE2D_DESC *pDesc = nullptr,
                      const D3D11_SUBRESOURCE_DATA *pInitialData = nullptr);
   ~MockD3D11Texture2D();

   // IUnknown
   ULONG STDMETHODCALLTYPE AddRef() override;
   ULONG STDMETHODCALLTYPE Release() override;
   HRESULT STDMETHODCALLTYPE QueryInterface(REFIID riid, void **ppvObject) override;

   // ID3D11DeviceChild
   void STDMETHODCALLTYPE GetDevice(ID3D11Device **ppDevice) override;
   HRESULT STDMETHODCALLTYPE GetPrivateData(REFGUID, UINT *, void *) override { return E_NOTIMPL; }
   HRESULT STDMETHODCALLTYPE SetPrivateData(REFGUID, UINT, const void *) override
   {
      return E_NOTIMPL;
   }
   HRESULT STDMETHODCALLTYPE SetPrivateDataInterface(REFGUID, const IUnknown *) override
   {
      return E_NOTIMPL;
   }

   // ID3D11Resource
   void STDMETHODCALLTYPE GetType(D3D11_RESOURCE_DIMENSION *pResourceDimension) override;
   void STDMETHODCALLTYPE SetEvictionPriority(UINT EvictionPriority) override;
   UINT STDMETHODCALLTYPE GetEvictionPriority() override;

   // ID3D11Texture2D
   void STDMETHODCALLTYPE GetDesc(D3D11_TEXTURE2D_DESC *pDesc) override;

   // Custom setter
   void SetDesc(const D3D11_TEXTURE2D_DESC &desc);

private:
   ULONG m_refCount = 1;
   MockD3D11Device *m_device = nullptr;
   D3D11_TEXTURE2D_DESC m_desc = {};
   std::vector<D3D11_SUBRESOURCE_DATA> m_initialData;
};
