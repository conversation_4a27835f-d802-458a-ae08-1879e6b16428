/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */


/*
 * cert_sso.cpp
 *
 *     cert sso related gtests
 *
 */


#include "stdafx.h"
#include <windows.h>
#include <Shlwapi.h>
#include <iostream>
#include <MessageFrameWork.h>
#include "cert_sso.h"
#include "inih/INIReader.h"


using namespace CORE;


// globals
TCHAR g_progName[MAX_PATH];
INIReader g_cfgReader;


/*
 *-----------------------------------------------------------------------------
 *
 * PrintConfigOpts --
 *
 *    Print input params for testing
 *    Typically used for debugging purposes
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

void
PrintConfigOpts()
{
   std::vector<std::string> sections = g_cfgReader.GetSections();
   std::vector<std::string> attribIds;
   std::string sectionExtraDelim = "";
   std::string paramExtraDelim;
   int iterCount;

   printf_s("================ Config Options =============================\n");
   for (size_t i = 0; i < sections.size(); ++i) {
      if (i == 1) {
         sectionExtraDelim = "\n";
      }

      if (g_cfgReader.SetSectionToRead(sections[i])) {
         iterCount = 0;

         printf_s("%sSectionName: %s\n", sectionExtraDelim.c_str(), sections[i].c_str());
         printf_s("-------------------------------------------------------------\n");

         attribIds = g_cfgReader.GetAttribIds();

         do {
            if (iterCount++ > 0) {
               printf_s("\n");
            }

            size_t attribCount = attribIds.size();
            for (size_t i = 0; i < attribCount; ++i) {
               std::string val = g_cfgReader.ReadValueFromSection(attribIds[i], "Unknown");
               printf_s("%s: %s\n", attribIds[i].c_str(), val.c_str());
            }
         } while (g_cfgReader.ReadNextSection());
      }
   }
   printf_s("=============================================================\n");
}


/*
 *-----------------------------------------------------------------------------
 *
 * ReadConfigOpts --
 *
 *    Reads the test config params
 *
 * Results:
 *    true  - on success
 *    false - on failure
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

bool
ReadConfigOpts(int argc, TCHAR **argv)
{
   bool retVal = false;

   if (argc > 1) {
      wprintf_s(L"Argc=%d . Argv[1] = %s\n", argc, argv[1]);

      if (PathFileExists(argv[1])) {
         wprintf_s(L"file exists\n");
         std::wstring wCfgFile = argv[1];
         std::string cfgFile(wCfgFile.begin(), wCfgFile.end());

         retVal = g_cfgReader.Init(cfgFile);
         if (!retVal) {
            wprintf_s(L"Can't obtain file '%s'\n", argv[1]);
         }
      } else {
         wprintf_s(L"file doesnt exist\n");
      }
   }

   return retVal;
}


/*
 *-----------------------------------------------------------------------------
 *
 * CertSSOTest::SetUp --
 *
 *    Setup actions performed before running tests
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

void
CertSSOTest::SetUp()
{
   // enable test mode for certlogon plugin
   wstr regValueTrue(L"true");
   regValueTrue.writeRegistry(utils::regPath() + CSSO_TEST_REGPATH + CSSO_TEST_REG_TESTKEY);
}


/*
 *-----------------------------------------------------------------------------
 *
 * CertSSOTest::TearDown --
 *
 *    Cleanup actions performed after running tests
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */
void
CertSSOTest::TearDown()
{
   // disable test mode for certlogon plugin
   wstr regValueFalse(L"false");
   regValueFalse.writeRegistry(utils::regPath() + CSSO_TEST_REGPATH + CSSO_TEST_REG_TESTKEY);
}


/*
 *-----------------------------------------------------------------------------
 *
 * CertSSO_LsaLogon::RunTest --
 *
 *    Pass params and initiate actual test
 *
 * Results:
 *    0     - on success
 *    non 0 - on failure
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

int
CertSSO_LsaLogon::RunTest(CertSSOParams &params)
{
   TCHAR *progParam = L"-t";
   TCHAR *certFName, *username;
   std::wstring wCertFile, wUsername;

   if (sizeof(TCHAR) == 1) {
      certFName = (TCHAR *)params.certFile.c_str();
      username = (TCHAR *)params.username.c_str();
   } else {
      std::wstring wsTmpCert(params.certFile.begin(), params.certFile.end());
      wCertFile = wsTmpCert;
      certFName = (TCHAR *)wCertFile.c_str();

      std::wstring wsTmpUser(params.username.begin(), params.username.end());
      wUsername = wsTmpUser;
      username = (TCHAR *)wUsername.c_str();
   }

   TCHAR *paramVals[] = {g_progName, progParam, certFName, username};
   return mainFunc(4, paramVals);
}


/*
 *-----------------------------------------------------------------------------
 *
 * TEST_F --
 *
 *    gtest feature of type CertSSOTest
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

TEST_F(CertSSOTest, LsaLogonSuccess)
{
   char *testName = "LsaLogonSuccess";

   CertSSO_LsaLogon lsaLogonTest;
   const ::testing::TestInfo *const test_info =
      ::testing::UnitTest::GetInstance()->current_test_info();

   if (g_cfgReader.SetSectionToRead(testName)) {
      do {
         CertSSOParams testParams;
         std::string curTestId;
         int testResult;

         testParams.username = g_cfgReader.ReadValueFromSection(CSSO_USERNAME, "Unknown");
         testParams.certFile = g_cfgReader.ReadValueFromSection(CSSO_CERT_FILE, "Unknown");
         testParams.privKeyFile = g_cfgReader.ReadValueFromSection(CSSO_PRIVKEY_FILE, "Unknown");
         testParams.desc = g_cfgReader.ReadValueFromSection(CSSO_DESCRIPTION, "Unknown");
         testParams.expectedRes =
            g_cfgReader.ReadValueFromSectionNoCase(CSSO_EXPECTED_RES, "Unknown");
         testParams.loopCount =
            atoi(g_cfgReader.ReadValueFromSectionNoCase(CSSO_LOOPCOUNT, "1").c_str());

         // make key file available for certlogon plugin
         wstr privKeyFile(tstr::to_wstr(testParams.privKeyFile.c_str()));
         privKeyFile.writeRegistry(utils::regPath() + CSSO_TEST_REGPATH + CSSO_TEST_REG_PRIVKEY);

         curTestId = std::string("[") + test_info->test_case_name() + std::string(".") +
                     test_info->name() + std::string("][") + testParams.desc + std::string("]");
         RecordProperty("CurrentTestId", curTestId.c_str());

         printf_s("-------------------------------------------------------------\n");
         printf_s("Test: %s\n   Params:\n", curTestId.c_str());
         printf_s("      Username: %s\n", testParams.username.c_str());
         printf_s("      CertificateFile: %s\n", testParams.certFile.c_str());
         printf_s("      PrivateKeyFile: %s\n", testParams.privKeyFile.c_str());
         printf_s("      Description: %s\n", testParams.desc.c_str());
         printf_s("      Loop: %d\n", testParams.loopCount);
         printf_s("      ExpectedResult: %s\n", testParams.expectedRes.c_str());

         for (int i = 1; i <= testParams.loopCount; ++i) {
            if (testParams.expectedRes == CSSO_TEST_PASS) {
               EXPECT_EQ(0, (testResult = lsaLogonTest.RunTest(testParams)));
               printf_s("   TestResult [%d/%d]: %s.  Value returned: %d\n", i, testParams.loopCount,
                        (0 == testResult) ? "Success" : "FAILURE!", testResult);
            } else if (testParams.expectedRes == CSSO_TEST_FAIL) {
               EXPECT_NE(0, (testResult = lsaLogonTest.RunTest(testParams)));
               printf_s("   TestResult [%d/%d]: %s.  Value returned: %d\n", i, testParams.loopCount,
                        (0 != testResult) ? "Success" : "FAILURE!", testResult);
            }
         }

         // remove key file name from test location
         privKeyFile = L"";
         privKeyFile.writeRegistry(utils::regPath() + CSSO_TEST_REGPATH + CSSO_TEST_REG_PRIVKEY);

      } while (g_cfgReader.ReadNextSection());
      printf_s("-------------------------------------------------------------\n");
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * _tmain --
 *
 *    Main function
 *
 * Results:
 *    0 - always
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

int
_tmain(int argc, TCHAR **argv)
{
   _tcscpy_s(g_progName, MAX_PATH, PathFindFileName(argv[0]));
   PathRemoveExtension(g_progName);

   testing::InitGoogleTest(&argc, argv);

   if (ReadConfigOpts(argc, argv)) {
      return RUN_ALL_TESTS();
   }
   return 0;
}
