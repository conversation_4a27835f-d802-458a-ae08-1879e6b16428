<?xml version="1.0" encoding="utf-8"?>

<!--
*******************************************************************************
   Copyright (c) Omnissa, LLC. All rights reserved.
   This product is protected by copyright and intellectual property laws in the
   United States and other countries as well as by international treaties.
   - Omnissa Restricted

   agent.wxs  (product: Horizon Agent)

      WiX source for building the product msi.
*******************************************************************************
-->

<Wix xmlns="http://schemas.microsoft.com/wix/2006/wi"
     xmlns:netfx="http://schemas.microsoft.com/wix/NetFxExtension"
     xmlns:util="http://schemas.microsoft.com/wix/UtilExtension">

   <?include Variables.wxi?>

   <Product Id="*"
            Name="$(var.ProductName)"
            Language="!(loc.LANGID)"
            Version="$(var.MSIProductVersion)"
            Manufacturer="Omnissa"
            UpgradeCode="$(var.UpgradeCode)">

      <!-- Set the package ID to '*' to autogenerate with every build -->
      <Package Id="*"
               InstallerVersion="500"
               InstallScope="perMachine"
               Languages="!(loc.LANGID)"
               Description="Omnissa Horizon Agent installer"
               Comments="Contact: Your local administrator"
               Keywords="Installer,MSI,Database"
               Compressed="yes" />

      <WixVariable Id="WixUIBannerBmp" Value="$(var.SRCROOT)/install/msi/horizon/common/images/banner.bmp" />
      <WixVariable Id="WixUIDialogBmp" Value="$(var.SRCROOT)/install/msi/horizon/agent/side.bmp" />

      <!-- include product icon -->
      <Icon Id="arp.ico" SourceFile="$(var.SRCROOT)/install/msi/horizon/common/icons/setup.ico" />


      <!-- LaunchConditions -->
      <!-- NOTE: if any of these conditions evaluate to 'false' the installer will exit.
           Format is: Installed Or Not (blocked condition),
           - if the MSI is already 'Installed' we skip the rest of the condition
           - if the (blocked condition) evaluates to 'true' we exit
           Reference: view16h1 support matrix - https://wiki.eng.vmware.com/View16h1/SupportMatrices

           The agent can be installed on Windows 10 or above and Windows Server 2016 or above.
           VersionNT > 603 will take care of all client and server OSes Windows 10/Windows Server 2016
           and above.
      -->
      <Condition Message="!(loc.MINIMUM_REQUIRED_OS)">VersionNT&gt;603</Condition>
      <Condition Message="!(loc.DENY_INSTALL_DOMAIN_CONTROLLER)">Installed Or Not (MsiNTProductType=2)</Condition>
      <Condition Message="!(loc.NEED_ADMIN)">Privileged</Condition>

      <!-- Core.cab file -->
      <Media Id="1" CompressionLevel="high" Cabinet="Core.cab" EmbedCab="no" />

      <!-- WiX Include Files -->
      <?include Components.wxi?>
      <?include Configuration.wxi?>
      <?include CustomActions.wxi?>
      <?include CustomTables.wxi?>
      <?include Directory.wxi?>
      <?include Error.wxi?>
      <?include Sequences.wxi?>
      <?include UI.wxi?>
      <?include UpgradeHandler_VMW_OMN.wxi?>
      <!-- Features -->
      <?include features\AgentGina.wxi?>
      <?include features\EnableRemoteDesktop.wxi?>
      <?include features\FeatureRebootRequired.wxi?>
      <?include features\FIDO2.wxi?>
      <?include features\HideFastUserSwitching.wxi?>
      <?include features\Teams.wxi?>
      <?include features\MaxNegativeCacheTtl.wxi?>
      <?include features\MKSvChan.wxi?>
      <?include features\PcoipMfw.wxi?>
      <?include features\PrintRedir.wxi?>
      <?include features\RDSH.wxi?>
      <?include features\RDSH3D.wxi?>
      <?include features\TSDR.wxi?>
      <?include features\TSMMR.wxi?>
      <?include features\RdeSvc.wxi?>
      <?include features\RegistryFeatureStates.wxi?>
      <?include features\Rtav.wxi?>
      <?include features\SoftwareSASGeneration.wxi?>
      <?include features\uncRedirection.wxi?>
      <?include features\UniversalPrep.wxi?>
      <?include features\UrlRedirection.wxi?>
      <?include features\UsbRedirection.wxi?>
      <?include features\VDPService.wxi?>
      <?include features\VDPServiceWsnmPlugin.wxi?>
      <?include features\ViewCoreAgent.wxi?>
      <?include features\ViewFeaturePackCore.wxi?>
      <?include features\VirtualChannels.wxi?>
      <?include features\Hznbus.wxi?>
      <?include features\Hznvwebcam.wxi?>
      <?include features\Hznvaudioin.wxi?>
      <?include features\hznflstor.wxi?>
      <?include features\hznufhid.wxi?>
      <?include features\hznusm.wxi?>
      <?include features\hznvhub.wxi?>

      <PropertyRef Id="WIX_IS_NETFRAMEWORK_40_OR_LATER_INSTALLED"/>
      <!-- TODO ENABLE_CORRETTO_JRE switch is disabled until Corretto JDK is approved -->
      <!-- <Property Id="ENABLE_CORRETTO_JRE" Value="1" /> -->

      <!-- Features -->
      <Feature Id="$(var.Core_FeatureName)"
               Title="!(loc.FEATURE_NAME_CORE)"
               Description="!(loc.FEATURE_DESC_CORE)"
               Display="1"
               Absent="disallow"
               ConfigurableDirectory="INSTALLDIR"
               AllowAdvertise="no"
               Level="1">

         <ComponentGroupRef Id="AgentGina" />
         <ComponentGroupRef Id="rdsh" />
         <ComponentGroupRef Id="ViewFeaturePackCore" />
         <?if $(var.EXCLUDE_SESSION_MONITOR)=0?>
            <ComponentGroupRef Id="Hznusm" />
         <?endif?>
         <ComponentRef Id="_64_OmnResolutionSet.dll" />
         <ComponentRef Id="_Registry_bEnumerateHWBeforeSW" />
         <ComponentGroupRef Id="ViewCoreAgent" />
         <MergeRef Id="AgentOpenSSL" />
         <MergeRef Id="Crypto" />
         <MergeRef Id="DCT" />
         <?if $(var.EXCLUDE_SESSION_MONITOR)=0?>
            <MergeRef Id="Omnksm" />
         <?endif?>
         <MergeRef Id="Wsauth" />
         <ComponentRef Id="_golden_image.reg" />
         <ComponentRef Id="_32_agentAutoUpdate_BuildNumber.reg" />
         <ComponentRef Id="_64_pcoip_libidn.dll" />
         <ComponentRef Id="_Registry_FipsEnabled" />
         <ComponentRef Id="_Registry_IpProtocolUsage" />
         <ComponentRef Id="_Registry_SetDefaultMgmtPort" />
         <ComponentRef Id="_arp.ico" />
         <ComponentRef Id="_Registry_Migration" />

         <!-- START: Updatetool files -->
         <ComponentRef Id="_64_horizon_updatetool.exe" />
         <ComponentRef Id="_64_cdsclient.dll" />
         <ComponentRef Id="_64_iconv.dll" />
         <ComponentRef Id="_64_libcds.dll" />
         <ComponentRef Id="_64_libcurl.dll" />
         <ComponentRef Id="_64_libxml2.dll" />
         <ComponentRef Id="_64_zlib1.dll" />
         <ComponentRef Id="_64_glib_2.0.dll" />
         <ComponentRef Id="_64_intl.dll" />
         <ComponentRef Id="_64_pcre2_8.dll" />

         <?if $(var.SSL_VERSION) = "3.0" ?>
            <ComponentRef Id="_64_libcrypto_3.dll" />
            <ComponentRef Id="_64_libssl_3.dll" />
            <ComponentRef Id="_64_fips.dll" />
         <?else?>
            <ComponentRef Id="_64_libeay32.dll" />
            <ComponentRef Id="_64_ssleay32.dll" />
         <?endif?>
         <!-- END: Updatetool files -->

         <ComponentRef Id="_evd_configurationapi.dll" />
         <ComponentRef Id="_nm_registry_update_preserve" />
         <ComponentRef Id="_open_source_licenses.txt" />
         <ComponentRef Id="_nodeManagerSecuredQueues.reg" />
         <ComponentRef Id="_ws_perfMon.dll_reg" />
         <ComponentRef Id="_services_pipe_timeout_reg" />
         <ComponentRef Id="_vm_type_setting_reg" />
         <ComponentRef Id="_omnrxgservice.exe" />
         <ComponentRef Id="_html5NativeMessagingHost.exe" />
         <ComponentRef Id="_html5NativeMessagingHost_manifest.json" />
         <ComponentRef Id="_remove_patch.ps1" />
         <ComponentRef Id="_VirtualChannels_reg" />
         <ComponentRef Id="_VirtualChannels_Core_reg" />
         <ComponentRef Id="_hzipc_x86.dll" />
         <ComponentRef Id="_hzipc_x64.dll" />

         <!-- BlastProtocol components -->
         <MergeRef Id="Blast" />
         <ComponentRef Id="_abctrl.dll" />

         <Feature Id="$(var.BlastUDP_FeatureName)"
                  Display="0"
                  InstallDefault="followParent"
                  Absent="disallow"
                  AllowAdvertise="no"
                  Level="1">
            <MergeRef Id="HznVudpd" />
         </Feature>

         <!-- RDS components -->
         <MergeRef Id="HznRds" />

         <!-- VDPService 32-bit components -->
         <ComponentRef Id="_VDPService_x86.dll" />
         <ComponentRef Id="_VDPServicePriv_x86.dll" />

          <?if $(var.SSL_VERSION) = "3.0" ?>
             <ComponentRef Id="_libssl_3_x86.dll" />
             <ComponentRef Id="_libcrypto_3_x86.dll" />
             <ComponentRef Id="_fips_x86.dll" />
         <?else?>
             <ComponentRef Id="_ssleay32_x86.dll" />
             <ComponentRef Id="_libeay32_x86.dll" />
         <?endif?>

         <!-- VDPService 64-bit components -->
         <ComponentRef Id="_VDPService_x64.dll" />
         <ComponentRef Id="_VDPServicePriv_x64.dll" />

         <?if $(var.SSL_VERSION) = "3.0" ?>
            <ComponentRef Id="_libssl_3_x64.dll" />
            <ComponentRef Id="_libcrypto_3_x64.dll" />
            <ComponentRef Id="_fips_x64.dll" />
         <?else?>
            <ComponentRef Id="_ssleay32_x64.dll" />
            <ComponentRef Id="_libeay32_x64.dll" />
         <?endif?>

         <!-- VDPService WSNM Plugin components -->
         <ComponentRef Id="_vdpservice_wsnmplugin.dll" />

         <!-- PCOIP MFW components -->
         <ComponentRef Id="_pcoip_mfw.dll" />

         <!-- HTML5 Server / MS Teams components -->
         <ComponentRef Id="_html5Server.exe" />
         <ComponentRef Id="_teamsHelper.exe" />
         <ComponentRef Id="_RtmControl.dll" />

         <!-- RdeSvc Components-->
         <ComponentRef Id="_UNITYSHELL_EXE" />
         <ComponentRef Id="_RDEVCHANSTUB_DLL" />
         <ComponentRef Id="_RDEVCHANSTUB_64_DLL" />
         <ComponentRef Id="_MESSAGEFRAMEWORK_DLL" />
         <ComponentRef Id="_VMWIME_DLL" />
         <ComponentRef Id="_GLIB_2_0_DLL" />
         <ComponentRef Id="_PCRE2_DLL" />
         <ComponentRef Id="_GMODULE_2_0_DLL" />
         <ComponentRef Id="_GOBJECT_2_0_DLL" />
         <ComponentRef Id="_GTHREAD_2_0_DLL" />
         <ComponentRef Id="_ICONV_DLL" />
         <ComponentRef Id="_INTL_DLL" />
         <ComponentRef Id="_LIBPNG_DLL" />
         <ComponentRef Id="_LIBFFI_DLL" />

         <ComponentRef Id="_RDESERVER_64_EXE" />
         <ComponentRef Id="_INPUTDEVTAP_64_DLL" />
         <ComponentRef Id="_MESSAGEFRAMEWORK_64_DLL" />
         <ComponentRef Id="_VMWIME_64_DLL" />
         <ComponentRef Id="_ZLIB1_64_DLL" />
         <ComponentRef Id="_GLIBMM_2_68_X64_DLL" />
         <ComponentRef Id="_GLIB_2_0_X64_DLL" />
         <ComponentRef Id="_PCRE2_X64_DLL" />
         <ComponentRef Id="_GMODULE_2_0_X64_DLL" />
         <ComponentRef Id="_GOBJECT_2_0_X64_DLL" />
         <ComponentRef Id="_GTHREAD_2_0_X64_DLL" />
         <ComponentRef Id="_SIGC_3_0_X64_DLL" />
         <ComponentRef Id="_ICONV_X64_DLL" />
         <ComponentRef Id="_INTL_X64_DLL" />
         <ComponentRef Id="_LIBPNG_X64_DLL" />
         <ComponentRef Id="_LIBFFI_X64_DLL" />

         <!-- PCoIP components -->
         <ComponentRef Id="_pcoip_tcpport.reg" />
         <ComponentRef Id="_pcoip_udpport.reg" />
         <ComponentRef Id="_pcoip_vchan64.dll" />
         <ComponentRef Id="_audio_enumerator_def.reg" />
         <ComponentRef Id="_pcoip_server_fips_mode.reg" />
         <ComponentRef Id="_vdp_rdpvcbridge.dll" />
         <ComponentRef Id="_vmkbd.sys" />
         <ComponentRef Id="_32_vdp_rdpvcbridge.dll" />
         <ComponentRef Id="_32_pcoip_vchan.dll" />
         <!-- PCoIP x64 components -->
         <ComponentRef Id="_pcoip_vchan_path.reg" />
         <ComponentRef Id="_64_audiodevtap.dll" />
         <ComponentRef Id="_64_inputdevtap.dll" />
         <ComponentRef Id="_64_pcoip_server_win32.exe" />
         <ComponentRef Id="_64_pthreadVC2.dll" />
         <ComponentRef Id="_64_svgadevtap.dll" />
         <ComponentRef Id="_64_svga_zlib1.dll" />
         <ComponentRef Id="_64_svga_libpng16.dll" />
         <ComponentRef Id="_64_ICuiSDK64.dll" />
         <ComponentRef Id="_64_libmfxsw64.dll" />
         <ComponentRef Id="_64_pcoip_agent_win64.dll" />
         <ComponentRef Id="_64_pcoip_perf_provider64.dll" />
         <ComponentRef Id="_64_mfxplugin64_screen_capture.dll" />
         <ComponentRef Id="_64_plugin.cfg" />
         <?if $(var.SSL_VERSION) = "3.0" ?>
            <ComponentRef Id="_64_pcoip_libcrypto_3.dll" />
            <ComponentRef Id="_64_pcoip_libssl_3.dll" />
            <ComponentRef Id="_64_pcoip_fips.dll" />
         <?else?>
            <ComponentRef Id="_64_pcoip_libeay32.dll" />
            <ComponentRef Id="_64_pcoip_ssleay32.dll" />
         <?endif?>

         <!-- Interception driver used to inject hznsci.dll-->
         <ComponentRef Id="_32_hznsci.dll" />
         <ComponentRef Id="_hznsci.dll" />
         <!-- Interception driver used to inject certStoreIntercept.dll-->
         <ComponentRef Id="_certStoreIntercept.dll" />
         <ComponentRef Id="_hznicpdr.reg" />
         <MergeRef Id="Hznicpdr" />

         <!-- Whfb Redirection -->
         <ComponentRef Id="_whfbRedirection.dll" />

         <!-- ipclib dll: used by RdeServer plugins WhfbRedirection &
              uncRedirection
         -->
         <ComponentRef Id="_ipclibRpc_dynamic_md.dll" />

         <!-- Mksvchan components -->
         <ComponentRef Id="_HorizonClipboard.exe" />
         <ComponentRef Id="_HorizonClipboard.exe_RDSH_reg" />
         <ComponentRef Id="_HorizonClipboard.exe_EventLog_reg" />

         <!-- UnityTouch components -->
         <MergeRef Id="UnityTouch" />

         <!-- Bellsoft -->
         <Feature Id="$(var.Bellsoft_FeatureName)"
                  Title="!(loc.FEATURE_NAME_BELLSOFT)"
                  Description="!(loc.FEATURE_DESC_BELLSOFT)"
                  Display="0"
                  InstallDefault="followParent"
                  Absent="disallow"
                  AllowAdvertise="no"
                  Level="1" >

            <!-- TODO disabled until Corretto JDK is approved to be shipped -->
            <!-- <Condition Level="0">Not ENABLE_CORRETTO_JRE=0</Condition>
            <Condition Level="1">REMOVE~="ALL" Or REMOVE~&gt;&lt;"$(var.Bellsoft_FeatureName)"</Condition> -->
            <MergeRef Id="Server-Jre" />
         </Feature>

         <!-- Corretto -->
         <!-- TODO disabled until Corretto JDK is approved to be shipped -->
         <!--          
         <Feature Id="$(var.Corretto_FeatureName)"
                  Title="!(loc.FEATURE_NAME_CORRETTO)"
                  Description="!(loc.FEATURE_DESC_CORRETTO)"
                  Display="0"
                  InstallDefault="followParent"
                  Absent="disallow"
                  AllowAdvertise="no"
                  Level="1">

            <Condition Level="0">Not ENABLE_CORRETTO_JRE=1</Condition>
            <Condition Level="1">REMOVE~="ALL" Or REMOVE~&gt;&lt;"$(var.Corretto_FeatureName)"</Condition>
            <MergeRef Id="Server-Jre-Corretto" />
         </Feature> 
         -->        

         <!-- UniversalPrep components -->
         <ComponentRef Id="_hzaprep.exe" />
         <ComponentRef Id="_unattend.xml" />

         <Feature Id="$(var.URLRedirection_FeatureName)"
                  Display="1"
                  Title="!(loc.FEATURE_NAME_URLREDIRECTION)"
                  Description="!(loc.FEATURE_DESC_URLREDIRECTION)"
                  InstallDefault="followParent"
                  AllowAdvertise="no"
                  Level="1">
            <!-- Install if URL_FILTERING_ENABLED is set and this feature isn't already installed by the client -->
            <Condition Level="0">Not ((URL_FILTERING_ENABLED=1 Or REG_URL_FILTERING_ENABLED=1) And REG_URL_FILTERING_ENABLED_SIDE&lt;&gt;"Client")</Condition>
            <Condition Level="1">REMOVE~="ALL" Or REMOVE~&gt;&lt;"$(var.URLRedirection_FeatureName)"</Condition>
            <ComponentGroupRef Id="_URLREDIRECTION" />
         </Feature>

         <Feature Id="$(var.UNCRedirection_FeatureName)"
                  Display="1"
                  Title="!(loc.FEATURE_NAME_UNCREDIRECTION)"
                  Description="!(loc.FEATURE_DESC_UNCREDIRECTION)"
                  InstallDefault="followParent"
                  AllowAdvertise="no"
                  Level="1">
            <!-- Install if ENABLE_UNC_REDIRECTION is set and this feature isn't already installed by the client -->
            <Condition Level="0">Not ((ENABLE_UNC_REDIRECTION=1 Or REG_ENABLE_UNC_REDIRECTION=1) And REG_ENABLE_UNC_REDIRECTION_SIDE&lt;&gt;"Client")</Condition>
            <Condition Level="1">REMOVE~="ALL" Or REMOVE~&gt;&lt;"$(var.UNCRedirection_FeatureName)"</Condition>
            <ComponentGroupRef Id="_UNCREDIRECTION" />
         </Feature>

         <!-- These are hidden, non-optional components that always need to be installed,
              unless they are being installed on an unsupported machine. -->
         <Feature Id="$(var.PSG_FeatureName)"
                  Display="0"
                  InstallDefault="followParent"
                  Absent="disallow"
                  AllowAdvertise="no"
                  Level="1">
            <!-- Install on RDSH only (not daas deployments) -->
            <Condition Level="0">Not (TerminalServer And VdmForceDesktopAgent&lt;&gt;1 And Not HORIZON_CLOUD_DEPLOYMENT)</Condition>
            <Condition Level="1">REMOVE~="All" Or REMOVE~&gt;&lt;"$(var.PSG_FeatureName)"</Condition>
            <ComponentRef Id="_psg.reg" />
            <MergeRef Id="PSG" />
         </Feature>

         <!-- Hyper-V graphics driver -->
         <Feature Id="$(var.HznVdisplay_FeatureName)"
                  Display="0"
                  InstallDefault="followParent"
                  Absent="disallow"
                  AllowAdvertise="no"
                  Level="1">
            <!-- Only install on desktops (not RDSH deployments) on Windows RS3 and older where IddCx support is not
                 available. However the "INSTALL_BLAST_VDISPLAY_DRIVER" option is provided to force installation.
                 This option is intentionally different from "INSTALL_VDISPLAY_DRIVER" as that option was previously publicly
                 documented and could install by accident if scripts are not updated. -->
            <Condition Level="0">Not ((Not TerminalServer) And (Not WINDOWS_10_RS4_AND_ABOVE=1 Or INSTALL_BLAST_VDISPLAY_DRIVER=1)) </Condition>
            <Condition Level="1">REMOVE~="All" Or REMOVE~&gt;&lt;"$(var.HznVdisplay_FeatureName)"</Condition>
            <MergeRef Id="HznVdisplay" />
         </Feature>

         <!-- Horizon indirect display driver -->
         <Feature Id="$(var.HznVidd_FeatureName)"
                  Display="0"
                  InstallDefault="followParent"
                  Absent="disallow"
                  AllowAdvertise="no"
                  Level="1">
            <!-- Refer to custom action VMCheckSupportedHznviddVersion which would determine if hznvidd driver would be installed. The
                 hznvidd driver would be installed if it is supported AND hznvidd2 is not supported -->
            <Condition Level="0">Not (SUPPORT_HZNVIDD_INSTALL=1 And SUPPORT_HZNVIDD2_INSTALL=0)</Condition>
            <Condition Level="1">REMOVE~="All" Or REMOVE~&gt;&lt;"$(var.HznVidd_FeatureName)"</Condition>
            <ComponentRef Id="_hznvidd.reg" />
            <MergeRef Id="HznVidd" />
         </Feature>

         <!-- Horizon indirect display driver version 2-->
         <Feature Id="$(var.HznVidd2_FeatureName)"
                  Display="0"
                  InstallDefault="followParent"
                  Absent="disallow"
                  AllowAdvertise="no"
                  Level="1">

            <!-- Refer to custom action VMCheckSupportedHznviddVersion which would determine if hznvidd2 driver would be installed  where
                 SUPPORT_HZNVIDD2_INSTALL is set to 1 -->
            <Condition Level="0">Not (SUPPORT_HZNVIDD2_INSTALL=1)</Condition>
            <Condition Level="1">REMOVE~="All" Or REMOVE~&gt;&lt;"$(var.HznVidd2_FeatureName)"</Condition>
            <ComponentRef Id="_hznvidd.reg" />
            <MergeRef Id="HznVidd2" />
         </Feature>

         <!-- Only install on physical PCs with Win 10 RS4 and above -->
         <Feature Id="$(var.SmartCard_SingleuserTS_FeatureName)"
                  Display="0"
                  InstallDefault="followParent"
                  Absent="disallow"
                  AllowAdvertise="no"
                  Level="1">
            <Condition Level="0">Not ((MsiNTProductType=1 And WINDOWS_10_RS4_AND_ABOVE=1 And (VM="" And Not TERA_HOST_CARD_PRESENT=1)) Or VDM_FORCE_RDS_INSTALL=1)</Condition>
            <Condition Level="1">REMOVE~="All" Or REMOVE~&gt;&lt;"$(var.SmartCard_SingleuserTS_FeatureName)"</Condition>
            <ComponentRef Id="_bin_wsnm_scredir.dll" />
            <MergeRef Id="HznVscrd" />
         </Feature>

         <!-- Only install on Win10 20H2 and above regardless of OS type -->
         <Feature Id="$(var.FIDO2_FeatureName)"
                  Display="0"
                  InstallDefault="followParent"
                  Absent="disallow"
                  AllowAdvertise="no"
                  Level="1">
            <Condition Level="0">Not (WINDOWS_10_20H2_AND_ABOVE=1)</Condition>
            <Condition Level="1">REMOVE~="All" Or REMOVE~&gt;&lt;"$(var.FIDO2_FeatureName)"</Condition>
            <ComponentGroupRef Id="_FIDO2" />
            <ComponentRef Id="_VirtualChannels_FIDO2_reg" />
         </Feature>

      </Feature> <!-- Core -->

      <Feature Id="$(var.ClientDriveRedirection_FeatureName)"
               Title="!(loc.FEATURE_NAME_CLIENTDRIVEREDIRECTION)"
               Description="!(loc.FEATURE_DESC_CLIENTDRIVEREDIRECTION)"
               Display="11"
               AllowAdvertise="no"
               Level="1">
         <ComponentGroupRef Id="TSDR" />
         <!-- File system minifilter used to improve rdpdr performance -->
         <MergeRef Id="Hzncdrfilter" />
         <ComponentRef Id="_VirtualChannels_CDR_reg" />
      </Feature>

      <Feature Id="$(var.RDP_FeatureName)"
               Title="Enable RDP (hidden)"
               Display="0"
               AllowAdvertise="no"
               Level="1">
         <ComponentRef Id="_Registry_enable_RDP" />
         <ComponentRef Id="_Registry_disable_NLA" />
      </Feature>

      <Feature Id="$(var.RDSH3D_FeatureName)"
               Title="!(loc.FEATURE_NAME_RDSH3D)"
               Description="!(loc.FEATURE_DESC_RDSH3D)"
               Display="3"
               AllowAdvertise="no"
               Level="32001">
         <!-- Install on RDSH and physical machines with Win 10 RS4 and above -->
         <Condition Level="0">Not($(var.RDSH3D_SupportedCondition))</Condition>
         <Condition Level="1">REMOVE~="All" Or REMOVE~&gt;&lt;"$(var.RDSH3D_FeatureName)"</Condition>

         <!-- The RDSH3D feature needs to have at least 1 component
              (see - https://bugzilla.eng.vmware.com/show_bug.cgi?id=2568458) -->
         <Component Id="RDSH3D_dummy_component" Guid="8A3E6D84-F1CB-4EC5-A069-957C5B9C4305" Directory="INSTALLDIR">
            <CreateFolder Directory="INSTALLDIR" />
         </Component>
      </Feature>

      <Feature Id="$(var.RTAV_FeatureName)"
               Title="!(loc.FEATURE_NAME_RTAV)"
               Description="!(loc.FEATURE_DESC_RTAV)"
               Display="8"
               AllowAdvertise="no"
               Level="100">
         <ComponentGroupRef Id="_RTAV" />
         <ComponentRef Id="_driver_hznbus" />
         <ComponentRef Id="_driver_hznvaudioin" />
         <ComponentRef Id="_driver_hznvwebcam" />
      </Feature>

      <Feature Id="$(var.NGVC_FeatureName)"
               Title="!(loc.FEATURE_NAME_NGVC)"
               Description="!(loc.FEATURE_DESC_NGVC)"
               Display="9"
               AllowAdvertise="no"
               Level="1">
         <!-- Install on 'managed' machines (not physical, Workstation, or Fusion guests), unless VDM_INSTALLER_CHECKS=0 -->
         <Condition Level="0">Not (VDM_VC_MANAGED_AGENT=1 Or VDM_INSTALLER_CHECKS=0 Or GOLDEN_IMAGE_INSTALL=1)</Condition>
         <Condition Level="1">REMOVE~="All" Or REMOVE~&gt;&lt;"$(var.NGVC_FeatureName)"</Condition>
         <!-- The NGVC feature should be disabled (but optional) by default during RDSH deployments -->
         <Condition Level="32001">VDM_VC_MANAGED_AGENT=1 And TerminalServer</Condition>
         <!-- The NGVC feature should be disabled (but optional) by default during Nutanix deployments -->
         <Condition Level="32002">VM="Nutanix"</Condition>
         <MergeRef Id="NGVC" />
      </Feature>

      <Feature Id="$(var.ScannerRedirection_FeatureName)"
               Title="!(loc.FEATURE_NAME_SCANNERREDIRECTION)"
               Description="!(loc.FEATURE_DESC_SCANNERREDIRECTION)"
               Display="20"
               AllowAdvertise="no"
               Level="32001">
         <!-- ScannerRedirection should not be displayed if "Desktop Experience" feature is not installed in RDSH environment. -->
         <Condition Level="0">Not (MsiNTProductType=1 Or (SVC_WINDOWS_IMAGE_ACQUISITION And ServerOsValid)) Or Not INCOMPATIBLE_PRODUCT_FOUND=0</Condition>
         <Condition Level="1">REMOVE~="All" Or REMOVE~&gt;&lt;"$(var.ScannerRedirection_FeatureName)"</Condition>
         <Condition Level="1000">MsiNTProductType=3 And SVC_WINDOWS_IMAGE_ACQUISITION</Condition>
         <MergeRef Id="Jail" />
         <MergeRef Id="Netlink" />
         <MergeRef Id="ScannerRDPPFModule" />
         <MergeRef Id="ScannerRDPSysModule" />
         <ComponentRef Id="_VirtualChannels_Scanner_reg" />
         <ComponentRef Id="_32_scannerredirhorizonds.dll" />
         <ComponentRef Id="_64_scannerredirhorizonds.dll" />
         <ComponentRef Id="_scannerredirserviceplugin.dll" />
         <ComponentRef Id="_scannerredirtray.exe" />
         <ComponentRef Id="_VirtualChannels_ScannerRedirMac_reg" />
      </Feature>

      <Feature Id="$(var.SerialPortRedirection_FeatureName)"
               Title="!(loc.FEATURE_NAME_SERIALPORTREDIRECTION)"
               Description="!(loc.FEATURE_DESC_SERIALPORTREDIRECTION)"
               Display="21"
               AllowAdvertise="no"
               Level="32001">
         <Condition Level="0">Not (MsiNTProductType=1 Or ServerOsValid)</Condition>
         <Condition Level="1">REMOVE~="All" Or REMOVE~&gt;&lt;"$(var.SerialPortRedirection_FeatureName)"</Condition>
         <MergeRef Id="Jail" />
         <MergeRef Id="Netlink" />
         <MergeRef Id="SerialPortRedirection" />
         <ComponentRef Id="_VirtualChannels_SerialPort_reg" />
      </Feature>

      <Feature Id="$(var.SmartCard_FeatureName)"
               Title="!(loc.FEATURE_NAME_SMARTCARD)"
               Description="!(loc.FEATURE_DESC_SMARTCARD)"
               Display="20"
               AllowAdvertise="no"
               Level="1000">
         <!-- Install as a part of 'desktop' installs (not physical PCs with Win 10
              RS4 and above because for this configuration, smartcard is installed by default
              as part of Core Feature) unless VDM_INSTALLER_CHECKS=0 or VDM_FORCE_RDS_INSTALL=1-->
         <Condition Level="0">Not ((Not TerminalServer And Not(WINDOWS_10_RS4_AND_ABOVE=1 And (VM="" And Not TERA_HOST_CARD_PRESENT=1)) Or VdmForceDesktopAgent=1 Or VDM_INSTALLER_CHECKS=0) Or VDM_FORCE_RDS_INSTALL=1)</Condition>
         <Condition Level="1">REMOVE~="All" Or REMOVE~&gt;&lt;"$(var.SmartCard_FeatureName)"</Condition>
         <ComponentRef Id="_bin_wsnm_scredir.dll" />
         <MergeRef Id="HznVscrd" />
      </Feature>

      <Feature Id="$(var.TSMMR_FeatureName)"
               Title="!(loc.FEATURE_NAME_TSMMR)"
               Description="!(loc.FEATURE_DESC_TSMMR)"
               Display="0"
               AllowAdvertise="no"
               Level="100">
         <!-- Install on desktops or RDSH installs, but not as a part of a Server-Desktop install. -->
         <Condition Level="0">Not (MsiNTProductType=1 Or TerminalServer And Not VdmForceDesktopAgent)</Condition>
         <Condition Level="1">REMOVE~="All" Or REMOVE~&gt;&lt;"$(var.TSMMR_FeatureName)"</Condition>
         <!-- TSMMR -->
         <ComponentRef Id="_tsmmrServer.dll" />
         <ComponentRef Id="_tsmmrServerDShow.dll" />
         <ComponentRef Id="_tsmmr_reg" />
         <ComponentRef Id="_tsmmrServerDShow_reg" />
         <ComponentRef Id="_tsmmrServer.dll_64" />
         <ComponentRef Id="_tsmmrServerDShow.dll_64" />
         <ComponentRef Id="_tsmmr_reg_64" />
         <ComponentRef Id="_tsmmrServerDShow_reg_64" />
      </Feature>

      <Feature Id="$(var.VMWPrint_FeatureName)"
               Title="!(loc.FEATURE_NAME_VMWPRINT)"
               Description="!(loc.FEATURE_DESC_VMWPRINT)"
               Display="35"
               AllowAdvertise="no"
               Level="100">
         <Condition Level="1">REMOVE~="All" Or REMOVE~&gt;&lt;"$(var.VMWPrint_FeatureName)"</Condition>

         <ComponentRef Id="_prvdpplugin.dll" />
         <ComponentRef Id="_horizon_print_redir_server.exe" />
         <ComponentRef Id="_horizon_print_redir_service.exe" />
         <ComponentRef Id="_hznprmon.dll" />
         <ComponentRef Id="_hznprserverapi.dll" />
         <ComponentRef Id="_hznprui.dll"/>
         <ComponentRef Id="_hznprUI_de_de_407.dll"/>
         <ComponentRef Id="_hznprUI_en_us_409.dll"/>
         <ComponentRef Id="_hznprUI_es_es_40a.dll"/>
         <ComponentRef Id="_hznprUI_fr_fr_40c.dll"/>
         <ComponentRef Id="_hznprUI_ja_jp_411.dll"/>
         <ComponentRef Id="_hznprUI_ko_kr_412.dll"/>
         <ComponentRef Id="_hznprUI_zh_cn_804.dll"/>
         <ComponentRef Id="_hznprUI_zh_tw_404.dll"/>
         <ComponentRef Id="_hznprupd.txt"/>
         <ComponentRef Id="_horizon_print_processor.dll"/>
         <ComponentRef Id="_hznprupd.cat"/>
         <ComponentRef Id="_hznprUpd.inf"/>
         <ComponentRef Id="_hznprGraphics.dll"/>
         <ComponentRef Id="_hznprps.dll"/>
         <ComponentRef Id="_hznprps.inf"/>
         <ComponentRef Id="_hznprps.ini"/>
         <ComponentRef Id="_hznprps.ppd"/>
         <ComponentRef Id="_hznprps.cat"/>
         <ComponentRef Id="_VirtualChannels_Print_reg" />
      </Feature>

      <Feature Id="$(var.USB_FeatureName)"
               Title="!(loc.FEATURE_NAME_USB)"
               Description="!(loc.FEATURE_DESC_USB)"
               Display="3"
               AllowAdvertise="no"
               Level="1000">
         <!-- Install on desktops, RDSH server, server OSes forced to be 'desktops', or if VDM_INSTALLER_CHECKS=0 -->
         <Condition Level="0">Not (MsiNTProductType=1 Or TerminalServer Or VdmForceDesktopAgent=1 Or VDM_INSTALLER_CHECKS=0)</Condition>
         <Condition Level="1">REMOVE~="All" Or REMOVE~&gt;&lt;"$(var.USB_FeatureName)"</Condition>
         <ComponentRef Id="_driver_usb" />
         <ComponentRef Id="_hznufhid.sys" />
         <ComponentRef Id="_hznflstor.sys" />
         <ComponentRef Id="_usbRedirectionServer.dll_64" />
         <ComponentRef Id="_usbRedirectionServer_sidechanneltype_reg" />
         <ComponentRef Id="_ws_vhub.dll" />
         <ComponentRef Id="_ws_vdpvhub.dll" />
         <ComponentRef Id="_includeVidPidRegistryEntry" />
         <ComponentRef Id="_vhublib.dll" />
         <ComponentRef Id="_ws_usbstor.dll" />
         <ComponentRef Id="_vhublib64.dll" />
         <ComponentRef Id="_32_pcoipAudioFwd.reg" />
         <ComponentRef Id="_VirtualChannels_USB_reg" />
      </Feature>

      <Feature Id="$(var.HznVaudio_FeatureName)"
               Title="!(loc.FEATURE_NAME_HZNVAUDIO)"
               Description="!(loc.FEATURE_DESC_HZNVAUDIO)"
               Display="21"
               AllowAdvertise="no"
               Level="1">
         <!-- Install for VDI session -->
         <?define VmwvaudioWin10PhysicalAgent="(MsiNTProductType=1 And WINDOWS_10_RS4_AND_ABOVE=1 And (VM="" And Not TERA_HOST_CARD_PRESENT=1)) Or VDM_FORCE_RDS_INSTALL=1"?>
         <Condition Level="0">Not (Not TerminalServer And Not ($(var.VmwvaudioWin10PhysicalAgent)))</Condition>
         <Condition Level="1">REMOVE~="All" Or REMOVE~&gt;&lt;"$(var.HznVaudio_FeatureName)"</Condition>
         <!-- feature status retention from VmwVaudio to HznVaudio -->
         <Condition Level="200">(Not TerminalServer And Not ($(var.VmwvaudioWin10PhysicalAgent))) AND FEATURE_VMWVAUDIO_INSTALLED="Absent"</Condition>
         <MergeRef Id="HznVaudio" />
      </Feature>

      <Feature Id="$(var.SdoSensor_FeatureName)"
               Title="!(loc.FEATURE_NAME_SDOSENSOR)"
               Description="!(loc.FEATURE_DESC_SDOSENSOR)"
               Display="25"
               AllowAdvertise="no"
               Level="32001">
         <!-- Install only on RDSH servers, not supported on desktops and non-RDSH servers -->
         <Condition Level="0">TerminalServer</Condition>
         <Condition Level="1">REMOVE~="All" Or REMOVE~&gt;&lt;"$(var.SdoSensor_FeatureName)"</Condition>
         <MergeRef Id="Hznsdo" />
      </Feature>

      <Feature Id="$(var.GEOREDIR_FeatureName)"
               Title="!(loc.FEATURE_NAME_GEOREDIR)"
               Description="!(loc.FEATURE_DESC_GEOREDIR)"
               Display="26"
               AllowAdvertise="no"
               Level="32001">
         <MergeRef Id="Hzngeoloc" />
         <ComponentRef Id="_geoRedir_reg_install_64" />
         <ComponentRef Id="_VirtualChannels_GEO_reg" />
      </Feature>

      <Feature Id="$(var.PerfTracker_FeatureName)"
               Title="!(loc.FEATURE_NAME_PERFTRACKER)"
               Description="!(loc.FEATURE_DESC_PERFTRACKER)"
               Display="33"
               AllowAdvertise="no"
               Level="32001">
         <MergeRef Id="PerfTracker" />
      </Feature>

      <Feature Id="$(var.HybridLogon_FeatureName)"
               Title="!(loc.FEATURE_NAME_HYBRIDLOGON)"
               Description="!(loc.FEATURE_DESC_HYBRIDLOGON)"
               Display="34"
               AllowAdvertise="no"
               Level="32001">
         <!-- Install only Windows Server 2019 and earlier with RDSH -->
         <Condition Level="0">Not (TerminalServer And WINDOWS_RELEASE_ID &lt;= WINDOWS_2019_RELEASE_ID)</Condition>
         <Condition Level="1">REMOVE~="All" Or REMOVE~&gt;&lt;"$(var.HybridLogon_FeatureName)"</Condition>
         <ComponentRef Id="_wslogonscriptlauncher.exe" />
      </Feature>

      <Feature Id="$(var.HELPDESK_FeatureName)"
               Title="!(loc.FEATURE_NAME_HELPDESK)"
               Description="!(loc.FEATURE_DESC_HELPDESK)"
               Display="35"
               AllowAdvertise="no"
               Level="100">
         <Condition Level="1">REMOVE~="All" Or REMOVE~&gt;&lt;"$(var.HELPDESK_FeatureName)"</Condition>
         <ComponentRef Id="_bin_wsnm_helpdesk.dll" />
         <ComponentRef Id="_bin_wssm_helpdesk.dll" />
      </Feature>

      <Feature Id="$(var.VMWStorageDrive_FeatureName)"
               Title="!(loc.FEATURE_NAME_STORAGEDRIVE)"
               Description="!(loc.FEATURE_DESC_STORAGEDRIVE)"
               Display="36"
               AllowAdvertise="no"
               Level="100">
         <Condition Level="1">REMOVE~="All" Or REMOVE~&gt;&lt;"$(var.VMWStorageDrive_FeatureName)"</Condition>
         <ComponentRef Id="_sdrserviceplugin.dll" />
         <ComponentRef Id="_sdrserverutil.exe" />
         <ComponentRef Id="_sdr_sidechanneltype_reg" />
         <ComponentRef Id="_VirtualChannels_StorageDrive_reg" />
         <MergeRef Id="Omnsdr" />
      </Feature>

      <!-- Upgrade Table -->
      <!-- The upgrade table is populated at runtime, but we need to make sure
           the table exists. This dummy row will ensure the Upgrade table is
           created with the appropriate schema.

           NOTE: add all other Upgrade table entries to CustomTables.wxs -->
      <Upgrade Id="{10700346-179A-47A5-B6B5-35E2878827A7}">
         <UpgradeVersion Minimum="0.0.0"
                         IncludeMinimum="no"
                         OnlyDetect="yes"
                         Property="UNUSED" />
      </Upgrade>

      <!-- AppSearch Table -->
      <Property Id="FEATURE_SCANNER_INSTALLED">
         <RegistrySearch Id="_32_sigScannerInstalled"
                         Root="HKLM"
                         Key="SOFTWARE\Omnissa\Horizon\Features"
                         Name="ScannerRedirection"
                         Type="raw" />
      </Property>

      <Property Id="FEATURE_SERIAL_INSTALLED">
         <RegistrySearch Id="_32_sigSerialInstalled"
                         Root="HKLM"
                         Key="SOFTWARE\Omnissa\Horizon\Features"
                         Name="SerialPortRedirection"
                         Type="raw" />
      </Property>

      <Property Id="VMWARE_AGENT_INSTALLPATH">
         <RegistrySearch Id="VMware_AgentInstallPath"
                         Root="HKLM"
                         Key="SOFTWARE\VMware, Inc.\VMware VDM"
                         Name="AgentInstallPath"
                         Type="raw" />
      </Property>

      <!--Do not update below code having vmw* featureID as part of rebranding change.
          As part of rebranding, feature IDs are changed to hzn*, but installer  still
          need to detect previous vmw* feature to preserve its state over the upgrade
      -->
      <Property Id="FEATURE_VMWVAUDIO_INSTALLED">
         <RegistrySearch Id="Vaudio_installed"
                         Root="HKLM"
                         Key="Software\VMware, Inc.\Installer\Features_HorizonAgent"
                         Name="VmwVaudio"
                         Type="raw" />
      </Property>

      <Property Id="MGMT_PORT">
         <RegistrySearch Id="sigMgmtPort"
                         Root="HKLM"
                         Key="SOFTWARE\Omnissa\Horizon\Node Manager"
                         Name="Management Port"
                         Type="raw" />
      </Property>

      <Property Id="NM_AGENT_IDENTITY">
         <RegistrySearch Id="nmAgentIdentity"
                         Root="HKLM"
                         Key="SOFTWARE\Omnissa\Horizon\Node Manager"
                         Name="Agent Identity"
                         Type="raw" />
      </Property>

      <Property Id="NM_AGENT_PUBLIC_KEY">
         <RegistrySearch Id="nmAgentPublicKey"
                         Root="HKLM"
                         Key="SOFTWARE\Omnissa\Horizon\Node Manager"
                         Name="Agent Public Key"
                         Type="raw" />
      </Property>

      <Property Id="NM_AGENT_PRIVATE_KEY">
         <RegistrySearch Id="nmAgentPrivateKey"
                         Root="HKLM"
                         Key="SOFTWARE\Omnissa\Horizon\Node Manager"
                         Name="Agent Private Key"
                         Type="raw" />
      </Property>

      <Property Id="NM_AGENT_KEY_REFERENCE">
         <RegistrySearch Id="nmAgentKeyReference"
                         Root="HKLM"
                         Key="SOFTWARE\Omnissa\Horizon\Node Manager"
                         Name="Agent Key Reference"
                         Type="raw" />
      </Property>

      <Property Id="NM_SERVER_DN">
         <RegistrySearch Id="nmServerDN"
                         Root="HKLM"
                         Key="SOFTWARE\Omnissa\Horizon\Node Manager"
                         Name="Server DN"
                         Type="raw" />
      </Property>

      <Property Id="NM_BROKER_PUBLIC_KEY">
         <RegistrySearch Id="nmBrokerPublicKey"
                         Root="HKLM"
                         Key="SOFTWARE\Omnissa\Horizon\Node Manager"
                         Name="Broker Public Key"
                         Type="raw" />
      </Property>

      <Property Id="NM_MS_MODE">
         <RegistrySearch Id="nmMsMode"
                         Root="HKLM"
                         Key="SOFTWARE\Omnissa\Horizon\Node Manager"
                         Name="MS Mode"
                         Type="raw" />
      </Property>

      <Property Id="NM_DISCONNECT_LIMIT_MINUTES">
         <RegistrySearch Id="nmDisconnectLimitMinutes"
                         Root="HKLM"
                         Key="SOFTWARE\Omnissa\Horizon\Node Manager"
                         Name="DisconnectLimitMinutes"
                         Type="raw" />
      </Property>

      <Property Id="NM_IDLE_LIMIT_MINUTES">
         <RegistrySearch Id="nmIdleLimitMinutes"
                         Root="HKLM"
                         Key="SOFTWARE\Omnissa\Horizon\Node Manager"
                         Name="IdleLimitMinutes"
                         Type="raw" />
      </Property>

      <Property Id="POWERMANAGEMENT_STATUS">
         <RegistrySearch Id="sigPowerManagementStatus"
                         Root="HKLM"
                         Key="[PowerManagementPath]"
                         Name="Attributes"
                         Type="raw" />
      </Property>

      <Property Id="RDP_MAX_INSTANCE_COUNT">
         <RegistrySearch Id="sigRdpMaxInstanceCount"
                         Root="HKLM"
                         Key="SYSTEM\CurrentControlSet\Control\Terminal Server\WinStations\RDP-Tcp"
                         Name="MaxInstanceCount"
                         Type="raw" />
      </Property>

      <Property Id="RDS_MAX_INSTANCE_COUNT">
         <RegistrySearch Id="sigRdsMaxInstanceCount"
                         Root="HKLM"
                         Key="SYSTEM\CurrentControlSet\Control\Terminal Server\WinStations\Horizon-RDS"
                         Name="MaxInstanceCount"
                         Type="raw" />
      </Property>

      <Property Id="TOOLSDIR">
         <RegistrySearch Id="sigTools"
                         Root="HKLM"
                         Key="SOFTWARE\VMware, Inc.\VMware Tools"
                         Name="InstallPath"
                         Win64="yes"
                         Type="raw" />
      </Property>

      <Property Id="TOOLSDIR32">
         <RegistrySearch Id="sigTools32"
                         Root="HKLM"
                         Key="SOFTWARE\VMware, Inc.\VMware Tools"
                         Name="InstallPath"
                         Type="raw" />
      </Property>

      <Property Id="VDM_SERVER_NAME">
         <RegistrySearch Id="sigBroker"
                         Root="HKLM"
                         Key="SOFTWARE\Omnissa\Horizon\Agent\Configuration"
                         Name="InitialBroker"
                         Win64="yes"
                         Type="raw" />
      </Property>

      <Property Id="AGENT_CONFIGURATION_BROKER_REG">
         <RegistrySearch Id="sigAgentConfigurationBroker"
                         Root="HKLM"
                         Key="SOFTWARE\Omnissa\Horizon\Agent\Configuration"
                         Name="Broker"
                         Win64="yes"
                         Type="raw" />
      </Property>

      <Property Id="VDM_FORCE_DESKTOP_AGENT">
         <RegistrySearch Id="sigForceDesktopAgent"
                         Root="HKLM"
                         Key="SOFTWARE\Omnissa\Horizon\Agent\Configuration"
                         Name="ForceDesktopAgent"
                         Win64="yes"
                         Type="raw" />
      </Property>

      <!-- This registry can be used to force rds components installation
           on a VM with Win 10 RS4 build and above. Without this registry,
           rds components will be installed only on physical machines with
           Win 10 RS4 builds and above -->
      <Property Id="VDM_FORCE_RDS_INSTALL">
         <RegistrySearch Id="sigForceRdsInstall"
                         Root="HKLM"
                         Key="SOFTWARE\Omnissa\Horizon\Agent\Configuration"
                         Name="ForceRdsInstall"
                         Type="raw" />
      </Property>

      <!-- This is a place-holder for Windows 10 RS4 build number
           : https://www.microsoft.com/en-us/itpro/windows-10/release-information -->
      <Property Id="WINDOWS_10_RS4_BUILD_NUMBER"
                Value="17134"
                SuppressModularization="yes" />

      <Property Id="WINDOWS_BUILD_NUMBER">
         <RegistrySearch Id="regWindowsBuildNumber"
                         Root="HKLM"
                         Key="SOFTWARE\Microsoft\Windows NT\CurrentVersion"
                         Name="CurrentBuildNumber"
                         Type="raw" />
      </Property>

      <!-- This registry entry only exists on windows 10 and later, it returns #10 for windows 10,
           we use this because VersionNT is broken on win10 and later -->
      <Property Id="WINDOWS_MAJOR_VERSION">
         <RegistrySearch Id="regWindowsMajorVersion"
                         Root="HKLM"
                         Key="SOFTWARE\Microsoft\Windows NT\CurrentVersion"
                         Name="CurrentMajorVersionNumber"
                         Type="raw" />
      </Property>

      <!-- Microsoft retains the release Id for the Long-term servicing channel releases from when the
           OS was released. For e.g. 1607 for Windows Server 2016 & 1809 for Windows Server 2019 -->
      <Property Id="WINDOWS_2016_RELEASE_ID" Value="1607" />
      <Property Id="WINDOWS_2019_RELEASE_ID" Value="1809" />

      <!-- This propery stores the release Id of the current OS -->
      <Property Id="WINDOWS_RELEASE_ID">
         <RegistrySearch Id="regWindowsReleaseId"
                         Root="HKLM"
                         Key="SOFTWARE\Microsoft\Windows NT\CurrentVersion"
                         Name="ReleaseId"
                         Type="raw" />
      </Property>

      <!-- This propery stores update build revision (UBR) which is the OS revision-->
      <Property Id="WINDOWS_REVISION_NUMBER">
         <RegistrySearch Id="regWindowsRevisionNumber"
                         Root="HKLM"
                         Key="SOFTWARE\Microsoft\Windows NT\CurrentVersion"
                         Name="UBR"
                         Type="raw" />
      </Property>

      <!-- A temporary way to recognize windows 2016 server -->
      <Property Id="WINDOWS_INSTALLATION_TYPE">
         <RegistrySearch Id="regWindowsInstallationType"
                         Root="HKLM"
                         Key="SOFTWARE\Microsoft\Windows NT\CurrentVersion"
                         Name="InstallationType"
                         Type="raw" />
      </Property>

      <Property Id="REG_URL_FILTERING_ENABLED">
         <RegistrySearch Id="sigURL_FILTERING_ENABLED"
                         Root="HKLM"
                         Key="SOFTWARE\Omnissa\Horizon\Agent\Installer"
                         Name="URL_FILTERING_ENABLED"
                         Type="raw"
                         Win64="no" />
      </Property>

      <!-- This property shows who installed URL Filtering (Client or Agent) -->
      <Property Id="REG_URL_FILTERING_ENABLED_SIDE">
         <RegistrySearch Id="sigURL_FILTERING_ENABLED_SIDE"
                         Root="HKLM"
                         Key="SOFTWARE\Omnissa\Horizon\URLRedirection"
                         Name="Origin"
                         Type="raw" />
      </Property>

      <Property Id="REG_ENABLE_UNC_REDIRECTION">
         <RegistrySearch Id="sigENABLE_UNC_REDIRECTION"
                         Root="HKLM"
                         Key="SOFTWARE\Omnissa\Horizon\Agent\Installer"
                         Name="ENABLE_UNC_REDIRECTION"
                         Type="raw"
                         Win64="no" />
      </Property>

      <!-- This property shows who installed URL Filtering (Client or Agent) -->
      <Property Id="REG_ENABLE_UNC_REDIRECTION_SIDE">
         <RegistrySearch Id="sigENABLE_UNC_REDIRECTION_SIDE"
                         Root="HKLM"
                         Key="SOFTWARE\Omnissa\Horizon\UNCRedirection"
                         Name="Origin"
                         Type="raw" />
      </Property>

      <Property Id="REG_BUILD_NUMBER">
         <RegistrySearch Id="sigBuildNumber"
                         Root="HKLM"
                         Key="SOFTWARE\Omnissa\Horizon"
                         Name="BuildNumber"
                         Type="raw" />
      </Property>

      <Property Id="REG_VDM_LOGS_OVERRIDE">
         <RegistrySearch Id="vdmLogsOverride"
                         Root="HKLM"
                         Key="SOFTWARE\Omnissa\Horizon\Log"
                         Name="LogFileDirectory"
                         Type="raw" />
      </Property>

      <!-- Property Table -->
      <Property Id="APPSERVERTYPE" Value="jboss" />
      <Property Id="ARPCOMMENTS" Value="Version: $(var.ProductVersionString), Build: $(var.BUILD_NUMBER)" />
      <Property Id="ARPCONTACT" Value="Omnissa" />
      <Property Id="ARPNOREPAIR" Value="1" />
      <Property Id="ARPPRODUCTICON" Value="arp.ico" />
      <Property Id="ARPURLINFOABOUT" Value="!(loc.Url)" />
      <Property Id="AgreeToLicense" Value="No" />
      <Property Id="ApplicationUsers" Value="AllUsers" />
      <Property Id="BuildNumber" Value="$(var.BUILD_NUMBER)" />
      <Property Id="ConnectionServerLogin" Value="1" />
      <Property Id="CopyrightYears" Value="$(var.CopyrightYears)" />
      <Property Id="DWUSLINK" Value="CEAB17488EEB20C8CEACF0EFB98B978FAEEBA08F498C178FCEECC7B89EEBA74F9EAC87FF99AC" />
      <Property Id="DiskPrompt" Value="[1]" />
      <Property Id="DisplayNameCustom" Value="!(loc.IDS__DisplayName_Custom)" />
      <Property Id="DisplayNameMinimal" Value="!(loc.IDS__DisplayName_Minimal)" />
      <Property Id="DisplayNameTypical" Value="!(loc.IDS__DisplayName_Typical)" />
      <Property Id="Display_IsBitmapDlg" Value="1" />
      <Property Id="EnableRDSH3D" Value="0" />
      <Property Id="FEATURE_DESC_PCOIP_PHYSICAL" Value="!(loc.FEATURE_DESC_PCOIP_PHYSICAL)" />
      <Property Id="FRAMEWORK_CHANNEL_PORT" Value="#32111" />
      <Property Id="FWCHOICE" Value="1" />
      <Property Id="INSTALLLEVEL" Value="100" />
      <Property Id="INTEL_UNS_DESC" Value="!(loc.INTEL_UNS_DESC)" />
      <Property Id="InstallChoice" Value="AR" />
      <Property Id="InstallerBackupKey" Value="SOFTWARE\Omnissa\Horizon\Agent\Installer" />
      <Property Id="MSIRESTARTMANAGERCONTROL" Value="Disable" />
      <Property Id="MsiLogging" Value="voicewarmup" />
      <Property Id="NO_UNINSTALL_SVGA.515AA82B_247C_4806_8675_6E6A2FF62731" Value="1" /> <!-- Leave the svga drivers in place when uninstalling, otherwise the system is left with no svga driver. -->
      <Property Id="PIDTemplate" Value="12345&lt;###-%%%%%%%&gt;@@@@@" />
      <Property Id="PowerManagementPath" Value="SYSTEM\CurrentControlSet\Services\ACPI\Parameters" />
      <Property Id="PowerManagementPathBackup" Value="SOFTWARE\Omnissa\Horizon\Agent\Installer" />
      <Property Id="ProductVersionString" Value="$(var.ProductVersionString)" />
      <Property Id="ProgressType0" Value="install" />
      <Property Id="ProgressType1" Value="Installing" />
      <Property Id="ProgressType2" Value="installed" />
      <Property Id="ProgressType3" Value="installs" />
      <Property Id="REINSTALLMODE" Value="emus" />
      <Property Id="RdpVcBridgeRegPath" Value="SOFTWARE\Omnissa\Horizon\RdpVcBridge" />
      <Property Id="RdpVcBridgeRegPathWow" Value="SOFTWARE\Wow6432Node\Omnissa\Horizon\RdpVcBridge" />
      <Property Id="RebootYesNo" Value="Yes" />
      <Property Id="ReinstallModeText" Value="emus" />
      <Property Id="RestartManagerOption" Value="CloseRestart" />
      <Property Id="SHOWLAUNCHPROGRAM" Value="0" />
      <Property Id="SetupType" Value="Typical" />
      <Property Id="VDM_ConnectionServerRequired" Value="0" />
      <Property Id="VDM_SKIP_BROKER_REGISTRATION" Value="0" /> <!-- Use for unmanaged agents that do not need install-time broker registration. -->
      <Property Id="WsnmServiceDescription" Value="!(loc.WsnmServiceDescription)" />
      <Property Id="_IsMaintenance" Value="Modify" />
      <Property Id="IS_AGENT_DCT" Value="1" />
      <Property Id="AGENT_REGISTRY_MIGRATION_DONE" Value="0" />

      <?if $(var.EXCLUDE_SESSION_MONITOR)=0?>
         <!-- Temporary property to exclude installation of Session Monitor components. -->
         <Property Id="VDM_EXCLUDE_SESSION_MONITOR" Value="0" />
      <?endif?>

      <!-- Hidden Properties -->
      <Property Id="VDM_SERVER_RECOVERY_PWD" Hidden="yes" />
      <Property Id="VDM_SERVER_PASSWORD" Hidden="yes" />
      <Property Id="VM_AddLDAPMachineEntry" Hidden="yes" />

      <!-- Secure Properties -->
      <Property Id="NEWPRODUCTFOUND" Secure="yes" />
      <Property Id="OLDPRODUCTFOUND" Secure="yes" />
      <Property Id="OLDPRODUCT_ALPHA" Secure="yes" />
      <Property Id="OLDPRODUCT_BETA" Secure="yes" />
      <Property Id="OLDPRODUCT_BETA2" Secure="yes" />
      <Property Id="OLDPRODUCT_RC" Secure="yes" />
      <Property Id="AGENT_20BETA1" Secure="yes" />
      <Property Id="VPFOUND" Secure="yes" />
      <Property Id="VPUPGRADECODE" Secure="yes" />
      <Property Id="AFP52" Secure="yes" />
      <Property Id="WIX_ACCOUNT_ADMINISTRATORS.35BDBBE8_72E0_4EA1_AD92_DE934704FA87" Value="BUG_UNSET_PROPERTY" Secure="yes" />
      <Property Id="WIX_ACCOUNT_USERS.35BDBBE8_72E0_4EA1_AD92_DE934704FA87" Value="BUG_UNSET_PROPERTY" Secure="yes" />

      <!-- PropertyValue = HK*:Path::HK*:Path::excluded keys::excluded keys;HK*:Path::HK*:Path#overrideFlag -->
      <Property Id="REGKEYSTOREPLICATE" Value="HKLM:SOFTWARE\\VMware, Inc.\\VMware VDM::HKLM:SOFTWARE\\Omnissa\\Horizon::ScannerRedirection::KeyVaultCNG::SerialCOM::DeviceRedirectionCommon::PrintRedir;HKLM:SOFTWARE\\VMware, Inc.\VMware AppTap::HKLM:SOFTWARE\\Omnissa\\Horizon\\AppTap;HKLM:SOFTWARE\\VMware, Inc.\\VMware Blast::HKLM:SOFTWARE\\Omnissa\\Horizon\\Blast::Recording Agent;HKLM:SOFTWARE\\VMware, Inc.\\VMware FIDO2::HKLM:SOFTWARE\\Omnissa\\Horizon\\FIDO2;HKLM:SOFTWARE\\VMware, Inc.\\VMware GEOREDIR::HKLM:SOFTWARE\\Omnissa\\Horizon\\GEOREDIR;HKLM:SOFTWARE\\VMware, Inc.\\VMware HzMon::HKLM:SOFTWARE\\Omnissa\\Horizon\\HzMon::ipcincludelist;HKLM:SOFTWARE\\VMware, Inc.\\Horizon Instant Clone Agent::HKLM:SOFTWARE\\Omnissa\\Horizon\\Instant Clone Agent;HKLM:SOFTWARE\\VMware, Inc.\\VMware Logon Monitor::HKLM:SOFTWARE\\Omnissa\\Horizon\\Logon Monitor;HKLM:SOFTWARE\\VMware, Inc.\\VMware VDM\\PrintRedir::HKLM:SOFTWARE\\Omnissa\\Horizon\\PrintRedir::Plugins;HKLM:SOFTWARE\\VMware, Inc.\\VMware SdoSensorRedir::HKLM:SOFTWARE\\Omnissa\\Horizon\\SdoSensorRedir;HKLM:SOFTWARE\\VMware, Inc.\\VMware TSDR::HKLM:SOFTWARE\\Omnissa\\Horizon\\TSDR;HKLM:SOFTWARE\\VMware, Inc.\\VMware Unity::HKLM:SOFTWARE\\Omnissa\\Horizon\\Unity;HKLM:SOFTWARE\\VMware, Inc.\\VMware UsbRedirection::HKLM:SOFTWARE\\Omnissa\\Horizon\\UsbRedirection;HKLM:SOFTWARE\\VMware, Inc.\\Installer::HKLM:SOFTWARE\\Omnissa\\Horizon\\Installer;HKLM:SOFTWARE\\VMware, Inc.\\VMware EUC::HKLM:SOFTWARE\\Omnissa\\Common;HKLM:SOFTWARE\\VMware, Inc.\\VMware VDM\\ScannerRedirection::HKLM:SOFTWARE\\Omnissa\\ScannerRedirection;HKLM:SOFTWARE\\VMware, Inc.\\VMware VDM\\SerialCOM::HKLM:SOFTWARE\\Omnissa\\SerialCOM;HKLM:SOFTWARE\\VMware, Inc.\\VMware Remote Experience Generic Service::HKLM:SOFTWARE\\Omnissa\\Horizon\\Remote Experience Generic Service;HKLM:SOFTWARE\\VMware, Inc.\\VMware SDRTrans::HKLM:SOFTWARE\\Omnissa\\Horizon\\SDRTrans;HKLM:SOFTWARE\\VMware, Inc.\\VMware TSMMR::HKLM:SOFTWARE\\Omnissa\\Horizon\\TSMMR;HKLM:SOFTWARE\\VMware, Inc.\\VMwareSetResolutionSDK::HKLM:SOFTWARE\\Omnissa\\Horizon\\SetResolutionSDK;HKLM:SOFTWARE\\VMware, Inc.\\VMware VDM\\DeviceRedirectionCommon::HKLM:SOFTWARE\\Omnissa\\DeviceRedirectionCommon;HKLM:SOFTWARE\\VMware, Inc.\\Horizon Instant Clone Agent::HKLM:SOFTWARE\\Omnissa\\Instant Clone Agent;HKLM:SOFTWARE\\VMware, Inc.\\VMware VDM\\Agent\\Update::HKLM:SOFTWARE\\Omnissa\\Horizon\\Agent\\Update;HKLM:SOFTWARE\\VMware, Inc.\\VMware VDM\\DaaS Agent::HKLM:SOFTWARE\\Omnissa\\Horizon\\DaaS Agent;#1" Secure="yes" />

      <!-- Installshield properties -->
      <Property Id="InstallShieldTempProp" Value="0" />
      <Property Id="IS_COMPLUS_PROGRESSTEXT_COST" Value="!(loc.IDS_COMPLUS_PROGRESSTEXT_COST)" />
      <Property Id="IS_COMPLUS_PROGRESSTEXT_INSTALL" Value="!(loc.IDS_COMPLUS_PROGRESSTEXT_INSTALL)" />
      <Property Id="IS_COMPLUS_PROGRESSTEXT_UNINSTALL" Value="!(loc.IDS_COMPLUS_PROGRESSTEXT_UNINSTALL)" />
      <Property Id="IS_PROGMSG_XML_COSTING" Value="!(loc.IDS_PROGMSG_XML_COSTING)" />
      <Property Id="IS_PROGMSG_XML_CREATE_FILE" Value="!(loc.IDS_PROGMSG_XML_CREATE_FILE)" />
      <Property Id="IS_PROGMSG_XML_FILES" Value="!(loc.IDS_PROGMSG_XML_FILES)" />
      <Property Id="IS_PROGMSG_XML_REMOVE_FILE" Value="!(loc.IDS_PROGMSG_XML_REMOVE_FILE)" />
      <Property Id="IS_PROGMSG_XML_ROLLBACK_FILES" Value="!(loc.IDS_PROGMSG_XML_ROLLBACK_FILES)" />
      <Property Id="IS_PROGMSG_XML_UPDATE_FILE" Value="!(loc.IDS_PROGMSG_XML_UPDATE_FILE)" />
      <Property Id="PROGMSG_IIS_CREATEAPPPOOL" Value="!(loc.IDS_PROGMSG_IIS_CREATEAPPPOOL)" />
      <Property Id="PROGMSG_IIS_CREATEAPPPOOLS" Value="!(loc.IDS_PROGMSG_IIS_CREATEAPPPOOLS)" />
      <Property Id="PROGMSG_IIS_CREATEVROOT" Value="!(loc.IDS_PROGMSG_IIS_CREATEVROOT)" />
      <Property Id="PROGMSG_IIS_CREATEVROOTS" Value="!(loc.IDS_PROGMSG_IIS_CREATEVROOTS)" />
      <Property Id="PROGMSG_IIS_CREATEWEBSERVICEEXTENSION" Value="!(loc.IDS_PROGMSG_IIS_CREATEWEBSERVICEEXTENSION)" />
      <Property Id="PROGMSG_IIS_CREATEWEBSERVICEEXTENSIONS" Value="!(loc.IDS_PROGMSG_IIS_CREATEWEBSERVICEEXTENSIONS)" />
      <Property Id="PROGMSG_IIS_EXTRACT" Value="!(loc.IDS_PROGMSG_IIS_EXTRACT)" />
      <Property Id="PROGMSG_IIS_EXTRACTDONE" Value="!(loc.IDS_PROGMSG_IIS_EXTRACTDONE)" />
      <Property Id="PROGMSG_IIS_EXTRACTDONEz" Value="!(loc.IDS_PROGMSG_IIS_EXTRACTDONE)" />
      <Property Id="PROGMSG_IIS_EXTRACTzDONE" Value="!(loc.IDS_PROGMSG_IIS_EXTRACTDONE)" />
      <Property Id="PROGMSG_IIS_REMOVEAPPPOOL" Value="!(loc.IDS_PROGMSG_IIS_REMOVEAPPPOOL)" />
      <Property Id="PROGMSG_IIS_REMOVEAPPPOOLS" Value="!(loc.IDS_PROGMSG_IIS_REMOVEAPPPOOLS)" />
      <Property Id="PROGMSG_IIS_REMOVESITE" Value="!(loc.IDS_PROGMSG_IIS_REMOVESITE)" />
      <Property Id="PROGMSG_IIS_REMOVEVROOT" Value="!(loc.IDS_PROGMSG_IIS_REMOVEVROOT)" />
      <Property Id="PROGMSG_IIS_REMOVEVROOTS" Value="!(loc.IDS_PROGMSG_IIS_REMOVEVROOTS)" />
      <Property Id="PROGMSG_IIS_REMOVEWEBSERVICEEXTENSION" Value="!(loc.IDS_PROGMSG_IIS_REMOVEWEBSERVICEEXTENSION)" />
      <Property Id="PROGMSG_IIS_REMOVEWEBSERVICEEXTENSIONS" Value="!(loc.IDS_PROGMSG_IIS_REMOVEWEBSERVICEEXTENSIONS)" />
      <Property Id="PROGMSG_IIS_ROLLBACKAPPPOOLS" Value="!(loc.IDS_PROGMSG_IIS_ROLLBACKAPPPOOLS)" />
      <Property Id="PROGMSG_IIS_ROLLBACKVROOTS" Value="!(loc.IDS_PROGMSG_IIS_ROLLBACKVROOTS)" />
      <Property Id="PROGMSG_IIS_ROLLBACKWEBSERVICEEXTENSIONS" Value="!(loc.IDS_PROGMSG_IIS_ROLLBACKWEBSERVICEEXTENSIONS)" />

      <!-- L10n properties for merge module services -->
      <Property Id="L10N_PCOIPSG_DISPLAY_NAME" Value="!(loc.IDS_PCOIPSG_DISPLAY_NAME)" />
      <Property Id="L10N_PCOIPSG_DESCRIPTION" Value="!(loc.IDS_PCOIPSG_DESCRIPTION)" />

      <Property Id="L10N_WSNM_SERVICE_DISPLAY_NAME" Value="!(loc.IDS_WSNM_SERVICE_DISPLAY_NAME)" />
      <Property Id="L10N_WSNM_SERVICE_DESCRIPTION" Value="!(loc.WsnmServiceDescription)" />

      <Property Id="L10N_WSSH_SERVICE_DISPLAY_NAME" Value="!(loc.IDS_WSSH_SERVICE_DISPLAY_NAME)" />
      <Property Id="L10N_WSSH_SERVICE_DESCRIPTION" Value="!(loc.IDS_WSSH_SERVICE_DESCRIPTION)" />

      <Property Id="L10N_VMLM_SERVICE_DISPLAY_NAME" Value="!(loc.IDS_VMLM_SERVICE_DISPLAY_NAME)" />
      <Property Id="L10N_VMLM_SERVICE_DESCRIPTION" Value="!(loc.IDS_VMLM_SERVICE_DESCRIPTION)" />

      <Property Id="L10N_HZMON_SERVICE_DISPLAY_NAME" Value="!(loc.IDS_HZMON_SERVICE_DISPLAY_NAME)" />
      <Property Id="L10N_HZMON_SERVICE_DESCRIPTION" Value="!(loc.IDS_HZMON_SERVICE_DESCRIPTION)" />
   </Product>
</Wix>
