/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * vvcTestMimic.cpp --
 *
 *      This file generates VVC structures created and initialized only to serve the purpose of the
 *      unit test, nothing more.
 *
 */


#include "vvclibInt.h"
#include "vvcChannel.h"


/*
 * VvcMimicCreateSession --
 *
 *      Mimic VVCLIB_OpenSession to return a partially initialized session.
 *
 */

VvcSession *
VvcMimicCreateSession(VvcInstanceHandle instanceHandle, // IN
                      VvcSessionHandle *sessionHandle)  // IN/OUT
{
   VvcStatus status;
   VvcTransptBackend be = {0};
   VvcSessionEvents ev = {0};
   VvcSessionParams params = {0};
   VvcInstance *instance = NULL;
   VvcSession *session = NULL;
   uint32 sid = 42;

   if (!VVC_GET_INSTANCE(instanceHandle, instance)) {
      return NULL;
   }

   ASSERT(!strcmp(instance->name, "TestVvcUTMain"));

   session = VvcFindSessionFromId(instance, sid);
   if (session) {
      VvcDebug("%s: Found session", __FUNCTION__);
      goto out;
   }

   status = VVCLIB_OpenSession(instanceHandle, &be, sid, 0, params, &ev, NULL, sessionHandle);
   ASSERT(status == VVC_STATUS_SUCCESS);

   session = VvcFindSessionFromId(instance, sid);

out:
   if (session) {
      session->state = VvcSessionEstablished;
      session->negotiatedRawChannelSupport = TRUE;
      session->negotiatedVerMajor = 4;
      session->tcpBweNegotiated = TRUE;
      session->rawChannelType = VVC_RAW_CHAN_TCP;
   }

   VVC_YIELD_INSTANCE(instance);

   return session;
}


void
VvcMimicCloseListener(VvcListenerHandle listener) // IN
{
   VVCLIB_CloseListener(listener);
}


/*
 * VvcMimicCloseSession --
 *
 *      Mimic VVCLIB_CloseSession.
 *      Minimal for now in order to reuse session for multiple  tests.
 *
 */

void
VvcMimicCloseSession(VvcSession *session)
{
   VVC_RELEASE_SESSION(session, VvcTagFindSessionFromId);
}


static void
TestOnConnectListener(char *name,                 // IN
                      VvcListenerHandle listener, // IN
                      void *connCookie,           // IN
                      uint32 connCaps,            // IN
                      int32 sessionId,            // IN
                      void *clientData)           // IN
{

   Log("Listener connected");
}


static void
TestOnCloseListener(VvcListenerHandle listener, // IN
                    void *clientData)           // IN

{

   Log("Listener closed");
}


/*
 * VvcMimicCreateListener --
 *
 *      Mimic create/activate listener.
 *
 */

void
VvcMimicCreateListener(char *name,                  // IN
                       VvcListenerHandle *listener) // OUT
{
   VvcStatus status;
   VvcListenerEvents listenerEvents;
   int sid = 42;

   memset(&listenerEvents, 0, sizeof listenerEvents);
   listenerEvents.onConnect = TestOnConnectListener;
   listenerEvents.onClose = TestOnCloseListener;

   status = VVCLIB_CreateListener(VVC_PLUGIN_ID_CORE_PROTOCOL, sid, name, &listenerEvents, NULL,
                                  listener);
   ASSERT(status == VVC_STATUS_SUCCESS);
   status = VVCLIB_ActivateListener(*listener);
   ASSERT(status == VVC_STATUS_SUCCESS);
}


static void
TestOnOpenChannel(VvcChannelHandle channelHandle, // IN
                  VvcStatus status,               // IN
                  uint8 *initialData,             // IN
                  size_t initialDataLen,          // IN
                  void *clientData)               // IN
{
   Log("Channel opened");
}

static void
TestOnCloseChannel(VvcChannelHandle channelHandle, // IN
                   VvcCloseChannelReason reason,   // IN
                   void *clientData)               // IN
{
   Log("Channel closed");
}


/*
 * VvcMimicCreateChannel --
 *
 *      Mimic VVCLIB_OpenChannel to return a partially initialized channel. There is no peer to
 *      create an actual channel, so this simply returns the locally created channel.
 *
 */

VvcChannel *
VvcMimicCreateChannel(VvcListenerHandle listener,     // IN
                      VvcSessionHandle sessionHandle, // IN
                      VvcSession *session,            // IN
                      int flags)                      // IN
{
   VvcStatus status;
   VvcChannelEvents events;
   uint32 channelId;

   events.onOpen = TestOnOpenChannel;
   events.onClose = TestOnCloseChannel;

   status = VVCLIB_OpenChannel(listener, session->instance->name, sessionHandle, &events,
                               VVC_LATENCY_NORMAL, 0, flags, NULL, 0, NULL, &channelId);
   ASSERT(status == VVC_STATUS_SUCCESS);

   return VvcFindChannelFromSession(session, session->instance->name, 0);
}


/*
 * VvcMimicRemoveChannel --
 *
 *      Mimic VVCLIB_CloseChannel with synchronous cleanup of the channel so it no longer exists.
 *
 */

void
VvcMimicRemoveChannel(VvcChannel *channel)
{
   VvcSession *session = channel->session;
   VVC_RELEASE_CHANNEL(channel, VvcTagFindChannelFromSession);
   VVC_RELEASE_CHANNEL(channel, VvcTagCreation);
   VVCLIB_CloseChannel(TOKEN_HANDLE(channel), VvcCloseChannelNormal);
   VERIFY(VvcFindChannelFromSession(session, session->instance->name, 0) == NULL);
}


/*
 * VvcTestIsRawChannel --
 *
 *      Check if channel created was raw
 *
 */
Bool
VvcTestIsRawChannel(VvcChannel *channel)
{
   if (channel->flags & VVC_CHANNEL_RAW) {
      ASSERT(channel->rawCtx);
      return TRUE;
   }

   return FALSE;
}
