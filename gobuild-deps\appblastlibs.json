{"cayman_amd_rapidfire_sdk": {"branch": "vmware-master", "buildtypes": {"beta": "release", "obj": "release", "opt": "release", "release": "release"}, "change": "cbb634822ce6952b847fb6eb1c273001e1f12a8e", "files": {"Windows": ["publish/RapidFire/include/.*", "publish/RapidFireServer/include/.*"]}}, "cayman_binaryen": {"branch": "vmware-main", "buildtypes": {"beta": "release", "obj": "release", "opt": "release", "release": "release"}, "change": "352c0e2e8b61a64e419b0ef4c41a3208d5260309", "hosttypes": {"Linux": "linux64", "Windows": "windows"}}, "cayman_boost": {"branch": "vmware-latest", "buildtypes": {"beta": "release", "obj": "obj", "opt": "release", "release": "release"}, "change": "e583643e6814890777e2ae43db780063c270d689", "hosttypes": {"Windows": "windows-2016-vs2015-U3"}}, "cayman_emscripten": {"branch": "vmware-emscripten-3.1.30", "buildtypes": {"beta": "release", "obj": "release", "opt": "release", "release": "release"}, "change": "20ecad6b2733c4f6ea877f1c60495bfce57f0b4a", "hosttypes": {"Linux": "linux64", "Windows": "linux64"}}, "cayman_esx_toolchain_gcc12": {"branch": "vmware-gcc12", "buildtypes": {"beta": "release", "obj": "release", "opt": "release", "release": "release"}, "change": "0ff42a33342eea3b90daeb4577e41614c973b15b", "hosttypes": {"Linux": "linux-centos8-fw"}, "product": "cayman_esx_toolchain"}, "cayman_intel_sdk": {"branch": "vmware-master", "buildtypes": {"beta": "beta", "obj": "obj", "opt": "release", "release": "release"}, "change": "5077e5602284c35566424b00f080e98fe3cd35a3", "hosttypes": {"Windows": "windows2016-clean"}}, "cayman_llvm": {"branch": "release_16.0.3+vmware", "buildtypes": {"beta": "release", "obj": "release", "opt": "release", "release": "release"}, "change": "80d09e2d6fce3585f35f21d5a26ef0f19e4b9ee0", "hosttypes": {"Linux": "linux-centos8-fw", "Windows": "windows2019-clean"}}, "cayman_msvc_drivers": {"branch": "vc140-toolset-latest@vs2019u11+sdk10_2104", "buildtypes": {"beta": "release", "obj": "release", "release": "release"}, "change": "fd69645b7c793aa8be05f3b0b7fbc8d9bb202dab", "hosttypes": {"Windows": "windows2016-clean"}}, "cayman_nodejs": {"branch": "vmware-prebuilt-node-16.x", "buildtypes": {"beta": "release", "obj": "release", "opt": "release", "release": "release"}, "change": "160475f5ca9569c765b4303b979ac0cd36b6337d", "hosttypes": {"Linux": "linux-centos72-gc32", "Windows": "windows2016-clean"}}, "libxdr_openbsd": {"branch": "libxdr_openbsd-4.3", "buildtypes": {"beta": "beta", "obj": "obj", "opt": "release", "release": "release"}, "change": "ad709351c53d9462e15c647064458ba01ce54842", "hosttypes": {"Linux": "linux64", "Windows": "windows2016-clean"}}, "webmks": {"branch": "master", "buildtypes": {"beta": "beta", "obj": "obj", "opt": "release", "release": "release"}, "change": "176cfc709518db8332bad975c0e4fda59dea7431", "hosttypes": {"Windows": "linux-centos8"}}, "vgauth-sdk": {"branch": "main", "buildtypes": {"beta": "beta", "obj": "obj", "opt": "release", "release": "release"}, "change": 12583185, "hosttypes": {"Windows": "windows2016-clean"}}}