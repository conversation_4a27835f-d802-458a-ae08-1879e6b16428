# Copyright (c) Omnissa, LLC. All rights reserved.
# This product is protected by copyright and intellectual property laws in the
# United States and other countries as well as by international treaties.
# -- Omnissa Restricted

import vmware
import os

vncName = "viewClient"
binaryName = "horizon-protocol"
hlslNodeName = "viewClientWin32HLSL"
cudaNodeName = "viewClientWin32Cuda"

env = vmware.DefaultEnvironment()
env.Prepend(
    CPPDEFINES={
        "USERLEVEL": None,
    },
    CPPPATH=[
        "#bora/apps/cedar/include",
        "#bora/apps/rde/blast/vncReplay/include",
        "#bora/apps/rde/rdeLibs/include",
        "#bora/apps/rde/vdpservice/public",
        "#bora/apps/rde/viewClient",
        "#bora/apps/lib/hznprotect",
        "#bora/lib/blastCodec",
        "#bora/lib/public",
        "#bora/lib/viewControl",
        "#bora/lib/vnc",
        "#bora/lib/vncConnectionManager",
        "#bora/public",
        vmware.HeaderDirectory("guestrpc-include", host="generic"),
        vmware.HeaderDirectory("vnc"),
    ],
)

env.LoadTool(
    [
        "ffmpeg",
        "libjpeg",
        "libogg",
        "libopus",
        "libpng",
        "libspeexdsp",
        "libssl",
        "libyuv",
        "libz",
        "vm-product",
    ]
)

viewClientSubdirs = [
    "apps/rde/viewClient",
]

if vmware.Host().IsMac():
    viewClientSubdirs += [
        "apps/rde/viewClient/macos",
    ]
    env.Append(
        CCFLAGS=["-fstack-protector"],
        CPPPATH=[
            "#bora/apps/lib",
        ],
        FRAMEWORKS=[
            "ApplicationServices",
            "AudioToolbox",
            "AudioUnit",
            "Carbon",
            "Cocoa",
            "CoreAudio",
            "CoreFoundation",
            "CoreMedia",
            "DiskArbitration",
            "Foundation",
            "IOKit",
            "IOSurface",
            "Metal",
            "MetalKit",
            "Quartz",
            "Security",
            "VideoToolbox",
        ],
        LIBS=[
            "png",
            "z",
        ],
        STATICLIBS=[
            "jpeg",
            "opus",
            "speexdsp",
            "ogg",
            "yuv",
        ],
    )

if vmware.Host().IsLinux():
    viewClientSubdirs += [
        "apps/rde/viewClient/linux",
        "apps/rde/viewClient/linux/vaapi2",
    ]
    env.LoadTool(
        [
            "alsa",
            "libboost",
            "libdbus",
            "libiconv",
            "libsndfile",
            "xorg",
            "libvdpau",
            "libffi",
            "libglib2",
            "libpcre2",
            "pulseaudio",
        ]
    )

    if vmware.Host().IsARM():
        env.LoadTool(["raspberrypi"])
    else:
        env.LoadTool(["libx264"])

    env.Append(
        LINKFLAGS=[
            "-Wl,-rpath -Wl,'$$ORIGIN'/../..",
            "-Wl,-rpath -Wl,'$$ORIGIN'/../usb",
            "-Wl,-rpath -Wl,'$$ORIGIN'/../html5mmr",
            "-Wl,-rpath -Wl,'$$ORIGIN'/../../../pcoip/vchan_plugins",
            "-Wl,-enable-new-dtags",
        ],
        STATICLIBS=[
            "jpeg",
            "opus",
            "speexdsp",
            "ogg",
            "yuv",
        ],
        LIBS=[
            "dbus-1",
            "dl",
            "m",
            "pthread",
            "pulse",
            "stdc++",
            "X11",
            "Xcursor",
            "Xdamage",
            "Xext",
            "Xi",
            "Xinerama",
            "xkbfile",
            "Xrandr",
            "Xrender",
            "Xtst",
            "z",
        ],
    )

    if vmware.BuildType() in ["beta", "obj", "opt"]:
        # stack unwinding code in Util_Backtrace still depends on these
        env.Append(CCFLAGS=["-fno-omit-frame-pointer"])
        env.Append(LINKFLAGS=["-Wl,--export-dynamic"])

        # Use version script to control which symbols to export
        verScript = vmware.FileAbsPath("#bora/apps/rde/viewClient/debug.version")
        env.Append(LINKFLAGS=["-Wl,--version-script=" + verScript])

if vmware.Host().IsWindows():
    viewClientSubdirs += [
        "apps/rde/viewClient/win32",
    ]
    env.LoadTool(
        [
            "atlmfc",
            "horizonUtils",
            "intel_sdk",
            "libx264",
            "libxdr",
            "msvcrt",
            "nvidia_sdk",
            "nvcodecheaders",
        ]
    )
    env.Append(
        CPPDEFINES={
            "VMX86_IMPORT_DLLDATA": None,
        },
        CPPPATH=[
            vmware.HeaderDirectory(hlslNodeName, host="win64"),
            vmware.HeaderDirectory(cudaNodeName, host="win64"),
            "#bora/apps/rde/viewClient/win32",
            "#bora/apps/rde/whfbRedirection/include",
            "#bora/modules/hznprotect",
        ],
        LINKFLAGS=[
            "-delayload:d3d11.dll",
            "-delayload:dcomp.dll",
            "-delayload:ddraw.dll",
            "-delayload:dxgi.dll",
            "-entry:wWinMainCRTStartup",
        ],
        LIBS=[
            "advapi32.lib",
            "bcrypt.lib",
            "crypt32.lib",
            "d3d11.lib",
            "dcomp.lib",
            "ddraw.lib",
            "delayimp.lib",
            "dxgi.lib",
            "dxguid.lib",
            "gdi32.lib",
            "glu32.lib",
            "imm32.lib",
            "iphlpapi.lib",
            "kernel32.lib",
            "mmdevapi.lib",
            "msimg32.lib",
            "netapi32.lib",
            "ntdll.lib",
            "oldnames.lib",
            "ole32.lib",
            "oleaut32.lib",
            "qwave.lib",
            "setupapi.lib",
            "shell32.lib",
            "shlwapi.lib",
            "tdh.lib",
            "user32.lib",
            "uuid.lib",
            "wbemuuid.lib",
            "winmm.lib",
            "ws2_32.lib",
            "Wtsapi32.lib",
        ],
        STATICLIBS=[
            "yuv.lib",
        ],
    )

    # This should be removed if we switch to use sse2neon instead of
    # Microsoft's emulation of Intel SSE intrinsics on win-arm64ec.
    if vmware.Host().IsARM64EC():
        env.Append(LIBS=["softintrin.lib"])

# Libraries required only for imgui, which is not enabled in release builds
if not vmware.BuildType() in ["release"]:
    env.LoadTool(
        [
            "imgui",
            "implot",
            "protobuf-c-3",
        ]
    )
    if vmware.Host().IsWindows():
        env.Append(LIBS=["d3dcompiler.lib"])
        if vmware.Host().Is64Bit():
            env.LoadTool(
                [
                    "presentmon",
                ]
            )
    if vmware.Host().IsMac():
        env.Append(LIBS=["protobuf-c"])

e = vmware.Executable(binaryName, env=env)
vncLibs = [
    "blastCodec",
    "sound",
    "soundlib",
    "viewControl",
    "vnc",
    "win32tsf",
]

if vmware.Host().IsMac():
    vncLibs += [
        "location",
    ]
    e.addStaticLibs("applibs", ["macOS/audioUtils"])
    e.addStaticLibs("applibs", ["objc/vmFoundation"])

if vmware.Host().IsWindows():
    e.GetEnv().AddRdeVersionInfo(e, binaryName + ".exe", "Horizon Protocol")
    vncLibs += [
        "kbdlayoutid",
    ]
    e.addSharedLibs(
        [
            "hznprotect",
        ]
    )

e.addSharedLibs(
    [
        "omnissabaselib",
    ]
)
subdirs = e.addStaticLibs("vmlibs", vncLibs)
e.addSubdirs(viewClientSubdirs)
e.addGlobalStaticLibs(
    [
        "vncReplay",
    ]
)

node = e.createProgramNode(basename=binaryName)

if vmware.Host().IsLinux():
    env.AddAlsaDeps(subdirs, node)

if vmware.Host().IsWindows():
    hlslNodes = vmware.LookupNode(hlslNodeName, host="win64")
    env.Depends(node, hlslNodes)

env["VNCLIBS"] = vncLibs
vmware.RegisterEnv(vncName, env)
vmware.RegisterNode(node, vncName)
vmware.Alias(vncName + "-build", node)
