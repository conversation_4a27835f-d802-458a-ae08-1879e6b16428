/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 * vvcMultiAsockBackend.c
 *
 * Implementations of TransportBe functions when -
 * VvcSession::transportBe::flags includes a MULTI_PROTOCOL flag and
 * AsyncSocket(s) have been pushed down to VVC via VVCLIB_AddAsockBackend().
 *
 * The multi-protocol "logic" is abstracted in a function call
 * "VvcGetAsockBackend()".
 * Every transport implementation would do VvcGetAsockBackend() and use
 * the returned AsockBackend in invoking subsequent AsyncSocket_* functions.
 *
 * XXX: For Multi-Protocol: VvcGetAsockBackend() will be expanded in future.
 */


#include "preference.h"
#include "str.h"
#include "util.h"
#include "hostinfo.h"
#include "hashMap.h"

#include "vvc.h"
#include "vvclib.h"
#include "vvcCtrl.h"
#include "vvcDebug.h"
#include "vvclibInt.h"
#include "vvcLog.h"
#include "vvcSendQueue.h"
#include "vvcChannel.h"

#include "asyncsocket.h"
#include "asyncSocketBase.h"
#include "asyncBweSocket.h"

#include "vvcMultiAsockBackend.h"
#include "vvcMultiAsockBackendQoS.h"
#include "vvcBandwidthDetectionAPI.h"
#include "vvcDataTransportSwitch.h"

typedef struct _VvcAsockBeCbContext {
   VvcAsockBackend *asockBe;
   MXUserRecLock *asockLock;
   void *sccBatcher;
} VvcAsockBeCbContext;


typedef enum { VvcSessionSendByteCounter, VvcSessionRecvByteCounter } VvcByteCounterType;


static void VvcInvokeSessionTransportSendComplete(VvcSession *session, void *sccBatcher, uint8 *buf,
                                                  size_t bufLen, VvcStatus status);

static void VvcAsockBackendDestroy(VvcAsockBackend *asockBe);
static void VvcAsockBackendClose(VvcAsockBackend *asockBe);
static void vvcMultiAsockBackendErrorCb(int error, AsyncSocket *asock, void *clientData);

static void VvcIncrementSessionByteCounters(VvcSession *session, int asockID, int bytes,
                                            VvcByteCounterType counterType);

static void VvcAsockBackendInstallKeepaliveTimer(VvcAsockBackend *asockBe);
static void VvcAsockBackendUninstallKeepaliveTimer(VvcAsockBackend *asockBe);

void VvcKeepaliveTimeoutCb(void *clientData);
VvcStatus VvcMultiAsockBackendStopKeepaliveTimeout(VvcSession *session);

VvcStatus VvcStopCtrlKeepAlive(VvcSession *session);
void VvcDispatchCtrlKeepAlive(void *clientData);
void VvcCtrlKeepAliveSend(VvcAsockBackend *asockBe);
void VvcCtrlKeepAliveSendCompleteCb(void *buf, int len, AsyncSocket *asock, void *cbData);

/* With MTP disabled, the temp buffer used in recv done on
 * the Non-Active AsockBackend.
 */
uint8 nonActiveAsockBackendTmpRecvBuf[256] = {0};

#define VVC_CTRL_KEEP_ALIVE_PERIOD_MS 60000 // 60 sec
#define VVC_CTRL_KEEP_ALIVE_DUMMYBUF_LEN 1
static uint8 ctrlKeepAliveDummyBuf[VVC_CTRL_KEEP_ALIVE_DUMMYBUF_LEN] = {0};


/*
 *----------------------------------------------------------------------------
 *
 * VvcIncrementSessionByteCounters --
 *
 *      Increment the recv|send byte counters for a session. This function
 *      should be called for both asock and non-asock backends.
 *
 *      - For an non-asock backend, caller should pass 'INVALID_ASOCKID' as the
 *        'asockID' param. And only the session wide counter is incremented.
 *
 *      - For an asock backend, caller should pass a valid asockID. And in
 *        addition to session wide counter, the per-protocol counters are also
 *        incremented.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      Session's receive or send counters are incremented.
 *
 *----------------------------------------------------------------------------
 */

void
VvcIncrementSessionByteCounters(VvcSession *session, int asockID, int bytes,
                                VvcByteCounterType counterType)
{
   Bool isSessionLocked = FALSE;
   VvcAsockBackend *asockBe;
   uint64 *counterPtr = NULL;
   uint64 *tcpCounterPtr = NULL;
   uint64 *udpCounterPtr = NULL;

   if (!session || bytes == 0) {
      return;
   }

   isSessionLocked = ISLOCKED_SESSION(session);
   if (!isSessionLocked) {
      LOCK_SESSION(session);
   }

   // Map counterType to corresponding counter members in session
   switch (counterType) {
   case VvcSessionSendByteCounter:
      counterPtr = &session->sentBytes;
      tcpCounterPtr = &session->tcpSentBytes;
      udpCounterPtr = &session->udpSentBytes;
      break;
   case VvcSessionRecvByteCounter:
      counterPtr = &session->recvedBytes;
      tcpCounterPtr = &session->tcpRecvedBytes;
      udpCounterPtr = &session->udpRecvedBytes;
      break;
   }

   // Increment session-wide Send|Recv counter
   *counterPtr += bytes;

   // Non-asock backends don't have per-protocol counters.
   if (asockID == INVALID_ASOCKID) {
      goto out;
   }

   asockBe = VvcGetAsockBackendFromAsockID(session, asockID);
   if (asockBe == NULL) {
      goto out;
   }

   // Now increment per-protocol counters
   if (asockBe->isControlAsock) {
      // TCP bytes
      *tcpCounterPtr += bytes;
   } else {
      // UDP bytes
      *udpCounterPtr += bytes;
   }

   VVC_RELEASE_ASOCKBE(asockBe, VvcTagAsockBeGeneric);
   asockBe = NULL;

out:
   if (!isSessionLocked) {
      UNLOCK_SESSION(session);
   }
}


/*
 *----------------------------------------------------------------------------
 *
 * VvcIncrementSessionSentProtocolBytes --
 *
 *      Increment send byte counters for a session. See
 *      VvcIncrementSessionByteCounters for details on what the 'asockID'
 *      parameter should be.

 *      Also increment BWE (VVC-original) counters and invoke BWE's
 *      "OnBytesSent" function if needed.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      Session's send counters are incremented.
 *
 *----------------------------------------------------------------------------
 */

void
VvcIncrementSessionSentProtocolBytes(VvcSession *session, int asockID, int len)
{
   Bool isSessionLocked = ISLOCKED_SESSION(session);
   VvcAsockBackend *asockBe;
   Bool incrementBweSentBytes = FALSE;

   if (len == 0) {
      return;
   }

   if (!isSessionLocked) {
      LOCK_SESSION(session);
   }

   VvcIncrementSessionByteCounters(session, asockID, len, VvcSessionSendByteCounter);

   if (asockID == INVALID_ASOCKID) {
      incrementBweSentBytes = !session->disableBandwidthDetection;
   } else {
      ASSERT(!session->disableBandwidthDetection);

      asockBe = VvcGetAsockBackendFromAsockID(session, asockID);

      incrementBweSentBytes = (asockBe != NULL && !asockBe->isEndToEndConnection);

      if (asockBe) {
         VVC_RELEASE_ASOCKBE(asockBe, VvcTagAsockBeGeneric);
         asockBe = NULL;
      }
   }

   /* Increment BWE sent bytes */
   if (incrementBweSentBytes) {
      session->bweSentBytes += len;
      ASSERT(session->bweAckedBytes <= session->bweSentBytes);
      VvcBandwidthDetection_OnBytesSent(session->bwDetection, len);
   }

   if (!isSessionLocked) {
      UNLOCK_SESSION(session);
   }
}


/*
 *----------------------------------------------------------------------------
 *
 * VvcIncrementSessionRecvedProtocolBytes --
 *
 *      Increment the received byte counters for a session. See
 *      VvcIncrementSessionByteCounters for details.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      Session's receive counters are incremented.
 *
 *----------------------------------------------------------------------------
 */

void
VvcIncrementSessionRecvedProtocolBytes(VvcSession *session, int asockID, int len)
{
   VvcIncrementSessionByteCounters(session, asockID, len, VvcSessionRecvByteCounter);
}


/*
 *----------------------------------------------------------------------------
 *
 * VvcIsCancelSafeSend --
 *
 *      If CANCEL_SAFE_SEND flag is specified then we need to notify
 *      VVC in SendCompleteCb.
 *
 * Results:
 *      Returns TRUE if cancel safe send is required, FALSE otherwise
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

Bool
VvcIsCancelSafeSend(VvcSession *session) // IN
{
   Bool isCancelSafeSend = TRUE;
   Bool isSessionLocked = FALSE;

   if (!session) {
      return isCancelSafeSend;
   }

   isSessionLocked = ISLOCKED_SESSION(session);

   if (!isSessionLocked) {
      LOCK_SESSION(session);
   }

   isCancelSafeSend = session->transportBe.flags & VVC_TRANSPORT_BE_ENABLE_FORCE_CANCEL_SAFE_IO;

   if (!isSessionLocked) {
      UNLOCK_SESSION(session);
   }

   return isCancelSafeSend;
}


/*
 *----------------------------------------------------------------------------
 *
 * VvcMultiAsockBackendIsSocketLocked --
 *
 *      Returns TRUE if the SocketLock is Locked.
 *
 * Results:
 *      Returns TRUE if Locked, FALSE otherwise.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

Bool
VvcMultiAsockBackendIsSocketLocked(MXUserRecLock *asockLock) // IN
{
   if (asockLock) {
      return MXUser_IsCurThreadHoldingRecLock(asockLock);
   } else {
      return FALSE;
   }
}


/*
 *----------------------------------------------------------------------------
 *
 * VvcMultiAsockBackendAcquireSocketLock --
 *
 *      Acquire the SocketLock
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

void
VvcMultiAsockBackendAcquireSocketLock(MXUserRecLock *asockLock) // IN
{
   if (asockLock) {
      MXUser_AcquireRecLock(asockLock);
   }
}


/*
 *----------------------------------------------------------------------------
 *
 * VvcMultiAsockBackendReleaseSocketLock --
 *
 *      Release the SocketLock
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

void
VvcMultiAsockBackendReleaseSocketLock(MXUserRecLock *asockLock) // IN
{
   if (asockLock) {
      MXUser_ReleaseRecLock(asockLock);
   }
}


/*
 *----------------------------------------------------------------------------
 *
 * VvcAsockBackendIncRef --
 *
 *      Increment a reference count on AsockBackend.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

VvcAsockBackend *
VvcAsockBackendIncRef(VvcAsockBackend *asockBe,        // IN
                      VvcTag tag,                      // IN
                      const char *callingFunctionName) // IN
{
   uint32 refCount;

   ASSERT(asockBe != NULL);

   refCount = Atomic_ReadInc32(&asockBe->refCount);

   ASSERT(refCount > 0);
   ASSERT(asockBe->isRemoved || refCount > 1);

   VvcMemTrace("%s adding tag %s to asockBe %d (%p). (pre-inc) Num refs: %d\n", callingFunctionName,
               VvcDebugTagToString(tag), asockBe->asockID, asockBe, refCount);

   return asockBe;
}


/*
 *----------------------------------------------------------------------------
 *
 * VvcAsockBackendDecRef --
 *
 *      Decrement a reference count on AsockBackend, and if this is the last
 *      reference, destroy the AsockBackend.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

void
VvcAsockBackendDecRef(VvcAsockBackend *asockBe,        // IN
                      VvcTag tag,                      // IN
                      const char *callingFunctionName) // IN
{
   uint32 refCount = 0; // initialize to fend gcc warnings of certain builds
   Bool alreadyDecremented = FALSE;
   MXUserRecLock *asockLock;
   Bool isAsockLocked;

   ASSERT(asockBe != NULL);
   asockLock = asockBe->asockLock;

   // lock-free fast path
   if (!asockBe->isRemoved) {
      refCount = Atomic_ReadDec32(&asockBe->refCount);
      ASSERT(refCount > 0);
      /*
       * Pre-dec refcount of 2 indicates VvcTagAsockBeSessionParent ref is now
       * released, and we must enter the critical section below to guarantee
       * only a single thread ends up destroying the asockBe.
       * A lot of this shutdown time complication is because locking at AddRef
       * is not a convenient retro fix. So shutdown DecRef code has to assume
       * same/different thread can inc refs even when asockBe 'isRemoved'.
       * Higher ref count decrements can take the lock-free fast path and
       * return early here.
       */
      if (refCount > 2) {
         VvcMemTrace("%s releasing tag %s to asockBe %d (%p). "
                     "(pre-dec) Num refs: %d\n",
                     callingFunctionName, VvcDebugTagToString(tag), asockBe->asockID, asockBe,
                     refCount);

         return;
      }
      alreadyDecremented = TRUE;
   }

   /*
    * Locking down the socket lock while doing the close operation. This handles
    * the following two race conditions.
    *
    * 1) Two threads might get into this call at the same time. This is possible
    *    because right after the first caller (e.g. a sender) of DecRef sees the
    *    remaining ref-count being 1 and entering this call; But before it could
    *    set asockBe->asock = NULL and do the actual socket close, a second
    *    caller (asock callback) can come in and increment the ref-count to 2,
    *    do the close. Now the first caller continues on and closes it again,
    *    causing a crash.
    *
    * 2) AsyncSocket_Close may invoke our VvcMultiAsockBackendSendCompleteCb
    *    inside the call recursively. But since we release the lock in that
    *    callback, this allows an additional callback after AsyncSocket_Close
    *    returns. The consequence is that the callback will access an invalid
    *    asockBe. See documentation for details:
    *    https://omnissa.atlassian.net/wiki/spaces/BNDT/pages/75629166/AsockBackend+Reference+Counting
    */

   isAsockLocked = VvcMultiAsockBackendIsSocketLocked(asockLock);
   if (!isAsockLocked) {
      VvcMultiAsockBackendAcquireSocketLock(asockLock);
   }

   if (alreadyDecremented) {
      if (refCount == 2) {
         // re-read the refcount under lock
         uint32 readRef = Atomic_Read32(&asockBe->refCount);
         // bail if some other thread managed to take a ref
         if (readRef != 1) {
            goto exit;
         }
      }
   } else {
      // decrement refcount under lock
      refCount = Atomic_ReadDec32(&asockBe->refCount);
      VvcMemTrace("%s releasing tag %s to asockBe %d (%p). (pre-dec) Num refs: "
                  " %d\n",
                  callingFunctionName, VvcDebugTagToString(tag), asockBe->asockID, asockBe,
                  refCount);
      if (refCount > 2) {
         goto exit;
      }
   }

   if (refCount == 2) {
      ASSERT(Atomic_Read32(&asockBe->refCount) == 1);

      // exit if we are recursively calling close
      if (asockBe->asock == NULL) {
         VvcLog("%s: Asock %d already closed", __FUNCTION__, asockBe->asockID);
         goto exit;
      }

      /*
       * Ref-count prior to decrement was 2, since the caller just released its
       * reference, the only entity possessor of this AsockBackend has to be the
       * AsyncSocket API callbacks. Proceed to close the asock now.
       */
      VvcAsockBackendClose(asockBe);
      ASSERT(asockBe->asock == NULL);
      if (!isAsockLocked) {
         VvcMultiAsockBackendReleaseSocketLock(asockLock);
      }
      /*
       * Release the reference for the AsyncSocket API callbacks. We cannot call
       * VvcAsockBackendDestroy directly because even after AsyncSocket_Close,
       * if an Asock API callback is racing with this function, it would have
       * incremented the ref-count in the meantime. When that callback returns,
       * it will release the ref-count and trigger the destruction of asockBE.
       */
      VVC_RELEASE_ASOCKBE(asockBe, VvcTagAsockBeAsockApi);
   } else if (refCount == 1) {
      ASSERT(asockBe->isRemoved);
      ASSERT(asockBe->asock == NULL);
      if (!isAsockLocked) {
         VvcMultiAsockBackendReleaseSocketLock(asockLock);
      }
      VvcAsockBackendDestroy(asockBe);
   }

   return;

exit:
   if (!isAsockLocked) {
      VvcMultiAsockBackendReleaseSocketLock(asockLock);
   }
}


/*
 *----------------------------------------------------------------------------
 *
 * VvcAsockBackendSetOptions() --
 *
 *      Set the socket options Flush timeout, low latency and no delay for
 *      asock. This was added to handle the bug - 2067572
 *
 * Results:
 *      Asock of the backend is set with the options
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

void
VvcAsockBackendSetOptions(AsyncSocket *asock) // IN
{
   Bool sendLowLatencyMode = TRUE;

   ASSERT(asock != NULL);

   AsyncSocket_SetCloseOptions(asock, ASYNCSOCKET_FLUSH_TIMEOUT_MSEC, NULL);

   AsyncSocket_SetOption(asock, ASYNC_SOCKET_OPTS_LAYER_BASE,
                         ASYNC_SOCKET_OPT_SEND_LOW_LATENCY_MODE, &sendLowLatencyMode,
                         sizeof(sendLowLatencyMode));

   AsyncSocket_UseNodelay(asock, TRUE);
}


/*
 *----------------------------------------------------------------------------
 *
 * VvcAsockBackendCreate --
 *
 *      Create a new AsockBackend based on input template.
 *
 * Results:
 *      The new AsockBackend.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

static VvcAsockBackend *
VvcAsockBackendCreate(VvcSession *session,                       // IN
                      const VvcAsockBackend *asockBackendParams) // IN
{
   VvcAsockBackend *asockBe;
   Bool isServer = (Bool)(session->transportBe.flags & VVC_TRANSPORT_BE_SERVER);

   ASSERT(session != NULL);
   ASSERT(asockBackendParams != NULL);
   ASSERT(asockBackendParams->asock != NULL);
   ASSERT(asockBackendParams->asockLock != NULL);

   asockBe = Util_SafeCalloc(1, sizeof *asockBe);

   asockBe->asock = asockBackendParams->asock;
   asockBe->asockLock = asockBackendParams->asockLock;
   asockBe->asockID = AsyncSocket_GetID(asockBackendParams->asock);
   ASSERT(asockBe->asockID != INVALID_ASOCKID);
   asockBe->errorCb = asockBackendParams->errorCb;
   asockBe->errorCbClientData = asockBackendParams->errorCbClientData;
   asockBe->isEndToEndConnection = asockBackendParams->isEndToEndConnection;
   asockBe->isControlAsock = asockBackendParams->isControlAsock;
   if (!isServer) {
      asockBe->isDataAsock = asockBackendParams->isDataAsock;
   }
   asockBe->dataSockActivatedCb = asockBackendParams->dataSockActivatedCb;
   asockBe->dataSockActivatedCbData = asockBackendParams->dataSockActivatedCbData;
   asockBe->getAuxFlowInfoCb = asockBackendParams->getAuxFlowInfoCb;
   asockBe->getAuxFlowInfoCbData = asockBackendParams->getAuxFlowInfoCbData;
   asockBe->getSetupMsgCb = asockBackendParams->getSetupMsgCb;
   asockBe->verifySetupMsgCb = asockBackendParams->verifySetupMsgCb;
   asockBe->verifySetupMsgCbCb = asockBackendParams->verifySetupMsgCbCb;
   asockBe->verifySetupMsgCbCbData = asockBackendParams->verifySetupMsgCbCbData;
   asockBe->tcpConnect = asockBackendParams->tcpConnect;
   asockBe->tcpConnectData = asockBackendParams->tcpConnectData;
   asockBe->remotePort = asockBackendParams->remotePort;
   asockBe->getSslCtxCb = asockBackendParams->getSslCtxCb;
   asockBe->getSslCtxCbData = asockBackendParams->getSslCtxCbData;

#if defined(_WIN32)
   asockBe->qwaveQoSFlowID = 0;
   asockBe->qwaveQoSHandle = NULL;
#endif

   asockBe->recvState = VvcAllocRecvState(asockBe->asockID);

   asockBe->channelMsgSeqMap = HashMap_AllocMap(VVC_ASOCKBE_CHANNEL_MSG_SEQ_MAP_INITIAL_SIZE,
                                                sizeof(uint32), // key size - channelId
                                                sizeof(VvcPerAsockBeMsgSeqEntry)); // data size

   /*
    * Reserve one reference for all AsyncSocket API callbacks, and one for the
    * caller. Manually add tags since we cannot call IncRef yet.
    */
   Atomic_Write32(&asockBe->refCount, 2);

   VvcMemTrace("%s adding tag %s to asockBe %d (%p). (pre-inc) Num refs: %d\n", __FUNCTION__,
               VvcDebugTagToString(VvcTagAsockBeAsockApi), asockBe->asockID, asockBe,
               Atomic_Read32(&asockBe->refCount));
   VvcMemTrace("%s adding tag %s to asockBe %d (%p). (pre-inc) Num refs: %d\n", __FUNCTION__,
               VvcDebugTagToString(VvcTagAsockBeGeneric), asockBe->asockID, asockBe,
               Atomic_Read32(&asockBe->refCount));

   // Save the VvcSession ptr and bump up refCount of the VvcSession by 1
   VVC_ADD_REF_SESSION(session, VvcTagAsockBackend);
   asockBe->session = session;

   // Only the primary asockBE provides a valid nonce
   if (asockBackendParams->workerNonce) {
      ASSERT(session->nonce == 0 || session->nonce == asockBackendParams->workerNonce);
      session->nonce = asockBackendParams->workerNonce;
   }

   asockBe->isVVCHeartbeatEnabled = FALSE;
   asockBe->lastRecvTs = Hostinfo_SystemTimerUS();
   asockBe->lastSendTs = Hostinfo_SystemTimerUS();

   VvcAsockBackendSetOptions(asockBe->asock);
   return asockBe;
}


/*
 *----------------------------------------------------------------------------
 *
 * VvcAsockBackendDestroy --
 *
 *      Destroy an AsockBackend. This should ONLY be called from
 *      VvcAsockBackendDecRef, when the last reference is released.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

void
VvcAsockBackendDestroy(VvcAsockBackend *asockBe)
{
   VvcSession *session = asockBe->session;
   int sessionId = session->sessionId;
   Bool isAsockLocked;

   ASSERT(session);

   ASSERT(Atomic_Read32(&asockBe->refCount) == 0);
   ASSERT(asockBe->isRemoved);
   ASSERT(asockBe->asock == NULL);

   isAsockLocked = VvcMultiAsockBackendIsSocketLocked(asockBe->asockLock);
   if (isAsockLocked) {
      VvcMultiAsockBackendReleaseSocketLock(asockBe->asockLock);
   }

   /*
    * Now it's safe to release asockBackend's session ref because after closing
    * the asock, there won't be any further asock callbacks that could possibly
    * accessing the session.
    */
   VVC_RELEASE_SESSION(session, VvcTagAsockBackend);

   VvcLog("%s: Freeing AsockBackend (asockId %d session %u)\n", __FUNCTION__, asockBe->asockID,
          sessionId);

   VvcDestroyRecvState(asockBe->recvState);
   HashMap_DestroyMap(asockBe->channelMsgSeqMap);

   if (isAsockLocked) {
      VvcMultiAsockBackendAcquireSocketLock(asockBe->asockLock);
   }

   free(asockBe);
}


/*
 *----------------------------------------------------------------------------
 *
 * VvcAsockBackendClose --
 *
 *      Close the Asock in the asockBackend. This should ONLY be called when
 *      the asockBackend has been removed and when the only references are from
 *      AsyncSocket API callbacks.
 *
 *      ONLY call this from VvcAsockBackendDecRef().
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

void
VvcAsockBackendClose(VvcAsockBackend *asockBe)
{
   VvcSession *session = asockBe->session;
   int sessionId = session->sessionId;
   AsyncSocket *asock;

   ASSERT(session);
   ASSERT(asockBe->isRemoved);

   /* About to close asock, so nullify the asock pointer first */
   asock = asockBe->asock;
   asockBe->asock = NULL;

   if (asock == NULL) {
      VvcLog("%s: Asock %d already closed.\n", __FUNCTION__, asockBe->asockID);
      ASSERT(Atomic_Read32(&asockBe->refCount) >= 1);
      return;
   }

   // Clear/unload Qwave QoS stuff if required
   if (asockBe->isControlAsock) {
      VvcMultiAsockBeUnsetDscpOpt(asockBe);
   }

   VvcLog("%s: Doing AsyncSocket_Close() for asock %d session %u\n", __FUNCTION__, asockBe->asockID,
          sessionId);

   AsyncSocket_CancelRecvEx(asock, NULL, NULL, NULL, TRUE);
   AsyncSocket_Close(asock);
}


/*
 *----------------------------------------------------------------------------
 *
 * VvcAsockBackendShutdown --
 *
 *      Stop receive operation and free the recvState on the asockbackend. Also
 *      mark the asockBe as removed. Do this as part of the asockbackend
 *      removal process.
 *
 *      This function must be called with session lock held so that we don't
 *      accidentally free the recvState pointer while receiving is in progress.
 *      Note that the recvState pointer is passed to the session for receive
 *      processing which is done under the session lock.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

void
VvcAsockBackendShutdown(VvcAsockBackend *asockBe)
{
   VvcSession *session = asockBe->session;
   size_t partialMsgBytes;

   ASSERT(ISLOCKED_SESSION(session));

   VvcMultiAsockBackendAcquireSocketLock(asockBe->asockLock);

   /* Cancel receive so we won't get a receive callback. */
   VvcLog("%s: Doing CancelRecv for Asock %d\n", __FUNCTION__, asockBe->asockID);
   ASSERT(asockBe->asock);
   AsyncSocket_CancelRecvEx(asockBe->asock, NULL, NULL, NULL, TRUE);

   /*
    * Adjust unackedRecvBytes with any partial message bytes that we over acked
    * (We do vvc-ack for a message as soon as any of the chunks arrives)
    */
   partialMsgBytes = VvcDestroyRecvState(asockBe->recvState);
   session->unackedRecvBytes -= partialMsgBytes;
   asockBe->recvState = NULL;
   VvcTrace("%s: Dropped %" FMTSZ "u partial message bytes on asock %d.\n", __FUNCTION__,
            partialMsgBytes, asockBe->asockID);

   VvcLog("%s: Removing AsockBe (asockId %d) refcount %u\n", __FUNCTION__, asockBe->asockID,
          Atomic_Read32(&asockBe->refCount));
   asockBe->isRemoved = TRUE;

   VvcMultiAsockBackendReleaseSocketLock(asockBe->asockLock);
}


/*
 *----------------------------------------------------------------------------
 *
 * VvcGetNumAsockBackends --
 *
 *      Get the VvcSession::numAsockBackends
 *
 * Results:
 *      Returns numAsockBackends
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

int32
VvcGetNumAsockBackends(VvcSession *session) // IN
{
   int32 numAsockBackends = 0;
   Bool isSessionLocked = FALSE;

   if (!session) {
      return numAsockBackends;
   }

   isSessionLocked = ISLOCKED_SESSION(session);

   if (!isSessionLocked) {
      LOCK_SESSION(session);
   }

   numAsockBackends = session->numAsockBackends;

   if (!isSessionLocked) {
      UNLOCK_SESSION(session);
   }

   return numAsockBackends;
}


/*
 *----------------------------------------------------------------------------
 *
 * VvcIsCtrlKeepAliveNeeded --
 *
 *      CtrlKeepAlive should be enabled if:
 *       - We are the server &&
 *       - We have more than one AsockBackend  &&
 *       - The ctrlAsockBackend is non-Bwe TCP &&
 *       - MTP/DTP is disabled.
 *
 * Results:
 *      Returns TRUE if CtrlKeepAlive should be enabled, FALSE otherwise.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

Bool
VvcIsCtrlKeepAliveNeeded(VvcSession *session) // IN
{
   Bool needed = FALSE;
   Bool isSessionLocked = FALSE;
   Bool isServer;
   VvcAsockBackend *ctrlAsockBe;

   ASSERT(session);

   isSessionLocked = ISLOCKED_SESSION(session);

   if (!isSessionLocked) {
      LOCK_SESSION(session);
   }

   isServer = (Bool)(session->transportBe.flags & VVC_TRANSPORT_BE_SERVER);

   ctrlAsockBe = VvcGetControlAsockBackend(session);
   if (ctrlAsockBe == NULL) {
      goto out;
   }

   if (isServer && session->state == VvcSessionEstablished &&
       !session->negotiatedDoConcurrentTransports && session->numAsockBackends > 1 &&
       !ctrlAsockBe->isEndToEndConnection) {
      needed = TRUE;
   }

out:
   if (needed) {
      VvcLog("%s: VvcSession: %p, CtrlKeepAlive is needed on asock %d\n", __FUNCTION__, session,
             ctrlAsockBe->asockID);
   } else {
      VvcLog("%s: VvcSession: %p, CtrlKeepAlive is not needed\n", __FUNCTION__, session);
   }

   if (ctrlAsockBe) {
      VVC_RELEASE_ASOCKBE(ctrlAsockBe, VvcTagAsockBeGeneric);
   }

   if (!isSessionLocked) {
      UNLOCK_SESSION(session);
   }

   return needed;
}


/*
 *----------------------------------------------------------------------------
 *
 * VvcMarkAllAsockBackendsAsData --
 *
 *      All AsockBackends can potentially receive data.
 *      If MTP is enabled, all AsockBackends will receive valid VVC data.
 *      If MTP is disabled, only active AsockBackend will receive value VVC
 *      data and others will receive dummy data (which will be dropped).
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

void
VvcMarkAllAsockBackendsAsData(VvcSession *session) // IN
{
   Bool isSessionLocked = FALSE;
   int32 i = 0;

   ASSERT(session);

   isSessionLocked = ISLOCKED_SESSION(session);

   if (!isSessionLocked) {
      LOCK_SESSION(session);
   }

   for (i = 0; i < session->numAsockBackends; i++) {
      session->asockBackends[i]->isDataAsock = TRUE;
   }

   if (!isSessionLocked) {
      UNLOCK_SESSION(session);
   }
}


/*
 *----------------------------------------------------------------------------
 *
 * VvcDispatchDataSockActivatedCb --
 *
 *      When a VvcAsockBe gets marked as DataSocket, dispatch the application's
 *      dataSockActivatedCb (provided in VVCLIB_AddAsockBackend) if needed. Log
 *      the reason for the activation.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

static void
VvcDispatchDataSockActivatedCb(VvcAsockBackend *asockBe,
                               VvcDataAsockActivationReason reason) // IN
{
   int32 sessionId = VVC_CURRENT_SESSION;
   Bool isSessionLocked = FALSE;
   VvcSession *session = NULL;
   Bool isServer;
   Bool isReconnect = FALSE;
   VvcAsockBackend *matchingAsockBe;
   Bool asockBackendIsValid;

   if (!asockBe || !asockBe->asock) {
      return;
   }

   session = (VvcSession *)(asockBe->session);

   if (!session) {
      return;
   }

   isSessionLocked = ISLOCKED_SESSION(session);

   if (!isSessionLocked) {
      LOCK_SESSION(session);
   }

   /*
    * Only dispatch the callback if
    *
    *   1) DataSockNotification is needed, and
    *   2) The asockbackend pointer is still valid.
    *
    */
   if (!VvcIsDataSockNotificationNeeded(session)) {
      if (!isSessionLocked) {
         UNLOCK_SESSION(session);
      }
      return;
   }

   matchingAsockBe = VvcGetAsockBackendFromAsockID(session, asockBe->asockID);
   asockBackendIsValid = (matchingAsockBe == asockBe);
   if (matchingAsockBe) {
      VVC_RELEASE_ASOCKBE(matchingAsockBe, VvcTagAsockBeGeneric);
   }

   if (!asockBackendIsValid) {
      /*
       * This could happen during network recovery, where a new control AsockBe
       * replaces the old one, but the one asockbe is still in the process of
       * shutting down.
       */
      VvcLog("%s: Not dispatching dataSockActivedCb (reason=%d) because this "
             "AsockBe (asockID=%d) has been removed\n",
             __FUNCTION__, reason, asockBe->asockID);

      if (!isSessionLocked) {
         UNLOCK_SESSION(session);
      }

      return;
   }

   sessionId = session->sessionId;

   VvcLog("%s: Session %d setting isDataAsock flag for asockBe "
          "(asock=%p, asockID=%d), reason=%d; This is %s the control socket\n",
          __FUNCTION__, sessionId, asockBe->asock, asockBe->asockID, reason,
          asockBe->isControlAsock == TRUE ? "also" : "not");

   /*
    * On Agent-Side,  VvcSession::activeAsockBackendIndex is set here.
    * On Client-Side, VvcSession::activeAsockBackendIndex gets set via
    * AddAsockBackend() call.
    */

   VvcSetActiveAsockBackend(asockBe->session, asockBe->asock);

   session->isDataSockNotificationNeeded = FALSE;

   /*
    * If DataSockActivatedCb is being fired when a Session is already in
    * Established state, then this is a Reconnect scenario.
    */
   isReconnect = session->state == VvcSessionEstablished;

   if (!isSessionLocked) {
      UNLOCK_SESSION(session);
   }

   isServer = (Bool)(session->transportBe.flags & VVC_TRANSPORT_BE_SERVER);
   if (isServer) {
      VvcEnableBandwidthEstimation(session);
   }

   VvcSetSessionCloseReason(session, VvcSessionCloseInvalid);

   VvcLog("VvcSession=%p, Asock=%p, AsockID=%d, isEndToEndConnection: %s, "
          "Vvc's Bw Detection is %s, activeAsockBackendIndex: %d.\n",
          session, asockBe->asock, asockBe->asockID,
          asockBe->isEndToEndConnection == TRUE ? "True" : "False",
          session->disableBandwidthDetection == 1 ? "Disabled" : "Enabled",
          session->activeAsockBackendIndex);

   if (asockBe->dataSockActivatedCb) {

      Bool isLocked = VvcMultiAsockBackendIsSocketLocked(asockBe->asockLock);

      if (isLocked) {
         VvcMultiAsockBackendReleaseSocketLock(asockBe->asockLock);
      }

      asockBe->dataSockActivatedCb(asockBe->asock, sessionId, isReconnect, asockBe->isControlAsock,
                                   asockBe->dataSockActivatedCbData);

      if (isLocked) {
         VvcMultiAsockBackendAcquireSocketLock(asockBe->asockLock);
      }
   }

   /*
    * Mark both AsockBackends as DataSockets here.
    * When MTP is enabled,  both will receive valid VVC Data.
    * When MTP is disabled, data received on non-Active AsockBe
    * should be dropped.
    */

   VvcMarkAllAsockBackendsAsData(session);

   VvcLog("[VVC Heartbeats] In multiasock, value of negotiatedDoVVCHeartbeats:"
          " %d\n",
          session->negotiatedDoVVCHeartbeats);
   if (session->negotiatedDoVVCHeartbeats == TRUE && session->state == VvcSessionEstablished) {
      /*
       * We'll only hit this in the case of network continuity. If we have
       * network recovery and the session restarts, we'll setup the
       * heartbeats after doing the CTRL_OP_INIT cap exchange. Since
       * network continuity doesn't repeat the cap exchange, we'll start
       * up heartbeats here.
       */
      VvcMultiAsockBackendStartKeepaliveTimeout(session);
   }

   /*
    * Control Keepalive - Send a dummy packet on the control socket at keepalive
    * interval to prevent middle boxes from terminate the control socket
    * connection. This mechanism is incompatible with DTP (Concurrent Transport)
    * because the keepalive packet is not a VVC message and will interfere with
    * normal data flow. It only works on a socket that will never send or
    * receive any VVC data.
    *
    * Now start sending KeepAlive messages on the Ctrl Socket if required. We
    * will hit this on normal start up and in the case of network continuity.
    */
   VvcStartCtrlKeepAliveIfNeeded(session);
}


/*
 *----------------------------------------------------------------------------
 *
 * VvcGetTCPAsockBackend --
 *
 *      Get AsockBe that has isControlSocket flag set.
 *      Currently, only TCP Socket can be Control AsockBe.
 *
 *      TODO: AsockBe::isControlSocket should be renamed to AsockBe::isTCP
 *      TODO: VvcGetControlAsockBackend() func should be removed and
 *            VvcGetTCPAsockBackend() should be used instead in its place
 *            everywhere.
 *
 * Results:
 *      Returns the appropriate VvcAsockBackend.
 *
 * Side effects:
 *      Increments the AsockBackend's ref count. Caller should decrement this
 *      ref count when done.
 *
 *----------------------------------------------------------------------------
 */

VvcAsockBackend *
VvcGetTCPAsockBackend(VvcSession *session) // IN
{
   VvcAsockBackend *asockBackend = NULL;
   Bool isSessionLocked = FALSE;
   int32 i = 0;

   ASSERT(session);

   isSessionLocked = ISLOCKED_SESSION(session);

   if (!isSessionLocked) {
      LOCK_SESSION(session);
   }

   for (i = 0; i < session->numAsockBackends; i++) {
      if (session->asockBackends[i]->isControlAsock == TRUE) {
         asockBackend = VVC_ADD_REF_ASOCKBE(session->asockBackends[i], VvcTagAsockBeGeneric);
         break;
      }
   }

   if (!isSessionLocked) {
      UNLOCK_SESSION(session);
   }

   return asockBackend;
}


/*
 *----------------------------------------------------------------------------
 *
 * VvcGetBEATAsockBackend --
 *
 *      Get AsockBe that has isControlSocket flag Not set.
 *      XXX: This func returns the first BEAT asock be.
 *           For DataPinning and "Aux" BEAT AsockBe, this func is not
 *           sufficient.
 *      Currently, only TCP Socket can be Control AsockBe.
 *
 * Results:
 *      Returns the appropriate VvcAsockBackend.
 *
 * Side effects:
 *      Increments the AsockBackend's ref count. Caller should decrement this
 *      ref count when done.
 *
 *----------------------------------------------------------------------------
 */

VvcAsockBackend *
VvcGetBEATAsockBackend(VvcSession *session) // IN
{
   VvcAsockBackend *asockBackend = NULL;
   Bool isSessionLocked = FALSE;
   int32 i = 0;

   ASSERT(session);

   isSessionLocked = ISLOCKED_SESSION(session);

   if (!isSessionLocked) {
      LOCK_SESSION(session);
   }

   for (i = 0; i < session->numAsockBackends; i++) {
      if (!session->asockBackends[i]->isControlAsock) {
         asockBackend = VVC_ADD_REF_ASOCKBE(session->asockBackends[i], VvcTagAsockBeGeneric);
         break;
      }
   }

   if (!isSessionLocked) {
      UNLOCK_SESSION(session);
   }

   return asockBackend;
}


/*
 *----------------------------------------------------------------------------
 *
 * VvcGetControlAsockBackend --
 *
 *      Get the AsockBackend that has isControlSocket switch set.
 *      Currently, there can be only One Control AsockBackend.
 *
 * Results:
 *      Returns the VvcAsockBackend.
 *
 * Side effects:
 *      Increments the AsockBackend's ref count. Caller should decrement this
 *      ref count when done.
 *
 *----------------------------------------------------------------------------
 */

VvcAsockBackend *
VvcGetControlAsockBackend(VvcSession *session) // IN
{
   return VvcGetTCPAsockBackend(session);
}


/*
 *----------------------------------------------------------------------------
 *
 * VvcGetDefaultDataAsockBackend --
 *
 *      Policy used:
 *      - If numAsockBackends = 2 then Return the second AsockBackend
 *      - If numAsockBackends = 1 then Return that AsockBackend
 *
 * Results:
 *      Returns the VvcAsockBackend.
 *
 * Side effects:
 *      Increments the AsockBackend's ref count. Caller should decrement this
 *      ref count when done.
 *
 *----------------------------------------------------------------------------
 */

VvcAsockBackend *
VvcGetDefaultDataAsockBackend(VvcSession *session) // IN
{
   VvcAsockBackend *asockBackend = NULL;
   Bool isSessionLocked = FALSE;

   ASSERT(session);

   isSessionLocked = ISLOCKED_SESSION(session);

   if (!isSessionLocked) {
      LOCK_SESSION(session);
   }

   if (session->numAsockBackends != 0) {
      /* Return the asockBackend that was added last */
      asockBackend = session->asockBackends[session->numAsockBackends - 1];
      VVC_ADD_REF_ASOCKBE(asockBackend, VvcTagAsockBeGeneric);
   }

   if (!isSessionLocked) {
      UNLOCK_SESSION(session);
   }

   return asockBackend;
}


/*
 *----------------------------------------------------------------------------
 *
 * VvcGetAsockBackendFromAsock --
 *
 *      Get the AsockBackend given the base AsyncSocket pointer.
 *
 * Results:
 *      Returns the VvcAsockBackend.
 *
 * Side effects:
 *      Increments the AsockBackend's ref count. Caller should decrement this
 *      ref count when done.
 *
 *----------------------------------------------------------------------------
 */

VvcAsockBackend *
VvcGetAsockBackendFromAsock(VvcSession *session, // IN
                            AsyncSocket *asock)  // IN
{
   VvcAsockBackend *asockBackend = NULL;
   Bool isSessionLocked = FALSE;
   int32 i = 0;

   ASSERT(session);

   if (!asock) {
      return NULL;
   }

   isSessionLocked = ISLOCKED_SESSION(session);

   if (!isSessionLocked) {
      LOCK_SESSION(session);
   }

   for (i = 0; i < session->numAsockBackends; i++) {
      if (session->asockBackends[i]->asock == asock) {
         asockBackend = VVC_ADD_REF_ASOCKBE(session->asockBackends[i], VvcTagAsockBeGeneric);
         break;
      }
   }

   if (!isSessionLocked) {
      UNLOCK_SESSION(session);
   }

   return asockBackend;
}


/*
 *----------------------------------------------------------------------------
 *
 * VvcGetAsockBackendFromAsockID --
 *
 *      Get the AsockBackend given the AsyncSocket ID.
 *
 * Results:
 *      Returns the VvcAsockBackend.
 *
 * Side effects:
 *      Increments the AsockBackend's ref count. Caller should decrement this
 *      ref count when done.
 *
 *----------------------------------------------------------------------------
 */

VvcAsockBackend *
VvcGetAsockBackendFromAsockID(VvcSession *session, // IN
                              int asockID)         // IN
{
   VvcAsockBackend *asockBackend = NULL;
   Bool isSessionLocked = FALSE;
   int32 i = 0;

   ASSERT(session);

   isSessionLocked = ISLOCKED_SESSION(session);

   if (!isSessionLocked) {
      LOCK_SESSION(session);
   }

   for (i = 0; i < session->numAsockBackends; i++) {
      if (session->asockBackends[i]->asockID == asockID) {
         asockBackend = VVC_ADD_REF_ASOCKBE(session->asockBackends[i], VvcTagAsockBeGeneric);
         break;
      }
   }

   if (!isSessionLocked) {
      UNLOCK_SESSION(session);
   }

   return asockBackend;
}


/*
 *----------------------------------------------------------------------------
 *
 * VvcGetAsockBackend --
 *
 *      Get the AsockBackend to be used for subsequent transport calls given
 *      VvcSession and (optionally) AsyncSocket ptr.
 *
 *      XXX: This function will expand in future for Multi-Protocol.
 *           The multi-protocol "logic" is abstracted in this func call.
 *
 * Results:
 *      Returns the VvcAsockBackend.
 *      Will set isDataAsock flag if required.
 *
 * Side effects:
 *      Increments the AsockBackend's ref count. Caller should decrement this
 *      ref count when done.
 *
 *----------------------------------------------------------------------------
 */

VvcAsockBackend *
VvcGetAsockBackend(VvcSession *session) // IN
{
   VvcAsockBackend *asockBackend = NULL;

   /*
    * Every Backend implementation invokes VvcGetAsockBackend() at its
    * beginning.
    * Therefore, VvcGetAsockBackend() should return as quickly as possible
    * in maximum number of cases.
    * Query the Active AsockBackend of the VvcSeesion first.
    * If none has been marked, then check for the Data AsockBackend.
    */

   asockBackend = VvcGetActiveAsockBackend(session);
   if (asockBackend) {
      return asockBackend;
   }

   /*
    * No AsockBackend has been marked as DataSocket yet.
    * VvcSession::AsockBackends[] does not recognize this asock ptr.
    * As a last resort, return a default Data AsockBackend.
    */
   asockBackend = VvcGetDefaultDataAsockBackend(session);
   if (!asockBackend) {
      VvcDebug("%s: No current active AsockBackend.\n", __FUNCTION__);
   }

   return asockBackend;
}


/*
 *----------------------------------------------------------------------------
 *
 * VvcGetActiveAsockBackend --
 *
 *      Return the VvcSession's active AsockBackend.
 *
 * Results:
 *      Returns the VvcAsockBackend.
 *
 * Side effects:
 *      Increments the AsockBackend's ref count. Caller should decrement this
 *      ref count when done.
 *
 *----------------------------------------------------------------------------
 */

VvcAsockBackend *
VvcGetActiveAsockBackend(VvcSession *session) // IN
{
   VvcAsockBackend *asockBackend = NULL;
   Bool isSessionLocked = FALSE;

   ASSERT(session);

   isSessionLocked = ISLOCKED_SESSION(session);

   if (!isSessionLocked) {
      LOCK_SESSION(session);
   }

   if (session->activeAsockBackendIndex == -1) {
      asockBackend = NULL;
   } else {
      ASSERT(session->activeAsockBackendIndex < 2);
      asockBackend = session->asockBackends[session->activeAsockBackendIndex];
      VVC_ADD_REF_ASOCKBE(asockBackend, VvcTagAsockBeGeneric);
   }

   if (!isSessionLocked) {
      UNLOCK_SESSION(session);
   }

   return asockBackend;
}


/*
 *----------------------------------------------------------------------------
 *
 * VvcSetActiveAsockBackend --
 *
 *      Set the VvcSession's active AsockBackend that holds the given Asock.
 *
 * Results:
 *      Returns the VvcSession::activeAsockBackendIndex set.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

int32
VvcSetActiveAsockBackend(VvcSession *session, // IN
                         AsyncSocket *asock)  // IN
{
   Bool isSessionLocked = FALSE;
   int32 i = 0;

   ASSERT(session);

   isSessionLocked = ISLOCKED_SESSION(session);

   if (!isSessionLocked) {
      LOCK_SESSION(session);
   }

   for (i = 0; i < session->numAsockBackends; i++) {
      if (session->asockBackends[i]->asock == asock) {
         session->activeAsockBackendIndex = i;
         break;
      }
   }

   if (!isSessionLocked) {
      UNLOCK_SESSION(session);
   }

   return i;
}


/*
 *----------------------------------------------------------------------------
 *
 * VvcHasActiveAsockBackend --
 *
 *      Returns if the VvcSession has an active AsockBackend or not.
 *
 * Results:
 *      Returns TRUE if VvcSession::activeAsockBackendIndex is valid
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

Bool
VvcHasActiveAsockBackend(VvcSession *session) // IN
{
   Bool ok = TRUE;
   Bool isSessionLocked = FALSE;

   ASSERT(session);

   isSessionLocked = ISLOCKED_SESSION(session);

   if (!isSessionLocked) {
      LOCK_SESSION(session);
   }

   if (session->activeAsockBackendIndex == -1) {
      ok = FALSE;
   }

   if (!isSessionLocked) {
      UNLOCK_SESSION(session);
   }

   return ok;
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcIsActiveAsockBackendTCP --
 *
 *    Return if current Active AsockBackend is a TCP Socket
 *
 * Results:
 *    Returns TRUE if current ActiveAsockBe is TCP, FALSE otherwise
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

Bool
VvcIsActiveAsockBackendTCP(VvcSession *session) // IN
{
   Bool isTCP = FALSE;
   VvcAsockBackend *activeAsockBackend = NULL;

   ASSERT(session);

   activeAsockBackend = VvcGetActiveAsockBackend(session);

   if (!activeAsockBackend) {
      return isTCP;
   }

   /*
    * Currently, ControlSocket can only be TCP.
    * TODO: AsockBackend::isControlAsock should be renamed to isTCPAsock
    *       AsockBackend::isDataAsock should be removed
    */
   isTCP = activeAsockBackend->isControlAsock;

   // Release the ref acquired by VvcGetActiveAsockBackend() above
   VVC_RELEASE_ASOCKBE(activeAsockBackend, VvcTagAsockBeGeneric);

   return isTCP;
}


/*
 *----------------------------------------------------------------------------
 *
 * VvcAsockBackendValidateAsock --
 *
 *  There is a very small chance we have removed an asockbackend and then we get
 *  one (last) AsyncSocket callback (send/recv/error). Catch this situation.
 *
 * Results:
 *      TRUE if the AsockBackend still valid, FALSE otherwise.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

Bool
VvcAsockBackendValidateAsock(AsyncSocket *asock, const VvcAsockBackend *asockBeCtx)
{
   VvcSession *session = (VvcSession *)asockBeCtx->session;
   Bool valid = FALSE;

   ASSERT(asockBeCtx);
   ASSERT(VvcMultiAsockBackendIsSocketLocked(asockBeCtx->asockLock));

   if (session == NULL) {
      VvcDebug("%s: For Asock %d, the asockBe->session is NULL, it is "
               "probably being or have been removed.\n",
               __FUNCTION__, AsyncSocket_GetID(asock));
      return FALSE;
   }

   if (asockBeCtx->isRemoved) {
      VvcWarning("%s: AsockBackend %p with Asock ID %d has been removed\n", __FUNCTION__,
                 asockBeCtx, AsyncSocket_GetID(asock));
      goto out;
   }

   if (!asockBeCtx->asock) {
      VvcWarning("%s: For Asock %d, the asockBe's Asock is NULL, it is "
                 "probably being or has been removed.\n",
                 __FUNCTION__, AsyncSocket_GetID(asock));
      goto out;
   }

   if (asockBeCtx->asock != asock) {
      /*
       * XXX AsyncProxySocket could invoke our AsyncSocket callbacks with one of
       * its "other" sockets, causing this mismatch. Because AsyncProxySocket is
       * deprecated (currently only used by Android clients), we should
       * remove this relaxation once every client switches to using
       * BlastSocketClient.
       */
      VvcDebug("%s: Asock %d from asockBeCtx %p doesn't match AsockBackend's "
               "own Asock (%d)\n",
               __FUNCTION__, AsyncSocket_GetID(asock), asockBeCtx, asockBeCtx->asockID);
   }

   valid = TRUE;

out:
   return valid;
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcMsgAssignAsockBe --
 *
 *    Assign an asockBackend to an outgoing VvcMsg. Assignment is decided by the
 *    message's pinToTransport (TCP|BEAT) property, if present, or the current
 *    active transport.
 *
 * Results:
 *    None.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */
void
VvcMsgAssignAsockBe(VvcMsg *msg)
{
   VvcSession *session = msg->channel->session;
   VvcAsockBackend *asockBe = NULL;
   Bool isMultiAsockTransport;

   ASSERT(session != NULL);

   isMultiAsockTransport = (session->transportBe.flags & VVC_TRANSPORT_BE_ENABLE_MULTI_PROTOCOL);

   if (!isMultiAsockTransport) {
      /*
       * This function only makes sense for MultiAsockBackends (not MFW, for
       * example)
       */
      return;
   }

   if (msg->sendAsockID != INVALID_ASOCKID) {
      /*
       * Once an asockBackend is assigned, it cannot be changed. This is to
       * prevent multiple chunks of the same message from being assigned
       * different asockBackends, which would violate the MPT protocol.
       */
      return;
   }

   if (msg->pinToTransport == VvcMsgPinToTCP) {
      asockBe = VvcGetTCPAsockBackend(session);
   } else if (msg->pinToTransport == VvcMsgPinToBEAT) {
      asockBe = VvcGetBEATAsockBackend(session);
   } else {
      /*
       * VvcGetAsockBackend() returns whichever AsockBe is currently active.
       */
      asockBe = VvcGetAsockBackend(session);
   }

   if (asockBe) {
      msg->sendAsockID = asockBe->asockID;
      VVC_RELEASE_ASOCKBE(asockBe, VvcTagAsockBeGeneric);
   } else {
      msg->sendAsockID = INVALID_ASOCKID;
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcMultiAsockBackendGetAddrFamily --
 *
 *    Query the AsyncSocket to get AddrFamily (v4 or v6)
 *
 * Results:
 *    Returns the addrFamily.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

int
VvcMultiAsockBackendGetAddrFamily(VvcAsockBackend *asockBe, // IN
                                  int *addrFamily)          // OUT
{
   Bool ok = TRUE;
   char *addrStr = NULL;
   VvcSession *session;

   ASSERT(asockBe);
   ASSERT(asockBe->asock);
   ASSERT(addrFamily);

   session = asockBe->session;
   ASSERT(session);
   ASSERT(ISLOCKED_SESSION(session));

   /*
    * AsyncSocket does not have an API to "get" Address Family.
    * So invoke AsyncSocket_GetINETIPStr() with both AF_INET and AF_INET6.
    */

   if (AsyncSocket_GetINETIPStr(asockBe->asock, AF_INET, &addrStr) == ASOCKERR_SUCCESS) {
      *addrFamily = AF_INET;
   } else if (AsyncSocket_GetINETIPStr(asockBe->asock, AF_INET6, &addrStr) == ASOCKERR_SUCCESS) {
      *addrFamily = AF_INET6;
   } else {
      *addrFamily = 0;
      ok = FALSE;
   }

   free(addrStr);
   return ok;
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcMultiAsockBackendGetDscpValueToApply --
 *
 *    Get the appropriate Dscp value from the QoSPolicy
 *
 * Results:
 *    Returns VVC_QOS_INVALID_VALUE on error, otherwise retrieve & return the
 *    required value from QoSPolicy.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

int
VvcMultiAsockBackendGetDscpValueToApply(VvcAsockBackend *asockBe, // IN
                                        Bool isServer,            // IN
                                        Bool isTCP)               // IN
{
   int dscpValue = VVC_QOS_INVALID_VALUE;
   int addrFamily = AF_INET;
   VvcSession *session;

   ASSERT(asockBe);
   session = asockBe->session;
   ASSERT(session);
   ASSERT(ISLOCKED_SESSION(session));

   if (VvcMultiAsockBackendGetAddrFamily(asockBe, &addrFamily) == FALSE) {
      VvcError("Unknown/unsupported AddrFamily: %d\n", addrFamily);
      return dscpValue;
   }

   if (isTCP) {
      if (isServer) {
         if (addrFamily == AF_INET) {
            dscpValue = session->qosPolicy->v1.dscpAOutTCPv4;
         } else if (addrFamily == AF_INET6) {
            dscpValue = session->qosPolicy->v1.dscpAOutTCPv6;
         } else {
            VvcError("%s: dscpAOutTCP: Unknown/unsupported AddrFamily: %d\n", __FUNCTION__,
                     addrFamily);
         }
      } else {
         if (addrFamily == AF_INET) {
            dscpValue = session->qosPolicy->v1.dscpCOutTCPv4;
         } else if (addrFamily == AF_INET6) {
            dscpValue = session->qosPolicy->v1.dscpCOutTCPv6;
         } else {
            VvcError("%s: dscpCOutTCP: Unknown/unsupported AddrFamily: %d\n", __FUNCTION__,
                     addrFamily);
         }
      }
   } else { // UDP
      if (isServer) {
         if (addrFamily == AF_INET) {
            dscpValue = session->qosPolicy->v1.dscpAOutUDPv4;
         } else if (addrFamily == AF_INET6) {
            dscpValue = session->qosPolicy->v1.dscpAOutUDPv6;
         } else {
            VvcError("%s: dscpAOutTCP: Unknown/unsupported AddrFamily: %d\n", __FUNCTION__,
                     addrFamily);
         }
      } else {
         if (addrFamily == AF_INET) {
            dscpValue = session->qosPolicy->v1.dscpCOutUDPv4;
         } else if (addrFamily == AF_INET6) {
            dscpValue = session->qosPolicy->v1.dscpCOutUDPv6;
         } else {
            VvcError("%s: dscpCOutTCP: Unknown/unsupported AddrFamily: %d\n", __FUNCTION__,
                     addrFamily);
         }
      }
   }

   return dscpValue;
}


/*
 *----------------------------------------------------------------------------
 *
 * VvcOnAsockBackendConnected --
 *
 *      If we are recovering from a Network going down, we need to trigger
 *      Retransmission of VvcMsgs from MptSendQ.
 *      Client-Side: Trigger from AddAsockBe func when a new Asock is pushed.
 *      Agent-Side: Trigger from the first recvCb on a newly pushed Asock.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

void
VvcOnAsockBackendConnected(VvcSession *session) // IN
{
   Bool isSessionLocked = FALSE;

   ASSERT(session);

   isSessionLocked = ISLOCKED_SESSION(session);

   if (!isSessionLocked) {
      LOCK_SESSION(session);
   }

   if (!session->asockXBeDown || !VvcSessionIsUp(session)) {
      goto done;
   }

   if (!session->negotiatedDoChannelResync) {
      session->asockXBeDown = FALSE;
      session->asockXBeConnectNeeded = FALSE;
      goto done;
   }

   VvcLog("%s:START "
          "negotiatedDoChannelResync=%s asockXBeDown=%s\n",
          __FUNCTION__, session->negotiatedDoChannelResync ? "TRUE" : "FALSE",
          session->asockXBeDown ? "TRUE" : "FALSE");

   if (!(session->transportBe.flags & VVC_TRANSPORT_BE_SERVER)) {
      /* Client-side will send a dup MPT ACK to trigger RecvCb
       * on Agent-side.
       */
      VvcSendChannelMPTDupAckNow(session->ctrlChannel, VvcMsgPinToDefault);
   }

   /*
    * If some channels want to receive network resumed notification,
    * enqueue the onResume events on to the channels.
    */
   VvcQueuePauseResumeEvents(session, VvcEvChannelOnResume);

   /* Send notification to SendQ manager.
    * The VvcNewAsockBackendConnected() will also flip session->asockXBeDown
    */
   VvcNewAsockBackendConnected(session);

   VvcLog("%s: END "
          "negotiatedDoChannelResync=%s asockXBeDown=%s\n",
          __FUNCTION__, session->negotiatedDoChannelResync ? "TRUE" : "FALSE",
          session->asockXBeDown ? "TRUE" : "FALSE");

done:
   if (!isSessionLocked) {
      UNLOCK_SESSION(session);
   }
}


/*
 *----------------------------------------------------------------------------
 *
 * VvcOnAsockBackendDisconnected --
 *
 *      When errorCb on the AsockBe is received, notify the MptSendQ
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

void
VvcOnAsockBackendDisconnected(VvcSession *session) // IN
{
   Bool isSessionLocked = FALSE;
   DblLnkLst_Links *link, *nextElem;
   VvcInstance *instance;

   ASSERT(session);
   ASSERT(session->instance);

   isSessionLocked = ISLOCKED_SESSION(session);

   if (!isSessionLocked) {
      LOCK_SESSION(session);
   }

   instance = session->instance;

   VvcLog("%s: START "
          "asockXbeDown=%s, negotiatedDoChannelResync=%s\n",
          __FUNCTION__, session->asockXBeDown == TRUE ? "TRUE" : "FALSE",
          session->negotiatedDoChannelResync == TRUE ? "TRUE" : "FALSE");

   /*
    * Set the session-wide asockXBeDown flag, which will prevent additional
    * channel messages from being queued, as well as letting various session
    * error handler know that a (hopefully) temporary network failure is in
    * effect. This flag will be cleared once the network comes back up.
    */
   session->asockXBeDown = TRUE;

   /*
    * Reset the asockXBeConnectNeeded flag to prevent treating any receive
    * as if from a new asockBe until all the receives from current asockBes
    * are cancelled.
    */
   session->asockXBeConnectNeeded = FALSE;

   /*
    * Purge send tree now - AbortChannel needs this to make sure no messages
    * can go in the scheduler.
    */
   VvcPurgeSendTree(session);

   if (!session->negotiatedDoChannelResync) {
      goto done;
   }

   /*
    * Purge sendQueue of channels that have declined NC
    */
   VvcLog("%s: Purging sendQueues of channels that have declined NC, "
          "sessionId: %d\n",
          __FUNCTION__, session->sessionId);

   LOCK_INSTANCE(instance);
   DblLnkLst_ForEachSafe(link, nextElem, &session->channelList)
   {
      VvcChannel *channel = DblLnkLst_Container(link, VvcChannel, sessionLink);
      VVC_ADD_REF_CHANNEL(channel, VvcTagPurgeChannelSendQueues);
      if (channel->flags & VVC_CHANNEL_DECLINE_NC) {
         VvcPurgeSendQueue(channel);
         VvcPurgeMptSendQueue(channel);
      }
      VVC_RELEASE_CHANNEL(channel, VvcTagPurgeChannelSendQueues);
   }
   UNLOCK_INSTANCE(instance);

   /*
    * Dispatch events for ex: send complete callbacks that might be
    * queued after VvcMsgs are purged as part of send tree purging
    * for all channels or send queue and mptSend queue purging for
    * NC declined channels.
    */
   VvcDispatchEvents(session->instance);

   /*
    * If some channels want to receive network paused notifications,
    * enqueue the onPause events on to the channels.
    */
   VvcQueuePauseResumeEvents(session, VvcEvChannelOnPause);

done:
   if (!isSessionLocked) {
      UNLOCK_SESSION(session);
   }

   VvcLog("%s END "
          "asockXbeDown=%s, negotiatedDoChannelResync=%s\n",
          __FUNCTION__, session->asockXBeDown == TRUE ? "TRUE" : "FALSE",
          session->negotiatedDoChannelResync == TRUE ? "TRUE" : "FALSE");
}


/*
 *----------------------------------------------------------------------------
 *
 * VvcInvokeSessionTransportSendComplete --
 *
 *      Invoke either the CancelSafe version or normal version of
 *      VVCLIB_SessionTransportSendComplete, depending on the transportBe
 *      flags.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

void
VvcInvokeSessionTransportSendComplete(VvcSession *session, // IN
                                      void *sccBatcher,    // IN
                                      uint8 *buf,          // IN
                                      size_t bufLen,       // IN
                                      VvcStatus status)    // IN
{
   // Tell VVC that the send has completed
   if (VvcIsCancelSafeSend(session)) {
      VVCLIB_SessionTransportCancelSafeSendComplete(sccBatcher, status, buf, bufLen);
   } else {
      VVCLIB_SessionTransportSendComplete(sccBatcher, status, buf, bufLen);
   }
}


/*
 *----------------------------------------------------------------------------
 *
 * VvcMultiAsockBackendSendCompleteCb --
 *
 *      Notification that the async socket is finished with our buffer.
 *      ClientData is the VvcAsockBeCbContext structure, that has complContext
 *      that VVC needs, and it also has the VvcAsockBackend (which
 *      has the other useful members like asyncSocketLock)
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      Frees memory.
 *
 *----------------------------------------------------------------------------
 */

static void
VvcMultiAsockBackendSendCompleteCb(void *buf,          // IN
                                   int len,            // IN
                                   AsyncSocket *asock, // IN
                                   void *cbData)       // IN
{
   VvcAsockBackend *asockBackend = NULL;
   VvcAsockBeCbContext *vvcAsockBeCbContext = cbData;
   VvcSession *session;
   VvcSCCBatcher *sccBatcher;
   int relockCount = 0;

   ASSERT(vvcAsockBeCbContext != NULL);

   asockBackend = vvcAsockBeCbContext->asockBe;
   vvcAsockBeCbContext->asockBe = NULL;
   ASSERT(asockBackend);
   session = asockBackend->session;
   ASSERT(session);
   sccBatcher = (VvcSCCBatcher *)vvcAsockBeCbContext->sccBatcher;

   /*
    * XXX Because we release and reacquire socket lock, another thread may come
    * in right after the lock is released and free the asockBackend on us. To
    * prevent that from happening, take an extra reference count before we do
    * that.
    */
   VVC_ADD_REF_ASOCKBE(asockBackend, VvcTagAsockBeInAsockCb);
   VvcMultiAsockBackendReleaseSocketLock(asockBackend->asockLock);

   /*
    * Close path takes an additional socket lock after setting asock to NULL
    * (refer comment in VvcAsockBackendClose). An additional nested lock is
    * possible taking this path:  recvCb->errorCb->close->sendCompleteCb
    * Release/reacquire socket locks to maintain ordering wrt the session lock.
    */
   if (UNLIKELY(asockBackend->asock == NULL)) {
      while (VvcMultiAsockBackendIsSocketLocked(asockBackend->asockLock)) {
         VvcMultiAsockBackendReleaseSocketLock(asockBackend->asockLock);
         relockCount++;
      }
      ASSERT(relockCount <= 2);
   } else {
      VvcDispatchDataSockActivatedCb(asockBackend, FirstAsockSendCompleted);
   }

   VvcTrace("%s: sent:%lu, buffer:%p, sccBatcher:%p, numChunks: %d\n", __FUNCTION__,
            (unsigned long)len, buf, sccBatcher, sccBatcher->numSCCs);

   VvcInvokeSessionTransportSendComplete(session, sccBatcher, (uint8 *)buf, len,
                                         VVC_STATUS_SUCCESS);

   while (relockCount--) {
      VvcMultiAsockBackendAcquireSocketLock(asockBackend->asockLock);
   }
   VvcMultiAsockBackendAcquireSocketLock(asockBackend->asockLock);
   VVC_RELEASE_ASOCKBE(asockBackend, VvcTagAsockBeInAsockCb);

   free(vvcAsockBeCbContext);
}


/*
 *----------------------------------------------------------------------------
 *
 * VvcMultiAsockBackendSend --
 *
 *      The callback invoked by VVC to emit a buffer onto the AsyncSocket.
 *
 *      clientData is the VvcAsockBackend tied to this send.
 *
 * Results:
 *      VvcStatus.
 *
 * Side effects:
 *      The content of the buffer is fed to the AsyncSocket for transmission.
 *
 *----------------------------------------------------------------------------
 */

VvcStatus
VvcMultiAsockBackendSend(uint8 *buf,       // IN
                         size_t len,       // IN
                         void *batcher,    // IN
                         void *clientData) // IN
{
   VvcStatus status = VVC_STATUS_SUCCESS;
   int sendAsockID = 0;
   VvcAsockBackend *asockBackend = NULL;
   VvcSession *session = clientData;
   Bool doChannelResync;
   int32 sessionId;
   VvcSCCBatcher *sccBatcher = (VvcSCCBatcher *)batcher;
   int usefulSendBytes = 0;

   ASSERT(session != NULL);
   ASSERT(!ISLOCKED_SESSION(session));

   doChannelResync = session->negotiatedDoChannelResync;
   sessionId = session->sessionId;

   sendAsockID = sccBatcher->sendAsockID;
   usefulSendBytes = sccBatcher->usefulSendBytes;

   if (sendAsockID != INVALID_ASOCKID) {
      asockBackend = VvcGetAsockBackendFromAsockID(session, sendAsockID);
   }

   // Increment usefulSendBytes now and roll back if send fails.
   VvcIncrementSessionSentProtocolBytes(session, sendAsockID, usefulSendBytes);

   if (asockBackend) {
      int err;
      VvcAsockBeCbContext *vvcAsockBeCbContext = Util_SafeCalloc(1, sizeof *vvcAsockBeCbContext);

      ASSERT(asockBackend->asock != NULL);

      vvcAsockBeCbContext->asockBe = asockBackend;
      vvcAsockBeCbContext->asockLock = asockBackend->asockLock;
      vvcAsockBeCbContext->sccBatcher = sccBatcher;

      VvcTrace("%s: send:%" FMTSZ "u, usefulSendBytes:%d, buffer:%p, "
               "sccBatcher:%p, VvcSession ID:%d, "
               "asock: %d (%p)\n",
               __FUNCTION__, len, usefulSendBytes, buf, sccBatcher, sessionId,
               asockBackend->asockID, asockBackend->asock);

      err = AsyncSocket_Send(asockBackend->asock, buf, (int)len, VvcMultiAsockBackendSendCompleteCb,
                             (void *)vvcAsockBeCbContext);

      if (err == ASOCKERR_SUCCESS) {
         asockBackend->lastSendTs = Hostinfo_SystemTimerUS();
         VvcTrace("[VVC Heartbeats] Updating lastSendTs on asock %d to be: %" FMT64 "u\n",
                  asockBackend->asockID, asockBackend->lastSendTs);
      } else {
         free(vvcAsockBeCbContext);
         VvcError("%s: Could not send data, asock: %d (%p) due to transport "
                  "error\n",
                  __FUNCTION__, asockBackend->asockID, asockBackend->asock);
         /*
          * Returning VVC_STATUS_ERROR here reports session error. However,
          * if asock error callback is in progress, let that take priority
          * by returning VVC_STATUS_TRANSPORT_ERROR.
          */
         if (Atomic_Read32(&asockBackend->errorCbInProgress) > 0) {
            status = VVC_STATUS_TRANSPORT_ERROR;
         } else {
            status = VVC_STATUS_ERROR;
         }
      }

      VVC_RELEASE_ASOCKBE(asockBackend, VvcTagAsockBeGeneric);
      asockBackend = NULL;
   } else {
      if (doChannelResync) {
         VvcDebug("%s: SessionID:%d: No valid AsockBackend. Pretend it's "
                  "immediately sent and complete SendCb now.\n",
                  __FUNCTION__, sessionId);

         VvcInvokeSessionTransportSendComplete(session, sccBatcher, buf, len, VVC_STATUS_SUCCESS);
      } else {
         VvcDebug("%s: SessionID:%d: No valid AsockBackend and channel resync "
                  "is off. Could not send data.\n",
                  __FUNCTION__, sessionId);
         status = VVC_STATUS_NOT_FOUND;
      }
   }

   if (!VVC_SUCCESS(status)) {
      /* Roll back send counters */
      VvcIncrementSessionSentProtocolBytes(session, sendAsockID, -usefulSendBytes);
   }

   return status;
}


/*
 *----------------------------------------------------------------------------
 *
 * VvcAsockBackendRecvCb
 *
 *      The AsyncSocket Callback - Got len bytes in buf from asock.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      Messages extracted from received data stream.
 *
 *----------------------------------------------------------------------------
 */

static void
VvcAsockBackendRecvCb(void *buf,          // IN
                      int len,            // IN
                      AsyncSocket *asock, // IN
                      void *cbData)       // IN
{
   VvcAsockBackend *asockBe = cbData;
   VvcSession *session = (VvcSession *)asockBe->session;
   VvcRecvState *recvState = NULL;
   size_t recvBufDataLen = len;
   VvcStatus status = VVC_STATUS_SUCCESS;
   VvcAsockBackend *activeAsockBe;
   Bool sessionUp = FALSE;

   ASSERT(asockBe);
   ASSERT(session);
   ASSERT(len >= 0);


   if (!VvcAsockBackendValidateAsock(asock, asockBe)) {
      VvcDebug("%s: Asock %d: Got receive callback when Asockbackend is "
               "already removed!\n",
               __FUNCTION__, AsyncSocket_GetID(asock));
      return;
   }

   /*
    * XXX Release and reacquire socket lock.
    */
   VVC_ADD_REF_ASOCKBE(asockBe, VvcTagAsockBeInAsockCb);
   VvcMultiAsockBackendReleaseSocketLock(asockBe->asockLock);

   if (asockBe->isVVCHeartbeatEnabled) {
      asockBe->lastRecvTs = Hostinfo_SystemTimerUS();
      VvcTrace("[VVC Heartbeats] Updating lastRecvTs on asock %d to be: %" FMT64 "u\n",
               asockBe->asockID, asockBe->lastRecvTs);
   }

   VvcDispatchDataSockActivatedCb(asockBe, FirstAsockRecvSeen);

   activeAsockBe = VvcGetActiveAsockBackend(session);
   if (activeAsockBe != NULL && !session->negotiatedDoConcurrentTransports &&
       asockBe != activeAsockBe) {
      VvcTrace("%s: Received data on non-active AsockBackend, ignored.\n", __FUNCTION__);
      VVC_RELEASE_ASOCKBE(activeAsockBe, VvcTagAsockBeGeneric);
      goto out;
   }
   if (activeAsockBe != NULL) {
      VVC_RELEASE_ASOCKBE(activeAsockBe, VvcTagAsockBeGeneric);
      activeAsockBe = NULL;
   }

   VvcTrace("%s: Asock %d got %lu bytes, buf:%p asockBe %p\n", __FUNCTION__, asockBe->asockID,
            (unsigned long)len, buf, asockBe);

   LOCK_SESSION(session);

   if (!asockBe->isRemoved && session->asockXBeConnectNeeded &&
       (session->transportBe.flags & VVC_TRANSPORT_BE_SERVER)) {
      /*
       * If we're the server then we now know that a new asock is connected, so
       * prepare for recovery.
       */
      VvcOnAsockBackendConnected(session);
   }

   /*
    * Note that asockBe->recvState might be freed and set to NULL in
    * VvcDropPartiallyReceivedMessages, which is done with the session lock. So
    * guard against that here.
    */
   recvState = asockBe->recvState;
   if (recvState == NULL) {
      VvcDebug("%s: recvState of Asock %d is NULL, the asock is probably "
               "down. Dropping the receive.\n",
               __FUNCTION__, asockBe->asockID);

      UNLOCK_SESSION(session);
      goto out;
   }

   /*
    * We passed recvState->recvBuf + recvState->recvBufOffset to Asock recv so
    * verify that.
    */
   ASSERT(recvState->recvBuf + recvState->recvBufOffset == buf);

   sessionUp = VvcSessionIsUp(session);

   if (sessionUp) {
      VvcTransportReceiveAndAck(session, recvState, recvState->recvBuf, recvBufDataLen,
                                recvState->recvBufSize);

      // Ref session before unlocking session, in case session goes away in an
      // error path.
      VVC_ADD_REF_SESSION(session, VvcTagAsyncReRead);
   }

   UNLOCK_SESSION(session);

   if (sessionUp) {
      status = VvcAsockBackendRecv(session, asockBe);
   } else {
      VvcDebug("%s: Session not up, canceling receives\n", __FUNCTION__);
      if (AsyncSocket_CancelRecvEx(asockBe->asock, NULL, NULL, NULL, TRUE) != ASOCKERR_SUCCESS) {
         VvcError("%s: Failed to cancel further Asyncsocket recvs\n", __FUNCTION__);
         status = VVC_STATUS_CLOSED;
      }
   }

   if (!VVC_SUCCESS(status)) {
      LOCK_SESSION(session);
      VvcSessionErrorHandler(session, status);
      UNLOCK_SESSION(session);
   }

   if (sessionUp) {
      VvcDispatchSendQueues(session, VvcDispatchSendTriggerTransportRecvComplete);
      VVC_RELEASE_SESSION(session, VvcTagAsyncReRead);
   }

   VvcDispatchEvents(session->instance);

out:
   VvcMultiAsockBackendAcquireSocketLock(asockBe->asockLock);
   VVC_RELEASE_ASOCKBE(asockBe, VvcTagAsockBeInAsockCb);
}


/*
 *----------------------------------------------------------------------------
 *
 * VvcAsockBackendRecv --
 *
 *      This function is invoked to kick off the first recv of the AsyncSocket
 *      added via VVCLIB_AddAsockBackend().
 *      TODO: OK, but that's not the only place this is called. Provide more
 *      general explanation here?
 *
 * Results:
 *      VvcStatus.
 *
 * Side effects:
 *      The buffer is made available to the AsyncSocket.
 *
 *----------------------------------------------------------------------------
 */

VvcStatus
VvcAsockBackendRecv(VvcSession *session, VvcAsockBackend *asockBe)
{
   VvcRecvState *recvState;
   int err;
   size_t recvOffset;

   ASSERT(session != NULL);
   ASSERT(session == asockBe->session);
   ASSERT(asockBe && asockBe->asock);

   ASSERT(!ISLOCKED_SESSION(session));

   VvcMultiAsockBackendAcquireSocketLock(asockBe->asockLock);
   if (asockBe->isRemoved == TRUE) {
      VvcDebug("%s: asockBe already removed, asock id - %d\n", __FUNCTION__, asockBe->asockID);
      VvcMultiAsockBackendReleaseSocketLock(asockBe->asockLock);
      return VVC_STATUS_SUCCESS;
   }

   ASSERT(asockBe->recvState != NULL);
   recvState = asockBe->recvState;
   recvOffset = recvState->recvBufOffset;
   ASSERT(recvOffset < recvState->recvBufSize);

   err =
      AsyncSocket_RecvPartial(asockBe->asock, recvState->recvBuf + recvOffset,
                              recvState->recvBufSize - recvOffset, VvcAsockBackendRecvCb, asockBe);

   VvcMultiAsockBackendReleaseSocketLock(asockBe->asockLock);
   return (err == ASOCKERR_SUCCESS ? VVC_STATUS_SUCCESS : VVC_STATUS_TRANSPORT_ERROR);
}


/*
 *----------------------------------------------------------------------------
 *
 * VvcMultiAsockBackendClose --
 *
 *      The callback invoked by VVC to close the transport.
 *
 * Results:
 *      VvcStatus.
 *
 * Side effects:
 *      The buffer is made available to the AsyncSocket.
 *
 *----------------------------------------------------------------------------
 */

VvcStatus
VvcMultiAsockBackendClose(void *clientData) // IN
{
   VvcSession *session = clientData;
   int32 numAsockBeRemoved = 0;

   ASSERT(session != NULL);

   VvcLog("%s: session %d\n", __FUNCTION__, session->sessionId);

   VvcSetSessionCloseReason(session, VvcSessionCloseNormal);

   /*
    * TransportBe.close gets invoked from VVCLIB_CloseSession.
    */

   VvcTrace("For VvcSession:%p, removing AsockBackends.\n", session);

   VvcRemoveAllAsockBackends(session, FALSE, &numAsockBeRemoved);
   VvcDisableBandwidthEstimation(session);

   VvcTrace("For VvcSession:%p, removed AsockBackends.\n", session);

   if (session->negotiatedDoConcurrentTransports) {
      VvcDataTransportSwitch_Stop(session);
   }

   return VVC_STATUS_SUCCESS;
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcMultiAsockBackendCloseOnSessionError --
 *
 *    Wrapper to call VvcMultiAsockBackendClose without a lock.
 *
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

void
VvcMultiAsockBackendCloseOnSessionError(VvcSession *session) // IN
{
   Bool isSessionLocked = FALSE;

   isSessionLocked = ISLOCKED_SESSION(session);

   if (isSessionLocked) {
      UNLOCK_SESSION(session);
   }

   VvcMultiAsockBackendClose(session);

   if (isSessionLocked) {
      LOCK_SESSION(session);
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcNwStatsFromAsockStats --
 *
 *    Derive b/w and save asyncsocket stats.
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

void
VvcNwStatsFromAsockStats(AsyncSocketNetworkStats *asockStats, // IN
                         VvcNetworkStats *networkStats)       // OUT
{
   networkStats->rttSeconds = asockStats->rttSmoothedAvgMillis * .001;
   networkStats->rttVarianceSeconds = asockStats->rttSmoothedVarMillis * .001;
   networkStats->bandwidthBytesPerSecond = asockStats->cwndBytes / networkStats->rttSeconds;
   networkStats->queuedBytes = asockStats->queuedBytes;
   networkStats->inflightBytes = asockStats->inflightBytes;
   networkStats->packetLossPercent = asockStats->packetLossPercent;
}


/*
 *----------------------------------------------------------------------------
 *
 * VvcMultiAsockBackendGetNwStats --
 *
 *      The callback invoked by VVC to get network statistics from transport.
 *
 * Results:
 *      VvcStatus.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

VvcStatus
VvcMultiAsockBackendGetNwStats(void *clientData,              // IN
                               VvcNetworkStats *networkStats) // OUT
{
   VvcAsockBackend *asockBackend = NULL;
   VvcSession *session = clientData;
   VvcStatus status = VVC_STATUS_NOT_FOUND;

   VvcVerbose("%s: VvcSession:%p %d\n", __FUNCTION__, session, session->sessionId);

   ASSERT(session != NULL);
   ASSERT(ISLOCKED_SESSION(session));

   memset(networkStats, 0, sizeof *networkStats);

   /*
    * Query network stats from transport backend only if it is an end to end
    * connection.
    */
   asockBackend = VvcGetAsockBackend(session);
   if (!asockBackend) {
      // This can happen when network is down and we're in the middle of NC
      return VVC_STATUS_NOT_FOUND;
   }

   networkStats->remotePort = asockBackend->remotePort;

   if (asockBackend->isEndToEndConnection) {
      AsyncSocketNetworkStats asockStats;
      int ret;

      ASSERT(asockBackend->asock != NULL);

      ret = AsyncSocket_GetNetworkStats(asockBackend->asock, &asockStats);
      if (ret == ASOCKERR_SUCCESS) {
         VvcNwStatsFromAsockStats(&asockStats, networkStats);
         status = VVC_STATUS_SUCCESS;
      } else {
         VvcTrace("Failed to get network stats from "
                  "AsyncSocket, AsockErr: %d\n",
                  ret);
         if (ret == ASOCKERR_GENERIC) {
            VvcTrace("Received ASOCKERR_GENERIC, error:%d\n",
                     AsyncSocket_GetGenericErrno(asockBackend->asock));
         }

         // Return VVC_STATUS_ERROR if NW Stats API failed
         status = VVC_STATUS_ERROR;
      }
   } else {
      /*
       * If current Active AsockBe can not estimate Bw, then get
       * the networkStats from Vvc's Bw Estimator.
       */

      networkStats->bandwidthBytesPerSecond = VvcBandwidthDetection_GetImmediateBandwidth(
         session->bwDetection, VVC_BANDWIDTH_APPLY_MULTIPLIER | VVC_BANDWIDTH_APPLY_MINIMUM_RATE);
      networkStats->rttSeconds = VvcBandwidthDetection_GetSmoothedRTT(session->bwDetection);
      /*
       * rttVariance & packetLoss are not calculated & exposed by VVC's
       * Bw Estimator.
       */
      networkStats->rttVarianceSeconds = 0;
      networkStats->packetLossPercent = 0;

      status = VVC_STATUS_SUCCESS;
   }

   VVC_RELEASE_ASOCKBE(asockBackend, VvcTagAsockBeGeneric);

   return status;
}


/*
 *----------------------------------------------------------------------------
 *
 * VvcMultiAsockBackendGetSNIHostname --
 *
 *      The callback invoked by VVC to get SNI hostname from backend socket.
 *
 * Results:
 *      VvcStatus.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

VvcStatus
VvcMultiAsockBackendGetSNIHostname(void *clientData,         // IN
                                   const char **sniHostname) // OUT
{
   VvcStatus status = VVC_STATUS_NOT_FOUND;
#ifdef BENEV
   int ret;
   VvcAsockBackend *asockBackend = NULL;
   VvcSession *session = clientData;

   VvcVerbose("%s: VvcSession:%p %d\n", __FUNCTION__, session, session->sessionId);

   ASSERT(session != NULL);
   ASSERT(ISLOCKED_SESSION(session));

   asockBackend = VvcGetAsockBackend(session);
   if (!asockBackend) {
      // This can happen when network is down and we're in the middle of NC
      return VVC_STATUS_NOT_FOUND;
   }

   ASSERT(asockBackend->asock != NULL);

   ret = AsyncSocket_GetSNIHostname(asockBackend->asock, sniHostname);
   if (ret == ASOCKERR_SUCCESS) {
      status = VVC_STATUS_SUCCESS;
   } else {
      VvcTrace("Failed to get SNI hostname from "
               "AsyncSocket, AsockErr: %d\n",
               ret);
      if (ret == ASOCKERR_GENERIC) {
         VvcTrace("Received ASOCKERR_GENERIC, error:%d\n",
                  AsyncSocket_GetGenericErrno(asockBackend->asock));
      }

      // Return VVC_STATUS_ERROR if SNI hostname API failed
      status = VVC_STATUS_ERROR;
   }

   VVC_RELEASE_ASOCKBE(asockBackend, VvcTagAsockBeGeneric);

#endif
   return status;
}


/*
 *----------------------------------------------------------------------------
 *
 * VvcMultiAsockBackendCancelRecv --
 *
 *      The callback invoked by VVC to notify transport to cancel further
 *      recvs.
 *
 * Results:
 *      VvcStatus.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

VvcStatus
VvcMultiAsockBackendCancelRecv(void *clientData) // IN
{
   VvcStatus status = VVC_STATUS_SUCCESS;
   VvcAsockBackend *asockBackend;
   VvcSession *session = clientData;

   ASSERT(session != NULL);

   VvcLog("VvcMultiAsockBackendCancelRecv()\n");

   asockBackend = VvcGetAsockBackend(session);

   VvcDebug("VvcMultiAsockBackendCancelRecv, VvcSession:%p\n", session);

   if (asockBackend) {
      VvcTrace("Cancelling further Asyncsocket recvs\n");

      ASSERT(asockBackend->asock);
      if (AsyncSocket_CancelRecvEx(asockBackend->asock, NULL, NULL, NULL, TRUE) !=
          ASOCKERR_SUCCESS) {
         VvcError("Failed to cancel further Asyncsocket recvs\n");
         status = VVC_STATUS_ERROR;
      }
      VVC_RELEASE_ASOCKBE(asockBackend, VvcTagAsockBeGeneric);
   } else {
      status = VVC_STATUS_NOT_FOUND;
   }

   return status;
}


/*
 *----------------------------------------------------------------------------
 *
 * VvcMultiAsockBackendGetSetupMsg --
 *
 *      Get BEAT setup msg from AsyncSocket.
 *
 * Results:
 *      VvcStatus.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

VvcStatus
VvcMultiAsockBackendGetSetupMsg(void *clientData, // IN
                                char **setupMsg,  // OUT
                                int *len)         // OUT
{
   VvcStatus status = VVC_STATUS_ERROR;
   VvcAsockBackend *asockBackend;
   VvcSession *session = clientData;
   Bool ok;

   ASSERT(session != NULL);
   ASSERT(setupMsg != NULL);

   asockBackend = VvcGetAsockBackend(session);
   if (asockBackend == NULL) {
      return VVC_STATUS_ERROR;
   }

   ASSERT(asockBackend->asock);

   VvcDebug("%s: build setup message for beat raw channel with asock %d\n", __FUNCTION__,
            AsyncSocket_GetID(asockBackend->asock));

   *len = 0;

   ASSERT(asockBackend->getSetupMsgCb);
   if (asockBackend->getSetupMsgCb) {
      ok = asockBackend->getSetupMsgCb(asockBackend->getAuxFlowInfoCbData, setupMsg, len);
      if (UNLIKELY(!ok)) {
         VvcError("%s: failed to obtain beat setup message\n", __FUNCTION__);
      } else {
         VvcDebug("%s: obtained beat raw channel setup message of length %d\n", __FUNCTION__, *len);
         status = VVC_STATUS_SUCCESS;
      }
   }

   VVC_RELEASE_ASOCKBE(asockBackend, VvcTagAsockBeGeneric);
   return status;
}


/*
 *----------------------------------------------------------------------------
 *
 * VvcMultiAsockBackendTcpConnect --
 *
 *      Used on the client side to invoke blastSockets' TCP connect to peer.
 *      This was introduced for TCP raw channels.
 *
 * Results:
 *      TRUE on success, FALSE otherwise
 *
 * Side effects:
 *      Connection is initiated here, the end result is async and will be
 *      notified by calling VVCLIB_RawChanConnectCb.
 *
 *----------------------------------------------------------------------------
 */

Bool
VvcMultiAsockBackendTcpConnect(void *clientData,       // IN
                               unsigned char serialNo, // IN
                               void *cbData,           // IN
                               void *connectionCookie) // IN
{
   VvcAsockBackend *asockBackend;
   VvcSession *session = clientData;
   Bool ret = FALSE;

   ASSERT(session != NULL);

   asockBackend = VvcGetAsockBackend(session);
   if (asockBackend == NULL) {
      return ret;
   }

   ASSERT(asockBackend->asock);
   VvcLog("%s: Tcp raw channel connect: asock %d, nonce 0x%x\n", __FUNCTION__,
          AsyncSocket_GetID(asockBackend->asock), session->nonce);

   ASSERT(asockBackend->tcpConnect);
   if (asockBackend->tcpConnect) {
      ret = asockBackend->tcpConnect(asockBackend->tcpConnectData, cbData, connectionCookie,
                                     session->nonce, serialNo);
      if (UNLIKELY(!ret)) {
         VvcError("%s: connect failed\n", __FUNCTION__);
      } else {
         VvcDebug("%s: connect initiated for raw channel\n", __FUNCTION__);
      }
   }

   VVC_RELEASE_ASOCKBE(asockBackend, VvcTagAsockBeGeneric);
   return ret;
}


/*
 *----------------------------------------------------------------------------
 *
 * VvcMultiAsockBackendVerifySetupMsg --
 *
 *      Verify BEAT setup msg from AsyncSocket.
 *
 * Results:
 *      VvcStatus.
 *
 * Side effects:
 *      After entire setup msg is received and validated, authenticated asock
 *      is pushed down to VVC and claimed by the respective raw channel.
 *
 *----------------------------------------------------------------------------
 */

VvcStatus
VvcMultiAsockBackendVerifySetupMsg(void *clientData,   // IN
                                   AsyncSocket *asock) // IN
{
   VvcAsockBackend *asockBackend;
   VvcSession *session = clientData;

   ASSERT(session != NULL);

   asockBackend = VvcGetAsockBackend(session);
   if (asockBackend == NULL) {
      return VVC_STATUS_ERROR;
   }

   ASSERT(asockBackend->asock);
   ASSERT(asockBackend->verifySetupMsgCb);
   ASSERT(asockBackend->verifySetupMsgCbCb);

   VvcDebug("%s: verify setup message for beat raw channel with asock %d\n", __FUNCTION__,
            AsyncSocket_GetID(asockBackend->asock));

   if (asockBackend->verifySetupMsgCb) {
      asockBackend->verifySetupMsgCb(asock, asockBackend->verifySetupMsgCbCb,
                                     asockBackend->verifySetupMsgCbCbData, 0 /* NA */);
      VvcDebug("%s: initiated beat raw channel setup message verification", __FUNCTION__);
   }

   VVC_RELEASE_ASOCKBE(asockBackend, VvcTagAsockBeGeneric);
   return VVC_STATUS_SUCCESS;
}


/*
 *----------------------------------------------------------------------------
 *
 * VvcMultiAsockBackendGetSslCtx --
 *
 *      Indirection to ultimately get ssl context from blastsockets layer.
 *      Primarily used for SSL server side listening sockets.
 *
 * Results:
 *      SSL context pointer or NULL on failure.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

void *
VvcMultiAsockBackendGetSslCtx(void *clientData) // IN
{
   VvcAsockBackend *asockBackend;
   VvcSession *session = clientData;

   ASSERT(session != NULL);
   asockBackend = VvcGetAsockBackend(session);
   if (!asockBackend || !asockBackend->getSslCtxCb) {
      return NULL;
   }

   return asockBackend->getSslCtxCb(asockBackend->getSslCtxCbData);
}


/*
 *----------------------------------------------------------------------------
 *
 * VvcMultiAsockBackendGetAuxiliaryFlowInfo --
 *
 *      Get Auxiliary Flow Info from AsyncSocket.
 *      Also used to get client side SSL verification params.
 *
 * Results:
 *      VvcStatus.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */
VvcStatus
VvcMultiAsockBackendGetAuxiliaryFlowInfo(void *clientData,                        // IN
                                         VvcAuxiliaryFlowInfo *auxiliaryFlowInfo, // OUT
                                         SSLVerifyParam *sslParams)               // OUT/OPT
{
   VvcStatus status = VVC_STATUS_ERROR;
   Bool valid;
   VvcAsockBackend *asockBackend;
   VvcSession *session = clientData;

   ASSERT(session != NULL);

   VvcLog("%s: VvcSession:%d\n", __FUNCTION__, session->sessionId);

   asockBackend = VvcGetAsockBackend(session);
   if (asockBackend == NULL) {
      return VVC_STATUS_ERROR;
   }

   if (asockBackend->getAuxFlowInfoCb) {
      /*
       * VVC in-proc plugin queries for AuxFlowInfo via VVCLIB_GetInfo()
       * The workflow is as follows:->
       * In-proc-plugins call vvcIntf->getInfo(VvcInfoAuxiliaryFlowInfo)
       *    -> transportBe.getAuxiliaryFlowInfo
       *       -> VvcMultiAsockBackendGetAuxiliaryFlowInfo
       *          -> getAuxFlowInfoCb to retrieve the auxFlowInfo from
       *             BlastSockets layer
       */

      BlastSocketClient_AuxiliaryFlowInfo opts;
      socklen_t optsLen;

      optsLen = sizeof opts;
      valid = asockBackend->getAuxFlowInfoCb(asockBackend->getAuxFlowInfoCbData, &opts, &optsLen,
                                             sslParams);

      if (valid) {
         if (sizeof auxiliaryFlowInfo->afiHMACKeyBuf < opts.afioHMACKeySize) {
            VvcError("HMAC key too big (%u) for AuxiliaryFlowInfo\n", opts.afioHMACKeySize);
            status = VVC_STATUS_ERROR;
            goto exit;
         }
         if (sizeof auxiliaryFlowInfo->afiDest <= strlen(opts.afioDest)) {
            VvcError("dest string too big (%" FMTSZ "u)"
                     " for AuxiliaryFlowInfo\n",
                     strlen(opts.afioDest));
            status = VVC_STATUS_ERROR;
            goto exit;
         }
         if (sizeof auxiliaryFlowInfo->afiHMACAlgo <= strlen(opts.afioHMACAlgo)) {
            VvcError("HMAC algo string too big (%" FMTSZ "u)"
                     " for AuxiliaryFlowInfo\n",
                     strlen(opts.afioHMACAlgo));
            status = VVC_STATUS_ERROR;
            goto exit;
         }

         /*
          * Coverity insists that we check that the keySize in the opts
          * response does not exceed the size of the opts keyBuf, because
          * we will eventually use the keySize value to specify the size
          * of a memcpy from the opts keyBuf.  For completeness, also
          * check that keySize does not exceed the size of the
          * destination KeyBuf.
          */
         if (opts.afioHMACKeySize >
             MIN(sizeof opts.afioHMACKeyBuf, sizeof auxiliaryFlowInfo->afiHMACKeyBuf)) {
            VvcError("ERROR: HMAC key size too big (%u) in"
                     " AuxiliaryFlowInfoOption\n",
                     opts.afioHMACKeySize);
            status = VVC_STATUS_ERROR;
            goto exit;
         }

         /*
          * Start by zeroing out the entire destination struct, to
          * remove noise for people looking at it in memory.
          */
         Util_Zero(auxiliaryFlowInfo, sizeof *auxiliaryFlowInfo);
         auxiliaryFlowInfo->afiLabel = opts.afioLabel;
         Str_Strcpy(auxiliaryFlowInfo->afiDest, opts.afioDest, sizeof(auxiliaryFlowInfo->afiDest));
         auxiliaryFlowInfo->afiPort = opts.afioPort;
         auxiliaryFlowInfo->afiHMACKeySize = opts.afioHMACKeySize;
         if (0 < auxiliaryFlowInfo->afiHMACKeySize) {
            memcpy(auxiliaryFlowInfo->afiHMACKeyBuf, opts.afioHMACKeyBuf,
                   auxiliaryFlowInfo->afiHMACKeySize);
         }
         Str_Strcpy(auxiliaryFlowInfo->afiHMACAlgo, opts.afioHMACAlgo,
                    sizeof auxiliaryFlowInfo->afiHMACAlgo);
         status = VVC_STATUS_SUCCESS;
      } else {
         status = VVC_STATUS_NOT_FOUND;
      }
   }

exit:
   VVC_RELEASE_ASOCKBE(asockBackend, VvcTagAsockBeGeneric);
   return status;
}


/*
 *----------------------------------------------------------------------------
 *
 * vvcMultiAsockBackendErrorCb
 *
 *      Notification that one of the asyncSockets encountered an error.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

static void
vvcMultiAsockBackendErrorCb(int error,          // IN
                            AsyncSocket *asock, // IN
                            void *clientData)   // IN
{
   MXUserRecLock *asockLock = NULL;
   VvcAsockBackend *asockBe = clientData;
   VvcSession *session = (VvcSession *)asockBe->session;

   if (!VvcAsockBackendValidateAsock(asock, asockBe)) {
      VvcDebug("%s: Asock %d: Got error callback when Asockbackend is "
               "already removed!\n",
               __FUNCTION__, AsyncSocket_GetID(asock));
      return;
   }

   asock = asockBe->asock;

   VvcLog("%s: Error: %d Asock: %p\n", __FUNCTION__, error, asock);

   if (error == ASOCKERR_GENERIC) {
      VvcLog("Received generic Asyncsocket error, error:%d.\n", AsyncSocket_GetGenericErrno(asock));
   }

   /*
    * XXX As in Send and Recv callbacks, we need to obtain an extra reference
    * before releasing the socket lock.
    */
   VVC_ADD_REF_ASOCKBE(asockBe, VvcTagAsockBeInAsockCb);

   Atomic_Inc32(&asockBe->errorCbInProgress);

   // Need to save asockLock because we're going to destroy asockBackend.
   asockLock = asockBe->asockLock;
   // Releases the AsyncSocket lock
   VvcMultiAsockBackendReleaseSocketLock(asockLock);

   VVC_ADD_REF_SESSION(session, VvcTagAsockBeErrorHandler);
   VvcMultiAsockBackendErrorHandler(error, asockBe);
   Atomic_Dec32(&asockBe->errorCbInProgress);
   VVC_RELEASE_SESSION(session, VvcTagAsockBeErrorHandler);

   // Re-acquires the AsyncSocket lock
   VvcMultiAsockBackendAcquireSocketLock(asockLock);
   VVC_RELEASE_ASOCKBE(asockBe, VvcTagAsockBeInAsockCb);
}


/*
 *----------------------------------------------------------------------------
 *
 * VvcMultiAsockBackendErrorHandler
 *
 *      Asock backend error handler.
 *      Currently all backends are removed if this is fired for any asock
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

void
VvcMultiAsockBackendErrorHandler(int error,                     // IN
                                 VvcAsockBackend *asockBackend) // IN
{
   int32 numAsockBeRemoved = 0;
   VvcSession *session = asockBackend->session;
   int32 sessionId = session->sessionId;
   Bool isSessionLocked = FALSE;
   Bool shouldCallUserAsockErrorCb = TRUE;
   Bool eventQueued = FALSE;

   ASSERT(session);

   isSessionLocked = ISLOCKED_SESSION(session);

   if (!isSessionLocked) {
      LOCK_SESSION(session);
   }

   /* Clean up send/receive data structures before taking down the asockBe */
   VvcOnAsockBackendDisconnected(session);
   VvcSetSessionCloseReason(session, error == ASOCKERR_REMOTE_DISCONNECT
                                        ? VvcSessionCloseNormal
                                        : VvcSessionCloseNetworkDisconnect);
   VvcRemoveAllAsockBackends(session, TRUE, &numAsockBeRemoved);
   VvcDisableBandwidthEstimation(session);

   VvcTrace("%s: error:%d, asock id :%d\n", __FUNCTION__, error, asockBackend->asockID);

   eventQueued = VvcIsErrorNotificationNeeded(session, &shouldCallUserAsockErrorCb);

   if (!isSessionLocked) {
      UNLOCK_SESSION(session);
   }

   if (asockBackend->errorCb && numAsockBeRemoved && shouldCallUserAsockErrorCb) {
      asockBackend->errorCb(error, NULL, sessionId, asockBackend->errorCbClientData);
      asockBackend->errorCb = NULL;
   }

   if (eventQueued) {
      VvcDispatchEvents(session->instance);
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcMultiAsockBackendDestroyRecvState --
 *
 *    Destroy recvState structs in all multi-asock backends.
 *
 * Results:
 *    Number of partial chunk bytes.
 *
 * Side effects:
 *    recvState struct is freed for each backend.
 *
 *-----------------------------------------------------------------------------
 */

size_t
VvcMultiAsockBackendDestroyRecvState(VvcSession *session)
{
   int i;
   VvcAsockBackend *asockBe;
   size_t partialChunkBytes = 0;

   ASSERT(ISLOCKED_SESSION(session));

   for (i = 0; i < session->numAsockBackends; i++) {
      asockBe = session->asockBackends[i];
      partialChunkBytes += VvcDestroyRecvState(asockBe->recvState);
      asockBe->recvState = NULL;
   }

   return partialChunkBytes;
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcRawChannelAsockBackend --
 *
 *    Pair connected BEAT or TCP asyncsocket that is passed down after
 *    successful auth validation, to their respective channels.
 *
 *    These asyncsockets are not added to asockBackends array yet, and are owned
 *    by the channel instead. In the future, it is desirable to manage these
 *    individual asyncsockets as VvcAsockBackend objects, like primary and
 *    secondary connections today.
 *
 * Results:
 *    Success if raw channel found, error otherwise.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

static VvcStatus
VvcRawChannelAsockBackend(VvcSession *session,                       // IN
                          const VvcAsockBackend *asockBackendParams) // IN
{
   DblLnkLst_Links *link;
   AsyncSocket *asock = asockBackendParams->asock;
   void *commitOp;
   size_t commitOpLen;
   Bool msgQueued = FALSE;

   ASSERT(!ISLOCKED_SESSION(session));
   LOCK_SESSION(session);
   VvcMultiAsockBackendAcquireSocketLock(asockBackendParams->asockLock);

   /*
    * Iterate through all channels to find a match.
    *
    * BEAT asock originates in VVC prior to calling into blastSockets, so a
    * simple pointer match is sufficient.
    *
    * TCP asock is a duplicate of accept()'ed socket in Blast Service, and a
    * serial number is used to match it to its channel.
    *
    * Sockets coming through for each raw channel type are:
    * (a) VVC_RAW_CHAN_BEAT: 1 BEAT socket
    * (b) VVC_RAW_CHAN_TCP : 1 TCP socket
    * (c) VVC_RAW_CHAN_VVC : 1 BEAT and 1 TCP socket (optional)
    */
   DblLnkLst_ForEach(link, &session->channelList)
   {
      VvcChannel *channel = DblLnkLst_Container(link, VvcChannel, sessionLink);

      if (VVC_RAW_CHANNEL(channel) &&
          (channel->rawCtx->unverifiedAsock == asock ||    // (a, c-BEAT)
           ((channel->rawCtx->type == VVC_RAW_CHAN_TCP ||  // (b)
             channel->rawCtx->type == VVC_RAW_CHAN_VVC) && // (c-TCP)
            channel->rawCtx->serialNo != 0 &&
            channel->rawCtx->serialNo == asockBackendParams->serialNo))) {

         VvcLog("%s: asock %d claimed by raw channel %d (%s)", __FUNCTION__,
                AsyncSocket_GetID(asock), channel->channelId, channel->name);

         VvcMultiAsockBackendReleaseSocketLock(asockBackendParams->asockLock);

         ASSERT(!channel->rawCtx->asock || !channel->rawCtx->bweAsock);

         /*
          * Send commit op to the client to finish OpenChannel handling on the client and send
          * OPEN_CHAN_ACK back to the server.
          */
         commitOp = VvcBuildRawOpenCommitOp(channel, &commitOpLen);
         VvcQueueMessage(session->ctrlChannel, commitOp, commitOpLen, FALSE, VvcDefaultCtrlMsg, 0,
                         0);
         msgQueued = TRUE;

         if (channel->rawCtx->type == VVC_RAW_CHAN_VVC && // (c-TCP)
             channel->rawCtx->asock) {
            /*
             * Channel is already open with BEAT. This socket is BENIT's TCP connection that comes
             * at the time of switch.
             */
            VvcRawChannelAssignBweAsock(channel, asock);
            UNLOCK_SESSION(session);
            VvcAsockBackendSetOptions(asock);
            AsyncSocket_SetErrorFn(asock, VvcRawAsockErrorCb, channel->rawCtx);
         } else { // (a) (b) (c-BEAT)
            UNLOCK_SESSION(session);
            VvcRawChannelAssignAsock(channel, asock, FALSE);
         }

         if (msgQueued) {
            VvcDispatchSendQueues(session, VvcDispatchSendTriggerOnCtrl);
         }

         ASSERT(channel->rawCtx->rawChannelOnConnect);
         return VVC_STATUS_SUCCESS;
      }
   }

   VvcMultiAsockBackendReleaseSocketLock(asockBackendParams->asockLock);
   UNLOCK_SESSION(session);

   return VVC_STATUS_NOT_FOUND;
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcAddAsockBackend -- Add an AsockBackend and kick off receive on asock
 *
 *    This API lets applications provide an AsyncSocket to VVCLIB.
 *    The AsyncSocket given must be in Connected State and Authenticated.
 *
 *    VvcSession::asockBackends is an array of two asockBackends.
 *    Since we don't support true Multi-Protocol yet, upon errorCb fired on any
 *    one socket, we tear down the whole session.
 *    Also, at any point only one of the two sockets receive data.
 *
 * Results:
 *    VVC_STATUS_SUCCESS if successful, error otherwise.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

VvcStatus
VvcAddAsockBackend(VvcSession *session,                       // IN
                   const VvcAsockBackend *asockBackendParams) // IN
{
   VvcStatus status;
   VvcAsockBackend *ctrlAsockBackend = NULL;
   VvcAsockBackend *asockBe = NULL;
   Bool isServer = (Bool)(session->transportBe.flags & VVC_TRANSPORT_BE_SERVER);

   ASSERT(!ISLOCKED_SESSION(session));

   /*
    * Currently we only allow a single control asock per vvc session.
    * If a control asock already exists while we add a new one, close
    * and remove all the backends associated with that control asock
    * and then add the new one.
    */
   if (asockBackendParams->isControlAsock) {
      ctrlAsockBackend = VvcGetControlAsockBackend(session);

      if (ctrlAsockBackend) {
         VvcWarning("%s: Found existing asock backend with control asock:%p, "
                    "asockID:%d. Removing all backends before adding a new "
                    "control asock:%p, asockID:%d\n",
                    __FUNCTION__, ctrlAsockBackend->asock, ctrlAsockBackend->asockID,
                    asockBackendParams->asock, AsyncSocket_GetID(asockBackendParams->asock));
         VvcMultiAsockBackendErrorHandler(ASOCKERR_REMOTE_DISCONNECT, ctrlAsockBackend);
         VVC_RELEASE_ASOCKBE(ctrlAsockBackend, VvcTagAsockBeGeneric);
         ctrlAsockBackend = NULL;
         session->tcpBweNegotiated = FALSE;
      }
   }

   // raw channels need to know if blastSocketThread, tcpBwe is enabled
   if (!session->blastSocketThreadEnabled && asockBackendParams->blastSocketThreadEnabled) {
      session->blastSocketThreadEnabled = TRUE;
   }

   // Save the negotiated TCP BWE status from the control asock
   if (asockBackendParams->isControlAsock) {
      session->tcpBweNegotiated =
         asockBackendParams->negotiatedTcpBweVersion != ASYNC_BWE_SOCKET_VERSION_NONE;
   }

   /*
    * Check to see if the asyncsocket belongs to a raw channel. On match return
    * without creating a VvcAsockBackend as the channel will own the asock.
    */
   if (isServer && VVC_SUCCESS(VvcRawChannelAsockBackend(session, asockBackendParams))) {
      // Asock is now owned by the respective channel, so we are done here
      return VVC_STATUS_SUCCESS;
   }

   // disable raw channels if support absent in Blast Hop header (from BSG) at the client
   if (!isServer && asockBackendParams->isControlAsock &&
       !asockBackendParams->negotiatedVvcRawChannels) {
      VvcLog("Raw channels not negotiated");
      session->localAllowRawChannels = session->negotiatedRawChannelSupport = 0;
   }

   /*
    * The AsockBackend::asockLock is owned outside of Vvc.
    * Windows-Agent: the asockLock is unranked.
    * Desktop-Clients: the asockLock is MKSSocketLock which is guaranteed to be
    *                  at lower rank than VvcSession lock.
    *
    * Hold the asockLock until we overwrite transportBe pointers (which in turn
    * requires VvcSession lock).
    */
   LOCK_SESSION(session);
   VvcMultiAsockBackendAcquireSocketLock(asockBackendParams->asockLock);

   if (!(session->transportBe.flags & VVC_TRANSPORT_BE_ENABLE_MULTI_PROTOCOL)) {
      VvcError("%s: Can not add asockBackend, VvcSession is not set up with "
               "MultiProtocol transport flag.\n",
               __FUNCTION__);
      status = VVC_STATUS_ERROR;
      goto done;
   }

   if (session->numAsockBackends >= VVC_MAX_NUM_ASOCK_BACKENDS) {
      VvcError("%s: No more asockBackends can be added.\n", __FUNCTION__);
      status = VVC_STATUS_ERROR;
      goto done;
   }

   /*
    * Currently we support a maximum of two AsockBackends at the same time.
    * BEAT: One Ctrl AsockBackend and one Data AsockBackend,
    * TCP:  One AsockBackend which is both Ctrl & Data.
    *
    * In ErrorCb:
    *   Tear down the connection (and both AsockBackends if present) if one of
    *   the below 2 conditions are met ->
    *   1. The Data Socket got the ErrorCb
    *   2. The Ctrl Socket got the ErrorCb.
    *
    * XXX: In future, there could be multiple Data Sockets and we would have to
    *      decide whether to tear down the entire session or not.
    */

   VvcLog("Adding AsockBackend: asock: %d (%p), asockLock: %p, "
          "isEndToEndConnection: %s, isControlAsock: %s, isDataAsock: %s\n",
          AsyncSocket_GetID(asockBackendParams->asock), asockBackendParams->asock,
          asockBackendParams->asockLock,
          asockBackendParams->isEndToEndConnection == TRUE ? "True" : "False",
          asockBackendParams->isControlAsock ? "True" : "False",
          asockBackendParams->isDataAsock ? "True" : "False");

   asockBe = VvcAsockBackendCreate(session, asockBackendParams);

   // Append the new AsockBackend at the end of session->asockBackends array
   VVC_ADD_REF_ASOCKBE(asockBe, VvcTagAsockBeSessionParent);
   session->asockBackends[session->numAsockBackends] = asockBe;
   session->numAsockBackends += 1;

   // Update the perf counter sent by blastSockets
   if (isServer && asockBackendParams->isControlAsock) {
      session->reconnectCount = asockBackendParams->reconnectCount;
   }

   // disable raw channels if primary was tunneled (to BSG) on the client
   if (!isServer && asockBackendParams->isPrimaryTunneled) {
      if (session->negotiatedRawChannelSupport) {
         VvcLog("Disable raw channels as primary connection is tunneled");
      }
      session->localAllowRawChannels = session->negotiatedRawChannelSupport = 0;
   }

   /*
    * If VvcSession state is Established, then this Add-AsockBe is happening
    * after OP_INIT exchange - a side effect of FastFallback.
    * If BENIT is enabled, request a Forced Switch to start to use
    * this newly added BEAT AsockBe.
    */
   if (session->state == VvcSessionEstablished && session->negotiatedDoConcurrentTransports &&
       asockBe->isControlAsock == FALSE) {
      /*
       * When session->state is Established and session->asockXBeDown is TRUE,
       * it means we have a persisted VvcSession (for NC).
       *
       * We have to account for below two cases:
       * (1) negotiatedDoConrrentTransports=TRUE & WaitForBEAT=TRUE:
       *     ForceSwitch Not required in both normal session setup & NC.
       *
       * (2) negotiatedDoConrrentTransports=TRUE & WaitForBEAT=FALSE:
       *     ForceSwitch required in both normal session setup & NC.
       *     Normal new session setup: session->asockXBeDown is FALSE.
       *     NC: Primary TCP socket would be pushed first, immediately triggering
       *     re-transmissions and re-setting the session->asockXBeDown.
       *
       */
      if (!session->asockXBeDown) {
         VvcLog("Requesting a ForceSwitch for session: %p, asock: %d (%p).\n", session,
                AsyncSocket_GetID(asockBe->asock), asockBe->asock);

         /* The below call sets up an Asynchronous forced switch.
          * In the next invocation of switchPollCb, a switch will happen.
          * Note that a forced switch is not counted towards
          * "dataTransportSwitchCountMax".
          *
          * XXX: This "ForceSwitch" is a kludge and is used only because
          *      the TCP estimator is not reliable yet to trigger the TCP->BEAT
          *      switch.
          *      This kludge should be removed once the TCP estimator becomes
          *      more reliable/predictable.
          */
         VvcDataTransportsSwitch_ForceSwitch(session);
      }
   }

   /*
    * On Client-Side, set VvcSession::activeAsockBackendIndex here.
    *
    * On Agent-Side, VvcSession::activeAsockBackendIndex will be set after
    * the fallback decision has been made in VvcDispatchDataSocketActivatedCb().
    */

   if (!isServer && asockBe->isDataAsock) {
      VvcSetActiveAsockBackend(session, asockBe->asock);
      VvcLog("%s: VvcSession: %p, activeAsockBackendIndex: %d\n", __FUNCTION__, session,
             session->activeAsockBackendIndex);
   }

   // Handle asock error
   AsyncSocket_SetErrorFn(asockBe->asock, vvcMultiAsockBackendErrorCb, asockBe);

   if (!isServer && session->asockXBeDown && asockBe->isDataAsock) {
      VvcEnableBandwidthEstimation(session);
      VvcOnAsockBackendConnected(session);
   }

   // Apply QoS Policy here.
   if (session->localEnableQoSPolicy && session->qosPolicy != NULL) {
      if (asockBe->isControlAsock) {
         VvcMultiAsockBackendApplyTCPDscpValue(session, isServer);
      } else {
         VvcMultiAsockBackendApplyUDPDscpValue(session, isServer);
      }
   }

   status = VVC_STATUS_SUCCESS;

done:
   VvcMultiAsockBackendReleaseSocketLock(asockBackendParams->asockLock);
   UNLOCK_SESSION(session);

   VvcLog("VvcAddAsockBackend: %s\n", status == VVC_STATUS_SUCCESS ? "Success" : "Failure");

   if (asockBe) {
      /* Kick off first read on asock */
      status = VvcAsockBackendRecv(session, asockBe);
      if (!VVC_SUCCESS(status)) {
         VvcDebug("%s: Failed VvcAsockBackendRecv\n", __FUNCTION__);
      }
      VVC_RELEASE_ASOCKBE(asockBe, VvcTagAsockBeGeneric);
   }

   return status;
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcRemoveAllAsockBackends --
 *
 *    Removing either the Control Socket or Data Socket should bring down
 *    whole session. So this "Remove" API removes either both AsockBackends
 *    (for BEAT - one Control and one Data) or one AsockBackend (for TCP).
 *    Therefore, currently we don't have a general purpose Remove API.
 *    Whenever we need to do a "Remove", we will remove all (one or two)
 *    AsockBackends held in a VvcSession.
 *
 * Results:
 *    VVC_STATUS_SUCCESS.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

VvcStatus
VvcRemoveAllAsockBackends(VvcSession *session,      // IN
                          Bool isSocketError,       // IN
                          int32 *numAsockBeRemoved) // OUT
{
   int32 i = 0;
   int numAsockBe = 0;
   Bool isServer = TRUE;
   Bool isSessionLocked = FALSE;
   VvcAsockBackend **removedAsockBes;

   ASSERT(session != NULL);

   isSessionLocked = ISLOCKED_SESSION(session);

   if (isSessionLocked) {
      UNLOCK_SESSION(session);
   }

   ASSERT(numAsockBeRemoved);
   *numAsockBeRemoved = 0;

   isServer = session->transportBe.flags & VVC_TRANSPORT_BE_SERVER;

   VvcLog("%s: Start, isSocketError: %s\n", __FUNCTION__, isSocketError == TRUE ? "Yes" : "No");

   if (isServer) {
      /*
       * Stop the KeepAlive messages on the Ctrl Socket if it was previously
       * started.
       */
      VvcStopCtrlKeepAlive(session);
   }

   if (session->negotiatedDoVVCHeartbeats) {
      VvcMultiAsockBackendStopKeepaliveTimeout(session);
   }

   if (!isServer && isSocketError) {
      VvcAsockBackend *activeAsockBe = VvcGetActiveAsockBackend(session);

      /*
       * VvcIsPeerRejected() invokes AsyncSocket_GetWebSocketError() to check
       * if any WebSocket Error Codes are reported that upper layers might be
       * interested in.
       */
      if (activeAsockBe) {
         if (activeAsockBe->isControlAsock && VvcIsPeerRejected(activeAsockBe->asock) == TRUE) {
            VvcSetSessionCloseReason(session, VvcSessionClosePeerRejected);
         }
         VVC_RELEASE_ASOCKBE(activeAsockBe, VvcTagAsockBeGeneric);
      }
   }

   LOCK_SESSION(session);

   /* Clear out VvcSession::numAsockBackends now. */
   numAsockBe = session->numAsockBackends;
   session->numAsockBackends = 0;
   *numAsockBeRemoved = numAsockBe;

   if (session->negotiatedDoConcurrentTransports) {
      VvcDataTransportSwitch_ResetCurrentSwitchCount(session);
   }

   /*
    * Go through the asockBackends array and remove all asock backends, but save
    * removed asock pointers in a separate array and clean up/close them after
    * the asock backend removal loop. We cannot do asock operations inside the
    * removal loop because these operations require locking down asock lock,
    * which is of lower rank than VvcSession lock.
    */
   removedAsockBes = Util_SafeCalloc(numAsockBe, sizeof(VvcAsockBackend *));

   for (i = 0; i < numAsockBe; i++) {
      VvcAsockBackend *asockBe = session->asockBackends[i];

      session->asockBackends[i] = NULL;

      /*
       * Transfer the reference to the entry in removedAsockBes, to be released
       * shortly below.
       */
      removedAsockBes[i] = asockBe;
      VvcAsockBackendShutdown(asockBe);
   }
   session->activeAsockBackendIndex = -1;
   session->isDataSockNotificationNeeded = TRUE;
   session->tcpBweNegotiated = FALSE;

   /*
    * Now that receives from all asockBes are cancelled, set the
    * asockXBeConnectNeeded flag to treat any receives from here on
    * as if from a new asockBe.
    */
   session->asockXBeConnectNeeded = TRUE;

   UNLOCK_SESSION(session);

   /*
    * Now release our AsockBackend references, which may result in their
    * destructions.
    */
   for (i = 0; i < numAsockBe; i++) {
      VvcAsockBackend *asockBe = removedAsockBes[i];

      if (asockBe) {
         /* Release the reference owned by the removeAsockBes temp array */
         VVC_RELEASE_ASOCKBE(asockBe, VvcTagAsockBeSessionParent);
      }
   }
   free(removedAsockBes);

   VvcLog("%s: Done, numAsockBeRemoved: %d\n", __FUNCTION__, *numAsockBeRemoved);

   if (isSessionLocked) {
      LOCK_SESSION(session);
   }

   return VVC_STATUS_SUCCESS;
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcAsockBackendInstallKeepaliveTimer --
 *
 *    Install the keepalive timer callback for an asockbackend through the VVC
 *    instance's backend poll API.
 *
 * Results:
 *    None.
 *
 * Side effects:
 *    Sets the asockBackend->isVVCHeartbeatEnabled flag if successful.
 *
 *-----------------------------------------------------------------------------
 */
void
VvcAsockBackendInstallKeepaliveTimer(VvcAsockBackend *asockBe)
{
   VvcStatus status;
   VvcSession *session = asockBe->session;
   VvcInstance *instance = session->instance;
   VvcStatus (*savedPollCallback)(VvcInstancePollCb callback, void *clientData, Bool periodic,
                                  uint32 timeoutUs) = NULL;

   ASSERT(!ISLOCKED_SESSION(session));

   LOCK_INSTANCE(instance);
   savedPollCallback = instance->instanceBe.pollCallback;
   UNLOCK_INSTANCE(instance);

   if (savedPollCallback == NULL) {
      VvcLog("[VVC Heartbeats] the instanceBe's pollCallback is NULL, VVC is "
             "probably uniniting. Don't install keepalive timer for "
             "asock %d session %u instance %s",
             asockBe->asockID, session->sessionId, instance->name);
      return;
   }

   /*
    * Add a ref-count to AsockBackend for the periodic poll callback. The
    * ref-count will be decremented when the poll callback is canceled.
    */
   asockBe->isVVCHeartbeatEnabled = TRUE;
   VVC_ADD_REF_ASOCKBE(asockBe, VvcTagAsockBeKeepaliveTimerCb);

   status = savedPollCallback(VvcKeepaliveTimeoutCb, asockBe,
                              FALSE, // don't do periodic
                              TRANSPORT_TIMEOUT_POLL_INTERVAL_SEC * MICRO_SEC_IN_SEC);

   if (!VVC_SUCCESS(status)) {
      VvcError("[VVC Heartbeats] Failed to start heartbeat expiry "
               "callback on asock %d\n",
               asockBe->asockID);
      asockBe->isVVCHeartbeatEnabled = FALSE;
      VVC_RELEASE_ASOCKBE(asockBe, VvcTagAsockBeKeepaliveTimerCb);
   } else {
      VvcDebug("[VVC Heartbeats] Rearming heartbeat expiry timer for "
               "asock %d, isEndToEndConnection: %d\n",
               asockBe->asockID, asockBe->isEndToEndConnection);
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcAsockBackendUninstallKeepaliveTimer --
 *
 *    Uninstall the keepalive timer callback for an asockbackend.
 *
 * Results:
 *    None.
 *
 * Side effects:
 *    Unsets the asockBackend->isVVCHeartbeatEnabled flag.
 *
 *-----------------------------------------------------------------------------
 */
void
VvcAsockBackendUninstallKeepaliveTimer(VvcAsockBackend *asockBe)
{
   VvcStatus status;
   VvcSession *session = asockBe->session;
   VvcInstance *instance = session->instance;

   ASSERT(!ISLOCKED_SESSION(session));

   // coverity[fnptr_free]
   status = instance->instanceBe.pollRemove(VvcKeepaliveTimeoutCb, asockBe, FALSE);
   /*
    * Because we don't do periodic timer, once the timerCb is fired (and before
    * we rearm it) it would be "uninstalled" from the poll. In this case,
    * pollRemove will return failure, which is expected. So either way the poll
    * callback should've been uninstalled.
    */
   asockBe->isVVCHeartbeatEnabled = FALSE;

   if (!VVC_SUCCESS(status)) {
      VvcLog("[VVC Heartbeats] VVC heartbeats already uninstalled, "
             "instance: %s, sessionId: %d, asock: %d\n",
             instance->name, session->sessionId, asockBe->asockID);
   } else {
      VvcLog("[VVC Heartbeats] Stopped VVC heartbeats, "
             "instance: %s, sessionId: %d, asock: %d\n",
             instance->name, session->sessionId, asockBe->asockID);
      /*
       * Remove the asockBackend reference count we added when we started the
       * keepalive timer callback.
       */
      VVC_RELEASE_ASOCKBE(asockBe, VvcTagAsockBeKeepaliveTimerCb);
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcMultiAsockBackendStartKeepaliveTimeout --
 *
 *    Runs through the various AsockBackends and, if the backend is not able
 *    to do its own keepalives and is a data socket, we will start a
 *    pollCallback to check for inactivity on that particular socket.
 *
 *    Note that the pollCallback gets called every
 *    TRANSPORT_TIMEOUT_POLL_INTERVAL_SEC seconds even though the expiry
 *    callback would only fire an error if TRANSPORT_KEEPALIVE_TIMEOUT_SEC
 *    seconds had passed without receiving anything. We do this because
 *    TRANSPORT_TIMEOUT_POLL_INTERVAL_SEC, being much smaller than
 *    TRANSPORT_KEEPALIVE_TIMEOUT_SEC, avoids situations where we might have to
 *    wait two intervals to actually realize a timeout had occurred.
 *
 * Results:
 *    VVC_STATUS_SUCCESS if the expiry callback was scheduled on poll,
 *    VVC_STATUS_ERROR otherwise.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

VvcStatus
VvcMultiAsockBackendStartKeepaliveTimeout(void *clientData) // IN
{
   VvcSession *session = clientData;
   VvcInstance *instance = NULL;
   VvcStatus status = VVC_STATUS_ERROR;
   Bool isMPTEnabled = session->transportBe.flags & VVC_TRANSPORT_BE_ENABLE_MULTI_PROTOCOL;
   Bool isSessionLocked = FALSE;
   VvcAsockBackend *asockBackend = NULL;

   ASSERT(session != NULL);
   instance = session->instance;
   ASSERT(session->negotiatedDoVVCHeartbeats == TRUE);

   isSessionLocked = ISLOCKED_SESSION(session);
   if (!isSessionLocked) {
      LOCK_SESSION(session);
   }

   if (!isMPTEnabled) {
      VvcError("[VVC Heartbeats] Cannot start VVC heartbeat expiry, "
               "MPT flag not enabled.\n");

      goto done;
   }

   /*
    *  Currently all Asockbackends in a VVC Session are marked as data.
    *  VVC Ctrl Keep Alives (VvcStartCtrlKeepAlive) are treated as data
    *  packets so that they do not fire dataSocketActivatedCallback  if
    *  they were marked only as control socket.
    *  TODO: Revisit this for MTP if multiple sockets are Active at
    *        the same time.
    */
   asockBackend = VvcGetActiveAsockBackend(session);
   if (NULL == asockBackend) {
      VvcError("[VVC Heartbeats] Failed to get active asockBackend.\n");
      goto done;
   }

   /*
    * Only start expiry callback on sockets which are:
    *      1) data sockets, and
    *      2) do not provide their own end to end keep-alives
    * We do this to avoid redundancy. We already have dummy keep-alives for
    * control channels (see below functions) and isEndToEndConnection signifies
    * that the socket can provide its own keep-alives.
    */
   if (asockBackend->isDataAsock && !asockBackend->isEndToEndConnection &&
       !asockBackend->isVVCHeartbeatEnabled) {
      /*
       * We have to unlock the session before doing the pollCallback due to lock
       * ranks - pollParams lock rank is less than session lock rank.  If we
       * hold the session lock during this setup, the session will crash.
       */
      UNLOCK_SESSION(session);
      VvcAsockBackendInstallKeepaliveTimer(asockBackend);
      LOCK_SESSION(session);
   }

done:
   if (!isSessionLocked) {
      UNLOCK_SESSION(session);
   }

   if (asockBackend != NULL) {
      // VvcGetActiveAsockBackend adds a reference to this object.
      // coverity[double_free]
      // coverity[pass_freed_arg]
      // coverity[deref_arg]
      VVC_RELEASE_ASOCKBE(asockBackend, VvcTagAsockBeGeneric);
   }

   return status;
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcKeepaliveTimeoutCb --
 *
 *    Called every TRANSPORT_KEEPALIVE_TIMEOUT_SEC seconds.
 *    Checks to see if there's been anything received on the socket within
 *    the timeout interval and, if not, fires a socket error.
 *
 * Results:
 *    None
 *
 * Side effects:
 *    Socket error gets triggered if there's been inactivity.
 *
 *-----------------------------------------------------------------------------
 */

void
VvcKeepaliveTimeoutCb(void *clientData)
{
   VvcAsockBackend *asockBe = clientData;
   VvcSession *session = (VvcSession *)(asockBe->session);
   uint64 now = Hostinfo_SystemTimerUS();
   uint64 inactiveRecvTime = now - asockBe->lastRecvTs;
   uint64 inactiveSendTime = now - asockBe->lastSendTs;
   Bool ctrlDataQueued = FALSE;

   ASSERT(asockBe);
   ASSERT(!ISLOCKED_SESSION(session));

   ASSERT_ON_COMPILE(TRANSPORT_KEEPALIVE_TIMEOUT_SEC > TRANSPORT_KEEPALIVE_INTERVAL_SEC);

   if (asockBe->isRemoved) {
      VvcLog("[VVC HeartBeats] Asock %d has been removed! Bailing out.\n", asockBe->asockID);
      goto out;
   }

   if (!asockBe->isVVCHeartbeatEnabled) {
      VvcLog("[VVC HeartBeats] Asock %d heartbeat disabled! Bailing out.\n", asockBe->asockID);
      goto out;
   }

   /*
    * Unset this now. Will set it again if we decide to rearm the timer later in
    * the function.
    */
   asockBe->isVVCHeartbeatEnabled = FALSE;

   if (inactiveRecvTime >= TRANSPORT_KEEPALIVE_TIMEOUT_SEC * MICRO_SEC_IN_SEC) {
      VvcError("[VVC Heartbeats] Asock %d has timed out, hasn't received data "
               "within interval: %" FMT64 "u. Last received data at: %" FMT64 "u, time now: %" FMT64
               "u\n",
               asockBe->asockID, inactiveRecvTime, asockBe->lastRecvTs, now);

      VvcMultiAsockBackendErrorHandler(ASOCKERR_GENERIC, asockBe);
      goto out;
   }

   /*
    * In case of IDLE connection or network inactivity when VVC Original BW
    * Estimator is used, we need to dispatchSendQueues after Keep Alive interval
    * to send RTT probe messages since they are only put in message queue.  When
    * there is network activity these RTT probes messages are sent every
    * rttInterval seconds along with other messages.
    */
   if (inactiveRecvTime >= TRANSPORT_KEEPALIVE_INTERVAL_SEC * MICRO_SEC_IN_SEC) {
      LOCK_SESSION(session);
      ctrlDataQueued =
         (session->ctrlChannel && DblLnkLst_IsLinked(&session->ctrlChannel->sendQueue));
      UNLOCK_SESSION(session);

      VvcDebug("[VVC Heartbeats] Asock %d has been Recv inactive for "
               "%" FMT64 "u, ctrlData is%s queued\n",
               asockBe->asockID, inactiveRecvTime, ctrlDataQueued ? "" : " not");

      if (ctrlDataQueued) {
         VvcDispatchSendQueues(session, VvcDispatchSendTriggerKeepAliveIntervalTimeout);
      }
   }

   /*
    * Send MPTDupAck if no data has been sent in
    * TRANSPORT_KEEPALIVE_INTERVAL_SEC.
    */
   if (inactiveSendTime >= TRANSPORT_KEEPALIVE_INTERVAL_SEC * MICRO_SEC_IN_SEC) {
      VvcLog("[VVC Heartbeats] Asock %d has been Send inactive for "
             "%" FMT64 "u\n",
             asockBe->asockID, inactiveSendTime);

      LOCK_SESSION(session);
      if (session->negotiatedDoConcurrentTransports && session->ctrlChannel != NULL) {
         /*
          * Send a MPT Dup Ack using ctrChannel's sndUna - 1 and pin it to TCP.
          * MPT Ack will be sent inline without going through Vvc's scheduler.
          */
         VvcLog("[VVC Heartbeats] Sending a DupAck pinned to TCP Asock, "
                "asockID: %d, asock: %p\n",
                asockBe->asockID, asockBe->asock);

         /* Add a channel ref to guard against the channel going away */
         VVC_ADD_REF_CHANNEL(session->ctrlChannel, VvcTagAsockBeKeepaliveTimerCb);
         VvcSendChannelMPTDupAckNow(session->ctrlChannel, VvcMsgPinToTCP);
         VVC_RELEASE_CHANNEL(session->ctrlChannel, VvcTagAsockBeKeepaliveTimerCb);
      }
      UNLOCK_SESSION(session);
   }

   /*
    * Rearm this keepaliveTimeoutCb. Note that the force send routine above
    * might trigger asockBe's error handler, and can cause this asockBe to be
    * removed. Don't rearm keepalive if that happens.
    */
   if (!asockBe->isRemoved) {
      VvcAsockBackendInstallKeepaliveTimer(asockBe);
   }

out:
   /*
    * Mirror the inc-ref when keepalive was installed. Note that, because the
    * increment and decrement are mirrored and we check that asockBe != NULL
    * at the beginning of this function, there's no way that it could be NULL
    * here.
    */
   // coverity[double_free]
   // coverity[pass_freed_arg]
   // coverity[deref_arg]
   VVC_RELEASE_ASOCKBE(asockBe, VvcTagAsockBeKeepaliveTimerCb);
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcMultiAsockBackendStopKeepaliveTimeout --
 *
 *    Remove the pollCallbacks related VVC heartbeats (sending and expiry).
 *
 * Results:
 *    VVC_STATUS_SUCCESS or VVC_STATUS_ERROR.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

VvcStatus
VvcMultiAsockBackendStopKeepaliveTimeout(VvcSession *session) // IN
{
   VvcStatus status = VVC_STATUS_ERROR;
   VvcInstance *instance = session->instance;
   int i;
   VvcAsockBackend **keepaliveAsockBes;
   int numAsockBes;

   ASSERT(session != NULL);
   ASSERT(!ISLOCKED_INSTANCE(instance));
   ASSERT(!ISLOCKED_SESSION(session));

   if (!session->negotiatedDoVVCHeartbeats) {
      VvcError("[VVC Heartbeats] Tried to stop heartbeats but session has "
               "negotiatedDoVVCHeartbeats == False, invalid.");
      return status;
   }

   LOCK_SESSION(session);

   /*
    * We have to unlock the session before removing the pollCallback due to lock
    * ranks - pollParams lock rank is less than session lock rank. If we hold
    * the session lock during this setup, the session will run into a panic
    * resulting from lock rank violation. Therefore, Save keepalive AsockBes in
    * a temporary array for pollRemove after the loop.
    */
   numAsockBes = session->numAsockBackends;
   keepaliveAsockBes = Util_SafeCalloc(numAsockBes, sizeof(VvcAsockBackend *));

   for (i = 0; i < session->numAsockBackends; i++) {
      VvcAsockBackend *asockBe = session->asockBackends[i];

      ASSERT(asockBe);
      if (asockBe->isVVCHeartbeatEnabled && instance->instanceBe.pollRemove) {
         asockBe->isVVCHeartbeatEnabled = FALSE;
         /* Save and add-ref this asockBe for out-of-loop processing below */
         keepaliveAsockBes[i] = VVC_ADD_REF_ASOCKBE(asockBe, VvcTagAsockBeSnapshot);
      }
   }

   UNLOCK_SESSION(session);

   for (i = 0; i < numAsockBes; i++) {
      VvcAsockBackend *asockBe = keepaliveAsockBes[i];

      if (asockBe != NULL) {
         VvcAsockBackendUninstallKeepaliveTimer(asockBe);
         // Release the temp ref from the previous for loop.
         // coverity[pass_freed_arg]
         // coverity[double_free]
         // coverity[deref_arg]
         VVC_RELEASE_ASOCKBE(asockBe, VvcTagAsockBeSnapshot);
      }
   }
   free(keepaliveAsockBes);

   return status;
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcStartCtrlKeepAliveIfNeeded --
 *
 *    When below 2 conditions are met (i.e. we are in a BEAT Session)
 *    - VvcSession has two AsockBackends
 *    - Ctrl and Data AsockBackends are Not same
 *    then, we schedule a pollCallback to send dummy data over the Ctrl
 *    AsockBackend periodically.
 *
 * Results:
 *    VVC_STATUS_SUCCESS if CtrlKeepAlive Send was scheduled on poll,
 *    VVC_STATUS_ERROR otherwise.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

VvcStatus
VvcStartCtrlKeepAliveIfNeeded(VvcSession *session) // IN
{
   VvcStatus status = VVC_STATUS_ERROR;
   VvcInstance *instance = NULL;
   Bool isSessionLocked;

   if (!session) {
      return status;
   }

   instance = session->instance;
   ASSERT(!ISLOCKED_INSTANCE(instance));

   if (!VvcIsCtrlKeepAliveNeeded(session) || session->ctrlKeepaliveStarted) {
      status = VVC_STATUS_SUCCESS;
      return status;
   }

   session->ctrlKeepaliveStarted = TRUE;

   isSessionLocked = ISLOCKED_SESSION(session);

   if (isSessionLocked) {
      UNLOCK_SESSION(session);
   }

   if (instance->instanceBe.pollCallback) {
      uint32 ctrlKeepAlivePeriodUS = 0;

      // XXX: VVC_CTRL_KEEP_ALIVE_PERIOD_MS could be read from a registry config
      ctrlKeepAlivePeriodUS = VVC_CTRL_KEEP_ALIVE_PERIOD_MS * MILLI_SEC_IN_SEC;

      VVC_ADD_REF_SESSION(session, VvcTagCtrlKeepAlive);

      // coverity[fnptr_free]
      status = instance->instanceBe.pollCallback(VvcDispatchCtrlKeepAlive,
                                                 session, // clientData
                                                 TRUE,    // isPeriodic
                                                 ctrlKeepAlivePeriodUS);
      if (!VVC_SUCCESS(status)) {
         session->ctrlKeepaliveStarted = FALSE;
         VVC_RELEASE_SESSION(session, VvcTagCtrlKeepAlive);
      }
   }

   if (VVC_SUCCESS(status)) {
      VvcLog("Started CtrlKeepAlive, "
             "instance: %s, session:%p, sessionId: %d\n",
             instance->name, session, session->sessionId);
   } else {
      VvcError("Failed to start CtrlKeepAlive, "
               "instance: %s, session:%p, sessionId: %d, status: %d\n",
               instance->name, session, session->sessionId, status);
   }

   if (isSessionLocked) {
      LOCK_SESSION(session);
   }

   return status;
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcStopCtrlKeepAlive --
 *
 *    Remove the pollCallback to send CtrlKeepAlive dummy data.
 *
 * Results:
 *    VVC_STATUS_SUCCESS.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

VvcStatus
VvcStopCtrlKeepAlive(VvcSession *session) // IN
{
   VvcStatus status = VVC_STATUS_ERROR;
   Bool isSessionLocked = FALSE;
   VvcInstance *instance = NULL;

   if (!session) {
      return status;
   }

   instance = session->instance;
   ASSERT(!ISLOCKED_INSTANCE(instance));

   if (!session->ctrlKeepaliveStarted) {
      status = VVC_STATUS_SUCCESS;
      return status;
   }

   isSessionLocked = ISLOCKED_SESSION(session);

   if (isSessionLocked) {
      UNLOCK_SESSION(session);
   }

   // coverity[fnptr_free]
   if (instance->instanceBe.pollRemove) {
      status = instance->instanceBe.pollRemove(VvcDispatchCtrlKeepAlive,
                                               session, // clientData
                                               TRUE);   // isPeriodic

      if (!VVC_SUCCESS(status)) {
         VvcError("Failed to stop CtrlKeepAlive, "
                  "instance: %s, session:%p, sessionId: %d, status: %d\n",
                  instance->name, session, session->sessionId, status);
      } else {
         VvcLog("Stopped CtrlKeepAlive, "
                "instance: %s, session:%p, sessionId: %d, ",
                instance->name, session, session->sessionId);

         session->ctrlKeepaliveStarted = FALSE;
         VVC_RELEASE_SESSION(session, VvcTagCtrlKeepAlive);
      }
   }

   if (isSessionLocked) {
      LOCK_SESSION(session);
   }

   return status;
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcDispatchCtrlKeepAlive --
 *
 *    Send some dummy data over the Ctrl AsockBe.
 *
 * Results:
 *    VVC_STATUS_SUCCESS if dummy data was sent successfully,
 *    VVC_STATUS_ERROR otherwise.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

void
VvcDispatchCtrlKeepAlive(void *clientData) // IN
{
   VvcSession *session = clientData;
   VvcAsockBackend *ctrlAsockBe = NULL;

   if (!session) {
      return;
   }

   ctrlAsockBe = VvcGetControlAsockBackend(session);

   if (!ctrlAsockBe) {
      return;
   }

   ASSERT(ctrlAsockBe->isControlAsock);

   VvcCtrlKeepAliveSend(ctrlAsockBe);

   VVC_RELEASE_ASOCKBE(ctrlAsockBe, VvcTagAsockBeGeneric);
}


/*
 *----------------------------------------------------------------------------
 *
 * VvcCtrlKeepAliveSend --
 *
 *      Send the dummy data as CtrlKeepAlive message over the Ctrl Socket in
 *      BEAT connection.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

void
VvcCtrlKeepAliveSend(VvcAsockBackend *asockBe) // IN
{
   int err;

   VvcDebug("VvcCtrlKeepAliveSend - asock: %p\n", asockBe->asock);

   err =
      AsyncSocket_Send(asockBe->asock, ctrlKeepAliveDummyBuf, (int)VVC_CTRL_KEEP_ALIVE_DUMMYBUF_LEN,
                       VvcCtrlKeepAliveSendCompleteCb, (void *)asockBe);

   if (err != ASOCKERR_SUCCESS) {
      VvcError("Could not send CtrlKeepAlive, asock:%p, err=%d\n", asockBe->asock, err);
   }
}


/*
 *----------------------------------------------------------------------------
 *
 * VvcCtrlKeepAliveSendCompleteCb --
 *
 *      SendCompleteCb for the CtrlKeepAlive sent over the Ctrl Socket in
 *      BEAT connection. Don't take any action.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      Frees memory.
 *
 *----------------------------------------------------------------------------
 */

void
VvcCtrlKeepAliveSendCompleteCb(void *buf,          // IN
                               int len,            // IN
                               AsyncSocket *asock, // IN
                               void *cbData)       // IN
{
   VvcAsockBackend *asockBe = cbData;

   VvcDebug("VvcCtrlKeepAliveSendCompleteCb - asock: %p\n", asockBe->asock);
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcMultiAsockBackendApplyTCPDscpValue --
 *
 *    Applies the QoS Policy to TCP AsockBe.
 *
 * Results:
 *    VVC_STATUS_SUCCESS if the QoS Policy got applied successfully,
 *    VVC_STATUS_ERROR otherwise.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

VvcStatus
VvcMultiAsockBackendApplyTCPDscpValue(VvcSession *session, // IN
                                      Bool isServer)       // IN
{
   VvcAsockBackend *asockBe = NULL;
   Bool ok = FALSE;
   Bool isSessionLocked = FALSE;

   ASSERT(session);

#if defined(_WIN32)
   if (!isServer) {
      /*
       * Setting Dscp values on Windows Clients:
       *    Dscp is Not set within the RMKS process on Windows Clients.
       *    RMKS/BlastSocket-Client sends a request to blast_hcSvcPlugin
       *    and it sets the Dscp value.
       */
      VvcLog("[VVC QoSPolicy] Windows Clients and TCP AsockBe: "
             "Not applying Dscp Value, session: %p, sessionId: %d\n",
             session, session->sessionId);
      return TRUE;
   }
#endif

   isSessionLocked = ISLOCKED_SESSION(session);

   if (!isSessionLocked) {
      LOCK_SESSION(session);
   }

   ASSERT(session->localEnableQoSPolicy);
   ASSERT(session->qosPolicy);

   asockBe = VvcGetTCPAsockBackend(session);

   if (asockBe) {
      int dscpValue = VvcMultiAsockBackendGetDscpValueToApply(asockBe, isServer, TRUE);

      if (dscpValue == VVC_QOS_INVALID_VALUE) {
         VvcLog("[VVC QoSPolicy] Invalid Dscp value for TCP AsockBe, "
                "No Dscp Value applied, session: %p, sessionId: %d\n",
                session, session->sessionId);
         ok = FALSE;
         goto exit;
      }

      /*
       * AsyncTCPSocket does not support SetOption for QoS.
       */
      if (!VvcMultiAsockBeSetDscpOpt(asockBe, dscpValue)) {
         VvcError("[VVC QoSPolicy] VvcMultiAsockBeSetDscpOpt Failed for TCP "
                  "Asock, can't apply Dscp value: %d, "
                  "session: %p, sessionId: %d\n",
                  dscpValue, session, session->sessionId);
         ok = FALSE;
         goto exit;
      }

      VvcLog("[VVC QoSPolicy] QoSPolicy applied to TCP AsockBe, "
             "Dscp value applied: %d, session: %p, sessionId: %d\n",
             dscpValue, session, session->sessionId);
      ok = TRUE;
   } else {
      VvcError("[VVC QoSPolicy] No TCP AsockBe Found, can't apply QoS Policy, "
               "session: %p, sessionId: %d.\n",
               session, session->sessionId);
      ok = FALSE;
   }

exit:

   if (asockBe) {
      // Release the ref acquired by VvcGetTCPAsockBackend() above
      VVC_RELEASE_ASOCKBE(asockBe, VvcTagAsockBeGeneric);
   }

   if (!isSessionLocked) {
      UNLOCK_SESSION(session);
   }

   return (ok ? VVC_STATUS_SUCCESS : VVC_STATUS_ERROR);
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcMultiAsockBackendApplyUDPDscpValue --
 *
 *    Applies the QoS Policy to UDP AsockBe.
 *
 * Results:
 *    VVC_STATUS_SUCCESS if the QoS Policy got applied successfully,
 *    VVC_STATUS_ERROR otherwise.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

VvcStatus
VvcMultiAsockBackendApplyUDPDscpValue(VvcSession *session, // IN
                                      Bool isServer)       // IN
{
   VvcAsockBackend *asockBe = NULL;
   Bool ok = FALSE;
   Bool isSessionLocked = FALSE;

   ASSERT(session);

#if defined(_WIN32)
   if (!isServer) {
      /*
       * Setting Dscp values on Windows Clients:
       *    Dscp is Not set within the RMKS process on Windows Clients.
       *    RMKS/BlastSocket-Client sends a request to blast_hcSvcPlugin
       *    and it sets the Dscp value.
       */
      VvcLog("[VVC QoSPolicy] Windows Clients and BEAT AsockBe: "
             "Not applying Dscp Value, session: %p, sessionId: %d\n",
             session, session->sessionId);
      return TRUE;
   }
#endif

   isSessionLocked = ISLOCKED_SESSION(session);

   if (!isSessionLocked) {
      LOCK_SESSION(session);
   }

   ASSERT(session->localEnableQoSPolicy);
   ASSERT(session->qosPolicy);

   asockBe = VvcGetBEATAsockBackend(session);

   if (asockBe) {
      int dscpValue = VvcMultiAsockBackendGetDscpValueToApply(asockBe, isServer, FALSE);

      if (dscpValue == VVC_QOS_INVALID_VALUE) {
         VvcLog("[VVC QoSPolicy] Invalid Dscp value for UDP AsockBe, "
                "No Dscp Value applied, session: %p, sessionId: %d\n",
                session, session->sessionId);
         ok = FALSE;
         goto exit;
      }

      if (AsyncSocket_SetOption(asockBe->asock, ASYNC_SOCKET_OPTS_LAYER_BASE, ASYNC_SOCKET_OPT_DSCP,
                                &dscpValue, sizeof dscpValue) != ASOCKERR_SUCCESS) {
         VvcError("[VVC QoSPolicy] AsyncSocket_SetOption Failed for UDP "
                  "Asock, can't apply Dscp value: %d, "
                  "session: %p, sessionId: %d\n",
                  dscpValue, session, session->sessionId);
         ok = FALSE;
         goto exit;
      }

      VvcLog("[VVC QoSPolicy] QoSPolicy applied to UDP AsockBe, "
             "Dscp value applied: %d, session: %p, sessionId: %d\n",
             dscpValue, session, session->sessionId);
      ok = TRUE;
   } else {
      VvcError("[VVC QoSPolicy] No UDP AsockBe Found, can't apply QoS Policy, "
               "session: %p, sessionId: %d.\n",
               session, session->sessionId);
      ok = FALSE;
   }

exit:

   if (asockBe) {
      // Release the ref acquired by VvcGetBEATAsockBackend() above
      VVC_RELEASE_ASOCKBE(asockBe, VvcTagAsockBeGeneric);
   }

   if (!isSessionLocked) {
      UNLOCK_SESSION(session);
   }

   return (ok ? VVC_STATUS_SUCCESS : VVC_STATUS_ERROR);
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcRefAsockBeMsgSeqEntry  --
 *
 *    Get a reference to the MsgSeq entry for the specified channel ID, and
 *    AsockID in the per-asockBackend MsgSeq map. Create a new entry and fill
 *    with default Seq values if it doesn't exist yet.
 *
 * Results:
 *    A pointer to MsgSeq entry in the map.
 *
 * Side effects:
 *    A new entry in the asockBe's channelMsgSeqMap may be created.
 *
 *-----------------------------------------------------------------------------
 */

VvcPerAsockBeMsgSeqEntry *
VvcRefAsockBeMsgSeqEntry(VvcAsockBackend *asockBe, uint32 channelId)
{
   VvcPerAsockBeMsgSeqEntry *seqEntryPtr;
   VvcPerAsockBeMsgSeqEntry newSeqEntry = {SeqNum_16Zero, SeqNum_16Zero};

   seqEntryPtr = HashMap_Get(asockBe->channelMsgSeqMap, &channelId);

   if (seqEntryPtr != NULL) {
      return seqEntryPtr;
   }

   /*
    * First time seeing an incoming or outgoing message on this channel, create
    * a new entry.
    */
   if (!HashMap_Put(asockBe->channelMsgSeqMap, &channelId, &newSeqEntry)) {
      VvcError("%s: channel %d, asockID %d: Failed to create/insert new "
               "MsgSeqEntry!\n",
               __FUNCTION__, channelId, asockBe->asockID);
      return NULL;
   }

   seqEntryPtr = HashMap_Get(asockBe->channelMsgSeqMap, &channelId);
   ASSERT(seqEntryPtr != NULL);

   return seqEntryPtr;
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcGetAsockBeMsgSeqEntry  --
 *
 *    Get a copy of the MsgSeq entry for the specified channel ID, and
 *    AsockID in the per-asockBackend MsgSeq map.
 *
 * Results:
 *    A copy of MsgSeq entry in the map if entry exists, zero-filled SeqEntry
 *    otherwise.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

VvcPerAsockBeMsgSeqEntry
VvcGetAsockBeMsgSeqEntry(VvcSession *session, // IN
                         int asockID,         // IN
                         uint32 channelId)    // IN
{
   VvcAsockBackend *asockBe;
   VvcPerAsockBeMsgSeqEntry *seqEntryPtr;
   VvcPerAsockBeMsgSeqEntry seqEntryCopy = {SeqNum_16Zero, SeqNum_16Zero};

   ASSERT(ISLOCKED_SESSION(session));

   asockBe = VvcGetAsockBackendFromAsockID(session, asockID);
   if (asockBe == NULL) {
      VvcWarning("%s: channelId: %d, asockID %d: AsockBe doesn't exist\n", __FUNCTION__, channelId,
                 asockID);
      return seqEntryCopy;
   }

   seqEntryPtr = HashMap_Get(asockBe->channelMsgSeqMap, &channelId);

   if (seqEntryPtr != NULL) {
      seqEntryCopy = *seqEntryPtr;
   }

   VVC_RELEASE_ASOCKBE(asockBe, VvcTagAsockBeGeneric);

   return seqEntryCopy;
}


/*
 *-----------------------------------------------------------------------------
 *
 * VvcAsockBackendCleanupChannelRecvMessages  --
 *
 *    Clean up the RecvMessage structs for invalid/closed channels on each of
 *    the AsockBackends. Caller must hold the session lock.
 *
 * Results:
 *    None.
 *
 * Side effects:
 *    None.
 *
 *-----------------------------------------------------------------------------
 */

void
VvcAsockBackendCleanupChannelRecvMessages(VvcSession *session, HashMap *validChannelIds)
{
   int i;

   ASSERT(ISLOCKED_SESSION(session));

   for (i = 0; i < session->numAsockBackends; i++) {
      VvcAsockBackend *asockBe = session->asockBackends[i];

      VvcRecvStateCleanupRecvMessages(asockBe->recvState, validChannelIds);
   }
}
