scons-common: &scons-common
  - bora/scons/vtools/common*/*
  - bora/scons/vtools/tree/*
  - conan-deps/scons_bootstrap.py
  - scons/bin/bootstrap-helper.py
  - scons/bin/scons.py
  - scons/init.py
  - scons/lib/vmware/__init__.py
  - scons/lib/vmware/buildclasses.py
  - scons/lib/vmware/compiledb.py
  - scons/lib/vmware/config.py
  - scons/lib/vmware/deprecation.py
  - scons/lib/vmware/environment.py
  - scons/lib/vmware/exception.py
  - scons/lib/vmware/java.py
  - scons/lib/vmware/llvm.py
  - scons/lib/vmware/logwrapper.py
  - scons/lib/vmware/manifest.py
  - scons/lib/vmware/noderegistry.py
  - scons/lib/vmware/nodetypes.py
  - scons/lib/vmware/orderedset.py
  - scons/lib/vmware/phase.py
  - scons/lib/vmware/rust.py
  - scons/lib/vmware/utils.py
  - scons/lib/vmware/vscode.py
  - scons/lib/vtools/*
  - scons/package.py
  - scons/SConstruct
  - scons/support/bin/*
  - scons/vtools/common*/*
  - scons/vtools/tree/*

scons-ios: &scons-ios
  - bora/scons/vtools/mac/*
  - scons/bin/tracker*
  - scons/lib/vmware/depcopWindows.py
  - scons/vtools/ios*/*
  - scons/vtools/mac/*

scons-linux: &scons-linux
  - bora/scons/vtools/*linux*/*
  - scons/lib/vmware/depcopLinux.py
  - scons/vtools/*linux*/*

scons-macos: &scons-macos
  - bora/scons/vtools/*mac*/*
  - scons/bin/tracker*
  - scons/lib/vmware/depcopWindows.py
  - scons/vtools/*mac*/*

scons-posix: &scons-posix
  - scons/bin/forker.py
  - scons/bin/scons
  - scons/lib/vmware/forker.py

scons-windows: &scons-windows
  - bora/scons/vtools/windows*/*
  - scons/bin/scons.cmd
  - scons/lib/vmware/depcopWindows.py
  - scons/lib/vmware/msvcAnalyze.py
  - scons/lib/vmware/visualstudio.py
  - scons/vtools/windows*/*

appblastlibs:
  - *scons-common
  - *scons-linux
  - *scons-posix
  - *scons-windows
  - .github/workflows/appblastlibs.yaml
  - .github/workflows/appblastlibs_ut.yaml
  - .github/actions/scons/action.yaml
  - .github/actions/sonar-upload/action.yaml
  - .github/actions/testframework/**/*
  - bora/apps/cedar/**/*
  - bora/apps/horizonCommon/lib/hzmon/helper/**/*
  - bora/apps/rde/blast/**/*
  - '!bora/apps/rde/blast/appblast/jenkins/**/*'
  - '!bora/apps/rde/blast/scripts/**/*'
  - bora/apps/rde/vvc/**/*
  - bora/apps/viewvc/**/*
  - bora/lib/asyncsocket/**/*
  - bora/lib/blastCodec/**/*
  - bora/lib/blastControl/**/*
  - bora/lib/blastSockets/**/*
  - bora/lib/cityhash/**/*
  - bora/lib/config/**/*
  - bora/lib/coreDump/**/*
  - bora/lib/cpuidInfo/**/*
  - bora/lib/crypto/**/*
  - bora/lib/d3des/**/*
  - bora/lib/dict/**/*
  - bora/lib/err/**/*
  - bora/lib/file/**/*
  - bora/lib/hashMap/**/*
  - bora/lib/image/**/*
  - bora/lib/keyboard/**/*
  - bora/lib/lfqueue/**/*
  - bora/lib/lock/**/*
  - bora/lib/log/**/*
  - bora/lib/mempool/**/*
  - bora/lib/misc/**/*
  - bora/lib/msrCache/**/*
  - bora/lib/panic/**/*
  - bora/lib/poll/**/*
  - bora/lib/pollDefault/**/*
  - bora/lib/productState/**/*
  - bora/lib/public/**/*
  - bora/lib/raster/**/*
  - bora/lib/rbtree/**/*
  - bora/lib/rdsutils/**/*
  - bora/lib/rectangle/**/*
  - bora/lib/region/**/*
  - bora/lib/slab/**/*
  - bora/lib/sound/**/*
  - bora/lib/soundlib/**/*
  - bora/lib/ssl/**/*
  - bora/lib/string/**/*
  - bora/lib/thread/**/*
  - bora/lib/udpfec/**/*
  - bora/lib/udpProxy/**/*
  - bora/lib/user/**/*
  - bora/lib/unicode/**/*
  - bora/lib/uuid/**/*
  - bora/lib/version/**/*
  - bora/lib/vmwarectrl/**/*
  - bora/lib/vnc/**/*
  - bora/lib/vncConnectionManager/**/*
  - bora/lib/vvclib/**/*
  - bora/lib/win32auth/**/*
  - bora/lib/win32cfgmgr/**/*
  - bora/lib/wmi/**/*
  - bora/public/hznvaudio.h
  - bora/scons/modules/view/blast/**/*
  - bora/scons/package/appblastlibs/*
  - bora/scons/products/appblastlibs.py
  - bora-vmsoft/apps/resolutionSetLib/**/*
  - bora-vmsoft/svga/devtap/**/*
  - bora-vmsoft/svga/hznvidd/**/*
  - bora-vmsoft/toolbox/windows/ResolutionSet/**/*
  - bora-vmsoft/hznvaudio/**/*
  - conan-deps/appblastlibs*.*
  - conan-deps/scons_bootstrap.py
  - github-deps/appblastlibs.json
  - gobuild-deps/appblastlibs.json
  - scons/bin/bootstrap-helper.py

hcandroid-common: &hcandroid-common
  - bora/apps/horizonCommon/include/mfw/*
  - bora/apps/horizonCommon/lib/mfw/common/*
  - bora/apps/horizonCommon/lib/mfw/common/platforms/*
  - bora/apps/horizonCommon/lib/mfw/common/regexp/*
  - bora/apps/horizonCommon/lib/mfw/messageframework/*
  - bora/apps/printRedir/apiHelper/posix/*
  - bora/apps/printRedir/common/*
  - bora/apps/printRedir/common/compressor/*
  - bora/apps/printRedir/libs/printUtils/posix/ioUtils/*
  - bora/apps/printRedir/redirectionClient/*
  - bora/apps/printRedir/redirectionClient/jobProcessor/*
  - bora/apps/printRedir/redirectionClient/jobProcessor/posix/*
  - bora/apps/printRedir/redirectionClient/mobile/*
  - bora/apps/printRedir/redirectionClient/printerAssistant/*
  - bora/apps/printRedir/transport/*
  - bora/apps/printRedir/transport/posix/*
  - bora/apps/printRedir/transport/plugins/pipe/posix/*
  - bora/apps/printRedir/transport/plugins/vdp/*
  - bora/apps/printRedir/transport/plugins/vdp/posix/*
  - bora/apps/rde/html5mmr/client/mobile/*
  - bora/apps/rde/html5mmr/common/*
  - bora/apps/rde/mksvchan/client/**/*
  - bora/apps/rde/mksvchan/common/**/*
  - bora/apps/rde/rdeLibs/common/*
  - bora/apps/rde/rdeLibs/include/*
  - bora/apps/rde/rdeLibs/posix/*
  - bora/apps/rde/rdeSvc/client/base/*
  - bora/apps/rde/rdeSvc/client/mobile/*
  - bora/apps/rde/rdeSvc/shared/*
  - bora/apps/rde/rdpvcbridge/common/*
  - bora/apps/rde/rdpvcbridge/common/linux/*
  - bora/apps/rde/rdpvcbridge/client/*
  - bora/apps/rde/rdpvcbridge/dll/*
  - bora/apps/rde/rdpvcbridge/include/*
  - bora/apps/rde/rtav/apps/viewMMDevRedir/*
  - bora/apps/rde/rtav/apps/viewMMDevRedir/v1/*
  - bora/apps/rde/rtav/libs/avCap/*
  - bora/apps/rde/rtav/libs/avManager/*
  - bora/apps/rde/rtav/libs/codecPlugin/*
  - bora/apps/rde/rtav/libs/common/*
  - bora/apps/rde/rtav/libs/configMonitor/*
  - bora/apps/rde/rtav/libs/devMgr/*
  - bora/apps/rde/rtav/libs/deviceMonitor/*
  - bora/apps/rde/rtav/libs/drvCommon/*
  - bora/apps/rde/rtav/libs/pcoip_mfw/linux/*
  - bora/apps/rde/rtav/libs/utils/*
  - bora/apps/rde/rtav/libs/vWebcamMgt/*
  - bora/apps/rde/rtav/libs/omnMMPacket/*
  - bora/apps/rde/scredirvchan/client/*
  - bora/apps/rde/tsdr/client/common/*
  - bora/apps/rde/tsdr/client/posix/*
  - bora/apps/rde/tsdr/common/*
  - bora/apps/rde/usbRedirection/client/*
  - bora/apps/rde/usbRedirection/common/*
  - bora/apps/rde/vdpservice/lib/channel/*
  - bora/apps/rde/vdpservice/lib/platform/*
  - bora/apps/rde/vdpservice/lib/platform/linux/*
  - bora/apps/rde/vdpservice/lib/rpc/*
  - bora/apps/rde/vdpservice/lib/rpc/linux/*
  - bora/apps/rde/vdpservice/lib/rdeCommon/*
  - bora/apps/rde/vdpservice/lib/shared/*
  - bora/apps/rde/vdpservice/lib/unity/*
  - bora/apps/rde/vdpservice/lib/util/*
  - bora/apps/rde/vdpservice/linux/*
  - bora/apps/rde/vdpservice/public/*
  - bora/apps/rde/viewClient/*
  - bora/apps/rde/vmwscard/vmwscard/*
  - bora/apps/rde/vmwscard/vmwscard/include/*
  - bora/apps/rde/vmwscard/vmwscard/piv/*
  - bora/apps/rde/vmwscard/vmwscard/smartcard/*
  - bora/apps/rde/vmwscard/vmwscard/tlv/*
  - bora/apps/usblibs/linux/*
  - bora/apps/viewusb/framework/usb/clientd/*
  - bora/apps/viewusb/framework/usb/include/*
  - bora/apps/viewusb/framework/usb/lib/devConfig/*
  - bora/apps/viewusb/framework/usb/lib/devFilter/*
  - bora/apps/viewusb/framework/usb/lib/interfaces/*
  - bora/apps/viewusb/framework/usb/lib/mmfw/*
  - bora/apps/viewusb/framework/usb/lib/stringStore/*
  - bora/apps/viewusb/framework/usb/lib/urbtrx/*
  - bora/apps/viewusb/framework/usb/lib/viewusblib/*
  - bora/guestABI/**/*
  - bora/lib/asyncsocket/*
  - bora/lib/cityhash/*
  - bora/lib/dnd/**/*
  - bora/lib/raster/*
  - bora/lib/ssl/*
  - bora/lib/vvclib/**/*
  - horizonclient/view/androidClient/androidTest/**/*
  - horizonclient/view/androidClient/client/**/*
  - horizonclient/view/androidClient/mks/**/*
  - horizonclient/view/openClient/*
  - horizonclient/view/openClient/android/**/*
  - horizonclient/view/openClient/lib/cdk/*
  - horizonclient/view/openClient/lib/cdk/android/*
  - horizonclient/view/openClient/lib/udpProxyImpl/*
  - horizonclient/view/openClient/tunnel/*
  - horizonclient/view/openClient/udpProxy/*
  - horizonclient/*gradle*

hcandroid:
  - *hcandroid-common
  - *scons-common
  - *scons-linux
  - *scons-posix
  - .github/workflows/hcandroid.yaml
  - .github/actions/scons/action.yaml
  - bora/scons/apps/printredir/prclient.py
  - bora/scons/apps/printredir/prvdpplugin.py
  - bora/scons/apps/viewusb/*
  - bora/scons/modules/appLibs.py
  - bora/scons/modules/libs.py
  - bora/scons/modules/usblibs.py
  - bora/scons/modules/view/client/libcdk.py
  - bora/scons/modules/view/client/hcandroidVmwscard.py
  - bora/scons/modules/view/client/hcandroidClient.py
  - bora/scons/modules/view/client/hcandroidMks.py
  - bora/scons/modules/view/client/hcandroidMksTest.py
  - bora/scons/modules/view/horizoncommon/messageframework.py
  - bora/scons/modules/view/horizonrxtest/*
  - bora/scons/modules/view/mobilelibs/mobilebaselibs.py
  - bora/scons/modules/view/rdeLibs.py
  - bora/scons/modules/view/rderft/html5mmrClient.py
  - bora/scons/modules/view/rderft/mksvchanclient.py
  - bora/scons/modules/view/rderft/mksvchanComponentTest.py
  - bora/scons/modules/view/rderft/rdeClient.py
  - bora/scons/modules/view/rderft/rdpvcbridge.py
  - bora/scons/modules/view/rderft/rtavPlugin.py
  - bora/scons/modules/view/rderft/scredirvchan.py
  - bora/scons/modules/view/rderft/tsdrClient.py
  - bora/scons/modules/view/rderft/tsdrComponentTest.py
  - bora/scons/modules/view/rderft/usbRedirectionClient.py
  - bora/scons/modules/view/rderft/vdpservice.py
  - bora/scons/package/hccrt/android.py
  - bora/scons/package/hccrt/componentTestAndroid.py
  - bora/scons/products/hccrt.py
  - conan-deps/hcandroid*.*
  - gobuild-deps/hcandroid.json
  - github-deps/hcandroid.json

hcandroid_codeql:
  - *hcandroid-common
  - .github/workflows/hcandroid_codeql.yml
  - .github/codeql/codeql-config_cart.yml
  - '!**/unittest/**/*'
  - '!**/unitTest/**/*'
  - '!**/componentTest/**/*'
  - '!**/componenttest/**/*'
  - '!**/test/**/*'
  - '!**/tests/**/*'

hcios-common: &hcios-common
  # TODO bora rx features codes
  - bora/scons/apps/printredir/prclient.py
  - bora/scons/apps/printredir/prvdpplugin.py
  - bora/scons/modules/appLibs.py
  - bora/scons/modules/libs.py
  - bora/scons/modules/view/client/hcios.py
  - bora/scons/modules/view/client/hciosappstore.py
  - bora/scons/modules/view/client/hciosdist.py
  - bora/scons/modules/view/client/hciosVmwscard.py
  - bora/scons/modules/view/client/hciosWidget.py
  - bora/scons/modules/view/client/icudata.py
  - bora/scons/modules/view/client/libcdk.py
  - bora/scons/modules/view/client/vmRPC.py
  - bora/scons/modules/view/horizoncommon/messageframework.py
  - bora/scons/modules/view/mobilelibs/mobilebaselibs.py
  - bora/scons/modules/view/mobilelibs/rmkslib.py
  - bora/scons/modules/view/rdeLibs.py
  - bora/scons/modules/view/rderft/html5mmrClient.py
  - bora/scons/modules/view/rderft/mksvchanclient.py
  - bora/scons/modules/view/rderft/rdeClient.py
  - bora/scons/modules/view/rderft/rdpvcbridge.py
  - bora/scons/modules/view/rderft/rtavPlugin.py
  - bora/scons/modules/view/rderft/scredirvchan.py
  - bora/scons/modules/view/rderft/tsdrClient.py
  - bora/scons/modules/view/rderft/vdpservice.py
  - bora/apps/rde/rdeSvc/client/base/*
  - bora/apps/rde/rdeSvc/client/mobile/*
  - bora/apps/rde/rdeSvc/shared/*
  - bora/apps/rde/viewClient/*
  - bora/apps/rde/vmwscard/vmwscard/*
  - bora/apps/rde/vmwscard/vmwscard/include/*
  - bora/apps/rde/vmwscard/vmwscard/piv/*
  - bora/apps/rde/vmwscard/vmwscard/smartcard/*
  - bora/apps/rde/vmwscard/vmwscard/tlv/*
  - bora/apps/rde/vmwscard/vmwscard/vmwscard.xcodeproj/*
  - bora/lib/vvclib/**/*
  - bora/scons/products/hccrt.py
  - bora/scons/package/hccrt/ios.py
  - bora/scons/package/hccrt/mobilelibs.py
  - horizonclient/view/iosClient/**/*
  - horizonclient/view/openClient/cdkProxy.c
  - horizonclient/view/openClient/cdkUrl.c
  - horizonclient/view/openClient/cdkProxyPosixAsync.c
  - horizonclient/view/openClient/cdkProxyDarwin.c
  - horizonclient/view/openClient/cdkMain.cc
  - horizonclient/view/openClient/cocoa/clientSDK/*
  - horizonclient/view/openClient/cocoa/common/*
  - horizonclient/view/openClient/cocoa/touch/**
  - horizonclient/view/openClient/lib/cdk/*
  - horizonclient/view/openClient/lib/cdk/cocoa/*
  - horizonclient/view/openClient/lib/udpProxyImpl/*
  - horizonclient/view/openClient/icons/*
  - horizonclient/view/openClient/po/*
  - horizonclient/view/openClient/tunnel/*

hcios:
  - *hcios-common
  - *scons-common
  - *scons-ios
  - *scons-posix
  - .github/workflows/hcios.yaml
  - .github/actions/scons/action.yaml
  - conan-deps/hcios*.*
  - gobuild-deps/hcios.json
  - github-deps/hcios.json

hcios_sonar:
  - *hcios-common
  - .github/workflows/hcios_sonar.yml

hclin-common: &hclin-common
  - bora/apps/crtbora/common/**
  - bora/apps/crtbora/linux/**
  - bora/apps/horizonCommon/lib/mfw/common/*
  - bora/apps/horizonCommon/lib/mfw/common/platforms/*
  - bora/apps/horizonCommon/lib/mfw/common/regexp/*
  - bora/apps/horizonCommon/lib/mfw/messageframework/*
  - bora/apps/lib/cui/core/**
  - bora/apps/lib/cui/dnd/**
  - bora/apps/lib/cui/ghi/**
  - bora/apps/lib/cui/mks/**
  - bora/apps/lib/cui/unity/**
  - bora/apps/lib/lui/mksShared/*
  - bora/apps/lib/lui/unity/*
  - bora/apps/lib/mksCtrlxx/**
  - bora/apps/lib/stringxx/*
  - bora/apps/lib/windowWatermark/**
  - bora/apps/lib/xutils/xutils.cc
  - bora/apps/printRedir/apiHelper/posix/**
  - bora/apps/printRedir/common/**
  - bora/apps/printRedir/common/compressor/*
  - bora/apps/printRedir/libs/cups/**
  - bora/apps/printRedir/libs/fileMonitor/posix/**
  - bora/apps/printRedir/libs/logCleaner/**
  - bora/apps/printRedir/libs/printUtils/posix/**
  - bora/apps/printRedir/proto/**
  - bora/apps/printRedir/redirectionClient/*
  - bora/apps/printRedir/redirectionClient/jobProcessor/posix/*
  - bora/apps/printRedir/transport/**
  - bora/apps/rde/appstub/linux/*
  - bora/apps/rde/blast/vncReplay/*
  - bora/apps/rde/fido2/client/*
  - bora/apps/rde/fido2/common/*
  - bora/apps/rde/fido2/tests/componentTest/cases/*
  - bora/apps/rde/fido2/tests/unitTest/client/*
  - bora/apps/rde/fido2/tests/unitTest/common/*
  - bora/apps/rde/html5mmr/client/*
  - bora/apps/rde/html5mmr/client/chromiumPlayerPlugin/*
  - bora/apps/rde/html5mmr/client/linux/**
  - bora/apps/rde/html5mmr/client/webrtcbase/*
  - bora/apps/rde/html5mmr/common/*
  - bora/apps/rde/mksvchan/client/*
  - bora/apps/rde/mksvchan/client/dnd/*
  - bora/apps/rde/mksvchan/common/*
  - bora/apps/rde/mksvchan/common/dnd/*
  - bora/apps/rde/mksvchan/common/utils/*
  - bora/apps/rde/mksvchan/common/vdpservice/*
  - bora/apps/rde/rdeLibs/common/**
  - bora/apps/rde/rdeLibs/posix/**
  - bora/apps/rde/rdeLibs/utils/compressor/common/*
  - bora/apps/rde/rdeLibs/utils/compressor/posix/*
  - bora/apps/rde/rdeLibs/utils/vvcPcoipWrapper/common/*
  - bora/apps/rde/rdeSvc/client/base/**
  - bora/apps/rde/rdeSvc/client/linux/**
  - bora/apps/rde/rdeSvc/client/posix/**
  - bora/apps/rde/rdeSvc/shared/*
  - bora/apps/rde/rdeSvc/shared/posix/**
  - bora/apps/rde/rdpvcbridge/client/*
  - bora/apps/rde/rdpvcbridge/common/*
  - bora/apps/rde/rdpvcbridge/common/linux/**
  - bora/apps/rde/rtav/apps/rtavCliLib/*
  - bora/apps/rde/rtav/apps/viewMMDevRedir/**
  - bora/apps/rde/rtav/libs/avCap/*
  - bora/apps/rde/rtav/libs/avManager/*
  - bora/apps/rde/rtav/libs/codecPlugin/*
  - bora/apps/rde/rtav/libs/common/*
  - bora/apps/rde/rtav/libs/configMonitor/*
  - bora/apps/rde/rtav/libs/deviceMonitor/*
  - bora/apps/rde/rtav/libs/pcoip_mfw/*
  - bora/apps/rde/rtav/libs/pcoip_mfw/linux/**
  - bora/apps/rde/rtav/libs/utils/*
  - bora/apps/rde/rtav/libs/vAudioInApi/*
  - bora/apps/rde/rtav/libs/omnMMPacket/*
  - bora/apps/rde/rtav/libs/vWebcamMgt/*
  - bora/apps/rde/rtav/libs/vWebcamApi/*
  - bora/apps/rde/scredirvchan/client/*
  - bora/apps/rde/tsdr/client/common/*
  - bora/apps/rde/tsdr/client/posix/**
  - bora/apps/rde/tsdr/common/**
  - bora/apps/rde/tsmmr/common/**
  - bora/apps/rde/tsmmr/player/linux/**
  - bora/apps/rde/udpProxyLib/**
  - bora/apps/rde/urlRedirection/shared/**
  - bora/apps/rde/urlRedirection/urlFilterLinux/**
  - bora/apps/rde/urlRedirection/urlFilterLinux/urlFilter/**
  - bora/apps/rde/usbRedirection/client/*
  - bora/apps/rde/usbRedirection/common/*
  - bora/apps/rde/vdpservice/lib/channel/*
  - bora/apps/rde/vdpservice/lib/channel/linux/**
  - bora/apps/rde/vdpservice/lib/overlay/*
  - bora/apps/rde/vdpservice/lib/platform/*
  - bora/apps/rde/vdpservice/lib/platform/linux/**
  - bora/apps/rde/vdpservice/lib/rdeCommon/*
  - bora/apps/rde/vdpservice/lib/rpc/*
  - bora/apps/rde/vdpservice/lib/rpc/linux/**
  - bora/apps/rde/vdpservice/lib/shared/*
  - bora/apps/rde/vdpservice/lib/unity/*
  - bora/apps/rde/vdpservice/linux/**
  - bora/apps/rde/viewClient/*
  - bora/apps/rde/viewClient/linux/**
  - bora/apps/usbArbitrator/*
  - bora/apps/viewusb/framework/usb/clientd/*
  - bora/apps/viewusb/framework/usb/lib/devConfig/*
  - bora/apps/viewusb/framework/usb/lib/devFilter/*
  - bora/apps/viewusb/framework/usb/lib/mmfw/**
  - bora/apps/viewusb/framework/usb/lib/stringStore/*
  - bora/apps/viewusb/framework/usb/lib/urbtrx/*
  - bora/apps/viewusb/framework/usb/lib/viewusblib/**
  - bora/lib/**/*
  - '!bora/lib/vncConnectionManager/**/*'
  - horizonclient/install/vmis/**/*
  - horizonclient/view/linuxClient/**
  - horizonclient/view/openClient/*
  - horizonclient/view/openClient/dct/common/**
  - horizonclient/view/openClient/dct/configFiles/linux/**
  - horizonclient/view/openClient/dct/linux/**
  - horizonclient/view/openClient/icons/**
  - horizonclient/view/openClient/icudata/**
  - horizonclient/view/openClient/lib/cdk/**
  - horizonclient/view/openClient/lib/httpListener/*
  - horizonclient/view/openClient/lib/libview/*
  - horizonclient/view/openClient/lib/udpProxyImpl/**
  - horizonclient/view/openClient/m4/**
  - horizonclient/view/openClient/po/**
  - horizonclient/view/openClient/sdk/internal/*
  - horizonclient/view/openClient/sdk/internal/linux/**
  - horizonclient/view/openClient/sdk/public/*
  - horizonclient/view/openClient/sdk/public/linux/**
  - horizonclient/view/openClient/sdk/semi-public/c/**
  - horizonclient/view/openClient/tunnel/*

hclin:
  - *hclin-common
  - *scons-common
  - *scons-linux
  - *scons-posix
  - .github/actions/scons/action.yaml
  - .github/actions/sonar-upload/action.yaml
  - .github/actions/testframework/runcase/action.yaml
  - .github/workflows/hclin.yaml
  - .github/workflows/hclin_ut.yaml
  - bora/apps/crtbora/componentTest/viewClientStub/**
  - bora/apps/rde/rdeSvc/tests/shared/*
  - bora/scons/modules/view/client/*
  - bora/scons/package/hccrt/devenv.py
  - bora/scons/package/hccrt/linux.py
  - bora/scons/package/hccrt/linux/*
  - bora/scons/package/hccrt/main.py
  - bora/scons/package/hccrt/oneClient/*
  - bora/scons/products/hccrt.py
  - conan-deps/hclin*.*
  - gobuild-deps/hclin.json
  - github-deps/hclin.json
  - horizonclient/view/oneClient/**
  - horizonclient/view/openClient/sdk/package/nuget/common.props
  - horizonclient/view/openClient/sdk/package/nuget/Linux/**

hclin_codeql:
  - *hclin-common
  - .github/workflows/hclin_codeql.yml
  - .github/codeql/codeql-config_cart.yml
  - .github/actions/codeql/action.yaml
  - '!**/unittest/**/*'
  - '!**/unitTest/**/*'
  - '!**/componentTest/**/*'
  - '!**/componenttest/**/*'
  - '!**/test/**/*'
  - '!**/tests/**/*'

hcmac-common: &hcmac-common
  - bora/apps/cdsng/**
  - '!bora/apps/cdsng/src/apps/vmUpdateLauncher/*'
  - '!bora/apps/cdsng/src/lib/win32/*'
  # TODO bora rx features codes
  - bora/scons/apps/cdsHelper.py
  - bora/scons/apps/docker.py
  - bora/scons/apps/fusionId.py
  - bora/scons/apps/printredir/prclient.py
  - bora/scons/apps/printredir/printRedirProto.py
  - bora/scons/apps/printredir/printredirut.py
  - bora/scons/apps/printredir/prvdpplugin.py
  - bora/scons/apps/usbArbitrator.py
  - bora/scons/apps/viewClientServices.py
  - bora/scons/apps/viewusb/envs.py
  - bora/scons/apps/viewusb/stringstore.py
  - bora/scons/apps/viewusb/devConfig.py
  - bora/scons/apps/viewusb/devFilter.py
  - bora/scons/apps/viewusb/mmfw.py
  - bora/scons/apps/viewusb/urbtrx.py
  - bora/scons/apps/viewusb/usbClient.py
  - bora/scons/apps/viewusb/viewusblib.py
  - bora/scons/modules/appLibs.py
  - bora/scons/modules/cedar/log.py
  - bora/scons/modules/cdsng.py
  - bora/scons/modules/viewClient.py
  - bora/scons/modules/viewClientApiTest.py
  - bora/scons/modules/view/client/clientSdk.py
  - bora/scons/modules/view/client/clientSdkCommon.py
  - bora/scons/modules/view/client/clientSdkC.py
  - bora/scons/modules/view/client/clientSdkOC.py
  - bora/scons/modules/view/client/hcmac.py
  - bora/scons/modules/view/client/hcmacUninstall.py
  - bora/scons/modules/view/client/icudata.py
  - bora/scons/modules/view/client/libcdk.py
  - bora/scons/modules/view/client/rxDCTComponentTestClient.py
  - bora/scons/modules/view/crtbora/crtbora.py
  - bora/scons/modules/view/crtbora/crtboraApiTest.py
  - bora/scons/modules/view/crtbora/crtboraEnv.py
  - bora/scons/modules/view/crtbora/crtboraRXDnDComponentTest.py
  - bora/scons/modules/view/crtbora/crtboraRXFAComponentTest.py
  - bora/scons/modules/view/crtbora/viewClientStub.py
  - bora/scons/modules/view/crtbora/vmipc.py
  - bora/scons/modules/guestrpc.py
  - bora/scons/modules/hugLibs.py
  - bora/scons/modules/libs.py
  - bora/scons/modules/tools/libs.py
  - bora/scons/modules/usblibs.py
  - bora/scons/modules/view/blast/vncReplay.py
  - bora/scons/modules/view/horizoncommon/messageframework.py
  - bora/scons/modules/view/rdeLibs.py
  - bora/scons/modules/view/rderft/fido2Client.py
  - bora/scons/modules/view/rderft/html5mmrClient.py
  - bora/scons/modules/view/rderft/mksvchanclient.py
  - bora/scons/modules/view/rderft/rdeClient.py
  - bora/scons/modules/view/rderft/rdeClientTest.py
  - bora/scons/modules/view/rderft/rdpvcbridge.py
  - bora/scons/modules/view/rderft/rtavCliLib.py
  - bora/scons/modules/view/rderft/rtavPlugin.py
  - bora/scons/modules/view/rderft/rtavProto.py
  - bora/scons/modules/view/rderft/rtavSetting.py
  - bora/scons/modules/view/rderft/rtavTestNodeClient.py
  - bora/scons/modules/view/rderft/rtavUnitTest.py
  - bora/scons/modules/view/rderft/scannerRedirClient.py
  - bora/scons/modules/view/rderft/scannerRedirProto.py
  - bora/scons/modules/view/rderft/sccheck.py
  - bora/scons/modules/view/rderft/scredirvchan.py
  - bora/scons/modules/view/rderft/scredirvchanComponentTest.py
  - bora/scons/modules/view/rderft/tlm_rdpvcbridge.py
  - bora/scons/modules/view/rderft/tsdrClient.py
  - bora/scons/modules/view/rderft/tsdrProto.py
  - bora/scons/modules/view/rderft/urlFilter.py
  - bora/scons/modules/view/rderft/urlFilterPlugin.py
  - bora/scons/modules/view/rderft/usbRedirectionClient.py
  - bora/scons/modules/view/rderft/vdpservice.py
  - bora/scons/modules/view/rxUnitTestLib.py
  - bora/scons/modules/view/tests/exampleTest.py
  - bora/scons/modules/view/tests/crtboraTest.py
  - bora/scons/modules/view/tests/testrunner.py
  - bora/scons/modules/view/tests/viewClientTest.py
  - bora/scons/modules/vmwarebaselib.py
  - bora/scons/package/hccrt/macos.py
  - bora/scons/package/hccrt/oneClient/*
  - bora/scons/package/hccrt/sdkCDoxygenDoc.py
  - bora/scons/products/hccrt.py
  - bora/apps/rde/viewClient/*
  - bora/apps/rde/viewClient/tests/shared/*
  - bora/apps/rde/viewClient/macos/*
  - horizonclient/view/oneClient/**
  - horizonclient/view/openClient/cdkProxy.c
  - horizonclient/view/openClient/cdkUrl.c
  - horizonclient/view/openClient/cdkProxyPosixAsync.c
  - horizonclient/view/openClient/cdkProxyDarwin.c
  - horizonclient/view/openClient/cdkMain.cc
  - horizonclient/view/openClient/cocoa/**
  - '!horizonclient/view/openClient/cocoa/touch/**'
  - horizonclient/view/openClient/lib/cdk/*
  - horizonclient/view/openClient/lib/cdk/cocoa/*
  - horizonclient/view/openClient/lib/httpListener/*
  - horizonclient/view/openClient/lib/udpProxyImpl/*
  - horizonclient/view/openClient/icons/*
  - horizonclient/view/openClient/po/*
  - horizonclient/view/openClient/sdk/docs/mac/*
  - horizonclient/view/openClient/sdk/docs/doxygen/c
  - horizonclient/view/openClient/sdk/internal/*
  - horizonclient/view/openClient/sdk/internal/macos/*
  - horizonclient/view/openClient/sdk/package/nuget/Macos/*
  - horizonclient/view/openClient/sdk/sample/mac/**/*
  - horizonclient/view/openClient/sdk/semi-public/*
  - horizonclient/view/openClient/sdk/semi-public/c/*
  - horizonclient/view/openClient/sdk/semi-public/c/macos/*
  - horizonclient/view/openClient/sdk/tests/internal/*
  - horizonclient/view/openClient/sdk/tests/internal/macos/*
  - horizonclient/view/openClient/sdk/tests/semi-public/c/*
  - horizonclient/view/openClient/tunnel/*
  - bora/apps/rde/fido2/client/*
  - bora/apps/rde/fido2/common/*
  - bora/apps/rde/fido2/tests/componentTest/cases/*
  - bora/apps/rde/fido2/tests/unitTest/client/*
  - bora/apps/rde/fido2/tests/unitTest/common/*

hcmac:
  - *hcmac-common
  - *scons-common
  - *scons-macos
  - *scons-posix
  - .github/workflows/hcmac.yaml
  - .github/workflows/hcmac_ut.yaml
  - .github/actions/scons/action.yaml
  - .github/actions/testframework/runcase/action.yaml
  - conan-deps/hcmac.*
  - gobuild-deps/hcmac.json
  - github-deps/hcmac.json
  - bora/apps/crtbora/common/**/*
  - bora/apps/crtbora/componentTest/appRemoting/shared/*
  - bora/apps/crtbora/componentTest/appRemoting/macos/**/*
  - bora/apps/crtbora/componentTest/fileAssociation/*
  - bora/apps/crtbora/componentTest/remoteExperience/*
  - bora/apps/crtbora/componentTest/remoteExperience/testCases/*
  - bora/apps/crtbora/componentTest/remoteExperience/utilities/*
  - bora/apps/crtbora/componentTest/remoteExperience/macos/*
  - bora/apps/crtbora/componentTest/viewClientStub/**
  - bora/apps/crtbora/macos/*
  - bora/apps/crtbora/macos/tests/*
  - bora/apps/lib/activexx/*
  - bora/apps/lib/cui/*
  - bora/apps/lib/cui/core/*
  - bora/apps/lib/cui/dnd/*
  - bora/apps/lib/cui/ghi/*
  - bora/apps/lib/cui/ghi/tests/*
  - bora/apps/lib/cui/mks/*
  - bora/apps/lib/cui/threads/*
  - bora/apps/lib/cui/unity/*
  - bora/apps/lib/mksCtrlxx/*
  - bora/apps/lib/public/*
  - bora/apps/lib/stringxx/*
  - bora/apps/lib/dui/**/*
  - bora/apps/rde/rdeSvc/*
  - bora/apps/rde/rdeSvc/client/base/**/*
  - bora/apps/rde/rdeSvc/client/posix/**/*
  - bora/apps/rde/rdeSvc/shared/*
  - bora/apps/rde/rdeSvc/tests/shared/*
  - bora/lib/vvclib/**/*

hcmac_sonar:
  - *hcmac-common
  - .github/workflows/hcmac_sonar.yml

hcwin-common: &hcwin-common
  - bora-vmsoft/apps/vmtoolslib/public/vmware/tools/*
  - bora-vmsoft/lib/public/*
  - bora-vmsoft/services/public/vmware/tools/*
  - bora-vmsoft/hznbus/inc/**/*
  - bora-vmsoft/vmwude/*
  - bora-vmsoft/vmwusbt/*
  - bora-vmsoft/vmwvscrd/*
  - bora/apps/cdsng/include/*
  - bora/apps/cdsng/public/*
  - bora/apps/cdsng/src/apps/vmUpdateLauncher/*
  - bora/apps/cdsng/src/lib/archive/*
  - bora/apps/cdsng/src/lib/bora/*
  - bora/apps/cdsng/src/lib/core/*
  - bora/apps/cdsng/src/lib/internal/*
  - bora/apps/cdsng/src/lib/log/*
  - bora/apps/cdsng/src/lib/mainloop/*
  - bora/apps/cdsng/src/lib/options/*
  - bora/apps/cdsng/src/lib/package/*
  - bora/apps/cdsng/src/lib/queue/*
  - bora/apps/cdsng/src/lib/transfer/*
  - bora/apps/cdsng/src/lib/util/*
  - bora/apps/cdsng/src/lib/verify/*
  - bora/apps/cdsng/src/lib/win32/*
  - bora/apps/cdsng/src/lib/xml/*
  - bora/apps/cdsng/src/private/*
  - bora/apps/cedar/lib/base/*
  - bora/apps/cedar/lib/ipc/ipclib/*
  - bora/apps/cedar/lib/log/*
  - bora/apps/cedar/lib/task/*
  - bora/apps/crtbora/common/**/*
  - bora/apps/crtbora/componentTest/appRemoting/shared/*
  - bora/apps/crtbora/componentTest/appRemoting/win32/**/*
  - bora/apps/crtbora/componentTest/fileAssociation/*
  - bora/apps/crtbora/componentTest/remoteExperience/*
  - bora/apps/crtbora/componentTest/remoteExperience/testCases/*
  - bora/apps/crtbora/componentTest/remoteExperience/utilities/*
  - bora/apps/crtbora/componentTest/remoteExperience/win32/*
  - bora/apps/crtbora/componentTest/screenCaptureUT/*
  - bora/apps/crtbora/componentTest/viewClientStub/**/*
  - bora/apps/crtbora/win32/*
  - bora/apps/crtbora/win32/tests/*
  - bora/apps/horizonAgent/apps/ws_diag/*
  - bora/apps/horizonAgent/apps/ws_dllhost/*
  - bora/apps/horizonAgent/common/resources/*
  - bora/apps/horizonAgent/gpo/**/*
  - bora/apps/horizonAgent/include/*
  - bora/apps/horizonAgent/lib/interceptUtil/*
  - bora/apps/horizonAgent/lib/ksplibCommon/entities/*
  - bora/apps/horizonAgent/lib/ksplibCommon/include/*
  - bora/apps/horizonAgent/lib/ntDllLoader/*
  - bora/apps/horizonAgent/lib/objectMap/*
  - bora/apps/horizonAgent/lib/scredirUtil/*
  - bora/apps/horizonAgent/lib/scredir_vchanclient/*
  - bora/apps/horizonAgent/lib/securitymanager/*
  - bora/apps/horizonAgent/lib/smlib/*
  - bora/apps/horizonAgent/lib/smsrv/*
  - bora/apps/horizonAgent/lib/ws_admin/*
  - bora/apps/horizonAgent/lib/ws_cluskeymgr/*
  - bora/apps/horizonAgent/lib/ws_cpakeymgr/*
  - bora/apps/horizonAgent/lib/ws_onrampkeymgr/*
  - bora/apps/horizonAgent/lib/ws_winauth/*
  - bora/apps/horizonCdcSdk/*
  - bora/apps/horizonCommon/include/mfw/*
  - bora/apps/horizonCommon/lib/mfw/common/*
  - bora/apps/horizonCommon/lib/mfw/common/regexp/*
  - bora/apps/horizonCommon/lib/mfw/keyvault/*
  - bora/apps/horizonCommon/lib/mfw/messageframework/*
  - bora/apps/horizonDCT/componentTest/*
  - bora/apps/horizonDCT/componentTest/utils/*
  - bora/apps/horizonDCT/componentTest/utils/win32/*
  - bora/apps/horizonrxtest/componentTest/*
  - bora/apps/horizonrxtest/componentTest/AndroidClient/app/src/main/cpp/*
  - bora/apps/horizonrxtest/componentTest/lib/envSetup/*
  - bora/apps/horizonrxtest/componentTest/lib/pluginHost/*
  - bora/apps/horizonrxtest/componentTest/lib/public/*
  - bora/apps/horizonrxtest/componentTest/lib/public/utilities/*
  - bora/apps/horizonrxtest/componentTest/lib/sessionManagement/*
  - bora/apps/horizonrxtest/componentTest/lib/utilities/*
  - bora/apps/horizonrxtest/componentTest/lib/utilities/win32/*
  - bora/apps/horizonrxtest/componentTest/public/*
  - bora/apps/horizonrxtest/componentTest/socketConnection/common/*
  - bora/apps/horizonrxtest/componentTest/socketConnection/socket_mfw/*
  - bora/apps/horizonrxtest/componentTest/socketConnection/socket_vchan/*
  - bora/apps/horizonrxtest/componentTest/testBase/**/*
  - bora/apps/horizonrxtest/unitTest/lib/*
  - bora/apps/horizonrxtest/unitTest/lib/win32/*
  - bora/apps/horizonrxtest/unitTest/public/*
  - bora/apps/horizonrxtest/unitTest/runner/*
  - bora/apps/horizonrxtest/unitTest/sample/*
  - bora/apps/install/updateBldNumRes/*
  - bora/apps/install/updateDrvRes/*
  - bora/apps/lib/activexx/*
  - bora/apps/lib/cui/*
  - bora/apps/lib/cui/core/*
  - bora/apps/lib/cui/dnd/*
  - bora/apps/lib/cui/ghi/*
  - bora/apps/lib/cui/ghi/tests/*
  - bora/apps/lib/cui/mks/*
  - bora/apps/lib/cui/threads/*
  - bora/apps/lib/cui/unity/*
  - bora/apps/lib/mksCtrlxx/*
  - bora/apps/lib/public/*
  - bora/apps/lib/stringxx/*
  - bora/apps/lib/vmwProtectClient/*
  - bora/apps/lib/vmwProtectClient/tests/InjectionDll/*
  - bora/apps/lib/vmwProtectClient/tests/libinject/*
  - bora/apps/lib/vmwpclient/*
  - bora/apps/lib/windowWatermark/*
  - bora/apps/lib/wui/**/*
  - bora/apps/printRedir/LBP/win32/*
  - bora/apps/printRedir/apiHelper/win32/*
  - bora/apps/printRedir/autoTest/unitTest/*
  - bora/apps/printRedir/autoTest/unitTest/common/*
  - bora/apps/printRedir/autoTest/unitTest/win32/**/*
  - bora/apps/printRedir/common/**/*
  - bora/apps/printRedir/componentTest/server/prTestPrint/*
  - bora/apps/printRedir/gpo/**/*
  - bora/apps/printRedir/libs/emf/win32/*
  - bora/apps/printRedir/libs/logCleaner/*
  - bora/apps/printRedir/libs/printUtils/win32/**/*
  - bora/apps/printRedir/libs/rpc/rpcClient/*
  - bora/apps/printRedir/libs/rpc/rpcPassThruServer/*
  - bora/apps/printRedir/libs/rpc/rpcServer/*
  - bora/apps/printRedir/libs/threadPoolTask/win32/*
  - bora/apps/printRedir/print/printMonitor/*
  - bora/apps/printRedir/print/printProcessor/*
  - bora/apps/printRedir/print/printUI/*
  - bora/apps/printRedir/proto/*
  - bora/apps/printRedir/redirectionClient/*
  - bora/apps/printRedir/redirectionClient/helpers/*
  - bora/apps/printRedir/redirectionClient/helpers/common/*
  - bora/apps/printRedir/redirectionClient/helpers/emf/win32/*
  - bora/apps/printRedir/redirectionClient/helpers/pdf/win32/*
  - bora/apps/printRedir/redirectionClient/helpers/raw/*
  - bora/apps/printRedir/redirectionClient/jobProcessor/*
  - bora/apps/printRedir/redirectionClient/jobProcessor/win32/*
  - bora/apps/printRedir/redirectionClient/printPreview/**/*
  - bora/apps/printRedir/redirectionClient/printPreview/commonWrapper/*
  - bora/apps/printRedir/redirectionClient/printUtil/*
  - bora/apps/printRedir/redirectionClient/printerAssistant/*
  - bora/apps/printRedir/redirectionServer/*
  - bora/apps/printRedir/redirectionServer/clientSimulator/*
  - bora/apps/printRedir/redirectionServer/jobRedirector/*
  - bora/apps/printRedir/redirectionServer/pdfMonitor/*
  - bora/apps/printRedir/redirectionServer/printerFilter/*
  - bora/apps/printRedir/redirectionServer/printerManager/*
  - bora/apps/printRedir/redirectionServer/virtualPrinter/win32/*
  - bora/apps/printRedir/redirectionServerLinux/common/*
  - bora/apps/printRedir/redirectionService/*
  - bora/apps/printRedir/transport/*
  - bora/apps/printRedir/transport/plugins/pipe/*
  - bora/apps/printRedir/transport/plugins/pipe/win32/*
  - bora/apps/printRedir/transport/plugins/vdp/*
  - bora/apps/printRedir/transport/plugins/vdp/win32/*
  - bora/apps/printRedir/transport/win32/*
  - bora/apps/rde/appstub/*
  - bora/apps/rde/apptap/public/*
  - bora/apps/rde/blast/blast_hcSvcPlugin/*
  - bora/apps/rde/blast/blast_hcSvcWrapIPC/*
  - bora/apps/rde/blast/vncReplay/*
  - bora/apps/rde/common/win32/*
  - bora/apps/rde/fido2/client/*
  - bora/apps/rde/fido2/common/*
  - bora/apps/rde/fido2/tests/componentTest/cases/*
  - bora/apps/rde/fido2/tests/unitTest/client/*
  - bora/apps/rde/fido2/tests/unitTest/common/*
  - bora/apps/rde/html5mmr/*
  - bora/apps/rde/html5mmr/client/*
  - bora/apps/rde/html5mmr/client/chromiumPlayerPlugin/*
  - bora/apps/rde/html5mmr/client/webrtcbase/*
  - bora/apps/rde/html5mmr/client/win32/*
  - bora/apps/rde/html5mmr/common/*
  - bora/apps/rde/html5mmr/helper/*
  - bora/apps/rde/hznClientCtrlVCPlugin/*
  - bora/apps/rde/mksvchan/client/*
  - bora/apps/rde/mksvchan/client/dnd/*
  - bora/apps/rde/mksvchan/client/fcp/*
  - bora/apps/rde/mksvchan/common/**/*
  - bora/apps/rde/mksvchan/gpo/**/*
  - bora/apps/rde/mksvchan/tests/componenttest/clipboard/*
  - bora/apps/rde/mksvchan/tests/componenttest/clipboard/fcpComponentTestVerifier/*
  - bora/apps/rde/mksvchan/tests/unittest/clipboard/*
  - bora/apps/rde/pcoip_mfw/include/*
  - bora/apps/rde/pcoip_mfw/pcoip_mfw/*
  - bora/apps/rde/pcoip_vchan/pcoip_vchan/*
  - bora/apps/rde/perfTracker/admx/**/*
  - bora/apps/rde/rdeLibs/common/*
  - bora/apps/rde/rdeLibs/include/*
  - bora/apps/rde/rdeLibs/utils/compressor/common/*
  - bora/apps/rde/rdeLibs/utils/compressor/include/*
  - bora/apps/rde/rdeLibs/utils/compressor/win32/*
  - bora/apps/rde/rdeLibs/utils/vvcPcoipWrapper/common/*
  - bora/apps/rde/rdeLibs/utils/vvcPcoipWrapper/include/*
  - bora/apps/rde/rdeLibs/utils/vvcPcoipWrapper/win32/*
  - bora/apps/rde/rdeLibs/win32/*
  - bora/apps/rde/rdeSvc/*
  - bora/apps/rde/rdeSvc/client/base/**/*
  - bora/apps/rde/rdeSvc/client/win32/**/*
  - bora/apps/rde/rdeSvc/shared/*
  - bora/apps/rde/rdeSvc/shared/win32/*
  - bora/apps/rde/rdeSvc/tests/componentTest/rdeClientTest/shared/*
  - bora/apps/rde/rdeSvc/tests/componentTest/rdeClientTest/win32/*
  - bora/apps/rde/rdeSvc/tests/componentTest/shared/*
  - bora/apps/rde/rdeSvc/tests/shared/*
  - bora/apps/rde/rdeSvc/tests/unitTest/screenCaptureUT/*
  - bora/apps/rde/rdeSvc/tests/unitTest/screenCaptureUT/client/*
  - bora/apps/rde/rdeSvc/tests/unitTest/screenCaptureUT/shared/*
  - bora/apps/rde/rdpvcbridge/client/*
  - bora/apps/rde/rdpvcbridge/common/*
  - bora/apps/rde/rdpvcbridge/common/win32/*
  - bora/apps/rde/rdpvcbridge/dll/*
  - bora/apps/rde/rdpvcbridge/include/*
  - bora/apps/rde/rdpvcbridge/server/*
  - bora/apps/rde/rds/util/*
  - bora/apps/rde/rtav/apps/rtavCliLib/*
  - bora/apps/rde/rtav/apps/viewMMDevRedir/**/*
  - bora/apps/rde/rtav/extras/GPO/**/*
  - bora/apps/rde/rtav/libs/avCap/*
  - bora/apps/rde/rtav/libs/avManager/*
  - bora/apps/rde/rtav/libs/baseClasses/*
  - bora/apps/rde/rtav/libs/codecPlugin/*
  - bora/apps/rde/rtav/libs/common/*
  - bora/apps/rde/rtav/libs/configMonitor/*
  - bora/apps/rde/rtav/libs/devMgr/*
  - bora/apps/rde/rtav/libs/deviceMonitor/*
  - bora/apps/rde/rtav/libs/drvCommon/*
  - bora/apps/rde/rtav/libs/ipcQueue/*
  - bora/apps/rde/rtav/libs/pcoip_mfw/*
  - bora/apps/rde/rtav/libs/utils/*
  - bora/apps/rde/rtav/libs/vAudioInApi/*
  - bora/apps/rde/rtav/libs/omnMMPacket/*
  - bora/apps/rde/rtav/libs/vWebcamMgt/*
  - bora/apps/rde/rtav/libs/vWebcamApi/*
  - bora/apps/rde/rtav/tests/lib/*
  - bora/apps/rde/rtav/tests/rtavTestNodeClient/*
  - bora/apps/rde/rtav/tests/unitTest/*
  - bora/apps/rde/rtav/tests/unitTest/codecPluginTest/*
  - bora/apps/rde/rtav/tests/unitTest/devMgrTest/*
  - bora/apps/rde/rtav/tests/unitTest/hzBusLibTest/*
  - bora/apps/rde/rtav/tests/utils/rtavSetting/*
  - bora/apps/rde/tlm_rdpvcbridge/common/*
  - bora/apps/rde/tlm_rdpvcbridge/include/*
  - bora/apps/rde/tlm_rdpvcbridge/tests/componenttest/*
  - bora/apps/rde/tlm_rdpvcbridge/tests/componenttest/etlmapi/*
  - bora/apps/rde/tlm_rdpvcbridge/tests/componenttest/lib/*
  - bora/apps/rde/tlm_rdpvcbridge/tests/unitTest/*
  - bora/apps/rde/tlm_rdpvcbridge/tlmPlugin/*
  - bora/apps/rde/tlm_rdpvcbridge/tlmPlugin/win32/*
  - bora/apps/rde/tsdr/client/common/*
  - bora/apps/rde/tsdr/client/win32/*
  - bora/apps/rde/tsdr/common/*
  - bora/apps/rde/tsdr/gpo/**/*
  - bora/apps/rde/tsdr/proto/*
  - bora/apps/rde/tsdr/tests/componenttest/*
  - bora/apps/rde/tsdr/tests/unittest/*
  - bora/apps/rde/tsdr/tests/unittest/client/*
  - bora/apps/rde/tsdr/tests/unittest/client/win32/*
  - bora/apps/rde/tsmmr/common/**/*
  - bora/apps/rde/tsmmr/player/win32/*
  - bora/apps/rde/udpProxyLib/*
  - bora/apps/rde/uncRedirection/gpo/**/*
  - bora/apps/rde/uncRedirection/shared/check/**/*
  - bora/apps/rde/uncRedirection/shared/config/win32/*
  - bora/apps/rde/uncRedirection/shared/hzipc/win32/hzipcClient/*
  - bora/apps/rde/uncRedirection/shared/hzipc/win32/hzipcServer/*
  - bora/apps/rde/uncRedirection/shared/include/**/*
  - bora/apps/rde/uncRedirection/shared/log/*
  - bora/apps/rde/uncRedirection/shared/uncMessage/*
  - bora/apps/rde/uncRedirection/tests/unitTest/**/*
  - bora/apps/rde/uncRedirection/uncFilterWin/*
  - bora/apps/rde/uncRedirection/uncSvc/*
  - bora/apps/rde/urlRedirection/appPathWin/*
  - bora/apps/rde/urlRedirection/browserPluginIE/*
  - bora/apps/rde/urlRedirection/defaultHandlers/*
  - bora/apps/rde/urlRedirection/shared/*
  - bora/apps/rde/urlRedirection/shared/win32/*
  - bora/apps/rde/urlRedirection/tests/componenttest/**/*
  - bora/apps/rde/urlRedirection/tests/unittest/*
  - bora/apps/rde/urlRedirection/urlNativeMessageHost/*
  - bora/apps/rde/urlRedirection/urlProtocolIntercept/*
  - bora/apps/rde/urlRedirection/urlProtocolLaunchHelper/*
  - bora/apps/rde/usbRedirection/client/**/*
  - bora/apps/rde/usbRedirection/common/*
  - bora/apps/rde/vdpservice/*
  - bora/apps/rde/vdpservice/lib/channel/*
  - bora/apps/rde/vdpservice/lib/channel/win32/*
  - bora/apps/rde/vdpservice/lib/overlay/**/*
  - bora/apps/rde/vdpservice/lib/platform/*
  - bora/apps/rde/vdpservice/lib/platform/win32/*
  - bora/apps/rde/vdpservice/lib/rdeCommon/*
  - bora/apps/rde/vdpservice/lib/rpc/*
  - bora/apps/rde/vdpservice/lib/rpc/win32/*
  - bora/apps/rde/vdpservice/lib/shared/*
  - bora/apps/rde/vdpservice/lib/unity/*
  - bora/apps/rde/vdpservice/lib/util/*
  - bora/apps/rde/vdpservice/public/*
  - bora/apps/rde/vdpservice/win32/*
  - bora/apps/rde/viewClient/*
  - bora/apps/rde/viewClient/tests/shared/*
  - bora/apps/rde/viewClient/tests/win32/*
  - bora/apps/rde/viewClient/win32/*
  - bora/apps/rde/whfbRedirection/crypto/*
  - bora/apps/rde/whfbRedirection/dataHandler/*
  - bora/apps/rde/whfbRedirection/include/*
  - bora/apps/rde/whfbRedirection/ipc/include/*
  - bora/apps/rde/whfbRedirection/ipc/logger/*
  - bora/apps/rde/whfbRedirection/ipc/server/*
  - bora/apps/rde/whfbRedirection/utilities/*
  - bora/apps/rde/whfbRedirection/vchan/*
  - bora/apps/sdr/common/**/*
  - bora/apps/sdr/proto/*
  - bora/apps/sdr/rxgserviceplugin/*
  - bora/apps/sdr/sdr_hcSvcPlugin/*
  - bora/apps/sdr/sdrclient/*
  - bora/apps/sdr/sdrclienthost/*
  - bora/apps/sdr/sdrclientworker/*
  - bora/apps/sdr/sdrserverutil/*
  - bora/apps/sdr/unitTest/**/*
  - bora/apps/usbArbitrator/*
  - bora/apps/usblibs/windows/*
  - bora/apps/viewScannerRedir/client/common/*
  - bora/apps/viewScannerRedir/client/win32/**/*
  - bora/apps/viewScannerRedir/common/**/*
  - bora/apps/viewScannerRedir/proto/*
  - bora/apps/viewScannerRedir/server/horizonDS/*
  - bora/apps/viewScannerRedir/unitTest/*
  - bora/apps/viewScannerRedir/unitTest/win32/*
  - bora/apps/viewusb/framework/usb/clientd/*
  - bora/apps/viewusb/framework/usb/include/*
  - bora/apps/viewusb/framework/usb/lib/devConfig/*
  - bora/apps/viewusb/framework/usb/lib/devFilter/*
  - bora/apps/viewusb/framework/usb/lib/interfaces/*
  - bora/apps/viewusb/framework/usb/lib/mmfw/*
  - bora/apps/viewusb/framework/usb/lib/stringStore/*
  - bora/apps/viewusb/framework/usb/lib/urbtrx/*
  - bora/apps/viewusb/framework/usb/lib/viewusblib/*
  - bora/apps/viewusb/framework/usb/test/ceipUtil/*
  - bora/apps/viewusb/framework/usb/test/componenttest/engine/*
  - bora/apps/viewusb/framework/usb/test/componenttest/plugin/*
  - bora/apps/viewusb/framework/usb/test/componenttest/utilities/*
  - bora/apps/viewusb/framework/usb/test/include/*
  - bora/apps/viewusb/framework/usb/test/misc/*
  - bora/apps/viewusb/framework/usb/test/udeTest/*
  - bora/apps/viewusb/framework/usb/test/unitTest/clientd/*
  - bora/apps/viewusb/framework/usb/test/unitTest/common/*
  - bora/apps/viewusb/framework/usb/test/unitTest/devConfig/*
  - bora/apps/viewusb/framework/usb/test/unitTest/devFilter/*
  - bora/apps/viewusb/framework/usb/test/unitTest/mmfw/*
  - bora/apps/viewusb/framework/usb/test/unitTest/mmfwClient/*
  - bora/apps/viewusb/framework/usb/test/unitTest/mmfwServer/*
  - bora/apps/viewusb/framework/usb/test/unitTest/mocks/*
  - bora/apps/viewusb/framework/usb/test/unitTest/stringStore/*
  - bora/apps/viewusb/framework/usb/test/unitTest/urbTrx/*
  - bora/apps/viewusb/framework/usb/test/unitTest/usbRedirectionClient/*
  - bora/apps/viewusb/framework/usb/test/unitTest/usbdLoader/*
  - bora/apps/viewusb/framework/usb/test/unitTest/viewusblib/*
  - bora/apps/viewusb/framework/usb/usbdLoader/win32/*
  - bora/apps/vmappsdkWin32/**/*
  - bora/apps/vmnet/nt/vnetlib/*
  - bora/apps/vmwarebaselib/*
  - bora/apps/rxgservice/include/*
  - bora/devices/public/*
  - bora/lib/**/*
  - '!bora/lib/vncConnectionManager/**/*'
  - bora/mks/public/*
  - bora/modules/shared/linux/*
  - bora/modules/vmwprotect/*
  - bora/public/*
  - bora/public/osfs/*
  - bora/public/x86/*
  - horizonclient/view/gpo-l10n/horizon/**/*
  - horizonclient/view/gpo/**/*
  - horizonclient/view/openClient/cdkProxy.c
  - horizonclient/view/openClient/cdkUrl.c
  - horizonclient/view/openClient/cdkProxyWin32.c
  - horizonclient/view/openClient/lib/cdk/*
  - horizonclient/view/openClient/lib/controllerAPI/**/*
  - horizonclient/view/openClient/lib/test/cdk/*
  - horizonclient/view/openClient/po/*
  - horizonclient/view/openClient/sdk/**/*
  - horizonclient/view/openClient/tunnel/*
  - horizonclient/view/openClient/win32/gssapiLib/*
  - horizonclient/view/openClient/win32/mfwWrapper/*
  - horizonclient/view/openClient/win32/ui_hcSvcPlugin/*
  - horizonclient/view/openClient/win32/rc/hzn_client.admx
  - horizonclient/view/winClient/**/*

hcwin:
  - *hcwin-common
  - *scons-common
  - *scons-windows
  - .github/workflows/hcwin.yaml
  - .github/workflows/hcwin_ut.yaml
  - .github/actions/scons/action.yaml
  - .github/actions/sonar-upload/action.yaml
  - .github/actions/testframework/**/*
  - bora/install/msi/printRedir/*
  - bora/install/msi/rtav/*
  - bora/install/msi/msm/printRedir/Client.wxs
  - bora/install/msi/msm/printRedir/printRedir.wxs
  - bora/install/msi/msm/Rtav/Client.wxs
  - bora/install/msi/msm/Rtav/Rtav.wxs
  - bora/apps/crtbora/componentTest/viewClientStub/**
  - bora/scons/apps/printredir/prclient.py
  - bora/scons/apps/printredir/prClientUtil.py
  - bora/scons/apps/printredir/prCommonWrapper.py
  - bora/scons/apps/printredir/printhelper.py
  - bora/scons/apps/printredir/printPreview.py
  - bora/scons/apps/printredir/printRedirProto.py
  - bora/scons/apps/printredir/printredirut.py
  - bora/scons/apps/printredir/prTestPrint.py
  - bora/scons/apps/printredir/prvdpplugin.py
  - bora/scons/package/hccrt/windows.py
  - bora/scons/package/hccrt/oneClient/*
  - bora/scons/modules/view/client/*
  - '!bora/scons/modules/view/client/hc*'
  - bora/scons/modules/view/crtbora/*
  - bora/scons/modules/view/rderft/rtavCliLib.py
  - bora/scons/modules/view/rderft/rtavLibs.py
  - bora/scons/modules/view/rderft/rtavPlugin.py
  - bora/scons/modules/view/rderft/rtavProto.py
  - bora/scons/modules/view/rderft/rtavSetting.py
  - bora/scons/modules/view/rderft/rtavTestNodeClient.py
  - bora/scons/modules/view/rderft/rtavUnitTest.py
  - bora/tests/remoteDevice/remoteDeviceTool/*
  - conan-deps/hcwin*.*
  - gobuild-deps/hcwin.json
  - github-deps/hcwin.json
  - horizonclient/install/windows/client/**/*
  - horizonclient/install/windows/ViewClientBA/**/*

hcwin_codeql:
  - *hcwin-common
  - .github/workflows/hcwin_codeql.yml
  - .github/codeql/codeql-config_cart.yml
  - '!**/unittest/**/*'
  - '!**/unitTest/**/*'
  - '!**/componentTest/**/*'
  - '!**/componenttest/**/*'
  - '!**/test/**/*'
  - '!**/tests/**/*'
  - '!**/e2eTest/**/*'
  - '!**/autoTest/**/*'

horizonagent-common: &horizonagent-common
  - bora/apps/horizonAgent/**/*
  - bora/apps/rde/gpsRedirection/**/*
  - bora/apps/rde/html5mmr/common/**/*
  - bora/apps/rde/html5mmr/helper/**/*
  - bora/apps/rde/html5mmr/server/**/*
  - bora/apps/rde/html5mmr/nativeMessaging/**/*
  - bora/apps/rde/rdeLibs/**/*
  - bora/apps/rde/rdpvcbridge/**/*
  - bora/apps/rde/rds/**/*
  - bora/apps/rde/vdpService/**/*
  - bora/lib/vvclib/**/*

horizonagent:
  - *horizonagent-common
  - *scons-common
  - *scons-windows
  - .github/workflows/horizonagent.yaml
  - .github/workflows/horizonagent_ut.yaml
  - .github/actions/scons/action.yaml
  - .github/actions/sonar-upload/action.yaml
  - .github/actions/testframework/**/*
  - bora/apps/rde/blast/appblast/**/*
  - bora/apps/rde/fido2/common/**/*
  - bora/apps/rde/fido2/agent/**/*
  - bora/apps/rde/printRedir/LBP/**/*
  - bora/apps/rde/printRedir/apiHelper/win32/**/*
  - bora/apps/rde/printRedir/common/**/*
  - bora/apps/rde/printRedir/componentTest/**/*
  - bora/apps/rde/printRedir/gpo/**/*
  - bora/apps/rde/printRedir/libs/emf/win32/*
  - bora/apps/rde/printRedir/libs/logCleaner/*
  - bora/apps/rde/printRedir/libs/printServerApi/*
  - bora/apps/rde/printRedir/libs/printUtils/win32/*
  - bora/apps/rde/printRedir/libs/rpc/*
  - bora/apps/rde/printRedir/libs/threadPoolTask/win32/*
  - bora/apps/rde/printRedir/print/**/*
  - bora/apps/rde/printRedir/proto/**/*
  - bora/apps/rde/printRedir/redirectionServer/**/*
  - bora/apps/rde/printRedir/redirectionService/**/*
  - bora/apps/rde/rdeSvc/server/**/*
  - bora/apps/rde/rdeSvc/tests/shared/*
  - bora/apps/printRedir/transport/*
  - bora/apps/printRedir/transport/plugins/pipe/*
  - bora/apps/printRedir/transport/plugins/pipe/win32/*
  - bora/apps/printRedir/transport/plugins/vdp/*
  - bora/apps/printRedir/transport/plugins/vdp/win32/*
  - bora/apps/printRedir/transport/win32/*
  - bora/apps/rde/rdeSvc/lib/*
  - bora/apps/rde/rdeSvc/server/*
  - bora/apps/rde/rdeSvc/server/appSvc/*
  - bora/apps/rde/rdeSvc/server/appTap/*
  - bora/apps/rde/rdeSvc/server/dpiSync/*
  - bora/apps/rde/rdeSvc/shared/rc/*
  - bora/apps/rde/rdeSvc/shared/rdsAadAuth/*
  - bora/apps/rde/rdeSvc/shared/utils/*
  - bora/apps/rde/rdeSvc/shared/win32/*
  - bora/apps/rde/rdeSvc/tests/componentTest/rdeServerTest/*
  - bora/apps/rde/rtav/apps/**/*
  - '!bora/apps/rde/rtav/apps/rtavCliLib/**/*'
  - bora/apps/rde/rtav/driver/**/*
  - bora/apps/rde/rtav/extras/**/*
  - bora/apps/rde/rtav/libs/**/*
  - '!bora/apps/rde/rtav/libs/pcoip_mfw/**/*'
  - bora/apps/rde/rtav/tests/**/*
  - '!bora/apps/rde/rtav/tests/unitTest/**/*'
  - bora/apps/rde/usbRedirection/server/**/*
  - bora/apps/rde/vmwime/**/*
  - bora/apps/sdr/**/*
  - bora/apps/viewScannerRedir/**/*
  - bora/apps/viewusb/framework/usb/lib/vhublib/**/*
  - bora/apps/viewusb/framework/usb/lib/urbtrx/**/*
  - bora/apps/viewusb/framework/usb/lib/stringStore/**/*
  - bora/apps/viewusb/framework/usb/lib/intfStore/**/*
  - bora/apps/viewusb/framework/usb/lib/devFilter/**/*
  - bora/apps/viewusb/framework/usb/lib/ws_usbstor/**/*
  - bora/apps/viewusb/framework/usb/lib/ws_vhub/**/*
  - bora/apps/viewusb/framework/usb/lib/ws_vdpvhub/**/*
  - bora/apps/viewusb/framework/usb/lib/ws_vhubcommon/**/*
  - bora/apps/viewvc/libs/**/*
  - bora/install/msi/horizon/agent/**/*
  - bora/install/msi/msm/GpsRedir/**/*
  - bora/install/msi/msm/Html5mmrCommon/**/*
  - bora/install/msi/msm/printRedir/Agent.wxs
  - bora/install/msi/msm/printRedir/printRedir.wxs
  - bora/install/msi/msm/Rtav/Agent.wxs
  - bora/install/msi/msm/Rtav/Rtav.wxs
  - bora/install/msi/msm/TeamsHelper/**/*
  - bora/install/msi/msm/VDPService/**/*
  - bora/install/msi/msm/vm**/*
  - bora/install/msi/printRedir/**/*
  - bora/install/msi/rtav/**/*
  - bora/lib/blastCodec/**/*
  - bora/lib/vnc/**/*
  - bora/lib/vvclib/**/*
  - bora/lib/vncConnectionManager/**
  - '!bora/lib/vncConnectionManager/linux/**'
  - bora/modules/**/*
  - bora/scons/apps/printredir/prGraphics.py
  - bora/scons/apps/printredir/printredir.py
  - bora/scons/apps/printredir/printRedirProto.py
  - bora/scons/apps/printredir/prmon.py
  - bora/scons/apps/printredir/prProcessor.py
  - bora/scons/apps/printredir/prPS.py
  - bora/scons/apps/printredir/prserverapi.py
  - bora/scons/apps/printredir/prService.py
  - bora/scons/apps/printredir/prTestPrint.py
  - bora/scons/apps/printredir/prtestserver.py
  - bora/scons/apps/printredir/prUI.py
  - bora/scons/apps/printredir/prUIRes.py
  - bora/scons/apps/printredir/prvdpplugin.py
  - bora/scons/modules/view/drivers/**/*
  - bora/scons/modules/view/horizonagent/**/*
  - bora/scons/modules/view/rderft/gpsRedirectionIEPlugin.py
  - bora/scons/modules/view/rderft/html5mmrNativeMessagingHost.py
  - bora/scons/modules/view/rderft/html5mmrServer.py
  - bora/scons/modules/view/rderft/rdeServer.py
  - bora/scons/modules/view/rderft/rdeServerTest.py
  - bora/scons/modules/view/rderft/rtavLibs.py
  - bora/scons/modules/view/rderft/rtavPlugin.py
  - bora/scons/modules/view/rderft/rtavProto.py
  - bora/scons/modules/view/rderft/rtavTestNodeServer.py
  - bora/scons/modules/view/rderft/teamsHelper.py
  - bora/scons/modules/view/rderft/scannerRedirAgent.py
  - bora/scons/modules/view/rderft/scannerRedirClient.py
  - bora/scons/modules/view/rderft/scannerRedirProto.py
  - bora/scons/modules/view/rderft/scannerRedirUt.py
  - bora/scons/modules/view/rderft/sdrClient.py
  - bora/scons/modules/view/rderft/sdrProto.py
  - bora/scons/modules/view/rderft/sdrServer.py
  - bora/scons/modules/view/rderft/sdrUt.py
  - bora/scons/products/horizonagent.py
  - bora/scons/package/common/horizon-stage.py
  - bora/scons/package/horizonagent/**/*
  - bora/scons/package/viewdrivers/**/*
  - bora-vmsoft/install/**/*
  - bora-vmsoft/svga/**/*
  - bora-vmsoft/vmw**/*
  - bora-vmsoft/lib/unity/**/*
  - conan-deps/horizonagent*.*
  - gobuild-deps/horizonagent.json
  - github-deps/horizonagent.json
  - scons/vtools/common/artifactory.py
  - scons/vtools/common/openjdk.py

horizonagent_codeql:
  - *horizonagent-common
  - .github/workflows/horizonagent_codeql.yml
  - .github/codeql/codeql-config_cart.yml
  - '!**/unittest/**/*'
  - '!**/unitTest/**/*'
  - '!**/componentTest/**/*'
  - '!**/componenttest/**/*'
  - '!**/test/**/*'
  - '!**/tests/**/*'

horizoncommon:
  - *scons-common
  - *scons-ios
  - *scons-linux
  - *scons-macos
  - *scons-posix
  - *scons-windows
  - .github/workflows/horizoncommon.yaml
  - .github/workflows/horizoncommon_ut.yaml
  - .github/actions/scons/action.yaml
  - bora/apps/cedar/**/*
  - bora/apps/horizonCommon/**/*
  - conan-deps/horizoncommon*.*
  - gobuild-deps/horizoncommon.json
  - github-deps/horizoncommon.json

horizonlinuxagent-common: &horizonlinuxagent-common
  - bora/apps/horizonLinuxAgent/**
  - bora/apps/lxagent/**
  - bora/apps/printRedir/proto/*
  - bora/apps/printRedir/redirectionServerLinux/**
  - bora/apps/rde/cdrserver/**
  - bora/apps/rde/collabui/**
  - bora/apps/rde/mksvchan/server/mksvchanserverLinux.c
  - bora/apps/rde/rdeSvc/server/appScanner/*
  - bora/apps/rde/rdeSvc/server/dpiSync/dpiUtilsLinux*.*
  - bora/apps/rde/rdeSvc/server/linux/*
  - bora/apps/rde/rtav/libs/vAudioInApi/VAudioInCtrlLinux*.*
  - bora/apps/rde/scrediragent/**
  - bora/apps/rde/unityPlugin/unityPluginLinux*.*
  - bora/apps/rde/usbRedirection/server/linux/**
  - bora/apps/rde/vncServer/**
  - bora/lib/blastProxy/**
  - bora/lib/vncConnectionManager/**
  - bora/lib/vvclib/**/*
  - '!bora/lib/vncConnectionManager/win32/**'
  - bora-vmsoft/lib/ghIntegration/x11/*
  - bora-vmsoft/lib/unity/x11/**
  - bora-vmsoft/lib/xdg/*

horizonlinuxagent:
  - *horizonlinuxagent-common
  - *scons-common
  - *scons-linux
  - *scons-posix
  - .github/workflows/horizonlinuxagent.yaml
  - .github/workflows/horizonlinuxagent_ut.yaml
  - .github/actions/scons/action.yaml
  - .github/actions/sonar-upload/action.yaml
  - .github/actions/testframework/**/*
  - bora/apps/horizonAgent/java/**
  - bora/apps/rde/html5mmr/common/**
  - bora/apps/rde/html5mmr/nativeMessaging/linux/**
  - bora/apps/rde/html5mmr/server/linux/**
  - bora/apps/rde/mksvchan/common/mksvchanClipboardX11.c
  - bora/apps/rde/vvc/vvcProxyStub/**
  - bora/lib/blastControl/*.*
  - bora/scons/modules/view/udpForwarder.py
  - bora/scons/modules/view/vncServer.py
  - bora/scons/modules/view/lxagent/*
  - bora/scons/modules/view/rderft/html5mmrNativeMessagingHost.py
  - bora/scons/modules/view/rderft/html5mmrServer.py
  - bora/scons/modules/view/rderft/html5mmrServerUnitTest.py
  - bora/scons/modules/view/rderft/rdeServer.py
  - bora/scons/modules/view/tests/lxagent/*
  - bora/scons/package/horizonlinuxagent/*
  - bora/scons/products/horizonlinuxagent.py
  - conan-deps/horizonlinuxagent*.*
  - gobuild-deps/horizonlinuxagent.json
  - github-deps/horizonlinuxagent.json
  - scons/vtools/common/openjdk.py

horizonlinuxagent_codeql:
  - *horizonlinuxagent-common
  - .github/workflows/horizonlinuxagent_codeql.yml
  - .github/codeql/codeql-config_cart.yml
  - bora/apps/rde/html5mmr/nativeMessaging/linux/*
  - bora/apps/rde/html5mmr/server/linux/*
  - '!**/unittest/**/*'
  - '!**/unitTest/**/*'
  - '!**/componentTest/**/*'
  - '!**/componenttest/**/*'
  - '!**/test/**/*'
  - '!**/tests/**/*'

horizonosot:
  - *scons-common
  - *scons-windows
  - .github/workflows/horizonosot.yaml
  - .github/actions/scons/action.yaml
  - bora/apps/horizonosot/**/*
  - conan-deps/horizonosot*.*
  - gobuild-deps/horizonosot.json

horizonprotocol_videoplayertest:
  - bora/apps/rde/blast/scripts/videoplayer.py
