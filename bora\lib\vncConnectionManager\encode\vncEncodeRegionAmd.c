/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*
 *
 * vncEncodeRegionAmd.c --
 *
 *      Encoding for whole frames with AMD's HW encoder
 *      using the AMF SDK.
 *
 *      The video region encoders usually operate on full frames and
 *      identify unchanged regions at the macroblock level.
 */

#include "vncEncodeRegionInt.h"
#include "vncEncodeRegionUtil.h"
#include "vncUtilInt.h"

#if defined(_WIN32)

#   include "dll_load_defs.h"
#   include "vncEncodeMP4Rect.h"
#   include "vncFreqMap.h"

#   include <windows.h>
#   include <versionhelpers.h>
#   include "win32uRegistry.h"
#   include "horizonPaths.h"

/*
 * Defining COBJMACROS allows access to COM pointer related macro
 * (IDXGIFactory1_EnumAdapters, ID3D11Device_Release,etc) that makes
 * code more readable.
 */
#   define COBJMACROS
#   include <d3d11.h>
#   include <dxgi.h>
#   include <core/Factory.h>
#   include <core/PropertyStorage.h>
#   include <core/Surface.h>
#   include <core/Variant.h>
#   include <components/VideoEncoderVCE.h>
#   include <components/VideoEncoderHEVC.h>
#   include <components/VideoEncoderAV1.h>


#   define LGPFX "RegEnc-Amd: "

/*
 * Turn off AMD hardware encoding if the agent is deployed
 * on a physical machine, until AMD fixes black screen.
 * Refer to installer custom action VM_SetVM_TYPE_REG
 */
#   define DEPLOYMENT_TYPE_VM 0
#   define DEPLOYMENT_TYPE_PHYSICAL_MACHINE 1
#   define DEPLOYMENT_REGKEY HORIZON_VDM_REG_ROOT_A

#   define HEVC_MAX_WIDTH 8192
#   define HEVC_MAX_HEIGHT 4320

#   define MIN_BITRATE_BITS_PER_SEC (10000)
#   define MAX_BITRATE_BITS_PER_SEC (100000000)

#   define AV1_MAX_QP 255
#   define AV1_QP_SCALE_FROM_H264(h264qp) (((h264qp) * AV1_MAX_QP) / CONFIG_H264_QP_MAX)

#   include "vncEncodeManager.h"

/* H.264 libvnc constants */
#   define MBWIDTH 16 /* H.264 MacroBlock width */

#   define MAX_CONSECUTIVE_BOOST_FRAMES 10
#   define MAX_BOOST_FPS 5


/* Input buffer macro for the encoder */
#   define TOTAL_INPUT_RESOURCE 2
#   define PRIMARY_INPUT_RESOURCE 0

#   if defined(VMX86_DEVEL) || defined(VMX86_DEBUG)
#      define TIMESTAMP_ENABLE 1
#   endif

#   if TIMESTAMP_ENABLE
#      define NOTE_TIME(h) VNCEncodeRegionNoteTime(h)
#      define PRINT_TIMES(h, a) VNCEncodeRegionPrintTimes(h, a)
#   else
#      define NOTE_TIME(h)
#      define PRINT_TIMES(h, a)
#   endif

typedef struct amfBitrateControllerParams {
   uint64_t targetBitsPerSec;
   uint64_t peakBitsPerSec;
} amfBitrateControllerParams;

typedef struct VNCRegionEncoderAmd {

   struct VNCRegionEncoder base;
   AMFFactory *factory;
   AMFContext *context;
   AMFComponent *encoder;
   HANDLE pSharedHandle[TOTAL_INPUT_RESOURCE];

   ID3D11Device *pD3d11Device;
   ID3D11DeviceContext *pD3d11DeviceContext;
   CRITICAL_SECTION csD3d11DeviceContext;
   ID3D11Texture2D *pD3d11Texture[TOTAL_INPUT_RESOURCE];
   ID3D11Texture2D *pD3d11TextureMappable[TOTAL_INPUT_RESOURCE];
   IDXGIKeyedMutex *pD3d11TextureKeyedMutex[TOTAL_INPUT_RESOURCE];

   int frameNumber;
   uint32 encodeAsH264;
   uint8 maxQP;
   uint8 minQP;
   Bool useYUV444;
   VNCEncodeCodecType codecType;
   Bool useUnifiedD3DDevice;
   Bool useCpuMapping;
   int width;
   int height;
   int mbWidth;
   int mbHeight;
   Bool firstTime;
   Bool isFrameMaxQual;
   int boostFrames;
   double lastBWupdate;
   int capacityCheckPeriodMs;
   int encFrameNum;
   int sendFrameNum;
   VNCBitmask *dirtyMask;
   HANDLE inputRegHandle[TOTAL_INPUT_RESOURCE];
   amfBitrateControllerParams rcParamNew;

   uint32 encSyncTimeoutMs;
   uint8 encMaxRetries;

   /* DEBUG */
   FILE *fhandleBitstream;
   FILE *fhandleRaw;
} VNCRegionEncoderAmd;


/* AMFSDK API functions imported from .dll/.so file */
static struct {
   DLL_HANDLE dllHandleAmdEncoder;

   AMF_RESULT (*AMFInit)(amf_uint64 version, AMFFactory **ppFactory);
   AMF_RESULT (*AMFQueryVersion)(amf_uint64 *pVersion);

} amdsdk;


/*
 * Track first allocation of H264 client.
 */
static int clientCount = 0;
static Bool amdEncInitialized = FALSE;
static double lastFailedCreateTime = 0.0;

/*
 * Track whether caps have been queried and if so, what the results are.
 */
static Bool sCapsQueried = FALSE;
static VNCRegionEncoderAmdCaps sCaps = {0};

#   define PCI_VENDOR_ID_AMD (0x1002)

static int VNCEncodeRegionAmdFindFreeHandleSlot(VNCRegionEncoderAmd *regEnc);
static int VNCEncodeRegionAmdFindSharedHandle(VNCRegionEncoderAmd *regEnc,
                                              const VNCRegEncFrameState *frameState);

#   include "vncEncodeRegionAmdWindows.h"

#   define AMDREGENC_DUMMY_LOCAL(id, dc)                                                           \
      VNCRegionEncoderAmd AmdRegEnc = {.base.config.encoderId = id,                                \
                                       .base.config.dynamicConfig = dc};                           \
      VNCRegionEncoderAmd *regEnc = &AmdRegEnc;


/*
 *----------------------------------------------------------------------------
 *
 * VNCEncodeRegionAmdIsYUV444Requested --
 *
 *      Check if YUV 444 is requested.
 *
 * Results:
 *      TRUE if YUV 444 is requested.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

static Bool
VNCEncodeRegionAmdIsYUV444Requested(const VNCRegionEncoderAmd *regEnc) // IN
{
   const VNCEncodeDynamicConfig *config = &regEnc->base.config.dynamicConfig;
   Bool yuv444Requested = FALSE;
   switch (regEnc->codecType) {
   case VNCENCODE_CODEC_TYPE_AV1:
      yuv444Requested = (config->allowAV1YUV444 && regEnc->base.config.clientSupportsAV1YUV444);
      break;
   case VNCENCODE_CODEC_TYPE_HEVC:
      yuv444Requested = (config->allowHEVCYUV444 && regEnc->base.config.clientSupportsHEVCYUV444);
      break;
   default:
      break;
   }

   return yuv444Requested;
}


/*
 *----------------------------------------------------------------------------
 *
 * VNCEncodeRegionAmdIsYUV444Supported --
 *
 *      Check if YUV 444 is supported.
 *
 * Results:
 *      TRUE if YUV 444 is supported.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

static Bool
VNCEncodeRegionAmdIsYUV444Supported(const VNCRegionEncoderAmd *regEnc) // IN
{
   Bool retVal = FALSE;
   switch (regEnc->codecType) {
   case VNCENCODE_CODEC_TYPE_AV1:
      retVal = sCaps.av1SupportsYUV444;
      break;
   case VNCENCODE_CODEC_TYPE_HEVC:
      retVal = sCaps.hevcSupportsYUV444;
      break;
   case VNCENCODE_CODEC_TYPE_H264:
   default:
      retVal = sCaps.h264SupportsYUV444;
      break;
   }

   return retVal;
}


/*
 *----------------------------------------------------------------------------
 *
 * VNCEncodeRegionAmdUnloadLibrary --
 *
 *      Unload the AMF SDK library.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

static void
VNCEncodeRegionAmdUnloadLibrary(void)
{
   VNCRegionEncoderAmd *regEnc = NULL; // for logging

   VNCEncodeManager_Lock();

   if (--clientCount == 0) {

      if (amdsdk.dllHandleAmdEncoder) {
         REGENCLG0_ONCE("Unloading AMF SDK shared library \"%s\"", AMF_LIBRARY_NAME);
         CLOSE_LIB(amdsdk.dllHandleAmdEncoder);
         amdsdk.dllHandleAmdEncoder = NULL;
      }
      ASSERT(amdEncInitialized == FALSE);
   }

   ASSERT(clientCount >= 0);
   VNCEncodeManager_Unlock();
}


/*
 *----------------------------------------------------------------------------
 *
 * VNCEncodeRegionAmdLoadLibrary --
 *
 *      Load the AMF SDK library.
 *
 * Results:
 *      TRUE if successful.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

static Bool
VNCEncodeRegionAmdLoadLibrary(void)
{
   VNCRegionEncoderAmd *regEnc = NULL; // for logging

   VNCEncodeManager_Lock();

   if (++clientCount > 1) {
      ASSERT(amdsdk.dllHandleAmdEncoder != NULL);
      VNCEncodeManager_Unlock();
      return TRUE;
   }

   /* Load the AMF library. */
   ASSERT(amdsdk.dllHandleAmdEncoder == NULL);
   amdsdk.dllHandleAmdEncoder = OPEN_LIB(AMF_LIBRARY_NAME);
   if (!amdsdk.dllHandleAmdEncoder) {
      REGENCLG0_ONCE("Library \"%s\" is unavailable (%s)", AMF_LIBRARY_NAME,
                     DYNAMIC_LOAD_ERROR_STRING);
      goto exit;
   }
   REGENCLG0_ONCE("Loaded AMF SDK shared library \"%s\"", AMF_LIBRARY_NAME);

   DLSYM_NOFAIL(AMFInit, amdsdk.dllHandleAmdEncoder);
   DLSYM_NOFAIL(AMFQueryVersion, amdsdk.dllHandleAmdEncoder);

   if (!amdsdk.AMFInit) {
      REGENCWARN_ONCE("DLL %s failed to find the init function", AMF_LIBRARY_NAME);
      goto exit;
   }
   if (!amdsdk.AMFQueryVersion) {
      REGENCWARN_ONCE("DLL %s failed to find the query version function", AMF_LIBRARY_NAME);
      goto exit;
   }

   VNCEncodeManager_Unlock();
   return TRUE;

exit:
   VNCEncodeRegionAmdUnloadLibrary();
   VNCEncodeManager_Unlock();

   return FALSE;
}


/*
 *----------------------------------------------------------------------------
 *
 * VNCEncodeRegionAmdSetBandwidth --
 *
 *      Updates the hardware encoder bandwidth settings in the rate control with
 *      the current bandwidth estimate.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *----------------------------------------------------------------------------
 */

static void
VNCEncodeRegionAmdSetBandwidth(const VNCRegionEncoderAmd *regEnc,    // IN
                               uint32 bandwidthBytesPerSec,          // IN
                               amfBitrateControllerParams *rcParams) // OUT
{
   /* Define a minimum bitrate */
   rcParams->targetBitsPerSec =
      HZN_CLAMP(8 * bandwidthBytesPerSec, MIN_BITRATE_BITS_PER_SEC, MAX_BITRATE_BITS_PER_SEC);
   REGENC_RLOG(9, "%s Bandwidth target bitrate : %d bits per second", __FUNCTION__,
               rcParams->targetBitsPerSec);

   /* Estimated max Kbps is 20% above target */
   rcParams->peakBitsPerSec = HZN_CLAMP(1.2 * rcParams->targetBitsPerSec, MIN_BITRATE_BITS_PER_SEC,
                                        MAX_BITRATE_BITS_PER_SEC);
}


/*
 *----------------------------------------------------------------------------
 *
 * VNCEncodeRegionAmdSetEncoderStaticParams --
 *
 *      Function to set the static encoding parameters/properties for the AMF
 *      encoder component. Static properties must be defined before the Init()
 *      function is called and will apply until the end of the encoding
 *      session.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *----------------------------------------------------------------------------
 */
static void
VNCEncodeRegionAmdSetEncoderStaticParams(VNCRegionEncoderAmd *regEnc, // IN/OUT
                                         int width,                   // IN
                                         int height)                  // IN
{

   AMFSize size = AMFConstructSize(width, height);
   AMF_RESULT res;
   int usage;

   switch (regEnc->codecType) {

   case VNCENCODE_CODEC_TYPE_AV1:
      usage = AMF_VIDEO_ENCODER_AV1_USAGE_LOW_LATENCY;
      AMF_ASSIGN_PROPERTY_INT64(res, regEnc->encoder, AMF_VIDEO_ENCODER_AV1_USAGE, usage);
      AMF_ASSIGN_PROPERTY_BOOL(res, regEnc->encoder, AMF_VIDEO_ENCODER_AV1_ENCODING_LATENCY_MODE,
                               AMF_VIDEO_ENCODER_AV1_ENCODING_LATENCY_MODE_LOWEST_LATENCY);
      AMF_ASSIGN_PROPERTY_INT64(res, regEnc->encoder, AMF_VIDEO_ENCODER_AV1_QUALITY_PRESET,
                                AMF_VIDEO_ENCODER_AV1_QUALITY_PRESET_SPEED);
      AMF_ASSIGN_PROPERTY_SIZE(res, regEnc->encoder, AMF_VIDEO_ENCODER_AV1_FRAMESIZE, size);
      AMF_ASSIGN_PROPERTY_INT64(res, regEnc->encoder, AMF_VIDEO_ENCODER_AV1_ALIGNMENT_MODE,
                                AMF_VIDEO_ENCODER_AV1_ALIGNMENT_MODE_NO_RESTRICTIONS);
      break;
   case VNCENCODE_CODEC_TYPE_HEVC:
      usage = AMF_VIDEO_ENCODER_HEVC_USAGE_LOW_LATENCY;
      AMF_ASSIGN_PROPERTY_INT64(res, regEnc->encoder, AMF_VIDEO_ENCODER_HEVC_USAGE, usage);
      AMF_ASSIGN_PROPERTY_BOOL(res, regEnc->encoder, AMF_VIDEO_ENCODER_HEVC_LOWLATENCY_MODE, true);
      AMF_ASSIGN_PROPERTY_INT64(res, regEnc->encoder, AMF_VIDEO_ENCODER_HEVC_QUALITY_PRESET,
                                AMF_VIDEO_ENCODER_HEVC_QUALITY_PRESET_SPEED);
      AMF_ASSIGN_PROPERTY_SIZE(res, regEnc->encoder, AMF_VIDEO_ENCODER_HEVC_FRAMESIZE, size);
      break;
   case VNCENCODE_CODEC_TYPE_H264:
   default:
      usage = AMF_VIDEO_ENCODER_USAGE_LOW_LATENCY;
      AMF_ASSIGN_PROPERTY_INT64(res, regEnc->encoder, AMF_VIDEO_ENCODER_USAGE, usage);
      AMF_ASSIGN_PROPERTY_BOOL(res, regEnc->encoder, AMF_VIDEO_ENCODER_LOWLATENCY_MODE, true);
      AMF_ASSIGN_PROPERTY_INT64(res, regEnc->encoder, AMF_VIDEO_ENCODER_QUALITY_PRESET,
                                AMF_VIDEO_ENCODER_QUALITY_PRESET_SPEED);
      AMF_ASSIGN_PROPERTY_SIZE(res, regEnc->encoder, AMF_VIDEO_ENCODER_FRAMESIZE, size);
      break;
   }
}


/*
 *----------------------------------------------------------------------------
 *
 * VNCEncodeRegionAmdSetEncoderDynamicParams --
 *
 *      Function to set the dynamic encoding parameters or properties for the
 *      AMF encoder component. These properties can be changed subsequently
 *      and these changes will be flushed to encoder only before the next
 *      Submit() call.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      None
 *
 *----------------------------------------------------------------------------
 */
static void
VNCEncodeRegionAmdSetEncoderDynamicParams(VNCRegionEncoderAmd *regEnc) // IN/OUT
{

   AMFRate framerate = AMFConstructRate(regEnc->base.config.dynamicConfig.maxFPS, 1);
   const VNCEncodeDynamicConfig *config = &regEnc->base.config.dynamicConfig;
   AMF_RESULT res;
   regEnc->maxQP = HZN_CLAMP(config->qpmaxH264, CONFIG_H264_QP_MIN, CONFIG_H264_QP_MAX);
   regEnc->minQP = HZN_CLAMP(config->qpminH264, CONFIG_H264_QP_MIN, regEnc->maxQP);
   VNCEncodeRegionAmdSetBandwidth(regEnc, config->maxBandwidth, &regEnc->rcParamNew);

   switch (regEnc->codecType) {

   case VNCENCODE_CODEC_TYPE_AV1:
      AMF_ASSIGN_PROPERTY_INT64(res, regEnc->encoder, AMF_VIDEO_ENCODER_AV1_GOP_SIZE, 0);
      AMF_ASSIGN_PROPERTY_INT64(res, regEnc->encoder, AMF_VIDEO_ENCODER_AV1_TARGET_BITRATE,
                                regEnc->rcParamNew.targetBitsPerSec);
      AMF_ASSIGN_PROPERTY_INT64(res, regEnc->encoder, AMF_VIDEO_ENCODER_AV1_PEAK_BITRATE,
                                regEnc->rcParamNew.peakBitsPerSec);
      AMF_ASSIGN_PROPERTY_RATE(res, regEnc->encoder, AMF_VIDEO_ENCODER_AV1_FRAMERATE, framerate);
      AMF_ASSIGN_PROPERTY_INT64(res, regEnc->encoder, AMF_VIDEO_ENCODER_AV1_MAX_Q_INDEX_INTER,
                                AV1_QP_SCALE_FROM_H264(regEnc->maxQP));
      AMF_ASSIGN_PROPERTY_INT64(res, regEnc->encoder, AMF_VIDEO_ENCODER_AV1_MIN_Q_INDEX_INTER,
                                AV1_QP_SCALE_FROM_H264(regEnc->minQP));
      break;
   case VNCENCODE_CODEC_TYPE_HEVC:
      AMF_ASSIGN_PROPERTY_INT64(res, regEnc->encoder, AMF_VIDEO_ENCODER_HEVC_GOP_SIZE, 0);
      AMF_ASSIGN_PROPERTY_INT64(res, regEnc->encoder, AMF_VIDEO_ENCODER_HEVC_TARGET_BITRATE,
                                regEnc->rcParamNew.targetBitsPerSec);
      AMF_ASSIGN_PROPERTY_INT64(res, regEnc->encoder, AMF_VIDEO_ENCODER_HEVC_PEAK_BITRATE,
                                regEnc->rcParamNew.peakBitsPerSec);
      AMF_ASSIGN_PROPERTY_RATE(res, regEnc->encoder, AMF_VIDEO_ENCODER_HEVC_FRAMERATE, framerate);
      AMF_ASSIGN_PROPERTY_INT64(res, regEnc->encoder, AMF_VIDEO_ENCODER_HEVC_MAX_QP_P,
                                regEnc->maxQP);
      AMF_ASSIGN_PROPERTY_INT64(res, regEnc->encoder, AMF_VIDEO_ENCODER_HEVC_MIN_QP_P,
                                regEnc->minQP);
      break;
   case VNCENCODE_CODEC_TYPE_H264:
   default:
      AMF_ASSIGN_PROPERTY_INT64(res, regEnc->encoder, AMF_VIDEO_ENCODER_B_PIC_PATTERN, 0);
      AMF_ASSIGN_PROPERTY_INT64(res, regEnc->encoder, AMF_VIDEO_ENCODER_IDR_PERIOD, 0);
      AMF_ASSIGN_PROPERTY_INT64(res, regEnc->encoder, AMF_VIDEO_ENCODER_TARGET_BITRATE,
                                regEnc->rcParamNew.targetBitsPerSec);
      AMF_ASSIGN_PROPERTY_INT64(res, regEnc->encoder, AMF_VIDEO_ENCODER_PEAK_BITRATE,
                                regEnc->rcParamNew.peakBitsPerSec);
      AMF_ASSIGN_PROPERTY_RATE(res, regEnc->encoder, AMF_VIDEO_ENCODER_FRAMERATE, framerate);
      AMF_ASSIGN_PROPERTY_INT64(res, regEnc->encoder, AMF_VIDEO_ENCODER_MAX_QP, regEnc->maxQP);
      AMF_ASSIGN_PROPERTY_INT64(res, regEnc->encoder, AMF_VIDEO_ENCODER_MIN_QP, regEnc->minQP);
      break;
   }
   REGENCLG0("qpmax:%u qpmin:%u", regEnc->maxQP, regEnc->minQP);
}


/*
 *--------------------------------------------------------------------------
 *
 * VNCEncodeRegionAmdPlaneCopy --
 *
 *      A helper function to copy Y or UV planes.
 *
 * Results:
 *
 *
 * Side effects:
 *      None.
 *
 *--------------------------------------------------------------------------
 */

static void
VNCEncodeRegionAmdPlaneCopy(const amf_uint8 *src, // IN
                            amf_int32 srcStride,  // IN
                            amf_int32 srcHeight,  // IN
                            amf_uint8 *dst,       // IN/OUT
                            amf_int32 dstStride,  // IN
                            amf_int32 dstHeight)  // IN
{
   amf_int32 minHeight = AMF_MIN(srcHeight, dstHeight);
   if (srcStride == dstStride) {
      memcpy(dst, src, minHeight * srcStride);
   } else {
      int minStride = AMF_MIN(srcStride, dstStride);
      for (int y = 0; y < minHeight; y++) {
         memcpy(dst + dstStride * y, src + srcStride * y, minStride);
      }
   }
}


/*
 *----------------------------------------------------------------------------
 *
 * VNCEncodeRegionAmdRect --
 *
 *      Send h264 network abstraction layer units across our VNC
 *      connection.  Currently bundling all NALs into a single
 *      rectangle.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      Expands the encode buffer, might allocate memory.
 *
 *----------------------------------------------------------------------------
 */

static void
VNCEncodeRegionAmdRect(BoxPtr dstRect,    // IN
                       VNCEncodeBuf *buf, // IN/OUT
                       uint16 opcode,     // IN
                       uint16 streamId,   // IN
                       Bool isH264YUV444, // IN
                       const void *data,  // IN
                       int dataSize)      // IN
{
   VNCH264Rect *rect = VNCEncodeBufRect(buf, sizeof *rect + dataSize);

   /*
    * The destination rectangle is currently always full-screen,
    * although the encoding allows more flexibilty than this.
    */
   if (isH264YUV444) {
      VNCEncodeRectHeader(&rect->header, dstRect, VNCH264YUV444RectEnc);
   } else {
      VNCEncodeRectHeader(&rect->header, dstRect, VNCH264RectEnc);
   }

   rect->opcode = htons(opcode);
   rect->streamId = htons(streamId);
   rect->dataLength = htonl(dataSize);

   /*
    * Copy the payload into the buffer
    */
   memcpy(rect + 1, data, dataSize);
}


/*
 *----------------------------------------------------------------------------
 *
 * VNCEncodeRegionAMDDumpBitstream --
 *
 *      Dump compressed Elementary Stream.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      Generates HUGE files.
 *
 *----------------------------------------------------------------------------
 */

static void
VNCEncodeRegionAmdDumpBitstream(VNCRegionEncoderAmd *regEnc,    // IN/OUT
                                const unsigned char *bitstream, // IN
                                int frameSize)                  // IN
{
   VERIFY(vmx86_debug);
   if (regEnc && regEnc->fhandleBitstream) {
      /*
       * Log the data to a file, which can be played with ffplay,
       * mplayer, etc.
       */
      if (fwrite(bitstream, frameSize, 1, regEnc->fhandleBitstream) == 0) {
         REGENCWARN("Write to bitstream dump file failed");
         VNCEncodeRegionCloseFile(&regEnc->fhandleBitstream);
      } else {
         REGENCWARN("Wrote frameSize %d", frameSize);
      }
   }
}


/*
 *----------------------------------------------------------------------------
 *
 * VNCEncodeRegionAmdSendNals --
 *
 *      Sends the encoded h264 payload for packetization
 *      and transmission.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      Expands the encode buffer, might allocate memory.
 *
 *----------------------------------------------------------------------------
 */

static void
VNCEncodeRegionAmdSendNals(VNCRegionEncoderAmd *regEnc, // IN
                           VNCEncodeBuf *buf,           // IN/OUT
                           const void *hwh264nal,       // IN
                           int nalCount,                // IN
                           int totalSize)               // IN
{
   int opcode;
   const VMRect *regionRect = &regEnc->base.config.region.rect;
   BoxRec rect;

   switch (regEnc->codecType) {
   case VNCENCODE_CODEC_TYPE_AV1:
      opcode = regEnc->firstTime ? VNC_AV1_RESET_STREAM : VNC_AV1_DATA;
      break;
   case VNCENCODE_CODEC_TYPE_HEVC:
      opcode = regEnc->firstTime ? VNC_HEVC_RESET_STREAM : VNC_HEVC_DATA;
      break;
   case VNCENCODE_CODEC_TYPE_H264:
   default:
      opcode = regEnc->firstTime ? VNC_H264_RESET_STREAM : VNC_H264_DATA;
      break;
   }
   if (regEnc->firstTime) {
      regEnc->firstTime = FALSE;
   }

   RECT_SETVMRECT(&rect, regionRect);

   /*
    * Pack the NALs into our EncodeBuf with an appropriate VNC
    * rectangle header.
    */
   if (regEnc->encodeAsH264) {
      VNCEncodeRegionAmdRect(&rect, buf, opcode, regEnc->base.config.screenNum, regEnc->useYUV444,
                             hwh264nal, totalSize);
   } else {
      ASSERT(BitVector_Test(regEnc->base.config.caps, VNCH264MP4RectCap));
      VNCEncodeMP4Rect(&rect, buf, opcode, regEnc->base.config.screenNum, hwh264nal, totalSize,
                       nalCount, regEnc->frameNumber);
   }

   regEnc->frameNumber++;
}


/*
 *-------------------------------------------------------------------------
 *
 * VNCEncodeRegionAmdCreateComponent --
 *
 *      Helper function to create AMD encoder component
 *
 * Results:
 *
 *
 * Side effects:
 *      None.
 *
 *-------------------------------------------------------------------------
 */

static AMF_RESULT
VNCEncodeRegionAmdCreateComponent(VNCRegionEncoderAmd *regEnc, // IN/OUT
                                  const wchar_t *codecId)      // IN/OUT
{
   AMF_RESULT res = AMF_OK;
   ;

   if (!regEnc || !regEnc->factory || !regEnc->context) {
      return AMF_NOT_SUPPORTED;
   }

   res = regEnc->factory->pVtbl->CreateComponent(regEnc->factory, regEnc->context, codecId,
                                                 &regEnc->encoder);
   if (res != AMF_OK) {
      REGENCWARN("%s: Creating encoder component failed", __FUNCTION__);
   }
   return res;
}


/*
 *----------------------------------------------------------------------------
 *
 * VNCEncodeRegionAMDInitDevice --
 *
 *      Helper function Initialize device specific resources.
 *
 * Results:
 *
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

static AMF_RESULT
VNCEncodeRegionAmdInitDevice(VNCRegionEncoderAmd *regEnc, // IN/OUT
                             void *devicePtr)             // IN
{
   AMF_RESULT res = AMF_OK;

   if (!regEnc || !regEnc->factory || !regEnc->context) {
      return AMF_NO_INTERFACE;
   }
   res = regEnc->context->pVtbl->InitDX11(regEnc->context, devicePtr, AMF_DX11_0);
   if (res != AMF_OK) {
      REGENCWARN("%s: AMF  DX11 initialization failed.", __FUNCTION__);
   }
   return res;
}


/*
 *----------------------------------------------------------------------------
 *
 * VNCEncodeRegionAmdEncoderInit --
 *
 *      Create and setup Amd Encoder.
 *
 * Results:
 *      mfxStatus of encoder init.
 *
 * Side effects:
 *      None
 *
 *----------------------------------------------------------------------------
 */

static AMF_RESULT
VNCEncodeRegionAmdEncoderInit(VNCRegionEncoderAmd *regEnc,           // IN
                              int width,                             // IN
                              int height,                            // IN
                              void *devicePtr,                       // IN
                              const VNCRegEncFrameState *frameState) // IN
{
   const VNCEncodeDynamicConfig *config = &regEnc->base.config.dynamicConfig;
   const wchar_t *codecId = NULL;
   AMF_RESULT res = AMF_OK;

   res = VNCEncodeRegionAmdInitDevice(regEnc, devicePtr);

   if (res != AMF_OK) {
      REGENCWARN("%s: AMF initialization failed with D3D11.", __FUNCTION__);
      return res;
   }

   switch (regEnc->codecType) {
   case VNCENCODE_CODEC_TYPE_AV1:
      codecId = AMFVideoEncoder_AV1;
      break;
   case VNCENCODE_CODEC_TYPE_HEVC:
      codecId = AMFVideoEncoder_HEVC;
      break;
   case VNCENCODE_CODEC_TYPE_H264:
   default:
      codecId = AMFVideoEncoderVCE_AVC;
      break;
   }

   res = VNCEncodeRegionAmdCreateComponent(regEnc, codecId);

   if (res != AMF_OK) {
      REGENCWARN("%s: AMF  create component failed.", __FUNCTION__);
      return res;
   }

   VNCEncodeRegionAmdSetEncoderStaticParams(regEnc, width, height);
   VNCEncodeRegionAmdSetEncoderDynamicParams(regEnc);

   res = regEnc->encoder->pVtbl->Init(regEnc->encoder, AMF_SURFACE_BGRA, width, height);

   if (res != AMF_OK) {
      REGENCWARN("%s: AMF  encoder->Init() failed.", __FUNCTION__);
   }

   REGENCLG0_ONCE("%s: Amd encoder initialized \n", __FUNCTION__);
   amdEncInitialized = TRUE;
   return res;
}


/*
 *----------------------------------------------------------------------------
 *
 * VNCEncodeRegionAmdCreate --
 *
 *      Create and initialize the AMD encoder.
 *
 * Results:
 *      Returns TRUE if successful, FALSE otherwise.
 *
 * Side effects:
 *      None
 *
 *----------------------------------------------------------------------------
 */

static VNCError
VNCEncodeRegionAmdCreate(VNCRegionEncoderAmd *regEnc,           // IN/OUT
                         const VNCRegEncFrameState *frameState) // IN
{
   const VMRect *rect = &regEnc->base.config.region.rect;
   VNCError error = VNC_SUCCESS;
   AMF_RESULT res = AMF_OK;

   /* UnifiedD3DDevice is a Windows-only feature. */
   if (!regEnc->useUnifiedD3DDevice || vmx86_linux) {
      error = VNCEncodeRegionAmdCreateOS(regEnc, frameState->adapterId);
      if (error != VNC_SUCCESS) {
         goto exit;
      }
   }

   if (regEnc->useUnifiedD3DDevice && frameState && frameState->texture) {
      ID3D11Texture2D_GetDevice((ID3D11Texture2D *)frameState->texture, &regEnc->pD3d11Device);
      ID3D11Device_GetImmediateContext(regEnc->pD3d11Device, &regEnc->pD3d11DeviceContext);
      InitializeCriticalSection(&regEnc->csD3d11DeviceContext);
   }

   /* Create the encoder device for the i-th screen. */
   res = VNCEncodeRegionAmdEncoderInit(regEnc, Rect_WH(rect), regEnc->pD3d11Device, frameState);

   if (res != AMF_OK || !regEnc->encoder) {
      error = VNC_ERROR_HW_ENCODE_FAILED;
      REGENCWARN("%s: Failed to create the AMD HW encoder session\n", __FUNCTION__);
   }

exit:
   return error;
}


/*
 *----------------------------------------------------------------------------
 *
 * VNCEncodeRegionAmdCloseHandles --
 *
 *      Unregisters all input resources, destroys all textures, and
 *      empties the list of shared handles.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

static void
VNCEncodeRegionAmdCloseHandles(VNCRegionEncoderAmd *regEnc) // IN
{
   int i;

   ASSERT(regEnc);

   for (i = 0; i < ARRAYSIZE(regEnc->inputRegHandle); i++) {

      if (regEnc->pD3d11Texture[i]) {
         ID3D11Texture2D_Release(regEnc->pD3d11Texture[i]);
         regEnc->pD3d11Texture[i] = NULL;
      }
      if (regEnc->pD3d11TextureKeyedMutex[i]) {
         IDXGIKeyedMutex_Release(regEnc->pD3d11TextureKeyedMutex[i]);
         regEnc->pD3d11TextureKeyedMutex[i] = NULL;
      }
      if (regEnc->pD3d11TextureMappable[i]) {
         ID3D11Texture2D_Release(regEnc->pD3d11TextureMappable[i]);
         regEnc->pD3d11TextureMappable[i] = NULL;
      }

      ASSERT_ON_COMPILE(ARRAYSIZE(regEnc->inputRegHandle) == ARRAYSIZE(regEnc->pD3d11Texture));
      ASSERT_ON_COMPILE(ARRAYSIZE(regEnc->inputRegHandle) ==
                        ARRAYSIZE(regEnc->pD3d11TextureKeyedMutex));
      ASSERT_ON_COMPILE(ARRAYSIZE(regEnc->inputRegHandle) ==
                        ARRAYSIZE(regEnc->pD3d11TextureMappable));

      regEnc->pSharedHandle[i] = NULL;
      ASSERT_ON_COMPILE(ARRAYSIZE(regEnc->inputRegHandle) == ARRAYSIZE(regEnc->pSharedHandle));
   }
}


/*
 *----------------------------------------------------------------------------
 *
 * VNCEncodeRegionAmdFindFreeHandleSlot --
 *
 *      Find the first free slot in the shared handle array.
 *
 * Results:
 *      The slot index, or -1 if there is no free slot.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

static int
VNCEncodeRegionAmdFindFreeHandleSlot(VNCRegionEncoderAmd *regEnc) // IN
{
   int texIdx;

   if (regEnc->useUnifiedD3DDevice) {
      for (texIdx = 0; texIdx < ARRAYSIZE(regEnc->pD3d11Texture); ++texIdx) {
         if (regEnc->pD3d11Texture[texIdx] == NULL) {
            return texIdx;
         }
      }

      return -1;
   }


   for (texIdx = 0; texIdx < ARRAYSIZE(regEnc->pSharedHandle); ++texIdx) {
      if (regEnc->pSharedHandle[texIdx] == NULL) {
         return texIdx;
      }
   }

   return -1;
}


/*
 *----------------------------------------------------------------------------
 *
 * VNCEncodeRegionAmdFindSharedHandle --
 *
 *      Match the given handle in the shared handle array.
 *
 * Results:
 *      The slot index, or -1 if not found.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

static int
VNCEncodeRegionAmdFindSharedHandle(VNCRegionEncoderAmd *regEnc,           // IN
                                   const VNCRegEncFrameState *frameState) // IN
{
   int texIdx;

   if (regEnc->useUnifiedD3DDevice) {
      for (texIdx = 0; texIdx < ARRAYSIZE(regEnc->pD3d11Texture); ++texIdx) {
         if (regEnc->pD3d11Texture[texIdx] != NULL &&
             regEnc->pD3d11Texture[texIdx] == frameState->texture) {
            return texIdx;
         }
      }

      return -1;
   }


   for (texIdx = 0; texIdx < ARRAYSIZE(regEnc->pSharedHandle); ++texIdx) {
      if (regEnc->pSharedHandle[texIdx] == frameState->sharedHandle) {
         return texIdx;
      }
   }

   return -1;
}


/*
 *----------------------------------------------------------------------------
 *
 * VNCEncodeRegionAmdPerformEncode --
 *
 *      Encode the given adapter's screen contents as an H.264 frame using the
 *      AMF SDK. Also maybe update the bandwidth and quality levels.
 *
 * Results:
 *      Returns VNCError code.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

static VNCError
VNCEncodeRegionAmdPerformEncode(VNCRegionEncoderAmd *regEnc,           // IN
                                VNCEncodeBuf *buf,                     // IN/OUT
                                int texIdx,                            // IN
                                const VNCRegEncFrameState *frameState) // IN
{
   int ret = 0;
   AMF_RESULT res = AMF_OK;
   AMFSurface *surface = NULL;
   AMFData *data = NULL;
   AMFBuffer *buffer;
   VNCError error = VNC_ERROR_HW_ENCODE_FAILED;
   unsigned char *outputBuffer = NULL;
   int frameSize;

   if (!regEnc) {
      return VNC_ERROR_GENERIC;
   }

   if (surface == NULL) {
      if (!regEnc->useCpuMapping) {
         res = regEnc->context->pVtbl->CreateSurfaceFromDX11Native(
            regEnc->context, regEnc->inputRegHandle[texIdx], &surface, NULL);
         if (res != AMF_OK) {
            REGENCWARN("%s Create surface with DX11 failed", __FUNCTION__);
            goto end;
         }
         surface->pVtbl->SetCrop(surface, 0, 0, regEnc->width, regEnc->height);

      } else {

         res =
            regEnc->context->pVtbl->AllocSurface(regEnc->context, AMF_MEMORY_HOST, AMF_SURFACE_BGRA,
                                                 regEnc->width, regEnc->height, &surface);

         AMFPlane *plane = surface->pVtbl->GetPlaneAt(surface, 0);
         VNCEncodeRegionAmdPlaneCopy(
            (uint8 *)frameState->fbPtr, (regEnc->width) * 4, regEnc->height,
            (unsigned char *)(plane->pVtbl->GetNative(plane)), plane->pVtbl->GetHPitch(plane),
            plane->pVtbl->GetVPitch(plane));
      }
   }

   if (surface == NULL) {
      REGENCWARN("%s Surface is NULL", __FUNCTION__);
      goto end;
   }
   /*
    * Update the AMD HW Encoder bitrate every second
    * after the first 5 seconds of the connection
    */
   if (regEnc->lastBWupdate + 1 < frameState->pumpTime ||
       regEnc->base.config.startTime + 5 > frameState->pumpTime) {
      VNCEncodeRegionAmdSetBandwidth(regEnc, frameState->bandwidth, &regEnc->rcParamNew);

      switch (regEnc->codecType) {
      case VNCENCODE_CODEC_TYPE_AV1:
         AMF_ASSIGN_PROPERTY_INT64(res, regEnc->encoder, AMF_VIDEO_ENCODER_AV1_TARGET_BITRATE,
                                   regEnc->rcParamNew.targetBitsPerSec);
         AMF_ASSIGN_PROPERTY_INT64(res, regEnc->encoder, AMF_VIDEO_ENCODER_AV1_PEAK_BITRATE,
                                   regEnc->rcParamNew.peakBitsPerSec);
         break;
      case VNCENCODE_CODEC_TYPE_HEVC:
         AMF_ASSIGN_PROPERTY_INT64(res, regEnc->encoder, AMF_VIDEO_ENCODER_HEVC_TARGET_BITRATE,
                                   regEnc->rcParamNew.targetBitsPerSec);
         AMF_ASSIGN_PROPERTY_INT64(res, regEnc->encoder, AMF_VIDEO_ENCODER_HEVC_PEAK_BITRATE,
                                   regEnc->rcParamNew.peakBitsPerSec);
         break;
      case VNCENCODE_CODEC_TYPE_H264:
         AMF_ASSIGN_PROPERTY_INT64(res, regEnc->encoder, AMF_VIDEO_ENCODER_TARGET_BITRATE,
                                   regEnc->rcParamNew.targetBitsPerSec);
         AMF_ASSIGN_PROPERTY_INT64(res, regEnc->encoder, AMF_VIDEO_ENCODER_PEAK_BITRATE,
                                   regEnc->rcParamNew.peakBitsPerSec);
         break;
      default:
         break;
      }

      regEnc->lastBWupdate = frameState->pumpTime;
   }

   /*
    * Turn off AUD being inserted with every frame
    */
   if (regEnc->codecType == VNCENCODE_CODEC_TYPE_H264) {
      AMF_ASSIGN_PROPERTY_BOOL(res, surface, AMF_VIDEO_ENCODER_INSERT_AUD, false);
   } else if (regEnc->codecType == VNCENCODE_CODEC_TYPE_HEVC) {
      AMF_ASSIGN_PROPERTY_BOOL(res, surface, AMF_VIDEO_ENCODER_HEVC_INSERT_AUD, false);
   }

   res = regEnc->encoder->pVtbl->SubmitInput(regEnc->encoder, (AMFData *)surface);
   if (res != AMF_OK) {
      surface->pVtbl->Release(surface);
      surface = NULL;
      REGENCWARN("%s %d, encoder submit input failed", __FUNCTION__, __LINE__);
      goto end;
   }

   while (true) {
      /*
       * If an output sample is not available yet, QueryOutput will return
       * AMF_REPEAT, indicating that the call needs to be retried after some
       * period of time. When draining has been initiated and the last output
       * sample has been retrieved, QueryOutput returns AMF_EOF.
       */
      res = regEnc->encoder->pVtbl->QueryOutput(regEnc->encoder, &data);
      if (res != AMF_REPEAT) {
         break;
      }
   }

   if (data == NULL) {
      REGENCWARN("%s Could not get the output data", __FUNCTION__);
      goto end;
   }

   AMFGuid guid = IID_AMFBuffer();
   data->pVtbl->QueryInterface(data, &guid, (void **)&buffer); // query for buffer interface
   frameSize = buffer->pVtbl->GetSize(buffer);
   outputBuffer = Util_SafeMalloc(frameSize);
   memcpy(outputBuffer, buffer->pVtbl->GetNative(buffer), frameSize);
   buffer->pVtbl->Release(buffer);
   data->pVtbl->Release(data);
   regEnc->encFrameNum++;
   REGENC_RLOG(9, "%s encoded frame %d with size %6d bytes", __FUNCTION__, regEnc->encFrameNum,
               frameSize);

   /* Package the NAL into a VNC message and put it on the wire. */
   VNCEncodeRegionAmdSendNals(regEnc, buf, outputBuffer, 1, frameSize);

   /* Assert that no frames were forgotten or sent twice. */
   regEnc->sendFrameNum++;
   ASSERT(regEnc->sendFrameNum == regEnc->encFrameNum);
   if (vmx86_debug) {
      /* Dump H264 compressed bitstream if enabled */
      VNCEncodeRegionAmdDumpBitstream(regEnc, outputBuffer, frameSize);
      /* Dump uncompressed screen contents if enabled */
      VNCEncodeRegionAmdDumpARGB(regEnc, texIdx);
   }
   /* If we have reached this point, we have succeeded in encoding the frame
    * return VNC_SUCCESS
    */
   if (surface != NULL) {
      surface->pVtbl->Release(surface);
      surface = NULL;
   }
   free(outputBuffer);
   error = VNC_SUCCESS;

end:
   return error;
}


/*
 *----------------------------------------------------------------------------
 *
 * VNCEncodeRegionAmd_Encode --
 *
 *      Sends any new H.264 frame(s) which were encoded in hardware using the
 *      AMF.
 *
 * Results:
 *      VNC_SUCCESS or VNC_ERROR_*.
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

static VNCError
VNCEncodeRegionAmd_Encode(VNCRegionEncoder *base,                // IN/OUT
                          const VNCRegEncFrameState *frameState, // IN
                          VNCEncodeBuf *buf)                     // IN/OUT
{
   VNCRegionEncoderAmd *regEnc = (VNCRegionEncoderAmd *)base;
   int texIdx = 0;
   VNCError error = VNC_SUCCESS;

   if (!regEnc) {
      return VNC_ERROR_GENERIC;
   }

   VNCBitmask_Union(regEnc->dirtyMask, frameState->dirtyMask);

   /* Create AMD encoder if it has not yet been done. */
   if (regEnc->encoder == NULL) {
#   ifdef _WIN32
      if (regEnc->base.config.staticConfig.allowCaptureEncodeUnifiedD3DDevice &&
          frameState->supportCaptureEncodeUnifiedD3DDevice) {
         /*
          * AMD Encoder currently supports only unified D3D device. Support
          * for non-unified would come later. It is optimal to use unified D3D
          * device since the synchronization of access to the shared texture can
          * be removed.
          */
         regEnc->useUnifiedD3DDevice = TRUE;
#   else
      regEnc->useUnifiedD3DDevice = FALSE;
#   endif
      }

      error = VNCEncodeRegionAmdCreate(regEnc, frameState);
      if (error != VNC_SUCCESS) {
         REGENCWARN("%s: Failed to create AMD encoder", __FUNCTION__);
         goto end;
      }
   }

   if (!regEnc->useCpuMapping) {
      /* Find index of texture associated with shared handle - maybe create it. */
      texIdx = VNCEncodeRegionAmdFindSharedHandle(regEnc, frameState);
      if (texIdx < 0 && !VNCEncodeRegionAmdRegisterTexture(regEnc, frameState, &texIdx)) {
         REGENCWARN("%s: Failed to register texture", __FUNCTION__);
         error = VNC_ERROR_HW_ENCODE_FAILED;
         goto end;
      }
   }


   ENTER_CRITICAL_SECTION(regEnc)


   /* Perform a synchronous encode. */
   error = VNCEncodeRegionAmdPerformEncode(regEnc, buf, texIdx, frameState);
   if (error != VNC_SUCCESS) {
      REGENCWARN("%s: VNCEncodeRegionAmdPerformEncode failed", __FUNCTION__);
      goto end;
   }

   /*
    * Stop encoding if we have not received any dirtyFrame in a while.
    */
   if (VNCBitmask_IsEmpty(regEnc->dirtyMask)) {
      regEnc->boostFrames++;
   } else {
      regEnc->boostFrames = 0;
   }

   if (regEnc->boostFrames > MAX_CONSECUTIVE_BOOST_FRAMES) {
      regEnc->isFrameMaxQual = TRUE;
      REGENC_RLOG(3, "Too many consecutive empty frames encoded. Stopping the encode");
   } else {
      regEnc->isFrameMaxQual = FALSE;
   }

   REGENC_RLOG(9, "DirtyMBs: %d boostFrames: %d", VNCBitmask_PopCount(regEnc->dirtyMask),
               regEnc->boostFrames);

   VNCBitmask_Clear(regEnc->dirtyMask);
   REGENC_RLOG(9, "DirtyMBs: %d boostFrames: %d", VNCBitmask_PopCount(regEnc->dirtyMask),
               regEnc->boostFrames);

   VNCBitmask_Clear(regEnc->dirtyMask);

end:
   if (error == VNC_SUCCESS) {
      if (regEnc->isFrameMaxQual) {
         regEnc->base.encodeState = REGENC_IDLE;
      } else {
         regEnc->base.encodeState = REGENC_FB_BOOSTING;
         regEnc->base.nextBoostTime = VNCUtil_SystemTime() + (1.0 / MAX_BOOST_FPS);
      }
   } else {
      lastFailedCreateTime = VNCUtil_SystemTime();
   }


   LEAVE_CRITICAL_SECTION(regEnc)


   return error;
}


/*
 *----------------------------------------------------------------------------
 *
 * VNCEncodeRegionAmdDestroy --
 *
 *      Destroy the region encoder's encoder object and associated resources for
 *      the given region.
 *
 * Results:
 *      None.
 *
 * Side effects:
 *      None
 *
 *----------------------------------------------------------------------------
 */

static void
VNCEncodeRegionAmdDestroy(VNCRegionEncoderAmd *regEnc) // IN
{
   if (!regEnc) {
      return;
   }

   VNCEncodeRegionAmdCloseHandles(regEnc);
   VNCEncodeRegionAmdDestroyOS(regEnc);
}


/*
 *----------------------------------------------------------------------------
 *
 * VNCEncodeRegionAmd_Destroy --
 *
 *      Destroy the encoder.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      Frees memory.
 *
 *----------------------------------------------------------------------------
 */

static void
VNCEncodeRegionAmd_Destroy(VNCRegionEncoder *base) // IN
{
   VNCRegionEncoderAmd *regEnc = (VNCRegionEncoderAmd *)base;

   if (regEnc != NULL) {

      VNCBitmask_Destroy(regEnc->dirtyMask);
      VNCEncodeRegionAmdDestroy(regEnc);

      /* will lose any delayed frames */
      VNCEncodeRegionCloseFile(&regEnc->fhandleBitstream);

      /* will lose any delayed frames */
      VNCEncodeRegionCloseFile(&regEnc->fhandleRaw);

      if (!regEnc->encodeAsH264) {
         VNCEncodeStopMP4DebugOutput();
      }

      if (regEnc->encoder) {
         regEnc->encoder->pVtbl->Terminate(regEnc->encoder);
         regEnc->encoder->pVtbl->Release(regEnc->encoder);
         regEnc->encoder = NULL;
      }
      if (regEnc->context) {
         regEnc->context->pVtbl->Terminate(regEnc->context);
         regEnc->context->pVtbl->Release(regEnc->context);
         regEnc->context = NULL; // context is the last
      }
      if (regEnc->factory) {
         regEnc->factory = NULL;
      }
      amdEncInitialized = FALSE;

      free(regEnc);
   }
   VNCEncodeRegionAmdUnloadLibrary();
}


/*
 *----------------------------------------------------------------------------
 *
 * VNCEncodeRegionAmdQueryEncoderCaps --
 *
 *      Queries the capabilities of the Amd H264/HEVC/AV1 encoder.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      Allocates memory.
 *
 *----------------------------------------------------------------------------
 */

static Bool
VNCEncodeRegionAmdQueryEncoderCaps(const VNCRegEncConfig *config,  // IN
                                   AMFFactory *factory,            // IN
                                   AMFContext *context,            // IN
                                   const wchar_t *codecId,         // IN
                                   const wchar_t *hwInstanceCap,   // IN
                                   const wchar_t *hwInstanceIndex, // IN
                                   int *maxWidth,                  // IN/OUT
                                   int *maxHeight,                 // IN/OUT
                                   Bool *supportsYUV444)           // IN/OUT
{

   AMFComponent *encoder = NULL;
   AMF_RESULT res = AMF_OK;
   AMFCaps *encoderCaps = NULL;
   amf_uint32 numOfHWInstances = 1;
   AMFVariantStruct var = {0};
   Bool success = FALSE;

   AMDREGENC_DUMMY_LOCAL(config->encoderId, config->dynamicConfig);

   res = factory->pVtbl->CreateComponent(factory, context, codecId, &encoder);
   if (res != AMF_OK || encoder == NULL) {
      REGENCWARN("%s: Creating encoder component failed", __FUNCTION__);
      return success;
   }

   res = encoder->pVtbl->GetCaps(encoder, &encoderCaps);
   if (res != AMF_OK || encoderCaps == NULL) {
      REGENCWARN("%s: Getting encoder caps failed", __FUNCTION__);
      goto exit;
   }

   encoderCaps->pVtbl->GetProperty(encoderCaps, hwInstanceCap, &var);
   numOfHWInstances = AMFVariantGetInt64(&var);
   REGENC_RLOG(9, "%s Number of hardware instances supported", __FUNCTION__, numOfHWInstances);
   if (numOfHWInstances == 0) {
      REGENCWARN("%s: Number of hardware instances is 0", __FUNCTION__);
      goto exit;
   }

   for (amf_uint64 i = 0; i < numOfHWInstances; i++) {
      if (numOfHWInstances > 1) {
         memset(&var, 0, sizeof(var));
         AMFVariantAssignInt64(&var, i);
         encoderCaps->pVtbl->SetProperty(encoderCaps, hwInstanceIndex, var);
      }

      // Output Caps
      AMFIOCaps *outputCaps = NULL;

      res = encoderCaps->pVtbl->GetOutputCaps(encoderCaps, &outputCaps);
      if (res != AMF_OK || outputCaps == NULL) {
         REGENCWARN("%s: getting output caps failed", __FUNCTION__);
         goto exit;
      }

      amf_int32 outMinWidth, outMaxWidth;
      outputCaps->pVtbl->GetWidthRange(outputCaps, &outMinWidth, &outMaxWidth);
      if (*maxWidth == 0) {
         *maxWidth = outMaxWidth;
      } else {
         *maxWidth = MIN(*maxWidth, outMaxWidth);
      }

      amf_int32 outMinHeight, outMaxHeight;
      outputCaps->pVtbl->GetHeightRange(outputCaps, &outMinHeight, &outMaxHeight);
      if (*maxHeight == 0) {
         *maxHeight = outMaxHeight;
      } else {
         *maxHeight = MIN(*maxHeight, outMaxHeight);
      }
      // AMD does not support H264 4:4:4
      *supportsYUV444 = FALSE;
      amf_int32 outNumOfFormats = outputCaps->pVtbl->GetNumOfFormats(outputCaps);
      REGENC_RLOG(9, "%s Total of %d pixel formats supported", __FUNCTION__, outNumOfFormats);

      for (amf_int32 i = 0; i < outNumOfFormats && config->dynamicConfig.vncRegEncLogLevel >= 9;
           i++) {
         AMF_SURFACE_FORMAT format;
         amf_bool native = false;
         if (outputCaps->pVtbl->GetFormatAt(outputCaps, i, &format, &native) == AMF_OK) {
            REGENC_RLOG(9, "%s Format type %d %s", __FUNCTION__, format, native ? "(native)" : "");
         } else {
            break;
         }
      }
      if (outputCaps) {
         outputCaps->pVtbl->Release(outputCaps);
         outputCaps = NULL;
      }
   }
   success = TRUE;
exit:

   if (encoder) {
      encoder->pVtbl->Terminate(encoder);
      encoder->pVtbl->Release(encoder);
      encoder = NULL;
   }
   return success;
}


/*
 *----------------------------------------------------------------------------
 *
 * VNCEncodeRegionAmd_Create --
 *
 *      Creates the AMD encoder.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      Allocates memory.
 *
 *----------------------------------------------------------------------------
 */

VNCRegionEncoder *
VNCEncodeRegionAmd_Create(const VNCRegEncConfig *regEncConfig, // IN
                          VNCEncodeCodecType codecType,        // IN
                          VNCServerOSCaptureType captureType)  // IN
{
   const VNCEncodeDynamicConfig *config = &regEncConfig->dynamicConfig;
   uint32 encodeAsH264 = BitVector_Test(regEncConfig->caps, VNCH264RectCap);
   // TODO: Do not announce MP4 support until we test on Chrome browser
   uint32 encodeAsMP4 = 0;
   struct VNCRegionEncoderAmd *regEnc = NULL;
   uint32 width = Rect_Width(&regEncConfig->region.rect);
   uint32 height = Rect_Height(&regEncConfig->region.rect);
   Bool isCaptureTypeGpuMem = (captureType == VNC_SERVEROS_CAPTURE_TYPE_HW) ? TRUE : FALSE;
   AMF_RESULT res = AMF_OK;
   double currentTime = VNCUtil_SystemTime();

   /*
    * If AMD Encoder failed during encode, then disable AMD HW RegEnc creation
    * until cool off timeout, and try only when there is a RegEnc creation
    * post the timeout
    */
   if (lastFailedCreateTime != 0.0 &&
       currentTime - lastFailedCreateTime < config->cooloffTimeoutSec) {
      REGENCWARN_ONCE("%2.2f seconds elapsed since last HW Encoder "
                      "failure. Will not retry creation for %2.2f seconds",
                      currentTime - lastFailedCreateTime, config->cooloffTimeoutSec);
      return NULL;
   }
   /* Try to load the shared libraries; abort immediately if this fails. */
   if (!VNCEncodeRegionAmdLoadLibrary()) {
      return NULL;
   }

   /*
    * Abort if:
    * - width or height is odd, and we did not explicitly enable support of
    *   odd resolutions.
    * - the resolution is 0 x 0.
    */
   if ((!config->allowOddDimensionsH264 && (width & 1 || height & 1)) || width == 0 ||
       height == 0) {
      REGENCWARN("Unsupported resolution %dx%d.", width, height);
      goto fail;
   }

   /*
    * Check for max supported resolution
    */
   if (codecType == VNCENCODE_CODEC_TYPE_AV1) {
      if (width > sCaps.av1MaxWidth || height > sCaps.av1MaxHeight) {
         REGENCWARN("Frame size (%dx%d pixels) > AV1 max (%dx%d pixels)", width, height,
                    sCaps.av1MaxWidth, sCaps.av1MaxHeight);
         goto fail;
      }
   } else if (codecType == VNCENCODE_CODEC_TYPE_HEVC) {
      if (width > sCaps.hevcMaxWidth || height > sCaps.hevcMaxHeight) {
         REGENCWARN("Frame size (%dx%d pixels) > HEVC max (%dx%d pixels)", width, height,
                    sCaps.hevcMaxWidth, sCaps.hevcMaxHeight);
         goto fail;
      }
   } else if (codecType == VNCENCODE_CODEC_TYPE_H264) {
      if (width > sCaps.h264MaxWidth || height > sCaps.h264MaxHeight) {
         REGENCWARN("Frame size (%dx%d pixels) > H264 max (%dx%d pixels)", width, height,
                    sCaps.h264MaxWidth, sCaps.h264MaxHeight);
         goto fail;
      }
   }

   if (!encodeAsH264 && !encodeAsMP4) {
      REGENCWARN("H.264 decoding cap unsupported by the client.");
      goto fail;
   }

   regEnc = Util_SafeCalloc(1, sizeof *regEnc);

   REGENC_RLOG(9, "Attempting to create Amd RegEnc with %s:%s:%s",
               (codecType == VNCENCODE_CODEC_TYPE_AV1)    ? "AV1"
               : (codecType == VNCENCODE_CODEC_TYPE_HEVC) ? "HEVC"
                                                          : "H264",
               VNCEncodeRegionAmdIsYUV444Requested(regEnc) ? "444" : "420",
               isCaptureTypeGpuMem ? "HW" : "SW");

   res = amdsdk.AMFInit(AMF_FULL_VERSION, &regEnc->factory);
   if (res != AMF_OK) {
      REGENCWARN("Failed to initialize AMF");
      goto fail;
   }

   res = regEnc->factory->pVtbl->CreateContext(regEnc->factory, &regEnc->context);
   if (res != AMF_OK) {
      REGENCWARN("Failed to create AMF context");
      goto fail;
   }

   REGENCLG0_ONCE("AMF Initialized");

   regEnc->useCpuMapping = !isCaptureTypeGpuMem;

   VNCRegionEncoderInit(&regEnc->base, regEncConfig,
                        regEnc->useCpuMapping ? VNC_SERVEROS_CAPTURE_TYPE_SW
                                              : VNC_SERVEROS_CAPTURE_TYPE_HW,
                        regEncConfig->region.hdrEnabled ? VNC_SERVEROS_PIXEL_FORMAT_10BIT_BGRX
                                                        : VNC_SERVEROS_PIXEL_FORMAT_8BIT_BGRX);

   regEnc->codecType = codecType;
   regEnc->useYUV444 =
      VNCEncodeRegionAmdIsYUV444Requested(regEnc) && VNCEncodeRegionAmdIsYUV444Supported(regEnc);

   switch (regEnc->codecType) {
   case VNCENCODE_CODEC_TYPE_AV1:
      if (regEnc->useCpuMapping) {
         regEnc->base.type = regEnc->useYUV444 ? VNC_REGION_ENCODER_AMD_SW_AV1_444
                                               : VNC_REGION_ENCODER_AMD_SW_AV1_420;
      } else {
         regEnc->base.type = regEnc->useYUV444 ? VNC_REGION_ENCODER_AMD_HW_AV1_444
                                               : VNC_REGION_ENCODER_AMD_HW_AV1_420;
      }
      break;
   case VNCENCODE_CODEC_TYPE_HEVC:
      if (regEnc->useCpuMapping) {
         regEnc->base.type = regEnc->useYUV444 ? VNC_REGION_ENCODER_AMD_SW_HEVC_444
                                               : VNC_REGION_ENCODER_AMD_SW_HEVC_420;
      } else {
         regEnc->base.type = regEnc->useYUV444 ? VNC_REGION_ENCODER_AMD_HW_HEVC_444
                                               : VNC_REGION_ENCODER_AMD_HW_HEVC_420;
      }
      break;
   case VNCENCODE_CODEC_TYPE_H264:
   default:
      if (regEnc->useCpuMapping) {
         regEnc->base.type = regEnc->useYUV444 ? VNC_REGION_ENCODER_AMD_SW_H264_444
                                               : VNC_REGION_ENCODER_AMD_SW_H264_420;
      } else {
         regEnc->base.type = regEnc->useYUV444 ? VNC_REGION_ENCODER_AMD_HW_H264_444
                                               : VNC_REGION_ENCODER_AMD_HW_H264_420;
      }
   }

   regEnc->base.allowMultipleInflightFrames = TRUE;
   regEnc->base.needsDeflateCMFHeaders = FALSE;
   regEnc->base.encode = VNCEncodeRegionAmd_Encode;
   regEnc->base.destroy = VNCEncodeRegionAmd_Destroy;
   regEnc->frameNumber = 0;
   regEnc->encodeAsH264 = encodeAsH264;

   regEnc->firstTime = TRUE;
   regEnc->width = Rect_Width(&regEncConfig->region.rect);
   regEnc->height = Rect_Height(&regEncConfig->region.rect);
   regEnc->mbWidth = CEILING(Rect_Width(&regEncConfig->region.rect), MBWIDTH);
   regEnc->mbHeight = CEILING(Rect_Height(&regEncConfig->region.rect), MBWIDTH);
   regEnc->capacityCheckPeriodMs = HZN_CLAMP(config->encCapacityCheckWaitPeriodMs, 0, 60000);
   regEnc->dirtyMask = VNCBitmask_Create(width, height, MBWIDTH);

   /* Dump compressed and/or uncompressed frames if requested. */
   if (config->dumpH264) {
      REGENCWARN("Dumping Amd bitstream.");
      char *fname = Str_Asprintf(NULL, FILE_DUMP_PREFIX "Session-%d-%d-%dx%d_%s.%s",
                                 regEncConfig->encoderId, regEncConfig->screenNum, regEnc->width,
                                 regEnc->height, regEnc->useYUV444 ? "I444" : "I420",
                                 (regEnc->codecType == VNCENCODE_CODEC_TYPE_AV1)    ? "av1"
                                 : (regEnc->codecType == VNCENCODE_CODEC_TYPE_HEVC) ? "h265"
                                                                                    : "h264");
      VNCEncodeRegionOpenFile(&regEnc->fhandleBitstream, fname);
      free(fname);
   }

   if (config->dumpRaw) {
      REGENCWARN("Dumping BGRA uncompressed surface contents.");
      char *fname = Str_Asprintf(NULL, FILE_DUMP_PREFIX "Session-%d-%d-%dx%d_source.bgra",
                                 regEncConfig->encoderId, regEncConfig->screenNum, regEnc->width,
                                 regEnc->height);

      VNCEncodeRegionOpenFile(&regEnc->fhandleRaw, fname);
      free(fname);
   }

   if (!regEnc->encodeAsH264) {
      VNCEncodeStartMP4DebugOutput(regEncConfig->encoderId);
   }

   return &regEnc->base;

fail:
   VNCEncodeRegionAmd_Destroy(&regEnc->base);

   return NULL;
}


/**
 *---------------------------------------------------------------------------
 *
 * IsPhysicalMachineDeployment --
 *
 *    Utility function to determine if deployed on a physical machine.
 *
 *  Results:
 *    TRUE if operation succeeded, FALSE otherwise
 *
 *  Side Effects:
 *    None
 *
 *---------------------------------------------------------------------------
 */

static Bool
IsPhysicalMachineDeployment(const VNCRegEncConfig *config, // IN
                            Bool *isPhysicalMachine)       // OUT
{
   LONG result;
   BOOL isWow64 = FALSE;
   REGSAM regAccess = KEY_READ;
   HKEY vdmKey;
   *isPhysicalMachine = FALSE;

   AMDREGENC_DUMMY_LOCAL(config->encoderId, config->dynamicConfig);

   IsWow64Process(GetCurrentProcess(), &isWow64);
   if (isWow64) {
      regAccess |= KEY_WOW64_64KEY;
   }

   result = Win32U_RegOpenKeyEx(HKEY_LOCAL_MACHINE, DEPLOYMENT_REGKEY, 0, regAccess, &vdmKey);
   if (result != ERROR_SUCCESS) {
      REGENCWARN("Fail to open VDM regkey path to %s (%d).", DEPLOYMENT_REGKEY, result);
      return FALSE;
   }

   DWORD deploymentType = 0;
   result = Win32U_RegQueryDWORDValue(vdmKey, "HorizonDeploymentType", &deploymentType);
   if (ERROR_SUCCESS != result) {
      REGENCWARN("Unable to read HorizonDeploymentType value in path %s (%d).", DEPLOYMENT_REGKEY,
                 result);
      RegCloseKey(vdmKey);
      return FALSE;
   }

   result = RegCloseKey(vdmKey);
   if (ERROR_SUCCESS != result) {
      REGENCWARN("Couldn't close registry (HorizonDeploymentType) %s key: (%d)", DEPLOYMENT_REGKEY,
                 result);
   }

   /*
    * A value of one indicate this is a physical machine.
    */
   if (deploymentType == DEPLOYMENT_TYPE_PHYSICAL_MACHINE) {
      *isPhysicalMachine = TRUE;
   }
   return TRUE;
}


/*
 *-------------------------------------------------------------------
 *
 * GetUserModeDriverVersion --
 *
 *      Get the user mode driver version as shown in device manager
 *      using DX11
 *
 * Results:
 *      TRUE if successful, FALSE otherwise.
 *
 * Side effects:
 *      None
 *
 *-------------------------------------------------------------------
 */

static Bool
GetUserModeDriverVersion(const VNCRegEncConfig *config, // IN
                         ID3D11Device *d3d11Device,     // IN/OUT
                         uint32 *majorVer,              // OUT
                         uint32 *minorVer,              // OUT
                         uint32 *buildNo,               // OUT
                         uint32 *revNo)                 // OUT
{
   IDXGIDevice *dxgiDevice = NULL;
   IDXGIAdapter *dxgiAdapter = NULL;
   DXGI_ADAPTER_DESC adapterDesc;
   HRESULT hr;
   Bool result = FALSE;

   AMDREGENC_DUMMY_LOCAL(config->encoderId, config->dynamicConfig);

   if (FAILED(
          hr = ID3D11Device_QueryInterface(d3d11Device, &IID_IDXGIDevice, (void **)&dxgiDevice))) {
      REGENCWARN("%s: Failed to query ID3D11Device (%08x) (device %p)", __FUNCTION__, hr,
                 d3d11Device);
      goto exit;
   }

   if (FAILED(hr = IDXGIDevice_GetAdapter(dxgiDevice, &dxgiAdapter))) {
      REGENCWARN("%s: Failed to get the IDXGIAdapter (%08x)", __FUNCTION__, hr);
      goto exit;
   }

   if (IDXGIAdapter_GetDesc(dxgiAdapter, &adapterDesc) == S_OK &&
       adapterDesc.VendorId == PCI_VENDOR_ID_AMD) {

      LARGE_INTEGER umd;
      if (FAILED(hr = IDXGIAdapter_CheckInterfaceSupport(dxgiAdapter, &IID_IDXGIDevice, &umd))) {
         REGENCWARN("%s: Could not determine the driver version "
                    "of the AMD adapter (%08x)",
                    __FUNCTION__, hr);
         goto exit;
      }

      ULONGLONG quadPart = umd.QuadPart;
      *majorVer = quadPart >> 48;
      *minorVer = (quadPart >> 32) & 0xFFFF;
      *buildNo = (quadPart >> 16) & 0xFFFF;
      *revNo = quadPart & 0xFFFF;
      REGENCLG0("User mode driver version: %u.%u.%u.%u", *majorVer, *minorVer, *buildNo, *revNo);
      result = TRUE;
   } else {
      REGENCWARN("%s : Failed to query the AMD adapter description");
   }
exit:
   // Release
   if (dxgiAdapter) {
      IDXGIAdapter_Release(dxgiAdapter);
   }
   if (dxgiDevice) {
      IDXGIDevice_Release(dxgiDevice);
   }
   return result;
}


/*
 *-----------------------------------------------------------------------
 *
 * IsDriverVersionCompatible --
 *
 *      Check the AMD Windows driver is greater than equal to the
 *      version 32.0.13031.3015 ( which is equivalent to AMD Adrenalin
 *      version 25.3.1 or AMD internal driver version  ***********)
 *      Ref :AMD version table https://gpuopen.com/version-table/
 *
 * Results:
 *      TRUE if the AMD driver is compatible
 *
 * Side effects:
 *      None.
 *
 *-----------------------------------------------------------------------
 */

static Bool
IsDriverVersionCompatible(const uint32 *majorVer, // IN
                          const uint32 *minorVer, // IN
                          const uint32 *buildNo)  // IN
{
   return (*majorVer > 32 || *majorVer == 32 && *minorVer > 0 ||
           *majorVer == 32 && *minorVer == 0 && *buildNo >= 13031);
}


/*
 *----------------------------------------------------------------------------
 *
 * VNCEncodeRegionAmd_QueryCaps --
 *
 *      Queries the capabilities of the Amd encoder.
 *
 * Results:
 *      None
 *
 * Side effects:
 *      Allocates memory.
 *
 *----------------------------------------------------------------------------
 */

void
VNCEncodeRegionAmd_QueryCaps(const VNCRegEncConfig *config, // IN
                             VNCRegionEncoderAmdCaps *caps) // OUT
{
   ID3D11Device *d3d11Device = NULL;
   Bool amdLibraryLoaded = FALSE;
   AMFFactory *factory = NULL;
   AMFContext *context = NULL;

   Bool isPhysicalMachine = FALSE;
   uint32 majorVer = 0;
   uint32 minorVer = 0;
   uint32 buildNo = 0;
   uint32 revNo = 0;
   AMF_RESULT res = AMF_OK;

   AMDREGENC_DUMMY_LOCAL(config->encoderId, config->dynamicConfig);

   ASSERT(caps != NULL);

   if (sCapsQueried) {
      goto exit;
   }

   d3d11Device = VNCEncodeRegionAmdCreateD3D11Device(regEnc, NULL, NULL, NULL);
   if (!d3d11Device) {
      REGENCLG0("%s: Creating d3d11Device failed", __FUNCTION__);
      goto exit;
   }

   if (!IsPhysicalMachineDeployment(config, &isPhysicalMachine)) {
      REGENCLG0("Could not determine agent deployment type. "
                "AMD hardware encoding is unavailable");
      sCapsQueried = TRUE;
      goto exit;
   }

   /*
    *  AMD physical PC will hit a black screen bug for driver versions
    *  prior to Adrenalin 24.9.1.
    *  REf: https://omnissa.atlassian.net/browse/VCART-2498
    *  Even though black screen was resolved from 24.9.1, another driver
    *  bug was found in 24.9.1 and later versions.
    *  Ref:https://omnissa.atlassian.net/browse/VCART-5788
    *  VCART-5788 is resolved in AMD Adralin 25.3.1
    *  So turn on HW encoding on physical pc only for driver versions
    *  greater than or equal to 25.3.1
    */

   if (isPhysicalMachine &&
       (!GetUserModeDriverVersion(config, d3d11Device, &majorVer, &minorVer, &buildNo, &revNo) ||
        !IsDriverVersionCompatible(&majorVer, &minorVer, &buildNo))) {
      REGENCLG0("The current AMD driver version %d.%d.%d is less than the minimum "
                "required version 32.0.13031. AMD hardware encoding is unavailable",
                majorVer, minorVer, buildNo);
      sCapsQueried = TRUE;
      goto exit;
   }

   if (!VNCEncodeRegionAmdLoadLibrary()) {
      goto exit;
   }
   amdLibraryLoaded = TRUE;

   REGENCLG0("AMF version (header):  %I64X\n", AMF_FULL_VERSION);
   res = amdsdk.AMFInit(AMF_FULL_VERSION, &factory);
   if (res != AMF_OK) {
      REGENC_RLOG(9, "%s: AMF Failed to initialize.", __FUNCTION__);
      goto exit;
   }

   res = factory->pVtbl->CreateContext(factory, &context);
   if (res != AMF_OK) {
      REGENC_RLOG(9, "%s: Failed to create AMF context", __FUNCTION__);
      goto exit;
   }

   res = context->pVtbl->InitDX11(context, d3d11Device, AMF_DX11_0);
   if (res != AMF_OK) {
      REGENCWARN("%s: AMF  DX11 initialization failed.", __FUNCTION__);
      goto exit;
   }

   if (!VNCEncodeRegionAmdQueryEncoderCaps(config, factory, context, AMFVideoEncoderVCE_AVC,
                                           AMF_VIDEO_ENCODER_CAP_NUM_OF_HW_INSTANCES,
                                           AMF_VIDEO_ENCODER_INSTANCE_INDEX, &sCaps.h264MaxWidth,
                                           &sCaps.h264MaxHeight, &sCaps.h264SupportsYUV444)) {
      REGENCWARN("%s: Querying H264 encoder caps failed", __FUNCTION__);
   }
   if (!VNCEncodeRegionAmdQueryEncoderCaps(
          config, factory, context, AMFVideoEncoder_HEVC,
          AMF_VIDEO_ENCODER_HEVC_CAP_NUM_OF_HW_INSTANCES, AMF_VIDEO_ENCODER_HEVC_INSTANCE_INDEX,
          &sCaps.hevcMaxWidth, &sCaps.hevcMaxHeight, &sCaps.hevcSupportsYUV444)) {
      REGENCWARN("%s: Querying HEVC encoder caps failed", __FUNCTION__);
   }
   if (!VNCEncodeRegionAmdQueryEncoderCaps(config, factory, context, AMFVideoEncoder_AV1,
                                           AMF_VIDEO_ENCODER_AV1_CAP_NUM_OF_HW_INSTANCES,
                                           AMF_VIDEO_ENCODER_AV1_ENCODER_INSTANCE_INDEX,
                                           &sCaps.av1MaxWidth, &sCaps.av1MaxHeight,
                                           &sCaps.av1SupportsYUV444)) {
      REGENCWARN("%s: Querying AV1 encoder caps failed", __FUNCTION__);
   }

   REGENCLG0("GPU caps - %s%s%s%s%s%s- "
             "H264 max (%dx%d pixels) HEVC max (%dx%d pixels) AV1 max (%dx%d pixels)",
             sCaps.h264SupportsYUV444 ? "H264-4:4:4 " : "",
             sCaps.hevcSupportsYUV444 ? "HEVC-4:4:4 " : "", sCaps.av1Supported ? "AV1 " : "",
             sCaps.av1SupportsYUV444 ? "-4:4:4 " : "", sCaps.h264Supports10Bit ? "H264-10bit " : "",
             sCaps.hevcSupports10Bit ? "HEVC-10bit " : "", sCaps.h264MaxWidth, sCaps.h264MaxHeight,
             sCaps.hevcMaxWidth, sCaps.hevcMaxHeight, sCaps.av1MaxWidth, sCaps.av1MaxHeight);
   sCapsQueried = TRUE;
   *caps = sCaps;

exit:
   if (context) {
      context->pVtbl->Terminate(context);
      context->pVtbl->Release(context);
      context = NULL;
   }
   if (factory) {
      factory = NULL;
   }
   if (amdLibraryLoaded) {
      VNCEncodeRegionAmdUnloadLibrary();
   }
   if (d3d11Device) {
      ID3D11Device_Release(d3d11Device);
   }
}


#else // defined(_WIN32)

/*
 *----------------------------------------------------------------------------
 *
 * VNCEncodeRegionAmd_Create --
 *
 *      Stub function for non-amd capable platforms. Always fails.
 *
 * Results:
 *      Always fails (returns NULL).
 *
 * Side effects:
 *      None.
 *
 *----------------------------------------------------------------------------
 */

VNCRegionEncoder *
VNCEncodeRegionAmd_Create(const VNCRegEncConfig *regEncConfig, // IN
                          VNCEncodeCodecType codecType,        // IN
                          VNCServerOSCaptureType captureType)  // IN
{
   return NULL;
}


void
VNCEncodeRegionAmd_QueryCaps(const VNCRegEncConfig *regEncConfig, // IN
                             VNCRegionEncoderAmdCaps *caps)       // OUT
{}

#endif
