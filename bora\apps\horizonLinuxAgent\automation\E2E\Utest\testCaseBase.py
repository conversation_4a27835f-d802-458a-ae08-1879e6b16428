# -*- coding: cp1252 -*-
# Copyright (c) Omnissa, LLC. All rights reserved.
# This product is protected by copyright and intellectual property laws in the
# United States and other countries as well as by international treaties.
# -- Omnissa Restricted
import sys,os
import time
import pyautogui
import unittest
import rpyc
import lib.kits
import lib.broker
import lib.agent
import lib.auto
import warnings
import lib.testIds as testIds

from concurrent import futures
from threading import Thread
from datetime import datetime

import lib.audio as audio
kits = lib.kits.kits
cached_values = lib.kits.cached_values
broker = lib.broker.broker
agent = lib.agent.agent
client = lib.client.client
broker_conn = None
DEFAULTDESKTOPSIZE=client.DEFAULTDESKTOPSIZE
client.Update(broker=lib.auto.horizonData['broker'], uag=lib.auto.horizonData['uag'])
remoteclients = lib.client.remoteclients
shadowclients = lib.client.shadowclients

isipv6 = lib.auto.isipv6

import lib.resultListener
resultListener = lib.resultListener.resultListener
if 'true' in lib.auto.horizonData['uagEnabled'].lower():
    import lib.uag
    uag = lib.uag.uag

#BAT_AUO_BLR
features = lib.auto.feature
if lib.auto.isvadc:
   import lib.vadc
   vadc = lib.vadc.vadc

product = lib.auto.product
# This function is to catch the exception in any test case
import functools, traceback
def catch_exception(f):
    @functools.wraps(f)
    def func (*args, **kwargs):
        try:
            return f(*args, **kwargs)
        except AssertionError:
            raise
        except Exception as e:
            print('Exception in', f.__name__)
            print('Exception Error: ', e)
            exceptionMsg = traceback.format_exc()
            print(exceptionMsg)
            kits.Verify(True, False, 'Hit Exception')
            kits.Comment(exceptionMsg)
    return func

# This is to do the task after last test case running, before python exit

import atexit
def exit_handler():
    """
    Actions before python exit
    """
    broker_conn.root.stop_session(broker.brokerPort)
    print('\n' + '#' * 80)
    print('Total Result: ')
    for caseName in kits.raceTrack.testedCases:
        print("%s:\t%s" %(kits.raceTrack.testedCasesResults[caseName], caseName))
        if kits.raceTrack.testedCasesResults[caseName] != 'Pass':
            cached_values['test_result'] = 'FAILED'
    if kits.raceTrack.tsID != None:
        print('Test Set link:', kits.raceTrack.GetTestSetURL())
    print('#' * 80)

    # kits.raceTrack.TestSetEnd()
    # different email format between BAT and workflow
    batrunning = lib.auto.toolData.get('batrunning', 'false')
    print("published in TestRail")
    tr_run = lib.kits.publishOthers()
    if lib.auto.sendEmail == "true":
        if batrunning == 'false' and cached_values['test_result'] == 'PASSED':
            #send Passed test results to the team and failed results to specific
            #todo update email in config file for the team once everything is stable
            To = "<EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>"
            lib.kits.sendresult(tr_run, To=To)
        else:
            print("BAT Failed Linux VDI Team to check")
            To = '<EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>'
            lib.kits.sendresult(tr_run,To=To)


atexit.register(exit_handler)

class TestCaseBase(unittest.TestCase):
    def __init__(self, *args, **kwargs):
        super(TestCaseBase, self).__init__(*args, **kwargs)
        self.Name = None
        self.Feature = None
        self.Description = None
        self.TCMSID = None
        self.updateChrome = "true"
        if kits.IsTestSetStarted == False:
            lib.kits.updateCacheBuild()
            # cached_values['racetrack_id'] = kits.raceTrack.TestSetBegin()
            cached_values['case_ids'] = []
            cached_values['results'] = []
            kits.IsTestSetStarted = True
        self.Scenarios = []

    @classmethod
    def setUpClass(cls):
        global broker_conn
        broker_conn = rpyc.connect(lib.auto.horizonData['broker'], 18812)
        broker.brokerPort = broker_conn.root.start_session()

    def setUp(self):
        warnings.simplefilter('ignore', ResourceWarning)
        unittest.TestCase.setUp(self)
        self.Feature = lib.auto.FeatureDescription
        if self.Feature == None:
            self.Feature = self.__class__.__name__
        if self.Name == None:
            self.Name = self._testMethodName
        if self.Description == None:
            self.Description = ""
        if self.TCMSID == None:
            self.TCMSID = ""
        print('\n' + '*' * 80)
        print('Start Test Case: %s -> %s' %(self.__class__.__name__, self._testMethodName))
        kits.raceTrack.TestCaseBegin(self.Name, self.Feature, self.Description, self.TCMSID)
        resultListener.AddTestCase(self._testMethodName, self.Name)
        print(f"######################## {self.Description} ########################")
        if not 'Feature' in cached_values:
           cached_values['Feature'] = self.Feature

    def tearDown(self, cleanAgentLog=False):
        kits.Comment("Tear Down Started:shutdown client!")
        client.KillClient()
        time.sleep(2)
        pktcapture = lib.auto.toolData.get('pktcapture', 'false')
        if pktcapture == 'true':
            client.StopPktCapture()
        # client.ResetMonitor(tool='vmtool')
        time.sleep(2)
        if 'linux' in sys.platform:
            client.KillFileExplorer()
        elif 'win32' in sys.platform:
            client.KillChomeBrowser()
        elif 'darwin' in sys.platform:
            client.KillSafariBrowser()

        if not 'agentIP' in cached_values:
           waitTime = 90
           if lib.auto.isphysical:
               agent.agentIP = lib.auto.agentData['agentIP']
           else:
               agentVM = None
               while waitTime > 0:
                   if self.poolInfo.get('VADC', False):
                       print('VADC agent')
                       agentVM = self.poolInfo['baseVM']
                       ret, agent.agentIP = vadc.GetIpAddress(agentVM)
                       if ret:
                           break
                   else:
                       print('None VADC agent')
                       agentVM = broker.GetAgentVMName(self.poolInfo['pool'])
                       if agentVM == "Done":
                           kits.Warning(f"Failed to get VM Name of {self.poolInfo['pool']}")
                           time.sleep(10)
                           waitTime -= 10
                           continue
                       ret, agent.agentIP = broker.VI_GetGuestIP(agentVM)
                       if ret:
                           break
        #only check in normal case
        if 'agentIP' in cached_values:
           try:
               if 'agentTS' in cached_values:
                   agent.CheckPanicWithinTimeFrame(ts=cached_values['agentTS'])
               else:
                   agent.CheckPanicOnAgent()
               alog = agent.GetLogFromAgentDirectly(agentIP=agent.agentIP)
               logInMB = os.path.getsize(alog) >> 20
               if logInMB > 1:
                   kits.Comment(f'{alog} is {logInMB} MB, too large to be uploaded to racetrack !')
                   os.remove(alog)
               else:
                   kits.Log("agent log bundle", alog, delete=True)
               agent.VerifyDump()
               if cleanAgentLog:
                   agent.CleanTmpLogs()
               agent.KillFileExplorer()
           except:
               pass

           if product == 'horizonlinuxagent_codecov':
               agent.TerminateProcOnAgent()
               if self._testMethodName != 'test_01':
                   agent.CheckCCData()

        # client.VerifyDump(cached_values.get('clientTS'))
        time.sleep(2)
        client.ClearLogs()
        kits.Comment('End Test Case: %s -> %s' %(self.__class__.__name__, self._testMethodName))
        unittest.TestCase.tearDown(self)
        time.sleep(1)
        kits.raceTrack.TestCaseEnd()
        resultListener.UpdateTestCaseResult(kits.raceTrack.testedCasesResults[self.Name])
        #update test result
        if not cached_values.get('agentBuildNum', None):
           lib.kits.updateCacheBuild()
        print("Features are"+str(list(features.keys())))
        lib.kits.updateCacheResult(self.TCMSID, self.Name)
        print('End Test Case: %s -> %s' %(self.__class__.__name__, self._testMethodName))

################################################################################

    def ConvertVmToTemplate(self, vmName):
        """
        Shutdown the VM and convert it to template
        """
        broker.VI_ShutdownVM(vmName)
        broker.VI_WaitUntillPoweredOff(vmName)
        broker.VI_ConvertVmToTemplate(vmName)

    def GetCCBuildArg(self, arg):
        buildnum = os.environ.get('buildnumber', None)
        if product == 'horizonlinuxagent_codecov':
            arg += ' --product horizonlinuxagent_codecov'
        if buildnum:
            if int(buildnum) > 50000000:
                arg += f' -s yes -n {buildnum}'
            else:
                arg += f' -n {buildnum}'
        print('--------------- installation arguments ---------------')
        print(arg)
        return arg

    def InstallVADCOnSnapshot(self, arg, vmName, ssFresh):

        arg = self.GetCCBuildArg(arg)
        print('Start VADC installation...')
        vadc.SnapshotOps(vmName, ssFresh, 'revert')
        kits.Comment('Revert snapshot...')
        # try to be safe here
        time.sleep(10)
        vadc.PowerOps(vmName, ops='poweron')
        print(f'power on {vmName}...')
        ret, agent.agentIP = vadc.WaitIpAddress(vmName, timeout=300)
        if ret:
            print("Agent IPAddress: {}".format(agent.agentIP))
            client.ClearSSHKey(agentIP=agent.agentIP)
            ok = agent.InstallBuild(arg, vadc=True)
            kits.Verify(ok, True, 'Linux Agent VADC installation Passed')
            if ok:
                return True

        unittest.TestCase.assertTrue(False, "Linux Agent VADC installation Failed!")
        return False

    def InstallAgentOnSnapshot(self, arg, vmName, ssFresh, agentDownload=True):
        """
        Install the Agent build on specified VM's snapshot, and create a new snapshot
        @Input:
          -vmName: VM name
          -ssFresh: the snapshot name where to install Agent build
          -arg: installer parameters with string text, such as:
                ' -B view-linuxagent-18fq4 -F yes -U yes'
        """
        arg = self.GetCCBuildArg(arg)
        if lib.auto.isSandbox:
            arg += ' -s yes'
        if lib.auto.buildNum:
            arg += f' -n {lib.auto.buildNum}'
        if isipv6:
            arg += ' --ipv6 yes'
        broker.VI_PowerOffVM(vmName)
        broker.VI_WaitUntillPoweredOff(vmName)
        # try to be safe here
        time.sleep(10)
        broker.VI_ReverToSnapshot(vmName, ssFresh)
        if not broker.VI_PowerOnVM(vmName):
            unittest.TestCase.assertTrue(False, f"Failed to Power On {vmName}")
        broker.VI_WaitUntillToolsRunning(vmName)
        time.sleep(10)    #BAT_AUTO_BLR  increase timeout and find out
        ret, agent.agentIP = broker.VI_GetGuestIP(vmName, cache=False)
        retries = 10
        while not agent.agentIP and retries != 0:
            print("Waiting for IP address")
            time.sleep(5)
            ret, agent.agentIP = broker.VI_GetGuestIP(vmName, cache=False)
            retries -= 1
        if ret:
            kits.Comment("Agent IPAddress: {}".format(agent.agentIP))
            client.ClearSSHKey(agentIP=agent.agentIP)
            ok = agent.InstallBuild(arg, isAgentDownload=agentDownload)
            kits.Verify(ok, True, 'Linux Agent installation Passed')
            self.assertEqual(ok, True, msg='Linux Agent installation Passed')
            if ok:
                return True
        unittest.TestCase.assertTrue(False, "Linux Agent installation Failed!")
        return False

    def UpdateAgent(self, arg, vmName):
        """
        Install the Agent build on specified VM's snapshot, and create a new snapshot
        @Input:
          -vmName: VM name
          -arg: installer parameters with string text, such as:
                ' -B main -U yes'
        """
        arg = self.GetCCBuildArg(arg)
        ret = True
        if lib.auto.isSandbox:
            arg += ' -s yes'
        if lib.auto.buildNum:
            arg += f' -n {lib.auto.buildNum}'
        if lib.auto.isipv6:
            arg += ' --ipv6 yes'
        if lib.auto.isphysical:
            agent.agentIP = lib.auto.agentData['agentIP']
        else:
            broker.VI_WaitUntillToolsRunning(vmName)
            ret, agent.agentIP = broker.VI_GetGuestIP(vmName, cache=True)
        if ret:
            kits.Comment("Agent IPAddress: {}".format(agent.agentIP))
            client.ClearSSHKey(agentIP=agent.agentIP)
            agent.StopViewAgentService()
            agent.UninstallViewagent()
            agent.CleanDCTLogs()
            agent.StopGDM()
            ok = agent.InstallBuild(arg)
            if ok:
                return True
        unittest.TestCase.assertTrue(False, "Agent installation Failed!")
        return False

    def NewSnapshotOnAgentVM(self, vmName, ssReady):
        """
        Install the Agent build on specified VM's snapshot, and create a new snapshot
        @Input:
          -vmName: VM name
          -ssReday: the snapshot name to create
        """
        broker.VI_ShutdownVM(vmName)
        broker.VI_WaitUntillPoweredOff(vmName)
        broker.VI_RemoveSnapshot(vmName, ssReady)
        broker.VI_NewSnapshot(vmName, ssReady)

    def RebootVADC(self, vmName, cold=False):
        if cold:
            vadc.PowerOps(vmName, ops='poweroff')
            time.sleep(10)
            vadc.PowerOps(vmName, ops='poweron')
            time.sleep(5)
        else:
            vadc.PowerOps(vmName, 'reboot')
            time.sleep(20)

    def RestartVM(self, vmName, cold=False):
        """
        shutdown VM and then restart VM
        @Input:
          -vmName: VM name
        """
        if cold:
            broker.VI_ShutdownVM(vmName)
            broker.VI_WaitUntillPoweredOff(vmName)
            time.sleep(20)
            broker.VI_PowerOnVM(vmName)
        else:
            if lib.auto.isphysical:
                agent.Reboot()
                time.sleep(30)
            else:
                broker.VI_RebootVM(vmName)
                time.sleep(10)
                broker.VI_WaitUntillToolsRunning(vmName)

    def PrepareParameters(self, poolInfo):
        baseVM = poolInfo['baseVM']
        if poolInfo['VmFolder'] == None:
            poolInfo['VmFolder'] = broker.VI_GetVmFolderPath(poolInfo['baseVM'])
            poolInfo['templatePath'] = '%s/%s' %(poolInfo['VmFolder'], baseVM)
        if poolInfo['HostOrCluster'] == None:
            poolInfo['HostOrCluster'] = broker.VI_GetVmClusterPath(baseVM)
        if poolInfo['ResourcePool'] == None:
            poolInfo['ResourcePool'] = broker.VI_GetVmResourcePoolPath(baseVM)
        if poolInfo['datastore'] == None:
            poolInfo['datastore'] = broker.VI_GetVmDatastore(baseVM)
            poolInfo['datastorePath'] = '%s/%s' %(poolInfo['HostOrCluster'], poolInfo['datastore'])

    def New_ICPool(self, poolInfo, OfflineJoinDomain='sssd', ssoDesktopType='UseGnomeClassic', agentDownload=True,
                   rejoin=False, enableCollab=True, vhciRebuild=False, adpasswd='VMware1!'):
        baseVM = poolInfo['baseVM']
        pool   = poolInfo['pool']
        broker.HV_RemovePool("-TerminateSession ", "-DeleteFromDisk", PoolName=poolInfo['pool'])
        self.InstallAgentOnSnapshot(poolInfo['installArg'], baseVM, poolInfo['ssFresh'], agentDownload=agentDownload)
        agent.UploadTestScript()
        agent.SetAgentCustomConfig('SSODesktopType', ssoDesktopType)
        agent.SetAgentCustomConfig('NetbiosDomain', poolInfo['domain'].upper())
        agent.SetAgentConfig('Clipboard.Direction', 1)
        if 'usbAutoDetect' in lib.auto.clientData:
            agent.SetAgentConfig('viewusb.AllowKeyboardMouse', 'o:true')
        agent.EnableUdiskPolicy()
        # subnet option
        agentIP = agent.agentIP
        if ':' in agentIP:
            agent.SetAgentCustomConfig('Subnet6', agentIP+'/64')
        else:
            agent.SetAgentCustomConfig('Subnet', agentIP+'/16')

        if rejoin:
            agent.RejoinManually(adType=rejoin, adpasswd=adpasswd)
        if OfflineJoinDomain:
            if OfflineJoinDomain == 'pbis':
                agent.SetAgentCustomConfig('OfflineJoinDomain', 'pbis')
            elif OfflineJoinDomain == 'samba':
                agent.SetAgentCustomConfig('OfflineJoinDomain', 'samba')
            else:
                pass
        else:
            agent.SetAgentCustomConfig('OfflineJoinDomain', 'none')
        agent.SetAgentCustomConfig('RunOnceScript', '/root/tScript.sh')
        agent.DelDuplicateLine(cfgFile='/etc/omnissa/viewagent-custom.conf')
        self.NewSnapshotOnAgentVM(baseVM, poolInfo['ssReady'])
        self.PrepareParameters(poolInfo)
        tstart = time.time()
        broker.HV_NewPool("-InstantClone",
                          PoolName = pool,
                          UserAssignment = "FLOATING",
                          ParentVM = baseVM,
                          SnapshotVM = poolInfo['ssReady'],
                          VmFolder = poolInfo['VmFolder'],
                          HostOrCluster = poolInfo['HostOrCluster'],
                          ResourcePool = poolInfo['ResourcePool'],
                          Datastores = poolInfo['datastore'],
                          NamingMethod = "PATTERN",
                          NamingPattern = poolInfo['NamingPattern'],
                          MaximumCount = "1",
                          NetBiosName = poolInfo['NetBiosName'],
                          PostSynchronizationScriptName = '/var/userScript/tScript.sh',
                          Vcenter = lib.auto.vcenterData['address'])
        broker.HV_WaitDesktopState(pool, "AVAILABLE", 600)
        tconsumed = time.time() - tstart
        t = tconsumed < 600
        kits.Verify(t, True, f'Instant Clone time consumed: {tconsumed:.3f} seconds, less than 600 seconds')
        # safegard here, the agent IP will change sometimes bug 2447474
        broker.VI_PoolingGuestIP(pool)
        if vhciRebuild:
            agent.ReBuildVHCI()
        self.Verify_IC_FC(pool)
        broker.HV_NewEntitlement(pool=pool,
                                 user=poolInfo['user'],
                                 domain=poolInfo['domain'])
        broker.HV_SetPool("{'desktopSettings.displayProtocolSettings.defaultDisplayProtocol': 'BLAST'}", pool)
        if enableCollab:
            broker.HV_EnableCollab(pool=pool)
        broker.HV_EnableHtml(poolInfo['pool'])
        time.sleep(2)

    def New_FCPool(self, poolInfo, ssoDesktopType='UseGnomeClassic', reJoin=False, enableCollab=True, agentDownload=True):
        baseVM = poolInfo['baseVM']
        pool   = poolInfo['pool']
        broker.VI_ConvertTemplateToVm(baseVM)
        broker.HV_RemovePool("-TerminateSession ", "-DeleteFromDisk", PoolName=pool)
        self.PrepareParameters(poolInfo)
        self.InstallAgentOnSnapshot(poolInfo['installArg'], baseVM, poolInfo['ssFresh'], agentDownload=agentDownload)
        agent.UploadTestScript()
        if reJoin:
            agent.RejoinWithRunOnce()
        else:
            agent.SetAgentCustomConfig('RunOnceScript', '/root/tScript.sh')
        # subnet option
        agent.SetAgentCustomConfig('Subnet', '**********/16')
        agent.SetAgentCustomConfig('SSODesktopType', ssoDesktopType)
        agent.SetAgentCustomConfig('NetbiosDomain', poolInfo['domain'].upper())
        agent.SetAgentConfig('Clipboard.Direction', 1)
        if 'usbAutoDetect' in lib.auto.clientData:
            agent.SetAgentConfig('viewusb.AllowKeyboardMouse', 'o:true')
        agent.SetAgentConfig('viewusb.AllowKeyboardMouse', 'o:true')
        agent.EnableUdiskPolicy()
        # subnet option
        agentIP = agent.agentIP
        if ':' in agentIP:
            agent.SetAgentCustomConfig('Subnet6', agentIP+'/64')
        else:
            agent.SetAgentCustomConfig('Subnet', agentIP+'/16')

        agent.DelDuplicateLine(cfgFile='/etc/omnissa/viewagent-custom.conf')
        self.ConvertVmToTemplate(baseVM)
        tstart = time.time()
        broker.HV_NewPool("-FullClone",
                          PoolName = pool,
                          UserAssignment = "FLOATING",
                          Template = baseVM,
                          VmFolder = poolInfo['VmFolder'],
                          HostOrCluster = poolInfo['HostOrCluster'],
                          ResourcePool = poolInfo['ResourcePool'],
                          Datastores = poolInfo['datastore'],
                          NamingMethod = "PATTERN",
                          NamingPattern = poolInfo['NamingPattern'],
                          MaximumCount = "1",
                          NetBiosName = poolInfo['NetBiosName'],
                          SysPrepName = poolInfo['CustomeSpecName'],
                          CustType = 'SYS_PREP',
                          Vcenter = lib.auto.vcenterData['address'])

        broker.HV_WaitDesktopState(pool, "AVAILABLE", 600)
        tconsumed = time.time() - tstart
        t = tconsumed < 600
        kits.Verify(t, True, f'Full Clone time consumed: {tconsumed:.3f} seconds, less than 600 seconds')
        # safegard here
        broker.VI_PoolingGuestIP(pool)
        okNum = 1
        if reJoin:
            okNum = 0
        self.Verify_IC_FC(pool, okNum=okNum)
        broker.VI_ConvertTemplateToVm(baseVM)
        broker.HV_NewEntitlement(pool=pool,
                                 user=poolInfo['user'],
                                 domain=poolInfo['domain'])
        broker.HV_SetPool("{'desktopSettings.displayProtocolSettings.defaultDisplayProtocol': 'BLAST'}", pool)
        if enableCollab:
            broker.HV_EnableCollab(pool=pool)
        broker.HV_EnableHtml(poolInfo['pool'])
        time.sleep(2)

    def New_VADCPool(self, poolInfo, ssoDesktopType='UseGnomeClassic', rejoin=False,
                     netBios=False, enableCollab=False, vhciRebuild=False):
        baseVM = poolInfo['baseVM']
        pool   = self.poolInfo['pool']

        installArg = lib.auto.GetInstallArg()
        self.Scenarios.extend(['vadcinstall'])
        testIds.assignTestCaseIds(features,self.Scenarios)
        print('----build id--------')
        # print(lib.auto.GetLatestBuildNum())
        kits.Comment('########################- Create VADC Pool -################################')
        ret = self.InstallVADCOnSnapshot(poolInfo['installArg'], baseVM, poolInfo['ssFresh'])
        if not ret:
            self.assertTrue(False, f'vadc: {baseVM} installation failed!')
            return False
        agent.SetAgentCustomConfig('SSODesktopType', ssoDesktopType)
        if netBios:
            agent.SetAgentCustomConfig('NetbiosDomain', poolInfo['domain'].upper())
        agent.DelDuplicateLine(cfgFile='/etc/omnissa/viewagent-custom.conf')
        agent.SetAgentConfig('Clipboard.Direction', 1)
        if 'usbAutoDetect' in lib.auto.clientData:
            agent.SetAgentConfig('viewusb.AllowKeyboardMouse', 'o:true')
        # setup agent debug level
        if os.environ.get('ADEBUG', False):
            agent.EnableVChanTrace()

        agent.EnableUdiskPolicy()
        if rejoin:
            agent.RejoinManually(adType=rejoin)
        else:
            pass
        agent.ReSyncNtp()
        #BAT_AUDO_BLR Commented the following code to figure out how to enable collab and vhci
        if enableCollab:
           agent.UploadCollabScript()
           agent.insertLDPath('/usr/lib/vmware/viewagent/VMwareBlastServer/')
        if vhciRebuild:
           agent.ReBuildVHCI()
        if self.poolInfo.get('multisession', False):
            # entitle domain groups
            agent.EntitleVADCDomainUsers()
            agent.EntitleVADCApps()
        else:
            # entitle vadc user
            agent.AddUserToVADCGroup(user=poolInfo['user'])
        # enable auto tool: create "adminEnabled" file under /usr/lib/omnissa/viewagent/vadc
        agent.EnableVADCAdmin()

        vadc.PowerOps(baseVM, ops='reboot')
        #kits.Comment(f'reboot {baseVM}...')
        ret, agent.agentIP = vadc.WaitIpAddress(baseVM, timeout=300)
        kits.Verify(ret, True, 'Get VADC IP address within 300 seconds')
        if ret:
            # set vadc host ip address
            vadc.UpdateHost(agent.agentIP)
            # set vadc launch broker ip address
            if self.poolInfo.get('multisession', False):
                for idx in remoteclients:
                    idx.broker = agent.agentIP
            else:
                client.broker = agent.agentIP

            waitTime = 120
            while waitTime > 0:
                state, _ = vadc.GetSessionInfo()
                if not state:
                    waitTime -= 10
                    continue

                if state == 'AVAILABLE':
                    print(f"VADC: {pool} became AVAILABLE")
                    # kits.Verify(ret, True, f"VADC: {pool} become 'AVAILABLE'")
                    return True
                else:
                    kits.Warning("vadc {} is in {} state".format(pool, state))
                    time.sleep(5)
                    waitTime -= 5
                    continue
            else:
                comment = f"vadc: {pool} failed to become 'AVAILABLE'"
                print(comment)
                self.assertTrue(False, comment)
                return False
        self.assertTrue(False, f'vadc: {baseVM} failed to get IP address')

    def New_ManualPool(self, poolInfo, ssoDesktopType='UseGnomeClassic', rejoin=False, addomain='ipv6.lxd', adpasswd='VMware1!',
                       netBios=False, enableCollab=True, isPending=False, vhciRebuild=False, agentDownload=True):
        baseVM = poolInfo['baseVM']
        pool   = poolInfo['pool']
        self.Scenarios.extend(['installation'])
        testIds.assignTestCaseIds(features,self.Scenarios)
        broker.HV_RemovePool("-TerminateSession ", PoolName=pool)
        self.InstallAgentOnSnapshot(poolInfo['installArg'], baseVM, poolInfo['ssFresh'], agentDownload=agentDownload)
        agent.SetAgentCustomConfig('SSODesktopType', ssoDesktopType)
        if netBios:
            agent.SetAgentCustomConfig('NetbiosDomain', poolInfo['domain'].upper())
        agent.DelDuplicateLine(cfgFile='/etc/omnissa/viewagent-custom.conf')
        if isPending:
            agent.InsertOption('/etc/omnissa/viewagent-custom.conf', 'PendingSessionTimeout', 5)
        agent.SetAgentConfig('Clipboard.Direction', 1)
        if 'usbAutoDetect' in lib.auto.clientData:
            agent.SetAgentConfig('viewusb.AllowKeyboardMouse', 'o:true')
        agent.EnableUdiskPolicy()
        # setup agent debug level
        if os.environ.get('ADEBUG', False):
            agent.EnableVChanTrace()

        # subnet option
        agentIP = agent.agentIP
        if ':' in agentIP:
            agent.SetAgentCustomConfig('Subnet6', agentIP+'/64')
        else:
            agent.SetAgentCustomConfig('Subnet', agentIP+'/16')

        if rejoin:
            agent.RejoinManually(adType=rejoin, addomain=addomain, adpasswd=adpasswd)
        else:
            pass
        agent.ReSyncNtp()
        self.RestartVM(baseVM)
        broker.HV_NewPool("-Manual",
                           PoolName=pool,
                           UserAssignment='FLOATING',
                           Source='VIRTUAL_CENTER',
                           VM=baseVM,
                           Vcenter=lib.auto.vcenterData['address'])
        time.sleep(10)
        status = broker.HV_WaitDesktopState(pool, "AVAILABLE", 800)
        broker.HV_SetPool("{'desktopSettings.displayProtocolSettings.defaultDisplayProtocol': 'BLAST'}", pool)
        #broker.HV_SetPool("{'desktopSettings.displayProtocolSettings.pcoipDisplaySettings.renderer3D': 'MANAGE_BY_VSPHERE_CLIENT'}", pool)
        broker.HV_NewEntitlement(pool=pool,
                                 user=poolInfo['user'],
                                 domain=poolInfo['domain'])
        #if enableCollab:
        #    broker.HV_EnableCollab(pool=pool)
        #    agent.UploadCollabScript()
        #    agent.insertLDPath('/usr/lib/vmware/viewagent/VMwareBlastServer/')
        #if vhciRebuild:
        #    agent.ReBuildVHCI()
        broker.HV_EnableHtml(poolInfo['pool'])
        time.sleep(2)
    
    def New_RDSPool(self, poolInfo, ssoDesktopType='UseGnomeClassic', rejoin=False, addomain='ipv6.lxd', adpasswd='VMware1!',
                       netBios=False, enableCollab=True, isPending=False, vhciRebuild=False, agentDownload=True):
        baseVM = poolInfo['baseVM']
        pool   = poolInfo['pool']
        farm   = poolInfo['farm']
        self.Scenarios.extend(['rdsinstall'])
        testIds.assignTestCaseIds(features,self.Scenarios)
        broker.HV_RemovePool("-TerminateSession", PoolName=pool)
        broker.HV_RemoveFarm(FarmName=farm)
        ret, agent.agentIP = broker.VI_GetGuestIP(baseVM, cache=False)
        cmd = 'hostname'
        hostname = agent.GetOutputFromAgentDirectly(cmd=cmd, agentIP=agent.agentIP, rootUser=True)
        broker.HV_RemoveRDSHost(hostname.strip())
        print("wait for 30 secs until RDS Host is deleted")
        time.sleep(30)
        self.InstallAgentOnSnapshot(poolInfo['installArg'], baseVM, poolInfo['ssFresh'], agentDownload=agentDownload)
        agent.SetAgentCustomConfig('SSODesktopType', ssoDesktopType)
        
        if netBios:
            agent.SetAgentCustomConfig('NetbiosDomain', poolInfo['domain'].upper())
        agent.DelDuplicateLine(cfgFile='/etc/omnissa/viewagent-custom.conf')
        if isPending:
            agent.InsertOption('/etc/omnissa/viewagent-custom.conf', 'PendingSessionTimeout', 5)
        agent.SetAgentConfig('Clipboard.Direction', 1)
        if 'usbAutoDetect' in lib.auto.clientData:
            agent.SetAgentConfig('viewusb.AllowKeyboardMouse', 'o:true')
        agent.EnableUdiskPolicy()
        # setup agent debug level
        if os.environ.get('ADEBUG', False):
            agent.EnableVChanTrace()

        # subnet option
        agentIP = agent.agentIP
        if ':' in agentIP:
            agent.SetAgentCustomConfig('Subnet6', agentIP+'/64')
        else:
            agent.SetAgentCustomConfig('Subnet', agentIP+'/16')

        if rejoin:
            agent.RejoinManually(adType=rejoin, addomain=addomain, adpasswd=adpasswd)
        else:
            pass
        agent.ReSyncNtp()
        self.RestartVM(baseVM)
        broker.HV_NewFarm("-Manual",
                          FarmName=farm,
                          FarmDisplayName=farm,
                          DefaultDisplayProtocol='BLAST',
                          EnableCollaboration=True,
                          RdsServers=hostname.strip())
        
        broker.HV_NewPool("-Rds",
                           PoolName=pool,
                           PoolDisplayName=pool,
                           Farm=farm)
        time.sleep(10)
        broker.HV_WaitRDSDesktopState(farm, "AVAILABLE", 300)
        broker.HV_NewEntitlement(pool=pool,
                                 user=poolInfo['user'],
                                 domain=poolInfo['domain'])
        broker.HV_EnableHtml(poolInfo['pool'])
        print("wait for RDS Host to register on Broker for 30 secs")
        time.sleep(30)

    def Update_ManualPool(self, poolInfo, ssoDesktopType='UseGnomeClassic', rejoin=False,
                          netBios=False, enableHid=True, onSnapshot=True):
        baseVM = poolInfo['baseVM']
        pool   = poolInfo['pool']
        poolType = broker.HV_GetPoolType(pool=pool)
        print(f"poolType : {poolType}")
        state = broker.HV_GetDesktopState(pool)
        if state in ('CONNECTED', 'DISCONNECTED'):
            broker.Disconnect_Logoff(pool=pool, action='logoff')
            time.sleep(10)
        if onSnapshot:
            self.InstallAgentOnSnapshot(poolInfo['installArg'], baseVM, poolInfo['ssFresh'])
            agent.SetAgentCustomConfig('SSODesktopType', ssoDesktopType)
            if netBios:
                agent.SetAgentCustomConfig('NetbiosDomain', poolInfo['domain'].upper())
            agent.DelDuplicateLine(cfgFile='/etc/omnissa/viewagent-custom.conf')
            agent.SetAgentConfig('Clipboard.Direction', 1)
            if enableHid:
                agent.SetAgentConfig('viewusb.AllowKeyboardMouse', 'o:true')
            if rejoin:
                agent.RejoinManually(adType=rejoin)
            else:
                pass
            agent.EnableUdiskPolicy()
        else:
            self.UpdateAgent(poolInfo['installArg'], baseVM)
        self.RestartVM(baseVM)
        agent.ReSyncNtp()
        self.RestartVM(baseVM)
        broker.HV_WaitDesktopState(pool, "AVAILABLE", 180)

    def Update_UnManagedFarm(self, poolInfo, rejoin=False, onSnapshot=True):
        baseVM = poolInfo['baseVM']
        pool   = poolInfo['pool']
        if onSnapshot:
            self.InstallAgentOnSnapshot(poolInfo['installArg'], baseVM, poolInfo['ssFresh'])
        else:
            status = broker.VI_GetVMPowerState(baseVM)
            if status == 'PoweredOff':
                broker.VI_PowerOnVM(baseVM)
                time.sleep(10)
            broker.VI_WaitUntillToolsRunning(baseVM)
        self.poolType = broker.HV_GetPoolType(pool=pool)
        print(f"poolType : {self.poolType}")
        self.UpdateAgent(poolInfo['installArg'], baseVM)
        agent.SetAgentConfig('Clipboard.Direction', 1)
        if rejoin:
            agent.RejoinManually(adType=rejoin)
        else:
            pass
        agent.ReSyncNtp()
        self.RestartVM(baseVM)
        find = False
        times = 90
        rdsInfo = ''
        while times > 0:
            rdsInfo = broker.HV_QueryRdsServer()
            print('********** rdsInfo ***********')
            print(f'{rdsInfo}')
            if isinstance(rdsInfo, dict):
                if poolInfo['Dns'] in rdsInfo['DnsName'] :
                    if rdsInfo['Status'] == 'AVAILABLE':
                        find = True
            elif isinstance(rdsInfo, list):
                for rds in rdsInfo:
                    print('********** rds info ***********')
                    print(rds)
                    if poolInfo['Dns'] in rds['DnsName'] :
                        if rds['Status'] == 'AVAILABLE':
                            find = True
                            break
            else:
                print('********** unexpected rds info ***********')
                print(type(rdsInfo))

            time.sleep(1)
            if find:
                kits.Verify(True, True, f"RDS Server {poolInfo['Dns']} become 'AVAILABLE' within 90 seconds")
                break
            else:
                print(f"Failed to become 'AVAILABLE' in {90-times} seconds")
            time.sleep(15)
            times -= 15
        else:
            print(rdsInfo)
            kits.Verify(False, True, f"RDS Server {rds['DnsName']} Failed to become 'AVAILABLE' in 90 Seconds")

    def Verify_IC_FC(self, pool, okNum=2, times=60):
        """
        Verify the Instant/Full Cloned pool
        """
        agentVM = ''
        while times > 0:
            agentVM = broker.GetAgentVMName(pool)
            print(f'agentVM : {agentVM}')
            if not 'Done' in agentVM:
                break
            else:
                kits.Wait(10)
                times -= 10
        else:
            # Instant/Full clone pool failed to get agentVM name sometimes;
            return False
        ret, agent.agentIP = broker.VI_GetGuestIP(agentVM, cache=False)
        if ret:
            kits.Comment("Agent IPAddress: {}".format(agent.agentIP))
            client.ClearSSHKey(agentIP=agent.agentIP)
            if okNum > 0:
                agent.VerifyTscriptResult(okNum)
            agentHostName = agent.GetHostName().replace('\n','')
            kits.Verify(agentHostName, agentVM, 'Agent hostname checking')
        else:
            return False

    def CheckAutofit(self, checkCollabIcon=True, sizeList=['1024x768','1366x768','1440x900']):
        poolInfo = self.poolInfo
        pool = poolInfo['pool']
        client.ActivateDesktop(pool)
        for x in sizeList:
            client.ResizeDesktop(pool, x)
            if x == 'all':
                width, height = pyautogui.size()
                x = f'{width}x{height}'
            times = 60
            while times > 0:
                client.ClickCenter()
                time.sleep(10)
                times -= 10
                client.ClickCenter()
                agentMonInfos = agent.GetResInAgent(agent.agentIP)
                kits.Comment(agentMonInfos)
                # only check the first monitor here
                agentRes = agentMonInfos[0][1]
                print("Agent resolution: {}".format(agentRes))
                print("Client resolution: {}".format(x))
                result = agentRes == x
                if not result:
                    continue

                kits.Verify(result, True, 'Client and Agent has the same screen resolution')
                kits.Comment("Agent resolution: {}".format(agentRes))
                kits.Comment("Client resolution: {}".format(x))
                if result:
                    guestOS = cached_values['agentDist']
                    #session collaboration is not supported in SLES/SLED
                    if checkCollabIcon and not ("SLED" in guestOS or "SLES" in guestOS):
                        self.CollabIconCheck()
                    ret = client.IsRemoteDesktopShownMss(agent.agentIP, timeCput=False)
                    kits.Verify(ret, True, 'Client and Agent has the same screen resolution', screenshot=ret!=True)
                    break
                else:
                    client.UploadRMKSLog(cached_values.get('clientTS'))
            else:
                kits.Comment("Agent resolution: {}".format(agentRes))
                kits.Comment("Client resolution: {}".format(x))
                kits.Verify(False, True, 'Client and Agent has the same screen resolution', screenshot=True)
                client.UploadRMKSLog(cached_values.get('clientTS'))

    def CheckAutofitWeb(self):
        client.MaxmizeBrowser()
        times = 60
        width, height = pyautogui.size()
        clientRes = "{}x{}".format(width, height)
        agentRes = '799x599'
        while times > 0:
            client.ClickCenter()
            time.sleep(10)
            times -= 10
            client.ClickCenter()
            client.RefreshWithF5()
            agentMonInfos = agent.GetResInAgent(agent.agentIP)
            print(f'agent resolution: {agentMonInfos}')
            if not isinstance(agentMonInfos, list):
                continue
            agentRes = agentMonInfos[0][1]
            result = True
            if 'win32' in sys.platform:
                result = agentRes.split('x')[0] == str(width)
            elif 'darwin' in sys.platform:
                result = int(agentRes.split('x')[0]) == int(int(width) - 4)
            if not result:
                continue
            kits.Verify(result, True, 'Web Client and Agent has the same screen resolution', screenshot=True)
            break
        else:
            print("Agent resolution: {}".format(agentRes))
            print("Client resolution: {}".format(clientRes))
            kits.Verify(False, True, 'Client and Agent has the same screen resolution', screenshot=True)

    def CheckClipboard(self, direction=1, clientText=None):
        '''
        note: this function depends on pyperclip module
        please googling for more info
        '''
        if not clientText:
            clientText = 'FromClient\n\n' * 50
        poolInfo = self.poolInfo
        pool = poolInfo['pool']
        ret = True
        if lib.auto.isphysical:
            agent.agentIP = lib.auto.agentData['agentIP']
        else:
            agentVM = None
            ret = False
            #BAT_AUTO_BLR
            #if self.poolInfo.get('VADC', False):
            #    agentVM = self.poolInfo['baseVM']
            #    ret, agent.agentIP = vadc.GetIpAddress(agentVM)
            #else:
            agentVM = broker.GetAgentVMName(pool)
            ret, agent.agentIP = broker.VI_GetGuestIP(agentVM)
        if ret:
            # check client copy to agent
            kits.Comment("Agent IPAddress: {}".format(agent.agentIP))
            client.UngrabDesktop()
            time.sleep(2)
            import pyperclip
            pyperclip.copy(clientText)
            time.sleep(2)
            client.ActivateDesktop(pool)
            time.sleep(2)
            lpaste = pyperclip.paste()
            kits.Comment("client->Agent: client side clipboard content: {}".format(lpaste))
            #conn = rpyc.classic.connect(agent.agentIP, ipv6=isipv6)
            conn = rpyc.classic.connect(agent.agentIP)
            rclip = conn.modules.pyperclip
            time.sleep(2)
            rpaste = ''
            try:
                rpaste = rclip.paste()
                print("****************** agent clipboard *****************")
            except Exception as e:
                print("Exception in paste agent clipboard content", e)
            kits.Comment("client->Agent: agent side clipboard content: {}".format(rpaste))
            ret = rpaste == lpaste
            kits.Verify(ret, True, 'copy from client to agent via clipboard succeeded')
            kits.Verify(ret, True, 'copy from client to agent via clipboard succeeded')
            if direction in (1, 3):
                # check agent copy to client, follow the steps below
                client.ActivateDesktop(pool)
                time.sleep(2)
                rcopy = 'FromAgent\n' * 50
                rclip.copy(rcopy)
                time.sleep(2)
                client.UngrabDesktop()
                time.sleep(2)
                client.ActivateDesktop(pool)
                time.sleep(2)
                print("****************** agent clipboard *****************")
                rpaste2 = ''
                try:
                    rpaste2 = rclip.paste()
                    print(repr(rpaste2))
                    kits.Comment("Agent->client: agent side clipboard content: {}".format(rpaste2))
                except Exception as e:
                    print("Exception in paste client clipboard content", e)
                lpaste2 = pyperclip.paste()
                print(repr(lpaste2))
                kits.Comment("Agent->client: client side clipboard content: {}".format(lpaste2))
                ret2 = False
                if lpaste2 and rpaste2:
                    ret2 = lpaste2.splitlines() == rpaste2.splitlines()
                kits.Verify(ret2, True, 'copy from agent to client via clipboard succeeded')
            conn.close()
            return ret and ret2
        else:
            return False

    def CheckUSB(self, includeHid=True, includeStor=True):
        """
        1, let's simplify the usb class here with only storage and HID for now;
        2, Linux Client side USB devices will be found automatically;
        3, Windows/Mac Client side USB devices will be specified in auto.ini or test file;
        """
        agentUsbs = agent.GetUSBDevices()
        if not len(agentUsbs):
            kits.Verify(False, any((includeHid, includeStor)), "None USB device redirected successfully in agent" )
            return False
        agentDevs = [(x.vendor, x.vid, x.pid, x.interfaces[0].usbclass) for x in agentUsbs]
        kits.Comment(f'Agent USB Devices: {agentDevs}')
        # sled11 and rhel6 show HID but rhel7 show 'Human Interface Device'
        aStor = [ x for x in agentDevs if 'stor' in x[3].lower()]
        aHid = [ x for x in agentDevs if 'hid' in x[3].lower()]
        aHid += [x for x in agentDevs if 'human' in x[3].lower()]
        kits.Comment(f'Agent HID Device: {aHid}')
        kits.Comment(f'Agent Storage Device: {aStor}')
        client.GetUsbDevices(lib.auto.clientData)
        kits.Comment(f'Client HID Device: {client.hidDevs}')
        kits.Comment(f'Client Storage Device : {client.storageDevs}')
        kits.Verify(includeHid, bool(aHid), "USB HID device redirected successfully in agent" )
        kits.Verify(includeStor, bool(aStor), "USB Storage device redirected successfully in agent" )
        ret = True
        if len(aHid):
            for idx in aHid:
                vidResult = hex(int(idx[1], 16)) in [hex(int(x[0], 16)) for x in client.hidDevs]
                pidResult = hex(int(idx[2], 16)) in [hex(int(x[1], 16)) for x in client.hidDevs]
                vp = all((vidResult, pidResult))
                kits.Verify(True, vp, f"The Vid and Pid of HID device: {idx[0]} is matched " )
                ret = ret and vp
        if len(aStor):
            for idx in aStor:
                vidResult = hex(int(idx[1], 16)) in [hex(int(x[0], 16)) for x in client.storageDevs]
                pidResult = hex(int(idx[2], 16)) in [hex(int(x[1], 16)) for x in client.storageDevs]
                vp = all((vidResult, pidResult))
                kits.Verify(True, vp, f"The Vid and Pid of Storage device {idx[0]} is matched" )
                ret = ret and vp
        return ret

    def CheckPrinter(self, times=10):
        """
        Let's simplify the scenario here:
           - Bullzip PDF Printer for windows client
           - cups-pdf for Linux client
        """
        while times > 0:
            clientPrinters = client.GetPrinters()
            print(f"Printers in Client:\n{clientPrinters}")
            agentPrinters = agent.GetPrinters()
            print(f"Printers in Agent:\n{agentPrinters}")
            ret = len(agentPrinters) >= len(clientPrinters)
            if ret:
                break
            times -= 1
            time.sleep(2)
            client.ClickCenter()

        ret = len(agentPrinters) == len(clientPrinters)
        kits.Verify(ret, True, "all Printers redirected successfully in agent" )
        kits.Comment(f"Printers in Agent:\n{agentPrinters}")
        kits.Comment(f"Printers in Client:\n{clientPrinters}")
        agent.UploadPrintingFiles()
        for idx in agentPrinters:
            if 'testpdf' in idx[0]:
                continue
            if 'microsoft' in idx[0].lower():
                continue
            ret = self.SimplePdfCheck(idx[0])
            kits.Verify(ret, True, "Redirected Printer works !" )
            return ret

    def PdfTextCheck(self, printer):
        rets = []
        client.CleanPDFDir()
        agent.PrintText(printer)
        kits.Wait(30)
        outp = client.GetLatestPDF()
        print(f"Text PDF job from agent: {outp}")
        ret = outp != None
        kits.Verify(ret, True, "Get Printing Job for Text from Agent")
        rets.append(ret)
        if ret:
            if not os.path.exists(outp):
                return False
            dest = orig = ''
            dest = client.GetPdfText(outp)
            kits.Comment(f'Printed text:\n{repr(dest)}')
            txtFile = os.path.normpath(os.path.join(os.getcwd(), 'ps', 'vdi.pdf'))
            orig = client.GetPdfText(txtFile)
            kits.Comment(f'Original text:\n{repr(orig)}')
            origClean = orig.strip(' \t\n\r')
            destClean = dest.strip(' \t\n\r')
            print("================original text================")
            print(f"{origClean}")
            print("================printed text================")
            print(f"{destClean}")
            ret = dest.count('Printing From Linux VDI') == 24
            rets.append(ret)
            kits.Verify(ret, True, "Same Text Printed")
        return all(rets)

    def PdfImageCheck(self, printer, fn='color.png'):
        import fitz
        import lib.findPic as pic
        rets = []
        client.CleanPDFDir()
        agent.PrintImage(printer)
        kits.Wait(30)
        outp = client.GetLatestPDF()
        print(f"Image PDF job from agent: {outp}")
        ret = outp != None
        kits.Verify(ret, True, "Get Printing Job for Image from Agent")
        if not outp:
            return False
        if not os.path.exists(outp):
            return False
        homeDir = os.path.expanduser("~")
        if not ret:
            return False
        imgFile = os.path.normpath(os.path.join(os.getcwd(), 'ps', fn))
        with fitz.open(outp) as p:
            pages = len(p)
            for pageIdx in range(pages):
                for imgIdx, img in enumerate(p.get_page_images(pageIdx)):
                    xref = img[0]
                    image = fitz.Pixmap(p, xref)
                    imgSave = os.path.join(homeDir, f"{pageIdx}_{imgIdx}.png")
                    if os.path.exists(imgSave):
                        os.remove(imgSave)
                        time.sleep(1)
                    # check if GRAY or RGP
                    if image.n < 5:
                        image.save(imgSave)
                    else:
                        newImage = fitz.Pixmap(fitz.csRGB, image)
                        newImage.save(imgSave)
                    if not os.path.exists(imgSave):
                        kits.Verify(False, True, "No images printed")
                        rets.append(False)
                        continue
                    kits.Screenshot('Image from Redirected Printer', imgSave)
                    try:
                        ret, _ = pic.FindPic(imgSave, srcImages=[imgFile], threshold=0.95,
                                                matchAlgoIdx=1, visualize=False)
                        print(f"{('not detected', 'detected')[ret]}, between original: {imgFile} and printed: {imgSave}")
                        rets.append(ret)
                        time.sleep(2)
                        os.remove(imgSave)
                    except Exception as e:
                        print("Exception in checking image within PDF ", e)
        return all(rets)

    def SimplePdfCheck(self, printer):
        ret1 = ret2 = True
        ret1 = self.PdfImageCheck(printer)
        ret2 = self.PdfTextCheck(printer)
        return ret1 and ret2

    def CheckAudioOut(self, threshold=5000):
        # step 1: copy src audio to agent, like ~/summer.wav
        audioClientFile = os.path.normpath(os.path.join(os.getcwd(), 'ps', 'summer.wav'))
        maxSrc = audio.GetAudioInfo(audioClientFile)
        print(f'max value of source audio: {maxSrc}')
        audioAgentFile = '~/summer.wav'
        if not agent.CopyAudioFileToAgent(audioClientFile, audioAgentFile):
            kits.Comment('failed to copy audio test file to agent')
            return False

        # step 2: play audio on agent,
        player = Thread(target=agent.PlaySound, args=(audioAgentFile,))
        player.start()

        # step 3: record audio out on client
        start = datetime.now()
        ts = start.strftime('%Y-%m-%d-%H-%M-%S')
        audioName = f'audioOut_{ts}.wav'
        audioPng = f'audioOut_{ts}.png'
        audioFile = os.path.join(os.path.expanduser("~"), audioName)
        audioPlot = os.path.join(os.path.expanduser("~"), audioPng)
        recorder = Thread(target=client.RecordAudio, kwargs={'audioFile':audioFile})
        recorder.start()
        recorder.join()

        player.join()

        # step 4: get audio out file info for silent detection
        if not os.path.exists(audioFile):
            self.assertTrue(False, "Not Audio Out Recorded!")
            return

        maxRec = audio.GetAudioInfo(audioFile)
        print(f'max value of source audio: {maxRec}')

        # check waveform for manually check
        audio.PlotAudio(audioFile, audioPlot)
        kits.Log('audio wave', audioPlot, delete=True)
        # use magic value
        ret =  maxRec > threshold
        kits.Verify(ret, True, 'Client hear Audio Out Sound!')
        kits.Log("audio out sound", audioFile, delete=True)

    def CheckAudioIn(self, threshold=150):
        # set threshold to 100 for U8
        # step 1: get src audio file, like ~/summer.wav
        audioClientFile = os.path.normpath(os.path.join(os.getcwd(), 'ps', 'summer.wav'))
        maxSrc = audio.GetAudioInfo(audioClientFile)
        print(f'max value of source audio: {maxSrc}')

        # step 2: make sure, the record file does not exist on agent side
        audioRecFile = "agent_summer.wav"
        kits.Comment(f'audio record file: {audioRecFile}')
        audioAgentFile = agent.GetOutputFromAgentDirectly(cmd="echo ~/%s" % audioRecFile,
                                                          agentIP=agent.agentIP, user=agent.user).strip()
        agent.DeleteAgentAudioFile(audioAgentFile)

        # step 3: play audio on client
        player = Thread(target=client.PlayAudio, args=(audioClientFile,))
        player.start()

        # step 4: record audio in on agent
        recorder = Thread(target=agent.RecordSound, args=(audioAgentFile,))
        recorder.start()

        # step 5: wait until both finished
        recorder.join()
        player.join()

        # step 6: get audio in file from agent side
        audioInFile = agent.CopyAudioFileFromAgent(audioAgentFile)
        if not audioInFile:
            self.assertTrue(False, "Not Audio In Recorded!")
            return

        # check waveform for manually check
        audioPng = 'audioIn.png'
        audioPlot = os.path.join(os.path.expanduser("~"), audioPng)
        audio.PlotAudio(audioInFile, audioPlot)
        kits.Log('audio wave of audio in', audioPlot, delete=True)

        # check max loudness
        maxRec = audio.GetAudioInfo(audioInFile)
        kits.Comment(f'max loudness of Audio In: {maxRec}')

        ret =  maxRec > threshold
        kits.Verify(ret, True, 'Agent hear Audio In Sound!')
        kits.Log("audio In sound", audioInFile, delete=True)

    def Verify_NW_Recovery(self, scenario='fallback', downTime=25, upTime=15, step=5, repeate=1,
                           checkCollabIcon=True, sizeList=['1024x768', '1366x768', '1440x900']):
        """
        Verify network recovery under different scenarios
        """
        desc = 'BlastWorker process exist after network recovery'
        ret = False
        if scenario in ('udp', 'tcp'):
            for dt in range(2, downTime, step):
                # comment this out for bug 2102571
                # for ut in range(2, min(dt, upTime), step):
                for idx in range(repeate):
                    agent.Iptables(protocol=scenario)
                    time.sleep(dt)
                    agent.Iptables(protocol=scenario, action='ACCEPT')
                    time.sleep(15)
                    client.ClickCenter()
                    ret = agent.IsProcessExist()
                    kits.Verify(ret, True, desc)
                    self.assertEqual(ret, True, msg=desc)
                    # Nobody is capturing the return value
                    if not ret:
                        return ret
                    c1 = "{} in {} times ".format(desc, idx+1)
                    c2 = "with network down time as {} and up time as {}".format(dt, 15)
                    comments = c1 + c2
                    kits.Comment(comments)
                    print(comments)

                    cdr = agent.IsProcessExist(name='cdrserver')
                    kits.Comment('cdrserver still exists: {}'.format('True' if cdr else 'False'))
                    usb = agent.IsProcessExist(name='usbRedirectionServer')
                    kits.Comment('usbRdirectionServer still exists: {}'.format('True' if usb else 'False'))
                    mks = agent.IsProcessExist(name='mksvchanserver')
                    kits.Comment('mksvchanserver still exists: {}'.format('True' if mks else 'False'))
                    printer = agent.IsProcessExist(name='printSvc')
                    kits.Comment('printSvc still exists: {}'.format('True' if printer else 'False'))
                    self.CheckAutofit(sizeList=sizeList, checkCollabIcon=checkCollabIcon)

        elif scenario == 'fallback':
            for dt in range(2, downTime, step):
                # comment this out for bug 2102571
                # for ut in range(2, min(dt, upTime), step):
                for idx in range(repeate):
                    agent.Iptables(protocol='udp')
                    time.sleep(dt)
                    agent.Iptables(protocol='tcp')
                    time.sleep(dt)
                    agent.Iptables(protocol='tcp', action='ACCEPT')
                    agent.Iptables(protocol='udp', action='ACCEPT')
                    time.sleep(15)
                    client.ClickCenter()
                    ret = agent.IsProcessExist()
                    kits.Verify(ret, True, desc)
                    if not ret:
                        return ret
                    c1 = "{} in {} times ".format(desc, idx+1)
                    c2 = "with network down time as {} and up time as {}".format(dt, 15)
                    comments = c1 + c2
                    kits.Comment(comments)
                    print(comments)

                    cdr = agent.IsProcessExist(name='cdrserver')
                    kits.Comment('cdrserver still exists: {}'.format('True' if cdr else 'False'))
                    usb = agent.IsProcessExist(name='usbRedirectionServer')
                    kits.Comment('usbRdirectionServer still exists: {}'.format('True' if usb else 'False'))
                    mks = agent.IsProcessExist(name='mksvchanserver')
                    kits.Comment('mksvchanserver still exists: {}'.format('True' if mks else 'False'))
                    printer = agent.IsProcessExist(name='printSvc')
                    kits.Comment('printSvc still exists: {}'.format('True' if printer else 'False'))
                    if not sizeList:
                        self.CheckAutofitWeb()
                    else:
                        self.CheckAutofit(sizeList=sizeList, checkCollabIcon=checkCollabIcon)
                    agent.CheckCpuMem()
        else: # client disable NIC
            for dt in range(2, downTime, step):
                for idx in range(repeate):
                    client.SleepClientNetwork(dt)
                    time.sleep(dt)
                    client.ClickCenter()
                    ret = agent.IsProcessExist()
                    kits.Verify(ret, True, desc)
                    if not ret:
                        return ret
                    c1 = "{} in {} times ".format(desc, idx + 1)
                    c2 = "with network down time as {} and up time as {}".format(dt, 15)
                    comments = c1 + c2
                    kits.Comment(comments)
                    print(comments)

                    cdr = agent.IsProcessExist(name='cdrserver')
                    kits.Comment('cdrserver still exists: {}'.format('True' if cdr else 'False'))
                    usb = agent.IsProcessExist(name='usbRedirectionServer')
                    kits.Comment('usbRdirectionServer still exists: {}'.format('True' if usb else 'False'))
                    clipboard = agent.IsProcessExist(name='mksvchanserver')
                    kits.Comment('mksvchanserver still exists: {}'.format('True' if clipboard else 'False'))
                    printer = agent.IsProcessExist(name='printSvc')
                    kits.Comment('printSvc still exists: {}'.format('True' if printer else 'False'))
                    if not sizeList:
                        self.CheckAutofitWeb()
                    else:
                        self.CheckAutofit(sizeList=sizeList, checkCollabIcon=checkCollabIcon)
                    agent.CheckCpuMem()
        return ret

    def GetVadcBuildCombo(self, agentVM):
        if not 'agentBuildNum' in cached_values:
            ret, agent.agentIP = vadc.GetIpAddress(agentVM)
            if ret:
                buildNum, buildType = agent.GetAgentBuild()
                if not 'unknown' == buildNum:
                    cached_values['agentBuildNum']= buildNum
                if not 'unknown' == buildType:
                    cached_values['agentBuildType'] = buildType
        if not 'clientBuildNum' in cached_values:
            cb = client.GetClientBuild()
            if not 'unknown' == cb:
                cached_values['clientBuildNum'] = cb

        kits.Comment(f'Client  build: {cached_values["clientBuildNum"]}')
        kits.Comment(f'Agent   build: {cached_values.get("agentBuildNum", "unknown")}')
        kits.Comment(f'Agent   buildType: {cached_values.get("agentBuildType", "unknown")}')

    def GetBuildCombo(self, pool):
        if not 'brokerBuildNum' in cached_values:
            bb, _ = broker.HV_GetBuildInfo(pool)
            if not 'unknown' == bb:
                cached_values['brokerBuildNum'] = bb
        if not 'agentBuildNum' in cached_values:
            ret = True
            if lib.auto.isphysical:
                agent.agentIP = lib.auto.agentData['agentIP']
            else:
                agentVM = broker.GetAgentVMName(pool)
                ret, agent.agentIP = broker.VI_GetGuestIP(agentVM)
                if ret:
                    buildNum, buildType = agent.GetAgentBuild()
                    if not 'unknown' == buildNum:
                        cached_values['agentBuildNum']= buildNum
                    if not 'unknown' == buildType:
                        cached_values['agentBuildType'] = buildType
        if not 'clientBuildNum' in cached_values:
            cb = client.GetClientBuild()
            if not 'unknown' == cb:
                cached_values['clientBuildNum'] = cb

        kits.Comment(f'Client  build: {cached_values["clientBuildNum"]}')
        kits.Comment(f'Broker  build: {cached_values["brokerBuildNum"]}')
        kits.Comment(f'Agent   build: {cached_values.get("agentBuildNum", "unknown")}')
        kits.Comment(f'Agent   buildType: {cached_values.get("agentBuildType", "unknown")}')

    def PreLaunchVadc(self, pool, times=90, ntpSync=False):
        print("entering PreLaunchVadc")
        originalTimes = times
        agentVM = self.poolInfo['baseVM']
        self.GetVadcBuildCombo(agentVM)
        ret, agent.agentIP = vadc.GetIpAddress(agentVM)
        if ret:
            # kits.Comment("Agent IPAddress: {}".format(agent.agentIP))
            client.ClearSSHKey(agentIP=agent.agentIP)
            cached_values['agentIP'] = agent.agentIP
            # set vadc host ip address
            vadc.host = agent.agentIP
            # set vadc launch broker ip address
            if self.poolInfo.get('multisession', False):
                for idx in remoteclients:
                    idx.broker = agent.agentIP
                    idx.domain = self.poolInfo['domain']
            else:
                client.broker = agent.agentIP
                client.domain = self.poolInfo['domain']

            if ntpSync:
                agent.ReSyncNtp()
            cached_values['agentTS'] = agent.GetCurrentTS()
            cached_values['clientTS '] = client.GetCurrentTS()
            # kits.Comment(f"Agent  DataTime: {cached_values['agentTS']}")
            # kits.Comment(f"Client DataTime: {cached_values['clientTS']}")
            agent.SetDistroName()
        else:
            self.assertTrue(False, "Failed to Get Agent IP !")
            return False
        # skip check in multi-session mode
        if self.poolInfo.get('multisession', False):
            return True

        while times > 0:
            state, _ = vadc.GetSessionInfo()

            if state == 'AVAILABLE':
                break
            else:
                kits.Warning("Pool {} is in {} state".format(pool, state))
                if state == 'DISCONNECTED':
                    agent.Logoff()
                time.sleep(10)
                times -= 10
                continue
        else:
            comment = "Pool {} is not AVAILABLE or DISCONNECTED for {} seconds".format(pool, originalTimes)
            kits.Comment(comment)
            self.assertTrue(False, comment)
            return False

    def GetPoolType(self, pool):
        if not 'poolType' in cached_values:
            poolType = broker.HV_GetPoolType(pool=pool)
            if not 'Done' == poolType:
                cached_values['poolType'] = poolType
        kits.Comment(f'{pool} is {cached_values["poolType"]} pool')
        return cached_values['poolType']

    def PreLaunch(self, pool, bypassBSG=False, times=120, rebVhci=False, ntpSync=False):
        pktcapture = lib.auto.toolData.get('pktcapture', 'false')
        if pktcapture == 'true':
            client.StartPktCapture()
        if self.poolInfo.get('VADC', False):
            return self.PreLaunchVadc(pool)
        if bypassBSG:
            # disable BSG
            broker.HV_SetBSG(enable=True)
        else:
            # enable BSG
            broker.HV_SetBSG(enable=False)
        originalTimes = times
        self.poolType = self.GetPoolType(pool)
        if self.poolType in ('RDS', 'APP-VDI', 'APP'):
            self.supportUsb = False
        else:
            self.supportUsb = True
        if self.poolType == 'Done':
            return False
        self.GetBuildCombo(pool)
        ret = True
        if lib.auto.isphysical:
            agent.agentIP = lib.auto.agentData['agentIP']
        else:
            agentVM = broker.GetAgentVMName(pool)
            ret, agent.agentIP = broker.VI_GetGuestIP(agentVM)
        if ret:
            kits.Comment("Agent IPAddress: {}".format(agent.agentIP))
            client.ClearSSHKey(agentIP=agent.agentIP)
            cached_values['agentIP'] = agent.agentIP
            if ntpSync:
                agent.ReSyncNtp()
            cached_values['agentTS'] = agent.GetCurrentTS()
            cached_values['clientTS'] = client.GetCurrentTS()
            cached_values['brokerTS'] = broker.GetCurrentTS()
            # kits.Comment("Agent  DataTime: {}".format(cached_values['agentTS']))
            # kits.Comment("Broker DateTime: {}".format(cached_values['brokerTS']))
            # kits.Comment("Client DataTime: {}".format(cached_values['clientTS']))
            agent.SetDistroName()
        else:
            self.assertTrue(False, "Failed to Get Agent IP !")
            return False
        if self.poolType in ('RDS', 'APP', 'APP-VDI'):
            return True
        state = None
        while times > 0:
            state = broker.HV_GetDesktopState(pool)
            if state == 'AVAILABLE':
                if rebVhci:
                    agent.ReBuildVHCI(agentIP=agent.agentIP)
                break
            else:
                kits.Warning("Pool {} is in {} state".format(pool, state))
                if state == 'DISCONNECTED':
                    agent.Logoff()
                time.sleep(10)
                times -= 10
                continue
        if state == 'AVAILABLE' or state == 'DISCONNECTED':
            return True
        else:
            comment = "Pool {} is not AVAILABLE or DISCONNECTED for {} seconds".format(pool, originalTimes)
            kits.Comment(comment)
            self.assertTrue(False, comment)
            return False

    def WaitSessionState(self, expectation='CONNECTED'):
        self.poolType = self.GetPoolType(pool=self.pool)
        if self.poolType in ('RDS', 'APP', 'APP-VDI'):
            users=[]
            users.append(self.poolInfo['user'])
            ret = broker.HV_WaitState(pool=self.pool, expectation=expectation, users=users)
        else:
            ret = broker.HV_WaitDesktopState(self.pool, expectation, 120)
        print(f"Wait expected Status:{expectation} {('failed', 'succeeded')[ret]}")
        return ret

    def CheckLogin(self, pool, agentIP, clientHost='localhost', threshold=0.8):
        if not self.poolInfo.get('VADC', False):
            users=[]
            users.append(self.poolInfo['user'])
            if self.poolType in ('RDS', 'APP', 'APP-VDI'):
                ret = broker.HV_WaitState(pool=self.pool, expectation='CONNECTED', users=users)
            else:
                ret = broker.HV_WaitDesktopState(pool, 'CONNECTED', 120)

                ret = broker.HV_WaitDesktopState(pool, 'CONNECTED', 120)
                kits.Verify(ret, True, 'Broker show connected status')
                if ret:
                    tconsumed = time.time() - client.cache['connStart']
                    t = tconsumed < 180
                    kits.Verify(t, True, f'consumed {tconsumed:.3f} seconds to show "CONNECTED" in broker, less than 180')
        if agent.GetGUILoginUser(agentIP):
            print("agent IP:"+agentIP+"Client host:"+clientHost)
            if not client.IsRemoteDesktopShownMss(agentIP, clientHost=clientHost, threshold=threshold):
                print(('Detect Agent Desktop Failed' + '!'*80))
            lockStatus = agent.GetScreenLockStatus(agent.agentIP)
            # kits.Comment(f"lockStatus after logon from {client.os} Clients:\n{lockStatus}")
            if len(lockStatus):
                unlockedDesktop = lockStatus.count('screen unlocked') > 0
                if not unlockedDesktop:
                    kits.Warning(f"user unlock desktop successfully from {client.os} Client!")
        else:
            comments = "Failed to Login from {} Client".format(client.os)
            client.UploadRMKSLog(cached_values.get('clientTS'))
            kits.Verify(False, True, comments, screenshot=True)
            self.assertTrue(False, comments)

    def CheckRedirection(self, testCdr=True, testUsb=True, testPrinter=True,
                         testClipboard=True, testAudioOut=True):
        if features.get('cdr') and testCdr:
            if not agent.VerifyCDRRediction():
                proc = 'cdrserver'
                print(('CDR test failed' + '!'*80))
                ret = agent.IsProcessExist(name=proc)
                kits.Comment(f"{proc} exists: {'True' if {ret} else 'False'}")
                agent.GetCallStack(procName=[proc])

        if features.get('clipboard') and testClipboard:
            if not self.CheckClipboard():
                proc = 'mksvchanserver'
                ret = agent.IsProcessExist(name=proc)
                kits.Comment(f"{proc} exists: {'True' if {ret} else 'False'}")
                agent.GetCallStack(procName=[proc])

        if features.get('usb') and testUsb:
            proc = 'usbRedirectionServer'
            print('check usb')
            ret = True
            if 'usbAutoDetect' in lib.auto.clientData:
                print('check usb auto detect')
                ret = self.CheckUSB()
            else:
                ret = agent.VerifyUSBRedictionSimple()
            if not ret:
                print('check usb auto call stack')
                ret = agent.IsProcessExist(name=proc)
                kits.Comment(f"{proc} exists: {'True' if {ret} else 'False'}")
                agent.GetCallStack(procName=[proc])

        if features.get('printer') and testPrinter:
            proc = 'printSvc'
            print('check Printer')
            ret = self.CheckPrinter()
            if not ret:
                print('check printer callstack')
                ret = agent.IsProcessExist(name=proc)
                kits.Comment(f"{proc} exists: {'True' if {ret} else 'False'}")
                agent.GetCallStack(procName=[proc])

        if features.get('audioout') and testAudioOut:
            print('check Audio Out')
            ret = self.CheckAudioOut()

    def CheckSpeed(self, testCdr=True, testUsb=False):
        if testCdr:
            agent.CheckCDRSpeed()

    """
    This test case checks all the Features
    -> CDR, Clipboard, Printer, SSO, USB, Audio In/Out
    Since we enabled them in .ini as well as in this function
    """
    def BlastH264EnabledUDPv2(self, manualLogin=False, rebVhci=False, checkTrueSso=False, hwEncoding=False,
                              testPrinter=True, testUsb=True, testAudioOut=False):
        pool = self.pool = self.poolInfo['pool']
        h264 = True
        self.PreLaunch(pool=pool, bypassBSG=True, rebVhci=rebVhci)
        print('########################- H264 Enabled, UDP -################################')
        self.Scenarios.extend(['udp'])
        testIds.assignTestCaseIds(features,self.Scenarios)
        ret = client.LaunchToDesktop(poolInfo=self.poolInfo, h264=True, udp='mix')
        if not ret:
            agent.GetCallStack()
            fromTS = client.cache.get('connectTS', None)
            if fromTS:
                # let's start from minutes
                fromTS = fromTS[:-4]
            agent.GetTSLogs(fromTS=fromTS)
            comments = 'client login failed'
            unittest.TestCase.assertTrue(False, comments)
        client.MoveDesktopToCenter(self.poolInfo['pool'])
        if manualLogin or not features.get('sso'):
            client.ManualLogin()
        self.CheckLogin(pool, agent.agentIP)
        if checkTrueSso and features.get('truesso'):
            self.CheckCertSsoData()
        self.CheckRedirection(testPrinter=testPrinter, testUsb=testUsb, testAudioOut=testAudioOut)
        self.CheckSpeed()
        h264, udp = agent.CheckH264UDPViaLog(hwEncoding=hwEncoding)
        kits.Verify(h264, True, 'H264 Enabled')
        self.assertEqual(h264, True, msg='H264 Enabled')
        print(f'Features Tested:\n{features}')
        kits.Comment('UDPv2 Detected : {}'.format(udp))
        broker.Disconnect_Logoff(pool=self.poolInfo['pool'], action='logoff')

    def BlastH264EnabledTCP(self, manualLogin=False, rebVhci=False, checkTrueSso=False, hwEncoding=False,
                            testPrinter=False, testUsb=True, testAudioOut=False):
        kits.Comment(f'Feature Matrix:\n{features}')
        self.Scenarios.extend(['tcp'])
        testIds.assignTestCaseIds(features,self.Scenarios)
        pool = self.pool = self.poolInfo['pool']
        h264 = True
        self.PreLaunch(pool=pool, bypassBSG=False, rebVhci=rebVhci)
        kits.Comment('########################- H264 Enabled, TCP -################################')
        ret = client.LaunchToDesktop(poolInfo=self.poolInfo, h264=True, udp='mix')
        if not ret:
            agent.GetCallStack()
            fromTS = client.cache.get('connectTS', None)
            if fromTS:
                # let's start from minutes
                fromTS = fromTS[:-4]
            agent.GetTSLogs(fromTS=fromTS)
            comments = 'client login failed'
            unittest.TestCase.assertTrue(False, comments)
        client.MoveDesktopToCenter(self.pool)
        if manualLogin or not features.get('sso'):
            client.ManualLogin()
        self.CheckLogin(pool, agent.agentIP)
        if checkTrueSso and features.get('truesso'):
            self.CheckCertSsoData()
        self.CheckRedirection(testPrinter=testPrinter, testUsb=testUsb, testAudioOut=testAudioOut)
        h264, udp = agent.CheckH264UDPViaLog(hwEncoding=hwEncoding)
        kits.Verify(h264, True, 'H264 Enabled')
        kits.Comment('UDPv2 Detected : {}'.format(udp))
        broker.Disconnect_Logoff(pool=self.poolInfo['pool'], action='logoff')

    def BlastH264DisabledUDPv2(self, manualLogin=False, rebVhci=False, checkTrueSso=False, testPrinter=False,
                               testUsb=True, testAudioOut=False):
        print(f'Feature Matrix:\n{features}')
        pool = self.pool = self.poolInfo['pool']
        h264 = False
        testIds.assignTestCaseIds(features,self.Scenarios)
        self.PreLaunch(pool=pool, bypassBSG=True, rebVhci=rebVhci)
        # kits.Comment('########################- H264 Disabled, UDP -################################')
        ret = client.LaunchToDesktop(poolInfo=self.poolInfo, h264=False, udp='mix')
        if not ret:
            agent.GetCallStack()
            fromTS = client.cache.get('connectTS', None)
            if fromTS:
                # let's start from minutes
                fromTS = fromTS[:-4]
            agent.GetTSLogs(fromTS=fromTS)
            comments = 'client login failed'
            unittest.TestCase.assertTrue(False, comments)
        client.MoveDesktopToCenter(self.pool)
        if manualLogin or not features.get('sso'):
            client.ManualLogin()
        self.CheckLogin(pool, agent.agentIP)
        if checkTrueSso and features.get('truesso'):
            self.CheckCertSsoData()
        self.CheckRedirection(testPrinter=testPrinter, testUsb=testUsb, testAudioOut=testAudioOut)
        self.CheckSpeed()
        h264, udp = agent.CheckH264UDPViaLog()
        print("Test Complete h264:"+str(h264)+", UPDv2 Detected:"+str(udp))
        kits.Verify(h264, False, 'H264 Enabled')
        kits.Comment('UDPv2 Detected : {}'.format(udp))
        broker.Disconnect_Logoff(pool=self.poolInfo['pool'], action='logoff')

    def BlastH264DisabledTCP(self, manualLogin=False, rebVhci=False, checkTrueSso=False, testPrinter=False,
                             testUsb=True, testAudioOut=False):
        kits.Comment(f'Feature Matrix:\n{features}')
        pool = self.pool = self.poolInfo['pool']
        h264 = False
        testIds.assignTestCaseIds(features,self.Scenarios)
        self.PreLaunch(pool=pool, bypassBSG=True, rebVhci=rebVhci)
        kits.Comment('########################- H264 Disabled, TCP -################################')
        ret = client.LaunchToDesktop(poolInfo=self.poolInfo, h264=False, udp='mix')
        if not ret:
            agent.GetCallStack()
            fromTS = client.cache.get('connectTS', None)
            if fromTS:
                # let's start from minutes
                fromTS = fromTS[:-4]
            agent.GetTSLogs(fromTS=fromTS)
            comments = 'client login failed'
            unittest.TestCase.assertTrue(False, comments)
        client.MoveDesktopToCenter(self.pool)
        if manualLogin or not features.get('sso'):
            client.ManualLogin()
        self.CheckLogin(pool, agent.agentIP)
        if checkTrueSso and features.get('truesso'):
            self.CheckCertSsoData()
        self.CheckRedirection(testPrinter=testPrinter, testUsb=testUsb, testAudioOut=testAudioOut)
        h264, udp = agent.CheckH264UDPViaLog()
        kits.Verify(h264, False, 'H264 Enabled')
        kits.Comment('UDPv2 Detected : {}'.format(udp))
        broker.Disconnect_Logoff(pool=self.poolInfo['pool'], action='logoff')

    def TestAutofit(self, manualLogin=False, h264=False):
        pool = self.pool = self.poolInfo['pool']
        self.PreLaunch(pool=pool, bypassBSG=True)
        kits.Comment('########################- H264 Enabled, UDP -################################')
        ret = client.LaunchToDesktop(poolInfo=self.poolInfo, h264=h264, udp='mix')
        if not ret:
            agent.GetCallStack()
            fromTS = client.cache.get('connectTS', None)
            if fromTS:
                # let's start from minutes
                fromTS = fromTS[:-4]
            agent.GetTSLogs(fromTS=fromTS)
            comments = 'client login failed'
            unittest.TestCase.assertTrue(False, comments)
        client.MoveDesktopToCenter(self.pool)
        if manualLogin:
            client.ManualLogin()
        self.CheckLogin(pool, agent.agentIP)
        for _ in range(2):
            self.CheckAutofit(sizeList=['1650x1080', '1440x900', '800x600'])

    def NetRecoveryUDP(self, sizeList=['1024x768', '1366x768', '1440x900'], manualLogin=False, checkCollabIcon=True, checkTrueSso=False):
        pool = self.pool = self.poolInfo['pool']
        testIds.assignTestCaseIds(features,self.Scenarios)
        self.PreLaunch(pool=pool, bypassBSG=True)
        kits.Comment('########################- Network Recovery, UDP -################################')
        ret = client.LaunchToDesktop(poolInfo=self.poolInfo, h264=True, udp='mix')
        if not ret:
            agent.GetCallStack()
            fromTS = client.cache.get('connectTS', None)
            if fromTS:
                # let's start from minutes
                fromTS = fromTS[:-4]
            agent.GetTSLogs(fromTS=fromTS)
            comments = 'client login failed'
            unittest.TestCase.assertTrue(False, comments)
        client.MoveDesktopToCenter(self.pool)
        if manualLogin:
            client.ManualLogin()
        self.CheckLogin(pool, agent.agentIP)
        if checkTrueSso:
            self.CheckCertSsoData()
        self.Verify_NW_Recovery(scenario='udp', checkCollabIcon=checkCollabIcon, sizeList=sizeList)
        broker.Disconnect_Logoff(pool=self.poolInfo['pool'], action='logoff')

    def NetRecoveryTCP(self, sizeList=['1024x768', '1366x768', '1440x900'], manualLogin=False, checkCollabIcon=True, checkTrueSso=False):
        pool = self.pool = self.poolInfo['pool']
        self.PreLaunch(pool=pool, bypassBSG=False)
        kits.Comment('########################- Network Recovery, TCP -################################')
        ret = client.LaunchToDesktop(poolInfo=self.poolInfo, h264=True, udp='mix')
        if not ret:
            agent.GetCallStack()
            fromTS = client.cache.get('connectTS', None)
            if fromTS:
                # let's start from minutes
                fromTS = fromTS[:-4]
            agent.GetTSLogs(fromTS=fromTS)
            comments = 'client login failed'
            unittest.TestCase.assertTrue(False, comments)
        client.MoveDesktopToCenter(self.pool)
        if manualLogin:
            client.ManualLogin()
        self.CheckLogin(pool, agent.agentIP)
        if checkTrueSso:
            self.CheckCertSsoData()
        self.Verify_NW_Recovery(scenario='tcp', checkCollabIcon=checkCollabIcon, sizeList=sizeList)

    def NetRecoveryFallback(self, sizeList=['1024x768', '1366x768', '1440x900'], manualLogin=False, checkCollabIcon=True, checkTrueSso=False):
        pool = self.pool = self.poolInfo['pool']
        self.PreLaunch(pool=pool, bypassBSG=False)
        kits.Comment('########################- Network Recovery, Fall back -################################')
        ret = client.LaunchToDesktop(poolInfo=self.poolInfo, h264=True, udp='mix')
        if not ret:
            agent.GetCallStack()
            fromTS = client.cache.get('connectTS', None)
            if fromTS:
                # let's start from minutes
                fromTS = fromTS[:-4]
            agent.GetTSLogs(fromTS=fromTS)
            comments = 'client login failed'
            unittest.TestCase.assertTrue(False, comments)
        client.MoveDesktopToCenter(self.pool)
        if manualLogin:
            client.ManualLogin()
        self.CheckLogin(pool, agent.agentIP)
        if checkTrueSso:
            self.CheckCertSsoData()
        self.Verify_NW_Recovery(scenario='fallback', checkCollabIcon=checkCollabIcon, sizeList=sizeList)

    def HtmlAccess(self, bypassBSG=False, bypassUAG=True, checkTrueSso=False):
        poolInfo = self.poolInfo
        self.pool = poolInfo['pool']
        testIds.assignTestCaseIds(features,self.Scenarios)
        agentVM = self.poolInfo['baseVM']
        if lib.auto.isphysical:
            agent.agentIP = lib.auto.agentData['agentIP']
        else:
            ret = False
            if not self.poolInfo.get('VADC', False):
                bb, ab = broker.HV_GetBuildInfo(self.pool)
                kits.Comment('Broker build: {}'.format(bb))
                kits.Comment('Agent build: {}'.format(ab))
                if bypassBSG:
                    # disable BSG
                    broker.HV_SetBSG(enable=True)
                else:
                    # enable BSG
                    broker.HV_SetBSG(enable=False)

                broker.HV_EnableHtml(poolInfo['pool'])
                agentVM = broker.GetAgentVMName(poolInfo['pool'])
                ret, agent.agentIP = broker.VI_GetGuestIP(agentVM)
            else:
                ret, agent.agentIP = vadc.GetIpAddress(agentVM)
                # set vadc launch broker ip address
                client.broker = agent.agentIP

        if ret:
            kits.Comment("Agent IPAddress: {}".format(agent.agentIP))
            client.ClearSSHKey(agentIP=agent.agentIP)
        else:
            self.assertTrue(False, "Failed to Get Agent IP !")

        client.LaunchToDesktopWithWeb(pool=self.pool, user=self.poolInfo['user'], domain=self.poolInfo['domain'], password=self.poolInfo['password'])
        kits.Wait(10)
        client.HTMLAccess_MoveCenter()
        for idx in range(5):
            if agent.GetGUILoginUser(agentIP=agent.agentIP):
                if not self.poolInfo.get('VADC', False):
                    ret = self.WaitSessionState()
                    kits.Verify(ret, True, 'Broker show connected status')
                    self.assertEqual(ret, True, msg='Broker show connected status')
                else:
                    waitTime = 240
                    while waitTime > 0 :
                        state, _ = vadc.GetSessionInfo()
                        ret = state == 'CONNECTED'
                        if not ret:
                            time.sleep(1)
                            waitTime -= 1
                            continue
                        else:
                            break
                    kits.Verify(ret, True, f'VADC show connected status within {waitTime} seconds')
                    self.assertEqual(ret, True, msg=f'VADC show connected status within {waitTime} seconds')

                if checkTrueSso:
                    self.CheckCertSsoData()
                time.sleep(5)
                lockStatus = agent.GetScreenLockStatus(agent.agentIP)
                kits.Comment(f"lockStatus after logon from HTML Access Clients:\n{lockStatus}")
                if len(lockStatus):
                    unlockedDesktop = lockStatus.count('screen unlocked') > 0
                    kits.Verify(unlockedDesktop, True, "user unlock desktop successfully from HTML Access Client!")
                    self.assertEqual(unlockedDesktop, True, msg="user unlock desktop successfully from HTML Access Client!")

            else:
                agent.GetCallStack()
                fromTS = client.cache.get('connectTS', None)
                if fromTS:
                    # let's start from minutes
                    fromTS = fromTS[:-4]
                agent.GetTSLogs(fromTS=fromTS)
                client.LaunchToDesktopWithWeb_CloseBrowser()
                self.assertTrue(False, "Failed to Login From HTML Access Client!")
                return
            client.ClickCenter()
            client.RefreshBrowser()
            kits.Wait(5)
        broker.Disconnect_Logoff(pool=self.pool, action='logoff')
        time.sleep(10)
        client.LaunchToDesktopWithWeb_CloseBrowser()

    def RepeateConnection(self, times=4, manualLogin=False, scenario='disconnect', bypassBSG=False, bypassUAG=True, testUsb=True,
                          bypassUT=True, h264=True, ncEnable=True, testPrinter=False, testAudioOut=False, testAudioIn=False, checkCollabIcon=True,
                          sizeList=['1024x768','1366x768','1440x900']):
        pool = self.pool = self.poolInfo['pool']
        if scenario == 'disconnect':
            self.Scenarios.extend(['disConReconnect'])
        else:
            self.Scenarios.extend(['logoff'])

        self.Scenarios.extend(['autofit','repeatConnectionH264'])
        testIds.assignTestCaseIds(features,self.Scenarios)
        self.PreLaunch(pool=pool, bypassBSG=bypassBSG)
        if not self.poolInfo.get('VADC', False):
            if bypassBSG:
                # disable BSG
                broker.HV_SetBSG(enable=True)
            else:
                # enable BSG
                broker.HV_SetBSG(enable=False)

            if not bypassUAG:
                self.PreUAGConfig(bypassUT=bypassUT)
        if not ncEnable:
            agent.SetAgentConfig('RemoteDisplay.enableNetworkContinuity', 'FALSE')
        kits.Comment('########################- Repeat connect after {} -################################'.format(scenario))
        for idx in range(1, times + 1):
            ret = client.LaunchToDesktop(poolInfo=self.poolInfo, h264=h264, udp='mix', bypassUAG=bypassUAG)
            if not ret:
                agent.GetCallStack()
                fromTS = client.cache.get('connectTS', None)
                if fromTS:
                    # let's start from minutes
                    fromTS = fromTS[:-4]
                agent.GetTSLogs(fromTS=fromTS)
                comments = 'client login failed'
                unittest.TestCase.assertTrue(False, comments)
            client.MoveDesktopToCenter(self.pool)
            if manualLogin:
                client.ManualLogin()
            if agent.GetGUILoginUser(agentIP=agent.agentIP):
                ret = False
                if not self.poolInfo.get('VADC', False):
                    ret = self.WaitSessionState()
                    kits.Verify(ret, True, 'Broker show connected status')
                    self.assertEqual(ret, True, msg='Broker show connected status')
                else:
                    waitTime = 240
                    while waitTime > 0 :
                        state, _ = vadc.GetSessionInfo()
                        ret = state == 'CONNECTED'
                        if not ret:
                            time.sleep(1)
                            waitTime -= 1
                            continue
                        else:
                            break
                    kits.Verify(ret, True, f'VADC show connected status within {240 - waitTime} seconds')
                lockStatus = agent.GetScreenLockStatus(agent.agentIP)
                kits.Comment(f"lockStatus after logon from {client.os} Clients:\n{lockStatus}")
                if len(lockStatus):
                    unlockedDesktop = lockStatus.count('screen unlocked') > 0
                    if not unlockedDesktop:
                        kits.Warning(f"user unlock desktop successfully from {client.os} Client!")
                if client.IsRemoteDesktopShownMss(agent.agentIP):
                    kits.Comment('Login Succeeded in # {} try'.format(idx))
                client.ClickCenter()
                if idx > 1:
                    self.CheckAutofit(sizeList=sizeList)
                client.ClickCenter()
                self.CheckRedirection(testPrinter=testPrinter, testUsb=testUsb, testAudioOut=testAudioOut)
            else:
                client.UploadRMKSLog(cached_values.get('clientTS'))
                comments = "Failed to Login from {} Client".format(client.os)
                kits.Verify(False, True, comments, screenshot=True)
                kits.Comment('Login Failed in # {} try'.format(idx))
                self.assertTrue(False, comments)
                break

            expectedState = 'DISCONNECTED'
            ret = False
            comments = ''
            if not self.poolInfo.get('VADC', False):
                if scenario == 'disconnect':
                    broker.Disconnect_Logoff(pool=self.pool)
                    time.sleep(10)
                else:
                    broker.Disconnect_Logoff(pool=self.pool, action='logoff')
                    time.sleep(20)
                    expectedState = 'AVAILABLE'
                ret = self.WaitSessionState(expectation=expectedState)
                comments = 'broker show {} status'.format(expectedState)
            else:
                if scenario == 'disconnect':
                    vadc.Disconnect_Logoff(self.poolInfo['user'], action='disconnect')
                else:
                    vadc.Disconnect_Logoff(self.poolInfo['user'], action='logoff')
                    expectedState = 'AVAILABLE'
                waitTime = 240
                while waitTime > 0 :
                    state, _ = vadc.GetSessionInfo()
                    print(f'VADC status: {state}')
                    ret = state == expectedState
                    if not ret:
                        time.sleep(1)
                        waitTime -= 1
                        continue
                    else:
                        break
            kits.Verify(ret, True, comments)
            self.assertEqual(ret, True, msg=comments)
            # comments = 'VADC show {} status'.format(state)
            if scenario == 'disconnect':
                time.sleep(5)
                lockStatus = agent.GetScreenLockStatus(agent.agentIP)
                kits.Comment(f"lockStatus after disconnect from broker:\n{lockStatus}")
                if len(lockStatus):
                    lockedDesktop = lockStatus.count('screen locked') > 0
                    if not lockedDesktop:
                        kits.Warning("desktop locked successfully after disconnect")

            client.KillClient()
            time.sleep(10)
            # for USB storage devices
            if 'linux' in sys.platform:
                client.KillFileExplorer()
            # check CPU and Memory usage
            agent.CheckCpuMem()
        if not ncEnable:
            agent.SetAgentConfig('RemoteDisplay.enableNetworkContinuity', 'TRUE')
        if not bypassUT:
            agent.SetAgentConfig('RemoteDisplay.enableNetworkIntelligence', 'TRUE')

    def SessionStolenWithWeb(self, times=1, bypassBSG=False, testPrinter=False, testUsb=True, bypassUAG=True,
                             bypassUT=True, h264=True, testAudioOut=False, testAudioIn=False, checkCollabIcon=True,
                             sizeList=['1024x768','1366x768','1440x900']):
        self.pool = pool = self.poolInfo['pool']
        self.Scenarios.extend(['sessionstolen'])
        testIds.assignTestCaseIds(features,self.Scenarios)
        self.PreLaunch(pool=pool, bypassBSG=bypassBSG)
        if not bypassUAG:
            self.PreUAGConfig(bypassUT=bypassUT)
        kits.Comment('########################- Session Stolen between HTML Access and {} Client-################################'.format(client.os))
        for idx in range(1, times + 1):
            # launch with native client first
            ret = client.LaunchToDesktop(poolInfo=self.poolInfo, h264=h264, udp='mix', bypassUAG=bypassUAG)
            if not ret:
                agent.GetCallStack()
                fromTS = client.cache.get('connectTS', None)
                if fromTS:
                    # let's start from minutes
                    fromTS = fromTS[:-4]
                agent.GetTSLogs(fromTS=fromTS)
                comments = 'client login failed'
                unittest.TestCase.assertTrue(False, comments)
            client.MoveDesktopToCenter(self.pool)
            self.CheckLogin(pool, agent.agentIP)
            self.CheckRedirection(testPrinter=testPrinter, testUsb=testUsb, testAudioOut=testAudioOut)

            # then with html access
            client.LaunchToDesktopWithWeb(pool=self.pool, user=self.poolInfo['user'], domain=self.poolInfo['domain'], password=self.poolInfo['password'])
            kits.Wait(10)
            if agent.GetGUILoginUser(agentIP=agent.agentIP):
                if not self.poolInfo.get('VADC', False):
                    ret = self.WaitSessionState()
                    kits.Verify(ret, True, 'Broker show connected status')
                    self.assertEqual(ret, True, msg='Broker show connected status')
                else:
                    waitTime = 240
                    while waitTime > 0 :
                        state, _ = vadc.GetSessionInfo()
                        ret = state == 'CONNECTED'
                        if not ret:
                            time.sleep(1)
                            waitTime -= 1
                            continue
                        else:
                            break
                    kits.Verify(ret, True, f'VADC show connected status within {waitTime} seconds')
                    self.assertEqual(ret, True, msg=f'VADC show connected status within {waitTime} seconds')

                lockStatus = agent.GetScreenLockStatus(agent.agentIP)
                kits.Comment(f"lockStatus after logon from HTML Access Clients:\n{lockStatus}")
                if len(lockStatus):
                    unlockedDesktop = lockStatus.count('screen unlocked') > 0
                    kits.Verify(unlockedDesktop, True, "user unlock desktop successfully from HTML Access Client!")
                    self.assertEqual(unlockedDesktop, True, msg="user unlock desktop successfully from HTML Access Client!")
                if 'win32' in sys.platform:
                    browserWindow = pyautogui.getWindow('Omnissa Horizon')
                    if browserWindow:
                        browserWindow.set_foreground()
                        print("Set Web Browser Foreground")
                client.HTMLAccess_MoveCenter()
                client.ClickCenter()
                self.CheckAutofitWeb()
            else:
                agent.GetCallStack()
                fromTS = client.cache.get('connectTS', None)
                if fromTS:
                    # let's start from minutes
                    fromTS = fromTS[:-4]
                agent.GetTSLogs(fromTS=fromTS)
                comments = 'client login failed'
                client.LaunchToDesktopWithWeb_CloseBrowser()
                self.assertTrue(False, "Failed to Login From HTML Access Client!")
            broker.Disconnect_Logoff(pool=self.poolInfo['pool'], action='logoff')
            time.sleep(10)
            client.LaunchToDesktopWithWeb_CloseBrowser()
            client.KillClient()
            time.sleep(10)
        if not bypassUT:
            agent.SetAgentConfig('RemoteDisplay.enableNetworkIntelligence', 'TRUE')

    def DisableTLS12(self, checkTrueSso=False):
        pool = self.pool = self.poolInfo['pool']
        poolInfo = self.poolInfo
        # we must disable BSG at the same time
        self.PreLaunch(pool=pool, bypassBSG=True)
        kits.Comment('########################- Disable TLSv1.2 -################################')
        ret = client.LaunchWithTLS12Disabled(poolInfo=poolInfo)
        if not ret:
            agent.GetCallStack()
            fromTS = client.cache.get('connectTS', None)
            if fromTS:
                # let's start from minutes
                fromTS = fromTS[:-4]
            agent.GetTSLogs(fromTS=fromTS)
            comments = 'Failed to Login with TLSv1.2 Disabled'
            kits.Verify(False, True, comments, screenshot=True)
            unittest.TestCase.assertTrue(False, comments)
        client.MoveDesktopToCenter(pool)
        self.CheckLogin(pool, agent.agentIP)
        if checkTrueSso:
            self.CheckCertSsoData()

    def CheckFIPsStatus(self):
        pool = self.pool = self.poolInfo['pool']
        self.PreLaunch(pool=pool)
        fips = agent.CheckFIPSViaConfig()
        kits.Verify(fips, True, 'FIPs is Enabled')

    def CheckCertSsoData(self, expected=True):
        trueSso = agent.CheckTrueSSOViaDebugLog()
        kits.Verify(trueSso, expected, 'TrueSSO detected in debug Log')

    def PreUAGConfig(self, bypassUT=True):
        blastEnabled = "true"
        if not bypassUT:
            tunnelEnabled = "true"
            # UAG + UT + BSG OFF + NI disable + UDP enable = BEAT
            agent.SetAgentConfig('RemoteDisplay.enableNetworkIntelligence', 'FALSE')
        else:
            tunnelEnabled = "false"
        uag.APSettingPutedgeservice(blastEnabled=blastEnabled, tunnelEnabled=tunnelEnabled)

    def LogoffafterDisconnection(self, manualLogin=False, scenario='IMMEDIATELY', bypassBSG=False, h264=True,
                                 bypassUAG=True, bypassUT=True, isLocked=False, lockedLogff=False, sizeList=['1024x768','1366x768','1440x900']):
        """
        :param scenario: 'NEVER', 'IMMEDIATELY', 'N' for after N minutes
        """
        pool = self.pool = self.poolInfo['pool']
        self.PreLaunch(pool=pool, bypassBSG=bypassBSG)
        if not bypassUAG:
            self.PreUAGConfig(bypassUT=bypassUT)
        if scenario in ('IMMEDIATELY', 'NEVER'):
            setting = "{'desktopSettings.logoffSettings.automaticLogoffPolicy': '%s'}" %scenario
        else:
            setting= "{'desktopSettings.logoffSettings.automaticLogoffPolicy': 'AFTER', 'desktopSettings.logoffSettings.automaticLogoffMinutes': %s}" %scenario
        broker.HV_SetPool(setting=setting, pool=pool)
        time.sleep(120) #Broker pushes machine configuration to agent per 2 mins
        for i in (1,3):
            ret = client.LaunchToDesktop(poolInfo=self.poolInfo, h264=h264, udp='mix', bypassUAG=bypassUAG)
            if not ret:
                agent.GetCallStack()
                fromTS = client.cache.get('connectTS', None)
                if fromTS:
                    # let's start from minutes
                    fromTS = fromTS[:-4]
                agent.GetTSLogs(fromTS=fromTS)
                comments = 'client login failed'
                unittest.TestCase.assertTrue(False, comments)
            client.MoveDesktopToCenter(self.pool)
            if manualLogin:
                client.ManualLogin()
            if agent.GetGUILoginUser(agentIP=agent.agentIP):
                ret = broker.HV_WaitDesktopState(self.pool, 'CONNECTED')
                kits.Verify(ret, True, 'Broker show connected status')
                client.ClickCenter()
                if not client.IsRemoteDesktopShownMss(agent.agentIP):
                    self.CheckAutofit(sizeList=sizeList)
                client.ClickCenter()
                if not agent.VerifyCDRRediction():
                    print(('CDR test failed' + '!'*80))
                client.ClickCenter()
                if not agent.VerifyUSBRedictionSimple():
                    print(('USB test failed' + '!'*80))
            else:
                comments = "Failed to Login from {} Client".format(client.os)
                kits.Verify(False, True, comments, screenshot=True)
                self.assertTrue(False, comments)
                break
            if i > 1: # Logoff directly after reconnect
                break
            if isLocked:
                agent.LockScreen(isSet=True)
                if lockedLogff: # logoff when screen is locked
                    if 'win32' in sys.platform:
                        clientOptionsImage = 'clientOptionsMenu.png'
                        clientLogoffImage = 'clientLogoffMenu.png'
                        clientOKIconImage = 'clientOKIcon.png'
                        clientOptions = os.path.join(os.getcwd(), 'images', clientOptionsImage)
                        clientLogoff = os.path.join(os.getcwd(), 'images', clientLogoffImage)
                        clientOKIcon = os.path.join(os.getcwd(), 'images', clientOKIconImage)
                        # click clientOptions
                        pyautogui.click(pyautogui.locateCenterOnScreen(clientOptions, confidence=.9, grayscale=True),
                                        interval=1)
                        time.sleep(2)
                        # click logoff menu
                        pyautogui.click(pyautogui.locateCenterOnScreen(clientLogoff, confidence=.9, grayscale=True),
                                        interval=1)
                        time.sleep(2)
                        pyautogui.click(pyautogui.locateCenterOnScreen(clientOKIcon, confidence=.9, grayscale=True),
                                        interval=1)
                        agent.CheckLockedLogoffFailedViaDebugLog()
                    else:
                        pass
                    broker.Disconnect_Logoff(pool=pool, action='logoff')
                    time.sleep(20)
                    expectedState = 'AVAILABLE'
                    ret = self.WaitSessionState(expectation=expectedState)
                    comments = 'broker show {} status'.format(expectedState)
                    kits.Verify(ret, True, comments, screenshot=True)
                    continue
            broker.Disconnect_Logoff(pool=self.pool, action='disconnect')
            time.sleep(10)
            if scenario == 'IMMEDIATELY':
                expectedState = 'AVAILABLE'
                ret = self.WaitSessionState(expectation=expectedState)
                comments = 'broker show {} status'.format(expectedState)
                kits.Verify(ret, True, comments, screenshot=True)
            else:
                expectedState = 'DISCONNECTED'
                ret = self.WaitSessionState(expectation=expectedState)
                comments = 'broker show {} status'.format(expectedState)
                kits.Verify(ret, True, comments, screenshot=True)
                if not (scenario == 'NEVER'):
                    kits.Comment("wait for auto-logoff timer {} expired".format(int(scenario) * 60))
                    kits.Wait(int(scenario) * 60)
                    expectedState = 'AVAILABLE'
                    ret = self.WaitSessionState(expectation=expectedState)
                    comments = 'broker show {} status'.format(expectedState)
                    kits.Verify(ret, True, comments, screenshot=True)
            client.KillClient()
            time.sleep(10)
            # for USB storage devices
            if 'linux' in sys.platform:
                client.KillFileExplorer()

        if not bypassUT:
            agent.SetAgentConfig('RemoteDisplay.enableNetworkIntelligence', 'TRUE')
        broker.HV_SetPool(setting="{'desktopSettings.logoffSettings.automaticLogoffPolicy': 'NEVER'}", pool=pool)
        time.sleep(120)  # agent get machine configuration update from broker per 2 mins

    def PendingConnection(self, timeout=False, bypassBSG=False, h264=True, bypassUAG=True, bypassUT=True,
                            guestOS='Ubuntu18', difUser=None, sizeList=['1024x768','1366x768','1440x900']):
        baseVM = self.poolInfo['baseVM']
        pool = self.pool = self.poolInfo['pool']
        username = self.poolInfo['user']
        password = self.poolInfo['password']

        self.Scenarios.extend(['PendingConnection'])
        testIds.assignTestCaseIds(features,self.Scenarios)

        self.PreLaunch(pool=pool, bypassBSG=bypassBSG)

        if not bypassUAG:
            self.PreUAGConfig(bypassUT=bypassUT)
        if bool(difUser):
            broker.HV_NewEntitlement(pool=pool,
                                     user=difUser,
                                     domain=self.poolInfo['domain'])
        agent.SetAgentSSO(False)
        self.RestartVM(baseVM)
        ret = self.WaitSessionState(expectation='AVAILABLE')

        # Pending session
        kits.Wait(20)
        ret = client.LaunchToDesktop(poolInfo=self.poolInfo, h264=h264, udp='mix', bypassUAG=bypassUAG)
        if not ret:
            agent.GetCallStack()
            fromTS = client.cache.get('connectTS', None)
            if fromTS:
                # let's start from minutes
                fromTS = fromTS[:-4]
            agent.GetTSLogs(fromTS=fromTS)
            comments = 'client login failed'
            unittest.TestCase.assertTrue(False, comments)
        kits.Wait(10)
        client.KillClient()
        kits.Comment("Quit at the login stage with SSO disabled.")
        expectedState = 'AVAILABLE'
        ret = self.WaitSessionState(expectation=expectedState)
        comments = 'broker show {} status'.format(expectedState)
        kits.Verify(ret, True, comments, screenshot=True)

        # Different account login failed because of pending session
        if bool(difUser):
            self.poolInfo['user'] = difUser
        if bool(difUser) and not timeout:
            client.LaunchToDesktop(poolInfo=self.poolInfo, h264=h264, udp='mix', bypassUAG=bypassUAG, expected=False)
            time.sleep(10)
            client.KillClient()
            kits.Comment('Wait for pending timer {} expired'.format(6 * 60))
            kits.Wait(6 * 60)
        # Both same and different accounts can login successfully after pending session timeout
        else:
            if timeout:
                kits.Comment('Wait for pending timer {} expired'.format(6*60))
                kits.Wait(6 * 60)

        for i in range(1,3):
            client.LaunchToDesktop(poolInfo=self.poolInfo, h264=h264, udp='mix', bypassUAG=bypassUAG)
            kits.Wait(30)
            client.ManualLogin(guestOS=guestOS, username=username, password=password)
            if agent.GetGUILoginUser(agentIP=agent.agentIP):
                ret = self.WaitSessionState()
                kits.Verify(ret, True, 'Broker show connected status')
                client.ClickCenter()
                if not client.IsRemoteDesktopShownMss(agent.agentIP):
                    self.CheckAutofit(sizeList=sizeList)
                client.ClickCenter()
                if not agent.VerifyCDRRediction():
                    print(('CDR test failed' + '!' * 80))
                client.ClickCenter()
                if not agent.VerifyUSBRedictionSimple():
                    print(('USB test failed' + '!' * 80))
            else:
                comments = "Failed to Login from {} Client".format(client.os)
                kits.Verify(False, True, comments, screenshot=True)
                self.assertTrue(False, comments)
            expectedState = 'AVAILABLE'
            broker.Disconnect_Logoff(pool=self.pool, action='logoff')
            ret = broker.HV_WaitDesktopState(self.pool, expectedState)
            comments = 'broker show {} status'.format(expectedState)
            kits.Verify(ret, True, comments, screenshot=True)
            client.KillClient()
            time.sleep(20)
            if 'linux' in sys.platform:
                client.KillFileExplorer()

        if bool(difUser):
            self.poolInfo['user'] = username
        agent.SetAgentSSO(True)
        self.RestartVM(baseVM)
        ret = self.WaitSessionState(expectation=expectedState)

    def InvalidConnection(self, manualLogin=False, pTimer=5, autoLogoffTimer=3, h264=True, difUser=None,
                          sizeList=['1024x768','1366x768','1440x900']):
        baseVM = self.poolInfo['baseVM']
        pool = self.pool = self.poolInfo['pool']
        username = self.poolInfo['user']
        password = self.poolInfo['password']
        self.Scenarios.extend(['InvalidConnection'])
        testIds.assignTestCaseIds(features,self.Scenarios)

        self.PreLaunch(pool=pool, bypassBSG=True)
        if bool(difUser):
            broker.HV_NewEntitlement(pool=pool,
                                     user=difUser,
                                     domain=self.poolInfo['domain'])
        if pTimer > 5:
            agent.SetAgentCustomConfig('PendingSessionTimeout', pTimer)
            self.RestartVM(baseVM)
        else:
            pTimer = 5
        setting= "{'desktopSettings.logoffSettings.automaticLogoffPolicy': 'AFTER', 'desktopSettings.logoffSettings.automaticLogoffMinutes': %s}" %autoLogoffTimer
        broker.HV_SetPool(setting=setting, pool=pool)
        kits.Wait(120)  # agent get machine configuration update from broker per 2 mins

        for i in (1,3):
            ret = client.LaunchToDesktop(poolInfo=self.poolInfo, h264=h264, udp='mix')
            if not ret:
                agent.GetCallStack()
                fromTS = client.cache.get('connectTS', None)
                if fromTS:
                    # let's start from minutes
                    fromTS = fromTS[:-4]
                agent.GetTSLogs(fromTS=fromTS)
                comments = 'client login failed'
                unittest.TestCase.assertTrue(False, comments)
            if manualLogin:
                client.ManualLogin(username=username, password=password)
            if agent.GetGUILoginUser(agentIP=agent.agentIP):
                ret = self.WaitSessionState()
                kits.Verify(ret, True, 'Broker show connected status')
                client.ClickCenter()
                if not client.IsRemoteDesktopShownMss(agent.agentIP):
                    self.CheckAutofit(sizeList=sizeList)
                client.ClickCenter()
                if not agent.VerifyCDRRediction():
                    print(('CDR test failed' + '!' * 80))
                client.ClickCenter()
                if not agent.VerifyUSBRedictionSimple():
                    print(('USB test failed' + '!' * 80))
            else:
                comments = "Failed to Login from {} Client".format(client.os)
                kits.Verify(False, True, comments, screenshot=True)
                self.assertTrue(False, comments)
            if i > 1:
                break

            # Invalid connection comes into Pending session
            agent.Iptables(protocol='tcp')
            client.LaunchToDesktop(poolInfo=self.poolInfo, h264=h264, udp='mix')
            kits.Wait(15)
            import lib.findPic as pic
            lpic = pic.GrabScreenLocal()
            kits.Screenshot('local desktop', lpic[0])
            for ld in lpic:
                if os.path.exists(ld):
                    os.remove(ld)
            expectedState = 'DISCONNECTED'
            ret = self.WaitSessionState(expectation=expectedState)
            comments = 'broker show {} status'.format(expectedState)
            kits.Verify(ret, True, comments, screenshot=True)
            kits.Comment("Steal session failed to trigger a pending & disconnected session")
            client.KillClient()
            agent.Iptables(protocol='tcp', action='ACCEPT')

            # Different account reconnect failed because of pending session
            if autoLogoffTimer < pTimer:
                kits.Comment('AutoLogoffTimer is less than pendingTimer. '
                             'Wait for auto-Logoff timer {} expired'.format(autoLogoffTimer * 60))
                kits.Wait(autoLogoffTimer * 60)
            else:
                kits.Comment('AutoLogoffTimer is longer than pendingTimer. '
                             'Wait for pending timer {} expired. The expected state will be AVAILABLE.'.format(pTimer * 60))
                kits.Wait(pTimer * 60)
            expectedState = 'AVAILABLE'
            ret = self.WaitSessionState(expectation=expectedState)
            comments = 'broker show {} status'.format(expectedState)
            kits.Verify(ret, True, comments, screenshot=True)
            if bool(difUser):
                self.poolInfo['user'] = difUser
                client.LaunchToDesktop(poolInfo=self.poolInfo, h264=h264, udp='mix', expected=False)
                kits.Comment("Different account login failed due to the session is not released.")
                kits.Wait(10)
                client.KillClient()
            if autoLogoffTimer < pTimer:
                kits.Comment('AutoLogoffTimer is less than pendingTimer. '
                             'Continue to wait for pending timer {} expired'.format((pTimer - autoLogoffTimer) * 60))
                kits.Wait((pTimer - autoLogoffTimer) * 60)
            else:
                kits.Comment('AutoLogoffTimer is longer than pendingTimer. '
                             'Continue to wait for auto-Logoff timer {} expired'.format((autoLogoffTimer - pTimer) * 60))
                kits.Wait((autoLogoffTimer - pTimer) * 60)
            expectedState = 'AVAILABLE'
            ret = self.WaitSessionState(expectation=expectedState)
            comments = 'broker show {} status'.format(expectedState)
            kits.Verify(ret, True, comments, screenshot=True)

            if 'linux' in sys.platform:
                client.KillFileExplorer()

        if bool(difUser):
            self.poolInfo['user'] = username
        if pTimer > 5:
            agent.SetAgentCustomConfig('PendingSessionTimeout',5)
            self.RestartVM(baseVM)
        broker.HV_SetPool(setting="{'desktopSettings.logoffSettings.automaticLogoffPolicy': 'NEVER'}", pool=pool)
        time.sleep(120)  # agent get machine configuration update from broker per 2 mins

    def BrokerTimeout(self, manualLogin=False, bTimeout=5, bypassBSG=False, h264=True, bypassUAG=True, bypassUT=True,
                       sizeList=['1024x768','1366x768','1440x900']):
        """
        :param bTimeout: N (broker session will be disconnected after N minutes, this timer will be RESET after broker reconnect)
        """
        pool = self.pool = self.poolInfo['pool']
        username = self.poolInfo['user']
        password = self.poolInfo['password']
        
        self.Scenarios.extend(['BrokerTimeout'])
        testIds.assignTestCaseIds(features,self.Scenarios)

        self.PreLaunch(pool=pool, bypassBSG=bypassBSG)
        if not bypassUAG:
            self.PreUAGConfig(bypassUT=bypassUT)
        setting = "{'generalData.clientMaxSessionTimeMinutes': %s}" %bTimeout
        broker.HV_SetGlobal(setting)
        for i in (1,3):
            ret = client.LaunchToDesktop(poolInfo=self.poolInfo, h264=h264, udp='mix', bypassUAG=bypassUAG)
            if not ret:
                agent.GetCallStack()
                fromTS = client.cache.get('connectTS', None)
                if fromTS:
                    # let's start from minutes
                    fromTS = fromTS[:-4]
                agent.GetTSLogs(fromTS=fromTS)
                comments = 'client login failed'
                unittest.TestCase.assertTrue(False, comments)
            client.MoveDesktopToCenter(self.pool)
            if manualLogin:
                client.ManualLogin(username=username, password=password)
            if agent.GetGUILoginUser(agentIP=agent.agentIP):
                ret = self.WaitSessionState(expectation='CONNECTED')
                kits.Verify(ret, True, 'Broker show connected status')
                client.ClickCenter()
                if not client.IsRemoteDesktopShownMss(agent.agentIP):
                    self.CheckAutofit(sizeList=sizeList)
                client.ClickCenter()
                if not agent.VerifyCDRRediction():
                    print(('CDR test failed' + '!'*80))
                client.ClickCenter()
                if not agent.VerifyUSBRedictionSimple():
                    print(('USB test failed' + '!'*80))
            else:
                comments = "Failed to Login from {} Client".format(client.os)
                kits.Verify(False, True, comments, screenshot=True)
                self.assertTrue(False, comments)
                break
            if i > 1:
                break
            kits.Comment("Wait for broker timer {} expired".format(10 + int(bTimeout) * 60))
            kits.Wait(10 + int(bTimeout) * 60)

            expectedState = 'DISCONNECTED'
            ret = self.WaitSessionState(expectation=expectedState)
            comments = 'broker show {} status'.format(expectedState)
            kits.Verify(ret, True, comments, screenshot=True)
            client.KillClient()
            time.sleep(10)
            # for USB storage devices
            if 'linux' in sys.platform:
                client.KillFileExplorer()
            broker.HV_SetGlobal("{'generalData.clientMaxSessionTimeMinutes': 600}")
        if not bypassUT:
            agent.SetAgentConfig('RemoteDisplay.enableNetworkIntelligence', 'TRUE')

    def BlastNetRecovery(self, manualLogin=False, scenario='client', type='native', ncEnable=True, bypassBSG=True, h264=True, bypassUAG=True, bypassUT=True):
        pool = self.pool = self.poolInfo['pool']
        self.PreLaunch(pool=pool, bypassBSG=bypassBSG)
        if not bypassUAG:
            self.PreUAGConfig(bypassUT=bypassUT)
        if not ncEnable:
            agent.SetAgentConfig('RemoteDisplay.enableNetworkContinuity', 'FALSE')
        if type == 'native':
            ret = client.LaunchToDesktop(poolInfo=self.poolInfo, h264=h264, udp='mix', bypassUAG=bypassUAG)
            if not ret:
                agent.GetCallStack()
                fromTS = client.cache.get('connectTS', None)
                if fromTS:
                    # let's start from minutes
                    fromTS = fromTS[:-4]
                agent.GetTSLogs(fromTS=fromTS)
                comments = 'client login failed'
                unittest.TestCase.assertTrue(False, comments)
            client.MoveDesktopToCenter(self.pool)
            if manualLogin:
                client.ManualLogin()
            if agent.GetGUILoginUser(agentIP=agent.agentIP):
                ret = self.WaitSessionState()
                kits.Verify(ret, True, 'Broker show connected status')
            else:
                comments = "Failed to Login from {} Client".format(client.os)
                kits.Verify(False, True, comments, screenshot=True)
                self.assertTrue(False, comments)
            if scenario == 'agent':
                self.Verify_NW_Recovery(scenario='fallback', downTime=80, step=20)
            else:
                self.Verify_NW_Recovery(scenario='client', downTime=80, step=20)
            client.KillClient()
            time.sleep(10)
            # for USB storage devices
            if 'linux' in sys.platform:
                client.KillFileExplorer()
        else:
            client.LaunchToDesktopWithWeb(pool=self.pool)
            kits.Wait(10)
            if agent.GetGUILoginUser(agentIP=agent.agentIP):
                ret = self.WaitSessionState()
                kits.Verify(ret, True, 'Broker show connected status')
                if 'win32' in sys.platform:
                    browserWindow = pyautogui.getWindow('Omnissa Horizon')
                    if browserWindow:
                        browserWindow.set_foreground()
                        print("Set Web Browser Foreground")
                client.HTMLAccess_MoveCenter()
                client.ClickCenter()
                client.RefreshWithF5()

                agent.OpenFileExplorer(agent.agentIP)
                time.sleep(2)
                agent.KillFileExplorer()
                if client.IsRemoteDesktopShownMss(agent.agentIP):
                    kits.Comment('Login succeeded from HTML Access Client')
                self.CheckAutofitWeb()
            else:
                agent.GetCallStack()
                fromTS = client.cache.get('connectTS', None)
                if fromTS:
                    # let's start from minutes
                    fromTS = fromTS[:-4]
                agent.GetTSLogs(fromTS=fromTS)
                comments = 'client login failed'
                client.LaunchToDesktopWithWeb_CloseBrowser()
                self.assertTrue(False, "Failed to Login From HTML Access Client!")
            if scenario == 'agent':
                self.Verify_NW_Recovery(scenario='fallback', downTime=80, step=20, sizeList=False)
            else:
                self.Verify_NW_Recovery(scenario='client', downTime=80, step=20, sizeList=False)
            client.LaunchToDesktopWithWeb_CloseBrowser()
            client.KillClient()
            time.sleep(10)
        if not ncEnable:
            agent.SetAgentConfig('RemoteDisplay.enableNetworkContinuity', 'TRUE')
        if not bypassUT:
            agent.SetAgentConfig('RemoteDisplay.enableNetworkIntelligence', 'TRUE')

    def ReconnectProvisionedVM(self, manualLogin=False, scenario='poweroff', bypassBSG=False, bypassUAG=True,
                          bypassUT=True, h264=True, difUser=None, sizeList=['1024x768','1366x768','1440x900']):
        baseVM = self.poolInfo['baseVM']
        pool = self.pool = self.poolInfo['pool']
        username = self.poolInfo['user']
        password = self.poolInfo['password']
        self.Scenarios.extend(['ReconnectProvisionedVM'])
        testIds.assignTestCaseIds(features,self.Scenarios)

        self.PreLaunch(pool=pool, bypassBSG=bypassBSG)
        if bool(difUser):
            broker.HV_NewEntitlement(pool=pool,
                                     user=difUser,
                                     domain=self.poolInfo['domain'])
        if not bypassUAG:
            self.PreUAGConfig(bypassUT=bypassUT)
        for idx in range(1, 3):
            if idx > 1 and bool(difUser):
                self.poolInfo['user'] = difUser
            ret = client.LaunchToDesktop(poolInfo=self.poolInfo, h264=h264, udp='mix', bypassUAG=bypassUAG)
            if not ret:
                agent.GetCallStack()
                fromTS = client.cache.get('connectTS', None)
                if fromTS:
                    # let's start from minutes
                    fromTS = fromTS[:-4]
                agent.GetTSLogs(fromTS=fromTS)
                comments = 'client login failed'
                unittest.TestCase.assertTrue(False, comments)
            client.MoveDesktopToCenter(self.pool)
            if manualLogin:
                client.ManualLogin(username=username, password=password)
            if agent.GetGUILoginUser(agentIP=agent.agentIP):
                ret = self.WaitSessionState()
                kits.Verify(ret, True, 'Broker show connected status')
                if not client.IsRemoteDesktopShownMss(agent.agentIP):
                    self.CheckAutofit(sizeList=sizeList)
                else:
                    kits.Comment('Login Succeeded in # {} try'.format(idx))
                client.ClickCenter()
                if not agent.VerifyCDRRediction():
                    print(('CDR test failed' + '!'*80))
                client.ClickCenter()
                if not agent.VerifyUSBRedictionSimple():
                    print(('USB test failed' + '!'*80))
            else:
                comments = "Failed to Login from {} Client".format(client.os)
                kits.Verify(False, True, comments, screenshot=True)
                kits.Comment('Login Failed in # {} try'.format(idx))
                self.assertTrue(False, comments)
                break
            if idx > 1:
                break
            broker.Disconnect_Logoff(pool=self.pool, action='logoff')
            time.sleep(20)
            comments = 'broker show {} status'.format('AVAILABLE')
            kits.Verify(ret, True, comments, screenshot=True)
            client.KillClient()
            # for USB storage devices
            if 'linux' in sys.platform:
                client.KillFileExplorer()

            if scenario == 'poweroff':
                expectedState = 'PROVISIONED'
                broker.VI_ShutdownVM(baseVM)
                broker.VI_WaitUntillPoweredOff(baseVM)
            else:
                expectedState = 'AVAILABLE'
                self.RestartVM(baseVM)
            ret = broker.HV_WaitDesktopState(self.pool, expectedState, 120)
            comments = 'broker show {} status'.format(ret)
            kits.Verify(ret, True, comments, screenshot=True)
            kits.Wait(30)

        if not bypassUT:
            agent.SetAgentConfig('RemoteDisplay.enableNetworkIntelligence', 'TRUE')

    def CollabIconCheck(self):
        import lib.findPic as pic
        guestOS = cached_values['agentDist']
        if "Red Hat" in guestOS and "8.10" in guestOS:
            icongood = os.path.normpath(os.path.join(os.getcwd(), 'images', 'collabIcon_rh8.png'))
        else:
            icongood = os.path.normpath(os.path.join(os.getcwd(), 'images', 'crop.png'))
        iconblur = os.path.normpath(os.path.join(os.getcwd(), 'images', 'cropblur.png'))
        exist = False
        # click icon
        for icon in [icongood, iconblur]:
            if not os.path.exists(icon):
                return exist
            #keeping .98 since there is a slight difference in 
            # this icon accross old linux distros and the new ones 
            confidence = 0.98
            for _ in range(20):
                try:
                    lpic = pic.GrabScreenLocal()
                    # check if totally black
                    if pic.IsBlack(lpic[0]):
                        for idxp in lpic:
                            if os.path.exists(idxp):
                                os.remove(idxp)
                        time.sleep(2)
                        continue
                    else:
                        # let's save the first screenshot, and delete others
                        if len(lpic) > 1:
                            for idxp in lpic[1:]:
                                if os.path.exists(idxp):
                                    os.remove(idxp)
                    imgFile = lpic[0]
                    ret, _ = pic.FindPic(icon, srcImages=[imgFile], threshold=confidence,
                                         matchAlgoIdx=1, visualize=False)
                    print(f"{('not detected', 'detected')[ret]}, between screenshot: {imgFile} and collaboration icon: {icon}")
                    os.remove(imgFile)
                    if ret:
                        exist = True
                        break
                    else:
                        confidence -= 0.01
                        print(f'confidence: {confidence}')
                        time.sleep(0.1)
                except Exception as e:
                    print(f"exception in collaboration {e}")

            if exist:
                break

        comments = 'Collaboration Icon exist'
        kits.Verify(exist, True, comments, screenshot=True)


    def CollabAddCollaborators(self, user, checkTrueSso=False, bypassBSG=False, h264=True, guestOS='Ubuntu18'):
        pool = self.pool = self.poolInfo['pool']
        self.PreLaunch(pool=pool, bypassBSG=bypassBSG)
        kits.Comment('########################- Collabration  -################################')
        client.LaunchToDesktop(poolInfo=self.poolInfo, h264=h264, udp='mix')
        client.MoveDesktopToCenter(self.pool)
        self.CheckLogin(pool, agent.agentIP)
        if checkTrueSso:
            self.CheckCertSsoData()
        agent.GetCollabLink(user, guestOS)
        kits.Comment('Add collaborators Done')
        # TODO: add successfully, add invalid user how?

    def CollabConnection(self, users, scenario='end', removeNo=1, bypassBSG=False, bypassUAG=True, guestOS='Ubuntu18'):
        pool = self.pool = self.poolInfo['pool']
        self.PreLaunch(pool=pool, bypassBSG=bypassBSG)
        kits.Comment('########################- Collaboration UI-################################')
        ret = client.LaunchToDesktop(poolInfo=self.poolInfo, bypassUAG=bypassUAG)
        if not ret:
            agent.GetCallStack()
            fromTS = client.cache.get('connectTS', None)
            if fromTS:
                # let's start from minutes
                fromTS = fromTS[:-4]
            agent.GetTSLogs(fromTS=fromTS)
            comments = 'client login failed'
            unittest.TestCase.assertTrue(False, comments)
        client.MoveDesktopToCenter(self.pool)
        if agent.GetGUILoginUser(agentIP=agent.agentIP):
            ret = broker.HV_WaitDesktopState(self.pool, 'CONNECTED')
            kits.Verify(ret, True, 'Broker show connected status')
            if not client.IsRemoteDesktopShownMss(agent.agentIP):
                kits.Comment('Desktop shows up failed')
        else:
            comments = "Failed to Login from {} Client".format(client.os)
            kits.Verify(False, True, comments, screenshot=True)
            self.assertTrue(False, comments)
        client.ClickCenter()

        collaborators=[]
        launchMethods=[]
        client_types=[]
        for key,value in users.items():
            collaborators.append(key)
            client_types.append(value.split(',')[0])
            if value.split(',')[0] in ('win', 'linux'):
                launchMethods.append(value.split(',')[1])
            else:
                launchMethods.append('')

        client_uri, webclient_uri, collabId = agent.GetCollabLink(collaborators, guestOS)
        kits.Comment(f'Native Client URI: {client_uri}' )
        kits.Comment(f'Web Client URI: {webclient_uri}' )
        if not client_uri or not webclient_uri:
            kits.Verify(False, True, "Add collaborators Failed", screenshot=True)
            return

        for i in range(len(collaborators)):
            kits.Comment(f'Launch Shadow Session on Client:{shadowclients[i].ip} with User:{collaborators[i]}' )
            shadowclients[i].LaunchShadowSessionByMethod(self.poolInfo, agent.agentIP, client_types[i],
                                               launchMethods[i], collabId, collaborators[i], client_uri)
            isConnected, isInput = agent.GetCollaboratorStatusViaLog(collaborators[i])
            kits.Verify(isConnected, True, 'Collaborator is Connected.')
            kits.Verify(isInput, False, 'Collaborator has no input control.')

        if scenario == 'end':
            agent.checkEndCollaborators(guestOS)
            for i in range(len(client_types)):
                shadowclients[i].KillShadowClient(client_types[i])
                kits.Comment('Close shadow session {} {}'.format(collaborators[i], client_types[i]))
                isConnected, isInput = agent.GetCollaboratorStatusViaLog(collaborators[i])
            if not isConnected:
                kits.Verify(isConnected, False, 'Collaborator is DISCONNECTED because collaboration is Ended.')
        elif scenario == 'remove':
            agent.checkRemoveCollaborators(removeNo, guestOS)
            for i in range(removeNo):
                shadowclients[i].KillShadowClient(client_types[i])
                kits.Comment('Close shadow session {} {}'.format(collaborators[i], client_types[i]))
                isConnected, isInput = agent.GetCollaboratorStatusViaLog(collaborators[i])
                kits.Verify(isConnected, False, 'Collaborator is DISCONNECTED because collaborator is Removed.')

    def checkDisconnectPrimary(self, CmdHandler, client_types, shadowclients, pool):
        '''
        Disconnect primary session
        '''
        CmdHandler.end()
        CmdHandler.remote_shutdown()
        broker.Disconnect_Logoff(pool=self.pool)
        kits.Wait(30)
        expectedState = 'DISCONNECTED'
        ret = broker.HV_WaitDesktopState(pool, expectedState, 360)
        comments = 'broker show {} status'.format(expectedState)
        kits.Verify(ret, True, comments)
        for i in range(len(client_types)):
            shadowclients[i].KillShadowClient(client_types[i])
        kits.Wait(30)
        ret = agent.CheckCollaborationStatusViaLog()
        kits.Verify(ret, True, 'Disconnect Primary Session: Collaboration is terminated.')

    def checkAddCollborators(self, users, CmdHandler):
        '''
        Invite collaborators
        '''
        collaborators = []
        launchMethods = []
        client_types = []
        for key, value in users.items():
            collaborators.append(key)
            client_types.append(value.split(',')[0])
            if value.split(',')[0] in ('win', 'linux'):
                launchMethods.append(value.split(',')[1])
            else:
                launchMethods.append('')
        for collaborator in collaborators:
            CmdHandler.lookup(collaborator)
            kits.Comment('Add collaborator {}'.format(collaborator))
        CmdHandler.invite()
        kits.Comment('Invite collaborators.')

        return collaborators, client_types, launchMethods

    def checkEndCollaboration(self, CmdHandler, client_types, shadowclients):
        '''
        End collaboration
        '''
        CmdHandler.end()
        CmdHandler.remote_shutdown()
        kits.Comment("Quit collabScript.py")
        for i in range(len(client_types)):
            shadowclients[i].KillShadowClient(client_types[i])
        kits.Comment("Shutdown all shadow clients")
        kits.Wait(120)
        ret = agent.CheckCollaborationStatusViaLog()
        kits.Verify(ret, True, 'End Collaboration: Collaboration is terminated.')

    def checkRemoveShadow(self, CmdHandler, client_types, shadowclients, removeUsers):
        '''
        Remove Collaborators
        '''
        for collaborator in removeUsers:
            CmdHandler.remove(collaborator)
            kits.Comment(f"Remove Collaborator {collaborator} via API")
            kits.Wait(30)
            isConnected, isInput = agent.GetCollaboratorStatusViaLog(collaborator, minBefore=2)
            kits.Verify(not isConnected, True, f'Remove Collaborator {collaborator}.')
        for i in range(len(client_types)):
            shadowclients[i].KillShadowClient(client_types[i])

    def CollabConnectionbyAPI(self, users, removeUsers=None, times=2, scenario='end', bypassBSG=False, h264=True):
        pool = self.pool = self.poolInfo['pool']

        # safegard here
        for idx in shadowclients:
            idx.KillClient()
        self.PreLaunch(pool=pool, bypassBSG=bypassBSG)
        kits.Comment('########################- Collabration API -################################')
        from lib.collab import Collab_CmdHandler
        CmdHandler = Collab_CmdHandler('%s:8000' % agent.agentIP)
        print(f"CmdHandler: {CmdHandler}")
        for idx in range(1, times + 1):
            ret = client.LaunchToDesktop(poolInfo=self.poolInfo, h264=h264, udp='mix')
            if not ret:
                agent.GetCallStack()
                fromTS = client.cache.get('connectTS', None)
                if fromTS:
                    # let's start from minutes
                    fromTS = fromTS[:-4]
                agent.GetTSLogs(fromTS=fromTS)
                comments = 'client login failed'
                unittest.TestCase.assertTrue(False, comments)
            client.MoveDesktopToCenter(self.pool)
            self.CheckLogin(pool, agent.agentIP)
            agent.endCollabServer(CmdHandler)
            time.sleep(10)
            agent.launchCollabServer(cached_values['agentBuildNum'])
            
            from lib.collab import Collab_CmdHandler
            collaborators, client_types, launchMethods = self.checkAddCollborators(users, CmdHandler)
            print(f"collaborators:{collaborators}, client types:{client_types}, launch methods: {launchMethods}")
            # we launched collabScript.py via root
            collabId = agent.GetShadowSessionIdViaLog('root')
            kits.Comment(f"collabId: {collabId}")
            if not collabId:
                CmdHandler.end()
                CmdHandler.remote_shutdown()
                return False
            client_uri = f'https://{self.poolInfo["broker"]}/portal/webclient/index.html?collabSessionId={collabId}'
            kits.Comment(f'Shadow Session Id is {collabId}, and web client uri is {client_uri}.')
            kits.Wait(15)
            for i in range(len(collaborators)):
                kits.Comment(f'Launch Shadow Session on Client:{shadowclients[i].ip} with User:{collaborators[i]}')
                shadowclients[i].LaunchShadowSessionByMethod(self.poolInfo, agent.agentIP, client_types[i],
                                                   launchMethods[i], collabId, collaborators[i], client_uri)
                kits.Wait(15)
                isConnected, isInput = agent.GetCollaboratorStatusViaLog(collaborators[i])
                kits.Verify(isConnected, True, f'Collaborator {collaborators[i]} is Connected.')

            CmdHandler.allowInput(collaborators[0])
            kits.Comment("Grant input control to {}".format(collaborators[0]))
            kits.Wait(15)
            isConnected, isInput = agent.GetCollaboratorStatusViaLog(collaborators[0])
            kits.Verify(isConnected, True, f'Collaborator {collaborators[0]} is Connected.')
            kits.Verify(isInput, True, f'Collaborator {collaborators[0]} has input control.')

            if scenario == 'end':
                self.checkEndCollaboration(CmdHandler, client_types, shadowclients)
                break
            elif scenario == 'remove':
                kits.Wait(30)
                self.checkRemoveShadow(CmdHandler, client_types, shadowclients, removeUsers)
                break
            elif scenario == 'disconnect':
                self.checkDisconnectPrimary(CmdHandler, client_types, shadowclients, self.pool)
                client.KillClient()
                kits.Wait(10)
        agent.endCollabServer(CmdHandler)

    def CollabNetworkRecovery(self, users, stop=180, step=120, ncEnable=True, disconn_vmType='primary', input='primary', bypassBSG=False):
        pool = self.pool = self.poolInfo['pool']
        # safegard here
        for idx in shadowclients:
            idx.KillClient()

        if not ncEnable:
            agent.SetAgentConfig('RemoteDisplay.enableNetworkContinuity', 'FALSE')
        self.PreLaunch(pool=pool, bypassBSG=bypassBSG)
        kits.Comment('########################- Collabration Network Recovery -################################')
        if disconn_vmType == 'primary':
            remoteclients[0].RemoteClient_LaunchToDesktopRpyc(self.pool, userName=self.poolInfo['user'], udp='mix')
            self.CheckLogin(pool, agent.agentIP, clientHost=remoteclients[0].ip)
        else:
            ret = client.LaunchToDesktop(poolInfo=self.poolInfo, udp='mix')
            if not ret:
                agent.GetCallStack()
                fromTS = client.cache.get('connectTS', None)
                if fromTS:
                    # let's start from minutes
                    fromTS = fromTS[:-4]
                agent.GetTSLogs(fromTS=fromTS)
                comments = 'Failed to Login with Network recovery in Collaboration'
                unittest.TestCase.assertTrue(False, comments)
            client.MoveDesktopToCenter(self.pool)
            self.CheckLogin(pool, agent.agentIP)
        from lib.collab import Collab_CmdHandler
        CmdHandler = Collab_CmdHandler('%s:8000' % agent.agentIP)
        agent.endCollabServer(CmdHandler)
        time.sleep(10)
        agent.launchCollabServer(cached_values['agentBuildNum'])
        time.sleep(20)
        collaborators, client_types, launchMethods = self.checkAddCollborators(users, CmdHandler)
        # we launched collabScript.py via root
        collabId = agent.GetShadowSessionIdViaLog('root')
        brokerip = self.poolInfo['broker']
        client_uri = f'https://{brokerip}/portal/webclient/index.html?collabSessionId={collabId}'
        kits.Comment(f'Shadow Session Id is {collabId}, and web client uri is {client_uri}.')
        for i in range(len(collaborators)):
            kits.Comment(f'Launch Shadow Session on Client:{shadowclients[i].ip} with User:{collaborators[i]}')
            shadowclients[i].LaunchShadowSessionByMethod(self.poolInfo, agent.agentIP, client_types[i],
                                               launchMethods[i], collabId, collaborators[i], client_uri)
            isConnected, isInput = agent.GetCollaboratorStatusViaLog(collaborators[i])
            kits.Verify(isConnected, True, f'Collaborator {collaborators[i]} is Connected.')
        inputGranted = False

        # disconnect win client NIC
        for dt in range(50, stop ,step):
            if input =='primary':
                if disconn_vmType =='primary':
                    remoteclients[0].connect()
                    remoteclients[0].SleepClientNetwork(dt)
                    kits.Wait(dt)
                    kits.Comment(f"Primary has input control and disconnect Primary {dt} seconds.")
                else:
                    shadowclients[0].connect()
                    shadowclients[0].SleepClientNetwork(dt)
                    kits.Wait(dt)
                    kits.Comment(f"Primary has input control and disconnect shadow {dt} seconds.")
                if dt > 120 and disconn_vmType =='primary':
                    kits.Wait(15)
                    ret = agent.CheckCollaborationStatusViaLog()
                    kits.Verify(ret, True, 'Primary Session disconnected: Collaboration is terminated.')
                    break
                else:
                    for i in range(len(collaborators)):
                        isConnected, isInput = agent.GetCollaboratorStatusViaLog(collaborators[i])
                        if dt > 120 and i == 0:
                            kits.Verify(isConnected, False, f'Collaborator {collaborators[i]} is Connected.')
                        else:
                            kits.Verify(isConnected, True, f'Collaborator {collaborators[i]} is Connected.')
                        #kits.Verify(isInput, False, f'Collaborator {collaborators[i]} has input control.')
            else:
                if not inputGranted:
                    CmdHandler.allowInput(collaborators[0])
                    kits.Comment("Grant input control to {}".format(collaborators[0]))
                    kits.Wait(5)
                    inputGranted = True
                    isConnected, isInput = agent.GetCollaboratorStatusViaLog(collaborators[0])
                    kits.Verify(isConnected, True, f'Collaborator {collaborators[0]} is Connected.')
                    kits.Verify(isInput, True, f'Collaborator {collaborators[0]} has input control.')
                if disconn_vmType == 'primary':
                    remoteclients[0].connect()
                    remoteclients[0].SleepClientNetwork(dt)
                    kits.Wait(dt)
                    kits.Comment(f"Shadow has input control and disconnect Primary {dt} seconds.")
                    if dt < 120:
                        isConnected, isInput = agent.GetCollaboratorStatusViaLog(collaborators[0])
                        kits.Verify(isConnected, True, f'Collaborator {collaborators[0]} is Connected.')
                        kits.Verify(isInput, True, f'Collaborator {collaborators[0]} has input control.')
                    else:
                        kits.Wait(15)
                        ret = agent.CheckCollaborationStatusViaLog()
                        kits.Verify(ret, True, 'Primary Session disconnected: Collaboration is terminated.')
                        inputGranted = False
                else:
                    shadowclients[0].connect()
                    shadowclients[0].SleepClientNetwork(dt)
                    kits.Wait(dt)
                    kits.Comment(f"Shadow has input control and disconnect active shadow {dt} seconds.")
                    if dt < 120:
                        isConnected, isInput = agent.GetCollaboratorStatusViaLog(collaborators[0])
                        kits.Verify(isConnected, True, f'Collaborator {collaborators[0]} is Connected.')
                        kits.Verify(isInput, True, f'Collaborator {collaborators[0]} has input control.')
                    else:
                        isConnected, isInput = agent.GetCollaboratorStatusViaLog(collaborators[0])
                        kits.Verify(isConnected, False, f'Collaborator {collaborators[0]} is Connected.')
                        #kits.Verify(isInput, False, f'Collaborator {collaborators[0]} has input control.')
                        inputGranted = False
                    if len(collaborators) > 1:
                        if inputGranted:
                            CmdHandler.disallowInput(collaborators[0])
                            kits.Comment("Get back input control to {}".format(collaborators[0]))
                            kits.Wait(5)
                        CmdHandler.allowInput(collaborators[1])
                        kits.Comment("Grant input control to {}".format(collaborators[1]))
                        kits.Wait(5)
                        isConnected, isInput = agent.GetCollaboratorStatusViaLog(collaborators[1])
                        kits.Verify(isConnected, True, f'Collaborator {collaborators[1]} is Connected.')
                        kits.Verify(isInput, True, f'Collaborator {collaborators[1]} has input control.')
                        shadowclients[0].connect()
                        shadowclients[0].SleepClientNetwork(dt)
                        kits.Wait(dt)
                        kits.Comment(f"Shadow has input control and disconnect passive shadow {dt} seconds.")
                        isConnected, isInput = agent.GetCollaboratorStatusViaLog(collaborators[1])
                        kits.Verify(isConnected, True, f'Collaborator {collaborators[1]} is Connected.')
                        kits.Verify(isInput, True, f'Collaborator {collaborators[1]} has input control.')
                        CmdHandler.disallowInput(collaborators[1])
                        kits.Comment("Get back input control to {}".format(collaborators[1]))
                        kits.Wait(5)
                        inputGranted = False

        for i in range(len(client_types)):
            shadowclients[i].KillShadowClient(client_types[i])
        if disconn_vmType == 'primary':
            remoteclients[0].RemoteClient_KillClient()
        if not ncEnable:
            agent.SetAgentConfig('RemoteDisplay.enableNetworkContinuity', 'TRUE')

    def Multimon(self, h264=True, hwEncoding=False, bypassBSG=False, bypassUAG=True, remote=False,
                 repeat=3, scenario='disconnect', primaryIdx=0, totalMonitors=4,
                 resList=['0+0+2048x1536', '2048+0+2048x1536', '0+1536+2048x1536', '2048+1536+2048x1536']):
        pool = self.pool = self.poolInfo['pool']
        h264 = h264
        
        self.Scenarios.extend(['Multimon'])
        testIds.assignTestCaseIds(features,self.Scenarios)

        enableH264 = ('disabled', 'enabled')[h264]
        self.PreLaunch(pool=pool, bypassBSG=bypassBSG)
        kits.Comment(f'########################- Multimon H264 {enableH264} -################################')
        for idx in range(1, repeat + 1):
            if remote:
                if not len(remoteclients):
                    return False
                for idx in remoteclients:
                    idx.connect()
                    idx.SetMultimon(primaryIdx=primaryIdx, totalMonitors=totalMonitors, resList=resList)
                    time.sleep(2)
                    idx.RemoteClient_LaunchToDesktopRpyc(pool, desktopSize='multimonitor', h264=h264, udp='mix')
                    self.CheckLogin(pool, agent.agentIP, clientHost=idx.ip)
                    agentMonInfos = agent.GetResInAgent(agent.agentIP)
                    agentResList = [f"{x[3].replace(',', '+')}+{x[1]}" for x in agentMonInfos]
                    for x in resList:
                        ret = x in agentResList
                        kits.Verify(ret, True, f"monitor {x} found in agent topology", screenshot=False)

                    h264detected, udp = agent.CheckH264UDPViaLog(hwEncoding=hwEncoding)
                    kits.Verify(h264detected, h264, 'H264 Enabled')
                    kits.Comment('UDPv2 Detected : {}'.format(udp))
            else:
                client.SetMultimon(primaryIdx=primaryIdx, totalMonitors=totalMonitors, resList=resList)
                time.sleep(2)
                # client.LaunchToDesktop(poolInfo=self.poolInfo, h264=h264, udp='mix')
                client.LaunchToDesktop(self.poolInfo, desktopSize='all', h264=h264, udp='mix')

                self.CheckLogin(pool, agent.agentIP, threshold=0.6)
                agentMonInfos = agent.GetResInAgent(agent.agentIP)
                agentResList = [f"{x[3].replace(',', '+')}+{x[1]}" for x in agentMonInfos]
                for x in resList:
                    ret = x in agentResList
                    kits.Verify(ret, True, f"monitor {x} found in agent topology", screenshot=False)

                h264detected, udp = agent.CheckH264UDPViaLog(hwEncoding=hwEncoding)
                kits.Verify(h264detected, h264, 'H264 Enabled')
                kits.Comment('UDPv2 Detected : {}'.format(udp))

            expectedState = 'DISCONNECTED'
            if scenario == 'disconnect':
                broker.Disconnect_Logoff(pool=self.pool)
                time.sleep(10)
            else:
                broker.Disconnect_Logoff(pool=self.pool, action='logoff')
                time.sleep(20)
                expectedState = 'AVAILABLE'
            ret = broker.HV_WaitDesktopState(self.pool, expectedState, 360)
            comments = 'broker show {} status'.format(expectedState)
            kits.Verify(ret, True, comments, screenshot=True)
            if remote:
                for idx in remoteclients:
                    idx.RemoteClient_KillClient()
                    idx.ResetMonitor(tool='vmtool')
            else:
                # client.KillClient()
                client.ResetMonitor(tool='vmtool')
            time.sleep(10)

    def Multimon4K(self, h264=True, hwEncoding=False, bypassBSG=False, bypassUAG=True, remote=False,
                 repeat=3, scenario='disconnect', primaryIdx=0, totalMonitors=4, is4k=True,
                 resList=['0+0+3840x2160', '3840+0+3840x2160', '0+2160+3840x2160', '3840+2160+3840x2160']):
        pool = self.pool = self.poolInfo['pool']
        h264 = h264
        self.Scenarios.extend(['Multimon4K'])
        testIds.assignTestCaseIds(features,self.Scenarios)

        enableH264 = ('disabled', 'enabled')[h264]
        self.PreLaunch(pool=pool, bypassBSG=bypassBSG)

        for idx in range(1, repeat + 1):
            kits.Comment(f'########################- vGPU Multimon H264 {enableH264} -################################')
            client.SetMultimon(primaryIdx=primaryIdx, is4k=True, totalMonitors=totalMonitors, resList=resList)
            time.sleep(2)
            ret = client.LaunchToDesktop(self.poolInfo, desktopSize='all', h264=h264, udp='mix')
            if not ret:
                agent.GetCallStack()
                fromTS = client.cache.get('connectTS', None)
                if fromTS:
                    # let's start from minutes
                    fromTS = fromTS[:-4]
                agent.GetTSLogs(fromTS=fromTS)
                comments = 'client login failed'
                unittest.TestCase.assertTrue(False, comments)

            self.CheckLogin(pool, agent.agentIP)
            agentMonInfos = agent.GetResInAgent(agent.agentIP)
            agentResList = [f"{x[3].replace(',', '+')}+{x[1]}" for x in agentMonInfos]
            for x in resList:
                ret = x in agentResList
                kits.Verify(ret, True, f"monitor {x} found in agent topology", screenshot=False)

            h264detected, udp = agent.CheckH264UDPViaLog(hwEncoding=hwEncoding)
            kits.Verify(h264detected, h264, 'H264 Enabled')
            kits.Comment('UDPv2 Detected : {}'.format(udp))

            ret = False
            comments = ''
            expectedState = 'DISCONNECTED'
            if not self.poolInfo.get('VADC', False):
                if scenario == 'disconnect':
                    broker.Disconnect_Logoff(pool=self.pool)
                    time.sleep(10)
                else:
                    broker.Disconnect_Logoff(pool=self.pool, action='logoff')
                    time.sleep(20)
                    expectedState = 'AVAILABLE'
                ret = self.WaitSessionState(expectation=expectedState)
                comments = 'broker show {} status'.format(expectedState)
            else:
                if scenario == 'disconnect':
                    vadc.Disconnect_Logoff(self.poolInfo['user'], action='disconnect')
                else:
                    vadc.Disconnect_Logoff(self.poolInfo['user'], action='logoff')
                    expectedState = 'AVAILABLE'
                waitTime = 240
                state = 'unknown'
                while waitTime > 0 :
                    state, _ = vadc.GetSessionInfo()
                    print(f'VADC status: {state}')
                    ret = state == expectedState
                    if not ret:
                        time.sleep(1)
                        waitTime -= 1
                        continue
                    else:
                        break
                comments = 'VADC show {} status'.format(state)
            kits.Verify(ret, True, comments)
            if scenario == 'disconnect':
                time.sleep(5)
                lockStatus = agent.GetScreenLockStatus(agent.agentIP)
                kits.Comment(f"lockStatus after disconnect from broker:\n{lockStatus}")
                if len(lockStatus):
                    lockedDesktop = lockStatus.count('screen locked') > 0
                    if not lockedDesktop:
                        kits.Warning("desktop locked successfully after disconnect")

            client.KillClient()
            client.ResetMonitor(tool='vmtool')

            time.sleep(10)

    def DEMSmartPolicy(self, poolInfo):
        """
        Disable clipboard/usb/cdr in config file and then enable them in DEM management.
        @Input:
          -poolInfo: information of POOL
        """
        pool = self.pool = self.poolInfo['pool']
        baseVM = poolInfo['baseVM']
        self.Scenarios.extend(['DEMSmartPolicy'])
        testIds.assignTestCaseIds(features,self.Scenarios)

        ret, agent.agentIP = broker.VI_GetGuestIP(baseVM)
        if poolInfo['DEMEnable'].lower() == 'true':
        # Disable Clipboard and USB, set CDRpermission read-only in config file.
            agent.SetAgentCustomConfig('DEMEnable', poolInfo['DEMEnable'])
            agent.SetAgentCustomConfig('DEMNetworkPath', poolInfo['DEMNetworkPath'])
            agent.SetAgentConfig('Clipboard.Direction', 0)
            agent.SetAgentConfig('cdrserver.permissions', 'R')
            agent.SetAgentCustomConfig('USBEnable', 'FALSE')

        # Create a new Policy of Clipboard/USB/CDR AllowAll in DEM management console.
        policyName = "enableAll"
        client.SetDEMConfig(poolInfo['DEMNetworkPath'], policyName)
        self.RestartVM(baseVM, cold=True)
        time.sleep(10)
        broker.HV_WaitDesktopState(pool, "AVAILABLE", 480)
        mountCmd = 'mount -t cifs {} /var/omnissa/UEM -o username={},password={}'.format(poolInfo['DEMNetworkPath'], poolInfo['user'], poolInfo['password'])
        agent.GetOutputFromAgentDirectly(cmd=mountCmd, agentIP=agent.agentIP, rootUser=True)
        self.PreLaunch(pool=pool)
        kits.Comment('########################- DEM Horizon Smart Policies -################################')
        ret = client.LaunchToDesktop(poolInfo=self.poolInfo)
        if not ret:
            agent.GetCallStack()
            fromTS = client.cache.get('connectTS', None)
            if fromTS:
                # let's start from minutes
                fromTS = fromTS[:-4]
            agent.GetTSLogs(fromTS=fromTS)
            comments = 'client login failed'
            unittest.TestCase.assertTrue(False, comments)
        client.MoveDesktopToCenter(self.pool)
        self.CheckLogin(pool, agent.agentIP)
        self.CheckRedirection()
        agent.GetOutputFromAgentDirectly(cmd="umount /var/omnissa/UEM", agentIP=agent.agentIP, rootUser=True)


    def GetUserRpycPortMapping(self, userXorgInfo):
        tmp = {a:b for a, b in zip(userXorgInfo[::2], userXorgInfo[1::2])}
        userPort = {a:int(b)+18812 for a, b in tmp.items()}
        return userPort

    def GetUserRpycInfo(self, users, remoteclients):
        userXorgInfo = agent.GetGUIUserXorgNum(agent.agentIP)
        print(f"Logged on Users and its Display Number:\n{userXorgInfo}")
        xorgList = userXorgInfo.split()
        print(f"\n{xorgList}")
        rpycPorts = self.GetUserRpycPortMapping(xorgList)
        loginUsers = []
        outs = agent.GetGUILoginUser(agent.agentIP)
        print(f"Agent Logged on Users: \n{outs}")
        if outs:
            loginUsers = outs.split()

        for user, idx in zip(users, remoteclients):
            if user in loginUsers:
                idx.user = user
                idx.rpycPort = rpycPorts[user]

    def HelpdeskWorking(self, manualLogin=False):
        self.Scenarios.extend(['HelpdeskWorking'])
        testIds.assignTestCaseIds(features,self.Scenarios)

        pool = self.pool = self.poolInfo['pool']
        self.PreLaunch(pool=pool, bypassBSG=True, rebVhci=False)
        ret = client.LaunchToDesktop(poolInfo=self.poolInfo, h264=True, udp='mix')
        if not ret:
            agent.GetCallStack()
            fromTS = client.cache.get('connectTS', None)
            if fromTS:
                # let's start from minutes
                fromTS = fromTS[:-4]
            agent.GetTSLogs(fromTS=fromTS)
            comments = 'client login failed'
            unittest.TestCase.assertTrue(False, comments)
        client.MoveDesktopToCenter(self.pool)
        if manualLogin:
            client.ManualLogin()
        self.CheckLogin(pool, agent.agentIP)

        kits.Comment('##### Verify Helpdesk Working#####')

        ret, message = broker.Execute_Rest("prepare")
        kits.Verify(ret, True, message)

    def HelpdeskBlast(self):
        self.Scenarios.extend(['HelpdeskBlast'])
        testIds.assignTestCaseIds(features,self.Scenarios)


        kits.Comment('##### Verify Helpdesk Blast#####')
        ret, message = broker.Execute_Rest("blast")

        # Need stop after Verify
        kits.Verify(ret, True, message)

        # Verify blast metrics
        blast = message["blastPerformanceData"]
        kits.Verify(blast["estimatedFPS"] >= 0, True, "Verify estimatedFPS")
        kits.Verify(blast["estimatedRTT"] >= 0, True, "Verify estimatedRTT")
        kits.Verify(blast["estimatedBandwidth"] > 1000, True, "Verify estimatedBandwidth")
        kits.Verify(blast["blastImagingStatistics"]["bytesReceived"] > 100, True,
                    "Verify blastImagingStatistics->bytesReceived")
        kits.Verify(blast["blastImagingStatistics"]["bytesTransmitted"] > 1000, True,
                    "Verify  blastImagingStatistics->bytesTransmitted")
        kits.Verify(blast["blastAudioStatistics"]["bytesReceived"] >= 0, True,
                    "Verify blastAudioStatistics->bytesReceived")
        kits.Verify(blast["blastAudioStatistics"]["bytesTransmitted"] >= 0, True,
                    "Verify blastAudioStatistics->bytesTransmitted")
        kits.Verify(blast["blastCDRStatistics"]["bytesReceived"] > 100, True,
                    "Verify blastCDRStatistics->bytesReceived")
        kits.Verify(blast["blastCDRStatistics"]["bytesTransmitted"] > 100, True,
                    "Verify blastCDRStatistics->bytesTransmitted")

    def HelpdeskClient(self):
        kits.Comment('##### Verify Helpdesk Client Info#####')
        ret, message = broker.Execute_Rest("client")

        # Need stop after Verify
        kits.Verify(ret, True, message)

        # Verify client info
        client = message
        kits.Verify(len(client["remoteIpAddress"]) >= 7, True, "Verify remoteIpAddress")
        kits.Verify(len(client["ipAddress"]) >= 7, True, "Verify ipAddress")
        kits.Verify(len(client["loggedOnDomainName"]) >= 3, True, "Verify loggedOnDomainName")
        kits.Verify(client["loggedOnUserName"] == "Administrator", True, "Verify loggedOnUserName")
        kits.Verify(len(client["macAddress"]) > 10, True, "Verify macAddress")
        kits.Verify(len(client["domainName"]) >= 3, True, "Verify domainName")
        kits.Verify(len(client["machineName"]) >= 3, True, "Verify machineName")
        kits.Verify(client["type"] == "Windows", True, "Verify type")
        kits.Verify(len(client["brokerDnsName"]) >= 7, True, "Verify brokerDnsName")

    def HelpdeskProcess(self):
        self.Scenarios.extend(['HelpdeskProcess'])
        testIds.assignTestCaseIds(features,self.Scenarios)

        kits.Comment('##### Verify Helpdesk Process Info#####')
        ret, message = broker.Execute_Rest("process")

        # Need stop after Verify
        kits.Verify(ret, True, message[0])

        # Verify client info
        process = message[0]
        kits.Verify(process["cpu"] >= 0, True, "Verify cpu")
        kits.Verify(process["memory"] >= 0, True, "Verify memory")
        kits.Verify(process["disk"] >= 0, True, "Verify disk")
        kits.Verify(process["processId"] >= 1, True, "Verify pid")
        kits.Verify(len(process["name"]) > 1, True, "Verify pname")
        kits.Verify(len(process["userName"]) > 1, True, "Verify username")

    def HelpdeskHistory(self):
        self.Scenarios.extend(['HelpdeskHistory'])
        testIds.assignTestCaseIds(features,self.Scenarios)

        kits.Comment('##### Verify Helpdesk Historical metrics####')
        ret, message = broker.Execute_Rest("history")

        # Need stop after Verify
        kits.Verify(ret, True, message)

        # Verify client info
        history = message[0]
        kits.Verify(history["overallCpu"] >= 0, True, "Verify overallCpu")
        kits.Verify(history["overallMemory"] > 0, True, "Verify overallMemory")
        kits.Verify(history["diskReadIops"] >= 0, True, "Verify diskReadIops")
        kits.Verify(history["diskWriteIops"] >= 0, True, "Verify diskWriteIops")
        kits.Verify(history["diskLatency"] >= 0, True, "Verify diskLatency")

    def HelpdeskDisconnect(self):
        self.Scenarios.extend(['HelpdeskDisconnect'])
        testIds.assignTestCaseIds(features,self.Scenarios)

        # Verify client info shall return failure
        ret, message = broker.Execute_Rest("client")
        kits.Verify(message == "viewclient/get failure", True, "Verify client info")

    def HelpdeskReconnect(self):
        self.Scenarios.extend(['HelpdeskReconnect'])
        testIds.assignTestCaseIds(features,self.Scenarios)

        pool = self.pool = self.poolInfo['pool']
        self.PreLaunch(pool=pool, bypassBSG=True, rebVhci=False)
        ret = client.LaunchToDesktop(poolInfo=self.poolInfo, h264=True, udp='mix')
        if not ret:
            agent.GetCallStack()
            fromTS = client.cache.get('connectTS', None)
            if fromTS:
                # let's start from minutes
                fromTS = fromTS[:-4]
            agent.GetTSLogs(fromTS=fromTS)
            comments = 'client login failed'
            unittest.TestCase.assertTrue(False, comments)
        client.MoveDesktopToCenter(self.pool)
        self.CheckLogin(pool, agent.agentIP)

        kits.Comment('##### Verify Helpdesk Reconnect#####')
        self.HelpdeskClient()
        self.HelpdeskBlast()

    def MultiSession(self, h264=True, users=[f'lvdi-p{x:03}' for x in range(1, 51)],
                     interval=5, repeat=2, scenario='disconnect', vmName='vadc-rh8ms',
                     desktopSize='1440x900'):
        pool = self.poolInfo['pool']
        # safe guard here, kill remote client before launch another one
        self.Scenarios.extend(['multisession'])
        testIds.assignTestCaseIds(features,self.Scenarios)
        ShutdownRemoteClients(remoteclients)

        time.sleep(2)
        enableH264 = ('disabled', 'enabled')[h264]
        self.PreLaunch(pool=self.poolInfo['pool'])

        kits.Comment(f'########################- Multiple Session H264 {enableH264} -################################')

        wkthreads = []
        dpthreads = []
        for it in range(1, repeat + 1):
            wkthreads.clear()
            dpthreads.clear()
            for user, idx in zip(users, remoteclients):
                p = Thread(target=MakeClientLaunch, args=(pool, desktopSize, h264, 'mix', idx, user))
                wkthreads.append(p)
                idx.user = user
            for p in wkthreads:
                if not p.is_alive():
                    p.start()
                if interval:
                    time.sleep(interval)
            # check display
            loginUsers = []
            timeouts = len(users) * interval + 300
            while timeouts > 0:
                outs = agent.GetGUILoginUser(agent.agentIP)
                print(f"Agent Logged on Users: \n{outs}")
                if outs:
                    loginUsers = outs.split()
                    if len(loginUsers) == len(users):
                        break
                time.sleep(2)
                timeouts -= 2
                continue
            print(f"Logged on Users:\n{loginUsers}")
            # check if all users login successfully
            ret = len(users) == len(loginUsers)
            # kits.Verify(ret, True, f"All Users login to VADC Desktop successfully in {it} iteration", screenshot=False)
            if len(loginUsers) == 0:
                kits.Verify(False, True, "None users login successfully!")
                return False
            timeouts = 90
            while timeouts > 0:
                sessionInfo = vadc.ListSessionInfo()
                print(f'vadc session info:\n{sessionInfo}')
                errSession = list(filter(lambda x: x[1] != 'CONNECTED', sessionInfo))
                ret = len(errSession) == 0
                if len(errSession):
                    print(f'error state:\n {errSession}')
                    time.sleep(2)
                    timeouts -= 2
                    continue
                else:
                    break
            # kits.Verify(ret, True, f"All Users connected to VADC Desktop successfully in {it} iteration", screenshot=False)
            self.GetUserRpycInfo(users, remoteclients)
            for idx in remoteclients:
                if not hasattr(idx, 'user'):
                    continue
                if idx.user in loginUsers:
                    p = Thread(target=CheckDesktop_Remote, args=(idx, idx.ip, agent.agentIP, idx.rpycPort))
                    p.start()
                    print(f'########################- check display for {idx.user} on client {idx.node}({idx.ip}) -#####################')
                    dpthreads.append(p)
                [x.join() for x in dpthreads]

                if scenario == 'disconnect':
                    vadc.Disconnect_Logoff(idx.user, action='disconnect')
                else:
                    vadc.Disconnect_Logoff(idx.user, action='logoff')

            ShutdownRemoteClients(remoteclients)
            kits.Wait(5)
            sessionInfo = vadc.ListSessionInfo()
            print(f'vadc session info:\n{sessionInfo}')

    def MultiApps(self, h264=True, users=[f'z{x:03}' for x in range(1, 10)],
                  interval=5, repeat=2, scenario='disconnect', appName='Terminal'):
        # safe guard here, kill remote client before launch another one
        ShutdownRemoteClients(remoteclients)

        time.sleep(2)
        self.Scenarios.extend(['multiapps'])
        testIds.assignTestCaseIds(features,self.Scenarios)
        enableH264 = ('disabled', 'enabled')[h264]
        self.PreLaunch(pool=self.poolInfo['pool'])

        kits.Comment(f'########################- Multiple Terminal H264 {enableH264} -################################')

        wkthreads = []
        for it in range(1, repeat + 1):
            wkthreads.clear()
            for user, idx in zip(users, remoteclients):
                p = Thread(target=CheckApp, args=(appName, h264, 'mix', idx, user))
                wkthreads.append(p)
                idx.user = user
            for p in wkthreads:
                if not p.is_alive():
                    p.start()
                if interval:
                    time.sleep(interval)
            # check GUI users
            loginUsers = []
            timeouts = len(users) * interval + 300
            while timeouts > 0:
                outs = agent.GetGUILoginUser(agent.agentIP)
                print(f"Agent Logged on Users: \n{outs}")
                if outs:
                    loginUsers = outs.split()
                    if len(loginUsers) == len(users):
                        break
                time.sleep(2)
                timeouts -= 2
            kits.Comment(f"Logged on Users:\n{loginUsers}")
            # check if all users login successfully
            ret = len(users) == len(loginUsers)
            kits.Verify(ret, True, f"All Users login to VADC Desktop successfully in {it} iteration", screenshot=False)
            if len(loginUsers) == 0:
                kits.Verify(False, True, "None users login successfully!")
                return False
            timeouts = 90
            while timeouts > 0:
                sessionInfo = vadc.ListSessionInfo()
                print(f'vadc session info:\n{sessionInfo}')
                errSession = list(filter(lambda x: x[1] != 'CONNECTED', sessionInfo))
                ret = len(errSession) == 0
                if len(errSession):
                    print(f'error state:\n {errSession}')
                    time.sleep(2)
                    timeouts -= 2
                    continue
                else:
                    break
            kits.Verify(ret, True, f"All Users connected to VADC Desktop successfully in {it} iteration", screenshot=False)
            self.GetUserRpycInfo(users, remoteclients)
            for idx in remoteclients:
                if not hasattr(idx, 'user'):
                    continue
                [x.join() for x in wkthreads]

                if scenario == 'disconnect':
                    vadc.Disconnect_Logoff(idx.user, action='disconnect')
                else:
                    vadc.Disconnect_Logoff(idx.user, action='logoff')

            ShutdownRemoteClients(remoteclients)
            kits.Wait(5)
            sessionInfo = vadc.ListSessionInfo()
            print(f'vadc session info:\n{sessionInfo}')

def CheckApp(appName, h264, udp, rclient, user):
    rclient.connect()
    rclient.RemoteClient_LaunchToAppsRpyc(appName=appName, userName=user, h264=h264, udp=udp, domain=rclient.domain)
    time.sleep(2)

def MakeClientLaunch(pool, desktopSize, h264, udp, rclient, user):
    rclient.connect()
    rclient.RemoteClient_LaunchToDesktopRpyc(pool, userName=user, desktopSize=desktopSize, h264=h264, udp=udp, domain=rclient.domain)
    time.sleep(2)

def ShutdownRemoteClients(remoteclients):
    with futures.ThreadPoolExecutor(len(remoteclients)) as executor:
        todo = []
        for idx in remoteclients:
            p = executor.submit(idx.RemoteClient_KillClient, idx.ip)
            todo.append(p)
            comment = f'Kill remote client over {idx.node}'
            print(comment)
    for future in futures.as_completed(todo):
        res = future.result()
        comment = f'{future} result {res}'
        print(comment)

def CheckDesktop_Remote(rclient, clientIP, agentIP, agentPort, timeout=60, threshold=0.8, expectation=True):
    if not rclient:
        return
    rclient.connect()
    rclient.RemoteClient_CheckDesktopOnRemoteClient(clientIP, agentIP, agentPort, timeout=timeout,
                                                    threshold=threshold, expectation=expectation)
    time.sleep(2)

#Test case Ids covered for CDR: https://omnissa.testrail.io/index.php?/cases/view/40346
# 40347, 40348, 40349, 40383,40384, 40385	
def CheckCDR_Remote(rclient='', agentIP=agent.agentIP):
    if not rclient:
        return
    agent.agentIP = lib.auto.agentData['agentIP']
    agentIP = agent.agentIP   
    rclient.connect()
    rclient.RemoteClient_LaunchToDesktopRpyc("ub-2404-ag","1024x1024", rclient.user,"VMware123" , rclient.domain, True, 'mix')
    # tsclient folder is shown on demand
    print("Waiting for 20 secs to login")
    time.sleep(20)
    accessCmd = "ls -R ~/tsclient"
    agent.GetOutputFromAgentDirectly(cmd=accessCmd, agentIP=agentIP, user=rclient.user)
    time.sleep(2)
    # let's hard code for simpleness
    cdrTestString = "cdrteststring"
    cdrTestFile = "cdrtestfile"
    try:
        conn = rpyc.classic.connect(rclient.ip)
        ros = conn.modules.os
        cdrClientFolder = "C:\\CDR"
        print(f'client:{rclient.os}, {rclient.node}, {rclient.ip}, {rclient.user}')
        if rclient.os == 'Linux':
            cdrClientFolder = ros.path.expanduser("~/CDR")
        cdrClientFile = ros.path.join(cdrClientFolder, cdrTestFile)
        cdrClientFile = ros.path.normpath(cdrClientFile)
        # kits.Comment("cdrClientFile:%s" % cdrClientFile)
        if ros.path.exists(cdrClientFile):
            ros.remove(cdrClientFile)
        cdrAgentFileTemp = "~/tsclient/%s/%s" % (ros.path.split(cdrClientFolder)[-1], cdrTestFile)
        cdrAgentFile = agent.GetOutputFromAgentDirectly(cmd="echo %s" % cdrAgentFileTemp, user=rclient.user,
                                                        agentIP=agentIP).strip()
        # kits.Comment(f"cdrAgentFile:{cdrAgentFile} for {rclient.user}" )
        cmd = 'ls ~/tsclient/*'
        ret = agent.GetOutputFromAgentDirectly(cmd=cmd, agentIP=agentIP, user=rclient.user)
        result = not('no such file' in ret.lower())
        comment = f'Client files/folders in ~/tsclient for {rclient.user}'
        print(comment+str(ret))
        # kits.Verify(result, True, comment)
        #make a directory
        agentCmd = 'mkdir -p ~/tsclient/CDR/CDRFolder'
        agent.GetOutputFromAgentDirectly(cmd=agentCmd, agentIP=agentIP, user=rclient.user)
        #touch a file
        agentCmd = 'touch ~/tsclient/CDR/CDRTouchTest.txt'
        agent.GetOutputFromAgentDirectly(cmd=agentCmd, agentIP=agentIP, user=rclient.user)
        #list a file
        cmd = 'ls ~/tsclient/*'
        ret = agent.GetOutputFromAgentDirectly(cmd=cmd, agentIP=agentIP, user=rclient.user)
        print(ret)
        #delete files and folder
        cmd = 'rm -rf ~/tsclient/CDR/CDRFolder ~/tsclient/CDR/CDRTouchTest.txt'
        ret = agent.GetOutputFromAgentDirectly(cmd=cmd, agentIP=agentIP, user=rclient.user)
        result = not('no such file' in ret.lower())
        print("Delete check Files/Folder: "+str(result))
        #write into a file
        agentCmd = 'echo %s > %s && sync' % (cdrTestString, cdrAgentFile)
        agent.GetOutputFromAgentDirectly(cmd=agentCmd, agentIP=agentIP, user=rclient.user)
        #read file contents
        agentCmd = 'cat %s' % cdrAgentFile
        ret = agent.GetOutputFromAgentDirectly(cmd=agentCmd, agentIP=agentIP, user=rclient.user)
        # kits.Comment(ret)
        result = cdrTestString in ret
        print("CDR write-read test from agent side is: "+str(result)+" for user:"+rclient.user)
        # kits.Verify(result, True, f"CDR write-read test from agent side OK for {rclient.user}")
        ret = ros.path.exists(cdrClientFile)
        print("CDR write check on client side is: "+str(ret)+" for user:"+rclient.user)
        # kits.Verify(ret, True, f"CDR write check on client side OK for {rclient.user}")
        if not ret:
            conn.close()
            return False
        with conn.builtins.open(cdrClientFile) as f:
            content = f.read()
            # kits.Comment(f'content of cdrClientFile:{content}')
            print("CDR read check on client side Content of cdrClientFile: ["+content+"]")
            ret = cdrTestString in content
            # kits.Verify(ret, True, f"CDR read check on client side OK for {rclient.user}")
        agentCmd = 'rm %s && sync' % cdrAgentFile
        ret = agent.GetOutputFromAgentDirectly(cmd=agentCmd, agentIP=agentIP, user=rclient.user)
        # kits.Wait(2)
        ret = ros.path.exists(cdrClientFile)
        print("CDR File exists after deletion on client side is: "+str(ret)+" for user:"+rclient.user)
        # kits.Verify(ret, False, f"CDR delete check on client side OK for {rclient.user}")
        conn.close()
        return not ret

    except Exception as e:
        print("Exception", e)
        # kits.Verify(True, False, f'Hit Exception: {e} in CDR testing')
