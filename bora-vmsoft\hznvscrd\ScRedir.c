/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/* File:    ScRedir.h
 *
 * Purpose: Purpose of this driver is to support smartcard redirection
 *          for non RDP session. The driver receives SmartCard IOCTL's
 *          from winscard on the Guest.
 *
 * Environment: Kernel mode only.
 */

#include "ScRedir.h"
#include "connectionstate.h"
#include "scioctl.h"
#include "vmcheck.h"
#include <stddef.h>
#include <Wdm.h>

/*
 * The trace message header file must be included in a source file before any WPP
 * macro calls and after defining a WPP_CONTROL_GUIDS macro. During the compilation,
 * WPP scans the source files for TraceEvents() calls and builds a .tmh file which
 * stores a unique data GUID for each message, the text resource string for each
 * message, and the data types of the variables passed in for each message.
 * This file is automatically generated and used during post-processing.
 */
#include "ScRedir.tmh"

#ifdef ALLOC_PRAGMA
#   pragma alloc_text(INIT, DriverEntry)
#   pragma alloc_text(PAGE, NonPnpDeviceAdd)
#   pragma alloc_text(PAGE, NonPnpEvtDriverContextCleanup)
#   pragma alloc_text(PAGE, NonPnpEvtDriverUnload)
#   pragma alloc_text(PAGE, NonPnpShutdown)

#   pragma alloc_text(PAGE, DefaultEvtDeviceFileCreate)
#   pragma alloc_text(PAGE, DefaultEvtFileCleanup)
#   pragma alloc_text(PAGE, DefaultEvtFileClose)
#   pragma alloc_text(PAGE, DefaultEvtDeviceIoInCallerContext)
#   pragma alloc_text(PAGE, DefaultEvtIoDeviceControl)
#   pragma alloc_text(PAGE, ScardEvtIoDeviceControl)
#   pragma alloc_text(PAGE, ClientEvtIoDeviceControl)

#   pragma alloc_text(PAGE, ScardEvtIoDeviceControlWinLogon)
#   pragma alloc_text(PAGE, ConnectedEvtIoDeviceControl)

#endif // ALLOC_PRAGMA

static NTSTATUS ExtractHandleFromIoctlBuffer(_In_ WDFREQUEST Request, _Out_ HANDLE *pHandle);

RTL_OSVERSIONINFOEXW g_osVersion = {sizeof(RTL_OSVERSIONINFOEXW), 0};

/* ------------------------------------------------------------------------------------
 *
 * Function:   DriverEntry()
 *
 * Purpose:    This routine is called by the Operating System to initialize the driver.
 *             It creates the device object, fills in the dispatch entry points and
 *             completes the initialization.
 *
 * ------------------------------------------------------------------------------------
 */

NTSTATUS
DriverEntry(_In_ PDRIVER_OBJECT DriverObject, _In_ PUNICODE_STRING RegistryPath)
{
   NTSTATUS status;
   WDF_DRIVER_CONFIG config;
   WDFDRIVER hDriver;
   PWDFDEVICE_INIT pInit = NULL;
   WDF_OBJECT_ATTRIBUTES attributes;
   PCONTROL_DRIVER_EXTENSION driverExtension = NULL;

   PAGED_CODE();

#if defined(VDKX_TARGET_OS_NTDDI_VERSION) && (VDKX_TARGET_OS_NTDDI_VERSION < NTDDI_WIN8)
   ExInitializeDriverRuntime(DrvRtPoolNxOptIn);
#endif

   KDPRINT("Horizon Smartcard Redirection Driver\n");
   KDPRINT("Driver built: %s %s\n", __DATE__, __TIME__);

   WDF_DRIVER_CONFIG_INIT(&config,
                          WDF_NO_EVENT_CALLBACK // This is a non-pnp driver.
   );

   // Pool Tag for Memory Allocations.
   config.DriverPoolTag = POOL_TAG;

   /* Tell the framework that this is non-pnp driver so that it doesn't
    * set the default AddDevice routine.
    */
   config.DriverInitFlags |= WdfDriverInitNonPnpDriver;

   /* NonPnp driver must explicitly register an unload routine for
    * the driver to be unloaded.
    */
   config.EvtDriverUnload = NonPnpEvtDriverUnload;

   /* Register a cleanup callback so that we can call WPP_CLEANUP when
    * the framework driver object is deleted during driver unload.
    */
   WDF_OBJECT_ATTRIBUTES_INIT(&attributes);
   attributes.EvtCleanupCallback = NonPnpEvtDriverContextCleanup;

   // Specify the size of device context
   WDF_OBJECT_ATTRIBUTES_SET_CONTEXT_TYPE(&attributes, CONTROL_DRIVER_EXTENSION);

   // Create a framework driver object to represent our driver.
   status = WdfDriverCreate(DriverObject, RegistryPath, &attributes, &config, &hDriver);
   if (!NT_SUCCESS(status)) {
      KDPRINT("WdfDriverCreate failed with status 0x%x\n", status);
      return status;
   }

   // Initialize the Device Context
   driverExtension = DriverGetExtension(hDriver);

   /* Since we are calling WPP_CLEANUP in the DriverContextCleanup
    * callback we should initialize WPP Tracing after WDFDRIVER
    * object is created to ensure that we cleanup WPP properly
    * if we return failure status from DriverEntry. This
    * eliminates the need to call WPP_CLEANUP in every path
    * of DriverEntry.
    */
   WPP_INIT_TRACING(DriverObject, RegistryPath);

   /* On Win2K system,  you will experience some delay in getting trace events
    * due to the way the ETW is activated to accept trace messages.
    */
   TraceEvents(TRACE_LEVEL_VERBOSE, DBG_INIT, "Horizon Smartcard Redirection Driver");

   TraceEvents(TRACE_LEVEL_VERBOSE, DBG_INIT, "Driver built: %s %s", __DATE__, __TIME__);

   /* In order to create a control device, we first need to allocate a
    * WDFDEVICE_INIT structure and set all properties.
    */
   pInit = WdfControlDeviceInitAllocate(hDriver, &SDDL_DEVOBJ_SYS_ALL_ADM_RWX_WORLD_RW_RES_R);

   if (pInit == NULL) {
      status = STATUS_INSUFFICIENT_RESOURCES;
      return status;
   }

   status = RtlGetVersion((RTL_OSVERSIONINFOW *)&g_osVersion);
   if (!NT_SUCCESS(status)) {
      return status;
   }

   /* Call NonPnpDeviceAdd to create a deviceobject to represent our
    * software device.
    */
   status = NonPnpDeviceAdd(hDriver, pInit);

   return status;
}


/* ------------------------------------------------------------------------------------
 *
 * Function:   NonPnpEvtDriverContextCleanup()
 *
 * Purpose:    Called when the driver object is deleted during driver unload.
 *             You can free all the resources created in DriverEntry that are
 *             not automatically freed by the framework.
 *
 * ------------------------------------------------------------------------------------
 */

_Use_decl_annotations_ VOID
NonPnpEvtDriverContextCleanup(_In_ WDFOBJECT Driver)
{
   PCONTROL_DRIVER_EXTENSION driverExtension = NULL;

   PAGED_CODE();

   TraceEvents(TRACE_LEVEL_VERBOSE, DBG_INIT, "Entered NonPnpEvtDriverContextCleanup\n");

   // Initialize the Device Context
   driverExtension = DriverGetExtension(Driver);

   // Remove the shared memory area.
   CloseSharedMemoryArea(driverExtension);

   // Remove any remaining Kernel event-items
   ClearKEventList(&driverExtension->keventList);

   /* No need to free the controldevice object explicitly because it will
    * be deleted when the Driver object is deleted due to the default parent
    * child relationship between Driver and ControlDevice.
    */
   WPP_CLEANUP(WdfDriverWdmGetDriverObject(Driver));
}


/* ------------------------------------------------------------------------------------
 *
 * Function:   NonPnpShutdown()
 *
 * Purpose:    Callback invoked when the machine is shutting down. If you register for
 *             a last chance shutdown notification you cannot do the following:
 *              o Call any pageable routines
 *              o Access pageable memory
 *              o Perform any file I/O operations
 *
 *             If you register for a normal shutdown notification, all of these are
 *             available to you.
 *
 * ------------------------------------------------------------------------------------
 */

VOID
NonPnpShutdown(_In_ WDFDEVICE Device)
{
   PAGED_CODE();

   UNREFERENCED_PARAMETER(Device);

   return;
}


/* ------------------------------------------------------------------------------------
 *
 * Function:   NonPnpEvtDriverUnload()
 *
 * Purpose:    Called by the I/O subsystem just before unloading the driver.
 *             You can free the resources created in the DriverEntry either
 *             in this routine or in the EvtDriverContextCleanup callback.
 *
 * ------------------------------------------------------------------------------------
 */

VOID
NonPnpEvtDriverUnload(_In_ WDFDRIVER Driver)
{
   PCONTROL_DRIVER_EXTENSION driverExtension = NULL;
   UNREFERENCED_PARAMETER(Driver);

   PAGED_CODE();

   driverExtension = DriverGetExtension(Driver);

   if (driverExtension->mupDevice) {
      FsRtlDeregisterUncProvider(driverExtension->mupDevice);
      driverExtension->mupDevice = NULL;
   }

   TraceEvents(TRACE_LEVEL_VERBOSE, DBG_INIT, "Entered NonPnpDriverUnload\n");

   return;
}


/* ------------------------------------------------------------------------------------
 *
 * Function:   NonPnpDeviceAdd()
 *
 * Purpose:    Called by the DriverEntry to create a control-device. This call is
 *             responsible for freeing the memory for DeviceInit.
 *
 * ------------------------------------------------------------------------------------
 */

NTSTATUS
NonPnpDeviceAdd(_In_ WDFDRIVER Driver, _In_ PWDFDEVICE_INIT DeviceInit)
{
   NTSTATUS status;
   WDF_OBJECT_ATTRIBUTES attributes;
   WDF_IO_QUEUE_CONFIG ioQueueConfig;
   WDF_FILEOBJECT_CONFIG fileConfig;
   WDFQUEUE queue;
   WDFDEVICE controlDevice;
   PCONTROL_DRIVER_EXTENSION driverExtension = NULL;
   DECLARE_CONST_UNICODE_STRING(ntDeviceName, NTDEVICE_NAME_STRING);
   DECLARE_CONST_UNICODE_STRING(symbolicLinkName, SYMBOLIC_NAME_STRING);

   PAGED_CODE();

   TraceEvents(TRACE_LEVEL_VERBOSE, DBG_INIT, "NonPnpDeviceAdd DeviceInit %p\n", DeviceInit);

   driverExtension = DriverGetExtension(Driver);

   WdfDeviceInitSetIoType(DeviceInit, WdfDeviceIoBuffered);

   status = WdfDeviceInitAssignName(DeviceInit, &ntDeviceName);

   if (!NT_SUCCESS(status)) {
      TraceEvents(TRACE_LEVEL_ERROR, DBG_INIT, "WdfDeviceInitAssignName failed %!STATUS!", status);
      goto End;
   }

   WdfControlDeviceInitSetShutdownNotification(DeviceInit, NonPnpShutdown, WdfDeviceShutdown);

   /*
    * Initialize WDF_FILEOBJECT_CONFIG_INIT struct to tell the
    * framework that we are interested in handling Create, Close and
    * Cleanup requests that get generated when an application opens a
    * handle to the device.
    */
   WDF_FILEOBJECT_CONFIG_INIT(&fileConfig, DefaultEvtDeviceFileCreate, DefaultEvtFileClose,
                              DefaultEvtFileCleanup);

   WdfDeviceInitSetFileObjectConfig(DeviceInit, &fileConfig, WDF_NO_OBJECT_ATTRIBUTES);

   /*
    * In order to support copying the event handle from usermode we
    * we need to register for the EvtDeviceIoInProcessContext callback
    * so that we can handle the request in the calling thread's context.
    */
   WdfDeviceInitSetIoInCallerContextCallback(DeviceInit, DefaultEvtDeviceIoInCallerContext);

   WDF_OBJECT_ATTRIBUTES_INIT(&attributes);
   attributes.SynchronizationScope = WdfSynchronizationScopeNone; // No Syncronization


   // Create the device
   status = WdfDeviceCreate(&DeviceInit, &attributes, &controlDevice);
   if (!NT_SUCCESS(status)) {
      TraceEvents(TRACE_LEVEL_ERROR, DBG_INIT, "WdfDeviceCreate failed %!STATUS!", status);
      goto End;
   }

   driverExtension->controlDevice = controlDevice;

   driverExtension->isBooting = TRUE;

   // Initialize SharedMemory Mutex
   KeInitializeMutex(&driverExtension->sharedMemoryMutex, 0);

   // Initialize Client Property Mutex
   KeInitializeMutex(&driverExtension->clientPropertyMutex, 0);

   // Initialize PCoIP Session Active - event
   KeInitializeEvent(&driverExtension->pcoipSessionConnected, NotificationEvent, FALSE);

   // Initialize PCoIP Session is being setup - event
   KeInitializeEvent(&driverExtension->pcoipSCardNotPending, NotificationEvent, TRUE);

   // Create SessionSetup Timer
   status = CreatePendingSessionTimer(controlDevice);

   if (!NT_SUCCESS(status)) {
      // Control device will be deleted automatically by the framework.
      TraceEvents(TRACE_LEVEL_ERROR, DBG_INIT, "Failed to create Timer %!STATUS!", status);
      goto End;
   }

   // Initialize Client Mutex
   KeInitializeMutex(&driverExtension->clientMutex, 0);

   // Initialize Client Completion Queue Mutex
   KeInitializeMutex(&driverExtension->clientCompletionMutex, 0);

   // Initialize Client RX Ready Event
   KeInitializeEvent(&driverExtension->clientRxReady, NotificationEvent, FALSE);

   // Initialize the list of change notification events
   InitialiseEventList(&driverExtension->eventList);

   // Initialize the list of Ref-Counted Kernel events used with the shared Memory
   InitialiseKEventList(&driverExtension->keventList);

   status = CreateSharedMemoryArea(driverExtension, 0);
   if (!NT_SUCCESS(status)) {
      TraceEvents(TRACE_LEVEL_ERROR, DBG_INIT, "CreateSharedMemoryArea failed %!STATUS!", status);
      goto End;
   }

   /* Create a symbolic link for the control object so that
    * usermode apps can open the device.
    */
   status = WdfDeviceCreateSymbolicLink(controlDevice, &symbolicLinkName);

   if (!NT_SUCCESS(status)) {
      // Control device will be deleted automatically by the framework.
      TraceEvents(TRACE_LEVEL_ERROR, DBG_INIT, "WdfDeviceCreateSymbolicLink failed %!STATUS!",
                  status);
      goto End;
   }

   // Create the DEFAULT QUEUE
   WDF_IO_QUEUE_CONFIG_INIT_DEFAULT_QUEUE(&ioQueueConfig, WdfIoQueueDispatchParallel);

   ioQueueConfig.EvtIoDeviceControl = DefaultEvtIoDeviceControl;

   /* Since we are using Zw function set execution level to passive so that
    * framework ensures that our Io callbacks called at only passive-level
    * even if the request came in at DISPATCH_LEVEL from another driver.
    */
   WDF_OBJECT_ATTRIBUTES_INIT(&attributes);
   attributes.ExecutionLevel = WdfExecutionLevelPassive;
   attributes.SynchronizationScope = WdfSynchronizationScopeNone; // No Syncronization

   __analysis_assume(ioQueueConfig.EvtIoStop != 0);
   status = WdfIoQueueCreate(controlDevice, &ioQueueConfig, &attributes,
                             &queue // default queue
   );
   __analysis_assume(ioQueueConfig.EvtIoStop == 0);

   if (!NT_SUCCESS(status)) {
      TraceEvents(TRACE_LEVEL_ERROR, DBG_INIT, "WdfIoQueueCreate failed %!STATUS!", status);
      goto End;
   }


   /*
    * To enable access from process running in AppContainer, we will
    * open file level access to our driver via MUP.
    * Failing this will not fail existing functionality, hence we will
    * not return in case of any failures.
    */
   if (IsWindows8AndAbove()) {
      PDEVICE_OBJECT wdmDeviceObject = WdfDeviceWdmGetDeviceObject(controlDevice);

      TraceEvents(TRACE_LEVEL_VERBOSE, DBG_INIT, "wdmDeviceObject %p\n", wdmDeviceObject);

      if (!wdmDeviceObject) {
         TraceEvents(TRACE_LEVEL_ERROR, DBG_INIT,
                     "WdfDeviceWdmGetDeviceObject returned NULL, UWP apps will not work ");
      } else {
         UNICODE_STRING deviceName;
         // This should match the device name used by hznsci
         RtlInitUnicodeString(&deviceName, L"\\Device\\HZNVSCard");
         wdmDeviceObject->Characteristics |= FILE_REMOTE_DEVICE;
         status = FsRtlRegisterUncProviderEx(&driverExtension->mupDevice, &deviceName,
                                             wdmDeviceObject, 0);
         if (!NT_SUCCESS(status)) {
            TraceEvents(TRACE_LEVEL_ERROR, DBG_INIT, "FsRtlRegisterUncProvider failed %!STATUS!",
                        status);
         }
      }
   }

   // Create specialised queues

   // SCARD IOCTL Queue
   WDF_IO_QUEUE_CONFIG_INIT(&ioQueueConfig, WdfIoQueueDispatchParallel);

   ioQueueConfig.EvtIoDeviceControl = ScardEvtIoDeviceControl;

   WDF_OBJECT_ATTRIBUTES_INIT(&attributes);
   attributes.ExecutionLevel = WdfExecutionLevelPassive;
   attributes.SynchronizationScope = WdfSynchronizationScopeNone; // No Syncronization

   __analysis_assume(ioQueueConfig.EvtIoStop != 0);
   status =
      WdfIoQueueCreate(controlDevice, &ioQueueConfig, &attributes, &driverExtension->scardQueue);
   __analysis_assume(ioQueueConfig.EvtIoStop == 0);

   if (!NT_SUCCESS(status)) {
      TraceEvents(TRACE_LEVEL_ERROR, DBG_INIT, "WdfIoQueueCreate failed %!STATUS!", status);
      goto End;
   }

   // Holding Queue for Pending Virtual Channel Ready Request
   WDF_IO_QUEUE_CONFIG_INIT(&ioQueueConfig, WdfIoQueueDispatchParallel);
   ioQueueConfig.EvtIoDeviceControl = ConnectedEvtIoDeviceControl;

   WDF_OBJECT_ATTRIBUTES_INIT(&attributes);
   attributes.ExecutionLevel = WdfExecutionLevelPassive;
   attributes.SynchronizationScope = WdfSynchronizationScopeNone; // No Syncronization

   __analysis_assume(ioQueueConfig.EvtIoStop != 0);
   status = WdfIoQueueCreate(controlDevice, &ioQueueConfig, &attributes,
                             &driverExtension->scardConnectedQueue // pointer to CONNECTED queue
   );
   __analysis_assume(ioQueueConfig.EvtIoStop == 0);

   if (!NT_SUCCESS(status)) {
      TraceEvents(TRACE_LEVEL_ERROR, DBG_INIT, "WdfIoQueueCreate failed %!STATUS!", status);
      goto End;
   }

   // CLIENT IOCTL Queue
   WDF_IO_QUEUE_CONFIG_INIT(&ioQueueConfig, WdfIoQueueDispatchParallel);

   ioQueueConfig.EvtIoDeviceControl = ClientEvtIoDeviceControl;

   WDF_OBJECT_ATTRIBUTES_INIT(&attributes);
   attributes.ExecutionLevel = WdfExecutionLevelPassive;
   attributes.SynchronizationScope = WdfSynchronizationScopeNone; // No Syncronization

   __analysis_assume(ioQueueConfig.EvtIoStop != 0);
   status =
      WdfIoQueueCreate(controlDevice, &ioQueueConfig, &attributes, &driverExtension->clientQueue);
   __analysis_assume(ioQueueConfig.EvtIoStop == 0);

   if (!NT_SUCCESS(status)) {
      TraceEvents(TRACE_LEVEL_ERROR, DBG_INIT, "WdfIoQueueCreate failed %!STATUS!", status);
      goto End;
   }

   // SEND Queue [Manual]
   WDF_IO_QUEUE_CONFIG_INIT(&ioQueueConfig, WdfIoQueueDispatchManual);

   WDF_OBJECT_ATTRIBUTES_INIT(&attributes);
   attributes.ExecutionLevel = WdfExecutionLevelPassive;

   status = WdfIoQueueCreate(controlDevice, &ioQueueConfig, &attributes,
                             &driverExtension->clientSendQueue);
   if (!NT_SUCCESS(status)) {
      TraceEvents(TRACE_LEVEL_ERROR, DBG_INIT, "WdfIoQueueCreate failed %!STATUS!", status);
      goto End;
   }

   // RECIEVE COMPLETION Queue [Manual]
   WDF_IO_QUEUE_CONFIG_INIT(&ioQueueConfig, WdfIoQueueDispatchManual);

   WDF_OBJECT_ATTRIBUTES_INIT(&attributes);
   attributes.ExecutionLevel = WdfExecutionLevelPassive;

   status = WdfIoQueueCreate(controlDevice, &ioQueueConfig, &attributes,
                             &driverExtension->clientCompletionQueue);
   if (!NT_SUCCESS(status)) {
      TraceEvents(TRACE_LEVEL_ERROR, DBG_INIT, "WdfIoQueueCreate failed %!STATUS!", status);
      goto End;
   }

   /*
    * Initialize WinLogon specific queues, these are required for XP guests.
    */

   // Initialize Winlogon Client Mutex
   KeInitializeMutex(&driverExtension->clientMutexWinLogon, 0);

   // Initialize Winlogon Client Completion Queue Mutex
   KeInitializeMutex(&driverExtension->clientCompletionMutexWinLogon, 0);

   // Initialize Winlogon Client RX Ready Event
   KeInitializeEvent(&driverExtension->clientRxReadyWinLogon, NotificationEvent, FALSE);

   // SCARD IOCTL WINLOGON Queue
   WDF_IO_QUEUE_CONFIG_INIT(&ioQueueConfig, WdfIoQueueDispatchParallel);

   ioQueueConfig.EvtIoDeviceControl = ScardEvtIoDeviceControlWinLogon;

   WDF_OBJECT_ATTRIBUTES_INIT(&attributes);
   attributes.ExecutionLevel = WdfExecutionLevelPassive;
   attributes.SynchronizationScope = WdfSynchronizationScopeNone; // No Syncronization

   __analysis_assume(ioQueueConfig.EvtIoStop != 0);
   status = WdfIoQueueCreate(controlDevice, &ioQueueConfig, &attributes,
                             &driverExtension->scardQueueWinLogon);
   __analysis_assume(ioQueueConfig.EvtIoStop == 0);

   if (!NT_SUCCESS(status)) {
      TraceEvents(TRACE_LEVEL_ERROR, DBG_INIT, "WdfIoQueueCreate failed %!STATUS!", status);
      goto End;
   }

   // WINLOGON CLIENT IOCTL Queue
   WDF_IO_QUEUE_CONFIG_INIT(&ioQueueConfig, WdfIoQueueDispatchSequential);

   ioQueueConfig.EvtIoDeviceControl = ClientEvtIoDeviceControl;

   WDF_OBJECT_ATTRIBUTES_INIT(&attributes);
   attributes.ExecutionLevel = WdfExecutionLevelPassive;
   attributes.SynchronizationScope = WdfSynchronizationScopeNone; // No Syncronization

   __analysis_assume(ioQueueConfig.EvtIoStop != 0);
   status = WdfIoQueueCreate(controlDevice, &ioQueueConfig, &attributes,
                             &driverExtension->clientQueueWinLogon);
   __analysis_assume(ioQueueConfig.EvtIoStop == 0);

   if (!NT_SUCCESS(status)) {
      TraceEvents(TRACE_LEVEL_ERROR, DBG_INIT, "WdfIoQueueCreate failed %!STATUS!", status);
      goto End;
   }

   // WINLOGON SEND Queue [Manual]
   WDF_IO_QUEUE_CONFIG_INIT(&ioQueueConfig, WdfIoQueueDispatchManual);

   WDF_OBJECT_ATTRIBUTES_INIT(&attributes);
   attributes.ExecutionLevel = WdfExecutionLevelPassive;

   status = WdfIoQueueCreate(controlDevice, &ioQueueConfig, &attributes,
                             &driverExtension->clientSendQueueWinLogon);
   if (!NT_SUCCESS(status)) {
      TraceEvents(TRACE_LEVEL_ERROR, DBG_INIT, "WdfIoQueueCreate failed %!STATUS!", status);
      goto End;
   }

   // WINLOGON RECIEVE COMPLETION Queue [Manual]
   WDF_IO_QUEUE_CONFIG_INIT(&ioQueueConfig, WdfIoQueueDispatchManual);

   WDF_OBJECT_ATTRIBUTES_INIT(&attributes);
   attributes.ExecutionLevel = WdfExecutionLevelPassive;

   status = WdfIoQueueCreate(controlDevice, &ioQueueConfig, &attributes,
                             &driverExtension->clientCompletionQueueWinLogon);
   if (!NT_SUCCESS(status)) {
      TraceEvents(TRACE_LEVEL_ERROR, DBG_INIT, "WdfIoQueueCreate failed %!STATUS!", status);
      goto End;
   }

   /* Control devices must notify WDF when they are done initializing.
    * I/O is rejected until this call is made.
    */
   WdfControlFinishInitializing(controlDevice);

End:
   /* If the device is created successfully, framework would clear the
    * DeviceInit value. Otherwise device create must have failed so we
    * should free the memory ourself.
    */
   if (DeviceInit != NULL) {
      WdfDeviceInitFree(DeviceInit);
   }

   return status;
}


/* ------------------------------------------------------------------------------------
 *
 * Function:   DefaultEvtDeviceFileCreate()
 *
 * Purpose:    The framework calls a driver's EvtDeviceFileCreate callback
 *             when it receives an IRP_MJ_CREATE request.
 *             The system sends this request when a user application opens the
 *             device to perform an I/O operation, such as reading or writing a file.
 *             This callback is called synchronously, in the context of the thread
 *             that created the IRP_MJ_CREATE request.
 *
 * ------------------------------------------------------------------------------------
 */

VOID
DefaultEvtDeviceFileCreate(_In_ WDFDEVICE Device, _In_ WDFREQUEST Request,
                           _In_ WDFFILEOBJECT FileObject)
{
   NTSTATUS status = STATUS_SUCCESS;
   WDF_OBJECT_ATTRIBUTES attributes;
   PCONTROL_FILE_EXTENSION fileExt = NULL;
   PUNICODE_STRING fileName;

   UNREFERENCED_PARAMETER(FileObject);

   PAGED_CODE();

   WDF_OBJECT_ATTRIBUTES_INIT_CONTEXT_TYPE(&attributes, CONTROL_FILE_EXTENSION);

   // Ensure the Shared Memory area has been created.
   status = CreateSharedMemoryArea(DriverGetExtension(WdfDeviceGetDriver(Device)), 10000);
   if (!NT_SUCCESS(status)) {
      WdfRequestComplete(Request, status);
      return;
   }

   // Create Context
   status = WdfObjectAllocateContext(FileObject, &attributes, &fileExt);
   if (!NT_SUCCESS(status)) {
      WdfRequestComplete(Request, status);
      return;
   }

   // Clear all members
   fileExt->ioDeviceType = ioDeviceUNK;
   fileExt->fileId = 0;

   // Get pointer to Driver Context
   fileExt->driverExt = DriverGetExtension(WdfDeviceGetDriver(Device));

   // RSCDRV\\SCARD
   fileName = WdfFileObjectGetFileName(FileObject);

   KDPRINT("Filename: %wZ\n", fileName);

   if ((fileName == NULL) || (fileName->Length == 0)) {
      if (IsWindows8AndAbove()) {
         /*
          * While registering, MUP will do a create IRP on driver, it will not have
          * any file name associated with it but it should succeed, or else registration
          * will fail.
          * We are selectively doing it only for request originating in kernel and when our
          * MUP device is being created.
          */
         if (WdfRequestGetRequestorMode(Request) == KernelMode && !fileExt->driverExt->mupDevice) {
            status = STATUS_SUCCESS;
         } else {
            KDPRINT("OPEN INVALID NAME\n");
            status = STATUS_NO_SUCH_FILE;
         }
      } else {
         // Win7, fail all calls
         KDPRINT("OPEN INVALID NAME\n");
         status = STATUS_NO_SUCH_FILE;
      }
   } else if (CompareFilename(fileName, L"\\REMOTE_STATE")) {
      fileExt->ioDeviceType = ioDeviceREMOTE_STATE;
      // Generate Connection Id
      fileExt->fileId = InterlockedIncrement(&fileExt->driverExt->drvConnectionId);
      if (fileExt->fileId == 0) {
         fileExt->fileId = InterlockedIncrement(&fileExt->driverExt->drvConnectionId);
      }
      KDPRINT("OPEN EVENT FILE [%d]\n", fileExt->fileId);
      status = STATUS_SUCCESS;
   } else if (CompareFilename(fileName, L"\\SCARD")) {
      fileExt->ioDeviceType = ioDeviceSCARD;
      // Generate Connection Id
      fileExt->fileId = InterlockedIncrement(&fileExt->driverExt->drvConnectionId);
      if (fileExt->fileId == 0) {
         fileExt->fileId = InterlockedIncrement(&fileExt->driverExt->drvConnectionId);
      }
      KDPRINT("OPEN REDIRECTED SCARD [%d]\n", fileExt->fileId);
      status = STATUS_SUCCESS;
   } else if (CompareFilename(fileName, L"\\SCARD\\WINLOGON")) {
      fileExt->ioDeviceType = ioDeviceSCARD_WINLOGON;

      // Generate Connection Id
      fileExt->fileId = InterlockedIncrement(&fileExt->driverExt->drvConnectionId);
      if (fileExt->fileId == 0) {
         fileExt->fileId = InterlockedIncrement(&fileExt->driverExt->drvConnectionId);
      }
      KDPRINT("OPEN REDIRECTED SCARD from WINLOGON[%d]\n", fileExt->fileId);
      status = STATUS_SUCCESS;
   } else if (CompareFilename(fileName, L"\\CLIENT")) {
      KDPRINT("OPEN SCARD IRPSERVER\n");

      // Ensure only one client is open an any one time
      if (KeWaitForMutexObject(&fileExt->driverExt->clientMutex, Executive, KernelMode, FALSE,
                               NULL) != STATUS_SUCCESS) {
         status = STATUS_UNSUCCESSFUL;
      } else {
         if (fileExt->driverExt->clientConnected) {
            status = STATUS_TOO_MANY_OPENED_FILES;
         } else {
            // Set file type to CLIENT
            fileExt->ioDeviceType = ioDeviceCLIENT;
            // Start up the Client Virtual Channel Queues
            WdfIoQueueStart(fileExt->driverExt->clientSendQueue);
            WdfIoQueueStart(fileExt->driverExt->clientCompletionQueue);
            fileExt->driverExt->clientConnected = TRUE;
         }
         KeReleaseMutex(&fileExt->driverExt->clientMutex, FALSE);
      }
   } else if (CompareFilename(fileName, L"\\CLIENT\\WINLOGON")) {
      KDPRINT("OPEN WINLOGON SCARD IRPSERVER\n");

      // Ensure only one client is open an any one time
      if (KeWaitForMutexObject(&fileExt->driverExt->clientMutexWinLogon, Executive, KernelMode,
                               FALSE, NULL) != STATUS_SUCCESS) {
         status = STATUS_UNSUCCESSFUL;
      } else {
         if (fileExt->driverExt->clientConnectedWinLogon) {
            status = STATUS_TOO_MANY_OPENED_FILES;
         } else {
            // Set file type to CLIENT_WINLOGON
            fileExt->ioDeviceType = ioDeviceCLIENT_WINLOGON;
            // Start up the Client Virtual Channel Queues
            WdfIoQueueStart(fileExt->driverExt->clientSendQueueWinLogon);
            WdfIoQueueStart(fileExt->driverExt->clientCompletionQueueWinLogon);
            fileExt->driverExt->clientConnectedWinLogon = TRUE;
            /* Set isBooting to FALSE to indicate that the smartcard
             * plugin has started up once since the system booted.
             */
            fileExt->driverExt->isBooting = FALSE;
         }
         KeReleaseMutex(&fileExt->driverExt->clientMutexWinLogon, FALSE);
      }
   } else {
      KDPRINT("OPEN INVALID NAME\n");
      status = STATUS_NO_SUCH_FILE;
   }

   WdfRequestComplete(Request, status);

   return;
}


/* ------------------------------------------------------------------------------------
 *
 * Function:   DefaultEvtFileCleanup()
 *
 * Purpose:    The framework calls the driver's file cleanup callback when the last
 *             handle to the file object has been closed and released, so the file
 *             has no additional clients. The cleanup callback should cancel all
 *             outstanding I/O requests for the file.
 *
 * ------------------------------------------------------------------------------------
 */

VOID
DefaultEvtFileCleanup(_In_ WDFFILEOBJECT FileObject)
{
   PCONTROL_FILE_EXTENSION fileExt = NULL;

   PAGED_CODE();

   //   TraceEvents(TRACE_LEVEL_VERBOSE, DBG_INIT, "DefaultEvtFileCleanup\n");

   fileExt = FileGetExtension(FileObject);

   if (fileExt->ioDeviceType == ioDeviceCLIENT) {
      PKEVENT clientDataEvent = NULL;

      KDPRINT("CLOSE SCARD IRPSERVER\n");

      if (KeWaitForMutexObject(&fileExt->driverExt->clientMutex, Executive, KernelMode, FALSE,
                               NULL) == STATUS_SUCCESS) {

         // Clear Client RX Ready Event
         KDPRINT("SET STATE TO LOCAL\n");
         KeClearEvent(&fileExt->driverExt->clientRxReady);

         // Set the PCoIP connection state to disconnected.
         SetPCoIPStateClosed(fileExt->driverExt, FALSE);

         if (fileExt->driverExt->clientDataEvent != NULL) {
            clientDataEvent = fileExt->driverExt->clientDataEvent;
            fileExt->driverExt->clientDataEvent = NULL;
         }

         WdfIoQueuePurgeSynchronously(fileExt->driverExt->clientSendQueue);
         WdfIoQueuePurgeSynchronously(fileExt->driverExt->clientCompletionQueue);

         fileExt->driverExt->clientConnected = FALSE;

         KeReleaseMutex(&fileExt->driverExt->clientMutex, FALSE);

         // Set all registered Change events as signalled
         SetAllEventItems(&fileExt->driverExt->eventList);
      }
      if (clientDataEvent != NULL) {
         ObDereferenceObject(clientDataEvent);
      }
   } else if (fileExt->ioDeviceType == ioDeviceCLIENT_WINLOGON) {
      PKEVENT clientDataEventWinLogon = NULL;

      KDPRINT("CLOSE WINLOGON SCARD IRPSERVER\n");

      if (KeWaitForMutexObject(&fileExt->driverExt->clientMutexWinLogon, Executive, KernelMode,
                               FALSE, NULL) == STATUS_SUCCESS) {

         // Clear WINLOGON Client RX Ready Event
         KDPRINT("SET WINLOGON STATE TO LOCAL\n");
         KeClearEvent(&fileExt->driverExt->clientRxReadyWinLogon);

         // Set the PCoIP connection state to disconnected.
         SetPCoIPStateClosed(fileExt->driverExt, FALSE);

         if (fileExt->driverExt->clientDataEventWinLogon != NULL) {
            clientDataEventWinLogon = fileExt->driverExt->clientDataEventWinLogon;
            fileExt->driverExt->clientDataEventWinLogon = NULL;
         }

         WdfIoQueuePurgeSynchronously(fileExt->driverExt->clientSendQueueWinLogon);
         WdfIoQueuePurgeSynchronously(fileExt->driverExt->clientCompletionQueueWinLogon);

         fileExt->driverExt->clientConnectedWinLogon = FALSE;

         KeReleaseMutex(&fileExt->driverExt->clientMutexWinLogon, FALSE);

         // Set all registered Change events as signalled
         SetAllEventItems(&fileExt->driverExt->eventList);
      }
      if (clientDataEventWinLogon != NULL) {
         ObDereferenceObject(clientDataEventWinLogon);
      }
   } else if (fileExt->ioDeviceType == ioDeviceREMOTE_STATE) {
      /* EVENT
       * Deference any notification event associated with this file.
       */
      DereferenceEvents(&fileExt->driverExt->eventList, fileExt->fileId);
   }

   return;
}


/* ---------------------------------------------------------------------------------------
 *
 * Function:   DefaultEvtFileClose()
 *
 * Purpose:    EvtFileClose is called when all the handles represented by the FileObject
 *             is closed and all the references to FileObject is removed. This callback
 *             may get called in an arbitrary thread context instead of the thread that
 *             called CloseHandle.
 *
 * ------------------------------------------------------------------------------------
 */

VOID
DefaultEvtFileClose(_In_ WDFFILEOBJECT FileObject)
{
   PCONTROL_FILE_EXTENSION fileExt = NULL;

   PAGED_CODE();

   //   TraceEvents(TRACE_LEVEL_VERBOSE, DBG_INIT, "NonPnpEvtFileClose\n");

   fileExt = FileGetExtension(FileObject);
   if (fileExt->fileId) {
      KDPRINT("CLOSE FILE [%d]\n", fileExt->fileId);
   }

   return;
}


/* ------------------------------------------------------------------------------------
 *
 * Function:   DefaultEvtDeviceIoInCallerContext()
 *
 * Purpose:    This I/O in-process callback is called in the calling thread's
 *             context/address space before the request is subjected to any framework
 *             locking or queueing scheme based on the device pnp/power or locking
 *             attributes set by the driver. The process context of the calling app is
 *             guaranteed as long as this driver is a top-level driver and no other
 *             filter driver is attached to it.
 *
 * ------------------------------------------------------------------------------------
 */

_Use_decl_annotations_ VOID
DefaultEvtDeviceIoInCallerContext(_In_ WDFDEVICE Device, _In_ WDFREQUEST Request)
{
   NTSTATUS status = STATUS_SUCCESS;
   PCONTROL_FILE_EXTENSION fileExt = NULL;
   WDF_REQUEST_PARAMETERS params;

   PAGED_CODE();

   //   TraceEvents(TRACE_LEVEL_VERBOSE, DBG_IOCTL,
   //               "Entered DefaultEvtDeviceIoInCallerContext %p\n", Request);

   WDF_REQUEST_PARAMETERS_INIT(&params);
   WdfRequestGetParameters(Request, &params);

   if (params.Type != WdfRequestTypeDeviceControl) {
      // Forward request for processing
      status = WdfDeviceEnqueueRequest(Device, Request);
      if (!NT_SUCCESS(status)) {
         TraceEvents(TRACE_LEVEL_ERROR, DBG_IOCTL, "Error forwarding Request 0x%x", status);
         goto ReturnFailure;
      }
      return;
   }

   if (IsWindows8AndAbove()) {
      if (WdfRequestGetRequestorMode(Request) == KernelMode) {
         // We are not a real network driver, finish MUP calls here
         switch (params.Parameters.DeviceIoControl.IoControlCode) {
         case IOCTL_REDIR_QUERY_PATH_EX:
         case IOCTL_REDIR_QUERY_PATH: {
            status = STATUS_BAD_NETWORK_PATH;
            goto ReturnFailure;
         }
         }
      }
   }

   // Get pointer to File Context
   fileExt = FileGetExtension(WdfRequestGetFileObject(Request));

   switch (fileExt->ioDeviceType) {
   case ioDeviceCLIENT: {
      if (params.Parameters.DeviceIoControl.IoControlCode == IOCTL_SCARD_REGISTER_DATA_EVENT) {
         /*
          * Register the Data Ready Event
          */
         HANDLE hEvent;
         PKEVENT clientDataEvent = NULL;

         KDPRINT("IOCTL_REGISTER_EVENT\n");

         status = ExtractHandleFromIoctlBuffer(Request, &hEvent);
         if (!NT_SUCCESS(status)) {
            goto ReturnFailure;
         }

         /*
          * Get the object pointer from the handle. Note we must be
          * in the context of the process that created the handle.
          */
         status = ObReferenceObjectByHandle(hEvent, EVENT_MODIFY_STATE, *ExEventObjectType,
                                            UserMode, &clientDataEvent, NULL);
         if (NT_SUCCESS(status)) {
            // Ensure only one client connection is opened at any one time.
            if ((status = KeWaitForMutexObject(&fileExt->driverExt->clientMutex, Executive,
                                               KernelMode, FALSE, NULL)) != STATUS_SUCCESS) {
               status = STATUS_UNSUCCESSFUL;
            } else {
               if (fileExt->driverExt->clientDataEvent != NULL) {
                  status = STATUS_TOO_MANY_OPENED_FILES;
               } else {
                  fileExt->driverExt->clientDataEvent = clientDataEvent;
                  // Set Client RX Ready Event
                  KeSetEvent(&fileExt->driverExt->clientRxReady, 0, FALSE);
                  // Set all registered Change events to signalled
                  SetAllEventItems(&fileExt->driverExt->eventList);
               }
               KeReleaseMutex(&fileExt->driverExt->clientMutex, FALSE);
            }
            if ((!NT_SUCCESS(status)) && (clientDataEvent != NULL)) {
               ObDereferenceObject(clientDataEvent);
            }
         }

         if (!NT_SUCCESS(status)) {
            TraceEvents(TRACE_LEVEL_ERROR, DBG_IOCTL, "Error adding event handle 0x%x", status);
            goto ReturnFailure;
         }

         // Complete the request
         WdfRequestComplete(Request, status);
         return;
      }
      break;
   } // ioDeviceCLIENT

   case ioDeviceCLIENT_WINLOGON: {
      if (params.Parameters.DeviceIoControl.IoControlCode == IOCTL_SCARD_REGISTER_DATA_EVENT) {
         /*
          * Register the Data Ready Event
          */
         HANDLE hEvent;
         PKEVENT clientDataEvent = NULL;

         KDPRINT("IOCTL_REGISTER_EVENT\n");

         status = ExtractHandleFromIoctlBuffer(Request, &hEvent);
         if (!NT_SUCCESS(status)) {
            goto ReturnFailure;
         }

         /*
          * Get the object pointer from the handle. Note we must be
          * in the context of the process that created the handle.
          */
         status = ObReferenceObjectByHandle(hEvent, EVENT_MODIFY_STATE, *ExEventObjectType,
                                            UserMode, &clientDataEvent, NULL);
         if (NT_SUCCESS(status)) {
            // Ensure only one client connection is opened at any one time.
            if ((status = KeWaitForMutexObject(&fileExt->driverExt->clientMutexWinLogon, Executive,
                                               KernelMode, FALSE, NULL)) != STATUS_SUCCESS) {
               status = STATUS_UNSUCCESSFUL;
            } else {
               if (fileExt->driverExt->clientDataEventWinLogon != NULL) {
                  status = STATUS_TOO_MANY_OPENED_FILES;
               } else {
                  fileExt->driverExt->clientDataEventWinLogon = clientDataEvent;
                  // Set Client RX Ready Event
                  KeSetEvent(&fileExt->driverExt->clientRxReadyWinLogon, 0, FALSE);
                  // Set all registered Change events to signalled
                  SetAllEventItems(&fileExt->driverExt->eventList);
               }
               KeReleaseMutex(&fileExt->driverExt->clientMutexWinLogon, FALSE);
            }
            if ((!NT_SUCCESS(status)) && (clientDataEvent != NULL)) {
               ObDereferenceObject(clientDataEvent);
            }
         }

         if (!NT_SUCCESS(status)) {
            TraceEvents(TRACE_LEVEL_ERROR, DBG_IOCTL, "Error adding event handle 0x%x", status);
            goto ReturnFailure;
         }

         // Complete the request
         WdfRequestComplete(Request, status);
         return;
      }
      break;
   } // ioDeviceCLIENT_WINLOGON

   case ioDeviceREMOTE_STATE: {
      switch (params.Parameters.DeviceIoControl.IoControlCode) {
      /*
       * Allow the Shim to register a console remote state change notification
       */
      case IOCTL_SCARD_REGISTER_CHANGE_EVENT: {
         HANDLE hEvent;
         PKEVENT clientEvent = NULL;

         KDPRINT("IOCTL_SCARD_REGISTER_CHANGE_EVENT\n");

         status = ExtractHandleFromIoctlBuffer(Request, &hEvent);
         if (!NT_SUCCESS(status)) {
            goto ReturnFailure;
         }

         /*
          * Get the object pointer from the handle. Note we must be
          * in the context of the process that created the handle.
          */
         status = ObReferenceObjectByHandle(hEvent, EVENT_MODIFY_STATE, *ExEventObjectType,
                                            UserMode, &clientEvent, NULL);
         if (NT_SUCCESS(status)) {
            // Add a new Event to the list
            status = AddEventItem(&fileExt->driverExt->eventList, fileExt->fileId, clientEvent);
            if ((!NT_SUCCESS(status)) && (clientEvent != NULL)) {
               KDPRINT("Failed to add event to list of events %0x08X\n", status);
               ObDereferenceObject(clientEvent);
            } else {
               KDPRINT("Successfully added a new event\n");
            }
         } else {
            KDPRINT("ObReferenceObjectByHandle Failed %0x08X\n", status);
         }

         if (!NT_SUCCESS(status)) {
            goto ReturnFailure;
         }

         // Complete the request
         WdfRequestComplete(Request, status);
         return;
      }
      case IOCTL_SCARD_GET_CLIENT_PROPERTIES: {
         status = GetPCoIPClientProperties(params, Request, fileExt->driverExt);
         WdfRequestComplete(Request, status);
         return;
      }
      default: {
         // IOCTL is not recognized!
         KDPRINT("This IOCTL is not recognized\n");
         status = STATUS_INVALID_DEVICE_REQUEST;
         goto ReturnFailure;
      }
      }
   } // ioDeviceREMOTE_STATE
   }

   // Forward request for processing
   status = WdfDeviceEnqueueRequest(Device, Request);
   if (!NT_SUCCESS(status)) {
      TraceEvents(TRACE_LEVEL_ERROR, DBG_IOCTL, "Error forwarding Request 0x%x", status);
      goto ReturnFailure;
   }

   return;

ReturnFailure:

   TraceEvents(TRACE_LEVEL_VERBOSE, DBG_IOCTL, "EvtIoPreProcess failed %x \n", status);
   WdfRequestSetInformation(Request, 0);
   WdfRequestComplete(Request, status);
   return;
}


/* ------------------------------------------------------------------------------------
 *
 * Function:   DefaultEvtIoDeviceControl
 *
 * Purpose:    This event is called when the framework receives IRP_MJ_DEVICE_CONTROL
 *             requests from the system.
 *
 * ------------------------------------------------------------------------------------
 */

_Use_decl_annotations_ VOID
DefaultEvtIoDeviceControl(_In_ WDFQUEUE Queue, _In_ WDFREQUEST Request,
                          _In_ size_t OutputBufferLength, _In_ size_t InputBufferLength,
                          _In_ ULONG IoControlCode)
{
   NTSTATUS status = STATUS_SUCCESS;
   PCONTROL_FILE_EXTENSION fileExt = NULL;

   UNREFERENCED_PARAMETER(Queue);
   UNREFERENCED_PARAMETER(OutputBufferLength);
   UNREFERENCED_PARAMETER(InputBufferLength);
   UNREFERENCED_PARAMETER(IoControlCode);

   PAGED_CODE();

   // Get pointer to File Context
   fileExt = FileGetExtension(WdfRequestGetFileObject(Request));

   // Route the I/O code to the correct queue
   switch (fileExt->ioDeviceType) {
   case ioDeviceSCARD:
   case ioDeviceSCARD_WINLOGON: {
      // Incoming request.
      WDF_OBJECT_ATTRIBUTES attributes;
      PCONTROL_REQUEST_EXTENSION reqExt = NULL;
      WDF_OBJECT_ATTRIBUTES_INIT_CONTEXT_TYPE(&attributes, CONTROL_REQUEST_EXTENSION);

      // Create Request Context
      if (!NT_SUCCESS(status = WdfObjectAllocateContext(Request, &attributes, &reqExt))) {
         break;
      }
      reqExt->isCancelled = FALSE;

      if (fileExt->ioDeviceType == ioDeviceSCARD) {
         status = WdfRequestForwardToIoQueue(Request, fileExt->driverExt->scardQueue);
      } else {
         status = WdfRequestForwardToIoQueue(Request, fileExt->driverExt->scardQueueWinLogon);
      }
      if (NT_SUCCESS(status)) {
         // The request was successfully forwarded to the Queue.
         return;
      }
      // Failed to move request to the queue, return the error.
      break;
   }

   case ioDeviceCLIENT:
   case ioDeviceCLIENT_WINLOGON: {
      switch (IoControlCode) {
      case IOCTL_SCARD_SET_CLIENT_VERSION: {
         KDPRINT("IOCTL_SCARD_SET_CLIENT_VERSION\n");
         status = SetPCoIPClientVersion(Request, fileExt->driverExt);
         WdfRequestComplete(Request, status);
         return;
      }
      case IOCTL_SCARD_SET_STATE_CHANGED: {
         KDPRINT("IOCTL_SCARD_SET_STATE_CHANGED\n");
         // Set all registered Change events to signalled
         SetAllEventItems(&fileExt->driverExt->eventList);
         status = STATUS_SUCCESS;
         WdfRequestComplete(Request, status);
         return;
      }
      case IOCTL_SCARD_SET_PCOIP_CONNECTED: {
         KDPRINT("IOCTL_SCARD_SET_PCOIP_CONNECTED\n");
         status = SetPCoIPStateConnected(Request, fileExt->driverExt);
         WdfRequestComplete(Request, status);
         return;
      }
      case IOCTL_SCARD_SET_PCOIP_CLOSED: {
         KDPRINT("IOCTL_SCARD_SET_PCOIP_CLOSED\n");
         SetPCoIPStateClosed(fileExt->driverExt, TRUE);
         status = STATUS_SUCCESS;
         WdfRequestComplete(Request, status);
         return;
      }
      default: {
         if (fileExt->ioDeviceType == ioDeviceCLIENT) {
            // I/O requests for the Client Virtual Channel
            status = WdfRequestForwardToIoQueue(Request, fileExt->driverExt->clientQueue);
         } else {
            // I/O requests for the Client Virtual Channel/WINLOGON
            status = WdfRequestForwardToIoQueue(Request, fileExt->driverExt->clientQueueWinLogon);
         }
         if (NT_SUCCESS(status)) {
            return;
         }
         // Failed to move request to the queue, return the error.
         break;
      }
      }
   }
   default: {
      // This IOCTL is not recognized!
      KDPRINT("Invalid file type, it should not be possible to end up here!\n");
      status = STATUS_INVALID_DEVICE_REQUEST;
      break;
   }
   }

   WdfRequestComplete(Request, status);
}


/* ------------------------------------------------------------------------------------
 *
 * Function:   ScardEvtIoDeviceControl
 *
 * Purpose:    WinSCARD IoDeviceControl, this callback will be invoked for
 *             all winscard.dll IO Calls from Applications.
 *
 * ------------------------------------------------------------------------------------
 */

_Use_decl_annotations_ VOID
ScardEvtIoDeviceControl(_In_ WDFQUEUE Queue, _In_ WDFREQUEST Request,
                        _In_ size_t OutputBufferLength, _In_ size_t InputBufferLength,
                        _In_ ULONG IoControlCode)
{
   NTSTATUS status = STATUS_SUCCESS;
   PCONTROL_FILE_EXTENSION fileExt = NULL;

   UNREFERENCED_PARAMETER(Queue);
   UNREFERENCED_PARAMETER(OutputBufferLength);
   UNREFERENCED_PARAMETER(InputBufferLength);
   UNREFERENCED_PARAMETER(IoControlCode);

   PAGED_CODE();

   // Get pointer to File Context
   fileExt = FileGetExtension(WdfRequestGetFileObject(Request));

#if DBG
   // Display type of IOCTL
   DisplayScardIOCTL(IoControlCode);
#endif

   switch (IoControlCode) {
   case SCARD_IOCTL_SMARTCARD_CONNECT: {
      // IOCTL Wait For SmartCard Redirection to be ready.
      KDPRINT("<SCARD_IOCTL_SMARTCARD_CONNECT>\n");

      // We will not return any data from this I/O call
      WdfRequestSetInformation(Request, 0);

      if (KeReadStateEvent(&fileExt->driverExt->clientRxReady)) {
         // Smartcard virtual channel is connected
         KDPRINT("SMARTCARD Client is Connected [%d]\n", fileExt->fileId);
         status = STATUS_SUCCESS;
      } else {
         /* Smartcard virtual channel is NOT connected
          * Re-queue the request to a paralell queue for separate completion
          */
         status = WdfRequestForwardToIoQueue(Request, fileExt->driverExt->scardConnectedQueue);
         if (NT_SUCCESS(status)) {
            // The request was successfully forwarded to the Queue.
            return;
         }
         /* winscard.dll requires that we return STATUS_DEVICE_NOT_CONNECTED
          * when we are in a disconnected state.
          */
         status = STATUS_DEVICE_NOT_CONNECTED;
      }
      break;
   }

   default: {
      // SCARD I/O package to send to the client.
      if (!RecognizedScardIOCTL(IoControlCode)) {
         // This SCARD IOCTL is not recognized!
         KDPRINT("####### This SCARD IOCTL is not recognized ####### [%0x08X]\n", IoControlCode);
         status = STATUS_INVALID_DEVICE_REQUEST;
      } else if (!KeReadStateEvent(&fileExt->driverExt->clientRxReady)) {
         /* The Virtual Channel is NOT connected, check if we are currently
          * setting up a PCoIP Session, if so waiting a while for Virtual
          * Channel to be connected before we return the request.
          */
         if (!KeReadStateEvent(&fileExt->driverExt->pcoipSessionConnected)) {
            KDPRINT("RETURN SMARTCARD NOT STARTED [%d]\n", fileExt->fileId);
            /* We are NOT connected, winscard.dll requires that we return
             * STATUS_DEVICE_NOT_CONNECTED when we are in a disconnected state.
             */
            status = STATUS_DEVICE_NOT_CONNECTED;
         } else {
            /* We have a PCoIP Session, wait a bit for the Smartcard VC to come up.
             */
            LARGE_INTEGER Timeout;
            PCONTROL_REQUEST_EXTENSION reqExt = RequestGetExtension(Request);

            KDPRINT("Virtual Channel is not started [%d] -- wait till it's up\n", fileExt->fileId);

            // Allow the request to be cancelled.
            WdfObjectReference(Request);
            status = WdfRequestMarkCancelableEx(Request, &EvtRequestCancel);

            if (NT_SUCCESS(status)) {
               /* We have a PCoIP Connection, but the smartcard VC is not up.
                * As long as we are in pending mode, then wait for the channel to come up.
                * The time out value is controlled by user mode (wsnm_scredir).
                */
               PVOID events[2];
               // Check once every 100ms if the call has been cancelled.
               Timeout.QuadPart = WDF_REL_TIMEOUT_IN_MS(100);

               events[0] = &fileExt->driverExt->clientRxReady;
               events[1] = &fileExt->driverExt->pcoipSCardNotPending;

               while (!reqExt->isCancelled) {
                  status = KeWaitForMultipleObjects(2, events, WaitAny, Executive, KernelMode,
                                                    FALSE, &Timeout, NULL);
                  if (status == STATUS_WAIT_0) {
                     // Virtual Channel is up
                     break;
                  } else if (status == STATUS_WAIT_1) {
                     // We no longer have a pending connection, abort
                     status = STATUS_DEVICE_NOT_CONNECTED;
                     break;
                  } else if (status != STATUS_TIMEOUT) {
                     // We failed to wait, abort
                     status = STATUS_DEVICE_NOT_CONNECTED;
                     break;
                  }
               }
               if (reqExt->isCancelled || WdfRequestUnmarkCancelable(Request) != STATUS_SUCCESS) {
                  WdfObjectDereference(Request);
                  return;
               }
               WdfObjectDereference(Request);
            } else {
               status = STATUS_CANCELLED;
            }

            if (status == STATUS_WAIT_0) {
               KDPRINT("Virtual Channel is now up. [%d]\n", fileExt->fileId);
               status = STATUS_SUCCESS;
            } else if (status != STATUS_CANCELLED) {
               /* We failed to wait, return that we are DISCONNETCED.
                */
               status = STATUS_DEVICE_NOT_CONNECTED;
            }
         }
      }

      if (status == STATUS_SUCCESS) {
         /* We appear to be connected, so move to the send queue
          * and signal the client that we have a new item.
          */
         if (KeWaitForMutexObject(&fileExt->driverExt->clientMutex, Executive, KernelMode, FALSE,
                                  NULL) != STATUS_SUCCESS) {
            KDPRINT("FAILED TO GET MUTEX [%d]\n", fileExt->fileId);
            status = STATUS_CANCELLED;
            break;
         } else {
            BOOLEAN ReQueued = FALSE;
            if (!KeReadStateEvent(&fileExt->driverExt->clientRxReady)) {
               KDPRINT("RETURN SMARTCARD NOT STARTED [%d]\n", fileExt->fileId);
               /* winscard.dll requires that we return STATUS_DEVICE_NOT_CONNECTED
                * when we are in a disconnected state.
                */
               status = STATUS_DEVICE_NOT_CONNECTED;
            } else {
               status = WdfRequestForwardToIoQueue(Request, fileExt->driverExt->clientSendQueue);
               if (NT_SUCCESS(status)) {
                  // Signal the user-mode Event.
                  KeSetEvent(fileExt->driverExt->clientDataEvent, 0, FALSE);
                  ReQueued = TRUE;
               }
            }
            KeReleaseMutex(&fileExt->driverExt->clientMutex, FALSE);

            if (NT_SUCCESS(status) && ReQueued) {
               // The request was successfully moved to the Send Queue.
               return;
            }
         }
      }

      WdfRequestSetInformation(Request, 0);

      break;
   }
   }

   //   TraceEvents(TRACE_LEVEL_VERBOSE, DBG_IOCTL, "Completing Request %p with status %X",
   //               Request, status );

   WdfRequestComplete(Request, status);
}


/* ------------------------------------------------------------------------------------
 *
 * Function:   ScardEvtIoDeviceControlWinLogon
 *
 * Purpose:    WinSCARD IoDeviceControl, this callback will be invoked for
 *             all winscard.dll IO Calls from WinLogon and LogonUI.
 *
 * ------------------------------------------------------------------------------------
 */

_Use_decl_annotations_ VOID
ScardEvtIoDeviceControlWinLogon(_In_ WDFQUEUE Queue, _In_ WDFREQUEST Request,
                                _In_ size_t OutputBufferLength, _In_ size_t InputBufferLength,
                                _In_ ULONG IoControlCode)
{
   NTSTATUS status = STATUS_SUCCESS;
   PCONTROL_FILE_EXTENSION fileExt = NULL;

   UNREFERENCED_PARAMETER(Queue);
   UNREFERENCED_PARAMETER(OutputBufferLength);
   UNREFERENCED_PARAMETER(InputBufferLength);
   UNREFERENCED_PARAMETER(IoControlCode);

   PAGED_CODE();

   // Get pointer to File Context
   fileExt = FileGetExtension(WdfRequestGetFileObject(Request));

#if DBG
   // Display type of IOCTL
   DisplayScardIOCTL(IoControlCode);
#endif

   switch (IoControlCode) {
   case SCARD_IOCTL_SMARTCARD_CONNECT: {
      // IOCTL Wait For SmartCard Redirection to be ready.
      KDPRINT("<SCARD_IOCTL_SMARTCARD_CONNECT>\n");

      // We will not return any data from this I/O call
      WdfRequestSetInformation(Request, 0);

      if (KeReadStateEvent(&fileExt->driverExt->clientRxReadyWinLogon)) {
         // Smartcard virtual channel connected
         KDPRINT("SMARTCARD STARTED [%d]\n", fileExt->fileId);
         status = STATUS_SUCCESS;
      } else {
         /* Smartcard virtual channel NOT connected
          * Re-queue the request to a paralell queue for separate completion
          */
         status = WdfRequestForwardToIoQueue(Request, fileExt->driverExt->scardConnectedQueue);
         if (NT_SUCCESS(status)) {
            // The request was successfully forwarded the Queue.
            return;
         }
         /* winscard.dll requires that we return STATUS_DEVICE_NOT_CONNECTED
          * when we are in a disconnected state.
          */
         status = STATUS_DEVICE_NOT_CONNECTED;
      }
      break;
   }

   default: {
      // SCARD I/O package to send to the client.
      if (!RecognizedScardIOCTL(IoControlCode)) {
         // This SCARD IOCTL is not recognized!
         KDPRINT("####### This SCARD IOCTL is not recognized ####### [%0x08X]\n", IoControlCode);
         status = STATUS_INVALID_DEVICE_REQUEST;
         break;
      } else if (!KeReadStateEvent(&fileExt->driverExt->clientRxReadyWinLogon)) {
         /*
          * We end up here when ever the emulated winlogon smartcard client
          * isn't running. In that situation we will block here until either
          * the emulated client, or a remote client is connected.
          */
         LARGE_INTEGER Timeout;
         PCONTROL_REQUEST_EXTENSION reqExt = RequestGetExtension(Request);

         KDPRINT("WINLOGON SMARTCARD NOT STARTED [%d] -- WAIT TILL IT'S UP\n", fileExt->fileId);

         // Allow the request to be cancelled.
         WdfObjectReference(Request);
         status = WdfRequestMarkCancelableEx(Request, &EvtRequestCancel);

         if (NT_SUCCESS(status)) {
            /*
             * The reader for the authentication channel is virtually always up, the only
             * time it's ever down is if node manager is stopped.
             *
             * If we wait for ever here, then it is causing the authentication elements
             * (winlogon/logonui) to be unresponsive any time when node manager isn't
             * running. - This is annoying when one is fiddeling with a system, and is
             * also likely to make any reboot after agent uninstall slow.
             *
             * To fix, we use a flag that is set when the driver is started (system booted),
             * and reset when the authentication reader is first started, if the flag is set,
             * then we wait indefinitely, that way we can make sure that we don't loose any
             * calls at boot time, but there after - we know that the system has been up and
             * we are fiddeling with it, so no reason to wait for ever, should we loose some
             * calls due to node manager being down, authetication may break - but so-what
             * you are tinkering with the agent, so what do you expect!
             */

            // Check once every 500ms if the call has been cancelled.
            Timeout.QuadPart = WDF_REL_TIMEOUT_IN_MS(500);

            if (fileExt->driverExt->isBooting) {
               // System is booting, wait infinitely
               while (!reqExt->isCancelled) {
                  status = KeWaitForSingleObject(&fileExt->driverExt->clientRxReadyWinLogon,
                                                 Executive, KernelMode, FALSE, &Timeout);
                  if (status == STATUS_WAIT_0) {
                     break;
                  }
               }
            } else {
               /* The node manager plugin has been running, but it's now stopped, so either
                * the system is being shutdown, the agent is being uninstalled, or some one
                * is fiddeling with it.. in order to not be completley unresponsive, only
                * wait for 5 secs before the call is cancelled by the driver.
                */
               int loopCount = 0;

               while (!reqExt->isCancelled && loopCount++ < 10) {
                  status = KeWaitForSingleObject(&fileExt->driverExt->clientRxReadyWinLogon,
                                                 Executive, KernelMode, FALSE, &Timeout);
                  if (status == STATUS_WAIT_0) {
                     break;
                  }
               }
            }
            if (reqExt->isCancelled || WdfRequestUnmarkCancelable(Request) != STATUS_SUCCESS) {
               WdfObjectDereference(Request);
               return;
            }
            WdfObjectDereference(Request);
         } else {
            status = STATUS_CANCELLED;
         }

         if (status == STATUS_WAIT_0) {
            // WinLogon reader now up
            KDPRINT("WinLogon reader now up. [%d]\n", fileExt->fileId);
            status = STATUS_SUCCESS;
         } else if (status != STATUS_CANCELLED) {
            // We failed to wait, return that we are DISCONNECTED.
            status = STATUS_DEVICE_NOT_CONNECTED;
         }
      } else {
         status = STATUS_SUCCESS;
      }

      if (NT_SUCCESS(status)) {
         /* We appear to be connected, so move to the send queue
          * and signal the client that we have a new item.
          */
         if (KeWaitForMutexObject(&fileExt->driverExt->clientMutexWinLogon, Executive, KernelMode,
                                  FALSE, NULL) != STATUS_SUCCESS) {
            KDPRINT("FAILED TO GET MUTEX [%d]\n", fileExt->fileId);
            status = STATUS_UNSUCCESSFUL;
            break;
         } else {
            BOOLEAN ReQueued = FALSE;
            if (!KeReadStateEvent(&fileExt->driverExt->clientRxReadyWinLogon)) {
               KDPRINT("WINLOGON SMARTCARD NOT STARTED [%d]\n", fileExt->fileId);
               /* We failed to dispatch for send, so node manager must
                * have been stopped this WILL trip up logonui, but what
                * else can we do!!
                */
               status = STATUS_DEVICE_NOT_CONNECTED;
            } else {
               status =
                  WdfRequestForwardToIoQueue(Request, fileExt->driverExt->clientSendQueueWinLogon);
               if (NT_SUCCESS(status)) {
                  // Signal the user-mode Event.
                  KeSetEvent(fileExt->driverExt->clientDataEventWinLogon, 0, FALSE);
                  ReQueued = TRUE;
               }
            }
            KeReleaseMutex(&fileExt->driverExt->clientMutexWinLogon, FALSE);

            if (NT_SUCCESS(status) && ReQueued) {
               // The request was successfully moved to the Send Queue.
               return;
            }
         }
      }

      WdfRequestSetInformation(Request, 0);

      break;
   }
   }

   //   TraceEvents(TRACE_LEVEL_VERBOSE, DBG_IOCTL, "Completing Request %p with status %X",
   //               Request, status );

   WdfRequestComplete(Request, status);
}


/* ------------------------------------------------------------------------------------
 *
 * Function:   ConnectedEvtIoDeviceControl
 *
 * Purpose:    This callback function serves request for the parallel queue,
 *             waiting for the virtual channel reader to be ready.
 *
 * ------------------------------------------------------------------------------------
 */

_Use_decl_annotations_ VOID
ConnectedEvtIoDeviceControl(_In_ WDFQUEUE Queue, _In_ WDFREQUEST Request,
                            _In_ size_t OutputBufferLength, _In_ size_t InputBufferLength,
                            _In_ ULONG IoControlCode)
{
   NTSTATUS status = STATUS_SUCCESS; // Assume success
   PCONTROL_FILE_EXTENSION fileExt = NULL;

   UNREFERENCED_PARAMETER(Queue);
   UNREFERENCED_PARAMETER(OutputBufferLength);
   UNREFERENCED_PARAMETER(InputBufferLength);
   UNREFERENCED_PARAMETER(IoControlCode);

   // WdfRequestAllocateTimer
   // ms-help://MS.VSCC.v90/MS.MSDNQTR.v90.en/kmdf_r/hh/KMDF_r/DFRequestObjectRef_ae292896-d156-44ae-b0cd-3f807fbc1765.xml.htm

   PAGED_CODE();

   // Get pointer to File Context
   fileExt = FileGetExtension(WdfRequestGetFileObject(Request));

   // We will not return any data from this I/O call
   WdfRequestSetInformation(Request, 0);

   switch (IoControlCode) {
   case SCARD_IOCTL_SMARTCARD_CONNECT: {
      // IOCTL Wait For SmartCard Redirection to be ready.

      if ((fileExt->ioDeviceType == ioDeviceSCARD) &&
          KeReadStateEvent(&fileExt->driverExt->clientRxReady)) {
         // Smartcard virtual channel connected
         status = STATUS_SUCCESS;
      } else if ((fileExt->ioDeviceType == ioDeviceSCARD_WINLOGON) &&
                 KeReadStateEvent(&fileExt->driverExt->clientRxReadyWinLogon)) {
         // Smartcard virtual channel connected
         status = STATUS_SUCCESS;
      } else {
         /* Smartcard virtual channel NOT connected
          * Wait here till the Smartcard Channel back to the client is up.
          * Return STATUS_DEVICE_NOT_CONNECTED if we failed to connect.
          */
         LARGE_INTEGER Timeout;
         PCONTROL_REQUEST_EXTENSION reqExt = RequestGetExtension(Request);

         KDPRINT("WAIT FOR SMARTCARD STARTED[%d]\n", fileExt->fileId);

         // Allow the request to be cancelled.
         WdfObjectReference(Request);
         status = WdfRequestMarkCancelableEx(Request, &EvtRequestCancel);

         if (NT_SUCCESS(status)) {
            if (fileExt->ioDeviceType == ioDeviceSCARD) {
               /* Application Channel.
                */
               if (!KeReadStateEvent(&fileExt->driverExt->pcoipSessionConnected)) {
                  // We Don't have a PCoIP Connection
                  status = STATUS_DEVICE_NOT_CONNECTED;
               } else {
                  /* We have a PCoIP Connection, but the smartcard VC is not up.
                   * As long as we are in pending mode, then wait for the channel to come up.
                   * The time out value is controlled by user mode (wsnm_scredir).
                   */
                  PVOID events[2];
                  // Check once every 100ms if the call has been cancelled.
                  Timeout.QuadPart = WDF_REL_TIMEOUT_IN_MS(100);

                  events[0] = &fileExt->driverExt->clientRxReady;
                  events[1] = &fileExt->driverExt->pcoipSCardNotPending;

                  while (!reqExt->isCancelled) {
                     status = KeWaitForMultipleObjects(2, events, WaitAny, Executive, KernelMode,
                                                       FALSE, &Timeout, NULL);
                     if (status == STATUS_WAIT_0) {
                        // Virtual Channel is up
                        break;
                     } else if (status == STATUS_WAIT_1) {
                        // We no longer have a pending connection, abort
                        status = STATUS_DEVICE_NOT_CONNECTED;
                        break;
                     } else if (status != STATUS_TIMEOUT) {
                        // We failed to wait, abort
                        status = STATUS_DEVICE_NOT_CONNECTED;
                        break;
                     }
                  }
               }
            } else {
               /* Winlogon Channel
                * Check once every 500ms if the call has been cancelled.
                */
               Timeout.QuadPart = WDF_REL_TIMEOUT_IN_MS(500);

               if (fileExt->driverExt->isBooting) {
                  // System is booting, wait infinitely
                  while (!reqExt->isCancelled) {
                     status = KeWaitForSingleObject(&fileExt->driverExt->clientRxReadyWinLogon,
                                                    Executive, KernelMode, FALSE, &Timeout);
                     if (status == STATUS_WAIT_0) {
                        break;
                     }
                  }
               } else {
                  /* The node manager plugin has been running, but it's now stopped, so either
                   * the system is being shutdown, the agent is being uninstalled, or some one
                   * is fiddeling with it.. in order to not be completley unresponsive, only
                   * wait for 5 sec before the call is cancelled by the driver.
                   */
                  int loopCount = 0;

                  while (!reqExt->isCancelled && loopCount++ < 10) {
                     status = KeWaitForSingleObject(&fileExt->driverExt->clientRxReadyWinLogon,
                                                    Executive, KernelMode, FALSE, &Timeout);
                     if (status == STATUS_WAIT_0) {
                        break;
                     }
                  }
               }
            }

            KDPRINT("WAIT COMPLETED status=0x%08X\n", status);
            if (reqExt->isCancelled || WdfRequestUnmarkCancelable(Request) != STATUS_SUCCESS) {
               WdfObjectDereference(Request);
               return; // Request is cancelled
            }
            WdfObjectDereference(Request);
            if (status != STATUS_SUCCESS) {
               KDPRINT(
                  "### WAIT FOR SMARTCARD STARTED -- TIMEOUT or CANCELLED [%d] status=0x%08X\n",
                  fileExt->fileId, status);
               if (status != STATUS_CANCELLED) {
                  status = STATUS_DEVICE_NOT_CONNECTED;
               }
            }
         } else if (status != STATUS_CANCELLED) {
            status = STATUS_DEVICE_NOT_CONNECTED;
         }

         KDPRINT("COMPLETED WAIT FOR SMARTCARD STARTED[%d] status=0x%08X\n", fileExt->fileId,
                 status);
      }
      break;
   }

   default: {
      status = STATUS_INVALID_DEVICE_REQUEST;
      break;
   }
   }

   //   TraceEvents(TRACE_LEVEL_VERBOSE, DBG_IOCTL, "Completing Request %p with status %X",
   //               Request, status );

   WdfRequestComplete(Request, status);
}


/* ------------------------------------------------------------------------------------
 *
 * Function:   EvtRequestCancel
 *
 * Purpose:    Callback for request that's been marked as cancelable
 *
 * ------------------------------------------------------------------------------------
 */
VOID
EvtRequestCancel(_In_ WDFREQUEST Request)
{
   PCONTROL_REQUEST_EXTENSION reqExt = NULL;

   // Get pointer to Request Context
   reqExt = RequestGetExtension(Request);
   if (reqExt != NULL) {
      reqExt->isCancelled = TRUE;
   }

   KDPRINT("CANCELLED REQUEST\n");

   WdfRequestComplete(Request, STATUS_CANCELLED);
}


/* ------------------------------------------------------------------------------------
 *
 * Function:   ClientEvtIoDeviceControl
 *
 * Purpose:    CLIENT IoDeviceControl
 *             Sends / Receives IO through the virtual Channel
 *
 * ------------------------------------------------------------------------------------
 */
_Use_decl_annotations_ VOID
ClientEvtIoDeviceControl(_In_ WDFQUEUE Queue, _In_ WDFREQUEST Request,
                         _In_ size_t OutputBufferLength, _In_ size_t InputBufferLength,
                         _In_ ULONG IoControlCode)
{
   NTSTATUS status = STATUS_SUCCESS; // Assume success
   PCONTROL_FILE_EXTENSION fileExt = NULL;
   PCHAR outBuf = NULL;
   size_t bufSize;

   UNREFERENCED_PARAMETER(Queue);
   UNREFERENCED_PARAMETER(OutputBufferLength);
   UNREFERENCED_PARAMETER(InputBufferLength);
   UNREFERENCED_PARAMETER(IoControlCode);

   PAGED_CODE();

   // Get pointer to File Context
   fileExt = FileGetExtension(WdfRequestGetFileObject(Request));

   switch (IoControlCode) {
   case IOCTL_SCARD_GET_REQUEST: {
      // IOCTL Get data to send through the virtual channel to the Client.
      WDFREQUEST sendRequest = NULL;
      WDF_REQUEST_PARAMETERS params;
      PCONTROL_REQUEST_EXTENSION reqExt = NULL;
      PCHAR sendInBuf = NULL;
      size_t sendInBufLen = 0;
      size_t bytesToReturn = 0;
      PSCARD_REQUEST scardSend = NULL;

      if (fileExt->ioDeviceType == ioDeviceCLIENT) {
         KDPRINT("<IOCTL_SCARD_GET_REQUEST APPLICNS>\n");
      } else if (fileExt->ioDeviceType == ioDeviceCLIENT_WINLOGON) {
         KDPRINT("<IOCTL_SCARD_GET_REQUEST WINLOGON>\n");
      } else {
         status = STATUS_INVALID_DEVICE_REQUEST;
         break;
      }

      WdfRequestSetInformation(Request, 0);

      // ensure the buffer is at least sizeof(SCARD_REQUEST)
      if (OutputBufferLength < sizeof(SCARD_REQUEST)) {
         WdfRequestComplete(Request, STATUS_BUFFER_TOO_SMALL);
         return;
      }

      status = WdfRequestRetrieveOutputBuffer(Request, 0, &outBuf, &bufSize);
      if (!NT_SUCCESS(status)) {
         break;
      }

      ASSERT(bufSize == OutputBufferLength);

      if (fileExt->ioDeviceType == ioDeviceCLIENT) {
         if (KeWaitForMutexObject(&fileExt->driverExt->clientMutex, Executive, KernelMode, FALSE,
                                  NULL) != STATUS_SUCCESS) {
            KDPRINT("FAILED TO GET THE REQUEST [%d]\n", fileExt->fileId);
            status = STATUS_UNSUCCESSFUL;
         } else {
            // Get the next request to send from the Send Queue.
            status =
               WdfIoQueueRetrieveNextRequest(fileExt->driverExt->clientSendQueue, &sendRequest);

            if (status == STATUS_NO_MORE_ENTRIES) {
               KDPRINT("STATUS_NO_MORE_ENTRIES.\n");
               KeClearEvent(fileExt->driverExt->clientDataEvent);
            } else if (NT_SUCCESS(status)) {
               if (WdfIoQueueGetState(fileExt->driverExt->clientSendQueue, NULL, NULL) &
                   WdfIoQueueNoRequests) {
                  KDPRINT("WdfIoQueueNoRequests.\n");
                  KeClearEvent(fileExt->driverExt->clientDataEvent);
               }
            }
            KeReleaseMutex(&fileExt->driverExt->clientMutex, FALSE);
         }
      } else { // WinLogon
         if (KeWaitForMutexObject(&fileExt->driverExt->clientMutexWinLogon, Executive, KernelMode,
                                  FALSE, NULL) != STATUS_SUCCESS) {
            KDPRINT("FAILED TO GET THE REQUEST [%d]\n", fileExt->fileId);
            status = STATUS_UNSUCCESSFUL;
         } else {
            // Get the next request to send from the Send Queue.
            status = WdfIoQueueRetrieveNextRequest(fileExt->driverExt->clientSendQueueWinLogon,
                                                   &sendRequest);

            if (status == STATUS_NO_MORE_ENTRIES) {
               KDPRINT("STATUS_NO_MORE_ENTRIES.\n");
               KeClearEvent(fileExt->driverExt->clientDataEventWinLogon);
            } else if (NT_SUCCESS(status)) {
               if (WdfIoQueueGetState(fileExt->driverExt->clientSendQueueWinLogon, NULL, NULL) &
                   WdfIoQueueNoRequests) {
                  KDPRINT("WdfIoQueueNoRequests.\n");
                  KeClearEvent(fileExt->driverExt->clientDataEventWinLogon);
               }
            }
            KeReleaseMutex(&fileExt->driverExt->clientMutexWinLogon, FALSE);
         }
      }

      if (!NT_SUCCESS(status)) {
         break;
      }

      // We have got an item, let's return it to the caller

      WDF_REQUEST_PARAMETERS_INIT(&params);
      WdfRequestGetParameters(sendRequest, &params);

      status = WdfRequestRetrieveInputBuffer(sendRequest, 0, &sendInBuf, &sendInBufLen);
      if (!NT_SUCCESS(status)) {
         status = WdfRequestRequeue(sendRequest);
         if (!NT_SUCCESS(status)) {
            // Oops what to do now, we failed to requeue the request!
            // We must cancel the request
            WdfRequestComplete(sendRequest, STATUS_CANCELLED);
         }
         break;
      }

      // Calculate how many bytes we need to return.
      bytesToReturn = sizeof *scardSend;
      if (sendInBufLen != 0) {
         bytesToReturn += (sendInBufLen - 1);
      }

      if (bufSize < bytesToReturn) {
         // The callers buffer is to small to return the request, re-queue the request
         status = WdfRequestRequeue(sendRequest);
         if (!NT_SUCCESS(status)) {
            // Oops what to do now, we failed to requeue the request!
            // We must cancel the request
            WdfRequestComplete(sendRequest, STATUS_CANCELLED);
         }
         status = STATUS_BUFFER_TOO_SMALL;

         // Signal the user mode Event that we have got data in the queue.
         if (fileExt->ioDeviceType == ioDeviceCLIENT) {
            KeSetEvent(fileExt->driverExt->clientDataEvent, 0, FALSE);
         } else { // WinLogon
            KeSetEvent(fileExt->driverExt->clientDataEventWinLogon, 0, FALSE);
         }

         break;
      }

      // Get pointer to Request Context
      reqExt = RequestGetExtension(sendRequest);

      /* Generate Request Id used to tie the completion
       * from the client back to the request
       */
      reqExt->completionId = InterlockedIncrement(&fileExt->driverExt->drvRequestId);
      if (reqExt->completionId == 0) {
         reqExt->completionId = InterlockedIncrement(&fileExt->driverExt->drvRequestId);
      }

      KDPRINT("SEND TO CLIENT CompletionId = %d\n", reqExt->completionId);

      scardSend = (PSCARD_REQUEST)outBuf;

      scardSend->completionId = reqExt->completionId;
      scardSend->ioControlCode = params.Parameters.DeviceIoControl.IoControlCode;
      // This type cast is to allow a 64 bit driver with a 32 bit client(app)
      scardSend->inputBufferLength =
         (unsigned long)params.Parameters.DeviceIoControl.InputBufferLength;
      scardSend->outputBufferLength =
         (unsigned long)params.Parameters.DeviceIoControl.OutputBufferLength;

      RtlCopyMemory(scardSend->buffer, sendInBuf, sendInBufLen);

      /* Assign the length of the data copied to IoStatus.Information
       * of the request and complete the request.
       */
      WdfRequestSetInformation(Request, bytesToReturn);

      // Enter Queue Mutex
      if (fileExt->ioDeviceType == ioDeviceCLIENT) {
         // Ensure only one thread is working on this queue at any one time
         if (KeWaitForMutexObject(&fileExt->driverExt->clientCompletionMutex, Executive, KernelMode,
                                  FALSE, NULL) != STATUS_SUCCESS) {
            status = STATUS_UNSUCCESSFUL;
         } else {
            // Move the request to the Result/Completion Queue
            status =
               WdfRequestForwardToIoQueue(sendRequest, fileExt->driverExt->clientCompletionQueue);
            KeReleaseMutex(&fileExt->driverExt->clientCompletionMutex, FALSE);
         }
      } else {
         // Ensure only one thread is working on this queue at any one time
         if (KeWaitForMutexObject(&fileExt->driverExt->clientCompletionMutexWinLogon, Executive,
                                  KernelMode, FALSE, NULL) != STATUS_SUCCESS) {
            status = STATUS_UNSUCCESSFUL;
         } else {
            // Move the request to the Winlogon Result/Completion Queue
            status = WdfRequestForwardToIoQueue(sendRequest,
                                                fileExt->driverExt->clientCompletionQueueWinLogon);
            KeReleaseMutex(&fileExt->driverExt->clientCompletionMutexWinLogon, FALSE);
         }
      }

      if (!NT_SUCCESS(status)) {
         // Failed to move the request to the result Queue.
         // Complete the original request (Passing back failure code)
         WdfRequestSetInformation(sendRequest, 0);
         WdfRequestComplete(sendRequest, status);
         WdfRequestSetInformation(Request, 0);
      }

      break;
   }

   case IOCTL_SCARD_SEND_COMPLETION: {
      // IOCTL Return response from data sent through the virtual channel.
      PCONTROL_REQUEST_EXTENSION reqExt = NULL;
      size_t inBufLen, outBufLen;
      PVOID inBuf;
      PSCARD_COMPLETION scardResult = NULL;

      WDFREQUEST resultRequest = NULL;
      WDFREQUEST prevTagRequest = NULL;
      WDFREQUEST tagRequest = NULL;
      size_t bytesReturned = 0;

      if (fileExt->ioDeviceType == ioDeviceCLIENT) {
         KDPRINT("COMPLETION FROM CLIENT APPLICNS:\n");
      } else if (fileExt->ioDeviceType == ioDeviceCLIENT_WINLOGON) {
         KDPRINT("COMPLETION FROM CLIENT WINLOGON:\n");
      } else {
         status = STATUS_INVALID_DEVICE_REQUEST;
         break;
      }

      WdfRequestSetInformation(Request, 0);

      // Get Input Buffer from the Client
      status = WdfRequestRetrieveInputBuffer(Request, 0, &inBuf, &inBufLen);
      if (!NT_SUCCESS(status)) {
         break;
      }

      ASSERT(inBufLen == InputBufferLength);

      scardResult = (PSCARD_COMPLETION)inBuf;

      // Calculate how many bytes we should have received
      bytesReturned = sizeof(SCARD_COMPLETION);
      if (scardResult->outputBufferLength != 0) {
         bytesReturned += (scardResult->outputBufferLength - 1);
      }

      if (inBufLen != bytesReturned) {
         KDPRINT("!! INVALID INPUT BUFFER(SIZE) !!\n");
         status = STATUS_INVALID_PARAMETER;
         break;
      }

      KDPRINT("RECIEVED FROM CLIENT CompletionId = %d\n", scardResult->completionId);

      // Enter Queue Mutex
      if (fileExt->ioDeviceType == ioDeviceCLIENT) {
         // Ensure only one thread is working on this queue at any one time
         if (KeWaitForMutexObject(&fileExt->driverExt->clientCompletionMutex, Executive, KernelMode,
                                  FALSE, NULL) != STATUS_SUCCESS) {
            status = STATUS_UNSUCCESSFUL;
            break;
         }
      } else {
         // Ensure only one thread is working on this queue at any one time
         if (KeWaitForMutexObject(&fileExt->driverExt->clientCompletionMutexWinLogon, Executive,
                                  KernelMode, FALSE, NULL) != STATUS_SUCCESS) {
            status = STATUS_UNSUCCESSFUL;
            break;
         }
      }

      // Scan through the list of requests waiting for completion
      for (;;) {
         if (fileExt->ioDeviceType == ioDeviceCLIENT) {
            status = WdfIoQueueFindRequest(fileExt->driverExt->clientCompletionQueue,
                                           prevTagRequest, NULL, NULL, &tagRequest);
         } else { // WinLogon
            status = WdfIoQueueFindRequest(fileExt->driverExt->clientCompletionQueueWinLogon,
                                           prevTagRequest, NULL, NULL, &tagRequest);
         }
         if (prevTagRequest) {
            WdfObjectDereference(prevTagRequest);
         }
         if (status == STATUS_NO_MORE_ENTRIES) {
            status = STATUS_UNSUCCESSFUL;
            break;
         }
         if (status == STATUS_NOT_FOUND) {
            /* The prevTagRequest request has disappeared from the
             * queue. There might be other requests that match
             * the criteria, so restart the search.
             */
            prevTagRequest = tagRequest = NULL;
            continue;
         }
         if (!NT_SUCCESS(status)) {
            status = STATUS_UNSUCCESSFUL;
            break;
         }

         // Get pointer to Request Context
         reqExt = RequestGetExtension(tagRequest);

         if (reqExt == NULL) {
            KDPRINT("!! NO REQUEST CONTEXT !!\n");
            status = STATUS_UNSUCCESSFUL;
            break;
         } else {
            KDPRINT("Check CompletionId = %d\n", reqExt->completionId);
         }

         if (reqExt->completionId == scardResult->completionId) {
            // Found a match. Retrieve the request from the queue.
            if (fileExt->ioDeviceType == ioDeviceCLIENT) {
               status = WdfIoQueueRetrieveFoundRequest(fileExt->driverExt->clientCompletionQueue,
                                                       tagRequest, &resultRequest);
            } else { // WinLogon
               status = WdfIoQueueRetrieveFoundRequest(
                  fileExt->driverExt->clientCompletionQueueWinLogon, tagRequest, &resultRequest);
            }
            WdfObjectDereference(tagRequest);
            break;
         } else {
            /* This request is not the correct one. Drop the reference
             * on the tagRequest after the driver obtains the next request.
             */
            prevTagRequest = tagRequest;
            continue;
         }
      }

      // Release Queue Mutex
      if (fileExt->ioDeviceType == ioDeviceCLIENT) {
         KeReleaseMutex(&fileExt->driverExt->clientCompletionMutex, FALSE);
      } else {
         KeReleaseMutex(&fileExt->driverExt->clientCompletionMutexWinLogon, FALSE);
      }

      if (NT_SUCCESS(status)) {
         /* The request was located in the queue.
          * Write the Result data to the I/O request
          */

         /* Get Output Buffer from the WinScard */
         status = WdfRequestRetrieveOutputBuffer(resultRequest, 0, &outBuf, &outBufLen);
         if (!NT_SUCCESS(status)) {
            KDPRINT("!! FAILED TO RETRIEVE OUTPUT BUFFER !!\n");
            break;
         }

         status = STATUS_SUCCESS;

         if (outBufLen < scardResult->outputBufferLength) {
            /* Result buffer to small!!!
             * We will complete both request at this point,
             * as unpractical to do any thing else.
             */
            status = STATUS_BUFFER_TOO_SMALL;

            KDPRINT("!! STATUS_BUFFER_TOO_SMALL !!\n");

            WdfRequestSetInformation(resultRequest, 0);
            WdfRequestComplete(resultRequest, status);
         } else {
            // Return the result to WINSCARD
            KDPRINT("Return the result to WINSCARD %d bytes\n", scardResult->outputBufferLength);
            RtlCopyMemory(outBuf, scardResult->buffer, scardResult->outputBufferLength);
            WdfRequestSetInformation(resultRequest, scardResult->outputBufferLength);
            WdfRequestComplete(resultRequest, scardResult->status);
         }
      } else {
         KDPRINT("#### Could not locate the item in the Queue can not complete the request ####\n");
      }
      break;
   }
   case IOCTL_SCARD_GET_SENT_REQUEST: {
      /* IOCTL Get a request that has already been retieved using
       * IOCTL_SCARD_GET_REQUEST. You can retieve a specific, or
       * the next in line (0) requets.
       * Note, if you ask for next more than once, you will get
       * the same one again unless it's been canceled or completed
       * since the last call.
       */
      WDFREQUEST sendRequest = NULL;
      unsigned long completionId = 0;
      size_t inBufLen;
      PVOID inBuf;

      if (fileExt->ioDeviceType == ioDeviceCLIENT) {
         KDPRINT("RETRIEVE_SENT_REQUEST FROM CLIENT:\n");
      } else if (fileExt->ioDeviceType == ioDeviceCLIENT_WINLOGON) {
         KDPRINT("RETRIEVE_SENT_REQUEST FROM WINLOGON:\n");
      } else {
         status = STATUS_INVALID_DEVICE_REQUEST;
         break;
      }

      WdfRequestSetInformation(Request, 0);

      // Get Input Buffer from the Client
      status = WdfRequestRetrieveInputBuffer(Request, 0, &inBuf, &inBufLen);
      if (!NT_SUCCESS(status)) {
         break;
      }
      if (inBufLen < sizeof(SCARD_REQUEST)) {
         KDPRINT("!! INVALID INPUT BUFFER(SIZE) !!\n");
         status = STATUS_INVALID_PARAMETER;
         break;
      }

      completionId = ((PSCARD_REQUEST)inBuf)->completionId;

      status = WdfRequestRetrieveOutputBuffer(Request, 0, &outBuf, &bufSize);
      if (!NT_SUCCESS(status)) {
         break;
      }

      ASSERT(bufSize == OutputBufferLength);

      if (OutputBufferLength < sizeof(SCARD_REQUEST)) {
         status = STATUS_BUFFER_TOO_SMALL;
         break;
      }

      // Enter Queue Mutex
      if (fileExt->ioDeviceType == ioDeviceCLIENT) {
         // Ensure only one thread is working on this queue at any one time
         if (KeWaitForMutexObject(&fileExt->driverExt->clientCompletionMutex, Executive, KernelMode,
                                  FALSE, NULL) != STATUS_SUCCESS) {
            status = STATUS_UNSUCCESSFUL;
            break;
         }
      } else {
         // Ensure only one thread is working on this queue at any one time
         if (KeWaitForMutexObject(&fileExt->driverExt->clientCompletionMutexWinLogon, Executive,
                                  KernelMode, FALSE, NULL) != STATUS_SUCCESS) {
            status = STATUS_UNSUCCESSFUL;
            break;
         }
      }

      if (completionId == 0) {
         // Get the next item in line
         if (fileExt->ioDeviceType == ioDeviceCLIENT) {
            // Get the next request to send from the Completion Queue.
            status = WdfIoQueueRetrieveNextRequest(fileExt->driverExt->clientCompletionQueue,
                                                   &sendRequest);
            if (status == STATUS_NO_MORE_ENTRIES) {
               KDPRINT("STATUS_NO_MORE_ENTRIES.\n");
            }
         } else { // WinLogon
            // Get the next request to send from the Completion Queue.
            status = WdfIoQueueRetrieveNextRequest(
               fileExt->driverExt->clientCompletionQueueWinLogon, &sendRequest);
            if (status == STATUS_NO_MORE_ENTRIES) {
               KDPRINT("STATUS_NO_MORE_ENTRIES.\n");
            }
         }
      } else {
         // Get a specific item
         if (fileExt->ioDeviceType == ioDeviceCLIENT) {
            // Get the request to send from the Completion Queue.
            status = FindRequestInQueue(fileExt->driverExt->clientCompletionQueue, completionId,
                                        &sendRequest);
         } else { // WinLogon
            // Get the request to send from the Completion Queue.
            status = FindRequestInQueue(fileExt->driverExt->clientCompletionQueueWinLogon,
                                        completionId, &sendRequest);
         }
      }

      // Release Queue Mutex
      if (fileExt->ioDeviceType == ioDeviceCLIENT) {
         KeReleaseMutex(&fileExt->driverExt->clientCompletionMutex, FALSE);
      } else {
         KeReleaseMutex(&fileExt->driverExt->clientCompletionMutexWinLogon, FALSE);
      }

      if (NT_SUCCESS(status)) {
         // We have got an item, let's return it to the caller
         status = PlaceRequestInOutputBuffer(Request,            // Request to complete
                                             OutputBufferLength, // Length of Output buffer
                                             sendRequest,        // The request to return.
                                             TRUE // Leave/Return the request to the queue
         );
      }

      // Job done, complete the request
      break;
   }
   case IOCTL_SCARD_CANCEL_SENT_REQUEST: {
      // Cancel ALL sent requests that are waiting for completion
      KDPRINT("<IOCTL_SCARD_CANCEL_SENT_REQUEST>\n");

      if (fileExt->ioDeviceType == ioDeviceCLIENT) {
         // Ensure only one thread is working on this queue at any one time
         if (KeWaitForMutexObject(&fileExt->driverExt->clientCompletionMutex, Executive, KernelMode,
                                  FALSE, NULL) != STATUS_SUCCESS) {
            status = STATUS_UNSUCCESSFUL;
         } else {
            WdfIoQueuePurgeSynchronously(fileExt->driverExt->clientCompletionQueue);
            WdfIoQueueStart(fileExt->driverExt->clientCompletionQueue);
            KeReleaseMutex(&fileExt->driverExt->clientCompletionMutex, FALSE);
            status = STATUS_SUCCESS;
         }
      } else {
         // Ensure only one thread is working on this queue at any one time
         if (KeWaitForMutexObject(&fileExt->driverExt->clientCompletionMutexWinLogon, Executive,
                                  KernelMode, FALSE, NULL) != STATUS_SUCCESS) {
            status = STATUS_UNSUCCESSFUL;
         } else {
            WdfIoQueuePurgeSynchronously(fileExt->driverExt->clientCompletionQueueWinLogon);
            WdfIoQueueStart(fileExt->driverExt->clientCompletionQueueWinLogon);
            KeReleaseMutex(&fileExt->driverExt->clientCompletionMutexWinLogon, FALSE);
            status = STATUS_SUCCESS;
         }
      }

      WdfRequestSetInformation(Request, 0);
      break;
   }

   default: {
      WdfRequestSetInformation(Request, 0);
      status = STATUS_INVALID_DEVICE_REQUEST;
      TraceEvents(TRACE_LEVEL_ERROR, DBG_IOCTL, "ERROR: unrecognized IOCTL %x\n", IoControlCode);
      break;
   }
   }

   //   TraceEvents(TRACE_LEVEL_VERBOSE, DBG_IOCTL, "Completing Request %p with status %X",
   //               Request, status );

   WdfRequestComplete(Request, status);
}

/* Locate a request in the queue
 */
NTSTATUS
FindRequestInQueue(_In_ WDFQUEUE Queue, _In_ unsigned long completionId,
                   _Out_ WDFREQUEST *OutRequest)
{
   NTSTATUS status;
   PCONTROL_REQUEST_EXTENSION reqExt = NULL;
   WDFREQUEST resultRequest = {0};
   WDFREQUEST prevTagRequest = {0};
   WDFREQUEST tagRequest = {0};

   // Scan through the list of requests waiting for completion
   for (;;) {
      status = WdfIoQueueFindRequest(Queue, prevTagRequest, NULL, NULL, &tagRequest);
      if (prevTagRequest) {
         WdfObjectDereference(prevTagRequest);
      }
      if (status == STATUS_NO_MORE_ENTRIES) {
         status = STATUS_UNSUCCESSFUL;
         break;
      }
      if (status == STATUS_NOT_FOUND) {
         /* The prevTagRequest request has disappeared from the
          * queue. There might be other requests that match
          * the criteria, so restart the search.
          */
         prevTagRequest = tagRequest = NULL;
         continue;
      }
      if (!NT_SUCCESS(status)) {
         status = STATUS_UNSUCCESSFUL;
         break;
      }

      // Get pointer to Request Context
      reqExt = RequestGetExtension(tagRequest);

      if (reqExt == NULL) {
         KDPRINT("!! NO REQUEST CONTEXT !!\n");
         status = STATUS_UNSUCCESSFUL;
         break;
      } else {
         KDPRINT("Check CompletionId = %d\n", reqExt->completionId);
      }

      if (reqExt->completionId == completionId) {
         // Found a match. Retrieve the request from the queue.
         status = WdfIoQueueRetrieveFoundRequest(Queue, tagRequest, &resultRequest);
         WdfObjectDereference(tagRequest);
         break;
      } else {
         /* This request is not the correct one. Drop the reference
          * on the tagRequest after the driver obtains the next request.
          */
         prevTagRequest = tagRequest;
         continue;
      }
   }

   if (NT_SUCCESS(status)) {
      /* The request was located in the queue. */
      *OutRequest = resultRequest;
   } else {
      KDPRINT("#### Could not locate the item in the Queue\n");
   }

   return status;
}


// Copy request data to the output buffer
// The request is Requeued if we failed to return it for waht ever reason
NTSTATUS
PlaceRequestInOutputBuffer(
   _In_ WDFREQUEST Request,        // Request to complete
   _In_ size_t OutputBufferLength, // Length of Output buffer
   _In_ WDFREQUEST sendRequest,    // The request to return.
   _In_ BOOLEAN requeRequest       // Requeue the request once the data has been copied
)
{
   NTSTATUS status;
   PCONTROL_FILE_EXTENSION fileExt = NULL;
   WDF_REQUEST_PARAMETERS params;
   PCONTROL_REQUEST_EXTENSION reqExt = NULL;
   PCHAR outBuf = NULL;
   size_t bufSize;
   PCHAR sendInBuf = NULL;
   size_t sendInBufLen = 0;
   size_t bytesToReturn = 0;
   PSCARD_REQUEST scardSend = NULL;

   // Get pointer to File Context
   fileExt = FileGetExtension(WdfRequestGetFileObject(Request));

   WDF_REQUEST_PARAMETERS_INIT(&params);
   WdfRequestGetParameters(sendRequest, &params);

   // Get details of the request we want to read
   status = WdfRequestRetrieveInputBuffer(sendRequest, 0, &sendInBuf, &sendInBufLen);
   if (!NT_SUCCESS(status)) {
      if (WdfRequestRequeue(sendRequest) != STATUS_SUCCESS) {
         // Oops what to do now, we failed to requeue the request!
         // We must cancel the request
         WdfRequestComplete(sendRequest, STATUS_CANCELLED);
      }
      return status;
   }

   // Calculate how many bytes we need to return.
   bytesToReturn = sizeof *scardSend;
   if (sendInBufLen != 0) {
      bytesToReturn += (sendInBufLen - 1);
   }

   // Get the output buffer, and ensure we have suficcient space
   status = WdfRequestRetrieveOutputBuffer(Request, 0, &outBuf, &bufSize);
   if (!NT_SUCCESS(status)) {
      if (WdfRequestRequeue(sendRequest) != STATUS_SUCCESS) {
         // Oops what to do now, we failed to requeue the request!
         // We must cancel the request
         WdfRequestComplete(sendRequest, STATUS_CANCELLED);
      }
      return status;
   }

   ASSERT(bufSize == OutputBufferLength);

   if (!OutputBufferLength) {
      if (WdfRequestRequeue(sendRequest) != STATUS_SUCCESS) {
         // Oops what to do now, we failed to requeue the request!
         // We must cancel the request
         WdfRequestComplete(sendRequest, STATUS_CANCELLED);
      }
      return STATUS_BUFFER_TOO_SMALL;
   }

   if (bufSize < bytesToReturn) {
      // The callers buffer is to small to return the request, re-queue the request
      if (WdfRequestRequeue(sendRequest) != STATUS_SUCCESS) {
         // Oops what to do now, we failed to requeue the request!
         // We must cancel the request
         WdfRequestComplete(sendRequest, STATUS_CANCELLED);
      }
      return STATUS_BUFFER_TOO_SMALL;
   }

   // Get pointer to Request Context
   reqExt = RequestGetExtension(sendRequest);

   if (reqExt == NULL) {
      KDPRINT("ERROR NO REQUEST CONTEXT!!\n");
      if (WdfRequestRequeue(sendRequest) != STATUS_SUCCESS) {
         // Oops what to do now, we failed to requeue the request!
         // We must cancel the request
         WdfRequestComplete(sendRequest, STATUS_CANCELLED);
      }
      return STATUS_UNSUCCESSFUL;
   }

   scardSend = (PSCARD_REQUEST)outBuf;

   scardSend->completionId = reqExt->completionId;
   scardSend->ioControlCode = params.Parameters.DeviceIoControl.IoControlCode;
   // This type cast is to allow a 64 bit driver with a 32 bit client(app)
   scardSend->inputBufferLength =
      (unsigned long)params.Parameters.DeviceIoControl.InputBufferLength;
   scardSend->outputBufferLength =
      (unsigned long)params.Parameters.DeviceIoControl.OutputBufferLength;

   RtlCopyMemory(scardSend->buffer, sendInBuf, sendInBufLen);

   if (requeRequest) {
      // Put the request back in the queue
      if (WdfRequestRequeue(sendRequest) != STATUS_SUCCESS) {
         // Oops what to do now, we failed to requeue the request!
         // We must cancel the request
         WdfRequestComplete(sendRequest, STATUS_CANCELLED);
      }
   }

   /* Assign the length of the data copied to IoStatus.Information
    * of the request.
    */
   WdfRequestSetInformation(Request, bytesToReturn);

   return status;
}


/* ------------------------------------------------------------------------------------
 *
 * Function:   CompareFilename
 *
 * Purpose:    Case insensitive string compare, first string is a unicode string,
 *             2nd string is a '\0' terminated string
 *
 * ------------------------------------------------------------------------------------
 */

BOOLEAN
CompareFilename(_In_ PUNICODE_STRING FileName, _In_ LPCWSTR szFileName)
{
   UNICODE_STRING fileNameCmp;
   RtlInitUnicodeString(&fileNameCmp, szFileName);
   return RtlEqualUnicodeString(FileName, &fileNameCmp, TRUE);
}


/* ------------------------------------------------------------------------------------
 *
 * Function:   DisplayScardIOCTL
 *
 * Purpose:    Debug Output type of IOCTL
 *
 * ------------------------------------------------------------------------------------
 */

void
DisplayScardIOCTL(_In_ unsigned long IoControlCode)
{
   UNREFERENCED_PARAMETER(IoControlCode);
   KDPRINT("IoControlCode %s\n", GetScardIOCTLtext(IoControlCode));
}


static NTSTATUS
ExtractHandleFromIoctlBuffer(_In_ WDFREQUEST Request, _Out_ HANDLE *pHandle)
{
   NTSTATUS status;
   size_t inBufLen;
   PVOID inBuf;

   status = WdfRequestRetrieveInputBuffer(Request, sizeof(REGISTER_EVENT_32), &inBuf, &inBufLen);
   if (!NT_SUCCESS(status)) {
      KDPRINT("Unable to retrieve the input buffer\n");
      return status;
   }

   // native case (32-on-32 or 64-on-64):
   if (inBufLen == sizeof(REGISTER_EVENT)) {
      // Parse input buffer as REGISTER_EVENT struct (containing native handle).
      PREGISTER_EVENT registerEvent = (PREGISTER_EVENT)inBuf;

      if (registerEvent->hEvent == NULL) {
         KDPRINT("Handle == NULL\n");
         return STATUS_INVALID_HANDLE;
      }

      // Got a valid-looking handle; return it.
      *pHandle = registerEvent->hEvent;
      KDPRINT("ExtractHandle: found native handle %p\n", *pHandle);
      return STATUS_SUCCESS;
   }

#ifdef _WIN64
   // WOW64 case (32-on-64):
   if (IoIs32bitProcess(WdfRequestWdmGetIrp(Request))) {
      if (inBufLen == sizeof(REGISTER_EVENT_32)) {
         // Parse input buffer as REGISTER_EVENT_32 struct (containing 32-bit
         // handle which we need to zero-extend).
         PREGISTER_EVENT_32 registerEvent = (PREGISTER_EVENT_32)inBuf;

         if (registerEvent->hEvent == NULL) {
            KDPRINT("Handle == NULL\n");
            return STATUS_INVALID_HANDLE;
         }

         // Got a valid-looking handle; return it.
         *pHandle = registerEvent->hEvent;
#   pragma warning(suppress : 6273) // warning for pointer cast - we'll ignore it
         KDPRINT("ExtractHandle: found WOW64 handle 0x%x -> %p\n", registerEvent->hEvent, *pHandle);
         return STATUS_SUCCESS;
      }
   }
#endif

   // Didn't match any handle size we know how to accept; so fail.
   return STATUS_INVALID_PARAMETER;
}