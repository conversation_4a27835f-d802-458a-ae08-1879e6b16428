﻿<?xml version="1.0" encoding="utf-8"?>

<WixLocalization Culture="de-de" Codepage="1252" xmlns="http://schemas.microsoft.com/wix/2006/localization">
   <String Id="LANGID">1031</String>

   <!-- Installshield Strings -->
   <String Id="IDS_COMPLUS_PROGRESSTEXT_COST">Kostenberechnung der COM+-Anwendung: [1]</String>
   <String Id="IDS_COMPLUS_PROGRESSTEXT_INSTALL">COM+-Anwendung wird installiert: [1]</String>
   <String Id="IDS_COMPLUS_PROGRESSTEXT_UNINSTALL">COM+-Anwendung wird deinstalliert: [1]</String>
   <String Id="IDS_PROGMSG_IIS_CREATEAPPPOOL">Anwendungspool %s wird erstellt</String>
   <String Id="IDS_PROGMSG_IIS_CREATEAPPPOOLS">Anwendungspools werden erstellt...</String>
   <String Id="IDS_PROGMSG_IIS_CREATEVROOT">Virtuelles IIS-Verzeichnis %s wird erstellt</String>
   <String Id="IDS_PROGMSG_IIS_CREATEVROOTS">Virtuelle IIS-Verzeichnisse werden erstellt...</String>
   <String Id="IDS_PROGMSG_IIS_CREATEWEBSERVICEEXTENSION">Webdiensterweiterung wird erstellt</String>
   <String Id="IDS_PROGMSG_IIS_CREATEWEBSERVICEEXTENSIONS">Webdiensterweiterungen werden erstellt...</String>
   <String Id="IDS_PROGMSG_IIS_EXTRACT">Informationen für virtuelle IIS-Verzeichnisse werden extrahiert...</String>
   <String Id="IDS_PROGMSG_IIS_EXTRACTDONE">Informationen für virtuelle IIS-Verzeichnisse wurden extrahiert...</String>
   <String Id="IDS_PROGMSG_IIS_REMOVEAPPPOOL">Anwendungspool wird entfernt</String>
   <String Id="IDS_PROGMSG_IIS_REMOVEAPPPOOLS">Anwendungspools werden entfernt...</String>
   <String Id="IDS_PROGMSG_IIS_REMOVESITE">Website auf Port %d wird entfernt</String>
   <String Id="IDS_PROGMSG_IIS_REMOVEVROOT">Virtuelles IIS-Verzeichnis %s wird entfernt</String>
   <String Id="IDS_PROGMSG_IIS_REMOVEVROOTS">Virtuelle IIS-Verzeichnisse werden entfernt...</String>
   <String Id="IDS_PROGMSG_IIS_REMOVEWEBSERVICEEXTENSION">Webdiensterweiterung wird entfernt</String>
   <String Id="IDS_PROGMSG_IIS_REMOVEWEBSERVICEEXTENSIONS">Webdiensterweiterungen werden entfernt...</String>
   <String Id="IDS_PROGMSG_IIS_ROLLBACKAPPPOOLS">Rollback für Anwendungspools wird durchgeführt...</String>
   <String Id="IDS_PROGMSG_IIS_ROLLBACKVROOTS">Änderungen an virtuellem Verzeichnis und Website werden zurückgesetzt...</String>
   <String Id="IDS_PROGMSG_IIS_ROLLBACKWEBSERVICEEXTENSIONS">Rollback für Webdiensterweiterungen wird durchgeführt...</String>
   <String Id="IDS_PROGMSG_XML_COSTING">Speicherplatzanalyse für XML-Dateien...</String>
   <String Id="IDS_PROGMSG_XML_CREATE_FILE">XML-Datei %s wird erstellt...</String>
   <String Id="IDS_PROGMSG_XML_FILES">Änderungen an XML-Datei werden durchgeführt...</String>
   <String Id="IDS_PROGMSG_XML_REMOVE_FILE">XML-Datei %s wird entfernt...</String>
   <String Id="IDS_PROGMSG_XML_ROLLBACK_FILES">Änderungen an der XML-Datei werden zurückgesetzt...</String>
   <String Id="IDS_PROGMSG_XML_UPDATE_FILE">XML-Datei %s wird aktualisiert...</String>


   <!-- LaunchCondition Error Messages -->
   <String Id="MINIMUM_REQUIRED_OS">Dieses Produkt kann nur unter Windows 10, Windows Server 2016 oder neueren Betriebssystemen installiert werden.</String>
   <String Id="DENY_INSTALL_DOMAIN_CONTROLLER">Dieses Produkt kann nicht auf einem Domänencontroller installiert werden.</String>
   <String Id="NEED_ADMIN">Sie benötigen Administratorrechte, um diese Software zu installieren/deinstallieren.</String>


   <!-- Feature Table -->
   <String Id="FEATURE_NAME_CORE">Kern</String>
   <String Id="FEATURE_DESC_CORE">Kernfunktionalität von [ProductName]</String>
   <String Id="FEATURE_NAME_CORRETTO">Corretto</String>
   <String Id="FEATURE_DESC_CORRETTO">Kernfunktionalität von [ProductName] mit der Corretto JDK-Distribution</String>
   <String Id="FEATURE_NAME_BELLSOFT">Bellsoft</String>
   <String Id="FEATURE_DESC_BELLSOFT">Kernfunktionalität von [ProductName] mit der Bellsoft JDK-Distribution</String>
   <String Id="FEATURE_NAME_RDSH3D">3D-RDSH</String>
   <String Id="FEATURE_DESC_RDSH3D">Diese Funktion aktiviert die Hardware-3D-Beschleunigung in RDSH- und physischen PC-Sitzungen.</String>
   <String Id="FEATURE_NAME_CLIENTDRIVEREDIRECTION">Clientlaufwerkumleitung</String>
   <String Id="FEATURE_DESC_CLIENTDRIVEREDIRECTION">Erlauben Sie Horizon Clients die Freigabe lokaler Laufwerke für Remote-Desktops und -Anwendungen. Falls nicht installiert, werden die Funktionen „Kopieren/Einfügen“ sowie „Drag &amp; Drop“ für Dateien und Ordner deaktiviert.</String>
   <String Id="FEATURE_NAME_NGVC">Instant Clone Agent</String>
   <String Id="FEATURE_DESC_NGVC">Instant Clone Agent sollte nur auf einer virtuellen Maschine installiert werden, die auf VMware vSphere 7.0 oder höher ausgeführt wird.</String>
   <String Id="FEATURE_DESC_PCOIP_PHYSICAL">Mit dieser Funktion werden PCoIP-Serverkomponenten auf dem Desktop installiert.</String>
   <String Id="FEATURE_NAME_RTAV">Echtzeit-Audio/Video</String>
   <String Id="FEATURE_DESC_RTAV">Echtzeit-Audio/Video ermöglicht es Benutzern, lokal angeschlossene Audio- und Videogeräte an Remote-Desktops umzuleiten, damit sie dort verwendet werden können.</String>
   <String Id="FEATURE_NAME_VMWPRINT">Horizon Integrated Printing</String>
   <String Id="FEATURE_DESC_VMWPRINT">Horizon Integrated Printing-Umleitung.</String>

   <String Id="FEATURE_NAME_SCANNERREDIRECTION">Scannerumleitung</String>
   <String Id="FEATURE_DESC_SCANNERREDIRECTION">Aktiviert die Scannerumleitungsfunktion.</String>
   <String Id="FEATURE_NAME_SERIALPORTREDIRECTION">Umleitung für seriellen Port</String>
   <String Id="FEATURE_DESC_SERIALPORTREDIRECTION">Aktiviert die Umleitungsfunktion für den seriellen Port.</String>
   <String Id="FEATURE_NAME_SMARTCARD">Smartcard-Umleitung</String>
   <String Id="FEATURE_DESC_SMARTCARD">Aktiviert die Smartcard-Umleitungsfunktion.</String>
   <String Id="FEATURE_NAME_TSMMR">TSMMR</String>
   <String Id="FEATURE_DESC_TSMMR">Multimedia-Umleitung für Terminaldienste.</String>
   <String Id="FEATURE_NAME_URLREDIRECTION">URL-Inhaltsumleitung</String>
   <String Id="FEATURE_DESC_URLREDIRECTION">Leitet URL-Inhalt von einer Serversitzung auf ein Clientgerät und umgekehrt um.</String>
   <String Id="FEATURE_NAME_UNCREDIRECTION">UNC-Pfadumleitung</String>
   <String Id="FEATURE_DESC_UNCREDIRECTION">Leitet den UNC-Pfad von einer Serversitzung auf ein Clientgerät und umgekehrt um.</String>
   <String Id="FEATURE_NAME_USB">USB-Umleitung</String>
   <String Id="FEATURE_DESC_USB">USB-Umleitung. Im Dokument zur Horizon-Sicherheit finden Sie Erläuterungen zur sicheren Verwendung der USB-Umleitung.</String>
   <String Id="FEATURE_NAME_HZNVAUDIO">Horizon Audio</String>
   <String Id="FEATURE_DESC_HZNVAUDIO">Virtueller Horizon Audio-Treiber</String>
   <String Id="FEATURE_NAME_HTML5MMR">HTML5 Multimedia-Umleitung</String>
   <String Id="FEATURE_DESC_HTML5MMR">Aktiviert die Umleitung von HTML5 Multimedia</String>
   <String Id="FEATURE_NAME_GEOREDIR">Geolocation-Umleitung</String>
   <String Id="FEATURE_DESC_GEOREDIR">Ermöglicht die Umleitung der Client-Geolocation zum Remote-Desktop</String>
   <String Id="FEATURE_NAME_SDOSENSOR">SDO-Sensor-Umleitung</String>
   <String Id="FEATURE_DESC_SDOSENSOR">Aktiviert die Funktion zur Umleitung des Simple Device Orientation(SDO)-Sensors und meldet Änderungen bei der Geräteausrichtung an den Remote-Desktop.</String>
   <String Id="FEATURE_NAME_STORAGEDRIVE">Umleitung des Speicherlaufwerks</String>
   <String Id="FEATURE_DESC_STORAGEDRIVE">Aktiviert die Umleitung des Speicherlaufwerks des Clients zum Remote-Desktop.</String>
   <String Id="FEATURE_NAME_PERFTRACKER">Horizon Performance Tracker</String>
   <String Id="FEATURE_DESC_PERFTRACKER">Aktiviert Horizon Performance Tracker</String>
   <String Id="FEATURE_NAME_HYBRIDLOGON">Hybrid-Anmeldung</String>
   <String Id="FEATURE_DESC_HYBRIDLOGON">Aktiviert die Hybrid-Anmeldung, die einem nicht authentifizierten Benutzer ohne Eingabe von Anmeldeinformationen Zugriff auf Netzwerkressourcen ermöglicht.</String>
   <String Id="FEATURE_NAME_HELPDESK">Helpdesk-Plug-in für Horizon Agent</String>
   <String Id="FEATURE_DESC_HELPDESK">Helpdesk-Plug-in für Horizon Agent.</String>

   <!-- Control Panel Strings -->
   <String Id="Url">https://www.omnissa.com/</String>

   <!-- Firewall Strings -->
   <String Id="BlastUDPFirewallExceptionName">Ausnahme beim UDP-Datenverkehr in Omnissa Horizon Blast</String>

   <!-- UI Dialog Strings -->
   <String Id="IDS__DisplayName_Custom">Benutzerdefiniert</String>
   <String Id="IDS__DisplayName_Minimal">Minimal</String>
   <String Id="IDS__DisplayName_Typical">Standard</String>
   <String Id="INTEL_UNS_DESC">Stellt Intel-Benutzerbenachrichtigungsdienste bereit.</String>
   <String Id="IDS_LicenseAcceptance">Mit der Installation akzeptieren Sie die</String>
   <String Id="IDS_GeneralTerms">Allgemeine Bedingungen</String>
   <String Id="IDS_CANCEL">Abbrechen</String>
   <String Id="IDS_CANCEL2">&amp;Abbrechen</String>
   <String Id="IDS_OK">OK</String>
   <String Id="IDS_BACK">&lt; &amp;Zurück</String>
   <String Id="IDS_NEXT">&amp;Weiter &gt;</String>
   <String Id="IDS_FINISH">&amp;Fertig stellen</String>
   <String Id="IDS__IsCancelDlg_No">&amp;Nein</String>
   <String Id="IDS__IsCancelDlg_Yes">&amp;Ja</String>
   <String Id="IDS__IsAdminInstallBrowse_LookIn">&amp;Suchen in:</String>
   <String Id="IDS__IsAdminInstallBrowse_UpOneLevel">Eine Ebene höher</String>
   <String Id="IDS__IsAdminInstallBrowse_BrowseDestination">Navigieren Sie zum Zielordner.</String>
   <String Id="IDS__IsAdminInstallBrowse_ChangeDestination">{&amp;MSSansBold8}Aktuellen Zielordner ändern</String>
   <String Id="IDS__IsAdminInstallBrowse_CreateFolder">Neuen Ordner erstellen</String>
   <String Id="IDS__IsAdminInstallBrowse_FolderName">&amp;Ordnername:</String>
   <String Id="IDS__IsAdminInstallPoint_Install">&amp;Installieren</String>
   <String Id="IDS__IsAdminInstallPoint_SpecifyNetworkLocation">Legen Sie einen Netzwerkspeicherort für das Server-Image des Produkts fest.</String>
   <String Id="IDS__IsAdminInstallPoint_EnterNetworkLocation">Geben Sie einen Netzwerkspeicherort an, oder klicken Sie auf 'Ändern', um einen Speicherort festzulegen. Klicken Sie auf 'Installieren', um ein Server-Image von [ProductName] am festgelegten Netzwerkort zu erzeugen, oder klicken Sie auf 'Abbrechen', um den Assistenten zu beenden.</String>
   <String Id="IDS__IsAdminInstallPoint_NetworkLocationFormatted">{&amp;MSSansBold8}Netzwerkspeicherort</String>
   <String Id="IDS__IsAdminInstallPoint_NetworkLocation">&amp;Netzwerkspeicherort:</String>
   <String Id="IDS__IsAdminInstallPoint_Change">&amp;Ändern...</String>
   <String Id="IDS__IsAdminInstallPointWelcome_Wizard">{&amp;TahomaBold10}Willkommen beim Installationsprogramm für [ProductName]</String>
   <String Id="IDS__IsAdminInstallPointWelcome_ServerImage">Das Installationsprogramm erstellt ein Server-Image von [ProductName] an einem festgelegten Netzwerkspeicherort. Klicken Sie zum Fortfahren auf 'Weiter'.</String>
   <String Id="ProductVersion">{&amp;Arial9}Produktversion: [ProductVersionString]</String>
   <String Id="IDS__IsCancelDlg_ConfirmCancel">Sind Sie sicher, dass Sie die Installation von [ProductName] abbrechen möchten?</String>
   <String Id="IDS__IsInstallRolesConfirmDlg_Message">Vom Installationsprogramm werden die erforderlichen Rollen für das Betriebssystem installiert. Klicken Sie auf „OK“, um fortzufahren.</String>
   <String Id="ConnectionServer_TitleDesc">Geben Sie die Horizon Connection Server-Instanz an, mit dem dieser Computer eine Verbindung herstellt.</String>
   <String Id="ConnectionServer_Title">{&amp;MSSansBold8}Registrierung bei Horizon Connection Server</String>
   <String Id="ConnectionServer_Text">Geben Sie den Servernamen für eine Horizon Connection Server-Instanz (Standard- oder Replikatinstanz) sowie Anmeldeinformationen für den Administrator ein, um diesen Computer bei Horizon Connection Server zu registrieren.</String>
   <String Id="ConnectionServer_ServerNote">(Hostname oder IP-Adresse)</String>
   <String Id="ConnectionServerLogin_Text1">&amp;Authentifizieren Sie sich als der aktuell angemeldete Benutzer</String>
   <String Id="ConnectionServerLogin_Text2">&amp;Geben Sie Administratoranmeldeinformationen an</String>
   <String Id="ConnectionServerLogin_Title">Authentifizierung:</String>
   <String Id="ConnectionServer_Username">&amp;Benutzername:</String>
   <String Id="ConnectionServer_UsernameNote">(Domäne\Benutzer)</String>
   <String Id="ConnectionServer_Password">&amp;Kennwort:</String>
   <String Id="IDS__IsCustomSelectionDlg_SelectFeatures">Wählen Sie die Programmfunktionen, die Sie installieren möchten.</String>
   <String Id="IDS__IsCustomSelectionDlg_ClickFeatureIcon">Klicken Sie auf ein Symbol in der nachstehenden Liste, um den Installationstyp für eine Funktion zu ändern.</String>
   <String Id="IDS__IsCustomSelectionDlg_CustomSetup">{&amp;MSSansBold8}Benutzerdefiniertes Setup</String>
   <String Id="IDS__IsCustomSelectionDlg_Change">&amp;Ändern...</String>
   <String Id="IDS__IsCustomSelectionDlg_FeatureDescription">Funktionsbeschreibung</String>
   <String Id="IDS__IsCustomSelectionDlg_InstallTo">Installieren in:</String>
   <String Id="IDS__IsCustomSelectionDlg_MultilineDescription">Ausführliche Beschreibung des ausgewählten Elements</String>
   <String Id="IDS__IsCustomSelectionDlg_FeaturePath">&lt;Pfad_zur_ausgewählten_Funktion&gt;</String>
   <String Id="IDS__IsCustomSelectionDlg_FeatureSize">Funktionsgröße</String>
   <String Id="IDS__IsCustomSelectionDlg_Help">&amp;Hilfe</String>
   <String Id="IDS__IsCustomSelectionDlg_Space">&amp;Speicherplatz</String>
   <String Id="IDS_SetupTips_CustomSetupDescription">Das benutzerdefinierte Setup ermöglicht die Installation ausgewählter Programmfunktionen.</String>
   <String Id="IDS_SetupTips_CustomSetup">{&amp;MSSansBold8}Tipps zum benutzerdefinierten Setup</String>
   <String Id="IDS_SetupTips_WillNotBeInstalled">Wird nicht installiert.</String>
   <String Id="IDS_SetupTips_Advertise">Wird bei erstmaliger Verwendung installiert. (Nur verfügbar, wenn die Funktion diese Option unterstützt.)</String>
   <String Id="IDS_SetupTips_InstallState">Bedeutung des Installationsstatus:</String>
   <String Id="IDS_SetupTips_AllInstalledLocal">Wird vollständig auf der lokalen Festplatte installiert.</String>
   <String Id="IDS_SetupTips_IconInstallState">Das Symbol neben dem Funktionsnamen zeigt den Installationsstatus der Funktion an. Klicken Sie auf das Symbol, um das Installationsstatusmenü für jede Funktion anzuzeigen.</String>
   <String Id="IDS_SetupTips_Network">Wird zum Starten über das Netzwerk installiert. (Nur verfügbar, wenn die Funktion diese Option unterstützt.)</String>
   <String Id="IDS_SetupTips_SubFeaturesInstalledLocal">Einige Unterfunktionen werden auf der lokalen Festplatte installiert (nur verfügbar, wenn die Funktion Unterfunktionen aufweist).</String>
   <String Id="DesktopConfig_Subtitle">Die folgenden Informationen werden zum Konfigurieren der Horizon Desktop-Funktion verwendet.</String>
   <String Id="DesktopConfig_Title">{&amp;MSSansBold8}Konfiguration des Desktop-Betriebssystems</String>
   <String Id="DesktopConfig_Text">Wählen Sie den Modus für [ProductName] auf diesem Betriebssystem aus:</String>
   <String Id="DesktopConfig_RDSHMode">Die erforderliche RDSH-Rolle (Remote Desktop Session Host) ist auf diesem Betriebssystem nicht installiert.

Klicken Sie auf „Weiter“, um die erforderlichen Rollen/Funktionen zu installieren. Das Betriebssystem muss nach Abschluss des Vorgangs neu gestartet werden.

Nach dem Neustart muss das Installationsprogramm von [ProductName] erneut gestartet werden, um die Installation im RDS-Modus fortzusetzen.</String>
   <String Id="DesktopConfig_DesktopMode">Dieser Agent wird im Desktop-VDI-Modus konfiguriert.</String>
   <String Id="DesktopConfig_InstallingRolesSuccess">Die Installation der erforderlichen Betriebssystemrollen/-funktionen war erfolgreich.
Starten Sie das Betriebssystem neu und anschließend das [ProductName]-Installationsprogramm.</String>
   <String Id="DesktopConfig_InstallingRolesFail">Fehler: Das Installationsprogramm konnte die erforderlichen Rollen/Funktionen des Betriebssystems nicht installieren.</String>
   <String Id="IDS__IsDesktopConfigDlg_RDSMode">RDS-Modus</String>
   <String Id="IDS__IsDesktopConfigDlg_DesktopMode">Desktop-Modus</String>
   <String Id="InstallRolesConfirm_InstallingRoles">Bitte warten Sie, während die erforderlichen Rollen/Funktionen auf diesem System konfiguriert werden...</String>
   <String Id="IDS__IsFeatureDetailsDlg_DiskSpaceRequirements">{&amp;MSSansBold8}Erforderlicher Festplattenspeicherplatz</String>
   <String Id="IDS__IsFeatureDetailsDlg_SpaceRequired">Der erforderliche Festplattenspeicherplatz für die Installation der ausgewählten Funktionen.</String>
   <String Id="IDS__IsFeatureDetailsDlg_VolumesTooSmall">Die hervorgehobenen Laufwerke weisen nicht genügend Speicherplatz für die gewählten Funktionen auf. Sie können entweder Dateien von den hervorgehobenen Laufwerken entfernen, weniger Funktionen zur Installation auswählen oder andere Ziellaufwerke angeben.</String>
   <String Id="IDS__IsFilesInUse_Retry">&amp;Wiederholen</String>
   <String Id="IDS__IsFilesInUse_Ignore">&amp;Ignorieren</String>
   <String Id="IDS__IsFilesInUse_Exit">&amp;Beenden</String>
   <String Id="IDS__IsFilesInUse_FilesInUse">{&amp;MSSansBold8}Dateien in Verwendung</String>
   <String Id="IDS__IsFilesInUse_FilesInUseMessage">Einige der zu aktualisierenden Dateien werden zurzeit verwendet.</String>
   <String Id="IDS__IsFilesInUse_ApplicationsUsingFiles">Die folgenden Anwendungen verwenden Dateien, die von diesem Setup aktualisiert werden müssen. Schließen Sie diese Anwendungen und klicken Sie auf „Wiederholen“, um fortzufahren.

Hinweis: Wenn in der folgenden Liste [ProductName] angezeigt wird, klicken Sie auf „Ignorieren“, um fortzufahren.</String>
   <String Id="IDS__IsBrowseFolderDlg_LookIn">&amp;Suchen in:</String>
   <String Id="IDS__IsBrowseFolderDlg_UpOneLevel">Eine Ebene höher</String>
   <String Id="IDS__IsBrowseFolderDlg_BrowseDestFolder">Navigieren Sie zum Zielordner.</String>
   <String Id="IDS__IsBrowseFolderDlg_ChangeCurrentFolder">{&amp;MSSansBold8}Aktuellen Zielordner ändern</String>
   <String Id="IDS__IsBrowseFolderDlg_CreateFolder">Neuen Ordner erstellen</String>
   <String Id="IDS__IsBrowseFolderDlg_FolderName">&amp;Ordnername:</String>
   <String Id="IDS__IsWelcomeDlg_WelcomeProductName">{&amp;TahomaBold10}Willkommen beim Installations-Assistenten für [ProductName]</String>
   <String Id="IDS__IsWelcomeDlg_InstallProductName">[ProductName] wird auf Ihrem Computer installiert. Klicken Sie zum Fortfahren auf 'Weiter'.</String>
   <String Id="InstallWelcome_UpgradeLine1">Der Installations-Assistent aktualisiert [ProductName] auf Ihrem Computer. Klicken Sie zum Fortfahren auf 'Weiter'.</String>
   <String Id="IDS__IsWelcomeDlg_WarningCopyright">Copyright © [CopyrightYears] Omnissa. Alle Rechte vorbehalten. Dieses Produkt ist durch Urheberrechtsgesetze und Gesetze zum Schutz geistigen Eigentums in den USA und anderen Ländern sowie durch internationale Verträge geschützt. „Omnissa“ bezieht sich auf Omnissa, LLC, Omnissa International Unlimited Company und/oder deren Tochtergesellschaften.</String>
   <String Id="IpProtocolConfig_DlgDesc">Kommunikationsprotokoll auswählen</String>
   <String Id="IpProtocolConfig_DlgTitle">{&amp;MSSansBold8}Netzwerkprotokollkonfiguration</String>
   <String Id="GoldenImage_DlgDesc">Select whether this machine will be used as a Golden Image</String>
   <String Id="GoldenImage_DlgTitle">{&amp;MSSansBold8}Golden Image Selection</String>
   <String Id="GoldenImage_CheckBoxText">This machine will be used as a Golden Image</String>
   <String Id="ConnectionServer_IpText">Geben Sie das Protokoll an, das zur Konfiguration dieser Horizon Agent-Instanz verwendet werden soll:</String>
   <String Id="ConnectionServer_IPv4Desc">Dieser Agent wird zur Auswahl des IPv4-Protokolls für die Herstellung aller Verbindungen konfiguriert.</String>
   <String Id="ConnectionServer_IPv6Desc">Dieser Agent wird zur Auswahl des IPv6-Protokolls für die Herstellung aller Verbindungen konfiguriert.</String>
   <String Id="ConnectionServer_Dual4Desc">Dieser Agent wird so konfiguriert, dass gemischte IP-Modi, die das IPv4-Protokoll für den Aufbau aller Verbindungen bevorzugen, unterstützt werden.</String>
   <String Id="ConnectionServer_Dual6Desc">Dieser Agent wird so konfiguriert, dass gemischte IP-Modi, die das IPv6-Protokoll für den Aufbau aller Verbindungen bevorzugen, unterstützt werden.</String>
   <String Id="IpProtocolConfig_FipsText">Geben Sie an, ob Sie dieses Produkt mit einer FIPS-kompatiblen Kryptografie installieren möchten.</String>
   <String Id="IpProtocolConfig_FipsDisabledDesc">Diese Agenteninstanz wird ohne FIPS-Kompatibilität betrieben.</String>
   <String Id="IpProtocolConfig_FipsEnabledDesc">Diese Agenteninstanz wird für eine FIPS-kompatible Kryptografie konfiguriert.</String>
   <String Id="FipsConfig_Disabled">Deaktiviert</String>
   <String Id="FipsConfig_Enabled">Aktiviert</String>
   <String Id="IDS__AgreeToLicense_0">Ich akzeptiere die Allgemeinen Bedingungen &amp;nicht</String>
   <String Id="IDS__AgreeToLicense_1">Ich &amp;akzeptiere die Allgemeinen Bedingungen</String>
   <String Id="IDS__IsLicenseDlg_ReadLicenseAgreement">Bitte lesen Sie die folgenden Allgemeinen Bedingungen sorgfältig durch.</String>
   <String Id="IDS__IsLicenseDlg_LicenseAgreement">{&amp;MSSansBold8}Allgemeine Bedingungen</String>
   <String Id="IDS__IsMaintenanceDlg_Modify">{&amp;MSSansBold8}&amp;Ändern</String>
   <String Id="IDS__IsMaintenanceDlg_Repair">{&amp;MSSansBold8}Programm &amp;reparieren</String>
   <String Id="IDS__IsMaintenanceDlg_Remove">{&amp;MSSansBold8}Programm &amp;entfernen</String>
   <String Id="IDS__IsMaintenanceDlg_MaitenanceOptions">Programm ändern, reparieren oder entfernen.</String>
   <String Id="IDS__IsMaintenanceDlg_ProgramMaintenance">{&amp;MSSansBold8}Programmwartung</String>
   <String Id="IDS__IsMaintenanceDlg_ModifyMessage">Erlaubt es Benutzern, die installierten Funktionen zu ändern.</String>
   <String Id="IDS__IsMaintenanceDlg_RepairMessage">Behebt Programmfehler. Über diese Option werden fehlende oder beschädigte Dateien, Verknüpfungen und Registrierungseinträge repariert.</String>
   <String Id="IDS__IsMaintenanceDlg_RemoveProductName">Entfernt [ProductName] von Ihrem Computer.</String>
   <String Id="IDS__IsMaintenanceWelcome_WizardWelcome">{&amp;TahomaBold10}Willkommen beim Installationsprogramm für [ProductName]</String>
   <String Id="IDS__IsMaintenanceWelcome_MaintenanceOptionsDescription">Das Installationsprogramm ermöglicht das Ändern, Reparieren oder Entfernen von [ProductName]. Klicken Sie zum Fortfahren auf „Weiter“.</String>
   <String Id="IDS_PRODUCTNAME_INSTALLSHIELD">[ProductName] - Installations-Assistent</String>
   <String Id="IDS__IsMsiRMFilesInUse_CloseRestart">Automatisch schließen und Anwendungen neu starten</String>
   <String Id="IDS__IsMsiRMFilesInUse_RebootAfter">Anwendungen nicht schließen (Neustart erforderlich)</String>
   <String Id="IDS__IsMsiRMFilesInUse_ApplicationsUsingFiles">Die folgenden Anwendungen verwenden Dateien, die aktualisiert werden müssen.</String>
   <String Id="IDS__IsDiskSpaceDlg_OutOfDiskSpace">{&amp;MSSansBold8}Nicht genügend Speicherplatz</String>
   <String Id="IDS__IsDiskSpaceDlg_DiskSpace">Der für die Installation erforderliche Festplattenspeicherplatz übersteigt den verfügbaren Speicherplatz.</String>
   <String Id="IDS__IsDiskSpaceDlg_HighlightedVolumes">Die hervorgehobenen Laufwerke weisen nicht genügend Speicherplatz für die gewählten Funktionen auf. Sie können entweder Dateien von den hervorgehobenen Volumes entfernen oder die Installation abbrechen.</String>
   <String Id="RdpChoice_EnableRdp">Remote-Desktop-Funktion auf diesem Computer &amp;aktivieren</String>
   <String Id="RdpChoice_NoRdp">Remote-Desktop-Funktion auf diesem Computer &amp;nicht aktivieren</String>
   <String Id="RdpConfig_Subtitle">Die folgenden Informationen werden zum Konfigurieren der Remote-Desktop-Funktion verwendet</String>
   <String Id="RdpConfig_Title">{&amp;MSSansBold8}Protokollkonfiguration für Remote-Desktop</String>
   <String Id="RdpConfig_Text">[ProductName] erfordert die Aktivierung der Remote-Desktop-Unterstützung. Es werden Firewall-Ausnahmen für den RDP-Port [RDP_PORT_NUMBER] und den View-Framework-Kanal [FRAMEWORK_CHANNEL_PORT] hinzugefügt. Wie möchten Sie vorgehen?</String>
   <String Id="IDS__IsVerifyReadyDlg_Install">&amp;Installieren</String>
   <String Id="IDS__IsVerifyReadyDlg_WizardReady">Die Installation kann jetzt gestartet werden.</String>
   <String Id="ReadyToInstall_RdshNote">HINWEIS: Die RDS-Rolle ist auf diesem Betriebssystem nicht aktiviert. [ProductName] unterstützt nur Einzel-Desktop-Verbindungen.</String>
   <String Id="IDS__IsVerifyReadyDlg_ClickInstall">Klicken Sie auf 'Installieren', um mit der Installation zu beginnen, oder klicken Sie auf 'Abbrechen', um den Assistenten zu beenden.</String>
   <String Id="IDS__IsVerifyReadyDlg_ReadyRepair">{&amp;MSSansBold8}Bereit zum Reparieren des Programms</String>
   <String Id="IDS__IsVerifyReadyDlg_ReadyInstall">{&amp;MSSansBold8}Bereit zum Installieren des Programms</String>
   <String Id="ReadyToInstall_InstallDir">[ProductName] wird installiert in:

[INSTALLDIR]</String>
   <String Id="ReadyToInstall_MsgSanPolicy_NGVC">HINWEIS: Die VDS SAN-Richtlinie wird auf „Online – Alle“ gesetzt, da dies für die Instant Clone Agent-Funktion (NGVC) erforderlich ist.</String>
   <String Id="IDS__IsVerifyRemoveAllDlg_Remove">&amp;Entfernen</String>
   <String Id="IDS__IsVerifyRemoveAllDlg_ChoseRemoveProgram">Sie haben sich dazu entschlossen, das Programm von Ihrem System zu entfernen.</String>
   <String Id="IDS__IsVerifyRemoveAllDlg_ClickRemove">Klicken Sie auf 'Entfernen', um [ProductName] von Ihrem Computer zu entfernen. Nach dem Entfernen steht dieses Programm nicht mehr zur Verfügung.</String>
   <String Id="IDS__IsVerifyRemoveAllDlg_ClickBack">Klicken Sie auf 'Zurück', wenn Sie Ihre Einstellungen überprüfen oder ändern möchten.</String>
   <String Id="IDS__IsVerifyRemoveAllDlg_RemoveProgram">{&amp;MSSansBold8}Programm entfernen</String>
   <String Id="IDS__IsFatalError_NotModified">Ihr System wurde nicht verändert. Um die Installation zu einem späteren Zeitpunkt abzuschließen, muss das Setup erneut ausgeführt werden.</String>
   <String Id="IDS__IsFatalError_ClickFinish">Klicken Sie auf 'Fertig stellen', um den Assistenten zu beenden.</String>
   <String Id="IDS__IsFatalError_KeepOrRestore">Sie können entweder die installierten Elemente auf Ihrem System belassen, um diese Installation zu einem späteren Zeitpunkt fortzusetzen, oder Sie können Ihr System in den Zustand vor der Installation zurückversetzen.</String>
   <String Id="IDS__IsFatalError_RestoreOrContinueLater">Klicken Sie auf 'Wiederherstellen' oder 'Später fortsetzen', um den Assistenten zu beenden.</String>
   <String Id="IDS__IsFatalError_WizardCompleted">{&amp;TahomaBold10}Installationsprogramm abgeschlossen</String>
   <String Id="IDS__IsFatalError_WizardInterrupted">Der Assistent wurde unterbrochen, bevor [ProductName] vollständig installiert werden konnte.</String>
   <String Id="IDS__IsFatalError_UninstallWizardInterrupted">Der Assistent wurde unterbrochen, bevor [ProductName] vollständig deinstalliert werden konnte.</String>
   <String Id="IDS__IsExitDialog_WizardCompleted">{&amp;TahomaBold10}Installationsprogramm abgeschlossen</String>
   <String Id="IDS__IsExitDialog_InstallSuccess">[ProductName] wurde erfolgreich installiert. Klicken Sie auf 'Fertig stellen', um den Assistenten zu beenden.</String>
   <String Id="IDS__IsExitDialog_UninstallSuccess">[ProductName] wurde erfolgreich deinstalliert. Klicken Sie auf 'Fertig stellen', um den Assistenten zu beenden.</String>
   <String Id="IDS__IsExitDialog_InstallingRolesSuccess">Das Installationsprogramm hat das Betriebssystem erfolgreich mit den Rollen/Funktionen konfiguriert, die für die Installation von [ProductName] im RDS-Modus erforderlich sind.

Klicken Sie auf „Beenden“, um den Assistenten zu beenden.</String>
   <String Id="IDS__IsErrorDlg_InstallerInfo">[ProductName] – Installationsinformationen</String>
   <String Id="IDS__IsErrorDlg_Abort">&amp;Abbrechen</String>
   <String Id="IDS__IsErrorDlg_Yes">&amp;Ja</String>
   <String Id="IDS__IsErrorDlg_No">&amp;Nein</String>
   <String Id="IDS__IsErrorDlg_Ignore">&amp;Ignorieren</String>
   <String Id="IDS__IsErrorDlg_OK">&amp;OK</String>
   <String Id="IDS__IsErrorDlg_Retry">&amp;Wiederholen</String>
   <String Id="IDS__IsInitDlg_WelcomeWizard">{&amp;TahomaBold10}Willkommen beim Installationsprogramm für [ProductName]</String>
   <String Id="IDS__IsInitDlg_PreparingWizard">[ProductName]-Setup bereitet den Installations-Assistenten vor, der Sie durch den Programminstallationsvorgang führen wird. Bitte warten.</String>
   <String Id="IDS__IsUserExit_NotModified">Ihr System wurde nicht verändert. Um die Installation zu einem späteren Zeitpunkt abzuschließen, muss das Setup erneut ausgeführt werden.</String>
   <String Id="IDS__IsUserExit_ClickFinish">Klicken Sie auf 'Fertig stellen', um den Assistenten zu beenden.</String>
   <String Id="IDS__IsUserExit_KeepOrRestore">Sie können entweder die installierten Elemente auf Ihrem System belassen, um diese Installation zu einem späteren Zeitpunkt fortzusetzen, oder Sie können Ihr System in den Zustand vor der Installation zurückversetzen.</String>
   <String Id="IDS__IsUserExit_RestoreOrContinue">Klicken Sie auf 'Wiederherstellen' oder 'Später fortsetzen', um den Assistenten zu beenden.</String>
   <String Id="IDS__IsUserExit_WizardCompleted">{&amp;TahomaBold10}Installationsprogramm abgeschlossen</String>
   <String Id="IDS__IsUserExit_WizardInterrupted">Der Assistent wurde unterbrochen, bevor [ProductName] vollständig installiert werden konnte.</String>
   <String Id="IDS__IsUserExit_UninstallWizardInterrupted">Der Assistent wurde unterbrochen, bevor [ProductName] vollständig deinstalliert werden konnte.</String>
   <String Id="IDS__IsProgressDlg_UninstallingFeatures2">Die ausgewählten Programmfunktionen werden installiert.</String>
   <String Id="IDS__IsProgressDlg_UninstallingFeatures">Die ausgewählten Programmfunktionen werden deinstalliert.</String>
   <String Id="IDS__IsProgressDlg_WaitUninstall2">Bitte warten Sie, während [ProductName] installiert wird. Dieser Vorgang kann einige Minuten in Anspruch nehmen.</String>
   <String Id="IDS__IsProgressDlg_WaitUninstall">Bitte warten Sie, während [ProductName] deinstalliert wird. Dieser Vorgang kann einige Minuten in Anspruch nehmen.</String>
   <String Id="IDS__IsProgressDlg_InstallingProductName">{&amp;MSSansBold8}Installation von [ProductName]</String>
   <String Id="IDS__IsProgressDlg_Uninstalling">{&amp;MSSansBold8}Deinstallation von [ProductName]</String>
   <String Id="IDS__IsProgressDlg_SecHidden">(Ausgeblendet)Sek.</String>
   <String Id="IDS__IsProgressDlg_Status">Status:</String>
   <String Id="IDS__IsProgressDlg_Hidden">(Ausgeblendet)</String>
   <String Id="IDS__IsProgressDlg_HiddenTimeRemaining">(Ausgeblendet)Geschätzte verbleibende Zeit:</String>
   <String Id="IDS__IsProgressDlg_ProgressDone">Fertig</String>
   <String Id="IDS__IsResumeDlg_Resuming">{&amp;TahomaBold10}Wiederaufnahme der Installation von [ProductName]</String>
   <String Id="IDS__IsResumeDlg_ResumeSuspended">Die unterbrochene Installation von [ProductName] wird fortgesetzt. Klicken Sie zum Fortfahren auf 'Weiter'.</String>
   <String Id="IDS__IsResumeDlg_WizardResume">Die Installation von [ProductName] wird fortgesetzt. Klicken Sie zum Fortfahren auf 'Weiter'.</String>


   <!-- Error Strings -->
   <String Id="MsgWSWCInstalled">Die Installation kann nicht fortgesetzt werden, da eine inkompatible Version des Horizon Clients bereits auf diesem Computer installiert ist.

Um mit der Installation des [ProductName] fortzufahren, deinstallieren Sie den Horizon Client und führen Sie dieses Installationsprogramm dann erneut aus.</String>
   <String Id="MsgClientRunning">Agent-Installation kann nicht fortgesetzt werden. Eine aktive Horizon Client-Sitzung wurde ermittelt.</String>
   <String Id="MsgDowngradeDetected">Das Installationsprogramm hat erkannt, dass bereits eine neuere Version von [ProductName] installiert wurde.</String>
   <String Id="MsgManualUninstallRequired">Über die vorhandene Produktinstallation kann keine Aktualisierung durchgeführt werden. Deinstallieren Sie das vorhandene Produkt, bevor Sie mit dieser Installation fortfahren.</String>
   <String Id="MsgMustReboot">Das System muss neu gestartet werden, bevor die Installation fortgesetzt werden kann.</String>
   <String Id="MsgServerInstalled">Die Installation kann nicht fortgesetzt werden, da Horizon Connection Server bereits auf diesem Computer installiert ist.

Um mit der Installation des [ProductName] fortzufahren, deinstallieren Sie die Verbindungsserver und führen Sie dieses Installationsprogramm dann erneut aus.</String>
   <String Id="MsgUnsupportedOldVersion">Installation nicht möglich, da bereits eine ältere, nicht unterstützte Version dieses Produkts installiert ist. Deinstallieren Sie die vorhandene Version und starten Sie das System neu, bevor Sie diese Version installieren.</String>
   <String Id="MsgUrlRedirectionInstalled">Sie versuchen, [ProductName] mit aktivierter URL-Umleitung zu installieren. Die URL-Umleitung wurde jedoch bereits durch den Horizon Client aktiviert. Dies wird nicht unterstützt. Wenn Sie fortfahren, wird der Agent ohne URL-Umleitung installiert. Um die URL-Umleitung im Agent-Modus aktivieren zu können, müssen Sie zuvor den Client deinstallieren und den Agent installieren.</String>
   <String Id="MsgUNCRedirectionInstalled">Sie versuchen, [ProductName] mit aktivierter UNC-Umleitung zu installieren. Die UNC-Umleitung wurde jedoch bereits durch den Horizon Client aktiviert. Dies wird nicht unterstützt. Wenn Sie fortfahren, wird der Agent ohne UNC-Umleitung installiert. Um die UNC-Umleitung im Agent-Modus aktivieren zu können, müssen Sie zuvor den Client deinstallieren und den Agent installieren.</String>
   <String Id="MsgVdmLoopbackIp">Fehler beim Versuch, die 'Localhost'-IP-Adresse zu ermitteln. Vergewissern Sie sich, dass ein IP-Protokoll ausgewählt wurde und das Protokoll auf diesem Computer installiert ist.</String>
   <String Id="MsgWindowsUpdateInProgress">Derzeit wird ein Windows-Update durchgeführt. Schließen Sie das Windows-Update ab und starten Sie das System neu, bevor Sie den Horizon Agent installieren.</String>
   <String Id="MsgWindowsUpdateAndRestartPending">Die Installation kann erst nach der Aktualisierung und dem Neustart des System fortgesetzt werden.</String>
   <String Id="NoRepairAllowed">Eine Horizon-Sitzung ist aktiv. Die [ProductName]-Reparatur kann nicht fortgesetzt werden.</String>
   <String Id="MsgInstallationAbortifSVIInstalled">Dieses Installationsprogramm kann kein Upgrade für die vorhandene Produktinstallation durchführen. Die Horizon View Composer-Funktion wird ab Version 8.1 nicht mehr unterstützt. Wenn Sie diesen Build installieren möchten, deinstallieren Sie zunächst den vorherigen Build.</String>
   <String Id="SettingsFileInvalid">Fehler beim Analysieren der Installationseinstellungsdatei: „[SETTINGS_FILE]“

Fehler in Zeile [SettingsFileErrorLine].</String>


   <!-- Action Text Strings -->
   <String Id="ActionText_RdpConfig">RDP-Konfiguration wird durchgeführt</String>
   <String Id="ConfigUserInit">UserInit-Prozess wird registriert: wssm.exe</String>
   <String Id="IDS_ACTIONTEXT_1">[1]</String>
   <String Id="IDS_ACTIONTEXT_1b">[1]</String>
   <String Id="IDS_ACTIONTEXT_1c">[1]</String>
   <String Id="IDS_ACTIONTEXT_1d">[1]</String>
   <String Id="IDS_ACTIONTEXT_Advertising">Anwendung wird angekündigt</String>
   <String Id="IDS_ACTIONTEXT_AllocatingRegistry">Reservierung von Speicherplatz in der Registrierung</String>
   <String Id="IDS_ACTIONTEXT_AppCommandLine">Anwendung: [1], Befehlszeile: [2]</String>
   <String Id="IDS_ACTIONTEXT_AppId">Anwendungs-ID: [1]{{, Anwendungstyp: [2]}}</String>
   <String Id="IDS_ACTIONTEXT_AppIdAppTypeRSN">Anwendungs-ID: [1]{{, Anwendungstyp: [2], Benutzer: [3], RSN: [4]}}</String>
   <String Id="IDS_ACTIONTEXT_Application">Anwendung: [1]</String>
   <String Id="IDS_ACTIONTEXT_BindingExes">Ausführbare Dateien werden gebunden</String>
   <String Id="IDS_ACTIONTEXT_ClassId">Klassen-ID: [1]</String>
   <String Id="IDS_ACTIONTEXT_ClsID">Klassen-ID: [1]</String>
   <String Id="IDS_ACTIONTEXT_ComponentIDQualifier">Komponenten-ID: [1], Bezeichner: [2]</String>
   <String Id="IDS_ACTIONTEXT_ComponentIdQualifier2">Komponenten-ID: [1], Bezeichner: [2]</String>
   <String Id="IDS_ACTIONTEXT_ComputingSpace">Speicherbedarf wird berechnet</String>
   <String Id="IDS_ACTIONTEXT_ComputingSpace2">Speicherbedarf wird berechnet</String>
   <String Id="IDS_ACTIONTEXT_ComputingSpace3">Speicherbedarf wird berechnet</String>
   <String Id="IDS_ACTIONTEXT_ContentTypeExtension">MIME-Inhaltstyp: [1], Erweiterung: [2]</String>
   <String Id="IDS_ACTIONTEXT_ContentTypeExtension2">MIME-Inhaltstyp: [1], Erweiterung: [2]</String>
   <String Id="IDS_ACTIONTEXT_CopyingNetworkFiles">Dateien werden in das Netzwerk kopiert</String>
   <String Id="IDS_ACTIONTEXT_CopyingNewFiles">Neue Dateien werden kopiert</String>
   <String Id="IDS_ACTIONTEXT_CreatingDuplicate">Dateien werden dupliziert</String>
   <String Id="IDS_ACTIONTEXT_CreatingFolders">Ordner werden erstellt</String>
   <String Id="IDS_ACTIONTEXT_CreatingShortcuts">Verknüpfungen werden erstellt</String>
   <String Id="IDS_ACTIONTEXT_DeletingServices">Dienste werden gelöscht</String>
   <String Id="IDS_ACTIONTEXT_EnvironmentStrings">Umgebungsvariablen werden aktualisiert</String>
   <String Id="IDS_ACTIONTEXT_EvaluateLaunchConditions">Startbedingungen werden überprüft</String>
   <String Id="IDS_ACTIONTEXT_Extension">Erweiterung: [1]</String>
   <String Id="IDS_ACTIONTEXT_Extension2">Erweiterung: [1]</String>
   <String Id="IDS_ACTIONTEXT_Feature">Funktion: [1]</String>
   <String Id="IDS_ACTIONTEXT_FeatureColon">Funktion: [1]</String>
   <String Id="IDS_ACTIONTEXT_File">Datei: [1]</String>
   <String Id="IDS_ACTIONTEXT_File2">Datei: [1]</String>
   <String Id="IDS_ACTIONTEXT_FileDependencies">Datei: [1], Abhängigkeiten: [2]</String>
   <String Id="IDS_ACTIONTEXT_FileDir">Datei: [1], Verzeichnis: [9]</String>
   <String Id="IDS_ACTIONTEXT_FileDir2">Datei: [1], Verzeichnis: [9]</String>
   <String Id="IDS_ACTIONTEXT_FileDir3">Datei: [1], Verzeichnis: [9]</String>
   <String Id="IDS_ACTIONTEXT_FileDirSize">Datei: [1], Verzeichnis: [9], Größe: [6]</String>
   <String Id="IDS_ACTIONTEXT_FileDirSize2">Datei: [1], Verzeichnis: [9], Größe: [6]</String>
   <String Id="IDS_ACTIONTEXT_FileDirSize3">Datei: [1], Verzeichnis: [9], Größe: [6]</String>
   <String Id="IDS_ACTIONTEXT_FileDirSize4">Datei: [1], Verzeichnis: [2], Größe: [3]</String>
   <String Id="IDS_ACTIONTEXT_FileDirectorySize">Datei: [1], Verzeichnis: [9], Größe: [6]</String>
   <String Id="IDS_ACTIONTEXT_FileFolder">Datei: [1], Ordner: [2]</String>
   <String Id="IDS_ACTIONTEXT_FileFolder2">Datei: [1], Ordner: [2]</String>
   <String Id="IDS_ACTIONTEXT_FileSectionKeyValue">Datei: [1], Abschnitt: [2], Schlüssel: [3], Wert: [4]</String>
   <String Id="IDS_ACTIONTEXT_FileSectionKeyValue2">Datei: [1], Abschnitt: [2], Schlüssel: [3], Wert: [4]</String>
   <String Id="IDS_ACTIONTEXT_Folder">Ordner: [1]</String>
   <String Id="IDS_ACTIONTEXT_Folder1">Ordner: [1]</String>
   <String Id="IDS_ACTIONTEXT_Font">Schriftart: [1]</String>
   <String Id="IDS_ACTIONTEXT_Font2">Schriftart: [1]</String>
   <String Id="IDS_ACTIONTEXT_FoundApp">Ermittelte Anwendung: [1]</String>
   <String Id="IDS_ACTIONTEXT_FreeSpace">Freier Speicherplatz: [1]</String>
   <String Id="IDS_ACTIONTEXT_GeneratingScript">Skriptoperationen werden generiert für Aktion:</String>
   <String Id="IDS_ACTIONTEXT_InitializeODBCDirs">ODBC-Verzeichnisse werden initialisiert</String>
   <String Id="IDS_ACTIONTEXT_InstallODBC">ODBC-Komponenten werden installiert</String>
   <String Id="IDS_ACTIONTEXT_InstallServices">Neue Dienste werden installiert</String>
   <String Id="IDS_ACTIONTEXT_InstallingSystemCatalog">Systemkatalog wird installiert</String>
   <String Id="IDS_ACTIONTEXT_KeyName">Schlüssel: [1], Name: [2]</String>
   <String Id="IDS_ACTIONTEXT_KeyNameValue">Schlüssel: [1], Name: [2], Wert: [3]</String>
   <String Id="IDS_ACTIONTEXT_LibId">Bibliothek-ID: [1]</String>
   <String Id="IDS_ACTIONTEXT_Libid2">Bibliothek-ID: [1]</String>
   <String Id="IDS_ACTIONTEXT_MigratingFeatureStates">Funktionsstatus verwandter Anwendungen wird migriert</String>
   <String Id="IDS_ACTIONTEXT_MovingFiles">Dateien werden verschoben</String>
   <String Id="IDS_ACTIONTEXT_NameValueAction">Name: [1], Wert: [2], Aktion [3]</String>
   <String Id="IDS_ACTIONTEXT_NameValueAction2">Name: [1], Wert: [2], Aktion [3]</String>
   <String Id="IDS_ACTIONTEXT_PatchingFiles">Dateien werden gepatcht</String>
   <String Id="IDS_ACTIONTEXT_ProgID">Programm-ID: [1]</String>
   <String Id="IDS_ACTIONTEXT_ProgID2">Programm-ID: [1]</String>
   <String Id="IDS_ACTIONTEXT_PropertySignature">Eigenschaft: [1], Signatur: [2]</String>
   <String Id="IDS_ACTIONTEXT_PublishProductFeatures">Produktfunktionen werden veröffentlicht</String>
   <String Id="IDS_ACTIONTEXT_PublishProductInfo">Produktinformationen werden veröffentlicht</String>
   <String Id="IDS_ACTIONTEXT_PublishingQualifiedComponents">Qualifizierte Komponenten werden veröffentlicht</String>
   <String Id="IDS_ACTIONTEXT_RegUser">Benutzer wird registriert</String>
   <String Id="IDS_ACTIONTEXT_RegisterClassServer">Klassenserver werden registriert</String>
   <String Id="IDS_ACTIONTEXT_RegisterExtensionServers">Erweiterungsserver werden registriert</String>
   <String Id="IDS_ACTIONTEXT_RegisterFonts">Schriftarten werden registriert</String>
   <String Id="IDS_ACTIONTEXT_RegisterMimeInfo">MIME-Informationen werden registriert</String>
   <String Id="IDS_ACTIONTEXT_RegisterTypeLibs">Typbibliotheken werden registriert</String>
   <String Id="IDS_ACTIONTEXT_RegisteringComPlus">COM+-Anwendungen und Komponenten werden registriert</String>
   <String Id="IDS_ACTIONTEXT_RegisteringModules">Module werden registriert</String>
   <String Id="IDS_ACTIONTEXT_RegisteringProduct">Produkt wird registriert</String>
   <String Id="IDS_ACTIONTEXT_RegisteringProgIdentifiers">Programmbezeichner werden registriert</String>
   <String Id="IDS_ACTIONTEXT_RemoveApps">Anwendungen werden entfernt</String>
   <String Id="IDS_ACTIONTEXT_RemovingBackup">Sicherungsdateien werden entfernt</String>
   <String Id="IDS_ACTIONTEXT_RemovingDuplicates">Doppelte Dateien werden entfernt</String>
   <String Id="IDS_ACTIONTEXT_RemovingFiles">Dateien werden entfernt</String>
   <String Id="IDS_ACTIONTEXT_RemovingFolders">Ordner werden entfernt</String>
   <String Id="IDS_ACTIONTEXT_RemovingIni">INI-Dateieinträge werden entfernt</String>
   <String Id="IDS_ACTIONTEXT_RemovingMoved">Verschobene Dateien werden entfernt</String>
   <String Id="IDS_ACTIONTEXT_RemovingODBC">ODBC-Komponenten werden entfernt</String>
   <String Id="IDS_ACTIONTEXT_RemovingRegistry">Werte werden aus der Systemregistrierung entfernt</String>
   <String Id="IDS_ACTIONTEXT_RemovingShortcuts">Verknüpfungen werden entfernt</String>
   <String Id="IDS_ACTIONTEXT_RollingBack">Aktion wird rückgängig gemacht:</String>
   <String Id="IDS_ACTIONTEXT_SearchForRelated">Suche nach verwandten Anwendungen</String>
   <String Id="IDS_ACTIONTEXT_SearchInstalled">Suche nach installierten Anwendungen</String>
   <String Id="IDS_ACTIONTEXT_SearchingQualifyingProducts">Suche nach kompatiblen Produkten</String>
   <String Id="IDS_ACTIONTEXT_ServerConfig">Horizon Connection Server wird konfiguriert</String>
   <String Id="IDS_ACTIONTEXT_Service">Dienst: [1]</String>
   <String Id="IDS_ACTIONTEXT_Service2">Dienst: [2]</String>
   <String Id="IDS_ACTIONTEXT_Service3">Dienst: [1]</String>
   <String Id="IDS_ACTIONTEXT_Service4">Dienst: [1]</String>
   <String Id="IDS_ACTIONTEXT_Shortcut">Verknüpfung: [1]</String>
   <String Id="IDS_ACTIONTEXT_Shortcut1">Verknüpfung: [1]</String>
   <String Id="IDS_ACTIONTEXT_StartingServices">Dienste werden gestartet</String>
   <String Id="IDS_ACTIONTEXT_StoppingServices">Dienste werden angehalten</String>
   <String Id="IDS_ACTIONTEXT_UnpublishProductFeatures">Veröffentlichung von Produktfunktionen wird rückgängig gemacht</String>
   <String Id="IDS_ACTIONTEXT_UnpublishQualified">Veröffentlichung qualifizierter Komponenten wird rückgängig gemacht</String>
   <String Id="IDS_ACTIONTEXT_UnpublishingProductInfo">Veröffentlichung von Produktinformationen wird rückgängig gemacht</String>
   <String Id="IDS_ACTIONTEXT_UnregTypeLibs">Typbibliotheken werden aus der Registrierung entfernt</String>
   <String Id="IDS_ACTIONTEXT_UnregisterClassServers">Klassenserver werden aus der Registrierung entfernt</String>
   <String Id="IDS_ACTIONTEXT_UnregisterExtensionServers">Erweiterungsserver werden aus der Registrierung entfernt</String>
   <String Id="IDS_ACTIONTEXT_UnregisterModules">Module werden aus der Registrierung entfernt</String>
   <String Id="IDS_ACTIONTEXT_UnregisteringComPlus">COM+-Anwendungen und Komponenten werden aus der Registrierung entfernt</String>
   <String Id="IDS_ACTIONTEXT_UnregisteringFonts">Schriftarten werden aus der Registrierung entfernt</String>
   <String Id="IDS_ACTIONTEXT_UnregisteringMimeInfo">MIME-Informationen werden aus der Registrierung entfernt</String>
   <String Id="IDS_ACTIONTEXT_UnregisteringProgramIds">Programmbezeichner werden aus der Registrierung entfernt</String>
   <String Id="IDS_ACTIONTEXT_UpdateComponentRegistration">Registrierung der Komponente(n) wird aktualisiert</String>
   <String Id="IDS_ACTIONTEXT_UpdateEnvironmentStrings">Umgebungsvariablen werden aktualisiert</String>
   <String Id="IDS_ACTIONTEXT_Validating">Installation wird überprüft</String>
   <String Id="IDS_ACTIONTEXT_BlastUDPInstall">Einrichten der UDP-Kommunikationseinstellungen</String>
   <String Id="IDS_ACTIONTEXT_BlastUDPUninstall">Löschen der UDP-Kommunikationseinstellungen</String>
   <String Id="IDS_ACTIONTEXT_WritingINI">INI-Dateiwerte werden geschrieben</String>
   <String Id="IDS_ACTIONTEXT_WritingRegistry">Werte werden in die Systemregistrierung geschrieben</String>
   <String Id="UnconfigUserInit">Registrierung des UserInit-Prozesses wird aufgehoben: wssm.exe</String>
   <String Id="VM_WaitForpairing_ProgressText">Waiting for agent pairing to complete...</String>

   <!-- UIText Strings -->
   <String Id="IDS_UITEXT_Available">Verfügbar</String>
   <String Id="IDS_UITEXT_Bytes">Byte</String>
   <String Id="IDS_UITEXT_CompilingFeaturesCost">Erforderlicher Speicherplatz für diese Funktion wird berechnet...</String>
   <String Id="IDS_UITEXT_Differences">Unterschiede</String>
   <String Id="IDS_UITEXT_DiskSize">Festplattengröße</String>
   <String Id="IDS_UITEXT_FeatureCompletelyRemoved">Diese Funktion wird vollständig entfernt.</String>
   <String Id="IDS_UITEXT_FeatureContinueNetwork">Diese Funktion wird weiterhin über das Netzwerk gestartet.</String>
   <String Id="IDS_UITEXT_FeatureFreeSpace">Diese Funktion gibt [1] auf der Festplatte frei.</String>
   <String Id="IDS_UITEXT_FeatureInstalledCD">Diese Funktion und alle Unterfunktionen werden zum Start von CD installiert.</String>
   <String Id="IDS_UITEXT_FeatureInstalledCD2">Diese Funktion wird zum Start von CD installiert.</String>
   <String Id="IDS_UITEXT_FeatureInstalledLocal">Diese Funktion und alle Unterfunktionen werden lokal installiert.</String>
   <String Id="IDS_UITEXT_FeatureInstalledLocal2">Diese Funktion wird lokal installiert.</String>
   <String Id="IDS_UITEXT_FeatureInstalledNetwork">Diese Funktion und alle Unterfunktionen werden zum Start über das Netzwerk installiert.</String>
   <String Id="IDS_UITEXT_FeatureInstalledNetwork2">Diese Funktion wird zum Start über das Netzwerk installiert.</String>
   <String Id="IDS_UITEXT_FeatureInstalledRequired">Wird bei Bedarf installiert.</String>
   <String Id="IDS_UITEXT_FeatureInstalledWhenRequired">Diese Funktion wird bei Bedarf installiert.</String>
   <String Id="IDS_UITEXT_FeatureInstalledWhenRequired2">Diese Funktion wird bei Bedarf installiert.</String>
   <String Id="IDS_UITEXT_FeatureLocal">Diese Funktion wird auf der lokalen Festplatte installiert.</String>
   <String Id="IDS_UITEXT_FeatureLocal2">Diese Funktion wird auf der lokalen Festplatte installiert.</String>
   <String Id="IDS_UITEXT_FeatureNetwork">Diese Funktion wird zum Start über das Netzwerk installiert.</String>
   <String Id="IDS_UITEXT_FeatureNetwork2">Diese Funktion kann über das Netzwerk gestartet werden.</String>
   <String Id="IDS_UITEXT_FeatureNotAvailable">Diese Funktion steht nicht zur Verfügung.</String>
   <String Id="IDS_UITEXT_FeatureOnCD">Diese Funktion wird zum Start von CD installiert.</String>
   <String Id="IDS_UITEXT_FeatureOnCD2">Diese Funktion kann von CD gestartet werden.</String>
   <String Id="IDS_UITEXT_FeatureRemainLocal">Diese Funktion verbleibt auf der lokalen Festplatte.</String>
   <String Id="IDS_UITEXT_FeatureRemoveNetwork">Diese Funktion wird von der lokalen Festplatte entfernt, kann jedoch weiterhin über das Netzwerk gestartet werden.</String>
   <String Id="IDS_UITEXT_FeatureRemovedCD">Diese Funktion wird von der lokalen Festplatte entfernt, kann jedoch weiterhin von CD gestartet werden.</String>
   <String Id="IDS_UITEXT_FeatureRemovedUnlessRequired">Diese Funktion wird von der lokalen Festplatte entfernt, wird jedoch bei Bedarf installiert.</String>
   <String Id="IDS_UITEXT_FeatureRequiredSpace">Diese Funktion erfordert [1] auf Ihrer Festplatte.</String>
   <String Id="IDS_UITEXT_FeatureRunFromCD">Diese Funktion wird weiterhin von CD gestartet</String>
   <String Id="IDS_UITEXT_FeatureSpaceFree">Diese Funktion gibt [1] auf der Festplatte frei. Es sind [2] von [3] Unterfunktionen ausgewählt. Die Unterfunktionen geben [4] auf der Festplatte frei.</String>
   <String Id="IDS_UITEXT_FeatureSpaceFree2">Diese Funktion gibt [1] auf der Festplatte frei. Es sind [2] von [3] Unterfunktionen ausgewählt. Die Unterfunktionen erfordern [4] auf der Festplatte.</String>
   <String Id="IDS_UITEXT_FeatureSpaceFree3">Diese Funktion erfordert [1] auf Ihrer Festplatte. Es sind [2] von [3] Unterfunktionen ausgewählt. Die Unterfunktionen geben [4] auf der Festplatte frei.</String>
   <String Id="IDS_UITEXT_FeatureSpaceFree4">Diese Funktion erfordert [1] auf Ihrer Festplatte. Es sind [2] von [3] Unterfunktionen ausgewählt. Die Unterfunktionen erfordern [4] auf der Festplatte.</String>
   <String Id="IDS_UITEXT_FeatureUnavailable">Diese Funktion wird nicht mehr verfügbar sein.</String>
   <String Id="IDS_UITEXT_FeatureUninstallNoNetwork">Diese Funktion wird vollständig entfernt und kann nicht mehr über das Netzwerk gestartet werden.</String>
   <String Id="IDS_UITEXT_FeatureWasCD">Diese Funktion, die bisher von CD gestartet wurde, wird jetzt für die Installation bei Bedarf konfiguriert.</String>
   <String Id="IDS_UITEXT_FeatureWasCDLocal">Diese Funktion, die bisher von CD gestartet wurde, wird jetzt auf der lokalen Festplatte installiert.</String>
   <String Id="IDS_UITEXT_FeatureWasOnNetworkInstalled">Diese Funktion, die bisher über das Netzwerk gestartet wurde, wird jetzt für die Installation bei Bedarf konfiguriert.</String>
   <String Id="IDS_UITEXT_FeatureWasOnNetworkLocal">Diese Funktion, die bisher über das Netzwerk ausgeführt wurde, wird jetzt auf der lokalen Festplatte installiert.</String>
   <String Id="IDS_UITEXT_FeatureWillBeUninstalled">Diese Funktion wird vollständig deinstalliert und kann anschließend nicht mehr von CD gestartet werden.</String>
   <String Id="IDS_UITEXT_Folder">Ordner|Neuer Ordner</String>
   <String Id="IDS_UITEXT_GB">GB</String>
   <String Id="IDS_UITEXT_KB">KB</String>
   <String Id="IDS_UITEXT_MB">MB</String>
   <String Id="IDS_UITEXT_Required">Erforderlich</String>
   <String Id="IDS_UITEXT_TimeRemaining">Verbleibende Zeit: {[1] Min }[2] Sek</String>
   <String Id="IDS_UITEXT_Volume">Volume</String>


   <!-- Error Table Strings -->
   <String Id="IDS_ERROR_0">{{Schwerwiegender Fehler: }}</String>
   <String Id="IDS_ERROR_1">Fehler [1].</String>
   <String Id="IDS_ERROR_2">Warnung [1].</String>
   <String Id="IDS_ERROR_4">Information [1].</String>
   <String Id="IDS_ERROR_5">Interner Fehler [1]. [2]{, [3]}{, [4]}</String>
   <String Id="IDS_ERROR_7">{{Festplatte voll: }}</String>
   <String Id="IDS_ERROR_8">Aktion [Time]: [1]. [2]</String>
   <String Id="IDS_ERROR_9">[ProductName]</String>
   <String Id="IDS_ERROR_10">{[2]}{, [3]}{, [4]}</String>
   <String Id="IDS_ERROR_11">Meldungstyp: [1], Argument: [2]</String>
   <String Id="IDS_ERROR_12">=== Protokollierung gestartet: [Date]  [Time] ===</String>
   <String Id="IDS_ERROR_13">=== Protokollierung beendet: [Date]  [Time] ===</String>
   <String Id="IDS_ERROR_14">Aktion gestartet um [Time]: [1].</String>
   <String Id="IDS_ERROR_15">Aktion beendet um [Time]: [1]. Rückgabewert [2].</String>
   <String Id="IDS_ERROR_16">Verbleibende Zeit: {[1] Minute(n) }{[2] Sekunde(n)}</String>
   <String Id="IDS_ERROR_17">Nicht genügend Arbeitsspeicher vorhanden. Beenden Sie andere Anwendungen und wiederholen Sie den Vorgang.</String>
   <String Id="IDS_ERROR_18">Installationsprogramm reagiert nicht.</String>
   <String Id="IDS_ERROR_19">Installationsprogramm wurde vorzeitig beendet.</String>
   <String Id="IDS_ERROR_20">Bitte warten Sie, während Windows [ProductName] konfiguriert.</String>
   <String Id="IDS_ERROR_21">Erforderliche Daten werden ermittelt...</String>
   <String Id="IDS_ERROR_22">Ältere Versionen dieser Anwendung werden entfernt...</String>
   <String Id="IDS_ERROR_23">Entfernen älterer Versionen dieser Anwendung wird vorbereitet...</String>
   <String Id="IDS_ERROR_32">Setup von {[ProductName]} erfolgreich abgeschlossen.</String>
   <String Id="IDS_ERROR_33">Setup von {[ProductName]} fehlgeschlagen.</String>
   <String Id="IDS_ERROR_1101">Fehler beim Lesen aus Datei: [2]. {{ Systemfehler [3].}} Überprüfen Sie, ob die Datei vorhanden ist und Sie darauf zugreifen können.</String>
   <String Id="IDS_ERROR_1301">Datei [3] kann nicht erstellt werden. Es ist bereits ein gleichnamiges Verzeichnis vorhanden. Brechen Sie die Installation ab und nehmen Sie die Installation in einem anderen Verzeichnis vor.</String>
   <String Id="IDS_ERROR_1302">Legen Sie den Datenträger ein: [2]</String>
   <String Id="IDS_ERROR_1303">Die Installer-Berechtigungen reichen für den Zugriff auf das folgende Verzeichnis nicht aus: [2]. Die Installation kann nicht fortgesetzt werden. Melden Sie sich als Administrator an oder wenden Sie sich an den Systemadministrator.</String>
   <String Id="IDS_ERROR_1304">Fehler beim Schreiben in Datei [2]. Überprüfen Sie, ob Sie auf das Verzeichnis zugreifen können.</String>
   <String Id="IDS_ERROR_1305">Fehler beim Lesen von Datei [2]. Überprüfen Sie, ob die Datei vorhanden ist und Sie darauf zugreifen können.</String>
   <String Id="IDS_ERROR_1306">Eine andere Anwendung hat exklusiven Zugriff auf die Datei [2]. Beenden Sie alle weiteren Anwendungen und klicken Sie auf 'Wiederholen'.</String>
   <String Id="IDS_ERROR_1307">Zum Installieren von Datei [2] ist nicht genügend freier Speicherplatz auf der Festplatte vorhanden. Geben Sie Speicherplatz frei und klicken Sie auf 'Wiederholen' oder klicken Sie zum Beenden auf 'Abbrechen'.</String>
   <String Id="IDS_ERROR_1308">Quelldatei nicht gefunden: [2]. Überprüfen Sie, ob die Datei vorhanden ist und Sie darauf zugreifen können.</String>
   <String Id="IDS_ERROR_1309">Fehler beim Lesen aus Datei: [3]. {{ Systemfehler [2].}} Überprüfen Sie, ob die Datei vorhanden ist und Sie darauf zugreifen können.</String>
   <String Id="IDS_ERROR_1310">Fehler beim Schreiben in Datei: [3]. {{ Systemfehler [2].}} Überprüfen Sie, ob Sie auf das Verzeichnis zugreifen können.</String>
   <String Id="IDS_ERROR_1311">Quelldatei {{(cabinet)}} nicht gefunden: [2]. Überprüfen Sie, ob die Datei vorhanden ist und Sie darauf zugreifen können.</String>
   <String Id="IDS_ERROR_1312">Verzeichnis [2] kann nicht erstellt werden. Eine gleichnamige Datei ist bereits vorhanden. Benennen Sie die Datei um oder entfernen Sie die Datei und klicken Sie auf 'Wiederholen'. Oder klicken Sie auf 'Abbrechen', um das Programm zu beenden.</String>
   <String Id="IDS_ERROR_1313">Volume [2] steht momentan nicht zur Verfügung. Wählen Sie ein anderes Volume aus.</String>
   <String Id="IDS_ERROR_1314">Der angegebene Pfad [2] ist nicht verfügbar.</String>
   <String Id="IDS_ERROR_1315">Schreibzugriff auf den angegebenen Ordner [2] ist nicht möglich.</String>
   <String Id="IDS_ERROR_1316">Netzwerkfehler beim Lesen von Datei [2].</String>
   <String Id="IDS_ERROR_1317">Fehler beim Erstellen von Verzeichnis [2].</String>
   <String Id="IDS_ERROR_1318">Netzwerkfehler beim Erstellen von Verzeichnis [2].</String>
   <String Id="IDS_ERROR_1319">Netzwerkfehler beim Öffnen der Quelldatei (CAB-Datei) [2].</String>
   <String Id="IDS_ERROR_1320">Der angegebene Pfad ist zu lang: [2]</String>
   <String Id="IDS_ERROR_1321">Die Berechtigungen des Installationsprogramms reichen zum Bearbeiten von Datei [2] nicht aus.</String>
   <String Id="IDS_ERROR_1322">Ein Teil des Pfades [2] überschreitet die vom System zugelassene Länge.</String>
   <String Id="IDS_ERROR_1323">Der Pfad [2] enthält Wörter, die in Ordnerpfaden nicht zulässig sind.</String>
   <String Id="IDS_ERROR_1324">Der Pfad [2] enthält ein unzulässiges Zeichen.</String>
   <String Id="IDS_ERROR_1325">[2] ist kein gültiger kurzer Dateiname.</String>
   <String Id="IDS_ERROR_1326">Fehler beim Abrufen der Dateisicherheitsinformationen: [3] GetLastError: [2]</String>
   <String Id="IDS_ERROR_1327">Ungültiges Laufwerk: [2]</String>
   <String Id="IDS_ERROR_1328">Fehler bei Patch-Anwendung auf Datei [2]. Die Datei wurde wahrscheinlich bereits auf andere Weise aktualisiert und kann über diesen Patch nicht mehr verändert werden. Wenden Sie sich an den Patch-Anbieter. {{Systemfehler: [3]}}</String>
   <String Id="IDS_ERROR_1329">Eine erforderliche Datei kann nicht installiert werden, da die CAB-Datei [2] nicht digital signiert ist. Möglicherweise ist die CAB-Datei beschädigt.</String>
   <String Id="IDS_ERROR_1330">Eine erforderliche Datei kann nicht installiert werden, da die CAB-Datei [2] über eine ungültige digitale Signatur verfügt. Möglicherweise ist die CAB-Datei beschädigt.{ WinVerifyTrust hat Fehler [3] zurückgegeben.}</String>
   <String Id="IDS_ERROR_1331">Fehler beim Kopieren der Datei [2]: CRC-Fehler.</String>
   <String Id="IDS_ERROR_1332">Fehler beim Patchen der Datei [2]: CRC-Fehler.</String>
   <String Id="IDS_ERROR_1333">Fehler beim Patchen der Datei [2]: CRC-Fehler.</String>
   <String Id="IDS_ERROR_1334">Datei '[2]' kann nicht installiert werden, da sie nicht in CAB-Datei '[3]' gefunden wurde. Möglicherweise liegt ein Netzwerkfehler, ein Fehler beim Lesen der CD-ROM oder ein Problem mit diesem Paket vor.</String>
   <String Id="IDS_ERROR_1335">Die zur Installation erforderliche CAB-Datei '[2]' ist beschädigt und kann nicht verwendet werden. Möglicherweise liegt ein Netzwerkfehler, ein Fehler beim Lesen der CD-ROM oder ein Problem mit diesem Paket vor.</String>
   <String Id="IDS_ERROR_1336">Fehler beim Erstellen einer temporären Datei, die zum Abschluss dieser Installation benötigt wird. Ordner: [3]. Systemfehlercode: [2]</String>
   <String Id="IDS_ERROR_1401">Schlüssel [2] konnte nicht erstellt werden. {{ Systemfehler [3].}} Überprüfen Sie, ob Sie über ausreichende Zugriffsrechte für diesen Schlüssel verfügen, oder wenden Sie sich an den Support.</String>
   <String Id="IDS_ERROR_1402">Schlüssel konnte nicht geöffnet werden: [2]. {{ Systemfehler [3].}} Überprüfen Sie, ob Sie über ausreichende Zugriffsrechte für diesen Schlüssel verfügen, oder wenden Sie sich an den Support.</String>
   <String Id="IDS_ERROR_1403">Wert [2] konnte nicht aus Schlüssel [3] gelöscht werden. {{ Systemfehler [4].}} Überprüfen Sie, ob Sie über ausreichende Zugriffsrechte für diesen Schlüssel verfügen, oder wenden Sie sich an den Support.</String>
   <String Id="IDS_ERROR_1404">Schlüssel [2] konnte nicht gelöscht werden. {{ Systemfehler [3].}} Überprüfen Sie, ob Sie über ausreichende Zugriffsrechte für diesen Schlüssel verfügen, oder wenden Sie sich an den Support.</String>
   <String Id="IDS_ERROR_1405">Wert [2] konnte nicht aus Schlüssel [3] gelesen werden. {{ Systemfehler [4].}} Überprüfen Sie, ob Sie über ausreichende Zugriffsrechte für diesen Schlüssel verfügen, oder wenden Sie sich an den Support.</String>
   <String Id="IDS_ERROR_1406">Wert [2] für Schlüssel [3] konnte nicht geschrieben werden. {{ Systemfehler [4].}} Überprüfen Sie, ob Sie über ausreichende Zugriffsrechte für diesen Schlüssel verfügen, oder wenden Sie sich an den Support.</String>
   <String Id="IDS_ERROR_1407">Wertenamen für Schlüssel [2] konnten nicht abgerufen werden. {{ Systemfehler [3].}} Überprüfen Sie, ob Sie über ausreichende Zugriffsrechte für diesen Schlüssel verfügen, oder wenden Sie sich an den Support.</String>
   <String Id="IDS_ERROR_1408">Namen der untergeordneten Schlüssel von Schlüssel [2] konnten nicht abgerufen werden. {{ Systemfehler [3].}} Überprüfen Sie, ob Sie über ausreichende Zugriffsrechte für diesen Schlüssel verfügen, oder wenden Sie sich an den Support.</String>
   <String Id="IDS_ERROR_1409">Sicherheitsinformationen für Schlüssel [2] konnten nicht gelesen werden. {{ Systemfehler [3].}} Überprüfen Sie, ob Sie über ausreichende Zugriffsrechte für diesen Schlüssel verfügen, oder wenden Sie sich an den Support.</String>
   <String Id="IDS_ERROR_1410">Der verfügbare Registrierungsspeicher konnte nicht vergrößert werden. Die Installation dieser Anwendung erfordert [2] KB freien Registrierungsspeicher.</String>
   <String Id="IDS_ERROR_1500">Es wird bereits eine Installation ausgeführt. Sie müssen die erste Installation zunächst abschließen, bevor Sie mit dieser Installation fortfahren können.</String>
   <String Id="IDS_ERROR_1501">Fehler beim Zugriff auf geschützte Daten. Stellen Sie sicher, dass das Windows-Installationsprogramm ordnungsgemäß konfiguriert ist, und wiederholen Sie die Installation.</String>
   <String Id="IDS_ERROR_1502">Benutzer [2] hat die Installation von Produkt [3] bereits initialisiert. Dieser Benutzer muss die Installation erneut ausführen, bevor dieses Produkt verwendet werden kann. Ihre aktuelle Installation wird jetzt fortgesetzt.</String>
   <String Id="IDS_ERROR_1503">Benutzer [2] hat die Installation von Produkt [3] bereits initialisiert. Dieser Benutzer muss die Installation erneut ausführen, bevor dieses Produkt verwendet werden kann.</String>
   <String Id="IDS_ERROR_1601">Nicht genügend freier Speicher auf der Festplatte -- Volume: '[2]'; benötigter Speicher: [3] KB; verfügbarer Speicher: [4] KB. Geben Sie Festplattenspeicher frei und wiederholen Sie den Vorgang.</String>
   <String Id="IDS_ERROR_1602">Möchten Sie den Vorgang wirklich abbrechen?</String>
   <String Id="IDS_ERROR_1603">Die Datei [2][3] wird im Augenblick verwendet{ von folgendem Prozess: Name: [4], ID: [5], Fenstertitel: [6]}. Schließen Sie diese Anwendung und wiederholen Sie den Vorgang.</String>
   <String Id="IDS_ERROR_1604">Das Produkt [2] ist bereits installiert und verhindert die Installation dieses Produkts. Die beiden Produkte sind inkompatibel.</String>
   <String Id="IDS_ERROR_1605">Nicht genügend freier Speicher auf der Festplatte -- Volume: [2]; benötigter Speicher: [3] KB; verfügbarer Speicher: [4] KB.  Falls die Rollback-Funktion deaktiviert ist, steht genügend Speicher zur Verfügung. Klicken Sie auf 'Abbrechen', um die Installation zu beenden, auf 'Wiederholen', um den verfügbaren Speicher erneut zu überprüfen oder auf "Ignorieren", um ohne Rollback fortzufahren.</String>
   <String Id="IDS_ERROR_1606">Zugriff auf Netzwerkadresse [2] nicht möglich.</String>
   <String Id="IDS_ERROR_1607">Die folgenden Anwendungen sollten geschlossen werden, bevor Sie die Installation fortsetzen:</String>
   <String Id="IDS_ERROR_1608">Keine zur Installation dieses Produkts erforderlichen passenden Produkte auf dem Computer gefunden.</String>
   <String Id="IDS_ERROR_1609">Beim Anwenden der Sicherheitseinstellungen ist ein Fehler aufgetreten. [2] ist kein gültiger Benutzer bzw. keine gültige Gruppe. Möglicherweise liegt ein Problem mit dem Paket vor oder es konnte keine Verbindung mit einem Domänencontroller im Netzwerk hergestellt werden. Überprüfen Sie die Netzwerkverbindung und klicken Sie auf 'Wiederholen'. Klicken Sie auf 'Abbrechen', um die Installation zu beenden. Benutzer-SID wurde nicht gefunden, Systemfehler [3]</String>
   <String Id="IDS_ERROR_1651">Admin-Benutzer hat ein Patch für eine auf Benutzer- oder Computerbasis verwaltete Anwendung nicht aufgespielt, die sich im angekündigten Zustand befindet.</String>
   <String Id="IDS_ERROR_1701">Der Schlüssel [2] ist ungültig. Überprüfen Sie, ob Sie den richtigen Schlüssel eingegeben haben.</String>
   <String Id="IDS_ERROR_1702">Das Installationsprogramm muss Ihren Computer neu starten, bevor die Konfiguration von [2] fortgesetzt werden kann. Klicken Sie auf 'Ja', um den Computer jetzt neu zu starten, oder auf 'Nein', um den Computer später manuell neu zu starten.</String>
   <String Id="IDS_ERROR_1703">Sie müssen Ihren Computer neu starten, damit die geänderte Konfiguration von [2] wirksam wird. Klicken Sie auf 'Ja', um den Computer jetzt neu zu starten, oder auf 'Nein', um den Computer später manuell neu zu starten.</String>
   <String Id="IDS_ERROR_1704">Eine Installation von [2] wurde unterbrochen. Sie müssen die von dieser Installation vorgenommenen Änderungen rückgängig machen, bevor Sie fortfahren können. Möchten Sie diese Änderungen rückgängig machen?</String>
   <String Id="IDS_ERROR_1705">Im Augenblick wird eine weitere Installation dieses Produkts ausgeführt. Sie müssen die von dieser Installation vorgenommenen Änderungen rückgängig machen, bevor Sie fortfahren können. Möchten Sie diese Änderungen rückgängig machen?</String>
   <String Id="IDS_ERROR_1706">Für Produkt [2] wurde kein Installationspaket gefunden. Das Windows-Installationsprogramm kann nicht fortgesetzt werden.</String>
   <String Id="IDS_ERROR_1707">Installation erfolgreich abgeschlossen.</String>
   <String Id="IDS_ERROR_1708">Installationsvorgang fehlgeschlagen.</String>
   <String Id="IDS_ERROR_1709">Produkt: [2] -- [3]</String>
   <String Id="IDS_ERROR_1710">Sie können entweder den ursprünglichen Zustand Ihres Computers wiederherstellen oder die Installation später fortsetzen. Möchten Sie wiederherstellen?</String>
   <String Id="IDS_ERROR_1711">Fehler beim Schreiben von Installationsinformationen auf die Festplatte. Überprüfen Sie, ob genügend Plattenspeicher verfügbar ist, und klicken Sie auf 'Wiederholen'. Oder klicken Sie auf 'Abbrechen', um die Installation abzubrechen.</String>
   <String Id="IDS_ERROR_1712">Eine oder mehrere Datei(en) nicht gefunden, die zum Wiederherstellen des ursprünglichen Zustands Ihres Computers benötigt werden. Wiederherstellen nicht möglich.</String>
   <String Id="IDS_ERROR_1713">Ein von [2] benötigtes Produkt kann nicht installiert werden. Wenden Sie sich an den technischen Support. {{Systemfehler: [3].}}</String>
   <String Id="IDS_ERROR_1714">Die ältere Version von [2] kann nicht entfernt werden. Wenden Sie sich an den technischen Support. {{Systemfehler [3].}}</String>
   <String Id="IDS_ERROR_1715">[2] wurde installiert.</String>
   <String Id="IDS_ERROR_1716">[2] wurde konfiguriert.</String>
   <String Id="IDS_ERROR_1717">[2] wurde entfernt.</String>
   <String Id="IDS_ERROR_1718">Datei [2] wurde von der Richtlinie für digitale Signaturen abgelehnt.</String>
   <String Id="IDS_ERROR_1719">Zugriff auf den Windows Installer-Dienst nicht möglich. Wenden Sie sich an den Support, um sicherzustellen, dass der Windows Installer-Dienst ordnungsgemäß registriert und aktiviert ist.</String>
   <String Id="IDS_ERROR_1720">Es liegt ein Problem mit dem Windows Installer-Paket vor. Ein zum Abschließen dieser Installation benötigtes Skript konnte nicht ausgeführt werden. Wenden Sie sich an den Support oder den Paketanbieter. Benutzerdefinierte Aktion [2], Skriptfehler [3], [4]: [5] Zeile [6], Spalte [7], [8]</String>
   <String Id="IDS_ERROR_1721">Es liegt ein Problem mit dem Windows Installer-Paket vor. Ein zum Abschließen dieser Installation benötigtes Programm konnte nicht ausgeführt werden. Wenden Sie sich an den Support oder den Paketanbieter. Aktion: [2], Pfad: [3], Befehl: [4]</String>
   <String Id="IDS_ERROR_1722">Es liegt ein Problem mit dem Windows Installer-Paket vor. Ein im Rahmen des Setups ausgeführtes Programm wurde nicht wie erwartet abgeschlossen. Wenden Sie sich an den Support oder den Paketanbieter. Aktion: [2], Pfad: [3], Befehl: [4]</String>
   <String Id="IDS_ERROR_1723">Es liegt ein Problem mit dem Windows Installer-Paket vor. Eine zum Abschließen dieser Installation erforderliche DLL konnte nicht ausgeführt werden. Wenden Sie sich an den Support oder den Paketanbieter. Aktion [2], Eintrag: [3], Bibliothek: [4]</String>
   <String Id="IDS_ERROR_1724">Entfernen erfolgreich abgeschlossen.</String>
   <String Id="IDS_ERROR_1725">Fehler beim Entfernen.</String>
   <String Id="IDS_ERROR_1726">Ankündigung erfolgreich abgeschlossen.</String>
   <String Id="IDS_ERROR_1727">Fehler bei der Ankündigung.</String>
   <String Id="IDS_ERROR_1728">Konfiguration erfolgreich abgeschlossen.</String>
   <String Id="IDS_ERROR_1729">Fehler bei der Konfiguration.</String>
   <String Id="IDS_ERROR_1730">Sie benötigen Administratorrechte, um diese Anwendung zu entfernen. Melden Sie sich als Administrator an, oder wenden Sie sich an den technischen Support.</String>
   <String Id="IDS_ERROR_1731">Das Quellinstallationspaket für Produkt [2] wurde nicht mit dem Clientpaket synchronisiert. Führen Sie die Installation mit einer gültigen Kopie des Installationspakets '[3]' erneut aus.</String>
   <String Id="IDS_ERROR_1732">Um die Installation von [2] abzuschließen, müssen Sie den Computer neu starten. Es sind zurzeit andere Benutzer an diesem Computer angemeldet, deren nicht gespeicherte Daten bei einem Neustart möglicherweise verloren gehen. Möchten Sie jetzt neu starten?</String>
   <String Id="IDS_ERROR_1801">Pfad [2] ist ungültig. Geben Sie einen gültigen Pfad an.</String>
   <String Id="IDS_ERROR_1802">Nicht genügend Arbeitsspeicher vorhanden. Beenden Sie andere Anwendungen und wiederholen Sie den Vorgang.</String>
   <String Id="IDS_ERROR_1803">In Laufwerk [2] ist kein Datenträger eingelegt. Legen Sie einen Datenträger ein und klicken Sie auf 'Wiederholen'. Oder klicken Sie auf 'Abbrechen', um zu dem zuvor ausgewählten Laufwerk zurückzukehren.</String>
   <String Id="IDS_ERROR_1804">In Laufwerk [2] ist kein Datenträger eingelegt. Legen Sie einen Datenträger ein und klicken Sie auf 'Wiederholen'. Oder klicken Sie auf 'Abbrechen', um zum Dialogfeld 'Durchsuchen' zurückzukehren und ein anderes Laufwerk auszuwählen.</String>
   <String Id="IDS_ERROR_1805">Ordner [2] ist nicht vorhanden. Geben Sie einen Pfad zu einem vorhandenen Ordner an.</String>
   <String Id="IDS_ERROR_1806">Ihre Berechtigungen reichen zum Lesen dieses Ordners nicht aus.</String>
   <String Id="IDS_ERROR_1807">Es konnte kein gültiger Zielordner für die Installation ermittelt werden.</String>
   <String Id="IDS_ERROR_1901">Fehler beim Lesen der Quellinstallationsdatenbank: [2].</String>
   <String Id="IDS_ERROR_1902">Planen des Computerneustarts: Datei [2] wird in [3] umbenannt. Der Computer muss neu gestartet werden, um den Vorgang abzuschließen.</String>
   <String Id="IDS_ERROR_1903">Planen des Computerneustarts: Datei [2] wird gelöscht. Der Computer muss neu gestartet werden, um den Vorgang abzuschließen.</String>
   <String Id="IDS_ERROR_1904">Fehler beim Registrieren von Modul [2]. HRESULT [3]. Wenden Sie sich an den Support.</String>
   <String Id="IDS_ERROR_1905">Fehler beim Entfernen von Modul [2] aus der Registrierung. HRESULT [3]. Wenden Sie sich an den Support.</String>
   <String Id="IDS_ERROR_1906">Fehler beim Zwischenspeichern von Paket [2]. Fehler: [3]. Wenden Sie sich an den Support.</String>
   <String Id="IDS_ERROR_1907">Schriftart [2] konnte nicht registriert werden. Überprüfen Sie, ob Sie über ausreichende Berechtigungen zum Installieren von Schriftarten besitzen und ob das System diese Schriftart unterstützt.</String>
   <String Id="IDS_ERROR_1908">Schriftart [2] konnte nicht aus der Registrierung entfernt werden. Überprüfen Sie, ob Sie ausreichende Berechtigungen zum Entfernen von Schriftarten besitzen.</String>
   <String Id="IDS_ERROR_1909">Verknüpfung [2] konnte nicht erstellt werden. Überprüfen Sie, ob der Zielordner vorhanden ist und Sie darauf zugreifen können.</String>
   <String Id="IDS_ERROR_1910">Verknüpfung [2] konnte nicht entfernt werden. Überprüfen Sie, ob die Verknüpfungsdatei vorhanden ist und Sie darauf zugreifen können.</String>
   <String Id="IDS_ERROR_1911">Typbibliothek für Datei [2] konnte nicht registriert werden. Wenden Sie sich an den Support.</String>
   <String Id="IDS_ERROR_1912">Typbibliothek für Datei [2] konnte nicht aus der Registrierung entfernt werden. Wenden Sie sich an den Support.</String>
   <String Id="IDS_ERROR_1913">Aktualisieren der INI-Datei [2][3] war nicht möglich: Überprüfen Sie, ob die Datei vorhanden ist und Sie darauf zugreifen können.</String>
   <String Id="IDS_ERROR_1914">Es konnte nicht festgelegt werden, dass Datei [3] beim Neustart des Computers durch Datei [2] ersetzt werden soll. Überprüfen Sie, ob Sie über Schreibberechtigungen für Datei [3] verfügen.</String>
   <String Id="IDS_ERROR_1915">Fehler beim Entfernen des ODBC-Treibermanagers, ODBC-Fehler [2]: [3]. Wenden Sie sich an den Support.</String>
   <String Id="IDS_ERROR_1916">Fehler bei der Installation des ODBC-Treibermanagers. ODBC-Fehler [2]: [3]. Wenden Sie sich an den Support.</String>
   <String Id="IDS_ERROR_1917">Fehler beim Entfernen des ODBC-Treibers: [4], ODBC-Fehler [2]: [3]. Überprüfen Sie, ob Sie ausreichende Berechtigungen zum Entfernen von ODBC-Treibern besitzen.</String>
   <String Id="IDS_ERROR_1918">Fehler beim Installieren des ODBC-Treibers: [4], ODBC-Fehler [2]: [3]. Überprüfen Sie, ob die Datei [4] vorhanden ist und Sie darauf zugreifen können.</String>
   <String Id="IDS_ERROR_1919">Fehler beim Konfigurieren der ODBC-Datenquelle: [4], ODBC-Fehler [2]: [3]. Überprüfen Sie, ob die Datei [4] vorhanden ist und Sie darauf zugreifen können.</String>
   <String Id="IDS_ERROR_1920">Dienst [2] ([3]) konnte nicht gestartet werden. Überprüfen Sie, ob Sie ausreichende Berechtigungen zum Starten von Systemdiensten besitzen.</String>
   <String Id="IDS_ERROR_1921">Dienst [2] ([3]) konnte nicht angehalten werden. Überprüfen Sie, ob Sie ausreichende Berechtigungen zum Anhalten von Systemdiensten besitzen.</String>
   <String Id="IDS_ERROR_1922">Dienst [2] ([3]) konnte nicht gelöscht werden. Überprüfen Sie, ob Sie ausreichende Berechtigungen zum Entfernen von Systemdiensten besitzen.</String>
   <String Id="IDS_ERROR_1923">Dienst [2] ([3]) konnte nicht installiert werden. Überprüfen Sie, ob Sie ausreichende Berechtigungen zum Installieren von Systemdiensten besitzen.</String>
   <String Id="IDS_ERROR_1924">Umgebungsvariable [2] konnte nicht aktualisiert werden. Überprüfen Sie, ob Sie ausreichende Berechtigungen zum Ändern von Umgebungsvariablen besitzen.</String>
   <String Id="IDS_ERROR_1925">Sie besitzen keine ausreichenden Berechtigungen, um diese Installation für alle Benutzer dieses Computers auszuführen. Melden Sie sich als Administrator an und wiederholen Sie den Vorgang.</String>
   <String Id="IDS_ERROR_1926">Sicherheitsberechtigungen für Datei [3] konnten nicht festgelegt werden. Fehler: [2]. Überprüfen Sie, ob Sie ausreichende Berechtigungen zum Ändern der Sicherheitsberechtigungen für diese Datei besitzen.</String>
   <String Id="IDS_ERROR_1927">Komponentendienste (COM+ 1.0) sind auf diesem Computer nicht installiert. Um diese Installation erfolgreich abzuschließen, müssen Komponentendienste installiert sein. Komponentendienste stehen unter Windows 2000 zur Verfügung.</String>
   <String Id="IDS_ERROR_1928">Fehler beim Registrieren einer COM+-Anwendung. Wenden Sie sich an den Support, um weitere Informationen zu erhalten.</String>
   <String Id="IDS_ERROR_1929">Fehler beim Entfernen einer COM+-Anwendung aus der Registrierung. Wenden Sie sich an den Support, um weitere Informationen zu erhalten.</String>
   <String Id="IDS_ERROR_1930">Die Beschreibung für Dienst '[2]' ([3]) konnte nicht geändert werden.</String>
   <String Id="IDS_ERROR_1931">Der Windows Installer-Dienst kann Systemdatei [2] nicht aktualisieren, weil die Datei von Windows geschützt wird. Sie müssen möglicherweise Ihr Betriebssystem aktualisieren, damit dieses Programm ordnungsgemäß funktioniert. {{Paketversion: [3], vom Betriebssystem geschützte Version: [4]}}</String>
   <String Id="IDS_ERROR_1932">Der Windows Installer-Dienst kann die geschützte Windows-Datei [2] nicht aktualisieren. {{Paketversion: [3], vom Betriebssystem geschützte Version: [4], SFP-Fehler: [5]}}</String>
   <String Id="IDS_ERROR_1933">Der Windows Installer-Dienst kann eine oder mehrere geschützte Windows-Dateien nicht aktualisieren. SFP-Fehler: [2]. Liste der geschützten Dateien: [3]</String>
   <String Id="IDS_ERROR_1934">Benutzerinstallationen sind auf diesem Computer durch Richtlinien deaktiviert.</String>
   <String Id="IDS_ERROR_1935">Fehler bei der Installation der Assemblykomponente [2]. HRESULT: [3]. {{Assemblyschnittstelle: [4], Funktion: [5], Assemblyname: [6]}}</String>
   <String Id="IDS_ERROR_1936">Fehler bei der Installation der Assembly '[6]'. Die Assembly weist keinen starken Namen auf oder ist nicht mit der minimalen Schlüssellänge signiert. HRESULT: [3]. {{Assemblyschnittstelle: [4], Funktion: [5], Komponente: [2]}}</String>
   <String Id="IDS_ERROR_1937">Fehler bei der Installation der Assembly '[6]'. Die Signatur bzw. der Katalog kann nicht verifiziert werden bzw. ist nicht gültig. HRESULT: [3]. {{Assemblyschnittstelle: [4], Funktion: [5], Komponente: [2]}}</String>
   <String Id="IDS_ERROR_1938">Fehler bei der Installation der Assembly '[6]'. Mindestens ein Modul der Assembly wurde nicht gefunden. HRESULT: [3]. {{Assemblyschnittstelle: [4], Funktion: [5], Komponente: [2]}}</String>
   <String Id="IDS_ERROR_2101">Verknüpfungen werden vom Betriebssystem nicht unterstützt.</String>
   <String Id="IDS_ERROR_2102">Ungültige .ini-Aktion: [2]</String>
   <String Id="IDS_ERROR_2103">Pfad für Shell-Ordner [2] konnte nicht aufgelöst werden.</String>
   <String Id="IDS_ERROR_2104">.ini-Datei wird geschrieben: [3]: Systemfehler: [2].</String>
   <String Id="IDS_ERROR_2105">Fehler beim Erstellen der Verknüpfung [3]. Systemfehler: [2].</String>
   <String Id="IDS_ERROR_2106">Fehler beim Löschen der Verknüpfung [3]. Systemfehler: [2].</String>
   <String Id="IDS_ERROR_2107">Fehler [3] beim Registrieren der Typbibliothek [2].</String>
   <String Id="IDS_ERROR_2108">Fehler [3] beim Entfernen der Typbibliothek [2] aus der Registrierung.</String>
   <String Id="IDS_ERROR_2109">Abschnitt für .ini-Aktion fehlt.</String>
   <String Id="IDS_ERROR_2110">Schlüssel für .ini-Aktion fehlt.</String>
   <String Id="IDS_ERROR_2111">Fehler beim Ermitteln ausgeführter Anwendungen. Es konnten keine Leistungsdaten abgerufen werden. Der Registrierungsvorgang hat Folgendes zurückgegeben: [2].</String>
   <String Id="IDS_ERROR_2112">Fehler beim Ermitteln ausgeführter Anwendungen, Leistungsindex konnten nicht abgerufen werden. Der Registrierungsvorgang hat Folgendes zurückgegeben: [2].</String>
   <String Id="IDS_ERROR_2113">Fehler beim Ermitteln ausgeführter Anwendungen.</String>
   <String Id="IDS_ERROR_2200">Datenbank: [2]. Fehler beim Erstellen des Datenbankobjekts, Modus = [3].</String>
   <String Id="IDS_ERROR_2201">Datenbank: [2]. Fehler bei der Initialisierung. Nicht genügend Arbeitsspeicher.</String>
   <String Id="IDS_ERROR_2202">Datenbank: [2]. Fehler beim Datenzugriff. Nicht genügend Arbeitsspeicher.</String>
   <String Id="IDS_ERROR_2203">Datenbank: [2]. Datenbankdatei kann nicht geöffnet werden. Systemfehler: [3].</String>
   <String Id="IDS_ERROR_2204">Datenbank: [2]. Tabelle bereits vorhanden: [3].</String>
   <String Id="IDS_ERROR_2205">Datenbank: [2]. Tabelle nicht vorhanden: [3].</String>
   <String Id="IDS_ERROR_2206">Datenbank: [2]. Tabelle konnte nicht gelöscht werden: [3].</String>
   <String Id="IDS_ERROR_2207">Datenbank: [2]. Prioritätsverletzung.</String>
   <String Id="IDS_ERROR_2208">Datenbank: [2]. Nicht genügend Parameter zum Ausführen.</String>
   <String Id="IDS_ERROR_2209">Datenbank: [2]. Ungültiger Cursorstatus.</String>
   <String Id="IDS_ERROR_2210">Datenbank: [2]. Ungültiger Aktualisierungsdatentyp in Spalte [3].</String>
   <String Id="IDS_ERROR_2211">Datenbank: [2]. Datenbanktabelle [3] konnte nicht erstellt werden.</String>
   <String Id="IDS_ERROR_2212">Datenbank: [2]. Schreiben in Datenbank nicht möglich.</String>
   <String Id="IDS_ERROR_2213">Datenbank: [2]. Fehler beim Speichern von Datenbanktabellen.</String>
   <String Id="IDS_ERROR_2214">Datenbank: [2]. Fehler beim Schreiben der Exportdatei: [3].</String>
   <String Id="IDS_ERROR_2215">Datenbank: [2]. Importdatei kann nicht geöffnet werden: [3].</String>
   <String Id="IDS_ERROR_2216">Datenbank: [2]. Fehler im Importdateiformat: [3], Zeile [4].</String>
   <String Id="IDS_ERROR_2217">Datenbank: [2]. Unzulässiger Status von CreateOutputDatabase [3].</String>
   <String Id="IDS_ERROR_2218">Datenbank: [2]. Es wurde kein Tabellenname angegeben.</String>
   <String Id="IDS_ERROR_2219">Datenbank: [2]. Ungültiges Installer-Datenbankformat.</String>
   <String Id="IDS_ERROR_2220">Datenbank: [2]. Ungültige Zeilen-/Felddaten.</String>
   <String Id="IDS_ERROR_2221">Datenbank: [2]. Codepage-Konflikt in Importdatei: [3].</String>
   <String Id="IDS_ERROR_2222">Datenbank: [2]. Die Codepage [3] zum Transformieren oder Zusammenführen weicht von der Datenbank-Codepage [4] ab.</String>
   <String Id="IDS_ERROR_2223">Datenbank: [2]. Die Datenbanken sind identisch. Es wurde keine Transformation generiert.</String>
   <String Id="IDS_ERROR_2224">Datenbank: [2]. GenerateTransform: Die Datenbank ist beschädigt. Tabelle: [3].</String>
   <String Id="IDS_ERROR_2225">Datenbank: [2]. Transform: Eine temporäre Tabelle kann nicht transformiert werden. Tabelle: [3].</String>
   <String Id="IDS_ERROR_2226">Datenbank: [2]. Fehler bei der Transformation.</String>
   <String Id="IDS_ERROR_2227">Datenbank: [2]. Ungültiger Bezeichner '[3]' in SQL-Abfrage: [4].</String>
   <String Id="IDS_ERROR_2228">Datenbank: [2]. Unbekannte Tabelle '[3]' in SQL-Abfrage: [4].</String>
   <String Id="IDS_ERROR_2229">Datenbank: [2]. Tabelle '[3]' in der SQL-Abfrage konnte nicht geladen werden: [4].</String>
   <String Id="IDS_ERROR_2230">Datenbank: [2]. Wiederholte Tabelle '[3]' in SQL-Abfrage: [4].</String>
   <String Id="IDS_ERROR_2231">Datenbank: [2]. ')' fehlt in der SQL-Abfrage: [3].</String>
   <String Id="IDS_ERROR_2232">Datenbank: [2]. Unerwartetes Token '[3]' in der SQL-Abfrage: [4].</String>
   <String Id="IDS_ERROR_2233">Datenbank: [2]. Keine Spalten in der SELECT-Klausel der SQL-Abfrage: [3].</String>
   <String Id="IDS_ERROR_2234">Datenbank: [2]. Keine Spalten in der ORDER BY-Klausel der SQL-Abfrage: [3].</String>
   <String Id="IDS_ERROR_2235">Datenbank: [2]. Spalte '[3]' nicht vorhanden oder mehrdeutig in der SQL-Abfrage: [4].</String>
   <String Id="IDS_ERROR_2236">Datenbank: [2]. Ungültiger Operator '[3]' in SQL-Abfrage: [4].</String>
   <String Id="IDS_ERROR_2237">Datenbank: [2]. Ungültige oder fehlende Abfragezeichenfolge: [3].</String>
   <String Id="IDS_ERROR_2238">Datenbank: [2]. Fehlende FROM-Klausel in SQL-Abfrage: [3].</String>
   <String Id="IDS_ERROR_2239">Datenbank: [2]. Nicht genügend Werte in der INSERT-SQL-Anweisung.</String>
   <String Id="IDS_ERROR_2240">Datenbank: [2]. Fehlende Aktualisierungsspalten in der UPDATE-SQL-Anweisung.</String>
   <String Id="IDS_ERROR_2241">Datenbank: [2]. Fehlende Einfügespalten in der INSERT-SQL-Anweisung.</String>
   <String Id="IDS_ERROR_2242">Datenbank: [2]. Spalte [3] wiederholt.</String>
   <String Id="IDS_ERROR_2243">Datenbank: [2]. Für die Tabellenerstellung wurden keine primären Spalten definiert.</String>
   <String Id="IDS_ERROR_2244">Datenbank: [2]. Ungültiger Typbezeichner '[3]' in der SQL-Abfrage [4].</String>
   <String Id="IDS_ERROR_2245">Fehler [3] bei 'IStorage::Stat'.</String>
   <String Id="IDS_ERROR_2246">Datenbank: [2]. Ungültiges Installer-Transformationsformat.</String>
   <String Id="IDS_ERROR_2247">Datenbank: [2] Fehler beim Lesen/Schreiben des Transformationsstreams.</String>
   <String Id="IDS_ERROR_2248">Datenbank: [2] GenerateTransform/Merge: Der Spaltentyp in der Basistabelle stimmt nicht mit der Referenztabelle überein. Tabelle: [3] Spaltennummer: [4].</String>
   <String Id="IDS_ERROR_2249">Datenbank: [2] GenerateTransform: Die Basistabelle enthält mehr Spalten als die Referenztabelle. Tabelle: [3].</String>
   <String Id="IDS_ERROR_2250">Datenbank: [2]. Transform: Eine vorhandene Zeile kann nicht hinzugefügt werden. Tabelle: [3].</String>
   <String Id="IDS_ERROR_2251">Datenbank: [2] Transform: Eine nicht vorhandene Zeile kann nicht gelöscht werden. Tabelle: [3].</String>
   <String Id="IDS_ERROR_2252">Datenbank: [2] Transform: Eine vorhandene Tabelle kann nicht hinzugefügt werden. Tabelle: [3].</String>
   <String Id="IDS_ERROR_2253">Datenbank: [2] Transform: Eine nicht vorhandene Tabelle kann nicht gelöscht werden. Tabelle: [3].</String>
   <String Id="IDS_ERROR_2254">Datenbank: [2] Transform: Eine nicht vorhandene Zeile kann nicht aktualisiert werden. Tabelle: [3].</String>
   <String Id="IDS_ERROR_2255">Datenbank: [2]. Transform: Eine Spalte mit diesem Namen ist bereits vorhanden. Tabelle: [3] Spalte: [4].</String>
   <String Id="IDS_ERROR_2256">Datenbank: [2] GenerateTransform/Merge: Die Anzahl der primären Schlüssel in der Basistabelle stimmt nicht mit der Referenztabelle überein. Tabelle: [3].</String>
   <String Id="IDS_ERROR_2257">Datenbank: [2]. Es wurde versucht, eine schreibgeschützte Tabelle zu ändern: [3].</String>
   <String Id="IDS_ERROR_2258">Datenbank: [2]. Typkonflikt in Parameter: [3].</String>
   <String Id="IDS_ERROR_2259">Datenbank: [2]. Fehler beim Aktualisieren der Tabelle(n)</String>
   <String Id="IDS_ERROR_2260">Fehler beim CopyTo-Vorgang in den Speicher. Systemfehler: [3].</String>
   <String Id="IDS_ERROR_2261">Fehler beim Entfernen des Streams [2]. Systemfehler: [3].</String>
   <String Id="IDS_ERROR_2262">Stream ist nicht vorhanden: [2]. Systemfehler: [3].</String>
   <String Id="IDS_ERROR_2263">Fehler beim Öffnen des Streams [2]. Systemfehler: [3].</String>
   <String Id="IDS_ERROR_2264">Fehler beim Entfernen des Streams [2]. Systemfehler: [3].</String>
   <String Id="IDS_ERROR_2265">Fehler beim Ausführen eines Commits für den Speicher. Systemfehler: [3].</String>
   <String Id="IDS_ERROR_2266">Fehler beim Ausführen eines Rollbacks für den Speicher. Systemfehler: [3].</String>
   <String Id="IDS_ERROR_2267">Fehler beim Löschen des Speichers [2]. Systemfehler: [3].</String>
   <String Id="IDS_ERROR_2268">Datenbank: [2]. Merge: In [3] Tabellen sind beim Zusammenführen Konflikte aufgetreten.</String>
   <String Id="IDS_ERROR_2269">Datenbank: [2]. Merge: Unterschiedliche Spaltenanzahl in Tabelle '[3]' der beiden Datenbanken.</String>
   <String Id="IDS_ERROR_2270">Datenbank: [2]. GenerateTransform/Merge: Der Spaltenname in der Basistabelle stimmt nicht mit der Referenztabelle überein. Tabelle: [3] Spaltennummer: [4].</String>
   <String Id="IDS_ERROR_2271">Fehler beim Schreiben von 'SummaryInformation' für die Transformation.</String>
   <String Id="IDS_ERROR_2272">Datenbank: [2]. 'MergeDatabase' schreibt keine Änderungen, da die Datenbank schreibgeschützt geöffnet ist.</String>
   <String Id="IDS_ERROR_2273">Datenbank: [2]. MergeDatabase: Ein Verweis auf die Basisdatenbank wurde als Referenzdatenbank übergeben.</String>
   <String Id="IDS_ERROR_2274">Datenbank: [2]. MergeDatabase: In die ERROR-Tabelle können keine Fehler geschrieben werden. Dies ist möglicherweise auf eine Spalte in einer vordefinierten ERROR-Tabelle zurückzuführen, die keine NULL-Werte zulässt.</String>
   <String Id="IDS_ERROR_2275">Datenbank: [2]. Der angegebene MODIFY-Vorgang für [3] ist für Tabellenverknüpfungen ungültig.</String>
   <String Id="IDS_ERROR_2276">Datenbank: [2]. Codepage [3] wird nicht vom System unterstützt.</String>
   <String Id="IDS_ERROR_2277">Datenbank: [2]. Fehler beim Speichern der Tabelle [3].</String>
   <String Id="IDS_ERROR_2278">Datenbank: [2]. Der zulässige Höchstwert von 32 Ausdrücken wurde in der WHERE-Klausel der SQL-Abfrage überschritten: [3].</String>
   <String Id="IDS_ERROR_2279">Datenbank: [2]. Transform: Zu viele Spalten in Basistabelle [3].</String>
   <String Id="IDS_ERROR_2280">Datenbank: [2]. Fehler beim Erstellen der Spalte [3] für Tabelle [4].</String>
   <String Id="IDS_ERROR_2281">Stream [2] konnte nicht umbenannt werden. Systemfehler: [3].</String>
   <String Id="IDS_ERROR_2282">Ungültiger Streamname [2].</String>
   <String Id="IDS_ERROR_2302">Patch-Benachrichtigung: Bisher wurden [2] Bytes gepatcht.</String>
   <String Id="IDS_ERROR_2303">Fehler beim Abrufen von Datenträgerinformationen. GetLastError: [2].</String>
   <String Id="IDS_ERROR_2304">Fehler beim Abrufen des freien Speicherplatzes auf dem Datenträger. GetLastError: [2]. Datenträger: [3].</String>
   <String Id="IDS_ERROR_2305">Fehler beim Warten auf den Patch-Thread. GetLastError: [2].</String>
   <String Id="IDS_ERROR_2306">Fehler beim Erstellen des Threads für die Patch-Anwendung. GetLastError: [2].</String>
   <String Id="IDS_ERROR_2307">Name des Quelldateischlüssels ist NULL.</String>
   <String Id="IDS_ERROR_2308">Zieldateiname ist NULL.</String>
   <String Id="IDS_ERROR_2309">Es wurde versucht, die Datei [2] zu patchen, obwohl bereits ein Patch ausgeführt wird.</String>
   <String Id="IDS_ERROR_2310">Es wurde versucht, ein Patch fortzusetzen, obwohl kein Patch ausgeführt wird.</String>
   <String Id="IDS_ERROR_2315">Fehlendes Pfadtrennzeichen: [2].</String>
   <String Id="IDS_ERROR_2318">Datei ist nicht vorhanden: [2].</String>
   <String Id="IDS_ERROR_2319">Fehler beim Festlegen des Dateiattributs: [3]. GetLastError: [2].</String>
   <String Id="IDS_ERROR_2320">Schreiben in Datei nicht möglich: [2].</String>
   <String Id="IDS_ERROR_2321">Fehler beim Erstellen der Datei: [2].</String>
   <String Id="IDS_ERROR_2322">Von Benutzer abgebrochen.</String>
   <String Id="IDS_ERROR_2323">Ungültiges Dateiattribut.</String>
   <String Id="IDS_ERROR_2324">Fehler beim Öffnen der Datei: [3] GetLastError: [2].</String>
   <String Id="IDS_ERROR_2325">Die Uhrzeitangabe für die Datei konnte nicht abgerufen werden: [3]. GetLastError: [2].</String>
   <String Id="IDS_ERROR_2326">Fehler in 'FileToDosDateTime'.</String>
   <String Id="IDS_ERROR_2327">Fehler beim Entfernen des Verzeichnisses: [3] GetLastError: [2].</String>
   <String Id="IDS_ERROR_2328">Fehler beim Abrufen der Dateiversionsinformationen für die Datei: [2].</String>
   <String Id="IDS_ERROR_2329">Fehler beim Löschen der Datei: [3]. GetLastError: [2].</String>
   <String Id="IDS_ERROR_2330">Fehler beim Abrufen der Dateiattribute: [3]. GetLastError: [2].</String>
   <String Id="IDS_ERROR_2331">Fehler beim Laden von Bibliothek [2] oder Suchen nach Einstiegspunkt [3].</String>
   <String Id="IDS_ERROR_2332">Fehler beim Abrufen der Dateiattribute. GetLastError: [2].</String>
   <String Id="IDS_ERROR_2333">Fehler beim Festlegen der Dateiattribute. GetLastError: [2].</String>
   <String Id="IDS_ERROR_2334">Fehler beim Konvertieren der Uhrzeitangabe in die Ortszeit für die Datei: [3]. GetLastError: [2].</String>
   <String Id="IDS_ERROR_2335">Pfad: [2] ist kein übergeordnetes Element von [3].</String>
   <String Id="IDS_ERROR_2336">Fehler beim Erstellen der temporären Datei im Pfad: [3]. GetLastError: [2].</String>
   <String Id="IDS_ERROR_2337">Fehler beim Schließen der Datei: [3] GetLastError: [2].</String>
   <String Id="IDS_ERROR_2338">Fehler beim Aktualisieren der Ressource für die Datei: [3] GetLastError: [2].</String>
   <String Id="IDS_ERROR_2339">Die Uhrzeitangabe für die Datei konnte nicht festgelegt werden: [3]. GetLastError: [2].</String>
   <String Id="IDS_ERROR_2340">Fehler beim Aktualisieren der Ressource für die Datei: [3], Ressource fehlt.</String>
   <String Id="IDS_ERROR_2341">Fehler beim Aktualisieren der Ressource für die Datei: [3], Ressource ist zu groß.</String>
   <String Id="IDS_ERROR_2342">Fehler beim Aktualisieren der Ressource für die Datei: [3] GetLastError: [2].</String>
   <String Id="IDS_ERROR_2343">Der angegebene Pfad ist leer.</String>
   <String Id="IDS_ERROR_2344">Die erforderliche Datei 'IMAGEHLP.DLL' zum Überprüfen der Datei wurde nicht gefunden: [2].</String>
   <String Id="IDS_ERROR_2345">[2]: Datei enthält keinen gültigen Prüfsummenwert.</String>
   <String Id="IDS_ERROR_2347">Vom Benutzer ignoriert.</String>
   <String Id="IDS_ERROR_2348">Fehler beim Lesen aus dem CAB-Stream.</String>
   <String Id="IDS_ERROR_2349">Der Kopiervorgang wurde mit anderen Informationen fortgesetzt.</String>
   <String Id="IDS_ERROR_2350">FDI-Serverfehler</String>
   <String Id="IDS_ERROR_2351">Dateischlüssel '[2]' in CAB-Datei '[3]' nicht gefunden. Die Installation kann nicht fortgesetzt werden.</String>
   <String Id="IDS_ERROR_2352">Fehler beim Initialisieren des CAB-Dateiservers. Möglicherweise fehlt die erforderliche Datei 'CABINET.DLL'.</String>
   <String Id="IDS_ERROR_2353">Keine CAB-Datei.</String>
   <String Id="IDS_ERROR_2354">CAB-Datei kann nicht verarbeitet werden.</String>
   <String Id="IDS_ERROR_2355">Beschädigte CAB-Datei.</String>
   <String Id="IDS_ERROR_2356">Die CAB-Datei wurde nicht im Stream gefunden: [2].</String>
   <String Id="IDS_ERROR_2357">Fehler beim Festlegen der Attribute.</String>
   <String Id="IDS_ERROR_2358">Fehler beim Ermitteln, ob die Datei verwendet wird: [3]. GetLastError: [2].</String>
   <String Id="IDS_ERROR_2359">Fehler beim Erstellen der Zieldatei: Die Datei wird möglicherweise verwendet.</String>
   <String Id="IDS_ERROR_2360">Statuszeiteinheit.</String>
   <String Id="IDS_ERROR_2361">Nächste CAB-Datei erforderlich.</String>
   <String Id="IDS_ERROR_2362">Ordner nicht gefunden: [2].</String>
   <String Id="IDS_ERROR_2363">Fehler beim Auflisten der Unterordner des Ordners: [2].</String>
   <String Id="IDS_ERROR_2364">Fehlerhafte Enumerationskonstante im CreateCopier-Aufruf.</String>
   <String Id="IDS_ERROR_2365">'BindImage' für .exe-Datei [2] konnte nicht ausgeführt werden.</String>
   <String Id="IDS_ERROR_2366">Benutzerfehler.</String>
   <String Id="IDS_ERROR_2367">Abbruch durch Benutzer.</String>
   <String Id="IDS_ERROR_2368">Fehler beim Abrufen der Netzwerkressourceninformationen. Fehler [2], Netzwerkpfad [3]. Erweiterter Fehler: Netzwerkanbieter [5], Fehlercode [4], Fehlerbeschreibung [6].</String>
   <String Id="IDS_ERROR_2370">Ungültiger CRC-Prüfsummenwert für Datei [2]. {Im Header ist [3] als Prüfsumme angegeben, der berechnete Wert lautet [4].}</String>
   <String Id="IDS_ERROR_2371">Patchen der Datei [2] nicht möglich. GetLastError: [3].</String>
   <String Id="IDS_ERROR_2372">Die Patch-Datei [2] ist beschädigt oder weist ein ungültiges Format auf. Es wird versucht, die Datei [3] zu patchen. GetLastError: [4].</String>
   <String Id="IDS_ERROR_2373">Datei [2] ist keine gültige Patch-Datei.</String>
   <String Id="IDS_ERROR_2374">Datei [2] ist keine gültige Zieldatei für Patch-Datei [3].</String>
   <String Id="IDS_ERROR_2375">Unbekannter Patch-Fehler: [2].</String>
   <String Id="IDS_ERROR_2376">CAB-Datei nicht gefunden.</String>
   <String Id="IDS_ERROR_2379">Fehler beim Öffnen der Datei zum Lesen: [3] GetLastError: [2].</String>
   <String Id="IDS_ERROR_2380">Fehler beim Öffnen der Datei zum Schreiben: [3]. GetLastError: [2].</String>
   <String Id="IDS_ERROR_2381">Verzeichnis nicht vorhanden: [2].</String>
   <String Id="IDS_ERROR_2382">Das Laufwerk ist nicht bereit: [2].</String>
   <String Id="IDS_ERROR_2401">Es wurde versucht, einen 64-Bit-Registrierungsvorgang auf einem 32-Bit-Betriebssystem für Schlüssel [2] auszuführen.</String>
   <String Id="IDS_ERROR_2402">Nicht genügend Arbeitsspeicher vorhanden.</String>
   <String Id="IDS_ERROR_2501">Der Rollback-Skript-Enumerator konnte nicht erstellt werden.</String>
   <String Id="IDS_ERROR_2502">'InstallFinalize' wurde aufgerufen, obwohl keine Installation durchgeführt wird.</String>
   <String Id="IDS_ERROR_2503">'RunScript' wurde aufgerufen, obwohl kein Skript für die Ausführung markiert ist.</String>
   <String Id="IDS_ERROR_2601">Ungültiger Wert für Eigenschaft [2]: '[3]'</String>
   <String Id="IDS_ERROR_2602">Dem Eintrag '[3]' in der Tabelle [2] ist kein Eintrag in der Medientabelle zugeordnet.</String>
   <String Id="IDS_ERROR_2603">Doppelter Tabellenname [2].</String>
   <String Id="IDS_ERROR_2604">Die Eigenschaft [2] ist nicht definiert.</String>
   <String Id="IDS_ERROR_2605">Server [2] wurde weder in [3] noch in [4] gefunden.</String>
   <String Id="IDS_ERROR_2606">Der Wert der Eigenschaft [2] ist kein gültiger vollständiger Pfad: '[3]'.</String>
   <String Id="IDS_ERROR_2607">Die Medientabelle wurde nicht gefunden oder ist leer (erforderlich für die Installation von Dateien).</String>
   <String Id="IDS_ERROR_2608">Fehler beim Erstellen einer Sicherheitsbeschreibung für das Objekt. Fehler: '[2]'.</String>
   <String Id="IDS_ERROR_2609">Es wurde versucht, die Produkteinstellungen vor der Initialisierung zu migrieren.</String>
   <String Id="IDS_ERROR_2611">Die Datei [2] ist als komprimiert markiert, der zugeordnete Medieneintrag gibt jedoch keine CAB-Datei an.</String>
   <String Id="IDS_ERROR_2612">Der Stream wurde in Spalte [2] nicht gefunden. Primärschlüssel: '[3]'.</String>
   <String Id="IDS_ERROR_2613">RemoveExistingProducts-Aktion ist falsch sequenziert.</String>
   <String Id="IDS_ERROR_2614">Vom Installationspaket konnte nicht auf das IStorage-Objekt zugegriffen werden.</String>
   <String Id="IDS_ERROR_2615">Fehler beim Auflösen der Quelle: Das Aufheben der Registrierung von Modul [2] wurde übersprungen.</String>
   <String Id="IDS_ERROR_2616">Übergeordnete Datei der Begleitdatei [2] fehlt.</String>
   <String Id="IDS_ERROR_2617">Die gemeinsam genutzte Komponente [2] wurde nicht in der Komponententabelle gefunden.</String>
   <String Id="IDS_ERROR_2618">Die isolierte Anwendungskomponente [2] wurde in der Komponententabelle nicht gefunden.</String>
   <String Id="IDS_ERROR_2619">Die isolierten Komponenten [2] und [3] gehören nicht zur gleichen Funktion.</String>
   <String Id="IDS_ERROR_2620">Die Schlüsseldatei der isolierten Anwendungskomponente [2] befindet sich nicht in der Dateitabelle.</String>
   <String Id="IDS_ERROR_2621">Ressourcen-DLL- oder Ressourcen-ID-Informationen für Verknüpfung [2] wurden nicht ordnungsgemäß festgelegt.</String>
   <String Id="IDS_ERROR_2701">Die Tiefe einer Funktion übersteigt die zulässige Strukturtiefe von [2] Ebenen.</String>
   <String Id="IDS_ERROR_2702">Ein Tabellendatensatz der Funktion ([2]) referenziert ein nicht vorhandenes übergeordnetes Element im Attributfeld.</String>
   <String Id="IDS_ERROR_2703">Für den Stammquellpfad wurde kein Eigenschaftsname definiert: [2]</String>
   <String Id="IDS_ERROR_2704">Die Stammverzeichniseigenschaft ist nicht definiert: [2]</String>
   <String Id="IDS_ERROR_2705">Ungültige Tabelle: [2]. Verknüpfung als Struktur nicht möglich.</String>
   <String Id="IDS_ERROR_2706">Fehler beim Erstellen der Quellpfade. Es ist kein Pfad für den Eintrag [2] in der Verzeichnistabelle vorhanden.</String>
   <String Id="IDS_ERROR_2707">Fehler beim Erstellen der Zielpfade. Es ist kein Pfad für den Eintrag [2] in der Verzeichnistabelle vorhanden.</String>
   <String Id="IDS_ERROR_2708">In der Dateitabelle wurden keine Einträge gefunden.</String>
   <String Id="IDS_ERROR_2709">Der angegebene Komponentenname ('[2]') wurde in der Komponententabelle nicht gefunden.</String>
   <String Id="IDS_ERROR_2710">Der angeforderte Select-Status ist für diese Komponente unzulässig.</String>
   <String Id="IDS_ERROR_2711">Der angegebene Funktionsname ('[2]') wurde in der Funktionstabelle nicht gefunden.</String>
   <String Id="IDS_ERROR_2712">Ungültige Rückgabe von nicht modalem Dialogfeld: [3], in Aktion [2].</String>
   <String Id="IDS_ERROR_2713">NULL-Wert in Spalte, die keine NULL-Werte zulässt ('[2]' in Spalte '[3]' von Tabelle '[4]').</String>
   <String Id="IDS_ERROR_2714">Ungültiger Wert für Standardordnername: [2].</String>
   <String Id="IDS_ERROR_2715">Der angegebene Dateischlüssel ('[2]') wurde in der Dateitabelle nicht gefunden.</String>
   <String Id="IDS_ERROR_2716">Fehler beim Erstellen eines zufälligen Unterkomponentennamens für die Komponente '[2]'.</String>
   <String Id="IDS_ERROR_2717">Ungültige Aktionsbedingung oder Fehler beim Aufrufen der benutzerdefinierten Aktion '[2]'.</String>
   <String Id="IDS_ERROR_2718">Fehlender Paketname für den Produktcode '[2]'.</String>
   <String Id="IDS_ERROR_2719">In der Quelle '[2]' wurde weder ein UNC-Pfad noch ein Pfad mit Laufwerkbuchstaben gefunden.</String>
   <String Id="IDS_ERROR_2720">Fehler beim Öffnen des Quelllistenschlüssels. Fehler: '[2]'</String>
   <String Id="IDS_ERROR_2721">Die benutzerdefinierte Aktion [2] wurde nicht im binären Tabellenstream gefunden.</String>
   <String Id="IDS_ERROR_2722">Die benutzerdefinierte Aktion [2] wurde nicht in der Dateitabelle gefunden.</String>
   <String Id="IDS_ERROR_2723">In der benutzerdefinierten Aktion [2] wurde ein nicht unterstützter Typ angegeben.</String>
   <String Id="IDS_ERROR_2724">Die Volumebezeichnung '[2]' auf dem von Ihnen ausgeführten Medium stimmt nicht mit der Bezeichnung '[3]' der Medientabelle überein. Dies ist nur dann zulässig, wenn die Medientabelle nur einen einzigen Eintrag enthält.</String>
   <String Id="IDS_ERROR_2725">Ungültige Datenbanktabellen</String>
   <String Id="IDS_ERROR_2726">Aktion nicht gefunden: [2].</String>
   <String Id="IDS_ERROR_2727">Der Verzeichniseintrag '[2]' ist in der Verzeichnistabelle nicht vorhanden.</String>
   <String Id="IDS_ERROR_2728">Tabellendefinitionsfehler: [2]</String>
   <String Id="IDS_ERROR_2729">Das Installationsmodul wurde nicht initialisiert.</String>
   <String Id="IDS_ERROR_2730">Ungültiger Wert in der Datenbank. Tabelle: '[2]'; Primärer Schlüssel: '[3]'; Spalte: '[4]'</String>
   <String Id="IDS_ERROR_2731">Der Auswahl-Manager wurde nicht initialisiert.</String>
   <String Id="IDS_ERROR_2732">Der Verzeichnis-Manager wurde nicht initialisiert.</String>
   <String Id="IDS_ERROR_2733">Ungültiger Fremdschlüssel ('[2]') in der Spalte '[3]' der Tabelle '[4]'.</String>
   <String Id="IDS_ERROR_2734">Ungültiges Zeichen für den Neuinstallationsmodus.</String>
   <String Id="IDS_ERROR_2735">Die benutzerdefinierte Aktion '[2]' hat eine nicht behandelte Ausnahme verursacht und wurde beendet. Dies kann auf einen internen Fehler bei der benutzerdefinierten Aktion zurückzuführen sein, z.B. eine Zugriffsverletzung.</String>
   <String Id="IDS_ERROR_2736">Fehler beim Erzeugen einer temporären Datei für eine benutzerdefinierte Aktion: [2].</String>
   <String Id="IDS_ERROR_2737">Fehler beim Zugriff auf die benutzerdefinierte Aktion [2], Eintrag [3] , Bibliothek [4]</String>
   <String Id="IDS_ERROR_2738">Fehler beim Zugriff auf die VBScript-Laufzeit für die benutzerdefinierte Aktion [2].</String>
   <String Id="IDS_ERROR_2739">Fehler beim Zugriff auf die JavaScript-Laufzeit für die benutzerdefinierte Aktion [2].</String>
   <String Id="IDS_ERROR_2740">Benutzerdefinierte Aktion [2] Skriptfehler [3], [4]: [5] Zeile [6], Spalte [7], [8].</String>
   <String Id="IDS_ERROR_2741">Beschädigte Konfigurationsinformationen für Produkt [2]. Ungültige Informationen: [2].</String>
   <String Id="IDS_ERROR_2742">Fehler beim Marshalling zum Server: [2].</String>
   <String Id="IDS_ERROR_2743">Fehler beim Ausführen der benutzerdefinierten Aktion: [2], Speicherort: [3], Befehl: [4].</String>
   <String Id="IDS_ERROR_2744">Fehler beim Ausführen einer von der benutzerdefinierten Aktion [2] aufgerufenen EXE-Datei, Speicherort: [3], Befehl: [4].</String>
   <String Id="IDS_ERROR_2745">Die Transformation [2] ist für das Paket [3] ungültig. Erwartete Sprache [4], gefundene Sprache [5].</String>
   <String Id="IDS_ERROR_2746">Die Transformation [2] ist für das Paket [3] ungültig. Erwartetes Produkt [4], gefundenes Produkt [5].</String>
   <String Id="IDS_ERROR_2747">Die Transformation [2] ist für das Paket [3] ungültig. Erwartete Produktversion &lt; [4], gefundene Produktversion [5].</String>
   <String Id="IDS_ERROR_2748">Die Transformation [2] ist für das Paket [3] ungültig. Erwartete Produktversion &lt;= [4], gefundene Produktversion [5].</String>
   <String Id="IDS_ERROR_2749">Die Transformation [2] ist für das Paket [3] ungültig. Erwartete Produktversion == [4], gefundene Produktversion [5].</String>
   <String Id="IDS_ERROR_2750">Die Transformation [2] ist für das Paket [3] ungültig. Erwartete Produktversion &gt;= [4], gefundene Produktversion [5].</String>
   <String Id="IDS_ERROR_2751">Die Transformation [2] ist für das Paket [3] ungültig. Erwartete Produktversion &gt; [4], gefundene Produktversion [5].</String>
   <String Id="IDS_ERROR_2752">Fehler beim Öffnen der Transformation [2], die als untergeordnetes Speicherelement von Paket [4] gespeichert wurde.</String>
   <String Id="IDS_ERROR_2753">Die Datei '[2]' ist nicht für die Installation markiert.</String>
   <String Id="IDS_ERROR_2754">Die Datei '[2]' ist keine gültige Patch-Datei.</String>
   <String Id="IDS_ERROR_2755">Der Server hat beim Installieren des Pakets [3] den unerwarteten Fehler [2] zurückgegeben.</String>
   <String Id="IDS_ERROR_2756">Die Eigenschaft '[2]' wurde in mindestens einer Tabelle als Verzeichniseigenschaft verwendet, es wurde jedoch kein Wert zugewiesen.</String>
   <String Id="IDS_ERROR_2757">Fehler beim Erstellen der Zusammenfassungsinformationen für die Transformation [2].</String>
   <String Id="IDS_ERROR_2758">Die Transformation [2] enthält keine MSI-Version.</String>
   <String Id="IDS_ERROR_2759">Die Transformation [2], Version [3], ist mit dem Modul nicht kompatibel; Min.: [4], Max.: [5].</String>
   <String Id="IDS_ERROR_2760">Die Transformation [2] ist für das Paket [3] ungültig. Erwarteter Aktualisierungscode: [4], gefunden: [5].</String>
   <String Id="IDS_ERROR_2761">Fehler beim Starten der Transaktion. Globales Mutex wurde nicht ordnungsgemäß initialisiert.</String>
   <String Id="IDS_ERROR_2762">Fehler beim Schreiben des Skriptdatensatzes. Die Transaktion wurde nicht gestartet.</String>
   <String Id="IDS_ERROR_2763">Fehler beim Ausführen des Skripts. Die Transaktion wurde nicht gestartet.</String>
   <String Id="IDS_ERROR_2765">Fehlender Assemblyname in Tabelle 'AssemblyName': Komponente: [4].</String>
   <String Id="IDS_ERROR_2766">Die Datei [2] ist keine gültige MSI-Speicherdatei.</String>
   <String Id="IDS_ERROR_2767">Keine weiteren Daten {beim Auflisten von [2]}.</String>
   <String Id="IDS_ERROR_2768">Ungültige Transformation im Patch-Paket.</String>
   <String Id="IDS_ERROR_2769">Die benutzerdefinierte Aktion [2] hat [3] MSIHANDLEs nicht beendet.</String>
   <String Id="IDS_ERROR_2770">Der zwischengespeicherte Ordner [2] ist in der internen Cacheordnertabelle nicht definiert.</String>
   <String Id="IDS_ERROR_2771">Fehlende Komponente bei Upgrade von Funktion [2].</String>
   <String Id="IDS_ERROR_2772">Neue Upgrade-Funktion [2] muss eine Blattfunktion sein.</String>
   <String Id="IDS_ERROR_2801">Unbekannte Meldung - Typ [2]. Es wird keine Aktion ausgeführt.</String>
   <String Id="IDS_ERROR_2802">Für das Ereignis [2] wurde kein Herausgeber gefunden.</String>
   <String Id="IDS_ERROR_2803">In der Dialogfeldansicht wurde kein Datensatz für das Dialogfeld [2] gefunden.</String>
   <String Id="IDS_ERROR_2804">Fehler beim Auswerten der Bedingung [3] durch 'CMsiDialog' während der Aktivierung des Steuerelements [3] im Dialogfeld [2].</String>
   <String Id="IDS_ERROR_2806">Fehler beim Auswerten der Bedingung [3] durch Dialogfeld [2].</String>
   <String Id="IDS_ERROR_2807">Unbekannte Aktion: [2].</String>
   <String Id="IDS_ERROR_2808">Die Standardschaltfläche im Dialogfeld [2] wurde falsch definiert.</String>
   <String Id="IDS_ERROR_2809">Die Zeiger auf das nächste Steuerelement im Dialogfeld [2] bilden keine Schleife. Es ist lediglich ein Zeiger von [3] auf [4] vorhanden.</String>
   <String Id="IDS_ERROR_2810">Die Zeiger auf das nächste Steuerelement im Dialogfeld [2] bilden keine Schleife. [3] und [5] zeigen auf [4].</String>
   <String Id="IDS_ERROR_2811">Im Dialogfeld [2] muss der Fokus auf dem Steuerelement [3] liegen. Diese Festlegung ist jedoch nicht möglich.</String>
   <String Id="IDS_ERROR_2812">Unbekanntes Ereignis: [2].</String>
   <String Id="IDS_ERROR_2813">Das EndDialog-Ereignis wurde mit dem [2]-Argument aufgerufen, das Dialogfeld verfügt aber über ein übergeordnetes Dialogfeld.</String>
   <String Id="IDS_ERROR_2814">Im Dialogfeld [2] benennt das Steuerelement [3] ein nicht vorhandenes Steuerelement [4] als nächstes Steuerelement.</String>
   <String Id="IDS_ERROR_2815">Die Tabelle 'ControlCondition' enthält eine Zeile ohne Bedingung für das Dialogfeld [2].</String>
   <String Id="IDS_ERROR_2816">Die Tabelle 'EventMapping' verweist für das Ereignis [3] auf das ungültige Steuerelement [4] im Dialogfeld [2].</String>
   <String Id="IDS_ERROR_2817">Fehler beim Festlegen des Attributs durch das [2]-Ereignis für das Steuerelement [4] im Dialogfeld [3].</String>
   <String Id="IDS_ERROR_2818">'EndDialog' verfügt in der Tabelle 'ControlEvent' über das unbekannte Argument [2].</String>
   <String Id="IDS_ERROR_2819">Das Steuerelement [3] im Dialogfeld [2] muss mit einer Eigenschaft verknüpft werden.</String>
   <String Id="IDS_ERROR_2820">Es wurde versucht, einen bereits initialisierten Handler zu initialisieren.</String>
   <String Id="IDS_ERROR_2821">Es wurde versucht, ein bereits initialisiertes Dialogfeld zu initialisieren: [2].</String>
   <String Id="IDS_ERROR_2822">Es kann erst nach dem Hinzufügen aller Steuerelemente zum Dialogfeld [2] eine andere Methode aufgerufen werden.</String>
   <String Id="IDS_ERROR_2823">Es wurde versucht, ein bereits initialisiertes Steuerelement zu initialisieren: [3] im Dialogfeld [2].</String>
   <String Id="IDS_ERROR_2824">Das [3]-Attribut des Dialogfelds benötigt einen Datensatz mit mindestens [2] Feld(ern).</String>
   <String Id="IDS_ERROR_2825">Das [3]-Attribut des Steuerelements benötigt einen Datensatz mit mindestens [2] Feld(ern).</String>
   <String Id="IDS_ERROR_2826">Das Steuerelement [3] im Dialogfeld [2] reicht um [5] Pixel über den Rand des Dialogfelds [4] hinaus.</String>
   <String Id="IDS_ERROR_2827">Die Schaltfläche [4] in der Optionsfeldgruppe [3] im Dialogfeld [2] reicht um [6] Pixel über den Rand der Gruppe [5] hinaus.</String>
   <String Id="IDS_ERROR_2828">Es wurde versucht, das Steuerelement [3] aus dem Dialogfeld [2] zu entfernen. Das Steuerelement gehört jedoch nicht zu diesem Dialogfeld.</String>
   <String Id="IDS_ERROR_2829">Es wurde versucht, ein nicht initialisiertes Dialogfeld zu verwenden.</String>
   <String Id="IDS_ERROR_2830">Es wurde versucht, ein nicht initialisiertes Steuerelement im Dialogfeld [2] zu verwenden.</String>
   <String Id="IDS_ERROR_2831">Das Steuerelement [3] im Dialogfeld [2] unterstützt [5] nicht für das [4]-Attribut.</String>
   <String Id="IDS_ERROR_2832">Das Dialogfeld [2] unterstützt das [3]-Attribut nicht.</String>
   <String Id="IDS_ERROR_2833">Das Steuerelement [4] im Dialogfeld [3] hat die Meldung [2] ignoriert.</String>
   <String Id="IDS_ERROR_2834">Die nächsten Zeiger im Dialogfeld [2] bilden keine einzelne Schleife.</String>
   <String Id="IDS_ERROR_2835">Das Steuerelement [2] wurde im Dialogfeld [3] nicht gefunden.</String>
   <String Id="IDS_ERROR_2836">Der Fokus kann nicht auf das Steuerelement [3] im Dialogfeld [2] festgelegt werden.</String>
   <String Id="IDS_ERROR_2837">Für das Steuerelement [3] im Dialogfeld [2] soll 'winproc' [4] zurückgeben.</String>
   <String Id="IDS_ERROR_2838">Das Element [2] in der Auswahltabelle ist sich selbst übergeordnet.</String>
   <String Id="IDS_ERROR_2839">Fehler beim Festlegen der [2]-Eigenschaft.</String>
   <String Id="IDS_ERROR_2840">Namenskonflikt beim Fehlerdialogfeld.</String>
   <String Id="IDS_ERROR_2841">Im Fehlerdialogfeld wurde keine Schaltfläche 'OK' gefunden.</String>
   <String Id="IDS_ERROR_2842">Im Fehlerdialogfeld wurde kein Textfeld gefunden.</String>
   <String Id="IDS_ERROR_2843">Das ErrorString-Attribut wird bei Standarddialogfeldern nicht unterstützt.</String>
   <String Id="IDS_ERROR_2844">Ein Fehlerdialogfeld kann nur ausgeführt werden, wenn 'Errorstring' festgelegt wurde.</String>
   <String Id="IDS_ERROR_2845">Die Gesamtbreite der Schaltflächen übersteigt die Größe des Fehlerdialogfelds.</String>
   <String Id="IDS_ERROR_2846">'SetFocus' konnte das erforderliche Steuerelement im Fehlerdialogfeld nicht finden.</String>
   <String Id="IDS_ERROR_2847">Für das Steuerelement [3] im Dialogfeld [2] wurde sowohl das Symbol- als auch das Bitmapformat festgelegt.</String>
   <String Id="IDS_ERROR_2848">Es wurde versucht, das Steuerelement [3] als Standardschaltfläche im Dialogfeld [2] festzulegen, aber das Steuerelement ist nicht vorhanden.</String>
   <String Id="IDS_ERROR_2849">Für den Typ des Steuerelements [3] im Dialogfeld [2] ist kein ganzzahliger Wert zulässig.</String>
   <String Id="IDS_ERROR_2850">Unbekannter Volumetyp.</String>
   <String Id="IDS_ERROR_2851">Ungültige Daten für das Symbol [2].</String>
   <String Id="IDS_ERROR_2852">Dem Dialogfeld [2] muss mindestens ein Steuerelement hinzugefügt werden, bevor es verwendet werden kann.</String>
   <String Id="IDS_ERROR_2853">Das Dialogfeld [2] ist nicht modal. Daher sollte für dieses Dialogfeld nicht die Execute-Methode aufgerufen werden.</String>
   <String Id="IDS_ERROR_2854">Im Dialogfeld [2] wurde das Steuerelement [3] als erstes aktives Steuerelement festgelegt. Dieses Steuerelement ist jedoch nicht vorhanden.</String>
   <String Id="IDS_ERROR_2855">Die Optionsfeldgruppe [3] im Dialogfeld [2] enthält weniger als 2 Schaltflächen.</String>
   <String Id="IDS_ERROR_2856">Eine zweite Kopie des Dialogfelds [2] wird erstellt.</String>
   <String Id="IDS_ERROR_2857">Das Verzeichnis [2] ist in der Auswahltabelle aufgeführt, wurde aber nicht gefunden.</String>
   <String Id="IDS_ERROR_2858">Die Daten für Bitmap [2] sind ungültig.</String>
   <String Id="IDS_ERROR_2859">Testfehlermeldung.</String>
   <String Id="IDS_ERROR_2860">Die Schaltfläche 'Abbrechen' im Dialogfeld [2] wurde falsch definiert.</String>
   <String Id="IDS_ERROR_2861">Die Zeiger auf das nächste Optionsfeld im Dialogfeld [2] für das Steuerelement [3] bilden keine Schleife.</String>
   <String Id="IDS_ERROR_2862">Die Attribute des Steuerelements [3] im Dialogfeld [2] definieren keine gültige Symbolgröße. Die Größe wird auf 16 festgelegt.</String>
   <String Id="IDS_ERROR_2863">Das Steuerelement [3] im Dialogfeld [2] benötigt das Symbol [4] in der Größe [5] x [5], diese Größe ist aber nicht verfügbar. Die erste verfügbare Größe wird geladen.</String>
   <String Id="IDS_ERROR_2864">Das Steuerelement [3] im Dialogfeld [2] hat ein Browse-Ereignis empfangen, es ist aber kein konfigurierbares Verzeichnis für die aktuelle Auswahl vorhanden. Wahrscheinliche Ursache: Die Schaltfläche 'Durchsuchen' wurde falsch definiert.</String>
   <String Id="IDS_ERROR_2865">Das Steuerelement [3] im Billboard [2] reicht um [5] Pixel über den Rand des Billboards [4] hinaus.</String>
   <String Id="IDS_ERROR_2866">Das [3]-Argument ist kein zulässiger Rückgabewert von Dialogfeld [2].</String>
   <String Id="IDS_ERROR_2867">Die Fehlerdialogfeldeigenschaft wurde nicht festgelegt.</String>
   <String Id="IDS_ERROR_2868">Für das Fehlerdialogfeld [2] wurde kein Fehlerformatbit festgelegt.</String>
   <String Id="IDS_ERROR_2869">Für das Dialogfeld [2] wurde das Fehlerformatbit festgelegt, obwohl es kein Fehlerdialogfeld ist.</String>
   <String Id="IDS_ERROR_2870">Die Hilfezeichenfolge [4] für das Steuerelement [3] im Dialogfeld [2] enthält kein Trennzeichen.</String>
   <String Id="IDS_ERROR_2871">Die Tabelle [2] ist veraltet: [3].</String>
   <String Id="IDS_ERROR_2872">Ungültiges Argument des CheckPath-Steuerelementereignisses im Dialogfeld [2].</String>
   <String Id="IDS_ERROR_2873">Ungültige Längenbeschränkung für die Zeichenfolge des Steuerelements [3] im Dialogfeld [2] : [4].</String>
   <String Id="IDS_ERROR_2874">Fehler beim Ändern der Textschriftart in [2].</String>
   <String Id="IDS_ERROR_2875">Fehler beim Ändern der Textfarbe in [2].</String>
   <String Id="IDS_ERROR_2876">Das Steuerelement [3] im Dialogfeld [2] musste die Zeichenfolge [4] abschneiden.</String>
   <String Id="IDS_ERROR_2877">Die Binärdaten [2] wurden nicht gefunden</String>
   <String Id="IDS_ERROR_2878">Im Dialog [2] ist für das Steuerelement [3] ein Wert möglich: [4]. Dieser Wert ist ungültig oder doppelt.</String>
   <String Id="IDS_ERROR_2879">Das Steuerelement [3] im Dialogfeld [2] kann die Maskenzeichenfolge nicht analysieren: [4].</String>
   <String Id="IDS_ERROR_2880">Die verbleibenden Steuerelementereignisse werden nicht ausgeführt.</String>
   <String Id="IDS_ERROR_2881">Fehler beim Initialisieren von 'CMsiHandler'.</String>
   <String Id="IDS_ERROR_2882">Fehler beim Registrieren der Dialogfensterklasse.</String>
   <String Id="IDS_ERROR_2883">Fehler beim Ausführen von 'CreateNewDialog' für das Dialogfeld [2].</String>
   <String Id="IDS_ERROR_2884">Fehler beim Erstellen eines Fensters für das Dialogfeld [2].</String>
   <String Id="IDS_ERROR_2885">Fehler beim Erstellen des Steuerelements [3] im Dialogfeld [2].</String>
   <String Id="IDS_ERROR_2886">Fehler beim Erstellen der [2]-Tabelle.</String>
   <String Id="IDS_ERROR_2887">Fehler beim Erstellen eines Cursors für die [2]-Tabelle.</String>
   <String Id="IDS_ERROR_2888">Fehler beim Ausführen der [2]-Sicht.</String>
   <String Id="IDS_ERROR_2889">Fehler beim Erstellen des Fensters für das Steuerelement [3] im Dialogfeld [2].</String>
   <String Id="IDS_ERROR_2890">Handler-Fehler beim Erstellen eines initialisierten Dialogfelds.</String>
   <String Id="IDS_ERROR_2891">Fehler beim Löschen des Fensters für das Dialogfeld [2].</String>
   <String Id="IDS_ERROR_2892">Das Steuerelement [2] akzeptiert ausschließlich Ganzzahlen, [3] ist kein gültiger Ganzzahlwert.</String>
   <String Id="IDS_ERROR_2893">Für das Steuerelement [3] im Dialogfeld [2] sind Eigenschaftswerte von bis zu [5] Zeichen Länge zulässig. Der Wert [4] überschreitet diesen Grenzwert und wurde abgeschnitten.</String>
   <String Id="IDS_ERROR_2894">Fehler beim Laden von 'RICHED20.DLL'. 'GetLastError()' hat [2] zurückgegeben.</String>
   <String Id="IDS_ERROR_2895">Fehler beim Freigeben von 'RICHED20.DLL'. 'GetLastError()' hat [2] zurückgegeben.</String>
   <String Id="IDS_ERROR_2896">Fehler beim Ausführen von Aktion [2].</String>
   <String Id="IDS_ERROR_2897">Fehler beim Erstellen einer [2]-Schriftart auf diesem System.</String>
   <String Id="IDS_ERROR_2898">Für das [2]-Textformat wurde systemseitig der Zeichensatz [4] in der Schriftart [3] erstellt.</String>
   <String Id="IDS_ERROR_2899">Fehler beim Erstellen des Textstils [2]. 'GetLastError()' hat [3] zurückgegeben.</String>
   <String Id="IDS_ERROR_2901">Ungültiger Parameter für Vorgang [2]: Parameter [3].</String>
   <String Id="IDS_ERROR_2902">Der Vorgang [2] wurde außerhalb der Sequenz aufgerufen.</String>
   <String Id="IDS_ERROR_2903">Die Datei [2] fehlt.</String>
   <String Id="IDS_ERROR_2904">'BindImage' für Datei [2] konnte nicht ausgeführt werden.</String>
   <String Id="IDS_ERROR_2905">Fehler beim Lesen eines Datensatzes aus Skriptdatei [2].</String>
   <String Id="IDS_ERROR_2906">Fehlender Header in Skriptdatei [2].</String>
   <String Id="IDS_ERROR_2907">Fehler beim Erstellen einer sicheren Sicherheitsbeschreibung. Fehler: [2].</String>
   <String Id="IDS_ERROR_2908">Fehler beim Registrieren der Komponente [2].</String>
   <String Id="IDS_ERROR_2909">Fehler beim Aufheben der Registrierung für Komponente [2].</String>
   <String Id="IDS_ERROR_2910">Fehler beim Ermitteln der Sicherheits-ID für den Benutzer.</String>
   <String Id="IDS_ERROR_2911">Fehler beim Entfernen des Ordners [2].</String>
   <String Id="IDS_ERROR_2912">Das Entfernen der Datei [2] beim Neustart konnte nicht geplant werden.</String>
   <String Id="IDS_ERROR_2919">Für die komprimierte Datei [2] wurde keine CAB-Datei angegeben.</String>
   <String Id="IDS_ERROR_2920">Für die Datei [2] wurde kein Quellverzeichnis angegeben.</String>
   <String Id="IDS_ERROR_2924">Die Version von Skript [2] wird nicht unterstützt. Skriptversion: [3], minimal erforderliche Version: [4], maximal zulässige Version: [5].</String>
   <String Id="IDS_ERROR_2927">Ungültige ShellFolder-ID: [2].</String>
   <String Id="IDS_ERROR_2928">Die maximal zulässige Anzahl an Quellen wurde überschritten. Quelle '[2]' wird übersprungen.</String>
   <String Id="IDS_ERROR_2929">Fehler beim Ermitteln des Veröffentlichungsstamms. Fehler: [2].</String>
   <String Id="IDS_ERROR_2932">Fehler beim Erstellen der Datei [2] aus den Skriptdaten. Fehler: [3].</String>
   <String Id="IDS_ERROR_2933">Fehler beim Initialisieren des Rollback-Skripts [2].</String>
   <String Id="IDS_ERROR_2934">Fehler beim Sichern von Transformation [2]. Fehler [3].</String>
   <String Id="IDS_ERROR_2935">Fehler beim Aufheben der Sicherheit für die Transformation [2]. Fehler [3].</String>
   <String Id="IDS_ERROR_2936">Transformation [2] wurde nicht gefunden.</String>
   <String Id="IDS_ERROR_2937">Windows Installer kann einen Katalog für den Systemdateischutz nicht installieren. Katalog: [2], Fehler: [3].</String>
   <String Id="IDS_ERROR_2938">Windows Installer kann einen Katalog für den Systemdateischutz nicht aus dem Cache abrufen. Katalog: [2], Fehler: [3].</String>
   <String Id="IDS_ERROR_2939">Windows Installer kann einen Katalog für den Systemdateischutz nicht aus dem Cache löschen. Katalog: [2], Fehler: [3].</String>
   <String Id="IDS_ERROR_2940">Für die Auflösung der Quelle wurde kein Verzeichnis-Manager bereitgestellt.</String>
   <String Id="IDS_ERROR_2941">Fehler bei der CRC-Berechnung für Datei [2].</String>
   <String Id="IDS_ERROR_2942">'BindImage' wurde für Datei [2] nicht ausgeführt.</String>
   <String Id="IDS_ERROR_2943">Diese Version von Windows bietet keine Unterstützung für die Bereitstellung von 64-Bit-Paketen. Das Skript [2] ist für ein 64-Bit-Paket vorgesehen.</String>
   <String Id="IDS_ERROR_2944">Fehler bei 'GetProductAssignmentType'.</String>
   <String Id="IDS_ERROR_2945">Fehler [3] beim Installieren der ComPlus-Anwendung [2].</String>
   <String Id="IDS_ERROR_3001">Die Patches in dieser Liste enthalten falsche Sequenzierungsinformationen: [2][3][4][5][6][7][8][9][10][11][12][13][14][15][16].</String>
   <String Id="IDS_ERROR_3002">Patch [2] enthält ungültige Sequenzierungsinformationen. </String>
   <String Id="IDS_ERROR_25032">Der LSI-Treiber konnte nicht installiert werden.</String>
   <String Id="IDS_ERROR_25520">Es konnte keine Sicherheitsbeschreibung für [3]\[4] erstellt werden, Systemfehler: [2]</String>
   <String Id="IDS_ERROR_25521">Es konnte keine Sicherheitsbeschreibung für Objekt [3] erstellt werden, Systemfehler: [2]</String>
   <String Id="IDS_ERROR_25522">Unbekannter Objekttyp [3], Systemfehler: [2]</String>
   <String Id="IDS_ERROR_27500">Für dieses Setup ist Internet Information Server 4.0 oder höher erforderlich, um virtuelle IIS-Stammverzeichnisse zu konfigurieren. Stellen Sie sicher, dass Sie über IIS 4.0 oder höher verfügen.</String>
   <String Id="IDS_ERROR_27501">Für dieses Setup müssen Sie über Administratorrechte verfügen, um virtuelle IIS-Stammverzeichnisse zu konfigurieren.</String>
   <String Id="IDS_ERROR_27502">Fehler beim Herstellen der Verbindung mit [2] '[3]'. [4]</String>
   <String Id="IDS_ERROR_27503">Fehler beim Abrufen der Versionszeichenfolge aus [2] '[3]'. [4]</String>
   <String Id="IDS_ERROR_27504">SQL-Versionsanforderungen nicht erfüllt: [3]. Diese Installation erfordert [2] [4] oder höher.</String>
   <String Id="IDS_ERROR_27505">SQL-Skriptdatei [2] konnte nicht geöffnet werden.</String>
   <String Id="IDS_ERROR_27506">Fehler beim Ausführen von SQL-Skript [2]. Zeile [3]. [4]</String>
   <String Id="IDS_ERROR_27507">Die Verbindungsherstellung mit oder die Suche nach Datenbankservern erfordert die Installation von MDAC.</String>
   <String Id="IDS_ERROR_27508">Fehler beim Installieren von COM+-Anwendung [2]. [3]</String>
   <String Id="IDS_ERROR_27509">Fehler beim Deinstallieren von COM+-Anwendung [2]. [3]</String>
   <String Id="IDS_ERROR_27510">Fehler beim Installieren von COM+-Anwendung [2]. Microsoft(R) .NET-Klassenbibliotheken konnten nicht geladen werden. Zur Registrierung der .NET-Dienstkomponenten muss das Microsoft (R) .NET Framework installiert sein.</String>
   <String Id="IDS_ERROR_27511">SQL-Skriptdatei [2] konnte nicht ausgeführt werden. Verbindung nicht geöffnet: [3]</String>
   <String Id="IDS_ERROR_27512">Fehler beim Starten von Transaktionen für [2] '[3]'. Datenbank [4]. [5]</String>
   <String Id="IDS_ERROR_27513">Fehler beim Übergeben der Transaktionen für [2] '[3]'. Datenbank [4]. [5]</String>
   <String Id="IDS_ERROR_27514">Für diese Installation ist Microsoft SQL Server erforderlich. Der angegebene Server '[3]' ist eine Microsoft SQL Server Desktop Engine (MSDE).</String>
   <String Id="IDS_ERROR_27515">Fehler beim Abrufen der Schemaversion aus [2] '[3]'. Datenbank: '[4]'. [5]</String>
   <String Id="IDS_ERROR_27516">Fehler beim Schreiben der Schemaversion in [2] '[3]'. Datenbank: '[4]'. [5]</String>
   <String Id="IDS_ERROR_27517">Diese Installation erfordert Administratorrechte zum Installieren von COM+-Anwendungen. Melden Sie sich als Administrator an und wiederholen Sie den Vorgang.</String>
   <String Id="IDS_ERROR_27518">Die COM+-Anwendung [2] ist so konfiguriert, dass sie als NT-Dienst ausgeführt wird; dazu ist COM+ 1.5 oder höher auf dem System erforderlich. Da Ihr System COM+ 1.0 verwendet, wird diese Anwendung nicht installiert.</String>
   <String Id="IDS_ERROR_27519">Fehler beim Aktualisieren der XML-Datei [2]. [3]</String>
   <String Id="IDS_ERROR_27520">Fehler beim Öffnen der XML-Datei [2]. [3]</String>
   <String Id="IDS_ERROR_27521">Diese Installation erfordert für die Konfiguration von XML-Dateien MSXML 3.0 oder höher. Stellen Sie sicher, dass Sie über die Version 3.0 oder höher verfügen.</String>
   <String Id="IDS_ERROR_27522">Fehler beim Erstellen der XML-Datei [2]. [3]</String>
   <String Id="IDS_ERROR_27523">Fehler beim Laden der Server.</String>
   <String Id="IDS_ERROR_27524">Fehler beim Laden von NetApi32.DLL. Die ISNetApi.DLL erfordert, dass die NetApi32.DLL ordnungsgemäß geladen wurde und ein NT-basiertes Betriebssystem verwendet wird.</String>
   <String Id="IDS_ERROR_27525">Server wurde nicht gefunden. Überprüfen Sie, ob der angegebene Server vorhanden ist. Der Servername darf nicht leer sein.</String>
   <String Id="IDS_ERROR_27526">Unbekannter Fehler von ISNetApi.dll.</String>
   <String Id="IDS_ERROR_27527">Der Puffer ist zu klein.</String>
   <String Id="IDS_ERROR_27528">Zugriff verweigert. Überprüfen Sie die Administratorrechte.</String>
   <String Id="IDS_ERROR_27529">Ungültiger Computer.</String>
   <String Id="IDS_ERROR_27530">Nicht definierte switch-case-Anweisung.</String>
   <String Id="IDS_ERROR_27531">Nicht behandelte Ausnahme.</String>
   <String Id="IDS_ERROR_27532">Ungültiger Benutzername für diesen Server oder diese Domäne.</String>
   <String Id="IDS_ERROR_27533">Die Kennwörter (Groß- und Kleinschreibung muss beachtet werden) stimmen nicht überein.</String>
   <String Id="IDS_ERROR_27534">Die Liste ist leer.</String>
   <String Id="IDS_ERROR_27535">Zugriffsverletzung.</String>
   <String Id="IDS_ERROR_27536">Fehler beim Abrufen der Gruppe.</String>
   <String Id="IDS_ERROR_27537">Fehler beim Hinzufügen eines Benutzers zur Gruppe. Stellen Sie sicher, dass die Gruppe für diese Domäne oder diesen Server vorhanden ist.</String>
   <String Id="IDS_ERROR_27538">Fehler bei Benutzererstellung.</String>
   <String Id="IDS_ERROR_27539">ERROR_NETAPI_ERROR_NOT_PRIMARY von NetAPI zurückgegeben.</String>
   <String Id="IDS_ERROR_27540">Der angegebene Benutzer ist bereits vorhanden.</String>
   <String Id="IDS_ERROR_27541">Die angegebene Gruppe ist bereits vorhanden.</String>
   <String Id="IDS_ERROR_27542">Ungültiges Kennwort. Stellen Sie sicher, dass das Kennwort den Kennwortrichtlinien für Ihr Netzwerk entspricht.</String>
   <String Id="IDS_ERROR_27543">Ungültiger Name.</String>
   <String Id="IDS_ERROR_27544">Ungültige Gruppe.</String>
   <String Id="IDS_ERROR_27545">Der Benutzername darf nicht leer sein und muss folgendes Format aufweisen: DOMAENE\Benutzername.</String>
   <String Id="IDS_ERROR_27546">Fehler beim Laden oder Erstellen der INI-Datei im TEMP-Verzeichnis des Benutzers.</String>
   <String Id="IDS_ERROR_27547">Die Datei ISNetAPI.dll wurde nicht geladen, oder es ist ein Fehler beim Laden der DLL-Datei aufgetreten. Die DLL-Datei muss für diesen Vorgang geladen werden. Stellen Sie sicher, dass sich die DLL-Datei im SUPPORTDIR-Verzeichnis befindet.</String>
   <String Id="IDS_ERROR_27548">Fehler beim Löschen der INI-Datei mit neuen Benutzerinformationen aus dem TEMP-Verzeichnis des Benutzers.</String>
   <String Id="IDS_ERROR_27549">Fehler beim Abrufen des primären Domänencontrollers (PDC).</String>
   <String Id="IDS_ERROR_27550">Jedes Feld muss einen Wert enthalten, damit ein Benutzer erstellt werden kann.</String>
   <String Id="IDS_ERROR_27551">ODBC-Treiber für [2] wurde nicht gefunden. Dies ist für die Verbindung mit [2]-Datenbankservern erforderlich.</String>
   <String Id="IDS_ERROR_27552">Fehler beim Erstellen der Datenbank [4]. Server: [2] [3]. [5]</String>
   <String Id="IDS_ERROR_27553">Fehler bei der Verbindungsherstellung mit Datenbank [4]. Server: [2] [3]. [5]</String>
   <String Id="IDS_ERROR_27554">Fehler beim Öffnen der Verbindung [2]. Mit dieser Verbindung sind keine gültigen Datenbankmetadaten verknüpft.</String>
   <String Id="IDS_ERROR_28030">Das Installationsprogramm konnte den USB-Treiber nicht installieren. Um eine erfolgreiche Installation zu gewährleisten, führen Sie einen Neustart des Computers aus und starten Sie das Installationsprogramm neu.</String>
   <String Id="IDS_ERROR_28033">Verbindung mit %s nicht möglich. TCP-Verbindungsfehler auf TCP-Port %d. Überprüfen Sie den eingegebenen Servernamen.</String>
   <String Id="IDS_ERROR_28034">Verbindung mit LDAP auf %s nicht möglich. Überprüfen Sie den eingegebenen Servernamen.</String>
   <String Id="IDS_ERROR_28035">Ungültige Anmeldeinformationen. Geben Sie einen Administratorbenutzernamen sowie das zugehörige Kennwort für den angegebenen Server ein.</String>
   <String Id="IDS_ERROR_28036">Fehler bei Bindung an LDAP auf %s. %s.</String>
   <String Id="IDS_ERROR_28037">Fehler beim Aktualisieren von LDAP auf %s. Zugriff verweigert. Geben Sie einen Administratorbenutzernamen sowie das zugehörige Kennwort mit ausreichenden Berechtigungen auf dem angegebenen Server an.</String>
   <String Id="IDS_ERROR_28038">Fehler beim Aktualisieren von LDAP auf %s. Schemaverletzung. Es ist möglich, dass der angegebene Server eine alte Softwareversion ausführt und aktualisiert werden muss, um diese Agenten-Installation zu unterstützen.</String>
   <String Id="IDS_ERROR_28039">Fehler beim Aktualisieren von LDAP auf %s. %s.</String>
   <String Id="IDS_ERROR_28040">Benutzername weist ungültiges Format auf.</String>
   <String Id="IDS_ERROR_28041">Dieser Agent ist bereits auf %s registriert. Möchten Sie diese Installation fortsetzen und die vorhandenen Registrierungsinformationen aktualisieren?</String>
   <String Id="IDS_ERROR_28042">Dieser Agent ist mehrfach auf %s registriert. Entfernen Sie mit dem Horizon Administrator die Einträge für diesen Computer und starten Sie die Installation neu.</String>
   <String Id="IDS_ERROR_28045">Bei der Eingabe von Anmeldeinformationen für einen Administrator müssen Sie einen Benutzernamen sowie ein Kennwort angeben.</String>
   <String Id="IDS_ERROR_28046">Fehler beim Abrufen von kritischen Sicherheitsinformationen von LDAP auf %s. Es ist möglich, dass der angegebene Server eine alte Softwareversion ausführt und aktualisiert werden muss, um diese Agenten-Installation zu unterstützen.</String>
   <String Id="IDS_ERROR_28053">Eine DLL konnte nicht registriert werden. Einzelheiten finden Sie in der letzten %TEMP%\vminst*.log-Datei.</String>
   <String Id="IDS_ERROR_28060">Fehler beim Installieren des Intel HECI-Gerätetreibers.</String>
   <String Id="IDS_ERROR_28062">Ein Agent für diesen Computer ist bereits auf '%s' als virtuelle Maschine registriert. Ändern Sie entweder den Computernamen für diesen Computer, oder entfernen Sie den VM-Eintrag mit Horizon Administrator auf dem Verbindungsserver. Starten Sie anschließend die Installation erneut.</String>
   <String Id="IDS_ERROR_28065">Das Installationsprogramm konnte den Smartcard-Redirector-Treiber nicht installieren.</String>
   <String Id="IDS_ERROR_28089">Der Verbindungsserver %s führt keine ausreichende Softwareversion aus, um diesen Agent vollständig zu unterstützen. Sie sollten Ihre Verbindungsserver aktualisieren, bevor Sie fortfahren. Wenn Sie den Vorgang dennoch fortsetzen, müssen Sie diesen Agent später erneut installieren, um alle RDS-Host-Funktionen vollständig nutzen zu können. Möchten Sie diese Installation fortsetzen?</String>
   <String Id="IDS_ERROR_28090">Setup hat nicht standardmäßige Registrierungswerte erkannt:

HKLM\SYSTEM\CurrentControlSet\Control\TerminalServer\WinStations\RDP-Tcp\MaxInstanceCount

HKLM\SYSTEM\CurrentControlSet\Control\TerminalServer\WinStations\Horizon-PCOIP\MaxInstanceCount

Da Horizon Sitzungslimits verwaltet, verursachen diese Registrierungseinstellungen ein unerwartetes Verhalten.</String>
   <String Id="IDS_ERROR_28092">Das Installationsprogramm konnte den Horizon Virtual Webcam-Treiber nicht installieren. Um eine erfolgreiche Installation zu gewährleisten, starten Sie den Computer und das Installationsprogramm neu.</String>
   <String Id="IDS_ERROR_28096">Im Dokument zur Horizon-Sicherheit finden Sie Erläuterungen zur sicheren Verwendung der USB-Umleitung.</String>
   <String Id="IDS_ERROR_28100">Sie sind im Begriff, Omnissa Horizon Agent zu installieren, obwohl einige Überprüfungen des Installationsprogramms deaktiviert sind. Dies wird nicht empfohlen und führt zu einer Installation, die nicht unterstützt wird. Brechen Sie den Vorgang ab und führen Sie das Installationsprogramm erneut mit aktivierten Überprüfungen aus.</String>
   <String Id="IDS_ERROR_28109">VC%d.%d nonredist ist nicht auf dem Computer installiert. Die Installation von Horizon Agent schlägt möglicherweise fehl, wenn Sie fortfahren. Sie können nonredist-Installationsprogramme für VC%d.%d vom Conan-Build msvc_debug_runtime_installer herunterladen.</String>
   <String Id="IDS_ERROR_28110">Für das Virtualization Pack für Skype for Business muss .NET Framework 4.0 oder höher vor der Installation dieser Funktion vorhanden sein. Installieren Sie .NET Framework und starten Sie das Installationsprogramm erneut, um diese Funktion zu installieren.</String>
   <String Id="IDS_ERROR_28111">Fügen Sie eine Funktion hinzu oder entfernen Sie diese, um fortzufahren.</String>
   <String Id="IDS_ERROR_28113">Während einer Änderungsinstallation kann die Funktion „Instant Clone Agent“ (NGVC) nicht geändert werden. Wenn Sie eine Klonfunktion hinzufügen oder entfernen möchten, deinstallieren Sie den Agent und installieren Sie ihn erneut.</String>
   <String Id="IDS_ERROR_28114">Sie benötigen Administratorrechte, um diese Installation zu bearbeiten oder zu reparieren. Sie können auch den folgenden Befehl über eine Eingabeaufforderung mit erhöhten Rechten ausführen:

msiexec.exe /i [DATABASE]</String>
   <String Id="IDS_ERROR_28115">Sie müssen über Administratorberechtigungen verfügen, um diesen Patch zu installieren. Möglicherweise müssen Sie den folgenden Befehl von einer erweiterten Eingabeaufforderung aus ausführen:

msiexec.exe /p [PATCH]</String>

   <String Id="IDS_ERROR_28116">Druckwarteschlangendienst wird nicht ausgeführt. Die Funktion „Horizon Integrated Printing“ ist unter Umständen nicht installiert.</String>

   <!-- L10n properties for merge module services -->
   <String Id="IDS_PCOIPSG_DISPLAY_NAME">Omnissa Horizon PCoIP-Sicherheits-Gateway</String>
   <String Id="IDS_PCOIPSG_DESCRIPTION">Stellt  PCoIP-Gateway-Dienste bereit.</String>

   <String Id="IDS_WSNM_SERVICE_DISPLAY_NAME">Omnissa Horizon Agent</String>
   <String Id="WsnmServiceDescription">Stellt Horizon Agent-Dienste bereit.</String>

   <String Id="IDS_WSSH_SERVICE_DISPLAY_NAME">Omnissa Horizon-Skripthost</String>
   <String Id="IDS_WSSH_SERVICE_DESCRIPTION">Stellt Skripthostdienste bereit.</String>

   <String Id="IDS_VMLM_SERVICE_DISPLAY_NAME">Omnissa Horizon-Anmeldeüberwachung</String>
   <String Id="IDS_VMLM_SERVICE_DESCRIPTION">Stellt Dienste für die Anmeldeüberwachung bereit.</String>

   <String Id="IDS_HZMON_SERVICE_DISPLAY_NAME">Omnissa Horizon Monitoring Service</String>
   <String Id="IDS_HZMON_SERVICE_DESCRIPTION">Stellt Überwachungsdienste bereit.</String>

   <String Id="IDS_VMWRXG_SERVICE_DISPLAY_NAME">Omnissa Horizon Remote Experience-Dienst</String>
   <String Id="IDS_VMWRXG_SERVICE_DESCRIPTION">Stellt generische Remote Experience-Dienste bereit.</String>

   <String Id="IDS_AUTORESTART">System bei erfolgreichem Abschluss automatisch neu starten</String>

   <String Id="IDS_AUTORESTART_MODIFY">System bei erfolgreichem Abschluss bei Bedarf automatisch neu starten</String>

</WixLocalization>
