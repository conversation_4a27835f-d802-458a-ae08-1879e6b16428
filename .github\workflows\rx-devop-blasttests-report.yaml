name: RX-Devops-FlakyTests-Report

on: 
  workflow_dispatch:
  schedule:
    - cron: '0 3 * * 1'
# Runs only one build for a branch at a time. Prevent simultaneous runs.
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true       
jobs:
  Disclaimer:
    runs-on: [lnxbuild-gh, self-hosted]
    steps:
      - name: Step summary
        run: >
          printf "# DISCLAIMER\n
          'Flaky-Job-Report' lists the jobs in the repo that have the most
          flaky runs, AKA where there is one attempt failing and one attempt
          passing on the same change. The data comes from the past week of
          runs.\n" >> $GITHUB_STEP_SUMMARY
        shell: bash

#   Failing-Test-Report:
#     env:
#       NATIVE_TEST_DOCKER_IMG: "http://artifactory.air-watch.com/asdk-local-aw/test-analysis-workbench:6.0"
#     permissions:
#       contents: read
#       actions: read
#       checks: write
#     runs-on: [ self-hosted, lnxbuild-gh ]
#     steps: 
#       - name: Checkout Code
#         uses: actions/checkout@v4
#       - name: Create Directory
#         run: |
#           mkdir output
#           chmod 777 output
#       - name: Build Debian package
#         run: >
#           docker run --rm -u "$(id -u):$(id -g)"
#           -e TEST_RESULTS_REPOSITORY="${{ secrets.VDUB_REPORT_DB_R }}"
#           -e TEST_DB_NAME="${{ secrets.VDUB_REPORT_DB_NAME }}"
#           -e TEST_COLLECTION_NAME="${{ secrets.VDUB_REPORT_COL_NAME }}"
#           -e HORIZON_MATRIX=True
#           artifactory.air-watch.com/asdk-local-aw/test-analysis-workbench:6.0
#           /app/default_runner.py /app/FlakyTestsAnalysis.py
#           $NATIVE_TEST_DOCKER_IMG > output/failed_test_report.txt
# 
#       - name: Parse Table and Add to Job Summary
#         run: |
#           echo "Failed Tests Report" >> $GITHUB_STEP_SUMMARY
#           echo "" >> $GITHUB_STEP_SUMMARY
#           cat output/failed_test_report.txt >> $GITHUB_STEP_SUMMARY
#       - name: Print Results
#         run: cat output/failed_test_report.txt

  Flaky-Job-Report:
    permissions:
      actions: read
      checks: read
      contents: read
    runs-on: [lnxbuild-gh, self-hosted]
    steps:
      - name: Publish flaky job report
        uses: euc-eng/flaky-job-reporter@v2
        with:
          workflowNames: >
            appblastlibs,hcandroid,hcios,hclin,hcmac,hcwin,horizonagent,
            horizoncommon,horizonlinuxagent,horizonosot
          ignoreJobs: '*-overall-status'
