/*
 * Copyright (c) Omnissa, LLC. All rights reserved.
 * This product is protected by copyright and intellectual property laws in the
 * United States and other countries as well as by international treaties.
 * -- Omnissa Restricted
 */

/*---------------------------------------------------------------------------*/
/** \file corethread.cpp
 *
 *  coreref threading related support
 *
 *  Author:     <PERSON>
 */
/*---------------------------------------------------------------------------*/

#include <typeinfo>


#include <coreref.h>
#include <horizonPaths.h>
using namespace CORE;


/***********
 ***********
 *********** Firstly the parts used also in dll mode local library
 ***********
 ***********/


/*
 *-----------------------------------------------------------------------------
 *
 * ****************************
 * coresynctimer
 * ****************************
 *
 *-----------------------------------------------------------------------------
 */

/* instanciated later in coretimer */
extern bool g_timerCritsecOk;

/*
 * the global criticical section used by the timer service (and corerunnable),
 * need precaution for use before construction / after deconstruction
 */
class CORE_TYPE coretimer_critsec : public corecritsec {
public:
   coretimer_critsec() : corecritsec(0) { g_timerCritsecOk = true; }

   ~coretimer_critsec() { g_timerCritsecOk = false; }

   /* lock the timer */
   void lock()
   {
      if (g_timerCritsecOk) {
         corecritsec::lock();
      }
   }

   /* unlock the timer */
   void unlock()
   {
      if (g_timerCritsecOk) {
         corecritsec::unlock();
      }
   }
};
extern coretimer_critsec CORE_TYPE g_timersync;

class CORE_TYPE coresynctimer {
public:
   coresynctimer() { g_timersync.lock(); }

   virtual ~coresynctimer() { g_timersync.unlock(); }
};

/*
 *-----------------------------------------------------------------------------
 *
 * ****************************
 * corerunnable
 * ****************************
 *
 *-----------------------------------------------------------------------------
 */

bool CORE::corerunnable_allThreads_constructed = false;
corerunnable_allThreadsGroup CORE::corerunnable_allThreads;

/*
 * We may be called to add a callback during construction of static objects
 * therefore we need to allocate this vector
 */
static std::vector<corerunnable_waitForAllThreadsCB> *corerunnable_waitForAllThreadsVector = NULL;

static bool corerunnable_waitDone = false;


/*
 *-----------------------------------------------------------------------------
 *
 * corerunnable::waitForAllThreads --
 *
 * Results:
 *    returns remaining threads after timeout
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

DWORD
corerunnable::waitForAllThreads(DWORD timeout)
{
   if (isInStaticDeconstruction) {
      return 0;
   }

   {
      coresynctimer sync;
      corerunnable_waitDone = true;
   }

   if (corerunnable_waitForAllThreadsVector) {
      std::vector<corerunnable_waitForAllThreadsCB>::iterator it;
      while ((it = corerunnable_waitForAllThreadsVector->begin()) !=
             corerunnable_waitForAllThreadsVector->end()) {
         (*it)();
         corerunnable_waitForAllThreadsVector->erase(it);
      }
      delete corerunnable_waitForAllThreadsVector;
      corerunnable_waitForAllThreadsVector = NULL;
   }

   corerunnable_allThreads.wait(timeout);
   Sleep(25); // see comment in corethreadgroup::wait
   /*
    * Reset the wait done flag.
    */
   {
      coresynctimer sync;
      corerunnable_waitDone = false;
   }
   return (DWORD)corerunnable_allThreads.size();
}

/*
 *-----------------------------------------------------------------------------
 *
 * corerunnable::waitForAllThreads_Notify --
 *
 *    Call all registered framework exit callbacks
 *
 * Results:
 *    true if successfull
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

bool
corerunnable::waitForAllThreads_Notify(corerunnable_waitForAllThreadsCB cb)
{
   g_timersync.lock();

   if (corerunnable_waitDone) {
      g_timersync.unlock();
      return false;
   }

   if (!corerunnable_waitForAllThreadsVector) {
      corerunnable_waitForAllThreadsVector = new std::vector<corerunnable_waitForAllThreadsCB>;
   }

   corerunnable_waitForAllThreadsVector->push_back(cb);

   g_timersync.unlock();
   return true;
}

#ifdef USE_MFW_NDC
extern "C" void CORE_TYPE NDC_Cleanup();
#endif

/*
 *-----------------------------------------------------------------------------
 *
 * ThreadNamesEnabled --
 *
 *    Check if the thread name feature is disabled by registry,
 *    default is enabled.
 *
 * Results:
 *    true if thread name feature is enabled else false
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

#ifdef WIN32
static bool
ThreadNamesEnabled()
{
   static bool threadNamesOk = false;
   static bool initDone = false;
   if (!initDone) {
      static long lock = -1;
      if (InterlockedIncrement(&lock) == 0) {
         threadNamesOk =
            mstr::readRegistry("HKLM\\" HORIZON_VDM_REG_ROOT_A "\\mfwUseThreadNames", "1").toBool();
         initDone = true;
      }
      while (!initDone) {
         Sleep(10);
      }
   }
   return threadNamesOk;
}
#endif

/*
 *-----------------------------------------------------------------------------
 *
 * win32ThreadNamePtr --
 *
 *    get the pointer to the Thread Environment Block UserAvailable field
 *
 * Results:
 *    pointer to thread name field
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

#ifdef WIN32

static char **
win32ThreadNamePtr()
{
#   ifdef WIN64
   /*
    * Fetch TIB using documented API on AMD64 and ARM64.
    * This code access _TEB->NT_TIB->ArbitraryUserPointer.
    */
   BYTE *pTIB = (BYTE *)NtCurrentTeb();
   char **pname = (char **)(pTIB + 0x28);
#   else

   /*
    * The 32 bit Thread Information Block offset 0x14 is defined as UserAvailable
    * TIB linear address from fs offset 0x18
    */
   BYTE *pTIB;
   __asm {
      mov EAX, FS :[18h]
      mov[pTIB], EAX
   }
   char **pname = (char **)(pTIB + 0x14);

#   endif // WIN64

   return pname;
}
#endif // WIN32

/*
 *-----------------------------------------------------------------------------
 *
 * corerunnable::setThreadName --
 *
 *    Sets the thread name for Debugger and DumpView
 *    NOTE: always call with name=NULL at thread exit to free the memory
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

void
corerunnable::setThreadName(const char *name)
{
#ifdef USE_MFW_NDC
   if (!name) {
#   ifdef MESSAGE_FRAMEWORK_DLL
      static bool tested = false;
      static void (*pNDC_Cleanup)() = 0;
      static long lock = -1;
      if (!tested) {
         /*
          * Use InterlockedIncrement instead of a critical section lock here
          * to avoid deadlocking with the SystemLock used in
          * GetModuleHandle/GetProcAddress
          */
         if (InterlockedIncrement(&lock) == 0) {
            pNDC_Cleanup = (void (*)())GetProcAddress(GetModuleHandle(TEXT("MessageFrameWork.dll")),
                                                      "NDC_Cleanup");
            tested = true;
         } else {
            while (!tested) {
               Sleep(10);
            }
         }
      }
      if (pNDC_Cleanup) {
         pNDC_Cleanup();
      }
#   else
      NDC_Cleanup();
#   endif // MESSAGE_FRAMEWORK_DLL
   }
#endif // USE_MFW_NDC

#ifdef WIN32

   if (ThreadNamesEnabled()) {
      char **pname = win32ThreadNamePtr();
      if (*pname) {
         free(*pname);
         *pname = 0;
      }

      if (name) {
         size_t s = min(strlen(name) + 1, 255);
         *pname = (char *)malloc(s + 6);
         memcpy(*pname, "name=", 5);
         *((*pname) + 5) = (char)s;
         memcpy((*pname) + 6, name, s - 1);
         *((*pname) + s + 5) = 0;
      }
   }

#   if defined(_DEBUG)
   if (name) {

      /* This sets the thread name for the Microsoft Visual Workbench */
      struct {
         DWORD dwType;     /* must be 0x1000 */
         LPCSTR szName;    /* pointer to name (in user addr space) */
         DWORD dwThreadID; /* thread ID (-1=caller thread) */
         DWORD dwFlags;    /* reserved for future use, must be zero */
      } info;
      info.dwType = 0x1000;
      info.szName = name;
      info.dwThreadID = (DWORD)-1;
      info.dwFlags = 0;
      __try {
         RaiseException(0x406D1388, 0, sizeof(info) / sizeof(ULONG_PTR), (ULONG_PTR *)&info);
      } __except (EXCEPTION_CONTINUE_EXECUTION) {
      }
   }

#   endif // defined(_DEBUG)
#endif    // WIN32
}

/*
 *-----------------------------------------------------------------------------
 *
 * corerunnable::setThreadNameStatic --
 *
 *    Sets the thread name, in static memory, for Debugger and DumpView
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

void
corerunnable::setThreadNameStatic(const char *name)
{
#ifdef WIN32
   if (ThreadNamesEnabled()) {
      char **pname = win32ThreadNamePtr();
      *pname = (char *)name;
   }
#endif // WIN32
}


/*
 *-----------------------------------------------------------------------------
 *
 * corerunnable::getThreadName --
 *
 * Results:
 *    Stored thread name (empty string if not set)
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

mstr
corerunnable::getThreadName()
{
#ifdef WIN32
   if (ThreadNamesEnabled()) {
      char **pname = win32ThreadNamePtr();
      if (*pname) {
         return mstr((*pname) + 6);
      }
   }
#endif // WIN32
   return mstr::printf("%u", GetCurrentThreadId());
}


/***********
 ***********
 *********** Secondly the parts not used in dll mode local library
 ***********
 ***********/


#ifndef MESSAGE_FRAMEWORK_DLL_LOCAL

/*
 *-----------------------------------------------------------------------------
 *
 * ****************************
 * Nested Diagnostic Contexts
 * ****************************
 *
 *-----------------------------------------------------------------------------
 */

#   ifdef USE_MFW_NDC
/*
 *-----------------------------------------------------------------------------
 *
 * NDC_Cleanup --
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

extern "C" void CORE_TYPE
NDC_Cleanup()
{
   NDC::remove();
}

#   endif


/*
 *-----------------------------------------------------------------------------
 *
 * ****************************
 * corerunnable globals
 * ****************************
 *
 *-----------------------------------------------------------------------------
 */

uintptr_t (*CORE::corerunnable_beginthreadex)(void *, unsigned int,
                                              unsigned int(__stdcall *)(void *), void *,
                                              unsigned int, unsigned int *) = _beginthreadex;

corethreadwrapper *CORE::corerunnable_defaultThreadWrapper = NULL;

#   ifdef _DEBUG
#      define TRACE_THREADS
#   endif

#   ifdef TRACE_THREADS
bool CORE::corerunnable_traceThreads = true;
#   else
bool CORE::corerunnable_traceThreads = false;
#   endif

DWORD CORE_TYPE CORE::corerunnable_threadAffinity = (DWORD)-1;

void CORE_TYPE
CORE::corerunnable_initThreadAffinity()
{
   coresync sync(g_timersync);
   CORE::corerunnable_threadAffinity = 0;
#   ifdef WIN32
   DWORD affinity =
      tstr::readRegistryPolicyOverride(
         TEXT("HKLM\\") HORIZON_VDM_REG_ROOT TEXT("\\mfwThreadAffinityMask"), TEXT("0"))
         .toUInt();
   if (affinity) {
      DWORD_PTR processAffinity, systemAffinity;
      if (!GetProcessAffinityMask(GetCurrentProcess(), &processAffinity, &systemAffinity)) {
         sync.unlock();
         corelog.Error(TEXT("GetProcessAffinityMask FAILED, error=%s"), tstr::formatError());
      } else if (!(affinity & processAffinity)) {
         sync.unlock();
         corelog.Error(TEXT("threadAffinity=%u NOT in processAffinityMask=%u"), affinity,
                       processAffinity);
      } else {
         CORE::corerunnable_threadAffinity = affinity;
         sync.unlock();
         corelog.Info(TEXT("Set threadAffinity=%u (processAffinityMask=%u)"), affinity,
                      processAffinity);
         SetThreadAffinityMask(GetCurrentThread(), CORE::corerunnable_threadAffinity);
      }
   }
#   endif
}


/*
 *-----------------------------------------------------------------------------
 *
 * ****************************
 * coretimerservice
 * ****************************
 *
 *-----------------------------------------------------------------------------
 */

bool g_timerCritsecOk = false;
coretimer_critsec g_timersync;

/* the global timer service (created on a as needed basis) */
static coretimerservice *g_timerservice = NULL;

class CORE::coretimerservice : public corerunnable {
public:
   std::vector<coreWeakHandle> m_queue;
   HANDLE m_event;
   bool m_threadStopped;
   bool m_shutdown;
   bool m_traceLog;

   coretimerservice(corethreadwrapper *wrap = 0) : corerunnable("TimerService", wrap)
   {
      m_event = 0;
      m_threadStopped = true;
      m_shutdown = !corerunnable::waitForAllThreads_Notify(goaway);
#   ifdef WIN32
      m_traceLog =
         mstr::readRegistry("HKLM\\" HORIZON_VDM_REG_ROOT_A "\\Log\\TraceTimerService", "false")
            .toBool();
#   else
      m_traceLog = false;
#   endif
   }

   ~coretimerservice()
   {
      m_queue.clear();

      if (m_event) {
         SetEvent(m_event);
         wait();
         CloseHandle(m_event);
      }
   }


   /*
    *-----------------------------------------------------------------------------
    *
    * goaway --
    *
    * Results:
    *    None
    *
    * Side effects:
    *    None
    *
    *-----------------------------------------------------------------------------
    */

   static void goaway()
   {
      coresynctimer sync;

      if (g_timerservice) {
         for (size_t i = 0; i < g_timerservice->m_queue.size(); i++) {
            g_timerservice->Release();
         }
         g_timerservice->m_queue.clear();

         if (g_timerservice->m_event) {
            SetEvent(g_timerservice->m_event);
         }
         g_timerservice->Release();
         g_timerservice = NULL;
      }
   }


   /*
    *-----------------------------------------------------------------------------
    *
    * add --
    *
    * Results:
    *    None
    *
    * Side effects:
    *    None
    *
    *-----------------------------------------------------------------------------
    */

   static void add(coretimer *timer)
   {
      coresynctimer sync;
      if (!g_timerservice) {
         g_timerservice = new coretimerservice;
      }

      if (g_timerservice->m_shutdown) {
         corelog.Debug(TEXT("Add of timer skipped at shutdown"));
      } else {
         g_timerservice->m_queue.push_back(timer->WeakHandle());
         g_timerservice->AddRef();
      }
   }


   /*
    *-----------------------------------------------------------------------------
    *
    * remove --
    *
    * Results:
    *    None
    *
    * Side effects:
    *    None
    *
    *-----------------------------------------------------------------------------
    */

   void remove(coretimer *timer)
   {
      coresynctimer sync;
      coreWeakHandle weakHandle = timer->WeakHandle();

      for (auto it = m_queue.begin(); it != m_queue.end(); it++) {
         if (*it == weakHandle) {
            m_queue.erase(it);
            Release();
            break;
         }
      }

      if ((g_timerservice) && (m_queue.size() == 0)) {
         if (m_event) {
            SetEvent(m_event);
         }

         g_timerservice->Release();
         g_timerservice = NULL;
      }
   }


   /*
    *-----------------------------------------------------------------------------
    *
    * start --
    *
    * Results:
    *    None
    *
    * Side effects:
    *    None
    *
    *-----------------------------------------------------------------------------
    */

   void start()
   {
      if ((!m_event) && ((m_event = CreateEvent(0, FALSE, FALSE, 0)) == NULL)) {
         throw coreException(GetLastError());
      }

      if (m_threadStopped) {
         m_threadStopped = false;

         if (!run()) {
            throw coreException(GetLastError());
         }
      } else {
         SetEvent(m_event);
      }
   }


   /*
    *-----------------------------------------------------------------------------
    *
    * ThreadEntry --
    *
    * Results:
    *    None
    *
    * Side effects:
    *    None
    *
    *-----------------------------------------------------------------------------
    */

   void ThreadEntry()
   {
#   ifdef WIN32
      SetThreadPriority(GetCurrentThread(), THREAD_PRIORITY_HIGHEST);
#   endif

      DWORD waitRes = WAIT_OBJECT_0;
      for (;;) {
         g_timersync.lock();

         if ((waitRes == WAIT_FAILED) || (!m_queue.size())) {
            g_timersync.unlock();
            m_threadStopped = true;
            return;
         }

         DWORD now = GetTickCount();
         DWORD interval = INFINITE;
         bool skipWaitForNextEvent = false;

         /*
          * An index is used to iterate over the vector because timers
          * can be removed from the vector during the iteration which
          * invalidates an std::iterator.  Using an index is more
          * robust since modifying the vector doesn't invalidate it.
          */
         for (std::size_t iTimer = 0; iTimer < m_queue.size(); iTimer++) {
            /*
             * m_queue holds a weak handle to the coretimer object.
             * ObjectLock() will return NULL if the timer has already
             * been deleted.  If a valid coretimer object is returned
             * a reference has been added that will need to be released.
             */
            coretimer *timer = coretimer::ObjectLock(m_queue[iTimer]);
            if (timer == NULL) {
               continue;
            }

            if ((timer->m_timerRunning) && (!timer->m_inCallback)) {
               DWORD elapsed = now - timer->m_timerTick;

               if (elapsed >= timer->m_timerMilliSeconds) {
                  timer->m_inCallback = true;
                  g_timersync.unlock();

                  /*
                   * The reference that was added by ObjectLock()
                   * will be released in callbackHandler().  Note
                   * that releasing the reference may delete the
                   * timer so it is not safe to access it when
                   * callbackHandler() returns.  It also removes
                   * it from m_queue which invalidates the index
                   * so it's best to restart the loop so that a
                   * timer is not skipped.
                   */
                  timer->callbackHandler(now, elapsed, m_traceLog);
                  skipWaitForNextEvent = true;
                  break;
               }

               if (interval > timer->m_timerMilliSeconds - elapsed) {
                  interval = timer->m_timerMilliSeconds - elapsed;
               }
            }

            /*
             * callbackHandler() wasn't called so release the
             * reference added by ObjectLock() here.  See the
             * comment above about what happens if releasing
             * the reference deletes the timer.
             */
            if (timer->Release() == 0) {
               skipWaitForNextEvent = true;
               break;
            }
         }

         if (skipWaitForNextEvent) {
            continue;
         }
         g_timersync.unlock();
         waitRes = WaitForSingleObject(m_event, interval);
      }
   }
};

/*
 *-----------------------------------------------------------------------------
 *
 * ****************************
 * coretimer
 * ****************************
 *
 *-----------------------------------------------------------------------------
 */

coretimer::coretimer()
{
   m_timerRunning = false;
   m_timerMilliSeconds = 0;
   m_inCallback = false;
   m_event = 0;
   coretimerservice::add(this);
}

coretimer::~coretimer()
{
   {
      coresynctimer sync;
      m_timerRunning = false;

      if (g_timerservice) {
         g_timerservice->remove(this);
      }

      if (m_inCallback) {
         m_event = CreateEvent(0, FALSE, FALSE, 0);
      }
   }

   if (m_event) {
      if (!isInStaticDeconstruction) {
         WaitForSingleObject(m_event, INFINITE);
      }
      CloseHandle(m_event);
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * ResetTimer --
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

void
coretimer::ResetTimer()
{
   coresynctimer sync;
   m_timerTick = GetTickCount();
}


/*
 *-----------------------------------------------------------------------------
 *
 * StartTimer --
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

void
coretimer::StartTimer(DWORD milliSeconds)
{
   coresynctimer sync;

   m_timerRunning = true;
   m_timerMilliSeconds = milliSeconds;
   m_timerTick = GetTickCount();

   if (g_timerservice) {
      g_timerservice->start();
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * StopTimer --
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

void
coretimer::StopTimer()
{
   coresynctimer sync;
   m_timerRunning = false;
}


/*
 *-----------------------------------------------------------------------------
 *
 * StopTimerWithWait --
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

void
coretimer::StopTimerWithWait()
{
   {
      coresynctimer sync;
      m_timerRunning = false;

      if (m_inCallback) {
         m_event = CreateEvent(0, FALSE, FALSE, 0);
      }
   }

   if (m_event) {
      if (!isInStaticDeconstruction) {
         WaitForSingleObject(m_event, INFINITE);
      }
      CloseHandle(m_event);
      m_event = 0;
   }
}


/*
 *-----------------------------------------------------------------------------
 *
 * callbackHandler --
 *
 * Results:
 *    None
 *
 * Side effects:
 *    None
 *
 *-----------------------------------------------------------------------------
 */

void
coretimer::callbackHandler(DWORD now, DWORD intervalElapsed, bool traceLog)
{
   if ((float)intervalElapsed > (float)m_timerMilliSeconds * 1.4f) {
      corelog.Error(TEXT("Timer callback missed by 1.4x, name=%s, expected=%u, ")
                       TEXT("now=%u, interval=%u, intervalElapsed=%u"),
                    mstr::to_tstr(typeid(*this).name()).c_str(), m_timerTick, now,
                    m_timerMilliSeconds, intervalElapsed);
   } else if (traceLog) {
      corelog.Info(
         TEXT("Calling timer callback, name=%s, now=%u, ") TEXT("interval=%u, intervalElapsed=%u"),
         mstr::to_tstr(typeid(*this).name()).c_str(), now, m_timerMilliSeconds, intervalElapsed);
   }

   m_timerRunning = TimerCallback();
   m_timerTick = GetTickCount();

   DWORD cbElasped = m_timerTick - now;

   if (cbElasped > 10000) {
      corelog.Error(TEXT("Detected a slow timer callback, name=%s, start=%u, ")
                       TEXT("end=%u, cbElapsed=%u"),
                    mstr::to_tstr(typeid(*this).name()).c_str(), now, m_timerTick, cbElasped);
   } else if (traceLog) {
      corelog.Info(TEXT("Timer callback complete, name=%s, start=%u, end=%u, ")
                      TEXT("cbElasped=%d"),
                   mstr::to_tstr(typeid(*this).name()).c_str(), now, m_timerTick, cbElasped);
   }

   /*
    * Cache the event handle and update the
    * state of the timer now because calling
    * Release() may delete the timer object
    * so it's not safe to access the member
    * variables after calling Release().
    */
   HANDLE theEvent;
   {
      coresynctimer sync;
      theEvent = m_event;
      m_inCallback = false;
   }
   Release();

   if (theEvent) {
      SetEvent(theEvent);
   }
}

/* ifndef MESSAGE_FRAMEWORK_DLL_LOCAL */
#endif
