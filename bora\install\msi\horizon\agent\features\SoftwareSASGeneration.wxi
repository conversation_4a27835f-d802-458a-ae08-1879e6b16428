<?xml version="1.0" encoding="utf-8"?>

<!--
*******************************************************************************
   Copyright (c) Omnissa, LLC. All rights reserved.
   This product is protected by copyright and intellectual property laws in the
   United States and other countries as well as by international treaties.
   - Omnissa Restricted

   SoftwareSASGeneration.wxi  (product: Horizon Agent)
*******************************************************************************
-->

<Include>

   <!-- These installer actions came out of this bug:
        https://bugzilla.eng.vmware.com/show_bug.cgi?id=463127
   -->

   <?define SwSasGenKey="SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System" ?>
   <?define SwSasGenName="SoftwareSASGeneration" ?>
   <?define SAS_MASK_VALUE="3" ?>

   <!-- Set to default value 3, as gpo policy defaults to 2 if key not existing -->
   <Property Id='SASVALUE' Value='3' />

   <Property Id="SWSASGEN_EXISTING_VALUE">
      <RegistrySearch Id="sigSwSasGenStatus"
                      Root="HKLM"
                      Key="$(var.SwSasGenKey)"
                      Name="$(var.SwSasGenName)"
                      Type="raw" />
   </Property>

   <Property Id="SWSASGEN_BACKUP_VALUE">
      <RegistrySearch Id="sigSwSasGenBackupValue"
                      Root="HKLM"
                      Key="$(var.SystemRegistryBackupPath)"
                      Name="$(var.SwSasGenName)"
                      Type="raw" />
   </Property>

   <!-- Expectations:
        - Overwrite the system registry value on install
        - Restore the system registry value on uninstall
        - Only request a reboot if the system registry value is changing
   -->

   <!-- CustomActionData = [RebootFlag];[RegistryKey];[BackupKey];[KeyValue];[Mask];[KeyValueData] -->
   <?define SwSasGenInstall="0;$(var.SwSasGenKey);$(var.SystemRegistryBackupPath);$(var.SwSasGenName);$(var.SAS_MASK_VALUE);[SASVALUE]"?>
   <?define SwSasGenInstallRB="0;$(var.SwSasGenKey);$(var.SystemRegistryBackupPath);$(var.SwSasGenName);$(var.SAS_MASK_VALUE)"?>
   <?define SwSasGenUninstall="0;$(var.SwSasGenKey);$(var.SystemRegistryBackupPath);$(var.SwSasGenName);$(var.SAS_MASK_VALUE)"?>
   <?define SwSasGenUninstallRB="0;$(var.SwSasGenKey);$(var.SystemRegistryBackupPath);$(var.SwSasGenName);$(var.SAS_MASK_VALUE);[SWSASGEN_EXISTING_VALUE]"?>

   <CustomAction Id="VM_WriteSwSasGen"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMSwapRegistryDwordValues"
                 Return="check"
                 Impersonate="no"
                 Execute="deferred" />

   <CustomAction Id="VM_WriteSwSasGen_RB"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMSwapRegistryDwordValues"
                 Return="ignore"
                 Impersonate="no"
                 Execute="rollback" />

   <CustomAction Id="VM_WriteSwSasGen_RB_SD"
                 Property="VM_WriteSwSasGen_RB"
                 Value="$(var.SwSasGenInstallRB)" />

   <CustomAction Id="VM_WriteSwSasGen_SD"
                 Property="VM_WriteSwSasGen"
                 Value="$(var.SwSasGenInstall)" />

   <CustomAction Id="VM_RestoreSwSasGen"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMSwapRegistryDwordValues"
                 Return="check"
                 Impersonate="no"
                 Execute="deferred" />

   <CustomAction Id="VM_RestoreSwSasGen_RB"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMSwapRegistryDwordValues"
                 Return="ignore"
                 Impersonate="no"
                 Execute="rollback" />

   <CustomAction Id="VM_RestoreSwSasGen_RB_SD"
                 Property="VM_RestoreSwSasGen_RB"
                 Value="$(var.SwSasGenUninstallRB)" />

   <CustomAction Id="VM_RestoreSwSasGen_SD"
                 Property="VM_RestoreSwSasGen"
                 Value="$(var.SwSasGenUninstall)" />

   <!-- Used to request a reboot if the system value changes -->
   <CustomAction Id="VM_SetRebootSwSasGen_SD"
                 Property="VM_SetRebootSwSasGen"
                 Value="$(var.SwSasGenName)" />

   <CustomAction Id="VM_SetRebootSwSasGen"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMSetRebootComponent"
                 Return="ignore"
                 Impersonate="no"
                 Execute="deferred" />

   <!-- Used to clear a reboot if the system value has not changed across an upgrade -->
   <CustomAction Id="VM_ClearRebootSwSasGen_SD"
                 Property="VM_ClearRebootSwSasGen"
                 Value="$(var.SwSasGenName)" />

   <CustomAction Id="VM_ClearRebootSwSasGen"
                 BinaryKey="vdmInstUtil.dll"
                 DllEntry="VMClearRebootComponent"
                 Return="ignore"
                 Impersonate="no"
                 Execute="deferred" />

   <!-- Used to set SAS value based on existing value -->
   <CustomAction Id="VM_SetSASValue_1_SD"
                 Property="SASVALUE"
                 Value="1" />


   <InstallExecuteSequence>

      <Custom Action="VM_RestoreSwSasGen_RB_SD" Before="RemoveRegistryValues">&amp;Core=2 And (SWSASGEN_EXISTING_VALUE="#1" Or SWSASGEN_EXISTING_VALUE="#3")</Custom>
      <Custom Action="VM_RestoreSwSasGen_SD" After="VM_RestoreSwSasGen_RB_SD">&amp;Core=2 And (SWSASGEN_EXISTING_VALUE="#1" Or SWSASGEN_EXISTING_VALUE="#3")</Custom>
      <Custom Action="VM_RestoreSwSasGen_RB" After="VM_RestoreSwSasGen_SD">VM_RestoreSwSasGen_RB</Custom>
      <Custom Action="VM_RestoreSwSasGen" After="VM_RestoreSwSasGen_RB">VM_RestoreSwSasGen</Custom>

      <Custom Action="VM_SetSASValue_1_SD" After="WriteRegistryValues">SWSASGEN_EXISTING_VALUE="#1" Or SWSASGEN_EXISTING_VALUE="#0"</Custom>

      <Custom Action="VM_WriteSwSasGen_RB_SD" After="VM_SetSASValue_1_SD">&amp;Core=3 Or (!Core=3 And REINSTALL)</Custom>
      <Custom Action="VM_WriteSwSasGen_SD" After="VM_WriteSwSasGen_RB_SD">&amp;Core=3 Or (!Core=3 And REINSTALL)</Custom>
      <Custom Action="VM_WriteSwSasGen_RB" After="VM_WriteSwSasGen_SD">VM_WriteSwSasGen_RB</Custom>
      <Custom Action="VM_WriteSwSasGen" After="VM_WriteSwSasGen_RB">VM_WriteSwSasGen</Custom>

      <!-- Reboot required when the system value changes during an install or an uninstall -->
      <Custom Action="VM_SetRebootSwSasGen_SD" Before="InstallFinalize">
         ((&amp;Core=3 Or (!Core=3 And REINSTALL)) And Not SWSASGEN_EXISTING_VALUE="#1") Or
         (&amp;Core=2 And Not SWSASGEN_EXISTING_VALUE=SWSASGEN_BACKUP_VALUE)
      </Custom>
      <Custom Action="VM_SetRebootSwSasGen" After="VM_SetRebootSwSasGen_SD">VM_SetRebootSwSasGen</Custom>

      <!-- Reboot value can be cleared when the system value has not changed during an upgrade -->
      <Custom Action="VM_ClearRebootSwSasGen_SD" After="VM_SetRebootSwSasGen">OLDPRODUCTFOUND And SWSASGEN_EXISTING_VALUE="#1"</Custom>
      <Custom Action="VM_ClearRebootSwSasGen" After="VM_ClearRebootSwSasGen_SD">VM_ClearRebootSwSasGen</Custom>
   </InstallExecuteSequence>

</Include>
