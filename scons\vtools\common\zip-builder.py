# Copyright (c) Omnissa, LLC. All rights reserved.
# This product is protected by copyright and intellectual property laws in the
# United States and other countries as well as by international treaties.
# -- Omnissa Restricted

import os
import os.path
import vmware

from SCons.Script import File, Dir

log = vmware.GetLogger("main")


def chmodAct(chmodMode):
    return vmware.ActionWithDisplay(
        "$CHMOD %s ${TARGET.abspath}" % chmodMode,
        "Setting mode %s for $TARGET" % chmodMode,
    )


def ZipDir(self, target, cdDir, dirName, chmodMode="0555"):
    """
    Creates a zip archive at `target` by first changing directory to `cdDir`
    and then archiving `dirName`, i.e. it adds the file at `dirName` with paths
    in the archive that are relative to `cdDir`.

    The zip command includes -b to prevent the zip process from writing the
    temporary archive to the source directory, which can cause build failures
    when running zip multiple times over the same directory simultaneously.
    """
    acts = []
    dirNode = cdDir.Dir(dirName)
    acts.append(
        vmware.ActionWithDisplay(
            "$CD %s && $ZIP -b %s -r ${TARGET.abspath} %s"
            % (
                self["ESCAPE"](cdDir.abspath),
                self["ESCAPE"](vmware.DirAbsPath(vmware.TempDirectory())),
                self["ESCAPE"](dirName),
            ),
            "Zipping $SOURCE to $TARGET",
        )
    )
    acts.append(chmodAct(chmodMode))
    return self.Command(target, dirNode, acts)


# You will need env.LoadTool('p7zip') to use this method.
#
# This will work on Mac OS, Linux and Windows as all have the p7zip
# binary available in TC.
def P7ZipDir(self, target, cdDir, dirName, chmodMode="0555"):
    acts = []
    dirNode = cdDir.Dir(dirName)
    # cd $cdDir, zip %dirname
    acts.append(
        vmware.ActionWithDisplay(
            "$CD %s && $P7ZIPCMD a -bd ${TARGET.abspath} %s"
            % (self["ESCAPE"](cdDir.abspath), self["ESCAPE"](dirName)),
            "7zipping $SOURCE to $TARGET",
        )
    )
    acts.append(chmodAct(chmodMode))
    acts.append(
        vmware.ActionWithDisplay(
            "$P7ZIPCMD t ${TARGET.abspath}", "Testing 7zip integrity: $TARGET"
        )
    )
    nodes = self.Command(target, dirNode, acts)
    self.NoCache(nodes)
    return nodes


def Zip(self, target, sources, chmodMode="0555", recurse=True):
    """
    Zips mixed sources together into a zip file. Sources can be either files or
    directories and should be a tuple of the form (parent directory,
    file or dir name).

    Optional parameter "recurse" specifies whether to recurse into
    subdirectories if any sources are directories.
    """
    allSources = []
    acts = []
    cacheable = True

    # Group sources by their base directory to improve efficiency. In the old naive implementation,
    # we would create a separate zip command for each source, which is inefficient. However, we
    # can't just create one zip command for all sources, because the zip tool structures the output
    # zip file paths relative to the base directory of the source files. Grouping sources by their
    # base directory allows us to create a single zip command for each base directory, which is more
    # efficient and still maintains the correct structure in the output zip file.
    #
    # As a note, the value of the dict is a set, not a list, to avoid duplicates. Avoiding
    # duplicates is important for the following reasons:
    #   1. On Mac, 7zz fails the command if duplicates are provided.
    #   2. On Windows, duplicates can cause us to go over the command line length limit.
    combinedSources = {}
    for basedir, src in sources:
        existingEntry = combinedSources.get(basedir)
        if existingEntry is not None:
            existingEntry.add(src)
        else:
            combinedSources[basedir] = {src}

    for basedir, srcs in combinedSources.items():
        # The zip tool allows files or directories, so let's allow both as well.
        currentSources = []
        # Sort the sources to ensure consistent zip commands across builds. Otherwise the order
        # of iteration over a set is not guaranteed.
        srcsSorted = sorted(srcs)
        for src in srcsSorted:
            srcPath = os.path.join(basedir, src)
            try:
                srcFile = File(srcPath)
            except TypeError:
                srcFile = Dir(srcPath)
            allSources.append(srcFile)
            currentSources.append(srcFile)

        if vmware.BuildHostIsMac():
            cmd = "$CD %s && $P7ZIPCMD -tzip a %s ${TARGET.abspath} %s"
            recurse = False  # 7zip doesn't need a recurse option
        else:
            cmd = "$CD %s && $ZIP -g %s ${TARGET.abspath} %s"

        if len(srcs) == 1:
            sourcePathForDisplay = self["ESCAPE"](currentSources[0].abspath)
        else:
            sourcePathForDisplay = (
                self["ESCAPE"](currentSources[0].dir.abspath)
                + " files "
                + self["ESCAPE"](
                    ",".join(currentSource.name for currentSource in currentSources)
                )
            )
        acts.append(
            vmware.ActionWithDisplay(
                cmd
                % (
                    self["ESCAPE"](vmware.DirAbsPath(basedir)),
                    "-r" if recurse else "",
                    " ".join(self["ESCAPE"](src) for src in srcsSorted),
                ),
                f"Zipping {sourcePathForDisplay} to $TARGET",
            )
        )

        # If any source files are not cacheable, this shouldn't be cacheable.
        cacheable = cacheable and getattr(srcFile, "nocache", False)

    if not cacheable:
        self.NoCache(target)

    if hasattr(self, "AddZipDeps"):
        self.AddZipDeps(target)

    return self.Command(target, allSources, acts)


# You will need env.LoadTool('p7zip') to use this method.
#
# This will work on Mac OS, Linux and Windows as all have the p7zip
# binary available in Conan.
def P7Zip(self, target, sources, chmodMode="0555", recurse=True):
    """
    Zips mixed sources together into a zip file. Sources can be either files or
    directories and should be a tuple of the form (parent directory,
    file or dir name).

    Optional parameter "recurse" specifies whether to recurse into
    subdirectories if any sources are directories.
    """
    absSources = []
    acts = []
    cacheable = True

    for basedir, src in sources:
        # The zip tool allows files or directories, so let's allow both as well.
        srcPath = os.path.join(basedir, src)
        try:
            srcFile = File(srcPath)
        except TypeError:
            srcFile = Dir(srcPath)
        absSources.append(srcFile)
        cmd = "$CD %s && $P7ZIPCMD -tzip a %s ${TARGET.abspath} %s"
        recurse = False  # 7zip doesn't need a recurse option
        acts.append(
            vmware.ActionWithDisplay(
                cmd
                % (
                    self["ESCAPE"](vmware.DirAbsPath(basedir)),
                    "-r" if recurse else "",
                    self["ESCAPE"](src),
                ),
                "7zipping %s to $TARGET" % self["ESCAPE"](srcFile.abspath),
            )
        )

        # If any source files are not cacheable, this shouldn't be cacheable.
        cacheable = cacheable and getattr(srcFile, "nocache", False)

    if not cacheable:
        self.NoCache(target)

    return self.Command(target, absSources, acts)


def generate(env):
    env.AddBoundMethod(Zip)
    env.AddBoundMethod(P7Zip)
    env.AddBoundMethod(ZipDir)
    env.AddBoundMethod(P7ZipDir)
