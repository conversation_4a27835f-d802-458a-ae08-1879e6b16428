[ ca ]
default_ca	= CA_default		# The default ca section

[ CA_default ]
dir             = ./
crldir          = $dir/crl
crlnumber       = $dir/crlnumber
crl             = $crldir/crl.pem
crl_extensions  = crl_ext
default_crl_days= 30
new_certs_dir = $dir/newcerts
database	= $dir/index.txt	# database index file.
default_md	= sha256		# use public key default MD
policy		= policy_match
serial		= $dir/serial 		# The current serial number

[ policy_match ]
countryName		= optional
stateOrProvinceName	= optional
organizationName	= optional
organizationalUnitName	= optional
commonName		= supplied
emailAddress		= optional

[ v3_ca ]
crlDistributionPoints = URI:http://horizon-core-agent-crl3.com:8885/hzagent-3.crl
keyUsage = critical, digitalSignature, keyEncipherment
extendedKeyUsage = serverAuth, clientAuth

[ crl_ext ]
authorityKeyIdentifier=keyid:always